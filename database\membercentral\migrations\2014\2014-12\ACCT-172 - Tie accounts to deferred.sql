use membercentral
GO

-- Add settings node to existing admin instances
update cms_applicationInstances
set settingsXML = replace(cast(settingsXML as varchar(max)),'</settings>','<setting name="useAccrualAccounting" value="false" /></settings>')
where applicationInstanceName = 'admin'
GO

ALTER PROCEDURE [dbo].[cms_createApplicationInstanceAdmin]
	@siteid int,
	@languageID int,
	@sectionID int,
	@isVisible bit,
	@pageName varchar(50),
	@pageTitle varchar(200),
	@pagedesc varchar(400),
	@zoneID int,
	@pageTemplateID int,
	@pageModeID int,
	@pgResourceTypeID int,
	@pgParentResourceID int = null,
	@allowReturnAfterLogin bit,
	@applicationInstanceName varchar(100),
	@applicationInstanceDesc varchar(200),
	@applicationInstanceID int OUTPUT,
	@siteResourceID int OUTPUT,
	@pageID int OUTPUT
AS

-- null OUTPUT vars
SELECT @applicationInstanceID = null, @siteResourceID = null, @pageID = null

DECLARE @appCreatedSectionResourceTypeID int, @applicationTypeID int, @rootSectionID int, @rc int
DECLARE @documentSectionName varchar(50)

select @appCreatedSectionResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedSection')
select @applicationTypeID = applicationTypeID from cms_applicationTypes where applicationTypeName = 'Admin'
	
BEGIN TRAN

exec @rc = dbo.cms_createApplicationInstance
		@siteid = @siteid,
		@languageID = @languageID,
		@sectionID = @sectionID,
		@applicationTypeID = @applicationTypeID,
		@isVisible = @isVisible,
		@pageName = @pageName,
		@pageTitle = @pageTitle,
		@pagedesc = @pagedesc,
		@zoneID = @zoneID,
		@pageTemplateID = @pageTemplateID,
		@pageModeID = @pageModeID,
		@pgResourceTypeID = @pgResourceTypeID,
		@pgParentResourceID = @pgParentResourceID,
		@allowReturnAfterLogin = @allowReturnAfterLogin,
		@applicationInstanceName = @applicationInstanceName,
		@applicationInstanceDesc = @applicationInstanceDesc,
		@applicationInstanceID = @applicationInstanceID OUTPUT,
		@siteResourceID = @siteResourceID OUTPUT,
		@pageID = @pageID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

select @documentSectionName = 'MemberDocument'

exec @rc = dbo.cms_createPageSection
		@siteID = @siteID, 
		@sectionResourceTypeID = @appCreatedSectionResourceTypeID, 
		@ovTemplateID = NULL,
		@ovTemplateIDMobile=NULL,
		@ovModeID = NULL, 
		@parentSectionID = @sectionID, 
		@sectionName = @documentSectionName, 
		@sectionCode = @documentSectionName,
		@inheritPlacements = 1,
		@sectionID = @rootSectionID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- update parentSiteResourceID of section
UPDATE sr
SET sr.parentSiteResourceID = @siteResourceID
FROM cms_pageSections s
INNER JOIN cms_siteResources sr on s.siteResourceID = sr.siteResourceID
	AND s.sectionID = @rootSectionID
	IF @@ERROR <> 0 GOTO on_error

-- update settings xml
UPDATE dbo.cms_applicationInstances
SET settingsXML = '<settings><setting name="memberDocumentSectionID" value="' + cast(@rootSectionID as varchar(8)) + + '" /><setting name="showMemberDocuments" value="false" /><setting name="showNotes" value="false" /><setting name="showRelationships" value="false" /><setting name="showMemberHistory" value="false" /><setting name="showSubscriptions" value="false" /><setting name="showReferrals" value="false" /><setting name="showApptTracker" value="false" /><setting name="showMemberPhotosInSearchResults" value="true" /><setting name="numPerPageInSearchResults" value="25" /><setting name="reportHeaderImage" value="gif" /><setting name="useAccrualAccounting" value="false" /></settings>'
WHERE applicationInstanceID = @applicationInstanceID
	IF @@ERROR <> 0 GOTO on_error

-- create the suite of tools
EXEC @rc = dbo.createAdminSuite @siteID
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- create admin member lookup fieldset
declare @memberAdminSRID int, @fieldSetID int, @fieldID int, @useID int
select @memberAdminSRID = dbo.fn_getSiteResourceIDForResourceType('MemberAdmin',@siteID)
EXEC @rc = dbo.ams_createMemberFieldSet @siteID=@siteID, @fieldsetName='Member Admin Search Form', @nameformat='LSXPFM', @showHelp=0, @fieldsetID=@fieldSetID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldSetID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_firstname', @fieldLabel='First Name', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_lastname', @fieldLabel='Last Name', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_company', @fieldLabel='Company', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_membernumber', @fieldLabel='Member Number', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = ams_createMemberFieldUsage @siteResourceID=@memberAdminSRID, @fieldsetID=@fieldSetID, @area='search', @createSiteResourceID=0, @useID=@useID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @useID = 0 GOTO on_error

EXEC @rc = dbo.ams_createMemberFieldSet @siteID=@siteID, @fieldsetName='Member Admin Search Results', @nameformat='LSXPFM', @showHelp=0, @fieldsetID=@fieldSetID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldSetID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_prefix', @fieldLabel='Prefix', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_middlename', @fieldLabel='Middle Name', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_suffix', @fieldLabel='Suffix', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = ams_createMemberFieldUsage @siteResourceID=@memberAdminSRID, @fieldsetID=@fieldSetID, @area='results', @createSiteResourceID=0, @useID=@useID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @useID = 0 GOTO on_error

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

ALTER PROC [dbo].[enableSiteFeature]
	@siteID int,
	@toolTypeList varchar(1000)
AS


declare @tblTools TABLE (toolType varchar(100))
insert into @tblTools (toolType)
select listItem from dbo.fn_varCharListToTable(@toolTypeList,',')


declare @applicationInstanceID int
select @applicationInstanceID = applicationInstanceID
	from dbo.cms_applicationInstances
	where siteID = @siteID
	and applicationInstanceName = 'admin'


declare @toolType varchar(100)
SELECT @toolType = min(toolType) from @tblTools
WHILE @toolType is not null BEGIN

	-- Accrual Accounting
	if @toolType = 'AccrualAccounting' begin
		declare @orgID int, @GLAccountID int
		select @orgID = orgID from dbo.sites where siteID = @siteID

		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''useAccrualAccounting'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=5, @accountName='Deferred Revenue Accounts', @accountCode='', @GLCode='DEFERREDREVENUE', @parentGLAccountID=null, @invoiceProfileID=null, @isSystemAccount=1, @isTaxable=0, @invoiceContentID=null, @GLAccountID=@GLAccountID output
	end

	-- appt tracker
	if @toolType = 'AppointmentTrackerAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showApptTracker'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- email blast
	if @toolType = 'EmailBlast' begin
		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- member documents
	if @toolType = 'MemberDocs' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showMemberDocuments'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		declare @resourceTypeID int, @sectionID int, @parentSectionID int
		select @resourceTypeID = resourceTypeID from dbo.cms_siteResourceTypes where resourceType = 'ApplicationCreatedSection'
		select @sectionID = sectionID from dbo.cms_pageSections where siteID = @siteID and sectionName = 'MCAMSMemberDocuments'
		IF @sectionID is null begin
			select @parentSectionID = sectionID from dbo.cms_pageSections where sectionName = 'root' and parentSectionID is null and siteID = @siteID
			exec dbo.cms_createPageSection @siteID, @resourceTypeID, null, null, null, @parentSectionID, 'MCAMSMemberDocuments', 'MCAMSMemberDocuments', 0, @sectionID output
		end
	end	

	-- member history
	if @toolType = 'MemberHistoryAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showMemberHistory'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID
	end

	-- referrals
	if @toolType = 'ReferralsAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showReferrals'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- relationships
	if @toolType = 'RelationshipAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showRelationships'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- reports
	if @toolType = 'Reports' begin
		declare @siteCode varchar(10)
		select @sitecode = sitecode from dbo.sites where siteID = @siteID

		insert into dbo.admin_siteToolRestrictions (toolTypeID, siteID)
		select tooltypeid, @siteID 
		from dbo.admin_toolTypes 
		where toolCFC like 'Reports.custom.' + @sitecode + '.%'
		or (toolCFC like 'Reports.%' and left(toolCFC,15) <> 'Reports.custom.')
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- subscriptions
	if @toolType = 'SubscriptionAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showSubscriptions'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = 'SubRenewalAdmin'
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- tasks
	if @toolType = 'NotesAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showNotes'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	SELECT @toolType = min(toolType) from @tblTools where toolType > @toolType
END


-- refresh and assign resources
exec dbo.createadminsuite @siteid


-- these need to be added after resources are there (createadminsuite)
IF EXISTS (select toolType from @tblTools where toolType = 'AppointmentTrackerAdmin')
	exec dbo.cms_createDefaultAppointmentCategories @siteID=@siteid, @contributingMemberID=461530

IF EXISTS (select toolType from @tblTools where toolType = 'EmailBlast')
	exec dbo.cms_createDefaultEmailBlastCategories @siteID=@siteID

IF EXISTS (select toolType from @tblTools where toolType = 'MemberHistoryAdmin')
	exec dbo.cms_createDefaultHistoryAdminCategories @siteID=@siteID, @contributingMemberID=461530

IF EXISTS (select toolType from @tblTools where toolType = 'RelationshipAdmin')
	exec dbo.cms_createDefaultRelationshipCategories @siteID=@siteID, @contributingMemberID=461530

IF EXISTS (select toolType from @tblTools where toolType = 'NotesAdmin')
	exec dbo.cms_createDefaultNotesCategories @siteID=@siteid, @contributingMemberID=461530

RETURN 0
GO

ALTER TABLE dbo.tr_GLAccounts ADD deferredGLAccountID int NULL
GO
ALTER TABLE dbo.tr_GLAccounts ADD CONSTRAINT
	FK_tr_GLAccounts_tr_GLAccounts1 FOREIGN KEY
	(
	deferredGLAccountID
	) REFERENCES dbo.tr_GLAccounts
	(
	GLAccountID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO

ALTER FUNCTION [dbo].[fn_getRecursiveGLAccountsWithAccountTypes] (@orgid int)
RETURNS TABLE 
AS
RETURN (

	WITH GLAccounts AS (
		select GLAccountID, AccountTypeID, InvoiceProfileID, AccountType, cast(AccountName as varchar(200)) as AccountName, 
			cast(AccountCode as varchar(200)) as accountCode, cast(GLCode as varchar(30)) as GLCode, 
			parentGLAccountID, deferredGLAccountID, cast([status] as char(1)) as [status], 
			cast(isSystemAccount as bit) as isSystemAccount, cast(isTaxable as bit) as isTaxable, 
			CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
			CAST(AccountName as varchar(max)) as thePathExpanded
		from (
			select 0 as GLAccountID, AccountTypeID, null as InvoiceProfileID, accountType, accountType + ' Accounts' as AccountName, 
				null as AccountCode, null as GLCode, null as parentGLAccountID, null as deferredGLAccountID, 
				'A' as [Status], 1 as isSystemAccount, 0 as isTaxable, ROW_NUMBER() OVER (ORDER BY typeOrder) AS theRow
			from dbo.tr_GLAccountTypes
		) as gltypes
		
		union all

		select GLAccountID, AccountTypeID, InvoiceProfileID, AccountType, AccountName, AccountCode, GLCode, parentGLAccountID, 
			deferredGLAccountID, [status], isSystemAccount, isTaxable, 
			thePath + '.' + CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
			thePathExpanded + ' \ ' + cast(AccountName as varchar(max)) as thePathExpanded
		from (
			select gl.GLAccountID, glt.accountTypeID, gl.InvoiceProfileID, glt.accountType, gl.AccountName, gl.AccountCode, gl.GLCode, 
				gl.parentGLAccountID, gl.deferredGLAccountID, gl.status, gl.isSystemAccount, gl.isTaxable, glcte.thePath,
				glcte.thePathExpanded, 
				ROW_NUMBER() OVER (ORDER BY glt.typeOrder, gl.AccountName) AS theRow
			from dbo.tr_GLAccounts as gl
			inner join dbo.tr_GLAccountTypes as glt on glt.accountTypeID = gl.accountTypeID
			inner JOIN GLAccounts as glcte ON glcte.accountType = glt.accountType
				and gl.parentGLAccountID is null
				and gl.orgID = @orgid
				and gl.status <> 'D'
				and glcte.accountName = glt.accountType + ' Accounts'
		) as x

		union all

		select GLAccountID, AccountTypeID, InvoiceProfileID, AccountType, AccountName, AccountCode, GLCode, parentGLAccountID, 
			deferredGLAccountID, [status], isSystemAccount, isTaxable, 
			thePath + '.' + CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
			thePathExpanded + ' \ ' + AccountName as thePathExpanded
		from (
			select gl.GLAccountID, glt.accountTypeID, gl.InvoiceProfileID, glt.accountType, gl.AccountName, gl.AccountCode, 
				gl.GLCode, gl.parentGLAccountID, gl.deferredGLAccountID, gl.status, gl.isSystemAccount, gl.isTaxable, 
				glcte.thePath, glcte.thePathExpanded, ROW_NUMBER() OVER (ORDER BY glt.typeOrder, gl.AccountName) AS theRow
			from dbo.tr_GLAccounts as gl
			inner join dbo.tr_GLAccountTypes as glt on glt.accountTypeID = gl.accountTypeID
			inner JOIN GLAccounts as glcte ON gl.parentGLAccountID = glcte.GLAccountID
				and gl.orgID = @orgid
				and gl.[status] <> 'D'
		) as y
	
	)

	select * 
	from GLAccounts

)
GO

ALTER FUNCTION [dbo].[fn_getGLAccountsStructureXML] (@orgID int)
RETURNS xml
AS
BEGIN
	DECLARE @xmlStructure xml
	
	SELECT @xmlStructure = (
		select (
			SELECT profileid, profilename
			from dbo.tr_invoiceProfiles as [profile]
			where orgID = @orgID
			order by profilename
			FOR XML AUTO, root('invoiceprofiles'), TYPE
			),
			isnull((
			select [content].contentid, [content].contenttitle, [content].rawcontent
			from dbo.cms_content as c
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = c.siteResourceID
			inner join dbo.cms_siteResourceTypes as srt on srt.resourceTypeID = sr.resourceTypeID
			inner join dbo.cms_siteResources as sr2 on sr2.siteResourceID = sr.parentSiteResourceID
			inner join dbo.cms_siteResourceTypes as srt2 on srt2.resourceTypeID = sr2.resourceTypeID
			inner join dbo.sites as s on s.siteID = c.siteID
			cross apply dbo.fn_getContent(c.contentID,1) as [content]
			where s.orgID = @orgID
			and sr.siteResourceStatusID = 1
			and srt.resourceType = 'ApplicationCreatedContent'
			and srt2.resourceType = 'GLAccountsAdmin'
			ORDER BY 1
			FOR XML AUTO, root('invoicecontent'), TYPE),'<invoicecontent/>'),
			(
			SELECT account.glaccountid, account.accounttypeid, account.accountname, account.status, 
				isnull(account.invoiceprofileid,0) as invoiceprofileid, account.thepathexpanded, 
				isnull(account.accountcode,'') as accountcode, isnull(account.glcode,'') as glcode, 
				isnull(account.parentglaccountid,0) as parentglaccountid, account.issystemaccount, 
				isnull(account.istaxable,0) as istaxable, isnull(gl.uid,'') as [uid],
				isnull(gl.invoiceContentID,0) as invoicecontentid
			FROM dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as account
			INNER JOIN dbo.tr_GlAccounts as gl on gl.glAccountID = account.glAccountID
			where account.glaccountid > 0
			and account.isSystemAccount = 0
			and account.status <> 'D'
			order by account.thePath
			FOR XML AUTO, root('accounts'), TYPE
			)
		FOR XML RAW('GLAccountsStructure'), TYPE
		)

	RETURN @xmlStructure

END
GO

ALTER PROC [dbo].[tr_viewTransaction_adjustment]
@transactionID int

AS

set nocount on

declare @orgID int
select @orgID = ownedByOrgID from dbo.tr_transactions where transactionID = @transactionID

declare @allGLs TABLE (GLAccountID int, thePathExpanded varchar(max), accountCode varchar(200), glCode varchar(30))
insert into @allGLS
select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode, rgl.glCode
from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl

-- transaction info
select TOP 1 t.transactionid, t.ownedByOrgID, t.recordedOnSiteID, t.amount, 
	case when glCred.glCode = 'ACCOUNTSRECEIVABLE' then 'Negative Adjustment' else 'Adjustment' end as [type],
	case when glCred.glCode = 'ACCOUNTSRECEIVABLE' then 'Negative Adjustment of ' else 'Adjustment of ' end + isnull(t.detail,'') as detail,
	t.transactionDate, t.dateRecorded, ts.status, mAss2.memberID as assignedTomemberID, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullif(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as recordedByMember,
	m2.company as recordedByMemberCompany,
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL,
	glCred.thePathExpanded + isnull(' (' + nullIf(glCred.accountCode,'') + ')','') as creditGL
from dbo.tr_transactions as t
inner join dbo.tr_types as tt on tt.typeID = t.typeID
inner join dbo.tr_statuses as ts on ts.statusID = t.statusID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join dbo.ams_members as m on m.memberid = t.recordedByMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
inner join @allGLs as glDeb on glDeb.GLAccountID = t.debitGLAccountID
inner join @allGLs as glCred on glCred.GLAccountID = t.creditGLAccountID
where t.transactionID = @transactionID

-- adjustment info
select top 1 i.invoiceID, ins.status, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, i.dateDue, i.invoiceProfileID, ip.profileName, i.invoiceCode,
	btn_canVoid = 
		case 
		when t.statusID = 1 and tAdjee.statusID = 1 and tAdjee.typeID <> 7 then 1 
		else 0 
		end
from dbo.tr_invoiceTransactions as it
inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
inner join dbo.organizations as o on o.orgID = @orgID
inner join dbo.tr_relationships as rAdj on rAdj.transactionID = t.transactionID
inner join dbo.tr_relationshipTypes as rtAdj on rtAdj.typeID = rAdj.typeID and rtAdj.type = 'AdjustTrans'
inner join dbo.tr_transactions as tAdjee on tAdjee.transactionID = rAdj.appliedToTransactionID
where t.transactionID = @transactionID

-- adjusting transaction
select top 1 tSaleTax.transactionID, tt.type, tSaleTax.amount, tSaleTax.detail,
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	glCred.thePathExpanded + isnull(' (' + nullIf(glCred.accountCode,'') + ')','') as creditGL
from dbo.tr_transactions as tSaleTax
inner join dbo.tr_types as tt on tt.typeID = tSaleTax.typeID
inner join dbo.tr_relationships as rAdj on rAdj.appliedToTransactionID = tSaleTax.transactionID
inner join dbo.tr_relationshipTypes as rtAdj on rtAdj.typeID = rAdj.typeID and rtAdj.type = 'AdjustTrans'
inner join dbo.ams_members as mAss on mAss.memberid = tSaleTax.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glCred on glCred.GLAccountID = tSaleTax.creditGLAccountID
where rAdj.transactionID = @transactionID

-- related transactions
EXEC dbo.tr_viewTransaction_related_recursive @transactionID

RETURN 0
GO

ALTER PROC [dbo].[tr_viewTransaction_allocation]
@transactionID int

AS

set nocount on

declare @orgID int
select @orgID = ownedByOrgID from dbo.tr_transactions where transactionID = @transactionID

declare @allGLs TABLE (GLAccountID int, thePathExpanded varchar(max), accountCode varchar(200), glCode varchar(30))
insert into @allGLS
select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode, rgl.glCode
from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl

-- transaction info
select TOP 1 t.transactionid, t.ownedByOrgID, t.recordedOnSiteID, t.amount,
	case when glCred.glCode = 'ACCOUNTSRECEIVABLE' then 'Allocation' else 'Deallocation' end as [type],
	case when glCred.glCode = 'ACCOUNTSRECEIVABLE' then 'Allocation ' else 'Deallocation ' end + 'of Payment' as detail,
	t.transactionDate, t.dateRecorded, ts.status, mAss2.memberID as assignedTomemberID, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullif(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as recordedByMember,
	m2.company as recordedByMemberCompany,
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL,
	glCred.thePathExpanded + isnull(' (' + nullIf(glCred.accountCode,'') + ')','') as creditGL
from dbo.tr_transactions as t
inner join dbo.tr_types as tt on tt.typeID = t.typeID
inner join dbo.tr_statuses as ts on ts.statusID = t.statusID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join dbo.ams_members as m on m.memberid = t.recordedByMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
inner join @allGLS as glDeb on glDeb.GLAccountID = t.debitGLAccountID
inner join @allGLS as glCred on glCred.GLAccountID = t.creditGLAccountID
where t.transactionID = @transactionID

-- allocation info
select b.batchID, b.batchName, b.depositDate, bs.status,
	btn_canVoid = case when t.statusID = 1 then 1 else 0 end
from dbo.tr_batchTransactions as bt 
inner join dbo.tr_transactions as t on t.transactionID = bt.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID
inner join dbo.tr_batchStatuses as bs on bs.statusID = b.statusID
where t.transactionID = @transactionID

-- current allocations
select tSaleAdj.transactionID as revenueTransactionID, tSaleAdj.amount as revenueAmount, ttSaleAdj.type as revenueType, 
	case when ttSaleAdj.typeid = 3 then 'Adjustment of ' else '' end + tSaleAdj.detail as revenueDetail,
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as revenueAssignedToMember,
	mAss2.company as revenueAssignedToMemberCompany,
	glCred.thePathExpanded + isnull(' (' + nullIf(glCred.accountCode,'') + ')','') as revenueCreditGL,
	tPay.transactionID as cashTransactionID, tPay.amount as cashAmount, ttPay.type as cashType, 
	tPay.detail as cashDetail,
	mAssPay2.firstname + isnull(' ' + nullif(mAssPay2.middlename,''),'') + ' ' + mAssPay2.lastname + isnull(' ' + nullif(mAssPay2.suffix,''),'') + ' (' + mAssPay2.membernumber + ')' as cashAssignedToMember,
	mAssPay2.company as cashAssignedToMemberCompany,
	glDebPay.thePathExpanded + isnull(' (' + nullIf(glDebPay.accountCode,'') + ')','') as cashDebitGL
from dbo.tr_transactions as tAlloc
inner join dbo.tr_relationships as rSaleAdj on rSaleAdj.transactionID = tAlloc.transactionID
inner join dbo.tr_relationshipTypes as rtSaleAdj on rtSaleAdj.typeID = rSaleAdj.typeID and rtSaleAdj.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as tSaleAdj on tSaleAdj.transactionID = rSaleAdj.appliedToTransactionID
inner join dbo.tr_types as ttSaleAdj on ttSaleAdj.typeID = tSaleAdj.typeID
inner join dbo.ams_members as mAss on mAss.memberid = tSaleAdj.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glCred on glCred.GLAccountID = tSaleAdj.creditGLAccountID
inner join dbo.tr_relationships as rPay on rPay.transactionID = tAlloc.transactionID
inner join dbo.tr_relationshipTypes as rtPay on rtPay.typeID = rPay.typeID and rtPay.type = 'AllocPayTrans'
inner join dbo.tr_transactions as tPay on tPay.transactionID = rPay.appliedToTransactionID
inner join dbo.tr_types as ttPay on ttPay.typeID = tPay.typeID
inner join dbo.ams_members as mAssPay on mAssPay.memberid = tPay.assignedToMemberID
inner join dbo.ams_members as mAssPay2 on mAssPay2.memberID = mAssPay.activeMemberID
inner join @allGLS as glDebPay on glDebPay.GLAccountID = tPay.debitGLAccountID
where tAlloc.transactionID = @transactionID

-- related transactions
EXEC dbo.tr_viewTransaction_related_recursive @transactionID

RETURN 0
GO

ALTER PROC [dbo].[tr_viewTransaction_void]
@transactionID int

AS

set nocount on

declare @orgID int
select @orgID = ownedByOrgID from dbo.tr_transactions where transactionID = @transactionID

-- transaction info
; WITH allGLs AS (
	select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode, rgl.glCode
	from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl
)
select TOP 1 t.transactionid, t.ownedByOrgID, t.recordedOnSiteID, tt.type, t.amount, 
	case
	when tVoidee.typeID = 3 and glVoidee.glCode = 'ACCOUNTSRECEIVABLE' then 'VOID of Negative Adjustment of ' + isnull(tVoidee.detail,'')
	when tVoidee.typeID = 3 and glVoidee.glCode <> 'ACCOUNTSRECEIVABLE' then 'VOID of Adjustment of ' + isnull(tVoidee.detail,'')
	when tVoidee.typeID = 6 and glVoidee.glCode = 'ACCOUNTSRECEIVABLE' then 'VOID of WriteOff of ' + isnull(tVoidee.detail,'')
	when tVoidee.typeID = 6 and glVoidee.glCode <> 'ACCOUNTSRECEIVABLE' then 'VOID of Negative WriteOff of ' + isnull(tVoidee.detail,'')
	when tVoidee.typeID = 9 then 'VOID of NSF of ' + isnull(tVoidee.detail,'')
	when tVoidee.typeID = 5 and glVoidee.glCode = 'ACCOUNTSRECEIVABLE' then 'VOID of Allocation to ' + isnull(tVoideeSale.detail,'')
	when tVoidee.typeID = 5 and glVoidee.glCode <> 'ACCOUNTSRECEIVABLE' then 'VOID of Deallocation from ' + isnull(tVoideeSale.detail,'')
	else 'VOID of ' + isnull(t.detail,'')
	end as detail,
	t.transactionDate, t.dateRecorded, ts.status, mAss2.memberID as assignedTomemberID, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullif(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as recordedByMember,
	m2.company as recordedByMemberCompany,
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL,
	glCred.thePathExpanded + isnull(' (' + nullIf(glCred.accountCode,'') + ')','') as creditGL
from dbo.tr_transactions as t
inner join dbo.tr_types as tt on tt.typeID = t.typeID
inner join dbo.tr_statuses as ts on ts.statusID = t.statusID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join dbo.ams_members as m on m.memberid = t.recordedByMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
inner join allGLs as glDeb on glDeb.GLAccountID = t.debitGLAccountID
inner join allGLs as glCred on glCred.GLAccountID = t.creditGLAccountID
inner join dbo.tr_relationships AS rVoid on rVoid.transactionID = t.transactionID
inner join dbo.tr_relationshipTypes AS rtVoid ON rtVoid.typeID = rVoid.typeID AND rtVoid.type = 'OffsetTrans'
inner join dbo.tr_transactions as tVoidee on tVoidee.transactionID = rVoid.appliedToTransactionID
inner join allGLs as glVoidee on glVoidee.GLAccountID = tVoidee.creditGLAccountID
left outer join dbo.tr_relationships AS rVoideeAllocSale 
	inner join dbo.tr_relationshipTypes AS rtVoideeAllocSale ON rtVoideeAllocSale.typeID = rVoideeAllocSale.typeID AND rtVoideeAllocSale.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as tVoideeSale on tVoideeSale.transactionID = rVoideeAllocSale.appliedToTransactionID
	on rVoideeAllocSale.transactionID = tVoidee.transactionID and tVoidee.typeID = 5
where t.transactionID = @transactionID

-- void info
IF (
	select tVoidee.typeID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships AS rVoid on rVoid.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes AS rtVoid ON rtVoid.typeID = rVoid.typeID AND rtVoid.type = 'OffsetTrans'
	inner join dbo.tr_transactions as tVoidee on tVoidee.transactionID = rVoid.appliedToTransactionID
	where t.transactionID = @transactionID
	) in (1,7,3)
	select it.itID, it.messageContentVersionID,
		i.invoiceID, ins.status, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, i.dateDue, i.invoiceProfileID, ip.profileName, i.invoiceCode
	from dbo.tr_invoiceTransactions as it
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
	inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
	inner join dbo.organizations as o on o.orgID = @orgID
	where it.transactionID = @transactionID
ELSE
	select b.batchID, b.batchName, b.depositDate, bs.status
	from dbo.tr_batchTransactions as bt 
	inner join dbo.tr_batches as b on b.batchID = bt.batchID
	inner join dbo.tr_batchStatuses as bs on bs.statusID = b.statusID
	where bt.transactionID = @transactionID

-- related transactions
EXEC dbo.tr_viewTransaction_related_recursive @transactionID

RETURN 0
GO


ALTER FUNCTION [dbo].[fn_getRecursiveGLAccounts] (@orgid int)
RETURNS TABLE 
AS
RETURN 
(
	WITH GLAccounts AS (
		select GLAccountID, [uid], invoiceProfileID, AccountTypeID, AccountType, AccountName, AccountCode, GLCode, 
			parentGLAccountID, [status], deferredGLAccountID, isSystemAccount, 
			CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
			CAST(AccountName as varchar(max)) as thePathExpanded
		from (
			select gl.GLAccountID, gl.uid, gl.invoiceProfileID, glt.accounttypeid, glt.accountType, gl.AccountName, 
				gl.AccountCode, gl.GLCode, gl.parentGLAccountID, gl.status, gl.deferredGLAccountID, gl.isSystemAccount, 
				ROW_NUMBER() OVER (ORDER BY glt.typeOrder, gl.AccountName) AS theRow
			from dbo.tr_GLAccounts as gl
			inner join dbo.tr_GLAccountTypes as glt on glt.accountTypeID = gl.accountTypeID
			where gl.orgID = @orgid
			and gl.parentGLAccountID is null
			and gl.status <> 'D'
		) as x
		union all
		select GLAccountID, [uid], invoiceProfileID, AccountTypeID, AccountType, AccountName, AccountCode, GLCode,
			parentGLAccountID, [status], deferredGLAccountID, isSystemAccount, 
			thePath + '.' + CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
			thePathExpanded + ' \ ' + AccountName as thePathExpanded
		from (
			select gl.GLAccountID, gl.uid, gl.invoiceProfileID, glt.accountTypeID, glt.accountType, gl.AccountName, 
				gl.AccountCode, gl.GLCode, gl.parentGLAccountID, gl.status, gl.deferredGLAccountID, gl.isSystemAccount, 
				glcte.thePath, glcte.thePathExpanded, 
				ROW_NUMBER() OVER (ORDER BY glt.typeOrder, gl.AccountName) AS theRow
			from dbo.tr_GLAccounts as gl
			inner join dbo.tr_GLAccountTypes as glt on glt.accountTypeID = gl.accountTypeID
			inner JOIN GLAccounts as glcte ON gl.parentGLAccountID = glcte.GLAccountID
				and gl.orgID = @orgid
				and gl.[status] <> 'D'
		) as y
	)

	select * 
	from GLAccounts

)
GO

ALTER PROC [dbo].[ev_exportRegistrantTransactions]
	@orgID int,
	@registrationID int,
	@filename varchar(400)
AS

-- status 2 (voided) do not appear here, along with VoidOffset types.
-- status 3 (pending) do not appear here -- they are not accepted transactions
-- status 4 (voidedpending) do not appear here -- they are meant to be completely hidden

DECLARE @fullsql varchar(max)

SELECT @fullsql = '
	select 	
		REGISTRANT = case
			when r.status <> ''D'' then mr2.lastname + '', '' + mr2.firstname
			else mr2.lastname + '', '' + mr2.firstname + '' (removed)''
			end,
		rt.transactionDate as TRANSACTION_DATE,
		rt.accrualDate as RECOGNIZED_DATE,
		tt.type as TRANSACTION_TYPE,
		TRANSACTION_DETAIL = isnull(rt.detail,''''),
		ma2.lastname + '', '' + ma2.firstname + '' ('' + ma2.membernumber + '')'' as TRANSACTION_ASSIGNED_TO,
		m2.lastname + '', '' + m2.firstname + '' ('' + m2.membernumber + '')'' as TRANSACTION_RECORDED_BY,
		ACCOUNT = case
			when tt.typeID = 1 then rglcred.thePathExpanded
			when tt.typeID = 2 then rgldeb.thePathExpanded
			when tt.typeID = 3 and rgldeb.GLCode = ''ACCOUNTSRECEIVABLE'' then rglcred.thePathExpanded
			when tt.typeID = 3 and rglcred.GLCode = ''ACCOUNTSRECEIVABLE'' then rgldeb.thePathExpanded
			when tt.typeID = 4 then rglcred.thePathExpanded
			when tt.typeID = 6 and rglcred.GLCode = ''ACCOUNTSRECEIVABLE'' then rgldeb.thePathExpanded
			when tt.typeID = 6 and rgldeb.GLCode = ''DEPOSITS'' then rglcred.thePathExpanded
			when tt.typeID = 7 then rglcred.thePathExpanded
			else '''' end,
		ACCOUNT_CODE = case
			when tt.typeID = 1 then rglcred.accountCode
			when tt.typeID = 2 then rgldeb.accountCode
			when tt.typeID = 3 and rgldeb.GLCode = ''ACCOUNTSRECEIVABLE'' then rglcred.accountCode
			when tt.typeID = 3 and rglcred.GLCode = ''ACCOUNTSRECEIVABLE'' then rgldeb.accountCode
			when tt.typeID = 4 then rglcred.accountCode
			when tt.typeID = 6 and rglcred.GLCode = ''ACCOUNTSRECEIVABLE'' then rgldeb.accountCode
			when tt.typeID = 6 and rgldeb.GLCode = ''DEPOSITS'' then rglcred.accountCode
			when tt.typeID = 7 then rglcred.accountCode
			else '''' end,
		DEBIT = case
			when tt.typeID = 1 then null
			when tt.typeID = 2 then rt.amount
			when tt.typeID = 3 and rgldeb.GLCode = ''ACCOUNTSRECEIVABLE'' then null
			when tt.typeID = 3 and rglcred.GLCode = ''ACCOUNTSRECEIVABLE'' then rt.amount
			when tt.typeID = 4 then null
			when tt.typeID = 6 and rglcred.GLCode = ''ACCOUNTSRECEIVABLE'' then rt.amount
			when tt.typeID = 6 and rgldeb.GLCode = ''DEPOSITS'' then null
			when tt.typeID = 7 then null
			else '''' end,
		CREDIT = case
			when tt.typeID = 1 then rt.amount
			when tt.typeID = 2 then null
			when tt.typeID = 3 and rgldeb.GLCode = ''ACCOUNTSRECEIVABLE'' then rt.amount
			when tt.typeID = 3 and rglcred.GLCode = ''ACCOUNTSRECEIVABLE'' then null
			when tt.typeID = 4 then rt.amount
			when tt.typeID = 6 and rglcred.GLCode = ''ACCOUNTSRECEIVABLE'' then null
			when tt.typeID = 6 and rgldeb.GLCode = ''DEPOSITS'' then rt.amount
			when tt.typeID = 7 then rt.amount
			else '''' end
	from dbo.ev_registrants as r
	inner join dbo.ev_registration as evr on evr.registrationID = r.registrationID 
		and r.registrationID = ' + cast(@registrationID as varchar(10)) + '
	inner join dbo.ams_members as mr on mr.memberID = r.memberid
	inner join dbo.ams_members as mr2 on mr2.memberID = mr.activeMemberID
	cross apply dbo.fn_ev_registrantTransactions(r.registrantid) as rt
	inner join dbo.tr_types as tt on tt.typeID = rt.typeID
	inner join dbo.ams_members as ma on ma.memberID = rt.AssignedTomemberid
	inner join dbo.ams_members as ma2 on ma2.memberID = ma.activeMemberID
	inner join dbo.ams_members as m on m.memberID = rt.recordedByMemberID
	inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
	INNER JOIN dbo.fn_getRecursiveGLAccounts(' + cast(@orgid as varchar(10)) + ') as rgldeb on rgldeb.GLAccountID = rt.debitGlAccountID
	INNER JOIN dbo.fn_getRecursiveGLAccounts(' + cast(@orgid as varchar(10)) + ') as rglcred on rglcred.GLAccountID = rt.creditGlAccountID
	WHERE rt.ownedByOrgID = ' + cast(@orgid as varchar(10)) + '
	and rt.statusID = 1
	and rt.typeID not in (5,8)
	order by r.status, 1'

-- export
EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql

RETURN 0
GO


ALTER PROC [dbo].[tr_report_accountCode]
@orgid int,
@datetype varchar(15),
@startdate datetime,
@enddate datetime,
@accrualDate bit

AS

-- set date to 11:59:59 of enddate
select @endDate = dateadd(s,-1,dateadd(day,1,DATEADD(dd, DATEDIFF(dd,0,@endDate), 0)))

-- start with all transactions in the date range (where acct hasnt been deleted)
; with allTrans AS (
	select t.transactionID, t.debitGLAccountID, t.creditGLAccountID, t.typeID, t.amount, t.accrualDate
	from dbo.tr_transactions as t
	INNER JOIN dbo.tr_GLAccounts as glDeb on glDeb.GLAccountID = t.debitGLAccountID and glDeb.status <> 'D'
	INNER JOIN dbo.tr_GLAccounts as glCred on glCred.GLAccountID = t.creditGLAccountID and glCred.status <> 'D'
	where t.ownedByOrgID = @orgID
	and t.statusID in (1,2)
	and (
		(@datetype = 'dateRecorded' and t.dateRecorded between @startdate and @endDate)
		or
		(@datetype = 'transactionDate' and t.transactionDate between @startdate and @endDate)
		or
		(@datetype = 'accrualDate' and t.accrualDate between @startdate and @endDate)
	)
), allTransDetl AS (
	select debitGLAccountID as glAccountID, case when @accrualDate = 1 then CAST(FLOOR(CAST(accrualDate AS FLOAT)) AS DATETIME) else '' end as accrualDate, amount as debit, null as credit
	from allTrans
		union all
	select creditGLAccountID as glAccountID, case when @accrualDate = 1 then CAST(FLOOR(CAST(accrualDate AS FLOAT)) AS DATETIME) else '' end as accrualDate, null as debit, amount as credit
	from allTrans
), allTransSum AS (
	select glAccountID, accrualDate, isnull(sum(debit),0) as debitAmt, isnull(sum(credit),0) as creditAmt
	from allTransDetl
	group by glAccountID, accrualDate
)
select rgl.GLAccountID, rgl.AccountCode, rgl.accountType, rgl.thePathExpanded, ats.accrualDate, 
	case 
	when rgl.accountType = 'Cash' and ats.debitAmt - ats.creditAmt >= 0 then ats.debitAmt - ats.creditAmt
	when rgl.accountType = 'Asset' and rgl.GLCode = 'ACCOUNTSRECEIVABLE' and ats.debitAmt - ats.creditAmt > 0 then ats.debitAmt - ats.creditAmt
	when rgl.accountType = 'Liability' and rgl.GLCode = 'DEPOSITS' and ats.creditAmt - ats.debitAmt <= 0 then abs(ats.creditAmt - ats.debitAmt)
	when rgl.accountType = 'Revenue' and ats.creditAmt - ats.debitAmt < 0 then abs(ats.creditAmt - ats.debitAmt)
	when rgl.accountType = 'Expense' and ats.debitAmt - ats.creditAmt >= 0 then ats.debitAmt - ats.creditAmt
	when rgl.accountType = 'Liability' and ats.creditAmt - ats.debitAmt <= 0 then abs(ats.creditAmt - ats.debitAmt)
	else null
	end as debits,
	case 
	when rgl.accountType = 'Cash' and ats.debitAmt - ats.creditAmt < 0 then abs(ats.debitAmt - ats.creditAmt)
	when rgl.accountType = 'Asset' and rgl.GLCode = 'ACCOUNTSRECEIVABLE' and ats.debitAmt - ats.creditAmt <= 0 then abs(ats.debitAmt - ats.creditAmt)
	when rgl.accountType = 'Liability' and rgl.GLCode = 'DEPOSITS' and ats.creditAmt - ats.debitAmt > 0 then ats.creditAmt - ats.debitAmt
	when rgl.accountType = 'Revenue' and ats.creditAmt - ats.debitAmt >= 0 then ats.creditAmt - ats.debitAmt
	when rgl.accountType = 'Expense' and ats.debitAmt - ats.creditAmt < 0 then abs(ats.debitAmt - ats.creditAmt)
	when rgl.accountType = 'Liability' and ats.creditAmt - ats.debitAmt > 0 then ats.creditAmt - ats.debitAmt
	else null
	end as credits
from allTransSum as ats
INNER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) as rgl on rgl.GLAccountID = ats.GLAccountID
order by rgl.thePath, ats.accrualDate

RETURN 0
GO

ALTER PROC [dbo].[tr_updateGLAccount]
@orgID int,
@GLAccountID int,
@accountName varchar(200),
@accountCode varchar(200),
@parentGLAccountID int,
@invoiceProfileID int,
@isTaxable bit,
@invoiceContentID int,
@deferredGLAccountID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- null account code if no length
	IF @accountCode is not null and len(@accountCode) = 0
		SET @accountCode = null

	-- null deferredGLAccountID if 0
	IF @deferredGLAccountID = 0
		SET @deferredGLAccountID = null

	declare @GLAccountTypeID int
	select @GLAccountTypeID = accountTypeID from dbo.tr_GLAccounts where GLAccountID = @GLAccountID

	-- null invoiceProfileID if not revenue
	IF @GLAccountTypeID <> 3
		SELECT @invoiceProfileID = null

	-- cant have multiple accounts with same accountName at same level
	-- cant have multiple accounts with same accountCode
	IF (@parentGLAccountID is null AND EXISTS (select GLAccountID FROM dbo.tr_GLAccounts where orgID = @orgID and accountName = @accountName and parentGLAccountID is null and accountTypeID = @GLAccountTypeID and GLAccountID <> @GLAccountID and status <> 'D'))
		OR (@parentGLAccountID is not null and EXISTS (select GLAccountID FROM dbo.tr_GLAccounts where orgID = @orgID and accountName = @accountName and parentGLAccountID = @parentGLAccountID and accountTypeID = @GLAccountTypeID and GLAccountID <> @GLAccountID and status <> 'D')) 
		OR (@accountCode is not null and EXISTS (select GLAccountID from dbo.tr_GLAccounts where orgID = @orgID and accountCode = @accountCode and GLAccountID <> @GLAccountID and status <> 'D'))
		OR (@GLAccountTypeID = 3 and @invoiceProfileID is null)
		RAISERROR('this account would violate distinct rules', 16, 1);
	ELSE BEGIN
		-- create new deferred account if necessary
		IF @deferredGLAccountID = -1 BEGIN
			DECLARE @accountNameDef varchar(200), @parentGLAccountIDDef int
			select @parentGLAccountIDDef = GLAccountID
				from dbo.tr_GLAccounts 
				where orgID = @orgID
				and AccountTypeID = 5 
				and GLCode = 'DEFERREDREVENUE' 
				and isSystemAccount = 1
			SET @accountNameDef = @accountName + ' Deferred'
			IF EXISTS (select GLAccountID FROM dbo.tr_GLAccounts where orgID = @orgID and accountName = @accountNameDef and parentGLAccountID = @parentGLAccountIDDef and status <> 'D')
				SET @accountNameDef = @accountName + ' ' + cast(@GLAccountID as varchar(10)) + ' Deferred'
			EXEC dbo.tr_createGLAccount @orgID=@orgID, @AccountTypeID=5, @accountName=@accountNameDef, @accountCode=null, @GLCode=null, 
				@parentGLAccountID=@parentGLAccountIDDef, @invoiceProfileID=null, @isSystemAccount=1, @isTaxable=0, 
				@invoiceContentID=null, @deferredGLAccountID=null, @GLAccountID=@deferredGLAccountID OUTPUT
		END

		UPDATE dbo.tr_GLAccounts
		SET accountName = @accountName, 
			accountCode = @accountCode, 
			parentGLAccountID = nullIf(@parentGLAccountID,0),
			isTaxable = @isTaxable,
			invoiceProfileID = @invoiceProfileID,
			invoiceContentID = @invoiceContentID,
			deferredGLAccountID = @deferredGLAccountID
		WHERE GLAccountID = @GLAccountID
		and orgID = @orgID
	END
 

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[tr_updateTaxAuthority]
@orgID int,
@taxAuthorityID int,
@authorityName varchar(200),
@accountCode varchar(200)

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- update authority
	UPDATE dbo.tr_taxAuthorities
	SET authorityName = @authorityName
	WHERE taxAuthorityID = @taxAuthorityID
	AND orgID = @orgID

	-- update GL Account for tax authority
	DECLARE @GLAccountID int, @parentGLAccountID int, @deferredGLAccountID int
	SELECT @GLAccountID = ta.GLAccountID, @parentGLAccountID = gl.parentGLAccountID, @deferredGLAccountID = gl.deferredGLAccountID
		from dbo.tr_taxAuthorities as ta
		inner join dbo.tr_GLAccounts as gl on gl.glAccountID = ta.GLAccountID 
		where ta.taxAuthorityID = @taxAuthorityID

	EXEC dbo.tr_updateGLAccount @orgID=@orgID, @GLAccountID=@GLAccountID, @accountName=@authorityName,
		@accountCode=@accountCode, @parentGLAccountID=@parentGLAccountID, @invoiceProfileID=null, @isTaxable=0, 
		@invoiceContentID=null, @deferredGLAccountID=@deferredGLAccountID


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[tr_createGLAccount]
@orgID int,
@accountTypeID int,
@accountName varchar(200),
@accountCode varchar(200),
@GLCode varchar(30),
@parentGLAccountID int,
@invoiceProfileID int,
@isSystemAccount bit,
@isTaxable bit,
@invoiceContentID int,
@deferredGLAccountID int,
@GLAccountID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	SELECT @GLAccountID = null

	-- null account/GL code if no length
	IF @accountCode is not null and len(@accountCode) = 0
		SELECT @accountCode = null
	IF @GLCode is not null and len(@GLCode) = 0
		SELECT @GLCode = null

	-- null invoiceProfileID if not revenue
	IF @accountTypeID <> 3
		SELECT @invoiceProfileID = null

	-- null deferredGLAccountID if 0
	IF @deferredGLAccountID = 0
		SET @deferredGLAccountID = null

	-- null deferredGLAccountID if not revenue or liability
	IF @accountTypeID not in (3,5)
		SELECT @deferredGLAccountID = null

	-- cant have multiple accounts with same accountName at same level (of same account type)
	-- cant have multiple accounts with same accountCode
	-- cant have multiple accounts with same GLCode
	IF (@parentGLAccountID is null AND EXISTS (select GLAccountID FROM dbo.tr_GLAccounts where orgID = @orgID and accountName = @accountName and parentGLAccountID is null and [status] <> 'D' and accountTypeID = @accountTypeID))
		OR (@parentGLAccountID is not null and EXISTS (select GLAccountID FROM dbo.tr_GLAccounts where orgID = @orgID and accountName = @accountName and parentGLAccountID = @parentGLAccountID and [status] <> 'D')) 
		OR (@accountCode is not null and EXISTS (select GLAccountID from dbo.tr_GLAccounts where orgID = @orgID and accountCode = @accountCode and [status] <> 'D'))
		OR (@GLCode is not null and EXISTS (select GLAccountID from dbo.tr_GLAccounts where orgID = @orgID and GLCode = @GLCode and [status] <> 'D'))
		OR (@accountTypeID = 3 and @invoiceProfileID is null)
		RAISERROR('this account would violate distinct rules', 16, 1);
	ELSE BEGIN
		INSERT INTO dbo.tr_GLAccounts (orgID, accountName, accountCode, parentGLAccountID, [status], isSystemAccount, isTaxable, accountTypeID, GLCode, [uid], invoiceProfileID, invoiceContentID)
		VALUES (@orgID, @accountName, nullif(@accountCode,''), @parentGLAccountID, 'A', @isSystemAccount, @isTaxable, @accountTypeID, nullif(@GLCode,''), newId(), @invoiceProfileID, @invoiceContentID)
			SELECT @GLAccountID = SCOPE_IDENTITY()

		-- create new deferred account if necessary
		IF @deferredGLAccountID = -1 BEGIN
			DECLARE @accountNameDef varchar(200), @parentGLAccountIDDef int
			select @parentGLAccountIDDef = GLAccountID
				from dbo.tr_GLAccounts 
				where orgID = @orgID
				and AccountTypeID = 5 
				and GLCode = 'DEFERREDREVENUE' 
				and isSystemAccount = 1
			SET @accountNameDef = @accountName + ' Deferred'
			IF EXISTS (select GLAccountID FROM dbo.tr_GLAccounts where orgID = @orgID and accountName = @accountNameDef and parentGLAccountID = @parentGLAccountIDDef and status <> 'D')
				SET @accountNameDef = @accountName + ' ' + cast(@GLAccountID as varchar(10)) + ' Deferred'
			EXEC dbo.tr_createGLAccount @orgID=@orgID, @AccountTypeID=5, @accountName=@accountNameDef, @accountCode=null, @GLCode=null, 
				@parentGLAccountID=@parentGLAccountIDDef, @invoiceProfileID=null, @isSystemAccount=1, @isTaxable=0, 
				@invoiceContentID=null, @deferredGLAccountID=null, @GLAccountID=@deferredGLAccountID OUTPUT
		END

		IF @deferredGLAccountID is not null
			update dbo.tr_GLAccounts
			set deferredGLAccountID = @deferredGLAccountID
			where GLAccountID = @GLAccountID
	END


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	SELECT @GLAccountID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[enableSiteFeature]
	@siteID int,
	@toolTypeList varchar(1000)
AS


declare @tblTools TABLE (toolType varchar(100))
insert into @tblTools (toolType)
select listItem from dbo.fn_varCharListToTable(@toolTypeList,',')


declare @applicationInstanceID int
select @applicationInstanceID = applicationInstanceID
	from dbo.cms_applicationInstances
	where siteID = @siteID
	and applicationInstanceName = 'admin'


declare @toolType varchar(100)
SELECT @toolType = min(toolType) from @tblTools
WHILE @toolType is not null BEGIN

	-- Accrual Accounting
	if @toolType = 'AccrualAccounting' begin
		declare @orgID int, @GLAccountID int
		select @orgID = orgID from dbo.sites where siteID = @siteID

		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''useAccrualAccounting'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=5, @accountName='Deferred Revenue Accounts', @accountCode='', @GLCode='DEFERREDREVENUE', @parentGLAccountID=null, @invoiceProfileID=null, @isSystemAccount=1, @isTaxable=0, @invoiceContentID=null, @deferredGLAccountID=null, @GLAccountID=@GLAccountID output
	end

	-- appt tracker
	if @toolType = 'AppointmentTrackerAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showApptTracker'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- email blast
	if @toolType = 'EmailBlast' begin
		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- member documents
	if @toolType = 'MemberDocs' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showMemberDocuments'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		declare @resourceTypeID int, @sectionID int, @parentSectionID int
		select @resourceTypeID = resourceTypeID from dbo.cms_siteResourceTypes where resourceType = 'ApplicationCreatedSection'
		select @sectionID = sectionID from dbo.cms_pageSections where siteID = @siteID and sectionName = 'MCAMSMemberDocuments'
		IF @sectionID is null begin
			select @parentSectionID = sectionID from dbo.cms_pageSections where sectionName = 'root' and parentSectionID is null and siteID = @siteID
			exec dbo.cms_createPageSection @siteID, @resourceTypeID, null, null, null, @parentSectionID, 'MCAMSMemberDocuments', 'MCAMSMemberDocuments', 0, @sectionID output
		end
	end	

	-- member history
	if @toolType = 'MemberHistoryAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showMemberHistory'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID
	end

	-- referrals
	if @toolType = 'ReferralsAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showReferrals'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- relationships
	if @toolType = 'RelationshipAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showRelationships'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- reports
	if @toolType = 'Reports' begin
		declare @siteCode varchar(10)
		select @sitecode = sitecode from dbo.sites where siteID = @siteID

		insert into dbo.admin_siteToolRestrictions (toolTypeID, siteID)
		select tooltypeid, @siteID 
		from dbo.admin_toolTypes 
		where toolCFC like 'Reports.custom.' + @sitecode + '.%'
		or (toolCFC like 'Reports.%' and left(toolCFC,15) <> 'Reports.custom.')
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- subscriptions
	if @toolType = 'SubscriptionAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showSubscriptions'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = 'SubRenewalAdmin'
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- tasks
	if @toolType = 'NotesAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showNotes'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	SELECT @toolType = min(toolType) from @tblTools where toolType > @toolType
END


-- refresh and assign resources
exec dbo.createadminsuite @siteid


-- these need to be added after resources are there (createadminsuite)
IF EXISTS (select toolType from @tblTools where toolType = 'AppointmentTrackerAdmin')
	exec dbo.cms_createDefaultAppointmentCategories @siteID=@siteid, @contributingMemberID=461530

IF EXISTS (select toolType from @tblTools where toolType = 'EmailBlast')
	exec dbo.cms_createDefaultEmailBlastCategories @siteID=@siteID

IF EXISTS (select toolType from @tblTools where toolType = 'MemberHistoryAdmin')
	exec dbo.cms_createDefaultHistoryAdminCategories @siteID=@siteID, @contributingMemberID=461530

IF EXISTS (select toolType from @tblTools where toolType = 'RelationshipAdmin')
	exec dbo.cms_createDefaultRelationshipCategories @siteID=@siteID, @contributingMemberID=461530

IF EXISTS (select toolType from @tblTools where toolType = 'NotesAdmin')
	exec dbo.cms_createDefaultNotesCategories @siteID=@siteid, @contributingMemberID=461530

RETURN 0
GO

ALTER PROC [dbo].[tr_createDefaultGLAccounts]
@orgID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	DECLARE @GLAccountID int, @batchID int, @invoiceProfileID int

	-- Create one default Invoice Profile
	insert into dbo.tr_invoiceProfiles (orgID, profileName, status)
	values (@orgID, 'Default Invoice Profile', 'A')
		select @invoiceProfileID = SCOPE_IDENTITY()

	EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=2, @accountName='Accounts Receivable', @accountCode='ACCOUNTSRECEIVABLE', @GLCode='ACCOUNTSRECEIVABLE', @parentGLAccountID=null, @invoiceProfileID=null, @isSystemAccount=1, @isTaxable=0, @invoiceContentID=null, @deferredGLAccountID=null, @GLAccountID=@GLAccountID output
	EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=5, @accountName='Deposits', @accountCode='DEPOSITS', @GLCode='DEPOSITS', @parentGLAccountID=null, @invoiceProfileID=null, @isSystemAccount=1, @isTaxable=0, @invoiceContentID=null, @deferredGLAccountID=null, @GLAccountID=@GLAccountID output
	EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=4, @accountName='Write Off', @accountCode='WRITEOFF', @GLCode='WRITEOFF', @parentGLAccountID=null, @invoiceProfileID=null, @isSystemAccount=1, @isTaxable=0, @invoiceContentID=null, @deferredGLAccountID=null, @GLAccountID=@GLAccountID output
	EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=3, @accountName='Misc Income', @accountCode='MISCINCOME', @GLCode='MISCINCOME', @parentGLAccountID=null, @invoiceProfileID=@invoiceProfileID, @isSystemAccount=1, @isTaxable=0, @invoiceContentID=null, @deferredGLAccountID=null, @GLAccountID=@GLAccountID output
	
	-- create pending payment batch
	EXEC dbo.tr_createBatch @orgID=@orgID, @payProfileID=null, @batchCode='PENDINGPAYMENTS', @batchName='Pending Payments', @controlAmt=0, @controlCount=0, @depositDate='1/1/2020', @isSystemCreated=1, @createdByMemberID=null, @batchID=@batchID OUTPUT
	
	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[tr_createTaxAuthority]
@orgID int,
@authorityName varchar(200),
@accountCode varchar(200),
@authorityID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- init output var
	SELECT @authorityID = null
	SELECT @accountCode = nullif(@accountCode,'')

	-- create GL Account for tax authority
	DECLARE @rc int, @GLAccountID int
	EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=5, @accountName=@authorityName, 
		@accountCode=@accountCode, @GLCode=null, @parentGLAccountID=null, @invoiceProfileID=null,
		@isSystemAccount=1, @isTaxable=0, @invoiceContentID=null, @deferredGLAccountID=null,
		@GLAccountID=@GLAccountID OUTPUT
		
	-- add authority
	INSERT INTO dbo.tr_taxAuthorities (orgID, authorityName, GLAccountID, [status])
	VALUES (@orgID, @authorityName, @GLAccountID, 'A')
		SELECT @authorityID = SCOPE_IDENTITY()


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @authorityID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[tr_deleteGLAccount]
@orgID int,
@GLAccountID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- inactive it if account is in use, otherwise, delete it.
	-- this will check all foreign keys to the GLAccountID dynamically.
	declare @innerUnion VARCHAR(max)
	declare @acctInUse bit
	declare @dynSQL nvarchar(max)

	SELECT @innerUnion = COALESCE(@innerUnion + ' union ' + char(13) + char(10), '') + 
		'select ' + COL_NAME(fc.parent_object_id, fc.parent_column_id) + ' as GLAccountID from dbo.' + OBJECT_NAME(f.parent_object_id) +
		case 
		when OBJECT_NAME(f.parent_object_id) = 'tr_taxAuthorities' and COL_NAME(fc.parent_object_id, fc.parent_column_id) = 'GLAccountID' then ' where [status] <> ''D''' 
		when OBJECT_NAME(f.parent_object_id) = 'tr_taxRules' and COL_NAME(fc.parent_object_id, fc.parent_column_id) = 'saleGLAccountID' then ' where [status] <> ''D''' 
		when OBJECT_NAME(f.parent_object_id) = 'tr_GLAccounts' and COL_NAME(fc.parent_object_id, fc.parent_column_id) = 'parentGLAccountID' then ' where [status] <> ''D''' 
		when OBJECT_NAME(f.parent_object_id) = 'tr_GLAccounts' and COL_NAME(fc.parent_object_id, fc.parent_column_id) = 'deferredGLAccountID' then ' where [status] <> ''D''' 
		else '' end
		FROM sys.foreign_keys AS f
		INNER JOIN sys.foreign_key_columns AS fc ON f.OBJECT_ID = fc.constraint_object_id
		and OBJECT_NAME (f.referenced_object_id) = 'tr_GLAccounts'
		and COL_NAME(fc.referenced_object_id,fc.referenced_column_id) = 'GLAccountID'
	set @acctInUse = 1
	set @dynSQL = '
		if exists (
			select GLAccountID from (' + @innerUnion + ') as tmp 
			where GLAccountID is not null
			and GLAccountID = ' + cast(@GLAccountID as varchar(10)) + '
		) set @acctInUse = 1
		else 
			set @acctInUse = 0
		'
	exec sp_executesql @dynSQL, N'@acctInUse bit output', @acctInUse output

	-- cant delete systemaccounts so ignore if systemaccount
	-- also null invoiceprofileid if we are Deleting the account
	IF @acctInUse = 0
		update dbo.tr_GLAccounts
		set [status] = 'D', invoiceProfileID = null, deferredGLAccountID = null
		where GLAccountID = @GLAccountID
		and orgID = @orgID
		and [status] <> 'D'
		and isSystemAccount = 0
	ELSE
		update dbo.tr_GLAccounts
		set [status] = 'I'
		where GLAccountID = @GLAccountID
		and orgID = @orgID
		and [status] = 'A'
		and isSystemAccount = 0

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

CREATE PROC dbo.tr_deleteUnlinkedDeferredAccounts
@orgID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @GLAccountID int
	select @GLAccountID = GLAccountID
		from dbo.tr_GLAccounts 
		where orgID = @orgID
		and AccountTypeID = 5 
		and GLCode = 'DEFERREDREVENUE' 
		and isSystemAccount = 1

	declare @tblGLs TABLE (glAccountID int)
	insert into @tblGLs (glAccountID)
	select gl.glAccountID
	from dbo.tr_GLAccounts as gl
	where gl.parentGLAccountID = @GLAccountID
	and NOT EXISTS (select distinct GLAccountID from dbo.tr_GLAccounts where orgID = 24 and status <> 'D' and deferredGLAccountID = gl.glAccountID)

	declare @minGLAccountID int
	select @minGLAccountID = min(GLAccountID) from @tblGLs
	while @minGLAccountID is not null BEGIN
		EXEC dbo.tr_deleteGLAccount @orgID=@orgID, @GLAccountID=@minGLAccountID
		select @minGLAccountID = min(GLAccountID) from @tblGLs where GLAccountID > @minGLAccountID
	END


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[tr_updateGLAccount]
@orgID int,
@GLAccountID int,
@accountName varchar(200),
@accountCode varchar(200),
@parentGLAccountID int,
@invoiceProfileID int,
@isTaxable bit,
@invoiceContentID int,
@deferredGLAccountID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- null account code if no length
	IF @accountCode is not null and len(@accountCode) = 0
		SET @accountCode = null

	-- null deferredGLAccountID if 0
	IF @deferredGLAccountID = 0
		SET @deferredGLAccountID = null

	declare @GLAccountTypeID int
	select @GLAccountTypeID = accountTypeID from dbo.tr_GLAccounts where GLAccountID = @GLAccountID

	-- null invoiceProfileID if not revenue
	IF @GLAccountTypeID <> 3
		SELECT @invoiceProfileID = null

	-- cant have multiple accounts with same accountName at same level
	-- cant have multiple accounts with same accountCode
	IF (@parentGLAccountID is null AND EXISTS (select GLAccountID FROM dbo.tr_GLAccounts where orgID = @orgID and accountName = @accountName and parentGLAccountID is null and accountTypeID = @GLAccountTypeID and GLAccountID <> @GLAccountID and status <> 'D'))
		OR (@parentGLAccountID is not null and EXISTS (select GLAccountID FROM dbo.tr_GLAccounts where orgID = @orgID and accountName = @accountName and parentGLAccountID = @parentGLAccountID and accountTypeID = @GLAccountTypeID and GLAccountID <> @GLAccountID and status <> 'D')) 
		OR (@accountCode is not null and EXISTS (select GLAccountID from dbo.tr_GLAccounts where orgID = @orgID and accountCode = @accountCode and GLAccountID <> @GLAccountID and status <> 'D'))
		OR (@GLAccountTypeID = 3 and @invoiceProfileID is null)
		RAISERROR('this account would violate distinct rules', 16, 1);
	ELSE BEGIN
		-- create new deferred account if necessary
		IF @deferredGLAccountID = -1 BEGIN
			DECLARE @accountNameDef varchar(200), @parentGLAccountIDDef int
			select @parentGLAccountIDDef = GLAccountID
				from dbo.tr_GLAccounts 
				where orgID = @orgID
				and AccountTypeID = 5 
				and GLCode = 'DEFERREDREVENUE' 
				and isSystemAccount = 1
			SET @accountNameDef = @accountName + ' Deferred'
			IF EXISTS (select GLAccountID FROM dbo.tr_GLAccounts where orgID = @orgID and accountName = @accountNameDef and parentGLAccountID = @parentGLAccountIDDef and status <> 'D')
				SET @accountNameDef = @accountName + ' ' + cast(@GLAccountID as varchar(10)) + ' Deferred'
			EXEC dbo.tr_createGLAccount @orgID=@orgID, @AccountTypeID=5, @accountName=@accountNameDef, @accountCode=null, @GLCode=null, 
				@parentGLAccountID=@parentGLAccountIDDef, @invoiceProfileID=null, @isSystemAccount=1, @isTaxable=0, 
				@invoiceContentID=null, @deferredGLAccountID=null, @GLAccountID=@deferredGLAccountID OUTPUT
		END

		UPDATE dbo.tr_GLAccounts
		SET accountName = @accountName, 
			accountCode = @accountCode, 
			parentGLAccountID = nullIf(@parentGLAccountID,0),
			isTaxable = @isTaxable,
			invoiceProfileID = @invoiceProfileID,
			invoiceContentID = @invoiceContentID,
			deferredGLAccountID = @deferredGLAccountID
		WHERE GLAccountID = @GLAccountID
		and orgID = @orgID

		-- cleanup orphaned deferred accounts		
		EXEC dbo.tr_deleteUnlinkedDeferredAccounts @orgID
	END
 

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[tr_deleteGLAccount]
@orgID int,
@GLAccountID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- inactive it if account is in use, otherwise, delete it.
	-- this will check all foreign keys to the GLAccountID dynamically.
	declare @innerUnion VARCHAR(max)
	declare @acctInUse bit
	declare @dynSQL nvarchar(max)

	SELECT @innerUnion = COALESCE(@innerUnion + ' union ' + char(13) + char(10), '') + 
		'select ' + COL_NAME(fc.parent_object_id, fc.parent_column_id) + ' as GLAccountID from dbo.' + OBJECT_NAME(f.parent_object_id) +
		case 
		when OBJECT_NAME(f.parent_object_id) = 'tr_taxAuthorities' and COL_NAME(fc.parent_object_id, fc.parent_column_id) = 'GLAccountID' then ' where [status] <> ''D''' 
		when OBJECT_NAME(f.parent_object_id) = 'tr_taxRules' and COL_NAME(fc.parent_object_id, fc.parent_column_id) = 'saleGLAccountID' then ' where [status] <> ''D''' 
		when OBJECT_NAME(f.parent_object_id) = 'tr_GLAccounts' and COL_NAME(fc.parent_object_id, fc.parent_column_id) = 'parentGLAccountID' then ' where [status] <> ''D''' 
		when OBJECT_NAME(f.parent_object_id) = 'tr_GLAccounts' and COL_NAME(fc.parent_object_id, fc.parent_column_id) = 'deferredGLAccountID' then ' where [status] <> ''D''' 
		else '' end
		FROM sys.foreign_keys AS f
		INNER JOIN sys.foreign_key_columns AS fc ON f.OBJECT_ID = fc.constraint_object_id
		and OBJECT_NAME (f.referenced_object_id) = 'tr_GLAccounts'
		and COL_NAME(fc.referenced_object_id,fc.referenced_column_id) = 'GLAccountID'
	set @acctInUse = 1
	set @dynSQL = '
		if exists (
			select GLAccountID from (' + @innerUnion + ') as tmp 
			where GLAccountID is not null
			and GLAccountID = ' + cast(@GLAccountID as varchar(10)) + '
		) set @acctInUse = 1
		else 
			set @acctInUse = 0
		'
	exec sp_executesql @dynSQL, N'@acctInUse bit output', @acctInUse output

	-- also null invoiceprofileid if we are Deleting the account
	IF @acctInUse = 0 BEGIN
		update dbo.tr_GLAccounts
		set [status] = 'D', invoiceProfileID = null, deferredGLAccountID = null
		where GLAccountID = @GLAccountID
		and orgID = @orgID
		and [status] <> 'D'

		-- cleanup orphaned deferred accounts		
		declare @parentGLAccountIDDef int
		select @parentGLAccountIDDef = GLAccountID
			from dbo.tr_GLAccounts 
			where orgID = @orgID
			and AccountTypeID = 5 
			and GLCode = 'DEFERREDREVENUE' 
			and isSystemAccount = 1		
		IF NOT EXISTS (select GLAccountID from dbo.tr_GLAccounts where GLAccountID = @GLAccountID and parentGLAccountID = @parentGLAccountIDDef)
			EXEC dbo.tr_deleteUnlinkedDeferredAccounts @orgID
	END
	ELSE
		update dbo.tr_GLAccounts
		set [status] = 'I'
		where GLAccountID = @GLAccountID
		and orgID = @orgID
		and [status] = 'A'

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO


ALTER PROC [dbo].[tr_createDefaultGLAccounts]
@orgID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	DECLARE @GLAccountID int, @batchID int, @invoiceProfileID int

	-- Create one default Invoice Profile
	insert into dbo.tr_invoiceProfiles (orgID, profileName, status)
	values (@orgID, 'Default Invoice Profile', 'A')
		select @invoiceProfileID = SCOPE_IDENTITY()

	EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=2, @accountName='Accounts Receivable', @accountCode='ACCOUNTSRECEIVABLE', @GLCode='ACCOUNTSRECEIVABLE', @parentGLAccountID=null, @invoiceProfileID=null, @isSystemAccount=1, @isTaxable=0, @invoiceContentID=null, @deferredGLAccountID=null, @GLAccountID=@GLAccountID output
	EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=5, @accountName='Deposits', @accountCode='DEPOSITS', @GLCode='DEPOSITS', @parentGLAccountID=null, @invoiceProfileID=null, @isSystemAccount=1, @isTaxable=0, @invoiceContentID=null, @deferredGLAccountID=null, @GLAccountID=@GLAccountID output
	EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=5, @accountName='Sales Tax Accounts', @accountCode=null, @GLCode='SALESTAX', @parentGLAccountID=null, @invoiceProfileID=null, @isSystemAccount=1, @isTaxable=0, @invoiceContentID=null, @deferredGLAccountID=null, @GLAccountID=@GLAccountID output
	EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=4, @accountName='Write Off', @accountCode='WRITEOFF', @GLCode='WRITEOFF', @parentGLAccountID=null, @invoiceProfileID=null, @isSystemAccount=1, @isTaxable=0, @invoiceContentID=null, @deferredGLAccountID=null, @GLAccountID=@GLAccountID output
	EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=3, @accountName='Misc Income', @accountCode='MISCINCOME', @GLCode='MISCINCOME', @parentGLAccountID=null, @invoiceProfileID=@invoiceProfileID, @isSystemAccount=1, @isTaxable=0, @invoiceContentID=null, @deferredGLAccountID=null, @GLAccountID=@GLAccountID output
	
	-- create pending payment batch
	EXEC dbo.tr_createBatch @orgID=@orgID, @payProfileID=null, @batchCode='PENDINGPAYMENTS', @batchName='Pending Payments', @controlAmt=0, @controlCount=0, @depositDate='1/1/2020', @isSystemCreated=1, @createdByMemberID=null, @batchID=@batchID OUTPUT
	
	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

declare @orgID int, @GLAccountID int
select @orgID = min(orgID) from organizations
while @orgID is not null BEGIN
	EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=5, @accountName='Sales Tax Accounts', @accountCode=null, @GLCode='SALESTAX', @parentGLAccountID=null, @invoiceProfileID=null, @isSystemAccount=1, @isTaxable=0, @invoiceContentID=null, @deferredGLAccountID=null, @GLAccountID=@GLAccountID output
	select @orgID = min(orgID) from organizations where orgID > @orgID
END
GO

declare @tblTax TABLE (GLAccountID int, orgID int, newParentGLAccountID int)
insert into @tblTax (GLAccountID, orgID)
select GLAccountID, orgID
from dbo.tr_GLAccounts
where accountTypeID = 5
and (GLCode is null or GLCode not in ('DEPOSITS','DEFERREDREVENUE','SALESTAX'))

update tmp
set tmp.newParentGLAccountID = tax.GLAccountID
from @tblTax as tmp
inner join dbo.tr_GLAccounts as tax on tax.orgID = tmp.orgID
where tax.GLCode = 'SALESTAX'
and tax.isSystemAccount = 1	

update gl
set gl.parentGLAccountID = tmp.newParentGLAccountID
from dbo.tr_GLAccounts as gl
inner join @tblTax as tmp on tmp.GLAccountID = gl.GLAccountID
GO

ALTER PROC [dbo].[tr_createTaxAuthority]
@orgID int,
@authorityName varchar(200),
@accountCode varchar(200),
@authorityID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- init output var
	SELECT @authorityID = null
	SELECT @accountCode = nullif(@accountCode,'')

	-- create GL Account for tax authority
	DECLARE @parentGLAccountID int, @GLAccountID int
	select @parentGLAccountID = GLAccountID
		from dbo.tr_GLAccounts 
		where orgID = @orgID
		and AccountTypeID = 5 
		and GLCode = 'SALESTAX' 
		and isSystemAccount = 1
	EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=5, @accountName=@authorityName, 
		@accountCode=@accountCode, @GLCode=null, @parentGLAccountID=@parentGLAccountID, @invoiceProfileID=null,
		@isSystemAccount=1, @isTaxable=0, @invoiceContentID=null, @deferredGLAccountID=null,
		@GLAccountID=@GLAccountID OUTPUT
		
	-- add authority
	INSERT INTO dbo.tr_taxAuthorities (orgID, authorityName, GLAccountID, [status])
	VALUES (@orgID, @authorityName, @GLAccountID, 'A')
		SELECT @authorityID = SCOPE_IDENTITY()


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @authorityID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[enableSiteFeature]
	@siteID int,
	@toolTypeList varchar(1000)
AS


declare @tblTools TABLE (toolType varchar(100))
insert into @tblTools (toolType)
select listItem from dbo.fn_varCharListToTable(@toolTypeList,',')


declare @applicationInstanceID int
select @applicationInstanceID = applicationInstanceID
	from dbo.cms_applicationInstances
	where siteID = @siteID
	and applicationInstanceName = 'admin'


declare @toolType varchar(100)
SELECT @toolType = min(toolType) from @tblTools
WHILE @toolType is not null BEGIN

	-- Accrual Accounting
	if @toolType = 'AccrualAccounting' begin
		declare @orgID int, @GLAccountID int
		select @orgID = orgID from dbo.sites where siteID = @siteID

		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''useAccrualAccounting'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=5, @accountName='Deferred Revenue Accounts', @accountCode='', @GLCode='DEFERREDREVENUE', @parentGLAccountID=null, @invoiceProfileID=null, @isSystemAccount=1, @isTaxable=0, @invoiceContentID=null, @deferredGLAccountID=null, @GLAccountID=@GLAccountID output
		EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=5, @accountName='Deferred Sales Tax Accounts', @accountCode='', @GLCode='DEFERREDTAX', @parentGLAccountID=null, @invoiceProfileID=null, @isSystemAccount=1, @isTaxable=0, @invoiceContentID=null, @deferredGLAccountID=null, @GLAccountID=@GLAccountID output
	end

	-- appt tracker
	if @toolType = 'AppointmentTrackerAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showApptTracker'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- email blast
	if @toolType = 'EmailBlast' begin
		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- member documents
	if @toolType = 'MemberDocs' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showMemberDocuments'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		declare @resourceTypeID int, @sectionID int, @parentSectionID int
		select @resourceTypeID = resourceTypeID from dbo.cms_siteResourceTypes where resourceType = 'ApplicationCreatedSection'
		select @sectionID = sectionID from dbo.cms_pageSections where siteID = @siteID and sectionName = 'MCAMSMemberDocuments'
		IF @sectionID is null begin
			select @parentSectionID = sectionID from dbo.cms_pageSections where sectionName = 'root' and parentSectionID is null and siteID = @siteID
			exec dbo.cms_createPageSection @siteID, @resourceTypeID, null, null, null, @parentSectionID, 'MCAMSMemberDocuments', 'MCAMSMemberDocuments', 0, @sectionID output
		end
	end	

	-- member history
	if @toolType = 'MemberHistoryAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showMemberHistory'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID
	end

	-- referrals
	if @toolType = 'ReferralsAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showReferrals'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- relationships
	if @toolType = 'RelationshipAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showRelationships'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- reports
	if @toolType = 'Reports' begin
		declare @siteCode varchar(10)
		select @sitecode = sitecode from dbo.sites where siteID = @siteID

		insert into dbo.admin_siteToolRestrictions (toolTypeID, siteID)
		select tooltypeid, @siteID 
		from dbo.admin_toolTypes 
		where toolCFC like 'Reports.custom.' + @sitecode + '.%'
		or (toolCFC like 'Reports.%' and left(toolCFC,15) <> 'Reports.custom.')
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- subscriptions
	if @toolType = 'SubscriptionAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showSubscriptions'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = 'SubRenewalAdmin'
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- tasks
	if @toolType = 'NotesAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showNotes'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	SELECT @toolType = min(toolType) from @tblTools where toolType > @toolType
END


-- refresh and assign resources
exec dbo.createadminsuite @siteid


-- these need to be added after resources are there (createadminsuite)
IF EXISTS (select toolType from @tblTools where toolType = 'AppointmentTrackerAdmin')
	exec dbo.cms_createDefaultAppointmentCategories @siteID=@siteid, @contributingMemberID=461530

IF EXISTS (select toolType from @tblTools where toolType = 'EmailBlast')
	exec dbo.cms_createDefaultEmailBlastCategories @siteID=@siteID

IF EXISTS (select toolType from @tblTools where toolType = 'MemberHistoryAdmin')
	exec dbo.cms_createDefaultHistoryAdminCategories @siteID=@siteID, @contributingMemberID=461530

IF EXISTS (select toolType from @tblTools where toolType = 'RelationshipAdmin')
	exec dbo.cms_createDefaultRelationshipCategories @siteID=@siteID, @contributingMemberID=461530

IF EXISTS (select toolType from @tblTools where toolType = 'NotesAdmin')
	exec dbo.cms_createDefaultNotesCategories @siteID=@siteid, @contributingMemberID=461530

RETURN 0
GO

ALTER PROC [dbo].[tr_updateGLAccount]
@orgID int,
@GLAccountID int,
@accountName varchar(200),
@accountCode varchar(200),
@parentGLAccountID int,
@invoiceProfileID int,
@isTaxable bit,
@invoiceContentID int,
@deferredGLAccountID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- null account code if no length
	IF @accountCode is not null and len(@accountCode) = 0
		SET @accountCode = null

	-- null deferredGLAccountID if 0
	IF @deferredGLAccountID = 0
		SET @deferredGLAccountID = null

	declare @GLAccountTypeID int
	select @GLAccountTypeID = accountTypeID from dbo.tr_GLAccounts where GLAccountID = @GLAccountID

	-- null invoiceProfileID if not revenue
	IF @GLAccountTypeID <> 3
		SELECT @invoiceProfileID = null

	-- cant have multiple accounts with same accountName at same level
	-- cant have multiple accounts with same accountCode
	IF (@parentGLAccountID is null AND EXISTS (select GLAccountID FROM dbo.tr_GLAccounts where orgID = @orgID and accountName = @accountName and parentGLAccountID is null and accountTypeID = @GLAccountTypeID and GLAccountID <> @GLAccountID and status <> 'D'))
		OR (@parentGLAccountID is not null and EXISTS (select GLAccountID FROM dbo.tr_GLAccounts where orgID = @orgID and accountName = @accountName and parentGLAccountID = @parentGLAccountID and accountTypeID = @GLAccountTypeID and GLAccountID <> @GLAccountID and status <> 'D')) 
		OR (@accountCode is not null and EXISTS (select GLAccountID from dbo.tr_GLAccounts where orgID = @orgID and accountCode = @accountCode and GLAccountID <> @GLAccountID and status <> 'D'))
		OR (@GLAccountTypeID = 3 and @invoiceProfileID is null)
		RAISERROR('this account would violate distinct rules', 16, 1);
	ELSE BEGIN
		-- create new deferred account if necessary
		IF @deferredGLAccountID = -1 BEGIN
			DECLARE @accountNameDef varchar(200), @parentGLAccountIDDef int
			select @parentGLAccountIDDef = GLAccountID
				from dbo.tr_GLAccounts 
				where orgID = @orgID
				and AccountTypeID = 5 
				and (
					(@GLAccountTypeID = 3 and GLCode = 'DEFERREDREVENUE')
					OR
					(@GLAccountTypeID = 5 and GLCode = 'DEFERREDTAX')
				)
				and isSystemAccount = 1
			SET @accountNameDef = @accountName + ' Deferred'
			IF EXISTS (select GLAccountID FROM dbo.tr_GLAccounts where orgID = @orgID and accountName = @accountNameDef and parentGLAccountID = @parentGLAccountIDDef and status <> 'D')
				SET @accountNameDef = @accountName + ' ' + cast(@GLAccountID as varchar(10)) + ' Deferred'
			EXEC dbo.tr_createGLAccount @orgID=@orgID, @AccountTypeID=5, @accountName=@accountNameDef, @accountCode=null, @GLCode=null, 
				@parentGLAccountID=@parentGLAccountIDDef, @invoiceProfileID=null, @isSystemAccount=1, @isTaxable=0, 
				@invoiceContentID=null, @deferredGLAccountID=null, @GLAccountID=@deferredGLAccountID OUTPUT
		END

		UPDATE dbo.tr_GLAccounts
		SET accountName = @accountName, 
			accountCode = @accountCode, 
			parentGLAccountID = nullIf(@parentGLAccountID,0),
			isTaxable = @isTaxable,
			invoiceProfileID = @invoiceProfileID,
			invoiceContentID = @invoiceContentID,
			deferredGLAccountID = @deferredGLAccountID
		WHERE GLAccountID = @GLAccountID
		and orgID = @orgID

		-- cleanup orphaned deferred accounts		
		EXEC dbo.tr_deleteUnlinkedDeferredAccounts @orgID
	END
 

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[tr_createGLAccount]
@orgID int,
@accountTypeID int,
@accountName varchar(200),
@accountCode varchar(200),
@GLCode varchar(30),
@parentGLAccountID int,
@invoiceProfileID int,
@isSystemAccount bit,
@isTaxable bit,
@invoiceContentID int,
@deferredGLAccountID int,
@GLAccountID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	SELECT @GLAccountID = null

	-- null account/GL code if no length
	IF @accountCode is not null and len(@accountCode) = 0
		SELECT @accountCode = null
	IF @GLCode is not null and len(@GLCode) = 0
		SELECT @GLCode = null

	-- null invoiceProfileID if not revenue
	IF @accountTypeID <> 3
		SELECT @invoiceProfileID = null

	-- null deferredGLAccountID if 0
	IF @deferredGLAccountID = 0
		SET @deferredGLAccountID = null

	-- null deferredGLAccountID if not revenue or liability
	IF @accountTypeID not in (3,5)
		SELECT @deferredGLAccountID = null

	-- cant have multiple accounts with same accountName at same level (of same account type)
	-- cant have multiple accounts with same accountCode
	-- cant have multiple accounts with same GLCode
	IF (@parentGLAccountID is null AND EXISTS (select GLAccountID FROM dbo.tr_GLAccounts where orgID = @orgID and accountName = @accountName and parentGLAccountID is null and [status] <> 'D' and accountTypeID = @accountTypeID))
		OR (@parentGLAccountID is not null and EXISTS (select GLAccountID FROM dbo.tr_GLAccounts where orgID = @orgID and accountName = @accountName and parentGLAccountID = @parentGLAccountID and [status] <> 'D')) 
		OR (@accountCode is not null and EXISTS (select GLAccountID from dbo.tr_GLAccounts where orgID = @orgID and accountCode = @accountCode and [status] <> 'D'))
		OR (@GLCode is not null and EXISTS (select GLAccountID from dbo.tr_GLAccounts where orgID = @orgID and GLCode = @GLCode and [status] <> 'D'))
		OR (@accountTypeID = 3 and @invoiceProfileID is null)
		RAISERROR('this account would violate distinct rules', 16, 1);
	ELSE BEGIN
		INSERT INTO dbo.tr_GLAccounts (orgID, accountName, accountCode, parentGLAccountID, [status], isSystemAccount, isTaxable, accountTypeID, GLCode, [uid], invoiceProfileID, invoiceContentID)
		VALUES (@orgID, @accountName, nullif(@accountCode,''), @parentGLAccountID, 'A', @isSystemAccount, @isTaxable, @accountTypeID, nullif(@GLCode,''), newId(), @invoiceProfileID, @invoiceContentID)
			SELECT @GLAccountID = SCOPE_IDENTITY()

		-- create new deferred account if necessary
		IF @deferredGLAccountID = -1 BEGIN
			DECLARE @accountNameDef varchar(200), @parentGLAccountIDDef int
			select @parentGLAccountIDDef = GLAccountID
				from dbo.tr_GLAccounts 
				where orgID = @orgID
				and AccountTypeID = 5 
				and (
					(@accountTypeID = 3 and GLCode = 'DEFERREDREVENUE')
					OR
					(@accountTypeID = 5 and GLCode = 'DEFERREDTAX')
				)
				and isSystemAccount = 1
			SET @accountNameDef = @accountName + ' Deferred'
			IF EXISTS (select GLAccountID FROM dbo.tr_GLAccounts where orgID = @orgID and accountName = @accountNameDef and parentGLAccountID = @parentGLAccountIDDef and status <> 'D')
				SET @accountNameDef = @accountName + ' ' + cast(@GLAccountID as varchar(10)) + ' Deferred'
			EXEC dbo.tr_createGLAccount @orgID=@orgID, @AccountTypeID=5, @accountName=@accountNameDef, @accountCode=null, @GLCode=null, 
				@parentGLAccountID=@parentGLAccountIDDef, @invoiceProfileID=null, @isSystemAccount=1, @isTaxable=0, 
				@invoiceContentID=null, @deferredGLAccountID=null, @GLAccountID=@deferredGLAccountID OUTPUT
		END

		IF @deferredGLAccountID is not null
			update dbo.tr_GLAccounts
			set deferredGLAccountID = @deferredGLAccountID
			where GLAccountID = @GLAccountID
	END


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	SELECT @GLAccountID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[tr_deleteUnlinkedDeferredAccounts]
@orgID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @tblGLs TABLE (glAccountID int)
	insert into @tblGLs (glAccountID)
	select gl.glAccountID
	from dbo.tr_GLAccounts as gl
	inner join dbo.tr_GLAccounts as gl2 on gl2.GLAccountID = gl.parentGLAccountID
	where gl.orgID = @orgID
	and gl.accountTypeID = 5 
	and gl.isSystemAccount = 1
	and gl.status = 'A'
	and gl2.GLCode in ('DEFERREDREVENUE','DEFERREDTAX')
	and NOT EXISTS (select distinct GLAccountID from dbo.tr_GLAccounts where orgID = @orgID and status = 'A' and deferredGLAccountID = gl.glAccountID)

	declare @minGLAccountID int
	select @minGLAccountID = min(GLAccountID) from @tblGLs
	while @minGLAccountID is not null BEGIN
		EXEC dbo.tr_deleteGLAccount @orgID=@orgID, @GLAccountID=@minGLAccountID
		select @minGLAccountID = min(GLAccountID) from @tblGLs where GLAccountID > @minGLAccountID
	END


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[tr_deleteGLAccount]
@orgID int,
@GLAccountID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- inactive it if account is in use, otherwise, delete it.
	-- this will check all foreign keys to the GLAccountID dynamically.
	declare @innerUnion VARCHAR(max)
	declare @acctInUse bit
	declare @dynSQL nvarchar(max)

	SELECT @innerUnion = COALESCE(@innerUnion + ' union ' + char(13) + char(10), '') + 
		'select ' + COL_NAME(fc.parent_object_id, fc.parent_column_id) + ' as GLAccountID from dbo.' + OBJECT_NAME(f.parent_object_id) +
		case 
		when OBJECT_NAME(f.parent_object_id) = 'tr_taxAuthorities' and COL_NAME(fc.parent_object_id, fc.parent_column_id) = 'GLAccountID' then ' where [status] <> ''D''' 
		when OBJECT_NAME(f.parent_object_id) = 'tr_taxRules' and COL_NAME(fc.parent_object_id, fc.parent_column_id) = 'saleGLAccountID' then ' where [status] <> ''D''' 
		when OBJECT_NAME(f.parent_object_id) = 'tr_GLAccounts' and COL_NAME(fc.parent_object_id, fc.parent_column_id) = 'parentGLAccountID' then ' where [status] <> ''D''' 
		when OBJECT_NAME(f.parent_object_id) = 'tr_GLAccounts' and COL_NAME(fc.parent_object_id, fc.parent_column_id) = 'deferredGLAccountID' then ' where [status] <> ''D''' 
		else '' end
		FROM sys.foreign_keys AS f
		INNER JOIN sys.foreign_key_columns AS fc ON f.OBJECT_ID = fc.constraint_object_id
		and OBJECT_NAME (f.referenced_object_id) = 'tr_GLAccounts'
		and COL_NAME(fc.referenced_object_id,fc.referenced_column_id) = 'GLAccountID'
	set @acctInUse = 1
	set @dynSQL = '
		if exists (
			select GLAccountID from (' + @innerUnion + ') as tmp 
			where GLAccountID is not null
			and GLAccountID = ' + cast(@GLAccountID as varchar(10)) + '
		) set @acctInUse = 1
		else 
			set @acctInUse = 0
		'
	exec sp_executesql @dynSQL, N'@acctInUse bit output', @acctInUse output

	-- also null invoiceprofileid if we are Deleting the account
	IF @acctInUse = 0 BEGIN
		update dbo.tr_GLAccounts
		set [status] = 'D', invoiceProfileID = null, deferredGLAccountID = null
		where GLAccountID = @GLAccountID
		and orgID = @orgID
		and [status] <> 'D'

		declare @GLAccountTypeID int, @parentGLAccountID int
		select @GLAccountTypeID = accountTypeID, @parentGLAccountID = parentGLAccountID from dbo.tr_GLAccounts where GLAccountID = @GLAccountID

		-- cleanup orphaned deferred accounts		
		declare @parentGLAccountIDDef int
		select @parentGLAccountIDDef = GLAccountID
			from dbo.tr_GLAccounts 
			where orgID = @orgID
			and AccountTypeID = 5 
			and (
				(@GLAccountTypeID = 3 and GLCode = 'DEFERREDREVENUE')
				OR
				(@GLAccountTypeID = 5 and GLCode = 'DEFERREDTAX')
			)
			and isSystemAccount = 1		
		IF isnull(@parentGLAccountID,0) <> isnull(@parentGLAccountIDDef,0)
			EXEC dbo.tr_deleteUnlinkedDeferredAccounts @orgID
	END
	ELSE
		update dbo.tr_GLAccounts
		set [status] = 'I'
		where GLAccountID = @GLAccountID
		and orgID = @orgID
		and [status] = 'A'

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER FUNCTION [dbo].[fn_getGLAccountsStructureXML] (@orgID int)
RETURNS xml
AS
BEGIN
	DECLARE @xmlStructure xml
	
	SELECT @xmlStructure = (
		select (
			SELECT profileid, profilename
			from dbo.tr_invoiceProfiles as [profile]
			where orgID = @orgID
			order by profilename
			FOR XML AUTO, root('invoiceprofiles'), TYPE
			),
			isnull((
			select [content].contentid, [content].contenttitle, [content].rawcontent
			from dbo.cms_content as c
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = c.siteResourceID
			inner join dbo.cms_siteResourceTypes as srt on srt.resourceTypeID = sr.resourceTypeID
			inner join dbo.cms_siteResources as sr2 on sr2.siteResourceID = sr.parentSiteResourceID
			inner join dbo.cms_siteResourceTypes as srt2 on srt2.resourceTypeID = sr2.resourceTypeID
			inner join dbo.sites as s on s.siteID = c.siteID
			cross apply dbo.fn_getContent(c.contentID,1) as [content]
			where s.orgID = @orgID
			and sr.siteResourceStatusID = 1
			and srt.resourceType = 'ApplicationCreatedContent'
			and srt2.resourceType = 'GLAccountsAdmin'
			ORDER BY 1
			FOR XML AUTO, root('invoicecontent'), TYPE),'<invoicecontent/>'),
			(
			SELECT account.glaccountid, account.accounttypeid, account.accountname, account.status, 
				isnull(account.invoiceprofileid,0) as invoiceprofileid, account.thepathexpanded, 
				isnull(account.accountcode,'') as accountcode, isnull(account.glcode,'') as glcode, 
				isnull(account.parentglaccountid,0) as parentglaccountid, 
				isnull(account.deferredglaccountid,0) as deferredglaccountid, account.issystemaccount, 
				isnull(account.istaxable,0) as istaxable, isnull(gl.uid,'') as [uid],
				isnull(gl.invoiceContentID,0) as invoicecontentid
			FROM dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as account
			INNER JOIN dbo.tr_GlAccounts as gl on gl.glAccountID = account.glAccountID
			where account.glaccountid > 0
			and account.status <> 'D'
			and (account.isSystemAccount = 0 OR (account.accounttypeid = 5 AND isnull(account.glcode,'') <> 'DEPOSITS'))
			order by account.thePath
			FOR XML AUTO, root('accounts'), TYPE
			)
		FOR XML RAW('GLAccountsStructure'), TYPE
		)

	RETURN @xmlStructure

END
GO

ALTER PROC [dbo].[tr_updateGLAccount]
@orgID int,
@GLAccountID int,
@accountName varchar(200),
@accountCode varchar(200),
@parentGLAccountID int,
@invoiceProfileID int,
@isTaxable bit,
@invoiceContentID int,
@deferredGLAccountID int,
@bypassDeferredCleanup bit

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- null account code if no length
	IF @accountCode is not null and len(@accountCode) = 0
		SET @accountCode = null

	-- null deferredGLAccountID if 0
	IF @deferredGLAccountID = 0
		SET @deferredGLAccountID = null

	declare @GLAccountTypeID int
	select @GLAccountTypeID = accountTypeID from dbo.tr_GLAccounts where GLAccountID = @GLAccountID

	-- null invoiceProfileID if not revenue
	IF @GLAccountTypeID <> 3
		SELECT @invoiceProfileID = null

	-- cant have multiple accounts with same accountName at same level
	-- cant have multiple accounts with same accountCode
	IF (@parentGLAccountID is null AND EXISTS (select GLAccountID FROM dbo.tr_GLAccounts where orgID = @orgID and accountName = @accountName and parentGLAccountID is null and accountTypeID = @GLAccountTypeID and GLAccountID <> @GLAccountID and status <> 'D'))
		OR (@parentGLAccountID is not null and EXISTS (select GLAccountID FROM dbo.tr_GLAccounts where orgID = @orgID and accountName = @accountName and parentGLAccountID = @parentGLAccountID and accountTypeID = @GLAccountTypeID and GLAccountID <> @GLAccountID and status <> 'D')) 
		OR (@accountCode is not null and EXISTS (select GLAccountID from dbo.tr_GLAccounts where orgID = @orgID and accountCode = @accountCode and GLAccountID <> @GLAccountID and status <> 'D'))
		OR (@GLAccountTypeID = 3 and @invoiceProfileID is null)
		RAISERROR('this account would violate distinct rules', 16, 1);
	ELSE BEGIN
		-- create new deferred account if necessary
		IF @deferredGLAccountID = -1 BEGIN
			DECLARE @accountNameDef varchar(200), @parentGLAccountIDDef int
			select @parentGLAccountIDDef = GLAccountID
				from dbo.tr_GLAccounts 
				where orgID = @orgID
				and AccountTypeID = 5 
				and (
					(@GLAccountTypeID = 3 and GLCode = 'DEFERREDREVENUE')
					OR
					(@GLAccountTypeID = 5 and GLCode = 'DEFERREDTAX')
				)
				and isSystemAccount = 1
			SET @accountNameDef = @accountName + ' Deferred'
			IF EXISTS (select GLAccountID FROM dbo.tr_GLAccounts where orgID = @orgID and accountName = @accountNameDef and parentGLAccountID = @parentGLAccountIDDef and status <> 'D')
				SET @accountNameDef = @accountName + ' ' + cast(@GLAccountID as varchar(10)) + ' Deferred'
			EXEC dbo.tr_createGLAccount @orgID=@orgID, @AccountTypeID=5, @accountName=@accountNameDef, @accountCode=null, @GLCode=null, 
				@parentGLAccountID=@parentGLAccountIDDef, @invoiceProfileID=null, @isSystemAccount=1, @isTaxable=0, 
				@invoiceContentID=null, @deferredGLAccountID=null, @GLAccountID=@deferredGLAccountID OUTPUT
		END

		UPDATE dbo.tr_GLAccounts
		SET accountName = @accountName, 
			accountCode = @accountCode, 
			parentGLAccountID = nullIf(@parentGLAccountID,0),
			isTaxable = @isTaxable,
			invoiceProfileID = @invoiceProfileID,
			invoiceContentID = @invoiceContentID,
			deferredGLAccountID = @deferredGLAccountID
		WHERE GLAccountID = @GLAccountID
		and orgID = @orgID

		-- cleanup orphaned deferred accounts		
		IF @bypassDeferredCleanup = 0		
			EXEC dbo.tr_deleteUnlinkedDeferredAccounts @orgID
	END
 

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[tr_updateTaxAuthority]
@orgID int,
@taxAuthorityID int,
@authorityName varchar(200),
@accountCode varchar(200)

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- update authority
	UPDATE dbo.tr_taxAuthorities
	SET authorityName = @authorityName
	WHERE taxAuthorityID = @taxAuthorityID
	AND orgID = @orgID

	-- update GL Account for tax authority
	DECLARE @GLAccountID int, @parentGLAccountID int, @deferredGLAccountID int
	SELECT @GLAccountID = ta.GLAccountID, @parentGLAccountID = gl.parentGLAccountID, @deferredGLAccountID = gl.deferredGLAccountID
		from dbo.tr_taxAuthorities as ta
		inner join dbo.tr_GLAccounts as gl on gl.glAccountID = ta.GLAccountID 
		where ta.taxAuthorityID = @taxAuthorityID

	EXEC dbo.tr_updateGLAccount @orgID=@orgID, @GLAccountID=@GLAccountID, @accountName=@authorityName,
		@accountCode=@accountCode, @parentGLAccountID=@parentGLAccountID, @invoiceProfileID=null, @isTaxable=0, 
		@invoiceContentID=null, @deferredGLAccountID=@deferredGLAccountID, @bypassDeferredCleanup=0


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO


