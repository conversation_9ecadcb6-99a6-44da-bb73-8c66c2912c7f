select d.documentID, ct.description as caseType, t.description as documentType, 
	d.expertName, d.style, d.documentDate, d.Notes, d.state, d.dateEntered, d.jurisdiction, 
	d.originalExt, d.pages, d.dateLastModified, d.disabled, d.uploadStatus, d.reviewFlag, 
	d.reviewComments, d.tiffonline, d.textonline, d.pdfonline,
	'copy d:\pdfs\' + cast(d.documentID as varchar(10)) + '.pdf "f:\temp\files\' + cast(d.documentID as varchar(10)) + '.pdf"' as cmdRunOnTSFILE1
from depoDocuments as d
inner join dbo.depoCaseTypes as ct on d.caseTypeId = ct.caseTypeID
inner join dbo.depogroups as g on d.groupid = g.groupid
inner join dbo.depoDocumentTypes t on d.documentTypeId = t.TypeId
where t.dcourtdoc = 1
and d.disabled = 'N' 
and g.groupid = 1092 -- defense 1085	medneg 1080		nursing 1092
order by 1