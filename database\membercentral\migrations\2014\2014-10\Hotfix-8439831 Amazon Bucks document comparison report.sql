-- Imported the report data provided in the ticket and ran query against it

select r.*, tmp.docCount
from aaReport r
inner join (
	select d.expertname, d.documentdate, count(d.documentid) docCount
	from aaReport r
	inner join depoDocuments d on d.expertname = r.expertname 
		and d.documentdate = r.documentdate
		and d.disabled ='N'
		and d.documentTypeID =1
	group by d.expertName, d.documentDate
	having count(d.documentID) > 1
) tmp on tmp.expertname = r.expertname and tmp.documentdate = r.documentdate
union all
select r.*, tmp.docCount
from aaReport r
inner join (
	select d.expertname, d.documentdate, count(d.documentid) docCount
	from aaReport r
	inner join depoDocuments d on d.expertname = r.expertname 
		and d.documentdate = r.documentdate
		and d.disabled='N'
		and d.documentTypeID =1
	group by d.expertName, d.documentDate
	having count(d.documentID) =1
) tmp on tmp.expertname = r.expertname and tmp.documentdate = r.documentdate