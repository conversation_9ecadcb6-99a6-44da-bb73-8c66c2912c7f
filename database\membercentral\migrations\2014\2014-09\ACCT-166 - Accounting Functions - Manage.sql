use membercentral
GO

-- delete the glAddAccount function
declare @functionID int
select @functionID = functionID from cms_siteResourceFunctions where functionName = 'glAddAccount'
delete from dbo.cms_siteResourceRightsCache where functionID = @functionID
delete from dbo.cache_perms_siteResourceFunctionRightPrints where functionID = @functionID
delete from dbo.cms_siteResourceRoleFunctions where resourceTypeFunctionID in (select resourceTypeFunctionID from dbo.cms_siteResourceTypeFunctions where functionID = @functionID)
delete from dbo.admin_functionsDeterminingNav where resourceTypeFunctionID in (select resourceTypeFunctionID from dbo.cms_siteResourceTypeFunctions where functionID = @functionID)
delete from dbo.cms_siteResourceTypeFunctions where functionID = @functionID
delete from cms_siteResourceFunctions where functionID = @functionID
GO

-- delete the glEditAccount function
declare @functionID int
select @functionID = functionID from cms_siteResourceFunctions where functionName = 'glEditAccount'
delete from dbo.cms_siteResourceRightsCache where functionID = @functionID
delete from dbo.cache_perms_siteResourceFunctionRightPrints where functionID = @functionID
delete from dbo.cms_siteResourceRoleFunctions where resourceTypeFunctionID in (select resourceTypeFunctionID from dbo.cms_siteResourceTypeFunctions where functionID = @functionID)
delete from dbo.admin_functionsDeterminingNav where resourceTypeFunctionID in (select resourceTypeFunctionID from dbo.cms_siteResourceTypeFunctions where functionID = @functionID)
delete from dbo.cms_siteResourceTypeFunctions where functionID = @functionID
delete from cms_siteResourceFunctions where functionID = @functionID
GO

-- add new manage GLAccounts function
declare @resourceTypeID int, @functionID int, @resourceTypeFunctionID int
select @resourceTypeID = dbo.fn_getResourceTypeID('GLAccountsAdmin')

EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='glManage', @displayName='Manage GL Accounts', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('glManage',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID
GO

-- set the admin tool to look for glManage instead of glAddAccount or glEditAccount
declare @resourceTypeID int, @toolTypeID int, @resourceTypeFunctionID int, @navigationID int
select @resourceTypeID = dbo.fn_getResourceTypeID('GLAccountsAdmin')
select @toolTypeID = toolTypeID from dbo.admin_toolTypes where toolType = 'GLAccountsAdmin'
SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('glManage',@resourceTypeID))
select @navigationID = navigationID from dbo.admin_navigation where navName = 'Chart of Accounts'

EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
GO

-- add new manage member pay profile function
declare @resourceTypeID int, @functionID int, @resourceTypeFunctionID int
select @resourceTypeID = dbo.fn_getResourceTypeID('MemberAdmin')

EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='memberPayProfileManage', @displayName='Manage Member Payment Methods', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('memberPayProfileManage',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID
GO


