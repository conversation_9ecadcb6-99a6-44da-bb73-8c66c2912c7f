USE memberCentral
GO

declare @toolTypeID1 int, @resourceTypeID1 int, @toolTypeID2 int, @resourceTypeID2 int

EXEC dbo.createAdminToolType @toolType='MMRMemberNotes', @toolCFC='Reports.members.MemberNotes', @toolDesc='Member Notes', @toolTypeID=@toolTypeID1 OUTPUT, @resourceTypeID=@resourceTypeID1 OUTPUT
EXEC dbo.createAdminToolType @toolType='MMRMemberNotesByType', @toolCFC='Reports.members.MemberNotesByType', @toolDesc='Member Notes By Type', @toolTypeID=@toolTypeID2 OUTPUT, @resourceTypeID=@resourceTypeID2 OUTPUT

declare @membersNavigationID int

select @membersNavigationID = n.navigationID
from dbo.admin_navigation n
inner join admin_navigation n2 on n2.navigationID = n.parentNavigationID
	and n2.navName = 'Reports'
	and n2.navAreaID = 1
where n.navname = 'Members'
and n.navAreaID = 2

declare @parentnavigationID int, @navigationID1 int, @navigationID2 int
EXEC dbo.createAdminNavigation @navName='Member Notes', @navDesc='Member Notes Reports', @parentNavigationID=@membersNavigationID, @navAreaID=3, @cfcMethod=null, @isHeader=0, @showInNav=1, @navigationID=@parentnavigationID OUTPUT
EXEC dbo.createAdminNavigation @navName='Member Notes By Member', @navDesc='List of member notes by member', @parentNavigationID=@parentnavigationID, @navAreaID=4, @cfcMethod='showReport', @isHeader=0, @showInNav=1, @navigationID=@navigationID1 OUTPUT
EXEC dbo.createAdminNavigation @navName='Member Notes By Type', @navDesc='List of member notes by type', @parentNavigationID=@parentnavigationID, @navAreaID=4, @cfcMethod='showReport', @isHeader=0, @showInNav=1, @navigationID=@navigationID2 OUTPUT

declare @resourceTypeFunctionID int
select @resourceTypeFunctionID = f.resourceTypeFunctionID 
from dbo.cms_siteResourceTypeFunctions f
inner join dbo.cms_siteResourceTypes rt on rt.resourceTypeID = f.resourceTypeID	and resourceType = 'MemberHistoryAdmin'
inner join dbo.cms_siteResourceFunctions rf on rf.functionID = f.functionID and rf.functionName = 'ViewNotes'

EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID=@resourceTypeFunctionID, @toolTypeID=@toolTypeID1, @navigationID=@navigationID1
EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID=@resourceTypeFunctionID, @toolTypeID=@toolTypeID2, @navigationID=@navigationID2

declare @copyFromToolTypeID int
select @copyFromToolTypeID = toolTypeID from admin_toolTypes where toolType = 'MMRMemberRelationships'

insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID)
select @toolTypeID1, siteID
from dbo.admin_siteToolRestrictions
where toolTypeID = @copyFromToolTypeID

insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID)
select @toolTypeID2, siteID
from dbo.admin_siteToolRestrictions
where toolTypeID = @copyFromToolTypeID

declare @siteID int
select @siteID=min(siteID) from dbo.sites
while @siteID is not null begin
	exec dbo.createAdminSuite @siteID=@siteID
	select @siteID = min(siteID) from dbo.sites where siteID > @siteID
end
GO
