﻿USE [memberCentral]
GO

set IDENTITY_INSERT ams_virtualGroupRuleTypes ON
insert into dbo.ams_virtualGroupRuleTypes (ruleTypeID, ruleType) VALUES (3, 'EmailBlast')
set IDENTITY_INSERT ams_virtualGroupRuleTypes OFF
GO
set IDENTITY_INSERT ams_virtualGroupConditionTypes ON
insert into dbo.ams_virtualGroupConditionTypes (conditionTypeID, conditionType) VALUES (3, 'EmailBlast')
set IDENTITY_INSERT ams_virtualGroupConditionTypes OFF
GO

CREATE TABLE [dbo].[email_EmailBlasts](
	[blastID] [int] IDENTITY(1,1) NOT NULL,
	[siteID] [int] NOT NULL,
	[memberid] [int] NOT NULL,
	[categoryID] [int] NOT NULL,
	[subCategoryID] [int] NULL,
	[blastName] [varchar](200) NOT NULL,
	[dateCreated] [datetime] NOT NULL CONSTRAINT [DF_email_emailBlasts_dateCreated]  DEFAULT (getdate()),
	[ruleID] [int] NOT NULL,
	[fromName] [varchar](200) NOT NULL,
	[fromEmail] [varchar](200) NOT NULL,
	[replyTo] [varchar](200) NOT NULL,
	[contentID] [int] NOT NULL,
 CONSTRAINT [PK_email_emailBlasts] PRIMARY KEY CLUSTERED 
(
	[blastID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE [dbo].[email_emailBlasts]  WITH CHECK ADD  CONSTRAINT [FK_email_emailBlasts_ams_members] FOREIGN KEY([memberid])
REFERENCES [dbo].[ams_members] ([memberID])
GO
ALTER TABLE [dbo].[email_emailBlasts] CHECK CONSTRAINT [FK_email_emailBlasts_ams_members]
GO
ALTER TABLE [dbo].[email_emailBlasts]  WITH CHECK ADD  CONSTRAINT [FK_email_emailBlasts_ams_virtualGroupRules] FOREIGN KEY([ruleID])
REFERENCES [dbo].[ams_virtualGroupRules] ([ruleID])
GO
ALTER TABLE [dbo].[email_emailBlasts] CHECK CONSTRAINT [FK_email_emailBlasts_ams_virtualGroupRules]
GO
ALTER TABLE [dbo].[email_emailBlasts]  WITH CHECK ADD  CONSTRAINT [FK_email_emailBlasts_sites] FOREIGN KEY([siteID])
REFERENCES [dbo].[sites] ([siteID])
GO
ALTER TABLE [dbo].[email_emailBlasts] CHECK CONSTRAINT [FK_email_emailBlasts_sites]
GO
ALTER TABLE [dbo].[email_emailBlasts]  WITH CHECK ADD  CONSTRAINT [FK_email_emailBlasts_cms_categories] FOREIGN KEY([categoryID])
REFERENCES [dbo].[cms_categories] ([categoryID])
GO
ALTER TABLE [dbo].[email_emailBlasts] CHECK CONSTRAINT [FK_email_emailBlasts_cms_categories]
GO
ALTER TABLE [dbo].[email_emailBlasts]  WITH CHECK ADD  CONSTRAINT [FK_email_emailBlasts_cms_categories1] FOREIGN KEY([subCategoryID])
REFERENCES [dbo].[cms_categories] ([categoryID])
GO
ALTER TABLE [dbo].[email_emailBlasts] CHECK CONSTRAINT [FK_email_emailBlasts_cms_categories1]
GO
ALTER TABLE dbo.email_EmailBlasts ADD CONSTRAINT
	FK_email_EmailBlasts_cms_content FOREIGN KEY
	(
	contentID
	) REFERENCES dbo.cms_content
	(
	contentID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
GO

CREATE PROC [dbo].[email_createEmailBlast]
@siteID int, 
@memberid int, 
@blastName varchar(200),
@categoryID int, 
@subCategoryID int, 
@blastID int OUTPUT

AS

declare @rc int, @ruleID int, @orgID int, @ruleXML xml, @siteName varchar(60), @subject varchar(80), 
	@fromName varchar(150), @fromEmail varchar(200), 
	@appCreatedContentResourceTypeID int, @siteResourceStatusID int, @languageID int,
	@thisContentID int, @thisContentResourceID int

select @blastID = 0
select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')
select @siteResourceStatusID = dbo.fn_getResourceStatusId('Active')
select @languageID = 1
select @orgID = orgID, @siteName = siteName from dbo.sites where siteID = @siteID
select @ruleXML = '<rule><conditionset op="AND" act="include" id="' + cast(NewID() as varchar(36)) + '" /></rule>'
select @fromName = firstname + ' ' + lastName from dbo.ams_members where memberid = @memberID
select TOP 1 @fromEmail = me.email
	FROM dbo.ams_memberEmails AS me
	INNER JOIN dbo.ams_memberEmailTypes as met on met.emailtypeID = me.emailTypeID and met.emailTypeOrder = 1
	WHERE me.memberID = @memberID

IF EXISTS (select blastID from dbo.email_emailBlasts where siteID = @siteID and categoryID = @categoryID and isnull(subCategoryID,0) = isnull(@subCategoryID,0) and blastName = @blastName)
	select @blastName = @blastName + ' ' + cast(cast(rand()*100000000 as int) as varchar(10))

BEGIN TRAN
	EXEC @rc = dbo.ams_createVirtualGroupRule @orgID=@orgID, @ruleTypeID=3, @ruleName='Saved Email Blast', @ruleXML=@ruleXML, @ruleSQL='', @ruleID=@ruleID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 or @ruleID = 0 GOTO on_error
	
	select @subject = 'Message from ' + @siteName
	exec @rc = dbo.cms_createContentObject @siteID, @appCreatedContentResourceTypeID, null, @siteResourceStatusID, 0, 1, @languageID, 1, @subject, '', '', @thisContentID OUTPUT, @thisContentResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 or @thisContentID = 0 GOTO on_error

	INSERT INTO dbo.email_emailBlasts (siteid, categoryID, subCategoryID, memberid, blastName, dateCreated, ruleID, fromName, fromEmail, replyTo, contentID)
	VALUES (@siteID, @categoryID, @subCategoryID, @memberID, left(@blastName,200), getdate(), @ruleID, @fromName, isnull(@fromEmail,''), isnull(@fromEmail,''), @thisContentID)
		IF @@ERROR <> 0 GOTO on_error
		SELECT @blastid = SCOPE_IDENTITY()

	UPDATE dbo.ams_virtualGroupRules
	SET ruleName = 'Saved Email Blast ' + cast(@blastid as varchar(10))
	WHERE ruleID = @ruleID
		IF @@ERROR <> 0 GOTO on_error

IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @blastid = 0
	RETURN -1
GO

CREATE PROC [dbo].[email_copyEmailBlast]
@blastID int,
@memberID int,
@newBlastID int OUTPUT

AS

declare @rc int, @siteID int, @categoryID int, @subCategoryID int, @newRuleID int,
	@newContentID int, @blastName varchar(200), @oldRuleXML xml, @oldRuleSQL varchar(max), 
	@oldFromName varchar(200), @oldFromEmail varchar(200), @oldReplyTo varchar(200), 
	@oldContentTitle varchar(200), @oldRawContent varchar(max)

select @siteID=sr.siteID, @categoryID=sr.categoryID, @subCategoryID=sr.subCategoryID, 
	@blastName='Copy of ' + left(sr.blastName,192), @oldFromName=sr.fromName, 
	@oldFromEmail=sr.fromEmail, @oldReplyTo=sr.replyTo, @oldContentTitle=cl.contentTitle, 
	@oldRuleXML=vgr.ruleXML, @oldRuleSQL=vgr.ruleSQL, @oldRawContent=cv.rawcontent
from dbo.email_emailBlasts as sr
inner join dbo.ams_virtualGroupRules as vgr on vgr.ruleID = sr.ruleID
inner join dbo.cms_content as c on c.contentID = sr.contentID
inner join dbo.cms_contentLanguages as cl on cl.contentID = c.contentID
inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID
where sr.blastID = @blastID
and cv.isActive = 1

BEGIN TRAN
	EXEC @rc = dbo.email_createEmailBlast @siteID=@siteID, @memberid=@memberID, @blastName=@blastName, @categoryID=@categoryID, @subCategoryID=@subCategoryID, @blastID=@NewBlastID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 or @newBlastID = 0 GOTO on_error

	UPDATE dbo.email_emailBlasts
	SET fromName = @oldFromName,
		fromEmail = @oldFromEmail,
		replyTo = @oldReplyTo
	WHERE blastID = @newBlastID
		IF @@ERROR <> 0 GOTO on_error

	SELECT @newRuleID=ruleID, @newContentID=contentID
	FROM dbo.email_emailBlasts
	WHERE blastID = @newBlastID
		IF @@ERROR <> 0 GOTO on_error

	UPDATE dbo.ams_virtualGroupRules
	set ruleXML = @oldRuleXML,
		ruleSQL = @oldRuleSQL
	where ruleID = @newRuleID
		IF @@ERROR <> 0 GOTO on_error

	EXEC @rc = dbo.cms_updateContent @contentID=@newContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@oldContentTitle, @contentDesc='', @rawcontent=@oldRawContent
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @newBlastID = 0
	RETURN -1
GO

CREATE PROC [dbo].[email_deleteEmailBlast]
@blastID int

AS

declare @rc int, @orgID int, @ruleIDList varchar(10)

select @orgID = s.orgID, @ruleIDList = sr.ruleID
from dbo.email_emailBlasts as sr
inner join dbo.sites as s on s.siteID = sr.siteID
where sr.blastID = @blastID

BEGIN TRAN
	DELETE FROM dbo.email_emailBlasts
	WHERE blastID = @blastID
		IF @@ERROR <> 0 GOTO on_error

	EXEC @rc = dbo.ams_deleteVirtualGroupRule @orgID=@orgID, @ruleIDList=@ruleIDList
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

CREATE PROCEDURE [dbo].[cms_createDefaultEmailBlastCategories] 
	@siteID int
AS
BEGIN TRAN

	declare @categoryTreeID int
	declare @controllingSiteResourceID int

	select @controllingSiteResourceID = dbo.fn_getSiteResourceIDForResourceType('EmailBlast', @siteID)

	IF @controllingSiteResourceID is NULL GOTO on_error

	select @categoryTreeID = categoryTreeID
	from cms_categoryTrees
	where controllingSiteResourceID = @controllingSiteResourceID

	-- create category tree
	IF @categoryTreeID is NULL
	BEGIN
		exec dbo.cms_createCategoryTree @siteID=@siteID, @categoryTreeName='Email Blast Types',
			@categoryTreeDesc='Types of Email Blasts and their subcategories',
			@categoryTreeCode='EmailBlastTypes',
			@controllingSiteResourceID=@controllingSiteResourceID,
			@categoryTreeID=@categoryTreeID OUTPUT	
		IF @@ERROR <> 0 GOTO on_error
	END

IF @@TRANCOUNT > 0 COMMIT TRAN	

-- normal exit
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

CREATE PROC [dbo].[ams_getVirtualGroupRuleVerbose]
@ruleID int,
@ruleVerbose varchar(max) OUTPUT

AS

declare @rulexml xml, @sqlXSL xml, @ruleSQLxml xml
select @ruleVerbose = null

-- xml
SELECT @ruleXML = (
	select isnull(
		(select ruleXML.query('/rule'),
			(select distinct [conditiondef].conditionID, [conditiondef].uid, [conditiondef].[verbose]
			from dbo.ams_virtualGroupRules as vgr
			CROSS APPLY ruleXML.nodes('//condition') as C(cond)
			INNER JOIN dbo.ams_virtualGroupConditions as [conditiondef] on [conditiondef].uid = C.cond.value('@id','uniqueidentifier')
			where vgr.ruleID = r.ruleid
			FOR XML AUTO, root('conditiondefs'), TYPE
			)
		from dbo.ams_virtualGroupRules as r
		where ruleID = @ruleID
		for XML PATH(''), root('rulexml'), TYPE)
	,'<rulexml/>'))

-- xsl
select @sqlXSL = cast('
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
	<xsl:strip-space elements="rule conditionset condition" />
	<xsl:output method="xml" encoding="UTF-8" cdata-section-elements="rule" />

	<xsl:template match="rule">
		<rule>
		<xsl:apply-templates/>
		</rule>
	</xsl:template>	
	
	<xsl:template match="conditionset">
		<xsl:if test="@act = ''exclude''">
			<xsl:text>NOT </xsl:text>
		</xsl:if>
		<xsl:if test="count(condition) != 1">
			<xsl:text>( </xsl:text>
		</xsl:if>
		<xsl:apply-templates/>
		<xsl:if test="count(condition) != 1">
			<xsl:text> )</xsl:text>
		</xsl:if>
		<xsl:if test="position() != last()">
			<xsl:value-of select="concat('' '',../@op,'' '')"/>
		</xsl:if>
	</xsl:template>

	<xsl:template match="condition">
		<xsl:param name="uid" select="@id"/>
		<xsl:value-of select="//conditiondef[@uid=$uid]/@verbose"/>
		<xsl:if test="position() != last()">
			<xsl:value-of select="concat('' '',../@op,'' '')"/>
		</xsl:if>
	</xsl:template>
	
	<xsl:template match="/">
		<xsl:apply-templates />
	</xsl:template>
	
</xsl:stylesheet>' as xml)

select @ruleSQLxml = null
select @ruleSQLxml = search.dbo.fn_XMLTransform(@rulexml,@sqlXSL)
select @ruleVerbose = @ruleSQLxml.value('(/rule)[1]','varchar(max)')

RETURN 0
GO

CREATE PROC dbo.email_previewEmailBlastSummary
@blastID int,
@mode tinyint

AS

declare @ruleID int, @xmlMembers xml
declare @tblM TABLE (memberid int PRIMARY KEY, hasEmail bit)

select @ruleID = ruleID from dbo.email_EmailBlasts where blastID = @blastID

EXEC dbo.ams_RunVirtualGroupRule @ruleID=@ruleID, @xmlVirtual=@xmlMembers OUTPUT

insert into @tblM (memberid, hasEmail)
SELECT m2.memberid, case when len(me.email) > 0 then 1 else 0 end
FROM @xmlMembers.nodes('//m') AS M(item)
INNER JOIN dbo.ams_members as m2 WITH(NOLOCK) on m2.memberid = M.item.value('@id', 'int')
LEFT OUTER JOIN dbo.ams_memberEmails as me WITH(NOLOCK) 
	INNER JOIN dbo.ams_memberEmailTypes as met WITH(NOLOCK) on met.emailTypeID = me.emailTypeID and met.emailTypeOrder = 1
	on me.memberid = m2.memberid 

-- summary counts
IF @mode = 0 BEGIN
	select 1 as row, 'Members matching filter criteria' as Members, count(distinct memberid) as MemberCount
	from @tblM
		union all
	select 2 as row, 'Members with no e-mail address' as Members, count(distinct memberid) as MemberCount
	from @tblM
	where hasEmail = 0
		union all
	select 3 as row, 'Total messages to be sent' as Members, count(distinct memberid) as MemberCount
	from @tblM
	where hasEmail = 1
	order by row
END

-- row 1 detail
IF @mode = 1 BEGIN
	select m.lastname + ', ' + m.firstname + ' ' + m.middlename + ' (' + m.membernumber + ')' as memberName, 
		m.company as memberCompany,
		memberEmail = case when len(me.email) > 0 then me.email else '<i>(no e-mail address)</i>' end
	from @tblM as tblM
	inner join dbo.ams_members as m on m.memberid = tblM.memberid
	LEFT OUTER JOIN dbo.ams_memberEmails as me WITH(NOLOCK) 
		INNER JOIN dbo.ams_memberEmailTypes as met WITH(NOLOCK) on met.emailTypeID = me.emailTypeID and met.emailTypeOrder = 1
		on me.memberid = m.memberid 
	order by 1
END

-- row 2 detail
IF @mode = 2 BEGIN
	select m.lastname + ', ' + m.firstname + ' ' + m.middlename + ' (' + m.membernumber + ')' as memberName, 
		m.company as memberCompany,
		memberEmail = '<i>(no e-mail address)</i>'
	from @tblM as tblM
	inner join dbo.ams_members as m on m.memberid = tblM.memberid
	where tblM.hasEmail = 0
	order by 1
END

-- row 3 detail 
IF @mode = 3 BEGIN
	select m.memberid, 
		m.lastname + ', ' + m.firstname + ' ' + m.middlename + ' (' + m.membernumber + ')' as memberName, 
		m.company as memberCompany,
		memberEmail = me.email
	from @tblM as tblM
	inner join dbo.ams_members as m on m.memberid = tblM.memberid
	INNER JOIN dbo.ams_memberEmails as me WITH(NOLOCK) on me.memberid = m.memberid 
	INNER JOIN dbo.ams_memberEmailTypes as met WITH(NOLOCK) on met.emailTypeID = me.emailTypeID and met.emailTypeOrder = 1
	where tblM.hasEmail = 1
	order by 1
END

RETURN 0
GO





DECLARE
	@toolTypeID int,
	@toolResourceTypeID int,
	@roleID int,
	@navigationID int,
	@resourceTypeFunctionID int,
	@orgcode varchar(5),
	@orgID int,
	@sitecode varchar(5),
	@siteID int,
	@TopTabNavigationID int,
	@level2EmailMembersNavigationID int,
	@rc int

BEGIN TRAN
	-- get top tab ID
	select 
		@TopTabNavigationID = navigationID 
	from 
		dbo.admin_navigation 
	where 
		navName = 'Members' 
		and parentNavigationID is null

	-- create level2 items

	EXEC @rc = dbo.createAdminNavigation
		@navName='Email Members',
		@navDesc='Email Members',
		@parentNavigationID=@TopTabNavigationID,
		@navAreaID=2,
		@cfcMethod=null,
		@isHeader=0,
		@showInNav=1,
		@navigationID=@level2EmailMembersNavigationID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	select @toolTypeID = null, @toolResourceTypeID = null

	-- create Admin tool type
	EXEC @rc = dbo.createAdminToolType
		@toolType='EmailBlast',
		@toolCFC='EmailBlast.EmailBlastAdmin',
		@toolDesc='Email Blast Administration',
		@toolTypeID=@toolTypeID OUTPUT,
		@resourceTypeID=@toolResourceTypeID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	DECLARE @viewfunctionID int, @viewResourceTypeFunctionID int, @superAdminRoleID int
	DECLARE @edifunctionID int, @editResourceTypeFunctionID int
	DECLARE @deletefunctionID int, @deleteResourceTypeFunctionID int

	select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')

	-- VIEW permissions
	EXEC @rc = cms_createSiteResourceFunction 
				@resourceTypeID=@toolResourceTypeID, 
				@functionName='View', 
				@displayName='View', 
				@functionID=@viewfunctionID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	select @viewResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@toolResourceTypeID,@viewfunctionID);
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRoleFunction 
				@roleID=@superAdminRoleID, 
				@resourceTypeFunctionID=@viewResourceTypeFunctionID;
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- EDIT permissions
	exec dbo.cms_createSiteResourceFunction 
				@resourceTypeID=@toolResourceTypeID, 
				@functionName='EditCategories', 
				@displayName='Edit the Email Blast Types', 
				@functionID=@edifunctionID OUTPUT
	IF @@ERROR <> 0 GOTO on_error

	select @editResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@toolResourceTypeID,@edifunctionID)

	exec dbo.cms_createSiteResourceRoleFunction 
				@roleID=@superAdminRoleID, 
				@resourceTypeFunctionID=@editResourceTypeFunctionID
	IF @@ERROR <> 0 GOTO on_error

	-- CREATE navigation
	select @navigationID = null
	EXEC @rc = dbo.createAdminNavigation
		@navName='New Email Blast',
		@navDesc='Create Email Blast',
		@parentNavigationID=@level2EmailMembersNavigationID,
		@navAreaID=3,
		@cfcMethod='addEmailBlast',
		@isHeader=0,
		@showInNav=1,
		@navigationID=@navigationID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = dbo.createAdminFunctionsDeterminingNav
		@resourceTypeFunctionID=@viewResourceTypeFunctionID,
		@toolTypeID=@toolTypeID,
		@navigationID=@navigationID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	select @navigationID = null
	EXEC @rc = dbo.createAdminNavigation
		@navName='My Email Blasts',
		@navDesc='List My Email Blasts',
		@parentNavigationID=@level2EmailMembersNavigationID,
		@navAreaID=3,
		@cfcMethod='listMyEmailBlasts',
		@isHeader=0,
		@showInNav=1,
		@navigationID=@navigationID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = dbo.createAdminFunctionsDeterminingNav
		@resourceTypeFunctionID=@viewResourceTypeFunctionID,
		@toolTypeID=@toolTypeID,
		@navigationID=@navigationID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	
	select @navigationID = null
	EXEC @rc = dbo.createAdminNavigation
		@navName='All Email Blasts',
		@navDesc='List All Email Blasts',
		@parentNavigationID=@level2EmailMembersNavigationID,
		@navAreaID=3,
		@cfcMethod='listAllEmailBlasts',
		@isHeader=0,
		@showInNav=1,
		@navigationID=@navigationID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = dbo.createAdminFunctionsDeterminingNav
		@resourceTypeFunctionID=@viewResourceTypeFunctionID,
		@toolTypeID=@toolTypeID,
		@navigationID=@navigationID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
		
	select @navigationID = null
	EXEC @rc = dbo.createAdminNavigation
		@navName='Email Blast Categories',
		@navDesc='Manage Email Blast Categories',
		@parentNavigationID=@level2EmailMembersNavigationID,
		@navAreaID=3,
		@cfcMethod='listCategories',
		@isHeader=0,
		@showInNav=1,
		@navigationID=@navigationID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = dbo.createAdminFunctionsDeterminingNav
		@resourceTypeFunctionID=@viewResourceTypeFunctionID,
		@toolTypeID=@toolTypeID,
		@navigationID=@navigationID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error		

IF @@TRANCOUNT > 0 COMMIT TRAN

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN

GO


declare @parentNavigationID int

select 	@parentNavigationID = navigationID 
from 	dbo.admin_navigation 
where	navName = 'Members' 
	and parentNavigationID is null

update	admin_navigation
set 	orderBy = 2
where	navName = 'Email Members'
	and parentNavigationID = @parentNavigationID

update	admin_navigation
set 	orderBy = 3
where	navName = 'Member Setup'
	and parentNavigationID = @parentNavigationID

update	admin_navigation
set 	orderBy = 4
where	navName = 'Subscriptions'
	and parentNavigationID = @parentNavigationID
GO

-- add tool to sites that currently have subscriptions
declare @siteID int
select @siteID = min(str.siteID)
	from dbo.admin_siteToolRestrictions as str
	inner join dbo.admin_toolTypes as tt on tt.toolTypeId = str.toolTypeID
	where tt.toolType = 'SubscriptionAdmin'
while @siteID is not null BEGIN
	insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
	select tooltypeID, @siteID
	from dbo.admin_toolTypes
	where toolType = 'EmailBlast'
	except
	select tooltypeID, siteID from dbo.admin_siteToolRestrictions

	exec dbo.cms_createDefaultEmailBlastCategories @siteID=@siteID

	exec dbo.createadminsuite @siteid
	
	select @siteID = min(str.siteID)
		from dbo.admin_siteToolRestrictions as str
		inner join dbo.admin_toolTypes as tt on tt.toolTypeId = str.toolTypeID
		where tt.toolType = 'SubscriptionAdmin'
		and str.siteID > @siteID
END
GO

update admin_navigation set
cfcMethod = 'showBlast'
where cfcMethod = 'addEmailBlast'
GO


CREATE PROC [dbo].[email_sendBlast]
@recordedByMemberID int,
@siteID int,
@blastID int

AS

declare @numRecipients int, @rc int, @messageTypeID int, 
	@messageStatusIDInserting int, @messageStatusIDQueued int, @sendingSiteResourceID int, 
	@messageID int, @rawcontent varchar(max), @messageToParse varchar(max),
	@fieldID int, @fieldName varchar(60), @contentID int, @languageID int
declare @metadataFields TABLE (fieldName varchar(60), fieldID int NULL)
declare @ruleID int, @xmlMembers xml
declare @fromName varchar(200), @fromEmail varchar(200), @replyToEmail varchar(200), @senderEmail varchar(200), @subject varchar(400), @contentVersionID int, @messageWrapper varchar(max)

IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
	DROP TABLE #tmpRecipients

select @messageTypeID = messageTypeID from platformMail.dbo.email_messageTypes where messageTypeCode = 'EMAILBLAST'
select @messageStatusIDInserting = statusID from platformMail.dbo.email_statuses where statusCode = 'I'
select @messageStatusIDQueued = statusID from platformMail.dbo.email_statuses where statusCode = 'Q'
select @languageID = dbo.fn_getLanguageID('en')

select @sendingSiteResourceID = st.siteResourceID
from sites s
inner join admin_siteTools st
	on st.siteID = s.siteID
	and st.siteID = @siteID
inner join admin_toolTypes tt
	on tt.toolTypeID = st.toolTypeID
	and tt.toolType = 'EmailBlast'
IF @@ERROR <> 0 goto on_error

select @messageWrapper = '<html><body>@@rawcontent@@</body></html>'
select @senderEmail = '<EMAIL>'


select 
	@ruleID = ruleID,
	@fromName = fromName,
	@fromEmail = fromEmail,
	@replyToEmail = replyTo, 
	@contentID = contentID
from dbo.email_EmailBlasts
where blastID = @blastID
IF @@ERROR <> 0 goto on_error

select @subject=contentTitle, @contentVersionID=contentVersionID, @rawContent = rawContent
from dbo.fn_getContent(@contentID,@languageID) as messageContent
IF @@ERROR <> 0 goto on_error

EXEC dbo.ams_RunVirtualGroupRule @ruleID=@ruleID, @xmlVirtual=@xmlMembers OUTPUT
select m.memberID, m.prefix, m.firstName, m.middlename, m.lastName, m.suffix, me.email
into #tmpRecipients
FROM @xmlMembers.nodes('//m') AS XM(item)
INNER JOIN dbo.ams_members as m WITH(NOLOCK)
	on m.memberid = XM.item.value('@id', 'int')
	and m.status = 'A'
inner join dbo.ams_memberEmails as me WITH(NOLOCK)
	on me.memberid = m.memberid
	and len(me.email) > 0
INNER JOIN dbo.ams_memberEmailTypes as met WITH(NOLOCK)
	on met.emailTypeID = me.emailTypeID
	and met.emailTypeOrder = 1
select @numRecipients = @@rowcount	
IF @@ERROR <> 0 goto on_error


-- add any necessary metadata fields
select @messageToParse = replace(@messageWrapper,'@@rawcontent@@',@rawcontent)
	IF @@ERROR <> 0 goto on_error
insert into @metadataFields (fieldName)
select distinct [Text]
from dbo.fn_RegexMatches(@messageToParse,'(?<=\[\[)([^,\]]+)(?=,?([^\]]+)?\]\])')
	IF @@ERROR <> 0 goto on_error


BEGIN TRAN
	-- add email_message
	EXEC @rc = platformMail.dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID, 
		@sendingSiteResourceID=@sendingSiteResourceID, @recordedByMemberID=@recordedByMemberID, 
		@fromName=@fromName, @fromEmail=@fromEmail, 
		@replyToEmail=@replyToEmail, @senderEmail=@senderEmail, 
		@subject=@subject, @contentVersionID=@contentVersionID, 
		@messageWrapper=@messageWrapper, @messageID=@messageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @messageID = 0 goto on_error

	-- add recipients as I (not ready to be queued yet)
	insert into platformMail.dbo.email_messageRecipientHistory (messageID, memberID, 
		dateLastUpdated, toName, toEmail, emailStatusID, batchID, batchStartDate)
	select @messageID, memberID, getdate(), firstName + ' ' + lastName, email, @messageStatusIDInserting, null, null
	from #tmpRecipients
		IF @@ERROR <> 0 goto on_error

	-- add message metadata fields
	insert into platformMail.dbo.email_metadataFields (fieldName, isMergeField)
	select fieldName, 1
	from @metadataFields
		except
	select fieldName, isMergeField
	from platformMail.dbo.email_metadataFields
		IF @@ERROR <> 0 goto on_error
	update tmp
	set tmp.fieldID = MF.fieldID
	from @metadataFields as tmp
	inner join platformMail.dbo.email_metadataFields as MF on MF.fieldName = tmp.fieldName
	where MF.isMergeField = 1
		IF @@ERROR <> 0 goto on_error

	-- add recipient metadata

	select @fieldID = min(fieldID) from @metadataFields where fieldName in ('firstname','lastname','prefix','fullName','extendedname')
	while @fieldID is not null BEGIN
		select @fieldName = fieldName from @metadataFields where fieldID = @fieldID
			IF @@ERROR <> 0 goto on_error
		insert into platformMail.dbo.email_messageMetadataFields (messageID, fieldID, memberID, fieldValue)
		select @messageID, @fieldID, memberID, 
			fieldValue = case @fieldName
				when 'firstname' then firstname
				when 'lastname' then lastname
				when 'prefix' then prefix 
				when 'fullName' then firstname + ' ' + lastname
				when 'extendedname' then firstname + isnull(' ' + nullif(middlename,''),'') + ' ' + lastname + isnull(' ' + nullif(suffix,''),'')
				end
		from #tmpRecipients
			IF @@ERROR <> 0 goto on_error
		select @fieldID = min(fieldID) from @metadataFields where fieldName in ('firstname','lastname','prefix','fullName','extendedname') and fieldID > @fieldID
	END

	-- mark recipients as queued
	update mrh set
		emailStatusID = @messageStatusIDQueued
	from platformMail.dbo.email_messages m
	inner join dbo.email_messageRecipientHistory mrh
		on m.messageID = mrh.messageID
		and m.messageID = @messageID
	IF @@ERROR <> 0 goto on_error

IF @@TRANCOUNT > 0 COMMIT TRAN

goto on_done

on_error:
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients
	RETURN -1	

on_done:
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients
	RETURN 0
GO

ALTER PROC [dbo].[email_sendBlast]
@recordedByMemberID int,
@siteID int,
@blastID int,
@messageWrapper varchar(max)

AS

declare @numRecipients int, @rc int, @messageTypeID int, 
	@messageStatusIDInserting int, @messageStatusIDQueued int, @sendingSiteResourceID int, 
	@messageID int, @rawcontent varchar(max), @messageToParse varchar(max),
	@fieldID int, @fieldName varchar(60), @contentID int, @languageID int
declare @metadataFields TABLE (fieldName varchar(60), fieldID int NULL)
declare @ruleID int, @xmlMembers xml
declare @fromName varchar(200), @fromEmail varchar(200), @replyToEmail varchar(200), @senderEmail varchar(200), @subject varchar(400), @contentVersionID int

IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
	DROP TABLE #tmpRecipients

select @messageTypeID = messageTypeID from platformMail.dbo.email_messageTypes where messageTypeCode = 'EMAILBLAST'
select @messageStatusIDInserting = statusID from platformMail.dbo.email_statuses where statusCode = 'I'
select @messageStatusIDQueued = statusID from platformMail.dbo.email_statuses where statusCode = 'Q'
select @languageID = dbo.fn_getLanguageID('en')
select @senderEmail = '<EMAIL>'

select @sendingSiteResourceID = st.siteResourceID
from dbo.sites as s
inner join dbo.admin_siteTools as st on st.siteID = s.siteID and st.siteID = @siteID
inner join dbo.admin_toolTypes as tt on tt.toolTypeID = st.toolTypeID and tt.toolType = 'EmailBlast'
	IF @@ERROR <> 0 RETURN -1

select @ruleID = ruleID, @fromName = fromName, @fromEmail = fromEmail, @replyToEmail = replyTo, @contentID = contentID
from dbo.email_EmailBlasts
where blastID = @blastID
	IF @@ERROR <> 0 RETURN -1

select @subject=contentTitle, @contentVersionID=contentVersionID, @rawContent = rawContent
from dbo.fn_getContent(@contentID,@languageID) as messageContent
	IF @@ERROR <> 0 RETURN -1

EXEC dbo.ams_RunVirtualGroupRule @ruleID=@ruleID, @xmlVirtual=@xmlMembers OUTPUT
	IF @@ERROR <> 0 RETURN -1

select m.memberID, m.prefix, m.firstName, m.middlename, m.lastName, m.suffix, me.email
into #tmpRecipients
FROM @xmlMembers.nodes('//m') AS XM(item)
INNER JOIN dbo.ams_members as m WITH(NOLOCK) on m.memberid = XM.item.value('@id', 'int')
	and m.status = 'A'
inner join dbo.ams_memberEmails as me WITH(NOLOCK) on me.memberid = m.memberid
	and len(me.email) > 0
INNER JOIN dbo.ams_memberEmailTypes as met WITH(NOLOCK) on met.emailTypeID = me.emailTypeID
	and met.emailTypeOrder = 1
	IF @@ERROR <> 0 RETURN -1
	select @numRecipients = @@rowcount	

-- add any necessary metadata fields
select @messageToParse = replace(@messageWrapper,'@@rawcontent@@',@rawcontent)
	IF @@ERROR <> 0 RETURN -1
insert into @metadataFields (fieldName)
select distinct [Text]
from dbo.fn_RegexMatches(@messageToParse,'(?<=\[\[)([^,\]]+)(?=,?([^\]]+)?\]\])')
	IF @@ERROR <> 0 RETURN -1

BEGIN TRAN
	-- add email_message
	EXEC @rc = platformMail.dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID, 
		@sendingSiteResourceID=@sendingSiteResourceID, @recordedByMemberID=@recordedByMemberID, 
		@fromName=@fromName, @fromEmail=@fromEmail, 
		@replyToEmail=@replyToEmail, @senderEmail=@senderEmail, 
		@subject=@subject, @contentVersionID=@contentVersionID, 
		@messageWrapper=@messageWrapper, @messageID=@messageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @messageID = 0 goto on_error

	-- add recipients as I (not ready to be queued yet)
	insert into platformMail.dbo.email_messageRecipientHistory (messageID, memberID, 
		dateLastUpdated, toName, toEmail, emailStatusID, batchID, batchStartDate)
	select @messageID, memberID, getdate(), firstName + ' ' + lastName, email, @messageStatusIDInserting, null, null
	from #tmpRecipients
		IF @@ERROR <> 0 goto on_error

	-- add message metadata fields
	insert into platformMail.dbo.email_metadataFields (fieldName, isMergeField)
	select fieldName, 1
	from @metadataFields
		except
	select fieldName, isMergeField
	from platformMail.dbo.email_metadataFields
		IF @@ERROR <> 0 goto on_error
	update tmp
	set tmp.fieldID = MF.fieldID
	from @metadataFields as tmp
	inner join platformMail.dbo.email_metadataFields as MF on MF.fieldName = tmp.fieldName
	where MF.isMergeField = 1
		IF @@ERROR <> 0 goto on_error

	-- add recipient metadata

	select @fieldID = min(fieldID) from @metadataFields where fieldName in ('firstname','lastname','prefix','fullName','extendedname')
	while @fieldID is not null BEGIN
		select @fieldName = fieldName from @metadataFields where fieldID = @fieldID
			IF @@ERROR <> 0 goto on_error
		insert into platformMail.dbo.email_messageMetadataFields (messageID, fieldID, memberID, fieldValue)
		select @messageID, @fieldID, memberID, 
			fieldValue = case @fieldName
				when 'firstname' then firstname
				when 'lastname' then lastname
				when 'prefix' then prefix 
				when 'fullName' then firstname + ' ' + lastname
				when 'extendedname' then firstname + isnull(' ' + nullif(middlename,''),'') + ' ' + lastname + isnull(' ' + nullif(suffix,''),'')
				end
		from #tmpRecipients
			IF @@ERROR <> 0 goto on_error
		select @fieldID = min(fieldID) from @metadataFields where fieldName in ('firstname','lastname','prefix','fullName','extendedname') and fieldID > @fieldID
	END

	-- mark recipients as queued
	update mrh set
		emailStatusID = @messageStatusIDQueued
	from platformMail.dbo.email_messages m
	inner join platformMail.dbo.email_messageRecipientHistory mrh
		on m.messageID = mrh.messageID
		and m.messageID = @messageID
	IF @@ERROR <> 0 goto on_error

IF @@TRANCOUNT > 0 COMMIT TRAN

goto on_done

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN

	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients
	RETURN -1


on_done:
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients
	RETURN 0
GO

CREATE PROC [dbo].[email_previewBlast]
@blastID int,
@memberID int,
@messageWrapper varchar(max)

AS

declare @messageToParse varchar(max)
select @messageToParse = replace(@messageWrapper,'@@rawcontent@@',messageContent.rawContent)
from dbo.email_EmailBlasts as eb
cross apply dbo.fn_getContent(eb.contentID,1) as messageContent
where eb.blastID = @blastID

-- all merge codes in message
select distinct [Text] as fieldName
from dbo.fn_RegexMatches(@messageToParse,'(?<=\[\[)([^,\]]+)(?=,?([^\]]+)?\]\])')

-- return recipient with all possible merge code data
select TOP 1 @messageToParse as messageBody, m.firstName + ' ' + m.lastName as toName, me.email as toEmail,
	m.firstname as firstname, m.lastname as lastname, m.prefix as prefix, 
	m.firstName + ' ' + m.lastName as fullName, 
	m.firstname + isnull(' ' + nullif(m.middlename,''),'') + ' ' + m.lastname + isnull(' ' + nullif(m.suffix,''),'') as extendedName
FROM dbo.ams_members as m WITH(NOLOCK)
inner join dbo.ams_memberEmails as me WITH(NOLOCK) on me.memberid = m.memberid
INNER JOIN dbo.ams_memberEmailTypes as met WITH(NOLOCK) on met.emailTypeID = me.emailTypeID
where m.memberID = @memberID
and met.emailTypeOrder = 1

RETURN 0
GO

CREATE PROC [dbo].[email_sendTestBlast]
@recordedByMemberID int,
@siteID int,
@blastID int,
@memberID int,
@email varchar(200),
@messageWrapper varchar(max)

AS

declare @numRecipients int, @rc int, @messageTypeID int, 
	@messageStatusIDInserting int, @messageStatusIDQueued int, @sendingSiteResourceID int, 
	@messageID int, @rawcontent varchar(max), @messageToParse varchar(max),
	@fieldID int, @fieldName varchar(60), @contentID int, @languageID int
declare @metadataFields TABLE (fieldName varchar(60), fieldID int NULL)
declare @fromName varchar(200), @fromEmail varchar(200), @replyToEmail varchar(200), @senderEmail varchar(200), @subject varchar(400), @contentVersionID int

IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
	DROP TABLE #tmpRecipients

select @messageTypeID = messageTypeID from platformMail.dbo.email_messageTypes where messageTypeCode = 'EMAILBLAST'
select @messageStatusIDInserting = statusID from platformMail.dbo.email_statuses where statusCode = 'I'
select @messageStatusIDQueued = statusID from platformMail.dbo.email_statuses where statusCode = 'Q'
select @languageID = dbo.fn_getLanguageID('en')
select @senderEmail = '<EMAIL>'

select @sendingSiteResourceID = st.siteResourceID
from dbo.sites as s WITH(NOLOCK)
inner join dbo.admin_siteTools as st WITH(NOLOCK) on st.siteID = s.siteID and st.siteID = @siteID
inner join dbo.admin_toolTypes as tt WITH(NOLOCK) on tt.toolTypeID = st.toolTypeID and tt.toolType = 'EmailBlast'
	IF @@ERROR <> 0 RETURN -1

select @fromName = fromName, @fromEmail = fromEmail, @replyToEmail = replyTo, @contentID = contentID
from dbo.email_EmailBlasts WITH(NOLOCK)
where blastID = @blastID
	IF @@ERROR <> 0 RETURN -1

select @subject='**TEST** ' + contentTitle, @contentVersionID=contentVersionID, @rawContent = rawContent
from dbo.fn_getContent(@contentID,@languageID) as messageContent
	IF @@ERROR <> 0 RETURN -1

select m.memberID, m.prefix, m.firstName, m.middlename, m.lastName, m.suffix, @email as email
into #tmpRecipients
FROM dbo.ams_members as m WITH(NOLOCK)
WHERE m.memberID = @memberID
	IF @@ERROR <> 0 RETURN -1
	select @numRecipients = @@rowcount	

-- add any necessary metadata fields
select @messageToParse = replace(@messageWrapper,'@@rawcontent@@',@rawcontent)
	IF @@ERROR <> 0 RETURN -1
insert into @metadataFields (fieldName)
select distinct [Text]
from dbo.fn_RegexMatches(@messageToParse,'(?<=\[\[)([^,\]]+)(?=,?([^\]]+)?\]\])')
	IF @@ERROR <> 0 RETURN -1

BEGIN TRAN
	-- add email_message
	EXEC @rc = platformMail.dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID, 
		@sendingSiteResourceID=@sendingSiteResourceID, @recordedByMemberID=@recordedByMemberID, 
		@fromName=@fromName, @fromEmail=@fromEmail, 
		@replyToEmail=@replyToEmail, @senderEmail=@senderEmail, 
		@subject=@subject, @contentVersionID=@contentVersionID, 
		@messageWrapper=@messageWrapper, @messageID=@messageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @messageID = 0 goto on_error

	-- add recipients as I (not ready to be queued yet)
	insert into platformMail.dbo.email_messageRecipientHistory (messageID, memberID, 
		dateLastUpdated, toName, toEmail, emailStatusID, batchID, batchStartDate)
	select @messageID, memberID, getdate(), firstName + ' ' + lastName, email, @messageStatusIDInserting, null, null
	from #tmpRecipients
		IF @@ERROR <> 0 goto on_error

	-- add message metadata fields
	insert into platformMail.dbo.email_metadataFields (fieldName, isMergeField)
	select fieldName, 1
	from @metadataFields
		except
	select fieldName, isMergeField
	from platformMail.dbo.email_metadataFields
		IF @@ERROR <> 0 goto on_error
	update tmp
	set tmp.fieldID = MF.fieldID
	from @metadataFields as tmp
	inner join platformMail.dbo.email_metadataFields as MF on MF.fieldName = tmp.fieldName
	where MF.isMergeField = 1
		IF @@ERROR <> 0 goto on_error

	-- add recipient metadata
	select @fieldID = min(fieldID) from @metadataFields where fieldName in ('firstname','lastname','prefix','fullName','extendedname')
	while @fieldID is not null BEGIN
		select @fieldName = fieldName from @metadataFields where fieldID = @fieldID
			IF @@ERROR <> 0 goto on_error
		insert into platformMail.dbo.email_messageMetadataFields (messageID, fieldID, memberID, fieldValue)
		select @messageID, @fieldID, memberID, 
			fieldValue = case @fieldName
				when 'firstname' then firstname
				when 'lastname' then lastname
				when 'prefix' then prefix 
				when 'fullName' then firstname + ' ' + lastname
				when 'extendedname' then firstname + isnull(' ' + nullif(middlename,''),'') + ' ' + lastname + isnull(' ' + nullif(suffix,''),'')
				end
		from #tmpRecipients
			IF @@ERROR <> 0 goto on_error
		select @fieldID = min(fieldID) from @metadataFields where fieldName in ('firstname','lastname','prefix','fullName','extendedname') and fieldID > @fieldID
	END

	-- mark recipients as queued
	update mrh set
		emailStatusID = @messageStatusIDQueued
	from platformMail.dbo.email_messages m
	inner join platformMail.dbo.email_messageRecipientHistory mrh
		on m.messageID = mrh.messageID
		and m.messageID = @messageID
	IF @@ERROR <> 0 goto on_error

IF @@TRANCOUNT > 0 COMMIT TRAN

goto on_done

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN

	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients
	RETURN -1


on_done:
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients
	RETURN 0
GO

USE [platformMail]
GO
ALTER PROC [dbo].[email_markRecipientBatch]
@batchSize int,
@restrictToMessageID int,
@workerUUID uniqueIdentifier OUTPUT

AS

declare @batchStartDate datetime, @newStatusID int, @oldStatusID int
declare @tblMessages table(
	messageID int NOT NULL, 
	replyToEmail varchar(200) NOT NULL, 
	subject varchar(400) NOT NULL, 
	siteCode varchar(10) NOT NULL, 
	siteName varchar(60) NOT NULL,
	emailFrom varchar(403) NOT NULL,
	senderEmail varchar(200) NOT NULL, 
	messageContent varchar(max) NOT NULL);
declare @tblRecipients table(
	recipientID int NOT NULL, 
	messageID int NOT NULL, 
	memberID int NOT NULL,
	toName varchar(200) NOT NULL,
	toEmail varchar(200) NOT NULL);
    
set @workerUUID = newID()
set @batchStartDate = getdate()
select @newStatusID = statusID from dbo.email_statuses where statusCode = 'P'
select @oldStatusID = statusID from dbo.email_statuses where statusCode = 'Q'

-- mark recipients
update r 
set batchID = @workerUUID,
	batchStartDate = @batchStartDate,
	emailStatusID = @newStatusID
output	inserted.recipientID, 
		inserted.messageID, 
		inserted.memberID,
		inserted.toName,
		inserted.toEmail
into @tblRecipients
from dbo.email_messageRecipientHistory as r
inner join (
	select top (@batchSize) recipientID
	from dbo.email_messageRecipientHistory
	where emailStatusID = @oldStatusID
		and batchID is null
		and messageID = isnull(@restrictToMessageID,messageID)
	order by dateLastUpdated
) as temp on temp.recipientID = r.recipientID
	IF @@ERROR <> 0 GOTO on_error

-- get messages
insert into @tblMessages (messageID, replyToEmail, subject, siteCode, siteName, emailFrom, senderEmail, messageContent)
select m.messageID, m.replyToEmail, m.subject, s.siteCode, s.siteName,
	m.fromEmail + case when len(m.fromName) > 0 then ' (' + m.fromName + ')' else '' end as emailFrom,
	m.senderEmail, 
	replace(m.messagewrapper,'@@rawcontent@@',cv.rawContent) as messageContent
from (select distinct messageID from @tblRecipients) as tmpM
inner join dbo.email_messages as m on m.messageID = tmpM.messageID
inner join membercentral.dbo.cms_contentVersions as cv on cv.contentVersionID = m.contentVersionID
inner join membercentral.dbo.sites as s on s.siteID = m.siteID

select *
from @tblMessages
order by messageID

-- get message merge codes
select distinct m.messageID, reg.Text as fieldName
from @tblMessages as m
cross apply membercentral.dbo.fn_RegexMatches(m.messageContent,'(?<=\[\[)([^,\]]+)(?=,?([^\]]+)?\]\])') as reg
order by 1, 2

-- get recipients
select recipientID, messageID, memberID, 
	toEmail + case when len(toName) > 0 then ' (' + toName + ')' else '' end as emailTo
from @tblRecipients
	IF @@ERROR <> 0 GOTO on_error

-- get metadata
select r.recipientID, f.fieldID, f.fieldName, mf.messageid, mf.memberid, mf.fieldValue
from dbo.email_metadataFields as f
inner join dbo.email_messageMetadataFields as mf on mf.fieldID = f.fieldID
inner join @tblRecipients as r on r.messageID = mf.messageID and r.memberID = mf.memberID
where f.isMergeField = 1

-- normal exit
RETURN 0

-- error exit
on_error:
	RETURN -1
GO

USE [memberCentral]
GO
ALTER PROC [dbo].[email_sendTestBlast]
@recordedByMemberID int,
@siteID int,
@blastID int,
@memberID int,
@email varchar(200),
@messageWrapper varchar(max),
@messageID int OUTPUT

AS

declare @numRecipients int, @rc int, @messageTypeID int, 
	@messageStatusIDInserting int, @messageStatusIDQueued int, @sendingSiteResourceID int, 
	@rawcontent varchar(max), @messageToParse varchar(max),
	@fieldID int, @fieldName varchar(60), @contentID int, @languageID int
declare @metadataFields TABLE (fieldName varchar(60), fieldID int NULL)
declare @fromName varchar(200), @fromEmail varchar(200), @replyToEmail varchar(200), @senderEmail varchar(200), @subject varchar(400), @contentVersionID int

IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
	DROP TABLE #tmpRecipients

select @messageID = null
select @messageTypeID = messageTypeID from platformMail.dbo.email_messageTypes where messageTypeCode = 'EMAILBLAST'
select @messageStatusIDInserting = statusID from platformMail.dbo.email_statuses where statusCode = 'I'
select @messageStatusIDQueued = statusID from platformMail.dbo.email_statuses where statusCode = 'Q'
select @languageID = dbo.fn_getLanguageID('en')
select @senderEmail = '<EMAIL>'

select @sendingSiteResourceID = st.siteResourceID
from dbo.sites as s WITH(NOLOCK)
inner join dbo.admin_siteTools as st WITH(NOLOCK) on st.siteID = s.siteID and st.siteID = @siteID
inner join dbo.admin_toolTypes as tt WITH(NOLOCK) on tt.toolTypeID = st.toolTypeID and tt.toolType = 'EmailBlast'
	IF @@ERROR <> 0 RETURN -1

select @fromName = fromName, @fromEmail = fromEmail, @replyToEmail = replyTo, @contentID = contentID
from dbo.email_EmailBlasts WITH(NOLOCK)
where blastID = @blastID
	IF @@ERROR <> 0 RETURN -1

select @subject='**TEST** ' + contentTitle, @contentVersionID=contentVersionID, @rawContent = rawContent
from dbo.fn_getContent(@contentID,@languageID) as messageContent
	IF @@ERROR <> 0 RETURN -1

select m.memberID, m.prefix, m.firstName, m.middlename, m.lastName, m.suffix, @email as email
into #tmpRecipients
FROM dbo.ams_members as m WITH(NOLOCK)
WHERE m.memberID = @memberID
	IF @@ERROR <> 0 RETURN -1
	select @numRecipients = @@rowcount	

-- add any necessary metadata fields
select @messageToParse = replace(@messageWrapper,'@@rawcontent@@',@rawcontent)
	IF @@ERROR <> 0 RETURN -1
insert into @metadataFields (fieldName)
select distinct [Text]
from dbo.fn_RegexMatches(@messageToParse,'(?<=\[\[)([^,\]]+)(?=,?([^\]]+)?\]\])')
	IF @@ERROR <> 0 RETURN -1

BEGIN TRAN
	-- add email_message
	EXEC @rc = platformMail.dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID, 
		@sendingSiteResourceID=@sendingSiteResourceID, @recordedByMemberID=@recordedByMemberID, 
		@fromName=@fromName, @fromEmail=@fromEmail, 
		@replyToEmail=@replyToEmail, @senderEmail=@senderEmail, 
		@subject=@subject, @contentVersionID=@contentVersionID, 
		@messageWrapper=@messageWrapper, @messageID=@messageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @messageID = 0 goto on_error

	-- add recipients as I (not ready to be queued yet)
	insert into platformMail.dbo.email_messageRecipientHistory (messageID, memberID, 
		dateLastUpdated, toName, toEmail, emailStatusID, batchID, batchStartDate)
	select @messageID, memberID, getdate(), firstName + ' ' + lastName, email, @messageStatusIDInserting, null, null
	from #tmpRecipients
		IF @@ERROR <> 0 goto on_error

	-- add message metadata fields
	insert into platformMail.dbo.email_metadataFields (fieldName, isMergeField)
	select fieldName, 1
	from @metadataFields
		except
	select fieldName, isMergeField
	from platformMail.dbo.email_metadataFields
		IF @@ERROR <> 0 goto on_error
	update tmp
	set tmp.fieldID = MF.fieldID
	from @metadataFields as tmp
	inner join platformMail.dbo.email_metadataFields as MF on MF.fieldName = tmp.fieldName
	where MF.isMergeField = 1
		IF @@ERROR <> 0 goto on_error

	-- add recipient metadata
	select @fieldID = min(fieldID) from @metadataFields where fieldName in ('firstname','lastname','prefix','fullName','extendedname')
	while @fieldID is not null BEGIN
		select @fieldName = fieldName from @metadataFields where fieldID = @fieldID
			IF @@ERROR <> 0 goto on_error
		insert into platformMail.dbo.email_messageMetadataFields (messageID, fieldID, memberID, fieldValue)
		select @messageID, @fieldID, memberID, 
			fieldValue = case @fieldName
				when 'firstname' then firstname
				when 'lastname' then lastname
				when 'prefix' then prefix 
				when 'fullName' then firstname + ' ' + lastname
				when 'extendedname' then firstname + isnull(' ' + nullif(middlename,''),'') + ' ' + lastname + isnull(' ' + nullif(suffix,''),'')
				end
		from #tmpRecipients
			IF @@ERROR <> 0 goto on_error
		select @fieldID = min(fieldID) from @metadataFields where fieldName in ('firstname','lastname','prefix','fullName','extendedname') and fieldID > @fieldID
	END

	-- mark recipients as queued
	update mrh set
		emailStatusID = @messageStatusIDQueued
	from platformMail.dbo.email_messages m
	inner join platformMail.dbo.email_messageRecipientHistory mrh
		on m.messageID = mrh.messageID
		and m.messageID = @messageID
	IF @@ERROR <> 0 goto on_error


IF @@TRANCOUNT > 0 COMMIT TRAN

goto on_done

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN

	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients
	select @messageID = 0
	RETURN -1


on_done:
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients
	RETURN 0
GO

