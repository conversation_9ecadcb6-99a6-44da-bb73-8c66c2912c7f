declare @done bit, @starttime datetime, @endtime datetime, @totaldone int, @lastbatch int, 
	@reportIntervalInSeconds int, @reportIntervalCount int, @secondsElasped int, @reportMessage varchar(100)
set @done = 0
set @totaldone = 0
set @starttime = getdate()
set @endtime = dateadd(minute,10,@starttime)
set @reportIntervalCount = 0
set @reportIntervalInSeconds = 5

while @done=0 BEGIN
    update ss set
	   useragentID = tmp.useragentID
    from statsSessions ss
    inner join (
	   select top 75000 ss2.sessionID, su.useragentID
	   from dbo.statsSessions ss2 (nolock)
	   inner join dbo.statsUseragents su (nolock)
		  on su.useragent = ss2.useragent
		  and ss2.useragentID is null
    ) tmp
	on tmp.sessionID = ss.sessionID

	set @lastbatch = @@rowcount
    if @lastbatch = 0
	   set @done = 1

    if getdate() > @endtime
	   set @done = 1

	set @totaldone = @totaldone + @lastbatch

	set @secondsElasped = datediff(second,@starttime,getdate())

	if @secondsElasped / @reportIntervalInSeconds > @reportIntervalCount BEGIN
		set @reportIntervalCount = @secondsElasped / @reportIntervalInSeconds
		set @reportMessage = 'Seconds Elasped: ' + cast(@secondsElasped as varchar(20)) + ' Total Done: ' + cast ( @totaldone as varchar(20))
		RAISERROR(@reportMessage,0,1)  WITH NOWAIT
	END
END
print 'Total Done: ' + cast ( @totaldone as varchar(20)) 
GO


-- run these after all above work is done and CF code is released.

ALTER TABLE dbo.statsSessions
	DROP COLUMN screenwidth
GO
ALTER TABLE dbo.statsSessions
	DROP COLUMN screenheight
GO
ALTER TABLE dbo.statsSessions
	DROP COLUMN flashVersion
GO
ALTER TABLE dbo.statsSessions
	DROP COLUMN searchengine
GO
ALTER TABLE dbo.statsSessions
	DROP COLUMN stickycookievalue
GO
ALTER TABLE dbo.statsSessions
	DROP COLUMN oldstickycookievalue
GO
ALTER TABLE dbo.statsSessions
	DROP COLUMN jsessionID
GO


