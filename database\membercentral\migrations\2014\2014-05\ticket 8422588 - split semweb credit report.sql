-- Create Scheduled task
use [memberCentral]
go
declare @taskID int

INSERT INTO [memberCentral].[dbo].[scheduledTasks] ([name],[nextRunDate],[interval],[intervalTypeID],[taskCFC],[timeoutMinutes],[disabled],[siteid])
VALUES ('Bi-Monthly Credit Report','5/16/2014 6:15am',1,2,'model.scheduledTasks.tasks.bimonthlyCreditReport',10,1,1)
	select @taskID = SCOPE_IDENTITY()
INSERT INTO [platformstats].[dbo].[scheduledTaskHistory] ([taskID],[statusTypeID],[dateStarted],[dateEnded],[serverid],[dateLastUpdated])
VALUES (@taskID, 2, '5/1/2014 6:15am', '5/1/2014 6:15am', 1, '5/1/2014 6:15am')

INSERT INTO [memberCentral].[dbo].[scheduledTasks] ([name],[nextRunDate],[interval],[intervalTypeID],[taskCFC],[timeoutMinutes],[disabled],[siteid])
VALUES ('Bi-Monthly Credit Report','6/1/2014 6:15am',1,2,'model.scheduledTasks.tasks.bimonthlyCreditReport',10,1,1)
	select @taskID = SCOPE_IDENTITY()
INSERT INTO [platformstats].[dbo].[scheduledTaskHistory] ([taskID],[statusTypeID],[dateStarted],[dateEnded],[serverid],[dateLastUpdated])
VALUES (@taskID, 2, '5/1/2014 6:15am', '5/1/2014 6:15am', 1, '5/1/2014 6:15am')
GO

