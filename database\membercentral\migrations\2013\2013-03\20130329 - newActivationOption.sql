USE [memberCentral]

insert into dbo.sub_activationOptions (subActivationName, subActivationCode, uid)
values ('First payment for This Subscription', 'T', '7639a12f-8b3f-4a83-bac1-6f19ff41c415')
GO

ALTER PROCEDURE [dbo].[sub_checkActivations] 
@memberid int
AS
BEGIN

	declare @activatedStatusID int, @nonActivatedStatusID int, @enteredByMemberID int

	select @activatedStatusID = statusID
	from dbo.sub_paymentStatuses
	where statusCode = 'P'

	select @nonActivatedStatusID = statusID
	from dbo.sub_paymentStatuses
	where statusCode = 'N'

	select @enteredByMemberID = memberID
	from ams_members
	where memberNumber = 'SYSTEM'
	and orgID = 1	

	declare @tblSubsToCheck TABLE (subscriberID int, rootSubscriberID int, memberID int, siteID int, subActivationCode char(1), statusCode char(1))
	declare @membersToCheck TABLE (id int IDENTITY(1,1), memberID int PRIMARY KEY)
	
	IF @memberID is null
	BEGIN
		insert into @tblSubsToCheck (subscriberID, rootSubscriberID, memberID, siteID, subActivationCode, statusCode)
		select sub.subscriberID, sub.rootSubscriberID, m.activememberID as memberID, t.siteid, ao.subActivationCode, s.statusCode
		from sub_subscribers as sub
		inner join dbo.sub_statuses as s on s.statusID = sub.statusID
		inner join dbo.sub_activationOptions as ao on ao.subActivationID = sub.subActivationID
		inner join dbo.ams_members as m on m.memberid = sub.memberID
		inner join dbo.sub_subscriptions as ss on ss.subscriptionID = sub.subscriptionID
		inner join dbo.sub_Types as t on t.typeID = ss.typeID
		where sub.paymentStatusID = @nonActivatedStatusID
		and s.statusCode in ('A','P','I','E')
		and ao.subActivationCode in ('C','P','I','E','N','T') 
		order by sub.subscriberID
	END
	ELSE BEGIN
		insert into @membersToCheck (memberID)
		select m2.memberID
		from ams_members m
		inner join ams_members m2 on m.activeMemberID = m2.activeMemberID
			and m.memberID = @memberID

		insert into @tblSubsToCheck (subscriberID, rootSubscriberID, memberID, siteID, subActivationCode, statusCode)
		select sub.subscriberID, sub.rootSubscriberID, m.activememberID as memberID, t.siteid, ao.subActivationCode, s.statusCode
		from @membersToCheck mtc
		inner join sub_subscribers as sub on mtc.memberID = sub.memberID
		inner join dbo.sub_statuses as s on s.statusID = sub.statusID
		inner join dbo.sub_activationOptions as ao on ao.subActivationID = sub.subActivationID
		inner join dbo.ams_members as m on m.memberid = sub.memberID
		inner join dbo.sub_subscriptions as ss on ss.subscriptionID = sub.subscriptionID
		inner join dbo.sub_Types as t on t.typeID = ss.typeID
		where sub.paymentStatusID = @nonActivatedStatusID
		and s.statusCode in ('A','P','I','E')
		and ao.subActivationCode in ('C','P','I','E','N','T') 
		order by sub.subscriberID

	END


	declare @tblSubsToMove TABLE (subscriberID int, statusCode char(1), reason varchar(50))

	-- N: No Reliance on payment
	insert into @tblSubsToMove (subscriberID, statusCode, reason)
	select tbl.subscriberID, tbl.statusCode, 'No reliance'
	from @tblSubsToCheck as tbl
	where tbl.subActivationCode = 'N'

	-- P: This sub paid in full
	insert into @tblSubsToMove (subscriberID, statusCode, reason)
	select tbl.subscriberID, tbl.statusCode, 'this paid in full'
	from @tblSubsToCheck as tbl
	inner join dbo.tr_applications as tra on tra.itemID = tbl.subscriberID
		and tra.applicationTypeID = 17
		and tra.itemType = 'Dues'
		and tra.status = 'A'
	inner join dbo.tr_transactionSales as ts on ts.transactionID = tra.transactionID
	where tbl.subActivationCode = 'P'
	and ts.cache_amountAfterAdjustment = ts.cache_activePaymentAllocatedAmount

	-- C: This sub and all children subs paid in full
	insert into @tblSubsToMove (subscriberID, statusCode, reason)
	select tbl.subscriberID, tbl.statusCode, 'this and children paid in full'
	from @tblSubsToCheck as tbl
	cross apply dbo.fn_getRecursiveMemberSubscriptions(tbl.memberid, tbl.siteid, tbl.subscriberID) as rms
	inner join dbo.tr_applications as tra on tra.itemID = rms.subscriberID
		and tra.applicationTypeID = 17
		and tra.itemType = 'Dues'
		and tra.status = 'A'
	inner join dbo.tr_transactionSales as ts on ts.transactionID = tra.transactionID
	where tbl.subActivationCode = 'C'
	group by tbl.subscriberID, tbl.statusCode
	having sum(ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount) = 0

	-- I: First invoice this sub appears on is paid
	insert into @tblSubsToMove (subscriberID, statusCode, reason)
	select tmp.subscriberID, tmp.statusCode, 'First invoice this sub'
	from (
		select tbl.subscriberID, tbl.statusCode, i.assignedToMemberID, min(i.invoiceNumber) as firstInvNumber
		from @tblSubsToCheck as tbl
		inner join dbo.tr_applications as tra on tra.itemID = tbl.subscriberID
			and tra.applicationTypeID = 17
			and tra.itemType = 'Dues'
			and tra.status = 'A'
		inner join dbo.tr_invoiceTransactions as it on it.transactionID = tra.transactionID
		inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
		where tbl.subActivationCode = 'I'
		group by tbl.subscriberID, tbl.statusCode, i.assignedToMemberID
	) as tmp 
	inner join dbo.tr_invoices as i2 on i2.invoiceNumber = tmp.firstInvNumber and i2.assignedToMemberID = tmp.assignedToMemberID
	and i2.statusID = 4

	-- E: First invoice this entire sub appears on is paid
	insert into @tblSubsToMove (subscriberID, statusCode, reason)
	select distinct tmp2.subscriberID, ts.statusCode, 'First invoice this entire sub'
	from (
		select s.subscriberID, i.assignedToMemberID, min(i.invoiceNumber) as firstInvNumber
		from dbo.sub_subscribers s
		inner join dbo.tr_applications as tra on tra.itemID = s.subscriberID
			and tra.applicationTypeID = 17
			and tra.itemType = 'Dues'
			and tra.status = 'A'
		inner join dbo.tr_invoiceTransactions as it on it.transactionID = tra.transactionID
		inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
		where exists (select tstc.rootSubscriberID
						from @tblSubsToCheck tstc 
						where tstc.subActivationCode = 'E'
						and s.rootSubscriberID = tstc.rootSubscriberID)
		group by s.rootSubscriberID, s.subscriberID, i.assignedToMemberID
	) as tmp2
	inner join dbo.tr_invoices as i2 on i2.invoiceNumber = tmp2.firstInvNumber and i2.assignedToMemberID = tmp2.assignedToMemberID
		and i2.statusID = 4
	inner join @tblSubsToCheck ts on ts.subscriberID = tmp2.subscriberID and ts.subActivationCode = 'E'

	-- T: First Payment on This Sub
	insert into @tblSubsToMove (subscriberID, statusCode, reason)
	select tbl.subscriberID, tbl.statusCode, 'First payment this sub'
	from @tblSubsToCheck as tbl
	inner join dbo.tr_applications as tra on tra.itemID = tbl.subscriberID
		and tra.applicationTypeID = 17
		and tra.itemType = 'Dues'
		and tra.status = 'A'
	inner join dbo.tr_transactionSales as ts on ts.transactionID = tra.transactionID
	where tbl.subActivationCode = 'T'
	and ts.cache_activePaymentAllocatedAmount > 0


	-- get the follow parents
	insert into @tblSubsToMove (subscriberID, statusCode, reason)
	select s.subscriberID, st.statusCode, 'follow parents'
	from sub_subscribers s
	inner join dbo.sub_statuses st
		on st.statusID = s.statusID and st.statusCode <> 'D'
		and s.paymentStatusID = @nonActivatedStatusID
	inner join sub_activationOptions a on a.subActivationID = s.subActivationID and a.subActivationCode = 'F'
	left outer join sub_subscribers pS on ps.subscriberID = s.parentSubscriberID and ps.paymentStatusID = @activatedStatusID
	where NOT EXISTS (select subscriberID from @tblSubsToMove where subscriberID = s.subscriberID)
			AND (EXISTS (select subscriberID from @tblSubsToMove where subscriberID = s.parentSubscriberID) OR pS.subscriberID is not null)

	while @@RowCount > 0
	begin

		insert into @tblSubsToMove (subscriberID, statusCode, reason)
		select s.subscriberID, st.statusCode, 'follow parents'
		from sub_subscribers s
		inner join dbo.sub_statuses st
			on st.statusID = s.statusID and st.statusCode <> 'D'
			and s.paymentStatusID = @nonActivatedStatusID
		inner join sub_activationOptions a on a.subActivationID = s.subActivationID and a.subActivationCode = 'F'
		left outer join sub_subscribers pS on ps.subscriberID = s.parentSubscriberID and ps.paymentStatusID = @activatedStatusID
		where NOT EXISTS (select subscriberID from @tblSubsToMove where subscriberID = s.subscriberID)
			AND (EXISTS (select subscriberID from @tblSubsToMove where subscriberID = s.parentSubscriberID) OR pS.subscriberID is not null)
	end


	update subs
	set subs.paymentStatusID = @activatedStatusID
	from dbo.sub_subscribers subs
	inner join @tblSubsToMove ts on ts.subscriberID = subs.subscriberID

	insert into dbo.sub_paymentStatusHistory(subscriberID, paymentStatusID, enteredByMemberID)
	select subscriberID, @activatedStatusID, @enteredByMemberID
	from @tblSubsToMove

	declare @minSubscriberID int, @currLoopMemberID int, @currLoopSubscriptionID int, @currLoopStatusCode char(1), @currLoopGroupID int, @tempCount int
	declare @prevSubscriberID int, @currLoopSiteID int, @insideResult int

	select @minSubscriberID=min(subscriberID)
	from @tblSubsToMove
	where statusCode in ('A','P')
	

	while @minSubscriberID is not null
	begin

		select @currLoopSubscriptionID=s.subscriptionID, @currLoopMemberID=m.activeMemberID, @currLoopStatusCode=t.statusCode, @currLoopSiteID=st.siteID
		from dbo.sub_subscribers s
		inner join @tblSubsToMove t on t.subscriberID = s.subscriberID
		inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID
		inner join dbo.sub_types st on st.typeID = subs.typeID
		inner join dbo.ams_members m on m.memberID = s.memberID
		where s.subscriberID = @minSubScriberID
		

		select @tempCount=count(s.subscriberID)
		from dbo.sub_subscribers s
		inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID AND pst.statusCode = 'N'
		inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @currLoopMemberID
		where s.subscriptionID = @currLoopSubscriptionID
		
		IF @tempCount = 0
		begin
			select @currLoopGroupID=null
			select @currLoopGroupID=groupID
			from ams_groups
			where groupCode like 'subWaitingPay_' + convert(varchar, @currLoopSubscriptionID) + '_tracking'

			IF @currLoopGroupID is not null BEGIN
				exec dbo.ams_deleteMemberGroup @currLoopMemberID, @currLoopGroupID
			END
		end

		IF @currLoopStatusCode = 'A'
		begin

			select @prevSubscriberID=null
			select @prevSubscriberID=s.subscriberID
			from dbo.sub_subscribers s
			inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode in ('A', 'I')
			inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID 
			inner join dbo.sub_types t on t.typeID = subs.typeID and t.siteID = @currLoopSiteID
			inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @currLoopMemberID											
			where s.subscriptionID = @currLoopSubscriptionID
			and s.subscriberID <> @minSubscriberID

			IF @prevSubscriberID is not null
			BEGIN

				EXEC sub_updateSubscriberStatus @prevSubscriberID, 'E', @currLoopSiteID, @enteredByMemberID, @insideResult OUTPUT

			END

			select @currLoopGroupID=null
			select @currLoopGroupID=groupID
			from ams_groups
			where groupCode like 'subActive_' + convert(varchar, @currLoopSubscriptionID) + '_tracking'

			IF @currLoopGroupID is not null BEGIN
				exec dbo.ams_createMemberGroup @currLoopMemberID, @currLoopGroupID
			END
		end
		ELSE IF @currLoopStatusCode = 'P'
		begin
			select @currLoopGroupID=null
			select @currLoopGroupID=groupID
			from ams_groups
			where groupCode like 'subPending_' + convert(varchar, @currLoopSubscriptionID) + '_tracking'

			IF @currLoopGroupID is not null BEGIN
				exec dbo.ams_createMemberGroup @currLoopMemberID, @currLoopGroupID
			END
		end

		select @minSubscriberID=min(subscriberID)
		from @tblSubsToMove
		where statusCode in ('A','P') and subscriberID > @minSubscriberID
	end

END
