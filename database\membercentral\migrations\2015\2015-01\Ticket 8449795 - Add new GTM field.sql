use seminarweb
GO

ALTER TABLE dbo.tblSeminarsSWLive ADD gotoMeetingRegisterID bigint NULL
GO

ALTER PROC [dbo].[swl_verifySWLCode]
@SWLCode char(10)

AS

SELECT TOP 1 e.enrollmentID, p.catalogURL, sswl.gotoMeetingID, sswl.gotoMeetingRegisterID, eswl.goToMeetingUID, eswl.SWLCode
FROM dbo.tblEnrollments AS e 
INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
INNER JOIN dbo.tblSeminars AS s ON e.seminarID = s.seminarID 
INNER JOIN dbo.tblSeminarsSWLive AS sswl ON s.seminarID = sswl.seminarID 
WHERE eswl.SWLCode = @SWLCode
AND LEN(eswl.SWLCode) > 0
AND LEN(eswl.goToMeetingUID) > 0
AND e.isActive = 1
-- Removed for SemWeb 109
--AND e.dateCompleted is null
--AND eswl.attended = 0

GO

ALTER PROC [dbo].[swl_getSeminar]
@seminarID int

AS

select top 1 'SWL' as SWType, s.seminarName, s.seminarDesc, s.isPublished, s.offerCertificate, 
	s.allowRegistrants, s.isPriceBasedOnActual, s.productCode, s.seminarDescText, 
	swl.liveID, swl.seminarID, swl.swlTypeID, swl.gotoMeetingID, swl.gotoMeetingRegisterID, swl.agenda, swl.dateStart, swl.dateEnd, 
	swl.wddxTimeZones, swl.programPassword, swl.lineProvider, swl.phoneAdmin, swl.phoneAttendee, swl.codeAdmin, 
	swl.codeAttendee, swl.codeSpeaker, swl.surveyLink, swl.offerCredit, swl.offerDVD, swl.sendCountToPremiere, 
	swl.premiereUsePIN, swl.premiereConfID, swl.priceSyndication, swl.isOpen, swl.isNATLE, swl.materialsLink,
	s.adID
from dbo.tblSeminars s
inner join dbo.tblSeminarsSWLive swl on swl.seminarID = s.seminarID
where s.seminarID = @seminarID
AND s.isDeleted = 0

GO

ALTER TABLE dbo.tblSeminarsSWLive ALTER COLUMN gotoMeetingRegisterID varchar(30) NULL
GO

ALTER FUNCTION [dbo].[fn_swl_getEnrollmentsForAdmin] (@depoMemberDataID int)
RETURNS TABLE 
AS
RETURN 
(
	SELECT e.enrollmentID, e.userid, s.seminarID, eswl.goToMeetingUID, ps.pin, eswl.swlcode, p.orgcode as SignUpOrgCode, 
		p.catalogURL, pub.orgcode as publisherOrgCode, s.seminarName, sswl.dateStart, sswl.dateEnd, sswl.swltypeID, 
		d.depomemberdataID, d.FirstName, d.LastName, d.Fax, d.Email, e.dateEnrolled, eswl.attended, eswl.attendedPhone, 
		eswl.joinTime, eswl.exitTime, eswl.duration, eswl.durationPhone, eswl.completedPolling, e.dateCompleted, e.passed, 
		(SELECT COUNT(*) FROM dbo.tblLogSWLive AS log1 WHERE enrollmentID = e.enrollmentID AND seminarID = e.seminarID AND contact LIKE '%@%') AS EmailCount,
		(SELECT COUNT(*) FROM dbo.tblLogSWLive AS log2 WHERE enrollmentID = e.enrollmentID AND seminarID = e.seminarID AND contact NOT LIKE '%@%') AS FaxCount,
		(SELECT COUNT(*) FROM dbo.tblEnrollmentsAndCredit WHERE enrollmentID = e.enrollmentID) AS CreditCount,
		pgtm.parkedID, sswl.phoneAttendee, sswl.codeAttendee, s.isDeleted, e.isActive, sswl.premiereUsePIN, sswl.goToMeetingID, 
		sswl.gotoMeetingRegisterID, sswl.isOpen, sswl.materialsLink, s.offerCertificate, 
		CASE 
		WHEN (select count(saf.formID)
				FROM dbo.tblEnrollments AS e2
				INNER JOIN dbo.tblSeminarsAndForms AS saf ON e2.seminarID = saf.seminarID
				LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
					INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
					INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
						and f.isPublished = 1
						and getdate() between f.dateStartPublish and f.dateEndPublish	
					on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e2.enrollmentID
				WHERE e2.enrollmentID = e.enrollmentID
				AND saf.loadPoint = 'evaluation'
				AND exists(
					SELECT sac.seminarCreditID, eac.idNumber, eac.earnedCertificate, caswl.evaluationRequired
					FROM dbo.tblEnrollmentsAndCredit AS eac 
					INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
					INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID 
					INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
					INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID 
					INNER JOIN dbo.tblCreditStatuses AS cstat ON sac.statusID = cstat.statusID
					INNER JOIN dbo.tblCreditAuthoritiesSWLive as caswl on caswl.authorityID = ca.authorityID
					WHERE eac.enrollmentID = e.enrollmentID
					and evaluationRequired = 1)
				AND (safr.responseID is null or r.dateCompleted is null)) > 0 THEN 0
		ELSE 1
		END as allEvaluationCompleted
	FROM dbo.tblEnrollments AS e 
	INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
	INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
	INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
	INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
	INNER JOIN dbo.tblSeminarsSWLive AS sswl ON e.seminarID = sswl.seminarID
	INNER JOIN dbo.tblSeminars as s on s.seminarID = sswl.seminarID
	INNER JOIN dbo.tblParticipants AS pub ON pub.participantID = s.participantID 
	LEFT OUTER JOIN dbo.tblParkedGTMSeats AS pgtm ON e.enrollmentID = pgtm.enrollmentID
	LEFT OUTER JOIN dbo.tblParkedPhoneSeats AS ps ON e.enrollmentID = ps.enrollmentID
	WHERE u.depomemberdataID = @depoMemberDataID
)
GO

ALTER PROC [dbo].[sw_copySeminar]
@seminarID int

AS

DECLARE @newSeminarID int

BEGIN TRANSACTION

-- insert new seminar record
INSERT INTO dbo.tblSeminars (seminarName, seminarDesc, seminarDescText, isPublished, participantID, offerCertificate, isDeleted, isPriceBasedOnActual, allowRegistrants)
SELECT seminarName + ' COPY', seminarDesc, seminarDescText, 0, participantID, offerCertificate, 0, isPriceBasedOnActual, allowRegistrants
FROM dbo.tblSeminars
where seminarID = @seminarID
	IF (@@error <> 0) GOTO on_error
	SELECT @newSeminarID = IDENT_CURRENT('dbo.tblSeminars')

-- SWOD
INSERT INTO dbo.tblSeminarsSWOD (seminarID, dateCatalogStart, dateCatalogEnd, dateOrigPublished, priceSyndication, allowSyndication, layoutID, seminarLength, offerQA, endOfSeminarText, blankOnInactivity)
SELECT @newSeminarID, dateCatalogStart, dateCatalogEnd, dateOrigPublished, priceSyndication, allowSyndication, layoutID, seminarLength, offerQA, endOfSeminarText, blankOnInactivity
FROM dbo.tblSeminarsSWOD
where seminarID = @seminarID
	IF (@@error <> 0) GOTO on_error
	
-- SWL
INSERT INTO dbo.tblSeminarsSWLive (seminarID, swlTypeID, gotomeetingID, gotoMeetingRegisterID, agenda, dateStart, dateEnd, wddxTimeZones, programPassword, lineprovider, phoneAdmin, phoneAttendee, codeAdmin, codeAttendee, surveyLink, offerCredit, offerDVD, sendCountToPremiere, premiereUsePIN, premiereConfID, priceSyndication, isOpen, isNATLE, materialsLink)
SELECT @newSeminarID, swlTypeID, gotomeetingID, gotoMeetingRegisterID, agenda, dateStart, dateEnd, wddxTimeZones, programPassword, lineprovider, phoneAdmin, phoneAttendee, codeAdmin, codeAttendee, surveyLink, offerCredit, offerDVD, sendCountToPremiere, premiereUsePIN, premiereConfID, priceSyndication, isOpen, isNATLE, materialsLink
FROM dbo.tblSeminarsSWLive
where seminarID = @seminarID
	IF (@@error <> 0) GOTO on_error

-- author link
INSERT INTO dbo.tblSeminarsAndAuthors (seminarID, authorID, authorOrder)
SELECT @newSeminarID, authorID, authorOrder
FROM dbo.tblSeminarsAndAuthors
WHERE seminarID = @seminarID
	IF (@@error <> 0) GOTO on_error

-- category link
INSERT INTO dbo.tblSeminarsAndCategories (seminarID, categoryID)
SELECT @newSeminarID, categoryID
FROM dbo.tblSeminarsAndCategories
WHERE seminarID = @seminarID
	IF (@@error <> 0) GOTO on_error

-- groups link
INSERT INTO dbo.tblSeminarsAndGroups (seminarID, groupID, price)
SELECT @newSeminarID, groupID, price
FROM dbo.tblSeminarsAndGroups
WHERE seminarID = @seminarID
	IF (@@error <> 0) GOTO on_error


COMMIT TRANSACTION
RETURN

on_error:
ROLLBACK TRANSACTION
RETURN
GO

ALTER PROC [dbo].[sw_getEnrollmentHistoryForSWAdminByUserID]
@limitToOrgCode varchar(5),
@userID int

AS

-- swl where publisher or signup matches orgcode
SELECT e.enrollmentID, s.seminarid, e.dateenrolled, s.seminarName, sswl.dateStart, sswl.dateEnd, 
	s.isDeleted, e.isActive, e.passed, s.offerCertificate, eswl.attended, eswl.attendedPhone, 
	eswl.gotoMeetingUID, eswl.SWLCode, sswl.premiereUsePIN, eswl.duration, eswl.durationPhone,
	sswl.SWLTypeID, ps.pin, sswl.gotoMeetingID, sswl.gotoMeetingRegisterID, sswl.phoneAttendee, sswl.codeAttendee, 
	(SELECT COUNT(*) FROM dbo.tblLogSWLive AS log1 WHERE enrollmentID = e.enrollmentID AND seminarID = e.seminarID AND contact LIKE '%@%') AS EmailCount,
	(SELECT COUNT(*) FROM dbo.tblLogSWLive AS log2 WHERE enrollmentID = e.enrollmentID AND seminarID = e.seminarID AND contact NOT LIKE '%@%') AS FaxCount,
	(SELECT COUNT(*) FROM dbo.tblEnrollmentsAndCredit WHERE enrollmentID = e.enrollmentID) AS CreditCount,
	p2.orgcode as publisherOrgCode, p.orgcode as signUpOrgCode
FROM dbo.tblEnrollments AS e 
INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
INNER JOIN dbo.tblSeminars AS s ON e.seminarID = s.seminarID 
INNER JOIN dbo.tblParticipants as p2 on p2.participantID = s.participantID
INNER JOIN dbo.tblSeminarsSWLive AS sswl ON s.seminarID = sswl.seminarID
INNER JOIN dbo.tblParticipants as p on p.participantID = e.participantID
LEFT OUTER JOIN dbo.tblParkedPhoneSeats AS ps ON e.enrollmentID = ps.enrollmentID
WHERE e.userID = @userID 
AND (p2.orgcode = @limitToOrgCode OR p.orgcode = @limitToOrgCode)
AND s.isDeleted = 0
AND e.isActive = 1
ORDER BY sswl.dateStart

-- swod where publisher or signup matches orgcode
SELECT e.enrollmentID, s.seminarID, e.dateEnrolled, s.seminarName, 
	s.isDeleted, e.isActive, s.offerCertificate, e.dateCompleted, e.passed,
	p2.orgcode as publisherOrgCode, p.orgcode as signUpOrgCode,
	CASE
	WHEN NOT EXISTS (select prid from dbo.tblSeminarsPreReqs where seminarID = s.seminarID) THEN -1
	ELSE (SELECT isnull((select TOP 1 CASE
			WHEN @userID = 0 THEN 0
			WHEN len(e2.dateCompleted) > 0 and e2.passed = 1 THEN 1
			ELSE 0
			END as preReqFulfilled
		 FROM dbo.tblSeminars as s2
		 INNER JOIN dbo.tblSeminarsPreReqs as pr on pr.preReqSeminarID = s2.seminarID
		 LEFT OUTER JOIN dbo.tblEnrollments as e2 on e2.seminarID = s2.seminarID and e2.userid = @userID and e2.isActive = 1
		 where pr.seminarID = s.seminarid
		 and s2.isDeleted = 0
		 ),0))
	END as preReqFulfilled,
	CASE 
	WHEN e.passed = 1 and len(e.datecompleted) > 0 THEN 1
	WHEN e.passed = 0 and len(e.datecompleted) > 0 THEN 2
	ELSE 3
	END as Progress,
	CASE
	WHEN len(e.dateCompleted) > 0 THEN 1
	ELSE 0
	END as showCertButton
FROM dbo.tblEnrollments AS e 
INNER JOIN dbo.tblSeminars AS s ON e.seminarID = s.seminarID 
INNER JOIN dbo.tblEnrollmentsSWOD AS esod ON e.enrollmentID = esod.enrollmentID
INNER JOIN dbo.tblParticipants as p2 on p2.participantID = s.participantID
INNER JOIN dbo.tblParticipants as p on p.participantID = e.participantID
WHERE e.userID = @userID 
AND (p2.orgcode = @limitToOrgCode OR p.orgcode = @limitToOrgCode)
AND s.isDeleted = 0
AND e.isActive = 1
ORDER BY s.seminarName

-- swtl
SELECT e.enrollmentID, t.titleID, e.dateEnrolled, t.titleName, e.isactive,
	p2.orgcode as publisherOrgCode, p.orgcode as signUpOrgCode
FROM dbo.tblEnrollments AS e 
INNER JOIN dbo.tblEnrollmentsSWTL AS eswtl ON e.enrollmentID = eswtl.enrollmentID 
INNER JOIN dbo.tblTitles AS t ON e.titleID = t.titleID
INNER JOIN dbo.tblParticipants as p2 on p2.participantID = t.participantID
INNER JOIN dbo.tblParticipants as p on p.participantID = e.participantID
WHERE e.userID = @userID 
AND t.isDeleted = 0
AND e.isActive = 1
AND (p2.orgcode = @limitToOrgCode OR p.orgcode = @limitToOrgCode)
ORDER BY t.titleName

-- certificate programs
select cp.programID, cp.programName, sss.semstatusstring, s.seminarID, s.seminarName, s.isPublished, s.offerCertificate,
	e.dateCompleted, e.dateenrolled, e.passed, e.enrollmentID, sswod.seminarID as SWODID, dbo.swcp_getSeminarStatus(cp.programID,@userid,s.seminarid) as semStatus,
	sswl.seminarID as SWLID, sswl.dateStart,
	CASE
	WHEN NOT EXISTS (select prid from dbo.tblSeminarsPreReqs where seminarID = s.seminarID) THEN 1
	ELSE (SELECT isnull((select TOP 1 CASE
			WHEN @userID = 0 THEN 0
			WHEN len(e2.dateCompleted) > 0 and e2.passed = 1 THEN 1
			ELSE 0
			END as preReqFulfilled
		 FROM dbo.tblSeminars as s2
		 INNER JOIN dbo.tblSeminarsPreReqs as pr on pr.preReqSeminarID = s2.seminarID
		 LEFT OUTER JOIN dbo.tblEnrollments as e2 on e2.seminarID = s2.seminarID and e2.userid = @userID and e2.isActive = 1
		 where pr.seminarID = s.seminarID
		 and s2.isDeleted = 0
		 ),0))
	END as preReqFulfilled
FROM dbo.tblCertificatePrograms AS cp 
INNER JOIN dbo.tblParticipants as p on p.participantID = cp.participantID
INNER JOIN dbo.tblCertificateProgramItems AS cpi ON cp.programID = cpi.programID 
INNER JOIN dbo.tblSeminars AS s ON cpi.seminarID = s.seminarID 
INNER JOIN (
	select distinct xcp.programid
	from dbo.tblCertificatePrograms AS xcp 
	INNER JOIN dbo.tblCertificateProgramItems AS xcpi ON xcp.programID = xcpi.programID 
	INNER JOIN dbo.tblEnrollments AS xe ON xcpi.seminarID = xe.seminarID 
	where xe.userID = @userID
	and xe.isactive = 1
) as tmp on tmp.programid = cp.programid
LEFT OUTER JOIN dbo.tblEnrollments as e on e.seminarID = s.seminarID AND e.userID = @userID and e.isactive = 1
LEFT OUTER JOIN dbo.tblSeminarsSWOD as sswod on sswod.seminarID = s.seminarID
LEFT OUTER JOIN dbo.tblSeminarsSWLive as sswl on sswl.seminarID = s.seminarID
cross apply dbo.swcp_getSeminarStatusString(cp.programID,@userid) as sss
WHERE p.orgcode = @limitToOrgCode
ORDER BY cp.programName, cpi.itemOrder
GO

ALTER PROCEDURE [dbo].[swl_getSeminarForCatalog]
@seminarID int,
@billingstate varchar(5),
@catalogOrgCode varchar(5),
@depomemberdataid int,
@depomemberSiteGroups varchar(max)

AS

-- use 1-1-2005 as dates so we can still support viewing details of past programs.
-- qry1: get seminar
select top 1 s.seminarID, s.seminarName, s.seminarDesc, s.seminarDescText, s.isPublished, s.offerCertificate, s.allowregistrants,
	CASE 
	WHEN @depomemberdataID > 0 AND dbo.fn_getEnrollmentIDFromDepoMemberDataID(s.seminarID,@depoMemberdataid) > 0 THEN 1
	ELSE 0
	END as isRegistered,
	dbo.fn_getEnrollmentIDFromDepoMemberDataID(s.seminarID,@depoMemberdataid) as enrollmentID,
	swl.gotomeetingID, swl.gotoMeetingRegisterID, swl.agenda, swl.dateStart, swl.dateEnd, swl.wddxTimeZones, swl.programPassword, 
	swl.phoneAdmin, swl.phoneAttendee, swl.codeAdmin, swl.codeAttendee, swl.surveyLink, swl.offerCredit, 
	swl.offerDVD, swl.sendCountToPremiere, swl.premiereConfID, swl.premiereUsePIN, swl.lineprovider, p.orgcode as publisherOrgCode, 
	swl.swltypeid, tla.description,
	(select count(*) from dbo.tblSeminarsAndGroups where seminarID = @seminarID) as numPriceOptions,
	s.isPriceBasedOnActual
from dbo.tblSeminars s
inner join dbo.tblSeminarsSWLive swl on swl.seminarID = s.seminarID
INNER JOIN dbo.tblParticipants as p on p.participantID = s.participantID
INNER JOIN trialsmith.dbo.depoTLA as tla on tla.State = p.OrgCode 
INNER JOIN dbo.swl_SeminarsInMyCatalog(@catalogOrgCode,'1-1-2005',DATEADD(yy, 2, getDate())) smc on smc.seminarID = swl.seminarID
where s.seminarID = @seminarID
AND s.isDeleted = 0

-- qry2: get prices for detail page
EXEC swl_getPricesForCatalog @seminarID, @catalogOrgCode, @billingstate

-- qry3: get prices for buy now pages
EXEC swl_getPricesForBuyNow @seminarID, @catalogOrgCode, @billingstate, @depomemberdataid, @depomemberSiteGroups
GO

ALTER PROC [dbo].[swl_verifyEnrollmentByDepoID]
@seminarID int,
@depoMemberDataID int

AS

SELECT TOP 1 pswl.catalogURL, sswl.gotoMeetingID, sswl.gotoMeetingRegisterID, eswl.goToMeetingUID
FROM dbo.tblEnrollments AS e 
INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
INNER JOIN dbo.tblSeminars AS s ON e.seminarID = s.seminarID 
INNER JOIN dbo.tblSeminarsSWLive AS sswl ON s.seminarID = sswl.seminarID 
INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
INNER JOIN dbo.tblParticipantsSWLive AS pswl ON p.participantID = pswl.participantID
INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID
WHERE s.seminarID = @seminarID 
AND u.depoMemberDataID = @depoMemberDataID
AND e.isActive = 1
GO

ALTER PROC [dbo].[swl_verifyEnrollmentByEnrollmentID]
@seminarID int,
@enrollmentID int

AS

SELECT TOP 1 pswl.catalogURL, sswl.gotoMeetingID, sswl.gotoMeetingRegisterID, eswl.goToMeetingUID
FROM dbo.tblEnrollments AS e 
INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
INNER JOIN dbo.tblSeminars AS s ON e.seminarID = s.seminarID 
INNER JOIN dbo.tblSeminarsSWLive AS sswl ON s.seminarID = sswl.seminarID 
INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
INNER JOIN dbo.tblParticipantsSWLive AS pswl ON p.participantID = pswl.participantID
WHERE s.seminarID = @seminarID 
AND e.enrollmentID = @enrollmentID
AND e.isActive = 1
GO

ALTER PROC [dbo].[swl_verifyEnrollmentByUID]
@seminarID int,
@goToMeetingUID varchar(20)

AS

SELECT TOP 1 pswl.catalogURL, sswl.gotoMeetingID, sswl.gotoMeetingRegisterID, eswl.goToMeetingUID
FROM dbo.tblEnrollments AS e 
INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
INNER JOIN dbo.tblSeminars AS s ON e.seminarID = s.seminarID 
INNER JOIN dbo.tblSeminarsSWLive AS sswl ON s.seminarID = sswl.seminarID 
INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
INNER JOIN dbo.tblParticipantsSWLive AS pswl ON p.participantID = pswl.participantID
WHERE s.seminarID = @seminarID 
AND eswl.goToMeetingUID = @goToMeetingUID
AND e.isActive = 1
AND LEN(eswl.goToMeetingUID) > 0
GO

ALTER PROC [dbo].[swl_verifyGoToMeetingUID]
@goToMeetingUID varchar(20)

AS

SELECT TOP 1 p.catalogURL, sswl.gotoMeetingID, sswl.gotoMeetingRegisterID, eswl.goToMeetingUID
FROM dbo.tblEnrollments AS e 
INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
INNER JOIN dbo.tblSeminars AS s ON e.seminarID = s.seminarID 
INNER JOIN dbo.tblSeminarsSWLive AS sswl ON s.seminarID = sswl.seminarID 
WHERE eswl.goToMeetingUID = @goToMeetingUID
AND LEN(eswl.goToMeetingUID) > 0
AND e.dateCompleted is null
AND eswl.attended = 0
AND e.isActive = 1
GO

