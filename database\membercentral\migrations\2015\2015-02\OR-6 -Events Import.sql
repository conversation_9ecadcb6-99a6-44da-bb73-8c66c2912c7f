use datatransfer
GO


/* START SET VARIABLES HERE */
declare 
	@sitecode varchar(10), @tmpFile varchar(400), @timeZoneID int, 
	@replyToEmail varchar(100), @deleteMissingCalendars bit, @deleteMissingMembers bit,	
	@deleteMissingCategories bit, @stopBeforeImport bit

set @sitecode = 'OR'
set @tmpFile = 'c:\temp\Event Import.txt'
set @timeZoneID = 3
set @replyToEmail = '<EMAIL> '
set @deleteMissingCalendars = 0
set @deleteMissingMembers = 0
set @deleteMissingCategories = 0
set @stopBeforeImport = 1
/* END SET VARIABLES HERE */

SET NOCOUNT ON

declare 
		@orgID int, @siteID int, @errSection varchar(30), 
		@qry varchar(2000), @minID int, @MCEventID int, 
		@MCCalendarID int, @MCsystemMemberID int, @MCAllDayEvent bit, 
		@eventCode varchar(15), @starttime datetime, @endtime datetime, 
		@eventContentID int, @eventName varchar(400), @expirationContentID int, 
		@maxEventID int, @MCRegistrationID int, @customQID int,
		@registrantCap int, @eventLocation varchar (max), @creditsGeneral decimal(6,2),	
		@creditsPractical decimal(6,2),	@creditsEthics decimal(6,2), @creditsAbuse decimal(6,2),
		@creditsAccess decimal(6,2), @internalNotes varchar (max), @programNumber varchar (50),
		@eventCreditType varchar(100), @eventCreditType2 varchar(100), @eventCreditType3 varchar(100),
		@eventCreditType4 varchar(100), @eventCreditType5 varchar(100), @ASID int,
		@locationContentID int, @rawcontent varchar(max)


select @orgID = orgID, @siteID = siteID from membercentral.dbo.sites where sitecode = @siteCode
select @MCsystemMemberID = memberID from membercentral.dbo.ams_members where memberNumber = 'SYSTEM' and orgID = 1
set @eventCreditType = 'General'
select @eventCreditType2 = 'Practice Skills'
select @eventCreditType3 = 'Professional Responsibility - Ethics'
select @eventCreditType4 = 'Professional Responsibility - Child Abuse Reporting'
select @eventCreditType5 = 'Professional Responsibility - Access to Justice'

/* ************** */
/* initial checks */
/* ************** */
IF @siteID is null BEGIN
	set @errSection = 'initialchecks'
	GOTO on_error	
END

/* ********************* */
/* import data from file */
/* ********************* */
IF OBJECT_ID('tempdb..##tmpEvImport') IS NOT NULL 
	DROP TABLE ##tmpEvImport
IF OBJECT_ID('tempdb..#tmpEvImport') IS NOT NULL 
	DROP TABLE #tmpEvImport
IF OBJECT_ID('tempdb..#tmpEvImportEV') IS NOT NULL 
	DROP TABLE #tmpEvImportEV

CREATE TABLE ##tmpEvImport (
	[calendarName] [varchar](200) NULL,
	[eventCode] [varchar](15) NULL,
	[eventCategory] [varchar](50) NULL,
	[eventName] [varchar](400) NULL,
	[eventStartDate] [datetime] NULL,
	[eventEndDate] [datetime] NULL,
	[programNumber] [varchar](50) NULL,
	[eventLocation] [varchar](max) NULL,
	[registrantCap] [int] NULL,
	[creditsGeneral] [decimal](6,2),	
	[creditsPractical] [decimal](6,2),	
	[creditsEthics] [decimal](6,2), 
	[creditsAbuse] [decimal](6,2),
	[creditsAccess] [decimal](6,2), 
	[internalNotes] [varchar] (max)
)

BEGIN TRY
	SELECT @qry = 'BULK INSERT ##tmpEvImport FROM ''' + @tmpFile + ''' WITH (FIELDTERMINATOR = ''' + char(9) + ''', FIRSTROW = 2);'
	EXEC(@qry)
END TRY
BEGIN CATCH
	EXEC membercentral.dbo.up_errorhandler
	set @errSection = 'importfromfile'
	GOTO on_error	
END CATCH

-- GOTO on_good

select *,
	cast(null as int) as MCCalendarID,
	cast(null as int) as MCMemberID,
	cast(null as int) as MCEventID,
	cast(null as int) as MCCategoryID,
	cast(null as int) as MCRegistrationID,
	cast(1 as bit) as MCAllDayEvent,
	cast(null as int) as custom1QID,
	cast(null as int) as MCCreditOfferingID,
	cast(null as int) as MCRegistrantID,
	ROW_NUMBER() OVER(order by calendarName, eventCode) as autoID
into #tmpEvImport 
from ##tmpEvImport

IF OBJECT_ID('tempdb..##tmpEvImport') IS NOT NULL 
	DROP TABLE ##tmpEvImport


/* ******************* */
/* remove extra quotes */
/* ******************* */
update #tmpEvImport
set calendarName = replace(calendarName,char(34),''),
	eventName = replace(eventName,char(34),'')


/* **************************** */
/* try to match by calendarname */
/* **************************** */
BEGIN TRY
	update tmp
	set tmp.MCCalendarID = c.calendarID
	from #tmpEvImport as tmp
	inner join membercentral.dbo.cms_applicationInstances as ai on ai.applicationInstanceName = tmp.calendarName
		and ai.siteID = @siteID
	inner join membercentral.dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID
		and sr.siteResourceStatusID = 1
	inner join membercentral.dbo.ev_calendars as c on c.applicationInstanceID = ai.applicationInstanceID

	IF @deleteMissingCalendars = 1
		delete from #tmpEvImport where MCCalendarID is null

	IF EXISTS (select * from #tmpEvImport where MCCalendarID is null) BEGIN
		select distinct calendarName as CalendarNotFound
		from #tmpEvImport
		where MCCalendarID is null
		order by 1

		RAISERROR('Error raised in TRY block.', 16, 1);
	END
END TRY
BEGIN CATCH
	set @errSection = 'calendarnotfound'
	GOTO on_error
END CATCH



/* ************************* */
/* try to match by eventcode */
/* ************************* */
BEGIN TRY
	update tmp
	set tmp.MCEventID = e.eventID
	from #tmpEvImport as tmp
	inner join membercentral.dbo.ev_events as e on e.reportCode = tmp.eventCode
		and e.status = 'A'
		and e.siteID = @siteID
	inner join membercentral.dbo.ev_calendarEvents as ce on ce.sourceEventID = e.eventID
		and ce.calendarID = tmp.MCCalendarID
		and ce.calendarID = ce.sourceCalendarID

	update tmp
	set tmp.MCRegistrationID = r.registrationID
	from #tmpEvImport as tmp
	inner join membercentral.dbo.ev_registration as r on r.eventID = tmp.MCEventID and r.status = 'A'
	where tmp.MCEventID is not null
END TRY
BEGIN CATCH
	set @errSection = 'matchingeventcode'
	GOTO on_error
END CATCH


/* ************************ */
/* try to match by category */
/* ************************ */
BEGIN TRY
	update tmp
	set tmp.MCCategoryID = cat.categoryID
	from #tmpEvImport as tmp
	inner join membercentral.dbo.cms_applicationInstances as ai on ai.applicationInstanceName = tmp.calendarName
		and ai.siteID = @siteID
	inner join membercentral.dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID
		and sr.siteResourceStatusID = 1
	inner join membercentral.dbo.ev_calendars as c on c.applicationInstanceID = ai.applicationInstanceID
	inner join membercentral.dbo.ev_categories as cat on cat.calendarID = c.calendarID
		and cat.category = tmp.eventCategory

	IF @deleteMissingCategories = 1
		delete from #tmpEvImport where MCCategoryID is null

	IF EXISTS (select * from #tmpEvImport where MCCategoryID is null) BEGIN
		select distinct eventCategory as EventCategoryNotFound
		from #tmpEvImport
		where MCCategoryID is null
		order by 1

		RAISERROR('Error raised in TRY block.', 16, 1);
	END
END TRY
BEGIN CATCH
	set @errSection = 'categorynotfound'
	GOTO on_error
END CATCH

/* *********** */
/* check dates */
/* *********** */
IF EXISTS (select * from #tmpEvImport where eventStartDate is null) BEGIN
	select 'Start Date is empty' as errReason, * 
	from #tmpEvImport
	where eventStartDate is null

	set @errSection = 'startempty'
	GOTO on_error
END
IF EXISTS (select * from #tmpEvImport where eventEndDate is null) BEGIN
	select 'End Date is empty' as errReason, * 
	from #tmpEvImport
	where eventEndDate is null

	set @errSection = 'endempty'
	GOTO on_error
END
IF EXISTS (select * from #tmpEvImport where eventStartDate > eventEndDate) BEGIN
	select 'Start Date is after End Date' as errReason, * 
	from #tmpEvImport
	where eventStartDate > eventEndDate

	set @errSection = 'startafterend'
	GOTO on_error
END


/* ***************** */
/* set all day event */
/* ***************** */
update #tmpEvImport
set MCAllDayEvent = 1
where eventStartDate = eventEndDate

/* NEED CHECKS HERE FOR CREDIT AUTHORITIES, TYPES, VALUES */



/* ******************* */
/* Get Distinct Events */
/* ******************* */
select distinct calendarName, eventCode, eventName, eventCategory, eventStartDate, eventEndDate, MCCalendarID, MCEventID, MCCategoryID, 
	MCAllDayEvent, MCRegistrationID, custom1QID
into #tmpEvImportEV
from #tmpEvImport

ALTER TABLE #tmpEvImportEV ADD autoID int IDENTITY(1,1);

IF EXISTS (select eventCode from #tmpEvImportEV group by eventCode having count(*) > 1) BEGIN
	select 'Duplicate Event Codes' as errReason, * 
	from #tmpEvImportEV
	where eventCode in (select eventCode from #tmpEvImportEV group by eventCode having count(*) > 1)

	set @errSection = 'dupeeventcodes'
	GOTO on_error
END

/* ********** */
/* IF TESTING */
/* ********** */
IF @stopBeforeImport = 1 BEGIN
	set @errSection = 'testing'
	GOTO on_error
END


/* ****************** */
print 'Update Existing Events'
/* ****************** */
EXEC membercentral.dbo.cache_perms_setStatus @orgID, 'disabled'
SELECT @minID = min(autoID) FROM #tmpEvImportEV where MCEventID is not null
WHILE @minID is not null BEGIN
	select @MCEventID = null, @MCCalendarID = null, @MCAllDayEvent = null, @eventCode = null, @starttime = null, 
		@endtime = null, @eventName = null, @eventContentID = null, @eventLocation = null,
		@internalNotes = null

	select 
		@eventLocation = te.eventLocation,
		@internalNotes = te.internalNotes,
		@MCEventID = ev.MCEventID
	from #tmpEvImportEV ev
		inner join #tmpEvImport te on
			te.eventCode = ev.eventCode
	where ev.autoID = @minID

	select 
		@eventLocation,
		@internalNotes 

	select @locationContentID = locationContentID FROM membercentral.dbo.ev_events WHERE eventID = @MCEventID
	select @rawcontent = rawContent from membercentral.dbo.fn_getContent(@locationContentID,1) 
	if len(@rawcontent) = 0 begin
		EXEC membercentral.dbo.cms_updateContent @contentID=@locationContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle='', @contentDesc='', @rawcontent=@eventLocation
	end
	update membercentral.dbo.ev_events set internalNotes = @internalNotes where  eventID = @MCEventID and len(internalNotes) = 0

	SELECT @minID = min(autoID) FROM #tmpEvImportEV where MCEventID is not null and autoID > @minID
END


/* ****************** */
print 'Add Missing Events'
/* ****************** */
select @maxEventID = max(eventID) from membercentral.dbo.ev_events
SELECT @minID = min(autoID) FROM #tmpEvImportEV where MCEventID is null
WHILE @minID is not null BEGIN
	select @MCEventID = null, @MCCalendarID = null, @MCAllDayEvent = null, @eventCode = null, @starttime = null, 
		@endtime = null, @eventName = null, @eventContentID = null, @eventLocation = null,
		@internalNotes = null

	select 
		@MCCalendarID = ev.MCCalendarID, @MCAllDayEvent = ev.MCAllDayEvent, @eventCode = ev.eventCode, 
		@starttime = ev.eventStartDate, @endtime = ev.eventEndDate, @eventName = ev.eventName, @eventLocation = te.eventLocation,
		@internalNotes = te.internalNotes
	from #tmpEvImportEV ev
		inner join #tmpEvImport te on
			te.eventCode = ev.eventCode
	where ev.autoID = @minID

	EXEC membercentral.dbo.ev_createEvent @siteID=@siteID, @calendarid=@MCCalendarID, @eventTypeID=1, @enteredByMemberID=@MCsystemMemberID, 
			@lockTimeZoneID=null, @isAllDayEvent=@MCAllDayEvent, @altRegistrationURL=null, @status='A', @reportCode=@eventCode, 
			@hiddenFromCalendar=0, @emailContactContent = 0, @emailLocationContent = 0, @emailCancelContent = 0, @emailTravelContent = 0, 
			@eventID=@MCEventID OUTPUT
	UPDATE #tmpEvImportEV SET MCEventID = @MCEventID WHERE autoID = @minID
	UPDATE #tmpEvImport SET MCEventID = @MCEventID WHERE eventCode = @eventCode

	EXEC membercentral.dbo.ev_createTime @eventID=@MCEventID, @timeZoneID=@timeZoneID, @startTime=@starttime, @endTime=@endtime

	select @eventContentID = eventContentID, @locationContentID = locationContentID FROM membercentral.dbo.ev_events WHERE eventID = @MCEventID
	EXEC membercentral.dbo.cms_updateContent @contentID=@eventContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@eventName, @contentDesc='', @rawcontent=@eventName
	EXEC membercentral.dbo.cms_updateContent @contentID=@locationContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle='', @contentDesc='', @rawcontent=@eventLocation

	update membercentral.dbo.ev_events set internalNotes = @internalNotes where  eventID = @MCEventID

	SELECT @minID = min(autoID) FROM #tmpEvImportEV where MCEventID is null and autoID > @minID
END

/* **************************** */
print 'Add Missing Event Categories'
/* **************************** */
INSERT INTO membercentral.dbo.ev_eventCategories (eventID, categoryID)
SELECT distinct MCEventID, MCCategoryID
FROM #tmpEvImportEV
where MCEventID > @maxEventID
except (
	SELECT eventID, categoryID
	FROM membercentral.dbo.ev_eventCategories
)

/* ************************ */
print 'Add Missing Registration'
/* ************************ */
set @minID = null
SELECT @minID = min(autoID) FROM #tmpEvImportEV where MCRegistrationID is null
WHILE @minID is not null BEGIN
	select @MCEventID = null, @starttime = null, @endtime = null, @MCRegistrationID = null, @expirationContentID = null, @registrantCap = null

	select @MCEventID = ev.MCEventID, @starttime = ev.eventStartDate, @endtime = ev.eventEndDate, @registrantCap = te.registrantCap
	from #tmpEvImportEV ev
		inner join #tmpEvImport te on
			te.eventCode = ev.eventCode
		where ev.autoID = @minID

	EXEC membercentral.dbo.ev_createRegistration @eventID=@MCEventID, @registrationTypeID=1, @startDate=@starttime, @endDate=@endtime, 
			@registrantCap=@registrantCap, @replyToEmail=@replyToEmail, @notifyEmail='', @isPriceBasedOnActual=1, @bulkCountByRate=0, 
			@registrationID=@MCRegistrationID OUTPUT
	UPDATE #tmpEvImportEV SET MCregistrationID = @MCRegistrationID WHERE autoID = @minID
	UPDATE #tmpEvImport SET MCregistrationID = @MCRegistrationID WHERE MCEventID = @MCEventID

	select @expirationContentID = expirationContentID FROM membercentral.dbo.ev_registration WHERE registrationID = @MCRegistrationID
	EXEC membercentral.dbo.cms_updateContent @contentID=@expirationContentID, @languageID=1, @isSSL=0, @isHTML=1, 
			@contentTitle='Expiration Message', @contentDesc='', @rawcontent='Registration for this event has closed.'

	SELECT @minID = min(autoID) FROM #tmpEvImportEV where MCRegistrationID is null and autoID > @minID
END


/* ****************** */
print 'Add Missing Credit'
/* ****************** */
select @ASID = cas.ASID
	from membercentral.dbo.crd_authoritySponsors as cas
	inner join membercentral.dbo.crd_authorities as ca on ca.authorityID = cas.authorityID and ca.authorityName = 'Oregon State Bar Association'
	inner join membercentral.dbo.crd_sponsors as cs on cs.sponsorID = cas.sponsorID and cs.orgID = @orgID
	
INSERT INTO membercentral.dbo.crd_offerings (ASID, statusID, ApprovalNum, offeredStartDate, offeredEndDate, completeByDate, isCreditRequired, 
	isIDRequired, isCreditDefaulted, eventID)
select distinct @ASID, 4, isNull(programNumber,''), tmp.eventStartDate, tmp.eventEndDate, tmp.eventEndDate, 0, 0, 0, tmp.MCEventID
from #tmpEvImport as tmp
where 
	 (tmp.creditsGeneral > 0 OR tmp.creditsPractical > 0 OR tmp.creditsEthics > 0 OR tmp.creditsAbuse > 0 OR tmp.creditsAccess > 0)
and not exists (
	select offeringID
	from membercentral.dbo.crd_offerings
	where eventID = tmp.MCEventID
	and ASID = @ASID
	and statusID = 4
)

update tmp
set tmp.MCCreditOfferingID = co.offeringID
from #tmpEvImport as tmp
inner join membercentral.dbo.crd_offerings as co on co.ASID = @ASID and co.eventID = tmp.MCEventID and co.statusID = 4

INSERT INTO membercentral.dbo.crd_offeringTypes (offeringID, ASTID, creditValue)
select distinct tmp.MCCreditOfferingID, ast.astID, tmp.creditsGeneral
FROM #tmpEvImport as tmp
INNER JOIN membercentral.dbo.crd_authorityTypes as cat on cat.typeName = @eventCreditType
INNER JOIN membercentral.dbo.crd_authoritySponsorTypes as ast on ast.typeID = cat.typeID and ast.ASID = @ASID
WHERE tmp.MCCreditOfferingID is not null
  and tmp.creditsGeneral > 0
EXCEPT (
	select offeringID, ASTID, creditValue
	from membercentral.dbo.crd_offeringTypes
)
	union all
select distinct tmp.MCCreditOfferingID, ast.astID, tmp.creditsPractical
FROM #tmpEvImport as tmp
INNER JOIN membercentral.dbo.crd_authorityTypes as cat on cat.typeName = @eventCreditType2
INNER JOIN membercentral.dbo.crd_authoritySponsorTypes as ast on ast.typeID = cat.typeID and ast.ASID = @ASID
WHERE tmp.MCCreditOfferingID is not null
and tmp.creditsPractical > 0
EXCEPT (
	select offeringID, ASTID, creditValue
	from membercentral.dbo.crd_offeringTypes
)
	union all
select distinct tmp.MCCreditOfferingID, ast.astID, tmp.creditsEthics
FROM #tmpEvImport as tmp
INNER JOIN membercentral.dbo.crd_authorityTypes as cat on cat.typeName = @eventCreditType3
INNER JOIN membercentral.dbo.crd_authoritySponsorTypes as ast on ast.typeID = cat.typeID and ast.ASID = @ASID
WHERE tmp.MCCreditOfferingID is not null
 and tmp.creditsEthics > 0
EXCEPT (
	select offeringID, ASTID, creditValue
	from membercentral.dbo.crd_offeringTypes
)
	union all
select distinct tmp.MCCreditOfferingID, ast.astID, tmp.creditsAbuse
FROM #tmpEvImport as tmp
INNER JOIN membercentral.dbo.crd_authorityTypes as cat on cat.typeName = @eventCreditType4
INNER JOIN membercentral.dbo.crd_authoritySponsorTypes as ast on ast.typeID = cat.typeID and ast.ASID = @ASID
WHERE tmp.MCCreditOfferingID is not null
 and tmp.creditsAbuse > 0
EXCEPT (
	select offeringID, ASTID, creditValue
	from membercentral.dbo.crd_offeringTypes
)
	union all
select distinct tmp.MCCreditOfferingID, ast.astID, tmp.creditsAccess
FROM #tmpEvImport as tmp
INNER JOIN membercentral.dbo.crd_authorityTypes as cat on cat.typeName = @eventCreditType5
INNER JOIN membercentral.dbo.crd_authoritySponsorTypes as ast on ast.typeID = cat.typeID and ast.ASID = @ASID
WHERE tmp.MCCreditOfferingID is not null
 and tmp.creditsAccess > 0
EXCEPT (
	select offeringID, ASTID, creditValue
	from membercentral.dbo.crd_offeringTypes
)


/* ************ */
/* Final Checks */
/* ************ */
IF EXISTS (select * from #tmpEvImport where MCRegistrationID is null) BEGIN
	select 'Registration is missing' as errReason, * 
	from #tmpEvImport
	where MCRegistrationID is null

	set @errSection = 'registrationmissing'
	GOTO on_error
END


/* ************************** */


GOTO on_good

on_error:
	print 'import stopped at ' + @errSection
	goto on_done

on_good:
	print 'import success.'

on_done:
	print 'end of import.'

	IF OBJECT_ID('tempdb..##tmpEvImport') IS NOT NULL 
		DROP TABLE ##tmpEvImport
	IF OBJECT_ID('tempdb..#tmpEvImport') IS NOT NULL 
		DROP TABLE #tmpEvImport
	IF OBJECT_ID('tempdb..#tmpEvImportEV') IS NOT NULL 
		DROP TABLE #tmpEvImportEV
	IF OBJECT_ID('tempdb..#tmpNewReg') IS NOT NULL 
		DROP TABLE #tmpNewReg

  EXEC membercentral.dbo.cache_perms_setStatus @orgID, 'enabled'

SET NOCOUNT OFF

