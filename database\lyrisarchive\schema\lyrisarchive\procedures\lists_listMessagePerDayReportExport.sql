ALTER PROC dbo.lists_listMessagePerDayReportExport
@orgcode varchar(10),
@startDate datetime,
@endDate datetime,
@filename varchar(800)

AS

IF OBJECT_ID('tempdb..##tmpListPerDayExport') IS NOT NULL 
	DROP TABLE ##tmpListPerDayExport

declare @listcounts TABLE (list varchar(100) PRIMARY KEY, membercount int)
insert into @listcounts (list, membercount)
select m.list_, count(*)
from lyris.trialslyris1.dbo.members_ as m
inner join lyris.trialslyris1.dbo.lists_format lf on m.list_ = lf.name COLLATE Latin1_General_CI_AI
	and lf.orgcode = @orgcode
where m.membertype_ in ('normal','held')
and m.list_ not in ('seminarweblive')
group by m.list_

select CONVERT(VARCHAR(10),creatStamp_,101) as date, 
	dayofweek = case datepart(dw,creatStamp_)
		when 1 then 'Sunday'
		when 2 then 'Monday'
		when 3 then 'Tuesday'
		when 4 then 'Wednesday'
		when 5 then 'Thursday'
		when 6 then 'Friday'
		when 7 then 'Saturday'
	end
	, sum(lc.membercount) totalMessagesSent, count(*) as uniqueMessageCount
	, ROW_NUMBER() OVER (ORDER BY CONVERT(VARCHAR(10),creatStamp_,101), datepart(dw,creatStamp_)) as row
into ##tmpListPerDayExport
from messagelists ml WITH(nolock)
inner join messages_ m WITH(nolock) on m.listID = ml.listID and m.isVisible=1
	and creatStamp_ between @startDate and @endDate
inner join @listcounts lc on lc.list = ml.list
group by CONVERT(VARCHAR(10),creatStamp_,101), datepart(dw,creatStamp_)

EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql='select [date], [dayofweek], uniqueMessageCount from ##tmpListPerDayExport order by row'

IF OBJECT_ID('tempdb..##tmpListPerDayExport') IS NOT NULL 
	DROP TABLE ##tmpListPerDayExport
GO
