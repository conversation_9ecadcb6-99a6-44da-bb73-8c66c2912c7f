use platformQueue
GO
truncate table dbo.tblQueueItems_addSubscribersDetail
GO
delete from dbo.tblQueueItems_addSubscribers
GO
ALTER TABLE dbo.tblQueueItems_addSubscribersDetail ADD recogStartDate datetime NOT NULL, recogEndDate datetime NOT NULL;
GO
ALTER PROC [dbo].[job_addSubscribers_clearDone]
AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @statusDone int, @queueTypeID int
	select @statusDone = qs.queueStatusID, @queueTypeID = qt.queueTypeID
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'addSubscribers'
		and qs.queueStatus = 'done'

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueStatuses qs on qs.queueStatusID = qi.queueStatusID
			and qs.queueTypeID = @queueTypeID
		inner join platformQueue.dbo.tblQueueItems_addSubscribers as qid ON qid.itemUID = qi.itemUID
		where qi.queueStatusID = @statusDone
			except
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueStatuses qs on qs.queueStatusID = qi.queueStatusID
			and qs.queueTypeID = @queueTypeID
		inner join platformQueue.dbo.tblQueueItems_addSubscribers as qid ON qid.itemUID = qi.itemUID
		where qi.queueStatusID <> @statusDone
	)
	DELETE from platformQueue.dbo.tblQueueItems
	where itemUID in (
		select qi.itemUID
		FROM platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueStatuses qs on qs.queueStatusID = qi.queueStatusID
			and qs.queueTypeID = @queueTypeID
		INNER JOIN platformQueue.dbo.tblQueueItems_addSubscribers as qid ON qid.itemUID = qi.itemUID
		INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qid.itemGroupUID
		WHERE qi.queueStatusID = @statusDone
	)

	DELETE from platformQueue.dbo.tblQueueItems_addSubscribersDetail
	where itemUID not in (select itemUID from platformQueue.dbo.tblQueueItems)
	
	DELETE from platformQueue.dbo.tblQueueItems_addSubscribers
	where itemUID not in (select itemUID from platformQueue.dbo.tblQueueItems)


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[job_addSubscribers_grabForProcessing]
@serverID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	DECLARE @batchSize int
	set @batchSize = 500

	declare @statusReady int, @statusGrabbed int
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'addSubscribers'
		and qs.queueStatus = 'readyToProcess'
	select @statusGrabbed = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'addSubscribers'
		and qs.queueStatus = 'grabbedForProcessing'

	IF OBJECT_ID('tempdb..#tmpTblQueueItems_addSubscribers') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_addSubscribers
	CREATE TABLE #tmpTblQueueItems_addSubscribers (itemUID uniqueidentifier, jobUID uniqueidentifier, 
		recordedByMemberID int, orgID int, siteID int, memberID int, treeCode varchar(50), skipEmailTemplateNotifications bit)

	declare @jobUID uniqueidentifier
	set @jobUID = NEWID()

	-- dequeue in order of dateAdded. get @batchsize trees
	update qi WITH (UPDLOCK, READPAST)
	set qi.queueStatusID = @statusGrabbed,
		qi.dateUpdated = getdate(),
		qi.jobUID = @jobUID,
		qi.jobDateStarted = getdate(),
		qi.jobServerID = @serverID
		OUTPUT inserted.itemUID, inserted.jobUID, qid.recordedByMemberID, qid.orgID, qid.siteID, qid.memberID, qid.treeCode, qid.skipEmailTemplateNotifications
		INTO #tmpTblQueueItems_addSubscribers
	from platformQueue.dbo.tblQueueItems as qi
	inner join platformQueue.dbo.tblQueueItems_addSubscribers as qid ON qid.itemUID = qi.itemUID
	inner join (
		select top(@BatchSize) qi2.itemUID 
		from platformQueue.dbo.tblQueueItems as qi2
		inner join platformQueue.dbo.tblQueueItems_addSubscribers as qid2 ON qid2.itemUID = qi2.itemUID
		where qi2.queueStatusID = @statusReady
		order by qi2.dateAdded, qi2.itemUID
		) as batch on batch.itemUID = qi.itemUID
	where qi.queueStatusID = @statusReady

	-- final data
	select qid.itemUID, qid.jobUID, qid.recordedByMemberID, qid.orgID, qid.siteID, qid.memberID, qid.skipEmailTemplateNotifications,
		qidd.rowID, qidd.subscriptionID, qidd.parentSubscriptionID, qidd.RFID, qidd.subStartDate, qidd.subscriberID, 
		qidd.subEndDate, qidd.graceEndDate, qidd.recogStartDate, qidd.recogEndDate, qidd.status, qidd.lastPrice, qidd.storeModifiedRate, 
		GLAccountID = case when subs.allowRateGLAccountOverride = 1 and r.GLAccountID is not null then r.GLAccountID else subs.GLAccountID end,
		ActivationOptionCode = case when qidd.status in ('R','O') then ao.subActivationCode else 'N' end
	from #tmpTblQueueItems_addSubscribers as qid
	inner join platformQueue.dbo.tblQueueItems_addSubscribersDetail as qidd on qidd.itemUID = qid.itemUID
	inner join membercentral.dbo.sub_subscriptions as subs on subs.subscriptionID = qidd.subscriptionID
	inner join membercentral.dbo.sub_activationOptions as ao on ao.subActivationID = subs.subAlternateActivationID
	inner join membercentral.dbo.sub_rates as r on r.uid = qidd.rateUID
	order by qid.itemUID, qidd.rowID

	IF OBJECT_ID('tempdb..#tmpTblQueueItems_addSubscribers') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_addSubscribers

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH
GO




use membercentral
GO

CREATE PROC [dbo].[sub_importSubscribers_prepTable]
@useAccrualAccounting bit,
@importResult xml OUTPUT

AS

SET NOCOUNT ON

set @importResult = null


-- *********************************
-- ensure all required columns exist 
-- *********************************
BEGIN TRY
	-- this will get the columns that are required
	IF OBJECT_ID('tempdb..#tblSubOrgCols') IS NOT NULL 
		DROP TABLE #tblSubOrgCols
	CREATE TABLE #tblSubOrgCols (COLUMN_NAME sysname)

	insert into #tblSubOrgCols
	select 'rowID' union all
	select 'MemberNumber' union all
	select 'SubscriptionType' union all
	select 'SubscriptionName' union all
	select 'RateName' union all
	select 'LastPrice' union all
	select 'Frequency' union all
	select 'StoreModifiedRate' union all
	select 'StartDate' union all
	select 'EndDate' union all
	select 'GraceEndDate' union all
	select 'Status' union all
	select 'ParentSubscriptionType' union all
	select 'ParentSubscriptionName' union all
	select 'TreeCode'

	if @useAccrualAccounting = 1 begin
		insert into #tblSubOrgCols
		select 'RecogStartDate' union all
		select 'RecogEndDate'
	end

	-- this will get the columns that are actually in the import
	IF OBJECT_ID('tempdb..#tblSubImportCols') IS NOT NULL 
		DROP TABLE #tblSubImportCols
	CREATE TABLE #tblSubImportCols (ORDINAL_POSITION int, COLUMN_NAME sysname)

	insert into #tblSubImportCols
	select column_id, [name] 
	from tempdb.sys.columns 
	where object_id = object_id('tempdb..#mc_SubImport');

	INSERT INTO #tblSubErrors (msg)
	select 'The required column ' + org.column_name + ' is missing from your data.'
	from #tblSubOrgCols as org
	left outer join #tblSubImportCols as imp on imp.column_name = org.column_name
	where imp.ORDINAL_POSITION is null
		IF @@ROWCOUNT > 0 GOTO on_done

	delete from #tblSubImportCols 
	where column_name in (select COLUMN_NAME from #tblSubOrgCols)

	IF OBJECT_ID('tempdb..#tblSubOrgCols') IS NOT NULL 
		DROP TABLE #tblSubOrgCols
END TRY
BEGIN CATCH
	INSERT INTO #tblSubErrors (msg)
	VALUES ('Unable to validate file contains all required columns.')

	GOTO on_done
END CATCH


-- **********
-- prep table 
-- **********
BEGIN TRY
	-- add holding columns
	ALTER TABLE #mc_SubImport ADD MCMemberID int null, subTypeUID uniqueidentifier null, subUID uniqueidentifier null, 
		subID int null, parentSubTypeUID uniqueidentifier null, parentSubUID uniqueidentifier null, parentSubID int null,  
		rateUID uniqueidentifier null, RFID int null, rootSubID int null, subscriberID int null;

	IF @useAccrualAccounting = 0 BEGIN
		ALTER TABLE #mc_SubImport ADD RecogStartDate datetime null, RecogEndDate datetime null;
	END

	-- ensure rowID is an int
	ALTER TABLE #mc_SubImport ALTER COLUMN RowID int not null;

END TRY
BEGIN CATCH
	INSERT INTO #tblSubErrors (msg)
	VALUES ('Unable to prepare import table by adding missing columns.')

	INSERT INTO #tblSubErrors (msg)
	VALUES (left(error_message(),300))

	GOTO on_done
END CATCH


-- *************
-- extra columns 
-- *************
BEGIN TRY
	-- extra columns should stop import to prevent accidental misnamed columns
	IF EXISTS (select column_name from #tblSubImportCols) BEGIN
		INSERT INTO #tblSubErrors (msg)
		SELECT TOP 100 PERCENT 'The imported file contains the extra column ' + cast(column_name as varchar(300)) + '. Remove this column from your file.' 
		FROM #tblSubImportCols  
		ORDER BY column_name
	END
END TRY
BEGIN CATCH
	INSERT INTO #tblSubErrors (msg)
	VALUES ('Unable to validate import file for extra columns.')

	INSERT INTO #tblSubErrors (msg)
	VALUES (left(error_message(),300))

	GOTO on_done
END CATCH


-- ************************
-- generate result xml file 
-- ************************
on_done:
	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblSubErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE)
	
	IF OBJECT_ID('tempdb..#tblSubOrgCols') IS NOT NULL 
		DROP TABLE #tblSubOrgCols
	IF OBJECT_ID('tempdb..#tblSubImportCols') IS NOT NULL 
		DROP TABLE #tblSubImportCols

RETURN 0
GO

CREATE PROC [dbo].[sub_importSubscribers_validate]
@siteid int, 
@useAccrualAccounting bit, 
@importResult xml OUTPUT

AS

SET NOCOUNT ON

declare @orgID int
--declare @dynSQL nvarchar(max)
select @orgID=orgID from dbo.sites where siteID = @siteID
set @importResult = null


-- ***********
-- clean table 
-- ***********
BEGIN TRY
	-- delete empty rows
	delete from #mc_SubImport where MemberNumber is null and SubscriptionName is null;
END TRY
BEGIN CATCH
	INSERT INTO #tblSubErrors (msg)
	VALUES ('Unable to clean import table.')

	INSERT INTO #tblSubErrors (msg)
	VALUES (left(error_message(),300))

	GOTO on_done
END CATCH


-- ****************
-- required columns 
-- ****************
BEGIN TRY
	-- put in unique treecode if none defined
	BEGIN TRY
		ALTER TABLE #mc_SubImport ALTER COLUMN TreeCode varchar(50) null;
		update #mc_SubImport set TreeCode = cast(NEWID() as varchar(50)) where nullIf(TreeCode,'') is null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblSubErrors (msg)
		VALUES ('The column TreeCode has empty entries and could not be populated automatically.')
	END CATCH

	-- no blank membernumbers
	update #mc_SubImport set membernumber = '' where membernumber is null;

	INSERT INTO #tblSubErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing MemberNumber.'
	FROM #mc_SubImport
	WHERE membernumber = ''
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- match on member
	update tmp 
	set tmp.MCMemberID = m.memberid
	from #mc_SubImport as tmp 
	inner join dbo.ams_members as m on m.memberNumber = tmp.memberNumber
		and m.orgID = @orgID
		and m.memberID = m.activeMemberID
		and m.status <> 'D'

	-- check for missing members
	BEGIN TRY
		ALTER TABLE #mc_SubImport ALTER COLUMN MCMemberID int not null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblSubErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' does not match an existing member.'
		FROM #mc_SubImport
		WHERE MCMemberID IS NULL
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done
	END CATCH

	-- no blank subscriptionTypes
	update #mc_SubImport set subscriptionType = '' where subscriptionType is null;

	INSERT INTO #tblSubErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing SubscriptionType.'
	FROM #mc_SubImport
	WHERE subscriptionType = ''
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- match on subscriptionTypes
	update tmp 
	set tmp.subtypeUID = t.uid
	from #mc_SubImport as tmp 
	inner join dbo.sub_types as t on t.typeName = tmp.SubscriptionType 
		and t.siteID = @siteID
		and t.status = 'A' 

	-- check for missing subscriptionTypes
	BEGIN TRY
		ALTER TABLE #mc_SubImport ALTER COLUMN subtypeUID uniqueidentifier not null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblSubErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' does not match an existing subscription type.'
		FROM #mc_SubImport
		WHERE subtypeUID IS NULL
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done
	END CATCH

	-- no blank subscriptionName
	update #mc_SubImport set subscriptionName = '' where subscriptionName is null;

	INSERT INTO #tblSubErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing SubscriptionName.'
	FROM #mc_SubImport
	WHERE subscriptionName = ''
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- match on subscriptionName
	update tmp 
	set tmp.subUID = subs.uid,
		tmp.subID = subs.subscriptionID
	from #mc_SubImport as tmp 
	inner join dbo.sub_subscriptions as subs on subs.subscriptionName = tmp.SubscriptionName 
		and subs.status = 'A'
	inner join dbo.sub_types as t on subs.typeID = t.typeID 
		and t.uid = tmp.subtypeUID
	
	-- check for missing subscriptionName
	BEGIN TRY
		ALTER TABLE #mc_SubImport ALTER COLUMN subUID uniqueidentifier not null;
		ALTER TABLE #mc_SubImport ALTER COLUMN subID int not null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblSubErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' does not match an existing subscription name.'
		FROM #mc_SubImport
		WHERE subUID IS NULL
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done
	END CATCH

	-- no blank Frequency
	update #mc_SubImport set Frequency = '' where Frequency is null;

	INSERT INTO #tblSubErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing Frequency.'
	FROM #mc_SubImport
	WHERE Frequency = ''
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- check for frequencies
	INSERT INTO #tblSubErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(tmp.rowID as varchar(10)) + ' does not match an existing Frequency.'
	FROM #mc_SubImport as tmp
	left outer join dbo.sub_frequencies as f on f.frequencyShortName = tmp.frequency 
		and f.siteID = @siteID
		and f.status = 'A'
	WHERE f.frequencyID is null
	ORDER BY tmp.rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- no blank RateName
	update #mc_SubImport set RateName = '' where RateName is null;

	INSERT INTO #tblSubErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing RateName.'
	FROM #mc_SubImport
	WHERE RateName = ''
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- match on RateName
	update tmp 
	set tmp.rateUID = r.uid,
		tmp.RFID = rf.rfid
	from #mc_SubImport as tmp 
	inner join dbo.sub_subscriptions as subs on subs.uid = tmp.subUID
	inner join dbo.sub_rates as r on r.scheduleID = subs.scheduleID and r.rateName = tmp.rateName and r.status = 'A'
	inner join dbo.sub_rateFrequencies as rf on rf.rateID = r.rateID
	inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID and f.frequencyShortName = tmp.frequency and f.status = 'A'

	-- check for missing RateName
	BEGIN TRY
		ALTER TABLE #mc_SubImport ALTER COLUMN rateUID uniqueidentifier not null;
		ALTER TABLE #mc_SubImport ALTER COLUMN RFID int not null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblSubErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' does not match an existing Rate Name for that frequency and subscription.'
		FROM #mc_SubImport
		WHERE rateUID IS NULL
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done
	END CATCH

	-- clean LastPrice
	update #mc_SubImport set LastPrice = dbo.fn_regexReplace(LastPrice,'[^0-9\.\-\(\)]','') where LastPrice is not null;

	-- ensure LastPrice is money (allow nulls for this check)
	BEGIN TRY
		ALTER TABLE #mc_SubImport ALTER COLUMN [LastPrice] money null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblSubErrors (msg)
		VALUES ('The column LastPrice contains invalid amounts.')
			GOTO on_done
	END CATCH

	-- check for null or negative LastPrice
	BEGIN TRY
		ALTER TABLE #mc_SubImport ALTER COLUMN [LastPrice] money not null;
		ALTER TABLE #mc_SubImport ADD CONSTRAINT lastPriceamtCheck CHECK (LastPrice >= 0);
	END TRY
	BEGIN CATCH
		INSERT INTO #tblSubErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' does not have a positive LastPrice amount.'
		FROM #mc_SubImport
		WHERE LastPrice IS NULL OR LastPrice < 0
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done
	END CATCH

	-- no blank StoreModifiedRate
	update #mc_SubImport set StoreModifiedRate = '' where StoreModifiedRate is null;

	INSERT INTO #tblSubErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing StoreModifiedRate.'
	FROM #mc_SubImport
	WHERE StoreModifiedRate = ''
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- check for StoreModifiedRate
	INSERT INTO #tblSubErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(tmp.rowID as varchar(10)) + ' has an invalid StoreModifiedRate. This column supports YES or NO.'
	FROM #mc_SubImport as tmp
	WHERE StoreModifiedRate not in ('Yes','No')
	ORDER BY tmp.rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- no blank status
	update #mc_SubImport set [status] = '' where [status] is null;

	INSERT INTO #tblSubErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing Status.'
	FROM #mc_SubImport
	WHERE [Status] = ''
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- check for status
	INSERT INTO #tblSubErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(tmp.rowID as varchar(10)) + ' has an invalid Status.'
	FROM #mc_SubImport as tmp
	left outer join dbo.sub_statuses as s on s.statusCode = tmp.Status 
	where s.statusID is null
	ORDER BY tmp.rowID
		IF @@ROWCOUNT > 0 GOTO on_done
	
	-- ensure StartDate is datetime (allow nulls for this check)
	BEGIN TRY
		ALTER TABLE #mc_SubImport ALTER COLUMN StartDate datetime null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblSubErrors (msg)
		VALUES ('The column StartDate contains invalid dates.')
			GOTO on_done
	END CATCH

	-- check for null StartDate
	BEGIN TRY
		ALTER TABLE #mc_SubImport ALTER COLUMN StartDate datetime not null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblSubErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing the required StartDate.'
		FROM #mc_SubImport
		WHERE StartDate IS NULL
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done
	END CATCH

	-- ensure EndDate is datetime (allow nulls for this check)
	BEGIN TRY
		ALTER TABLE #mc_SubImport ALTER COLUMN EndDate datetime null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblSubErrors (msg)
		VALUES ('The column EndDate contains invalid dates.')
			GOTO on_done
	END CATCH

	-- check for null EndDate
	BEGIN TRY
		ALTER TABLE #mc_SubImport ALTER COLUMN EndDate datetime not null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblSubErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing the required EndDate.'
		FROM #mc_SubImport
		WHERE EndDate IS NULL
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done
	END CATCH

	-- ensure GraceEndDate is datetime (allow nulls for this check)
	BEGIN TRY
		ALTER TABLE #mc_SubImport ALTER COLUMN GraceEndDate datetime null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblSubErrors (msg)
		VALUES ('The column GraceEndDate contains invalid dates.')
			GOTO on_done
	END CATCH

	IF @useAccrualAccounting = 1 BEGIN
		-- ensure RecogStartDate is datetime (allow nulls for this check)
		BEGIN TRY
			ALTER TABLE #mc_SubImport ALTER COLUMN RecogStartDate datetime null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSubErrors (msg)
			VALUES ('The column RecogStartDate contains invalid dates.')
				GOTO on_done
		END CATCH

		-- check for null RecogStartDate
		BEGIN TRY
			ALTER TABLE #mc_SubImport ALTER COLUMN RecogStartDate datetime not null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSubErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing the required RecogStartDate.'
			FROM #mc_SubImport
			WHERE RecogStartDate IS NULL
			ORDER BY rowID
				IF @@ROWCOUNT > 0 GOTO on_done
		END CATCH		

		-- ensure RecogEndDate is datetime (allow nulls for this check)
		BEGIN TRY
			ALTER TABLE #mc_SubImport ALTER COLUMN RecogEndDate datetime null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSubErrors (msg)
			VALUES ('The column RecogEndDate contains invalid dates.')
				GOTO on_done
		END CATCH

		-- check for null RecogEndDate
		BEGIN TRY
			ALTER TABLE #mc_SubImport ALTER COLUMN RecogEndDate datetime not null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSubErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing the required RecogEndDate.'
			FROM #mc_SubImport
			WHERE RecogEndDate IS NULL
			ORDER BY rowID
				IF @@ROWCOUNT > 0 GOTO on_done
		END CATCH		
	END ELSE BEGIN
		update #mc_SubImport
		set recogStartDate = StartDate,
			recogEndDate = EndDate
	END

	-- match on parent subscription type
	update #mc_SubImport set ParentSubscriptionType = '' where ParentSubscriptionType is null;

	update tmp
	set tmp.parentSubtypeUID = t.uid
	from #mc_SubImport as tmp
	inner join dbo.sub_types as t on t.typeName = tmp.ParentSubscriptionType 
		and t.siteID = @siteID
		and t.status = 'A' 
	where tmp.ParentSubscriptionType <> ''
	
	-- check for missing parent sub type matches
	INSERT INTO #tblSubErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' does not match an existing Parent Subscription Type.'
	FROM #mc_SubImport
	WHERE ParentSubscriptionType <> '' and parentSubtypeUID IS NULL
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- match on parent subscription 
	update #mc_SubImport set ParentSubscriptionName = '' where ParentSubscriptionName is null;

	update tmp
	set tmp.parentSubUID = subs.uid,
		tmp.parentSubID = subs.subscriptionID
	from #mc_SubImport as tmp
	inner join dbo.sub_subscriptions as subs on subs.subscriptionName = tmp.ParentSubscriptionName 
		and subs.status = 'A'
	inner join dbo.sub_types as t on subs.typeID = t.typeID 
		and t.uid = tmp.ParentSubtypeUID
	where tmp.ParentSubscriptionName <> ''

	-- check for missing parent sub matches
	INSERT INTO #tblSubErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' does not match an existing Parent Subscription.'
	FROM #mc_SubImport
	WHERE ParentSubscriptionName <> '' and parentSubUID IS NULL
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- check for parent subs and subs the same
	INSERT INTO #tblSubErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a parent subscription of itself.'
	FROM #mc_SubImport
	WHERE parentSubUID is not null and subUID is not null and parentSubUID = subUID
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- check sub dates
	INSERT INTO #tblSubErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a StartDate after the EndDate.'
	FROM #mc_SubImport
	WHERE StartDate > EndDate
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	INSERT INTO #tblSubErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a GraceEndDate before the EndDate.'
	FROM #mc_SubImport
	WHERE GraceEndDate is not null and EndDate > GraceEndDate
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	INSERT INTO #tblSubErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a RecogStartDate after the RecogEndDate.'
	FROM #mc_SubImport
	WHERE RecogStartDate > RecogEndDate
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	INSERT INTO #tblSubErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a RecogStartDate before the StartDate.'
	FROM #mc_SubImport
	WHERE RecogStartDate < StartDate
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	INSERT INTO #tblSubErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a RecogEndDate after the EndDate.'
	FROM #mc_SubImport
	WHERE RecogEndDate > EndDate
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	INSERT INTO #tblSubErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is an expired subscription incorrectly ending in the future.'
	FROM #mc_SubImport
	WHERE endDate > getdate() and [status] = 'E'
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	INSERT INTO #tblSubErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is an active subscription incorrectly starting in the future.'
	FROM #mc_SubImport
	WHERE startDate > getdate() and [status] = 'A'
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- EndDate and GraceEndDate should be 23:59:59.997
	update #mc_SubImport set EndDate = dateadd(ms,-3,dateadd(day,1,DATEADD(dd,DATEDIFF(dd,0,EndDate),0)))
	update #mc_SubImport set GraceEndDate = dateadd(ms,-3,dateadd(day,1,DATEADD(dd,DATEDIFF(dd,0,GraceEndDate),0))) where GraceEndDate is not null
	update #mc_SubImport set recogEndDate = dateadd(ms,-3,dateadd(day,1,DATEADD(dd,DATEDIFF(dd,0,recogEndDate),0)))

	-- check child subs
	INSERT INTO #tblSubErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(tmp.rowID as varchar(10)) + ' cannot locate matching parent subscription.'
	FROM #mc_SubImport as tmp
	left outer join #mc_SubImport as tmp2 on tmp2.MCMemberid = tmp.MCMemberid 
		and tmp2.subID = tmp.parentSubID
		and tmp2.TreeCode = tmp.TreeCode
	where tmp.parentSubID is not null
	and tmp2.rowID is null
	ORDER BY tmp.rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- ensure treecode has only one root
	INSERT INTO #tblSubErrors (msg)
	SELECT TOP 100 PERCENT 'MemberNumber ' + MemberNumber + ' has multiple root subscriptions in TreeCode ' + TreeCode
	FROM #mc_SubImport
	where parentSubID is null
	group by MemberNumber, TreeCode
	having count(*) > 1
	order by 1
		IF @@ROWCOUNT > 0 GOTO on_done

	-- get the rootSubID for each treecode
	update a
	set a.rootSubID = b.subID
	from #mc_SubImport as a
	inner join (
		select distinct MCMemberID, TreeCode, subID
		from #mc_SubImport
		where parentSubID is null
	) as b on b.MCmemberID = a.MCmemberID and b.TreeCode = a.TreeCode

	-- reorder import file based on subscription path
	update a
	set a.rowID = b.newRowID
	from #mc_SubImport as a
	inner join (
		select orig.rowID, row_number() OVER (ORDER by orig.membernumber, tmp2.rootSubscriptionID, tmp2.subscriptionPath, orig.rowID) as newRowID
		from #mc_SubImport as orig
		inner join (
			select tmp.subID as rootSubscriptionID, sto.subscriptionID, sto.subscriptionPath
			from (
				select distinct subID
				from #mc_SubImport
				where parentSubID is null
			) as tmp
			cross apply dbo.fn_sub_getSubscriptionTreeOrder(tmp.subID) as sto
		) as tmp2 on tmp2.subscriptionID = orig.subID and tmp2.rootSubscriptionID = orig.rootSubID
	) as b on b.rowID = a.rowID

	BEGIN TRY
		ALTER TABLE #mc_SubImport ADD PRIMARY KEY (rowID);
	END TRY
	BEGIN CATCH
		INSERT INTO #tblSubErrors (msg)
		VALUES ('Reordering based on subscription path has failed. Usually this indicates an issue with incorrect TreeCodes.')

		/*
		INSERT INTO #tblSubErrors (msg)
		select 'Duplicate RowIDs after reordering: RowID ' + cast(tmp.rowID as varchar(10)) + ' (' + (select top 1 membernumber + ' - ' + subscriptionName from #mc_SubImport where rowID = tmp.rowID) + ')'
		from #mc_SubImport as tmp
		group by tmp.rowID 
		having count(*) > 1
		order by 1
		*/

		INSERT INTO #tblSubErrors (msg)
		VALUES (left(error_message(),300))
	END CATCH

END TRY
BEGIN CATCH
	INSERT INTO #tblSubErrors (msg)
	VALUES ('Unable to validate data in required columns.')

	INSERT INTO #tblSubErrors (msg)
	VALUES (left(error_message(),300))

	GOTO on_done
END CATCH


-- ************************
-- generate result xml file 
-- ************************
on_done:
	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblSubErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE)

RETURN 0
GO

CREATE PROC [dbo].[sub_importSubscribers_import]
@siteID int,
@recordedByMemberID int, 
@useAccrualAccounting bit,
@skipEmailTemplateNotifications bit,
@importResult xml OUTPUT

AS

SET NOCOUNT ON

DECLARE @orgID int
select @orgID = orgID from dbo.sites where siteID = @siteID

DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpSubscriberTrees') IS NOT NULL 
		DROP TABLE #tmpSubscriberTrees

	CREATE TABLE #tmpSubscriberTrees (memberID int, treeCode varchar(50), itemUID uniqueidentifier DEFAULT NEWID())
	INSERT INTO #tmpSubscriberTrees (memberID, treeCode)
	select distinct MCMemberID, treeCode
	from #mc_SubImport

	-- all get the same ItemGroupUID
	declare @itemGroupUID uniqueidentifier
	select @itemGroupUID = NEWID()

	declare @statusInserting int, @statusReady int
	select @statusInserting = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'addSubscribers'
		and qs.queueStatus = 'insertingItems'
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'addSubscribers'
		and qs.queueStatus = 'readyToProcess'

	insert into platformQueue.dbo.tblQueueItems_addSubscribers (itemUID, itemGroupUID, recordedByMemberID, orgID, siteID, memberID, treeCode, skipEmailTemplateNotifications)
		OUTPUT inserted.itemUID, @statusInserting 
		INTO platformQueue.dbo.tblQueueItems(itemUID, queueStatusID)
	select distinct itemUID, @itemGroupUID, @recordedByMemberID, @orgID, @siteID, memberID, treeCode, @skipEmailTemplateNotifications
	from #tmpSubscriberTrees

	insert into platformQueue.dbo.tblQueueItems_addSubscribersDetail (itemUID, rowID, subscriptionID, parentSubscriptionID, RFID, 
		rateUID, subStartDate, subEndDate, graceEndDate, recogStartDate, recogEndDate, [status], lastPrice, storeModifiedRate)
	select tmpT.itemUID, tmp.rowID, tmp.subID, tmp.parentSubID, tmp.RFID, tmp.rateUID, tmp.StartDate, tmp.EndDate, tmp.graceEndDate, 
		tmp.recogStartDate, tmp.recogEndDate, tmp.status, tmp.lastPrice, tmp.storeModifiedRate
	from #mc_SubImport as tmp
	inner join #tmpSubscriberTrees as tmpT on tmpT.memberID = tmp.MCMemberID and tmpT.treeCode = tmp.TreeCode

	-- update queue item groups to show ready to process
	update qi WITH (UPDLOCK, HOLDLOCK)
	set qi.queueStatusID = @statusReady,
		dateUpdated = getdate()
	from platformQueue.dbo.tblQueueItems as qi
	inner join #tmpSubscriberTrees as tmpT on tmpT.itemUID = qi.itemUID

	IF OBJECT_ID('tempdb..#tmpSubscriberTrees') IS NOT NULL 
		DROP TABLE #tmpSubscriberTrees

	IF @TranCounter = 0
		COMMIT TRAN;
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	INSERT INTO #tblSubErrors (msg)
	VALUES ('Unable to add subscribers to the queue.')

	INSERT INTO #tblSubErrors (msg)
	VALUES (left(error_message(),300))
END CATCH


on_done:
	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblSubErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE)

	IF OBJECT_ID('tempdb..#tmpSubscriberTrees') IS NOT NULL 
		DROP TABLE #tmpSubscriberTrees

RETURN 0
GO

CREATE PROC [dbo].[sub_importSubscribers]
@siteid int, 
@recordedByMemberID int,
@useAccrualAccounting bit,
@skipEmailTemplateNotifications bit,
@importResult xml OUTPUT

AS

SET NOCOUNT ON

IF OBJECT_ID('tempdb..#tblSubErrors') IS NOT NULL 
	DROP TABLE #tblSubErrors
CREATE TABLE #tblSubErrors (rowid int IDENTITY(1,1), msg varchar(300))

-- ensure temp table exists
IF OBJECT_ID('tempdb..#mc_SubImport') IS NULL BEGIN
	INSERT INTO #tblSubErrors (msg)
	VALUES ('Unable to locate the imported data for processing.')

	GOTO on_done
END

-- ensure all columns exist
set @importResult = null
EXEC dbo.sub_importSubscribers_prepTable @useAccrualAccounting=@useAccrualAccounting, @importResult=@importResult OUTPUT
IF @importResult.value('count(/import/errors/error)','int') > 0
	GOTO on_done

-- validate data
set @importResult = null
EXEC dbo.sub_importSubscribers_validate @siteID=@siteID, @useAccrualAccounting=@useAccrualAccounting, @importResult=@importResult OUTPUT
IF @importResult.value('count(/import/errors/error)','int') > 0
	GOTO on_done

-- import data
set @importResult = null
EXEC dbo.sub_importSubscribers_import @siteID=@siteID, @recordedByMemberID=@recordedByMemberID, @useAccrualAccounting=@useAccrualAccounting, @skipEmailTemplateNotifications=@skipEmailTemplateNotifications, @importResult=@importResult OUTPUT

-- cleanup
on_done:
	IF OBJECT_ID('tempdb..#tblSubErrors') IS NOT NULL 
		DROP TABLE #tblSubErrors

RETURN 0
GO

DROP PROC dbo.sub_importSubscriptions_toTemp
GO
DROP PROC dbo.sub_importSubscriptions_toPerm
GO
