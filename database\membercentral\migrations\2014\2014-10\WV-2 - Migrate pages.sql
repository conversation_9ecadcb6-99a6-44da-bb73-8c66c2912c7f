use membercentral
SET nocount on

DECLARE @oldRoot varchar(500),@newRoot varchar(500), @oldDocumentRoot varchar(500), 
		@newDocumentRoot varchar(500), @oldorgcode varchar(10), @orgcode varchar(10), 
		@sitecode varchar(10), @siteID int, @orgID int, @rootSectionID int, 
		@userCreatedSectionResourceID int, @minSection varchar(50), @thisSectionID int, 
		@minpageID int, @pgResourceTypeID int, 
		@appPgResourceTypeID int, @siteResourceStatusID int, @thisPageID int, 
		@HTMLResourceTypeID int, @customPageApplicationTypeID int, @zoneIDMain int,
		@ppageTypeID int, @ppageName varchar(100), @ppageTitle varchar(100), @ppagemode int,
		@ppageDescription varchar(100), @pContentPath varchar(300), 
		@applicationInstanceID int, @siteresourceID int, 
		@penableSSL bit, @presourceid int, @rawContent varchar(max), @thisContentID int, 
		@thisResourceID int, @trashID int, @minpgGroupID int, @documentResourceTypeID int, 
		@doc_orgdocumentId int, @doc_sectionID int, @doc_contributorMemberID int, 
		@doc_docTitle varchar(200), @doc_docDesc varchar(200), @doc_filename varchar(100), 
		@doc_fileExt varchar(20), @doc_dateCreated datetime, @doc_newSiteResourceID int, 
		@minID int, @maxDocID int, @rc int, @documentLanguageID int, @documentVersionID int,
		@param1 varchar(max), @param2 varchar(max)
declare @cmd VARCHAR(1000)
DECLARE @tmpPages TABLE (pageID int, newPageID int, pageTypeID int, pageName varchar(100), pageTitle varchar(100), sortDescription varchar(500), pageStatusID int, dateCreated datetime, datemodified datetime, resourceid int, sectionTitle varchar(200), pageDescription varchar(100), enableSSL bit, pagemode smallint, ContentPath varchar(300), CFMLPath varchar(300), CFMLDirectoryPath varchar(300))
DECLARE @sections TABLE (sectionName varchar(50), sectionID int)
DECLARE @pgGroupID TABLE (groupID int)
DECLARE @docs TABLE (id int IDENTITY(1,1), orgdocumentId int, sectionID int, contributorMemberID int, docTitle varchar(200), docDesc varchar(200), filename varchar(100), fileExt varchar(20), dateCreated datetime, dateModified datetime);
DECLARE @filesToCopy TABLE (id int IDENTITY(1,1), param1 varchar(max), param2 varchar(max));
DECLARE @sectionsCheck TABLE (sectionName varchar(50), sectionCode varchar(50));
DECLARE @environ varchar(5)

/* ***** set codes *************** */
select @oldorgcode = 'WV'
select @orgcode = 'WV'
select @sitecode = 'WV'
select @environ = 'DEV'   -- DEV or PROD
/* ***** set codes *************** */


-- maximum orgdocumentID cannot be higher than 40000
select @maxDocID = max(orgdocumentID)
	from tlasites.trialsmith.dbo.orgdocuments d
	where d.orgcode = @oldorgcode
	and d.statusID = 1
IF @maxDocID > 40000 BEGIN
	print 'STOPPED - maximum orgdocumentID cannot be higher than 40000'
	GOTO on_done
END

-- duplicate section code check
-- this query should not return any rows. If so, need to clean up record 

insert into @sectionsCheck(sectionName, sectionCode)
select distinct ltrim(rtrim(isnull(p.sectionTitle,''))) as sectionTitle, dbo.fn_regExReplace(isnull(ltrim(rtrim(isnull(p.sectionTitle,''))) ,''),'[^A-Za-z0-9]','') sectionTitleCode
from tlasites.trialsmith.dbo.Pages as p
where p.orgcode = @oldorgcode
and p.pageTypeID = 2
and p.pagestatusID = 1
and p.pagename not in ('login','events','main','storemain','unavailable','norights','InactiveUser','SiteAgreement','MemberEditText','accountalreadysetup')
and p.pageid not in (
	select searchPageID from tlasites.trialsmith.dbo.orgMemberConfig where orgcode = p.orgcode and searchPageID is not null
	union
	select editPageID from tlasites.trialsmith.dbo.orgMemberConfig where orgcode = p.orgcode and editPageID is not null
	union
	select searchPageID from tlasites.trialsmith.dbo.jobBank where orgcode = p.orgcode and searchPageID is not null
	union
	select editPageID from tlasites.trialsmith.dbo.jobBank where orgcode = p.orgcode and editPageID is not null
	)

IF EXISTS (
	select sectionCode, count(*)
	from @sectionsCheck 
	group by sectionCode
	having count(*) > 1
) BEGIN
	select sectionCode, count(*)
	from @sectionsCheck 
	group by sectionCode
	having count(*) > 1

	print 'STOPPED - duplicate section codes would be created'
	GOTO on_done
END



IF @environ = 'PROD' BEGIN
	set @oldRoot = '\\tsfile1\f$\tlasites\www\'
	set @oldDocumentRoot = '\\tsfile1\f$\tlasites\docCatalog\'
	set @newRoot = '\\tsfile1\f$\platform\membercentral\'
	set @newDocumentRoot = '\\tsfile1\f$\platform\siteDocuments\'
END ELSE BEGIN
	set @oldRoot = '\\dev01\tlasites\www\'
	set @oldDocumentRoot = '\\dev01\tlasites\docCatalog\'
	set @newRoot = '\\dev01\platform\membercentral\'
	set @newDocumentRoot = '\\dev01\platform\siteDocuments\'
END

BEGIN TRAN

select @siteID = dbo.fn_getSiteIDFromSiteCode(@sitecode)
	IF @@ERROR <> 0 GOTO on_error
select @orgID = dbo.fn_getOrgIDFromOrgCode(@orgcode)
	IF @@ERROR <> 0 GOTO on_error
select @rootSectionID = dbo.fn_getRootSectionID(@siteid)
	IF @@ERROR <> 0 GOTO on_error
select @userCreatedSectionResourceID = dbo.fn_getResourceTypeID('UserCreatedSection')
	IF @@ERROR <> 0 GOTO on_error
select @pgResourceTypeID = dbo.fn_getResourceTypeId('UserCreatedPage')
	IF @@ERROR <> 0 GOTO on_error
select @appPgResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedPage')
	IF @@ERROR <> 0 GOTO on_error
select @HTMLResourceTypeID = dbo.fn_getResourceTypeID('UserCreatedContent')
	IF @@ERROR <> 0 GOTO on_error
select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')
	IF @@ERROR <> 0 GOTO on_error
select @customPageApplicationTypeID = dbo.fn_getApplicationTypeIDFromName('CustomPage')
	IF @@ERROR <> 0 GOTO on_error
select @zoneIDMain = dbo.fn_getZoneID('Main')
	IF @@ERROR <> 0 GOTO on_error
select @documentResourceTypeID = dbo.fn_getResourceTypeID('UserCreatedDocument')
	IF @@ERROR <> 0 GOTO on_error

exec dbo.cache_perms_setStatus @orgid=@orgid, @status='disabled'

-- Populate Page temp table
INSERT INTO @tmpPages (pageID, newPageID, pageTypeID, pageName, pageTitle, sortDescription, pageStatusID, dateCreated, datemodified, resourceid, sectionTitle, pageDescription, enableSSL, pagemode, ContentPath)
select p.pageid, null, p.pageTypeID, p.pageName, isnull(nullif(p.pageHeader,''),p.pagename) as pageTitle, p.sortDescription, p.pageStatusId, p.datecreated, p.datemodified, p.resourceid, ltrim(rtrim(isnull(p.sectionTitle,''))) as sectionTitle,
	left(p.pageDescription,100), isnull(p.enableSSL,0) as enableSSL, 
	case when p.showfullpage = 1 then dbo.fn_getModeId('Full') else null end,
	ContentPath = @oldRoot + p.orgcode + '\views\' + p.pageName + 'content.cfm'
from tlasites.trialsmith.dbo.Pages as p
where p.orgcode = @oldorgcode
and p.pageTypeID = 2
and p.pagestatusID = 1
and p.pagename not in ('login','events','main','storemain','unavailable','norights','InactiveUser','SiteAgreement','MemberEditText','accountalreadysetup')
and p.pageid not in (
	select searchPageID from tlasites.trialsmith.dbo.orgMemberConfig where orgcode = p.orgcode and searchPageID is not null
	union
	select editPageID from tlasites.trialsmith.dbo.orgMemberConfig where orgcode = p.orgcode and editPageID is not null
	union
	select searchPageID from tlasites.trialsmith.dbo.jobBank where orgcode = p.orgcode and searchPageID is not null
	union
	select editPageID from tlasites.trialsmith.dbo.jobBank where orgcode = p.orgcode and editPageID is not null
	)
order by p.pageName
	IF @@ERROR <> 0 GOTO on_error

/* ********************************* */
/* Create Sections */
/* ********************************* */
insert into @sections (sectionName)
select distinct sectionTitle from @tmpPages where len(sectionTitle) > 0
	IF @@ERROR <> 0 GOTO on_error

select @minSection = min(sectionName) from @sections
while @minSection is not null BEGIN
	print 'section: ' + @minSection

	EXEC @rc = dbo.cms_createPageSection @siteID=@siteID, 
		@sectionResourceTypeID = @userCreatedSectionResourceID, @ovTemplateID = null, @ovTemplateIDMobile = null,
		@ovModeID = null, @parentSectionID = @rootSectionID, @sectionName = @minSection,
		@sectionCode = @minSection, @inheritPlacements = 1, @sectionID = @thisSectionID OUTPUT
		IF @@ERROR <> 0 or @RC <> 0 OR @thisSectionID = 0 GOTO on_error
	update @sections set sectionID = @thisSectionID where sectionName = @minSection
		IF @@ERROR <> 0 GOTO on_error

	select @minSection = min(sectionName) from @sections where sectionName > @minSection
END

/* ********************************* */
/* Create Pages */
/* ********************************* */
SELECT @minpageID = min(pageID) from @tmpPages
WHILE @minpageID is not null BEGIN

	SELECT @ppageTypeID = pageTypeID, @ppageName = pageName, @ppageTitle = pageTitle,
		@ppagemode = pagemode, @ppageDescription = pageDescription, @pContentPath = ContentPath,
		@penableSSL = enableSSL, @presourceid = resourceid, 
		@thisSectionID = isnull(s.sectionID,@rootSectionID)
	FROM @tmpPages tmp
	LEFT OUTER JOIN @sections as s on s.sectionName = tmp.sectionTitle
	WHERE tmp.pageID = @minpageID

	print 'Page: ' + isnull(@ppageName,'')

	EXEC @rc = dbo.cms_createPage @siteid=@siteID, @languageID=1, @resourceTypeID=@pgResourceTypeID,
		@siteResourceStatusID=@siteResourceStatusID, @pgParentResourceID=null, 
		@isVisible=1, @sectionID=@thisSectionID, @ovTemplateID=null, @ovTemplateIDMobile=null, @ovModeID=@ppagemode,
		@pageName=@ppageName, @pageTitle=@ppageTitle, @pageDesc=@ppageDescription,
		@keywords='', @inheritPlacements=1,	@allowReturnAfterLogin=1, @checkReservedNames=1,
		@pageID=@thisPageID OUTPUT
	IF @@ERROR <> 0 or @RC <> 0 OR @thisPageID = 0 BEGIN
		print '*** ' + isnull(@ppageName,'') + ' page not migrated'
	END
	ELSE BEGIN
		select @rawContent = ltrim(dbo.fn_readfile(@pContentPath,0,0))

		exec @rc = dbo.cms_createContentObject
			@siteID = @siteID,
			@resourceTypeID = @HTMLResourceTypeID,
			@parentSiteResourceID = null,
			@siteResourceStatusID = @siteResourceStatusID,
			@isSSL = @penableSSL, 
			@isHTML=1, 
			@languageID = 1, 
			@isActive = 1, 
			@contentTitle = @ppageTitle, 
			@contentDesc = @ppageDescription, 
			@rawContent = @rawContent, 
			@memberID = null,
			@contentID = @thisContentID OUTPUT,
			@siteResourceID = @thisResourceID OUTPUT
		IF @@ERROR <> 0 or @RC <> 0 OR @thisContentID = 0 GOTO on_error

		exec @rc = dbo.cms_createPageZoneResource
			@pageID = @thisPageID,
			@zoneID = @zoneIDMain,
			@siteResourceID = @thisResourceID,
			@pzrID = @trashID OUTPUT
		IF @@ERROR <> 0 or @RC <> 0 OR @trashID = 0 GOTO on_error

		insert into @pgGroupID (groupID)
		select g.groupID
		from tlasites.trialsmith.dbo.siteresourcerights as srr
		inner join tlasites.trialsmith.dbo.usergroups as ug on ug.usergroupid = srr.groupID 
			and ug.orgcode = @orgcode
		inner join dbo.ams_groups as g on g.groupName = 
			case ug.description when 'Guest Group' then 'Public' else ug.description end COLLATE Latin1_General_CI_AI
			and orgID = @orgID
		where srr.resourceID = @presourceid
		and srr.functionID = 1
			IF @@ERROR <> 0 GOTO on_error

		select @minpgGroupID = min(groupID) from @pgGroupID
		WHILE @minpgGroupID is not null BEGIN
			exec dbo.cms_createSiteResourceRight
				@siteID = @siteID,
				@siteResourceID = @thisResourceID, 
				@include = 1, 
				@functionID = 4, 
				@roleID = null, 
				@groupID = @minpgGroupID, 
				@memberID = null, 
				@inheritedRightsResourceID = null, 
				@inheritedRightsFunctionID = null, 
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 GOTO on_error
			select @minpgGroupID = min(groupID) from @pgGroupID where groupID > @minpgGroupID
		END

		DELETE FROM @pgGroupID
	END	

	SELECT @minpageID = min(pageID) from @tmpPages where pageID > @minpageID
END

/* ********************************* */
/* content: 'main','InactiveUser','norights','siteagreement' */
/* ********************************* */

-- update main content
select @rawContent = ltrim(dbo.fn_readfile(@oldRoot + @orgcode + '\views\maincontent.cfm',0,0))
if len(@rawContent) > 0 BEGIN
	select @thisContentID = c.contentID
		from dbo.cms_pages p 
		inner join dbo.cms_pageZonesResources as pzr on p.pageID = pzr.pageID
			and p.siteID = @siteID
			and p.pageName = 'Main'
			and p.pageID = pzr.pageID
			and pzr.zoneID = @zoneIDMain
		inner join dbo.cms_siteResources sr on sr.siteResourceID = pzr.siteResourceID
		inner join dbo.cms_content as c on c.siteResourceID = sr.siteResourceID
	exec dbo.cms_updateContent @contentID = @thisContentID, @languageID = 1, @isSSL = 0, @isHTML = 1, @contentTitle = 'Main', @contentDesc = '', @rawcontent = @rawContent
END

-- update No Rights
select @rawContent = ltrim(dbo.fn_readfile(@oldRoot + @orgcode + '\views\NoRightsContent.cfm',0,0))
if len(@rawContent) > 0 BEGIN
	select @thisContentID = s.noRightsContentID
		from sites s 
		where s.siteID = @siteID
	exec dbo.cms_updateContent @contentID = @thisContentID, @languageID = 1, @isSSL = 0, @isHTML = 1, @contentTitle = 'No Rights', @contentDesc = '', @rawcontent = @rawContent
END

-- update Inactive User
select @rawContent = ltrim(dbo.fn_readfile(@oldRoot + @orgcode + '\views\InactiveUserContent.cfm',0,0))
if len(@rawContent) > 0 BEGIN
	select @thisContentID = s.InactiveUserContentID
		from sites s 
		where s.siteID = @siteID
	exec dbo.cms_updateContent @contentID = @thisContentID, @languageID = 1, @isSSL = 0, @isHTML = 1, @contentTitle = 'Inactive User', @contentDesc = '', @rawcontent = @rawContent
END

-- update site agreement
select @rawContent = ltrim(dbo.fn_readfile(@oldRoot + @orgcode + '\views\SiteAgreementContent.cfm',0,0))
if len(@rawContent) > 0 BEGIN
	select @thisContentID = s.siteAgreementContentID
		from sites s 
		where s.siteID = @siteID
	exec dbo.cms_updateContent @contentID = @thisContentID, @languageID = 1, @isSSL = 0, @isHTML = 1, @contentTitle = 'Site Agreement', @contentDesc = '', @rawcontent = @rawContent
END


-- Populate siteRedirects table aka quick links 
INSERT INTO dbo.siteRedirects (r.siteID, r.redirectName, r.redirectURL)
select @siteID, redirectName, replace(replace(redirectURL, '/'+@orgCode,''),'event=showPage&', '') as newRedirect
from tlasites.trialsmith.dbo.siteRedirects  
where siteRedirects.orgCode = @orgCode
and redirectName not in (
	SELECT r.redirectName
	FROM dbo.siteRedirects r
	where siteID = @siteID
)
	IF @@ERROR <> 0 GOTO on_error

/* ********************************* */
/* Import Documents */
/* ********************************* */
insert into @docs (orgdocumentId , sectionID , contributorMemberID , docTitle , docDesc , filename , fileExt , dateCreated , dateModified)
select d.orgDocumentId, dbo.fn_getRootSectionID(@siteID) as sectionID,
	4 as contributorMemberID, left(d.title,200), left(d.description,200), left(d.filename,100), left(d.fileNameExtension,20), d.dateCreated, d.dateModified
from tlasites.trialsmith.dbo.orgdocuments d
where d.orgcode = @orgcode
and d.statusID = 1
and dbo.fn_fileExists(@oldDocumentRoot + @orgcode + '\' + cast(d.orgdocumentID as varchar(10)) + '.' + d.fileNameExtension) = 1
	IF @@ERROR <> 0 GOTO on_error

SELECT @minID = min(id) from @docs
WHILE @minID is not null BEGIN

	SELECT @doc_orgdocumentId = orgdocumentID,
		@doc_sectionID = sectionID,
		@doc_contributorMemberID = contributorMemberID,
		@doc_docTitle = docTitle,
		@doc_docDesc = docDesc,
		@doc_filename = filename,
		@doc_fileExt = fileExt,
		@doc_dateCreated = dateCreated
	from @docs
	WHERE ID = @minID

	select @doc_newSiteResourceID = null

	-- first create a resourceID for the document
	EXEC dbo.cms_createSiteResource @resourceTypeID=@documentResourceTypeID, 
		@siteResourceStatusID=@siteResourceStatusID, @siteID=@siteid, @isVisible=1, 
		@parentSiteResourceID=null, @siteResourceID=@doc_newSiteResourceID OUTPUT
	IF @@ERROR <> 0 GOTO on_error

	-- add the document
	SET IDENTITY_INSERT dbo.cms_documents ON
	INSERT INTO dbo.cms_documents (documentid, siteID, siteResourceID, sectionID, dateCreated)
	VALUES (@doc_orgdocumentId, @siteid, @doc_newSiteResourceID, @doc_sectionID, @doc_dateCreated)
		IF @@ERROR <> 0 GOTO on_error
	SET IDENTITY_INSERT dbo.cms_documents OFF
		
	-- add document language
	INSERT INTO dbo.cms_documentLanguages (documentID, languageID, docTitle, docDesc, dateCreated, dateModified)
	VALUES (@doc_orgdocumentId, 1, isnull(@doc_docTitle,''), isnull(@doc_docDesc,''), @doc_dateCreated, getdate())
		IF @@ERROR <> 0 GOTO on_error
		SELECT @documentLanguageID = IDENT_CURRENT('cms_documentLanguages')

	-- add content version
	EXEC @rc = dbo.cms_createDocumentVersion @documentLanguageID=@documentLanguageID, @fileName=@doc_filename, 
		@fileExt=@doc_fileExt, @author=NULL, @contributorMemberID=@doc_contributorMemberID, @recordedByMemberID=@doc_contributorMemberID,
		@isActive=1, @publicationDate=@doc_dateCreated, @documentVersionID=@documentVersionID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- insert data to do copy outside transaction
	select @param1 = @oldDocumentRoot + @orgcode + '\' + cast(@doc_orgdocumentId as varchar(10)) + '.' + @doc_fileExt
	select @param2 = @newDocumentRoot + @orgcode + '\' + @siteCode + '\' + cast(@documentVersionID as varchar(10)) + '.' + @doc_fileExt

	INSERT INTO @filesToCopy (param1, param2)
	VALUES (@param1, @param2)
		IF @@ERROR <> 0 GOTO on_error
	

	-- permissions
	delete from @pgGroupID	
	insert into @pgGroupID (groupID)
	select g.groupID
	from tlasites.trialsmith.dbo.orgDocumentRights as odr
	inner join tlasites.trialsmith.dbo.usergroups as ug on ug.usergroupid = odr.usergroupID 
		and ug.orgcode = @orgcode
	inner join dbo.ams_groups as g on g.groupName = 
		case ug.description when 'Guest Group' then 'Public' else ug.description end COLLATE Latin1_General_CI_AI
		and orgID = @orgID
	where odr.orgDocumentID = @doc_orgdocumentId
	and odr.functionID = 1
		IF @@ERROR <> 0 GOTO on_error

	select @minpgGroupID = min(groupID) from @pgGroupID
	WHILE @minpgGroupID is not null BEGIN
		exec dbo.cms_createSiteResourceRight
			@siteID = @siteID,
			@siteResourceID = @doc_newSiteResourceID, 
			@include = 1, 
			@functionID = 4, 
			@roleID = null, 
			@groupID = @minpgGroupID, 
			@memberID = null, 
			@inheritedRightsResourceID = null, 
			@inheritedRightsFunctionID = null, 
			@resourceRightID = @trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
		select @minpgGroupID = min(groupID) from @pgGroupID where groupID > @minpgGroupID
	END

	SELECT @minID = min(ID) from @docs where ID > @minID
END


SET nocount off

COMMIT TRAN

SELECT * from @filesToCopy
-- Move files
SELECT @minID = min(id) from @filesToCopy
WHILE @minID is not null BEGIN
	SELECT @param1 = param1,
		@param2 = param2
	from @filesToCopy
	WHERE ID = @minID

	-- copy the file
	IF dbo.fn_createDirectory(@newDocumentRoot + @orgcode + '\' + @siteCode + '\') = 1 BEGIN
		select @trashID = dbo.fn_copyFile(@param1, @param2, 1)
		IF @@ERROR <> 0 GOTO on_error
	END

	SELECT @minID = min(ID) from @filesToCopy where ID > @minID
END


-- copy userimages
select @cmd = 'copy ' + @oldRoot + @oldorgcode + '\userimages\*.* ' + @newRoot + 'assets\' + @orgcode + '\' + @siteCode + '\userimages\'
EXEC master..xp_CMDShell @cmd

exec dbo.cache_perms_setStatus @orgid=@orgid, @status='enabled'

print 'Import completed successfully'
GOTO on_done

on_error:
	ROLLBACK TRAN
	print 'Import NOT completed successfully'

on_done:
