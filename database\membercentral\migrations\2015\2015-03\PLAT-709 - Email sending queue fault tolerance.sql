use platformMail;
GO

declare @statusID int

insert into dbo.email_statuses (statusCode, status, statusOrder) values ('G','GrabbedForProcessing',3)
select @statusID = SCOPE_IDENTITY()

update dbo.email_statuses set
    statusOrder = statusOrder + 1
where statusOrder >= 3 and statusID <> @statusID
GO
declare @statusID int

insert into dbo.email_statuses (statusCode, status, statusOrder) values ('R','Requeue',7)
select @statusID = SCOPE_IDENTITY()

GO



ALTER PROC [dbo].[email_markRecipientBatch]
@batchSize int,
@restrictToMessageID int,
@workerUUID uniqueIdentifier OUTPUT

AS

declare @batchStartDate datetime, @newStatusID int, @oldStatusID int
declare @tblMessages table(
	messageID int NOT NULL, 
	replyToEmail varchar(200) NOT NULL, 
	subject varchar(400) NOT NULL, 
	siteCode varchar(10) NOT NULL, 
	siteName varchar(60) NOT NULL,
	email<PERSON>rom varchar(403) NOT NULL,
	senderEmail varchar(200) NOT NULL, 
	messageContent varchar(max) NOT NULL);
declare @tblRecipients table(
	recipientID int NOT NULL, 
	messageID int NOT NULL, 
	memberID int NOT NULL,
	toName varchar(200) NOT NULL,
	toEmail varchar(200) NOT NULL);
    
set @workerUUID = newID()
set @batchStartDate = getdate()
select @newStatusID = statusID from dbo.email_statuses where statusCode = 'G'
select @oldStatusID = statusID from dbo.email_statuses where statusCode = 'Q'

-- mark recipients
update r 
set batchID = @workerUUID,
	batchStartDate = @batchStartDate,
	emailStatusID = @newStatusID
output	inserted.recipientID, 
		inserted.messageID, 
		inserted.memberID,
		inserted.toName,
		inserted.toEmail
into @tblRecipients
from dbo.email_messageRecipientHistory as r
inner join (
	select top (@batchSize) recipientID
	from dbo.email_messageRecipientHistory
	where emailStatusID = @oldStatusID
		and batchID is null
		and messageID = isnull(@restrictToMessageID,messageID)
	order by dateLastUpdated
) as temp on temp.recipientID = r.recipientID
	IF @@ERROR <> 0 GOTO on_error

-- get messages
insert into @tblMessages (messageID, replyToEmail, subject, siteCode, siteName, emailFrom, senderEmail, messageContent)
select m.messageID, m.replyToEmail, m.subject, s.siteCode, s.siteName,
	m.fromEmail + case when len(m.fromName) > 0 then ' (' + m.fromName + ')' else '' end as emailFrom,
	m.senderEmail, 
	replace(m.messagewrapper,'@@rawcontent@@',cv.rawContent) as messageContent
from (select distinct messageID from @tblRecipients) as tmpM
inner join dbo.email_messages as m on m.messageID = tmpM.messageID
inner join membercentral.dbo.cms_contentVersions as cv on cv.contentVersionID = m.contentVersionID
inner join membercentral.dbo.sites as s on s.siteID = m.siteID

select *
from @tblMessages
order by messageID

-- get message merge codes
select distinct m.messageID, reg.Text as fieldName
from @tblMessages as m
cross apply membercentral.dbo.fn_RegexMatches(m.messageContent,'(?<=\[\[)([^,\]]+)(?=,?([^\]]+)?\]\])') as reg
order by 1, 2

-- get recipients
select recipientID, messageID, memberID, 
	toEmail + case when len(toName) > 0 then ' (' + toName + ')' else '' end as emailTo
from @tblRecipients
	IF @@ERROR <> 0 GOTO on_error

-- get metadata
select r.recipientID, f.fieldID, f.fieldName, mf.messageid, mf.memberid, mf.fieldValue
from dbo.email_metadataFields as f
inner join dbo.email_messageMetadataFields as mf on mf.fieldID = f.fieldID
inner join @tblRecipients as r on r.messageID = mf.messageID and r.memberID = mf.memberID
where f.isMergeField = 1

-- normal exit
RETURN 0

-- error exit
on_error:
	RETURN -1
GO
