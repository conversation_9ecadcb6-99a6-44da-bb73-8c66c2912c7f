ALTER PROC lyris_clearInmailMessageFullSource
@batchSize int = 50

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @rc int;

	IF OBJECT_ID('tempdb..##tmpDel') IS NOT NULL 
		DROP TABLE ##tmpDel;
	CREATE TABLE ##tmpDel (inmailID int PRIMARY KEY);

	INSERT INTO ##tmpDel (inmailID)
	SELECT fs.inmailID
	FROM dbo.inmailMesssageFullSource fs WITH(nolock)
	INNER JOIN dbo.inmailMessages as im WITH(nolock) on fs.inmailID = im.inmailID
	INNER JOIN dbo.messages_ m WITH(nolock) on m.MessageID_ = im.messageID_
	INNER JOIN dbo.messageLists ml WITH(nolock) on ml.listID = m.listid
	LEFT OUTER JOIN trialslyris1.dbo.inmail_ lin WITH(nolock) on lin.messageid_ = fs.inmailID
	WHERE lin.messageid_ is null;

	SET @rc = @@ROWCOUNT;

	WHILE (@rc > 0) BEGIN
		DELETE TOP (@BatchSize) fs
		FROM ##tmpDel as D
		INNER JOIN dbo.inmailMesssageFullSource as fs on fs.inmailID = d.inmailID;

		SET @rc = @@ROWCOUNT;

		--RAISERROR('Deleted %d records', 0, 1, @rc) WITH NOWAIT;
		WAITFOR DELAY '00:00:01';
	END

	IF OBJECT_ID('tempdb..##tmpDel') IS NOT NULL 
		DROP TABLE ##tmpDel;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
