ALTER PROCEDURE [dbo].[lyris_ThreadedView2]
@startrow int = 1,
@top int = 100,
@listname varchar(60) = '',
@startdate smalldatetime = NULL

as

SET NOCOUNT ON

-- set startdate if null passed in so we dont have to duplicate queries below
if (@startdate is null) 
	SELECT @startdate = '1-1-1995'

-- set this range
DECLARE @startWithTop int, @firstMessageIDForPage int
SELECT @startWithTop = @top + @startrow

DECLARE @tmpMessages TABLE (messageid_ int, parentid_ int, numMessages int, progressiveCount int)

SET ROWCOUNT @startWithTop

INSERT INTO @tmpMessages (messageid_, parentId_, numMessages, progressiveCount)
SELECT max(m.messageid_) as messageID_, max(m.parentid_) as parentid_, count(m.messageid_) as nummessages, 0
FROM dbo.messages_ as m
inner join dbo.messageLists as ml on ml.listid = m.listid
where ml.list = @listname 
and m.creatstamp_ >= @startdate
and m.parentid_ is not null
and m.isVisible=1
group by m.parentid_
order by messageid_ desc

SET ROWCOUNT 0

-- update progressive message count field
UPDATE tmp
SET progressiveCount = (SELECT sum(nummessages) FROM @tmpMessages as t2 WHERE t2.messageID_ >= tmp.messageID_)
FROM @tmpMessages as tmp


-- remove threads from previous paging calls 
delete from @tmpMessages where progressiveCount < @startrow

-- get first MessageID for this page
select top 1 @firstMessageIDForPage = messageid_ 
from @tmpMessages 
order by progressiveCount

-- remove threads that will be on subsequent paging calls, keeping at least the first thread
delete from @tmpMessages 
where progressiveCount > @startWithTop 
and messageid_ <> @firstMessageIDForPage


-- return joined query
SELECT 1 as orderNode, tmp.messageid_ as tmpMessageID, m.messageid_, m.parentid_, m.creatstamp_, m.hdrsubject_, m.hdrfrom_, m.hdrfromspc_, m.attachmentflag 
from messages_ m
inner join dbo.messageLists as ml on ml.listid = m.listid and m.isVisible=1
INNER JOIN @tmpMessages tmp ON m.messageid_ = tmp.parentid_ AND m.messageID_ = m.parentID_
WHERE ml.list = @listname
union
SELECT 2 as orderNode, tmp.messageid_ as tmpMessageID, m.messageid_, m.parentid_, m.creatstamp_, m.hdrsubject_, m.hdrfrom_, m.hdrfromspc_, m.attachmentflag 
from messages_ m
inner join dbo.messageLists as ml on ml.listid = m.listid and m.isVisible=1
INNER JOIN @tmpMessages tmp ON m.parentid_ = tmp.parentid_ AND m.messageid_ <> m.parentid_
WHERE ml.list = @listname
and creatstamp_ >= @startdate
ORDER BY tmpMessageID desc, orderNode asc, m.messageid_ asc

SET NOCOUNT OFF
GO
