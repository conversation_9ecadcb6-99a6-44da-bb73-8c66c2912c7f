ALTER PROC dbo.checkForFailedDigests
@dateoflastdigest datetime,
@daysToLookBack int,
@fixList bit,
@badlist varchar(60) OUTPUT

AS

-- whenever the overnight digest process fails, it's usually because a corrupted message cause an internal exception in lyris
-- this script will figure out which list likely failed and mark all of the messages that <PERSON>OU<PERSON> have been in that digest as "DIGESTED"
-- This way the bad message will likely be skipped the next time digests run

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @dateOfLookBackStart datetime;

	-- if didn't pass a specific date, look up most recent digest
	if @dateoflastdigest IS NULL
		select top 1 @dateoflastdigest = Created_
		from trialslyris1.dbo.outmail_
		where type_ in ('digest','mdigest','index')
		order by Created_ desc;

	if @daysToLookBack IS NULL
		set @daysToLookBack = 1;

	set @dateOfLookBackStart = dateadd(day,-1 * @daysToLookBack,@dateoflastdigest);

	select distinct top 1 @badlist = m.list_
	from trialslyris1.dbo.messages_ as m
	inner join trialslyris1.dbo.members_ as lm on lm.list_ = m.list_
	where lm.membertype_ in ('normal','held')
	and lm.SubType_ in ('digest','mimedigest','index')
	and m.CreatStamp_ between @dateOfLookBackStart and @dateoflastdigest
	and m.digested_ = 'F'
	order by m.list_;

	IF @fixList = 1 and @badlist is not null
		update trialslyris1.dbo.messages_ 
		set digested_ = 'T'
		where list_ = @badlist
		and CreatStamp_ between @dateOfLookBackStart and @dateoflastdigest
		and digested_ = 'F';

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO
