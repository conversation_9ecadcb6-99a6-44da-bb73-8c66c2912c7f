-- Fileshare IDs
--485 - Eagle Exchange

declare @siteID int
declare @rootSectionID int 

select @siteID = siteID from sites where sitecode='wa'
select @rootSectionID=rootSectionID from fs_fileshare where fileshareid = 485

SELECT tmp2.*
FROM (
	SELECT 
		section.sectionName as [Name of Folder],
		dl.docTitle as [Title of Document], dl.documentID,  dl.docDesc as [Description],
		v.dateCreated as [Created On], v.dateModified as [Last Modified], v.author,
		m3.firstName as [Contributed By First Name], m3.lastName  as [Contributed By Last Name]
	FROM dbo.cms_documents docs
		INNER JOIN cms_documentLanguages dl on docs.documentID = dl.documentID
		INNER JOIN cms_documentVersions v on dl.documentLanguageID = v.documentLanguageID AND v.isActive = 1
		INNER JOIN cms_languages l on dl.languageID = l.languageID
		inner join cms_siteResources docResource on docs.siteResourceID = docResource.siteResourceID
			and docs.siteID =@siteID
			and docResource.isVisible=1
		inner join dbo.fn_getRecursiveSections (@siteID, @rootSectionID, DEFAULT) section on section.sectionID = docs.sectionID
		inner join cms_siteResourceTypes docResourceType on docResource.resourceTypeID = docResourceType.resourceTypeID
		inner join dbo.cms_siteResourceStatuses docResourceStatus on docResourceStatus.siteResourceStatusID = docResource.siteResourceStatusID
			and docResourceStatus.siteResourceStatusDesc = 'Active'
		left outer join cms_siteResources AppResource
			inner join cms_siteResourceTypes AppResourceTypes on AppResourceTypes.resourceTypeID = AppResource.resourceTypeID
			inner join cms_siteResourceTypeClasses AppResourceTypeClasses on AppResourceTypeClasses.resourceTypeClassID = AppResourceTypes.resourceTypeClassID
				and AppResourceTypeClasses.resourceTypeClassName = 'application'
			inner join cms_applicationInstances ai on ai.siteResourceID = AppResource.siteResourceID
			inner join cms_applicationTypes at on ai.applicationTypeID = at.applicationTypeID
		on AppResource.parentSiteResourceID = docResource.siteResourceID
		INNER JOIN dbo.ams_members contributors ON contributors.memberID = v.contributorMemberID
		INNER JOIN dbo.ams_members m3 on contributors.activeMemberid = m3.memberID
) tmp2
