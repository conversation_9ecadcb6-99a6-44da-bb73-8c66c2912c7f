USE [seminarWeb]
GO
/****** Object:  StoredProcedure [dbo].[swl_getSeminarProgress]    Script Date: 02/06/2014 08:58:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROC [dbo].[swl_getSeminarProgress]
@enrollmentID int

AS

-- qry1: seminar settings
SELECT swl.isOpen
from dbo.tblEnrollments as e
inner join dbo.tblSeminars as s on s.seminarID = e.seminarID
inner join dbo.tblSeminarsSWLive as swl on swl.seminarID = s.seminarID
where e.enrollmentID = @enrollmentID

-- qry2: data used to determine progress
DECLARE @allPreTestCompleted bit, @allPostTestCompleted bit, @allEvaluationCompleted bit, @hasCertificate bit, @showSurvey bit, @allowAutoSendOfCertificate bit
SELECT @allPreTestCompleted = 
	CASE 
	WHEN (select count(saf.formID)
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblSeminarsAndForms AS saf ON e.seminarID = saf.seminarID
			LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
				INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
				INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
					and f.isPublished = 1
					and getdate() between f.dateStartPublish and f.dateEndPublish	
					and r.passingPct >= f.passingPct				
				on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e.enrollmentID
			WHERE e.enrollmentID = @enrollmentID
			AND saf.loadPoint = 'preTest'
			AND saf.isRequired = 1
			AND safr.responseID is null) > 0 THEN 0
	ELSE 1
	END
SELECT @allPostTestCompleted = 
	CASE 
	WHEN (select count(saf.formID)
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblSeminarsAndForms AS saf ON e.seminarID = saf.seminarID
			LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
				INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
				INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
					and f.isPublished = 1
					and getdate() between f.dateStartPublish and f.dateEndPublish	
					and r.passingPct >= f.passingPct				
				on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e.enrollmentID
			WHERE e.enrollmentID = @enrollmentID
			AND saf.loadPoint = 'postTest'
			AND saf.isRequired = 1
			AND safr.responseID is null) > 0 THEN 0
	ELSE 1
	END
SELECT @showSurvey = 
	CASE 
	WHEN (select count(saf.formID)
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID
			INNER JOIN dbo.tblSeminarsAndForms AS saf ON e.seminarID = saf.seminarID
			LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
				INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
				INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
					and f.isPublished = 1
					and getdate() between f.dateStartPublish and f.dateEndPublish	
				on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e.enrollmentID
			WHERE e.enrollmentID = @enrollmentID
			AND saf.loadPoint = 'evaluation'
			AND eswl.attended = 1
			AND (safr.responseID is null)) > 0 THEN 1
	ELSE 0
	END
SELECT @allEvaluationCompleted = 
	CASE 
	WHEN (select count(saf.formID)
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblSeminarsAndForms AS saf ON e.seminarID = saf.seminarID
			LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
				INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
				INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
					and f.isPublished = 1
					and getdate() between f.dateStartPublish and f.dateEndPublish	
				on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e.enrollmentID
			WHERE e.enrollmentID = @enrollmentID
			AND saf.loadPoint = 'evaluation'
			AND (safr.responseID is null or r.dateCompleted is null)) > 0 THEN 0
	ELSE 1
	END
SELECT @hasCertificate = 
	CASE 
	WHEN (SELECT count(e.enrollmentID)
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
			INNER JOIN dbo.tblSeminars AS s ON e.seminarID = s.seminarID 
			INNER JOIN dbo.tblSeminarsSWLive AS sswl ON s.seminarID = sswl.seminarID
			WHERE e.enrollmentID = @enrollmentID 
			AND s.isDeleted = 0
			AND e.isActive = 1
			AND eswl.attended = 1
			AND s.offerCertificate = 1
			AND e.passed = 1
			AND LEN(e.dateCompleted) > 0) > 0 THEN 1
	ELSE 0
	END
SELECT @allowAutoSendOfCertificate = 
	CASE 
	WHEN (SELECT count(sac.seminarCreditID)
				FROM dbo.tblEnrollmentsAndCredit AS eac 
				INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
				INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID 
				INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
				INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID 
				INNER JOIN dbo.tblCreditStatuses AS cstat ON sac.statusID = cstat.statusID
				INNER JOIN dbo.tblCreditAuthoritiesSWLive as caswl on caswl.authorityID = ca.authorityID
				WHERE eac.enrollmentID = @enrollmentID
				and evaluationRequired = 1) > 0 THEN 1
	ELSE 0
	END
SELECT @allPreTestCompleted as allPreTestCompleted, 
	@allPostTestCompleted as allPostTestCompleted,
	@allEvaluationCompleted as allEvaluationCompleted,
	@hasCertificate as hasCertificate,
	@showSurvey as showSurvey,
	@allowAutoSendOfCertificate as allowAutoSendOfCertificate


