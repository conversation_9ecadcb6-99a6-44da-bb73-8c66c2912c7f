use membercentral
GO
ALTER TABLE dbo.sub_Types ADD feEmailNotification varchar(400) NULL
GO
ALTER PROC [dbo].[sub_createSubscriptionType] 
	@orgID int, 
	@siteid int,
	@typeName varchar(100),
	@feRawContent varchar(max),
	@feCompletedRawContent varchar(max),
	@feEmailNotification varchar(max),
	@subTypeID INT OUTPUT
AS 

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	DECLARE @rc INT
	DECLARE @subAdminResourceTypeID INT, @siteResourceStatusID INT, @appCreatedContentResourceTypeID INT, @languageID INT, @contentID INT, @contentSRID INT,  @completedContentID INT, @completedContentSRID INT

	select @languageID = defaultLanguageID
		from dbo.sites
		where siteID = @siteID

	select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')
	select @siteResourceStatusID = dbo.fn_getResourceStatusId('Active')
	select @subAdminResourceTypeID = dbo.fn_getSiteResourceIDForResourceType('SubscriptionAdmin',@siteid)

	-- insert into types
	INSERT INTO dbo.sub_Types (typeName, siteID, feEmailNotification)
	VALUES(@typeName, @siteid, @feEmailNotification)
		SELECT @subTypeID = SCOPE_IDENTITY()

	-- Add content object
	exec dbo.cms_createContentObject @siteid, @appCreatedContentResourceTypeID, @subAdminResourceTypeID, @siteResourceStatusID, 
									 0, 1, @languageID, 1, '', '', @feRawContent, 
									 @contentID OUTPUT, @contentSRID OUTPUT

	exec dbo.cms_createContentObject @siteid, @appCreatedContentResourceTypeID, @subAdminResourceTypeID, @siteResourceStatusID, 
									 0, 1, @languageID, 1, '', '', @feCompletedRawContent, 
									 @completedContentID OUTPUT, @completedContentSRID OUTPUT

	update dbo.sub_Types 
	set frontEndContentID = @contentID, 
		frontEndCompletedContentID = @completedContentID
	where typeID = @subTypeID

	-- create tracking groups
	DECLARE @groupCode varchar(30), @groupName varchar(115), @groupDesc varchar(200), @subTypeGroupID int, @trashID int
	SELECT @groupCode = 'SubType_' + cast(@subTypeID as varchar(10)) + '_Tracking'
	SELECT @groupName = @typeName + ' (All)'
	SELECT @groupDesc = 'Subscription Type ' + cast(@subTypeID as varchar(10)) + ' Tracking Group'
	EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode=@groupCode, @groupName=@groupName,
		@groupDesc=@groupDesc, @isSystemGroup=0, @allowManualAssignment=1, 
		@parentGroupID=null, @hideOnGroupLists=1, @groupID=@subTypeGroupID OUTPUT
		IF @subTypeGroupID = 0 RAISERROR('Unable to create group %s with groupcode %s',16,1,@groupName,@groupCode)

	SELECT @groupCode = 'Active_' + cast(@subTypeID as varchar(10)) + '_Tracking'
	SELECT @groupName = @typeName + ' (Active)'
	SELECT @groupDesc = 'Subscription Type ' + cast(@subTypeID as varchar(10)) + ' Active Tracking Group'
	EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode=@groupCode, @groupName=@groupName,
		@groupDesc=@groupDesc, @isSystemGroup=0, @allowManualAssignment=1, 
		@parentGroupID=@subTypeGroupID, @hideOnGroupLists=1, @groupID=@trashID OUTPUT
		IF @trashID = 0 RAISERROR('Unable to create group %s with groupcode %s',16,1,@groupName,@groupCode)

	SELECT @groupCode = 'WaitingPay_' + cast(@subTypeID as varchar(10)) + '_Tracking'
	SELECT @groupName = @typeName + ' (Not Activated)'
	SELECT @groupDesc = 'Subscription Type ' + cast(@subTypeID as varchar(10)) + ' Not Activated Tracking Group'
	EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode=@groupCode, @groupName=@groupName,
		@groupDesc=@groupDesc, @isSystemGroup=0, @allowManualAssignment=1, 
		@parentGroupID=@subTypeGroupID, @hideOnGroupLists=1, @groupID=@trashID OUTPUT
		IF @trashID = 0 RAISERROR('Unable to create group %s with groupcode %s',16,1,@groupName,@groupCode)

	SELECT @groupCode = 'Inactive_' + cast(@subTypeID as varchar(10)) + '_Tracking'
	SELECT @groupName = @typeName + ' (Inactive)'
	SELECT @groupDesc = 'Subscription Type ' + cast(@subTypeID as varchar(10)) + ' Inactive Tracking Group'
	EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode=@groupCode, @groupName=@groupName,
		@groupDesc=@groupDesc, @isSystemGroup=0, @allowManualAssignment=1, 
		@parentGroupID=@subTypeGroupID, @hideOnGroupLists=1, @groupID=@trashID OUTPUT
		IF @trashID = 0 RAISERROR('Unable to create group %s with groupcode %s',16,1,@groupName,@groupCode)

	SELECT @groupCode = 'Renewable_' + cast(@subTypeID as varchar(10)) + '_Tracking'
	SELECT @groupName = @typeName + ' (Renewable)'
	SELECT @groupDesc = 'Subscription Type ' + cast(@subTypeID as varchar(10)) + ' Renewable Tracking Group'
	EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode=@groupCode, @groupName=@groupName,
		@groupDesc=@groupDesc, @isSystemGroup=0, @allowManualAssignment=1, 
		@parentGroupID=@subTypeGroupID, @hideOnGroupLists=1, @groupID=@trashID OUTPUT
		IF @trashID = 0 RAISERROR('Unable to create group %s with groupcode %s',16,1,@groupName,@groupCode)

	SELECT @groupCode = 'Pending_' + cast(@subTypeID as varchar(10)) + '_Tracking'
	SELECT @groupName = @typeName + ' (Accepted)'
	SELECT @groupDesc = 'Subscription Type ' + cast(@subTypeID as varchar(10)) + ' Pending Tracking Group'
	EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode=@groupCode, @groupName=@groupName,
		@groupDesc=@groupDesc, @isSystemGroup=0, @allowManualAssignment=1, 
		@parentGroupID=@subTypeGroupID, @hideOnGroupLists=1, @groupID=@trashID OUTPUT
		IF @trashID = 0 RAISERROR('Unable to create group %s with groupcode %s',16,1,@groupName,@groupCode)

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	SELECT @subTypeID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[sub_exportSetup]
@siteID int,
@fileName varchar(400)

AS

declare @orgID int
select @orgID = orgID from dbo.sites where siteID = @siteID

IF OBJECT_ID('tempdb..#SubSetup') IS NOT NULL 
	DROP TABLE #SubSetup

; with allGLS as (
	select gl.GlAccountID, gl.uid, gl.thePathExpanded, gl.accountCode, gl.status, ip.profileName, ip.enableAutoPay
	from dbo.fn_getRecursiveGLAccounts(@orgID) as gl
	inner join dbo.tr_invoiceProfiles as ip on ip.profileID = gl.invoiceProfileID
	where gl.AccountType = 'Revenue'
)
select t.uid as [Subscription Type UID], t.typeName as [Subscription Type], t.feEmailNotification as feEmailNotification,
	fec.rawcontent as [Subscription Type Front End Content], fecc.rawcontent as [Subscription Type Front End Completed Content],

	s.uid as [Subscription UID], s.subscriptionName as [Subscription Name], 
	case s.status when 'A' then 'Active' when 'I' then 'Inactive' when 'D' then 'Deleted' end as [Subscription Status],
	case s.autoExpire when 1 then 'Yes' else 'No' end as [Subscription Auto Expire],
	case s.soldSeparately when 1 then 'Yes' else 'No' end as [Subscription Sold Separately],
	case s.rateTermDateFlag 
		when 'A' then 'Adhere to term dates in rate' 
		when 'S' then 'Use current day as term start date (adhere to term end date in rate)'
		when 'C' then 'Calculate term end date for term length (term start date is current day)'
		end as [Subscription Term Dates],
	s.comments as [Subscription Comments],
	case s.isFeatured when 1 then 'Yes' else 'No' end as [Subscription Featured],
	s.paymentOrder as [Subscription Payment Order],
	s.reportCode as [Subscription Report Code],
	ao.subActivationName as [Subscription Activation Option],
	aoAlt.subActivationName as [Subscription Alternate Activation Option],
	case s.allowRateGLAccountOverride when 1 then 'Yes' else 'No' end as [Subscription Allow Rate GL Account Override],
	et.templateName as [Subscription Welcome Email Template],
	etRenew.templateName as [Subscription Renew Email Template],
	sfec.rawcontent as [Subscription Front End Content], sfecc.rawcontent as [Subscription Front End Completed Content],

	glSub.uid as [Subscription GL Account UID], 
	glSub.thePathExpanded + isnull(' (' + nullIf(glSub.AccountCode,'') + ')','') as [Subscription GL Account],
	case glSub.status when 'A' then 'Active' when 'I' then 'Inactive' when 'D' then 'Deleted' end as [Subscription GL Account Status],
	glSub.profileName as [Subscription GL Account Invoice Profile],
	case glSub.enableAutoPay when 1 then 'Yes' else 'No' end as [Subscription GL Account Invoice Profile AutoPay Enabled],

	rs.uid as [Subscription Rate Schedule UID],
	rs.scheduleName as [Subscription Rate Schedule Name],
	case rs.status when 'A' then 'Active' when 'I' then 'Inactive' when 'D' then 'Deleted' end as [Subscription Rate Schedule Status],

	r.uid as [Rate UID], r.rateName as [Rate Name],
	case r.isRenewalRate when 0 then 'Join Rate' else 'Renewal Rate' end as [Rate Type], 
	case r.status when 'A' then 'Active' when 'I' then 'Inactive' when 'D' then 'Deleted' end as [Rate Status],
	r.reportCode as [Rate Report Code],
	r.rateStartDate as [Rate Available Start Date], r.rateEndDate as [Rate Available End Date], 
	r.termStartDate as [Rate Term Start Date], r.termEndDate as [Rate Term End Date], 
	r.graceEndDate as [Rate Grace End Date], 
	case r.forceUpfront when 1 then 'Yes' else 'No' end as [Rate Must Pay on 1st invoice],
	case r.frontEndAllowChangePrice when 1 then 'Yes' else 'No' end as [Rate User can change price],
	case r.keepChangedPriceOnRenewal when 1 then 'Yes' else 'No' end as [Rate Renewal Keeps changed price],
	r.frontEndChangePriceMin as [Rate Change price Min], r.frontEndChangePriceMax as [Rate Change price Max], 
	rFall.uid as [Rate Preferred Fallback Renewal Rate UID],
	rFall.rateName as [Rate Preferred Fallback Renewal Rate Name],

	glRate.uid as [Rate GL Account UID], 
	glRate.thePathExpanded + isnull(' (' + nullIf(glRate.AccountCode,'') + ')','') as [Rate GL Account],
	case glRate.status when 'A' then 'Active' when 'I' then 'Inactive' when 'D' then 'Deleted' end as [Rate GL Account Status],
	glRate.profileName as [Rate GL Account Invoice Profile],
	case glRate.enableAutoPay when 1 then 'Yes' else 'No' end as [Rate GL Account Invoice Profile AutoPay Enabled],

	f.uid as [Rate Frequency UID], f.frequencyName as [Rate Frequency Name], 
	rf.rateAmt as [Rate Frequency Amount], 
	case rf.status when 'A' then 'Active' when 'I' then 'Inactive' when 'D' then 'Deleted' end as [Rate Frequency Status],
	case rf.allowFrontEnd when 1 then 'Yes' else 'No' end as [Rate Frequency Allow on Front End],
	mp.profileName as [Rate Frequency Payment Profile Name]

into #SubSetup
from dbo.sub_types as t
outer apply dbo.fn_getContent(t.frontEndContentID,1) as fec
outer apply dbo.fn_getContent(t.frontEndCompletedContentID,1) as fecc
inner join dbo.sub_subscriptions as s on s.typeID = t.typeID
left outer join allGLS as glSub on glSub.glAccountID = s.GLAccountID
inner join dbo.sub_rateSchedules as rs on rs.scheduleID = s.scheduleID
inner join dbo.sub_activationOptions as ao on ao.subActivationID = s.subActivationID
inner join dbo.sub_activationOptions as aoAlt on aoAlt.subActivationID = s.subAlternateActivationID
left outer join dbo.et_emailTemplates as et on et.templateID = s.emailTemplateID
left outer join dbo.et_emailTemplates as etRenew on etRenew.templateID = s.renewEmailTemplateID
outer apply dbo.fn_getContent(s.frontEndContentID,1) as sfec
outer apply dbo.fn_getContent(s.frontEndCompletedContentID,1) as sfecc
left outer join dbo.sub_rates as r on r.scheduleID = rs.scheduleID
left outer join allGLS as glRate on glRate.glAccountID = r.GLAccountID
left outer join dbo.sub_rates as rFall on rFall.rateID = r.fallbackRenewalRateID
left outer join dbo.sub_rateFrequencies as rf 
	inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID and rf.status = 'A'
	on rf.rateID = r.rateID
left outer join dbo.sub_rateFrequenciesMerchantProfiles as rfmp 
	inner join dbo.mp_profiles as mp on mp.profileID = rfmp.profileID and mp.status = 'A'
	on rfmp.rfid = rf.rfid and rfmp.status = 'A'
where t.siteID = @siteID
and t.status = 'A'
and s.status <> 'D'

-- sql for export
DECLARE @fullsql varchar(max)
SELECT @fullsql = '
	select *
	from #SubSetup
	order by [Subscription Type], [Subscription Name], [Rate Name], [Rate Frequency Name], [Rate Frequency Payment Profile Name]'

-- export
EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql=@fullsql

IF OBJECT_ID('tempdb..#SubSetup') IS NOT NULL 
	DROP TABLE #SubSetup

GO

