/* To prevent any potential data loss issues, you should review this script in detail before running it outside the context of the database designer.*/

US<PERSON> customApps
GO

BEGIN TRANSACTION
SET QUOTED_IDENTIFIER ON
SET ARITHABORT ON
SET NUMERIC_ROUNDABORT OFF
SET CONCAT_NULL_YIELDS_NULL ON
SET ANSI_NULLS ON
SET ANSI_PADDING ON
SET ANSI_WARNINGS ON
COMMIT
BEGIN TRANSACTION
GO
ALTER TABLE dbo.CA_Nominations
	DROP CONSTRAINT DF_CA_12Nominations_hasphoto
GO
ALTER TABLE dbo.CA_Nominations
	DROP CONSTRAINT DF_CA_12Nominations_emailPhoto
GO
CREATE TABLE dbo.Tmp_CA_Nominations
	(
	responseID int NOT NULL IDENTITY (1, 1),
	memberID int NOT NULL,
	candidateName varchar(200) NOT NULL,
	firmname varchar(400) NOT NULL,
	city varchar(400) NOT NULL,
	district varchar(200) NOT NULL,
	office varchar(100) NOT NULL,
	paytype varchar(10) NOT NULL,
	bio varchar(MAX) NULL,
	officeHeld varchar(400) NULL,
	boardattend varchar(100) NULL,
	membership varchar(20) NULL,
	legislation varchar(10) NULL,
	education varchar(10) NULL,
	forum varchar(10) NULL,
	presclub varchar(10) NULL,
	advocates varchar(10) NULL,
	sponsor varchar(50) NULL,
	advocatesNames varchar(200) NULL,
	othercom varchar(10) NULL,
	othercomList varchar(MAX) NULL,
	misc varchar(MAX) NULL,
	hasphoto bit NOT NULL,
	emailPhoto bit NOT NULL,
	electionYear int NOT NULL,
	dateAdded datetime NULL
	)  ON [PRIMARY]
	 TEXTIMAGE_ON [PRIMARY]
GO
ALTER TABLE dbo.Tmp_CA_Nominations ADD CONSTRAINT
	DF_CA_12Nominations_hasphoto DEFAULT ((0)) FOR hasphoto
GO
ALTER TABLE dbo.Tmp_CA_Nominations ADD CONSTRAINT
	DF_CA_12Nominations_emailPhoto DEFAULT ((0)) FOR emailPhoto
GO
SET IDENTITY_INSERT dbo.Tmp_CA_Nominations ON
GO
IF EXISTS(SELECT * FROM dbo.CA_Nominations)
	 EXEC('INSERT INTO dbo.Tmp_CA_Nominations (responseID, memberID, candidateName, firmname, city, district, office, paytype, bio, officeHeld, boardattend, membership, legislation, education, forum, presclub, advocates, advocatesNames, othercom, othercomList, misc, hasphoto, emailPhoto, electionYear, dateAdded)
		SELECT responseID, memberID, candidateName, firmname, city, district, office, paytype, bio, officeHeld, boardattend, membership, legislation, education, forum, presclub, advocates, advocatesNames, othercom, othercomList, misc, hasphoto, emailPhoto, electionYear, dateAdded FROM dbo.CA_Nominations WITH (HOLDLOCK TABLOCKX)')
GO
SET IDENTITY_INSERT dbo.Tmp_CA_Nominations OFF
GO
DROP TABLE dbo.CA_Nominations
GO
EXECUTE sp_rename N'dbo.Tmp_CA_Nominations', N'CA_Nominations', 'OBJECT' 
GO
ALTER TABLE dbo.CA_Nominations ADD CONSTRAINT
	PK_CA_12Nominations PRIMARY KEY CLUSTERED 
	(
	responseID
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

GO
COMMIT