use membercentral
GO

ALTER PROC [dbo].[ams_saveMemberEmail]
@memberID int,
@emailTypeID int,
@email varchar(255),
@byPassQueue bit = 0

as

-- ensure valid formed
declare @regex varchar(max)
set @regex = '^[a-zA-Z_0-9-''''\&\+~]+(\.[a-zA-Z_0-9-''''\&\+~]+)*@([a-zA-Z_0-9-]+\.)+[a-zA-Z]{2,7}$'
if len(@email) > 0 and dbo.fn_RegExReplace(@email,@regex,'') <> ''
	set @email = ''

if len(@email) > 0 BEGIN
	if exists (select emailID from dbo.ams_memberEmails where memberID = @memberID and emailTypeID = @emailTypeID) begin
		update dbo.ams_memberEmails
		set email = @email
		where memberID = @memberID 
		and emailTypeID = @emailTypeID
	end else begin
		insert into dbo.ams_memberEmails (memberID, emailTypeID, email)
		values (@memberID, @emailTypeID, @email)
	end
end else begin
	delete from dbo.ams_memberEmails
	where memberID = @memberID 
	and emailTypeID = @emailTypeID
end

-- set member as updated
update dbo.ams_members
set dateLastUpdated = getdate()
where memberID = @memberID

IF @byPassQueue = 0 BEGIN
	-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
	declare @itemGroupUID uniqueidentifier, @orgID int, @conditionIDList varchar(max)
	SELECT @orgID = orgID from dbo.ams_members where memberID = @memberID	
	SELECT @conditionIDList = COALESCE(@conditionIDList + ',', '') + cast(c.conditionID as varchar(10)) 
		from dbo.ams_virtualGroupConditions as c
		where c.orgID = @orgID
		and c.fieldcode = 'me_' + cast(@emailTypeID as varchar(10)) + '_email'
		group by c.conditionID
	IF @conditionIDList is not null
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberID, @conditionIDList=@conditionIDList, @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT
END

return 0
GO

DELETE FROM ams_memberEmails
WHERE len(email) > 0 and dbo.fn_RegExReplace(email,'^[a-zA-Z_0-9-''''\&\+~]+(\.[a-zA-Z_0-9-''''\&\+~]+)*@([a-zA-Z_0-9-]+\.)+[a-zA-Z]{2,7}$','') <> ''
GO

