use membercentral
GO
-- WA-35 Fileshare1 Migration to Fileshare2 - Round 2

/* ***************************** */
/* move 50 "Expert" files from EAGLE DOCUMENT AND INFORMATION EXCHANGE fs1 to Expert Documents fs2 */
/* ***************************** */
declare @tblexpert table (documentID int, expert varchar(200), Cat varchar(200), subcat varchar(200), expertCatID int, docCatID int)
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674715, '<PERSON><PERSON>, <PERSON>O, <PERSON>', 'C.V.', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (670062, '<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>', 'Discovery Deposition', 'called by plaintiff')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (672497, '<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>', 'C.V.', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (672502, '<PERSON>, <PERSON>, <PERSON>', 'C.V.', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (672505, 'Austin, DC, Dennis', 'C.V.', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (675246, 'Probst, Bradley', 'Reports', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (675247, 'Probst, Bradley', 'Reports', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (675248, 'Probst, Bradley', 'Reports', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (672133, 'Blue, MD, James', 'Discovery Deposition', 'called by plaintiff')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674814, 'Bauer, MD, R. David', 'Discovery Deposition', 'called by defense')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (672495, 'Nussbaum, MD, Eliezer', 'C.V.', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (672498, 'Laux, PhD, Lila', 'C.V.', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (677304, 'Frost, ACF, James', 'C.V.', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (672568, 'Hogan, DC, Michael', 'C.V.', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674251, 'Jackson, MD, Allen', 'C.V.', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674252, 'Jackson, MD, Allen', 'billing rates', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674438, 'Sutton, DC, Mark', 'C.V.', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674439, 'Sutton, DC, Mark', 'Reports', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674441, 'Manoso, MD, Mark', 'C.V.', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674442, 'Rivera, DC, Richard', 'C.V.', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674452, 'Nicholes, DC, David', 'Reports', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674475, 'Rivera, DC, Richard', 'Reports', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674476, 'Nicholes, DC, David', 'Reports', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674477, 'Brooke, MD, Marvin', 'C.V.', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674478, 'Austin, DC, Dennis', 'C.V.', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674753, 'Wray, MD, Linda', 'C.V.', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674754, 'Wray, MD, Linda', 'Miscellaneous', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674755, 'Wray, MD, Linda', 'Discovery Deposition', 'called by plaintiff')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674756, 'Wray, MD, Linda', 'Discovery Deposition', 'called by plaintiff')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674757, 'Wray, MD, Linda', 'Discovery Deposition', 'called by plaintiff')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674758, 'Wray, MD, Linda', 'Discovery Deposition', 'called by plaintiff')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674759, 'Wray, MD, Linda', 'Trial Testimony/Perpetuation Deposition', 'called by defense')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674760, 'Wray, MD, Linda', 'Trial Testimony/Perpetuation Deposition', 'called by defense')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674761, 'Wray, MD, Linda', 'Trial Testimony/Perpetuation Deposition', 'called by defense')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674762, 'Wray, MD, Linda', 'Discovery Deposition', 'called by defense')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674763, 'Wray, MD, Linda', 'Discovery Deposition', 'called by defense')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674764, 'Wray, MD, Linda', 'Trial Testimony/Perpetuation Deposition', 'called by defense')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674765, 'Wray, MD, Linda', 'Trial Testimony/Perpetuation Deposition', 'called by defense')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674766, 'Wray, MD, Linda', 'Discovery Deposition', 'called by plaintiff')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674712, 'Bays, DO, Patrick', 'Miscellaneous', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674711, 'Bays, DO, Patrick', 'Tax Returns/Compensation Info', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674713, 'Bays, DO, Patrick', 'Tax Returns/Compensation Info', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (670063, 'Tencer, MD, Allen F.', 'Motions/Court Orders', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (670065, 'Tencer, MD, Allen F.', 'Motions/Court Orders', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (670071, 'Tencer, MD, Allen F.', 'Motions/Court Orders', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (670072, 'Tencer, MD, Allen F.', 'Motions/Court Orders', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (670073, 'Tencer, MD, Allen F.', 'Motions/Court Orders', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (670451, 'Tencer, MD, Allen F.', 'Motions/Court Orders', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (672499, 'Tencer, MD, Allen F.', 'Motions/Court Orders', '')
INSERT INTO @tblexpert (documentID, expert, cat, subcat) values (674808, 'Bays, DO, Patrick', 'Trial Testimony/Perpetuation Deposition', 'called by defense')

-- fetch expert categoryID 
update tmp
set tmp.expertCatID = c.categoryID
from @tblexpert as tmp
inner join dbo.cms_categories as c on c.categoryTreeID = 894 and c.categoryName = tmp.expert and c.isActive = 1

-- fetch docType categoryID 
update tmp
set tmp.docCatID = subC.categoryID
from @tblexpert as tmp
inner join dbo.cms_categories as c on c.categoryTreeID = 895 and c.categoryName = tmp.cat and c.isActive = 1
inner join dbo.cms_categories as subC on subC.categoryTreeID = c.categoryTreeID and subC.categoryName = tmp.subcat and subC.isActive = 1 and subC.parentCategoryID = c.categoryID
where tmp.cat <> '' and tmp.subcat <> ''

update tmp
set tmp.docCatID = c.categoryID
from @tblexpert as tmp
inner join dbo.cms_categories as c on c.categoryTreeID = 895 and c.categoryName = tmp.cat and c.isActive = 1
where tmp.cat <> '' and tmp.subcat = ''
and tmp.docCatID is null

-- set section to root of fileshare
update d 
set d.sectionID = 5726 
from dbo.cms_documents as d 
inner join @tblexpert as tmp on tmp.documentID = d.documentID

-- link to categories
insert into dbo.cms_categorySiteResources (categoryID, siteResourceID)
select tmp.expertCatID, d.siteResourceID
from @tblexpert as tmp
inner join dbo.cms_documents as d on d.documentID = tmp.documentID
where tmp.expertCatID is not null
	union
select tmp.docCatID, d.siteResourceID
from @tblexpert as tmp
inner join dbo.cms_documents as d on d.documentID = tmp.documentID
where tmp.docCatID is not null
GO

/* ***************************** */
/* move 1 "WSAJ Published Documents" files from EAGLE DOCUMENT AND INFORMATION EXCHANGE fs1 to WSAJ Published Documents fs2 */
/* ***************************** */
declare @tbldocs table (documentID int, Cat varchar(200), docCatID int)
INSERT INTO @tbldocs (documentID, cat) values (677530, 'WSAJ Employment Law Deskbook')

-- fetch doc categoryID 
update tmp
set tmp.docCatID = c.categoryID
from @tbldocs as tmp
inner join dbo.cms_categories as c on c.categoryTreeID = 896 and c.categoryName = tmp.cat and c.isActive = 1

-- set section to root of fileshare
update d 
set d.sectionID = 5727 
from dbo.cms_documents as d 
inner join @tbldocs as tmp on tmp.documentID = d.documentID

-- link to categories
insert into dbo.cms_categorySiteResources (categoryID, siteResourceID)
select tmp.docCatID, d.siteResourceID
from @tbldocs as tmp
inner join dbo.cms_documents as d on d.documentID = tmp.documentID
where tmp.docCatID is not null
GO

/* ***************************** */
/* move 132 "Blank" files from EAGLE DOCUMENT AND INFORMATION EXCHANGE fs1 to Eagle Exchange fs2 */
/* ***************************** */
declare @tbldocs table (documentID int, Cat varchar(200), docCatID int)
INSERT INTO @tbldocs (documentID, cat) values (676416, 'Anti-SLAPP Motions')
INSERT INTO @tbldocs (documentID, cat) values (676417, 'Anti-SLAPP Motions')
INSERT INTO @tbldocs (documentID, cat) values (676418, 'Anti-SLAPP Motions')
INSERT INTO @tbldocs (documentID, cat) values (676419, 'Anti-SLAPP Motions')
INSERT INTO @tbldocs (documentID, cat) values (676420, 'Anti-SLAPP Motions')
INSERT INTO @tbldocs (documentID, cat) values (676421, 'Anti-SLAPP Motions')
INSERT INTO @tbldocs (documentID, cat) values (676422, 'Anti-SLAPP Motions')
INSERT INTO @tbldocs (documentID, cat) values (670350, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (670692, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (670693, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (671901, 'Trial Brief')
INSERT INTO @tbldocs (documentID, cat) values (671902, 'Trial Brief')
INSERT INTO @tbldocs (documentID, cat) values (672178, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (672179, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (672492, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (672493, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (673007, 'briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (673795, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (673796, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (673916, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (673921, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (673923, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (673928, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (674190, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (674203, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (674338, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (674339, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (674342, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (674343, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (674522, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (674524, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (674534, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (674535, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (674536, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (674913, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (674917, 'Depositions')
INSERT INTO @tbldocs (documentID, cat) values (675326, 'briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (675335, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (675337, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (675338, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (675342, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (676245, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (676249, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (676256, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (676257, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (676259, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (676260, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (676266, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (676268, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (676337, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (676348, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (676349, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (676350, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (676351, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (676364, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (676977, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (676979, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (677276, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (677277, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (677279, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (677280, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (677283, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (677293, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (677295, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (677296, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (677299, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (677300, 'briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (677303, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (677306, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (677308, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (677309, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (677313, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (677607, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (677614, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (673794, 'correspondence')
INSERT INTO @tbldocs (documentID, cat) values (674205, 'correspondence')
INSERT INTO @tbldocs (documentID, cat) values (674206, 'correspondence')
INSERT INTO @tbldocs (documentID, cat) values (674751, 'correspondence')
INSERT INTO @tbldocs (documentID, cat) values (674752, 'correspondence')
INSERT INTO @tbldocs (documentID, cat) values (674911, 'correspondence')
INSERT INTO @tbldocs (documentID, cat) values (674915, 'correspondence')
INSERT INTO @tbldocs (documentID, cat) values (674918, 'correspondence')
INSERT INTO @tbldocs (documentID, cat) values (675327, 'correspondence')
INSERT INTO @tbldocs (documentID, cat) values (675332, 'correspondence')
INSERT INTO @tbldocs (documentID, cat) values (676406, 'correspondence')
INSERT INTO @tbldocs (documentID, cat) values (677294, 'correspondence')
INSERT INTO @tbldocs (documentID, cat) values (670166, 'court orders')
INSERT INTO @tbldocs (documentID, cat) values (672500, 'court orders')
INSERT INTO @tbldocs (documentID, cat) values (673929, 'court orders')
INSERT INTO @tbldocs (documentID, cat) values (674521, 'court orders')
INSERT INTO @tbldocs (documentID, cat) values (674523, 'court orders')
INSERT INTO @tbldocs (documentID, cat) values (674921, 'court orders')
INSERT INTO @tbldocs (documentID, cat) values (677297, 'court orders')
INSERT INTO @tbldocs (documentID, cat) values (677609, 'court orders')
INSERT INTO @tbldocs (documentID, cat) values (670181, 'CR 35 documents')
INSERT INTO @tbldocs (documentID, cat) values (670184, 'CR 35 documents')
INSERT INTO @tbldocs (documentID, cat) values (672496, 'CR 35 documents')
INSERT INTO @tbldocs (documentID, cat) values (674208, 'CR 35 documents')
INSERT INTO @tbldocs (documentID, cat) values (674919, 'CR 35 documents')
INSERT INTO @tbldocs (documentID, cat) values (674920, 'CR 35 documents')
INSERT INTO @tbldocs (documentID, cat) values (675331, 'CR 35 documents')
INSERT INTO @tbldocs (documentID, cat) values (675333, 'CR 35 documents')
INSERT INTO @tbldocs (documentID, cat) values (677164, 'CR 35 documents')
INSERT INTO @tbldocs (documentID, cat) values (677165, 'CR 35 documents')
INSERT INTO @tbldocs (documentID, cat) values (677166, 'CR 35 documents')
INSERT INTO @tbldocs (documentID, cat) values (677278, 'CR 35 documents')
INSERT INTO @tbldocs (documentID, cat) values (670064, 'Depositions')
INSERT INTO @tbldocs (documentID, cat) values (670806, 'Depositions')
INSERT INTO @tbldocs (documentID, cat) values (672640, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (672641, 'Briefs/Pleadings')
INSERT INTO @tbldocs (documentID, cat) values (674061, 'discovery')
INSERT INTO @tbldocs (documentID, cat) values (674340, 'Depositions')
INSERT INTO @tbldocs (documentID, cat) values (677301, 'discovery')
INSERT INTO @tbldocs (documentID, cat) values (677302, 'discovery')
INSERT INTO @tbldocs (documentID, cat) values (677081, 'experts')
INSERT INTO @tbldocs (documentID, cat) values (674862, 'Insurance Fair Conduct Act')
INSERT INTO @tbldocs (documentID, cat) values (677305, 'Insurance Fair Conduct Act')
INSERT INTO @tbldocs (documentID, cat) values (677307, 'Insurance Fair Conduct Act')
INSERT INTO @tbldocs (documentID, cat) values (672494, 'Jury Instructions')
INSERT INTO @tbldocs (documentID, cat) values (675336, 'Jury Instructions')
INSERT INTO @tbldocs (documentID, cat) values (676244, 'Jury Instructions')
INSERT INTO @tbldocs (documentID, cat) values (672489, 'miscellaneous')
INSERT INTO @tbldocs (documentID, cat) values (672490, 'miscellaneous')
INSERT INTO @tbldocs (documentID, cat) values (672491, 'miscellaneous')
INSERT INTO @tbldocs (documentID, cat) values (674207, 'HiTech')
INSERT INTO @tbldocs (documentID, cat) values (674916, 'HiTech')
INSERT INTO @tbldocs (documentID, cat) values (675334, 'miscellaneous')
INSERT INTO @tbldocs (documentID, cat) values (676258, 'miscellaneous')
INSERT INTO @tbldocs (documentID, cat) values (677275, 'miscellaneous')
INSERT INTO @tbldocs (documentID, cat) values (677285, 'miscellaneous')
INSERT INTO @tbldocs (documentID, cat) values (677312, 'miscellaneous')
INSERT INTO @tbldocs (documentID, cat) values (676981, 'Pleadings & Motions CD')


-- fetch doc categoryID 
update tmp
set tmp.docCatID = c.categoryID
from @tbldocs as tmp
inner join dbo.cms_categories as c on c.categoryTreeID = 897 and c.categoryName = tmp.cat and c.isActive = 1

-- set section to root of fileshare
update d 
set d.sectionID = 5728 
from dbo.cms_documents as d 
inner join @tbldocs as tmp on tmp.documentID = d.documentID

-- link to categories
insert into dbo.cms_categorySiteResources (categoryID, siteResourceID)
select tmp.docCatID, d.siteResourceID
from @tbldocs as tmp
inner join dbo.cms_documents as d on d.documentID = tmp.documentID
where tmp.docCatID is not null
GO




