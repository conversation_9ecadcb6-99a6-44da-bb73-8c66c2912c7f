USE [memberCentral]
GO

SET NOCOUNT ON

IF  EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME ='FK_ad_zoneTypes_ad_zones')
BEGIN
	print 'Relationship FK_ad_zoneTypes_ad_zones Exists! Deleting...'
	ALTER TABLE [ad_zones] DROP CONSTRAINT [FK_ad_zoneTypes_ad_zones]
END
GO

IF  EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME ='FK_ad_advertiserZones_ad_zones')
BEGIN
	print 'Relationship FK_ad_advertiserZones_ad_zones Exists! Deleting...'
	ALTER TABLE [ad_advertiserZones] DROP CONSTRAINT [FK_ad_advertiserZones_ad_zones]
END
GO

IF  EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME ='FK_ad_advertiserZones_ad_advertisers')
BEGIN
	print 'Relationship FK_ad_advertiserZones_ad_advertisers Exists! Deleting...'
	ALTER TABLE [ad_advertiserZones] DROP CONSTRAINT [FK_ad_advertiserZones_ad_advertisers]
END
GO

IF  EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME ='FK_ad_adPlacements_ad_ads')
BEGIN
	print 'Relationship FK_ad_adPlacements_ad_ads Exists! Deleting...'
	ALTER TABLE [ad_adPlacements] DROP CONSTRAINT [FK_ad_adPlacements_ad_ads]
END
GO

IF  EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME ='FK_ad_ads_ad_tyes')
BEGIN
	print 'Relationship FK_ad_ads_ad_tyes Exists! Deleting...'
	ALTER TABLE [ad_ads] DROP CONSTRAINT [FK_ad_ads_ad_tyes]
END
GO

IF  EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME ='FK_ad_ads_ad_advertisers')
BEGIN
	print 'Relationship FK_ad_ads_ad_advertisers Exists! Deleting...'
	ALTER TABLE [ad_ads] DROP CONSTRAINT [FK_ad_ads_ad_advertisers]
END
GO

IF  EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME ='FK_ad_zoneTypes_ad_zoneAdTypes')
BEGIN
	print 'Relationship FK_ad_zoneTypes_ad_zoneAdTypes Exists! Deleting...'
	ALTER TABLE [ad_zoneAdTypes] DROP CONSTRAINT [FK_ad_zoneTypes_ad_zoneAdTypes]
END
GO

IF  EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME ='FK_ad_types_ad_zoneAdTypes')
BEGIN
	print 'Relationship FK_ad_types_ad_zoneAdTypes Exists! Deleting...'
	ALTER TABLE [ad_zoneAdTypes] DROP CONSTRAINT [FK_ad_types_ad_zoneAdTypes]
END
GO

IF  EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME ='FK_ad_adPlacements_ad_zones')
BEGIN
	print 'Relationship FK_ad_adPlacements_ad_zones Exists! Deleting...'
	ALTER TABLE [ad_adPlacements] DROP CONSTRAINT [FK_ad_adPlacements_ad_zones]
END
GO

IF  EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME ='FK_ad_advertisers_organizations')
BEGIN
	print 'Relationship FK_ad_advertisers_organizations Exists! Deleting...'
	ALTER TABLE [ad_advertisers] DROP CONSTRAINT [FK_ad_advertisers_organizations]
END
GO

print '********************************************'
print 'CREATING TABLE ad_advertisers'

IF OBJECT_ID('ad_advertisers') IS NOT NULL 
	EXEC('DROP TABLE  ad_advertisers')
GO

create table ad_advertisers(
	advertiserID	int IDENTITY(1,1) NOT NULL PRIMARY KEY CLUSTERED,
	name varchar(255) NOT NULL,
	orgID	int NOT NULL,
	googleUACode	varchar(200) NULL,
	[status] char(1) NOT NULL,
	dateCreated datetime NULL
)
GO

print 'TABLE ad_advertisers created'
print '********************************************'

print '********************************************'
print 'CREATING TABLE ad_types'

IF OBJECT_ID('ad_types') IS NOT NULL 
	EXEC('DROP TABLE  ad_types')
GO

create table ad_types(
	adTypeID	int IDENTITY(1,1) NOT NULL PRIMARY KEY CLUSTERED,
	adTypeName varchar(255) NOT NULL,
	supportsText bit NULL,
	supportsImage bit NULL,
	supportContentObject bit NULL,
	ImageWidthMin int NULL,
	ImageWidthMax int NULL,
	ImageHeightMin int NULL,
	ImageHeightMax int NULL
)
GO

print 'TABLE ad_types created'
print '********************************************'

print '********************************************'
print 'CREATING TABLE ad_zoneTypes'

IF OBJECT_ID('ad_zoneTypes') IS NOT NULL 
	EXEC('DROP TABLE  ad_zoneTypes')
GO

create table ad_zoneTypes(
	zoneTypeID	int IDENTITY(1,1) NOT NULL PRIMARY KEY CLUSTERED,
	zoneTypeName varchar(255) NOT NULL
)
GO

print 'TABLE ad_zoneTypes created'
print '********************************************'

print '********************************************'
print 'CREATING TABLE ad_zones'

IF OBJECT_ID('ad_zones') IS NOT NULL 
	EXEC('DROP TABLE  ad_zones')
GO

create table ad_zones(
	zoneID	int IDENTITY(1,1) NOT NULL PRIMARY KEY CLUSTERED,
	zoneName varchar(255) NOT NULL,
	zoneTypeID int NOT NULL,
	siteCode varchar(10) NOT NULL,
	uniqueCode varchar(50) NOT NULL
)
GO

print 'TABLE ad_zones created'
print '********************************************'

print '********************************************'
print 'CREATING TABLE ad_advertiserZones '

IF OBJECT_ID('ad_advertiserZones') IS NOT NULL 
	EXEC('DROP TABLE  ad_advertiserZones')
GO

create table ad_advertiserZones(
	id	int IDENTITY(1,1) NOT NULL PRIMARY KEY CLUSTERED,
	zoneID int NOT NULL,
	advertiserID int NOT NULL
)
GO

print 'TABLE ad_advertiserZones created'
print '********************************************'

print '********************************************'
print 'CREATING TABLE ad_ads'

IF OBJECT_ID('ad_ads') IS NOT NULL 
	EXEC('DROP TABLE  ad_ads')
GO

create table ad_ads(
	adID	int IDENTITY(1,1) NOT NULL PRIMARY KEY CLUSTERED,
	adName varchar(255) NOT NULL,
	advertiserID int NOT NULL,
	adTypeID int NOT NULL,
	imageUrl varchar(1000) NULL,
	adLink varchar(1000) NULL,
	adShortText varchar(100) NULL,
	imageWidth int NULL,
	imageHeight int NULL,
	adContentObjectID int NULL,
	[status] char(1) NOT NULL,
	dateCreated datetime NULL
)
GO

print 'TABLE ad_ads created'
print '********************************************'

print '********************************************'
print 'CREATING TABLE ad_adPlacements'

IF OBJECT_ID('ad_adPlacements') IS NOT NULL 
	EXEC('DROP TABLE  ad_adPlacements')
GO

create table ad_adPlacements(
	adPlacementID	int IDENTITY(1,1) NOT NULL PRIMARY KEY CLUSTERED,
	adID int NOT NULL,
	zoneID int NOT NULL,
	[status] char(1) NOT NULL
)
GO

print 'TABLE ad_adPlacements created'
print '********************************************'

print '********************************************'
print 'CREATING TABLE ad_zoneAdTypes '

IF OBJECT_ID('ad_zoneAdTypes') IS NOT NULL 
	EXEC('DROP TABLE  ad_zoneAdTypes')
GO

create table ad_zoneAdTypes(
	id	int IDENTITY(1,1) NOT NULL PRIMARY KEY CLUSTERED,
	zoneTypeID int NOT NULL,
	adTypeID int NOT NULL
)
GO

print 'TABLE ad_zoneAdTypes created'
print '********************************************'

print 'Creating relationship FK_ad_zoneTypes_ad_zones...'
ALTER TABLE [ad_zones]  WITH NOCHECK ADD  CONSTRAINT [FK_ad_zoneTypes_ad_zones] FOREIGN KEY([zoneTypeID])
REFERENCES [dbo].[ad_zoneTypes] ([zoneTypeID])
GO

print 'Creating relationship FK_ad_advertiserZones_ad_zones...'
ALTER TABLE [ad_advertiserZones]  WITH NOCHECK ADD  CONSTRAINT [FK_ad_advertiserZones_ad_zones] FOREIGN KEY([zoneID])
REFERENCES [dbo].[ad_zones] ([zoneID])
GO

print 'Creating relationship FK_ad_advertiserZones_ad_advertisers...'
ALTER TABLE [ad_advertiserZones]  WITH NOCHECK ADD  CONSTRAINT [FK_ad_advertiserZones_ad_advertisers] FOREIGN KEY([advertiserID])
REFERENCES [dbo].[ad_advertisers] ([advertiserID])
GO

print 'Creating relationship FK_ad_adPlacements_ad_ads...'
ALTER TABLE [ad_adPlacements]  WITH NOCHECK ADD  CONSTRAINT [FK_ad_adPlacements_ad_ads] FOREIGN KEY([adID])
REFERENCES [dbo].[ad_ads] ([adID])
GO

print 'Creating relationship FK_ad_ads_ad_tyes...'
ALTER TABLE [ad_ads]  WITH NOCHECK ADD  CONSTRAINT [FK_ad_ads_ad_tyes] FOREIGN KEY([adTypeID])
REFERENCES [dbo].[ad_types] ([adTypeID])
GO

print 'Creating relationship FK_ad_ads_ad_advertisers...'
ALTER TABLE [ad_ads]  WITH NOCHECK ADD  CONSTRAINT [FK_ad_ads_ad_advertisers] FOREIGN KEY([advertiserID])
REFERENCES [dbo].[ad_advertisers] ([advertiserID])
GO

print 'Creating relationship FK_ad_zoneTypes_ad_zoneAdTypes...'
ALTER TABLE [ad_zoneAdTypes]  WITH NOCHECK ADD  CONSTRAINT [FK_ad_zoneTypes_ad_zoneAdTypes] FOREIGN KEY([zoneTypeID])
REFERENCES [dbo].[ad_zoneTypes] ([zoneTypeID])
GO

print 'Creating relationship FK_ad_types_ad_zoneAdTypes...'
ALTER TABLE [ad_zoneAdTypes]  WITH NOCHECK ADD  CONSTRAINT [FK_ad_types_ad_zoneAdTypes] FOREIGN KEY([adTypeID])
REFERENCES [dbo].[ad_types] ([adTypeID])
GO

print 'Creating relationship FK_ad_adPlacements_ad_zones...'
ALTER TABLE [ad_adPlacements]  WITH NOCHECK ADD  CONSTRAINT [FK_ad_adPlacements_ad_zones] FOREIGN KEY([zoneID])
REFERENCES [dbo].[ad_zones] ([zoneID])
GO

print 'Creating relationship FK_ad_advertisers_organizations...'
ALTER TABLE [ad_advertisers]  WITH NOCHECK ADD  CONSTRAINT [FK_ad_advertisers_organizations] FOREIGN KEY([orgID])
REFERENCES [dbo].[organizations] ([orgID])
GO

SET NOCOUNT OFF	
GO


USE [memberCentral]
GO

-- INSERT zone types
print 'INSERT zone types'

delete from ad_zoneTypes

insert into ad_zoneTypes
values('EClips')

insert into ad_zoneTypes
values('Listserver')

insert into ad_zoneTypes
values('Search')

insert into ad_zoneTypes
values('Listviewer')

insert into ad_zoneTypes
values('Login Page')
GO

-- INSERT zones
print 'INSERT zones'

declare
	@eclipsZoneTypeID int,
	@listserverZoneTypeID int,
	@searchZoneTypeID int,
	@listviewerZoneTypeID int,
	@loginPageZoneTypeID int

select @eclipsZoneTypeID = zoneTypeID from ad_zoneTypes where zoneTypeName = 'EClips'

print @eclipsZoneTypeID

insert into ad_zones (
	zoneName,
	zoneTypeID,
	siteCode,
	uniqueCode	
)
select 
	name as ZoneName, 
	@eclipsZoneTypeID,
	state as sitecode, 
	publicationID as uniquecode
from 
	tlasites.trialsmith.dbo.eclipsPublications
--GO

--declare
--	@listserverZoneTypeID int

select @listserverZoneTypeID = zoneTypeID from ad_zoneTypes where zoneTypeName = 'Listserver'

print @listserverZoneTypeID

insert into ad_zones (
	zoneName,
	zoneTypeID,
	siteCode,
	uniqueCode	
)
select 
	DescShort_ + ' (' + name_ + ')' as ZoneName, 
	@listserverZoneTypeID,
	lf.orgcode as sitecode, 
	name_ as uniquecode
from 
	lyris.trialslyris1.dbo.lists_ l
	inner join lyris.trialslyris1.dbo.lists_format lf on 
		lf.name = l.name_
--GO

--declare
--	@searchZoneTypeID int,
--	@listviewerZoneTypeID int,
--	@loginPageZoneTypeID int

select @searchZoneTypeID = zoneTypeID from ad_zoneTypes where zoneTypeName = 'Search'

print @searchZoneTypeID

select @listviewerZoneTypeID = zoneTypeID from ad_zoneTypes where zoneTypeName = 'Listviewer'

print @listviewerZoneTypeID

select @loginPageZoneTypeID = zoneTypeID from ad_zoneTypes where zoneTypeName = 'Login Page'

print @loginPageZoneTypeID

insert into ad_zones (
	zoneName,
	zoneTypeID,
	siteCode,
	uniqueCode	
)
select 
	at.applicationTypeName as ZoneName, 
	case
		when at.applicationTypeName = 'Search' then @searchZoneTypeID
		when at.applicationTypeName = 'Login' then @loginPageZoneTypeID
		when at.applicationTypeName = 'Listviewer' then @listviewerZoneTypeID
	end as zoneTypeID,
	s.sitecode, 
	at.applicationTypeName as uniquecode
from 
	sites s
	inner join cms_applicationInstances ai on 
		ai.siteID = s.siteID
	inner join cms_applicationTypes at on 
		at.applicationTypeID = ai.applicationTypeID
		and at.applicationTypeName in ('Search','Login','Listviewer')
	inner join cms_siteResources sr on 
		sr.siteResourceID = ai.siteResourceID
	inner join cms_siteResourceStatuses srs on 
		srs.siteResourceStatusID = sr.siteResourceStatusID
		and srs.siteResourceStatusDesc = 'Active'
group by 
	s.sitecode, at.applicationTypeName
--GO

-- INSERT ad types
print 'INSERT ad types'

delete from ad_types

declare
	@eclipsAdTypeID int,
	@listserverAdTypeID int,
	@searchAdTypeID int,
	@listviewerAdTypeID int,
	@loginPageAdTypeID int

insert into ad_types (
	adTypeName,
	supportsText,
	supportsImage,
	supportContentObject,
	ImageWidthMin,
	ImageWidthMax,
	ImageHeightMin,
	ImageHeightMax
)
values(
	'EClips Image Ad',
	1,
	1,
	0,
	120,
	120,
	360,
	360
)

select @eclipsAdTypeID = SCOPE_IDENTITY()

insert into ad_zoneAdTypes (
	zoneTypeID,
	adTypeID
)
values(
	@eclipsZoneTypeID,
	@eclipsAdTypeID
)

insert into ad_types (
	adTypeName,
	supportsText,
	supportsImage,
	supportContentObject,
	ImageWidthMin,
	ImageWidthMax,
	ImageHeightMin,
	ImageHeightMax
)
values(
	'Listserver Text Ad',
	1,
	0,
	0,
	NULL,
	NULL,
	NULL,
	NULL
)

select @listserverAdTypeID = SCOPE_IDENTITY()

insert into ad_zoneAdTypes (
	zoneTypeID,
	adTypeID
)
values(
	@listserverZoneTypeID,
	@listserverAdTypeID
)

insert into ad_types (
	adTypeName,
	supportsText,
	supportsImage,
	supportContentObject,
	ImageWidthMin,
	ImageWidthMax,
	ImageHeightMin,
	ImageHeightMax
)
values(
	'Search Ad',
	0,
	1,
	1,
	180,
	180,
	150,
	150
)

select @searchAdTypeID = SCOPE_IDENTITY()

insert into ad_zoneAdTypes (
	zoneTypeID,
	adTypeID
)
values(
	@searchZoneTypeID,
	@searchAdTypeID
)

insert into ad_types (
	adTypeName,
	supportsText,
	supportsImage,
	supportContentObject,
	ImageWidthMin,
	ImageWidthMax,
	ImageHeightMin,
	ImageHeightMax
)
values(
	'Listviewer Ad',
	0,
	1,
	0,
	728,
	728,
	90,
	90
)

select @listviewerAdTypeID = SCOPE_IDENTITY()

insert into ad_zoneAdTypes (
	zoneTypeID,
	adTypeID
)
values(
	@listviewerZoneTypeID,
	@listviewerAdTypeID
)

insert into ad_types (
	adTypeName,
	supportsText,
	supportsImage,
	supportContentObject,
	ImageWidthMin,
	ImageWidthMax,
	ImageHeightMin,
	ImageHeightMax
)
values(
	'Login Page Ad',
	0,
	1,
	0,
	180,
	180,
	150,
	150
)

select @loginPageAdTypeID = SCOPE_IDENTITY()

insert into ad_zoneAdTypes (
	zoneTypeID,
	adTypeID
)
values(
	@loginPageZoneTypeID,
	@loginPageAdTypeID
)
GO

USE [memberCentral]
GO


print '********************************************'
print 'CREATING SP ad_createAdvertiser'
print '********************************************'

IF OBJECT_ID('ad_createAdvertiser') IS NOT NULL
	DROP PROCEDURE ad_createAdvertiser
GO

CREATE PROCEDURE [dbo].[ad_createAdvertiser]
	@name varchar(255),
	@orgID	int,
	@googleUACode varchar(200),
	@status char(1),
	@advertiserID int OUTPUT
AS

declare
	@rc int

if ( exists (select advertiserID from dbo.ad_advertisers where [name] = @name and orgID = @orgID and [status] = 'A'))
	GOTO on_error
else begin

	insert into dbo.ad_advertisers (
		name,
		orgID,
		googleUACode,
		[status]
	)
	values (
		@name,
		@orgID,
		@googleUACode,
		@status
	)

	IF @@ERROR <> 0 GOTO on_error
	SELECT @advertiserID = SCOPE_IDENTITY()	

	IF @@TRANCOUNT > 0 COMMIT TRAN
	GOTO on_success
	
end

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @advertiserID = 0
	RETURN -1

on_success:
	RETURN 0
	
GO



DECLARE
	@toolTypeID int,
	@toolResourceTypeID int,
	@roleID int,
	@navigationID int,
	@resourceTypeFunctionID int,
	@orgcode varchar(5),
	@orgID int,
	@sitecode varchar(5),
	@siteID int,
	@TopTabNavigationID int,
	@level2NavigationID int,
	@level3NavigationID int,
	@rc int,
	@viewfunctionID int,
	@superAdminRoleID int,
	@viewResourceTypeFunctionID int

select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')

select @siteID = dbo.fn_getSiteIDfromSiteCode('MC')
select @orgID = dbo.fn_getOrgIDfromOrgCode('MC')

select @toolTypeID = null, @toolResourceTypeID = null

-- create Admin tool type
EXEC @rc = dbo.createAdminToolType
	@toolType='AdsAdmin',
	@toolCFC='Ads.AdsAdmin',
	@toolDesc='Advertisement Administrator',
	@toolTypeID=@toolTypeID OUTPUT,
	@resourceTypeID=@toolResourceTypeID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- restrict this tool to MC
insert into admin_siteToolRestrictions (toolTypeID, siteID) values (@toolTypeID, @siteID)
IF @@ERROR <> 0 GOTO on_error


select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')

EXEC @rc = cms_createSiteResourceFunction @resourceTypeID=@toolResourceTypeID, @functionName='View', @displayName='View', @functionID=@viewfunctionID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	select @viewResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@toolResourceTypeID,@viewfunctionID);
		IF @@ERROR <> 0 GOTO on_error
	exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@superAdminRoleID, @resourceTypeFunctionID=@viewResourceTypeFunctionID;
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

select @TopTabNavigationID = navigationID
from admin_navigation n
where navname = 'Website'
and navAreaID = 1

select @resourceTypeFunctionID = resourceTypeFunctionID
from admin_toolTypes tt
inner join cms_siteResourceTypes srt
	on tt.resourceTypeID = srt.resourceTypeID
	and tt.toolType ='AdsAdmin'
inner join cms_siteResourceTypeFunctions srtf
	on srtf.resourceTypeID = srt.resourceTypeID
inner join cms_siteResourceFunctions srf
	on srf.functionID = srtf.functionID
	and srf.functionName = 'view'

select @resourceTypeFunctionID 

BEGIN TRAN
	-- create level 2 nav
	EXEC @rc = dbo.createAdminNavigation
		@navName='Advertisements',
		@navDesc='Advertisement Administrator',
		@parentNavigationID=@TopTabNavigationID,
		@navAreaID=2,
		@cfcMethod=null,
		@isHeader=0,
		@showInNav=1,
		@navigationID=@level2NavigationID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- create level3 items

	select @level3NavigationID = null

	EXEC @rc = dbo.createAdminNavigation
		@navName='Ad Management',
		@navDesc='Ad Management',
		@parentNavigationID=@level2NavigationID,
		@navAreaID=3,
		@cfcMethod='listAds',
		@isHeader=0,
		@showInNav=1,
		@navigationID=@level3NavigationID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = dbo.createAdminFunctionsDeterminingNav
		@resourceTypeFunctionID=@viewResourceTypeFunctionID,
		@toolTypeID=@toolTypeID,
		@navigationID=@level3NavigationID
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	select @level3NavigationID = null

	EXEC @rc = dbo.createAdminNavigation
		@navName='Advertisers',
		@navDesc='Advertisers Management',
		@parentNavigationID=@level2NavigationID,
		@navAreaID=3,
		@cfcMethod='listAdvertisers',
		@isHeader=0,
		@showInNav=1,
		@navigationID=@level3NavigationID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = dbo.createAdminFunctionsDeterminingNav
		@resourceTypeFunctionID=@viewResourceTypeFunctionID,
		@toolTypeID=@toolTypeID,
		@navigationID=@level3NavigationID
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	select @level3NavigationID = null

	EXEC @rc = dbo.createAdminNavigation
		@navName='Ad Zones',
		@navDesc='Ad Zones Management',
		@parentNavigationID=@level2NavigationID,
		@navAreaID=3,
		@cfcMethod='listZones',
		@isHeader=0,
		@showInNav=1,
		@navigationID=@level3NavigationID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error	

	EXEC @rc = dbo.createAdminFunctionsDeterminingNav
		@resourceTypeFunctionID=@viewResourceTypeFunctionID,
		@toolTypeID=@toolTypeID,
		@navigationID=@level3NavigationID
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

IF @@TRANCOUNT > 0 COMMIT TRAN

EXEC dbo.createAdminSuite @siteid=@siteID

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
GO

use membercentral
GO

declare @TurnOnSubscriptions bit, @TurnOnRelationships bit, @TurnOnTasks bit, 
		@TurnOnDocs bit, @TurnOnApptTracker bit, @TurnOnReports bit, 
		@TurnOnMemberHistory bit, @TurnOnReferrals bit, @TurnOnAds bit
declare @sitecode varchar(10), @siteID int


/* CHANGE SETTINGS HERE */
set @sitecode = 'MC'
set @TurnOnSubscriptions = 0
set @TurnOnRelationships = 0
set @TurnOnTasks = 0
set @TurnOnDocs = 0
set @TurnOnApptTracker = 0
set @TurnOnMemberHistory = 0
set @TurnOnReports = 0
set @TurnOnReferrals = 0
set @TurnOnAds = 1


select @siteID = siteID from dbo.sites where siteCode = @siteCode

-- subscriptions
IF @TurnOnSubscriptions = 1 BEGIN
	update ai
	set settingsXML.modify('replace value of (/settings/setting[@name=''showSubscriptions'']/@value)[1] with ''true''')
	from dbo.cms_applicationInstances as ai
	inner join dbo.sites as s on s.siteID = ai.siteID
	where ai.applicationInstanceName = 'admin'
	and s.sitecode = @siteCode

	insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
	select tooltypeID, @siteID
	from dbo.admin_toolTypes
	where toolType = 'SubscriptionAdmin'
	except
	select tooltypeID, siteID from dbo.admin_siteToolRestrictions

	insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
	select tooltypeID, @siteID
	from dbo.admin_toolTypes
	where toolType = 'SubRenewalAdmin'
	except
	select tooltypeID, siteID from dbo.admin_siteToolRestrictions
END

-- member history
IF @TurnOnMemberHistory = 1 BEGIN
	update ai
	set settingsXML.modify('replace value of (/settings/setting[@name=''showMemberHistory'']/@value)[1] with ''true''')
	from dbo.cms_applicationInstances as ai
	inner join dbo.sites as s on s.siteID = ai.siteID
	where ai.applicationInstanceName = 'admin'
	and s.sitecode = @siteCode

	insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
	select tooltypeID, @siteID
	from dbo.admin_toolTypes
	where toolType = 'MemberHistoryAdmin'
	except
	select tooltypeID, siteID from dbo.admin_siteToolRestrictions

	exec dbo.cms_createDefaultHistoryAdminCategories @siteID=@siteID, @contributingMemberID=11
END

-- referrals
IF @TurnOnReferrals = 1 BEGIN
	update ai
	set settingsXML.modify('replace value of (/settings/setting[@name=''showReferrals'']/@value)[1] with ''true''')
	from dbo.cms_applicationInstances as ai
	inner join dbo.sites as s on s.siteID = ai.siteID
	where ai.applicationInstanceName = 'admin'
	and s.sitecode = @siteCode

	insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
	select tooltypeID, @siteID
	from dbo.admin_toolTypes
	where toolType = 'ReferralsAdmin'
	except
	select tooltypeID, siteID from dbo.admin_siteToolRestrictions
END

-- relationships
IF @TurnOnRelationships = 1 BEGIN
	update ai
	set settingsXML.modify('replace value of (/settings/setting[@name=''showRelationships'']/@value)[1] with ''true''')
	from dbo.cms_applicationInstances as ai
	inner join dbo.sites as s on s.siteID = ai.siteID
	where ai.applicationInstanceName = 'admin'
	and s.sitecode = @siteCode

	insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
	select tooltypeID, @siteID
	from dbo.admin_toolTypes
	where toolType = 'RelationshipAdmin'
	except
	select tooltypeID, siteID from dbo.admin_siteToolRestrictions
END

-- tasks
IF @TurnOnTasks = 1 BEGIN
	update ai
	set settingsXML.modify('replace value of (/settings/setting[@name=''showNotes'']/@value)[1] with ''true''')
	from dbo.cms_applicationInstances as ai
	inner join dbo.sites as s on s.siteID = ai.siteID
	where ai.applicationInstanceName = 'admin'
	and s.sitecode = @siteCode

	insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
	select tooltypeID, @siteID
	from dbo.admin_toolTypes
	where toolType = 'NotesAdmin'
	except
	select tooltypeID, siteID from dbo.admin_siteToolRestrictions

	EXEC dbo.cms_createDefaultNotesCategories @siteid, 11
END

-- member documents
IF @TurnOnDocs = 1 BEGIN
	update ai
	set settingsXML.modify('replace value of (/settings/setting[@name=''showMemberDocuments'']/@value)[1] with ''true''')
	from dbo.cms_applicationInstances as ai
	inner join dbo.sites as s on s.siteID = ai.siteID
	where ai.applicationInstanceName = 'admin'
	and s.sitecode = @siteCode
END

-- appt tracker
IF @TurnOnApptTracker = 1 BEGIN
	insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
	select tooltypeID, @siteID
	from dbo.admin_toolTypes
	where toolType = 'AppointmentTrackerAdmin'
	except
	select tooltypeID, siteID from dbo.admin_siteToolRestrictions

	EXEC dbo.cms_createDefaultAppointmentCategories @siteID, 11
END

-- reports
IF @TurnOnReports = 1 BEGIN
	insert into dbo.admin_siteToolRestrictions (toolTypeID, siteID)
	select tooltypeid, @siteID 
	from dbo.admin_toolTypes 
	where toolCFC like 'Reports.%' 
	or toolCFC like 'Reports.custom.' + @sitecode + '.%'
	except
	select tooltypeID, siteID from dbo.admin_siteToolRestrictions
END

-- Ads
IF @TurnOnAds = 1 BEGIN
	update ai
	set settingsXML.modify('replace value of (/settings/setting[@name=''showAds'']/@value)[1] with ''true''')
	from dbo.cms_applicationInstances as ai
	inner join dbo.sites as s on s.siteID = ai.siteID
	where ai.applicationInstanceName = 'admin'
	and s.sitecode = @siteCode

	insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
	select tooltypeID, @siteID
	from dbo.admin_toolTypes
	where toolType = 'AdsAdmin'
	except
	select tooltypeID, siteID from dbo.admin_siteToolRestrictions
END

-- refresh
exec dbo.createadminsuite @siteid
GO

