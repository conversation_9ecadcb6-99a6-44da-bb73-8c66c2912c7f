use membercentral
GO

delete from cache_members_conditions
where autoID in (
	select min(autoid) as minAutoID
	from dbo.cache_members_conditions
	group by memberid, conditionid
	having count(*) > 1
)
GO

ALTER TABLE dbo.cache_members_conditions DROP CONSTRAINT PK_cache_members_conditions
GO
ALTER TABLE dbo.cache_members_conditions ADD CONSTRAINT
	PK_cache_members_conditions PRIMARY KEY CLUSTERED 
	(
	memberid,
	conditionID
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

GO

IF  EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[cache_members_conditions]') AND name = N'IX_cache_members_conditions_memberid_conditionID')
DROP INDEX [IX_cache_members_conditions_memberid_conditionID] ON [dbo].[cache_members_conditions] WITH ( ONLINE = OFF )
GO

IF  EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[cache_members_conditions]') AND name = N'IX_cache_members_conditions_conditionID')
DROP INDEX [IX_cache_members_conditions_conditionID] ON [dbo].[cache_members_conditions] WITH ( ONLINE = OFF )
GO

CREATE NONCLUSTERED INDEX [IX_cache_members_conditions_conditionID] ON [dbo].[cache_members_conditions] 
(
	[conditionID] ASC
)
INCLUDE ([memberid]) WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO

ALTER TABLE dbo.cache_members_conditions DROP COLUMN autoid
GO

