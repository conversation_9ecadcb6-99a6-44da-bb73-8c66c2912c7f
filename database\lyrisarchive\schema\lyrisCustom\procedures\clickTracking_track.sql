ALTER PROC dbo.clickTracking_track
@ipNumber int, 
@memberID int,
@messageID int,
@groupID int,
@linkID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	BEGIN TRAN;
		INSERT INTO trialslyris1.dbo.clicktracking_ (IPAddress_, MemberID_, MessageID_, TimeClicked_, GroupID_)
		SELECT @ipNumber, @memberID, @messageID, getdate(), @groupID
		WHERE NOT EXISTS ( 
			SELECT 1 
			FROM trialslyris1.dbo.clicktracking_ 
			WHERE IPAddress_= @ipNumber
			AND MemberID_ = @memberID
			AND MessageID_ = @messageID
			AND GroupID_ = @groupID
			AND StreamWebPageName_ IS NULL 
			AND UrlID_ IS NULL
		); 

		INSERT INTO trialslyris1.dbo.clicktracking_ (IPAddress_, MemberID_, MessageID_, TimeClicked_, UrlID_, GroupID_)
		values (@ipNumber, @memberID, @messageID, getdate(), @linkID, @groupID);
	COMMIT TRAN;

	RETURN 0;

E<PERSON> TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO
