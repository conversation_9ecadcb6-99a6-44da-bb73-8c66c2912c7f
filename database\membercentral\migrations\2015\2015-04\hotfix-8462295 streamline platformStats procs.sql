use platformstats;
GO
ALTER PROCEDURE [dbo].[up_SiteStats]
@siteID int = 0,
@orgID int = 0,
@statsStart smalldatetime = getdate,
@statsEnd smalldatetime = getdate,
@onlyReturnFirst bit = '0'

AS

SET NOCOUNT ON


IF OBJECT_ID('tempdb..#statSessions') IS NOT NULL 
	DROP TABLE #statSessions
create table #statSessions (sessionid int PRIMARY KEY, memberid int)

IF OBJECT_ID('tempdb..#statsAppHits') IS NOT NULL 
	DROP TABLE #statsAppHits
create table #statsAppHits (appHitID int PRIMARY KEY, sessionid int, appSectionID int)

IF OBJECT_ID('tempdb..#statsPageHits') IS NOT NULL 
	DROP TABLE #statsPageHits
create table #statsPageHits (pageHitID int PRIMARY KEY, sessionid int, pageID int)

-- declare vars
DECLARE @numMembers int, @numtotalmembers int, @numInactiveMembers int, @grossmemberhits int, @grossmemberhitsApps int
DECLARE @grossmemberdeniedhits int, @uniquememberhits int, @membervisits int, @totalnonmembers int, @grossnonmemberhits int
DECLARE @grossnonmemberhitsapps int, @grossnonmemberdeniedhits int, @uniquenonmemberhits int

declare @minPageHitID int, @maxPageHitID int, @minAppHitID int, @maxAppHitID int

declare @appNames TABLE (appSectionID int PRIMARY KEY, appNameID int, name varchar(200))
declare @highestRankingPages TABLE (numhits int, itemID int, itemName varchar(200), isPageHit bit, isMemberHit bit)

-- reset start date to 00:00 of startdate
-- reset end date to 23:59 of enddate (actually 0:00 of next day)
SELECT @statsStart = DATEADD(dd, DATEDIFF(dd,0,@statsStart), 0)
SELECT @statsEnd = DATEADD(dd, DATEDIFF(dd,0,@statsEnd)+1, 0)

DECLARE @sessionStart smalldatetime
DECLARE @sessionEnd smalldatetime

-- 1hr padding with (NOLOCK) ON session start time just to make sure that we have the sessions for ALL
-- of the hits in the time range
select @sessionStart = dateadd(hour,-1,@statsStart)
select @sessionEnd = @statsEnd


insert into @appNames (appSectionID, appNameID, name)
select sas.appSectionID, san.appNameID, san.name
from statsAppSections sas (nolock)
inner join dbo.statsAppNames san (nolock)
	on san.appNameID = sas.appNameID

insert into #statSessions (sessionid, memberid)
select sessionid, memberid
from dbo.statsSessions with (NOLOCK)
where siteID = @siteID
and dateentered between @sessionStart and @sessionEnd
AND ignore = 0


CREATE INDEX IX_statSessions on #statSessions (sessionid asc) INCLUDE (memberID)

insert into #statsAppHits (appHitID,sessionid ,appSectionID)
select sah.appHitID,ss.sessionid ,sah.appSectionID
from #statSessions ss
inner join dbo.statsAppHits sah (nolock)
	on sah.sessionID = ss.sessionID
	--and sah.dateentered between @statsStart and @statsEnd


CREATE INDEX IX_statsAppHits on #statsAppHits (sessionid asc, appSectionID asc)


insert into #statsPageHits (pageHitID,sessionid ,pageID)
select sph.pageHitID,ss.sessionid ,sph.pageID
from #statSessions ss
inner join dbo.statsPageHits sph (nolock)
	on sph.sessionID = ss.sessionID
	--and sph.dateentered between @statsStart and @statsEnd

CREATE INDEX IX_statspageHits on #statsPageHits (sessionid asc, pageID asc)


-- numRegisteredMembers
SELECT @nummembers = COUNT(m.memberID)
	from membercentral.dbo.ams_members m  with (NOLOCK)
		INNER JOIN membercentral.dbo.ams_memberNetworkProfiles AS mnp with (NOLOCK) ON mnp.memberID = m.memberID
	where m.orgID = @orgID
	AND m.status = 'A'

-- numTotalMembers
SELECT @numtotalmembers = COUNT(memberid)
	FROM membercentral.dbo.ams_members with (NOLOCK)
	WHERE orgID = @orgID
	and status = 'A'

-- numInactiveMembers
SELECT @numInactiveMembers = COUNT(memberid)
	FROM membercentral.dbo.ams_members with (NOLOCK)
	WHERE orgID = @orgID
	and status = 'I'

-- grossmemberhits  NEW
SELECT @grossmemberhits = COUNT(sph.pageHitID)
	FROM #statsPageHits as sph  with (NOLOCK)
	INNER JOIN #statSessions as ss 
		ON sph.sessionid = ss.sessionid 
			AND ss.memberid IS NOT NULL
	option (RECOMPILE)


-- grossmemberhitsApps
SELECT @grossmemberhitsApps = COUNT(sah.appHitID)
	FROM #statsAppHits as sah  with (NOLOCK)
	INNER JOIN #statSessions as ss 
		ON sah.sessionid = ss.sessionid
		AND ss.memberid IS NOT NULL

option (RECOMPILE)

-- grossmemberdeniedhits
SELECT @grossmemberdeniedhits = COUNT(sph.pageHitID)
	FROM #statsPageHits as sph  with (NOLOCK)
	INNER JOIN #statSessions as ss
	ON sph.sessionid = ss.sessionid
		AND ss.memberid IS NOT NULL

option (RECOMPILE)

-- uniquememberhits
SELECT @uniquememberhits = count(*) FROM (
	SELECT DISTINCT ss.memberid
	FROM #statsPageHits AS sph  with (NOLOCK)
	INNER JOIN #statSessions AS ss 
		ON sph.sessionid = ss.sessionid
		AND ss.memberid IS NOT NULL
	union
	SELECT DISTINCT ss.memberid
	FROM #statsAppHits AS sah  with (NOLOCK)
	INNER JOIN #statSessions AS ss 
		ON sah.sessionid = ss.sessionid
		AND ss.memberid IS NOT NULL
) as t	
option (RECOMPILE)

-- membervisits
SELECT @membervisits = count(*) FROM (
	SELECT DISTINCT sph.sessionid
	FROM #statsPageHits AS sph  with (NOLOCK)
	INNER JOIN #statSessions AS ss
		ON sph.sessionid = ss.sessionid
		AND ss.memberid IS NOT NULL
	union
	SELECT DISTINCT sah.sessionid
	FROM #statsAppHits AS sah  with (NOLOCK)
	INNER JOIN #statSessions AS ss 
		ON sah.sessionid = ss.sessionid
		AND ss.memberid IS NOT NULL
) as t	
option (RECOMPILE)

-- totalnonmembers
SELECT @totalnonmembers = count(*) FROM (
	SELECT DISTINCT sph.sessionid
	FROM #statsPageHits AS sph  with (NOLOCK)
	INNER JOIN #statSessions AS ss 
		ON sph.sessionid = ss.sessionid
		AND ss.memberid IS NULL
	union
	SELECT DISTINCT sah.sessionid
	FROM #statsAppHits AS sah  with (NOLOCK)
	INNER JOIN #statSessions AS ss 
		ON sah.sessionid = ss.sessionid
		AND ss.memberid IS NULL
) as t	
option (RECOMPILE)

-- grossnonmemberhits
SELECT @grossnonmemberhits = COUNT(sph.pageHitID)
	FROM #statsPageHits as sph  with (NOLOCK)
	INNER JOIN #statSessions as ss
		ON sph.sessionid = ss.sessionid
		AND ss.memberid IS NULL

option (RECOMPILE)

-- grossnonmemberhitsapps
SELECT @grossnonmemberhitsapps = COUNT(sah.appHitID)
	FROM #statsAppHits as sah  with (NOLOCK)
	INNER JOIN #statSessions as ss
		ON sah.sessionid = ss.sessionid
		AND ss.memberid IS NULL
	
option (RECOMPILE)

-- grossnonmemberdeniedhits
SELECT @grossnonmemberdeniedhits = 0


-- uniquenonmemberhits
SELECT @uniquenonmemberhits = @totalnonmembers

-- return one query for the counts
SELECT 
	@orgID as orgID,
	@numMembers as numMembers, 
	@numtotalmembers as numtotalmembers, 
	@numInactiveMembers as numInactiveMembers, 
	@grossmemberhits as grossmemberhits, 
	@grossmemberhitsApps as grossmemberhitsApps, 
	@grossmemberdeniedhits as grossmemberdeniedhits, 
	@uniquememberhits as uniquememberhits, 
	@membervisits as membervisits, 
	@totalnonmembers as totalnonmembers, 
	@grossnonmemberhits as grossnonmemberhits, 
	@grossnonmemberhitsapps as grossnonmemberhitsapps, 
	@grossnonmemberdeniedhits as grossnonmemberdeniedhits, 
	@uniquenonmemberhits as uniquenonmemberhits

if @onlyReturnFirst = '1'
	GOTO on_cleanup


-- HighestRankingPagesNonmemberHits
insert into @highestRankingPages (numhits, itemID, isPageHit,isMemberHit)
SELECT TOP 50 COUNT(sph.pageHitID) AS numhits, sph.pageid as itemID, 1 as isPageHit, 0 as isMemberHit
FROM #statsPageHits AS sph  with (NOLOCK)
INNER JOIN #statSessions AS ss 
	ON sph.sessionid = ss.sessionid 
	AND ss.memberid IS NULL
GROUP BY sph.pageid
order by numhits desc

insert into @highestRankingPages (numhits, itemName, isPageHit,isMemberHit)
SELECT TOP 50 COUNT(sah.appHitID) AS numhits, aname.name as itemName, 0 as isPageHit, 0 as isMemberHit
FROM #statSessions AS ss 
INNER JOIN #statsAppHits as sah with (NOLOCK)
	ON ss.sessionid = sah.sessionid
		AND ss.memberid IS NULL
INNER JOIN @appNames as aname ON sah.appSectionID = aname.appSectionID 
GROUP BY aname.name
ORDER BY numhits DESC



-- HighestRankingPagesMemberHits
insert into @highestRankingPages (numhits, itemID, isPageHit,isMemberHit)
SELECT TOP 50 COUNT(sph.pageHitID) AS numhits, sph.pageid as itemID, 1 as isPageHit, 1 as isMemberHit
FROM #statsPageHits AS sph  with (NOLOCK)
INNER JOIN #statSessions AS ss
	ON sph.sessionid = ss.sessionid
	AND ss.memberid IS NOT NULL
GROUP BY sph.pageid
order by numhits desc

insert into @highestRankingPages (numhits, itemName, isPageHit,isMemberHit)
SELECT TOP 50 COUNT(sah.appHitID) AS numhits, aname.name as itemName, 0 as isPageHit, 1 as isMemberHit
FROM #statSessions AS ss 
INNER JOIN #statsAppHits as sah with (NOLOCK)
	ON ss.sessionid = sah.sessionid
	AND ss.memberid IS NOT NULL
INNER JOIN @appNames as aname ON sah.appSectionID = aname.appSectionID 
GROUP BY aname.name
order by numhits desc

-- get pagenames from IDs
update tmp set
	itemName = p.pagename
from @highestRankingPages tmp
INNER JOIN membercentral.dbo.cms_pages AS p with (NOLOCK) ON p.pageId = tmp.itemID

-- output HighestRankingPagesNonmemberHits
select top 50 numhits, itemName as pageName, type = case isPageHit when 1 then 'page' else 'app' end
from @highestRankingPages tmp
where isMemberHit=0
order by numhits desc

-- output HighestRankingPagesMemberHits
select top 50 numhits, itemName as pageName, type = case isPageHit when 1 then 'page' else 'app' end
from @highestRankingPages tmp
where isMemberHit=1
order by numhits desc


on_cleanup:


IF OBJECT_ID('tempdb..#statSessions') IS NOT NULL 
	DROP TABLE #statSessions

IF OBJECT_ID('tempdb..#statsAppHits') IS NOT NULL 
	DROP TABLE #statsAppHits

IF OBJECT_ID('tempdb..#statsPageHits') IS NOT NULL 
	DROP TABLE #statsPageHits


SET NOCOUNT OFF

GO
ALTER PROCEDURE [dbo].[up_pageStats]
	@siteID int = 0,
	@orgID int = 0,
	@pageName varchar(100) = '',
	@statsStart smalldatetime = getdate,
	@statsEnd smalldatetime = getdate

AS

SET NOCOUNT ON

IF OBJECT_ID('tempdb..#statSessions') IS NOT NULL 
	DROP TABLE #statSessions
create table #statSessions (sessionid int PRIMARY KEY, memberid int)

IF OBJECT_ID('tempdb..#statsPageHits') IS NOT NULL 
	DROP TABLE #statsPageHits
create table #statsPageHits (pageHitID int PRIMARY KEY, sessionid int, pageID int, refererpageid int)



-- declare vars
DECLARE @pageid int, @pagecreated datetime, @pageModified datetime, @resourceid int
DECLARE @tmpStats TABLE (
	pageID int,
	pagecreated datetime,
	pagemodified datetime,
	resourceid int,
	numRegisteredMembers int,	
	numTotalMembers int,	
	memberGrossViewsAllowed int,
	guestGrossViewsAllowed int,
	totalGrossViewsAllowed int,
	memberGrossViewsDenied int,
	guestGrossViewsDenied int,
	totalGrossViewsDenied int,
	memberSessions int,
	guestSessions int,
	totalSessions int,
	memberPageViewsPerSession int,
	guestPageViewsPerSession int,
	totalPageViewsPerSession int,
	membersVisited int,
	membersVisitedPctRegistered int,
	membersVisitedPctTotal int
)

-- reset start date to 00:00 of startdate
-- reset end date to 23:59 of enddate (actually 0:00 of next day)
SELECT @statsStart = DATEADD(dd, DATEDIFF(dd,0,@statsStart), 0)
SELECT @statsEnd = DATEADD(dd, DATEDIFF(dd,0,@statsEnd)+1, 0)

-- get pageid
select @pageid = pageid, @resourceid = siteResourceid
from membercentral.dbo.cms_pages with (NOLOCK)
where siteID = @siteID
and pagename = @pageName

INSERT INTO @tmpStats (pageID, resourceid)
VALUES (@pageid, @resourceid)


insert into #statsPageHits (pageHitID,sessionid ,pageID, refererpageid)
select sph.pageHitID,ss.sessionid ,sph.pageID, sph.refererpageid
from dbo.statsSessions ss (nolock)
inner join dbo.statsPageHits sph (nolock)
	on sph.sessionID = ss.sessionID
     and sph.pageID = @pageID
	and sph.dateentered between @statsStart and @statsEnd
option(recompile)

CREATE INDEX IX_statspageHits on #statsPageHits (sessionid asc, pageID asc) include (refererPageid)


insert into #statSessions (sessionid, memberid)
select distinct ss.sessionid, ss.memberid
from #statsPageHits tmp
inner join dbo.statsSessions ss with (NOLOCK)
    on ss.sessionID = tmp.sessionID
where ignore = 0
option(recompile)

CREATE INDEX IX_statSessions on #statSessions (sessionid asc) INCLUDE (memberID)

-- numRegisteredMembers
UPDATE @tmpStats
set numRegisteredMembers = (SELECT COUNT(m.memberID)
	from membercentral.dbo.ams_members m  with (NOLOCK)
		INNER JOIN membercentral.dbo.ams_memberNetworkProfiles AS mnp with (NOLOCK) ON mnp.memberID = m.memberID
	where m.orgID = @orgID
	AND m.status = 'A')

-- numTotalMembers
UPDATE @tmpStats
set numTotalMembers = (SELECT COUNT(memberid)
	FROM membercentral.dbo.ams_members with (NOLOCK)
	WHERE orgID = @orgID
	and status = 'A')



-- memberGrossViewsAllowed
UPDATE @tmpStats
set memberGrossViewsAllowed = (select COUNT(sph.pageHitID)
	FROM #statsPageHits as sph with (NOLOCK) 
	INNER JOIN #statSessions as ss with (NOLOCK) ON sph.sessionid = ss.sessionid
	WHERE ss.memberid IS NOT NULL)

-- guestGrossViewsAllowed
UPDATE @tmpStats
set guestGrossViewsAllowed = (select COUNT(sph.pageHitID)
	FROM #statsPageHits as sph  with (NOLOCK)
	INNER JOIN #statSessions as ss with (NOLOCK) 
	   ON sph.sessionid = ss.sessionid
	WHERE ss.memberid IS NULL)


-- totalGrossViewsAllowed
UPDATE @tmpStats
set totalGrossViewsAllowed = isnull(memberGrossViewsAllowed,0) + isnull(guestGrossViewsAllowed,0)


-- memberSessions
UPDATE @tmpStats
set memberSessions = (select COUNT(DISTINCT sph.sessionid)
	FROM #statsPageHits AS sph  with (NOLOCK)
	INNER JOIN #statSessions AS ss with (NOLOCK) ON sph.sessionid = ss.sessionid
	WHERE ss.memberid IS NOT NULL)

-- guestSessions
UPDATE @tmpStats
set guestSessions = (select COUNT(DISTINCT sph.sessionid)
	FROM #statsPageHits AS sph  with (NOLOCK)
	INNER JOIN #statSessions AS ss with (NOLOCK) ON sph.sessionid = ss.sessionid
	WHERE ss.memberid IS NULL)

-- totalSessions
UPDATE @tmpStats
set totalSessions = isnull(memberSessions,0) + isnull(guestSessions,0)

-- memberPageViewsPerSession, guestPageViewsPerSession, totalPageViewsPerSession
UPDATE @tmpStats
set memberPageViewsPerSession = CASE
	WHEN memberGrossViewsAllowed = 0 or memberSessions = 0 then 0
	ELSE cast(memberGrossViewsAllowed / memberSessions as int)
	END,
	guestPageViewsPerSession = CASE
	WHEN guestGrossViewsAllowed = 0 or guestSessions = 0 then 0
	ELSE cast(guestGrossViewsAllowed / guestSessions as int)
	END,
	totalPageViewsPerSession = CASE
	WHEN totalGrossViewsAllowed = 0 or totalSessions = 0 then 0
	ELSE cast(totalGrossViewsAllowed / totalSessions as int)
	END

-- membersVisited
UPDATE @tmpStats
set membersVisited = (select COUNT(DISTINCT ss.memberid)
	FROM #statsPageHits AS sph  with (NOLOCK)
	INNER JOIN #statSessions AS ss with (NOLOCK) ON sph.sessionid = ss.sessionid
	INNER JOIN membercentral.dbo.ams_members as o with (NOLOCK) ON o.memberid = ss.memberid
	WHERE o.orgID = @orgID)

-- membersVisitedPctRegistered, membersVisitedPctTotal
UPDATE @tmpStats
set membersVisitedPctRegistered = CASE
	WHEN numRegisteredMembers = 0 or membersVisited = 0 then 0
	ELSE cast(membersVisited / numRegisteredMembers * 100 as int)
	END,
	membersVisitedPctTotal = CASE
	WHEN numTotalMembers = 0 or membersVisited = 0 then 0
	ELSE cast(membersVisited / numTotalMembers * 100 as int)
	END

-- return one query for the counts
SELECT * from @tmpStats


-- top referers
SELECT top 10 sph.refererPageid, count(sph.pagehitid) as numhits, p.pagename
FROM #statsPageHits as sph  with (NOLOCK) 
INNER JOIN #statSessions as ss with (NOLOCK) ON sph.sessionid = ss.sessionid
left outer join membercentral.dbo.cms_pages as p with (NOLOCK) ON p.pageid = sph.refererPageid
group by sph.refererpageid, p.pagename
order by count(sph.pagehitid) desc

-- top destinations
SELECT top 10 sph.Pageid, count(sph.pagehitid) as numhits, p.pagename
FROM dbo.statsPageHits as sph   with (NOLOCK)
INNER JOIN #statSessions as ss with (NOLOCK) ON sph.sessionid = ss.sessionid
left outer join membercentral.dbo.cms_pages as p with (NOLOCK) ON p.pageid = sph.Pageid
WHERE sph.refererpageID = @pageid
and sph.dateentered BETWEEN @statsStart and @statsEnd
group by sph.pageid, p.pagename
order by count(sph.pagehitid) desc
option(recompile)


IF OBJECT_ID('tempdb..#statSessions') IS NOT NULL 
	DROP TABLE #statSessions
IF OBJECT_ID('tempdb..#statsPageHits') IS NOT NULL 
	DROP TABLE #statsPageHits

SET NOCOUNT OFF

GO
