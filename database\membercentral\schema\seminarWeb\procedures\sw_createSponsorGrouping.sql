ALTER PROC dbo.sw_createSponsorGrouping
@seminarID INT,
@participantID INT,
@sponsorGrouping VARCHAR(200),
@sponsorGroupingID INT OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET @sponsorGroupingID = NULL;

	IF EXISTS (SELECT sponsorGroupingID FROM dbo.sw_sponsorGrouping WHERE seminarID = @seminarID AND sponsorGrouping = @sponsorGrouping AND participantID = @participantID)
		RAISERROR('Sponsor Grouping Exists.',16,1);

	DECLARE @sponsorGroupingOrder INT;
	SELECT @sponsorGroupingOrder = ISNULL(MAX(sponsorGroupingOrder),0)+1 
	FROM dbo.sw_sponsorGrouping 
	WHERE seminarID = @seminarID
	AND participantID = @participantID;

	INSERT INTO dbo.sw_sponsorGrouping (participantID, seminarID, sponsorGrouping, sponsorGroupingOrder)
	VALUES (@participantID, @seminarID, @sponsorGrouping, @sponsorGroupingOrder);

	SELECT @sponsorGroupingID = SCOPE_IDENTITY();

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
