use membercentral;
GO
ALTER PROCEDURE [dbo].[ev_getCalendarsViewableByGroupPrintID]
	@siteID int,
	@groupPrintID int = null
AS

declare @siteResourceStatusID int, @viewFunctionID int, @orgID int
select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')
select @viewFunctionID = dbo.fn_getResourceFunctionID('view', dbo.fn_getResourceTypeID('events'))
select @orgID = dbo.fn_getOrgIDFromSiteID(@siteID)

if @groupPrintID is null
    select @groupPrintID = publicGroupPrintID
    from organizations
    where orgID = @orgID


select c.calendarID, sr.siteResourceID, ai.applicationInstanceName as calendarName
from ev_calendars c
inner join cms_applicationInstances ai
	on ai.applicationInstanceID = c.applicationInstanceID
	and ai.siteID = @siteID
inner join cms_siteResources sr
	on sr.siteResourceID = ai.siteResourceID
	and sr.siteResourceStatusID = @siteResourceStatusID
inner join dbo.cache_perms_siteResourceFunctionRightPrints srfrp
	on srfrp.siteResourceID = sr.siteResourceID
	and srfrp.functionID = @viewfunctionID
inner join dbo.cache_perms_groupPrintsRightPrints gprp
	on gprp.rightPrintID = srfrp.rightPrintID
	and gprp.groupPrintID = @groupPrintID
option(recompile)
GO

ALTER PROC [dbo].[ams_createMemberSiteDefaultsByOrgID]
@orgID int

AS

-- tl 7/2010 - only consider where memberid=activememberid when populating

DECLARE @tmpMembers TABLE (autoid int IDENTITY(1,1), siteID int, [status] char(1), memberID int)
DECLARE @memCount int, @memCount2 int, @message varchar(max), @functionID int
DECLARE @tblUN TABLE (autoid int IDENTITY(1,1), un varchar(40))
DECLARE @tblPW TABLE (autoid int IDENTITY(1,1), pw varchar(40))
DECLARE @orgcode varchar(10)
SELECT @orgcode = orgcode from dbo.organizations where orgID = @orgID
select @functionID = dbo.fn_getResourceFunctionID('Login', dbo.fn_getResourceTypeID('Site'))

select @message = 'Running ams_createMemberSiteDefaultsByOrgID for orgcode ' + @orgcode + char(10) + char(10)

-- members in groups that are not linked to a profile and do not have def u/p
INSERT INTO @tmpMembers (siteID, [status], memberID)
select distinct s.siteid, m.status, m.memberid
from sites s
inner join ams_members m
    on s.orgID = m.orgID
    and s.orgID = @orgID
    and m.memberID = m.activememberID
    and m.status in ('A','I')
inner join cache_perms_groupPrintsRightPrints gprp
    on gprp.groupPrintID = m.groupPrintID
inner join cache_perms_siteResourceFunctionRightPrints srfrp
    on srfrp.siteResourceID = s.siteResourceID
    and srfrp.functionID = @functionID
    and srfrp.rightPrintID = gprp.rightPrintID
left outer join dbo.ams_memberNetworkProfiles as mnp
	inner join dbo.ams_networkProfiles as np on np.profileid = mnp.profileid and np.status <> 'D' and mnp.status <> 'D'
	on mnp.memberid = m.memberid
left outer join dbo.ams_memberSiteDefaults as msd 
    on msd.memberid = m.memberid and msd.siteid = s.siteid and msd.status <> 'D'
where mnp.mnpID is null
and msd.defaultID is null
order by s.siteid

select @memCount = @@ROWCOUNT
select @message = @message + cast(@memCount as varchar(6)) + ' members found that need defaults.' + char(10)

-- create a pool of default usernames
INSERT INTO @tblUN (un)
SELECT TOP (@memCount) rntbl2.un
FROM (
	select randomNumber, @orgcode + randomNumber as un
	from (
		SELECT cast(abs(cast(newid() as binary(6)) %999999) + 1 as varchar(6)) as randomNumber
		FROM dbo.F_TABLE_NUMBER_RANGE(1,@memCount*5)
	) as rntbl
) as rntbl2
LEFT OUTER JOIN dbo.ams_memberSiteDefaults as msd on msd.defaultusername = rntbl2.un
LEFT OUTER JOIN dbo.ams_networkprofiles as np on np.username = rntbl2.un 
WHERE msd.defaultID is null
and np.profileID is null
GROUP BY rntbl2.randomNumber, rntbl2.un
HAVING rntbl2.randomNumber > 100000
AND Count(rntbl2.un) = 1
	select @message = @message + cast(@@ROWCOUNT as varchar(6)) + ' default usernames created.' + char(10)

-- create a pool of default passwords
INSERT INTO @tblPW (pw)
SELECT TOP (@memCount) sample.randomNumber
FROM (
	SELECT cast(abs(cast(newid() as binary(6)) %999999) + 1 as varchar(6)) as randomNumber
	FROM dbo.F_TABLE_NUMBER_RANGE(1,@memCount*5)) as sample
GROUP BY sample.randomNumber
HAVING sample.randomNumber > 100000
AND Count(sample.randomNumber) = 1
	select @message = @message + cast(@@ROWCOUNT as varchar(6)) + ' default passwords created.' + char(10)

-- add defaults
INSERT INTO dbo.ams_memberSiteDefaults (memberID, siteID, defaultUsername, defaultPassword, [status], dateCreated)
SELECT m.memberID, m.siteID, u.un, p.pw, m.status, getdate()
FROM @tmpMembers as m
inner join @tblUN as u on u.autoID = m.autoID
inner join @tblPW as p on p.autoID = m.autoID
where dbo.fn_checkConstraint_UsernamePassword(m.siteID,null,u.un,p.pw) = 1
	select @memCount2 = @@ROWCOUNT
	select @message = @message + cast(@memCount2 as varchar(6)) + ' defaults entered into database.' + char(10)

-- if counts are different, email about difference
IF @memCount2 <> @memCount BEGIN
	DECLARE @smtpserver varchar(20)
	SELECT @smtpserver = smtpserver	from membercentral.dbo.fn_getServerSettings()

	select @message = @message + 'Need to manually run "EXEC ams_createMemberSiteDefaultsByOrgID @orgID=' + cast(@orgID as varchar(8)) + '"' + char(10)

	EXEC dbo.sys_sendEmail 
		@from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject='createMemberSiteDefaultsByOrgID difference detected',
		@message=@message, @priority='high', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END

RETURN 0
GO