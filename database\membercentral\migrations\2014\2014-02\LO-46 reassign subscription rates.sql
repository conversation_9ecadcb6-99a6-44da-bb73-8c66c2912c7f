
/*
People who currently have renewal rates are given renewal rates, those with join rates are given join rates.
People are given the same frequency that they alreadg have (full, monthly, etc)

Doesn't touch site administrators, since they qualify for all of the rates

People who currently have one of the rates below are not touched.
Also, script prevents any of the people who ARE touched from getting one of these rates

- Sustaining
- Renewal for Sustaining
- Lifetime Attorney
- Lifetime Sustainer
- Retired
*/

declare @membersToProcess TABLE (orgID int, memberID int)

--select activem.memberID, activeM.memberNumber, activem.firstname, activem.lastname, ss.subscriberID, r.ratename, newrate.ratename as newratename
update ss
	set rfid = newrf.rfid
OUTPUT
	activem.orgID, activem.memberID
INTO @membersToProcess
from sites s
inner join dbo.sub_types t
	on t.siteID = s.siteID
	and s.sitecode = 'lo'
	and t.typeName in ('Membership Dues')
inner join sub_subscriptions subs
	on subs.typeID = t.typeID
	and subs.subscriptionName in ('Attorney Member')
inner join sub_rateSchedules rs
	on rs.scheduleID = subs.scheduleID
inner join sub_subscribers ss
	on ss.subscriptionID = subs.subscriptionID
inner join sub_statuses st
	on st.statusID = ss.statusID
	and st.statusCode in ('A','P','O')
inner join sub_rateFrequencies rf
	on rf.rfid = ss.rfid
inner join sub_rates r
	on r.rateID = rf.rateID
	and r.rateName not in ('Sustaining','Renewal for Sustaining','Lifetime Attorney','Lifetime Sustainer','Retired')
inner join ams_members m
	on m.memberID = ss.memberID
inner join ams_members activeM
	on activeM.memberID = m.activememberID
	and activeM.status in ('A','I')
left outer join cache_members_groups mg
	inner join ams_groups g
		on g.groupID = mg.groupID
		and g.groupcode = 'SiteAdmins'
on mg.memberID = activem.memberID
left outer join cache_perms_groupPrintsRightPrints gprp
	inner join cache_perms_siteResourceFunctionRightPrints srfrp
		on srfrp.rightPrintID = gprp.rightPrintID
	inner join cms_siteResourceFunctions srf
		on srf.functionID = srfrp.functionID
		and srf.functionName = 'Qualify'
	inner join sub_rates newRate
		on srfrp.siteResourceID = newRate.siteResourceID
		and newRate.rateName not in ('Sustaining','Renewal for Sustaining','Lifetime Attorney','Lifetime Sustainer','Retired')
	inner join sub_rateFrequencies newrf
		on newrf.rateID = newRate.rateID
on gprp.groupPrintID = activem.groupPrintID
and rs.scheduleID = newRate.scheduleID
and newRate.isRenewalRate = r.isRenewalRate
and newrf.frequencyID = rf.frequencyID
where mg.groupID is null

insert into dbo.queue_processMemberGroups (orgID,memberID)
select orgID, memberID from @membersToProcess


