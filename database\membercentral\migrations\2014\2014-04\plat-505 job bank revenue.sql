use membercentral
Go

/*
The following actions are performed in this script:
- ALTER jobBank TBL, Adding GLAccountID column
- UPDATIING jobBank. Assigning GL Accounts
- ALTER jobBank TBL, make GLAccountID column not nullable
- ALTER cms_createApplicationInstanceJobBank SP 
*/

print '%%%%%%%%%% ALTER jobBank TBL %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO


if not exists (select column_name from INFORMATION_SCHEMA.columns where table_name = 'jobBank' and column_name = 'GLAccountID')
	alter table dbo.jobBank 
		add GLAccountID int 
GO

IF  EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME ='FK_jobBank_tr_GlAccounts')
BEGIN
	print 'Relationship FK_jobBank_tr_GlAccounts Exists! Deleting...'
	ALTER TABLE [jobBank] DROP CONSTRAINT [FK_jobBank_tr_GlAccounts]
END
GO

print '%%%%%%%%%% UPDATIING jobBank. Assigning GL Accounts %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

declare @GLAccountID int, @jobBankID int

select 
	@GLAccountID = GLAccountID 
from 
	tr_GlAccounts 
where
	orgID = dbo.fn_getOrgIDFromOrgCode('MD')
	and accountCode = '4029'
	and status = 'A'

select 
	@jobBankID = jobBankID
from 
	dbo.jobBank jb
	inner join cms_applicationInstances ai on
		ai.applicationInstanceID = jb.applicationInstanceID
	inner join sites s on
		s.siteID = ai.siteID
		and s.siteCode = 'MD'

if @GLAccountID is not null
	update
		jobBank
	set
		GLAccountID = @GLAccountID
	where
		jobBankID = @jobBankID
---------------------------------------------------

set @GLAccountID = NULL
set @jobBankID = NULL

select 
	@GLAccountID = GLAccountID 
from 
	tr_GlAccounts 
where
	orgID = dbo.fn_getOrgIDFromOrgCode('KY')
	and accountCode = '1270'
	and status = 'A'

select 
	@jobBankID = jobBankID
from 
	dbo.jobBank jb
	inner join cms_applicationInstances ai on
		ai.applicationInstanceID = jb.applicationInstanceID
	inner join sites s on
		s.siteID = ai.siteID
		and s.siteCode = 'KY'

if @GLAccountID is not null
	update
		jobBank
	set
		GLAccountID = @GLAccountID
	where
		jobBankID = @jobBankID
---------------------------------------------------

set @GLAccountID = NULL
set @jobBankID = NULL

select 
	@GLAccountID = GLAccountID 
from 
	tr_GlAccounts 
where
	orgID = dbo.fn_getOrgIDFromOrgCode('BC')
	and accountCode = '4520'
	and status = 'A'

select 
	@jobBankID = jobBankID
from 
	dbo.jobBank jb
	inner join cms_applicationInstances ai on
		ai.applicationInstanceID = jb.applicationInstanceID
	inner join sites s on
		s.siteID = ai.siteID
		and s.siteCode = 'BC'

if @GLAccountID is not null
	update
		jobBank
	set
		GLAccountID = @GLAccountID
	where
		jobBankID = @jobBankID
---------------------------------------------------

set @GLAccountID = NULL
set @jobBankID = NULL

select 
	@GLAccountID = GLAccountID 
from 
	tr_GlAccounts 
where
	orgID = dbo.fn_getOrgIDFromOrgCode('OH')
	and accountCode = 'Jobbank'
	and status = 'A'

select 
	@jobBankID = jobBankID
from 
	dbo.jobBank jb
	inner join cms_applicationInstances ai on
		ai.applicationInstanceID = jb.applicationInstanceID
	inner join sites s on
		s.siteID = ai.siteID
		and s.siteCode = 'OH'

if @GLAccountID is not null
	update
		jobBank
	set
		GLAccountID = @GLAccountID
	where
		jobBankID = @jobBankID
---------------------------------------------------

set @GLAccountID = NULL
set @jobBankID = NULL

select 
	@GLAccountID = GLAccountID 
from 
	tr_GlAccounts 
where
	orgID = dbo.fn_getOrgIDFromOrgCode('OR')
	and accountCode = 'Jobbank'
	and status = 'A'

select 
	@jobBankID = jobBankID
from 
	dbo.jobBank jb
	inner join cms_applicationInstances ai on
		ai.applicationInstanceID = jb.applicationInstanceID
	inner join sites s on
		s.siteID = ai.siteID
		and s.siteCode = 'OR'

if @GLAccountID is not null
	update
		jobBank
	set
		GLAccountID = @GLAccountID
	where
		jobBankID = @jobBankID

---------------------------------------------------

set @GLAccountID = NULL
set @jobBankID = NULL

select 
	@GLAccountID = GLAccountID 
from 
	tr_GlAccounts 
where
	orgID = dbo.fn_getOrgIDFromOrgCode('CA')
	and accountCode = 'Jobbank'
	and status = 'A'

select 
	@jobBankID = jobBankID
from 
	dbo.jobBank jb
	inner join cms_applicationInstances ai on
		ai.applicationInstanceID = jb.applicationInstanceID
	inner join sites s on
		s.siteID = ai.siteID
		and s.siteCode = 'CA'

if @GLAccountID is not null
	update
		jobBank
	set
		GLAccountID = @GLAccountID
	where
		jobBankID = @jobBankID

---------------------------------------------------

set @GLAccountID = NULL
set @jobBankID = NULL

select 
	@GLAccountID = GLAccountID 
from 
	tr_GlAccounts 
where
	orgID = dbo.fn_getOrgIDFromOrgCode('XX')
	and accountCode = 'Jobbank'
	and status = 'A'

select 
	@jobBankID = jobBankID
from 
	dbo.jobBank jb
	inner join cms_applicationInstances ai on
		ai.applicationInstanceID = jb.applicationInstanceID
	inner join sites s on
		s.siteID = ai.siteID
		and s.siteCode = 'XX'

if @GLAccountID is not null
	update
		jobBank
	set
		GLAccountID = @GLAccountID
	where
		jobBankID = @jobBankID

---------------------------------------------------

set @GLAccountID = NULL
set @jobBankID = NULL

select 
	@GLAccountID = GLAccountID 
from 
	tr_GlAccounts 
where
	orgID = dbo.fn_getOrgIDFromOrgCode('SA')
	and accountCode = 'Jobbank'
	and status = 'A'

select 
	@jobBankID = jobBankID
from 
	dbo.jobBank jb
	inner join cms_applicationInstances ai on
		ai.applicationInstanceID = jb.applicationInstanceID
	inner join sites s on
		s.siteID = ai.siteID
		and s.siteCode = 'SA'

if @GLAccountID is not null
	update
		jobBank
	set
		GLAccountID = @GLAccountID
	where
		jobBankID = @jobBankID

---------------------------------------------------

set @GLAccountID = NULL
set @jobBankID = NULL

select 
	@GLAccountID = GLAccountID 
from 
	tr_GlAccounts 
where
	orgID = dbo.fn_getOrgIDFromOrgCode('WA')
	and accountCode = 'Jobbank'
	and status = 'A'

select 
	@jobBankID = jobBankID
from 
	dbo.jobBank jb
	inner join cms_applicationInstances ai on
		ai.applicationInstanceID = jb.applicationInstanceID
	inner join sites s on
		s.siteID = ai.siteID
		and s.siteCode = 'WA'

if @GLAccountID is not null
	update
		jobBank
	set
		GLAccountID = @GLAccountID
	where
		jobBankID = @jobBankID

---------------------------------------------------

set @GLAccountID = NULL
set @jobBankID = NULL

select 
	@GLAccountID = GLAccountID 
from 
	tr_GlAccounts 
where
	orgID = dbo.fn_getOrgIDFromOrgCode('LO')
	and accountCode = 'Jobbank'
	and status = 'A'

select 
	@jobBankID = jobBankID
from 
	dbo.jobBank jb
	inner join cms_applicationInstances ai on
		ai.applicationInstanceID = jb.applicationInstanceID
	inner join sites s on
		s.siteID = ai.siteID
		and s.siteCode = 'LO'

if @GLAccountID is not null
	update
		jobBank
	set
		GLAccountID = @GLAccountID
	where
		jobBankID = @jobBankID

---------------------------------------------------

set @GLAccountID = NULL
set @jobBankID = NULL

select 
	@GLAccountID = GLAccountID 
from 
	tr_GlAccounts 
where
	orgID = dbo.fn_getOrgIDFromOrgCode('TCRA')
	and accountCode = 'Jobbank'
	and status = 'A'

select 
	@jobBankID = jobBankID
from 
	dbo.jobBank jb
	inner join cms_applicationInstances ai on
		ai.applicationInstanceID = jb.applicationInstanceID
	inner join sites s on
		s.siteID = ai.siteID
		and s.siteCode = 'TCRA'

if @GLAccountID is not null
	update
		jobBank
	set
		GLAccountID = @GLAccountID
	where
		jobBankID = @jobBankID

---------------------------------------------------

set @GLAccountID = NULL
set @jobBankID = NULL

select 
	@GLAccountID = GLAccountID 
from 
	tr_GlAccounts 
where
	orgID = dbo.fn_getOrgIDFromOrgCode('NY')
	and accountCode = 'Jobbank'
	and status = 'A'

select 
	@jobBankID = jobBankID
from 
	dbo.jobBank jb
	inner join cms_applicationInstances ai on
		ai.applicationInstanceID = jb.applicationInstanceID
	inner join sites s on
		s.siteID = ai.siteID
		and s.siteCode = 'NY'

if @GLAccountID is not null
	update
		jobBank
	set
		GLAccountID = @GLAccountID
	where
		jobBankID = @jobBankID

---------------------------------------------------

set @GLAccountID = NULL
set @jobBankID = NULL

select 
	@GLAccountID = GLAccountID 
from 
	tr_GlAccounts 
where
	orgID = dbo.fn_getOrgIDFromOrgCode('NC')
	and accountCode = 'Jobbank'
	and status = 'A'

select 
	@jobBankID = jobBankID
from 
	dbo.jobBank jb
	inner join cms_applicationInstances ai on
		ai.applicationInstanceID = jb.applicationInstanceID
	inner join sites s on
		s.siteID = ai.siteID
		and s.siteCode = 'NC'

if @GLAccountID is not null
	update
		jobBank
	set
		GLAccountID = @GLAccountID
	where
		jobBankID = @jobBankID

---------------------------------------------------

set @GLAccountID = NULL
set @jobBankID = NULL

select 
	@GLAccountID = GLAccountID 
from 
	tr_GlAccounts 
where
	orgID = dbo.fn_getOrgIDFromOrgCode('CAAA')
	and accountCode = 'Jobbank'
	and status = 'A'

select 
	@jobBankID = jobBankID
from 
	dbo.jobBank jb
	inner join cms_applicationInstances ai on
		ai.applicationInstanceID = jb.applicationInstanceID
	inner join sites s on
		s.siteID = ai.siteID
		and s.siteCode = 'CAAA'

if @GLAccountID is not null
	update
		jobBank
	set
		GLAccountID = @GLAccountID
	where
		jobBankID = @jobBankID

---------------------------------------------------

set @GLAccountID = NULL
set @jobBankID = NULL

select 
	@GLAccountID = GLAccountID 
from 
	tr_GlAccounts 
where
	orgID = dbo.fn_getOrgIDFromOrgCode('EN')
	and accountCode = 'Jobbank'
	and status = 'A'

select 
	@jobBankID = jobBankID
from 
	dbo.jobBank jb
	inner join cms_applicationInstances ai on
		ai.applicationInstanceID = jb.applicationInstanceID
	inner join sites s on
		s.siteID = ai.siteID
		and s.siteCode = 'EN'

if @GLAccountID is not null
	update
		jobBank
	set
		GLAccountID = @GLAccountID
	where
		jobBankID = @jobBankID

---------------------------------------------------

set @GLAccountID = NULL
set @jobBankID = NULL

select 
	@GLAccountID = GLAccountID 
from 
	tr_GlAccounts 
where
	orgID = dbo.fn_getOrgIDFromOrgCode('GA')
	and accountCode = 'Jobbank'
	and status = 'A'

select 
	@jobBankID = jobBankID
from 
	dbo.jobBank jb
	inner join cms_applicationInstances ai on
		ai.applicationInstanceID = jb.applicationInstanceID
	inner join sites s on
		s.siteID = ai.siteID
		and s.siteCode = 'GA'

if @GLAccountID is not null
	update
		jobBank
	set
		GLAccountID = @GLAccountID
	where
		jobBankID = @jobBankID

---------------------------------------------------

set @GLAccountID = NULL
set @jobBankID = NULL

select 
	@GLAccountID = GLAccountID 
from 
	tr_GlAccounts 
where
	orgID = dbo.fn_getOrgIDFromOrgCode('PHL')
	and accountCode = 'Jobbank'
	and status = 'A'

select 
	@jobBankID = jobBankID
from 
	dbo.jobBank jb
	inner join cms_applicationInstances ai on
		ai.applicationInstanceID = jb.applicationInstanceID
	inner join sites s on
		s.siteID = ai.siteID
		and s.siteCode = 'PHL'

if @GLAccountID is not null
	update
		jobBank
	set
		GLAccountID = @GLAccountID
	where
		jobBankID = @jobBankID
GO

print '%%%%%%%%%% ALTER jobBank TBL, make GLAccountID column not nullable %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER TABLE jobBank ALTER COLUMN GLAccountID int NOT NULL
GO

print 'Creating relationship FK_jobBank_tr_GlAccounts...'
ALTER TABLE [jobBank]  WITH NOCHECK ADD  CONSTRAINT [FK_jobBank_tr_GlAccounts] FOREIGN KEY([GLAccountID])
REFERENCES [dbo].[tr_GlAccounts] ([GLAccountID])
GO

print '%%%%%%%%%% ALTER cms_createApplicationInstanceJobBank SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROCEDURE [dbo].[cms_createApplicationInstanceJobBank]
	@siteid int,
	@languageID int,
	@sectionID int,
	@isVisible bit,
	@pageName varchar(50),
	@pageTitle varchar(200),
	@pagedesc varchar(400),
	@zoneID int,
	@pageTemplateID int,
	@pageModeID int,
	@pgResourceTypeID int,
	@pgParentResourceID int = null,
	@allowReturnAfterLogin bit,
	@applicationInstanceName varchar(100),
	@applicationInstanceDesc varchar(200),
	@defaultGLAccountID	int,
	@applicationInstanceID int OUTPUT, @siteResourceID int OUTPUT, @pageID int OUTPUT
AS

-- null OUTPUT vars
SELECT @applicationInstanceID = null, @siteResourceID = null, @pageID = null

DECLARE @appCreatedSectionResourceTypeID int, @applicationTypeID int, @rootSectionID int, @rc int, @documentSectionName varchar(50)

select @applicationTypeID = applicationTypeID from cms_applicationTypes where applicationTypeName = 'JobBank'
select @appCreatedSectionResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedSection')
select @documentSectionName = 'JobBank ' + @pageName

BEGIN TRAN

exec @rc = dbo.cms_createApplicationInstance
		@siteid = @siteid,
		@languageID = @languageID,
		@sectionID = @sectionID,
		@applicationTypeID = @applicationTypeID,
		@isVisible = @isVisible,
		@pageName = @pageName,
		@pageTitle = @pageTitle,
		@pagedesc = @pagedesc,
		@zoneID = @zoneID,
		@pageTemplateID = @pageTemplateID,
		@pageModeID = @pageModeID,
		@pgResourceTypeID = @pgResourceTypeID,
		@pgParentResourceID = @pgParentResourceID,
		@allowReturnAfterLogin = @allowReturnAfterLogin,
		@applicationInstanceName = @applicationInstanceName,
		@applicationInstanceDesc = @applicationInstanceDesc,
		@applicationInstanceID = @applicationInstanceID OUTPUT,
		@siteResourceID = @siteResourceID OUTPUT,
		@pageID = @pageID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


exec @rc = dbo.cms_createPageSection
		@siteID = @siteID, 
		@sectionResourceTypeID = @appCreatedSectionResourceTypeID, 
		@ovTemplateID = NULL,
		@ovTemplateIDMobile=NULL,
		@ovModeID = NULL, 
		@parentSectionID = @sectionID, 
		@sectionName = @documentSectionName, 
		@sectionCode = @documentSectionName,
		@inheritPlacements = 1,
		@sectionID = @rootSectionID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- update parentSiteResourceID of section
update sr
set sr.parentSiteResourceID = @siteResourceID
from cms_pageSections s
	inner join cms_siteResources sr on s.siteResourceID = sr.siteResourceID
		and s.sectionID = @rootSectionID

insert into dbo.jobBank (applicationInstanceID, GLAccountID	)
values (@applicationInstanceID, @defaultGLAccountID	)
	IF @@ERROR <> 0 GOTO on_error

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1

GO
