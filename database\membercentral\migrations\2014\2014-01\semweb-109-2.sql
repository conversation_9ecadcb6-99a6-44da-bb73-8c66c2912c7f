USE [seminarWeb]
GO
/****** Object:  StoredProcedure [dbo].[sw_getEnrollmentHistoryByUserID]    Script Date: 01/23/2014 09:42:32 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROC [dbo].[sw_getEnrollmentHistoryByUserID]
@userID int,
@catalogOrgCode varchar(5)

AS

-- swl history that appears on this site's catalog
SELECT e.enrollmentID, s.seminarID, sswl.wddxTimeZones, sswl.dateStart, s.offerCertificate, 
	sswl.dateEnd, s.seminarName, e.dateCompleted, e.passed,
	CASE
	WHEN NOT EXISTS (select prid from dbo.tblSeminarsPreReqs where seminarID = s.seminarID) THEN 1
	ELSE (SELECT isnull((select TOP 1 CASE
			WHEN @userID = 0 THEN 0
			WHEN len(e2.dateCompleted) > 0 and e2.passed = 1 THEN 1
			ELSE 0
			END as preReqFulfilled
		 FROM dbo.tblSeminars as s2
		 INNER JOIN dbo.tblSeminarsPreReqs as pr on pr.preReqSeminarID = s2.seminarID
		 LEFT OUTER JOIN dbo.tblEnrollments as e2 on e2.seminarID = s2.seminarID and e2.userid = @userID and e2.isActive = 1
		 where pr.seminarID = s.seminarid
		 and s2.isDeleted = 0
		 ),0))
	END as preReqFulfilled,
	(SELECT COUNT(*) FROM dbo.tblLogSWLive AS log1 WHERE enrollmentID = e.enrollmentID AND seminarID = e.seminarID AND contact LIKE '%@%') AS EmailCount,
	(SELECT COUNT(*) FROM dbo.tblLogSWLive AS log2 WHERE enrollmentID = e.enrollmentID AND seminarID = e.seminarID AND contact NOT LIKE '%@%') AS FaxCount,
	(SELECT COUNT(*) FROM dbo.tblEnrollmentsAndCredit WHERE enrollmentID = e.enrollmentID) AS CreditCount
FROM dbo.tblEnrollments AS e 
INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
INNER JOIN dbo.tblSeminars AS s ON e.seminarID = s.seminarID 
INNER JOIN dbo.tblSeminarsSWLive AS sswl ON s.seminarID = sswl.seminarID
INNER JOIN dbo.swl_SeminarsInMyCatalogMy(@catalogOrgCode) as simc on simc.seminarID = s.seminarID
WHERE e.userID = @userID 
AND s.isDeleted = 0
AND e.isActive = 1
ORDER BY sswl.dateStart


-- swod history that appears on this site's catalog
-- DPC, 10/13/2011: I took out "AND s.isDeleted = 0" from the WHERE clause so I could 
-- display the Certificate link on the My History page for inactivated or deleted programs.
SELECT e.enrollmentID, s.seminarID, s.seminarName, e.dateEnrolled, e.dateCompleted, e.passed, 
	s.isPublished, s.offerCertificate,
	CASE
	WHEN NOT EXISTS (select prid from dbo.tblSeminarsPreReqs where seminarID = s.seminarID) THEN 1
	ELSE (SELECT isnull((select TOP 1 CASE
			WHEN @userID = 0 THEN 0
			WHEN len(e2.dateCompleted) > 0 and e2.passed = 1 THEN 1
			ELSE 0
			END as preReqFulfilled
		 FROM dbo.tblSeminars as s2
		 INNER JOIN dbo.tblSeminarsPreReqs as pr on pr.preReqSeminarID = s2.seminarID
		 LEFT OUTER JOIN dbo.tblEnrollments as e2 on e2.seminarID = s2.seminarID and e2.userid = @userID and e2.isActive = 1
		 where pr.seminarID = s.seminarid
		 ),0))
	END as preReqFulfilled
FROM dbo.tblEnrollments AS e 
INNER JOIN dbo.tblSeminars AS s ON e.seminarID = s.seminarID 
INNER JOIN dbo.tblEnrollmentsSWOD AS esod ON e.enrollmentID = esod.enrollmentID
INNER JOIN dbo.swod_SeminarsInMyCatalogMy(@catalogOrgCode) as simc on simc.seminarID = s.seminarID
WHERE e.userID = @userID 
	AND e.isActive = 1
ORDER BY s.seminarName

-- swtl history that appears on this site's catalog
SELECT e.enrollmentID, t.titleID, t.titleName
FROM dbo.tblEnrollments AS e 
INNER JOIN dbo.tblEnrollmentsSWTL AS eswtl ON e.enrollmentID = eswtl.enrollmentID 
INNER JOIN dbo.tblTitles AS t ON e.titleID = t.titleID
INNER JOIN dbo.swtl_TitlesInMyCatalogMy(@catalogOrgCode) as timc on timc.titleID = t.titleID
WHERE e.userID = @userID 
AND e.isActive = 1
AND t.isDeleted = 0
ORDER BY t.titleName

-- certificate programs
select cp.programID, cp.programName, sss.semstatusstring, s.seminarID, s.seminarName, s.isPublished, s.offerCertificate,
	e.dateCompleted, e.dateenrolled, e.passed, e.enrollmentID, sswod.seminarID as SWODID, dbo.swcp_getSeminarStatus(cp.programID,@userid,s.seminarid) as semStatus,
	sswl.seminarID as SWLID, sswl.dateStart,
	CASE
	WHEN NOT EXISTS (select prid from dbo.tblSeminarsPreReqs where seminarID = s.seminarID) THEN 1
	ELSE (SELECT isnull((select TOP 1 CASE
			WHEN @userID = 0 THEN 0
			WHEN len(e2.dateCompleted) > 0 and e2.passed = 1 THEN 1
			ELSE 0
			END as preReqFulfilled
		 FROM dbo.tblSeminars as s2
		 INNER JOIN dbo.tblSeminarsPreReqs as pr on pr.preReqSeminarID = s2.seminarID
		 LEFT OUTER JOIN dbo.tblEnrollments as e2 on e2.seminarID = s2.seminarID and e2.userid = @userID and e2.isActive = 1
		 where pr.seminarID = s.seminarID
		 and s2.isDeleted = 0
		 ),0))
	END as preReqFulfilled
FROM dbo.tblCertificatePrograms AS cp 
INNER JOIN dbo.tblCertificateProgramItems AS cpi ON cp.programID = cpi.programID 
INNER JOIN dbo.tblSeminars AS s ON cpi.seminarID = s.seminarID 
INNER JOIN (
	select distinct xcp.programid
	from dbo.tblCertificatePrograms AS xcp 
	INNER JOIN dbo.tblCertificateProgramItems AS xcpi ON xcp.programID = xcpi.programID 
	INNER JOIN dbo.tblEnrollments AS xe ON xcpi.seminarID = xe.seminarID 
	where xe.userID = @userID
	and xe.isactive = 1
) as tmp on tmp.programid = cp.programid
LEFT OUTER JOIN dbo.tblEnrollments as e on e.seminarID = s.seminarID AND e.userID = @userID and e.isactive = 1
LEFT OUTER JOIN dbo.tblSeminarsSWOD as sswod on sswod.seminarID = s.seminarID
LEFT OUTER JOIN dbo.tblSeminarsSWLive as sswl on sswl.seminarID = s.seminarID
cross apply dbo.swcp_getSeminarStatusString(cp.programID,@userid) as sss
ORDER BY cp.programName, cpi.itemOrder

-- self-reported CLE 
SELECT s.selfID, s.eventName, s.eventDate, s.userID, s.courseNumber, e.creditXML, ca.wddxcredittypes
FROM dbo.tblSelfReportedEvents AS s
INNER JOIN dbo.tblSelfReportEventAndCredit AS e ON s.selfID = e.selfID 
INNER JOIN dbo.tblCreditSponsorsAndAuthorities as csa on csa.csalinkID = e.csalinkID
INNER JOIN dbo.tblCreditAuthorities as ca on ca.authorityID = csa.authorityID
WHERE s.userID = @userid
ORDER BY s.eventDate desc, s.eventName

