ALTER PROC dbo.emailTracking_recordSendGridDeliveryEvent
@messagetype varchar(50),
@applicationtype varchar(20),
@applicationname varchar(100),
@periodCode int,
@event varchar(50),
@sgmessageid varchar(100),
@sgeventid varchar(100),
@timestamp datetime,
@membernumber varchar(100),
@sendingapplicationid int,
@sendingapplicationmessageid int,
@sendingapplicationrecipientid int,
@siteid int,
@emailusername varchar(100),
@emailDomainID int,
@emaildomain varchar(250),
@deliverystatusid int,
@attempt int,
@ippoolid int,
@sendgridsubuserid int,
@sendgridsubuserdomainid int,
@messagetemplate varchar(1000),
@messagesha1 varchar(40),
@smtpstatuscode varchar(25),
@messagetemplatematchfound bit,
@bounceClassificationID int = NULL

AS

SET XACT_ABORT, NOCOUNT ON;

SET @bounceClassificationID= NULLIF(@bounceClassificationID,0);

declare 
	@eventadded bit = 0,
	@statusprocessed int,
	@statusdropped int,
	@statusdeferred int,
	@statusdelivered int,
	@statusbounce int;

select @statusprocessed = deliveryStatusID from dbo.deliveryStatuses where statusCode='processed';
select @statusdropped = deliveryStatusID from dbo.deliveryStatuses where statusCode='dropped';
select @statusdeferred = deliveryStatusID from dbo.deliveryStatuses where statusCode='deferred';
select @statusdelivered = deliveryStatusID from dbo.deliveryStatuses where statusCode='delivered';
select @statusbounce = deliveryStatusID from dbo.deliveryStatuses where statusCode='bounce-technical';



-- get sendingapplicationid if NULL was passed in 
IF NULLIF(@sendingapplicationid,0) IS NULL
	EXEC dbo.emailTracking_createApplicationID @applicationName=@applicationName, @applicationType=@applicationType, @sendingApplicationID=@sendingapplicationid OUTPUT;

IF NULLIF(@emailDomainID,0) IS NULL
	EXEC dbo.emailTracking_createDomainID @domain=@emaildomain, @domainID=@emailDomainID OUTPUT;

IF NULLIF(@messagesha1,'') IS NOT NULL and @messagetemplatematchfound=0 and len(@messagetemplate) > 0
	EXEC dbo.emailTracking_createDeliveryMessageTemplate @statusMessageTemplate=@messagetemplate, @messageTemplateSHA1=@messagesha1;

-- create messageStats entry if it doesn't exist already
IF @sendingapplicationmessageid > 0 AND NOT EXISTS (
	select 1
	from dbo.messageStats
	where siteID = @siteID 
	and sendingApplicationID = @sendingapplicationid 
	and sendingApplicationMessageID = @sendingapplicationmessageid
) BEGIN
	SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

	insert into dbo.messageStats (sendingApplicationID, sendingApplicationMessageID, siteID, ipPoolID, sendgrid_subuserID,
		sendgrid_subuserDomainID, periodCode, uniqueOpens, uniqueClicks, totalOpens, totalClicks, totalSpamReports)
	select @sendingApplicationID, @sendingApplicationMessageID, @siteID, @ipPoolID, @sendgridsubuserid, @sendgridsubuserdomainid,
		@periodCode, 0, 0, 0, 0, 0
	WHERE NOT EXISTS (
		select 1
		from dbo.messageStats
		where siteID = @siteID 
		and sendingApplicationID = @sendingapplicationid 
		and sendingApplicationMessageID = @sendingapplicationmessageid
	);

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
END

-- create recipient entry if it doesn't exist already
IF NOT EXISTS (
	select 1
	from dbo.recipients 
	where siteID = @siteID 
	and sendingApplicationID = @sendingapplicationid 
	and sendingApplicationMessageID = @sendingapplicationmessageid 
	and sg_message_id = @sgmessageid
) BEGIN
	SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

	insert into dbo.recipients (sg_message_id, memberNumber, sendingApplicationID, sendingApplicationMessageID, siteID,
		username, domainID, DeliveryStatusID, sendingApplicationRecipientID, deliveryAttempts)
	select @sgmessageid, @membernumber, @sendingApplicationID, @sendingApplicationMessageID, @siteID, @emailusername,
		@emailDomainID, @deliverystatusid, @sendingapplicationrecipientid, 0
	WHERE NOT EXISTS (
		select 1
		from dbo.recipients 
		where siteID = @siteID 
		and sendingApplicationID = @sendingapplicationid 
		and sendingApplicationMessageID = @sendingapplicationmessageid 
		and sg_message_id = @sgmessageid
	);

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
END

-- acknowledge receipt by Sendgrid
-- status not being set either because either it was already set by the insert above or by another message
-- since they are processed in parallel, a delivered or deferred event may have already been processed
-- if this recipient was dropped, then there would be no other event and the insert above would have taken care of it
IF @event = 'processed' or @event = 'dropped'
	update recipients set 
		dateacknowledged = @timestamp,
		deliverySeconds = case when deliveryStatusID = @statusdelivered then datediff(second,@timestamp, dateLastAttempted) else deliverySeconds end
	where siteID = @siteID 
	and sendingApplicationID = @sendingapplicationid 
	and sendingApplicationMessageID = @sendingapplicationmessageid 
	and sg_message_id = @sgmessageid;

IF @event = 'delivered'
	update recipients 
	set dateLastAttempted = @timestamp,
		deliveryAttempts = deliveryAttempts+1,
		deliveryStatusID = @deliverystatusid,
		deliverySeconds =  datediff(second,dateAcknowledged, @timestamp)
	where siteID = @siteID 
	and sendingApplicationID = @sendingapplicationid 
	and sendingApplicationMessageID = @sendingapplicationmessageid 
	and sg_message_id = @sgmessageid;

IF @event = 'deferred' or @event = 'dropped' or @event = 'bounce' or @event = 'failed'  BEGIN
	-- create messageStats entry if it doesn't exist already
	IF NOT EXISTS (
		select 1
		from dbo.recipientDeliveryMessages
		where siteID = @siteID 
		and sendingApplicationID = @sendingapplicationid 
		and sendingApplicationMessageID = @sendingapplicationmessageid 
		and sg_event_id = @sgeventid
	) BEGIN
		SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

		insert into dbo.recipientDeliveryMessages (sg_event_id, sg_message_id, memberNumber, sendingApplicationID,
			sendingApplicationMessageID, siteID, username, domainID, periodCode, dateReceived, smtpStatusCode,
			MessageTemplateSHA1, hasBeenAggregated, deliveryStatusID)
		select @sgeventid, @sgmessageid, @membernumber, @sendingApplicationID, @sendingApplicationMessageID, @siteID, 
			@emailusername, @emailDomainID, @periodCode, @timestamp, @smtpstatuscode, @messagesha1, 0, @deliverystatusid
		WHERE NOT EXISTS (
			select 1
			from dbo.recipientDeliveryMessages
			where siteID = @siteID 
			and sendingApplicationID = @sendingapplicationid 
			and sendingApplicationMessageID = @sendingapplicationmessageid 
			and sg_event_id = @sgeventid
		);

		IF @@ROWCOUNT > 0
			set @eventadded = 1;

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	END
END

IF @event = 'deferred' and @eventadded = 1
	update recipients 
	set dateLastAttempted = @timestamp,
		deliveryAttempts = @attempt,
		deliveryStatusID = @deliverystatusid
	where siteID = @siteID 
	and sendingApplicationID = @sendingapplicationid 
	and sendingApplicationMessageID = @sendingapplicationmessageid 
	and sg_message_id = @sgmessageid
	and deliveryAttempts < @attempt
	and DeliveryStatusID <> @statusdelivered;

IF @event = 'bounce' and @eventadded = 1
	update recipients 
	set dateLastAttempted = @timestamp,
		deliveryStatusID = @deliverystatusid
	where siteID = @siteID 
	and sendingApplicationID = @sendingapplicationid 
	and sendingApplicationMessageID = @sendingapplicationmessageid 
	and sg_message_id = @sgmessageid;

IF @event = 'failed' and @eventadded = 1
	update recipients 
	set dateLastAttempted = @timestamp,
		deliveryAttempts = @attempt,
		deliveryStatusID = @deliverystatusid
	where siteID = @siteID 
	and sendingApplicationID = @sendingapplicationid 
	and sendingApplicationMessageID = @sendingapplicationmessageid 
	and sg_message_id = @sgmessageid
	and deliveryAttempts < @attempt
	and DeliveryStatusID <> @statusdelivered;

/*
IF @event = 'failed' AND @statusbounce = @deliverystatusid BEGIN
	DECLARE @timeNow datetime = getDate();
	DECLARE @emailAddress varchar(200) = @emailusername + '@' + @emaildomain;
	EXEC MEMBERCENTRAL.platformMail.dbo.email_addLyrisSuppressionList @emailAddress=@emailAddress, 
		@statusCode='sg_bounce', @siteID=@siteID, @subuserID=@sendgridsubuserid, @sgReason=NULL, @sgStatus=@smtpstatuscode, 
		@responseDate= @timeNow, @classID=@bounceClassificationID;
END
*/

RETURN 0;
GO
