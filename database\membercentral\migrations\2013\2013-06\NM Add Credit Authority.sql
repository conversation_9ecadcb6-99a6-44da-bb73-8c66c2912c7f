use membercentral
GO

declare @orgCode varchar(10), @authorityID int, @sponsorID int, @orgID int, @ASID int, @typeID1 int, @typeID2 int
set @orgcode = 'NM'
select @orgID = orgID from organizations where orgcode = 'NM'

insert into dbo.crd_authorities (authorityName, contact, address, city, state, ZIP, phone, fax, email, website, creditIDText)
values ('New Mexico MCLE Commission', '', 'PO Box 93070', 'Albuquerque', 'NM', '87199', '', '', '', 'http://www.nmmcle.org', 'ID Number')
	select @authorityID = SCOPE_IDENTITY()

insert into dbo.crd_authorityTypes (authorityID, typeName)
values (@authorityID, 'General')
	select @typeID1 = SCOPE_IDENTITY()

insert into dbo.crd_authorityTypes (authorityID, typeName)
values (@authorityID, 'Ethics')
	select @typeID2 = SCOPE_IDENTITY()

insert into dbo.crd_sponsors (orgID, statementAppProvider, statementAppProgram, statementPendProgram)
values (@orgID, '', '', '')
	select @sponsorID = SCOPE_IDENTITY()

insert into dbo.crd_authoritySponsors (authorityID, sponsorID, certificateMessage, creditMessage, affirmationFileName)
values (@authorityID, @sponsorID, '', '', null)
	select @ASID = SCOPE_IDENTITY()

insert into dbo.crd_authoritySponsorTypes (ASID, typeID, ovTypeName, LiveApprovedCertificateID, LiveDeniedCertificateID)
values (@ASID, @typeID1, null, null, null)
insert into dbo.crd_authoritySponsorTypes (ASID, typeID, ovTypeName, LiveApprovedCertificateID, LiveDeniedCertificateID)
values (@ASID, @typeID2, null, null, null)

GO