ALTER PROC dbo.sw_moveSponsorGrouping
@sponsorGrouping<PERSON> INT,
@direction VARCHAR(10)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF @direction NOT IN ('up','down')
		RAISERROR('Invalid direction. Must be up or down.',16,1);

	DECLARE @seminarID INT, @participantID INT, @currentOrder INT, @swapOrder INT, @swapGroupingID INT;
	
	SELECT @seminarID = seminarID, @participantID = participantID, @currentOrder = sponsorGroupingOrder 
	FROM dbo.sw_sponsorGrouping 
	WHERE sponsorGroupingID = @sponsorGroupingID;

	IF @seminarID IS NULL
		RAISERROR('Sponsor Grouping does not exist.',16,1);

	-- Find the grouping to swap with
	IF @direction = 'up'
	BEGIN
		SELECT TOP 1 @swapGroupingID = sponsorGroupingID, @swapOrder = sponsorGroupingOrder
		FROM dbo.sw_sponsorGrouping
		WHERE seminarID = @seminarID AND participantID = @participantID AND sponsorGroupingOrder < @currentOrder
		ORDER BY sponsorGroupingOrder DESC;
	END
	ELSE
	BEGIN
		SELECT TOP 1 @swapGroupingID = sponsorGroupingID, @swapOrder = sponsorGroupingOrder
		FROM dbo.sw_sponsorGrouping
		WHERE seminarID = @seminarID AND participantID = @participantID AND sponsorGroupingOrder > @currentOrder
		ORDER BY sponsorGroupingOrder ASC;
	END

	-- Perform the swap if we found a grouping to swap with
	IF @swapGroupingID IS NOT NULL
	BEGIN
		UPDATE dbo.sw_sponsorGrouping SET sponsorGroupingOrder = @swapOrder WHERE sponsorGroupingID = @sponsorGroupingID;
		UPDATE dbo.sw_sponsorGrouping SET sponsorGroupingOrder = @currentOrder WHERE sponsorGroupingID = @swapGroupingID;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
