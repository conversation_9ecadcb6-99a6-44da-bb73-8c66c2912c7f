use membercentral
GO

-- link fn to resource and add to roles
declare @functionID int, @resourceTypeIDSite int, @resourceTypeFunctionID int
select @functionID = functionID from cms_siteResourceFunctions where functionName = 'viewAccountingReports'
select @resourceTypeIDSite = dbo.fn_getResourceTypeID('Site')

insert into cms_siteResourceTypeFunctions(resourceTypeID, functionID)
values (@resourceTypeIDSite,@functionID)

SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeIDSite,dbo.fn_getResourceFunctionID('viewAccountingReports',@resourceTypeIDSite))
EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID
GO

-- anything that uses old RTFID needs to use new RTFID
declare @resourceTypeIDAdmin int, @resourceTypeFunctionIDold int, @resourceTypeIDSite int, @resourceTypeFunctionIDnew int
select @resourceTypeIDAdmin = dbo.fn_getResourceTypeID('Admin')
SELECT @resourceTypeFunctionIDold = dbo.fn_getResourceTypeFunctionID(@resourceTypeIDAdmin,dbo.fn_getResourceFunctionID('viewAccountingReports',@resourceTypeIDAdmin))
select @resourceTypeIDSite = dbo.fn_getResourceTypeID('Site')
SELECT @resourceTypeFunctionIDnew = dbo.fn_getResourceTypeFunctionID(@resourceTypeIDSite,dbo.fn_getResourceFunctionID('viewAccountingReports',@resourceTypeIDSite))
update dbo.admin_functionsDeterminingNav set resourceTypeFunctionID = @resourceTypeFunctionIDnew where resourceTypeFunctionID = @resourceTypeFunctionIDold
GO

-- delete old fn from roles and delete old fn from admin
declare @resourceTypeID int, @resourceTypeFunctionID int
select @resourceTypeID = dbo.fn_getResourceTypeID('Admin')
SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('viewAccountingReports',@resourceTypeID))
EXEC dbo.cms_deleteSiteResourceRoleFunction 9, @resourceTypeFunctionID
EXEC dbo.cms_deleteSiteResourceRoleFunction 10, @resourceTypeFunctionID
delete from cms_siteResourceTypeFunctions where resourceTypeFunctionID = @resourceTypeFunctionID
GO

