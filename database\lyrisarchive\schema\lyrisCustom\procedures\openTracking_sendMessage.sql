ALTER PROC dbo.openTracking_sendMessage
@xmlMessage xml

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	declare @DialogHandleFinal uniqueidentifier;
	set @DialogHandleFinal = NEWID();

	BEGIN DIALOG CONVERSATION @DialogHandleFinal
		FROM SERVICE [OpenTrackingQueue/OpenTrackingService]
		TO SERVICE N'OpenTrackingQueue/OpenTrackingService'
		ON CONTRACT [OpenTrackingQueue/OpenTrackingContract]
		WITH ENCRYPTION = OFF;
	SEND ON CONVERSATION @DialogHandleFinal 
		MESSAGE TYPE [OpenTrackingQueue/OpenTrackingRequest] (@xmlMessage);

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC up_ErrorHandler;
	RETURN -1;
END CATCH
GO
