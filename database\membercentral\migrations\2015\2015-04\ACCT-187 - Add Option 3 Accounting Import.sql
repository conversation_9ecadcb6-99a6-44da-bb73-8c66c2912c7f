use membercentral
GO
CREATE PROC [dbo].[tr_importTransactionsOpt3_toTemp]
@orgid int, 
@payProfileCode varchar(20),
@tmptbl varchar(30),
@pathToExport varchar(100),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

IF OBJECT_ID('tempdb..#tblAccErrors') IS NOT NULL 
	DROP TABLE #tblAccErrors
CREATE TABLE #tblAccErrors (rowid int IDENTITY(1,1), msg varchar(300), fatal bit)

declare @var_tmpCols varchar(20), @orgCode varchar(10), @payProfileID int, 
	@payProfileGatewayID int, @dynSQL nvarchar(max), @good bit, @flatfile varchar(160), @bcpprefix varchar(50),
	@exportcmd varchar(400)

select @orgcode=orgcode from dbo.organizations where orgID = @orgid
set @var_tmpCols = '##tmpAccCols' + @orgcode
set @bcpprefix = @orgcode + '_acc_flat_' + convert(varchar(8),getdate(),112) + replace(convert(varchar(8),getdate(),108),':','')
set @flatfile = @pathToExport + @bcpprefix
select TOP 1 @payProfileID = mp.profileID, @payProfileGatewayID = mp.gatewayID
	from dbo.mp_profiles as mp
	inner join dbo.sites as s on s.siteID = mp.siteID
	where s.orgID = @orgID
	and mp.profileCode = @payProfileCode 
	and mp.status = 'A'


/* ************** */
/* initial checks */
/* ************** */
IF @payProfileID is null BEGIN
	INSERT INTO #tblAccErrors (msg, fatal)
	VALUES ('Unable to locate Payment Method.',1)

	GOTO on_done
END


-- ******************************** 
-- ensure all columns exist (fatal)
-- ******************************** 
BEGIN TRY
	IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
		EXEC('DROP TABLE ' + @var_tmpCols)
	IF OBJECT_ID('tempdb..#tblAccOrgCols') IS NOT NULL 
		DROP TABLE #tblAccOrgCols
	IF OBJECT_ID('tempdb..#tblAccImportCols') IS NOT NULL 
		DROP TABLE #tblAccImportCols

	-- this will get the columns that should be there
	CREATE TABLE #tblAccOrgCols (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
		COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
		SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
		COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
		CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)

		select @dynSQL = '	
			select cast(null as datetime) as batchDate, cast(null as int) as batchNumber,
				cast(null as varchar(50)) as payMemberID, cast(null as datetime) as payDate, cast(null as money) as payAmount,
				cast(null as varchar(200)) as payCashGL, cast(null as varchar(30)) as payID, cast(null as varchar(200)) as payReference, 
				cast(null as varchar(max)) as saleDescription, cast(null as datetime) as saleDate, cast(null as varchar(50)) as saleMemberID, 
				cast(null as money) as saleAmount, cast(null as varchar(200)) as saleRevenueGL, cast(null as varchar(30)) as saleID,
				cast(null as int) as rowID
			into ' + @var_tmpCols + '
			from dbo.tr_glAccounts
			where 1=0 '
		EXEC(@dynSQL)

		-- get cols
		INSERT INTO #tblAccOrgCols
		EXEC tempdb.dbo.SP_COLUMNS @var_tmpCols
		
		-- cleanup table no longer needed
		IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
			EXEC('DROP TABLE ' + @var_tmpCols)

	-- this will get the columns that are actually in the import
	CREATE TABLE #tblAccImportCols (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
		COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
		SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
		COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
		CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)

		-- get cols
		INSERT INTO #tblAccImportCols
		EXEC tempdb.dbo.SP_COLUMNS @tmptbl

	INSERT INTO #tblAccErrors (msg, fatal)
	select 'The column ' + org.column_name + ' is missing from your data.', 1
	from #tblAccOrgCols as org
	left outer join #tblAccImportCols as imp on imp.column_name = org.column_name
	where imp.table_name is null

	IF @@ROWCOUNT > 0
		GOTO on_done
END TRY
BEGIN CATCH
	INSERT INTO #tblAccErrors (msg, fatal)
	VALUES ('Unable to process accounting file.',1)

	GOTO on_done
END CATCH


-- **********************
-- columns checks (fatal)
-- **********************
BEGIN TRY
	select @dynSQL = 'ALTER TABLE ' + @tmptbl + ' ADD payMID int null, saleMID int null, payCashGLAID int null, saleRevenueGLAID int null, invoiceProfileID int null'
	EXEC(@dynSQL)

	-- ensure batchDate is datetime (allow nulls for this check)
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [batchDate] datetime null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO #tblAccErrors (msg, fatal)
		VALUES ('The column batchDate contains invalid dates.', 1)

	-- check for null batchDate
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [batchDate] datetime not null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' is missing the required BatchDate.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE batchDate IS NULL
			ORDER BY rowID'
		INSERT INTO #tblAccErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	-- ensure batchNumber is int (allow nulls for this check)
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [batchNumber] int null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO #tblAccErrors (msg, fatal)
		VALUES ('The column batchNumber contains invalid numbers.', 1)

	-- check for null batchNumber
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [batchNumber] int not null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' is missing the required BatchNumber.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE BatchNumber IS NULL
			ORDER BY rowID'
		INSERT INTO #tblAccErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	-- check for unique batchNum amongst distinct batch cols 
	select @dynSQL = 'SELECT TOP 100 PERCENT ''BatchNumber '' + cast(batchNumber as varchar(20)) + '' is not unique. The same batchNumber appears with different batch dates.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		where BatchNumber is not null
		group by batchNumber
		having count(distinct batchDate) > 1
		order by batchNumber'
	INSERT INTO #tblAccErrors (msg, fatal)
	EXEC(@dynSQL)

	-- match payMemberID on membernumber
	select @dynSQL = 'update tmp
		set tmp.payMID = m.memberid
		from ' + @tmptbl + ' as tmp
		inner join membercentral.dbo.ams_members as m on m.memberNumber = tmp.payMemberID
			and m.orgID = ' + cast(@orgID as varchar(5)) + '
			and m.memberID = m.activeMemberID'
	EXEC(@dynSQL)

	-- check for missing member matches for payMID
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN payMID int not null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(payMemberID,'''') + '') does not match an existing MemberNumber for PayMemberID.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE payMID IS NULL
			ORDER BY rowID'
		INSERT INTO #tblAccErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	-- ensure payDate is datetime (allow nulls for this check)
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [payDate] datetime null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO #tblAccErrors (msg, fatal)
		VALUES ('The column payDate contains invalid dates.', 1)

	-- check for null payDate
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [payDate] datetime not null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' is missing the required PayDate.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE payDate IS NULL
			ORDER BY rowID'
		INSERT INTO #tblAccErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	-- clean payAmount amounts
	select @dynSQL = 'update ' + @tmptbl + ' set payAmount = membercentral.dbo.fn_regexReplace(payAmount,''[^0-9\.\-\(\)]'','''') where payAmount is not null;'
	EXEC(@dynSQL)

	-- ensure payAmount is money (allow nulls for this check)
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [payAmount] money null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO #tblAccErrors (msg, fatal)
		VALUES ('The column payAmount contains invalid amounts.', 1)

	-- check for null or negative payAmount amounts
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [payAmount] money not null;
			ALTER TABLE ' + @tmptbl + ' ADD CONSTRAINT amtCheckPay CHECK (payAmount >= 0);
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' does not have a positive PayAmount.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE payAmount IS NULL OR payAmount < 0
			ORDER BY rowID'
		INSERT INTO #tblAccErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	-- match on payCashGL
	select @dynSQL = 'update tmp
		set tmp.payCashGLAID = gl.glAccountID
		from ' + @tmptbl + ' as tmp
		inner join membercentral.dbo.tr_GlAccounts as gl on gl.accountCode = tmp.payCashGL
			and gl.accountTypeID = 1
			and gl.status = ''A''
			and gl.orgID = ' + cast(@orgID as varchar(5))
	EXEC(@dynSQL)

	-- check for null payCashGL GLs
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN payCashGLAID int not null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' does not match an existing cash GL Account Code.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE payCashGLAID IS NULL
			ORDER BY rowID'
		INSERT INTO #tblAccErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	-- check for null payID
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN payID varchar(30) not null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO #tblAccErrors (msg, fatal)
		VALUES ('The column payID contains blank values.', 1)

	-- check for unique payID amongst distinct pay cols
	select @dynSQL = 'SELECT TOP 100 PERCENT ''PayID '' + cast(payID as varchar(20)) + '' is not unique. The same payID appears with different pay information.'' as msg, 1 as fatal 
		from (
			select distinct payMemberID, payDate, payAmount, payCashGL, payID, payReference
			FROM ' + @tmptbl + ' 
		) as tmp
		group by payID
		having count(*) > 1
		order by PayID'
	INSERT INTO #tblAccErrors (msg, fatal)
	EXEC(@dynSQL)

	-- check for null sale descriptions
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [saleDescription] varchar(max) not null;
			update ' + @tmptbl + ' set saleDescription = null where saleDescription = '''';
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' is missing the required SaleDescription.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE (saleDescription IS NULL OR ltrim(rtrim(saleDescription)) = '''')
			ORDER BY rowID'
		INSERT INTO #tblAccErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	-- ensure saleDate is datetime (allow nulls for this check)
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [saleDate] datetime null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO #tblAccErrors (msg, fatal)
		VALUES ('The column saleDate contains invalid dates.', 1)

	-- check for null saleDate
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [saleDate] datetime not null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' is missing the required SaleDate.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE saleDate IS NULL
			ORDER BY rowID'
		INSERT INTO #tblAccErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	-- match saleMemberID on membernumber
	select @dynSQL = 'update tmp
		set tmp.saleMID = m.memberid
		from ' + @tmptbl + ' as tmp
		inner join membercentral.dbo.ams_members as m on m.memberNumber = tmp.saleMemberID
			and m.orgID = ' + cast(@orgID as varchar(5)) + '
			and m.memberID = m.activeMemberID'
	EXEC(@dynSQL)

	-- check for missing member matches for saleMID
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN saleMID int not null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' does not match an existing MemberNumber for SaleMemberID.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE saleMID IS NULL
			ORDER BY rowID'
		INSERT INTO #tblAccErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	-- clean saleAmount amounts
	select @dynSQL = 'update ' + @tmptbl + ' set saleAmount = membercentral.dbo.fn_regexReplace(saleAmount,''[^0-9\.\-\(\)]'','''') where saleAmount is not null;'
	EXEC(@dynSQL)

	-- ensure saleAmount is money (allow nulls for this check)
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [saleAmount] money null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO #tblAccErrors (msg, fatal)
		VALUES ('The column saleAmount contains invalid amounts.', 1)

	-- check for null or negative saleAmount amounts
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [saleAmount] money not null;
			ALTER TABLE ' + @tmptbl + ' ADD CONSTRAINT amtCheckSale CHECK (saleAmount >= 0);
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' does not have a positive sale amount.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE saleAmount IS NULL OR saleAmount < 0
			ORDER BY rowID'
		INSERT INTO #tblAccErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	-- match saleRevenueGL on GL
	select @dynSQL = 'update tmp
		set tmp.saleRevenueGLAID = gl.glAccountID,
			tmp.invoiceProfileID = gl.invoiceProfileID
		from ' + @tmptbl + ' as tmp
		inner join membercentral.dbo.tr_GlAccounts as gl on gl.accountCode = tmp.saleRevenueGL
			and gl.accountTypeID = 3
			and gl.status = ''A''
			and gl.orgID = ' + cast(@orgID as varchar(5))
	EXEC(@dynSQL)

	-- check for null saleRevenueGL GLs
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN saleRevenueGLAID int not null;
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN invoiceProfileID int not null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' does not match an existing revenue GL Account Code.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE saleRevenueGLAID IS NULL OR invoiceProfileID is null
			ORDER BY rowID'
		INSERT INTO #tblAccErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	-- check for null saleID
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN saleID varchar(30) not null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO #tblAccErrors (msg, fatal)
		VALUES ('The column saleID contains blank values.', 1)

	-- check for unique saleID amongst distinct sale cols
	select @dynSQL = 'SELECT TOP 100 PERCENT ''SaleID '' + cast(saleID as varchar(20)) + '' is not unique. The same saleID appears with different sale information.'' as msg, 1 as fatal 
		from (
			select distinct saleDescription, saleDate, saleMemberID, saleAmount, saleRevenueGL, saleID 
			FROM ' + @tmptbl + ' 
		) as tmp
		group by saleID
		having count(*) > 1
		order by SaleID'
	INSERT INTO #tblAccErrors (msg, fatal)
	EXEC(@dynSQL)

	-- check for under allocated payments
	select @dynSQL = 'SELECT TOP 100 PERCENT ''PayID '' + cast(payID as varchar(10)) + '' is under-allocated.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		group by payID, payAmount
		having payAmount > sum(saleAmount)
		order by PayID'
	INSERT INTO #tblAccErrors (msg, fatal)
	EXEC(@dynSQL)

	-- check for over allocated payments
	select @dynSQL = 'SELECT TOP 100 PERCENT ''PayID '' + cast(payID as varchar(10)) + '' is over-allocated.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		group by payID, payAmount
		having payAmount < sum(saleAmount)
		order by PayID'
	INSERT INTO #tblAccErrors (msg, fatal)
	EXEC(@dynSQL)
END TRY
BEGIN CATCH
	INSERT INTO #tblAccErrors (msg, fatal)
	VALUES ('Unable to validate accounting file.',1)

	INSERT INTO #tblAccErrors (msg, fatal)
	VALUES (error_message(),0)

	GOTO on_done
END CATCH


IF (select count(*) from #tblAccErrors) = 0 BEGIN
	/* ***************** */
	/* get expanded data */
	/* ***************** */
	BEGIN TRY
		IF OBJECT_ID('tempdb..##tmpAccImportSales') IS NOT NULL 
			DROP TABLE ##tmpAccImportSales
		CREATE TABLE ##tmpAccImportSales (autoid int IDENTITY(1,1), saleID varchar(30)) 
		select @dynSQL = 'select distinct saleID from ' + @tmptbl
		insert into ##tmpAccImportSales (saleID)
		EXEC(@dynSQL)

		IF OBJECT_ID('tempdb..##tmpAccImportFull') IS NOT NULL 
			DROP TABLE ##tmpAccImportFull
		CREATE TABLE ##tmpAccImportFull (autoid int, batchID int, batchDate datetime, batchCode varchar(40), batchName varchar(400), 
			payTID int, payHistoryID int, payMemberID int, payDate datetime, payAmount money, payCashGLAID int, payID varchar(30), 
			payReference varchar(200), payDescription varchar(max), invoiceID int, invoiceProfileID int, saleTID int, 
			saleDescription varchar(max), saleDate datetime, saleMemberID int, saleAmount money, saleRevenueGLAID int, 
			saleID varchar(30), allocTID int, payProfileID int, payProfileGatewayID int)
		select @dynSQL = ' 
			select tmp.rowID as autoid, tmp2.autoid as saleAutoID, 
				cast(null as int) as batchID, DATEADD(dd,DATEDIFF(dd,0,tmp.batchDate),0) as batchDate, 
				convert(varchar(8),tmp.batchDate,112) + ''_'' + tmp.batchNumber + '''' as batchCode,
				convert(varchar(8),tmp.batchDate,112) + '' ' + @payProfileCode + ' '' + tmp.batchNumber as batchName,
				cast(null as int) as payTID, cast(null as int) as payHistoryID, tmp.payMID as payMemberID, tmp.payDate, 
				tmp.payAmount, tmp.payCashGLAID, tmp.payID, tmp.payReference, ''Payment for '' + tmp.saleDescription as payDescription, 
				cast(null as int) as invoiceID, tmp.invoiceProfileID, cast(null as int) as saleTID, 
				tmp.saleDescription, tmp.saleDate, tmp.saleMID as saleMemberID, tmp.saleAmount, tmp.saleRevenueGLAID, tmp.saleID,
				cast(null as int) as allocTID, ' + cast(@payProfileID as varchar(10)) + ' as payProfileID, ' + 
				cast(@payProfileGatewayID as varchar(10)) + ' as payProfileGatewayID  
			from ' + @tmptbl + ' as tmp
			inner join ##tmpAccImportSales as tmp2 on tmp2.saleID = tmp.saleID '
		insert into ##tmpAccImportFull
		EXEC(@dynSQL)

		IF OBJECT_ID('tempdb..##tmpAccImportSales') IS NOT NULL 
			DROP TABLE ##tmpAccImportSales
	END TRY
	BEGIN CATCH
		INSERT INTO #tblAccErrors (msg, fatal)
		VALUES ('Unable to prepare final accounting data for final import.',1)

		INSERT INTO #tblAccErrors (msg, fatal)
		VALUES (error_message(),0)

		set @flatfile = ''

		GOTO on_done
	END CATCH


	-- *****************************
	-- check batches. cannot put new payments on an already-created and NOT OPEN batch.
	-- *****************************
	INSERT INTO #tblAccErrors (msg, fatal)
	select TOP 100 PERCENT 'We cannot add transactions from ' + convert(varchar(10),batchDate,101) + ' to closed or posted batches.', 1
	from ##tmpAccImportFull as tmp 
	where payAmount > 0
	and exists (
		select batchID 
		from dbo.tr_batches 
		where orgID = @orgID 
		and (batchCode = tmp.batchCode OR batchName = tmp.batchName)
		and statusID <> 1
	)
	group by convert(varchar(10),batchDate,101)
	order by convert(varchar(10),batchDate,101)
	
	IF @@ROWCOUNT > 0 BEGIN
		set @flatfile = ''
		GOTO on_done
	END


	-- ******************************** 
	-- dump flat table and format file and creation script
	-- would love to use xml format files, but it appears column names with spaces cause it to fail
	-- ******************************** 
	BEGIN TRY
		select @exportcmd = 'bcp ##tmpAccImportFull format nul -f ' + @flatfile + '.txt -n -T -S' + CAST(serverproperty('servername') as varchar(40))
		EXEC master..xp_cmdshell @exportcmd, NO_OUTPUT
		select @exportcmd = 'bcp ##tmpAccImportFull out ' + @flatfile + '.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
		EXEC master..xp_cmdshell @exportcmd, NO_OUTPUT

		declare @coltypes table (datatype varchar(16))          
		insert into @coltypes values('bit')          
		insert into @coltypes values('binary')          
		insert into @coltypes values('bigint')          
		insert into @coltypes values('int')          
		insert into @coltypes values('float')          
		insert into @coltypes values('datetime')          
		insert into @coltypes values('text')          
		insert into @coltypes values('image')          
		insert into @coltypes values('money')          
		insert into @coltypes values('uniqueidentifier')          
		insert into @coltypes values('smalldatetime')          
		insert into @coltypes values('tinyint')          
		insert into @coltypes values('smallint')          
		insert into @coltypes values('sql_variant')          
		select @dynSQL = ''
		select @dynSQL = @dynSQL +           
			case when charindex('(',@dynSQL,1)<=0 then '(' else '' end + '[' + Column_Name + '] ' +Data_Type +
			case when Data_Type in (Select datatype from @coltypes) then '' else  '(' end+
			case when data_type in ('real','decimal','numeric')  then cast(isnull(numeric_precision,'') as varchar)+','+
			case when data_type in ('real','decimal','numeric') then cast(isnull(Numeric_Scale,'') as varchar) end
			when data_type in ('nvarchar','varchar') and cast(isnull(Character_Maximum_Length,'') as varchar) = '-1' then 'max'
			when data_type in ('char','nvarchar','varchar','nchar') then cast(isnull(Character_Maximum_Length,'') as varchar) else '' end+
			case when Data_Type in (Select datatype from @coltypes)then '' else  ')' end+
			case when Is_Nullable='No' then ' Not null,' else ' null,' end
			from tempdb.Information_Schema.COLUMNS where Table_Name='##tmpAccImportFull'
			order by ordinal_position
		select @dynSQL='Create table ##xxx ' + substring(@dynSQL,1,len(@dynSQL)-1) +' )'            
		if dbo.fn_WriteFile(@flatfile + '.sql', @dynSQL, 1) = 0 BEGIN
			RAISERROR('Error raised in TRY block.', 16, 1);
		END
	END TRY
	BEGIN CATCH
		INSERT INTO #tblAccErrors (msg, fatal)
		VALUES ('Unable to save accounting data for final import.',1)

		set @flatfile = ''

		GOTO on_done
	END CATCH
END ELSE
	set @flatfile = ''


-- ************************
-- generate result xml file 
-- ************************
on_done:
	select @importResult = (
		select getdate() as "@date", @flatfile as "@flatfile", 
			isnull((select top 301 dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg", "@severity" = case fatal when 1 then 'fatal' else 'nonfatal' end
			from #tblAccErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE)
	
	IF OBJECT_ID('tempdb..#tblAccOrgCols') IS NOT NULL 
		DROP TABLE #tblAccOrgCols
	IF OBJECT_ID('tempdb..#tblAccImportCols') IS NOT NULL 
		DROP TABLE #tblAccImportCols
	IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
		EXEC('DROP TABLE ' + @var_tmpCols)
	IF OBJECT_ID('tempdb..#tblAccErrors') IS NOT NULL 
		DROP TABLE #tblAccErrors
	IF OBJECT_ID('tempdb..##tmpAccImportFull') IS NOT NULL 
		DROP TABLE ##tmpAccImportFull

RETURN 0
GO

CREATE PROC [dbo].[tr_importTransactionsOpt3_toPerm]
@orgID int,
@siteID int,
@recordedByMemberID int,
@statsSessionID int,
@flatfile varchar(160),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

DECLARE @paymentcreditGLAccountID int, @saledebitGLAccountID int, @allocCreditGLAccountID int, @allocDebitGLAccountID int,
	@createSQL varchar(max), @cmd varchar(400), @invoicenum int, @nowDate datetime

select @paymentcreditGLAccountID = glaccountid 
	from dbo.tr_GLAccounts 
	where orgID = @orgID
	and isSystemAccount = 1
	and GLCode = 'DEPOSITS'
	and [status] = 'A'
select @saledebitGLAccountID = glaccountid 
	from dbo.tr_GLAccounts 
	where orgID = @orgID
	and isSystemAccount = 1
	and GLCode = 'ACCOUNTSRECEIVABLE'
	and [status] = 'A'
select @allocCreditGLAccountID = @saledebitGLAccountID
select @allocDebitGLAccountID = @paymentcreditGLAccountID
set @nowDate = getdate()


IF OBJECT_ID('tempdb..#tblAccImpErrors') IS NOT NULL 
	DROP TABLE #tblAccImpErrors
CREATE TABLE #tblAccImpErrors (rowid int IDENTITY(1,1), msg varchar(300), fatal bit)

-- see if files exist
if dbo.fn_fileExists(@flatfile + '.sql') = 0
	or dbo.fn_fileExists(@flatfile + '.txt') = 0
	or dbo.fn_fileExists(@flatfile + '.bcp') = 0
BEGIN
	INSERT INTO #tblAccImpErrors (msg, fatal)
	VALUES ('Unable to read accounting data files.',1)

	GOTO on_done
END

-- **************
-- read in sql create script and create global temp table
-- **************
BEGIN TRY
	SET @createSQL = replace(dbo.fn_ReadFile(@flatfile + '.sql',0,1),'##xxx','##importAcctData')
	IF OBJECT_ID('tempdb..##importAcctData') IS NOT NULL
		DROP TABLE ##importAcctData
	EXEC(@createSQL)
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	VALUES ('Unable to create holding table for accounting data.',1)

	GOTO on_done
END CATCH


-- *******************
-- bcp in data
-- *******************
BEGIN TRY
	set @cmd = 'bcp ##importAcctData in ' + @flatfile + '.bcp -f ' + @flatfile + '.txt -n -T -S' + CAST(serverproperty('servername') as varchar(40))
	exec master..xp_cmdshell @cmd, NO_OUTPUT
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	VALUES ('Unable to import data into holding table.',1)

	GOTO on_done
END CATCH


-- *******************
-- immediately put into local temp table and drop global temp 
-- *******************
BEGIN TRY
	IF OBJECT_ID('tempdb..#importAcctData') IS NOT NULL
		DROP TABLE #importAcctData
	select *
	into #importAcctData 
	from ##importAcctData
	IF OBJECT_ID('tempdb..##importAcctData') IS NOT NULL
		DROP TABLE ##importAcctData
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	VALUES ('Unable to import data into local temporary table.',1)

	GOTO on_done
END CATCH


-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;


-- ******************************
-- Add temp fields to the tables
-- ******************************
BEGIN TRY
	SET @createSQL = '
		IF NOT EXISTS(SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N''dbo.tr_transactions'') AND name = ''tmpID'')
			ALTER TABLE dbo.tr_transactions ADD tmpID int NULL, tmpID2 int NULL, tmpID5 int NULL;
		IF NOT EXISTS(SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N''dbo.tr_paymentHistory'') AND name = ''tmpID2'')
			ALTER TABLE dbo.tr_paymentHistory ADD tmpID2 int NULL;
		IF NOT EXISTS(SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N''dbo.tr_invoices'') AND name = ''tmpID'')
			ALTER TABLE dbo.tr_invoices ADD tmpID int NULL;
		'
	EXEC(@createSQL)
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	VALUES ('Unable to add temporary fields.',1)

	GOTO on_doneT
END CATCH


/* ******* */
/* BATCHES */
/* ******* */
BEGIN TRY
	INSERT INTO dbo.tr_batches (orgID, statusID, batchCode, batchName, controlAmt, controlCount, depositDate, isSystemCreated, createdByMemberID, payProfileID)
	select distinct @orgID, 4, batchCode, batchName, 0, 0, batchDate, 0, @recordedByMemberID, payProfileID
	from #importAcctData
	where batchCode not in (select batchCode from dbo.tr_batches where orgID = @orgID and batchCode is not null)
	order by batchDate

	update tmp
	set tmp.batchID = b.batchID
	from #importAcctData as tmp
	inner join dbo.tr_batches as b on b.batchCode = tmp.batchCode and b.orgid = @orgID 

	IF EXISTS (select top 1 * from #importAcctData where batchID is null) BEGIN
		RAISERROR('Error raised in TRY block.', 16, 1);
	END
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	SELECT distinct top 100 PERCENT 'Could not identify required batch: ' + batchName, 1
	from #importAcctData 
	where batchID is null
	order by batchName

	GOTO on_doneT
END CATCH


/* ******** */
/* PAYMENTS */
/* ******** */
BEGIN TRY
	IF OBJECT_ID('tempdb..#tmpPayments') IS NOT NULL
		DROP TABLE #tmpPayments
	CREATE TABLE #tmpPayments (transactionID int, payID int)

	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, 
		parentTransactionID, amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, 
		statsSessionID, typeID, accrualDate, debitGLAccountID, creditGLAccountID, tmpID2)
		OUTPUT inserted.transactionid, inserted.tmpID2 
		INTO #tmpPayments(transactionID, payID)
	SELECT distinct @orgID, @siteID, 1, payDescription, null, payAmount, @nowDate, payDate, payMemberID, 
		@recordedByMemberID, @statsSessionID, 2, payDate, payCashGLAID, @paymentcreditGLAccountID, payID
	from #importAcctData
	where payAmount > 0

	update tmp
	set tmp.payTID = tmpP.transactionID
	from #importAcctData as tmp
	inner join #tmpPayments as tmpP on tmpP.payID = tmp.payID

	IF OBJECT_ID('tempdb..#tmpPayments') IS NOT NULL
		DROP TABLE #tmpPayments

	IF EXISTS (select top 1 * from #importAcctData where payAmount > 0 and payTID is null) BEGIN
		RAISERROR('Did not add all necessary payments.', 16, 1);
	END

	IF OBJECT_ID('tempdb..#tmpPaymentsHistory') IS NOT NULL
		DROP TABLE #tmpPaymentsHistory
	CREATE TABLE #tmpPaymentsHistory (historyID int, payID int)

	INSERT INTO dbo.tr_paymentHistory (paymentInfo, gatewayResponse, datePaid, statsSessionID, gatewayTransactionID, gatewayApprovalCode, 
		payerMemberID, memberPaymentProfileID, isSuccess, tmpID2)
		OUTPUT inserted.historyID, inserted.tmpID2 
		INTO #tmpPaymentsHistory(historyID, payID)
	select distinct 
		paymentInfo = '<payment gatewayid="' + cast(payProfileGatewayID as varchar(10)) + '" profileid="' + cast(payProfileID as varchar(10)) + '"><args><x_amount>' + cast(payAmount as varchar(20)) + '</x_amount><x_description>' + replace(isnull(payDescription,''),'&','&amp;') + '</x_description><fld_19_>' + replace(isnull(payReference,''),'&','&amp;') + '</fld_19_></args><gateway><x_amount>' + cast(payAmount as varchar(20)) + '</x_amount><x_description>' + replace(isnull(payDescription,''),'&','&amp;') + '</x_description><fld_19_>' + replace(isnull(payReference,''),'&','&amp;') + '</fld_19_></gateway></payment>',
		gatewayResponse = '<response><rawresponse/><responsecode>1</responsecode><responsereasontext/><responsereasoncode/><transactionid/><approvalcode/><transactiondetail/><status>Active</status><glaccountid>' + cast(payCashGLAID as varchar(10)) + '</glaccountid><historyid/></response>',
		payDate, @statsSessionID, '', '', payMemberID, null, 1, payID
	from #importAcctData
	where payTID is not null

	update tmp
	set tmp.payHistoryID = tmpP.historyID
	from #importAcctData as tmp
	inner join #tmpPaymentsHistory as tmpP on tmpP.payID = tmp.payID

	IF OBJECT_ID('tempdb..#tmpPaymentsHistory') IS NOT NULL
		DROP TABLE #tmpPaymentsHistory

	IF EXISTS (select top 1 * from #importAcctData where payAmount > 0 and payHistoryID is null) BEGIN
		RAISERROR('Did not add all necessary payment histories.', 16, 1);
	END

	INSERT INTO dbo.tr_transactionPayments (transactionID, profileID, historyID, cache_allocatedAmountOfPayment, cache_refundableAmountOfPayment)
	select distinct payTID, payProfileID, payHistoryID, payAmount, payAmount
	from #importAcctData
	where payTID is not null

	INSERT INTO dbo.tr_batchTransactions (batchID, transactionID)
	select distinct batchID, payTID
	from #importAcctData
	where payTID is not null
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	SELECT distinct top 100 PERCENT 'Unable to add payment transaction for row ' + cast(autoid as varchar(10)), 1
	from #importAcctData 
	where payAmount > 0 and payTID is null
	order by autoid

	INSERT INTO #tblAccImpErrors (msg, fatal)
	SELECT distinct top 100 PERCENT 'Unable to add payment history record for row ' + cast(autoid as varchar(10)), 1
	from #importAcctData 
	where payAmount > 0 and payHistoryID is null
	order by autoid

	GOTO on_doneT
END CATCH


/* ******** */
/* INVOICES */
/* ******** */
BEGIN TRY
	IF OBJECT_ID('tempdb..#tmpInvoices') IS NOT NULL
		DROP TABLE #tmpInvoices
	CREATE TABLE #tmpInvoices (invoiceID int, autoID int)

	select @invoicenum = isnull(max(i.invoiceNumber),0)
		from dbo.tr_invoices as i 
		inner join dbo.ams_members as m on m.memberID = i.assignedToMemberID
		where m.orgID = @orgID

	INSERT INTO dbo.tr_invoices (datecreated, statusID, invoiceNumber, dateBilled, dateDue, assignedToMemberID, invoiceCode, payProfileID, invoiceProfileID, tmpID)
		OUTPUT inserted.invoiceID, inserted.tmpID 
		INTO #tmpInvoices(invoiceID, autoID)
	select distinct @nowDate, 4, saleAutoID + @invoicenum, DATEADD(dd,DATEDIFF(dd,0,saleDate),0), DATEADD(dd,DATEDIFF(dd,0,saleDate),0), saleMemberID, '', null, invoiceProfileID, saleAutoID
	from #importAcctData

	update tmp
	set tmp.invoiceID = tmpI.invoiceID
	from #importAcctData as tmp
	inner join #tmpInvoices as tmpI on tmpI.autoID = tmp.saleAutoID

	IF OBJECT_ID('tempdb..#tmpInvoices') IS NOT NULL
		DROP TABLE #tmpInvoices

	IF EXISTS (select top 1 * from #importAcctData where invoiceID is null) BEGIN
		RAISERROR('Error raised in TRY block.', 16, 1);
	END

	INSERT INTO dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
	select invoiceID, DATEADD(dd,DATEDIFF(dd,0,saleDate),0), 1, null, @recordedByMemberID
	from #importAcctData

	INSERT INTO dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
	select invoiceID, DATEADD(ss,5,DATEADD(dd,DATEDIFF(dd,0,saleDate),0)), 3, 1, @recordedByMemberID
	from #importAcctData

	INSERT INTO dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
	select invoiceID, DATEADD(ss,10,DATEADD(dd,DATEDIFF(dd,0,saleDate),0)), 4, 3, @recordedByMemberID
	from #importAcctData

	;WITH numbers AS (select NUMBER as n, Abs(Checksum(Newid()))%26 as c from dbo.F_TABLE_NUMBER_RANGE(1,800000))
	update i
	set i.invoiceCode = tmpnum.code
	from dbo.tr_invoices as i
	inner join #importAcctData as tmp on tmp.invoiceID = i.invoiceID
	inner join (
		SELECT n, CAST((SELECT TOP 8 CHAR(65 + case c 
					when 0 then 2 -- A = C
					when 1 then 22 -- B = W
					when 4 then 10 -- E = K
					when 8 then 7 -- I = H
					when 14 then 15 -- O = P
					when 18 then 19 -- S = T
					when 20 then 3 -- U = D
					when 25 then 17 -- Z = R
					else c end)
					 FROM   numbers n1
					 WHERE  n1.n >= -n2.n
					 FOR XML PATH('')) AS CHAR(8)) as code
		FROM numbers n2  
	) as tmpnum on tmpnum.n = i.invoiceID
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	SELECT distinct top 100 PERCENT 'Unable to add invoice for row ' + cast(autoid as varchar(10)), 1
	from #importAcctData 
	where invoiceID is null
	order by autoid

	GOTO on_doneT
END CATCH


/* ***** */
/* SALES */
/* ***** */
BEGIN TRY
	IF OBJECT_ID('tempdb..#tmpSales') IS NOT NULL
		DROP TABLE #tmpSales
	CREATE TABLE #tmpSales (transactionID int, autoid int)

	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, 
		parentTransactionID, amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, 
		statsSessionID, typeID, accrualDate, debitGLAccountID, creditGLAccountID, tmpID)
		OUTPUT inserted.transactionid, inserted.tmpID 
		INTO #tmpSales(transactionID, autoid)
	select distinct @orgID, @siteID, 1, saleDescription, null, saleAmount, @nowDate, saleDate, saleMemberID, 
		@recordedByMemberID, @statsSessionID, 1, saleDate, @saledebitGLAccountID, saleRevenueGLAID, saleAutoID
	from #importAcctData

	update tmp
	set tmp.saleTID = tmpS.transactionID
	from #importAcctData as tmp
	inner join #tmpSales as tmpS on tmpS.autoid = tmp.saleAutoID

	IF OBJECT_ID('tempdb..#tmpSales') IS NOT NULL
		DROP TABLE #tmpSales

	IF EXISTS (select top 1 * from #importAcctData where saleTID is null) BEGIN
		RAISERROR('Error raised in TRY block.', 16, 1);
	END

	INSERT INTO dbo.tr_transactionSales (transactionID, cache_amountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, stateIDForTax)
	select distinct saleTID, saleAmount, saleAmount, 0, null
	from #importAcctData

	INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount)
	select distinct saleTID, invoiceID, saleAmount, saleAmount, 0
	from #importAcctData
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	SELECT distinct top 100 PERCENT 'Unable to add sale transaction for row ' + cast(autoid as varchar(10)), 1
	from #importAcctData 
	where saleTID is null
	order by autoid

	GOTO on_doneT
END CATCH


/* *********** */
/* ALLOCATIONS */
/* *********** */
BEGIN TRY
	IF OBJECT_ID('tempdb..#tmpAllocations') IS NOT NULL
		DROP TABLE #tmpAllocations
	CREATE TABLE #tmpAllocations (transactionID int, autoid int)

	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, 
		parentTransactionID, amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, 
		statsSessionID, typeID, accrualDate, debitGLAccountID, creditGLAccountID, tmpID5)
		OUTPUT inserted.transactionid, inserted.tmpID5 
		INTO #tmpAllocations(transactionID, autoid)
	select distinct @orgID, @siteID, 1, null, null, saleAmount, @nowDate, saleDate, payMemberID,
		@recordedByMemberID, @statsSessionID, 5, saleDate, @allocDebitGLAccountID, @allocCreditGLAccountID, autoid
	from #importAcctData
	where payTID is not null

	update tmp
	set tmp.allocTID = tmpA.transactionID
	from #importAcctData as tmp
	inner join #tmpAllocations as tmpA on tmpA.autoid = tmp.autoid

	IF OBJECT_ID('tempdb..#tmpAllocations') IS NOT NULL
		DROP TABLE #tmpAllocations

	IF EXISTS (select top 1 * from #importAcctData where payTID is not null and allocTID is null) BEGIN
		RAISERROR('Error raised in TRY block.', 16, 1);
	END

	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)	--allocpaytrans
	select distinct 2, allocTID, payTID
	from #importAcctData
	where allocTID is not null

	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)	--allocsaletrans
	select distinct 3, allocTID, saleTID
	from #importAcctData
	where allocTID is not null
	
	INSERT INTO dbo.tr_batchTransactions (batchID, transactionID)
	select distinct batchID, allocTID
	from #importAcctData
	where allocTID is not null
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	SELECT distinct top 100 PERCENT 'Unable to add allocation transaction for row ' + cast(autoid as varchar(10)), 1
	from #importAcctData 
	where payTID is not null and allocTID is null
	order by autoid

	GOTO on_doneT
END CATCH


/* ******************* */
/* UPDATE BATCH COUNTS */
/* ******************* */
BEGIN TRY
	update b
	set b.controlCount = act.actualCount,
		b.controlAmt = act.actualAmount
	from dbo.tr_batches as b
	inner join (select distinct batchID from #importAcctData) as tmp on tmp.batchID = b.batchID
	cross apply dbo.fn_tr_getBatchActual(tmp.batchID) as act
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	VALUES ('Unable to update actual batch counts.',1)

	GOTO on_doneT
END CATCH


-- ******************************
-- Remove temp fields to the tables
-- ******************************
BEGIN TRY
	SET @createSQL = '
		IF EXISTS(SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N''dbo.tr_transactions'') AND name = ''tmpID'')
			ALTER TABLE dbo.tr_transactions DROP COLUMN tmpID, tmpID2, tmpID5;
		IF EXISTS(SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N''dbo.tr_paymentHistory'') AND name = ''tmpID2'')
			ALTER TABLE dbo.tr_paymentHistory DROP COLUMN tmpID2;
		IF EXISTS(SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N''dbo.tr_invoices'') AND name = ''tmpID'')
			ALTER TABLE dbo.tr_invoices DROP COLUMN tmpID;
		'
	EXEC(@createSQL)
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	VALUES ('Unable to remove temporary fields.',1)

	GOTO on_done
END CATCH

IF @TranCounter = 0
	COMMIT TRAN;
GOTO on_done


on_doneT:
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

on_done:
	select @importResult = (
		select getdate() as "@date", @flatfile as "@flatfile", 
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg", "@severity" = case fatal when 1 then 'fatal' else 'nonfatal' end
			from #tblAccImpErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE)

	IF OBJECT_ID('tempdb..#tblAccImpErrors') IS NOT NULL 
		DROP TABLE #tblAccImpErrors
	IF OBJECT_ID('tempdb..##importAcctData') IS NOT NULL
		DROP TABLE ##importAcctData
	IF OBJECT_ID('tempdb..#importAcctData') IS NOT NULL
		DROP TABLE #importAcctData
	IF OBJECT_ID('tempdb..#tmpPayments') IS NOT NULL
		DROP TABLE #tmpPayments
	IF OBJECT_ID('tempdb..#tmpPaymentsHistory') IS NOT NULL
		DROP TABLE #tmpPaymentsHistory
	IF OBJECT_ID('tempdb..#tmpInvoices') IS NOT NULL
		DROP TABLE #tmpInvoices
	IF OBJECT_ID('tempdb..#tmpSales') IS NOT NULL
		DROP TABLE #tmpSales
	IF OBJECT_ID('tempdb..#tmpAllocations') IS NOT NULL
		DROP TABLE #tmpAllocations
GO


