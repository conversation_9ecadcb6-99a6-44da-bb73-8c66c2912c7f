use membercentral
GO
ALTER FUNCTION dbo.fn_cleanInvalidXMLChars (@string varchar(max))
RETURNS varchar(max)
AS
BEGIN
	RETURN dbo.fn_RegExReplace(@string,'[^\x09\x0A\x0D\x20-\uD7FF\uE000-\uFFFD\u10000-\u10FFFF]','')
END
GO

ALTER PROC [dbo].[ams_importMemberHistory_toHolding]
@siteid int,
@typeID int,
@csvfilename varchar(200),
@strTableColumnNames varchar(max),
@pathToExport varchar(100),
@importResult xml OUTPUT

AS

DECLARE @qry varchar(max), @sitecode varchar(10), @tmptbl varchar(17), @var_tmpCols varchar(20), @orgID int
declare @good bit, @dynSQL nvarchar(max), @prefix varchar(50), @flatfile varchar(160), @exportcmd varchar(400)
declare @tblMissingCols TABLE (colName varchar(255))
declare @tblErrors TABLE (rowid int IDENTITY(1,1), msg varchar(300), fatal bit)
declare @tblCounts TABLE (rowid int IDENTITY(1,1), countName varchar(50), countNum int)
declare @catTree_1 int, @catTree_2 int, @catTree_3 int

-- get category trees
select @catTree_1 = dbo.fn_getCategoryTreeIDForSiteResourceID(dbo.fn_getSiteResourceIDForResourceType('MemberHistoryAdmin',@siteID))
select @catTree_2 = dbo.fn_getCategoryTreeIDForSiteResourceID(dbo.fn_getSiteResourceIDForResourceType('RelationshipAdmin',@siteID))
select @catTree_3 = dbo.fn_getCategoryTreeIDForSiteResourceID(dbo.fn_getSiteResourceIDForResourceType('HistoryAdmin',@siteID))

-- get table name
select @sitecode=sitecode, @orgID=orgID from dbo.sites where siteID = @siteid
select @tmptbl = '##tmpMH' + @sitecode
select @var_tmpCols = '##tmpColsMH' + @sitecode

-- delete temp table if exists
IF OBJECT_ID('tempdb..' + @tmptbl) IS NOT NULL 
	EXEC('DROP TABLE ' + @tmptbl)

-- create temp table using columnlist
select @qry = COALESCE(@qry + ', ', '') + quotename(listitem) + ' varchar(max) '
FROM dbo.fn_varCharListToTable(@strTableColumnNames,char(7))
order by autoid
	EXEC('CREATE TABLE ' + @tmptbl + ' (' + @qry + ')')

-- rowID and membernumber is in a key column of an index and needs to not be varchar(max)
declare @trash bit
set @trash = 1
EXEC('ALTER TABLE ' + @tmptbl + ' ALTER COLUMN rowID int') 
BEGIN TRY
	EXEC('ALTER TABLE ' + @tmptbl + ' ALTER COLUMN membernumber varchar(800)') 
END TRY
BEGIN CATCH
	set @trash = 0
END CATCH

-- Execute a bulk insert into previously defined temporary table
SELECT @qry = 'BULK INSERT ' + @tmptbl + ' FROM ''' + @csvfilename + ''' WITH (FIELDTERMINATOR = '''+ char(7) + ''', FIRSTROW = 2);'
EXEC(@qry)

IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
	EXEC('DROP TABLE ' + @var_tmpCols)
IF OBJECT_ID('tempdb..#tblOrgCols') IS NOT NULL 
	DROP TABLE #tblOrgCols
IF OBJECT_ID('tempdb..#tblImportCols') IS NOT NULL 
	DROP TABLE #tblImportCols

-- ******************************** 
-- ensure all columns exist (fatal)
-- ******************************** 
-- this will get the columns that should be there
CREATE TABLE #tblOrgCols (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
	COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
	SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
	COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
	CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)

-- Need the temp table code for the export. 
IF @typeID = 1
	select @dynSQL = 'select top 1 m.MemberNumber, cType.categoryName as Category, cType.categoryName as Subcategory, 
						mh.userDate as [StartDate], mh.userEndDate as [EndDate], mh.Description, mh.Quantity, mh.dollarAmt as Amount, 
						m.MemberNumber as LinkedMemberNumber
						into ' + @var_tmpCols + '
						from dbo.ams_members as m
						inner join dbo.ams_memberHistory as mh on mh.memberID = m.memberID
						inner join dbo.cms_categories as cType on cType.categoryID = mh.categoryID
						where m.orgID = 0'
IF @typeID = 2
	select @dynSQL = 'select top 1 m.MemberNumber, cType.categoryName as Relationship, 
						mh.userDate as [StartDate], mh.userEndDate as [EndDate], mh.Description, m.MemberNumber as LinkedMemberNumber
						into ' + @var_tmpCols + '
						from dbo.ams_members as m
						inner join dbo.ams_memberHistory as mh on mh.memberID = m.memberID
						inner join dbo.cms_categories as cType on cType.categoryID = mh.categoryID
						where m.orgID = 0'
IF @typeID = 3
	select @dynSQL = 'select top 1 m.MemberNumber, cType.categoryName as Category, cType.categoryName as Subcategory, 
						mh.userDate as [StartDate], mh.userEndDate as [EndDate], mh.Description, m.MemberNumber as LinkedMemberNumber
						into ' + @var_tmpCols + '
						from dbo.ams_members as m
						inner join dbo.ams_memberHistory as mh on mh.memberID = m.memberID
						inner join dbo.cms_categories as cType on cType.categoryID = mh.categoryID
						where m.orgID = 0'
EXEC(@dynSQL)

-- get cols
INSERT INTO #tblOrgCols
EXEC tempdb.dbo.SP_COLUMNS @var_tmpCols
	
-- cleanup table no longer needed
IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
	EXEC('DROP TABLE ' + @var_tmpCols)

-- this will get the columns that are actually in the import
CREATE TABLE #tblImportCols (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
	COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
	SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
	COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
	CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)

	-- get cols
	INSERT INTO #tblImportCols
	EXEC tempdb.dbo.SP_COLUMNS @tmptbl

INSERT INTO @tblErrors (msg, fatal)
select 'The column ' + org.column_name + ' is missing from your data.', 1
from #tblOrgCols as org
left outer join #tblImportCols as imp on imp.column_name = org.column_name
where imp.table_name is null

insert into @tblMissingCols(colname)
select org.column_name
from #tblOrgCols as org
left outer join #tblImportCols as imp on imp.column_name = org.column_name
where imp.table_name is null

-- dont drop these temp tables here because we use them for skipped rows below

-- ********************************
-- data type checks (fatal)
-- ********************************
IF NOT EXISTS (select colName from @tblMissingCols where colName = 'StartDate') BEGIN
	EXEC('UPDATE ' + @tmptbl + ' SET [StartDate] = null where len([StartDate]) = 0')

	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [StartDate] datetime null
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column StartDate contains invalid dates.', 1)
END

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'EndDate') BEGIN
	EXEC('UPDATE ' + @tmptbl + ' SET [EndDate] = null where len([EndDate]) = 0')

	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [EndDate] datetime null
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column EndDate contains invalid dates.', 1)
END
				
IF @typeID = 1 and NOT EXISTS (select colName from @tblMissingCols where colName = 'quantity') BEGIN
	EXEC('UPDATE ' + @tmptbl + ' SET quantity = null where len(quantity) = 0')

	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [quantity] int null
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column Quantity contains invalid whole number values.', 1)
END
				
IF @typeID = 1 and NOT EXISTS (select colName from @tblMissingCols where colName = 'Amount') BEGIN
	EXEC('UPDATE ' + @tmptbl + ' SET Amount = null where len(Amount) = 0')

	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN Amount money null
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column Amount contains invalid dollar values.', 1)
END

-- ********************************
-- change Relationship to Category for easier querying below
-- ********************************
IF @typeID = 2 and NOT EXISTS (select colName from @tblMissingCols where colName = 'Relationship') BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			EXEC tempdb..sp_rename ''' + @tmptbl + '.Relationship'', ''Category'', ''COLUMN''
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column Relationship could not be processed.', 1)
END

-- ********************************
-- no/bad member number (fatal)
-- ********************************
IF NOT EXISTS (select colName from @tblMissingCols where colName = 'memberNumber') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' is missing a Member Number. Member Numbers are required for all entries.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE (memberNumber IS NULL OR ltrim(rtrim(memberNumber)) = '''')
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' Member Number ('' + tb.membernumber + '') was not found. Member Numbers must match existing members.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' as tb
		left outer join dbo.ams_members as m on m.memberNumber = tb.membernumber
			and m.orgID = ' + cast(@orgID as varchar(10)) + '
			and m.memberID = m.activeMemberID
		WHERE tb.membernumber IS NOT NULL 
		AND ltrim(rtrim(tb.membernumber)) <> ''''
		AND m.memberNumber is null
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'linkedmembernumber') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' Linked Member Number ('' + tb.linkedmembernumber + '') was not found. Linked Member Numbers must match existing members.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' as tb
		left outer join dbo.ams_members as m on m.memberNumber = tb.linkedmembernumber
			and m.orgID = ' + cast(@orgID as varchar(10)) + '
			and m.memberID = m.activeMemberID
		WHERE tb.linkedmembernumber IS NOT NULL 
		AND ltrim(rtrim(tb.linkedmembernumber)) <> ''''
		AND m.memberNumber is null
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

-- ********************************
-- no/bad categories (fatal)
-- ********************************
IF NOT EXISTS (select colName from @tblMissingCols where colName = 'Category') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' is missing a ' + case when @typeID = 2 then 'Relationship Type' else 'Category' end + '. ' + case when @typeID = 2 then 'Relationship Types' else 'Categories' end + ' are required for all entries.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE (Category IS NULL OR ltrim(rtrim(Category)) = '''')
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ' + case when @typeID = 2 then 'Relationship Type' else 'Category' end + ' ('' + tb.Category + '') was not found. Values must match existing ' + case when @typeID = 2 then 'Relationship Types' else 'Categories' end + '.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' as tb
		left outer join dbo.cms_categories as c on c.categoryName = tb.category
			and c.categoryTreeID = ' + 
			case 
			when @typeID = 1 then cast(@catTree_1 as varchar(10))
			when @typeID = 2 then cast(@catTree_2 as varchar(10))
			when @typeID = 3 then cast(@catTree_3 as varchar(10))
			end + '
			and c.isActive = 1
			and c.parentCategoryID is NULL
		WHERE tb.Category IS NOT NULL 
		AND ltrim(rtrim(tb.Category)) <> ''''
		AND c.categoryName is null
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

IF @typeID <> 2 and NOT EXISTS (select colName from @tblMissingCols where colName = 'subcategory') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' Subcategory ('' + tb.subcategory + '') was not found. Values must match existing Subcategories.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' as tb
		left outer join dbo.cms_categories as c 
			inner join dbo.cms_categories as cP on cp.categoryID = c.parentCategoryID
				and cP.categoryTreeID = ' + 
				case 
				when @typeID = 1 then cast(@catTree_1 as varchar(10))
				when @typeID = 3 then cast(@catTree_3 as varchar(10))
				end + '
				and cP.isActive = 1 
				and cP.parentCategoryID is NULL
			on c.categoryName = tb.Subcategory
			and cP.categoryName = tb.Category
			and c.isActive = 1 
			and c.parentCategoryID is not NULL
		WHERE tb.SubCategory IS NOT NULL 
		AND ltrim(rtrim(tb.SubCategory)) <> ''''
		AND c.categoryName is null
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

-- clean descriptions.
select @dynSQL = 'update ' + @tmptbl + ' set [description] = dbo.fn_cleanInvalidXMLChars([description]) where [description] is not null'
EXEC(@dynSQL)

-- cleanup - these are no longer needed
IF OBJECT_ID('tempdb..#tblOrgCols') IS NOT NULL
	DROP TABLE #tblOrgCols
IF OBJECT_ID('tempdb..#tblImportCols') IS NOT NULL
	DROP TABLE #tblImportCols

-- ******************************** 
-- dump flat table and format file and creation script
-- would love to use xml format files, but it appears column names with spaces cause it to fail
-- ******************************** 
SELECT @prefix = @sitecode + '_MH_flat_' + convert(varchar(8),getdate(),112) + replace(convert(varchar(8),getdate(),108),':','')
SELECT @flatfile = @pathToExport + @prefix

select @exportcmd = 'bcp ' + @tmptbl + ' format nul -f ' + @flatfile + '.txt -n -T -S' + CAST(serverproperty('servername') as varchar(40))
EXEC master..xp_cmdshell @exportcmd, NO_OUTPUT

select @exportcmd = 'bcp ' + @tmptbl + ' out ' + @flatfile + '.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
EXEC master..xp_cmdshell @exportcmd, NO_OUTPUT

declare @createTableSQLTop varchar(100)          
declare @coltypes table (datatype varchar(16))          
insert into @coltypes values('bit')          
insert into @coltypes values('binary')          
insert into @coltypes values('bigint')          
insert into @coltypes values('int')          
insert into @coltypes values('float')          
insert into @coltypes values('datetime')          
insert into @coltypes values('text')          
insert into @coltypes values('image')          
insert into @coltypes values('money')          
insert into @coltypes values('uniqueidentifier')          
insert into @coltypes values('smalldatetime')          
insert into @coltypes values('tinyint')          
insert into @coltypes values('smallint')          
insert into @coltypes values('sql_variant')          
select @dynSQL = ''
select @dynSQL = @dynSQL +           
	case when charindex('(',@dynSQL,1)<=0 then '(' else '' end + '[' + Column_Name + '] ' +Data_Type +
	case when Data_Type in (Select datatype from @coltypes) then '' else  '(' end+
	case when data_type in ('real','decimal','numeric')  then cast(isnull(numeric_precision,'') as varchar)+','+
	case when data_type in ('real','decimal','numeric') then cast(isnull(Numeric_Scale,'') as varchar) end
	when data_type in ('nvarchar','varchar') and cast(isnull(Character_Maximum_Length,'') as varchar) = '-1' then 'max'
	when data_type in ('char','nvarchar','varchar','nchar') then cast(isnull(Character_Maximum_Length,'') as varchar) else '' end+
	case when Data_Type in (Select datatype from @coltypes)then '' else  ')' end+
	case when Is_Nullable='No' then ' Not null,' else ' null,' end
	from tempdb.Information_Schema.COLUMNS where Table_Name=@tmptbl
	order by ordinal_position
select @createTableSQLTop = 'Create table ##xxx ' 
select @dynSQL=@createTableSQLTop + substring(@dynSQL,1,len(@dynSQL)-1) +' )'            
if dbo.fn_WriteFile(@pathToExport + @prefix + '.sql', @dynSQL, 1) = 0 BEGIN
	RETURN 0
END

-- create index on temp table for numbers below
EXEC('CREATE NONCLUSTERED INDEX [idx_' + @tmptbl + '_memnum_rowid] ON ' + @tmptbl + '([membernumber] ASC,[rowID] ASC)')

-- ******************************** 
-- Counts
-- ******************************** 
IF NOT EXISTS (select rowID from @tblErrors where fatal = 1) BEGIN
	-- TotalEntriesImported
	select @dynSQL = 'SELECT ''TotalEntriesImported'', COUNT(rowID) FROM ' + @tmptbl
	INSERT INTO @tblCounts (countName, countNum)
	EXEC(@dynSQL)
END
				
-- ********************************
-- generate result xml file 
-- ********************************
select @importResult = (
	select getdate() as "@date", @flatfile as "@flatfile",
		isnull((select top 301 dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg", "@severity" = case fatal when 1 then 'fatal' else 'nonfatal' end
		from @tblErrors
		order by rowid
		FOR XML path('error'), root('errors'), type),'<errors/>'),

		isnull((select countName as "@name", countNum as "@num"
		from @tblCounts
		order by rowid
		FOR XML path('count'), root('counts'), type),'<counts/>')
	for xml path('import'), TYPE)

-- drop temp tables 
IF OBJECT_ID('tempdb..' + @tmptbl) IS NOT NULL
	EXEC('DROP TABLE ' + @tmptbl)

RETURN 0
GO

