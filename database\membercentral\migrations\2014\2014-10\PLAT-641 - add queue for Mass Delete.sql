use platformQueue;
GO

declare @queueTypeID int

insert into dbo.tblQueueTypes (queueType) values ('subsMassDelete')
SELECT @queueTypeID = SCOPE_IDENTITY()

insert into dbo.tblQueueStatuses (queueTypeID, queueStatus)
select @queueTypeID, qs.queueStatus
from dbo.tblQueueTypes qt
inner join dbo.tblQueueStatuses qs on qt.queueTypeID  = qs.queueTypeID
	and qt.queueType = 'subsMarkBilled'
order by qs.queueStatusID
GO

CREATE PROC [dbo].[job_subsMassDelete_grabForProcessing]
@serverID int,
@batchSize int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @statusReady int, @statusGrabbed int
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'subsMassDelete'
		and qs.queueStatus = 'readyToProcess'
	select @statusGrabbed = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'subsMassDelete'
		and qs.queueStatus = 'grabbedForProcessing'

	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL 
		DROP TABLE #tmpSubscribers
	CREATE TABLE #tmpSubscribers (itemUID uniqueidentifier, jobUID uniqueidentifier, subscriberID int, recordedByMemberID int)

	declare @jobUID uniqueidentifier
	set @jobUID = NEWID()

	-- dequeue in order of dateAdded. get @batchsize subscribers
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.queueStatusID = @statusGrabbed,
		qi.dateUpdated = getdate(),
		qi.jobUID = @jobUID,
		qi.jobDateStarted = getdate(),
		qi.jobServerID = @serverID
		OUTPUT inserted.itemUID, inserted.jobUID, qid.subscriberID, qid.recordedByMemberID
		INTO #tmpSubscribers
	FROM platformQueue.dbo.tblQueueItems as qi
	INNER JOIN platformQueue.dbo.tblQueueItems_subscriptionStatusChanges as qid ON qid.itemUID = qi.itemUID
	INNER JOIN (
		SELECT top(@BatchSize) qi2.itemUID 
		from platformQueue.dbo.tblQueueItems as qi2
		INNER JOIN platformQueue.dbo.tblQueueItems_subscriptionStatusChanges as qid2 ON qid2.itemUID = qi2.itemUID
		WHERE qi2.queueStatusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemUID
	) as batch on batch.itemUID = qi.itemUID
	WHERE qi.queueStatusID = @statusReady

	-- return subscriber information
	select qid.itemUID, qid.subscriberID, s.siteID, s.orgID, qid.jobUID, qid.recordedByMemberID
	from #tmpSubscribers as qid
	inner join membercentral.dbo.sub_subscribers as ss on ss.subscriberID = qid.subscriberID
	inner join membercentral.dbo.sub_subscriptions as subs on subs.subscriptionID = ss.subscriptionID
	inner join membercentral.dbo.sub_types as t on t.typeID = subs.typeID
	inner join membercentral.dbo.sites as s on s.siteID = t.siteID
	order by qid.itemUID

	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL 
		DROP TABLE #tmpSubscribers

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH
GO

CREATE PROC [dbo].[job_subsMassDelete_grabForNotification]
AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @statusReady int, @statusGrabbed int, @queueTypeID int
	select @statusReady = qs.queueStatusID , @queueTypeID = qt.queueTypeID
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'subsMassDelete'
		and qs.queueStatus = 'readyToNotify'
	select @statusGrabbed = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'subsMassDelete'
		and qs.queueStatus = 'grabbedForNotifying'

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL 
		DROP TABLE #tmpNotify
	
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier)

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.queueStatusID
			and qs.queueTypeID = @queueTypeID
		inner join platformQueue.dbo.tblQueueItems_subscriptionStatusChanges as qid ON qid.itemUID = qi.itemUID
		where qi.queueStatusID = @statusReady
			except
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.queueStatusID
			and qs.queueTypeID = @queueTypeID
		inner join platformQueue.dbo.tblQueueItems_subscriptionStatusChanges as qid ON qid.itemUID = qi.itemUID
		where qi.queueStatusID <> @statusReady
	)
	UPDATE platformQueue.dbo.tblQueueItems WITH (UPDLOCK, READPAST)
	SET queueStatusID = @statusGrabbed,
		dateUpdated = getdate()
		OUTPUT qid.itemGroupUID
		INTO #tmpNotify
	FROM platformQueue.dbo.tblQueueItems as qi
	inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.queueStatusID
		and qs.queueTypeID = @queueTypeID
	INNER JOIN platformQueue.dbo.tblQueueItems_subscriptionStatusChanges as qid ON qid.itemUID = qi.itemUID
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qid.itemGroupUID
	where qi.queueStatusID = @statusReady

	-- return report information
	select tmpN.itemGroupUID, qid.itemUID, qid.errorMessage, sub.subscriptionName, 
		m2.memberID, m2.lastname + ', ' + m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' (' + m2.membernumber + ')' as subscriberName,
		m4.memberID as reportMemberID, me.email as reportEmail, m4.orgID as reportOrgID, m4.firstname as reportFirstName, m4.lastname as reportLastName, m4.memberNumber as reportMemberNumber, sites.siteName, sites.sitecode, sites.siteID, sites.orgID
	from (select distinct itemGroupUID from #tmpNotify) as tmpN
	INNER JOIN platformQueue.dbo.tblQueueItems_subscriptionStatusChanges as qid ON qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN membercentral.dbo.sub_subscribers as s on s.subscriberID = qid.subscriberID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = s.memberID
	INNER JOIN membercentral.dbo.ams_members as m2 on m2.memberid = m.activeMemberID
	INNER JOIN membercentral.dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
	INNER JOIN membercentral.dbo.sub_types as st on st.typeID = sub.typeID
	INNER JOIN membercentral.dbo.sites on sites.siteID = st.siteID
	INNER JOIN membercentral.dbo.ams_members as m3 on m3.memberID = qid.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as m4 on m4.memberid = m3.activeMemberID
	LEFT OUTER JOIN membercentral.dbo.ams_memberEmails as me 
		INNER JOIN membercentral.dbo.ams_memberEmailTypes as met on met.emailTypeID = me.emailTypeID and met.emailTypeOrder = 1
		on me.memberID = m4.memberID
	order by tmpN.itemGroupUID, case when errorMessage is null then 0 else 1 end desc, subscriberName, subscriptionName

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL 
		DROP TABLE #tmpNotify

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH
GO

CREATE PROC [dbo].[job_subsMassDelete_clearDone]
AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @statusDone int, @queueTypeID int
	select @statusDone = qs.queueStatusID, @queueTypeID = qt.queueTypeID
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'subsMassDelete'
		and qs.queueStatus = 'done'

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.queueStatusID
			and qs.queueTypeID = @queueTypeID
		inner join platformQueue.dbo.tblQueueItems_subscriptionStatusChanges as qid ON qid.itemUID = qi.itemUID
		where qi.queueStatusID = @statusDone
			except
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.queueStatusID
			and qs.queueTypeID = @queueTypeID
		inner join platformQueue.dbo.tblQueueItems_subscriptionStatusChanges as qid ON qid.itemUID = qi.itemUID
		where qi.queueStatusID <> @statusDone
	)
	DELETE from platformQueue.dbo.tblQueueItems
	where itemUID in (
		select qi.itemUID
		FROM platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.queueStatusID
			and qs.queueTypeID = @queueTypeID
		INNER JOIN platformQueue.dbo.tblQueueItems_subscriptionStatusChanges as qid ON qid.itemUID = qi.itemUID
		INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qid.itemGroupUID
		WHERE qi.queueStatusID = @statusDone
	)

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH
GO

use membercentral;
GO
insert into membercentral.dbo.scheduledTasks (name, nextRunDate, interval, intervalTypeID, taskCFC, timeoutMinutes, disabled, siteid)
select  'Process Subscription MassDelete Queue' as name, nextRunDate, interval, intervalTypeID, 'model.scheduledTasks.tasks.processSubMassDeleteQueue' as taskCFC, timeoutMinutes, disabled, siteid
from membercentral.dbo.scheduledTasks
where taskCFC = 'model.scheduledTasks.tasks.processSubMarkBilledQueue'
GO

CREATE PROC [dbo].[sub_queueMassDelete]
@recordedByMemberID int,
@subscriberIDList varchar(max)

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL 
		DROP TABLE #tmpSubscribers
	CREATE TABLE #tmpSubscribers (subscriberID int, itemUID uniqueidentifier DEFAULT NEWID())

	declare @statusInserting int, @statusReady int, @queueTypeID int

	select @queueTypeID = queueTypeID
	from platformQueue.dbo.tblQueueTypes as qt
	where qt.queueType = 'subsMassDelete'

	select @statusInserting = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		where qs.queueTypeID = @queueTypeID
		and qs.queueStatus = 'insertingItems'
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		where qs.queueTypeID = @queueTypeID
		and qs.queueStatus = 'readyToProcess'

	-- there should only be one itemGroupUID for these deletions
	declare @itemGroupUID uniqueidentifier
	set @itemGroupUID = NEWID()

	-- get subscribers to Mass Delete
	insert into #tmpSubscribers (subscriberID)
	select s.subscriberID
	from dbo.fn_intListToTable(@subscriberIDList,',') as tmp
	inner join dbo.sub_subscribers as s on s.subscriberID = tmp.listitem
		except
	select qid.subscriberID
	from platformQueue.dbo.tblQueueItems_subscriptionStatusChanges as qid
	inner join platformQueue.dbo.tblQueueItems as qi on qi.itemUID = qid.itemUID
	inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.queueStatusID
		and qs.queueTypeID = @queueTypeID
		and qs.queueStatus not in ('readyToNotify','grabbedForNotifying','done')

	-- queue items
	insert into platformQueue.dbo.tblQueueItems_subscriptionStatusChanges (queueTypeID, itemUID, itemGroupUID, subscriberID, recordedByMemberID)
		OUTPUT inserted.itemUID, inserted.dateAdded, @statusInserting 
		INTO platformQueue.dbo.tblQueueItems(itemUID, dateAdded, queueStatusID)
	select @queueTypeID, itemUID, @itemGroupUID, subscriberID, @recordedByMemberID
	from #tmpSubscribers

	-- update queue item groups to show ready to process
	update qi WITH (UPDLOCK, HOLDLOCK)
	set qi.queueStatusID = @statusReady,
		qi.dateUpdated = getdate()
	from platformQueue.dbo.tblQueueItems as qi
	inner join #tmpSubscribers as s on s.itemUID = qi.itemUID

	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL 
		DROP TABLE #tmpSubscribers

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

