use membercentral
GO

-- Add needed columns
ALTER TABLE dbo.email_EmailBlasts ADD
	emailDateScheduled datetime NULL,
	emailDateSent datetime NULL
GO


declare @taskID int

insert into dbo.scheduledTasks (
	[name],
	nextRunDate,
	interval,
	intervalTypeID,
	taskCFC,
	timeoutMinutes,
	disabled,
	siteid
)
values (
	'Send Scheduled Email Blasts',
	getDate(),
	1,
	5,
	'model.scheduledTasks.tasks.sendScheduledEmailBlasts',
	10,
	1,
	1
)

select @taskID = SCOPE_IDENTITY()	

insert into platformstats.dbo.scheduledTaskHistory(
	taskID,
	statusTypeID,
	dateStarted,
	dateEnded,
	serverid,
	dateLastUpdated
)
select 
	top 1 
	@taskID,
	statusTypeID,
	dateStarted,
	dateEnded,
	serverid,
	dateLastUpdated
from 
	platformstats.dbo.scheduledTaskHistory 
order by 
	taskid desc
GO


USE [memberCentral]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROC [dbo].[email_sendBlast]
@recordedByMemberID int,
@siteID int,
@blastID int,
@messageWrapper varchar(max)

AS

declare @numRecipients int, @rc int, @messageTypeID int, 
	@messageStatusIDInserting int, @messageStatusIDQueued int, @sendingSiteResourceID int, 
	@messageID int, @rawcontent varchar(max), @footercontent varchar(max), @messageToParse varchar(MAX),
	@fieldID int, @fieldName varchar(60), @contentID int, @footerContentID [int], @languageID int
declare @metadataFields TABLE (fieldName varchar(60), fieldID int NULL)
declare @ruleID int, @xmlMembers xml
declare @fromName varchar(200), @fromEmail varchar(200), @replyToEmail varchar(200), @senderEmail varchar(200), @subject varchar(400), @contentVersionID int

IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
	DROP TABLE #tmpRecipients

select @messageTypeID = messageTypeID from platformMail.dbo.email_messageTypes where messageTypeCode = 'EMAILBLAST'
select @messageStatusIDInserting = statusID from platformMail.dbo.email_statuses where statusCode = 'I'
select @messageStatusIDQueued = statusID from platformMail.dbo.email_statuses where statusCode = 'Q'
select @languageID = dbo.fn_getLanguageID('en')
select @senderEmail = '<EMAIL>'

select @sendingSiteResourceID = st.siteResourceID
from dbo.sites as s
inner join dbo.admin_siteTools as st on st.siteID = s.siteID and st.siteID = @siteID
inner join dbo.admin_toolTypes as tt on tt.toolTypeID = st.toolTypeID and tt.toolType = 'EmailBlast'
	IF @@ERROR <> 0 RETURN -1

select @ruleID = ruleID, @fromName = fromName, @fromEmail = fromEmail, @replyToEmail = replyTo, @contentID = contentID, @footerContentID = footerContentID
from dbo.email_EmailBlasts
where blastID = @blastID
	IF @@ERROR <> 0 RETURN -1

select @subject=contentTitle, @contentVersionID=contentVersionID, @rawContent = rawContent
from dbo.fn_getContent(@contentID,@languageID) as messageContent
	IF @@ERROR <> 0 RETURN -1
	
SELECT @footercontent = rawcontent
FROM [dbo].[fn_getContent](@footerContentID,@languageID) AS footerContent
	IF @@ERROR <> 0 RETURN -1

EXEC dbo.ams_RunVirtualGroupRule @ruleID=@ruleID, @xmlVirtual=@xmlMembers OUTPUT
	IF @@ERROR <> 0 RETURN -1

select m.memberID, m.prefix, m.firstName, m.middlename, m.lastName, m.suffix, me.email
into #tmpRecipients
FROM @xmlMembers.nodes('//m') AS XM(item)
INNER JOIN dbo.ams_members as m WITH(NOLOCK) on m.memberid = XM.item.value('@id', 'int')
	and m.status = 'A'
inner join dbo.ams_memberEmails as me WITH(NOLOCK) on me.memberid = m.memberid
	and len(me.email) > 0
INNER JOIN dbo.ams_memberEmailTypes as met WITH(NOLOCK) on met.emailTypeID = me.emailTypeID
	and met.emailTypeOrder = 1
	IF @@ERROR <> 0 RETURN -1
	select @numRecipients = @@rowcount	

-- add any necessary metadata fields
select @messageToParse = replace(@messageWrapper,'@@rawcontent@@',@rawcontent)
	IF @@ERROR <> 0 RETURN -1
select @messageToParse = replace(@messageToParse,'@@footerContent@@',@footercontent)
	IF @@ERROR <> 0 RETURN -1
	
insert into @metadataFields (fieldName)
select distinct [Text]
from dbo.fn_RegexMatches(@messageToParse,'(?<=\[\[)([^,\]]+)(?=,?([^\]]+)?\]\])')
	IF @@ERROR <> 0 RETURN -1

BEGIN TRAN
	-- add email_message
	EXEC @rc = platformMail.dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID, 
		@sendingSiteResourceID=@sendingSiteResourceID, @recordedByMemberID=@recordedByMemberID, 
		@fromName=@fromName, @fromEmail=@fromEmail, 
		@replyToEmail=@replyToEmail, @senderEmail=@senderEmail, 
		@subject=@subject, @contentVersionID=@contentVersionID, 
		@messageWrapper=@messageWrapper, @messageID=@messageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @messageID = 0 goto on_error

	-- add recipients as I (not ready to be queued yet)
	insert into platformMail.dbo.email_messageRecipientHistory (messageID, memberID, 
		dateLastUpdated, toName, toEmail, emailStatusID, batchID, batchStartDate)
	select @messageID, memberID, getdate(), firstName + ' ' + lastName, email, @messageStatusIDInserting, null, null
	from #tmpRecipients
		IF @@ERROR <> 0 goto on_error

	-- add message metadata fields
	insert into platformMail.dbo.email_metadataFields (fieldName, isMergeField)
	select fieldName, 1
	from @metadataFields
		except
	select fieldName, isMergeField
	from platformMail.dbo.email_metadataFields
		IF @@ERROR <> 0 goto on_error
	update tmp
	set tmp.fieldID = MF.fieldID
	from @metadataFields as tmp
	inner join platformMail.dbo.email_metadataFields as MF on MF.fieldName = tmp.fieldName
	where MF.isMergeField = 1
		IF @@ERROR <> 0 goto on_error

	-- add recipient metadata

	select @fieldID = min(fieldID) from @metadataFields where fieldName in ('firstname','lastname','prefix','fullName','extendedname')
	while @fieldID is not null BEGIN
		select @fieldName = fieldName from @metadataFields where fieldID = @fieldID
			IF @@ERROR <> 0 goto on_error
		insert into platformMail.dbo.email_messageMetadataFields (messageID, fieldID, memberID, fieldValue)
		select @messageID, @fieldID, memberID, 
			fieldValue = case @fieldName
				when 'firstname' then firstname
				when 'lastname' then lastname
				when 'prefix' then prefix 
				when 'fullName' then firstname + ' ' + lastname
				when 'extendedname' then firstname + isnull(' ' + nullif(middlename,''),'') + ' ' + lastname + isnull(' ' + nullif(suffix,''),'')
				end
		from #tmpRecipients
			IF @@ERROR <> 0 goto on_error
		select @fieldID = min(fieldID) from @metadataFields where fieldName in ('firstname','lastname','prefix','fullName','extendedname') and fieldID > @fieldID
	END

	-- mark recipients as queued
	update mrh set
		emailStatusID = @messageStatusIDQueued
	from platformMail.dbo.email_messages m
	inner join platformMail.dbo.email_messageRecipientHistory mrh
		on m.messageID = mrh.messageID
		and m.messageID = @messageID
	IF @@ERROR <> 0 goto on_error

	-- mark emailSent if emailDateScheduled is not null so scheduled task will not attempt to send again.
	update dbo.email_EmailBlasts set 
		emailDateScheduled = null,
		emailDateSent = getDate()
	where blastID =  @blastID
	AND emailDateScheduled is not null
	IF @@ERROR <> 0 goto on_error

IF @@TRANCOUNT > 0 COMMIT TRAN

goto on_done

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN

	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients
	RETURN -1


on_done:
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients
	RETURN 0

