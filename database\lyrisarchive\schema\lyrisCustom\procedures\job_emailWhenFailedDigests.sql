ALTER PROC dbo.job_emailWhenFailedDigests

AS

DECLARE @badlist VARCHAR(60), @message VARCHAR(1000), @errorSubject VARCHAR(300);
EXEC dbo.checkForFailedDigests @dateoflastdigest=NULL, @daysToLookBack=1, @fixList=0, @badlist=@badlist OUTPUT;

IF @badlist IS NOT NULL BEGIN
	
	SET @message = 'Possible Failed Lyris Digests with list '+ @badlist;
	SET @errorSubject = 'Possible Failed Lyris Digests';
	
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@message, @forDev=1;

END
GO
