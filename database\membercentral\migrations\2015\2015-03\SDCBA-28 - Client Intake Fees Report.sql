USE memberCentral
GO

declare 
	@toolTypeID int, 
	@resourceTypeID int, 
	@siteID int,
	@parentnavigationID int, 
	@navigationID int, 
	@resourceTypeFunctionID int

select @siteID = siteID from  sites where siteCode = 'sdcba'

EXEC dbo.createAdminToolType 
		@toolType='ReferralClientIntakeFeesReport', 
		@toolCFC='Reports.Referrals.ClientIntakeFeesReport', 
		@toolDesc='Client Intake Fees Report', 
		@toolTypeID=@toolTypeID OUTPUT, 
		@resourceTypeID=@resourceTypeID OUTPUT

select 
	@parentNavigationID=n.navigationID
from 
	admin_navigation n
	inner join admin_navigation n2 on
		n2.navigationID = n.parentNavigationID
		and n2.navName = 'Referrals'
		and n2.navAreaID = 2
where 
	n.navname = 'General'
	and n.navAreaID = 3

EXEC dbo.createAdminNavigation 
		@navName='Client Intake Fees Report', 
		@navDesc='Allows user to run reports showing fees paid by clients over a date range. Allows filtering by panels and  referral metadata.', 
		@parentNavigationID=@parentnavigationID, 
		@navAreaID=4, 
		@cfcMethod='showReport', 
		@isHeader=0, 
		@showInNav=1, 
		@navigationID=@navigationID OUTPUT

select 
	@resourceTypeFunctionID = f.resourceTypeFunctionID 
from 
	cms_siteResourceTypeFunctions f
	inner join dbo.cms_siteResourceTypes rt on
		rt.resourceTypeID = f.resourceTypeID
		and resourceType = 'Admin'
	inner join dbo.cms_siteResourceFunctions rf on
		rf.functionID = f.functionID
		and rf.functionName = 'View'
		and rf.displayName = 'View'

EXEC dbo.createAdminFunctionsDeterminingNav 
		@resourceTypeFunctionID=@resourceTypeFunctionID, 
		@toolTypeID=@toolTypeID, 
		@navigationID=@navigationID

exec dbo.createAdminSuite @siteID=@siteID

insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
values(@tooltypeID, @siteID)
GO