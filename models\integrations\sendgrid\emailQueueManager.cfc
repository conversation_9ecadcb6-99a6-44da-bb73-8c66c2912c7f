<cfcomponent output="false" cache="true">
	<cffunction name="init" access="public" returntype="void">
		<cfscript>

			variables.sendgridUsername = "membercentral_sender";
			variables.sendgridPassword = "MvW4o!VFFzV5kSl";

			variables.HalonEmailIP = "************";
			variables.HalonEmailPort = "587";

			variables.nonAsciiRegex = "[^\x00-\x7F]";
			variables.objMimeUtility = createObject("java","javax.mail.internet.MimeUtility");
			variables.sendgridIPPools = getSendgridIPPools();
			variables.deliveryStatuses = getDeliveryStatusIDs();
			variables.sendgridSubUsersAndDomains = loadSendgridSubUsersAndDomains();		
			variables.bounceClassifications = getbounceClassifications();

			variables.javaSessionPools = {};

			
			switch(application.MCEnvironment) {
				case 'production': case 'beta':
					variables.webhookProcessingEnvironmentListFilter = application.MCEnvironment;
					break;
				case 'development': case 'test': case 'localDevelopment':
					variables.webhookProcessingEnvironmentListFilter = "development,test,localDevelopment,production";
					break;
			}
		</cfscript>
	</cffunction>

	<cffunction name="sendMessage" access="public" output="false" returntype="void">
		<cfargument name="messageID" type="numeric" required="true">

		<cfset var local = structNew()>

		<!--- mark the batch and get back the recipients to loop over --->
		<cfset local.strRecipientBatch = markRecipientBatch(batchSize=500, restrictToMessageID=arguments.messageID)>
		<cfset processBatch(local.strRecipientBatch)>
	</cffunction>
	
	<cffunction name="markRecipientBatch" access="package" output="false" returntype="struct">
		<cfargument name="batchSize" type="numeric" required="true">
		<cfargument name="restrictToMessageID" type="numeric" required="true" hint="Restrict batch to this messageID. If no restriction, pass zero">
		
		<cfset var local = structNew()>
		<cfset var returnStruct = { rc=0, batchUID='', qryMessages='', qrySubjectMetadata='', qryMessageMetadata='', qryRecipients='', qryRecipientMetadata='', qryRecipientAttachments='' }>
		
		<cftry>
			<cfstoredproc datasource="#application.dsn.platformMail.dsn#" procedure="email_markRecipientBatch" returncode="yes">
				<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.batchSize#">
				<cfif arguments.restrictToMessageID>
					<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.restrictToMessageID#">
				<cfelse>
					<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" null="true">
				</cfif>
				<cfprocparam cfsqltype="CF_SQL_IDSTAMP" type="out" variable="returnStruct.batchUID">
				<cfprocresult name="returnStruct.qryMessages" resultset="1">
				<cfprocresult name="returnStruct.qrySubjectMetadata" resultset="2">	
				<cfprocresult name="returnStruct.qryMessageMetadata" resultset="3">				
				<cfprocresult name="returnStruct.qryRecipients" resultset="4">
				<cfprocresult name="returnStruct.qryRecipientMetadata" resultset="5">
				<cfprocresult name="returnStruct.qryRecipientAttachments" resultset="6">
				<cfprocresult name="returnStruct.qryTaskParams" resultset="7">
				<cfprocresult name="returnStruct.qrySubusers" resultset="8">
				<cfprocresult name="returnStruct.qrySubuserDomains" resultset="9">
			</cfstoredproc>
			<cfset returnStruct.rc = cfstoredproc.statusCode>

			<cfif len(returnStruct.qrySubusers?.siteID_mailstreamID ?: '')>
				<cfquery name="returnStruct.strSubusers" dbtype="query" returntype="struct" columnkey="siteID_mailstreamID">
					select *
					from returnStruct.qrySubusers
				</cfquery>

				<cfquery name="returnStruct.strSubuserWarmupPlans" dbtype="query" returntype="struct" columnkey="warmupPlanStepID">
					select distinct warmupplanID, warmupPlanStepID, stepPhaseOutMessagesSent, stepPhaseInMessagesSent, stepMaxPhaseInMessagesAllowed, 0 as miniBatchPhaseOutMessagesSent, 0 as miniBatchPhaseInMessagesSent
					from returnStruct.qrySubusers
				</cfquery>
				<cfscript>
					returnStruct.strSubusers.each((subuserKey,subuserValue) => {
						subuserValue.subuserDomains = returnStruct.qrySubuserDomains.filter((row) => row.siteID_mailstreamID eq subuserValue.siteID_mailstreamID);
					});
				</cfscript>


			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset returnStruct.rc = -1>
		</cfcatch>
		</cftry> 

		<cfreturn returnStruct>
	</cffunction> 
	
	<cffunction name="processBatch" access="package" output="false" returntype="boolean">
		<cfargument name="strRecipientBatch" type="struct" required="true">
		
		<cfscript>
			var local = structNew();
			local.success = true;
			var processRecipientThreadCount = 4;
			var recipientsPerMiniBatch = 250;
			var sendingMethod = "smtp";
			var limitWarmupToInternalAddresses = 0;

			try {
				if ((arguments.strRecipientBatch.rc is 0) 
					and isDefined("arguments.strRecipientBatch.qryRecipients")
					and isQuery(arguments.strRecipientBatch.qryRecipients) 
					and arguments.strRecipientBatch.qryRecipients.recordcount) {

					var extraParams = {
						strRecipientBatch = arguments.strRecipientBatch,
					} ;

					// We may need to bump the number of threads based on delivery speeds
					if (val(arguments.strRecipientBatch.qryTaskParams?.threadCount ?: 0))
						processRecipientThreadCount = arguments.strRecipientBatch.qryTaskParams.threadCount;

					if (val(arguments.strRecipientBatch.qryTaskParams?.recipientsPerMiniBatch ?: 0))
						recipientsPerMiniBatch = arguments.strRecipientBatch.qryTaskParams.recipientsPerMiniBatch;

					if (listFindNoCase("smtp", (arguments.strRecipientBatch.qryTaskParams?.sendingMethod ?: '')))
						sendingMethod = arguments.strRecipientBatch.qryTaskParams.sendingMethod;

					if (structKeyExists(arguments.strRecipientBatch.qryTaskParams,"limitWarmupToInternalAddresses"))
						limitWarmupToInternalAddresses = arguments.strRecipientBatch.qryTaskParams.limitWarmupToInternalAddresses;

					extraParams.sendingTaskParams = {
						"limitWarmupToInternalAddresses" = limitWarmupToInternalAddresses,
						"javaSMTP" = ((arguments.strRecipientBatch.qryTaskParams?.javaSMTP ?: false) eq true)
					};

					// check all existing mail sessions and transports if we're using Java SMTP

					if (extraParams.sendingTaskParams.javaSMTP)
						checkAllJavaSMTPMailTransportPools();

					// limit request memory usage by slicing recipient query into Mini batches
					local.arrRecipientMiniBatches = [];
					local.currentSliceStartIndex = 1;
					while (local.currentSliceStartIndex lte arguments.strRecipientBatch.qryRecipients.recordcount) {
						if (local.currentSliceStartIndex + recipientsPerMiniBatch lte arguments.strRecipientBatch.qryRecipients.recordcount)
							local.arrRecipientMiniBatches.append(arguments.strRecipientBatch.qryRecipients.slice(local.currentSliceStartIndex, recipientsPerMiniBatch));
						else // take the remainder of the array
							local.arrRecipientMiniBatches.append(arguments.strRecipientBatch.qryRecipients.slice(local.currentSliceStartIndex));
						local.currentSliceStartIndex = local.currentSliceStartIndex + recipientsPerMiniBatch;
					}
					
					for (local.qryRecipients in local.arrRecipientMiniBatches) {
						QueryEach(local.qryRecipients,
							function(struct thisRow) {
								if (sendingMethod eq 'smtp') {
									local.subuser = extraParams.strRecipientBatch?.strSubusers[thisRow?.siteID_mailstreamID];
									local.resultStruct = sendEmailviaSMTP(thisRecipient=thisRow, strRecipientBatch=extraParams.strRecipientBatch, sendgridSubuserInfo=local.subuser, sendingTaskParams = extraParams.sendingTaskParams );
								}
							}
						, true, processRecipientThreadCount);
					}

					// update warmup step sending counts
					if (structKeyExists(arguments.strRecipientBatch,"strSubuserWarmupPlans")) {
						arguments.strRecipientBatch.strSubuserWarmupPlans.each(function(warmupPlanStepID, warmupplan) {
							if (warmupplan.miniBatchPhaseOutMessagesSent OR warmupplan.miniBatchPhaseInMessagesSent) {
								updateWarmupPlanMessagesSent(
									warmupPlanStepID=warmupPlanStepID,
									stepPhaseOutMessagesSent=warmupplan.miniBatchPhaseOutMessagesSent, 
									stepPhaseInMessagesSent=warmupplan.miniBatchPhaseInMessagesSent
								);
								warmupplan.miniBatchPhaseOutMessagesSent=0;
								warmupplan.miniBatchPhaseInMessagesSent=0;
							}
						});
					}
				}
			} catch(any cfcatch) {
				local.exceptionCustomMessage = 'Error occured in recipient sending loop in-between recipients. Some recipients may be left in grabbedForProcessing status until checkQueue grabs them.';
				application.objError.sendError(cfcatch=cfcatch, objectToDump=local, customMessage=local.exceptionCustomMessage);
				local.success = false;
			}

			return local.success;
		</cfscript>
	</cffunction> 

	<cffunction name="sendEmailviaSMTP" access="public" output="false" returntype="struct">
		<cfargument name="thisRecipient" type="struct" required="true">
		<cfargument name="strRecipientBatch" type="struct" required="true">
		<cfargument name="sendgridSubuserInfo" type="struct" required="true">
		<cfargument name="sendingTaskParams" type="struct" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnstruct = {
			usePhaseOutSettings = false,
			warmupPlanStepID = 0,
			stepPhaseInMessagesSent = 0,
			stepPhaseOutMessagesSent = 0

		}>

		<cfset local.thisEmailSent = false>
		<cfset local.thisCustomError = "">
		<cftry>
			<!--- get message details --->
			<cfquery name="local.qryMessage" dbtype="query">
				select *
				from [arguments].strRecipientBatch.qryMessages
				where messageID = #arguments.thisRecipient.messageID#
			</cfquery>
			<cfset local.siteInfo = application.objSiteInfo.getSiteInfo(sitecode=local.qryMessage.siteCode)>

			<!--- Verify Recipient still marked as Queued --->
			<cfif recipientHasStatus(arguments.thisRecipient.recipientID,'G')>
				<!--- Need to update recipient with status of Processing and updated date --->
				<cfset setRecipientStatus(siteID=local.siteInfo.siteID,messageID=arguments.thisRecipient.messageID,recipientID=arguments.thisRecipient.recipientID ,statusCode='P',updateDate=true)>

				<cfset local.messageSubject = local.qryMessage.subject>	
				<cfset local.messageBody = local.qryMessage.messageContent>	
				<cfset local.fromName = local.qryMessage.fromName>						
				
				<!--- ------------------- --->
				<!--- merge code handling ---> 
				<!--- ------------------- --->
				<cfif arguments.strRecipientBatch.qryMessageMetadata.recordcount gt 0 or arguments.strRecipientBatch.qrySubjectMetadata.recordcount gt 0>

					<!--- replace additional member merge codes --->
					<cfquery name="local.qryAdditionalMemberMetadata" dbtype="query">
						select fieldName, fieldValue, fieldTextToReplace
						from [arguments].strRecipientBatch.qryRecipientMetadata
						where recipientID = #arguments.thisRecipient.recipientID#
						and fieldTextToReplace is not null
					</cfquery>
					
					<cfif local.qryAdditionalMemberMetadata.recordCount>
						<cfloop query="local.qryAdditionalMemberMetadata">
							<cfset local.messageBody = replace(local.messageBody,local.qryAdditionalMemberMetadata.fieldTextToReplace,local.qryAdditionalMemberMetadata.fieldValue,"ALL")>
						</cfloop>
						<cfset local.baseurl = local.siteInfo.scheme & "://" & local.siteInfo.mainhostname & "/">
						<cfset local.messageBody = application.objApplications.qualifyAllLinks(content=local.messageBody, siteID=local.siteInfo.siteID, qualURL=local.baseurl)>
					</cfif>
				
					<!--- create a structure of all possible merge codes appearing in subject and message --->
					<cfset local.tempMergeData = structNew()>

					<cfif arguments.strRecipientBatch.qrySubjectMetadata.recordcount>
						<cfquery name="local.qrySubjectMetadata" dbtype="query">
							select fieldName1 
							from [arguments].strRecipientBatch.qrySubjectMetadata
							where messageID = #arguments.thisRecipient.messageID#
							order by fieldName1 
						</cfquery>
						<cfloop query="local.qrySubjectMetadata">
							<cfset structInsert(local.tempMergeData,local.qrySubjectMetadata.fieldname1,'',true)>
						</cfloop>
					</cfif>								
					<cfif arguments.strRecipientBatch.qryMessageMetadata.recordcount>
						<cfquery name="local.qryMessageMetadata" dbtype="query">
							select fieldName
							from [arguments].strRecipientBatch.qryMessageMetadata
							where messageID = #arguments.thisRecipient.messageID#
							order by fieldName 
						</cfquery>									
						<cfloop query="local.qryMessageMetadata">
							<cfset StructInsert(local.tempMergeData,local.qryMessageMetadata.fieldname,'',true)>
						</cfloop>								
					</cfif>
					
					<!--- create a structure of all member metadata. --->
					<cfquery name="local.qryMemberMetadata" dbtype="query">
						select fieldName, fieldValue
						from [arguments].strRecipientBatch.qryRecipientMetadata
						where recipientID = #arguments.thisRecipient.recipientID#
						and fieldTextToReplace is null
					</cfquery>
					<cfloop query="local.qryMemberMetadata">
						<cfif structKeyExists(local.tempMergeData,local.qryMemberMetadata.fieldName)>
							<cfset StructUpdate(local.tempMergeData,local.qryMemberMetadata.fieldName,local.qryMemberMetadata.fieldValue)>
						</cfif>
					</cfloop>
					
					<!--- Replace merge codes --->
					<cfloop collection="#local.tempMergeData#" item="local.thisField">
						<cfset local.messageSubject = reReplaceNoCase(local.messageSubject,"\[\[#local.thisField#\]\]",local.tempMergeData[local.thisField],"ALL")>

						<cfset local.fromName = reReplaceNoCase(local.fromName,"\[\[#local.thisField#\]\]",local.tempMergeData[local.thisField],"ALL")>

						<cfif len(trim(local.tempMergeData[local.thisField]))>
							<cfset local.messageBody = reReplaceNoCase(local.messageBody,"\[\[#local.thisField#,([^\]]+)\]\]",local.tempMergeData[local.thisField],"ALL")>
						<cfelse>
							<cfset local.messageBody = reReplaceNoCase(local.messageBody,"\[\[#local.thisField#,([^\]]+)\]\]","\1","ALL")>
						</cfif>
						<cfset local.messageBody = reReplaceNoCase(local.messageBody,"\[\[#local.thisField#\]\]",local.tempMergeData[local.thisField],"ALL")>
					</cfloop>
				</cfif>

				<!--- ----------- --->
				<!--- attachments ---> 
				<!--- ----------- --->
				<cfset local.arrMailAttachments = arrayNew(1)>
				<cfif arguments.strRecipientBatch.qryRecipientAttachments.recordcount>
					<cfquery name="local.qryRecipientAttachments" dbtype="query">
						select attachmentID, fileName, localDirectory
						from [arguments].strRecipientBatch.qryRecipientAttachments
						where recipientID = #arguments.thisRecipient.recipientID#
					</cfquery>

					<cfif local.qryRecipientAttachments.recordCount>
						<cfloop query="local.qryRecipientAttachments">
							<cfset local.localFileFound = false>

							<cfif FileExists('#local.qryRecipientAttachments.localDirectory#/#local.qryRecipientAttachments.fileName#')>
								<cfset local.localFileFound = true>
							<cfelse>
								<cfset local.s3keyMod = numberFormat(local.qryRecipientAttachments.attachmentID mod 1000,"0000")>
								<cfset local.objectKey = lCase('#application.MCEnvironment#/outgoing/#local.s3keyMod#/#local.qryRecipientAttachments.attachmentID#/#local.qryRecipientAttachments.fileName#')>
								<cfif application.objS3.s3FileExists(bucket='platformmail-membercentral-com', objectKey=local.objectKey, requestType="vhost")>
									
									<cfset local.displayName = ReReplaceNoCase(local.qryRecipientAttachments.fileName,'[^A-Z0-9 \-_.!\()]','','ALL')> 
									<cfset local.arrAmzHeaders = arrayNew(1)>
									<cfset local.tmpStr = { key="response-content-disposition", value="attachment; filename=""#local.displayName#""; filename*=UTF-8''#urlEncodedFormat(local.displayName)#" }>
									 <cfset arrayAppend(local.arrAmzHeaders,local.tmpStr)>

									<cfset local.s3FileURL = application.objS3.s3Url(bucket='platformmail-membercentral-com', objectKey=local.objectKey, requestType="vhost", canonicalizedAmzHeaders=local.arrAmzHeaders)>
									<cfif len(local.s3FileURL)>
										<cfif NOT directoryExists("#local.qryRecipientAttachments.localDirectory#")>
											<cfdirectory action="create" directory="#local.qryRecipientAttachments.localDirectory#">
										</cfif>
										<cfhttp method="get" getasbinary="yes" url="#local.s3FileURL#" path="#local.qryRecipientAttachments.localDirectory#" file="#local.qryRecipientAttachments.fileName#" timeout="60" throwonerror="true"></cfhttp>
										<cfset local.localFileFound = true>
									</cfif>
								</cfif>
							</cfif>

							<cfif local.localFileFound>
								<!--- get type from extension --->
								<cfswitch expression="#ListLast(local.qryRecipientAttachments.fileName,'.')#">
									<cfcase value="ach"><cfset local.type = "text/ach"></cfcase>
									<cfcase value="csv"><cfset local.type = "text/csv"></cfcase>
									<cfcase value="doc"><cfset local.type = "application/msword"></cfcase>
									<cfcase value="docx"><cfset local.type = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"></cfcase>
									<cfcase value="htm,html"><cfset local.type = "text/html"></cfcase>
									<cfcase value="iif"><cfset local.type = "text/iif"></cfcase>
									<cfcase value="m4v"><cfset local.type = "video/x-m4v"></cfcase>
									<cfcase value="mp3"><cfset local.type = "audio/mpeg"></cfcase>
									<cfcase value="mp4"><cfset local.type = "audio/mp4"></cfcase>
									<cfcase value="pdf"><cfset local.type = "application/pdf"></cfcase>
									<cfcase value="ppt"><cfset local.type = "application/vnd.ms-powerpoint"></cfcase>
									<cfcase value="pptx"><cfset local.type = "application/vnd.openxmlformats-officedocument.presentationml.presentation"></cfcase>
									<cfcase value="rtf"><cfset local.type = "application/rtf"></cfcase>
									<cfcase value="txt"><cfset local.type = "text/plain"></cfcase>
									<cfcase value="xls"><cfset local.type = "application/vnd.ms-excel"></cfcase>
									<cfcase value="xlsx"><cfset local.type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"></cfcase>
									<cfcase value="zip"><cfset local.type = "application/zip"></cfcase>
									<cfdefaultcase><cfset local.type = "application/octet-stream"></cfdefaultcase>
								</cfswitch>

								<cfset local.strMailAttach = structNew()>
								<cfset local.strMailAttach["file"] = "#local.qryRecipientAttachments.localDirectory#/#local.qryRecipientAttachments.fileName#">
								<cfset local.strMailAttach["type"] = local.type>
								<cfset arrayAppend(local.arrMailAttachments,local.strMailAttach)>
							</cfif>
						</cfloop>
					</cfif>
				</cfif>

				<!--- -------------------------------- --->
				<!--- Put the email together and send. --->
				<!--- -------------------------------- --->
				<cfscript>
					local.mailCollection = duplicate(application.mailservers.sendgrid);

					if (arguments.strRecipientBatch.qryTaskParams?.smtpAsync)
						local.mailCollection["async"] = true;
					else
						local.mailCollection["async"] = false;

					local.toName = checkBalancedParenthesis(arguments.thisRecipient.toName) ? arguments.thisRecipient.toName : reReplace(arguments.thisRecipient.toName, "[()]", "", "ALL");
					local.toEmail = arguments.thisRecipient.toEmail;
					

					var subUserSendingDetails = {
						subUserID = arguments.sendgridSubuserInfo.subUserID,
						outboundProviderCode = arguments.sendgridSubuserInfo.outboundProviderCode,
						smtphost = arguments.sendgridSubuserInfo.smtpHost,
						smtpPort = arguments.sendgridSubuserInfo.smtpPort,
						requestedFromDomain = listlast(local.qryMessage.fromEmail,"@"),
						defaultFromUsername = arguments.sendgridSubuserInfo.defaultFromUsername,
						defaultSendingHostname = arguments.sendgridSubuserInfo.defaultSendingHostname
					}
					subUserSendingDetails.allowedSendingDomains = arguments.sendgridSubuserInfo.subuserDomains.filter((row) => row.outboundProviderCode eq subUserSendingDetails.outboundProviderCode);


					if (refind(variables.nonAsciiRegex,local.toName))
						local.toName = variables.objMimeUtility.encodeText(local.toName);
					if (refind(variables.nonAsciiRegex,local.fromName))
						local.fromName = variables.objMimeUtility.encodeText(local.fromName);
					if (refind(variables.nonAsciiRegex,local.messageSubject))
						local.messageSubject = variables.objMimeUtility.encodeText(local.messageSubject);
					if (NOT listFindNoCase("production",application.MCEnvironment))
						local.toEmail = "<EMAIL>";

					if (len(local.toName))
						local.mailCollection["to"] = local.toEmail & ' ("' & local.toName & '")';
					else
						local.mailCollection["to"] = local.toEmail;

					if (len(local.qryMessage.replyToEmail))
						local.mailCollection["replyto"] = local.qryMessage.replyToEmail;						

					local.mailCollection["subject"] = local.messageSubject;
					local.mailCollection["mailerid"] = local.qryMessage.siteName;
					local.mailCollection["type"] = "HTML";
					local.mailCollection["server"] = arguments.sendgridSubuserInfo.smtphost;
					local.mailCollection["username"] = "apikey";
					local.mailCollection["password"] = arguments.sendgridSubuserInfo.apikey;
					local.mailCollection["port"] = arguments.sendgridSubuserInfo.smtpport;

					local.mailCollectionParams = duplicate(application.mailservers.environmentHeaders);
					local.mailCollectionParams["X-MemberCentral-Site"] = local.qryMessage.siteCode;
					local.mailCollectionParams["X-MemberCentral-Batch"] = arguments.strRecipientBatch.batchUID;
					
					if (len(local.qryMessage.consentListIDs)) {
						local.emailOptOutURL= generateConsentListManagementURL (
							siteID=local.siteInfo.siteID ,
							hostname=local.siteInfo.mainhostname ,
							memberID= arguments.thisRecipient.memberID,
							email=local.toEmail,
							messageID=arguments.thisRecipient.messageID
						)
						if (len(local.emailOptOutURL)) {
							local.mailCollectionParams["List-Unsubscribe"] = "<#local.emailOptOutURL#>";
							local.mailCollectionParams["List-Unsubscribe-Post"] = "List-Unsubscribe=One-Click";
						}

					}

					if (len(local.qryMessage.senderEmail))
						local.mailCollectionParams["Sender"] = local.qryMessage.senderEmail;

					// Add SMPTAPI header
					local.SMTPAPIStruct = {
						"ip_pool" = local.qryMessage.poolName,
						"category" = [local.qryMessage.messageType],
						"unique_args" = {
							"mc_mailtype" = "emailsendingqueue",
							"mc_mailtypecategory" = local.qryMessage.messageType,
							"mc_mailtypecategorycode" = local.qryMessage.messageTypeCode,
							"mc_environment" = application.MCEnvironment,
							"mc_sitecode" = local.qryMessage.sitecode,
							"mc_recipientid" = arguments.thisRecipient.recipientID,
							"mc_messageid" = arguments.thisRecipient.messageID,
							"mc_sentvia" = "SMTP",
							"sendgrid_subuser" = arguments.sendgridSubuserInfo.username
						}
					};

					// Implement warmup
					if (isnumeric(arguments.sendgridSubuserInfo?.warmupPlanID ?: '')) {
						local.thisWarmupPlanID = arguments.sendgridSubuserInfo.warmupPlanStepID;
						local.thisWarmupPlan = strRecipientBatch.strSubuserWarmupPlans[arguments.sendgridSubuserInfo.warmupPlanStepID];
					} else {
						local.thisWarmupPlanID = 0;
					}
					
					if (sendingTaskParams.limitWarmupToInternalAddresses AND NOT listfindNoCase("membercentral.com,seminarweb.com,trialsmith.com",listLast(local.toEmail,"@")))
						local.returnStruct.usePhaseOutSettings = true;

					else if (local.thisWarmupPlanID and local.thisWarmupPlan.stepPhaseInMessagesSent gte local.thisWarmupPlan.stepMaxPhaseInMessagesAllowed) {
						local.returnStruct.usePhaseOutSettings = true;
						local.returnStruct.warmupPlanStepID = arguments.sendgridSubuserInfo.warmupPlanStepID;
					} else if (local.thisWarmupPlanID) {
						local.returnStruct.usePhaseOutSettings = ((arguments.sendgridSubuserInfo?.stepRatio ?: 0) lt rand("SHA1PRNG"));
						local.returnStruct.warmupPlanStepID = arguments.sendgridSubuserInfo.warmupPlanStepID;
					} else
						local.returnStruct.usePhaseOutSettings = false;

					if (local.thisWarmupPlanID and not local.returnStruct.usePhaseOutSettings) {
						local.thisWarmupPlan.stepPhaseInMessagesSent++;
						local.thisWarmupPlan.miniBatchPhaseInMessagesSent++;
					} else if (local.returnStruct.usePhaseOutSettings and structKeyExists(arguments.sendgridSubuserInfo,"phaseout_apikey") and len(arguments.sendgridSubuserInfo.phaseout_apikey)) {

						local.thisWarmupPlan.stepPhaseOutMessagesSent++;
						local.thisWarmupPlan.miniBatchPhaseOutMessagesSent++;

						subUserSendingDetails.subUserID = arguments.sendgridSubuserInfo.phaseout_subUserID;
						subUserSendingDetails.outboundProviderCode = arguments.sendgridSubuserInfo.phaseout_outboundProviderCode;
						subUserSendingDetails.smtphost = arguments.sendgridSubuserInfo.phaseout_smtpHost;
						subUserSendingDetails.smtpPort = arguments.sendgridSubuserInfo.phaseout_smtpPort;
						subUserSendingDetails.defaultFromUsername = arguments.sendgridSubuserInfo.phaseout_defaultFromUsername;
						subUserSendingDetails.defaultSendingHostname = arguments.sendgridSubuserInfo.phaseout_defaultSendingHostname;
	
						local.mailCollection["username"] = "apikey";
						local.mailCollection["password"] = arguments.sendgridSubuserInfo.phaseout_apikey;
						local.SMTPAPIStruct["ip_pool"] = arguments.sendgridSubuserInfo.phaseout_poolName;
						local.SMTPAPIStruct["unique_args"]["sendgrid_subuser"] = arguments.sendgridSubuserInfo.phaseout_username;
					}

					subUserSendingDetails.allowedSendingDomains = arguments.sendgridSubuserInfo.subuserDomains.filter((row) => row.outboundProviderCode eq subUserSendingDetails.outboundProviderCode);
					subUserSendingDetails.selectedSendingDomain = subUserSendingDetails.allowedSendingDomains.filter((row) => row.sendingHostname eq subUserSendingDetails.requestedFromDomain);

					// make sure that valid from domain is used
					if (subUserSendingDetails.selectedSendingDomain.recordcount)
						local.fromEmail = local.qryMessage.fromEmail;
					else {
						subUserSendingDetails.selectedSendingDomain = subUserSendingDetails.allowedSendingDomains.filter((row) => row.sendingHostname eq subUserSendingDetails.defaultSendingHostname);
						local.fromEmail = "#subUserSendingDetails.defaultFromUsername#@#subUserSendingDetails.defaultSendingHostname#";
					}

					if (len(local.fromName))
						local.mailCollection["from"] = local.fromEmail & ' ("' & local.fromName & '")'; 
					else
						local.mailCollection["from"] = local.fromEmail; 



					// build whitelabel header based on selected from domain 
					local.mailCollectionParams["X-HALON-WHITELABEL"] = serializeJSON({
						"todomain":"", 
						"mailfromDomain": subUserSendingDetails.selectedSendingDomain.returnPathHostname, 
						"trackingDomain": subUserSendingDetails.selectedSendingDomain.linkBrandHostname
					});

					local.SMTPAPIStruct["unique_args"]["mc_fromaddress"] = local.fromEmail;
					local.SMTPAPIStruct["unique_args"]["sendgrid_pool"] = local.SMTPAPIStruct["ip_pool"];

					local.mailCollectionParams["X-SMTPAPI"] = serializeJSON(local.SMTPAPIStruct);

					local.mailCollection["server"] = subUserSendingDetails.smtpHost;
					local.mailCollection["port"] = subUserSendingDetails.smtpPort;

					local.mailCollectionParams["X-MC-SENDGRIDSUBUSER"] = arguments.sendgridSubuserInfo.username;

					switch (subUserSendingDetails.outboundProviderCode) {
						case "HALON":
							local.mailCollection["username"] = "";
							local.mailCollection["password"] = "";
							local.mailCollectionParams["X-WARMUP-OK"] = 'Y';
							local.mailCollectionParams["X-Relay-Volume"] = "0";
							local.mailCollectionParams["X-HALON-IPPOOL"] = lcase( local.qryMessage.poolName );
							local.mailCollectionParams["X-DKIM"] = "#subUserSendingDetails.selectedSendingDomain.dkimSelectorActive#," & lcase("#local.qryMessage.siteCode#.#subUserSendingDetails.selectedSendingDomain.dkimSelectorActive#") & "," & lcase(subUserSendingDetails.selectedSendingDomain.sendingHostname);
							
							break;
						case "SENDGRID":
							if (arguments.sendgridSubuserInfo.deliveryMode eq "postfix" and application.MCEnvironment eq "production") {
								local.mailCollection["username"] = application.mailservers.sendgrid.username;
								local.mailCollection["password"] = application.mailservers.sendgrid.password;
								local.mailCollectionParams["X-WARMUP-OK"] = 'N';
								local.mailCollectionParams["X-Relay-Volume"] = "100";
								local.mailCollectionParams["X-HALON-IPPOOL"] = "";
							}
							break;
					}

					
					var args2 = duplicate(arguments.thisRecipient);
					local.mail.listener = {
						after = function () {
							local.myargs = duplicate(arguments);
							local.msg = { 
								amessage: "*********Email QueueManager: inside listener",
								toemail: local.myargs.detail.to,
								memberID: args2.memberID,
								recipientID: args2.recipientID,
								messageID: args2.messageID,
								exceptions: local.myargs.advanced.exceptions
							 };
							 systemOutput(obj=local.msg, addNewLine=true, doErrorStream=true);
						}
					};
					local.mailCollectionParamsAlphaOrder = listSort(local.mailCollectionParams.keyList(),"textnocase");
				</cfscript>

				<cfif sendingTaskParams.javaSMTP>
					<cfscript>
						// grab connection from pool
						local.transportStruct = checkoutJavaSMTPMailTransport(local.mailCollection["server"], local.mailCollection["port"], local.mailCollection["username"], local.mailCollection["password"]);

						// Create the message
						local.message = createObject("java", "javax.mail.internet.MimeMessage");
						local.message.init(local.transportStruct.mailSession);
						if (len(local.fromName))
							local.message.setFrom(createObject("java", "javax.mail.internet.InternetAddress").init(local.fromEmail,local.fromName));
						else
							local.message.setFrom(createObject("java", "javax.mail.internet.InternetAddress").init(local.fromEmail));


						// handle unexpected delimited list of TO email addresses
						for (local.thisToEmail in listToArray(local.toEmail, ",;")) { 
							if (len(local.toName))
								local.message.addRecipient(createObject("java", "javax.mail.Message$RecipientType").TO, createObject("java", "javax.mail.internet.InternetAddress").init(local.thisToEmail,local.toName))
							else
								local.message.addRecipient(createObject("java", "javax.mail.Message$RecipientType").TO, createObject("java", "javax.mail.internet.InternetAddress").init(local.thisToEmail));
						} 

						// handle adding a replyto if it exists
						if (local.mailCollection.keyExists('replyto') and len(local.mailCollection["replyto"])) {
							// Set the "Reply-To" address
							local.replyTo = [createObject("java", "javax.mail.internet.InternetAddress").init(local.mailCollection["replyto"])];
							local.message.setReplyTo(local.replyTo);
						}

						local.message.setSubject(local.messageSubject);
						local.message.setHeader("Mailer-ID", local.qryMessage.siteName)
						// add some connection pool info to a header for visual inspection
						local.message.setHeader("X-MC-JAVASMTP-INFO", serializeJSON(local.transportStruct.filter((key, value) => listFindNoCase("messagesSent,created,poolKey",key))));
						// Add Email Headers
						for (local.thisKey in listToArray(local.mailCollectionParamsAlphaOrder, ",")) {
							local.message.setHeader(local.thisKey, local.mailCollectionParams[local.thisKey]);
						}

						if (not local.arrMailAttachments.len()) {
							local.message.setContent(local.messageBody, "text/html; charset=utf-8");
							local.message.setHeader("Content-Transfer-Encoding", "quoted-printable"); // Set quoted-printable encoding
						} else {

							// Create the message body part and encode it as quoted-printable
							local.messageBodyPart = createObject("java", "javax.mail.internet.MimeBodyPart");
							local.messageBodyPart.setContent(local.messageBody, "text/html; charset=utf-8");
							local.messageBodyPart.setHeader("Content-Transfer-Encoding", "quoted-printable"); // Set quoted-printable encoding

							// Create the multipart container to hold the body and attachments
							local.multipart = createObject("java", "javax.mail.internet.MimeMultipart");
							local.multipart.addBodyPart(local.messageBodyPart);

							// Add Attachments (using streams to limit RAM usage)
							for (local.thisAttach in local.arrMailAttachments) {
								if (FileExists(local.thisAttach.file)) {
									local.attachmentBodyPart = createObject("java", "javax.mail.internet.MimeBodyPart");
									local.attachmentBodyPart.setDataHandler(
										createObject("java", "javax.activation.DataHandler").init(
											createObject("java", "javax.activation.FileDataSource").init(local.thisAttach.file)
										)
									);
									local.attachmentBodyPart.setFileName(listLast(local.thisAttach.file,'/'));
									local.attachmentBodyPart.setHeader("Content-Type", local.thisAttach.type);
									local.attachmentBodyPart.setHeader("Content-Transfer-Encoding", "base64"); // Set base64 encoding for attachment
									local.multipart.addBodyPart(local.attachmentBodyPart);
								}
							}

							// Set the content of the message to the multipart container
							local.message.setContent(local.multipart);
						}

						local.transportStruct.transport.sendMessage(local.message, local.message.getAllRecipients());
						local.transportStruct.messagesSent = local.transportStruct.messagesSent + 1;
						// return connection to the pool
						checkinJavaSMTPMailTransport(local.transportStruct);

					</cfscript>
				<cfelseif application.enableMailHanderLogging EQ true>
					<cfmail attributecollection="#local.mailCollection#" listener="#local.mail.listener#" async="true">
						<cfloop list="#local.mailCollectionParamsAlphaOrder#" item="local.mailparam">
							<cfmailparam name="#local.mailparam#" value="#local.mailCollectionParams[local.mailparam]#">
						</cfloop>
						<cfloop array="#local.arrMailAttachments#" index="local.thisAttach">
							<cfif FileExists("#local.thisAttach.file#")>
								<cfmailparam file="#local.thisAttach.file#" type="#local.thisAttach.type#">
							</cfif>
						</cfloop>
						#local.messageBody#
					</cfmail> 
				<cfelse>
					<cfmail attributecollection="#local.mailCollection#">
						<cfloop list="#local.mailCollectionParamsAlphaOrder#" item="local.mailparam">
							<cfmailparam name="#local.mailparam#" value="#local.mailCollectionParams[local.mailparam]#">
						</cfloop>
						<cfloop array="#local.arrMailAttachments#" index="local.thisAttach">
							<cfif FileExists("#local.thisAttach.file#")>
								<cfmailparam file="#local.thisAttach.file#" type="#local.thisAttach.type#">
							</cfif>
						</cfloop>
						#local.messageBody#
					</cfmail> 					
				</cfif>

				<cfset local.thisEmailSent = true>
	
				<!--- Need to update recipient with status of Sent and updated date --->
				<cfset setRecipientStatus(siteID=local.siteInfo.siteID,messageID=arguments.thisRecipient.messageID,recipientID=arguments.thisRecipient.recipientID ,statusCode='released',updateDate=true)>

			</cfif>
		<cfcatch type="Any">
			<cfif local.thisEmailSent>
				<cfset local.exceptionCustomMessage = 'Error occured after CFMAIL tag. Message may have been sent. Requires investigation.'>
				<cfset local.errorStatus = 'E'>
			<cfelse>
				<cfset local.exceptionCustomMessage = 'Error occured before execution of CFMAIL tag. Will attempt to mark recipient as Requeued so that checkQueue will reset message to be retried in a few minutes.'>
				<cfset local.errorStatus = 'R'>
			</cfif>
			<cfif findNoCase("message file too big",cfcatch.message)>
				<cfset local.exceptionCustomMessage = 'Message too big and has been cancelled. Requires investigation.'>
				<cfset local.errorStatus = 'C'>
			</cfif>

			<!--- Need to update recipient with appropriate status. Will be picked up in a few minutes by checkQueue --->
			<cfset setRecipientStatus(siteID=local.siteInfo.siteID,messageID=arguments.thisRecipient.messageID,recipientID=arguments.thisRecipient.recipientID ,statusCode=local.errorStatus,updateDate=true)>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump={local=local,arguments=arguments}, customMessage=local.exceptionCustomMessage)>
		</cfcatch>
		</cftry>

		<cfreturn local.returnstruct>
	</cffunction>

	<cffunction name="checkoutJavaSMTPMailTransport" access="private" returntype="struct" output="false">
		<cfargument name="server" type="string" required="true">
		<cfargument name="port" type="numeric" required="true">
		<cfargument name="username" type="string" required="true">
		<cfargument name="password" type="string" required="true">
		
		<cfscript>
			var local = {};
			// get a code unique to the combination of arguments for poolKey
			try {
				local.poolKey = getJavaSMTPMailTransportPoolKey(arguments.server,arguments.port,arguments.username,arguments.password);

				// readonly lock to pause checkin/checkout while checkJavaSMTPMailTransportPool does its work
				lock timeout="2" type="readonly" name=local.poolKey { 
					// make sure that variables.javaSessionPools[local.poolKey] exists in thread-safe manner
					if (not structKeyExists(variables.javaSessionPools,local.poolKey))
						structAppend(variables.javaSessionPools,{"#local.poolKey#":[]},false);

					try {
						// grab first available connection and remove from pool
						local.transportStruct = arrayShift(variables.javaSessionPools[local.poolKey]);
					} catch(expression e) {
						// if none found, catch the exception and generate a new one
						local.transportStruct = generateJavaSMTPMailTransport(arguments.server,arguments.port,arguments.username,arguments.password);
					} catch (any e) {
						application.objError.sendError(cfcatch=e, objectToDump=arguments, customMessage="Error grabbing a checkoutJavaSMTPMailTransport")
					}

					//make sure grabbed transport is still good, if not discard and get a new one
					if (not structKeyExists(local,"transportStruct") or not isJavaSMTPMailTransportStillGood(local.transportStruct))
						local.transportStruct = generateJavaSMTPMailTransport(arguments.server,arguments.port,arguments.username,arguments.password);
				}
			} catch(e) {
				application.objError.sendError(cfcatch=e, objectToDump={arguments:arguments, local:local, pool:variables.javaSessionPools[local.poolKey]}, customMessage="Error in checkoutJavaSMTPMailTransport. + rethrow")
				rethrow;
			}
			return local.transportStruct;
		</cfscript>
	</cffunction>
	<cffunction name="checkinJavaSMTPMailTransport" access="private" returntype="void" output="false">
		<cfargument name="transportStruct" type="struct" required="true">
		
		<cfscript>
			var local = {};

			// readonly lock to pause checkin/checkout while checkJavaSMTPMailTransportPool does its work
			lock timeout="2" type="readonly" name=arguments.transportStruct.poolKey { 
				// make sure that variables.javaSessionPools[local.poolKey] exists in thread-safe manner
				if (not structKeyExists(variables.javaSessionPools,arguments.transportStruct.poolKey))
					structAppend(variables.javaSessionPools,{"#arguments.transportStruct.poolKey#":[]},false);

				// add connection to pool of available connections if it's still valid
				if (isJavaSMTPMailTransportStillGood(arguments.transportStruct))
					variables.javaSessionPools[arguments.transportStruct.poolKey].push(arguments.transportStruct);
				else {
					destroyJavaSMTPMailTransport(arguments.transportStruct);
				}
			}

		</cfscript>
	</cffunction>

	<cffunction name="isJavaSMTPMailTransportStillGood" access="private" returntype="boolean" output="false">
		<cfargument name="transportStruct" type="struct" required="true">
		
		<cfscript>
			var local = {};
			local.messageLimit = 100;
			local.ttl = 60; //how many seconds to keep connection alive

			local.isUnderMessageLimit = (arguments.transportStruct.keyExists("messagesSent") and  arguments.transportStruct.messagesSent lte local.messageLimit);
			local.isStillFresh = (arguments.transportStruct.keyExists("created") and  ((getTickCount() - arguments.transportStruct.created)/1000) lte local.ttl);
			local.stillConnected = (arguments.transportStruct.keyExists("transport") and arguments.transportStruct.transport.isConnected());

			return (local.isUnderMessageLimit and local.isStillFresh and local.stillConnected)
		</cfscript>
	</cffunction>

	<cffunction name="checkAllJavaSMTPMailTransportPools" access="private" returntype="void" output="false">
		<cfscript>
			variables.javaSessionPools.each((key, value, struct) => checkJavaSMTPMailTransportPool(poolArray=value,lockName=key),true,4);
		</cfscript>
	</cffunction>
	<cffunction name="checkJavaSMTPMailTransportPool" access="private" returntype="void" output="false">
		<cfargument name="poolArray" type="array" required="true">
		<cfargument name="lockName" type="string" required="true">
		
		<cfscript>
			lock timeout="2" type="exclusive" name=arguments.lockName { 
				arguments.poolArray = arguments.poolArray.filter((element) => {
					var keepit = isJavaSMTPMailTransportStillGood(element);
					if (not keepIt)
						destroyJavaSMTPMailTransport(element);
					return keepit;
				})
			} 
		</cfscript>
	</cffunction>
	<cffunction name="generateJavaSMTPMailTransport" access="private" returntype="struct" output="false">
		<cfargument name="server" type="string" required="true">
		<cfargument name="port" type="numeric" required="true">
		<cfargument name="username" type="string" required="true">
		<cfargument name="password" type="string" required="true">
		
		<cfscript>
			var local = {};

			// get a code unique to the combination of arguments
			local.poolKey = getJavaSMTPMailTransportPoolKey(arguments.server,arguments.port,arguments.username,arguments.password);

			local.props = createObject("java", "java.util.Properties");
			local.props.setProperty("mail.smtp.host", arguments.server);
			local.props.setProperty("mail.smtp.port", arguments.port);
			local.props.setProperty("mail.smtp.starttls.enable", "false");
			if (arguments.username.len())
				local.props.setProperty("mail.smtp.auth", "true");
			else {
				local.props.setProperty("mail.smtp.auth", "false");
			}
				
			local.mailSession = createObject("java", "javax.mail.Session").getInstance(local.props);
			local.mailSession.setDebug(false); // Enable debugging
			local.transport = local.mailSession.getTransport("smtp");

			if (arguments.username.len()) {
				local.transport.connect(arguments.server,arguments.port,arguments.username,arguments.password);
			}
			else {
				local.transport.connect();
			}
			return {
				mailSession = local.mailSession,
				transport = local.transport,
				messagesSent = 0,
				created = getTickCount(),
				poolKey = local.poolKey
			};
		</cfscript>
	</cffunction>
	<cffunction name="destroyJavaSMTPMailTransport" access="private" returntype="void" output="false">
		<cfargument name="transportStruct" type="struct" required="true">
		
		<cfscript>
			try arguments.transportStruct.mailSession.close(); catch (e) { }
			try arguments.transportStruct.mailSession.finalize(); catch (e) { }
			try arguments.transportStruct.transport.close(); catch (e) { }
			try arguments.transportStruct.transport.finalize(); catch (e) { }
			try arguments.transportStruct.delete("mailSession"); catch (e) { }
			try arguments.transportStruct.delete("transport"); catch (e) { }
			try arguments.transportStruct.delete("messagesSent"); catch (e) { }
			try arguments.transportStruct.delete("created"); catch (e) { }
			try arguments.transportStruct.delete("poolKey"); catch (e) { }

		</cfscript>
	</cffunction>
	<cffunction name="getJavaSMTPMailTransportPoolKey" access="private" returntype="string" output="false" hint="generates a unique key used to segment the connection pool">
		<cfargument name="server" type="string" required="true">
		<cfargument name="port" type="numeric" required="true">
		<cfargument name="username" type="string" required="true">
		<cfargument name="password" type="string" required="true">

		<cfreturn hash("#lcase(arguments.server)#|#arguments.port#|#arguments.username#|#arguments.password#","SHA", "UTF-8")>
	</cffunction>


	<cffunction name="updateWarmupPlanMessagesSent" access="private" returntype="void" output="false">
		<cfargument name="warmupPlanStepID" type="numeric" required="true">
		<cfargument name="stepPhaseOutMessagesSent" type="numeric" required="true">
		<cfargument name="stepPhaseInMessagesSent" type="numeric" required="true">
		
		<cfset var local = structnew()>

		<cfquery name="local.qryStatus" datasource="#application.dsn.platformMail.dsn#" result="local.qryJobIssuesResult">
			set nocount on;

			declare 
				@warmupPlanStepID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.warmupPlanStepID#">, 
				@stepPhaseOutMessagesSent int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.stepPhaseOutMessagesSent#">,
				@stepPhaseInMessagesSent int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.stepPhaseInMessagesSent#">;

			update sendgrid_subuserMailstreamWarmupPlanSteps set
				stepPhaseOutMessagesSent = stepPhaseOutMessagesSent + @stepPhaseOutMessagesSent,
				stepPhaseInMessagesSent = stepPhaseInMessagesSent + @stepPhaseInMessagesSent
			where warmupPlanStepID = @warmupPlanStepID;
		</cfquery>
	</cffunction>

	<cffunction name="recipientHasStatus" access="private" returntype="boolean" output="false">
		<cfargument name="recipientID" type="numeric" required="true">
		<cfargument name="statusCode" type="string" required="true">

		<cfset var local = structnew()>

		<cfquery name="local.qryStatus" datasource="#application.dsn.platformMail.dsn#" result="local.qryJobIssuesResult">
			set nocount on;

			declare @statusID int, @recipientID int;

			select @statusID=statusID
			from dbo.email_statuses
			where statusCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.statusCode#">;

			set @recipientID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.recipientID#">;

			select recipientID
			from dbo.email_messageRecipientHistory mrh
			where emailStatusID = @statusID and recipientID = @recipientID;
		</cfquery>

		<cfreturn (local.qryStatus.recordcount gt 0)>
	</cffunction>

	<cffunction name="setRecipientStatus" access="private" returntype="void" output="false">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="messageID" type="numeric" required="true">
		<cfargument name="recipientID" type="numeric" required="true">
		<cfargument name="statusCode" type="string" required="true">
		<cfargument name="updateDate" type="boolean" required="true">

		<cfset var local = structnew()>

		<cftry>
			<cfquery datasource="#application.dsn.platformMail.dsn#" name="local.qryChangeStatus">
				EXEC dbo.email_setMessageRecipientHistoryStatus
					@siteID=<cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">,
					@messageID=<cfqueryparam value="#arguments.messageID#" cfsqltype="CF_SQL_INTEGER">,
					@recipientID=<cfqueryparam value="#arguments.recipientID#" cfsqltype="CF_SQL_INTEGER">,
					@statusCode=<cfqueryparam value="#arguments.statusCode#" cfsqltype="CF_SQL_VARCHAR">,
					@updateDate=<cfqueryparam value="#arguments.updateDate#" cfsqltype="CF_SQL_BIT">
			</cfquery>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments, customMessage="Error calling email_setMessageRecipientHistoryStatus for a Sendgrid event webhook entry")>
		</cfcatch>
		</cftry>
	</cffunction>

	<cffunction name="recordHalonStatusTracking" access="private" returntype="boolean" output="false">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="messageID" type="numeric" required="true">
		<cfargument name="recipientID" type="numeric" required="true">
		<cfargument name="statusCode" type="string" required="true">
		<cfargument name="sgSubuser" type="string" required="true">
		<cfargument name="updateDate" type="boolean" required="true">
		<cfargument name="sgEventID" type="string" required="true">
		<cfargument name="ipaddress" type="string" required="true">
		<cfargument name="detail" type="string" required="true">
		<cfargument name="reason" type="string" required="true">
		<cfargument name="status" type="string" required="true">
		<cfargument name="responseDate" type="date" required="true">
		<cfargument name="jsonResult" type="string" required="true">
		<cfargument name="logid" type="string" required="true">
		<cfargument name="class" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.success = true>

		<cfif arguments.updateDate>
			<cfset local.updateDate = 1>
		<cfelse>			
			<cfset local.updateDate = 0>
		</cfif>
		<!--- Look up classID based on class --->
		<cfset local.classID = 0>
		<cfif len(arguments.class)>
			<cfset local.classID = getBounceClassification(arguments.class)>
		</cfif>
		
		<cfset local.xmlMessage = "<t><siteid>#arguments.siteID#</siteid><m>#arguments.messageID#</m><r>#arguments.recipientID#</r><c>#arguments.statusCode#</c><s>#XMLFORMAT(arguments.sgSubuser)#</s><u>#local.updateDate#</u><e>#XMLFORMAT(arguments.sgEventID)#</e><i>#arguments.ipaddress#</i><d>#XMLFORMAT(arguments.detail)#</d><x>#XMLFORMAT(left(arguments.reason,200))#</x><y>#XMLFORMAT(left(arguments.status,200))#</y><z>#DateTimeFormat(arguments.responseDate,'m/d/yyyy h:nn:ss tt')#</z><j>#XMLFORMAT(arguments.jsonResult)#</j><l>#arguments.logid#</l><b>#local.classID#</b></t>">

		<cftry>
			<cfstoredproc datasource="#application.dsn.platformQueue.dsn#" procedure="HalonStatusTracking_sendMessage">
				<cfprocparam cfsqltype="cf_sql_longvarchar" type="In" value="#local.xmlMessage#">
			</cfstoredproc>
		<cfcatch type="any">
			<cfset local.success = false>
			<cfset local.arguments = arguments>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local, customMessage="Error calling HalonStatusTracking_sendMessage for a Halon event webhook entry")>
		</cfcatch>
		</cftry>
		<cfreturn local.success>
	</cffunction>

	<cffunction name="recordSendGridStatusTracking" access="private" returntype="boolean" output="false">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="messageID" type="numeric" required="true">
		<cfargument name="recipientID" type="numeric" required="true">
		<cfargument name="statusCode" type="string" required="true">
		<cfargument name="sgSubuser" type="string" required="true">
		<cfargument name="updateDate" type="boolean" required="true">
		<cfargument name="sgEventID" type="string" required="true">
		<cfargument name="ipaddress" type="string" required="true">
		<cfargument name="detail" type="string" required="true">
		<cfargument name="reason" type="string" required="true">
		<cfargument name="status" type="string" required="true">
		<cfargument name="responseDate" type="date" required="true">
		<cfargument name="jsonResult" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.success = true>

		<cfif arguments.updateDate>
			<cfset local.updateDate = 1>
		<cfelse>			
			<cfset local.updateDate = 0>
		</cfif>

		<cfset local.xmlMessage = "<t><siteid>#arguments.siteID#</siteid><m>#arguments.messageID#</m><r>#arguments.recipientID#</r><c>#arguments.statusCode#</c><s>#XMLFORMAT(arguments.sgSubuser)#</s><u>#local.updateDate#</u><e>#XMLFORMAT(arguments.sgEventID)#</e><i>#arguments.ipaddress#</i><d>#XMLFORMAT(arguments.detail)#</d><x>#XMLFORMAT(left(arguments.reason,200))#</x><y>#XMLFORMAT(left(arguments.status,200))#</y><z>#DateTimeFormat(arguments.responseDate,'m/d/yyyy h:nn:ss tt')#</z><j>#XMLFORMAT(arguments.jsonResult)#</j></t>">

		<cftry>
			<cfstoredproc datasource="#application.dsn.platformQueue.dsn#" procedure="SendGridStatusTracking_sendMessage">
				<cfprocparam cfsqltype="cf_sql_longvarchar" type="In" value="#local.xmlMessage#">
			</cfstoredproc>
		<cfcatch type="any">
			<cfset local.success = false>
			<cfset local.arguments = arguments>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local, customMessage="Error calling SendGridStatusTracking_sendMessage for a Sendgrid event webhook entry")>
		</cfcatch>
		</cftry>
		<cfreturn local.success>
	</cffunction>

	<cffunction name="sendLyrisDeliveryTrackingMessage" access="private" returntype="boolean" output="false">
		<cfargument name="applicationType" type="string" required="true">
		<cfargument name="applicationName" type="string" required="true">
		<cfargument name="sgMessageID" type="string" required="true">
		<cfargument name="sgEventID" type="string" required="true">
		<cfargument name="event" type="string" required="true">
		<cfargument name="timestamp" type="date" required="true">
		<cfargument name="membernumber" type="string" required="true">
		<cfargument name="sendingApplicationID" type="numeric" required="true">
		<cfargument name="sendingApplicationMessageID" type="numeric" required="true">
		<cfargument name="sendingApplicationRecipientID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="emailusername" type="string" required="true">
		<cfargument name="emaildomain" type="string" required="true">
		<cfargument name="emaildomainID" type="numeric" required="true">
		<cfargument name="deliveryStatusID" type="numeric" required="true">
		<cfargument name="smtpstatuscode" type="string" required="true">
		<cfargument name="attempt" type="numeric" required="true">
		<cfargument name="ipPoolID" type="numeric" required="true">
		<cfargument name="sendgridSubuserID" type="numeric" required="true">
		<cfargument name="sendgridSubuserDomainID" type="numeric" required="true">
		<cfargument name="messageTemplate" type="string" required="true">
		<cfargument name="messageSHA1" type="string" required="true">
		<cfargument name="messageTemplateMatchFound" type="boolean" required="true">
		<cfargument name="bounceClassificationID" type="numeric" required="true">
				
		<cfset var local = structNew()>
		<cfset local.success = true>
		<cfsavecontent variable="local.xmlMessage">
			<cfoutput>
				<t>
					<messagetype>delivery</messagetype>
					<applicationtype>#xmlformat(arguments.applicationtype)#</applicationtype>
					<applicationname>#xmlformat(arguments.applicationname)#</applicationname>
					<periodcode>#dateTimeFormat(arguments.timestamp,'yyyymmddHH')#</periodcode>
					<event>#xmlformat(arguments.event)#</event>
					<sgmessageid>#xmlformat(arguments.sgmessageid)#</sgmessageid>
					<sgeventid>#xmlformat(arguments.sgeventid)#</sgeventid>
					<timestamp>#DateTimeFormat(arguments.timestamp,'m/d/yyyy h:nn:ss tt')#</timestamp>
					<membernumber>#xmlformat(arguments.membernumber)#</membernumber>
					<sendingapplicationid>#xmlformat(arguments.sendingapplicationid)#</sendingapplicationid>
					<sendingapplicationmessageid>#xmlformat(arguments.sendingapplicationmessageid)#</sendingapplicationmessageid>
					<sendingapplicationrecipientid>#xmlformat(arguments.sendingapplicationrecipientid)#</sendingapplicationrecipientid>
					<siteid>#xmlformat(arguments.siteid)#</siteid>
					<emailusername>#xmlformat(arguments.emailusername)#</emailusername>
					<emaildomain>#xmlformat(arguments.emaildomain)#</emaildomain>
					<emaildomainid>#xmlformat(arguments.emaildomainid)#</emaildomainid>
					<deliverystatusid>#xmlformat(arguments.deliverystatusid)#</deliverystatusid>
					<smtpstatuscode>#xmlformat(arguments.smtpstatuscode)#</smtpstatuscode>
					<attempt>#xmlformat(arguments.attempt)#</attempt>
					<ippoolid>#xmlformat(arguments.ippoolid)#</ippoolid>
					<sendgridsubuserid>#xmlformat(arguments.sendgridsubuserid)#</sendgridsubuserid>
					<sendgridsubuserdomainid>#xmlformat(arguments.sendgridsubuserdomainid)#</sendgridsubuserdomainid>
					<messagetemplate>#xmlformat(arguments.messagetemplate)#</messagetemplate>
					<messagesha1>#xmlformat(arguments.messagesha1)#</messagesha1>
					<messagetemplatematchfound>#xmlformat(arguments.messagetemplatematchfound)#</messagetemplatematchfound>
					<bounceClassificationID>#xmlformat(arguments.bounceClassificationID)#</bounceClassificationID>
				</t>
			</cfoutput>
		</cfsavecontent>
		<cftry>
			<cfquery name="local.qrySendMessage" datasource="#application.dsn.lyrisarchive.dsn#" result="local.qrySendMessageResult">
				EXEC emailTracking.dbo.sendgridStatusTracking_sendMessage @messageBody=<cfqueryparam cfsqltype="cf_sql_sqlxml" value="#local.xmlMessage#">;
			</cfquery>
		<cfcatch type="any">
			<cfset local.success = false>
			<cfset local.arguments = arguments>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local, customMessage="Error calling Lyris sendgridStatusTracking_sendMessage for a Sendgrid delivery event webhook entry")>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>
	<cffunction name="sendLyrisEngagementTrackingMessage" access="private" returntype="boolean" output="false">
		<cfargument name="applicationType" type="string" required="true">
		<cfargument name="applicationName" type="string" required="true">
		<cfargument name="sgMessageID" type="string" required="true">
		<cfargument name="sgEventID" type="string" required="true">
		<cfargument name="event" type="string" required="true">
		<cfargument name="timestamp" type="date" required="true">
		<cfargument name="membernumber" type="string" required="true">
		<cfargument name="sendingApplicationID" type="numeric" required="true">
		<cfargument name="sendingApplicationMessageID" type="numeric" required="true">
		<cfargument name="sendingApplicationRecipientID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="emailusername" type="string" required="true">
		<cfargument name="emaildomain" type="string" required="true">
		<cfargument name="emaildomainID" type="numeric" required="true">
		<cfargument name="useragent" type="string" required="true">
		<cfargument name="useragentID" type="numeric" required="true">
		<cfargument name="useragentsha1" type="string" required="true">
		<cfargument name="ipaddress" type="string" required="true">
		<cfargument name="isMachineOpen" type="boolean" required="true">
		<cfargument name="url" type="string" required="true">
				
		<cfset var local = structNew()>
		<cfset local.success = true>
		<cfset local.isMachineOpen=0>
		<cfif arguments.isMachineOpen>
			<cfset local.isMachineOpen = 1>
		</cfif>
		<cfsavecontent variable="local.xmlMessage">
			<cfoutput>
				<t>
					<messagetype>engagement</messagetype>
					<applicationtype>#xmlformat(arguments.applicationtype)#</applicationtype>
					<applicationname>#xmlformat(arguments.applicationname)#</applicationname>
					<periodcode>#dateTimeFormat(arguments.timestamp,'yyyymmddHH')#</periodcode>
					<event>#xmlformat(arguments.event)#</event>
					<sgmessageid>#xmlformat(arguments.sgmessageid)#</sgmessageid>
					<sgeventid>#xmlformat(arguments.sgeventid)#</sgeventid>
					<timestamp>#DateTimeFormat(arguments.timestamp,'m/d/yyyy h:nn:ss tt')#</timestamp>
					<membernumber>#xmlformat(arguments.membernumber)#</membernumber>
					<sendingapplicationid>#xmlformat(arguments.sendingapplicationid)#</sendingapplicationid>
					<sendingapplicationmessageid>#xmlformat(arguments.sendingapplicationmessageid)#</sendingapplicationmessageid>
					<sendingapplicationrecipientid>#xmlformat(arguments.sendingapplicationrecipientid)#</sendingapplicationrecipientid>
					<siteid>#xmlformat(arguments.siteid)#</siteid>
					<emailusername>#xmlformat(arguments.emailusername)#</emailusername>
					<emaildomain>#xmlformat(arguments.emaildomain)#</emaildomain>
					<emaildomainid>#xmlformat(arguments.emaildomainid)#</emaildomainid>
					<useragent>#xmlformat(arguments.useragent)#</useragent>
					<useragentid>#xmlformat(arguments.useragentid)#</useragentid>
					<useragentsha1>#xmlformat(arguments.useragentsha1)#</useragentsha1>
					<ipaddress>#xmlformat(arguments.ipaddress)#</ipaddress>
					<ismachineopen>#xmlformat(local.ismachineopen)#</ismachineopen>
					<url>#xmlformat(arguments.url)#</url>
					<urldomain>#xmlformat(rereplace(arguments.url,'^(?:https?:)(?://)?([^/?\r\n]+).*$','\1'))#</urldomain>
				</t>
			</cfoutput>
		</cfsavecontent>
		<cftry>
			<cfquery name="local.qrySendMessage" datasource="#application.dsn.lyrisarchive.dsn#" result="local.qrySendMessageResult">
				EXEC emailTracking.dbo.sendgridStatusTracking_sendMessage @messageBody=<cfqueryparam cfsqltype="cf_sql_sqlxml" value="#local.xmlMessage#">;
			</cfquery>
		<cfcatch type="any">
			<cfset local.success = false>
			<cfset local.arguments = arguments>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local, customMessage="Error calling Lyris sendgridStatusTracking_sendMessage for a Sendgrid engagement event webhook entry")>
		</cfcatch>
		</cftry>
		<cfreturn local.success>
	</cffunction>


	<cffunction name="processEventWebhookBatch" access="public" returntype="boolean" output="false">
		<cfargument name="eventArray" type="array" required="true">
		<cfargument name="numthreads" type="numeric" required="true">

		<cfscript>
			var local = structnew();
			local.maxthreads = 4;
			// ArrayFilter keeps the array elements for which the closure returns true.
			// Since we only want to keep track of failures, we negate the result of processEventWebhookItem
			// if local.failedItems has no elements, then all nodes passed.
			local.failedItems = ArrayFilter(
				arguments.eventArray, 
				function(eachEvent) {return (not processEventWebhookItem(eachEvent));},
				true,
				min(local.maxthreads,arguments.numthreads));
		</cfscript>

		<cfreturn ArrayIsEmpty(local.failedItems)>
	</cffunction>

	<cffunction name="processEventWebhookItem" access="private" returntype="boolean" output="false">
		<cfargument name="itemStruct" type="struct" required="true">

		<cfscript>
			if (structKeyExists(arguments.itemStruct, "v") and listLen(arguments.itemStruct.v, "-") eq 2) 
				return processEventWebhookItemV2(itemStruct=arguments.itemStruct);
			else 
				return processEventWebhookItemV1(itemStruct=arguments.itemStruct);
		</cfscript>
	</cffunction>


	<cffunction name="processEventWebhookItemV2" access="private" returntype="boolean" output="false">
		<cfargument name="itemStruct" type="struct" required="true">
		<cfscript>

			var local = {};
			try {
				local.success = true;
				if (structKeyExists(arguments.itemStruct, "v") and listLen(arguments.itemStruct.v, "-") eq 2 and listfindnocase(variables.webhookProcessingEnvironmentListFilter,listLast(arguments.itemStruct.v, "-"))) {


					local.sitecode="";
					local.sgSubuser="";
					local.siteInfo = {};
					local.ipPoolID = 0;
					local.deliveryStatusCode = "";
					local.deliveryStatusID = 0;
					local.isMachineOpen = 0;

					local.mailSystem = listFirst(arguments.itemStruct.mc_mailtype,"_");
					local.sg_event_id = structKeyExists(arguments.itemStruct, "sg_event_id") ? arguments.itemStruct.sg_event_id : "";
					local.emailaddress = structKeyExists(arguments.itemStruct, "email") ? arguments.itemStruct.email : "";
					local.emailUsername = listFirst(local.emailaddress,"@");
					local.emailDomain = listLast(local.emailaddress,"@");
					local.emailDomainID = lyrisEmailTracking_getDomainID(domain=local.emailDomain);

					local.ipaddress = structKeyExists(arguments.itemStruct, "ip") ? arguments.itemStruct.ip : "";
					local.url = structKeyExists(arguments.itemStruct, "url") ? arguments.itemStruct.url : "";
					local.isMachineOpen = structKeyExists(arguments.itemStruct, "sg_machine_open") ? arguments.itemStruct.sg_machine_open : false;
					if ((arguments?.itemStruct?.sg_machine_open ?: false))
						local.isMachineOpen = 1;

					if (structKeyExists(arguments.itemStruct, "useragent") and len(arguments.itemStruct.useragent) )
						local.useragentInfo = lyrisEmailTracking_getUserAgentInfo(useragent=arguments.itemStruct.useragent);
					else 
						local.useragentInfo = { useragent = "",useragentSHA1 = "",userAgentID = 0}
					
					local.sendingMessageID = structKeyExists(arguments.itemStruct, "sa_msgid") ? val(arguments.itemStruct.sa_msgid) : 0;
					local.sg_message_id = structKeyExists(arguments.itemStruct, "sg_message_id") ? arguments.itemStruct.sg_message_id : "";
					local.saName = arguments.itemStruct.sa_name;
					local.sendingHostname = structKeyExists(arguments.itemStruct, "mc_sud") ? arguments.itemStruct.mc_sud : "";
					local.attempt = structKeyExists(arguments.itemStruct, "attempt") ? arguments.itemStruct.attempt : 0;
					local.messageTemplate = {SHA1 = "",template="", matchFound=1};

					if (structKeyExists(arguments.itemStruct, "mc_site") and listLen(arguments.itemStruct.mc_site,"-",true) eq 2) {
						local.sitecode=listFirst(arguments.itemStruct.mc_site,"-",true);
						local.sgSubuser=listLast(arguments.itemStruct.mc_site,"-",true);
						local.siteInfo = application.objSiteInfo.getSiteInfo(sitecode=local.siteCode);
						if (structCount(local.siteInfo))
							local.siteID = local.siteInfo.siteID;
						else 
							local.siteID = application.objSiteInfo.getSiteIDFromSiteCode(sitecode=local.siteCode);
					}
					if (structKeyExists(arguments.itemStruct, "sendgrid_pool") and structKeyExists(variables.sendgridIPPools,arguments.itemStruct.sendgrid_pool)) {
						local.ipPoolID = variables.sendgridIPPools[arguments.itemStruct.sendgrid_pool].ipPoolID;
					}

					local.reason = structKeyExists(arguments.itemStruct, "reason") ? arguments.itemStruct.reason : "";
					local.response = structKeyExists(arguments.itemStruct, "response") ? arguments.itemStruct.response : "";
					local.status = structKeyExists(arguments.itemStruct, "status") ? arguments.itemStruct.status : "";
					local.epochDate = arguments.itemStruct.timestamp;
					// Calculate adjustments for Central timezone and daylightsavingtime
					local.Offset = ((GetTimeZoneInfo().utcHourOffset)+1)*-3600;
					// Date is returned as number of seconds since 1-1-1970
					local.responseDateInCT = DateAdd('s', local.epochDate+local.Offset, CreateDateTime(1970, 1, 1, 0, 0, 0));
					local.sendgridSubuserInfo = getSendgridSubUsersAndDomains(subuserUsername=local.sgSubuser ,sendingdomain=local.sendingHostname);

					switch(local.mailSystem) {
						case "lyris":
							if ((randrange(1,100) <= application.lyrisWebhookProcessingRatio) && structKeyExists(arguments.itemStruct, "sa_name")) {

								local.membernumber="";
								local.lyris_memberid=0;
								if (structKeyExists(arguments.itemStruct, "sa_rcp") and listLen(arguments.itemStruct.sa_rcp,"|",true) eq 2) {
									local.membernumber=listFirst(arguments.itemStruct.sa_rcp,"|",true);
									local.lyris_memberid=val(listLast(arguments.itemStruct.sa_rcp,"|",true));
								}
								local.sendingApplicationID = lyrisEmailTracking_getSendingApplicationID(applicationType=arguments.itemStruct.mc_mailtype, applicationName=local.saName);

								switch(arguments.itemStruct.event) {
									case "processed": case "delivered":
										local.deliveryStatusCode = arguments.itemStruct.event;
										local.deliveryStatusID = variables.deliveryStatuses[local.deliveryStatusCode].deliveryStatusID;
										break;
									case "deferred": 
										local.deliveryStatusCode = arguments.itemStruct.event;
										local.deliveryStatusID = variables.deliveryStatuses[local.deliveryStatusCode].deliveryStatusID;
										if (len(local.response))
											local.messageTemplate = generateMessageTemplate(emailUsername=local.emailUsername,emailDomain=local.emailDomain,message=local.response);
										break;
									case "dropped":
										local.deliveryStatusCode = arguments.itemStruct.event;
										local.deliveryStatusID = variables.deliveryStatuses[local.deliveryStatusCode].deliveryStatusID;
										if (len(local.reason))
											local.messageTemplate = generateMessageTemplate(emailUsername=local.emailUsername,emailDomain=local.emailDomain,message=local.reason);
										break;
									case "bounce":
										local.deliveryStatusCode = replace("#arguments.itemStruct.type#-#arguments.itemStruct.bounce_classification#"," ","all");
										if (structKeyExists(variables.deliveryStatuses,local.deliveryStatusCode))
											local.deliveryStatusID = variables.deliveryStatuses[local.deliveryStatusCode].deliveryStatusID;
										else {
											local.deliveryStatusID = variables.deliveryStatuses["#arguments.itemStruct.type#-unclassified"].deliveryStatusID;
										}
										if (len(local.reason))
											local.messageTemplate = generateMessageTemplate(emailUsername=local.emailUsername,emailDomain=local.emailDomain,message=local.reason);
										break;
								}

								switch(arguments.itemStruct.event) {
									case "processed": case "delivered": case "deferred": case "dropped": case "bounce":
										local.success = sendLyrisDeliveryTrackingMessage(
											applicationType=arguments.itemStruct.mc_mailtype, 
											applicationName=local.saName,
											sgEventID = local.sg_event_id,
											sgMessageID = local.sg_message_id,
											event = arguments.itemStruct.event,
											timestamp = local.responseDateInCT,
											membernumber = local.membernumber,
											sendingApplicationID = val(local.sendingApplicationID),
											sendingApplicationMessageID = val(local.sendingMessageID),
											sendingapplicationrecipientid = val(local.lyris_memberid),
											siteID = val(local.siteID),
											emailusername = local.emailUsername,
											emaildomain = local.emailDomain,
											emailDomainID = val(local.emailDomainID),
											deliveryStatusID = val(local.deliveryStatusID),
											smtpstatuscode = local.status,
											attempt = local.attempt,
											ipPoolID = val(local.ipPoolID),
											sendgridSubuserID = val(local.sendgridSubuserInfo.subuserID),
											sendgridSubuserDomainID = val(local.sendgridSubuserInfo.subuserDomainID),
											messageTemplate = local.messageTemplate.template,
											messageSHA1 = local.messageTemplate.sha1,
											messageTemplateMatchFound = local.messageTemplate.matchFound,
											bounceClassificationID=0
										);
										break;
									case "open": case "click":  case "spamreport": 
										local.success = sendLyrisEngagementTrackingMessage(
											applicationType=arguments.itemStruct.mc_mailtype, 
											applicationName=local.saName,
											sgEventID = local.sg_event_id,
											sgMessageID = local.sg_message_id,
											event = arguments.itemStruct.event,
											timestamp = local.responseDateInCT,
											membernumber = local.membernumber,
											sendingApplicationID = val(local.sendingApplicationID),
											sendingApplicationMessageID = val(local.sendingMessageID),
											sendingapplicationrecipientid = val(local.lyris_memberid),
											siteID = val(local.siteID),
											emailusername = local.emailUsername,
											emaildomain = local.emailDomain,
											emailDomainID = val(local.emailDomainID),
											useragent = local.useragentInfo.useragent,
											useragentID = val(local.useragentInfo.useragentID),
											useragentSHA1 = local.useragentInfo.useragentSHA1,
											ipaddress = local.ipaddress,
											isMachineOpen = local.isMachineOpen,
											url = local.url
										);
										break;
								}
							}							
						break;
						case "emailsendingqueue":
							switch(arguments.itemStruct.event) {
								case "processed":
									local.success = recordSendGridStatusTracking(siteID=local.siteID, messageID=local.sendingMessageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_process', sgSubuser=local.sgSubuser, updateDate=true, sgEventID="", ipaddress="", detail="", reason="", status="", responseDate=local.responseDateInCT, jsonResult="");
									break;
								case "dropped":
									local.success = recordSendGridStatusTracking(siteID=local.siteID, messageID=local.sendingMessageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_drop', sgSubuser=local.sgSubuser, updateDate=true, sgEventID=local.sg_event_id, ipaddress="", detail=local.reason, reason=local.reason, status=local.status, responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct));
									break;
								case "delivered":
									local.success = recordSendGridStatusTracking(siteID=local.siteID, messageID=local.sendingMessageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_deliver', sgSubuser=local.sgSubuser, updateDate=true, sgEventID="", ipaddress="", detail="", reason="", status=local.status, responseDate=local.responseDateInCT, jsonResult="");
									break;
								case "deferred":
									local.success = recordSendGridStatusTracking(siteID=local.siteID, messageID=local.sendingMessageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_defer', sgSubuser=local.sgSubuser, updateDate=true, sgEventID="", ipaddress="", detail=local.response, reason="", status="", responseDate=local.responseDateInCT, jsonResult="");
									break;
								case "bounce":
									local.type = "";
									if (isDefined("arguments.itemStruct.type")) local.type = arguments.itemStruct.type;
									if (local.type EQ 'blocked')
										local.success = recordSendGridStatusTracking(siteID=local.siteID, messageID=local.sendingMessageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_block', sgSubuser=local.sgSubuser, updateDate=true, sgEventID=local.sg_event_id, ipaddress="", detail=local.reason, reason=local.reason, status=local.status, responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct));
									else 
										local.success = recordSendGridStatusTracking(siteID=local.siteID, messageID=local.sendingMessageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_bounce', sgSubuser=local.sgSubuser, updateDate=true, sgEventID=local.sg_event_id, ipaddress="", detail="Type: #local.type# Reason: #local.reason#", reason=local.reason, status=local.status, responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct));
									break;
								case "open":
									local.ip = "";
									if (isDefined("arguments.itemStruct.ip")) local.ip = arguments.itemStruct.ip;
									local.success = recordSendGridStatusTracking(siteID=local.siteID, messageID=local.sendingMessageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_open', sgSubuser=local.sgSubuser, updateDate=true, sgEventID=local.sg_event_id, ipaddress=local.ip, detail="", reason="", status="", responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct));
									break;
								case "spamreport":
									local.success = recordSendGridStatusTracking(siteID=local.siteID, messageID=local.sendingMessageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_spam', sgSubuser=local.sgSubuser, updateDate=true, sgEventID=local.sg_event_id, ipaddress="", detail="", reason="", status="", responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct));
									break;
								case "click":
									local.ip = "";
									if (isDefined("arguments.itemStruct.ip")) local.ip = arguments.itemStruct.ip;
									local.clickurl = "";
									if (isDefined("arguments.itemStruct.url")) local.clickurl = arguments.itemStruct.url;
									local.success = recordSendGridStatusTracking(siteID=local.siteID, messageID=local.sendingMessageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_click', sgSubuser=local.sgSubuser, updateDate=false, sgEventID=local.sg_event_id, ipaddress=local.ip, detail=local.clickurl, reason="", status="", responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct));
									break;
							}
							break;
					}
				}
			} catch (e) {
				application.objError.sendError(cfcatch=e, objectToDump={arguments=arguments, local=local}, customMessage="Error processing a Sendgrid event webhook entry v2");
				local.success = false;
			}
			return local.success;
		</cfscript>
	</cffunction>

	<cffunction name="processEventWebhookItemV1" access="private" returntype="boolean" output="false">
		<cfargument name="itemStruct" type="struct" required="true">

		<cfscript>
			var local = structnew();
			local.success = true;
			try {
				if (structKeyExists(arguments.itemStruct, "event") and structKeyExists(arguments.itemStruct, "mc_environment")) {

					//make sure environment matches. We don't want dev/beta messages to pollute production db.
					if (listfindnocase(variables.webhookProcessingEnvironmentListFilter,arguments.itemStruct.mc_environment) and structKeyExists(arguments.itemStruct, "mc_mailtype")) {
						switch(arguments.itemStruct.mc_mailtype) {
							case "emailsendingqueue":
								if (structKeyExists(arguments.itemStruct, "mc_recipientid")) {
									local.siteID=0;
									local.sitecode=structKeyExists(arguments.itemStruct, "mc_sitecode") ? arguments.itemStruct.mc_sitecode : "";
									local.messageID = structKeyExists(arguments.itemStruct, "mc_messageid") ? arguments.itemStruct.mc_messageid : 0;
									local.sg_event_id = structKeyExists(arguments.itemStruct, "sg_event_id") ? arguments.itemStruct.sg_event_id : "";
									local.sgSubuser = structKeyExists(arguments.itemStruct, "sendgrid_subuser") ? arguments.itemStruct.sendgrid_subuser : "";
									local.reason = structKeyExists(arguments.itemStruct, "reason") ? arguments.itemStruct.reason : "";
									local.response = structKeyExists(arguments.itemStruct, "response") ? arguments.itemStruct.response : "";
									local.status = structKeyExists(arguments.itemStruct, "status") ? arguments.itemStruct.status : "";
									local.epochDate = arguments.itemStruct.timestamp;
									// Calculate adjustments for Central timezone and daylightsavingtime
									local.Offset = ((GetTimeZoneInfo().utcHourOffset)+1)*-3600;
									// Date is returned as number of seconds since 1-1-1970
									local.responseDateInCT = DateAdd('s', local.epochDate+local.Offset, CreateDateTime(1970, 1, 1, 0, 0, 0));

									if (len(local.sitecode)) {
										local.siteInfo = application.objSiteInfo.getSiteInfo(sitecode=trim(local.siteCode));
										if (structCount(local.siteInfo))
											local.siteID = local.siteInfo.siteID;
										else 
											local.siteID = application.objSiteInfo.getSiteIDFromSiteCode(sitecode=local.siteCode);
									}

									switch(arguments.itemStruct.event) {
										case "processed":
											local.success = recordSendGridStatusTracking(siteID=local.siteID, messageID=local.messageID, recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_process', sgSubuser=local.sgSubuser, updateDate=true, sgEventID="", ipaddress="", detail="", reason="", status="", responseDate=local.responseDateInCT, jsonResult="");
											break;
										case "dropped":
											local.success = recordSendGridStatusTracking(siteID=local.siteID, messageID=local.messageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_drop', sgSubuser=local.sgSubuser, updateDate=true, sgEventID=local.sg_event_id, ipaddress="", detail=local.reason, reason=local.reason, status=local.status, responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct));
											break;
										case "delivered":
											local.success = recordSendGridStatusTracking(siteID=local.siteID, messageID=local.messageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_deliver', sgSubuser=local.sgSubuser, updateDate=true, sgEventID="", ipaddress="", detail="", reason="", status=local.status, responseDate=local.responseDateInCT, jsonResult="");
											break;
										case "deferred":
											local.success = recordSendGridStatusTracking(siteID=local.siteID, messageID=local.messageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_defer', sgSubuser=local.sgSubuser, updateDate=true, sgEventID="", ipaddress="", detail=local.response, reason="", status="", responseDate=local.responseDateInCT, jsonResult="");
											break;
										case "bounce":
											local.type = "";
											if (isDefined("arguments.itemStruct.type")) local.type = arguments.itemStruct.type;
											if (local.type EQ 'blocked')
												local.success = recordSendGridStatusTracking(siteID=local.siteID, messageID=local.messageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_block', sgSubuser=local.sgSubuser, updateDate=true, sgEventID=local.sg_event_id, ipaddress="", detail=local.reason, reason=local.reason, status=local.status, responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct));
											else 
												local.success = recordSendGridStatusTracking(siteID=local.siteID, messageID=local.messageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_bounce', sgSubuser=local.sgSubuser, updateDate=true, sgEventID=local.sg_event_id, ipaddress="", detail="Type: #local.type# Reason: #local.reason#", reason=local.reason, status=local.status, responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct));
 											break;
										case "open":
											local.ip = "";
											if (isDefined("arguments.itemStruct.ip")) local.ip = arguments.itemStruct.ip;
											local.success = recordSendGridStatusTracking(siteID=local.siteID, messageID=local.messageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_open', sgSubuser=local.sgSubuser, updateDate=true, sgEventID=local.sg_event_id, ipaddress=local.ip, detail="", reason="", status="", responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct));
											break;
										case "spamreport":
											local.success = recordSendGridStatusTracking(siteID=local.siteID, messageID=local.messageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_spam', sgSubuser=local.sgSubuser, updateDate=true, sgEventID=local.sg_event_id, ipaddress="", detail="", reason="", status="", responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct));
											break;
										case "click":
											local.ip = "";
											if (isDefined("arguments.itemStruct.ip")) local.ip = arguments.itemStruct.ip;
											local.clickurl = "";
											if (isDefined("arguments.itemStruct.url")) local.clickurl = arguments.itemStruct.url;
											local.success = recordSendGridStatusTracking(siteID=local.siteID, messageID=local.messageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_click', sgSubuser=local.sgSubuser, updateDate=false, sgEventID=local.sg_event_id, ipaddress=local.ip, detail=local.clickurl, reason="", status="", responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct));
											break;
									}
								}
								break;
						}
					}
				}
			} catch (any e) {
				local.success = false;
				application.objError.sendError(cfcatch=e, objectToDump=arguments.itemStruct, customMessage="Error processing a Sendgrid event webhook entry");
			}
		</cfscript>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="processHalonEventWebhookBatch" access="public" returntype="boolean" output="false">
		<cfargument name="eventArray" type="array" required="true">
		<cfargument name="numthreads" type="numeric" required="true">

		<cfscript>
			var local = structnew();
			local.maxthreads = 4;
			// ArrayFilter keeps the array elements for which the closure returns true.
			// Since we only want to keep track of failures, we negate the result of processEventWebhookItem
			// if local.failedItems has no elements, then all nodes passed.
			local.failedItems = ArrayFilter(
				arguments.eventArray, 
				function(eachEvent) {return (not processHalonEventWebhookItem(eachEvent));},
				true,
				min(local.maxthreads,arguments.numthreads));
		</cfscript>

		<cfreturn ArrayIsEmpty(local.failedItems)>
	</cffunction>

	<cffunction name="processHalonEventWebhookItem" access="private" returntype="boolean" output="false">
		<cfargument name="itemStruct" type="struct" required="true">

		<cfscript>
			if ((structKeyExists(arguments.itemStruct, "additional") and structKeyExists(arguments.itemStruct.additional, "v") and listLen(arguments.itemStruct.additional.v, "-") eq 2)) {
				arguments.itemStruct.category = arguments.itemStruct.additional.category;
				arguments.itemStruct.ippool = arguments.itemStruct.additional.ippool;
				arguments.itemStruct.jobid = arguments.itemStruct.additional.jobid;
				arguments.itemStruct.logid = arguments.itemStruct.additional.logid;
				arguments.itemStruct.mc_mailtype = arguments.itemStruct.additional.mc_mailtype;
				arguments.itemStruct.mc_site = arguments.itemStruct.additional.mc_site;
				arguments.itemStruct.mc_sud = arguments.itemStruct.additional.mc_sud;
				arguments.itemStruct.sa_msgid = arguments.itemStruct.additional.sa_msgid;
				arguments.itemStruct.sa_name = arguments.itemStruct.additional.sa_name;
				arguments.itemStruct.sa_rcp = arguments.itemStruct.additional.sa_rcp;
				arguments.itemStruct.sendgrid_pool = arguments.itemStruct.additional.sendgrid_pool;
				arguments.itemStruct.v = arguments.itemStruct.additional.v;
			}
			if ((structKeyExists(arguments.itemStruct, "v") and listLen(arguments.itemStruct.v, "-") eq 2))
				return processHalonEventWebhookItemV2(itemStruct=arguments.itemStruct);
			else 
				return processHalonEventWebhookItemV1(itemStruct=arguments.itemStruct);
		</cfscript>
	</cffunction>

	<cffunction name="processHalonEventWebhookItemV1" access="private" returntype="boolean" output="false">
		<cfargument name="itemStruct" type="struct" required="true">

		<cfscript>
			var local = structnew();
			local.success = true;
			try {
				if ((structKeyExists(arguments.itemStruct, "event") or structKeyExists(arguments.itemStruct, "type")) and structKeyExists(arguments.itemStruct, "mc_environment")) {

					//make sure environment matches. We don't want dev/beta messages to pollute production db.
					if (listfindnocase(variables.webhookProcessingEnvironmentListFilter,arguments.itemStruct.mc_environment) and structKeyExists(arguments.itemStruct, "mc_mailtype")) {
						switch(arguments.itemStruct.mc_mailtype) {
							case "emailsendingqueue":
								if (structKeyExists(arguments.itemStruct, "mc_recipientid")) {
									local.siteID=0;
									local.sitecode=structKeyExists(arguments.itemStruct, "mc_sitecode") ? arguments.itemStruct.mc_sitecode : "";
									local.messageID = structKeyExists(arguments.itemStruct, "mc_messageid") ? arguments.itemStruct.mc_messageid : 0;
									local.logid = structKeyExists(arguments.itemStruct, "logid") ? arguments.itemStruct.logid : "";									
									local.sg_event_id = structKeyExists(arguments.itemStruct, "event_id") ? arguments.itemStruct.event_id : "";
									local.sgSubuser = structKeyExists(arguments.itemStruct, "sendgrid_subuser") ? arguments.itemStruct.sendgrid_subuser : "";
									local.reason = structKeyExists(arguments.itemStruct, "reason") ? arguments.itemStruct.reason : "";
									local.response = structKeyExists(arguments.itemStruct, "response") ? arguments.itemStruct.response : "";
									local.status = structKeyExists(arguments.itemStruct, "status") ? arguments.itemStruct.status : "";
									local.recipientID = arguments.itemStruct.mc_recipientid									
									local.timestamp = 0;
									if (structKeyExists(arguments.itemStruct, "type")) {
										local.timestamp = arguments.itemStruct.timestamp;
									} else {										
										local.timestamp = arguments.itemStruct.time;
									}
									local.epochDate = local.timestamp / 1000;
									
									// Calculate adjustments for Central timezone and daylightsavingtime
									local.Offset = ((GetTimeZoneInfo().utcHourOffset)+1)*-3600;
									// Date is returned as number of seconds since 1-1-1970
									local.responseDateInCT = DateAdd('s', local.epochDate+local.Offset, CreateDateTime(1970, 1, 1, 0, 0, 0));

									if (len(local.sitecode)) {
										local.siteInfo = application.objSiteInfo.getSiteInfo(sitecode=local.siteCode);
										if (structCount(local.siteInfo))
											local.siteID = local.siteInfo.siteID;
										else 
											local.siteID = application.objSiteInfo.getSiteIDFromSiteCode(sitecode=local.siteCode);
									}

									if (structKeyExists(arguments.itemStruct, "type")) {
										switch(arguments.itemStruct.type) {
											case "open":
												local.ip = "";
												if (isDefined("arguments.itemStruct.ip")) local.ip = arguments.itemStruct.ip;
												local.success = recordHalonStatusTracking(siteID=local.siteID, messageID=local.messageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_open', sgSubuser=local.sgSubuser, updateDate=true, sgEventID=local.sg_event_id, ipaddress=local.ip, detail="", reason="", status="", responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct), logid=local.logid, class="");
												break;
											case "click":
												local.ip = "";
												if (isDefined("arguments.itemStruct.ip")) local.ip = arguments.itemStruct.ip;
												local.clickurl = "";
												if (isDefined("arguments.itemStruct.url")) local.clickurl = arguments.itemStruct.url;
												local.success = recordHalonStatusTracking(siteID=local.siteID, messageID=local.messageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_click', sgSubuser=local.sgSubuser, updateDate=false, sgEventID=local.sg_event_id, ipaddress=local.ip, detail=local.clickurl, reason="", status="", responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct), logid=local.logid, class="");
												break;
										}
									}
									else {
										switch(arguments.itemStruct.event) {
											case "processed":
												local.success = recordHalonStatusTracking(siteID=local.siteID, messageID=local.messageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_process', sgSubuser=local.sgSubuser, updateDate=true, sgEventID="", ipaddress="", detail="", reason="", status="", responseDate=local.responseDateInCT, jsonResult="", logid=local.logid, class="");
												break;
											case "dropped":
												local.success = recordHalonStatusTracking(siteID=local.siteID, messageID=local.messageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_drop', sgSubuser=local.sgSubuser, updateDate=true, sgEventID=local.sg_event_id, ipaddress="", detail=local.reason, reason=local.reason, status=local.status, responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct), logid=local.logid, class="");
												break;
											case "delivered":
												local.success = recordHalonStatusTracking(siteID=local.siteID, messageID=local.messageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_deliver', sgSubuser=local.sgSubuser, updateDate=true, sgEventID="", ipaddress="", detail="", reason="", status=local.status, responseDate=local.responseDateInCT, jsonResult="", logid=local.logid, class="");
												break;
											case "deferred":
												local.success = recordHalonStatusTracking(siteID=local.siteID, messageID=local.messageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_defer', sgSubuser=local.sgSubuser, updateDate=true, sgEventID="", ipaddress="", detail=local.response, reason="", status="", responseDate=local.responseDateInCT, jsonResult="", logid=local.logid, class="");
												break;
											case "bounce":
												local.type = "";
												if (isDefined("arguments.itemStruct.type")) local.type = arguments.itemStruct.type;
												if (local.type EQ 'blocked')
													local.success = recordHalonStatusTracking(siteID=local.siteID, messageID=local.messageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_block', sgSubuser=local.sgSubuser, updateDate=true, sgEventID=local.sg_event_id, ipaddress="", detail=local.reason, reason=local.reason, status=local.status, responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct), logid=local.logid, class="");
												else 
													local.success = recordHalonStatusTracking(siteID=local.siteID, messageID=local.messageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_bounce', sgSubuser=local.sgSubuser, updateDate=true, sgEventID=local.sg_event_id, ipaddress="", detail="Type: #local.type# Reason: #local.reason#", reason=local.reason, status=local.status, responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct), logid=local.logid, class="");
												 break;

											case "failed":
												local.sg_event_id = (arguments.itemStruct?.dsn?.id?.transaction ?: "");
												local.reason = arguments?.itemStruct?.reason ?: "";
												local.response = arguments?.itemStruct?.dsn?.diagnosticcode ?: "";
												local.status = arguments.itemStruct?.attempt?.result?.code ?: "";
												local.remoteip = arguments.itemStruct?.attempt?.connection?.remoteip ?: "";
												switch(arguments.itemStruct.class) {
													case "spam":
													case "badrecipient":
													case "transientfail":
													case "dnsFail":
													case "serverpolicy":
													case "relayfail":
													case "generalfail":
													case "badsender":
													case "overquota":
													case "timeout":
													case "content":
													case "serverfail":
													case "verifyfail":
													case "other":
														if (arguments.itemStruct.action eq "QUEUE") {
															local.success = recordHalonStatusTracking(siteID=local.siteID, messageID=local.messageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_defer', sgSubuser=local.sgSubuser, updateDate=true, sgEventID=local.sg_event_id, ipaddress=local.remoteip, detail=local.response, reason=local.response, status=local.status, responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct), logid=local.logid, class=arguments.itemStruct.class);
														}
														else if (arguments.itemStruct.action eq "BOUNCE") {
															local.success = recordHalonStatusTracking(siteID=local.siteID, messageID=local.messageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_bounce', sgSubuser=local.sgSubuser, updateDate=true, sgEventID=local.sg_event_id, ipaddress=local.remoteip, detail=local.response, reason=local.response, status=local.status, responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct), logid=local.logid, class=arguments.itemStruct.class);
														}
														else {
															systemOutput(obj="#dateTimeFormat(now(),"yyyy-mm-dd HH:nn:ss")# [emailQueueManager.cfc] [processHalonEventWebhookItemV1] unknown failed - Halon event type webhook entry: #serializeJSON(arguments.itemStruct)#", addNewLine=true, doErrorStream=false);
														}														
													break;
													case "block":
														local.success = recordHalonStatusTracking(siteID=local.siteID, messageID=local.messageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_block', sgSubuser=local.sgSubuser, updateDate=true, sgEventID=local.sg_event_id, ipaddress="", detail=local.reason, reason=local.reason, status=local.status, responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct), logid=local.logid, class="");
													break;
													default:
														if (arguments.itemStruct.action eq "QUEUE") {
															local.success = recordHalonStatusTracking(siteID=local.siteID, messageID=local.messageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_defer', sgSubuser=local.sgSubuser, updateDate=true, sgEventID=local.sg_event_id, ipaddress=local.remoteip, detail=local.response, reason=local.response, status=local.status, responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct), logid=local.logid, class=arguments.itemStruct.class);
														}
														else if (arguments.itemStruct.action eq "BOUNCE") {
															local.success = recordHalonStatusTracking(siteID=local.siteID, messageID=local.messageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_bounce', sgSubuser=local.sgSubuser, updateDate=true, sgEventID=local.sg_event_id, ipaddress=local.remoteip, detail=local.response, reason=local.response, status=local.status, responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct), logid=local.logid, class=arguments.itemStruct.class);
														}
														else {
															systemOutput(obj="#dateTimeFormat(now(),"yyyy-mm-dd HH:nn:ss")# [emailQueueManager.cfc] [processHalonEventWebhookItemV1] unknown failed - Halon event type webhook entry: #serializeJSON(arguments.itemStruct)#", addNewLine=true, doErrorStream=false);
														}														
														systemOutput(obj="#dateTimeFormat(now(),"yyyy-mm-dd HH:nn:ss")# [emailQueueManager.cfc] [processHalonEventWebhookItemV1] unknown - Halon event type webhook entry: #serializeJSON(arguments.itemStruct)#", addNewLine=true, doErrorStream=false);
		
												}											
												break;
											case "spamreport":
												local.success = recordHalonStatusTracking(siteID=local.siteID, messageID=local.messageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_spam', sgSubuser=local.sgSubuser, updateDate=true, sgEventID=local.sg_event_id, ipaddress="", detail="", reason="", status="", responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct), logid=local.logid, class="");
												break;
											case "async":
												systemOutput(obj="#dateTimeFormat(now(),"yyyy-mm-dd HH:nn:ss")# [emailQueueManager.cfc] [processHalonEventWebhookItemV1] async - Halon event type webhook entry: #serializeJSON(arguments.itemStruct)#", addNewLine=true, doErrorStream=false);
												break;
											case "arf":
												systemOutput(obj="#dateTimeFormat(now(),"yyyy-mm-dd HH:nn:ss")# [emailQueueManager.cfc] [processHalonEventWebhookItemV1] arf - Halon event type webhook entry: #serializeJSON(arguments.itemStruct)#", addNewLine=true, doErrorStream=false);
												break;												
											default:
												systemOutput(obj="#dateTimeFormat(now(),"yyyy-mm-dd HH:nn:ss")# [emailQueueManager.cfc] [processHalonEventWebhookItemV1] unknown - Halon event type webhook entry: #serializeJSON(arguments.itemStruct)#", addNewLine=true, doErrorStream=false);
												break;												
										}
									}
							
								}

								break;
						}
					}
				}
				else {
					// This is because recipientid or mc_environment is not known
					if (structKeyExists(arguments.itemStruct, "event"))	{
						switch(arguments.itemStruct.event) {
							case "async":
								systemOutput(obj="#dateTimeFormat(now(),"yyyy-mm-dd HH:nn:ss")# [emailQueueManager.cfc] [processHalonEventWebhookItemV1] async - Halon event type webhook entry: #serializeJSON(arguments.itemStruct)#", addNewLine=true, doErrorStream=false);
								break;
							case "arf":
								systemOutput(obj="#dateTimeFormat(now(),"yyyy-mm-dd HH:nn:ss")# [emailQueueManager.cfc] [processHalonEventWebhookItemV1] arf - Halon event type webhook entry: #serializeJSON(arguments.itemStruct)#", addNewLine=true, doErrorStream=false);
								break;												
							default:
								systemOutput(obj="#dateTimeFormat(now(),"yyyy-mm-dd HH:nn:ss")# [emailQueueManager.cfc] [processHalonEventWebhookItemV1] unknown - Halon event type webhook entry: #serializeJSON(arguments.itemStruct)#", addNewLine=true, doErrorStream=false);
								break;												
						}
					}
					else {
						systemOutput(obj="#dateTimeFormat(now(),"yyyy-mm-dd HH:nn:ss")# [emailQueueManager.cfc] [processHalonEventWebhookItemV1] Not handled - Halon event type webhook entry: #serializeJSON(arguments.itemStruct)#", addNewLine=true, doErrorStream=false);
					}		
				}
			} catch (any e) {
				local.success = false;
				application.objError.sendError(cfcatch=e, objectToDump=arguments.itemStruct, customMessage="Error processing a Halon event webhook entry");
			}

			return local.success;			
		</cfscript>

	</cffunction>

	<cffunction name="processHalonEventWebhookItemV2" access="private" returntype="boolean" output="false">
		<cfargument name="itemStruct" type="struct" required="true">
		<cfscript>

			var local = {};
			try {
				local.success = true;
				if (structKeyExists(arguments.itemStruct, "v") and listLen(arguments.itemStruct.v, "-") eq 2 and listfindnocase(variables.webhookProcessingEnvironmentListFilter,listLast(arguments.itemStruct.v, "-"))) {
					local.sitecode="";
					local.sgSubuser = structKeyExists(arguments.itemStruct, "sendgrid_subuser") ? arguments.itemStruct.sendgrid_subuser : "";
					local.siteInfo = {};
					local.ipPoolID = 0;
					local.deliveryStatusCode = "";
					local.class = "";
					local.bounceClassificationID = 0;
					local.deliveryStatusID = 0;
					local.isMachineOpen = 0;

					local.mailSystem = listFirst(arguments.itemStruct.mc_mailtype,"_");
					if(arguments.itemStruct.keyExists('type') and arrayFindnoCase(['open','click'],arguments.itemStruct.type)) {
						local.sg_event_id = (arguments.itemStruct?.event_id ?: "");
						local.emailaddress = (arguments.itemStruct?.recipient ?: "");
					} else if (arguments.itemStruct.keyExists('event') and NOT isStruct(arguments.itemStruct.event) and arguments.itemStruct.event eq "processed" ) {
						local.sg_event_id = (arguments.itemStruct?.id?.transaction ?: "");
						local.emailaddress = arguments.itemStruct?.recipient?.localpart & "@" & arguments.itemStruct?.recipient?.domain;
					} else {
						local.sg_event_id = (arguments.itemStruct?.dsn?.id?.transaction ?: "");
						local.emailaddress = (arguments.itemStruct?.message?.recipient ?: "");
					}

					local.emailUsername = listFirst(local.emailaddress,"@");
					local.emailDomain = listLast(local.emailaddress,"@");
					local.emailDomainID = lyrisEmailTracking_getDomainID(domain=local.emailDomain);
					local.ipaddress = structKeyExists(arguments.itemStruct, "ip") ? arguments.itemStruct.ip : "";
					local.url = structKeyExists(arguments.itemStruct, "url") ? arguments.itemStruct.url : "";
					local.isMachineOpen = structKeyExists(arguments.itemStruct, "sg_machine_open") ? arguments.itemStruct.sg_machine_open : false;

					if ((arguments?.itemStruct?.sg_machine_open ?: false))
						local.isMachineOpen = 1;

					if (structKeyExists(arguments.itemStruct, "user_agent") and len(arguments.itemStruct.user_agent) )
						local.useragentInfo = lyrisEmailTracking_getUserAgentInfo(useragent=arguments.itemStruct.user_agent);
					else 
						local.useragentInfo = { useragent = "",useragentSHA1 = "",userAgentID = 0}
					
					local.sendingMessageID = structKeyExists(arguments.itemStruct, "sa_msgid") ? val(arguments.itemStruct.sa_msgid) : 0;
					local.sg_message_id = structKeyExists(arguments.itemStruct, "logid") ? arguments.itemStruct.logid : "";
					local.saName = arguments.itemStruct.sa_name;
					local.sendingHostname = structKeyExists(arguments.itemStruct, "mc_sud") ? arguments.itemStruct.mc_sud : "";
					local.attempt = structKeyExists(arguments.itemStruct, "retry") ? arguments.itemStruct.retry : 0;
					local.messageTemplate = {SHA1 = "",template="", matchFound=1};

					if (structKeyExists(arguments.itemStruct, "mc_site") and listLen(arguments.itemStruct.mc_site,"-",true) eq 2) {
						local.sitecode=listFirst(arguments.itemStruct.mc_site,"-",true);
						local.sgSubuser=listLast(arguments.itemStruct.mc_site,"-",true);
						local.siteInfo = application.objSiteInfo.getSiteInfo(sitecode=local.siteCode);
						if (structCount(local.siteInfo))
							local.siteID = local.siteInfo.siteID;
						else 
							local.siteID = application.objSiteInfo.getSiteIDFromSiteCode(sitecode=local.siteCode);
					}
					else {
						local.sendingHostname = (arguments.itemStruct?.message?.senderaddress?.domain ?: "");					
					}
					if (structKeyExists(arguments.itemStruct, "sendgrid_pool") and structKeyExists(variables.sendgridIPPools,arguments.itemStruct.sendgrid_pool)) {
						local.ipPoolID = variables.sendgridIPPools[arguments.itemStruct.sendgrid_pool].ipPoolID;
					}
					local.remoteip = arguments.itemStruct?.attempt?.connection?.remoteip ?: "";	
					local.reason = arguments?.itemStruct?.reason ?: "";
					local.response = arguments?.itemStruct?.dsn?.diagnosticcode ?: "";
					local.status = arguments.itemStruct?.attempt?.result?.code ?: "";
					if (structKeyExists(arguments.itemStruct, "type")) {
						local.timestamp = arguments.itemStruct.timestamp;
					} else {
						local.timestamp = arguments.itemStruct.time;
					}
					local.epochDate = local.timestamp / 1000;					// Calculate adjustments for Central timezone and daylightsavingtime
					local.Offset = ((GetTimeZoneInfo().utcHourOffset)+1)*-3600;
					// Date is returned as number of seconds since 1-1-1970
					local.responseDateInCT = DateAdd('s', local.epochDate+local.Offset, CreateDateTime(1970, 1, 1, 0, 0, 0));
					local.sendgridSubuserInfo = getSendgridSubUsersAndDomains(subuserUsername=local.sgSubuser ,sendingdomain=local.sendingHostname);

					switch(local.mailSystem) {
						case "lyris":
							if ((randrange(1,100) <= application.lyrisWebhookProcessingRatio) && structKeyExists(arguments.itemStruct, "sa_name")) {
								local.membernumber="";
								local.lyris_memberid=0;
								if (structKeyExists(arguments.itemStruct, "sa_rcp") and listLen(arguments.itemStruct.sa_rcp,"|",true) eq 2) {
									local.membernumber=listFirst(arguments.itemStruct.sa_rcp,"|",true);
									local.lyris_memberid=val(listLast(arguments.itemStruct.sa_rcp,"|",true));
								}
								local.sendingApplicationID = lyrisEmailTracking_getSendingApplicationID(applicationType=arguments.itemStruct.mc_mailtype, applicationName=local.saName);

								if (structKeyExists(arguments.itemStruct, "type")) {
									switch(arguments.itemStruct.type) {
										case "open":
										case "click":
											if (local.sg_event_id eq "") {
												systemOutput(obj="#dateTimeFormat(now(),"yyyy-mm-dd HH:nn:ss")# [emailQueueManager.cfc] [processHalonEventWebhookItemV2] engagement event missing eventid - Halon event type v2 webhook entry: #serializeJSON(arguments.itemStruct)#", addNewLine=true, doErrorStream=false);
											}
											else {
												local.success = sendLyrisEngagementTrackingMessage(
													applicationType=arguments.itemStruct.mc_mailtype, 
													applicationName=local.saName,
													sgEventID = local.sg_event_id,
													sgMessageID = local.sg_message_id,
													event = arguments.itemStruct.type,
													timestamp = local.responseDateInCT,
													membernumber = local.membernumber,
													sendingApplicationID = val(local.sendingApplicationID),
													sendingApplicationMessageID = val(local.sendingMessageID),
													sendingapplicationrecipientid = val(local.lyris_memberid),
													siteID = val(local.siteID),
													emailusername = local.emailUsername,
													emaildomain = local.emailDomain,
													emailDomainID = val(local.emailDomainID),
													useragent = local.useragentInfo.useragent,
													useragentID = val(local.useragentInfo.useragentID),
													useragentSHA1 = local.useragentInfo.useragentSHA1,
													ipaddress = local.ipaddress,
													isMachineOpen = local.isMachineOpen,
													url = local.url
												);
											}

											break;
										default:
											systemOutput(obj="#dateTimeFormat(now(),"yyyy-mm-dd HH:nn:ss")# [emailQueueManager.cfc] [processHalonEventWebhookItemV2] unknown engagement event - Halon event type v2 webhook entry: #serializeJSON(arguments.itemStruct)#", addNewLine=true, doErrorStream=false);

									}
								} else {
									switch(arguments.itemStruct.event) {
										case "processed": case "delivered":
											local.deliveryStatusCode = arguments.itemStruct.event;
											local.deliveryStatusID = variables.deliveryStatuses[local.deliveryStatusCode].deliveryStatusID;
											break;
										case "deferred": 
											local.deliveryStatusCode = arguments.itemStruct.event;
											local.deliveryStatusID = variables.deliveryStatuses[local.deliveryStatusCode].deliveryStatusID;
											if (len(local.response))
												local.messageTemplate = generateMessageTemplate(emailUsername=local.emailUsername,emailDomain=local.emailDomain,message=local.response);
											break;
										case "dropped":

											local.deliveryStatusCode = arguments.itemStruct.event;
											local.deliveryStatusID = variables.deliveryStatuses[local.deliveryStatusCode].deliveryStatusID;
											if (len(local.reason))
												local.messageTemplate = generateMessageTemplate(emailUsername=local.emailUsername,emailDomain=local.emailDomain,message=local.reason);
											break;
										case "failed":
											local.bounceClassificationID =  getBounceClassification(arguments.itemStruct.class);
											switch(arguments.itemStruct.class) {
												case "spam":
												case "badrecipient":
												case "transientfail":
												case "dnsFail":
												case "serverpolicy":
												case "relayfail":
												case "generalfail":
												case "badsender":
												case "overquota":
												case "timeout":
												case "content":
												case "serverfail":
												case "verifyfail":
												case "other":
													if (arguments.itemStruct.action eq "QUEUE") {
														local.deliveryStatusCode = "deferred";
														local.deliveryStatusID = variables.deliveryStatuses[local.deliveryStatusCode].deliveryStatusID;
														if (len(local.response))
															local.messageTemplate = generateMessageTemplate(emailUsername=local.emailUsername,emailDomain=local.emailDomain,message=local.response);
													}
													else if (arguments.itemStruct.action eq "BOUNCE") {
														local.deliveryStatusCode = "bounce-technical";
														if (structKeyExists(variables.deliveryStatuses,local.deliveryStatusCode))
															local.deliveryStatusID = variables.deliveryStatuses[local.deliveryStatusCode].deliveryStatusID;
														else {
															local.deliveryStatusID = variables.deliveryStatuses["#arguments.itemStruct.type#-unclassified"].deliveryStatusID;
														}
														if (len(local.response))
															local.messageTemplate = generateMessageTemplate(emailUsername=local.emailUsername,emailDomain=local.emailDomain,message=local.response);
													}
													else {
														systemOutput(obj="#dateTimeFormat(now(),"yyyy-mm-dd HH:nn:ss")# [emailQueueManager.cfc] [processHalonEventWebhookItemV2] unknown failed - Halon event type webhook entry: #serializeJSON(arguments.itemStruct)#", addNewLine=true, doErrorStream=false);
													}														
												break;
												case "block":
													local.deliveryStatusCode = "blocked-unclassified";
													local.deliveryStatusID = variables.deliveryStatuses[local.deliveryStatusCode].deliveryStatusID;
													if (len(local.response))
														local.messageTemplate = generateMessageTemplate(emailUsername=local.emailUsername,emailDomain=local.emailDomain,message=local.response);
												break;
												default:
													systemOutput(obj="#dateTimeFormat(now(),"yyyy-mm-dd HH:nn:ss")# [emailQueueManager.cfc] [processHalonEventWebhookItemV2] unknown - Halon event type webhook entry: #serializeJSON(arguments.itemStruct)#", addNewLine=true, doErrorStream=false);
	
											}											
											break;
									}
	
									switch(arguments.itemStruct.event) {
										case "processed": case "delivered": case "deferred": case "dropped": case "failed":
										// case "bounce":

											local.success = sendLyrisDeliveryTrackingMessage(
												applicationType=arguments.itemStruct.mc_mailtype, 
												applicationName=local.saName,
												sgEventID = local.sg_event_id,
												sgMessageID = local.sg_message_id,
												event = arguments.itemStruct.event,
												timestamp = local.responseDateInCT,
												membernumber = local.membernumber,
												sendingApplicationID = val(local.sendingApplicationID),
												sendingApplicationMessageID = val(local.sendingMessageID),
												sendingapplicationrecipientid = val(local.lyris_memberid),
												siteID = val(local.siteID),
												emailusername = local.emailUsername,
												emaildomain = local.emailDomain,
												emailDomainID = val(local.emailDomainID),
												deliveryStatusID = val(local.deliveryStatusID),
												smtpstatuscode = local.status,
												attempt = local.attempt,
												ipPoolID = val(local.ipPoolID),
												sendgridSubuserID = val(local.sendgridSubuserInfo.subuserID),
												sendgridSubuserDomainID = val(local.sendgridSubuserInfo.subuserDomainID),
												messageTemplate = local.messageTemplate.template,
												messageSHA1 = local.messageTemplate.sha1,
												messageTemplateMatchFound = local.messageTemplate.matchFound,
												bounceClassificationID = local.bounceClassificationID 
											);
											break;
										default:
											systemOutput(obj="#dateTimeFormat(now(),"yyyy-mm-dd HH:nn:ss")# [emailQueueManager.cfc] [processHalonEventWebhookItemV2] unknown - Halon event type webhook entry: #serializeJSON(arguments.itemStruct)#", addNewLine=true, doErrorStream=false);
									}

								}
							}							
						break;
						case "emailsendingqueue":
							systemOutput(obj="#dateTimeFormat(now(),"yyyy-mm-dd HH:nn:ss")# [emailQueueManager.cfc] [processHalonEventWebhookItemV2] emailsendingqueue - Halon event type webhook entry: #serializeJSON(arguments.itemStruct)#", addNewLine=true, doErrorStream=false);
							switch(arguments.itemStruct.event) {
								case "processed":
									local.success = recordSendGridStatusTracking(siteID=local.siteID, messageID=local.sendingMessageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_process', sgSubuser=local.sgSubuser, updateDate=true, sgEventID="", ipaddress="", detail="", reason="", status="", responseDate=local.responseDateInCT, jsonResult="");
									break;
								case "dropped":
									local.success = recordSendGridStatusTracking(siteID=local.siteID, messageID=local.sendingMessageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_drop', sgSubuser=local.sgSubuser, updateDate=true, sgEventID=local.sg_event_id, ipaddress="", detail=local.reason, reason=local.reason, status=local.status, responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct));
									break;
								case "delivered":
									local.success = recordSendGridStatusTracking(siteID=local.siteID, messageID=local.sendingMessageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_deliver', sgSubuser=local.sgSubuser, updateDate=true, sgEventID="", ipaddress="", detail="", reason="", status=local.status, responseDate=local.responseDateInCT, jsonResult="");
									break;
								case "deferred":
									local.success = recordSendGridStatusTracking(siteID=local.siteID, messageID=local.sendingMessageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_defer', sgSubuser=local.sgSubuser, updateDate=true, sgEventID="", ipaddress="", detail=local.response, reason="", status="", responseDate=local.responseDateInCT, jsonResult="");
									break;
								case "bounce":
									local.type = "";
									if (isDefined("arguments.itemStruct.type")) local.type = arguments.itemStruct.type;
									if (local.type EQ 'blocked')
										local.success = recordSendGridStatusTracking(siteID=local.siteID, messageID=local.sendingMessageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_block', sgSubuser=local.sgSubuser, updateDate=true, sgEventID=local.sg_event_id, ipaddress="", detail=local.reason, reason=local.reason, status=local.status, responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct));
									else 
										local.success = recordSendGridStatusTracking(siteID=local.siteID, messageID=local.sendingMessageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_bounce', sgSubuser=local.sgSubuser, updateDate=true, sgEventID=local.sg_event_id, ipaddress="", detail="Type: #local.type# Reason: #local.reason#", reason=local.reason, status=local.status, responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct));
									break;
								case "open":
									local.ip = "";
									if (isDefined("arguments.itemStruct.ip")) local.ip = arguments.itemStruct.ip;
									local.success = recordSendGridStatusTracking(siteID=local.siteID, messageID=local.sendingMessageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_open', sgSubuser=local.sgSubuser, updateDate=true, sgEventID=local.sg_event_id, ipaddress=local.ip, detail="", reason="", status="", responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct));
									break;
								case "spamreport":
									local.success = recordSendGridStatusTracking(siteID=local.siteID, messageID=local.sendingMessageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_spam', sgSubuser=local.sgSubuser, updateDate=true, sgEventID=local.sg_event_id, ipaddress="", detail="", reason="", status="", responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct));
									break;
								case "click":
									local.ip = "";
									if (isDefined("arguments.itemStruct.ip")) local.ip = arguments.itemStruct.ip;
									local.clickurl = "";
									if (isDefined("arguments.itemStruct.url")) local.clickurl = arguments.itemStruct.url;
									local.success = recordSendGridStatusTracking(siteID=local.siteID, messageID=local.sendingMessageID,recipientID=arguments.itemStruct.mc_recipientid, statusCode='sg_click', sgSubuser=local.sgSubuser, updateDate=false, sgEventID=local.sg_event_id, ipaddress=local.ip, detail=local.clickurl, reason="", status="", responseDate=local.responseDateInCT, jsonResult=serializeJSON(arguments.itemStruct));
									break;
							}
							break;
					}
				}
			} catch (e) {
				application.objError.sendError(cfcatch=e, objectToDump={arguments=arguments, local=local}, customMessage="Error processing a Sendgrid event webhook entry v2");
				local.success = false;
			}
			return local.success;
		</cfscript>
	</cffunction>


	<cffunction name="checkBalancedParenthesis" access="private" returntype="boolean" output="false">
		<cfargument name="stringToCheck" type="string" required="true">

		<cfscript>
			var isBalanced = true;
			var balanceCheck = stringToCheck.rereplace('[^()]',"","all").listtoArray("").reduce((answer, thisCharacter) => {
				switch(thisCharacter) {
					case "(":
						answer.depth = answer.depth+1;
						break;
					case ")":
						answer.depth = answer.depth-1;
						break;
		
				}
				if (answer.depth < 0) answer.outOfOrder = true;
				return answer
			} , {outOfOrder: false, depth: 0})
			
			if (balanceCheck.outOfOrder OR balanceCheck.depth neq 0)
				isBalanced = false;
		</cfscript>

		<cfreturn isBalanced>
	</cffunction>
	
	<cffunction name="lyrisEmailTracking_getSendingApplicationID" access="private" returntype="numeric" output="false">
		<cfargument name="applicationType" type="string" required="true">
		<cfargument name="applicationName" type="string" required="true">

		<cfscript>
			var local = {};
			local.cachekey = lcase("EmailQueueManager-lyrisEmailTracking_getSendingApplicationID-#arguments.applicationType#-#arguments.applicationName#");
			local.sendingApplicationID = application.mcCacheManager.clusterGetValue(keyname=local.cachekey, defaultValue=0);				

			if (not local.sendingApplicationID) {
				local.sendingApplicationInfo = queryExecute("
					set nocount on;
					declare @sendingApplicationID int;
					exec emailTracking.dbo.emailTracking_getApplicationID
						@applicationType = :applicationType,
						@applicationName = :applicationName,
						@sendingApplicationID = @sendingApplicationID OUTPUT

					select isnull(@sendingApplicationID,0) as sendingApplicationID
				", 
				{ 
					applicationType = { value=arguments.applicationType, cfsqltype="cf_sql_varchar" },
					applicationName = { value=arguments.applicationName, cfsqltype="cf_sql_varchar" }
				}, 
				{ datasource=application.dsn.lyrisarchive.dsn, cachedwithin=createTimeSpan(0,0,5,0) } );

				local.sendingApplicationID = local.sendingApplicationInfo.sendingApplicationID;

				if (local.sendingApplicationID)
					application.mcCacheManager.clusterSetValue(keyname=local.cachekey, value=local.sendingApplicationID);
			}


			return local.sendingApplicationID;
		</cfscript>
	</cffunction>
	<cffunction name="lyrisEmailTracking_getDomainID" access="private" returntype="numeric" output="false">
		<cfargument name="domain" type="string" required="true">
		<cfscript>
			var local = {};
			local.cachekey = lcase("EmailQueueManager-lyrisEmailTracking_getDomainID-#arguments.domain#");
			local.domainID = application.mcCacheManager.clusterGetValue(keyname=local.cachekey, defaultValue=0);

			if (not local.domainID) {
				local.domainInfo = queryExecute("
					set nocount on;
					declare @domainID int;
					exec emailTracking.dbo.emailTracking_getDomainID
						@domain = :domain,
						@domainID = @domainID OUTPUT

					select isnull(@domainID,0) as domainID
				", 
				{ 
					domain = { value=arguments.domain, cfsqltype="cf_sql_varchar" }
				}, 
				{ datasource=application.dsn.lyrisarchive.dsn, cachedwithin=createTimeSpan(0,0,5,0) } );

				local.domainID = local.domainInfo.domainID;

				if (local.domainID)
					application.mcCacheManager.clusterSetValue(keyname=local.cachekey, value=local.domainID);
			}
			return local.domainID;
		</cfscript>
	</cffunction>
	<cffunction name="lyrisEmailTracking_getUserAgentInfo" access="private" returntype="struct" output="false">
		<cfargument name="userAgent" type="string" required="true">
		<cfscript>
			var local = {};
			local.returnStruct = {
				useragent = arguments.useragent,
				useragentSHA1 = "",
				userAgentID = 0
			}
			local.returnStruct.useragentSHA1 = hash(local.returnStruct.useragent, "SHA", "UTF-8");

			local.cachekey = lcase("EmailQueueManager-lyrisEmailTracking_getUserAgentInfo-#local.returnStruct.useragentSHA1#");
			local.userAgentID = application.mcCacheManager.clusterGetValue(keyname=local.cachekey, defaultValue=0);

			if (not local.userAgentID) {
				local.userAgentInfo = queryExecute("
					set nocount on;
					declare @userAgentID int;
					exec emailTracking.dbo.emailTracking_getUseragentID
						@userAgentSHA1 = :useragentSHA1,
						@userAgentID = @userAgentID OUTPUT

					select isnull(@userAgentID,0) as userAgentID
				", 
				{ 
					useragentSHA1 = { value=local.returnStruct.useragentSHA1, cfsqltype="cf_sql_varchar" }
				}, 
				{ datasource=application.dsn.lyrisarchive.dsn, cachedwithin=createTimeSpan(0,0,5,0) } );

				local.userAgentID = local.userAgentInfo.userAgentID;
				if (local.userAgentID)
					application.mcCacheManager.clusterSetValue(keyname=local.cachekey, value=local.userAgentID);
			}

			local.returnStruct.userAgentID = local.userAgentID;


			return local.returnStruct;
		</cfscript>
	</cffunction>
	<cffunction name="getSendgridIPPools" access="private" returntype="struct" output="false">
		<cfscript>
			local.ipPools = queryExecute("
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select ipPoolID, poolname
				from platformMail.dbo.sendgrid_ipPools;
				
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			", 
			{}, 
			{ datasource=application.dsn.platformMail.dsn, returntype="struct", columnkey="poolname" } );


			return local.ipPools;
		</cfscript>
	</cffunction>

	<cffunction name="getDeliveryStatusIDs" access="private" returntype="struct" output="false">
		<cfscript>
			local.statuses = queryExecute("

				select deliveryStatusID, statusCode
				from emailTracking.dbo.deliveryStatuses;


			", 
			{ 
			}, 
			{ datasource=application.dsn.lyrisarchive.dsn, returntype="struct", columnkey="statusCode" } );


			return local.statuses;
		</cfscript>
	</cffunction>
	
	<cffunction name="getBounceClassifications" access="private" returntype="struct" output="false">
		<cfscript>
			local.bounceClassifications = queryExecute("
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select bounceClassificationID, class
				from platformMail.dbo.email_bounceClassifications;
				
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			", 
			{}, 
			{ datasource=application.dsn.platformMail.dsn, returntype="struct", columnkey="class" } );


			return local.bounceClassifications;
		</cfscript>
	</cffunction>

	<cffunction name="addBounceClassification" access="private" returntype="void" output="false">
		<cfargument name="class" type="string" required="true">
		<cfscript>
			// using insert/select/except to guard against race condition of multiple requests attempting to add the same new value 
			queryExecute("
				INSERT INTO platformMail.dbo.email_bounceClassifications (class)
				select :newclass
				except
				select class
				from platformMail.dbo.email_bounceClassifications
				where class=:newclass
			", 
			{ 
				newclass = { value=arguments.class, cfsqltype="cf_sql_varchar" }
			}, 
			{ datasource=application.dsn.platformMail.dsn, result="local.insertResult" } );
		</cfscript>
	</cffunction>	

	<cffunction name="getBounceClassification" access="private" returntype="number" output="false">
		<cfargument name="class" type="string" required="true">
		<cfscript>
			var local = {};
			local.classID = 0;
			local.class = trim(arguments.class);

			if (len(local.class)) {

				if (structKeyExists(variables.bounceClassifications,local.class))
					local.classID = variables.bounceClassifications[local.class].bounceClassificationID;
				else {
					addBounceClassification(local.class);
					//refresh cache
					variables.bounceClassifications = getBounceClassifications();
					if (structKeyExists(variables.bounceClassifications,local.class))
						local.classID = variables.bounceClassifications[local.class].bounceClassificationID;
				}
				
			}

			return local.classID;
		</cfscript>
	</cffunction>

	<cffunction name="getSendgridSubUsersAndDomains" access="private" returntype="struct" output="false">
		<cfargument name="subuserUsername" type="string" required="true">
		<cfargument name="sendingdomain" type="string" required="true">

		<cfscript>
			var local = {};
			local.returnStruct = {};
			local.objkey = "#arguments.subuserUsername#@#arguments.sendingdomain#";
			if (structKeyExists(variables.sendgridSubUsersAndDomains,local.objkey))
				local.returnStruct = variables.sendgridSubUsersAndDomains[local.objkey];
			else {
				local.returnStruct = loadSendgridSubUsersAndDomains(argumentCollection=arguments);
				if (structCount(local.returnStruct))
					variables.sendgridSubUsersAndDomains.append(sourceStruct=local.returnStruct, overwriteFlag=true);
			}
			return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="loadSendgridSubUsersAndDomains" access="private" returntype="struct" output="false">
		<cfargument name="subuserUsername" type="string" required="false" default="">
		<cfargument name="sendingdomain" type="string" required="false" default="">
		<cfset var local = {}>

		<cfquery name="local.subusersAndDomains" datasource="#application.dsn.platformMail.dsn#" returntype="struct" columnkey="objkey">
			set nocount on;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @activeStatusID int;
			DECLARE @environmentID INT, @environmentName varchar(50);

			SELECT @environmentName = tier FROM membercentral.dbo.fn_getServerSettings();
			SELECT @environmentID = environmentID FROM membercentral.dbo.platform_environments WHERE environmentName = @environmentName;

			select @activeStatusID=subuserStatusID
			from platformMail.dbo.sendgrid_subuserStatuses
			where status='Active'

			select objkey = su.username + '@' + sud.sendinghostname, sud.sendinghostname, su.username, su.subuserID, sud.subuserDomainID
			from platformMail.dbo.sendgrid_subuserDomains sud
			inner join platformMail.dbo.sendgrid_subusers su 
				on su.subuserID = sud.subuserID
				and su.environmentID in (@environmentID,5)
				and su.statusID=@activeStatusID
				and sud.statusID=@activeStatusID
				<cfif len(arguments.subuserUsername)>
					and su.username = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.subuserUsername#">
				</cfif>
				<cfif len(arguments.sendingdomain)>
					and sud.sendinghostname = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.sendingdomain#">
				</cfif>

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfreturn local.subusersAndDomains>
	</cffunction>

	<cffunction name="generateMessageTemplate" access="private" returntype="struct" output="false">
		<cfargument name="emailUsername" type="string" required="true">
		<cfargument name="emailDomain" type="string" required="true">
		<cfargument name="message" type="string" required="true">

		<cfscript>
			var local = {};
			local.returnStruct = { SHA1 = "",template=arguments.message, matchFound=0};
			if (len(arguments.emailUsername) AND len (arguments.emailDomain)) {
				local.returnStruct.template = local.returnStruct.template.replaceNoCase(arguments.emailUsername,'[username]','all').replaceNoCase(arguments.emailDomain,'[domain]','all').rereplace('[\s]+',' ','all');
			}
			local.returnStruct.SHA1 = hash(local.returnStruct.template, "SHA", "UTF-8");

			local.cachekey = lcase("EmailQueueManager-generateMessageTemplate-#local.returnStruct.SHA1#");
			local.matchFound = application.mcCacheManager.clusterGetValue(keyname=local.cachekey, defaultValue=0);

			if (not local.matchFound) {
				local.templateExists = queryExecute("

					declare @matchFound bit = 0;
					IF EXISTS ( select 1 from emailTracking.dbo.deliveryMessageTemplates where messageTemplateSHA1 = :messageTemplateSHA1)
						set @matchFound = 1;
					
					select @matchFound as matchFound
				", 
				{ 
					messageTemplateSHA1 = { value=local.returnStruct.SHA1, cfsqltype="cf_sql_varchar" }
				}, 
				{ datasource=application.dsn.lyrisarchive.dsn} );

				local.matchFound = local.templateExists.matchFound;

				if (local.matchFound)
					application.mcCacheManager.clusterSetValue(keyname=local.cachekey, value=local.matchFound);
			}
			local.returnStruct.matchFound = local.matchFound;

			return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="generateConsentListManagementURL" access="private" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="hostname" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="email" type="string" required="true">
		<cfargument name="messageID" type="numeric" required="false" default="0">

		<cfset var local = structNew()>
		<cfset local.siteCode = application.objSiteInfo.getSiteCodeFromSiteID(siteID=arguments.siteID)>

		<cfif len(arguments.email)>
			<cfset local.strConsentList = structNew()>
			<cfset local.strConsentList.m = arguments.memberID>
			<cfset local.strConsentList.s = arguments.siteID>
			<cfset local.strConsentList.d = dateAdd('M', 1, now())>
			<cfset local.strConsentList.e = arguments.email>
			<cfset local.strConsentList.em = arguments.messageID>

			<cfif arguments.messageID>
				<cfquery datasource="#application.dsn.platformMail.dsn#" name="local.consentLists">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
	
					declare 
						@siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">,
						@messageID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.messageID#">;
	
					select consentListID
					from email_messageConsentLists
					where siteID = @siteID and messageID = @messageID
					order by isPrimary desc;
	
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
				<cfset local.strConsentList.l = valueList(local.consentLists.consentListID)>
			</cfif>
			<cfset local.JSONString = serializeJSON(local.strConsentList)>
			<cfset local.encryptedID = encrypt(local.JSONString,"TRiaL_SMiTH", "CFMX_COMPAT", "Hex")>
			<cfset local.consentListManagementURL = "#application.objSiteInfo.mc_siteInfo[local.siteCode].scheme#://#arguments.hostname#/?pg=emailPreferences&id=#local.encryptedID#">
		<cfelse>
			<cfset local.consentListManagementURL = "">
		</cfif>

		<cfreturn local.consentListManagementURL>
	</cffunction>

</cfcomponent>