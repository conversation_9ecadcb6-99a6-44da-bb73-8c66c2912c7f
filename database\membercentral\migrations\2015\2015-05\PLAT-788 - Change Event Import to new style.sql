use membercentral
GO

DROP PROC dbo.ev_importEvents_toTemp
GO
DROP PROC dbo.ev_importEvents_toPerm
GO

CREATE PROC [dbo].[ev_importEvents_import]
@siteID int,
@recordedByMemberID int,
@ovAction char(1),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

declare @nowDate datetime, @queueTypeID int, @statusInserting int, @statusReady int,
	@itemGroupUID uniqueidentifier, @colList varchar(max), @dynSQL nvarchar(max)
set @nowDate = getdate()
select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'importEvents'
select @statusInserting = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'insertingItems'
select @statusReady = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess'

-- all imported events get the same ItemGroupUID
select @itemGroupUID = NEWID()


DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	insert into platformQueue.dbo.tblQueueItems (itemUID, queueStatusID)
	select itemUID, @statusInserting
	from #mc_EvImport
	
	insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueInteger)
	select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtInt.columnValueInt
	from #mc_EvImport as tmp
	inner join (
		select rowID, columnname, columnValueInt
		from #mc_EvImport
		unpivot (columnValueInt for columnname in (MCCalendarID, MCEventID, MCCategoryID, MCRegistrationID, MCParentEventID)) u
	) as unPvtInt on unPvtInt.rowID = tmp.rowID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtInt.columnname

	insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueString)
	select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtString.columnValueString
	from #mc_EvImport as tmp
	inner join (
		select rowID, columnname, columnValueString
		from #mc_EvImport
		unpivot (columnValueString for columnname in (EventTitle, EventCode, ParentEventCode, RegistrationReplyEmail, ContactTitle, LocationTitle, CancellationTitle, TravelTitle, InformationTitle)) u
	) as unPvtString on unPvtString.rowID = tmp.rowID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtString.columnname

	insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueDate)
	select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtDate.columnValueDate
	from #mc_EvImport as tmp
	inner join (
		select rowID, columnname, columnValueDate
		from #mc_EvImport
		unpivot (columnValueDate for columnname in (EventStart, EventEnd)) u
	) as unPvtDate on unPvtDate.rowID = tmp.rowID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtDate.columnname

	insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueBit)
	select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtBit.columnValueBit
	from #mc_EvImport as tmp
	inner join (
		select rowID, columnname, columnValueBit
		from #mc_EvImport
		unpivot (columnValueBit for columnname in (EventHidden, EventAllDay, ContactInclude, LocationInclude, CancellationInclude, TravelInclude, DisplayCredits)) u
	) as unPvtBit on unPvtBit.rowID = tmp.rowID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtBit.columnname

	insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueText)
	select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtText.columnValueText
	from #mc_EvImport as tmp
	inner join (
		select rowID, columnname, columnValueText
		from #mc_EvImport
		unpivot (columnValueText for columnname in (EventDescription, Contact, Location, Cancellation, Travel, Information, InternalNotes)) u
	) as unPvtText on unPvtText.rowID = tmp.rowID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtText.columnname

	insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueString)
	select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, @ovAction
	from #mc_EvImport as tmp
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'ovAction'

	-- add credits
	set @colList = null
	select @colList = COALESCE(@colList + ',', '') + authorityCode + '_approval,' + authorityCode + '_status' from #tblPossibleCredits 
	if @colList is not null BEGIN
		select @dynSQL = '
			select ''' + cast(@itemGroupUID as varchar(60)) + ''', tmp.itemUID, ' + cast(@recordedByMemberID as varchar(20)) + ', ' + cast(@siteID as varchar(10)) + ', dc.columnID, tmp.rowID, unPvtStr.columnValueString
			from #mc_EvImport as tmp
			inner join (
				select rowID, columnname, columnValueString
				from #mc_EvImport 
				unpivot (columnValueString for columnname in (' + @colList + ')) u
			) as unPvtStr on unPvtStr.rowID = tmp.rowID
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = ' + cast(@queueTypeID as varchar(10)) + ' and dc.columnname = unPvtStr.columnname'
		insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueString)
		EXEC(@dynSQL)
	end

	set @colList = null
	select @colList = COALESCE(@colList + ',', '') + column_name from #tblPossibleCreditCols 
	IF @colList is not null BEGIN
		select @dynSQL = '
			select ''' + cast(@itemGroupUID as varchar(60)) + ''', tmp.itemUID, ' + cast(@recordedByMemberID as varchar(20)) + ', ' + cast(@siteID as varchar(10)) + ', dc.columnID, tmp.rowID, unPvtDec.columnValueDecimal2
			from #mc_EvImport as tmp
			inner join (
				select rowID, columnname, columnValueDecimal2
				from #mc_EvImport 
				unpivot (columnValueDecimal2 for columnname in (' + @colList + ')) u
			) as unPvtDec on unPvtDec.rowID = tmp.rowID
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = ' + cast(@queueTypeID as varchar(10)) + ' and dc.columnname = unPvtDec.columnname'
		insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueDecimal2)
		EXEC(@dynSQL)
	END

	-- update queue item groups to show ready to process
	update qi WITH (UPDLOCK, HOLDLOCK)
	set qi.queueStatusID = @statusReady,
		qi.dateUpdated = getdate()
	from platformQueue.dbo.tblQueueItems as qi
	inner join #mc_EvImport as tmp on tmp.itemUID = qi.itemUID


	IF @TranCounter = 0
		COMMIT TRAN;
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	INSERT INTO #tblEvErrors (msg)
	VALUES ('Unable to queue events for import.')

	INSERT INTO #tblEvErrors (msg)
	VALUES (left(error_message(),300))
END CATCH


on_done:
	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblEvErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE)

RETURN 0
GO

CREATE PROC [dbo].[ev_importEvents_validate]
@siteid int, 
@importResult xml OUTPUT

AS

SET NOCOUNT ON

declare @ASID int, @crdAuthorityCode varchar(20), @creditColName varchar(50), @dynSQL nvarchar(max), @queueTypeID int
select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'importEvents'
set @importResult = null


-- ***********
-- clean table 
-- ***********
BEGIN TRY
	-- delete empty rows
	delete from #mc_EvImport where Calendar is null and EventTitle is null;
END TRY
BEGIN CATCH
	INSERT INTO #tblEvErrors (msg)
	VALUES ('Unable to clean import table.')

	INSERT INTO #tblEvErrors (msg)
	VALUES (left(error_message(),300))

	GOTO on_done
END CATCH


-- ****************
-- required columns 
-- ****************
BEGIN TRY
	-- match on calendar
	update tmp 
	set tmp.MCCalendarID = c.calendarID 
	from #mc_EvImport as tmp 
	inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceName = isnull(tmp.calendar,'') and ai.siteID = @siteID
	inner join dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID and sr.siteResourceStatusID = 1 
	inner join dbo.ev_calendars as c on c.applicationInstanceID = ai.applicationInstanceID

	-- check for missing calendars
	BEGIN TRY
		ALTER TABLE #mc_EvImport ALTER COLUMN MCCalendarID int not null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' does not match an existing calendar.'
		FROM #mc_EvImport
		WHERE MCCalendarID IS NULL
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done
	END CATCH

	-- no blank eventTitles
	update #mc_EvImport set eventTitle = '' where eventTitle is null;

	INSERT INTO #tblEvErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing EventTitle.'
	FROM #mc_EvImport
	WHERE eventTitle = ''
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- eventTitles must be at or under 200 chars
	INSERT INTO #tblEvErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an invalid EventTitle. EventTitles must be 200 characters or less.'
	FROM #mc_EvImport
	WHERE len(EventTitle) > 200 
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- no blank eventCodes
	update #mc_EvImport set eventCode = '' where eventCode is null;

	INSERT INTO #tblEvErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing EventCode.'
	FROM #mc_EvImport
	WHERE eventCode = ''
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- eventcode must be at or under 15 chars
	INSERT INTO #tblEvErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an invalid EventCode. EventCodes must be 15 characters or less.'
	FROM #mc_EvImport
	WHERE len(eventCode) > 15 
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- eventcode must be unique in file
	BEGIN TRY
		ALTER TABLE #mc_EvImport ALTER COLUMN eventCode varchar(15) not null;
		ALTER TABLE #mc_EvImport ADD PRIMARY KEY (eventCode);
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'EventCode ' + eventCode + ' appears in the file multiple times. EventCodes must be unique.'
		FROM #mc_EvImport
		GROUP BY eventCode
		HAVING count(*) > 1
		ORDER BY eventCode
			IF @@ROWCOUNT > 0 GOTO on_done
	END CATCH

	-- match on eventCode
	update tmp 
	set tmp.MCEventID = e.eventID
	from #mc_EvImport as tmp 
	inner join dbo.ev_events as e on e.reportCode = tmp.eventCode
		and e.status = 'A'
		and e.siteID = @siteID
	inner join dbo.ev_calendarEvents as ce on ce.sourceEventID = e.eventID
		and ce.calendarID = tmp.MCCalendarID
		and ce.calendarID = ce.sourceCalendarID

	-- lookup registration
	update tmp 
	set tmp.MCRegistrationID = r.registrationID
	from #mc_EvImport as tmp 
	inner join dbo.ev_registration as r on r.eventID = tmp.MCEventID 
		and r.status = 'A'
		and r.registrationTypeID = 1
	where tmp.MCEventID is not null

	-- no blank EventCategory
	update #mc_EvImport set EventCategory = '' where EventCategory is null;

	INSERT INTO #tblEvErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing EventCategory.'
	FROM #mc_EvImport
	WHERE EventCategory = ''
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- match on EventCategory
	update tmp 
	set tmp.MCCategoryID = cat.categoryID  
	from #mc_EvImport as tmp 
	inner join dbo.ev_calendars as c on c.calendarID = tmp.MCCalendarID
	inner join dbo.ev_categories as cat on cat.calendarID = c.calendarID and cat.category = tmp.EventCategory

	-- check for missing EventCategory
	BEGIN TRY
		ALTER TABLE #mc_EvImport ALTER COLUMN MCCategoryID int not null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' does not match an existing category for that calendar.'
		FROM #mc_EvImport
		WHERE MCCategoryID IS NULL
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done
	END CATCH

	-- ensure EventStart is datetime (allow nulls for this check)
	BEGIN TRY
		ALTER TABLE #mc_EvImport ALTER COLUMN EventStart datetime null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('The column EventStart contains invalid dates.')
			GOTO on_done
	END CATCH

	-- check for null EventStart
	BEGIN TRY
		ALTER TABLE #mc_EvImport ALTER COLUMN EventStart datetime not null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing the required EventStart.'
		FROM #mc_EvImport
		WHERE EventStart IS NULL
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done
	END CATCH

	-- ensure EventEnd is datetime (allow nulls for this check)
	BEGIN TRY
		ALTER TABLE #mc_EvImport ALTER COLUMN EventEnd datetime null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('The column EventEnd contains invalid dates.')
			GOTO on_done
	END CATCH

	-- check for null EventEnd
	BEGIN TRY
		ALTER TABLE #mc_EvImport ALTER COLUMN EventEnd datetime not null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing the required EventEnd.'
		FROM #mc_EvImport
		WHERE EventEnd IS NULL
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done
	END CATCH

	-- check dates
	INSERT INTO #tblEvErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a EventStart after the EventEnd.'
	FROM #mc_EvImport
	WHERE EventStart > EventEnd
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- no blank RegistrationReplyEmail
	update #mc_EvImport set RegistrationReplyEmail = '' where RegistrationReplyEmail is null;

	INSERT INTO #tblEvErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing RegistrationReplyEmail.'
	FROM #mc_EvImport
	WHERE RegistrationReplyEmail = ''
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

END TRY
BEGIN CATCH
	INSERT INTO #tblEvErrors (msg)
	VALUES ('Unable to validate data in required columns.')

	INSERT INTO #tblEvErrors (msg)
	VALUES (left(error_message(),300))

	GOTO on_done
END CATCH


-- ****************
-- optional columns 
-- ****************
BEGIN TRY
	-- bit columns provided in the original upload need a value for each row
	IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'EventHidden') BEGIN
		UPDATE #mc_EvImport set EventHidden = '' where EventHidden is null;
		UPDATE #mc_EvImport set EventHidden = '1' where EventHidden in ('Yes','Y','TRUE');
		UPDATE #mc_EvImport set EventHidden = '0' where EventHidden in ('No','N','FALSE');
		
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the EventHidden column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
		FROM #mc_EvImport
		WHERE EventHidden = ''
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done

		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN EventHidden bit NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			VALUES ('There are invalid values in the EventHidden column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.')
				GOTO on_done
		END CATCH
	END

	IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'EventAllDay') BEGIN
		UPDATE #mc_EvImport set EventAllDay = '' where EventAllDay is null;
		UPDATE #mc_EvImport set EventAllDay = '1' where EventAllDay in ('Yes','Y','TRUE');
		UPDATE #mc_EvImport set EventAllDay = '0' where EventAllDay in ('No','N','FALSE');
		UPDATE #mc_EvImport set EventAllDay = '1' where EventStart = EventEnd;

		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the EventAllDay column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
		FROM #mc_EvImport
		WHERE EventAllDay = ''
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done

		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN EventAllDay bit NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			VALUES ('There are invalid values in the EventAllDay column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.')
				GOTO on_done
		END CATCH
	END

	IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'DisplayCredits') BEGIN
		UPDATE #mc_EvImport set DisplayCredits = '' where DisplayCredits is null;
		UPDATE #mc_EvImport set DisplayCredits = '1' where DisplayCredits in ('Yes','Y','TRUE');
		UPDATE #mc_EvImport set DisplayCredits = '0' where DisplayCredits in ('No','N','FALSE');

		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the DisplayCredits column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
		FROM #mc_EvImport
		WHERE DisplayCredits = ''
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done

		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN DisplayCredits bit NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			VALUES ('There are invalid values in the DisplayCredits column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.')
				GOTO on_done
		END CATCH
	END

	IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'ContactInclude') BEGIN
		UPDATE #mc_EvImport set ContactInclude = '' where ContactInclude is null;
		UPDATE #mc_EvImport set ContactInclude = '1' where ContactInclude in ('Yes','Y','TRUE');
		UPDATE #mc_EvImport set ContactInclude = '0' where ContactInclude in ('No','N','FALSE');

		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the ContactInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
		FROM #mc_EvImport
		WHERE ContactInclude = ''
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done

		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN ContactInclude bit NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			VALUES ('There are invalid values in the ContactInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.')
				GOTO on_done
		END CATCH
	END

	IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'LocationInclude') BEGIN
		UPDATE #mc_EvImport set LocationInclude = '' where LocationInclude is null;
		UPDATE #mc_EvImport set LocationInclude = '1' where LocationInclude in ('Yes','Y','TRUE');
		UPDATE #mc_EvImport set LocationInclude = '0' where LocationInclude in ('No','N','FALSE');

		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the LocationInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
		FROM #mc_EvImport
		WHERE LocationInclude = ''
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done

		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN LocationInclude bit NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			VALUES ('There are invalid values in the LocationInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.')
				GOTO on_done
		END CATCH
	END

	IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'CancellationInclude') BEGIN
		UPDATE #mc_EvImport set CancellationInclude = '' where CancellationInclude is null;
		UPDATE #mc_EvImport set CancellationInclude = '1' where CancellationInclude in ('Yes','Y','TRUE');
		UPDATE #mc_EvImport set CancellationInclude = '0' where CancellationInclude in ('No','N','FALSE');

		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the CancellationInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
		FROM #mc_EvImport
		WHERE CancellationInclude = ''
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done

		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN CancellationInclude bit NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			VALUES ('There are invalid values in the CancellationInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.')
				GOTO on_done
		END CATCH
	END

	IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'TravelInclude') BEGIN
		UPDATE #mc_EvImport set TravelInclude = '' where TravelInclude is null;
		UPDATE #mc_EvImport set TravelInclude = '1' where TravelInclude in ('Yes','Y','TRUE');
		UPDATE #mc_EvImport set TravelInclude = '0' where TravelInclude in ('No','N','FALSE');

		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the TravelInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
		FROM #mc_EvImport
		WHERE TravelInclude = ''
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done

		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN TravelInclude bit NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			VALUES ('There are invalid values in the TravelInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.')
				GOTO on_done
		END CATCH
	END

	IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'ParentEventCode') BEGIN
		-- parenteventcode must be at or under 15 chars
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an invalid ParentEventCode. ParentEventCode must be 15 characters or less.'
		FROM #mc_EvImport
		WHERE len(isnull(ParentEventCode,'')) > 15 
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done

		-- ParentEventCode cannot be itself
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an invalid ParentEventCode. An event cannot be a child event of itself.'
		FROM #mc_EvImport
		WHERE len(isnull(ParentEventCode,'')) > 0
		and ParentEventCode = EventCode
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done

		-- match on ParentEventCode for events created before
		update tmp 
		set tmp.MCParentEventID = e.eventID
		from #mc_EvImport as tmp 
		inner join dbo.ev_events as e on e.reportCode = tmp.ParentEventCode
			and e.status = 'A'
			and e.siteID = @siteID
		inner join dbo.ev_calendarEvents as ce on ce.sourceEventID = e.eventID
			and ce.calendarID = tmp.MCCalendarID
			and ce.calendarID = ce.sourceCalendarID
		where len(isnull(tmp.ParentEventCode,'')) > 0

		-- match on ParentEventCode for events in this file
		update tmp 
		set tmp.MCParentRowID = tmp2.rowID
		from #mc_EvImport as tmp 
		inner join #mc_EvImport as tmp2 on tmp2.eventCode = tmp.ParentEventCode and tmp.MCCalendarID = tmp2.MCCalendarID
		where len(isnull(tmp.ParentEventCode,'')) > 0

		-- ParentEventCode cannot be bad
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an invalid ParentEventCode. No matching EventCode in the file or Control Panel.'
		FROM #mc_EvImport
		WHERE len(isnull(ParentEventCode,'')) > 0
		and MCParentEventID is null
		and MCParentRowID is null
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done

		-- events with a parent event code must appear in the file after the parent event
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is a sub-event but appears after the master event in the file. Ensure all master events are listed before sub-events.'
		FROM #mc_EvImport
		WHERE MCParentRowID is not null
		and rowID < MCParentRowID
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done

		-- sub events cannot be parent events
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(tmp.rowID as varchar(10)) + ' is a sub-event but also appears as a parent event. Sub-events cannot contain sub-events.'
		FROM #mc_EvImport as tmp
		inner join #mc_EvImport as tmp2 on tmp2.ParentEventCode = tmp.EventCode
		WHERE (tmp.MCParentRowID is not null OR tmp.MCParentEventID is not null)
		ORDER BY tmp.rowID
			IF @@ROWCOUNT > 0 GOTO on_done
	END

END TRY
BEGIN CATCH
	INSERT INTO #tblEvErrors (msg)
	VALUES ('Unable to validate data in optional columns.')

	INSERT INTO #tblEvErrors (msg)
	VALUES (left(error_message(),300))

	GOTO on_done
END CATCH


-- **************
-- credit columns
-- **************
BEGIN TRY

	select @ASID = min(ASID) from #tblPossibleCredits
	while @ASID is not null begin
		select @crdAuthorityCode = authorityCode from #tblPossibleCredits where ASID = @ASID

		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = @crdAuthorityCode + '_approval') BEGIN
			select @dynSQL = '
				SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' has an invalid value for ' + @crdAuthorityCode + '_approval. Approvals must be 50 characters or less.''
				FROM #mc_EvImport  
				WHERE len(isnull([' + @crdAuthorityCode + '_approval],'''')) > 50 
				ORDER BY rowID'
			INSERT INTO #tblEvErrors (msg)
			EXEC(@dynSQL)
		END

		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = @crdAuthorityCode + '_status') BEGIN
			select @dynSQL = '
				SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' has an invalid value for ' + @crdAuthorityCode + '_status. Status must be Approved, Denied, Not Submitted, or Pending.''
				FROM #mc_EvImport  
				WHERE [' + @crdAuthorityCode + '_status] not in ('''',''Approved'',''Denied'',''Not Submitted'',''Pending'') 
				ORDER BY rowID'
			INSERT INTO #tblEvErrors (msg)
			EXEC(@dynSQL)
		END

		set @creditColName = null;
		select @creditColName = min(column_name) from #tblPossibleCreditCols where ASID = @ASID
		while @creditColName is not null begin
			IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = @creditColName) BEGIN
				set @dynSQL = '
					BEGIN TRY
						UPDATE #mc_EvImport set [' + @creditColName + '] = null where [' + @creditColName + '] is not null and [' + @creditColName + '] = '''';
						ALTER TABLE #mc_EvImport ALTER COLUMN [' + @creditColName + '] decimal(6,2) NULL;
					END TRY
					BEGIN CATCH
						INSERT INTO #tblEvErrors (msg)
						VALUES (''There are invalid values in the ' + @creditColName + ' column.'')
					END CATCH'
				EXEC(@dynSQL)
			END

			select @creditColName = min(column_name) from #tblPossibleCreditCols where ASID = @ASID and column_name > @creditColName
		end

		select @ASID = min(ASID) from #tblPossibleCredits where ASID > @ASID
	end

END TRY
BEGIN CATCH
	INSERT INTO #tblEvErrors (msg)
	VALUES ('Unable to validate data in credit columns.')

	INSERT INTO #tblEvErrors (msg)
	VALUES (left(error_message(),300))

	GOTO on_done
END CATCH

-- ensure credit columns are included in the queue tables
BEGIN TRY
	insert into platformQueue.dbo.tblQueueTypeDataColumns (queueTypeID, columnName, dataTypeID) 
	select @queueTypeID, tmp.column_name, tmp.dataTypeID
	from (
		select authorityCode + '_approval' as column_name, 1 as dataTypeID
		from #tblPossibleCredits
			union all
		select authorityCode + '_status' as column_name, 1 as dataTypeID
		from #tblPossibleCredits
			union all	
		select column_name, 2
		from #tblPossibleCreditCols
	) as tmp
		except 
	select queueTypeID, columnName, dataTypeID
	from platformQueue.dbo.tblQueueTypeDataColumns
	where queueTypeID = @queueTypeID
END TRY
BEGIN CATCH
	INSERT INTO #tblEvErrors (msg)
	VALUES ('Unable to add credit columns to the queue tables.')

	INSERT INTO #tblEvErrors (msg)
	VALUES (left(error_message(),300))

	GOTO on_done
END CATCH


-- *************************
-- prepare table for unpivot
-- *************************
IF NOT EXISTS (select top 1 * from #tblEvErrors) BEGIN
	BEGIN TRY
		ALTER TABLE #mc_EvImport ALTER COLUMN EventCode varchar(200) NOT NULL;
		ALTER TABLE #mc_EvImport ALTER COLUMN ParentEventCode varchar(200) NULL;
		ALTER TABLE #mc_EvImport ALTER COLUMN EventTitle varchar(200) NOT NULL;
		ALTER TABLE #mc_EvImport ALTER COLUMN RegistrationReplyEmail varchar(200) NOT NULL;
		ALTER TABLE #mc_EvImport ALTER COLUMN ContactTitle varchar(200) NULL;
		ALTER TABLE #mc_EvImport ALTER COLUMN LocationTitle varchar(200) NULL;
		ALTER TABLE #mc_EvImport ALTER COLUMN CancellationTitle varchar(200) NULL;
		ALTER TABLE #mc_EvImport ALTER COLUMN TravelTitle varchar(200) NULL;
		ALTER TABLE #mc_EvImport ALTER COLUMN InformationTitle varchar(200) NULL;

		select @ASID = min(ASID) from #tblPossibleCredits
		while @ASID is not null begin
			select @crdAuthorityCode = authorityCode from #tblPossibleCredits where ASID = @ASID

			select @dynSQL = 'ALTER TABLE #mc_EvImport ALTER COLUMN [' + @crdAuthorityCode + '_approval] varchar(50) NULL;'
			EXEC(@dynSQL)
			select @dynSQL = 'ALTER TABLE #mc_EvImport ALTER COLUMN [' + @crdAuthorityCode + '_status] varchar(50) NULL;'
			EXEC(@dynSQL)

			select @ASID = min(ASID) from #tblPossibleCredits where ASID > @ASID
		end
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to prepare data for unpivot.')

		INSERT INTO #tblEvErrors (msg)
		VALUES (left(error_message(),300))

		GOTO on_done
	END CATCH
END


-- ************************
-- generate result xml file 
-- ************************
on_done:
	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblEvErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE)
	
	IF OBJECT_ID('tempdb..#tblEventCreditCols') IS NOT NULL 
		DROP TABLE #tblEventCreditCols

RETURN 0
GO

CREATE PROC [dbo].[ev_importEvents_prepTable]
@importResult xml OUTPUT

AS

SET NOCOUNT ON

declare @ASID int, @crdAuthorityCode varchar(20), @creditColName varchar(50)
set @importResult = null


-- *********************************
-- ensure all required columns exist 
-- *********************************
BEGIN TRY
	-- this will get the columns that are required
	IF OBJECT_ID('tempdb..#tblEvOrgCols') IS NOT NULL 
		DROP TABLE #tblEvOrgCols
	CREATE TABLE #tblEvOrgCols (COLUMN_NAME sysname)

	insert into #tblEvOrgCols
	select 'rowID' union all
	select 'Calendar' union all
	select 'EventTitle' union all
	select 'EventCode' union all
	select 'EventCategory' union all
	select 'EventStart' union all
	select 'EventEnd' union all
	select 'RegistrationReplyEmail'

	-- this will get the columns that are actually in the import
	IF OBJECT_ID('tempdb..#tblEvImportCols') IS NOT NULL 
		DROP TABLE #tblEvImportCols
	CREATE TABLE #tblEvImportCols (ORDINAL_POSITION int, COLUMN_NAME sysname)

	insert into #tblEvImportCols
	select column_id, [name] 
	from tempdb.sys.columns 
	where object_id = object_id('tempdb..#mc_EvImport');

	INSERT INTO #tblEvErrors (msg)
	select 'The required column ' + org.column_name + ' is missing from your data.'
	from #tblEvOrgCols as org
	left outer join #tblEvImportCols as imp on imp.column_name = org.column_name
	where imp.ORDINAL_POSITION is null
		IF @@ROWCOUNT > 0 GOTO on_done

	delete from #tblEvImportCols 
	where column_name in (select COLUMN_NAME from #tblEvOrgCols)

	IF OBJECT_ID('tempdb..#tblEvOrgCols') IS NOT NULL 
		DROP TABLE #tblEvOrgCols
END TRY
BEGIN CATCH
	INSERT INTO #tblEvErrors (msg)
	VALUES ('Unable to validate file contains all required columns.')

	GOTO on_done
END CATCH


-- **********
-- prep table 
-- **********
BEGIN TRY
	-- add holding columns
	ALTER TABLE #mc_EvImport ADD MCCalendarID int null, MCEventID int null, MCCategoryID int null, 
		MCRegistrationID int null, MCParentEventID int null, MCParentRowID int null,
		itemUID uniqueidentifier NOT NULL DEFAULT(NEWID());

	-- ensure rowID is an int
	ALTER TABLE #mc_EvImport ALTER COLUMN RowID int not null;

	-- add missing columns
	IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'EventHidden') BEGIN
		ALTER TABLE #mc_EvImport ADD EventHidden bit NULL;
		INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('EventHidden')
	END ELSE
		delete from #tblEvImportCols where column_name = 'EventHidden'

	IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'EventAllDay') BEGIN
		ALTER TABLE #mc_EvImport ADD EventAllDay bit NULL;
		INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('EventAllDay')
	END ELSE
		delete from #tblEvImportCols where column_name = 'EventAllDay'

	IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'DisplayCredits') BEGIN
		ALTER TABLE #mc_EvImport ADD DisplayCredits bit NULL;
		INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('DisplayCredits')
	END ELSE
		delete from #tblEvImportCols where column_name = 'DisplayCredits'

	IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'ContactInclude') BEGIN
		ALTER TABLE #mc_EvImport ADD ContactInclude bit NULL;
		INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('ContactInclude')
	END ELSE
		delete from #tblEvImportCols where column_name = 'ContactInclude'

	IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'LocationInclude') BEGIN
		ALTER TABLE #mc_EvImport ADD LocationInclude bit NULL;
		INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('LocationInclude')
	END ELSE
		delete from #tblEvImportCols where column_name = 'LocationInclude'

	IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'CancellationInclude') BEGIN
		ALTER TABLE #mc_EvImport ADD CancellationInclude bit NULL;
		INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('CancellationInclude')
	END ELSE
		delete from #tblEvImportCols where column_name = 'CancellationInclude'

	IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'TravelInclude') BEGIN
		ALTER TABLE #mc_EvImport ADD TravelInclude bit NULL;
		INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('TravelInclude')
	END ELSE
		delete from #tblEvImportCols where column_name = 'TravelInclude'

	IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'EventDescription') BEGIN
		ALTER TABLE #mc_EvImport ADD EventDescription varchar(max) NULL;
		INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('EventDescription')
	END ELSE
		delete from #tblEvImportCols where column_name = 'EventDescription'

	IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'ContactTitle') BEGIN
		ALTER TABLE #mc_EvImport ADD ContactTitle varchar(200) NULL;
		INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('ContactTitle')
	END ELSE
		delete from #tblEvImportCols where column_name = 'ContactTitle'

	IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'Contact') BEGIN
		ALTER TABLE #mc_EvImport ADD Contact varchar(max) NULL;
		INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('Contact')
	END ELSE
		delete from #tblEvImportCols where column_name = 'Contact'

	IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'LocationTitle') BEGIN
		ALTER TABLE #mc_EvImport ADD LocationTitle varchar(200) NULL;
		INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('LocationTitle')
	END ELSE
		delete from #tblEvImportCols where column_name = 'LocationTitle'

	IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'Location') BEGIN
		ALTER TABLE #mc_EvImport ADD Location varchar(max) NULL;
		INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('Location')
	END ELSE
		delete from #tblEvImportCols where column_name = 'Location'

	IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'CancellationTitle') BEGIN
		ALTER TABLE #mc_EvImport ADD CancellationTitle varchar(200) NULL;
		INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('CancellationTitle')
	END ELSE
		delete from #tblEvImportCols where column_name = 'CancellationTitle'

	IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'Cancellation') BEGIN
		ALTER TABLE #mc_EvImport ADD Cancellation varchar(max) NULL;
		INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('Cancellation')
	END ELSE
		delete from #tblEvImportCols where column_name = 'Cancellation'

	IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'TravelTitle') BEGIN
		ALTER TABLE #mc_EvImport ADD TravelTitle varchar(200) NULL;
		INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('TravelTitle')
	END ELSE
		delete from #tblEvImportCols where column_name = 'TravelTitle'

	IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'Travel') BEGIN
		ALTER TABLE #mc_EvImport ADD Travel varchar(max) NULL;
		INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('Travel')
	END ELSE
		delete from #tblEvImportCols where column_name = 'Travel'

	IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'InformationTitle') BEGIN
		ALTER TABLE #mc_EvImport ADD InformationTitle varchar(200) NULL;
		INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('InformationTitle')
	END ELSE
		delete from #tblEvImportCols where column_name = 'InformationTitle'

	IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'Information') BEGIN
		ALTER TABLE #mc_EvImport ADD Information varchar(max) NULL;
		INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('Information')
	END ELSE
		delete from #tblEvImportCols where column_name = 'Information'

	IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'InternalNotes') BEGIN
		ALTER TABLE #mc_EvImport ADD InternalNotes varchar(max) NULL;
		INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('InternalNotes')
	END ELSE
		delete from #tblEvImportCols where column_name = 'InternalNotes'

	IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'ParentEventCode') BEGIN
		ALTER TABLE #mc_EvImport ADD ParentEventCode varchar(15) NULL;
		INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('ParentEventCode')
	END ELSE
		delete from #tblEvImportCols where column_name = 'ParentEventCode'


	-- add missing credit columns
	select @ASID = min(ASID) from #tblPossibleCredits
	while @ASID is not null begin
		select @crdAuthorityCode = authorityCode from #tblPossibleCredits where ASID = @ASID

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = @crdAuthorityCode + '_approval') BEGIN
			EXEC('ALTER TABLE #mc_EvImport ADD ' + @crdAuthorityCode + '_approval varchar(50) NULL;')
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES (@crdAuthorityCode + '_approval')
			update #tblPossibleCredits set missingFromFile = 1 where ASID = @ASID and missingFromFile = 0
		END ELSE BEGIN
			delete from #tblEvImportCols where column_name = @crdAuthorityCode + '_approval'
			update #tblPossibleCredits set anyInFile = 1 where ASID = @ASID and anyInFile = 0
		END

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = @crdAuthorityCode + '_status') BEGIN
			EXEC('ALTER TABLE #mc_EvImport ADD ' + @crdAuthorityCode + '_status varchar(15) NULL;')
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES (@crdAuthorityCode + '_status')
			update #tblPossibleCredits set missingFromFile = 1 where ASID = @ASID and missingFromFile = 0
		END ELSE BEGIN
			delete from #tblEvImportCols where column_name = @crdAuthorityCode + '_status'
			update #tblPossibleCredits set anyInFile = 1 where ASID = @ASID and anyInFile = 0
		END

		set @creditColName = null;
		select @creditColName = min(column_name) from #tblPossibleCreditCols where ASID = @ASID
		while @creditColName is not null begin
			IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = @creditColName) BEGIN
				EXEC('ALTER TABLE #mc_EvImport ADD ' + @creditColName + ' decimal(6,2) NULL;')
				INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES (@creditColName)
				update #tblPossibleCredits set missingFromFile = 1 where ASID = @ASID and missingFromFile = 0
			END ELSE BEGIN
				delete from #tblEvImportCols where column_name = @creditColName
				update #tblPossibleCredits set anyInFile = 1 where ASID = @ASID and anyInFile = 0
			END
			select @creditColName = min(column_name) from #tblPossibleCreditCols where ASID = @ASID and column_name > @creditColName
		end

		select @ASID = min(ASID) from #tblPossibleCredits where ASID > @ASID
	end

	-- all credit columns must be included if one column is included
	INSERT INTO #tblEvErrors (msg)
	SELECT 'Missing credit columns for authority ' + authorityCode + '. If one credit column for an authority is included, all columns for that authority must be included.'
	FROM #tblPossibleCredits
	WHERE anyInFile = 1 and missingFromFile = 1
		IF @@ROWCOUNT > 0 GOTO on_done

END TRY
BEGIN CATCH
	INSERT INTO #tblEvErrors (msg)
	VALUES ('Unable to prepare import table by adding missing columns.')

	INSERT INTO #tblEvErrors (msg)
	VALUES (left(error_message(),300))

	GOTO on_done
END CATCH


-- *************
-- extra columns 
-- *************
BEGIN TRY
	-- extra columns should stop import to prevent accidental misnamed columns
	IF EXISTS (select column_name from #tblEvImportCols) BEGIN
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'The imported file contains the extra column ' + cast(column_name as varchar(300)) + '. Remove this column from your file.' 
		FROM #tblEvImportCols  
		ORDER BY column_name
	END
END TRY
BEGIN CATCH
	INSERT INTO #tblEvErrors (msg)
	VALUES ('Unable to validate import file for extra columns.')

	INSERT INTO #tblEvErrors (msg)
	VALUES (left(error_message(),300))

	GOTO on_done
END CATCH


-- ************************
-- generate result xml file 
-- ************************
on_done:
	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblEvErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE)
	
	IF OBJECT_ID('tempdb..#tblEvOrgCols') IS NOT NULL 
		DROP TABLE #tblEvOrgCols
	IF OBJECT_ID('tempdb..#tblEvImportCols') IS NOT NULL 
		DROP TABLE #tblEvImportCols

RETURN 0
GO

CREATE PROC [dbo].[ev_importEvents]
@siteid int, 
@recordedByMemberID int, 
@ovAction char(1),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

IF OBJECT_ID('tempdb..#tblEvErrors') IS NOT NULL 
	DROP TABLE #tblEvErrors
IF OBJECT_ID('tempdb..#tblPossibleCredits') IS NOT NULL 
	DROP TABLE #tblPossibleCredits
IF OBJECT_ID('tempdb..#tblPossibleCreditCols') IS NOT NULL 
	DROP TABLE #tblPossibleCreditCols
IF OBJECT_ID('tempdb..#tblColsAdded') IS NOT NULL 
	DROP TABLE #tblColsAdded
CREATE TABLE #tblEvErrors (rowid int IDENTITY(1,1), msg varchar(300))
CREATE TABLE #tblPossibleCredits (ASID int, authorityID int, authorityCode varchar(20), anyInFile bit NOT NULL DEFAULT(0), missingFromFile bit NOT NULL DEFAULT(0))
CREATE TABLE #tblPossibleCreditCols (ASID int, column_name sysname, ASTID int)
CREATE TABLE #tblColsAdded (COLUMN_NAME sysname)

declare @orgID int
select @orgID=orgID from dbo.sites where siteID = @siteID

insert into #tblPossibleCredits (ASID, authorityID, authorityCode)
select distinct crdAS.ASID, crdA.authorityID, crdA.authorityCode
from dbo.crd_sponsors as crdS
inner join dbo.crd_authoritySponsors as crdAS on crdAS.sponsorID = crdS.sponsorID
inner join dbo.crd_authorities as crdA on crdA.authorityID = crdAS.authorityID
where crdS.orgID = @orgID

insert into #tblPossibleCreditCols (ASID, column_name, ASTID)
select crdAS.ASID, crdAS.authorityCode + '_' + crdAT.typeCode, crdAST.ASTID
from #tblPossibleCredits as crdAS
inner join dbo.crd_authorityTypes as crdAT on crdAT.authorityID = crdAS.authorityID
inner join dbo.crd_authoritySponsorTypes as crdAST on crdAST.ASID = crdAS.ASID and crdAST.typeID = crdAT.typeID


-- ensure temp table exists
IF OBJECT_ID('tempdb..#mc_EvImport') IS NULL BEGIN
	INSERT INTO #tblEvErrors (msg)
	VALUES ('Unable to locate the imported data for processing.')

	GOTO on_done
END

-- ensure all columns exist
set @importResult = null
EXEC dbo.ev_importEvents_prepTable @importResult=@importResult OUTPUT
IF @importResult.value('count(/import/errors/error)','int') > 0
	GOTO on_done

-- validate data
set @importResult = null
EXEC dbo.ev_importEvents_validate @siteID=@siteID, @importResult=@importResult OUTPUT
IF @importResult.value('count(/import/errors/error)','int') > 0
	GOTO on_done

-- import data
set @importResult = null
EXEC dbo.ev_importEvents_import @siteID=@siteID, @recordedByMemberID=@recordedByMemberID, @ovAction=@ovAction, @importResult=@importResult OUTPUT


-- cleanup
on_done:
	IF OBJECT_ID('tempdb..#tblEvErrors') IS NOT NULL 
		DROP TABLE #tblEvErrors
	IF OBJECT_ID('tempdb..#tblPossibleCredits') IS NOT NULL 
		DROP TABLE #tblPossibleCredits
	IF OBJECT_ID('tempdb..#tblPossibleCreditCols') IS NOT NULL 
		DROP TABLE #tblPossibleCreditCols

RETURN 0
GO


