USE [memberCentral]
GO

ALTER PROC [dbo].[ams_importMemberHistory]
@siteID int,
@typeID int,
@flatfile varchar(160),
@enteredByMemberID int

AS

SET NOCOUNT ON

BEGIN TRY

	declare @orgID int, @catTreeID int, @createSQL varchar(max), @cmd varchar(400)
	select @orgID = orgID from dbo.sites where siteID = @siteID
	select @catTreeID = case 
		when @typeID = 1 then dbo.fn_getCategoryTreeIDForSiteResourceID(dbo.fn_getSiteResourceIDForResourceType('MemberHistoryAdmin',@siteID))
		when @typeID = 2 then dbo.fn_getCategoryTreeIDForSiteResourceID(dbo.fn_getSiteResourceIDForResourceType('RelationshipAdmin',@siteID))
		when @typeID = 3 then dbo.fn_getCategoryTreeIDForSiteResourceID(dbo.fn_getSiteResourceIDForResourceType('HistoryAdmin',@siteID))
		end

	-- ensure files exist
	if dbo.fn_fileExists(@flatfile + '.sql') = 0 or dbo.fn_fileExists(@flatfile + '.bcp') = 0
		RAISERROR('Import files missing', 16, 1);

	-- **************
	-- read in sql create script and create global temp table
	-- **************
	select @createSQL = replace(dbo.fn_ReadFile(@flatfile + '.sql',0,1),'##xxx','##importMemberHistoryData')
	IF OBJECT_ID('tempdb..##importMemberHistoryData') IS NOT NULL
		EXEC('DROP TABLE ##importMemberHistoryData')
	EXEC(@createSQL)

	-- *******************
	-- bcp in data
	-- *******************
	select @cmd = 'bcp ##importMemberHistoryData in ' + @flatfile + '.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
	exec master..xp_cmdshell @cmd, NO_OUTPUT

	-- *******************
	-- immediately put into local temp table and drop global temp
	-- *******************
	select * into #importMemberHistoryData from ##importMemberHistoryData
	IF @typeID = 2
		ALTER TABLE #importMemberHistoryData ADD subCategory varchar(200) NULL, quantity int NULL, amount money NULL;
	IF @typeID = 3
		ALTER TABLE #importMemberHistoryData ADD quantity int NULL, amount money NULL;
	IF OBJECT_ID('tempdb..##importMemberHistoryData') IS NOT NULL
		EXEC('DROP TABLE ##importMemberHistoryData')

	-- Add index for queries below
	CREATE NONCLUSTERED INDEX [idx_importMemberHistoryData] ON #importMemberHistoryData ([membernumber] ASC);

	-- *******************
	-- process data
	-- *******************
	insert into dbo.ams_memberHistory (typeID, memberID, categoryID, subCategoryID, userDate, userEndDate, quantity, dollarAmt, description, dateEntered, enteredByMemberID, linkMemberID)
	select @typeID, m.memberID, cP.categoryID, c.categoryID, tb12.startDate, tb12.endDate, nullif(tb12.quantity,0), tb12.Amount, tb12.description, getdate(), @enteredByMemberID, mLink.memberID
	from #importMemberHistoryData as tb12 
	inner join dbo.ams_members as m on m.memberNumber = tb12.membernumber and m.orgID = @orgID and m.memberID = m.activeMemberID and m.status <> 'D'
	inner join dbo.cms_categories as cP on cP.categoryName = tb12.category and cP.categoryTreeID = @catTreeID and cP.isActive = 1 and cP.parentCategoryID is NULL
	left outer join dbo.cms_categories as c on cp.categoryID = c.parentCategoryID and c.categoryName = tb12.SubCategory and c.isActive = 1 and c.parentCategoryID is not NULL
	left outer join dbo.ams_members as mLink on mLink.memberNumber = tb12.linkedmembernumber and mLink.orgID = @orgID and mLink.memberID = mLink.activeMemberID and mLink.status <> 'D'

	-- add conditions to queue (@runSchedule=2 indicates delayed processing) 
	declare @itemGroupUID uniqueidentifier, @conditionIDList varchar(max)
	SELECT @conditionIDList = COALESCE(@conditionIDList + ',', '') + cast(vgc.conditionID as varchar(10)) 
		from dbo.ams_virtualGroupConditions as vgc
		where vgc.orgID = @orgID
		and vgc.fieldCode = 'mh_entry'
		group by vgc.conditionID
	IF @conditionIDList is not null 
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList='', @conditionIDList=@conditionIDList, @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT


	IF OBJECT_ID('tempdb..#importMemberHistoryData') IS NOT NULL
		EXEC('DROP TABLE #importMemberHistoryData')

	RETURN 0
END TRY
BEGIN CATCH
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO
