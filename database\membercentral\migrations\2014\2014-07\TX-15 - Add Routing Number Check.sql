USE membercentral
GO
CREATE FUNCTION dbo.fn_tr_checkABARoutingNumber (@RoutingNumber VARCHAR(8000))
RETURNS bit
AS
BEGIN

	DECLARE @isValid bit

	;WITH numbers AS ( select NUMBER as n from dbo.F_TABLE_NUMBER_RANGE(1,9) )
	SELECT @isValid = CASE
		WHEN LEN(@RoutingNumber)=9
			AND @RoutingNumber NOT LIKE '%[^0-9]%' --Check to make sure is all digits
			AND RIGHT(@RoutingNumber ,1) = (
				--==== Calculates check digit (Digit 9)
				SELECT 10-SUM(mv.DigitVal)%10 --Sum of individual digit values subtracted for new number ending in 0
				FROM (--==== Gets multiplied value for each digit in 3, 7, 1 pattern
					SELECT CASE 
						WHEN (t.N-1)%3 = 0 THEN SUBSTRING(@RoutingNumber,t.N,1)*3 --Digits 1,4,7
						WHEN (t.N-1)%3 = 1 THEN SUBSTRING(@RoutingNumber,t.N,1)*7 --Digits 2,5,8
						WHEN (t.N-1)%3 = 2 THEN SUBSTRING(@RoutingNumber,t.N,1)*1 --Digits 3,6
						END AS DigitVal
					FROM numbers as t
					WHERE t.N <= 8
				) as mv
			)
			THEN 1
		ELSE 0
		END

	RETURN @isValid

END
GO
