
use platformmail;
GO


update dbo.email_statuses set statusOrder=14 where statusCode='S'

update dbo.email_statuses set statusOrder=statusOrder+1 where statusOrder >= 6
insert into dbo.email_statuses (statusCode, status, statusOrder) values ('released','Processing (awaiting validation)',6)
GO

/* To prevent any potential data loss issues, you should review this script in detail before running it outside the context of the database designer.*/
CREATE TABLE dbo.email_statusApprovedFlow
	(
	statusChangeID int NOT NULL IDENTITY (1, 1),
	currentStatusID int NOT NULL,
	allowedNewStatusID int NOT NULL
	)  ON [PRIMARY]
GO
ALTER TABLE dbo.email_statusApprovedFlow ADD CONSTRAINT
	PK_email_statusApprovedFlow PRIMARY KEY CLUSTERED 
	(
	statusChangeID
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

GO
ALTER TABLE dbo.email_statusApprovedFlow ADD CONSTRAINT
	FK_email_statusApprovedFlow_email_statuses FOREIGN KEY
	(
	currentStatusID
	) REFERENCES dbo.email_statuses
	(
	statusID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.email_statusApprovedFlow ADD CONSTRAINT
	FK_email_statusApprovedFlow_email_statuses1 FOREIGN KEY
	(
	allowedNewStatusID
	) REFERENCES dbo.email_statuses
	(
	statusID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO

insert into dbo.email_statusApprovedFlow(currentStatusID, allowedNewStatusID)
select currentstatus.statusID as currentStatusID,newstatus.statusID as allowedNewStatusID
from dbo.email_statuses currentstatus
inner join dbo.email_statuses newstatus
    on currentstatus.statuscode='I'
    and newstatus.statuscode in ('Q','E','C','R')
union
select currentstatus.statusID as currentStatusID,newstatus.statusID as allowedNewStatusID
from dbo.email_statuses currentstatus
inner join dbo.email_statuses newstatus
    on currentstatus.statuscode='Q'
    and newstatus.statuscode in ('R','C','E','G')
union
select currentstatus.statusID as currentStatusID,newstatus.statusID as allowedNewStatusID
from dbo.email_statuses currentstatus
inner join dbo.email_statuses newstatus
    on currentstatus.statuscode='R'
    and newstatus.statuscode in ('Q')
union
select currentstatus.statusID as currentStatusID,newstatus.statusID as allowedNewStatusID
from dbo.email_statuses currentstatus
inner join dbo.email_statuses newstatus
    on currentstatus.statuscode='G'
    and newstatus.statuscode in ('P','C','E','R')
union
select currentstatus.statusID as currentStatusID,newstatus.statusID as allowedNewStatusID
from dbo.email_statuses currentstatus
inner join dbo.email_statuses newstatus
    on currentstatus.statuscode='P'
    and newstatus.statuscode in ('S','R','C','E','released')
union
select currentstatus.statusID as currentStatusID,newstatus.statusID as allowedNewStatusID
from dbo.email_statuses currentstatus
inner join dbo.email_statuses newstatus
    on currentstatus.statuscode='S'
    and newstatus.statuscode in ('E','C','sg_process','sg_drop','sg_deliver','sg_bounce','sg_defer','sg_open')
union
select currentstatus.statusID as currentStatusID,newstatus.statusID as allowedNewStatusID
from dbo.email_statuses currentstatus
inner join dbo.email_statuses newstatus
    on currentstatus.statuscode='released'
    and newstatus.statuscode in ('E','C','sg_process','sg_drop','sg_deliver','sg_bounce','sg_defer','sg_open')
union
select currentstatus.statusID as currentStatusID,newstatus.statusID as allowedNewStatusID
from dbo.email_statuses currentstatus
inner join dbo.email_statuses newstatus
    on currentstatus.statuscode='sg_process'
    and newstatus.statuscode in ('sg_deliver','sg_bounce','sg_open','sg_spam','sg_defer')
union
select currentstatus.statusID as currentStatusID,newstatus.statusID as allowedNewStatusID
from dbo.email_statuses currentstatus
inner join dbo.email_statuses newstatus
    on currentstatus.statuscode='sg_defer'
    and newstatus.statuscode in ('sg_deliver','sg_bounce','sg_open','sg_spam','sg_defer')
union
select currentstatus.statusID as currentStatusID,newstatus.statusID as allowedNewStatusID
from dbo.email_statuses currentstatus
inner join dbo.email_statuses newstatus
    on currentstatus.statuscode='sg_deliver'
    and newstatus.statuscode in ('sg_bounce','sg_open','sg_spam')
union
select currentstatus.statusID as currentStatusID,newstatus.statusID as allowedNewStatusID
from dbo.email_statuses currentstatus
inner join dbo.email_statuses newstatus
    on currentstatus.statuscode='sg_open'
    and newstatus.statuscode in ('sg_spam')

GO




ALTER PROC [dbo].[email_setMessageRecipientHistoryStatus]
@recipientID int,
@statusCode varchar(10),
@updateDate bit

AS

declare @emailStatusID int, @currentDate datetime
select @emailStatusID = statusID from dbo.email_statuses where statusCode = @statusCode
	IF @@ERROR <> 0 GOTO on_error
set @currentDate = getdate()

update mrh
set emailStatusID = @emailStatusID,
    dateLastUpdated = case when @updateDate = 1 then @currentDate else dateLastUpdated end
from dbo.email_messageRecipientHistory mrh
inner join dbo.email_statusApprovedFlow flow
   on flow.currentStatusID = mrh.emailStatusID
   and flow.allowedNewStatusID = @emailStatusID
   and recipientID = @recipientID
IF @@ERROR <> 0 GOTO on_error



RETURN 0

-- error exit
on_error:
	RETURN -1
GO


CREATE PROC [dbo].[email_setMessageAllRecipientHistoryStatus]
@messageID int,
@statusCode varchar(10),
@updateDate bit

AS

declare @emailStatusID int, @currentDate datetime
select @emailStatusID = statusID from dbo.email_statuses where statusCode = @statusCode
	IF @@ERROR <> 0 GOTO on_error
set @currentDate = getdate()

update mrh
set emailStatusID = @emailStatusID,
    dateLastUpdated = case when @updateDate = 1 then @currentDate else dateLastUpdated end
from dbo.email_messageRecipientHistory mrh
inner join dbo.email_statusApprovedFlow flow
   on flow.currentStatusID = mrh.emailStatusID
   and flow.allowedNewStatusID = @emailStatusID
   and messageID = @messageID
    IF @@ERROR <> 0 GOTO on_error


RETURN 0

-- error exit
on_error:
	RETURN -1
GO

CREATE TABLE dbo.email_messageRecipientHistoryTracking
	(
     mcEventID int NOT NULL IDENTITY (1, 1),
	sgEventId varchar(100) NOT NULL,
	recipientID int NOT NULL,
	ipaddress varchar(16) NULL,
	statusID int NOT NULL,
     dateAdded datetime NOT NULL,
	detail varchar(500) NULL,
     jsonResult varchar(max)
	)  ON [PRIMARY]
GO
ALTER TABLE dbo.email_messageRecipientHistoryTracking ADD CONSTRAINT
	PK_email_messageRecipientHistoryTracking PRIMARY KEY CLUSTERED 
	(
	mcEventId
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

GO
ALTER TABLE dbo.email_messageRecipientHistoryTracking ADD CONSTRAINT
	FK_email_messageRecipientHistoryTracking_email_messageRecipientHistory FOREIGN KEY
	(
	recipientID
	) REFERENCES dbo.email_messageRecipientHistory
	(
	recipientID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.email_messageRecipientHistoryTracking ADD CONSTRAINT
	FK_email_messageRecipientHistoryTracking_email_statuses FOREIGN KEY
	(
	statusID
	) REFERENCES dbo.email_statuses
	(
	statusID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
CREATE INDEX [email_messageRecipientHistoryTracking_sgEventID] ON [dbo].[email_messageRecipientHistoryTracking] (sgEventID)
GO



CREATE PROC [dbo].[email_addMessageRecipientTrackingHistory]
@sgEventId varchar(100),
@recipientID int,
@statusCode varchar(10),
@ipaddress varchar(16),
@detail varchar(500),
@jsonResult varchar(max)

AS

declare @emailStatusID int, @currentDate datetime
select @emailStatusID = statusID from dbo.email_statuses where statusCode = @statusCode
set @currentDate = getdate()

if exists (select recipientID from dbo.email_messageRecipientHistory where recipientID=@recipientID) and not exists(select sgEventId from dbo.email_messageRecipientHistoryTracking where sgEventId = @sgEventId)
BEGIN
    insert into dbo.email_messageRecipientHistoryTracking (sgEventId,recipientID, ipaddress, statusID, dateAdded,detail, jsonResult)
    values (@sgEventId,@recipientID, nullif(@ipaddress,''), @emailStatusID, @currentDate,@detail, @jsonResult)
END

RETURN 0
GO

insert into dbo.email_statuses (statusCode, status, statusOrder) values ('sg_click','Clicked', 16)
GO