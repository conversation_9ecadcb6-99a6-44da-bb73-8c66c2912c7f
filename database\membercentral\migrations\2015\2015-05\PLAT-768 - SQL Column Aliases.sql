use formbuilder
GO

ALTER PROC [dbo].[up_getExamXML]
@formIDlist varchar(max),
@enabledOnly bit

AS

-- query 1: examXML
SELECT (
	SELECT Exam.formID, Exam.formTitle, Exam.passingPct, Exam.submitBtnText, 
		Exam.formIntro, Exam.formClose, ExamSection.sectionID, ExamSection.sectionTitle, 
		ExamSection.sectionDesc, ExamQuestion.questionID, ExamQuestion.questionText, ExamQuestion.isRequired,
		ExamQuestion.displayQuestionText, ExamQuestion.isDisplayedInline, ExamQuestion.displayQuestionNumber,
		ExamQuestion.questionTypeID, isnull(qt.questiontypecode,'') as questionTypeCode, ExamQuestion.controlStyle, 
		controlField = CASE
			WHEN ExamQuestion.questionTypeID in (1,2,9,10) then 'q_' + CAST(ExamQuestion.questionID as varchar(10)) + '_' + CAST(ExamQuestion.questionTypeID as varchar(3)) + '_txt' 
			WHEN ExamQuestion.questionTypeID in (3) then 'q_' + CAST(ExamQuestion.questionID as varchar(10)) + '_' + CAST(ExamQuestion.questionTypeID as varchar(3)) + '_opt' 
			END,
		ExamQuestionOptions.optionID, 
		ExamQuestionOptions.responseText, ExamQuestionOptions.optionText, ExamQuestionOptions.isCorrect,
		ExamQuestionOptions.showInput, ExamQuestionOptions.inputText,
		controlField = CASE
			WHEN ExamQuestionOptions.showInput = 1 then 'q_' + CAST(ExamQuestion.questionID as varchar(10)) + '_' + CAST(ExamQuestion.questionTypeID as varchar(3)) + '_' + CAST(ExamQuestionOptions.optionID as varchar(10)) + '_txt' 
			END
	FROM dbo.tblForms as Exam 
	INNER JOIN dbo.tblSections as ExamSection ON ExamSection.formID = Exam.formID
	INNER JOIN dbo.tblQuestions as ExamQuestion ON ExamQuestion.sectionID = ExamSection.sectionID and ExamQuestion.isEnabled = 1
	inner join dbo.tblQuestionTypes qt on ExamQuestion.questionTypeID = qt.questionTypeID
	INNER JOIN dbo.tblOptions as ExamQuestionOptions on ExamQuestionOptions.questionID = ExamQuestion.questionID and ExamQuestionOptions.isEnabled = 1
	INNER JOIN dbo.fn_IntListToTable(@formIDlist) as Exams on Exams.intvalue = Exam.formID
	WHERE 1 = (select 
				CASE 
				WHEN @enabledOnly = 1 and isPublished = 1 and getdate() between dateStartPublish and dateEndPublish THEN 1
				WHEN @enabledOnly = 0 THEN 1
				ELSE 0
				END as Response
				FROM dbo.tblForms
				WHERE formID = Exam.formID)
	order by Exams.autoID, ExamSection.sectionOrder, ExamQuestion.questionOrder, 
		CASE WHEN ExamQuestion.randomizeOptions = 1 THEN ABS(CHECKSUM(NEWID())) 
		ELSE ExamQuestionOptions.OptionOrder
		END
	for xml auto,type,root('Exams')
) as examXML

-- query 2: examquestionTypesXML
SELECT (
	SELECT distinct ExamQuestionType.questionTypeID, ExamQuestionType.questionType, ExamQuestionType.questionTypeCode
	FROM dbo.tblQuestions as ExamQuestion 
	INNER JOIN dbo.tblOptions as ExamQuestionOptions ON ExamQuestion.questionID = ExamQuestionOptions.questionID and ExamQuestion.isEnabled = 1 and ExamQuestionOptions.isEnabled = 1
	INNER JOIN dbo.tblQuestionTypes as ExamQuestionType ON ExamQuestion.questionTypeID = ExamQuestionType.questionTypeID 
	INNER JOIN dbo.tblSections as ExamSection ON ExamQuestion.sectionID = ExamSection.sectionID 
	INNER JOIN dbo.tblForms as Exam ON ExamSection.formID = Exam.formID
	INNER JOIN dbo.fn_IntListToTable(@formIDlist) as Exams on Exams.intvalue = Exam.formID
	WHERE 1 = (select 
				CASE 
				WHEN @enabledOnly = 1 and isPublished = 1 and getdate() between dateStartPublish and dateEndPublish THEN 1
				WHEN @enabledOnly = 0 THEN 1
				ELSE 0
				END as Response
				FROM dbo.tblForms
				WHERE formID = Exam.formID)
	order by ExamQuestionType.questionTypeID
	for xml auto,type,root('ExamQuestionTypes')
) as examQuestionTypesXML
GO

ALTER PROC [dbo].[up_getFormXML]
@formID int,
@enabledOnly bit,
@includeQuestions xml,
@obeyRandom bit

AS

-- put questions for this form into a temp table
-- generate a qkey for each question used for ordering
declare @tblQ TABLE (
	qkey int NOT NULL DEFAULT ABS(CHECKSUM(NEWID())),
	questionid int,
	questiontext varchar(max), 
	questiontypeid int,
	questiontypecode varchar(50), 
	isrequired bit, 
	isdisplayedinline bit, 
	displayquestionnumber bit, 
	controlstyle varchar(30), 
	displayquestiontext bit, 
	optionresponselimit int, 
	sectionID int, 
	questionOrder int, 
	randomizeOptions bit
)

declare @mode varchar(7)
select @mode = cast(@includeQuestions.query('data(/questions/@mode)') as varchar(7))

if @mode = 'ALL'
	INSERT INTO @tblQ (questionid, questiontext, questiontypeid, questiontypecode, isrequired, isdisplayedinline, displayquestionnumber, 
		controlstyle, displayquestiontext, optionresponselimit, sectionID, questionOrder, randomizeOptions)
	SELECT q.questionid, q.questiontext, q.questiontypeid, qt.questiontypecode, q.isrequired, q.isdisplayedinline, q.displayquestionnumber, 
		q.controlstyle, q.displayquestiontext, q.optionresponselimit, q.sectionID, q.questionOrder, q.randomizeOptions
	from dbo.tblQuestions as q
	inner join dbo.tblQuestionTypes qt on q.questionTypeID = qt.questionTypeID
	inner join dbo.tblSections as s on s.sectionID = q.sectionID
	where q.isEnabled = 1
	and s.formID = @formid
else
begin
	-- parse xml and get sections and numquestions	
	declare @tmpXmls table (sectionid int, numquestions int)
	INSERT INTO @tmpXmls (sectionID, numquestions)
	SELECT sections.section.value('@sectionid','int') as sectionid, sections.section.value('@numquestions','int') as numquestions
	FROM @includeQuestions.nodes('/questions/section') as sections(section)	

	-- loop over table in order to get TOP x for each section
	declare @minSectionID int, @numquestions int
	SELECT @minSectionID = min(sectionid) from @tmpXmls
	while @minSectionID is not null
	begin
		select @numquestions = numquestions from @tmpXmls where sectionID = @minSectionID

		INSERT INTO @tblQ (questionid, questiontext, questiontypeid, questiontypecode, isrequired, isdisplayedinline, displayquestionnumber, 
			controlstyle, displayquestiontext, optionresponselimit, sectionID, questionOrder, randomizeOptions)
		SELECT TOP (@numquestions) q.questionid, q.questiontext, q.questiontypeid, qt.questiontypecode, q.isrequired, q.isdisplayedinline, q.displayquestionnumber, 
			q.controlstyle, q.displayquestiontext, q.optionresponselimit, q.sectionID, q.questionOrder, q.randomizeOptions
		from dbo.tblQuestions as q
		inner join dbo.tblQuestionTypes qt on q.questionTypeID = qt.questionTypeID
		inner join dbo.tblSections as s on s.sectionID = q.sectionID
		where q.isEnabled = 1
		and s.formID = @formid
		and s.sectionID = @minSectionID
		ORDER BY ABS(CHECKSUM(NEWID()))

		SELECT @minSectionID = min(sectionid) from @tmpXmls where sectionid > @minSectionID
	end
end

-- put options for this form into a temp table
-- generate a okey for each option used for ordering
declare @tblO TABLE (
	okey int NOT NULL DEFAULT ABS(CHECKSUM(NEWID())),
	optionid int,
	questionid int,
	optiontext varchar(1000), 
	showinput bit, 
	inputtext varchar(500),
	optionorder int
)
insert into @tblO (optionid, questionid, optiontext, showinput, inputtext, optionorder)
select o.optionid, o.questionid, o.optiontext, o.showinput, o.inputtext, o.optionorder
from dbo.tblOptions as o
inner join @tblQ as q on q.questionid = o.questionid
where o.isEnabled = 1


-- query 1: formXML
SELECT isnull((
	select form.formid, form.formtitle, form.submitbtntext, form.formintro, form.formclose, form.alloweditresponses, form.passingpct, 
		isnull(ft.formType,'') as formtype, 	
		section.sectionid, section.sectiontitle, section.sectiondesc,
		Question.questionid, Question.QKey, Question.questiontext, Question.questiontypeid, Question.questiontypecode,  Question.isrequired, Question.isdisplayedinline,
		Question.displayquestionnumber, Question.controlstyle, Question.displayquestiontext, Question.optionresponselimit,
		controlfield = CASE
			WHEN Question.questionTypeID in (1,2,9,10) then 'q_' + CAST(Question.questionID as varchar(10)) + '_' + CAST(Question.questionTypeID as varchar(3)) + '_txt' 
			WHEN Question.questionTypeID in (3,4,5,6) then 'q_' + CAST(Question.questionID as varchar(10)) + '_' + CAST(Question.questionTypeID as varchar(3)) + '_opt' 
			END,
		options.optionid, options.optiontext, options.showinput, options.inputtext, 
		controlfield = CASE
			WHEN question.questiontypeid in (7,8) then 'q_' + CAST(question.questionid as varchar(10)) + '_' + CAST(question.questiontypeid as varchar(3)) + '_' + CAST(options.optionid as varchar(10)) + '_opt' 
			WHEN options.showinput = 1 then 'q_' + CAST(question.questionid as varchar(10)) + '_' + CAST(question.questiontypeid as varchar(3)) + '_' + CAST(options.optionid as varchar(10)) + '_txt' 
			END,
		optionsx.optionid, optionsx.optiontext,
		controlfield = CASE
			WHEN question.questionTypeID in (11) then 'q_' + CAST(Question.questionID as varchar(10)) + '_' + CAST(Question.questionTypeID as varchar(3)) + '_' + CAST(Options.optionID as varchar(10)) + '_' + CAST(OptionsX.optionID as varchar(10)) +  '_txt' 
			END
	from dbo.tblForms AS form
	inner join dbo.tblFormTypes as ft on ft.formtypeid = form.formtypeid
	inner join dbo.tblSections AS section ON form.formid = section.formid
	inner join @tblQ as question on section.sectionid = question.sectionid
	left outer join @tblO AS options ON options.questionid = question.questionid
	left outer join dbo.tblOptionsX as optionsx on optionsx.questionid = question.questionid
	where form.formid = @formid
	AND 1 = (select 
				CASE 
				WHEN @enabledOnly = 1 and isPublished = 1 and getdate() between dateStartPublish and dateEndPublish THEN 1
				WHEN @enabledOnly = 0 THEN 1
				ELSE 0
				END as Response
				FROM dbo.tblForms
				WHERE formID = Form.formID)
	order by section.sectionOrder,
		CASE WHEN @obeyRandom = 1 and section.randomizeQuestions = 1 THEN question.QKey ELSE Question.questionOrder END,
		CASE WHEN @obeyRandom = 1 and question.randomizeOptions = 1 THEN options.okey ELSE Options.OptionOrder END,
		OptionsX.OptionOrder
	for xml auto,type
),'<form/>') as formXML

-- query 2: formQuestionTypesXML
SELECT isnull((
	SELECT distinct QuestionType.questiontypeid, QuestionType.questiontype, QuestionType.questiontypecode
	FROM @tblQ as question 
	INNER JOIN dbo.tblQuestionTypes as questiontype ON Question.questionTypeID = QuestionType.questionTypeID 
	order by QuestionType.questionTypeID
	for xml auto,type,root('questiontypes')
),'<questiontypes/>') as QuestionTypesXML
GO

ALTER PROC [dbo].[up_getFormXMLBasedOnResponseID]
@formID int,
@enabledOnly bit,
@responseID int

AS

IF OBJECT_ID('tempdb..#tmpQuestions') IS NOT NULL 
      DROP TABLE #tmpQuestions
IF OBJECT_ID('tempdb..#tmpOptions') IS NOT NULL 
      DROP TABLE #tmpOptions
IF OBJECT_ID('tempdb..#tmpOptionsx') IS NOT NULL 
      DROP TABLE #tmpOptionsx

-- all questions and their versions
;WITH CTE 
AS (
      SELECT q.questionID, q.questionTypeID, q.questionText, q.formerQuestionID, q.questionID as useQuestionID
      FROM dbo.tblQuestions as q 
      inner join dbo.tblSections as s on s.sectionID = q.sectionID
      where s.formID = @formID
      and q.isEnabled = 1
            UNION ALL
      SELECT q.questionID, q.questionTypeID, q.questionText, q.formerQuestionID, CTE.useQuestionID
      FROM dbo.tblQuestions as q
      INNER JOIN CTE ON q.questionID = CTE.formerQuestionID
)
select CTE.questionID, CTE.useQuestionID, s.sectionID, CTE.questionTypeID, q.questionText as useQuestionText, 
	q.randomizeOptions, q.questionOrder, q.isRequired, q.isDisplayedInline, q.displayQuestionNumber, q.isEnabled, q.displayQuestionText,
	q.controlStyle, q.optionResponseLimit
into #tmpQuestions
from CTE
inner join dbo.tblQuestions as q on q.questionID = CTE.useQuestionID
inner join dbo.tblSections as s on s.sectionID = q.sectionID

-- all options and their versions
;WITH CTE 
AS (
      SELECT o.optionID, o.optionText, o.formerOptionID, o.optionID as useOptionID
      FROM formBuilder.dbo.tblOptions o
      inner join formbuilder.dbo.tblQuestions as q on q.questionID = o.questionID
      inner join formbuilder.dbo.tblSections as s on s.sectionID = q.sectionID
      where s.formID = @formID
      and o.isEnabled = 1
            UNION ALL
      SELECT o.optionID, o.optionText, o.formerOptionID, CTE.useOptionID
      FROM formBuilder.dbo.tblOptions as o
      INNER JOIN CTE ON o.optionID = CTE.formerOptionID
)
select CTE.optionID, CTE.useOptionID, o.optionText as useOptionText, o.questionID, o.optionOrder, o.showinput, o.inputtext, o.isEnabled
into #tmpOptions
from CTE
inner join formBuilder.dbo.tblOptions as o on o.optionID = CTE.useOptionID

-- all optionsX and their versions
;WITH CTE 
AS (
      SELECT o.optionID, o.optionText, o.formerOptionID, o.optionID as useOptionID
      FROM formBuilder.dbo.tblOptionsX o
      inner join formbuilder.dbo.tblQuestions as q on q.questionID = o.questionID
      inner join formbuilder.dbo.tblSections as s on s.sectionID = q.sectionID
      where s.formID = @formID
      and o.isEnabled = 1
            UNION ALL
      SELECT o.optionID, o.optionText, o.formerOptionID, CTE.useOptionID
      FROM formBuilder.dbo.tblOptionsX as o
      INNER JOIN CTE ON o.optionID = CTE.formerOptionID
)
select CTE.optionID, CTE.useOptionID, o.optionText as useOptionText, o.questionID, o.optionOrder, o.isEnabled
into #tmpOptionsX
from CTE
inner join formBuilder.dbo.tblOptionsX as o on o.optionID = CTE.useOptionID

-- query 1: formXML
SELECT isnull((
	select form.formid, form.formtitle, form.submitbtntext, form.formintro, form.formclose, form.alloweditresponses,
		section.sectionid, section.sectiontitle, section.sectiondesc,
		Question.useQuestionID as questionid, Question.useQuestionText as questiontext, Question.questiontypeid, isnull(qt.questiontypecode,'') as questiontypecode, Question.isrequired, Question.isdisplayedinline,
		Question.displayquestionnumber, Question.controlstyle, Question.displayquestiontext, Question.optionresponselimit,
		controlfield = CASE
			WHEN Question.questionTypeID in (1,2,9,10) then 'q_' + CAST(Question.useQuestionID as varchar(10)) + '_' + CAST(Question.questionTypeID as varchar(3)) + '_txt' 
			WHEN Question.questionTypeID in (3,4,5,6) then 'q_' + CAST(Question.useQuestionID as varchar(10)) + '_' + CAST(Question.questionTypeID as varchar(3)) + '_opt' 
			END,
		options.useoptionid as optionid, options.useoptiontext as optiontext, options.showinput, options.inputtext, 
		controlfield = CASE
			WHEN question.questiontypeid in (7,8) then 'q_' + CAST(question.useQuestionID as varchar(10)) + '_' + CAST(question.questiontypeid as varchar(3)) + '_' + CAST(options.useoptionid as varchar(10)) + '_opt' 
			WHEN options.showinput = 1 then 'q_' + CAST(question.useQuestionID as varchar(10)) + '_' + CAST(question.questiontypeid as varchar(3)) + '_' + CAST(options.useoptionid as varchar(10)) + '_txt' 
			END,
		optionsx.useoptionid as optionid, optionsx.useoptiontext as optiontext,
		controlfield = CASE
			WHEN question.questionTypeID in (11) then 'q_' + CAST(Question.useQuestionID as varchar(10)) + '_' + CAST(Question.questionTypeID as varchar(3)) + '_' + CAST(Options.useoptionID as varchar(10)) + '_' + CAST(OptionsX.useoptionID as varchar(10)) +  '_txt' 
			END,
		section.sectionOrder, Question.questionOrder, Options.OptionOrder, OptionsX.OptionOrder
	from dbo.tblForms AS form
	inner join dbo.tblSections AS section ON form.formid = section.formid
	inner join #tmpQuestions as question on section.sectionid = question.sectionid
	inner join dbo.tblQuestionTypes qt on Question.questionTypeID = qt.questionTypeID
	left outer join dbo.tblResponseDetails as rd on rd.questionid = question.questionid
	left outer join #tmpOptions AS options ON options.questionid = question.usequestionid AND options.isEnabled = 1
	left outer join #tmpOptionsX as optionsx on optionsx.questionid = question.usequestionid AND optionsx.isEnabled = 1
	where form.formid = @formid
	and rd.responseID = @responseID	
	AND question.isEnabled = 1
	AND 1 = (select 
				CASE 
				WHEN @enabledOnly = 1 and isPublished = 1 and getdate() between dateStartPublish and dateEndPublish THEN 1
				WHEN @enabledOnly = 0 THEN 1
				ELSE 0
				END as Response
				FROM dbo.tblForms
				WHERE formID = Form.formID)
	group by
		form.formid, form.formtitle, form.submitbtntext, form.formintro, form.formclose, form.alloweditresponses
		,section.sectionid, section.sectiontitle, section.sectiondesc
		, Question.useQuestionID, Question.usequestiontext, Question.questiontypeid, isnull(qt.questiontypecode,''), Question.isrequired, Question.isdisplayedinline,
		Question.displayquestionnumber, Question.controlstyle, Question.displayquestiontext, Question.optionresponselimit,
		CASE
			WHEN Question.questionTypeID in (1,2,9,10) then 'q_' + CAST(Question.useQuestionID as varchar(10)) + '_' + CAST(Question.questionTypeID as varchar(3)) + '_txt' 
			WHEN Question.questionTypeID in (3,4,5,6) then 'q_' + CAST(Question.useQuestionID as varchar(10)) + '_' + CAST(Question.questionTypeID as varchar(3)) + '_opt' 
			END
		, options.useoptionid, options.useoptiontext, options.showinput, options.inputtext
		, CASE
			WHEN question.questiontypeid in (7,8) then 'q_' + CAST(question.useQuestionID as varchar(10)) + '_' + CAST(question.questiontypeid as varchar(3)) + '_' + CAST(options.useoptionid as varchar(10)) + '_opt' 
			WHEN options.showinput = 1 then 'q_' + CAST(question.useQuestionID as varchar(10)) + '_' + CAST(question.questiontypeid as varchar(3)) + '_' + CAST(options.useoptionid as varchar(10)) + '_txt' 
			END
		, optionsx.useoptionid, optionsx.useoptiontext, 
		 CASE
			WHEN question.questionTypeID in (11) then 'q_' + CAST(Question.useQuestionID as varchar(10)) + '_' + CAST(Question.questionTypeID as varchar(3)) + '_' + CAST(Options.useoptionID as varchar(10)) + '_' + CAST(OptionsX.useoptionID as varchar(10)) +  '_txt' 
			END,
		section.sectionOrder, Question.questionOrder, Options.OptionOrder, OptionsX.OptionOrder
	order by section.sectionOrder, Question.questionOrder, Options.OptionOrder, OptionsX.OptionOrder
	for xml auto,type
),'<form/>') as formXML

-- query 2: formQuestionTypesXML
SELECT isnull((
	SELECT distinct QuestionType.questiontypeid, QuestionType.questiontype,QuestionType.questiontypecode
	FROM dbo.tblQuestions as question 
	INNER JOIN dbo.tblQuestionTypes as questiontype ON Question.questionTypeID = QuestionType.questionTypeID 
	INNER JOIN dbo.tblResponseDetails as rd on rd.questionid = question.questionid
	WHERE rd.responseID = @responseID
	order by QuestionType.questionTypeID
	for xml auto,type,root('questiontypes')
),'<questiontypes/>') as QuestionTypesXML

IF OBJECT_ID('tempdb..#tmpQuestions') IS NOT NULL 
      DROP TABLE #tmpQuestions
IF OBJECT_ID('tempdb..#tmpOptions') IS NOT NULL 
      DROP TABLE #tmpOptions
IF OBJECT_ID('tempdb..#tmpOptionsx') IS NOT NULL 
      DROP TABLE #tmpOptionsx
GO


use trialsmith
GO
ALTER Function [dbo].[fn_Account_possibleMatches] (
	@depomemberdataid int
) 
returns @tmpPossibleMatches table (
	SourceID int,
	depoMemberDataID int,
	LastName varchar(100),
	FirstName varchar(100),
	username varchar(50),	
	BillingFirm varchar(200),
	BillingState  varchar(50),
	TLAMemberState varchar(50),
	membertype varchar(50),
	signupOrgCode varchar(10),
	renewalDate datetime,
	status varchar(10),
	isFinal bit
)
AS

begin

	-- if depo is 0
	IF @depomemberdataid = 0
		RETURN

	-- get depo data
	DECLARE @depoEmail varchar(100)
	DECLARE @depoLastName varchar(100)
	DECLARE @depoFirstName varchar(100)
	DECLARE @depoTLAMemberState varchar(50)
	
	SELECT	@depoEmail = case when email is null or len(email) = 0 then 'N/A NO EMAIL' else email end,
			@depoLastName = case when len(LastName) = 0 then 'N/A NO LASTNAME' else lastname end,
			@depoFirstName = case when len(FirstName) = 0 then 'N/A NO FIRSTNAME' else FirstName end,
			@depoTLAMemberState = TLAMemberState
	from depoMemberData WHERE depoMemberDataID = @depomemberdataid

	-- get everyone with the same email
	INSERT INTO @tmpPossibleMatches (SourceID, depomemberdataID, LastName, FirstName, Username, BillingFirm, BillingState, TLAMemberState, membertype, signuporgcode, renewalDate, status, isFinal)
	SELECT m.SourceID, m.depomemberdataID, m.LastName, m.FirstName, m.username, m.BillingFirm, m.BillingState, m.TLAMemberState, mt.membertype, 
		signuporgcode = CASE 
		WHEN len(m.signuporgcode) > 0 THEN m.signuporgcode
		ELSE 'TrialSmith'
		END, m.renewalDate, 
		Status = CASE 
		WHEN m.Pending = 0 THEN 'Pending'
		WHEN m.MailCode = 'I' THEN 'Inactive'
		WHEN m.MailCode = 'A' THEN 'Active'
		ELSE m.MailCode
		END, 0
	FROM dbo.depomemberdata m 
	INNER JOIN dbo.membertype mt ON m.membertype = mt.membertypeID
	WHERE m.depomemberdataID <> @depomemberdataid
	and m.Email = @depoEmail

		-- FINAL: same email / name
		INSERT INTO @tmpPossibleMatches (SourceID, depomemberdataID, LastName, FirstName, username, BillingFirm, BillingState, TLAMemberState, membertype, signuporgcode, renewalDate, status, isFinal)
		select SourceID, depomemberdataID, LastName, FirstName, username, BillingFirm, BillingState, TLAMemberState, membertype, signuporgcode, renewalDate, status, 1
		from @tmpPossibleMatches
		where LastName = @depoLastName
		and FirstName = @depoFirstName
		and isFinal = 0

		-- FINAL: similar name / same email
		INSERT INTO @tmpPossibleMatches (SourceID, depomemberdataID, LastName, FirstName, username, BillingFirm, BillingState, TLAMemberState, membertype, signuporgcode, renewalDate, status, isFinal)
		select tmp.SourceID, tmp.depomemberdataID, tmp.LastName, tmp.FirstName, tmp.username, tmp.BillingFirm, tmp.BillingState, tmp.TLAMemberState, tmp.membertype, tmp.signuporgcode, tmp.renewalDate, tmp.status, 1
		from @tmpPossibleMatches tmp
		where tmp.LastName = @depoLastName
		and left(tmp.FirstName,1) = left(@depoFirstName,1)
		and tmp.isFinal = 0

		-- FINAL: same email only
		INSERT INTO @tmpPossibleMatches (SourceID, depomemberdataID, LastName, FirstName, username, BillingFirm, BillingState, TLAMemberState, membertype, signuporgcode, renewalDate, status, isFinal)
		select tmp.SourceID, tmp.depomemberdataID, tmp.LastName, tmp.FirstName, tmp.username, tmp.BillingFirm, tmp.BillingState, tmp.TLAMemberState, tmp.membertype, tmp.signuporgcode, tmp.renewalDate, tmp.status, 1
		from @tmpPossibleMatches tmp
		where tmp.isFinal = 0

		-- clear @tmpPossibleMatches
		DELETE FROM @tmpPossibleMatches where isFinal = 0

	-- get everyone with the same last name
	INSERT INTO @tmpPossibleMatches (SourceID, depomemberdataID, LastName, FirstName, username, BillingFirm, BillingState, TLAMemberState, membertype, signuporgcode, renewalDate, status, isFinal)
	SELECT m.SourceID, m.depomemberdataID, m.LastName, m.FirstName, m.username, m.BillingFirm, m.BillingState, m.TLAMemberState, mt.membertype, 
		signuporgcode = CASE 
		WHEN len(m.signuporgcode) > 0 THEN m.signuporgcode
		ELSE 'TrialSmith'
		END, m.renewalDate, 
		Status = CASE 
		WHEN m.Pending = 0 THEN 'Pending'
		WHEN m.MailCode = 'I' THEN 'Inactive'
		WHEN m.MailCode = 'A' THEN 'Active'
		ELSE m.MailCode
		END, 0
	FROM dbo.depomemberdata m 
	INNER JOIN dbo.membertype mt ON m.membertype = mt.membertypeID
	WHERE m.depomemberdataID <> @depomemberdataid
	and m.LastName = @depoLastName

		-- FINAL: same name
		INSERT INTO @tmpPossibleMatches (SourceID, depomemberdataID, LastName, FirstName, username ,BillingFirm, BillingState, TLAMemberState, membertype, signuporgcode, renewalDate, status, isFinal)
		select tmp.SourceID, tmp.depomemberdataID, tmp.LastName, tmp.FirstName, tmp.username, tmp.BillingFirm, tmp.BillingState, tmp.TLAMemberState, tmp.membertype, tmp.signuporgcode, tmp.renewalDate, tmp.status, 1
		from @tmpPossibleMatches tmp
		where tmp.FirstName = @depoFirstName
		and tmp.isFinal = 0

		-- FINAL: same last name and association
		INSERT INTO @tmpPossibleMatches (SourceID, depomemberdataID, LastName, FirstName, username, BillingFirm, BillingState, TLAMemberState, membertype, signuporgcode, renewalDate, status, isFinal)
		select tmp.SourceID, tmp.depomemberdataID, tmp.LastName, tmp.FirstName, tmp.username, tmp.BillingFirm, tmp.BillingState, tmp.TLAMemberState, tmp.membertype, tmp.signuporgcode, tmp.renewalDate, tmp.status, 1
		from @tmpPossibleMatches tmp
		where tmp.tlaMemberState = @depoTLAMemberState
		and tmp.isFinal = 0

		-- FINAL: similar name
		INSERT INTO @tmpPossibleMatches (SourceID, depomemberdataID, LastName, FirstName, username, BillingFirm, BillingState, TLAMemberState, membertype, signuporgcode, renewalDate, status, isFinal)
		select tmp.SourceID, tmp.depomemberdataID, tmp.LastName, tmp.FirstName, tmp.username, tmp.BillingFirm, tmp.BillingState, tmp.TLAMemberState, tmp.membertype, tmp.signuporgcode, tmp.renewalDate, tmp.status, 1
		from @tmpPossibleMatches tmp
		where left(tmp.FirstName,1) = left(@depoFirstName,1)
		and tmp.isFinal = 0	

	-- clear @tmpPossibleMatches
	DELETE FROM @tmpPossibleMatches where isFinal = 0

	RETURN
end
GO

ALTER PROCEDURE [dbo].[account_PossibleMatches]
@depomemberdataid int = 0

AS

-- if depo is 0
IF @depomemberdataid = 0
BEGIN
	select 0 as MatchCount
	from depoMemberData
	where 0 = 1

	RETURN 0
END

-- get depo data
DECLARE @depoEmail varchar(100)
DECLARE @depoLastName varchar(100)
DECLARE @depoFirstName varchar(100)
DECLARE @depoTLAMemberState varchar(50)
SELECT @depoEmail = Email from depoMemberData WHERE depoMemberDataID = @depomemberdataid
IF @depoEmail is null or LEN(@depoEmail) = 0
	SELECT @depoEmail = 'N/A NO EMAIL'
SELECT @depoLastName = LastName from depoMemberData WHERE depoMemberDataID = @depomemberdataid
IF LEN(@depoLastName) = 0
	SELECT @depoLastName = 'N/A NO LASTNAME'
SELECT @depoFirstName = FirstName from depoMemberData WHERE depoMemberDataID = @depomemberdataid
IF LEN(@depoFirstName) = 0
	SELECT @depoFirstName = 'N/A NO FIRSTNAME'
SELECT @depoTLAMemberState = TLAMemberState from depoMemberData WHERE depoMemberDataID = @depomemberdataid

-- create temp table
DECLARE @tmpPossibleMatches TABLE (
	SourceID int,
	depoMemberDataID int,
	LastName varchar(100),
	FirstName varchar(100),
	BillingFirm varchar(200),
	BillingState  varchar(50),
	TLAMemberState varchar(50),
	membertype varchar(50),
	signupOrgCode varchar(10)
)
-- create temp table for results
DECLARE @tmpPossibleMatchesFinal TABLE (
	SourceID int,
	depoMemberDataID int,
	LastName varchar(100),
	FirstName varchar(100),
	BillingFirm varchar(200),
	BillingState  varchar(50),
	TLAMemberState varchar(50),
	membertype varchar(50),
	signupOrgCode varchar(10)
)


-- get everyone with the same email
INSERT INTO @tmpPossibleMatches (SourceID, depomemberdataID, LastName, FirstName, BillingFirm, BillingState, TLAMemberState, membertype, signuporgcode)
SELECT m.SourceID, m.depomemberdataID, m.LastName, m.FirstName, m.BillingFirm, m.BillingState, m.TLAMemberState, mt.membertype, 
	signuporgcode = CASE 
	WHEN len(m.signuporgcode) > 0 THEN m.signuporgcode
	ELSE 'TrialSmith'
	END
FROM depomemberdata m 
INNER JOIN membertype mt ON m.membertype = mt.membertypeID
WHERE m.depomemberdataID <> @depomemberdataid
and m.Email = @depoEmail

	-- FINAL: same email / name
	INSERT INTO @tmpPossibleMatchesFinal (SourceID, depomemberdataID, LastName, FirstName, BillingFirm, BillingState, TLAMemberState, membertype, signuporgcode)
	select SourceID, depomemberdataID, LastName, FirstName, BillingFirm, BillingState, TLAMemberState, membertype, signuporgcode
	from @tmpPossibleMatches
	where LastName = @depoLastName
	and FirstName = @depoFirstName

	-- FINAL: similar name / same email
	INSERT INTO @tmpPossibleMatchesFinal (SourceID, depomemberdataID, LastName, FirstName, BillingFirm, BillingState, TLAMemberState, membertype, signuporgcode)
	select tmp.SourceID, tmp.depomemberdataID, tmp.LastName, tmp.FirstName, tmp.BillingFirm, tmp.BillingState, tmp.TLAMemberState, tmp.membertype, tmp.signuporgcode
	from @tmpPossibleMatches tmp
	where tmp.LastName = @depoLastName
	and left(tmp.FirstName,1) = left(@depoFirstName,1)

	-- FINAL: same email only
	INSERT INTO @tmpPossibleMatchesFinal (SourceID, depomemberdataID, LastName, FirstName, BillingFirm, BillingState, TLAMemberState, membertype, signuporgcode)
	select tmp.SourceID, tmp.depomemberdataID, tmp.LastName, tmp.FirstName, tmp.BillingFirm, tmp.BillingState, tmp.TLAMemberState, tmp.membertype, tmp.signuporgcode
	from @tmpPossibleMatches tmp

-- clear @tmpPossibleMatches
DELETE FROM @tmpPossibleMatches

-- get everyone with the same last name
INSERT INTO @tmpPossibleMatches (SourceID, depomemberdataID, LastName, FirstName, BillingFirm, BillingState, TLAMemberState, membertype, signuporgcode)
SELECT m.SourceID, m.depomemberdataID, m.LastName, m.FirstName, m.BillingFirm, m.BillingState, m.TLAMemberState, mt.membertype, 
	signuporgcode = CASE 
	WHEN len(m.signuporgcode) > 0 THEN m.signuporgcode
	ELSE 'TrialSmith'
	END
FROM depomemberdata m 
INNER JOIN membertype mt ON m.membertype = mt.membertypeID
WHERE m.depomemberdataID <> @depomemberdataid
and m.LastName = @depoLastName

	-- FINAL: same name
	INSERT INTO @tmpPossibleMatchesFinal (SourceID, depomemberdataID, LastName, FirstName, BillingFirm, BillingState, TLAMemberState, membertype, signuporgcode)
	select tmp.SourceID, tmp.depomemberdataID, tmp.LastName, tmp.FirstName, tmp.BillingFirm, tmp.BillingState, tmp.TLAMemberState, tmp.membertype, tmp.signuporgcode
	from @tmpPossibleMatches tmp
	where tmp.FirstName = @depoFirstName

	-- FINAL: same last name and association
	INSERT INTO @tmpPossibleMatchesFinal (SourceID, depomemberdataID, LastName, FirstName, BillingFirm, BillingState, TLAMemberState, membertype, signuporgcode)
	select tmp.SourceID, tmp.depomemberdataID, tmp.LastName, tmp.FirstName, tmp.BillingFirm, tmp.BillingState, tmp.TLAMemberState, tmp.membertype, tmp.signuporgcode
	from @tmpPossibleMatches tmp
	where tmp.tlaMemberState = @depoTLAMemberState

	-- FINAL: similar name
	INSERT INTO @tmpPossibleMatchesFinal (SourceID, depomemberdataID, LastName, FirstName, BillingFirm, BillingState, TLAMemberState, membertype, signuporgcode)
	select tmp.SourceID, tmp.depomemberdataID, tmp.LastName, tmp.FirstName, tmp.BillingFirm, tmp.BillingState, tmp.TLAMemberState, tmp.membertype, tmp.signuporgcode
	from @tmpPossibleMatches tmp
	where left(tmp.FirstName,1) = left(@depoFirstName,1)

-- return query
SELECT count(*) as MatchCount, count(*)*100/7 as MatchPct, SourceID, depomemberdataID, LastName, FirstName, BillingFirm, BillingState, TLAMemberState, membertype, signuporgcode
FROM @tmpPossibleMatchesFinal
GROUP BY SourceID, depomemberdataID, LastName, FirstName, BillingFirm, BillingState, TLAMemberState, membertype, signuporgcode
ORDER BY MatchCount DESC, LastName, FirstName, depomemberdataID desc
GO

ALTER PROCEDURE [dbo].[knowx_checkRecords]
@xmlText text
 
as
 
DECLARE @iDoc INT
EXEC sp_xml_preparedocument @iDoc OUTPUT, @xmlText
                       
-- insert into temp table so we can get the writer
CREATE TABLE #tmpKnowX (
	autoID int IDENTITY,
	custid varchar(30),
	ts_depoMemberDataID int DEFAULT(0),
	date datetime,
	price numeric (18,2),
	Product varchar(100),
	Search varchar(100),
	ts_TransDesc varchar(600) NULL,
	ts_tax numeric(19,2) DEFAULT(0),
	ts_transactionID int DEFAULT(0)
)

INSERT INTO #tmpKnowX (custid, date, price, product, search)
SELECT custid, date, price, product, search
FROM OPENXML(@iDoc,'//billing-info/lineitem',2)
	WITH (custid varchar(30) 'custid', 
		date datetime 'date',
		price numeric(18,2) 'price', 
		product varchar(100) 'product', 
		search varchar(100) 'search')
 
EXEC sp_xml_removedocument @iDoc
 
-- update with trans Description
UPDATE #tmpKnowX
SET ts_TransDesc = 'PublicRecordsSearch: ' + product + ' ' + replace(search,'"',' ')
WHERE isnumeric(custid) = 1

-- update depoID
UPDATE tmp
SET tmp.ts_depoMemberDataID = d.depoMemberDataID
FROM #tmpKnowX tmp
INNER JOIN depoMemberData d ON d.sourceID = tmp.custID
WHERE isnumeric(tmp.custid) = 1

-- update tax
UPDATE tmp
SET tmp.ts_tax = price * 0.0825
FROM #tmpKnowX tmp
INNER JOIN depoMemberData d ON d.sourceID = tmp.custID
WHERE isnumeric(tmp.custid) = 1 AND d.billingstate = 'TX'

-- update transactionID
DECLARE @minautoID int
SELECT @minautoID = min(autoID) from #tmpKnowX
WHILE (@minautoID IS NOT NULL)
BEGIN
	UPDATE #tmpKnowX
	SET ts_transactionID = IsNull((
		SELECT TOP 1 t.transactionID 
		from depoTransactions t 
		where t.depoMemberDataID = #tmpKnowX.ts_depoMemberDataID
		AND t.Description = #tmpKnowX.ts_TransDesc
		AND t.AmountBilled = #tmpKnowX.Price
		AND t.DatePurchased = cast (#tmpKnowX.Date as smalldatetime)
		AND t.AccountCode = '5003'
		AND t.DeliveryType = 4
		AND t.transactionID NOT IN (select distinct ts_transactionID from #tmpKnowX)
		), 0)
	WHERE isnumeric(custid) = 1 and autoID = @minautoID
	

	SELECT @minautoID = min(autoID) from #tmpKnowX where autoID > @minautoID
END


-- return table
SELECT *, reason = CASE
	WHEN ts_depoMemberDataID = 0 THEN 'No matching depoMemberDataID'
	WHEN ts_transactionID = 0 THEN 'No matching transactoinID'
	ELSE 'Matching transactionID'
	END
FROM  #tmpKnowX
order by date, custID

-- drop temp table
DROP TABLE #tmpKnowX
GO


