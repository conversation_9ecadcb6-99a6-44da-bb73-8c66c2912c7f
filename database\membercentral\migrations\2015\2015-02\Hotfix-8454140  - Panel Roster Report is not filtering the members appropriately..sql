use membercentral
GO

declare @tmpParentPanelTbl as table(panelMemberID int,panelID int , statusID int, memberID int)
declare @tmpsubPanelTbl as table( panelName varchar(255), panelID int , panelParentID int)
declare @tmpMemberSubPanelTbl as table(panelMemberID int,panelID int , statusID int, memberID int, panelParentID int)

-- this query is to get parent panels and their statuses to later be used for updating sub-panels' statuses
insert into @tmpParentPanelTbl
select pm.panelMemberID, pm.panelID, pm.statusID, pm.memberID
from
	ref_panelMembers pm
	inner join ref_panels p on
		p.panelID = pm.panelID
		and p.panelParentID is null
order by
	p.name

-- select * from @tmpParentPanelTbl

-- this query is to get sub-panels to later be used for getting their  statuses
insert into @tmpsubPanelTbl
select distinct p.name, p.panelID, panelParentID
from
	@tmpParentPanelTbl tt 
	inner join ref_panels p on
		p.panelParentID = tt.panelID
order by
	p.name

-- select * from @tmpsubPanelTbl

-- this query is to get sub-panels' panelMemberIDs in order to later be used for updating statuses
insert into @tmpMemberSubPanelTbl 
select pm.panelMemberID, pm.panelID , pm.statusID, pm.memberID, sp.panelParentID
from
	ref_panelMembers pm
	inner join @tmpsubPanelTbl sp on
		sp.panelID = pm.panelID
order by
	pm.memberID	

-- select * from @tmpMemberSubPanelTbl

-- this query is to test results prior actually updating the mismatching records.
select 
	sp.panelMemberID, sp.panelID as subPanelID , sp.statusID as subPanelStatusID, 
	sp.memberID as subPanelMemberID, sp.panelParentID,
	p.memberID as parentMemberID, p.statusID as parentStatusID
from 
	@tmpMemberSubPanelTbl sp
	inner join @tmpParentPanelTbl p on
		p.panelID = sp.panelParentID
		and p.memberID = sp.memberID
		and p.statusID <> sp.statusID
	inner join ref_panelMembers pm on
		pm.panelMemberID = sp.panelMemberID

------------------------------  UPDATE records --------------------------------------
update
	pm
set
	pm.statusID = p.statusID
from 
	@tmpMemberSubPanelTbl sp
	inner join @tmpParentPanelTbl p on
		p.panelID = sp.panelParentID
		and p.memberID = sp.memberID
		and p.statusID <> sp.statusID
	inner join ref_panelMembers pm on
		pm.panelMemberID = sp.panelMemberID
GO