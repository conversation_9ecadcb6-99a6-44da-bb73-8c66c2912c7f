declare @resourceTypeID int, @resourceTypeName varchar(50), @functionID int, @functionName varchar(50), @functionDisplayName varchar(50)

DECLARE @rc int, @ResourceTypeFunctionID int

set @resourceTypeName = 'Members'

select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)

select @functionName = 'resultsShowVcard', @functionDisplayName = 'Show vcard on directory results'
EXEC @rc = cms_createSiteResourceFunction
	@resourceTypeID=@resourceTypeID,
	@functionName=@functionName,
	@displayName=@functionDisplayName,
	@functionID=@functionID OUTPUT

select @functionName = 'detailsShowVcard', @functionDisplayName = 'Show vcard on directory details'
EXEC @rc = cms_createSiteResourceFunction
	@resourceTypeID=@resourceTypeID,
	@functionName=@functionName,
	@displayName=@functionDisplayName,
	@functionID=@functionID OUTPUT


select @functionName = 'resultsShowPhoto', @functionDisplayName = 'Show Photo on directory results'
EXEC @rc = cms_createSiteResourceFunction
	@resourceTypeID=@resourceTypeID,
	@functionName=@functionName,
	@displayName=@functionDisplayName,
	@functionID=@functionID OUTPUT


select @functionName = 'detailsShowPhoto', @functionDisplayName = 'Show Photo on directory details'
EXEC @rc = cms_createSiteResourceFunction
	@resourceTypeID=@resourceTypeID,
	@functionName=@functionName,
	@displayName=@functionDisplayName,
	@functionID=@functionID OUTPUT


select @functionName = 'resultsShowMap', @functionDisplayName = 'Show Map Link on directory results'
EXEC @rc = cms_createSiteResourceFunction
	@resourceTypeID=@resourceTypeID,
	@functionName=@functionName,
	@displayName=@functionDisplayName,
	@functionID=@functionID OUTPUT


select @functionName = 'detailsShowMap', @functionDisplayName = 'Show Map Link on directory details'
EXEC @rc = cms_createSiteResourceFunction
	@resourceTypeID=@resourceTypeID,
	@functionName=@functionName,
	@displayName=@functionDisplayName,
	@functionID=@functionID OUTPUT


select @functionName = 'resultsShowDetailsLink', @functionDisplayName = 'Show Link to Detailed Listing '
EXEC @rc = cms_createSiteResourceFunction
	@resourceTypeID=@resourceTypeID,
	@functionName=@functionName,
	@displayName=@functionDisplayName,
	@functionID=@functionID OUTPUT

GO



ALTER PROCEDURE [dbo].[cms_createApplicationInstanceMemberDirectory] 
@siteid int,
@languageID int,
@sectionID int,
@isVisible bit,
@pageName varchar(50),
@pageTitle varchar(200),
@pagedesc varchar(400),
@zoneID int,
@pageTemplateID int,
@pageModeID int,
@pgResourceTypeID int,
@pgParentResourceID int = NULL,
@allowReturnAfterLogin bit,
@applicationInstanceName varchar(100),
@applicationInstanceDesc varchar(200),
@applicationInstanceID int OUTPUT,
@siteResourceID int OUTPUT,
@pageID int OUTPUT

AS

BEGIN TRAN

declare @rc int, @applicationTypeID int, @centerID int,
	@appCreatedContentResourceTypeID int, @activeSiteResourceStatusID int, @defaultLanguageID int,
	@searchContentID int, @searchContentSiteResourceID int

declare @resourceTypeID int, @appearInDirectoryFunctionID int, @trashID int
declare 
	@resultsShowDetailsLinkFunctionID int,
	@detailsShowMapFunctionID int,
	@resultsShowMapFunctionID int,
	@detailsShowPhotoFunctionID int,
	@resultsShowPhotoFunctionID int,
	@detailsShowVcardFunctionID int,
	@resultsShowVcardFunctionID int



select @applicationInstanceID = null
select @siteResourceID = null
select @pageID = null

select @applicationTypeID = applicationTypeID, @resourceTypeID=resourceTypeID
from dbo.cms_applicationTypes where applicationTypeName = 'Members'

select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedContent')
select @activeSiteResourceStatusID = dbo.fn_getResourceStatusID('Active')
select @defaultLanguageID = defaultLanguageID from sites where siteID = @siteID

select @appearInDirectoryFunctionID = dbo.fn_getResourceFunctionID('AppearInDirectory',@resourceTypeID)
select @resultsShowDetailsLinkFunctionID = dbo.fn_getResourceFunctionID('resultsShowDetailsLink',@resourceTypeID)
select @detailsShowMapFunctionID = dbo.fn_getResourceFunctionID('detailsShowMap',@resourceTypeID)
select @resultsShowMapFunctionID = dbo.fn_getResourceFunctionID('resultsShowMap',@resourceTypeID)
select @detailsShowPhotoFunctionID  = dbo.fn_getResourceFunctionID('detailsShowPhoto',@resourceTypeID)
select @resultsShowPhotoFunctionID  = dbo.fn_getResourceFunctionID('resultsShowPhoto',@resourceTypeID)
select @detailsShowVcardFunctionID  = dbo.fn_getResourceFunctionID('detailsShowVcard',@resourceTypeID)
select @resultsShowVcardFunctionID  = dbo.fn_getResourceFunctionID('resultsShowVcard',@resourceTypeID)


-- create instance
EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, 
		@applicationTypeID=@applicationTypeID, @isVisible=@isVisible, @pageName=@pageName, 
		@pageTitle=@pageTitle, @pageDesc=@pagedesc, @zoneID=@zoneID, @pagetemplateid=@pageTemplateID,
		@pageModeID=@pageModeID, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID = @pgParentResourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
		@applicationInstanceName=@applicationInstanceName, @applicationInstanceDesc=@applicationInstanceDesc, 
		@applicationInstanceID=@applicationInstanceID OUTPUT, 
		@siteresourceID=@siteResourceID OUTPUT, 
		@pageID=@pageID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- create searchcontentid
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@defaultLanguageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', @contentID=@searchContentID OUTPUT, 
	@siteResourceID=@searchContentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- insert into memberDirectory table
INSERT INTO dbo.ams_memberDirectories (applicationInstanceID, maxsearchresults, recordsperpage, showVCard, showPhotos, showPhotosDetail, inactivemembersVisible, searchContentID)
VALUES (@applicationInstanceID, 50, 15, 1, 1, 1, 0, @searchContentID)
	IF @@ERROR <> 0 GOTO on_error
	select @centerID = SCOPE_IDENTITY()


exec @rc = dbo.cms_createSiteResourceRight
	@siteID=@siteid, @siteResourceID=@siteResourceID, @include=1,
	@functionID=@resultsShowDetailsLinkFunctionID, @roleID=null, @groupID=null, @memberID=null,
	@inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@appearInDirectoryFunctionID, @resourceRightID=@trashID OUTPUT
IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
exec @rc = dbo.cms_createSiteResourceRight
	@siteID=@siteid, @siteResourceID=@siteResourceID, @include=1,
	@functionID=@detailsShowVcardFunctionID, @roleID=null, @groupID=null, @memberID=null,
	@inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@appearInDirectoryFunctionID, @resourceRightID=@trashID OUTPUT
IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
exec @rc = dbo.cms_createSiteResourceRight
	@siteID=@siteid, @siteResourceID=@siteResourceID, @include=1,
	@functionID=@resultsShowVcardFunctionID, @roleID=null, @groupID=null, @memberID=null,
	@inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@appearInDirectoryFunctionID, @resourceRightID=@trashID OUTPUT
IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
exec @rc = dbo.cms_createSiteResourceRight
	@siteID=@siteid, @siteResourceID=@siteResourceID, @include=1,
	@functionID=@detailsShowMapFunctionID, @roleID=null, @groupID=null, @memberID=null,
	@inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@appearInDirectoryFunctionID, @resourceRightID=@trashID OUTPUT
IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
exec @rc = dbo.cms_createSiteResourceRight
	@siteID=@siteid, @siteResourceID=@siteResourceID, @include=1,
	@functionID=@resultsShowMapFunctionID, @roleID=null, @groupID=null, @memberID=null,
	@inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@appearInDirectoryFunctionID, @resourceRightID=@trashID OUTPUT
IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
exec @rc = dbo.cms_createSiteResourceRight
	@siteID=@siteid, @siteResourceID=@siteResourceID, @include=1,
	@functionID=@detailsShowPhotoFunctionID, @roleID=null, @groupID=null, @memberID=null,
	@inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@appearInDirectoryFunctionID, @resourceRightID=@trashID OUTPUT
IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
exec @rc = dbo.cms_createSiteResourceRight
	@siteID=@siteid, @siteResourceID=@siteResourceID, @include=1,
	@functionID=@resultsShowPhotoFunctionID, @roleID=null, @groupID=null, @memberID=null,
	@inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@appearInDirectoryFunctionID, @resourceRightID=@trashID OUTPUT



-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1

GO


ALTER FUNCTION [dbo].[fn_cache_perms_getResourceRightsXMLForGroupPrint] (@siteResourceID int, @groupPrintID int, @siteID int)
RETURNS xml
AS
BEGIN

	DECLARE @rightsXML xml

	select @rightsXML = isnull((

			SELECT [right].functionName, allowed = case when gprp.groupPrintID is not null then 1 else 0 end
			FROM cms_siteResources sr
			inner join cms_siteResourceTypeFunctions srtf 
				on sr.siteResourceID = @siteResourceID 
				and sr.resourceTypeID = srtf.resourceTypeID
			inner join dbo.cms_siteResourceFunctions as [right]
				on [right].functionID = srtf.functionID
			left outer join cache_perms_siteResourceFunctionRightPrints srfrp
				inner join cache_perms_groupPrintsRightPrints gprp
					on gprp.rightPrintID = srfrp.rightPrintID
					and gprp.groupPrintID = @groupPrintID
			on srfrp.siteResourceID = sr.siteResourceID
			and srfrp.functionID = [right].functionID
			for XML AUTO, ROOT('rights'), TYPE

		),'<rights/>')

	-- Return the result of the function
	RETURN @rightsXML

END


GO


declare @resourceTypeID int, @resourceTypeName varchar(50), @functionID int, @functionName varchar(50), @appearInDirectoryFunctionID int
DECLARE @rc int, @trashID int

declare 
	@resultsShowDetailsLinkFunctionID int,
	@detailsShowMapFunctionID int,
	@resultsShowMapFunctionID int,
	@detailsShowPhotoFunctionID int,
	@resultsShowPhotoFunctionID int,
	@detailsShowVcardFunctionID int,
	@resultsShowVcardFunctionID int


declare 
	@thissiteResourceID int, 
	@thissiteID int, 
	@thisshowVCard bit, 
	@thisshowGoogleMapsLink bit, 
	@thisshowPhotos bit, 
	@thisshowPhotosDetail bit




declare @directories TABLE (siteResourceID int PRIMARY KEY, siteID int, showVCard bit, showGoogleMapsLink bit, showPhotos bit, showPhotosDetail bit)

set @resourceTypeName = 'Members'
select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)

select @appearInDirectoryFunctionID = dbo.fn_getResourceFunctionID('AppearInDirectory',@resourceTypeID)

select @resultsShowDetailsLinkFunctionID = dbo.fn_getResourceFunctionID('resultsShowDetailsLink',@resourceTypeID)
select @detailsShowMapFunctionID = dbo.fn_getResourceFunctionID('detailsShowMap',@resourceTypeID)
select @resultsShowMapFunctionID = dbo.fn_getResourceFunctionID('resultsShowMap',@resourceTypeID)
select @detailsShowPhotoFunctionID  = dbo.fn_getResourceFunctionID('detailsShowPhoto',@resourceTypeID)
select @resultsShowPhotoFunctionID  = dbo.fn_getResourceFunctionID('resultsShowPhoto',@resourceTypeID)
select @detailsShowVcardFunctionID  = dbo.fn_getResourceFunctionID('detailsShowVcard',@resourceTypeID)
select @resultsShowVcardFunctionID  = dbo.fn_getResourceFunctionID('resultsShowVcard',@resourceTypeID)


insert into @directories (siteResourceID,siteID, showVCard, showGoogleMapsLink, showPhotos, showPhotosDetail)
select ai.siteResourceID, ai.siteID, md.showVCard, md.showGoogleMapsLink, md.showPhotos, md.showPhotosDetail
from cms_applicationTypes at
inner join cms_applicationInstances ai
	on ai.applicationTypeID = at.applicationTypeID
	and at.applicationTypeName = 'Members'
inner join ams_memberDirectories md
	on md.applicationInstanceID = ai.applicationInstanceID
order by ai.siteResourceID




select @thissiteResourceID = min(siteResourceID) from @directories
while @thissiteResourceID is not null BEGIN

	select
		@thissiteID = siteID, 
		@thisshowVCard = showVCard , 
		@thisshowGoogleMapsLink = showGoogleMapsLink, 
		@thisshowPhotos = showPhotos, 
		@thisshowPhotosDetail = showPhotosDetail
	from @directories
	where siteResourceID = @thissiteResourceID


	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@thissiteID, @siteResourceID=@thissiteResourceID, @include=1,
		@functionID=@resultsShowDetailsLinkFunctionID, @roleID=null, @groupID=null, @memberID=null,
		@inheritedRightsResourceID=@thissiteResourceID, @inheritedRightsFunctionID=@appearInDirectoryFunctionID, @resourceRightID=@trashID OUTPUT

	if @thisshowVCard = 1 
	BEGIN 
		exec @rc = dbo.cms_createSiteResourceRight
			@siteID=@thissiteID, @siteResourceID=@thissiteResourceID, @include=1,
			@functionID=@detailsShowVcardFunctionID, @roleID=null, @groupID=null, @memberID=null,
			@inheritedRightsResourceID=@thissiteResourceID, @inheritedRightsFunctionID=@appearInDirectoryFunctionID, @resourceRightID=@trashID OUTPUT

		exec @rc = dbo.cms_createSiteResourceRight
			@siteID=@thissiteID, @siteResourceID=@thissiteResourceID, @include=1,
			@functionID=@resultsShowVcardFunctionID, @roleID=null, @groupID=null, @memberID=null,
			@inheritedRightsResourceID=@thissiteResourceID, @inheritedRightsFunctionID=@appearInDirectoryFunctionID, @resourceRightID=@trashID OUTPUT
	END

	if @thisshowGoogleMapsLink = 1 
	BEGIN 
		exec @rc = dbo.cms_createSiteResourceRight
			@siteID=@thissiteID, @siteResourceID=@thissiteResourceID, @include=1,
			@functionID=@detailsShowMapFunctionID, @roleID=null, @groupID=null, @memberID=null,
			@inheritedRightsResourceID=@thissiteResourceID, @inheritedRightsFunctionID=@appearInDirectoryFunctionID, @resourceRightID=@trashID OUTPUT

		exec @rc = dbo.cms_createSiteResourceRight
			@siteID=@thissiteID, @siteResourceID=@thissiteResourceID, @include=1,
			@functionID=@resultsShowMapFunctionID, @roleID=null, @groupID=null, @memberID=null,
			@inheritedRightsResourceID=@thissiteResourceID, @inheritedRightsFunctionID=@appearInDirectoryFunctionID, @resourceRightID=@trashID OUTPUT
	END

	if @thisshowPhotos = 1 
	BEGIN 
		exec @rc = dbo.cms_createSiteResourceRight
			@siteID=@thissiteID, @siteResourceID=@thissiteResourceID, @include=1,
			@functionID=@detailsShowPhotoFunctionID, @roleID=null, @groupID=null, @memberID=null,
			@inheritedRightsResourceID=@thissiteResourceID, @inheritedRightsFunctionID=@appearInDirectoryFunctionID, @resourceRightID=@trashID OUTPUT
	END
	if @thisshowPhotosDetail = 1 
	BEGIN 
		exec @rc = dbo.cms_createSiteResourceRight
			@siteID=@thissiteID, @siteResourceID=@thissiteResourceID, @include=1,
			@functionID=@resultsShowPhotoFunctionID, @roleID=null, @groupID=null, @memberID=null,
			@inheritedRightsResourceID=@thissiteResourceID, @inheritedRightsFunctionID=@appearInDirectoryFunctionID, @resourceRightID=@trashID OUTPUT
	END

	select @thissiteResourceID = min(siteResourceID) from @directories where siteResourceID > @thissiteResourceID
END


GO

update cms_siteResourceFunctions set
displayName = 'Appears In Directory'
where functionName = 'appearInDirectory'

GO
