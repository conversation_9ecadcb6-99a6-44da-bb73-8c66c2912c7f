use membercentral
GO

-- add universalRole
SET IDENTITY_INSERT dbo.cms_siteResourceRoles on
insert into dbo.cms_siteResourceRoles (roleID, roleName, roleTypeID)
values (8, 'Client Administrator', 2)
SET IDENTITY_INSERT dbo.cms_siteResourceRoles off
GO

-- add new manage protected groups function and give to ClientAdmin and SuperAdmin roles
declare @resourceTypeID int, @functionID int, @resourceTypeFunctionID int
select @resourceTypeID = dbo.fn_getResourceTypeID('Site')
EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='manageProtectedGroups', @displayName='Manage Protected Groups', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('manageProtectedGroups',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 8, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID
GO

-- add protected column to groups
ALTER TABLE dbo.ams_groups ADD isProtected bit NOT NULL CONSTRAINT DF_ams_groups_isProtected DEFAULT 0
GO

ALTER FUNCTION [dbo].[fn_getRecursiveGroups] (@orgid int, @startGroupID int=null, @excludeGroupID int=null, @hideOnGroupLists bit=0)
RETURNS TABLE 
AS
RETURN 
(
	WITH Groups AS (
		SELECT groupID, groupCode, groupName, groupDesc, allowManualAssignment, isProtected, parentGroupID, isSystemGroup,
			CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
			CAST(groupName as varchar(max)) as thePathExpanded
		FROM (
			SELECT g.groupID, g.groupCode, g.groupName, g.groupDesc, g.allowManualAssignment, g.isProtected, g.parentGroupID, g.isSystemGroup, 
				ROW_NUMBER() OVER (ORDER BY groupName) AS theRow
			FROM dbo.ams_groups AS g
			WHERE orgID = @orgid
			AND groupID = @startGroupID
			AND g.hideOnGroupLists = @hideOnGroupLists
			AND g.status <> 'D'
			AND isnull(@startGroupID,0) = @startGroupID
			union			
			SELECT g.groupID, g.groupCode, g.groupName, g.groupDesc, g.allowManualAssignment, g.isProtected, g.parentGroupID, g.isSystemGroup, 
				ROW_NUMBER() OVER (ORDER BY groupName) AS theRow
			FROM dbo.ams_groups AS g
			WHERE orgID = @orgid
			AND parentGroupID is null
			AND g.status <> 'D'
			AND g.hideOnGroupLists = @hideOnGroupLists
			AND isnull(@startGroupID,0) = 0
		) as x
		union all
		SELECT groupID, groupCode, groupName, groupDesc, allowManualAssignment, isProtected, parentGroupID, isSystemGroup, 
			thePath + '.' + CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
			thePathExpanded = case 
								when isnull(parentGroupID,0) = isnull(@startGroupID,0) then groupName
								else thePathExpanded + ' \ ' + groupName 
							end
		FROM (
			SELECT g.groupID, g.groupCode, g.groupName, g.groupDesc, g.allowManualAssignment, g.isProtected, g.parentGroupID, g.isSystemGroup, 
				gcte.thePath, gcte.thePathExpanded, ROW_NUMBER() OVER (ORDER BY g.groupName) AS therow
			FROM dbo.ams_groups AS g
			inner JOIN Groups as gcte ON g.parentGroupID = gcte.groupID
				AND g.status <> 'D'
				AND g.hideOnGroupLists = @hideOnGroupLists
		) as y
	)

	select *, ROW_NUMBER() OVER (ORDER BY thePath) AS row
	from Groups
	where (@excludeGroupID is null or thePath + '.' not like (select thePath + '.' from Groups where groupID = @excludeGroupID) + '%')
)
GO

ALTER PROC [dbo].[ams_createDefaultGroups]
@orgID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	DECLARE @rc int, @groupID int

	EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode='Public', @groupName='Public', @groupDesc='Visitors that are not logged in', @isSystemGroup=1, @allowManualAssignment=0, @parentGroupID=null, @hideOnGroupLists=0, @groupID=@groupID OUTPUT
	EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode='Guests', @groupName='Guests', @groupDesc='Registered guest users', @isSystemGroup=1, @allowManualAssignment=0, @parentGroupID=null, @hideOnGroupLists=0, @groupID=@groupID OUTPUT
	EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode='Users', @groupName='Users', @groupDesc='Approved site users', @isSystemGroup=1, @allowManualAssignment=0, @parentGroupID=null, @hideOnGroupLists=0, @groupID=@groupID OUTPUT
	EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode='SiteAdmins', @groupName='Site Administrators', @groupDesc='Full Site Administrators', @isSystemGroup=1, @allowManualAssignment=1, @parentGroupID=null, @hideOnGroupLists=0, @groupID=@groupID OUTPUT

	EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode='ClientAdmins', @groupName='Client Administrators', @groupDesc='Client Administrators', @isSystemGroup=1, @allowManualAssignment=1, @parentGroupID=null, @hideOnGroupLists=0, @groupID=@groupID OUTPUT
	UPDATE dbo.ams_groups set isProtected = 1 where groupID = @groupID

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	SELECT @groupID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO


-- add new group to every org
declare @orgID int, @groupID int
select @orgID = min(orgID) from organizations
while @orgID is not null BEGIN
	set @groupID = null

	EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode='ClientAdmins', @groupName='Client Administrators', @groupDesc='Client Administrators', @isSystemGroup=1, @allowManualAssignment=1, @parentGroupID=null, @hideOnGroupLists=0, @groupID=@groupID OUTPUT
	UPDATE dbo.ams_groups set isProtected = 1 where groupID = @groupID

	select @orgID = min(orgID) from organizations where orgID > @orgID
END
GO

ALTER PROC [dbo].[ams_createGroup]
@orgID int,
@groupCode varchar(30),
@groupName varchar(115),
@groupDesc varchar(200),
@isSystemGroup bit,
@allowManualAssignment bit,
@parentGroupID int,
@hideOnGroupLists bit,
@groupID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	SELECT @groupID = null

	-- if not there, add it
	IF (@parentGroupID is null and NOT EXISTS (select groupID FROM dbo.ams_groups where orgID = @orgID and groupName = @groupName and parentGroupID is null and [status] = 'A'))
		OR
		(@parentGroupID is not null and NOT EXISTS (select groupID FROM dbo.ams_groups where orgID = @orgID and groupName = @groupName and parentGroupID = @parentGroupID and [status] = 'A')) 
		BEGIN

			-- since we are not passing in isProtected, new groups should have the same isProtected setting as their parent.
			DECLARE @isProtected bit
			SELECT @isProtected = isProtected from dbo.ams_groups where groupID = @parentGroupID
			IF @isProtected is null
				SET @isProtected = 0

			-- if this is a protected group, allowManualAssignment must be yes.
			IF @isProtected = 1
				SET @allowManualAssignment = 1

			INSERT INTO dbo.ams_groups (orgID, groupCode, groupName, groupDesc, isSystemGroup, allowManualAssignment, parentGroupID, [status], hideOnGroupLists, [uid], isProtected)
			VALUES (@orgID, @groupCode, @groupName, @groupDesc, @isSystemGroup, @allowManualAssignment, @parentGroupID, 'A', @hideOnGroupLists, newId(), @isProtected)
				SELECT @groupID = SCOPE_IDENTITY()

			EXEC dbo.cache_ams_updateRecursiveGroups @restrictToOrgID=@orgID, @restrictToGroupID=@groupID

		END
	ELSE 
		SELECT @groupID = 0

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	SELECT @groupID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[ams_updateGroup]
@groupID int,
@orgID int,
@groupCode varchar(30),
@groupName varchar(115),
@groupDesc varchar(200),
@parentGroupID int,
@allowManualAssignment bit

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	DECLARE @oldParentGroupID int, @rc int, @isProtected bit
	SELECT @oldParentGroupID = parentGroupID, @isProtected = isProtected
		FROM dbo.ams_groups
		WHERE groupID = @groupID and orgID = @orgID

	-- if this is a protected group, allowManualAssignment must be yes.
	IF @isProtected = 1
		SET @allowManualAssignment = 1

	UPDATE dbo.ams_groups
	SET groupCode = @groupCode, 
		groupName = @groupName, 
		groupDesc = @groupDesc,
		parentGroupID = @parentGroupID,
		allowManualAssignment = @allowManualAssignment
	WHERE groupID = @groupID
	and orgID = @orgID

	-- Assume group name was changed and update the vebose condition name.
	update dbo.ams_virtualGroupConditions 
	set [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
	where fieldCode = 'grp_' + cast(@groupID as varchar(10))

	IF isNull(@oldParentGroupID,0) <> isNull(@parentGroupID,0) BEGIN
		EXEC dbo.cache_ams_updateRecursiveGroups @restrictToOrgID=@orgID, @restrictToGroupID=null
	END

	IF @TranCounter = 0
		COMMIT TRAN;

	IF isNull(@oldParentGroupID,0) <> isNull(@parentGroupID,0) BEGIN
		-- (@runSchedule=2 indicates delayed processing)  
		declare @itemGroupUID uniqueidentifier
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList='', @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT
	END

	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO


ALTER PROC [dbo].[createSite]
	@orgID int,
	@sitecode varchar(10),
	@siteName varchar(60),
	@mainNetworkID int,
	@isLoginNetwork bit,
	@isMasterSite bit,
	@defaultLanguageID int,
	@defaultTimeZoneID int,
	@defaultCurrencyTypeID int,
	@showCurrencyType bit,
	@hasSSL bit,
	@allowGuestAccounts bit,
	@forceLoginPage bit,
	@useRemoteLogin bit,
	@affiliationRequired bit,
	@providesFreeFastCase bit,
	@enforceSiteAgreement bit,
	@allowMemberUpdates bit,
	@immediateMemberUpdates bit,
	@emailMemberUpdates varchar(200),
	@defaultPostalState varchar(10),
	@joinURL varchar(100),
	@pdfPassword varchar(30),
	@alternateGuestAccountCreationLink varchar(400),
	@alternateGuestAccountPopup bit,
	@alternateForgotPasswordLink varchar(400),
	@mainhostname varchar(80),
	@norightsContent varchar(max),
	@norightsNotLoggedInContent varchar(max),
	@inactiveUserContent varchar(max),
	@siteagreementContent varchar(max),
	@welcomeMessageContent varchar(max),
	@firstTimeLoginContent varchar(max),
	@siteID int OUTPUT

AS

DECLARE @rc int, @templateID int, @modeID int, @sectionID int, @sectionResourceTypeID int, 
	@clientAdminRoleID int, @siteAdminRoleID int, @superAdminRoleID int, 
	@clientAdminGroupID int, @siteAdminGroupID int, @superAdminGroupID int, @trashID int

BEGIN TRAN
	-- check for existing sitecode
	SELECT @siteID = null
	SELECT @siteID = siteID FROM dbo.sites where sitecode = @sitecode

	-- if not there, add it
	IF @siteID is not null
		GOTO on_error

	-- insert sites
	INSERT INTO dbo.sites (orgID, sitecode, siteName, defaultLanguageID, defaultTimeZoneId, defaultCurrencyTypeID,
		hasSSL, allowGuestAccounts, forceLoginPage, useRemoteLogin, affiliationRequired, 
		providesFreeFastCase, allowMemberUpdates, enforceSiteAgreement, immediateMemberUpdates, emailMemberUpdates, 
		defaultPostalState, joinURL, pdfPassword, alternateGuestAccountCreationLink, alternateGuestAccountPopup, 
		alternateForgotPasswordLink, showCurrencyType, enableMobile, enableDeviceDetection)
	VALUES (@orgID, @sitecode, @siteName, @defaultLanguageID, @defaultTimeZoneId, @defaultCurrencyTypeID, 
		@hasSSL, @allowGuestAccounts, @forceLoginPage, @useRemoteLogin, @affiliationRequired, 
		@providesFreeFastCase, @allowMemberUpdates, @enforceSiteAgreement, @immediateMemberUpdates, @emailMemberUpdates, 
		@defaultPostalState, @joinURL, @pdfPassword, @alternateGuestAccountCreationLink, @alternateGuestAccountPopup, 
		@alternateForgotPasswordLink, @showCurrencyType, 0, 0)
		IF @@ERROR <> 0 GOTO on_error
		SELECT @siteID = SCOPE_IDENTITY()

	-- createSiteLanguage		
	EXEC @rc = dbo.createSiteLanguage @siteID=@siteID, @languageID=@defaultLanguageID		
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- get resourceType for site
	declare @siteResourceTypeID int
	select @siteResourceTypeID = dbo.fn_getResourceTypeID('Site')

	-- get active resource status
	declare @siteResourceStatusID int
	select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

	-- create a resourceID for the site
	DECLARE @siteResourceID int	
	exec dbo.cms_createSiteResource
		@resourceTypeID = @siteResourceTypeID,
		@siteResourceStatusID = @siteResourceStatusID,
		@siteID = @siteid,
		@isVisible = 1,
		@parentSiteResourceID = null,
		@siteResourceID = @siteResourceID OUTPUT
		IF @@ERROR <> 0 OR @siteResourceID = 0 GOTO on_error
	
	-- update site with new resource
	UPDATE dbo.sites
	SET siteResourceID = @siteResourceID
	WHERE siteID = @siteID
		IF @@ERROR <> 0 GOTO on_error
		
	-- roles
	select @clientAdminRoleID = dbo.fn_getResourceRoleID('Client Administrator')
	select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')
	select @siteAdminRoleID = dbo.fn_getResourceRoleID('Site Administrator')
	select @clientAdminGroupID = groupID 
		from dbo.ams_groups 
		where groupCode = 'ClientAdmins' 
		and orgID = @orgID
	select @siteAdminGroupID = groupID 
		from dbo.ams_groups 
		where groupCode = 'SiteAdmins' 
		and orgID = @orgID
	select @superAdminGroupID = groupID 
		from dbo.ams_groups 
		where groupCode = 'SuperAdmins' 
		and orgID = 1

	-- give clientAdmin Role to clientAdmin Group
	EXEC @rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1,
		@functionID=null, @roleID=@clientAdminRoleID, @groupID=@clientAdminGroupID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, @resourceRightID = @trashID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- give siteAdmin Role to siteAdmin Group
	EXEC @rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1,
		@functionID=null, @roleID=@siteAdminRoleID, @groupID=@siteAdminGroupID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, @resourceRightID = @trashID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- give superAdmin Role to superAdmin Group
	EXEC @rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1,
		@functionID=null, @roleID=@superAdminRoleID, @groupID=@superAdminGroupID, @memberID=null,
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, @resourceRightID = @trashID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- add default hostname
	DECLARE @hostnameID int	
	EXEC @rc = dbo.createSiteHostName @siteID=@siteID, @hostname=@mainhostname, @useRedirect=null, @hostnameID=@hostnameID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- add default template
	DECLARE @templateTypeID int
	SELECT @templateTypeID = dbo.fn_getTemplateTypeID('Page')
	EXEC @rc = dbo.cms_CreatePageTemplate @siteid=@siteID, @templateTypeID=@templateTypeID, @templateName='Default', @templateDesc='Default site template', @templateFileName='Default', @templateID=@templateID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @templateID = 0 GOTO on_error2
	
	-- add default page sections
	SELECT @modeID = dbo.fn_getModeID('Normal')
	SELECT @sectionResourceTypeID = dbo.fn_getResourceTypeID('SystemCreatedSection')
	EXEC @rc = dbo.cms_createPageSection @siteID=@siteID, @sectionResourceTypeID=@sectionResourceTypeID, @ovTemplateID=@templateID, @ovTemplateIDMobile=null, @ovModeID=@modeID, @parentSectionID=null, @sectionName='Root', @sectionCode='Root', @inheritPlacements=1, @sectionID=@sectionID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createPageSection @siteID=@siteID, @sectionResourceTypeID=@sectionResourceTypeID, @ovTemplateID=null, @ovTemplateIDMobile=null, @ovModeID=null, @parentSectionID=@sectionID, @sectionName='MCAMSMemberDocuments', @sectionCode='MCAMSMemberDocuments', @inheritPlacements=0, @sectionID=@trashID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- default fieldsets
	EXEC @rc = dbo.cms_createDefaultFieldsets @siteid=@siteID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add default pages
	EXEC @rc = dbo.cms_createDefaultPages @siteid=@siteID, @sectionid=@sectionID, @languageID=@defaultLanguageID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add default History (notes) Categories
	EXEC @rc = dbo.cms_createDefaultHistoryCategories @siteid=@siteID, @contributingMemberID=461530
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add to network
	EXEC @rc = dbo.createNetworkSite @networkID=@mainNetworkID, @siteID=@siteID, @isLoginNetwork=@isLoginNetwork, @isMasterSite=@isMasterSite
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add default FULL Frequency
	insert into dbo.sub_frequencies(frequencyName, frequency, frequencyShortName, uid, 
		rateRequired, hasInstallments, monthlyInterval, isSystemRate, siteID, status)
	values('Full', 1, 'F', newid(), 1, 1, 1, 1, @siteID, 'A')	
		IF @@ERROR <> 0 GOTO on_error2

	-- add content objects
	DECLARE @sysCreatedContentResourceTypeID int, @activesiteResourceStatusID int, @newContentid int, @newresourceid int
	select @sysCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('SystemCreatedContent')
	select @activesiteResourceStatusID = dbo.fn_getResourceStatusId('Active')
	EXEC @rc = dbo.cms_createContentObject 
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'NoRights',
		@contentDesc = null,
		@rawContent = @norightsContent,
		@memberID=NULL,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET noRightsContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject 
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'NoRightsNotLoggedIn',
		@contentDesc = null,
		@rawContent = @norightsNotLoggedInContent,
		@memberID=NULL,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET noRightsNotLoggedInContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'InactiveUser',
		@contentDesc = null,
		@rawContent = @inactiveUserContent,
		@memberID=NULL,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET InactiveUserContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'SiteAgreement',
		@contentDesc = null,
		@rawContent = @siteagreementContent,
		@memberID=NULL,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET SiteAgreementContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'Welcome Message',
		@contentDesc = null,
		@rawContent = @welcomeMessageContent,
		@memberID=NULL,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET welcomeMessageContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'First Time Login Message',
		@contentDesc = null,
		@rawContent = @firstTimeLoginContent,
		@memberID=NULL,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET firstTimeLoginContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2

	-- link up superusers to all new sites
	INSERT INTO dbo.ams_memberNetworkProfiles (memberID, profileID, [status], dateCreated, siteID)
	SELECT distinct mnp.memberID, mnp.profileID, 'A', getdate(), @siteID
	FROM dbo.ams_memberNetworkProfiles AS mnp 
	INNER JOIN dbo.ams_networkProfiles AS np ON mnp.profileID = np.profileID
	WHERE mnp.status = 'A'
	AND np.networkID = dbo.fn_getNetworkID('MemberCentral Super Administrators')
	AND np.status = 'A'
	AND mnp.siteID <> @siteID
		IF @@ERROR <> 0 GOTO on_error2

	EXEC @rc = dbo.cms_populateSiteResourceRightsCache @siteID=@siteID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @siteID = 0
	RETURN -1

on_error2:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO


-- Client Administrators group granted universal role of clientAdmin
DECLARE @siteID int, @orgID int, @siteResourceID int, @clientAdminRoleID int, @clientAdminGroupID int, @trashID int
select @clientAdminRoleID = dbo.fn_getResourceRoleID('Client Administrator')

select @siteID = min(siteID) from sites
while @siteID is not null BEGIN
	select @orgID = orgID, @siteResourceID = siteResourceID from sites where siteID = @siteID
	select @clientAdminGroupID = groupID from dbo.ams_groups where groupCode = 'ClientAdmins' and orgID = @orgID

	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1,
		@functionID=null, @roleID=@clientAdminRoleID, @groupID=@clientAdminGroupID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, @resourceRightID = @trashID OUTPUT
	
	select @siteID = min(siteID) from sites where siteID > @siteID
END
GO

ALTER PROC [dbo].[ams_moveGroup]
@groupID int,
@parentGroupID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	DECLARE @oldParentGroupID int, @orgID int, @rc int
	SELECT @oldParentGroupID = parentGroupID, @orgID = orgID
		FROM dbo.ams_groups
		WHERE groupID = @groupID

	IF isNull(@oldParentGroupID,0) <> isNull(@parentGroupID,0) BEGIN
		UPDATE dbo.ams_groups
		SET parentGroupID = @parentGroupID		
		WHERE groupID = @groupID

		-- if parent group is protected, this group and subgroups must be marked as protected
		DECLARE @isProtected bit
		SELECT @isProtected = isProtected from dbo.ams_groups where groupID = @parentGroupID
		IF @isProtected = 1 BEGIN
			update g
			set g.isProtected = 1
			from dbo.ams_groups as g
			inner join dbo.cache_ams_recursiveGroups as rg on g.groupID = rg.startGroupID
			where rg.groupID = @groupID
		END

		EXEC dbo.cache_ams_updateRecursiveGroups @restrictToOrgID=@orgID, @restrictToGroupID=null

		-- (@runSchedule=2 indicates delayed processing)  
		declare @itemGroupUID uniqueidentifier
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList='', @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT
	END

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[ams_createMemberGroup]
@memberid int,
@groupID int,
@bypassQueue bit = 0

AS

-- Ensure we have the activememberid
SELECT @memberID = activeMemberID
	from dbo.ams_members
	where memberID = @memberID

-- if not there and group is in same org as member, add it
IF NOT EXISTS (select memberGroupID from dbo.ams_memberGroups where memberID = @memberID and groupID = @groupID)
	AND EXISTS (select groupID from dbo.ams_groups as g inner join dbo.ams_members as m on m.orgid = g.orgid and m.memberid = @memberid where g.groupID = @groupID)
BEGIN

	-- add to manual assignment table
	INSERT INTO dbo.ams_memberGroups (memberid, groupID)
    VALUES (@memberid, @groupID)
		IF @@ERROR <> 0 GOTO on_error

	declare @orgid int
	select @orgid=orgID from dbo.ams_groups where groupID = @groupID

	-- add to cache table for immediate viewing on the grid
	IF NOT EXISTS (select memberID from dbo.cache_members_groups where memberID = @memberID and groupID = @groupID) BEGIN
		INSERT INTO dbo.cache_members_groups (orgid, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
		VALUES (@orgID, @memberID, @groupID, 1, 0, 0, 0)
			IF @@ERROR <> 0 GOTO on_error
	END

	IF @bypassQueue = 0 BEGIN
		-- queue member groups (@runSchedule=2 indicates delayed processing) 
		declare @statusReadyPopMemGroups int, @itemGroupUID uniqueidentifier
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberID, @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT
	END

END

RETURN 0

-- error exit
on_error:
	RETURN -1
GO

