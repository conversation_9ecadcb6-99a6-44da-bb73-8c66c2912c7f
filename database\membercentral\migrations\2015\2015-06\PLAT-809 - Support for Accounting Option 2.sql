USE platformQueue
GO

insert into dbo.tblQueueTypes (queueType) values ('importAcctOpt2')
GO

declare @queueTypeID int
select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'importAcctOpt2'
insert into dbo.tblQueueStatuses (queueTypeID, queueStatus) values (@queueTypeID, 'insertingItems')
insert into dbo.tblQueueStatuses (queueTypeID, queueStatus) values (@queueTypeID, 'readyToProcess')
insert into dbo.tblQueueStatuses (queueTypeID, queueStatus) values (@queueTypeID, 'grabbedForProcessing')
insert into dbo.tblQueueStatuses (queueTypeID, queueStatus) values (@queueTypeID, 'processingItem')
insert into dbo.tblQueueStatuses (queueTypeID, queueStatus) values (@queueTypeID, 'grabbedForNotifying')
insert into dbo.tblQueueStatuses (queueTypeID, queueStatus) values (@queueTypeID, 'readyToNotify')
insert into dbo.tblQueueStatuses (queueTypeID, queueStatus) values (@queueTypeID, 'done')
GO

declare @queueTypeID int
select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'importAcctOpt2'
insert into dbo.tblQueueTypeDataColumns (queueTypeID, columnName, dataTypeID, ColumnDesc) values (@queueTypeID, 'rowID', 3, null)
insert into dbo.tblQueueTypeDataColumns (queueTypeID, columnName, dataTypeID, ColumnDesc) values (@queueTypeID, 'MCMemberID', 3, null)
insert into dbo.tblQueueTypeDataColumns (queueTypeID, columnName, dataTypeID, ColumnDesc) values (@queueTypeID, 'MCInvoiceProfileID', 3, null)
insert into dbo.tblQueueTypeDataColumns (queueTypeID, columnName, dataTypeID, ColumnDesc) values (@queueTypeID, 'MCRevenueGLAID', 3, null)
insert into dbo.tblQueueTypeDataColumns (queueTypeID, columnName, dataTypeID, ColumnDesc) values (@queueTypeID, 'saleDate', 4, null)
insert into dbo.tblQueueTypeDataColumns (queueTypeID, columnName, dataTypeID, ColumnDesc) values (@queueTypeID, 'saleDescription', 1, null)
insert into dbo.tblQueueTypeDataColumns (queueTypeID, columnName, dataTypeID, ColumnDesc) values (@queueTypeID, 'saleAmount', 2, null)
insert into dbo.tblQueueTypeDataColumns (queueTypeID, columnName, dataTypeID, ColumnDesc) values (@queueTypeID, 'MCPayProfileID', 3, null)
insert into dbo.tblQueueTypeDataColumns (queueTypeID, columnName, dataTypeID, ColumnDesc) values (@queueTypeID, 'MCPayGatewayID', 3, null)
insert into dbo.tblQueueTypeDataColumns (queueTypeID, columnName, dataTypeID, ColumnDesc) values (@queueTypeID, 'MCCashGLAID', 3, null)
insert into dbo.tblQueueTypeDataColumns (queueTypeID, columnName, dataTypeID, ColumnDesc) values (@queueTypeID, 'MCProfileCode', 1, null)
insert into dbo.tblQueueTypeDataColumns (queueTypeID, columnName, dataTypeID, ColumnDesc) values (@queueTypeID, 'MCCashGLName', 1, null)
GO


CREATE PROC [dbo].[job_importAcctOpt2_grabForProcessing]
@serverID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @statusReady int, @statusGrabbed int
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'importAcctOpt2'
		and qs.queueStatus = 'readyToProcess'
	select @statusGrabbed = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'importAcctOpt2'
		and qs.queueStatus = 'grabbedForProcessing'

	IF OBJECT_ID('tempdb..#tmpTblQueueItems_importAcctOpt2') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_importAcctOpt2
	CREATE TABLE #tmpTblQueueItems_importAcctOpt2 (itemUID uniqueidentifier)

	declare @jobUID uniqueidentifier
	set @jobUID = NEWID()

	-- dequeue in order of dateAdded. 
	update qi WITH (UPDLOCK, READPAST)
	set qi.queueStatusID = @statusGrabbed,
		qi.dateUpdated = getdate(),
		qi.jobUID = @jobUID,
		qi.jobDateStarted = getdate(),
		qi.jobServerID = @serverID
		OUTPUT inserted.itemUID
		INTO #tmpTblQueueItems_importAcctOpt2
	from platformQueue.dbo.tblQueueItems as qi
	inner join (
		select top 1 qi2.itemUID 
		from platformQueue.dbo.tblQueueItems as qi2
		where qi2.queueStatusID = @statusReady
		order by qi2.dateAdded, qi2.itemUID
		) as batch on batch.itemUID = qi.itemUID
	where qi.queueStatusID = @statusReady

	-- final data
	select itemUID, @jobUID as jobUID
	from #tmpTblQueueItems_importAcctOpt2

	IF OBJECT_ID('tempdb..#tmpTblQueueItems_importAcctOpt2') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_importAcctOpt2


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH
GO

CREATE PROC [dbo].[job_importAcctOpt2_clearDone]
AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @statusDone int
	select @statusDone = qs.queueStatusID
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'importAcctOpt2'
		and qs.queueStatus = 'done'

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID = @statusDone
			except
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID <> @statusDone
	)
	DELETE from platformQueue.dbo.tblQueueItems
	where itemUID in (
		select qi.itemUID
		FROM platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qid.itemGroupUID
		WHERE qi.queueStatusID = @statusDone
	)

	DELETE from platformQueue.dbo.tblQueueItemData
	where itemUID not in (select itemUID from platformQueue.dbo.tblQueueItems)


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH
GO

CREATE PROC [dbo].[job_importAcctOpt2_grabForNotification]
AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @statusReady int, @statusGrabbed int, @queueTypeID int
	select @statusReady = qs.queueStatusID , @queueTypeID = qt.queueTypeID
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'importAcctOpt2'
		and qs.queueStatus = 'readyToNotify'
	select @statusGrabbed = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'importAcctOpt2'
		and qs.queueStatus = 'grabbedForNotifying'

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL 
		DROP TABLE #tmpNotify
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier)

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID = @statusReady
			except
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID <> @statusReady
	)
	UPDATE platformQueue.dbo.tblQueueItems WITH (UPDLOCK, READPAST)
	SET queueStatusID = @statusGrabbed,
		dateUpdated = getdate()
		OUTPUT qid.itemGroupUID
		INTO #tmpNotify
	FROM platformQueue.dbo.tblQueueItems as qi
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qid.itemGroupUID
	where qi.queueStatusID = @statusReady

	-- we need to update batch controls and post them now that they are all done
	EXEC membercentral.dbo.tr_autoPostImportedBatches

	-- return itemGroupUIDs that can be marked as done
	select distinct tmpN.itemGroupUID, me.email as reportEmail, s.siteName, s.siteCode, m.firstname, m.lastname, m.memberNumber
	from #tmpNotify as tmpN
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN membercentral.dbo.sites as s on s.siteID = qid.siteID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = qid.recordedByMemberID
	LEFT OUTER JOIN membercentral.dbo.ams_memberEmails as me 
		INNER JOIN membercentral.dbo.ams_memberEmailTypes as met on met.emailTypeID = me.emailTypeID and met.emailTypeOrder = 1
		on me.memberID = m.activeMemberID
	order by tmpN.itemGroupUID
	

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL 
		DROP TABLE #tmpNotify

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH
GO

USE [memberCentral]
GO

CREATE PROC [dbo].[tr_importTransactionsOption2_prepTable]
@importResult xml OUTPUT

AS

SET NOCOUNT ON

set @importResult = null


-- *********************************
-- ensure all required columns exist 
-- *********************************
BEGIN TRY
	-- this will get the columns that are required
	IF OBJECT_ID('tempdb..#tblAcctCols') IS NOT NULL 
		DROP TABLE #tblAcctCols
	CREATE TABLE #tblAcctCols (COLUMN_NAME sysname)

	insert into #tblAcctCols
	select 'rowID' union all
	select 'saleDescription' union all
	select 'saleDate' union all
	select 'saleMemberNumber' union all
	select 'saleAmount' union all
	select 'saleGLCode'

	-- this will get the columns that are actually in the import
	IF OBJECT_ID('tempdb..#tblAcctImportCols') IS NOT NULL 
		DROP TABLE #tblAcctImportCols
	CREATE TABLE #tblAcctImportCols (ORDINAL_POSITION int, COLUMN_NAME sysname)

	insert into #tblAcctImportCols
	select column_id, [name] 
	from tempdb.sys.columns 
	where object_id = object_id('tempdb..#mc_Acct2Import');

	INSERT INTO #tblAcctErrors (msg)
	select 'The required column ' + org.column_name + ' is missing from your data.'
	from #tblAcctCols as org
	left outer join #tblAcctImportCols as imp on imp.column_name = org.column_name
	where imp.ORDINAL_POSITION is null
		IF @@ROWCOUNT > 0 GOTO on_done

	delete from #tblAcctImportCols 
	where column_name in (select COLUMN_NAME from #tblAcctCols)

	IF OBJECT_ID('tempdb..#tblAcctCols') IS NOT NULL 
		DROP TABLE #tblAcctCols
END TRY
BEGIN CATCH
	INSERT INTO #tblAcctErrors (msg)
	VALUES ('Unable to validate file contains all required columns.')

	GOTO on_done
END CATCH


-- **********
-- prep table 
-- **********
BEGIN TRY
	-- add holding columns
	ALTER TABLE #mc_Acct2Import ADD MCMemberID int null, MCRevenueGLAID int null, MCInvoiceProfileID int null,
		MCCashGLAID int null, MCPayProfileID int null, MCProfileCode varchar(20) null, MCPayGatewayID int null, MCCashGLName varchar(200) null, 
		itemUID uniqueidentifier NOT NULL DEFAULT(newID());

	-- ensure rowID is an int
	ALTER TABLE #mc_Acct2Import ALTER COLUMN RowID int not null;

END TRY
BEGIN CATCH
	INSERT INTO #tblAcctErrors (msg)
	VALUES ('Unable to prepare import table by adding missing columns.')

	INSERT INTO #tblAcctErrors (msg)
	VALUES (left(error_message(),300))

	GOTO on_done
END CATCH


-- *************
-- extra columns 
-- *************
BEGIN TRY
	-- extra columns should stop import to prevent accidental misnamed columns
	IF EXISTS (select column_name from #tblAcctImportCols) BEGIN
		INSERT INTO #tblAcctErrors (msg)
		SELECT TOP 100 PERCENT 'The imported file contains the extra column ' + cast(column_name as varchar(300)) + '. Remove this column from your file.' 
		FROM #tblAcctImportCols  
		ORDER BY column_name
	END
END TRY
BEGIN CATCH
	INSERT INTO #tblAcctErrors (msg)
	VALUES ('Unable to validate import file for extra columns.')

	INSERT INTO #tblAcctErrors (msg)
	VALUES (left(error_message(),300))

	GOTO on_done
END CATCH


-- ************************
-- generate result xml file 
-- ************************
on_done:
	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblAcctErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE)
	
	IF OBJECT_ID('tempdb..#tblAcctCols') IS NOT NULL 
		DROP TABLE #tblAcctCols
	IF OBJECT_ID('tempdb..#tblAcctImportCols') IS NOT NULL 
		DROP TABLE #tblAcctImportCols

RETURN 0
GO

CREATE PROC [dbo].[tr_importTransactionsOption2_validate]
@siteid int, 
@payProfileID int, 
@importResult xml OUTPUT

AS

SET NOCOUNT ON

declare @orgID int
select @orgID=orgID from dbo.sites where siteID = @siteID
set @importResult = null


-- ***********
-- clean table 
-- ***********
BEGIN TRY
	-- delete empty rows
	delete from #mc_Acct2Import where saleMemberNumber is null and saleAmount is null;

END TRY
BEGIN CATCH
	INSERT INTO #tblAcctErrors (msg)
	VALUES ('Unable to clean import table.')

	INSERT INTO #tblAcctErrors (msg)
	VALUES (left(error_message(),300))

	GOTO on_done
END CATCH


-- ***********
-- pay profile 
-- ***********
BEGIN TRY
	update tmp
	set tmp.MCCashGLAID = mp.GLAccountID, 
		tmp.MCPayProfileID = mp.profileID, 
		tmp.MCProfileCode = mp.profileCode, 
		tmp.MCPayGatewayID = mp.gatewayID,
		tmp.MCCashGLName = gl.accountname
	from #mc_Acct2Import as tmp
	inner join dbo.mp_profiles as mp on mp.profileID = @payProfileID 
		and mp.siteID = @siteID
		and mp.status = 'A'	
	inner join dbo.tr_glAccounts as gl on gl.glAccountID = mp.glAccountID
		and gl.status = 'A'

	IF EXISTS(
		select top 1 * 
		from #mc_Acct2Import 
		where MCCashGLAID is null or MCPayProfileID is null or MCProfileCode is null or MCPayGatewayID is null or MCCashGLName is null
		) BEGIN
		INSERT INTO #tblAcctErrors (msg)
		VALUES ('Unable to identify payment profile or cash GL account.')
			GOTO on_done
	END

END TRY
BEGIN CATCH
	INSERT INTO #tblAcctErrors (msg)
	VALUES ('Unable to validate payment profile.')

	INSERT INTO #tblAcctErrors (msg)
	VALUES (left(error_message(),300))

	GOTO on_done
END CATCH


-- ****************
-- required columns 
-- ****************
BEGIN TRY
	-- no blank descriptions
	update #mc_Acct2Import set saleDescription = '' where saleDescription is null;

	INSERT INTO #tblAcctErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing saleDescription.'
	FROM #mc_Acct2Import
	WHERE saleDescription = ''
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- ensure saleDate is datetime (allow nulls for this check)
	BEGIN TRY
		ALTER TABLE #mc_Acct2Import ALTER COLUMN saleDate datetime null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblAcctErrors (msg)
		VALUES ('The column saleDate contains invalid dates.')
			GOTO on_done
	END CATCH

	-- check for null saleDate
	BEGIN TRY
		ALTER TABLE #mc_Acct2Import ALTER COLUMN saleDate datetime not null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblAcctErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing the required saleDate.'
		FROM #mc_Acct2Import
		WHERE saleDate IS NULL
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done
	END CATCH

	-- match on member
	update tmp 
	set tmp.MCMemberID = m.memberid
	from #mc_Acct2Import as tmp 
	inner join dbo.ams_members as m on m.memberNumber = tmp.saleMemberNumber
		and m.orgID = @orgID
		and m.memberID = m.activeMemberID
		and m.status <> 'D'

	-- check for missing members
	BEGIN TRY
		ALTER TABLE #mc_Acct2Import ALTER COLUMN MCMemberID int not null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblAcctErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' does not match an existing member.'
		FROM #mc_Acct2Import
		WHERE MCMemberID IS NULL
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done
	END CATCH

	-- clean saleAmount
	update #mc_Acct2Import set saleAmount = dbo.fn_regexReplace(saleAmount,'[^0-9\.\-\(\)]','') where saleAmount is not null;

	-- ensure LastPrice is money (allow nulls for this check)
	BEGIN TRY
		ALTER TABLE #mc_Acct2Import ALTER COLUMN saleAmount money null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblAcctErrors (msg)
		VALUES ('The column saleAmount contains invalid amounts.')
			GOTO on_done
	END CATCH

	-- check for null or negative saleAmount
	BEGIN TRY
		ALTER TABLE #mc_Acct2Import ALTER COLUMN saleAmount money not null;
		ALTER TABLE #mc_Acct2Import ADD CONSTRAINT saleAmountamtCheck CHECK (saleAmount >= 0);
	END TRY
	BEGIN CATCH
		INSERT INTO #tblAcctErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' does not have a positive saleAmount amount.'
		FROM #mc_Acct2Import
		WHERE saleAmount IS NULL OR saleAmount < 0
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done
	END CATCH	

	-- no blank saleRevenueGL
	update #mc_Acct2Import set saleGLCode = '' where saleGLCode is null;

	INSERT INTO #tblAcctErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing saleGLCode.'
	FROM #mc_Acct2Import
	WHERE saleGLCode = ''
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- match on GL
	update tmp 
	set tmp.MCRevenueGLAID = gl.glAccountID,
		tmp.MCInvoiceProfileID = gl.invoiceProfileID
	from #mc_Acct2Import as tmp 
	inner join dbo.tr_GlAccounts as gl on gl.accountCode = tmp.saleGLCode
		and gl.orgID = @orgID
		and gl.status = 'A'
		and gl.accountTypeID = 3

	-- check for missing GLs
	BEGIN TRY
		ALTER TABLE #mc_Acct2Import ALTER COLUMN MCRevenueGLAID int not null;
		ALTER TABLE #mc_Acct2Import ALTER COLUMN MCInvoiceProfileID int not null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblAcctErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' does not match an existing active revenue GL account.'
		FROM #mc_Acct2Import
		WHERE MCRevenueGLAID IS NULL OR MCInvoiceProfileID IS NULL
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done
	END CATCH

	-- ensure these string cols are all same length for unpivot
	BEGIN TRY
		ALTER TABLE #mc_Acct2Import ALTER COLUMN saleDescription varchar(max) not null;
		ALTER TABLE #mc_Acct2Import ALTER COLUMN MCProfileCode varchar(max) not null;
		ALTER TABLE #mc_Acct2Import ALTER COLUMN MCCashGLName varchar(max) not null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblAcctErrors (msg)
		VALUES ('Unable to prepare certain columns for inserting into the queue.')
			GOTO on_done
	END CATCH

END TRY
BEGIN CATCH
	INSERT INTO #tblAcctErrors (msg)
	VALUES ('Unable to validate data in required columns.')

	INSERT INTO #tblAcctErrors (msg)
	VALUES (left(error_message(),300))

	GOTO on_done
END CATCH


-- ************************
-- generate result xml file 
-- ************************
on_done:
	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblAcctErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE)

RETURN 0
GO

CREATE PROC [dbo].[tr_importTransactionsOption2_import]
@siteID int,
@recordedByMemberID int,
@importResult xml OUTPUT

AS

SET NOCOUNT ON


declare @queueTypeID int, @statusInserting int, @statusReady int, @orgID int, @saledebitGLAccountID int, 
		@itemGroupUID uniqueidentifier
select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'importAcctOpt2'
select @statusInserting = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'insertingItems'
select @statusReady = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess'
select @orgID=orgID from dbo.sites where siteID = @siteID
set @importResult = null

select @saledebitGLAccountID = glaccountid 
from dbo.tr_GLAccounts 
where orgID = @orgID
and isSystemAccount = 1
and GLCode = 'ACCOUNTSRECEIVABLE'
and [status] = 'A'

-- all imported transactions get the same ItemGroupUID
select @itemGroupUID = NEWID()

DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	insert into platformQueue.dbo.tblQueueItems (itemUID, queueStatusID)
	select itemUID, @statusInserting
	from #mc_Acct2Import

	insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueInteger)
	select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtInt.columnValueInt
	from #mc_Acct2Import as tmp
	inner join (
		select rowID, columnname, columnValueInt
		from #mc_Acct2Import
		unpivot (columnValueInt for columnname in (MCMemberID, MCInvoiceProfileID, MCRevenueGLAID, MCPayProfileID, MCPayGatewayID, MCCashGLAID)) u
	) as unPvtInt on unPvtInt.rowID = tmp.rowID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtInt.columnname
	
	insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueString)
	select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtStr.columnValueString
	from #mc_Acct2Import as tmp
	inner join (
		select rowID, columnname, columnValueString
		from #mc_Acct2Import
		unpivot (columnValueString for columnname in (saleDescription, MCProfileCode, MCCashGLName)) u
	) as unPvtStr on unPvtStr.rowID = tmp.rowID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtStr.columnname

	insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueDate)
	select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, tmp.saleDate
	from #mc_Acct2Import as tmp
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'saleDate'

	insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueDecimal2)
	select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, tmp.saleAmount
	from #mc_Acct2Import as tmp
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'saleAmount'

	-- update queue item groups to show ready to process
	update qi WITH (UPDLOCK, HOLDLOCK)
	set qi.queueStatusID = @statusReady,
		qi.dateUpdated = getdate()
	from platformQueue.dbo.tblQueueItems as qi
	inner join #mc_Acct2Import as tmp on tmp.itemUID = qi.itemUID


	IF @TranCounter = 0
		COMMIT TRAN;
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	INSERT INTO #tblAcctErrors (msg)
	VALUES ('Unable to add transactions to the queue.')

	INSERT INTO #tblAcctErrors (msg)
	VALUES (left(error_message(),300))
END CATCH


on_done:
	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblAcctErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE)

RETURN 0
GO

CREATE PROC [dbo].[tr_importTransactionsOption2]
@siteid int, 
@recordedByMemberID int,
@payProfileID int,
@importResult xml OUTPUT

AS

SET NOCOUNT ON

IF OBJECT_ID('tempdb..#tblAcctErrors') IS NOT NULL 
	DROP TABLE #tblAcctErrors
CREATE TABLE #tblAcctErrors (rowid int IDENTITY(1,1), msg varchar(300))

-- ensure temp table exists
IF OBJECT_ID('tempdb..#mc_Acct2Import') IS NULL BEGIN
	INSERT INTO #tblAcctErrors (msg)
	VALUES ('Unable to locate the imported data for processing.')

	GOTO on_done
END

-- ensure all columns exist
set @importResult = null
EXEC dbo.tr_importTransactionsOption2_prepTable @importResult=@importResult OUTPUT
IF @importResult.value('count(/import/errors/error)','int') > 0
	GOTO on_done

-- validate data
set @importResult = null
EXEC dbo.tr_importTransactionsOption2_validate @siteID=@siteID, @payProfileID=@payProfileID, @importResult=@importResult OUTPUT
IF @importResult.value('count(/import/errors/error)','int') > 0
	GOTO on_done

-- import data
set @importResult = null
EXEC dbo.tr_importTransactionsOption2_import @siteID=@siteID, @recordedByMemberID=@recordedByMemberID, @importResult=@importResult OUTPUT

-- cleanup
on_done:
	IF OBJECT_ID('tempdb..#tblAcctErrors') IS NOT NULL 
		DROP TABLE #tblAcctErrors

RETURN 0
GO

CREATE PROC [dbo].[tr_importTransactionsOption2FromQueue]
@itemUID uniqueidentifier

AS

SET NOCOUNT ON

declare @itemGroupUID uniqueidentifier, @recordedByMemberID int, @siteID int, @rowID int, @orgID int, 
		@MCMemberID int, @MCInvoiceProfileID int, @MCRevenueGLAID int, @saleDate datetime, @saleDescription varchar(255), 
		@saleAmount decimal(9,2), @MCCashGLAID int, @MCPayProfileID int, @MCProfileCode varchar(20), @MCPayGatewayID int, 
		@MCCashGLName varchar(200), @invoiceID int, @invoiceNumber varchar(18), @saleDateMidnight datetime, 
		@saleTransactionID int, @batchCode varchar(40), @batchName varchar(400), @batchID int, @paymentDescription varchar(255),
		@historyID int, @paymentTransactionID int, @allocationTransactionID int

-- update status and get info
BEGIN TRY
	EXEC platformQueue.dbo.queue_setStatus @queueType='importAcctOpt2', @itemUID=@itemUID, @queueStatus='processingItem'

	select top 1 @itemGroupUID=itemGroupUID, @recordedByMemberID=recordedByMemberID, @siteID=siteID, @rowID=dataKey
	from platformQueue.dbo.tblQueueItemData
	where itemUID = @itemUID

	select @orgID=orgID from dbo.sites where siteID=@siteID

	IF OBJECT_ID('tempdb..#tmpAcctQueueData') IS NOT NULL 
		DROP TABLE #tmpAcctQueueData		
	select dc.columnname, qid.columnValueString, qid.columnValueDecimal2, qid.columnValueInteger, qid.columnvalueDate
	into #tmpAcctQueueData
	from platformQueue.dbo.tblQueueItemData as qid
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
	where qid.itemUID = @itemUID

END TRY
BEGIN CATCH
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH


-- setup transaction to import
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	select @MCMemberID = columnValueInteger from #tmpAcctQueueData where columnname = 'MCMemberID'
	select @MCInvoiceProfileID = columnValueInteger from #tmpAcctQueueData where columnname = 'MCInvoiceProfileID'
	select @MCRevenueGLAID = columnValueInteger from #tmpAcctQueueData where columnname = 'MCRevenueGLAID'
	select @MCCashGLAID = columnValueInteger from #tmpAcctQueueData where columnname = 'MCCashGLAID'
	select @MCPayProfileID = columnValueInteger from #tmpAcctQueueData where columnname = 'MCPayProfileID'
	select @MCPayGatewayID = columnValueInteger from #tmpAcctQueueData where columnname = 'MCPayGatewayID'
	select @saleDate = columnValueDate from #tmpAcctQueueData where columnname = 'saleDate'
	select @saleDateMidnight = DATEADD(dd,DATEDIFF(dd,0,@saleDate),0)
	select @saleDescription = columnValueString from #tmpAcctQueueData where columnname = 'saleDescription'
	select @saleAmount = columnValueDecimal2 from #tmpAcctQueueData where columnname = 'saleAmount'
	select @MCProfileCode = columnValueString from #tmpAcctQueueData where columnname = 'MCProfileCode'
	select @MCCashGLName = columnValueString from #tmpAcctQueueData where columnname = 'MCCashGLName'

	EXEC dbo.tr_createInvoice @invoiceProfileID=@MCInvoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
		@assignedToMemberID=@MCMemberID, @dateBilled=@saleDateMidnight, @dateDue=@saleDateMidnight, 
		@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT

	EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, @assignedToMemberID=@MCMemberID, 
		@recordedByMemberID=@recordedByMemberID, @statsSessionID=null, @status='Active', @detail=@saleDescription, 
		@parentTransactionID=null, @amount=@saleAmount, @transactionDate=@saleDate, @accrualDate=@saleDate, 
		@creditGLAccountID=@MCRevenueGLAID, @invoiceID=@invoiceID, @stateIDForTax=null, @bypassTax=1, 
		@bypassInvoiceMessage=1, @bypassAccrual=1, @xmlSchedule=null, @transactionID=@saleTransactionID OUTPUT
	
	EXEC dbo.tr_closeInvoice @enteredByMemberID=@recordedByMemberID, @invoiceIDList=@invoiceID

	IF @saleAmount > 0 BEGIN
	
		-- batch
		set @batchCode = convert(char(8),@saleDateMidnight,112) + replace(cast(@itemGroupUID as char(36)),'-','')
		set @batchName = convert(char(8),@saleDateMidnight,112) + ' ' + @MCProfileCode + ' ' + @MCCashGLName + ' ' + cast(@itemGroupUID as char(36))
		select @batchID = b.batchID
			from dbo.tr_batches as b
			where b.orgID = @orgID
			and b.batchCode = @batchCode
			and b.statusID = 1
		IF @batchID is null BEGIN
			EXEC dbo.tr_createBatch @orgID=@orgID, @payProfileID=@MCPayProfileID, @batchCode=@batchCode, @batchName=@batchName, 
				@controlAmt=0, @controlCount=0, @depositDate=@saleDateMidnight, @isSystemCreated=0, 
				@createdByMemberID=@recordedByMemberID, @batchID=@batchID OUTPUT
		END

		-- payment
		set @paymentDescription = 'Payment for ' + @saleDescription
		INSERT INTO dbo.tr_paymentHistory (paymentInfo, gatewayResponse, datePaid, statsSessionID, gatewayTransactionID, gatewayApprovalCode, 
			payerMemberID, memberPaymentProfileID, isSuccess)
		VALUES (
			'<payment gatewayid="' + cast(@MCPayGatewayID as varchar(10)) + '" profileid="' + cast(@MCPayProfileID as varchar(10)) + '"><args><x_amount>' + cast(@saleAmount as varchar(20)) + '</x_amount><x_description>' + replace(@paymentDescription,'&','&amp;') + '</x_description><fld_19_></fld_19_></args><gateway><x_amount>' + cast(@saleAmount as varchar(20)) + '</x_amount><x_description>' + replace(@paymentDescription,'&','&amp;') + '</x_description><fld_19_></fld_19_></gateway></payment>',
			'<response><rawresponse/><responsecode>1</responsecode><responsereasontext/><responsereasoncode/><transactionid/><approvalcode/><transactiondetail/><status>Active</status><glaccountid>' + cast(@MCCashGLAID as varchar(10)) + '</glaccountid><historyid/></response>',
			@saleDate, null, '', '', @MCMemberID, null, 1
		)
			select @historyID = SCOPE_IDENTITY()

		EXEC dbo.tr_createTransaction_payment @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, @assignedToMemberID=@MCMemberID, 
			@recordedByMemberID=@recordedByMemberID, @statsSessionID=null, @status='Active', @detail=@paymentDescription, 
			@amount=@saleAmount, @transactionDate=@saleDate, @debitGLAccountID=@MCCashGLAID, @profileID=@MCPayProfileID, 
			@historyID=@historyID, @batchid=@batchID, @transactionID=@paymentTransactionID OUTPUT

		-- allocation
		EXEC dbo.tr_createTransaction_allocation @recordedOnSiteID=@siteID, @recordedByMemberID=@recordedByMemberID, 
			@statsSessionID=null, @status='Active', @amount=@saleAmount, @transactionDate=@saleDate, 
			@paymentTransactionID=@paymentTransactionID, @saleTransactionID=@saleTransactionID, @ovBatchID=@batchID, 
			@transactionID=@allocationTransactionID OUTPUT

	END


	IF @TranCounter = 0
		COMMIT TRAN;
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH


-- update status
BEGIN TRY
	EXEC platformQueue.dbo.queue_setStatus @queueType='importAcctOpt2', @itemUID=@itemUID, @queueStatus='readyToNotify'

	IF OBJECT_ID('tempdb..#tmpAcctQueueData') IS NOT NULL 
		DROP TABLE #tmpAcctQueueData		
END TRY
BEGIN CATCH
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

CREATE PROC [dbo].[tr_autoPostImportedBatches]
AS

SET NOCOUNT ON

-- temp table needs to be there
IF OBJECT_ID('tempdb..#tmpNotify') IS NULL
	RETURN 0

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- get batches that need to be posted
	IF OBJECT_ID('tempdb..#tblBatches') IS NOT NULL 
		DROP TABLE #tblBatches		

	CREATE TABLE #tblBatches (batchID int, depositDate datetime, controlCount int, controlAmt money, actualCount int, actualAmt money)
	insert into #tblBatches (batchID, depositDate, controlCount, controlAmt, actualCount, actualAmt)
	select b.batchID, b.depositDate, b.controlCount, b.controlAmt, isnull(actual.actualCount,0), isnull(actual.actualAmount,0)
	from dbo.tr_batches as b
	inner join #tmpNotify as tmpN on convert(char(8),b.depositDate,112) + replace(cast(tmpN.itemGroupUID as char(36)),'-','') = b.batchCode
	outer apply dbo.fn_tr_getBatchActual(b.batchID) as actual
	where b.statusID <> 4
		
	-- post batches
	update b
	set b.statusID = 4
	from dbo.tr_batches as b
	inner join #tblBatches as tbl on tbl.batchID = b.batchID

	-- update counts if necessary
	update b
	set b.controlCount = tbl.actualCount
	from dbo.tr_batches as b
	inner join #tblBatches as tbl on tbl.batchID = b.batchID
	where b.controlCount <> tbl.actualCount

	-- update amounts if necessary
	update b
	set b.controlAmt = tbl.actualAmt
	from dbo.tr_batches as b
	inner join #tblBatches as tbl on tbl.batchID = b.batchID
	where b.controlAmt <> tbl.actualAmt

	-- reprocess any applicable conditions based on these just-posted batches	
	declare @tblConds TABLE (orgID int, conditionID int, batchDateLower datetime, batchDateUpper datetime)
	insert into @tblConds (orgID, conditionID, batchDateLower, batchDateUpper)
	select distinct c.orgID, c.conditionID, cv.conditionValue, cv2.conditionValue
	from dbo.ams_virtualGroupConditions as c
	inner join dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'batchDateLower'
	inner join dbo.ams_virtualGroupConditionValues as cv2 on cv2.conditionID = c.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k2 on k2.conditionKeyID = cv2.conditionKeyID and k2.conditionKey = 'batchDateUpper'
	where c.fieldcode IN ('acct_allocsum','acct_allocsumrecog')

	-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
	declare @itemGroupUID uniqueidentifier, @orgID int, @conditionIDList varchar(max)
	SELECT top 1 @orgID = orgID from @tblConds
	SELECT @conditionIDList = COALESCE(@conditionIDList + ',', '') + cast(c.conditionID as varchar(10)) 
		from @tblConds as c
		inner join #tblBatches as b on b.batchID = b.batchID
		where b.depositDate between c.batchDateLower and c.batchDateUpper
		group by c.conditionID
	IF @conditionIDList is not null
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList='', @conditionIDList=@conditionIDList, @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

use customApps
GO
ALTER PROC [dbo].[TAGD_importNationalData]
@batchDate datetime,
@recordedByMemberID int,
@statsSessionID int,
@pathToImport varchar(200), 
@pathToExport varchar(200),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

DECLARE @orgID int, @siteID int, @cmd varchar(600), @IDList VARCHAR(max), @finalMSG varchar(max), @emailtouse varchar(300),
	@flatfile varchar(200)
SELECT @orgID = membercentral.dbo.fn_getOrgIDFromOrgCode('TAGD')
SELECT @siteID = membercentral.dbo.fn_getSiteIDFromSiteCode('TAGD')



/* *********** */
/* DATA CHECKS */
/* *********** */
-- ensure IDs appear only once and set as primary key
BEGIN TRY
	ALTER TABLE ##tagd_nationals_alpha ADD CONSTRAINT PK__tagd_nationals_alpha PRIMARY KEY CLUSTERED (ID) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
END TRY
BEGIN CATCH
	SET @IDList = null
	SELECT @IDList = COALESCE(@IDList + ', ', '') + ID 
		from ##tagd_nationals_alpha
		group by ID
		having count(*) > 1
	SET @importResult = '<import date="" flatfile=""><errors><error msg="The file uploaded for Constituent Alpha Roster contains multiple rows for the following IDs: ' + @IDList + '. Ensure each ID appears only once in the file." severity="fatal" /></errors></import>'
	GOTO on_cleanup
END CATCH

-- ensure IDs appear only once and set as primary key
BEGIN TRY
	ALTER TABLE ##tagd_nationals_new ADD CONSTRAINT PK__tagd_nationals_new PRIMARY KEY CLUSTERED (ID) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
END TRY
BEGIN CATCH
	SET @IDList = null
	SELECT @IDList = COALESCE(@IDList + ', ', '') + ID 
		from ##tagd_nationals_new
		group by ID
		having count(*) > 1
	SET @importResult = '<import date="" flatfile=""><errors><error msg="The file uploaded for Constituent New Members contains multiple rows for the following IDs: ' + @IDList + '. Ensure each ID appears only once in the file." severity="fatal" /></errors></import>'
	GOTO on_cleanup
END CATCH

-- no missing PAID THRU dates for accounting
IF EXISTS (select top 1 ID from ##tagd_nationals_dues where [PAID THRU] is null) BEGIN
	SET @IDList = null
	SELECT @IDList = COALESCE(@IDList + ', ', '') + ID 
		from ##tagd_nationals_dues
		where [PAID THRU] is null
		group by ID
	SET @importResult = '<import date="" flatfile=""><errors><error msg="The file uploaded for Constituent Dues contains missing Paid Thru Dates for the following IDs: ' + @IDList + '. Ensure each row has a Paid Thru Date in the file." severity="fatal" /></errors></import>'
	GOTO on_cleanup
END

-- no negative amounts for accounting
IF EXISTS (select top 1 ID from ##tagd_nationals_dues where [Constituent PAID] < 0) BEGIN
	SET @IDList = null
	SELECT @IDList = COALESCE(@IDList + ', ', '') + ID 
		from ##tagd_nationals_dues
		where [Constituent PAID] < 0
		group by ID
	SET @importResult = '<import date="" flatfile=""><errors><error msg="The file uploaded for Constituent Dues contains negative CONSTITUENT PAID amounts for the following IDs: ' + @IDList + '. Negative amounts are not permitted." severity="fatal" /></errors></import>'
	GOTO on_cleanup
END



/* ****************** */
/* MEMBER DATA IMPORT */
/* ****************** */
-- flatten alpha table
IF OBJECT_ID('tempdb..##tagd_nationals_Members') IS NOT NULL
	DROP TABLE ##tagd_nationals_Members
select 
	m.prefix, 
	alpha.[FIRST NAME] as firstname, alpha.[MIDDLE NAME] as middlename, alpha.[LAST NAME] as lastname, alpha.SUFFIX as suffix, 
	cast(alpha.[DESIGNATION] as varchar(100)) as professionalSuffix, alpha.[ID] as memberNumber,
	m.company, 'Mailing Address' as BillingAddressType, 
	
	vw.[Mailing Address_attn], 
	alpha.[ADDRESS 1] as [Mailing Address_address1], alpha.[ADDRESS 2] as [Mailing Address_address2], alpha.[CITY] as [Mailing Address_city],
	alpha.[STATE PROVINCE] as [Mailing Address_stateprov], alpha.[ZIP] as [Mailing Address_postalCode], cast('' as varchar(50)) as [Mailing Address_county],
	cast('United States' as varchar(100)) as [Mailing Address_country], alpha.[WORK PHONE] as [Mailing Address_Phone], alpha.[FAX] as [Mailing Address_Fax],
	vw.[Mailing Address_Cell], vw.[Mailing Address_2nd Phone],
 
	vw.[Office Address_address1], vw.[Office Address_address2], vw.[Office Address_city], vw.[Office Address_stateprov],
	vw.[Office Address_postalCode], vw.[Office Address_county], vw.[Office Address_country], vw.[Office Address_Phone], 
	vw.[Office Address_Fax], vw.[Office Address_Cell], vw.[Office Address_2nd Phone],

	vw.[Other Address_address1], vw.[Other Address_address2], vw.[Other Address_city], vw.[Other Address_stateprov], vw.[Other Address_postalCode],
	vw.[Other Address_county], vw.[Other Address_country], vw.[Other Address_Phone], vw.[Other Address_Fax], vw.[Other Address_Cell],
	vw.[Other Address_2nd Phone],

	coalesce(new.[HOME ADDRESS 1],vw.[Home Address_address1]) as [Home Address_address1],
	coalesce(new.[HOME ADDRESS 2],vw.[Home Address_address2]) as [Home Address_address2],	
	coalesce(new.[HOME CITY],vw.[Home Address_city]) as [Home Address_city],	
	coalesce(new.[HOME STATE],vw.[Home Address_stateprov]) as [Home Address_stateprov], 
	coalesce(new.[HOME ZIP],vw.[Home Address_postalCode]) as [Home Address_postalCode],
	vw.[Home Address_county], 
	case when new.[HOME STATE] is not null then 'United States' else vw.[Home Address_country] end as [Home Address_country], 
	alpha.[HOME PHONE] as [Home Address_Phone], 
	coalesce(new.[HOME FAX],vw.[Home Address_Fax]) as [Home Address_Fax],
	vw.[Home Address_Cell], vw.[Home Address_2nd Phone],

	coalesce(new.WEBSITE,vw.[Business Website]) as [Business Website],
	alpha.Email as [Email],
	coalesce(new.[HOME EMAIL],vw.[Personal Email]) as [Personal Email],
	vw.[Other Email],

	alpha.[JOIN DATE] as [AGD Join Date],
	cast(alpha.[ID] as varchar(255)) as [AGD Member Number], 
	alpha.[PAID THRU] as [AGD Paid Thru Date],
	cast(alpha.[FIRST NAME] as varchar(255)) as [Badge Name], 

	cast(case
	when alpha.[MEMBER TYPE] = 'AC' and alpha.[CATEGORY DESCRIPTION] = 'Disability Waiver' then 'General Dentist|Dues Waived'
	when alpha.[MEMBER TYPE] IN ('AC','EM','HM') then 'General Dentist'
	when alpha.[MEMBER TYPE] = 'AF' then 'Affiliate'
	when alpha.[MEMBER TYPE] = 'AS' then 'General Dentist'
	when alpha.[MEMBER TYPE] = 'RE' then 'Retired'
	when alpha.[MEMBER TYPE] = 'ST' then 'Student'
	else '' end as varchar(max)) as [Contact Type],
	alpha.[BIRTH DATE] as [Date of Birth],
	replace(replace(alpha.[DESIGNATION],',','|'),' ','') as [Designation],
	case 
	when new.ETHNICITY is not null and new.ETHNICITY = 'African American' then 'African-American'
	when new.ETHNICITY is not null and new.ETHNICITY = 'Hispanic or Latino' then 'Hispanic'
	when new.ETHNICITY is not null and new.ETHNICITY = 'White or Caucasian' then 'Caucasian'
	when new.ETHNICITY is not null and new.ETHNICITY = 'Asian or Pacific IslANDer' then 'Asian'
	else coalesce(new.ETHNICITY,vw.[Ethnicity])	end as [Ethnicity],
	coalesce(new.GENDER,vw.[Gender]) as [Gender],
	alpha.[GRAD DATE] as [Graduation Date],
	coalesce(new.LICENSENO,vw.[License Number]) as [License Number],
	cast(case
	when alpha.[MEMBER TYPE] = 'AC' and alpha.[CATEGORY DESCRIPTION] = 'Disability Waiver' then 'Disability Waiver'
	when alpha.[MEMBER TYPE] = 'AC' and alpha.[CATEGORY DESCRIPTION] = 'Residency' then 'Resident'
	when alpha.[MEMBER TYPE] = 'AC' then 'Active General Dentist'
	when alpha.[MEMBER TYPE] = 'AF' then 'Affiliate'
	when alpha.[MEMBER TYPE] = 'AS' then 'Associate'
	when alpha.[MEMBER TYPE] = 'EM' then 'Emeritus'
	when alpha.[MEMBER TYPE] = 'HM' then 'Honorary Member'
	when alpha.[MEMBER TYPE] = 'RE' then 'Retired'
	when alpha.[MEMBER TYPE] = 'ST' then 'Student Member'
	else '' end as varchar(255)) as [MemberType],
	cast(case 
	when alpha.[DENTAL SCHOOL] = 'Texas A&M Health Science Center Baylor College of Dentistry' then null
	when alpha.[DENTAL SCHOOL] = 'University of Texas Dental Branch at Houston' then null
	when alpha.[DENTAL SCHOOL] = 'University of Texas Health Science Center at San Antonio Dental School' then null
	else alpha.[DENTAL SCHOOL] end as varchar(255)) as [Other Dental School],
	cast(case alpha.[DENTAL SCHOOL]
	when 'Texas A&M Health Science Center Baylor College of Dentistry' then 'Baylor College of Dentistry'
	when 'University of Texas Dental Branch at Houston' then 'UTHSCS Houston'
	when 'University of Texas Health Science Center at San Antonio Dental School' then 'UTHSC San Antonio'
	else null end as varchar(255)) as [Texas Dental School],
	coalesce(new.[PRACTICE ENVIRONMENT],vw.[Type of Practice]) as [Type of Practice]
into ##tagd_nationals_Members
from ##tagd_nationals_alpha as alpha
left outer join membercentral.dbo.ams_members as m
	inner join membercentral.dbo.vw_memberdata_TAGD as vw on vw.memberID = m.memberID
	on m.orgID = @orgID and m.membernumber = alpha.[ID] and m.status <> 'D' and m.memberid = m.activeMemberID
left outer join ##tagd_nationals_new as new on new.ID = alpha.ID

-- get active members in database now so we dont inactivate them
insert into ##tagd_nationals_Members (
	[prefix],[firstname],[middlename],[lastname],[suffix],[professionalSuffix],[memberNumber],[company],
	[BillingAddressType], 
	[Mailing Address_attn],[Mailing Address_address1],[Mailing Address_address2],
	[Mailing Address_city],[Mailing Address_stateprov],[Mailing Address_postalCode],[Mailing Address_county],[Mailing Address_country],[Mailing Address_Phone],
	[Mailing Address_Fax],[Mailing Address_Cell],[Mailing Address_2nd Phone],[Office Address_address1],[Office Address_address2],[Office Address_city],
	[Office Address_stateprov],[Office Address_postalCode],[Office Address_county],[Office Address_country],[Office Address_Phone],[Office Address_Fax],
	[Office Address_Cell],[Office Address_2nd Phone],[Other Address_address1],[Other Address_address2],[Other Address_city],[Other Address_stateprov],
	[Other Address_postalCode],[Other Address_county],[Other Address_country],[Other Address_Phone],[Other Address_Fax],[Other Address_Cell],
	[Other Address_2nd Phone],[Home Address_address1],[Home Address_address2],[Home Address_city],[Home Address_stateprov],[Home Address_postalCode],
	[Home Address_county],[Home Address_country],[Home Address_Phone],[Home Address_Fax],[Home Address_Cell],[Home Address_2nd Phone],[Business Website],
	[Email],[Personal Email],[Other Email],[AGD Join Date],[AGD Member Number],[AGD Paid Thru Date],[Badge Name],[Contact Type],[Date of Birth],[Designation],
	[Ethnicity],[Gender],[Graduation Date],[License Number],[MemberType],[Other Dental School],[Texas Dental School],[Type of Practice]
)
select m.prefix, m.firstname, m.middlename, m.lastname, m.suffix, m.professionalSuffix, m.membernumber, m.company, 
	'Mailing Address', 
	vw.[Mailing Address_attn], vw.[Mailing Address_address1], vw.[Mailing Address_address2],
	vw.[Mailing Address_city],vw.[Mailing Address_stateprov],vw.[Mailing Address_postalCode],vw.[Mailing Address_county],vw.[Mailing Address_country],vw.[Mailing Address_Phone],
	vw.[Mailing Address_Fax],vw.[Mailing Address_Cell],vw.[Mailing Address_2nd Phone],vw.[Office Address_address1],vw.[Office Address_address2],vw.[Office Address_city],
	vw.[Office Address_stateprov],vw.[Office Address_postalCode],vw.[Office Address_county],vw.[Office Address_country],vw.[Office Address_Phone],vw.[Office Address_Fax],
	vw.[Office Address_Cell],vw.[Office Address_2nd Phone],vw.[Other Address_address1],vw.[Other Address_address2],vw.[Other Address_city],vw.[Other Address_stateprov],
	vw.[Other Address_postalCode],vw.[Other Address_county],vw.[Other Address_country],vw.[Other Address_Phone],vw.[Other Address_Fax],vw.[Other Address_Cell],
	vw.[Other Address_2nd Phone],vw.[Home Address_address1],vw.[Home Address_address2],vw.[Home Address_city],vw.[Home Address_stateprov],vw.[Home Address_postalCode],
	vw.[Home Address_county],vw.[Home Address_country],vw.[Home Address_Phone],vw.[Home Address_Fax],vw.[Home Address_Cell],vw.[Home Address_2nd Phone],vw.[Business Website],
	vw.[Email],vw.[Personal Email],vw.[Other Email],vw.[AGD Join Date],vw.[AGD Member Number],vw.[AGD Paid Thru Date],vw.[Badge Name],vw.[Contact Type],vw.[Date of Birth],vw.[Designation],
	vw.[Ethnicity],vw.[Gender],vw.[Graduation Date],vw.[License Number],vw.[MemberType],vw.[Other Dental School],vw.[Texas Dental School],vw.[Type of Practice]
from membercentral.dbo.ams_members as m
inner join membercentral.dbo.vw_memberdata_TAGD as vw on vw.memberID = m.memberID
where m.orgID = @orgID
and m.status = 'A'
and m.memberid = m.activeMemberID
and m.memberTypeID = 2
and not exists (select membernumber from ##tagd_nationals_Members where membernumber = m.membernumber)

-- add rowID
ALTER TABLE ##tagd_nationals_Members ADD rowIDtemp int IDENTITY(1,1), rowID int NULL;
update ##tagd_nationals_Members set rowID = rowIDtemp;
ALTER TABLE ##tagd_nationals_Members DROP COLUMN rowIDtemp;

-- move data to holding tables
EXEC memberCentral.dbo.ams_importMemberData_tempToHolding @orgid=@orgID, @tmptbl='##tagd_nationals_Members', @pathToExport=@pathToExport, @importResult=@importResult OUTPUT

-- if no errors, run member import
IF @importResult.value('count(/import/errors/error)','int') = 0
BEGIN
	SELECT @flatfile = @importResult.value('(/import/@flatfile)[1]','varchar(160)')
	EXEC membercentral.dbo.ams_importMemberData_holdingToPerm @orgID=@orgID, @flatfile=@flatfile

	declare @recordTypeID int
	select @recordTypeID = recordTypeID  from membercentral.dbo.ams_recordTypes where orgID = @orgID and recordTypeCode = 'Individual'

	update
		m
	set
		m.recordTypeID = @recordTypeID
	from
		memberCentral.dbo.ams_members m
		inner join ##tagd_nationals_alpha nm on
			m.memberNumber = nm.id
			and m.orgID = @orgID

	DECLARE @smtpserver varchar(20)
	SELECT @smtpserver = smtpserver from membercentral.dbo.fn_getServerSettings()		

	BEGIN TRY
		EXEC membercentral.dbo.ams_importMemberDataHoldingResultReport @importResult=@importResult, @finalMSG=@finalMSG OUTPUT
		EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject='TAGD Membership Import Results',
			@message=@finalMSG, @priority='normal', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
		SELECT @emailtouse = emailImportResults from membercentral.dbo.organizations where orgID = @orgID	
		IF len(@emailtouse) > 0 BEGIN
			EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to=@emailtouse,
				@cc=null, @bcc='<EMAIL>', @subject='TAGD Membership Import Results',
				@message=@finalMSG, @priority='normal', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
		END
	END TRY
	BEGIN CATCH      
		DECLARE @ErrorMessage VARCHAR(max)      
		DECLARE @ErrorSeverity INT      
		DECLARE @ErrorState INT        
		SELECT @ErrorMessage = 'Error running TAGD Membership Import' + char(13) + char(10) + ERROR_MESSAGE(),   
			@ErrorSeverity = ERROR_SEVERITY(),   
			@ErrorState = ERROR_STATE()        
		EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject='Error Importing TAGD Data',
			@message=@ErrorMessage, @priority='high', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END CATCH


	/* ***************** */
	/* ACCOUNTING IMPORT */
	/* ***************** */
	-- prep acct table (ignore $0)
	IF OBJECT_ID('tempdb..#mc_Acct2Import') IS NOT NULL
		DROP TABLE #mc_Acct2Import
	select rowID,
		'TAGD Dues ' + cast(year([PAID THRU]) as varchar(4)) as SaleDescription,
		@batchDate as saleDate,
		[ID] as saleMemberNumber,
		[Constituent PAID] as saleAmount,
		'4010' as saleGLCode
	into #mc_Acct2Import
	from ##tagd_nationals_dues
	where [Constituent PAID] <> 0

	declare @payProfileID int
	select @payProfileID = profileID
	from membercentral.dbo.mp_profiles
	where profileCode = 'AGDimport'
	and siteID = @siteID
	and [status] = 'A'	

	set @importResult = null
	EXEC membercentral.dbo.tr_importTransactionsOption2 @siteID=@siteID, @recordedByMemberID=@recordedByMemberID, @payProfileID=@payProfileID, @importResult=@importResult OUTPUT

	IF @importResult.value('count(/import/errors/error)','int') = 0
	BEGIN
		/* ******************* */
		/* SUBSCRIPTION IMPORT */
		/* ******************* */
		-- Start prep work for importing subs
		IF OBJECT_ID('tempdb..#tagd_sub_renewals') IS NOT NULL
			DROP TABLE #tagd_sub_renewals
		IF OBJECT_ID('tempdb..#tagd_sub_newmembers') IS NOT NULL
			DROP TABLE #tagd_sub_newmembers
		IF OBJECT_ID('tempdb..#tagd_sub_newComponent') IS NOT NULL
			DROP TABLE #tagd_sub_newComponent
		IF OBJECT_ID('tempdb..#tagd_sub_switchedComponent') IS NOT NULL
			DROP TABLE #tagd_sub_switchedComponent
		IF OBJECT_ID('tempdb..#tagd_memberType') IS NOT NULL
			DROP TABLE #tagd_memberType
		IF OBJECT_ID('tempdb..#mc_SubImport') IS NOT NULL
			DROP TABLE #mc_SubImport
		IF OBJECT_ID('tempdb..#tagd_sub_expireComponent') IS NOT NULL
			DROP TABLE #tagd_sub_expireComponent
		IF OBJECT_ID('tempdb..#tagd_sub_noSubCreated') IS NOT NULL
			DROP TABLE #tagd_sub_noSubCreated

		declare @subscriberIDList varchar(8000), @membershipSubscriptionType varchar(100), 
			@componentSubscriptionType varchar(100), @membershipSubscriptionName varchar(100),
			@thismemberid int, @thissubscriptionID int, @thisparentSubscriberID int, 
			@thisRFID int, @thisGLAccountID int, @thisstatus varchar(1), @thissubStartDate datetime,
			@thissubEndDate datetime, @thisgraceEndDate datetime, @thispcfree bit, 
			@thisactivationOptionCode char(1), @thisrecordedByMemberID int,  @thisbypassQueue bit,
			@thissubscriberID int, @thisexistingComponentSubscriberID int, @trashID int,
			@autoID int, @currentTimeStamp datetime
		set @currentTimeStamp = getdate()
		set @membershipSubscriptionType = 'Membership Dues'
		set @componentSubscriptionType = 'Components'
		set @membershipSubscriptionName = 'TAGD Member'

		CREATE TABLE #tagd_sub_renewals (memberID int, memberNumber varchar(100), subStartDate datetime,
			subEndDate datetime, rootSubscriptionID int, componentSubscriptionID int, treecode uniqueIdentifier)
		CREATE TABLE #tagd_sub_newmembers (memberID int, memberNumber varchar(100), subStartDate datetime,
			subEndDate datetime, rootSubscriptionID int, componentSubscriptionID int, treecode uniqueIdentifier)
		CREATE TABLE #tagd_sub_newComponent (autoID int IDENTITY(1,1) PRIMARY KEY, memberID int,
			memberNumber varchar(100), existingRootSubscriberID int, existingComponentSubscriberID int,
			subStartDate datetime, subEndDate datetime, graceEndDate datetime, rootSubscriptionID int,
			componentSubscriptionID int, rfid int, GLAccountID int, status char(1), pcfree bit, 
			activationOptionCode varchar(1), existingComponentSubscriptionID int)
		CREATE TABLE #tagd_sub_switchedComponent (autoID int IDENTITY(1,1) PRIMARY KEY, memberID int,
			memberNumber varchar(100), existingRootSubscriberID int, existingComponentSubscriberID int,
			subStartDate datetime, subEndDate datetime, graceEndDate datetime, rootSubscriptionID int,
			componentSubscriptionID int, rfid int, GLAccountID int, status char(1), pcfree bit, 
			activationOptionCode varchar(1), existingComponentSubscriptionID int)
		CREATE TABLE #tagd_memberType (tempMemberTypeId int NOT NULL identity(1,1),
			ValueForMemberTypeCustomField varchar(255) NOT NULL DEFAULT (''), 
			MemberTypeValueFromAGDAlphaRoster varchar(10) NOT NULL DEFAULT (''), 
			CategoryDescriptionValueFromAGDAlphaRoster varchar(255) NULL DEFAULT (''), 
			MembershipDuesTAGDMemberSubRate varchar(255) NOT NULL DEFAULT ('')
		)
		CREATE TABLE #tagd_sub_expireComponent (
			autoID int IDENTITY(1,1) 
			PRIMARY KEY, memberID int,
			memberNumber varchar(100), 
			existingRootSubscriberID int, 
			existingComponentSubscriberID int,
			existingComponentSubscriptionID int,
			rootSubscriptionID int,
			rfid int, GLAccountID int, status char(1), pcfree bit, 
			activationOptionCode varchar(1)
		)

		CREATE TABLE #tagd_sub_noSubCreated (
			autoID int IDENTITY(1,1) PRIMARY KEY, 
			memberNumber varchar(100), 
			fullName varchar(100), 
			paidThruDate varchar(20),
			subEndDate varchar(20)
		)

		insert into #tagd_sub_renewals (memberID, memberNumber, subStartDate, subEndDate, rootSubscriptionID, 
			componentSubscriptionID, treecode)
		select activeMember.memberID, na.id, 
			newStartDate = case 
				when (isnull(na.[JOIN DATE],'1/1/1900') > cast (cast(YEAR(na.[PAID THRU]) as nvarchar)+'0101' as datetime)) and (na.[JOIN DATE] > DATEADD(dd, DATEDIFF(dd, 0,dateadd(d,1,max(ss.subenddate))), 0))
					then isnull(na.[JOIN DATE],'1/1/1900')
				when (max(ss.subenddate) > cast (cast(YEAR(na.[PAID THRU]) as nvarchar)+'0101' as datetime)) and (DATEADD(dd, DATEDIFF(dd, 0,dateadd(d,1,max(ss.subenddate))), 0) > isnull(na.[JOIN DATE],'1/1/1900'))
					then DATEADD(dd, DATEDIFF(dd, 0,dateadd(d,1,max(ss.subenddate))), 0)
				else
					cast (cast(YEAR(na.[PAID THRU]) as nvarchar)+'0101' as datetime) 
				end,
			newSubEndDate = dateadd(ms,-3,dateadd(day,1,[Paid Thru])),
			subs.subscriptionID as rootSubscriptionID, 
			csubs.subscriptionID as componentSubscripionID,
			treecode = newid()
		from membercentral.dbo.sub_types t
		inner join membercentral.dbo.sub_subscriptions subs on subs.typeID = t.typeID
			and t.typeName = @membershipSubscriptionType
			and subs.subscriptionName = @membershipSubscriptionName
			and t.siteID = @siteID
		inner join membercentral.dbo.sub_subscribers ss on ss.subscriptionID = subs.subscriptionID
			and ss.parentSubscriberID is null
		inner join membercentral.dbo.sub_statuses st on st.statusID = ss.statusID
			and st.statuscode in ('A','I','P')
		inner join membercentral.dbo.ams_members m on m.memberID = ss.memberID
		inner join membercentral.dbo.ams_members activeMember on activeMember.memberID = m.activeMemberID
			and activeMember.status in ('A','I')
		--make sure we have subscriberID with the latest end date
		left outer join membercentral.dbo.sub_subscribers otherterms_ss
			inner join membercentral.dbo.sub_statuses otherterms_st on otherterms_st.statusID = otherterms_ss.statusID
				and otherterms_st.statuscode in ('A','I','P')
			inner join membercentral.dbo.ams_members otherterms_m on otherterms_m.memberID = otherterms_ss.memberID
			on ss.subscriptionID = otherterms_ss.subscriptionID
				and ss.subenddate < otherterms_ss.subenddate
				and otherterms_m.activeMemberID = activeMember.memberID
		inner join ##tagd_nationals_alpha na on na.id = activeMember.membernumber
			and na.[Paid Thru] is not null
			and ss.subenddate < na.[paid thru]
			and na.[paid thru] > @currentTimeStamp
		inner join membercentral.dbo.sub_types ct on ct.siteID = t.siteID
			and ct.typeName = @componentSubscriptionType
		left outer join membercentral.dbo.sub_subscriptions csubs on csubs.typeID = ct.typeID
			and csubs.subscriptionName = na.[COMPONENT TITLE]
		where otherterms_ss.subscriberID is null
		group by activeMember.memberID,na.id,  na.[paid thru], na.[JOIN DATE], subs.subscriptionID, csubs.subscriptionID
		order by na.[JOIN DATE] desc

		insert into #tagd_sub_newmembers (memberID, memberNumber,subStartDate,subEndDate, rootSubscriptionID, componentSubscriptionID,treecode)
		select 
			activeMember.memberID,
			na.id,
			newStartDate = case 
				when (isnull(na.[JOIN DATE],'1/1/1900') > cast (cast(YEAR(na.[PAID THRU]) as nvarchar)+'0101' as datetime))
					then isnull(na.[JOIN DATE],'1/1/1900')
				else
					cast (cast(YEAR(na.[PAID THRU]) as nvarchar)+'0101' as datetime) 
				end,
			newSubEndDate = dateadd(ms,-3,dateadd(day,1,[Paid Thru])),
			subs.subscriptionID as rootSubscriptionID, 
			csubs.subscriptionID as componentSubscripionID,
			treecode = newid()
		from 
			##tagd_nationals_alpha na
			inner join membercentral.dbo.ams_members activeMember
				on activeMember.memberNumber = na.id
				and activeMember.orgID = @orgID
				and activeMember.status in ('A','I')
				and activeMember.memberID = activeMember.activeMemberID
				and na.[paid thru] > @currentTimeStamp
			inner join membercentral.dbo.ams_members m
				on activeMember.memberID = m.activeMemberID
			inner join membercentral.dbo.sub_types t
				on t.typeName = @membershipSubscriptionType
				and t.siteID = @siteID
			inner join membercentral.dbo.sub_subscriptions subs
				on subs.typeID = t.typeID
				and subs.subscriptionName = @membershipSubscriptionName
			left outer join membercentral.dbo.sub_subscribers ss
				inner join membercentral.dbo.sub_statuses st
					on st.statusID = ss.statusID
					and st.statuscode in ('A','I','P')
					and ss.parentSubscriberID is null
			on m.memberID = ss.memberID
				and ss.subscriptionID = subs.subscriptionID
			inner join membercentral.dbo.sub_types ct
				on ct.siteID = @siteID
				and ct.typeName = @componentSubscriptionType
			left outer join membercentral.dbo.sub_subscriptions csubs
				on csubs.typeID = ct.typeID
				and csubs.subscriptionName = na.[COMPONENT TITLE]
		group by 
			activeMember.memberID, na.id,  na.[paid thru], na.[JOIN DATE], subs.subscriptionID, csubs.subscriptionID
		having 
			max(isnull(ss.subscriberID,0)) = 0
		order by 
			na.[JOIN DATE] desc

		insert into #tagd_sub_newComponent (
			memberID, 
			memberNumber,
			subStartDate,
			subEndDate, 
			graceEndDate, 
			rootSubscriptionID, 
			componentSubscriptionID, 
			existingRootSubscriberID, 
			existingComponentSubscriberID,
			existingComponentSubscriptionID
		)
		select 
			activeMember.memberID,
			na.id,
			-- case statement to handle changing future Accepted subscriptions
			newStartDate = case 
				when (@currentTimeStamp > ss.substartDate)
					then @currentTimeStamp
				else
					ss.substartDate
				end,
			newSubEndDate = case 
				when (@currentTimeStamp > ss.subEndDate)
					then cast (cast(YEAR(@currentTimeStamp) as nvarchar)+'1231' as datetime)
				else
					ss.subEndDate
				end,
			graceEndDate = cast (cast(YEAR(ss.subEndDate) + 1 as nvarchar)+'0331' as datetime),
			subs.subscriptionID as rootSubscriptionID, 
			correct_csubs.subscriptionID as componentSubscripionID,
			ss.rootSubscriberID as existingRootSubscriberID, 
			current_css.subscriberID as existingComponentSubscriberID,
			current_css.subscriptionID as existingComponentSubscriptionID
		from 
			membercentral.dbo.sub_types t
			inner join membercentral.dbo.sub_subscriptions subs
				on subs.typeID = t.typeID
				and t.typeName = @membershipSubscriptionType
				and subs.subscriptionName = @membershipSubscriptionName
				and t.siteID = @siteID
			inner join membercentral.dbo.sub_subscribers ss
				on ss.subscriptionID = subs.subscriptionID
				and ss.parentSubscriberID is null
			inner join membercentral.dbo.sub_statuses st
				on st.statusID = ss.statusID
				and st.statuscode in ('A','I','P')
			inner join membercentral.dbo.ams_members m
				on m.memberID = ss.memberID
			inner join membercentral.dbo.ams_members activeMember
				on activeMember.memberID = m.activeMemberID
				and activeMember.status in ('A','I')
			inner join ##tagd_nationals_alpha na
				on na.id = activeMember.membernumber
				and na.[Paid Thru] is not null
			inner join membercentral.dbo.sub_types ct
				on ct.siteID = t.siteID
				and ct.typeName = @componentSubscriptionType
			inner join membercentral.dbo.sub_subscriptions correct_csubs
				on correct_csubs.typeID = ct.typeID
				and correct_csubs.subscriptionName = na.[COMPONENT TITLE]
			left outer join membercentral.dbo.sub_subscribers current_css
				inner join membercentral.dbo.sub_statuses current_cst
					on current_cst.statusID = current_css.statusID
					and current_cst.statuscode in ('A','I','P')
				on current_css.parentSubscriberID = ss.subscriberID
			and correct_csubs.subscriptionID = current_css.subscriptionID
		where
			current_css.subscriptionID is null
		group by 
			current_cst.statuscode, activeMember.memberID,na.id,  na.[paid thru], na.[JOIN DATE], subs.subscriptionID, 
			correct_csubs.subscriptionID, ss.rootSubscriberID, current_css.subscriberID,
			current_css.subscriptionID, ss.substartDate, ss.subEndDate
		order by na.[JOIN DATE] desc

		insert into #tagd_sub_switchedComponent (
			memberID, 
			memberNumber,
			subStartDate,
			subEndDate, 
			graceEndDate, 
			rootSubscriptionID, 
			componentSubscriptionID, 
			existingRootSubscriberID, 
			existingComponentSubscriberID,
			existingComponentSubscriptionID
		)
		select 
			activeMember.memberID,
			na.id,
			-- case statement to handle changing future Accepted subscriptions
			newStartDate = case 
				when (@currentTimeStamp > ss.substartDate)
					then @currentTimeStamp
				else
					ss.substartDate
				end,
			newSubEndDate = case 
				when (@currentTimeStamp > ss.subEndDate)
					then cast (cast(YEAR(@currentTimeStamp) as nvarchar)+'1231' as datetime)
				else
					ss.subEndDate
				end,
			graceEndDate = cast (cast(YEAR(ss.subEndDate) + 1 as nvarchar)+'0331' as datetime),
			subs.subscriptionID as rootSubscriptionID, 
			correct_csubs.subscriptionID as componentSubscripionID,
			ss.rootSubscriberID as existingRootSubscriberID, 
			current_css.subscriberID as existingComponentSubscriberID,
			current_css.subscriptionID as existingComponentSubscriptionID
		from 
			membercentral.dbo.sub_types t
			inner join membercentral.dbo.sub_subscriptions subs
				on subs.typeID = t.typeID
				and t.typeName = @membershipSubscriptionType
				and subs.subscriptionName = @membershipSubscriptionName
				and t.siteID = @siteID
			inner join membercentral.dbo.sub_subscribers ss
				on ss.subscriptionID = subs.subscriptionID
				and ss.parentSubscriberID is null
			inner join membercentral.dbo.sub_statuses st
				on st.statusID = ss.statusID
				and st.statuscode in ('A','I','P')
			inner join membercentral.dbo.ams_members m
				on m.memberID = ss.memberID
			inner join membercentral.dbo.ams_members activeMember
				on activeMember.memberID = m.activeMemberID
				and activeMember.status in ('A','I')
			inner join ##tagd_nationals_alpha na
				on na.id = activeMember.membernumber
				and na.[Paid Thru] is not null
			inner join membercentral.dbo.sub_types ct
				on ct.siteID = t.siteID
				and ct.typeName = @componentSubscriptionType
			inner join membercentral.dbo.sub_subscriptions correct_csubs
				on correct_csubs.typeID = ct.typeID
				and correct_csubs.subscriptionName = na.[COMPONENT TITLE]
			inner join membercentral.dbo.sub_subscribers current_css
				on current_css.parentSubscriberID = ss.subscriberID
				and correct_csubs.subscriptionID <> current_css.subscriptionID
			inner join membercentral.dbo.sub_statuses current_cst
				on current_cst.statusID = current_css.statusID
				and current_cst.statuscode in ('A','I','P')
		group by 
			activeMember.memberID,na.id,  na.[paid thru], na.[JOIN DATE], subs.subscriptionID, 
			correct_csubs.subscriptionID, ss.rootSubscriberID, current_css.subscriberID,
			current_css.subscriptionID, ss.substartDate, ss.subEndDate
		order by na.[JOIN DATE] desc

		insert into #tagd_sub_expireComponent (
			memberID, 
			memberNumber,
			rootSubscriptionID, 
			existingComponentSubscriptionID, 
			existingRootSubscriberID, 
			existingComponentSubscriberID
		)			
		select 
			activeMember.memberID,
			na.id,
			subs.subscriptionID as rootSubscriptionID, 
			current_css.subscriptionID as existingComponentSubscriptionID,
			ss.rootSubscriberID as existingRootSubscriberID, 
			current_css.subscriberID as existingComponentSubscriberID
		from 
			membercentral.dbo.sub_types t
			inner join membercentral.dbo.sub_subscriptions subs
				on subs.typeID = t.typeID
				and t.typeName = @membershipSubscriptionType
				and subs.subscriptionName = @membershipSubscriptionName
				and t.siteID = @siteID
			inner join membercentral.dbo.sub_subscribers ss
				on ss.subscriptionID = subs.subscriptionID
				and ss.parentSubscriberID is null
			inner join membercentral.dbo.sub_statuses st
				on st.statusID = ss.statusID
				and st.statuscode in ('A','I','P')
			inner join membercentral.dbo.ams_members m
				on m.memberID = ss.memberID
			inner join membercentral.dbo.ams_members activeMember
				on activeMember.memberID = m.activeMemberID
				and activeMember.status in ('A','I')
			inner join ##tagd_nationals_alpha na
				on na.id = activeMember.membernumber
				and na.[Paid Thru] is not null
			inner join membercentral.dbo.sub_types ct
				on ct.siteID = t.siteID
				and ct.typeName = @componentSubscriptionType
			inner join membercentral.dbo.sub_subscriptions correct_csubs
				on correct_csubs.typeID = ct.typeID
				and correct_csubs.subscriptionName <> na.[COMPONENT TITLE]
			inner join membercentral.dbo.sub_subscribers current_css
				on current_css.parentSubscriberID = ss.subscriberID
				and correct_csubs.subscriptionID = current_css.subscriptionID
			inner join membercentral.dbo.sub_statuses current_cst
				on current_cst.statusID = current_css.statusID
				and current_cst.statuscode in ('A','I','P')
			left outer join #tagd_sub_switchedComponent sc on
				current_css.subscriberID = sc.existingComponentSubscriberID
		where
			sc.existingComponentSubscriberID is null
		group by 
			activeMember.memberID,na.id, subs.subscriptionID, 
			current_css.subscriptionID, ss.rootSubscriberID, current_css.subscriberID,
			 ss.substartDate, ss.subEndDate
		union
			select 
				renewals.memberID,
				renewals.memberNumber,
				renewals.rootSubscriptionID, 
				sc.existingComponentSubscriptionID,
				sc.existingRootSubscriberID, 
				sc.existingComponentSubscriberID
			from 
				#tagd_sub_renewals  renewals
				inner join #tagd_sub_switchedComponent sc on 
					sc.rootSubscriptionID = renewals.rootSubscriptionID
					and sc.memberNumber = renewals.memberNumber
		order by 
			activeMember.memberID

		update sc set
			GLAccountID = case
				when subs.allowRateGLAccountOverride = 1 then isnull(r.GLAccountID,subs.GLAccountID) 
				else subs.GLAccountID
			end, 
			rfid=rf.rfID,
			pcfree=0, 
			activationOptionCode = sao.subActivationCode,
			Status = case 
				when subStartDate > @currentTimeStamp then 'P'
				else 'A'
			end
		from 
			#tagd_sub_switchedComponent sc
			inner join membercentral.dbo.sub_subscriptions subs
				on subs.subscriptionID = sc.componentSubscriptionID
			inner join membercentral.dbo.sub_rateSchedules as rs 
				on subs.scheduleID = rs.scheduleID 
				and rs.status = 'A'
			inner join membercentral.dbo.sub_rates r
				on r.scheduleID = rs.scheduleID
				and r.status = 'A'
				and r.isRenewalRate = 0 
				and r.rateName = 'Annual Term'
			inner join membercentral.dbo.sub_rateFrequencies rf on
				rf.rateID = r.rateID
				and rf.status = 'A'
			inner join membercentral.dbo.sub_frequencies f on
				f.frequencyID = rf.frequencyID
				and f.status = 'A'
			inner join membercentral.dbo.sub_rateFrequenciesMerchantProfiles as rfmp on 
				rfmp.rfid = rf.rfID 
				and rfmp.status = 'A' 
			inner join membercentral.dbo.mp_profiles as mp on 
				mp.profileID = rfmp.profileID 
				and mp.status = 'A' 
			inner join membercentral.dbo.sub_activationOptions sao
				on sao.subActivationID = subs.subAlternateActivationID

		update nc set
			GLAccountID = case
				when subs.allowRateGLAccountOverride = 1 then isnull(r.GLAccountID,subs.GLAccountID) 
				else subs.GLAccountID
			end, 
			rfid=rf.rfID,
			pcfree=0, 
			activationOptionCode = sao.subActivationCode,
			Status = case 
				when subStartDate > @currentTimeStamp then 'P'
				else 'A'
			end
		from 
			#tagd_sub_newComponent nc
			inner join membercentral.dbo.sub_subscriptions subs
				on subs.subscriptionID = nc.componentSubscriptionID
			inner join membercentral.dbo.sub_rateSchedules as rs 
				on subs.scheduleID = rs.scheduleID 
				and rs.status = 'A'
			inner join membercentral.dbo.sub_rates r
				on r.scheduleID = rs.scheduleID
				and r.status = 'A'
				and r.isRenewalRate = 0 
				and r.rateName = 'Annual Term'
			inner join membercentral.dbo.sub_rateFrequencies rf on
				rf.rateID = r.rateID
				and rf.status = 'A'
			inner join membercentral.dbo.sub_frequencies f on
				f.frequencyID = rf.frequencyID
				and f.status = 'A'
			inner join membercentral.dbo.sub_rateFrequenciesMerchantProfiles as rfmp on 
				rfmp.rfid = rf.rfID 
				and rfmp.status = 'A' 
			inner join membercentral.dbo.mp_profiles as mp on 
				mp.profileID = rfmp.profileID 
				and mp.status = 'A' 
			inner join membercentral.dbo.sub_activationOptions sao
				on sao.subActivationID = subs.subAlternateActivationID

		insert into #tagd_memberType (
			ValueForMemberTypeCustomField,
			MemberTypeValueFromAGDAlphaRoster,
			CategoryDescriptionValueFromAGDAlphaRoster,
			MembershipDuesTAGDMemberSubRate
		)
		select 'Active Member', 'AC', '',	'Active General Dentist'
		union
		select 'Affiliate', 'AF', '',	'Affiliate'
		union
		select 'Associate', 'AS', '',	'Associate'
		union
		select 'Disability Waiver', 'AC', 'Disability Waiver',	'Dues Waiver'
		union
		select 'Emeritus Member', 'EM', '',	'Emeritus'
		union
		select 'Honorary Member', 'HM', '',	'Honorary Member'
		union
		select 'Retired', 'RE', '',	'Retired'
		union
		select 'Resident', 'AC', 'Residency',	'Resident'
		union
		select 'Student Member', 'ST', '',	'Student'
		union
		select 'Financial Waiver', 'AC', 'Financial Waiver','Dues Waiver'

		IF OBJECT_ID('tempdb..#temp_tagd_nationals_alpha') IS NOT NULL
			DROP TABLE #temp_tagd_nationals_alpha

		-- Inner join alpha with memberType so we can get the right rate
		select * 
		into #temp_tagd_nationals_alpha
		from ##tagd_nationals_alpha na
		left outer join #tagd_memberType mt on ltrim(rtrim(mt.MemberTypeValueFromAGDAlphaRoster)) = ltrim(rtrim(na.[member type]))
			and ltrim(rtrim(mt.CategoryDescriptionValueFromAGDAlphaRoster )) = ltrim(rtrim(na.[category description]))
		order by na.[category description] desc

		-- Get the query to be passed to import subscriptions stored proc
		select ROW_NUMBER() OVER(order by MemberNumber, TreeCode, ParentSubscriptionType) as rowID,
			memberNumber, SubscriptionType, SubscriptionName, RateName, LastPrice, Frequency, StoreModifiedRate, 
			StartDate, EndDate, graceEndDate, [Status], ParentSubscriptionType, ParentSubscriptionName, TreeCode
		into #mc_SubImport 
		from (
			select distinct renewals.memberNumber
				,@membershipSubscriptionType as SubscriptionType
				, subs.SubscriptionName
				,r.RateName
				, rf.rateAmt as LastPrice
				, f.frequencyShortName as Frequency
				, 'NO' as StoreModifiedRate
				, renewals.subStartDate as StartDate
				, renewals.subEndDate as EndDate
				, cast (cast(YEAR(renewals.subEndDate) + 1 as nvarchar)+'0331' as datetime) as graceEndDate
				, [Status] = case 
					when renewals.subStartDate > @currentTimeStamp then 'P'
					else 'A'
				end
				, NULL as ParentSubscriptionType
				, NULL  as ParentSubscriptionName
				, renewals.treecode as TreeCode
			from (
				select memberID, memberNumber,subStartDate,subEndDate, rootSubscriptionID, componentSubscriptionID, treecode from #tagd_sub_renewals
				union
				select memberID, memberNumber,subStartDate,subEndDate, rootSubscriptionID, componentSubscriptionID, treecode from #tagd_sub_newmembers 
			) as renewals
			inner join membercentral.dbo.sub_subscriptions as subs
				on renewals.rootSubscriptionID = subs.subscriptionID
				and subs.status = 'A' 
			inner join membercentral.dbo.sub_rateSchedules as rs 
				on subs.scheduleID = rs.scheduleID 
				and rs.status = 'A'
			inner join membercentral.dbo.sub_rates r
				on r.scheduleID = rs.scheduleID
				and r.status = 'A'
				and r.isRenewalRate = 0 
			inner join #tagd_memberType mt on 
				ltrim(rtrim(mt.MembershipDuesTAGDMemberSubRate)) = ltrim(rtrim(r.rateName))
			inner join #temp_tagd_nationals_alpha tmt on
				tmt.tempMemberTypeId = mt.tempMemberTypeId	
				and tmt.id = renewals.memberNumber	
			inner join membercentral.dbo.sub_rateFrequencies rf on
				rf.rateID = r.rateID
				and rf.status = 'A'
			inner join membercentral.dbo.sub_frequencies f on
				f.frequencyID = rf.frequencyID
				and f.status = 'A'
			inner join membercentral.dbo.sub_rateFrequenciesMerchantProfiles as rfmp on 
				rfmp.rfid = rf.rfID 
				and rfmp.status = 'A' 
			inner join membercentral.dbo.mp_profiles as mp on 
				mp.profileID = rfmp.profileID 
				and mp.status = 'A' 
			union
			select 
				distinct renewals.memberNumber
				,@componentSubscriptionType as SubscriptionType
				, subs.SubscriptionName
				,r.RateName
				, rf.rateAmt as LastPrice
				, f.frequencyShortName as Frequency
				, 'NO' as StoreModifiedRate
				, renewals.subStartDate as StartDate
				, renewals.subEndDate as EndDate
				, cast (cast(YEAR(renewals.subEndDate) + 1 as nvarchar)+'0331' as datetime) as graceEndDate
				, Status = case 
					when renewals.subStartDate > @currentTimeStamp then 'P'
					else 'A'
				end
				, @membershipSubscriptionType as ParentSubscriptionType
				, @membershipSubscriptionName as ParentSubscriptionName
				, renewals.treecode as TreeCode
			from (
				select memberID, memberNumber,subStartDate,subEndDate, rootSubscriptionID, componentSubscriptionID, treecode from #tagd_sub_renewals
				union
				select memberID, memberNumber,subStartDate,subEndDate, rootSubscriptionID, componentSubscriptionID, treecode from #tagd_sub_newmembers 
			) as renewals
			inner join membercentral.dbo.sub_subscriptions as subs
				on renewals.componentSubscriptionID = subs.subscriptionID
				and subs.status = 'A' 
			inner join membercentral.dbo.sub_rateSchedules as rs 
				on subs.scheduleID = rs.scheduleID 
				and rs.status = 'A'
			inner join membercentral.dbo.sub_rates r
				on r.scheduleID = rs.scheduleID
				and r.status = 'A'
				and r.isRenewalRate = 0 
				and r.rateName = 'Annual Term'
			inner join membercentral.dbo.sub_rateFrequencies rf on
				rf.rateID = r.rateID
				and rf.status = 'A'
			inner join membercentral.dbo.sub_frequencies f on
				f.frequencyID = rf.frequencyID
				and f.status = 'A'
			inner join membercentral.dbo.sub_rateFrequenciesMerchantProfiles as rfmp on 
				rfmp.rfid = rf.rfID 
				and rfmp.status = 'A' 
			inner join membercentral.dbo.mp_profiles as mp on 
				mp.profileID = rfmp.profileID 
				and mp.status = 'A' 
		) tmp
		order by rowID

		-- Get records to be included in e-mail for subs that were not created
		/*
		(1) This query gets the members with no active subscriptions and whose paid thru date is less than today
		(2) This query gets the members whose subscription is still valid, but the subscription's end date and paid thru date is before today
		*/
		insert into #tagd_sub_noSubCreated	(memberNumber,fullName,paidThruDate)
		select na.[ID], na.[full name], convert(varchar, na.[paid thru], 101)
		from 
			##tagd_nationals_alpha na
			inner join membercentral.dbo.ams_members activeMember
				on activeMember.memberNumber = na.id
				and activeMember.orgID = @orgID
				and activeMember.status in ('A','I')
				and activeMember.memberID = activeMember.activeMemberID
				and na.[paid thru] < @currentTimeStamp
			inner join membercentral.dbo.ams_members m
				on activeMember.memberID = m.activeMemberID
			inner join membercentral.dbo.sub_types t
				on t.typeName = @membershipSubscriptionType
				and t.siteID = @siteID
			inner join membercentral.dbo.sub_subscriptions subs
				on subs.typeID = t.typeID
				and subs.subscriptionName = @membershipSubscriptionName
			left outer join membercentral.dbo.sub_subscribers ss
				inner join membercentral.dbo.sub_statuses st
					on st.statusID = ss.statusID
					and st.statuscode in ('A','I','P')
					and ss.parentSubscriberID is null
			on m.memberID = ss.memberID
				and ss.subscriptionID = subs.subscriptionID
				and convert(varchar, ss.subenddate, 101) = convert(varchar, na.[paid thru], 101)
		group by 
			na.id,  na.[full name], convert(varchar, na.[paid thru], 101)
		having 
			max(isnull(ss.subscriberID,0)) = 0

		-- Change components 
		select 
			@autoID = min(sc.autoID) 
		from 
			#tagd_sub_switchedComponent sc
			left outer join #tagd_sub_renewals sr on
				sr.componentSubscriptionID = sc.componentSubscriptionID
				and sc.memberNumber = sr.memberNumber
		where
			sr.componentSubscriptionID is null 

		while @autoID is not null
		begin
			select 
				@thismemberid=NULL, 
				@thissubscriptionID=NULL, 
				@thisparentSubscriberID=NULL, 
				@thisRFID=NULL, 
				@thisGLAccountID=NULL, 
				@thisstatus=NULL, 
				@thissubStartDate=NULL, 
				@thissubEndDate=NULL, 
				@thisgraceEndDate = NULL, 
				@thispcfree=NULL, 
				@thisactivationOptionCode=NULL, 
				@thisbypassQueue=NULL, 
				@thisexistingComponentSubscriberID = NULL

			select 
				@thismemberid=memberID, 
				@thissubscriptionID=componentSubscriptionID, 
				@thisparentSubscriberID=existingRootSubscriberID, 
				@thisRFID=rfID, 
				@thisGLAccountID=GLAccountID, 
				@thisstatus=status, 
				@thissubStartDate=subStartDate, 
				@thissubEndDate=subEndDate, 
				@thisgraceEndDate = graceEndDate , 
				@thispcfree=pcfree, 
				@thisactivationOptionCode=activationOptionCode, 
				@thisbypassQueue=0, 
				@thisexistingComponentSubscriberID = existingComponentSubscriberID
			from #tagd_sub_switchedComponent
			where autoID = @autoID


			exec membercentral.dbo.sub_expireSubscriber @subscriberID=@thisexistingComponentSubscriberID, @memberID=@thismemberid, 
				@siteID=@siteID, @enteredByMemberID=@recordedByMemberID, @statsSessionID=0, @AROption='C', @fReturnQuery=0

			exec membercentral.dbo.sub_addSubscriber @orgID=@orgID, @memberid=@thismemberid, @subscriptionID=@thissubscriptionID, 
				@parentSubscriberID=@thisparentSubscriberID, @RFID=@thisRFID, @GLAccountID=@thisGLAccountID, @status=@thisstatus, 
				@subStartDate=@thissubStartDate, @subEndDate=@thissubEndDate, @graceEndDate=@thisgraceEndDate, 
				@recogStartDate=@thissubStartDate, @recogEndDate=@thissubEndDate, @pcfree=@thispcfree, 
				@activationOptionCode=@thisactivationOptionCode, @recordedByMemberID=@recordedByMemberID, @bypassQueue=@thisbypassQueue, 
				@subscriberID=@trashID OUTPUT

			select 
				@autoID = min(sc.autoID) 
			from 
				#tagd_sub_switchedComponent sc
				left outer join #tagd_sub_renewals sr on
					sr.componentSubscriptionID = sc.componentSubscriptionID
					and sc.memberNumber = sr.memberNumber
			where
				sr.componentSubscriptionID is null 
				and sc.autoID > @autoID
		end

		-- Add individual components 
		set @autoID = null
		select 
			@autoID = min(nc.autoID) 
		from 
			#tagd_sub_newComponent nc
			left outer join #tagd_sub_switchedComponent sc on
				nc.componentSubscriptionID = sc.componentSubscriptionID
				and nc.memberNumber = sc.memberNumber
		where
			sc.componentSubscriptionID is null 

		while @autoID is not null
		begin

			select 
				@thismemberid=NULL, 
				@thissubscriptionID=NULL, 
				@thisparentSubscriberID=NULL, 
				@thisRFID=NULL, 
				@thisGLAccountID=NULL, 
				@thisstatus=NULL, 
				@thissubStartDate=NULL, 
				@thissubEndDate=NULL, 
				@thisgraceEndDate = NULL, 
				@thispcfree=NULL, 
				@thisactivationOptionCode=NULL, 
				@thisbypassQueue=NULL, 
				@thisexistingComponentSubscriberID = NULL

			select 
				@thismemberid=memberID, 
				@thissubscriptionID=componentSubscriptionID, 
				@thisparentSubscriberID=existingRootSubscriberID, 
				@thisRFID=rfID, 
				@thisGLAccountID=GLAccountID, 
				@thisstatus=status, 
				@thissubStartDate=subStartDate, 
				@thissubEndDate=subEndDate, 
				@thisgraceEndDate = graceEndDate , 
				@thispcfree=pcfree, 
				@thisactivationOptionCode=activationOptionCode, 
				@thisbypassQueue=0, 
				@thisexistingComponentSubscriberID = existingComponentSubscriberID
			from #tagd_sub_newComponent
			where autoID = @autoID

			exec membercentral.dbo.sub_addSubscriber @orgID=@orgID, @memberid=@thismemberid, @subscriptionID=@thissubscriptionID, 
				@parentSubscriberID=@thisparentSubscriberID, @RFID=@thisRFID, @GLAccountID=@thisGLAccountID, @status=@thisstatus, 
				@subStartDate=@thissubStartDate, @subEndDate=@thissubEndDate, @graceEndDate=@thisgraceEndDate, 
				@recogStartDate=@thissubStartDate, @recogEndDate=@thissubEndDate, @pcfree=@thispcfree, 
				@activationOptionCode=@thisactivationOptionCode, @recordedByMemberID=@recordedByMemberID, @bypassQueue=@thisbypassQueue, 
				@subscriberID=@trashID OUTPUT

			select 
				@autoID = min(nc.autoID) 
			from 
				#tagd_sub_newComponent nc
				left outer join #tagd_sub_switchedComponent sc on
					nc.componentSubscriptionID = sc.componentSubscriptionID		
					and nc.memberNumber = sc.memberNumber		
			where 
				nc.autoID > @autoID
				and sc.componentSubscriptionID is null
		end

		-- Exprire components 
		select @autoID = min(autoID) from #tagd_sub_expireComponent
		while @autoID is not null
		begin
			select 
				@thismemberid=memberID, 
				@thisparentSubscriberID=existingRootSubscriberID, 
				@thisbypassQueue=0, 
				@thisexistingComponentSubscriberID = existingComponentSubscriberID
			from #tagd_sub_expireComponent
			where autoID = @autoID

			exec membercentral.dbo.sub_expireSubscriber
				@subscriberID=@thisexistingComponentSubscriberID, 
				@memberID=@thismemberid, 
				@siteID=@siteID, 
				@enteredByMemberID=@recordedByMemberID, 
				@statsSessionID=0, 
				@AROption='C', 
				@fReturnQuery=0

			select @autoID = min(autoID) from #tagd_sub_expireComponent where autoID > @autoID
		end

		-- E-mail component results
		declare @subImportResult xml

		select @subImportResult = (
			select getdate() as "@date",
				isnull((select top 301  id as "@membernumber", [FULL NAME] as "@fullname"
				from ##tagd_nationals_alpha
				where len([COMPONENT TITLE]) = 0
				order by id
				FOR XML path('member'), root('nocomponent'), type),'<nocomponent/>'),
				isnull((select top 301  id as "@membernumber", [FULL NAME] as "@fullname"
				from ##tagd_nationals_alpha na
					inner join #tagd_sub_renewals sr on
						na.id = sr.memberNumber
						and len(na.[COMPONENT TITLE]) > 0
						and sr.componentSubscriptionID is null
				order by id
				FOR XML path('member'), root('invalidcomponent'), type),'<invalidcomponent/>'),
				isnull((select top 301  id as "@membernumber", [FULL NAME] as "@fullname"
				from ##tagd_nationals_alpha na
					inner join #tagd_sub_expireComponent ec on
						na.id = ec.memberNumber
				order by id
				FOR XML path('member'), root('expiredcomponent'), type),'<expiredcomponent/>'),
				isnull((select noSubCreated.memberNumber as "@membernumber", noSubCreated.[fullName] as "@fullname", convert(varchar, noSubCreated.[paidThruDate], 101) as "@paidthru"
				from #tagd_sub_noSubCreated noSubCreated
				group by noSubCreated.memberNumber, noSubCreated.[fullName], convert(varchar, noSubCreated.[paidThruDate], 101)
				order by noSubCreated.memberNumber
				FOR XML path('member'), root('nosubcreated'), type),'<nosubcreated/>')
			for xml path('subimport'), TYPE)

		declare @qrynomcomponent varchar(max)
		select @qrynomcomponent = COALESCE(@qrynomcomponent + char(10), '') + '- ' + nocomponent.theMember.value('@fullname','varchar(75)') + ' (' + nocomponent.theMember.value('@membernumber','varchar(50)') + ')'
		from @subImportResult.nodes('/subimport/nocomponent/member') as nocomponent(theMember)

		declare @qryinvalidcomponent varchar(max)
		select @qryinvalidcomponent = COALESCE(@qryinvalidcomponent + char(10), '') + '- ' + invalidcomponent.theMember.value('@fullname','varchar(75)') + ' (' + invalidcomponent.theMember.value('@membernumber','varchar(50)') + ')'
		from @subImportResult.nodes('/subimport/invalidcomponent/member') as invalidcomponent(theMember)

		declare @qryexpiredcomponent varchar(max)
		select @qryexpiredcomponent = COALESCE(@qryexpiredcomponent + char(10), '') + '- ' + expiredcomponent.theMember.value('@fullname','varchar(75)') + ' (' + expiredcomponent.theMember.value('@membernumber','varchar(50)') + ')'
		from @subImportResult.nodes('/subimport/expiredcomponent/member') as expiredcomponent(theMember)

		declare @qrynosubcreated varchar(max)
		select @qrynosubcreated = COALESCE(@qrynosubcreated + char(10), '') + '- ' + nosubcreated.theMember.value('@fullname','varchar(75)') + ' (' + nosubcreated.theMember.value('@membernumber','varchar(50)') + ')' + ', ' + nosubcreated.theMember.value('@paidthru','varchar(20)')
		from @subImportResult.nodes('/subimport/nosubcreated/member') as nosubcreated(theMember)

		set @finalMSG = ''
		
		if @qrynomcomponent is not null BEGIN
			select @finalMSG = @finalMSG + 'The following records contain blank Components' + case 
				when @subImportResult.value('count(/subimport/nocomponent/member)','int') > 300 then ' (only 300 are shown)'
				else '' end + ':' + char(10) + @qrynomcomponent + char(10) + char(10)
		END

		if @qryinvalidcomponent is not null BEGIN
			select @finalMSG = @finalMSG + 'The following records contain invalid Components' + case 
				when @subImportResult.value('count(/subimport/invalidcomponent/member)','int') > 300 then ' (only 300 are shown)'
				else '' end + ':' + char(10) + @qryinvalidcomponent + char(10) + char(10)
		END
		
		if @qryexpiredcomponent is not null BEGIN
			select @finalMSG = @finalMSG + 'The following are records in which existing Components have been expired due to blank Components or changed Components in the import file:' + case 
				when @subImportResult.value('count(/subimport/expiredcomponent/member)','int') > 300 then ' (only 300 are shown)'
				else '' end + ':' + char(10) + @qryexpiredcomponent + char(10) + char(10)
		END

		if @qrynosubcreated is not null BEGIN
			select @finalMSG = @finalMSG + 'No Dues subscriptions were created for the following records' + case 
				when @subImportResult.value('count(/subimport/nosubcreated/member)','int') > 300 then ' (only 300 are shown)'
				else '' end + ':' + char(10) + @qrynosubcreated + char(10) + char(10)
		END

		IF len(@finalMSG) = 0 BEGIN
			set @finalMSG = 'The Components have successfully been added. No invalid Components found.'
		END

		set @emailtouse = NULL

		select 
			@emailtouse = me.email
		from 
			membercentral.dbo.ams_members as m
			inner join membercentral.dbo.ams_members as m2 on 
				m2.memberid = m.activeMemberID
			left outer join membercentral.dbo.ams_memberEmails as me 
				inner join membercentral.dbo.ams_memberEmailTypes as met on 
					met.emailTypeID = me.emailTypeID 
					and met.emailTypeOrder = 1
				on me.memberID = m2.memberID
		where
			 m.memberID = @recordedByMemberID

		IF len(@emailtouse) > 0 BEGIN
			EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to=@emailtouse,
				@cc=null, @bcc='<EMAIL>', @subject='TAGD Component and Dues Import Results',
				@message=@finalMSG, @priority='normal', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
		END

		set @importResult = null
		EXEC membercentral.dbo.sub_importSubscribers @siteID=@siteID, @recordedByMemberID=@recordedByMemberID, @useAccrualAccounting=0, @skipEmailTemplateNotifications=1, @importResult=@importResult OUTPUT
	END
END

-- cleanup
on_cleanup:
	IF OBJECT_ID('tempdb..##tagd_nationals_alpha') IS NOT NULL
		DROP TABLE ##tagd_nationals_alpha
	IF OBJECT_ID('tempdb..##tagd_nationals_new') IS NOT NULL
		DROP TABLE ##tagd_nationals_new
	IF OBJECT_ID('tempdb..##tagd_nationals_dues') IS NOT NULL
		DROP TABLE ##tagd_nationals_dues
	IF OBJECT_ID('tempdb..##tagd_nationals_Members') IS NOT NULL
		DROP TABLE ##tagd_nationals_Members
	IF OBJECT_ID('tempdb..#mc_Acct2Import') IS NOT NULL
		DROP TABLE #mc_Acct2Import
	IF OBJECT_ID('tempdb..#tagd_memberType') IS NOT NULL
		DROP TABLE #tagd_memberType
	IF OBJECT_ID('tempdb..#temp_tagd_nationals_alpha') IS NOT NULL
		DROP TABLE #temp_tagd_nationals_alpha
	IF OBJECT_ID('tempdb..#mc_SubImport') IS NOT NULL
		DROP TABLE #mc_SubImport
	IF OBJECT_ID('tempdb..#tempTreeCodeTbl') IS NOT NULL
		DROP TABLE #tempTreeCodeTbl
	IF OBJECT_ID('tempdb..#tagd_sub_renewals') IS NOT NULL
		DROP TABLE #tagd_sub_renewals
	IF OBJECT_ID('tempdb..#tagd_sub_newmembers') IS NOT NULL
		DROP TABLE #tagd_sub_newmembers
	IF OBJECT_ID('tempdb..#tagd_sub_switchedComponent') IS NOT NULL
		DROP TABLE #tagd_sub_switchedComponent
	IF OBJECT_ID('tempdb..#tagd_sub_newComponent') IS NOT NULL
		DROP TABLE #tagd_sub_newComponent
	IF OBJECT_ID('tempdb..#tagd_sub_expireComponent') IS NOT NULL
		DROP TABLE #tagd_sub_expireComponent
	IF OBJECT_ID('tempdb..#tagd_sub_noSubCreated') IS NOT NULL
		DROP TABLE #tagd_sub_noSubCreated  
GO


use membercentral
GO
DROP PROC dbo.tr_importTransactions_toTemp
GO
DROP PROC dbo.tr_importTransactions_toPerm
GO
