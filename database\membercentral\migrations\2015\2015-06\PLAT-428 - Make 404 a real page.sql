use membercentral;
GO

declare @404Content varchar(max)

set @404Content ='<div class="tsAppHeading">Page Not Found</div>
<br/>
<div class="tsAppBodyText">
	Sorry! This page is not available.
	<br/><br/>
	Go to the <a href="/index.cfm">Homepage</a> or <a href="/index.cfm?pg=search">search</a> for the page you were looking for.
</div>'


declare 
    @thisSiteID int, 
    @thisPublicGroupID int, 
    @thisPageID int, 
    @thisContentSiteResourceID int, 
    @thisContentID int, 
    @thisRootSectionID int,
    @viewFunctionID int,
    @pgResourceTypeID int,
    @HTMLResourceTypeID int,
    @siteResourceStatusID int,
    @mainZoneID int,
    @languageID int,
    @trashID int,
    @rc int


select @pgResourceTypeID = dbo.fn_getResourceTypeID('SystemCreatedPage')
select @HTMLResourceTypeID = dbo.fn_getResourceTypeID('UserCreatedContent')
select @viewFunctionID = dbo.fn_getResourceFunctionID('view',dbo.fn_getResourceTypeID('UpdateMember'))
select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')
select @languageID = dbo.fn_getLanguageID('en')

select @mainZoneID = dbo.fn_getZoneID('Main')


select @thisSiteID = min(siteID) from sites
while @thisSiteID is not null 
BEGIN

    SELECT @thisRootSectionID = ps.sectionID, @thisPublicGroupID = g.groupID
    FROM sites s
    inner join dbo.cms_pageSections ps
	   on s.siteID = ps.siteID
	   and s.siteID = @thisSiteID
	   AND ps.sectionName = 'Root'
	   AND ps.parentSectionID is null
    inner join ams_groups g
	   on g.orgID = s.orgID
	   and g.groupCode = 'public'
	   and g.status = 'A'



	-- create 404 page
	EXEC dbo.cms_createPage 
		@siteid=@thisSiteID, 
		@languageID=@languageID, 
		@resourceTypeID=@pgResourceTypeID, 
		@siteResourceStatusID=@siteResourceStatusID, 
		@pgParentResourceID=null, 
		@isVisible=1, 
		@sectionID=@thisRootSectionID, 
		@ovTemplateID=null, 
		@ovTemplateIDMobile=null,
		@ovModeID=null, 
		@pageName='404', 
		@pageTitle='Page Not Found', 
		@pageDesc='Page Not Found', 
		@keywords='Page Not Found',
		@inheritPlacements=1,
		@allowReturnAfterLogin=0, 
		@checkReservedNames=0, 
		@pageID=@thisPageID OUTPUT

	EXEC dbo.cms_createContent 
	   @siteID=@thisSiteID, 
	   @pageID=@thisPageID, 
	   @zoneID=@mainZoneID, 
	   @resourceTypeID=@HTMLResourceTypeID, 
	   @siteResourceStatusID=@siteResourceStatusID, 
	   @isSSL=0, 
	   @isHTML=1, 
	   @languageID=@languageID, 
	   @isActive=1, 
	   @contentTitle='Page Not Found', 
	   @contentDesc='Page Not Found', 
	   @rawContent=@404Content,
	   @memberID=4, 
	   @contentID=@thisContentID OUTPUT, 
	   @contentSiteResourceID=@thisContentSiteResourceID OUTPUT

	EXEC dbo.cms_createSiteResourceRight @siteID=@thisSiteID, @siteResourceID=@thisContentSiteResourceID, @include=1, 
		@functionID=@viewFunctionID, @roleID=null, @groupID=@thisPublicGroupID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT




    select @thisSiteID = min(siteID) from sites where siteID > @thisSiteID
END
GO
ALTER PROC [dbo].[cms_createDefaultPages]
@siteid int,
@sectionid int,
@languageID int

AS

declare @appPageID int

-- get resourceType for system created pages and HTML content
declare @pgResourceTypeID int, @HTMLResourceTypeID int, @appPgResourceTypeID int, @siteResourceTypeID int
select @pgResourceTypeID = dbo.fn_getResourceTypeID('SystemCreatedPage')
select @HTMLResourceTypeID = dbo.fn_getResourceTypeID('UserCreatedContent')
select @appPgResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedPage')
select @siteResourceTypeID = dbo.fn_getResourceTypeID('site')

-- get siteresourceid
declare @websiteResourceID int
select @websiteResourceID = siteResourceID from sites where siteID = @siteID

-- get active resource status
declare @siteResourceStatusID int
select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

-- get Main Zone
declare @mainZoneID int
select @mainZoneID = dbo.fn_getZoneID('Main')

-- get page modes
declare @fullModeID int, @directModeID int, @streamModeID int
select @fullModeID = dbo.fn_getModeID('Full')
select @directModeID = dbo.fn_getModeID('Direct')
select @streamModeID = dbo.fn_getModeID('Stream')

-- get resource functions
declare @viewFID int, @loginFID int, @editOwnFID int
SELECT @viewFID = dbo.fn_getResourceFunctionID('view',@HTMLResourceTypeID)
SELECT @loginFID = dbo.fn_getResourceFunctionID('login',@siteResourceTypeID)
SELECT @editOwnFID = dbo.fn_getResourceFunctionID('editOwn',dbo.fn_getResourceTypeID('UpdateMember'))


-- get public, siteadmin group
declare @publicGID int, @siteadminGID int, @usersGID int, @guestGID int, @orgID int
select @publicGID = g.groupID, @orgID = s.orgID from dbo.ams_groups as g inner join dbo.sites as s on s.orgid = g.orgid where g.isSystemGroup = 1 and g.groupName = 'Public' AND g.status <> 'D' and s.siteid = @siteid
select @usersGID = g.groupID from dbo.ams_groups as g inner join dbo.sites as s on s.orgid = g.orgid where g.isSystemGroup = 1 and g.groupName = 'Users' AND g.status <> 'D' and s.siteid = @siteid
select @guestGID = g.groupID from dbo.ams_groups as g inner join dbo.sites as s on s.orgid = g.orgid where g.isSystemGroup = 1 and g.groupName = 'Guests' AND g.status <> 'D' and s.siteid = @siteid
select @siteadminGID = g.groupID from dbo.ams_groups as g inner join dbo.sites as s on s.orgid = g.orgid where g.isSystemGroup = 1 and g.groupName = 'Site Administrators' AND g.status <> 'D' and s.siteid = @siteid

-- Add memberID 0 record to the cache member groups table to allow public access to public content for non-authenticated users.
INSERT INTO [dbo].[cache_members_groups]
           ([orgid],[memberID],[groupID],[isManualDirect],[isManualIndirect],[isVirtualDirect],[isVirtualIndirect])
     VALUES
           (@orgID, 0, @publicGID, 0, 0, 0, 1)

-- create default pages and applications
DECLARE @rc int, @pageID int, @contentID int, @contentSiteResourceID int, @applicationTypeID int, @suggestedPageName varchar(100), @applicationInstanceID int, @siteresourceID int, @trashID int
BEGIN TRAN

	-- main page and content
	EXEC @rc = dbo.cms_createPage 
		@siteid=@siteid, 
		@languageID=@languageID, 
		@resourceTypeID=@pgResourceTypeID, 
		@siteResourceStatusID=@siteResourceStatusID, 
		@pgParentResourceID=null, 
		@isVisible=1, 
		@sectionID=@sectionID, 
		@ovTemplateID=null, 
		@ovTemplateIDMobile=null,
		@ovModeID=null, 
		@pageName='Main', 
		@pageTitle='Welcome', 
		@pageDesc=null, 
		@keywords=null,
		@inheritPlacements=1,
		@allowReturnAfterLogin=1, 
		@checkReservedNames=0, 
		@pageID=@pageID OUTPUT

	   	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = dbo.cms_createContent @siteID=@siteid, @pageID=@pageID, @zoneID=@mainZoneID, @resourceTypeID=@HTMLResourceTypeID, @siteResourceStatusID=@siteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle='Welcome', @contentDesc=null, @rawContent='Put your homepage text here. You can include tables, images, lists, and more! Our built-in content editor can do all of this and more.', @memberID=NULL, @contentID=@contentID, @contentSiteResourceID=@contentSiteResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	-- 404 page and content
    declare @404Content varchar(max)

    set @404Content ='<div class="tsAppHeading">Page Not Found</div>
    <br/>
    <div class="tsAppBodyText">
	    Sorry! This page is not available.
	    <br/><br/>
	    Go to the <a href="/index.cfm">Homepage</a> or <a href="/index.cfm?pg=search">search</a> for the page you were looking for.
    </div>'

	EXEC @rc = dbo.cms_createPage 
		@siteid=@siteid, 
		@languageID=@languageID, 
		@resourceTypeID=@pgResourceTypeID, 
		@siteResourceStatusID=@siteResourceStatusID, 
		@pgParentResourceID=null, 
		@isVisible=1, 
		@sectionID=@sectionID, 
		@ovTemplateID=null, 
		@ovTemplateIDMobile=null,
		@ovModeID=null, 
		@pageName='404', 
		@pageTitle='Page Not Found', 
		@pageDesc='Page Not Found', 
		@keywords='Page Not Found',
		@inheritPlacements=1,
		@allowReturnAfterLogin=0, 
		@checkReservedNames=0, 
		@pageID=@pageID OUTPUT

		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = dbo.cms_createContent @siteID=@siteid, @pageID=@pageID, @zoneID=@mainZoneID, @resourceTypeID=@HTMLResourceTypeID, @siteResourceStatusID=@siteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle='Page Not Found', @contentDesc='Page Not Found', @rawContent=@404Content, @memberID=NULL, @contentID=@contentID, @contentSiteResourceID=@contentSiteResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@contentSiteResourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error



	-- login app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'Login'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=1, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@fullModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- org doc download app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'OrgDocDownload'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- store doc download app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'StoreDocDownload'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- ts doc download app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'TSDocDownload'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- account locator
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'accountLocator'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@directModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1,
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null,
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null,
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- userinfo app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'UserInfo'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@directModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- updatemember app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'UpdateMember'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=1, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=null, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- Ticket 8352718 - default permissions when setting up a site
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@editOwnFID, @roleID=null, @groupID=@usersGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@editOwnFID, @roleID=null, @groupID=@guestGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- ContentEditor app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'ContentEditor'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@fullModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- ajax app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'Ajax'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	
	-- Flash Express Install app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'flashExpressInstall'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	
	-- AppProxy app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'appProxy'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	
	-- invoices app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'invoices'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@fullModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- BuyNow app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'buyNow'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@fullModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	
	-- ViewCart app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'viewCart'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@fullModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	
	-- Support app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'Support'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=null, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- admin app
	-- set template used by the admin app to the admin template
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'Admin'
	EXEC @rc = dbo.cms_createApplicationInstanceAdmin @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle='Website Control Panel', @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=2, @pageModeID=null, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@siteadminGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	--set admin page inheritPlacements = 0
	update cms_pages set inheritPlacements = 0 where pageID = @appPageID
	IF @@ERROR <> 0 GOTO on_error

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1

GO
declare @fullModeID int
select @fullModeID = dbo.fn_getModeID('Full')

update cms_pages set
    ovModeID=@fullModeID
where pagename = '404'
GO