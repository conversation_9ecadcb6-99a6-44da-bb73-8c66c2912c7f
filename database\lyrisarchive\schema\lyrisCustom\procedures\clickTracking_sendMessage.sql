ALTER PROC dbo.clickTracking_sendMessage
@xmlMessage xml

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	declare @DialogHandleFinal uniqueidentifier;
	set @DialogHandleFinal = NEWID();

	BEGIN DIALOG CONVERSATION @DialogHandleFinal
		FROM SERVICE [ClickTrackingQueue/ClickTrackingService]
		TO SERVICE N'ClickTrackingQueue/ClickTrackingService'
		ON CONTRACT [ClickTrackingQueue/ClickTrackingContract]
		WITH ENCRYPTION = OFF;
	SEND ON CONVERSATION @DialogHandleFinal 
		MESSAGE TYPE [ClickTrackingQueue/ClickTrackingRequest] (@xmlMessage);

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC up_ErrorHandler;
	RETURN -1;
END CATCH
GO
