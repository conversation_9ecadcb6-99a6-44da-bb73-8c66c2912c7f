declare 
	@columnID int, 
	@valueID int,
	@rc int
	
set @columnID = 6173

begin try

select @valueID = min(valueID) from dbo.ams_memberDataColumnValues where columnID = @columnID

while @valueID is not null
begin

	print @valueID
	
	exec @rc = dbo.ams_removeMemberDataColumnValue @valueID = @valueID
	
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	
	select 
		@valueID = min(valueID) 
	from 
		dbo.ams_memberDataColumnValues 
	where 
		columnID = @columnID 
		and valueID > @valueID
end

end try

begin catch
    --returns the complete original error message as a result set
    SELECT 
        ERROR_NUMBER() AS ErrorNumber
        ,ERROR_SEVERITY() AS ErrorSeverity
        ,ERROR_STATE() AS ErrorState
        ,ERROR_PROCEDURE() AS ErrorProcedure
        ,ERROR_LINE() AS ErrorLine
        ,ERROR_MESSAGE() AS ErrorMessage

    --will return the complete original error message as an error message
    DECLARE @ErrorMessage nvarchar(400), @ErrorNumber int, @ErrorSeverity int, @ErrorState int, @ErrorLine int
    SELECT @ErrorMessage = N'Error %d, Line %d, Message: '+ERROR_MESSAGE(),@ErrorNumber = ERROR_NUMBER(),@ErrorSeverity = ERROR_SEVERITY(),@ErrorState = ERROR_STATE(),@ErrorLine = ERROR_LINE()
    RAISERROR (@ErrorMessage, @ErrorSeverity, @ErrorState, @ErrorNumber,@ErrorLine)

end catch

goto on_done

-- error exit
on_error:
	print 'ERROR'

on_done:

GO


