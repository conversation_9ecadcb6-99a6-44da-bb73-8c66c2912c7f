use customApps
GO

CREATE TABLE dbo.schedTask_memberJoinDateSubTypes
	(
	subTypeUDID int NOT NULL IDENTITY (1, 1),
	memberJoinDateUDID int NOT NULL,
	subscriptionTypeUID uniqueidentifier NOT NULL
	)  ON [PRIMARY]
GO
ALTER TABLE dbo.schedTask_memberJoinDateSubTypes ADD CONSTRAINT
	PK_schedTask_memberJoinDateSubTypes PRIMARY KEY CLUSTERED 
	(
	subTypeUDID
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

GO

insert into schedTask_memberJoinDateSubTypes (memberJoinDateUDID,subscriptionTypeUID)
select udid,subscriptionTypeUID
from dbo.schedTask_memberJoinDates
GO

ALTER TABLE dbo.schedTask_memberJoinDateSubTypes ADD CONSTRAINT
	FK_schedTask_memberJoinDateSubTypes_schedTask_memberJoinDates FOREIGN KEY
	(
	memberJoinDateUDID
	) REFERENCES dbo.schedTask_memberJoinDates
	(
	udid
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO

ALTER PROCEDURE [dbo].[job_memberJoinDates] 
@udid int,
@error_code int OUTPUT

AS

set nocount on

declare @siteCode varchar(10), @compareDate datetime,
	@joinFieldName varchar(max), @dropFieldName varchar(max), @rejoinFieldName varchar(max),
	@currColumnName varchar(max), @paidthrudatefieldname varchar(max)
declare @siteID int, @orgID int, @joinColumnID int, @droppedColumnID int, @rejoinColumnID int,
	@minSDID int, @rc int, @columnvalueID int, @currColumnValue varchar(max), @currColumnID int,
	@currMemberID int, @paidThruColumnID int
select @error_code = 0

IF OBJECT_ID('tempdb..#subJoinDates') IS NOT NULL 
	DROP TABLE #subJoinDates
Create TABLE #subJoinDates (
	id int identity(1,1) PRIMARY KEY, 
	activeMemberID int, 
	updateDate dateTime,
	[action] varchar(1), 
	columnID int, 
	columnValueID int
)

select @siteCode = sitecode, @compareDate = isnull(lastSuccessDate,'1/1/1900'),
	@joinFieldName = joinDateFieldName, @dropFieldName = droppedDateFieldName,
	@rejoinFieldName = rejoinDateFieldName, @paidthrudatefieldname = paidThruDateFieldName
	from dbo.schedTask_memberJoinDates
	where udid = @udid
select @siteID = siteID, @orgID = orgID
	from membercentral.dbo.sites
	where siteCode = @siteCode

-- get columnIDs
select @joinColumnID=columnID
	from membercentral.dbo.ams_memberDataColumns as mdc
	where orgID = @orgID
	and columnName = @joinFieldName
select @droppedColumnID=columnID
	from membercentral.dbo.ams_memberDataColumns as mdc
	where orgID = @orgID
	and columnName = @dropFieldName
select @rejoinColumnID=columnID
	from membercentral.dbo.ams_memberDataColumns as mdc
	where orgID = @orgID
	and columnName = @rejoinFieldName
select @paidThruColumnID=columnID
	from membercentral.dbo.ams_memberDataColumns as mdc
	where orgID = @orgID
	and columnName = @paidthrudatefieldname

-- @paidThruColumnID is not required. It's an optional field and the other fields calculations do not depend on it 
IF @joinColumnID is NULL OR @droppedColumnID is NULL OR @rejoinColumnID is NULL
BEGIN
	select @error_code = 1
	GOTO on_error
END

-- get rows that do not have a join date but do have an active subscription
insert into #subJoinDates (activeMemberID, updateDate, [action], columnID)
select m.activeMemberID, max(sh.updateDate) as updateDate, 'J' as [action], @joinColumnID as columnID
from membercentral.dbo.sub_statusHistory as sh
inner join membercentral.dbo.sub_subscribers as s on s.subscriberID = sh.subscriberID
inner join membercentral.dbo.sub_subscriptions as subs on subs.subscriptionID = s.subscriptionID
inner join membercentral.dbo.sub_types as t on t.typeID = subs.typeID
	and t.siteID = @siteID
inner join schedTask_memberJoinDateSubTypes mjdst
	on mjdst.subscriptionTypeUID = t.uid
	and mjdst.memberJoinDateUDID = @udid
inner join membercentral.dbo.sub_statuses as st on st.statusID = sh.statusID
	and st.statusCode = 'A'
inner join membercentral.dbo.ams_members as m on m.memberID = s.memberID
left outer join membercentral.dbo.ams_memberData as md
	inner join membercentral.dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
	inner join membercentral.dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
		and mdc.orgID = @orgID
		and mdc.columnName = @joinFieldName
	on md.memberID = m.activeMemberID
where mdcv.columnvalueDate is null
and sh.updateDate > @compareDate
group by activeMemberID
	IF @@ERROR <> 0
	BEGIN
		select @error_code = 2
		GOTO on_error
	END

-- get rows that have been expired, deleted, or had an offer expired and do not have an active, pending, or offered Status
insert into #subJoinDates (activeMemberID, updateDate, [action], columnID)
select m.activeMemberID, max(sh.updateDate), 'D' as [action], @droppedColumnID as columnID
from membercentral.dbo.sub_statusHistory as sh
inner join membercentral.dbo.sub_subscribers as s on s.subscriberID = sh.subscriberID
inner join membercentral.dbo.sub_subscriptions as subs on subs.subscriptionID = s.subscriptionID
inner join membercentral.dbo.sub_types as t on t.typeID = subs.typeID
	and t.siteID = @siteID
inner join schedTask_memberJoinDateSubTypes mjdst
	on mjdst.subscriptionTypeUID = t.uid
	and mjdst.memberJoinDateUDID = @udid
inner join membercentral.dbo.sub_statuses as st on st.statusID = sh.statusID
	and st.statusCode in ('E','X','D')
inner join membercentral.dbo.ams_members as m on m.memberID = s.memberID
where sh.updateDate > @compareDate
group by activeMemberID
	IF @@ERROR <> 0
	BEGIN
		select @error_code = 3
		GOTO on_error
	END

delete from #subJoinDates
where [action] = 'D'
and activeMemberID in (
	select distinct m.activeMemberID
	from membercentral.dbo.sub_subscribers as s
	inner join membercentral.dbo.sub_subscriptions as subs on subs.subscriptionID = s.subscriptionID
	inner join membercentral.dbo.sub_types as t on t.typeID = subs.typeID
		and t.siteID = @siteID
	inner join schedTask_memberJoinDateSubTypes mjdst
		on mjdst.subscriptionTypeUID = t.uid
		and mjdst.memberJoinDateUDID = @udid
	inner join membercentral.dbo.sub_statuses as st on st.statusID = s.statusID
		and st.statusCode in ('A','P','O')
	inner join membercentral.dbo.ams_members as m on m.memberID = s.memberID
	inner join #subJoinDates as sd on sd.activeMemberID = m.activeMemberID
)
	IF @@ERROR <> 0
	BEGIN
		select @error_code = 4
		GOTO on_error
	END

-- get people who have been moved to active, have a drop date, and rejoin date is null or before drop date
insert into #subJoinDates (activeMemberID, updateDate, [action], columnID)
select m.activeMemberID, max(sh.updateDate), 'R' as [action], @rejoinColumnID as columnID
from membercentral.dbo.sub_statusHistory as sh
inner join membercentral.dbo.sub_subscribers as s on s.subscriberID = sh.subscriberID
inner join membercentral.dbo.sub_subscriptions as subs on subs.subscriptionID = s.subscriptionID
inner join membercentral.dbo.sub_types as t on t.typeID = subs.typeID
	and t.siteID = @siteID
inner join schedTask_memberJoinDateSubTypes mjdst
	on mjdst.subscriptionTypeUID = t.uid
	and mjdst.memberJoinDateUDID = @udid
inner join membercentral.dbo.sub_statuses as st on st.statusID = sh.statusID
	and st.statusCode = 'A'
inner join membercentral.dbo.ams_members as m on m.memberID = s.memberID
left outer join membercentral.dbo.ams_memberData as md 
	inner join membercentral.dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
	inner join membercentral.dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
		and mdc.orgID = @orgID
		and mdc.columnName = @dropFieldName
	on md.memberID = m.activeMemberID
left outer join membercentral.dbo.ams_memberData as md2 
	inner join membercentral.dbo.ams_memberDataColumnValues as mdcv2 on mdcv2.valueID = md2.valueID
	inner join membercentral.dbo.ams_memberDataColumns as mdc2 on mdc2.columnID = mdcv2.columnID
		and mdc2.orgID = @orgID
		and mdc2.columnName = @rejoinFieldName
	on md2.memberID = m.activeMemberID
where mdcv.columnvalueDate is not null
and (mdcv2.columnvalueDate is null or mdcv2.columnvalueDate < mdcv.columnvalueDate)
and sh.updateDate > @compareDate
group by activeMemberID
	IF @@ERROR <> 0
	BEGIN
		select @error_code = 5
		GOTO on_error
	END

-- find members with subscription activity and add those needing paidThrudate changes to update table
IF @paidThruColumnID IS NOT NULL 
BEGIN
	;with cte as (
		select m.activeMemberID,
			mdcv.columnvalueDate,
			max(latestActivatedS.subenddate) as paidThruDate,
			'P' as [action],
			@paidThruColumnID as columnID
		from membercentral.dbo.sub_types as t 
		inner join membercentral.dbo.sub_subscriptions subs
			on t.typeID = subs.typeID
			and t.siteID = @siteID
		inner join schedTask_memberJoinDateSubTypes mjdst
			on mjdst.subscriptionTypeUID = t.uid
			and mjdst.memberJoinDateUDID = @udid
		inner join membercentral.dbo.sub_subscribers as s
			on s.subscriptionID = subs.subscriptionID
		inner join membercentral.dbo.sub_statusHistory as sh
			on s.subscriberID = sh.subscriberID
		inner join membercentral.dbo.sub_paymentStatusHistory psh
			on psh.subscriberID = s.subscriberID
		inner join membercentral.dbo.ams_members m
			on m.memberID = s.memberID
		inner join membercentral.dbo.ams_members allm
			on allm.activeMemberID = m.activeMemberID
		inner join membercentral.dbo.sub_subscriptions as latestActivatedSub
			on latestActivatedSub.typeID = t.typeID
		inner join membercentral.dbo.sub_subscribers as latestActivatedS
			on latestActivatedS.memberID = allm.memberID
			and latestActivatedS.subscriptionID = latestActivatedSub.subscriptionID
		inner join membercentral.dbo.sub_paymentStatuses ps
			on ps.statusID = latestActivatedS.paymentStatusID
			and ps.statusCode = 'P'
		-- only consider if the current status is one that implies that the subscription was or will be active at some point 
		inner join membercentral.dbo.sub_statuses as st
			on st.statusID = latestActivatedS.statusID
			and st.statusCode in ('A','I','P','E')
		left outer join membercentral.dbo.ams_memberData as md
			inner join membercentral.dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
			inner join membercentral.dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
				and mdc.orgID = @orgID
				and mdc.columnName = @paidthrudatefieldname
		on md.memberID = m.activeMemberID
		where (sh.updateDate > @compareDate or psh.updateDate > @compareDate)
		group by m.activememberID, mdcv.columnvalueDate
	)
	insert into #subJoinDates (activeMemberID, updateDate, [action], columnID)
	select activeMemberID, paidThruDate,[action], columnID
	from cte
	where (columnvalueDate is null or columnvalueDate <> paidThruDate)
		IF @@ERROR <> 0
		BEGIN
			select @error_code = 6
			GOTO on_error
		END
END

select @minSDID = min(id) from #subJoinDates
while @minSDID is not null BEGIN
	select @currMemberID=activeMemberID, 
		@currColumnValue=convert(varchar,updateDate), 
		@currColumnID = columnID,
		@currColumnName = case 
			when [action] = 'R' then @rejoinFieldName
			when [action] = 'D' then @dropFieldName
			when [action] = 'J' then @joinFieldName
			when [action] = 'P' then @paidthrudatefieldname
			end
	from #subJoinDates 
	where id = @minSDID
		IF @@ERROR <> 0
		BEGIN
			select @error_code = 7
			GOTO on_error
		END
	
	print 'EXEC @rc = membercentral.dbo.ams_setMemberData ' + cast(@currMemberID as varchar(10)) + ', ' + cast(@orgID as varchar(10))+ ', ' + @currColumnName + ', ' + @currColumnValue

	EXEC @rc = membercentral.dbo.ams_setMemberData @currMemberID, @orgID, @currColumnName, @currColumnValue
		IF @@ERROR <> 0 OR @rc <> 0
		BEGIN
			select @error_code = 8
			GOTO on_error
		END

	select @minSDID = min(id) from #subJoinDates where id > @minSDID
END

-- set member as updated
UPDATE m
SET m.dateLastUpdated = getdate()
from membercentral.dbo.ams_members as m
inner join #subJoinDates as sd on sd.activeMemberID = m.memberID
	IF @@ERROR <> 0
	BEGIN
		select @error_code = 9
		GOTO on_error
	END

-- queue processing of member groups
insert into membercentral.dbo.queue_processMemberGroups (orgID, memberID, conditionID)
select @orgID as orgID, sd.activeMemberID as memberID, c.conditionID
from #subJoinDates sd
inner join membercentral.dbo.ams_virtualGroupConditions as c on c.fieldcode = 'md_' + cast(sd.columnID as varchar(10))
	IF @@ERROR <> 0
	BEGIN
		select @error_code = 10
		GOTO on_error
	END

update dbo.schedTask_memberJoinDates
set lastSuccessDate = getDate(),
	lastErrorCode = @error_code
where udid = @udid

GOTO on_done


on_error:
	update dbo.schedTask_memberJoinDates
	set lastErrorCode = @error_code
	where udid = @udid

on_done:
	IF OBJECT_ID('tempdb..#subJoinDates') IS NOT NULL 
		DROP TABLE #subJoinDates
	RETURN 0	

set nocount off
GO

ALTER TABLE dbo.schedTask_memberJoinDates
	DROP COLUMN subscriptionTypeUID
GO

insert into schedTask_memberJoinDateSubTypes (memberJoinDateUDID,subscriptionTypeUID)
select udid,'FEB70DA9-DF4D-46FE-9837-DC03B4DAFADD' as subscriptionTypeUID
from dbo.schedTask_memberJoinDates
where sitecode = 'md'
GO

