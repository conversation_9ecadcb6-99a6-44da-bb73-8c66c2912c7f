-- delete existing pg=LegislativeContribution
EXEC dbo.cms_deleteSiteResourceAndChildren 1275349
GO

-- add custom page
declare @siteID int, @sectionID int, @zoneID int, @pgResourceTypeID int, @applicationInstanceID int, @siteResourceID int, 
	@pageID int, @resourceRightID int
		      
select @siteID = siteID from sites where siteCode = 'TX'
select @sectionID = sectionID from cms_pageSections where siteID = @siteID and sectionName = 'Legislature' and sectionCode = 'Legislature'
SELECT @zoneID = dbo.fn_getZoneID('Main')
SELECT @pgResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedPage')

EXEC dbo.cms_createApplicationInstance @siteid=@siteID, @languageID=1, @sectionID=@sectionID, @applicationTypeID=18,
	@isVisible=1, @pageName='LegislativeContribution', @pageTitle='TTLA Legislative Contribution', @pagedesc='TTLA Legislative Contribution Application', @zoneID=@zoneID, @pageTemplateID=null,
	@pageModeID=null, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1,
	@applicationInstanceName='LegislativeContribution', @applicationInstanceDesc='LegislativeContribution', @applicationInstanceID=@applicationInstanceID OUTPUT,
	@siteResourceID=@siteResourceID OUTPUT, @pageID=@pageID OUTPUT
								
INSERT INTO cms_customPages(appInstanceID, customFileName)
VALUES(@applicationInstanceID,'LegislativeContribution')

-- Give rights to the public group
EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1, @functionID=4, @roleID=null, 
	@groupID=489, @memberID=null, @inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, @resourceRightID=@resourceRightID OUTPUT
GO


