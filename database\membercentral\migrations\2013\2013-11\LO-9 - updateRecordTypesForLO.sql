declare @recordTypeID int
select @recordTypeID = recordTypeID from dbo.ams_recordTypes where orgID = 19 and recordTypeName = 'Organization'

UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '361252AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360568AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '361411AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '361377AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '025157AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '039190AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '092052AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '363025AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362580AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '016670AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '016980AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '019120AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '019160AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '364090AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '361285AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362172AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362160AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362099AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360060AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362038AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362064AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362051AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '027194AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '027155AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '021879AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362017AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362023AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362024AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362027AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360243AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '361998AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360940AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359135AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '020296AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '025100AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362458AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '361197AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '026192AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360578AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359442AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '361568AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '361645AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '025545AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '001511AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '361345AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '361346AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359146AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '361294AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '026303AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360144AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '017521AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '361192AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '193048AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '023126AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '026233AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360061AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359140AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '361069AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '361020AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '361017AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '361037AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360039AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '349126AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '374280AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360981AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360951AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '121480AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360965AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360966AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360964AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359143AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360950AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359995AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359985AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360939AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '027179AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360824AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360797AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '014190AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '021988AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359390AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360537AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '288614AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359423AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359149AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360371AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '021801AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '002221AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '026372AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '155822AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359407AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360091AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '025140AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '027217AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '024516AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360128AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360101AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '373740AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '027178AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '001536AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360065AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360066AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '027219AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '130812AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '013501AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360038AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359952AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '027270AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '026823AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359425AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '017218AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359693AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '003792AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360231AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359153AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359151AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359142AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359159AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359133AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '020351AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359147AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359129AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '026589AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359131AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '019469AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '166893AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '167634AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '006007AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '027243AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '154000AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '162252AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '014127AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '012991AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '027214AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '027216AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '026211AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '015721AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '027181AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '146835AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '139659AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '146834AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '349124AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '349058AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '027195AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '139612AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '027192AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '027176AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '025095AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360028AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '288165AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '288530AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '288514AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '026332AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '011296AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '034741AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '217384AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '007373AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '026471AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '026470AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '006359AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '025432AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '021689AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '026553AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '084787AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '019947AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '296466AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '217385AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '378900AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '370840AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '012680AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '370200AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '361805AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '367960AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '367850AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '367840AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '141486AFF'
--UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '027154AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362854AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '367430AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '367140AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '366960AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '006050AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '025132AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '022727AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362814AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '365400AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '000704AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '146833AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362773AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '364960AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '364820AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '364780AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '004370AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '012831AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '016530AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '021511AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360538AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '018396AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '021408AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '027182AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '012939AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360027AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '288174AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '364350AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '349096AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '364300AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '364290AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '364250AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359424AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '003330AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '023068AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '363043AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '007150AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360795AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '015580AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360566AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362978AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '012530AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '015340AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '019221AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '374040AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '014110AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '019855AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359141AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362897AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '009810AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '025013AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '009780AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '009820AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '009800AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '009770AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360133AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '027193AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360217AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '146837AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '007440AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '006840AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '006140AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '010876AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '022577AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360062AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '027175AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '374200AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360059AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362778AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '022578AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '004820AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '004390AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '004380AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '004210AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '025054AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '019487AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '004000AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '004030AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '002740AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '002990AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360973AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362509AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360975AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360963AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362613AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '004090AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362587AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '001780AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '000920AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360822AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '008303AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '030323AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '000430AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360160AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '000320AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '000310AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '361628AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362488AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362469AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362484AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359311AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362481AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '146460AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362967AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362972AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '022088AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362955AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362911AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '026367AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362853AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360141AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360140AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360143AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362868AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362838AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '003234AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '021973AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362097AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362831AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362132AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362785AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360577AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '003025AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360099AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360567AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360270AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '358976AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362786AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '361070AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362784AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '022962AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362028AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359992AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '374540AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362727AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362612AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362725AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362640AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362708AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362614AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362610AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '003641AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '361996AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '017219AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362609AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360894AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '374490AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '374530AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '374520AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359403AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '361852AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362607AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359701AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359157AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '000309AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '025286AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '386180AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '386190AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '385950AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '385930AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '385940AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '023167AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '021799AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '023166AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362959AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359154AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362970AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '013190AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '011240AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '008060AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '383780AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '383270AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '006001AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362912AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '361420AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '361425AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '368320AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '381790AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '381780AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '381620AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '008910AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '367830AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '013645AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362025AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '025094AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '361196AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '002406AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '006850AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '381060AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '377920AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '380880AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '367030AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '380650AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '006800AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '006820AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '366580AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '380200AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '380050AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '007426AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '001891AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '005260AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362095AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '005675AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '025131AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '023955AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '156457AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360092AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '020711AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '365980AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362400AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '379740AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '379520AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '012630AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '379410AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '005220AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360067AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '146838AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '022575AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '025689AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '379030AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '379040AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '378920AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '378910AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '014491AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359449AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '013181AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '027177AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '003944AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '004360AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '378580AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '009906AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '026131AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '022734AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360980AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '024629AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360994AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '003930AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '003490AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '005566AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '374680AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '374670AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362966AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '349097AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '364320AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '364330AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '364280AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '364310AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '364260AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '027135AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '014120AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '363900AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '377930AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '026215AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '374740AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '008510AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '377900AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '003210AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '148285AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '378510AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '377640AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '022081AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360823AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '003233AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '359156AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '361995AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360421AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '361984AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '004060AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '004080AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362001AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '146832AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '003320AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '021967AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '003430AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '004070AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362726AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '361410AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '043317AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360962AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '377390AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '019753AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '377090AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '377110AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '019810AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '376410AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '375750AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362575AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '001319AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360063AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362586AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '137415AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '360825AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '001513AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '000610AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '375690AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '375700AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '375330AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362886AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '375260AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '375070AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '375080AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '374690AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '016680AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '000280AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '017076AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '362494AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '000410AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '139634AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '025142AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '375060AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '000380AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '386540AFF'
UPDATE ams_members SET recordTypeID = @recordTypeID WHERE memberNumber = '386560AFF'


GO

declare @recordTypeID int
select @recordTypeID = recordTypeID from dbo.ams_recordTypes where orgID = 19 and recordTypeName = 'Individual'

update ams_members
set recordTypeID = @recordTypeID
where orgID = 19
and recordTypeID is null
GO

--Primary Contacts
declare @recordTypeRelationshipTypeID int
declare @recordTypeIDIndiv int, @recordTypeIDOrg int, @relationshipTypeID int
select @recordTypeIDIndiv = recordTypeID from dbo.ams_recordTypes where orgID = 19 and recordTypeName = 'Individual'
select @recordTypeIDOrg = recordTypeID from dbo.ams_recordTypes where orgID = 19 and recordTypeName = 'Organization'
select @relationshipTypeID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = 19 and relationshipTypeCode = 'OrgPrimaryContact'
select @recordTypeRelationshipTypeID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes where relationshipTypeID = @relationshipTypeID and masterRecordTypeID = @recordTypeIDOrg and childRecordTypeID = @recordTypeIDIndiv and isActive = 1

declare @memnum varchar(30), @mastermemberid int, @childmemberid int
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '361252AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360568AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '361411AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '361377AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '025157AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '039190AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '092052AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '363025AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362580AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '016670AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '016980AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '019120AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '019160AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '364090AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '361285AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362172AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362160AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362099AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360060AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362038AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362064AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362051AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '027194AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '027155AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '021879AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362017AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362023AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362024AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362027AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360243AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '361998AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360940AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359135AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '020296AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '025100AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362458AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '361197AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '026192AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360578AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359442AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '361568AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '361645AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '025545AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '001511AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '361345AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '361346AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359146AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '361294AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '026303AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360144AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '017521AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '361192AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '193048AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '023126AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '026233AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360061AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359140AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '361069AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '361020AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '361017AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '361037AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360039AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '349126AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '374280AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360981AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360951AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '121480AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360965AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360966AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360964AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359143AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360950AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359995AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359985AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360939AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '027179AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360824AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360797AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '014190AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '021988AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359390AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360537AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '288614AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359423AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359149AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360371AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '021801AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '002221AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '026372AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '155822AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359407AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360091AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '025140AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '027217AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '024516AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360128AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360101AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '373740AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '027178AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '001536AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360065AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360066AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '027219AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '130812AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '013501AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360038AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359952AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '027270AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '026823AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359425AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '017218AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359693AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '003792AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360231AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359153AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359151AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359142AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359159AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359133AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '020351AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359147AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359129AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '026589AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359131AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '019469AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '166893AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '167634AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '006007AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '027243AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '154000AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '162252AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '014127AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '012991AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '027214AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '027216AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '026211AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '015721AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '027181AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '146835AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '139659AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '146834AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '349124AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '349058AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '027195AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '139612AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '027192AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '027176AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '025095AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360028AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '288165AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '288530AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '288514AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '026332AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '011296AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '034741AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '217384AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '007373AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '026471AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '026470AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '006359AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '025432AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '021689AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '026553AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '084787AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '019947AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '296466AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '217385AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '378900AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '370840AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '012680AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '370200AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '361805AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '367960AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '367850AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '367840AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '141486AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
--select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '027154AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362854AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '367430AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '367140AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '366960AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '006050AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '025132AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '022727AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362814AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '365400AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '000704AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '146833AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362773AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '364960AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '364820AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '364780AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '004370AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '012831AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '016530AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '021511AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360538AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '018396AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '021408AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '027182AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '012939AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360027AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '288174AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '364350AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '349096AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '364300AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '364290AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '364250AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359424AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '003330AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '023068AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '363043AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '007150AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360795AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '015580AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360566AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362978AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '012530AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '015340AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '019221AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '374040AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '014110AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '019855AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359141AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362897AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '009810AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '025013AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '009780AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '009820AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '009800AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '009770AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360133AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '027193AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360217AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '146837AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '007440AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '006840AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '006140AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '010876AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '022577AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360062AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '027175AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '374200AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360059AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362778AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '022578AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '004820AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '004390AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '004380AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '004210AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '025054AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '019487AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '004000AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '004030AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '002740AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '002990AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360973AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362509AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360975AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360963AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362613AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '004090AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362587AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '001780AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '000920AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360822AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '008303AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '030323AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '000430AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360160AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '000320AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '000310AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '361628AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362488AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362469AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362484AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359311AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362481AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '146460AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362967AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362972AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '022088AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362955AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362911AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '026367AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362853AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360141AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360140AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360143AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362868AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362838AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '003234AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '021973AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362097AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362831AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362132AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362785AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360577AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '003025AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360099AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360567AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360270AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '358976AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362786AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '361070AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362784AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '022962AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362028AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359992AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '374540AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362727AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362612AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362725AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362640AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362708AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362614AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362610AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '003641AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '361996AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '017219AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362609AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360894AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '374490AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '374530AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '374520AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359403AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '361852AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362607AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359701AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359157AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '000309AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '025286AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '386180AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '386190AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '385950AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '385930AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '385940AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '023167AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '021799AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '023166AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362959AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359154AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362970AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '013190AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '011240AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '008060AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '383780AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '383270AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '006001AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362912AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '361420AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '361425AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '368320AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '381790AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '381780AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '381620AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '008910AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '367830AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '013645AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362025AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '025094AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '361196AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '002406AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '006850AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '381060AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '377920AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '380880AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '367030AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '380650AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '006800AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '006820AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '366580AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '380200AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '380050AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '007426AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '001891AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '005260AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362095AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '005675AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '025131AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '023955AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '156457AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360092AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '020711AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '365980AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362400AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '379740AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '379520AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '012630AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '379410AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '005220AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360067AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '146838AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '022575AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '025689AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '379030AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '379040AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '378920AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '378910AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '014491AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359449AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '013181AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '027177AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '003944AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '004360AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '378580AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '009906AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '026131AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '022734AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360980AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '024629AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360994AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '003930AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '003490AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '005566AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '374680AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '374670AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362966AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '349097AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '364320AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '364330AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '364280AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '364310AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '364260AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '027135AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '014120AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '363900AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '377930AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '026215AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '374740AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '008510AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '377900AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '003210AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '148285AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '378510AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '377640AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '022081AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360823AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '003233AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '359156AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '361995AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360421AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '361984AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '004060AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '004080AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362001AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '146832AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '003320AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '021967AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '003430AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '004070AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362726AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '361410AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '043317AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360962AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '377390AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '019753AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '377090AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '377110AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '019810AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '376410AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '375750AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362575AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '001319AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360063AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362586AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '137415AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '360825AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '001513AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '000610AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '375690AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '375700AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '375330AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362886AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '375260AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '375070AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '375080AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '374690AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '016680AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '000280AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '017076AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '362494AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '000410AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '139634AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '025142AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '375060AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '000380AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);

select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '386540AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);
select @memnum = null, @mastermemberid = null, @childmemberid = null; select @memnum = '386560AFF'; select @MasterMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = @memnum and status = 'A'; select @ChildMemberid = memberID from dbo.ams_members where orgID = 19 and membernumber = replace(@memnum,'AFF','') and status = 'A';insert into ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive) VALUES(@recordTypeRelationshipTypeID, @MasterMemberid, @childMemberID, 1);


GO

-- Representatives
declare @recordTypeRelationshipTypeID int, @recordTypeRelationshipTypeID2 int
declare @recordTypeIDIndiv int, @recordTypeIDOrg int, @relationshipTypeID int, @relationshipTypeID2 int
select @recordTypeIDIndiv = recordTypeID from dbo.ams_recordTypes where orgID = 19 and recordTypeName = 'Individual'
select @recordTypeIDOrg = recordTypeID from dbo.ams_recordTypes where orgID = 19 and recordTypeName = 'Organization'
select @relationshipTypeID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = 19 and relationshipTypeCode = 'OrgPrimaryContact'
select @recordTypeRelationshipTypeID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes where relationshipTypeID = @relationshipTypeID and masterRecordTypeID = @recordTypeIDOrg and childRecordTypeID = @recordTypeIDIndiv and isActive = 1
select @relationshipTypeID2 = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = 19 and relationshipTypeCode = 'OrgRep'
select @recordTypeRelationshipTypeID2 = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes where relationshipTypeID = @relationshipTypeID2 and masterRecordTypeID = @recordTypeIDOrg and childRecordTypeID = @recordTypeIDIndiv and isActive = 1

select m.memberID as childMemberID, m.company, m.recordTypeID, 0 as masterMemberID
into #temp
from dbo.ams_members as m
where m.orgID = 19
and m.recordTypeID = @recordTypeIDIndiv
and m.company in (
	select company
	from dbo.ams_members
	where orgID = 19
	and recordTypeID = @recordTypeIDOrg)
and m.memberid not in (
	select childMemberID
	from dbo.ams_recordRelationships
	where recordTypeRelationshipTypeID = @recordTypeRelationshipTypeID)

-- Match reps to company
select m.memberID as masterMemberID, m.company
into #temp2
from dbo.ams_members m
where orgID = 19
and recordTypeID = @recordTypeIDOrg
and company in (select company from #temp)

-- Set MasterMemberID for reps in temp table
update t1
set t1.masterMemberID = t2.masterMemberID
from #temp as t1
inner join #temp2 as t2 on t2.company = t1.company

-- Create relationships
insert dbo.ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive)
select @recordTypeRelationshipTypeID2, masterMemberID, childMemberID, 1 
from #temp

drop table #temp
drop table #temp2
GO

