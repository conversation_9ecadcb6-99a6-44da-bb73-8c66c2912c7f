declare 
	@navigationID int, 
	@parentNavigationID int, 
	@rc int,
	@siteResourceID int,
	@parentSiteResourceID int,
	@applicationTypeID int, 
	@appCreatedContentResourceTypeID int, 
	@activeSiteResourceStatusID int,
	@maincontentID int, 
	@maincontentSiteResourceID int,
	@documentSectionName varchar(50),
	@siteID int,
	@toolResourceTypeID int,
	@toolTypeID int,
	@viewfunctionID int,
	@viewResourceTypeFunctionID int,
	@superAdminRoleID int

select @siteResourceID = null
select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')
select @activeSiteResourceStatusID = dbo.fn_getResourceStatusId('Active')
select @siteID = siteID from sites s where s.siteCode = 'mc'

-- create mainContent
EXEC @rc = dbo.cms_createContentObject 
	@siteID=@siteID, 
	@resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, 
	@isSSL=0, 
	@isHTML=1, 
	@languageID=1, 
	@isActive=1, 
	@contentTitle='TrialSmith_Options', 
	@contentDesc=null, 
	@rawContent='Welcome to TrialSmith', 
	@contentID=@maincontentID OUTPUT, 
	@siteResourceID=@maincontentSiteResourceID OUTPUT
IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- get parent siteResourceID for recently created content object
select 
	@parentSiteResourceID = ast.siteResourceID,
	@toolTypeID = att.toolTypeID, 
	@toolResourceTypeID = att.resourceTypeID
from 
	dbo.admin_siteTools ast
	inner join dbo.admin_toolTypes att on
		att.tooltypeID = ast.toolTypeID
		and att.toolType = 'SearchAdsAdmin'
	inner join sites s on
		s.siteID = ast.siteID
		and s.siteCode = 'mc'	

print '@parentSiteResourceID: ' + cast(@parentSiteResourceID as varchar)

-- assign parent resourceID to newly created content object
update
	dbo.cms_siteResources
set
	parentSiteResourceID = @parentSiteResourceID
where
	siteResourceID = @maincontentSiteResourceID
IF @@ERROR <> 0 GOTO on_error

select @parentNavigationID = navigationID from dbo.admin_navigation where  navname = 'Search Ads Management'

print '@parentNavigationID: ' + cast(@parentNavigationID as varchar)

select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')

-- get view function
EXEC @rc = cms_createSiteResourceFunction 
	@resourceTypeID=@toolResourceTypeID, 
	@functionName='View', 
	@displayName='View', 
	@functionID=@viewfunctionID OUTPUT
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

select @viewResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@toolResourceTypeID,@viewfunctionID);
IF @@ERROR <> 0 GOTO on_error

exec @rc = dbo.cms_createSiteResourceRoleFunction 
	@roleID=@superAdminRoleID, 
	@resourceTypeFunctionID=@viewResourceTypeFunctionID;
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- Add new navigation
EXEC @rc = dbo.createAdminNavigation
	@navName='TrialSmith Options',
	@navDesc='TrialSmith Options',
	@parentNavigationID=@parentNavigationID,
	@navAreaID=3,
	@cfcMethod='getOptions',
	@isHeader=0,
	@showInNav=1,
	@navigationID=@navigationID OUTPUT
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

EXEC @rc = dbo.createAdminFunctionsDeterminingNav
	@resourceTypeFunctionID=@viewResourceTypeFunctionID,
	@toolTypeID=@toolTypeID,
	@navigationID=@navigationID
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

GOTO on_success

on_error:
	print 'ERROR FOUND!'

on_success:
