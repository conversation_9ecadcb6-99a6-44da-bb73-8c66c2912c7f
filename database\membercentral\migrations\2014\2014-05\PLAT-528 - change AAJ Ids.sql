SELECT m.memberid, right('000000000' + mdcv.columnValueString,9) + 'I' as newValue,
	'EXEC dbo.ams_setMemberData ' + cast(m.memberid as varchar(10)) + ', ' + cast(o.orgid as varchar(10)) + ', ''' + mdc.columnName + ''', ''' + right('000000000' + mdcv.columnValueString,9) + 'I' + '''' as sqlToRun
FROM dbo.ams_members as m
INNER JOIN dbo.organizations as o on o.orgID = m.orgID
INNER JOIN dbo.ams_memberData as md ON m.memberID = md.memberID 
INNER JOIN dbo.ams_memberDataColumnValues as mdcv ON mdcv.valueID = md.valueID
INNER JOIN dbo.ams_memberDataColumns as mdc ON mdcv.columnID = mdc.columnID
WHERE o.orgcode = 'KY'
and mdc.columnName = 'AAJ ID Number' 
and m.memberID = m.activeMemberID 

SELECT m.memberid, right('000000000' + mdcv.columnValueString,9) + 'I' as newValue,
	'EXEC dbo.ams_setMemberData ' + cast(m.memberid as varchar(10)) + ', ' + cast(o.orgid as varchar(10)) + ', ''' + mdc.columnName + ''', ''' + right('000000000' + mdcv.columnValueString,9) + 'I' + '''' as sqlToRun
FROM dbo.ams_members as m
INNER JOIN dbo.organizations as o on o.orgID = m.orgID
INNER JOIN dbo.ams_memberData as md ON m.memberID = md.memberID 
INNER JOIN dbo.ams_memberDataColumnValues as mdcv ON mdcv.valueID = md.valueID
INNER JOIN dbo.ams_memberDataColumns as mdc ON mdcv.columnID = mdc.columnID
WHERE o.orgcode = 'MD'
and mdc.columnName = 'AAJ Presidents Club ID' 
and m.memberID = m.activeMemberID 

SELECT m.memberid, right('000000000' + mdcv.columnValueString,9) + 'I' as newValue,
	'EXEC dbo.ams_setMemberData ' + cast(m.memberid as varchar(10)) + ', ' + cast(o.orgid as varchar(10)) + ', ''' + mdc.columnName + ''', ''' + right('000000000' + mdcv.columnValueString,9) + 'I' + '''' as sqlToRun
FROM dbo.ams_members as m
INNER JOIN dbo.organizations as o on o.orgID = m.orgID
INNER JOIN dbo.ams_memberData as md ON m.memberID = md.memberID 
INNER JOIN dbo.ams_memberDataColumnValues as mdcv ON mdcv.valueID = md.valueID
INNER JOIN dbo.ams_memberDataColumns as mdc ON mdcv.columnID = mdc.columnID
WHERE o.orgcode = 'NM'
and mdc.columnName = 'AAJ COIR ID' 
and m.memberID = m.activeMemberID 

SELECT m.memberid, right('000000000' + mdcv.columnValueString,9) + 'I' as newValue,
	'EXEC dbo.ams_setMemberData ' + cast(m.memberid as varchar(10)) + ', ' + cast(o.orgid as varchar(10)) + ', ''' + mdc.columnName + ''', ''' + right('000000000' + mdcv.columnValueString,9) + 'I' + '''' as sqlToRun
FROM dbo.ams_members as m
INNER JOIN dbo.organizations as o on o.orgID = m.orgID
INNER JOIN dbo.ams_memberData as md ON m.memberID = md.memberID 
INNER JOIN dbo.ams_memberDataColumnValues as mdcv ON mdcv.valueID = md.valueID
INNER JOIN dbo.ams_memberDataColumns as mdc ON mdcv.columnID = mdc.columnID
WHERE o.orgcode = 'NY'
and mdc.columnName = 'AAJ Import ID' 
and m.memberID = m.activeMemberID 

SELECT m.memberid, right('000000000' + mdcv.columnValueString,9) + 'I' as newValue,
	'EXEC dbo.ams_setMemberData ' + cast(m.memberid as varchar(10)) + ', ' + cast(o.orgid as varchar(10)) + ', ''' + mdc.columnName + ''', ''' + right('000000000' + mdcv.columnValueString,9) + 'I' + '''' as sqlToRun
FROM dbo.ams_members as m
INNER JOIN dbo.organizations as o on o.orgID = m.orgID
INNER JOIN dbo.ams_memberData as md ON m.memberID = md.memberID 
INNER JOIN dbo.ams_memberDataColumnValues as mdcv ON mdcv.valueID = md.valueID
INNER JOIN dbo.ams_memberDataColumns as mdc ON mdcv.columnID = mdc.columnID
WHERE o.orgcode = 'TN'
and mdc.columnName = 'AAJ ID Number' 
and m.memberID = m.activeMemberID 

SELECT m.memberid, right('000000000' + mdcv.columnValueString,9) + 'I' as newValue,
	'EXEC dbo.ams_setMemberData ' + cast(m.memberid as varchar(10)) + ', ' + cast(o.orgid as varchar(10)) + ', ''' + mdc.columnName + ''', ''' + right('000000000' + mdcv.columnValueString,9) + 'I' + '''' as sqlToRun
FROM dbo.ams_members as m
INNER JOIN dbo.organizations as o on o.orgID = m.orgID
INNER JOIN dbo.ams_memberData as md ON m.memberID = md.memberID 
INNER JOIN dbo.ams_memberDataColumnValues as mdcv ON mdcv.valueID = md.valueID
INNER JOIN dbo.ams_memberDataColumns as mdc ON mdcv.columnID = mdc.columnID
WHERE o.orgcode = 'VA'
and mdc.columnName = 'AAJ ID Number' 
and m.memberID = m.activeMemberID 

SELECT m.memberid, right('000000000' + mdcv.columnValueString,9) + 'I' as newValue,
	'EXEC dbo.ams_setMemberData ' + cast(m.memberid as varchar(10)) + ', ' + cast(o.orgid as varchar(10)) + ', ''' + mdc.columnName + ''', ''' + right('000000000' + mdcv.columnValueString,9) + 'I' + '''' as sqlToRun
FROM dbo.ams_members as m
INNER JOIN dbo.organizations as o on o.orgID = m.orgID
INNER JOIN dbo.ams_memberData as md ON m.memberID = md.memberID 
INNER JOIN dbo.ams_memberDataColumnValues as mdcv ON mdcv.valueID = md.valueID
INNER JOIN dbo.ams_memberDataColumns as mdc ON mdcv.columnID = mdc.columnID
WHERE o.orgcode = 'NC'
and mdc.columnName = 'AAJ ID' 
and m.memberID = m.activeMemberID 

SELECT m.memberid,
	'EXEC dbo.ams_setMemberData ' + cast(m.memberid as varchar(10)) + ', ' + cast(o.orgid as varchar(10)) + ', ''' + mdc.columnName + ''', ''' + left(mdcv.columnValueString,9) + 'O' + '''' as sqlToRun
FROM dbo.ams_members as m
INNER JOIN dbo.organizations as o on o.orgID = m.orgID
INNER JOIN dbo.ams_memberData as md ON m.memberID = md.memberID 
INNER JOIN dbo.ams_memberDataColumnValues as mdcv ON mdcv.valueID = md.valueID
INNER JOIN dbo.ams_memberDataColumns as mdc ON mdcv.columnID = mdc.columnID
WHERE o.orgcode = 'NY'
and mdc.columnName = 'AAJ Import ID' 
and m.memberID = m.activeMemberID
and mdcv.columnValueString in (
'000016602I','000032171I','000113865I','000119746I','000152501I','000165118I','000180151I','000180152I','000180364I','000191736I','000191793I','000200242I','000200374I','000201262I','000205934I','000208050I','000208244I',
'000213729I','000214263I','000215156I','000216938I','000217128I','000223908I','000224371I','000227968I','000228070I','000228302I','000228323I','000228558I','000228790I','000228841I','000229026I','000229323I','000229344I',
'000229491I','000229994I','000230470I','000233298I','000234730I','000234892I','000234905I','000235309I','000237014I','000237173I','000237174I','000237175I','000238116I','000238209I','000240412I','000250274I','000250396I',
'000253224I','000270030I','000271889I','000273305I','000274768I','000274927I','000275113I','000275114I','000275115I','000275158I','000275245I','000275247I','000276710I','000276815I','000278558I','000279302I','000279810I',
'000284361I','000284429I','000284905I','000285012I','000285058I','000286145I','000286423I','000286436I','000286728I','000288333I','000314908I','000315770I','000315884I','000316140I','000316820I','000316821I','000316822I',
'000316823I','000316848I','000316913I','000317383I','000318289I','000318724I','000318725I','000319454I','000319684I','000319685I','000319687I','000319689I','000319690I','000319691I','000319703I','000319707I','000319708I',
'000319711I','000319712I','000319713I','000319714I','000319716I','000319717I','000319718I','000320068I','000320095I','000322202I','000322547I','000324606I')



