USE [memberCentral]
GO

PRINT '********************************************************************************'
PRINT 'ALTER ams_memberDirectoryClassifications'
PRINT '********************************************************************************'


ALTER TABLE [dbo].[ams_memberDirectoryClassifications]
    ADD classificationOrder [int] 
GO

PRINT '********************************************************************************'
PRINT 'Provide order numbers to all records in table ams_memberDirectoryClassifications'
PRINT '********************************************************************************'

DECLARE @memberDirectoryID [int], @autoID [int], @classificationID [int], @i [int]
DECLARE @memberDirectory TABLE (memberDirectoryID int)
DECLARE @memberDirectoryClassifications TABLE (autoID int IDENTITY(1,1), classificationID int)

INSERT INTO @memberDirectory ([memberDirectoryID])
SELECT 
    DISTINCT memberDirectoryID
FROM 
    dbo.ams_memberDirectoryClassifications
 
 SELECT @memberDirectoryID = MIN(memberDirectoryID) FROM @memberDirectory
 
 WHILE @memberDirectoryID IS NOT NULL
 BEGIN
 
    INSERT INTO @memberDirectoryClassifications ([classificationID])
    SELECT 
        classificationID
     FROM 
        dbo.ams_memberDirectoryClassifications
    WHERE
        memberDirectoryID = @memberDirectoryID
     ORDER BY 
        [name]
     
    PRINT '@memberDirectoryID: ' + CAST(@memberDirectoryID AS varchar)
    
    SELECT @autoID =  MIN(autoID) FROM @memberDirectoryClassifications
    
    SET @i = 1
    WHILE @autoID IS NOT NULL
    BEGIN
    
        SELECT @classificationID =  classificationID FROM @memberDirectoryClassifications WHERE autoID = @autoID
        
        PRINT '@classificationID: ' + CAST(@classificationID AS varchar)
        PRINT '@i: ' + CAST(@i AS varchar)
        
        UPDATE
            dbo.ams_memberDirectoryClassifications
        SET
            classificationOrder = @i
        WHERE
            classificationID = @classificationID
        
        SET @i = @i + 1
        SELECT @autoID =  MIN(autoID) FROM @memberDirectoryClassifications WHERE autoID > @autoID
    end
    
    DELETE FROM @memberDirectoryClassifications
 
    SELECT @memberDirectoryID = MIN(memberDirectoryID) FROM @memberDirectory WHERE memberDirectoryID > @memberDirectoryID
 end
GO

PRINT '********************************************************************************'
PRINT 'CREATE SP ams_reorderMemberDirectoryClassifications'
PRINT '********************************************************************************'

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[ams_reorderMemberDirectoryClassifications]
	@memberDirectoryID int
AS

declare @tmp table (neworder int identity(1,1) NOT NULL, 
					classificationID int NOT NULL, 
					classificationOrder int NOT NULL)

declare	@classificationOrder int, @error_section int

begin tran

	insert into @tmp (classificationID, classificationOrder)
	select 
		mdc.classificationID, mdc.classificationOrder
	from 
		dbo.ams_memberDirectoryClassifications as mdc
	where  
		mdc.memberDirectoryID = @memberDirectoryID
	order by 
		mdc.classificationOrder

	IF @@ERROR <> 0 BEGIN
		select @error_section = 1
		GOTO on_error
	END

	update 
		mdc
	set 
		mdc.classificationOrder = t.neworder
	from
		dbo.ams_memberDirectoryClassifications as mdc 
		inner join @tmp as t on mdc.classificationID = t.classificationID
	where 
		mdc.memberDirectoryID = @memberDirectoryID

	IF @@ERROR <> 0 BEGIN
		select @error_section = 2
		GOTO on_error
	END

	-- normal exit
	IF @@TRANCOUNT > 0 COMMIT TRAN

	RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

PRINT '********************************************************************************'
PRINT 'CREATE SP ams_moveMemberDirectoryClassificationsDown'
PRINT '********************************************************************************'

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[ams_moveMemberDirectoryClassificationsDown]
	@classificationID int
AS

declare 
	@classificationOrder int, 
	@memberDirectoryID int
	
select 
	@classificationOrder = classificationOrder, 
	@memberDirectoryID = memberDirectoryID
from 
	dbo.ams_memberDirectoryClassifications
where 
	classificationID = @classificationID

update 
	mdc
set 
	mdc.classificationOrder = mdc.classificationOrder - 1
from 
	dbo.ams_memberDirectoryClassifications as mdc
where  
	mdc.memberDirectoryID = @memberDirectoryID
	AND mdc.classificationOrder <= @classificationOrder + 1
	
update 
	dbo.ams_memberDirectoryClassifications
set 
	classificationOrder = classificationOrder  + 2
where 
	classificationID = @classificationID

exec dbo.ams_reorderMemberDirectoryClassifications @memberDirectoryID = @memberDirectoryID

return
GO

PRINT '********************************************************************************'
PRINT 'CREATE SP ams_moveMemberDirectoryClassificationsUp'
PRINT '********************************************************************************'

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[ams_moveMemberDirectoryClassificationsUp]
	@classificationID int
AS

declare 
	@classificationOrder int, 
	@memberDirectoryID int
	
select 
	@classificationOrder = classificationOrder, 
	@memberDirectoryID = memberDirectoryID
from 
	dbo.ams_memberDirectoryClassifications
where 
	classificationID = @classificationID

update 
	mdc
set 
	mdc.classificationOrder = mdc.classificationOrder + 1
from 
	dbo.ams_memberDirectoryClassifications as mdc
where  
	mdc.memberDirectoryID = @memberDirectoryID
	AND mdc.classificationOrder >= @classificationOrder - 1

update 
	dbo.ams_memberDirectoryClassifications
set 
	classificationOrder = classificationOrder  - 2
where 
	classificationID = @classificationID

exec dbo.ams_reorderMemberDirectoryClassifications @memberDirectoryID = @memberDirectoryID

RETURN
GO

PRINT '********************************************************************************'
PRINT 'ALTER ams_Classifications'
PRINT '********************************************************************************'

ALTER TABLE [dbo].[ams_Classifications]
    ADD classificationOrder [int] 
GO

PRINT '********************************************************************************'
PRINT 'Provide order numbers to all records in table ams_classifications'
PRINT '********************************************************************************'

DECLARE @siteResourceID [int], @autoID [int], @classificationID [int], @i [int]
DECLARE @siteResource TABLE (siteResourceID int)
DECLARE @classifications TABLE (autoID int IDENTITY(1,1), classificationID int)

INSERT INTO @siteResource (siteResourceID)
SELECT 
    DISTINCT siteResourceID
FROM 
    dbo.ams_classifications
 
 SELECT @siteResourceID = MIN(siteResourceID) FROM @siteResource
 
 WHILE @siteResourceID IS NOT NULL
 BEGIN
 
    INSERT INTO @classifications ([classificationID])
    SELECT 
        classificationID
     FROM 
        dbo.ams_classifications
    WHERE
        siteResourceID = @siteResourceID
     ORDER BY 
        [name]
     
    PRINT '@siteResourceID: ' + CAST(@siteResourceID AS varchar)
    
    SELECT @autoID =  MIN(autoID) FROM @classifications
    
    SET @i = 1
    WHILE @autoID IS NOT NULL
    BEGIN
    
        SELECT @classificationID =  classificationID FROM @classifications WHERE autoID = @autoID
        
        PRINT '@classificationID: ' + CAST(@classificationID AS varchar)
        PRINT '@i: ' + CAST(@i AS varchar)
        
        UPDATE
            dbo.ams_classifications
        SET
            classificationOrder = @i
        WHERE
            classificationID = @classificationID
        
        SET @i = @i + 1
        SELECT @autoID =  MIN(autoID) FROM @classifications WHERE autoID > @autoID
    end
    
    DELETE FROM @classifications
 
    SELECT @siteResourceID = MIN(siteResourceID) FROM @siteResource WHERE siteResourceID > @siteResourceID
 end
GO

PRINT '********************************************************************************'
PRINT 'CREATE SP ams_reorderClassifications'
PRINT '********************************************************************************'

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[ams_reorderClassifications]
	@siteResourceID int
AS

declare @tmp table (neworder int identity(1,1) NOT NULL, 
					classificationID int NOT NULL, 
					classificationOrder int NOT NULL)

declare	@classificationOrder int, @error_section int

begin tran

	insert into @tmp (classificationID, classificationOrder)
	select 
		c.classificationID, c.classificationOrder
	from 
		dbo.ams_classifications as c
	where  
		c.siteResourceID = @siteResourceID
	order by 
		c.classificationOrder

	IF @@ERROR <> 0 BEGIN
		select @error_section = 1
		GOTO on_error
	END

	update 
		c
	set 
		c.classificationOrder = t.neworder
	from
		dbo.ams_classifications as c 
		inner join @tmp as t on c.classificationID = t.classificationID
	where 
		c.siteResourceID = @siteResourceID

	IF @@ERROR <> 0 BEGIN
		select @error_section = 2
		GOTO on_error
	END

	-- normal exit
	IF @@TRANCOUNT > 0 COMMIT TRAN

	RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO


PRINT '********************************************************************************'
PRINT 'CREATE SP ams_moveClassificationsDown'
PRINT '********************************************************************************'

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[ams_moveClassificationsDown]
	@classificationID int
AS

declare 
	@classificationOrder int, 
	@siteResourceID int
	
select 
	@classificationOrder = classificationOrder, 
	@siteResourceID = siteResourceID
from 
	dbo.ams_classifications
where 
	classificationID = @classificationID

update 
	c
set 
	c.classificationOrder = c.classificationOrder - 1
from 
	dbo.ams_classifications as c
where  
	c.siteResourceID = @siteResourceID
	AND c.classificationOrder <= @classificationOrder + 1
	
update 
	dbo.ams_classifications
set 
	classificationOrder = classificationOrder  + 2
where 
	classificationID = @classificationID

exec dbo.ams_reorderClassifications @siteResourceID = @siteResourceID

return
GO

PRINT '********************************************************************************'
PRINT 'CREATE SP ams_moveClassificationsUp'
PRINT '********************************************************************************'

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[ams_moveClassificationsUp]
	@classificationID int
AS

declare 
	@classificationOrder int, 
	@siteResourceID int
	
select 
	@classificationOrder = classificationOrder, 
	@siteResourceID = siteResourceID
from 
	dbo.ams_classifications
where 
	classificationID = @classificationID

update 
	c
set 
	c.classificationOrder = c.classificationOrder + 1
from 
	dbo.ams_classifications as c
where  
	c.siteResourceID = @siteResourceID
	AND c.classificationOrder >= @classificationOrder - 1

update 
	dbo.ams_classifications
set 
	classificationOrder = classificationOrder  - 2
where 
	classificationID = @classificationID

exec dbo.ams_reorderClassifications @siteResourceID = @siteResourceID

RETURN
GO

