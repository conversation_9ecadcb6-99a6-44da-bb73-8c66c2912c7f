-- DEV 


use membercentral
GO
declare @orgID int
select @orgID = orgID from sites where sitecode = 'SC'

select tmp.company, vw.[Office Address_address1], vw.[Office Address_address2], 
	vw.[Office Address_city], s.stateID, vw.[Office Address_postalCode], 
	vw.[Office Address_country], vw.[Contact Type],
	ROW_NUMBER() OVER (ORDER BY tmp.company) as row
into datatransfer.dbo.tmp_SCFirms
from (
	select m.company, min(m.memberID) as memberID
	from membercentral.dbo.ams_members as m
	inner join membercentral.dbo.vw_memberData_SC as vw2 on vw2.memberid = m.memberid
	where len(m.company) > 0
	and m.company <> 'Attorney at Law'
	group by m.company
	having count(*) > 1
) as tmp
inner join membercentral.dbo.vw_memberData_SC as vw on vw.memberid = tmp.memberid
left outer join membercentral.dbo.ams_states as s on s.code = vw.[Office Address_stateprov] and s.countryID = 1
GO

ALTER TABLE datatransfer.dbo.tmp_SCFirms ADD memberid int NULL
GO

declare @companyRTID int, @IndivRTID int, @FirmMemberRTRTID int, @FirmRelTID int, @orgID int
select @orgID = orgID from sites where sitecode = 'SC'
select @companyRTID = recordTypeID from dbo.ams_recordTypes where orgID = @orgID and recordTypeCode = 'LawFirm'
select @IndivRTID = recordTypeID from dbo.ams_recordTypes where orgID = @orgID and recordTypeCode = 'Individual'
select @FirmRelTID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgID and relationshipTypeCode = 'FirmMember'
select @FirmMemberRTRTID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes where masterRecordTypeID = @companyRTID and childRecordTypeID = @IndivRTID and relationshipTypeID = @FirmRelTID


BEGIN TRAN

declare @row int, @firstname varchar(75), @company varchar(300), @ct varchar(200), @membernumber varchar(40), 
	@memberID int, @rc int, @recordTypeID int, @RTID int
select @row = min(row) from datatransfer.dbo.tmp_SCFirms
while @row is not null BEGIN
	select	@memberID = null, @ct = null, @firstname = null, @company = null, @membernumber = null, 
			@RTID = null, @recordTypeID = null

	select @company = company, @ct = [Contact Type]
		from datatransfer.dbo.tmp_SCFirms
		where row = @row

	select @firstname = 'Firm'
	select @membernumber = 'FIRM' + RIGHT('00000' + cast(@row as varchar(4)),5)
	select @recordTypeID = @companyRTID
	select @RTID = @FirmMemberRTRTID

	EXEC @rc = dbo.ams_createMember @orgID=@orgID, @memberTypeID=2, @prefix='', @firstname=@firstname, @middlename='', @lastname='Account', @suffix='', @professionalsuffix='', @company=@company, @memberNumber=@membernumber, @status='A', @memberID=@memberID OUTPUT
		IF @@ERROR <> 0 or @rc <> 0 or @memberID = 0 goto on_error

	UPDATE ams_members
	SET recordTypeID = @recordTypeID
	where memberID = @memberID
		IF @@ERROR <> 0 goto on_error

	UPDATE datatransfer.dbo.tmp_SCFirms
	set memberID = @memberID
	where row = @row
		IF @@ERROR <> 0 goto on_error

	UPDATE ams_members
	SET recordTypeID = @IndivRTID
	where orgID = @orgID
	and memberid = activeMemberID
	and status <> 'D'
	and company = @company
	and recordTypeID is null
		IF @@ERROR <> 0 goto on_error

	INSERT INTO dbo.ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive)
	select @RTID, @memberID, memberid, 1
	from membercentral.dbo.ams_members
	where orgID = @orgID
	and memberid = activeMemberID
	and status <> 'D'
	and company = @company
	and memberID <> @memberID
		IF @@ERROR <> 0 goto on_error

	select @row = min(row) from datatransfer.dbo.tmp_SCFirms where row > @row
END

declare @addressTypeID int
select @addressTypeID = addressTypeID from dbo.ams_memberAddressTypes where orgID = @orgID and addressTypeOrder = 1

insert into dbo.ams_memberAddresses (memberID, addressTypeID, address1, address2, address3, city, stateID, postalCode, countryID)
select memberID, @addressTypeID, isnull([Office Address_address1],''), 
	isnull([Office Address_address2],''), '', 
	isnull([Office Address_city],''), stateID, isnull([Office Address_postalCode],''), 1
from datatransfer.dbo.tmp_SCFirms
	IF @@ERROR <> 0 goto on_error

COMMIT TRAN
goto on_success

on_error:
	ROLLBACK TRAN
	goto on_done

on_success:
	-- queue member groups (@runSchedule=1 indicates immediate processing) 
	declare @itemGroupUID uniqueidentifier, @memberIDList varchar(max)
	SELECT @memberIDList = COALESCE(@memberIDList + ',', '') + cast(memberID as varchar(10)) 
		from datatransfer.dbo.tmp_SCFirms 
		group by memberID
	EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberIDList, @conditionIDList='', @runSchedule=1, @itemGroupUID=@itemGroupUID OUTPUT

on_done:
	DROP TABLE datatransfer.dbo.tmp_SCFirms

GO

