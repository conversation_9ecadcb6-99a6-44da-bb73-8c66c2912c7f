USE [memberCentral]
GO

CREATE PROCEDURE ams_emailWelcomeEmailSingle
	@recordedByMemberID int,
	@memberIDToEmail int,
	@siteID int,
	@createdFrom datetime,
	@createdTo datetime,
	@messageStatusCode varchar(10),
	@messageWrapper varchar(max)

AS
BEGIN
declare @loginNetworkID int, @numRecipients int, @rc int, @messageTypeID int, 
	@mesageStatusID int, @sendingSiteResourceID int, @supportProviderEmail varchar(100),
	@supportProviderName varchar(100), @emailSubject varchar(200), @contentVersionID int,
	@messageID int, @rawcontent varchar(max), @messageToParse varchar(max),
	@fieldID int, @fieldName varchar(60)
declare @metadataFields TABLE (fieldName varchar(60), fieldID int NULL)
select @loginNetworkID = dbo.fn_getloginnetworkfromsiteid(@siteID)
		
IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
	DROP TABLE #tmpRecipients

SELECT distinct m.memberID, m.prefix, m.firstName, m.middlename, m.lastName, m.suffix, me.email, 0 as messageID
into #tmpRecipients
FROM dbo.ams_members as m
inner join dbo.ams_memberEmails as me on m.memberID = me.memberID
inner join dbo.ams_memberEmailTypes as met on me.emailTypeID = met.emailTypeID and met.emailTypeOrder = 1
inner join dbo.ams_memberSiteDefaults as msd on m.memberid = msd.memberid AND msd.siteID = @siteid
WHERE m.memberID = m.activeMemberID
and m.memberID = @memberIDToEmail
and m.status = 'A'
and m.memberTypeID = 2
and m.dateCreated between @createdFrom and @createdTo
and me.Email LIKE '_%'
AND msd.status = 'A'
AND not exists (
	select mnp.mnpID 
	from dbo.ams_membernetworkprofiles as mnp
	inner join dbo.ams_networkprofiles as np on np.profileid = mnp.profileid and np.networkid = @loginNetworkID
	where mnp.memberID = m.memberID
	and mnp.status = 'A'
	AND np.status = 'A' 
)
and exists (
	select srr.groupID
	from dbo.sites as s
	inner join dbo.cms_siteResourceRightsCache AS srr on srr.resourceID = s.siteResourceID
	INNER JOIN dbo.cms_siteResourceFunctions AS srf ON srf.functionID = srr.functionID and srf.functionname = 'Login'
	INNER JOIN dbo.cms_siteResourceTypeFunctions AS srtf ON srtf.functionID = srf.functionID
	INNER JOIN dbo.cms_siteResourceTypes AS srt ON srt.resourceTypeID = srtf.resourceTypeID and srt.resourceType = 'Site'
	INNER JOIN dbo.cache_members_groups as mg on mg.groupID = srr.groupID and mg.memberID = m.memberid
	where s.siteID = @siteid
)

select @numRecipients = count(*) from #tmpRecipients

IF @numRecipients > 0 BEGIN
	select @messageTypeID = messageTypeID from platformMail.dbo.email_messageTypes where messageTypeCode = 'SITEWEL'
	select @sendingSiteResourceID = siteResourceID from dbo.sites where siteID = @siteID
	select @mesageStatusID = statusID from platformMail.dbo.email_statuses where statusCode = @messageStatusCode
	select TOP 1 @supportProviderName = net.supportProviderName, @supportProviderEmail = net.supportProviderEmail,
		@emailSubject = 'Access Instructions for ' + s.siteName, @contentVersionID = cv.contentVersionID,
		@rawcontent = cv.rawContent
		from dbo.networks as net
		inner join dbo.networkSites as ns on net.networkID = ns.networkID
		inner join dbo.sites as s on s.siteID = ns.siteID
		INNER JOIN dbo.cms_contentLanguages as cl ON cl.contentID = s.welcomeMessageContentID AND cl.languageID = 1
		INNER JOIN dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID AND cv.isActive = 1
		where s.siteID = @siteID 
		and ns.isLoginNetwork = 1
		IF @@ERROR <> 0 goto on_error

	-- add email_message
	EXEC @rc = platformMail.dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID, 
		@sendingSiteResourceID=@sendingSiteResourceID, @recordedByMemberID=@recordedByMemberID, 
		@fromName=@supportProviderName, @fromEmail=@supportProviderEmail, 
		@replyToEmail=@supportProviderEmail, @senderEmail=@supportProviderEmail, 
		@subject=@emailSubject, @contentVersionID=@contentVersionID, 
		@messageWrapper=@messageWrapper, @messageID=@messageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @messageID = 0 goto on_error

	-- update recipients table so we can brint it back to CF
	update #tmpRecipients set messageID = @messageID
		IF @@ERROR <> 0 goto on_error

	-- add recipients as I (not ready to be queued yet)
	insert into platformMail.dbo.email_messageRecipientHistory (messageID, memberID, 
		dateLastUpdated, toName, toEmail, emailStatusID, batchID, batchStartDate)
	select @messageID, memberID, getdate(), firstName + ' ' + lastName, email, @mesageStatusID, null, null
	from #tmpRecipients
		IF @@ERROR <> 0 goto on_error

	-- add any necessary metadata fields
	select @messageToParse = replace(@messageWrapper,'@@rawcontent@@',@rawcontent)
		IF @@ERROR <> 0 goto on_error
	insert into @metadataFields (fieldName)
	select distinct [Text]
	from dbo.fn_RegexMatches(@messageToParse,'(?<=\[\[)([^,\]]+)(?=,?([^\]]+)?\]\])')
		IF @@ERROR <> 0 goto on_error
	insert into platformMail.dbo.email_metadataFields (fieldName, isMergeField)
	select fieldName, 1
	from @metadataFields
		except
	select fieldName, isMergeField
	from platformMail.dbo.email_metadataFields
		IF @@ERROR <> 0 goto on_error
	update tmp
	set tmp.fieldID = MF.fieldID
	from @metadataFields as tmp
	inner join platformMail.dbo.email_metadataFields as MF on MF.fieldName = tmp.fieldName
	where MF.isMergeField = 1
		IF @@ERROR <> 0 goto on_error
	select @fieldID = min(fieldID) from @metadataFields where fieldName in ('firstname','lastname','prefix','fullName','extendedname')
	while @fieldID is not null BEGIN
		select @fieldName = fieldName from @metadataFields where fieldID = @fieldID
			IF @@ERROR <> 0 goto on_error
		insert into platformMail.dbo.email_messageMetadataFields (messageID, fieldID, memberID, fieldValue)
		select @messageID, @fieldID, memberID, 
			fieldValue = case @fieldName
				when 'firstname' then firstname
				when 'lastname' then lastname
				when 'prefix' then prefix 
				when 'fullName' then firstname + ' ' + lastname
				when 'extendedname' then firstname + isnull(' ' + nullif(middlename,''),'') + ' ' + lastname + isnull(' ' + nullif(suffix,''),'')
				end
		from #tmpRecipients
			IF @@ERROR <> 0 goto on_error
		select @fieldID = min(fieldID) from @metadataFields where fieldName in ('firstname','lastname','prefix','fullName','extendedname') and fieldID > @fieldID
	END
END

-- return recipients
select * 
from #tmpRecipients

goto on_done


on_error:
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients
	RETURN -1	

on_done:
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients
	RETURN 0

END
GO
