USE [memberCentral]
GO
CREATE TABLE [dbo].[ams_memberHistory] (
	[historyID] [int] IDENTITY(1,1) NOT NULL,
	[typeID] [int] NOT NULL,
	[memberID] [int] NOT NULL,
	[categoryID] [int] NOT NULL,
	[subCategoryID] [int] NULL,
	[userDate] [datetime] NULL,
	[quantity] [int] NULL,
	[dollarAmt] [money] NULL,
	[description] [varchar](max) NULL,
	[dateEntered] [datetime] NOT NULL CONSTRAINT [DF_ams_memberHistory_dateEntered]  DEFAULT (getdate()),
	[enteredByMemberID] [int] NOT NULL,
	[linkMemberID] [int] NULL,
	[oldMongoID] [varchar](40) NULL,
CONSTRAINT [PK_ams_memberHistory] PRIMARY KEY CLUSTERED 
(
	[historyID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[ams_memberHistory]  WITH CHECK ADD  CONSTRAINT [FK_ams_memberHistory_ams_members] FOREIGN KEY([enteredByMemberID])
REFERENCES [dbo].[ams_members] ([memberID])
GO
ALTER TABLE [dbo].[ams_memberHistory] CHECK CONSTRAINT [FK_ams_memberHistory_ams_members]
GO

ALTER TABLE [dbo].[ams_memberHistory]  WITH CHECK ADD  CONSTRAINT [FK_ams_memberHistory_ams_members1] FOREIGN KEY([linkMemberID])
REFERENCES [dbo].[ams_members] ([memberID])
GO
ALTER TABLE [dbo].[ams_memberHistory] CHECK CONSTRAINT [FK_ams_memberHistory_ams_members1]
GO

ALTER TABLE [dbo].[ams_memberHistory]  WITH CHECK ADD  CONSTRAINT [FK_ams_memberHistory_cms_categories] FOREIGN KEY([categoryID])
REFERENCES [dbo].[cms_categories] ([categoryID])
GO
ALTER TABLE [dbo].[ams_memberHistory] CHECK CONSTRAINT [FK_ams_memberHistory_cms_categories]
GO

ALTER TABLE [dbo].[ams_memberHistory]  WITH CHECK ADD  CONSTRAINT [FK_ams_memberHistory_cms_categories1] FOREIGN KEY([subCategoryID])
REFERENCES [dbo].[cms_categories] ([categoryID])
GO
ALTER TABLE [dbo].[ams_memberHistory] CHECK CONSTRAINT [FK_ams_memberHistory_cms_categories1]
GO

ALTER TABLE [dbo].[ams_memberHistory]  WITH CHECK ADD  CONSTRAINT [FK_ams_memberHistory_ams_members2] FOREIGN KEY([memberID])
REFERENCES [dbo].[ams_members] ([memberID])
GO
ALTER TABLE [dbo].[ams_memberHistory] CHECK CONSTRAINT [FK_ams_memberHistory_ams_members2]
GO

insert into ams_memberHistory (typeID, memberID, categoryID, subCategoryID, userDate, quantity, dollarAmt, description, dateEntered, enteredByMemberID, linkMemberID)
select case when isRelationship = 0 then 1 else 2 end, memberID, categoryID, subCategoryID, userDate, quantity, dollarAmt, description, dateEntered, enteredByMemberID, linkMemberID
from dbo.mh_memberHistory
GO

CREATE PROCEDURE [dbo].[ams_deleteMemberHistory] 
@historyID int

AS

delete from dbo.ams_memberHistory
where historyID = @historyID

RETURN 0
GO

CREATE PROCEDURE [dbo].[ams_addMemberHistory] 
@typeID int,
@memberID int,
@categoryID int, 
@subCategoryID int, 
@userDate datetime,
@qty int,
@dollarAmt money,
@description varchar(max),
@linkMemberID int,
@enteredByMemberID int,
@historyID int OUTPUT

AS

set @historyID = 0

BEGIN TRY
	insert into dbo.ams_memberHistory (typeID, memberID, categoryID, subCategoryID, userDate, quantity, dollarAmt, description, dateEntered, enteredByMemberID, linkMemberID)
	values(@typeID, @memberID, @categoryID, @subCategoryID, @userDate, @qty, @dollarAmt, @description, getDate(), @enteredByMemberID, @linkMemberID)
		select @historyID = SCOPE_IDENTITY()

	RETURN 0
END TRY
BEGIN CATCH
	set @historyID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

CREATE PROC [dbo].[ams_importMemberHistory]
@typeID int,
@siteID int,
@siteResourceID int,
@flatfile varchar(160),
@enteredByMemberID int

AS

SET NOCOUNT ON

BEGIN TRY

	declare @orgID int, @catTreeID int, @createSQL varchar(max), @cmd varchar(400), @minType varchar(200), 
		@trashID int, @minCatID int, @parentCatID int
	select @orgID = orgID from dbo.sites where siteID = @siteID
	select @catTreeID = dbo.fn_getCategoryTreeIDForSiteResourceID(@siteResourceID)

	-- ensure files exist
	if dbo.fn_fileExists(@flatfile + '.sql') = 0 or dbo.fn_fileExists(@flatfile + '.bcp') = 0
		RAISERROR('Import files missing', 16, 1);

	-- **************
	-- read in sql create script and create global temp table
	-- **************
	select @createSQL = replace(dbo.fn_ReadFile(@flatfile + '.sql',0,1),'##xxx','##importMemberHistoryData')
	IF OBJECT_ID('tempdb..##importMemberHistoryData') IS NOT NULL
		EXEC('DROP TABLE ##importMemberHistoryData')
	EXEC(@createSQL)

	-- *******************
	-- bcp in data
	-- *******************
	select @cmd = 'bcp ##importMemberHistoryData in ' + @flatfile + '.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
	exec master..xp_cmdshell @cmd, NO_OUTPUT

	-- *******************
	-- immediately put into local temp table and drop global temp (memberid is a holder to be updated later)
	-- *******************
	select 0 as memberID, * into #importMemberHistoryData from ##importMemberHistoryData
	IF OBJECT_ID('tempdb..##importMemberHistoryData') IS NOT NULL
		EXEC('DROP TABLE ##importMemberHistoryData')
	IF OBJECT_ID('tempdb..#importMemberHistoryDataNewCats') IS NOT NULL
		EXEC('DROP TABLE #importMemberHistoryDataNewCats')

	-- Add index for queries below
	CREATE NONCLUSTERED INDEX [idx_importMemberHistoryData] ON #importMemberHistoryData ([membernumber] ASC);

	-- *******************
	-- process data
	-- *******************
	-- ensure all categories are created
	select @minType = min(tb1.type)
		from #importMemberHistoryData tb1 
		left outer join dbo.cms_categories as c on c.categoryName = tb1.type
			and c.categoryTreeID = @catTreeID
			and c.isActive = 1 
			and c.parentCategoryID is NULL
		where c.categoryName is null
	while @minType is not null BEGIN
		EXEC dbo.cms_createCategory @categoryTreeID=@catTreeID, @categoryName=@minType, @categoryDesc='', @categoryCode='', @parentCategoryID=NULL, @contributorMemberID=@enteredByMemberID, @categoryID=@trashID OUTPUT
			IF @trashID = 0 RAISERROR('Unable to create category %s', 16, 1,@minType);

		select @minType = min(tb1.type)
			from #importMemberHistoryData tb1 
			left outer join dbo.cms_categories as c on c.categoryName = tb1.type
				and c.categoryTreeID = @catTreeID
				and c.isActive = 1 
				and c.parentCategoryID is NULL
			where c.categoryName is null
			and tb1.type > @minType
	END

	-- ensure all sub-categories are created
	select cp.categoryID as parentCategoryID, tb12.type as typeName, tb12.category as categoryName, ROW_NUMBER() OVER (order by tb12.type, tb12.category) as rowID
	into #importMemberHistoryDataNewCats
	from #importMemberHistoryData as tb12 
	inner join dbo.cms_categories cP on cP.categoryName = tb12.type and cP.categoryTreeID = @catTreeID and cP.isActive = 1 and cP.parentCategoryID is NULL
	left outer join dbo.cms_categories c on cp.categoryID = c.parentCategoryID and c.categoryName = tb12.category and c.isActive = 1 and c.parentCategoryID is not NULL
	where c.categoryName is null
	and len(tb12.category) > 0
	group by cp.categoryID, tb12.type, tb12.category

	select @minCatID = min(rowID) from #importMemberHistoryDataNewCats
	while @minCatID is not null BEGIN
		select @minType=null, @parentCatID=null

		select @minType=categoryName, @parentCatID=parentCategoryID
		from #importMemberHistoryDataNewCats
		where rowID = @minCatID
	
		EXEC dbo.cms_createCategory @categoryTreeID=@catTreeID, @categoryName=@minType, @categoryDesc='', @categoryCode='', @parentCategoryID=@parentCatID, @contributorMemberID=@enteredByMemberID, @categoryID=@trashID OUTPUT
			IF @trashID = 0 RAISERROR('Unable to create category %s', 16, 1,@minType);
	
		select @minCatID = min(rowID) from #importMemberHistoryDataNewCats where rowID > @minCatID
	END

	-- insert into history
	insert into dbo.ams_memberHistory (typeID, memberID, categoryID, subCategoryID, userDate, quantity, dollarAmt, description, dateEntered, enteredByMemberID, linkMemberID)
	select 1, m.memberID, cP.categoryID, c.categoryID, tb12.date, nullif(tb12.quantity,0), nullif(tb12.dollaramount,0.00), tb12.description, getdate(), @enteredByMemberID, mLink.memberID
	from #importMemberHistoryData as tb12 
	inner join dbo.ams_members as m on m.memberNumber = tb12.membernumber and m.orgID = @orgID and m.memberID = m.activeMemberID
	inner join dbo.cms_categories as cP on cP.categoryName = tb12.type and cP.categoryTreeID = @catTreeID and cP.isActive = 1 and cP.parentCategoryID is NULL
	left outer join dbo.cms_categories as c on cp.categoryID = c.parentCategoryID and c.categoryName = tb12.category and c.isActive = 1 and c.parentCategoryID is not NULL
	left outer join dbo.ams_members as mLink on mLink.memberNumber = tb12.linkedmembernumber and mLink.orgID = @orgID and mLink.memberID = mLink.activeMemberID

	IF OBJECT_ID('tempdb..#importMemberHistoryDataNewCats') IS NOT NULL
		EXEC('DROP TABLE #importMemberHistoryDataNewCats')
	IF OBJECT_ID('tempdb..#importMemberHistoryData') IS NOT NULL
		EXEC('DROP TABLE #importMemberHistoryData')

	RETURN 0
END TRY
BEGIN CATCH
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

update ams_memberHistory
set quantity = null, dollarAmt = null
where quantity = 0 and dollarAmt = 0
GO

update ams_memberHistory
set quantity = null
where quantity = 0
GO

update admin_navigation
set cfcmethod = 'listHistory'
where navigationID = 201
GO

update admin_navigation
set cfcmethod = 'listRelationships'
where navigationID = 65
GO

update admin_navigation
set cfcmethod = 'listNotes'
where navigationID = 184
GO

insert into dbo.admin_functionsDeterminingNav (resourceTypeFunctionID, toolTypeID, navigationID)
values (427,137,201)
GO

update admin_functionsDeterminingNav
set toolTypeID = 137
where functionNavID in (117,118)
GO

delete from dbo.admin_functionsDeterminingNav where navigationID = 184

insert into dbo.admin_functionsDeterminingNav (resourceTypeFunctionID, toolTypeID, navigationID)
values (337,137,184)
insert into dbo.admin_functionsDeterminingNav (resourceTypeFunctionID, toolTypeID, navigationID)
values (338,137,184)
insert into dbo.admin_functionsDeterminingNav (resourceTypeFunctionID, toolTypeID, navigationID)
values (418,137,184)
GO

update admin_navigation
set cfcmethod = 'listCategories'
where navigationID = 95
GO

CREATE PROCEDURE [dbo].[cms_createDefaultHistoryCategories] 
	@siteID int, 
	@contributingMemberID int
AS
BEGIN TRAN

	declare @categoryTreeID int
	declare @controllingSiteResourceID int

	select @controllingSiteResourceID = dbo.fn_getSiteResourceIDForResourceType('HistoryAdmin', @siteID)
	IF @controllingSiteResourceID is NULL GOTO on_error

	select @categoryTreeID = categoryTreeID
	from cms_categoryTrees
	where controllingSiteResourceID = @controllingSiteResourceID

	-- create category tree
	IF @categoryTreeID is NULL
	BEGIN
		exec [cms_createCategoryTree] @siteID=@siteID,@categoryTreeName='HistoryTypes',
		@categoryTreeDesc='Types of History and their subcategories',
		@categoryTreeCode='',
		@controllingSiteResourceID=@controllingSiteResourceID,
		@categoryTreeID=@categoryTreeID OUTPUT	

		IF @@ERROR <> 0 GOTO on_error
	END

IF @@TRANCOUNT > 0 COMMIT TRAN	

-- normal exit
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

ALTER PROC [dbo].[createSite]
	@orgID int,
	@sitecode varchar(10),
	@siteName varchar(60),
	@mainNetworkID int,
	@isLoginNetwork bit,
	@isMasterSite bit,
	@defaultLanguageID int,
	@defaultTimeZoneID int,
	@defaultCurrencyTypeID int,
	@showCurrencyType bit,
	@hasSSL bit,
	@isSSL bit,
	@enableMobile bit,
	@allowGuestAccounts bit,
	@forceLoginPage bit,
	@useRemoteLogin bit,
	@affiliationRequired bit,
	@providesFreeFastCase bit,
	@enforceSiteAgreement bit,
	@allowMemberUpdates bit,
	@immediateMemberUpdates bit,
	@emailMemberUpdates varchar(200),
	@defaultPostalState varchar(10),
	@joinURL varchar(100),
	@pdfPassword varchar(30),
	@alternateGuestAccountCreationLink varchar(400),
	@alternateGuestAccountPopup bit,
	@alternateForgotPasswordLink varchar(400),
	@mainhostname varchar(80),
	@norightsContent varchar(max),
	@norightsNotLoggedInContent varchar(max),
	@inactiveUserContent varchar(max),
	@siteagreementContent varchar(max),
	@welcomeMessageContent varchar(max),
	@firstTimeLoginContent varchar(max),
	@siteID int OUTPUT

AS

DECLARE @rc int, @templateID int, @modeID int, @sectionID int, 
	@sectionResourceTypeID int, @siteAdminRoleID int, @superAdminRoleID int, 
	@siteAdminGroupID int, @superAdminGroupID int, @trashID int

BEGIN TRAN
	-- check for existing sitecode
	SELECT @siteID = null
	SELECT @siteID = siteID FROM dbo.sites where sitecode = @sitecode

	-- if not there, add it
	IF @siteID is not null
		GOTO on_error

	-- insert sites
	INSERT INTO dbo.sites (orgID, sitecode, siteName, defaultLanguageID, defaultTimeZoneId, defaultCurrencyTypeID,
		hasSSL, allowGuestAccounts, forceLoginPage, useRemoteLogin, affiliationRequired, 
		providesFreeFastCase, allowMemberUpdates, enforceSiteAgreement, immediateMemberUpdates, emailMemberUpdates, 
		defaultPostalState, joinURL, pdfPassword, alternateGuestAccountCreationLink, alternateGuestAccountPopup, 
		alternateForgotPasswordLink, showCurrencyType,isSSL, enableMobile)
	VALUES (@orgID, @sitecode, @siteName, @defaultLanguageID, @defaultTimeZoneId, @defaultCurrencyTypeID, 
		@hasSSL, @allowGuestAccounts, @forceLoginPage, @useRemoteLogin, @affiliationRequired, 
		@providesFreeFastCase, @allowMemberUpdates, @enforceSiteAgreement, @immediateMemberUpdates, @emailMemberUpdates, 
		@defaultPostalState, @joinURL, @pdfPassword, @alternateGuestAccountCreationLink, @alternateGuestAccountPopup, 
		@alternateForgotPasswordLink, @showCurrencyType,@isSSL, @enableMobile)
		IF @@ERROR <> 0 GOTO on_error
		SELECT @siteID = SCOPE_IDENTITY()

	-- createSiteLanguage		
	EXEC @rc = dbo.createSiteLanguage @siteID=@siteID, @languageID=@defaultLanguageID		
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- get resourceType for site
	declare @siteResourceTypeID int
	select @siteResourceTypeID = dbo.fn_getResourceTypeID('Site')

	-- get active resource status
	declare @siteResourceStatusID int
	select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

	-- create a resourceID for the site
	DECLARE @siteResourceID int	
	exec dbo.cms_createSiteResource
		@resourceTypeID = @siteResourceTypeID,
		@siteResourceStatusID = @siteResourceStatusID,
		@siteID = @siteid,
		@isVisible = 1,
		@parentSiteResourceID = null,
		@siteResourceID = @siteResourceID OUTPUT
		IF @@ERROR <> 0 OR @siteResourceID = 0 GOTO on_error
	
	-- update site with new resource
	UPDATE dbo.sites
	SET siteResourceID = @siteResourceID
	WHERE siteID = @siteID
		IF @@ERROR <> 0 GOTO on_error
		
	-- roles
	select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')
	select @siteAdminRoleID = dbo.fn_getResourceRoleID('Site Administrator')
	select @siteAdminGroupID = groupID 
		from dbo.ams_groups 
		where groupCode = 'SiteAdmins' 
		and orgID = @orgID
	select @superAdminGroupID = groupID 
		from dbo.ams_groups 
		where groupCode = 'SuperAdmins' 
		and orgID = 1

	-- give siteAdmin Role to siteAdmin Group
	EXEC @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteID,
		@siteResourceID=@siteResourceID,
		@include=1,
		@functionID=null,
		@roleID=@siteAdminRoleID,
		@groupID=@siteAdminGroupID,
		@memberID=null,
		@inheritedRightsResourceID=null,
		@inheritedRightsFunctionID=null,
		@resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- give superAdmin Role to superAdmin Group
	EXEC @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteID,
		@siteResourceID=@siteResourceID,
		@include=1,
		@functionID=null,
		@roleID=@superAdminRoleID,
		@groupID=@superAdminGroupID,
		@memberID=null,
		@inheritedRightsResourceID=null,
		@inheritedRightsFunctionID=null,
		@resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- add default hostname
	DECLARE @hostnameID int	
	EXEC @rc = dbo.createSiteHostName @siteID=@siteID, @hostname=@mainhostname, @useRedirect=null, @hostnameID=@hostnameID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- add default template
	DECLARE @templateTypeID int
	SELECT @templateTypeID = dbo.fn_getTemplateTypeID('Page')
	EXEC @rc = dbo.cms_CreatePageTemplate @siteid=@siteID, @templateTypeID=@templateTypeID, @templateName='Default', @templateDesc='Default site template', @templateFileName='Default', @templateID=@templateID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @templateID = 0 GOTO on_error2
	
	-- add default page section
	SELECT @modeID = dbo.fn_getModeID('Normal')
	SELECT @sectionResourceTypeID = dbo.fn_getResourceTypeID('SystemCreatedSection')
	EXEC @rc = dbo.cms_createPageSection @siteID=@siteID, @sectionResourceTypeID=@sectionResourceTypeID, @ovTemplateID=@templateID, @ovTemplateIDMobile=null, @ovModeID=@modeID, @parentSectionID=null, @sectionName='Root', @sectionCode='Root', @inheritPlacements=1, @sectionID=@sectionID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- default fieldsets
	EXEC @rc = dbo.cms_createDefaultFieldsets @siteid=@siteID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add default pages
	EXEC @rc = dbo.cms_createDefaultPages @siteid=@siteID, @sectionid=@sectionID, @languageID=@defaultLanguageID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add default Relationship Categories
	EXEC @rc = dbo.cms_createDefaultRelationshipCategories @siteid=@siteID, @contributingMemberID=461530
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add default History (notes) Categories
	EXEC @rc = dbo.cms_createDefaultHistoryCategories @siteid=@siteID, @contributingMemberID=461530
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add to network
	EXEC @rc = dbo.createNetworkSite @networkID=@mainNetworkID, @siteID=@siteID, @isLoginNetwork=@isLoginNetwork, @isMasterSite=@isMasterSite
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add default FULL Frequency
	insert into dbo.sub_frequencies(frequencyName, frequency, frequencyShortName, uid, 
		rateRequired, hasInstallments, monthlyInterval, isSystemRate, siteID, status)
	values('Full', 1, 'F', newid(), 1, 1, 1, 1, @siteID, 'A')	
		IF @@ERROR <> 0 GOTO on_error2

	-- add content objects
	DECLARE @sysCreatedContentResourceTypeID int, @activesiteResourceStatusID int, @newContentid int, @newresourceid int
	select @sysCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('SystemCreatedContent')
	select @activesiteResourceStatusID = dbo.fn_getResourceStatusId('Active')
	EXEC @rc = dbo.cms_createContentObject 
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'NoRights',
		@contentDesc = null,
		@rawContent = @norightsContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET noRightsContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject 
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'NoRightsNotLoggedIn',
		@contentDesc = null,
		@rawContent = @norightsNotLoggedInContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET noRightsNotLoggedInContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'InactiveUser',
		@contentDesc = null,
		@rawContent = @inactiveUserContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET InactiveUserContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'SiteAgreement',
		@contentDesc = null,
		@rawContent = @siteagreementContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET SiteAgreementContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'Welcome Message',
		@contentDesc = null,
		@rawContent = @welcomeMessageContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET welcomeMessageContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'First Time Login Message',
		@contentDesc = null,
		@rawContent = @firstTimeLoginContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET firstTimeLoginContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2

	-- link up superusers to all new sites
	INSERT INTO dbo.ams_memberNetworkProfiles (memberID, profileID, [status], dateCreated, siteID)
	SELECT distinct mnp.memberID, mnp.profileID, 'A', getdate(), @siteID
	FROM dbo.ams_memberNetworkProfiles AS mnp 
	INNER JOIN dbo.ams_networkProfiles AS np ON mnp.profileID = np.profileID
	WHERE mnp.status = 'A'
	AND np.networkID = dbo.fn_getNetworkID('MemberCentral Super Administrators')
	AND np.status = 'A'
	AND mnp.siteID <> @siteID
		IF @@ERROR <> 0 GOTO on_error2

	EXEC @rc = dbo.cms_populateSiteResourceRightsCache @siteID=@siteID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @siteID = 0
	RETURN -1

on_error2:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1

GO

declare @siteID int
SELECT @siteID=min(siteID) FROM admin_siteTools where toolTypeID = 60
while @siteID is not null BEGIN
	EXEC dbo.cms_createDefaultHistoryCategories @siteid=@siteID, @contributingMemberID=461530
	SELECT @siteID=min(siteID) FROM admin_siteTools where toolTypeID = 60 and siteID > @siteID
END
GO

DROP PROC dbo.ams_createRelationship
GO
DROP PROC dbo.mh_importMemberHistory
GO
DROP PROC dbo.mh_addMemberHistory
GO
DROP PROC dbo.mh_deleteMemberHistory
GO
DROP TABLE dbo.mh_memberHistory
GO

insert into dbo.cms_siteResourceTypeFunctions (resourceTypeID, functionID)
values (144,104)
insert into dbo.cms_siteResourceTypeFunctions (resourceTypeID, functionID)
values (144,105)
GO

update admin_navigation set orderBy = 3 where navigationID = 41
update admin_navigation set orderBy = 4 where navigationID = 65
update admin_navigation set orderBy = 5 where navigationID = 184
update admin_navigation set orderBy = 6 where navigationID = 201
update admin_navigation set orderBy = 7 where navigationID = 79
update admin_navigation set orderBy = 8 where navigationID = 198
update admin_navigation set orderBy = 9 where navigationID = 199
GO

update admin_navigation set orderBy = 1 where navigationID = 42
update admin_navigation set orderBy = 2 where navigationID = 53
update admin_navigation set orderBy = 3 where navigationID = 103
update admin_navigation set orderBy = 4 where navigationID = 54
update admin_navigation set orderBy = 5 where navigationID = 43
update admin_navigation set orderBy = 6 where navigationID = 182
update admin_navigation set orderBy = 7 where navigationID = 72
update admin_navigation set orderBy = 8 where navigationID = 78
update admin_navigation set orderBy = 9 where navigationID = 70
update admin_navigation set orderBy = 10 where navigationID = 66
update admin_navigation set orderBy = 11 where navigationID = 95
update admin_navigation set orderBy = 12 where navigationID = 202
update admin_navigation set orderBy = 13 where navigationID = 200
GO

CREATE PROC dbo.ams_importMemberHistory_toHolding
@siteid int,
@typeID int,
@csvfilename varchar(200),
@strTableColumnNames varchar(max),
@pathToExport varchar(100),
@importResult xml OUTPUT

AS

DECLARE @qry varchar(max), @sitecode varchar(10), @tmptbl varchar(17), @var_tmpCols varchar(20), @orgID int
declare @good bit, @dynSQL nvarchar(max), @prefix varchar(50), @flatfile varchar(160), @exportcmd varchar(400)
declare @tblMissingCols TABLE (colName varchar(255))
declare @tblErrors TABLE (rowid int IDENTITY(1,1), msg varchar(300), fatal bit)
declare @tblCounts TABLE (rowid int IDENTITY(1,1), countName varchar(50), countNum int)
declare @catTree_1 int, @catTree_2 int, @catTree_3 int

-- get category trees
select @catTree_1 = dbo.fn_getCategoryTreeIDForSiteResourceID(dbo.fn_getSiteResourceIDForResourceType('MemberHistoryAdmin',@siteID))
select @catTree_2 = dbo.fn_getCategoryTreeIDForSiteResourceID(dbo.fn_getSiteResourceIDForResourceType('RelationshipAdmin',@siteID))
select @catTree_3 = dbo.fn_getCategoryTreeIDForSiteResourceID(dbo.fn_getSiteResourceIDForResourceType('HistoryAdmin',@siteID))

-- get table name
select @sitecode=sitecode, @orgID=orgID from dbo.sites where siteID = @siteid
select @tmptbl = '##tmpMH' + @sitecode
select @var_tmpCols = '##tmpColsMH' + @sitecode

-- delete temp table if exists
IF OBJECT_ID('tempdb..' + @tmptbl) IS NOT NULL 
	EXEC('DROP TABLE ' + @tmptbl)

-- create temp table using columnlist
select @qry = COALESCE(@qry + ', ', '') + quotename(listitem) + ' varchar(max) '
FROM dbo.fn_varCharListToTable(@strTableColumnNames,char(7))
order by autoid
	EXEC('CREATE TABLE ' + @tmptbl + ' (' + @qry + ')')

-- rowID and membernumber is in a key column of an index and needs to not be varchar(max)
declare @trash bit
set @trash = 1
EXEC('ALTER TABLE ' + @tmptbl + ' ALTER COLUMN rowID int') 
BEGIN TRY
	EXEC('ALTER TABLE ' + @tmptbl + ' ALTER COLUMN membernumber varchar(800)') 
END TRY
BEGIN CATCH
	set @trash = 0
END CATCH

-- Execute a bulk insert into previously defined temporary table
SELECT @qry = 'BULK INSERT ' + @tmptbl + ' FROM ''' + @csvfilename + ''' WITH (FIELDTERMINATOR = '''+ char(7) + ''', FIRSTROW = 2);'
EXEC(@qry)

IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
	EXEC('DROP TABLE ' + @var_tmpCols)
IF OBJECT_ID('tempdb..#tblOrgCols') IS NOT NULL 
	DROP TABLE #tblOrgCols
IF OBJECT_ID('tempdb..#tblImportCols') IS NOT NULL 
	DROP TABLE #tblImportCols

-- ******************************** 
-- ensure all columns exist (fatal)
-- ******************************** 
-- this will get the columns that should be there
CREATE TABLE #tblOrgCols (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
	COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
	SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
	COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
	CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)

-- Need the temp table code for the export. 
IF @typeID = 1
	select @dynSQL = 'select top 1 m.MemberNumber, cType.categoryName as Category, cType.categoryName as Subcategory, 
						mh.userDate as Date, mh.Description, mh.Quantity, mh.dollarAmt as Amount, 
						m.MemberNumber as LinkedMemberNumber
						into ' + @var_tmpCols + '
						from dbo.ams_members as m
						inner join dbo.ams_memberHistory as mh on mh.memberID = m.memberID
						inner join dbo.cms_categories as cType on cType.categoryID = mh.categoryID
						where m.orgID = 0'
IF @typeID = 2
	select @dynSQL = 'select top 1 m.MemberNumber, cType.categoryName as Relationship, 
						mh.userDate as Date, mh.Description, m.MemberNumber as LinkedMemberNumber
						into ' + @var_tmpCols + '
						from dbo.ams_members as m
						inner join dbo.ams_memberHistory as mh on mh.memberID = m.memberID
						inner join dbo.cms_categories as cType on cType.categoryID = mh.categoryID
						where m.orgID = 0'
IF @typeID = 3
	select @dynSQL = 'select top 1 m.MemberNumber, cType.categoryName as Category, cType.categoryName as Subcategory, 
						mh.userDate as Date, mh.Description, m.MemberNumber as LinkedMemberNumber
						into ' + @var_tmpCols + '
						from dbo.ams_members as m
						inner join dbo.ams_memberHistory as mh on mh.memberID = m.memberID
						inner join dbo.cms_categories as cType on cType.categoryID = mh.categoryID
						where m.orgID = 0'
EXEC(@dynSQL)

-- get cols
INSERT INTO #tblOrgCols
EXEC tempdb.dbo.SP_COLUMNS @var_tmpCols
	
-- cleanup table no longer needed
IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
	EXEC('DROP TABLE ' + @var_tmpCols)

-- this will get the columns that are actually in the import
CREATE TABLE #tblImportCols (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
	COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
	SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
	COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
	CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)

	-- get cols
	INSERT INTO #tblImportCols
	EXEC tempdb.dbo.SP_COLUMNS @tmptbl

INSERT INTO @tblErrors (msg, fatal)
select 'The column ' + org.column_name + ' is missing from your data.', 1
from #tblOrgCols as org
left outer join #tblImportCols as imp on imp.column_name = org.column_name
where imp.table_name is null

insert into @tblMissingCols(colname)
select org.column_name
from #tblOrgCols as org
left outer join #tblImportCols as imp on imp.column_name = org.column_name
where imp.table_name is null

-- dont drop these temp tables here because we use them for skipped rows below

-- ********************************
-- data type checks (fatal)
-- ********************************
IF NOT EXISTS (select colName from @tblMissingCols where colName = 'date') BEGIN
	EXEC('UPDATE ' + @tmptbl + ' SET [date] = null where len([date]) = 0')

	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [date] datetime null
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column Date contains invalid dates.', 1)
END
				
IF @typeID = 1 and NOT EXISTS (select colName from @tblMissingCols where colName = 'quantity') BEGIN
	EXEC('UPDATE ' + @tmptbl + ' SET quantity = null where len(quantity) = 0')

	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [quantity] int null
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column Quantity contains invalid whole number values.', 1)
END
				
IF @typeID = 1 and NOT EXISTS (select colName from @tblMissingCols where colName = 'Amount') BEGIN
	EXEC('UPDATE ' + @tmptbl + ' SET Amount = null where len(Amount) = 0')

	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN Amount money null
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column Amount contains invalid dollar values.', 1)
END

-- ********************************
-- change Relationship to Category for easier querying below
-- ********************************
IF @typeID = 2 and NOT EXISTS (select colName from @tblMissingCols where colName = 'Relationship') BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			EXEC tempdb..sp_rename ''' + @tmptbl + '.Relationship'', ''Category'', ''COLUMN''
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column Relationship could not be processed.', 1)
END

-- ********************************
-- no/bad member number (fatal)
-- ********************************
IF NOT EXISTS (select colName from @tblMissingCols where colName = 'memberNumber') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' is missing a Member Number. Member Numbers are required for all entries.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE (memberNumber IS NULL OR ltrim(rtrim(memberNumber)) = '''')
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' Member Number ('' + tb.membernumber + '') was not found. Member Numbers must match existing members.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' as tb
		left outer join dbo.ams_members as m on m.memberNumber = tb.membernumber
			and m.orgID = ' + cast(@orgID as varchar(10)) + '
			and m.memberID = m.activeMemberID
		WHERE tb.membernumber IS NOT NULL 
		AND ltrim(rtrim(tb.membernumber)) <> ''''
		AND m.memberNumber is null
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'linkedmembernumber') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' Linked Member Number ('' + tb.linkedmembernumber + '') was not found. Linked Member Numbers must match existing members.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' as tb
		left outer join dbo.ams_members as m on m.memberNumber = tb.linkedmembernumber
			and m.orgID = ' + cast(@orgID as varchar(10)) + '
			and m.memberID = m.activeMemberID
		WHERE tb.linkedmembernumber IS NOT NULL 
		AND ltrim(rtrim(tb.linkedmembernumber)) <> ''''
		AND m.memberNumber is null
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

-- ********************************
-- no/bad categories (fatal)
-- ********************************
IF NOT EXISTS (select colName from @tblMissingCols where colName = 'Category') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' is missing a ' + case when @typeID = 2 then 'Relationship Type' else 'Category' end + '. ' + case when @typeID = 2 then 'Relationship Types' else 'Categories' end + ' are required for all entries.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE (Category IS NULL OR ltrim(rtrim(Category)) = '''')
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ' + case when @typeID = 2 then 'Relationship Type' else 'Category' end + ' ('' + tb.Category + '') was not found. Values must match existing ' + case when @typeID = 2 then 'Relationship Types' else 'Categories' end + '.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' as tb
		left outer join dbo.cms_categories as c on c.categoryName = tb.category
			and c.categoryTreeID = ' + 
			case 
			when @typeID = 1 then cast(@catTree_1 as varchar(10))
			when @typeID = 2 then cast(@catTree_2 as varchar(10))
			when @typeID = 3 then cast(@catTree_3 as varchar(10))
			end + '
			and c.isActive = 1
			and c.parentCategoryID is NULL
		WHERE tb.Category IS NOT NULL 
		AND ltrim(rtrim(tb.Category)) <> ''''
		AND c.categoryName is null
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

IF @typeID <> 2 and NOT EXISTS (select colName from @tblMissingCols where colName = 'subcategory') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' Subcategory ('' + tb.subcategory + '') was not found. Values must match existing Subcategories.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' as tb
		left outer join dbo.cms_categories as c 
			inner join dbo.cms_categories as cP on cp.categoryID = c.parentCategoryID
				and cP.categoryTreeID = ' + 
				case 
				when @typeID = 1 then cast(@catTree_1 as varchar(10))
				when @typeID = 3 then cast(@catTree_3 as varchar(10))
				end + '
				and cP.isActive = 1 
				and cP.parentCategoryID is NULL
			on c.categoryName = tb.Subcategory
			and cP.categoryName = tb.Category
			and c.isActive = 1 
			and c.parentCategoryID is not NULL
		WHERE tb.SubCategory IS NOT NULL 
		AND ltrim(rtrim(tb.SubCategory)) <> ''''
		AND c.categoryName is null
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

-- cleanup - these are no longer needed
IF OBJECT_ID('tempdb..#tblOrgCols') IS NOT NULL
	DROP TABLE #tblOrgCols
IF OBJECT_ID('tempdb..#tblImportCols') IS NOT NULL
	DROP TABLE #tblImportCols

-- ******************************** 
-- dump flat table and format file and creation script
-- would love to use xml format files, but it appears column names with spaces cause it to fail
-- ******************************** 
SELECT @prefix = @sitecode + '_MH_flat_' + convert(varchar(8),getdate(),112) + replace(convert(varchar(8),getdate(),108),':','')
SELECT @flatfile = @pathToExport + @prefix

select @exportcmd = 'bcp ' + @tmptbl + ' format nul -f ' + @flatfile + '.txt -n -T -S' + CAST(serverproperty('servername') as varchar(40))
EXEC master..xp_cmdshell @exportcmd, NO_OUTPUT

select @exportcmd = 'bcp ' + @tmptbl + ' out ' + @flatfile + '.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
EXEC master..xp_cmdshell @exportcmd, NO_OUTPUT

declare @createTableSQLTop varchar(100)          
declare @coltypes table (datatype varchar(16))          
insert into @coltypes values('bit')          
insert into @coltypes values('binary')          
insert into @coltypes values('bigint')          
insert into @coltypes values('int')          
insert into @coltypes values('float')          
insert into @coltypes values('datetime')          
insert into @coltypes values('text')          
insert into @coltypes values('image')          
insert into @coltypes values('money')          
insert into @coltypes values('uniqueidentifier')          
insert into @coltypes values('smalldatetime')          
insert into @coltypes values('tinyint')          
insert into @coltypes values('smallint')          
insert into @coltypes values('sql_variant')          
select @dynSQL = ''
select @dynSQL = @dynSQL +           
	case when charindex('(',@dynSQL,1)<=0 then '(' else '' end + '[' + Column_Name + '] ' +Data_Type +
	case when Data_Type in (Select datatype from @coltypes) then '' else  '(' end+
	case when data_type in ('real','decimal','numeric')  then cast(isnull(numeric_precision,'') as varchar)+','+
	case when data_type in ('real','decimal','numeric') then cast(isnull(Numeric_Scale,'') as varchar) end
	when data_type in ('nvarchar','varchar') and cast(isnull(Character_Maximum_Length,'') as varchar) = '-1' then 'max'
	when data_type in ('char','nvarchar','varchar','nchar') then cast(isnull(Character_Maximum_Length,'') as varchar) else '' end+
	case when Data_Type in (Select datatype from @coltypes)then '' else  ')' end+
	case when Is_Nullable='No' then ' Not null,' else ' null,' end
	from tempdb.Information_Schema.COLUMNS where Table_Name=@tmptbl
	order by ordinal_position
select @createTableSQLTop = 'Create table ##xxx ' 
select @dynSQL=@createTableSQLTop + substring(@dynSQL,1,len(@dynSQL)-1) +' )'            
if dbo.fn_WriteFile(@pathToExport + @prefix + '.sql', @dynSQL, 1) = 0 BEGIN
	RETURN 0
END

-- create index on temp table for numbers below
EXEC('CREATE NONCLUSTERED INDEX [idx_' + @tmptbl + '_memnum_rowid] ON ' + @tmptbl + '([membernumber] ASC,[rowID] ASC)')

-- ******************************** 
-- Counts
-- ******************************** 
IF NOT EXISTS (select rowID from @tblErrors where fatal = 1) BEGIN
	-- TotalEntriesImported
	select @dynSQL = 'SELECT ''TotalEntriesImported'', COUNT(rowID) FROM ' + @tmptbl
	INSERT INTO @tblCounts (countName, countNum)
	EXEC(@dynSQL)
END
				
-- ********************************
-- generate result xml file 
-- ********************************
select @importResult = (
	select getdate() as "@date", @flatfile as "@flatfile",
		isnull((select top 301 dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg", "@severity" = case fatal when 1 then 'fatal' else 'nonfatal' end
		from @tblErrors
		order by rowid
		FOR XML path('error'), root('errors'), type),'<errors/>'),

		isnull((select countName as "@name", countNum as "@num"
		from @tblCounts
		order by rowid
		FOR XML path('count'), root('counts'), type),'<counts/>')
	for xml path('import'), TYPE)

-- drop temp tables 
IF OBJECT_ID('tempdb..' + @tmptbl) IS NOT NULL
	EXEC('DROP TABLE ' + @tmptbl)

RETURN 0
GO

ALTER PROC [dbo].[ams_importMemberHistory]
@siteID int,
@typeID int,
@flatfile varchar(160),
@enteredByMemberID int

AS

SET NOCOUNT ON

BEGIN TRY

	declare @orgID int, @catTreeID int, @createSQL varchar(max), @cmd varchar(400)
	select @orgID = orgID from dbo.sites where siteID = @siteID
	select @catTreeID = case 
		when @typeID = 1 then dbo.fn_getCategoryTreeIDForSiteResourceID(dbo.fn_getSiteResourceIDForResourceType('MemberHistoryAdmin',@siteID))
		when @typeID = 2 then dbo.fn_getCategoryTreeIDForSiteResourceID(dbo.fn_getSiteResourceIDForResourceType('RelationshipAdmin',@siteID))
		when @typeID = 3 then dbo.fn_getCategoryTreeIDForSiteResourceID(dbo.fn_getSiteResourceIDForResourceType('HistoryAdmin',@siteID))
		end

	-- ensure files exist
	if dbo.fn_fileExists(@flatfile + '.sql') = 0 or dbo.fn_fileExists(@flatfile + '.bcp') = 0
		RAISERROR('Import files missing', 16, 1);

	-- **************
	-- read in sql create script and create global temp table
	-- **************
	select @createSQL = replace(dbo.fn_ReadFile(@flatfile + '.sql',0,1),'##xxx','##importMemberHistoryData')
	IF OBJECT_ID('tempdb..##importMemberHistoryData') IS NOT NULL
		EXEC('DROP TABLE ##importMemberHistoryData')
	EXEC(@createSQL)

	-- *******************
	-- bcp in data
	-- *******************
	select @cmd = 'bcp ##importMemberHistoryData in ' + @flatfile + '.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
	exec master..xp_cmdshell @cmd, NO_OUTPUT

	-- *******************
	-- immediately put into local temp table and drop global temp (memberid is a holder to be updated later)
	-- *******************
	select * into #importMemberHistoryData from ##importMemberHistoryData
	IF @typeID = 2
		ALTER TABLE #importMemberHistoryData ADD subCategory varchar(200) NULL, quantity int NULL, amount money NULL;
	IF @typeID = 3
		ALTER TABLE #importMemberHistoryData ADD quantity int NULL, amount money NULL;
	IF OBJECT_ID('tempdb..##importMemberHistoryData') IS NOT NULL
		EXEC('DROP TABLE ##importMemberHistoryData')

	-- Add index for queries below
	CREATE NONCLUSTERED INDEX [idx_importMemberHistoryData] ON #importMemberHistoryData ([membernumber] ASC);

	-- *******************
	-- process data
	-- *******************
	insert into dbo.ams_memberHistory (typeID, memberID, categoryID, subCategoryID, userDate, quantity, dollarAmt, description, dateEntered, enteredByMemberID, linkMemberID)
	select @typeID, m.memberID, cP.categoryID, c.categoryID, tb12.date, nullif(tb12.quantity,0), tb12.Amount, tb12.description, getdate(), @enteredByMemberID, mLink.memberID
	from #importMemberHistoryData as tb12 
	inner join dbo.ams_members as m on m.memberNumber = tb12.membernumber and m.orgID = @orgID and m.memberID = m.activeMemberID
	inner join dbo.cms_categories as cP on cP.categoryName = tb12.category and cP.categoryTreeID = @catTreeID and cP.isActive = 1 and cP.parentCategoryID is NULL
	left outer join dbo.cms_categories as c on cp.categoryID = c.parentCategoryID and c.categoryName = tb12.SubCategory and c.isActive = 1 and c.parentCategoryID is not NULL
	left outer join dbo.ams_members as mLink on mLink.memberNumber = tb12.linkedmembernumber and mLink.orgID = @orgID and mLink.memberID = mLink.activeMemberID

	IF OBJECT_ID('tempdb..#importMemberHistoryData') IS NOT NULL
		EXEC('DROP TABLE #importMemberHistoryData')

	RETURN 0
END TRY
BEGIN CATCH
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

