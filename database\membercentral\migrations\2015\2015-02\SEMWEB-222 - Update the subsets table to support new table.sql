-- Run in listmanager\trialslyris1 database 

update ss set
	ClauseFrom_ = 'sw_marketing',
	ClauseSelect_ = 'email',
	ClauseWhere ='-- sw_marketing support    
					sw_marketing.list = members_.list_ and upper(sw_marketing.orgcode) = upper(members_.association_) and upper(members_.association_) =''' + convert(varchar(20), sw.orgcode) + ''' '
from dbo.subsets_ ss
inner join sw_marketing sw 
	on	sw.orgcode=ss.name_
	and ss.list_ = 'seminarweblive'

