ALTER PROC dbo.lyris_populateMCRecipientsByListByDay
@reportDate date

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#completedReceipients') IS NOT NULL
	    DROP TABLE #completedReceipients;
	CREATE TABLE #completedReceipients (mailingID int);

	DECLARE @calculatedStart datetime = @reportDate;
	DECLARE @calculatedEnd datetime = DATEADD(MS,-3,DATEADD(DAY,1,@calculatedStart));

	INSERT INTO #completedReceipients (mailingID)
	select mailingID
	from trialslyris1.dbo.lyrCompletedRecips (nolock)
	where FinalAttempt between @calculatedStart and @calculatedEnd;

	CREATE INDEX IX_completedReceipients_mailingID ON #completedReceipients (mailingID asc);

	-- add receipients by list per day
	INSERT INTO lyrisarchive.dbo.mc_recipientsByListByDay (listID, reportDate, numRecipients)
	select ml.listID, @reportDate, count(*)
	from #completedReceipients as cr
	inner join trialslyris1.dbo.outmail_ as om on om.MessageID_ = cr.mailingID
	inner join lyrisarchive.dbo.messageLists as ml on ml.list = om.list_
	group by ml.listID;

	IF OBJECT_ID('tempdb..#completedReceipients') IS NOT NULL
		DROP TABLE #completedReceipients;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO
