USE [memberCentral]
GO
/****** Object:  UserDefinedFunction [dbo].[fn_sub_getDuplicateSubscriptions]    Script Date: 09/03/2014 14:13:45 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER FUNCTION [dbo].[fn_sub_getDuplicateSubscriptions] ()
RETURNS TABLE 
AS
RETURN 
(
	select m2.memberid, m2.membernumber, m2.lastname + ', ' + m2.firstname + isnull(' ' + nullif(m2.middleName,''),'') as memberName,
		s.subscriptionName, ss.statusName, o.orgcode, sub.subscriptionID, sub.subEndDate
	from dbo.sub_subscribers as sub
	inner join dbo.sub_subscriptions as s on s.subscriptionID = sub.subscriptionID
	inner join dbo.sub_statuses as ss on ss.statusID = sub.statusID
	inner join dbo.ams_members as m on m.memberID = sub.memberID
	inner join dbo.ams_members as m2 on m2.memberID = m.activememberID
	inner join dbo.organizations as o on o.orgID = m2.orgID
	where (
		(ss.statusCode = 'A' and sub.paymentStatusID = 1)
		or
		(ss.statusCode = 'P')
		or
		(ss.statusCode = 'O')
	)
	group by m2.memberid, m2.membernumber, m2.lastname, m2.firstname, m2.middleName, s.subscriptionName, ss.statusName, o.orgcode, sub.subscriptionID, sub.subEndDate
	having count(*) > 1
)
