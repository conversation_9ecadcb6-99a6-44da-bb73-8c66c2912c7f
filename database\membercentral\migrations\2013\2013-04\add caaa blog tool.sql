USE [customApps]
GO
/****** Object:  Table [dbo].[CAAA_Blog_Categories]    Script Date: 11/23/2011 09:07:27 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
SET ANSI_PADDING ON
GO
CREATE TABLE [dbo].[CAAA_Blog_Categories](
	[blogCategoryID] [int] IDENTITY(1,1) NOT NULL,
	[blogCategoryDescription] [varchar](50) NOT NULL,
	[sortOrder] [int] NULL,
 CONSTRAINT [PK_CAAA_BlogCategories] PRIMARY KEY CLUSTERED 
(
	[blogCategoryID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
SET ANSI_PADDING OFF
GO
/****** Object:  Table [dbo].[CAAA_Blog_Entries]    Script Date: 11/23/2011 09:07:27 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
SET ANSI_PADDING ON
GO
CREATE TABLE [dbo].[CAAA_Blog_Entries](
	[blogEntryID] [int] IDENTITY(1,1) NOT NULL,
	[blogID] [int] NOT NULL,
	[title] [varchar](250) NULL,
	[link] [varchar](500) NULL,
	[pubDate] [datetime] NULL,
	[guid] [varchar](500) NULL,
	[description] [varchar](max) NULL,
	[shortDesc] [varchar](400) NULL,
 CONSTRAINT [PK_CAAA_Blog_Entries] PRIMARY KEY CLUSTERED 
(
	[blogEntryID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
SET ANSI_PADDING OFF
GO
/****** Object:  Table [dbo].[CAAA_Blog_Feeds]    Script Date: 11/23/2011 09:07:27 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
SET ANSI_PADDING ON
GO
CREATE TABLE [dbo].[CAAA_Blog_Feeds](
	[blogID] [int] IDENTITY(1,1) NOT NULL,
	[blogCategoryID] [int] NULL,
	[blogURL] [varchar](350) NULL,
	[blogDescription] [varchar](50) NULL,
	[addAuth] [bit] NOT NULL CONSTRAINT [DF_CAAA_Blog_Feeds_addAuth]  DEFAULT ((0)),
	[sortOrder] [int] NULL,
	[isValid] [int] NOT NULL CONSTRAINT [DF_CAAA_Blog_Feeds_isValid]  DEFAULT ((0)),
 CONSTRAINT [PK_CAAA_BlogFeeds] PRIMARY KEY CLUSTERED 
(
	[blogID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
SET ANSI_PADDING OFF

GO

declare @toolTypeID int
declare @toolResourceTypeID int
declare @navigationID int
declare @resourceTypeFunctionID int

SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('Admin'),dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('Admin')))

exec createAdminToolType @toolType='CAAABlogAdmin', @toolCFC='Custom.CAAA.CAAA.BlogAdmin', @toolDesc='CAAA Blog Administration', @toolTypeID=@toolTypeID OUTPUT, @resourceTypeID=@toolResourceTypeID OUTPUT
	exec createAdminNavigation @navName='Blogs',@navDesc=NULL,@parentNavigationID=8,@navAreaID=3,@cfcMethod='list',@isHeader=0, @showInNav=1,@navigationID=@navigationID OUTPUT
		exec dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID=@resourceTypeFunctionID, @toolTypeID=@toolTypeID, @navigationID=@navigationID


insert into admin_siteToolRestrictions (toolTypeID, siteID) values(@toolTypeID, 105)
  
exec [createAdminSuite] 105
GO


-- Create the entry for the scheduled task.  Add it in a disabled state

INSERT INTO [memberCentral].[dbo].[scheduledTasks]
           ([name]
           ,[nextRunDate]
           ,[interval]
           ,[intervalTypeID]
           ,[taskCFC]
           ,[timeoutMinutes]
           ,[disabled]
           ,[siteid])
     VALUES
           ('CAAA Blog Updater'
           ,'4/1/2013 6:29:40 AM'
           ,1
           ,4
           ,'model.scheduledTasks.tasks.CAAA-BlogUpdater'
           ,5
           ,0
           ,105)
GO
          