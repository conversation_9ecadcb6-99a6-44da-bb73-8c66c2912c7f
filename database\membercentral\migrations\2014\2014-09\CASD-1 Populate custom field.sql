-- DEV Run time: 00:00:14
use membercentral
GO

declare 
	@siteID int, 
	@orgID int, 
	@practiceAreaColumnID int, 
	@thisColumnID int, 
	@thisColumnName varchar	(255), 
	@valueID int, 
	@thisItem int,
	@memberID int,
	@columnName varchar	(255) 

set @columnName = 'Practice Areas' 

declare @memberData as table (thisItem int  IDENTITY(1,1), columnID int , columnName varchar	(255), valueID int, columnValueString varchar(255), memberID int)

declare @practiceAreas as table (valueID int, columnValue varchar	(255))

select @siteID = siteID, @orgID = orgID from sites where siteCode = 'SA'

select @practiceAreaColumnID = columnID from dbo.ams_memberDataColumns  mdc where orgID = @orgID and columnName = @columnName

select @practiceAreaColumnID 

-- Get existing custom fields this assume all fields define as boolean.
insert into @memberData (columnID, columnName, valueID, columnValueString, memberID)
select 
	mdc.columnID, mdc.columnName, mdcv.valueID, mdcv.columnValueString, md.memberID
from 
	dbo.ams_memberDataColumns  mdc 
	inner join dbo.ams_memberDataColumnValues mdcv on
		mdcv.columnID = mdc.columnID
		and mdcv.columnValueBit = 1
	inner join dbo.ams_memberData md on
		md.valueID = mdcv.valueID
where 
	orgID = @orgID 
	and columnName in (
		select dbField from dbo.ams_memberFieldSets fs
		inner join dbo.ams_memberFields mf on mf.fieldSetID = fs.fieldSetID
		where fs.fieldsetName = @columnName 
		and fs.siteID = @siteID
	)

-- Update temporary table with mapping values.
-- Provided in mapping document.
update @memberData set columnName = 'Administrative Law' where columnName = 'Administrative'
update @memberData set columnName = 'Alternative Dispute Resolution' where columnName = 'ADR'
update @memberData set columnName = 'Animal Law' where columnName = 'Animal'
update @memberData set columnName = 'Appeals' where columnName = 'Appeals'
update @memberData set columnName = 'Attorney Malpractice' where columnName = 'Attorney Malpractice'
update @memberData set columnName = 'Automobile/Liablility Collisions' where columnName = 'Automobile Liablility Collision'
update @memberData set columnName = 'Aviation' where columnName = 'Aviation'
update @memberData set columnName = 'Bankruptcy Law' where columnName = 'Bankruptcy'
update @memberData set columnName = 'Business Litigation' where columnName = 'Business Litigation'
update @memberData set columnName = 'Business Planning' where columnName = 'Business Plan'
update @memberData set columnName = 'Class Action' where columnName = 'Class Action'
update @memberData set columnName = 'Construction Defect' where columnName = 'ConDefect'
update @memberData set columnName = 'Credit Repair' where columnName = 'Credit Repair'
update @memberData set columnName = 'Criminal Defense' where columnName = 'Criminal Defense'
update @memberData set columnName = 'Disability' where columnName = 'Disability'
update @memberData set columnName = 'Elder Law' where columnName = 'Elder'
update @memberData set columnName = 'Eminent Domain' where columnName = 'Eminent Domain'
update @memberData set columnName = 'Environmental' where columnName = 'Environment'
update @memberData set columnName = 'Estate Planning/Probate' where columnName = 'Estate Probate'
update @memberData set columnName = 'Family Law' where columnName = 'Family'
update @memberData set columnName = 'Homeowner Associations' where columnName = 'Home Association'
update @memberData set columnName = 'Immigration' where columnName = 'Immigration'
update @memberData set columnName = 'Insurance Bad Faith' where columnName = 'Insurance Bad Faith'
update @memberData set columnName = 'Intellectual Property' where columnName = 'Intellectual Property'
update @memberData set columnName = 'Juvenile' where columnName = 'Juvenile'
update @memberData set columnName = 'Labor/Employment' where columnName = 'Labor Employment'
update @memberData set columnName = 'Mediation' where columnName = 'Mediation'
update @memberData set columnName = 'Medical Malpractice' where columnName = 'Medical Malpractice'
update @memberData set columnName = 'Personal Injury' where columnName = 'Personal Injury'
update @memberData set columnName = 'Premises Liability' where columnName = 'Premises Liability'
update @memberData set columnName = 'Product Liability' where columnName = 'Product Liability'
update @memberData set columnName = 'Real Estate' where columnName = 'Real Estate'
update @memberData set columnName = 'Securities' where columnName = 'Securities'
update @memberData set columnName = 'Social Security' where columnName = 'Social Security'
update @memberData set columnName = 'Workers'' Compensation' where columnName = 'Workers Compensation'

print '==== Updated mapping from old column name to new'

select *  from @memberData


insert into @practiceAreas (valueID, columnValue)
select 
	mdcv.valueID, mdcv.columnValueString as columnValue
from
	ams_memberDataColumns mdc
	inner join ams_memberdatacolumnvalues mdcv
		on mdcv.columnID = mdc.columnID
		and mdc.columnName = @columnName
where 
	orgID = @orgID 

 select * from @practiceAreas

print '=========================================================================='

-- Migrate member settings from legacy custom fields to new custom field.

select @thisItem = min(thisItem) from @memberData 

while @thisItem is not null begin
	
	set @thisColumnName = null
	set @memberID = null 
	set @valueID = null

	select 
		@memberID = memberid,
		@thisColumnName = columnName
	from 
		@memberData 
	where 
		thisItem = @thisItem

	select 
		@valueID = valueID
	from 
		@practiceAreas 
	where	
		columnValue = @thisColumnName
	
	print @memberID
	print @thisColumnName
	print @valueID

	exec dbo.ams_saveMemberData @memberID=@memberID, @columnID=@practiceAreaColumnID, @columnvalueID=@valueID, @columnValue=null, @byPassQueue=1

	print '*********'

	select @thisItem = min(thisItem) from @memberData where thisItem > @thisItem

end

GO


