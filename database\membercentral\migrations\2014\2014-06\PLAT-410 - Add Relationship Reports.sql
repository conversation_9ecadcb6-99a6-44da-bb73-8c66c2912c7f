-- remove old navigation item no longer used
update admin_navigation set isActive = 0 where navigationID = 141

declare @toolTypeID1 int, @resourceTypeID1 int, @toolTypeID2 int, @resourceTypeID2 int
EXEC dbo.createAdminToolType @toolType='MMRMemberRelationships', @toolCFC='Reports.members.MemberRelationships', @toolDesc='Member Relationships', @toolTypeID=@toolTypeID1 OUTPUT, @resourceTypeID=@resourceTypeID1 OUTPUT
EXEC dbo.createAdminToolType @toolType='MMRMemberRelationshipsByType', @toolCFC='Reports.members.MemberRelationshipsByType', @toolDesc='Member Relationships By Type', @toolTypeID=@toolTypeID2 OUTPUT, @resourceTypeID=@resourceTypeID2 OUTPUT

declare @parentnavigationID int, @navigationID int
EXEC dbo.createAdminNavigation @navName='Member Relationships', @navDesc='Member Relationships Reports', @parentNavigationID=122, @navAreaID=3, @cfcMethod=null, @isHeader=0, @showInNav=1, @navigationID=@parentnavigationID OUTPUT
EXEC dbo.createAdminNavigation @navName='Member Relationships By Member', @navDesc='List of member relationships by member', @parentNavigationID=@parentnavigationID, @navAreaID=4, @cfcMethod='showReport', @isHeader=0, @showInNav=1, @navigationID=@navigationID OUTPUT
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID=58, @toolTypeID=@toolTypeID1, @navigationID=@navigationID
EXEC dbo.createAdminNavigation @navName='Member Relationships By Type', @navDesc='List of member relationships by type', @parentNavigationID=@parentnavigationID, @navAreaID=4, @cfcMethod='showReport', @isHeader=0, @showInNav=1, @navigationID=@navigationID OUTPUT
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID=58, @toolTypeID=@toolTypeID2, @navigationID=@navigationID

declare @siteID int
select @siteID=min(siteID) from dbo.sites
while @siteID is not null begin
	exec dbo.createAdminSuite @siteID=@siteID
	select @siteID = min(siteID) from dbo.sites where siteID > @siteID
end