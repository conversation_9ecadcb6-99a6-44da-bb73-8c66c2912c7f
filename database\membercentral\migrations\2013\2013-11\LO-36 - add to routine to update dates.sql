use customApps
GO
declare @udid int

insert into dbo.schedTask_memberJoinDates (siteCode, joinDateFieldName, rejoinDateFieldName, droppedDateFieldName, lastSuccessDate, lastErrorCode, isActive, paidThruDateFieldName)
values ('LO', 'Join Date', 'Rejoin Date', 'Termination Date', '1/1/1972', 0, 1, 'Expiration Date')
	select @udid = SCOPE_IDENTITY()

insert into dbo.schedTask_memberJoinDateSubTypes (memberJoinDateUDID, subscriptionTypeUID)
values (@udid, '76E79B5E-67EF-4173-8602-F44374564830')
insert into dbo.schedTask_memberJoinDateSubTypes (memberJoinDateUDID, subscriptionTypeUID)
values (@udid, 'E825293E-F6FB-43EC-9856-B8C011AD8A2A')
GO


declare @error_code int
EXEC dbo.job_memberJoinDates @udid=9, @error_code=@error_code OUTPUT
select @error_code