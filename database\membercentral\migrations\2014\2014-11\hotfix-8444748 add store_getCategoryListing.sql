USE [memberCentral]
GO
CREATE PROC [dbo].[store_getCategoryListing]
@storeID int

AS

IF OBJECT_ID('tempdb..#storeCats') IS NOT NULL 
	DROP TABLE #storeCats

IF OBJECT_ID('tempdb..#fullySetupProducts') IS NOT NULL 
	DROP TABLE #fullySetupProducts

declare @siteResourceStatusID int
create table #storeCats (categoryID int PRIMARY KEY, categoryName varchar(200), parentCategoryID int, thePath varchar(500), thepathexpanded varchar(max), directItemCount int)
create table #fullySetupProducts (itemID int, categoryID int)

select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

insert into #storeCats (categoryID, categoryName, parentCategoryID, thePath, thepathexpanded)
SELECT categoryID, categoryName, parentCategoryID, thePath, thepathexpanded
FROM fn_getRecursiveStoreCategories(@storeID, null, null) as c
option(recompile)

insert into #fullySetupProducts (itemID,categoryID)
select distinct spf.itemid, pcl.categoryID
from dbo.store_Products as sp
inner join store_ProductFormats spf 
	on sp.itemid = spf.itemid
	and spf.status = 'A'
	and sp.status = 'A'
	AND sp.storeID = @storeID
	and sp.showAvailable = 1
inner join store_rates sr
	on spf.formatid = sr.formatid
inner join cms_siteResources c 
	on sr.siteResourceID = c.siteResourceID
	and c.siteResourceStatusID = @siteResourceStatusID
INNER JOIN dbo.store_ProductCategoryLinks as pcl 
	on pcl.itemID = sp.itemID
option(recompile)

create index IX_itemID_categoryID on #fullySetupProducts (itemID asc, categoryID asc)

update sc set
	directItemCount = temp.itemCount
from #storeCats sc
inner join (
	select sc2.categoryID, count(itemID) as itemCount
	from #storeCats sc2
	inner join #fullySetupProducts fsp
		on fsp.categoryID = sc2.categoryID
	group by sc2.categoryID
) temp on temp.categoryID = sc.categoryID
option(recompile)

select sc.categoryID, sc.categoryName, sc.parentCategoryID, sc.thePath, sc.thepathexpanded,left(sc.thePath, 4) as rootPath, sc.directItemCount, sum(tree.directItemCount) as itemCount, count(tree.categoryID) as numCategories
from #storeCats sc
inner join #storeCats tree
	on sc.thePath = left(tree.thePath,len(sc.thePath))
group by sc.categoryID, sc.categoryName, sc.parentCategoryID, sc.thePath, sc.thepathexpanded, sc.directItemCount
order by sc.thePath
option(recompile)


set nocount off	

IF OBJECT_ID('tempdb..#storeCats') IS NOT NULL 
	DROP TABLE #storeCats

IF OBJECT_ID('tempdb..#fullySetupProducts') IS NOT NULL 
	DROP TABLE #fullySetupProducts
GO