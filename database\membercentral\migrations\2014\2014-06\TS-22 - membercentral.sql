use datatransfer
GO
create TABLE swl_eligibleForNatleMarketingList (
	platform varchar(20), 
	orgcode varchar(5), 
	memberID int, 
	membernumber varchar(100), 
	fullname varchar(500), 
	email varchar(200)
)

USE [customApps]
GO
create PROCEDURE [dbo].[swl_getEligibleForNatleMarketingList] 

AS

IF OBJECT_ID('tempdb..#eligibleContacts') IS NOT NULL
	DROP TABLE #eligibleContacts

create TABLE #eligibleContacts (platform varchar(20), orgcode varchar(5), memberID int, membernumber varchar(100), fullname varchar(500), email varchar(200), rownumber int)


truncate table datatransfer.dbo.swl_eligibleForNatleMarketingList

declare @orgs TABLE (orgcode varchar(5) PRIMARY KEY, isLiveOnNewPlatform bit, isPortalLaunched char(1))



declare @functionID int, @SRActive int, @listname varchar(100)
set @listname = 'seminarweblive'
set @functionID = membercentral.dbo.fn_getResourceFunctionID('TrialSmithAllowed', membercentral.dbo.fn_getResourceTypeID('Site'))
set @SRActive = membercentral.dbo.fn_getResourceStatusID('Active')

insert into @orgs (orgcode,isLiveOnNewPlatform, isPortalLaunched)
select tla.state, tla.isLiveOnNewPlatform, tla.isPortalLaunched
from tlasites.seminarweb.dbo.tblParticipants p
inner join tlasites.seminarweb.dbo.tblNationalProgramParticipants npp
	on npp.participantID = p.participantID
	and p.isSWL = 1
inner join tlasites.seminarweb.dbo.tblNationalPrograms np
	on np.programID = npp.programID
	and np.programName = 'NATLE'
inner join tlasites.trialsmith.dbo.depotla tla
	on tla.state = p.orgcode
	and tla.includeInSWEmailMarketing = 1
	

insert into #eligibleContacts (platform, orgcode, memberID, membernumber, fullname, email , rownumber)

select 'MEMBERCENTRAL' as platform, po.orgcode,m.memberID, m.membernumber, fullname = m.firstname + ' ' + m.lastname, ltrim(rtrim(me.email)), row_number() over (partition by ltrim(rtrim(me.email)) order by m.memberID)
from @orgs po
inner join membercentral.dbo.organizations o
	on o.orgcode = po.orgcode
	and po.isLiveOnNewPlatform = 1
	and po.orgcode <> 'ts'
inner join membercentral.dbo.sites s
	on s.orgID = o.orgID
inner join membercentral.dbo.cms_siteResources sr
	on s.siteResourceID = sr.siteResourceID
	and sr.siteResourceStatusID = @SRActive
inner join membercentral.dbo.cache_perms_siteResourceFunctionRightPrints srfrp
	on srfrp.siteResourceID = sr.siteResourceID
	and srfrp.functionID = @functionID
inner join membercentral.dbo.cache_perms_groupPrintsRightPrints gprp
	on gprp.rightPrintID = srfrp.rightPrintID
inner join membercentral.dbo.ams_members m
	on m.groupPrintID = gprp.groupPrintID
	and m.orgID = o.orgID
	and m.memberID = m.activeMemberID
	and m.status = 'A'
inner join membercentral.dbo.ams_memberEmails me
	on me.memberID = m.memberID
inner join membercentral.dbo.ams_memberEmailTypes met
	on met.emailTypeOrder = 1
	and met.emailTypeID = me.emailTypeID

union

select 'TLASITES' as platform, po.orgcode,m.orgmemberdataID as memberID, m.membernumber, fullname = m.firstname + ' ' + m.lastname, ltrim(rtrim(m.email)), row_number() over (partition by ltrim(rtrim(m.email)) order by m.orgmemberdataID)
from @orgs po
inner join tlasites.trialsmith.dbo.depotla tla
	on tla.state = po.orgcode
	and po.isLiveOnNewPlatform = 0
	and po.orgcode <> 'ts'
inner join tlasites.trialsmith.dbo.orgmemberdata m
	on m.orgcode = tla.state
	and m.status = 'A'
	and m.disabled = 0
	and m.allowTS = 1


CREATE INDEX IX_eligibleContacts_email ON #eligibleContacts (email asc, platform asc);

delete 
from #eligibleContacts
where rownumber > 1 or email = ''

delete ec
from #eligibleContacts ec
inner join #eligibleContacts ec2
	on ec.platform = 'tlasites'
	and ec2.platform = 'membercentral'
	and ec.email = ec2.email

delete
from #eligibleContacts
WHERE membercentral.dbo.fn_RegExReplace(email,'^[a-zA-Z_0-9-''\+~]+(\.[a-zA-Z_0-9-''\+~]+)*@([a-zA-Z_0-9-]+\.)+[a-zA-Z]{2,7}$','') <> '' 

insert into datatransfer.dbo.swl_eligibleForNatleMarketingList (platform, orgcode, memberID, membernumber, fullname, email)
select platform, orgcode, memberID, membernumber, fullname, email
from #eligibleContacts


IF OBJECT_ID('tempdb..#eligibleContacts') IS NOT NULL
	DROP TABLE #eligibleContacts

GO
