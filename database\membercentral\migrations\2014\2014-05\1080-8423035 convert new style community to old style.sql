
-- LIST NOTE
--
--  REMEMBER FOR SDCBA lists to change the configuration of the list viewer application in lists_listViewer table.
--
--  The column includeAllListsFromLoginNetwork needs to be set to 0 for the community to render the list browse.
--  
--  You need to determine the appropriate list.
--  update lists_listViewer set includeAllListsFromLoginNetwork = 0 where listViewerID = @listViewerID
--



declare @siteCode varchar(5), @siteID int, @pageName varchar(100), @pageID int, @commSectionID int, @widgetSiteResourceID int, @zoneIDA int, @trashID int, @zoneIDMain int

set @siteCode = 'sdcba'
set @pageName = 'Crawford'



select @siteID = dbo.fn_getSiteIDFromSiteCode(@siteCode)
select @zoneIDA = zoneID from cms_pageZones where zoneName = 'A'
select @zoneIDMain = zoneID from cms_pageZones where zoneName = 'Main'

select @commSectionID = ps.sectionID, @pageID = p.pageID, @widgetSiteResourceID=wsr.siteResourceID
from cms_pages p
inner join cms_siteResources psr
	on psr.siteResourceID = p.siteResourceID
	and p.siteID = @siteID
	and p.pagename = @pageName
	and psr.siteResourceStatusID = 1
inner join cms_siteResources asr
	on asr.parentSiteResourceID = psr.siteResourceID
inner join cms_siteResources pssr
	on pssr.parentSiteResourceID = asr.siteResourceID
inner join cms_siteResources wsr
	on wsr.parentSiteResourceID = asr.siteResourceID
inner join cms_applicationInstances ai
	on ai.siteResourceID = asr.siteResourceID
inner join comm_communities c
	on c.applicationInstanceID = ai.applicationInstanceID
inner join cms_applicationWidgetInstances awi
	on awi.applicationInstanceID = ai.applicationInstanceID
	and awi.siteResourceID = wsr.siteResourceID
inner join cms_pageSections ps
	on ps.siteResourceID = pssr.siteResourceID
order by p.pageid desc


--update widgetinstanacesettingsxml
update cms_applicationWidgetInstances set settingsXML = null
where siteResourceID = @widgetSiteResourceID

-- remove widget from pzr
delete
from cms_pageZonesResources 
where siteResourceID = @widgetSiteResourceID

EXEC dbo.cms_reorderPageZonesResources @pageID=@pageID, @zoneID=@zoneIDMain

-- add widget to pszr

exec dbo.cms_createPageSectionZoneResource
	@sectionID=@commSectionID, @zoneID=@zoneIDA, @siteResourceID=@widgetSiteResourceID, @pszrID=@trashID OUTPUT