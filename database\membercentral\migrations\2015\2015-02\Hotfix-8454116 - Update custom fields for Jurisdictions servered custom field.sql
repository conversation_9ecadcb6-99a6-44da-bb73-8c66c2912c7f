declare @orgID int, @columnID int
select @orgID = orgID from sites where sitecode='CCDB'


select @columnID = columnID
from ams_memberdatacolumns
where columnName = 'Jurisdictions Served' and orgID = @orgID

declare @tblImport table (memberNumber varchar(40), JServed varchar(750))
insert into @tblImport (memberNumber, JServed) values('100','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('101','1st (<PERSON><PERSON>, <PERSON>)|2nd (Denver)|17th (Adams, Broomfield)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('103','7th (Delta, Gunnison, Hinsdale, Montrose, Ouray, San Miguel)|9th (Garfield, Pitkin, Rio Blanco)|21st (Mesa)')
insert into @tblImport (memberNumber, JServed) values('111','6th (Archuleta, La Plata, San Juan)|22nd (Dolores, Montezuma)')
insert into @tblImport (memberNumber, JServed) values('112','4th (El Paso, Teller)|10th (Pueblo)')
insert into @tblImport (memberNumber, JServed) values('113','4th (El Paso, Teller)')
insert into @tblImport (memberNumber, JServed) values('114','1st (Gilpin, Jefferson)|2nd (Denver)|5th (Clear Creek, Eagle, Lake, Summit)|8th (Jackson, Larimer)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|19th (Weld)')
insert into @tblImport (memberNumber, JServed) values('115','20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('118','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('120','4th (El Paso, Teller)')
insert into @tblImport (memberNumber, JServed) values('126','1st (Gilpin, Jefferson)|2nd (Denver)|8th (Jackson, Larimer)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|19th (Weld)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('128','1st (Gilpin, Jefferson)|2nd (Denver)|4th (El Paso, Teller)|8th (Jackson, Larimer)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|19th (Weld)|20th (Boulder)|21st (Mesa)')
insert into @tblImport (memberNumber, JServed) values('130','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('134','17th (Adams, Broomfield)')
insert into @tblImport (memberNumber, JServed) values('136','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('138','6th (Archuleta, La Plata, San Juan)')
insert into @tblImport (memberNumber, JServed) values('139','1st (Gilpin, Jefferson)|2nd (Denver)|11th (Chaffee, Custer, Fremont, Park)|18th (Arapahoe, Douglas, Elbert, Lincoln)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('144','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|19th (Weld)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('145','4th (El Paso, Teller)')
insert into @tblImport (memberNumber, JServed) values('147','2nd (Denver)')
insert into @tblImport (memberNumber, JServed) values('150','1st (Gilpin, Jefferson)|4th (El Paso, Teller)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('152','4th (El Paso, Teller)')
insert into @tblImport (memberNumber, JServed) values('156','2nd (Denver)')
insert into @tblImport (memberNumber, JServed) values('158','2nd (Denver)|4th (El Paso, Teller)|10th (Pueblo)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('163','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|19th (Weld)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('168','8th (Jackson, Larimer)|19th (Weld)')
insert into @tblImport (memberNumber, JServed) values('169','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('170','8th (Jackson, Larimer)|13th (Kit Carson, Logan, Morgan, Phillips, Sedgwick)|17th (Adams, Broomfield)|19th (Weld)')
insert into @tblImport (memberNumber, JServed) values('171','2nd (Denver)')
insert into @tblImport (memberNumber, JServed) values('173','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('175','1st (Gilpin, Jefferson)|2nd (Denver)|3rd (Huerfano, Las Animas)|4th (El Paso, Teller)|5th (Clear Creek, Eagle, Lake, Summit)|6th (Archuleta, La Plata, San Juan)|7th (Delta, Gunnison, Hinsdale, Montrose, Ouray, San Miguel)|8th (Jackson, Larimer)|9th (Garfield, Pitkin, Rio Blanco)|10th (Pueblo)|11th (Chaffee, Custer, Fremont, Park)|12th (Alamosa, Conejos, Costilla, Rio Grande, Saguache)|13th (Kit Carson, Logan, Morgan, Phillips, Sedgwick)|14th (Grand, Moffat, Routt)|15th (Baca, Cheyenne, Kiowa, Prowers)|16th (Bent, Crowley, Otero)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|19th (Weld)|20th (Boulder)|21st (Mesa)|22nd (Dolores, Montezuma)|Appellate')
insert into @tblImport (memberNumber, JServed) values('187','2nd (Denver)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('188','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('200','1st (Gilpin, Jefferson)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('206','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('208','4th (El Paso, Teller)')
insert into @tblImport (memberNumber, JServed) values('210','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('212','1st (Gilpin, Jefferson)|2nd (Denver)|8th (Jackson, Larimer)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('215','2nd (Denver)')
insert into @tblImport (memberNumber, JServed) values('216','4th (El Paso, Teller)|10th (Pueblo)|11th (Chaffee, Custer, Fremont, Park)')
insert into @tblImport (memberNumber, JServed) values('219','20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('222','1st (Gilpin, Jefferson)|2nd (Denver)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('223','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)')
insert into @tblImport (memberNumber, JServed) values('225','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('229','20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('231','5th (Clear Creek, Eagle, Lake, Summit)|11th (Chaffee, Custer, Fremont, Park)')
insert into @tblImport (memberNumber, JServed) values('232','8th (Jackson, Larimer)|13th (Kit Carson, Logan, Morgan, Phillips, Sedgwick)|19th (Weld)')
insert into @tblImport (memberNumber, JServed) values('233','20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('236','4th (El Paso, Teller)')
insert into @tblImport (memberNumber, JServed) values('238','8th (Jackson, Larimer)|19th (Weld)')
insert into @tblImport (memberNumber, JServed) values('247','1st (Gilpin, Jefferson)|17th (Adams, Broomfield)')
insert into @tblImport (memberNumber, JServed) values('251','4th (El Paso, Teller)')
insert into @tblImport (memberNumber, JServed) values('254','2nd (Denver)|17th (Adams, Broomfield)')
insert into @tblImport (memberNumber, JServed) values('256','8th (Jackson, Larimer)|19th (Weld)')
insert into @tblImport (memberNumber, JServed) values('257','8th (Jackson, Larimer)')
insert into @tblImport (memberNumber, JServed) values('259','1st (Gilpin, Jefferson)|2nd (Denver)|8th (Jackson, Larimer)|17th (Adams, Broomfield)|19th (Weld)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('260','18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('262','21st (Mesa)')
insert into @tblImport (memberNumber, JServed) values('264','7th (Delta, Gunnison, Hinsdale, Montrose, Ouray, San Miguel)|21st (Mesa)')
insert into @tblImport (memberNumber, JServed) values('274','1st (Gilpin, Jefferson)|2nd (Denver)|8th (Jackson, Larimer)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|19th (Weld)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('275','4th (El Paso, Teller)')
insert into @tblImport (memberNumber, JServed) values('280','7th (Delta, Gunnison, Hinsdale, Montrose, Ouray, San Miguel)')
insert into @tblImport (memberNumber, JServed) values('284','3rd (Huerfano, Las Animas)|12th (Alamosa, Conejos, Costilla, Rio Grande, Saguache)')
insert into @tblImport (memberNumber, JServed) values('286','2nd (Denver)')
insert into @tblImport (memberNumber, JServed) values('293','21st (Mesa)')
insert into @tblImport (memberNumber, JServed) values('296','18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('297','1st (Gilpin, Jefferson)|2nd (Denver)|5th (Clear Creek, Eagle, Lake, Summit)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('302','2nd (Denver)|18th (Arapahoe, Douglas, Elbert, Lincoln)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('303','2nd (Denver)')
insert into @tblImport (memberNumber, JServed) values('304','10th (Pueblo)')
insert into @tblImport (memberNumber, JServed) values('308','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('310','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('311','Appellate')
insert into @tblImport (memberNumber, JServed) values('312','7th (Delta, Gunnison, Hinsdale, Montrose, Ouray, San Miguel)|9th (Garfield, Pitkin, Rio Blanco)|21st (Mesa)')
insert into @tblImport (memberNumber, JServed) values('313','4th (El Paso, Teller)|10th (Pueblo)')
insert into @tblImport (memberNumber, JServed) values('314','9th (Garfield, Pitkin, Rio Blanco)|14th (Grand, Moffat, Routt)')
insert into @tblImport (memberNumber, JServed) values('315','17th (Adams, Broomfield)')
insert into @tblImport (memberNumber, JServed) values('319','18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('320','1st (Gilpin, Jefferson)| 2')
insert into @tblImport (memberNumber, JServed) values('323','4th (El Paso, Teller)')
insert into @tblImport (memberNumber, JServed) values('324','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('332','20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('335','8th (Jackson, Larimer)|19th (Weld)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('340','21st (Mesa)')
insert into @tblImport (memberNumber, JServed) values('341','4th (El Paso, Teller)|10th (Pueblo)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('343','1st (Gilpin, Jefferson)|17th (Adams, Broomfield)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('344','10th (Pueblo)')
insert into @tblImport (memberNumber, JServed) values('349','1st (Gilpin, Jefferson)')
insert into @tblImport (memberNumber, JServed) values('350','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|19th (Weld)')
insert into @tblImport (memberNumber, JServed) values('351','4th (El Paso, Teller)')
insert into @tblImport (memberNumber, JServed) values('358','7th (Delta, Gunnison, Hinsdale, Montrose, Ouray, San Miguel)|9th (Garfield, Pitkin, Rio Blanco)|21st (Mesa)')
insert into @tblImport (memberNumber, JServed) values('360','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('364','18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('366','17th (Adams, Broomfield)')
insert into @tblImport (memberNumber, JServed) values('367','8th (Jackson, Larimer)|19th (Weld)')
insert into @tblImport (memberNumber, JServed) values('369','4th (El Paso, Teller)')
insert into @tblImport (memberNumber, JServed) values('371','1st (Gilpin, Jefferson)| 9')
insert into @tblImport (memberNumber, JServed) values('372','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)')
insert into @tblImport (memberNumber, JServed) values('373','4th (El Paso, Teller)')
insert into @tblImport (memberNumber, JServed) values('374','6th (Archuleta, La Plata, San Juan)|22nd (Dolores, Montezuma)')
insert into @tblImport (memberNumber, JServed) values('378','2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('379','2nd (Denver)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('382','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('392','1st (Gilpin, Jefferson)| 2')
insert into @tblImport (memberNumber, JServed) values('393','21st (Mesa)')
insert into @tblImport (memberNumber, JServed) values('394','1st (Gilpin, Jefferson)|2nd (Denver)|18th (Arapahoe, Douglas, Elbert, Lincoln)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('395','1st (Gilpin, Jefferson)|2nd (Denver)|4th (El Paso, Teller)|10th (Pueblo)|11th (Chaffee, Custer, Fremont, Park)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|  |20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('396','20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('403','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('406','18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('414','17th (Adams, Broomfield)|19th (Weld)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('418','4th (El Paso, Teller)')
insert into @tblImport (memberNumber, JServed) values('421','8th (Jackson, Larimer)|19th (Weld)')
insert into @tblImport (memberNumber, JServed) values('424','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('429','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('430','17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('431','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('432','18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('433','2nd (Denver)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('438','1st (Gilpin, Jefferson)|2nd (Denver)|4th (El Paso, Teller)| 6, |17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|19th (Weld)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('439','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('441','8th (Jackson, Larimer)')
insert into @tblImport (memberNumber, JServed) values('442','1st (Gilpin, Jefferson)|2nd (Denver)|18th (Arapahoe, Douglas, Elbert, Lincoln)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('444','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('445','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)')
insert into @tblImport (memberNumber, JServed) values('450','1st (Gilpin, Jefferson)')
insert into @tblImport (memberNumber, JServed) values('451','5th (Clear Creek, Eagle, Lake, Summit)')
insert into @tblImport (memberNumber, JServed) values('453','Appellate')
insert into @tblImport (memberNumber, JServed) values('456','1st (Gilpin, Jefferson)|2nd (Denver)|18th (Arapahoe, Douglas, Elbert, Lincoln)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('462','Appellate')
insert into @tblImport (memberNumber, JServed) values('464','5th (Clear Creek, Eagle, Lake, Summit)|11th (Chaffee, Custer, Fremont, Park)')
insert into @tblImport (memberNumber, JServed) values('468','1st (Gilpin, Jefferson)|2nd (Denver)|5th (Clear Creek, Eagle, Lake, Summit)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('473','3rd (Huerfano, Las Animas)|10th (Pueblo)')
insert into @tblImport (memberNumber, JServed) values('475','1st (Gilpin, Jefferson)|2nd (Denver)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('476','4th (El Paso, Teller)|10th (Pueblo)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('482','18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('485','19th (Weld)')
insert into @tblImport (memberNumber, JServed) values('487','21st (Mesa)')
insert into @tblImport (memberNumber, JServed) values('489','11th (Chaffee, Custer, Fremont, Park)')
insert into @tblImport (memberNumber, JServed) values('490','1st (Gilpin, Jefferson)|2nd (Denver)|8th (Jackson, Larimer)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|19th (Weld)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('491','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('498','1st (Gilpin, Jefferson)')
insert into @tblImport (memberNumber, JServed) values('503','8th (Jackson, Larimer)|19th (Weld)')
insert into @tblImport (memberNumber, JServed) values('504','1st (Gilpin, Jefferson)|2nd (Denver)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('505','7th (Delta, Gunnison, Hinsdale, Montrose, Ouray, San Miguel)')
insert into @tblImport (memberNumber, JServed) values('506','18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('511','21st (Mesa)')
insert into @tblImport (memberNumber, JServed) values('512','5th (Clear Creek, Eagle, Lake, Summit)| 9')
insert into @tblImport (memberNumber, JServed) values('513','8th (Jackson, Larimer)|13th (Kit Carson, Logan, Morgan, Phillips, Sedgwick)')
insert into @tblImport (memberNumber, JServed) values('520','18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('531','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('532','1st (Gilpin, Jefferson)|8th (Jackson, Larimer)|17th (Adams, Broomfield)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('534','1st (Gilpin, Jefferson)|2nd (Denver)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('538','11th (Chaffee, Custer, Fremont, Park)|12th (Alamosa, Conejos, Costilla, Rio Grande, Saguache)')
insert into @tblImport (memberNumber, JServed) values('541','7th (Delta, Gunnison, Hinsdale, Montrose, Ouray, San Miguel)')
insert into @tblImport (memberNumber, JServed) values('542','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('544','8th (Jackson, Larimer)|19th (Weld)')
insert into @tblImport (memberNumber, JServed) values('546','17th (Adams, Broomfield)')
insert into @tblImport (memberNumber, JServed) values('548','1st (Gilpin, Jefferson)')
insert into @tblImport (memberNumber, JServed) values('549','1st (Gilpin, Jefferson)|2nd (Denver)')
insert into @tblImport (memberNumber, JServed) values('552','5th (Clear Creek, Eagle, Lake, Summit)|9th (Garfield, Pitkin, Rio Blanco)|21st (Mesa)')
insert into @tblImport (memberNumber, JServed) values('554','19th (Weld)')
insert into @tblImport (memberNumber, JServed) values('555','5th (Clear Creek, Eagle, Lake, Summit)')
insert into @tblImport (memberNumber, JServed) values('561','1st (Gilpin, Jefferson)|2nd (Denver)| 8, |17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|19th (Weld)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('562','20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('565','9th (Garfield, Pitkin, Rio Blanco)')
insert into @tblImport (memberNumber, JServed) values('566','2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('569','2nd (Denver)|17th (Adams, Broomfield)')
insert into @tblImport (memberNumber, JServed) values('572','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('573','4th (El Paso, Teller)|10th (Pueblo)')
insert into @tblImport (memberNumber, JServed) values('576','13th (Kit Carson, Logan, Morgan, Phillips, Sedgwick)|17th (Adams, Broomfield)')
insert into @tblImport (memberNumber, JServed) values('577','1st (Gilpin, Jefferson)|2nd (Denver)|8th (Jackson, Larimer)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('578','1st (Gilpin, Jefferson)')
insert into @tblImport (memberNumber, JServed) values('579','1st (Gilpin, Jefferson)|2nd (Denver)')
insert into @tblImport (memberNumber, JServed) values('582','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|19th (Weld)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('584','8th (Jackson, Larimer)')
insert into @tblImport (memberNumber, JServed) values('587','1st (Gilpin, Jefferson)|17th (Adams, Broomfield)|, |20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('590','8th (Jackson, Larimer)')
insert into @tblImport (memberNumber, JServed) values('591','2nd (Denver)')
insert into @tblImport (memberNumber, JServed) values('592','18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('598','18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('599','2nd (Denver)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('600','18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('604','2nd (Denver)')
insert into @tblImport (memberNumber, JServed) values('607','1st (Gilpin, Jefferson)|2nd (Denver)|8th (Jackson, Larimer)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|19th (Weld)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('608','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)')
insert into @tblImport (memberNumber, JServed) values('609','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('611','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('613','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|19th (Weld)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('614','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|19th (Weld)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('616','1st (Gilpin, Jefferson)|2nd (Denver)|4th (El Paso, Teller)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|19th (Weld)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('621','1st (Gilpin, Jefferson)|2nd (Denver)|4th (El Paso, Teller)|8th (Jackson, Larimer)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|19th (Weld)|20th (Boulder)| appellate')
insert into @tblImport (memberNumber, JServed) values('626','4th (El Paso, Teller)')
insert into @tblImport (memberNumber, JServed) values('633','20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('636','17th (Adams, Broomfield)')
insert into @tblImport (memberNumber, JServed) values('639','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('641','1st (Gilpin, Jefferson)|2nd (Denver)|4th (El Paso, Teller)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('642','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|19th (Weld)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('645','1st (Gilpin, Jefferson)| 2')
insert into @tblImport (memberNumber, JServed) values('648','5th (Clear Creek, Eagle, Lake, Summit)')
insert into @tblImport (memberNumber, JServed) values('649','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|19th (Weld)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('650','1st (Gilpin, Jefferson)|4th (El Paso, Teller)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('651','2nd (Denver)')
insert into @tblImport (memberNumber, JServed) values('652','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('655','1st (Gilpin, Jefferson)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('656','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|19th (Weld)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('658','1st (Gilpin, Jefferson)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('661','1st (Gilpin, Jefferson)|2nd (Denver)')
insert into @tblImport (memberNumber, JServed) values('662','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('665','1st (Gilpin, Jefferson)|2nd (Denver)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('669','2nd (Denver)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('672','8th (Jackson, Larimer)')
insert into @tblImport (memberNumber, JServed) values('674','20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('675','20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('676','18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('678','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('680','2nd (Denver)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('684','4th (El Paso, Teller)|10th (Pueblo)')
insert into @tblImport (memberNumber, JServed) values('685','2nd (Denver)|17th (Adams, Broomfield)')
insert into @tblImport (memberNumber, JServed) values('686','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('696','20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('705','1st (Gilpin, Jefferson)|2nd (Denver)|4th (El Paso, Teller)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('707','10th (Pueblo)')
insert into @tblImport (memberNumber, JServed) values('712','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|19th (Weld)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('716','1st (Gilpin, Jefferson)|2nd (Denver)|8th (Jackson, Larimer)|17th (Adams, Broomfield)|19th (Weld)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('723','8th (Jackson, Larimer)')
insert into @tblImport (memberNumber, JServed) values('725','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('726','Appellate')
insert into @tblImport (memberNumber, JServed) values('729','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('731','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('732','1st (Gilpin, Jefferson)|2nd (Denver)')
insert into @tblImport (memberNumber, JServed) values('733','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('735','6th (Archuleta, La Plata, San Juan)|22nd (Dolores, Montezuma)')
insert into @tblImport (memberNumber, JServed) values('741','8th (Jackson, Larimer)|13th (Kit Carson, Logan, Morgan, Phillips, Sedgwick)|14th (Grand, Moffat, Routt)|19th (Weld)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('745','2nd (Denver)|8th (Jackson, Larimer)|13th (Kit Carson, Logan, Morgan, Phillips, Sedgwick)|19th (Weld)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('746','4th (El Paso, Teller)')
insert into @tblImport (memberNumber, JServed) values('749','18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('752','18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('754','1st (Gilpin, Jefferson)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('757','20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('761','20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('769','1st (Gilpin, Jefferson)|2nd (Denver)|5th (Clear Creek, Eagle, Lake, Summit)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('770','2nd (Denver)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('772','17th (Adams, Broomfield)')
insert into @tblImport (memberNumber, JServed) values('777','1st (Gilpin, Jefferson)|2nd (Denver)|8th (Jackson, Larimer)|17th (Adams, Broomfield)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('779','18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('781','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('782','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('786','2nd (Denver)')
insert into @tblImport (memberNumber, JServed) values('787','8th (Jackson, Larimer)')
insert into @tblImport (memberNumber, JServed) values('788','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('790','2nd (Denver)|17th (Adams, Broomfield)')
insert into @tblImport (memberNumber, JServed) values('791','2nd (Denver)')
insert into @tblImport (memberNumber, JServed) values('792','3rd (Huerfano, Las Animas)|4th (El Paso, Teller)|10th (Pueblo)|11th (Chaffee, Custer, Fremont, Park)|16th (Bent, Crowley, Otero)')
insert into @tblImport (memberNumber, JServed) values('793','1st (Gilpin, Jefferson)| 2,|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('805','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('813','6th (Archuleta, La Plata, San Juan)|22nd (Dolores, Montezuma)')
insert into @tblImport (memberNumber, JServed) values('814','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)')
insert into @tblImport (memberNumber, JServed) values('815','7th (Delta, Gunnison, Hinsdale, Montrose, Ouray, San Miguel)|14th (Grand, Moffat, Routt)|9th (Garfield, Pitkin, Rio Blanco)|21st (Mesa)')
insert into @tblImport (memberNumber, JServed) values('819','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('820','2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('821','1st (Gilpin, Jefferson)| 2')
insert into @tblImport (memberNumber, JServed) values('824','2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('829','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|, |18th (Arapahoe, Douglas, Elbert, Lincoln)|, |19th (Weld)|, |20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('831','14th (Grand, Moffat, Routt)')
insert into @tblImport (memberNumber, JServed) values('834','2nd (Denver)')
insert into @tblImport (memberNumber, JServed) values('836','4th (El Paso, Teller)|7th (Delta, Gunnison, Hinsdale, Montrose, Ouray, San Miguel)|10th (Pueblo)|11th (Chaffee, Custer, Fremont, Park)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('837','1st (Gilpin, Jefferson)|8th (Jackson, Larimer)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)|19th (Weld)|, |20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('842','6th (Archuleta, La Plata, San Juan)')
insert into @tblImport (memberNumber, JServed) values('843','18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('844','4th (El Paso, Teller)')
insert into @tblImport (memberNumber, JServed) values('845','20th (Boulder)')
insert into @tblImport (memberNumber, JServed) values('848','6th (Archuleta, La Plata, San Juan)')
insert into @tblImport (memberNumber, JServed) values('850','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('853','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('857','14th (Grand, Moffat, Routt)')
insert into @tblImport (memberNumber, JServed) values('858','2nd (Denver)')
insert into @tblImport (memberNumber, JServed) values('859','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('860','1st (Gilpin, Jefferson)|2nd (Denver)|17th (Adams, Broomfield)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('861','2nd (Denver)|18th (Arapahoe, Douglas, Elbert, Lincoln)')
insert into @tblImport (memberNumber, JServed) values('870','12th (Alamosa, Conejos, Costilla, Rio Grande, Saguache)')
insert into @tblImport (memberNumber, JServed) values('1022','1st (Gilpin, Jefferson)|2nd (Denver)|18th (Arapahoe, Douglas, Elbert, Lincoln)')


IF OBJECT_ID('datatransfer.dbo.ccdbJServed') IS NOT NULL
	DROP TABLE datatransfer.dbo.ccdbJServed
IF OBJECT_ID('datatransfer.dbo.ccdbJServed2') IS NOT NULL
	DROP TABLE datatransfer.dbo.ccdbJServed2


select m.memberNumber, m.memberID, vcList.listitem
into datatransfer.dbo.ccdbJServed
from @tblImport as tbli
inner join ams_members m on m.memberNumber = tbli.MemberNumber and orgID = @orgID
cross apply dbo.fn_varcharListToTable(tbli.JServed,'|') as vclist

select data.memberID, mdcv.valueID
into datatransfer.dbo.ccdbJServed2
from datatransfer.dbo.ccdbJServed data
inner join ams_memberDataColumnValues mdcv on mdcv.columnID = @columnID and mdcv.columnValueString = data.listitem

-- Remove values that exist
delete data 
from datatransfer.dbo.ccdbJServed2 data
inner join ams_memberData md on data.memberid = md.memberid and data.valueid = md.valueid

INSERT INTO dbo.ams_memberData (memberid, valueID)
select memberid, valueid
from datatransfer.dbo.ccdbJServed2


IF OBJECT_ID('datatransfer.dbo.ccdbJServed') IS NOT NULL
	DROP TABLE datatransfer.dbo.ccdbJServed
IF OBJECT_ID('datatransfer.dbo.ccdbJServed2') IS NOT NULL
	DROP TABLE datatransfer.dbo.ccdbJServed2

declare @customFields TABLE (orgID int, fieldName varchar(200))
declare @conditionsToReprocess TABLE (orgID int, conditionList varchar(2000))

declare @thisOrgID int, @thisConditionList varchar(2000)



-- insert your orgID, fieldname combinations into this table var
insert into @customFields (orgID,fieldname) values (@orgID,'Jurisdictions Served')




-- DO NOT CHANGE BELOW

insert into @conditionsToReprocess (orgID, conditionList)
select cf.orgID, dbo.sortedIntList(vgc.conditionID)
from @customFields cf
inner join dbo.ams_memberDataColumns mdc
    on mdc.orgID = cf.orgID
    and mdc.columnName = cf.fieldname
inner join dbo.ams_virtualGroupConditions vgc
    on vgc.fieldCode = 'md_' + cast(mdc.columnID as varchar(10))
inner join dbo.ams_virtualGroupConditionTypes vgct
    on vgct.conditionTypeID = vgc.conditionTypeID
    and vgct.conditionType = 'GroupAssignment'
group by cf.orgID
order by cf.orgID


select @thisOrgID = min(orgID) from @conditionsToReprocess
while @thisOrgID is not null
begin
	select 
		@thisOrgID = orgID, 
		@thisConditionList = conditionList
	from @conditionsToReprocess
	where orgID = @thisOrgID

	
	exec platformQueue.dbo.queue_processMemberGroups_insert
	   @orgID=@thisOrgID, 
	   @memberIDList='', 
	   @conditionIDList=@thisConditionList, 
	   @runSchedule=2, 
	   @itemGroupUID=null

	select @thisOrgID = min(orgID) from @conditionsToReprocess where orgID > @thisOrgID
end
