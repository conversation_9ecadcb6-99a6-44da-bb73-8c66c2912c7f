use membercentral
GO
ALTER FUNCTION [dbo].[fn_ev_totalRegRateFeeAndPaid] (
	@registrantID int
)
RETURNS TABLE
AS
RETURN (

	select tsFull.cache_amountAfterAdjustment as totalRegFee, tsFull.cache_activePaymentAllocatedAmount as regFeePaid
	from dbo.tr_transactions as t
	cross apply dbo.fn_tr_transactionSalesWithDIT(t.transactionID) as tsFull
	where t.transactionID = dbo.fn_ev_getTransactionIDForRateAdjustment(@registrantID)

)
GO

ALTER FUNCTION [dbo].[fn_ev_totalRegCustomFeeAndPaid] (
	@registrantID int,
	@detailID int
)
RETURNS TABLE
AS
RETURN (

	select tsFull.cache_amountAfterAdjustment as totalRegFee, tsFull.cache_activePaymentAllocatedAmount as regFeePaid
	from dbo.tr_transactions as t
	cross apply dbo.fn_tr_transactionSalesWithDIT(t.transactionID) as tsFull
	where t.transactionID = dbo.fn_ev_getTransactionIDForCustomAdjustment(@registrantID,@detailID)

)
GO

ALTER FUNCTION [dbo].[fn_ev_totalRegFeeAndPaid] (
	@registrantID int
)
RETURNS TABLE
AS
RETURN (

	select sum(ts.cache_amountAfterAdjustment) as totalRegFee, sum(ts.cache_activePaymentAllocatedAmount) as regFeePaid
	from dbo.fn_ev_registrantTransactions(@registrantID) as rt
	inner join dbo.tr_transactionSales as ts on ts.transactionID = rt.transactionid

)
GO

DROP FUNCTION dbo.fn_ev_registrantTransactions
GO
CREATE FUNCTION [dbo].[fn_ev_registrantTransactions] (@registrantID int)
RETURNS @tblTransactions TABLE (
	transactionID int, 
	ownedByOrgID int, 
	recordedOnSiteID int, 
	statusID int, 
	detail varchar(max), 
	parentTransactionID int,
	amount money, 
	dateRecorded datetime, 
	transactionDate datetime, 
	assignedToMemberID int, 
	recordedByMemberID int,
	statsSessionID int, 
	typeID int, 
	accrualDate datetime, 
	debitGLAccountID int, 
	creditGLAccountID int
)
AS
BEGIN

	-- this should include all transactions (ignoring status)
	DECLARE @tblHold TABLE (transactionID int)

	-- rate sales
	insert into @tblHold
	select t.transactionID
	from dbo.ev_registrants as r
	inner join dbo.tr_applications as a on a.itemType = 'Rate' and a.ItemID = r.registrantID
	inner join dbo.tr_transactions as t on t.transactionID = a.transactionID 
	inner join dbo.tr_types as tt on tt.typeID = t.typeID and tt.type = 'Sale'
	where r.registrantID = @registrantID

	-- custom q sales
	insert into @tblHold
	select t.transactionID
	from dbo.ev_registrants as r
	inner join dbo.ev_registrantDetails as rd on rd.registrantID = r.registrantID
	inner join dbo.tr_applications as a on a.itemType = 'Custom' and a.ItemID = rd.detailID
	inner join dbo.tr_transactions as t on t.transactionID = a.transactionID 
	inner join dbo.tr_types as tt on tt.typeID = t.typeID and tt.type = 'Sale'
	where r.registrantID = @registrantID

	-- tax
	insert into @tblHold
	select distinct t.transactionID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'SalesTaxTrans'
	inner join @tblHold as rt on rt.transactionID = tr.appliedToTransactionID 

	-- adjustments
	insert into @tblHold
	select distinct t.transactionID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'AdjustTrans'
	inner join @tblHold as rt on rt.transactionID = tr.appliedToTransactionID 

	-- deferred
	insert into @tblHold
	select distinct t.transactionID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'DITSaleTrans'
	inner join @tblHold as rt on rt.transactionID = tr.appliedToTransactionID 

	-- allocations (allocSaleTrans)
	insert into @tblHold
	select distinct t.transactionID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'AllocSaleTrans'
	inner join @tblHold as rt on rt.transactionID = tr.appliedToTransactionID 

	-- writeoffs (writeOffSaleTrans)
	insert into @tblHold
	select distinct t.transactionID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'WriteOffSaleTrans'
	inner join @tblHold as rt on rt.transactionID = tr.appliedToTransactionID 

	-- payments (tied to allocations - AllocPayTrans)
	insert into @tblHold
	select distinct t.transactionID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.appliedToTransactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'AllocPayTrans'
	inner join @tblHold as rt on rt.transactionID = tr.transactionID 

	-- refunds tied to included payments
	insert into @tblHold
	select distinct t.transactionID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'RefundTrans'
	inner join @tblHold as rt on rt.transactionID = tr.appliedToTransactionID 


	insert into @tblTransactions
	select t.transactionID, t.ownedByOrgID, t.recordedOnSiteID, t.statusID, t.detail, t.parentTransactionID,
		t.amount, t.dateRecorded, t.transactionDate, t.assignedToMemberID, t.recordedByMemberID,
		t.statsSessionID, t.typeID, t.accrualDate, t.debitGLAccountID, t.creditGLAccountID
	from @tblHold as rt
	inner join dbo.tr_transactions as t on t.transactionid = rt.transactionid

	RETURN

END
GO


ALTER PROC [dbo].[ev_removeRegistrant]
@registrantID int,
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @ApplicationTypeID int, @minTID int, @adjtransactionID int, 
		@assignedToMemberID int, @invoiceProfileID int, @invoiceID int, @registrationID int,
		@GLAccountID int
	declare @invoiceNumber varchar(18)
	DECLARE @adjAmount money
	declare @nowdate datetime
	DECLARE @tblAdjust TABLE (transactionID int, amountToAdjust money, creditGLAccountID int)
	DECLARE @tblInvoices TABLE (invoiceID int, invoiceProfileID int)

	select @nowdate = getdate()
	select @ApplicationTypeID = dbo.fn_getApplicationTypeIDFromName('Events')
	select @assignedToMemberID = m.activeMemberID, @registrationID = r.registrationID
		from dbo.ev_registrants as r
		inner join dbo.ams_members as m on m.memberID = r.memberID
		where r.registrantID = @registrantID

	-- put all open invoices used for this registration into table since they were already created and can be used for adjustments
	insert into @tblInvoices (invoiceID, invoiceProfileID)
	select distinct i.invoiceID, i.invoiceProfileID
	from dbo.fn_ev_registrantTransactions(@registrantID) as rt
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = rt.transactionID
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	where i.statusID = 1

	-- update registrant status
	UPDATE dbo.ev_registrants
	SET [status] = 'D'
	WHERE registrantID = @registrantID
	
	UPDATE dbo.tr_applications
	SET [status] = 'D'
	WHERE itemID = @registrantID
	AND itemType = 'Rate'
	AND applicationTypeID = @ApplicationTypeID

	-- update registrant details status
	UPDATE dbo.ev_registrantDetails
	SET [status] = 'D'
	WHERE registrantID = @registrantID

	UPDATE dbo.tr_applications
	SET [status] = 'D'
	WHERE itemID in (select detailID from dbo.ev_registrantDetails WHERE registrantID = @registrantID)
	AND itemType = 'Custom'
	AND applicationTypeID = @ApplicationTypeID

	-- get all registrant-related sales transactions we need to adjust
	INSERT INTO @tblAdjust (transactionID, amountToAdjust, creditGLAccountID)
	select rt.transactionID, tsFull.cache_amountAfterAdjustment, t.creditGLAccountID
	from dbo.fn_ev_registrantTransactions(@registrantID) as rt
	inner join dbo.tr_transactions as t on t.transactionID = rt.transactionID
	cross apply dbo.fn_tr_transactionSalesWithDIT(t.transactionID) as tsFull
	where tsFull.cache_amountAfterAdjustment > 0
	and rt.typeID = 1

	-- if there are adjustments to make
	IF EXISTS (select transactionID from @tblAdjust) BEGIN
		SELECT @minTID = min(transactionID) from @tblAdjust
		WHILE @minTID IS NOT NULL BEGIN
			select @invoiceProfileID = null, @invoiceID = null, @adjAmount = null, @GLAccountID = null

			select @adjAmount = amountToAdjust*-1, @GLAccountID = creditGLAccountID from @tblAdjust where transactionID = @minTID
			select @invoiceProfileID = invoiceProfileID from dbo.tr_glAccounts where glAccountID = @GLAccountID
			select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID
			
			-- if necessary, create invoice assigned to registrant based on invoice profile
			IF @invoiceID is null BEGIN
				EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
					@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@nowdate, 
					@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT

				insert into @tblInvoices (invoiceID, invoiceProfileID)
				values (@invoiceID, @invoiceProfileID)
			END	

			EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@recordedOnSiteID,
				@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID,
				@status='Active', @amount=@adjAmount, @transactionDate=@nowdate,
				@saleTransactionID=@minTID, @invoiceID=@invoiceID, @xmlSchedule=null, 
				@transactionID=@adjtransactionID OUTPUT
			
			SELECT @minTID = min(transactionID) from @tblAdjust where transactionID > @minTID
		END
	END

	-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
	declare @itemGroupUID uniqueidentifier, @orgID int, @eventID int, @conditionIDList varchar(max)
	SELECT @orgID = orgID from dbo.ams_members where memberID = @assignedToMemberID
	SELECT @eventID = eventID from dbo.ev_registration where registrationID = @registrationID
	SELECT @conditionIDList = COALESCE(@conditionIDList + ',', '') + cast(c.conditionID as varchar(10)) 
		from dbo.ams_virtualGroupConditions as c
		where c.orgID = @orgID
		and c.fieldcode = 'e_' + cast(@eventID as varchar(10))
		group by c.conditionID
	IF @conditionIDList is not null
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@assignedToMemberID, @conditionIDList=@conditionIDList, @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[ev_exportRegistrantTransactions]
	@orgID int,
	@registrationID int,
	@filename varchar(400)
AS

-- status 2 (voided) do not appear here
-- status 3 (pending) do not appear here -- they are not accepted transactions
-- status 4 (voidedpending) do not appear here -- they are meant to be completely hidden
-- types 5 (allocations), 8 (voidOffsets), and 10 (dit) do not appear here

DECLARE @fullsql varchar(max)

SELECT @fullsql = '
	select 	
		REGISTRANT = case
			when r.status <> ''D'' then mr2.lastname + '', '' + mr2.firstname
			else mr2.lastname + '', '' + mr2.firstname + '' (removed)''
			end,
		rt.transactionDate as TRANSACTION_DATE,
		rt.accrualDate as RECOGNIZED_DATE,
		tt.type as TRANSACTION_TYPE,
		TRANSACTION_DETAIL = isnull(rt.detail,''''),
		ma2.lastname + '', '' + ma2.firstname + '' ('' + ma2.membernumber + '')'' as TRANSACTION_ASSIGNED_TO,
		m2.lastname + '', '' + m2.firstname + '' ('' + m2.membernumber + '')'' as TRANSACTION_RECORDED_BY,
		ACCOUNT = case
			when tt.typeID = 1 then rglcred.thePathExpanded
			when tt.typeID = 2 then rgldeb.thePathExpanded
			when tt.typeID = 3 and rgldeb.GLCode = ''ACCOUNTSRECEIVABLE'' then rglcred.thePathExpanded
			when tt.typeID = 3 and rglcred.GLCode = ''ACCOUNTSRECEIVABLE'' then rgldeb.thePathExpanded
			when tt.typeID = 4 then rglcred.thePathExpanded
			when tt.typeID = 6 and rglcred.GLCode = ''ACCOUNTSRECEIVABLE'' then rgldeb.thePathExpanded
			when tt.typeID = 6 and rgldeb.GLCode = ''DEPOSITS'' then rglcred.thePathExpanded
			when tt.typeID = 7 then rglcred.thePathExpanded
			else '''' end,
		ACCOUNT_CODE = case
			when tt.typeID = 1 then rglcred.accountCode
			when tt.typeID = 2 then rgldeb.accountCode
			when tt.typeID = 3 and rgldeb.GLCode = ''ACCOUNTSRECEIVABLE'' then rglcred.accountCode
			when tt.typeID = 3 and rglcred.GLCode = ''ACCOUNTSRECEIVABLE'' then rgldeb.accountCode
			when tt.typeID = 4 then rglcred.accountCode
			when tt.typeID = 6 and rglcred.GLCode = ''ACCOUNTSRECEIVABLE'' then rgldeb.accountCode
			when tt.typeID = 6 and rgldeb.GLCode = ''DEPOSITS'' then rglcred.accountCode
			when tt.typeID = 7 then rglcred.accountCode
			else '''' end,
		DEBIT = case
			when tt.typeID = 1 then null
			when tt.typeID = 2 then rt.amount
			when tt.typeID = 3 and rgldeb.GLCode = ''ACCOUNTSRECEIVABLE'' then null
			when tt.typeID = 3 and rglcred.GLCode = ''ACCOUNTSRECEIVABLE'' then rt.amount
			when tt.typeID = 4 then null
			when tt.typeID = 6 and rglcred.GLCode = ''ACCOUNTSRECEIVABLE'' then rt.amount
			when tt.typeID = 6 and rgldeb.GLCode = ''DEPOSITS'' then null
			when tt.typeID = 7 then null
			else '''' end,
		CREDIT = case
			when tt.typeID = 1 then rt.amount
			when tt.typeID = 2 then null
			when tt.typeID = 3 and rgldeb.GLCode = ''ACCOUNTSRECEIVABLE'' then rt.amount
			when tt.typeID = 3 and rglcred.GLCode = ''ACCOUNTSRECEIVABLE'' then null
			when tt.typeID = 4 then rt.amount
			when tt.typeID = 6 and rglcred.GLCode = ''ACCOUNTSRECEIVABLE'' then null
			when tt.typeID = 6 and rgldeb.GLCode = ''DEPOSITS'' then rt.amount
			when tt.typeID = 7 then rt.amount
			else '''' end
	from dbo.ev_registrants as r
	inner join dbo.ev_registration as evr on evr.registrationID = r.registrationID 
		and r.registrationID = ' + cast(@registrationID as varchar(10)) + '
	inner join dbo.ams_members as mr on mr.memberID = r.memberid
	inner join dbo.ams_members as mr2 on mr2.memberID = mr.activeMemberID
	cross apply dbo.fn_ev_registrantTransactions(r.registrantid) as rt
	inner join dbo.tr_types as tt on tt.typeID = rt.typeID
	inner join dbo.ams_members as ma on ma.memberID = rt.AssignedTomemberid
	inner join dbo.ams_members as ma2 on ma2.memberID = ma.activeMemberID
	inner join dbo.ams_members as m on m.memberID = rt.recordedByMemberID
	inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
	INNER JOIN dbo.fn_getRecursiveGLAccounts(' + cast(@orgid as varchar(10)) + ') as rgldeb on rgldeb.GLAccountID = rt.debitGlAccountID
	INNER JOIN dbo.fn_getRecursiveGLAccounts(' + cast(@orgid as varchar(10)) + ') as rglcred on rglcred.GLAccountID = rt.creditGlAccountID
	WHERE rt.ownedByOrgID = ' + cast(@orgid as varchar(10)) + '
	and rt.statusID = 1
	and rt.typeID not in (5,8,10)
	order by r.status, 1'

-- export
EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql

RETURN 0
GO

