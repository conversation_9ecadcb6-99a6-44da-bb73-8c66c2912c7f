
USE memberCentral
GO

declare @toolTypeID int, @resourceTypeID int

EXEC dbo.createAdminToolType 
	@toolType='MMRMemberMerged', 
	@toolCFC='Reports.members.MemberMerged', 
	@toolDesc='Member Merged Report', 
	@toolTypeID=@toolTypeID OUTPUT, 
	@resourceTypeID=@resourceTypeID OUTPUT

declare @membersNavigationID int

select 
	@membersNavigationID = n.navigationID
from 
	admin_navigation n
	inner join admin_navigation n2 on
		n2.navigationID = n.parentNavigationID
		and n2.navName = 'Reports'
		and n2.navAreaID = 1
where 
	n.navname = 'Members'
	and n.navAreaID = 2

declare @parentnavigationID int, @navigationID int, @resourceTypeFunctionID int

EXEC dbo.createAdminNavigation 
	@navName='Member Merged', 
	@navDesc='Member Merged Report', 
	@parentNavigationID=@membersNavigationID, 
	@navAreaID=3, 
	@cfcMethod=null, 
	@isHeader=0, 
	@showInNav=1, 
	@navigationID=@parentnavigationID OUTPUT

EXEC dbo.createAdminNavigation 
	@navName='Member Merged By Member', 
	@navDesc='List of member merges by member', 
	@parentNavigationID=@parentnavigationID, 
	@navAreaID=4, 
	@cfcMethod='showReport', 
	@isHeader=0, 
	@showInNav=1, 
	@navigationID=@navigationID OUTPUT

select 
	@resourceTypeFunctionID = f.resourceTypeFunctionID 
from 
	cms_siteResourceTypeFunctions f
	inner join dbo.cms_siteResourceTypes rt on
		rt.resourceTypeID = f.resourceTypeID
		and resourceType = 'Admin'
	inner join dbo.cms_siteResourceFunctions rf on
		rf.functionID = f.functionID
		and rf.functionName = 'View'
		and rf.displayName = 'View'

EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID=@resourceTypeFunctionID, @toolTypeID=@toolTypeID, @navigationID=@navigationID

declare @siteID int
select @siteID=min(siteID) from dbo.sites
while @siteID is not null begin
	exec dbo.createAdminSuite @siteID=@siteID
	select @siteID = min(siteID) from dbo.sites where siteID > @siteID
end