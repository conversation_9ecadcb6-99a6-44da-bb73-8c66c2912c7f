use membercentral
GO

ALTER PROC [dbo].[crd_updateOfferingType]
@offeringID int,
@ASTID int,
@creditValue decimal(6,2),
@updated bit OUTPUT

AS

set @updated = 0

-- if 0 and noon<PERSON> has signed up for it, delete it
IF @creditValue = 0 
BEGIN
	IF NOT EXISTS (
		select r.requestID
		from dbo.crd_requests as r
		inner join dbo.crd_offeringTypes as ot on ot.offeringTypeID = r.offeringTypeID
		where ot.offeringID = @offeringID
		and ot.ASTID = @ASTID
	) BEGIN
		DELETE FROM dbo.crd_offeringTypes
		WHERE offeringID = @offeringID
		AND ASTID = @ASTID

		set @updated = 1
	END
END

ELSE BEGIN
	IF EXISTS (select offeringTypeID from dbo.crd_offeringTypes where offeringID = @offeringID and ASTID = @ASTID)
		UPDATE dbo.crd_offeringTypes
		SET creditValue = @creditValue
		WHERE offeringID = @offeringID
		AND ASTID = @ASTID
	ELSE
		INSERT INTO dbo.crd_offeringTypes (offeringID, ASTID, creditValue)
		VALUES (@offeringID, @ASTID, @creditValue)

	set @updated = 1
END

RETURN 0
GO

ALTER PROC [dbo].[ev_importEventFromQueue]
@itemUID uniqueidentifier

AS

SET NOCOUNT ON

declare @recordedByMemberID int, @siteID int, @rowID int, @orgID int, @timeZoneID int
declare @ovAction char(1), @MCEventID int, @MCCalendarID int, @EventAllDay bit, @EventCode varchar(15), @EventHidden bit, 
	@ContactInclude bit, @LocationInclude bit, @CancellationInclude bit, @TravelInclude bit, @EventStart datetime, 
	@EventEnd datetime,	@eventContentID int, @EventTitle varchar(200), @EventDescription varchar(max), @MCCategoryID int,
	@origCalendarID int, @InternalNotes varchar(max), @RegistrationReplyEmail varchar(200), @MCRegistrationID int,
	@DisplayCredits bit, @expirationContentID int, @ContactTitle varchar(200), @Contact varchar(max), @contentTitle varchar(200), 
	@rawContent varchar(max), @LocationTitle varchar(200), @Location varchar(max), @CancellationTitle varchar(200), 
	@Cancellation varchar(max), @TravelTitle varchar(200), @Travel varchar(max), @InformationTitle varchar(200), 
	@Information varchar(max), @locationContentID int, @cancellationPolicyContentID int, @travelContentID int,
	@informationContentID int, @subEventID int, @existingEvent bit, @ASID int, @crdAuthorityCode varchar(20),
	@crdApproval varchar(50), @crdStatus varchar(20), @crdOfferedID int, @crdStatusID int, @ASTID int, @crdColName sysname,
	@crdValue decimal(6,2), @origCategoryID int, @contactContentID int, @MCParentEventID int, @ParentEventCode varchar(15),
	@crdupdated bit
declare @tblPossibleCredits TABLE (ASID int, authorityID int, authorityCode varchar(20))
declare @tblPossibleCreditCols TABLE (ASID int, column_name sysname, ASTID int)

-- update status and get event info
BEGIN TRY
	EXEC platformQueue.dbo.queue_setStatus @queueType='importEvents', @itemUID=@itemUID, @queueStatus='processingEvent'

	select top 1 @recordedByMemberID=recordedByMemberID, @siteID=siteID, @rowID=dataKey
	from platformQueue.dbo.tblQueueItemData
	where itemUID = @itemUID

	select @orgID=orgID, @timeZoneID=defaultTimeZoneID from dbo.sites where siteID=@siteID

	IF OBJECT_ID('tempdb..#tmpEVQueueData') IS NOT NULL 
		DROP TABLE #tmpEVQueueData		
	select dc.columnname, qid.columnValueString, qid.columnValueDecimal2, qid.columnValueInteger, qid.columnvalueDate, 
		qid.columnValueBit, qid.columnValueXML, qid.columnValueText
	into #tmpEVQueueData
	from platformQueue.dbo.tblQueueItemData as qid
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
	where qid.itemUID = @itemUID

END TRY
BEGIN CATCH
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH


-- setup transaction to import event
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	select @ovAction = columnValueString from #tmpEVQueueData where columnname = 'ovAction'
	select @MCEventID = columnValueInteger from #tmpEVQueueData where columnname = 'MCEventID'
	select @MCRegistrationID = columnValueInteger from #tmpEVQueueData where columnname = 'MCRegistrationID'
	select @EventStart = columnValueDate from #tmpEVQueueData where columnname = 'EventStart'
	select @EventEnd = columnValueDate from #tmpEVQueueData where columnname = 'EventEnd'

	IF @MCEventID is not null
		set @existingEvent = 1
	ELSE
		set @existingEvent = 0

	-- if we are updating event with overwrite setting
	IF @existingEvent = 1 and @ovAction = 'o' BEGIN
		select @MCCalendarID = columnValueInteger from #tmpEVQueueData where columnname = 'MCCalendarID'
		select @MCCategoryID = columnValueInteger from #tmpEVQueueData where columnname = 'MCCategoryID'
		select @origCalendarID = calendarID from dbo.ev_calendarEvents where sourceEventID = @MCEventID and calendarID = sourceCalendarID
		select @origCategoryID = categoryID from dbo.ev_eventCategories where eventID = @MCEventID

		-- move to diff calendar if necessary (this also moves categories)
		IF (@MCCalendarID != @origCalendarID) BEGIN
			EXEC dbo.ev_moveCalendarEvent @eventID=@MCEventID, @fromCalendarID=@origCalendarID, @fromCategoryID=@origCategoryID, @toCalendarID=@MCCalendarID, @toCategoryID=@MCCategoryID
		END

		-- move category if necessary
		IF (@MCCalendarID = @origCalendarID AND @MCCategoryID != @origCategoryID) BEGIN
			update dbo.ev_eventCategories
			set categoryID = @MCCategoryID
			where eventID = @MCEventID
			and categoryID = @origCategoryID
		END

		-- update changed fields
		select @EventAllDay = columnValueBit from #tmpEVQueueData where columnname = 'EventAllDay'
		select @EventHidden = columnValueBit from #tmpEVQueueData where columnname = 'EventHidden'
		select @ContactInclude = columnValueBit from #tmpEVQueueData where columnname = 'ContactInclude'
		select @LocationInclude = columnValueBit from #tmpEVQueueData where columnname = 'LocationInclude'
		select @CancellationInclude = columnValueBit from #tmpEVQueueData where columnname = 'CancellationInclude'
		select @TravelInclude = columnValueBit from #tmpEVQueueData where columnname = 'TravelInclude'
		select @InternalNotes = columnValueText from #tmpEVQueueData where columnname = 'InternalNotes'

		--determine if times need to be forced to full day, if allDayEvent
		select @EventAllDay = isnull(@EventAllDay,isAllDayEvent)
		from dbo.ev_events
		where eventID = @MCEventID

		if (@EventAllDay = 1) BEGIN
			set @EventStart = Convert(DateTime, DATEDIFF(DAY, 0, @EventStart))
			set @EventEnd = dateadd(ms,-3,Convert(DateTime, DATEDIFF(DAY, -1, @EventEnd)))
		END

		update dbo.ev_events
		set isAllDayEvent = @EventAllDay,
			hiddenFromCalendar = isnull(@EventHidden,hiddenFromCalendar),
			emailContactContent = isnull(@ContactInclude,emailContactContent),
			emailLocationContent = isnull(@LocationInclude,emailLocationContent),
			emailCancelContent = isnull(@CancellationInclude,emailCancelContent),
			emailTravelContent = isnull(@TravelInclude,emailTravelContent),
			internalNotes = isnull(@InternalNotes,internalNotes)
		where eventID = @MCEventID

		-- update event times
		delete from dbo.ev_times where eventID = @MCEventID
		EXEC dbo.ev_createTime @eventID=@MCEventID, @timeZoneID=@timeZoneID, @startTime=@EventStart, @endTime=@EventEnd

		-- update title/description
		select @EventTitle = columnValueString from #tmpEVQueueData where columnname = 'EventTitle'
		select @EventDescription = columnValueText from #tmpEVQueueData where columnname = 'EventDescription'

		select @eventContentID = eventContentID FROM dbo.ev_events WHERE eventID=@MCEventID
		select @contentTitle=contentTitle, @rawContent=rawContent from dbo.fn_getContent(@eventContentID,1)
		set @EventDescription = isnull(@EventDescription,@rawContent)
		EXEC dbo.cms_updateContent @contentID=@eventContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@EventTitle, 
			@contentDesc='', @rawcontent=@EventDescription

		-- update other content objects
		select @ContactTitle = columnValueString from #tmpEVQueueData where columnname = 'ContactTitle'
		select @Contact = columnValueText from #tmpEVQueueData where columnname = 'Contact'
		select @LocationTitle = columnValueString from #tmpEVQueueData where columnname = 'LocationTitle'
		select @Location = columnValueText from #tmpEVQueueData where columnname = 'Location'
		select @CancellationTitle = columnValueString from #tmpEVQueueData where columnname = 'CancellationTitle'
		select @Cancellation = columnValueText from #tmpEVQueueData where columnname = 'Cancellation'
		select @TravelTitle = columnValueString from #tmpEVQueueData where columnname = 'TravelTitle'
		select @Travel = columnValueText from #tmpEVQueueData where columnname = 'Travel'
		select @InformationTitle = columnValueString from #tmpEVQueueData where columnname = 'InformationTitle'
		select @Information = columnValueText from #tmpEVQueueData where columnname = 'Information'

		select @contactContentID = contactContentID FROM dbo.ev_events WHERE eventID=@MCEventID
		select @contentTitle=null, @rawContent=null
		select @contentTitle=contentTitle, @rawContent=rawContent from dbo.fn_getContent(@contactContentID,1)
		set @ContactTitle = isnull(@ContactTitle,@contentTitle)
		set @Contact = isnull(@Contact,@rawContent)
		EXEC dbo.cms_updateContent @contentID=@contactContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@ContactTitle, 
			@contentDesc='', @rawcontent=@Contact

		select @locationContentID = locationContentID FROM dbo.ev_events WHERE eventID=@MCEventID
		select @contentTitle=null, @rawContent=null
		select @contentTitle=contentTitle, @rawContent=rawContent from dbo.fn_getContent(@locationContentID,1)
		set @LocationTitle = isnull(@LocationTitle,@contentTitle)
		set @Location = isnull(@Location,@rawContent)
		EXEC dbo.cms_updateContent @contentID=@locationContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@LocationTitle, 
			@contentDesc='', @rawcontent=@Location

		select @cancellationPolicyContentID = cancellationPolicyContentID FROM dbo.ev_events WHERE eventID=@MCEventID
		select @contentTitle=null, @rawContent=null
		select @contentTitle=contentTitle, @rawContent=rawContent from dbo.fn_getContent(@cancellationPolicyContentID,1)
		set @CancellationTitle = isnull(@CancellationTitle,@contentTitle)
		set @Cancellation = isnull(@Cancellation,@rawContent)
		EXEC dbo.cms_updateContent @contentID=@cancellationPolicyContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@CancellationTitle, 
			@contentDesc='', @rawcontent=@Cancellation

		select @travelContentID = travelContentID FROM dbo.ev_events WHERE eventID=@MCEventID
		select @contentTitle=null, @rawContent=null
		select @contentTitle=contentTitle, @rawContent=rawContent from dbo.fn_getContent(@travelContentID,1)
		set @TravelTitle = isnull(@TravelTitle,@contentTitle)
		set @Travel = isnull(@Travel,@rawContent)
		EXEC dbo.cms_updateContent @contentID=@travelContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@TravelTitle, 
			@contentDesc='', @rawcontent=@Travel

		select @informationContentID = informationContentID FROM dbo.ev_events WHERE eventID=@MCEventID
		select @contentTitle=null, @rawContent=null
		select @contentTitle=contentTitle, @rawContent=rawContent from dbo.fn_getContent(@informationContentID,1)
		set @InformationTitle = isnull(@InformationTitle,@contentTitle)
		set @Information = isnull(@Information,@rawContent)
		EXEC dbo.cms_updateContent @contentID=@informationContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@InformationTitle, 
			@contentDesc='', @rawcontent=@Information
	END

	-- if we are adding new event
	IF @existingEvent = 0 BEGIN
		select @MCCalendarID = columnValueInteger from #tmpEVQueueData where columnname = 'MCCalendarID'
		select @MCCategoryID = columnValueInteger from #tmpEVQueueData where columnname = 'MCCategoryID'
		select @EventAllDay = isnull(columnValueBit,0) from #tmpEVQueueData where columnname = 'EventAllDay'
		select @EventCode = columnValueString from #tmpEVQueueData where columnname = 'EventCode'
		select @EventHidden = isnull(columnValueBit,0) from #tmpEVQueueData where columnname = 'EventHidden'
		select @ContactInclude = isnull(columnValueBit,0) from #tmpEVQueueData where columnname = 'ContactInclude'
		select @LocationInclude = isnull(columnValueBit,0) from #tmpEVQueueData where columnname = 'LocationInclude'
		select @CancellationInclude = isnull(columnValueBit,0) from #tmpEVQueueData where columnname = 'CancellationInclude'
		select @TravelInclude = isnull(columnValueBit,0) from #tmpEVQueueData where columnname = 'TravelInclude'
		select @EventTitle = columnValueString from #tmpEVQueueData where columnname = 'EventTitle'
		select @EventDescription = isnull(columnValueText,'') from #tmpEVQueueData where columnname = 'EventDescription'
		select @InternalNotes = isnull(columnValueText,'') from #tmpEVQueueData where columnname = 'InternalNotes'
		select @ContactTitle = isnull(columnValueString,'') from #tmpEVQueueData where columnname = 'ContactTitle'
		select @Contact = isnull(columnValueText,'') from #tmpEVQueueData where columnname = 'Contact'
		select @LocationTitle = isnull(columnValueString,'') from #tmpEVQueueData where columnname = 'LocationTitle'
		select @Location = isnull(columnValueText,'') from #tmpEVQueueData where columnname = 'Location'
		select @CancellationTitle = isnull(columnValueString,'') from #tmpEVQueueData where columnname = 'CancellationTitle'
		select @Cancellation = isnull(columnValueText,'') from #tmpEVQueueData where columnname = 'Cancellation'
		select @TravelTitle = isnull(columnValueString,'') from #tmpEVQueueData where columnname = 'TravelTitle'
		select @Travel = isnull(columnValueText,'') from #tmpEVQueueData where columnname = 'Travel'
		select @InformationTitle = isnull(columnValueString,'') from #tmpEVQueueData where columnname = 'InformationTitle'
		select @Information = isnull(columnValueText,'') from #tmpEVQueueData where columnname = 'Information'

		--determine if times need to be forced to full day, if allDayEvent
		if (@EventAllDay = 1) BEGIN
			set @EventStart = Convert(DateTime, DATEDIFF(DAY, 0, @EventStart))
			set @EventEnd = dateadd(ms,-3,Convert(DateTime, DATEDIFF(DAY, -1, @EventEnd)))
		END

		EXEC dbo.ev_createEvent @siteID=@siteID, @calendarid=@MCCalendarID, @eventTypeID=1, @enteredByMemberID=@recordedByMemberID, 
			@lockTimeZoneID=null, @isAllDayEvent=@EventAllDay, @altRegistrationURL=null, @status='A', @reportCode=@EventCode, 
			@hiddenFromCalendar=@EventHidden, @emailContactContent=@ContactInclude, @emailLocationContent=@LocationInclude, 
			@emailCancelContent=@CancellationInclude, @emailTravelContent=@TravelInclude, @eventID=@MCEventID OUTPUT
		EXEC dbo.ev_createTime @eventID=@MCEventID, @timeZoneID=@timeZoneID, @startTime=@EventStart, @endTime=@EventEnd

		IF len(@InternalNotes) > 0
			update dbo.ev_events
			set internalNotes = @InternalNotes
			where eventID = @MCEventID

		INSERT INTO dbo.ev_eventCategories (eventID, categoryID)
		VALUES (@MCEventID, @MCCategoryID)

		select @eventContentID = eventContentID FROM dbo.ev_events WHERE eventID=@MCEventID
		EXEC dbo.cms_updateContent @contentID=@eventContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@EventTitle, 
			@contentDesc='', @rawcontent=@EventDescription

		IF LEN(@ContactTitle) > 0 or LEN(@Contact) > 0 BEGIN
			select @contactContentID = contactContentID FROM dbo.ev_events WHERE eventID=@MCEventID
			EXEC dbo.cms_updateContent @contentID=@contactContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@ContactTitle, 
				@contentDesc='', @rawcontent=@Contact
		END

		IF LEN(@LocationTitle) > 0 or LEN(@Location) > 0 BEGIN
			select @locationContentID = locationContentID FROM dbo.ev_events WHERE eventID=@MCEventID
			EXEC dbo.cms_updateContent @contentID=@locationContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@LocationTitle, 
				@contentDesc='', @rawcontent=@Location
		END

		IF LEN(@CancellationTitle) > 0 or LEN(@Cancellation) > 0 BEGIN
			select @cancellationPolicyContentID = cancellationPolicyContentID FROM dbo.ev_events WHERE eventID=@MCEventID
			EXEC dbo.cms_updateContent @contentID=@cancellationPolicyContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@CancellationTitle, 
				@contentDesc='', @rawcontent=@Cancellation
		END

		IF LEN(@TravelTitle) > 0 or LEN(@Travel) > 0 BEGIN
			select @travelContentID = travelContentID FROM dbo.ev_events WHERE eventID=@MCEventID
			EXEC dbo.cms_updateContent @contentID=@travelContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@TravelTitle, 
				@contentDesc='', @rawcontent=@Travel
		END

		IF LEN(@InformationTitle) > 0 or LEN(@Information) > 0 BEGIN
			select @informationContentID = informationContentID FROM dbo.ev_events WHERE eventID=@MCEventID
			EXEC dbo.cms_updateContent @contentID=@informationContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@InformationTitle, 
				@contentDesc='', @rawcontent=@Information
		END
	END 



	-- if we are updating registration with overwrite setting
	IF @existingEvent = 1 and @MCRegistrationID is not null and @ovAction = 'o' BEGIN
		select @RegistrationReplyEmail = columnValueString from #tmpEVQueueData where columnname = 'RegistrationReplyEmail'
		select @DisplayCredits = columnValueBit from #tmpEVQueueData where columnname = 'DisplayCredits'

		update dbo.ev_registration
		set replyToEmail = @RegistrationReplyEmail,
			showCredit = isnull(@DisplayCredits,showCredit)
		where registrationID = @MCRegistrationID
	END

	-- if event does not have registration
	IF @MCRegistrationID is null BEGIN
		select @RegistrationReplyEmail = columnValueString from #tmpEVQueueData where columnname = 'RegistrationReplyEmail'
		select @DisplayCredits = columnValueBit from #tmpEVQueueData where columnname = 'DisplayCredits'

		-- it could have rsvp or alt reg, so we need to switch it
		IF EXISTS(select registrationID from dbo.ev_registration where eventID = @MCEventID and status = 'A')
			UPDATE dbo.ev_registration
			SET status = 'D'
			WHERE eventID = @MCEventID 
			and status = 'A'

		UPDATE dbo.ev_events
		SET altRegistrationURL = NULL
		WHERE eventID = @MCEventID
		AND altRegistrationURL is not null

		EXEC dbo.ev_createRegistration @eventID=@MCEventID, @registrationTypeID=1, @startDate=@EventStart, @endDate=@EventEnd, 
			@registrantCap=null, @replyToEmail=@RegistrationReplyEmail, @notifyEmail='', @isPriceBasedOnActual=1, @bulkCountByRate=0, 
			@registrationID=@MCRegistrationID OUTPUT

		update dbo.ev_registration
		set showCredit = isnull(@DisplayCredits,1)
		where registrationID = @MCRegistrationID

		select @expirationContentID = expirationContentID FROM dbo.ev_registration WHERE registrationID = @MCRegistrationID
		EXEC dbo.cms_updateContent @contentID=@expirationContentID, @languageID=1, @isSSL=0, @isHTML=1, 
			@contentTitle='Expiration Message', @contentDesc='', @rawcontent='Registration for this event has closed.'
	END


	-- handle sub events
	IF @existingEvent = 0 OR (@existingEvent = 1 and @ovAction = 'o') BEGIN
		select @MCParentEventID = columnValueInteger from #tmpEVQueueData where columnname = 'MCParentEventID'
		select @ParentEventCode = nullIf(columnValueString,'') from #tmpEVQueueData where columnname = 'ParentEventCode'

		-- if @MCParentEventID is null but @ParentEventCode is not null, this event should be a child of a recently added event
		IF @MCParentEventID is null and @ParentEventCode is not null BEGIN
			select @MCCalendarID = columnValueInteger from #tmpEVQueueData where columnname = 'MCCalendarID'

			select @MCParentEventID = e.eventID
			from dbo.ev_events as e
			inner join membercentral.dbo.ev_calendarEvents as ce on ce.sourceEventID = e.eventID
				and ce.calendarID = @MCCalendarID
				and ce.calendarID = ce.sourceCalendarID
			where e.reportCode = @ParentEventCode
			and e.status = 'A'
			and e.siteID = @siteID
		END

		-- if @MCParentEventID is not null, this event should be a child of that event	
		IF @MCParentEventID is not null BEGIN
			select @subEventID = subEventID from dbo.ev_subEvents where parentEventID=@MCParentEventID and eventID=@MCEventID
			IF @subEventID is null 
				insert into dbo.ev_subEvents (parentEventID, eventID)
				values (@MCParentEventID, @MCEventID)
		END
	END


	-- credits
	IF @existingEvent = 0 OR (@existingEvent = 1 and @ovAction = 'o') BEGIN
		insert into @tblPossibleCredits (ASID, authorityID, authorityCode)
		select distinct crdAS.ASID, crdA.authorityID, crdA.authorityCode
		from dbo.crd_sponsors as crdS
		inner join dbo.crd_authoritySponsors as crdAS on crdAS.sponsorID = crdS.sponsorID
		inner join dbo.crd_authorities as crdA on crdA.authorityID = crdAS.authorityID
		where crdS.orgID = @orgID

		insert into @tblPossibleCreditCols (ASID, column_name, ASTID)
		select crdAS.ASID, crdAS.authorityCode + '_' + crdAT.typeCode, crdAST.ASTID 
		from @tblPossibleCredits as crdAS
		inner join dbo.crd_authorityTypes as crdAT on crdAT.authorityID = crdAS.authorityID
		inner join dbo.crd_authoritySponsorTypes as crdAST on crdAST.ASID = crdAS.ASID and crdAST.typeID = crdAT.typeID

		select @ASID = min(ASID) from @tblPossibleCredits
		while @ASID is not null begin
			select @crdAuthorityCode=null, @crdApproval=null, @crdStatus=null, @crdOfferedID=null, @crdStatusID=null, @ASTID=null

			select @crdAuthorityCode = authorityCode from @tblPossibleCredits where ASID = @ASID
			select @crdApproval = columnValueString from #tmpEVQueueData where columnname = @crdAuthorityCode + '_approval'
			select @crdStatus = columnValueString from #tmpEVQueueData where columnname = @crdAuthorityCode + '_status'

			IF @crdApproval is not null and @crdStatus is not null BEGIN
				select @crdOfferedID = offeringID from dbo.crd_offerings where ASID = @ASID and eventID = @MCEventID		
				IF @crdOfferedID is null
					EXEC dbo.crd_addOffering @applicationType='Events', @itemID=@MCEventID, @ASID=@ASID, @offeredID=@crdOfferedID OUTPUT

				select @crdStatusID = statusID from dbo.crd_statuses where [status] = @crdStatus

				UPDATE dbo.crd_offerings
				SET statusID = @crdStatusID, approvalNum = @crdApproval
				WHERE offeringID = @crdOfferedID

				select @ASTID = min(ASTID) from @tblPossibleCreditCols where ASID = @ASID
				while @ASTID is not null begin
					select @crdColName=null, @crdValue=null

					select @crdColName = column_name from @tblPossibleCreditCols where ASTID = @ASTID
					select @crdValue = columnValueDecimal2 from #tmpEVQueueData where columnname = @crdColName

					IF @crdValue is not null
						EXEC dbo.crd_updateOfferingType @offeringID=@crdOfferedID, @ASTID=@ASTID, @creditValue=@crdValue, @updated=@crdupdated OUTPUT

					select @ASTID = min(ASTID) from @tblPossibleCreditCols where ASID = @ASID and ASTID > @ASTID
				end
			END

			select @ASID = min(ASID) from @tblPossibleCredits where ASID > @ASID
		end

	END


	IF @TranCounter = 0
		COMMIT TRAN;
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH


-- update status
BEGIN TRY
	EXEC platformQueue.dbo.queue_setStatus @queueType='importEvents', @itemUID=@itemUID, @queueStatus='readyToNotify'

	IF OBJECT_ID('tempdb..#tmpEVQueueData') IS NOT NULL 
		DROP TABLE #tmpEVQueueData		
END TRY
BEGIN CATCH
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

