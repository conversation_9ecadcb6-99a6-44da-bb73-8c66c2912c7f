declare
	@orgID int,
	@lawFirmRecordTypeID int,
	@vendorRecordTypeID int,
	@individualRecordTypeID int,
	@stateAssociationRecordTypeID int,
	@businessRecordTypeID int,
	@firmMemberRelationshipTypeID int,
	@firmPartnerRelationshipTypeID int,
	@repRelationshipTypeID int,
	@mainContactRelationshipTypeID int,
	@associationContactRelationshipTypeID int,
	@businessContactRelationshipTypeID int,
	@trashID int

set @orgID = 108

-- Create Record Types
exec dbo.ams_createRecordType
	@orgID=@orgID,
	@recordTypeCode = 'LawFirm',
	@recordTypeName = 'Law Firm', 
	@recordTypeDesc = 'Law Firm', 
	@isPerson = 0,
	@isOrganization = 1,
	@recordTypeID = @lawFirmRecordTypeID OUTPUT

exec dbo.ams_createRecordType
	@orgID=@orgID,
	@recordTypeCode = 'Vendor',
	@recordTypeName = 'Vendor', 
	@recordTypeDesc = 'Vendor',
	@isPerson = 0,
	@isOrganization = 1,
	@recordTypeID = @vendorRecordTypeID OUTPUT

exec dbo.ams_createRecordType
	@orgID=@orgID,
	@recordTypeCode = 'Individual',
	@recordTypeName = 'Individual', 
	@recordTypeDesc = 'Individual',
	@isPerson = 1,
	@isOrganization = 0,
	@recordTypeID = @individualRecordTypeID OUTPUT

exec dbo.ams_createRecordType
	@orgID=@orgID,
	@recordTypeCode = 'State Association',
	@recordTypeName = 'State Association', 
	@recordTypeDesc = 'State Association',
	@isPerson = 0,
	@isOrganization = 1,
	@recordTypeID = @stateAssociationRecordTypeID OUTPUT

exec dbo.ams_createRecordType
	@orgID=@orgID,
	@recordTypeCode = 'Business',
	@recordTypeName = 'Business', 
	@recordTypeDesc = 'Business',
	@isPerson = 0,
	@isOrganization = 1,
	@recordTypeID = @businessRecordTypeID OUTPUT

-- Create Record Relationship Type
exec dbo.ams_createRecordRelationShipType
	@orgID=@orgID,
	@relationshipTypeCode='FirmMember',
	@relationshipTypeName='Firm Member',
	@relationshipTypeID=@firmMemberRelationshipTypeID OUTPUT

exec dbo.ams_createRecordRelationShipType
	@orgID=@orgID,
	@relationshipTypeCode='FirmPartner',
	@relationshipTypeName='Firm Partner',
	@relationshipTypeID=@firmPartnerRelationshipTypeID OUTPUT

exec dbo.ams_createRecordRelationShipType
	@orgID=@orgID,
	@relationshipTypeCode='Representative',
	@relationshipTypeName='Representative',
	@relationshipTypeID=@repRelationshipTypeID OUTPUT

exec dbo.ams_createRecordRelationShipType
	@orgID=@orgID,
	@relationshipTypeCode='MainContact',
	@relationshipTypeName='Main Contact',
	@relationshipTypeID=@mainContactRelationshipTypeID OUTPUT

exec dbo.ams_createRecordRelationShipType
	@orgID=@orgID,
	@relationshipTypeCode='AssociationContact',
	@relationshipTypeName='Association Contact',
	@relationshipTypeID=@associationContactRelationshipTypeID OUTPUT

exec dbo.ams_createRecordRelationShipType
	@orgID=@orgID,
	@relationshipTypeCode='BusinessContact',
	@relationshipTypeName='Business Contact',
	@relationshipTypeID=@businessContactRelationshipTypeID OUTPUT

-- Create Record Types Relationship Type for Law Firm --> Firm Member, Firm Partner
exec dbo.ams_createRecordTypesRelationshipTypes
	@relationshipTypeID=@firmMemberRelationshipTypeID,
	@masterRecordTypeID=@lawFirmRecordTypeID,
	@childRecordTypeID=@individualRecordTypeID,
	@isActive=1,
	@recordTypeRelationshipTypeID = @trashID OUTPUT

exec dbo.ams_createRecordTypesRelationshipTypes
	@relationshipTypeID=@firmPartnerRelationshipTypeID,
	@masterRecordTypeID=@lawFirmRecordTypeID,
	@childRecordTypeID=@individualRecordTypeID,
	@isActive=1,
	@recordTypeRelationshipTypeID = @trashID OUTPUT

-- Create Record Types Relationship Type for Vendor --> Representative, Main Contact
exec dbo.ams_createRecordTypesRelationshipTypes
	@relationshipTypeID=@repRelationshipTypeID,
	@masterRecordTypeID=@vendorRecordTypeID,
	@childRecordTypeID=@individualRecordTypeID,
	@isActive=1,
	@recordTypeRelationshipTypeID = @trashID OUTPUT

exec dbo.ams_createRecordTypesRelationshipTypes
	@relationshipTypeID=@mainContactRelationshipTypeID,
	@masterRecordTypeID=@vendorRecordTypeID,
	@childRecordTypeID=@individualRecordTypeID,
	@isActive=1,
	@recordTypeRelationshipTypeID = @trashID OUTPUT

-- Create Record Types Relationship Type for State Association --> Association Contact
exec dbo.ams_createRecordTypesRelationshipTypes
	@relationshipTypeID=@associationContactRelationshipTypeID,
	@masterRecordTypeID=@stateAssociationRecordTypeID,
	@childRecordTypeID=@individualRecordTypeID,
	@isActive=1,
	@recordTypeRelationshipTypeID = @trashID OUTPUT

-- Create Record Types Relationship Type for Business --> Business Contact
exec dbo.ams_createRecordTypesRelationshipTypes
	@relationshipTypeID=@businessContactRelationshipTypeID,
	@masterRecordTypeID=@businessRecordTypeID,
	@childRecordTypeID=@individualRecordTypeID,
	@isActive=1,
	@recordTypeRelationshipTypeID = @trashID OUTPUT
GO
