<cfcomponent output="false" cache="true">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">

		<cfset local.processResult = resetReportReadOnlyMode()>
		
		<cfif NOT local.processResult.success>
			<cfthrow message="Error running resetReportReadOnlyMode()">
		<cfelse>
			<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
			<cfset arguments.strTask.scheduledTasksUtils.addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.processResult.itemCount)>
		</cfif>
	</cffunction>
	
	<cffunction name="resetReportReadOnlyMode" access="private" output="false" returntype="struct">
		<cfset var local = structnew()>
		<cfset local.returnStruct = { "success":true, "itemCount":0 }>
		
		<cftry>
			<cfstoredproc procedure="rpt_resetReadOnlyReports" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.returnStruct.itemCount">
			</cfstoredproc>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

</cfcomponent>