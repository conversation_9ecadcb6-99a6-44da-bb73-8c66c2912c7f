CREATE TRIGGER lists_UpdateTrig 
ON lists_ 
AFTER UPDATE AS 
   BEGIN 
	SET NOCOUNT ON 
    IF UPDATE(Name_) 
     BEGIN 
       UPDATE lyrMemberSubsets SET List = i.Name_ FROM inserted i, subsets_ s, deleted d WHERE 
              i.ListID_ = d.ListID_ AND s.List_ = d.Name_ AND SubsetID = s.SubsetID_ AND List = d.Name_ 
  END 
	SET NOCOUNT OFF 
END
ALTER TABLE [dbo].[lists_] ENABLE TRIGGER [lists_UpdateTrig]
GO
