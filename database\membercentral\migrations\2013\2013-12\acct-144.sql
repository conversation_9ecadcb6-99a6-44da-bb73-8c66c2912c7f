use [<PERSON><PERSON>]
GO

ALTER TABLE dbo.TaxTable ADD isActive bit NOT NULL CONSTRAINT DF_TaxTable_isActive DEFAULT 1
GO

ALTER TABLE dbo.depoTransactions ADD taxTableID int NULL
GO
ALTER TABLE dbo.depoTransactions ADD CONSTRAINT
	FK_depoTransactions_TaxTable FOREIGN KEY
	(
	taxTableID
	) REFERENCES dbo.TaxTable
	(
	TaxTableID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
GO

-- Update the depoTransactions to use taxTableID 
update dbo.depoTransactions set taxTableID = 40 where salesTaxAmount <> 0 and DatePurchased < '11/1/2013'

update t
set t.taxTableID = tt.taxTableID
from depoTransactions t 
inner join depomemberdata d on d.depomemberdataid = t.depomemberdataid
inner join (
	select min(taxtableid) as taxTableID, state
	from taxtable
	where orgcode = 'TS'
	group by state
) as tt on tt.state = d.billingstate 
where t.salesTaxAmount <> 0 
and t.datePurchased >= '11/1/2013'
GO

--- New Stored Proc for sales tax breakdown
CREATE PROCEDURE [dbo].[report_SalesTaxBreakDown]
  @BeginDate smalldatetime,
  @EndDate smalldatetime
AS

select	tt.state,
		sum(isnull(AmountBilled,0)) as TaxableSales, 
		sum(isnull(t.SalesTaxAmount,0)) as SalesTaxAmount
from depoTransactions t
inner join taxTable tt on tt.taxTableID = t.taxTableID
where t.DatePurchased between @BeginDate and @EndDate 
and isnull(t.accountCode,'') <> '8300'
group by tt.state
GO


CREATE Function [dbo].[fn_SalesTax_getTaxRule] (
	@orgcode varchar(5),
	@billingstate varchar(5),
	@shippingstate varchar(5)
) 
returns int
AS
begin

	declare @taxTableID int

	select top 1 @taxTableID = taxTableID
	from dbo.taxTable
	where orgcode = @orgcode
	and (
		(state = @billingstate and billingStateEligible = 1)
		or	
		(state = @shippingstate and shippingStateEligible = 1)
	)
	and isActive = 1
	order by taxrate desc
	
	return @taxTableID

end
GO


ALTER Function [dbo].[fn_VerdictsCart_getPrice] (
	@verdictID int,
	@billingstate varchar(5),
	@websiteorgcode varchar(5),
	@depomemberdataID int
) 
returns @tblPrice table (
	verdictID int,
	depomemberdataid int,
	price numeric(9,2), 
	salestaxamount numeric(9,2),
	taxtableid int,
	pricefound bit
)
AS
begin

	-- enter default row
	INSERT INTO @tblPrice(verdictID,depomemberdataid,price,salestaxamount,pricefound)
	VALUES(@verdictID,@depomemberdataID,0,0,0)

	-- hasPermission
	DECLARE @hasPermission bit
	SELECT @hasPermission = dbo.fn_Verdicts_checkPermissionsToDoc(@verdictID,@depomemberdataID)

	-- use default row if there is no price needed
	IF @hasPermission = 1
	BEGIN
		UPDATE @tblPrice SET pricefound = 1
		RETURN
	END

	-- standard price
	DECLARE @numVerdicts int, @newCost numeric(18,2)
	select @numVerdicts = count(cartID) from dbo.documentCart where depomemberdataid = @depomemberdataID AND itemTypeID = 4
	select @newCost = amount/@numVerdicts from dbo.verdictPricing where verdictCount = @numVerdicts
	IF @newCost is not null
		UPDATE @tblPrice 
		SET price = @newCost, 
			salestaxamount = dbo.fn_SalesTax_getTax('TS',@newCost,@billingstate,''),
			taxtableid = dbo.fn_SalesTax_getTaxRule('TS',@billingstate,''),
			pricefound = 1

	return
end
GO


ALTER Function [dbo].[fn_Documents_getPrice] (
	@documentID int,
	@membertype int,
	@billingstate varchar(5),
	@websiteorgcode varchar(5),
	@depomemberdataID int
) 
returns @tblPrice table (
	documentID int,
	depomemberdataid int,
	price numeric(9,2), 
	salestaxamount numeric(9,2),
	taxtableid int,
	depoDocumentsPriceExceptionID int,
	royaltyArrangementid int,
	pricefound bit
)
AS
begin

	-- enter default row
	INSERT INTO @tblPrice(documentID,depomemberdataid,price,salestaxamount,depoDocumentsPriceExceptionID,royaltyArrangementid,pricefound)
	VALUES(@documentID,@depomemberdataID,0,0,0,0,0)

	-- hasContributed
	DECLARE @hasContributed bit	
	IF EXISTS (select depomemberdataid from dbo.depoDocuments where documentID = @documentID and depomemberdataID = @depomemberdataID)
		SELECT @hasContributed = 1
	ELSE	
		SELECT @hasContributed = 0

	-- isExpertDoc
	DECLARE @isExpertDoc bit	
	IF EXISTS (select documentTypeID from dbo.depoDocuments where documentID = @documentID and documentTypeID = 288 and casetypeid = 115)
		SELECT @isExpertDoc = 1
	ELSE	
		SELECT @isExpertDoc = 0

	-- hasPermission
	DECLARE @hasPermission bit
	SELECT @hasPermission = dbo.fn_Documents_checkPermissionsToDoc(@documentID,@depomemberdataID)

	-- hasBankAccess
	DECLARE @hasBankAccess bit
	SELECT @hasBankAccess = dbo.fn_Documents_hasBankAccess(@documentID,@depomemberdataID)

	-- use default row if there is no price needed
	IF @hasContributed = 1 OR @isExpertDoc = 1 or @hasPermission = 1 or @hasBankAccess = 1
	BEGIN
		UPDATE @tblPrice SET pricefound = 1
		RETURN
	END

	-- Figure out which royalty plan this falls under if any
	DECLARE @royaltyArrangementid int
	SELECT TOP 1 @royaltyArrangementid = dra.royaltyArrangementid
		FROM dbo.depoDocumentsRoyaltyArrangements AS dra 
		INNER JOIN dbo.depoDocuments AS d
			ON dra.documentorgcode = d.State
			and isnull(dra.websiteorgcode,'') in (@websiteorgcode,'')
			AND d.DocumentID = @documentID
		WHERE dra.purchaserorgcode IN (
			SELECT tlamemberstate 
			from dbo.depomemberdata 
			where depomemberdataid = @depomemberdataID
			union
			select orgcode 
			from dbo.orgmemberdata
			where depomemberdataid = @depomemberdataID
			union
			select o.orgcode
			from membercentral.membercentral.dbo.ams_networkProfiles np
			inner join membercentral.membercentral.dbo.ams_memberNetworkProfiles mnp
				on mnp.profileID = np.profileID
				and np.depomemberdataID = @depomemberdataID
			inner join membercentral.membercentral.dbo.ams_members m
				on m.memberID = mnp.memberID
			inner join membercentral.membercentral.dbo.organizations o
				on o.orgID = m.orgID
		)
		ORDER BY dra.royaltyArrangementid
	IF @royaltyArrangementid IS NOT NULL
		UPDATE @tblPrice SET royaltyArrangementid = @royaltyArrangementid

	-- Price exceptions
	DECLARE @newprice numeric(18,2), @depoDocumentsPriceExceptionID int
	SELECT top 1 @newprice = dpe.price, @depoDocumentsPriceExceptionID = dpe.depoDocumentsPriceExceptionID
		FROM dbo.depoDocumentsPriceException dpe
		INNER JOIN dbo.depoDocuments d ON dpe.documenttypeid = d.DocumentTypeID AND dpe.orgcode = d.State
		WHERE d.DocumentID = @documentID 
		AND dpe.websiteorgcode in (@websiteorgcode,'')
		AND dpe.usergroupid IN (
			SELECT DISTINCT ugm.usergroupid
			FROM dbo.usergroupmembers AS ugm 
			INNER JOIN dbo.orgmemberdata AS o ON ugm.orgmemberdataid = o.orgmemberdataid
			WHERE o.depomemberdataid = @depomemberdataID
			union
			SELECT DISTINCT g.userGroupId
			FROM dbo.orgMemberOptions AS o 
			INNER JOIN dbo.orgMemberOptionGroups AS g ON o.optionCodeID = g.optionCodeID 
			INNER JOIN dbo.orgmemberdata As om ON o.orgMemberDataID = om.orgmemberdataid
			WHERE om.depomemberdataid = @depomemberdataID
			union
			select distinct g.groupID
			from membercentral.membercentral.dbo.ams_networkProfiles np
			inner join membercentral.membercentral.dbo.ams_memberNetworkProfiles mnp
				on mnp.profileID = np.profileID
				and np.depomemberdataID = @depomemberdataID
			inner join membercentral.membercentral.dbo.ams_members m
				on m.memberID = mnp.memberID
			inner join membercentral.membercentral.dbo.organizations o
				on o.orgID = m.orgID
			inner join membercentral.membercentral.dbo.cache_members_groups mg
				on mg.memberID = m.memberID
			inner join membercentral.membercentral.dbo.ams_groups g
				on g.groupID = mg.groupID
		)
		order by dpe.price
	IF @newprice is not null
	BEGIN
		UPDATE @tblPrice 
		SET price = @newprice, 
			depoDocumentsPriceExceptionID = @depoDocumentsPriceExceptionID,
			salestaxamount = dbo.fn_SalesTax_getTax('TS',@newPrice,@billingstate,''),
			taxtableid = dbo.fn_SalesTax_getTaxRule('TS',@billingstate,''),
			pricefound = 1

		RETURN
	END

	-- standard price
	IF @newprice is null
	BEGIN
		DECLARE @newCost numeric(18,2)
		SELECT TOP 1 @newCost = pt.cost
			FROM dbo.pricetable AS pt 
			INNER JOIN dbo.depoDocuments AS d ON pt.DocumentType = d.DocumentTypeID
			WHERE pt.MemberType = @memberType
			AND d.DocumentID = @documentID
			ORDER BY pt.cost
		IF @newCost is not null
		BEGIN
			UPDATE @tblPrice 
			SET price = @newCost, 
				salestaxamount = dbo.fn_SalesTax_getTax('TS',@newCost,@billingstate,''),
				taxtableid = dbo.fn_SalesTax_getTaxRule('TS',@billingstate,''),
				pricefound = 1
			RETURN
		END	
	END

	return
end
GO


ALTER Function [dbo].[fn_Documents_getShippingCosts] (
	@documentID int,
	@deliveryTypeID int,
	@billingstate varchar(5),
	@depomemberdataID int
) 
returns @tblShippingCosts table (
	price numeric(9,2), 
	salestaxamount numeric(9,2),
	taxtableid int,
	accountcode int,
	deliveryType varchar(20),
	shippingNeeded bit
)
AS
begin

	-- get documentTypeID
	DECLARE @documentTypeID int
	SELECT @documentTypeID = documenttypeid FROM dbo.depodocuments WHERE documentid = @documentID
	IF @documentTypeID IS NULL
		RETURN

	-- insert blank record
	INSERT INTO @tblShippingCosts (price, salestaxamount, accountcode, deliveryType, shippingNeeded)
	SELECT 0, 0, acctcode, deliveryType, shippingNeeded
	from dbo.documentCartDeliveryTypes
	where deliveryTypeID = @deliveryTypeID

	-- declare vars
	DECLARE @newPrice numeric(9,2), @shippingMin numeric(9,2)
	SELECT @shippingMin = shippingMin from dbo.documentCartDeliveryTypes where deliveryTypeID = @deliveryTypeID

	-- Laser 
	IF @deliveryTypeID = 1 AND EXISTS (select op1 from dbo.depodocumentTypes where typeid = @documentTypeID and op1 = 1)
	BEGIN
		DECLARE @numPages int
		SELECT @numPages = pages from depodocuments where documentid = @documentID

		IF @numPages IS NOT NULL
		BEGIN
			SELECT @newPrice = cast(@numPages as numeric(9,2)) * shippingperpage from dbo.documentCartDeliveryTypes where deliveryTypeID = @deliveryTypeID
			IF @newPrice < @shippingMin
				SELECT @newPrice = @shippingMin
		END

		IF @numPages IS NULL
			SELECT @newPrice = @shippingMin

		update @tblShippingCosts 
		set price = @newPrice,
			salestaxamount = dbo.fn_SalesTax_getTax('TS',@newPrice,@billingstate,''),
			taxtableid = dbo.fn_SalesTax_getTaxRule('TS',@billingstate,'')
	END

	-- CD
	IF @deliveryTypeID = 2 AND EXISTS (select op2 from dbo.depodocumentTypes where typeid = @documentTypeID and op2 = 1)
	BEGIN
		IF EXISTS (select t.transactionid 
				from dbo.depotransactions t
				inner join dbo.documentCartDeliveryTypes dt on dt.acctcode = t.accountcode
				where t.depomemberdataid = @depomemberdataID
				and dt.deliveryTypeID = @deliveryTypeID
				and t.reversable = 'Y'
				and t.datepurchased > dbo.fn_DeliveryTypes_getPreviousCutoff(getdate())) OR
			EXISTS (
				select cartid from dbo.documentcart 
				where deliveryTypeID = @deliveryTypeID 
				and depomemberdataid = @depomemberdataID 
				and cartID < (select cartid 
								from documentcart 
								where depomemberdataid = @depomemberdataID 
								and documentid = cast(@documentID as varchar(20)))
			)
			SELECT @newPrice = 0
		ELSE
			SELECT @newPrice = @shippingMin

		update @tblShippingCosts 
		set price = @newPrice,
			salestaxamount = dbo.fn_SalesTax_getTax('TS',@newPrice,@billingstate,''),
			taxtableid = dbo.fn_SalesTax_getTaxRule('TS',@billingstate,'')
	END

	-- Email
	IF @deliveryTypeID = 3 AND EXISTS (select op3 from dbo.depodocumentTypes where typeid = @documentTypeID and op3 = 1)
		update @tblShippingCosts 
		set price = @shippingMin,
			salestaxamount = dbo.fn_SalesTax_getTax('TS',@shippingMin,@billingstate,''),
			taxtableid = dbo.fn_SalesTax_getTaxRule('TS',@billingstate,'')


	-- Download
	IF @deliveryTypeID = 4 AND EXISTS (select op4 from dbo.depodocumentTypes where typeid = @documentTypeID and op4 = 1)
		update @tblShippingCosts 
		set price = @shippingMin, 
			salestaxamount = dbo.fn_SalesTax_getTax('TS',@shippingMin,@billingstate,''),
			taxtableid = dbo.fn_SalesTax_getTaxRule('TS',@billingstate,'')

	-- View
	IF @deliveryTypeID = 5 AND EXISTS (select op5 from dbo.depodocumentTypes where typeid = @documentTypeID and op5 = 1)
		update @tblShippingCosts 
		set price = @shippingMin,
			salestaxamount = dbo.fn_SalesTax_getTax('TS',@shippingMin,@billingstate,''),
			taxtableid = dbo.fn_SalesTax_getTaxRule('TS',@billingstate,'')

	-- Video
	IF @deliveryTypeID = 6 AND EXISTS (select op6 from dbo.depodocumentTypes where typeid = @documentTypeID and op6 = 1)
		update @tblShippingCosts 
		set price = @shippingMin,
			salestaxamount = dbo.fn_SalesTax_getTax('TS',@shippingMin,@billingstate,''),
			taxtableid = dbo.fn_SalesTax_getTaxRule('TS',@billingstate,'')

	return
end
GO

ALTER Function [dbo].[fn_cart_getDocuments] (
	@depomemberdataid int,
	@membertype int,
	@billingstate varchar(5),
	@orgcode varchar(5)
) 
returns @tmpCart table (
	cartID int,
	itemTypeID int,
	documentID varchar(30),
	caseRef varchar(100),
	deliveryTypeID int,
	deliveryType varchar(20),
	pPrice numeric(9,2),
	pSalesTaxAmount numeric(9,2),
	pTaxTableID int,
	sPrice numeric(9,2),
	sSalesTaxAmount numeric(9,2),
	sTaxTableID int,
	expertName varchar(500),
	documentDate varchar(30),
	docTitle varchar(255),
	dateAdded datetime,
	transactionCreditAllowed char(1),
	shippingNeeded bit,
	displayType varchar(30),
	displayTypeOrder int
)

AS
BEGIN

	-- legal forms
	INSERT INTO @tmpCart (cartID, itemTypeID, documentID, caseRef, deliveryTypeID, deliveryType, pPrice, pSalesTaxAmount, 
		pTaxTableID, sPrice, sSalesTaxAmount, sTaxTableID, expertName, documentDate, docTitle, dateAdded, transactionCreditAllowed, 
		shippingNeeded,	displayType, displayTypeOrder)
	SELECT TOP 100 PERCENT c.cartID, c.itemTypeID, c.documentID, c.caseRef, dt.deliveryTypeID, dt.deliveryType,
		d.price as pPrice, trialsmith.dbo.fn_SalesTax_getTax('TS',d.price,@billingstate,'') as pSalesTaxAmount, 
		trialsmith.dbo.fn_SalesTax_getTaxRule('TS',@billingstate,'') as pTaxTableID,
		0 as sPrice, 0 as sSalesTaxAmount, null as sTaxTableID, null as expertName, null as documentDate, d.formTitle as docTitle, 
		c.dateAdded, 'N' as transactionCreditAllowed, dt.shippingNeeded, 'Legal Forms' as displayType, 1 as displayTypeOrder
	FROM dbo.documentCart as c
	INNER JOIN dbo.documentCartDeliveryTypes as dt on dt.deliveryTypeID = c.deliveryTypeID
	inner join uslegalforms.dbo.tblForms as d on d.controlNum = c.documentID
	WHERE c.depomemberdataid = @depomemberdataid
	AND c.itemTypeID = 3
	ORDER BY dateAdded DESC

	-- verdicts
	INSERT INTO @tmpCart (cartID, itemTypeID, documentID, caseRef, deliveryTypeID, deliveryType, pPrice, pSalesTaxAmount, 
		pTaxTableID, sPrice, sSalesTaxAmount, sTaxTableID, expertName, documentDate, docTitle, dateAdded, transactionCreditAllowed, 
		shippingNeeded, displayType, displayTypeOrder)
	SELECT TOP 100 PERCENT c.cartID, c.itemTypeID, c.documentID, c.caseRef, dt.deliveryTypeID, dt.deliveryType,
		p.price as pPrice, p.salestaxamount as pSalesTaxAmount, p.taxtableid as pTaxTableID, 0 as sPrice, 0 as sSalesTaxAmount, null as sTaxTableID,
		dv.Name, d.verdictDate, d.topic as docTitle, c.dateAdded, 'N' as transactionCreditAllowed, dt.shippingNeeded,
		'Verdicts' as displayType, 2 as displayTypeOrder
	FROM dbo.documentCart as c
	INNER JOIN dbo.documentCartDeliveryTypes as dt on dt.deliveryTypeID = c.deliveryTypeID
	inner join dbo.verdicts as d on d.verdictID = c.documentID
	inner join dbo.verdictVendor as dv on dv.vendorid = d.vendorid
	cross apply dbo.fn_VerdictsCart_getPrice(c.documentid,@billingstate,@orgcode,@depomemberdataid) as p
	WHERE c.depomemberdataid = @depomemberdataid
	AND c.itemTypeID = 4
	AND dbo.fn_Verdicts_checkPermissionsToDoc(d.verdictID,@depomemberdataid) = 0
	ORDER BY dateAdded DESC
	
	-- update verdict pricing -- all non zero verdicts should be counted and used in lookup of price
	declare @numVerdicts int, @cartIDFirstVerdict int, @priceOfVerdicts money
	select @numVerdicts = count(cartID) from @tmpCart where itemTypeID = 4 and pPrice > 0	
	select @cartIDFirstVerdict = min(cartID) from @tmpCart where itemTypeID = 4 and pPrice > 0
	if @numVerdicts > 0
	begin
		-- set all prices to 0		
		update @tmpCart set pPrice = 0, pSalesTaxAmount = 0 WHERE itemTypeID = 4

		-- get price based on count of purchaseable verdicts
		select @priceOfVerdicts = amount from dbo.verdictPricing where verdictCount = @numVerdicts

		-- update verdicts in cart
		update @tmpCart
		set pPrice = @priceOfVerdicts, 
			pSalesTaxAmount = dbo.fn_SalesTax_getTax('TS',@priceOfVerdicts,@billingstate,''),
			pTaxTableID = dbo.fn_SalesTax_getTaxRule('TS',@billingstate,'')
		where cartID = @cartIDFirstVerdict
		AND itemTypeID = 4
	end

	-- disc actions
	INSERT INTO @tmpCart (cartID, itemTypeID, documentID, caseRef, deliveryTypeID, deliveryType, pPrice, pSalesTaxAmount, 
		pTaxTableID, sPrice, sSalesTaxAmount, sTaxTableID, expertName, documentDate, docTitle, dateAdded, transactionCreditAllowed, shippingNeeded,
		displayType, displayTypeOrder)
	SELECT TOP 100 PERCENT c.cartID, c.itemTypeID, c.documentID, c.caseRef, dt.deliveryTypeID, dt.deliveryType,
		p.cost as pPrice, p.salesTax as pSalesTaxAmount, p.taxtableid as pTaxTableID, 0 as sPrice, 0 as sSalesTaxAmount, 
		null as sTaxTableID,
		isnull(un.firstname,'') + ' ' + isnull(un.lastname,'') as expertName, null as documentDate, 
		isnull(un.salutation,'') + ' ' + isnull(un.firstname,'') + ' ' + isnull(un.middlename,'') + ' ' + isnull(un.lastname,'') + ' ' + isnull(un.suffix,'') + ' ' + isnull(un.professionalSuffix,'') as docTitle, 
		c.dateAdded, 
		'N' as transactionCreditAllowed, dt.shippingNeeded, 'Disciplinary Actions' as displayType, 
		4 as displayTypeOrder
	FROM dbo.documentCart as c
	INNER JOIN dbo.documentCartDeliveryTypes as dt on dt.deliveryTypeID = c.deliveryTypeID
	inner join disiplinaryActions.dbo.uniqueNames as un on un.uniqueNameID = c.documentID
	cross apply (
		select price.cost, dbo.fn_SalesTax_getTax('TS',price.cost,@billingstate,'') as salesTax, dbo.fn_SalesTax_getTaxRule('TS',@billingstate,'') as taxTableID
		from dbo.depoDocumenttypes as dt
		inner join dbo.priceTable as price on dt.typeID = price.documentType and dt.description = 'Disciplinary Actions' and price.memberType = @membertype
	) as p
	WHERE c.depomemberdataid = @depomemberdataid
	AND c.itemTypeID = 6
	ORDER BY dateAdded DESC

	-- depositions, court docs, mdex
	INSERT INTO @tmpCart (cartID, itemTypeID, documentID, caseRef, deliveryTypeID, deliveryType, pPrice, pSalesTaxAmount, 
		pTaxTableID, sPrice, sSalesTaxAmount, sTaxTableID, expertName, documentDate, docTitle, dateAdded, transactionCreditAllowed, 
		shippingNeeded,	displayType, displayTypeOrder)
	SELECT TOP 100 PERCENT c.cartID, c.itemTypeID, c.documentID, c.caseRef, dt.deliveryTypeID, dt.deliveryType,
		p.price as pPrice, p.salestaxamount as pSalesTaxAmount, p.taxtableid as pTaxTableID, s.price as sPrice, 
		s.salestaxamount as sSalesTaxAmount, s.taxtableid as sTaxTableID,
		d.expertName, convert(varchar(10),d.documentDate,101) as documentDate, d.style as docTitle, c.dateAdded, 
		ddt.transactionCreditAllowed, dt.shippingNeeded, 'Documents' as displayType, 3 as displayTypeOrder
	FROM dbo.documentCart as c
	INNER JOIN dbo.documentCartDeliveryTypes as dt on dt.deliveryTypeID = c.deliveryTypeID
	inner join dbo.depoDocuments as d on d.documentID = c.documentID
	inner join dbo.depoDocumentTypes as ddt on ddt.typeID = d.documentTypeID
	cross apply dbo.fn_Documents_getPrice(c.documentid,@membertype,@billingstate,@orgcode,@depomemberdataid) as p
	cross apply dbo.fn_Documents_getShippingCosts(c.documentid,dt.deliveryTypeID,@billingstate,@depomemberdataid) as s
	WHERE c.depomemberdataid = @depomemberdataid
	AND c.itemTypeID IN (1,2,5,7)
	ORDER BY dateAdded DESC

	return
END
GO

ALTER PROCEDURE [dbo].[up_Document_Buy]
	@documentID int,
	@depomemberdataid int,
	@membertype int,
	@billingstate varchar(5),
	@orgcode varchar(5),
	@deliveryTypeID int,
	@allowPCuse bit,
	@caseref varchar(600),
	@linksource varchar(50),
	@linkterms varchar(100),
	@transactionID int OUTPUT

AS

DECLARE @pPrice numeric(9,2), @pSalesTaxAmount numeric(9,2), @pTaxTableID int
DECLARE @depoDocumentsPriceExceptionID int, @royaltyArrangementid int
DECLARE @sPrice numeric(9,2), @sSalesTaxAmount numeric(9,2), @sTaxTableID int
SELECT @transactionID = 0

-- get price for doc
select *
into #tmpGetPrice
from dbo.fn_Documents_getPrice(@documentID,@membertype,@billingstate,@orgcode,@depomemberdataid)

SELECT @pPrice = price, @pSalesTaxAmount = isnull(salestaxamount,0), @pTaxTableID = taxtableid,
	@depoDocumentsPriceExceptionID = depoDocumentsPriceExceptionID, 
	@royaltyArrangementid = royaltyArrangementid
from #tmpGetPrice

-- get shipping for doc
select *
into #tmpGetShipping
from dbo.fn_Documents_getShippingCosts(@documentID,@deliveryTypeID,@billingstate,@depomemberdataid)

SELECT @sPrice = isnull(price,0), @sSalesTaxAmount = isnull(salestaxamount,0), @sTaxTableID = taxtableid
from #tmpGetShipping

-- if price is not found
IF NOT EXISTS (select pricefound from #tmpGetPrice where pricefound = 1)
	RETURN

-- get purchase credits
DECLARE @pcbalance numeric(9,2)
SELECT @pcbalance = balance from dbo.fn_Documents_getPurchaseCredits(@depomemberdataid)

BEGIN TRAN
	-- get transgroup
	DECLARE @transgroup int	
	SELECT @transgroup = MAX(transgroup) + 1 FROM dbo.depoTransactions

	-- check permission and add if needed
	IF (dbo.fn_Documents_checkPermissionsToDoc(@documentID,@depomemberdataid) = 0)
	BEGIN
		insert into dbo.depoPermissions (depomemberdataid, documentid, dateadded)
		values (@depomemberdataid,@documentID,getdate())
			IF @@ERROR <> 0 GOTO on_error
	END

	-- insert sale of document
	INSERT INTO dbo.depoTransactions (depomemberdataid, documentID, [Description], AmountBilled, AccountCode, 
		CaseRef, SourceState, deliveryType, salestaxamount, taxtableid, orgcode, transgroup, 
		depodocumentspriceexception, linksource, linkterms, royaltyArrangementid)
	SELECT TOP 1 @depomemberdataid, @documentID, dt.description + ' - ' + d.expertname, @pPrice, dt.acctcode, 
		@caseref, d.state, @deliveryTypeID, isnull(@pSalesTaxAmount,0), @pTaxTableID, @orgcode, @transgroup, 
		@depoDocumentsPriceExceptionID, @linksource, @linkterms, @royaltyArrangementid
	FROM dbo.depoDocuments d
	INNER JOIN dbo.DepoDocumentTypes dt on dt.TypeID = d.documentTypeID
	WHERE d.documentID = @documentID
		IF @@ERROR <> 0 GOTO on_error

	-- get new transactionid
	SELECT @transactionID = IDENT_CURRENT('depoTransactions')
		IF @@ERROR <> 0 GOTO on_error

	-- can we use whole purchase credit?	
	IF @allowPCuse = 1 AND @pPrice > 0 AND @pcbalance >= @pPrice
	BEGIN
		DECLARE @reversePrice numeric(9,2), @reverseTax numeric(9,2)
		SELECT @reversePrice = @pPrice * -1
			IF @@ERROR <> 0 GOTO on_error
		SELECT @reverseTax = @pSalesTaxAmount * -1
			IF @@ERROR <> 0 GOTO on_error

		-- insert pc transaction
		INSERT INTO depoTransactions (depomemberdataid, documentID, Description, AmountBilled, AccountCode, 
			CaseRef, SourceState, deliveryType, salestaxamount, taxtableid, orgcode, transgroup, 
			linksource, linkterms, royaltyArrangementid, Reversable, PurchaseCreditFlag)
		SELECT TOP 1 @depomemberdataid, @documentID, 'Purchase Credit Applied from Document Contributions', 
			@reversePrice, dt.acctcode, @caseref, d.state, @deliveryTypeID, isnull(@reverseTax,0), @pTaxTableID, @orgcode, @transgroup,
			@linksource, @linkterms, @royaltyArrangementid, 'N', 'Y'
		FROM dbo.depoDocuments d
		INNER JOIN dbo.DepoDocumentTypes dt on dt.TypeID = d.documentTypeID
		WHERE d.documentID = @documentID
			IF @@ERROR <> 0 GOTO on_error

		-- deduct from PC
		insert into PurchaseCredits (DepomemberDataId, StateToCredit, StateCreditAmount, PurchaseCreditAmount, 
			CreditDescription, DocumentID, transactionid)
		SELECT TOP 1 @depomemberdataid, d.State, 0, @reversePrice, 'Credit Deducted for Purchase of document ' + CAST(@documentID as varchar(10)), @documentID, @transactionID
		FROM dbo.depoDocuments d
		WHERE d.documentID = @documentID
			IF @@ERROR <> 0 GOTO on_error
	END
	
	-- can we use partial purchase credit?	
	IF @allowPCuse = 1 AND @pcbalance > 0 AND @pcbalance < @pPrice
	BEGIN
		-- get tax
		DECLARE @pc_salestaxamount numeric(9,2)
		SELECT @pc_salestaxamount = isnull(dbo.fn_SalesTax_getTax('TS',@pcbalance,@billingstate,''),0)
			IF @@ERROR <> 0 GOTO on_error

		DECLARE @pc_taxtableid int
		SELECT @pc_taxtableid = dbo.fn_SalesTax_getTaxRule('TS',@billingstate,'')
			IF @@ERROR <> 0 GOTO on_error

		DECLARE @reverseBalance numeric(9,2), @reverseBalanceTax numeric(9,2)
		SELECT @reverseBalance = @pcbalance * -1
			IF @@ERROR <> 0 GOTO on_error
		SELECT @reverseBalanceTax = @pc_salestaxamount * -1
			IF @@ERROR <> 0 GOTO on_error

		-- insert pc transaction
		INSERT INTO depoTransactions (depomemberdataid, documentID, Description, AmountBilled, 
			AccountCode, CaseRef, SourceState, deliveryType, SalesTaxAmount, TaxTableID, Reversable, 
			PurchaseCreditFlag, orgcode, transgroup, linksource, linkterms, royaltyArrangementid)
		SELECT TOP 1 @depomemberdataid, @documentID, 'Purchase Credit Applied from Document Contributions', @reverseBalance, 
			dt.acctcode, @caseref, d.state, @deliveryTypeID, isnull(@reverseBalanceTax,0), @pc_taxtableid, 'N', 
			'Y', @orgcode, @transgroup, @linksource, @linkterms, @royaltyArrangementid
		FROM dbo.depoDocuments d
		INNER JOIN dbo.DepoDocumentTypes dt on dt.TypeID = d.documentTypeID
		WHERE d.documentID = @documentID
			IF @@ERROR <> 0 GOTO on_error
		
		-- deduct from PC
		insert into dbo.PurchaseCredits (DepomemberDataId, StateToCredit, StateCreditAmount, PurchaseCreditAmount, 
			CreditDescription, DocumentID, transactionid)
		SELECT TOP 1 @depomemberdataid, d.State, 0, @reverseBalance, 'Credit Deducted for Purchase of document ' + CAST(@documentID as varchar(10)), @documentID, @transactionID
		FROM dbo.depoDocuments d
		WHERE d.documentID = @documentID
			IF @@ERROR <> 0 GOTO on_error
	END

	-- shipping
	IF @sPrice <> 0	
	BEGIN
		INSERT INTO dbo.depoTransactions (depomemberdataid, [Description], AmountBilled, AccountCode, 
			deliveryType, SourceState, SalesTaxAmount, TaxTableID, orgcode, transgroup, linksource, linkterms, royaltyArrangementid)
		SELECT TOP 1 @depomemberdataid, 'Shipping Charge - ' + s.deliveryType, s.price, s.accountcode, 
			@deliveryTypeID, d.state, isnull(s.salestaxamount,0), s.taxtableid, @orgcode, 0, @linksource, @linkterms, @royaltyArrangementid
		FROM dbo.depoDocuments d
		CROSS JOIN #tmpGetShipping s
		WHERE d.documentID = @documentID
			IF @@ERROR <> 0 GOTO on_error
	END

COMMIT TRAN
GOTO on_result

on_error:
	ROLLBACK TRAN
	GOTO on_result

on_result:
	-- Drop temp tables
	DROP TABLE #tmpGetPrice
	DROP TABLE #tmpGetShipping
	
	-- end sp
	RETURN
GO


ALTER PROC [dbo].[documentCart_Checkout]
	@depomemberdataID int,
	@billingstate varchar(5),
	@orgCode varchar(5),
	@sourceState varchar(20),
	@linksource varchar(50),
	@linkterms varchar(100)
AS

SET NOCOUNT ON

DECLARE @transactionID int
declare @caseref varchar(600)

-- get membertype
declare @membertype int
select @membertype = membertype from depomemberdata where depomemberdataid = @depomemberdataid

-- delete any verdicts in cart that user has permission to already (so we can calculate correct verdict price)
delete from dbo.documentCart
where depomemberdataid = @depomemberdataID
and itemTypeID = 4
and dbo.fn_Verdicts_checkPermissionsToDoc(documentID,@depomemberdataid) = 1

-- get cart items
select *, null as success
into #tmpCart
from dbo.fn_Cart_getDocuments(@depomemberdataID,@membertype,@billingstate,@orgCode)

-- get opening purchase credit
declare @pcstart int
select @pcstart = balance from dbo.fn_Documents_getPurchaseCredits(@depomemberdataID)

/************************************
buy all verdicts in cart in one shot 
*************************************/
IF EXISTS (select documentid from #tmpCart where itemTypeID = 4)
BEGIN
	BEGIN TRAN
	
	declare @verdictAmount numeric(18,2), @verdictTaxAmount numeric(18,2), @verdictTaxTableID int
	select @verdictAmount = sum(pPrice), @verdictTaxAmount = sum(pSalesTaxAmount) from #tmpCart where itemTypeID = 4
		IF (@@ERROR <> 0) goto on_error
	select top 1 @verdictTaxTableID = pTaxTableID from #tmpCart where itemTypeID = 4
		IF (@@ERROR <> 0) goto on_error

	-- select caseref (left 600)
	select @caseref = left(REPLACE((
		Select distinct caseref as [data()]
		from #tmpCart
		where itemTypeID = 4
		and len(caseref) > 0
		FOR XML PATH ('')
		), ' ', ', '),600)

	INSERT INTO dbo.depoTransactions (documentID, [Description], AmountBilled, salestaxamount, taxtableid, AccountCode, transgroup, CaseRef, SourceState, Depomemberdataid, deliveryType, orgcode, linksource, linkterms)
	VALUES(-20, 'Verdict Purchase - ' + isnull(@caseref,''), @verdictAmount, isnull(@verdictTaxAmount,0), @verdictTaxTableID, '5001', 0, @caseref, @sourceState, @depomemberdataID, 4, @orgCode, @linksource, @linkterms)
		IF (@@ERROR <> 0) goto on_error
	SELECT @transactionID = IDENT_CURRENT('depoTransactions')
		IF (@@ERROR <> 0) goto on_error

	insert into dbo.verdictPermissions (verdictid, depomemberdataid, dateAdded, transactionID)
	select documentid, @depomemberdataID, getdate(), @transactionID
	from #tmpCart
	where itemTypeID = 4
		IF (@@ERROR <> 0) goto on_error

	UPDATE #tmpCart
	SET success = 1
	where itemTypeID = 4
		IF (@@ERROR <> 0) goto on_error
	COMMIT TRAN
END

/******************************************
loop over remaining cart items and buy them
*******************************************/
declare @minCartID int, @itemtypeid int, @documentid varchar(20), @pprice numeric(9,2), @psalestaxamount numeric(9,2), @ptaxtableid int
declare @deliveryTypeID int, @allowPCuse bit
SELECT @minCartID = min(cartid) from #tmpCart where success is null
WHILE @minCartID is not null
BEGIN
	-- get info from cart
	select @itemtypeid = itemtypeid, @documentid = documentID, @deliveryTypeID = deliveryTypeID,
		@allowPCuse = CASE WHEN transactioncreditallowed = 'Y' then 1 else 0 end,
		@pprice = pprice, @psalestaxamount = psalestaxamount, @ptaxtableid = ptaxtableid, @caseref = caseref
		from #tmpCart
		where cartID = @minCartID

	-- if legal form
	IF @itemtypeid = 3
		EXEC uslegalforms.dbo.up_buyForm @documentID, @depomemberdataID, @billingstate, @linksource, @linkterms, @orgcode, @pprice, @psalestaxamount, @ptaxtableid, @caseref, @transactionID OUTPUT
	
	-- if disc action
	IF @itemtypeid = 6
		EXEC disiplinaryactions.dbo.up_buyActions @documentID, @depomemberdataID, @billingstate, @linksource, @linkterms, @orgcode, @pprice, @psalestaxamount, @ptaxtableid, @caseref, @transactionID OUTPUT

	-- else if depo, mdex, courtdoc
	IF @itemtypeid IN (1,2,5,7)
		EXEC dbo.up_document_buy @documentID, @depomemberdataID, @membertype, @billingstate, @orgcode, @deliveryTypeID, @allowPCuse, @caseref, @linksource, @linkterms, @transactionID OUTPUT

	-- set success flag	
	UPDATE #tmpCart
	SET success = CASE WHEN @transactionID > 0 THEN 1 else 0 end
	where cartID = @minCartID

	SELECT @minCartID = min(cartid) from #tmpCart where success is null and cartID > @minCartID
END

GOTO on_end

on_error:
	ROLLBACK TRAN
	GOTO on_end

on_end:
	-- get ending purchase credit
	declare @pcend int
	select @pcend = balance from dbo.fn_Documents_getPurchaseCredits(@depomemberdataID)

	-- return #tmpCart
	SELECT *, @pcstart-@pcend as pcUsed
	FROM #tmpCart 
	where success = 1

	-- remove items from cart
	delete from dbo.documentCart
	where depomemberdataid = @depomemberdataID
	and cartID in (select cartID from #tmpCart where success = 1)
	
	-- drop temp table
	DROP TABLE #tmpCart

	RETURN
GO

	
ALTER PROCEDURE [dbo].[jbank_job_posting_transaction]
@controlnum varchar(30),
@linksource varchar(50),
@linkterms varchar(100),
@orgcode varchar(5),
@price money,
@salestax money,
@taxtableid int,
@caseref varchar(600),
@transactionID int OUTPUT

AS
BEGIN
	SET NOCOUNT ON

	BEGIN TRAN
		-- insert sale of jobbank
		INSERT INTO trialsmith.dbo.depoTransactions (depomemberdataid, Description, AmountBilled, AccountCode, 
			SourceState, salestaxamount, taxtableid, orgcode, linksource, linkterms, caseref, DocumentID)
		SELECT TOP 1 depomemberdataid, 'Job Bank Posting' + ' - ' + JBJ.JobTitle, @price,
			'5010', 'TS', @salestax, @taxtableid, @orgcode, @linksource, @linkterms, @caseref, @controlnum
		FROM dbo.JobBankJobs JBJ
		WHERE jobId = @controlnum
			IF @@ERROR <> 0 GOTO on_error

		-- get transaction id
		SELECT @transactionID = SCOPE_IDENTITY()


	COMMIT TRAN
	RETURN 1

	on_error:
		ROLLBACK TRAN
		SET @transactionID = 0
		RETURN 0

END
GO	
	


USE [uslegalForms]
GO
ALTER PROCEDURE [dbo].[up_buyForm]
@controlnum varchar(30),
@depomemberdataid int,
@billingstate varchar(5),
@linksource varchar(50),
@linkterms varchar(100),
@orgcode varchar(5),
@price money,
@salestax money,
@taxtableid int,
@caseref varchar(600),
@transactionID int OUTPUT

AS

SET NOCOUNT ON

BEGIN TRAN
	-- insert sale of form
	INSERT INTO trialsmith.dbo.depoTransactions (depomemberdataid, Description, AmountBilled, AccountCode, 
		SourceState, salestaxamount, taxtableid, orgcode, linksource, linkterms, caseref, transgroup)
	SELECT TOP 1 @depomemberdataid, dt.description + ' - ' + @controlnum, @price,
		dt.acctcode, 'TS', @salestax, @taxtableid, @orgcode, @linksource, @linkterms, @caseref, 0
	FROM trialsmith.dbo.DepoDocumentTypes dt
	WHERE dt.acctcode = '5007'
		IF @@ERROR <> 0 GOTO on_error

	-- get transaction id
	SELECT @transactionID = IDENT_CURRENT('trialsmith.dbo.depoTransactions')

	-- insert permissions	
	DECLARE @dateExpires datetime
	SELECT @dateExpires = dateadd(day, 30, getdate())
	INSERT INTO trialsmith.dbo.usLegalPermissions (depomemberDataID, controlNum, downloadLink, datePurchased, dateExpires)
	VALUES (@depoMemberDataID, @controlNum, 'http://secure.uslegalforms.com/cgi-bin/forms/v2-trialsmith.pl?' + @controlnum, getdate(), @dateExpires)
		IF @@ERROR <> 0 GOTO on_error

COMMIT TRAN
RETURN 1

on_error:
	ROLLBACK TRAN
	SET @transactionID = 0	
	RETURN 0
GO
	
	
USE [disiplinaryActions]
GO
ALTER PROCEDURE [dbo].[up_buyActions]
@uniqueNameID varchar(30),
@depomemberdataid int,
@billingstate varchar(5),
@linksource varchar(50),
@linkterms varchar(100),
@orgcode varchar(5),
@price money,
@salestax money,
@taxtableid int,
@caseref varchar(600),
@transactionID int OUTPUT

AS

SET NOCOUNT ON

BEGIN TRAN
	-- get name from unique name ID
	declare @fullName varchar(max)
	select @fullName = 
		isnull(salutation,'') + ' ' + 
		isnull(firstname,'') + ' ' + 
		isnull(middlename,'') + ' ' + 
		isnull(lastname,'') + ' ' + 
		isnull(suffix,'') + ' ' + 
		isnull(professionalsuffix,'')
	from dbo.uniqueNames
	where uniqueNameID = @uniqueNameID

	-- insert sale of actions
	INSERT INTO trialsmith.dbo.depoTransactions (depomemberdataid, Description, AmountBilled, AccountCode, 
		SourceState, salestaxamount, taxtableid, orgcode, linksource, linkterms, caseref, transgroup)
	SELECT TOP 1 @depomemberdataid, left(dt.description + ' - ' + @fullName,600), @price,
		dt.acctcode, 'TS', @salestax, @taxtableid, @orgcode, @linksource, @linkterms, @caseref, 0
	FROM trialsmith.dbo.DepoDocumentTypes dt
	WHERE dt.acctcode = '5011'
		IF @@ERROR <> 0 GOTO on_error

	-- get transaction id
	SELECT @transactionID = IDENT_CURRENT('trialsmith.dbo.depoTransactions')

	-- insert report to actions	
	DECLARE @reportID INT

	INSERT INTO trialsmith.dbo.da_purchasedReports (dateEntered, depomemberdataid, transactionID, uniqueNameID)
	VALUES (getdate(),@depomemberdataid,@transactionID,@uniqueNameID)
		IF @@ERROR <> 0 GOTO on_error
		SELECT @reportID = IDENT_CURRENT('trialsmith.dbo.da_purchasedReports')

	INSERT INTO trialsmith.dbo.da_purchasedReportItems (professionalID, reportID)
	SELECT professionalID, @reportID AS reportID
	FROM dbo.professionalUniqueNames 
	WHERE uniqueNameID = @uniqueNameID
		IF @@ERROR <> 0 GOTO on_error

COMMIT TRAN
RETURN 1

on_error:
	ROLLBACK TRAN
	SET @transactionID = 0	
	RETURN 0
GO

	
USE [seminarWeb]
GO
ALTER PROC [dbo].[swb_buyBundle]
@bundleID int,
@depomemberdataid int,
@billingstate varchar(5),
@linksource varchar(50),
@linkterms varchar(100),
@orgcode varchar(5),
@price money,
@salestax money,
@transactionID int OUTPUT

AS

SET NOCOUNT ON

BEGIN TRAN
	DECLARE @bundleName varchar(200), @taxTableID int
	SELECT top 1 @bundleName = b.bundlename 
		from dbo.tblBundles as b
		WHERE b.bundleID = @bundleID

	SELECT @taxTableID = trialsmith.dbo.fn_SalesTax_getTaxRule(@orgcode,@billingstate,'')

	-- insert sale
	INSERT INTO trialsmith.dbo.depoTransactions (depomemberdataid, Description, 
		AmountBilled, AccountCode, SourceState, salestaxamount, taxTableID, orgcode, linksource, linkterms,purchasedItemID,purchasedItemTableName)
	SELECT TOP 1 @depomemberdataid, dt.description + ' - ' + isNull(@bundleName,''), @price,
		dt.acctcode, 'TS', @salestax, @taxTableID, @orgcode, @linksource, @linkterms, @bundleID, 'SeminarWebBundle'
	FROM trialsmith.dbo.DepoDocumentTypes dt
	WHERE dt.acctcode = '7005'
		IF @@ERROR <> 0 GOTO on_error

	-- get transaction id
	SELECT @transactionID = IDENT_CURRENT('trialsmith.dbo.depoTransactions')

	-- return firstname, lastname
	SELECT firstname, lastname
	from trialsmith.dbo.depoMemberData
	where depomemberdataID = @depomemberdataid

COMMIT TRAN
RETURN 1

on_error:
	ROLLBACK TRAN
	RETURN 0	
GO
	
ALTER PROC [dbo].[swb_getPricesForBuyNow]
@bundleID int,
@catalogOrgCode varchar(5),
@billingstate varchar(5),
@depomemberdataid int,
@depomemberSiteGroups varchar(max)

AS

-- if orgcode of catalog doesnt match orgcode of bundle, return all synd prices
IF not exists (SELECT b.bundleID
			   FROM dbo.tblBundles AS b 
			   INNER JOIN dbo.tblParticipants AS p ON b.participantID = p.participantID
			   WHERE b.bundleID = @bundleID
			   AND p.orgcode = @catalogOrgCode)
	BEGIN
	
	DECLARE @pricegroup xml
	SELECT @pricegroup = pricesyndication
	FROM dbo.tblBundles AS b
		INNER JOIN dbo.tblBundledItems bi on b.bundleID = bi.bundleID
		INNER JOIN dbo.tblSeminars as s on s.seminarID = bi.seminarID
	WHERE b.bundleID = @bundleID 
	AND s.isPublished = 1
	AND s.isDeleted = 0
	AND b.allowSyndication = 1
	--AND GETDATE() BETWEEN sod.dateCatalogStart AND sod.dateCatalogEnd

	SELECT isnull(tblValues.col.value('price[1]','money'),-1) as price, 
		trialsmith.dbo.fn_SalesTax_getTax(@catalogOrgCode,isnull(tblValues.col.value('price[1]','money'),0),@billingstate,'') as salesTax,
		trialsmith.dbo.fn_SalesTax_getTaxRule(@catalogOrgCode,@billingstate,'') as taxTableID,
		tblValues.col.value('group[1]','VARCHAR(20)') as [description]
	FROM @pricegroup.nodes('/syndication/pricegroup') as tblValues(col) 	
	ORDER BY price, [description]

	END
	
-- else return all prices (if necessary) or just best price based on group
ELSE
BEGIN

	DECLARE @useActual bit
	SELECT @useActual = isPriceBasedOnActual from dbo.tblBundles where bundleID = @bundleID

	IF @useActual = 1 AND @depomemberdataid > 0
	BEGIN
		DECLARE @price numeric(18,2)
		SELECT @price = dbo.sw_getPriceForBundle(@bundleID,@catalogOrgCode,@depomemberdataid,@depomemberSiteGroups)

		-- 4/17/07 tl - use catalogOrgCode's tax rules not Trialsmith's
		DECLARE @salestax numeric(18,2)
		SELECT @salestax = trialsmith.dbo.fn_SalesTax_getTax(@catalogOrgCode,@price,@billingstate,'')

		DECLARE @taxtableid int
		SELECT @taxtableid = trialsmith.dbo.fn_SalesTax_getTaxRule(@catalogOrgCode,@billingstate,'')

		SELECT @price as price, @salestax as SalesTax, @taxtableid as taxTableID, 'Best Rate' as description
	END

	ELSE
		SELECT bag.price, trialsmith.dbo.fn_SalesTax_getTax(@catalogOrgCode,bag.price,@billingstate,'') as salesTax, 
				trialsmith.dbo.fn_SalesTax_getTaxRule(@catalogOrgCode,@billingstate,'') as salesTax, replace(g.description,'Guest Group','Non-members') as description
		FROM dbo.tblBundlesAndGroups as bag
		INNER JOIN trialsmith.dbo.usergroups as g on g.usergroupid = bag.groupID
		WHERE bag.bundleID = @bundleID
		ORDER BY description

END
GO

ALTER PROC [dbo].[swb_getPricesForCatalog]
@bundleID int,
@catalogOrgCode varchar(5),
@billingstate varchar(5)

AS

-- if orgcode of catalog doesnt match orgcode of bundle, return bundle syndication price
IF not exists (SELECT b.bundleID
			   FROM dbo.tblBundles AS b 
			   INNER JOIN dbo.tblParticipants AS p ON b.participantID = p.participantID
			   WHERE b.bundleID = @bundleID
			   AND p.orgcode = @catalogOrgCode)
	--SELECT -1 as price, 0 as salesTax, '' as description
	BEGIN
	DECLARE @pricegroup xml

	SELECT @pricegroup = pricesyndication
		FROM dbo.tblBundles AS b
		INNER JOIN dbo.tblBundledItems bi on bi.bundleID = b.bundleID
		INNER JOIN dbo.tblSeminars as s on s.seminarID = bi.seminarID
		WHERE b.bundleID = @bundleID 
		AND s.isPublished = 1
		AND s.isDeleted = 0
		AND b.allowSyndication = 1

	SELECT isnull(tblValues.col.value('price[1]','money'),-1) as price, 
		trialsmith.dbo.fn_SalesTax_getTax(@catalogOrgCode,isnull(tblValues.col.value('price[1]','money'),0),@billingstate,'') as salesTax,
		trialsmith.dbo.fn_SalesTax_getTaxRule(@catalogOrgCode,@billingstate,'') as taxTableID,
		tblValues.col.value('group[1]','VARCHAR(20)') as [description]
	FROM @pricegroup.nodes('/syndication/pricegroup') as tblValues(col) 	
	ORDER BY price, [description]
	END
-- else return all prices with showOnDetail = 1
ELSE
	SELECT bag.price, trialsmith.dbo.fn_SalesTax_getTax(@catalogOrgCode,bag.price,@billingstate,'') as salesTax
		, trialsmith.dbo.fn_SalesTax_getTaxRule(@catalogOrgCode,@billingstate,'') as taxTableID, replace(g.description,'Guest Group','Non-members') as description
	FROM dbo.tblBundlesAndGroups as bag
	INNER JOIN trialsmith.dbo.usergroups as g on g.usergroupid = bag.groupID
	WHERE bag.bundleID = @bundleID
	AND bag.showOnDetail = 1
	ORDER BY description
GO

ALTER PROCEDURE [dbo].[swl_buySeminar]
@seminarID int,
@depomemberdataid int,
@billingstate varchar(5),
@linksource varchar(50),
@linkterms varchar(100),
@orgcode varchar(5),
@price money,
@salestax money,
@transactionID int OUTPUT

AS

SET NOCOUNT ON

BEGIN TRAN
	DECLARE @seminarName varchar(250), @taxTableID int
	DECLARE @gotomeetingID bigint
	SELECT top 1 @seminarName = s.seminarname, @gotomeetingID = swl.gotoMeetingID
		from dbo.tblSeminars s
		inner join dbo.tblSeminarsSWLive swl on swl.seminarID = s.seminarID
		WHERE s.seminarID = @seminarID

	SELECT @taxTableID = trialsmith.dbo.fn_SalesTax_getTaxRule(@orgcode,@billingstate,'')

	-- insert sale
	INSERT INTO trialsmith.dbo.depoTransactions (depomemberdataid, Description, 
		AmountBilled, AccountCode, SourceState, salestaxamount, taxTableID, orgcode, linksource, linkterms,purchasedItemID,purchasedItemTableName)
	SELECT TOP 1 @depomemberdataid, dt.description + ' - ' + isNull(@seminarName,''), @price,
		dt.acctcode, 'TS', @salestax, @taxTableID, @orgcode, @linksource, @linkterms, @seminarID, 'SeminarWebLive'
	FROM trialsmith.dbo.DepoDocumentTypes dt
	WHERE dt.acctcode = '7001'
		IF @@ERROR <> 0 GOTO on_error

	-- get transaction id
	SELECT @transactionID = IDENT_CURRENT('trialsmith.dbo.depoTransactions')

	-- return firstname, lastname
	SELECT firstname, lastname, @gotomeetingID as gotoMeetingID
	from trialsmith.dbo.depoMemberData
	where depomemberdataID = @depomemberdataid

COMMIT TRAN
RETURN 1

on_error:
	ROLLBACK TRAN
	RETURN 0	
GO

ALTER PROC [dbo].[swl_getPricesForBuyNow]
@seminarID int,
@catalogOrgCode varchar(5),
@billingstate varchar(5),
@depomemberdataid int,
@depomemberSiteGroups varchar(max)

AS

-- if orgcode of catalog doesnt match orgcode of seminar, seminar is syndicated. return the synd price
IF not exists (SELECT swl.liveID
				FROM dbo.tblSeminarsSWLive AS swl
				INNER JOIN dbo.tblSeminars as s on s.seminarID = swl.seminarID
				INNER JOIN dbo.tblParticipants AS p ON s.participantID = p.participantID
				WHERE s.seminarID = @seminarID 
				AND p.orgcode = @catalogOrgCode)
	BEGIN
		DECLARE @pricegroup xml
		SELECT @pricegroup = swl.pricesyndication
			FROM dbo.tblSeminarsSWLive AS swl
			INNER JOIN dbo.tblSeminars as s on s.seminarID = swl.seminarID
			WHERE s.seminarID = @seminarID 
			AND s.isPublished = 1
			AND s.isDeleted = 0

		SELECT isnull(tblValues.col.value('price[1]','money'),-1) as price, 
			trialsmith.dbo.fn_SalesTax_getTax(@catalogOrgCode,isnull(tblValues.col.value('price[1]','money'),0),@billingstate,'') as salesTax,
			trialsmith.dbo.fn_SalesTax_getTaxRule(@catalogOrgCode,@billingstate,'') as taxTableID,
			tblValues.col.value('group[1]','VARCHAR(20)') as [description]
		FROM @pricegroup.nodes('/syndication/pricegroup') as tblValues(col) 	
		ORDER BY price, [description]
	END
	
-- else return all prices (if necessary) or just best price based on group
ELSE
BEGIN

	DECLARE @useActual bit
	SELECT @useActual = isPriceBasedOnActual from dbo.tblSeminars where seminarID = @seminarID

	IF @useActual = 1 AND @depomemberdataid > 0
	BEGIN
		DECLARE @price numeric(18,2)
		SELECT @price = dbo.swl_getPriceForSeminar(@seminarID,@catalogOrgCode,@depomemberdataid,@depomemberSiteGroups)

		-- 4/17/07 tl - use catalogOrgCode's tax rules not Trialsmith's
		DECLARE @salestax numeric(18,2)
		SELECT @salestax = trialsmith.dbo.fn_SalesTax_getTax(@catalogOrgCode,@price,@billingstate,'')

		DECLARE @taxtableid int
		SELECT @taxtableid = trialsmith.dbo.fn_SalesTax_getTaxRule(@catalogOrgCode,@billingstate,'')

		SELECT @price as price, @salestax as SalesTax, @taxtableid as taxTableID, 'Best Rate' as description
	END

	ELSE
		SELECT sag.price, trialsmith.dbo.fn_SalesTax_getTax(@catalogOrgCode,sag.price,@billingstate,'') as salesTax
			, trialsmith.dbo.fn_SalesTax_getTaxRule(@catalogOrgCode,@billingstate,'') as taxTableID, replace(g.description,'Guest Group','Non-members') as description
		FROM dbo.tblSeminarsAndGroups as sag
		INNER JOIN trialsmith.dbo.usergroups as g on g.usergroupid = sag.groupID
		WHERE sag.seminarID = @seminarID
		ORDER BY price, description

END
GO

ALTER PROC [dbo].[swl_getPricesForCatalog]
@seminarID int,
@catalogOrgCode varchar(5),
@billingstate varchar(5)

AS

-- if orgcode of catalog doesnt match orgcode of seminar, seminar is syndicated. return all synd prices
IF not exists (SELECT swl.liveID 
				FROM dbo.tblSeminarsSWLive AS swl 
				INNER JOIN dbo.tblSeminars as s on s.seminarID = swl.seminarID
				INNER JOIN dbo.tblParticipants AS p ON s.participantID = p.participantID
				WHERE s.seminarID = @seminarID 
				AND p.orgcode = @catalogOrgCode)
	BEGIN
		DECLARE @pricegroup xml
		SELECT @pricegroup = swl.pricesyndication
			FROM dbo.tblSeminarsSWLive AS swl
			INNER JOIN dbo.tblSeminars as s on s.seminarID = swl.seminarID
			WHERE s.seminarID = @seminarID 
			AND s.isPublished = 1
			AND s.isDeleted = 0

		SELECT isnull(tblValues.col.value('price[1]','money'),-1) as price, 
			trialsmith.dbo.fn_SalesTax_getTax(@catalogOrgCode,isnull(tblValues.col.value('price[1]','money'),0),@billingstate,'') as salesTax,
			trialsmith.dbo.fn_SalesTax_getTaxRule(@catalogOrgCode,@billingstate,'') as taxTableID,
			tblValues.col.value('group[1]','VARCHAR(20)') as [description]
		FROM @pricegroup.nodes('/syndication/pricegroup') as tblValues(col) 	
		ORDER BY price, [description]
	END

-- else return all prices with showOnDetail = 1
ELSE
	SELECT sag.price, trialsmith.dbo.fn_SalesTax_getTax(@catalogOrgCode,sag.price,@billingstate,'') as salesTax,
			trialsmith.dbo.fn_SalesTax_getTaxRule(@catalogOrgCode,@billingstate,'') as taxTableID, replace(g.description,'Guest Group','Non-members') as description
	FROM dbo.tblSeminarsAndGroups as sag
	INNER JOIN trialsmith.dbo.usergroups as g on g.usergroupid = sag.groupID
	WHERE sag.seminarID = @seminarID
	AND sag.showOnDetail = 1
	ORDER BY price, description
GO

ALTER PROCEDURE [dbo].[swod_buySeminar]
@seminarID int,
@depomemberdataid int,
@billingstate varchar(5),
@linksource varchar(50),
@linkterms varchar(100),
@orgcode varchar(5),
@price money,
@salestax money,
@transactionID int OUTPUT

AS

SET NOCOUNT ON

BEGIN TRAN
	DECLARE @seminarName varchar(250), @taxTableID int
	SELECT top 1 @seminarName = s.seminarname 
		from dbo.tblSeminars s
		WHERE s.seminarID = @seminarID

	SELECT @taxTableID = trialsmith.dbo.fn_SalesTax_getTaxRule(@orgcode,@billingstate,'')

	-- insert sale
	INSERT INTO trialsmith.dbo.depoTransactions (depomemberdataid, Description, 
		AmountBilled, AccountCode, SourceState, salestaxamount, taxTableID, orgcode, linksource, linkterms,purchasedItemID,purchasedItemTableName)
	SELECT TOP 1 @depomemberdataid, dt.description + ' - ' + isNull(@seminarName,''), @price,
		dt.acctcode, 'TS', @salestax, @taxTableID, @orgcode, @linksource, @linkterms, @seminarID, 'SeminarWebOnDemand'
	FROM trialsmith.dbo.DepoDocumentTypes dt
	WHERE dt.acctcode = '7000'
		IF @@ERROR <> 0 GOTO on_error

	-- get transaction id
	SELECT @transactionID = IDENT_CURRENT('trialsmith.dbo.depoTransactions')

	-- return firstname, lastname
	SELECT firstname, lastname
	from trialsmith.dbo.depoMemberData
	where depomemberdataID = @depomemberdataid

COMMIT TRAN
RETURN 1

on_error:
	ROLLBACK TRAN
	RETURN 0	
GO
	
ALTER PROC [dbo].[swod_getPricesForBuyNow]
@seminarID int,
@catalogOrgCode varchar(5),
@billingstate varchar(5),
@depomemberdataid int,
@depomemberSiteGroups varchar(max)

AS

-- if orgcode of catalog doesnt match orgcode of seminar, seminar is syndicated. return all synd prices
IF not exists (SELECT sod.onDemandID 
				FROM dbo.tblSeminarsSWOD AS sod 
				INNER JOIN dbo.tblSeminars as s on s.seminarID = sod.seminarID
				INNER JOIN dbo.tblParticipants AS p ON s.participantID = p.participantID
				WHERE sod.seminarID = @seminarID 
				AND p.orgcode = @catalogOrgCode)
	BEGIN
		DECLARE @pricegroup xml
		SELECT @pricegroup = pricesyndication
			FROM dbo.tblSeminarsSWOD AS sod
			INNER JOIN dbo.tblSeminars as s on s.seminarID = sod.seminarID
			WHERE sod.seminarID = @seminarID 
			AND s.isPublished = 1
			AND s.isDeleted = 0
			AND sod.allowSyndication = 1
			AND GETDATE() BETWEEN sod.dateCatalogStart AND sod.dateCatalogEnd

		SELECT isnull(tblValues.col.value('price[1]','money'),-1) as price, 
			trialsmith.dbo.fn_SalesTax_getTax(@catalogOrgCode,isnull(tblValues.col.value('price[1]','money'),0),@billingstate,'') as salesTax,
			trialsmith.dbo.fn_SalesTax_getTaxRule(@catalogOrgCode,@billingstate,'') as taxTableID,
			tblValues.col.value('group[1]','VARCHAR(20)') as [description]
		FROM @pricegroup.nodes('/syndication/pricegroup') as tblValues(col) 	
		ORDER BY price, [description]
	END
	
-- else return all prices (if necessary) or just best price based on group
ELSE
BEGIN

	DECLARE @useActual bit
	SELECT @useActual = isPriceBasedOnActual from dbo.tblSeminars where seminarID = @seminarID

	IF @useActual = 1 AND @depomemberdataid > 0
	BEGIN
		DECLARE @price numeric(18,2)
		SELECT @price = dbo.swod_getPriceForSeminar(@seminarID,@catalogOrgCode,@depomemberdataid,@depomemberSiteGroups)

		-- 4/17/07 tl - use catalogOrgCode's tax rules not Trialsmith's
		DECLARE @salestax numeric(18,2)
		SELECT @salestax = trialsmith.dbo.fn_SalesTax_getTax(@catalogOrgCode,@price,@billingstate,'')

		DECLARE @taxtableid int
		SELECT @taxtableid = trialsmith.dbo.fn_SalesTax_getTaxRule(@catalogOrgCode,@billingstate,'')

		SELECT @price as price, @salestax as SalesTax, @taxtableid as taxTableID, 'Best Rate' as description
	END

	ELSE
		SELECT sag.price, trialsmith.dbo.fn_SalesTax_getTax(@catalogOrgCode,sag.price,@billingstate,'') as salesTax,
				trialsmith.dbo.fn_SalesTax_getTaxRule(@catalogOrgCode,@billingstate,'') as taxTableID, replace(g.description,'Guest Group','Non-members') as description
		FROM dbo.tblSeminarsAndGroups as sag
		INNER JOIN trialsmith.dbo.usergroups as g on g.usergroupid = sag.groupID
		WHERE sag.seminarID = @seminarID
		ORDER BY description

END
GO

ALTER PROC [dbo].[swod_getPricesForCatalog]
@seminarID int,
@catalogOrgCode varchar(5),
@billingstate varchar(5)

AS

-- if orgcode of catalog doesnt match orgcode of seminar, seminar is syndicated. return all synd prices
IF not exists (SELECT sod.onDemandID 
				FROM dbo.tblSeminarsSWOD AS sod 
				INNER JOIN dbo.tblSeminars as s on s.seminarID = sod.seminarID
				INNER JOIN dbo.tblParticipants AS p ON s.participantID = p.participantID
				WHERE sod.seminarID = @seminarID 
				AND p.orgcode = @catalogOrgCode)
	BEGIN
		DECLARE @pricegroup xml
		SELECT @pricegroup = pricesyndication
			FROM dbo.tblSeminarsSWOD AS sod
			INNER JOIN dbo.tblSeminars as s on s.seminarID = sod.seminarID
			WHERE sod.seminarID = @seminarID 
			AND s.isPublished = 1
			AND s.isDeleted = 0
			AND sod.allowSyndication = 1
			AND GETDATE() BETWEEN sod.dateCatalogStart AND sod.dateCatalogEnd

		SELECT isnull(tblValues.col.value('price[1]','money'),-1) as price, 
			trialsmith.dbo.fn_SalesTax_getTax(@catalogOrgCode,isnull(tblValues.col.value('price[1]','money'),0),@billingstate,'') as salesTax,
			trialsmith.dbo.fn_SalesTax_getTaxRule(@catalogOrgCode,@billingstate,'') as taxTableID,
			tblValues.col.value('group[1]','VARCHAR(20)') as [description]
		FROM @pricegroup.nodes('/syndication/pricegroup') as tblValues(col) 	
		ORDER BY price, [description]
	END

-- else return all prices with showOnDetail = 1
ELSE
	SELECT sag.price, trialsmith.dbo.fn_SalesTax_getTax(@catalogOrgCode,sag.price,@billingstate,'') as salesTax, 
			trialsmith.dbo.fn_SalesTax_getTaxRule(@catalogOrgCode,@billingstate,'') as taxTableID, replace(g.description,'Guest Group','Non-members') as description
	FROM dbo.tblSeminarsAndGroups as sag
	INNER JOIN trialsmith.dbo.usergroups as g on g.usergroupid = sag.groupID
	WHERE sag.seminarID = @seminarID
	AND sag.showOnDetail = 1
	ORDER BY price, [description]
GO

ALTER PROCEDURE [dbo].[swtl_buyTitle]
@titleID int,
@depomemberdataid int,
@billingstate varchar(5),
@linksource varchar(50),
@linkterms varchar(100),
@orgcode varchar(5),
@price money,
@salestax money,
@transactionID int OUTPUT

AS

SET NOCOUNT ON

BEGIN TRAN
	DECLARE @titleName varchar(250), @taxTableID int
	SELECT top 1 @titleName = t.titlename 
		from dbo.tblTitles t
		WHERE t.titleID = @titleID

	SELECT @taxTableID = trialsmith.dbo.fn_SalesTax_getTaxRule(@orgcode,@billingstate,'')

	-- insert sale
	INSERT INTO trialsmith.dbo.depoTransactions (depomemberdataid, Description, 
		AmountBilled, AccountCode, SourceState, salestaxamount, taxTableID, orgcode, linksource, linkterms,purchasedItemID,purchasedItemTableName)
	SELECT TOP 1 @depomemberdataid, dt.description + ' - ' + isNull(@titleName,''), @price,
		dt.acctcode, 'TS', @salestax, @taxTableID, @orgcode, @linksource, @linkterms, @titleID, 'SeminarWebTitle'
	FROM trialsmith.dbo.DepoDocumentTypes dt
	WHERE dt.acctcode = '7003'
		IF @@ERROR <> 0 GOTO on_error

	-- get transaction id
	SELECT @transactionID = IDENT_CURRENT('trialsmith.dbo.depoTransactions')

	-- return firstname, lastname
	SELECT firstname, lastname
	from trialsmith.dbo.depoMemberData
	where depomemberdataID = @depomemberdataid

COMMIT TRAN
RETURN 1

on_error:
	ROLLBACK TRAN
	RETURN 0
GO	
	
ALTER PROC [dbo].[swtl_getPricesForBuyNow]
@titleID int,
@catalogOrgCode varchar(5),
@billingstate varchar(5),
@depomemberdataid int,
@depomemberSiteGroups varchar(max)

AS

-- if orgcode of catalog doesnt match orgcode of title, title is syndicated. return all synd prices
IF not exists (SELECT t.titleID 
				FROM dbo.tblTitles AS t 
				INNER JOIN dbo.tblParticipants AS p ON t.participantID = p.participantID
				WHERE t.titleID = @titleID
				AND p.orgcode = @catalogOrgCode)
	BEGIN
		DECLARE @pricegroup xml
		SELECT @pricegroup = t.pricesyndication
			FROM dbo.tblTitles AS t
			WHERE t.titleID = @titleID 
			AND t.isPublished = 1
			AND t.allowSyndication = 1
			AND GETDATE() BETWEEN t.dateCatalogStart AND t.dateCatalogEnd

		SELECT isnull(tblValues.col.value('price[1]','money'),-1) as price, 
			trialsmith.dbo.fn_SalesTax_getTax(@catalogOrgCode,isnull(tblValues.col.value('price[1]','money'),0),@billingstate,'') as salesTax,
			trialsmith.dbo.fn_SalesTax_getTaxRule(@catalogOrgCode,@billingstate,'') as taxTableID,
			tblValues.col.value('group[1]','VARCHAR(20)') as [description]
		FROM @pricegroup.nodes('/syndication/pricegroup') as tblValues(col) 	
		ORDER BY price, [description]
	END

-- else return all prices (if necessary) or just best price based on group
ELSE
BEGIN

	DECLARE @useActual bit
	SELECT @useActual = isPriceBasedOnActual from dbo.tblTitles where titleID = @titleID

	IF @useActual = 1 AND @depomemberdataid > 0
	BEGIN
		DECLARE @price numeric(18,2)
		SELECT @price = dbo.swtl_getPriceForTitle(@titleID,@catalogOrgCode,@depomemberdataid,@depomemberSiteGroups)

		-- 4/17/07 tl - use catalogOrgCode's tax rules not Trialsmith's
		DECLARE @salestax numeric(18,2)
		SELECT @salestax = trialsmith.dbo.fn_SalesTax_getTax(@catalogOrgCode,@price,@billingstate,'')

		DECLARE @taxtableid int
		SELECT @taxtableid = trialsmith.dbo.fn_SalesTax_getTaxRule(@catalogOrgCode,@billingstate,'')

		SELECT @price as price, @salestax as SalesTax, @taxtableid as taxTableID, 'Best Rate' as description
	END

	ELSE
		SELECT tag.price, trialsmith.dbo.fn_SalesTax_getTax(@catalogOrgCode,tag.price,@billingstate,'') as salesTax,
				trialsmith.dbo.fn_SalesTax_getTaxRule(@catalogOrgCode,@billingstate,'') as taxTableID, replace(g.description,'Guest Group','Non-members') as description
		FROM dbo.tblTitlesAndGroups as tag
		INNER JOIN trialsmith.dbo.usergroups as g on g.usergroupid = tag.groupID
		WHERE tag.titleID = @titleID
		ORDER BY description

END
GO

ALTER PROC [dbo].[swtl_getPricesForCatalog]
@titleID int,
@catalogOrgCode varchar(5),
@billingstate varchar(5)

AS

-- if orgcode of catalog doesnt match orgcode of title, title is syndicated. return all synd prices
IF not exists (SELECT t.titleID 
				FROM dbo.tblTitles AS t 
				INNER JOIN dbo.tblParticipants AS p ON t.participantID = p.participantID
				WHERE t.titleID = @titleID
				AND p.orgcode = @catalogOrgCode)
	BEGIN
		DECLARE @pricegroup xml
		SELECT @pricegroup = t.pricesyndication
			FROM dbo.tblTitles AS t
			WHERE t.titleID = @titleID 
			AND t.isPublished = 1
			AND t.allowSyndication = 1
			AND GETDATE() BETWEEN t.dateCatalogStart AND t.dateCatalogEnd

		SELECT isnull(tblValues.col.value('price[1]','money'),-1) as price, 
			trialsmith.dbo.fn_SalesTax_getTax(@catalogOrgCode,isnull(tblValues.col.value('price[1]','money'),0),@billingstate,'') as salesTax,
			trialsmith.dbo.fn_SalesTax_getTaxRule(@catalogOrgCode,@billingstate,'') as taxTableID,
			tblValues.col.value('group[1]','VARCHAR(20)') as [description]
		FROM @pricegroup.nodes('/syndication/pricegroup') as tblValues(col) 	
		ORDER BY price, [description]
	END

-- else return all prices with showOnDetail = 1
ELSE
	SELECT tag.price, trialsmith.dbo.fn_SalesTax_getTax(@catalogOrgCode,tag.price,@billingstate,'') as salesTax,
			trialsmith.dbo.fn_SalesTax_getTaxRule(@catalogOrgCode,@billingstate,'') as taxTableID, replace(g.description,'Guest Group','Non-members') as description
	FROM dbo.tblTitlesAndGroups as tag
	INNER JOIN trialsmith.dbo.usergroups as g on g.usergroupid = tag.groupID
	WHERE tag.titleID = @titleID
	AND tag.showOnDetail = 1
	ORDER BY price, [description]
GO

