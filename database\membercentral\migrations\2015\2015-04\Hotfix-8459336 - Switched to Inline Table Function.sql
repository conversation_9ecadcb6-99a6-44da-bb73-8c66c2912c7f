use membercentral
GO

DROP FUNCTION dbo.fn_tr_transactionSalesWithDIT
GO

CREATE FUNCTION [dbo].[fn_tr_transactionSalesWithDIT] (
	@saleTransactionID int
) 
RETURNS TABLE 
AS
RETURN 
(

	WITH tblHold AS (
		select transactionID
		from dbo.tr_transactions
		WHERE transactionID = @saleTransactionID
		AND typeID in (1,3,7,10)
			union all
		select adj.transactionID
		from dbo.tr_transactions as adj
		inner join dbo.tr_relationships as r on r.transactionID = adj.transactionID
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
		inner join tblHold as tbl on tbl.transactionID = r.appliedToTransactionID
			union all
		select dit.transactionID
		from dbo.tr_transactions as dit
		inner join dbo.tr_relationships as r on r.transactionID = dit.transactionID
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
		inner join tblHold as tbl on tbl.transactionID = r.appliedToTransactionID
	)
	select @saleTransactionID as transactionID, 
		sum(cache_amountAfterAdjustment) as cache_amountAfterAdjustment,
		sum(cache_activePaymentAllocatedAmount) as cache_activePaymentAllocatedAmount,
		sum(cache_pendingPaymentAllocatedAmount) as cache_pendingPaymentAllocatedAmount
	from tblHold as tbl
	inner join dbo.tr_transactionSales as ts on ts.transactionID = tbl.transactionID

)
GO
