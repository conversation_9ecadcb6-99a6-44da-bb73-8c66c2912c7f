USE seminarweb 
GO

IF (OBJECT_ID('sw_optInProgram') IS NOT NULL)
  DROP PROCEDURE sw_optInProgram
GO

CREATE PROCEDURE [dbo].[sw_optInProgram]
@orgcode varchar(5),
@seminarID int,
@programID int,
@isSWL bit,
@isSWOD bit

AS

	if @isSWL =  1 begin
		insert into dbo.tblSeminarsOptIn (seminarID, participantID)
		select 
			@seminarID, p.participantID
		from 
			dbo.tblParticipants  p 
			inner join trialsmith.dbo.depoTLA tla on 
				tla.State = p.orgcode 
			inner join 	dbo.tblNationalProgramParticipants  npp on
				npp.participantID = p.participantID
				and npp.programID = @programID	
				and p.isSWL = 1
			left outer join dbo.tblSeminarsOptIn  soi on  
				p.participantID = soi.participantID 
				and soi.seminarID = @seminarID
		where 
			soi.optinID is null
		RETURN
	end

	if @isSWOD =  1 begin
		insert into dbo.tblSeminarsOptIn (seminarID, participantID)
		select 
			@seminarID, p.participantID
		from 
			dbo.tblParticipants  p 
			inner join trialsmith.dbo.depoTLA tla on 
				tla.State = p.orgcode 
				and p.orgcode <> @orgcode
			inner join 	dbo.tblNationalProgramParticipants  npp on
				npp.participantID = p.participantID
				and npp.programID = @programID	
				and p.isSWOD = 1
			left outer join dbo.tblSeminarsOptIn  soi on  
				p.participantID = soi.participantID 
				and soi.seminarID = @seminarID
		where 
			soi.optinID is null
		RETURN
	end

GO

IF (OBJECT_ID('sw_optInProgramBundle') IS NOT NULL)
  DROP PROCEDURE sw_optInProgramBundle
GO

CREATE PROCEDURE dbo.sw_optInProgramBundle
@orgcode varchar(5),
@bundleID int,
@programID int

AS
	INSERT INTO dbo.tblBundlesOptIn (bundleID, participantID)
	select 
		@bundleID, p.participantID
	from 
		dbo.tblParticipants p 
		inner join trialsmith.dbo.depoTLA tla on 
			tla.State = p.orgcode 
			and p.orgcode <> @orgcode
		inner join 	dbo.tblNationalProgramParticipants  npp on
			npp.participantID = p.participantID
			and npp.programID = @programID
			and p.isSWOD = 1
		left outer join  dbo.tblBundlesOptIn boi on 
			p.participantID = boi.participantID 
			and boi.bundleID = @bundleID
	where 
		boi.bundleOptInID is null

	RETURN

GO