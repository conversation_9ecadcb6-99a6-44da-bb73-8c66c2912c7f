CREATE AGGREGATE [dbo].[sortedIntList]
(@value [int])
RETURNS[nvarchar](4000)
EXTERNAL NAME [SortedIntAggregates].[sortedIntList]
GO
EXEC sys.sp_addextendedproperty @name=N'AutoDeployed', @value=N'yes' , @level0type=N'ASSEMBLY',@level0name=N'CustomAggregates'
GO
EXEC sys.sp_addextendedproperty @name=N'SqlAssemblyProjectRoot', @value=N'\\Devserver\e$\ConsoleDevelopment\SQLCLR\CustomAggregates\CustomAggregates' , @level0type=N'ASSEMBLY',@level0name=N'CustomAggregates'
GO
EXEC sys.sp_addextendedproperty @name=N'AutoDeployed', @value=N'yes' , @level0type=N'ASSEMBLY',@level0name=N'SortedIntAggregates'
GO
EXEC sys.sp_addextendedproperty @name=N'SqlAssemblyProjectRoot', @value=N'C:\git-repos\ConsoleDevelopment\SQLCLR\SortedIntAggregates\SortedIntAggregates' , @level0type=N'ASSEMBLY',@level0name=N'SortedIntAggregates'
GO
EXEC sys.sp_addextendedproperty @name=N'AutoDeployed', @value=N'yes' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'AGGREGATE',@level1name=N'PipeList'
GO
EXEC sys.sp_addextendedproperty @name=N'SqlAssemblyFile', @value=N'pipeList.cs' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'AGGREGATE',@level1name=N'PipeList'
GO
EXEC sys.sp_addextendedproperty @name=N'SqlAssemblyFileLine', @value=N'8' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'AGGREGATE',@level1name=N'PipeList'
GO
EXEC sys.sp_addextendedproperty @name=N'AutoDeployed', @value=N'yes' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'AGGREGATE',@level1name=N'sortedIntList'
GO
EXEC sys.sp_addextendedproperty @name=N'SqlAssemblyFile', @value=N'sortedIntList.cs' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'AGGREGATE',@level1name=N'sortedIntList'
GO
EXEC sys.sp_addextendedproperty @name=N'SqlAssemblyFileLine', @value=8 , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'AGGREGATE',@level1name=N'sortedIntList'
GO
