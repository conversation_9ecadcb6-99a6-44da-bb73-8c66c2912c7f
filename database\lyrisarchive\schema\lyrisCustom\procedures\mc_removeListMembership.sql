ALTER PROC dbo.mc_removeListMembership
@siteID int,
@memberID_ int,
@listName varchar(60),
@externalMemberID varchar(100),
@receiverMemberID int,
@recordedByMemberID int,
@resultCount int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @emailAddr varchar(100);

	SET @resultCount = 0;

	IF EXISTS (
		SELECT memberid_
		FROM trialslyris1.dbo.members_ 
		WHERE memberid_ = @memberID_ 
		AND list_ = @listName 
		AND externalMemberID = @externalMemberID
	) BEGIN
		SELECT @emailAddr = emailaddr_
		FROM trialslyris1.dbo.members_
		WHERE memberid_ = @memberID_;

		DELETE FROM trialslyris1.dbo.members_
		WHERE memberid_ = @memberID_;

		INSERT INTO memberCentral.platformQueue.dbo.queue_mongo (msgjson)
		SELECT '{ "c":"historyEntries_SYS_ADMIN_LISTUPDATE", "d": { "HISTORYCODE":"SYS_ADMIN_LISTUPDATE", "SITEID":' + cast(@siteID as varchar(10)) + 
			', "ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + 
			', "RECEIVERMEMBERID":' + cast(@receiverMemberID as varchar(10)) + 
			', "MAINMESSAGE":"List Membership Removed", "LISTNAME":"'+ @listName +'"'+
			', "MESSAGES":[ "Email ['+ @emailAddr +'] has been removed from the list." ]'+
			', "UPDATEDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '"'+
			' } }';

		SET @resultCount = 1;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET @resultCount = 0;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
