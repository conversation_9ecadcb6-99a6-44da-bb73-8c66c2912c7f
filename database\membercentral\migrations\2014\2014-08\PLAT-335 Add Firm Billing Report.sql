use membercentral
GO
declare @toolTypeID int, @toolResourceTypeID int, @level3NavID int, @navigationID int, @resourceTypeFunctionID int, @siteID int
set @level3NavID = 161
SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('Admin'),dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('Admin')))

EXEC dbo.createAdminToolType @toolType='FirmBillingReport', @toolCFC='Reports.subscriptions.FirmBilling', @toolDesc='Firm Billing', @toolTypeID=@toolTypeID OUTPUT, @resourceTypeID=@toolResourceTypeID OUTPUT
	EXEC dbo.createAdminNavigation @navName='Firm Billing', @navDesc='Allows user to produce subscription offers and statements grouped by firm.', @parentNavigationID=@level3NavID, @navAreaID=4, @cfcMethod='showReport', @isHeader=0, @showInNav=1, @navigationID=@navigationID OUTPUT
		EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID=@resourceTypeFunctionID, @toolTypeID=@toolTypeID, @navigationID=@navigationID

insert into dbo.admin_siteToolRestrictions (toolTypeID, siteID)
select @toolTypeID, siteID
from dbo.admin_siteToolRestrictions as stre
inner join dbo.admin_toolTypes as tt on tt.toolTypeID = stre.toolTypeID
where tt.tooltype = 'CreditsByProgramReport'

select @siteID = min(siteID) from dbo.sites
while @siteID is not null BEGIN
	exec dbo.createAdminSuite @siteID
	select @siteID = min(siteID) from dbo.sites where siteID > @siteID
END
GO

-- Remove firm billing tool
-- Do this after report is up and running.

