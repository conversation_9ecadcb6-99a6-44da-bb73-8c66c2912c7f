/*
CHECKS
*/
DECLARE @errCode int
EXEC membercentral.dbo.migrate_check_members @orgcode='WV', @activeOnly=0, @checkCode=@errCode OUTPUT
select @errCode

/*
GROUPS
- migrates ams_groups
*/
EXEC membercentral.dbo.migrate_groups 'WV'

/* 
CUSTOM FIELDS
- migrates columns that dont already exist
- migrates column options if any defined
- creates virtual group conditions and rules if necessary
*/
EXEC membercentral.dbo.migrate_customfields 'WV'

/*
MEMBERS
- runs member checks
- migrates ams_members that havent been migrated yet
- migrates ams_memberEmails that havent been migrated yet
- migrates ams_memberAddresses that havent been migrated yet
- migrates ams_memberPhones that havent been migrated yet (phone/fax)
- migrates ams_memberData for columns that have been created on new as string datatypes
- adds members to site admin group that dont currently exist in siteadmin group
- adds manual group assignments that dont currently exist (based on group name)
*/
EXEC membercentral.dbo.migrate_members 'WV', @activeOnly=0, @adminsOnly=1
EXEC membercentral.dbo.migrate_members 'WV', @activeOnly=0, @adminsOnly=0

/*
PROFILES
- runs member checks
- adds ams_networkProfiles to those linked accts IAthout one
- adds ams_memberNetworkProfile for each linked acct
- migrates ams_networkProfileEmails that havent been migrated yet
*/
EXEC membercentral.dbo.migrate_profiles 'WV', @activeOnly=0, @adminsOnly=1
EXEC membercentral.dbo.migrate_profiles 'WV', @activeOnly=0, @adminsOnly=0


