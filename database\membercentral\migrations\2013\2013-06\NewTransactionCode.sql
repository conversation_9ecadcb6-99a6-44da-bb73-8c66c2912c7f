USE [memberCentral]
GO
IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].up_errorhandler') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].up_errorhandler
GO
CREATE PROCEDURE up_errorhandler 
AS
 
DECLARE @errmsg   nvarchar(2048),
		@severity tinyint,
		@state    tinyint,
		@errno    int,
		@proc     sysname,
		@lineno   int
	           
SELECT @errmsg = error_message(), @severity = error_severity(),
		@state  = error_state(), @errno = error_number(),
		@proc   = error_procedure(), @lineno = error_line()
       
IF @errmsg NOT LIKE '***%' BEGIN 
	SELECT @errmsg = '*** ' + coalesce(quotename(@proc), '<dynamic SQL>') + ', ' + ltrim(str(@lineno)) + '. Errno ' + ltrim(str(@errno)) + ': ' + @errmsg
	RAISERROR(@errmsg, @severity, @state)
END
ELSE
	RAISERROR(@errmsg, @severity, @state)
go

ALTER PROC [dbo].[tr_voidTransaction]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@transactionID int,
@checkInBounds bit = 1, -- checkInBounds should ALWAYS be 1 unless it is called from another proc in this sequence. NEVER call this with checkInBounds = 0 directly!
@vidPool xml OUTPUT,
@tids xml OUTPUT

AS

set nocount on

declare @tblVoided TABLE (transactionid int)
declare @statusID int, @typeID int
declare @tidsdeep xml
declare @nowdate datetime
select @nowdate = getdate()
select @tids = null


-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;


BEGIN TRY

	-- get info about transaction to be voided
	select @statusID=statusID, @typeID=typeID 
		from dbo.tr_transactions 
		where transactionID = @transactionID

	-- if transaction is already voided, dont try to void again. Not an error -- just skip everything.
	IF @statusID in (1,3) BEGIN

		-- if 1,3,7 we can prime the invoice pool for use by the voidoffsets
		IF @typeID in (1,3,7) BEGIN
			select @vidPool = isnull((
				select invoiceid as [id]
				from (
					select i.invoiceid
					from dbo.tr_invoiceTransactions as it
					inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
					where it.transactionID = @transactionID
					and i.statusID in (1,2)
						union
					select distinct i.invoiceid
					from dbo.fn_tr_getRelatedTransactionsToVoid_sale(@transactionID) as rtv
					inner join dbo.tr_invoiceTransactions as it on it.transactionID = rtv.transactionID
					inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
					where i.statusID in (1,2)
					and @typeID = 1
						union
					select distinct i.invoiceid
					from dbo.fn_tr_getRelatedTransactionsToVoid_salesTax(@transactionID) as rtv
					inner join dbo.tr_invoiceTransactions as it on it.transactionID = rtv.transactionID
					inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
					where i.statusID in (1,2)
					and @typeID = 7
						union
					select distinct i.invoiceid
					from dbo.fn_tr_getRelatedTransactionsToVoid_adjustment(@transactionID) as rtv
					inner join dbo.tr_invoiceTransactions as it on it.transactionID = rtv.transactionID
					inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
					where i.statusID in (1,2)
					and @typeID = 3
						union
					select V.item.value('@id','int')
					FROM @vidPool.nodes('/vpool/v') as V(item)
				) as tmpV
				FOR XML RAW('v'), root('vpool')
			),'<vpool/>')
		END

		IF @typeID = 1 BEGIN
			EXEC dbo.tr_voidTransaction_sale @recordedOnSiteID=@recordedOnSiteID,
				@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID,
				@transactionID=@transactionID, @vidPool=@vidPool OUTPUT, @tids=@tidsdeep OUTPUT
			insert into @tblVoided (transactionid)
			select T.item.value('@tid','int')
			FROM @tidsdeep.nodes('/tr/t') as T(item)
		END

		IF @typeID = 2 BEGIN
			EXEC dbo.tr_voidTransaction_payment @recordedOnSiteID=@recordedOnSiteID,
				@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID,
				@transactionID=@transactionID, @vidPool=@vidPool OUTPUT, @tids=@tidsdeep OUTPUT
			insert into @tblVoided (transactionid)
			select T.item.value('@tid','int')
			FROM @tidsdeep.nodes('/tr/t') as T(item)
		END

		IF @typeID = 3 BEGIN
			EXEC dbo.tr_voidTransaction_adjustment @recordedOnSiteID=@recordedOnSiteID,
				@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID,
				@transactionID=@transactionID, @vidPool=@vidPool OUTPUT, @tids=@tidsdeep OUTPUT
			insert into @tblVoided (transactionid)
			select T.item.value('@tid','int')
			FROM @tidsdeep.nodes('/tr/t') as T(item)
		END

		IF @typeID = 4 BEGIN
			EXEC dbo.tr_voidTransaction_refund @recordedOnSiteID=@recordedOnSiteID,
				@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID,
				@transactionID=@transactionID, @vidPool=@vidPool OUTPUT, @tids=@tidsdeep OUTPUT
			insert into @tblVoided (transactionid)
			select T.item.value('@tid','int')
			FROM @tidsdeep.nodes('/tr/t') as T(item)
		END

		IF @typeID = 5 BEGIN
			EXEC dbo.tr_voidTransaction_allocation @recordedOnSiteID=@recordedOnSiteID,
				@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID,
				@transactionID=@transactionID, @vidPool=@vidPool OUTPUT, @tids=@tidsdeep OUTPUT
			insert into @tblVoided (transactionid)
			select T.item.value('@tid','int')
			FROM @tidsdeep.nodes('/tr/t') as T(item)
		END

		IF @typeID = 6 BEGIN
			-- if debit acct is DEP, then it is a write off of a payment.
			IF EXISTS (select transactionID 
						from dbo.tr_transactions as t
						inner join dbo.tr_glAccounts as gl on gl.glAccountID = t.debitGLAccountID
							and gl.GLCode = 'DEPOSITS'
							and gl.isSystemAccount = 1
							and t.transactionID = @transactionID) BEGIN
				EXEC dbo.tr_voidTransaction_writeoff_payment @recordedOnSiteID=@recordedOnSiteID,
					@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID,
					@transactionID=@transactionID, @vidPool=@vidPool OUTPUT, @tids=@tidsdeep OUTPUT
				insert into @tblVoided (transactionid)
				select T.item.value('@tid','int')
				FROM @tidsdeep.nodes('/tr/t') as T(item)
			END

			-- else it is a write off of a sale.
			ELSE BEGIN
				EXEC dbo.tr_voidTransaction_writeoff_sale @recordedOnSiteID=@recordedOnSiteID,
					@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID,
					@transactionID=@transactionID, @vidPool=@vidPool OUTPUT, @tids=@tidsdeep OUTPUT
				insert into @tblVoided (transactionid)
				select T.item.value('@tid','int')
				FROM @tidsdeep.nodes('/tr/t') as T(item)
			END
		END

		IF @typeID = 7 BEGIN
			EXEC dbo.tr_voidTransaction_salesTax @recordedOnSiteID=@recordedOnSiteID,
				@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID,
				@transactionID=@transactionID, @vidPool=@vidPool OUTPUT, @tids=@tidsdeep OUTPUT
			insert into @tblVoided (transactionid)
			select T.item.value('@tid','int')
			FROM @tidsdeep.nodes('/tr/t') as T(item)
		END

		IF @typeID = 9 BEGIN
			EXEC dbo.tr_voidTransaction_nsf @recordedOnSiteID=@recordedOnSiteID,
				@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID,
				@transactionID=@transactionID, @vidPool=@vidPool OUTPUT, @tids=@tidsdeep OUTPUT
			insert into @tblVoided (transactionid)
			select T.item.value('@tid','int')
			FROM @tidsdeep.nodes('/tr/t') as T(item)
		END

	END

	-- if @checkInBounds = 1 then we need to run the in-bounds checking and invoice cleanup routines
	-- should only run once per voidtransaction root call (at the end)
	IF @checkInBounds = 1 BEGIN
		insert into @tblVoided (transactionID)
		select r.appliedToTransactionID
		from @tblVoided as tbl
		inner join dbo.tr_transactions as t on t.transactionID = tbl.transactionID and t.typeID = 3
		inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
			union
		select tSale.transactionID
		from @tblVoided as tbl
		inner join dbo.tr_transactions as t on t.transactionID = tbl.transactionID and t.typeID = 5
		inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AllocSaleTrans'
		inner join dbo.tr_transactions as tSale on tSale.transactionID = r.appliedToTransactionID
			union
		select rAdj.appliedToTransactionID
		from @tblVoided as tbl
		inner join dbo.tr_transactions as t on t.transactionID = tbl.transactionID and t.typeID = 5
		inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AllocSaleTrans'
		inner join dbo.tr_transactions as tAdj on tAdj.transactionID = r.appliedToTransactionID and t.typeID = 3
		inner join dbo.tr_relationships as rAdj on rAdj.transactionID = tAdj.transactionID
		inner join dbo.tr_relationshipTypes as rtAdj on rtAdj.typeID = rAdj.typeID and rtAdj.type = 'AdjustTrans'
			union
		select r.appliedToTransactionID
		from @tblVoided as tbl
		inner join dbo.tr_transactions as t on t.transactionID = tbl.transactionID and t.typeID = 5
		inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AllocPayTrans'
			union
		select r.appliedToTransactionID
		from @tblVoided as tbl
		inner join dbo.tr_transactions as t on t.transactionID = tbl.transactionID and t.typeID = 6
		inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'WriteOffSaleTrans'
			union
		select r.appliedToTransactionID
		from @tblVoided as tbl
		inner join dbo.tr_transactions as t on t.transactionID = tbl.transactionID and t.typeID = 6
		inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'WriteOffPayTrans'

		-- sales - new cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount must be between 0 and cache_amountAfterAdjustment
		-- payments - new cache_allocatedAmountOfPayment must be between 0 and cache_refundableAmountOfPayment
		IF EXISTS (
			select ts.saleID 
			from dbo.tr_transactionSales as ts
			inner join @tblVoided as tbl on tbl.transactionID = ts.transactionID 
			where ts.cache_activePaymentAllocatedAmount+ts.cache_pendingPaymentAllocatedAmount not between 0 and ts.cache_amountAfterAdjustment
		)
			RAISERROR('In-bounds checking failed for tr_transactionSales.', 16, 1);
		IF EXISTS (
			select tp.paymentID 
			from dbo.tr_transactionPayments as tp
			inner join @tblVoided as tbl on tbl.transactionID = tp.transactionID 
			where tp.cache_allocatedAmountOfPayment not between 0 and tp.cache_refundableAmountOfPayment
		)
			RAISERROR('In-bounds checking failed for tr_transactionPayments.', 16, 1);

		-- invoiceTransactions cache checking
		IF EXISTS (
			select it.itID
			from dbo.tr_invoiceTransactions as it
			inner join @tblVoided as tbl on tbl.transactionID = it.transactionID 
			where it.cache_activePaymentAllocatedAmount+it.cache_pendingPaymentAllocatedAmount not between 0 and it.cache_invoiceAmountAfterAdjustment
		)		
			RAISERROR('In-bounds checking failed for tr_invoiceTransactions.', 16, 1);


		/* **************** */
		/* cleanup invoices */
		/* **************** */
		declare @tblAllInvoicesTouched TABLE (invoiceid int, statusid int, amtDueNoPendingOnInvoice money, needToClose bit)
		insert into @tblAllInvoicesTouched (invoiceid, statusid, amtDueNoPendingOnInvoice, needToClose)
		select i.invoiceid, i.statusid, 0, 0
		from @vidPool.nodes('/vpool/v') as V(item)
		inner join dbo.tr_invoices as i on i.invoiceid = V.item.value('@id','int')
			union
		select i.invoiceid, i.statusid, 0, 0
		from @tblVoided as tbl
		inner join dbo.tr_invoiceTransactions as it on it.transactionID = tbl.transactionID
		inner join dbo.tr_invoices as i on i.invoiceid = it.invoiceid
		
		update tbl
		set tbl.amtDueNoPendingOnInvoice = isnull(tmp.amtDueNoPending,0)
		from @tblAllInvoicesTouched as tbl
		inner join (
			select it.invoiceid, sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) as amtDueNoPending
			from dbo.tr_invoiceTransactions as it
			inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
			inner join @tblAllInvoicesTouched as tbl on tbl.invoiceid = it.invoiceid
			where t.statusID <> 2
			group by it.invoiceid
		) as tmp on tmp.invoiceid = tbl.invoiceid

		-- close any open invoices that only contain voidoffset transactions.
		update tbl
		set tbl.needToClose = 1
		from @tblAllInvoicesTouched as tbl
		inner join (
			select i.invoiceID
			from @tblAllInvoicesTouched as i
			inner join tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
			where i.statusID = 1
			and not exists (
				select i2.invoiceID
				from @tblAllInvoicesTouched as i2
				inner join tr_invoiceTransactions as it2 on it2.invoiceID = i2.invoiceID
				inner join tr_transactions as t on t.transactionid = it2.transactionid
				where i2.statusID = 1
				and t.typeID <> 8
				and i2.invoiceid = i.invoiceID
			)
		) as tmp on tmp.invoiceid = tbl.invoiceid

		update i
		set i.statusID = 3
		from dbo.tr_invoices as i	
		inner join @tblAllInvoicesTouched as tbl on tbl.invoiceid = i.invoiceid
		where tbl.needToClose = 1

		insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
		select tbl.invoiceid, getdate(), 3, 1, @recordedByMemberID
		from @tblAllInvoicesTouched as tbl
		where tbl.needToClose = 1

		update @tblAllInvoicesTouched
		set statusID = 3
		where needToClose = 1

		-- if invoice is closed and is now fully paid with active payments, mark it as paid
		update i
		set i.statusID = 4, i.payProfileID = null
		from dbo.tr_invoices as i	
		inner join @tblAllInvoicesTouched as tbl on tbl.invoiceid = i.invoiceid
		where tbl.statusID = 3
		and tbl.amtDueNoPendingOnInvoice = 0
		
		insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
		select tbl.invoiceid, getdate(), 4, 3, @recordedByMemberID
		from @tblAllInvoicesTouched as tbl
		where tbl.statusID = 3
		and tbl.amtDueNoPendingOnInvoice = 0

		-- if invoice is paid and is now NOT fully paid with active payments, mark it as closed
		update i
		set i.statusID = 3
		from dbo.tr_invoices as i	
		inner join @tblAllInvoicesTouched as tbl on tbl.invoiceid = i.invoiceid
		where tbl.statusID = 4
		and tbl.amtDueNoPendingOnInvoice > 0

		insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
		select tbl.invoiceid, getdate(), 3, 4, @recordedByMemberID
		from @tblAllInvoicesTouched as tbl
		where tbl.statusID = 4
		and tbl.amtDueNoPendingOnInvoice > 0
	END


	IF @TranCounter = 0
		COMMIT TRAN;
	SELECT @tids = (SELECT transactionid as tid FROM @tblVoided FOR XML RAW('t'), root('tr'))
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	SELECT @tids = '<tr/>'
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_voidTransaction_adjustment]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@transactionID int,
@vidPool xml OUTPUT,
@tids xml OUTPUT

AS

/* ***********************************************
	DO NOT CALL THIS PROC DIRECTLY!
	THIS SHOULD ONLY BE CALLED FROM WITHIN 
	tr_voidTransaction
************************************************** */
set nocount on

declare @offsetTransactionID int, @statusID int, @minTID int, @minAID int
declare @tblVoided TABLE (transactionid int)
declare @tidsdeep xml
select @statusID = dbo.fn_tr_getStatusID('Voided')
select @tids = null


-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- create offset trans
	EXEC dbo.tr_createTransaction_voidOffset @recordedOnSiteID=@recordedOnSiteID,
		@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID,
		@appliedToTransactionID=@transactionID, @vidPool=@vidPool OUTPUT,
		@transactionID=@offsetTransactionID OUTPUT
	insert into @tblVoided (transactionid) 
	values (@offsetTransactionID)

	-- update transaction status
	UPDATE dbo.tr_transactions
	SET statusID = @statusID
	WHERE transactionID = @transactionID

	insert into @tblVoided (transactionid) 
	values (@transactionID)

	-- void any related transactions
	declare @toVoid TABLE (autoid int IDENTITY(1,1), transactionID int, dateRecorded datetime)
	insert into @toVoid (transactionID, dateRecorded)
	select transactionID, dateRecorded
	from dbo.fn_tr_getRelatedTransactionsToVoid_adjustment(@transactionID)
	ORDER BY dateRecorded desc, transactionID desc	

	SELECT @minAID = null
	SELECT @minAID = min(autoid) FROM @toVoid
	while @minAID is not null BEGIN
		SELECT @minTID = null
		SELECT @minTID = transactionID from @toVoid where autoID = @minAID

		-- checkInBounds should be 0 on these nested voids
		EXEC dbo.tr_voidTransaction @recordedOnSiteID=@recordedOnSiteID, 
			@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
			@transactionID=@minTID, @checkInBounds=0, @vidPool=@vidPool OUTPUT, 
			@tids=@tidsdeep OUTPUT
		insert into @tblVoided (transactionid)
		select T.item.value('@tid','int')
		FROM @tidsdeep.nodes('/tr/t') as T(item)

		SELECT @minAID = min(autoid) FROM @toVoid where autoID > @minAID
	END

	-- get amount of adjustment
	declare @adjAmt money
	select @adjAmt = amount from dbo.tr_transactions where transactionID = @transactionID
		IF @adjAmt is null RAISERROR('adjAmt is null', 16, 1);

	-- if debit acct is AR, then adjustment was up (positive amt).
	-- if not, adjustment was down (negative amt).
	IF NOT EXISTS (select transactionID 
				from dbo.tr_transactions as t
				inner join dbo.tr_glAccounts as gl on gl.glAccountID = t.debitGLAccountID
					and gl.GLCode = 'ACCOUNTSRECEIVABLE'
					and gl.isSystemAccount = 1
					and t.transactionID = @transactionID) BEGIN
		SELECT @adjAmt = @adjAmt * -1
	END

	-- get sale that has been adjusted
	declare @saleTID int
	select @saleTID = r.appliedToTransactionID 
		from dbo.tr_relationships as r
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID
			and r.transactionID = @transactionID
			and rt.type = 'AdjustTrans'
		IF @saleTID is null RAISERROR('saleTID is null', 16, 1);
	
	-- update invoicetransactions
	UPDATE dbo.tr_invoiceTransactions
	SET cache_invoiceAmountAfterAdjustment = 0,
		cache_ActivePaymentAllocatedAmount = 0,
		cache_PendingPaymentAllocatedAmount = 0
	WHERE transactionID = @transactionID

	-- if negative adjustment that we're voiding, loop over adjustInvTrans and add back amounts
	IF @adjAmt < 0 BEGIN
		update it
		set it.cache_invoiceAmountAfterAdjustment = it.cache_invoiceAmountAfterAdjustment + abs(tmp.amount)
		from dbo.tr_invoiceTransactions as it
		inner join (		
			select r.appliedToTransactionID, r.amount
			from dbo.tr_invoiceTransactions as it2
			inner join dbo.tr_relationships as r on r.transactionID = it2.transactionID and it2.transactionID = @transactionID
			inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustInvTrans'
		) as tmp on tmp.appliedToTransactionID = it.transactionID
	END

	-- update cache
	IF @adjAmt > 0 BEGIN
		UPDATE dbo.tr_transactionSales
		SET cache_amountAfterAdjustment = cache_amountAfterAdjustment - @adjAmt
		WHERE transactionID = @saleTID
	END 
	ELSE BEGIN
		UPDATE dbo.tr_transactionSales
		SET cache_amountAfterAdjustment = cache_amountAfterAdjustment + abs(@adjAmt)
		WHERE transactionID = @saleTID
	END


	IF @TranCounter = 0
		COMMIT TRAN;
	SELECT @tids = (SELECT transactionid as tid FROM @tblVoided FOR XML RAW('t'), root('tr'))
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	SELECT @tids = '<tr/>'
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO


ALTER PROC [dbo].[tr_voidTransaction_allocation]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@transactionID int,
@vidPool xml OUTPUT,
@tids xml OUTPUT

AS

/* ***********************************************
	DO NOT CALL THIS PROC DIRECTLY!
	THIS SHOULD ONLY BE CALLED FROM WITHIN 
	tr_voidTransaction
************************************************** */
set nocount on

declare @offsetTransactionID int, @statusID int, @minTID int, @minAID int
declare @tblVoided TABLE (transactionid int)
declare @tidsdeep xml
select @statusID = dbo.fn_tr_getStatusID('Voided')
select @tids = null


-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;


BEGIN TRY

	-- create offset trans
	EXEC dbo.tr_createTransaction_voidOffset @recordedOnSiteID=@recordedOnSiteID,
		@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID,
		@appliedToTransactionID=@transactionID, @vidPool=@vidPool OUTPUT,
		@transactionID=@offsetTransactionID OUTPUT
	insert into @tblVoided (transactionid) 
	values (@offsetTransactionID)

	-- update transaction status
	UPDATE dbo.tr_transactions
	SET statusID = @statusID
	WHERE transactionID = @transactionID
	
	insert into @tblVoided (transactionid) 
	values (@transactionID)

	-- void any related transactions
	declare @toVoid TABLE (autoid int IDENTITY(1,1), transactionID int, dateRecorded datetime)
	insert into @toVoid (transactionID, dateRecorded)
	select transactionID, dateRecorded
	from dbo.fn_tr_getRelatedTransactionsToVoid_allocation(@transactionID)
	ORDER BY dateRecorded desc, transactionID desc	

	SELECT @minAID = null
	SELECT @minAID = min(autoid) FROM @toVoid
	while @minAID is not null BEGIN
		SELECT @minTID = null
		SELECT @minTID = transactionID from @toVoid where autoID = @minAID

		-- checkInBounds should be 0 on these nested voids
		EXEC dbo.tr_voidTransaction @recordedOnSiteID=@recordedOnSiteID, 
			@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
			@transactionID=@minTID, @checkInBounds=0, @vidPool=@vidPool OUTPUT,
			@tids=@tidsdeep OUTPUT
		insert into @tblVoided (transactionid)
		select T.item.value('@tid','int')
		FROM @tidsdeep.nodes('/tr/t') as T(item)

		SELECT @minAID = min(autoid) FROM @toVoid where autoID > @minAID
	END

	-- get amount of allocation
	declare @allocAmt money
	select @allocAmt = amount from dbo.tr_transactions where transactionID = @transactionID
		IF @allocAmt is null RAISERROR('allocAmt is null', 16, 1);

	-- if debit acct is AR, then was a deallocation. else allocation.
	IF EXISTS (select transactionID 
				from dbo.tr_transactions as t
				inner join dbo.tr_glAccounts as gl on gl.glAccountID = t.debitGLAccountID
					and gl.GLCode = 'ACCOUNTSRECEIVABLE'
					and gl.isSystemAccount = 1
					and t.transactionID = @transactionID) BEGIN
		SELECT @allocAmt = @allocAmt * -1
	END

	-- get what has been allocated.
	declare @saleTaxAdjTID int, @saleTaxAdjTypeID int, @saleTaxTID int
	declare @paymentTID int, @paystatusID int
	select @paymentTID = r.appliedToTransactionID, @paystatusID = t.statusID
		from dbo.tr_relationships as r
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID
			and r.transactionID = @transactionID
			and rt.type = 'AllocPayTrans'
		inner join dbo.tr_transactions as t on t.transactionID = r.appliedToTransactionID
		IF @paymentTID is null RAISERROR('paymentTID is null', 16, 1);
	select @saleTaxAdjTID = r.appliedToTransactionID, @saleTaxAdjTypeID = t.typeID
		from dbo.tr_relationships as r
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID
			and r.transactionID = @transactionID
			and rt.type = 'AllocSaleTrans'
		inner join dbo.tr_transactions as t on t.transactionID = r.appliedToTransactionID
		IF @saleTaxAdjTID is null RAISERROR('saleTaxAdjTID is null', 16, 1);
	IF @saleTaxAdjTypeID in (1,7)
		SELECT @saleTaxTID = @saleTaxAdjTID
	ELSE BEGIN
		SELECT @saleTaxTID = r.appliedToTransactionID
			from dbo.tr_transactions as tAdj
			inner join dbo.tr_relationships as r on r.transactionID = tAdj.transactionID
			inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
			where tAdj.transactionID = @saleTaxAdjTID
		IF @saleTaxTID is null RAISERROR('saleTaxTID is null', 16, 1);
	END

	-- update invoicetransactions
	declare @activePaymentAllocatedAmount money, @pendingPaymentAllocatedAmount money
	select @activePaymentAllocatedAmount = activePaymentAllocatedAmount, 
			@pendingPaymentAllocatedAmount = pendingPaymentAllocatedAmount
	from dbo.fn_tr_getAllocatedAmountofSaleOrAdj(@saleTaxAdjTID,null)

	UPDATE dbo.tr_invoiceTransactions
	SET cache_activePaymentAllocatedAmount = @activePaymentAllocatedAmount,
		cache_pendingPaymentAllocatedAmount = @pendingPaymentAllocatedAmount
	WHERE transactionID = @saleTaxAdjTID

	-- put in the @saleTaxAdjTID for the cleanup invoice routine
	insert into @tblVoided (transactionid)
	values (@saleTaxAdjTID)

	-- update sales cache
	declare @cache_activePaymentAllocatedAmount money, @cache_pendingPaymentAllocatedAmount money
	select @cache_activePaymentAllocatedAmount=activePaymentAllocatedAmount, 
			@cache_pendingPaymentAllocatedAmount=pendingPaymentAllocatedAmount
	from dbo.fn_tr_getAllocatedAmountofSale(@saleTaxTID)

	UPDATE dbo.tr_transactionSales
	SET cache_activePaymentAllocatedAmount = @cache_activePaymentAllocatedAmount,
		cache_pendingPaymentAllocatedAmount = @cache_pendingPaymentAllocatedAmount
	WHERE transactionID = @saleTaxTID

	-- update payment cache
	IF @allocAmt > 0 BEGIN
		UPDATE dbo.tr_transactionPayments
		SET cache_allocatedAmountOfPayment = cache_allocatedAmountOfPayment - @allocAmt
		WHERE transactionID = @paymentTID
	END
	IF @allocAmt < 0 BEGIN
		UPDATE dbo.tr_transactionPayments
		SET cache_allocatedAmountOfPayment = cache_allocatedAmountOfPayment + abs(@allocAmt)
		WHERE transactionID = @paymentTID
	END


	IF @TranCounter = 0
		COMMIT TRAN;
	SELECT @tids = (SELECT transactionid as tid FROM @tblVoided FOR XML RAW('t'), root('tr'))
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	SELECT @tids = '<tr/>'
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_voidTransaction_nsf]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@transactionID int,
@vidPool xml OUTPUT,
@tids xml OUTPUT

AS

/* ***********************************************
	DO NOT CALL THIS PROC DIRECTLY!
	THIS SHOULD ONLY BE CALLED FROM WITHIN 
	tr_voidTransaction
************************************************** */
set nocount on

declare @offsetTransactionID int, @statusID int, @minTID int
declare @tblVoided TABLE (transactionid int)
select @statusID = dbo.fn_tr_getStatusID('Voided')
select @tids = null


-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;


BEGIN TRY

	-- create offset trans
	EXEC dbo.tr_createTransaction_voidOffset @recordedOnSiteID=@recordedOnSiteID,
		@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID,
		@appliedToTransactionID=@transactionID, @vidPool=@vidPool OUTPUT,
		@transactionID=@offsetTransactionID OUTPUT
	insert into @tblVoided (transactionid) 
	values (@offsetTransactionID)

	-- update transaction status
	UPDATE dbo.tr_transactions
	SET statusID = @statusID
	WHERE transactionID = @transactionID

	insert into @tblVoided (transactionid) 
	values (@transactionID)

	-- get amount of nsf
	declare @nsfAmt money
	select @nsfAmt = amount from dbo.tr_transactions where transactionID = @transactionID
		IF @nsfAmt is null RAISERROR('nsfAmt is null', 16, 1);

	-- get payment that had been NSF'd
	declare @payTID int
	select @payTID = r.appliedToTransactionID 
		from dbo.tr_relationships as r
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID
			and r.transactionID = @transactionID
			and rt.type = 'NSFTrans'
		IF @payTID is null RAISERROR('payTID is null', 16, 1);

	-- update cache for payment
	UPDATE dbo.tr_transactionPayments
	SET cache_refundableAmountOfPayment = cache_refundableAmountOfPayment + @nsfAmt
	WHERE transactionID = @payTID


	IF @TranCounter = 0
		COMMIT TRAN;
	SELECT @tids = (SELECT transactionid as tid FROM @tblVoided FOR XML RAW('t'), root('tr'))
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	SELECT @tids = '<tr/>'
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_voidTransaction_payment]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@transactionID int,
@vidPool xml OUTPUT,
@tids xml OUTPUT

AS

/* ***********************************************
	DO NOT CALL THIS PROC DIRECTLY!
	THIS SHOULD ONLY BE CALLED FROM WITHIN 
	tr_voidTransaction
************************************************** */
set nocount on

declare @offsetTransactionID int, @statusID int, @minTID int, @minAID int
declare @tblVoided TABLE (transactionid int)
declare @tidsdeep xml
select @statusID = dbo.fn_tr_getStatusID('Voided')
select @tids = null


-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;


BEGIN TRY

	-- create offset trans
	EXEC dbo.tr_createTransaction_voidOffset @recordedOnSiteID=@recordedOnSiteID,
		@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID,
		@appliedToTransactionID=@transactionID, @vidPool=@vidPool OUTPUT,
		@transactionID=@offsetTransactionID OUTPUT
	insert into @tblVoided (transactionid) 
	values (@offsetTransactionID)

	-- update transaction status
	UPDATE dbo.tr_transactions
	SET statusID = @statusID
	WHERE transactionID = @transactionID

	insert into @tblVoided (transactionid) 
	values (@transactionID)

	-- void any allocations, writeoffs, refunds (in reverse order to prevent conflicts)
	declare @toVoid TABLE (autoid int IDENTITY(1,1), transactionID int, dateRecorded datetime)
	insert into @toVoid (transactionID, dateRecorded)
	select transactionID, dateRecorded
	from dbo.fn_tr_getRelatedTransactionsToVoid_payment(@transactionID)
	ORDER BY dateRecorded desc, transactionID desc	

	SELECT @minAID = null
	SELECT @minAID = min(autoid) FROM @toVoid
	while @minAID is not null BEGIN
		SELECT @minTID = null
		SELECT @minTID = transactionID from @toVoid where autoID = @minAID

		-- checkInBounds should be 0 on these nested voids
		EXEC dbo.tr_voidTransaction @recordedOnSiteID=@recordedOnSiteID, 
			@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
			@transactionID=@minTID, @checkInBounds=0, @vidPool=@vidPool OUTPUT,
			@tids=@tidsdeep OUTPUT
		insert into @tblVoided (transactionid)
		select T.item.value('@tid','int')
		FROM @tidsdeep.nodes('/tr/t') as T(item)

		SELECT @minAID = min(autoid) FROM @toVoid where autoID > @minAID
	END
	
	-- update cache
	UPDATE dbo.tr_transactionPayments
	SET cache_allocatedAmountOfPayment = 0,
		cache_refundableAmountOfPayment = 0
	WHERE transactionID = @transactionID


	IF @TranCounter = 0
		COMMIT TRAN;
	SELECT @tids = (SELECT transactionid as tid FROM @tblVoided FOR XML RAW('t'), root('tr'))
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	SELECT @tids = '<tr/>'
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO



ALTER PROC [dbo].[tr_voidTransaction_refund]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@transactionID int,
@vidPool xml OUTPUT,
@tids xml OUTPUT

AS

/* ***********************************************
	DO NOT CALL THIS PROC DIRECTLY!
	THIS SHOULD ONLY BE CALLED FROM WITHIN 
	tr_voidTransaction
************************************************** */
set nocount on

declare @offsetTransactionID int, @statusID int, @minTID int
declare @tblVoided TABLE (transactionid int)
select @statusID = dbo.fn_tr_getStatusID('Voided')
select @tids = null


-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;


BEGIN TRY

	-- create offset trans
	EXEC dbo.tr_createTransaction_voidOffset @recordedOnSiteID=@recordedOnSiteID,
		@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID,
		@appliedToTransactionID=@transactionID, @vidPool=@vidPool OUTPUT,
		@transactionID=@offsetTransactionID OUTPUT
	insert into @tblVoided (transactionid) 
	values (@offsetTransactionID)

	-- update transaction status
	UPDATE dbo.tr_transactions
	SET statusID = @statusID
	WHERE transactionID = @transactionID

	insert into @tblVoided (transactionid) 
	values (@transactionID)

	-- get amount of refund
	declare @refAmt money
	select @refAmt = amount from dbo.tr_transactions where transactionID = @transactionID
		IF @refAmt is null RAISERROR('refAmt is null', 16, 1);

	-- get payment that had been refunded
	declare @payTID int
	select @payTID = r.appliedToTransactionID 
		from dbo.tr_relationships as r
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID
			and r.transactionID = @transactionID
			and rt.type = 'RefundTrans'
		IF @payTID is null RAISERROR('payTID is null', 16, 1);

	-- update cache for payment
	UPDATE dbo.tr_transactionPayments
	SET cache_refundableAmountOfPayment = cache_refundableAmountOfPayment + @refAmt
	WHERE transactionID = @payTID

	-- update cache for refund
	UPDATE dbo.tr_transactionPayments
	SET cache_allocatedAmountOfPayment = 0,
		cache_refundableAmountOfPayment = 0
	WHERE transactionID = @transactionID


	IF @TranCounter = 0
		COMMIT TRAN;
	SELECT @tids = (SELECT transactionid as tid FROM @tblVoided FOR XML RAW('t'), root('tr'))
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	SELECT @tids = '<tr/>'
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO


ALTER PROC [dbo].[tr_voidTransaction_sale]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@transactionID int,
@vidPool xml OUTPUT,
@tids xml OUTPUT

AS

/* ***********************************************
	DO NOT CALL THIS PROC DIRECTLY!
	THIS SHOULD ONLY BE CALLED FROM WITHIN 
	tr_voidTransaction
************************************************** */
set nocount on

declare @offsetTransactionID int, @statusID int, @minTID int, @minAID int
declare @tblVoided TABLE (transactionid int)
declare @tidsdeep xml
select @statusID = dbo.fn_tr_getStatusID('Voided')
select @tids = null


-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;


BEGIN TRY

	-- create offset trans
	EXEC dbo.tr_createTransaction_voidOffset @recordedOnSiteID=@recordedOnSiteID,
		@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID,
		@appliedToTransactionID=@transactionID, @vidPool=@vidPool OUTPUT,
		@transactionID=@offsetTransactionID OUTPUT
	insert into @tblVoided (transactionid) 
	values (@offsetTransactionID)

	-- update transaction status
	UPDATE dbo.tr_transactions
	SET statusID = @statusID
	WHERE transactionID = @transactionID
	
	insert into @tblVoided (transactionid) 
	values (@transactionID)

	-- void any children sales, sales tax, adjustments, allocations, writeoffs (in reverse order to prevent conflicts)
	declare @toVoid TABLE (autoid int IDENTITY(1,1), transactionID int, dateRecorded datetime)
	insert into @toVoid (transactionID, dateRecorded)
	select transactionID, dateRecorded
	from dbo.fn_tr_getRelatedTransactionsToVoid_sale(@transactionID)
	ORDER BY dateRecorded desc, transactionID desc	

	SELECT @minAID = null
	SELECT @minAID = min(autoid) FROM @toVoid
	while @minAID is not null BEGIN
		SELECT @minTID = null
		SELECT @minTID = transactionID from @toVoid where autoID = @minAID

		-- checkInBounds should be 0 on these nested voids
		EXEC dbo.tr_voidTransaction @recordedOnSiteID=@recordedOnSiteID, 
			@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
			@transactionID=@minTID, @checkInBounds=0, @vidPool=@vidPool OUTPUT, 
			@tids=@tidsdeep OUTPUT
		insert into @tblVoided (transactionid)
		select T.item.value('@tid','int')
		FROM @tidsdeep.nodes('/tr/t') as T(item)

		SELECT @minAID = min(autoid) FROM @toVoid where autoID > @minAID
	END

	-- update invoicetransactions
	UPDATE dbo.tr_invoiceTransactions
	SET cache_invoiceAmountAfterAdjustment = 0,
		cache_ActivePaymentAllocatedAmount = 0,
		cache_PendingPaymentAllocatedAmount = 0
	WHERE transactionID = @transactionID

	-- update cache
	UPDATE dbo.tr_transactionSales
	SET cache_amountAfterAdjustment = 0,
		cache_ActivePaymentAllocatedAmount = 0,
		cache_PendingPaymentAllocatedAmount = 0
	WHERE transactionID = @transactionID


	IF @TranCounter = 0
		COMMIT TRAN;
	SELECT @tids = (SELECT transactionid as tid FROM @tblVoided FOR XML RAW('t'), root('tr'))
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	SELECT @tids = '<tr/>'
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_voidTransaction_salesTax]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@transactionID int,
@vidPool xml OUTPUT,
@tids xml OUTPUT

AS

/* ***********************************************
	DO NOT CALL THIS PROC DIRECTLY!
	THIS SHOULD ONLY BE CALLED FROM WITHIN 
	tr_voidTransaction
************************************************** */
set nocount on

declare @offsetTransactionID int, @statusID int, @minTID int, @minAID int
declare @tblVoided TABLE (transactionid int)
declare @tidsdeep xml
select @statusID = dbo.fn_tr_getStatusID('Voided')
select @tids = null


-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;


BEGIN TRY

	-- create offset trans
	EXEC dbo.tr_createTransaction_voidOffset @recordedOnSiteID=@recordedOnSiteID,
		@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID,
		@appliedToTransactionID=@transactionID, @vidPool=@vidPool OUTPUT,
		@transactionID=@offsetTransactionID OUTPUT
	insert into @tblVoided (transactionid) 
	values (@offsetTransactionID)

	-- update transaction status
	UPDATE dbo.tr_transactions
	SET statusID = @statusID
	WHERE transactionID = @transactionID
	
	insert into @tblVoided (transactionid) 
	values (@transactionID)

	-- void any adjustments, allocations, writeoffs (in reverse order to prevent conflicts)
	declare @toVoid TABLE (autoid int IDENTITY(1,1), transactionID int, dateRecorded datetime)
	insert into @toVoid (transactionID, dateRecorded)
	select transactionID, dateRecorded
	from dbo.fn_tr_getRelatedTransactionsToVoid_salesTax(@transactionID)
	ORDER BY dateRecorded desc, transactionID desc	

	SELECT @minAID = null
	SELECT @minAID = min(autoid) FROM @toVoid
	while @minAID is not null BEGIN
		SELECT @minTID = null
		SELECT @minTID = transactionID from @toVoid where autoID = @minAID

		-- checkInBounds should be 0 on these nested voids
		EXEC dbo.tr_voidTransaction @recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID,
			@statsSessionID=@statsSessionID, @transactionID=@minTID, @checkInBounds=0, @vidPool=@vidPool OUTPUT,
			@tids=@tidsdeep OUTPUT
		insert into @tblVoided (transactionid)
		select T.item.value('@tid','int')
		FROM @tidsdeep.nodes('/tr/t') as T(item)

		SELECT @minAID = min(autoid) FROM @toVoid where autoID > @minAID
	END

	-- update invoicetransactions
	UPDATE dbo.tr_invoiceTransactions
	SET cache_invoiceAmountAfterAdjustment = 0,
		cache_ActivePaymentAllocatedAmount = 0,
		cache_PendingPaymentAllocatedAmount = 0
	WHERE transactionID = @transactionID

	-- update cache
	UPDATE dbo.tr_transactionSales
	SET cache_amountAfterAdjustment = 0,
		cache_ActivePaymentAllocatedAmount = 0,
		cache_PendingPaymentAllocatedAmount = 0
	WHERE transactionID = @transactionID


	IF @TranCounter = 0
		COMMIT TRAN;
	SELECT @tids = (SELECT transactionid as tid FROM @tblVoided FOR XML RAW('t'), root('tr'))
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	SELECT @tids = '<tr/>'
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_voidTransaction_writeoff_payment]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@transactionID int,
@vidPool xml OUTPUT,
@tids xml OUTPUT

AS

/* ***********************************************
	DO NOT CALL THIS PROC DIRECTLY!
	THIS SHOULD ONLY BE CALLED FROM WITHIN 
	tr_voidTransaction
************************************************** */
set nocount on

declare @offsetTransactionID int, @statusID int, @minTID int
declare @tblVoided TABLE (transactionid int)
select @statusID = dbo.fn_tr_getStatusID('Voided')
select @tids = null


-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;


BEGIN TRY

	-- create offset trans
	EXEC dbo.tr_createTransaction_voidOffset @recordedOnSiteID=@recordedOnSiteID,
		@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID,
		@appliedToTransactionID=@transactionID, @vidPool=@vidPool OUTPUT,
		@transactionID=@offsetTransactionID OUTPUT
	insert into @tblVoided (transactionid) 
	values (@offsetTransactionID)

	-- update transaction status
	UPDATE dbo.tr_transactions
	SET statusID = @statusID
	WHERE transactionID = @transactionID

	insert into @tblVoided (transactionid) 
	values (@transactionID)

	-- get amount of write off
	declare @woAmt money
	select @woAmt = amount from dbo.tr_transactions where transactionID = @transactionID
		IF @woAmt is null RAISERROR('woAmt is null', 16, 1);

	-- get payment that has been written off
	declare @payTID int
	select @payTID = r.appliedToTransactionID 
		from dbo.tr_relationships as r
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID
			and r.transactionID = @transactionID
			and rt.type = 'WriteOffPayTrans'
		IF @payTID is null RAISERROR('payTID is null', 16, 1);

	-- update cache. Payment writeoffs are shown in the cache_refundableAmountOfPayment field.
	UPDATE dbo.tr_transactionPayments
	SET cache_refundableAmountOfPayment = cache_refundableAmountOfPayment + @woAmt
	WHERE transactionID = @payTID
	

	IF @TranCounter = 0
		COMMIT TRAN;
	SELECT @tids = (SELECT transactionid as tid FROM @tblVoided FOR XML RAW('t'), root('tr'))
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	SELECT @tids = '<tr/>'
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_voidTransaction_writeoff_sale]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@transactionID int,
@vidPool xml OUTPUT,
@tids xml OUTPUT

AS

/* ***********************************************
	DO NOT CALL THIS PROC DIRECTLY!
	THIS SHOULD ONLY BE CALLED FROM WITHIN 
	tr_voidTransaction
************************************************** */
set nocount on

declare @offsetTransactionID int, @statusID int, @minTID int
declare @tblVoided TABLE (transactionid int)
select @statusID = dbo.fn_tr_getStatusID('Voided')
select @tids = null


-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;


BEGIN TRY

	-- create offset trans
	EXEC dbo.tr_createTransaction_voidOffset @recordedOnSiteID=@recordedOnSiteID,
		@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID,
		@appliedToTransactionID=@transactionID, @vidPool=@vidPool OUTPUT,
		@transactionID=@offsetTransactionID OUTPUT
	insert into @tblVoided (transactionid) 
	values (@offsetTransactionID)

	-- update transaction status
	UPDATE dbo.tr_transactions
	SET statusID = @statusID
	WHERE transactionID = @transactionID

	insert into @tblVoided (transactionid) 
	values (@transactionID)

	-- get amount of write off
	declare @woAmt money
	select @woAmt = amount from dbo.tr_transactions where transactionID = @transactionID
		IF @woAmt is null RAISERROR('woAmt is null', 16, 1);

	-- get sale that has been written off
	declare @saleTID int
	select @saleTID = r.appliedToTransactionID 
		from dbo.tr_relationships as r
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID
			and r.transactionID = @transactionID
			and rt.type = 'WriteOffSaleTrans'
		IF @saleTID is null RAISERROR('saleTID is null', 16, 1);

	-- update sales cache
	-- if writing off an adjustment, update cache of sale or tax it is adjusting
	declare @stTypeID int, @stID int, @activePaymentAllocatedAmount money, @pendingPaymentAllocatedAmount money
	select @stTypeID = typeID from dbo.tr_transactions where transactionID = @saleTID 
	IF @stTypeID in (1,7)
		select @stID = @saleTID
	ELSE
		select @stID = tSale.transactionID
			from dbo.tr_transactions as tSale
			inner join dbo.tr_relationships as tR on tR.appliedToTransactionID = tSale.transactionID and tR.transactionID = @saleTID
			inner join dbo.tr_relationshipTypes as trt on trt.typeID = tR.typeID and trt.type = 'AdjustTrans'

	select @activePaymentAllocatedAmount = activePaymentAllocatedAmount,
		@pendingPaymentAllocatedAmount = pendingPaymentAllocatedAmount
	from dbo.fn_tr_getAllocatedAmountofSale(@stID)

	UPDATE dbo.tr_transactionSales
	SET cache_activePaymentAllocatedAmount = @activePaymentAllocatedAmount,
		cache_pendingPaymentAllocatedAmount = @pendingPaymentAllocatedAmount
	WHERE transactionID = @stID

	-- update invoiceTransactions cache
	select @activePaymentAllocatedAmount = null, @pendingPaymentAllocatedAmount = null
	select @activePaymentAllocatedAmount = activePaymentAllocatedAmount,
		@pendingPaymentAllocatedAmount = pendingPaymentAllocatedAmount
	from dbo.fn_tr_getAllocatedAmountofSaleOrAdj(@saleTID,null)

	UPDATE dbo.tr_invoiceTransactions
	SET cache_activePaymentAllocatedAmount = @activePaymentAllocatedAmount,
		cache_pendingPaymentAllocatedAmount = @pendingPaymentAllocatedAmount
	WHERE transactionID = @saleTID

	-- put in the @saleTID for the cleanup invoice routine
	insert into @tblVoided (transactionid)
	values (@saleTID)


	IF @TranCounter = 0
		COMMIT TRAN;
	SELECT @tids = (SELECT transactionid as tid FROM @tblVoided FOR XML RAW('t'), root('tr'))
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	SELECT @tids = '<tr/>'
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO


ALTER PROC [dbo].[tr_createTransaction_voidOffset]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@appliedToTransactionID int,
@vidPool xml OUTPUT,
@transactionID int OUTPUT

AS

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;


BEGIN TRY

	-- init output param
	select @transactionID = 0

	-- ensure @appliedToTransactionID is not voided and not of type VoidOffset
	IF EXISTS (select transactionID from dbo.tr_transactions where transactionID = @appliedToTransactionID and (statusID not in (1,3) or typeID = 8))
		RAISERROR('appliedToTransactionID is voided or is a voidOffset', 16, 1);

	-- dont assume memberid is the active one. get the active one.
	declare @assignedToMemberID int, @ownedByOrgID int
	select @assignedToMemberID = m.activeMemberID, @ownedByOrgID = t.ownedByOrgID
		from dbo.tr_transactions as t
		inner join dbo.ams_members as m on m.memberid = t.assignedToMemberID
		where t.transactionID = @appliedToTransactionID
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	-- insert into transactions with debit/credit accounts switched
	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
		amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
		typeID, accrualDate, debitGLAccountID, creditGLAccountID)
	SELECT top 1 @ownedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID('Active'), detail, null, 
		amount, getdate(), getdate(), @assignedToMemberID, @recordedByMemberID, @statsSessionID, 
		dbo.fn_tr_getTypeID('VoidOffset'), accrualDate, creditGLAccountID, debitGLAccountID
	FROM dbo.tr_transactions
	where transactionID = @appliedToTransactionID
		select @transactionID = SCOPE_IDENTITY()

	-- insert into relationships
	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
	VALUES (dbo.fn_tr_getRelationshipTypeID('OffsetTrans'), @transactionID, @appliedToTransactionID)
	
	declare @typeID int, @nowDate datetime
	select @typeID = typeID from dbo.tr_transactions where transactionID = @appliedToTransactionID
	select @nowDate = getdate()

	/* 
	Put on invoice if voiding 1,3,7
	1. put VO on same invoice as Transaction if invoice is open/pending.
	2. put VO on first invoice from vidpool that matches invoice profile.
	3. put VO on new invoice if necessary
	*/
	declare @originvoiceID int, @originvoiceProfileID int, @origInvoiceStatusID int, @invoiceID int, 
		@invoiceNumber varchar(18)
	IF @typeID in (1,3,7) BEGIN
		select @originvoiceid = it.invoiceid, @originvoiceProfileID = i.invoiceProfileID,
			@origInvoiceStatusID = i.statusID
			from dbo.tr_invoiceTransactions as it
			inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
			where it.transactionID = @appliedToTransactionID

		-- case 1
		IF @origInvoiceStatusID in (1,2) begin
			select @invoiceID = @originvoiceid
			select @vidPool = (
				select [id]
				from (
					select @invoiceID as [id]
						union				
					select V.item.value('@id','int')
					FROM @vidPool.nodes('/vpool/v') as V(item)
				) as tmp
				FOR XML RAW('v'), root('vpool')
			)
		end else begin
			-- case 2
			select top 1 @invoiceID = i.invoiceID
				FROM @vidPool.nodes('/vpool/v') as V(item)
				inner join dbo.tr_invoices as i on i.invoiceID = V.item.value('@id','int')
				where i.invoiceProfileID = @originvoiceProfileID
				order by i.invoiceid

			-- case 3
			if @invoiceID is null begin
				EXEC dbo.tr_createInvoice @invoiceProfileID=@originvoiceProfileID, 
					@enteredByMemberID=@recordedByMemberID, @assignedToMemberID=@assignedToMemberID, 
					@dateBilled=@nowDate, @dateDue=@nowdate, 
					@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT
				select @vidPool = (
					select [id]
					from (
						select @invoiceID as [id]
							union				
						select V.item.value('@id','int')
						FROM @vidPool.nodes('/vpool/v') as V(item)
					) as tmp
					FOR XML RAW('v'), root('vpool')
				)
			end
		end

		INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount)
		VALUES (@transactionID, @invoiceID, 0, 0, 0)
	END

	/* 
	Put on batch if voiding 2,4,9,5,6 of Pay
	1. put VO on same batch as Transaction if batch is Open
	2. else put VO on daily _VD batch 
	-- if credit acct of this VO transaction is DEPOSITS, it is a VO of a Negative Write Off
	*/
	declare @isWOPayment bit, @batchID int, @batchCode varchar(40), @batchName varchar(400),
		@batchPayProfileID int, @transactionDate datetime
	
	IF EXISTS (
		select t.transactionID
		from dbo.tr_transactions as t
		inner join dbo.tr_GLAccounts as glCred on glCred.GLAccountID = t.creditGLAccountID
		where t.transactionID = @transactionID
		and glCred.GLCode = 'DEPOSITS' 
		and glCred.isSystemAccount = 1
		and glCred.status = 'A'
		) 
		SELECT @isWOPayment = 1
	ELSE 	
		SELECT @isWOPayment = 0
	
	IF @typeID in (2,4,9,5) OR (@typeID = 6 and @isWOPayment = 1) BEGIN
		select @batchID = b.batchID
			from dbo.tr_batchTransactions as bt
			inner join dbo.tr_batches as b on b.batchID = bt.batchID
			where bt.transactionID = @appliedToTransactionID
			and b.statusID = 1
		IF @batchID is null BEGIN
			select TOP 1 
				@batchPayProfileID = tmp.BatchPayProfileID,
				@batchCode = CONVERT(CHAR(8),@nowDate,112) + '_' + cast(tmp.BatchPayProfileID as varchar(10)) + '_' + cast(tmp.BatchCashGLAccountID as varchar(10)) + '_VD',
				@batchName = CONVERT(CHAR(8),@nowDate,112) + ' ' + mp.profileCode + ' ' + gl.accountName + ' Voids'
			from (
				select 
					case 
					when tVoided.typeID = 2 then tVoided.debitGLAccountID
					when tVoided.typeID = 4 then tVoided.creditGLAccountID
					when tVoided.typeID = 9 then tVoided.creditGLAccountID
					when tVoided.typeID = 5 then tPay.debitGLAccountID
					when tVoided.typeID = 6 then tPay3.debitGLAccountID
					else null
					end as BatchCashGLAccountID,
					case
					when tVoided.typeID = 2 then tpV.profileID
					when tVoided.typeID = 4 then tpV.profileID
					when tVoided.typeID = 9 then tp2.profileID
					when tVoided.typeID = 5 then tp.profileID
					when tVoided.typeID = 6 then tp3.profileID
					else null
					end as BatchPayProfileID
				from dbo.tr_transactions as tVoided
				left outer join dbo.tr_transactionPayments as tpV on tpV.transactioniD = tVoided.transactionID and tVoided.typeID in (2,4)
				left outer join dbo.tr_relationships as tr2
					inner join dbo.tr_transactions as tPay on tPay.transactionID = tr2.appliedToTransactionID and tPay.typeID = 2
					inner join dbo.tr_transactionPayments as tp on tp.transactioniD = tPay.transactionID
					on tr2.transactionID = tVoided.transactionID and tr2.typeID = 2 and tVoided.typeID = 5
				left outer join dbo.tr_relationships as tr3
					inner join dbo.tr_transactions as tPay2 on tPay2.transactionID = tr3.appliedToTransactionID and tPay2.typeID = 2
					inner join dbo.tr_transactionPayments as tp2 on tp2.transactioniD = tPay2.transactionID
					on tr3.transactionID = tVoided.transactionID and tr3.typeID = 13 and tVoided.typeID = 9
				left outer join dbo.tr_relationships as tr4
					inner join dbo.tr_transactions as tPay3 on tPay3.transactionID = tr4.appliedToTransactionID and tPay3.typeID = 2
					inner join dbo.tr_transactionPayments as tp3 on tp3.transactioniD = tPay3.transactionID
					on tr4.transactionID = tVoided.transactionID and tr4.typeID = 7 and tVoided.typeID = 6
				where tVoided.transactionID = @AppliedToTransactionID
			) as tmp		
			inner join dbo.tr_glAccounts as gl on gl.glAccountID = tmp.BatchCashGLAccountID
			inner join dbo.mp_profiles as mp on mp.profileID = tmp.BatchPayProfileID
		
			select @batchID = b.batchID
				from dbo.tr_batches as b
				where b.orgID = @ownedByOrgID
				and b.batchCode = @batchCode
				and b.statusID = 1
				and b.isSystemCreated = 1
			IF @batchID is null
				EXEC dbo.tr_createBatch @orgID=@ownedByOrgID, @payProfileID=@batchPayProfileID, 
					@batchCode=@batchCode, @batchName=@batchName, @controlAmt=0, @controlCount=0, 
					@depositDate=@nowDate, @isSystemCreated=1, @createdByMemberID=null, @batchID=@batchID OUTPUT 
		END

		INSERT INTO dbo.tr_batchTransactions (batchID, transactionID)
		VALUES (@batchID, @transactionID)
	END


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @transactionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO


ALTER PROC [dbo].[tr_writeOffSale]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@amount money,
@transactionDate datetime,
@saleTransactionID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- amt needs to be positive.
	IF @amount <= 0
		RAISERROR('amount is not greater than 0', 16, 1);

	-- dont assume memberid is the active one. get the active one.
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members WITH(NOLOCK)
		where memberID = @recordedByMemberID 

	-- ensure amount is 2 decimals
	select @amount = cast(@amount as decimal(10,2))

	-- use numeric(10,2) instead of money to prevent 4 digit money values
	-- get sale, tax, and adjustments
	declare @tblOpenTran TABLE (autoid int IDENTITY(1,1), transactionid int, maxAmtToWriteOff numeric(10,2), PITSaleTID int, PITWOAmt numeric(10,2), newWOTID int)

	;with innerTbl as (
		select t.transactionID, ts.cache_amountAfterAdjustment - ts.cache_activePaymentAllocatedAmount as salesDue
		from dbo.tr_transactions as t
		inner join dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID
		where t.typeID = 1
		and t.statusID = 1
		and t.transactionID = @saleTransactionID
			union all
		select t.transactionID, ts.cache_amountAfterAdjustment - ts.cache_activePaymentAllocatedAmount as salesDue
		from dbo.tr_transactions as t
		inner join dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID
		inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'SalesTaxTrans'
		inner join innerTbl on innerTbl.transactionID = r.appliedToTransactionID
		where t.typeID = 7
		and t.statusID = 1
			union all
		select t.transactionID, it.cache_invoiceAmountAfterAdjustment - it.cache_activePaymentAllocatedAmount as salesDue
		from dbo.tr_transactions as t
		inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
		inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
		inner join innerTbl on innerTbl.transactionID = r.appliedToTransactionID
		where t.typeID = 3
		and t.statusID = 1
	)	
	insert into @tblOpenTran (transactionID, maxAmtToWriteOff)
	select it.transactionID, sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) as maxAmtToWriteOff
	from dbo.tr_invoiceTransactions as it
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	inner join innerTbl on innerTbl.transactionID = it.transactionid
	where innerTbl.salesDue > 0
	group by it.transactionID
	having sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) > 0
	order by it.transactionID

	-- group by PITTaxTrans. sales and adj to sales just put the transactionid
	update tbl
	set tbl.PITSaleTID = tbl.transactionID
	from @tblOpenTran as tbl
	inner join dbo.tr_transactions as t on t.transactionID = tbl.transactionID and t.typeID = 1

	update tbl
	set tbl.PITSaleTID = tbl.transactionID
	from @tblOpenTran as tbl
	inner join dbo.tr_transactions as t on t.transactionID = tbl.transactionID and t.typeID = 3
	inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
	inner join dbo.tr_transactions as tSale on tSale.transactionID = r.appliedToTransactionID and tSale.typeID = 1
	where tbl.PITSaleTID is null

	update tbl
	set tbl.PITSaleTID = tbl2.transactionID
	from @tblOpenTran as tbl
	inner join dbo.tr_relationships as r on r.transactionID = tbl.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'PITTaxTrans'
	inner join @tblOpenTran as tbl2 on tbl2.transactionID = r.appliedToTransactionID
	where tbl.PITSaleTID is null

	-- did we miss any PITTaxTrans records?
	IF EXISTS (select autoid from @tblOpenTran where PITSaleTID is null)
		RAISERROR('Records are missing PITSaleTID', 16, 1);

	-- loop over each group to get write off amount and % split
	declare @totalAmountLeftToWriteOff money, @grpTID int, @grpMaxAmtToWriteOff money, @w_amount money
	select @totalAmountLeftToWriteOff = @amount
	select @grpTID = min(PITSaleTID) from @tblOpenTran
	while @grpTID is not null BEGIN

		-- how much can we writeoff to this group of transactions?
		select @grpMaxAmtToWriteOff = sum(maxAmtToWriteOff) from @tblOpenTran where PITSaleTID = @grpTID
		IF @totalAmountLeftToWriteOff > @grpMaxAmtToWriteOff
			select @w_amount = @grpMaxAmtToWriteOff
		ELSE
			select @w_amount = @totalAmountLeftToWriteOff

		-- calc amount to write off for each transaction
		update @tblOpenTran
		set PITWOAmt = case 
						when @w_amount = 0 then 0 
						else ((maxAmtToWriteOff/ @grpMaxAmtToWriteOff) * @w_amount) 
						end
		WHERE PITSaleTID = @grpTID

		-- handle remainders from percentage calculations
		-- put any diff in the 1st item that can accept it
		declare @sumWriteOffAmount numeric(10,2), @amountDiff numeric(10,2)
		select @sumWriteOffAmount = sum(PITWOAmt) from @tblOpenTran WHERE PITSaleTID = @grpTID
		IF @sumWriteOffAmount <> abs(@w_amount) BEGIN
			select @amountDiff = abs(@w_amount) - @sumWriteOffAmount
	
			update top (1) @tblOpenTran
			set PITWOAmt = PITWOAmt + @amountDiff
			where PITWOAmt + @amountDiff <= maxAmtToWriteOff
		END

		select @totalAmountLeftToWriteOff = @totalAmountLeftToWriteOff - @w_amount
		if @totalAmountLeftToWriteOff <= 0
			break

		select @grpTID = min(PITSaleTID) from @tblOpenTran where PITSaleTID > @grpTID
	END

	-- loop over and make write offs
	declare @minAutoID int, @w_TID int, @newWriteOffTID int
	select @minAutoID = min(autoID) from @tblOpenTran where PITWOAmt > 0
	while @minAutoID is not null BEGIN
		select @w_amount=PITWOAmt, @w_TID=transactionID
			from @tblOpenTran
			where autoID = @minAutoID

		EXEC dbo.tr_createTransaction_writeoff_sale @recordedOnSiteID=@recordedOnSiteID, 
			@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
			@status='Active', @amount=@w_amount, @transactionDate=@transactionDate, 
			@saleTransactionID=@w_TID, @transactionID=@newWriteOffTID OUTPUT

		update @tblOpenTran
		set newWOTID = @newWriteOffTID
		where autoID = @minAutoID

		select @minAutoID = min(autoID) from @tblOpenTran where PITWOAmt > 0 and autoID > @minAutoID
	END	

	-- insert WriteOffTaxTrans relationships
	IF EXISTS (select autoid from @tblOpenTran where PITSaleTID <> transactionID) BEGIN
		INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
		select dbo.fn_tr_getRelationshipTypeID('WriteOffTaxTrans'), tbl.newWOTID, tbl2.newWOTID
		from @tblOpenTran as tbl
		inner join @tblOpenTran as tbl2 on tbl2.transactionID = tbl.PITSaleTID
		where tbl.PITSaleTID <> tbl.transactionID
		and tbl.newWOTID is not null
		and tbl2.newWOTID is not null
	END	


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO


ALTER PROC [dbo].[tr_createTransaction_writeoff_sale]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@status varchar(20),
@amount money,
@transactionDate datetime,
@saleTransactionID int, -- this can be a sale or adjustment TID
@transactionID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- init output param
	select @transactionID = 0

	-- no zero or negative dollar writeoffs
	if @amount <= 0
		RAISERROR('amount is not greater than 0', 16, 1);

	-- ensure amount is 2 decimals
	select @amount = cast(@amount as decimal(10,2))

	-- ensure @saleTransactionID is a non-voided sale, sales tax, or positive adjustment
	IF NOT EXISTS (
		select t.transactionID
		from dbo.tr_transactions as t
		inner join dbo.tr_glAccounts as glDeb on glDeb.GLAccountID = t.debitGLAccountID
		where t.transactionID = @saleTransactionID
		and t.typeID in (1,3,7)
		and t.statusID = 1
		and glDeb.GLCode = 'ACCOUNTSRECEIVABLE' 
		and glDeb.isSystemAccount = 1
	)
		RAISERROR('saleTransactionID is not a non-voided sale, sales tax, or positive adjustment', 16, 1);

	-- get assignedToMemberID from sale transaction
	declare @assignedToMemberID int, @ownedByOrgID int, @detail varchar(max)
	select @assignedToMemberID = assignedToMemberID, @ownedByOrgID = ownedByOrgID, @detail = detail
		from dbo.tr_transactions 
		where transactionID = @saleTransactionID

	-- dont assume memberid is the active one. get the active one.
	select @assignedToMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @assignedToMemberID 
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	-- WO is debit acct, AR is credit acct
	declare @debitGLAccountID int, @creditGLAccountID int
	select @debitGLAccountID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and isSystemAccount = 1
		and GLCode = 'WRITEOFF'
		and [status] = 'A'
	select @creditGLAccountID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and isSystemAccount = 1
		and GLCode = 'ACCOUNTSRECEIVABLE'
		and [status] = 'A'

	-- ensure we have active debit/credit accts
	IF @debitGLAccountID is null or @creditGLAccountID is null
		RAISERROR('debitGLAccountID or creditGLAccountID is blank', 16, 1);

	-- if writing off a sale on an open/pending invoice, close it. Only closed inv can write offs.
	declare @invoiceID int, @invstatus varchar(50)
	select @invoiceID=i.invoiceID, @invstatus=ins.status
		from dbo.tr_invoices as i
		inner join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
		inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
		where it.transactionID = @saleTransactionID
	IF @invstatus <> 'Closed' 
		EXEC dbo.tr_closeInvoice @enteredByMemberID=@recordedByMemberID, @invoiceIDList=@invoiceID
	
	-- insert into transactions
	-- ensure amount is abs
	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
		amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
		typeID, accrualDate, debitGLAccountID, creditGLAccountID)
	VALUES (@ownedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID(@status), @detail, null, 
		abs(@amount), getdate(), @transactionDate, @assignedToMemberID, @recordedByMemberID, @statsSessionID, 
		6, @transactionDate, @debitGLAccountID, @creditGLAccountID)
	select @transactionID = SCOPE_IDENTITY()

	-- insert into relationships
	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
	VALUES (dbo.fn_tr_getRelationshipTypeID('WriteOffSaleTrans'), @transactionID, @saleTransactionID)

	-- update sales cache
	-- if writing off an adjustment, update cache of sale or tax it is adjusting
	declare @stTypeID int, @stID int, @activePaymentAllocatedAmount money, @pendingPaymentAllocatedAmount money
	select @stTypeID = typeID from dbo.tr_transactions where transactionID = @saleTransactionID 
	IF @stTypeID in (1,7)
		select @stID = @saleTransactionID
	ELSE
		select @stID = tSale.transactionID
		from dbo.tr_transactions as tSale
		inner join dbo.tr_relationships as tR on tR.appliedToTransactionID = tSale.transactionID and tR.transactionID = @saleTransactionID
		inner join dbo.tr_relationshipTypes as trt on trt.typeID = tR.typeID and trt.type = 'AdjustTrans'

	select @activePaymentAllocatedAmount = activePaymentAllocatedAmount,
		@pendingPaymentAllocatedAmount = pendingPaymentAllocatedAmount
		from dbo.fn_tr_getAllocatedAmountofSale(@stID)
	UPDATE dbo.tr_transactionSales
	SET cache_activePaymentAllocatedAmount = @activePaymentAllocatedAmount,
		cache_pendingPaymentAllocatedAmount = @pendingPaymentAllocatedAmount
	WHERE transactionID = @stID

	-- update invoiceTransactions cache
	select @activePaymentAllocatedAmount = null, @pendingPaymentAllocatedAmount = null
	select @activePaymentAllocatedAmount = activePaymentAllocatedAmount,
		@pendingPaymentAllocatedAmount = pendingPaymentAllocatedAmount
		from dbo.fn_tr_getAllocatedAmountofSaleOrAdj(@saleTransactionID,null)
	UPDATE dbo.tr_invoiceTransactions
	SET cache_activePaymentAllocatedAmount = @activePaymentAllocatedAmount,
		cache_pendingPaymentAllocatedAmount = @pendingPaymentAllocatedAmount
	WHERE transactionID = @saleTransactionID

	-- check the in-bound rules.
	-- sale - new cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount must be between 0 and cache_amountAfterAdjustment
	IF NOT EXISTS (select saleID from dbo.tr_transactionSales where transactionID = @stID and cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount between 0 and cache_amountAfterAdjustment)
		OR NOT EXISTS (select itID from dbo.tr_invoiceTransactions where transactionID = @saleTransactionID and cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount between 0 and cache_invoiceAmountAfterAdjustment)
		RAISERROR('in-bound checks failed', 16, 1);

	-- cleanup invoice
	-- if invoice is closed and is now fully paid with active payments, mark it as paid
	declare @amtDueNoPendingOnInvoice money
	select @invoiceID=i.invoiceID, @invstatus=ins.status
		from dbo.tr_invoices as i
		inner join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
		inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
		where it.transactionID = @saleTransactionID
	select @amtDueNoPendingOnInvoice = sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount)
		from dbo.tr_invoiceTransactions as it
		inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
		where it.invoiceID = @invoiceID
		and t.statusID <> 2
	IF @invstatus = 'closed' and @amtDueNoPendingOnInvoice = 0 BEGIN
		update dbo.tr_invoices
		set statusID = 4, payProfileID = null
		where invoiceID = @invoiceID
		
		insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
		values (@invoiceID, getdate(), 4, 3, @recordedByMemberID)
	END


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @transactionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO


ALTER PROC [dbo].[tr_closeInvoice]
@enteredByMemberID int,
@invoiceIDList varchar(max)

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	DECLARE @tblInvoices TABLE (invoiceID int, statusID int, amtDueNoPendingOnInvoice money)

	-- split invoiceIDs
	INSERT INTO @tblInvoices (invoiceID, statusID, amtDueNoPendingOnInvoice)
	select i.invoiceID, i.statusID, isnull(sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount),0) as amtDueNoPendingOnInvoice
	from dbo.fn_intListToTable(@invoiceIDList,',') as tmp
	inner join dbo.tr_invoices as i on i.invoiceID = tmp.listitem
	left outer join dbo.tr_invoiceTransactions as it 
		inner join dbo.tr_transactions as t on t.transactionID = it.transactionID and t.statusID <> 2
		on it.invoiceID = i.invoiceID
	group by i.invoiceID, i.statusID

	-- if currently open/pending, set to closed.
	UPDATE tr
	set tr.statusID = 3
	from dbo.tr_invoices as tr
	inner join @tblInvoices as tbl on tbl.invoiceID = tr.invoiceID and tbl.statusID in (1,2)

	insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
	select invoiceID, getdate(), 3, statusID, @enteredByMemberID
	from @tblInvoices
	where statusID in (1,2)

	update @tblInvoices
	set statusID = 3
	where statusID in (1,2)

	-- if invoice will be closed and is now fully paid with active payments, set to paid and clear the PayProfileID
	UPDATE tr
	set tr.statusID = 4, 
		tr.payProfileID = null
	from dbo.tr_invoices as tr
	inner join @tblInvoices as tbl on tbl.invoiceID = tr.invoiceID and tbl.statusID = 3 and tbl.amtDueNoPendingOnInvoice = 0

	insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
	select invoiceID, getdate(), 4, statusID, @enteredByMemberID
	from @tblInvoices
	where statusID = 3
	and amtDueNoPendingOnInvoice = 0


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_createTransaction_allocation]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@status varchar(20),
@amount money,
@transactionDate datetime,
@paymentTransactionID int,
@saleTransactionID int,	-- this can be a sale or adjustment TID
@transactionID int OUTPUT

AS

set nocount on

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- reset output param
	select @transactionID = 0

	-- no zero dollar allocations
	if @amount = 0
		RAISERROR('amount is 0', 16, 1);

	-- ensure amount is 2 decimals
	select @amount = cast(@amount as decimal(10,2))

	-- ensure @paymentTransactionID is a payment
	IF NOT EXISTS (select transactionID from dbo.tr_transactions where transactionID = @paymentTransactionID and typeID = 2)
		RAISERROR('paymentTransactionID is not a payment', 16, 1);

	-- ensure @saleTransactionID is either a sale, sales tax, or positive adjustment
	IF NOT EXISTS (
		select t.transactionID
		from dbo.tr_transactions as t
		inner join dbo.tr_glAccounts as glDeb on glDeb.GLAccountID = t.debitGLAccountID
		where t.transactionID = @saleTransactionID
		and t.typeID in (1,3,7)
		and glDeb.GLCode = 'ACCOUNTSRECEIVABLE' 
		and glDeb.isSystemAccount = 1
		)
	RAISERROR('saleTransactionID is not a sale, sales tax, or positive adjustment', 16, 1);

	-- get info from payment transaction
	declare @assignedToMemberID int, @ownedByOrgID int, @payProfileID int, @payProfileCode varchar(20),
		@payCashGLID int, @payCashGLName varchar(200)
	select @assignedToMemberID = t.assignedToMemberID, 
		@ownedByOrgID = t.ownedByOrgID,
		@payProfileID = mp.profileID,
		@payProfileCode = mp.profileCode,
		@payCashGLID = glDeb.GLAccountID,
		@payCashGLName = glDeb.AccountName
	from dbo.tr_transactions as t
	inner join dbo.tr_transactionPayments as tp on tp.transactionID = t.transactionID
	inner join dbo.mp_profiles as mp on mp.profileID = tp.profileID
	inner join dbo.tr_GLAccounts as glDeb on glDeb.GLAccountID = t.debitGLAccountID
	where t.transactionID = @paymentTransactionID

	-- dont assume memberid is the active one. get the active one.
	select @assignedToMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @assignedToMemberID 
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	-- if amount is positive, allocating. Debit is DEP, Credit is AR.
	-- if amount is negative, deallocating. Debit is AR, Credit is DEP.
	declare @debitGLAccountID int, @creditGLAccountID int, @ARGLAID int, @DEPGLAID int
	select @ARGLAID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and isSystemAccount = 1
		and GLCode = 'ACCOUNTSRECEIVABLE'
		and [status] = 'A'
	select @DEPGLAID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and isSystemAccount = 1
		and GLCode = 'DEPOSITS'
		and [status] = 'A'
	if @amount > 0 BEGIN
		select @debitGLAccountID = @DEPGLAID
		select @creditGLAccountID = @ARGLAID
	END
	ELSE BEGIN
		select @debitGLAccountID = @ARGLAID
		select @creditGLAccountID = @DEPGLAID
	END

	-- ensure we have active debit/credit accts
	IF @debitGLAccountID is null or @creditGLAccountID is null
		RAISERROR('debitGLAccountID or creditGLAccountID is null', 16, 1);

	-- if allocating to a sale on an open/pending invoice, close it. Only closed inv can accept payment.
	declare @invoiceID int, @invstatus varchar(50)
	select @invoiceID=i.invoiceID, @invstatus=ins.status
		from dbo.tr_invoices as i
		inner join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
		inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
		where it.transactionID = @saleTransactionID
	IF @amount > 0 AND @invstatus <> 'Closed'
		EXEC dbo.tr_closeInvoice @enteredByMemberID=@recordedByMemberID, @invoiceIDList=@invoiceID

	-- insert into transactions
	-- ensure amount is abs
	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
		amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
		typeID, accrualDate, debitGLAccountID, creditGLAccountID)
	VALUES (@ownedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID(@status), null, null, 
		abs(@amount), getdate(), @transactionDate, @assignedToMemberID, @recordedByMemberID, @statsSessionID, 
		5, @transactionDate, @debitGLAccountID, @creditGLAccountID)
		select @transactionID = SCOPE_IDENTITY()

	-- insert into relationships
	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
	VALUES (dbo.fn_tr_getRelationshipTypeID('AllocPayTrans'), @transactionID, @paymentTransactionID)

	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
	VALUES (dbo.fn_tr_getRelationshipTypeID('AllocSaleTrans'), @transactionID, @saleTransactionID)

	-- add allocation to batch
	-- put on same batch as payment if that batch is open.
	-- otherwise, put on daily exception batch
	declare @allocBatchID int, @batchCode varchar(40), @batchName varchar(400)
	select @allocBatchID = b.batchID
		from dbo.tr_batchTransactions as bt
		inner join dbo.tr_batches as b on b.batchID = bt.batchID
		where bt.transactionID = @paymentTransactionID
		and b.statusID = 1
	IF @allocBatchID is null BEGIN
		select @batchCode = CONVERT(CHAR(8),@transactionDate,112) + '_' + cast(@payProfileID as varchar(10)) + '_' + cast(@payCashGLID as varchar(10)) + '_EX'
		select @batchName = CONVERT(CHAR(8),@transactionDate,112) + ' ' + @payProfileCode + ' ' + @payCashGLName + ' Exceptions'
		select @allocBatchID = b.batchID
			from dbo.tr_batches as b
			where b.orgID = @ownedByOrgID
			and b.batchCode = @batchCode
			and b.statusID = 1
			and b.isSystemCreated = 1
		IF @allocBatchID is null 
			EXEC dbo.tr_createBatch @orgID=@ownedByOrgID, @payProfileID=@payProfileID, @batchCode=@batchCode, 
				@batchName=@batchName, @controlAmt=0, @controlCount=0, @depositDate=@transactionDate, 
				@isSystemCreated=1, @createdByMemberID=null, @batchID=@allocBatchID OUTPUT 
	END
	INSERT INTO dbo.tr_batchTransactions (batchID, transactionID)
	VALUES (@allocBatchID, @transactionID)

	-- update payment cache	
	declare @refundableAmount money, @allocatedAmount money
	select @refundableAmount = refundableAmount from dbo.fn_tr_getRefundableAmountofPayment(@paymentTransactionID,null)
	select @allocatedAmount = allocatedAmount from dbo.fn_tr_getAllocatedAmountofPayment(@paymentTransactionID,null)

	update dbo.tr_transactionPayments
	set cache_allocatedAmountOfPayment = @allocatedAmount,
		cache_refundableAmountOfPayment = @refundableAmount
	where transactionID = @paymentTransactionID

	-- update sales cache
	-- if allocating to an adjustment, update cache of sale or tax it is adjusting
	declare @stTypeID int, @stID int, @activePaymentAllocatedAmount money, @pendingPaymentAllocatedAmount money
	select @stTypeID = typeID from dbo.tr_transactions where transactionID = @saleTransactionID 
	IF @stTypeID in (1,7)
		select @stID = @saleTransactionID
	ELSE
		select @stID = tSale.transactionID
			from dbo.tr_transactions as tSale
			inner join dbo.tr_relationships as tR on tR.appliedToTransactionID = tSale.transactionID and tR.transactionID = @saleTransactionID
			inner join dbo.tr_relationshipTypes as trt on trt.typeID = tR.typeID and trt.type = 'AdjustTrans'

	select @activePaymentAllocatedAmount = activePaymentAllocatedAmount,
		@pendingPaymentAllocatedAmount = pendingPaymentAllocatedAmount
	from dbo.fn_tr_getAllocatedAmountofSale(@stID)

	UPDATE dbo.tr_transactionSales
	SET cache_activePaymentAllocatedAmount = @activePaymentAllocatedAmount,
		cache_pendingPaymentAllocatedAmount = @pendingPaymentAllocatedAmount
	WHERE transactionID = @stID

	-- update invoiceTransactions cache
	select @activePaymentAllocatedAmount = null, @pendingPaymentAllocatedAmount = null
	select @activePaymentAllocatedAmount = activePaymentAllocatedAmount,
		@pendingPaymentAllocatedAmount = pendingPaymentAllocatedAmount
	from dbo.fn_tr_getAllocatedAmountofSaleOrAdj(@saleTransactionID,null)
	
	UPDATE dbo.tr_invoiceTransactions
	SET cache_activePaymentAllocatedAmount = @activePaymentAllocatedAmount,
		cache_pendingPaymentAllocatedAmount = @pendingPaymentAllocatedAmount
	WHERE transactionID = @saleTransactionID

	-- check the in-bound rules.
	-- sale - new cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount must be between 0 and cache_amountAfterAdjustment
	-- payment - new cache_allocatedAmountOfPayment must be between 0 and cache_refundableAmountOfPayment
	IF NOT EXISTS (select saleID from dbo.tr_transactionSales where transactionID = @stID and cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount between 0 and cache_amountAfterAdjustment)
		OR NOT EXISTS (select itID from dbo.tr_invoiceTransactions where transactionID = @saleTransactionID and cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount between 0 and cache_invoiceAmountAfterAdjustment)
		OR NOT EXISTS (select paymentID from dbo.tr_transactionPayments where transactionID = @paymentTransactionID and cache_allocatedAmountOfPayment between 0 and cache_refundableAmountOfPayment)
		RAISERROR('in-bounds checking failed', 16, 1);

	-- cleanup invoice
	-- if invoice is closed and is now fully paid with active payments, mark it as paid
	-- if invoice is paid and is now not fully paid with active payments, mark it as closed
	declare @amtDueNoPendingOnInvoice money
	select @invoiceID=i.invoiceID, @invstatus=ins.status
		from dbo.tr_invoices as i
		inner join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
		inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
		where it.transactionID = @saleTransactionID
	select @amtDueNoPendingOnInvoice = sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount)
		from dbo.tr_invoiceTransactions as it
		inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
		where it.invoiceID = @invoiceID
		and t.statusID <> 2
	IF @invstatus = 'closed' and @amtDueNoPendingOnInvoice = 0 BEGIN
		update dbo.tr_invoices
		set statusID = 4, payProfileID = null
		where invoiceID = @invoiceID
		
		insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
		values (@invoiceID, getdate(), 4, 3, @recordedByMemberID)
	END
	IF @invstatus = 'paid' and @amtDueNoPendingOnInvoice > 0 BEGIN
		update dbo.tr_invoices
		set statusID = 3
		where invoiceID = @invoiceID

		insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
		values (@invoiceID, getdate(), 3, 4, @recordedByMemberID)
	END


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @transactionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_autoMarkClosedInvoicesAsPaid]

AS

set nocount on

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- closed invoices with nothing due - these should be marked as paid
	declare @MCsystemMemberID int, @invoiceIDList varchar(max)
	select @MCsystemMemberID = memberID from dbo.ams_members where memberNumber = 'SYSTEM' and orgID = 1

	select @invoiceIDList = COALESCE(@invoiceIDList + ',', '') + cast(i.invoiceID as varchar(20)) 
	from dbo.tr_invoices as i
	left outer join dbo.tr_invoiceTransactions as it 
		inner join dbo.tr_transactions as t on t.transactionID = it.transactionID and t.statusID <> 2
		on it.invoiceID = i.invoiceID
	where i.statusID = 3
	group by i.invoiceid
	having isnull(sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount),0) = 0

	IF @invoiceIDList is not null
		EXEC dbo.tr_closeInvoice @enteredByMemberID=@MCsystemMemberID, @invoiceIDList=@invoiceIDList


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[sub_removeSubscriber]
@subscriberID int,
@memberID int,
@siteID int,
@enteredByMemberID int,
@statsSessionID int,
@AROption char(1)

AS

set nocount on

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @ApplicationTypeID int, @nowdate datetime, @minSubID int, @updateResult int, @invoiceID int,
		@invoiceNumber varchar(18), @minTID int, @adjAmount money, @adjtransactionID int,
		@autoCloseInvoice bit, @invoiceProfileID int
	DECLARE @tblAdjust TABLE (transactionID int, amountToAdjust money)
	set @autoCloseInvoice = 0

	select @ApplicationTypeID = dbo.fn_getApplicationTypeIDFromName('Admin')
	select @nowdate = getdate()

	declare @tblSubs TABLE (subscriberID int, thePathExpanded varchar(max))
	insert into @tblSubs (subscriberID, thePathExpanded)
	select subscriberID, thePathExpanded
	from dbo.fn_getRecursiveMemberSubscriptions(@memberID,@siteID,@subscriberID)
	where status <> 'D'

	select @minSubID = min(subscriberID) from @tblSubs
	while @minSubID is not null BEGIN
		EXEC dbo.sub_updateSubscriberStatus @subscriberID=@minSubID, @newStatusCode='D', @siteID=@siteID, 
			@enteredByMemberID=@enteredByMemberID, @result=@updateResult OUTPUT
			IF @updateResult < 0 RAISERROR('sub_updateSubscriberStatus returned %i', 16, 1,@updateResult);

		IF @AROption = 'A' BEGIN
			INSERT INTO @tblAdjust (transactionID, amountToAdjust)
			select rt.mainTransactionID, ts.cache_amountAfterAdjustment
			from dbo.fn_sub_subscriberTransactions(@minSubID) as rt
			inner join dbo.tr_transactionSales as ts on ts.transactionID = rt.transactionID
				and ts.cache_amountAfterAdjustment > 0
				and rt.typeID = 1
		END
		IF @AROption = 'B' BEGIN
			INSERT INTO @tblAdjust (transactionID, amountToAdjust)
			select rt.mainTransactionID, ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount-ts.cache_pendingPaymentAllocatedAmount
			from dbo.fn_sub_subscriberTransactions(@minSubID) as rt
			inner join dbo.tr_transactionSales as ts on ts.transactionID = rt.transactionID
				and ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount-ts.cache_pendingPaymentAllocatedAmount > 0
				and rt.typeID = 1
		END

		UPDATE dbo.tr_applications
		SET [status] = 'D'
		WHERE itemID = @minSubID
		AND itemType = 'Dues'
		AND applicationTypeID = @ApplicationTypeID
		AND [status] <> 'D'

		select @minSubID = min(subscriberID) from @tblSubs where subscriberID > @minSubID
	END

	-- if there are adjustments to make
	IF EXISTS (select transactionID from @tblAdjust) BEGIN

		-- if any transactions are on a open or pending invoice, grab it for all adjustments. 
		-- otherwise, we need to create one to hold these adjustments
		select top 1 @invoiceID = i.invoiceID, @invoiceProfileID=i.invoiceProfileID
			from dbo.tr_invoices as i
			inner join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
			inner join @tblAdjust as tbl on tbl.transactionID = it.transactionID
			and i.statusID in (1,2)
			order by i.invoiceID
		IF @invoiceID is null BEGIN
			EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@enteredByMemberID,
				@assignedToMemberID=@memberID, @dateBilled=@nowdate, @dateDue=@nowdate, 
				@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT
			set @autoCloseInvoice = 1
		END

		-- record adjustments
		SELECT @minTID = min(transactionID) from @tblAdjust
		WHILE @minTID IS NOT NULL BEGIN
			SELECT @adjAmount = amountToAdjust*-1 from @tblAdjust where transactionID = @minTID
			
			EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteID,
				@recordedByMemberID=@enteredByMemberID, @statsSessionID=@statsSessionID,
				@status='Active', @amount=@adjAmount, @transactionDate=@nowdate,
				@saleTransactionID=@minTID, @invoiceID=@invoiceID, @transactionID=@adjtransactionID OUTPUT
			
			SELECT @minTID = min(transactionID) from @tblAdjust where transactionID > @minTID
		END

		IF @autoCloseInvoice = 1
			EXEC dbo.tr_closeInvoice @enteredByMemberID=@enteredByMemberID, @invoiceIDList=@invoiceID			
	END

	-- return query of subs
	select * from @tblSubs


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO


ALTER PROC [dbo].[sub_expireSubscriber]
@subscriberID int,
@memberID int,
@siteID int,
@enteredByMemberID int,
@statsSessionID int,
@AROption char(1),
@fReturnQuery bit=1

AS

set nocount on

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @ApplicationTypeID int, @nowdate datetime, @minSubID int, @updateResult int, @invoiceID int,
		@invoiceNumber varchar(18), @minTID int, @adjAmount money, @adjtransactionID int,
		@invoiceProfileID int
	DECLARE @tblAdjust TABLE (transactionID int, amountToAdjust money)
	select @ApplicationTypeID = dbo.fn_getApplicationTypeIDFromName('Admin')
	select @nowdate = getdate()

	declare @tblSubs TABLE (subscriberID int, thePathExpanded varchar(max))
	insert into @tblSubs (subscriberID, thePathExpanded)
	select subscriberID, thePathExpanded
	from dbo.fn_getRecursiveMemberSubscriptions(@memberID,@siteID,@subscriberID)
	where status not in ('D','E')

	select @minSubID = min(subscriberID) from @tblSubs
	while @minSubID is not null BEGIN
		EXEC dbo.sub_updateSubscriberStatus @subscriberID=@minSubID, @newStatusCode='E', @siteID=@siteID, 
			@enteredByMemberID=@enteredByMemberID, @result=@updateResult OUTPUT
			IF @updateResult < 0 RAISERROR('sub_updateSubscriberStatus returned %i', 16, 1,@updateResult);

		IF @AROption = 'A' BEGIN
			INSERT INTO @tblAdjust (transactionID, amountToAdjust)
			select rt.mainTransactionID, ts.cache_amountAfterAdjustment
			from dbo.fn_sub_subscriberTransactions(@minSubID) as rt
			inner join dbo.tr_transactionSales as ts on ts.transactionID = rt.transactionID
				and ts.cache_amountAfterAdjustment > 0
				and rt.typeID = 1
		END
		IF @AROption = 'B' BEGIN
			INSERT INTO @tblAdjust (transactionID, amountToAdjust)
			select rt.mainTransactionID, ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount-ts.cache_pendingPaymentAllocatedAmount
			from dbo.fn_sub_subscriberTransactions(@minSubID) as rt
			inner join dbo.tr_transactionSales as ts on ts.transactionID = rt.transactionID
				and ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount-ts.cache_pendingPaymentAllocatedAmount > 0
				and rt.typeID = 1
		END

		UPDATE dbo.tr_applications
		SET [status] = 'D'
		WHERE itemID = @minSubID
		AND itemType = 'Dues'
		AND applicationTypeID = @ApplicationTypeID
		AND [status] <> 'D'

		select @minSubID = min(subscriberID) from @tblSubs where subscriberID > @minSubID
	END

	-- if there are adjustments to make
	IF EXISTS (select transactionID from @tblAdjust) BEGIN

		-- if any transactions are on a open or pending invoice, grab it for all adjustments. 
		-- otherwise, we need to create one to hold these adjustments
		select top 1 @invoiceID = i.invoiceID, @invoiceProfileID = i.invoiceProfileID
			from dbo.tr_invoices as i
			inner join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
			inner join @tblAdjust as tbl on tbl.transactionID = it.transactionID
			and statusID in (1,2)
			order by i.invoiceID
		IF @invoiceID is null
			EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@enteredByMemberID,
				@assignedToMemberID=@memberID, @dateBilled=@nowdate, @dateDue=@nowdate, 
				@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT
		
		-- record adjustments
		SELECT @minTID = min(transactionID) from @tblAdjust
		WHILE @minTID IS NOT NULL BEGIN
			SELECT @adjAmount = amountToAdjust*-1 from @tblAdjust where transactionID = @minTID
			
			EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteID,
				@recordedByMemberID=@enteredByMemberID, @statsSessionID=@statsSessionID,
				@status='Active', @amount=@adjAmount, @transactionDate=@nowdate,
				@saleTransactionID=@minTID, @invoiceID=@invoiceID, @transactionID=@adjtransactionID OUTPUT
			
			SELECT @minTID = min(transactionID) from @tblAdjust where transactionID > @minTID
		END

		EXEC dbo.tr_closeInvoice @enteredByMemberID=@enteredByMemberID, @invoiceIDList=@invoiceID			

	END

	IF @fReturnQuery = 1
		select * from @tblSubs


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROCEDURE [dbo].[sub_closePendingInvoices] 
AS

set nocount on

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- end of day today
	DECLARE @dateToUse DATETIME
	select @dateToUse = dateadd(ms,-3,dateadd(day,1,DATEADD(dd, DATEDIFF(dd,0,getdate()), 0)))

	declare @MCsystemMemberID int, @invoiceIDList varchar(max)
	select @MCsystemMemberID = memberID from dbo.ams_members where memberNumber = 'SYSTEM' and orgID = 1

	select @invoiceIDList = COALESCE(@invoiceIDList + ',', '') + cast(invoiceID as varchar(20)) 
	from dbo.tr_invoices 
	where dateDue <= @dateToUse 
	AND statusID = 2

	IF @invoiceIDList is not null
		exec dbo.tr_closeInvoice @enteredByMemberID=@MCsystemMemberID, @invoiceIDList=@invoiceIDList


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_allocateToInvoice]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@amount money,
@transactionDate datetime,
@paymentTransactionID int,
@invoiceID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- amt needs to be positive.
	IF @amount <= 0
		RAISERROR('amount is not more than 0', 16, 1);

	-- invoice must be closed to accept payment.
	IF NOT EXISTS (select invoiceID from dbo.tr_invoices where invoiceID = @invoiceID and statusID = 3)
		RAISERROR('invoice is not closed', 16, 1);

	-- dont assume memberid is the active one. get the active one.
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	-- dont assume entire amount can be allocated from payment. adjust the cap if needed.
	declare @unallocatedAmount money
	select @unallocatedAmount = tp.cache_refundableAmountOfPayment - tp.cache_allocatedAmountOfPayment
		from dbo.tr_transactionPayments as tp
		where tp.transactionID = @paymentTransactionID
	IF @amount > @unallocatedAmount
		set @amount = @unallocatedAmount

	-- ensure amount is 2 decimals
	select @amount = cast(@amount as decimal(10,2))

	-- use numeric(9,2) instead of money to prevent 4 digit money values
	declare @tblOpenTran TABLE (autoid int IDENTITY(1,1), itID int, transactionid int, maxAmtToAllocate numeric(10,2), PITSaleTID int, PITAllocAmt numeric(10,2), newAllocTID int)

	-- get open transactions on invoice
	insert into @tblOpenTran (itID, transactionID, maxAmtToAllocate)
	select it.itID, it.transactionID, it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount
	from dbo.tr_invoiceTransactions as it
	inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
	where it.invoiceID = @invoiceID
	and t.statusID <> 2
	and t.typeID <> 8
	order by it.itID

	-- group by PITTaxTrans. sales and adj to sales just put the transactionid
	update tbl
	set tbl.PITSaleTID = tbl.transactionID
	from @tblOpenTran as tbl
	inner join dbo.tr_transactions as t on t.transactionID = tbl.transactionID and t.typeID = 1

	update tbl
	set tbl.PITSaleTID = tbl.transactionID
	from @tblOpenTran as tbl
	inner join dbo.tr_transactions as t on t.transactionID = tbl.transactionID and t.typeID = 3
	inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
	inner join dbo.tr_transactions as tSale on tSale.transactionID = r.appliedToTransactionID and tSale.typeID = 1
	where tbl.PITSaleTID is null

	update tbl
	set tbl.PITSaleTID = tbl2.transactionID
	from @tblOpenTran as tbl
	inner join dbo.tr_relationships as r on r.transactionID = tbl.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'PITTaxTrans'
	inner join @tblOpenTran as tbl2 on tbl2.transactionID = r.appliedToTransactionID
	where tbl.PITSaleTID is null

	-- did we miss any PITTaxTrans records?
	IF EXISTS (select itID from @tblOpenTran where PITSaleTID is null)
		RAISERROR('PITSaleTID is missing', 16, 1);
	
	-- loop over each group to get allocation amount and % split
	declare @totalAmountLeftToAllocate money, @grpTID int, @grpMaxAmtToAllocate money, @a_amount money
	select @totalAmountLeftToAllocate = @amount
	select @grpTID = min(PITSaleTID) from @tblOpenTran
	while @grpTID is not null BEGIN

		-- how much can we allocate to this group of transactions?
		select @grpMaxAmtToAllocate = sum(MaxAmtToAllocate) from @tblOpenTran where PITSaleTID = @grpTID
		IF @totalAmountLeftToAllocate > @grpMaxAmtToAllocate
			select @a_amount = @grpMaxAmtToAllocate
		ELSE
			select @a_amount = @totalAmountLeftToAllocate

		-- calc amount to allocate for each transaction
		update @tblOpenTran
		set PITAllocAmt = case 
						when @a_amount = 0 then 0 
						else ((maxAmtToAllocate/ @grpMaxAmtToAllocate) * @a_amount) 
						end
		WHERE PITSaleTID = @grpTID

		-- handle remainders from percentage calculations
		-- put any diff in the 1st item that can accept it
		declare @sumAllocAmount numeric(10,2), @amountDiff numeric(10,2)
		select @sumAllocAmount = sum(PITAllocAmt) from @tblOpenTran WHERE PITSaleTID = @grpTID
		IF @sumAllocAmount <> abs(@a_amount) BEGIN
			select @amountDiff = abs(@a_amount) - @sumAllocAmount

			update top (1) @tblOpenTran
			set PITAllocAmt = PITAllocAmt + @amountDiff
			where PITAllocAmt + @amountDiff <= maxAmtToAllocate
		END

		select @totalAmountLeftToAllocate = @totalAmountLeftToAllocate - @a_amount
		if @totalAmountLeftToAllocate <= 0
			break

		select @grpTID = min(PITSaleTID) from @tblOpenTran where PITSaleTID > @grpTID
	END

	-- loop over and make allocations
	declare @minAutoID int, @a_TID int, @newAllocationTID int
	select @minAutoID = min(autoID) from @tblOpenTran where PITAllocAmt > 0
	while @minAutoID is not null BEGIN
		select @a_amount=PITAllocAmt, @a_TID=transactionID
			from @tblOpenTran
			where autoID = @minAutoID

		EXEC dbo.tr_createTransaction_allocation @recordedOnSiteID=@recordedOnSiteID, 
			@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
			@status='Active', @amount=@a_amount, @transactionDate=@transactionDate, 
			@paymentTransactionID=@paymentTransactionID, @saleTransactionID=@a_TID, 
			@transactionID=@newAllocationTID OUTPUT

		update @tblOpenTran
		set newAllocTID = @newAllocationTID
		where autoID = @minAutoID

		select @minAutoID = min(autoID) from @tblOpenTran where PITAllocAmt > 0 and autoID > @minAutoID
	END

	-- insert AllocTaxTrans relationships
	IF EXISTS (select autoid from @tblOpenTran where PITSaleTID <> transactionID) BEGIN
		INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
		select dbo.fn_tr_getRelationshipTypeID('AllocTaxTrans'), tbl.newAllocTID, tbl2.newAllocTID
		from @tblOpenTran as tbl
		inner join @tblOpenTran as tbl2 on tbl2.transactionID = tbl.PITSaleTID
		where tbl.PITSaleTID <> tbl.transactionID
		and tbl.newAllocTID is not null
		and tbl2.newAllocTID is not null
	END	


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_allocateToSale]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@amount money,
@transactionDate datetime,
@paymentTransactionID int,
@saleTransactionID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- amt needs to be positive.
	IF @amount <= 0
		RAISERROR('amount is not more than 0', 16, 1);

	-- dont assume memberid is the active one. get the active one.
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	-- ensure amount is 2 decimals
	select @amount = cast(@amount as decimal(10,2))

	-- use numeric(9,2) instead of money to prevent 4 digit money values
	-- get sale, child transactions, tax, and adjustments
	declare @tblOpenTran TABLE (autoid int IDENTITY(1,1), transactionid int, maxAmtToAllocate numeric(10,2), PITSaleTID int, PITAllocAmt numeric(10,2), newAllocTID int)

	;with innerTbl as (
		select t.transactionID, ts.cache_amountAfterAdjustment - ts.cache_activePaymentAllocatedAmount as salesDue
		from dbo.tr_transactions as t
		inner join dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID
		where t.typeID = 1
		and t.statusID not in (2,4)
		and t.transactionID = @saleTransactionID
			union all
		select t.transactionid, ts.cache_amountAfterAdjustment - ts.cache_activePaymentAllocatedAmount as salesDue
		from dbo.tr_transactions as t
		inner join dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID
		inner join innerTbl on innerTbl.transactionID = t.parentTransactionID
		where t.typeID = 1
		and t.statusID not in (2,4)
			union all
		select t.transactionID, ts.cache_amountAfterAdjustment - ts.cache_activePaymentAllocatedAmount as salesDue
		from dbo.tr_transactions as t
		inner join dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID
		inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'SalesTaxTrans'
		inner join innerTbl on innerTbl.transactionID = r.appliedToTransactionID
		where t.typeID = 7
		and t.statusID not in (2,4)
			union all
		select t.transactionID, it.cache_invoiceAmountAfterAdjustment - it.cache_activePaymentAllocatedAmount as salesDue
		from dbo.tr_transactions as t
		inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
		inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
		inner join innerTbl on innerTbl.transactionID = r.appliedToTransactionID
		where t.typeID = 3
		and t.statusID not in (2,4)
	)	
	insert into @tblOpenTran (transactionID, maxAmtToAllocate)
	select it.transactionID, sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) as maxAmtToAllocate
	from dbo.tr_invoiceTransactions as it
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	inner join innerTbl on innerTbl.transactionID = it.transactionid
	where innerTbl.salesDue > 0
	group by it.transactionID
	having sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) > 0
	order by it.transactionID

	-- group by PITTaxTrans. sales and adj to sales just put the transactionid
	update tbl
	set tbl.PITSaleTID = tbl.transactionID
	from @tblOpenTran as tbl
	inner join dbo.tr_transactions as t on t.transactionID = tbl.transactionID and t.typeID = 1

	update tbl
	set tbl.PITSaleTID = tbl.transactionID
	from @tblOpenTran as tbl
	inner join dbo.tr_transactions as t on t.transactionID = tbl.transactionID and t.typeID = 3
	inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
	inner join dbo.tr_transactions as tSale on tSale.transactionID = r.appliedToTransactionID and tSale.typeID = 1
	where tbl.PITSaleTID is null

	update tbl
	set tbl.PITSaleTID = tbl2.transactionID
	from @tblOpenTran as tbl
	inner join dbo.tr_relationships as r on r.transactionID = tbl.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'PITTaxTrans'
	inner join @tblOpenTran as tbl2 on tbl2.transactionID = r.appliedToTransactionID
	where tbl.PITSaleTID is null

	-- did we miss any PITTaxTrans records?
	IF EXISTS (select autoid from @tblOpenTran where PITSaleTID is null)
		RAISERROR('PITSaleTID is missing', 16, 1);

	-- loop over each group to get allocation amount and % split
	declare @totalAmountLeftToAllocate money, @grpTID int, @grpMaxAmtToAllocate money, @a_amount money
	select @totalAmountLeftToAllocate = @amount
	select @grpTID = min(PITSaleTID) from @tblOpenTran
	while @grpTID is not null BEGIN

		-- how much can we allocate to this group of transactions?
		select @grpMaxAmtToAllocate = sum(MaxAmtToAllocate) from @tblOpenTran where PITSaleTID = @grpTID
		IF @totalAmountLeftToAllocate > @grpMaxAmtToAllocate
			select @a_amount = @grpMaxAmtToAllocate
		ELSE
			select @a_amount = @totalAmountLeftToAllocate

		-- calc amount to allocate for each transaction
		update @tblOpenTran
		set PITAllocAmt = case 
						when @a_amount = 0 then 0 
						else ((maxAmtToAllocate/ @grpMaxAmtToAllocate) * @a_amount) 
						end
		WHERE PITSaleTID = @grpTID

		-- handle remainders from percentage calculations
		-- put any diff in the 1st item that can accept it
		declare @sumAllocAmount numeric(10,2), @amountDiff numeric(10,2)
		select @sumAllocAmount = sum(PITAllocAmt) from @tblOpenTran WHERE PITSaleTID = @grpTID
		IF @sumAllocAmount <> abs(@a_amount) BEGIN
			select @amountDiff = abs(@a_amount) - @sumAllocAmount

			update top (1) @tblOpenTran
			set PITAllocAmt = PITAllocAmt + @amountDiff
			where PITAllocAmt + @amountDiff <= maxAmtToAllocate
		END

		select @totalAmountLeftToAllocate = @totalAmountLeftToAllocate - @a_amount
		if @totalAmountLeftToAllocate <= 0
			break

		select @grpTID = min(PITSaleTID) from @tblOpenTran where PITSaleTID > @grpTID
	END

	-- loop over and make allocations
	declare @minAutoID int, @a_TID int, @newAllocationTID int
	select @minAutoID = min(autoID) from @tblOpenTran where PITAllocAmt > 0
	while @minAutoID is not null BEGIN
		select @a_amount=PITAllocAmt, @a_TID=transactionID
			from @tblOpenTran
			where autoID = @minAutoID

		EXEC dbo.tr_createTransaction_allocation @recordedOnSiteID=@recordedOnSiteID, 
			@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
			@status='Active', @amount=@a_amount, @transactionDate=@transactionDate, 
			@paymentTransactionID=@paymentTransactionID, @saleTransactionID=@a_TID, 
			@transactionID=@newAllocationTID OUTPUT

		update @tblOpenTran
		set newAllocTID = @newAllocationTID
		where autoID = @minAutoID

		select @minAutoID = min(autoID) from @tblOpenTran where PITAllocAmt > 0 and autoID > @minAutoID
	END	

	-- insert AllocTaxTrans relationships
	IF EXISTS (select autoid from @tblOpenTran where PITSaleTID <> transactionID) BEGIN
		INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
		select dbo.fn_tr_getRelationshipTypeID('AllocTaxTrans'), tbl.newAllocTID, tbl2.newAllocTID
		from @tblOpenTran as tbl
		inner join @tblOpenTran as tbl2 on tbl2.transactionID = tbl.PITSaleTID
		where tbl.PITSaleTID <> tbl.transactionID
		and tbl.newAllocTID is not null
		and tbl2.newAllocTID is not null
	END	


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_createTransaction_nsf]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@nsfAmount money,
@transactionDate datetime,
@batchID int,
@paymentTransactionID int,
@transactionID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @tblAllocations TABLE (autoid int IDENTITY(1,1) PRIMARY KEY, transactionID int, PITTaxTID int, allocAmount money, NewAllocationTID int, newRelTID int)
	declare @assignedToMemberID int, @ownedByOrgID int, @creditGLAccountID int, @detail varchar(max)
	declare @debitGLAccountID int
	declare @refundableAmt money, @allocatedAmt money
	declare @a_autoid int, @a_appliedToTID int, @a_allocAmount money, @newAllocationTID int

	-- init output param
	select @transactionID = 0

	-- no zero or negative dollar amounts
	if @nsfAmount <= 0
		RAISERROR('nsfAmount is not gt 0', 16, 1);

	-- ensure @paymentTransactionID is an active payment
	IF NOT EXISTS (select transactionID from dbo.tr_transactions where transactionID = @paymentTransactionID and typeID = 2 and statusID = 1)
		RAISERROR('paymentTransactionID is not an active payment', 16, 1);

	-- get assignedToMemberID from payment transaction
	select @assignedToMemberID = assignedToMemberID, @ownedByOrgID = ownedByOrgID, 
		@creditGLAccountID = debitGLAccountID, @detail = detail
		from tr_transactions 
		where transactionID = @paymentTransactionID

	-- dont assume memberid is the active one. get the active one.
	select @assignedToMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @assignedToMemberID 
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	-- get DEP account. this is the debit account.
	select @debitGLAccountID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and isSystemAccount = 1
		and GLCode = 'DEPOSITS'
		and [status] = 'A'

	-- if batchID is null or not an open batch, reject nsf.
	IF @batchID is null OR NOT EXISTS (
		select batchID
		from dbo.tr_batches
		where orgID = @ownedByOrgID
		and batchID = @batchID
		and statusID = 1)
	RAISERROR('batchID is null or not an open batch', 16, 1);

	-- the maximum amount of the nsf is the refundable amount of the payment
	select @refundableAmt = tp.cache_refundableAmountOfPayment, @allocatedAmt = tp.cache_allocatedAmountOfPayment
		from dbo.tr_transactions as t
		inner join dbo.tr_transactionPayments as tp on tp.transactionID = t.transactionID
			and t.transactionID = @paymentTransactionID
	IF (@nsfAmount > @refundableAmt)
		RAISERROR('nsfAmount greater than refundableAmt', 16, 1);

	-- verify credit account accepts new transactions
	-- ensure we have active debit/credit accts
	IF @debitGLAccountID is null or NOT EXISTS (
		select glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and glaccountID = @creditGLAccountID
		and [status] = 'A')
		RAISERROR('debitGLAccountID is null or creditGLAccountID is not active', 16, 1);

	-- if anything is allocated to this payment, we need to deallocate it first
	IF @allocatedAmt > 0 BEGIN
		insert into @tblAllocations (transactionID, PITTaxTID, allocAmount)
		select transactionID, PITTaxTID, allocAmount
		from dbo.fn_tr_getAllocatedTransactionsofPayment(@paymentTransactionID)

		select @a_autoid = min(autoid) from @tblAllocations
		while @a_autoid is not null BEGIN
			select @a_allocAmount=allocAmount*-1, @a_appliedToTID=transactionID
			from @tblAllocations 
			where autoID = @a_autoid

			EXEC dbo.tr_createTransaction_allocation @recordedOnSiteID=@recordedOnSiteID, 
				@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
				@status='Active', @amount=@a_allocAmount, @transactionDate=@transactionDate, 
				@paymentTransactionID=@paymentTransactionID, @saleTransactionID=@a_appliedToTID, 
				@transactionID=@newAllocationTID OUTPUT

			UPDATE @tblAllocations
			set NewAllocationTID = @newAllocationTID
			where autoID = @a_autoid

			select @a_autoid = min(autoid) from @tblAllocations where autoid > @a_autoid
		end

		update tbl
		set tbl.newRelTID = tbl2.newAllocationTID
		from @tblAllocations as tbl
		inner join @tblAllocations as tbl2 on tbl2.transactionID = tbl.PITTaxTID
		where tbl.PITTaxTID is not null

		INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
		select dbo.fn_tr_getRelationshipTypeID('AllocTaxTrans'), newAllocationTID, newRelTID
		from @tblAllocations
		where newRelTID is not null
	END

	-- insert into transactions 
	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
		amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
		typeID, accrualDate, debitGLAccountID, creditGLAccountID)
	VALUES (@ownedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID('Active'), @detail, null, 
		@nsfAmount, getdate(), @transactionDate, @assignedToMemberID, @recordedByMemberID, @statsSessionID, 
		dbo.fn_tr_getTypeID('NSF'), @transactionDate, @debitGLAccountID, @creditGLAccountID)
		select @transactionID = SCOPE_IDENTITY()

	-- insert into batchTransactions 
	INSERT INTO dbo.tr_batchTransactions (batchID, transactionID)
	VALUES (@batchID, @transactionID)

	-- insert into relationships
	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
	VALUES (dbo.fn_tr_getRelationshipTypeID('NSFTrans'), @transactionID, @paymentTransactionID)

	-- update payment cache for @paymentTransactionID
	update dbo.tr_transactionPayments
	set cache_refundableAmountOfPayment = cache_refundableAmountOfPayment - @nsfAmount
	where transactionID = @paymentTransactionID


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @transactionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_createTransaction_adjustment]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@status varchar(20),
@amount money,
@transactionDate datetime,
@saleTransactionID int,
@invoiceID int,
@transactionID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- init output param
	select @transactionID = 0

	declare @origSaleOwnedByOrgID int, @origSaleAssignedToMemberID int, @origCreditGLAccountID int, 
			@origSaleCreditGLAccountID int, @ARGLAccountID int, @AdjSaleTransactionID int, 
			@TaxForAdjustmentMinRow int, @TaxForAdjustmentGLAID int, @taxTransactionID int,
			@AdjTaxTransactionID int, @AllocPaymentTID int, @AllocSaleTID int, 
			@allocTID int, @SaleAllocTransactionID int, @invoiceProfileID int, @contentVersionID int
	declare @TaxForAdjustmentAmt money, @amtLeftToDeallocate money, @AllocAmt money, @DeallocateAmtNeg money
	declare @invoiceNumber varchar(18)
	declare @origSaleDetail varchar(max), @TaxForAdjustmentDetail varchar(max)
	declare @tblTaxForAdjustment TABLE (row int, taxAuthorityID int, taxGLAccountID int, authorityName varchar(200), taxRuleID int, taxRate float, taxAmount money) 

	-- no zero dollar adjustments
	if @amount = 0
		RAISERROR('amount is 0', 16, 1);

	-- ensure @saleTransactionID is an active sale
	IF EXISTS (select transactionID from dbo.tr_transactions where transactionID = @saleTransactionID and typeID <> 1 and statusID not in (1,3))
		RAISERROR('saleTransactionID is not an active sale', 16, 1);

	-- get data from sale transaction
	select @origSaleOwnedByOrgID=ownedByOrgID, @origSaleAssignedToMemberID=assignedToMemberID, 
		@origSaleDetail=detail, @origCreditGLAccountID=creditGLAccountID
		from dbo.tr_transactions
		where transactionID = @saleTransactionID
		and typeID = 1
		and statusID in (1,3)

	-- dont assume memberid is the active one. get the active one.
	select @origSaleAssignedToMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @origSaleAssignedToMemberID 
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	select @ARGLAccountID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @origSaleOwnedByOrgID
		and isSystemAccount = 1
		and GLCode = 'ACCOUNTSRECEIVABLE'
		and [status] = 'A'
	IF @ARGLAccountID is null
		RAISERROR('ARGLAccountID is null', 16, 1);

	-- if invoiceID is null, not an open/pending invoice, or inv profile doesnt match revenue GL, assume need to create a new one.
	IF @invoiceID is null 
		OR NOT EXISTS (select invoiceID from dbo.tr_invoices where invoiceID = @invoiceID and statusID in (1,2))
		OR ((select dbo.fn_tr_doesInvoiceProfileSupportRevenueGL(@invoiceID,@origCreditGLAccountID)) = 0)
	BEGIN
		select @invoiceProfileID=invoiceProfileID from dbo.tr_GLAccounts where GLAccountID = @origCreditGLAccountID
			IF @invoiceProfileID is null RAISERROR('invoiceProfileID is null', 16, 1);

		EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID,
			@enteredByMemberID=@recordedByMemberID, 
			@assignedToMemberID=@origSaleAssignedToMemberID,
			@dateBilled=@transactionDate, @dateDue=@transactionDate, 
			@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT
	END
	
	-- adjustments up	
	if @amount > 0 BEGIN

		-- credit account of adj is credit account of sale
		select @origSaleCreditGLAccountID = gl.glAccountID
			from dbo.tr_transactions as t
			inner join dbo.tr_GLAccounts as gl on gl.glAccountID = t.creditGLAccountID
				and t.transactionID = @saleTransactionID
				and gl.status = 'A'
		if @origSaleCreditGLAccountID is null
			RAISERROR('origSaleCreditGLAccountID is null', 16, 1);

		-- insert adj into transactions
		-- ensure amount is abs
		INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
			amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
			typeID, accrualDate, debitGLAccountID, creditGLAccountID)
		VALUES (@origSaleOwnedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID(@status), @origSaleDetail, null, 
			@amount, getdate(), @transactionDate, @origSaleAssignedToMemberID, @recordedByMemberID, @statsSessionID, 
			dbo.fn_tr_getTypeID('Adjustment'), @transactionDate, @ARGLAccountID, @origSaleCreditGLAccountID)
			select @AdjSaleTransactionID = SCOPE_IDENTITY()

		-- insert adj into relationships
		INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
		VALUES (dbo.fn_tr_getRelationshipTypeID('AdjustTrans'), @AdjSaleTransactionID, @saleTransactionID)

		-- put adj on invoice
		SELECT @contentVersionID = null
		SELECT @contentVersionID = max(cv.contentVersionID)
			FROM dbo.tr_glAccounts as gl
			INNER JOIN dbo.cms_content as c on c.contentID = gl.invoiceContentID
			INNER JOIN dbo.cms_siteResources sr	on sr.siteResourceID = c.siteResourceID 
			INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
			INNER JOIN dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID AND cl.languageID = 1
			INNER JOIN dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID
			WHERE gl.GLAccountID = @origSaleCreditGLAccountID
			AND cv.isActive = 1
			AND len(cv.rawContent) > 0
		INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, messageContentVersionID)
		VALUES (@AdjSaleTransactionID, @invoiceID, @amount, 0, 0, @contentVersionID)

		-- update cache
		UPDATE dbo.tr_transactionSales
		SET cache_amountAfterAdjustment = cache_amountAfterAdjustment + @amount
		WHERE transactionID = @saleTransactionID

		-- determine sales tax based on adjustment date and @amount
		IF EXISTS (select glAccountID from dbo.tr_glAccounts where glAccountID = @origSaleCreditGLAccountID and isTaxable = 1) BEGIN

			-- determine the tax on the adjusted amount
			insert into @tblTaxForAdjustment (row, taxAuthorityID, taxGLAccountID, authorityName, taxRuleID, taxRate, taxAmount)
			select row, taxAuthorityID, taxGLAccountID, authorityName, taxRuleID, taxRate, taxAmount
			from dbo.fn_tr_getTaxForAdjustment(@AdjSaleTransactionID)
			where taxAmount > 0

			-- loop over taxes
			select @TaxForAdjustmentMinRow = min(row) from @tblTaxForAdjustment
			while @TaxForAdjustmentMinRow is not null BEGIN
				select @TaxForAdjustmentAmt=taxAmount, @TaxForAdjustmentGLAID=taxGLAccountID, 
					@TaxForAdjustmentDetail = 'Sales Tax: ' + authorityName + ' @ ' + cast(taxRate*100 as varchar(10)) + '%'
				from @tblTaxForAdjustment 
				where row=@TaxForAdjustmentMinRow

				-- is there already a tax transaction using this acct code for this sale? if so, adjust it
				SELECT @taxTransactionID = null
				SELECT @taxTransactionID = tTax.transactionID
					FROM dbo.tr_transactions as tTax
					INNER JOIN dbo.tr_relationships AS r ON r.transactionID = tTax.transactionID
						AND tTax.typeID = 7
						AND tTax.statusID in (1,3)
						AND r.appliedToTransactionID = @saleTransactionID
						AND tTax.creditGLAccountID = @TaxForAdjustmentGLAID
					INNER JOIN dbo.tr_relationshipTypes AS rt ON rt.typeID = r.typeID AND rt.type = 'SalesTaxTrans'
				IF @taxTransactionID is not null BEGIN

					-- insert adj into transactions
					INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
						amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
						typeID, accrualDate, debitGLAccountID, creditGLAccountID)
					VALUES (@origSaleOwnedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID(@status), @TaxForAdjustmentDetail, null, 
						@TaxForAdjustmentAmt, getdate(), @transactionDate, @origSaleAssignedToMemberID, @recordedByMemberID, @statsSessionID, 
						dbo.fn_tr_getTypeID('Adjustment'), @transactionDate, @ARGLAccountID, @TaxForAdjustmentGLAID)
						select @AdjTaxTransactionID = SCOPE_IDENTITY()

					-- insert adj into relationships
					-- tie tax to the sale adjustment
					INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
					VALUES (dbo.fn_tr_getRelationshipTypeID('AdjustTrans'), @AdjTaxTransactionID, @taxTransactionID)
					
					INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
					VALUES (dbo.fn_tr_getRelationshipTypeID('PITTaxTrans'), @AdjTaxTransactionID, @AdjSaleTransactionID)

					-- put adj on invoice
					INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount)
					VALUES (@AdjTaxTransactionID, @invoiceID, @TaxForAdjustmentAmt, 0, 0)

					-- update cache
					UPDATE dbo.tr_transactionSales
					SET cache_amountAfterAdjustment = cache_amountAfterAdjustment + @TaxForAdjustmentAmt
					WHERE transactionID = @taxTransactionID

				END 

				-- else, create a new sales tax transaction.
				ELSE BEGIN

					EXEC dbo.tr_createTransaction_salesTax @ownedByOrgID=@origSaleOwnedByOrgID, 
						@recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
						@statsSessionID=@statsSessionID, @status=@status, @detail=@TaxForAdjustmentDetail, 
						@amount=@TaxForAdjustmentAmt, @transactionDate=@transactionDate, 
						@creditGLAccountID=@TaxForAdjustmentGLAID, @saleTransactionID=@saleTransactionID, 
						@invoiceID=@invoiceID, @transactionID=@taxTransactionID OUTPUT

					-- tie tax to the sale adjustment
					INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
					VALUES (dbo.fn_tr_getRelationshipTypeID('PITTaxTrans'), @taxTransactionID, @AdjSaleTransactionID)

				END

				select @TaxForAdjustmentMinRow = min(row) from @tblTaxForAdjustment where row > @TaxForAdjustmentMinRow
			END

		END

	END 

	-- adjustments down
	ELSE BEGIN

		declare @amtLeftToAdjust money, @SaleAndAdjustmentsAmt money, @amountToAdjust money,
				@AdjToMakeAmt money, @AdjToMakeUnallocatedAmt money, @AllocationsAmt money,
				@amountToAllocate money, @a_allocAmount money, @a_subtract_allocAmount money,
				@it_amtLeftToAdj money, @it_MaxAmountToTake money, @it_amtToAdj money,
				@amtDueNoPendingOnInvoice money
		declare @SaleAndAdjustmentsMinAutoID int, @SaleAndAdjustmentsTID int, @SaleAndAdjustmentsTypeID int,
				@AdjToMakeAutoID int, @AdjToMakeTID int, @AdjToMakeGLAID int, @newAllocationTID int,
				@a_autoid int, @a_appliedToTID int, @a_paymentTID int, @a_subtract_autoid int, @minITID int,
				@it_invoiceID int
		declare @AdjToMakeIsSale bit
		declare @it_invstatus varchar(50)
		declare @AdjToMakeDetail varchar(max)
		declare @tblSaleAndAdjustments TABLE (autoid int IDENTITY(1,1) PRIMARY KEY, transactionID int, typeID int, amount money)
		declare @tblAdjToMake TABLE (autoid int IDENTITY(1,1), isSale bit, saleTransactionID int, debitGLAID int, detail varchar(max), amountToAdjust numeric(9,2))
		declare @tblAdjToMakeFinal TABLE (autoid int IDENTITY(1,1), isSale bit, saleTransactionID int, debitGLAID int, detail varchar(max), amountToAdjust numeric(9,2))
		declare @tblAllocations TABLE (autoid int IDENTITY(1,1) PRIMARY KEY, appliedToTID int, paymentTID int, allocAmount money, origAllocationTID int, origRelTID int)
		declare @tblAllocationsToMake TABLE (autoid int IDENTITY(1,1) PRIMARY KEY, origAllocationTID int, origRelTID int, newAllocationTID int, newRelTID int)

		-- get all active adjustments to sale in reverse order (daterecorded).
		-- consider the non-written off amounts only and remove any that are 0
		insert into @tblSaleAndAdjustments (transactionID, typeID, amount)
		select tAdj.transactionID, tAdj.typeID, 
			case 
			when gl.isSystemAccount = 1 and gl.GLCode = 'ACCOUNTSRECEIVABLE' then tAdj.amount
			else tAdj.amount * -1
			end - wo.writeOffAmount as maxAmountForNegativeAdjustment 
		from dbo.tr_transactions as tAdj
		inner join dbo.tr_relationships as tr on tr.transactionID = tAdj.transactionID
			and tr.appliedToTransactionID = @saleTransactionID
		inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'AdjustTrans'
		inner join dbo.tr_glAccounts as gl on gl.glAccountID = tAdj.debitGLAccountID
		cross apply dbo.fn_tr_getWriteOffAmountofSaleOrAdj(tAdj.transactionID) as wo
		where tAdj.statusID = 1
		and tAdj.typeID = 3
		and case 
			when gl.isSystemAccount = 1 and gl.GLCode = 'ACCOUNTSRECEIVABLE' then tAdj.amount
			else tAdj.amount * -1
			end - wo.writeOffAmount <> 0
		order by tAdj.daterecorded desc, tAdj.transactionID desc
		
		-- add in the original sale as last entry
		insert into @tblSaleAndAdjustments (transactionID, typeID, amount)
		select tSale.transactionID, tSale.typeID, tSale.amount - wo.writeOffAmount as maxAmountForNegativeAdjustment 
		from dbo.tr_transactions as tSale
		cross apply dbo.fn_tr_getWriteOffAmountofSaleOrAdj(tSale.transactionID) as wo
		where tSale.transactionID = @saleTransactionID
		and tSale.amount - wo.writeOffAmount <> 0

		-- loop over @tblSaleAndAdjustments until we get adjustment amount. grab PIT tax as well.		
		select @amtLeftToAdjust = abs(@amount)
		select @SaleAndAdjustmentsMinAutoID = min(autoid) from @tblSaleAndAdjustments
		while @SaleAndAdjustmentsMinAutoID is not null BEGIN
			select @SaleAndAdjustmentsTID=transactionID, @SaleAndAdjustmentsTypeID=typeID, 
				@SaleAndAdjustmentsAmt=amount
			from @tblSaleAndAdjustments 
			where autoID = @SaleAndAdjustmentsMinAutoID

			-- if amt left can be adjusted in full from this adjustment, take full amt. else take what we can.
			if @SaleAndAdjustmentsAmt < @amtLeftToAdjust
				select @amountToAdjust = @SaleAndAdjustmentsAmt
			ELSE
				select @amountToAdjust = @amtLeftToAdjust

			-- grab orig credit gl. adjustments will use this same gl.
			select @origSaleCreditGLAccountID = gl.glAccountID
				from dbo.tr_transactions as t
				inner join dbo.tr_GLAccounts as gl on gl.glAccountID = t.creditGLAccountID
					and t.transactionID = @saleTransactionID
					and gl.status = 'A'
			if @origSaleCreditGLAccountID is null
				RAISERROR('origSaleCreditGLAccountID is null', 16, 1);

			-- add to adj to make. adj debit acct is orig credit acct
			insert into @tblAdjToMake (saleTransactionID, isSale, debitGLAID, detail, amountToAdjust)
			values (@saleTransactionID, 1, @origSaleCreditGLAccountID, @origSaleDetail, @amountToAdjust*-1)
				
			-- and all its PIT taxes (adj to sales tax and sales tax). adj debit acct is orig credit acct
			-- 1. adjust to sales tax tied to adjust to sale (could be pos or neg so find out based on AR)
			-- 2+3. sales tax tied to sale or adjust to sale (only happens on positive adjustments so just take adj amount)
			insert into @tblAdjToMake (saleTransactionID, isSale, debitGLAID, detail, amountToAdjust)
			SELECT tTax.transactionID, 0, tTax.creditGLAccountID, tTax.detail, 
				case 
				when gl.isSystemAccount = 1 and gl.GLCode = 'ACCOUNTSRECEIVABLE' then ((((tAdj.amount-wo.writeOffAmount)/@SaleAndAdjustmentsAmt)*@amountToAdjust)*-1)
				else (((tAdj.amount-wo.writeOffAmount)/@SaleAndAdjustmentsAmt)*@amountToAdjust)
				end as amountToAdjust
			FROM dbo.tr_transactions AS tAdj 
			inner join dbo.tr_glAccounts as gl on gl.glAccountID = tAdj.debitGLAccountID
			INNER JOIN dbo.tr_relationships AS tr ON tAdj.transactionID = tr.transactionID 
			INNER JOIN dbo.tr_relationshipTypes AS trt ON tr.typeID = trt.typeID 
			INNER JOIN dbo.tr_relationships AS tr2 ON tAdj.transactionID = tr2.transactionID 
			INNER JOIN dbo.tr_relationshipTypes AS trt2 ON tr2.typeID = trt2.typeID 
			INNER JOIN dbo.tr_transactions AS tTax ON tr2.appliedToTransactionID = tTax.transactionID
			cross apply dbo.fn_tr_getWriteOffAmountofSaleOrAdj(tAdj.transactionID) as wo
			WHERE tr.appliedToTransactionID = @SaleAndAdjustmentsTID
			AND trt.type = 'PITTaxTrans'
			AND @SaleAndAdjustmentsTypeID = 3
			AND tAdj.typeID = 3
			AND tAdj.statusID IN (1,3)
			AND trt2.type = 'AdjustTrans'
			AND tTax.typeID = 7
			AND tTax.statusID IN (1,3)
				union all
			SELECT tTax.transactionID, 0, tTax.creditGLAccountID, tTax.detail, ((((tTax.amount-wo.writeOffAmount)/@SaleAndAdjustmentsAmt)*@amountToAdjust)*-1) as amountToAdjust
			FROM dbo.tr_transactions AS tTax 
			INNER JOIN dbo.tr_relationships AS tr ON tTax.transactionID = tr.transactionID 
			INNER JOIN dbo.tr_relationshipTypes AS trt ON tr.typeID = trt.typeID
			cross apply dbo.fn_tr_getWriteOffAmountofSaleOrAdj(tTax.transactionID) as wo
			WHERE tr.appliedToTransactionID = @SaleAndAdjustmentsTID
			AND tTax.typeID = 7 
			AND tTax.statusID IN (1,3)
			AND trt.type = 'PITTaxTrans'
			AND @SaleAndAdjustmentsTypeID in (1,3)

			select @amtLeftToAdjust = @amtLeftToAdjust - @amountToAdjust
			IF @amtLeftToAdjust <= 0
				BREAK

			select @SaleAndAdjustmentsMinAutoID = min(autoid) from @tblSaleAndAdjustments where autoid > @SaleAndAdjustmentsMinAutoID
		END

		-- sum and group transactions by saleTID. these are the adj transactions to record
		insert into @tblAdjToMakeFinal (saleTransactionID, isSale, debitGLAID, detail, amountToAdjust)
		select saleTransactionID, isSale, debitGLAID, detail, sum(amountToAdjust)
		from @tblAdjToMake
		group by saleTransactionID, isSale, debitGLAID, detail
		having sum(amountToAdjust) <> 0
		order by min(autoid)

		-- loop over the final adjustments to make. 
		-- handle necessary deallocations, record adj, and adjust cache amounts
		select @AdjToMakeAutoID = min(autoid) from @tblAdjToMakeFinal
		while @AdjToMakeAutoID is not null BEGIN
			select	@AdjToMakeTID=saleTransactionID, @AdjToMakeAmt=amountToAdjust, 
					@AdjToMakeIsSale=issale, @AdjToMakeGLAID=debitGLAID, @AdjToMakeDetail=detail
			from @tblAdjToMakeFinal 
			where autoid = @AdjToMakeAutoID

			-- get "how much has not been paid/allocated" of sale before adjustment
			select @AdjToMakeUnallocatedAmt = cache_amountAfterAdjustment - cache_activePaymentAllocatedAmount - cache_pendingPaymentAllocatedAmount
				from dbo.tr_transactionSales
				where transactionID = @AdjToMakeTID
			
			-- only need to deallocate if adj amount is gt than @AdjToMakeUnallocatedAmt
			if abs(@AdjToMakeAmt) > @AdjToMakeUnallocatedAmt BEGIN

				-- cleanup temp vars from any previous loops
				delete from @tblAllocations
				
				-- get all active allocations to sale/tax and all adjustments in reverse order.
				-- get the original allocation TID AllocTaxTrans relationship TID
				insert into @tblAllocations (appliedToTID, paymentTID, allocAmount, origAllocationTID, origRelTID)
				select tSaleAdj.transactionID, tPay.transactionID,
					case when glAllocDeb.GLCode = 'ACCOUNTSRECEIVABLE' and glAllocDeb.AccountTypeID = 2 then tAlloc.amount*-1 else tAlloc.amount end,
					tAlloc.transactionID, tATT.transactionID
				from 
					(
					select transactionID from dbo.tr_transactions where transactionID = @AdjToMakeTID
					union
					select adjInner.transactionID
					from dbo.tr_transactions as adjInner
					inner join dbo.tr_relationships as rInner on rInner.transactionID = adjInner.transactionID and rInner.appliedToTransactionID = @AdjToMakeTID
					inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
					where adjInner.statusID in (1,3)
					) as tSaleAdj
				inner join dbo.tr_relationships as rS on rS.appliedToTransactionID = tSaleAdj.transactionID
				inner join dbo.tr_relationshipTypes as rtS on rtS.typeID = rS.typeID and rtS.type = 'AllocSaleTrans'
				inner join dbo.tr_transactions as tAlloc on tAlloc.transactionID = rS.transactionID and tAlloc.statusID in (1,3)
				inner join dbo.tr_GLAccounts as glAllocDeb on glAllocDeb.GLAccountID = tAlloc.debitGLAccountID
				inner join dbo.tr_relationships as rP on rP.transactionID = tAlloc.transactionID
				inner join dbo.tr_relationshipTypes as rtP on rtP.typeID = rP.typeID and rtP.type = 'AllocPayTrans'
				inner join dbo.tr_transactions as tPay on tPay.transactionID = rP.appliedToTransactionID and tPay.statusID in (1,3)
				left outer join dbo.tr_relationships as rATT
					inner join dbo.tr_relationshipTypes as rtATT on rtATT.typeID = rATT.typeID and rtATT.type = 'AllocTaxTrans'
					inner join dbo.tr_transactions as tATT on tATT.transactionID = rATT.appliedToTransactionID and tATT.statusID in (1,3)
					on rATT.transactionID = tAlloc.transactionID
				ORDER BY tAlloc.transactionDate desc, tAlloc.transactionID desc

				-- for each negative allocation in tbl, subtract from closest positive allocation. delete negative allocation.
				IF EXISTS (select autoid from @tblAllocations where allocAmount < 0) BEGIN
				
					select @a_autoid = null
					select @a_autoid = min(autoid) from @tblAllocations where allocAmount < 0
					while @a_autoid is not null BEGIN
						select @a_allocAmount=allocAmount, @a_appliedToTID=appliedToTID, @a_paymentTID=paymentTID
						from @tblAllocations 
						where autoID = @a_autoid

						while @a_allocAmount < 0 BEGIN
							select @a_subtract_autoid = null, @a_subtract_allocAmount = null

							select top 1 @a_subtract_autoid=autoid, @a_subtract_allocAmount=allocAmount
							from @tblAllocations
							where allocAmount > 0
							and appliedToTID = @a_appliedToTID
							and paymentTID = @a_paymentTID
							order by autoid desc

							if @a_subtract_allocAmount < abs(@a_allocAmount) begin
								-- subtract amount from pos allocation (which should bring it to 0)
								update @tblAllocations
								set allocAmount = allocAmount - @a_subtract_allocAmount
								where autoID = @a_subtract_autoid

								-- add to a_allocAmount since it is negative to bring it closer to 0
								select @a_allocAmount = @a_allocAmount + @a_subtract_allocAmount
							end ELSE begin
								-- subtract remaining amount from pos allocation
								update @tblAllocations
								set allocAmount = allocAmount - abs(@a_allocAmount)
								where autoID = @a_subtract_autoid

								-- remove neg allocation since it is now all accounted for
								delete from @tblAllocations
								where autoID = @a_autoid

								-- to get out of loop
								select @a_allocAmount = 0
							end
						end

						select @a_autoid = min(autoid) from @tblAllocations where allocAmount < 0 and autoID > @a_autoid
					end

				END

				-- loop over @tblAllocations until we get deallocation amount. deallocate.
				select @amtLeftToDeallocate = abs(@AdjToMakeAmt)-@AdjToMakeUnallocatedAmt
				select @a_autoid = null
				select @a_autoid = min(autoid) from @tblAllocations
				while @a_autoid is not null BEGIN
					select @a_allocAmount=allocAmount, @a_appliedToTID=appliedToTID, @a_paymentTID=paymentTID
					from @tblAllocations 
					where autoID = @a_autoid

					-- if amt left can be deallocated in full from this allocation, take full amt. else take what we can.
					if @a_allocAmount < @amtLeftToDeallocate
						select @amountToAllocate = @a_allocAmount
					ELSE
						select @amountToAllocate = @amtLeftToDeallocate

					-- if amt > 0 then deallocate that amount.
					select @newAllocationTID = null
					IF @amountToAllocate > 0 BEGIN
						select @DeallocateAmtNeg = @amountToAllocate * -1

						EXEC dbo.tr_createTransaction_allocation @recordedOnSiteID=@recordedOnSiteID, 
							@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
							@status='Active', @amount=@DeallocateAmtNeg, @transactionDate=@transactionDate, 
							@paymentTransactionID=@a_paymentTID, @saleTransactionID=@a_appliedToTID, 
							@transactionID=@newAllocationTID OUTPUT
					END

					insert into @tblAllocationsToMake (origAllocationTID, origRelTID, newAllocationTID)
					select origAllocationTID, origRelTID, @newAllocationTID
					from @tblAllocations
					where autoID = @a_autoid

					select @amtLeftToDeallocate = @amtLeftToDeallocate - @amountToAllocate
					IF @amtLeftToDeallocate <= 0
						BREAK

					select @a_autoid = min(autoid) from @tblAllocations where autoid > @a_autoid
				end

			end			
	
			-- insert adj into transactions
			-- ensure amount is abs
			INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
				amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
				typeID, accrualDate, debitGLAccountID, creditGLAccountID)
			VALUES (@origSaleOwnedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID(@status), @AdjToMakeDetail, null, 
				abs(@AdjToMakeAmt), getdate(), @transactionDate, @origSaleAssignedToMemberID, @recordedByMemberID, @statsSessionID, 
				dbo.fn_tr_getTypeID('Adjustment'), @transactionDate, @AdjToMakeGLAID, @ARGLAccountID)
			IF @AdjToMakeIsSale = 1
				select @AdjSaleTransactionID = SCOPE_IDENTITY()
			ELSE
				select @AdjTaxTransactionID = SCOPE_IDENTITY()

			-- insert adj into relationships
			-- tie tax to the sale adjustment
			IF @AdjToMakeIsSale = 0	BEGIN
				INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
				VALUES (dbo.fn_tr_getRelationshipTypeID('AdjustTrans'), @AdjTaxTransactionID, @AdjToMakeTID)

				INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
				VALUES (dbo.fn_tr_getRelationshipTypeID('PITTaxTrans'), @AdjTaxTransactionID, @AdjSaleTransactionID)
			END
			ELSE BEGIN
				INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
				VALUES (dbo.fn_tr_getRelationshipTypeID('AdjustTrans'), @AdjSaleTransactionID, @AdjToMakeTID)
			END

			-- put adj on invoice (0 dollars.. no neg amounts here)
			IF @AdjToMakeIsSale = 1 BEGIN			
				SELECT @contentVersionID = null
				SELECT @contentVersionID = max(cv.contentVersionID)
					FROM dbo.tr_glAccounts as gl
					INNER JOIN dbo.cms_content as c on c.contentID = gl.invoiceContentID
					INNER JOIN dbo.cms_siteResources sr	on sr.siteResourceID = c.siteResourceID 
					INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
					INNER JOIN dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID AND cl.languageID = 1
					INNER JOIN dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID
					WHERE gl.GLAccountID = @AdjToMakeGLAID
					AND cv.isActive = 1
					AND len(cv.rawContent) > 0
				INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, messageContentVersionID)
				VALUES (@AdjSaleTransactionID, @invoiceID, 0, 0, 0, @contentVersionID)
			END ELSE BEGIN
				INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount)
				VALUES (@AdjTaxTransactionID, @invoiceID, 0, 0, 0)
			END

			-- update invoiceTransactions to reduce amount of initial sale/adj
			-- get adjToMakeTID and all its adjustments in order of oldest ITID
			select @it_amtLeftToAdj = abs(@AdjToMakeAmt)
			select @minITID = null
			select @minITID = min(it.itID)
				from dbo.tr_invoiceTransactions as it
				inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
				where (t.transactionID = @AdjToMakeTID
				OR t.transactionID in (
					select r.transactionID
					from dbo.tr_relationships as r
					inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
					inner join dbo.tr_transactions as tAdj on tAdj.transactionID = r.transactionID
					where r.appliedToTransactionID = @AdjToMakeTID
					and tAdj.statusID <> 2
				))
				and t.statusID <> 2
				and it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount > 0
			while @minITID is not null begin

				select	@it_invoiceID=it.invoiceID, 
						@it_invstatus=ins.status,
						@it_MaxAmountToTake=it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount
					from dbo.tr_invoiceTransactions as it
					inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
					inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
					where it.itID = @minITID

				if @it_MaxAmountToTake < @it_amtLeftToAdj
					select @it_amtToAdj = @it_MaxAmountToTake
				ELSE
					select @it_amtToAdj = @it_amtLeftToAdj

				update dbo.tr_invoiceTransactions
				set cache_invoiceAmountAfterAdjustment = cache_invoiceAmountAfterAdjustment - @it_amtToAdj
				where itID = @minITID

				IF @AdjToMakeIsSale = 1 BEGIN
					INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID, amount)
					SELECT top 1 dbo.fn_tr_getRelationshipTypeID('AdjustInvTrans'), @AdjSaleTransactionID, transactionID, @it_amtToAdj
					FROM dbo.tr_invoiceTransactions
					WHERE itid = @minITID
				END ELSE BEGIN
					INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID, amount)
					SELECT top 1 dbo.fn_tr_getRelationshipTypeID('AdjustInvTrans'), @AdjTaxTransactionID, transactionID, @it_amtToAdj
					FROM dbo.tr_invoiceTransactions
					WHERE itid = @minITID
				END

				-- cleanup invoice
				-- if invoice is closed and is now fully paid with active payments, mark it as paid
				-- if invoice is paid and is now not fully paid with active payments, mark it as closed
				select @amtDueNoPendingOnInvoice = sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount)
					from dbo.tr_invoiceTransactions as it
					inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
					where it.invoiceID = @it_invoiceID
					and t.statusID <> 2
				IF @it_invstatus = 'closed' and @amtDueNoPendingOnInvoice = 0 BEGIN
					update dbo.tr_invoices
					set statusID = 4, payProfileID = null
					where invoiceID = @it_invoiceID

					insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
					values (@it_invoiceID, getdate(), 4, 3, @recordedByMemberID)
				END
				IF @it_invstatus = 'paid' and @amtDueNoPendingOnInvoice > 0 BEGIN
					update dbo.tr_invoices
					set statusID = 3
					where invoiceID = @it_invoiceID

					insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
					values (@it_invoiceID, getdate(), 3, 4, @recordedByMemberID)
				END

				select @it_amtLeftToAdj = @it_amtLeftToAdj - @it_amtToAdj
				IF @it_amtLeftToAdj <= 0
					BREAK

				select @minITID = min(it.itID)
					from dbo.tr_invoiceTransactions as it
					inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
					where (t.transactionID = @AdjToMakeTID
					OR t.transactionID in (
						select r.transactionID
						from dbo.tr_relationships as r
						inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
						inner join dbo.tr_transactions as tAdj on tAdj.transactionID = r.transactionID
						where r.appliedToTransactionID = @AdjToMakeTID
						and tAdj.statusID <> 2
					))
					and t.statusID <> 2
					and it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount > 0
					and it.itid > @minITID
			end

			-- update cache
			UPDATE dbo.tr_transactionSales
			SET cache_amountAfterAdjustment = cache_amountAfterAdjustment - abs(@AdjToMakeAmt)
			WHERE transactionID = @AdjToMakeTID

			select @AdjToMakeAutoID = min(autoid) from @tblAdjToMakeFinal where autoid > @AdjToMakeAutoID
		END

		-- Record new AllocTaxTrans relationship if deallocations happened
		IF (select count(*) from @tblAllocationsToMake) > 0 BEGIN
			update tbl
			set tbl.newRelTID = tbl2.newAllocationTID
			from @tblAllocationsToMake as tbl
			inner join @tblAllocationsToMake as tbl2 on tbl2.origAllocationTID = tbl.origRelTID
			where tbl.newAllocationTID is not null
			and tbl.origRelTID is not null

			INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
			select dbo.fn_tr_getRelationshipTypeID('AllocTaxTrans'), newAllocationTID, newRelTID
			from @tblAllocationsToMake
			where newAllocationTID is not null 
			and newRelTID is not null
		END

	END


	IF @TranCounter = 0
		COMMIT TRAN;
	SELECT @transactionID = @AdjSaleTransactionID
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @transactionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_updateTaxRule]
@taxRuleID int,
@taxAuthorityID int,
@saleGLAccountID int,
@stateID int,
@taxRate float,
@startDate datetime,
@endDate datetime

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- ensure enddate is set to end of day
	declare @endDateFinal datetime
	select @endDateFinal = dateadd(s,-1,dateadd(day,1,DATEADD(dd, DATEDIFF(dd,0,@endDate), 0)))

	-- update rule
	UPDATE dbo.tr_taxRules
	SET taxAuthorityID = @taxAuthorityID, 
		saleGLAccountID = @saleGLAccountID, 
		stateID = @stateID,
		taxRate = @taxRate, 
		startDate = @startDate, 
		endDate = @endDateFinal
	WHERE taxRuleID = @taxRuleID

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_updateTaxAuthority]
@orgID int,
@taxAuthorityID int,
@authorityName varchar(200),
@accountCode varchar(200)

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- update authority
	UPDATE dbo.tr_taxAuthorities
	SET authorityName = @authorityName
	WHERE taxAuthorityID = @taxAuthorityID
	AND orgID = @orgID

	-- update GL Account for tax authority
	DECLARE @GLAccountID int
	SELECT @GLAccountID = GLAccountID from dbo.tr_taxAuthorities where taxAuthorityID = @taxAuthorityID
	SELECT @accountCode = nullif(@accountCode,'')
	EXEC dbo.tr_updateGLAccount @orgID=@orgID, @GLAccountID=@GLAccountID, @accountName=@authorityName,
		@accountCode=@accountCode, @parentGLAccountID=null, @invoiceProfileID=null, @isTaxable=0, 
		@invoiceContentID = null


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_updateGLAccount]
@orgID int,
@GLAccountID int,
@accountName varchar(200),
@accountCode varchar(200),
@parentGLAccountID int,
@invoiceProfileID int,
@isTaxable bit,
@invoiceContentID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- null account code if no length
	IF @accountCode is not null and len(@accountCode) = 0
		SELECT @accountCode = null

	declare @GLAccountTypeID int
	select @GLAccountTypeID = accountTypeID from dbo.tr_GLAccounts where GLAccountID = @GLAccountID

	-- null invoiceProfileID if not revenue
	IF @GLAccountTypeID <> 3
		SELECT @invoiceProfileID = null

	-- cant have multiple accounts with same accountName at same level
	-- cant have multiple accounts with same accountCode
	IF (@parentGLAccountID is null AND EXISTS (select GLAccountID FROM dbo.tr_GLAccounts where orgID = @orgID and accountName = @accountName and parentGLAccountID is null and accountTypeID = @GLAccountTypeID and GLAccountID <> @GLAccountID))
		OR (@parentGLAccountID is not null and EXISTS (select GLAccountID FROM dbo.tr_GLAccounts where orgID = @orgID and accountName = @accountName and parentGLAccountID = @parentGLAccountID and accountTypeID = @GLAccountTypeID and GLAccountID <> @GLAccountID)) 
		OR (@accountCode is not null and EXISTS (select GLAccountID from dbo.tr_GLAccounts where orgID = @orgID and accountCode = @accountCode and GLAccountID <> @GLAccountID and status <> 'D'))
		OR (@GLAccountTypeID = 3 and @invoiceProfileID is null)
		RAISERROR('this account would violate distinct rules', 16, 1);
	ELSE BEGIN
		UPDATE dbo.tr_GLAccounts
		SET accountName = @accountName, 
			accountCode = @accountCode, 
			parentGLAccountID = nullIf(@parentGLAccountID,0),
			isTaxable = @isTaxable,
			invoiceProfileID = @invoiceProfileID,
			invoiceContentID = @invoiceContentID
		WHERE GLAccountID = @GLAccountID
		and orgID = @orgID
	END
 

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_moveInvoiceTransaction]
@orgID int,
@transactionID int,
@fromInvoiceID int,
@toInvoiceID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- if both invoices are same, do nothing
	IF @fromInvoiceID <> @toInvoiceID BEGIN
		
		-- invoices must belong to org
		-- both invoices must be open
		-- both invoices must be the same invoice profile id
		declare @currentIPID int
		select @currentIPID = invoiceProfileID from dbo.tr_invoices where invoiceID = @fromInvoiceID
		IF EXISTS (
			select i.invoiceID 
			from dbo.tr_invoices as i
			inner join dbo.ams_members as m on m.memberID = i.assignedToMemberID
			where i.invoiceID in (@fromInvoiceID,@toInvoiceID) 
			and (
				m.orgID <> @orgID
				OR 
				i.statusID <> 1
				OR
				i.invoiceProfileID <> @currentIPID
			)
			) RAISERROR('invoices are not compatible', 16, 1);

		-- transaction must be a sale, adjustment, or void offset
		IF EXISTS (
			select transactionID
			from dbo.tr_transactions
			where transactionID = @transactionID
			and typeID not in (1,3,8)
			) RAISERROR('transaction must be a sale, adjustment, or void offset', 16, 1);

		-- move transactionID from the one invoice to the other
		update dbo.tr_invoiceTransactions
		set invoiceID = @toInvoiceID
		where invoiceID = @fromInvoiceID
		and transactionID = @transactionID

	END


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_moveBatchTransaction]
@orgID int,
@transactionID int,
@fromBatchID int,
@toBatchID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- if both batches are same, do nothing
	IF @fromBatchID <> @toBatchID BEGIN

		-- batches must belong to org
		-- both batches must be open
		-- cannot be system created batch
		IF EXISTS (
			select batchID 
			from dbo.tr_batches 
			where batchID in (@fromBatchID,@toBatchID) 
			and (
				orgID <> @orgID
				OR 
				statusID <> 1
				OR 
				isSystemCreated = 1
			)
			) RAISERROR('batches are not compatible', 16, 1);

		-- transaction must be a payment,refund
		IF EXISTS (
			select transactionID
			from dbo.tr_transactions
			where transactionID = @transactionID
			and typeID not in (2,4)
			) RAISERROR('transaction must be a payment,refund', 16, 1);

		-- move transactionID and all allocations/WO/Voids from the one batch to the other
		update bt
		set bt.batchID = @toBatchID
		from dbo.tr_batchTransactions as bt
		inner join (
			select transactionID
			from dbo.tr_getBatchTransactions(@orgID,@fromBatchID)
			where payTID = @transactionID
		) as tmp on tmp.transactionID = bt.transactionID
	
	END

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_deleteTaxRule]
@orgID int,
@TaxRuleID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- make sure rule not already deleted
	IF NOT EXISTS (select taxRuleID from dbo.tr_taxRules where taxRuleID = @TaxRuleID and status = 'A')
		RAISERROR('tax rule is not active', 16, 1);

	-- delete tax rule
	UPDATE dbo.tr_taxRules
	set [status] = 'D'
	where taxRuleID = @TaxRuleID


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_deleteTaxAuthority]
@orgID int,
@TaxAuthorityID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- make sure auth/org matches and not already deleted
	IF NOT EXISTS (select taxAuthorityID from dbo.tr_taxAuthorities where taxAuthorityID = @TaxAuthorityID and orgID = @orgID and status = 'A')
		RAISERROR('tax authority is not active', 16, 1);

	-- delete any active tax rules in mass
	UPDATE dbo.tr_taxRules
	set [status] = 'D'
	where taxAuthorityID = @TaxAuthorityID
	and [status] = 'A'

	-- delete authority
	UPDATE dbo.tr_taxAuthorities
	set [status] = 'D'
	where taxAuthorityID = @TaxAuthorityID
	and [status] = 'A'

	-- delete corresponding gl account
	DECLARE @GLAccountID int
	SELECT @GLAccountID = GLAccountID from dbo.tr_taxAuthorities where taxAuthorityID = @TaxAuthorityID
	EXEC dbo.tr_deleteGLAccount @orgID=@orgID, @GLAccountID=@GLAccountID


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_deleteInvoiceProfile]
@orgID int,
@profileID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	IF EXISTS (select profileID from dbo.tr_invoiceProfiles where profileID = @profileID AND orgID = @orgID and status <> 'D') 
		AND NOT EXISTS (select GLAccountID from dbo.tr_GLAccounts where invoiceProfileID = @profileID)
		AND NOT EXISTS (select invoiceID from dbo.tr_invoices where invoiceProfileID = @profileID)
		DELETE FROM dbo.tr_invoiceProfiles
		WHERE profileID = @profileID

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_deleteGLAccount]
@orgID int,
@GLAccountID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- inactive it if account is in use, otherwise, delete it.
	-- this will check all foreign keys to the GLAccountID dynamically.
	declare @innerUnion VARCHAR(max)
	declare @acctInUse bit
	declare @dynSQL nvarchar(max)

	SELECT @innerUnion = COALESCE(@innerUnion + ' union ' + char(13) + char(10), '') + 
		'select ' + COL_NAME(fc.parent_object_id, fc.parent_column_id) + ' as GLAccountID from dbo.' + OBJECT_NAME(f.parent_object_id) +
		case 
		when OBJECT_NAME(f.parent_object_id) = 'tr_taxAuthorities' and COL_NAME(fc.parent_object_id, fc.parent_column_id) = 'GLAccountID' then ' where [status] <> ''D''' 
		when OBJECT_NAME(f.parent_object_id) = 'tr_taxRules' and COL_NAME(fc.parent_object_id, fc.parent_column_id) = 'saleGLAccountID' then ' where [status] <> ''D''' 
		when OBJECT_NAME(f.parent_object_id) = 'tr_GLAccounts' and COL_NAME(fc.parent_object_id, fc.parent_column_id) = 'parentGLAccountID' then ' where [status] <> ''D''' 
		else '' end
		FROM sys.foreign_keys AS f
		INNER JOIN sys.foreign_key_columns AS fc ON f.OBJECT_ID = fc.constraint_object_id
		and OBJECT_NAME (f.referenced_object_id) = 'tr_GLAccounts'
		and COL_NAME(fc.referenced_object_id,fc.referenced_column_id) = 'GLAccountID'
	set @acctInUse = 1
	set @dynSQL = '
		if exists (
			select GLAccountID from (' + @innerUnion + ') as tmp 
			where GLAccountID is not null
			and GLAccountID = ' + cast(@GLAccountID as varchar(10)) + '
		) set @acctInUse = 1
		else 
			set @acctInUse = 0
		'
	exec sp_executesql @dynSQL, N'@acctInUse bit output', @acctInUse output

	-- cant delete systemaccounts so ignore if systemaccount
	-- also null invoiceprofileid if we are Deleting the account
	IF @acctInUse = 0
		update dbo.tr_GLAccounts
		set [status] = 'D', invoiceProfileID = null
		where GLAccountID = @GLAccountID
		and orgID = @orgID
		and [status] <> 'D'
		and isSystemAccount = 0
	ELSE
		update dbo.tr_GLAccounts
		set [status] = 'I'
		where GLAccountID = @GLAccountID
		and orgID = @orgID
		and [status] = 'A'
		and isSystemAccount = 0

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_createTransaction_writeoff_payment]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@status varchar(20),
@amount money,
@transactionDate datetime,
@paymentTransactionID int,
@transactionID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- init output param
	select @transactionID = 0

	-- no zero or negative dollar writeoffs
	if @amount <= 0
		RAISERROR('amount is not gt 0', 16, 1);

	-- ensure amount is 2 decimals
	select @amount = cast(@amount as decimal(10,2))

	-- ensure @paymentTransactionID is a non-voided, active payment
	IF NOT EXISTS (
		select transactionID 
		from dbo.tr_transactions 
		where transactionID = @paymentTransactionID 
		and typeID = 2
		and statusID = 1
		)
		RAISERROR('paymentTransactionID is not a non-voided, active payment', 16, 1);

	-- get info from payment transaction
	declare @assignedToMemberID int, @ownedByOrgID int, @payProfileID int, @payProfileCode varchar(20),
		@payCashGLID int, @payCashGLName varchar(200), @detail varchar(max)
	select @assignedToMemberID = t.assignedToMemberID, 
		   @ownedByOrgID = t.ownedByOrgID,
		   @payProfileID = mp.profileID,
		   @payProfileCode = mp.profileCode,
		   @payCashGLID = glDeb.GLAccountID,
		   @payCashGLName = glDeb.AccountName,
		   @detail = t.detail
	from dbo.tr_transactions as t
	inner join dbo.tr_transactionPayments as tp on tp.transactionID = t.transactionID
	inner join dbo.mp_profiles as mp on mp.profileID = tp.profileID
	inner join dbo.tr_GLAccounts as glDeb on glDeb.GLAccountID = t.debitGLAccountID
	where t.transactionID = @paymentTransactionID

	-- dont assume memberid is the active one. get the active one.
	select @assignedToMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @assignedToMemberID 
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	-- DEP is debit acct, MISC INC is credit acct
	declare @debitGLAccountID int, @creditGLAccountID int
	select @debitGLAccountID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and isSystemAccount = 1
		and GLCode = 'DEPOSITS'
		and [status] = 'A'
	select @creditGLAccountID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and isSystemAccount = 1
		and GLCode = 'MISCINCOME'
		and [status] = 'A'

	-- ensure we have active debit/credit accts
	IF @debitGLAccountID is null or @creditGLAccountID is null
		RAISERROR('debitGLAccountID is null or creditGLAccountID is null', 16, 1);

	-- ensure payment has @amount of unallocated funds
	IF (select cache_refundableAmountOfPayment - cache_allocatedAmountOfPayment from tr_transactionPayments where transactionID = @paymentTransactionID) < @amount
		RAISERROR('payment does not have amount of unallocated funds', 16, 1);

	-- insert into transactions
	-- ensure amount is abs
	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
		amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
		typeID, accrualDate, debitGLAccountID, creditGLAccountID)
	VALUES (@ownedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID(@status), @detail, null, 
		abs(@amount), getdate(), @transactionDate, @assignedToMemberID, @recordedByMemberID, @statsSessionID, 
		dbo.fn_tr_getTypeID('Write Off'), @transactionDate, @debitGLAccountID, @creditGLAccountID)
		select @transactionID = SCOPE_IDENTITY()

	-- insert into relationships
	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
	VALUES (dbo.fn_tr_getRelationshipTypeID('WriteOffPayTrans'), @transactionID, @paymentTransactionID)

	-- add write off to batch
	-- put on same batch as payment if that batch is open.
	-- otherwise, put on daily exception batch
	declare @woBatchID int, @batchCode varchar(40), @batchName varchar(400)
	select @woBatchID = b.batchID
		from dbo.tr_batchTransactions as bt
		inner join dbo.tr_batches as b on b.batchID = bt.batchID
		where bt.transactionID = @paymentTransactionID
		and b.statusID = 1
	IF @woBatchID is null BEGIN
		select @batchCode = CONVERT(CHAR(8),@transactionDate,112) + '_' + cast(@payProfileID as varchar(10)) + '_' + cast(@payCashGLID as varchar(10)) + '_EX'
		select @batchName = CONVERT(CHAR(8),@transactionDate,112) + ' ' + @payProfileCode + ' ' + @payCashGLName + ' Exceptions'
		select top 1 @woBatchID = b.batchID
			from dbo.tr_batches as b
			where b.orgID = @ownedByOrgID
			and b.batchCode = @batchCode
			and b.statusID = 1
			and b.isSystemCreated = 1
		IF @woBatchID is null
			EXEC dbo.tr_createBatch @orgID=@ownedByOrgID, @payProfileID=@payProfileID, @batchCode=@batchCode, 
				@batchName=@batchName, @controlAmt=0, @controlCount=0, @depositDate=@transactionDate, 
				@isSystemCreated=1, @createdByMemberID=null, @batchID=@woBatchID OUTPUT 
	END
	INSERT INTO dbo.tr_batchTransactions (batchID, transactionID)
	VALUES (@woBatchID, @transactionID)

	-- can only write off what is unallocated/refundable, so just adjust cache amount of payment
	UPDATE dbo.tr_transactionPayments
	SET cache_refundableAmountOfPayment = cache_refundableAmountOfPayment - abs(@amount)
	WHERE transactionID = @paymentTransactionID

	-- check the in-bound rules.
	-- payments - new cache_allocatedAmountOfPayment must be between 0 and cache_refundableAmountOfPayment
	IF EXISTS (
		select paymentID 
		from dbo.tr_transactionPayments
		where transactionID = @paymentTransactionID
		and cache_allocatedAmountOfPayment not between 0 and cache_refundableAmountOfPayment
	)
		RAISERROR('in-bound checking failed', 16, 1);


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @transactionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_createTransaction_salesTax]
@ownedByOrgID int,
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@status varchar(20),
@detail varchar(max),
@amount money,
@transactionDate datetime,
@creditGLAccountID int,
@saleTransactionID int,
@invoiceID int,
@transactionID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- init output param
	select @transactionID = 0

	-- ensure @saleTransactionID is a sale
	IF EXISTS (select transactionID from dbo.tr_transactions where transactionID = @saleTransactionID and typeID <> 1)
		RAISERROR('saleTransactionID is not a sale', 16, 1);

	-- get assignedToMemberID, stateIDforTax from sale transaction
	declare @assignedToMemberID int, @stateIDForTax int
	select @assignedToMemberID = t.assignedToMemberID, @stateIDForTax = ts.stateIDForTax
		from dbo.tr_transactions as t
		inner join dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID
		where t.transactionID = @saleTransactionID

	-- dont assume memberid is the active one. get the active one.
	select @assignedToMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @assignedToMemberID 
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	-- get AR account. this is the debit account.
	declare @debitGLAccountID int
	select @debitGLAccountID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and isSystemAccount = 1
		and GLCode = 'ACCOUNTSRECEIVABLE'
		and [status] = 'A'

	-- ensure amount is abs
	select @amount = abs(@amount)

	-- verify credit account accepts new transactions
	-- ensure we have active debit/credit accts
	IF @debitGLAccountID is null or NOT EXISTS (
		select glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and glaccountID = @creditGLAccountID
		and [status] = 'A')
		RAISERROR('credit account does not accept new transactions', 16, 1);

	-- if invoiceID is null or not an open invoice, reject tax.
	IF @invoiceID is null OR NOT EXISTS (
		select invoiceID
		from dbo.tr_invoices
		where invoiceID = @invoiceID
		and statusID = 1)
		RAISERROR('invoiceID not an open invoice', 16, 1);

	-- insert into transactions
	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
		amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
		typeID, accrualDate, debitGLAccountID, creditGLAccountID)
	VALUES (@ownedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID(@status), @detail, null, 
		@amount, getdate(), @transactionDate, @assignedToMemberID, @recordedByMemberID, @statsSessionID, 
		dbo.fn_tr_getTypeID('Sales Tax'), @transactionDate, @debitGLAccountID, @creditGLAccountID)
		select @transactionID = SCOPE_IDENTITY()

	-- insert into transactionSales
	INSERT INTO dbo.tr_transactionSales (transactionID, cache_amountAfterAdjustment, 
		cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, stateIDForTax)
	VALUES (@transactionID, @amount, 0, 0, @stateIDForTax)

	-- insert into relationships
	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
	VALUES (dbo.fn_tr_getRelationshipTypeID('SalesTaxTrans'), @transactionID, @saleTransactionID)

	-- put on invoice
	INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount)
	VALUES (@transactionID, @invoiceID, @amount, 0, 0)

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @transactionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_createTransaction_sale]
@ownedByOrgID int,
@recordedOnSiteID int,
@assignedToMemberID int,
@recordedByMemberID int,
@statsSessionID int,
@status varchar(20),
@detail varchar(max),
@parentTransactionID int,
@amount money,
@transactionDate datetime,
@accrualDate datetime,
@creditGLAccountID int,
@invoiceID int,
@stateIDForTax int = null, -- null to use stateid from member record
@bypassTax bit = 0,
@transactionID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- init output param
	select @transactionID = 0

	-- dont assume memberid is the active one. get the active one.
	select @assignedToMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @assignedToMemberID 
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	-- get AR account. this is the debit account.
	declare @debitGLAccountID int
	select @debitGLAccountID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and isSystemAccount = 1
		and GLCode = 'ACCOUNTSRECEIVABLE'
		and [status] = 'A'

	-- ensure amount is abs
	select @amount = abs(@amount)

	declare @statusID int
	select @statusID = dbo.fn_tr_getStatusID(@status)

	-- verify credit account accepts new transactions
	-- ensure we have active debit/credit accts
	IF @debitGLAccountID is null or NOT EXISTS (
		select glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and glaccountID = @creditGLAccountID
		and [status] = 'A')
		RAISERROR('credit account does not accept new transactions', 16, 1);

	-- if invoiceID is null, not an open invoice, or inv profile doesnt match revenue GL, reject sale.
	IF @invoiceID is null 
		OR NOT EXISTS (select invoiceID from dbo.tr_invoices where invoiceID = @invoiceID and statusID = 1)
		OR ((select dbo.fn_tr_doesInvoiceProfileSupportRevenueGL(@invoiceID,@creditGLAccountID)) = 0)
		RAISERROR('invoiceid not eligible', 16, 1);

	-- insert into transactions
	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
		amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
		typeID, accrualDate, debitGLAccountID, creditGLAccountID)
	VALUES (@ownedByOrgID, @recordedOnSiteID, @statusID, @detail, @parentTransactionID, 
		@amount, getdate(), @transactionDate, @assignedToMemberID, @recordedByMemberID, @statsSessionID, 
		dbo.fn_tr_getTypeID('Sale'), @accrualDate, @debitGLAccountID, @creditGLAccountID)
		select @transactionID = SCOPE_IDENTITY()

	-- if stateIDforTax is null, get state on member record to record in the transactionSales table
	if nullif(@stateIDForTax,0) is null BEGIN
		select @stateIDForTax = ma.stateID
		from dbo.ams_memberAddresses as ma 
		inner join dbo.ams_members as m on m.memberID = ma.memberID
		where ma.memberid = @assignedToMemberID
		and ma.addressTypeID = m.billingAddressTypeID
	end

	-- insert into transactionSales
	INSERT INTO dbo.tr_transactionSales (transactionID, cache_amountAfterAdjustment, 
		cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, stateIDForTax)
	VALUES (@transactionID, @amount, 0, 0, nullif(@stateIDForTax,0))

	-- put sale on invoice
	declare @contentVersionID int
	SELECT @contentVersionID = max(cv.contentVersionID)
		FROM dbo.tr_glAccounts as gl
		INNER JOIN dbo.cms_content as c on c.contentID = gl.invoiceContentID
		INNER JOIN dbo.cms_siteResources sr	on sr.siteResourceID = c.siteResourceID 
		INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
		INNER JOIN dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID AND cl.languageID = 1
		INNER JOIN dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID
		WHERE gl.GLAccountID = @creditGLAccountID
		AND cv.isActive = 1
		AND len(cv.rawContent) > 0
	INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, messageContentVersionID)
	VALUES (@transactionID, @invoiceID, @amount, 0, 0, @contentVersionID)
	
	-- determine sales tax if necessary
	IF @amount > 0 AND @bypassTax = 0 and EXISTS (select glAccountID from dbo.tr_glAccounts where glAccountID = @creditGLAccountID and isTaxable = 1) BEGIN
		
		DECLARE @tblTax TABLE (row int, taxAuthorityID int, taxGLAccountID int, authorityName varchar(200), taxRuleID int, taxRate float, taxAmount money) 
		insert into @tblTax	(row, taxAuthorityID, taxGLAccountID, authorityName, taxRuleID, taxRate, taxAmount)
		select row, taxAuthorityID, taxGLAccountID, authorityName, taxRuleID, taxRate, taxAmount
		from dbo.fn_tr_getTaxForSale(@transactionID,@stateIDForTax)
		where taxAmount > 0

		DECLARE @minrow int, @rc int, @taxTransactionID int, @taxGLAID int, @taxAmt money, @taxdetail varchar(max)
		select @minrow = min(row) from @tblTax
		while @minrow is not null BEGIN
			select @taxAmt=taxAmount, @taxGLAID=taxGLAccountID, @taxdetail = 'Sales Tax: ' + authorityName + ' @ ' + cast(taxRate*100 as varchar(10)) + '%'
				from @tblTax 
				where row=@minrow

			EXEC dbo.tr_createTransaction_salesTax @ownedByOrgID=@ownedByOrgID, 
				@recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
				@statsSessionID=@statsSessionID, @status=@status, @detail=@taxdetail, 
				@amount=@taxAmt, @transactionDate=@transactionDate, @creditGLAccountID=@taxGLAID, 
				@saleTransactionID=@transactionID, @invoiceID=@invoiceID, 
				@transactionID=@taxTransactionID OUTPUT

			INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
			VALUES (dbo.fn_tr_getRelationshipTypeID('PITTaxTrans'), @taxTransactionID, @transactionID)

			select @minrow = min(row) from @tblTax where row > @minrow
		END

	END

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @transactionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_createTransaction_refund]
@ownedByOrgID int,
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@status varchar(20),
@detail varchar(max),
@amount money,
@transactionDate datetime,
@creditGLAccountID int,
@profileID int,
@historyID int,
@batchID int,
@paymentTransactionID int,
@transactionID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- init output param
	select @transactionID = 0

	-- no zero or negative dollar refunds
	if @amount <= 0
		RAISERROR('amount is not gt 0', 16, 1);

	-- ensure @paymentTransactionID is a payment
	IF EXISTS (select transactionID from dbo.tr_transactions where transactionID = @paymentTransactionID and typeID <> 2)
		RAISERROR('paymentTransactionID is not a payment', 16, 1);

	-- get assignedToMemberID from payment transaction
	declare @assignedToMemberID int
	select @assignedToMemberID = assignedToMemberID
		from tr_transactions 
		where transactionID = @paymentTransactionID

	-- dont assume memberid is the active one. get the active one.
	select @assignedToMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @assignedToMemberID 
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	-- get DEP account. this is the debit account.
	declare @debitGLAccountID int
	select @debitGLAccountID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and isSystemAccount = 1
		and GLCode = 'DEPOSITS'
		and [status] = 'A'

	-- if batchID is null, not an open batch, or is linked to a payment profile diff than this refund, reject refund.
	IF @batchID is null 
		OR NOT EXISTS (select batchID from dbo.tr_batches where orgID = @ownedByOrgID and batchID = @batchID and statusID = 1)
		OR NOT EXISTS (select batchID from dbo.tr_batches where orgID = @ownedByOrgID and batchID = @batchID and (payProfileID is null or payProfileID = @profileID))
		RAISERROR('batchid is not eligible', 16, 1);

	-- get unallocated amount of payment
	declare @unallocatedAmt money
	select @unallocatedAmt = tp.cache_refundableAmountOfPayment - tp.cache_allocatedAmountOfPayment
		from dbo.tr_transactions as t
		inner join dbo.tr_transactionPayments as tp on tp.transactionID = t.transactionID
			and t.transactionID = @paymentTransactionID

	-- ensure payment has @amount of refundable funds (and unallocated funds)
	IF (select cache_refundableAmountOfPayment from tr_transactionPayments where transactionID = @paymentTransactionID) < @amount
		OR @unallocatedAmt < @amount
		RAISERROR('payment does not have amount of refundable funds', 16, 1);

	-- verify credit account accepts new transactions
	-- ensure we have active debit/credit accts
	IF @debitGLAccountID is null or NOT EXISTS (
		select glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and glaccountID = @creditGLAccountID
		and [status] = 'A')
		RAISERROR('credit account does not accept new transactions', 16, 1);

	-- insert into transactions
	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
		amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
		typeID, accrualDate, debitGLAccountID, creditGLAccountID)
	VALUES (@ownedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID(@status), @detail, null, 
		@amount, getdate(), @transactionDate, @assignedToMemberID, @recordedByMemberID, @statsSessionID, 
		dbo.fn_tr_getTypeID('Refund'), @transactionDate, @debitGLAccountID, @creditGLAccountID)
		select @transactionID = SCOPE_IDENTITY()

	-- insert into transactionPayments
	INSERT INTO dbo.tr_transactionPayments (transactionID, profileID, historyID,  
		cache_allocatedAmountOfPayment, cache_refundableAmountOfPayment)
	VALUES (@transactionID, @profileID, @historyID, 0, 0)

	-- insert into batchTransactions
	INSERT INTO dbo.tr_batchTransactions (batchID, transactionID)
	VALUES (@batchID, @transactionID)

	-- insert into relationships
	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
	VALUES (dbo.fn_tr_getRelationshipTypeID('RefundTrans'), @transactionID, @paymentTransactionID)

	-- update payment cache for @paymentTransactionID
	update dbo.tr_transactionPayments
	set cache_refundableAmountOfPayment = cache_refundableAmountOfPayment - @amount
	where transactionID = @paymentTransactionID


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @transactionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_createTransaction_payment]
@ownedByOrgID int,
@recordedOnSiteID int,
@assignedToMemberID int,
@recordedByMemberID int,
@statsSessionID int,
@status varchar(20),
@detail varchar(max),
@amount money,
@transactionDate datetime,
@debitGLAccountID int,
@profileID int,
@historyID int,
@batchid int,
@transactionID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- init output param
	select @transactionID = 0

	-- no zero or negative dollar payments
	if @amount <= 0
		RAISERROR('amount is not gt 0', 16, 1);

	-- dont assume memberid is the active one. get the active one.
	select @assignedToMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @assignedToMemberID 
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	-- get DEP account. this is the credit account.
	declare @creditGLAccountID int
	select @creditGLAccountID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and isSystemAccount = 1
		and GLCode = 'DEPOSITS'
		and [status] = 'A'

	-- verify debit account accepts new transactions
	-- ensure we have active debit/credit accts
	IF @creditGLAccountID is null or NOT EXISTS (
		select glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and glaccountID = @debitGLAccountID
		and [status] = 'A')
		RAISERROR('debit account does not accept new transactions', 16, 1);

	-- if batchID is null, not an open batch, or is linked to a payment profile diff than this payment, reject payment.
	IF @batchID is null 
		OR NOT EXISTS (select batchID from dbo.tr_batches where orgID = @ownedByOrgID and batchID = @batchID and statusID = 1)
		OR NOT EXISTS (select batchID from dbo.tr_batches where orgID = @ownedByOrgID and batchID = @batchID and (payProfileID is null or payProfileID = @profileID))
		RAISERROR('batchid is not an eligible batch', 16, 1);

	-- insert into transactions
	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
		amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
		typeID, accrualDate, debitGLAccountID, creditGLAccountID)
	VALUES (@ownedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID(@status), @detail, null, 
		@amount, getdate(), @transactionDate, @assignedToMemberID, @recordedByMemberID, @statsSessionID, 
		dbo.fn_tr_getTypeID('Payment'), @transactionDate, @debitGLAccountID, @creditGLAccountID)
		select @transactionID = SCOPE_IDENTITY()

	-- insert into transactionPayments
	INSERT INTO dbo.tr_transactionPayments (transactionID, profileID, historyID, cache_allocatedAmountOfPayment, cache_refundableAmountOfPayment)
	VALUES (@transactionID, @profileID, @historyID, 0, @amount)

	-- insert into batchTransactions
	INSERT INTO dbo.tr_batchTransactions (batchID, transactionID)
	VALUES (@batchID, @transactionID)

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @transactionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_createTaxRule]
@taxAuthorityID int,
@saleGLAccountID int,
@stateID int,
@taxRate float,
@startDate datetime,
@endDate datetime,
@taxRuleID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- init output var
	SELECT @taxRuleID = null

	-- ensure enddate is set to end of day
	declare @endDateFinal datetime
	select @endDateFinal = dateadd(s,-1,dateadd(day,1,DATEADD(dd, DATEDIFF(dd,0,@endDate), 0)))

	-- add rule
	INSERT INTO dbo.tr_taxRules (taxAuthorityID, saleGLAccountID, stateID, taxRate, startDate, endDate, [status])
	VALUES (@taxAuthorityID, @saleGLAccountID, @stateID, @taxRate, @startDate, @endDateFinal, 'A')
		SELECT @taxRuleID = SCOPE_IDENTITY()


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @taxRuleID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_createTaxAuthority]
@orgID int,
@authorityName varchar(200),
@accountCode varchar(200),
@authorityID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- init output var
	SELECT @authorityID = null
	SELECT @accountCode = nullif(@accountCode,'')

	-- create GL Account for tax authority
	DECLARE @rc int, @GLAccountID int
	EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=5, @accountName=@authorityName, 
		@accountCode=@accountCode, @GLCode=null, @parentGLAccountID=null, @invoiceProfileID=null,
		@isSystemAccount=1, @isTaxable=0, @invoiceContentID=null, @GLAccountID=@GLAccountID OUTPUT
		
	-- add authority
	INSERT INTO dbo.tr_taxAuthorities (orgID, authorityName, GLAccountID, [status])
	VALUES (@orgID, @authorityName, @GLAccountID, 'A')
		SELECT @authorityID = SCOPE_IDENTITY()


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @authorityID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_createInvoiceMerchantProfile]
@invoiceID int,
@profileIDList varchar(max)

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- split list into table
	;WITH profiles AS (
		select listitem
		from dbo.fn_intListToTable(@profileIDList,',')
	)
	INSERT into dbo.tr_invoiceMerchantProfiles (invoiceID, profileID)
	SELECT @invoiceID, listitem
	from profiles
		except
	select invoiceID, profileID
	from dbo.tr_invoiceMerchantProfiles
	where invoiceID = @invoiceID

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_createInvoice]
@invoiceProfileID int,
@enteredByMemberID int, 
@assignedToMemberID int, 
@dateBilled datetime,
@dateDue datetime,
@invoiceID int OUTPUT,
@invoiceNumber varchar(18) OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @statusID int, @invoicenum int
	declare @orgID int, @orgcode varchar(10)
	declare @invoiceCode char(8)

	-- reset output vars
	select @invoiceID = 0, @invoiceNumber = ''

	-- dateDue should be 00:00:00 of passed in date
	select @dateDue = DATEADD(dd,DATEDIFF(dd,0,@dateDue),0)

	-- get open status
	select @statusID = statusID from dbo.tr_invoiceStatuses where [status] = 'Open'

	-- dont assume memberid is the active one. get the active one.
	select @assignedToMemberID=m.activeMemberID, @orgID=o.orgID, @orgcode=o.orgcode
		from dbo.ams_members as m
		inner join dbo.organizations as o on o.orgID = m.orgID
		where m.memberID = @assignedToMemberID 

	-- generate invoice code
	;WITH numbers AS ( select NUMBER as n, Abs(Checksum(Newid()))%26 as c from dbo.F_TABLE_NUMBER_RANGE(1,8) )
	SELECT top 1 @invoiceCode = CAST((SELECT TOP 8 CHAR(65 + case c 
									when 0 then 2 -- A = C
									when 1 then 22 -- B = W
									when 4 then 10 -- E = K
									when 8 then 7 -- I = H
									when 14 then 15 -- O = P
									when 18 then 19 -- S = T
									when 20 then 3 -- U = D
									when 25 then 17 -- Z = R
									else c end)
									FROM numbers n1
									WHERE n1.n >= -n2.n
									FOR XML PATH('')) AS CHAR(8))
	FROM numbers n2  

	-- get new invoiceNumber for this org
	select @invoicenum = isnull(max(i.invoiceNumber),0) + 1
		from dbo.tr_invoices as i 
		inner join dbo.ams_members as m on m.memberID = i.assignedToMemberID
		where m.orgID = @orgID

	-- insert invoice
	INSERT INTO dbo.tr_invoices (datecreated, dateBilled, dateDue, assignedToMemberID, statusID, invoiceNumber, invoiceCode, invoiceProfileID)
	VALUES (getdate(), @dateBilled, @dateDue, @assignedToMemberID, @statusID, @invoicenum, @invoiceCode, @invoiceProfileID)
		select @invoiceID = SCOPE_IDENTITY()

	insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
	values(@invoiceID, getdate(), 1, null, @enteredByMemberID)

	-- pad invoice number
	select @invoiceNumber = @orgcode + dbo.fn_tr_padInvoiceNumber(@invoicenum)
		
	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_createGLAccount]
@orgID int,
@accountTypeID int,
@accountName varchar(200),
@accountCode varchar(200),
@GLCode varchar(30),
@parentGLAccountID int,
@invoiceProfileID int,
@isSystemAccount bit,
@isTaxable bit,
@invoiceContentID int,
@GLAccountID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	SELECT @GLAccountID = null

	-- null account code if no length
	IF @accountCode is not null and len(@accountCode) = 0
		SELECT @accountCode = null
	IF @GLCode is not null and len(@GLCode) = 0
		SELECT @GLCode = null

	-- null invoiceProfileID if not revenue
	IF @accountTypeID <> 3
		SELECT @invoiceProfileID = null

	-- cant have multiple accounts with same accountName at same level (of same account type)
	-- cant have multiple accounts with same accountCode
	-- cant have multiple accounts with same GLCode
	IF (@parentGLAccountID is null AND EXISTS (select GLAccountID FROM dbo.tr_GLAccounts where orgID = @orgID and accountName = @accountName and parentGLAccountID is null and [status] <> 'D' and accountTypeID = @accountTypeID))
		OR (@parentGLAccountID is not null and EXISTS (select GLAccountID FROM dbo.tr_GLAccounts where orgID = @orgID and accountName = @accountName and parentGLAccountID = @parentGLAccountID and [status] <> 'D')) 
		OR (@accountCode is not null and EXISTS (select GLAccountID from dbo.tr_GLAccounts where orgID = @orgID and accountCode = @accountCode and [status] <> 'D'))
		OR (@GLCode is not null and EXISTS (select GLAccountID from dbo.tr_GLAccounts where orgID = @orgID and GLCode = @GLCode and [status] <> 'D'))
		OR (@accountTypeID = 3 and @invoiceProfileID is null)
		RAISERROR('this account would violate distinct rules', 16, 1);

	ELSE BEGIN
		INSERT INTO dbo.tr_GLAccounts (orgID, accountName, accountCode, parentGLAccountID, [status], isSystemAccount, isTaxable, accountTypeID, GLCode, [uid], invoiceProfileID, invoiceContentID)
		VALUES (@orgID, @accountName, nullif(@accountCode,''), @parentGLAccountID, 'A', @isSystemAccount, @isTaxable, @accountTypeID, nullif(@GLCode,''), newId(), @invoiceProfileID, @invoiceContentID)
			SELECT @GLAccountID = SCOPE_IDENTITY()
	END


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	SELECT @GLAccountID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_createDefaultGLAccounts]
@orgID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	DECLARE @GLAccountID int, @batchID int, @invoiceProfileID int

	-- Create one default Invoice Profile
	insert into dbo.tr_invoiceProfiles (orgID, profileName, status)
	values (@orgID, 'Default Invoice Profile', 'A')
		select @invoiceProfileID = SCOPE_IDENTITY()

	EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=2, @accountName='Accounts Receivable', @accountCode='ACCOUNTSRECEIVABLE', @GLCode='ACCOUNTSRECEIVABLE', @parentGLAccountID=null, @invoiceProfileID=null, @isSystemAccount=1, @isTaxable=0, @invoiceContentID=null, @GLAccountID=@GLAccountID output
	EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=5, @accountName='Deposits', @accountCode='DEPOSITS', @GLCode='DEPOSITS', @parentGLAccountID=null, @invoiceProfileID=null, @isSystemAccount=1, @isTaxable=0, @invoiceContentID=null, @GLAccountID=@GLAccountID output
	EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=4, @accountName='Write Off', @accountCode='WRITEOFF', @GLCode='WRITEOFF', @parentGLAccountID=null, @invoiceProfileID=null, @isSystemAccount=1, @isTaxable=0, @invoiceContentID=null, @GLAccountID=@GLAccountID output
	EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=3, @accountName='Misc Income', @accountCode='MISCINCOME', @GLCode='MISCINCOME', @parentGLAccountID=null, @invoiceProfileID=@invoiceProfileID, @isSystemAccount=1, @isTaxable=0, @invoiceContentID=null, @GLAccountID=@GLAccountID output
	
	-- create pending payment batch
	EXEC dbo.tr_createBatch @orgID=@orgID, @payProfileID=null, @batchCode='PENDINGPAYMENTS', @batchName='Pending Payments', @controlAmt=0, @controlCount=0, @depositDate='1/1/2020', @isSystemCreated=1, @createdByMemberID=null, @batchID=@batchID OUTPUT
	
	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_createBatch]
@orgID int,
@payProfileID int,
@batchCode varchar(40), 
@batchName varchar(400), 
@controlAmt money, 
@controlCount int, 
@depositDate datetime, 
@isSystemCreated bit,
@createdByMemberID int,
@batchID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- reset output var
	SELECT @batchID = 0

	-- get open status
	declare @statusID int
	select @statusID = statusID from dbo.tr_batchStatuses where [status] = 'Open'
	
	IF @isSystemCreated = 1
		select @createdByMemberID = null

	INSERT INTO dbo.tr_batches (orgID, statusID, batchCode, batchName, controlAmt, controlCount, depositDate, isSystemCreated, createdByMemberID, payProfileID)
	VALUES (@orgID, @statusID, @batchCode, @batchName, @controlAmt, @controlCount, DATEADD(dd, DATEDIFF(dd,0,@depositDate), 0), @isSystemCreated, @createdByMemberID, @payProfileID)
		SELECT @batchID = SCOPE_IDENTITY()

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_createApplication]
@applicationType varchar(100),
@transactionID int,
@itemType varchar(30),
@itemID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	INSERT INTO dbo.tr_applications (applicationTypeID, transactionID, itemType, itemID, status)
	VALUES (dbo.fn_getApplicationTypeIDFromName(@applicationType), @transactionID, @itemType, @itemID, 'A')

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_autoPostSystemBatches]

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- post batches
	update dbo.tr_batches
	set statusID = 4
	where isSystemCreated = 1
	and statusID <> 4
	and batchCode <> 'PENDINGPAYMENTS'
	and depositDate < DATEADD(dd, DATEDIFF(dd,0,getdate()), 0)

	-- put in control totals for system created posted batches. these will need to be updated.
	declare @tblBatches TABLE (batchID int, controlCount int, controlAmt money, actualCount int, actualAmt money)
	insert into @tblBatches (batchID, controlCount, controlAmt, actualCount, actualAmt)
	select b.batchID, b.controlCount, b.controlAmt, isnull(actual.actualCount,0), isnull(actual.actualAmount,0)
	from dbo.tr_batches as b
	outer apply dbo.fn_tr_getBatchActual(b.batchID) as actual
	where b.statusID = 4
	and b.batchCode <> 'PENDINGPAYMENTS'
	and b.isSystemCreated = 1
	and b.depositDate < DATEADD(dd, DATEDIFF(dd,0,getdate()), 0)
	and (
		b.controlCount <> isnull(actual.actualCount,0)
		OR 
		b.controlAmt <> isnull(actual.actualAmount,0)
	)
		
	update b
	set b.controlCount = tbl.actualCount
	from dbo.tr_batches as b
	inner join @tblBatches as tbl on tbl.batchID = b.batchID
	where b.controlCount <> tbl.actualCount

	update b
	set b.controlAmt = tbl.actualAmt
	from dbo.tr_batches as b
	inner join @tblBatches as tbl on tbl.batchID = b.batchID
	where b.controlAmt <> tbl.actualAmt

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_autoPayInvoices]
AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @jobUID uniqueidentifier
	select @jobUID = NewID()

	; WITH innerTbl as (
		select i.invoiceID, m2.memberID, o.orgID, s.siteID, mp.gatewayID, mp.profileID,
			o.useBatches, mpp.customerProfileID, mpp.paymentProfileID, mp.profileCode,
			s.siteName, i.dateCreated, i.dateBilled, i.dateDue, 
			o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber,
			isnull(m2.lastname,'') + ', ' + isnull(m2.firstname,'') + case when o.hasMiddleName = 1 then isnull(' ' + m2.middlename,'') else '' end + ' (' + m2.membernumber + ')' as memberName,
			mpp.detail, mp.profileName, o.accountingEmail
		from dbo.tr_invoices as i
		inner join dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = i.payProfileID
		inner join dbo.ams_members as m on m.memberid = i.assignedToMemberID
		inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
		inner join dbo.organizations as o on o.orgID = m2.orgID
		inner join dbo.mp_profiles as mp on mp.profileID = mpp.profileID
		inner join dbo.sites as s on s.siteID = mp.siteID
		where i.statusID = 3
		and i.dateDue < getdate()
		and mpp.status = 'A'
		and mp.status = 'A'
		and mp.allowPayments = 1
	)

	insert into customApps.dbo.schedTask_autoPayInvoices (jobID, invoiceID, memberID, orgID,
		siteID, gatewayID, profileID, useBatches, customerProfileID, paymentProfileID,
		profileCode, siteName, chargeAmount, dateCreated, dateBilled, dateDue, invoiceNumber, 
		memberName, detail, profileName, accountingEmail)
	select @jobUID, tmp.invoiceID, tmp.memberID, tmp.orgID, tmp.siteID, tmp.gatewayID, tmp.profileID, 
		tmp.useBatches, tmp.customerProfileID, tmp.paymentProfileID, tmp.profileCode, tmp.siteName, 
		sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) as chargeAmount,
		tmp.dateCreated, tmp.dateBilled, tmp.dateDue, tmp.invoiceNumber, tmp.memberName, tmp.detail,
		tmp.profileName, tmp.accountingEmail
	from innerTbl as tmp
	left outer join dbo.tr_invoiceTransactions as it on it.invoiceID = tmp.invoiceID
	group by tmp.invoiceID, tmp.memberID, tmp.orgID, tmp.siteID, tmp.gatewayID, tmp.profileID, 
		tmp.useBatches, tmp.customerProfileID, tmp.paymentProfileID, tmp.profileCode, tmp.siteName, 
		tmp.dateCreated, tmp.dateBilled, tmp.dateDue, tmp.invoiceNumber, tmp.memberName, tmp.detail,
		tmp.profileName, tmp.accountingEmail
	having sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) > 0
	order by 1, 2, 3

	select *
	from customApps.dbo.schedTask_autoPayInvoices
	where jobID = @jobUID


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_acceptPaymentTransaction]
@enteredByMemberID int,
@paymentTransactionID int,
@transactionDate datetime,
@batchID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @activeStatusID int

	-- get status
	select @activeStatusID = dbo.fn_tr_getStatusID('Active')

	-- if transactionDate is null, get existing date from transaction
	IF @transactionDate is null
		select @transactionDate = transactionDate
		from dbo.tr_transactions
		where transactionID = @paymentTransactionID

	-- if batchID is null or not an open batch, reject payment.
	IF @batchID is null OR NOT EXISTS (
		select batchID
		from dbo.tr_batches
		where batchID = @batchID
		and statusID = 1)
		RAISERROR('batchid is not eligible', 16, 1);

	-- update the status of the payment from Pending to Active.
	-- update the transactionDate of the payment to provided date
	update dbo.tr_transactions
	set statusID = @activeStatusID, transactionDate = @transactionDate
	where transactionID = @paymentTransactionID

	-- get allocations to this batch for later use
	declare @tblAlloc TABLE (transactionID int)
	insert into @tblAlloc (transactionID)
	select distinct tAlloc.transactionID
	from dbo.tr_transactions as tSale
	inner join dbo.tr_relationships as rS on rS.appliedToTransactionID = tSale.transactionID
	inner join dbo.tr_relationshipTypes as rtS on rtS.typeID = rS.typeID and rtS.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as tAlloc on tAlloc.transactionID = rS.transactionID and tAlloc.statusID in (1,3)
	inner join dbo.tr_GLAccounts as glAllocDeb on glAllocDeb.GLAccountID = tAlloc.debitGLAccountID
	inner join dbo.tr_relationships as rP on rP.transactionID = tAlloc.transactionID
	inner join dbo.tr_relationshipTypes as rtP on rtP.typeID = rP.typeID and rtP.type = 'AllocPayTrans'
	inner join dbo.tr_transactions as tPay on tPay.transactionID = rP.appliedToTransactionID and tPay.statusID in (1,3)
		and tPay.transactionID = @paymentTransactionID

	-- move payment from pending batch to selected batch
	update dbo.tr_batchTransactions
	set batchID = @batchID 
	where transactionID = @paymentTransactionID

	-- move allocations from pending batch to selected batch
	update bt
	set bt.batchID = @batchID 
	from dbo.tr_batchTransactions as bt
	inner join @tblAlloc as alloc on alloc.transactionID = bt.transactionID

	-- update the transactionDate of all allocations to this payment
	update t
	set t.transactionDate = @transactionDate
	from dbo.tr_transactions as t
	inner join @tblAlloc as alloc on alloc.transactionID = t.transactionID

	-- update sales cache amounts to show no longer pending allocation
	update ts
	set cache_activePaymentAllocatedAmount = amt.activePaymentAllocatedAmount,
		cache_pendingPaymentAllocatedAmount = amt.pendingPaymentAllocatedAmount
	from dbo.tr_transactionSales as ts
	cross apply dbo.fn_tr_getAllocatedAmountofSale(ts.transactionid) as amt
	where ts.transactionID in (
		select tSale.transactionID
		from dbo.tr_transactions as tAlloc
		inner join dbo.tr_relationships as rP on rP.transactionID = tAlloc.transactionID and rP.appliedToTransactionID = @paymentTransactionID
		inner join dbo.tr_relationshipTypes as rtP on rtP.typeID = rP.typeID and rtP.type = 'AllocPayTrans'
		inner join dbo.tr_transactions as tPay on tPay.transactionID = rP.appliedToTransactionID and tPay.statusID in (1,3)
		inner join dbo.tr_relationships as rS on rS.transactionID = tAlloc.transactionID
		inner join dbo.tr_relationshipTypes as rtS on rtS.typeID = rS.typeID and rtS.type = 'AllocSaleTrans'
		inner join dbo.tr_transactions as tSale on tSale.transactionID = rS.appliedToTransactionID and tSale.statusID in (1,3)
		where tAlloc.statusID in (1,3)
			union
		select tSale.transactionID
		from dbo.tr_transactions as tAlloc
		inner join dbo.tr_relationships as rP on rP.transactionID = tAlloc.transactionID and rP.appliedToTransactionID = @paymentTransactionID
		inner join dbo.tr_relationshipTypes as rtP on rtP.typeID = rP.typeID and rtP.type = 'AllocPayTrans'
		inner join dbo.tr_transactions as tPay on tPay.transactionID = rP.appliedToTransactionID and tPay.statusID in (1,3)
		inner join dbo.tr_relationships as rA on rA.transactionID = tAlloc.transactionID
		inner join dbo.tr_relationshipTypes as rtA on rtA.typeID = rA.typeID and rtA.type = 'AllocSaleTrans'
		inner join dbo.tr_transactions as tAdj on tAdj.transactionID = rA.appliedToTransactionID and tAdj.statusID in (1,3)
		inner join dbo.tr_relationships as rS on rS.transactionID = tAdj.transactionID
		inner join dbo.tr_relationshipTypes as rtS on rtS.typeID = rS.typeID and rtS.type = 'AdjustTrans'
		inner join dbo.tr_transactions as tSale on tSale.transactionID = rS.appliedToTransactionID and tSale.statusID in (1,3)
		where tAlloc.statusID in (1,3)
	)

	-- update invoice transaction cache amounts to show no longer pending allocation
	update it
	set cache_activePaymentAllocatedAmount = amt.activePaymentAllocatedAmount,
		cache_pendingPaymentAllocatedAmount = amt.pendingPaymentAllocatedAmount
	from dbo.tr_invoiceTransactions as it
	cross apply dbo.fn_tr_getAllocatedAmountofSaleOrAdj(it.transactionid,null) as amt
	where it.transactionID in (
		select tSale.transactionID
		from dbo.tr_transactions as tAlloc
		inner join dbo.tr_relationships as rP on rP.transactionID = tAlloc.transactionID and rP.appliedToTransactionID = @paymentTransactionID
		inner join dbo.tr_relationshipTypes as rtP on rtP.typeID = rP.typeID and rtP.type = 'AllocPayTrans'
		inner join dbo.tr_transactions as tPay on tPay.transactionID = rP.appliedToTransactionID and tPay.statusID in (1,3)
		inner join dbo.tr_relationships as rS on rS.transactionID = tAlloc.transactionID
		inner join dbo.tr_relationshipTypes as rtS on rtS.typeID = rS.typeID and rtS.type = 'AllocSaleTrans'
		inner join dbo.tr_transactions as tSale on tSale.transactionID = rS.appliedToTransactionID and tSale.statusID in (1,3)
		where tAlloc.statusID in (1,3)
	)

	-- for each invoice tied to invoiceTransactions tied to allocated payment, run the invoice checks
	declare @tblInv TABLE (invoiceID int, [status] varchar(50))
	insert into @tblInv (invoiceID, [status])
	select distinct i.invoiceID, ins.status
	from dbo.tr_transactions as tAlloc
	inner join dbo.tr_relationships as rP on rP.transactionID = tAlloc.transactionID and rP.appliedToTransactionID = @paymentTransactionID
	inner join dbo.tr_relationshipTypes as rtP on rtP.typeID = rP.typeID and rtP.type = 'AllocPayTrans'
	inner join dbo.tr_transactions as tPay on tPay.transactionID = rP.appliedToTransactionID and tPay.statusID in (1,3)
	inner join dbo.tr_relationships as rS on rS.transactionID = tAlloc.transactionID
	inner join dbo.tr_relationshipTypes as rtS on rtS.typeID = rS.typeID and rtS.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as tSale on tSale.transactionID = rS.appliedToTransactionID and tSale.statusID in (1,3)
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSale.transactionID
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
	where tAlloc.statusID in (1,3)
	
	declare @invoiceID int, @invStatus varchar(50), @amtDueNoPendingOnInvoice money
	select @invoiceID = min(invoiceID) from @tblInv
	while @invoiceID is not null BEGIN
		select @invStatus = [status] from @tblInv where invoiceID = @invoiceID

		-- cleanup invoice
		-- if invoice is closed and is now fully paid with active payments, mark it as paid
		-- if invoice is paid and is now not fully paid with active payments, mark it as closed
		select @amtDueNoPendingOnInvoice = sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount)
			from dbo.tr_invoiceTransactions as it
			inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
			where it.invoiceID = @invoiceID
			and t.statusID <> 2
		IF @invStatus = 'closed' and @amtDueNoPendingOnInvoice = 0 BEGIN
			update dbo.tr_invoices
			set statusID = 4, payProfileID = null
			where invoiceID = @invoiceID

			insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
			values (@invoiceID, getdate(), 4, 3, @enteredByMemberID)
		END
		IF @invStatus = 'paid' and @amtDueNoPendingOnInvoice > 0 BEGIN
			update dbo.tr_invoices
			set statusID = 3
			where invoiceID = @invoiceID

			insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
			values (@invoiceID, getdate(), 3, 4, @enteredByMemberID)
		END

		select @invoiceID = min(invoiceID) from @tblInv where invoiceID > @invoiceID
	END

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[createOrganization]
@orgcode varchar(10),
@orgName varchar(100),
@orgshortname varchar(20),
@orgURL varchar(200),
@orgID int OUTPUT

AS

DECLARE @rc int, @groupID int	

-- null passed in vars
SELECT @orgID = null

BEGIN TRAN
	-- if org already exists, error
	IF EXISTS (select orgID FROM dbo.organizations where orgcode = @orgcode)
		GOTO on_error

	-- orgcode cannot start with an underscore (would potentially conflict with views)
	IF LEFT(@orgcode,1) = '_'
		GOTO on_error

	-- add org with default values
	INSERT INTO dbo.organizations (orgcode, orgName, orgshortname, orgURL, hasPrefix, hasMiddleName, hasSuffix, hasProfessionalSuffix, hasCompany, cache_perms_status, usePrefixList, useBatches, memNumPrefixGuest, memNumPrefixUser, accountingEmail)
	VALUES (@orgcode, @orgName, @orgshortname, @orgURL, 1, 1, 1, 1, 1, 'disabled', 0, 0, @orgcode, @orgcode, '')
		IF @@ERROR <> 0 GOTO on_error
		SELECT @orgID = SCOPE_IDENTITY()

	-- create default GL Accounts
	EXEC dbo.tr_createDefaultGLAccounts @orgID=@orgID

	-- create default groups
	EXEC @rc = dbo.ams_createDefaultGroups @orgID=@orgID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- create default address types
	EXEC @rc = dbo.ams_createDefaultMemberAddressTypes @orgID=@orgid
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- create default phone types
	EXEC @rc = dbo.ams_createDefaultMemberPhoneTypes @orgID=@orgid
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- create default email types
	EXEC @rc = dbo.ams_createDefaultMemberEmailTypes @orgID=@orgid
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- create default website types
	EXEC @rc = dbo.ams_createDefaultMemberWebsiteTypes @orgID=@orgid
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- create default prof license statuses
	EXEC @rc = dbo.ams_createDefaultMemberLicenseStatuses @orgID=@orgid
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- create default memberdata view
	EXEC @rc = dbo.ams_createVWMemberData @orgID=@orgID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
	
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @orgID = 0
	RETURN -1

on_error2:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1

GO

ALTER PROC [dbo].[ev_removeRegistrant]
@registrantID int,
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @ApplicationTypeID int, @minTID int, @adjtransactionID int, 
		@assignedToMemberID int, @invoiceProfileID int, @invoiceID int, @registrationID int,
		@GLAccountID int
	declare @invoiceNumber varchar(18)
	DECLARE @adjAmount money
	declare @nowdate datetime
	DECLARE @tblAdjust TABLE (transactionID int, amountToAdjust money, creditGLAccountID int)
	DECLARE @tblInvoices TABLE (invoiceID int, invoiceProfileID int)

	select @nowdate = getdate()
	select @ApplicationTypeID = dbo.fn_getApplicationTypeIDFromName('Events')
	select @assignedToMemberID = m.activeMemberID, @registrationID = r.registrationID
		from dbo.ev_registrants as r
		inner join dbo.ams_members as m on m.memberID = r.memberID
		where r.registrantID = @registrantID

	-- put all open invoices used for this registration into table since they were already created and can be used for adjustments
	insert into @tblInvoices (invoiceID, invoiceProfileID)
	select distinct i.invoiceID, i.invoiceProfileID
	from dbo.fn_ev_registrantTransactions(@registrantID) as rt
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = rt.transactionID
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	where i.statusID = 1

	-- update registrant status
	UPDATE dbo.ev_registrants
	SET [status] = 'D'
	WHERE registrantID = @registrantID
	
	UPDATE dbo.tr_applications
	SET [status] = 'D'
	WHERE itemID = @registrantID
	AND itemType = 'Rate'
	AND applicationTypeID = @ApplicationTypeID

	-- update registrant details status
	UPDATE dbo.ev_registrantDetails
	SET [status] = 'D'
	WHERE registrantID = @registrantID

	UPDATE dbo.tr_applications
	SET [status] = 'D'
	WHERE itemID in (select detailID from dbo.ev_registrantDetails WHERE registrantID = @registrantID)
	AND itemType = 'Custom'
	AND applicationTypeID = @ApplicationTypeID

	-- get all registrant-related sales transactions we need to adjust
	INSERT INTO @tblAdjust (transactionID, amountToAdjust, creditGLAccountID)
	select rt.transactionID, ts.cache_amountAfterAdjustment, t.creditGLAccountID
	from dbo.fn_ev_registrantTransactions(@registrantID) as rt
	inner join dbo.tr_transactionSales as ts on ts.transactionID = rt.transactionID
	inner join dbo.tr_transactions as t on t.transactionID = ts.transactionID
	where ts.cache_amountAfterAdjustment > 0
	and rt.typeID = 1

	-- if there are adjustments to make
	IF EXISTS (select transactionID from @tblAdjust) BEGIN
		SELECT @minTID = min(transactionID) from @tblAdjust
		WHILE @minTID IS NOT NULL BEGIN
			select @invoiceProfileID = null, @invoiceID = null, @adjAmount = null, @GLAccountID = null

			select @adjAmount = amountToAdjust*-1, @GLAccountID = creditGLAccountID from @tblAdjust where transactionID = @minTID
			select @invoiceProfileID = invoiceProfileID from dbo.tr_glAccounts where glAccountID = @GLAccountID
			select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID
			
			-- if necessary, create invoice assigned to registrant based on invoice profile
			IF @invoiceID is null BEGIN
				EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
					@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@nowdate, 
					@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT

				insert into @tblInvoices (invoiceID, invoiceProfileID)
				values (@invoiceID, @invoiceProfileID)
			END	

			EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@recordedOnSiteID,
				@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID,
				@status='Active', @amount=@adjAmount, @transactionDate=@nowdate,
				@saleTransactionID=@minTID, @invoiceID=@invoiceID, @transactionID=@adjtransactionID OUTPUT
			
			SELECT @minTID = min(transactionID) from @tblAdjust where transactionID > @minTID
		END
	END

	-- queue processing of member groups
	insert into dbo.queue_processMemberGroups (orgID, memberID, conditionID)
	select c.orgID, @assignedToMemberID, c.conditionID
	from dbo.ams_virtualGroupConditions as c
	inner join ev_registration er
		on c.fieldcode = 'e_' + cast(er.eventID as varchar(10))
		and er.registrationID = @registrationID

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

