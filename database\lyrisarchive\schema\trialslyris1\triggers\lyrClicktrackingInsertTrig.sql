CREATE TRIGGER lyrClicktrackingInsertTrig
ON clicktracking_
AFTER INSERT AS
BEGIN
	SET NOCOUNT ON
	INSERT INTO lyrUnsummarizedClicks (ClickID, IPAddress , MemberID, MailingID, StreamWebPageName, TimeClicked, UrlID)
		SELECT ClickID_, IPAddress_, MemberID_, MessageID_, StreamWebPageName_, TimeClicked_, UrlID_ FROM inserted 
END;
ALTER TABLE [dbo].[clicktracking_] ENABLE TRIGGER [lyrClicktrackingInsertTrig]
GO
