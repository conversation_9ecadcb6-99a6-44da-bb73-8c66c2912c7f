use [membercentral]
GO
ALTER TABLE dbo.fs_fileShare ADD
	showDocDownloadCountToMembers bit NOT NULL CONSTRAINT DF_fs_fileShare_showDocDownloadCountToMembers DEFAULT 0
GO




USE [platformstats]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [dbo].[stats_docDownloads]
@siteID int,
@applicationInstanceID int,
@statsStart smalldatetime,
@statsEnd smalldatetime

WITH RECOMPILE

AS

SET NOCOUNT ON

-- declare vars
DECLARE @sessionStart smalldatetime, @sessionEnd smalldatetime

/*
24hr padding with (NOLOCK) ON session start time just to make sure 
that we have the sessions for ALL of the hits in the time range
-- reset start date to 00:00 of startdate
-- reset end date to 23:59 of enddate (actually 0:00 of next day)
*/
select @sessionStart = DateAdd(dd,-1,@statsStart)
select @sessionEnd = @statsEnd
SELECT @statsStart = DATEADD(dd, DATEDIFF(dd,0,@statsStart), 0)
SELECT @statsEnd = DATEADD(dd, DATEDIFF(dd,0,@statsEnd)+1, 0)


-- qryTop10InDateRange
select top 10 count(dh.docHitID) as viewCount, dl.docTitle, dv.filename, d.documentID
from dbo.statsDocumentHits dh with (NOLOCK)
inner join dbo.statsSessions ss with (NOLOCK)
	on ss.sessionid = dh.sessionid
	and dh.dateentered BETWEEN @statsStart and @statsEnd 
	and ss.siteID = @siteID
inner join membercentral.dbo.cms_documentVersions dv with (NOLOCK)
	on dv.documentVersionID = dh.documentVersionID
inner join membercentral.dbo.cms_documentLanguages dl with (NOLOCK)
	on dl.documentLanguageID = dv.documentLanguageID
inner join membercentral.dbo.cms_documents d with (NOLOCK)
	on d.documentID = dl.documentID
inner join membercentral.dbo.fs_fileshare fs with (NOLOCK)
	on fs.rootSectionID = d.sectionID
	and fs.applicationInstanceID = @applicationInstanceID
group by dl.docTitle, dv.filename, d.documentID
order by count(dh.docHitID) desc
option (RECOMPILE)

-- qryTop10AllTime
select top 10 count(dh.docHitID) as viewCount, dl.docTitle, dv.filename, d.documentID
from dbo.statsDocumentHits dh with (NOLOCK)
inner join dbo.statsSessions ss with (NOLOCK)
	on ss.sessionid = dh.sessionid
	and ss.siteID = @siteID
inner join membercentral.dbo.cms_documentVersions dv with (NOLOCK)
	on dv.documentVersionID = dh.documentVersionID
inner join membercentral.dbo.cms_documentLanguages dl with (NOLOCK)
	on dl.documentLanguageID = dv.documentLanguageID
inner join membercentral.dbo.cms_documents d with (NOLOCK)
	on d.documentID = dl.documentID
inner join membercentral.dbo.fs_fileshare fs with (NOLOCK)
	on fs.rootSectionID = d.sectionID
	and fs.applicationInstanceID = @applicationInstanceID
group by dl.docTitle, dv.filename, d.documentID
order by count(dh.docHitID) desc
option (RECOMPILE)

RETURN 0
