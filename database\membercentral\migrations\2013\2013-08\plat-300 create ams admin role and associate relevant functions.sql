-- create AMS Admin ROle and add initial functions

declare @amsAdminRoleID int

exec dbo.cms_createSiteResourceRole
	@roleName='AMS Administrator',
	@roleTypeName='UniversalRole',
	@roleID = @amsAdminRoleID OUTPUT

declare @resourceTypeID int, @resourceTypeName varchar(50), @functionID int, @functionName varchar(50), @functionDisplayName varchar(50), @addToSuperUserRole bit, @addToSiteAdminRole bit

DECLARE @rc int, @ResourceTypeFunctionID int

select @resourceTypeName = 'MemberHistoryAdmin', @functionName = 'ViewMemberHistoryAdmin'
select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)
SELECT @functionID = dbo.fn_getResourceFunctionID(@functionName,@resourceTypeID)
select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);
exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@amsAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;

select @resourceTypeName = 'MemberHistoryAdmin', @functionName = 'DeleteMemberHistory'
select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)
SELECT @functionID = dbo.fn_getResourceFunctionID(@functionName,@resourceTypeID)
select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);
exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@amsAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;

select @resourceTypeName = 'MemberHistoryAdmin', @functionName = 'ViewMemberHistoryTypes'
select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)
SELECT @functionID = dbo.fn_getResourceFunctionID(@functionName,@resourceTypeID)
select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);
exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@amsAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;

select @resourceTypeName = 'MemberHistoryAdmin', @functionName = 'EditCategories'
select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)
SELECT @functionID = dbo.fn_getResourceFunctionID(@functionName,@resourceTypeID)
select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);
exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@amsAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;

GO


declare @resourceTypeID int, @resourceTypeName varchar(50), @functionID int, @functionName varchar(50), @functionDisplayName varchar(50), @addToSuperUserRole bit, @addToSiteAdminRole bit

DECLARE @rc int, @superAdminRoleID int, @amsAdminRoleID int, @ResourceTypeFunctionID int

set @resourceTypeName = 'MemberHistoryAdmin'
set @functionName = 'ViewRelationships'
set @functionDisplayName = 'View Relationships'

--- Do not change below this line

select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)
select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')
select @amsAdminRoleID = dbo.fn_getResourceRoleID('AMS Administrator')


EXEC @rc = cms_createSiteResourceFunction
	@resourceTypeID=@resourceTypeID,
	@functionName=@functionName,
	@displayName=@functionDisplayName,
	@functionID=@functionID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);
			IF @@ERROR <> 0 GOTO on_error

exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@superAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@amsAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


-- error exit
on_error:




GO


declare @resourceTypeID int, @resourceTypeName varchar(50), @functionID int, @functionName varchar(50), @functionDisplayName varchar(50), @addToSuperUserRole bit, @addToSiteAdminRole bit

DECLARE @rc int, @superAdminRoleID int, @amsAdminRoleID int, @ResourceTypeFunctionID int

set @resourceTypeName = 'MemberHistoryAdmin'
set @functionName = 'EditRelationships'
set @functionDisplayName = 'Edit Relationships'

--- Do not change below this line

select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)
select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')
select @amsAdminRoleID = dbo.fn_getResourceRoleID('AMS Administrator')


EXEC @rc = cms_createSiteResourceFunction
	@resourceTypeID=@resourceTypeID,
	@functionName=@functionName,
	@displayName=@functionDisplayName,
	@functionID=@functionID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);
			IF @@ERROR <> 0 GOTO on_error

exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@superAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@amsAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- error exit
on_error:



GO


declare @resourceTypeID int, @resourceTypeName varchar(50), @functionID int, @functionName varchar(50), @functionDisplayName varchar(50), @addToSuperUserRole bit, @addToSiteAdminRole bit

DECLARE @rc int, @superAdminRoleID int, @siteAdminRoleID int, @ResourceTypeFunctionID int

set @resourceTypeName = 'MemberHistoryAdmin'
set @functionName = 'ViewNotes'
set @functionDisplayName = 'View Notes Admin Tool'

--- Do not change below this line

select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)
select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')
select @siteAdminRoleID = dbo.fn_getResourceRoleID('Site Administrator')


EXEC @rc = cms_createSiteResourceFunction
	@resourceTypeID=@resourceTypeID,
	@functionName=@functionName,
	@displayName=@functionDisplayName,
	@functionID=@functionID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);
			IF @@ERROR <> 0 GOTO on_error

exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@superAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@siteAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


-- error exit
on_error:




GO


declare @resourceTypeID int, @resourceTypeName varchar(50), @functionID int, @functionName varchar(50), @functionDisplayName varchar(50), @addToSuperUserRole bit, @addToSiteAdminRole bit

DECLARE @rc int, @superAdminRoleID int, @siteAdminRoleID int, @ResourceTypeFunctionID int

set @resourceTypeName = 'MemberHistoryAdmin'
set @functionName = 'editNotes'
set @functionDisplayName = 'Edit Member Notes'

--- Do not change below this line

select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)
select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')
select @siteAdminRoleID = dbo.fn_getResourceRoleID('Site Administrator')


EXEC @rc = cms_createSiteResourceFunction
	@resourceTypeID=@resourceTypeID,
	@functionName=@functionName,
	@displayName=@functionDisplayName,
	@functionID=@functionID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);
			IF @@ERROR <> 0 GOTO on_error

exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@superAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@siteAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


-- error exit
on_error:




GO


-- Award siteadmin group of existing AMS clients the AMS Administrator Role

DECLARE @amsAdminRoleID int
select @amsAdminRoleID = dbo.fn_getResourceRoleID('AMS Administrator')
declare @permissionsToAdd TABLE (autoID int IDENTITY(1,1), siteID int, siteResourceID int, groupID int)
declare 
	@thisAutoID int,
	@thissiteID int,
	@thissiteResourceID int,
	@thisgroupID int,
	@trashID int



insert into @permissionsToAdd (siteID, siteResourceID, groupID)
select distinct s.siteID, s.siteResourceID, g.groupID
from sub_types t
inner join sites s
	on s.siteID = t.siteID
inner join ams_groups g
	on g.orgID = s.orgID
	and g.groupCode = 'siteadmins'


SELECT @thisAutoID = min(autoid) from @permissionsToAdd
WHILE @thisAutoID IS NOT NULL BEGIN
	select
		@thissiteID = siteID,
		@thissiteResourceID = siteResourceID,
		@thisgroupID = groupID
	from @permissionsToAdd where autoID = @thisAutoID

	EXEC dbo.cms_createSiteResourceRight
		@siteID=@thissiteID,
		@siteResourceID=@thissiteResourceID,
		@include=1, 
		@functionID=null,
		@roleID=@amsAdminRoleID,
		@groupID=@thisgroupID,
		@memberID=null, 
		@inheritedRightsResourceID=null,
		@inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT

	SELECT @thisAutoID = min(autoid) from @permissionsToAdd where autoid > @thisAutoID
END




GO

-- Remove AMS Only functions from the site administrator role

declare @resourceTypeID int, @resourceTypeName varchar(50), @functionID int, @functionName varchar(50)
DECLARE @rc int, @siteAdminRoleID int, @ResourceTypeFunctionID int
select @siteAdminRoleID = dbo.fn_getResourceRoleID('Site Administrator')

select @resourceTypeName = 'RelationshipAdmin', @functionName = 'ViewRelationships'
select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)
SELECT @functionID = dbo.fn_getResourceFunctionID(@functionName,@resourceTypeID)
select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);
exec @rc = dbo.cms_deleteSiteResourceRoleFunction @roleID=@siteAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;

select @resourceTypeName = 'RelationshipAdmin', @functionName = 'EditRelationships'
select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)
SELECT @functionID = dbo.fn_getResourceFunctionID(@functionName,@resourceTypeID)
select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);
exec @rc = dbo.cms_deleteSiteResourceRoleFunction @roleID=@siteAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;

select @resourceTypeName = 'MemberHistoryAdmin', @functionName = 'ViewMemberHistoryAdmin'
select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)
SELECT @functionID = dbo.fn_getResourceFunctionID(@functionName,@resourceTypeID)
select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);
exec @rc = dbo.cms_deleteSiteResourceRoleFunction @roleID=@siteAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;

select @resourceTypeName = 'MemberHistoryAdmin', @functionName = 'DeleteMemberHistory'
select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)
SELECT @functionID = dbo.fn_getResourceFunctionID(@functionName,@resourceTypeID)
select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);
exec @rc = dbo.cms_deleteSiteResourceRoleFunction @roleID=@siteAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;

select @resourceTypeName = 'MemberHistoryAdmin', @functionName = 'ViewMemberHistoryTypes'
select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)
SELECT @functionID = dbo.fn_getResourceFunctionID(@functionName,@resourceTypeID)
select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);
exec @rc = dbo.cms_deleteSiteResourceRoleFunction @roleID=@siteAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;

select @resourceTypeName = 'MemberHistoryAdmin', @functionName = 'EditCategories'
select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)
SELECT @functionID = dbo.fn_getResourceFunctionID(@functionName,@resourceTypeID)
select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);
exec @rc = dbo.cms_deleteSiteResourceRoleFunction @roleID=@siteAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;

GO



declare @toolTypeID int
select @toolTypeID = toolTypeID
from admin_toolTypes
where toolType = 'MemberHistoryAdmin'

insert into dbo.admin_siteToolRestrictions (toolTypeID, siteID)
select distinct @toolTypeID, sr.siteID
from cms_siteResourceRoles srroles
inner join cms_siteResourceRights srr
	on srroles.roleID = srr.roleID
	and srroles.roleName = 'AMS Administrator'
inner join cms_siteResources sr
	on srr.resourceID = sr.siteResourceID

GO

ALTER PROC [dbo].[enableSiteFeature]
	@siteID int,
	@toolTypeList varchar(1000)
AS

declare 
	@toolType varchar (100), 
	@tempList varchar(1000), 
	@itemCounter int,
	@sitecode varchar(10),
	@resourceTypeID int,
	@sectionID int,
	@parentSectionID int

-- @toolTypeList list is separated by ','
set @tempList = @toolTypeList
set @itemCounter = 1

select @sitecode = sitecode from sites where siteID = @siteID

while len(@tempList) > 0
begin
	set @toolType = left(@tempList, charindex(',', @tempList+',')-1)

	-- subscriptions
	if @toolType = 'SubscriptionAdmin' begin
		update 
			ai
		set 
			settingsXML.modify('replace value of (/settings/setting[@name=''showSubscriptions'']/@value)[1] with ''true''')
		from 
			dbo.cms_applicationInstances as ai
			inner join dbo.sites as s on 
				s.siteID = ai.siteID
				and s.siteID = @siteID
		where 
			ai.applicationInstanceName = 'admin'

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select 
			tooltypeID, @siteID
		from 
			dbo.admin_toolTypes
		where 
			toolType = @toolType
		except
		select 
			tooltypeID, siteID 
		from 
			dbo.admin_siteToolRestrictions

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select 
			tooltypeID, @siteID
		from 
			dbo.admin_toolTypes
		where 
			toolType = 'SubRenewalAdmin'
		except
		select 
			tooltypeID, siteID 
		from 
			dbo.admin_siteToolRestrictions
	end

	-- referrals
	if @toolType = 'ReferralsAdmin' begin
		update 
			ai
		set 
			settingsXML.modify('replace value of (/settings/setting[@name=''showReferrals'']/@value)[1] with ''true''')
		from 
			dbo.cms_applicationInstances as ai
			inner join dbo.sites as s on 
				s.siteID = ai.siteID
				and s.siteID = @siteID
		where 
			ai.applicationInstanceName = 'admin'

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select 
			tooltypeID, @siteID
		from 
			dbo.admin_toolTypes
		where 
			toolType = @toolType
		except
		select 
			tooltypeID, siteID 
		from 
			dbo.admin_siteToolRestrictions
	end

	-- member history
	if @toolType = 'MemberHistoryAdmin' begin
		update 
			ai
		set 
			settingsXML.modify('replace value of (/settings/setting[@name=''showMemberHistory'']/@value)[1] with ''true''')
		from 
			dbo.cms_applicationInstances as ai
			inner join dbo.sites as s on 
				s.siteID = ai.siteID
				and s.siteID = @siteID
		where 
			ai.applicationInstanceName = 'admin'

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select 
			tooltypeID, @siteID
		from 
			dbo.admin_toolTypes
		where 
			toolType = @toolType
		except
		select 
			tooltypeID, siteID 
		from 
			dbo.admin_siteToolRestrictions
	end

	-- relationships
	if @toolType = 'RelationshipAdmin' begin
		update 
			ai
		set 
			settingsXML.modify('replace value of (/settings/setting[@name=''showRelationships'']/@value)[1] with ''true''')
		from 
			dbo.cms_applicationInstances as ai
			inner join dbo.sites as s on 
				s.siteID = ai.siteID
				and s.siteID = @siteID
		where 
			ai.applicationInstanceName = 'admin'

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select 
			tooltypeID, @siteID
		from 
			dbo.admin_toolTypes
		where 
			toolType = @toolType
		except
		select 
			tooltypeID, siteID 
		from 
			dbo.admin_siteToolRestrictions
	end

	-- tasks
	if @toolType = 'NotesAdmin' begin
		update 
			ai
		set 
			settingsXML.modify('replace value of (/settings/setting[@name=''showNotes'']/@value)[1] with ''true''')
		from 
			dbo.cms_applicationInstances as ai
			inner join dbo.sites as s on 
				s.siteID = ai.siteID
				and s.siteID = @siteID
		where 
			ai.applicationInstanceName = 'admin'

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select 
			tooltypeID, @siteID
		from 
			dbo.admin_toolTypes
		where 
			toolType = @toolType
		except
		select 
			tooltypeID, siteID 
		from 
			dbo.admin_siteToolRestrictions
	end

	-- member documents
	if @toolType = 'MemberDocs' begin
		update 
			ai
		set 
			settingsXML.modify('replace value of (/settings/setting[@name=''showMemberDocuments'']/@value)[1] with ''true''')
		from 
			dbo.cms_applicationInstances as ai
			inner join dbo.sites as s on 
				s.siteID = ai.siteID
				and s.siteID = @siteID
		where 
			ai.applicationInstanceName = 'admin'

		select 
			@resourceTypeID = srt.resourceTypeID
		from
			dbo.cms_siteResourceTypes srt 
		where 
			srt.resourceType = 'ApplicationCreatedSection'

		select @sectionID = sectionID from cms_pageSections where siteID = @siteID and sectionName = 'MCAMSMemberDocuments'

		IF @sectionID is null
		begin

			select 
				@parentSectionID = ps.sectionID
			from 
				dbo.cms_pageSections ps
			where 
				ps.sectionName = 'root'
				and ps.parentSectionID is null
				and ps.siteID = @siteID

			exec dbo.cms_createPageSection @siteID, @resourceTypeID, null, null, null, @parentSectionID, 'MCAMSMemberDocuments', 'MCAMSMemberDocuments', 0, @sectionID output

		end

	end	 -- member documents

	-- appt tracker
	if @toolType = 'AppointmentTrackerAdmin' begin
		update 
			ai
		set 
			settingsXML.modify('replace value of (/settings/setting[@name=''showApptTracker'']/@value)[1] with ''true''')
		from 
			dbo.cms_applicationInstances as ai
			inner join dbo.sites as s on 
				s.siteID = ai.siteID
				and s.siteID = @siteID
		where 
			ai.applicationInstanceName = 'admin'

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select 
			tooltypeID, @siteID
		from 
			dbo.admin_toolTypes
		where 
			toolType = @toolType
		except
		select 
			tooltypeID, siteID 
		from 
			dbo.admin_siteToolRestrictions
	end

	-- reports
	if @toolType = 'Reports' begin
		insert into dbo.admin_siteToolRestrictions (toolTypeID, siteID)
		select 
			tooltypeid, @siteID 
		from 
			dbo.admin_toolTypes 
		where 
			toolCFC like 'Reports.custom.' + @sitecode + '.%'
			or (toolCFC like 'Reports.%' and left(toolCFC,15) <> 'Reports.custom.')
		except
		select 
			tooltypeID, siteID 
		from 
			dbo.admin_siteToolRestrictions
	end

	-- email blast
	if @toolType = 'EmailBlast' begin
		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select 
			tooltypeID, @siteID
		from 
			dbo.admin_toolTypes
		where 
			toolType = @toolType
		except
		select 
			tooltypeID, siteID 
		from 
			dbo.admin_siteToolRestrictions
	end

	set @tempList = stuff(@tempList, 1, charindex(',', @tempList+','), '')
	set @itemCounter = @itemCounter + 1
end

-- refresh
exec dbo.createadminsuite @siteid

-- @toolTypeList list is separated by ','
set @tempList = @toolTypeList
set @itemCounter = 1

while len(@tempList) > 0
begin

	set @toolType = left(@tempList, charindex(',', @tempList+',')-1)

	-- these need to be added after resources are there (createadminsuite)
	if @toolType = 'RelationshipAdmin' begin
		exec dbo.cms_createDefaultRelationshipCategories @siteID=@siteID, @contributingMemberID=461530
	end

	if @toolType = 'MemberHistoryAdmin' begin
		exec dbo.cms_createDefaultHistoryAdminCategories @siteID=@siteID, @contributingMemberID=461530
	end
	
	if @toolType = 'NotesAdmin' begin
		exec dbo.cms_createDefaultNotesCategories @siteid, 461530
	end

	if @toolType = 'AppointmentTrackerAdmin' begin
		exec dbo.cms_createDefaultAppointmentCategories @siteID, 461530
	end

	if @toolType = 'EmailBlast' begin
		exec dbo.cms_createDefaultEmailBlastCategories @siteID=@siteID
	end

	set @tempList = stuff(@tempList, 1, charindex(',', @tempList+','), '')
	set @itemCounter = @itemCounter + 1
end

RETURN 0
GO

ALTER PROCEDURE [dbo].[cms_createApplicationInstanceAdmin]
	@siteid int,
	@languageID int,
	@sectionID int,
	@isVisible bit,
	@pageName varchar(50),
	@pageTitle varchar(200),
	@pagedesc varchar(400),
	@zoneID int,
	@pageTemplateID int,
	@pageModeID int,
	@pgResourceTypeID int,
	@pgParentResourceID int = null,
	@allowReturnAfterLogin bit,
	@applicationInstanceName varchar(100),
	@applicationInstanceDesc varchar(200),
	@applicationInstanceID int OUTPUT,
	@siteResourceID int OUTPUT,
	@pageID int OUTPUT
AS

-- null OUTPUT vars
SELECT @applicationInstanceID = null, @siteResourceID = null, @pageID = null

DECLARE @appCreatedSectionResourceTypeID int, @applicationTypeID int, @rootSectionID int, @rc int
DECLARE @documentSectionName varchar(50)

select @appCreatedSectionResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedSection')
select @applicationTypeID = applicationTypeID from cms_applicationTypes where applicationTypeName = 'Admin'
	
BEGIN TRAN

exec @rc = dbo.cms_createApplicationInstance
		@siteid = @siteid,
		@languageID = @languageID,
		@sectionID = @sectionID,
		@applicationTypeID = @applicationTypeID,
		@isVisible = @isVisible,
		@pageName = @pageName,
		@pageTitle = @pageTitle,
		@pagedesc = @pagedesc,
		@zoneID = @zoneID,
		@pageTemplateID = @pageTemplateID,
		@pageModeID = @pageModeID,
		@pgResourceTypeID = @pgResourceTypeID,
		@pgParentResourceID = @pgParentResourceID,
		@allowReturnAfterLogin = @allowReturnAfterLogin,
		@applicationInstanceName = @applicationInstanceName,
		@applicationInstanceDesc = @applicationInstanceDesc,
		@applicationInstanceID = @applicationInstanceID OUTPUT,
		@siteResourceID = @siteResourceID OUTPUT,
		@pageID = @pageID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

select @documentSectionName = 'MemberDocument'

exec @rc = dbo.cms_createPageSection
		@siteID = @siteID, 
		@sectionResourceTypeID = @appCreatedSectionResourceTypeID, 
		@ovTemplateID = NULL,
		@ovTemplateIDMobile=NULL,
		@ovModeID = NULL, 
		@parentSectionID = @sectionID, 
		@sectionName = @documentSectionName, 
		@sectionCode = @documentSectionName,
		@inheritPlacements = 1,
		@sectionID = @rootSectionID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- update parentSiteResourceID of section
UPDATE sr
SET sr.parentSiteResourceID = @siteResourceID
FROM cms_pageSections s
INNER JOIN cms_siteResources sr on s.siteResourceID = sr.siteResourceID
	AND s.sectionID = @rootSectionID
	IF @@ERROR <> 0 GOTO on_error

-- update settings xml
UPDATE dbo.cms_applicationInstances
SET settingsXML = '<settings><setting name="memberDocumentSectionID" value="' + cast(@rootSectionID as varchar(8)) + + '" /><setting name="showMemberDocuments" value="false" /><setting name="showNotes" value="false" /><setting name="showRelationships" value="false" /><setting name="showMemberHistory" value="false" /><setting name="showSubscriptions" value="false" /><setting name="showReferrals" value="false" /><setting name="showApptTracker" value="false" /><setting name="showMemberPhotosInSearchResults" value="true" /><setting name="numPerPageInSearchResults" value="25" /></settings>'
WHERE applicationInstanceID = @applicationInstanceID
	IF @@ERROR <> 0 GOTO on_error

-- create the suite of tools
EXEC @rc = dbo.createAdminSuite @siteID
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- create admin member lookup fieldset
declare @memberAdminSRID int, @fieldSetID int, @fieldID int, @useID int
select @memberAdminSRID = dbo.fn_getSiteResourceIDForResourceType('MemberAdmin',@siteID)
EXEC @rc = dbo.ams_createMemberFieldSet @siteID=@siteID, @fieldsetName='Member Admin Search Form', @nameformat='LSXPFM', @showHelp=0, @fieldsetID=@fieldSetID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldSetID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_firstname', @fieldLabel='First Name', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_lastname', @fieldLabel='Last Name', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_company', @fieldLabel='Company', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_membernumber', @fieldLabel='Member Number', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = ams_createMemberFieldUsage @siteResourceID=@memberAdminSRID, @fieldsetID=@fieldSetID, @area='search', @createSiteResourceID=0, @useID=@useID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @useID = 0 GOTO on_error

EXEC @rc = dbo.ams_createMemberFieldSet @siteID=@siteID, @fieldsetName='Member Admin Search Results', @nameformat='LSXPFM', @showHelp=0, @fieldsetID=@fieldSetID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldSetID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_prefix', @fieldLabel='Prefix', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_middlename', @fieldLabel='Middle Name', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_suffix', @fieldLabel='Suffix', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = ams_createMemberFieldUsage @siteResourceID=@memberAdminSRID, @fieldsetID=@fieldSetID, @area='results', @createSiteResourceID=0, @useID=@useID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @useID = 0 GOTO on_error

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

ALTER PROC [dbo].[createSite]
	@orgID int,
	@sitecode varchar(10),
	@siteName varchar(60),
	@mainNetworkID int,
	@isLoginNetwork bit,
	@isMasterSite bit,
	@defaultLanguageID int,
	@defaultTimeZoneID int,
	@defaultCurrencyTypeID int,
	@showCurrencyType bit,
	@hasSSL bit,
	@isSSL bit,
	@allowGuestAccounts bit,
	@forceLoginPage bit,
	@useRemoteLogin bit,
	@affiliationRequired bit,
	@providesFreeFastCase bit,
	@enforceSiteAgreement bit,
	@allowMemberUpdates bit,
	@immediateMemberUpdates bit,
	@emailMemberUpdates varchar(200),
	@defaultPostalState varchar(10),
	@joinURL varchar(100),
	@pdfPassword varchar(30),
	@alternateGuestAccountCreationLink varchar(400),
	@alternateGuestAccountPopup bit,
	@alternateForgotPasswordLink varchar(400),
	@mainhostname varchar(80),
	@norightsContent varchar(max),
	@norightsNotLoggedInContent varchar(max),
	@inactiveUserContent varchar(max),
	@siteagreementContent varchar(max),
	@welcomeMessageContent varchar(max),
	@firstTimeLoginContent varchar(max),
	@siteID int OUTPUT

AS

DECLARE @rc int, @templateID int, @modeID int, @sectionID int, 
	@sectionResourceTypeID int, @siteAdminRoleID int, @superAdminRoleID int, 
	@siteAdminGroupID int, @superAdminGroupID int, @trashID int

BEGIN TRAN
	-- check for existing sitecode
	SELECT @siteID = null
	SELECT @siteID = siteID FROM dbo.sites where sitecode = @sitecode

	-- if not there, add it
	IF @siteID is not null
		GOTO on_error

	-- insert sites
	INSERT INTO dbo.sites (orgID, sitecode, siteName, defaultLanguageID, defaultTimeZoneId, defaultCurrencyTypeID,
		hasSSL, allowGuestAccounts, forceLoginPage, useRemoteLogin, affiliationRequired, 
		providesFreeFastCase, allowMemberUpdates, enforceSiteAgreement, immediateMemberUpdates, emailMemberUpdates, 
		defaultPostalState, joinURL, pdfPassword, alternateGuestAccountCreationLink, alternateGuestAccountPopup, 
		alternateForgotPasswordLink, showCurrencyType,isSSL, enableMobile, enableDeviceDetection)
	VALUES (@orgID, @sitecode, @siteName, @defaultLanguageID, @defaultTimeZoneId, @defaultCurrencyTypeID, 
		@hasSSL, @allowGuestAccounts, @forceLoginPage, @useRemoteLogin, @affiliationRequired, 
		@providesFreeFastCase, @allowMemberUpdates, @enforceSiteAgreement, @immediateMemberUpdates, @emailMemberUpdates, 
		@defaultPostalState, @joinURL, @pdfPassword, @alternateGuestAccountCreationLink, @alternateGuestAccountPopup, 
		@alternateForgotPasswordLink, @showCurrencyType, @isSSL, 0, 0)
		IF @@ERROR <> 0 GOTO on_error
		SELECT @siteID = SCOPE_IDENTITY()

	-- createSiteLanguage		
	EXEC @rc = dbo.createSiteLanguage @siteID=@siteID, @languageID=@defaultLanguageID		
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- get resourceType for site
	declare @siteResourceTypeID int
	select @siteResourceTypeID = dbo.fn_getResourceTypeID('Site')

	-- get active resource status
	declare @siteResourceStatusID int
	select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

	-- create a resourceID for the site
	DECLARE @siteResourceID int	
	exec dbo.cms_createSiteResource
		@resourceTypeID = @siteResourceTypeID,
		@siteResourceStatusID = @siteResourceStatusID,
		@siteID = @siteid,
		@isVisible = 1,
		@parentSiteResourceID = null,
		@siteResourceID = @siteResourceID OUTPUT
		IF @@ERROR <> 0 OR @siteResourceID = 0 GOTO on_error
	
	-- update site with new resource
	UPDATE dbo.sites
	SET siteResourceID = @siteResourceID
	WHERE siteID = @siteID
		IF @@ERROR <> 0 GOTO on_error
		
	-- roles
	select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')
	select @siteAdminRoleID = dbo.fn_getResourceRoleID('Site Administrator')
	select @siteAdminGroupID = groupID 
		from dbo.ams_groups 
		where groupCode = 'SiteAdmins' 
		and orgID = @orgID
	select @superAdminGroupID = groupID 
		from dbo.ams_groups 
		where groupCode = 'SuperAdmins' 
		and orgID = 1

	-- give siteAdmin Role to siteAdmin Group
	EXEC @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteID,
		@siteResourceID=@siteResourceID,
		@include=1,
		@functionID=null,
		@roleID=@siteAdminRoleID,
		@groupID=@siteAdminGroupID,
		@memberID=null,
		@inheritedRightsResourceID=null,
		@inheritedRightsFunctionID=null,
		@resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- give superAdmin Role to superAdmin Group
	EXEC @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteID,
		@siteResourceID=@siteResourceID,
		@include=1,
		@functionID=null,
		@roleID=@superAdminRoleID,
		@groupID=@superAdminGroupID,
		@memberID=null,
		@inheritedRightsResourceID=null,
		@inheritedRightsFunctionID=null,
		@resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- add default hostname
	DECLARE @hostnameID int	
	EXEC @rc = dbo.createSiteHostName @siteID=@siteID, @hostname=@mainhostname, @useRedirect=null, @hostnameID=@hostnameID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- add default template
	DECLARE @templateTypeID int
	SELECT @templateTypeID = dbo.fn_getTemplateTypeID('Page')
	EXEC @rc = dbo.cms_CreatePageTemplate @siteid=@siteID, @templateTypeID=@templateTypeID, @templateName='Default', @templateDesc='Default site template', @templateFileName='Default', @templateID=@templateID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @templateID = 0 GOTO on_error2
	
	-- add default page sections
	SELECT @modeID = dbo.fn_getModeID('Normal')
	SELECT @sectionResourceTypeID = dbo.fn_getResourceTypeID('SystemCreatedSection')
	EXEC @rc = dbo.cms_createPageSection @siteID=@siteID, @sectionResourceTypeID=@sectionResourceTypeID, @ovTemplateID=@templateID, @ovTemplateIDMobile=null, @ovModeID=@modeID, @parentSectionID=null, @sectionName='Root', @sectionCode='Root', @inheritPlacements=1, @sectionID=@sectionID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createPageSection @siteID=@siteID, @sectionResourceTypeID=@sectionResourceTypeID, @ovTemplateID=null, @ovTemplateIDMobile=null, @ovModeID=null, @parentSectionID=@sectionID, @sectionName='MCAMSMemberDocuments', @sectionCode='MCAMSMemberDocuments', @inheritPlacements=0, @sectionID=@sectionID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- default fieldsets
	EXEC @rc = dbo.cms_createDefaultFieldsets @siteid=@siteID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add default pages
	EXEC @rc = dbo.cms_createDefaultPages @siteid=@siteID, @sectionid=@sectionID, @languageID=@defaultLanguageID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add default History (notes) Categories
	EXEC @rc = dbo.cms_createDefaultHistoryCategories @siteid=@siteID, @contributingMemberID=461530
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add to network
	EXEC @rc = dbo.createNetworkSite @networkID=@mainNetworkID, @siteID=@siteID, @isLoginNetwork=@isLoginNetwork, @isMasterSite=@isMasterSite
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add default FULL Frequency
	insert into dbo.sub_frequencies(frequencyName, frequency, frequencyShortName, uid, 
		rateRequired, hasInstallments, monthlyInterval, isSystemRate, siteID, status)
	values('Full', 1, 'F', newid(), 1, 1, 1, 1, @siteID, 'A')	
		IF @@ERROR <> 0 GOTO on_error2

	-- add content objects
	DECLARE @sysCreatedContentResourceTypeID int, @activesiteResourceStatusID int, @newContentid int, @newresourceid int
	select @sysCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('SystemCreatedContent')
	select @activesiteResourceStatusID = dbo.fn_getResourceStatusId('Active')
	EXEC @rc = dbo.cms_createContentObject 
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'NoRights',
		@contentDesc = null,
		@rawContent = @norightsContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET noRightsContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject 
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'NoRightsNotLoggedIn',
		@contentDesc = null,
		@rawContent = @norightsNotLoggedInContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET noRightsNotLoggedInContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'InactiveUser',
		@contentDesc = null,
		@rawContent = @inactiveUserContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET InactiveUserContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'SiteAgreement',
		@contentDesc = null,
		@rawContent = @siteagreementContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET SiteAgreementContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'Welcome Message',
		@contentDesc = null,
		@rawContent = @welcomeMessageContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET welcomeMessageContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'First Time Login Message',
		@contentDesc = null,
		@rawContent = @firstTimeLoginContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET firstTimeLoginContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2

	-- link up superusers to all new sites
	INSERT INTO dbo.ams_memberNetworkProfiles (memberID, profileID, [status], dateCreated, siteID)
	SELECT distinct mnp.memberID, mnp.profileID, 'A', getdate(), @siteID
	FROM dbo.ams_memberNetworkProfiles AS mnp 
	INNER JOIN dbo.ams_networkProfiles AS np ON mnp.profileID = np.profileID
	WHERE mnp.status = 'A'
	AND np.networkID = dbo.fn_getNetworkID('MemberCentral Super Administrators')
	AND np.status = 'A'
	AND mnp.siteID <> @siteID
		IF @@ERROR <> 0 GOTO on_error2

	EXEC @rc = dbo.cms_populateSiteResourceRightsCache @siteID=@siteID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @siteID = 0
	RETURN -1

on_error2:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1

GO


declare @toolTypeID int
select @toolTypeID = toolTypeID
from admin_toolTypes
where toolType = 'MemberHistoryAdmin'

delete
from dbo.admin_siteToolRestrictions 
where toolTypeID = @toolTypeID

GO


delete fdn
from admin_navigation n
inner join dbo.admin_functionsDeterminingNav fdn
	on fdn.navigationID = n.navigationID
	and n.navName in ('Member Notes','Note Types')
inner join dbo.cms_siteResourceTypeFunctions srtf
	on srtf.resourceTypeFunctionID =  fdn.resourceTypeFunctionID 
inner join cms_siteResourceFunctions srf
	on srf.functionID = srtf.functionID
inner join cms_siteResourceTypes srt
	on srt.resourceTypeID = srtf.resourceTypeID

GO


declare @toolTypeID int
declare @resourceTypeID int, @resourceTypeName varchar(50), @functionID int, @functionName varchar(50), @navigationID int, @navName varchar(100)
DECLARE @rc int, @siteAdminRoleID int, @superAdminRoleID int, @ResourceTypeFunctionID int

set @resourceTypeName = 'MemberHistoryAdmin'
set @functionName = 'viewNotes'
set @navName = 'Member Notes'

select @toolTypeID = dbo.fn_getAdminToolTypeID('MemberHistoryAdmin')
select @navigationID = n.navigationID from admin_navigation n where navName = @navName
select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)
SELECT @functionID = dbo.fn_getResourceFunctionID(@functionName,@resourceTypeID)
select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);

exec dbo.createAdminFunctionsDeterminingNav
	@resourceTypeFunctionID=@ResourceTypeFunctionID,
	@toolTypeID=@toolTypeID,
	@navigationID=@navigationID

GO

declare @toolTypeID int
declare @resourceTypeID int, @resourceTypeName varchar(50), @functionID int, @functionName varchar(50), @navigationID int, @navName varchar(100)
DECLARE @rc int, @siteAdminRoleID int, @superAdminRoleID int, @ResourceTypeFunctionID int

set @resourceTypeName = 'MemberHistoryAdmin'
set @functionName = 'editNotes'
set @navName = 'Note Types'

select @toolTypeID = dbo.fn_getAdminToolTypeID('MemberHistoryAdmin')
select @navigationID = n.navigationID from admin_navigation n where navName = @navName
select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)
SELECT @functionID = dbo.fn_getResourceFunctionID(@functionName,@resourceTypeID)
select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);

exec dbo.createAdminFunctionsDeterminingNav
	@resourceTypeFunctionID=@ResourceTypeFunctionID,
	@toolTypeID=@toolTypeID,
	@navigationID=@navigationID

GO

delete fdn
from admin_navigation n
inner join dbo.admin_functionsDeterminingNav fdn
	on fdn.navigationID = n.navigationID
	and n.navName in ('Note Types')
inner join dbo.cms_siteResourceTypeFunctions srtf
	on srtf.resourceTypeFunctionID =  fdn.resourceTypeFunctionID 
inner join cms_siteResourceFunctions srf
	on srf.functionID = srtf.functionID
inner join cms_siteResourceTypes srt
	on srt.resourceTypeID = srtf.resourceTypeID

GO



declare @toolTypeID int
declare @resourceTypeID int, @resourceTypeName varchar(50), @functionID int, @functionName varchar(50), @navigationID int, @navName varchar(100)
DECLARE @rc int, @siteAdminRoleID int, @superAdminRoleID int, @ResourceTypeFunctionID int

set @resourceTypeName = 'MemberHistoryAdmin'
set @functionName = 'editNotes'
set @navName = 'Note Types'

select @toolTypeID = dbo.fn_getAdminToolTypeID('HistoryAdmin')
select @navigationID = n.navigationID from admin_navigation n where navName = @navName
select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)
SELECT @functionID = dbo.fn_getResourceFunctionID(@functionName,@resourceTypeID)
select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);

exec dbo.createAdminFunctionsDeterminingNav
	@resourceTypeFunctionID=@ResourceTypeFunctionID,
	@toolTypeID=@toolTypeID,
	@navigationID=@navigationID

GO

ALTER PROC [dbo].[enableSiteFeature]
	@siteID int,
	@toolTypeList varchar(1000)
AS

declare 
	@toolType varchar (100), 
	@tempList varchar(1000), 
	@itemCounter int,
	@sitecode varchar(10),
	@resourceTypeID int,
	@sectionID int,
	@parentSectionID int

-- @toolTypeList list is separated by ','
set @tempList = @toolTypeList
set @itemCounter = 1

select @sitecode = sitecode from sites where siteID = @siteID

while len(@tempList) > 0
begin
	set @toolType = left(@tempList, charindex(',', @tempList+',')-1)

	-- subscriptions
	if @toolType = 'SubscriptionAdmin' begin
		update 
			ai
		set 
			settingsXML.modify('replace value of (/settings/setting[@name=''showSubscriptions'']/@value)[1] with ''true''')
		from 
			dbo.cms_applicationInstances as ai
			inner join dbo.sites as s on 
				s.siteID = ai.siteID
				and s.siteID = @siteID
		where 
			ai.applicationInstanceName = 'admin'

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select 
			tooltypeID, @siteID
		from 
			dbo.admin_toolTypes
		where 
			toolType = @toolType
		except
		select 
			tooltypeID, siteID 
		from 
			dbo.admin_siteToolRestrictions

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select 
			tooltypeID, @siteID
		from 
			dbo.admin_toolTypes
		where 
			toolType = 'SubRenewalAdmin'
		except
		select 
			tooltypeID, siteID 
		from 
			dbo.admin_siteToolRestrictions
	end

	-- referrals
	if @toolType = 'ReferralsAdmin' begin
		update 
			ai
		set 
			settingsXML.modify('replace value of (/settings/setting[@name=''showReferrals'']/@value)[1] with ''true''')
		from 
			dbo.cms_applicationInstances as ai
			inner join dbo.sites as s on 
				s.siteID = ai.siteID
				and s.siteID = @siteID
		where 
			ai.applicationInstanceName = 'admin'

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select 
			tooltypeID, @siteID
		from 
			dbo.admin_toolTypes
		where 
			toolType = @toolType
		except
		select 
			tooltypeID, siteID 
		from 
			dbo.admin_siteToolRestrictions
	end

	-- member history
	if @toolType = 'MemberHistoryAdmin' begin
		update 
			ai
		set 
			settingsXML.modify('replace value of (/settings/setting[@name=''showMemberHistory'']/@value)[1] with ''true''')
		from 
			dbo.cms_applicationInstances as ai
			inner join dbo.sites as s on 
				s.siteID = ai.siteID
				and s.siteID = @siteID
		where 
			ai.applicationInstanceName = 'admin'
	end

	-- relationships
	if @toolType = 'RelationshipAdmin' begin
		update 
			ai
		set 
			settingsXML.modify('replace value of (/settings/setting[@name=''showRelationships'']/@value)[1] with ''true''')
		from 
			dbo.cms_applicationInstances as ai
			inner join dbo.sites as s on 
				s.siteID = ai.siteID
				and s.siteID = @siteID
		where 
			ai.applicationInstanceName = 'admin'

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select 
			tooltypeID, @siteID
		from 
			dbo.admin_toolTypes
		where 
			toolType = @toolType
		except
		select 
			tooltypeID, siteID 
		from 
			dbo.admin_siteToolRestrictions
	end

	-- tasks
	if @toolType = 'NotesAdmin' begin
		update 
			ai
		set 
			settingsXML.modify('replace value of (/settings/setting[@name=''showNotes'']/@value)[1] with ''true''')
		from 
			dbo.cms_applicationInstances as ai
			inner join dbo.sites as s on 
				s.siteID = ai.siteID
				and s.siteID = @siteID
		where 
			ai.applicationInstanceName = 'admin'

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select 
			tooltypeID, @siteID
		from 
			dbo.admin_toolTypes
		where 
			toolType = @toolType
		except
		select 
			tooltypeID, siteID 
		from 
			dbo.admin_siteToolRestrictions
	end

	-- member documents
	if @toolType = 'MemberDocs' begin
		update 
			ai
		set 
			settingsXML.modify('replace value of (/settings/setting[@name=''showMemberDocuments'']/@value)[1] with ''true''')
		from 
			dbo.cms_applicationInstances as ai
			inner join dbo.sites as s on 
				s.siteID = ai.siteID
				and s.siteID = @siteID
		where 
			ai.applicationInstanceName = 'admin'

		select 
			@resourceTypeID = srt.resourceTypeID
		from
			dbo.cms_siteResourceTypes srt 
		where 
			srt.resourceType = 'ApplicationCreatedSection'

		select @sectionID = sectionID from cms_pageSections where siteID = @siteID and sectionName = 'MCAMSMemberDocuments'

		IF @sectionID is null
		begin

			select 
				@parentSectionID = ps.sectionID
			from 
				dbo.cms_pageSections ps
			where 
				ps.sectionName = 'root'
				and ps.parentSectionID is null
				and ps.siteID = @siteID

			exec dbo.cms_createPageSection @siteID, @resourceTypeID, null, null, null, @parentSectionID, 'MCAMSMemberDocuments', 'MCAMSMemberDocuments', 0, @sectionID output

		end

	end	 -- member documents

	-- appt tracker
	if @toolType = 'AppointmentTrackerAdmin' begin
		update 
			ai
		set 
			settingsXML.modify('replace value of (/settings/setting[@name=''showApptTracker'']/@value)[1] with ''true''')
		from 
			dbo.cms_applicationInstances as ai
			inner join dbo.sites as s on 
				s.siteID = ai.siteID
				and s.siteID = @siteID
		where 
			ai.applicationInstanceName = 'admin'

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select 
			tooltypeID, @siteID
		from 
			dbo.admin_toolTypes
		where 
			toolType = @toolType
		except
		select 
			tooltypeID, siteID 
		from 
			dbo.admin_siteToolRestrictions
	end

	-- reports
	if @toolType = 'Reports' begin
		insert into dbo.admin_siteToolRestrictions (toolTypeID, siteID)
		select 
			tooltypeid, @siteID 
		from 
			dbo.admin_toolTypes 
		where 
			toolCFC like 'Reports.custom.' + @sitecode + '.%'
			or (toolCFC like 'Reports.%' and left(toolCFC,15) <> 'Reports.custom.')
		except
		select 
			tooltypeID, siteID 
		from 
			dbo.admin_siteToolRestrictions
	end

	-- email blast
	if @toolType = 'EmailBlast' begin
		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select 
			tooltypeID, @siteID
		from 
			dbo.admin_toolTypes
		where 
			toolType = @toolType
		except
		select 
			tooltypeID, siteID 
		from 
			dbo.admin_siteToolRestrictions
	end

	set @tempList = stuff(@tempList, 1, charindex(',', @tempList+','), '')
	set @itemCounter = @itemCounter + 1
end

-- refresh
exec dbo.createadminsuite @siteid

-- @toolTypeList list is separated by ','
set @tempList = @toolTypeList
set @itemCounter = 1

while len(@tempList) > 0
begin

	set @toolType = left(@tempList, charindex(',', @tempList+',')-1)

	-- these need to be added after resources are there (createadminsuite)
	if @toolType = 'RelationshipAdmin' begin
		exec dbo.cms_createDefaultRelationshipCategories @siteID=@siteID, @contributingMemberID=461530
	end

	if @toolType = 'MemberHistoryAdmin' begin
		exec dbo.cms_createDefaultHistoryAdminCategories @siteID=@siteID, @contributingMemberID=461530
	end
	
	if @toolType = 'NotesAdmin' begin
		exec dbo.cms_createDefaultNotesCategories @siteid, 461530
	end

	if @toolType = 'AppointmentTrackerAdmin' begin
		exec dbo.cms_createDefaultAppointmentCategories @siteID, 461530
	end

	if @toolType = 'EmailBlast' begin
		exec dbo.cms_createDefaultEmailBlastCategories @siteID=@siteID
	end

	set @tempList = stuff(@tempList, 1, charindex(',', @tempList+','), '')
	set @itemCounter = @itemCounter + 1
end

RETURN 0
GO


