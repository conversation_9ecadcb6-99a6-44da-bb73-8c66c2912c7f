ALTER PROC dbo.emailTracking_createDomainID
@domain VARCHAR(255),
@domainID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- Create a table variable to store the OUTPUT result
	DECLARE @domainIDVar TABLE (domainID INT);

	BEGIN TRAN;
		<PERSON>R<PERSON> INTO dbo.recipientDomains WITH (HOLDLOCK) AS Target
		USING (SELECT @domain AS domain) AS Source
			ON Target.domain = Source.domain 
		WHEN MATCHED THEN
			UPDATE SET @domainID = Target.domainID
		WHEN NOT MATCHED THEN
			INSERT (domain) VALUES (source.domain)
			OUTPUT INSERTED.domainID INTO @domainIDVar;

		SET @domainID = (SELECT domainID FROM @domainIDVar)
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO
