USE [dataTransfer]
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[tmp_zipCodes]') AND type in (N'U'))
DROP TABLE [dbo].tmp_zipCodes
GO

CREATE TABLE [dbo].tmp_zipCodes(
	[zipcode] [varchar](7) NOT NULL,
	[ZIPType] [char](1) NULL,
	[cityName] [varchar](64) NULL,
	[cityType] [char](1) NULL,
	[countyName] [varchar](64) NULL,
	[countyFIPS] [char](5) NULL,
	[stateName] [varchar](40) NULL,
	[stateAbbr] [char](2) NULL,
	[stateFIPS] [char](2) NULL,
	[MSACode] [char](4) NULL,
	[areaCode] [varchar](16) NULL,
	[timeZone] [varchar](16) NULL,
	[UTC] [decimal](3, 1) NULL,
	[DST] [char](1) NULL,
	[latitude] [decimal](9, 6) NOT NULL,
	[longitude] [decimal](9, 6) NOT NULL
) ON [PRIMARY]
GO
declare @qry varchar(1000)
SELECT @qry = 'BULK INSERT tmp_zipCodes FROM "e:\temp\5-digit Commercial.txt" WITH (FIELDTERMINATOR = '''+ char(9) + ''', FIRSTROW = 2);'
EXEC(@qry)
GO

insert into membercentral.dbo.ams_zipCodes (zipcode, ZIPType, cityName, cityType, countyName, countyFIPS, stateID, stateFIPS, countryID, MSACode, areaCode, timeZone, UTC, DST, latitude, longitude)
select tmp.zipcode, tmp.ZIPType, tmp.cityName, tmp.cityType, tmp.countyName, tmp.countyFIPS, s.stateID, tmp.stateFIPS, 1, tmp.MSACode, tmp.areaCode, tmp.timeZone, tmp.UTC, tmp.DST, tmp.latitude, tmp.longitude
from dataTransfer.dbo.tmp_zipCodes as tmp
left outer join membercentral.dbo.ams_states as s on s.name = case
	when tmp.StateName = 'Armed Forces - Europe/Africa/Canada' then 'Armed Forces Europe'
	when tmp.StateName = 'Armed Forces - Americas' then 'Armed Forces America'
	when tmp.StateName = 'Armed Forces - Pacific' then 'Armed Forces Pacific'
	else tmp.StateName end
	and s.countryID = 1
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[tmp_zipCodes]') AND type in (N'U'))
DROP TABLE [dbo].tmp_zipCodes
GO
