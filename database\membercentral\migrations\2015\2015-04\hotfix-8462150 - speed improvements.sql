use membercentral;
GO
ALTER PROCEDURE [dbo].[cms_deleteSiteResourceRight] 
	@siteID int, 
	@siteResourceID int,
	@siteResourceRightID int
AS
BEGIN
	
	DECLARE @isValidUniversalRoleAssignment bit;

	DECLARE @currentSiteResourceAutoID int, @currentSiteResourceID int;

	IF OBJECT_ID('tempdb..#siteResourcesToProcess') IS NOT NULL
		DROP TABLE #siteResourcesToProcess
	CREATE TABLE #siteResourcesToProcess (autoid int IDENTITY(1,1), siteResourceID int);



	set @isValidUniversalRoleAssignment = 0

	if (EXISTS (
		SELECT	s.siteResourceID
		FROM sites s
			inner join cms_siteResourceRights srr
				on srr.resourceRightsID = @siteResourceRightID
				and s.siteResourceID = srr.resourceID
				and s.siteResourceID = @siteResourceID
				and s.siteID = @siteID
			inner join organizations o on s.orgID = o.orgID
			INNER JOIN ams_groups g
				on g.orgID = o.orgID
				and g.groupID = srr.groupID
		) and EXISTS (
			select srroles.roleID
			from cms_siteResourceRoles as srroles
			inner join cms_siteResourceRights srr
				on srr.resourceRightsID = @siteResourceRightID
				and srr.roleID = srroles.roleID
			INNER join cms_siteResourceRoleTypes as srrt
				on srroles.roleTypeID = srrt.roleTypeID
				and srrt.roleTypeName = 'UniversalRole'
		))
		 set @isValidUniversalRoleAssignment = 1



	if (@isValidUniversalRoleAssignment = 0)
		BEGIN

			/* step1: get SRID and all SRIDs inheriting from it into temp table */


			IF exists (select resourceRightsID from dbo.cms_siteResourceRights where resourceID = @siteResourceID and inheritedRightsResourceID is not null) BEGIN
			    ; WITH allSRIDs AS (
				    -- SRID in question
				    select resourceID, functionid, inheritedRightsResourceID, inheritedRightsFunctionID
				    from dbo.cms_siteResourceRights
				    where resourceID = @siteResourceID
					    union all
				    -- inheritance
				    select srr.resourceID, srr.functionid, srr.inheritedRightsResourceID, srr.inheritedRightsFunctionID
				    from dbo.cms_siteResourceRights as srr
				    inner join allSRIDs as rr on rr.resourceID = srr.inheritedRightsResourceID
					    and rr.functionID = srr.inheritedRightsFunctionID
				    inner join dbo.cms_siteResources as sr on sr.siteResourceID = srr.resourceID and sr.siteID = @siteID
			    )
			    INSERT INTO #siteResourcesToProcess (siteResourceID)
			    select distinct resourceID
			    from allSRIDs
			    option(recompile)
			END ELSE BEGIN
				INSERT INTO #siteResourcesToProcess (siteResourceID) values (@siteResourceID)
			END

			/* step2: delete from cache where SRID is in temp table */
			delete from srrc
			from dbo.cms_siteResourceRightsCache as srrc
			inner join #siteResourcesToProcess as srid
				on srid.siteResourceID = srrc.resourceID
				and srrc.universalRoleResourceRightsID is null

			/* step3: delete from rights */
			delete from dbo.cms_siteResourceRights
			where resourceRightsID = @siteResourceRightID
			and resourceID = @siteResourceID

			/* step4: rebuild rights cache for all SRIDs in temp table */
			-- rebuild rights cache and add anything new
			-- similar code exists in three places -- here, cms_createSiteResourceRight, and cms_populateSiteResourceRightsCache
			;with rightsRoleFunc as (
				-- get rights for functions
				SELECT srr.resourceID, srr.functionID, srr.groupID, srr.memberID, srr.include, srr.inheritedRightsResourceID, srr.inheritedRightsFunctionID
				from dbo.cms_siteResourceRights as srr
				inner join dbo.cms_siteResources as sr on sr.siteResourceID = srr.resourceID and sr.siteID = @siteID
				inner join #siteResourcesToProcess as allSRID on allSRID.siteResourceID = srr.resourceID
				where srr.roleID is null 
				and srr.functionID is not null
					union all
				-- get rights for roles		
				SELECT srr.resourceID, srtf.functionID, srr.groupID, srr.memberID, srr.include, srr.inheritedRightsResourceID, srr.inheritedRightsFunctionID
				from dbo.cms_siteResourceRights as srr
				inner join dbo.cms_siteResourceRoles as srroles on srroles.roleID = srr.RoleID
				inner join dbo.cms_siteResourceRoleTypes as srrt on srroles.roleTypeID = srrt.roleTypeID and srrt.roleTypeName = 'InstanceRole'
				inner join dbo.cms_siteResourceRoleFunctions as srrf on srr.roleID = srrf.roleID
				INNER JOIN cms_siteResourceTypeFunctions srtf on srrf.resourceTypeFunctionID = srtf.resourceTypeFunctionID
				inner join dbo.cms_siteResources as sr on sr.siteResourceID = srr.resourceID and sr.siteID = @siteID
				inner join #siteResourcesToProcess as allSRID on allSRID.siteResourceID = srr.resourceID
			), resourceRights as (
				select resourceID as rootResourceID, functionID as rootFunctionID, resourceID, functionID, groupID, memberID, include, inheritedRightsResourceID, inheritedRightsFunctionID
				from rightsRoleFunc
					union all
				-- inheritance
				select rr.RootResourceID, rr.rootFunctionID, srr.resourceID, srr.functionID, srr.groupID, srr.memberID, srr.include, srr.inheritedRightsResourceID, srr.inheritedRightsFunctionID
				from dbo.cms_siteResourceRights as srr
				inner join resourceRights as rr on rr.inheritedRightsResourceID = srr.resourceID
					and rr.inheritedRightsFunctionID = srr.functionID
				inner join dbo.cms_siteResources as sr on sr.siteResourceID = srr.resourceID and sr.siteID = @siteID
			)
			insert into dbo.cms_siteResourceRightsCache (resourceID, functionID, groupID, memberID, [include])
			select distinct rr.rootresourceID, rr.rootfunctionID, rr.groupID, rr.memberID, rr.include
			from resourceRights as rr
			left outer join dbo.cms_siteResourceRightsCache as srrc
				on srrc.resourceID = rr.rootresourceID
				and isnull(srrc.functionID,0) = isnull(rr.rootfunctionID,0)
				and isnull(srrc.groupID,0) = isnull(rr.groupID,0)
				and isnull(srrc.memberID,0) = isnull(rr.memberID,0)
				and srrc.include = rr.include
			where inheritedRightsResourceID is null 
			and inheritedRightsFunctionID is null
			and srrc.cachedRightsID is null
			option(recompile)
		END
	ELSE
		BEGIN

			INSERT INTO #siteResourcesToProcess (siteResourceID)
			select distinct sr.siteresourceID
			from cms_siteResourceRightsCache srrc
				inner join cms_siteResources sr
					on srrc.universalRoleResourceRightsID = @siteResourceRightID
					and srrc.universalRoleResourceTypeID = sr.resourceTypeID
					and sr.siteID = @siteID


			/* step2: delete from cache where SRID is in temp table */
			delete from dbo.cms_siteResourceRightsCache
			where universalRoleResourceRightsID = @siteResourceRightID

			/* step3: delete from rights */
			delete from dbo.cms_siteResourceRights
			where resourceRightsID = @siteResourceRightID
			and resourceID = @siteResourceID

		END



	if exists(select siteResourceID from #siteResourcesToProcess)
	BEGIN

		CREATE INDEX IX_siteResourcesToProcess_SRID ON #siteResourcesToProcess (siteResourceID); 

		exec dbo.cache_perms_updateSiteResourceFunctionRightPrintsForSiteResourcesBulk
			@siteID = @siteID
	END


	IF OBJECT_ID('tempdb..#siteResourcesToProcess') IS NOT NULL
		DROP TABLE #siteResourcesToProcess


END
GO
use platformMail
GO
ALTER PROC [dbo].[email_markRecipientBatch]
@batchSize int,
@restrictToMessageID int,
@workerUUID uniqueIdentifier OUTPUT

AS

declare @batchStartDate datetime, @newStatusID int, @oldStatusID int
declare @tblMessages table(
	messageID int NOT NULL, 
	replyToEmail varchar(200) NOT NULL, 
	subject varchar(400) NOT NULL, 
	siteCode varchar(10) NOT NULL, 
	siteName varchar(60) NOT NULL,
	emailFrom varchar(403) NOT NULL,
	senderEmail varchar(200) NOT NULL, 
	messageContent varchar(max) NOT NULL);
declare @tblRecipients table(
	recipientID int NOT NULL, 
	messageID int NOT NULL, 
	memberID int NOT NULL,
	toName varchar(200) NOT NULL,
	toEmail varchar(200) NOT NULL);
    
set @workerUUID = newID()
set @batchStartDate = getdate()
select @newStatusID = statusID from dbo.email_statuses where statusCode = 'G'
select @oldStatusID = statusID from dbo.email_statuses where statusCode = 'Q'


if exists (
    select recipientID 
    from dbo.email_messageRecipientHistory 
    where emailStatusID = @oldStatusID 
    and batchID is null
    and messageID = isnull(@restrictToMessageID,messageID)
) BEGIN
    -- mark recipients
    update r 
    set batchID = @workerUUID,
	    batchStartDate = @batchStartDate,
	    emailStatusID = @newStatusID
    output	inserted.recipientID, 
		    inserted.messageID, 
		    inserted.memberID,
		    inserted.toName,
		    inserted.toEmail
    into @tblRecipients
    from dbo.email_messageRecipientHistory as r
    inner join (
	    select top (@batchSize) recipientID
	    from dbo.email_messageRecipientHistory
	    where emailStatusID = @oldStatusID
		    and batchID is null
		    and messageID = isnull(@restrictToMessageID,messageID)
	    order by dateLastUpdated
    ) as temp on temp.recipientID = r.recipientID
	    IF @@ERROR <> 0 GOTO on_error

    -- get messages
    insert into @tblMessages (messageID, replyToEmail, subject, siteCode, siteName, emailFrom, senderEmail, messageContent)
    select m.messageID, m.replyToEmail, m.subject, s.siteCode, s.siteName,
	    m.fromEmail + case when len(m.fromName) > 0 then ' (' + m.fromName + ')' else '' end as emailFrom,
	    m.senderEmail, 
	    replace(m.messagewrapper,'@@rawcontent@@',cv.rawContent) as messageContent
    from (select distinct messageID from @tblRecipients) as tmpM
    inner join dbo.email_messages as m on m.messageID = tmpM.messageID
    inner join membercentral.dbo.cms_contentVersions as cv on cv.contentVersionID = m.contentVersionID
    inner join membercentral.dbo.sites as s on s.siteID = m.siteID
END

select *
from @tblMessages
order by messageID

-- get message merge codes
select distinct m.messageID, reg.Text as fieldName
from @tblMessages as m
cross apply membercentral.dbo.fn_RegexMatches(m.messageContent,'(?<=\[\[)([^,\]]+)(?=,?([^\]]+)?\]\])') as reg
order by 1, 2

-- get recipients
select recipientID, messageID, memberID, 
    toEmail + case when len(toName) > 0 then ' (' + toName + ')' else '' end as emailTo
from @tblRecipients
    IF @@ERROR <> 0 GOTO on_error

-- get metadata
select r.recipientID, f.fieldID, f.fieldName, mf.messageid, mf.memberid, mf.fieldValue
from dbo.email_metadataFields as f
inner join dbo.email_messageMetadataFields as mf on mf.fieldID = f.fieldID
inner join @tblRecipients as r on r.messageID = mf.messageID and r.memberID = mf.memberID
where f.isMergeField = 1

-- normal exit
RETURN 0

-- error exit
on_error:
	RETURN -1
GO
