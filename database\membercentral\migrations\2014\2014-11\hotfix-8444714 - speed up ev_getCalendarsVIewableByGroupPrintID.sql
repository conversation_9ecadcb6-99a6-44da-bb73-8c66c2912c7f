use membercentral;
GO
ALTER PROCEDURE [dbo].[ev_getCalendarsViewableByGroupPrintID]
	@siteID int,
	@groupPrintID int = null
AS

declare @siteResourceStatusID int, @viewFunctionID int, @orgID int
select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')
select @viewFunctionID = dbo.fn_getResourceFunctionID('view', dbo.fn_getResourceTypeID('events'))
select @orgID = dbo.fn_getOrgIDFromSiteID(@siteID)

if @groupPrintID is null
	BEGIN
		select c.calendarID, sr.siteResourceID, ai.applicationInstanceName as calendarName
		from ev_calendars c
		inner join cms_applicationInstances ai
			on ai.applicationInstanceID = c.applicationInstanceID
			and ai.siteID = @siteID
		inner join cms_siteResources sr
			on sr.siteResourceID = ai.siteResourceID
			and sr.siteResourceStatusID = @siteResourceStatusID
		inner join cms_siteResourceRightsCache srrc
			on srrc.resourceID = sr.siteResourceID
			and srrc.functionID = @viewFunctionID
			and srrc.include = 1
		inner join ams_groups g
			on g.groupID = srrc.groupID
			and g.orgID = @orgID
			and g.groupCode = 'Public'
		option(recompile)
	END
else
	BEGIN
		select c.calendarID, sr.siteResourceID, ai.applicationInstanceName as calendarName
		from ev_calendars c
		inner join cms_applicationInstances ai
			on ai.applicationInstanceID = c.applicationInstanceID
			and ai.siteID = @siteID
		inner join cms_siteResources sr
			on sr.siteResourceID = ai.siteResourceID
			and sr.siteResourceStatusID = @siteResourceStatusID
		inner join dbo.cache_perms_siteResourceFunctionRightPrints srfrp
			on srfrp.siteResourceID = sr.siteResourceID
			and srfrp.functionID = @viewfunctionID
		inner join dbo.cache_perms_groupPrintsRightPrints gprp
			on gprp.rightPrintID = srfrp.rightPrintID
			and gprp.groupPrintID = @groupPrintID
		option(recompile)
	END


GO
