/*
In this files the following tasks are performed:

(1) alter table dbo.schedTask_member<PERSON>oinDates, add column renewalDateFieldName
(2) CREATE PROCEDURE dbo.job_initializeMemberRenewalDates
(3) Add NV to schedTask_memberJoinDates and schedTask_memberJoinDateSubTypes tables. Also initialize the renewal date by calling the new job_initializeMember<PERSON><PERSON>walDates PROC.
(4) ALTER PROCEDURE dbo.job_memberJoinDates

*/

use customApps
GO

-- (1) **************************************************************************************************
if not exists(	select * 
				from sys.columns 
				where Name = N'renewalDateFieldName' 
					and Object_ID = Object_ID(N'schedTask_memberJoinDates'))
begin
	alter table dbo.schedTask_member<PERSON>oinDates
		add renewalDateFieldName varchar(255) null
end
GO

-- (2) **************************************************************************************************

IF OBJECT_ID('job_initializeMemberRenewalDates') IS NOT NULL
	DROP PROCEDURE job_initializeMemberRenewalDates
GO

CREATE PROCEDURE dbo.job_initializeMemberRenewalDates
@udid int,
@error_code int OUTPUT

AS

set nocount on

declare 
	@siteCode varchar(10), @joinFieldName varchar(max), 
	@renewalDateFieldName varchar(max), @currColumnName varchar(max)
declare 
	@siteID int, @orgID int, @joinColumnID int, @rc int, @minSDID int,
	@columnvalueID int,@renewalColumnID int, @currMemberID int, 
	@currColumnValue varchar(max), @currColumnID int

select @error_code = 0

IF OBJECT_ID('tempdb..#subJoinDates') IS NOT NULL 
	DROP TABLE #subJoinDates

CREATE TABLE #subJoinDates (
	id int identity(1,1) PRIMARY KEY, 
	activeMemberID int, 
	updateDate dateTime,
	[action] varchar(1), 
	columnID int, 
	columnValueID int
)

select 
	@siteCode = sitecode, 
	@joinFieldName = joinDateFieldName, 
	@renewalDateFieldName = renewalDateFieldName
from 
	dbo.schedTask_memberJoinDates
where 
	udid = @udid

select 
	@siteID = siteID, @orgID = orgID
from 
	membercentral.dbo.sites
where 
	siteCode = @siteCode

-- get columnIDs
select @joinColumnID=columnID
	from membercentral.dbo.ams_memberDataColumns as mdc
	where orgID = @orgID
	and columnName = @joinFieldName

select @renewalColumnID=columnID
	from membercentral.dbo.ams_memberDataColumns as mdc
	where orgID = @orgID
	and columnName = @renewalDateFieldName

-- get people who have been moved to active or accepted, have a join date, and renewal date is null or before join date
IF @renewalColumnID is not null
BEGIN
	insert into #subJoinDates (activeMemberID, updateDate, [action], columnID)
	select m.activeMemberID, max(psh.updateDate), 'W' as [action], @renewalColumnID as columnID
	from membercentral.dbo.sub_paymentStatusHistory as psh
	inner join membercentral.dbo.sub_subscribers as s on 
		s.subscriberID = psh.subscriberID
	inner join membercentral.dbo.sub_subscriptions as subs on 
		subs.subscriptionID = s.subscriptionID
	inner join membercentral.dbo.sub_types as t on 
		t.typeID = subs.typeID
		and t.siteID = @siteID
	inner join membercentral.dbo.sub_rateFrequencies as rf on
		rf.rfid = s.rfid
	inner join membercentral.dbo.sub_rates as r on
		r.rateID = rf.rateID
		and r.isRenewalRate = 1
		and r.status = 'A'
	inner join customApps.dbo.schedTask_memberJoinDateSubTypes mjdst on 
		mjdst.subscriptionTypeUID = t.uid
		and mjdst.memberJoinDateUDID = @udid
		and subs.uid = isnull(mjdst.subscriptionUID,subs.uid)
	inner join membercentral.dbo.sub_statuses as st on 
		st.statusID = s.statusID
		and st.statusCode in ('A','P') -- Active, Accepted
	inner join membercentral.dbo.sub_paymentStatuses ps on
		ps.statusID = s.paymentStatusID
		and ps.statusCode = 'P' -- Activation Requirement Met
	inner join membercentral.dbo.ams_members as m on 
		m.memberID = s.memberID
	inner join membercentral.dbo.ams_memberData as md 
		on md.memberID = m.activeMemberID
	inner join membercentral.dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
	   and mdcv.columnvalueDate is not null
	inner join membercentral.dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
		and mdc.orgID = @orgID
		and mdc.columnName = @joinFieldName
	left outer join membercentral.dbo.ams_memberData as md2 
		inner join membercentral.dbo.ams_memberDataColumnValues as mdcv2 on mdcv2.valueID = md2.valueID
		inner join membercentral.dbo.ams_memberDataColumns as mdc2 on mdc2.columnID = mdcv2.columnID
			and mdc2.orgID = @orgID
			and mdc2.columnName = @renewalDateFieldName
		on md2.memberID = m.activeMemberID
	where 
		(mdcv2.columnvalueDate is null or mdcv2.columnvalueDate < mdcv.columnvalueDate)
	group by activeMemberID

	IF @@ERROR <> 0 BEGIN
		select @error_code = 1
		GOTO on_error
	END
END

--force times to midnight
update #subJoinDates set 
    updateDate = DATEADD(dd, DATEDIFF(dd,0,updateDate), 0)

-- perform the update
IF EXISTS (select top 1 id from #subJoinDates) BEGIN
	select @minSDID = min(id) from #subJoinDates
	while @minSDID is not null BEGIN
		select @currMemberID=activeMemberID, 
			@currColumnValue=convert(varchar,updateDate), 
			@currColumnID = columnID,
			@currColumnName = @renewalDateFieldName
		from #subJoinDates 
		where id = @minSDID
			IF @@ERROR <> 0 BEGIN
				select @error_code = 2
				GOTO on_error
			END
		
		EXEC @rc = membercentral.dbo.ams_setMemberData @currMemberID, @orgID, @currColumnName, @currColumnValue, 1
			IF @@ERROR <> 0 OR @rc <> 0 BEGIN
				select @error_code = 3
				GOTO on_error
			END

		select @minSDID = min(id) from #subJoinDates where id > @minSDID
	END

	-- set member as updated
	UPDATE m
	SET m.dateLastUpdated = getdate()
	from membercentral.dbo.ams_members as m
	inner join #subJoinDates as sd on sd.activeMemberID = m.memberID
		IF @@ERROR <> 0 BEGIN
			select @error_code = 4
			GOTO on_error
		END

	-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
	DECLARE @itemGroupUID uniqueidentifier, @memberIDList varchar(max), @conditionIDList varchar(max)
	SELECT @memberIDList = COALESCE(@memberIDList + ',', '') + cast(activeMemberID as varchar(10)) 
		from #subJoinDates 
		group by activeMemberID
	SELECT @conditionIDList = COALESCE(@conditionIDList + ',', '') + cast(c.conditionID as varchar(10)) 
		from #subJoinDates as sd
		inner join membercentral.dbo.ams_virtualGroupConditions as c on c.fieldcode = 'md_' + cast(sd.columnID as varchar(10))
		group by c.conditionID
	IF @memberIDList is not null or @conditionIDList is not null BEGIN
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberIDList, @conditionIDList=@conditionIDList, @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT
		IF @@ERROR <> 0 BEGIN
			select @error_code = 5
			GOTO on_error
		END
	END
END

update dbo.schedTask_memberJoinDates
set lastSuccessDate = getDate(),
	lastErrorCode = @error_code
where udid = @udid

GOTO on_done

on_error:
	update dbo.schedTask_memberJoinDates
	set lastErrorCode = @error_code
	where udid = @udid

on_done:
	IF OBJECT_ID('tempdb..#subJoinDates') IS NOT NULL 
		DROP TABLE #subJoinDates
	RETURN 0	

set nocount off
GO

-- (3) **************************************************************************************************

declare 
	@udid int, 
	@siteCode char(2), 
	@siteID int,
	@subscriptionTypeUID uniqueidentifier

select
	@siteCode = siteCode,
	@siteID = siteID
from
	membercentral.dbo.sites
where
	siteCode = 'NV'

select 
	@subscriptionTypeUID = UID
from 
	membercentral.dbo.sub_types 
where 
	siteID = @siteID
	and status = 'A'
	and typeName = 'Membership Dues'

if not exists(	select * 
				from dbo.schedTask_memberJoinDates
				where joinDateFieldName = 'Join Date' 
					and siteCode = @siteCode)
begin
	insert into dbo.schedTask_memberJoinDates (
		siteCode, 
		joinDateFieldName, 
		rejoinDateFieldName, 
		droppedDateFieldName, 
		lastSuccessDate, 
		lastErrorCode, 
		isActive, 
		paidThruDateFieldName,
		renewalDateFieldName
	)
	values (
		@siteCode,
		'Join Date',
		'Rejoin Date',
		'Dropped Date',
		NULL,
		0,
		1,
		'Paid Through Date',
		'Renewal Date'
	)

	select @udid =  scope_identity()

	insert into dbo.schedTask_memberJoinDateSubTypes (
		memberJoinDateUDID, 
		subscriptionTypeUID 
	)
	values (
		@udid,
		@subscriptionTypeUID	
	)

	declare @error_code int
	exec dbo.job_initializeMemberRenewalDates @udid=@udid, @error_code=@error_code

end
GO

-- (4) **************************************************************************************************

ALTER PROCEDURE dbo.job_memberJoinDates
@udid int,
@error_code int OUTPUT

AS

set nocount on

declare @siteCode varchar(10), @compareDate datetime,
	@joinFieldName varchar(max), @dropFieldName varchar(max), @rejoinFieldName varchar(max),
	@currColumnName varchar(max), @paidthrudatefieldname varchar(max), @renewalDateFieldName varchar(max)
declare @siteID int, @orgID int, @joinColumnID int, @droppedColumnID int, @rejoinColumnID int,
	@minSDID int, @rc int, @columnvalueID int, @currColumnValue varchar(max), @currColumnID int,
	@currMemberID int, @paidThruColumnID int, @renewalColumnID int
select @error_code = 0

IF OBJECT_ID('tempdb..#subJoinDates') IS NOT NULL 
	DROP TABLE #subJoinDates

CREATE TABLE #subJoinDates (
	id int identity(1,1) PRIMARY KEY, 
	activeMemberID int, 
	updateDate dateTime,
	[action] varchar(1), 
	columnID int, 
	columnValueID int
)

select 
	@siteCode = sitecode, @compareDate = isnull(lastSuccessDate,'1/1/1900'),
	@joinFieldName = joinDateFieldName, @dropFieldName = droppedDateFieldName,
	@rejoinFieldName = rejoinDateFieldName, @paidthrudatefieldname = paidThruDateFieldName,
	@renewalDateFieldName = renewalDateFieldName
from 
	dbo.schedTask_memberJoinDates
where 
	udid = @udid

select 
	@siteID = siteID, @orgID = orgID
from 
	membercentral.dbo.sites
where 
	siteCode = @siteCode

-- get columnIDs
select @joinColumnID=columnID
	from membercentral.dbo.ams_memberDataColumns as mdc
	where orgID = @orgID
	and columnName = @joinFieldName

select @droppedColumnID=columnID
	from membercentral.dbo.ams_memberDataColumns as mdc
	where orgID = @orgID
	and columnName = @dropFieldName

select @rejoinColumnID=columnID
	from membercentral.dbo.ams_memberDataColumns as mdc
	where orgID = @orgID
	and columnName = @rejoinFieldName

select @paidThruColumnID=columnID
	from membercentral.dbo.ams_memberDataColumns as mdc
	where orgID = @orgID
	and columnName = @paidthrudatefieldname

select @renewalColumnID=columnID
	from membercentral.dbo.ams_memberDataColumns as mdc
	where orgID = @orgID
	and columnName = @renewalDateFieldName

-- get rows that do not have a join date but do have an active subscription
IF @joinColumnID IS NOT NULL 
BEGIN
	insert into #subJoinDates (activeMemberID, updateDate, [action], columnID)
	select m.activeMemberID, max(sh.updateDate) as updateDate, 'J' as [action], @joinColumnID as columnID
	from membercentral.dbo.sub_statusHistory as sh WITH(NOLOCK)
	inner join membercentral.dbo.sub_subscribers as s WITH(NOLOCK) on s.subscriberID = sh.subscriberID
	inner join membercentral.dbo.sub_subscriptions as subs WITH(NOLOCK) on subs.subscriptionID = s.subscriptionID
	inner join membercentral.dbo.sub_types as t WITH(NOLOCK) on t.typeID = subs.typeID and t.siteID = @siteID
	inner join schedTask_memberJoinDateSubTypes as mjdst WITH(NOLOCK)
		on mjdst.subscriptionTypeUID = t.uid
		and subs.uid = isnull(mjdst.subscriptionUID,subs.uid)
		and mjdst.memberJoinDateUDID = @udid
	inner join membercentral.dbo.sub_statuses as st WITH(NOLOCK) on st.statusID = sh.statusID
		and st.statusCode = 'A'
	inner join membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberID = s.memberID
	left outer join membercentral.dbo.ams_memberData as md WITH(NOLOCK)
		inner join membercentral.dbo.ams_memberDataColumnValues as mdcv WITH(NOLOCK) on mdcv.valueID = md.valueID
		inner join membercentral.dbo.ams_memberDataColumns as mdc WITH(NOLOCK) on mdc.columnID = mdcv.columnID
			and mdc.orgID = @orgID
			and mdc.columnName = @joinFieldName
		on md.memberID = m.activeMemberID
	where mdcv.columnvalueDate is null
	and sh.updateDate > @compareDate
	group by activeMemberID

	IF @@ERROR <> 0 BEGIN
		select @error_code = 2
		GOTO on_error
	END
END

-- get rows that have been expired, deleted, or had an offer expired and do not have an active, pending, or offered Status
IF @droppedColumnID IS NOT NULL 
BEGIN
	insert into #subJoinDates (activeMemberID, updateDate, [action], columnID)
	select m.activeMemberID, max(sh.updateDate), 'D' as [action], @droppedColumnID as columnID
	from membercentral.dbo.sub_statusHistory as sh
	inner join membercentral.dbo.sub_subscribers as s on s.subscriberID = sh.subscriberID
	inner join membercentral.dbo.sub_subscriptions as subs on subs.subscriptionID = s.subscriptionID
	inner join membercentral.dbo.sub_types as t on t.typeID = subs.typeID
		and t.siteID = @siteID
	inner join schedTask_memberJoinDateSubTypes mjdst
		on mjdst.subscriptionTypeUID = t.uid
		and mjdst.memberJoinDateUDID = @udid
		and subs.uid = isnull(mjdst.subscriptionUID,subs.uid)
	inner join membercentral.dbo.sub_statuses as st on st.statusID = sh.statusID
		and st.statusCode in ('E','X','D')
	inner join membercentral.dbo.ams_members as m on m.memberID = s.memberID
	where sh.updateDate > @compareDate
	group by activeMemberID
		IF @@ERROR <> 0 BEGIN
			select @error_code = 3
			GOTO on_error
		END

	delete from #subJoinDates
	where [action] = 'D'
	and activeMemberID in (
		select distinct m.activeMemberID
		from membercentral.dbo.sub_subscribers as s
		inner join membercentral.dbo.sub_subscriptions as subs on subs.subscriptionID = s.subscriptionID
		inner join membercentral.dbo.sub_types as t on t.typeID = subs.typeID
			and t.siteID = @siteID
		inner join schedTask_memberJoinDateSubTypes mjdst
			on mjdst.subscriptionTypeUID = t.uid
			and mjdst.memberJoinDateUDID = @udid
			and subs.uid = isnull(mjdst.subscriptionUID,subs.uid)
		inner join membercentral.dbo.sub_statuses as st on st.statusID = s.statusID
			and st.statusCode in ('A','P','O')
		inner join membercentral.dbo.ams_members as m on m.memberID = s.memberID
		inner join #subJoinDates as sd on sd.activeMemberID = m.activeMemberID
	)
		IF @@ERROR <> 0 BEGIN
			select @error_code = 4
			GOTO on_error
		END
END

-- get people who have been moved to active, have a drop date, and rejoin date is null or before drop date
IF @rejoinColumnID is not null
BEGIN
	insert into #subJoinDates (activeMemberID, updateDate, [action], columnID)
	select m.activeMemberID, max(sh.updateDate), 'R' as [action], @rejoinColumnID as columnID
	from membercentral.dbo.sub_statusHistory as sh
	inner join membercentral.dbo.sub_subscribers as s on s.subscriberID = sh.subscriberID
	inner join membercentral.dbo.sub_subscriptions as subs on subs.subscriptionID = s.subscriptionID
	inner join membercentral.dbo.sub_types as t on t.typeID = subs.typeID
		and t.siteID = @siteID
	inner join schedTask_memberJoinDateSubTypes mjdst
		on mjdst.subscriptionTypeUID = t.uid
		and mjdst.memberJoinDateUDID = @udid
		and subs.uid = isnull(mjdst.subscriptionUID,subs.uid)
	inner join membercentral.dbo.sub_statuses as st on st.statusID = sh.statusID
		and st.statusCode = 'A'
	inner join membercentral.dbo.ams_members as m on m.memberID = s.memberID
	left outer join membercentral.dbo.ams_memberData as md 
		inner join membercentral.dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
		inner join membercentral.dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
			and mdc.orgID = @orgID
			and mdc.columnName = @dropFieldName
		on md.memberID = m.activeMemberID
	left outer join membercentral.dbo.ams_memberData as md2 
		inner join membercentral.dbo.ams_memberDataColumnValues as mdcv2 on mdcv2.valueID = md2.valueID
		inner join membercentral.dbo.ams_memberDataColumns as mdc2 on mdc2.columnID = mdcv2.columnID
			and mdc2.orgID = @orgID
			and mdc2.columnName = @rejoinFieldName
		on md2.memberID = m.activeMemberID
	where mdcv.columnvalueDate is not null
	and (mdcv2.columnvalueDate is null or mdcv2.columnvalueDate < mdcv.columnvalueDate)
	and sh.updateDate > @compareDate
	group by activeMemberID
		IF @@ERROR <> 0 BEGIN
			select @error_code = 5
			GOTO on_error
		END
END

-- find members with subscription activity and add those needing paidThrudate changes to update table
IF @paidThruColumnID IS NOT NULL 
BEGIN
	;with cte as (
		select m.activeMemberID, mdcv.columnvalueDate, max(latestActivatedS.subenddate) as paidThruDate,
			'P' as [action], @paidThruColumnID as columnID
		from membercentral.dbo.sub_types as t 
		inner join membercentral.dbo.sub_subscriptions subs on t.typeID = subs.typeID and t.siteID = @siteID
		inner join schedTask_memberJoinDateSubTypes mjdst 
			on mjdst.subscriptionTypeUID = t.uid 
			and mjdst.memberJoinDateUDID = @udid
			and subs.uid = isnull(mjdst.subscriptionUID,subs.uid)
		inner join membercentral.dbo.sub_subscribers as s on s.subscriptionID = subs.subscriptionID
		inner join membercentral.dbo.sub_statusHistory as sh on s.subscriberID = sh.subscriberID
		left outer join membercentral.dbo.sub_paymentStatusHistory psh on psh.subscriberID = s.subscriberID
		inner join membercentral.dbo.ams_members m on m.memberID = s.memberID
		inner join membercentral.dbo.ams_members allm on allm.activeMemberID = m.activeMemberID
		inner join membercentral.dbo.sub_subscriptions as latestActivatedSub on latestActivatedSub.typeID = t.typeID
		inner join membercentral.dbo.sub_subscribers as latestActivatedS on latestActivatedS.memberID = allm.memberID
			and latestActivatedS.subscriptionID = latestActivatedSub.subscriptionID
		inner join membercentral.dbo.sub_paymentStatuses ps on ps.statusID = latestActivatedS.paymentStatusID
			and ps.statusCode = 'P'
		-- only consider if the current status is one that implies that the subscription was or will be active at some point 
		inner join membercentral.dbo.sub_statuses as st on st.statusID = latestActivatedS.statusID
			and st.statusCode in ('A','I','P','E')
		left outer join membercentral.dbo.ams_memberData as md
			inner join membercentral.dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
			inner join membercentral.dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
				and mdc.orgID = @orgID
				and mdc.columnName = @paidthrudatefieldname
		on md.memberID = m.activeMemberID
		where (sh.updateDate > @compareDate or psh.updateDate > @compareDate)
		group by m.activememberID, mdcv.columnvalueDate
	)
	insert into #subJoinDates (activeMemberID, updateDate, [action], columnID)
	select activeMemberID, paidThruDate,[action], columnID
	from cte
	where (columnvalueDate is null or columnvalueDate <> paidThruDate)
		IF @@ERROR <> 0 BEGIN
			select @error_code = 6
			GOTO on_error
		END
END

-- get people who have been moved to active or accepted, have a join date, and renewal date is null or before join date
IF @renewalColumnID is not null
BEGIN
	insert into #subJoinDates (activeMemberID, updateDate, [action], columnID)
	select m.activeMemberID, max(psh.updateDate), 'W' as [action], @renewalColumnID as columnID
	from membercentral.dbo.sub_paymentStatusHistory as psh
	inner join membercentral.dbo.sub_subscribers as s on 
		s.subscriberID = psh.subscriberID
		and psh.updateDate > @compareDate
	inner join membercentral.dbo.sub_subscriptions as subs on 
		subs.subscriptionID = s.subscriptionID
	inner join membercentral.dbo.sub_types as t on 
		t.typeID = subs.typeID
		and t.siteID = @siteID
	inner join membercentral.dbo.sub_rateFrequencies as rf on
		rf.rfid = s.rfid
	inner join membercentral.dbo.sub_rates as r on
		r.rateID = rf.rateID
		and r.isRenewalRate = 1
		and r.status = 'A'
	inner join customApps.dbo.schedTask_memberJoinDateSubTypes mjdst on 
		mjdst.subscriptionTypeUID = t.uid
		and mjdst.memberJoinDateUDID = @udid
		and subs.uid = isnull(mjdst.subscriptionUID,subs.uid)
	inner join membercentral.dbo.sub_statuses as st on 
		st.statusID = s.statusID
		and st.statusCode in ('A','P') -- Active, Accepted
	inner join membercentral.dbo.sub_paymentStatuses ps on
		ps.statusID = s.paymentStatusID
		and ps.statusCode = 'P' -- Activation Requirement Met
	inner join membercentral.dbo.ams_members as m on 
		m.memberID = s.memberID
	inner join membercentral.dbo.ams_memberData as md 
		on md.memberID = m.activeMemberID
	inner join membercentral.dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
	   and mdcv.columnvalueDate is not null
	inner join membercentral.dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
		and mdc.orgID = @orgID
		and mdc.columnName = @joinFieldName
	left outer join membercentral.dbo.ams_memberData as md2 
		inner join membercentral.dbo.ams_memberDataColumnValues as mdcv2 on mdcv2.valueID = md2.valueID
		inner join membercentral.dbo.ams_memberDataColumns as mdc2 on mdc2.columnID = mdcv2.columnID
			and mdc2.orgID = @orgID
			and mdc2.columnName = @renewalDateFieldName
		on md2.memberID = m.activeMemberID
	where 
		(mdcv2.columnvalueDate is null or mdcv2.columnvalueDate < mdcv.columnvalueDate)
	group by activeMemberID

	IF @@ERROR <> 0 BEGIN
		select @error_code = 5
		GOTO on_error
	END
END

--force times to midnight
update #subJoinDates set 
    updateDate = DATEADD(dd, DATEDIFF(dd,0,updateDate), 0)

-- perform the update
IF EXISTS (select top 1 id from #subJoinDates) BEGIN
	select @minSDID = min(id) from #subJoinDates
	while @minSDID is not null BEGIN
		select @currMemberID=activeMemberID, 
			@currColumnValue=convert(varchar,updateDate), 
			@currColumnID = columnID,
			@currColumnName = case 
				when [action] = 'R' then @rejoinFieldName
				when [action] = 'D' then @dropFieldName
				when [action] = 'J' then @joinFieldName
				when [action] = 'P' then @paidthrudatefieldname
				when [action] = 'W' then @renewalDateFieldName
				end
		from #subJoinDates 
		where id = @minSDID
			IF @@ERROR <> 0 BEGIN
				select @error_code = 7
				GOTO on_error
			END
		
		EXEC @rc = membercentral.dbo.ams_setMemberData @currMemberID, @orgID, @currColumnName, @currColumnValue, 1
			IF @@ERROR <> 0 OR @rc <> 0 BEGIN
				select @error_code = 8
				GOTO on_error
			END

		select @minSDID = min(id) from #subJoinDates where id > @minSDID
	END

	-- set member as updated
	UPDATE m
	SET m.dateLastUpdated = getdate()
	from membercentral.dbo.ams_members as m
	inner join #subJoinDates as sd on sd.activeMemberID = m.memberID
		IF @@ERROR <> 0 BEGIN
			select @error_code = 9
			GOTO on_error
		END

	-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
	DECLARE @itemGroupUID uniqueidentifier, @memberIDList varchar(max), @conditionIDList varchar(max)
	SELECT @memberIDList = COALESCE(@memberIDList + ',', '') + cast(activeMemberID as varchar(10)) 
		from #subJoinDates 
		group by activeMemberID
	SELECT @conditionIDList = COALESCE(@conditionIDList + ',', '') + cast(c.conditionID as varchar(10)) 
		from #subJoinDates as sd
		inner join membercentral.dbo.ams_virtualGroupConditions as c on c.fieldcode = 'md_' + cast(sd.columnID as varchar(10))
		group by c.conditionID
	IF @memberIDList is not null or @conditionIDList is not null BEGIN
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberIDList, @conditionIDList=@conditionIDList, @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT
		IF @@ERROR <> 0 BEGIN
			select @error_code = 10
			GOTO on_error
		END
	END
END

update dbo.schedTask_memberJoinDates
set lastSuccessDate = getDate(),
	lastErrorCode = @error_code
where udid = @udid

GOTO on_done

on_error:
	update dbo.schedTask_memberJoinDates
	set lastErrorCode = @error_code
	where udid = @udid

on_done:
	IF OBJECT_ID('tempdb..#subJoinDates') IS NOT NULL 
		DROP TABLE #subJoinDates
	RETURN 0	

set nocount off
GO
