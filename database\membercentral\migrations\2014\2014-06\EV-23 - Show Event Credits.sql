use membercentral
GO

-- remove the showCredit column on the crd_offerings table since we dont use it and would soon cause confusion.
ALTER PROC [dbo].[crd_addOffering]
@applicationType varchar(20),
@itemID int,
@ASID int,
@offeredID int OUTPUT

AS

declare @statusID int, @offeredStartDate datetime, @offeredEndDate datetime, @completeByDate datetime
select @statusID = statusID from dbo.crd_statuses where [status] = 'Not Submitted'
select @offeredID = 0

IF @applicationType = 'Events' BEGIN
	select @offeredStartDate = min(starttime), @offeredEndDate = max(endTime)
		from dbo.ev_times
		where eventID = @itemID

	INSERT INTO dbo.crd_offerings (ASID, statusID, ApprovalNum, offeredStartDate, offeredEndDate, completeByDate, isCreditRequired, isIDRequired, isCreditDefaulted, eventID, notes)
	VALUES (@ASID, @statusID, '', @offeredStartDate, @offeredEndDate, @offeredEndDate, 0, 0, 0, @itemID, null)
		SELECT @offeredID = SCOPE_IDENTITY()
END

IF @applicationType = 'Store' BEGIN
	select @offeredStartDate = DATEADD(dd, DATEDIFF(dd,0,getdate()), 0), 
		   @offeredEndDate = dateadd(s,-1,dateadd(day,1,DATEADD(dd, DATEDIFF(dd,0,dateadd(yyyy,5,getdate())), 0)))

	INSERT INTO dbo.crd_offerings (ASID, statusID, ApprovalNum, offeredStartDate, offeredEndDate, completeByDate, isCreditRequired, isIDRequired, isCreditDefaulted, productFormatID, notes)
	VALUES (@ASID, @statusID, '', @offeredStartDate, @offeredEndDate, @offeredEndDate, 0, 0, 0, @itemID, null)
		SELECT @offeredID = SCOPE_IDENTITY()
END

RETURN 0
GO
ALTER PROC [dbo].[crd_getCreditsOfferedGrid]
@applicationType varchar(20),
@itemID int

AS

SELECT ec.offeringID, ec.statusid, cas.ASID, ec.ApprovalNum, ec.offeredStartDate, 
	ec.offeredEndDate, ec.completeByDate, ec.isCreditRequired, 
	ec.isIDRequired, ec.isCreditDefaulted, cstat.status, cas.authorityID,
	cas.sponsorID, ca.authorityName, cstat.statusOrder as bestCategory, ec.notes, 
	cast(isnull((
	select ECT.offeringTypeID, ECT.ASTID, ect.creditValue, isnull(ast.ovTypeName,cat.typeName) as creditType
	from dbo.crd_offeringTypes as ect
	inner join dbo.crd_authoritySponsorTypes as ast on ast.ASTID = ect.ASTID
	inner join dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
	where ect.offeringID = ec.offeringID
	order by 4, 3
	FOR XML AUTO, ROOT('creditTypes')
	),'<creditTypes/>') as xml) as offeredCreditTypes
FROM dbo.crd_offerings AS ec
INNER JOIN dbo.crd_statuses as cstat on cstat.statusID = ec.statusID
INNER JOIN dbo.crd_authoritySponsors as cas on cas.ASID = ec.ASID
INNER JOIN dbo.crd_authorities as ca on ca.authorityID = cas.authorityID
INNER JOIN dbo.crd_sponsors as cs on cs.sponsorID = cas.sponsorID
WHERE 
	case @applicationType 
		when 'Events' then ec.eventID
		when 'Store' then ec.productFormatID
		else null
		end = @itemID
ORDER BY ca.authorityName

RETURN 0
GO
ALTER PROC [dbo].[crd_updateOffering]
@offeringID int,
@statusID tinyint,
@approvalNum varchar(50),
@offeredStartDate datetime,
@offeredEndDate datetime,
@completeByDate datetime,
@isCreditRequired bit,
@isIDRequired bit,
@isCreditDefaulted bit,
@notes varchar(max)

AS

UPDATE dbo.crd_offerings
SET statusID = @statusID,
	approvalNum = @approvalNum,
	offeredStartDate = @offeredStartDate,
	offeredEndDate = @offeredEndDate,
	completeByDate = @completeByDate,
	isCreditRequired = @isCreditRequired,
	isIDRequired = @isIDRequired,
	isCreditDefaulted = @isCreditDefaulted,
	notes = @notes
WHERE offeringID = @offeringID

RETURN 0
GO
ALTER PROC [dbo].[ev_copyEvent]
@eventid int,
@copiedByMemberID int,
@newEventID int OUTPUT

AS

DECLARE @rc int
DECLARE @siteID int, @eventTypeID int, @lockTimeZoneID int, @isAllDayEvent bit,
	@altRegistrationURL varchar(300), @GLAccountID int, @calendarID int, @status char(1), @reportCode varchar(15),
	@internalNotes varchar(max)
DECLARE @eventContentID int, @contactContentID int, @locationContentID int, @cancelContentID int, @travelContentID int, @informationContentID int
DECLARE @neweventContentID int, @newcontactContentID int, @newlocationContentID int, @newcancelContentID int, @newtravelContentID int, @newinformationContentID int
DECLARE @languageID int, @isSSL bit, @isHTML bit, @contentTitle varchar(200), @contentDesc varchar(400), @rawcontent varchar(max)
DECLARE @emailContactContent bit, @emailLocationContent bit, @emailCancelContent bit, @emailTravelContent	bit
DECLARE @registrationID int, @registrationTypeID int, @startDate datetime, @endDate datetime, @registrantCap int,
	@ReplyToEmail varchar(200), @notifyEmail varchar(200), @isPriceBasedOnActual bit, @bulkCountByRate bit, @newregistrationID int
DECLARE @expirationContentID int, @registrantCapContentID int
DECLARE @newexpirationContentID int, @newregistrantCapContentID int
DECLARE @minofferingID int, @newofferingID int
DECLARE @minRateId int, @rateGroupingID int, @rateGLAccountID int, @rateName varchar(100),
	@rate money, @ratestartDate datetime, @rateendDate datetime, @newRateID int, @newRatesiteResourceID int,
	@ratereportCode varchar(15), @rateQty int
DECLARE @minBulkRateID int, @bulkrate money, @bulksiteResourceID int, @bulkrateqty int, @newBulkRateID int,
	@newBulkRatesiteResourceID int, @bulkresourceRightID int
DECLARE @siteResourceID int, @newrateGroupingID int
DECLARE @srr_rightsID int, @srr_roleid int, @srr_functionID int, @srr_groupid int, @srr_memberid int, @srr_include bit, @srr_inheritedRightsResourceID int, @srr_inheritedRightsFunctionID int, @resourceRightID int
DECLARE @minCustomID int, @newCustomID int
DECLARE @isOnlineMeeting bit, @onlineEmbedCode varchar(max), @onlineEmbedOverrideLink varchar(400), @onlineEnterStartTime datetime, @onlineEnterEndTime datetime
SELECT @newEventID = null

BEGIN TRAN

-- get the event we are copying
SELECT @siteID=siteID, @eventTypeID=eventTypeID, 
	@lockTimeZoneID=lockTimeZoneID, @isAllDayEvent=isAllDayEvent, @altRegistrationURL=altRegistrationURL,
	@GLAccountID=GLAccountID, @status=status, @reportCode=reportcode, @internalNotes=internalNotes,
	@emailContactContent = emailContactContent, @emailLocationContent = emailLocationContent,
	@emailCancelContent = emailCancelContent, @emailTravelContent = emailTravelContent
	FROM dbo.ev_events
	WHERE eventID = @eventID
	IF @@ERROR <> 0 GOTO on_error
SELECT TOP 1 @calendarID = calendarID
	FROM dbo.ev_calendarEvents
	WHERE calendarID = sourceCalendarID
	AND sourceEventID = @eventID
	IF @@ERROR <> 0 GOTO on_error

-- create event
EXEC @rc = dbo.ev_createEvent @siteID=@siteID, @calendarID=@calendarID, @eventTypeID=@eventTypeID, 
	@enteredByMemberID=@copiedByMemberID, @lockTimeZoneID=@lockTimeZoneID, @isAllDayEvent=@isAllDayEvent, 
	@altRegistrationURL=@altRegistrationURL, @status=@status, @reportCode=@reportCode, 
	@emailContactContent=@emailContactContent, @emailLocationContent=@emailLocationContent,
	@emailCancelContent=@emailCancelContent, @emailTravelContent=@emailTravelContent,
	@eventID=@newEventID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

update dbo.ev_events
set GLAccountID = @GLAccountID,
	internalNotes = @internalNotes
where eventID = @newEventID
	IF @@ERROR <> 0 GOTO on_error

select @neweventContentID=eventContentID, @newcontactContentID=contactContentID, @newlocationContentID=locationContentID,
	@newcancelContentID=cancellationPolicyContentID, @newtravelContentID=travelContentID, @newinformationContentID=informationContentID
FROM dbo.ev_events
where eventID = @newEventID
	IF @@ERROR <> 0 GOTO on_error

select @eventContentID=eventContentID, @contactContentID=contactContentID, @locationContentID=locationContentID,
	@cancelContentID=cancellationPolicyContentID, @travelContentID=travelContentID, @informationContentID=informationContentID
FROM dbo.ev_events
where eventID = @EventID
	IF @@ERROR <> 0 GOTO on_error

-- make event inactive
UPDATE dbo.ev_events
SET [status] = 'I'
WHERE eventID = @newEventID
	IF @@ERROR <> 0 GOTO on_error

-- copy event category
INSERT INTO dbo.ev_eventcategories (eventID, categoryID)
select @newEventID, categoryID
from dbo.ev_eventcategories
where eventID = @eventID
	IF @@ERROR <> 0 GOTO on_error

-- copy event times
INSERT INTO dbo.ev_Times (eventid, timeZoneID, startTime, endTime)
select @newEventID, timeZoneID, startTime, endTime
from dbo.ev_times
where eventid = @eventID
	IF @@ERROR <> 0 GOTO on_error

-- copy content objects
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle='Copy of ' + contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@eventContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@neweventContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@contactContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newcontactContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@locationContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newlocationContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@cancelContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newcancelContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@travelContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newtravelContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@informationContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newinformationContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- copy sponsors
insert into dbo.ev_sponsors (eventID, sponsorContentID, sponsorOrder)
select @newEventID, sponsorContentID, sponsorOrder
from dbo.ev_sponsors
where eventID = @eventID
	IF @@ERROR <> 0 GOTO on_error

-- does orig event have registration?
SELECT @registrationID=registrationid, @registrationTypeID=registrationTypeID, @startDate=startdate, 
	@endDate=endDate, @registrantCap=registrantCap, @ReplyToEmail=ReplyToEmail, @notifyEmail=notifyEmail, 
	@isPriceBasedOnActual=isPriceBasedOnActual, @bulkCountByRate=bulkCountByRate, @expirationContentID=expirationContentID, 
	@registrantCapContentID=registrantCapContentID, @isOnlineMeeting=isOnlineMeeting, @onlineEmbedCode=onlineEmbedCode,
	@onlineEmbedOverrideLink=onlineEmbedOverrideLink, @onlineEnterStartTime=onlineEnterStartTime, @onlineEnterEndTime=onlineEnterEndTime
	from dbo.ev_registration
	where eventID = @eventID
	and [status] <> 'D'
IF @registrationID is not null BEGIN

	-- insert registration
	EXEC @rc = dbo.ev_createRegistration @eventID=@newEventID, @registrationTypeID=@registrationTypeID, 
			@startDate=@startDate, @endDate=@endDate, @registrantCap=@registrantCap, @ReplyToEmail=@ReplyToEmail,
			@notifyEmail=@notifyEmail, @isPriceBasedOnActual=@isPriceBasedOnActual, @bulkCountByRate=@bulkCountByRate,
			@registrationID=@newregistrationID OUTPUT
		IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

	select @newexpirationContentID=expirationContentID, @newregistrantCapContentID=registrantCapContentID
	FROM dbo.ev_registration
	where registrationID = @newregistrationID
		IF @@ERROR <> 0 GOTO on_error

	-- copy content objects
	select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@expirationContentID,1)
	EXEC @rc = dbo.cms_updateContent @contentID=@newexpirationContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
		IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
	select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@registrantCapContentID,1)
	EXEC @rc = dbo.cms_updateContent @contentID=@newregistrantCapContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
		IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

	-- if reg type
	IF @registrationTypeID = 1 BEGIN

		-- other registration fields
		update dbo.ev_registration
		set isOnlineMeeting = @isOnlineMeeting,
			onlineEmbedCode = @onlineEmbedCode,
			onlineEmbedOverrideLink = @onlineEmbedOverrideLink,
			onlineEnterStartTime = @onlineEnterStartTime,
			onlineEnterEndTime = @onlineEnterEndTime
		where registrationID = @newregistrationID
			IF @@ERROR <> 0 GOTO on_error

		-- merchant profiles
		insert into dbo.ev_registrationMerchantProfiles (registrationID, profileID)
		select @newregistrationID, rmp.profileID
		from dbo.ev_registrationMerchantProfiles as rmp
		inner join dbo.mp_profiles as mp on mp.profileID = rmp.profileID
		inner join dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
		where rmp.registrationID = @registrationID
		and mp.status = 'A'
		and mp.allowPayments = 1
		and g.isActive = 1
			IF @@ERROR <> 0 GOTO on_error

		-- credit offered
		select @minofferingID = min(offeringID) from dbo.crd_offerings where eventID = @eventID
		while @minofferingID is not null BEGIN
			INSERT INTO dbo.crd_offerings (ASID, statusID, ApprovalNum, offeredStartDate, offeredEndDate, completeByDate, isCreditRequired, isIDRequired, isCreditDefaulted, eventID)
			SELECT ASID, statusID, ApprovalNum, offeredStartDate, offeredEndDate, completeByDate, isCreditRequired, isIDRequired, isCreditDefaulted, @newEventID
			FROM dbo.crd_offerings
			WHERE offeringID = @minofferingID
				IF @@ERROR <> 0 GOTO on_error
				SELECT @newofferingID = SCOPE_IDENTITY()

			INSERT INTO dbo.crd_offeringTypes (offeringID, ASTID, creditValue)
			SELECT @newofferingID, ASTID, creditValue
			FROM dbo.crd_offeringTypes
			WHERE offeringID = @minofferingID
				IF @@ERROR <> 0 GOTO on_error

			select @minofferingID = min(offeringID) from dbo.crd_offerings where eventID = @eventID and offeringID > @minofferingID
		END

		-- rate groupings
		insert into dbo.ev_rateGrouping (rateGrouping, registrationID, rateGroupingOrder)
		select rateGrouping, @newregistrationID, rateGroupingOrder
		from dbo.ev_rateGrouping
		where registrationID = @registrationID
			IF @@ERROR <> 0 GOTO on_error

		-- active rates and permissions
		select @minRateId = min(r.rateID) 
			from dbo.ev_rates as r
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			where r.registrationID = @registrationID
			and r.parentRateID is null
		while @minRateID is not null BEGIN
			select @rateGroupingID = null, @rateGLAccountID = null, @rateName = null, 
				@ratereportCode = null, @rate = null, @ratestartDate = null, @rateendDate = null, 
				@siteResourceID = null, @rateqty = null

			select @rateGroupingID=rateGroupingID, @rateGLAccountID=GLAccountID, @rateName=rateName, 
				@ratereportCode=reportCode, @rate=rate, @ratestartDate=startDate, @rateendDate=endDate, 
				@siteResourceID=siteResourceID, @rateqty=bulkQty
			from dbo.ev_rates 
			where rateID = @minRateID

			select @newrateGroupingID = rg1.rateGroupingID
			from dbo.ev_rateGrouping as rg1
			inner join dbo.ev_rateGrouping as rg2 
				on rg2.rateGrouping = rg1.rateGrouping
				and rg1.registrationID = @newregistrationID
				and rg2.registrationID = @registrationID
				and isnull(rg2.rateGroupingID,0) = isnull(@rateGroupingID,0)
			
			IF @rateName is not null and @rate is not null BEGIN
				EXEC @rc = dbo.ev_createRate @registrationID=@newregistrationID,  
					@rateGroupingID=@newrateGroupingID, @GLAccountID=@rateGLAccountID, @rateName=@rateName, 
					@reportCode=@ratereportCode, @rate=@rate, @startDate=@ratestartDate, @endDate=@rateendDate,
					@parentRateID=null, @qty=@rateqty, 
					@rateID=@newRateID OUTPUT, @siteResourceID=@newRatesiteResourceID OUTPUT
					IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

				select @minBulkRateID = min(rateID)
				from dbo.ev_rates
				where parentRateID = @minRateID

				while @minBulkRateID is not null begin
					select @bulkrate=rate, @bulksiteResourceID=siteResourceID, @bulkrateqty=bulkQty
					from dbo.ev_rates 
					where rateID = @minBulkRateID

					EXEC @rc = dbo.ev_createRate @registrationID=@newregistrationID,  
						@rateGroupingID=@newrateGroupingID, @GLAccountID=@rateGLAccountID, @rateName=@rateName, 
						@reportCode=@ratereportCode, @rate=@rate, @startDate=@ratestartDate, @endDate=@rateendDate,
						@parentRateID=@newRateID, @qty=@bulkrateqty, 
						@rateID=@newBulkRateID OUTPUT, @siteResourceID=@newBulkRatesiteResourceID OUTPUT
						IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

					select @minBulkRateID = min(rateID)
					from dbo.ev_rates
					where parentRateID = @minRateID
					and rateID > @minBulkRateID
				end

				-- copy resource rights for this resource		
				SELECT @srr_rightsID = null	
				SELECT @srr_rightsID = min(resourceRightsID) from dbo.cms_siteResourceRights where resourceID = @siteResourceID
				WHILE @srr_rightsID IS NOT NULL BEGIN
					SELECT @srr_roleid=roleID, @srr_functionID=functionID, @srr_groupid=groupID, 
						@srr_memberid=memberID, @srr_include=[include], @srr_inheritedRightsResourceID=inheritedRightsResourceID, 
						@srr_inheritedRightsFunctionID=inheritedRightsFunctionID
					FROM dbo.cms_siteResourceRights
					WHERE resourceRightsID = @srr_rightsID

					EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@newRatesiteResourceID, @include=@srr_include, 
						@functionID=@srr_functionID, @roleID=@srr_roleid, @groupID=@srr_groupid, @memberID=@srr_memberid, 
						@inheritedRightsResourceID=@srr_inheritedRightsResourceID, @inheritedRightsFunctionID=@srr_inheritedRightsFunctionID, 
						@resourceRightID=@resourceRightID OUTPUT
					IF @@ERROR <> 0 GOTO on_error

					select @minBulkRateID = min(rateID)
					from dbo.ev_rates
					where parentRateID = @newRateID

					while @minBulkRateID is not null begin
						select @bulksiteResourceID=siteResourceID
						from dbo.ev_rates 
						where rateID = @minBulkRateID

						EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@bulksiteResourceID, @include=@srr_include, 
							@functionID=@srr_functionID, @roleID=@srr_roleid, @groupID=@srr_groupid, @memberID=@srr_memberid, 
							@inheritedRightsResourceID=@srr_inheritedRightsResourceID, @inheritedRightsFunctionID=@srr_inheritedRightsFunctionID, 
							@resourceRightID=@bulkresourceRightID OUTPUT
						IF @@ERROR <> 0 GOTO on_error

						select @minBulkRateID = min(rateID)
						from dbo.ev_rates
						where parentRateID = @minRateID
						and rateID > @newRateID
					end
		
					SELECT @srr_rightsID = min(resourceRightsID) from dbo.cms_siteResourceRights where resourceID = @siteResourceID and resourceRightsID > @srr_rightsID
				END

			END
			
			select @minRateId = min(rateID) 
				from dbo.ev_rates as r
				inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
				inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
					and srs.siteResourceStatusDesc = 'Active'
				where r.registrationID = @registrationID
				and r.parentRateID is null
				and rateID > @minRateID
		END

		-- custom questions
		SELECT @minCustomID = min(CustomID) FROM dbo.ev_registrationCustom WHERE registrationID = @registrationID
		WHILE @minCustomID is not null BEGIN
			INSERT INTO dbo.ev_registrationCustom (registrationID, areaID, FieldDesc, titleOnInvoice, customTypeID, IsRequired, RequiredMsg, fieldOrder, Status, amount, offerQTY, GLAccountID)
			SELECT	@newregistrationID, areaID, FieldDesc, titleOnInvoice, customTypeID, IsRequired, RequiredMsg, fieldOrder, Status, amount, offerQTY, GLAccountID
			FROM	dbo.ev_registrationCustom	
			WHERE	customID = @minCustomID
				IF @@ERROR <> 0 GOTO on_error
				SELECT @newCustomID = SCOPE_IDENTITY()
				
			INSERT INTO dbo.ev_customOptions(customID, optionDesc, optionOrder, status, amount)
			SELECT	@newCustomID, optionDesc, optionOrder, status, amount
			FROM	dbo.ev_customOptions
			WHERE	customID = @minCustomID
				IF @@ERROR <> 0 GOTO on_error			
	
			SELECT @minCustomID = min(CustomID) FROM dbo.ev_registrationCustom WHERE registrationID = @registrationID and CustomID > @minCustomID	
		END

	END

END

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @newEventID = 0
	RETURN -1
GO

ALTER PROC [dbo].[store_getCreditsForProduct]
@productID int

AS

SELECT spf.FormatID, spf.name, ca.authorityName, cstat.status, ec.offeredEndDate, 
	cs.statementAppProvider, cas.creditMessage, 
	cast(isnull((
	select ect.creditValue, isnull(ast.ovTypeName,cat.typeName) as creditType
	from dbo.crd_offeringTypes as ect
	inner join dbo.crd_authoritySponsorTypes as ast on ast.ASTID = ect.ASTID
	inner join dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
	where ect.offeringID = ec.offeringID
	order by 2, 1
	FOR XML AUTO, ROOT('creditTypes')
	),'<creditTypes/>') as xml) as offeredCreditTypes
FROM dbo.crd_offerings AS ec
INNER JOIN dbo.crd_statuses as cstat on cstat.statusID = ec.statusID
INNER JOIN dbo.crd_authoritySponsors as cas on cas.ASID = ec.ASID
INNER JOIN dbo.crd_authorities as ca on ca.authorityID = cas.authorityID
INNER JOIN dbo.crd_sponsors as cs on cs.sponsorID = cas.sponsorID
INNER JOIN dbo.store_productFormats as spf on spf.FormatID = ec.productFormatID
INNER JOIN dbo.store_products as sp on sp.ItemID = spf.itemID
INNER JOIN dbo.store on store.storeID = sp.storeID
WHERE sp.itemID = @productID
and cstat.status in ('Pending','Approved')
and store.offerAffirmations = 1
and spf.offerAffirmations = 1
and getdate() between ec.offeredStartDate and ec.offeredEndDate
ORDER BY spf.formatOrder, ca.authorityName

RETURN 0
GO

ALTER TABLE dbo.crd_offerings DROP CONSTRAINT DF_crd_offerings_showCredit
GO
ALTER TABLE dbo.crd_offerings DROP COLUMN showCredit
GO


-- add column at registration level
ALTER TABLE dbo.ev_registration ADD showCredit bit NOT NULL CONSTRAINT DF_ev_registration_showCredit DEFAULT 0
GO
ALTER PROC [dbo].[ev_getRegistrationMetaByEventID]
@eventID int,
@languageID int

AS

select r.registrationid, r.registrationtypeID, rt.registrationtype, r.startdate, r.enddate, 
	r.registrantCap, r.notifyEmail, r.replyToEmail, r.status, r.isPriceBasedOnActual, r.bulkCountByRate, 
	r.expirationcontentID as expireContentID, expireContent.contentTitle as expireContentTitle, 
	expireContent.rawContent as expireContent,
	r.registrantCapcontentID as registrantCapContentID, 
	registrantCapContent.contentTitle as registrantCapContentTitle, 
	registrantCapContent.rawContent as registrantCapContent,
	dbo.fn_getRegCapReached(r.eventID) as regCapReached,
	r.freeRateDisplay, r.isOnlineMeeting, r.onlineEnterStartTime, r.onlineEnterEndTime, 
	r.onlineEmbedOverrideLink, r.onlineEmbedCode, r.showCredit
from dbo.ev_registration as r
inner join dbo.ev_registrationTypes as rt on rt.registrationTypeID = r.registrationTypeID
inner join dbo.ev_events as e on e.eventid = r.eventid and e.status <> 'D'
cross apply dbo.fn_getContent(r.expirationcontentID,@languageID) as expireContent
cross apply dbo.fn_getContent(r.registrantCapcontentID,@languageID) as registrantCapContent
where r.eventID = @eventID
and r.status <> 'D'

RETURN 0
GO
ALTER PROC [dbo].[ev_copyEvent]
@eventid int,
@copiedByMemberID int,
@newEventID int OUTPUT

AS

DECLARE @rc int
DECLARE @siteID int, @eventTypeID int, @lockTimeZoneID int, @isAllDayEvent bit,
	@altRegistrationURL varchar(300), @GLAccountID int, @calendarID int, @status char(1), @reportCode varchar(15),
	@internalNotes varchar(max)
DECLARE @eventContentID int, @contactContentID int, @locationContentID int, @cancelContentID int, @travelContentID int, @informationContentID int
DECLARE @neweventContentID int, @newcontactContentID int, @newlocationContentID int, @newcancelContentID int, @newtravelContentID int, @newinformationContentID int
DECLARE @languageID int, @isSSL bit, @isHTML bit, @contentTitle varchar(200), @contentDesc varchar(400), @rawcontent varchar(max)
DECLARE @emailContactContent bit, @emailLocationContent bit, @emailCancelContent bit, @emailTravelContent	bit
DECLARE @registrationID int, @registrationTypeID int, @startDate datetime, @endDate datetime, @registrantCap int,
	@ReplyToEmail varchar(200), @notifyEmail varchar(200), @isPriceBasedOnActual bit, @bulkCountByRate bit, @newregistrationID int
DECLARE @expirationContentID int, @registrantCapContentID int
DECLARE @newexpirationContentID int, @newregistrantCapContentID int
DECLARE @minofferingID int, @newofferingID int
DECLARE @minRateId int, @rateGroupingID int, @rateGLAccountID int, @rateName varchar(100),
	@rate money, @ratestartDate datetime, @rateendDate datetime, @newRateID int, @newRatesiteResourceID int,
	@ratereportCode varchar(15), @rateQty int
DECLARE @minBulkRateID int, @bulkrate money, @bulksiteResourceID int, @bulkrateqty int, @newBulkRateID int,
	@newBulkRatesiteResourceID int, @bulkresourceRightID int
DECLARE @siteResourceID int, @newrateGroupingID int
DECLARE @srr_rightsID int, @srr_roleid int, @srr_functionID int, @srr_groupid int, @srr_memberid int, @srr_include bit, @srr_inheritedRightsResourceID int, @srr_inheritedRightsFunctionID int, @resourceRightID int
DECLARE @minCustomID int, @newCustomID int, @showCredit bit
DECLARE @isOnlineMeeting bit, @onlineEmbedCode varchar(max), @onlineEmbedOverrideLink varchar(400), @onlineEnterStartTime datetime, @onlineEnterEndTime datetime
SELECT @newEventID = null

BEGIN TRAN

-- get the event we are copying
SELECT @siteID=siteID, @eventTypeID=eventTypeID, 
	@lockTimeZoneID=lockTimeZoneID, @isAllDayEvent=isAllDayEvent, @altRegistrationURL=altRegistrationURL,
	@GLAccountID=GLAccountID, @status=status, @reportCode=reportcode, @internalNotes=internalNotes,
	@emailContactContent = emailContactContent, @emailLocationContent = emailLocationContent,
	@emailCancelContent = emailCancelContent, @emailTravelContent = emailTravelContent
	FROM dbo.ev_events
	WHERE eventID = @eventID
	IF @@ERROR <> 0 GOTO on_error
SELECT TOP 1 @calendarID = calendarID
	FROM dbo.ev_calendarEvents
	WHERE calendarID = sourceCalendarID
	AND sourceEventID = @eventID
	IF @@ERROR <> 0 GOTO on_error

-- create event
EXEC @rc = dbo.ev_createEvent @siteID=@siteID, @calendarID=@calendarID, @eventTypeID=@eventTypeID, 
	@enteredByMemberID=@copiedByMemberID, @lockTimeZoneID=@lockTimeZoneID, @isAllDayEvent=@isAllDayEvent, 
	@altRegistrationURL=@altRegistrationURL, @status=@status, @reportCode=@reportCode, 
	@emailContactContent=@emailContactContent, @emailLocationContent=@emailLocationContent,
	@emailCancelContent=@emailCancelContent, @emailTravelContent=@emailTravelContent,
	@eventID=@newEventID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

update dbo.ev_events
set GLAccountID = @GLAccountID,
	internalNotes = @internalNotes
where eventID = @newEventID
	IF @@ERROR <> 0 GOTO on_error

select @neweventContentID=eventContentID, @newcontactContentID=contactContentID, @newlocationContentID=locationContentID,
	@newcancelContentID=cancellationPolicyContentID, @newtravelContentID=travelContentID, @newinformationContentID=informationContentID
FROM dbo.ev_events
where eventID = @newEventID
	IF @@ERROR <> 0 GOTO on_error

select @eventContentID=eventContentID, @contactContentID=contactContentID, @locationContentID=locationContentID,
	@cancelContentID=cancellationPolicyContentID, @travelContentID=travelContentID, @informationContentID=informationContentID
FROM dbo.ev_events
where eventID = @EventID
	IF @@ERROR <> 0 GOTO on_error

-- make event inactive
UPDATE dbo.ev_events
SET [status] = 'I'
WHERE eventID = @newEventID
	IF @@ERROR <> 0 GOTO on_error

-- copy event category
INSERT INTO dbo.ev_eventcategories (eventID, categoryID)
select @newEventID, categoryID
from dbo.ev_eventcategories
where eventID = @eventID
	IF @@ERROR <> 0 GOTO on_error

-- copy event times
INSERT INTO dbo.ev_Times (eventid, timeZoneID, startTime, endTime)
select @newEventID, timeZoneID, startTime, endTime
from dbo.ev_times
where eventid = @eventID
	IF @@ERROR <> 0 GOTO on_error

-- copy content objects
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle='Copy of ' + contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@eventContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@neweventContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@contactContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newcontactContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@locationContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newlocationContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@cancelContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newcancelContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@travelContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newtravelContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@informationContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newinformationContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- copy sponsors
insert into dbo.ev_sponsors (eventID, sponsorContentID, sponsorOrder)
select @newEventID, sponsorContentID, sponsorOrder
from dbo.ev_sponsors
where eventID = @eventID
	IF @@ERROR <> 0 GOTO on_error

-- does orig event have registration?
SELECT @registrationID=registrationid, @registrationTypeID=registrationTypeID, @startDate=startdate, 
	@endDate=endDate, @registrantCap=registrantCap, @ReplyToEmail=ReplyToEmail, @notifyEmail=notifyEmail, 
	@isPriceBasedOnActual=isPriceBasedOnActual, @bulkCountByRate=bulkCountByRate, @expirationContentID=expirationContentID, 
	@registrantCapContentID=registrantCapContentID, @isOnlineMeeting=isOnlineMeeting, @onlineEmbedCode=onlineEmbedCode,
	@onlineEmbedOverrideLink=onlineEmbedOverrideLink, @onlineEnterStartTime=onlineEnterStartTime, @onlineEnterEndTime=onlineEnterEndTime,
	@showCredit=showCredit
	from dbo.ev_registration
	where eventID = @eventID
	and [status] <> 'D'
IF @registrationID is not null BEGIN

	-- insert registration
	EXEC @rc = dbo.ev_createRegistration @eventID=@newEventID, @registrationTypeID=@registrationTypeID, 
			@startDate=@startDate, @endDate=@endDate, @registrantCap=@registrantCap, @ReplyToEmail=@ReplyToEmail,
			@notifyEmail=@notifyEmail, @isPriceBasedOnActual=@isPriceBasedOnActual, @bulkCountByRate=@bulkCountByRate,
			@registrationID=@newregistrationID OUTPUT
		IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

	select @newexpirationContentID=expirationContentID, @newregistrantCapContentID=registrantCapContentID
	FROM dbo.ev_registration
	where registrationID = @newregistrationID
		IF @@ERROR <> 0 GOTO on_error

	-- copy content objects
	select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@expirationContentID,1)
	EXEC @rc = dbo.cms_updateContent @contentID=@newexpirationContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
		IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
	select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@registrantCapContentID,1)
	EXEC @rc = dbo.cms_updateContent @contentID=@newregistrantCapContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
		IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

	-- if reg type
	IF @registrationTypeID = 1 BEGIN

		-- other registration fields
		update dbo.ev_registration
		set isOnlineMeeting = @isOnlineMeeting,
			onlineEmbedCode = @onlineEmbedCode,
			onlineEmbedOverrideLink = @onlineEmbedOverrideLink,
			onlineEnterStartTime = @onlineEnterStartTime,
			onlineEnterEndTime = @onlineEnterEndTime,
			showCredit = @showCredit
		where registrationID = @newregistrationID
			IF @@ERROR <> 0 GOTO on_error

		-- merchant profiles
		insert into dbo.ev_registrationMerchantProfiles (registrationID, profileID)
		select @newregistrationID, rmp.profileID
		from dbo.ev_registrationMerchantProfiles as rmp
		inner join dbo.mp_profiles as mp on mp.profileID = rmp.profileID
		inner join dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
		where rmp.registrationID = @registrationID
		and mp.status = 'A'
		and mp.allowPayments = 1
		and g.isActive = 1
			IF @@ERROR <> 0 GOTO on_error

		-- credit offered
		select @minofferingID = min(offeringID) from dbo.crd_offerings where eventID = @eventID
		while @minofferingID is not null BEGIN
			INSERT INTO dbo.crd_offerings (ASID, statusID, ApprovalNum, offeredStartDate, offeredEndDate, completeByDate, isCreditRequired, isIDRequired, isCreditDefaulted, eventID)
			SELECT ASID, statusID, ApprovalNum, offeredStartDate, offeredEndDate, completeByDate, isCreditRequired, isIDRequired, isCreditDefaulted, @newEventID
			FROM dbo.crd_offerings
			WHERE offeringID = @minofferingID
				IF @@ERROR <> 0 GOTO on_error
				SELECT @newofferingID = SCOPE_IDENTITY()

			INSERT INTO dbo.crd_offeringTypes (offeringID, ASTID, creditValue)
			SELECT @newofferingID, ASTID, creditValue
			FROM dbo.crd_offeringTypes
			WHERE offeringID = @minofferingID
				IF @@ERROR <> 0 GOTO on_error

			select @minofferingID = min(offeringID) from dbo.crd_offerings where eventID = @eventID and offeringID > @minofferingID
		END

		-- rate groupings
		insert into dbo.ev_rateGrouping (rateGrouping, registrationID, rateGroupingOrder)
		select rateGrouping, @newregistrationID, rateGroupingOrder
		from dbo.ev_rateGrouping
		where registrationID = @registrationID
			IF @@ERROR <> 0 GOTO on_error

		-- active rates and permissions
		select @minRateId = min(r.rateID) 
			from dbo.ev_rates as r
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			where r.registrationID = @registrationID
			and r.parentRateID is null
		while @minRateID is not null BEGIN
			select @rateGroupingID = null, @rateGLAccountID = null, @rateName = null, 
				@ratereportCode = null, @rate = null, @ratestartDate = null, @rateendDate = null, 
				@siteResourceID = null, @rateqty = null

			select @rateGroupingID=rateGroupingID, @rateGLAccountID=GLAccountID, @rateName=rateName, 
				@ratereportCode=reportCode, @rate=rate, @ratestartDate=startDate, @rateendDate=endDate, 
				@siteResourceID=siteResourceID, @rateqty=bulkQty
			from dbo.ev_rates 
			where rateID = @minRateID

			select @newrateGroupingID = rg1.rateGroupingID
			from dbo.ev_rateGrouping as rg1
			inner join dbo.ev_rateGrouping as rg2 
				on rg2.rateGrouping = rg1.rateGrouping
				and rg1.registrationID = @newregistrationID
				and rg2.registrationID = @registrationID
				and isnull(rg2.rateGroupingID,0) = isnull(@rateGroupingID,0)
			
			IF @rateName is not null and @rate is not null BEGIN
				EXEC @rc = dbo.ev_createRate @registrationID=@newregistrationID,  
					@rateGroupingID=@newrateGroupingID, @GLAccountID=@rateGLAccountID, @rateName=@rateName, 
					@reportCode=@ratereportCode, @rate=@rate, @startDate=@ratestartDate, @endDate=@rateendDate,
					@parentRateID=null, @qty=@rateqty, 
					@rateID=@newRateID OUTPUT, @siteResourceID=@newRatesiteResourceID OUTPUT
					IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

				select @minBulkRateID = min(rateID)
				from dbo.ev_rates
				where parentRateID = @minRateID

				while @minBulkRateID is not null begin
					select @bulkrate=rate, @bulksiteResourceID=siteResourceID, @bulkrateqty=bulkQty
					from dbo.ev_rates 
					where rateID = @minBulkRateID

					EXEC @rc = dbo.ev_createRate @registrationID=@newregistrationID,  
						@rateGroupingID=@newrateGroupingID, @GLAccountID=@rateGLAccountID, @rateName=@rateName, 
						@reportCode=@ratereportCode, @rate=@rate, @startDate=@ratestartDate, @endDate=@rateendDate,
						@parentRateID=@newRateID, @qty=@bulkrateqty, 
						@rateID=@newBulkRateID OUTPUT, @siteResourceID=@newBulkRatesiteResourceID OUTPUT
						IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

					select @minBulkRateID = min(rateID)
					from dbo.ev_rates
					where parentRateID = @minRateID
					and rateID > @minBulkRateID
				end

				-- copy resource rights for this resource		
				SELECT @srr_rightsID = null	
				SELECT @srr_rightsID = min(resourceRightsID) from dbo.cms_siteResourceRights where resourceID = @siteResourceID
				WHILE @srr_rightsID IS NOT NULL BEGIN
					SELECT @srr_roleid=roleID, @srr_functionID=functionID, @srr_groupid=groupID, 
						@srr_memberid=memberID, @srr_include=[include], @srr_inheritedRightsResourceID=inheritedRightsResourceID, 
						@srr_inheritedRightsFunctionID=inheritedRightsFunctionID
					FROM dbo.cms_siteResourceRights
					WHERE resourceRightsID = @srr_rightsID

					EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@newRatesiteResourceID, @include=@srr_include, 
						@functionID=@srr_functionID, @roleID=@srr_roleid, @groupID=@srr_groupid, @memberID=@srr_memberid, 
						@inheritedRightsResourceID=@srr_inheritedRightsResourceID, @inheritedRightsFunctionID=@srr_inheritedRightsFunctionID, 
						@resourceRightID=@resourceRightID OUTPUT
					IF @@ERROR <> 0 GOTO on_error

					select @minBulkRateID = min(rateID)
					from dbo.ev_rates
					where parentRateID = @newRateID

					while @minBulkRateID is not null begin
						select @bulksiteResourceID=siteResourceID
						from dbo.ev_rates 
						where rateID = @minBulkRateID

						EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@bulksiteResourceID, @include=@srr_include, 
							@functionID=@srr_functionID, @roleID=@srr_roleid, @groupID=@srr_groupid, @memberID=@srr_memberid, 
							@inheritedRightsResourceID=@srr_inheritedRightsResourceID, @inheritedRightsFunctionID=@srr_inheritedRightsFunctionID, 
							@resourceRightID=@bulkresourceRightID OUTPUT
						IF @@ERROR <> 0 GOTO on_error

						select @minBulkRateID = min(rateID)
						from dbo.ev_rates
						where parentRateID = @minRateID
						and rateID > @newRateID
					end
		
					SELECT @srr_rightsID = min(resourceRightsID) from dbo.cms_siteResourceRights where resourceID = @siteResourceID and resourceRightsID > @srr_rightsID
				END

			END
			
			select @minRateId = min(rateID) 
				from dbo.ev_rates as r
				inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
				inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
					and srs.siteResourceStatusDesc = 'Active'
				where r.registrationID = @registrationID
				and r.parentRateID is null
				and rateID > @minRateID
		END

		-- custom questions
		SELECT @minCustomID = min(CustomID) FROM dbo.ev_registrationCustom WHERE registrationID = @registrationID
		WHILE @minCustomID is not null BEGIN
			INSERT INTO dbo.ev_registrationCustom (registrationID, areaID, FieldDesc, titleOnInvoice, customTypeID, IsRequired, RequiredMsg, fieldOrder, Status, amount, offerQTY, GLAccountID)
			SELECT	@newregistrationID, areaID, FieldDesc, titleOnInvoice, customTypeID, IsRequired, RequiredMsg, fieldOrder, Status, amount, offerQTY, GLAccountID
			FROM	dbo.ev_registrationCustom	
			WHERE	customID = @minCustomID
				IF @@ERROR <> 0 GOTO on_error
				SELECT @newCustomID = SCOPE_IDENTITY()
				
			INSERT INTO dbo.ev_customOptions(customID, optionDesc, optionOrder, status, amount)
			SELECT	@newCustomID, optionDesc, optionOrder, status, amount
			FROM	dbo.ev_customOptions
			WHERE	customID = @minCustomID
				IF @@ERROR <> 0 GOTO on_error			
	
			SELECT @minCustomID = min(CustomID) FROM dbo.ev_registrationCustom WHERE registrationID = @registrationID and CustomID > @minCustomID	
		END

	END

END

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @newEventID = 0
	RETURN -1
GO
ALTER PROC [dbo].[crd_getCreditsOfferedGrid]
@applicationType varchar(20),
@itemID int

AS

SELECT ec.offeringID, ec.statusid, cas.ASID, ec.ApprovalNum, ec.offeredStartDate, 
	ec.offeredEndDate, ec.completeByDate, ec.isCreditRequired, 
	ec.isIDRequired, ec.isCreditDefaulted, cstat.status, cas.authorityID,
	cas.sponsorID, ca.authorityName, cstat.statusOrder as bestCategory, ec.notes, 
	cast(isnull((
	select ECT.offeringTypeID, ECT.ASTID, ect.creditValue, isnull(ast.ovTypeName,cat.typeName) as creditType
	from dbo.crd_offeringTypes as ect
	inner join dbo.crd_authoritySponsorTypes as ast on ast.ASTID = ect.ASTID
	inner join dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
	where ect.offeringID = ec.offeringID
	order by 4, 3
	FOR XML AUTO, ROOT('creditTypes')
	),'<creditTypes/>') as xml) as offeredCreditTypes, cs.statementAppProvider, cas.creditMessage
FROM dbo.crd_offerings AS ec
INNER JOIN dbo.crd_statuses as cstat on cstat.statusID = ec.statusID
INNER JOIN dbo.crd_authoritySponsors as cas on cas.ASID = ec.ASID
INNER JOIN dbo.crd_authorities as ca on ca.authorityID = cas.authorityID
INNER JOIN dbo.crd_sponsors as cs on cs.sponsorID = cas.sponsorID
WHERE 
	case @applicationType 
		when 'Events' then ec.eventID
		when 'Store' then ec.productFormatID
		else null
		end = @itemID
ORDER BY ca.authorityName

RETURN 0
GO




