<cfsavecontent variable="local.SponsorSelectorJS">
	<cfoutput>
	<script language="javascript">
		function loadSponsors_#arguments.selectorID#() {
			let loadSponsorsResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					doloadSponsors_#arguments.selectorID#(r.arrincludedsponsors,r.arravailablesponsors,r.arrsponsorGroupings);
				} else {
					let reloadHTML = '<div class="text-center mt-5"><span class="d-block text-danger mb-2">Sorry, we were unable to load the data.</span><i class="fa-solid fa-rotate-right fa-2x cursor-pointer" onclick="loadSponsors_#arguments.selectorID#()"></i><span class="d-block">Reload</span></div>';
					$('##availSponsorWidgetContainer#arguments.selectorID#, ##incSponsorWidgetContainer#arguments.selectorID#').html(reloadHTML);
				}
			};

			$('##incSponsorWidgetContainer#arguments.selectorID#, ##availSponsorWidgetContainer#arguments.selectorID#').html(mca_getLoadingHTML());
			$('##SponsorsWidgetContainer#arguments.selectorID# .SponsorCountSpan').toggleClass('d-none', true);
			$('##SponsorsWidgetContainer#arguments.selectorID# .SponsorCountLoadingSpan').toggleClass('d-none', false);
			let objParams = { referenceType:'#arguments.referenceType#', referenceID:#arguments.referenceID# };
			TS_AJX('SPONSORS','getSponsorsWithGroupings',objParams,loadSponsorsResult,loadSponsorsResult,60000,loadSponsorsResult);
		}
		function doloadSponsors_#arguments.selectorID#(arrIncludedSponsors,arrAvailableSponsors,arrSponsorGroupings) {
			let availSponsorListSource = $('##mc_AvailSponsorList_#arguments.selectorID#').html().replace(/\n/g,'');
			let availSponsorTemplate = Handlebars.compile(availSponsorListSource);
			$('##availSponsorWidgetContainer#arguments.selectorID#').html(availSponsorTemplate({arrAvailableSponsors:arrAvailableSponsors}));
			mcActivateTooltip($('##availSponsorWidgetContainer#arguments.selectorID#'));

			// Enhanced data processing for hierarchical display
			let processedGroupings = arrSponsorGroupings.map(function(group) {
				let sponsorsInGroup = arrIncludedSponsors.filter(function(sponsor) {
					return sponsor.sponsorgroupingid === group.sponsorgroupingid;
				});
				return {
					...group,
					sponsorcount: sponsorsInGroup.length
				};
			});

			let ungroupedSponsors = arrIncludedSponsors.filter(function(sponsor) {
				return !sponsor.sponsorgroupingid || sponsor.sponsorgroupingid === 0;
			});

			let incSponsorSource = $('##mc_incSponsors_#arguments.selectorID#').html().replace(/\n/g,'');
			let incSponsorTemplate = Handlebars.compile(incSponsorSource);
			$('##incSponsorWidgetContainer#arguments.selectorID#').html(incSponsorTemplate({
				arrIncludedSponsors: arrIncludedSponsors,
				arrSponsorGroupings: processedGroupings,
				hasUngroupedSponsors: ungroupedSponsors.length > 0,
				ungroupedCount: ungroupedSponsors.length,
				referenceType: '#arguments.referenceType#',
				selectorID: '#arguments.selectorID#'
			}));
			mcActivateTooltip($('##incSponsorWidgetContainer#arguments.selectorID#'));
			<cfif arguments.referenceType EQ 'events'>
				onChangeSponsors_#arguments.selectorID#();
			</cfif>

			$('##SponsorsWidgetContainer#arguments.selectorID# .selSponsorCount').text(arrIncludedSponsors.length);
			$('##SponsorsWidgetContainer#arguments.selectorID# .availSponsorCount').text(arrAvailableSponsors.length);
			$('##SponsorsWidgetContainer#arguments.selectorID# .SponsorCountLoadingSpan').toggleClass('d-none', true);
			$('##SponsorsWidgetContainer#arguments.selectorID# .SponsorCountSpan').toggleClass('d-none', false);
			if (typeof window['disableSponsor_#arguments.selectorID#'] == 'function'){
				window['disableSponsor_#arguments.selectorID#'](arrIncludedSponsors.length);
			}
		}
		<cfif NOT arguments.readOnly>
			function editSponsor_#arguments.selectorID#(id) {
				MCModalUtils.showModal({
					isslideout: true,
					modaloptions: {
						backdrop: 'static',
						keyboard: false
					},
					size: 'lg',
					title: (id == 0 ? 'Add' : 'Edit') + ' Sponsor' ,
					iframe: true,
					contenturl: '#local.editSponsorLink#&sponsorID=' + id,
					strmodalfooter: {
						classlist: 'd-flex',
						showclose: true,
						showextrabutton: true,
						extrabuttonclass: 'btn-primary ml-auto',
						extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.saveSponsorForm',
						extrabuttonlabel: (id == 0 ? 'Add' : 'Save') +  ' Sponsor',
					}
				});
			}
			function associateSponsor_#arguments.selectorID#(sid) {
				var associateSponsorResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true'){
						loadSponsors_#arguments.selectorID#();
					} else {
						alert('Unable to associate this sponsor.');
					}
				};
				let objParams = { sponsorID:sid, referenceType:'#arguments.referenceType#', referenceID:#arguments.referenceID# };
				TS_AJX('SPONSORS','associateSponsor',objParams,associateSponsorResult,associateSponsorResult,10000,associateSponsorResult);
			}
			function deassociateSponsor_#arguments.selectorID#(suid) {
				var deassociateSponsorResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true'){
						loadSponsors_#arguments.selectorID#();
						
					} else {
						alert('Unable to deassociate this sponsor.');
						delBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
					}
				};

				let delBtn = $('##btnDelSponsor#arguments.selectorID#_'+suid);
				mca_initConfirmButton(delBtn, function(){
					var objParams = { sponsorUsageID:suid, referenceType:'#arguments.referenceType#', referenceID:#arguments.referenceID# };
					TS_AJX('SPONSORS','deassociateSponsor',objParams,deassociateSponsorResult,deassociateSponsorResult,10000,deassociateSponsorResult);
				});
			}
			function moveSponsorUsageRow_#arguments.selectorID#(suid,dir) {
				var moveSponsorResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true'){
						mca_moveDataTableRow('sponsorUsageRow_'+suid,dir,'moveSponsorUp','moveSponsorDown');
						<cfif arguments.referenceType EQ 'events'>
							onChangeSponsors_#arguments.selectorID#();
						</cfif>
					}
				};
				var objParams = { sponsorUsageID:suid, referenceType:'#arguments.referenceType#', referenceID:#arguments.referenceID#, dir:dir };
				TS_AJX('SPONSORS','moveSponsor',objParams,moveSponsorResult,moveSponsorResult,10000,moveSponsorResult);
			}
			function moveSponsorWithinGroup_#arguments.selectorID#(sponsorUsageID, groupID, direction) {
				var moveResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
						loadSponsors_#arguments.selectorID#();
					} else {
						alert('Unable to move sponsor within group.');
					}
				};
				var objParams = {
					sponsorUsageID: sponsorUsageID,
					sponsorGroupingID: groupID,
					direction: direction,
					referenceType: '#arguments.referenceType#',
					referenceID: #arguments.referenceID#
				};
				TS_AJX('SPONSORS','moveSponsorWithinGroup',objParams,moveResult,moveResult,10000,moveResult);
			}
			function editSponsorImage_#arguments.selectorID#(sid,title,frmLink) {
				MCModalUtils.hideModal();
				$('##MCModal').on('hidden.bs.modal', function() {
					MCModalUtils.showModal({
						isslideout: true,
						modaloptions: {
							backdrop: 'static',
							keyboard: false
						},
						size: 'lg',
						title: title,
						iframe: true,
						contenturl: frmLink,
						strmodalfooter : {
							classlist: 'd-flex',
							showclose: true,
							showextrabutton: true,
							extrabuttonclass: 'btn-primary ml-auto',
							extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmFeaturedImage :submit").click',
							extrabuttonlabel: 'Upload',
							extrabuttoniconclass:'fa-light fa-file-arrow-up'
						}
					});
					$('##MCModal').on('hidden.bs.modal', function() { editSponsor_#arguments.selectorID#(sid); });
				});
			}
			// Sponsor Grouping Functions
			function editSponsorGrouping_#arguments.selectorID#(groupingID, currentName) {
				MCModalUtils.showModal({
					isslideout: true,
					size: 'md',
					title: (groupingID == 0 ? 'Add' : 'Edit') + ' Sponsor Group',
					iframe: true,
					contenturl: '#local.editSponsorGroupLink#&referenceType=#arguments.referenceType#&referenceID=#arguments.referenceID#&sponsorGroupingID=' + groupingID,
					strmodalfooter: {
						showclose: true,
						showextrabutton: true,
						extrabuttonclass: 'btn-primary',
						extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmEditSponsorGroup :submit").click()',
						extrabuttonlabel: 'Save Changes'
					}
				});
			}
			function moveSponsorGrouping_#arguments.selectorID#(groupingID, direction) {
				var moveGroupingResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
						loadSponsors_#arguments.selectorID#();
					} else {
						alert('Unable to move sponsor grouping.');
					}
				};
				var objParams = {
					sponsorGroupingID: groupingID,
					direction: direction,
					referenceType: '#arguments.referenceType#',
					referenceID: #arguments.referenceID#
				};
				TS_AJX('SPONSORS','moveSponsorGrouping',objParams,moveGroupingResult,moveGroupingResult,10000,moveGroupingResult);
			}
			function removeSponsorGrouping_#arguments.selectorID#(groupingID) {
				var removeGroupingResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
						loadSponsors_#arguments.selectorID#();
					} else {
						delBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
						alert('Unable to remove sponsor grouping. Make sure no sponsors are assigned to this grouping.');
					}
				};

				let delBtn = $('##btnRemoveGroup#arguments.selectorID#_'+groupingID);
				mca_initConfirmButton(delBtn, function(){
					var objParams = {
						sponsorGroupingID: groupingID,
						referenceType: '#arguments.referenceType#',
						referenceID: #arguments.referenceID#
					};
					TS_AJX('SPONSORS','deleteSponsorGrouping',objParams,removeGroupingResult,removeGroupingResult,10000,removeGroupingResult);
				});
			}
			// Modal callback functions
			function onSponsorGroupCreated_#arguments.selectorID#() {
				MCModalUtils.hideModal();
				loadSponsors_#arguments.selectorID#();
			}
			function onSponsorGroupUpdated_#arguments.selectorID#() {
				MCModalUtils.hideModal();
				loadSponsors_#arguments.selectorID#();
			}
			<cfif arguments.referenceType EQ 'events'>
				function onChangeSponsors_#arguments.selectorID#() {
					if (typeof confirmRecurringEventChanges != 'function') return false;

					let isInitEvSponsors = $('##incSponsorWidgetContainer#arguments.selectorID#').data('sponsorids') == null;
					let arrNewEvSponsorIDs = [], arrEvSponsors = [];

					$('tr.sponsorUsageRow').each(function() {
						arrNewEvSponsorIDs.push(Number($(this).data('sponsorid')));
					});

					if (isInitEvSponsors)
						arrEvSponsors = arrNewEvSponsorIDs;
					else if ($('##incSponsorWidgetContainer#arguments.selectorID#').data('sponsorids') != '')
						arrEvSponsors = $('##incSponsorWidgetContainer#arguments.selectorID#').data('sponsorids').split(',').map(Number);

					$('##incSponsorWidgetContainer#arguments.selectorID#').data('sponsorids',arrNewEvSponsorIDs.join(','));

					if (JSON.stringify(arrNewEvSponsorIDs) != JSON.stringify(arrEvSponsors))
						confirmRecurringEventChanges('sponsors');
				}
			</cfif>
		</cfif>
	
		$(function() {
			loadSponsors_#arguments.selectorID#();
		});
	</script>
	<style>
		##SponsorsWidgetContainer#arguments.selectorID# .btn-xs.btnSponsor { padding-right: 0.32rem; padding-left: 0.32rem; margin: 0.15rem }
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.SponsorSelectorJS)#">

<cfoutput>
<div id="SponsorsWidgetContainer#arguments.selectorID#" class="card card-box">
	<div class="card-header bg-light">
		<div class="card-header--title font-weight-bold font-size-sm">
			Selected Sponsors
		</div>
		<span class="SponsorCountSpan small d-none"><span class="selSponsorCount pr-1"></span>selected</span>
		<span class="SponsorCountLoadingSpan small">loading..</span>
	</div>
	<div class="card-body p-0">
		<div id="incSponsorWidgetContainer#arguments.selectorID#" style="height:#arguments.selectedSponsorsCardHeight#px;overflow-y:auto;"></div>
		<div class="accordion" id="availSponsorAccordion_#arguments.selectorID#">
			<div class="card card-box rounded-bottom shadow-none">
				<div class="card-header bg-light rounded-0" id="heading_#arguments.selectorID#">
					<button class="btn btn-link d-flex align-items-center justify-content-between font-size-sm collapsed" type="button" data-toggle="collapse" data-target="##collapse_#arguments.selectorID#" aria-expanded="false" aria-controls="collapse_#arguments.selectorID#">
						<span class="SponsorCountSpan d-none"><span class="availSponsorCount pr-1"></span>Additional Sponsors Available</span>
						<span class="SponsorCountLoadingSpan">loading..</span>
						<span>
							<i class="fa-solid fa-caret-up font-size-xl"></i>
						</span>
					</button>
				</div>
				<div id="collapse_#arguments.selectorID#" class="collapse" aria-labelledby="heading_#arguments.selectorID#" data-parent="##availSponsorAccordion_#arguments.selectorID#" style="">
					<div class="card">
						<div class="card-header py-2">
							<div class="card-header--title font-size-xs text-grey">
								Choose from the following Sponsors:
							</div>
							<div class="card-header--actions">
								<cfif NOT arguments.readOnly>
									<a href="##" name="btnCreateSponsor" onclick="editSponsor_#arguments.selectorID#(0);return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Create a Sponsor">
										<i class="fa-regular fa-circle-plus fa-lg"></i>
									</a>
								</cfif>
							</div>
						</div>
						<div class="card-body bg-secondary p-2" id="availSponsorWidgetContainer#arguments.selectorID#" style="height:250px;overflow-y:auto;"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<script id="mc_incSponsors_#arguments.selectorID#" type="text/x-handlebars-template">
	{{##if arrIncludedSponsors}}
		<!-- Hierarchical Sponsor Groups Display -->
		{{##each arrSponsorGroupings}}
			<div class="sponsor-group-section border-bottom">
				<!-- Group Header -->
				<div class="d-flex justify-content-between align-items-center py-2 px-3 bg-light border-bottom">
					<div class="d-flex align-items-center">
						<i class="fa-solid fa-layer-group text-primary mr-2"></i>
						<span class="font-weight-bold font-size-sm">{{sponsorgrouping}}</span>
						<span class="badge badge-secondary ml-2">{{sponsorcount}} sponsors</span>
					</div>
					<cfif NOT arguments.readOnly>
						<div class="btn-group" role="group">
							<a href="##" onclick="editSponsorGrouping_#arguments.selectorID#({{sponsorgroupingid}},'{{sponsorgrouping}}');return false;"
							   class="btn btn-xs btn-outline-primary" data-toggle="tooltip" title="Edit Group">
								<i class="fa-solid fa-pencil"></i>
							</a>
							{{##compare @index '!=' 0}}
								<a href="##" onclick="moveSponsorGrouping_#arguments.selectorID#({{sponsorgroupingid}},'up');return false;"
								   class="btn btn-xs btn-outline-dark" data-toggle="tooltip" title="Move Group Up">
									<i class="fa-solid fa-up"></i>
								</a>
							{{/compare}}
							{{##compare (math @index "+" 1) '!=' ../arrSponsorGroupings.length}}
								<a href="##" onclick="moveSponsorGrouping_#arguments.selectorID#({{sponsorgroupingid}},'down');return false;"
								   class="btn btn-xs btn-outline-dark" data-toggle="tooltip" title="Move Group Down">
									<i class="fa-solid fa-down"></i>
								</a>
							{{/compare}}
							<a href="##" id="btnRemoveGroup#arguments.selectorID#_{{sponsorgroupingid}}"
							   onclick="removeSponsorGrouping_#arguments.selectorID#({{sponsorgroupingid}});return false;"
							   class="btn btn-xs btn-outline-danger{{##if sponsorcount}} disabled{{/if}}"
							   data-toggle="tooltip" title="{{##if sponsorcount}}Cannot remove group with sponsors{{else}}Remove Group{{/if}}" data-confirm="0">
								<i class="fa-solid fa-trash-can"></i>
							</a>
						</div>
					</cfif>
				</div>

				<!-- Sponsors in this Group -->
				{{##each ../arrIncludedSponsors}}
					{{##compare sponsorgroupingid '==' ../sponsorgroupingid}}
						<div class="d-flex justify-content-between align-items-center py-2 px-4 border-bottom sponsor-row"
							 id="sponsorUsageRow_{{sponsorusageid}}" data-sponsorid="{{sponsorid}}" data-groupid="{{sponsorgroupingid}}">
							<div class="sponsor-info">
								<span class="font-size-sm">{{sponsorname}}</span>
							</div>
							<cfif NOT arguments.readOnly>
								<div class="btn-group" role="group">
									{{##compare @../index '!=' 0}}
										<a href="##" onclick="$(this).tooltip('hide');moveSponsorWithinGroup_#arguments.selectorID#({{sponsorusageid}},{{sponsorgroupingid}},'up');return false;"
										   class="btn btn-xs btn-outline-dark" data-toggle="tooltip" title="Move Sponsor Up">
											<i class="fa-solid fa-up"></i>
										</a>
									{{/compare}}
									{{##compare (math @../index "+" 1) '!=' ../../arrIncludedSponsors.length}}
										<a href="##" onclick="$(this).tooltip('hide');moveSponsorWithinGroup_#arguments.selectorID#({{sponsorusageid}},{{sponsorgroupingid}},'down');return false;"
										   class="btn btn-xs btn-outline-dark" data-toggle="tooltip" title="Move Sponsor Down">
											<i class="fa-solid fa-down"></i>
										</a>
									{{/compare}}
									<a href="##" onclick="$(this).tooltip('hide');editSponsor_#arguments.selectorID#({{sponsorid}});return false;"
									   class="btn btn-xs btn-outline-primary" data-toggle="tooltip" title="Edit Sponsor">
										<i class="fa-solid fa-pencil"></i>
									</a>
									<a href="##" id="btnDelSponsor#arguments.selectorID#_{{sponsorusageid}}"
									   onclick="$(this).tooltip('hide');deassociateSponsor_#arguments.selectorID#({{sponsorusageid}});return false;"
									   class="btn btn-xs btn-outline-danger" data-toggle="tooltip" title="Remove Sponsor" data-confirm="0">
										<i class="fa-solid fa-trash-can"></i>
									</a>
								</div>
							</cfif>
						</div>
					{{/compare}}
				{{/each}}
			</div>
		{{/each}}

		<!-- Ungrouped Sponsors Section -->
		{{##if hasUngroupedSponsors}}
			<div class="sponsor-group-section">
				<div class="d-flex justify-content-between align-items-center py-2 px-3 bg-light border-bottom">
					<div class="d-flex align-items-center">
						<i class="fa-solid fa-circle text-muted mr-2"></i>
						<span class="font-weight-bold font-size-sm text-muted">Ungrouped Sponsors</span>
						<span class="badge badge-light ml-2">{{ungroupedCount}} sponsors</span>
					</div>
				</div>

				{{##each arrIncludedSponsors}}
					{{##unless sponsorgroupingid}}
						<div class="d-flex justify-content-between align-items-center py-2 px-4 border-bottom sponsor-row"
							 id="sponsorUsageRow_{{sponsorusageid}}" data-sponsorid="{{sponsorid}}" data-groupid="0">
							<div class="sponsor-info">
								<span class="font-size-sm">{{sponsorname}}</span>
							</div>
							<cfif NOT arguments.readOnly>
								<div class="btn-group" role="group">
									{{##compare @../index '!=' 0}}
										<a href="##" onclick="$(this).tooltip('hide');moveSponsorWithinGroup_#arguments.selectorID#({{sponsorusageid}},0,'up');return false;"
										   class="btn btn-xs btn-outline-dark" data-toggle="tooltip" title="Move Sponsor Up">
											<i class="fa-solid fa-up"></i>
										</a>
									{{/compare}}
									{{##compare (math @../index "+" 1) '!=' ../../arrIncludedSponsors.length}}
										<a href="##" onclick="$(this).tooltip('hide');moveSponsorWithinGroup_#arguments.selectorID#({{sponsorusageid}},0,'down');return false;"
										   class="btn btn-xs btn-outline-dark" data-toggle="tooltip" title="Move Sponsor Down">
											<i class="fa-solid fa-down"></i>
										</a>
									{{/compare}}
									<a href="##" onclick="$(this).tooltip('hide');editSponsor_#arguments.selectorID#({{sponsorid}});return false;"
									   class="btn btn-xs btn-outline-primary" data-toggle="tooltip" title="Edit Sponsor">
										<i class="fa-solid fa-pencil"></i>
									</a>
									<a href="##" id="btnDelSponsor#arguments.selectorID#_{{sponsorusageid}}"
									   onclick="$(this).tooltip('hide');deassociateSponsor_#arguments.selectorID#({{sponsorusageid}});return false;"
									   class="btn btn-xs btn-outline-danger" data-toggle="tooltip" title="Remove Sponsor" data-confirm="0">
										<i class="fa-solid fa-trash-can"></i>
									</a>
								</div>
							</cfif>
						</div>
					{{/unless}}
				{{/each}}
			</div>
		{{/if}}

		<!-- Create Group Button -->
		<cfif NOT arguments.readOnly>
			<div class="text-center py-3">
				<a href="##" onclick="editSponsorGrouping_#arguments.selectorID#(0,'');return false;"
				   class="btn btn-sm btn-outline-success">
					<i class="fa-solid fa-plus"></i> Create Sponsor Group
				</a>
			</div>
		</cfif>
	{{else}}
		<div class="text-center py-3 text-dim">No Sponsors Selected.</div>
	{{/if}}
</script>
<script id="mc_AvailSponsorList_#arguments.selectorID#" type="text/x-handlebars-template">
	{{##if arrAvailableSponsors}}
		<ul class="list-group mt-2">
		{{##each arrAvailableSponsors}}
			<li class="list-group-item py-1">
				<div class="row no-gutters align-items-center">
					<div class="col font-size-sm">{{sponsorname}}</div>
					<div class="col-auto pl-2">
						<cfif NOT arguments.readOnly>
							<a href="##" id="btnAssociateSponsor_#arguments.selectorID#_{{sponsorid}}" onclick="$(this).tooltip('hide');associateSponsor_#arguments.selectorID#({{sponsorid}});return false;" class="btn btn-xs btn-outline-success btnSponsor" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Add Sponsor">
								<i class="fa-solid fa-plus"></i>
							</a>
							<a href="##" onclick="$(this).tooltip('hide');editSponsor_#arguments.selectorID#({{sponsorid}});return false;" class="btn btn-xs btn-outline-primary btnSponsor" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Edit this Sponsor">
								<i class="fa-solid fa-pencil"></i>
							</a>
						</cfif>
					</div>
				</div>
			</li>
		{{/each}}
		</ul>
	{{else}}
		<div class="text-center py-3 text-dim">No Sponsors Available.</div>
	{{/if}}
</script>
</cfoutput>