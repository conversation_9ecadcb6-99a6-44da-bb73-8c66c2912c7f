use seminarweb
GO

-- delete all sync points linked to deleted seminars
delete from dbo.tblSeminarAndFilesSyncPoints
where syncpointid in (
	select syncpointid
	from dbo.tblSeminarAndFilesSyncPoints as ssp
	inner join dbo.tblSeminars as s on s.seminarID = ssp.seminarID
	where s.isDeleted = 1
)
GO

ALTER PROC [dbo].[sw_deleteSeminar]
@seminarID int

AS

UPDATE dbo.tblSeminars 
SET isDeleted = 1, isPublished = 0
where seminarID = @seminarID

DECLARE @minTitleID int
select @minTitleID = min(titleID) from dbo.tblSeminarsAndTitles WHERE seminarID = @seminarID
WHILE @minTitleID is not null
BEGIN
	EXEC sw_deleteTitle @minTitleID
	select @minTitleID = min(titleID) from dbo.tblSeminarsAndTitles WHERE seminarID = @seminarID and titleID > @minTitleID
END

DELETE FROM dbo.tblSeminarsAndTitles
WHERE seminarID = @seminarID

DELETE FROM dbo.tblSeminarAndFilesSyncPoints
WHERE seminarID = @seminarID

GO

-- delete all sync points linked to deleted files
delete from dbo.tblSeminarAndFilesSyncPoints
where syncpointid in (
	select syncpointid
	from dbo.tblSeminarAndFilesSyncPoints as ssp
	inner join dbo.tblFiles as f on f.fileID = ssp.fileID
	where f.isDeleted = 1
)
GO
delete from dbo.tblSeminarAndFilesSyncPoints
where syncpointid in (
	select syncpointid
	from dbo.tblSeminarAndFilesSyncPoints as ssp
	inner join dbo.tblFiles as f on f.fileID = ssp.linkedfileID
	where f.isDeleted = 1
)
GO

ALTER PROC [dbo].[sw_deleteFile]
@fileID int

AS

UPDATE dbo.tblFiles
SET isDeleted = 1
where fileID = @fileID

delete from dbo.tblTitlesAndFiles
where fileID = @fileID

delete from dbo.tblSeminarsAndFiles
where fileID = @fileID

DELETE FROM dbo.tblSeminarAndFilesSyncPoints
where fileID = @fileID

DELETE FROM dbo.tblSeminarAndFilesSyncPoints
where linkedfileID = @fileID

GO

