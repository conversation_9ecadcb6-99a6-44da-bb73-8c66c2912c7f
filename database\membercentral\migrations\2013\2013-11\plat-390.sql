USE [memberCentral]
GO
CREATE PROC [dbo].[sub_firmBillingData]
	@siteID int,
	@firmMemberID int,
	@numMembers int
AS
	declare @orgID int
	declare @totalPriceTbl table (memberid int, subPrice money)
	declare @qryAllFirmMembers table (masterMemberID int, masterName varchar(1000), childMemberID int, childName varchar(1000), firstName varchar(250), lastName varchar(250), relationshipTypeName varchar(1000) )
	declare @qrySubs table (
		subscriberID int,
		GLAccountID int,
		memberID int,
		prefix varchar(50),
		firstname varchar(75),
		middlename varchar(25),
		lastname varchar(75),
		suffix varchar(50),
		memberName varchar(256),
		addressinfo varchar(1),
		company varchar(1),
		orgID int,
		subscriptionID int,
		typeID int,
		typeName varchar(100),
		subscriptionName varchar(300),
		rateName varchar(200),
		status varchar(1),
		paymentStatus varchar(1),
		subStartDate datetime,
		subEndDate datetime,
		graceEndDate datetime,
		parentSubscriberID int,
		rfid int,
		modifiedRate money,
		subPrice money,
		subActivationCode varchar(1),
		thePath varchar(max),
		thePathExpanded varchar(max),
		footNoteTxt varchar(max),
		footNoteID int,
		img_name varchar(36)
	)

	select @orgID = orgID from sites where siteID = @siteID

	insert into @qryAllFirmMembers
	select masterMemberID, masterName, childMemberID, childName, m.firstname, m.lastname, relationshipList 
	from (
		  select masterMemberID, masterName, childMemberID, childName,replace(dbo.pipeList(relationshipTypeName),'|',', ') as relationshipList, count(*) over (partition by masterMemberID) as masterCount
		  from (
				select distinct mMasterActive.memberID as masterMemberID, mChildActive.memberID as childMemberID,
					  case when rtMaster.isOrganization = 1 then mMasterActive.company else mMasterActive.lastName + ', ' + mMasterActive.firstName end as masterName,
					  case when rtChild.isOrganization = 1 then mChildActive.company else mChildActive.lastName + ', ' + mChildActive.firstName end as childName,
					  rrt.relationshipTypeName
				from dbo.ams_recordTypesRelationshipTypes rtrt
				inner join dbo.ams_recordRelationships rr on rr.recordTypeRelationshipTypeID = rtrt.recordTypeRelationshipTypeID
				inner join dbo.ams_recordRelationshipTypes rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID and rrt.orgID = @orgID
				inner join dbo.ams_recordTypes rtMaster on rtMaster.recordTypeID = rtrt.masterRecordTypeID  and rtMaster.orgID = @orgID
				inner join dbo.ams_recordTypes rtChild on rtChild.recordTypeID = rtrt.childRecordTypeID and rtChild.orgID = @orgID
				inner join dbo.ams_members mMaster on mMaster.memberID = rr.masterMemberID and mMaster.memberID = @firmMemberID and mMaster.orgID = @orgID
				inner join dbo.ams_members mMasterActive on mMasterActive.memberID = mMaster.memberID and mMasterActive.orgID = @orgID and mMasterActive.status in ('A','I')
				inner join dbo.ams_members mChild on mChild.memberID = rr.childMemberID and mChild.orgID = @orgID
				inner join dbo.ams_members mChildActive on mChildActive.memberID = mChild.activeMemberID and mChildActive.orgID = @orgID and mChildActive.status in ('A','I')
				where rtrt.isActive = 1
/*
				<cfif local.masterID neq 0>
				and rtrt.masterRecordTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.masterID#">
				</cfif>
				<cfif local.childID neq 0>
				and rtrt.childRecordTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.childID#">
				</cfif>
*/
				) x
		  group by masterName, childName, masterMemberID, childMemberID
	) as outertbl
	inner join ams_members m on m.memberid = outertbl.childMemberID
	where masterCount >= @numMembers
	order by masterName, childName

-- result 1: all members
	select * from @qryAllFirmMembers


; WITH subscribers AS (
 select subscriberID, GLAccountID, memberID, prefix, firstname, middlename, lastname, suffix,  memberName, '' as addressinfo, orgID, subscriptionID, typeID, typeName, subscriptionName, rateName, [status], paymentStatus,
  subStartDate, subEndDate, graceEndDate, parentSubscriberID, rfid, modifiedRate, subPrice, subActivationCode,
  CONVERT(varchar, memberID) + '.' + CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
  CAST(subscriptionName as varchar(max)) as thePathExpanded
 FROM (
	 select s.subscriberID, s.GLAccountID , s.memberID,
		m.prefix, m.firstname, m.middlename, m.lastname, m.suffix,
	  RTRIM(mActive.lastname + ' ' + isnull(mActive.suffix, '')) + ', ' + mActive.firstname + isnull(' (' + mActive.membernumber + ')','') AS memberName, m.orgID,
	  s.subscriptionID, t.typeID, t.typeName, sc.subscriptionName, r.rateName, st.statusCode as status, pst.statusCode as paymentStatus,
	  s.subStartDate, s.subEndDate, s.graceEndDate, s.parentSubscriberID, s.rfid, isnull(s.modifiedRate,0) as modifiedRate, coalesce(s.modifiedRate, lastPrice, cast(0.00 as money)) as subPrice, o.subActivationCode,
	  ROW_NUMBER() OVER (ORDER BY mActive.lastname, mActive.firstname, mActive.memberID, s.parentSubscriberID, s.subStartDate, s.subscriberID) AS theRow
	 from dbo.sub_subscribers s
	 inner join dbo.sub_statuses st on st.statusID = s.statusID -- AND st.statusCode = 'O'
	 inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID
	 inner join dbo.sub_activationOptions o on o.subActivationID = s.subActivationID
	 inner join dbo.sub_subscriptions sc on sc.subscriptionID = s.subscriptionID
	 inner join dbo.sub_types t on t.typeID = sc.typeID and t.siteID = @siteID
	 inner join dbo.ams_members m on m.memberID = s.memberID
	 inner join dbo.ams_members mActive on mActive.memberID = m.activeMemberID
	 inner join @qryAllFirmMembers fm on fm.childMemberID = mActive.memberID

		inner join dbo.sub_rateFrequencies as rf on rf.rfid = s.rfid
		inner join dbo.sub_rates r on r.rateID = rf.rateID
	 where s.parentSubscriberID is null
	 ) as x
	 UNION ALL
	 select subscriberID, GLAccountID, memberID, prefix, firstname, middlename, lastname, suffix,
		 memberName, '' as addressinfo, orgID, subscriptionID, typeID, typeName, subscriptionName, rateName, [status], paymentStatus,
	  subStartDate, subEndDate, graceEndDate, parentSubscriberID, rfid, modifiedRate, subPrice, subActivationCode,
	  thePath + '.' + CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
	  thePathExpanded = case
	   	when isnull(parentSubscriberID,0) = 0 then subscriptionName
	   	else thePathExpanded + ' \ ' + subscriptionName
	  	end
	 FROM (
	 select s.subscriberID, s.GLAccountID, s.memberID,
		m.prefix, m.firstname, m.middlename, m.lastname, m.suffix,
	  RTRIM(mActive.lastname + ' ' + isnull(mActive.suffix, '')) + ', ' + mActive.firstname + isnull(' (' + mActive.membernumber + ')','') AS memberName, m.orgID,
	  s.subscriptionID, t.typeID, t.typeName, sc.subscriptionName, r.rateName, st.statusCode as status, pst.statusCode as paymentStatus,
	  s.subStartDate, s.subEndDate, s.graceEndDate, s.parentSubscriberID, s.rfid, isnull(s.modifiedRate,0) as modifiedRate, coalesce(s.modifiedRate, lastPrice, cast(0.00 as money)) as subPrice, o.subActivationCode,
	  scte.thePath, scte.thePathExpanded,
	  ROW_NUMBER() OVER (ORDER BY mActive.lastname, mActive.firstname, mActive.memberID, s.parentSubscriberID, s.subStartDate, s.subscriberID) AS theRow
	 from dbo.sub_subscribers s
	 inner join dbo.sub_statuses st on st.statusID = s.statusID -- AND st.statusCode = 'O'
	 inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID
	 inner join dbo.sub_activationOptions o on o.subActivationID = s.subActivationID
	 inner join dbo.sub_subscriptions sc on sc.subscriptionID = s.subscriptionID
	 inner join dbo.sub_types t on t.typeID = sc.typeID and t.siteID = @siteID
	 inner join dbo.ams_members m on m.memberID = s.memberID
	 inner join dbo.ams_members mActive on mActive.memberID = m.activeMemberID
	 INNER JOIN subscribers scte on s.parentSubscriberID = scte.subscriberID
	 inner join dbo.sub_rateFrequencies as rf on rf.rfid = s.rfid
	inner join dbo.sub_rates r on r.rateID = rf.rateID
	 ) as y
)
insert into @qrySubs
select 
	subscriberID, s.GLAccountID,
	memberID, 
	prefix, 
	s.firstname, s.middlename, s.lastname, suffix, memberName, addressinfo, '' as company, s.orgID, 
	subscriptionID, typeID, typeName, subscriptionName, rateName, s.[status], paymentStatus,
	subStartDate, subEndDate, graceEndDate, parentSubscriberID, rfid, modifiedRate, subPrice, subActivationCode,
	thePath, thePathExpanded
	, cv.rawContent as footNoteTxt, 0 as footNoteID
	, cast(a.invoiceProfileID as varchar) + '.' + ip.imageExt as img_name
from subscribers s
	INNER join @qryAllFirmMembers f on  
		s.memberID = f.childMemberID
	left outer join dbo.tr_GLAccounts a on
		a.GLAccountID = s.GLAccountID
	left outer  join cms_contentlanguages cl
		on cl.contentID = a.invoiceContentID
		and cl.languageID = 1
	left outer  join cms_contentVersions cv
		on cv.contentLanguageID = cl.contentLanguageID
		and cv.isactive  = 1 
	left outer join tr_invoiceProfiles ip on
		ip.profileID = a.invoiceProfileID				
order by s.memberName	

-- result 2: all subscriptions in billed state
select *
from @qrySubs 
where status = 'O'
order by lastname, firstname

-- result 3: all subscriptions summary totals
select subscriptionName, count(subscriptionName) as totalCount, sum(subPrice) as totalPrice
from @qrySubs
where status = 'O'
group by subscriptionName
order by subscriptionName

-- result 4: all members who do not have a subscription
select * 
from @qryAllFirmMembers
where childMemberID not in (
	select distinct memberID
	from @qrySubs
)

-- result 5: all members and their subtotals
select memberID, firstname, lastname, sum(subPrice) as totalPrice
from @qrySubs
where status = 'O'
group by memberID, firstname, lastname
order by lastname, firstname

RETURN 0
GO

CREATE PROC [dbo].[sub_firmBillingList]
	@siteID int,
	@numMembers int,
	@masterRecordTypeID int = null,
	@childRecordTypeID int = null,
	@subTypeID int = null,
	@subscriptionID int = null,
	@rateID int = null,
	@subStartDate datetime = null,
	@subEndDate datetime = null
AS
	declare @orgID int
	declare @totalPriceTbl table (memberid int, subPrice money)
	declare @qryAllFirmMembers table (masterMemberID int, masterName varchar(1000), childMemberID int, childName varchar(1000), firstName varchar(250), lastName varchar(250), relationshipTypeName varchar(1000) )
	select @orgID = orgID from sites where siteID = @siteID
	declare @qrySubs table (
		subscriberID int,
		memberID int,
		memberName varchar(256),
		subscriptionID int,
		typeID int,
		typeName varchar(100),
		subscriptionName varchar(300),
		status varchar(1),
		statusName varchar(50),
		subStartDate datetime,
		subEndDate datetime,
		graceEndDate datetime,
		parentSubscriberID int,
		rfid int,
		thePath varchar(max),
		thePathExpanded varchar(max),
		masterMemberID int,
		masterName varchar(1000),
		childMemberID int,
		childName varchar(1000),
		relationshipList varchar(1000)
	)

	insert into @qryAllFirmMembers
	select distinct masterMemberID, masterName, childMemberID, childName, m.firstname, m.lastname, relationshipList 
	from (
		  select masterMemberID, masterName, childMemberID, childName,replace(dbo.pipeList(relationshipTypeName),'|',', ') as relationshipList, count(*) over (partition by masterMemberID) as masterCount
		  from (
				select distinct mMasterActive.memberID as masterMemberID, mChildActive.memberID as childMemberID,
					  case when rtMaster.isOrganization = 1 then mMasterActive.company else mMasterActive.lastName + ', ' + mMasterActive.firstName end as masterName,
					  case when rtChild.isOrganization = 1 then mChildActive.company else mChildActive.lastName + ', ' + mChildActive.firstName end as childName,
					  rrt.relationshipTypeName
				from dbo.ams_recordTypesRelationshipTypes rtrt
				inner join dbo.ams_recordRelationships rr on rr.recordTypeRelationshipTypeID = rtrt.recordTypeRelationshipTypeID
				inner join dbo.ams_recordRelationshipTypes rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID and rrt.orgID = @orgID
				inner join dbo.ams_recordTypes rtMaster on rtMaster.recordTypeID = rtrt.masterRecordTypeID  and rtMaster.orgID = @orgID
				inner join dbo.ams_recordTypes rtChild on rtChild.recordTypeID = rtrt.childRecordTypeID and rtChild.orgID = @orgID
				inner join dbo.ams_members mMaster on mMaster.memberID = rr.masterMemberID and mMaster.orgID = @orgID
				inner join dbo.ams_members mMasterActive on mMasterActive.memberID = mMaster.memberID and mMasterActive.orgID = @orgID and mMasterActive.status in ('A','I')
				inner join dbo.ams_members mChild on mChild.memberID = rr.childMemberID and mChild.orgID = @orgID
				inner join dbo.ams_members mChildActive on mChildActive.memberID = mChild.activeMemberID and mChildActive.orgID = @orgID and mChildActive.status in ('A','I')
				where rtrt.isActive = 1
				and rtrt.masterRecordTypeID = isnull(@masterRecordTypeID, rtrt.masterRecordTypeID)
				and rtrt.childRecordTypeID = isnull(@childRecordTypeID, rtrt.childRecordTypeID)
				) x
		  group by masterName, childName, masterMemberID, childMemberID
	) as outertbl
	inner join ams_members m on m.memberid = outertbl.childMemberID
	where masterCount >= @numMembers
	order by masterName, childName


-- result 1: subscriptions for the firms selected
	insert into @qrySubs
	select subscriberID, memberID, memberName, subscriptionID, typeID, typeName, subscriptionName, [status], statusName,
		subStartDate, subEndDate, graceEndDate, parentSubscriberID, rfid, 
		CONVERT(varchar, memberID) + '.' + CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
		CAST(subscriptionName as varchar(max)) as thePathExpanded,
		masterMemberID, masterName, childMemberID, childName, relationshipTypeName as relationshipList
	FROM (
		select distinct s.subscriberID, s.memberID, RTRIM(activeMember.lastname + ' ' + isnull(activeMember.suffix, '')) + ', ' + activeMember.firstname + isnull(' (' + activeMember.membernumber + ')','') AS memberName,
		s.subscriptionID, t.typeID, t.typeName, sub.subscriptionName, st.statusCode as status, st.statusName, s.subStartDate, s.subEndDate, s.graceEndDate, s.rfid, s.parentSubscriberID,
		ROW_NUMBER() OVER (ORDER BY activeMember.lastname, activeMember.firstname, activeMember.memberID, s.parentSubscriberID, s.subStartDate, s.subscriberID) AS theRow
		from dbo.sub_subscribers s
			inner join sub_subscriptions sub 
				on sub.subscriptionID = s.subscriptionID
				and s.parentSubscriberID is null
				and s.subscriptionID = isnull(@subscriptionID, s.subscriptionID)
				and s.subStartDate >= isnull(@subStartDate, s.subStartDate)
				and s.subEndDate >= isnull(@subEndDate, s.subEndDate)
			inner join sub_types t
				on sub.typeiD = t.typeID and t.siteID = @siteID
				and t.typeID = isnull(@subTypeID, t.typeID)
			inner join dbo.ams_members m
				on s.memberID = m.memberID and m.orgID = @orgID
			inner join dbo.ams_members activeMember
				on activeMember.memberID = m.activeMemberID and activeMember.orgID = @orgID
			inner join @qryAllFirmMembers fm on fm.childMemberID = activeMember.memberID
			inner join dbo.sub_statuses st on st.statusID = s.statusID AND st.statusCode = 'O'
			inner join dbo.sub_rateFrequencies rf on rf.rfid = s.rfid
			inner join dbo.sub_rates r on r.rateID = rf.rateID 
				and r.rateID = isnull(@rateID, r.rateID)
		) as x
		inner join @qryAllFirmMembers fm on fm.childMemberID = x.memberID
		order by fm.masterName, fm.childName, thePath

-- result 1: subscriptions for the firms selected
	select * from @qrySubs

-- result 2: all firms which have billed subscriptions
	select distinct masterMemberID, MasterName from @qrySubs order by MasterName

RETURN 0
GO
