declare 
	@orgcode varchar(5),
	@siteCode varchar(5),
	@siteID int,
	@orgID int,
	@subenddate datetime

declare @subscriberIDs TABLE (subscriberID int PRIMARY KEY, statusHistoryID int,oldStatusID int)

set @orgcode = 'CO'
set @siteCode  ='CO'
set @orgID = dbo.fn_getOrgIDfromOrgCode(@siteCode)
set @siteID  = dbo.fn_getSiteIDfromSiteCode(@siteCode)


-- declare variable for this specific case
-- insert into temp table all subscriberIDs that need to have there most recent status change reversed

set @subenddate = '9/30/2015 23:59:59.997'

insert into @subscriberIDs (subscriberID,statusHistoryID,oldStatusID)
select ss.subscriberID, sh.statusHistoryID, sh.oldStatusID
from tr_applications ta
inner join sub_subscribers ss
	on ss.subscriberID = ta.itemID
	and ta.itemtype = 'Dues'
	and ta.status='a'
	and ta.transactionID in (3031750,3031751,3033031,3033032,3033033,3033034,3033035,3034506,3034505,3034504,3034503,3034502,3034501,3034500,3034499,3034498,3034497,3034496,3034495,3034494,3034493,3033228,3033227,3033226,3033225,3033224,3033223,3033222,30332213033220,3033219,3033218,3033578,3033577,3033576,3033575)
inner join sub_statusHistory sh
	on sh.subscriberID = ss.subscriberID
inner join sub_statuses st
	on st.statusID = sh.oldStatusID
	and st.statuscode in ('A')

update ss set
	statusID = sh.oldStatusID,
	ss.subenddate = @subenddate
from @subscriberIDs temp
inner join sub_subscribers ss
	on ss.subscriberID = temp.subscriberID
inner join sub_statusHistory sh
	on sh.statusHistoryID = temp.statusHistoryID


delete sh
from @subscriberIDs temp
inner join sub_statusHistory sh
	on sh.statusHistoryID = temp.statusHistoryID


exec dbo.sub_fixGroups @siteID