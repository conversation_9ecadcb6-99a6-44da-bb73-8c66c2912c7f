
	update subAppInstance set
		applicationInstanceName = 'Document Bank'	
	from dbo.cms_pages as p
	inner join dbo.cms_pageLanguages as pl on p.pageID = pl.pageID and pl.languageID = 1
	inner join dbo.comm_communities as c on c.communityID in (select communityid from comm_communities)
	inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = c.applicationInstanceID and ai.siteid = 154
	inner join dbo.cms_siteResources as pageResource on p.siteresourceID = pageResource.siteResourceID and pageResource.parentSiteResourceID = ai.siteResourceID
	inner join dbo.cms_siteResourceStatuses as pageResourceStatus on pageResourceStatus.siteResourceStatusID = pageResource.siteResourceStatusID and pageResourceStatus.siteResourceStatusDesc = 'Active'
	inner join dbo.cms_siteResourceTypes as pageResourceType on pageResource.resourceTypeID = pageResourceType.resourceTypeID
	inner join dbo.cms_siteResourceTypeClasses as pageResourceTypeClass on pageResourceTypeClass.resourceTypeClassID = pageResourceType.resourceTypeClassID and pageResourceTypeClass.resourceTypeClassName = 'Page'
	left outer join dbo.cms_siteResources as subAppResource 
		inner join dbo.cms_applicationInstances as subAppInstance on subAppInstance.siteResourceID = subAppResource.siteResourceID
		inner join dbo.cms_siteResourceStatuses as subAppResourceStatus on subAppResourceStatus.siteResourceStatusID = subAppResource.siteResourceStatusID and subAppResourceStatus.siteResourceStatusDesc = 'Active'
		inner join dbo.cms_siteResourceTypes as subAppResourceTypes on subAppResourceTypes.resourceTypeID = subAppResource.resourceTypeID
		inner join dbo.cms_siteResourceTypeClasses as subAppResourceTypeClasses on subAppResourceTypeClasses.resourceTypeClassID = subAppResourceTypes.resourceTypeClassID and subAppResourceTypeClasses.resourceTypeClassName = 'application'
	on subAppResource.parentSiteResourceID = pageResource.siteResourceID
	left outer join dbo.cms_siteResourceSortOrder as pageOrder on pageOrder.siteResourceID = pageResource.siteResourceID
where pl.pageTitle = 'Files'



	update pl set
		pageTitle = 'Document Bank'	
	from dbo.cms_pages as p
	inner join dbo.cms_pageLanguages as pl on p.pageID = pl.pageID and pl.languageID = 1
	inner join dbo.comm_communities as c on c.communityID in (select communityid from comm_communities)
	inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = c.applicationInstanceID and ai.siteid = 154
	inner join dbo.cms_siteResources as pageResource on p.siteresourceID = pageResource.siteResourceID and pageResource.parentSiteResourceID = ai.siteResourceID
	inner join dbo.cms_siteResourceStatuses as pageResourceStatus on pageResourceStatus.siteResourceStatusID = pageResource.siteResourceStatusID and pageResourceStatus.siteResourceStatusDesc = 'Active'
	inner join dbo.cms_siteResourceTypes as pageResourceType on pageResource.resourceTypeID = pageResourceType.resourceTypeID
	inner join dbo.cms_siteResourceTypeClasses as pageResourceTypeClass on pageResourceTypeClass.resourceTypeClassID = pageResourceType.resourceTypeClassID and pageResourceTypeClass.resourceTypeClassName = 'Page'
	left outer join dbo.cms_siteResources as subAppResource 
		inner join dbo.cms_applicationInstances as subAppInstance on subAppInstance.siteResourceID = subAppResource.siteResourceID
		inner join dbo.cms_siteResourceStatuses as subAppResourceStatus on subAppResourceStatus.siteResourceStatusID = subAppResource.siteResourceStatusID and subAppResourceStatus.siteResourceStatusDesc = 'Active'
		inner join dbo.cms_siteResourceTypes as subAppResourceTypes on subAppResourceTypes.resourceTypeID = subAppResource.resourceTypeID
		inner join dbo.cms_siteResourceTypeClasses as subAppResourceTypeClasses on subAppResourceTypeClasses.resourceTypeClassID = subAppResourceTypes.resourceTypeClassID and subAppResourceTypeClasses.resourceTypeClassName = 'application'
	on subAppResource.parentSiteResourceID = pageResource.siteResourceID
	left outer join dbo.cms_siteResourceSortOrder as pageOrder on pageOrder.siteResourceID = pageResource.siteResourceID
where pl.pageTitle = 'Files'