
DECLARE	@sitecode varchar(10);

declare @commList TABLE 
	(autoID int IDENTITY(1,1),
	pageName varchar(100),
	pageTitle varchar(200),
	pagedesc varchar(400),
	applicationInstanceName varchar(100),
	applicationInstanceDesc  varchar(100),
	sn<PERSON><PERSON>s varchar(100),
	needsMemberManager bit,
	needsLists bit,
	needsFiles bit,
	needsCalendar bit,
	needsAnnouncements bit,
	needsPhotos bit,
	needsVideos bit);

set @sitecode = 'sdcba';

insert into @commList (pageName,pageTitle,pagedesc,applicationInstanceName,applicationInstanceDesc,snAlias,needsMemberManager,needsLists,needsFiles,needsCalendar,needsAnnouncements,needsPhotos,needsVideos)
values ('JEECSubcommittee1','JEEC Subcommittee 1','JEEC Subcommittee 1','JEEC Subcommittee 1 App','JJEEC Subcommittee 1','sdcba-jeecsubcommittee1',1,1,1,1,1,0,0)
insert into @commList (pageName,pageTitle,pagedesc,applicationInstanceName,applicationInstanceDesc,snAlias,needsMemberManager,needsLists,needsFiles,needsCalendar,needsAnnouncements,needsPhotos,needsVideos)
values ('JEECSubcommittee2','JEEC Subcommittee 2','JEEC Subcommittee 2','JEEC Subcommittee 2 App','JJEEC Subcommittee 2','sdcba-jeecsubcommittee2',1,1,1,1,1,0,0)
insert into @commList (pageName,pageTitle,pagedesc,applicationInstanceName,applicationInstanceDesc,snAlias,needsMemberManager,needsLists,needsFiles,needsCalendar,needsAnnouncements,needsPhotos,needsVideos)
values ('JEECSubcommittee3','JEEC Subcommittee 3','JEEC Subcommittee 3','JEEC Subcommittee 3 App','JJEEC Subcommittee 3','sdcba-jeecsubcommittee3',1,1,1,1,1,0,0)
insert into @commList (pageName,pageTitle,pagedesc,applicationInstanceName,applicationInstanceDesc,snAlias,needsMemberManager,needsLists,needsFiles,needsCalendar,needsAnnouncements,needsPhotos,needsVideos)
values ('JEECSubcommittee4','JEEC Subcommittee 4','JEEC Subcommittee 4','JEEC Subcommittee 4 App','JJEEC Subcommittee 4','sdcba-jeecsubcommittee4',1,1,1,1,1,0,0)
insert into @commList (pageName,pageTitle,pagedesc,applicationInstanceName,applicationInstanceDesc,snAlias,needsMemberManager,needsLists,needsFiles,needsCalendar,needsAnnouncements,needsPhotos,needsVideos)
values ('SanDiegoLawyerEditorialBoard','San Diego Lawyer Editorial Board','JSan Diego Lawyer Editorial Board','San Diego Lawyer Editorial Board App','San Diego Lawyer Editorial Board','sdcba-lawyereditorialboard',1,1,1,1,1,0,0)

-- NO CHANGES BELOW THIS LINE
---------------------------------

DECLARE	@siteid int;

select @siteID = dbo.fn_getSiteIDFromSiteCode(@siteCode)


update @commList set
	pageName = replace(pageName,' ',''),
	snAlias = replace(snAlias,' ','')

if exists (select cl.snAlias from @commlist cl group by cl.snAlias having count(*) > 1)
	GOTO on_duplicateAliases

if exists (select cl.pageName from @commlist cl group by cl.pageName having count(*) > 1)
	GOTO on_duplicatePageNames

IF exists (
			select p.pageID
			from @commlist cl
				inner join cms_pages p
					on cl.pageName = p.pageName
					and p.siteID = @siteID)

GOTO on_takenPages


DECLARE
	@childSocialNetworkID int,
	@childSocialNetworkSiteResourceID int,
	@masterSocialNetworkID int,
	@masterSocialNetworkSiteResourceID int,
	@communityCenterAppInstanceID int,
	@languageID int,
	@sectionID int,
	@zoneID int,
	@pageTemplateID int,
	@subpageTemplateID int,
	@pageModeID int,
	@pgResourceTypeID int,
	@allowReturnAfterLogin bit,
	@isVisible bit,
	@viewFunction int,
	@applicationWidgetInstanceID int,
	@applicationWidgetInstanceSiteResourceID int,
	@commSiteResourceTypeID int,
	@zoneIDB int,
	@trashID int,
	@SharedAppCommunityManagerPoolRoleTypeID int,
	@poolTypeID int,
	@SNMasterInstancePoolRoleTypeID int,
	@SNChildInstancePoolRoleTypeID int,
	@SNPoolID int


select
	@isVisible = 1,
	@languageID =  dbo.fn_getLanguageID('en'),
	@zoneID = dbo.fn_getZoneID('Main'),
	@zoneIDB = dbo.fn_getZoneID('B'),
	@pageTemplateID=null,
	@subpageTemplateID=dbo.fn_getTemplateID(null,'communityThreeColumn'),
	@pageModeID=null,
	@pgResourceTypeID= dbo.fn_getResourceTypeID('ApplicationSubPage'),
	@allowReturnAfterLogin = 1







declare
	@thisAutoID int,
	@thispageName varchar(100),
	@thispageTitle varchar(200),
	@thispagedesc varchar(400),
	@thisapplicationInstanceName varchar(100),
	@thisapplicationInstanceDesc  varchar(100),
	@thissnAlias varchar(100),
	@thisNeedsMemberManager bit,
	@thisNeedsLists bit,
	@thisNeedsFiles bit,
	@thisNeedsCalendar bit,
	@thisNeedsAnnouncements bit,
	@thisNeedsPhotos bit,
	@thisNeedsVideos bit,

	@thisapplicationInstanceID int,
	@thissiteResourceID int,
	@thispageID int,
	@thisRootSectionID int,
	@subAppPageName varchar(100),
	@subAppPageTitle varchar(100),
	@subAppPageDesc varchar(100),
	@subAppResourceID int,
	@subAppApplicationInstanceID int,
	@subAppPageID int,
	@commHomePageID int,
	@recentFilesWidgetTypeID int,
	@recentPhotosWidgetTypeID int,
	@recentVideosWidgetTypeID int,
	@defaultGLAccountID int


DECLARE
	@viewFunctionID int,
	@participateFunctionID int,
	@SocialNetworkResourceTypeID int,
	@MemberManagerResourceTypeID int,
	@ListViewerResourceTypeID int,
	@EventsResourceTypeID int,
	@FileShareResourceTypeID int,
	@AnnouncementsResourceTypeID int,
	@VideoGalleryResourceTypeID int,
	@PhotoGalleryResourceTypeID int;

declare
	@fsAddDocumentsFunctionID int,
	@fsEditOwnMetadataFunctionID int,
	@fsReuploadOwnFunctionID int,
	@fsDeleteOwnFunctionID int,
	@fsAddSubFolderFunctionID int,
	@editOwnFunctionID int,
	@deleteOwnFunctionID int,
	@AddVideosFunctionID int,
	@rc int;

select @socialNetworkResourceTypeID = dbo.fn_getResourceTypeID('SocialNetwork')
select @MemberManagerResourceTypeID = srt.resourceTypeID  from cms_applicationTypes at inner join cms_siteResourceTypes srt on srt.resourceTypeID = at.resourceTypeID where applicationTypename = 'MemberManager'
select @ListViewerResourceTypeID = srt.resourceTypeID  from cms_applicationTypes at inner join cms_siteResourceTypes srt on srt.resourceTypeID = at.resourceTypeID where applicationTypename = 'ListViewer'
select @EventsResourceTypeID = srt.resourceTypeID  from cms_applicationTypes at inner join cms_siteResourceTypes srt on srt.resourceTypeID = at.resourceTypeID where applicationTypename = 'Events'
select @FileShareResourceTypeID = srt.resourceTypeID  from cms_applicationTypes at inner join cms_siteResourceTypes srt on srt.resourceTypeID = at.resourceTypeID where applicationTypename = 'FileShare'
select @AnnouncementsResourceTypeID = srt.resourceTypeID  from cms_applicationTypes at inner join cms_siteResourceTypes srt on srt.resourceTypeID = at.resourceTypeID where applicationTypename = 'Announcements'
select @VideoGalleryResourceTypeID = srt.resourceTypeID  from cms_applicationTypes at inner join cms_siteResourceTypes srt on srt.resourceTypeID = at.resourceTypeID where applicationTypename = 'VideoGallery'
select @PhotoGalleryResourceTypeID = srt.resourceTypeID  from cms_applicationTypes at inner join cms_siteResourceTypes srt on srt.resourceTypeID = at.resourceTypeID where applicationTypename = 'PhotoGallery'


select @recentPhotosWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationWidgetTypeName = 'recentPhotos'
select @recentVideosWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationWidgetTypeName = 'recentVideos'
select @recentFilesWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationWidgetTypeName = 'recentFiles'
select @commSiteResourceTypeID = dbo.fn_getResourceTypeID('Community')


select @participateFunctioniD = dbo.fn_getResourceFunctionID('Participate', @commSiteResourceTypeID)
select @viewFunctionID = dbo.fn_getResourceFunctionID('View', @commSiteResourceTypeID)


select @fsAddDocumentsFunctionID = dbo.fn_getResourceFunctionID('fsAddDocuments', @FileShareResourceTypeID)
select @fsEditOwnMetadataFunctionID = dbo.fn_getResourceFunctionID('fsEditOwnMetadata', @FileShareResourceTypeID)
select @fsReuploadOwnFunctionID = dbo.fn_getResourceFunctionID('fsReuploadOwn', @FileShareResourceTypeID)
select @fsDeleteOwnFunctionID = dbo.fn_getResourceFunctionID('fsDeleteOwn', @FileShareResourceTypeID)
select @fsAddSubFolderFunctionID = dbo.fn_getResourceFunctionID('fsAddSubFolder', @FileShareResourceTypeID)
select @editOwnFunctionID = dbo.fn_getResourceFunctionID('editOwn', @PhotoGalleryResourceTypeID)
select @deleteOwnFunctionID = dbo.fn_getResourceFunctionID('deleteOwn', @PhotoGalleryResourceTypeID)
select @AddVideosFunctionID = dbo.fn_getResourceFunctionID('AddVideos', @VideoGalleryResourceTypeID)

select 
	@defaultGLAccountID = GLAccountID
	FROM tr_glAccounts
	WHERE orgID = dbo.fn_getOrgIDFromSiteID(@siteID)
	and AccountName = 'Seminar Fees 2014'  /* This is what all the other calendars are set to */


IF @defaultGLAccountID is null
GOTO on_noGL

select @poolTypeID = dbo.fn_getResourcePoolTypeID ('SNConfig');
select @SNMasterInstancePoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'MasterInstance')
select @SNChildInstancePoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'ChildInstance')
select @SharedAppCommunityManagerPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'SharedAppCommunityManager')

/*
select
	@childSocialNetworkID = sn.socialNetworkID,
	@childSocialNetworkSiteResourceID = sr.siteResourceID,
	@sectionID = sn.rootSectionID,
	@masterSocialNetworkID = masterNetwork.socialNetworkID,
	@masterSocialNetworkSiteResourceID = masterSR.siteResourceID
from sn_socialNetworks sn
	inner join cms_applicationInstances ai
		on ai.applicationInstanceID = sn.applicationInstanceID
		and ai.siteID = @siteID
	inner join cms_siteResources sr
		on sr.siteResourceID = ai.siteResourceID
	inner join cms_siteResourceStatuses srs
		on sr.siteResourceStatusID = srs.siteResourceStatusID
		and srs.siteResourceStatusDesc = 'Active'
	inner join sn_socialNetworks masterNetwork
		on sn.masterSocialNetworkID = masterNetwork.socialNetworkID
	inner join cms_applicationInstances masterai
		on masterai.applicationInstanceID = masterNetwork.applicationInstanceID
	inner join cms_siteResources masterSR
		on masterSR.siteResourceID = masterai.siteResourceID
	inner join cms_siteResourceStatuses mastersrs
		on masterSR.siteResourceStatusID = mastersrs.siteResourceStatusID
		and mastersrs.siteResourceStatusDesc = 'Active'

select @communityCenterAppInstanceID = ai.applicationInstanceID
from dbo.fn_cms_getSiteResourcePoolRoles(@masterSocialNetworkSiteResourceID,'SNConfig','SharedAppCommunityManager') pr
	inner join cms_applicationInstances ai on pr.siteResourceID = ai.siteResourceID


IF (@childSocialNetworkID is null
	or @childSocialNetworkSiteResourceID is null
	or @sectionID is null
	or @masterSocialNetworkID is null
	or @masterSocialNetworkSiteResourceID is null)
GOTO on_SNConfigError

IF @communityCenterAppInstanceID is null
GOTO on_noCommCenter



IF exists (
			select snpageID
			from @commlist cl
				inner join sn_pages snp
					on cl.snAlias = snp.alias
					and snp.socialNetworkID = @masterSocialNetworkID)

GOTO on_takenAliases
*/

BEGIN TRAN
	select top 1 @thisAutoID = autoID from @commList order by autoID 
	while @thisAutoID is not null
	begin

		select
			@thispageName = pageName,
			@thispageTitle = pageTitle,
			@thispagedesc = pagedesc,
			@thisapplicationInstanceName = applicationInstanceName,
			@thisapplicationInstanceDesc = applicationInstanceDesc,
			@thissnAlias = snAlias,
			@thisNeedsMemberManager = NeedsMemberManager,
			@thisNeedsLists = NeedsLists,
			@thisNeedsFiles = NeedsFiles,
			@thisNeedsCalendar = NeedsCalendar,
			@thisNeedsPhotos = NeedsPhotos,
			@thisNeedsVideos = NeedsVideos,
			@thisNeedsAnnouncements = NeedsAnnouncements,
			@thisapplicationInstanceID = null,
			@thissiteResourceID = null,
			@thispageID = null
		from @commlist where autoID = @thisAutoID


		select @sectionID = sectionID
			from dbo.cms_pageSections
			where siteid = @siteID
			and sectionCode='root'



		exec @rc = dbo.cms_createApplicationInstanceCommunity

			@siteid=@siteID,
			@languageID=@languageID,
			@sectionID=@sectionID,
			@isVisible=1,
			@pageName=@thispageName,
			@pageTitle=@thispageTitle,
			@pagedesc=@thispagedesc,
			@zoneID=@zoneID,
			@pageTemplateID=@pageTemplateID,
			@subpageTemplateID=@subpageTemplateID,
			@pageModeID=@pageModeID,
			@pgResourceTypeID=@pgResourceTypeID,
			@pgParentResourceID=@childSocialNetworkSiteResourceID,
			@allowReturnAfterLogin=@allowReturnAfterLogin,
			@applicationInstanceName=@thisapplicationInstanceName,
			@applicationInstanceDesc=@thisapplicationInstanceDesc,
			@applicationInstanceID=@thisapplicationInstanceID OUTPUT,
			@siteResourceID=@thissiteResourceID OUTPUT,
			@pageID=@thispageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

		select @thisRootSectionID = rootSectionID
		from comm_communities where applicationInstanceID = @thisapplicationInstanceID

/*
		insert into sn_pages (pageID, socialNetworkID, alias, isSharedApp, applicationInstanceID)
		values (@thispageID, @masterSocialNetworkID, @thissnAlias, 0, @thisapplicationInstanceID)
*/
		update comm_communities
			set communityCenterAppInstanceID = @communityCenterAppInstanceID
		where applicationInstanceID = @thisapplicationInstanceID


		select @commHomePageID = p.pageID
		from comm_communities comm
			inner join cms_pages p
				on p.pageName = comm.defaultCommunityPageName
				and comm.rootSectionID = p.sectionID
			inner join cms_siteResources sr
				on sr.siteResourceID = p.siteResourceID
			inner join cms_siteResourceStatuses srs
				on sr.siteResourceStatusID = srs.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'

		if @thisNeedsMemberManager = 1
		BEGIN
			set @subAppPageName = @thispageName + 'MemberManager';
			set @subAppPageTitle = 'Members';

			EXEC @rc = dbo.cms_createApplicationInstanceMemberManager @siteid=@siteid, @languageID=@languageID, @sectionID=@thisRootSectionID, 
					@isVisible=@isVisible, @pageName=@subAppPageName, 
					@pageTitle=@subAppPageTitle, @pageDesc='', @zoneID=@zoneID, @pagetemplateid=null,@subPageTemplateID = null,
					@pageModeID=null, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID = @thissiteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
					@applicationInstanceName=@subAppPageTitle, @applicationInstanceDesc='', 
					@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
					@siteresourceID=@subAppResourceID OUTPUT, 
					@pageID=@subAppPageID OUTPUT
				IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			-- set the default action per MF; Set's the default view on membermanager.
			update dbo.cms_applicationInstances
			  set settingsXML = '<settings><setting name="defaultAction" value="SearchResults" /></settings>'
			where applicationInstanceID = @subAppApplicationInstanceID
				IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@thissiteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
		END


		if @thisNeedsLists = 1
		BEGIN
			set @subAppPageName = @thispageName + 'Lists';
			set @subAppPageTitle = 'List Viewer';

			EXEC @rc = dbo.cms_createApplicationInstanceListViewer @siteid=@siteid, @languageID=@languageID, @sectionID=@thisRootSectionID, 
					@isVisible=@isVisible,@pageName=@subAppPageName, 
					@pageTitle=@subAppPageTitle, @pageDesc='', @zoneID=@zoneID, @pagetemplateid=null,
					@pageModeID=null, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID = @thissiteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
					@applicationInstanceName=@subAppPageTitle, @applicationInstanceDesc='', 
					@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
					@siteresourceID=@subAppResourceID OUTPUT, 
					@pageID=@subAppPageID OUTPUT
				IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@thissiteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
		END


		if @thisNeedsFiles = 1
		BEGIN
			set @subAppPageName = @thispageName + 'Files';
			set @subAppPageTitle = 'Fileshare';

			EXEC @rc = dbo.cms_createApplicationInstanceFileShare @siteid=@siteid, @languageID=@languageID, @sectionID=@thisRootSectionID, 
					@isVisible=@isVisible,@pageName=@subAppPageName, 
					@pageTitle=@subAppPageTitle, @pageDesc='', @zoneID=@zoneID, @pagetemplateid=null,
					@pageModeID=null, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID = @thissiteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
					@applicationInstanceName=@subAppPageTitle, @applicationInstanceDesc='', 
					@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
					@siteresourceID=@subAppResourceID OUTPUT, 
					@pageID=@subAppPageID OUTPUT
				IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@thissiteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid,
				@siteResourceID=@subAppResourceID,
				@include=1, @functionID=@fsAddDocumentsFunctionID,
				@roleID=null, @groupID=null, @memberID=null,
				@inheritedRightsResourceID=@thissiteResourceID,
				@inheritedRightsFunctionID=@participateFunctioniD,
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid,
				@siteResourceID=@subAppResourceID,
				@include=1, @functionID=@fsEditOwnMetadataFunctionID,
				@roleID=null, @groupID=null, @memberID=null,
				@inheritedRightsResourceID=@thisSiteResourceID,
				@inheritedRightsFunctionID=@participateFunctioniD,
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid,
				@siteResourceID=@subAppResourceID,
				@include=1, @functionID=@fsReuploadOwnFunctionID,
				@roleID=null, @groupID=null, @memberID=null,
				@inheritedRightsResourceID=@thisSiteResourceID,
				@inheritedRightsFunctionID=@participateFunctioniD,
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid,
				@siteResourceID=@subAppResourceID,
				@include=1, @functionID=@fsAddSubFolderFunctionID,
				@roleID=null, @groupID=null, @memberID=null,
				@inheritedRightsResourceID=@thisSiteResourceID,
				@inheritedRightsFunctionID=@participateFunctioniD,
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid,
				@siteResourceID=@subAppResourceID,
				@include=1, @functionID=@fsDeleteOwnFunctionID,
				@roleID=null, @groupID=null, @memberID=null,
				@inheritedRightsResourceID=@thisSiteResourceID,
				@inheritedRightsFunctionID=@participateFunctioniD,
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error




			EXEC @rc = dbo.cms_createApplicationWidgetInstance
				@siteid=@siteID,
				@applicationInstanceID=@subAppApplicationInstanceID,
				@applicationWidgetTypeID=@recentFilesWidgetTypeID,
				@applicationWidgetInstanceName='Recent Files',
				@applicationWidgetInstanceDesc='Recent Files',
				@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
				@siteResourceID=@applicationWidgetInstanceSiteResourceID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createPageZoneResource
				@pageID=@commHomePageID, @zoneID=@zoneIDB, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
		END


		if @thisNeedsCalendar = 1
		BEGIN

			set @subAppPageName = @thispageName + 'Calendar';
			set @subAppPageTitle = 'Calendar and Events';
			EXEC @rc = dbo.cms_createApplicationInstanceEvents @siteid=@siteid, @languageID=@languageID, @sectionID=@thisRootSectionID, 
					@pageName=@subAppPageName, 
					@pageTitle=@subAppPageTitle, @pageDesc='', @zoneID=@zoneID, @pagetemplateid=null,
					@pageModeID=null, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID = @thissiteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
					@defaultGLAccountID=@defaultGLAccountID,
					@applicationInstanceName=@subAppPageTitle, @applicationInstanceDesc='', 
					@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
					@siteresourceID=@subAppResourceID OUTPUT, 
					@pageID=@subAppPageID OUTPUT
				IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@thissiteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
		END



		if @thisNeedsAnnouncements = 1
		BEGIN

			set @subAppPageName = @thispageName + 'Announcements';
			set @subAppPageTitle = 'Announcements';
			EXEC @rc = dbo.cms_createApplicationInstanceAnnouncements @siteid=@siteid, @languageID=@languageID, @sectionID=@thisRootSectionID, 
					@isVisible=@isVisible, @pageName=@subAppPageName, 
					@pageTitle=@subAppPageTitle, @pageDesc='', @zoneID=@zoneID, @pagetemplateid=null,
					@pageModeID=null, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID = @thissiteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
					@applicationInstanceName=@subAppPageTitle, @applicationInstanceDesc='', 
					@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
					@siteresourceID=@subAppResourceID OUTPUT, 
					@pageID=@subAppPageID OUTPUT
				IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@thissiteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
		END

		if @thisNeedsPhotos = 1
		BEGIN
			set @subAppPageName = @thispageName + 'Photos';
			set @subAppPageTitle = 'Photo Gallery';

			EXEC @rc = dbo.cms_createApplicationInstancePhotoGallery @siteid=@siteid, @languageID=@languageID, @sectionID=@thisRootSectionID, 
					@isVisible=@isVisible,@pageName=@subAppPageName, 
					@pageTitle=@subAppPageTitle, @pageDesc='', @zoneID=@zoneID, @pagetemplateid=null,
					@pageModeID=null, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID = @thissiteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
					@allowSubGalleries = 1,
					@applicationInstanceName=@subAppPageTitle, @applicationInstanceDesc='', 
					@creatorMemberID = 3,
					@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
					@siteresourceID=@subAppResourceID OUTPUT, 
					@pageID=@subAppPageID OUTPUT
				IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@thissiteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid,
				@siteResourceID=@subAppResourceID,
				@include=1, @functionID=@editOwnFunctionID,
				@roleID=null, @groupID=null, @memberID=null,
				@inheritedRightsResourceID=@thisSiteResourceID,
				@inheritedRightsFunctionID=@participateFunctioniD,
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid,
				@siteResourceID=@subAppResourceID,
				@include=1, @functionID=@deleteOwnFunctionID,
				@roleID=null, @groupID=null, @memberID=null,
				@inheritedRightsResourceID=@thisSiteResourceID,
				@inheritedRightsFunctionID=@participateFunctioniD,
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


			EXEC @rc = dbo.cms_createApplicationWidgetInstance
				@siteid=@siteID,
				@applicationInstanceID=@subAppApplicationInstanceID,
				@applicationWidgetTypeID=@recentPhotosWidgetTypeID,
				@applicationWidgetInstanceName='Recent Photos',
				@applicationWidgetInstanceDesc='Recent Photos',
				@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
				@siteResourceID=@applicationWidgetInstanceSiteResourceID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createPageZoneResource
				@pageID=@commHomePageID, @zoneID=@zoneIDB, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
		END

		if @thisNeedsVideos = 1
		BEGIN
			set @subAppPageName = @thispageName + 'Videos';
			set @subAppPageTitle = 'Video Gallery';

			EXEC @rc = dbo.cms_createApplicationInstanceVideoGallery @siteid=@siteid, @languageID=@languageID, @sectionID=@thisRootSectionID, 
					@isVisible=@isVisible,@pageName=@subAppPageName, 
					@pageTitle=@subAppPageTitle, @pageDesc='', @zoneID=@zoneID, @pagetemplateid=null,
					@pageModeID=null, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID = @thissiteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
					@allowSubGalleries = 1,
					@applicationInstanceName=@subAppPageTitle, @applicationInstanceDesc='', 
					@creatorMemberID = 3,
					@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
					@siteresourceID=@subAppResourceID OUTPUT, 
					@pageID=@subAppPageID OUTPUT
				IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@thissiteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid,
				@siteResourceID=@subAppResourceID,
				@include=1, @functionID=@editOwnFunctionID,
				@roleID=null, @groupID=null, @memberID=null,
				@inheritedRightsResourceID=@thisSiteResourceID,
				@inheritedRightsFunctionID=@viewFunctionID,
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid,
				@siteResourceID=@subAppResourceID,
				@include=1, @functionID=@deleteOwnFunctionID,
				@roleID=null, @groupID=null, @memberID=null,
				@inheritedRightsResourceID=@thisSiteResourceID,
				@inheritedRightsFunctionID=@participateFunctioniD,
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid,
				@siteResourceID=@subAppResourceID,
				@include=1, @functionID=@AddVideosFunctionID,
				@roleID=null, @groupID=null, @memberID=null,
				@inheritedRightsResourceID=@thisSiteResourceID,
				@inheritedRightsFunctionID=@participateFunctioniD,
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			EXEC @rc = dbo.cms_createApplicationWidgetInstance
				@siteid=@siteID,
				@applicationInstanceID=@subAppApplicationInstanceID,
				@applicationWidgetTypeID=@recentVideosWidgetTypeID,
				@applicationWidgetInstanceName='Recent Videos',
				@applicationWidgetInstanceDesc='Recent Videos',
				@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
				@siteResourceID=@applicationWidgetInstanceSiteResourceID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createPageZoneResource
				@pageID=@commHomePageID, @zoneID=@zoneIDB, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
		END

		-- create Help for community
		DECLARE @AppSubPageTypeID int, @siteResourceStatusID int, @commpgTemplateID int
		DECLARE @commpgName varchar(60), @commpgTitle varchar(210), @commpgPageID int, @communityPageID int
		DECLARE @userCreatedContentResourceTypeID int, @contentID int, @contentSiteResourceID int
		SELECT @AppSubPageTypeID = dbo.fn_getResourceTypeId('ApplicationSubPage')
		select @siteResourceStatusID = dbo.fn_getResourceStatusId('Active')
		set @subAppPageName = @thispageName + 'Help';		
		select @userCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('UserCreatedContent')
		EXEC @rc = dbo.cms_createPage @siteid=@siteid, @languageID=@languageID, @resourceTypeID=@AppSubPageTypeID, 
			@siteResourceStatusID=@siteResourceStatusID, @pgParentResourceID = @thissiteresourceID, @isVisible=@isVisible, @sectionID=@thisRootSectionID, 
			@ovTemplateID=NULL, @ovTemplateIDMobile=NULL, @ovModeID=NULL, @pageName=@subAppPageName, @pageTitle='Help & FAQ',
			@pageDesc=null, @keywords=null, @allowReturnAfterLogin=0, @inheritPlacements=1, @checkReservedNames=1, @pageID=@commpgPageID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @commpgPageID = 0 GOTO on_error

		EXEC @rc = dbo.cms_createContent @siteID=@siteID, @pageID=@commpgPageID, @zoneID=@zoneID, 
			@resourceTypeID=@userCreatedContentResourceTypeID, @siteResourceStatusID=@siteResourceStatusID,
			@isSSL=0, @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle='Help & FAQ', 
			@contentDesc='', 
			@rawContent='Welcome to this community. Click around and see what''s here.',
			@contentID=@contentID OUTPUT, @contentSiteResourceID=@contentSiteResourceID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

		-- create Guidelines for community
		set @subAppPageName = @thispageName + 'Guidelines';		
		select @userCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('UserCreatedContent')
		EXEC @rc = dbo.cms_createPage @siteid=@siteid, @languageID=@languageID, @resourceTypeID=@AppSubPageTypeID, 
			@siteResourceStatusID=@siteResourceStatusID, @pgParentResourceID = @thissiteresourceID, @isVisible=@isVisible, @sectionid=@thisRootSectionID, 
			@ovTemplateID=NULL, @ovTemplateIDMobile=NULL, @ovModeID=NULL, @pageName=@subAppPageName, @pageTitle='E-Community Guidelines',
			@pageDesc=null, @keywords=null, @allowReturnAfterLogin=0, @inheritPlacements=1, @checkReservedNames=1, @pageID=@commpgPageID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @commpgPageID = 0 GOTO on_error

		EXEC @rc = dbo.cms_createContent @siteID=@siteID, @pageID=@commpgPageID, @zoneID=@zoneID, 
			@resourceTypeID=@userCreatedContentResourceTypeID, @siteResourceStatusID=@siteResourceStatusID,
			@isSSL=0, @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle='Community Guidelines', 
			@contentDesc='', 
			@rawContent='Welcome to this community. Click around and see what''s here.',
			@contentID=@contentID OUTPUT, @contentSiteResourceID=@contentSiteResourceID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

		if exists (select autoID from @commList where autoid > @thisAutoID)
			select top 1 @thisAutoID = autoID from @commList where autoid > @thisAutoID order by autoid
		else
			select @thisAutoID = null
	END

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN

GOTO on_done

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN

	GOTO on_done

on_duplicateAliases:

	print 'Some aliases names are used more than once in your commlist';
	select cl.snAlias, 'used ' + cast(count(*) as varchar(100)) + ' time(s) in your commlist'
	from @commlist cl
	group by cl.snAlias
	having count(*) > 1

	GOTO on_done


on_takenAliases:

	print 'Some aliases you requested are already in use within this social network';
	select distinct cl.snAlias as aliasesAlreadyTaken, 'already in use within this social network'
	from @commlist cl
		inner join sn_pages snp
			on cl.snAlias = snp.alias
			and snp.socialNetworkID = @masterSocialNetworkID
	GOTO on_done

on_duplicatePageNames:

	print 'Some pageNames names are used more than once in your commlist';
	select cl.pageName, 'used ' + cast(count(*) as varchar(100)) + ' time(s) in your commlist'
	from @commlist cl
	group by cl.pageName
	having count(*) > 1

	GOTO on_done


on_SNConfigError:

	print 'No socialNetwork found';
	select 'No socialNetwork found'

	GOTO on_done





on_takenPages:

	print 'Some pagename you requested are already in use within this site';
	select distinct cl.pagename as pagesAlreadyTaken, 'already in use within this site'
	from @commlist cl
		inner join cms_pages p
			on cl.pagename = p.pagename
			and p.siteID = @siteID
	GOTO on_done


on_noCommCenter:

	print 'No Community Center found for Master Social Network';
	select 'No Community Center found for Master Social Network'

	GOTO on_done


on_noGL:
	print 'No default GL Account found';
	select 'No default GL Account found'

	GOTO on_done


on_done: