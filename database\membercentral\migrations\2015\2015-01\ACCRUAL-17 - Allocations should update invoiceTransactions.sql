use membercentral
GO
ALTER FUNCTION [dbo].[fn_tr_getAllocatedAmountofSale] (
	@saleTransactionID int -- can be either sale, tax, deferred
) 
RETURNS TABLE WITH SCHEMABINDING
AS
RETURN (

	WITH saleAndAdj AS (
		select @saleTransactionID as saleTransactionID, tSale.transactionID, tSale.statusID
		from dbo.tr_transactions as tSale
		inner join dbo.tr_transactionSales as ts on ts.transactionID = tSale.transactionID
			and tSale.transactionID = @saleTransactionID
			union
		select @saleTransactionID as saleTransactionID, adjInner.transactionID, adjInner.statusID
		from dbo.tr_transactions as adjInner
		inner join dbo.tr_relationships as rInner on rInner.transactionID = adjInner.transactionID and rInner.appliedToTransactionID = @saleTransactionID
		inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
		where adjInner.statusID = 1
	), ActiveAlloc AS (
		select saleTransactionID, sum(allocAmt) as allocAmt
		from (
			select saleAndAdj.saleTransactionID, 
				case 
				when saleAndAdj.statusID IN (2,4) then 0 
				else CASE 
					WHEN glAllocDeb.GLCode = 'ACCOUNTSRECEIVABLE' then tAlloc.amount*-1
					ELSE tAlloc.amount
					END
				end as allocAmt
			from saleAndAdj
			inner join dbo.tr_relationships as rS on rS.appliedToTransactionID = saleAndAdj.transactionID
			inner join dbo.tr_relationshipTypes as rtS on rtS.typeID = rS.typeID and rtS.type = 'AllocSaleTrans'
			inner join dbo.tr_transactions as tAlloc on tAlloc.transactionID = rS.transactionID and tAlloc.statusID = 1
			inner join dbo.tr_GLAccounts as glAllocDeb on glAllocDeb.GLAccountID = tAlloc.debitGLAccountID
			inner join dbo.tr_relationships as rP on rP.transactionID = tAlloc.transactionID
			inner join dbo.tr_relationshipTypes as rtP on rtP.typeID = rP.typeID and rtP.type = 'AllocPayTrans'
			inner join dbo.tr_transactions as tPay on tPay.transactionID = rP.appliedToTransactionID and tPay.statusID = 1
			where saleAndAdj.statusid = 1
				union all
			select saleAndAdj.saleTransactionID, 
				case 
				when saleAndAdj.statusID IN (2,4) then 0 
				else tWO.amount
				end as allocAmt
			from saleAndAdj
			inner join dbo.tr_relationships as rS on rS.appliedToTransactionID = saleAndAdj.transactionID
			inner join dbo.tr_relationshipTypes as rtS on rtS.typeID = rS.typeID and rtS.type = 'WriteOffSaleTrans'
			inner join dbo.tr_transactions as tWO on tWO.transactionID = rS.transactionID and tWO.statusID = 1
			where saleAndAdj.statusid = 1
		) tmp
		group by saleTransactionID
	), PendingAlloc AS (
		select saleAndAdj.saleTransactionID, 
			case 
			when saleAndAdj.statusID IN (2,4) then 0 
			else sum(
					CASE 
					WHEN glAllocDeb.GLCode = 'ACCOUNTSRECEIVABLE' then tAlloc.amount*-1
					ELSE tAlloc.amount
					END
					) 
			end as allocAmt
		from saleAndAdj
		inner join dbo.tr_relationships as rS on rS.appliedToTransactionID = saleAndAdj.transactionID
		inner join dbo.tr_relationshipTypes as rtS on rtS.typeID = rS.typeID and rtS.type = 'AllocSaleTrans'
		inner join dbo.tr_transactions as tAlloc on tAlloc.transactionID = rS.transactionID and tAlloc.statusID = 1
		inner join dbo.tr_GLAccounts as glAllocDeb on glAllocDeb.GLAccountID = tAlloc.debitGLAccountID
		inner join dbo.tr_relationships as rP on rP.transactionID = tAlloc.transactionID
		inner join dbo.tr_relationshipTypes as rtP on rtP.typeID = rP.typeID and rtP.type = 'AllocPayTrans'
		inner join dbo.tr_transactions as tPay on tPay.transactionID = rP.appliedToTransactionID and tPay.statusID = 3
		where saleAndAdj.statusid = 1
		group by saleAndAdj.saleTransactionID, saleAndAdj.statusID
	)
	select distinct saleAndAdj.saleTransactionID, 
		isnull(ActiveAlloc.allocAmt,0) as activePaymentAllocatedAmount,
		isnull(PendingAlloc.allocAmt,0) as pendingPaymentAllocatedAmount
	from saleAndAdj
	left outer join ActiveAlloc on ActiveAlloc.saleTransactionID = saleAndAdj.saleTransactionID
	left outer join PendingAlloc on PendingAlloc.saleTransactionID = saleAndAdj.saleTransactionID
)
GO

ALTER FUNCTION [dbo].[fn_tr_getAllocatedAmountofSaleOrAdj] (
	@transactionID int -- can be sale/tax/deferred or adj of sale/tax/deferred
) 
RETURNS TABLE WITH SCHEMABINDING
AS
RETURN (

	WITH saleAndAdj AS (
		select transactionID as saleTransactionID, transactionID as transactionID, statusID
		from dbo.tr_transactions
		where transactionID = @transactionID
	), ActiveAlloc AS (
		select saleTransactionID, sum(allocAmt) as allocAmt
		from (		
			select saleAndAdj.saleTransactionID, 
				case 
				when saleAndAdj.statusID IN (2,4) then 0 
				else CASE 
					WHEN glAllocDeb.GLCode = 'ACCOUNTSRECEIVABLE' then tAlloc.amount*-1
					ELSE tAlloc.amount
					END
				end as allocAmt
			from saleAndAdj
			inner join dbo.tr_relationships as rS on rS.appliedToTransactionID = saleAndAdj.transactionID
			inner join dbo.tr_relationshipTypes as rtS on rtS.typeID = rS.typeID and rtS.type = 'AllocSaleTrans'
			inner join dbo.tr_transactions as tAlloc on tAlloc.transactionID = rS.transactionID and tAlloc.statusID = 1
			inner join dbo.tr_GLAccounts as glAllocDeb on glAllocDeb.GLAccountID = tAlloc.debitGLAccountID
			inner join dbo.tr_relationships as rP on rP.transactionID = tAlloc.transactionID
			inner join dbo.tr_relationshipTypes as rtP on rtP.typeID = rP.typeID and rtP.type = 'AllocPayTrans'
			inner join dbo.tr_transactions as tPay on tPay.transactionID = rP.appliedToTransactionID and tPay.statusID = 1
			where saleAndAdj.statusid = 1
				union all
			select saleAndAdj.saleTransactionID, 
				case 
				when saleAndAdj.statusID IN (2,4) then 0 
				else tWO.amount
				end as allocAmt
			from saleAndAdj
			inner join dbo.tr_relationships as rS on rS.appliedToTransactionID = saleAndAdj.transactionID
			inner join dbo.tr_relationshipTypes as rtS on rtS.typeID = rS.typeID and rtS.type = 'WriteOffSaleTrans'
			inner join dbo.tr_transactions as tWO on tWO.transactionID = rS.transactionID and tWO.statusID = 1
			where saleAndAdj.statusid = 1
		) tmp
		group by saleTransactionID
	), PendingAlloc AS (
		select saleAndAdj.saleTransactionID, 
			case 
			when saleAndAdj.statusID IN (2,4) then 0 
			else sum(
					CASE 
					WHEN glAllocDeb.GLCode = 'ACCOUNTSRECEIVABLE' then tAlloc.amount*-1
					ELSE tAlloc.amount
					END
					) 
			end as allocAmt
		from saleAndAdj
		inner join dbo.tr_relationships as rS on rS.appliedToTransactionID = saleAndAdj.transactionID
		inner join dbo.tr_relationshipTypes as rtS on rtS.typeID = rS.typeID and rtS.type = 'AllocSaleTrans'
		inner join dbo.tr_transactions as tAlloc on tAlloc.transactionID = rS.transactionID and tAlloc.statusID = 1
		inner join dbo.tr_GLAccounts as glAllocDeb on glAllocDeb.GLAccountID = tAlloc.debitGLAccountID
		inner join dbo.tr_relationships as rP on rP.transactionID = tAlloc.transactionID
		inner join dbo.tr_relationshipTypes as rtP on rtP.typeID = rP.typeID and rtP.type = 'AllocPayTrans'
		inner join dbo.tr_transactions as tPay on tPay.transactionID = rP.appliedToTransactionID and tPay.statusID = 3
		where saleAndAdj.statusid = 1
		group by saleAndAdj.saleTransactionID, saleAndAdj.statusID
	)
	select distinct saleAndAdj.saleTransactionID, 
		isnull(ActiveAlloc.allocAmt,0) as activePaymentAllocatedAmount,
		isnull(PendingAlloc.allocAmt,0) as pendingPaymentAllocatedAmount
	from saleAndAdj
	left outer join ActiveAlloc on ActiveAlloc.saleTransactionID = saleAndAdj.saleTransactionID
	left outer join PendingAlloc on PendingAlloc.saleTransactionID = saleAndAdj.saleTransactionID
)
GO

ALTER PROC [dbo].[tr_acceptPaymentTransaction]
@enteredByMemberID int,
@paymentTransactionID int,
@transactionDate datetime,
@batchID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @activeStatusID int

	-- get status
	select @activeStatusID = dbo.fn_tr_getStatusID('Active')

	-- if transactionDate is null, get existing date from transaction
	IF @transactionDate is null
		select @transactionDate = transactionDate
		from dbo.tr_transactions
		where transactionID = @paymentTransactionID

	-- if batchID is null or not an open batch, reject payment.
	IF @batchID is null OR NOT EXISTS (
		select batchID
		from dbo.tr_batches
		where batchID = @batchID
		and statusID = 1)
		RAISERROR('batchid is not eligible', 16, 1);

	-- update the status of the payment from Pending to Active.
	-- update the transactionDate of the payment to provided date
	update dbo.tr_transactions
	set statusID = @activeStatusID, transactionDate = @transactionDate
	where transactionID = @paymentTransactionID

	-- get allocations to this batch for later use
	declare @tblAlloc TABLE (transactionID int)
	insert into @tblAlloc (transactionID)
	select distinct tAlloc.transactionID
	from dbo.tr_transactions as tSale
	inner join dbo.tr_relationships as rS on rS.appliedToTransactionID = tSale.transactionID
	inner join dbo.tr_relationshipTypes as rtS on rtS.typeID = rS.typeID and rtS.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as tAlloc on tAlloc.transactionID = rS.transactionID and tAlloc.statusID in (1,3)
	inner join dbo.tr_GLAccounts as glAllocDeb on glAllocDeb.GLAccountID = tAlloc.debitGLAccountID
	inner join dbo.tr_relationships as rP on rP.transactionID = tAlloc.transactionID
	inner join dbo.tr_relationshipTypes as rtP on rtP.typeID = rP.typeID and rtP.type = 'AllocPayTrans'
	inner join dbo.tr_transactions as tPay on tPay.transactionID = rP.appliedToTransactionID and tPay.statusID in (1,3)
		and tPay.transactionID = @paymentTransactionID

	-- move payment from pending batch to selected batch
	update dbo.tr_batchTransactions
	set batchID = @batchID 
	where transactionID = @paymentTransactionID

	-- move allocations from pending batch to selected batch
	update bt
	set bt.batchID = @batchID 
	from dbo.tr_batchTransactions as bt
	inner join @tblAlloc as alloc on alloc.transactionID = bt.transactionID

	-- update the transactionDate of all allocations to this payment
	update t
	set t.transactionDate = @transactionDate
	from dbo.tr_transactions as t
	inner join @tblAlloc as alloc on alloc.transactionID = t.transactionID

	-- update sales cache amounts to show no longer pending allocation
	update ts
	set cache_activePaymentAllocatedAmount = amt.activePaymentAllocatedAmount,
		cache_pendingPaymentAllocatedAmount = amt.pendingPaymentAllocatedAmount
	from dbo.tr_transactionSales as ts
	cross apply dbo.fn_tr_getAllocatedAmountofSale(ts.transactionid) as amt
	where ts.transactionID in (
		select tSale.transactionID
		from dbo.tr_transactions as tAlloc
		inner join dbo.tr_relationships as rP on rP.transactionID = tAlloc.transactionID and rP.appliedToTransactionID = @paymentTransactionID
		inner join dbo.tr_relationshipTypes as rtP on rtP.typeID = rP.typeID and rtP.type = 'AllocPayTrans'
		inner join dbo.tr_transactions as tPay on tPay.transactionID = rP.appliedToTransactionID and tPay.statusID in (1,3)
		inner join dbo.tr_relationships as rS on rS.transactionID = tAlloc.transactionID
		inner join dbo.tr_relationshipTypes as rtS on rtS.typeID = rS.typeID and rtS.type = 'AllocSaleTrans'
		inner join dbo.tr_transactions as tSale on tSale.transactionID = rS.appliedToTransactionID and tSale.statusID in (1,3)
		where tAlloc.statusID in (1,3)
			union
		select tSale.transactionID
		from dbo.tr_transactions as tAlloc
		inner join dbo.tr_relationships as rP on rP.transactionID = tAlloc.transactionID and rP.appliedToTransactionID = @paymentTransactionID
		inner join dbo.tr_relationshipTypes as rtP on rtP.typeID = rP.typeID and rtP.type = 'AllocPayTrans'
		inner join dbo.tr_transactions as tPay on tPay.transactionID = rP.appliedToTransactionID and tPay.statusID in (1,3)
		inner join dbo.tr_relationships as rA on rA.transactionID = tAlloc.transactionID
		inner join dbo.tr_relationshipTypes as rtA on rtA.typeID = rA.typeID and rtA.type = 'AllocSaleTrans'
		inner join dbo.tr_transactions as tAdj on tAdj.transactionID = rA.appliedToTransactionID and tAdj.statusID in (1,3)
		inner join dbo.tr_relationships as rS on rS.transactionID = tAdj.transactionID
		inner join dbo.tr_relationshipTypes as rtS on rtS.typeID = rS.typeID and rtS.type = 'AdjustTrans'
		inner join dbo.tr_transactions as tSale on tSale.transactionID = rS.appliedToTransactionID and tSale.statusID in (1,3)
		where tAlloc.statusID in (1,3)
	)

	-- update invoice transaction cache amounts to show no longer pending allocation
	update it
	set cache_activePaymentAllocatedAmount = amt.activePaymentAllocatedAmount,
		cache_pendingPaymentAllocatedAmount = amt.pendingPaymentAllocatedAmount
	from dbo.tr_invoiceTransactions as it
	cross apply dbo.fn_tr_getAllocatedAmountofSaleOrAdj(it.transactionid) as amt
	where it.transactionID in (
		select tSale.transactionID
		from dbo.tr_transactions as tAlloc
		inner join dbo.tr_relationships as rP on rP.transactionID = tAlloc.transactionID and rP.appliedToTransactionID = @paymentTransactionID
		inner join dbo.tr_relationshipTypes as rtP on rtP.typeID = rP.typeID and rtP.type = 'AllocPayTrans'
		inner join dbo.tr_transactions as tPay on tPay.transactionID = rP.appliedToTransactionID and tPay.statusID in (1,3)
		inner join dbo.tr_relationships as rS on rS.transactionID = tAlloc.transactionID
		inner join dbo.tr_relationshipTypes as rtS on rtS.typeID = rS.typeID and rtS.type = 'AllocSaleTrans'
		inner join dbo.tr_transactions as tSale on tSale.transactionID = rS.appliedToTransactionID and tSale.statusID in (1,3)
		where tAlloc.statusID in (1,3)
	)

	-- for each invoice tied to invoiceTransactions tied to allocated payment, run the invoice checks
	declare @tblInv TABLE (invoiceID int, [status] varchar(50))
	insert into @tblInv (invoiceID, [status])
	select distinct i.invoiceID, ins.status
	from dbo.tr_transactions as tAlloc
	inner join dbo.tr_relationships as rP on rP.transactionID = tAlloc.transactionID and rP.appliedToTransactionID = @paymentTransactionID
	inner join dbo.tr_relationshipTypes as rtP on rtP.typeID = rP.typeID and rtP.type = 'AllocPayTrans'
	inner join dbo.tr_transactions as tPay on tPay.transactionID = rP.appliedToTransactionID and tPay.statusID in (1,3)
	inner join dbo.tr_relationships as rS on rS.transactionID = tAlloc.transactionID
	inner join dbo.tr_relationshipTypes as rtS on rtS.typeID = rS.typeID and rtS.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as tSale on tSale.transactionID = rS.appliedToTransactionID and tSale.statusID in (1,3)
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSale.transactionID
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
	where tAlloc.statusID in (1,3)
	
	declare @invoiceID int, @invStatus varchar(50), @amtDueNoPendingOnInvoice money
	select @invoiceID = min(invoiceID) from @tblInv
	while @invoiceID is not null BEGIN
		select @invStatus = [status] from @tblInv where invoiceID = @invoiceID

		-- cleanup invoice
		-- if invoice is closed and is now fully paid with active payments, mark it as paid
		-- if invoice is paid and is now not fully paid with active payments, mark it as closed
		select @amtDueNoPendingOnInvoice = sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount)
			from dbo.tr_invoiceTransactions as it
			inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
			where it.invoiceID = @invoiceID
			and t.statusID <> 2
		IF @invStatus = 'closed' and @amtDueNoPendingOnInvoice = 0 BEGIN
			update dbo.tr_invoices
			set statusID = 4, payProfileID = null
			where invoiceID = @invoiceID

			insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
			values (@invoiceID, getdate(), 4, 3, @enteredByMemberID)
		END
		IF @invStatus = 'paid' and @amtDueNoPendingOnInvoice > 0 BEGIN
			update dbo.tr_invoices
			set statusID = 3
			where invoiceID = @invoiceID

			insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
			values (@invoiceID, getdate(), 3, 4, @enteredByMemberID)
		END

		select @invoiceID = min(invoiceID) from @tblInv where invoiceID > @invoiceID
	END

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[tr_createTransaction_writeoff_sale]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@status varchar(20),
@amount money,
@transactionDate datetime,
@saleTransactionID int, -- this can be a sale or adjustment TID
@transactionID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- init output param
	select @transactionID = 0

	-- no zero or negative dollar writeoffs
	if @amount <= 0
		RAISERROR('amount is not greater than 0', 16, 1);

	-- ensure amount is 2 decimals
	select @amount = cast(@amount as decimal(10,2))

	-- ensure @saleTransactionID is a non-voided sale, sales tax, or positive adjustment
	IF NOT EXISTS (
		select t.transactionID
		from dbo.tr_transactions as t
		inner join dbo.tr_glAccounts as glDeb on glDeb.GLAccountID = t.debitGLAccountID
		where t.transactionID = @saleTransactionID
		and t.typeID in (1,3,7)
		and t.statusID = 1
		and glDeb.GLCode = 'ACCOUNTSRECEIVABLE' 
		and glDeb.isSystemAccount = 1
	)
		RAISERROR('saleTransactionID is not a non-voided sale, sales tax, or positive adjustment', 16, 1);

	-- get assignedToMemberID from sale transaction
	declare @assignedToMemberID int, @ownedByOrgID int, @detail varchar(max)
	select @assignedToMemberID = assignedToMemberID, @ownedByOrgID = ownedByOrgID, @detail = detail
		from dbo.tr_transactions 
		where transactionID = @saleTransactionID

	-- dont assume memberid is the active one. get the active one.
	select @assignedToMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @assignedToMemberID 
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	-- WO is debit acct, AR is credit acct
	declare @debitGLAccountID int, @creditGLAccountID int
	select @debitGLAccountID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and isSystemAccount = 1
		and GLCode = 'WRITEOFF'
		and [status] = 'A'
	select @creditGLAccountID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and isSystemAccount = 1
		and GLCode = 'ACCOUNTSRECEIVABLE'
		and [status] = 'A'

	-- ensure we have active debit/credit accts
	IF @debitGLAccountID is null or @creditGLAccountID is null
		RAISERROR('debitGLAccountID or creditGLAccountID is blank', 16, 1);

	-- if writing off a sale on an open/pending invoice, close it. Only closed inv can write offs.
	declare @invoiceID int, @invstatus varchar(50)
	select @invoiceID=i.invoiceID, @invstatus=ins.status
		from dbo.tr_invoices as i
		inner join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
		inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
		where it.transactionID = @saleTransactionID
	IF @invstatus <> 'Closed' 
		EXEC dbo.tr_closeInvoice @enteredByMemberID=@recordedByMemberID, @invoiceIDList=@invoiceID
	
	-- insert into transactions
	-- ensure amount is abs
	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
		amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
		typeID, accrualDate, debitGLAccountID, creditGLAccountID)
	VALUES (@ownedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID(@status), @detail, null, 
		abs(@amount), getdate(), @transactionDate, @assignedToMemberID, @recordedByMemberID, @statsSessionID, 
		6, @transactionDate, @debitGLAccountID, @creditGLAccountID)
	select @transactionID = SCOPE_IDENTITY()

	-- insert into relationships
	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
	VALUES (dbo.fn_tr_getRelationshipTypeID('WriteOffSaleTrans'), @transactionID, @saleTransactionID)

	-- update sales cache
	-- if writing off an adjustment, update cache of sale or tax it is adjusting
	declare @stTypeID int, @stID int, @activePaymentAllocatedAmount money, @pendingPaymentAllocatedAmount money
	select @stTypeID = typeID from dbo.tr_transactions where transactionID = @saleTransactionID 
	IF @stTypeID in (1,7)
		select @stID = @saleTransactionID
	ELSE
		select @stID = tSale.transactionID
		from dbo.tr_transactions as tSale
		inner join dbo.tr_relationships as tR on tR.appliedToTransactionID = tSale.transactionID and tR.transactionID = @saleTransactionID
		inner join dbo.tr_relationshipTypes as trt on trt.typeID = tR.typeID and trt.type = 'AdjustTrans'

	select @activePaymentAllocatedAmount = activePaymentAllocatedAmount,
		@pendingPaymentAllocatedAmount = pendingPaymentAllocatedAmount
		from dbo.fn_tr_getAllocatedAmountofSale(@stID)
	UPDATE dbo.tr_transactionSales
	SET cache_activePaymentAllocatedAmount = @activePaymentAllocatedAmount,
		cache_pendingPaymentAllocatedAmount = @pendingPaymentAllocatedAmount
	WHERE transactionID = @stID

	-- update invoiceTransactions cache
	select @activePaymentAllocatedAmount = null, @pendingPaymentAllocatedAmount = null
	select @activePaymentAllocatedAmount = activePaymentAllocatedAmount,
		@pendingPaymentAllocatedAmount = pendingPaymentAllocatedAmount
		from dbo.fn_tr_getAllocatedAmountofSaleOrAdj(@saleTransactionID)
	UPDATE dbo.tr_invoiceTransactions
	SET cache_activePaymentAllocatedAmount = @activePaymentAllocatedAmount,
		cache_pendingPaymentAllocatedAmount = @pendingPaymentAllocatedAmount
	WHERE transactionID = @saleTransactionID

	-- check the in-bound rules.
	-- sale - new cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount must be between 0 and cache_amountAfterAdjustment
	IF NOT EXISTS (select saleID from dbo.tr_transactionSales where transactionID = @stID and cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount between 0 and cache_amountAfterAdjustment)
		OR NOT EXISTS (select itID from dbo.tr_invoiceTransactions where transactionID = @saleTransactionID and cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount between 0 and cache_invoiceAmountAfterAdjustment)
		RAISERROR('in-bound checks failed', 16, 1);

	-- cleanup invoice
	-- if invoice is closed and is now fully paid with active payments, mark it as paid
	declare @amtDueNoPendingOnInvoice money
	select @invoiceID=i.invoiceID, @invstatus=ins.status
		from dbo.tr_invoices as i
		inner join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
		inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
		where it.transactionID = @saleTransactionID
	select @amtDueNoPendingOnInvoice = sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount)
		from dbo.tr_invoiceTransactions as it
		inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
		where it.invoiceID = @invoiceID
		and t.statusID <> 2
	IF @invstatus = 'closed' and @amtDueNoPendingOnInvoice = 0 BEGIN
		update dbo.tr_invoices
		set statusID = 4, payProfileID = null
		where invoiceID = @invoiceID
		
		insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
		values (@invoiceID, getdate(), 4, 3, @recordedByMemberID)
	END


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @transactionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[tr_voidTransaction_allocation]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@transactionID int,
@vidPool xml OUTPUT,
@tids xml OUTPUT

AS

/* ***********************************************
	DO NOT CALL THIS PROC DIRECTLY!
	THIS SHOULD ONLY BE CALLED FROM WITHIN 
	tr_voidTransaction
************************************************** */
set nocount on

declare @offsetTransactionID int, @statusID int, @minTID int, @minAID int
declare @tblVoided TABLE (transactionid int)
declare @tidsdeep xml
select @statusID = dbo.fn_tr_getStatusID('Voided')
select @tids = null


-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;


BEGIN TRY

	-- create offset trans
	EXEC dbo.tr_createTransaction_voidOffset @recordedOnSiteID=@recordedOnSiteID,
		@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID,
		@appliedToTransactionID=@transactionID, @vidPool=@vidPool OUTPUT,
		@transactionID=@offsetTransactionID OUTPUT
	insert into @tblVoided (transactionid) 
	values (@offsetTransactionID)

	-- update transaction status
	UPDATE dbo.tr_transactions
	SET statusID = @statusID
	WHERE transactionID = @transactionID
	
	insert into @tblVoided (transactionid) 
	values (@transactionID)

	-- void any related transactions
	declare @toVoid TABLE (autoid int IDENTITY(1,1), transactionID int, dateRecorded datetime)
	insert into @toVoid (transactionID, dateRecorded)
	select transactionID, dateRecorded
	from dbo.fn_tr_getRelatedTransactionsToVoid_allocation(@transactionID)
	ORDER BY dateRecorded desc, transactionID desc	

	SELECT @minAID = null
	SELECT @minAID = min(autoid) FROM @toVoid
	while @minAID is not null BEGIN
		SELECT @minTID = null
		SELECT @minTID = transactionID from @toVoid where autoID = @minAID

		-- checkInBounds should be 0 on these nested voids
		EXEC dbo.tr_voidTransaction @recordedOnSiteID=@recordedOnSiteID, 
			@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
			@transactionID=@minTID, @checkInBounds=0, @vidPool=@vidPool OUTPUT,
			@tids=@tidsdeep OUTPUT
		insert into @tblVoided (transactionid)
		select T.item.value('@tid','int')
		FROM @tidsdeep.nodes('/tr/t') as T(item)

		SELECT @minAID = min(autoid) FROM @toVoid where autoID > @minAID
	END

	-- get amount of allocation
	declare @allocAmt money, @assignedToMemberID int
	select @allocAmt = amount, @assignedToMemberID=assignedToMemberID from dbo.tr_transactions where transactionID = @transactionID
		IF @allocAmt is null RAISERROR('allocAmt is null', 16, 1);

	-- dont assume memberid is the active one. get the active one.
	select @assignedToMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @assignedToMemberID 
	
	-- if debit acct is AR, then was a deallocation. else allocation.
	IF EXISTS (select transactionID 
				from dbo.tr_transactions as t
				inner join dbo.tr_glAccounts as gl on gl.glAccountID = t.debitGLAccountID
					and gl.GLCode = 'ACCOUNTSRECEIVABLE'
					and gl.isSystemAccount = 1
					and t.transactionID = @transactionID) BEGIN
		SELECT @allocAmt = @allocAmt * -1
	END

	-- get what has been allocated.
	declare @saleTaxAdjTID int, @saleTaxAdjTypeID int, @saleTaxTID int
	declare @paymentTID int, @paystatusID int
	select @paymentTID = r.appliedToTransactionID, @paystatusID = t.statusID
		from dbo.tr_relationships as r
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID
			and r.transactionID = @transactionID
			and rt.type = 'AllocPayTrans'
		inner join dbo.tr_transactions as t on t.transactionID = r.appliedToTransactionID
		IF @paymentTID is null RAISERROR('paymentTID is null', 16, 1);
	select @saleTaxAdjTID = r.appliedToTransactionID, @saleTaxAdjTypeID = t.typeID
		from dbo.tr_relationships as r
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID
			and r.transactionID = @transactionID
			and rt.type = 'AllocSaleTrans'
		inner join dbo.tr_transactions as t on t.transactionID = r.appliedToTransactionID
		IF @saleTaxAdjTID is null RAISERROR('saleTaxAdjTID is null', 16, 1);
	IF @saleTaxAdjTypeID in (1,7)
		SELECT @saleTaxTID = @saleTaxAdjTID
	ELSE BEGIN
		SELECT @saleTaxTID = r.appliedToTransactionID
			from dbo.tr_transactions as tAdj
			inner join dbo.tr_relationships as r on r.transactionID = tAdj.transactionID
			inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
			where tAdj.transactionID = @saleTaxAdjTID
		IF @saleTaxTID is null RAISERROR('saleTaxTID is null', 16, 1);
	END

	-- update invoicetransactions
	declare @activePaymentAllocatedAmount money, @pendingPaymentAllocatedAmount money
	select @activePaymentAllocatedAmount = activePaymentAllocatedAmount, 
			@pendingPaymentAllocatedAmount = pendingPaymentAllocatedAmount
	from dbo.fn_tr_getAllocatedAmountofSaleOrAdj(@saleTaxAdjTID)

	UPDATE dbo.tr_invoiceTransactions
	SET cache_activePaymentAllocatedAmount = @activePaymentAllocatedAmount,
		cache_pendingPaymentAllocatedAmount = @pendingPaymentAllocatedAmount
	WHERE transactionID = @saleTaxAdjTID

	-- put in the @saleTaxAdjTID for the cleanup invoice routine
	insert into @tblVoided (transactionid)
	values (@saleTaxAdjTID)

	-- update sales cache
	declare @cache_activePaymentAllocatedAmount money, @cache_pendingPaymentAllocatedAmount money
	select @cache_activePaymentAllocatedAmount=activePaymentAllocatedAmount, 
			@cache_pendingPaymentAllocatedAmount=pendingPaymentAllocatedAmount
	from dbo.fn_tr_getAllocatedAmountofSale(@saleTaxTID)

	UPDATE dbo.tr_transactionSales
	SET cache_activePaymentAllocatedAmount = @cache_activePaymentAllocatedAmount,
		cache_pendingPaymentAllocatedAmount = @cache_pendingPaymentAllocatedAmount
	WHERE transactionID = @saleTaxTID

	-- update payment cache
	IF @allocAmt > 0 BEGIN
		UPDATE dbo.tr_transactionPayments
		SET cache_allocatedAmountOfPayment = cache_allocatedAmountOfPayment - @allocAmt
		WHERE transactionID = @paymentTID
	END
	IF @allocAmt < 0 BEGIN
		UPDATE dbo.tr_transactionPayments
		SET cache_allocatedAmountOfPayment = cache_allocatedAmountOfPayment + abs(@allocAmt)
		WHERE transactionID = @paymentTID
	END

	-- update credit balances
	EXEC dbo.tr_updateCreditBalanceByMember @memberID=@assignedToMemberID

	IF @TranCounter = 0
		COMMIT TRAN;
	SELECT @tids = (SELECT transactionid as tid FROM @tblVoided FOR XML RAW('t'), root('tr'))
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	SELECT @tids = '<tr/>'
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[tr_voidTransaction_writeoff_sale]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@transactionID int,
@vidPool xml OUTPUT,
@tids xml OUTPUT

AS

/* ***********************************************
	DO NOT CALL THIS PROC DIRECTLY!
	THIS SHOULD ONLY BE CALLED FROM WITHIN 
	tr_voidTransaction
************************************************** */
set nocount on

declare @offsetTransactionID int, @statusID int, @minTID int
declare @tblVoided TABLE (transactionid int)
select @statusID = dbo.fn_tr_getStatusID('Voided')
select @tids = null


-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;


BEGIN TRY

	-- create offset trans
	EXEC dbo.tr_createTransaction_voidOffset @recordedOnSiteID=@recordedOnSiteID,
		@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID,
		@appliedToTransactionID=@transactionID, @vidPool=@vidPool OUTPUT,
		@transactionID=@offsetTransactionID OUTPUT
	insert into @tblVoided (transactionid) 
	values (@offsetTransactionID)

	-- update transaction status
	UPDATE dbo.tr_transactions
	SET statusID = @statusID
	WHERE transactionID = @transactionID

	insert into @tblVoided (transactionid) 
	values (@transactionID)

	-- get amount of write off
	declare @woAmt money
	select @woAmt = amount from dbo.tr_transactions where transactionID = @transactionID
		IF @woAmt is null RAISERROR('woAmt is null', 16, 1);

	-- get sale that has been written off
	declare @saleTID int
	select @saleTID = r.appliedToTransactionID 
		from dbo.tr_relationships as r
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID
			and r.transactionID = @transactionID
			and rt.type = 'WriteOffSaleTrans'
		IF @saleTID is null RAISERROR('saleTID is null', 16, 1);

	-- update sales cache
	-- if writing off an adjustment, update cache of sale or tax it is adjusting
	declare @stTypeID int, @stID int, @activePaymentAllocatedAmount money, @pendingPaymentAllocatedAmount money
	select @stTypeID = typeID from dbo.tr_transactions where transactionID = @saleTID 
	IF @stTypeID in (1,7)
		select @stID = @saleTID
	ELSE
		select @stID = tSale.transactionID
			from dbo.tr_transactions as tSale
			inner join dbo.tr_relationships as tR on tR.appliedToTransactionID = tSale.transactionID and tR.transactionID = @saleTID
			inner join dbo.tr_relationshipTypes as trt on trt.typeID = tR.typeID and trt.type = 'AdjustTrans'

	select @activePaymentAllocatedAmount = activePaymentAllocatedAmount,
		@pendingPaymentAllocatedAmount = pendingPaymentAllocatedAmount
	from dbo.fn_tr_getAllocatedAmountofSale(@stID)

	UPDATE dbo.tr_transactionSales
	SET cache_activePaymentAllocatedAmount = @activePaymentAllocatedAmount,
		cache_pendingPaymentAllocatedAmount = @pendingPaymentAllocatedAmount
	WHERE transactionID = @stID

	-- update invoiceTransactions cache
	select @activePaymentAllocatedAmount = null, @pendingPaymentAllocatedAmount = null
	select @activePaymentAllocatedAmount = activePaymentAllocatedAmount,
		@pendingPaymentAllocatedAmount = pendingPaymentAllocatedAmount
	from dbo.fn_tr_getAllocatedAmountofSaleOrAdj(@saleTID)

	UPDATE dbo.tr_invoiceTransactions
	SET cache_activePaymentAllocatedAmount = @activePaymentAllocatedAmount,
		cache_pendingPaymentAllocatedAmount = @pendingPaymentAllocatedAmount
	WHERE transactionID = @saleTID

	-- put in the @saleTID for the cleanup invoice routine
	insert into @tblVoided (transactionid)
	values (@saleTID)


	IF @TranCounter = 0
		COMMIT TRAN;
	SELECT @tids = (SELECT transactionid as tid FROM @tblVoided FOR XML RAW('t'), root('tr'))
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	SELECT @tids = '<tr/>'
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[tr_createTransaction_allocation]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@status varchar(20),
@amount money,
@transactionDate datetime,
@paymentTransactionID int,
@saleTransactionID int,	-- this can be a sale, tax, adjustment, or DIT
@ovBatchID int, -- optional override
@transactionID int OUTPUT

AS

set nocount on

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- reset output param
	select @transactionID = 0

	-- no zero dollar allocations
	if @amount = 0
		RAISERROR('amount is 0', 16, 1);

	-- ensure amount is 2 decimals
	select @amount = cast(@amount as decimal(10,2))

	-- ensure @paymentTransactionID is a payment
	IF NOT EXISTS (select transactionID from dbo.tr_transactions where transactionID = @paymentTransactionID and typeID = 2)
		RAISERROR('paymentTransactionID is not a payment', 16, 1);

	-- ensure @saleTransactionID is either a sale, sales tax, positive adjustment, or pos DIT
	IF NOT EXISTS (
		select t.transactionID
		from dbo.tr_transactions as t
		inner join dbo.tr_glAccounts as glDeb on glDeb.GLAccountID = t.debitGLAccountID
		where t.transactionID = @saleTransactionID
		and t.typeID in (1,3,7)
		and glDeb.GLCode = 'ACCOUNTSRECEIVABLE' 
		and glDeb.isSystemAccount = 1
		) AND NOT EXISTS (
			select t.transactionID
			from dbo.tr_transactions as t
			inner join dbo.tr_glAccounts as glDeb on glDeb.GLAccountID = t.debitGLAccountID
			inner join dbo.tr_glAccountTypes as glt on glt.accountTypeID = glDeb.accountTypeID
			where t.transactionID = @saleTransactionID
			and t.typeID = 10
			and glt.accountType = 'Liability' 
			and glDeb.isSystemAccount = 1
		)
	RAISERROR('saleTransactionID is not a sale, sales tax, positive adjustment, or deferred transfer', 16, 1);

	-- get info from payment transaction
	declare @assignedToMemberID int, @ownedByOrgID int, @payProfileID int, @payProfileCode varchar(20),
		@payCashGLID int, @payCashGLName varchar(200)
	select @assignedToMemberID = t.assignedToMemberID, 
		@ownedByOrgID = t.ownedByOrgID,
		@payProfileID = mp.profileID,
		@payProfileCode = mp.profileCode,
		@payCashGLID = glDeb.GLAccountID,
		@payCashGLName = glDeb.AccountName
	from dbo.tr_transactions as t
	inner join dbo.tr_transactionPayments as tp on tp.transactionID = t.transactionID
	inner join dbo.mp_profiles as mp on mp.profileID = tp.profileID
	inner join dbo.tr_GLAccounts as glDeb on glDeb.GLAccountID = t.debitGLAccountID
	where t.transactionID = @paymentTransactionID

	-- dont assume memberid is the active one. get the active one.
	select @assignedToMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @assignedToMemberID 
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	-- if amount is positive, allocating. Debit is DEP, Credit is AR.
	-- if amount is negative, deallocating. Debit is AR, Credit is DEP.
	declare @debitGLAccountID int, @creditGLAccountID int, @ARGLAID int, @DEPGLAID int
	select @ARGLAID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and isSystemAccount = 1
		and GLCode = 'ACCOUNTSRECEIVABLE'
		and [status] = 'A'
	select @DEPGLAID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and isSystemAccount = 1
		and GLCode = 'DEPOSITS'
		and [status] = 'A'
	if @amount > 0 BEGIN
		select @debitGLAccountID = @DEPGLAID
		select @creditGLAccountID = @ARGLAID
	END
	ELSE BEGIN
		select @debitGLAccountID = @ARGLAID
		select @creditGLAccountID = @DEPGLAID
	END

	-- ensure we have active debit/credit accts
	IF @debitGLAccountID is null or @creditGLAccountID is null
		RAISERROR('debitGLAccountID or creditGLAccountID is null', 16, 1);

	-- get transaction Type	for saletransactionID
	declare @stTypeID int
	select @stTypeID = typeID from dbo.tr_transactions where transactionID = @saleTransactionID 

	-- only closed invoices can accept payment. see if sale/tax/adj (or linked sale/tax/adj for deferred) is open/pending and close it.
	declare @invoiceID int, @invstatus varchar(50), @linkedTID int
	IF @amount > 0 BEGIN
		IF @stTypeID in (1,3,7)
			set @linkedTID = @saleTransactionID
		ELSE
			select @linkedTID = r.appliedToTransactionID
			from dbo.tr_relationships as r 
			inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
			where r.transactionID = @saleTransactionID

		select @invoiceID=i.invoiceID, @invstatus=ins.status
		from dbo.tr_invoices as i
		inner join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
		inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
		where it.transactionID = @linkedTID

		IF @invoiceID is not null AND @invstatus <> 'Closed'
			EXEC dbo.tr_closeInvoice @enteredByMemberID=@recordedByMemberID, @invoiceIDList=@invoiceID
	END


	-- insert into transactions
	-- ensure amount is abs
	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
		amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
		typeID, accrualDate, debitGLAccountID, creditGLAccountID)
	VALUES (@ownedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID(@status), null, null, 
		abs(@amount), getdate(), @transactionDate, @assignedToMemberID, @recordedByMemberID, @statsSessionID, 
		5, @transactionDate, @debitGLAccountID, @creditGLAccountID)
		select @transactionID = SCOPE_IDENTITY()

	-- insert into relationships
	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
	VALUES (dbo.fn_tr_getRelationshipTypeID('AllocPayTrans'), @transactionID, @paymentTransactionID)

	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
	VALUES (dbo.fn_tr_getRelationshipTypeID('AllocSaleTrans'), @transactionID, @saleTransactionID)

	-- add allocation to batch
	-- if batch is passed in, put on that batch (if that batch is open).
	-- if no batch passed in, put on same batch as payment (if that batch is open).
	-- otherwise, put on daily exception batch
	declare @allocBatchID int, @batchCode varchar(40), @batchName varchar(400)
	IF @ovBatchID is not null
		select @allocBatchID = batchID
		from dbo.tr_batches 
		where batchID = @ovBatchID
		and statusID = 1
	ELSE	
		select @allocBatchID = b.batchID
		from dbo.tr_batchTransactions as bt
		inner join dbo.tr_batches as b on b.batchID = bt.batchID
		where bt.transactionID = @paymentTransactionID
		and b.statusID = 1
	IF @allocBatchID is null BEGIN
		select @batchCode = CONVERT(CHAR(8),@transactionDate,112) + '_' + cast(@payProfileID as varchar(10)) + '_' + cast(@payCashGLID as varchar(10)) + '_EX'
		select @batchName = CONVERT(CHAR(8),@transactionDate,112) + ' ' + @payProfileCode + ' ' + @payCashGLName + ' Exceptions'
		select @allocBatchID = b.batchID
			from dbo.tr_batches as b
			where b.orgID = @ownedByOrgID
			and b.batchCode = @batchCode
			and b.statusID = 1
			and b.isSystemCreated = 1
		IF @allocBatchID is null 
			EXEC dbo.tr_createBatch @orgID=@ownedByOrgID, @payProfileID=@payProfileID, @batchCode=@batchCode, 
				@batchName=@batchName, @controlAmt=0, @controlCount=0, @depositDate=@transactionDate, 
				@isSystemCreated=1, @createdByMemberID=null, @batchID=@allocBatchID OUTPUT 
	END
	INSERT INTO dbo.tr_batchTransactions (batchID, transactionID)
	VALUES (@allocBatchID, @transactionID)


	-- update payment cache	
	declare @refundableAmount money, @allocatedAmount money
	select @refundableAmount = refundableAmount from dbo.fn_tr_getRefundableAmountofPayment(@paymentTransactionID,null)
	select @allocatedAmount = allocatedAmount from dbo.fn_tr_getAllocatedAmountofPayment(@paymentTransactionID,null)

	update dbo.tr_transactionPayments
	set cache_allocatedAmountOfPayment = @allocatedAmount,
		cache_refundableAmountOfPayment = @refundableAmount
	where transactionID = @paymentTransactionID


	-- update sales cache
	-- if allocating to sale/tax/deferred, update that transaction record
	-- if allocating to an adjustment, update cache of sale/tax/deferred it is adjusting
	declare @tsID int, @activePaymentAllocatedAmount money, @pendingPaymentAllocatedAmount money
	IF @stTypeID in (1,7,10)
		set @tsID = @saleTransactionID
	ELSE
		select @tsID = r.appliedToTransactionID
			from dbo.tr_relationships as r 
			inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
			where r.transactionID = @saleTransactionID		

	select @activePaymentAllocatedAmount = activePaymentAllocatedAmount, @pendingPaymentAllocatedAmount = pendingPaymentAllocatedAmount
	from dbo.fn_tr_getAllocatedAmountofSale(@tsID)

	UPDATE dbo.tr_transactionSales
	SET cache_activePaymentAllocatedAmount = @activePaymentAllocatedAmount,
		cache_pendingPaymentAllocatedAmount = @pendingPaymentAllocatedAmount
	WHERE transactionID = @tsID


	-- update invoiceTransactions cache
	-- if allocating to sale/tax/adj, update that transaction record
	-- if allocating to deferred, update cache of linked sale/tax/adj 
	declare @itID int	
	IF @stTypeID in (1,3,7)
		set @itID = @saleTransactionID
	ELSE
		select @itID = r.appliedToTransactionID
		from dbo.tr_relationships as r 
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
		where r.transactionID = @saleTransactionID

	select @activePaymentAllocatedAmount = null, @pendingPaymentAllocatedAmount = null
	select @activePaymentAllocatedAmount = activePaymentAllocatedAmount, @pendingPaymentAllocatedAmount = pendingPaymentAllocatedAmount
	from dbo.fn_tr_getAllocatedAmountofSaleOrAdj(@itID)
	
	UPDATE dbo.tr_invoiceTransactions
	SET cache_activePaymentAllocatedAmount = @activePaymentAllocatedAmount,
		cache_pendingPaymentAllocatedAmount = @pendingPaymentAllocatedAmount
	WHERE transactionID = @itID


	-- check the in-bound rules.
	-- sale - new cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount must be between 0 and cache_amountAfterAdjustment
	-- payment - new cache_allocatedAmountOfPayment must be between 0 and cache_refundableAmountOfPayment
	IF NOT EXISTS (select saleID from dbo.tr_transactionSales where transactionID = @tsID and cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount between 0 and cache_amountAfterAdjustment)
		OR NOT EXISTS (select itID from dbo.tr_invoiceTransactions where transactionID = @itID and cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount between 0 and cache_invoiceAmountAfterAdjustment)
		OR NOT EXISTS (select paymentID from dbo.tr_transactionPayments where transactionID = @paymentTransactionID and cache_allocatedAmountOfPayment between 0 and cache_refundableAmountOfPayment)
		RAISERROR('in-bounds checking failed', 16, 1);


	-- cleanup invoice
	-- if invoice is closed and is now fully paid with active payments, mark it as paid
	-- if invoice is paid and is now not fully paid with active payments, mark it as closed
	declare @amtDueNoPendingOnInvoice money
	select @invoiceID=i.invoiceID, @invstatus=ins.status
		from dbo.tr_invoices as i
		inner join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
		inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
		where it.transactionID = @itID
	select @amtDueNoPendingOnInvoice = sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount)
		from dbo.tr_invoiceTransactions as it
		inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
		where it.invoiceID = @invoiceID
		and t.statusID <> 2
	IF @invoiceID is not null and @invstatus = 'closed' and @amtDueNoPendingOnInvoice = 0 BEGIN
		update dbo.tr_invoices
		set statusID = 4, payProfileID = null
		where invoiceID = @invoiceID
		
		insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
		values (@invoiceID, getdate(), 4, 3, @recordedByMemberID)
	END
	IF @invoiceID is not null and @invstatus = 'paid' and @amtDueNoPendingOnInvoice > 0 BEGIN
		update dbo.tr_invoices
		set statusID = 3
		where invoiceID = @invoiceID

		insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
		values (@invoiceID, getdate(), 3, 4, @recordedByMemberID)
	END


	-- update credit balances
	EXEC dbo.tr_updateCreditBalanceByMember @memberID=@assignedToMemberID


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @transactionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

