declare @siteID int, @orgID int
select @siteID=siteID, @orgID=orgID from sites where sitecode='NY'

declare @FID int
select @FID = dbo.fn_getResourceFunctionID('qualify',dbo.fn_getResourceTypeID('SubscriptionRate'))

declare @dtNow datetime
select @dtNow = getdate()			

declare @queuedRenewals TABLE (subscriberID int PRIMARY KEY)
insert into @queuedRenewals (subscriberID)
select qid.subscriberID
from platformQueue.dbo.tblQueueItems_renewSubscribers as qid
inner join platformQueue.dbo.tblQueueItems as qi on qi.itemUID = qid.itemUID
inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.queueStatusID
where qs.queueStatus not in ('readyToNotify','grabbedForNotifying','done')



select memberid, subscriberID, subscriptionID, 
	typeName, subscriptionName, [status], statusName,
	paymentStatus, paymentStatusName, subStartDate, subEndDate, graceEndDate, 
	canRenew, canEmail, canEdit, canExpire, canPay, canMarkAsBilled, 
	parentSubscriberID, rootSubscriberID, payProfileID, thePath,rateName,
	rootSubscriberEndDate, billAmt1, dueAmt1, subTransAmt3.billAmt as billAmt3, subTransAmt3.dueAmt as dueAmt3, subCanRenew, subStatusSort		
into datatransfer.dbo.nyupdates
from (
	select s.memberid, s.subscriberID, s.subscriptionID, t.typeName, sub.subscriptionName, st.statusCode as status, st.statusName,
		pst.statusCode as paymentStatus, pst.statusName as paymentStatusName, s.subStartDate, s.subEndDate, s.graceEndDate,
		st.canRenew, st.canEmail, st.canEdit, st.canExpire, st.canPay, st.canMarkAsBilled,
		s.parentSubscriberID, s.rootSubscriberID, s.payProfileID, convert(varchar, s.rootSubscriberID) + '.' + s.subscriberPath as thePath, r.ratename,
		sRoot.subEndDate as rootSubscriberEndDate,
		case rst.statusCode
				when 'A' then 1 
	 			when 'I' then 2 
		 		when 'P' then 3
			 	when 'O' then 4
				when 'R' then 5
				when 'E' then 6
				when 'X' then 7 
				when 'D' then 8 
				else 9 
			end as statusSort,
		case st.statusCode
				when 'A' then 1 
	 			when 'I' then 2 
		 		when 'P' then 3
			 	when 'O' then 4
				when 'R' then 5
				when 'E' then 6
				when 'X' then 7 
				when 'D' then 8 
				else 9 
			end as subStatusSort,						
			case 
				when rst.statusCode = 'R' or rst.statusCode = 'O' then 
					( select sum(priceToUse) as billAmt
					from (
						select m.memberID,
							case when s1.PCFree = 1 then 0.00
								when s1.modifiedRate is not null then s1.modifiedRate
								else s1.lastPrice end as priceToUse
						from dbo.sub_subscribers s1 WITH(NOLOCK)
						inner join dbo.sub_statuses st WITH(NOLOCK) on st.statusID = s1.statusID and st.statusCode in ('R','O')
						inner join dbo.ams_members as m2 WITH(NOLOCK) on m2.memberid = s1.memberID
						inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = m2.activeMemberID
						where s1.rootSubscriberID = s.rootSubscriberID
					) as innerSales	 )
			end as billAmt1,			
			case 
				when rst.statusCode = 'R' or rst.statusCode = 'O' then 
					(select sum(priceToUse) as DueAmt
					from (
						select m.memberID,
							case when s1.PCFree = 1 then 0.00
								when s1.modifiedRate is not null then s1.modifiedRate
								else s1.lastPrice end as priceToUse
						from dbo.sub_subscribers s1 WITH(NOLOCK)
						inner join dbo.sub_statuses st WITH(NOLOCK) on st.statusID = s1.statusID and st.statusCode in ('R','O')
						inner join dbo.ams_members as m2 WITH(NOLOCK) on m2.memberid = s1.memberID
						inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = m2.activeMemberID
						where s1.rootSubscriberID = s.rootSubscriberID
					) as innerSales	)
			end as dueAmt1						                	
	from sub_subscribers s WITH(NOLOCK) 
	inner join dbo.sub_rateFrequencies rf WITH(NOLOCK) on rf.rfid = s.rfid
	inner join dbo.sub_rates r WITH(NOLOCK) on r.rateID = rf.rateID
	inner join dbo.sub_subscriptions sub WITH(NOLOCK) on sub.subscriptionID = s.subscriptionID
	inner join dbo.sub_types t WITH(NOLOCK) on t.typeID = sub.typeID and t.siteID = @siteID
	inner join dbo.sub_statuses st WITH(NOLOCK) on st.statusID = s.statusID
		and st.statusCode = 'A'
	inner join dbo.sub_paymentStatuses pst WITH(NOLOCK) on pst.statusID = s.paymentStatusID
			and pst.statusCode = 'P'
	inner join sub_subscribers sRoot WITH(NOLOCK) on sRoot.subscriberID = s.rootSubscriberID
	inner join dbo.sub_statuses rst WITH(NOLOCK) on rst.statusID = sRoot.statusID
	where s.memberID in (select mAll.memberID 
						from dbo.ams_members m WITH(NOLOCK) 
						inner join dbo.ams_members mAll WITH(NOLOCK) on mAll.activeMemberID = m.activeMemberID
						where  m.orgID = @orgID)
) x
outer apply (
	select sum(AmtBilled) as billAmt, sum(AmtDue) as dueAmt
	from (
		select 
			AmtBilled = ts.cache_amountAfterAdjustment,
			AmtDue = (ts.cache_amountAfterAdjustment - ts.cache_activePaymentAllocatedAmount)
		from dbo.tr_transactions as t WITH(NOLOCK) 
		inner join dbo.tr_applications tra WITH(NOLOCK) on tra.transactionID = t.transactionID
			and tra.applicationTypeID = 17
			and tra.itemType = 'Dues'
			and tra.status = 'A'
		inner join dbo.sub_subscribers s WITH(NOLOCK) on s.subscriberID = tra.itemID
			and s.rootSubscriberID = x.rootSubscriberID
		inner join dbo.tr_transactionSales ts WITH(NOLOCK) on ts.transactionID = t.transactionID
		where t.ownedByOrgID = @orgID
		and t.statusID = 1
		and t.typeID in (1,7)
	) as innerSales
) as subTransAmt3
outer apply (
	select count(m.memberID) as subCanRenew
	from dbo.sub_subscribers s WITH(NOLOCK) 
		inner join dbo.sub_statuses st WITH(NOLOCK) on st.statusID = s.statusID and st.statusCode not in ('D','X')
		inner join sub_subscriptions sc WITH(NOLOCK) on sc.subscriptionID = s.subscriptionID and sc.soldSeparately = 1
		inner join sub_types t WITH(NOLOCK) on sc.typeiD = t.typeID 
		inner join dbo.ams_members m WITH(NOLOCK) on s.memberID = m.memberID 
		inner join dbo.ams_members activeMember WITH(NOLOCK) on activeMember.memberID = m.activeMemberID and activeMember.status <> 'D'
		left outer join dbo.sub_rates r WITH(NOLOCK) on r.rateID = (	select max(rateID) 
														from dbo.sub_rates r1 WITH(NOLOCK) 
														INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints srfrp WITH(NOLOCK) ON srfrp.siteResourceID = r1.siteResourceID
															AND srfrp.functionID = @FID
														INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp WITH(NOLOCK) on srfrp.rightPrintID = gprp.rightPrintID
														inner join ams_members m1 WITH(NOLOCK) on m1.groupPrintID = gprp.groupPrintID
															and m1.memberID = m.activeMemberID
														where r1.scheduleID = sc.scheduleID 
														and r1.isRenewalRate = 1 
														and r1.status <> 'D')
		left outer join dbo.sub_subscribers sP WITH(NOLOCK) 
			inner join dbo.sub_statuses st2 WITH(NOLOCK) on st2.statusID = sP.statusID and st2.statusCode not in ('D','X')
			inner join dbo.ams_members m2 WITH(NOLOCK) on m2.memberID = sP.memberID 
			on sP.subscriptionID = s.subscriptionID 
				and sP.subscriberID <> s.subscriberID
				and m2.activeMemberID = m.activeMemberID 
				and sP.subEndDate >= s.subEndDate
		left outer join @queuedRenewals rq on rq.subscriberID = s.subscriberID

		where sP.subscriberID is null
			and rq.subscriberID is null
			and s.subscriberID = x.rootSubscriberID
			and (select case 
					when sc.rateTermDateFlag in ('A','S') then (select case when ISNULL(r.termAFStartDate, @dtNow) <= @dtNow then @dtNow else r.termAFStartDate end)
					when sc.rateTermDateFlag = 'C' then dateAdd(dd, 1, s.subEndDate) end) > s.subEndDate
			and (sP.subscriberID is null OR 
				(st2.statusCode = 'P' AND 
					((select case 
						when sc.rateTermDateFlag in ('A','S') then (select case when ISNULL(r.termAFStartDate, @dtNow) <= @dtNow then @dtNow else r.termAFStartDate end)
						when sc.rateTermDateFlag = 'C' then dateAdd(dd, 1, s.subEndDate) end) > sP.subEndDate)))
) as checkSubRenew		
where subTransAmt3.dueAmt > 0	
and subTransAmt3.dueAmt = subTransAmt3.billAmt
order by memberid, statusSort, subStatusSort, rootSubscriberEndDate desc, thePath

update s
	set paymentStatusID = 2
from sub_subscribers s
inner join datatransfer.dbo.nyupdates ny on ny.subscriberID = s.subscriberID

delete psh
from sub_paymentStatusHistory psh
inner join datatransfer.dbo.nyupdates ny on ny.subscriberID = psh.subscriberID

exec dbo.sub_fixGroups @siteID=@siteID, @bypassQueue = 0

-- drop table 	datatransfer.dbo.nyupdates