CREATE AGGREGATE [dbo].[PipeList]
(@value [nvarchar](4000))
RETURNS[nvarchar](4000)
EXTERNAL NAME [CustomAggregates].[PipeList]
GO
EXEC sys.sp_addextendedproperty @name=N'AutoDeployed', @value=N'yes' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'AGGREGATE',@level1name=N'PipeList'
GO
EXEC sys.sp_addextendedproperty @name=N'SqlAssemblyFile', @value=N'pipeList.cs' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'AGGREGATE',@level1name=N'PipeList'
GO
EXEC sys.sp_addextendedproperty @name=N'SqlAssemblyFileLine', @value=N'8' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'AGGREGATE',@level1name=N'PipeList'
GO
