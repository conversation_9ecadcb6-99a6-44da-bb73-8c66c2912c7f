use membercentral;
GO
CREATE FUNCTION [dbo].[fn_sub_getMostExclusiveRateInfo]
(
	-- Add the parameters for the function here
	@memberID int, @subscriptionID int, @isRenewalRate int
)
RETURNS 

@MostExclusiveRateInfo TABLE 
(

rateID int,
uid uniqueIdentifier,
rateName varchar(200)
)
AS
BEGIN

	DECLARE @FID int, @scheduleID int
	select @FID = dbo.fn_getResourceFunctionID('qualify',dbo.fn_getResourceTypeID('SubscriptionRate'))

	select @scheduleID = scheduleID
	from sub_subscriptions
	where subscriptionID = @subscriptionID

	insert into @MostExclusiveRateInfo (rateID, uid,rateName)

	select top 1 newRate.rateID, newRate.uid,  newRate.rateName
	from sub_subscriptions subs
	inner join dbo.sub_rateSchedules as rs
		on rs.scheduleID = subs.scheduleID
		and rs.status = 'A'
		and subs.subscriptionID = @subscriptionID
	inner join dbo.sub_rates as newRate 
		on newRate.scheduleID = rs.scheduleID 
		and newRate.status = 'A' 
		and newRate.isRenewalRate = @isRenewalRate
		and getdate() between newRate.rateAFStartDate and dateadd(day, datediff(day, 0, newRate.rateAFEndDate)+1, 0)
	INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints srfrp 
		ON srfrp.siteResourceID = newRate.siteResourceID
		AND srfrp.functionID = @FID
	INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp on srfrp.rightPrintID = gprp.rightPrintID
	inner join ams_members m
		on m.groupPrintID = gprp.groupPrintID
		and m.memberID = @memberID
	inner join (
		select r.rateID, row_number() over (order by count(*)) as exclusivityRank
		from dbo.sub_rateSchedules as rs
		inner join dbo.sub_rates as r 
			on r.scheduleID = rs.scheduleID 
			and rs.scheduleID = @scheduleID
			and r.status = 'A'
			and r.isRenewalRate = @isRenewalRate
			and rs.status = 'A'
		INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints srfrp 
			ON srfrp.siteResourceID = r.siteResourceID
			AND srfrp.functionID = @FID
		INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp 
			on srfrp.rightPrintID = gprp.rightPrintID
		inner join ams_members m
			on m.groupPrintID = gprp.groupPrintID
			and m.memberID = m.activeMemberID
		group by r.rateID
	) as exclusivityTable
	on exclusivityTable.rateID = newRate.rateID
	order by 
		exclusivityTable.exclusivityRank
	RETURN 
END
GO

ALTER FUNCTION [dbo].[fn_sub_getBestRenewalRateInfo]
(
	-- Add the parameters for the function here
	@memberID int, @subscriptionID int, @currentSubscriberID int
)
RETURNS 
@BestRenewalRateInfo TABLE 
(

rfid int,
rateAmt money,
numInstallments int,
frequencyName varchar(50), 
frequency int, 
frequencyID int, 
rateName varchar(200),
keepChangePrice bit,
modifiedRate money,
selectionMethod varchar(100),
generateWarning bit
)
AS
BEGIN


	DECLARE @FID int, @scheduleID int
	select @FID = dbo.fn_getResourceFunctionID('qualify',dbo.fn_getResourceTypeID('SubscriptionRate'))

	select @scheduleID = scheduleID
	from sub_subscriptions
	where subscriptionID = @subscriptionID

	insert into @BestRenewalRateInfo (rfid,rateAmt,numInstallments,frequencyName, frequency, frequencyID, rateName,keepChangePrice,modifiedRate,selectionMethod,generateWarning)

	select top 1 rf.rfid, rf.rateAmt, rf.numInstallments, isnull(f.frequencyName,'') as frequencyName, isnull(f.frequency,0) as frequency, isnull(f.frequencyID,0) as frequencyID, isnull(newRate.rateName,'') as rateName,
		keepChangePrice = case when existingRate.keepChangedPriceOnRenewal = 1 and (newRate.linkedNonRenewalRateID = existingRate.rateID or newRate.rateID = existingRate.rateID) then cast (1 as bit) else cast (0 as bit) end,
		modifiedRate = case when existingRate.keepChangedPriceOnRenewal = 1 and (newRate.linkedNonRenewalRateID = existingRate.rateID or newRate.rateID = existingRate.rateID) then ss.modifiedRate else null end,

		selectionMethod = case 
			when newRate.rateID = existingRate.rateID then 'sameRateMatch'
			--linkedRateMatch (user had a Primary Join Rate that was directly linked to a renewal rate)
			when newRate.linkedNonRenewalRateID = existingRate.rateID then 'linkedRateMatch'
			--linkedJoinRateMatch (user had a Join rate linked to the same Primary Join Rate as this Renewal Rate)
			when existingRate.isRenewalRate = 0 and newRate.linkedNonRenewalRateID = existingRate.linkedNonRenewalRateID then 'linkedJoinRate_linkedRenewalRateMatch'
			-- fallbackRateMatch (direct fallback of existing rate -- works for both join and renewal rates )
			when newRate.rateID = existingRate.fallbackRenewalRateID then 'fallbackRateMatch'
			-- linkedJoinRateFallbackRateMatch of linked join rate (covers both when user had a renewal rate or a secondary join rates)
			when newRate.rateID = linkedJoinRateOfExistingRate.fallbackRenewalRateID then 'linkedJoinRate_FallbackRateMatch'
			-- favors the rates with the least number of qualified records
			else 'exclusivityRank'
		end,
		0 as generateWarning


	from sub_subscriptions subs
	inner join dbo.sub_rateSchedules as rs
		on rs.scheduleID = subs.scheduleID
		and rs.status = 'A'
		and subs.subscriptionID = @subscriptionID
	inner join dbo.sub_rates as newRate on newRate.scheduleID = rs.scheduleID and newRate.status = 'A' and newRate.isRenewalRate = 1
		and getdate() between newRate.rateAFStartDate and dateadd(day, datediff(day, 0, newRate.rateAFEndDate)+1, 0)
	inner join dbo.sub_rateFrequencies rf on rf.rateID = newRate.rateID
	inner join dbo.sub_frequencies f on rf.frequencyID = f.frequencyID
	INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints srfrp ON srfrp.siteResourceID = newRate.siteResourceID
		AND srfrp.functionID = @FID
	INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp on srfrp.rightPrintID = gprp.rightPrintID
	inner join ams_members m
		on m.groupPrintID = gprp.groupPrintID
		and m.memberID = @memberID
	inner join (
		select r.rateID, row_number() over (order by count(*)) as exclusivityRank
		from dbo.sub_rateSchedules as rs
		inner join dbo.sub_rates as r 
			on r.scheduleID = rs.scheduleID 
			and rs.scheduleID = @scheduleID
			and r.status = 'A'
			and r.isRenewalRate = 1
			and rs.status = 'A'
		INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints srfrp 
			ON srfrp.siteResourceID = r.siteResourceID
			AND srfrp.functionID = @FID
		INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp 
			on srfrp.rightPrintID = gprp.rightPrintID
		inner join ams_members m
			on m.groupPrintID = gprp.groupPrintID
			and m.memberID = m.activeMemberID
		group by r.rateID
	) as exclusivityTable
	on exclusivityTable.rateID = newRate.rateID

	left outer join sub_subscribers ss
		inner join dbo.sub_rateFrequencies rf2 on rf2.rfid = ss.rfid
		inner join dbo.sub_rates existingRate on existingRate.rateID = rf2.rateID
		left outer join dbo.sub_rates linkedJoinRateOfExistingRate on existingRate.linkedNonRenewalRateID = linkedJoinRateOfExistingRate.rateID
		--left outer join dbo.sub_rates linkedRenewRatesOflinkedJoinRateOfExistingRate on linkedRenewRatesOflinkedJoinRateOfExistingRate.linkedNonRenewalRateID = linkedJoinRateOfExistingRate.rateID
	on ss.subscriberID = @currentSubscriberID
	order by 
		--	sameRateMatch (user already had a renewal rate)
		case when newRate.rateID = existingRate.rateID then 1 else 0 end desc,
		--linkedRenewalRateMatch (user had a Primary Join Rate that was directly linked to a renewal rate)
		case when newRate.linkedNonRenewalRateID = existingRate.rateID then 1 else 0 end desc,
		--linkedJoinRate_linkedRenewalRateMatch (user had a Join rate linked to the same Primary Join Rate as this Renewal Rate)
		case when existingRate.isRenewalRate = 0 and newRate.linkedNonRenewalRateID = existingRate.linkedNonRenewalRateID then 1 else 0 end desc,
		-- fallbackRateMatch (direct fallback of existing rate -- works for both join and renewal rates )
		case when newRate.rateID = existingRate.fallbackRenewalRateID then 1 else 0 end desc,
		-- linkedJoinRate_FallbackRateMatch of linked join rate (covers both when user had a renewal rate or a secondary join rates)
		case when newRate.rateID = linkedJoinRateOfExistingRate.fallbackRenewalRateID then 1 else 0 end desc,
		-- favors the rates with the least number of qualified records
		exclusivityTable.exclusivityRank,
		--sameFrequencyMatch
		case when rf.frequencyID = rf2.frequencyID then 1 else 0 end desc,
		--fullRateMatch
		case when f.frequencyName = 'Full' then 1 else 0 end desc


	update @BestRenewalRateInfo set
		generateWarning = 1
	where selectionMethod in ('exclusivityRank')

	

	RETURN 
END
GO