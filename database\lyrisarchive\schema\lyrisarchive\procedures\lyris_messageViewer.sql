ALTER PROCEDURE [dbo].[lyris_messageViewer]
@messageid int,
@lists varchar(max),
@AttachedMessage varchar(max),
@AttachmentID int,
@AttachmentPath varchar(100)

AS

SET NOCOUNT ON

-- global vars
DECLARE @tmpIndex int

-- get message info based on msgID and lists
DECLARE @qry varchar(max)
SELECT @qry = 'select top 1 messageid_, hdrall_, body_, list_ FROM messages_ '
SELECT @qry = @qry + 'where messageid_ = ' + CAST(@messageid as varchar(10)) + ' '
SELECT @qry = @qry + 'and list_ in (' + @lists + ')  and isVisible=1'

-- put message into temp table
CREATE TABLE #tmpMessage (
	tblmessageid_ int, 
	tblhdrall_ varchar(max),
	tblbody_ varchar(max),
	tbllist_ varchar(60),
	spMimeMessage varchar(max) NULL,
	objsubject varchar(200) NULL,
	objnumattachedmessages int NULL,
	objnumattachments int NULL,
	objnumto int NULL,
	objnumcc int NULL,
	objmsgfrom varchar(200) NULL,
	objmsgfromaddress varchar(200) NULL,
	objemaildate datetime NULL,
	objhtmlbody varchar(max) NULL,
	objtextbody varchar(max) NULL
)
INSERT INTO #tmpMessage (tblmessageid_, tblhdrall_, tblbody_, tbllist_)
EXEC(@qry)

-- Create holding tables
DECLARE @tmpAttachments TABLE (attfilename varchar(500), attfilesize bigint)
DECLARE @tmpAttachedMsgs TABLE (subject varchar(200))
DECLARE @tmpAttachedMsgList TABLE (autoID int IDENTITY(1,1), attachID int)

-- set unlock code for mailman
DECLARE @unlockCode varchar(50)
SELECT @unlockCode = 'ATrialSmithMAIL_CaMM1czz6T3w'

-- create objects and unlock component
DECLARE @objMail int, @objWebMail int, @isUnlocked int
EXEC sp_OACreate 'ChilkatWebMail2.WebEmail2', @objMail OUTPUT
EXEC sp_OACreate 'ChilkatWebMail2.WebMailMan2', @objWebMail OUTPUT
EXEC sp_OAMethod @objWebMail, 'UnlockComponent', @isUnlocked OUTPUT, @unlockCode

-- construct whole message
DECLARE @lf char(1), @crlf char(2)
SELECT @lf = char(10)
SELECT @crlf = char(13) + @lf
UPDATE #tmpMessage SET spMimeMessage = replace(replace(tblhdrall_ + @crlf + @crlf + tblbody_,@crlf,@lf),@lf,@crlf)

-- set from mime text
DECLARE @MimeMessage varchar(max)
SELECT @MimeMessage = spMimeMessage FROM #tmpMessage
EXEC sp_OAMethod @objMail, 'setFromMimeText', NULL, @MimeMessage

-- if attachedmessage is not null, get the real message to show
IF @AttachedMessage IS NOT NULL
BEGIN
	-- put list into table
	INSERT INTO @tmpAttachedMsgList (AttachID)
	SELECT ListItem
	FROM dbo.fn_NumberListToTable(@AttachedMessage)

	-- loop over list to get the final attached message
	DECLARE @minAutoID int, @realAttachID int, @newobjMail int
	SELECT @minAutoID = min(autoID) from @tmpAttachedMsgList
	WHILE @minAutoID IS NOT NULL
	BEGIN
		SELECT @realAttachID = attachID from @tmpAttachedMsgList where autoID = @minAutoID
		
		EXEC sp_OAMethod @objMail, 'GetAttachedMessage', @newobjMail OUTPUT, @realAttachID
		SELECT @objMail = @newobjMail
	
		SELECT @minAutoID = min(autoID) from @tmpAttachedMsgList WHERE autoID > @minAutoID
	END
END

-- if attachmentID is null, we are getting the message info
IF @AttachmentID IS NULL
BEGIN
	-- get properties
	DECLARE @subject varchar(200), @numattachedmessages int, @numattachments int,
		@msgfrom varchar(200), @numto int, @numcc int, @msgfromaddress varchar(200),
		@emaildate datetime
	EXEC sp_OAGetProperty @objMail, 'subject', @subject OUTPUT
	EXEC sp_OAGetProperty @objMail, 'numattachedmessages', @numattachedmessages OUTPUT
	EXEC sp_OAGetProperty @objMail, 'numattachments', @numattachments OUTPUT
	EXEC sp_OAGetProperty @objMail, 'from', @msgfrom OUTPUT
	EXEC sp_OAGetProperty @objMail, 'numto', @numto OUTPUT
	EXEC sp_OAGetProperty @objMail, 'numcc', @numcc OUTPUT
	EXEC sp_OAGetProperty @objMail, 'fromaddress', @msgfromaddress OUTPUT
	EXEC sp_OAGetProperty @objMail, 'emaildate', @emaildate OUTPUT

	-- get properties that are really methods
	DECLARE @tmpMessageHTML TABLE (htmlbody varchar(max))
		INSERT INTO @tmpMessageHTML (htmlbody)
		EXEC sp_OAMethod @objMail, 'gethtmlbody'
	DECLARE @tmpMessageTEXT TABLE (textbody varchar(max))
		INSERT INTO @tmpMessageTEXT (textbody)
		EXEC sp_OAMethod @objMail, 'getplaintextbody'

	-- update message table
	UPDATE #tmpMessage 
	set objsubject = @subject, 
		objnumattachedmessages = @numattachedmessages,
		objnumattachments = @numattachments,
		objmsgfrom = @msgfrom,
		objnumto = @numto,
		objnumcc = @numcc,
		objmsgfromaddress = @msgfromaddress,
		objemaildate = @emaildate,
		objhtmlbody = (select htmlbody from @tmpMessageHTML),
		objtextbody = (select textbody from @tmpMessageTEXT)

	-- get the attachments
	DECLARE @attfilename varchar(500), @attfilesize bigint
	SELECT @tmpIndex = 0
	WHILE @tmpIndex < @numattachments
	BEGIN
		EXEC sp_OAMethod @objMail, 'GetAttachmentFilename', @attfilename OUTPUT, @tmpIndex
		EXEC sp_OAMethod @objMail, 'GetAttachmentSize', @attfilesize OUTPUT, @tmpIndex

		INSERT INTO @tmpAttachments (attfilename, attfilesize)
		VALUES(@attfilename, @attfilesize)

		SELECT @tmpIndex = @tmpIndex + 1
	END

	-- get the attached messages
	DECLARE @attEmail int
	SELECT @tmpIndex = 0
	WHILE @tmpIndex < @numattachedmessages
	BEGIN
		EXEC sp_OAMethod @objMail, 'getattachedmessage', @attEmail OUTPUT, @tmpIndex
			
		INSERT INTO @tmpAttachedMsgs (subject)
		EXEC sp_OAGetProperty @attEmail, 'subject'

		SELECT @tmpIndex = @tmpIndex + 1
	END

	-- Query 1: message table
	SELECT tblmessageid_, tbllist_, objsubject, objnumto, objnumcc, 
		objmsgfrom, objmsgfromaddress, objemaildate, objhtmlbody, objtextbody
	from #tmpMessage

	-- Query 2: attachments
	SELECT attfilename, attfilesize from @tmpAttachments

	-- Query 3: attached messages
	SELECT subject from @tmpAttachedMsgs
END

-- if attachmentID is gte 0, save and return the attachment info
IF @AttachmentID >= 0
BEGIN
	DECLARE @savedFile int	
	EXEC sp_OAMethod @objMail, 'SaveAttachedFile', @savedFile OUTPUT, @AttachmentID, @AttachmentPath

	-- get the attachments
	EXEC sp_OAMethod @objMail, 'GetAttachmentFilename', @attfilename OUTPUT, @AttachmentID
	EXEC sp_OAMethod @objMail, 'GetAttachmentSize', @attfilesize OUTPUT, @AttachmentID

	INSERT INTO @tmpAttachments (attfilename, attfilesize)
	VALUES(@attfilename, @attfilesize)	

	-- Query 1: attachments
	SELECT attfilename, attfilesize from @tmpAttachments
END

-- drop temp tables
DROP TABLE #tmpMessage

-- clear objects
EXEC sp_OADestroy @objWebMail
EXEC sp_OADestroy @objMail
GO
