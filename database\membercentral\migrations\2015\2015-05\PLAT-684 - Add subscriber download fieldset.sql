use membercentral
GO

CREATE PROC dbo.cms_createDefaultSubscriberDownloadFieldset
@siteID int

AS

set nocount on

DECLARE @fsid int, @fieldID int, @minEmailTypeID int, @enteredByMemberID int
DECLARE @fieldcode varchar(30), @fieldLabel varchar(100)
DECLARE @addressTypeID int, @addressType varchar(20)
DECLARE @xml varchar(max)

select @enteredByMemberID=activeMemberID from ams_members where orgid = 1 and lastname like 'System'

DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	EXEC dbo.ams_createMemberFieldset @siteID, 'Subscriber Download Standard', 'LSXPFM', 0, @enteredByMemberID, 1, @fsid OUTPUT
	EXEC dbo.ams_createMemberField @fsid, 'm_lastname', 'Last Name', '', 0, 0, @fieldID OUTPUT
	EXEC dbo.ams_createMemberField @fsid, 'm_firstname', 'First Name', '', 0, 0, @fieldID OUTPUT
	EXEC dbo.ams_createMemberField @fsid, 'm_membernumber', 'Member Number', '', 0, 0, @fieldID OUTPUT
	EXEC dbo.ams_createMemberField @fsid, 'm_company', 'Company', '', 0, 0, @fieldID OUTPUT

	EXEC dbo.ams_createMemberField @fsid, 'ma_0_address1', 'Billing Address', '', 0, 0, @fieldID OUTPUT
	EXEC dbo.ams_createMemberField @fsid, 'ma_0_city', 'Billing City', '', 0, 0, @fieldID OUTPUT
	EXEC dbo.ams_createMemberField @fsid, 'ma_0_stateprov', 'Billing State', '', 0, 0, @fieldID OUTPUT
	EXEC dbo.ams_createMemberField @fsid, 'ma_0_postalcode', 'Billing Postal Code', '', 0, 0, @fieldID OUTPUT
	EXEC dbo.ams_createMemberField @fsid, 'ma_0_country', 'Billing Country', '', 0, 0, @fieldID OUTPUT

	select @minEmailTypeID = min(met.emailTypeID) 
		from dbo.sites as s 
		inner join dbo.ams_memberEmailTypes as met on met.orgID = s.orgID
		where s.siteid = @siteID
	while @minEmailTypeID is not null BEGIN
		select @fieldcode = 'me_' + cast(met.emailTypeID as varchar(4)) + '_email',
			   @fieldLabel = met.emailType
		from dbo.sites as s 
		inner join dbo.ams_memberEmailTypes as met on met.orgID = s.orgID
		where s.siteid = @siteid
		and met.emailTypeID = @minEmailTypeID
		      
		EXEC dbo.ams_createMemberField @fsid, @fieldcode, @fieldLabel, '', 0, 0, @fieldID OUTPUT

		select @minEmailTypeID = min(met.emailTypeID) 
			from dbo.sites as s 
			inner join dbo.ams_memberEmailTypes as met on met.orgID = s.orgID
			where s.siteid = @siteID 
			and met.emailTypeID > @minEmailTypeID
	END


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[enableSiteFeature]
	@siteID int,
	@toolTypeList varchar(1000)
AS


declare @tblTools TABLE (toolType varchar(100))
insert into @tblTools (toolType)
select listItem from dbo.fn_varCharListToTable(@toolTypeList,',')


declare @applicationInstanceID int
select @applicationInstanceID = applicationInstanceID
	from dbo.cms_applicationInstances
	where siteID = @siteID
	and applicationInstanceName = 'admin'


declare @toolType varchar(100)
SELECT @toolType = min(toolType) from @tblTools
WHILE @toolType is not null BEGIN

	-- Accrual Accounting
	if @toolType = 'AccrualAccounting' begin
		declare @orgID int, @GLAccountID int
		select @orgID = orgID from dbo.sites where siteID = @siteID

		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''useAccrualAccounting'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=5, @accountName='Deferred Revenue Accounts', @accountCode='', @GLCode='DEFERREDREVENUE', @parentGLAccountID=null, @invoiceProfileID=null, @isSystemAccount=1, @isTaxable=0, @invoiceContentID=null, @deferredGLAccountID=null, @GLAccountID=@GLAccountID output
		EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=5, @accountName='Deferred Sales Tax Accounts', @accountCode='', @GLCode='DEFERREDTAX', @parentGLAccountID=null, @invoiceProfileID=null, @isSystemAccount=1, @isTaxable=0, @invoiceContentID=null, @deferredGLAccountID=null, @GLAccountID=@GLAccountID output

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType in ('AccrualScheduleReport','DeferredIncomeAnalysisReport')
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- appt tracker
	if @toolType = 'AppointmentTrackerAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showApptTracker'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- email blast
	if @toolType = 'EmailBlast' begin
		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- member documents
	if @toolType = 'MemberDocs' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showMemberDocuments'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		declare @resourceTypeID int, @sectionID int, @parentSectionID int
		select @resourceTypeID = resourceTypeID from dbo.cms_siteResourceTypes where resourceType = 'ApplicationCreatedSection'
		select @sectionID = sectionID from dbo.cms_pageSections where siteID = @siteID and sectionName = 'MCAMSMemberDocuments'
		IF @sectionID is null begin
			select @parentSectionID = sectionID from dbo.cms_pageSections where sectionName = 'root' and parentSectionID is null and siteID = @siteID
			exec dbo.cms_createPageSection @siteID, @resourceTypeID, null, null, null, @parentSectionID, 'MCAMSMemberDocuments', 'MCAMSMemberDocuments', 0, @sectionID output
		end
	end	

	-- member history
	if @toolType = 'MemberHistoryAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showMemberHistory'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID
	end

	-- referrals
	if @toolType = 'ReferralsAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showReferrals'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- relationships
	if @toolType = 'RelationshipAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showRelationships'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- reports
	if @toolType = 'Reports' begin
		declare @siteCode varchar(10)
		select @sitecode = sitecode from dbo.sites where siteID = @siteID

		insert into dbo.admin_siteToolRestrictions (toolTypeID, siteID)
		select tooltypeid, @siteID 
		from dbo.admin_toolTypes 
		where toolCFC like 'Reports.custom.' + @sitecode + '.%'
		or (toolCFC like 'Reports.%' and left(toolCFC,15) <> 'Reports.custom.')
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- subscriptions
	if @toolType = 'SubscriptionAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showSubscriptions'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = 'SubRenewalAdmin'
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID

		-- subsriber download fieldset
		EXEC cms_createDefaultSubscriberDownloadFieldset @siteID=@siteID
	end

	-- tasks
	if @toolType = 'NotesAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showNotes'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	SELECT @toolType = min(toolType) from @tblTools where toolType > @toolType
END


-- refresh and assign resources
exec dbo.createadminsuite @siteid


-- these need to be added after resources are there (createadminsuite)
IF EXISTS (select toolType from @tblTools where toolType = 'AppointmentTrackerAdmin')
	exec dbo.cms_createDefaultAppointmentCategories @siteID=@siteid, @contributingMemberID=461530

IF EXISTS (select toolType from @tblTools where toolType = 'EmailBlast')
	exec dbo.cms_createDefaultEmailBlastCategories @siteID=@siteID

IF EXISTS (select toolType from @tblTools where toolType = 'MemberHistoryAdmin')
	exec dbo.cms_createDefaultHistoryAdminCategories @siteID=@siteID, @contributingMemberID=461530

IF EXISTS (select toolType from @tblTools where toolType = 'RelationshipAdmin')
	exec dbo.cms_createDefaultRelationshipCategories @siteID=@siteID, @contributingMemberID=461530

IF EXISTS (select toolType from @tblTools where toolType = 'NotesAdmin')
	exec dbo.cms_createDefaultNotesCategories @siteID=@siteid, @contributingMemberID=461530

RETURN 0
GO

-- add fieldset to sites with AMS
declare @siteID int
select @siteID = min(stre.siteID)
	from dbo.admin_siteToolRestrictions as stre
	inner join dbo.admin_toolTypes as tt on tt.toolTypeID = stre.toolTypeID
	where tt.toolType = 'SubRenewalAdmin'
while @siteID is not null begin
	EXEC dbo.cms_createDefaultSubscriberDownloadFieldset @siteID=@siteID

	select @siteID = min(stre.siteID)
		from dbo.admin_siteToolRestrictions as stre
		inner join dbo.admin_toolTypes as tt on tt.toolTypeID = stre.toolTypeID
		where tt.toolType = 'SubRenewalAdmin'
		and stre.siteID > @siteID
end
GO

