USE [memberCentral]
GO
/****** Object:  View [dbo].[vw_Subscriber Rate Frequency missing]    Script Date: 05/01/2014 08:58:29 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE VIEW [dbo].[vw_Subscriber Rate Frequency missing]
AS
SELECT     subscriberID, memberID, subscriptionID, RFID, subStartDate, subEndDate, graceEndDate, dateRecorded, recordedByMemberID, parentSubscriberID, PCFree, 
                      GLAccountID, offerRescindDate, directLink, modifiedRate, lastPrice, paymentStatusID, subActivationID, statusID, rootSubscriberID, subscriberPath, payProfileID, 
                      directLinkCode
FROM         dbo.sub_subscribers
WHERE     (RFID IS NULL)

GO







USE [memberCentral]
GO
/****** Object:  StoredProcedure [dbo].[job_runDailyMaintenanceChecks]    Script Date: 05/01/2014 09:15:31 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROC [dbo].[job_runDailyMaintenanceChecks]
AS

-- Do not lock anything, and do not get held up by any locks.
SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

DECLARE @tier varchar(20), @errorSubjectRoot varchar(100), @errorSubjectRootNonDev varchar(100), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)
DECLARE @tableHTML VARCHAR(MAX)

/* variables */
SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '***********'
IF @@SERVERNAME = 'DEV04\PLATFORM2008' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'DEV03\PLATFORM2008' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'STAGING01\PLATFORM2008' BEGIN
	SET @tier = 'STAGING'
	SET @smtpserver = 'mail.trialsmith.com'
END
SET @errorSubjectRoot = @tier + ' - Developer Needed - '
SET @errorSubjectRootNonDev = @tier + ' - Non-Developer Needed - '


/* CHECK - subscription child parent mismatch */
BEGIN TRY
	IF EXISTS (select * from dbo.[vw_Child Parent Subscriber Mismatch]) BEGIN
		SET @errorSubject = @errorSubjectRoot + 'child/parent subscription mismatch'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are child subscriptions linked to parent subscriptions belonging to another member. Run the [vw_Child Parent Subscriber Mismatch] view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR child/parent subscription mismatch'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* CHECK - duplicate subscriptions */
BEGIN TRY
	IF EXISTS (select * from dbo.[vw_Duplicate Subscriptions]) BEGIN
		SET @tableHTML = '' + 
			replace(Stuff((
				SELECT '|' + orgcode + char(9) + memberName + ' (' + membernumber + ') - ' + subscriptionName + ' (' + statusName + ')' AS [text()]
				from dbo.[vw_Duplicate Subscriptions]
				order by orgcode, membername, subscriptionName
				FOR XML PATH ('')
			),1,1,''),'|',char(13)+char(10))
		SET @errorSubject = @errorSubjectRootNonDev + 'Member with Duplicate Subscriptions'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are members with duplicate subscriptions found in the [vw_Duplicate Subscriptions] view.' + @crlf + @crlf + @tableHTML
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Member with Duplicate Subscriptions'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* CHECK - subscribers with NULL RFIDs  */
BEGIN TRY
	IF EXISTS (select * from dbo.[vw_Subscriber Rate Frequency missing]) BEGIN
		SET @tableHTML = '' + 
			replace(Stuff((
				SELECT '|' + orgcode + char(9) + m2.lastname + ', ' + m2.firstname + isnull(' ' + nullif(m2.middleName,''),'') + ' (' + m2.membernumber + ') - ' + subscriptionName + ' (' + statusName + ')' AS [text()]
					from dbo.sub_subscribers as sub
					inner join dbo.[vw_Subscriber Rate Frequency missing] sub2 on sub2.subscriberID = sub.subscriberID
					inner join dbo.sub_subscriptions as s on s.subscriptionID = sub.subscriptionID
					inner join dbo.sub_statuses as ss on ss.statusID = sub.statusID
					inner join dbo.ams_members as m on m.memberID = sub.memberID
					inner join dbo.ams_members as m2 on m2.memberID = m.activememberID
					inner join dbo.organizations as o on o.orgID = m2.orgID
				order by orgcode, m2.lastname, subscriptionName
				FOR XML PATH ('')
			),1,1,''),'|',char(13)+char(10))
		SET @errorSubject = @errorSubjectRoot + 'Member Subscriptions with NULL Rate Frequency'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are members with NULL Rate Frequencies in their subscriptions found in the [vw_Subscriber Rate Frequency missing] view.' + @crlf + @crlf + @tableHTML
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Member Subscriptions with NULL Rate Frequency'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* CHECK - duplicate member numbers */
BEGIN TRY
	IF EXISTS (select * from dbo.[vw_Duplicate Member Numbers]) BEGIN
		SET @errorSubject = @errorSubjectRoot + 'Duplicate Member Numbers'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are members that share the same member number for an org. Run the [vw_Duplicate Member Numbers] view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Duplicate Member Numbers'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* CHECK - duplicate member site defaults */
BEGIN TRY
	IF EXISTS (select * from dbo.[vw_Duplicate Member Site Defaults]) BEGIN
		SET @errorSubject = @errorSubjectRoot + 'Duplicate Member Site Defaults'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are members that have duplicate active site defaults for a site. Run the [vw_Duplicate Member Site Defaults] view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Duplicate Member Site Defaults'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* CHECK - duplicate search cms_documentversions */
BEGIN TRY
	IF EXISTS (select * from search.dbo.[vw_Duplicate Document Versions]) BEGIN
		SET @errorSubject = @errorSubjectRoot + 'search.cms_documentversions has duplicate entries'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are document versions that appear multiple times in the search table. Run the search.dbo.[vw_Duplicate Document Versions] view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR search.cms_documentversions has duplicate entries'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* CHECK - Future active subscriptions */
BEGIN TRY
	IF EXISTS (select * from dbo.[vw_Active Subscribers Starting in Future])  BEGIN
		SET @errorSubject = @errorSubjectRoot + 'future dated active subscriptions'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are members with active subscriptions that have not started yet. Run the [vw_Active Subscribers Starting in Future] view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR future dated active subscriptions'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* CHECK - Member data org mismatch */
BEGIN TRY
	IF EXISTS (select * from dbo.[vw_memberDataOrgMismatch]) BEGIN
		SET @errorSubject = @errorSubjectRoot + 'Org Member Data Mismatch'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are org mismatches in member data. Run the [vw_memberDataOrgMismatch] view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Org Member Data Mismatch'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* CHECK - org permission mismatch */
BEGIN TRY
	IF EXISTS (select * from dbo.[vw_permissionsOrgMismatch]) BEGIN
		SET @errorSubject = @errorSubjectRoot + 'Org Member Permission Mismatch'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are org mismatches in member permissions. Run the [vw_permissionsOrgMismatch] view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Org Member Permission Mismatch'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* CHECK - group assignment rules non existent conditions */
BEGIN TRY
	IF EXISTS (select * from dbo.[vw_Group Assignment Rules with non existant conditions]) BEGIN
		SET @tableHTML = 'orgcode'+char(9)+'ruleName'+@crlf+
			replace(Stuff((
				SELECT DISTINCT ',' + orgcode+char(9)+char(9) + ruleName AS [text()]
				from dbo.[vw_Group Assignment Rules with non existant conditions]
				FOR XML PATH ('')
			),1,1,''),',',@crlf)
		SET @errorSubject = @errorSubjectRootNonDev + 'Group Rules with non-existent conditions'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are group assignment rules with with non-existent conditions found in the [vw_Group Assignment Rules with non existant conditions] view.' + @crlf + @crlf + @tableHTML
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Group Rules with non-existent conditions'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* CHECK - Org Cache Enabled Status */
BEGIN TRY
	IF EXISTS (select * from dbo.organizations where cache_perms_status <> 'enabled') BEGIN
		SET @errorSubject = @errorSubjectRoot + 'cache_perms_status not enabled'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'cache_perms_status is not enabled for some organizations.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR cache_perms_status not enabled'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* CHECK - invalid or mismatched pro license statuses */
BEGIN TRY
	IF EXISTS (select * from dbo.[vw_memberProfessionalLicensesWithMissingOrInvalidStatus]) BEGIN
		SET @errorSubject = @errorSubjectRoot + 'invalid or mismatched pro license statuses'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are members with professional licenses with no status or linked to the status of a different license type. Run the [vw_memberProfessionalLicensesWithMissingOrInvalidStatus] view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR invalid or mismatched pro license statuses'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* CHECK - duplicate entries in siteResourceFunctionRightPrints cache */
-- this was changed to not use the view because it would get hung and run for HOURS for no fucking reason
BEGIN TRY
	IF EXISTS (
		select s.siteID, s.sitecode, srt.resourceType, sr.siteResourceID, srfrp.functionID, min(siteResourceFunctionRightPrintID) as minSiteResourceFunctionRightPrintID
		from dbo.cache_perms_siteResourceFunctionRightPrints srfrp
		inner join cms_siteResources sr on sr.siteResourceID = srfrp.siteResourceID
		inner join cms_siteResourceTypes srt on sr.resourceTypeID = srt.resourceTypeID
		inner join sites s on s.siteID = sr.siteID
		group by s.siteID, s.sitecode, srt.resourceType, sr.siteResourceID, srfrp.functionID
		having count(*) > 1
	) BEGIN
		SET @errorSubject = @errorSubjectRoot + 'duplicate entries in the siteResourceFunctionRightPrints cache'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are duplicate entries in the siteResourceFunctionRightPrints cache. Run the [vw_Duplicate Entries in siteResourceFunctionRightPrints cache] view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR duplicate entries in the siteResourceFunctionRightPrints cache'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* CHECK - Subscriptions Setup Issues Report */
BEGIN TRY
	EXEC dbo.sub_setupIssuesReport
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Running Subscription Setup Issues Report'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* CHECK - events setup issues report */
BEGIN TRY
	EXEC dbo.ev_setupIssuesReport
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Running Events Setup Issues Report'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* CHECK - multiple member network profiles */
BEGIN TRY
	IF EXISTS (select * from dbo.[vw_Members with multiple active member network profiles]) BEGIN
		SET @tableHTML = 'mnpCount'+char(9)+'siteid'+char(9)+'networkid'+char(9)+'memberid'+@crlf+
			replace(Stuff((
				SELECT DISTINCT ',' + cast(mnpCount as varchar(5)) + char(9) + cast(siteid as varchar(10)) + char(9) + cast(networkid as varchar(10)) + char(9) + cast(memberid as varchar(10)) AS [text()]
				from dbo.[vw_Members with multiple active member network profiles]
				FOR XML PATH ('')
			),1,1,''),',',@crlf)
		SET @errorSubject = @errorSubjectRoot + 'Members with multiple active mnp'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'The following members are linked to multiple active member network profiles in the same network found in the [vw_Members with multiple active member network profiles] view:' + @crlf + @crlf + @tableHTML
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Members with multiple active mnp'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* CHECK - Depo Accounts linked to multiple Network Profiles */
BEGIN TRY
	IF EXISTS (select * from dbo.[vw_Depo Accounts Linked To Multiple Network Profiles]) BEGIN
		SET @errorSubject = @errorSubjectRoot + 'Depo Accounts linked to multiple profiles'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are depo accounts linked to multiple active network profiles. This is usually an indication that one TS Admin account is linked to two accounts in MemberCentral under different networks. Run the [vw_Depo Accounts Linked To Multiple Network Profiles] view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Depo Accounts linked to multiple profiles'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* CHECK - Paid Invoices with Amounts Due */
BEGIN TRY
	IF EXISTS (select * from membercentral.dbo.[vw_Paid Invoices With Amount Due]) BEGIN
		SET @errorSubject = @errorSubjectRoot + 'paid invoices with amount due'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are paid invoices with amounts due. These need to be set to closed. Run the [vw_Paid Invoices With Amount Due] view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR paid invoices with amount due'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* CHECK - Payment History with no Payment Transaction */
BEGIN TRY
	IF EXISTS (select * from dbo.[vw_Payment History Without Payment Transaction]) BEGIN
		SET @errorSubject = @errorSubjectRoot + 'Payment History with No Payment Transaction'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are successful payments without payment transactions. Run the [vw_Payment History Without Payment Transaction] view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Payment History with No Payment Transaction'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* CHECK - Payments with bad cache allocated amounts */
BEGIN TRY
	IF EXISTS (select * from dbo.[vw_Payments With Bad Cached Allocated Amounts]) BEGIN
		SET @errorSubject = @errorSubjectRoot + 'Payments with bad cached allocated amounts'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are payments with bad cached allocated amounts. Run the [vw_Payments With Bad Cached Allocated Amounts] view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Payments with bad cached allocated amounts'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* CHECK - Payments with bad cache refundable amounts */
BEGIN TRY
	IF EXISTS (select * from dbo.[vw_Payments With Bad Cached Refundable Amounts]) BEGIN
		SET @errorSubject = @errorSubjectRoot + 'Payments with bad cached refundable amounts'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are payments with bad cached refundable amounts. Run the [vw_Payments With Bad Cached Refundable Amounts] view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Payments with bad cached refundable amounts'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* CHECK - Payments with misallocated funds */
BEGIN TRY
	IF EXISTS (select * from dbo.[vw_Payments MisAllocated]) BEGIN
		SET @errorSubject = @errorSubjectRoot + 'Payments with misallocated funds'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are payments with misallocated funds. Run the [vw_Payments MisAllocated] view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Payments with misallocated funds'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* CHECK - disallowed multiple values for custom fields */
BEGIN TRY
	IF EXISTS (select * from dbo.[vw_Members with Multiple Values for Custom Field]) BEGIN
		SET @errorSubject = @errorSubjectRoot + 'Multiple Custom Field Values'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are members with multiple values for the same custom field. Run the [vw_Members with Multiple Values for Custom Field] view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Multiple Custom Field Values'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


RETURN 0
