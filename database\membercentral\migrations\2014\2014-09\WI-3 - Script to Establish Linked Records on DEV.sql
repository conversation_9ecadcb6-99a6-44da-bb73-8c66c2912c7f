-- DEV 
use datatransfer
GO

IF OBJECT_ID('dbo.tmp_WIFirms', 'U') IS NOT NULL
  DROP TABLE dbo.tmp_WIFirms

use membercentral
GO

declare @orgID int
select @orgID = orgID from sites where sitecode = 'WI'

select 
	tmp.company, vw.[Office Address_address1], vw.[Office Address_address2], 
	vw.[Office Address_city], s.stateID, vw.[Office Address_postalCode], 
	vw.[Office Address_country], vw.[Contact Type],
	ROW_NUMBER() OVER (ORDER BY tmp.company) as row
	into datatransfer.dbo.tmp_WIFirms
from (
	select
		m.company, min(m.memberID) as memberID
	from 
		membercentral.dbo.ams_members as m
		inner join membercentral.dbo.vw_memberData_WI as vw2 on vw2.memberid = m.memberid
	where 
		len(m.company) > 0
		and m.company <> 'Attorney at Law'
	group by 
		m.company
	having count(*) > 1
) as tmp
inner join membercentral.dbo.vw_memberData_WI as vw on vw.memberid = tmp.memberid
left outer join membercentral.dbo.ams_states as s on s.code = vw.[Office Address_stateprov] and s.countryID = 1
GO

ALTER TABLE datatransfer.dbo.tmp_WIFirms ADD memberid int NULL
GO

declare 
	@companyRTID int, 
	@IndivRTID int, 
	@AttorneyRTRTID int, 
	@AttorneyRelTID int, 
	@ParalegalRTRTID int, 
	@ParalegalRelTID int,
	@orgID int

declare @attorneyLinkTbl as table (memberID int)

declare @paralegalLinkTbl as table (memberID int)
		
select @orgID = orgID from sites where sitecode = 'WI'

select @companyRTID = recordTypeID from dbo.ams_recordTypes where orgID = @orgID and recordTypeCode = 'LawFirm'

select @IndivRTID = recordTypeID from dbo.ams_recordTypes where orgID = @orgID and recordTypeCode = 'Individual'

select @AttorneyRelTID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgID and relationshipTypeCode = 'Attorney'

select 
	@AttorneyRTRTID = recordTypeRelationshipTypeID 
from 
	dbo.ams_recordTypesRelationshipTypes 
where 
	masterRecordTypeID = @companyRTID 
	and childRecordTypeID = @IndivRTID 
	and relationshipTypeID = @AttorneyRelTID

select @ParalegalRelTID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgID and relationshipTypeCode = 'Paralegal'

select 
	@ParalegalRTRTID = recordTypeRelationshipTypeID 
from 
	dbo.ams_recordTypesRelationshipTypes 
where 
	masterRecordTypeID = @companyRTID 
	and childRecordTypeID = @IndivRTID 
	and relationshipTypeID = @ParalegalRelTID

insert into @attorneyLinkTbl
select 
	vw.memberID
from
	membercentral.dbo.vw_memberData_WI as vw 
	inner join 		membercentral.dbo.ams_members m on
		vw.memberid = m.memberid
		and m.orgID = @orgID
		and m.memberid = m.activeMemberID
		and m.status <> 'D'
where 
	(vw.[Contact Type] in ('Plaintiff Attorney', 'Defense Attorney', 'Retired Attorney', 'Prospect', 'Former Member')
		OR vw.[Contact Type] is null)
group by
	vw.memberID

insert into @paralegalLinkTbl
select 
	vw.memberID
from
	membercentral.dbo.vw_memberData_WI as vw 
	inner join 		membercentral.dbo.ams_members m on
		vw.memberid = m.memberid
		and m.orgID = @orgID
		and m.memberid = m.activeMemberID
		and m.status <> 'D'
where 
	vw.[Contact Type] in ('Paralegal')
group by
	vw.memberID

BEGIN TRAN

declare 
	@row int, @firstname varchar(75), 
	@company varchar(300), @ct varchar(200), 
	@membernumber varchar(40), @memberID int, 
	@rc int, @recordTypeID int, @RTID int

select @row = min(row) from datatransfer.dbo.tmp_WIFirms

while @row is not null begin

	select	
		@memberID = null, @ct = null, 
		@firstname = null, @company = null, 
		@membernumber = null, @RTID = null, 
		@recordTypeID = null

	select 
		@company = company, 
		@ct = [Contact Type]
	from 
		datatransfer.dbo.tmp_WIFirms
	where 
		row = @row

	select @firstname = 'Firm'
	select @membernumber = 'FIRM' + RIGHT('00000' + cast(@row as varchar(4)),5)
	select @recordTypeID = @companyRTID
--	select @RTID = @AttorneyRTRTID

	exec @rc = dbo.ams_createMember 
			@orgID=@orgID, 
			@memberTypeID=2, 
			@prefix='', 
			@firstname=@firstname, 
			@middlename='', 
			@lastname='Account',
			@suffix='', 
			@professionalsuffix='', 
			@company=@company, 
			@memberNumber=@membernumber, 
			@status='A', 
			@memberID=@memberID OUTPUT
		IF @@ERROR <> 0 or @rc <> 0 or @memberID = 0 goto on_error

	update 
		ams_members
	set 
		recordTypeID = @recordTypeID
	where 
		memberID = @memberID
	IF @@ERROR <> 0 goto on_error

	update 
		datatransfer.dbo.tmp_WIFirms
	set 
		memberID = @memberID
	where 
		row = @row
		IF @@ERROR <> 0 goto on_error

	update 
		ams_members
	set 
		recordTypeID = @IndivRTID
	where 
		orgID = @orgID
		and memberid = activeMemberID
		and status <> 'D'
		and company = @company
		and recordTypeID is null
	IF @@ERROR <> 0 goto on_error

	-- 	Linking role of "Attorney"
	insert into dbo.ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive)
	select 
		@AttorneyRTRTID, @memberID, m.memberid, 1
	from 
		membercentral.dbo.ams_members m
		inner join @attorneyLinkTbl al on 
			al.memberID = m.memberid
	where 
		m.orgID = @orgID
		and m.memberid = m.activeMemberID
		and status <> 'D'
		and m.company = @company
		and m.memberID <> @memberID
	IF @@ERROR <> 0 goto on_error

	-- 	Linking role of "Paralegal"
	insert into dbo.ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive)
	select 
		@ParalegalRTRTID, @memberID, m.memberid, 1
	from 
		membercentral.dbo.ams_members m
		inner join @paralegalLinkTbl pl on 
			pl.memberID = m.memberid
	where 
		m.orgID = @orgID
		and m.memberid = m.activeMemberID
		and status <> 'D'
		and m.company = @company
		and m.memberID <> @memberID
	IF @@ERROR <> 0 goto on_error

	select @row = min(row) from datatransfer.dbo.tmp_WIFirms where row > @row
END

declare @addressTypeID int
select 
	@addressTypeID = addressTypeID 
from 
	dbo.ams_memberAddressTypes 
where 
	orgID = @orgID 
	and addressTypeOrder = 1

insert into dbo.ams_memberAddresses (
	memberID, 
	addressTypeID, 
	address1, 
	address2, 
	address3, 
	city, 
	stateID, 
	postalCode, 
	countryID
)
select 
	memberID, 
	@addressTypeID, 
	isnull([Office Address_address1],''), 
	isnull([Office Address_address2],''), '', 
	isnull([Office Address_city],''), 
	stateID, 
	isnull([Office Address_postalCode],''), 1
from 
	datatransfer.dbo.tmp_WIFirms
IF @@ERROR <> 0 goto on_error

update 
	ams_members
set 
	recordTypeID = @IndivRTID
where 
	orgID = @orgID
	and memberid = activeMemberID
	and status <> 'D'
	and recordTypeID is null
IF @@ERROR <> 0 goto on_error

COMMIT TRAN
goto on_success

on_error:
	ROLLBACK TRAN
	goto on_done

on_success:
	-- queue member groups (@runSchedule=1 indicates immediate processing) 
	declare @itemGroupUID uniqueidentifier, @memberIDList varchar(max)
	select 
		@memberIDList = COALESCE(@memberIDList + ',', '') + cast(memberID as varchar(10)) 
	from 
		datatransfer.dbo.tmp_WIFirms 
	group by 
		memberID
	
	exec platformQueue.dbo.queue_processMemberGroups_insert 
			@orgID=@orgID, 
			@memberIDList=@memberIDList, 
			@conditionIDList='', 
			@runSchedule=1, 
			@itemGroupUID=@itemGroupUID OUTPUT

on_done:
	drop table datatransfer.dbo.tmp_WIFirms

GO
