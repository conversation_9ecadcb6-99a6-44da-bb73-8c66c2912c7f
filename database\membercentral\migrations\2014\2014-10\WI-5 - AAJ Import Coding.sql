use Membercentral
GO
DECLARE
	@toolTypeID int,
	@toolResourceTypeID int,
	@roleID int,
	@navigationID int,
	@resourceTypeFunctionID int,
	@orgcode varchar(5),
	@orgID int,
	@sitecode varchar(5),
	@siteID int,
	@TopTabNavigationID int,
	@level2NavigationID int,
	@level3NavigationID int,
	@rc int,
	@viewfunctionID int,
	@superAdminRoleID int,
	@viewResourceTypeFunctionID int

select @siteID = dbo.fn_getSiteIDfromSiteCode('wi')
select @orgID = dbo.fn_getOrgIDfromOrgCode('wi')

select @toolTypeID = null, @toolResourceTypeID = null

select 
	@level2NavigationID = n1.navigationID,
	@TopTabNavigationID = n2.navigationID
from 
	dbo.admin_navigation n1
	inner join admin_navigation n2 on
		n2.navigationID = n1.parentNavigationID
		and n2.navName = 'Members'
		and n2.navAreaID = 1
where 
	n1.navName = 'Subscriptions'
	and n1.navAreaID = 2

select 
	@level2NavigationID as level2NavigationID,
	@TopTabNavigationID as TopTabNavigationID

select 
	@resourceTypeFunctionID = fdn.resourceTypeFunctionID,
	@toolTypeID = tt.toolTypeID
from admin_toolTypes tt
	inner join dbo.admin_functionsDeterminingNav fdn on
		fdn.toolTypeID = tt.toolTypeID
		and tt.toolType = 'AAJImportAdmin'
	inner join dbo.admin_navigation n on
		n.navigationID = fdn.navigationID
		and n.navname = 'AAJ Import'
	inner join cms_siteResourceTypeFunctions srtf on
		srtf.resourceTypeFunctionID = fdn.resourceTypeFunctionID
	inner join cms_siteResourceFunctions srf on
		srf.functionID = srtf.functionID
		and srf.functionName = 'viewAAJImport'
	inner join cms_siteResourceTypes srt on
		srt.resourceTypeID = srtf.resourceTypeID
		and srt.resourceType = 'AAJImportAdmin'

select @resourceTypeFunctionID as resourceTypeFunctionID, @toolTypeID as toolTypeID

BEGIN TRAN

	-- restrict this tool to WI
	insert into admin_siteToolRestrictions (toolTypeID, siteID) values (@toolTypeID, @siteID)
	IF @@ERROR <> 0 GOTO on_error

	-- create level3 items
	select @level3NavigationID = null

	EXEC @rc = dbo.createAdminNavigation
		@navName='AAJ Import',
		@navDesc='AAJ Import',
		@parentNavigationID=@level2NavigationID,
		@navAreaID=3,
		@cfcMethod='showImportStart',
		@isHeader=0,
		@showInNav=1,
		@navigationID=@level3NavigationID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = dbo.createAdminFunctionsDeterminingNav
		@resourceTypeFunctionID=@resourceTypeFunctionID,
		@toolTypeID=@toolTypeID,
		@navigationID=@level3NavigationID
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

IF @@TRANCOUNT > 0 COMMIT TRAN

EXEC dbo.createAdminSuite @siteid=@siteID

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
GO
