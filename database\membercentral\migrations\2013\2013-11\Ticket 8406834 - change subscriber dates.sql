select s.subStartDate, s.subEndDate, s.graceEndDate, '
	update sub_subscribers 
	set subStartDate = ''' + convert(varchar(50),dateadd(mm,-3,s.subStartDate), 121) + ''', 
		subEndDate = ''' + convert(varchar(50),dateadd(mm,-3,s.subEndDate), 121) + ''',
		graceEndDate = ''' + convert(varchar(50),dateadd(mm,-1,s.subEndDate), 121) + '''
  	where subscriberID = ' + cast(s.subscriberID as varchar(10)) + '; 
	exec dbo.sub_createStatusBackfill 40, ' + cast(s.subscriberID as varchar(10)) + ', 1, 461530;
	'
from sub_subscribers as s
inner join sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
inner join sub_types as st on st.typeID = sub.typeID
where st.siteID = 40
and st.typeName = 'Board Titles'
and s.statusID = 1
and s.dateRecorded between '3/5/13' and '3/6/13'





