ALTER FUNCTION [dbo].[fn_getRecursiveSubscriptionsByID]
( @siteID int,
 @subMainIDList varchar(max)
)
RETURNS TABLE
AS
RETURN
(
WITH subscribers AS (
 select subscriberID, memberID, memberName, subscriptionID, typeID, typeName, subscriptionName, [status], paymentStatus,
  subStartDate, subEndDate, graceEndDate, parentSubscriberID, rfid, modifiedRate, subPrice, subActivationCode,
  CONVERT(varchar, memberID) + '.' + CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
  CAST(subscriptionName as varchar(max)) as thePathExpanded
 FROM (
 select s.subscriberID, s.memberID,
  RTRIM(mActive.lastname + ' ' + isnull(mActive.suffix, '')) + ', ' + mActive.firstname + isnull(' (' + mActive.membernumber + ')','') AS memberName,
  s.subscriptionID, t.typeID, t.typeName, sc.subscriptionName, st.statusCode as status, pst.statusCode as paymentStatus,
  s.subStartDate, s.subEndDate, s.graceEndDate, s.parentSubscriberID, s.rfid, isnull(s.modifiedRate,0) as modifiedRate, coalesce(s.modifiedRate, lastPrice, cast(0.00 as money)) as subPrice, o.subActivationCode,
  ROW_NUMBER() OVER (ORDER BY mActive.lastname, mActive.firstname, mActive.memberID, s.parentSubscriberID, s.subStartDate, s.subscriberID) AS theRow
 from dbo.sub_subscribers s
 inner join dbo.sub_statuses st on st.statusID = s.statusID
 inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID
 inner join dbo.sub_activationOptions o on o.subActivationID = s.subActivationID
 inner join dbo.sub_subscriptions sc on sc.subscriptionID = s.subscriptionID
 inner join dbo.sub_types t on t.typeID = sc.typeID and t.siteID = @siteID
 inner join dbo.ams_members m on m.memberID = s.memberID
 inner join dbo.ams_members mActive on mActive.memberID = m.activeMemberID
 inner join fn_intListToTable(@subMainIDList, ',') pid on pid.listitem = s.subscriberID
 where s.parentSubscriberID is null
 ) as x
 UNION ALL
 select subscriberID, memberID, memberName, subscriptionID, typeID, typeName, subscriptionName, [status], paymentStatus,
  subStartDate, subEndDate, graceEndDate, parentSubscriberID, rfid, modifiedRate, subPrice, subActivationCode,
  thePath + '.' + CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
  thePathExpanded = case
   	when isnull(parentSubscriberID,0) = 0 then subscriptionName
   	else thePathExpanded + ' \ ' + subscriptionName
  	end
 FROM (
 select s.subscriberID, s.memberID,
  RTRIM(mActive.lastname + ' ' + isnull(mActive.suffix, '')) + ', ' + mActive.firstname + isnull(' (' + mActive.membernumber + ')','') AS memberName,
  s.subscriptionID, t.typeID, t.typeName, sc.subscriptionName, st.statusCode as status, pst.statusCode as paymentStatus,
  s.subStartDate, s.subEndDate, s.graceEndDate, s.parentSubscriberID, s.rfid, isnull(s.modifiedRate,0) as modifiedRate, coalesce(s.modifiedRate, lastPrice, cast(0.00 as money)) as subPrice, o.subActivationCode,
  scte.thePath, scte.thePathExpanded,
  ROW_NUMBER() OVER (ORDER BY mActive.lastname, mActive.firstname, mActive.memberID, s.parentSubscriberID, s.subStartDate, s.subscriberID) AS theRow
 from dbo.sub_subscribers s
 inner join dbo.sub_statuses st on st.statusID = s.statusID
 inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID
 inner join dbo.sub_activationOptions o on o.subActivationID = s.subActivationID
 inner join dbo.sub_subscriptions sc on sc.subscriptionID = s.subscriptionID
 inner join dbo.sub_types t on t.typeID = sc.typeID and t.siteID = @siteID
 inner join dbo.ams_members m on m.memberID = s.memberID
 inner join dbo.ams_members mActive on mActive.memberID = m.activeMemberID
 INNER JOIN subscribers scte on s.parentSubscriberID = scte.subscriberID
 
 ) as y
)
select subscriberID, memberID, memberName, subscriptionID, typeID, typeName, subscriptionName, [status], paymentStatus,
  subStartDate, subEndDate, graceEndDate, parentSubscriberID, rfid, modifiedRate,subPrice, subActivationCode,
  thePath, thePathExpanded
from subscribers
)
