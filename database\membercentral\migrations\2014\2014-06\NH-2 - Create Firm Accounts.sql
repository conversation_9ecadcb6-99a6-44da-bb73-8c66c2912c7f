use membercentral
GO

-- SET ALL TO INDIV
declare @orgID int, @IndivRTID int
select @orgID = orgID from sites where sitecode = 'NH'
select @IndivRTID = recordTypeID from dbo.ams_recordTypes where orgID = @orgID and recordTypeCode = 'Individual'

UPDATE dbo.ams_members
SET recordTypeID = @IndivRTID
where orgID = @orgID
and recordTypeID is null
GO


-- VENDOR FIRMS
declare @orgID int
select @orgID = orgID from sites where sitecode = 'NH'

select tmp.company, vw.[Address_address1], vw.[Address_address2], vw.[Address_city], s.stateID, vw.[Address_postalCode], 
	vw.[Address_country], vw.[Contact Type], ROW_NUMBER() OVER (ORDER BY tmp.company) as row
into datatransfer.dbo.tmp_NHFirms
from (
	select m.company, min(m.memberID) as memberID
	from membercentral.dbo.ams_members as m
	inner join membercentral.dbo.vw_memberData_NH as vw2 on vw2.memberid = m.memberid
	where len(m.company) > 0
	and m.company <> 'Attorney at Law'
	and (left(m.membernumber,4) = 'EXHB' OR vw2.[Contact type] like '%Vendor%')
	group by m.company
	having count(*) > 1
) as tmp
inner join membercentral.dbo.vw_memberData_NH as vw on vw.memberid = tmp.memberid
left outer join membercentral.dbo.ams_states as s on s.code = vw.[Address_stateprov] and s.countryID = 1
GO

ALTER TABLE datatransfer.dbo.tmp_NHFirms ADD memberid int NULL
GO

declare @companyRTID int, @IndivRTID int, @FirmMemberRTRTID int, @FirmRelTID int, @orgID int
select @orgID = orgID from sites where sitecode = 'NH'
select @companyRTID = recordTypeID from dbo.ams_recordTypes where orgID = @orgID and recordTypeCode = 'VendorFirm'
select @IndivRTID = recordTypeID from dbo.ams_recordTypes where orgID = @orgID and recordTypeCode = 'Individual'
select @FirmRelTID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgID and relationshipTypeCode = 'Representative'
select @FirmMemberRTRTID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes where masterRecordTypeID = @companyRTID and childRecordTypeID = @IndivRTID and relationshipTypeID = @FirmRelTID


BEGIN TRAN

declare @row int, @firstname varchar(75), @company varchar(300), @ct varchar(200), @membernumber varchar(40), 
	@memberID int, @rc int, @recordTypeID int, @RTID int
select @row = min(row) from datatransfer.dbo.tmp_NHFirms
while @row is not null BEGIN
	select	@memberID = null, @ct = null, @firstname = null, @company = null, @membernumber = null, 
			@RTID = null, @recordTypeID = null

	select @company = company, @ct = [Contact Type]
		from datatransfer.dbo.tmp_NHFirms
		where row = @row

	select @membernumber = 'VENDFIRM' + RIGHT('00000' + cast(@row as varchar(4)),5)
	select @recordTypeID = @companyRTID
	select @RTID = @FirmMemberRTRTID

	EXEC @rc = dbo.ams_createMember @orgID=@orgID, @memberTypeID=2, @prefix='', @firstname='Vendor', @middlename='', @lastname='Account', @suffix='', @professionalsuffix='', @company=@company, @memberNumber=@membernumber, @status='A', @memberID=@memberID OUTPUT
		IF @@ERROR <> 0 or @rc <> 0 or @memberID = 0 goto on_error

	UPDATE ams_members
	SET recordTypeID = @recordTypeID
	where memberID = @memberID
		IF @@ERROR <> 0 goto on_error

	UPDATE datatransfer.dbo.tmp_NHFirms
	set memberID = @memberID
	where row = @row
		IF @@ERROR <> 0 goto on_error

	INSERT INTO dbo.ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive)
	select @RTID, @memberID, m.memberid, 1
	from dbo.ams_members as m
	inner join dbo.vw_memberData_NH as vw2 on vw2.memberid = m.memberid
	where m.orgID = @orgID
	and m.company = @company
	and (left(m.membernumber,4) = 'EXHB' OR vw2.[Contact type] like '%Vendor%')
	and m.memberID <> @memberID
		IF @@ERROR <> 0 goto on_error

	select @row = min(row) from datatransfer.dbo.tmp_NHFirms where row > @row
END

declare @addressTypeID int
select @addressTypeID = addressTypeID from dbo.ams_memberAddressTypes where orgID = @orgID and addressTypeOrder = 1

insert into dbo.ams_memberAddresses (memberID, addressTypeID, address1, address2, address3, city, stateID, postalCode, countryID)
select memberID, @addressTypeID, isnull([Address_address1],''), isnull([Address_address2],''), '', 
	isnull([Address_city],''), stateID, isnull([Address_postalCode],''), 1
from datatransfer.dbo.tmp_NHFirms
	IF @@ERROR <> 0 goto on_error

COMMIT TRAN
goto on_success

on_error:
	ROLLBACK TRAN
	goto on_done

on_success:
	-- queue member groups (@runSchedule=2 indicates delayed processing) 
	declare @itemGroupUID uniqueidentifier, @memberIDList varchar(max)
	EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList='', @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

on_done:
	DROP TABLE datatransfer.dbo.tmp_NHFirms

GO



-- FIRMS
declare @orgID int
select @orgID = orgID from sites where sitecode = 'NH'

select tmp.company, vw.[Address_address1], vw.[Address_address2], vw.[Address_city], s.stateID, vw.[Address_postalCode], 
	vw.[Address_country], vw.[Contact Type], ROW_NUMBER() OVER (ORDER BY tmp.company) as row
into datatransfer.dbo.tmp_NHFirms
from (
	select m.company, min(m.memberID) as memberID
	from dbo.ams_members as m
	inner join dbo.vw_memberData_NH as vw2 on vw2.memberid = m.memberid
	where len(m.company) > 0
	and m.company <> 'Attorney at Law'
	and (
		vw2.[Contact type] like '%Attorney%'
		or vw2.[Contact type] like '%Judge%'
		or vw2.[Contact type] like '%Paralegal%'
	)
	group by m.company
	having count(*) > 1
) as tmp
inner join dbo.vw_memberData_NH as vw on vw.memberid = tmp.memberid
left outer join dbo.ams_states as s on s.code = vw.[Address_stateprov] and s.countryID = 1
GO

ALTER TABLE datatransfer.dbo.tmp_NHFirms ADD memberid int NULL
GO

declare @companyRTID int, @IndivRTID int, @FirmMemberRTRTID int, @FirmMemberRTRTID2 int, @FirmRelTID int, @FirmRelTID2 int, @orgID int
select @orgID = orgID from sites where sitecode = 'NH'
select @companyRTID = recordTypeID from dbo.ams_recordTypes where orgID = @orgID and recordTypeCode = 'Firm'
select @IndivRTID = recordTypeID from dbo.ams_recordTypes where orgID = @orgID and recordTypeCode = 'Individual'
select @FirmRelTID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgID and relationshipTypeCode = 'Attorney'
select @FirmRelTID2 = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgID and relationshipTypeCode = 'Paralegal'
select @FirmMemberRTRTID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes where masterRecordTypeID = @companyRTID and childRecordTypeID = @IndivRTID and relationshipTypeID = @FirmRelTID
select @FirmMemberRTRTID2 = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes where masterRecordTypeID = @companyRTID and childRecordTypeID = @IndivRTID and relationshipTypeID = @FirmRelTID2

BEGIN TRAN

declare @row int, @firstname varchar(75), @company varchar(300), @ct varchar(200), @membernumber varchar(40), 
	@memberID int, @rc int, @recordTypeID int, @RTID int
select @row = min(row) from datatransfer.dbo.tmp_NHFirms
while @row is not null BEGIN
	select	@memberID = null, @ct = null, @firstname = null, @company = null, @membernumber = null, 
			@RTID = null, @recordTypeID = null

	select @company = company, @ct = [Contact Type]
		from datatransfer.dbo.tmp_NHFirms
		where row = @row

	select @membernumber = 'FIRM' + RIGHT('00000' + cast(@row as varchar(4)),5)
	select @recordTypeID = @companyRTID

	EXEC @rc = dbo.ams_createMember @orgID=@orgID, @memberTypeID=2, @prefix='', @firstname='Firm', @middlename='', @lastname='Account', @suffix='', @professionalsuffix='', @company=@company, @memberNumber=@membernumber, @status='A', @memberID=@memberID OUTPUT
		IF @@ERROR <> 0 or @rc <> 0 or @memberID = 0 goto on_error

	UPDATE ams_members
	SET recordTypeID = @recordTypeID
	where memberID = @memberID
		IF @@ERROR <> 0 goto on_error

	UPDATE datatransfer.dbo.tmp_NHFirms
	set memberID = @memberID
	where row = @row
		IF @@ERROR <> 0 goto on_error

	INSERT INTO dbo.ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive)
	select @FirmMemberRTRTID, @memberID, m.memberid, 1
	from dbo.ams_members as m
	inner join dbo.vw_memberData_NH as vw2 on vw2.memberid = m.memberid
	where m.company = @company
	and (vw2.[Contact type] like '%Attorney%' or vw2.[Contact type] like '%Judge%')
	and m.memberID <> @memberID
		IF @@ERROR <> 0 goto on_error

	INSERT INTO dbo.ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive)
	select @FirmMemberRTRTID2, @memberID, m.memberid, 1
	from dbo.ams_members as m
	inner join dbo.vw_memberData_NH as vw2 on vw2.memberid = m.memberid
	where m.company = @company
	and vw2.[Contact type] like '%Paralegal%'
	and m.memberID <> @memberID
		IF @@ERROR <> 0 goto on_error

	select @row = min(row) from datatransfer.dbo.tmp_NHFirms where row > @row
END

declare @addressTypeID int
select @addressTypeID = addressTypeID from dbo.ams_memberAddressTypes where orgID = @orgID and addressTypeOrder = 1

insert into dbo.ams_memberAddresses (memberID, addressTypeID, address1, address2, address3, city, stateID, postalCode, countryID)
select memberID, @addressTypeID, isnull([Address_address1],''), isnull([Address_address2],''), '', 
	isnull([Address_city],''), stateID, isnull([Address_postalCode],''), 1
from datatransfer.dbo.tmp_NHFirms
	IF @@ERROR <> 0 goto on_error

COMMIT TRAN
goto on_success

on_error:
	ROLLBACK TRAN
	goto on_done

on_success:
	-- queue member groups (@runSchedule=2 indicates delayed processing) 
	declare @itemGroupUID uniqueidentifier, @memberIDList varchar(max)
	EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList='', @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

on_done:
	DROP TABLE datatransfer.dbo.tmp_NHFirms

GO
