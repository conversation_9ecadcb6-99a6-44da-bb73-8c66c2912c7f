-- OLD PLATFORM

declare @bucketTypeID int, @bucketGroupID int, @siteID int, @maxOrder int

/*  CHANGE SiteID on PROD if  needed!!!! */ 
select @siteid = 179
select @bucketTypeID = bucketTypeID from search.dbo.tblSearchBucketTypes  where bucketType = 'SeminarWeb'
select @bucketGroupID = 1

select 
	@maxOrder = isnull(max(bucketOrder) + 1,1)
from 
	search.dbo.tblSearchBuckets
where 
	siteid = @siteid
	and isActive = 1

print @siteid
print @bucketTypeID
print @bucketGroupID
print @maxOrder

	
insert into search.dbo.tblSearchBuckets (
	orgcode, 
	siteid, 
	bucketName, 
	bucketGroupID, 
	bucketTypeID, 
	bucketOrder, 
	bucketSettings, 
	hideUntilSearched, 
	isActive, 
	restrictToGroupID, 
	hideIfRestricted
)
values (
	NULL, 
	@siteid, 
	'SeminarWeb', 
	@bucketGroupID, 
	@bucketTypeID, 
	@maxOrder, 
	null, 
	0, 
	1, 
	null, 
	0
)

GO