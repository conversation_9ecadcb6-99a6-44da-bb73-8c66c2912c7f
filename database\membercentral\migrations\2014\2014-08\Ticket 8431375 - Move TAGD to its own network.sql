-- rename existing network
UPDATE networks
SET networkName = 'Academy of General Dentistry'
where networkName = 'Texas Academy of General Dentistry'
GO

-- create new network
declare @networkID int
EXEC dbo.createNetwork 'Texas Academy of General Dentistry', null, null, 'MemberCentral', '800-701-4728', '<EMAIL>', 0, @networkID OUTPUT
GO

-- remove TAGD from old network
declare @networkID int, @siteID int
select @networkID = networkID from networks where networkName = 'Academy of General Dentistry'
select @siteID = siteID from sites where sitecode = 'TAGD'
DELETE from networkSites where siteID = @siteID and networkID = @networkID
GO

-- add TAGD site to the new TAGD network
declare @networkID int, @siteID int
select @networkID = networkID from networks where networkName = 'Texas Academy of General Dentistry'
select @siteID = siteID from sites where sitecode = 'TAGD'
EXEC dbo.createNetworkSite @networkID, @siteID, 1, 1
GO

-- need to move network profiles
declare @oldnetworkID int, @newnetworkID int, @siteID int
select @oldnetworkID = networkID from networks where networkName = 'Academy of General Dentistry'
select @newnetworkID = networkID from networks where networkName = 'Texas Academy of General Dentistry'
select @siteID = siteID from sites where sitecode = 'TAGD'

select np.profileID, mnp.mnpID, mnp.memberID, mnp.siteID, 0 as mnpCount
into #tmpMNP
from ams_networkProfiles as np
inner join ams_memberNetworkProfiles as mnp on mnp.profileID = np.profileID
where networkID = @oldnetworkID
and np.status <> 'D'
and mnp.status <> 'D'
order by np.profileID

update tmp
set tmp.mnpCount = (select count(*) from #tmpMNP where profileID = tmp.profileID)
from #tmpMNP as tmp

update ams_networkProfiles
set networkID = @newnetworkID
where profileID in (select profileID from #tmpMNP where mnpCount = 1 and siteID = @siteID)

delete from #tmpMNP
where mnpCount = 1 

-- these need to handled manually
select * from #tmpMNP order by 1

	update ams_membernetworkProfiles set status = 'D' where mnpID = 196576

	update ams_networkProfiles
	set networkID = 55
	where profileID in (53283,53283,73532,73532,73556,73556,73715,73715,73717,73717,73867,73867,73971,73971,74064,74064,74183,74183,74279,74279,74323,74323,113960)

	delete from #tmpMNP where profileID in (53283,53283,73532,73532,73556,73556,73715,73715,73717,73717,73867,73867,73971,73971,74064,74064,74183,74183,74279,74279,74323,74323,113960)

DROP TABLE #tmpMNP

