use platformQueue;
GO

declare @queueTypeID int

insert into dbo.tblQueueTypes (queueType) values ('subsForceAdd')
SELECT @queueTypeID = SCOPE_IDENTITY()

insert into dbo.tblQueueStatuses (queueTypeID, queueStatus)
select @queueTypeID, qs.queueStatus
from dbo.tblQueueTypes qt
inner join dbo.tblQueueStatuses qs
	on qt.queueTypeID  = qs.queueTypeID
	and qt.queueType = 'ExpireSubs'
order by qs.queueStatusID

GO


declare @queueTypeID int, @trashID int
select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType='subsForceAdd'

exec dbo.queue_createQueueTypeDataColumn @queueTypeID=@queueTypeID, @columnName='subscriberID', @columnDesc='rootSubscriberID of existing subscriber', @dataTypeCode='INTEGER', @columnID=@trashID OUTPUT
exec dbo.queue_createQueueTypeDataColumn @queueTypeID=@queueTypeID, @columnName='subscriptionID', @columnDesc='subscriptionID to be added', @dataTypeCode='INTEGER', @columnID=@trashID OUTPUT
exec dbo.queue_createQueueTypeDataColumn @queueTypeID=@queueTypeID, @columnName='resultMessage', @columnDesc='subscriptionID to be added', @dataTypeCode='STRING', @columnID=@trashID OUTPUT
GO

CREATE PROC [dbo].[job_subsForceAdd_grabForProcessing]
@serverID int,
@batchSize int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int, @subscriberDataColumnID int, @subscriptionToAddDataColumnID int

	select @queueTypeID = queueTypeID
	from platformQueue.dbo.tblQueueTypes as qt
	where qt.queueType = 'subsForceAdd'

	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueTypeID = @queueTypeID
		and qs.queueStatus = 'readyToProcess'
	select @statusGrabbed = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueTypeID = @queueTypeID
		and qs.queueStatus = 'grabbedForProcessing'

	select @subscriberDataColumnID = columnID
		from dbo.tblQueueTypeDataColumns qdc
		where queueTypeID = @queueTypeID
		and columnName = 'subscriberID'

	select @subscriptionToAddDataColumnID = columnID
		from dbo.tblQueueTypeDataColumns qdc
		where queueTypeID = @queueTypeID
		and columnName = 'subscriptionID'


	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL 
		DROP TABLE #tmpSubscribers
	CREATE TABLE #tmpSubscribers (itemUID uniqueidentifier, jobUID uniqueidentifier, subscriberID int, subscriptionIDToAdd int, recordedByMemberID int)

	declare @jobUID uniqueidentifier
	set @jobUID = NEWID()

	-- dequeue in order of dateAdded. get @batchsize subscribers
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.queueStatusID = @statusGrabbed,
		qi.dateUpdated = getdate(),
		qi.jobUID = @jobUID,
		qi.jobDateStarted = getdate(),
		qi.jobServerID = @serverID
		OUTPUT inserted.itemUID, inserted.jobUID, qid.columnValueInteger as subscriberID, qid2.columnValueInteger as subscriptionIDToAdd, qid.recordedByMemberID
		INTO #tmpSubscribers
	FROM platformQueue.dbo.tblQueueItems as qi
	INNER JOIN (
		SELECT top(@BatchSize) qi2.itemUID 
		from platformQueue.dbo.tblQueueItems as qi2
		WHERE qi2.queueStatusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemUID
	) as batch on batch.itemUID = qi.itemUID
	inner join platformQueue.dbo.tblQueueItemData qid
		on qi.itemUID = qid.itemUID
		and qid.columnID = @subscriberDataColumnID
	inner join platformQueue.dbo.tblQueueItemData qid2
		on qi.itemUID = qid2.itemUID
		and qid2.columnID = @subscriptionToAddDataColumnID
	WHERE qi.queueStatusID = @statusReady

	-- return subscriber information
	select qid.itemUID, s.siteID, s.orgID, qid.jobUID, qid.recordedByMemberID,
		qid.subscriberID, subs.subscriptionID as rootSubscriptionID, subsToAdd.subscriptionID, subsToAdd.subscriptionName, subsToAdd.allowRateGLAccountOverride,subsToAdd.GLAccountID, tToAdd.typeID, tToAdd.typeName,
		ss.memberID, mActive.memberID as activeMemberID, r.rateID, r.isRenewalRate, rf.frequencyID,
		RTRIM(mActive.lastname + ' ' + isnull(mActive.suffix, '')) + ', ' + mActive.firstname + isnull(' (' + mActive.membernumber + ')','') AS memberName
	from #tmpSubscribers as qid
	inner join membercentral.dbo.sub_subscribers as ss 
		on ss.subscriberID = qid.subscriberID
	inner join membercentral.dbo.sub_rateFrequencies rf 
		on rf.rfid = ss.rfid
	inner join membercentral.dbo.sub_rates r
		on r.rateID = rf.rateID
	inner join membercentral.dbo.ams_members m
		on m.memberID = ss.memberID
	inner join membercentral.dbo.ams_members mActive
		on mActive.memberID = m.activeMemberID
	inner join membercentral.dbo.sub_subscriptions as subs 
		on subs.subscriptionID = ss.subscriptionID
	inner join membercentral.dbo.sub_types as t 
		on t.typeID = subs.typeID
	inner join membercentral.dbo.sites as s 
		on s.siteID = t.siteID
	inner join membercentral.dbo.sub_subscriptions subsToAdd
		on subsToAdd.subscriptionID = qid.subscriptionIDToAdd
	inner join membercentral.dbo.sub_types tToAdd
		on subsToAdd.typeID = tToAdd.typeID
	order by qid.itemUID

	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL 
		DROP TABLE #tmpSubscribers

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH

GO

use membercentral;
GO
CREATE PROC [dbo].[sub_queueForceAddSubscribers]
@recordedByMemberID int,
@subscriptionIDToAdd int,
@subscriberIDList varchar(max)

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL 
		DROP TABLE #tmpSubscribers
	CREATE TABLE #tmpSubscribers (subscriberID int, itemUID uniqueidentifier DEFAULT NEWID())

	declare @statusInserting int, @statusReady int, @queueTypeID int, @subscriberDataColumnID int, @subscriptionToAddDataColumnID int, @siteID int, @resultMessageDataColumnID int

	select @queueTypeID = queueTypeID
	from platformQueue.dbo.tblQueueTypes as qt
	where qt.queueType = 'subsForceAdd'

	select @statusInserting = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
	where qs.queueTypeID = @queueTypeID
		and qs.queueStatus = 'insertingItems'
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
	where qs.queueTypeID = @queueTypeID
		and qs.queueStatus = 'readyToProcess'

	select @subscriberDataColumnID = columnID
	from platformQueue.dbo.tblQueueTypeDataColumns qdc
	where queueTypeID = @queueTypeID
	and columnName = 'subscriberID'

	select @subscriptionToAddDataColumnID = columnID
	from platformQueue.dbo.tblQueueTypeDataColumns qdc
	where queueTypeID = @queueTypeID
	and columnName = 'subscriptionID'

	select @resultMessageDataColumnID = columnID
	from platformQueue.dbo.tblQueueTypeDataColumns qdc
	where queueTypeID = @queueTypeID
	and columnName = 'resultMessage'



	select @siteID = t.siteID
	from sub_subscriptions subs
	inner join sub_types t
		on t.typeID = subs.typeID
		and subs.subscriptionID = @subscriptionIDToAdd

	-- there should only be one itemGroupUID for this request
	declare @itemGroupUID uniqueidentifier
	set @itemGroupUID = NEWID()

	-- get subscribers to modify
	insert into #tmpSubscribers (subscriberID)
	select s.subscriberID
	from dbo.fn_intListToTable(@subscriberIDList,',') as tmp
	inner join dbo.sub_subscribers as s on s.subscriberID = tmp.listitem
		except
	select qid.columnValueInteger as subscriberID
	from platformQueue.dbo.tblQueueItems as qi
	inner join platformQueue.dbo.tblQueueItemData qid
		on qi.itemUID = qid.itemUID
		and qid.columnID = @subscriberDataColumnID
	inner join platformQueue.dbo.tblQueueItemData qid2
		on qi.itemUID = qid2.itemUID
		and qid2.columnID = @subscriptionToAddDataColumnID
		and qid2.columnValueInteger = @subscriptionIDToAdd
	inner join platformQueue.dbo.tblQueueStatuses as qs 
		on qs.queueStatusID = qi.queueStatusID
		and qs.queueTypeID = @queueTypeID
		and qs.queueStatus not in ('readyToNotify','grabbedForNotifying','done')

	-- queue items
	insert into platformQueue.dbo.tblQueueItemData (itemGroupUID,itemUID, recordedByMemberID, siteID, columnID, columnValueInteger)
		OUTPUT inserted.itemUID, getdate(), @statusInserting 
		INTO platformQueue.dbo.tblQueueItems(itemUID, dateAdded, queueStatusID)
	select @itemGroupUID, itemUID, @recordedByMemberID, @siteID , @subscriberDataColumnID, subscriberID
	from #tmpSubscribers

	insert into platformQueue.dbo.tblQueueItemData (itemGroupUID,itemUID, recordedByMemberID,siteID, columnID, columnValueInteger)
	select @itemGroupUID, itemUID, @recordedByMemberID, @siteID , @subscriptionToAddDataColumnID, @subscriptionIDToAdd
	from #tmpSubscribers

	insert into platformQueue.dbo.tblQueueItemData (itemGroupUID,itemUID, recordedByMemberID,siteID, columnID, columnValueString)
	select @itemGroupUID, itemUID, @recordedByMemberID, @siteID , @resultMessageDataColumnID, ''
	from #tmpSubscribers

	-- update queue item groups to show ready to process
	update qi WITH (UPDLOCK, HOLDLOCK)
	set qi.queueStatusID = @statusReady,
		qi.dateUpdated = getdate()
	from platformQueue.dbo.tblQueueItems as qi
	inner join #tmpSubscribers as s on s.itemUID = qi.itemUID

	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL 
		DROP TABLE #tmpSubscribers

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER FUNCTION [dbo].[fn_sub_getSubscriptionTreeOrder]
(
 @subscriptionID int
)
RETURNS TABLE
AS
RETURN
(



 WITH suborderCTE as (
	select
		sub.subscriptionID,
		sub.uid,
		sub.subscriptionName,
		sub.paymentOrder,
		cast('0001' as varchar(max)) as subscriptionPath,
		null as parentSubscriptionID,
		null as addOnID
	from sub_subscriptions sub
	where sub.subscriptionID = @subscriptionID

	union all

	select
		sub.subscriptionID,
		sub.uid,
		sub.subscriptionName,
		sub.paymentOrder,
		subscriptionPath = suborderCTE.subscriptionPath + '.' + CAST(RIGHT('100000'+(row_number() over ( ORDER BY ao.orderNum, ssets.orderNum)),4) as varchar(max)),
		suborderCTE.subscriptionID as parentSubscriptionID,
		ao.addOnID
	from suborderCTE
	inner join sub_addons ao
		on suborderCTE.subscriptionID = ao.subscriptionID
	inner join sub_sets as sets
		on sets.setID = ao.childSetID
	inner join dbo.sub_subscriptionSets ssets
		on ssets.setID = sets.setID
	inner join dbo.sub_subscriptions sub
		on sub.subscriptionID = ssets.subscriptionID
)
select 	
	subscriptionID,
	uid,
	subscriptionName,
	paymentOrder,
	subscriptionPath,
	parentSubscriptionID,
	addOnID
from suborderCTE		
)
GO
use platformQueue;
GO
CREATE PROC [dbo].[job_subsForceAdd_grabForNotification]
AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @statusReady int, @statusGrabbed int, @queueTypeID int, @subscriberDataColumnID int, @subscriptionToAddDataColumnID int, @resultMessageDataColumnID int



	select @queueTypeID = queueTypeID
	from platformQueue.dbo.tblQueueTypes as qt
	where qt.queueType = 'subsForceAdd'


	select @statusReady = qs.queueStatusID , @queueTypeID = qt.queueTypeID
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueTypeID = @queueTypeID
		and qs.queueStatus = 'readyToNotify'
	select @statusGrabbed = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueTypeID = @queueTypeID
		and qs.queueStatus = 'grabbedForNotifying'


	select @subscriberDataColumnID = columnID
		from dbo.tblQueueTypeDataColumns qdc
		where queueTypeID = @queueTypeID
		and columnName = 'subscriberID'

	select @subscriptionToAddDataColumnID = columnID
		from dbo.tblQueueTypeDataColumns qdc
		where queueTypeID = @queueTypeID
		and columnName = 'subscriptionID'

	select @resultMessageDataColumnID = columnID
		from dbo.tblQueueTypeDataColumns qdc
		where queueTypeID = @queueTypeID
		and columnName = 'resultMessage'




	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL 
		DROP TABLE #tmpNotify
	
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier)

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueStatuses qs
			on qs.queueStatusID = qi.queueStatusID
			and qs.queueTypeID = @queueTypeID
		inner join platformQueue.dbo.tblQueueItemData qid
			on qi.itemUID = qid.itemUID
			and qid.columnID = @subscriberDataColumnID
		where qi.queueStatusID = @statusReady
			except
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueStatuses qs
			on qs.queueStatusID = qi.queueStatusID
			and qs.queueTypeID = @queueTypeID
		inner join platformQueue.dbo.tblQueueItemData qid
			on qi.itemUID = qid.itemUID
			and qid.columnID = @subscriberDataColumnID
		where qi.queueStatusID <> @statusReady
	)
	UPDATE platformQueue.dbo.tblQueueItems WITH (UPDLOCK, READPAST)
	SET queueStatusID = @statusGrabbed,
		dateUpdated = getdate()
		OUTPUT qid.itemGroupUID
		INTO #tmpNotify
	FROM platformQueue.dbo.tblQueueItems as qi
	inner join platformQueue.dbo.tblQueueStatuses qs
		on qs.queueStatusID = qi.queueStatusID
		and qs.queueTypeID = @queueTypeID
	inner join platformQueue.dbo.tblQueueItemData qid
		on qi.itemUID = qid.itemUID
		and qid.columnID = @subscriberDataColumnID
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qid.itemGroupUID
	where qi.queueStatusID = @statusReady

	-- return report information
	select tmpN.itemGroupUID, qid.itemUID, sub.subscriptionName,
		subAdded.subscriptionID as newSubscriptionID, subAdded.subscriptionName as newSubscriptionName, tAdded.typeID as newTypeID, tAdded.typeName as newTypeName,
		m2.memberID, m2.lastname + ', ' + m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' (' + m2.membernumber + ')' as subscriberName,
		qid3.columnValueString as resultMessage, qid.columnValueInteger as rootSubscriberID,
		m4.memberID as reportMemberID, me.email as reportEmail, m4.orgID as reportOrgID,  m4.firstname as reportFirstName, m4.lastname as reportLastName, m4.memberNumber as reportMemberNumber, sites.siteName, sites.sitecode, sites.siteID, sites.orgID
	from (select distinct itemGroupUID from #tmpNotify) as tmpN
	inner join platformQueue.dbo.tblQueueItemData qid
		on tmpN.itemGroupUID = qid.itemGroupUID
		and qid.columnID = @subscriberDataColumnID
	INNER JOIN platformQueue.dbo.tblQueueItems as qi 
		ON qi.itemUID = qid.itemUID	
	inner join platformQueue.dbo.tblQueueItemData qid2
		on qi.itemUID = qid2.itemUID
		and qid2.columnID = @subscriptionToAddDataColumnID
	inner join platformQueue.dbo.tblQueueItemData qid3
		on qi.itemUID = qid3.itemUID
		and qid3.columnID = @resultMessageDataColumnID
	INNER JOIN membercentral.dbo.sub_subscribers as s on s.subscriberID = qid.columnValueInteger
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = s.memberID
	INNER JOIN membercentral.dbo.ams_members as m2 on m2.memberid = m.activeMemberID
	INNER JOIN membercentral.dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
	INNER JOIN membercentral.dbo.sub_types as st on st.typeID = sub.typeID
	INNER JOIN membercentral.dbo.sub_subscriptions as subAdded on subAdded.subscriptionID = qid2.columnValueInteger
	INNER JOIN membercentral.dbo.sub_types as tAdded on tAdded.typeID = subAdded.typeID
	INNER JOIN membercentral.dbo.sites on sites.siteID = st.siteID
	INNER JOIN membercentral.dbo.ams_members as m3 on m3.memberID = qid.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as m4 on m4.memberid = m3.activeMemberID
	LEFT OUTER JOIN membercentral.dbo.ams_memberEmails as me 
		INNER JOIN membercentral.dbo.ams_memberEmailTypes as met on met.emailTypeID = me.emailTypeID and met.emailTypeOrder = 1
		on me.memberID = m4.memberID
	order by tmpN.itemGroupUID, subscriberName, subscriptionName

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL 
		DROP TABLE #tmpNotify

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH
GO
CREATE PROC [dbo].[job_subsForceAdd_clearDone]
AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @statusDone int, @queueTypeID int, @subscriberDataColumnID int, @subscriptionToAddDataColumnID int, @resultMessageDataColumnID int

	select @statusDone = qs.queueStatusID, @queueTypeID = qt.queueTypeID
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'subsForceAdd'
		and qs.queueStatus = 'done'


	select @subscriberDataColumnID = columnID
		from dbo.tblQueueTypeDataColumns qdc
		where queueTypeID = @queueTypeID
		and columnName = 'subscriberID'

	select @subscriptionToAddDataColumnID = columnID
		from dbo.tblQueueTypeDataColumns qdc
		where queueTypeID = @queueTypeID
		and columnName = 'subscriptionID'

	select @resultMessageDataColumnID = columnID
		from dbo.tblQueueTypeDataColumns qdc
		where queueTypeID = @queueTypeID
		and columnName = 'resultMessage'



	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueStatuses qs
			on qs.queueStatusID = qi.queueStatusID
			and qs.queueTypeID = @queueTypeID
		inner join platformQueue.dbo.tblQueueItemData qid
			on qi.itemUID = qid.itemUID
			and qid.columnID = @subscriberDataColumnID
		where qi.queueStatusID = @statusDone
			except
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueStatuses qs
			on qs.queueStatusID = qi.queueStatusID
			and qs.queueTypeID = @queueTypeID
		inner join platformQueue.dbo.tblQueueItemData qid
			on qi.itemUID = qid.itemUID
			and qid.columnID = @subscriberDataColumnID
		where qi.queueStatusID <> @statusDone
	)
	DELETE from platformQueue.dbo.tblQueueItems
	where itemUID in (
		select qi.itemUID
		FROM platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueStatuses qs
			on qs.queueStatusID = qi.queueStatusID
			and qs.queueTypeID = @queueTypeID
		inner join platformQueue.dbo.tblQueueItemData qid
			on qi.itemUID = qid.itemUID
			and qid.columnID = @subscriberDataColumnID
		INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qid.itemGroupUID
		WHERE qi.queueStatusID = @statusDone
	)

	DELETE qid
	from platformQueue.dbo.tblQueueItemData qid
	inner join dbo.tblQueueTypeDataColumns qtdc
		on qtdc.columnID = qid.columnID
		and qtdc.queueTypeID = @queueTypeID
	where itemUID not in (select itemUID from platformQueue.dbo.tblQueueItems)

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH
GO
use membercentral;
GO
insert into membercentral.dbo.scheduledTasks (name, nextRunDate, interval, intervalTypeID, taskCFC, timeoutMinutes, disabled, siteid)
select  'Process Subscription ForceAdd Queue' as name, nextRunDate, interval, intervalTypeID, 'model.scheduledTasks.tasks.processSubForceAddQueue' as taskCFC, timeoutMinutes, disabled, siteid
from membercentral.dbo.scheduledTasks
where taskCFC = 'model.scheduledTasks.tasks.processSubExpireQueue'
GO