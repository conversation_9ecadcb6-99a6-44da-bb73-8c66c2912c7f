use customApps
GO

ALTER PROC [dbo].[abota_importMemberData]
@pathToImport varchar(200), 
@pathToExport varchar(200),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

DECLARE @orgID int
SELECT @orgID = membercentral.dbo.fn_getOrgIDFromOrgCode('ABOTA')

EXEC membercentral.dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Starting abota_importMemberData'

-- ensure files are present
IF membercentral.dbo.fn_FileExists(@pathToImport + 'demo.bcp') = 0
	OR membercentral.dbo.fn_FileExists(@pathToImport + 'addr.bcp') = 0
	OR membercentral.dbo.fn_FileExists(@pathToImport + 'phns.bcp') = 0
	OR membercentral.dbo.fn_FileExists(@pathToImport + 'grps.bcp') = 0
	OR membercentral.dbo.fn_FileExists(@pathToImport + 'attr.bcp') = 0 
BEGIN
	declare @tblErrors TABLE (rowid int IDENTITY(1,1), msg varchar(300), fatal bit)
	insert into @tblErrors (msg, fatal) VALUES ('One or more required files in the package is missing.',1)
	SELECT @importResult = (
		select getdate() as "@date", 'none' as "@flatfile",
			(select msg as "@msg", 'fatal' as "@severity"
			from @tblErrors
			FOR XML path('error'), root('errors'), type)
		for xml path('import'), TYPE)
	RETURN -1
END

-- create temp tables
IF OBJECT_ID('tempdb..##abota_demo') IS NOT NULL
	DROP TABLE ##abota_demo
CREATE TABLE ##abota_demo (
	[CONSTITUENT_ID] [varchar](20) NOT NULL PRIMARY KEY,
	[prefix] [varchar](100) NULL DEFAULT (''),
	[firstname] [varchar](50) NOT NULL DEFAULT (''),
	[middlename] [varchar](50) NOT NULL DEFAULT (''),
	[lastname] [varchar](100) NOT NULL DEFAULT (''),
	[suffix] [varchar](100) NULL DEFAULT (''),
	[nickname] [varchar](50) NULL DEFAULT (''),
	[maiden_name] [varchar](100) NULL DEFAULT (''),
	[Company] [varchar](60) NULL DEFAULT (''),
	[sex] [varchar](7) NOT NULL DEFAULT (''),
	[Birth_Date] [datetime] NULL,
	[marital_status] [varchar](100) NULL DEFAULT (''),
	[no_email] [bit] NULL DEFAULT (0),
	[Spouse] [varchar](110) NULL DEFAULT (''),
	[staff] [bit] NULL DEFAULT (0)
)

IF OBJECT_ID('tempdb..##abota_addr') IS NOT NULL
	DROP TABLE ##abota_addr
CREATE TABLE ##abota_addr (
	[CONSTITUENT_ID] [varchar](20) NOT NULL DEFAULT (''),
	[LONGDESCRIPTION] [varchar](100) NOT NULL DEFAULT (''),
	[CONSTITUENTADDRESSID] [int] NULL,
	[ADDRESS1] [varchar](150) NOT NULL DEFAULT (''),
	[ADDRESS2] [varchar](150) NOT NULL DEFAULT (''),
	[ADDRESS3] [varchar](150) NOT NULL DEFAULT (''),
	[CITY] [varchar](50) NOT NULL DEFAULT (''),
	[STATE] [varchar](3) NOT NULL DEFAULT (''),
	[POST_CODE] [varchar](12) NOT NULL DEFAULT (''),
	[Country] [varchar](100) NOT NULL DEFAULT ('')
)

IF OBJECT_ID('tempdb..##abota_phns') IS NOT NULL
	DROP TABLE ##abota_phns
CREATE TABLE ##abota_phns (
	[CONSTITUENT_ID] [varchar](20) NOT NULL DEFAULT (''),
	[CONSTITADDRESSID] [int] NOT NULL,
	[LONGDESCRIPTION] [varchar](100) NOT NULL DEFAULT (''),
	[NUM] [varchar](2047) NOT NULL DEFAULT ('')
)

IF OBJECT_ID('tempdb..##abota_grps') IS NOT NULL
	DROP TABLE ##abota_grps
CREATE TABLE ##abota_grps (
	[CONSTITUENT_ID] [varchar](20) NOT NULL DEFAULT (''),
	[GRPCODE] [varchar](30) NOT NULL DEFAULT (''),
	[VALUE] [varchar](100) NOT NULL DEFAULT ('')
)

IF OBJECT_ID('tempdb..##abota_attr') IS NOT NULL
	DROP TABLE ##abota_attr
CREATE TABLE ##abota_attr (
	[CONSTITUENT_ID] [varchar](20) NOT NULL DEFAULT (''),
	[DESCRIPTION] [varchar](50) NOT NULL DEFAULT (''),
	[LONGDESCRIPTION] [varchar](100) NOT NULL DEFAULT ('')
)

-- import bcp files into temp tables. Use @tmpBCP to suppress cmdshell output from calling code
declare @cmd varchar(400)

EXEC membercentral.dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Importing Demographic Data'
select @cmd = 'bcp ##abota_demo in ' + @pathToImport + 'demo.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
exec master..xp_cmdshell @cmd, NO_OUTPUT

EXEC membercentral.dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Importing Address Data'
select @cmd = 'bcp ##abota_addr in ' + @pathToImport + 'addr.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
exec master..xp_cmdshell @cmd, NO_OUTPUT

EXEC membercentral.dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Importing Phone Data'
select @cmd = 'bcp ##abota_phns in ' + @pathToImport + 'phns.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
exec master..xp_cmdshell @cmd, NO_OUTPUT

EXEC membercentral.dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Importing Group Data'
select @cmd = 'bcp ##abota_grps in ' + @pathToImport + 'grps.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
exec master..xp_cmdshell @cmd, NO_OUTPUT

EXEC membercentral.dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Importing Attribute Data'
select @cmd = 'bcp ##abota_attr in ' + @pathToImport + 'attr.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
exec master..xp_cmdshell @cmd, NO_OUTPUT

EXEC membercentral.dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Preparing to Flatten Data'

-- filter 
delete from ##abota_demo
where firstname = '' OR lastname = ''

-- marital status should be null, not empty string
update ##abota_demo
set [marital_status] = null
where [marital_status] = ''

-- remove phone entries tied to addresses other than what is it the addr tables
delete from ##abota_phns
where CONSTITADDRESSID not in (select CONSTITUENTADDRESSID from ##abota_addr)


-- Update the chapters for multiselect support.
DECLARE @tmpGroups TABLE (CONSTITUENT_ID varchar(20), GRPCODE varchar(30), [VALUE] varchar(100))

insert into @tmpGroups (CONSTITUENT_ID, GRPCODE, [VALUE])
select CONSTITUENT_ID, GRPCODE, [VALUE]
from ##abota_grps grp
where left(grpcode,3) = 'ch_' 
or left(grpcode,4) = 'sro_'

-- Remove the chapter groups will be reinserted
delete
from ##abota_grps 
where left(grpcode,3) = 'ch_' 
or left(grpcode,4) = 'sro_'

-- Insert in the correct format for multiselect
insert into ##abota_grps (CONSTITUENT_ID, GRPCODE, [VALUE])
select CONSTITUENT_ID, GRPCODE, membercentral.dbo.pipelist(grp.[VALUE]) as xx
from @tmpGroups grp
where left(grpcode,3) = 'ch_' 
or left(grpcode,4) = 'sro_'
group by CONSTITUENT_ID, GRPCODE

-- pivot attributes into temp table to use in join below
IF OBJECT_ID('tempdb..##tmpABOTA_attr') IS NOT NULL
	DROP TABLE ##tmpABOTA_attr
select CONSTITUENT_ID as CONSTITUENT_IDAttr, [Chapter], [Fellows Level], [Rank], [St/Reg Organization] as [State-Regional Organization], [Type of Law],[Current Program],[Constituency Code]
into ##tmpABOTA_attr
from (
	select CONSTITUENT_ID, [DESCRIPTION], LONGDESCRIPTION
	from ##abota_attr
) tmp
PIVOT (min(LONGDESCRIPTION) for DESCRIPTION in ([Chapter],[Fellows Level],[Rank],[St/Reg Organization],[Type of Law],[Current Program],[Constituency Code])) as p

-- pivot phones into temp table to use in join below
IF OBJECT_ID('tempdb..##tmpABOTA_phns') IS NOT NULL
	DROP TABLE ##tmpABOTA_phns
select CONSTITUENT_ID, CONSTITADDRESSID, [Business Phone],[Business Fax],[Home Phone],[Cell Phone],[Direct Line]
into ##tmpABOTA_phns
from (
	select CONSTITUENT_ID, CONSTITADDRESSID, LONGDESCRIPTION, NUM
	from ##abota_phns
) tmp
PIVOT (min(NUM) for LONGDESCRIPTION in ([Business Phone],[Business Fax],[Home Phone],[Cell Phone],[Direct Line])) as p

-- pivot emails/website into temp table to use in join below
IF OBJECT_ID('tempdb..##tmpABOTA_emails') IS NOT NULL
	DROP TABLE ##tmpABOTA_emails
select CONSTITUENT_ID as CONSTITUENT_IDEmls, 
	case when len([Website]) > 0 and left([website],4) <> 'http' then 'http://' + [website] else [website] end as [Website], 
	case when len([Facebook]) > 0 and left([Facebook],4) <> 'http' then 'http://' + [facebook] else [facebook] end as [Facebook], 
	case when len([LinkedIn]) > 0 and left([LinkedIn],4) <> 'http' then 'http://' + [LinkedIn] else [LinkedIn] end as [LinkedIn], 
	case when len([Twitter]) > 0 and left([Twitter],4) <> 'http' then 'http://' + [Twitter] else [Twitter] end as [Twitter], 
	[Asst Email] as [Assistant Email], 
	[E-Mail] as [Email]
into ##tmpABOTA_emails
from (
	select CONSTITUENT_ID, LONGDESCRIPTION, NUM
	from ##abota_phns
	where num <> ''
) tmp
PIVOT (min(NUM) for LONGDESCRIPTION in ([Website],[Facebook],[LinkedIn],[Twitter],[Asst Email],[E-Mail])) as p

-- get the group list for PIVOT
declare @grpList varchar(max)
select @grpList = COALESCE(@grpList + ',', '') + quotename(columnName)
	from membercentral.dbo.ams_memberdataColumns
	where orgID = @orgID
	and columnName like 'SRO\_%' escape ('\')
	order by columnName
select @grpList = COALESCE(@grpList + ',', '') + quotename(columnName)
	from membercentral.dbo.ams_memberdataColumns
	where orgID = @orgID
	and columnName like 'C\_%' escape ('\')
	order by columnName
select @grpList = COALESCE(@grpList + ',', '') + quotename(columnName)
	from membercentral.dbo.ams_memberdataColumns
	where orgID = @orgID
	and columnName like 'CH\_%' escape ('\')
	order by columnName

-- put group assignments into temp table to use in join below
IF OBJECT_ID('tempdb..##tmpABOTA_grps') IS NOT NULL
	DROP TABLE ##tmpABOTA_grps
DECLARE @grpsql varchar(max)
select @grpsql = ''
select @grpsql = @grpsql + 'select constituent_id, ' + @grpList + ' '
select @grpsql = @grpsql + 'into ##tmpABOTA_grps '
select @grpsql = @grpsql + 'from ( '
select @grpsql = @grpsql + '	select CONSTITUENT_ID, grpCode, value '
select @grpsql = @grpsql + '	from ##abota_grps '
select @grpsql = @grpsql + ') grps '
select @grpsql = @grpsql + 'PIVOT (min(value) for grpCode in (' + @grpList + ')) as p '
EXEC(@grpsql)

EXEC membercentral.dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Flattening Data'


-- put data into temp flat table
IF OBJECT_ID('tempdb..##tmpABOTA') IS NOT NULL
	DROP TABLE ##tmpABOTA
SELECT ROW_NUMBER() OVER (ORDER BY demo.CONSTITUENT_ID ASC) as rowID, 
	demo.CONSTITUENT_ID as membernumber,
	demo.prefix, demo.firstname, demo.middlename, demo.lastname, demo.suffix,
	demo.company, demo.nickname, demo.maiden_name as [Maiden Name], demo.sex, 
	convert(varchar(10),demo.birth_date,101) as [Birth Date], 
	demo.marital_status as [Marital Status], demo.no_email as [Email Opt Out], 
	demo.Spouse, demo.staff, '' as [BillingAddressType],

	addr1.address1 as [Business_address1], addr1.address2 as [Business_address2], 
	addr1.address3 as [Business_address3], addr1.city as [Business_city], addr1.state as [Business_stateprov], 
	addr1.post_Code as [Business_postalcode], addr1.country as [Business_country], 
	phone1.[Business Phone] as [Business_Business], phone1.[Business Fax] as [Business_Fax],
	phone1.[Home Phone] as [Business_Home], phone1.[Cell Phone] as [Business_Cell],
	phone1.[Direct Line] as [Business_Direct],

	addr2.address1 as [Home_address1], addr2.address2 as [Home_address2], 
	addr2.address3 as [Home_address3], addr2.city as [Home_city], addr2.state as [Home_stateprov], 
	addr2.post_Code as [Home_postalcode], addr2.country as [Home_country], 
	phone2.[Business Phone] as [Home_Business], phone2.[Business Fax] as [Home_Fax],
	phone2.[Home Phone] as [Home_Home], phone2.[Cell Phone] as [Home_Cell],
	phone2.[Direct Line] as [Home_Direct],

	grps.*, attr.*, emls.*
INTO ##tmpABOTA
FROM ##abota_demo as demo
LEFT OUTER JOIN ##abota_addr as addr1 on addr1.CONSTITUENT_ID = demo.CONSTITUENT_ID and addr1.LONGDESCRIPTION = 'Business'
LEFT OUTER JOIN ##tmpABOTA_phns as phone1 on phone1.CONSTITADDRESSID = addr1.CONSTITUENTADDRESSID
LEFT OUTER JOIN ##abota_addr as addr2 on addr2.CONSTITUENT_ID = demo.CONSTITUENT_ID and addr2.LONGDESCRIPTION = 'Home'
LEFT OUTER JOIN ##tmpABOTA_phns as phone2 on phone2.CONSTITADDRESSID = addr2.CONSTITUENTADDRESSID
LEFT OUTER JOIN ##tmpABOTA_attr as attr on attr.CONSTITUENT_IDAttr = demo.CONSTITUENT_ID
LEFT OUTER JOIN ##tmpABOTA_emails as emls on emls.CONSTITUENT_IDEmls = demo.CONSTITUENT_ID
LEFT OUTER JOIN ##tmpABOTA_grps as grps on grps.CONSTITUENT_ID = demo.CONSTITUENT_ID

-- drop extra column picked up in the joins
ALTER TABLE ##tmpABOTA DROP COLUMN CONSTITUENT_ID, CONSTITUENT_IDAttr, CONSTITUENT_IDEmls;


-- cleanup
IF OBJECT_ID('tempdb..##abota_demo') IS NOT NULL
	DROP TABLE ##abota_demo
IF OBJECT_ID('tempdb..##abota_addr') IS NOT NULL
	DROP TABLE ##abota_addr
IF OBJECT_ID('tempdb..##abota_phns') IS NOT NULL
	DROP TABLE ##abota_phns
IF OBJECT_ID('tempdb..##abota_attr') IS NOT NULL
	DROP TABLE ##abota_attr
IF OBJECT_ID('tempdb..##abota_grps') IS NOT NULL
	DROP TABLE ##abota_grps
IF OBJECT_ID('tempdb..##tmpABOTA_attr') IS NOT NULL
	DROP TABLE ##tmpABOTA_attr
IF OBJECT_ID('tempdb..##tmpABOTA_phns') IS NOT NULL
	DROP TABLE ##tmpABOTA_phns
IF OBJECT_ID('tempdb..##tmpABOTA_emails') IS NOT NULL
	DROP TABLE ##tmpABOTA_emails
IF OBJECT_ID('tempdb..##tmpABOTA_grps') IS NOT NULL
	DROP TABLE ##tmpABOTA_grps

-- move data to holding tables
EXEC memberCentral.dbo.ams_importMemberData_tempToHolding @orgid=@orgID, @tmptbl='##tmpABOTA', @pathToExport=@pathToExport, @importResult=@importResult OUTPUT

RETURN 0
GO

ALTER PROC [dbo].[fl_importMemberData]
@pathToImport varchar(200), 
@pathToExport varchar(200),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

DECLARE @orgID int
SELECT @orgID = membercentral.dbo.fn_getOrgIDFromOrgCode('FL')

EXEC membercentral.dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Starting fl_importMemberData'

-- create temp tables
IF OBJECT_ID('tempdb..##fl_MbrDemographics') IS NOT NULL
	DROP TABLE ##fl_MbrDemographics
CREATE TABLE [##fl_MbrDemographics] (
	[ID] [varchar] (10) NOT NULL DEFAULT (''),
	[MEMBER_TYPE] [varchar] (5) NOT NULL DEFAULT (''),
	[MBRTYPE_DESCRIPTION] [varchar] (100) NOT NULL DEFAULT (''),
	[CATEGORY] [varchar] (5) NOT NULL DEFAULT (''),
	[STATUS] [varchar] (5) NOT NULL DEFAULT (''),
	[CO_ID] [varchar] (10) NOT NULL DEFAULT (''),
	[COMPANY] [varchar] (80) NOT NULL DEFAULT (''),
	[FULL_NAME] [varchar] (60) NOT NULL DEFAULT (''),
	[INFORMAL] [varchar] (20) NOT NULL DEFAULT (''),
	[LAST_FIRST] [varchar] (30) NOT NULL DEFAULT (''),
	[PREFIX] [varchar] (10) NOT NULL DEFAULT (''),
	[FIRST_NAME] [varchar] (20) NOT NULL DEFAULT (''),
	[MIDDLE_NAME] [varchar] (20) NOT NULL DEFAULT (''),
	[LAST_NAME] [varchar] (30) NOT NULL DEFAULT (''),
	[SUFFIX] [varchar] (10) NOT NULL DEFAULT (''),
	[TITLE] [varchar] (80) NOT NULL DEFAULT (''),
	[DESIGNATION] [varchar] (20) NOT NULL DEFAULT (''),
	[ADDRESS_1] [char] (100) NOT NULL DEFAULT (''),
	[ADDRESS_2] [char] (100) NOT NULL DEFAULT (''),
	[CITY] [varchar] (50) NOT NULL DEFAULT (''),
	[STATE_PROVINCE] [varchar] (15) NOT NULL DEFAULT (''),
	[ZIP] [varchar] (10) NOT NULL DEFAULT (''),
	[COUNTY] [varchar] (30) NOT NULL DEFAULT (''),
	[COUNTRY] [varchar] (15) NOT NULL DEFAULT (''),
	[WORK_PHONE] [varchar] (25) NOT NULL DEFAULT (''),
	[TOLL_FREE] [varchar] (25) NOT NULL DEFAULT (''),
	[HOME_PHONE] [varchar] (25) NOT NULL DEFAULT (''),
	[FAX] [varchar] (25) NOT NULL DEFAULT (''),
	[NO_FAX] [bit] NOT NULL DEFAULT (0),
	[EMAIL] [varchar] (100) NOT NULL DEFAULT (''),
	[NO_EMAIL] [bit] NOT NULL DEFAULT (0),
	[REJECTED_EMAIL] [bit] NOT NULL DEFAULT (0),
	[WEBSITE] [varchar] (255) NOT NULL DEFAULT (''),
	[YES_WEBSITE] [bit] NOT NULL DEFAULT (0),
	[FL_BAR_ID] [varchar] (10) NOT NULL DEFAULT (''),
	[BAR_DATE] [datetime] NULL ,
	[JUDICIAL_CIRCUIT] [varchar] (10) NOT NULL DEFAULT (''),
	[GENDER] [varchar] (12) NOT NULL DEFAULT (''),
	[BIRTH_DATE] [datetime] NULL ,
	[JOIN_DATE] [datetime] NULL ,
	[LAST_REINST_DATE] [datetime] NULL ,
	[PAID_THRU] [datetime] NULL ,
	[PARTY] [varchar] (12) NOT NULL DEFAULT (''),
	[RACE] [varchar] (20) NOT NULL DEFAULT (''),
	[APPELLATE_SECTION] [bit] NOT NULL DEFAULT (0),
	[WOMENS_CAUCUS_SECTION] [bit] NOT NULL DEFAULT (0),
	[YOUNG_LAWYERS_SECTION] [bit] NOT NULL DEFAULT (0),
	[LEGISFS_ADMIN] [bit] NOT NULL DEFAULT (0),
	[LEGISFS] [bit] NOT NULL DEFAULT (0),
	[PERSONAL_INJURY] [varchar] (20) NOT NULL DEFAULT (''),
	[PERSONAL_INJURY_DEFENSE] [varchar] (20) NOT NULL DEFAULT (''),
	[FIRM_PERSONAL_INJURY] [varchar] (20) NOT NULL DEFAULT (''),
	[FIRM_PI_DEFENSE] [varchar] (20) NOT NULL DEFAULT (''),
	[ADMINISTRATIVE] [bit] NOT NULL DEFAULT (0),
	[ADMIRALTY_MARITIME] [bit] NOT NULL DEFAULT (0),
	[ANIMAL_LAW] [bit] NOT NULL DEFAULT (0),
	[APPELLATE_PRACTICE] [bit] NOT NULL DEFAULT (0),
	[ARBITRATION] [bit] NOT NULL DEFAULT (0),
	[AUTO_COLLISION] [bit] NOT NULL DEFAULT (0),
	[AUTO_INSURANCE] [bit] NOT NULL DEFAULT (0),
	[AVIATION] [bit] NOT NULL DEFAULT (0),
	[BAD_FAITH] [bit] NOT NULL DEFAULT (0),
	[BANKRUPTCY] [bit] NOT NULL DEFAULT (0),
	[BUSINESS_PLANNING] [bit] NOT NULL DEFAULT (0),
	[CATASTROPHIC_INJURY] [bit] NOT NULL DEFAULT (0),
	[CIVIL_RIGHTS] [bit] NOT NULL DEFAULT (0),
	[CLASS_ACTION] [bit] NOT NULL DEFAULT (0),
	[COMMERCIAL_LITIGATION] [bit] NOT NULL DEFAULT (0),
	[CONDO_LITIGATION] [bit] NOT NULL DEFAULT (0),
	[CONSTITUTIONAL_LITIGATION] [bit] NOT NULL DEFAULT (0),
	[CONSUMER_LAW] [bit] NOT NULL DEFAULT (0),
	[CRIMINAL_DEFENSE] [bit] NOT NULL DEFAULT (0),
	[ELECTION_LAW] [bit] NOT NULL DEFAULT (0),
	[EMINENT_DOMAIN] [bit] NOT NULL DEFAULT (0),
	[EMPLOYMENT_LAW] [bit] NOT NULL DEFAULT (0),
	[ENVIRONMENTAL_LAW] [bit] NOT NULL DEFAULT (0),
	[ERISA] [bit] NOT NULL DEFAULT (0),
	[FAMILY_LAW] [bit] NOT NULL DEFAULT (0),
	[FIRST_PARTY_INSURANCE] [bit] NOT NULL DEFAULT (0),
	[FORECLOSURES] [bit] NOT NULL DEFAULT (0),
	[GENERAL_NEGLIGENCE] [bit] NOT NULL DEFAULT (0),
	[INSURANCE_LAW] [bit] NOT NULL DEFAULT (0),
	[LEGAL_MALPRACTICE] [bit] NOT NULL DEFAULT (0),
	[LIEN_MITIGATION] [bit] NOT NULL DEFAULT (0),
	[MANAGED_CARE] [bit] NOT NULL DEFAULT (0),
	[MASS_TORTS] [bit] NOT NULL DEFAULT (0),
	[MEDIATION] [bit] NOT NULL DEFAULT (0),
	[MEDICAID_MEDICARE] [bit] NOT NULL DEFAULT (0),
	[MEDICAL_NEGLIGENCE] [bit] NOT NULL DEFAULT (0),
	[MILITARY_LAW] [bit] NOT NULL DEFAULT (0),
	[NEGLIGENT_SECURITY] [bit] NOT NULL DEFAULT (0),
	[NURSING_HOME] [bit] NOT NULL DEFAULT (0),
	[PERSONAL_INJURY2] [bit] NOT NULL DEFAULT (0),
	[PHARMACEUTICALS] [bit] NOT NULL DEFAULT (0),
	[PREMISES_LIABILITY] [bit] NOT NULL DEFAULT (0),
	[PROBATE_ESTATES] [bit] NOT NULL DEFAULT (0),
	[PRODUCTS_LIABILITY] [bit] NOT NULL DEFAULT (0),
	[PROFESSIONAL_NEGLIGENCE] [bit] NOT NULL DEFAULT (0),
	[PROPERTY_INSURANCE] [bit] NOT NULL DEFAULT (0),
	[QUI_TAM] [bit] NOT NULL DEFAULT (0),
	[RAILROAD_LAW] [bit] NOT NULL DEFAULT (0),
	[REAL_ESTATE] [bit] NOT NULL DEFAULT (0),
	[SECURITIES_LAW] [bit] NOT NULL DEFAULT (0),
	[SINKHOLE_LITIGATION] [bit] NOT NULL DEFAULT (0),
	[SOCIAL_SECURITY] [bit] NOT NULL DEFAULT (0),
	[SOVEREIGN_IMMUNITY] [bit] NOT NULL DEFAULT (0),
	[SPECIAL_NEEDS_TRUSTS] [bit] NOT NULL DEFAULT (0),
	[TAX_] [bit] NOT NULL DEFAULT (0),
	[TORT] [bit] NOT NULL DEFAULT (0),
	[TOXIC_MOLD] [bit] NOT NULL DEFAULT (0),
	[WORKERS_COMPENSATION] [bit] NOT NULL DEFAULT (0)
)

IF OBJECT_ID('tempdb..##fl_EAGLE') IS NOT NULL
	DROP TABLE ##fl_EAGLE
CREATE TABLE [##fl_EAGLE] (
	[ID] [varchar] (10) NOT NULL DEFAULT (''),
	[PLEDGE_DATE_YEAR] [varchar] (4) NULL ,
	[PLEDGE_DATE] [datetime] NULL ,
	[EXPIRE_DATE] [datetime] NULL ,
	[EAGLE_PLEDGE] [money] NOT NULL DEFAULT (0),
	[PLEDGE_PAYMENTS] [money] NOT NULL DEFAULT (0),
	[OVERALL_BALANCE] [money] NOT NULL DEFAULT (0),
	[PLEDGE_SCHEDULE] [varchar] (10) NOT NULL DEFAULT (''),
	[PAY_METHOD] [varchar] (10) NOT NULL DEFAULT (''),
	[CURRENT_INV_CHARGES] [money] NOT NULL DEFAULT (0),
	[CURRENT_INV_PAYMENT] [money] NOT NULL DEFAULT (0),
	[CURRENT_INV_BAL_DUE] [money] NOT NULL DEFAULT (0),
	[FR_REGION] [varchar] (3) NOT NULL DEFAULT (''),
	[FR_CONTACT] [varchar] (25) NOT NULL DEFAULT (''),
	[FR_CONTACT_PHN] [varchar] (25) NOT NULL DEFAULT (''),
	[PLEDGE_PRODUCT_CODE] [varchar] (15) NOT NULL DEFAULT (''),
	[EAGLE_DEDUCTIBILITY_PERCENT] [varchar] (4) NOT NULL DEFAULT (''),
	[EAGLE_DEDUCTIBILITY_YEAR] [varchar] (4) NOT NULL DEFAULT (''),
	[RENEWAL_DATE] [datetime] NULL ,
	[RENEWAL_YEAR] [varchar] (4) NULL ,
	[RENEWAL_AMOUNT] [money] NOT NULL DEFAULT (0),
	[RENEWAL_SCHEDULE] [varchar] (10) NOT NULL DEFAULT (''),
	[RENEWAL_PRODUCT_CODE] [varchar] (15) NOT NULL DEFAULT (''),
	[BFE_CATEGORY] [varchar] (50) NOT NULL DEFAULT ('')
)

IF OBJECT_ID('tempdb..##fl_SubscriptionInfo') IS NOT NULL
	DROP TABLE ##fl_SubscriptionInfo
CREATE TABLE [##fl_SubscriptionInfo] (
	[ID] [varchar] (10) NOT NULL DEFAULT (''),
	[AFTL] [varchar] (4) NOT NULL DEFAULT (''),
	[AFTL_DEDUCTIBILITY_PERCENT] [varchar] (4) NOT NULL DEFAULT (''),
	[AFTL_DEDUCTIBILITY_YEAR] [varchar] (4) NOT NULL DEFAULT (''),
	[AFTL_PAID_THRU] [datetime] NULL ,
	[AFTL_BILL_BEGIN] [datetime] NULL ,
	[AFTL_BILL_THRU] [datetime] NULL ,
	[AFTL_PREVIOUS_BALANCE] [money] NOT NULL DEFAULT (0),
	[AFTL_BILL_AMOUNT] [money] NOT NULL DEFAULT (0),
	[AFTL_ADJUSTMENT_AMOUNT] [money] NOT NULL DEFAULT (0),
	[AFTL_PAYMENT_DATE] [datetime] NULL ,
	[AFTL_PAYMENT_AMOUNT] [money] NOT NULL DEFAULT (0),
	[AFTL_BALANCE] [money] NOT NULL DEFAULT (0),
	[FLAG_OPT] [varchar] (8) NOT NULL DEFAULT (''),
	[FLAG_OPT_PAID_THRU] [datetime] NULL ,
	[FLAG_OPT_BILL_BEGIN] [datetime] NULL ,
	[FLAG_OPT_BILL_THRU] [datetime] NULL ,
	[FLAG_OPT_PREVIOUS_BALANCE] [money] NOT NULL DEFAULT (0),
	[FLAG_OPT_BILL_AMOUNT] [money] NOT NULL DEFAULT (0),
	[FLAG_OPT_ADJUSTMENT_AMOUNT] [money] NOT NULL DEFAULT (0),
	[FLAG_OPT_PAYMENT_DATE] [datetime] NULL ,
	[FLAG_OPT_PAYMENT_AMOUNT] [money] NOT NULL DEFAULT (0),
	[FLAG_OPT_BALANCE] [money] NOT NULL DEFAULT (0),
	[JOUR] [varchar] (4) NOT NULL DEFAULT (''),
	[JOUR_DEDUCTIBILITY_PERCENT] [varchar] (4) NOT NULL DEFAULT (''),
	[JOUR_DEDUCTIBILITY_YEAR] [varchar] (4) NOT NULL DEFAULT (''),
	[JOUR_PAID_THRU] [datetime] NULL ,
	[JOUR_BILL_BEGIN] [datetime] NULL ,
	[JOUR_BILL_THRU] [datetime] NULL ,
	[JOUR_PREVIOUS_BALANCE] [money] NOT NULL DEFAULT (0),
	[JOUR_BILL_AMOUNT] [money] NOT NULL DEFAULT (0),
	[JOUR_ADJUSTMENT_AMOUNT] [money] NOT NULL DEFAULT (0),
	[JOUR_PAYMENT_DATE] [datetime] NULL ,
	[JOUR_PAYMENT_AMOUNT] [money] NOT NULL DEFAULT (0),
	[JOUR_BALANCE] [money] NOT NULL DEFAULT (0),
	[TLEL] [varchar] (4) NOT NULL DEFAULT (''),
	[TLEL_PAID_THRU] [datetime] NULL ,
	[TLEL_BILL_BEGIN] [datetime] NULL ,
	[TLEL_BILL_THRU] [datetime] NULL ,
	[TLEL_PREVIOUS_BALANCE] [money] NOT NULL DEFAULT (0),
	[TLEL_BILL_AMOUNT] [money] NOT NULL DEFAULT (0),
	[TLEL_ADJUSTMENT_AMOUNT] [money] NOT NULL DEFAULT (0),
	[TLEL_PAYMENT_DATE] [datetime] NULL ,
	[TLEL_PAYMENT_AMOUNT] [money] NOT NULL DEFAULT (0),
	[TLEL_BALANCE] [money] NOT NULL DEFAULT (0),
	[NURSE] [varchar] (5) NOT NULL DEFAULT (''),
	[NURSE_PAID_THRU] [datetime] NULL ,
	[NURSE_BILL_BEGIN] [datetime] NULL ,
	[NURSE_BILL_THRU] [datetime] NULL ,
	[NURSE_PREVIOUS_BALANCE] [money] NOT NULL DEFAULT (0),
	[NURSE_BILL_AMOUNT] [money] NOT NULL DEFAULT (0),
	[NURSE_ADJUSTMENT_AMOUNT] [money] NOT NULL DEFAULT (0),
	[NURSE_PAYMENT_DATE] [datetime] NULL ,
	[NURSE_PAYMENT_AMOUNT] [money] NOT NULL DEFAULT (0),
	[NURSE_BALANCE] [money] NOT NULL DEFAULT (0),
	[FLAG_A] [varchar] (6) NOT NULL DEFAULT (''),
	[FLAG_A_PAID_THRU] [datetime] NULL ,
	[FLAG_A_BILL_BEGIN] [datetime] NULL ,
	[FLAG_A_BILL_THRU] [datetime] NULL ,
	[FLAG_A_PREVIOUS_BALANCE] [money] NOT NULL DEFAULT (0),
	[FLAG_A_BILL_AMOUNT] [money] NOT NULL DEFAULT (0),
	[FLAG_A_ADJUSTMENT_AMOUNT] [money] NOT NULL DEFAULT (0),
	[FLAG_A_PAYMENT_DATE] [datetime] NULL ,
	[FLAG_A_PAYMENT_AMOUNT] [money] NOT NULL DEFAULT (0),
	[FLAG_A_BALANCE] [money] NOT NULL DEFAULT (0),
	[FLAG_S] [varchar] (6) NOT NULL DEFAULT (''),
	[FLAG_S_PAID_THRU] [datetime] NULL ,
	[FLAG_S_BILL_BEGIN] [datetime] NULL ,
	[FLAG_S_BILL_THRU] [datetime] NULL ,
	[FLAG_S_PREVIOUS_BALANCE] [money] NOT NULL DEFAULT (0),
	[FLAG_S_BILL_AMOUNT] [money] NOT NULL DEFAULT (0),
	[FLAG_S_ADJUSTMENT_AMOUNT] [money] NOT NULL DEFAULT (0),
	[FLAG_S_PAYMENT_DATE] [datetime] NULL ,
	[FLAG_S_PAYMENT_AMOUNT] [money] NOT NULL DEFAULT (0),
	[FLAG_S_BALANCE] [money] NOT NULL DEFAULT (0),
	[FLAG_Q] [varchar] (6) NOT NULL DEFAULT (''),
	[FLAG_Q_PAID_THRU] [datetime] NULL ,
	[FLAG_Q_BILL_BEGIN] [datetime] NULL ,
	[FLAG_Q_BILL_THRU] [datetime] NULL ,
	[FLAG_Q_PREVIOUS_BALANCE] [money] NOT NULL DEFAULT (0),
	[FLAG_Q_BILL_AMOUNT] [money] NOT NULL DEFAULT (0),
	[FLAG_Q_ADJUSTMENT_AMOUNT] [money] NOT NULL DEFAULT (0),
	[FLAG_Q_PAYMENT_DATE] [datetime] NULL ,
	[FLAG_Q_PAYMENT_AMOUNT] [money] NOT NULL DEFAULT (0),
	[FLAG_Q_BALANCE] [money] NOT NULL DEFAULT (0),
	[FLAG_M] [varchar] (6) NOT NULL DEFAULT (''),
	[FLAG_M_PAID_THRU] [datetime] NULL ,
	[FLAG_M_BILL_BEGIN] [datetime] NULL ,
	[FLAG_M_BILL_THRU] [datetime] NULL ,
	[FLAG_M_PREVIOUS_BALANCE] [money] NULL ,
	[FLAG_M_BILL_AMOUNT] [money] NULL ,
	[FLAG_M_ADJUSTMENT_AMOUNT] [money] NOT NULL DEFAULT (0),
	[FLAG_M_PAYMENT_DATE] [datetime] NULL ,
	[FLAG_M_PAYMENT_AMOUNT] [money] NOT NULL DEFAULT (0),
	[FLAG_M_BALANCE] [money] NOT NULL DEFAULT (0),
)

IF OBJECT_ID('tempdb..##fl_Committee') IS NOT NULL
	DROP TABLE ##fl_Committee
CREATE TABLE [##fl_Committee] (
	[ID] [varchar] (10) NOT NULL DEFAULT (''),
	[FULL_NAME] [varchar] (150) NOT NULL DEFAULT (''), --ignored
	[COMMITTEE_CODE] [varchar] (15) NOT NULL DEFAULT (''),
	[DESCRIPTION] [varchar] (200) NOT NULL DEFAULT (''), --ignored
	[THRU_DATE] [datetime] NULL, --ignored
	[POSITION_CODE] [varchar] (15) NOT NULL DEFAULT ('')
)

-- import bcp files into temp tables. 
declare @cmd varchar(400)
EXEC membercentral.dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Importing Demographic Data'
select @cmd = 'bcp ##fl_MbrDemographics in ' + @pathToImport + 'BCP_WebDbMbrDemographics.txt -n -T -S' + CAST(serverproperty('servername') as varchar(40))
exec master..xp_cmdshell @cmd, NO_OUTPUT

EXEC membercentral.dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Importing EAGLE Data'
select @cmd = 'bcp ##fl_EAGLE in ' + @pathToImport + 'BCP_WebDb_EAGLE.txt -n -T -S' + CAST(serverproperty('servername') as varchar(40))
exec master..xp_cmdshell @cmd, NO_OUTPUT

EXEC membercentral.dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Importing Subscription Data'
select @cmd = 'bcp ##fl_SubscriptionInfo in ' + @pathToImport + 'BCP_WebDb_SubscriptionInfo.txt -n -T -S' + CAST(serverproperty('servername') as varchar(40))
exec master..xp_cmdshell @cmd, NO_OUTPUT

EXEC membercentral.dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Importing Committee Data'
select @cmd = 'bcp ##fl_Committee in ' + @pathToImport + 'BCP_WebDb_Committee.txt -n -T -S' + CAST(serverproperty('servername') as varchar(40))
exec master..xp_cmdshell @cmd, NO_OUTPUT

EXEC membercentral.dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Preparing to Flatten Data'

-- get the committee list for PIVOT
declare @grpList varchar(max)
select @grpList = COALESCE(@grpList + ',', '') + quotename(columnName)
	from membercentral.dbo.ams_memberDataColumns
	where orgID = @orgID
	and columnName like 'C\_%' escape '\'
	order by columnName

-- put group assignments into temp table to use in join below
IF OBJECT_ID('tempdb..##tmpfl_Committee') IS NOT NULL
	DROP TABLE ##tmpfl_Committee
DECLARE @grpsql varchar(max)
select @grpsql = ''
select @grpsql = @grpsql + 'select * '
select @grpsql = @grpsql + 'into ##tmpfl_Committee '
select @grpsql = @grpsql + 'from ( '
select @grpsql = @grpsql + '	select ID, ''C_'' + committee_code as columnValue, position_code '
select @grpsql = @grpsql + '	from ##fl_Committee '
select @grpsql = @grpsql + ') grps '
select @grpsql = @grpsql + 'PIVOT (min(position_code) for columnValue in (' + @grpList + ')) as p '
EXEC(@grpsql)

EXEC membercentral.dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Flattening Data'

-- put data into temp flat table
IF OBJECT_ID('tempdb..##tmpFL') IS NOT NULL
	DROP TABLE ##tmpFL
SELECT ROW_NUMBER() OVER (ORDER BY demo.ID ASC) as rowID, 
	demo.prefix, demo.first_name as firstname, demo.MIDDLE_NAME as middlename, demo.LAST_NAME as lastname, 
	demo.SUFFIX, demo.company, demo.ID as membernumber,
	demo.email, '' as BillingAddressType, 

	-- address
	demo.ADDRESS_1 as [address_address1], demo.ADDRESS_2 as [address_address2], 
	demo.city as [address_city], demo.STATE_PROVINCE as [address_stateprov],
	demo.ZIP as [address_postalcode], demo.county as [address_county], demo.COUNTRY as [address_country], 
	demo.WORK_PHONE as [address_work phone], demo.TOLL_FREE as [address_toll free], demo.HOME_PHONE as [address_home phone],
	demo.FAX as [address_fax], 

	-- misc fields	
	demo.member_Type + '_' + demo.CATEGORY as RIGHTS, demo.CO_ID, 
	demo.MEMBER_TYPE, demo.CATEGORY, demo.NO_FAX, demo.YES_WEBSITE, demo.NO_EMAIL, demo.REJECTED_EMAIL, 
	demo.WEBSITE, FL_BAR_ID, convert(varchar(10),demo.BAR_DATE,101) as BAR_DATE, 
	nullIf(demo.JUDICIAL_CIRCUIT,'') as JUDICIAL_CIRCUIT, demo.GENDER, convert(varchar(10),demo.BIRTH_DATE,101) as BIRTH_DATE,
	convert(varchar(10),demo.JOIN_DATE,101) as JOIN_DATE, 
	convert(varchar(10),demo.LAST_REINST_DATE,101) as LAST_REINST_DATE, 
	convert(varchar(10),demo.PAID_THRU,101) as PAID_THRU, 
	demo.PARTY, demo.RACE, demo.PERSONAL_INJURY, demo.PERSONAL_INJURY_DEFENSE, 
	demo.FIRM_PERSONAL_INJURY, demo.FIRM_PI_DEFENSE, 
	demo.LEGISFS_ADMIN, demo.LEGISFS, 

	-- committees
	grps.*,

	-- sections
	demo.APPELLATE_SECTION as S_APPELLATE_SECTION, 
	demo.WOMENS_CAUCUS_SECTION as S_WOMENS_CAUCUS_SECTION, 
	demo.YOUNG_LAWYERS_SECTION as S_YOUNG_LAWYERS_SECTION, 

	-- practice areas
	demo.ADMINISTRATIVE as P_ADMINISTRATIVE, 
	demo.ADMIRALTY_MARITIME as P_ADMIRALTY_MARITIME, 
	demo.ANIMAL_LAW as P_ANIMAL_LAW, 
	demo.APPELLATE_PRACTICE as P_APPELLATE_PRACTICE, 
	demo.ARBITRATION as P_ARBITRATION, 
	demo.AUTO_COLLISION as P_AUTO_COLLISION, 
	demo.AUTO_INSURANCE as P_AUTO_INSURANCE, 
	demo.AVIATION as P_AVIATION, 
	demo.BAD_FAITH as P_BAD_FAITH, 
	demo.BANKRUPTCY as P_BANKRUPTCY, 
	demo.BUSINESS_PLANNING as P_BUSINESS_PLANNING, 
	demo.CATASTROPHIC_INJURY as P_CATASTROPHIC_INJURY, 
	demo.CIVIL_RIGHTS as P_CIVIL_RIGHTS, 
	demo.CLASS_ACTION as P_CLASS_ACTION, 
	demo.COMMERCIAL_LITIGATION as P_COMMERCIAL_LITIGATION, 
	demo.CONDO_LITIGATION as P_CONDO_LITIGATION, 
	demo.CONSTITUTIONAL_LITIGATION as P_CONSTITUTIONAL_LITIGATION, 
	demo.CONSUMER_LAW as P_CONSUMER_LAW, 
	demo.CRIMINAL_DEFENSE as P_CRIMINAL_DEFENSE, 
	demo.ELECTION_LAW as P_ELECTION_LAW, 
	demo.EMINENT_DOMAIN as P_EMINENT_DOMAIN, 
	demo.EMPLOYMENT_LAW as P_EMPLOYMENT_LAW, 
	demo.ENVIRONMENTAL_LAW as P_ENVIRONMENTAL_LAW, 
	demo.ERISA as P_ERISA, 
	demo.FAMILY_LAW as P_FAMILY_LAW, 
	demo.FIRST_PARTY_INSURANCE as P_FIRST_PARTY_INSURANCE, 
	demo.FORECLOSURES as P_FORECLOSURES, 
	demo.GENERAL_NEGLIGENCE as P_GENERAL_NEGLIGENCE, 
	demo.INSURANCE_LAW as P_INSURANCE_LAW, 
	demo.LEGAL_MALPRACTICE as P_LEGAL_MALPRACTICE, 
	demo.LIEN_MITIGATION as P_LIEN_MITIGATION, 
	demo.MANAGED_CARE as P_MANAGED_CARE, 
	demo.MASS_TORTS as P_MASS_TORTS, 
	demo.MEDIATION as P_MEDIATION, 
	demo.MEDICAID_MEDICARE as P_MEDICAID_MEDICARE, 
	demo.MEDICAL_NEGLIGENCE as P_MEDICAL_NEGLIGENCE, 
	demo.MILITARY_LAW as P_MILITARY_LAW, 
	demo.NEGLIGENT_SECURITY as P_NEGLIGENT_SECURITY, 
	demo.NURSING_HOME as P_NURSING_HOME, 
	demo.PERSONAL_INJURY2 as P_PERSONAL_INJURY2, 
	demo.PHARMACEUTICALS as P_PHARMACEUTICALS, 
	demo.PREMISES_LIABILITY as P_PREMISES_LIABILITY, 
	demo.PROBATE_ESTATES as P_PROBATE_ESTATES, 
	demo.PRODUCTS_LIABILITY as P_PRODUCTS_LIABILITY, 
	demo.PROFESSIONAL_NEGLIGENCE as P_PROFESSIONAL_NEGLIGENCE, 
	demo.PROPERTY_INSURANCE as P_PROPERTY_INSURANCE, 
	demo.QUI_TAM as P_QUI_TAM, 
	demo.RAILROAD_LAW as P_RAILROAD_LAW, 
	demo.REAL_ESTATE as P_REAL_ESTATE, 
	demo.SECURITIES_LAW as P_SECURITIES_LAW, 
	demo.SINKHOLE_LITIGATION as P_SINKHOLE_LITIGATION, 
	demo.SOCIAL_SECURITY as P_SOCIAL_SECURITY, 
	demo.SOVEREIGN_IMMUNITY as P_SOVEREIGN_IMMUNITY, 
	demo.SPECIAL_NEEDS_TRUSTS as P_SPECIAL_NEEDS_TRUSTS, 
	demo.TAX_ as P_TAX_, 
	demo.TORT as P_TORT, 
	demo.TOXIC_MOLD as P_TOXIC_MOLD, 
	demo.WORKERS_COMPENSATION as P_WORKERS_COMPENSATION, 

	-- misc fields	
	eagle.PLEDGE_DATE_YEAR, eagle.PLEDGE_DATE, eagle.EXPIRE_DATE, eagle.EAGLE_PLEDGE,
	eagle.PLEDGE_PAYMENTS, eagle.OVERALL_BALANCE, eagle.PLEDGE_SCHEDULE, eagle.PAY_METHOD,
	eagle.CURRENT_INV_CHARGES, eagle.CURRENT_INV_PAYMENT, eagle.CURRENT_INV_BAL_DUE, 
	eagle.FR_REGION, eagle.FR_CONTACT, eagle.FR_CONTACT_PHN, eagle.PLEDGE_PRODUCT_CODE,
	eagle.EAGLE_DEDUCTIBILITY_PERCENT, eagle.EAGLE_DEDUCTIBILITY_YEAR, eagle.RENEWAL_DATE, 
	eagle.RENEWAL_YEAR, eagle.RENEWAL_AMOUNT, eagle.RENEWAL_SCHEDULE, eagle.RENEWAL_PRODUCT_CODE,
	eagle.BFE_CATEGORY,
	subinfo.AFTL, subinfo.AFTL_DEDUCTIBILITY_PERCENT, subinfo.AFTL_DEDUCTIBILITY_YEAR,
	subinfo.AFTL_PAID_THRU, subinfo.AFTL_BILL_BEGIN, subinfo.AFTL_BILL_THRU, 
	subinfo.AFTL_PREVIOUS_BALANCE, subinfo.AFTL_BILL_AMOUNT, subinfo.AFTL_ADJUSTMENT_AMOUNT,
	subinfo.AFTL_PAYMENT_DATE, subinfo.AFTL_PAYMENT_AMOUNT, subinfo.AFTL_BALANCE, 
	subinfo.FLAG_OPT, subinfo.FLAG_OPT_PAID_THRU, subinfo.FLAG_OPT_BILL_BEGIN, 
	subinfo.FLAG_OPT_BILL_THRU, subinfo.FLAG_OPT_PREVIOUS_BALANCE, subinfo.FLAG_OPT_BILL_AMOUNT,
	subinfo.FLAG_OPT_ADJUSTMENT_AMOUNT, subinfo.FLAG_OPT_PAYMENT_DATE, subinfo.FLAG_OPT_PAYMENT_AMOUNT,
	subinfo.FLAG_OPT_BALANCE,
	subinfo.JOUR, subinfo.JOUR_DEDUCTIBILITY_PERCENT, subinfo.JOUR_DEDUCTIBILITY_YEAR,
	subinfo.JOUR_PAID_THRU, subinfo.JOUR_BILL_BEGIN, subinfo.JOUR_BILL_THRU, 
	subinfo.JOUR_PREVIOUS_BALANCE, subinfo.JOUR_BILL_AMOUNT, subinfo.JOUR_ADJUSTMENT_AMOUNT,
	subinfo.JOUR_PAYMENT_DATE, subinfo.JOUR_PAYMENT_AMOUNT, subinfo.JOUR_BALANCE,
	subinfo.TLEL, subinfo.TLEL_PAID_THRU, subinfo.TLEL_BILL_BEGIN, subinfo.TLEL_BILL_THRU,
	subinfo.TLEL_PREVIOUS_BALANCE, subinfo.TLEL_BILL_AMOUNT, subinfo.TLEL_ADJUSTMENT_AMOUNT,
	subinfo.TLEL_PAYMENT_DATE, subinfo.TLEL_PAYMENT_AMOUNT, subinfo.TLEL_BALANCE,
	subinfo.NURSE, subinfo.NURSE_PAID_THRU, subinfo.NURSE_BILL_BEGIN, subinfo.NURSE_BILL_THRU,
	subinfo.NURSE_PREVIOUS_BALANCE, subinfo.NURSE_BILL_AMOUNT, subinfo.NURSE_ADJUSTMENT_AMOUNT,
	subinfo.NURSE_PAYMENT_DATE, subinfo.NURSE_PAYMENT_AMOUNT, subinfo.NURSE_BALANCE,
	subinfo.FLAG_A, subinfo.FLAG_A_PAID_THRU, subinfo.FLAG_A_BILL_BEGIN, subinfo.FLAG_A_BILL_THRU,
	subinfo.FLAG_A_PREVIOUS_BALANCE, subinfo.FLAG_A_BILL_AMOUNT, subinfo.FLAG_A_ADJUSTMENT_AMOUNT,
	subinfo.FLAG_A_PAYMENT_DATE, subinfo.FLAG_A_PAYMENT_AMOUNT, subinfo.FLAG_A_BALANCE,
	subinfo.FLAG_S, subinfo.FLAG_S_PAID_THRU, subinfo.FLAG_S_BILL_BEGIN, subinfo.FLAG_S_BILL_THRU,
	subinfo.FLAG_S_PREVIOUS_BALANCE, subinfo.FLAG_S_BILL_AMOUNT, subinfo.FLAG_S_ADJUSTMENT_AMOUNT,
	subinfo.FLAG_S_PAYMENT_DATE, subinfo.FLAG_S_PAYMENT_AMOUNT, subinfo.FLAG_S_BALANCE,
	subinfo.FLAG_Q, subinfo.FLAG_Q_PAID_THRU, subinfo.FLAG_Q_BILL_BEGIN, subinfo.FLAG_Q_BILL_THRU,
	subinfo.FLAG_Q_PREVIOUS_BALANCE, subinfo.FLAG_Q_BILL_AMOUNT, subinfo.FLAG_Q_ADJUSTMENT_AMOUNT,
	subinfo.FLAG_Q_PAYMENT_DATE, subinfo.FLAG_Q_PAYMENT_AMOUNT, subinfo.FLAG_Q_BALANCE, 
	subinfo.FLAG_M, subinfo.FLAG_M_PAID_THRU, subinfo.FLAG_M_BILL_BEGIN, subinfo.FLAG_M_BILL_THRU,
	subinfo.FLAG_M_PREVIOUS_BALANCE, subinfo.FLAG_M_BILL_AMOUNT, subinfo.FLAG_M_ADJUSTMENT_AMOUNT,
	subinfo.FLAG_M_PAYMENT_DATE, subinfo.FLAG_M_PAYMENT_AMOUNT, subinfo.FLAG_M_BALANCE
INTO ##tmpFL
FROM ##fl_MbrDemographics as demo
LEFT OUTER JOIN ##fl_EAGLE as eagle on eagle.ID = demo.ID
LEFT OUTER JOIN ##fl_SubscriptionInfo as subinfo on subinfo.ID = demo.ID
LEFT OUTER JOIN ##tmpfl_Committee as grps on grps.ID = demo.ID

-- drop extra column picked up in the grps.* selection
ALTER TABLE ##tmpFL DROP COLUMN ID;

-- cleanup
IF OBJECT_ID('tempdb..##fl_MbrDemographics') IS NOT NULL
	DROP TABLE ##fl_MbrDemographics
IF OBJECT_ID('tempdb..##fl_EAGLE') IS NOT NULL
	DROP TABLE ##fl_EAGLE
IF OBJECT_ID('tempdb..##fl_SubscriptionInfo') IS NOT NULL
	DROP TABLE ##fl_SubscriptionInfo
IF OBJECT_ID('tempdb..##fl_Committee') IS NOT NULL
	DROP TABLE ##fl_Committee
IF OBJECT_ID('tempdb..##tmpfl_Committee') IS NOT NULL
	DROP TABLE ##tmpfl_Committee

-- move data to holding tables
EXEC memberCentral.dbo.ams_importMemberData_tempToHolding @orgid=@orgID, @tmptbl='##tmpFL', @pathToExport=@pathToExport, @importResult=@importResult OUTPUT

RETURN 0
GO

ALTER PROC [dbo].[la_importMemberData]
@pathToImport varchar(200), 
@pathToExport varchar(200),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

DECLARE @orgID int
SELECT @orgID = membercentral.dbo.fn_getOrgIDFromOrgCode('LA')

EXEC membercentral.dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Starting la_importMemberData'

-- create temp tables
IF OBJECT_ID('tempdb..##la_all') IS NOT NULL
	DROP TABLE ##la_all
CREATE TABLE ##la_all (
	[MEMBER_TYPE] [varchar](5) NOT NULL DEFAULT (''),
	[LawStudent] [varchar](3) NOT NULL DEFAULT (''),
	[WorkersComp] [varchar](3) NOT NULL DEFAULT (''),
	[Staff] [varchar](3) NOT NULL DEFAULT (''),
	[STEP] [varchar](3) NOT NULL DEFAULT (''),
	[MedicalMalpractic] [varchar](3) NOT NULL DEFAULT (''),
	[Martime] [varchar](3) NOT NULL DEFAULT (''),
	[LawTechnology] [varchar](3) NOT NULL DEFAULT (''),
	[InsuranceLaw] [varchar](3) NOT NULL DEFAULT (''),
	[FamilyLaw] [varchar](3) NOT NULL DEFAULT (''),
	[EmploymentLaw] [varchar](3) NOT NULL DEFAULT (''),
	[CriminalLaw] [varchar](3) NOT NULL DEFAULT (''),
	[CommercialLitigat] [varchar](3) NOT NULL DEFAULT (''),
	[Civilrights] [varchar](3) NOT NULL DEFAULT (''),
	[Aviation] [varchar](3) NOT NULL DEFAULT (''),
	[AutoTorts] [varchar](3) NOT NULL DEFAULT (''),
	[AllowTrialSmith] [nvarchar](255) NOT NULL DEFAULT (''),
	[PREFIX] [varchar](25) NOT NULL DEFAULT (''),
	[FIRST_NAME] [varchar](20) NOT NULL DEFAULT (''),
	[MIDDLE_NAME] [varchar](20) NOT NULL DEFAULT (''),
	[LAST_NAME] [varchar](30) NOT NULL DEFAULT (''),
	[SUFFIX] [varchar](10) NOT NULL DEFAULT (''),
	[COMPANY] [varchar](80) NOT NULL DEFAULT (''),
	[ADDRESS_1] [varchar](40) NOT NULL DEFAULT (''),
	[CITY] [varchar](40) NOT NULL DEFAULT (''),
	[STATE_PROVINCE] [varchar](15) NOT NULL DEFAULT (''),
	[ZIP] [varchar](10) NOT NULL DEFAULT (''),
	[WORK_PHONE] [varchar](25) NOT NULL DEFAULT (''),
	[FAX] [varchar](25) NOT NULL DEFAULT (''),
	[EMAIL] [varchar](100) NOT NULL DEFAULT (''),
	[MemberNumber] [varchar](10) NOT NULL PRIMARY KEY,
	[COUNTY] [varchar](30) NOT NULL DEFAULT (''),
	[MemberDate] [datetime] NULL,
	[WEBSITE] [varchar](255) NOT NULL DEFAULT (''),
	[GENDER] [varchar](1) NOT NULL DEFAULT (''), 
	[BARDATE] [datetime] NULL,
	[STATEBAR] [varchar](20) NOT NULL DEFAULT (''),
	[PositionAmicus] [nvarchar](255) NOT NULL DEFAULT (''),
	[PositionBoardOfGovernors] [nvarchar](255) NOT NULL DEFAULT (''),
	[PositionEthics] [nvarchar](255) NOT NULL DEFAULT (''),
	[PositionLegis] [nvarchar](255) NOT NULL DEFAULT (''),
	[PositionNewLawyer] [nvarchar](255) NOT NULL DEFAULT (''),
	[PositionWomenCaucus] [nvarchar](255) NOT NULL DEFAULT (''),
	[Eagles] [varchar](3) NOT NULL DEFAULT (''),
	[Address_Address2] [varchar](40) NOT NULL DEFAULT (''),
	[Address_Country] [varchar](25) NOT NULL DEFAULT (''),
	[ProdLiab] [varchar](3) NOT NULL DEFAULT (''),
	[ParalegalLegalAsst] [varchar](3) NOT NULL DEFAULT ('')
)

-- import bcp files into temp tables. 
declare @cmd varchar(400)
EXEC membercentral.dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Importing Data'
select @cmd = 'bcp ##la_all in ' + @pathToImport + 'LAImport.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
exec master..xp_cmdshell @cmd, NO_OUTPUT

EXEC membercentral.dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Flattening Data'

-- data is already flat; just alias cols
IF OBJECT_ID('tempdb..##tmpLA') IS NOT NULL
	DROP TABLE ##tmpLA
SELECT ROW_NUMBER() OVER (ORDER BY membernumber ASC) as rowID, 
	prefix, first_name as firstname, middle_name as middlename, last_name as lastname, 
	suffix, company, membernumber, '' as BillingAddressType, 

	Email, Website, 

	'' as [Address_attn], Address_1 as [Address_address1], Address_Address2 as [Address_address2], 
	city as [Address_city], state_province as [Address_stateprov], zip as [Address_postalcode], 
	county as [Address_county], 
	case when len(Address_Country) = 0 then 'United States' else Address_Country end as [Address_country], 
	work_phone as [Address_phone], Fax as [Address_fax], 

	AllowTrialSmith as AllowTS, AutoTorts as [Auto Torts Section], Aviation as [Aviation Connection],
	Civilrights as [Civil Rights Section], CommercialLitigat as [Commercial Litigation Section],
	CriminalLaw as [Criminal Law Section], EmploymentLaw as [Employment Law Section],
	FamilyLaw as [Family Law Section], InsuranceLaw as [Insurance Law Section],
	LawTechnology as [Law Technology Section], Martime as [Maritime Section],
	MedicalMalpractic as [Medical Malpractice Section], Member_Type, Staff, STEP as [STEP],
	WorkersComp as [Workers Compensation Section], convert(varchar(10),MemberDate,101) as MemberDate, 
	PositionAmicus as [Amicus Curiae Committee], PositionBoardOfGovernors as [Board of Governors],
	Eagles as [Eagles], LawStudent as [Law Student], PositionLegis as [Legislation Committee],
	PositionNewLawyer as [New Lawyers Division], PositionWomenCaucus as [Womens Caucus],
	ProdLiab as [ProdLiab], ParalegalLegalAsst as [ParalegalLegalAsst]
INTO ##tmpLA
FROM ##la_all

-- cleanup
IF OBJECT_ID('tempdb..##la_all') IS NOT NULL
	DROP TABLE ##la_all

-- move data to holding tables
EXEC memberCentral.dbo.ams_importMemberData_tempToHolding @orgid=@orgID, @tmptbl='##tmpLA', @pathToExport=@pathToExport, @importResult=@importResult OUTPUT

RETURN 0
GO

ALTER PROC [dbo].[TAGD_importNationalData]
@batchDate datetime,
@recordedByMemberID int,
@statsSessionID int,
@pathToImport varchar(200), 
@pathToExport varchar(200),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

DECLARE @orgID int, @siteID int, @cmd varchar(600), @IDList VARCHAR(max), @finalMSG varchar(max), @emailtouse varchar(300),
	@flatfile varchar(200)
SELECT @orgID = membercentral.dbo.fn_getOrgIDFromOrgCode('TAGD')
SELECT @siteID = membercentral.dbo.fn_getSiteIDFromSiteCode('TAGD')

-- create temp tables
IF OBJECT_ID('tempdb..##tagd_nationals_alpha') IS NOT NULL
	DROP TABLE ##tagd_nationals_alpha
CREATE TABLE ##tagd_nationals_alpha (
	[rowid] [int] NOT NULL,
	[ID] [varchar](50) NOT NULL,
	[MEMBER TYPE] [varchar](10) NOT NULL DEFAULT (''),
	[MEMBER TYPE DESCRIPTION] [varchar](100) NOT NULL DEFAULT (''),
	[CATEGORY DESCRIPTION] [varchar](200) NOT NULL DEFAULT (''),
	[FULL NAME] [varchar](200) NOT NULL DEFAULT (''),
	[LAST FIRST] [varchar](200) NOT NULL DEFAULT (''),
	[FIRST NAME] [varchar](75) NOT NULL DEFAULT (''),
	[MIDDLE NAME] [varchar](25) NOT NULL DEFAULT (''),
	[LAST NAME] [varchar](75) NOT NULL DEFAULT (''),
	[SUFFIX] [varchar](50) NOT NULL DEFAULT (''),
	[DESIGNATION] [varchar](max) NULL,
	[WORK PHONE] [varchar](40) NULL,
	[HOME PHONE] [varchar](40) NULL,
	[FAX] [varchar](40) NULL,
	[FULL ADDRESS] [varchar](200) NOT NULL DEFAULT (''),
	[ADDRESS 1] [varchar](100) NULL,
	[ADDRESS 2] [varchar](100) NULL,
	[CITY] [varchar](35) NULL,
	[STATE PROVINCE] [varchar](25) NULL,
	[ZIP] [varchar](25) NULL,
	[GRAD DATE] datetime NULL,
	[DENTAL SCHOOL] [varchar](300) NULL,
	[JOIN DATE] datetime NULL,
	[PAID THRU] datetime NULL,
	[DO NOT MAIL] [varchar](300) NOT NULL DEFAULT (''),
	[EMAIL] [varchar](255) NULL,
	[COMPONENT TITLE] [varchar](300) NOT NULL DEFAULT (''),
	[BIRTH DATE] datetime NULL,
	[BAD ADDRESS] [varchar](300) NOT NULL DEFAULT ('')
)

IF OBJECT_ID('tempdb..##tagd_nationals_new') IS NOT NULL
	DROP TABLE ##tagd_nationals_new
CREATE TABLE ##tagd_nationals_new (
	[rowid] [int] NOT NULL,
	[ID] [varchar](50) NOT NULL,
	[FIRST NAME] [varchar](75) NOT NULL DEFAULT (''), 
	[MIDDLE NAME] [varchar](25) NOT NULL DEFAULT (''), 
	[LAST NAME] [varchar](75) NOT NULL DEFAULT (''), 
	[WORK ADDRESS 1] [varchar](100) NULL, 
	[WORK ADDRESS 2] [varchar](100) NULL, 
	[WORK CITY] [varchar](35) NULL, 
	[WORK STATE] [varchar](25) NULL, 
	[WORK ZIP] [varchar](25) NULL, 
	[WORK PHONE] [varchar](40) NULL, 
	[WORK FAX] [varchar](40) NULL, 
	[WORK EMAIL] [varchar](200) NULL, 
	[HOME ADDRESS 1] [varchar](100) NULL, 
	[HOME ADDRESS 2] [varchar](100) NULL, 
	[HOME CITY] [varchar](35) NULL, 
	[HOME STATE] [varchar](25) NULL, 
	[HOME ZIP] [varchar](25) NULL, 
	[HOME PHONE] [varchar](40) NULL, 
	[HOME FAX] [varchar](40) NULL, 
	[HOME EMAIL] [varchar](255) NULL, 
	[BIRTH DATE] datetime NULL,
	[WEBSITE] [varchar](200) NULL, 
	[PREFERRED ADDRESS] [varchar](200) NOT NULL DEFAULT (''), 
	[MEMBER TYPE] [varchar](200) NOT NULL DEFAULT (''), 
	[MEMBER TYPE CATEGORY] [varchar](200) NOT NULL DEFAULT (''), 
	[GENDER] [varchar](200) NULL , 
	[ETHNICITY] [varchar](200) NULL, 
	[MEMBER OF OTHER DENTAL ORGANIZATION] [varchar](200) NOT NULL DEFAULT (''), 
	[PREVIOUS MEMBER] [varchar](200) NOT NULL DEFAULT (''), 
	[JOIN DATE] datetime NULL,
	[PAID THRU] datetime NULL,
	[COMPONENT] [varchar](200) NOT NULL DEFAULT (''), 
	[DENTAL DEGREE] [varchar](200) NOT NULL DEFAULT (''), 
	[DENTAL SCHOOL] [varchar](200) NOT NULL DEFAULT (''), 
	[GRAD_DATE] datetime NULL,
	[GRADE] [varchar](200) NOT NULL DEFAULT (''), 
	[US RESIDENT] [varchar](200) NOT NULL DEFAULT (''), 
	[POSTDOCTORAL INSTITUTION] [varchar](200) NOT NULL DEFAULT (''), 
	[USA LICENSE] [varchar](200) NOT NULL DEFAULT (''), 
	[LICENSENO] [varchar](200) NULL, 
	[FEDERAL SERVICES] [varchar](200) NOT NULL DEFAULT (''), 
	[BRANCH] [varchar](200) NOT NULL DEFAULT (''), 
	[PRACTICE ENVIRONMENT] [varchar](200) NOT NULL DEFAULT (''), 
	[SPECIALTY] [varchar](200) NOT NULL DEFAULT ('')
)


IF OBJECT_ID('tempdb..##tagd_nationals_dues') IS NOT NULL
	DROP TABLE ##tagd_nationals_dues
CREATE TABLE ##tagd_nationals_dues (
	[rowid] [int] NOT NULL,
	[ID] [varchar](50) NOT NULL,
	[CHAPTER] [varchar](200) NOT NULL DEFAULT (''), 
	[FIRST NAME] [varchar](75) NOT NULL DEFAULT (''), 
	[MIDDLE NAME] [varchar](25) NOT NULL DEFAULT (''), 
	[LAST NAME] [varchar](75) NOT NULL DEFAULT (''), 
	[SUFFIX] [varchar](50) NOT NULL DEFAULT (''), 
	[DESIGNATION] [varchar](max) NULL, 
	[ADDRESS 1] [varchar](100) NOT NULL DEFAULT (''), 
	[ADDRESS 2] [varchar](100) NOT NULL DEFAULT (''), 
	[CITY] [varchar](35) NOT NULL DEFAULT (''), 
	[STATE] [varchar](25) NOT NULL DEFAULT (''), 
	[ZIP] [varchar](25) NOT NULL DEFAULT (''), 
	[WORK PHONE] [varchar](40) NOT NULL DEFAULT (''), 
	[FAX] [varchar](40) NOT NULL DEFAULT (''), 
	[EMAIL] [varchar](255) NULL, 
	[MEMBER TYPE] [varchar](200) NOT NULL DEFAULT (''), 
	[MEMBER TYPE DESCRIPTION] [varchar](200) NOT NULL DEFAULT (''), 
	[CATEGORY] [varchar](200) NOT NULL DEFAULT (''), 
	[CATEGORY DESCRIPTION] [varchar](200) NOT NULL DEFAULT (''), 
	[MEMBER TYPE BREAKDOWN] [varchar](200) NOT NULL DEFAULT (''), 
	[GRAD DATE] datetime NULL,
	[JOIN DATE] datetime NULL,
	[PAID THRU] datetime NULL,
	[CONSTITUENT] [varchar](200) NOT NULL DEFAULT (''), 
	[CONSTITUENT DUES] [decimal](9,2) NOT NULL, 
	[CONSTITUENT PAID] [decimal](9,2) NOT NULL, 
	[CONSTITUENT ABBREVIATION] [varchar](200) NOT NULL DEFAULT ('')
)


-- import csv files into temp tables.
BEGIN TRY
	SELECT @cmd = 'BULK INSERT ##tagd_nationals_alpha FROM ''' + @pathToImport + '1.csv'' WITH (DATAFILETYPE = ''widechar'', FIELDTERMINATOR = '''+ char(7) + ''', FIRSTROW = 2);'
	exec(@cmd)
	SELECT @cmd = 'BULK INSERT ##tagd_nationals_new FROM ''' + @pathToImport + '2.csv'' WITH (DATAFILETYPE = ''widechar'', FIELDTERMINATOR = '''+ char(7) + ''', FIRSTROW = 2);'
	exec(@cmd)
	SELECT @cmd = 'BULK INSERT ##tagd_nationals_dues FROM ''' + @pathToImport + '3.csv'' WITH (DATAFILETYPE = ''widechar'', FIELDTERMINATOR = '''+ char(7) + ''', FIRSTROW = 2);'
	exec(@cmd)
END TRY
BEGIN CATCH
	SET @importResult = '<import date="" flatfile=""><errors><error msg="Unable to import data. Ensure the columns are in the correct order and match previous imports." severity="fatal" /></errors></import>'
	GOTO on_cleanup
END CATCH


/* *********** */
/* DATA CHECKS */
/* *********** */
-- ensure IDs appear only once and set as primary key
BEGIN TRY
	ALTER TABLE ##tagd_nationals_alpha ADD CONSTRAINT PK__tagd_nationals_alpha PRIMARY KEY CLUSTERED (ID) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
END TRY
BEGIN CATCH
	SET @IDList = null
	SELECT @IDList = COALESCE(@IDList + ', ', '') + ID 
		from ##tagd_nationals_alpha
		group by ID
		having count(*) > 1
	SET @importResult = '<import date="" flatfile=""><errors><error msg="The file uploaded for Constituent Alpha Roster contains multiple rows for the following IDs: ' + @IDList + '. Ensure each ID appears only once in the file." severity="fatal" /></errors></import>'
	GOTO on_cleanup
END CATCH

-- ensure IDs appear only once and set as primary key
BEGIN TRY
	ALTER TABLE ##tagd_nationals_new ADD CONSTRAINT PK__tagd_nationals_new PRIMARY KEY CLUSTERED (ID) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
END TRY
BEGIN CATCH
	SET @IDList = null
	SELECT @IDList = COALESCE(@IDList + ', ', '') + ID 
		from ##tagd_nationals_new
		group by ID
		having count(*) > 1
	SET @importResult = '<import date="" flatfile=""><errors><error msg="The file uploaded for Constituent New Members contains multiple rows for the following IDs: ' + @IDList + '. Ensure each ID appears only once in the file." severity="fatal" /></errors></import>'
	GOTO on_cleanup
END CATCH

-- no missing PAID THRU dates for accounting
IF EXISTS (select top 1 ID from ##tagd_nationals_dues where [PAID THRU] is null) BEGIN
	SET @IDList = null
	SELECT @IDList = COALESCE(@IDList + ', ', '') + ID 
		from ##tagd_nationals_dues
		where [PAID THRU] is null
		group by ID
	SET @importResult = '<import date="" flatfile=""><errors><error msg="The file uploaded for Constituent Dues contains missing Paid Thru Dates for the following IDs: ' + @IDList + '. Ensure each row has a Paid Thru Date in the file." severity="fatal" /></errors></import>'
	GOTO on_cleanup
END

-- no negative amounts for accounting
IF EXISTS (select top 1 ID from ##tagd_nationals_dues where [Constituent PAID] < 0) BEGIN
	SET @IDList = null
	SELECT @IDList = COALESCE(@IDList + ', ', '') + ID 
		from ##tagd_nationals_dues
		where [Constituent PAID] < 0
		group by ID
	SET @importResult = '<import date="" flatfile=""><errors><error msg="The file uploaded for Constituent Dues contains negative CONSTITUENT PAID amounts for the following IDs: ' + @IDList + '. Negative amounts are not permitted." severity="fatal" /></errors></import>'
	GOTO on_cleanup
END



/* ****************** */
/* MEMBER DATA IMPORT */
/* ****************** */
-- flatten alpha table
IF OBJECT_ID('tempdb..##tagd_nationals_Members') IS NOT NULL
	DROP TABLE ##tagd_nationals_Members
select 
	m.prefix, 
	alpha.[FIRST NAME] as firstname, alpha.[MIDDLE NAME] as middlename, alpha.[LAST NAME] as lastname, alpha.SUFFIX as suffix, 
	cast(alpha.[DESIGNATION] as varchar(100)) as professionalSuffix, alpha.[ID] as memberNumber,
	m.company, '' as BillingAddressType, 
	
	vw.[Mailing Address_attn], 
	alpha.[ADDRESS 1] as [Mailing Address_address1], alpha.[ADDRESS 2] as [Mailing Address_address2], alpha.[CITY] as [Mailing Address_city],
	alpha.[STATE PROVINCE] as [Mailing Address_stateprov], alpha.[ZIP] as [Mailing Address_postalCode], cast('' as varchar(50)) as [Mailing Address_county],
	cast('United States' as varchar(100)) as [Mailing Address_country], alpha.[WORK PHONE] as [Mailing Address_Phone], alpha.[FAX] as [Mailing Address_Fax],
	vw.[Mailing Address_Cell], vw.[Mailing Address_2nd Phone],
 
	vw.[Office Address_address1], vw.[Office Address_address2], vw.[Office Address_city], vw.[Office Address_stateprov],
	vw.[Office Address_postalCode], vw.[Office Address_county], vw.[Office Address_country], vw.[Office Address_Phone], 
	vw.[Office Address_Fax], vw.[Office Address_Cell], vw.[Office Address_2nd Phone],

	vw.[Other Address_address1], vw.[Other Address_address2], vw.[Other Address_city], vw.[Other Address_stateprov], vw.[Other Address_postalCode],
	vw.[Other Address_county], vw.[Other Address_country], vw.[Other Address_Phone], vw.[Other Address_Fax], vw.[Other Address_Cell],
	vw.[Other Address_2nd Phone],

	coalesce(new.[HOME ADDRESS 1],vw.[Home Address_address1]) as [Home Address_address1],
	coalesce(new.[HOME ADDRESS 2],vw.[Home Address_address2]) as [Home Address_address2],	
	coalesce(new.[HOME CITY],vw.[Home Address_city]) as [Home Address_city],	
	coalesce(new.[HOME STATE],vw.[Home Address_stateprov]) as [Home Address_stateprov], 
	coalesce(new.[HOME ZIP],vw.[Home Address_postalCode]) as [Home Address_postalCode],
	vw.[Home Address_county], 
	case when new.[HOME STATE] is not null then 'United States' else vw.[Home Address_country] end as [Home Address_country], 
	alpha.[HOME PHONE] as [Home Address_Phone], 
	coalesce(new.[HOME FAX],vw.[Home Address_Fax]) as [Home Address_Fax],
	vw.[Home Address_Cell], vw.[Home Address_2nd Phone],

	coalesce(new.WEBSITE,vw.[Business Website]) as [Business Website],
	alpha.Email as [Email],
	coalesce(new.[HOME EMAIL],vw.[Personal Email]) as [Personal Email],
	vw.[Other Email],

	alpha.[JOIN DATE] as [AGD Join Date],
	cast(alpha.[ID] as varchar(255)) as [AGD Member Number], 
	alpha.[PAID THRU] as [AGD Paid Thru Date],
	cast(alpha.[FIRST NAME] as varchar(255)) as [Badge Name], 

	cast(case
	when alpha.[MEMBER TYPE] = 'AC' and alpha.[CATEGORY DESCRIPTION] = 'Disability Waiver' then 'General Dentist|Dues Waived'
	when alpha.[MEMBER TYPE] IN ('AC','EM','HM') then 'General Dentist'
	when alpha.[MEMBER TYPE] = 'AF' then 'Affiliate'
	when alpha.[MEMBER TYPE] = 'AS' then 'General Dentist'
	when alpha.[MEMBER TYPE] = 'RE' then 'Retired'
	when alpha.[MEMBER TYPE] = 'ST' then 'Student'
	else '' end as varchar(max)) as [Contact Type],
	alpha.[BIRTH DATE] as [Date of Birth],
	replace(replace(alpha.[DESIGNATION],',','|'),' ','') as [Designation],
	case 
	when new.ETHNICITY is not null and new.ETHNICITY = 'African American' then 'African-American'
	when new.ETHNICITY is not null and new.ETHNICITY = 'Hispanic or Latino' then 'Hispanic'
	when new.ETHNICITY is not null and new.ETHNICITY = 'White or Caucasian' then 'Caucasian'
	when new.ETHNICITY is not null and new.ETHNICITY = 'Asian or Pacific IslANDer' then 'Asian'
	else coalesce(new.ETHNICITY,vw.[Ethnicity])	end as [Ethnicity],
	coalesce(new.GENDER,vw.[Gender]) as [Gender],
	alpha.[GRAD DATE] as [Graduation Date],
	coalesce(new.LICENSENO,vw.[License Number]) as [License Number],
	cast(case
	when alpha.[MEMBER TYPE] = 'AC' and alpha.[CATEGORY DESCRIPTION] = 'Disability Waiver' then 'Disability Waiver'
	when alpha.[MEMBER TYPE] = 'AC' and alpha.[CATEGORY DESCRIPTION] = 'Residency' then 'Resident'
	when alpha.[MEMBER TYPE] = 'AC' then 'Active General Dentist'
	when alpha.[MEMBER TYPE] = 'AF' then 'Affiliate'
	when alpha.[MEMBER TYPE] = 'AS' then 'Associate'
	when alpha.[MEMBER TYPE] = 'EM' then 'Emeritus'
	when alpha.[MEMBER TYPE] = 'HM' then 'Honorary Member'
	when alpha.[MEMBER TYPE] = 'RE' then 'Retired'
	when alpha.[MEMBER TYPE] = 'ST' then 'Student Member'
	else '' end as varchar(255)) as [MemberType],
	cast(case 
	when alpha.[DENTAL SCHOOL] = 'Texas A&M Health Science Center Baylor College of Dentistry' then null
	when alpha.[DENTAL SCHOOL] = 'University of Texas Dental Branch at Houston' then null
	when alpha.[DENTAL SCHOOL] = 'University of Texas Health Science Center at San Antonio Dental School' then null
	else alpha.[DENTAL SCHOOL] end as varchar(255)) as [Other Dental School],
	cast(case alpha.[DENTAL SCHOOL]
	when 'Texas A&M Health Science Center Baylor College of Dentistry' then 'Baylor College of Dentistry'
	when 'University of Texas Dental Branch at Houston' then 'UTHSCS Houston'
	when 'University of Texas Health Science Center at San Antonio Dental School' then 'UTHSC San Antonio'
	else null end as varchar(255)) as [Texas Dental School],
	coalesce(new.[PRACTICE ENVIRONMENT],vw.[Type of Practice]) as [Type of Practice]
into ##tagd_nationals_Members
from ##tagd_nationals_alpha as alpha
left outer join membercentral.dbo.ams_members as m
	inner join membercentral.dbo.vw_memberdata_TAGD as vw on vw.memberID = m.memberID
	on m.orgID = @orgID and m.membernumber = alpha.[ID] and m.status <> 'D' and m.memberid = m.activeMemberID
left outer join ##tagd_nationals_new as new on new.ID = alpha.ID

-- get active members in database now so we dont inactivate them
insert into ##tagd_nationals_Members (
	[prefix],[firstname],[middlename],[lastname],[suffix],[professionalSuffix],[memberNumber],[company],
	[Mailing Address_attn],[Mailing Address_address1],[Mailing Address_address2],
	[Mailing Address_city],[Mailing Address_stateprov],[Mailing Address_postalCode],[Mailing Address_county],[Mailing Address_country],[Mailing Address_Phone],
	[Mailing Address_Fax],[Mailing Address_Cell],[Mailing Address_2nd Phone],[Office Address_address1],[Office Address_address2],[Office Address_city],
	[Office Address_stateprov],[Office Address_postalCode],[Office Address_county],[Office Address_country],[Office Address_Phone],[Office Address_Fax],
	[Office Address_Cell],[Office Address_2nd Phone],[Other Address_address1],[Other Address_address2],[Other Address_city],[Other Address_stateprov],
	[Other Address_postalCode],[Other Address_county],[Other Address_country],[Other Address_Phone],[Other Address_Fax],[Other Address_Cell],
	[Other Address_2nd Phone],[Home Address_address1],[Home Address_address2],[Home Address_city],[Home Address_stateprov],[Home Address_postalCode],
	[Home Address_county],[Home Address_country],[Home Address_Phone],[Home Address_Fax],[Home Address_Cell],[Home Address_2nd Phone],[Business Website],
	[Email],[Personal Email],[Other Email],[AGD Join Date],[AGD Member Number],[AGD Paid Thru Date],[Badge Name],[Contact Type],[Date of Birth],[Designation],
	[Ethnicity],[Gender],[Graduation Date],[License Number],[MemberType],[Other Dental School],[Texas Dental School],[Type of Practice]
)
select m.prefix, m.firstname, m.middlename, m.lastname, m.suffix, m.professionalSuffix, m.membernumber, m.company, 
	vw.[Mailing Address_attn], vw.[Mailing Address_address1], vw.[Mailing Address_address2],
	vw.[Mailing Address_city],vw.[Mailing Address_stateprov],vw.[Mailing Address_postalCode],vw.[Mailing Address_county],vw.[Mailing Address_country],vw.[Mailing Address_Phone],
	vw.[Mailing Address_Fax],vw.[Mailing Address_Cell],vw.[Mailing Address_2nd Phone],vw.[Office Address_address1],vw.[Office Address_address2],vw.[Office Address_city],
	vw.[Office Address_stateprov],vw.[Office Address_postalCode],vw.[Office Address_county],vw.[Office Address_country],vw.[Office Address_Phone],vw.[Office Address_Fax],
	vw.[Office Address_Cell],vw.[Office Address_2nd Phone],vw.[Other Address_address1],vw.[Other Address_address2],vw.[Other Address_city],vw.[Other Address_stateprov],
	vw.[Other Address_postalCode],vw.[Other Address_county],vw.[Other Address_country],vw.[Other Address_Phone],vw.[Other Address_Fax],vw.[Other Address_Cell],
	vw.[Other Address_2nd Phone],vw.[Home Address_address1],vw.[Home Address_address2],vw.[Home Address_city],vw.[Home Address_stateprov],vw.[Home Address_postalCode],
	vw.[Home Address_county],vw.[Home Address_country],vw.[Home Address_Phone],vw.[Home Address_Fax],vw.[Home Address_Cell],vw.[Home Address_2nd Phone],vw.[Business Website],
	vw.[Email],vw.[Personal Email],vw.[Other Email],vw.[AGD Join Date],vw.[AGD Member Number],vw.[AGD Paid Thru Date],vw.[Badge Name],vw.[Contact Type],vw.[Date of Birth],vw.[Designation],
	vw.[Ethnicity],vw.[Gender],vw.[Graduation Date],vw.[License Number],vw.[MemberType],vw.[Other Dental School],vw.[Texas Dental School],vw.[Type of Practice]
from membercentral.dbo.ams_members as m
inner join membercentral.dbo.vw_memberdata_TAGD as vw on vw.memberID = m.memberID
where m.orgID = @orgID
and m.status = 'A'
and m.memberid = m.activeMemberID
and m.memberTypeID = 2
and not exists (select membernumber from ##tagd_nationals_Members where membernumber = m.membernumber)

-- add rowID
ALTER TABLE ##tagd_nationals_Members ADD rowIDtemp int IDENTITY(1,1), rowID int NULL;
update ##tagd_nationals_Members set rowID = rowIDtemp;
ALTER TABLE ##tagd_nationals_Members DROP COLUMN rowIDtemp;

-- move data to holding tables
EXEC memberCentral.dbo.ams_importMemberData_tempToHolding @orgid=@orgID, @tmptbl='##tagd_nationals_Members', @pathToExport=@pathToExport, @importResult=@importResult OUTPUT

-- if no errors, run member import
IF @importResult.value('count(/import/errors/error)','int') = 0
BEGIN
	SELECT @flatfile = @importResult.value('(/import/@flatfile)[1]','varchar(160)')
	EXEC membercentral.dbo.ams_importMemberData_holdingToPerm @orgID=@orgID, @flatfile=@flatfile

	DECLARE @smtpserver varchar(20)
	SELECT @smtpserver = smtpserver from membercentral.dbo.fn_getServerSettings()		

	BEGIN TRY
		EXEC membercentral.dbo.ams_importMemberDataHoldingResultReport @importResult=@importResult, @finalMSG=@finalMSG OUTPUT
		EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject='TAGD Membership Import Results',
			@message=@finalMSG, @priority='normal', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
		SELECT @emailtouse = emailImportResults from membercentral.dbo.organizations where orgID = @orgID	
		IF len(@emailtouse) > 0 BEGIN
			EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to=@emailtouse,
				@cc=null, @bcc='<EMAIL>', @subject='TAGD Membership Import Results',
				@message=@finalMSG, @priority='normal', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
		END
	END TRY
	BEGIN CATCH      
		DECLARE @ErrorMessage VARCHAR(max)      
		DECLARE @ErrorSeverity INT      
		DECLARE @ErrorState INT        
		SELECT @ErrorMessage = 'Error running TAGD Membership Import' + char(13) + char(10) + ERROR_MESSAGE(),   
			@ErrorSeverity = ERROR_SEVERITY(),   
			@ErrorState = ERROR_STATE()        
		EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject='Error Importing TAGD Data',
			@message=@ErrorMessage, @priority='high', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END CATCH


	/* ***************** */
	/* ACCOUNTING IMPORT */
	/* ***************** */
	-- prep acct table (ignore $0)
	IF OBJECT_ID('tempdb..##tagd_nationals_acct') IS NOT NULL
		DROP TABLE ##tagd_nationals_acct
	select 'TAGD Dues ' + cast(year([PAID THRU]) as varchar(4)) as SaleDescription,
		@batchDate as saleDate,
		[ID] as saleMemberID,
		[Constituent PAID] as saleAmount,
		'4010' as saleRevenueGL,
		rowID as saleID,
		rowID as rowID
	into ##tagd_nationals_acct
	from ##tagd_nationals_dues
	where [Constituent PAID] <> 0

	set @importResult = null
	EXEC membercentral.dbo.tr_importTransactions_toTemp @orgid=@orgID, @payProfileCode='AGDimport', @tmptbl='##tagd_nationals_acct', @pathToExport=@pathToExport, @importResult=@importResult OUTPUT

	-- if no errors, run accounting import
	IF @importResult.value('count(/import/errors/error)','int') = 0
	BEGIN
		SELECT @flatfile = @importResult.value('(/import/@flatfile)[1]','varchar(160)')
		set @importResult = null
		EXEC membercentral.dbo.tr_importTransactions_toPerm @orgID=@orgID, @siteID=@siteID, @recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, @flatfile=@flatfile, @importResult=@importResult OUTPUT


		IF @importResult.value('count(/import/errors/error)','int') = 0
		BEGIN
			/* ******************* */
			/* SUBSCRIPTION IMPORT */
			/* ******************* */
			-- Start prep work for importing subs
			IF OBJECT_ID('tempdb..#tagd_sub_renewals') IS NOT NULL
				DROP TABLE #tagd_sub_renewals
			IF OBJECT_ID('tempdb..#tagd_sub_newmembers') IS NOT NULL
				DROP TABLE #tagd_sub_newmembers
			IF OBJECT_ID('tempdb..#tagd_sub_newComponent') IS NOT NULL
				DROP TABLE #tagd_sub_newComponent
			IF OBJECT_ID('tempdb..#tagd_sub_switchedComponent') IS NOT NULL
				DROP TABLE #tagd_sub_switchedComponent
			IF OBJECT_ID('tempdb..#tagd_memberType') IS NOT NULL
				DROP TABLE #tagd_memberType
			IF OBJECT_ID('tempdb..##tagd_import_subscriptions') IS NOT NULL
				DROP TABLE ##tagd_import_subscriptions
			IF OBJECT_ID('tempdb..#tagd_sub_expireComponent') IS NOT NULL
				DROP TABLE #tagd_sub_expireComponent

			declare @subscriberIDList varchar(8000), @membershipSubscriptionType varchar(100), 
				@componentSubscriptionType varchar(100), @membershipSubscriptionName varchar(100),
				@thismemberid int, @thissubscriptionID int, @thisparentSubscriberID int, 
				@thisRFID int, @thisGLAccountID int, @thisstatus varchar(1), @thissubStartDate datetime,
				@thissubEndDate datetime, @thisgraceEndDate datetime, @thispcfree bit, 
				@thisactivationOptionCode char(1), @thisrecordedByMemberID int,  @thisbypassQueue bit,
				@thissubscriberID int, @thisexistingComponentSubscriberID int, @trashID int,
				@autoID int, @currentTimeStamp datetime
			set @currentTimeStamp = getdate()
			set @membershipSubscriptionType = 'Membership Dues'
			set @componentSubscriptionType = 'Components'
			set @membershipSubscriptionName = 'TAGD Member'

			CREATE TABLE #tagd_sub_renewals (memberID int, memberNumber varchar(100), subStartDate datetime,
				subEndDate datetime, rootSubscriptionID int, componentSubscriptionID int, treecode uniqueIdentifier)
			CREATE TABLE #tagd_sub_newmembers (memberID int, memberNumber varchar(100), subStartDate datetime,
				subEndDate datetime, rootSubscriptionID int, componentSubscriptionID int, treecode uniqueIdentifier)
			CREATE TABLE #tagd_sub_newComponent (autoID int IDENTITY(1,1) PRIMARY KEY, memberID int,
				memberNumber varchar(100), existingRootSubscriberID int, existingComponentSubscriberID int,
				subStartDate datetime, subEndDate datetime, graceEndDate datetime, rootSubscriptionID int,
				componentSubscriptionID int, rfid int, GLAccountID int, status char(1), pcfree bit, 
				activationOptionCode varchar(1), existingComponentSubscriptionID int)
			CREATE TABLE #tagd_sub_switchedComponent (autoID int IDENTITY(1,1) PRIMARY KEY, memberID int,
				memberNumber varchar(100), existingRootSubscriberID int, existingComponentSubscriberID int,
				subStartDate datetime, subEndDate datetime, graceEndDate datetime, rootSubscriptionID int,
				componentSubscriptionID int, rfid int, GLAccountID int, status char(1), pcfree bit, 
				activationOptionCode varchar(1), existingComponentSubscriptionID int)
			CREATE TABLE #tagd_memberType (tempMemberTypeId int NOT NULL identity(1,1),
				ValueForMemberTypeCustomField varchar(255) NOT NULL DEFAULT (''), 
				MemberTypeValueFromAGDAlphaRoster varchar(10) NOT NULL DEFAULT (''), 
				CategoryDescriptionValueFromAGDAlphaRoster varchar(255) NULL DEFAULT (''), 
				MembershipDuesTAGDMemberSubRate varchar(255) NOT NULL DEFAULT ('')
			)
			CREATE TABLE #tagd_sub_expireComponent (
				autoID int IDENTITY(1,1) 
				PRIMARY KEY, memberID int,
				memberNumber varchar(100), 
				existingRootSubscriberID int, 
				existingComponentSubscriberID int,
				existingComponentSubscriptionID int,
				rootSubscriptionID int,
				rfid int, GLAccountID int, status char(1), pcfree bit, 
				activationOptionCode varchar(1)
			)

			insert into #tagd_sub_renewals (memberID, memberNumber, subStartDate, subEndDate, rootSubscriptionID, 
				componentSubscriptionID, treecode)
			select activeMember.memberID, na.id, 
				newStartDate = case 
					when (isnull(na.[JOIN DATE],'1/1/1900') > cast (cast(YEAR(na.[PAID THRU]) as nvarchar)+'0101' as datetime)) and (na.[JOIN DATE] > DATEADD(dd, DATEDIFF(dd, 0,dateadd(d,1,max(ss.subenddate))), 0))
						then isnull(na.[JOIN DATE],'1/1/1900')
					when (max(ss.subenddate) > cast (cast(YEAR(na.[PAID THRU]) as nvarchar)+'0101' as datetime)) and (DATEADD(dd, DATEDIFF(dd, 0,dateadd(d,1,max(ss.subenddate))), 0) > isnull(na.[JOIN DATE],'1/1/1900'))
						then DATEADD(dd, DATEDIFF(dd, 0,dateadd(d,1,max(ss.subenddate))), 0)
					else
						cast (cast(YEAR(na.[PAID THRU]) as nvarchar)+'0101' as datetime) 
					end,
				newSubEndDate = dateadd(ms,-3,dateadd(day,1,[Paid Thru])),
				subs.subscriptionID as rootSubscriptionID, 
				csubs.subscriptionID as componentSubscripionID,
				treecode = newid()
			from membercentral.dbo.sub_types t
			inner join membercentral.dbo.sub_subscriptions subs on subs.typeID = t.typeID
				and t.typeName = @membershipSubscriptionType
				and subs.subscriptionName = @membershipSubscriptionName
				and t.siteID = @siteID
			inner join membercentral.dbo.sub_subscribers ss on ss.subscriptionID = subs.subscriptionID
				and ss.parentSubscriberID is null
			inner join membercentral.dbo.sub_statuses st on st.statusID = ss.statusID
				and st.statuscode in ('A','I','P')
			inner join membercentral.dbo.ams_members m on m.memberID = ss.memberID
			inner join membercentral.dbo.ams_members activeMember on activeMember.memberID = m.activeMemberID
				and activeMember.status in ('A','I')
			--make sure we have subscriberID with the latest end date
			left outer join membercentral.dbo.sub_subscribers otherterms_ss
				inner join membercentral.dbo.sub_statuses otherterms_st on otherterms_st.statusID = otherterms_ss.statusID
					and otherterms_st.statuscode in ('A','I','P')
				inner join membercentral.dbo.ams_members otherterms_m on otherterms_m.memberID = otherterms_ss.memberID
				on ss.subscriptionID = otherterms_ss.subscriptionID
					and ss.subenddate < otherterms_ss.subenddate
					and otherterms_m.activeMemberID = activeMember.memberID
			inner join ##tagd_nationals_alpha na on na.id = activeMember.membernumber
				and na.[Paid Thru] is not null
				and ss.subenddate < na.[paid thru]
				and na.[paid thru] > @currentTimeStamp
			inner join membercentral.dbo.sub_types ct on ct.siteID = t.siteID
				and ct.typeName = @componentSubscriptionType
			left outer join membercentral.dbo.sub_subscriptions csubs on csubs.typeID = ct.typeID
				and csubs.subscriptionName = na.[COMPONENT TITLE]
			where otherterms_ss.subscriberID is null
			group by activeMember.memberID,na.id,  na.[paid thru], na.[JOIN DATE], subs.subscriptionID, csubs.subscriptionID
			order by na.[JOIN DATE] desc

			insert into #tagd_sub_newmembers (memberID, memberNumber,subStartDate,subEndDate, rootSubscriptionID, componentSubscriptionID,treecode)
			select 
				activeMember.memberID,
				na.id,
				newStartDate = case 
					when (isnull(na.[JOIN DATE],'1/1/1900') > cast (cast(YEAR(na.[PAID THRU]) as nvarchar)+'0101' as datetime))
						then isnull(na.[JOIN DATE],'1/1/1900')
					else
						cast (cast(YEAR(na.[PAID THRU]) as nvarchar)+'0101' as datetime) 
					end,
				newSubEndDate = dateadd(ms,-3,dateadd(day,1,[Paid Thru])),
				subs.subscriptionID as rootSubscriptionID, 
				csubs.subscriptionID as componentSubscripionID,
				treecode = newid()
			from 
				##tagd_nationals_alpha na
				inner join membercentral.dbo.ams_members activeMember
					on activeMember.memberNumber = na.id
					and activeMember.orgID = @orgID
					and activeMember.status in ('A','I')
					and activeMember.memberID = activeMember.activeMemberID
					and na.[paid thru] > @currentTimeStamp
				inner join membercentral.dbo.ams_members m
					on activeMember.memberID = m.activeMemberID
				inner join membercentral.dbo.sub_types t
					on t.typeName = @membershipSubscriptionType
					and t.siteID = @siteID
				inner join membercentral.dbo.sub_subscriptions subs
					on subs.typeID = t.typeID
					and subs.subscriptionName = @membershipSubscriptionName
				left outer join membercentral.dbo.sub_subscribers ss
					inner join membercentral.dbo.sub_statuses st
						on st.statusID = ss.statusID
						and st.statuscode in ('A','I','P')
						and ss.parentSubscriberID is null
				on m.memberID = ss.memberID
					and ss.subscriptionID = subs.subscriptionID
				inner join membercentral.dbo.sub_types ct
					on ct.siteID = @siteID
					and ct.typeName = @componentSubscriptionType
				left outer join membercentral.dbo.sub_subscriptions csubs
					on csubs.typeID = ct.typeID
					and csubs.subscriptionName = na.[COMPONENT TITLE]
			group by 
				activeMember.memberID, na.id,  na.[paid thru], na.[JOIN DATE], subs.subscriptionID, csubs.subscriptionID
			having 
				max(isnull(ss.subscriberID,0)) = 0
			order by 
				na.[JOIN DATE] desc

			insert into #tagd_sub_newComponent (
				memberID, 
				memberNumber,
				subStartDate,
				subEndDate, 
				graceEndDate, 
				rootSubscriptionID, 
				componentSubscriptionID, 
				existingRootSubscriberID, 
				existingComponentSubscriberID,
				existingComponentSubscriptionID
			)
			select 
				activeMember.memberID,
				na.id,
				-- case statement to handle changing future Accepted subscriptions
				newStartDate = case 
					when (@currentTimeStamp > ss.substartDate)
						then @currentTimeStamp
					else
						ss.substartDate
					end,
				newSubEndDate = case 
					when (@currentTimeStamp > ss.subEndDate)
						then cast (cast(YEAR(@currentTimeStamp) as nvarchar)+'1231' as datetime)
					else
						ss.subEndDate
					end,
				graceEndDate = cast (cast(YEAR(ss.subEndDate) + 1 as nvarchar)+'0331' as datetime),
				subs.subscriptionID as rootSubscriptionID, 
				correct_csubs.subscriptionID as componentSubscripionID,
				ss.rootSubscriberID as existingRootSubscriberID, 
				current_css.subscriberID as existingComponentSubscriberID,
				current_css.subscriptionID as existingComponentSubscriptionID
			from 
				membercentral.dbo.sub_types t
				inner join membercentral.dbo.sub_subscriptions subs
					on subs.typeID = t.typeID
					and t.typeName = @membershipSubscriptionType
					and subs.subscriptionName = @membershipSubscriptionName
					and t.siteID = @siteID
				inner join membercentral.dbo.sub_subscribers ss
					on ss.subscriptionID = subs.subscriptionID
					and ss.parentSubscriberID is null
				inner join membercentral.dbo.sub_statuses st
					on st.statusID = ss.statusID
					and st.statuscode in ('A','I','P')
				inner join membercentral.dbo.ams_members m
					on m.memberID = ss.memberID
				inner join membercentral.dbo.ams_members activeMember
					on activeMember.memberID = m.activeMemberID
					and activeMember.status in ('A','I')
				inner join ##tagd_nationals_alpha na
					on na.id = activeMember.membernumber
					and na.[Paid Thru] is not null
				inner join membercentral.dbo.sub_types ct
					on ct.siteID = t.siteID
					and ct.typeName = @componentSubscriptionType
				inner join membercentral.dbo.sub_subscriptions correct_csubs
					on correct_csubs.typeID = ct.typeID
					and correct_csubs.subscriptionName = na.[COMPONENT TITLE]
				left outer join membercentral.dbo.sub_subscribers current_css
					inner join membercentral.dbo.sub_statuses current_cst
						on current_cst.statusID = current_css.statusID
						and current_cst.statuscode in ('A','I','P')
					on current_css.parentSubscriberID = ss.subscriberID
				and correct_csubs.subscriptionID = current_css.subscriptionID
			where
				current_css.subscriptionID is null
			group by 
				current_cst.statuscode, activeMember.memberID,na.id,  na.[paid thru], na.[JOIN DATE], subs.subscriptionID, 
				correct_csubs.subscriptionID, ss.rootSubscriberID, current_css.subscriberID,
				current_css.subscriptionID, ss.substartDate, ss.subEndDate
			order by na.[JOIN DATE] desc

			insert into #tagd_sub_switchedComponent (
				memberID, 
				memberNumber,
				subStartDate,
				subEndDate, 
				graceEndDate, 
				rootSubscriptionID, 
				componentSubscriptionID, 
				existingRootSubscriberID, 
				existingComponentSubscriberID,
				existingComponentSubscriptionID
			)
			select 
				activeMember.memberID,
				na.id,
				-- case statement to handle changing future Accepted subscriptions
				newStartDate = case 
					when (@currentTimeStamp > ss.substartDate)
						then @currentTimeStamp
					else
						ss.substartDate
					end,
				newSubEndDate = case 
					when (@currentTimeStamp > ss.subEndDate)
						then cast (cast(YEAR(@currentTimeStamp) as nvarchar)+'1231' as datetime)
					else
						ss.subEndDate
					end,
				graceEndDate = cast (cast(YEAR(ss.subEndDate) + 1 as nvarchar)+'0331' as datetime),
				subs.subscriptionID as rootSubscriptionID, 
				correct_csubs.subscriptionID as componentSubscripionID,
				ss.rootSubscriberID as existingRootSubscriberID, 
				current_css.subscriberID as existingComponentSubscriberID,
				current_css.subscriptionID as existingComponentSubscriptionID
			from 
				membercentral.dbo.sub_types t
				inner join membercentral.dbo.sub_subscriptions subs
					on subs.typeID = t.typeID
					and t.typeName = @membershipSubscriptionType
					and subs.subscriptionName = @membershipSubscriptionName
					and t.siteID = @siteID
				inner join membercentral.dbo.sub_subscribers ss
					on ss.subscriptionID = subs.subscriptionID
					and ss.parentSubscriberID is null
				inner join membercentral.dbo.sub_statuses st
					on st.statusID = ss.statusID
					and st.statuscode in ('A','I','P')
				inner join membercentral.dbo.ams_members m
					on m.memberID = ss.memberID
				inner join membercentral.dbo.ams_members activeMember
					on activeMember.memberID = m.activeMemberID
					and activeMember.status in ('A','I')
				inner join ##tagd_nationals_alpha na
					on na.id = activeMember.membernumber
					and na.[Paid Thru] is not null
				inner join membercentral.dbo.sub_types ct
					on ct.siteID = t.siteID
					and ct.typeName = @componentSubscriptionType
				inner join membercentral.dbo.sub_subscriptions correct_csubs
					on correct_csubs.typeID = ct.typeID
					and correct_csubs.subscriptionName = na.[COMPONENT TITLE]
				inner join membercentral.dbo.sub_subscribers current_css
					on current_css.parentSubscriberID = ss.subscriberID
					and correct_csubs.subscriptionID <> current_css.subscriptionID
				inner join membercentral.dbo.sub_statuses current_cst
					on current_cst.statusID = current_css.statusID
					and current_cst.statuscode in ('A','I','P')
			group by 
				activeMember.memberID,na.id,  na.[paid thru], na.[JOIN DATE], subs.subscriptionID, 
				correct_csubs.subscriptionID, ss.rootSubscriberID, current_css.subscriberID,
				current_css.subscriptionID, ss.substartDate, ss.subEndDate
			order by na.[JOIN DATE] desc

			insert into #tagd_sub_expireComponent (
				memberID, 
				memberNumber,
				rootSubscriptionID, 
				existingComponentSubscriptionID, 
				existingRootSubscriberID, 
				existingComponentSubscriberID
			)			
			select 
				activeMember.memberID,
				na.id,
				subs.subscriptionID as rootSubscriptionID, 
				current_css.subscriptionID as existingComponentSubscriptionID,
				ss.rootSubscriberID as existingRootSubscriberID, 
				current_css.subscriberID as existingComponentSubscriberID
			from 
				membercentral.dbo.sub_types t
				inner join membercentral.dbo.sub_subscriptions subs
					on subs.typeID = t.typeID
					and t.typeName = @membershipSubscriptionType
					and subs.subscriptionName = @membershipSubscriptionName
					and t.siteID = @siteID
				inner join membercentral.dbo.sub_subscribers ss
					on ss.subscriptionID = subs.subscriptionID
					and ss.parentSubscriberID is null
				inner join membercentral.dbo.sub_statuses st
					on st.statusID = ss.statusID
					and st.statuscode in ('A','I','P')
				inner join membercentral.dbo.ams_members m
					on m.memberID = ss.memberID
				inner join membercentral.dbo.ams_members activeMember
					on activeMember.memberID = m.activeMemberID
					and activeMember.status in ('A','I')
				inner join ##tagd_nationals_alpha na
					on na.id = activeMember.membernumber
					and na.[Paid Thru] is not null
				inner join membercentral.dbo.sub_types ct
					on ct.siteID = t.siteID
					and ct.typeName = @componentSubscriptionType
				inner join membercentral.dbo.sub_subscriptions correct_csubs
					on correct_csubs.typeID = ct.typeID
					and correct_csubs.subscriptionName <> na.[COMPONENT TITLE]
				inner join membercentral.dbo.sub_subscribers current_css
					on current_css.parentSubscriberID = ss.subscriberID
					and correct_csubs.subscriptionID = current_css.subscriptionID
				inner join membercentral.dbo.sub_statuses current_cst
					on current_cst.statusID = current_css.statusID
					and current_cst.statuscode in ('A','I','P')
				left outer join #tagd_sub_switchedComponent sc on
					current_css.subscriberID = sc.existingComponentSubscriberID
			where
				sc.existingComponentSubscriberID is null
			group by 
				activeMember.memberID,na.id, subs.subscriptionID, 
				current_css.subscriptionID, ss.rootSubscriberID, current_css.subscriberID,
				 ss.substartDate, ss.subEndDate
			union
				select 
					renewals.memberID,
					renewals.memberNumber,
					renewals.rootSubscriptionID, 
					sc.existingComponentSubscriptionID,
					sc.existingRootSubscriberID, 
					sc.existingComponentSubscriberID
				from 
					#tagd_sub_renewals  renewals
					inner join #tagd_sub_switchedComponent sc on 
						sc.rootSubscriptionID = renewals.rootSubscriptionID
						and sc.memberNumber = renewals.memberNumber
			order by 
				activeMember.memberID

			update sc set
				GLAccountID = case
					when subs.allowRateGLAccountOverride = 1 then isnull(r.GLAccountID,subs.GLAccountID) 
					else subs.GLAccountID
				end, 
				rfid=rf.rfID,
				pcfree=0, 
				activationOptionCode = sao.subActivationCode,
				Status = case 
					when subStartDate > @currentTimeStamp then 'P'
					else 'A'
				end
			from 
				#tagd_sub_switchedComponent sc
				inner join membercentral.dbo.sub_subscriptions subs
					on subs.subscriptionID = sc.componentSubscriptionID
				inner join membercentral.dbo.sub_rateSchedules as rs 
					on subs.scheduleID = rs.scheduleID 
					and rs.status = 'A'
				inner join membercentral.dbo.sub_rates r
					on r.scheduleID = rs.scheduleID
					and r.status = 'A'
					and r.isRenewalRate = 0 
					and r.rateName = 'Annual Term'
				inner join membercentral.dbo.sub_rateFrequencies rf on
					rf.rateID = r.rateID
					and rf.status = 'A'
				inner join membercentral.dbo.sub_frequencies f on
					f.frequencyID = rf.frequencyID
					and f.status = 'A'
				inner join membercentral.dbo.sub_rateFrequenciesMerchantProfiles as rfmp on 
					rfmp.rfid = rf.rfID 
					and rfmp.status = 'A' 
				inner join membercentral.dbo.mp_profiles as mp on 
					mp.profileID = rfmp.profileID 
					and mp.status = 'A' 
				inner join membercentral.dbo.sub_activationOptions sao
					on sao.subActivationID = subs.subAlternateActivationID

			update nc set
				GLAccountID = case
					when subs.allowRateGLAccountOverride = 1 then isnull(r.GLAccountID,subs.GLAccountID) 
					else subs.GLAccountID
				end, 
				rfid=rf.rfID,
				pcfree=0, 
				activationOptionCode = sao.subActivationCode,
				Status = case 
					when subStartDate > @currentTimeStamp then 'P'
					else 'A'
				end
			from 
				#tagd_sub_newComponent nc
				inner join membercentral.dbo.sub_subscriptions subs
					on subs.subscriptionID = nc.componentSubscriptionID
				inner join membercentral.dbo.sub_rateSchedules as rs 
					on subs.scheduleID = rs.scheduleID 
					and rs.status = 'A'
				inner join membercentral.dbo.sub_rates r
					on r.scheduleID = rs.scheduleID
					and r.status = 'A'
					and r.isRenewalRate = 0 
					and r.rateName = 'Annual Term'
				inner join membercentral.dbo.sub_rateFrequencies rf on
					rf.rateID = r.rateID
					and rf.status = 'A'
				inner join membercentral.dbo.sub_frequencies f on
					f.frequencyID = rf.frequencyID
					and f.status = 'A'
				inner join membercentral.dbo.sub_rateFrequenciesMerchantProfiles as rfmp on 
					rfmp.rfid = rf.rfID 
					and rfmp.status = 'A' 
				inner join membercentral.dbo.mp_profiles as mp on 
					mp.profileID = rfmp.profileID 
					and mp.status = 'A' 
				inner join membercentral.dbo.sub_activationOptions sao
					on sao.subActivationID = subs.subAlternateActivationID

			insert into #tagd_memberType (
				ValueForMemberTypeCustomField,
				MemberTypeValueFromAGDAlphaRoster,
				CategoryDescriptionValueFromAGDAlphaRoster,
				MembershipDuesTAGDMemberSubRate
			)
			select 'Active Member', 'AC', '',	'Active General Dentist'
			union
			select 'Affiliate', 'AF', '',	'Affiliate'
			union
			select 'Associate', 'AS', '',	'Associate'
			union
			select 'Disability Waiver', 'AC', 'Disability Waiver',	'Dues Waiver'
			union
			select 'Emeritus Member', 'EM', '',	'Emeritus'
			union
			select 'Honorary Member', 'HM', '',	'Honorary Member'
			union
			select 'Retired', 'RE', '',	'Retired'
			union
			select 'Resident', 'AC', 'Residency',	'Resident'
			union
			select 'Student Member', 'ST', '',	'Student'
			union
			select 'Financial Waiver', 'AC', 'Financial Waiver','Dues Waiver'

			IF OBJECT_ID('tempdb..#temp_tagd_nationals_alpha') IS NOT NULL
				DROP TABLE #temp_tagd_nationals_alpha

			-- Inner join alpha with memberType so we can get the right rate
			select * 
			into #temp_tagd_nationals_alpha
			from ##tagd_nationals_alpha na
			left outer join #tagd_memberType mt on ltrim(rtrim(mt.MemberTypeValueFromAGDAlphaRoster)) = ltrim(rtrim(na.[member type]))
				and ltrim(rtrim(mt.CategoryDescriptionValueFromAGDAlphaRoster )) = ltrim(rtrim(na.[category description]))
			order by na.[category description] desc

			-- Get the query to be passed to import subscriptions stored proc
			select *  
			into ##tagd_import_subscriptions 
			from (
				select 
					distinct renewals.memberNumber
					,@membershipSubscriptionType as SubscriptionType
					, subs.SubscriptionName
					,r.RateName
					, rf.rateAmt as LastPrice
					, f.frequencyShortName as Frequency
					, 'NO' as StoreModifiedRate
					, renewals.subStartDate as StartDate
					, renewals.subEndDate as EndDate
					, cast (cast(YEAR(renewals.subEndDate) + 1 as nvarchar)+'0331' as datetime) as graceEndDate
					, Status = case 
						when renewals.subStartDate > @currentTimeStamp then 'P'
						else 'A'
					end
					, NULL as ParentSubscriptionType
					, NULL  as ParentSubscriptionName
					, renewals.treecode as TreeCode
				from (
					select memberID, memberNumber,subStartDate,subEndDate, rootSubscriptionID, componentSubscriptionID, treecode from #tagd_sub_renewals
					union
					select memberID, memberNumber,subStartDate,subEndDate, rootSubscriptionID, componentSubscriptionID, treecode from #tagd_sub_newmembers 
				) as renewals
				inner join membercentral.dbo.sub_subscriptions as subs
					on renewals.rootSubscriptionID = subs.subscriptionID
					and subs.status = 'A' 
				inner join membercentral.dbo.sub_rateSchedules as rs 
					on subs.scheduleID = rs.scheduleID 
					and rs.status = 'A'
				inner join membercentral.dbo.sub_rates r
					on r.scheduleID = rs.scheduleID
					and r.status = 'A'
					and r.isRenewalRate = 0 
				inner join #tagd_memberType mt on 
					ltrim(rtrim(mt.MembershipDuesTAGDMemberSubRate)) = ltrim(rtrim(r.rateName))
				inner join #temp_tagd_nationals_alpha tmt on
					tmt.tempMemberTypeId = mt.tempMemberTypeId	
					and tmt.id = renewals.memberNumber	
				inner join membercentral.dbo.sub_rateFrequencies rf on
					rf.rateID = r.rateID
					and rf.status = 'A'
				inner join membercentral.dbo.sub_frequencies f on
					f.frequencyID = rf.frequencyID
					and f.status = 'A'
				inner join membercentral.dbo.sub_rateFrequenciesMerchantProfiles as rfmp on 
					rfmp.rfid = rf.rfID 
					and rfmp.status = 'A' 
				inner join membercentral.dbo.mp_profiles as mp on 
					mp.profileID = rfmp.profileID 
					and mp.status = 'A' 
				union
				select 
					distinct renewals.memberNumber
					,@componentSubscriptionType as SubscriptionType
					, subs.SubscriptionName
					,r.RateName
					, rf.rateAmt as LastPrice
					, f.frequencyShortName as Frequency
					, 'NO' as StoreModifiedRate
					, renewals.subStartDate as StartDate
					, renewals.subEndDate as EndDate
					, cast (cast(YEAR(renewals.subEndDate) + 1 as nvarchar)+'0331' as datetime) as graceEndDate
					, Status = case 
						when renewals.subStartDate > @currentTimeStamp then 'P'
						else 'A'
					end
					, @membershipSubscriptionType as ParentSubscriptionType
					, @membershipSubscriptionName  as ParentSubscriptionName
					, renewals.treecode as TreeCode
				from (
					select memberID, memberNumber,subStartDate,subEndDate, rootSubscriptionID, componentSubscriptionID, treecode from #tagd_sub_renewals
					union
					select memberID, memberNumber,subStartDate,subEndDate, rootSubscriptionID, componentSubscriptionID, treecode from #tagd_sub_newmembers 
				) as renewals
				inner join membercentral.dbo.sub_subscriptions as subs
					on renewals.componentSubscriptionID = subs.subscriptionID
					and subs.status = 'A' 
				inner join membercentral.dbo.sub_rateSchedules as rs 
					on subs.scheduleID = rs.scheduleID 
					and rs.status = 'A'
				inner join membercentral.dbo.sub_rates r
					on r.scheduleID = rs.scheduleID
					and r.status = 'A'
					and r.isRenewalRate = 0 
					and r.rateName = 'Annual Term'
				inner join membercentral.dbo.sub_rateFrequencies rf on
					rf.rateID = r.rateID
					and rf.status = 'A'
				inner join membercentral.dbo.sub_frequencies f on
					f.frequencyID = rf.frequencyID
					and f.status = 'A'
				inner join membercentral.dbo.sub_rateFrequenciesMerchantProfiles as rfmp on 
					rfmp.rfid = rf.rfID 
					and rfmp.status = 'A' 
				inner join membercentral.dbo.mp_profiles as mp on 
					mp.profileID = rfmp.profileID 
					and mp.status = 'A' 
			) tmp
			order by tmp.MemberNumber, tmp.ParentSubscriptionType

			DECLARE @qry varchar(400)
			SELECT @qry = 'ALTER TABLE ##tagd_import_subscriptions ADD rowID int NULL, rowID2 int identity(1,1);'
			EXEC(@qry)
			SELECT @qry = 'update ##tagd_import_subscriptions set rowID = rowID2;'
			EXEC(@qry)
			SELECT @qry = 'ALTER TABLE ##tagd_import_subscriptions DROP COLUMN rowID2;'
			EXEC(@qry)
			SELECT @qry = 'ALTER TABLE ##tagd_import_subscriptions ALTER COLUMN rowID int NOT NULL;'
			EXEC(@qry)

			-- Change components 
			select 
				@autoID = min(sc.autoID) 
			from 
				#tagd_sub_switchedComponent sc
				left outer join #tagd_sub_renewals sr on
					sr.componentSubscriptionID = sc.componentSubscriptionID
					and sc.memberNumber = sr.memberNumber
			where
				sr.componentSubscriptionID is null 

			while @autoID is not null
			begin
				select 
					@thismemberid=NULL, 
					@thissubscriptionID=NULL, 
					@thisparentSubscriberID=NULL, 
					@thisRFID=NULL, 
					@thisGLAccountID=NULL, 
					@thisstatus=NULL, 
					@thissubStartDate=NULL, 
					@thissubEndDate=NULL, 
					@thisgraceEndDate = NULL, 
					@thispcfree=NULL, 
					@thisactivationOptionCode=NULL, 
					@thisbypassQueue=NULL, 
					@thisexistingComponentSubscriberID = NULL

				select 
					@thismemberid=memberID, 
					@thissubscriptionID=componentSubscriptionID, 
					@thisparentSubscriberID=existingRootSubscriberID, 
					@thisRFID=rfID, 
					@thisGLAccountID=GLAccountID, 
					@thisstatus=status, 
					@thissubStartDate=subStartDate, 
					@thissubEndDate=subEndDate, 
					@thisgraceEndDate = graceEndDate , 
					@thispcfree=pcfree, 
					@thisactivationOptionCode=activationOptionCode, 
					@thisbypassQueue=0, 
					@thisexistingComponentSubscriberID = existingComponentSubscriberID
				from #tagd_sub_switchedComponent
				where autoID = @autoID


				exec membercentral.dbo.sub_expireSubscriber
					@subscriberID=@thisexistingComponentSubscriberID, 
					@memberID=@thismemberid, 
					@siteID=@siteID, 
					@enteredByMemberID=@recordedByMemberID, 
					@statsSessionID=0, 
					@AROption='C', 
					@fReturnQuery=0

				exec membercentral.dbo.sub_addSubscriber
					@orgID=@orgID, 
					@memberid=@thismemberid, 
					@subscriptionID=@thissubscriptionID, 
					@parentSubscriberID=@thisparentSubscriberID, 
					@RFID=@thisRFID, 
					@GLAccountID=@thisGLAccountID, 
					@status=@thisstatus, 
					@subStartDate=@thissubStartDate, 
					@subEndDate=@thissubEndDate, 
					@graceEndDate=@thisgraceEndDate, 
					@pcfree=@thispcfree, 
					@activationOptionCode=@thisactivationOptionCode,
					@recordedByMemberID=@recordedByMemberID, 
					@bypassQueue=@thisbypassQueue, 
					@subscriberID=@trashID OUTPUT

				select 
					@autoID = min(sc.autoID) 
				from 
					#tagd_sub_switchedComponent sc
					left outer join #tagd_sub_renewals sr on
						sr.componentSubscriptionID = sc.componentSubscriptionID
						and sc.memberNumber = sr.memberNumber
				where
					sr.componentSubscriptionID is null 
					and sc.autoID > @autoID
			end

			-- Add individual components 
			set @autoID = null
			select 
				@autoID = min(nc.autoID) 
			from 
				#tagd_sub_newComponent nc
				left outer join #tagd_sub_switchedComponent sc on
					nc.componentSubscriptionID = sc.componentSubscriptionID
					and nc.memberNumber = sc.memberNumber
			where
				sc.componentSubscriptionID is null 

			while @autoID is not null
			begin

				select 
					@thismemberid=NULL, 
					@thissubscriptionID=NULL, 
					@thisparentSubscriberID=NULL, 
					@thisRFID=NULL, 
					@thisGLAccountID=NULL, 
					@thisstatus=NULL, 
					@thissubStartDate=NULL, 
					@thissubEndDate=NULL, 
					@thisgraceEndDate = NULL, 
					@thispcfree=NULL, 
					@thisactivationOptionCode=NULL, 
					@thisbypassQueue=NULL, 
					@thisexistingComponentSubscriberID = NULL

				select 
					@thismemberid=memberID, 
					@thissubscriptionID=componentSubscriptionID, 
					@thisparentSubscriberID=existingRootSubscriberID, 
					@thisRFID=rfID, 
					@thisGLAccountID=GLAccountID, 
					@thisstatus=status, 
					@thissubStartDate=subStartDate, 
					@thissubEndDate=subEndDate, 
					@thisgraceEndDate = graceEndDate , 
					@thispcfree=pcfree, 
					@thisactivationOptionCode=activationOptionCode, 
					@thisbypassQueue=0, 
					@thisexistingComponentSubscriberID = existingComponentSubscriberID
				from #tagd_sub_newComponent
				where autoID = @autoID

				exec membercentral.dbo.sub_addSubscriber
					@orgID=@orgID, 
					@memberid=@thismemberid, 
					@subscriptionID=@thissubscriptionID, 
					@parentSubscriberID=@thisparentSubscriberID, 
					@RFID=@thisRFID, 
					@GLAccountID=@thisGLAccountID, 
					@status=@thisstatus, 
					@subStartDate=@thissubStartDate, 
					@subEndDate=@thissubEndDate, 
					@graceEndDate=@thisgraceEndDate, 
					@pcfree=@thispcfree, 
					@activationOptionCode=@thisactivationOptionCode,
					@recordedByMemberID=@recordedByMemberID, 
					@bypassQueue=@thisbypassQueue, 
					@subscriberID=@trashID OUTPUT

				select 
					@autoID = min(nc.autoID) 
				from 
					#tagd_sub_newComponent nc
					left outer join #tagd_sub_switchedComponent sc on
						nc.componentSubscriptionID = sc.componentSubscriptionID		
						and nc.memberNumber = sc.memberNumber		
				where 
					nc.autoID > @autoID
					and sc.componentSubscriptionID is null
			end

			-- Exprire components 
			select @autoID = min(autoID) from #tagd_sub_expireComponent
			while @autoID is not null
			begin
				select 
					@thismemberid=memberID, 
					@thisparentSubscriberID=existingRootSubscriberID, 
					@thisbypassQueue=0, 
					@thisexistingComponentSubscriberID = existingComponentSubscriberID
				from #tagd_sub_expireComponent
				where autoID = @autoID

				exec membercentral.dbo.sub_expireSubscriber
					@subscriberID=@thisexistingComponentSubscriberID, 
					@memberID=@thismemberid, 
					@siteID=@siteID, 
					@enteredByMemberID=@recordedByMemberID, 
					@statsSessionID=0, 
					@AROption='C', 
					@fReturnQuery=0

				select @autoID = min(autoID) from #tagd_sub_expireComponent where autoID > @autoID
			end

			-- E-mail component results
			declare @subImportResult xml

			select @subImportResult = (
				select getdate() as "@date",
					isnull((select top 301  id as "@membernumber", [FULL NAME] as "@fullname"
					from ##tagd_nationals_alpha
					where len([COMPONENT TITLE]) = 0
					order by id
					FOR XML path('member'), root('nocomponent'), type),'<nocomponent/>'),
					isnull((select top 301  id as "@membernumber", [FULL NAME] as "@fullname"
					from ##tagd_nationals_alpha na
						inner join #tagd_sub_renewals sr on
							na.id = sr.memberNumber
							and len(na.[COMPONENT TITLE]) > 0
							and sr.componentSubscriptionID is null
					order by id
					FOR XML path('member'), root('invalidcomponent'), type),'<invalidcomponent/>'),
					isnull((select top 301  id as "@membernumber", [FULL NAME] as "@fullname"
					from ##tagd_nationals_alpha na
						inner join #tagd_sub_expireComponent ec on
							na.id = ec.memberNumber
					order by id
					FOR XML path('member'), root('expiredcomponent'), type),'<expiredcomponent/>')
				for xml path('subimport'), TYPE)

			declare @qrynomcomponent varchar(max)
			select @qrynomcomponent = COALESCE(@qrynomcomponent + char(10), '') + '- ' + nocomponent.theMember.value('@fullname','varchar(75)') + ' (' + nocomponent.theMember.value('@membernumber','varchar(50)') + ')'
			from @subImportResult.nodes('/subimport/nocomponent/member') as nocomponent(theMember)

			declare @qryinvalidcomponent varchar(max)
			select @qryinvalidcomponent = COALESCE(@qryinvalidcomponent + char(10), '') + '- ' + invalidcomponent.theMember.value('@fullname','varchar(75)') + ' (' + invalidcomponent.theMember.value('@membernumber','varchar(50)') + ')'
			from @subImportResult.nodes('/subimport/invalidcomponent/member') as invalidcomponent(theMember)

			declare @qryexpiredcomponent varchar(max)
			select @qryexpiredcomponent = COALESCE(@qryexpiredcomponent + char(10), '') + '- ' + expiredcomponent.theMember.value('@fullname','varchar(75)') + ' (' + expiredcomponent.theMember.value('@membernumber','varchar(50)') + ')'
			from @subImportResult.nodes('/subimport/expiredcomponent/member') as expiredcomponent(theMember)

			set @finalMSG = ''
			
			if @qrynomcomponent is not null BEGIN
				select @finalMSG = @finalMSG + 'The following records contain blank Components' + case 
					when @subImportResult.value('count(/subimport/nocomponent/member)','int') > 300 then ' (only 300 are shown)'
					else '' end + ':' + char(10) + @qrynomcomponent + char(10) + char(10)
			END

			if @qryinvalidcomponent is not null BEGIN
				select @finalMSG = @finalMSG + 'The following records contain invalid Components' + case 
					when @subImportResult.value('count(/subimport/invalidcomponent/member)','int') > 300 then ' (only 300 are shown)'
					else '' end + ':' + char(10) + @qryinvalidcomponent + char(10) + char(10)
			END
			
			if @qryexpiredcomponent is not null BEGIN
				select @finalMSG = @finalMSG + 'The following are records in which existing Components have been expired due to blank Components or changed Components in the import file:' + case 
					when @subImportResult.value('count(/subimport/expiredcomponent/member)','int') > 300 then ' (only 300 are shown)'
					else '' end + ':' + char(10) + @qryexpiredcomponent + char(10) + char(10)
			END

			IF len(@finalMSG) = 0 BEGIN
				set @finalMSG = 'The Components have successfully been added. No invalid Components found.'
			END

			set @emailtouse = NULL

			select 
				@emailtouse = me.email
			from 
				membercentral.dbo.ams_members as m
				inner join membercentral.dbo.ams_members as m2 on 
					m2.memberid = m.activeMemberID
				left outer join membercentral.dbo.ams_memberEmails as me 
					inner join membercentral.dbo.ams_memberEmailTypes as met on 
						met.emailTypeID = me.emailTypeID 
						and met.emailTypeOrder = 1
					on me.memberID = m2.memberID
			where
				 m.memberID = @recordedByMemberID

			IF len(@emailtouse) > 0 BEGIN
				EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to=@emailtouse,
					@cc=null, @bcc='<EMAIL>', @subject='TAGD Component Import Results',
					@message=@finalMSG, @priority='normal', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
			END

			set @importResult = null
			EXEC membercentral.dbo.sub_importSubscriptions_toTemp @siteID=@siteID, @tmptbl='##tagd_import_subscriptions', @pathToExport=@pathToExport, @importResult=@importResult OUTPUT
		
			-- if no errors, run subscription import
			IF @importResult.value('count(/import/errors/error)','int') = 0
			BEGIN
				SELECT @flatfile = @importResult.value('(/import/@flatfile)[1]','varchar(160)')
				set @importResult = null
				EXEC membercentral.dbo.sub_importSubscriptions_toPerm @siteID=@siteID, @recordedByMemberID=@recordedByMemberID, @flatfile=@flatfile, @importResult=@importResult OUTPUT
			END
		END
	END
END

-- cleanup
on_cleanup:
	IF OBJECT_ID('tempdb..##tagd_nationals_alpha') IS NOT NULL
		DROP TABLE ##tagd_nationals_alpha
	IF OBJECT_ID('tempdb..##tagd_nationals_new') IS NOT NULL
		DROP TABLE ##tagd_nationals_new
	IF OBJECT_ID('tempdb..##tagd_nationals_dues') IS NOT NULL
		DROP TABLE ##tagd_nationals_dues
	IF OBJECT_ID('tempdb..##tagd_nationals_Members') IS NOT NULL
		DROP TABLE ##tagd_nationals_Members
	IF OBJECT_ID('tempdb..##tagd_nationals_acct') IS NOT NULL
		DROP TABLE ##tagd_nationals_acct
	IF OBJECT_ID('tempdb..#tagd_memberType') IS NOT NULL
		DROP TABLE #tagd_memberType
	IF OBJECT_ID('tempdb..#temp_tagd_nationals_alpha') IS NOT NULL
		DROP TABLE #temp_tagd_nationals_alpha
	IF OBJECT_ID('tempdb..##tagd_import_subscriptions') IS NOT NULL
		DROP TABLE ##tagd_import_subscriptions
	IF OBJECT_ID('tempdb..#tempTreeCodeTbl') IS NOT NULL
		DROP TABLE #tempTreeCodeTbl
	IF OBJECT_ID('tempdb..#tagd_sub_renewals') IS NOT NULL
		DROP TABLE #tagd_sub_renewals
	IF OBJECT_ID('tempdb..#tagd_sub_newmembers') IS NOT NULL
		DROP TABLE #tagd_sub_newmembers
	IF OBJECT_ID('tempdb..#tagd_sub_switchedComponent') IS NOT NULL
		DROP TABLE #tagd_sub_switchedComponent
	IF OBJECT_ID('tempdb..#tagd_sub_newComponent') IS NOT NULL
		DROP TABLE #tagd_sub_newComponent
	IF OBJECT_ID('tempdb..#tagd_sub_expireComponent') IS NOT NULL
		DROP TABLE #tagd_sub_expireComponent
GO

DROP PROC dbo.md_importMemberData
GO


USE [memberCentral]
GO

ALTER PROC [dbo].[ams_importMemberData]
@orgid int,
@csvfilename varchar(200),
@strTableColumnNames varchar(max),
@pathToExport varchar(100),
@importResult xml OUTPUT

AS

DECLARE @qry varchar(max), @orgcode varchar(10), @tblName varchar(15)

-- log it
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Starting importMemberData'

-- get table name
select @orgcode = orgcode from dbo.organizations where orgID = @orgID
select @tblName = '##tmp' + @orgcode

-- delete temp table if exists
IF OBJECT_ID('tempdb..' + @tblName) IS NOT NULL 
	EXEC('DROP TABLE ' + @tblName)

-- create temp table using columnlist
select @qry = COALESCE(@qry + ', ', '') + quotename(listitem) + ' varchar(max) '
FROM dbo.fn_varCharListToTable(@strTableColumnNames,char(7))
order by autoid
	EXEC('CREATE TABLE ' + @tblName + ' (' + @qry + ')')

-- rowID and membernumber is in a key column of an index and needs to not be varchar(max)
declare @trash bit
set @trash = 1
EXEC('ALTER TABLE ' + @tblName + ' ALTER COLUMN rowID int') 
BEGIN TRY
	EXEC('ALTER TABLE ' + @tblName + ' ALTER COLUMN membernumber varchar(800)') 
END TRY
BEGIN CATCH
	set @trash = 0
END CATCH

-- Execute a bulk insert into previously defined temporary table
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Importing Flattened Data'
SELECT @qry = 'BULK INSERT ' + @tblName + ' FROM ''' + @csvfilename + ''' WITH (FIELDTERMINATOR = '''+ char(7) + ''', FIRSTROW = 2);'
EXEC(@qry)

-- add in the special column BillingAddressType if it doesnt already exist
BEGIN TRY
	EXEC('ALTER TABLE ' + @tblName + ' ADD BillingAddressType varchar(20) NULL')
END TRY
BEGIN CATCH
	set @trash = 0
END CATCH
EXEC('UPDATE ' + @tblName + ' SET BillingAddressType = '''' where BillingAddressType is null')

-- move data to holding tables
EXEC dbo.ams_importMemberData_tempToHolding @orgid=@orgID, @tmptbl=@tblName, @pathToExport=@pathToExport, @importResult=@importResult OUTPUT

RETURN 0
GO

ALTER PROC [dbo].[ams_importMemberData_tempToHolding]
@orgid int, 
@tmptbl varchar(30),
@pathToExport varchar(100),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Starting importMemberData_toHolding'

DECLARE @prefix varchar(50), @exportcmd varchar(400), @dynSQL nvarchar(max), @flatfile varchar(160),
	@columnID int, @mincol varchar(255), @websiteType varchar(20), @emailType varchar(20), 
	@addressType varchar(20), @good bit, @importFileCol varchar(400), @defaultValueID int, 
	@colDataTypeCode varchar(20), @plType varchar(200), @allownull bit, @OrgColDataLength int,
	@ImpColDataLength int
declare @tblMissingCols TABLE (colName varchar(255))
declare @tblErrors TABLE (rowid int IDENTITY(1,1), msg varchar(600), fatal bit)
declare @tblDataChanged TABLE (rowid int IDENTITY(1,1), msg varchar(600))
declare @tblCounts TABLE (rowid int IDENTITY(1,1), countName varchar(50), countNum int)
declare @tblMemList1 TABLE (memberid int, memberNumber varchar(50), firstname varchar(75), lastname varchar(75))
declare @tblMemList2 TABLE (memberid int, memberNumber varchar(50), firstname varchar(75), lastname varchar(75))
declare @tblMemList3 TABLE (rowid int, memberNumber varchar(50), firstname varchar(75), lastname varchar(75))

DECLARE @orgCode varchar(10), @hasPrefix bit, @usePrefixList bit, @hasMiddleName bit, @hasSuffix bit, @hasProfessionalSuffix bit, @hasCompany bit
select @orgcode=orgcode, @hasPrefix=hasPrefix, @usePrefixList=usePrefixList, @hasMiddleName=hasMiddleName,
	@hasSuffix=hasSuffix, @hasProfessionalSuffix=hasProfessionalSuffix, @hasCompany=hasCompany
	from dbo.organizations 
	where orgID = @orgid

declare @var_tmpCols varchar(20), @var_tmpColsSkipped varchar(30)
select @var_tmpCols = '##tmpCols' + @orgcode
select @var_tmpColsSkipped = '##tmpColsSkipped' + @orgcode

-- cleanup
IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
	EXEC('DROP TABLE ' + @var_tmpCols)
IF OBJECT_ID('tempdb..#tblOrgCols') IS NOT NULL 
	DROP TABLE #tblOrgCols
IF OBJECT_ID('tempdb..#tblImportCols') IS NOT NULL 
	DROP TABLE #tblImportCols
IF OBJECT_ID('tempdb..' + @var_tmpColsSkipped) IS NOT NULL 
	EXEC('DROP TABLE ' + @var_tmpColsSkipped)


-- ******************************** 
-- ensure all columns exist (fatal)
-- ******************************** 
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking Required Columns'

-- this will get the columns that should be there based on current setup
CREATE TABLE #tblOrgCols (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
	COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
	SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
	COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
	CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)

	-- Need the temp table code for the export. replace the first FROM occurence 
	select @dynSQL = ''
	EXEC dbo.ams_getFlattenedMemberDataSQL @orgID=@orgID, @limitTop=1, @limitActive=1, @limitMID='', @showMCFields=0, @showSkippedFields=0, @sql=@dynSQL OUTPUT
	select @dynSQL = stuff(@dynSQL, charIndex('from membercentral',@dynSQL), len('from membercentral'), 'into ' + @var_tmpCols + ' from membercentral')
	EXEC(@dynSQL)

	-- BillingAddressType is a required column for member imports only.
	EXEC('ALTER TABLE ' + @var_tmpCols + ' ADD BillingAddressType varchar(20) NULL')

	-- get cols
	INSERT INTO #tblOrgCols
	EXEC tempdb.dbo.SP_COLUMNS @var_tmpCols
	
	-- cleanup table no longer needed
	IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
		EXEC('DROP TABLE ' + @var_tmpCols)

-- this will get the columns that are actually in the import
CREATE TABLE #tblImportCols (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
	COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
	SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
	COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
	CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)

	-- get cols
	INSERT INTO #tblImportCols
	EXEC tempdb.dbo.SP_COLUMNS @tmptbl

INSERT INTO @tblErrors (msg, fatal)
select 'The column ' + org.column_name + ' is missing from your data.', 1
from #tblOrgCols as org
left outer join #tblImportCols as imp on imp.column_name = org.column_name
where imp.table_name is null

insert into @tblMissingCols(colname)
select org.column_name
from #tblOrgCols as org
left outer join #tblImportCols as imp on imp.column_name = org.column_name
where imp.table_name is null


-- ********************************
-- data type checks (fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking DATE columns'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and dt.dataTypeCode = 'DATE'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'datetime')
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			UPDATE ' + @tmptbl + ' set ' + quotename(@mincol) + ' = null where ' + quotename(@mincol) + ' = '''';
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' datetime null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid dates.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and dt.dataTypeCode = 'DATE'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'datetime')
		where mdc.columnname > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking DECIMAL2 columns'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 0
		and dt.dataTypeCode = 'DECIMAL2'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'decimal')
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			UPDATE ' + @tmptbl + ' SET ' + quotename(@mincol) + ' = replace(' + quotename(@mincol) + ','','','''');
			UPDATE ' + @tmptbl + ' SET ' + quotename(@mincol) + ' = null where ' + quotename(@mincol) + ' = '''';
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' decimal(9,2) null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid decimal values.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and mdc.allowMultiple = 0
			and dt.dataTypeCode = 'DECIMAL2'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'decimal')
		where mdc.columnname > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking DECIMAL2 columns (multiple values)'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 1
		and dt.dataTypeCode = 'DECIMAL2'
		and mdc.columnname not in (select colName from @tblMissingCols)
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' varchar(max) null;

			IF EXISTS (
				select top 1 tbl.listItem
				from ' + @tmptbl + ' 
				cross apply dbo.fn_decimal2ListToTable(' + quotename(@mincol) + ',''|'') as tbl
				where nullif(' + quotename(@mincol) + ','''') is not null
			) set @good = @good
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid decimal values.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and mdc.allowMultiple = 1
			and dt.dataTypeCode = 'DECIMAL2'
			and mdc.columnname not in (select colName from @tblMissingCols)
		where mdc.columnname > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking INTEGER columns'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 0
		and dt.dataTypeCode = 'INTEGER'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and mdc.columnname not in (select column_Name from #tblImportCols where left(type_name,3) = 'int')
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			UPDATE ' + @tmptbl + ' SET ' + quotename(@mincol) + ' = replace(' + quotename(@mincol) + ','','','''');
			UPDATE ' + @tmptbl + ' SET ' + quotename(@mincol) + ' = null where ' + quotename(@mincol) + ' = '''';
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' int null
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid whole number values.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and mdc.allowMultiple = 0
			and dt.dataTypeCode = 'INTEGER'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnname not in (select column_Name from #tblImportCols where left(type_name,3) = 'int')
		where mdc.columnname > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking INTEGER columns (multiple values)'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 1
		and dt.dataTypeCode = 'INTEGER'
		and mdc.columnname not in (select colName from @tblMissingCols)
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' varchar(max) null;

			IF EXISTS (			
				select top 1 tbl.listItem
				from ' + @tmptbl + ' 
				cross apply dbo.fn_intListToTable(' + quotename(@mincol) + ',''|'') as tbl
				where nullif(' + quotename(@mincol) + ','''') is not null
			) set @good = @good
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid whole number values.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and mdc.allowMultiple = 1
			and dt.dataTypeCode = 'INTEGER'
			and mdc.columnname not in (select colName from @tblMissingCols)
		where mdc.columnname > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking BIT columns'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and dt.dataTypeCode = 'BIT'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'bit')
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			UPDATE ' + @tmptbl + ' SET ' + quotename(@mincol) + ' = 1 where ' + quotename(@mincol) + ' = ''TRUE'' OR ' + quotename(@mincol) + ' = ''YES'';
			UPDATE ' + @tmptbl + ' SET ' + quotename(@mincol) + ' = 0 where ' + quotename(@mincol) + ' = ''FALSE'' OR ' + quotename(@mincol) + ' = ''NO'';
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' bit null
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid boolean values.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and dt.dataTypeCode = 'BIT'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'bit')
		where mdc.columnname > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking XML columns'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and dt.dataTypeCode = 'XML'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'xml')
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' xml null
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid XML values.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and dt.dataTypeCode = 'XML'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'xml')
		where mdc.columnname > @mincol
END

-- *********************************************************************
-- ensure all varchar columns match data length requirements (non-fatal)
-- *********************************************************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking VARCHAR columns for lengths'
select @mincol = null
select @mincol = min(column_name)
	from #tblOrgCols
	where type_Name = 'varchar'
	and [length] > 0
	and column_name not in (select colName from @tblMissingCols)
	and column_name in (select column_Name from #tblImportCols where type_Name = 'varchar' and [length] > 0)
while @mincol is not null BEGIN
	select @OrgColDataLength = null, @ImpColDataLength = null
	select @OrgColDataLength = [length] from #tblOrgCols where column_name = @mincol
	select @ImpColDataLength = [length] from #tblImportCols where column_name = @mincol

	-- if length of orgcol < length of importcol, report on data truncation
	IF @OrgColDataLength < @ImpColDataLength BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '') has invalid data for ' + quoteName(@mincol) + '. The maximum number of characters for this column is ' + cast(@OrgColDataLength as varchar(5)) + '.'' as msg, 0 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE len(' + quotename(@mincol) + ') > ' + cast(@OrgColDataLength as varchar(5)) + '
			ORDER BY rowID'
		INSERT INTO @tblErrors (msg, fatal)
		EXEC(@dynSQL)

		IF @@ROWCOUNT > 0 BEGIN
			select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '') will truncate ' + quoteName(@mincol) + ' to fit.'' as msg
				FROM ' + @tmptbl + ' 
				WHERE len(' + quotename(@mincol) + ') > ' + cast(@OrgColDataLength as varchar(5)) + '
				ORDER BY rowID'
			INSERT INTO @tblDataChanged (msg)
			EXEC(@dynSQL)

			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@mincol) + ' = left(' + quotename(@mincol) + ',' + cast(@OrgColDataLength as varchar(5)) + ')
				WHERE len(' + quotename(@mincol) + ') > ' + cast(@OrgColDataLength as varchar(5)) + ' '
			EXEC(@dynSQL)
		END
	END

	-- if length of orgcol <> length of importcol, alter table so it matches.
	IF @OrgColDataLength <> @ImpColDataLength BEGIN
		set @good = 1
		set @dynSQL = '
			set @good = 1
			BEGIN TRY
				ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' varchar(' + cast(@OrgColDataLength as varchar(5)) + ') null;
			END TRY
			BEGIN CATCH
				set @good = 0
			END CATCH'
			exec sp_executesql @dynSQL, N'@good bit output', @good output
		IF @good = 0
			INSERT INTO @tblErrors (msg, fatal)
			VALUES ('The column ' + @mincol + ' could not be expanded to support ' + cast(@OrgColDataLength as varchar(5)) + ' characters.', 1)
	END

	select @mincol = min(column_name)
		from #tblOrgCols
		where type_Name = 'varchar'
		and [length] > 0
		and column_name not in (select colName from @tblMissingCols)
		and column_name in (select column_Name from #tblImportCols where type_Name = 'varchar' and [length] > 0)
		and column_name > @mincol
END


-- ********************************
-- no member number (fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying missing member numbers'

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'memberNumber') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '') is missing a Member Number. Member Numbers are required for all members.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE (memberNumber IS NULL OR ltrim(rtrim(memberNumber)) = '''')
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

-- ********************************
-- dupe member numbers (fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying duplicate member numbers'

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'memberNumber') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''The Member Number '' + isnull(memberNumber,'''') + '' appears '' + cast(count(*) as varchar(10)) + '' times. Member Numbers must be unique.'' as msg, 1 as fatal
			FROM ' + @tmptbl + ' 
			GROUP BY memberNumber
			HAVING COUNT(*) > 1
			ORDER BY 1'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

-- ********************************
-- conflicting member numbers with guest accounts (fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying conflicting member numbers'

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'memberNumber') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''The Member Number '' + isnull(tbl.memberNumber,'''') + '' belongs to an existing guest account.'' as msg, 1 as fatal
			FROM ' + @tmptbl + ' as tbl 
			WHERE isnull(tbl.memberNumber,'''')	<> ''''
			AND EXISTS (
				select memberID
				from dbo.ams_members
				where status <> ''D''
				and orgID = ' + cast(@orgID as varchar(6)) + ' 
				and memberTypeID = 1
				and membernumber = tbl.memberNumber
			)
			ORDER BY 1'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

-- ********************************
-- no first name (fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying missing first names'

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'firstname') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') is missing a First Name. First Names are required for all members.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE (firstname IS NULL OR ltrim(rtrim(firstname)) = '''')
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

-- ********************************
-- no last name (fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying missing last names'

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'lastname') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') is missing a Last Name. Last Names are required for all members.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE (lastname IS NULL OR ltrim(rtrim(lastname)) = '''')
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

-- ********************************
-- bad website data (non-fatal)
-- be nice and clean up their website data first (ensure http:// in front)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying invalid website data'

declare @tldList varchar(max)
set @tldList = 'com|edu|gov|int|mil|net|org|arpa|coop|asia|cat|academy|accountants|active|actor|aero|agency|airforce|archi|army|associates|attorney|auction|audio|autos|band|bargains|beer|best|bid|bike|bio|biz|black|blackfriday|blue|boo|boutique|build|builders|business|buzz|cab|camera|camp|cancerresearch|capital|cards|care|career|careers|cash|catering|center|ceo|channel|cheap|christmas|church|city|claims|cleaning|click|clinic|clothing|club|coach|codes|coffee|college|community|company|computer|condos|construction|consulting|contractors|cooking|cool|country|credit|creditcard|cricket|cruises|dad|dance|dating|day|deals|degree|delivery|democrat|dental|dentist|diamonds|diet|digital|direct|directory|discount|domains|eat|education|email|energy|engineer|engineering|equipment|esq|estate|events|exchange|expert|exposed|fail|farm|feedback|finance|financial|fish|fishing|fitness|flights|florist|fly|foo|forsale|foundation|fund|furniture|futbol|gallery|gift|gifts|gives|glass|global|gop|graphics|green|gripe|guide|guitars|guru|healthcare|help|here|hiphop|hiv|holdings|holiday|homes|horse|host|hosting|house|how|info|ing|ink|insure|international|investments|jobs|kim|kitchen|land|lawyer|lease|legal|lgbt|life|lighting|limited|limo|link|loans|lotto|luxe|luxury|management|market|marketing|media|meet|meme|memorial|menu|mobi|moe|money|mortgage|motorcycles|mov|museum|name|navy|network|new|ngo|ninja|ong|onl|ooo|organic|partners|parts|party|pharmacy|photo|photography|photos|physio|pics|pictures|pink|pizza|place|plumbing|poker|post|press|pro|productions|prof|properties|property|qpon|recipes|red|rehab|ren|rentals|repair|report|republican|reviews|rich|rip|rocks|rodeo|rsvp|science|services|sexy|shoes|singles|social|software|solar|solutions|space|supplies|supply|support|surf|surgery|systems|tattoo|tax|technology|tel|tips|tires|today|tools|top|town|toys|trade|training|travel|university|vacations|vet|villas|vision|vodka|vote|voting|voyage|wang|watch|webcam|website|wed|wiki|works|world|wtf|xxx|xyz|zone'

select @websiteType = min(websiteType)
	FROM dbo.ams_memberWebsiteTypes
	WHERE orgID = @orgID
	and websiteType NOT IN (select colName from @tblMissingCols)
while @websiteType is not null BEGIN

	select @dynSQL = 'update ' + @tmptbl + ' 
		set ' + quotename(@websiteType) + ' = ''http://'' + ' + quotename(@websiteType) + ' 
		where len(' + quotename(@websiteType) + ') > 0 and left(' + quotename(@websiteType) + ',4) <> ''http'''
	EXEC(@dynSQL)

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the website ' + @websiteType + ': '' + ' + quoteName(@websiteType) + ' as msg, 0 as fatal
		FROM ' + @tmptbl + ' 
		WHERE len(' + quotename(@websiteType) + ') > 0 and dbo.fn_RegExReplace(' + quotename(@websiteType) + ',''^(http|https|ftp)\://([a-zA-Z0-9\.\-]+(\:[a-zA-Z0-9\.&amp;%\$\-]+)*@)*((25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9])\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[0-9])|localhost|([a-zA-Z0-9\-]+\.)*[a-zA-Z0-9\-]+\.(' + @tldList + '|[a-zA-Z]{2}))(\:[0-9]+)*(/($|[a-zA-Z0-9\.\,\?\''''\\\+&amp;%\!$#\=~_\-]+))*$'','''') <> '''' 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the website ' + @websiteType + '.'' as msg
			FROM ' + @tmptbl + ' 
			WHERE len(' + quotename(@websiteType) + ') > 0 and dbo.fn_RegExReplace(' + quotename(@websiteType) + ',''^(http|https|ftp)\://([a-zA-Z0-9\.\-]+(\:[a-zA-Z0-9\.&amp;%\$\-]+)*@)*((25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9])\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[0-9])|localhost|([a-zA-Z0-9\-]+\.)*[a-zA-Z0-9\-]+\.(' + @tldList + '|[a-zA-Z]{2}))(\:[0-9]+)*(/($|[a-zA-Z0-9\.\,\?\''''\\\+&amp;%\!$#\=~_\-]+))*$'','''') <> '''' 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@websiteType) + ' = '''' 
			WHERE len(' + quotename(@websiteType) + ') > 0 and dbo.fn_RegExReplace(' + quotename(@websiteType) + ',''^(http|https|ftp)\://([a-zA-Z0-9\.\-]+(\:[a-zA-Z0-9\.&amp;%\$\-]+)*@)*((25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9])\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[0-9])|localhost|([a-zA-Z0-9\-]+\.)*[a-zA-Z0-9\-]+\.(' + @tldList + '|[a-zA-Z]{2}))(\:[0-9]+)*(/($|[a-zA-Z0-9\.\,\?\''''\\\+&amp;%\!$#\=~_\-]+))*$'','''') <> '''' '
		EXEC(@dynSQL)
	END

	select @websiteType = min(websiteType)
		FROM dbo.ams_memberWebsiteTypes
		WHERE orgID = @orgID
		and websiteType NOT IN (select colName from @tblMissingCols)
		AND websiteType > @websiteType
END

-- ********************************
-- bad email data (non-fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying invalid e-mail data'

select @emailType = min(emailType)
	FROM dbo.ams_memberEmailTypes
	WHERE orgID = @orgID
	and emailType NOT IN (select colName from @tblMissingCols)
while @emailType is not null BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the e-mail ' + @emailType + ': '' + ' + quotename(@emailType) + ' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE len(' + quotename(@emailType) + ') > 0 and dbo.fn_RegExReplace(' + quotename(@emailType) + ',''^[a-zA-Z_0-9-''''\&\+~]+(\.[a-zA-Z_0-9-''''\&\+~]+)*@([a-zA-Z_0-9-]+\.)+[a-zA-Z]{2,7}$'','''') <> '''' 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the e-mail ' + @emailType + '.'' as msg
			FROM ' + @tmptbl + ' 
			WHERE len(' + quotename(@emailType) + ') > 0 and dbo.fn_RegExReplace(' + quotename(@emailType) + ',''^[a-zA-Z_0-9-''''\&\+~]+(\.[a-zA-Z_0-9-''''\&\+~]+)*@([a-zA-Z_0-9-]+\.)+[a-zA-Z]{2,7}$'','''') <> '''' 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@emailType) + ' = '''' 
			WHERE len(' + quotename(@emailType) + ') > 0 and dbo.fn_RegExReplace(' + quotename(@emailType) + ',''^[a-zA-Z_0-9-''''\&\+~]+(\.[a-zA-Z_0-9-''''\&\+~]+)*@([a-zA-Z_0-9-]+\.)+[a-zA-Z]{2,7}$'','''') <> '''' '
		EXEC(@dynSQL)
	END

	select @emailType = min(emailType)
		FROM dbo.ams_memberEmailTypes
		WHERE orgID = @orgID
		and emailType NOT IN (select colName from @tblMissingCols)
		AND emailType > @emailType
END


-- ********************************
-- bad billing address type (non-fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying invalid billing address types'

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'BillingAddressType') BEGIN
	declare @defaultBillingAddressType varchar(20)
	select @defaultBillingAddressType = addressType from dbo.ams_memberAddressTypes where orgID = @orgID and addressTypeOrder = 1

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(tmp.rowID as varchar(10)) + '' ('' + isnull(tmp.firstname,'''') + '' '' + isnull(tmp.lastname,'''') + '') has invalid data for BillingAddressType.'' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as tmp
		LEFT OUTER JOIN membercentral.dbo.ams_memberAddressTypes as mat on mat.orgID = ' + cast(@orgID as varchar(10)) + ' and mat.addressType = tmp.BillingAddressType 
		WHERE len(tmp.BillingAddressType) > 0 
		AND mat.addressTypeID is null 
		ORDER BY tmp.rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(tmp.rowID as varchar(10)) + '' ('' + isnull(tmp.firstname,'''') + '' '' + isnull(tmp.lastname,'''') + '') will change the BillingAddressType from '' + tmp.BillingAddressType + '' to ' + @defaultBillingAddressType + ''' as msg
			FROM ' + @tmptbl + ' as tmp
			LEFT OUTER JOIN membercentral.dbo.ams_memberAddressTypes as mat on mat.orgID = ' + cast(@orgID as varchar(10)) + ' and mat.addressType = tmp.BillingAddressType 
			WHERE len(tmp.BillingAddressType) > 0 
			AND mat.addressTypeID is null 
			ORDER BY tmp.rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE tmp
			SET tmp.BillingAddressType = '''' 
			FROM ' + @tmptbl + ' as tmp
			LEFT OUTER JOIN membercentral.dbo.ams_memberAddressTypes as mat on mat.orgID = ' + cast(@orgID as varchar(10)) + ' and mat.addressType = tmp.BillingAddressType 
			WHERE len(tmp.BillingAddressType) > 0 
			AND mat.addressTypeID is null '
		EXEC(@dynSQL)
	END

	-- if blank, existing members should retain their existing value.
	select @dynSQL = 'UPDATE tmp 
		SET tmp.BillingAddressType = mat.addressType 
		FROM ' + @tmptbl + ' as tmp 
		INNER JOIN dbo.ams_members as m on m.memberNumber = tmp.memberNumber 
			AND m.orgID = ' + cast(@orgID as varchar(10)) + ' 
			AND m.membertypeID = 2 
			AND m.memberID = m.activeMemberID 
			AND m.status <> ''D'' 
		INNER JOIN dbo.ams_memberAddressTypes as mat on mat.addressTypeID = m.billingAddressTypeID 
		WHERE tmp.BillingAddressType = '''''
	EXEC(@dynSQL)

	-- if blank, new members should get the default value. 
	select @dynSQL = 'UPDATE ' + @tmptbl + ' 
		SET BillingAddressType = ''' + @defaultBillingAddressType + '''  
		WHERE BillingAddressType = '''''
	EXEC(@dynSQL)
END


-- ********************************
-- bad address data (non-fatal)
--  - country specified but not valid country
--  - state and country specified but not valid state
--  - state specified but no country specified
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying invalid address data'

select @addressType = min(addressType)
	FROM dbo.ams_memberAddressTypes
	WHERE orgID = @orgID
	and (
		addressType + '_stateprov' NOT IN (select colName from @tblMissingCols)
		or
		addressType + '_country' NOT IN (select colName from @tblMissingCols)
	)
while @addressType is not null BEGIN

	-- country specified but not valid country
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for ' + @addressType + '_country' + ': '' + ' + quotename(@addressType + '_country') + ' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as tmp 
		WHERE len(isnull(tmp.' + quotename(@addressType + '_country') + ','''')) > 0 
		and not exists (select countryID from dbo.ams_countries where country = isnull(tmp.' + quotename(@addressType + '_country') + ',''''))
		ORDER BY tmp.rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the ' + @addressType + '_country' + '.'' as msg
			FROM ' + @tmptbl + ' as tmp 
			WHERE len(isnull(tmp.' + quotename(@addressType + '_country') + ','''')) > 0 
			and not exists (select countryID from dbo.ams_countries where country = isnull(tmp.' + quotename(@addressType + '_country') + ',''''))
			ORDER BY tmp.rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@addressType + '_country') + ' = '''' 
			WHERE len(isnull(' + quotename(@addressType + '_country') + ','''')) > 0 
			and not exists (select countryID from dbo.ams_countries where country = isnull(' + quotename(@addressType + '_country') + ','''')) '
		EXEC(@dynSQL)
	END

	-- state and country specified but not valid state
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for ' + @addressType + '_stateprov' + ': '' + ' + quotename(@addressType + '_stateprov') + ' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as tmp 
		WHERE len(isnull(tmp.' + quotename(@addressType + '_stateprov') + ','''')) > 0 
		and len(isnull(tmp.' + quotename(@addressType + '_country') + ','''')) > 0 
		and not exists (
			select s.stateID
			from dbo.ams_states as s
			inner join dbo.ams_countries as c on c.countryID = s.countryID
			where s.code = isnull(tmp.' + quotename(@addressType + '_stateprov') + ','''') 
			and c.country = isnull(tmp.' + quotename(@addressType + '_country') + ','''') 
		)
		and exists (select countryID from dbo.ams_countries where country = isnull(tmp.' + quotename(@addressType + '_country') + ',''''))
		ORDER BY tmp.rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the ' + @addressType + '_stateprov' + '.'' as msg
			FROM ' + @tmptbl + ' as tmp 
			WHERE len(isnull(tmp.' + quotename(@addressType + '_stateprov') + ','''')) > 0 
			and len(isnull(tmp.' + quotename(@addressType + '_country') + ','''')) > 0 
			and not exists (
				select s.stateID
				from dbo.ams_states as s
				inner join dbo.ams_countries as c on c.countryID = s.countryID
				where s.code = isnull(tmp.' + quotename(@addressType + '_stateprov') + ','''') 
				and c.country = isnull(tmp.' + quotename(@addressType + '_country') + ','''') 
			)
			and exists (select countryID from dbo.ams_countries where country = isnull(tmp.' + quotename(@addressType + '_country') + ',''''))
			ORDER BY tmp.rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@addressType + '_stateprov') + ' = '''' 
			WHERE len(isnull(' + quotename(@addressType + '_stateprov') + ','''')) > 0 
			and len(isnull(' + quotename(@addressType + '_country') + ','''')) > 0 
			and not exists (
				select s.stateID
				from dbo.ams_states as s
				inner join dbo.ams_countries as c on c.countryID = s.countryID
				where s.code = isnull(' + quotename(@addressType + '_stateprov') + ','''') 
				and c.country = isnull(' + quotename(@addressType + '_country') + ','''') 
			)
			and exists (select countryID from dbo.ams_countries where country = isnull(' + quotename(@addressType + '_country') + ','''')) '
		EXEC(@dynSQL)
	END

	-- state specified but no country specified
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had a missing country value for ' + @addressType + '_stateprov' + ': '' + ' + quotename(@addressType + '_stateprov') + ' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as tmp 
		WHERE len(isnull(tmp.' + quotename(@addressType + '_stateprov') + ','''')) > 0 
		and len(isnull(tmp.' + quotename(@addressType + '_country') + ','''')) = 0 
		ORDER BY tmp.rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the ' + @addressType + '_stateprov' + '.'' as msg
			FROM ' + @tmptbl + ' as tmp 
			WHERE len(isnull(tmp.' + quotename(@addressType + '_stateprov') + ','''')) > 0 
			and len(isnull(tmp.' + quotename(@addressType + '_country') + ','''')) = 0 
			ORDER BY tmp.rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@addressType + '_stateprov') + ' = '''' 
			WHERE len(isnull(' + quotename(@addressType + '_stateprov') + ','''')) > 0 
			and len(isnull(' + quotename(@addressType + '_country') + ','''')) = 0 '
		EXEC(@dynSQL)
	END

	select @addressType = min(addressType)
		FROM dbo.ams_memberAddressTypes
		WHERE orgID = @orgID
		and (
			addressType + '_stateprov' NOT IN (select colName from @tblMissingCols)
			or
			addressType + '_country' NOT IN (select colName from @tblMissingCols)
		)
		AND addressType > @addressType
END

-- ********************************
-- bad prefix data (non-fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying invalid prefix data'

IF @hasPrefix = 1 and @usePrefixList = 1 and NOT EXISTS (select colName from @tblMissingCols where colName = 'prefix') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid prefix value'' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE prefix is not null and len(prefix) > 0 and prefix not in (select prefix from dbo.ams_memberPrefixTypes where orgID = ' + cast(@orgID as varchar(10)) + ')  
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the Prefix.'' as msg
			FROM ' + @tmptbl + ' 
			WHERE prefix is not null and len(prefix) > 0 and prefix not in (select prefix from dbo.ams_memberPrefixTypes where orgID = ' + cast(@orgID as varchar(10)) + ')  
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET prefix = '''' 
			WHERE prefix is not null and len(prefix) > 0 and prefix not in (select prefix from dbo.ams_memberPrefixTypes where orgID = ' + cast(@orgID as varchar(10)) + ') '
		EXEC(@dynSQL)
	END
END

-- ********************************
-- bad prof license data (fatal and non-fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying invalid professional license data'

select @plType = min(PLName)
	FROM dbo.ams_memberProfessionalLicenseTypes
	WHERE orgID = @orgID
	and PLName + '_licenseNumber' NOT IN (select colName from @tblMissingCols)
	and PLName + '_status' NOT IN (select colName from @tblMissingCols)
	and PLName + '_activeDate' NOT IN (select colName from @tblMissingCols)
while @plType is not null BEGIN
	
	-- dates must be valid
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			UPDATE ' + @tmptbl + ' set ' + quotename(@plType + '_activeDate') + ' = null where ' + quotename(@plType + '_activeDate') + ' = ''''
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@plType + '_activeDate') + ' datetime null
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @plType + '_activeDate contains invalid dates.', 1)
	END
	ELSE BEGIN
		-- if status is defined, it must be valid
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for ' + @plType + '_status' + ': '' + ' + quotename(@plType + '_status') + ' as msg, 0 as fatal 
			FROM ' + @tmptbl + ' as tmp 
			WHERE len(isnull(tmp.' + quotename(@plType + '_status') + ','''')) > 0 
			and not exists (
				select PLStatusID
				from dbo.ams_memberProfessionalLicenseStatuses
				where StatusName = isnull(tmp.' + quotename(@plType + '_status') + ','''')
				and orgID = ' + cast(@orgID as varchar(10)) + '
			)'
		INSERT INTO @tblErrors (msg, fatal)
		EXEC(@dynSQL)

		IF @@ROWCOUNT > 0 BEGIN
			select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the ' + @plType + '_status' + '.'' as msg
				FROM ' + @tmptbl + ' as tmp 
				WHERE len(isnull(tmp.' + quotename(@plType + '_status') + ','''')) > 0 
				and not exists (
					select PLStatusID
					from dbo.ams_memberProfessionalLicenseStatuses
					where StatusName = isnull(tmp.' + quotename(@plType + '_status') + ','''')
					and orgID = ' + cast(@orgID as varchar(10)) + '
				)'
			INSERT INTO @tblDataChanged (msg)
			EXEC(@dynSQL)

			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@plType + '_status') + ' = '''' 
				WHERE len(isnull(' + quotename(@plType + '_status') + ','''')) > 0 
				and not exists (
					select PLStatusID
					from dbo.ams_memberProfessionalLicenseStatuses
					where StatusName = isnull(' + quotename(@plType + '_status') + ','''')
					and orgID = ' + cast(@orgID as varchar(10)) + '
				)'
			EXEC(@dynSQL)
		END

		-- if licenseNumber or activeDate is defined, status must be as well
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had a missing value for ' + @plType + '_status'' as msg, 0 as fatal 
			FROM ' + @tmptbl + ' as tmp 
			WHERE (
				len(isnull(tmp.' + quotename(@plType + '_licenseNumber') + ','''')) > 0
				or tmp.' + quotename(@plType + '_activeDate') + ' is not null
			)
			and len(isnull(tmp.' + quotename(@plType + '_status') + ','''')) = 0 '
		INSERT INTO @tblErrors (msg, fatal)
		EXEC(@dynSQL)

		IF @@ROWCOUNT > 0 BEGIN
			select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the ' + @plType + ' information.'' as msg
				FROM ' + @tmptbl + ' as tmp 
				WHERE (
					len(isnull(tmp.' + quotename(@plType + '_licenseNumber') + ','''')) > 0
					or tmp.' + quotename(@plType + '_activeDate') + ' is not null
				)
				and len(isnull(tmp.' + quotename(@plType + '_status') + ','''')) = 0 '
			INSERT INTO @tblDataChanged (msg)
			EXEC(@dynSQL)

			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@plType + '_licenseNumber') + ' = '''',
					' + quotename(@plType + '_activeDate') + ' = null 
				WHERE (
					len(isnull(' + quotename(@plType + '_licenseNumber') + ','''')) > 0
					or ' + quotename(@plType + '_activeDate') + ' is not null
				)
				and len(isnull(' + quotename(@plType + '_status') + ','''')) = 0 '
			EXEC(@dynSQL)
		END
	END

	select @plType = min(PLName)
		FROM dbo.ams_memberProfessionalLicenseTypes
		WHERE orgID = @orgID
		and PLName + '_licenseNumber' NOT IN (select colName from @tblMissingCols)
		and PLName + '_status' NOT IN (select colName from @tblMissingCols)
		and PLName + '_activeDate' NOT IN (select colName from @tblMissingCols)
		and PLName > @plType
END

-- ********************************
-- check custom columns that have a default defined; use that default instead of null
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking columns with default values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	where mdc.allowNull = 0
	and mdc.skipImport = 0
	and mdc.orgID = @orgID
	and mdc.defaultValueID is not null
	and mdc.columnname not in (select colName from @tblMissingCols)
while @mincol is not null BEGIN
	select @defaultValueID = defaultValueID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol
	select @colDataTypeCode = dt.dataTypeCode from dbo.ams_memberDataColumnDataTypes as dt inner join dbo.ams_memberDataColumns as mdc on mdc.dataTypeID = dt.dataTypeID and mdc.orgID = @orgID and mdc.columnName = @mincol

	IF @colDataTypeCode = 'STRING'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueString from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where nullif(' + quoteName(@mincol) + ','''') is null'
	IF @colDataTypeCode = 'DECIMAL2'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueDecimal2 from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where ' + quoteName(@mincol) + ' is null'
	IF @colDataTypeCode = 'INTEGER'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueInteger from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where ' + quoteName(@mincol) + ' is null'
	IF @colDataTypeCode = 'DATE'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueDate from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where ' + quoteName(@mincol) + ' is null'
	IF @colDataTypeCode = 'BIT'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueBit from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where ' + quoteName(@mincol) + ' is null'
	IF @colDataTypeCode = 'XML'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueXML from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where ' + quoteName(@mincol) + ' is null'
	IF @colDataTypeCode = 'CONTENTOBJ' OR @colDataTypeCode = 'DOCUMENTOBJ'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueSiteResourceID from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where ' + quoteName(@mincol) + ' is null'

	BEGIN TRY
		EXEC(@dynSQL)
	END TRY
	BEGIN CATCH
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains null values that could not use the defined default value.', 1)
	END CATCH

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		where mdc.allowNull = 0
		and mdc.skipImport = 0
		and mdc.orgID = @orgID
		and mdc.defaultValueID is not null
		and mdc.columnname not in (select colName from @tblMissingCols)
		and mdc.columnName > @mincol
END

-- ********************************
-- check for any custom columns that don't permit new values on import
-- for these cols, make sure all values in file are in defined set
-- skip columns where there is a fatal error on converting all values to correct datatype
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking STRING columns for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 0
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'STRING'
		and mdc.columnname not in (select colName from @tblMissingCols)
while @mincol is not null BEGIN
	select @columnID = null, @allownull = null
	select @columnID = columnID, @allownull = allowNull from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	IF @allownull = 1 BEGIN
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = null where ' + quoteName(@mincol) + ' = '''''
		EXEC(@dynSQL)
	END

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and mdcv.columnValueString = importfile.' + quoteName(@mincol) + ' 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and mdcv.columnValueString = importfile.' + quoteName(@mincol) + ' 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		IF @allownull = 1 BEGIN		
			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@mincol) + ' = null
				WHERE rowID in (
					SELECT rowID
					FROM ' + @tmptbl + ' as importfile
					LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
						and mdcv.columnValueString = importfile.' + quoteName(@mincol) + ' 
					WHERE mdcv.valueID is null 
					AND importfile.' + quoteName(@mincol) + ' is not null 
				)'
		END ELSE BEGIN
			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@mincol) + ' = '''' 
				WHERE rowID in (
					SELECT rowID
					FROM ' + @tmptbl + ' as importfile
					LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
						and mdcv.columnValueString = importfile.' + quoteName(@mincol) + ' 
					WHERE mdcv.valueID is null 
					AND importfile.' + quoteName(@mincol) + ' is not null 
				)'
		END
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and mdc.allowMultiple = 0
			and mdc.allowNewValuesOnImport = 0
			and dt.dataTypeCode = 'STRING'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking STRING columns (multiple values) for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 1
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'STRING'
		and mdc.columnname not in (select colName from @tblMissingCols)
while @mincol is not null BEGIN
	select @columnID = null, @allownull = null
	select @columnID = columnID, @allownull = allowNull from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	IF @allownull = 1 BEGIN
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = null where ' + quoteName(@mincol) + ' = '''''
		EXEC(@dynSQL)
	END

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		cross apply dbo.fn_varcharListToTable(' + quotename(@mincol) + ',''|'') as tbl
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and mdcv.columnValueString = tbl.listitem  
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
	
	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			cross apply dbo.fn_varcharListToTable(' + quotename(@mincol) + ',''|'') as tbl
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and mdcv.columnValueString = tbl.listitem  
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		IF @allownull = 1 BEGIN		
			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@mincol) + ' = null
				WHERE rowID in (
					SELECT rowID
					FROM ' + @tmptbl + ' as importfile
					cross apply dbo.fn_varcharListToTable(' + quotename(@mincol) + ',''|'') as tbl
					LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
						and mdcv.columnValueString = tbl.listitem  
					WHERE mdcv.valueID is null 
					AND importfile.' + quoteName(@mincol) + ' is not null 
				)'
		END ELSE BEGIN
			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@mincol) + ' = '''' 
				WHERE rowID in (
					SELECT rowID
					FROM ' + @tmptbl + ' as importfile
					cross apply dbo.fn_varcharListToTable(' + quotename(@mincol) + ',''|'') as tbl
					LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
						and mdcv.columnValueString = tbl.listitem  
					WHERE mdcv.valueID is null 
					AND importfile.' + quoteName(@mincol) + ' is not null 
				)'
		END
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and mdc.allowMultiple = 1
			and mdc.allowNewValuesOnImport = 0
			and dt.dataTypeCode = 'STRING'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking DATE columns for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'DATE'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid dates.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and mdcv.columnValueDate = importfile.' + quoteName(@mincol) + ' 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and mdcv.columnValueDate = importfile.' + quoteName(@mincol) + ' 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
					and mdcv.columnValueDate = importfile.' + quoteName(@mincol) + ' 
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END
	
	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'DATE'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid dates.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking DECIMAL2 columns for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowMultiple = 0
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'DECIMAL2'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid decimal values.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and mdcv.columnValueDecimal2 = importfile.' + quoteName(@mincol) + ' 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg 
			FROM ' + @tmptbl + ' as importfile
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and mdcv.columnValueDecimal2 = importfile.' + quoteName(@mincol) + ' 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
					and mdcv.columnValueDecimal2 = importfile.' + quoteName(@mincol) + ' 
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END
	
	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 0
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'DECIMAL2'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid decimal values.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking DECIMAL2 columns (multiple values) for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowMultiple = 1
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'DECIMAL2'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid decimal values.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		cross apply dbo.fn_decimal2ListToTable(' + quotename(@mincol) + ',''|'') as tbl
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + ' 
			and mdcv.columnValueDecimal2 = tbl.listitem 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY importfile.rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
	
	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			cross apply dbo.fn_decimal2ListToTable(' + quotename(@mincol) + ',''|'') as tbl
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + ' 
				and mdcv.columnValueDecimal2 = tbl.listitem 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY importfile.rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				cross apply dbo.fn_decimal2ListToTable(' + quotename(@mincol) + ',''|'') as tbl
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + ' 
					and mdcv.columnValueDecimal2 = tbl.listitem 
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 1
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'DECIMAL2'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid decimal values.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking INTEGER columns for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowMultiple = 0
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'INTEGER'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid whole number values.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and mdcv.columnValueInteger = importfile.' + quoteName(@mincol) + ' 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg 
			FROM ' + @tmptbl + ' as importfile
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and mdcv.columnValueInteger = importfile.' + quoteName(@mincol) + ' 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
					and mdcv.columnValueInteger = importfile.' + quoteName(@mincol) + ' 
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 0
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'INTEGER'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid whole number values.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking INTEGER columns (multiple values) for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowMultiple = 1
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'INTEGER'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid whole number values.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		cross apply dbo.fn_intListToTable(' + quotename(@mincol) + ',''|'') as tbl
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + ' 
			and mdcv.columnValueInteger = tbl.listitem 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			cross apply dbo.fn_intListToTable(' + quotename(@mincol) + ',''|'') as tbl
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + ' 
				and mdcv.columnValueInteger = tbl.listitem 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				cross apply dbo.fn_intListToTable(' + quotename(@mincol) + ',''|'') as tbl
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + ' 
					and mdcv.columnValueInteger = tbl.listitem 
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 1
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'INTEGER'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid whole number values.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking BIT columns for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'BIT'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid boolean values.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and mdcv.columnValueBit = importfile.' + quoteName(@mincol) + ' 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and mdcv.columnValueBit = importfile.' + quoteName(@mincol) + ' 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
					and mdcv.columnValueBit = importfile.' + quoteName(@mincol) + ' 
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'BIT'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid boolean values.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking XML columns for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'XML'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid XML values.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and cast(mdcv.columnValueXML as varchar(max)) = cast(importfile.' + quoteName(@mincol) + ' as varchar(max))  
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and cast(mdcv.columnValueXML as varchar(max)) = cast(importfile.' + quoteName(@mincol) + ' as varchar(max))  
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
					and cast(mdcv.columnValueXML as varchar(max)) = cast(importfile.' + quoteName(@mincol) + ' as varchar(max))  
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'XML'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid XML values.')
		and mdc.columnName > @mincol
END


-- cleanup - these are no longer needed
IF OBJECT_ID('tempdb..#tblOrgCols') IS NOT NULL
	DROP TABLE #tblOrgCols
IF OBJECT_ID('tempdb..#tblImportCols') IS NOT NULL
	DROP TABLE #tblImportCols

-- ******************************** 
-- dump flat table and format file and creation script
-- would love to use xml format files, but it appears column names with spaces cause it to fail
-- ******************************** 
IF NOT EXISTS (select rowID from @tblErrors where fatal = 1) BEGIN
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Backup Flattened Data'

	SELECT @prefix = @orgcode + '_flat_' + convert(varchar(8),getdate(),112) + replace(convert(varchar(8),getdate(),108),':','')
	SELECT @flatfile = @pathToExport + @prefix

	select @exportcmd = 'bcp ' + @tmptbl + ' format nul -f ' + @flatfile + '.txt -n -T -S' + CAST(serverproperty('servername') as varchar(40))
	EXEC master..xp_cmdshell @exportcmd, NO_OUTPUT

	select @exportcmd = 'bcp ' + @tmptbl + ' out ' + @flatfile + '.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
	EXEC master..xp_cmdshell @exportcmd, NO_OUTPUT

	declare @createTableSQLTop varchar(100)          
	declare @coltypes table (datatype varchar(16))          
	insert into @coltypes values('bit')          
	insert into @coltypes values('binary')          
	insert into @coltypes values('bigint')          
	insert into @coltypes values('int')          
	insert into @coltypes values('float')          
	insert into @coltypes values('datetime')          
	insert into @coltypes values('text')          
	insert into @coltypes values('image')          
	insert into @coltypes values('money')          
	insert into @coltypes values('uniqueidentifier')          
	insert into @coltypes values('smalldatetime')          
	insert into @coltypes values('tinyint')          
	insert into @coltypes values('smallint')          
	insert into @coltypes values('sql_variant')          
	select @dynSQL = ''
	select @dynSQL = @dynSQL +           
		case when charindex('(',@dynSQL,1)<=0 then '(' else '' end + '[' + Column_Name + '] ' +Data_Type +
		case when Data_Type in (Select datatype from @coltypes) then '' else  '(' end+
		case when data_type in ('real','decimal','numeric')  then cast(isnull(numeric_precision,'') as varchar)+','+
		case when data_type in ('real','decimal','numeric') then cast(isnull(Numeric_Scale,'') as varchar) end
		when data_type in ('nvarchar','varchar') and cast(isnull(Character_Maximum_Length,'') as varchar) = '-1' then 'max'
		when data_type in ('char','nvarchar','varchar','nchar') then cast(isnull(Character_Maximum_Length,'') as varchar) else '' end+
		case when Data_Type in (Select datatype from @coltypes)then '' else  ')' end+
		case when Is_Nullable='No' then ' Not null,' else ' null,' end
		from tempdb.Information_Schema.COLUMNS where Table_Name=@tmptbl
		order by ordinal_position
	select @createTableSQLTop = 'Create table ##xxx ' 
	select @dynSQL=@createTableSQLTop + substring(@dynSQL,1,len(@dynSQL)-1) +' )'            
	if dbo.fn_WriteFile(@pathToExport + @prefix + '.sql', @dynSQL, 1) = 0 BEGIN
		EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Failed writing table creation script'
		RETURN 0
	END

	-- create index on temp table for numbers below
	EXEC('CREATE NONCLUSTERED INDEX [idx_' + @tmptbl + '_memnum_rowid] ON ' + @tmptbl + '([membernumber] ASC,[rowID] ASC)')
END ELSE BEGIN
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Backup Flattened Data Skipped - Fatal Errors'
END

-- ******************************** 
-- Counts
-- ******************************** 
IF NOT EXISTS (select rowID from @tblErrors where fatal = 1) BEGIN
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Querying Intermediate Totals'

	-- TotalCurrentMembers
	INSERT INTO @tblCounts (countName, countNum)
	SELECT 'TotalCurrentMembers', COUNT(memberid)
	FROM dbo.ams_members
	WHERE orgID = @orgID
	AND status <> 'D'
	and memberTypeID = 2
	AND memberID = activeMemberID

	-- TotalCurrentActive
	INSERT INTO @tblCounts (countName, countNum)
	SELECT 'TotalCurrentActive', COUNT(memberid)
	FROM dbo.ams_members
	WHERE orgID = @orgID
	AND status = 'A'
	and memberTypeID = 2
	AND memberID = activeMemberID

	-- TotalCurrentInActive
	INSERT INTO @tblCounts (countName, countNum)
	SELECT 'TotalCurrentInActive', COUNT(memberid)
	FROM dbo.ams_members
	WHERE orgID = @orgID
	AND status = 'I'
	and memberTypeID = 2
	AND memberID = activeMemberID

	-- TotalNewImported
	select @dynSQL = 'SELECT ''TotalNewImported'', Count(newlist.rowID)
		FROM ' + @tmptbl + ' as newlist
		LEFT OUTER JOIN dbo.ams_members as m on m.memberNumber = newlist.memberNumber
			AND m.orgID = ' + cast(@orgID as varchar(7)) + ' 
			and m.memberID = m.activeMemberID
			and m.memberTypeID = 2
		WHERE m.memberID is null'
	INSERT INTO @tblCounts (countName, countNum)
	EXEC(@dynSQL)
	
	-- TotalCurrentMembersWillActivated
	select @dynSQL = 'SELECT ''TotalCurrentMembersWillBeActivated'', COUNT(distinct m.memberID)
		FROM dbo.ams_members as m
		INNER JOIN ' + @tmptbl + ' as newlist on newlist.memberNumber = m.memberNumber 
			AND m.orgID = ' + cast(@orgID as varchar(7)) + ' 
			AND m.status = ''I''
			and m.memberID = m.activeMemberID
			and m.memberTypeID = 2'
	INSERT INTO @tblCounts (countName, countNum)
	EXEC(@dynSQL)

	-- TotalCurrentMembersWillBeInactivated
	select @dynSQL = 'SELECT ''TotalCurrentMembersWillBeInactivated'', COUNT(distinct m.memberID)
		FROM dbo.organizations as o
		INNER JOIN dbo.ams_members as m on m.orgID = o.orgID
			AND m.orgID = ' + cast(@orgID as varchar(7)) + ' 
			AND m.status = ''A''
			and m.memberID = m.activeMemberID
			and m.memberTypeID = 2
		LEFT OUTER JOIN ' + @tmptbl + ' as newlist on newlist.memberNumber = m.memberNumber 
		WHERE newlist.rowID is null'
	INSERT INTO @tblCounts (countName, countNum)
	EXEC(@dynSQL)

	-- TotalMembersImported
	select @dynSQL = 'SELECT ''TotalMembersImported'', COUNT(rowID)
		FROM ' + @tmptbl
	INSERT INTO @tblCounts (countName, countNum)
	EXEC(@dynSQL)

END ELSE BEGIN
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Querying Intermediate Totals Skipped - Fatal Errors'
END

-- ******************************** 
-- Member Lists
-- ******************************** 
IF NOT EXISTS (select rowID from @tblErrors where fatal = 1) BEGIN
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Querying Member Lists'

	-- members that will be activated 
	select @dynSQL = 'SELECT TOP 100 PERCENT m.memberID, m.memberNumber, m.firstname, m.lastname
		FROM dbo.ams_members as m WITH(NOLOCK)
		INNER JOIN ' + @tmptbl + ' as newlist on newlist.memberNumber = m.memberNumber 
			AND m.orgID = ' + cast(@orgID as varchar(7)) + '
			AND m.status = ''I''
			and m.memberID = m.activeMemberID
			and m.memberTypeID = 2
		ORDER BY newlist.rowID'
	INSERT INTO @tblMemList1 (memberid, memberNumber, firstname, lastname)
	EXEC(@dynSQL)

	-- members that will be inactivated
	select @dynSQL = 'SELECT TOP 100 PERCENT  m.memberID, m.memberNumber, m.firstname, m.lastname
		FROM dbo.organizations as o
		INNER JOIN dbo.ams_members as m on m.orgID = o.orgID
			AND m.orgID = ' + cast(@orgID as varchar(7)) + '
			AND m.status = ''A''
			and m.memberID = m.activeMemberID
			and m.memberTypeID = 2
		LEFT OUTER JOIN ' + @tmptbl + ' as newlist on newlist.memberNumber = m.memberNumber 
		WHERE newlist.rowID is null
		ORDER BY m.memberID'
	INSERT INTO @tblMemList2 (memberid, memberNumber, firstname, lastname)
	EXEC(@dynSQL)

	-- members that will be added
	select @dynSQL = 'SELECT TOP 100 PERCENT newlist.rowID, newlist.memberNumber, newlist.firstname, newlist.lastname
		FROM ' + @tmptbl + ' as newlist
		LEFT OUTER JOIN dbo.ams_members as m on m.memberNumber = newlist.memberNumber
			AND m.orgID = ' + cast(@orgID as varchar(7)) + '
			and m.memberID = m.activeMemberID
			and m.memberTypeID = 2
		WHERE m.memberID is null
		ORDER BY newlist.rowID'
	INSERT INTO @tblMemList3 (rowID, memberNumber, firstname, lastname)
	EXEC(@dynSQL)

END ELSE BEGIN
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Querying Member Lists Skipped - Fatal Errors'
END

-- ********************************
-- generate result xml file 
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Generating XML response'
select @importResult = (
	select getdate() as "@date", @flatfile as "@flatfile",
		isnull((select top 301 dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg", "@severity" = case fatal when 1 then 'fatal' else 'nonfatal' end
		from @tblErrors
		order by rowid
		FOR XML path('error'), root('errors'), type),'<errors/>'),

		isnull((select countName as "@name", countNum as "@num"
		from @tblCounts
		order by rowid
		FOR XML path('count'), root('counts'), type),'<counts/>'),

		isnull((select top 301 memberid as "@memberid", membernumber as "@membernumber", firstname as "@firstname", lastname as "@lastname"
		from @tblMemList1
		order by memberid
		FOR XML path('member'), root('qryactivated'), type),'<qryactivated/>'),

		isnull((select top 301 memberid as "@memberid", membernumber as "@membernumber", firstname as "@firstname", lastname as "@lastname"
		from @tblMemList2
		order by memberid
		FOR XML path('member'), root('qryinactivated'), type),'<qryinactivated/>'),

		isnull((select top 301 rowid as "@rowid", membernumber as "@membernumber", firstname as "@firstname", lastname as "@lastname"
		from @tblMemList3
		order by rowid
		FOR XML path('member'), root('qryadded'), type),'<qryadded/>'),

		isnull((select top 301 dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
		from @tblDataChanged
		order by rowid
		FOR XML path('change'), root('qrychanges'), type),'<qrychanges/>')
	for xml path('import'), TYPE)

-- drop temp tables 
IF OBJECT_ID('tempdb..' + @tmptbl) IS NOT NULL
	EXEC('DROP TABLE ' + @tmptbl)

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Import to Holding Complete'

RETURN 0
GO

ALTER PROC [dbo].[ams_importMemberData_holdingToPerm]
@orgID int,
@flatfile varchar(160)

AS

SET NOCOUNT ON

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Starting importMemberData_toPerm'

declare @stepNum tinyint
declare @returnVal int
declare @cmd varchar(400), @sql varchar(max), @cmd2 varchar(8000), @createSQL varchar(max)
declare @logTable TABLE (stepNum tinyint, stepMsg varchar(100), stepDate datetime)
DECLARE @orgCode varchar(10), @hasPrefix bit, @hasMiddleName bit, @hasSuffix bit, @hasProfessionalSuffix bit, @hasCompany bit
select @orgcode=orgcode, @hasPrefix=hasPrefix, @hasMiddleName=hasMiddleName,
	@hasSuffix=hasSuffix, @hasProfessionalSuffix=hasProfessionalSuffix, 
	@hasCompany=hasCompany
	from dbo.organizations 
	where orgID = @orgid

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Preparing to Import Flattened Data'

-- see if files exist
if dbo.fn_fileExists(@flatfile + '.sql') = 0
	or dbo.fn_fileExists(@flatfile + '.txt') = 0
	or dbo.fn_fileExists(@flatfile + '.bcp') = 0
	BEGIN
		EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Flattened Data files do not exist'
		RETURN -1
	END

-- **************
-- read in sql create script and create global temp table
-- **************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Creating table for flattened data'
select @createSQL = replace(dbo.fn_ReadFile(@flatfile + '.sql',0,1),'##xxx','##importMemberData')
IF OBJECT_ID('tempdb..##importMemberData') IS NOT NULL
	EXEC('DROP TABLE ##importMemberData')
EXEC(@createSQL)

-- *******************
-- bcp in data
-- *******************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Importing flattened data'
select @cmd = 'bcp ##importMemberData in ' + @flatfile + '.bcp -f ' + @flatfile + '.txt -n -T -S' + CAST(serverproperty('servername') as varchar(40))
exec master..xp_cmdshell @cmd, NO_OUTPUT

-- *******************
-- immediately put into local temp table and drop global temp (memberid is a holder to be updated later)
-- *******************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Importing into local temp table'
select 0 as memberID, * into #importMemberData from ##importMemberData
IF OBJECT_ID('tempdb..##importMemberData') IS NOT NULL
	EXEC('DROP TABLE ##importMemberData')

-- *******************
-- drop all columns that should be skipped upon import
-- *******************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Dropping Columns to be Skipped'
select @sql = null
select @sql = COALESCE(@sql + char(10), '') + 'IF EXISTS (select * from tempdb.sys.columns where name = ''' + columnName + ''' and object_id = object_id(''tempdb..#importMemberData'')) ALTER TABLE #importMemberData DROP COLUMN [' + columnName + ']'
	from dbo.ams_memberDataColumns
	where orgID = @orgID
	and skipImport = 1
IF LEN(@sql) > 0 
	EXEC(@sql)

-- *******************
-- add back the columns we dont use from members (sigh) so the queries dont break
-- *******************
IF @hasPrefix = 0 AND NOT EXISTS (select [name] from tempdb.sys.columns where name = 'prefix' and object_id = object_id('tempdb..#importMemberData')) 
	ALTER TABLE #importMemberData ADD prefix varchar(4) NOT NULL DEFAULT('');
IF @hasMiddleName = 0 AND NOT EXISTS (select [name] from tempdb.sys.columns where name = 'middlename' and object_id = object_id('tempdb..#importMemberData')) 
	ALTER TABLE #importMemberData ADD middlename varchar(4) NOT NULL DEFAULT('');
IF @hasSuffix = 0 AND NOT EXISTS (select [name] from tempdb.sys.columns where name = 'suffix' and object_id = object_id('tempdb..#importMemberData')) 
	ALTER TABLE #importMemberData ADD suffix varchar(4) NOT NULL DEFAULT('');
IF @hasProfessionalSuffix = 0 AND NOT EXISTS (select [name] from tempdb.sys.columns where name = 'ProfessionalSuffix' and object_id = object_id('tempdb..#importMemberData')) 
	ALTER TABLE #importMemberData ADD ProfessionalSuffix varchar(4) NOT NULL DEFAULT('');
IF @hasCompany = 0 AND NOT EXISTS (select [name] from tempdb.sys.columns where name = 'Company' and object_id = object_id('tempdb..#importMemberData')) 
	ALTER TABLE #importMemberData ADD Company varchar(4) NOT NULL DEFAULT('');

-- ensure no null values in data. saves us from having to blank if null inline.
update #importMemberData set prefix = '' where prefix is null
update #importMemberData set middlename = '' where middlename is null
update #importMemberData set suffix = '' where suffix is null
update #importMemberData set professionalsuffix = '' where professionalsuffix is null
update #importMemberData set company = '' where company is null

-- Add index for queries below
EXEC('CREATE NONCLUSTERED INDEX [idx_importMemberData_' + @orgcode + '] ON #importMemberData ([membernumber] ASC) INCLUDE ( [prefix],[firstname],[middlename],[lastname],[suffix],[company],[ProfessionalSuffix]); ')
EXEC('CREATE STATISTICS [stat_importMemberData_' + @orgcode + '] ON #importMemberData([prefix]); ')

-- *******************
-- bcp out copy of current data 
-- have to do it this way since bcp cannot queryout dynamic sql queries
-- *******************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Exporting backup of existing data'
EXEC dbo.ams_getFlattenedMemberDataSQL @orgID=@orgID, @limitTop=0, @limitActive=0, @limitMID='', @showMCFields=1, @showSkippedFields=1, @sql=@sql OUTPUT
select @cmd2 = 'bcp "SET QUOTED_IDENTIFIER ON; ' + dbo.fn_ConsolidateWhiteSpace(@sql) + '" queryout ' + replace(@flatfile,'_flat_','_export_') + '.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
exec master..xp_cmdshell @cmd2, NO_OUTPUT

-- *******************
-- process data
-- *******************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Processing flattened data'
SELECT @stepNum = max(stepNum)
	from datatransfer.dbo.ams_memberDataImportStatus
	where orgID = @orgID

BEGIN TRAN

	-- Inactivate all non-guest members (the import contains only active people)
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Inactivate non-guest accounts',getdate())

	UPDATE dbo.ams_members
	SET [status] = 'I'
	WHERE orgID = @orgID
	AND membertypeID = 2
	AND memberID = activeMemberID
	AND [status] = 'A'
		IF @@ERROR <> 0 GOTO on_error

	-- update non-guest members already in table based on membernumber
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Update existing non-guest accounts',getdate())

	UPDATE m
	set m.prefix = flat.prefix,
		m.firstname = flat.firstname,
		m.middlename = flat.middlename,
		m.lastname = flat.lastname,
		m.suffix = flat.suffix,
		m.professionalsuffix = flat.professionalsuffix,
		m.company = flat.company,
		m.memberNumber = flat.membernumber,
		m.status = 'A',
		m.billingAddressTypeID = mat.addressTypeID
	FROM dbo.ams_members as m 
	INNER JOIN #importMemberData as flat on flat.memberNumber = m.memberNumber
		AND m.orgID = @orgID
		AND m.membertypeID = 2
		AND m.memberID = m.activeMemberID
		AND m.status <> 'D'
	INNER JOIN dbo.ams_memberAddressTypes as mat on mat.orgID = @orgID and mat.addressType = flat.BillingAddressType
		IF @@ERROR <> 0 GOTO on_error

	-- add new non-guest members
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Inserting new non-guest accounts',getdate())

	INSERT INTO dbo.ams_members (orgID, prefix, firstname, middlename, lastname, suffix, professionalsuffix, company, memberNumber, [status], dateCreated, membertypeID, dateLastUpdated, billingAddressTypeID)
	SELECT @orgID, flat.prefix, flat.Firstname, flat.middlename, flat.LastName, flat.suffix, flat.professionalsuffix, flat.company, flat.membernumber, 'A', getdate(), 2, getdate(), mat.addressTypeID
	from #importMemberData as flat
	INNER JOIN dbo.ams_memberAddressTypes as mat on mat.orgID = @orgID and mat.addressType = flat.BillingAddressType
	WHERE NOT EXISTS(
		select memberNumber 
		from dbo.ams_members as m 
		where m.memberNumber = flat.memberNumber 
		and m.orgID = @orgID
		and m.memberid = m.activeMemberID 
		and m.membertypeID = 2
		AND m.status <> 'D'
	)
	ORDER BY flat.membernumber
		IF @@ERROR <> 0 GOTO on_error

	UPDATE dbo.ams_members 
	SET activeMemberID = memberID 
	WHERE orgid=@orgid 
	and activeMemberID is null 
		IF @@ERROR <> 0 GOTO on_error

	-- ************
	-- update #importMemberData with memberid for easier reference later
	-- ************
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating flat file with memberid',getdate())

	update flat
	set flat.memberID = m.memberID
	from #importMemberData as flat
	inner join dbo.ams_members as m on m.membernumber = flat.membernumber 
		and m.orgid = @orgid
		and m.memberid = m.activememberid
		and m.memberTypeID = 2
		AND m.status <> 'D'
	IF @@ERROR <> 0 GOTO on_error

	-- *******
	-- websites
	-- loop over website types
	-- update website if it exists for member
	-- add website if it doesnt already exist for member
	-- *******
	declare @minWTID int, @minWT varchar(20), @wtsql varchar(max)
	select @minWTID = min(websiteTypeID) from dbo.ams_memberWebsiteTypes where orgID = @orgID
	while @minWTID is not null BEGIN
		select @minWT = websiteType from dbo.ams_memberWebsiteTypes where websiteTypeID = @minWTID

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Updating existing member websites',getdate())

		select @wtsql = ''
		select @wtsql = @wtsql + 'UPDATE mw '
		select @wtsql = @wtsql + 'SET mw.website = isnull(flat.[' + @minWT + '],'''') '
		select @wtsql = @wtsql + 'FROM dbo.ams_memberWebsites as mw '
		select @wtsql = @wtsql + 'inner join dbo.ams_members as m on m.memberID = mw.memberID '
		select @wtsql = @wtsql + '	AND m.orgID = ' + cast(@orgid as varchar(10)) + ' '
		select @wtsql = @wtsql + '	AND m.memberID = m.activeMemberID '
		select @wtsql = @wtsql + '	AND m.membertypeID = 2 '
		select @wtsql = @wtsql + '	AND m.status <> ''D'' '
		select @wtsql = @wtsql + '  AND mw.websiteTypeID = ' + cast(@minWTID as varchar(10)) + ' '
		select @wtsql = @wtsql + 'INNER JOIN #importMemberData as flat on flat.memberNumber = m.memberNumber '
		EXEC(@wtsql)
		IF @@ERROR <> 0 GOTO on_error

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Inserting new member websites',getdate())

		select @wtsql = ''
		select @wtsql = @wtsql + 'INSERT INTO dbo.ams_memberWebsites (memberID, websiteTypeID, website) '
		select @wtsql = @wtsql + 'select m.memberid, ' + cast(@minWTID as varchar(10)) + ', isnull(flat.[' + @minWT + '],'''') '
		select @wtsql = @wtsql + 'from #importMemberData as flat '
		select @wtsql = @wtsql + 'inner join dbo.ams_members as m on m.membernumber = flat.membernumber '
		select @wtsql = @wtsql + '	and m.orgid = ' + cast(@orgid as varchar(10)) + ' '
		select @wtsql = @wtsql + '	and m.memberID = m.activeMemberID '
		select @wtsql = @wtsql + '	and m.membertypeID = 2 '
		select @wtsql = @wtsql + '	AND m.status <> ''D'' '
		select @wtsql = @wtsql + 'WHERE NOT EXISTS( '
		select @wtsql = @wtsql + '	select websiteID '
		select @wtsql = @wtsql + '	from dbo.ams_memberWebsites as mw '
		select @wtsql = @wtsql + '	where mw.websiteTypeID = ' + cast(@minWTID as varchar(10)) + ' '
		select @wtsql = @wtsql + '	and mw.memberID = m.memberID '
		select @wtsql = @wtsql + ') '
		EXEC(@wtsql)
		IF @@ERROR <> 0 GOTO on_error

		select @minWTID = min(websiteTypeID) from dbo.ams_memberWebsiteTypes where orgID = @orgID and websiteTypeID > @minWTID
	END

	-- *******
	-- emails
	-- loop over email types
	-- update email if it exists for member
	-- add email if it doesnt already exist for member
	-- *******
	declare @minETID int, @minET varchar(20), @etsql varchar(max)
	select @minETID = min(emailTypeID) from dbo.ams_memberEmailTypes where orgID = @orgID
	while @minETID is not null BEGIN
		select @minET = emailType from dbo.ams_memberEmailTypes where emailTypeID = @minETID

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Updating existing member emails',getdate())

		select @etsql = ''
		select @etsql = @etsql + 'UPDATE me '
		select @etsql = @etsql + 'SET me.email = isnull(flat.[' + @minET + '],'''') '
		select @etsql = @etsql + 'FROM dbo.ams_memberEmails as me '
		select @etsql = @etsql + 'inner join dbo.ams_members as m on m.memberID = me.memberID '
		select @etsql = @etsql + '	AND m.orgID = ' + cast(@orgid as varchar(10)) + ' '
		select @etsql = @etsql + '	AND m.memberID = m.activeMemberID '
		select @etsql = @etsql + '	AND m.membertypeID = 2 '
		select @etsql = @etsql + '	AND m.status <> ''D'' '
		select @etsql = @etsql + '  AND me.emailTypeID = ' + cast(@minETID as varchar(10)) + ' '
		select @etsql = @etsql + 'INNER JOIN #importMemberData as flat on flat.memberNumber = m.memberNumber '
		EXEC(@etsql)
		IF @@ERROR <> 0 GOTO on_error

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Inserting new member emails',getdate())

		select @etsql = ''
		select @etsql = @etsql + 'INSERT INTO dbo.ams_memberEmails (memberID, emailTypeID, email) '
		select @etsql = @etsql + 'select m.memberid, ' + cast(@minETID as varchar(10)) + ', isnull(flat.[' + @minET + '],'''') '
		select @etsql = @etsql + 'from #importMemberData as flat '
		select @etsql = @etsql + 'inner join dbo.ams_members as m on m.membernumber = flat.membernumber '
		select @etsql = @etsql + '	and m.orgid = ' + cast(@orgid as varchar(10)) + ' '
		select @etsql = @etsql + '	and m.memberID = m.activeMemberID '
		select @etsql = @etsql + '	and m.membertypeID = 2 '
		select @etsql = @etsql + '	AND m.status <> ''D'' '
		select @etsql = @etsql + 'WHERE NOT EXISTS( '
		select @etsql = @etsql + '	select emailID '
		select @etsql = @etsql + '	from dbo.ams_memberEmails as me '
		select @etsql = @etsql + '	where me.emailTypeID = ' + cast(@minETID as varchar(10)) + ' '
		select @etsql = @etsql + '	and me.memberID = m.memberID '
		select @etsql = @etsql + ') '
		EXEC(@etsql)
		IF @@ERROR <> 0 GOTO on_error

		select @minETID = min(emailTypeID) from dbo.ams_memberEmailTypes where orgID = @orgID and emailTypeID > @minETID
	END

	-- *******
	-- addresses and phones
	-- loop over address types (and then phone types)
	-- update address/phone if it exists for member
	-- add address/phone if it doesnt already exist for member
	-- *******
	declare @minATID int, @minAT varchar(20), @atsql varchar(max), @minPTID int, @minPT varchar(20)
	declare @hasAttn bit, @hasAddress2 bit, @hasAddress3 bit, @hasCounty bit
	declare @updateAddressDate varchar(25)
	select @minATID = min(addressTypeID) from dbo.ams_memberAddressTypes where orgID = @orgID
	while @minATID is not null BEGIN
		select @minAT=addressType, @hasAttn=hasAttn, @hasAddress2=hasAddress2, @hasAddress3=hasAddress3, @hasCounty=hasCounty
			from dbo.ams_memberAddressTypes 
			where addressTypeID = @minATID

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Updating existing member addresses',getdate())

		select @updateAddressDate = convert(varchar(30),getdate(),21)

		select @atsql = ''
		select @atsql = @atsql + 'UPDATE ma '
		select @atsql = @atsql + 'SET '
		IF @hasAttn = 1
			select @atsql = @atsql + '  ma.attn = isnull(flat.[' + @minAT + '_attn],''''), '
		select @atsql = @atsql + '	ma.address1 = isnull(flat.[' + @minAT + '_address1],''''), '
		IF @hasAddress2 = 1		
			select @atsql = @atsql + '	ma.address2 = isnull(flat.[' + @minAT + '_address2],''''), '
		IF @hasAddress3 = 1		
			select @atsql = @atsql + '	ma.address3 = isnull(flat.[' + @minAT + '_address3],''''), '
		select @atsql = @atsql + '	ma.city = isnull(flat.[' + @minAT + '_city],''''), '
		select @atsql = @atsql + '	ma.stateID = s.stateID, '
		select @atsql = @atsql + '	ma.postalCode = isnull(flat.[' + @minAT + '_postalcode],''''), '
		IF @hasCounty = 1		
			select @atsql = @atsql + '	ma.county = isnull(flat.[' + @minAT + '_county],''''), '
		select @atsql = @atsql + '	ma.countryID = c.countryID, '
		select @atsql = @atsql + '	ma.dateLastUpdated = ''' + @updateAddressDate + ''' '
		select @atsql = @atsql + 'FROM dbo.ams_memberAddresses as ma '
		select @atsql = @atsql + 'inner join dbo.ams_members as m on m.memberID = ma.memberID '
		select @atsql = @atsql + '	AND m.orgID = ' + cast(@orgid as varchar(10)) + ' '
		select @atsql = @atsql + '	AND m.memberID = m.activeMemberID '
		select @atsql = @atsql + '	AND m.membertypeID = 2 '
		select @atsql = @atsql + '	AND m.status <> ''D'' '
		select @atsql = @atsql + '  AND ma.addressTypeID = ' + cast(@minATID as varchar(10)) + ' '
		select @atsql = @atsql + 'INNER JOIN #importMemberData as flat on flat.memberNumber = m.memberNumber '
		select @atsql = @atsql + 'left outer join dbo.ams_countries as c on c.country = isnull(flat.[' + @minAT + '_country],'''') '
		select @atsql = @atsql + 'left outer join dbo.ams_states as s on s.code = isnull(flat.[' + @minAT + '_stateprov],'''') and s.countryID = c.countryID '
		select @atsql = @atsql + 'WHERE NOT EXISTS ( '
		select @atsql = @atsql + '		SELECT addressID '
		select @atsql = @atsql + '		FROM dbo.ams_memberAddresses '
		select @atsql = @atsql + '		where addressID = ma.addressID '
		IF @hasAttn = 1
			select @atsql = @atsql + '		AND IsNull(attn,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_attn],'''') '
		select @atsql = @atsql + '		AND IsNull(address1,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_address1],'''') '
		IF @hasAddress2 = 1		
			select @atsql = @atsql + '		AND IsNull(address2,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_address2],'''') '
		IF @hasAddress3 = 1		
			select @atsql = @atsql + '		AND IsNull(address3,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_address3],'''') '
		select @atsql = @atsql + '		AND IsNull(city,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_city],'''') '
		select @atsql = @atsql + '		AND IsNull(stateID,-1) = IsNull(s.stateID,-1) '
		select @atsql = @atsql + '		AND IsNull(postalCode,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_postalcode],'''') '
		IF @hasCounty = 1		
			select @atsql = @atsql + '		AND IsNull(county,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_county],'''') '
		select @atsql = @atsql + '		AND IsNull(countryID,-1) = IsNull(c.countryID,-1) '
		select @atsql = @atsql + ') '
		EXEC(@atsql)
		IF @@ERROR <> 0 GOTO on_error
		
		-- clear address data for addresses we just updated
		delete from dbo.ams_memberAddressData
		where addressID in (
			select ma.addressID
			from dbo.ams_memberAddresses as ma
			inner join dbo.ams_members as m on m.memberID = ma.memberID
			where m.orgID = @orgID
			and ma.dateLastUpdated = @updateAddressDate
		)
		IF @@ERROR <> 0 GOTO on_error

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Inserting new member addresses',getdate())

		select @atsql = ''
		select @atsql = @atsql + 'INSERT INTO dbo.ams_memberAddresses (memberID, addressTypeID, attn, address1, address2, address3, city, stateID, postalCode, county, countryID) '
		select @atsql = @atsql + 'select m.memberid, ' + cast(@minATID as varchar(10)) + ', ' + case when @hasAttn = 1 then 'isnull(flat.[' + @minAT + '_attn],''''), ' else ''''', ' end + 'isnull(flat.[' + @minAT + '_address1],''''), ' + case when @hasAddress2 = 1 then 'isnull(flat.[' + @minAT + '_address2],''''), ' else ''''', ' end + case when @hasAddress3 = 1 then 'isnull(flat.[' + @minAT + '_address3],''''), ' else ''''', ' end + 'isnull(flat.[' + @minAT + '_city],''''), s.stateID, isnull(flat.[' + @minAT + '_postalcode],''''), ' + case when @hasCounty = 1 then 'isnull(flat.[' + @minAT + '_county],''''), ' else ''''', ' end + 'c.countryID '
		select @atsql = @atsql + 'from #importMemberData as flat '
		select @atsql = @atsql + 'inner join dbo.ams_members as m on m.membernumber = flat.membernumber '
		select @atsql = @atsql + '	and m.orgid = ' + cast(@orgid as varchar(10)) + ' '
		select @atsql = @atsql + '	and m.memberID = m.activeMemberID '
		select @atsql = @atsql + '	and m.membertypeID = 2 '
		select @atsql = @atsql + '	AND m.status <> ''D'' '
		select @atsql = @atsql + 'left outer join dbo.ams_countries as c on c.country = isnull(flat.[' + @minAT + '_country],'''') '
		select @atsql = @atsql + 'left outer join dbo.ams_states as s on s.code = isnull(flat.[' + @minAT + '_stateprov],'''') and s.countryID = c.countryID '
		select @atsql = @atsql + 'WHERE NOT EXISTS( '
		select @atsql = @atsql + '	select addressID '
		select @atsql = @atsql + '	from dbo.ams_memberAddresses as ma '
		select @atsql = @atsql + '	where ma.addressTypeID = ' + cast(@minATID as varchar(10)) + ' '
		select @atsql = @atsql + '	and ma.memberID = m.memberID '
		select @atsql = @atsql + ') '
		EXEC(@atsql)
		IF @@ERROR <> 0 GOTO on_error

		select @minPTID = min(phoneTypeID) from dbo.ams_memberPhoneTypes where orgID = @orgID
		while @minPTID is not null BEGIN
			select @minPT = phoneType from dbo.ams_memberPhoneTypes where phoneTypeID = @minPTID

			SELECT @stepNum = @stepNum + 1
			INSERT INTO @logTable (stepNum,stepMsg,stepDate)
			VALUES (@stepNum,'Updating existing member phones',getdate())

			select @atsql = ''
			select @atsql = @atsql + 'UPDATE mp '
			select @atsql = @atsql + 'SET phone = isnull(flat.[' + @minAT + '_' + @minPT + '],'''') '
			select @atsql = @atsql + 'FROM dbo.ams_memberPhones as mp '
			select @atsql = @atsql + 'inner join dbo.ams_memberAddresses as ma on ma.addressID = mp.addressid '
			select @atsql = @atsql + '	and ma.addressTypeID = ' + cast(@minATID as varchar(10)) + ' '
			select @atsql = @atsql + '  and mp.phoneTypeID = ' + cast(@minPTID as varchar(10)) + ' '
			select @atsql = @atsql + 'inner join dbo.ams_members as m on m.memberID = ma.memberID '
			select @atsql = @atsql + '	AND m.orgID = ' + cast(@orgid as varchar(10)) + ' '
			select @atsql = @atsql + '	AND m.memberID = m.activeMemberID '
			select @atsql = @atsql + '	AND m.membertypeID = 2 '
			select @atsql = @atsql + '	AND m.status <> ''D'' '
			select @atsql = @atsql + 'INNER JOIN #importMemberData as flat on flat.memberNumber = m.memberNumber '
			EXEC(@atsql)
			IF @@ERROR <> 0 GOTO on_error

			SELECT @stepNum = @stepNum + 1
			INSERT INTO @logTable (stepNum,stepMsg,stepDate)
			VALUES (@stepNum,'Inserting new member phones',getdate())

			select @atsql = ''
			select @atsql = @atsql + 'INSERT INTO dbo.ams_memberPhones (phoneTypeID, addressid, phone) '
			select @atsql = @atsql + 'select ' + cast(@minPTID as varchar(10)) + ', ma.addressid, isnull(flat.[' + @minAT + '_' + @minPT + '],'''') '
			select @atsql = @atsql + 'from #importMemberData as flat '
			select @atsql = @atsql + 'inner join dbo.ams_members as m on m.membernumber = flat.membernumber '
			select @atsql = @atsql + '	and m.orgid = ' + cast(@orgid as varchar(10)) + ' '
			select @atsql = @atsql + '	and m.memberID = m.activeMemberID '
			select @atsql = @atsql + '	and m.membertypeID = 2 '
			select @atsql = @atsql + '	AND m.status <> ''D'' '
			select @atsql = @atsql + 'inner join dbo.ams_memberAddresses as ma on ma.memberid = m.memberid '
			select @atsql = @atsql + '	and ma.addressTypeID = ' + cast(@minATID as varchar(10)) + ' '
			select @atsql = @atsql + 'WHERE NOT EXISTS( '
			select @atsql = @atsql + '	select phoneID '
			select @atsql = @atsql + '	from dbo.ams_memberPhones as mp '
			select @atsql = @atsql + '	where mp.phoneTypeID = ' + cast(@minPTID as varchar(10)) + ' '
			select @atsql = @atsql + '	and mp.addressID = ma.addressID '
			select @atsql = @atsql + ') '
			EXEC(@atsql)
			IF @@ERROR <> 0 GOTO on_error

			select @minPTID = min(phoneTypeID) from dbo.ams_memberPhoneTypes where orgID = @orgID and phoneTypeID > @minPTID
		END

		select @minATID = min(addressTypeID) from dbo.ams_memberAddressTypes where orgID = @orgID and addressTypeID > @minATID
	END

	-- *******
	-- professional licenses
	-- loop over prof license types
	-- update prof license if it exists for member
	-- add prof license if it doesnt already exist for member
	-- *******
	declare @minPLTID int, @minPLT varchar(200), @pltsql varchar(max)
	select @minPLTID = min(PLTypeID) from dbo.ams_memberProfessionalLicenseTypes where orgID = @orgID
	while @minPLTID is not null BEGIN
		select @minPLT=PLName
			from dbo.ams_memberProfessionalLicenseTypes 
			where PLTypeID = @minPLTID

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Updating existing member professional licenses',getdate())

		select @pltsql = ''
		select @pltsql = @pltsql + 'UPDATE mpl '
		select @pltsql = @pltsql + 'SET '
		select @pltsql = @pltsql + '	mpl.licenseNumber = isnull(flat.[' + @minPLT + '_licenseNumber],''''), '
		select @pltsql = @pltsql + '	mpl.activeDate = nullif(flat.[' + @minPLT + '_activeDate],''''), '
		select @pltsql = @pltsql + '	mpl.PLstatusID = pls.PLStatusID '
		select @pltsql = @pltsql + 'FROM dbo.ams_memberProfessionalLicenses as mpl '
		select @pltsql = @pltsql + 'inner join dbo.ams_members as m on m.memberID = mpl.memberID '
		select @pltsql = @pltsql + '	AND m.orgID = ' + cast(@orgid as varchar(10)) + ' '
		select @pltsql = @pltsql + '	AND m.memberID = m.activeMemberID '
		select @pltsql = @pltsql + '	AND m.membertypeID = 2 '
		select @pltsql = @pltsql + '	AND m.status <> ''D'' '
		select @pltsql = @pltsql + '  AND mpl.PLTypeID = ' + cast(@minPLTID as varchar(10)) + ' '
		select @pltsql = @pltsql + 'INNER JOIN #importMemberData as flat on flat.memberNumber = m.memberNumber '
		select @pltsql = @pltsql + 'LEFT OUTER JOIN dbo.ams_memberProfessionalLicenseStatuses as pls on pls.statusName = isnull(flat.[' + @minPLT + '_status],'''') and pls.orgID = ' + cast(@orgid as varchar(10))
		EXEC(@pltsql)
		IF @@ERROR <> 0 GOTO on_error
		
		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Inserting new member professional licenses',getdate())

		select @pltsql = ''
		select @pltsql = @pltsql + 'INSERT INTO dbo.ams_memberProfessionalLicenses (memberID, PLTypeID, LicenseNumber, ActiveDate, PLstatusID) '
		select @pltsql = @pltsql + 'select m.memberid, ' + cast(@minPLTID as varchar(10)) + ', isnull(flat.[' + @minPLT + '_licenseNumber],''''), nullif(flat.[' + @minPLT + '_activeDate],''''), pls.PLStatusID '
		select @pltsql = @pltsql + 'from #importMemberData as flat '
		select @pltsql = @pltsql + 'LEFT OUTER JOIN dbo.ams_memberProfessionalLicenseStatuses as pls on pls.statusName = isnull(flat.[' + @minPLT + '_status],'''') and pls.orgID = ' + cast(@orgid as varchar(10))
		select @pltsql = @pltsql + 'inner join dbo.ams_members as m on m.membernumber = flat.membernumber '
		select @pltsql = @pltsql + '	and m.orgid = ' + cast(@orgid as varchar(10)) + ' '
		select @pltsql = @pltsql + '	and m.memberID = m.activeMemberID '
		select @pltsql = @pltsql + '	and m.membertypeID = 2 '
		select @pltsql = @pltsql + '	AND m.status <> ''D'' '
		select @pltsql = @pltsql + 'WHERE NOT EXISTS( '
		select @pltsql = @pltsql + '	select PLID '
		select @pltsql = @pltsql + '	from dbo.ams_memberProfessionalLicenses as mpl '
		select @pltsql = @pltsql + '	where mpl.PLTypeID = ' + cast(@minPLTID as varchar(10)) + ' '
		select @pltsql = @pltsql + '	and mpl.memberID = m.memberID '
		select @pltsql = @pltsql + ')  '
		select @pltsql = @pltsql + 'AND ( '
		select @pltsql = @pltsql + '	nullIf(flat.[' + @minPLT + '_licenseNumber],'''') is not null OR '
		select @pltsql = @pltsql + '	nullif(flat.[' + @minPLT + '_activeDate],'''') is not null OR '
		select @pltsql = @pltsql + '	nullIf(flat.[' + @minPLT + '_status],'''') is not null '
		select @pltsql = @pltsql + ')  '

		EXEC(@pltsql)
		IF @@ERROR <> 0 GOTO on_error

		select @minPLTID = min(PLTypeID) from dbo.ams_memberProfessionalLicenseTypes where orgID = @orgID and PLTypeID > @minPLTID
	END

	-- delete licenses where everything is now null (could have been updated to null values above)
	delete
	from dbo.ams_memberProfessionalLicenses
	where nullif(licenseNumber,'') is null
	and activedate is null
	and PlStatusID is null
		IF @@ERROR <> 0 GOTO on_error
	
	-- ***********
	-- memberdata
	-- figure out what should be in memberdata
	-- delete what shouldnt be but is
	-- add what should be but isnt
	-- ***********
	
	-- clear memberdataholding for org
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Cleaning up holding table',getdate())

	DELETE FROM datatransfer.dbo.ams_memberDataHolding where orgID = @orgID
		IF @@ERROR <> 0 GOTO on_error

	-- traverse each column and put into holding table
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Inserting custom data into holding table',getdate())

	DECLARE @minCol varchar(255), @minColID int, @coldataTypeCode varchar(20), @colDisplayTypeCode varchar(20), @allowMultiple bit, @colqry varchar(max)
	SELECT @minColID = MIN(mdc.columnID) 
		FROM dbo.ams_memberDataColumns as mdc
		where mdc.orgID = @orgid
		and mdc.skipImport = 0
	WHILE @minColID is not null BEGIN
		select @minCol=mdc.columnName, @coldataTypeCode=dt.dataTypeCode, @allowMultiple=mdc.allowMultiple
			from dbo.ams_memberDataColumns as mdc
			inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			where mdc.columnID = @minColID
			IF @@ERROR <> 0 GOTO on_error

		IF EXISTS (select * from tempdb.sys.columns where name = @minCol and object_id = object_id('tempdb..#importMemberData')) BEGIN
			SELECT @colqry = 'insert into datatransfer.dbo.ams_memberDataHolding (orgID, rowID, columnID, ' + 
				case @coldataTypeCode 
				when 'STRING' then 'columnValueString'
				when 'DECIMAL2' then 'columnValueDecimal2'
				when 'INTEGER' then 'columnValueInteger'
				when 'DATE' then 'columnValueDate'
				when 'BIT' then 'columnValueBit'
				when 'XML' then 'columnValueXML'
				when 'CONTENTOBJ' then 'columnValueContent'
				when 'DOCUMENTOBJ' then 'columnValueSiteResourceID'
				else 'columnValueString'
				end + ') '
			SELECT @colqry = @colqry + 'select ' + cast(@orgID as varchar(6)) + ', rowID, ' + cast(@minColID as varchar(12)) + ', ' + 
				case when @allowMultiple = 1 then 'tbl.listitem'
				else 
					case @coldataTypeCode 
					when 'STRING' then quotename(@minCol)
					when 'DECIMAL2' then 'cast(' + quotename(@minCol) + ' as decimal(9,2))'
					when 'INTEGER' then 'cast(' + quotename(@minCol) + ' as int)'
					when 'DATE' then 'cast(' + quotename(@minCol) + ' as datetime)'
					when 'BIT' then 'cast(' + quotename(@minCol) + ' as bit)'
					when 'XML' then 'cast(' + quotename(@minCol) + ' as xml)'
					when 'CONTENTOBJ' then quotename(@minCol)
					when 'DOCUMENTOBJ' then 'cast(' + quotename(@minCol) + ' as int)'
					else quotename(@minCol)
					end
				end + ' '
			SELECT @colqry = @colqry + 'from #importMemberData '
			IF @allowMultiple = 1 BEGIN
				SELECT @colqry = @colqry + 'cross apply ' + 
					case @coldataTypeCode 
					when 'STRING' then 'dbo.fn_varcharListToTable(' + quotename(@minCol) + ',''|'') as tbl '
					when 'DECIMAL2' then 'dbo.fn_decimal2ListToTable(' + quotename(@minCol) + ',''|'') as tbl '
					when 'INTEGER' then 'dbo.fn_intListToTable(' + quotename(@minCol) + ',''|'') as tbl '
					end + ' '
			END
			SELECT @colqry = @colqry + 'where ' + 
				case @coldataTypeCode 
				when 'STRING' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(255)),'''') is not null'
				when 'DECIMAL2' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(20)),'''') is not null'
				when 'INTEGER' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(20)),'''') is not null'
				when 'DATE' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(10)),'''') is not null'
				when 'BIT' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(1)),'''') is not null'
				when 'XML' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(max)),'''') is not null'
				when 'CONTENTOBJ' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(max)),'''') is not null'
				when 'DOCUMENTOBJ' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(20)),'''') is not null'
				else 'nullif(cast(' + quotename(@minCol) + ' as varchar(255)),'''') is not null'
				end
			EXEC(@colqry)
			IF @@ERROR <> 0 GOTO on_error
		END
				
		SELECT @minColID = MIN(mdc.columnID) 
			FROM dbo.ams_memberDataColumns as mdc
			where mdc.orgID = @orgid
			and mdc.skipImport = 0
			and columnID > @minColID
	END

	-- handle the content object fields in the holding table
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Handling content object custom data in holding table',getdate())

	-- get first site created for the org. We will put content objects on this site.
	declare @siteID int, @isHTML bit, @dataID bigint, @rc int, @contentID int, @contentSiteResourceID int, @rawContent varchar(max)
	select TOP 1 @siteID=siteID from dbo.sites where orgID = @orgID order by siteID
		IF @@ERROR <> 0 GOTO on_error

	SELECT @minColID = null
	SELECT @minColID = MIN(mdc.columnID) 
		FROM dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgid
		and mdc.skipImport = 0
		and dt.dataTypeCode = 'CONTENTOBJ'
		and EXISTS (select * from tempdb.sys.columns where name = mdc.columnName and object_id = object_id('tempdb..#importMemberData'))
	WHILE @minColID is not null BEGIN
		select @colDisplayTypeCode=dt.displayTypeCode
			from dbo.ams_memberDataColumns as mdc
			inner join dbo.ams_memberDataColumnDisplayTypes as dt on dt.displayTypeID = mdc.displayTypeID
			where mdc.columnID = @minColID
			IF @@ERROR <> 0 GOTO on_error

		-- lookup any existing SiteResourceID
		update mdh
		set mdh.columnValueSiteResourceID = mdcv.columnValueSiteResourceID
		from #importMemberData as imp
		inner join datatransfer.dbo.ams_memberDataHolding as mdh on mdh.rowID = imp.rowID
			and mdh.orgID = @orgID
		inner join membercentral.dbo.ams_memberData as md WITH(NOLOCK) on md.memberid = imp.memberid
		inner join membercentral.dbo.ams_memberDataColumnValues as mdcv WITH(NOLOCK) on mdcv.valueID = md.valueID
			and mdcv.columnID = mdh.columnID
		where mdh.columnValueContent is not null
		and mdcv.columnValueSiteResourceID is not null
		and mdcv.columnID = @minColID
			IF @@ERROR <> 0 GOTO on_error

		IF @colDisplayTypeCode = 'HTMLCONTENT'
			SET @isHTML = 1
		ELSE
			SET @isHTML = 0

		-- update content objects for those that already have one
		select @dataID = null
		select @dataID = min(dataid)
			from datatransfer.dbo.ams_memberDataHolding
			where orgID = @orgID
			and columnValueSiteResourceID is not null
		while @dataID is not null BEGIN
			select @contentSiteResourceID = null, @rawContent = null, @contentID = null
			select @contentSiteResourceID = columnValueSiteResourceID, @rawContent = columnValueContent
				from datatransfer.dbo.ams_memberDataHolding
				where dataID = @dataID
				IF @@ERROR <> 0 GOTO on_error
			select @contentID = contentID
				from dbo.cms_content
				where siteResourceID = @contentSiteResourceID
				and siteID = @siteID

			if @contentID is not null BEGIN
				EXEC @rc = dbo.cms_updateContent @contentID=@contentID, @languageID=1, @isSSL=0, 
					@isHTML=@isHTML, @contentTitle='', @contentDesc='', @rawContent=@rawContent, @memberID=NULL
					IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
			end

			select @dataID = min(dataid)
				from datatransfer.dbo.ams_memberDataHolding
				where orgID = @orgID
				and columnValueSiteResourceID is not null
				and dataid > @dataID
		end

		-- create content objects for those that need it
		select @dataID = null
		select @dataID = min(dataid)
			from datatransfer.dbo.ams_memberDataHolding
			where orgID = @orgID
			and columnValueContent is not null
			and columnValueSiteResourceID is null
		while @dataID is not null BEGIN
			select @rawContent = null, @contentID = null, @contentSiteResourceID = null
			select @rawContent = columnValueContent
				from datatransfer.dbo.ams_memberDataHolding
				where dataID = @dataID
				IF @@ERROR <> 0 GOTO on_error

			EXEC @rc = dbo.cms_createContentField @siteID=@siteID, @isSSL=0, @isHTML=@isHTML, @isActive=1, 
				@languageID=1, @contentTitle='', @contentDesc='', @rawContent=@rawContent, 
				@contentID=@contentID OUTPUT, @contentSiteResourceID=@contentSiteResourceID OUTPUT
				IF @@ERROR <> 0 OR @RC <> 0 OR @contentID = 0 GOTO on_error

			UPDATE datatransfer.dbo.ams_memberDataHolding
			SET columnValueSiteResourceID = @contentSiteResourceID
			WHERE dataID = @dataID
				IF @@ERROR <> 0 GOTO on_error

			select @dataID = min(dataid)
				from datatransfer.dbo.ams_memberDataHolding
				where orgID = @orgID
				and columnValueContent is not null
				and columnValueSiteResourceID is null
				and dataID > @dataID
		end

		SELECT @minColID = MIN(mdc.columnID) 
			FROM dbo.ams_memberDataColumns as mdc
			inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			where mdc.orgID = @orgid
			and mdc.skipImport = 0
			and dt.dataTypeCode = 'CONTENTOBJ'
			and EXISTS (select * from tempdb.sys.columns where name = mdc.columnName and object_id = object_id('tempdb..#importMemberData'))
			and mdc.columnID > @minColID
	END

	-- remove empty values in holding table
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Removing empty values in holding table',getdate())

	DELETE FROM datatransfer.dbo.ams_memberDataHolding
	WHERE orgID = @orgID
	AND (columnValueString = '' or columnValueString is null)
	AND columnValueDecimal2 is null
	AND columnValueInteger is null
	AND columnValueDate is null
	AND columnValueBit is null
	AND columnValueXML is null
	AND columnValueSiteResourceID is null
	AND (columnValueContent = '' or columnValueContent is null)
		IF @@ERROR <> 0 GOTO on_error

	-- add new columnValues	
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Inserting new member data column values',getdate())

	INSERT INTO dbo.ams_memberDataColumnValues (columnID, columnValueString, columnValueDecimal2, columnValueInteger, columnValueDate, columnValueBit, columnValueXML, columnValueSiteResourceID)
	select distinct columnID, columnValueString, columnValueDecimal2, columnValueInteger, columnValueDate, columnValueBit, cast(columnValueXML as varchar(max)), columnValueSiteResourceID
	from datatransfer.dbo.ams_memberDataHolding
	where orgID = @orgid
		except
	select mdcv.columnID, mdcv.columnValueString, mdcv.columnValueDecimal2, mdcv.columnValueInteger, mdcv.columnValueDate, mdcv.columnValueBit, cast(mdcv.columnValueXML as varchar(max)), mdcv.columnValueSiteResourceID
	from dbo.ams_memberDataColumnValues as mdcv
	inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
		and mdc.orgID = @orgID
		IF @@ERROR <> 0 GOTO on_error

	-- update holding with valueIDs
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for string',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode = 'STRING'
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and mdcv.columnValueString = mdh.columnValueString
			IF @@ERROR <> 0 GOTO on_error

	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for decimal',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode = 'DECIMAL2'
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and mdcv.columnValueDecimal2 = mdh.columnValueDecimal2
			IF @@ERROR <> 0 GOTO on_error

	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for integer',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode = 'INTEGER'
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and mdcv.columnValueInteger = mdh.columnValueInteger
			IF @@ERROR <> 0 GOTO on_error

	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for date',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode = 'DATE'
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and mdcv.columnValueDate = mdh.columnValueDate
			IF @@ERROR <> 0 GOTO on_error

	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for bit',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode = 'BIT'
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and mdcv.columnValueBit = mdh.columnValueBit
			IF @@ERROR <> 0 GOTO on_error

	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for xml',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode = 'XML'
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and cast(mdcv.columnValueXML as varchar(max)) = cast(mdh.columnValueXML as varchar(max))
			IF @@ERROR <> 0 GOTO on_error

	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for SRID',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode IN ('CONTENTOBJ','DOCUMENTOBJ')
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and mdcv.columnValueSiteResourceID = mdh.columnValueSiteResourceID
			IF @@ERROR <> 0 GOTO on_error

	-- figure out what should be in memberdata
	-- include imported data and current data for columns that were skipped
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Figure out what should be in memberdata',getdate())

	CREATE TABLE #shouldBeMemberData (dataid int IDENTITY(1,1), memberid int, valueid int)
	INSERT INTO #shouldBeMemberData (memberid, valueID)
	SELECT distinct m.memberid, mdcv.valueid
	from #importMemberData as M
	inner join datatransfer.dbo.ams_memberDataHolding as mdh on mdh.rowid = M.rowid 
		and mdh.orgID = @orgID
	inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdh.columnID 
		and mdcv.valueID = mdh.valueID
		union
	select distinct md.memberid, md.valueid
	from dbo.ams_memberData as md
	inner join dbo.ams_members as m on m.memberid = md.memberID
		and m.orgID = @orgID
		and m.memberid = m.activeMemberID
	inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
	inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
		and mdc.skipImport = 1
		IF @@ERROR <> 0 GOTO on_error

	-- delete what shouldnt be but is
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Removing old custom data for members',getdate())

	DELETE FROM dbo.ams_memberData
	where dataid in (
		select md.dataid
		from dbo.ams_memberData as md
		inner join #importMemberData as flat on md.memberid = flat.memberid
		WHERE NOT EXISTS(
			select dataid 
			from #shouldBeMemberData as sb
			where sb.memberid = md.memberid
			and sb.valueid = md.valueid
		)
	)
		IF @@ERROR <> 0 GOTO on_error
	
	-- add what should be but isnt
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Inserting new custom data for members',getdate())

	INSERT INTO dbo.ams_memberData (memberID, valueID)
	select sb.memberid, sb.valueid
	from #shouldBeMemberData as sb
	WHERE NOT EXISTS(
		select dataid 
		from dbo.ams_memberData as md
		where md.memberid = sb.memberid
		and md.valueid = sb.valueid
	)
		IF @@ERROR <> 0 GOTO on_error

	-- clear #shouldBeMemberData
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Cleaning up #shouldBeMemberData',getdate())

	IF OBJECT_ID('tempdb..#shouldBeMemberData') IS NOT NULL BEGIN
		DROP TABLE #shouldBeMemberData
		IF @@ERROR <> 0 GOTO on_error
	END

	-- clear memberdataholding
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Cleaning up holding table',getdate())

	DELETE FROM datatransfer.dbo.ams_memberDataHolding where orgID = @orgID
		IF @@ERROR <> 0 GOTO on_error

	SELECT @returnVal = 0

IF @@TRANCOUNT > 0 COMMIT TRAN
GOTO on_cleanup

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @returnVal = -2
	GOTO on_cleanup

on_cleanup:
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Inserting entries into log table',getdate())

	INSERT INTO dataTransfer.dbo.ams_memberDataImportStatus (orgID,stepNum,stepMsg,stepDate)
	select @orgID, stepNum, stepMsg, stepDate
	from @logTable
	order by stepNum

	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Cleaning up #importMemberData'
	IF OBJECT_ID('tempdb..#importMemberData') IS NOT NULL
		DROP TABLE #importMemberData

	-- populate member group cache (@runSchedule=1 indicates immediate processing)
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Repopulating Member Group Cache'
	declare @itemGroupUID uniqueidentifier
	EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList='', @conditionIDList='', @runSchedule=1, @itemGroupUID=@itemGroupUID OUTPUT
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Import complete'

	RETURN @returnVal
GO

