use customApps
GO
declare @udid int

insert into dbo.schedTask_memberJoinDates (siteCode, joinDateFieldName, rejoinDateFieldName, droppedDateFieldName, paidThruDateFieldName, lastSuccessDate, lastErrorCode, isActive)
values ('NC', 'Original Join Date', 'Rejoin Date', 'Dropped Date', 'Expiration Date', '1/1/1980', 0, 1)
	select @udid = SCOPE_IDENTITY()

insert into dbo.schedTask_memberJoinDateSubTypes (memberJoinDateUDID, subscriptionTypeUID)
values (@udid, '44450800-451E-41E6-9266-C9BF658C4E0E')

declare @rc int
EXEC dbo.job_memberJoinDates @udid, @rc OUTPUT
select @rc
GO
