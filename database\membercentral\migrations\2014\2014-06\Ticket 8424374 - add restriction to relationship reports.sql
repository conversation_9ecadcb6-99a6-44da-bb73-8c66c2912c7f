insert into dbo.admin_siteToolRestrictions (toolTypeID, siteID)
select 154, siteID
from cms_applicationInstances
where applicationInstanceName = 'admin'
and settingsXML.value('(//settings/setting[@name="showRelationships"]/@value)[1]','bit') = 1
union
select 153, siteID
from cms_applicationInstances
where applicationInstanceName = 'admin'
and settingsXML.value('(//settings/setting[@name="showRelationships"]/@value)[1]','bit') = 1

declare @siteID int
select @siteID = min(siteID) from sites
while @siteID is not null BEGIN
	EXEC createAdminSuite @siteID
	select @siteID = min(siteID) from sites where siteID > @siteID
END