USE [dataTransfer]
GO

IF OBJECT_ID('NY_May2015StoreWideDiscount') IS  NULL BEGIN
	CREATE TABLE [dbo].[NY_May2015StoreWideDiscount](
		[reducedPrice] [money] NULL,
		[rateID] [int] NOT NULL,
		[formatID] [int] NOT NULL,
		[rateName] [varchar](100) NOT NULL,
		[rate] [money] NOT NULL,
		[startDate] [datetime] NOT NULL,
		[endDate] [datetime] NULL,
		[rateOrder] [int] NOT NULL,
		[siteResourceID] [int] NOT NULL,
		[GLAccountID] [int] NULL,
		[reportCode] [varchar](15) NULL,
		[ShippingGLAccountID] [int] NULL
	) 
end
GO

declare @siteID int, @storeID int
select @siteID = siteID from membercentral.dbo.sites where siteCode = 'NY'

insert into dbo.NY_May2015StoreWideDiscount
select ((rate * 85) / 100) as reducedPrice, 
	rateID, 
	r.formatID, 
	rateName, 
	rate, 
	startDate, 
	endDate, 
	rateOrder, 
	siteResourceID, 
	coalesce(s.GLAccountID, p.GLAccountID, spf.GLAccountID, r.GLAccountID) as GLAccountID,
	reportCode, 
	coalesce(s.ShippingGLAccountID, p.ShippingGLAccountID, spf.ShippingGLAccountID, r.ShippingGLAccountID) as ShippingGLAccountID
from membercentral.dbo.store s
inner join membercentral.dbo.store_Products as p on p.storeID = s.storeID                
inner join membercentral.dbo.store_ProductFormats as spf on spf.itemID = p.itemID
inner join membercentral.dbo.store_Rates r on r.formatID = spf.formatID
where s.siteID = @siteID

update r
set r.rate = tt.reducedPrice
from membercentral.dbo.store s
inner join membercentral.dbo.store_Products as p on p.storeID = s.storeID                
inner join membercentral.dbo.store_ProductFormats as spf on spf.itemID = p.itemID
inner join membercentral.dbo.store_Rates r on r.formatID = spf.formatID
inner join dbo.NY_May2015StoreWideDiscount tt on tt.rateID = r.rateID and tt.formatID = r.formatID
where s.siteID = @siteID	
GO
