use membercentral
GO

ALTER TABLE dbo.ams_memberDataColumns ADD
	minChars int null,
	maxChars int null,
	minSelected int null,
	maxSelected int null,
	minValueInt int null,
	maxValueInt int null,
	minValueDecimal2 decimal(9,2) null,
	maxValueDecimal2 decimal(9,2) null,
	minValueDate datetime null,
	maxValueDate datetime null;
GO

update ams_memberDataColumns
set maxNumChar = null
where maxNumChar = 8000 or maxNumChar = 0
GO

update ams_memberDataColumns
set maxChars = maxNumChar
where maxNum<PERSON>har is not null
GO

ALTER TABLE dbo.ams_memberDataColumns DROP COLUMN maxNumChar;
GO


ALTER PROC [dbo].[ams_createMemberDataColumn]
@orgID int,
@columnName varchar(255),
@columnDesc varchar(255),
@allowMultiple bit,
@skipImport bit,
@allowNull bit,
@defaultValue varchar(max),
@allowNewValuesOnImport bit,
@dataTypeCode varchar(20),
@displayTypeCode varchar(20),
@isReadOnly bit,
@minChars int,
@maxChars int,
@minSelected int,
@maxSelected int,
@minValueInt int,
@maxValueInt int,
@minValueDecimal2 decimal(9,2),
@maxValueDecimal2 decimal(9,2),
@minValueDate datetime,
@maxValueDate datetime,
@columnID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- check for existing column with same name
	SELECT @columnID = null

	-- if reserved column name, return 0
	IF EXISTS(select emailTypeID from dbo.ams_memberEmailTypes where orgID = @orgID and emailType = @columnName)
	OR EXISTS(select websiteTypeID from dbo.ams_memberWebsiteTypes where orgID = @orgID and websiteType = @columnName)
	OR EXISTS(select columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @columnName)
	OR EXISTS(select C.NAME FROM SYSCOLUMNS C INNER JOIN SYSOBJECTS O ON C.ID = O.ID WHERE O.NAME = 'ams_members' and  C.NAME = @columnName)
		SELECT @columnID = 0

	-- if not there, add it
	ELSE BEGIN
		DECLARE @rc int, @valueID int
		
		DECLARE @dataTypeID int, @displayTypeID int
		SELECT @dataTypeID = dataTypeID from dbo.ams_memberDataColumnDataTypes where dataTypeCode = @dataTypeCode
			IF @dataTypeID is null RAISERROR('invalid datatypecode', 16, 1);
		SELECT @displayTypeID = displayTypeID from dbo.ams_memberDataColumnDisplayTypes where displayTypeCode = @displayTypeCode
			IF @displayTypeID is null RAISERROR('invalid displaytypecode', 16, 1);

		-- validate display type when multiple
		IF @allowMultiple = 1 and @displayTypeCode = 'RADIO' BEGIN
			SELECT @displayTypeCode = 'CHECKBOX'
			SELECT @displayTypeID = displayTypeID from dbo.ams_memberDataColumnDisplayTypes where displayTypeCode = @displayTypeCode
		END

		-- validations
		IF @displayTypeCode IN ('DOCUMENT','TEXTAREA','HTMLCONTENT') OR @dataTypeCode in ('XML','CONTENTOBJ','DOCUMENTOBJ')
			SELECT @allowNull = 1
		IF @displayTypeCode IN ('DOCUMENT') OR @dataTypeCode in ('DOCUMENTOBJ')
			SELECT @skipImport = 1
		IF @allowNull = 0 and len(isnull(@defaultValue,'')) = 0
			SELECT @allowNull = 1
		IF @skipImport = 1
			SELECT @allowNewValuesOnImport = 1
		IF @allowNull = 1
			SELECT @defaultValue = ''

		-- add column
		INSERT INTO dbo.ams_memberDataColumns (orgID, columnName, columnDesc, skipImport, dataTypeID, displayTypeID, allowNull, 
			allowNewValuesOnImport, defaultValueID, isReadOnly, allowMultiple, minChars, maxChars, minSelected, maxSelected, 
			minValueInt, maxValueInt, minValueDecimal2, maxValueDecimal2, minValueDate, maxValueDate)
		VALUES (@orgID, @columnName, @columnDesc, @skipImport, @dataTypeID, @displayTypeID, @allowNull, @allowNewValuesOnImport, 
			null, @isReadOnly, @allowMultiple, @minChars, @maxChars, @minSelected, @maxSelected, @minValueInt, @maxValueInt, 
			@minValueDecimal2, @maxValueDecimal2, @minValueDate, @maxValueDate)
		SELECT @columnID = SCOPE_IDENTITY()
		
		-- if adding a bit column, add the two values automatically
		IF @dataTypeCode = 'BIT' BEGIN
			EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=0, @valueID=@valueID OUTPUT
				IF @valueID = 0 RAISERROR('unable to create bit column value 0', 16, 1);
			EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=1, @valueID=@valueID OUTPUT
				IF @valueID = 0 RAISERROR('unable to create bit column value 1', 16, 1);
		END

		-- set default valueID if necessary
		IF len(isnull(@defaultValue,'')) > 0 BEGIN
			EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=@defaultValue, @valueID=@valueID OUTPUT
				IF @valueID = 0 RAISERROR('unable to create default column value', 16, 1);
			UPDATE dbo.ams_memberDataColumns 
			SET defaultValueID = @valueID
			WHERE columnID = @columnID
		
			-- Anyone who doesnt have a value for this column needs this value.
			IF OBJECT_ID('tempdb..#tblMDDEF') IS NOT NULL 
				DROP TABLE #tblMDDEF
			CREATE TABLE #tblMDDEF (memberid int PRIMARY KEY);

			insert into #tblMDDEF (memberid)
			select distinct m.memberid
			from dbo.ams_members as m
			left outer join dbo.ams_memberData as md 
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID and mdcv.columnID = @columnID
				on md.memberid = m.memberID
			where m.orgID = @orgID
			and m.memberid = m.activememberid
			and m.status <> 'D'
			and md.dataid is null

			INSERT INTO dbo.ams_memberData (memberid, valueID)
			select memberid, @valueID
			from #tblMDDEF

			UPDATE dbo.ams_members
			SET dateLastUpdated = getdate()
			WHERE memberID in (select memberID from #tblMDDEF)

			IF OBJECT_ID('tempdb..#tblMDDEF') IS NOT NULL 
				DROP TABLE #tblMDDEF
		END 

		-- recreate view for org
		EXEC dbo.ams_createVWMemberData	@orgID=@orgID
	END


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @columnID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[migrate_customfields]
@orgcode varchar(5)

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- this can be called if orgcode on old, sitecode on new, and orgcode on new are all the same

	declare @newcolumnid int, @oldcolumnID int, @valueID int, @mingrp int, @minODID int, @mingrpNew int,
		@orgID int, @rc int, @conditionID int, @ruleID int
	declare @colList varchar(max), @mincol varchar(255), @minOD varchar(max), @fieldcode varchar(60),
		@errMsg varchar(800), @x varchar(400), @s varchar(400), @rulename varchar(400), @ruleSQL varchar(max), 
		@valueXML xml
	declare @conditionUID uniqueidentifier
	SET @orgID = dbo.fn_getOrgIDFromOrgCode(@orgcode)
		
	select @colList = replace(replace((
		select replace(optionDescription,' ','^') as [data()]
		from tlasites.trialsmith.dbo.orgMemberOptionNames
		where orgcode = @orgcode
		FOR XML PATH ('')), ' ', ','),'^',' ')
	select @mincol = min(listitem) from dbo.fn_varCharListToTable(@colList,',')
	while @minCol is not null BEGIN	
		select @oldcolumnID = null
		select @newcolumnID = null
		select @oldcolumnID = optionNameID from tlasites.trialsmith.dbo.orgmemberOptionNames where orgcode=@orgcode and optionDescription=@minCol
		select @newcolumnID = columnID from dbo.ams_memberDataColumns where orgID=@orgid and columnName=@minCol
		IF @newcolumnID is null BEGIN

			-- create it as a select/string field if there are options on old
			IF EXISTS (
				select omoc.optionCodeID
				from tlasites.trialsmith.dbo.orgMemberOptionCodes as omoc
				where omoc.optionNameID = @oldcolumnID
				) BEGIN
				EXEC dbo.ams_createMemberDataColumn @orgID=@orgID, @columnName=@minCol, @columnDesc=@minCol, @allowMultiple=0, @skipImport=0, 
					@allowNull=1, @defaultValue=null, @allowNewValuesOnImport=1, @dataTypeCode='STRING', @displayTypeCode='SELECT', @isReadOnly=0, 
					@minChars=null, @maxChars=null, @minSelected=null, @maxSelected=null, @minValueInt=null, @maxValueInt=null, 
					@minValueDecimal2=null, @maxValueDecimal2=null, @minValueDate=null, @maxValueDate=null, @columnID=@newcolumnID OUTPUT

				select @minOD = null
				select @minOD = min(optionCodeDescription) from tlasites.trialsmith.dbo.orgMemberOptionCodes where optionNameID = @oldcolumnID
				while @minOD is not null BEGIN
					EXEC dbo.ams_createMemberDataColumnValue @columnID=@newcolumnID, @columnValue=@minOD, @valueID=@valueID OUTPUT

					-- if there are groups tied to the option on old, create those links on new
					IF EXISTS (
						SELECT omog.usergroupid
						from tlasites.trialsmith.dbo.orgMemberOptionGroups as omog
						inner join tlasites.trialsmith.dbo.orgMemberOptionCodes as omoc on omoc.optionCodeID = omog.optionCodeID
						where omoc.optionCodeDescription = @minOD
						AND omoc.optionNameID = @oldcolumnID
					) BEGIN

						-- create condition
						select @fieldcode = null
						select @fieldcode = 'md_' + cast(@newcolumnID as varchar(5))
						set @valueXML = '<values><value key="value" value="' + cast(@valueID as varchar(20)) + '"/></values>'

						EXEC dbo.ams_createVirtualGroupCondition @orgID=@orgID, @conditionTypeID=1, @fieldCode=@fieldcode, @expression='eq', @datepart=null, @dateExpression=null, @value=@valueXML, @isDefined=1, @bypassQueue=1, @conditionID=@conditionID OUTPUT
						SELECT @conditionUID = [uid] from dbo.ams_virtualGroupConditions where conditionID = @conditionID

						-- create rule
						SET @x = null
						SET @s = null
						SET @rulename = null
						SET @x = '<rule><conditionset op="AND" act="include" id="' + cast(newid() as varchar(255)) + '"><condition id="' + cast(@conditionUID as varchar(50)) + '" /></conditionset></rule>'; 
						SET @rulename = @minCol + ' - ' + @minOD
						EXEC dbo.ams_createVirtualGroupRule @orgID, 1, @rulename, @x, '', @ruleID OUTPUT
						EXEC dbo.ams_updateVirtualGroupRuleSQL @ruleID=@ruleID
									
						-- loop over the linked groups				
						select @mingrp = null
						select @mingrp = min(omog.usergroupid) 
							from tlasites.trialsmith.dbo.orgMemberOptionGroups as omog
							inner join tlasites.trialsmith.dbo.orgMemberOptionCodes as omoc on omoc.optionCodeID = omog.optionCodeID
							where omoc.optionCodeDescription = @minOD
							AND omoc.optionNameID = @oldcolumnID
						while @mingrp is not null begin

							-- create rulegroup
							select @mingrpNew = g.groupID 
								from dbo.ams_groups as g
								inner join tlasites.trialsmith.dbo.usergroups as oldg on oldg.description = g.groupName COLLATE Latin1_General_CI_AI
									and oldg.orgcode = @orgcode
								where g.orgID = @orgID 
								and oldg.usergroupID = @mingrp

							EXEC dbo.ams_createVirtualGroupRuleGroup @ruleID, @mingrpNew

							select @mingrp = min(omog.usergroupid) 
								from tlasites.trialsmith.dbo.orgMemberOptionGroups as omog
								inner join tlasites.trialsmith.dbo.orgMemberOptionCodes as omoc on omoc.optionCodeID = omog.optionCodeID
								where omoc.optionCodeDescription = @minOD
								AND omoc.optionNameID = @oldcolumnID
								AND omog.usergroupid > @mingrp
						END

					END

					select @minOD = min(optionCodeDescription) from tlasites.trialsmith.dbo.orgMemberOptionCodes where optionNameID = @oldcolumnID and optionCodeDescription > @minOD
				END

				END	
		
			-- else, create it as a textbox question
			ELSE BEGIN
				EXEC dbo.ams_createMemberDataColumn @orgID=@orgID, @columnName=@minCol, @columnDesc=@minCol, @allowMultiple=0, @skipImport=0, 
					@allowNull=1, @defaultValue=null, @allowNewValuesOnImport=1, @dataTypeCode='STRING', @displayTypeCode='TEXTBOX', @isReadOnly=0, 
					@minChars=null, @maxChars=null, @minSelected=null, @maxSelected=null, @minValueInt=null, @maxValueInt=null, 
					@minValueDecimal2=null, @maxValueDecimal2=null, @minValueDate=null, @maxValueDate=null, @columnID=@newcolumnID OUTPUT
			END
		END

		select @mincol = min(listitem) from dbo.fn_varCharListToTable(@colList,',') where listitem > @mincol
	END


	-- Activate all rules
	UPDATE ams_virtualGroupRules 
	set isActive = 1
	where orgid = @orgID

	-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
	declare @itemGroupUID uniqueidentifier
	EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList='', @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[ams_updateMemberDataColumn]
@columnID int,
@columnName varchar(255),
@columnDesc varchar(255),
@allowMultiple bit,
@skipImport bit,
@allowNull bit,
@defaultValue varchar(max),
@allowNewValuesOnImport bit,
@dataTypeCode varchar(20),
@displayTypeCode varchar(20),
@isReadOnly bit,
@minChars int,
@maxChars int,
@minSelected int,
@maxSelected int,
@minValueInt int,
@maxValueInt int,
@minValueDecimal2 decimal(9,2),
@maxValueDecimal2 decimal(9,2),
@minValueDate datetime,
@maxValueDate datetime

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	DECLARE @valueID int, @rc int, @orgID int, @displayTypeID int, @dataTypeID int, @newbitvalueID int
	DECLARE @oldColumnName varchar(255), @olddataTypeCode varchar(20), @olddisplayTypeCode varchar(20)
	DECLARE @memberIDList varchar(max), @conditionIDList varchar(max)

	IF @columnID is not null AND @columnName is not null 
	BEGIN

		SELECT @orgID = orgID from dbo.ams_memberdataColumns where columnID = @columnID

		IF EXISTS(select emailTypeID from dbo.ams_memberEmailTypes where orgID = @orgID and emailType = @columnName)
		OR EXISTS(select websiteTypeID from dbo.ams_memberWebsiteTypes where orgID = @orgID and websiteType = @columnName)
		OR EXISTS(select columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @columnName and columnID <> @columnID)
		OR EXISTS(select C.NAME FROM SYSCOLUMNS C INNER JOIN SYSOBJECTS O ON C.ID = O.ID WHERE O.NAME = 'ams_members' and  C.NAME = @columnName)
			RAISERROR('That column name is reserved or already in use.', 16, 1) 
		ELSE BEGIN
			SELECT @oldColumnName = columnName from dbo.ams_memberDataColumns where columnID = @columnID

			-- validate display type when multiple
			IF @allowMultiple = 1 and @displayTypeCode = 'RADIO'
				SELECT @displayTypeCode = 'CHECKBOX'
			IF @allowMultiple = 0 and @displayTypeCode = 'CHECKBOX'
				SELECT @displayTypeCode = 'RADIO'

			-- validations
			SELECT @olddataTypeCode = dt.dataTypeCode
				from dbo.ams_memberDataColumns as c
				inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = c.dataTypeID
				and c.columnID = @columnID
			SELECT @olddisplayTypeCode = dt.displayTypeCode
				from dbo.ams_memberDataColumns as c
				inner join dbo.ams_memberDataColumnDisplayTypes as dt on dt.displayTypeID = c.displayTypeID
				and c.columnID = @columnID
			SELECT @displayTypeID = displayTypeID
				from dbo.ams_memberDataColumnDisplayTypes
				where displayTypeCode = @displayTypeCode
			SELECT @dataTypeID = dataTypeID
				from dbo.ams_memberDataColumnDataTypes
				where dataTypeCode = @dataTypeCode
			IF @displayTypeCode IN ('DOCUMENT','TEXTAREA','HTMLCONTENT') OR @dataTypeCode in ('XML','CONTENTOBJ','DOCUMENTOBJ')
				SELECT @allowNull = 1
			IF @displayTypeCode IN ('DOCUMENT') OR @dataTypeCode in ('DOCUMENTOBJ')
				SELECT @skipImport = 1
			IF @allowNull = 0 and len(isnull(@defaultValue,'')) = 0
				SELECT @allowNull = 1
			IF @skipImport = 1
				SELECT @allowNewValuesOnImport = 1
			IF @allowNull = 1
				SELECT @defaultValue = ''

			-- set default valueID if necessary
			IF len(isnull(@defaultValue,'')) > 0 BEGIN
				EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=@defaultValue, @valueID=@valueID OUTPUT
					IF @valueID = 0 SELECT @allowNull = 1
			END 

			-- update column info
			UPDATE dbo.ams_memberDataColumns 
			SET columnName = @columnName,
				columnDesc = @columnDesc,
				skipImport = @skipImport,
				allowNull = @allowNull,
				defaultValueID = nullif(@valueID,0),
				allowNewValuesOnImport = @allowNewValuesOnImport,
				isReadOnly = @isReadOnly,
				allowMultiple = @allowMultiple,
				minChars = @minChars,
				maxChars = @maxChars,
				minSelected = @minSelected,
				maxSelected = @maxSelected,
				minValueInt = @minValueInt,
				maxValueInt = @maxValueInt,
				minValueDecimal2 = @minValueDecimal2,
				maxValueDecimal2 = @maxValueDecimal2,
				minValueDate = @minValueDate,
				maxValueDate = @maxValueDate
			WHERE columnID = @columnID

			-- if changing the display type
			IF @displayTypeCode <> @olddisplayTypeCode BEGIN
				UPDATE dbo.ams_memberDataColumns
				SET displayTypeID = @displayTypeID
				WHERE columnID = @columnID

				UPDATE dbo.ams_memberFields
				SET displayTypeID = @displayTypeID
				WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))

				-- if was a radio/select/checkbox (not bit) and is no longer that, we need to convert valueID to value
				IF @olddataTypeCode <> 'BIT' and @dataTypeCode <> 'BIT' and @olddisplayTypeCode in ('RADIO','SELECT','CHECKBOX') and @displayTypeCode not in ('RADIO','SELECT','CHECKBOX') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = coalesce(mdcv.columnValueString, cast(mdcv.columnValueDecimal2 as varchar(15)), cast(mdcv.columnValueInteger as varchar(15)), convert(varchar(10),mdcv.columnvalueDate,101))
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
				END

				-- if was NOT a radio/select/checkbox (not bit) and is now that, we need to convert value to valueID
				IF @olddataTypeCode <> 'BIT' and @dataTypeCode <> 'BIT' and @olddisplayTypeCode not in ('RADIO','SELECT','CHECKBOX') and @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
					
					-- err if any expressions are not 1,2,7,8 now that it will be a select
					IF EXISTS (select conditionID from dbo.ams_virtualGroupConditions where fieldCode = 'md_' + cast(@columnID as varchar(10)) and expressionID not in (1,2,7,8))
						RAISERROR('There are group assignment conditions that are not compatible with the selected display type.', 16, 1) 

					-- create column values for those condition values that dont yet exist as column values
					declare @tblVals TABLE (newVal varchar(max))
					insert into @tblVals (newVal)
					select vgcv.conditionValue
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.dataTypeID = 1
					and vgc.expressionID in (1,2)
					and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueString = vgcv.conditionValue)
						union
					select cast(vgcv.conditionValue as varchar(15))
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.dataTypeID = 2
					and vgc.expressionID in (1,2)
					and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueDecimal2 = cast(vgcv.conditionValue as decimal(9,2)))
						union
					select cast(vgcv.conditionValue as varchar(15))
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.dataTypeID = 3
					and vgc.expressionID in (1,2)
					and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueInteger = cast(vgcv.conditionValue as int))
						union
					select convert(varchar(10),vgcv.conditionValue,101)
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.dataTypeID = 4
					and vgc.expressionID in (1,2)
					and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueDate = cast(vgcv.conditionValue as datetime))

					DECLARE @newvalueID int, @minValue varchar(max)
					select @minValue = min(newVal) from @tblVals
					while @minValue is not null BEGIN
						EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=@minValue, @valueID=@newvalueID OUTPUT
						select @minValue = min(newVal) from @tblVals where newVal > @minValue
					END

					-- get the valueID
					UPDATE vgcv
					SET vgcv.conditionValue = tmp.valueID
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					INNER JOIN (
						select vgc.conditionID, mdcv.valueID
						from dbo.ams_virtualGroupConditions as vgc
						INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
						and vgc.expressionID in (1,2)
						and vgc.dataTypeID = 1 
						and vgcv.conditionValue = mdcv.columnvalueString
							union
						select vgc.conditionID, mdcv.valueID
						from dbo.ams_virtualGroupConditions as vgc
						INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
						and vgc.expressionID in (1,2)
						and vgc.dataTypeID = 2 
						and cast(vgcv.conditionValue as decimal(9,2)) = mdcv.columnvalueDecimal2
							union
						select vgc.conditionID, mdcv.valueID
						from dbo.ams_virtualGroupConditions as vgc
						INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
						and vgc.expressionID in (1,2)
						and vgc.dataTypeID = 3 
						and cast(vgcv.conditionValue as int) = mdcv.columnvalueInteger
							union
						select vgc.conditionID, mdcv.valueID
						from dbo.ams_virtualGroupConditions as vgc
						INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
						and vgc.expressionID in (1,2)
						and vgc.dataTypeID = 4 
						and cast(vgcv.conditionValue as datetime) = mdcv.columnvalueDate
					) as tmp on tmp.conditionID = vgc.conditionID
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
				END

				UPDATE dbo.ams_virtualGroupConditions
				SET displayTypeID = @displayTypeID
				WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))

				UPDATE dbo.ams_virtualGroupConditions
				set [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
				WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))
			END

			-- if changing the data type
			IF @dataTypeCode <> @olddataTypeCode BEGIN
				UPDATE dbo.ams_memberDataColumns
				SET dataTypeID = @dataTypeID
				WHERE columnID = @columnID

				UPDATE dbo.ams_memberFields
				SET dataTypeID = @dataTypeID
				WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))

				-- check ams_virtualGroupConditions for expression conflicts
				IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'DECIMAL2' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueDecimal2 = cast(columnValueString as decimal(9,2))
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are string values not compatible with the Decimal Number (2) data type.', 16, 1) 
					END CATCH

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = null
					where columnID = @columnID

					IF EXISTS (
						select conditionID
						from dbo.ams_virtualGroupConditions
						where fieldCode = 'md_' + cast(@columnID as varchar(10))
						and expressionID in (9,10)
					) RAISERROR('There are group assignment conditions that are not compatible with the Decimal Number (2) data type.', 16, 1) 
				END
				IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'INTEGER' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueInteger = cast(columnValueString as int)
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are string values not compatible with the Whole Number data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = null
					where columnID = @columnID

					IF EXISTS (
						select conditionID
						from dbo.ams_virtualGroupConditions
						where fieldCode = 'md_' + cast(@columnID as varchar(10))
						and expressionID in (9,10)
					) RAISERROR('There are group assignment conditions that are not compatible with the Whole Number data type.', 16, 1) 
				END
				IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'DATE' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueDate = cast(columnValueString as datetime)
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are string values not compatible with the Date data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = null
					where columnID = @columnID

					IF EXISTS (
						select conditionID
						from dbo.ams_virtualGroupConditions
						where fieldCode = 'md_' + cast(@columnID as varchar(10))
						and expressionID in (9,10)
					) RAISERROR('There are group assignment conditions that are not compatible with the Date data type.', 16, 1) 
				END
				IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'BIT' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueBit = cast(columnValueString as bit)
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are string values not compatible with the Boolean data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = null
					where columnID = @columnID

					-- ensure both bit values are there					
					EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='1', @valueID=@newbitvalueID OUTPUT
					EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='0', @valueID=@newbitvalueID OUTPUT

					IF EXISTS (
						select conditionID
						from dbo.ams_virtualGroupConditions
						where fieldCode = 'md_' + cast(@columnID as varchar(10))
						and expressionID in (3,4,5,6,9,10)
					) RAISERROR('There are group assignment conditions that are not compatible with the Boolean data type.', 16, 1) 

					-- if was a radio/select/checkbox, we need to convert valueID to value because BIT doesnt store valueID
					IF @olddisplayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
						UPDATE vgcv
						SET vgcv.conditionValue = mdcv.columnValueBit
						FROM dbo.ams_virtualGroupConditions as vgc
						INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
							and cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue
						WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
						and vgc.expressionID in (1,2)
					END
				END
				IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'XML' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueXML = cast(columnValueString as xml)
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are string values not compatible with the XML data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = null
					where columnID = @columnID

					IF EXISTS (
						select conditionID
						from dbo.ams_virtualGroupConditions
						where fieldCode = 'md_' + cast(@columnID as varchar(10))
						and expressionID in (1,2,3,4,5,6,9,10)
					) RAISERROR('There are group assignment conditions that are not compatible with the XML data type.', 16, 1) 
				END
				IF @olddataTypeCode = 'DECIMAL2' AND @dataTypeCode = 'STRING' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueString = cast(columnValueDecimal2 as varchar(255))
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are decimal values not compatible with the Text String data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueDecimal2 = null
					where columnID = @columnID
				END
				IF @olddataTypeCode = 'INTEGER' AND @dataTypeCode = 'STRING' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueString = cast(columnValueInteger as varchar(255))
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are whole number values not compatible with the Text String data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueInteger = null
					where columnID = @columnID
				END
				IF @olddataTypeCode = 'INTEGER' AND @dataTypeCode = 'DECIMAL2' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueDecimal2 = cast(columnValueInteger as decimal(9,2))
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are whole number values not compatible with the Decimal Number (2) data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueInteger = null
					where columnID = @columnID
				END
				IF @olddataTypeCode = 'INTEGER' AND @dataTypeCode = 'BIT' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueBit = cast(columnValueInteger as bit)
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are whole number values not compatible with the Boolean data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueInteger = null
					where columnID = @columnID

					-- ensure both bit values are there					
					EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='1', @valueID=@newbitvalueID OUTPUT
					EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='0', @valueID=@newbitvalueID OUTPUT

					IF EXISTS (
						select conditionID
						from dbo.ams_virtualGroupConditions
						where fieldCode = 'md_' + cast(@columnID as varchar(10))
						and expressionID in (3,4,5,6)
					) RAISERROR('There are group assignment conditions that are not compatible with the Boolean data type.', 16, 1) 

					-- if was a radio/select/checkbox, we need to convert valueID to value because BIT doesnt store valueID
					IF @olddisplayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
						UPDATE vgcv
						SET vgcv.conditionValue = mdcv.columnValueBit
						FROM dbo.ams_virtualGroupConditions as vgc
						INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
							and cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue
						WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
						and vgc.expressionID in (1,2)
					END
				END
				IF @olddataTypeCode = 'DATE' AND @dataTypeCode = 'STRING' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueString = convert(varchar(10),columnValueDate,101)
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are date values not compatible with the Text String data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueDate = null
					where columnID = @columnID

					IF EXISTS (
						select conditionID
						from dbo.ams_virtualGroupConditions
						where fieldCode = 'md_' + cast(@columnID as varchar(10))
						and expressionID in (11,12)
					) RAISERROR('There are group assignment conditions that are not compatible with the Text String data type.', 16, 1) 
				END
				IF @olddataTypeCode = 'BIT' AND @dataTypeCode = 'STRING' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueString = cast(columnValueBit as varchar(255))
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are boolean values not compatible with the Text String data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueBit = null
					where columnID = @columnID

					-- if going to be radio/select/checkbox, we need to convert value to valueID because BIT doesnt store valueID
					IF @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
						UPDATE vgcv
						SET vgcv.conditionValue = mdcv.valueID
						FROM dbo.ams_virtualGroupConditions as vgc
						INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
							and mdcv.columnvalueString = vgcv.conditionValue
						WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
						and vgc.expressionID in (1,2)
					END
				END
				IF @olddataTypeCode = 'BIT' AND @dataTypeCode = 'DECIMAL2' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueDecimal2 = cast(columnValueBit as decimal(9,2))
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are boolean values not compatible with the Decimal Number (2) data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueBit = null
					where columnID = @columnID

					-- if going to be radio/select/checkbox, we need to convert value to valueID because BIT doesnt store valueID
					IF @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
						UPDATE vgcv
						SET vgcv.conditionValue = mdcv.valueID
						FROM dbo.ams_virtualGroupConditions as vgc
						INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
							and mdcv.columnvalueDecimal2 = vgcv.conditionValue
						WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
						and vgc.expressionID in (1,2)
					END
				END
				IF @olddataTypeCode = 'BIT' AND @dataTypeCode = 'INTEGER' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueInteger = cast(columnValueBit as int)
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are boolean values not compatible with the Whole Number data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueBit = null
					where columnID = @columnID

					-- if going to be radio/select/checkbox, we need to convert value to valueID because BIT doesnt store valueID
					IF @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
						UPDATE vgcv
						SET vgcv.conditionValue = mdcv.valueID
						FROM dbo.ams_virtualGroupConditions as vgc
						INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
							and cast(mdcv.columnvalueInteger as varchar(15)) = vgcv.conditionValue
						WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
						and vgc.expressionID in (1,2)
					END
				END

				UPDATE dbo.ams_virtualGroupConditions
				set dataTypeID = @dataTypeID
				WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))
			
				UPDATE dbo.ams_virtualGroupConditions
				set [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
				WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))
			END

			-- if valueID is not null, there is a def value. 
			-- Anyone who doesnt have a value for this column needs this value.
			IF nullif(@valueID,0) is not null BEGIN
				IF OBJECT_ID('tempdb..#tblMDDEF') IS NOT NULL 
					DROP TABLE #tblMDDEF
				CREATE TABLE #tblMDDEF (memberID int PRIMARY KEY)

				insert into #tblMDDEF (memberID)
				select distinct m.memberid
				from dbo.ams_members as m
				where m.orgID = @orgID
				and m.memberid = m.activememberid
				and m.status <> 'D'
					except
				select distinct md.memberID
				from dbo.ams_memberData as md 
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
				where mdcv.columnID = @columnID

				INSERT INTO dbo.ams_memberData (memberid, valueID)
				select memberid, @valueID
				from #tblMDDEF

				UPDATE m
				SET m.dateLastUpdated = getdate()
				FROM dbo.ams_members as m
				INNER JOIN #tblMDDEF as tmp on tmp.memberID = m.memberID

				-- queue processing of member groups (@runSchedule=2 indicates delayed processing)
				-- run this at end, outside of transaction, to speed it up 
				SELECT @conditionIDList = COALESCE(@conditionIDList + ',', '') + cast(c.conditionID as varchar(10)) 
					from dbo.ams_virtualGroupConditions as C
					where c.orgID = @orgID
					and C.fieldcode = 'md_' + Cast(@columnID as varchar(10))
					group by c.conditionID
				IF @conditionIDList is not NULL BEGIN				
					SELECT @memberIDList = COALESCE(@memberIDList + ',', '') + cast(m.memberID as varchar(10)) 
						from #tblMDDEF as m 
						group by m.memberID
				END

				IF OBJECT_ID('tempdb..#tblMDDEF') IS NOT NULL 
					DROP TABLE #tblMDDEF
			END

			-- if there was a change in columnname
			IF @oldColumnName <> @columnName COLLATE Latin1_General_CS_AI BEGIN
				-- update member fields
				UPDATE dbo.ams_memberFields
				SET dbField = @columnName
				WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))
			
				-- update virtual group conditions
				UPDATE dbo.ams_virtualGroupConditions
				SET [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
				WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))
			END

			IF @oldColumnName <> @columnName OR @dataTypeCode <> @olddataTypeCode BEGIN
				EXEC dbo.ams_createVWMemberData	@orgID=@orgID
			END

		END

	END

	IF @TranCounter = 0
		COMMIT TRAN;

	-- if we need to call processMemberGroups
	IF @conditionIDList is not null BEGIN
		declare @itemGroupUID uniqueidentifier
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberIDList, @conditionIDList=@conditionIDList, @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT
	END

	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[ams_getMemberFields]
@fieldsetID int

AS

set nocount on

declare @tblMF TABLE (fieldID int PRIMARY KEY, columnID int)
insert into @tblMF (fieldID, columnID)
select mf.fieldID, cast(case when left(mf.fieldCode,3) = 'md_' then replace(mf.fieldCode,'md_','') else 0 end as int) as columnID
from dbo.ams_memberFields as mf
where mf.fieldsetID = @fieldsetid

select isnull(cast((
	select	mfs.fieldsetName as '@fieldsetName', mfs.nameformat as '@nameformat', mfs.fieldSetID as '@fieldsetID', mfs.showHelp as '@showHelp',
			mfs.descriptionContentID as '@descriptionContentID', mfs.descriptionPosition as '@descriptionPosition',
		(
		select mf.fieldID, mf.dbObject, mf.dbObjectAlias, mf.dbField, mf.fieldCode, mf.mdColumnID, mf.fieldLabel, mf.fieldDescription, 
			mf.isRequired, mf.isGrouped, mf.displayTypeCode, mf.dataTypeCode, mf.isReadOnly, mf.minChars, mf.maxChars, mf.minSelected, 
			mf.maxSelected, mf.minValueInt, mf.maxValueInt, mf.minValueDecimal2, mf.maxValueDecimal2, mf.minValueDate, mf.maxValueDate,
			mf.allowMultiple, opt.columnValueDecimal2, opt.columnValueInteger, opt.columnvalueDate, opt.columnValueBit, 
			opt.columnValueXML, opt.columnValueSiteResourceID, 
			case when left(mf.fieldCode,4) = 'grp_' or left(mf.fieldCode,5) = 'acct_' then 0 else opt.valueID end as valueID, 
			case 
				when mf.dbObjectAlias = 'subs' and left(mf.dbField,3) = 'sub' then (select coalesce(reportCode,subscriptionName) from dbo.sub_subscriptions where subscriptionID = parseName(replace(mf.dbField,'_','.'),2)) 
				when mf.dbObjectAlias = 'subs' then (select typeName from dbo.sub_Types where typeID = parseName(replace(mf.dbField,'_','.'),2)) 
				when mf.dbObjectAlias = 'grps' then (select coalesce(groupcode,groupName) from dbo.ams_groups where groupID = parseName(replace(mf.fieldCode,'_','.'),1)) 
				when mf.dbObjectAlias = 'acct' and left(mf.dbField,13) = 'acct_balance_' then isnull((select profileName from dbo.mp_profiles where profileID = parseName(replace(mf.dbField,'_','.'),1)),'Total') 
				else opt.columnValueString 
				end as columnValueString
		from (
			select amf.fieldID, amf.dbObject, amf.dbObjectAlias, amf.dbField, amf.fieldCode, amf.fieldLabel, amf.fieldDescription, 
				amf.isRequired, amf.isGrouped, dt.displayTypeCode, ddt.dataTypeCode, amf.fieldOrder,
				case when left(amf.fieldcode,4) = 'mad_' then 1 when tmp.columnID is not null then mdc.isReadOnly else 0 end as isReadOnly,
				isnull(mdc.allowMultiple,0) as allowMultiple,
				case when dt.displayTypeCode in ('SELECT','RADIO','CHECKBOX') and tmp.columnID is not null then tmp.columnID else 0 end as mdColumnID, 
				mdc.minChars, mdc.maxChars, mdc.minSelected, mdc.maxSelected, mdc.minValueInt, mdc.maxValueInt, mdc.minValueDecimal2,
				mdc.maxValueDecimal2, mdc.minValueDate, mdc.maxValueDate
			from @tblMF as tmp
			inner join dbo.ams_memberFields as amf on amf.fieldID = tmp.fieldID
			inner join dbo.ams_memberDataColumnDisplayTypes as dt on dt.displayTypeID = amf.displayTypeID
			inner join dbo.ams_memberDataColumnDataTypes as ddt on ddt.dataTypeID = amf.dataTypeID
			left outer join dbo.ams_memberDataColumns as mdc on mdc.columnID = tmp.columnID
		) as mf
		left outer join dbo.ams_memberDataColumnValues as opt on opt.columnID = mf.mdColumnID and mf.mdColumnID > 0	
		order by mf.isGrouped desc, mf.fieldOrder, opt.columnValueString, opt.columnValueDecimal2, opt.columnValueInteger, 
			opt.columnvalueDate, opt.columnValueBit, cast(opt.columnValueXML as varchar(max)), opt.columnValueSiteResourceID
		FOR XML AUTO, TYPE
		)
	from dbo.ams_memberFieldSets as mfs
	where mfs.fieldsetID = @fieldsetid
	FOR XML PATH('fields')
) as XML),'<fields/>') as fieldsXML

RETURN 0
GO

ALTER PROC [dbo].[ams_updateMemberDataColumn]
@columnID int,
@columnName varchar(255),
@columnDesc varchar(255),
@allowMultiple bit,
@skipImport bit,
@allowNull bit,
@defaultValue varchar(max),
@allowNewValuesOnImport bit,
@dataTypeCode varchar(20),
@displayTypeCode varchar(20),
@isReadOnly bit,
@minChars int,
@maxChars int,
@minSelected int,
@maxSelected int,
@minValueInt int,
@maxValueInt int,
@minValueDecimal2 decimal(9,2),
@maxValueDecimal2 decimal(9,2),
@minValueDate datetime,
@maxValueDate datetime

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	DECLARE @valueID int, @rc int, @orgID int, @displayTypeID int, @dataTypeID int, @newbitvalueID int
	DECLARE @oldColumnName varchar(255), @olddataTypeCode varchar(20), @olddisplayTypeCode varchar(20)
	DECLARE @memberIDList varchar(max), @conditionIDList varchar(max)

	IF @columnID is not null AND @columnName is not null 
	BEGIN

		SELECT @orgID = orgID from dbo.ams_memberdataColumns where columnID = @columnID

		-- check custom field name
		IF EXISTS(select emailTypeID from dbo.ams_memberEmailTypes where orgID = @orgID and emailType = @columnName)
		OR EXISTS(select websiteTypeID from dbo.ams_memberWebsiteTypes where orgID = @orgID and websiteType = @columnName)
		OR EXISTS(select columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @columnName and columnID <> @columnID)
		OR EXISTS(select C.NAME FROM SYSCOLUMNS C INNER JOIN SYSOBJECTS O ON C.ID = O.ID WHERE O.NAME = 'ams_members' and  C.NAME = @columnName)
			RAISERROR('That column name is already in use.', 16, 1) 

		SELECT @oldColumnName = columnName from dbo.ams_memberDataColumns where columnID = @columnID

		-- validate display type when multiple
		IF @allowMultiple = 1 and @displayTypeCode = 'RADIO'
			SELECT @displayTypeCode = 'CHECKBOX'
		IF @allowMultiple = 0 and @displayTypeCode = 'CHECKBOX'
			SELECT @displayTypeCode = 'RADIO'

		-- validations
		SELECT @olddataTypeCode = dt.dataTypeCode
			from dbo.ams_memberDataColumns as c
			inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = c.dataTypeID
			and c.columnID = @columnID
		SELECT @olddisplayTypeCode = dt.displayTypeCode
			from dbo.ams_memberDataColumns as c
			inner join dbo.ams_memberDataColumnDisplayTypes as dt on dt.displayTypeID = c.displayTypeID
			and c.columnID = @columnID
		SELECT @displayTypeID = displayTypeID
			from dbo.ams_memberDataColumnDisplayTypes
			where displayTypeCode = @displayTypeCode
		SELECT @dataTypeID = dataTypeID
			from dbo.ams_memberDataColumnDataTypes
			where dataTypeCode = @dataTypeCode
		IF @displayTypeCode IN ('DOCUMENT','TEXTAREA','HTMLCONTENT') OR @dataTypeCode in ('XML','CONTENTOBJ','DOCUMENTOBJ')
			SELECT @allowNull = 1
		IF @displayTypeCode IN ('DOCUMENT') OR @dataTypeCode in ('DOCUMENTOBJ')
			SELECT @skipImport = 1
		IF @allowNull = 0 and len(isnull(@defaultValue,'')) = 0
			SELECT @allowNull = 1
		IF @skipImport = 1
			SELECT @allowNewValuesOnImport = 1
		IF @allowNull = 1
			SELECT @defaultValue = ''

		-- set default valueID if necessary
		IF len(isnull(@defaultValue,'')) > 0 BEGIN
			EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=@defaultValue, @valueID=@valueID OUTPUT
				IF @valueID = 0 SELECT @allowNull = 1
		END 

		-- update column info
		UPDATE dbo.ams_memberDataColumns 
		SET columnName = @columnName,
			columnDesc = @columnDesc,
			skipImport = @skipImport,
			allowNull = @allowNull,
			defaultValueID = nullif(@valueID,0),
			allowNewValuesOnImport = @allowNewValuesOnImport,
			isReadOnly = @isReadOnly,
			allowMultiple = @allowMultiple,
			minChars = @minChars,
			maxChars = @maxChars,
			minSelected = @minSelected,
			maxSelected = @maxSelected,
			minValueInt = @minValueInt,
			maxValueInt = @maxValueInt,
			minValueDecimal2 = @minValueDecimal2,
			maxValueDecimal2 = @maxValueDecimal2,
			minValueDate = @minValueDate,
			maxValueDate = @maxValueDate
		WHERE columnID = @columnID

		-- if changing the display type
		IF @displayTypeCode <> @olddisplayTypeCode BEGIN
			UPDATE dbo.ams_memberDataColumns
			SET displayTypeID = @displayTypeID
			WHERE columnID = @columnID

			UPDATE dbo.ams_memberFields
			SET displayTypeID = @displayTypeID
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))

			-- if was a radio/select/checkbox (not bit) and is no longer that, we need to convert valueID to value
			IF @olddataTypeCode <> 'BIT' and @dataTypeCode <> 'BIT' and @olddisplayTypeCode in ('RADIO','SELECT','CHECKBOX') and @displayTypeCode not in ('RADIO','SELECT','CHECKBOX') BEGIN
				UPDATE vgcv
				SET vgcv.conditionValue = coalesce(mdcv.columnValueString, cast(mdcv.columnValueDecimal2 as varchar(15)), cast(mdcv.columnValueInteger as varchar(15)), convert(varchar(10),mdcv.columnvalueDate,101))
				FROM dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
					and cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue
				WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.expressionID in (1,2)
			END

			-- if was NOT a radio/select/checkbox (not bit) and is now that, we need to convert value to valueID
			IF @olddataTypeCode <> 'BIT' and @dataTypeCode <> 'BIT' and @olddisplayTypeCode not in ('RADIO','SELECT','CHECKBOX') and @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
				
				-- err if any expressions are not 1,2,7,8 now that it will be a select
				IF EXISTS (select conditionID from dbo.ams_virtualGroupConditions where fieldCode = 'md_' + cast(@columnID as varchar(10)) and expressionID not in (1,2,7,8))
					RAISERROR('There are group assignment conditions that are not compatible with the selected display type.', 16, 1) 

				-- create column values for those condition values that dont yet exist as column values
				declare @tblVals TABLE (newVal varchar(max))
				insert into @tblVals (newVal)
				select vgcv.conditionValue
				from dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.dataTypeID = 1
				and vgc.expressionID in (1,2)
				and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueString = vgcv.conditionValue)
					union
				select cast(vgcv.conditionValue as varchar(15))
				from dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.dataTypeID = 2
				and vgc.expressionID in (1,2)
				and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueDecimal2 = cast(vgcv.conditionValue as decimal(9,2)))
					union
				select cast(vgcv.conditionValue as varchar(15))
				from dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.dataTypeID = 3
				and vgc.expressionID in (1,2)
				and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueInteger = cast(vgcv.conditionValue as int))
					union
				select convert(varchar(10),vgcv.conditionValue,101)
				from dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.dataTypeID = 4
				and vgc.expressionID in (1,2)
				and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueDate = cast(vgcv.conditionValue as datetime))

				DECLARE @newvalueID int, @minValue varchar(max)
				select @minValue = min(newVal) from @tblVals
				while @minValue is not null BEGIN
					EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=@minValue, @valueID=@newvalueID OUTPUT
					select @minValue = min(newVal) from @tblVals where newVal > @minValue
				END

				-- get the valueID
				UPDATE vgcv
				SET vgcv.conditionValue = tmp.valueID
				FROM dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				INNER JOIN (
					select vgc.conditionID, mdcv.valueID
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
					and vgc.dataTypeID = 1 
					and vgcv.conditionValue = mdcv.columnvalueString
						union
					select vgc.conditionID, mdcv.valueID
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
					and vgc.dataTypeID = 2 
					and cast(vgcv.conditionValue as decimal(9,2)) = mdcv.columnvalueDecimal2
						union
					select vgc.conditionID, mdcv.valueID
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
					and vgc.dataTypeID = 3 
					and cast(vgcv.conditionValue as int) = mdcv.columnvalueInteger
						union
					select vgc.conditionID, mdcv.valueID
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
					and vgc.dataTypeID = 4 
					and cast(vgcv.conditionValue as datetime) = mdcv.columnvalueDate
				) as tmp on tmp.conditionID = vgc.conditionID
				WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.expressionID in (1,2)
			END

			UPDATE dbo.ams_virtualGroupConditions
			SET displayTypeID = @displayTypeID
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))

			UPDATE dbo.ams_virtualGroupConditions
			set [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))
		END

		-- if changing the data type
		IF @dataTypeCode <> @olddataTypeCode BEGIN
			UPDATE dbo.ams_memberDataColumns
			SET dataTypeID = @dataTypeID
			WHERE columnID = @columnID

			UPDATE dbo.ams_memberFields
			SET dataTypeID = @dataTypeID
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))

			-- check ams_virtualGroupConditions for expression conflicts
			IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'DECIMAL2' BEGIN
				BEGIN TRY
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueDecimal2 = cast(columnValueString as decimal(9,2))
					where columnID = @columnID
				END TRY
				BEGIN CATCH
					RAISERROR('There are string values not compatible with the Decimal Number (2) data type.', 16, 1) 
				END CATCH

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueString = null
				where columnID = @columnID

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (9,10)
				) RAISERROR('There are group assignment conditions that are not compatible with the Decimal Number (2) data type.', 16, 1) 
			END
			IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'INTEGER' BEGIN
				BEGIN TRY
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueInteger = cast(columnValueString as int)
					where columnID = @columnID
				END TRY
				BEGIN CATCH
					RAISERROR('There are string values not compatible with the Whole Number data type.', 16, 1) 
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueString = null
				where columnID = @columnID

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (9,10)
				) RAISERROR('There are group assignment conditions that are not compatible with the Whole Number data type.', 16, 1) 
			END
			IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'DATE' BEGIN
				BEGIN TRY
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueDate = cast(columnValueString as datetime)
					where columnID = @columnID
				END TRY
				BEGIN CATCH
					RAISERROR('There are string values not compatible with the Date data type.', 16, 1) 
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueString = null
				where columnID = @columnID

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (9,10)
				) RAISERROR('There are group assignment conditions that are not compatible with the Date data type.', 16, 1) 
			END
			IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'BIT' BEGIN
				BEGIN TRY
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueBit = cast(columnValueString as bit)
					where columnID = @columnID
				END TRY
				BEGIN CATCH
					RAISERROR('There are string values not compatible with the Boolean data type.', 16, 1) 
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueString = null
				where columnID = @columnID

				-- ensure both bit values are there					
				EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='1', @valueID=@newbitvalueID OUTPUT
				EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='0', @valueID=@newbitvalueID OUTPUT

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (3,4,5,6,9,10)
				) RAISERROR('There are group assignment conditions that are not compatible with the Boolean data type.', 16, 1) 

				-- if was a radio/select/checkbox, we need to convert valueID to value because BIT doesnt store valueID
				IF @olddisplayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = mdcv.columnValueBit
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
				END
			END
			IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'XML' BEGIN
				BEGIN TRY
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueXML = cast(columnValueString as xml)
					where columnID = @columnID
				END TRY
				BEGIN CATCH
					RAISERROR('There are string values not compatible with the XML data type.', 16, 1) 
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueString = null
				where columnID = @columnID

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (1,2,3,4,5,6,9,10)
				) RAISERROR('There are group assignment conditions that are not compatible with the XML data type.', 16, 1) 
			END
			IF @olddataTypeCode = 'DECIMAL2' AND @dataTypeCode = 'STRING' BEGIN
				BEGIN TRY
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = cast(columnValueDecimal2 as varchar(255))
					where columnID = @columnID
				END TRY
				BEGIN CATCH
					RAISERROR('There are decimal values not compatible with the Text String data type.', 16, 1) 
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueDecimal2 = null
				where columnID = @columnID
			END
			IF @olddataTypeCode = 'INTEGER' AND @dataTypeCode = 'STRING' BEGIN
				BEGIN TRY
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = cast(columnValueInteger as varchar(255))
					where columnID = @columnID
				END TRY
				BEGIN CATCH
					RAISERROR('There are whole number values not compatible with the Text String data type.', 16, 1) 
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueInteger = null
				where columnID = @columnID
			END
			IF @olddataTypeCode = 'INTEGER' AND @dataTypeCode = 'DECIMAL2' BEGIN
				BEGIN TRY
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueDecimal2 = cast(columnValueInteger as decimal(9,2))
					where columnID = @columnID
				END TRY
				BEGIN CATCH
					RAISERROR('There are whole number values not compatible with the Decimal Number (2) data type.', 16, 1) 
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueInteger = null
				where columnID = @columnID
			END
			IF @olddataTypeCode = 'INTEGER' AND @dataTypeCode = 'BIT' BEGIN
				BEGIN TRY
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueBit = cast(columnValueInteger as bit)
					where columnID = @columnID
				END TRY
				BEGIN CATCH
					RAISERROR('There are whole number values not compatible with the Boolean data type.', 16, 1) 
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueInteger = null
				where columnID = @columnID

				-- ensure both bit values are there					
				EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='1', @valueID=@newbitvalueID OUTPUT
				EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='0', @valueID=@newbitvalueID OUTPUT

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (3,4,5,6)
				) RAISERROR('There are group assignment conditions that are not compatible with the Boolean data type.', 16, 1) 

				-- if was a radio/select/checkbox, we need to convert valueID to value because BIT doesnt store valueID
				IF @olddisplayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = mdcv.columnValueBit
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
				END
			END
			IF @olddataTypeCode = 'DATE' AND @dataTypeCode = 'STRING' BEGIN
				BEGIN TRY
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = convert(varchar(10),columnValueDate,101)
					where columnID = @columnID
				END TRY
				BEGIN CATCH
					RAISERROR('There are date values not compatible with the Text String data type.', 16, 1) 
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueDate = null
				where columnID = @columnID

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (11,12)
				) RAISERROR('There are group assignment conditions that are not compatible with the Text String data type.', 16, 1) 
			END
			IF @olddataTypeCode = 'BIT' AND @dataTypeCode = 'STRING' BEGIN
				BEGIN TRY
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = cast(columnValueBit as varchar(255))
					where columnID = @columnID
				END TRY
				BEGIN CATCH
					RAISERROR('There are boolean values not compatible with the Text String data type.', 16, 1) 
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueBit = null
				where columnID = @columnID

				-- if going to be radio/select/checkbox, we need to convert value to valueID because BIT doesnt store valueID
				IF @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = mdcv.valueID
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and mdcv.columnvalueString = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
				END
			END
			IF @olddataTypeCode = 'BIT' AND @dataTypeCode = 'DECIMAL2' BEGIN
				BEGIN TRY
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueDecimal2 = cast(columnValueBit as decimal(9,2))
					where columnID = @columnID
				END TRY
				BEGIN CATCH
					RAISERROR('There are boolean values not compatible with the Decimal Number (2) data type.', 16, 1) 
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueBit = null
				where columnID = @columnID

				-- if going to be radio/select/checkbox, we need to convert value to valueID because BIT doesnt store valueID
				IF @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = mdcv.valueID
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and mdcv.columnvalueDecimal2 = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
				END
			END
			IF @olddataTypeCode = 'BIT' AND @dataTypeCode = 'INTEGER' BEGIN
				BEGIN TRY
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueInteger = cast(columnValueBit as int)
					where columnID = @columnID
				END TRY
				BEGIN CATCH
					RAISERROR('There are boolean values not compatible with the Whole Number data type.', 16, 1) 
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueBit = null
				where columnID = @columnID

				-- if going to be radio/select/checkbox, we need to convert value to valueID because BIT doesnt store valueID
				IF @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = mdcv.valueID
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and cast(mdcv.columnvalueInteger as varchar(15)) = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
				END
			END

			UPDATE dbo.ams_virtualGroupConditions
			set dataTypeID = @dataTypeID
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))
		
			UPDATE dbo.ams_virtualGroupConditions
			set [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))
		END

		-- if valueID is not null, there is a def value. 
		-- Anyone who doesnt have a value for this column needs this value.
		IF nullif(@valueID,0) is not null BEGIN
			IF OBJECT_ID('tempdb..#tblMDDEF') IS NOT NULL 
				DROP TABLE #tblMDDEF
			CREATE TABLE #tblMDDEF (memberID int PRIMARY KEY)

			insert into #tblMDDEF (memberID)
			select distinct m.memberid
			from dbo.ams_members as m
			where m.orgID = @orgID
			and m.memberid = m.activememberid
			and m.status <> 'D'
				except
			select distinct md.memberID
			from dbo.ams_memberData as md 
			inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
			where mdcv.columnID = @columnID

			INSERT INTO dbo.ams_memberData (memberid, valueID)
			select memberid, @valueID
			from #tblMDDEF

			UPDATE m
			SET m.dateLastUpdated = getdate()
			FROM dbo.ams_members as m
			INNER JOIN #tblMDDEF as tmp on tmp.memberID = m.memberID

			-- queue processing of member groups (@runSchedule=2 indicates delayed processing)
			-- run this at end, outside of transaction, to speed it up 
			SELECT @conditionIDList = COALESCE(@conditionIDList + ',', '') + cast(c.conditionID as varchar(10)) 
				from dbo.ams_virtualGroupConditions as C
				where c.orgID = @orgID
				and C.fieldcode = 'md_' + Cast(@columnID as varchar(10))
				group by c.conditionID
			IF @conditionIDList is not NULL BEGIN				
				SELECT @memberIDList = COALESCE(@memberIDList + ',', '') + cast(m.memberID as varchar(10)) 
					from #tblMDDEF as m 
					group by m.memberID
			END

			IF OBJECT_ID('tempdb..#tblMDDEF') IS NOT NULL 
				DROP TABLE #tblMDDEF
		END

		
		-- check custom field validation ranges against existing data
		IF @minChars is not null and @maxChars is not null BEGIN
			IF @dataTypeCode = 'STRING' BEGIN
				IF EXISTS(
					select top 1 valueID
					from dbo.ams_memberdatacolumnValues
					where columnID = @columnID
					and len(columnValueString) > 0
					and len(columnValueString) not between @minChars and @maxChars
				)
				RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1) 
			END
			IF @dataTypeCode = 'CONTENTOBJ' BEGIN
				IF EXISTS(
					select top 1 mdcv.valueID
					from dbo.ams_memberdatacolumnValues as mdcv
					inner join dbo.cms_content as c on c.siteResourceID = mdcv.columnValueSiteResourceID
					inner join dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID and cl.languageID = 1
					inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
					where mdcv.columnID = @columnID
					and len(cv.rawContent) > 0
					and len(cv.rawContent) not between @minChars and @maxChars
				)
				RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1) 
			END
		END
		IF @minSelected is not null and @maxSelected is not null BEGIN
			IF EXISTS(select columnID from dbo.ams_memberDataColumns where columnID = @columnID and allowMultiple = 1)
			AND EXISTS(
				select top 1 md.memberid
				from dbo.ams_memberData as md
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
				where mdcv.columnID = @columnID
				group by md.memberid
				having count(*) not between @minSelected and @maxSelected
			)
			RAISERROR('There are existing members that have field options that are outside the data validation range.', 16, 1) 
		END
		IF @minValueInt is not null and @maxValueInt is not null BEGIN
			IF EXISTS(
				select top 1 valueID
				from dbo.ams_memberdatacolumnValues
				where columnID = @columnID
				and columnValueInteger is not null
				and columnValueInteger not between @minValueInt and @maxValueInt
			)
			RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1) 
		END
		IF @minValueDecimal2 is not null and @maxValueDecimal2 is not null BEGIN
			IF EXISTS(
				select top 1 valueID
				from dbo.ams_memberdatacolumnValues
				where columnID = @columnID
				and columnValueDecimal2 is not null
				and columnValueDecimal2 not between @minValueDecimal2 and @maxValueDecimal2
			)
			RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1) 
		END
		IF @minValueDate is not null and @maxValueDate is not null BEGIN
			IF EXISTS(
				select top 1 valueID
				from dbo.ams_memberdatacolumnValues
				where columnID = @columnID
				and columnValueDate is not null
				and columnValueDate not between @minValueDate and @maxValueDate
			)
			RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1) 
		END


		-- if there was a change in columnname
		IF @oldColumnName <> @columnName COLLATE Latin1_General_CS_AI BEGIN
			-- update member fields
			UPDATE dbo.ams_memberFields
			SET dbField = @columnName
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))
		
			-- update virtual group conditions
			UPDATE dbo.ams_virtualGroupConditions
			SET [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))
		END

		IF @oldColumnName <> @columnName OR @dataTypeCode <> @olddataTypeCode BEGIN
			EXEC dbo.ams_createVWMemberData	@orgID=@orgID
		END


	END

	IF @TranCounter = 0
		COMMIT TRAN;

	-- if we need to call processMemberGroups
	IF @conditionIDList is not null BEGIN
		declare @itemGroupUID uniqueidentifier
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberIDList, @conditionIDList=@conditionIDList, @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT
	END

	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER FUNCTION [dbo].[fn_getMemberDataColumnStructureXML] (@orgID int)
RETURNS xml
AS
BEGIN
	DECLARE @xmlStructure xml
	
	SELECT @xmlStructure = (
		select (
			select [field].uid, [field].columnName, [field].skipImport, isnull(mdata.uid,'') as dataTypeUID,
				isnull(mdisp.uid,'') as displayTypeUID, isnull(mdata.dataTypeCode,'') as dataTypeCode,
				isnull(mdisp.displayTypeCode,'') as displayTypeCode, isnull([field].ColumnDesc,'') as ColumnDesc, 
				[field].allowNewValuesOnImport, [field].allowNull, 
				isNull(cast([field].minChars as varchar(10)),'') as minChars,
				isNull(cast([field].maxChars as varchar(10)),'') as maxChars,
				isNull(cast([field].minSelected as varchar(10)),'') as minSelected,
				isNull(cast([field].maxSelected as varchar(10)),'') as maxSelected,
				isNull(cast([field].minValueInt as varchar(10)),'') as minValueInt,
				isNull(cast([field].maxValueInt as varchar(10)),'') as maxValueInt,
				isNull(cast([field].minValueDecimal2 as varchar(12)),'') as minValueDecimal2,
				isNull(cast([field].maxValueDecimal2 as varchar(12)),'') as maxValueDecimal2,
				isNull(convert(varchar(10),[field].minValueDate,101),'') as minValueDate,
				isNull(convert(varchar(10),[field].maxValueDate,101),'') as maxValueDate,
				isNull(cast([field].defaultValueID as varchar(20)),'') as defaultValueID, 
				isnull(case when mdisp.displayTypeCode = 'RADIO' OR mdisp.displayTypeCode = 'SELECT' then
					case mdata.dataTypeCode
						when 'STRING' then mdcv.columnValueString
						when 'DECIMAL2' then convert(varchar(255), mdcv.columnValueDecimal2)
						when 'INTEGER' then convert(varchar(255), mdcv.columnValueInteger)
						when 'DATE' then convert(varchar(255), mdcv.columnValueDate)
						when 'BIT' then convert(varchar(255), mdcv.columnValueBit)
						when 'XML' then convert(varchar(255), mdcv.columnValueXML)
						else null
						end
				else null	
				end,'') as defaultValue, [field].isReadOnly, [field].allowMultiple,
				[value].valueID, 
					case mdata.dataTypeCode
						when 'STRING' then [value].columnValueString
						when 'DECIMAL2' then convert(varchar(255), [value].columnValueDecimal2)
						when 'INTEGER' then convert(varchar(255), [value].columnValueInteger)
						when 'DATE' then convert(varchar(255), [value].columnValueDate)
						when 'BIT' then convert(varchar(255), [value].columnValueBit)
						when 'XML' then convert(varchar(255), [value].columnValueXML)
						else NULL
						end as fieldValue, 
					case when mdisp.displayTypeCode = 'RADIO' OR mdisp.displayTypeCode = 'SELECT' then isnull(mdisp.displayTypeCode,'') else null end as displayTypeCode,
					case when mdisp.displayTypeCode = 'RADIO' OR mdisp.displayTypeCode = 'SELECT' then isnull(mdata.dataTypeCode,'') else null end as dataTypeCode
			from dbo.ams_memberDataColumns as [field]
			inner join dbo.ams_memberDataColumnDataTypes as mdata on mdata.dataTypeID = [field].dataTypeID
			inner join dbo.ams_memberDataColumnDisplayTypes as mdisp on mdisp.displayTypeID = [field].displayTypeID
			left outer join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = [field].defaultValueID and mdcv.columnID = [field].columnID
			left outer join dbo.ams_memberDataColumnValues as [value] on [value].columnID = [field].columnID and (mdisp.displayTypeCode = 'RADIO' OR mdisp.displayTypeCode = 'SELECT')
			where [field].orgID = @orgID
			order by [field].columnName, fieldValue
			FOR XML AUTO, root('customfields'), TYPE
			)
		FOR XML RAW('customfieldstruct'), TYPE
		)

	RETURN @xmlStructure

END
GO

ALTER PROC [dbo].[ams_createMemberDataColumnValue]
@columnID int,
@columnValue varchar(max),
@valueID int OUTPUT

AS

-- ensure null
SELECT @valueID = null

-- get data type code for column
DECLARE @columnDataTypeCode varchar(20), @minChars int, @maxChars int, @minValueDecimal2 decimal(9,2), @maxValueDecimal2 decimal(9,2),
	@minValueInt int, @maxValueInt int, @minValueDate datetime, @maxValueDate datetime
SELECT @columnDataTypeCode=dt.dataTypeCode, @minChars=minChars, @maxChars=maxChars, @minValueDecimal2=minValueDecimal2,
	@maxValueDecimal2=maxValueDecimal2, @minValueInt=minValueInt, @maxValueInt=maxValueInt, @minValueDate=minValueDate,
	@maxValueDate=maxValueDate
FROM dbo.ams_memberDataColumnDataTypes as dt
INNER JOIN dbo.ams_memberDataColumns as c on c.dataTypeID = dt.dataTypeID
WHERE c.columnID = @columnID

-- check for existing value. if not there, add it
IF @columnDataTypeCode = 'STRING' BEGIN
	DECLARE @realColumnValue varchar(255)
	SELECT @realColumnValue = @columnValue

	SELECT @valueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueString = @realColumnValue COLLATE Latin1_General_CS_AS
	IF @valueID is null BEGIN
		IF @minChars is not null and @maxChars is not null AND len(@realColumnValue) not between @minChars and @maxChars
			GOTO on_error

		INSERT INTO dbo.ams_memberDataColumnValues (columnID, columnValueString)
		VALUES (@columnID, @realColumnValue)
			IF @@ERROR <> 0 GOTO on_error
			SELECT @valueID = SCOPE_IDENTITY()
	END
END

IF @columnDataTypeCode = 'DECIMAL2' BEGIN
	DECLARE @realColumnValue2 decimal(9,2)
	SELECT @realColumnValue2 = cast(@columnValue as decimal(9,2))
		IF @@ERROR <> 0 GOTO on_error

	SELECT @valueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueDecimal2 = @realColumnValue2
	IF @valueID is null BEGIN
		IF @minValueDecimal2 is not null and @maxValueDecimal2 is not null AND @realColumnValue2 not between @minValueDecimal2 and @maxValueDecimal2
			GOTO on_error

		INSERT INTO dbo.ams_memberDataColumnValues (columnID, columnValueDecimal2)
		VALUES (@columnID, @realColumnValue2)
			IF @@ERROR <> 0 GOTO on_error
			SELECT @valueID = SCOPE_IDENTITY()
	END
END

IF @columnDataTypeCode = 'INTEGER' BEGIN
	DECLARE @realColumnValue3 int
	SELECT @realColumnValue3 = cast(@columnValue as int)
		IF @@ERROR <> 0 GOTO on_error

	SELECT @valueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueInteger = @realColumnValue3
	IF @valueID is null BEGIN
		IF @minValueInt is not null and @maxValueInt is not null AND @realColumnValue3 not between @minValueInt and @maxValueInt
			GOTO on_error

		INSERT INTO dbo.ams_memberDataColumnValues (columnID, columnValueInteger)
		VALUES (@columnID, @realColumnValue3)
			IF @@ERROR <> 0 GOTO on_error
			SELECT @valueID = SCOPE_IDENTITY()
	END
END

IF @columnDataTypeCode = 'DATE' BEGIN
	DECLARE @realColumnValue4 datetime
	SELECT @realColumnValue4 = DATEADD(dd,DATEDIFF(dd,0,cast(@columnValue as datetime)),0)
		IF @@ERROR <> 0 GOTO on_error

	SELECT @valueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueDate = @realColumnValue4
	IF @valueID is null BEGIN
		IF @minValueDate is not null and @maxValueDate is not null AND @realColumnValue4 not between @minValueDate and @maxValueDate
			GOTO on_error

		INSERT INTO dbo.ams_memberDataColumnValues (columnID, columnValueDate)
		VALUES (@columnID, @realColumnValue4)
			IF @@ERROR <> 0 GOTO on_error
			SELECT @valueID = SCOPE_IDENTITY()
	END
END

IF @columnDataTypeCode = 'BIT' BEGIN
	DECLARE @realColumnValue5 bit
	SELECT @realColumnValue5 = cast(@columnValue as bit)
		IF @@ERROR <> 0 GOTO on_error

	SELECT @valueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueBit = @realColumnValue5
	IF @valueID is null BEGIN
		INSERT INTO dbo.ams_memberDataColumnValues (columnID, columnValueBit)
		VALUES (@columnID, @realColumnValue5)
			IF @@ERROR <> 0 GOTO on_error
			SELECT @valueID = SCOPE_IDENTITY()
	END
END

IF @columnDataTypeCode = 'XML' BEGIN
	DECLARE @realColumnValue6 xml
	SELECT @realColumnValue6 = cast(@columnValue as xml)
		IF @@ERROR <> 0 GOTO on_error

	SELECT @valueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and cast(columnValueXML as varchar(max)) = cast(@realColumnValue6 as varchar(max))
	IF @valueID is null BEGIN
		INSERT INTO dbo.ams_memberDataColumnValues (columnID, columnValueXML)
		VALUES (@columnID, @realColumnValue6)
			IF @@ERROR <> 0 GOTO on_error
			SELECT @valueID = SCOPE_IDENTITY()
	END
END

IF @columnDataTypeCode = 'CONTENTOBJ' OR @columnDataTypeCode = 'DOCUMENTOBJ' BEGIN
	DECLARE @realColumnValue7 int
	SELECT @realColumnValue7 = cast(@columnValue as int)
		IF @@ERROR <> 0 GOTO on_error

	SELECT @valueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueSiteResourceID = @realColumnValue7
	IF @valueID is null BEGIN
		INSERT INTO dbo.ams_memberDataColumnValues (columnID, columnValueSiteResourceID)
		VALUES (@columnID, @realColumnValue7)
			IF @@ERROR <> 0 GOTO on_error
			SELECT @valueID = SCOPE_IDENTITY()
	END
END

RETURN 0

-- error exit
on_error:
	SELECT @valueID = 0
	RETURN -1
GO

ALTER PROC [dbo].[migrate_customfields]
@orgcode varchar(5)

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- this can be called if orgcode on old, sitecode on new, and orgcode on new are all the same

	declare @newcolumnid int, @oldcolumnID int, @valueID int, @mingrp int, @minODID int, @mingrpNew int,
		@orgID int, @rc int, @conditionID int, @ruleID int
	declare @colList varchar(max), @mincol varchar(255), @minOD varchar(max), @fieldcode varchar(60),
		@errMsg varchar(800), @x varchar(400), @s varchar(400), @rulename varchar(400), @ruleSQL varchar(max), 
		@valueXML xml
	declare @conditionUID uniqueidentifier
	SET @orgID = dbo.fn_getOrgIDFromOrgCode(@orgcode)
		
	select @colList = replace(replace((
		select replace(optionDescription,' ','^') as [data()]
		from tlasites.trialsmith.dbo.orgMemberOptionNames
		where orgcode = @orgcode
		FOR XML PATH ('')), ' ', ','),'^',' ')
	select @mincol = min(listitem) from dbo.fn_varCharListToTable(@colList,',')
	while @minCol is not null BEGIN	
		select @oldcolumnID = null
		select @newcolumnID = null
		select @oldcolumnID = optionNameID from tlasites.trialsmith.dbo.orgmemberOptionNames where orgcode=@orgcode and optionDescription=@minCol
		select @newcolumnID = columnID from dbo.ams_memberDataColumns where orgID=@orgid and columnName=@minCol
		IF @newcolumnID is null BEGIN

			-- create it as a select/string field if there are options on old
			IF EXISTS (
				select omoc.optionCodeID
				from tlasites.trialsmith.dbo.orgMemberOptionCodes as omoc
				where omoc.optionNameID = @oldcolumnID
				) BEGIN
				EXEC dbo.ams_createMemberDataColumn @orgID=@orgID, @columnName=@minCol, @columnDesc=@minCol, @allowMultiple=0, @skipImport=0, 
					@allowNull=1, @defaultValue=null, @allowNewValuesOnImport=1, @dataTypeCode='STRING', @displayTypeCode='SELECT', @isReadOnly=0, 
					@minChars=null, @maxChars=null, @minSelected=null, @maxSelected=null, @minValueInt=null, @maxValueInt=null, 
					@minValueDecimal2=null, @maxValueDecimal2=null, @minValueDate=null, @maxValueDate=null, @columnID=@newcolumnID OUTPUT

				select @minOD = null
				select @minOD = min(optionCodeDescription) from tlasites.trialsmith.dbo.orgMemberOptionCodes where optionNameID = @oldcolumnID
				while @minOD is not null BEGIN
					EXEC dbo.ams_createMemberDataColumnValue @columnID=@newcolumnID, @columnValue=@minOD, @valueID=@valueID OUTPUT
						IF @valueID = 0 RAISERROR('Unable to create column value.', 16, 1) 

					-- if there are groups tied to the option on old, create those links on new
					IF EXISTS (
						SELECT omog.usergroupid
						from tlasites.trialsmith.dbo.orgMemberOptionGroups as omog
						inner join tlasites.trialsmith.dbo.orgMemberOptionCodes as omoc on omoc.optionCodeID = omog.optionCodeID
						where omoc.optionCodeDescription = @minOD
						AND omoc.optionNameID = @oldcolumnID
					) BEGIN

						-- create condition
						select @fieldcode = null
						select @fieldcode = 'md_' + cast(@newcolumnID as varchar(5))
						set @valueXML = '<values><value key="value" value="' + cast(@valueID as varchar(20)) + '"/></values>'

						EXEC dbo.ams_createVirtualGroupCondition @orgID=@orgID, @conditionTypeID=1, @fieldCode=@fieldcode, @expression='eq', @datepart=null, @dateExpression=null, @value=@valueXML, @isDefined=1, @bypassQueue=1, @conditionID=@conditionID OUTPUT
						SELECT @conditionUID = [uid] from dbo.ams_virtualGroupConditions where conditionID = @conditionID

						-- create rule
						SET @x = null
						SET @s = null
						SET @rulename = null
						SET @x = '<rule><conditionset op="AND" act="include" id="' + cast(newid() as varchar(255)) + '"><condition id="' + cast(@conditionUID as varchar(50)) + '" /></conditionset></rule>'; 
						SET @rulename = @minCol + ' - ' + @minOD
						EXEC dbo.ams_createVirtualGroupRule @orgID, 1, @rulename, @x, '', @ruleID OUTPUT
						EXEC dbo.ams_updateVirtualGroupRuleSQL @ruleID=@ruleID
									
						-- loop over the linked groups				
						select @mingrp = null
						select @mingrp = min(omog.usergroupid) 
							from tlasites.trialsmith.dbo.orgMemberOptionGroups as omog
							inner join tlasites.trialsmith.dbo.orgMemberOptionCodes as omoc on omoc.optionCodeID = omog.optionCodeID
							where omoc.optionCodeDescription = @minOD
							AND omoc.optionNameID = @oldcolumnID
						while @mingrp is not null begin

							-- create rulegroup
							select @mingrpNew = g.groupID 
								from dbo.ams_groups as g
								inner join tlasites.trialsmith.dbo.usergroups as oldg on oldg.description = g.groupName COLLATE Latin1_General_CI_AI
									and oldg.orgcode = @orgcode
								where g.orgID = @orgID 
								and oldg.usergroupID = @mingrp

							EXEC dbo.ams_createVirtualGroupRuleGroup @ruleID, @mingrpNew

							select @mingrp = min(omog.usergroupid) 
								from tlasites.trialsmith.dbo.orgMemberOptionGroups as omog
								inner join tlasites.trialsmith.dbo.orgMemberOptionCodes as omoc on omoc.optionCodeID = omog.optionCodeID
								where omoc.optionCodeDescription = @minOD
								AND omoc.optionNameID = @oldcolumnID
								AND omog.usergroupid > @mingrp
						END

					END

					select @minOD = min(optionCodeDescription) from tlasites.trialsmith.dbo.orgMemberOptionCodes where optionNameID = @oldcolumnID and optionCodeDescription > @minOD
				END

				END	
		
			-- else, create it as a textbox question
			ELSE BEGIN
				EXEC dbo.ams_createMemberDataColumn @orgID=@orgID, @columnName=@minCol, @columnDesc=@minCol, @allowMultiple=0, @skipImport=0, 
					@allowNull=1, @defaultValue=null, @allowNewValuesOnImport=1, @dataTypeCode='STRING', @displayTypeCode='TEXTBOX', @isReadOnly=0, 
					@minChars=null, @maxChars=null, @minSelected=null, @maxSelected=null, @minValueInt=null, @maxValueInt=null, 
					@minValueDecimal2=null, @maxValueDecimal2=null, @minValueDate=null, @maxValueDate=null, @columnID=@newcolumnID OUTPUT
			END
		END

		select @mincol = min(listitem) from dbo.fn_varCharListToTable(@colList,',') where listitem > @mincol
	END


	-- Activate all rules
	UPDATE ams_virtualGroupRules 
	set isActive = 1
	where orgid = @orgID

	-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
	declare @itemGroupUID uniqueidentifier
	EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList='', @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[ams_updateMemberDataColumnValue]
@valueID int,
@columnValue varchar(255)

AS

DECLARE @newvalueID int, @columnID int, @recalc bit
DECLARE @tblCond TABLE (orgID int, conditionID int)
SET @recalc = 0

-- if valueID not passed do nothing.
IF @valueID is null or @columnValue is null
	GOTO on_error

-- get data type code for column
DECLARE @columnDataTypeCode varchar(20), @minChars int, @maxChars int, @minValueDecimal2 decimal(9,2), @maxValueDecimal2 decimal(9,2),
	@minValueInt int, @maxValueInt int, @minValueDate datetime, @maxValueDate datetime
SELECT @columnDataTypeCode = dt.dataTypeCode, @columnID=c.columnID, @minChars=c.minChars, @maxChars=c.maxChars, @minValueDecimal2=c.minValueDecimal2,
	@maxValueDecimal2=c.maxValueDecimal2, @minValueInt=c.minValueInt, @maxValueInt=c.maxValueInt, @minValueDate=c.minValueDate,
	@maxValueDate=c.maxValueDate
from dbo.ams_memberDataColumnValues as cv
inner join dbo.ams_memberDataColumns as c on c.columnID = cv.columnID
inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = c.dataTypeID
where cv.valueID = @valueID

IF @columnDataTypeCode = 'STRING' BEGIN
	DECLARE @realColumnValue varchar(255)
	SELECT @realColumnValue = @columnValue

	SELECT @newvalueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueString = @realColumnValue COLLATE Latin1_General_CS_AS
	IF @newvalueID is null BEGIN
		IF @minChars is not null and @maxChars is not null AND len(@realColumnValue) not between @minChars and @maxChars
			GOTO on_error

		UPDATE dbo.ams_memberDataColumnValues 
		SET columnValueString = @realColumnValue
		WHERE valueID = @valueID
			IF @@ERROR <> 0 GOTO on_error
		SET @recalc = 1
	END
END

IF @columnDataTypeCode = 'DECIMAL2' BEGIN
	DECLARE @realColumnValue2 decimal(9,2)
	SELECT @realColumnValue2 = cast(@columnValue as decimal(9,2))
		IF @@ERROR <> 0 GOTO on_error

	SELECT @newvalueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueDecimal2 = @realColumnValue2
	IF @newvalueID is null BEGIN
		IF @minValueDecimal2 is not null and @maxValueDecimal2 is not null AND @realColumnValue2 not between @minValueDecimal2 and @maxValueDecimal2
			GOTO on_error

		UPDATE dbo.ams_memberDataColumnValues 
		SET columnValueDecimal2 = @realColumnValue2
		WHERE valueID = @valueID
			IF @@ERROR <> 0 GOTO on_error
		SET @recalc = 1
	END
END

IF @columnDataTypeCode = 'INTEGER' BEGIN
	DECLARE @realColumnValue3 int
	SELECT @realColumnValue3 = cast(@columnValue as int)
		IF @@ERROR <> 0 GOTO on_error

	SELECT @newvalueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueInteger = @realColumnValue3
	IF @newvalueID is null BEGIN
		IF @minValueInt is not null and @maxValueInt is not null AND @realColumnValue3 not between @minValueInt and @maxValueInt
			GOTO on_error

		UPDATE dbo.ams_memberDataColumnValues 
		SET columnValueInteger = @realColumnValue3
		WHERE valueID = @valueID
			IF @@ERROR <> 0 GOTO on_error
		SET @recalc = 1
	END
END

IF @columnDataTypeCode = 'DATE' BEGIN
	DECLARE @realColumnValue4 datetime
	SELECT @realColumnValue4 = DATEADD(dd,DATEDIFF(dd,0,cast(@columnValue as datetime)),0)
		IF @@ERROR <> 0 GOTO on_error

	SELECT @newvalueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueDate = @realColumnValue4
	IF @newvalueID is null BEGIN
		IF @minValueDate is not null and @maxValueDate is not null AND @realColumnValue4 not between @minValueDate and @maxValueDate
			GOTO on_error

		UPDATE dbo.ams_memberDataColumnValues 
		SET columnValueDate = @realColumnValue4
		WHERE valueID = @valueID
			IF @@ERROR <> 0 GOTO on_error
		SET @recalc = 1
	END
END

IF @columnDataTypeCode = 'BIT' BEGIN
	DECLARE @realColumnValue5 bit
	SELECT @realColumnValue5 = cast(@columnValue as bit)
		IF @@ERROR <> 0 GOTO on_error

	SELECT @newvalueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueBit = @realColumnValue5
	IF @newvalueID is null BEGIN
		UPDATE dbo.ams_memberDataColumnValues 
		SET columnValueBit = @realColumnValue5
		WHERE valueID = @valueID
			IF @@ERROR <> 0 GOTO on_error
		SET @recalc = 1
	END
END

IF @columnDataTypeCode = 'XML' BEGIN
	DECLARE @realColumnValue6 xml
	SELECT @realColumnValue6 = cast(@columnValue as xml)
		IF @@ERROR <> 0 GOTO on_error

	SELECT @newvalueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and cast(columnValueXML as varchar(max)) = cast(@realColumnValue6 as varchar(max))
	IF @newvalueID is null BEGIN
		UPDATE dbo.ams_memberDataColumnValues 
		SET columnValueXML = @realColumnValue6
		WHERE valueID = @valueID
			IF @@ERROR <> 0 GOTO on_error
		SET @recalc = 1
	END
END

IF @columnDataTypeCode = 'CONTENTOBJ' OR @columnDataTypeCode = 'DOCUMENTOBJ' BEGIN
	DECLARE @realColumnValue7 int
	SELECT @realColumnValue7 = cast(@columnValue as int)
		IF @@ERROR <> 0 GOTO on_error

	SELECT @newvalueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueSiteResourceID = @realColumnValue7
	IF @newvalueID is null BEGIN
		UPDATE dbo.ams_memberDataColumnValues 
		SET columnValueSiteResourceID = @realColumnValue7
		WHERE valueID = @valueID
			IF @@ERROR <> 0 GOTO on_error
		SET @recalc = 1
	END
END

IF @recalc = 1 BEGIN
	INSERT INTO @tblCond (orgID, conditionID)
	select distinct mdc.orgID, vgc.conditionID
	from dbo.ams_memberDataColumnValues as mdcv
	inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
	inner join dbo.ams_virtualGroupConditions as vgc on vgc.fieldcode = 'md_' + cast(mdc.columnID as varchar(10))
	inner join dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
		and vgcv.conditionValue = cast(@valueID as varchar(10))
	where mdcv.valueID = @valueID
		IF @@ERROR <> 0 GOTO on_error

	UPDATE vgc
	set vgc.verbose = dbo.ams_getVirtualGroupConditionVerbose(vgc.conditionID)
	from dbo.ams_virtualGroupConditions as vgc
	inner join @tblCond as tbl on tbl.conditionID = vgc.conditionID
		IF @@ERROR <> 0 GOTO on_error

	-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
	declare @itemGroupUID uniqueidentifier, @orgID int, @conditionIDList varchar(max)
	SELECT top 1 @orgID = orgID from @tblCond
	SELECT @conditionIDList = COALESCE(@conditionIDList + ',', '') + cast(c.conditionID as varchar(10)) 
		from @tblCond as c
		group by c.conditionID
	IF @conditionIDList is not null BEGIN
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList='', @conditionIDList=@conditionIDList, @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	END
END

RETURN 0

-- error exit
on_error:
	RETURN -1
GO

ALTER PROC [dbo].[ams_saveMemberData]
@memberID int,
@columnID int,
@columnvalueID int = NULL,
@columnValue varchar(max) = NULL,
@byPassQueue bit = 0

AS

/* ****************************************************************
In order to enforce a custom field's min/max selected validation range, 
this procedure should NOT be called directly for custom field that 
accept multiple values. Call ams_setMemberData instead.
******************************************************************* */

BEGIN TRAN

IF @columnvalueID is null BEGIN
	IF len(@columnValue) > 0 BEGIN
		DECLARE @rc int
		EXEC @rc = dbo.ams_createMemberDataColumnValue @columnID, @columnValue, @columnvalueID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @columnvalueID = 0 GOTO on_error
	END 
	ELSE BEGIN
		goto on_ok
	END
END

if not exists (select dataid from dbo.ams_memberData where memberid = @memberID and valueID = @columnValueID) BEGIN
	-- add member data
	INSERT INTO dbo.ams_memberData (memberid, valueID)
	VALUES (@memberID, @columnvalueID)
		IF @@ERROR <> 0 GOTO on_error

	-- set member as updated
	UPDATE dbo.ams_members
	SET dateLastUpdated = getdate()
	WHERE memberID = @memberID
		IF @@ERROR <> 0 GOTO on_error

	-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
	IF @byPassQueue	= 0 BEGIN
		declare @itemGroupUID uniqueidentifier, @orgID int, @conditionIDList varchar(max)
		SELECT @orgID = orgID from dbo.ams_members where memberID = @memberID	
		SELECT @conditionIDList = COALESCE(@conditionIDList + ',', '') + cast(c.conditionID as varchar(10)) 
			from dbo.ams_virtualGroupConditions as c
			where c.orgID = @orgID
			and c.fieldcode = 'md_' + cast(@columnID as varchar(10))
			group by c.conditionID
		IF @conditionIDList is not null BEGIN
			EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberID, @conditionIDList=@conditionIDList, @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT
			IF @@ERROR <> 0 GOTO on_error
		END
	END
end

-- normal exit
on_ok:
	IF @@TRANCOUNT > 0 COMMIT TRAN
	RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

use customApps
GO
ALTER PROC [dbo].[caaa_updateHomeChapterRO]
AS

	declare @orgID int
	declare @tblMembers table(memberID int, homeChapter varchar(255))
	declare @tblMembersRO table(memberID int, homeChapter varchar(255))
	declare @tblMembersToUpdate table(tid int identity(1,1), memberID int, homeChapter varchar(255), roHomeChapter varchar(255))

	declare @homeChapterID int, @roHomeChapterID int

	/* get the org */
	select @orgID = orgID
	from membercentral.dbo.organizations
	where orgCode = 'CAAA'

	/* get the columns */
	select @homeChapterID = columnID
	from membercentral.dbo.ams_memberDataColumns
	where orgID = @orgID
	and columnName = 'Home Chapter'

	select @roHomeChapterID = columnID
	from membercentral.dbo.ams_memberDataColumns
	where orgID = @orgID
	and columnName = 'Home Chapter RO'

	/* get the values for the home chapter */
	insert into @tblMembers(memberID, homeChapter)
	select md.memberID, mdcv.columnValueString as homeChapter
	from membercentral.dbo.ams_memberData md
	inner join membercentral.dbo.ams_memberDataColumnValues mdcv
		on mdcv.valueID = md.valueID
		and mdcv.columnID = @homeChapterID

	/* get the values for the read only home chapter */
	insert into @tblMembersRO(memberID, homeChapter)
	select md.memberID, mdcv.columnValueString as homeChapter
	from membercentral.dbo.ams_memberData md
	inner join membercentral.dbo.ams_memberDataColumnValues mdcv
		on mdcv.valueID = md.valueID
		and mdcv.columnID = @roHomeChapterID


	/* union the two so that we get differences including nulls on both sides */
	insert into @tblMembersToUpdate(memberID, homeChapter, roHomeChapter)
	select memberID, homeChapter, roHomeChapter
	from (
	select tm.memberID, tm.homeChapter, tmro.homeChapter as roHomeChapter
	from @tblMembers tm
	left outer join @tblMembersRO tmro
		on tmro.memberID = tm.memberID
	where IsNull(tm.homeChapter,'*NULL*') <> IsNull(tmro.homeChapter,'*NULL*')

	union all

	select tmro.memberID, tm.homeChapter, tmro.homeChapter as roHomeChapter
	from @tblMembersRO tmro
	left outer join @tblMembers tm
		on tm.memberID = tmro.memberID
	where IsNull(tm.homeChapter,'*NULL*') <> IsNull(tmro.homeChapter,'*NULL*')
	) x
	group by memberID, homeChapter, roHomeChapter

	/* delete read only values for those that were different */
	delete from membercentral.dbo.ams_memberData
	where dataID in (
		SELECT md.dataID
		FROM membercentral.dbo.ams_memberData as md
		INNER JOIN @tblMembersToUpdate as tmtu ON tmtu.memberID = md.memberID
		INNER JOIN membercentral.dbo.ams_memberDataColumnValues as mdcv ON md.valueID = mdcv.valueID 
		INNER JOIN membercentral.dbo.ams_memberDataColumns as mdc ON mdcv.columnID = mdc.columnID
		WHERE mdcv.columnID = @roHomeChapterID
	)

	/* update the value for those that were different and not changed to null */
	declare @minTID int, @currMemberID int, @currHomeChapter varchar(255)

	select @minTID = min(tid)
	from @tblMembersToUpdate
	where homeChapter is not null

	while @minTID is not null
	begin
		select @currMemberID=memberID, @currHomeChapter=homeChapter
		from @tblMembersToUpdate
		where tid = @minTID
		
		EXEC membercentral.dbo.ams_setMemberData @memberID=@currMemberID, @orgID=@orgID, @columnName='Home Chapter RO', @columnValue=@currHomeChapter, @byPassQueue=1

		select @minTID = min(tid)
		from @tblMembersToUpdate
		where homeChapter is not null
		and tid > @minTID
	end

	DECLARE @memberIDList varchar(max), @itemGroupUID uniqueidentifier
	SELECT @memberIDList = COALESCE(@memberIDList + ',', '') + cast(memberID as varchar(10)) 
		from @tblMembersToUpdate
		group by memberid
	IF @memberIDList is not null BEGIN
		set @itemGroupUID = null
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberIDList, @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT
	END
GO

use membercentral
GO

ALTER PROC [dbo].[ams_setMemberData]
@memberID int,
@orgID int,
@columnName varchar(255),
@columnValue varchar(max),
@byPassQueue bit = 0

AS

DECLARE @rc int

BEGIN TRAN

-- get columnid
declare @columnID int, @allowMultiple bit, @minSelected int, @maxSelected int
select @columnID=columnID, @allowMultiple=allowMultiple, @minSelected=minSelected, @maxSelected=maxSelected
FROM dbo.ams_memberDataColumns
where orgID = @orgID
and columnName = @columnName

IF @memberID is null or @columnID is null
	GOTO on_error

-- delete records from memberdata for this column and user
EXEC @rc = dbo.ams_deleteMemberData @memberID=@memberID, @columnID=@columnID, @byPassQueue=@byPassQueue
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- if allowMultiple = 1, then @columnValue will always be a list of valueIDs.
IF @allowMultiple = 1 BEGIN

	-- save each value
	declare @thisValueID int
	select @thisValueID = min(listitem) from dbo.fn_intListToTable(@columnValue,',')
		IF @@ERROR <> 0 GOTO on_error
	while @thisValueID is not null BEGIN
		EXEC @rc = dbo.ams_saveMemberData @memberID=@memberID, @columnID=@columnID, @columnvalueID=@thisValueID, @columnValue=null, @byPassQueue=@byPassQueue
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
		select @thisValueID = min(listitem) from dbo.fn_intListToTable(@columnValue,',') where listitem > @thisValueID
			IF @@ERROR <> 0 GOTO on_error
	END

	-- validation for min/max selected
	IF @minSelected is not null and @maxSelected is not null BEGIN
		IF EXISTS(		
			select top 1 md.memberid
			from dbo.ams_memberData as md
			inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
			where mdcv.columnID = @columnID
			and md.memberid = @memberID
			group by md.memberid
			having count(*) not between @minSelected and @maxSelected
		) GOTO on_error
	END
END

-- if allowMultiple = 0, it will be a columnValue.
IF @allowMultiple = 0 BEGIN
	EXEC @rc = dbo.ams_saveMemberData @memberID=@memberID, @columnID=@columnID, @columnvalueID=NULL, @columnValue=@columnValue, @byPassQueue=@byPassQueue
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
END

IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

ALTER PROC [dbo].[ams_getOrgMemberDataColumns]
@orgID int

AS

select isnull(cast((
	select [column].columnID, [column].columnName, [column].allowNull, [column].defaultValueID, 
		[column].isReadOnly, [column].allowMultiple, 
		isNull(cast([column].minChars as varchar(10)),'') as minChars,
		isNull(cast([column].maxChars as varchar(10)),'') as maxChars,
		isNull(cast([column].minSelected as varchar(10)),'') as minSelected,
		isNull(cast([column].maxSelected as varchar(10)),'') as maxSelected,
		isNull(cast([column].minValueInt as varchar(10)),'') as minValueInt,
		isNull(cast([column].maxValueInt as varchar(10)),'') as maxValueInt,
		isNull(cast([column].minValueDecimal2 as varchar(12)),'') as minValueDecimal2,
		isNull(cast([column].maxValueDecimal2 as varchar(12)),'') as maxValueDecimal2,
		isNull(convert(varchar(10),[column].minValueDate,101),'') as minValueDate,
		isNull(convert(varchar(10),[column].maxValueDate,101),'') as maxValueDate,
		isnull([dataType].dataTypeCode,'') as dataTypeCode,
		isnull([displayType].displayTypeCode,'') as displayTypeCode,
		[columnvalue].valueID, 
		[columnvalue].columnValueString,
		[columnvalue].columnValueDecimal2,
		[columnvalue].columnValueInteger,
		[columnvalue].columnValueDate,
		[columnvalue].columnValueBit,
		[columnvalue].columnValueXML,
		[columnvalue].columnValueSiteResourceID
	from dbo.ams_memberDataColumns as [column]
	inner join dbo.ams_memberDataColumnDataTypes as [dataType] on [dataType].dataTypeID = [column].dataTypeID
	inner join dbo.ams_memberDataColumnDisplayTypes as [displayType] on [displayType].displayTypeID = [column].displayTypeID
	left outer join dbo.ams_memberDataColumnValues as [columnvalue] on [column].columnID = [columnvalue].columnID 
		and [displayType].displayTypeCode IN ('RADIO','SELECT','CHECKBOX')
	where [column].orgid = @orgid
	order by [column].columnName, [columnvalue].columnValueString,
		[columnvalue].columnValueDecimal2,
		[columnvalue].columnValueInteger,
		[columnvalue].columnValueDate,
		[columnvalue].columnValueBit,
		cast([columnvalue].columnValueXML as varchar(max)),
		[columnvalue].columnValueSiteResourceID
	for xml auto, ROOT('data') 
) as XML),'<data/>') as additionalDataXML
OPTION(RECOMPILE)
RETURN 0
GO

ALTER PROC [dbo].[ams_importMemberData_tempToHolding]
@orgid int, 
@tmptbl varchar(30),
@pathToExport varchar(100),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Starting importMemberData_toHolding'

DECLARE @prefix varchar(50), @exportcmd varchar(400), @dynSQL nvarchar(max), @flatfile varchar(160),
	@columnID int, @mincol varchar(255), @websiteType varchar(20), @emailType varchar(20), 
	@addressType varchar(20), @good bit, @importFileCol varchar(400), @defaultValueID int, 
	@colDataTypeCode varchar(20), @plType varchar(200), @allownull bit, @OrgColDataLength int,
	@ImpColDataLength int, @minChars int, @maxChars int, @minSelected int, @maxSelected int,
	@minValueInt int, @maxValueInt int, @minValueDecimal2 decimal(9,2), @maxValueDecimal2 decimal(9,2),
	@minValueDate datetime, @maxValueDate datetime
declare @tblMissingCols TABLE (colName varchar(255))
declare @tblErrors TABLE (rowid int IDENTITY(1,1), msg varchar(600), fatal bit)
declare @tblDataChanged TABLE (rowid int IDENTITY(1,1), msg varchar(600))
declare @tblCounts TABLE (rowid int IDENTITY(1,1), countName varchar(50), countNum int)
declare @tblMemList1 TABLE (memberid int, memberNumber varchar(50), firstname varchar(75), lastname varchar(75))
declare @tblMemList2 TABLE (memberid int, memberNumber varchar(50), firstname varchar(75), lastname varchar(75))
declare @tblMemList3 TABLE (rowid int, memberNumber varchar(50), firstname varchar(75), lastname varchar(75))

DECLARE @orgCode varchar(10), @hasPrefix bit, @usePrefixList bit, @hasMiddleName bit, @hasSuffix bit, @hasProfessionalSuffix bit, @hasCompany bit
select @orgcode=orgcode, @hasPrefix=hasPrefix, @usePrefixList=usePrefixList, @hasMiddleName=hasMiddleName,
	@hasSuffix=hasSuffix, @hasProfessionalSuffix=hasProfessionalSuffix, @hasCompany=hasCompany
	from dbo.organizations 
	where orgID = @orgid

declare @var_tmpCols varchar(20), @var_tmpColsSkipped varchar(30)
select @var_tmpCols = '##tmpCols' + @orgcode
select @var_tmpColsSkipped = '##tmpColsSkipped' + @orgcode

-- cleanup
IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
	EXEC('DROP TABLE ' + @var_tmpCols)
IF OBJECT_ID('tempdb..#tblOrgCols') IS NOT NULL 
	DROP TABLE #tblOrgCols
IF OBJECT_ID('tempdb..#tblImportCols') IS NOT NULL 
	DROP TABLE #tblImportCols
IF OBJECT_ID('tempdb..' + @var_tmpColsSkipped) IS NOT NULL 
	EXEC('DROP TABLE ' + @var_tmpColsSkipped)


-- ******************************** 
-- ensure all columns exist (fatal)
-- ******************************** 
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking Required Columns'

-- this will get the columns that should be there based on current setup
CREATE TABLE #tblOrgCols (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
	COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
	SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
	COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
	CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)

	-- Need the temp table code for the export. replace the first FROM occurence 
	select @dynSQL = ''
	EXEC dbo.ams_getFlattenedMemberDataSQL @orgID=@orgID, @importDataMode=1, @memberIDList='', @sql=@dynSQL OUTPUT
	select @dynSQL = stuff(@dynSQL, charIndex('from membercentral',@dynSQL), len('from membercentral'), 'into ' + @var_tmpCols + ' from membercentral')
	EXEC(@dynSQL)

	-- get cols
	INSERT INTO #tblOrgCols
	EXEC tempdb.dbo.SP_COLUMNS @var_tmpCols
	
	-- cleanup table no longer needed
	IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
		EXEC('DROP TABLE ' + @var_tmpCols)

-- this will get the columns that are actually in the import
CREATE TABLE #tblImportCols (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
	COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
	SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
	COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
	CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)

	-- get cols
	INSERT INTO #tblImportCols
	EXEC tempdb.dbo.SP_COLUMNS @tmptbl

INSERT INTO @tblErrors (msg, fatal)
select 'The column ' + org.column_name + ' is missing from your data.', 1
from #tblOrgCols as org
left outer join #tblImportCols as imp on imp.column_name = org.column_name
where imp.table_name is null

insert into @tblMissingCols(colname)
select org.column_name
from #tblOrgCols as org
left outer join #tblImportCols as imp on imp.column_name = org.column_name
where imp.table_name is null


-- ********************************
-- data type checks (fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking DATE columns'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and dt.dataTypeCode = 'DATE'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'datetime')
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			UPDATE ' + @tmptbl + ' set ' + quotename(@mincol) + ' = null where ' + quotename(@mincol) + ' = '''';
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' datetime null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid dates.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and dt.dataTypeCode = 'DATE'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'datetime')
		where mdc.columnname > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking DECIMAL2 columns'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 0
		and dt.dataTypeCode = 'DECIMAL2'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'decimal')
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			UPDATE ' + @tmptbl + ' SET ' + quotename(@mincol) + ' = replace(' + quotename(@mincol) + ','','','''');
			UPDATE ' + @tmptbl + ' SET ' + quotename(@mincol) + ' = null where ' + quotename(@mincol) + ' = '''';
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' decimal(9,2) null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid decimal values.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and mdc.allowMultiple = 0
			and dt.dataTypeCode = 'DECIMAL2'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'decimal')
		where mdc.columnname > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking DECIMAL2 columns (multiple values)'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 1
		and dt.dataTypeCode = 'DECIMAL2'
		and mdc.columnname not in (select colName from @tblMissingCols)
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' varchar(max) null;

			IF EXISTS (
				select top 1 tbl.listItem
				from ' + @tmptbl + ' 
				cross apply dbo.fn_decimal2ListToTable(' + quotename(@mincol) + ',''|'') as tbl
				where nullif(' + quotename(@mincol) + ','''') is not null
			) set @good = @good
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid decimal values.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and mdc.allowMultiple = 1
			and dt.dataTypeCode = 'DECIMAL2'
			and mdc.columnname not in (select colName from @tblMissingCols)
		where mdc.columnname > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking INTEGER columns'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 0
		and dt.dataTypeCode = 'INTEGER'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and mdc.columnname not in (select column_Name from #tblImportCols where left(type_name,3) = 'int')
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			UPDATE ' + @tmptbl + ' SET ' + quotename(@mincol) + ' = replace(' + quotename(@mincol) + ','','','''');
			UPDATE ' + @tmptbl + ' SET ' + quotename(@mincol) + ' = null where ' + quotename(@mincol) + ' = '''';
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' int null
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid whole number values.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and mdc.allowMultiple = 0
			and dt.dataTypeCode = 'INTEGER'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnname not in (select column_Name from #tblImportCols where left(type_name,3) = 'int')
		where mdc.columnname > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking INTEGER columns (multiple values)'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 1
		and dt.dataTypeCode = 'INTEGER'
		and mdc.columnname not in (select colName from @tblMissingCols)
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' varchar(max) null;

			IF EXISTS (			
				select top 1 tbl.listItem
				from ' + @tmptbl + ' 
				cross apply dbo.fn_intListToTable(' + quotename(@mincol) + ',''|'') as tbl
				where nullif(' + quotename(@mincol) + ','''') is not null
			) set @good = @good
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid whole number values.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and mdc.allowMultiple = 1
			and dt.dataTypeCode = 'INTEGER'
			and mdc.columnname not in (select colName from @tblMissingCols)
		where mdc.columnname > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking BIT columns'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and dt.dataTypeCode = 'BIT'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'bit')
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			UPDATE ' + @tmptbl + ' SET ' + quotename(@mincol) + ' = 1 where ' + quotename(@mincol) + ' = ''TRUE'' OR ' + quotename(@mincol) + ' = ''YES'';
			UPDATE ' + @tmptbl + ' SET ' + quotename(@mincol) + ' = 0 where ' + quotename(@mincol) + ' = ''FALSE'' OR ' + quotename(@mincol) + ' = ''NO'';
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' bit null
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid boolean values.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and dt.dataTypeCode = 'BIT'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'bit')
		where mdc.columnname > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking XML columns'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and dt.dataTypeCode = 'XML'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'xml')
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' xml null
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid XML values.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and dt.dataTypeCode = 'XML'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'xml')
		where mdc.columnname > @mincol
END

-- *********************************************************************
-- ensure all varchar columns match data length requirements (non-fatal)
-- *********************************************************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking VARCHAR columns for lengths'
select @mincol = null
select @mincol = min(column_name)
	from #tblOrgCols
	where type_Name = 'varchar'
	and [length] > 0
	and column_name not in (select colName from @tblMissingCols)
	and column_name in (select column_Name from #tblImportCols where type_Name = 'varchar' and [length] > 0)
while @mincol is not null BEGIN
	select @OrgColDataLength = null, @ImpColDataLength = null
	select @OrgColDataLength = [length] from #tblOrgCols where column_name = @mincol
	select @ImpColDataLength = [length] from #tblImportCols where column_name = @mincol

	-- if length of orgcol < length of importcol, report on data truncation
	IF @OrgColDataLength < @ImpColDataLength BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '') has invalid data for ' + quoteName(@mincol) + '. The maximum number of characters for this column is ' + cast(@OrgColDataLength as varchar(5)) + '.'' as msg, 0 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE len(' + quotename(@mincol) + ') > ' + cast(@OrgColDataLength as varchar(5)) + '
			ORDER BY rowID'
		INSERT INTO @tblErrors (msg, fatal)
		EXEC(@dynSQL)

		IF @@ROWCOUNT > 0 BEGIN
			select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '') will truncate ' + quoteName(@mincol) + ' to fit.'' as msg
				FROM ' + @tmptbl + ' 
				WHERE len(' + quotename(@mincol) + ') > ' + cast(@OrgColDataLength as varchar(5)) + '
				ORDER BY rowID'
			INSERT INTO @tblDataChanged (msg)
			EXEC(@dynSQL)

			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@mincol) + ' = left(' + quotename(@mincol) + ',' + cast(@OrgColDataLength as varchar(5)) + ')
				WHERE len(' + quotename(@mincol) + ') > ' + cast(@OrgColDataLength as varchar(5)) + ' '
			EXEC(@dynSQL)
		END
	END

	-- if length of orgcol <> length of importcol, alter table so it matches.
	IF @OrgColDataLength <> @ImpColDataLength BEGIN
		set @good = 1
		set @dynSQL = '
			set @good = 1
			BEGIN TRY
				ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' varchar(' + cast(@OrgColDataLength as varchar(5)) + ') null;
			END TRY
			BEGIN CATCH
				set @good = 0
			END CATCH'
			exec sp_executesql @dynSQL, N'@good bit output', @good output
		IF @good = 0
			INSERT INTO @tblErrors (msg, fatal)
			VALUES ('The column ' + @mincol + ' could not be expanded to support ' + cast(@OrgColDataLength as varchar(5)) + ' characters.', 1)
	END

	select @mincol = min(column_name)
		from #tblOrgCols
		where type_Name = 'varchar'
		and [length] > 0
		and column_name not in (select colName from @tblMissingCols)
		and column_name in (select column_Name from #tblImportCols where type_Name = 'varchar' and [length] > 0)
		and column_name > @mincol
END


-- ********************************
-- no member number (fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying missing member numbers'

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'memberNumber') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '') is missing a Member Number. Member Numbers are required for all members.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE (memberNumber IS NULL OR ltrim(rtrim(memberNumber)) = '''')
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

-- ********************************
-- dupe member numbers (fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying duplicate member numbers'

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'memberNumber') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''The Member Number '' + isnull(memberNumber,'''') + '' appears '' + cast(count(*) as varchar(10)) + '' times. Member Numbers must be unique.'' as msg, 1 as fatal
			FROM ' + @tmptbl + ' 
			GROUP BY memberNumber
			HAVING COUNT(*) > 1
			ORDER BY 1'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

-- ********************************
-- conflicting member numbers with guest accounts (fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying conflicting member numbers'

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'memberNumber') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''The Member Number '' + isnull(tbl.memberNumber,'''') + '' belongs to an existing guest account.'' as msg, 1 as fatal
			FROM ' + @tmptbl + ' as tbl 
			WHERE isnull(tbl.memberNumber,'''')	<> ''''
			AND EXISTS (
				select memberID
				from dbo.ams_members
				where status <> ''D''
				and orgID = ' + cast(@orgID as varchar(6)) + ' 
				and memberTypeID = 1
				and membernumber = tbl.memberNumber
			)
			ORDER BY 1'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

-- ********************************
-- no first name (fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying missing first names'

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'firstname') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') is missing a First Name. First Names are required for all members.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE (firstname IS NULL OR ltrim(rtrim(firstname)) = '''')
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

-- ********************************
-- no last name (fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying missing last names'

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'lastname') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') is missing a Last Name. Last Names are required for all members.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE (lastname IS NULL OR ltrim(rtrim(lastname)) = '''')
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

-- ********************************
-- bad website data (non-fatal)
-- be nice and clean up their website data first (ensure http:// in front)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying invalid website data'

declare @tldList varchar(max)
set @tldList = 'com|edu|gov|int|mil|net|org|arpa|coop|asia|cat|academy|accountants|active|actor|aero|agency|airforce|archi|army|associates|attorney|auction|audio|autos|band|bargains|beer|best|bid|bike|bio|biz|black|blackfriday|blue|boo|boutique|build|builders|business|buzz|cab|camera|camp|cancerresearch|capital|cards|care|career|careers|cash|catering|center|ceo|channel|cheap|christmas|church|city|claims|cleaning|click|clinic|clothing|club|coach|codes|coffee|college|community|company|computer|condos|construction|consulting|contractors|cooking|cool|country|credit|creditcard|cricket|cruises|dad|dance|dating|day|deals|degree|delivery|democrat|dental|dentist|diamonds|diet|digital|direct|directory|discount|domains|eat|education|email|energy|engineer|engineering|equipment|esq|estate|events|exchange|expert|exposed|fail|farm|feedback|finance|financial|fish|fishing|fitness|flights|florist|fly|foo|forsale|foundation|fund|furniture|futbol|gallery|gift|gifts|gives|glass|global|gop|graphics|green|gripe|guide|guitars|guru|healthcare|help|here|hiphop|hiv|holdings|holiday|homes|horse|host|hosting|house|how|info|ing|ink|insure|international|investments|jobs|kim|kitchen|land|lawyer|lease|legal|lgbt|life|lighting|limited|limo|link|loans|lotto|luxe|luxury|management|market|marketing|media|meet|meme|memorial|menu|mobi|moe|money|mortgage|motorcycles|mov|museum|name|navy|network|new|ngo|ninja|ong|onl|ooo|organic|partners|parts|party|pharmacy|photo|photography|photos|physio|pics|pictures|pink|pizza|place|plumbing|poker|post|press|pro|productions|prof|properties|property|qpon|recipes|red|rehab|ren|rentals|repair|report|republican|reviews|rich|rip|rocks|rodeo|rsvp|science|services|sexy|shoes|singles|social|software|solar|solutions|space|supplies|supply|support|surf|surgery|systems|tattoo|tax|technology|tel|tips|tires|today|tools|top|town|toys|trade|training|travel|university|vacations|vet|villas|vision|vodka|vote|voting|voyage|wang|watch|webcam|website|wed|wiki|works|world|wtf|xxx|xyz|zone'

select @websiteType = min(websiteType)
	FROM dbo.ams_memberWebsiteTypes
	WHERE orgID = @orgID
	and websiteType NOT IN (select colName from @tblMissingCols)
while @websiteType is not null BEGIN

	select @dynSQL = 'update ' + @tmptbl + ' 
		set ' + quotename(@websiteType) + ' = ''http://'' + ' + quotename(@websiteType) + ' 
		where len(' + quotename(@websiteType) + ') > 0 and left(' + quotename(@websiteType) + ',4) <> ''http'''
	EXEC(@dynSQL)

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the website ' + @websiteType + ': '' + ' + quoteName(@websiteType) + ' as msg, 0 as fatal
		FROM ' + @tmptbl + ' 
		WHERE len(' + quotename(@websiteType) + ') > 0 and dbo.fn_RegExReplace(' + quotename(@websiteType) + ',''^(http|https|ftp)\://([a-zA-Z0-9\.\-]+(\:[a-zA-Z0-9\.&amp;%\$\-]+)*@)*((25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9])\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[0-9])|localhost|([a-zA-Z0-9\-]+\.)*[a-zA-Z0-9\-]+\.(' + @tldList + '|[a-zA-Z]{2}))(\:[0-9]+)*(/($|[a-zA-Z0-9\.\,\?\''''\\\+&amp;%\!$#\=~_\-]+))*$'','''') <> '''' 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the website ' + @websiteType + '.'' as msg
			FROM ' + @tmptbl + ' 
			WHERE len(' + quotename(@websiteType) + ') > 0 and dbo.fn_RegExReplace(' + quotename(@websiteType) + ',''^(http|https|ftp)\://([a-zA-Z0-9\.\-]+(\:[a-zA-Z0-9\.&amp;%\$\-]+)*@)*((25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9])\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[0-9])|localhost|([a-zA-Z0-9\-]+\.)*[a-zA-Z0-9\-]+\.(' + @tldList + '|[a-zA-Z]{2}))(\:[0-9]+)*(/($|[a-zA-Z0-9\.\,\?\''''\\\+&amp;%\!$#\=~_\-]+))*$'','''') <> '''' 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@websiteType) + ' = '''' 
			WHERE len(' + quotename(@websiteType) + ') > 0 and dbo.fn_RegExReplace(' + quotename(@websiteType) + ',''^(http|https|ftp)\://([a-zA-Z0-9\.\-]+(\:[a-zA-Z0-9\.&amp;%\$\-]+)*@)*((25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9])\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[0-9])|localhost|([a-zA-Z0-9\-]+\.)*[a-zA-Z0-9\-]+\.(' + @tldList + '|[a-zA-Z]{2}))(\:[0-9]+)*(/($|[a-zA-Z0-9\.\,\?\''''\\\+&amp;%\!$#\=~_\-]+))*$'','''') <> '''' '
		EXEC(@dynSQL)
	END

	select @websiteType = min(websiteType)
		FROM dbo.ams_memberWebsiteTypes
		WHERE orgID = @orgID
		and websiteType NOT IN (select colName from @tblMissingCols)
		AND websiteType > @websiteType
END

-- ********************************
-- bad email data (non-fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying invalid e-mail data'

select @emailType = min(emailType)
	FROM dbo.ams_memberEmailTypes
	WHERE orgID = @orgID
	and emailType NOT IN (select colName from @tblMissingCols)
while @emailType is not null BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the e-mail ' + @emailType + ': '' + ' + quotename(@emailType) + ' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE len(' + quotename(@emailType) + ') > 0 and dbo.fn_RegExReplace(' + quotename(@emailType) + ',''^[a-zA-Z_0-9-''''\&\+~]+(\.[a-zA-Z_0-9-''''\&\+~]+)*@([a-zA-Z_0-9-]+\.)+[a-zA-Z]{2,7}$'','''') <> '''' 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the e-mail ' + @emailType + '.'' as msg
			FROM ' + @tmptbl + ' 
			WHERE len(' + quotename(@emailType) + ') > 0 and dbo.fn_RegExReplace(' + quotename(@emailType) + ',''^[a-zA-Z_0-9-''''\&\+~]+(\.[a-zA-Z_0-9-''''\&\+~]+)*@([a-zA-Z_0-9-]+\.)+[a-zA-Z]{2,7}$'','''') <> '''' 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@emailType) + ' = '''' 
			WHERE len(' + quotename(@emailType) + ') > 0 and dbo.fn_RegExReplace(' + quotename(@emailType) + ',''^[a-zA-Z_0-9-''''\&\+~]+(\.[a-zA-Z_0-9-''''\&\+~]+)*@([a-zA-Z_0-9-]+\.)+[a-zA-Z]{2,7}$'','''') <> '''' '
		EXEC(@dynSQL)
	END

	select @emailType = min(emailType)
		FROM dbo.ams_memberEmailTypes
		WHERE orgID = @orgID
		and emailType NOT IN (select colName from @tblMissingCols)
		AND emailType > @emailType
END


-- ********************************
-- bad billing address type (non-fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying invalid billing address types'

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'BillingAddressType') BEGIN
	declare @defaultBillingAddressType varchar(20)
	select @defaultBillingAddressType = addressType from dbo.ams_memberAddressTypes where orgID = @orgID and addressTypeOrder = 1

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(tmp.rowID as varchar(10)) + '' ('' + isnull(tmp.firstname,'''') + '' '' + isnull(tmp.lastname,'''') + '') has invalid data for BillingAddressType.'' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as tmp
		LEFT OUTER JOIN membercentral.dbo.ams_memberAddressTypes as mat on mat.orgID = ' + cast(@orgID as varchar(10)) + ' and mat.addressType = tmp.BillingAddressType 
		WHERE len(tmp.BillingAddressType) > 0 
		AND mat.addressTypeID is null 
		ORDER BY tmp.rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(tmp.rowID as varchar(10)) + '' ('' + isnull(tmp.firstname,'''') + '' '' + isnull(tmp.lastname,'''') + '') will change the BillingAddressType from '' + tmp.BillingAddressType + '' to ' + @defaultBillingAddressType + ''' as msg
			FROM ' + @tmptbl + ' as tmp
			LEFT OUTER JOIN membercentral.dbo.ams_memberAddressTypes as mat on mat.orgID = ' + cast(@orgID as varchar(10)) + ' and mat.addressType = tmp.BillingAddressType 
			WHERE len(tmp.BillingAddressType) > 0 
			AND mat.addressTypeID is null 
			ORDER BY tmp.rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE tmp
			SET tmp.BillingAddressType = '''' 
			FROM ' + @tmptbl + ' as tmp
			LEFT OUTER JOIN membercentral.dbo.ams_memberAddressTypes as mat on mat.orgID = ' + cast(@orgID as varchar(10)) + ' and mat.addressType = tmp.BillingAddressType 
			WHERE len(tmp.BillingAddressType) > 0 
			AND mat.addressTypeID is null '
		EXEC(@dynSQL)
	END

	-- if blank, existing members should retain their existing value.
	select @dynSQL = 'UPDATE tmp 
		SET tmp.BillingAddressType = mat.addressType 
		FROM ' + @tmptbl + ' as tmp 
		INNER JOIN dbo.ams_members as m on m.memberNumber = tmp.memberNumber 
			AND m.orgID = ' + cast(@orgID as varchar(10)) + ' 
			AND m.membertypeID = 2 
			AND m.memberID = m.activeMemberID 
			AND m.status <> ''D'' 
		INNER JOIN dbo.ams_memberAddressTypes as mat on mat.addressTypeID = m.billingAddressTypeID 
		WHERE tmp.BillingAddressType = '''''
	EXEC(@dynSQL)

	-- if blank, new members should get the default value. 
	select @dynSQL = 'UPDATE ' + @tmptbl + ' 
		SET BillingAddressType = ''' + @defaultBillingAddressType + '''  
		WHERE BillingAddressType = '''''
	EXEC(@dynSQL)
END


-- ********************************
-- bad address data (non-fatal)
--  - country specified but not valid country
--  - state and country specified but not valid state
--  - state specified but no country specified
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying invalid address data'

select @addressType = min(addressType)
	FROM dbo.ams_memberAddressTypes
	WHERE orgID = @orgID
	and (
		addressType + '_stateprov' NOT IN (select colName from @tblMissingCols)
		or
		addressType + '_country' NOT IN (select colName from @tblMissingCols)
	)
while @addressType is not null BEGIN

	-- country specified but not valid country
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for ' + @addressType + '_country' + ': '' + ' + quotename(@addressType + '_country') + ' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as tmp 
		WHERE len(isnull(tmp.' + quotename(@addressType + '_country') + ','''')) > 0 
		and not exists (select countryID from dbo.ams_countries where country = isnull(tmp.' + quotename(@addressType + '_country') + ',''''))
		ORDER BY tmp.rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the ' + @addressType + '_country' + '.'' as msg
			FROM ' + @tmptbl + ' as tmp 
			WHERE len(isnull(tmp.' + quotename(@addressType + '_country') + ','''')) > 0 
			and not exists (select countryID from dbo.ams_countries where country = isnull(tmp.' + quotename(@addressType + '_country') + ',''''))
			ORDER BY tmp.rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@addressType + '_country') + ' = '''' 
			WHERE len(isnull(' + quotename(@addressType + '_country') + ','''')) > 0 
			and not exists (select countryID from dbo.ams_countries where country = isnull(' + quotename(@addressType + '_country') + ','''')) '
		EXEC(@dynSQL)
	END

	-- state and country specified but not valid state
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for ' + @addressType + '_stateprov' + ': '' + ' + quotename(@addressType + '_stateprov') + ' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as tmp 
		WHERE len(isnull(tmp.' + quotename(@addressType + '_stateprov') + ','''')) > 0 
		and len(isnull(tmp.' + quotename(@addressType + '_country') + ','''')) > 0 
		and not exists (
			select s.stateID
			from dbo.ams_states as s
			inner join dbo.ams_countries as c on c.countryID = s.countryID
			where s.code = isnull(tmp.' + quotename(@addressType + '_stateprov') + ','''') 
			and c.country = isnull(tmp.' + quotename(@addressType + '_country') + ','''') 
		)
		and exists (select countryID from dbo.ams_countries where country = isnull(tmp.' + quotename(@addressType + '_country') + ',''''))
		ORDER BY tmp.rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the ' + @addressType + '_stateprov' + '.'' as msg
			FROM ' + @tmptbl + ' as tmp 
			WHERE len(isnull(tmp.' + quotename(@addressType + '_stateprov') + ','''')) > 0 
			and len(isnull(tmp.' + quotename(@addressType + '_country') + ','''')) > 0 
			and not exists (
				select s.stateID
				from dbo.ams_states as s
				inner join dbo.ams_countries as c on c.countryID = s.countryID
				where s.code = isnull(tmp.' + quotename(@addressType + '_stateprov') + ','''') 
				and c.country = isnull(tmp.' + quotename(@addressType + '_country') + ','''') 
			)
			and exists (select countryID from dbo.ams_countries where country = isnull(tmp.' + quotename(@addressType + '_country') + ',''''))
			ORDER BY tmp.rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@addressType + '_stateprov') + ' = '''' 
			WHERE len(isnull(' + quotename(@addressType + '_stateprov') + ','''')) > 0 
			and len(isnull(' + quotename(@addressType + '_country') + ','''')) > 0 
			and not exists (
				select s.stateID
				from dbo.ams_states as s
				inner join dbo.ams_countries as c on c.countryID = s.countryID
				where s.code = isnull(' + quotename(@addressType + '_stateprov') + ','''') 
				and c.country = isnull(' + quotename(@addressType + '_country') + ','''') 
			)
			and exists (select countryID from dbo.ams_countries where country = isnull(' + quotename(@addressType + '_country') + ','''')) '
		EXEC(@dynSQL)
	END

	-- state specified but no country specified
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had a missing country value for ' + @addressType + '_stateprov' + ': '' + ' + quotename(@addressType + '_stateprov') + ' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as tmp 
		WHERE len(isnull(tmp.' + quotename(@addressType + '_stateprov') + ','''')) > 0 
		and len(isnull(tmp.' + quotename(@addressType + '_country') + ','''')) = 0 
		ORDER BY tmp.rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the ' + @addressType + '_stateprov' + '.'' as msg
			FROM ' + @tmptbl + ' as tmp 
			WHERE len(isnull(tmp.' + quotename(@addressType + '_stateprov') + ','''')) > 0 
			and len(isnull(tmp.' + quotename(@addressType + '_country') + ','''')) = 0 
			ORDER BY tmp.rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@addressType + '_stateprov') + ' = '''' 
			WHERE len(isnull(' + quotename(@addressType + '_stateprov') + ','''')) > 0 
			and len(isnull(' + quotename(@addressType + '_country') + ','''')) = 0 '
		EXEC(@dynSQL)
	END

	select @addressType = min(addressType)
		FROM dbo.ams_memberAddressTypes
		WHERE orgID = @orgID
		and (
			addressType + '_stateprov' NOT IN (select colName from @tblMissingCols)
			or
			addressType + '_country' NOT IN (select colName from @tblMissingCols)
		)
		AND addressType > @addressType
END

-- ********************************
-- bad prefix data (non-fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying invalid prefix data'

IF @hasPrefix = 1 and @usePrefixList = 1 and NOT EXISTS (select colName from @tblMissingCols where colName = 'prefix') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid prefix value'' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE prefix is not null and len(prefix) > 0 and prefix not in (select prefix from dbo.ams_memberPrefixTypes where orgID = ' + cast(@orgID as varchar(10)) + ')  
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the Prefix.'' as msg
			FROM ' + @tmptbl + ' 
			WHERE prefix is not null and len(prefix) > 0 and prefix not in (select prefix from dbo.ams_memberPrefixTypes where orgID = ' + cast(@orgID as varchar(10)) + ')  
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET prefix = '''' 
			WHERE prefix is not null and len(prefix) > 0 and prefix not in (select prefix from dbo.ams_memberPrefixTypes where orgID = ' + cast(@orgID as varchar(10)) + ') '
		EXEC(@dynSQL)
	END
END

-- ********************************
-- bad prof license data (fatal and non-fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying invalid professional license data'

select @plType = min(PLName)
	FROM dbo.ams_memberProfessionalLicenseTypes
	WHERE orgID = @orgID
	and PLName + '_licenseNumber' NOT IN (select colName from @tblMissingCols)
	and PLName + '_status' NOT IN (select colName from @tblMissingCols)
	and PLName + '_activeDate' NOT IN (select colName from @tblMissingCols)
while @plType is not null BEGIN
	
	-- dates must be valid
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			UPDATE ' + @tmptbl + ' set ' + quotename(@plType + '_activeDate') + ' = null where ' + quotename(@plType + '_activeDate') + ' = ''''
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@plType + '_activeDate') + ' datetime null
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @plType + '_activeDate contains invalid dates.', 1)
	END
	ELSE BEGIN
		-- if status is defined, it must be valid
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for ' + @plType + '_status' + ': '' + ' + quotename(@plType + '_status') + ' as msg, 0 as fatal 
			FROM ' + @tmptbl + ' as tmp 
			WHERE len(isnull(tmp.' + quotename(@plType + '_status') + ','''')) > 0 
			and not exists (
				select PLStatusID
				from dbo.ams_memberProfessionalLicenseStatuses
				where StatusName = isnull(tmp.' + quotename(@plType + '_status') + ','''')
				and orgID = ' + cast(@orgID as varchar(10)) + '
			)'
		INSERT INTO @tblErrors (msg, fatal)
		EXEC(@dynSQL)

		IF @@ROWCOUNT > 0 BEGIN
			select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the ' + @plType + '_status' + '.'' as msg
				FROM ' + @tmptbl + ' as tmp 
				WHERE len(isnull(tmp.' + quotename(@plType + '_status') + ','''')) > 0 
				and not exists (
					select PLStatusID
					from dbo.ams_memberProfessionalLicenseStatuses
					where StatusName = isnull(tmp.' + quotename(@plType + '_status') + ','''')
					and orgID = ' + cast(@orgID as varchar(10)) + '
				)'
			INSERT INTO @tblDataChanged (msg)
			EXEC(@dynSQL)

			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@plType + '_status') + ' = '''' 
				WHERE len(isnull(' + quotename(@plType + '_status') + ','''')) > 0 
				and not exists (
					select PLStatusID
					from dbo.ams_memberProfessionalLicenseStatuses
					where StatusName = isnull(' + quotename(@plType + '_status') + ','''')
					and orgID = ' + cast(@orgID as varchar(10)) + '
				)'
			EXEC(@dynSQL)
		END

		-- if licenseNumber or activeDate is defined, status must be as well
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had a missing value for ' + @plType + '_status'' as msg, 0 as fatal 
			FROM ' + @tmptbl + ' as tmp 
			WHERE (
				len(isnull(tmp.' + quotename(@plType + '_licenseNumber') + ','''')) > 0
				or tmp.' + quotename(@plType + '_activeDate') + ' is not null
			)
			and len(isnull(tmp.' + quotename(@plType + '_status') + ','''')) = 0 '
		INSERT INTO @tblErrors (msg, fatal)
		EXEC(@dynSQL)

		IF @@ROWCOUNT > 0 BEGIN
			select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the ' + @plType + ' information.'' as msg
				FROM ' + @tmptbl + ' as tmp 
				WHERE (
					len(isnull(tmp.' + quotename(@plType + '_licenseNumber') + ','''')) > 0
					or tmp.' + quotename(@plType + '_activeDate') + ' is not null
				)
				and len(isnull(tmp.' + quotename(@plType + '_status') + ','''')) = 0 '
			INSERT INTO @tblDataChanged (msg)
			EXEC(@dynSQL)

			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@plType + '_licenseNumber') + ' = '''',
					' + quotename(@plType + '_activeDate') + ' = null 
				WHERE (
					len(isnull(' + quotename(@plType + '_licenseNumber') + ','''')) > 0
					or ' + quotename(@plType + '_activeDate') + ' is not null
				)
				and len(isnull(' + quotename(@plType + '_status') + ','''')) = 0 '
			EXEC(@dynSQL)
		END
	END

	select @plType = min(PLName)
		FROM dbo.ams_memberProfessionalLicenseTypes
		WHERE orgID = @orgID
		and PLName + '_licenseNumber' NOT IN (select colName from @tblMissingCols)
		and PLName + '_status' NOT IN (select colName from @tblMissingCols)
		and PLName + '_activeDate' NOT IN (select colName from @tblMissingCols)
		and PLName > @plType
END

-- ********************************
-- check custom columns that have a default defined; use that default instead of null
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking columns with default values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	where mdc.allowNull = 0
	and mdc.skipImport = 0
	and mdc.orgID = @orgID
	and mdc.defaultValueID is not null
	and mdc.columnname not in (select colName from @tblMissingCols)
while @mincol is not null BEGIN
	select @defaultValueID = defaultValueID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol
	select @colDataTypeCode = dt.dataTypeCode from dbo.ams_memberDataColumnDataTypes as dt inner join dbo.ams_memberDataColumns as mdc on mdc.dataTypeID = dt.dataTypeID and mdc.orgID = @orgID and mdc.columnName = @mincol

	IF @colDataTypeCode = 'STRING'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueString from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where nullif(' + quoteName(@mincol) + ','''') is null'
	IF @colDataTypeCode = 'DECIMAL2'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueDecimal2 from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where ' + quoteName(@mincol) + ' is null'
	IF @colDataTypeCode = 'INTEGER'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueInteger from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where ' + quoteName(@mincol) + ' is null'
	IF @colDataTypeCode = 'DATE'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueDate from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where ' + quoteName(@mincol) + ' is null'
	IF @colDataTypeCode = 'BIT'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueBit from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where ' + quoteName(@mincol) + ' is null'
	IF @colDataTypeCode = 'XML'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueXML from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where ' + quoteName(@mincol) + ' is null'
	IF @colDataTypeCode = 'CONTENTOBJ' OR @colDataTypeCode = 'DOCUMENTOBJ'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueSiteResourceID from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where ' + quoteName(@mincol) + ' is null'

	BEGIN TRY
		EXEC(@dynSQL)
	END TRY
	BEGIN CATCH
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains null values that could not use the defined default value.', 1)
	END CATCH

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		where mdc.allowNull = 0
		and mdc.skipImport = 0
		and mdc.orgID = @orgID
		and mdc.defaultValueID is not null
		and mdc.columnname not in (select colName from @tblMissingCols)
		and mdc.columnName > @mincol
END

-- ********************************
-- check for any custom columns that don't permit new values on import
-- for these cols, make sure all values in file are in defined set
-- skip columns where there is a fatal error on converting all values to correct datatype
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking STRING columns for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 0
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'STRING'
		and mdc.columnname not in (select colName from @tblMissingCols)
while @mincol is not null BEGIN
	select @columnID = null, @allownull = null
	select @columnID = columnID, @allownull = allowNull from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	IF @allownull = 1 BEGIN
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = null where ' + quoteName(@mincol) + ' = '''''
		EXEC(@dynSQL)
	END

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and mdcv.columnValueString = importfile.' + quoteName(@mincol) + ' 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and mdcv.columnValueString = importfile.' + quoteName(@mincol) + ' 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		IF @allownull = 1 BEGIN		
			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@mincol) + ' = null
				WHERE rowID in (
					SELECT rowID
					FROM ' + @tmptbl + ' as importfile
					LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
						and mdcv.columnValueString = importfile.' + quoteName(@mincol) + ' 
					WHERE mdcv.valueID is null 
					AND importfile.' + quoteName(@mincol) + ' is not null 
				)'
		END ELSE BEGIN
			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@mincol) + ' = '''' 
				WHERE rowID in (
					SELECT rowID
					FROM ' + @tmptbl + ' as importfile
					LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
						and mdcv.columnValueString = importfile.' + quoteName(@mincol) + ' 
					WHERE mdcv.valueID is null 
					AND importfile.' + quoteName(@mincol) + ' is not null 
				)'
		END
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and mdc.allowMultiple = 0
			and mdc.allowNewValuesOnImport = 0
			and dt.dataTypeCode = 'STRING'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking STRING columns (multiple values) for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 1
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'STRING'
		and mdc.columnname not in (select colName from @tblMissingCols)
while @mincol is not null BEGIN
	select @columnID = null, @allownull = null
	select @columnID = columnID, @allownull = allowNull from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	IF @allownull = 1 BEGIN
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = null where ' + quoteName(@mincol) + ' = '''''
		EXEC(@dynSQL)
	END

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		cross apply dbo.fn_varcharListToTable(' + quotename(@mincol) + ',''|'') as tbl
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and mdcv.columnValueString = tbl.listitem  
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
	
	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			cross apply dbo.fn_varcharListToTable(' + quotename(@mincol) + ',''|'') as tbl
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and mdcv.columnValueString = tbl.listitem  
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		IF @allownull = 1 BEGIN		
			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@mincol) + ' = null
				WHERE rowID in (
					SELECT rowID
					FROM ' + @tmptbl + ' as importfile
					cross apply dbo.fn_varcharListToTable(' + quotename(@mincol) + ',''|'') as tbl
					LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
						and mdcv.columnValueString = tbl.listitem  
					WHERE mdcv.valueID is null 
					AND importfile.' + quoteName(@mincol) + ' is not null 
				)'
		END ELSE BEGIN
			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@mincol) + ' = '''' 
				WHERE rowID in (
					SELECT rowID
					FROM ' + @tmptbl + ' as importfile
					cross apply dbo.fn_varcharListToTable(' + quotename(@mincol) + ',''|'') as tbl
					LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
						and mdcv.columnValueString = tbl.listitem  
					WHERE mdcv.valueID is null 
					AND importfile.' + quoteName(@mincol) + ' is not null 
				)'
		END
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and mdc.allowMultiple = 1
			and mdc.allowNewValuesOnImport = 0
			and dt.dataTypeCode = 'STRING'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking DATE columns for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'DATE'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid dates.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and mdcv.columnValueDate = importfile.' + quoteName(@mincol) + ' 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and mdcv.columnValueDate = importfile.' + quoteName(@mincol) + ' 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
					and mdcv.columnValueDate = importfile.' + quoteName(@mincol) + ' 
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END
	
	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'DATE'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid dates.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking DECIMAL2 columns for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowMultiple = 0
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'DECIMAL2'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid decimal values.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and mdcv.columnValueDecimal2 = importfile.' + quoteName(@mincol) + ' 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg 
			FROM ' + @tmptbl + ' as importfile
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and mdcv.columnValueDecimal2 = importfile.' + quoteName(@mincol) + ' 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
					and mdcv.columnValueDecimal2 = importfile.' + quoteName(@mincol) + ' 
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END
	
	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 0
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'DECIMAL2'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid decimal values.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking DECIMAL2 columns (multiple values) for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowMultiple = 1
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'DECIMAL2'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid decimal values.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		cross apply dbo.fn_decimal2ListToTable(' + quotename(@mincol) + ',''|'') as tbl
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + ' 
			and mdcv.columnValueDecimal2 = tbl.listitem 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY importfile.rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
	
	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			cross apply dbo.fn_decimal2ListToTable(' + quotename(@mincol) + ',''|'') as tbl
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + ' 
				and mdcv.columnValueDecimal2 = tbl.listitem 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY importfile.rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				cross apply dbo.fn_decimal2ListToTable(' + quotename(@mincol) + ',''|'') as tbl
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + ' 
					and mdcv.columnValueDecimal2 = tbl.listitem 
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 1
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'DECIMAL2'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid decimal values.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking INTEGER columns for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowMultiple = 0
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'INTEGER'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid whole number values.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and mdcv.columnValueInteger = importfile.' + quoteName(@mincol) + ' 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg 
			FROM ' + @tmptbl + ' as importfile
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and mdcv.columnValueInteger = importfile.' + quoteName(@mincol) + ' 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
					and mdcv.columnValueInteger = importfile.' + quoteName(@mincol) + ' 
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 0
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'INTEGER'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid whole number values.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking INTEGER columns (multiple values) for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowMultiple = 1
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'INTEGER'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid whole number values.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		cross apply dbo.fn_intListToTable(' + quotename(@mincol) + ',''|'') as tbl
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + ' 
			and mdcv.columnValueInteger = tbl.listitem 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			cross apply dbo.fn_intListToTable(' + quotename(@mincol) + ',''|'') as tbl
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + ' 
				and mdcv.columnValueInteger = tbl.listitem 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				cross apply dbo.fn_intListToTable(' + quotename(@mincol) + ',''|'') as tbl
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + ' 
					and mdcv.columnValueInteger = tbl.listitem 
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 1
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'INTEGER'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid whole number values.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking BIT columns for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'BIT'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid boolean values.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and mdcv.columnValueBit = importfile.' + quoteName(@mincol) + ' 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and mdcv.columnValueBit = importfile.' + quoteName(@mincol) + ' 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
					and mdcv.columnValueBit = importfile.' + quoteName(@mincol) + ' 
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'BIT'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid boolean values.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking XML columns for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'XML'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid XML values.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and cast(mdcv.columnValueXML as varchar(max)) = cast(importfile.' + quoteName(@mincol) + ' as varchar(max))  
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and cast(mdcv.columnValueXML as varchar(max)) = cast(importfile.' + quoteName(@mincol) + ' as varchar(max))  
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
					and cast(mdcv.columnValueXML as varchar(max)) = cast(importfile.' + quoteName(@mincol) + ' as varchar(max))  
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'XML'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid XML values.')
		and mdc.columnName > @mincol
END


-- ********************************
-- check for any custom columns that have validation requirements
-- for these cols, make sure all values in file are in range
-- skip columns where there is a fatal error on converting all values to correct datatype
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking STRING/TEXTBOX columns for minChars/maxChars'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	inner join dbo.ams_memberDataColumnDisplayTypes as ddt on ddt.displayTypeID = mdc.displayTypeID
	WHERE mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.minChars is not null
	and mdc.maxChars is not null
	and dt.dataTypeCode = 'STRING'
	and ddt.displayTypeCode = 'TEXTBOX'
	and mdc.columnname not in (select colName from @tblMissingCols)
while @mincol is not null BEGIN
	select @columnID = null, @allownull = null, @minChars = null, @maxChars = null
	select @columnID=columnID, @allownull=allowNull, @minChars=minChars, @maxChars=maxChars from dbo.ams_memberDataColumns where orgID=@orgID and columnName=@mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an out-of-range value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		WHERE importfile.' + quoteName(@mincol) + ' is not null 
		and len(importfile.' + quoteName(@mincol) + ') > 0
		and len(importfile.' + quoteName(@mincol) + ') not between ' + cast(@minChars as varchar(10)) + ' and ' + cast(@maxChars as varchar(10)) + '
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			WHERE importfile.' + quoteName(@mincol) + ' is not null 
			and len(importfile.' + quoteName(@mincol) + ') > 0
			and len(importfile.' + quoteName(@mincol) + ') not between ' + cast(@minChars as varchar(10)) + ' and ' + cast(@maxChars as varchar(10)) + '
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = ' + case when @allownull = 1 then 'null' else '''' end + ' 
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				WHERE importfile.' + quoteName(@mincol) + ' is not null 
				and len(importfile.' + quoteName(@mincol) + ') > 0
				and len(importfile.' + quoteName(@mincol) + ') not between ' + cast(@minChars as varchar(10)) + ' and ' + cast(@maxChars as varchar(10)) + '
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		inner join dbo.ams_memberDataColumnDisplayTypes as ddt on ddt.displayTypeID = mdc.displayTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.minChars is not null
		and mdc.maxChars is not null
		and dt.dataTypeCode = 'STRING'
		and ddt.displayTypeCode = 'TEXTBOX'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking STRING/SELECT MULTI columns for minSelected/maxSelected'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	inner join dbo.ams_memberDataColumnDisplayTypes as ddt on ddt.displayTypeID = mdc.displayTypeID
	WHERE mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowMultiple = 1
	and mdc.minSelected is not null
	and mdc.maxSelected is not null
	and dt.dataTypeCode = 'STRING'
	and ddt.displayTypeCode = 'SELECT'
	and mdc.columnname not in (select colName from @tblMissingCols)
while @mincol is not null BEGIN
	select @columnID = null, @allownull = null, @minSelected = null, @maxSelected = null
	select @columnID=columnID, @allownull=allowNull, @minSelected=minSelected, @maxSelected=maxSelected from dbo.ams_memberDataColumns where orgID=@orgID and columnName=@mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an out-of-range value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		cross apply dbo.fn_varcharListToTable(importfile.' + quotename(@mincol) + ',''|'') as tbl
		WHERE importfile.' + quoteName(@mincol) + ' is not null 
		GROUP BY rowID, firstname, lastname, membernumber
		having count(*) not between ' + cast(@minSelected as varchar(10)) + ' and ' + cast(@maxSelected as varchar(10)) + '
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			cross apply dbo.fn_varcharListToTable(importfile.' + quotename(@mincol) + ',''|'') as tbl
			WHERE importfile.' + quoteName(@mincol) + ' is not null 
			GROUP BY rowID, firstname, lastname, membernumber
			having count(*) not between ' + cast(@minSelected as varchar(10)) + ' and ' + cast(@maxSelected as varchar(10)) + '
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = ' + case when @allownull = 1 then 'null' else '''' end + ' 
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				cross apply dbo.fn_varcharListToTable(importfile.' + quotename(@mincol) + ',''|'') as tbl
				WHERE importfile.' + quoteName(@mincol) + ' is not null 
				GROUP BY rowID, firstname, lastname, membernumber
				having count(*) not between ' + cast(@minSelected as varchar(10)) + ' and ' + cast(@maxSelected as varchar(10)) + '
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		inner join dbo.ams_memberDataColumnDisplayTypes as ddt on ddt.displayTypeID = mdc.displayTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 1
		and mdc.minSelected is not null
		and mdc.maxSelected is not null
		and dt.dataTypeCode = 'STRING'
		and ddt.displayTypeCode = 'SELECT'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking INTEGER/TEXTBOX columns for minValueInt/maxValueInt'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	inner join dbo.ams_memberDataColumnDisplayTypes as ddt on ddt.displayTypeID = mdc.displayTypeID
	WHERE mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.minValueInt is not null
	and mdc.maxValueInt is not null
	and dt.dataTypeCode = 'INTEGER'
	and ddt.displayTypeCode = 'TEXTBOX'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid whole number values.')
while @mincol is not null BEGIN
	select @columnID = null, @minValueInt = null, @maxValueInt = null
	select @columnID=columnID, @minValueInt=minValueInt, @maxValueInt=maxValueInt from dbo.ams_memberDataColumns where orgID=@orgID and columnName=@mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an out-of-range value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		WHERE importfile.' + quoteName(@mincol) + ' is not null 
		and importfile.' + quoteName(@mincol) + ' not between ' + cast(@minValueInt as varchar(10)) + ' and ' + cast(@maxValueInt as varchar(10)) + '
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			WHERE importfile.' + quoteName(@mincol) + ' is not null 
			and importfile.' + quoteName(@mincol) + ' not between ' + cast(@minValueInt as varchar(10)) + ' and ' + cast(@maxValueInt as varchar(10)) + '
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				WHERE importfile.' + quoteName(@mincol) + ' is not null 
				and importfile.' + quoteName(@mincol) + ' not between ' + cast(@minValueInt as varchar(10)) + ' and ' + cast(@maxValueInt as varchar(10)) + '
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		inner join dbo.ams_memberDataColumnDisplayTypes as ddt on ddt.displayTypeID = mdc.displayTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.minValueInt is not null
		and mdc.maxValueInt is not null
		and dt.dataTypeCode = 'INTEGER'
		and ddt.displayTypeCode = 'TEXTBOX'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid whole number values.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking INTEGER/SELECT MULTI columns for minSelected/maxSelected'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	inner join dbo.ams_memberDataColumnDisplayTypes as ddt on ddt.displayTypeID = mdc.displayTypeID
	WHERE mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowMultiple = 1
	and mdc.minSelected is not null
	and mdc.maxSelected is not null
	and dt.dataTypeCode = 'INTEGER'
	and ddt.displayTypeCode = 'SELECT'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid whole number values.')
while @mincol is not null BEGIN
	select @columnID = null, @minSelected = null, @maxSelected = null
	select @columnID=columnID, @minSelected=minSelected, @maxSelected=maxSelected from dbo.ams_memberDataColumns where orgID=@orgID and columnName=@mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an out-of-range value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		cross apply dbo.fn_intListToTable(importfile.' + quotename(@mincol) + ',''|'') as tbl
		WHERE importfile.' + quoteName(@mincol) + ' is not null 
		GROUP BY rowID, firstname, lastname, membernumber
		having count(*) not between ' + cast(@minSelected as varchar(10)) + ' and ' + cast(@maxSelected as varchar(10)) + '
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			cross apply dbo.fn_intListToTable(importfile.' + quotename(@mincol) + ',''|'') as tbl
			WHERE importfile.' + quoteName(@mincol) + ' is not null 
			GROUP BY rowID, firstname, lastname, membernumber
			having count(*) not between ' + cast(@minSelected as varchar(10)) + ' and ' + cast(@maxSelected as varchar(10)) + '
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null 
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				cross apply dbo.fn_intListToTable(importfile.' + quotename(@mincol) + ',''|'') as tbl
				WHERE importfile.' + quoteName(@mincol) + ' is not null 
				GROUP BY rowID, firstname, lastname, membernumber
				having count(*) not between ' + cast(@minSelected as varchar(10)) + ' and ' + cast(@maxSelected as varchar(10)) + '
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		inner join dbo.ams_memberDataColumnDisplayTypes as ddt on ddt.displayTypeID = mdc.displayTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 1
		and mdc.minSelected is not null
		and mdc.maxSelected is not null
		and dt.dataTypeCode = 'INTEGER'
		and ddt.displayTypeCode = 'SELECT'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid whole number values.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking DECIMAL2/TEXTBOX columns for minValueDecimal2/maxValueDecimal2'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	inner join dbo.ams_memberDataColumnDisplayTypes as ddt on ddt.displayTypeID = mdc.displayTypeID
	WHERE mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.minValueDecimal2 is not null
	and mdc.maxValueDecimal2 is not null
	and dt.dataTypeCode = 'DECIMAL2'
	and ddt.displayTypeCode = 'TEXTBOX'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid decimal values.')
while @mincol is not null BEGIN
	select @columnID = null, @minValueDecimal2 = null, @maxValueDecimal2 = null
	select @columnID=columnID, @minValueDecimal2=minValueDecimal2, @maxValueDecimal2=maxValueDecimal2 from dbo.ams_memberDataColumns where orgID=@orgID and columnName=@mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an out-of-range value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		WHERE importfile.' + quoteName(@mincol) + ' is not null 
		and importfile.' + quoteName(@mincol) + ' not between ' + cast(@minValueDecimal2 as varchar(12)) + ' and ' + cast(@maxValueDecimal2 as varchar(12)) + '
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			WHERE importfile.' + quoteName(@mincol) + ' is not null 
			and importfile.' + quoteName(@mincol) + ' not between ' + cast(@minValueDecimal2 as varchar(12)) + ' and ' + cast(@maxValueDecimal2 as varchar(12)) + '
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				WHERE importfile.' + quoteName(@mincol) + ' is not null 
				and importfile.' + quoteName(@mincol) + ' not between ' + cast(@minValueDecimal2 as varchar(12)) + ' and ' + cast(@maxValueDecimal2 as varchar(12)) + '
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		inner join dbo.ams_memberDataColumnDisplayTypes as ddt on ddt.displayTypeID = mdc.displayTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.minValueDecimal2 is not null
		and mdc.maxValueDecimal2 is not null
		and dt.dataTypeCode = 'DECIMAL2'
		and ddt.displayTypeCode = 'TEXTBOX'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid decimal values.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking DECIMAL2/SELECT MULTI columns for minSelected/maxSelected'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	inner join dbo.ams_memberDataColumnDisplayTypes as ddt on ddt.displayTypeID = mdc.displayTypeID
	WHERE mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowMultiple = 1
	and mdc.minSelected is not null
	and mdc.maxSelected is not null
	and dt.dataTypeCode = 'DECIMAL2'
	and ddt.displayTypeCode = 'SELECT'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid decimal values.')
while @mincol is not null BEGIN
	select @columnID = null, @minSelected = null, @maxSelected = null
	select @columnID=columnID, @minSelected=minSelected, @maxSelected=maxSelected from dbo.ams_memberDataColumns where orgID=@orgID and columnName=@mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an out-of-range value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		cross apply dbo.fn_decimal2ListToTable(importfile.' + quotename(@mincol) + ',''|'') as tbl
		WHERE importfile.' + quoteName(@mincol) + ' is not null 
		GROUP BY rowID, firstname, lastname, membernumber
		having count(*) not between ' + cast(@minSelected as varchar(10)) + ' and ' + cast(@maxSelected as varchar(10)) + '
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			cross apply dbo.fn_decimal2ListToTable(importfile.' + quotename(@mincol) + ',''|'') as tbl
			WHERE importfile.' + quoteName(@mincol) + ' is not null 
			GROUP BY rowID, firstname, lastname, membernumber
			having count(*) not between ' + cast(@minSelected as varchar(10)) + ' and ' + cast(@maxSelected as varchar(10)) + '
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null 
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				cross apply dbo.fn_decimal2ListToTable(importfile.' + quotename(@mincol) + ',''|'') as tbl
				WHERE importfile.' + quoteName(@mincol) + ' is not null 
				GROUP BY rowID, firstname, lastname, membernumber
				having count(*) not between ' + cast(@minSelected as varchar(10)) + ' and ' + cast(@maxSelected as varchar(10)) + '
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		inner join dbo.ams_memberDataColumnDisplayTypes as ddt on ddt.displayTypeID = mdc.displayTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 1
		and mdc.minSelected is not null
		and mdc.maxSelected is not null
		and dt.dataTypeCode = 'DECIMAL2'
		and ddt.displayTypeCode = 'SELECT'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid decimal values.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking DATE/DATE columns for minValueDate/maxValueDate'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	inner join dbo.ams_memberDataColumnDisplayTypes as ddt on ddt.displayTypeID = mdc.displayTypeID
	WHERE mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.minValueDate is not null
	and mdc.maxValueDate is not null
	and dt.dataTypeCode = 'DATE'
	and ddt.displayTypeCode = 'DATE'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid dates.')
while @mincol is not null BEGIN
	select @columnID = null, @minValueDate = null, @maxValueDate = null
	select @columnID=columnID, @minValueDate=minValueDate, @maxValueDate=maxValueDate from dbo.ams_memberDataColumns where orgID=@orgID and columnName=@mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an out-of-range value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		WHERE importfile.' + quoteName(@mincol) + ' is not null 
		and importfile.' + quoteName(@mincol) + ' not between ''' + convert(varchar(10),@minValueDate,101) + ''' and ''' + convert(varchar(10),@maxValueDate,101) + '''
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			WHERE importfile.' + quoteName(@mincol) + ' is not null 
			and importfile.' + quoteName(@mincol) + ' not between ''' + convert(varchar(10),@minValueDate,101) + ''' and ''' + convert(varchar(10),@maxValueDate,101) + '''
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				WHERE importfile.' + quoteName(@mincol) + ' is not null 
				and importfile.' + quoteName(@mincol) + ' not between ''' + convert(varchar(10),@minValueDate,101) + ''' and ''' + convert(varchar(10),@maxValueDate,101) + '''
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		inner join dbo.ams_memberDataColumnDisplayTypes as ddt on ddt.displayTypeID = mdc.displayTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.minValueDate is not null
		and mdc.maxValueDate is not null
		and dt.dataTypeCode = 'DATE'
		and ddt.displayTypeCode = 'DATE'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid dates.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking CONTENTOBJ columns for minChars/maxChars'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	WHERE mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.minChars is not null
	and mdc.maxChars is not null
	and dt.dataTypeCode = 'CONTENTOBJ'
	and mdc.columnname not in (select colName from @tblMissingCols)
while @mincol is not null BEGIN
	select @columnID = null, @minChars = null, @maxChars = null
	select @columnID=columnID, @minChars=minChars, @maxChars=maxChars from dbo.ams_memberDataColumns where orgID=@orgID and columnName=@mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an out-of-range value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		WHERE importfile.' + quoteName(@mincol) + ' is not null 
		and len(importfile.' + quoteName(@mincol) + ') > 0
		and len(importfile.' + quoteName(@mincol) + ') not between ' + cast(@minChars as varchar(10)) + ' and ' + cast(@maxChars as varchar(10)) + '
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			WHERE importfile.' + quoteName(@mincol) + ' is not null 
			and len(importfile.' + quoteName(@mincol) + ') > 0
			and len(importfile.' + quoteName(@mincol) + ') not between ' + cast(@minChars as varchar(10)) + ' and ' + cast(@maxChars as varchar(10)) + '
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = '''' 
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				WHERE importfile.' + quoteName(@mincol) + ' is not null 
				and len(importfile.' + quoteName(@mincol) + ') > 0
				and len(importfile.' + quoteName(@mincol) + ') not between ' + cast(@minChars as varchar(10)) + ' and ' + cast(@maxChars as varchar(10)) + '
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.minChars is not null
		and mdc.maxChars is not null
		and dt.dataTypeCode = 'CONTENTOBJ'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and mdc.columnName > @mincol
END


-- cleanup - these are no longer needed
IF OBJECT_ID('tempdb..#tblOrgCols') IS NOT NULL
	DROP TABLE #tblOrgCols
IF OBJECT_ID('tempdb..#tblImportCols') IS NOT NULL
	DROP TABLE #tblImportCols

-- ******************************** 
-- dump flat table and format file and creation script
-- would love to use xml format files, but it appears column names with spaces cause it to fail
-- ******************************** 
IF NOT EXISTS (select rowID from @tblErrors where fatal = 1) BEGIN
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Backup Flattened Data'

	SELECT @prefix = @orgcode + '_flat_' + convert(varchar(8),getdate(),112) + replace(convert(varchar(8),getdate(),108),':','')
	SELECT @flatfile = @pathToExport + @prefix

	select @exportcmd = 'bcp ' + @tmptbl + ' format nul -f ' + @flatfile + '.txt -n -T -S' + CAST(serverproperty('servername') as varchar(40))
	EXEC master..xp_cmdshell @exportcmd, NO_OUTPUT

	select @exportcmd = 'bcp ' + @tmptbl + ' out ' + @flatfile + '.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
	EXEC master..xp_cmdshell @exportcmd, NO_OUTPUT

	declare @createTableSQLTop varchar(100)          
	declare @coltypes table (datatype varchar(16))          
	insert into @coltypes values('bit')          
	insert into @coltypes values('binary')          
	insert into @coltypes values('bigint')          
	insert into @coltypes values('int')          
	insert into @coltypes values('float')          
	insert into @coltypes values('datetime')          
	insert into @coltypes values('text')          
	insert into @coltypes values('image')          
	insert into @coltypes values('money')          
	insert into @coltypes values('uniqueidentifier')          
	insert into @coltypes values('smalldatetime')          
	insert into @coltypes values('tinyint')          
	insert into @coltypes values('smallint')          
	insert into @coltypes values('sql_variant')          
	select @dynSQL = ''
	select @dynSQL = @dynSQL +           
		case when charindex('(',@dynSQL,1)<=0 then '(' else '' end + '[' + Column_Name + '] ' +Data_Type +
		case when Data_Type in (Select datatype from @coltypes) then '' else  '(' end+
		case when data_type in ('real','decimal','numeric')  then cast(isnull(numeric_precision,'') as varchar)+','+
		case when data_type in ('real','decimal','numeric') then cast(isnull(Numeric_Scale,'') as varchar) end
		when data_type in ('nvarchar','varchar') and cast(isnull(Character_Maximum_Length,'') as varchar) = '-1' then 'max'
		when data_type in ('char','nvarchar','varchar','nchar') then cast(isnull(Character_Maximum_Length,'') as varchar) else '' end+
		case when Data_Type in (Select datatype from @coltypes)then '' else  ')' end+
		case when Is_Nullable='No' then ' Not null,' else ' null,' end
		from tempdb.Information_Schema.COLUMNS where Table_Name=@tmptbl
		order by ordinal_position
	select @createTableSQLTop = 'Create table ##xxx ' 
	select @dynSQL=@createTableSQLTop + substring(@dynSQL,1,len(@dynSQL)-1) +' )'            
	if dbo.fn_WriteFile(@pathToExport + @prefix + '.sql', @dynSQL, 1) = 0 BEGIN
		EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Failed writing table creation script'
		RETURN 0
	END

	-- create index on temp table for numbers below
	EXEC('CREATE NONCLUSTERED INDEX [idx_' + @tmptbl + '_memnum_rowid] ON ' + @tmptbl + '([membernumber] ASC,[rowID] ASC)')
END ELSE BEGIN
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Backup Flattened Data Skipped - Fatal Errors'
END

-- ******************************** 
-- Counts
-- ******************************** 
IF NOT EXISTS (select rowID from @tblErrors where fatal = 1) BEGIN
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Querying Intermediate Totals'

	-- TotalCurrentMembers
	INSERT INTO @tblCounts (countName, countNum)
	SELECT 'TotalCurrentMembers', COUNT(memberid)
	FROM dbo.ams_members
	WHERE orgID = @orgID
	AND status <> 'D'
	and memberTypeID = 2
	AND memberID = activeMemberID

	-- TotalCurrentActive
	INSERT INTO @tblCounts (countName, countNum)
	SELECT 'TotalCurrentActive', COUNT(memberid)
	FROM dbo.ams_members
	WHERE orgID = @orgID
	AND status = 'A'
	and memberTypeID = 2
	AND memberID = activeMemberID

	-- TotalCurrentInActive
	INSERT INTO @tblCounts (countName, countNum)
	SELECT 'TotalCurrentInActive', COUNT(memberid)
	FROM dbo.ams_members
	WHERE orgID = @orgID
	AND status = 'I'
	and memberTypeID = 2
	AND memberID = activeMemberID

	-- TotalNewImported
	select @dynSQL = 'SELECT ''TotalNewImported'', Count(newlist.rowID)
		FROM ' + @tmptbl + ' as newlist
		LEFT OUTER JOIN dbo.ams_members as m on m.memberNumber = newlist.memberNumber
			AND m.orgID = ' + cast(@orgID as varchar(7)) + ' 
			and m.memberID = m.activeMemberID
			and m.memberTypeID = 2
		WHERE m.memberID is null'
	INSERT INTO @tblCounts (countName, countNum)
	EXEC(@dynSQL)
	
	-- TotalCurrentMembersWillActivated
	select @dynSQL = 'SELECT ''TotalCurrentMembersWillBeActivated'', COUNT(distinct m.memberID)
		FROM dbo.ams_members as m
		INNER JOIN ' + @tmptbl + ' as newlist on newlist.memberNumber = m.memberNumber 
			AND m.orgID = ' + cast(@orgID as varchar(7)) + ' 
			AND m.status = ''I''
			and m.memberID = m.activeMemberID
			and m.memberTypeID = 2'
	INSERT INTO @tblCounts (countName, countNum)
	EXEC(@dynSQL)

	-- TotalCurrentMembersWillBeInactivated
	select @dynSQL = 'SELECT ''TotalCurrentMembersWillBeInactivated'', COUNT(distinct m.memberID)
		FROM dbo.organizations as o
		INNER JOIN dbo.ams_members as m on m.orgID = o.orgID
			AND m.orgID = ' + cast(@orgID as varchar(7)) + ' 
			AND m.status = ''A''
			and m.memberID = m.activeMemberID
			and m.memberTypeID = 2
		LEFT OUTER JOIN ' + @tmptbl + ' as newlist on newlist.memberNumber = m.memberNumber 
		WHERE newlist.rowID is null'
	INSERT INTO @tblCounts (countName, countNum)
	EXEC(@dynSQL)

	-- TotalMembersImported
	select @dynSQL = 'SELECT ''TotalMembersImported'', COUNT(rowID)
		FROM ' + @tmptbl
	INSERT INTO @tblCounts (countName, countNum)
	EXEC(@dynSQL)

END ELSE BEGIN
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Querying Intermediate Totals Skipped - Fatal Errors'
END

-- ******************************** 
-- Member Lists
-- ******************************** 
IF NOT EXISTS (select rowID from @tblErrors where fatal = 1) BEGIN
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Querying Member Lists'

	-- members that will be activated 
	select @dynSQL = 'SELECT TOP 100 PERCENT m.memberID, m.memberNumber, m.firstname, m.lastname
		FROM dbo.ams_members as m WITH(NOLOCK)
		INNER JOIN ' + @tmptbl + ' as newlist on newlist.memberNumber = m.memberNumber 
			AND m.orgID = ' + cast(@orgID as varchar(7)) + '
			AND m.status = ''I''
			and m.memberID = m.activeMemberID
			and m.memberTypeID = 2
		ORDER BY newlist.rowID'
	INSERT INTO @tblMemList1 (memberid, memberNumber, firstname, lastname)
	EXEC(@dynSQL)

	-- members that will be inactivated
	select @dynSQL = 'SELECT TOP 100 PERCENT  m.memberID, m.memberNumber, m.firstname, m.lastname
		FROM dbo.organizations as o
		INNER JOIN dbo.ams_members as m on m.orgID = o.orgID
			AND m.orgID = ' + cast(@orgID as varchar(7)) + '
			AND m.status = ''A''
			and m.memberID = m.activeMemberID
			and m.memberTypeID = 2
		LEFT OUTER JOIN ' + @tmptbl + ' as newlist on newlist.memberNumber = m.memberNumber 
		WHERE newlist.rowID is null
		ORDER BY m.memberID'
	INSERT INTO @tblMemList2 (memberid, memberNumber, firstname, lastname)
	EXEC(@dynSQL)

	-- members that will be added
	select @dynSQL = 'SELECT TOP 100 PERCENT newlist.rowID, newlist.memberNumber, newlist.firstname, newlist.lastname
		FROM ' + @tmptbl + ' as newlist
		LEFT OUTER JOIN dbo.ams_members as m on m.memberNumber = newlist.memberNumber
			AND m.orgID = ' + cast(@orgID as varchar(7)) + '
			and m.memberID = m.activeMemberID
			and m.memberTypeID = 2
		WHERE m.memberID is null
		ORDER BY newlist.rowID'
	INSERT INTO @tblMemList3 (rowID, memberNumber, firstname, lastname)
	EXEC(@dynSQL)

END ELSE BEGIN
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Querying Member Lists Skipped - Fatal Errors'
END

-- ********************************
-- generate result xml file 
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Generating XML response'
select @importResult = (
	select getdate() as "@date", @flatfile as "@flatfile",
		isnull((select top 301 dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg", "@severity" = case fatal when 1 then 'fatal' else 'nonfatal' end
		from @tblErrors
		order by rowid
		FOR XML path('error'), root('errors'), type),'<errors/>'),

		isnull((select countName as "@name", countNum as "@num"
		from @tblCounts
		order by rowid
		FOR XML path('count'), root('counts'), type),'<counts/>'),

		isnull((select top 301 memberid as "@memberid", membernumber as "@membernumber", firstname as "@firstname", lastname as "@lastname"
		from @tblMemList1
		order by memberid
		FOR XML path('member'), root('qryactivated'), type),'<qryactivated/>'),

		isnull((select top 301 memberid as "@memberid", membernumber as "@membernumber", firstname as "@firstname", lastname as "@lastname"
		from @tblMemList2
		order by memberid
		FOR XML path('member'), root('qryinactivated'), type),'<qryinactivated/>'),

		isnull((select top 301 rowid as "@rowid", membernumber as "@membernumber", firstname as "@firstname", lastname as "@lastname"
		from @tblMemList3
		order by rowid
		FOR XML path('member'), root('qryadded'), type),'<qryadded/>'),

		isnull((select top 301 dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
		from @tblDataChanged
		order by rowid
		FOR XML path('change'), root('qrychanges'), type),'<qrychanges/>')
	for xml path('import'), TYPE)

-- drop temp tables 
IF OBJECT_ID('tempdb..' + @tmptbl) IS NOT NULL
	EXEC('DROP TABLE ' + @tmptbl)

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Import to Holding Complete'

RETURN 0
GO

ALTER PROC [dbo].[ams_getMemberFields]
@fieldsetID int

AS

set nocount on

declare @tblMF TABLE (fieldID int PRIMARY KEY, columnID int)
insert into @tblMF (fieldID, columnID)
select mf.fieldID, cast(case when left(mf.fieldCode,3) = 'md_' then replace(mf.fieldCode,'md_','') else 0 end as int) as columnID
from dbo.ams_memberFields as mf
where mf.fieldsetID = @fieldsetid

select isnull(cast((
	select	mfs.fieldsetName as '@fieldsetName', mfs.nameformat as '@nameformat', mfs.fieldSetID as '@fieldsetID', mfs.showHelp as '@showHelp',
			mfs.descriptionContentID as '@descriptionContentID', mfs.descriptionPosition as '@descriptionPosition',
		(
		select mf.fieldID, mf.dbObject, mf.dbObjectAlias, mf.dbField, mf.fieldCode, mf.mdColumnID, mf.fieldLabel, mf.fieldDescription, 
			mf.isRequired, mf.isGrouped, mf.displayTypeCode, mf.dataTypeCode, mf.isReadOnly, mf.allowNull, mf.minChars, mf.maxChars, 
			mf.minSelected, mf.maxSelected, mf.minValueInt, mf.maxValueInt, mf.minValueDecimal2, mf.maxValueDecimal2, mf.minValueDate, 
			mf.maxValueDate, mf.allowMultiple, opt.columnValueDecimal2, opt.columnValueInteger, opt.columnvalueDate, opt.columnValueBit, 
			opt.columnValueXML, opt.columnValueSiteResourceID, 
			case when left(mf.fieldCode,4) = 'grp_' or left(mf.fieldCode,5) = 'acct_' then 0 else opt.valueID end as valueID, 
			case 
				when mf.dbObjectAlias = 'subs' and left(mf.dbField,3) = 'sub' then (select coalesce(reportCode,subscriptionName) from dbo.sub_subscriptions where subscriptionID = parseName(replace(mf.dbField,'_','.'),2)) 
				when mf.dbObjectAlias = 'subs' then (select typeName from dbo.sub_Types where typeID = parseName(replace(mf.dbField,'_','.'),2)) 
				when mf.dbObjectAlias = 'grps' then (select coalesce(groupcode,groupName) from dbo.ams_groups where groupID = parseName(replace(mf.fieldCode,'_','.'),1)) 
				when mf.dbObjectAlias = 'acct' and left(mf.dbField,13) = 'acct_balance_' then isnull((select profileName from dbo.mp_profiles where profileID = parseName(replace(mf.dbField,'_','.'),1)),'Total') 
				else opt.columnValueString 
				end as columnValueString
		from (
			select amf.fieldID, amf.dbObject, amf.dbObjectAlias, amf.dbField, amf.fieldCode, amf.fieldLabel, amf.fieldDescription, 
				amf.isRequired, amf.isGrouped, dt.displayTypeCode, ddt.dataTypeCode, amf.fieldOrder,
				case when left(amf.fieldcode,4) = 'mad_' then 1 when tmp.columnID is not null then mdc.isReadOnly else 0 end as isReadOnly,
				case when tmp.columnID is not null then mdc.allowNull else 1 end as allowNull,
				isnull(mdc.allowMultiple,0) as allowMultiple,
				case when dt.displayTypeCode in ('SELECT','RADIO','CHECKBOX') and tmp.columnID is not null then tmp.columnID else 0 end as mdColumnID, 
				mdc.minChars, mdc.maxChars, mdc.minSelected, mdc.maxSelected, mdc.minValueInt, mdc.maxValueInt, mdc.minValueDecimal2,
				mdc.maxValueDecimal2, mdc.minValueDate, mdc.maxValueDate
			from @tblMF as tmp
			inner join dbo.ams_memberFields as amf on amf.fieldID = tmp.fieldID
			inner join dbo.ams_memberDataColumnDisplayTypes as dt on dt.displayTypeID = amf.displayTypeID
			inner join dbo.ams_memberDataColumnDataTypes as ddt on ddt.dataTypeID = amf.dataTypeID
			left outer join dbo.ams_memberDataColumns as mdc on mdc.columnID = tmp.columnID
		) as mf
		left outer join dbo.ams_memberDataColumnValues as opt on opt.columnID = mf.mdColumnID and mf.mdColumnID > 0	
		order by mf.isGrouped desc, mf.fieldOrder, opt.columnValueString, opt.columnValueDecimal2, opt.columnValueInteger, 
			opt.columnvalueDate, opt.columnValueBit, cast(opt.columnValueXML as varchar(max)), opt.columnValueSiteResourceID
		FOR XML AUTO, TYPE
		)
	from dbo.ams_memberFieldSets as mfs
	where mfs.fieldsetID = @fieldsetid
	FOR XML PATH('fields')
) as XML),'<fields/>') as fieldsXML

RETURN 0
GO

ALTER PROC [dbo].[ams_getMemberFields]
@fieldsetID int

AS

set nocount on

declare @tblMF TABLE (fieldID int PRIMARY KEY, columnID int)
insert into @tblMF (fieldID, columnID)
select mf.fieldID, cast(case when left(mf.fieldCode,3) = 'md_' then replace(mf.fieldCode,'md_','') else 0 end as int) as columnID
from dbo.ams_memberFields as mf
where mf.fieldsetID = @fieldsetid

select isnull(cast((
	select	mfs.fieldsetName as '@fieldsetName', mfs.nameformat as '@nameformat', mfs.fieldSetID as '@fieldsetID', mfs.showHelp as '@showHelp',
			mfs.descriptionContentID as '@descriptionContentID', mfs.descriptionPosition as '@descriptionPosition',
		(
		select mf.fieldID, mf.dbObject, mf.dbObjectAlias, mf.dbField, mf.fieldCode, mf.mdColumnID, mf.fieldLabel, mf.fieldDescription, 
			mf.isRequired, mf.isGrouped, mf.displayTypeCode, mf.dataTypeCode, mf.isReadOnly, mf.allowNull, mf.minChars, mf.maxChars, 
			mf.minSelected, mf.maxSelected, mf.minValueInt, mf.maxValueInt, mf.minValueDecimal2, mf.maxValueDecimal2, mf.minValueDate, 
			mf.maxValueDate, mf.allowMultiple, opt.columnValueDecimal2, opt.columnValueInteger, opt.columnvalueDate, opt.columnValueBit, 
			opt.columnValueXML, opt.columnValueSiteResourceID, 
			case when left(mf.fieldCode,4) = 'grp_' or left(mf.fieldCode,5) = 'acct_' then 0 else opt.valueID end as valueID, 
			case 
				when mf.dbObjectAlias = 'subs' and left(mf.dbField,3) = 'sub' then (select coalesce(reportCode,subscriptionName) from dbo.sub_subscriptions where subscriptionID = parseName(replace(mf.dbField,'_','.'),2)) 
				when mf.dbObjectAlias = 'subs' then (select typeName from dbo.sub_Types where typeID = parseName(replace(mf.dbField,'_','.'),2)) 
				when mf.dbObjectAlias = 'grps' then (select coalesce(groupcode,groupName) from dbo.ams_groups where groupID = parseName(replace(mf.fieldCode,'_','.'),1)) 
				when mf.dbObjectAlias = 'acct' and left(mf.dbField,13) = 'acct_balance_' then isnull((select profileName from dbo.mp_profiles where profileID = parseName(replace(mf.dbField,'_','.'),1)),'Total') 
				else opt.columnValueString 
				end as columnValueString
		from (
			select amf.fieldID, amf.dbObject, amf.dbObjectAlias, amf.dbField, amf.fieldCode, amf.fieldLabel, amf.fieldDescription, 
				amf.isRequired, amf.isGrouped, dt.displayTypeCode, ddt.dataTypeCode, amf.fieldOrder,
				case when left(amf.fieldcode,4) = 'mad_' then 1 when mdc.isReadOnly is not null then mdc.isReadOnly else 0 end as isReadOnly,
				case when mdc.allowNull is not null then mdc.allowNull else 1 end as allowNull,
				isnull(mdc.allowMultiple,0) as allowMultiple, tmp.columnID as mdColumnID, 
				mdc.minChars, mdc.maxChars, mdc.minSelected, mdc.maxSelected, mdc.minValueInt, mdc.maxValueInt, mdc.minValueDecimal2,
				mdc.maxValueDecimal2, mdc.minValueDate, mdc.maxValueDate
			from @tblMF as tmp
			inner join dbo.ams_memberFields as amf on amf.fieldID = tmp.fieldID
			inner join dbo.ams_memberDataColumnDisplayTypes as dt on dt.displayTypeID = amf.displayTypeID
			inner join dbo.ams_memberDataColumnDataTypes as ddt on ddt.dataTypeID = amf.dataTypeID
			left outer join dbo.ams_memberDataColumns as mdc on mdc.columnID = tmp.columnID
		) as mf
		left outer join dbo.ams_memberDataColumnValues as opt on opt.columnID = mf.mdColumnID and mf.mdColumnID > 0	
		order by mf.isGrouped desc, mf.fieldOrder, opt.columnValueString, opt.columnValueDecimal2, opt.columnValueInteger, 
			opt.columnvalueDate, opt.columnValueBit, cast(opt.columnValueXML as varchar(max)), opt.columnValueSiteResourceID
		FOR XML AUTO, TYPE
		)
	from dbo.ams_memberFieldSets as mfs
	where mfs.fieldsetID = @fieldsetid
	FOR XML PATH('fields')
) as XML),'<fields/>') as fieldsXML

RETURN 0
GO

ALTER Function [dbo].[fn_intListToTable] (@intlist varchar(max), @delimiter varchar(1)) 
returns @IntTable table (autoid int IDENTITY (1,1), listitem int)
AS
begin

	set @intlist = nullIf(@intlist,'')

	declare @strAsXML as xml
	set @strAsXML = cast(('<x>'+replace(@intlist,@delimiter,'</x><x>')+'</x>') as xml)
	
	insert into @inttable(listitem)
	select N.value('.','int') as value
	from @strAsXML.nodes('x') as T(N)

	return
end
GO

ALTER Function [dbo].[fn_decimal2ListToTable] (@intlist varchar(max), @delimiter varchar(1)) 
returns @IntTable table (autoid int IDENTITY (1,1), listitem decimal(9,2))
AS
begin

	set @intlist = nullIf(@intlist,'')

	declare @strAsXML as xml
	set @strAsXML = cast(('<x>'+replace(@intlist,@delimiter,'</x><x>')+'</x>') as xml)
	
	insert into @inttable(listitem)
	select N.value('.','decimal(9,2)') as value
	from @strAsXML.nodes('x') as T(N)

	return
end
GO

ALTER PROC [dbo].[ams_removeMemberDataColumnValue]
@valueID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @orgID int, @columnID int
	declare @itemGroupUID uniqueidentifier, @memberIDList varchar(max)
	declare @dataTypeCode varchar(20), @minChars int, @maxChars int, @minSelected int, @maxSelected int, 
		@minValueInt int, @maxValueInt int, @minValueDecimal2 decimal(9,2), @maxValueDecimal2 decimal(9,2), 
		@minValueDate datetime, @maxValueDate datetime

	select @orgID=mdc.orgID, @columnID=mdc.columnID, @minChars=mdc.minChars, @maxChars=mdc.maxChars, 
		@minSelected=mdc.minSelected, @maxSelected=mdc.maxSelected, @minValueInt=mdc.minValueInt, 
		@maxValueInt=mdc.maxValueInt, @minValueDecimal2=mdc.minValueDecimal2, @maxValueDecimal2=mdc.maxValueDecimal2, 
		@minValueDate=mdc.minValueDate, @maxValueDate=mdc.maxValueDate
	from dbo.ams_memberDataColumnValues as mdcv
	inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
	where mdcv.valueID = @valueID

	-- update members with this value
	UPDATE dbo.ams_members
	SET dateLastUpdated = getdate()
	WHERE memberID in (
		select memberID 
		from dbo.ams_memberData 
		where valueID = @valueID
	)	

	-- what conditions use this value?
	declare @tblCond TABLE (conditionID int, cvid int)
	INSERT INTO @tblCond (conditionID, cvid)
	select distinct vgc.conditionID, vgcv.cvid
	from dbo.ams_memberDataColumnValues as mdcv
	inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
	inner join dbo.ams_virtualGroupConditions as vgc on vgc.fieldcode = 'md_' + cast(mdc.columnID as varchar(10))
	inner join dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
	and vgcv.conditionValue = cast(@valueID as varchar(10))
	where mdcv.valueID = @valueID

	-- if tied to conditions, need to recalc members who have this value (@runSchedule=2 indicates delayed processing) 
	IF EXISTS (select * from @tblCond) BEGIN
		select @memberIDList = COALESCE(@memberIDList + ',', '') + cast(m.activeMemberID as varchar(10)) 
			from dbo.ams_memberData as md
			inner join dbo.ams_members as m on m.memberid = md.memberid
			where md.valueID = @valueID
			group by m.activeMemberID
	END

	-- delete from memberdata
	DELETE FROM dbo.ams_memberData
	where valueID = @valueID

	-- remove value
	DELETE from dbo.ams_memberDataColumnValues
	where valueID = @valueID

	-- remove value from conditions
	DELETE cv
	from dbo.ams_virtualGroupConditionValues as cv
	inner join @tblCond as tmp on tmp.cvid = cv.cvid

	-- delete condition if they no longer have values
	declare @CIDList varchar(max)
	select @CIDList = COALESCE(@CIDList + ',', '') + cast(tmp.conditionID as varchar(10)) 
		from @tblCond as tmp
		where exists (
			select count(cvid)
			from dbo.ams_virtualGroupConditionValues
			where conditionID = tmp.conditionID
			having count(cvid) = 0
		)
		group by tmp.conditionID
	IF @CIDList is not null
		EXEC dbo.ams_deleteVirtualGroupCondition @orgID=@orgID, @conditionIDList=@CIDList

	-- update verbose if condition has other values
	UPDATE vgc
	set vgc.verbose = dbo.ams_getVirtualGroupConditionVerbose(vgc.conditionID)
	from dbo.ams_virtualGroupConditions as vgc
	inner join @tblCond as tbl on tbl.conditionID = vgc.conditionID
	where exists (
		select count(cvid)
		from dbo.ams_virtualGroupConditionValues
		where conditionID = tbl.conditionID
		having count(cvid) > 0
	)

	-- check custom field validation ranges against existing data
	IF @minChars is not null and @maxChars is not null BEGIN
		IF @dataTypeCode = 'STRING' BEGIN
			IF EXISTS(
				select top 1 valueID
				from dbo.ams_memberdatacolumnValues
				where columnID = @columnID
				and len(columnValueString) > 0
				and len(columnValueString) not between @minChars and @maxChars
			)
			RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1) 
		END
		IF @dataTypeCode = 'CONTENTOBJ' BEGIN
			IF EXISTS(
				select top 1 mdcv.valueID
				from dbo.ams_memberdatacolumnValues as mdcv
				inner join dbo.cms_content as c on c.siteResourceID = mdcv.columnValueSiteResourceID
				inner join dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID and cl.languageID = 1
				inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
				where mdcv.columnID = @columnID
				and len(cv.rawContent) > 0
				and len(cv.rawContent) not between @minChars and @maxChars
			)
			RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1) 
		END
	END
	IF @minSelected is not null and @maxSelected is not null BEGIN
		IF EXISTS(select columnID from dbo.ams_memberDataColumns where columnID = @columnID and allowMultiple = 1)
		AND EXISTS(
			select top 1 md.memberid
			from dbo.ams_memberData as md
			inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
			where mdcv.columnID = @columnID
			group by md.memberid
			having count(*) not between @minSelected and @maxSelected
		)
		RAISERROR('There are existing members that have field options that are outside the data validation range.', 16, 1) 
	END
	IF @minValueInt is not null and @maxValueInt is not null BEGIN
		IF EXISTS(
			select top 1 valueID
			from dbo.ams_memberdatacolumnValues
			where columnID = @columnID
			and columnValueInteger is not null
			and columnValueInteger not between @minValueInt and @maxValueInt
		)
		RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1) 
	END
	IF @minValueDecimal2 is not null and @maxValueDecimal2 is not null BEGIN
		IF EXISTS(
			select top 1 valueID
			from dbo.ams_memberdatacolumnValues
			where columnID = @columnID
			and columnValueDecimal2 is not null
			and columnValueDecimal2 not between @minValueDecimal2 and @maxValueDecimal2
		)
		RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1) 
	END
	IF @minValueDate is not null and @maxValueDate is not null BEGIN
		IF EXISTS(
			select top 1 valueID
			from dbo.ams_memberdatacolumnValues
			where columnID = @columnID
			and columnValueDate is not null
			and columnValueDate not between @minValueDate and @maxValueDate
		)
		RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1) 
	END


	IF @TranCounter = 0
		COMMIT TRAN;

	-- if we need to call processMemberGroups
	IF @memberIDList is not null 	
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberIDList, @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

