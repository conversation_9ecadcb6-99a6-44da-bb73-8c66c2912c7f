use membercentral
GO

IF  EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME ='FK_ev_subEventRegistrants_ev_events_sub')
BEGIN
	print 'Relationship FK_ev_subEventRegistrants_ev_events_sub Exists! Deleting...'
	ALTER TABLE ev_subEventRegistrants DROP CONSTRAINT FK_ev_subEventRegistrants_ev_events_sub
END
GO

IF  EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME ='FK_ev_subEventRegistrants_ev_registrants')
BEGIN
	print 'Relationship FK_ev_subEventRegistrants_ev_registrants Exists! Deleting...'
	ALTER TABLE ev_subEventRegistrants DROP CONSTRAINT FK_ev_subEventRegistrants_ev_registrants
END
GO

IF OBJECT_ID('ev_subEventRegistrants') IS NOT NULL 
	EXEC('DROP TABLE  ev_subEventRegistrants')
GO

create table ev_subEventRegistrants(
	subEventRegistrantID	int IDENTITY(1,1) NOT NULL PRIMARY KEY CLUSTERED,
	eventID	int	 NULL,
	registrantID int NOT NULL
)
GO


print 'Creating relationship FK_ev_subEventRegistrants_ev_events_sub...'
ALTER TABLE ev_subEventRegistrants  WITH NOCHECK ADD  CONSTRAINT FK_ev_subEventRegistrants_ev_events_sub FOREIGN KEY(eventID)
REFERENCES dbo.ev_events (eventID)
GO

print 'Creating relationship FK_ev_subEventRegistrants_ev_registrants...'
ALTER TABLE ev_subEventRegistrants  WITH NOCHECK ADD  CONSTRAINT FK_ev_subEventRegistrants_ev_registrants FOREIGN KEY(registrantID)
REFERENCES dbo.ev_registrants (registrantID)
GO
