-- WA-28 
use search
GO

-- reorder buckets to prep for new buckets
update dbo.tblSearchBuckets 
set bucketOrder = bucketOrder + 5
where siteid = 21
and isActive = 1
and bucketGroupID = 1
and bucketorder > 3

-- search bucket bid=3322 deleted  plush  Trial Briefs, Appellate Briefs, and Amicus Briefs
update dbo.tblSearchBuckets set isActive=0 where siteid = 21 and bucketID=3322
update dbo.tblSearchBuckets set isActive=0 where siteid = 21 and bucketID=3324
update dbo.tblSearchBuckets set isActive=0 where siteid = 21 and bucketID=3321
update dbo.tblSearchBuckets set isActive=0 where siteid = 21 and bucketID=3293

-- new search bucket created that points to the Eagle Exchange fileshare2 instead of fileshare1
-- rename Eagle Exchange to Eagle Documents (rename)
insert into tblSearchBuckets (siteid, bucketName, bucketGroupID, bucketTypeID, bucketOrder, bucketSettings, hideUntilSearched, isActive, restrictToGroupID, hideIfRestricted)
select siteid, 'EAGLE Documents', bucketGroupID, 34, bucketOrder, '<settings fileShareID="842" />', hideUntilSearched, 1, restrictToGroupID, hideIfRestricted
from dbo.tblSearchBuckets 
where siteid = 21 and bucketID=3322

-- add EAGLE Expert Documents fileshare2 below Eagle Documents
insert into tblSearchBuckets (siteid, bucketName, bucketGroupID, bucketTypeID, bucketOrder, bucketSettings, hideUntilSearched, isActive, restrictToGroupID, hideIfRestricted)
select siteid, 'EAGLE Expert Documents', bucketGroupID, 34, 4, '<settings fileShareID="840" />', hideUntilSearched, 1, restrictToGroupID, hideIfRestricted
from dbo.tblSearchBuckets 
where siteid = 21 and bucketID=3322

-- add WSAJ Published Documents fileshare below Eagle Expert Documents
insert into tblSearchBuckets (siteid, bucketName, bucketGroupID, bucketTypeID, bucketOrder, bucketSettings, hideUntilSearched, isActive, restrictToGroupID, hideIfRestricted)
select siteid, 'WSAJ Published Documents', bucketGroupID, 34, 5, '<settings fileShareID="841" />', hideUntilSearched, 1, restrictToGroupID, hideIfRestricted
from dbo.tblSearchBuckets 
where siteid = 21 and bucketID=3322

-- add a new search bucket for the "EAGLE Legal Staff" fileshare2 named "Eagle Legal Staff" below WSAJ Published Documents
insert into tblSearchBuckets (siteid, bucketName, bucketGroupID, bucketTypeID, bucketOrder, bucketSettings, hideUntilSearched, isActive, restrictToGroupID, hideIfRestricted)
select siteid, 'EAGLE Legal Staff', bucketGroupID, 34, 6, '<settings fileShareID="843" />', hideUntilSearched, 1, restrictToGroupID, hideIfRestricted
from dbo.tblSearchBuckets 
where siteid = 21 and bucketID=3322

-- change Trial Briefs, Appellate Briefs, and Amicus Briefs to point to fileshare2
insert into tblSearchBuckets (siteid, bucketName, bucketGroupID, bucketTypeID, bucketOrder, bucketSettings, hideUntilSearched, isActive, restrictToGroupID, hideIfRestricted)
select siteid, 'Trial Briefs', bucketGroupID, 34, bucketOrder, '<settings fileShareID="880" />', hideUntilSearched, 1, restrictToGroupID, hideIfRestricted
from dbo.tblSearchBuckets 
where siteid = 21 and bucketID=3324

insert into tblSearchBuckets (siteid, bucketName, bucketGroupID, bucketTypeID, bucketOrder, bucketSettings, hideUntilSearched, isActive, restrictToGroupID, hideIfRestricted)
select siteid, 'Appellate Briefs', bucketGroupID, 34, bucketOrder, '<settings fileShareID="848" />', hideUntilSearched, 1, restrictToGroupID, hideIfRestricted
from dbo.tblSearchBuckets 
where siteid = 21 and bucketID=3321

insert into tblSearchBuckets (siteid, bucketName, bucketGroupID, bucketTypeID, bucketOrder, bucketSettings, hideUntilSearched, isActive, restrictToGroupID, hideIfRestricted)
select siteid, 'Amicus Briefs', bucketGroupID, 34, bucketOrder, '<settings fileShareID="847" />', hideUntilSearched, 1, restrictToGroupID, hideIfRestricted
from dbo.tblSearchBuckets 
where siteid = 21 and bucketID=3293

GO