-- DEV Run time: 00:00:17
use membercentral
GO

declare 
	@siteID int, 
	@orgID int, 
	@contactTypeColumnID int, 
	@thisColumnID int, 
	@thisColumnName varchar	(255), 
	@valueID int, 
	@thisItem int,
	@memberID int,
	@columnName varchar	(255) 

set @columnName = 'Contact Type' 

declare @memberData as table (thisItem int  IDENTITY(1,1), columnID int , columnName varchar	(255), valueID int, columnValueString varchar(255), memberID int)

declare @practiceAreas as table (valueID int, columnValue varchar	(255))

select @siteID = siteID, @orgID = orgID from sites where siteCode = 'SA'

select @contactTypeColumnID = columnID from dbo.ams_memberDataColumns  mdc where orgID = @orgID and columnName = @columnName

select @contactTypeColumnID 

declare @VendorRTID int
select @VendorRTID = recordTypeID from ams_recordTypes where orgID = @orgID and recordTypeCode = 'VendorFirm'
select @VendorRTID as 'Vendor Firm'

declare @BusinessAssociateRTID int
select @BusinessAssociateRTID = recordTypeID from ams_recordTypes where orgID = @orgID and recordTypeCode = 'BusinessAssociate'
select @BusinessAssociateRTID as 'Business Associate'

declare @LawFirmRTID int
select @LawFirmRTID = recordTypeID from ams_recordTypes where orgID = @orgID and recordTypeCode = 'LawFirm'
select @LawFirmRTID as 'Law Firm'

declare @IndividualRTID int
select @IndividualRTID = recordTypeID from ams_recordTypes where orgID = @orgID and recordTypeCode = 'Individual'
select @IndividualRTID as 'Individual'

declare @RepresentativeTID int
select @RepresentativeTID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgID and relationshipTypeCode = 'Representative'
select @RepresentativeTID as 'Representative'

declare @AttorneyTID int
select @AttorneyTID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgID and relationshipTypeCode = 'Attorney'
select @AttorneyTID as 'Attorney'
declare @LawStudentTID int
select @LawStudentTID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgID and relationshipTypeCode = 'LawStudent'
select @LawStudentTID as 'Law Student'
declare @NonAttorneyTID int
select @NonAttorneyTID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgID and relationshipTypeCode = 'Non Attorney'
select @NonAttorneyTID as 'Non Attorney'


declare @BusinessAssociateRTRTID int
select @BusinessAssociateRTRTID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes where masterRecordTypeID = @BusinessAssociateRTID and childRecordTypeID = @IndividualRTID and relationshipTypeID = @RepresentativeTID
select @BusinessAssociateRTRTID as 'BusinessAssociateRTRTID'

declare @VendorRTRTID int
select @VendorRTRTID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes where masterRecordTypeID = @VendorRTID and childRecordTypeID = @IndividualRTID and relationshipTypeID = @RepresentativeTID
select @VendorRTRTID as 'VendorRTRTID'

declare @AttorneyRTRTID int
select @AttorneyRTRTID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes where masterRecordTypeID = @LawFirmRTID and childRecordTypeID = @IndividualRTID and relationshipTypeID = @AttorneyTID
select @AttorneyRTRTID as 'AttorneyRTRTID'
declare @LawStudentRTRTID int
select @LawStudentRTRTID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes where masterRecordTypeID = @LawFirmRTID and childRecordTypeID = @IndividualRTID and relationshipTypeID = @LawStudentTID
select @LawStudentRTRTID as 'LawStudentRTRTID'
declare @NonAttorneyRTRTID int
select @NonAttorneyRTRTID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes where masterRecordTypeID = @LawFirmRTID and childRecordTypeID = @IndividualRTID and relationshipTypeID = @NonAttorneyTID
select @NonAttorneyRTRTID as 'NonAttorneyRTRTID'



-- Get existing custom fields this assume all fields define as boolean.
insert into @memberData (columnID, columnName, valueID, columnValueString, memberID)
select 
	mdc.columnID, mdc.columnName, mdcv.valueID, mdcv.columnValueString, md.memberID
from 
	dbo.ams_memberDataColumns  mdc 
	inner join dbo.ams_memberDataColumnValues mdcv on
		mdcv.columnID = mdc.columnID
	inner join dbo.ams_memberData md on
		md.valueID = mdcv.valueID
where 
	orgID = @orgID 
	and mdc.columnID = @contactTypeColumnID

--select distinct columnValueString from @memberData

-- clear recordtypeid
print 'clearing firm recordtypeid'
update  ams_members  
	set recordTypeID = null
where orgid = @orgID
and membernumber like 'FIRM%'


-- Update Vendors
update m
	set m.recordTypeID = @VendorRTID
from ams_members m
inner join @memberData md on 
	md.memberID = m.memberid
	and md.columnValueString = 'Firm Master Record'
inner join @memberData md2 on 
	md2.memberID = m.memberid
	and md2.columnValueString = 'Vendor'
where m.orgid = @orgID
and m.membernumber like 'FIRM%'
print 'set Vendors recordtypeid'

-- Update Business Associate 
update m
	set m.recordTypeID = @BusinessAssociateRTID
from ams_members m
inner join @memberData md on 
	md.memberID = m.memberid
	and md.columnValueString = 'Firm Master Record'
inner join @memberData md2 on 
	md2.memberID = m.memberid
	and (md2.columnValueString = 'Organizations-TLA' OR md2.columnValueString = 'Organizations-SD'  OR md2.columnValueString = 'Others - Does Not Qualify')
where m.orgid = @orgID
and m.membernumber like 'FIRM%'
print 'set Business Associate recordtypeid'

-- Update Law Firms
update m
	set m.recordTypeID = @LawFirmRTID
from ams_members m
inner join @memberData md on 
	md.memberID = m.memberid
	and md.columnValueString = 'Firm Master Record'
where m.orgid = @orgID
and m.membernumber like 'FIRM%'
and m.recordTypeID is null
print 'set Law Firms recordtypeid'

-- set all other records as Individual 
update m
	set m.recordTypeID = @IndividualRTID
from ams_members m
where m.orgid = @orgID
and m.recordTypeID is null
print 'set Individual recordtypeid'

-- Linked role Representative
INSERT INTO dbo.ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive)
select distinct @VendorRTRTID, firmMember.memberID, m.memberid, 1
from dbo.ams_members as m
inner join dbo.ams_memberData as md on md.memberID = m.memberID
inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
inner join dbo.ams_members as firmMember on  firmMember.orgid = @orgID 
	and firmMember.membernumber = mdcv.columnValueString 
	and firmMember.recordTypeID = @VendorRTID
where m.orgID = @orgID
and m.status <> 'D'
and m.recordTypeID = @IndividualRTID
and mdc.columnName = 'Company ID in Access'
print 'Linked Vendor Roles'

INSERT INTO dbo.ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive)
select distinct @BusinessAssociateRTRTID, firmMember.memberID, m.memberid, 1
from dbo.ams_members as m
inner join dbo.ams_memberData as md on md.memberID = m.memberID
inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
inner join dbo.ams_members as firmMember on  firmMember.orgid = @orgID 
	and firmMember.membernumber = mdcv.columnValueString 
	and firmMember.recordTypeID = @BusinessAssociateRTID
where m.orgID = @orgID
and m.status <> 'D'
and m.recordTypeID = @IndividualRTID
and mdc.columnName = 'Company ID in Access'
print 'Linked Business Associate Roles'


INSERT INTO dbo.ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive)
select distinct @AttorneyRTRTID, firmMember.memberID, m.memberid, 1
from dbo.ams_members as m
inner join dbo.ams_memberData as md on md.memberID = m.memberID
inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
inner join dbo.ams_memberData as md2 on md2.memberID = m.memberID
inner join dbo.ams_memberDataColumnValues as mdcv2 on mdcv2.valueID = md2.valueID
inner join dbo.ams_memberDataColumns as mdc2 on mdc2.columnID = mdcv2.columnID
inner join dbo.ams_members as firmMember on  firmMember.orgid = @orgID 
	and firmMember.membernumber = mdcv.columnValueString 
	and firmMember.recordTypeID = @LawFirmRTID
where m.orgID = @orgID
and m.status <> 'D'
and m.recordTypeID = @IndividualRTID
and mdc.columnName = 'Company ID in Access'
and mdc2.columnName = 'Contact Type'
and mdcv2.columnValueString in ('Attorney')
print 'Linked Law Firm Roles Attorney'


INSERT INTO dbo.ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive)
select distinct @LawStudentRTRTID, firmMember.memberID, m.memberid, 1
from dbo.ams_members as m
inner join dbo.ams_memberData as md on md.memberID = m.memberID
inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
inner join dbo.ams_memberData as md2 on md2.memberID = m.memberID
inner join dbo.ams_memberDataColumnValues as mdcv2 on mdcv2.valueID = md2.valueID
inner join dbo.ams_memberDataColumns as mdc2 on mdc2.columnID = mdcv2.columnID
inner join dbo.ams_members as firmMember on  firmMember.orgid = @orgID 
	and firmMember.membernumber = mdcv.columnValueString 
	and firmMember.recordTypeID = @LawFirmRTID
where m.orgID = @orgID
and m.status <> 'D'
and m.recordTypeID = @IndividualRTID
and mdc.columnName = 'Company ID in Access'
and mdc2.columnName = 'Contact Type'
and mdcv2.columnValueString in ('Student')
print 'Linked Law Firm Roles Law Student'


INSERT INTO dbo.ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive)
select distinct @NonAttorneyRTRTID, firmMember.memberID, m.memberid, 1
from dbo.ams_members as m
inner join dbo.ams_memberData as md on md.memberID = m.memberID
inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
inner join dbo.ams_memberData as md2 on md2.memberID = m.memberID
inner join dbo.ams_memberDataColumnValues as mdcv2 on mdcv2.valueID = md2.valueID
inner join dbo.ams_memberDataColumns as mdc2 on mdc2.columnID = mdcv2.columnID
inner join dbo.ams_members as firmMember on  firmMember.orgid = @orgID 
	and firmMember.membernumber = mdcv.columnValueString 
	and firmMember.recordTypeID = @LawFirmRTID
where m.orgID = @orgID
and m.status <> 'D'
and m.recordTypeID = @IndividualRTID
and mdc.columnName = 'Company ID in Access'
and mdc2.columnName = 'Contact Type'
and mdcv2.columnValueString in ('Non Attorney')
print 'Linked Law Firm Roles Non Attorney'

GO


