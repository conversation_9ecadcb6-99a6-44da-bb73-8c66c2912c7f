ALTER FUNCTION fn_getSocialNetworkMemberID
(
	@listName varchar(100),
	@emailaddress varchar(250)
)
RETURNS int
AS
BEGIN
	
	DECLARE @snMemberID int


	select @snMemberID = snp.memberID
	from trialslyris1.dbo.members_ lm
	inner join trialslyris1.dbo.lists_format lf
		on lm.list_ = lf.name
		and lm.list_ = @listname
		and lm.emailaddr_ = @emailaddress
	inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
	inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
	inner join membercentral.membercentral.dbo.sites s on s.siteID = sr.siteID
	inner join membercentral.membercentral.dbo.networkSites ns
		on ns.siteID = s.siteID
		and ns.isLoginNetwork = 1
	inner join membercentral.membercentral.dbo.organizations o
		on s.orgID = o.orgID
		and o.orgcode = lf.orgcode collate Latin1_General_CI_AI
	inner join membercentral.membercentral.dbo.ams_members m 
		on lm.externalMemberID = m.memberNumber collate Latin1_General_CI_AI
	inner join membercentral.membercentral.dbo.ams_memberNetworkProfiles mnp on m.activememberID = mnp.memberID
	inner join membercentral.membercentral.dbo.ams_networkProfiles np
		on mnp.ProfileID = np.profileID
		and np.networkID = ns.networkID
	inner join membercentral.membercentral.dbo.comm_lists cl on l.listID = cl.listID
	inner join membercentral.membercentral.dbo.comm_communities c on cl.communityID = c.communityID
	inner join membercentral.membercentral.dbo.sn_pages sp on c.applicationInstanceID = sp.applicationInstanceID
	inner join membercentral.membercentral.dbo.sn_socialNetworks sn on sn.socialNetworkID = sp.socialNetworkID
	inner join membercentral.membercentral.dbo.sn_profile snp
		on snp.networkProfileID = np.profileID
		and snp.socialNetworkID = sn.masterSocialNetworkID


	RETURN @snMemberID

END
GO
