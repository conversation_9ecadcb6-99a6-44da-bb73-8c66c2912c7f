use seminarweb
go

ALTER TABLE dbo.tblSeminarsSWLive ADD confirmationEmailInstruct bit NOT NULL DEFAULT(1);
GO

ALTER PROC [dbo].[swl_getSeminar]
@seminarID int

AS

select top 1 'SWL' as SWType, s.seminarName, s.seminarDesc, s.isPublished, s.offerCertificate, 
	s.allowRegistrants, s.isPriceBasedOnActual, s.productCode, s.seminarDescText, 
	swl.liveID, swl.seminarID, swl.swlTypeID, swl.gotoMeetingID, swl.gotoMeetingRegisterID, swl.agenda, swl.dateStart, swl.dateEnd, 
	swl.wddxTimeZones, swl.programPassword, swl.lineProvider, swl.phoneAdmin, swl.phoneAttendee, swl.codeAdmin, 
	swl.codeAttendee, swl.codeSpeaker, swl.surveyLink, swl.offerCredit, swl.offerDVD, swl.sendCountToPremiere, 
	swl.premiereUsePIN, swl.premiereConfID, swl.priceSyndication, swl.isOpen, swl.isNATLE, swl.materialsLink,
	s.adID, swl.confirmationEmailInstruct
from dbo.tblSeminars s
inner join dbo.tblSeminarsSWLive swl on swl.seminarID = s.seminarID
where s.seminarID = @seminarID
AND s.isDeleted = 0

GO

