ALTER PROC dbo.ky_listServices
AS

IF OBJECT_ID('tempdb..#tmpKYLists') IS NOT NULL 
	DROP TABLE #tmpKYLists;

-- get all KY lists
select [name] as listname
into #tmpKYLists
from trialsLyris1.dbo.lists_format 
where orgcode = 'KY'
and [name] <> 'eclips_ky';

-- get membernumbers on those lists from lyris
delete from MEMBERCENTRAL.datatransfer.dbo.ky_listServicesEligible;

insert into MEMBERCENTRAL.datatransfer.dbo.ky_listServicesEligible (memberid_, MCMemberNumber, MCMemberID)
select m.memberid_, m.externalMemberID, null
from trialsLyris1.dbo.members_ as m 
inner join #tmpKYLists as tmp on tmp.listname = m.list_
where m.externalMemberID is not null;

-- populate data
EXEC MEMBERCENTRAL.customApps.dbo.ky_listServicesEligible;

-- get data into local table
truncate table dbo.ky_listServicesEligible;

insert into dbo.ky_listServicesEligible (memberid_, Address1_, Address2_, Address3_, areaofpractice1_, 
	areaofpractice2_, areaofpractice3_, BarDate_, City_, Company_, CongressionalDistrict_, ContactPosition_, County_, 
	District_, Fax_, Firstname_, Gender_, HD, JoinDate_, LastName_, Legislative_, MemberLevel_, MemberStatus_, MiddleName_, 
	nickname_, numeric1_, numeric2_, numeric3_, PostalCode_, prefix_, ProfSuffix_, renewLink_, SD, StateProvince_, Suffix_, 
	Text1_, Text3_, Text4_, Text5_, Website_, WorkPhone_)
select memberid_, Address1_, Address2_, Address3_, areaofpractice1_, 
	areaofpractice2_, areaofpractice3_, BarDate_, City_, Company_, CongressionalDistrict_, ContactPosition_, County_, 
	District_, Fax_, Firstname_, Gender_, HD, JoinDate_, LastName_, Legislative_, MemberLevel_, MemberStatus_, MiddleName_, 
	nickname_, numeric1_, numeric2_, numeric3_, PostalCode_, prefix_, ProfSuffix_, renewLink_, SD, StateProvince_, Suffix_, 
	Text1_, Text3_, Text4_, Text5_, Website_, WorkPhone_
from MEMBERCENTRAL.datatransfer.dbo.ky_listServicesEligibleFinal;


-- update lyris member data
update m
set m.Address1_ = tmp.Address1_,
	m.Address2_ = tmp.Address2_,
	m.Address3_ = tmp.Address3_,
	m.areaofpractice1_ = tmp.areaofpractice1_,
	m.areaofpractice2_ = tmp.areaofpractice2_,
	m.areaofpractice3_ = tmp.areaofpractice3_,
	m.BarDate_ = tmp.BarDate_,
	m.City_ = tmp.City_,
	m.Company_ = tmp.Company_,
	m.CongressionalDistrict_ = tmp.CongressionalDistrict_,
	m.Contactposition_ = tmp.Contactposition_,
	m.County_ = tmp.County_,
	m.District_ = tmp.district_,
	m.Fax_ = tmp.Fax_,
	m.Firstname_ = tmp.Firstname_,
	m.Gender_ = tmp.Gender_,
	m.HD = tmp.HD,
	m.JoinDate_ = tmp.JoinDate_,
	m.LastName_ = tmp.LastName_,
	m.Legislative_ = tmp.Legislative_,
	m.MemberLevel_ = tmp.MemberLevel_,
	m.MemberStatus_ = tmp.MemberStatus_,
	m.MiddleName_ = tmp.MiddleName_,
	m.nickname_ = tmp.nickname_,
	m.numeric1_ = tmp.numeric1_,
	m.numeric2_ = tmp.numeric2_,
	m.numeric3_ = tmp.numeric3_,
	m.PostalCode_ = tmp.PostalCode_,
	m.prefix_ = tmp.prefix_,
	m.ProfSuffix_ = tmp.ProfSuffix_,
	m.RenewLink_ = tmp.RenewLink_,
	m.SD = tmp.SD,
	m.StateProvince_ = tmp.StateProvince_,
	m.Suffix_ = tmp.Suffix_,
	m.Text1_ = tmp.Text1_,
	m.Text3_ = tmp.Text3_,
	m.Text4_ = tmp.Text4_,
	m.Text5_ = tmp.Text5_,
	m.Website_ = tmp.Website_,
	m.WorkPhone_ = tmp.WorkPhone_
from trialslyris1.dbo.members_ as m
inner join dbo.ky_listServicesEligible as tmp on tmp.memberid_ = m.memberid_;


-- cleanup
truncate table dbo.ky_listServicesEligible;

IF OBJECT_ID('tempdb..#tmpKYLists') IS NOT NULL 
	DROP TABLE #tmpKYLists;
GO
