use membercentral
GO
CREATE FUNCTION dbo.fn_tr_getDeferredGLAccountID (
	@GLAccountID int
)
RETURNS int
AS
BEGIN
	DECLARE @DeferredGLAccountID int

	SELECT @DeferredGLAccountID = deferredGLAccountID
		FROM dbo.tr_glAccounts
		WHERE glAccountID = @GLAccountID

	RETURN @DeferredGLAccountID
END
GO

ALTER FUNCTION [dbo].[fn_tr_getTaxForSale] (	
	@saleTransactionID int,
	@stateIDForTax int -- null to use stateid from member record
)
RETURNS TABLE 
AS
RETURN (

	-- this uses the transaction's amount after adjustment amount to determine the tax amount
	WITH rules AS (
		select ta.orgID, tr.taxAuthorityID, isnull(tr.saleGLAccountID,0) as saleGLAccountID, tr.stateID, tr.taxRuleID, 0 as theLevel
		from dbo.tr_taxRules as tr
		inner join dbo.tr_taxAuthorities as ta on ta.taxAuthorityID = tr.taxAuthorityID and ta.status = 'A'
		inner join dbo.tr_transactions as t on t.transactionID = @saleTransactionID and t.ownedByOrgID = ta.orgID 
			and t.statusID = 1
			and t.typeID = 1
		inner join dbo.ams_members as m on m.memberid = t.assignedToMemberID
		inner join dbo.ams_members as m2 on m2.memberid = m.activeMemberID
		left outer join dbo.ams_memberAddresses as ma on ma.memberid = m2.memberid and ma.addressTypeID = m.billingAddressTypeID
		where t.transactionDate between tr.startDate and tr.endDate
		and tr.status = 'A'
		and coalesce(@stateIDForTax,ma.stateid,0) = tr.stateid
			union all
		select rules.orgID, rules.taxAuthorityID, gl.glAccountID, rules.stateID, rules.taxRuleID, rules.theLevel + 1 as theLevel
		from tr_glAccounts as gl
		inner join rules on rules.saleGLAccountID = isnull(gl.parentGLAccountID,0) and gl.orgID = rules.orgID
	)
	select ROW_NUMBER() OVER (ORDER BY ta.authorityName, tr.taxRate) AS row,
		ta.taxAuthorityID, ta.GLAccountID as taxGLAccountID, glT.deferredGLAccountID, ta.authorityName, r.taxRuleID, tr.taxRate, 
		cast(ts.cache_amountAfterAdjustment*tr.taxRate as numeric(9,2)) as taxAmount
	from (
		select taxAuthorityID, saleGLAccountID, stateID, min(theLevel) as minLevel
		from rules
		group by taxAuthorityID, saleGLAccountID, stateID
	) as grp
	inner join rules as r on r.taxAuthorityID = grp.taxAuthorityID
		and r.saleGLAccountID = grp.saleGLAccountID
		and r.stateID = grp.stateID
		and r.theLevel = grp.minLevel
	inner join dbo.tr_taxAuthorities as ta on ta.taxAuthorityID = r.taxAuthorityID
	inner join dbo.tr_glAccounts as glT on glT.glaccountID = ta.GLAccountID
	inner join dbo.tr_taxRules as tr on tr.taxRuleID = r.taxRuleID
	inner join dbo.tr_transactions as t on t.transactionID = @saleTransactionID and t.creditGLAccountID = r.saleGLAccountID
	inner join dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID
	inner join dbo.tr_glAccounts as gl on gl.glaccountID = r.saleGLAccountID
		and gl.isTaxable = 1

)
GO

CREATE FUNCTION dbo.fn_tr_prepareDeferredScheduleTable (
	@xmlSchedule xml,
	@amount decimal(10,2),
	@transactionDate datetime
)
RETURNS @tblDeferredSchedule TABLE (
	rowID int IDENTITY(1,1), 
	amt decimal(10,2), 
	dt datetime, 
	pct decimal(10,2)
)
AS
BEGIN
	-- put xml into table
	insert into @tblDeferredSchedule (amt, dt)
	select R.value('@amt', 'decimal(10,2)'), R.value('@dt', 'datetime') 
	from @xmlSchedule.nodes('//row') as T(R)
	order by 2

	-- if nothing entered, xmlSchedule was null or had no rows. put entire amount into one schedule row.
	IF @@ROWCOUNT = 0
		insert into @tblDeferredSchedule (amt, dt)
		values (@amount, DATEADD(dd,DATEDIFF(dd,0,@transactionDate),0))

	-- update with percentages
	IF @amount > 0	
		update @tblDeferredSchedule set pct = amt / @amount
	ELSE
		update @tblDeferredSchedule set pct = 0
	
	RETURN 
END
GO

ALTER PROC [dbo].[tr_createTransaction_salesTax]
@ownedByOrgID int,
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@status varchar(20),
@detail varchar(max),
@amount money,
@transactionDate datetime,
@creditGLAccountID int,
@saleTransactionID int,
@invoiceID int,
@xmlSchedule xml = null, 
@transactionID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- init output param
	select @transactionID = 0

	-- ensure @saleTransactionID is a sale
	IF EXISTS (select transactionID from dbo.tr_transactions where transactionID = @saleTransactionID and typeID <> 1)
		RAISERROR('saleTransactionID is not a sale', 16, 1);

	-- get assignedToMemberID, stateIDforTax from sale transaction
	declare @assignedToMemberID int, @stateIDForTax int
	select @assignedToMemberID = t.assignedToMemberID, @stateIDForTax = ts.stateIDForTax
		from dbo.tr_transactions as t
		inner join dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID
		where t.transactionID = @saleTransactionID

	-- dont assume memberid is the active one. get the active one.
	select @assignedToMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @assignedToMemberID 
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	-- get AR account. this is the debit account.
	declare @debitGLAccountID int
	select @debitGLAccountID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and isSystemAccount = 1
		and GLCode = 'ACCOUNTSRECEIVABLE'
		and [status] = 'A'

	-- ensure amount is abs
	select @amount = cast(abs(@amount) as decimal(10,2))

	-- verify credit account accepts new transactions
	-- ensure we have active debit/credit accts
	IF @debitGLAccountID is null or NOT EXISTS (
		select glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and glaccountID = @creditGLAccountID
		and [status] = 'A')
		RAISERROR('credit account does not accept new transactions', 16, 1);

	-- if invoiceID is null or not an open invoice, reject tax.
	IF @invoiceID is null OR NOT EXISTS (
		select invoiceID
		from dbo.tr_invoices
		where invoiceID = @invoiceID
		and statusID = 1)
		RAISERROR('invoiceID not an open invoice', 16, 1);

	-- insert into transactions
	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
		amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
		typeID, accrualDate, debitGLAccountID, creditGLAccountID)
	VALUES (@ownedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID(@status), @detail, null, 
		@amount, getdate(), @transactionDate, @assignedToMemberID, @recordedByMemberID, @statsSessionID, 
		dbo.fn_tr_getTypeID('Sales Tax'), @transactionDate, @debitGLAccountID, @creditGLAccountID)
		select @transactionID = SCOPE_IDENTITY()

	-- insert into transactionSales
	INSERT INTO dbo.tr_transactionSales (transactionID, cache_amountAfterAdjustment, 
		cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, stateIDForTax)
	VALUES (@transactionID, @amount, 0, 0, @stateIDForTax)

	-- insert into relationships
	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
	VALUES (dbo.fn_tr_getRelationshipTypeID('SalesTaxTrans'), @transactionID, @saleTransactionID)

	-- put on invoice
	INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount)
	VALUES (@transactionID, @invoiceID, @amount, 0, 0)


	-- record deferred schedule if necessary
	declare @DeferredGLAccountID int
	select @DeferredGLAccountID = dbo.fn_tr_getDeferredGLAccountID(@creditGLAccountID)
	IF @DeferredGLAccountID is not null BEGIN
		-- prepare deferred schedule
		declare @tblDeferredSchedule TABLE (rowID int, amt decimal(10,2), dt datetime, pct decimal(10,2))		
		insert into @tblDeferredSchedule
		select rowID, amt, dt, pct
		from dbo.fn_tr_prepareDeferredScheduleTable(@xmlSchedule, @amount, @transactionDate)

		declare @minSchedRow int, @rowAmt decimal(10,2), @rowDt datetime, @ditTransactionID int, @ditTransactionID2 int
		select @minSchedRow = min(rowID) from @tblDeferredSchedule
		WHILE @minSchedRow is not null BEGIN
			select @rowAmt=amt, @rowDt=dt 
				from @tblDeferredSchedule 
				where rowID = @minSchedRow

			-- put all scheduled rows here for auditing, even if they are immediately recognized
			EXEC dbo.tr_createTransaction_dit @recordedOnSiteID=@recordedOnSiteID, 
				@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
				@amount=@rowAmt, @transactionDate=@transactionDate, @recognitionDate=@rowDt, 
				@debitGLAccountID=@creditGLAccountID, @creditGLAccountID=@DeferredGLAccountID, 
				@saleTransactionID=@transactionID, @DITTransactionID=null, 
				@transactionID=@ditTransactionID OUTPUT

			-- if recognition date is today or in past, then move it out immediately back to revenue
			IF @rowDt < getdate() BEGIN
				set @rowAmt = @rowAmt * -1
				EXEC dbo.tr_createTransaction_dit @recordedOnSiteID=@recordedOnSiteID, 
					@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
					@amount=@rowAmt, @transactionDate=@transactionDate, @recognitionDate=@rowDt, 
					@debitGLAccountID=@DeferredGLAccountID, @creditGLAccountID=@creditGLAccountID, 
					@saleTransactionID=@transactionID, @DITTransactionID=@ditTransactionID, 
					@transactionID=@ditTransactionID2 OUTPUT
			END

			select @minSchedRow = min(rowID) from @tblDeferredSchedule where rowID > @minSchedRow
		END
	END


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @transactionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO


ALTER PROC [dbo].[tr_createTransaction_sale]
@ownedByOrgID int,
@recordedOnSiteID int,
@assignedToMemberID int,
@recordedByMemberID int,
@statsSessionID int,
@status varchar(20),
@detail varchar(max),
@parentTransactionID int,
@amount money,
@transactionDate datetime,
@accrualDate datetime,
@creditGLAccountID int,
@invoiceID int,
@stateIDForTax int = null, -- null to use stateid from member record
@bypassTax bit = 0,
@xmlSchedule xml = null, 
@transactionID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- init output param
	select @transactionID = 0

	-- dont assume memberid is the active one. get the active one.
	select @assignedToMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @assignedToMemberID 
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	-- get AR account. this is the debit account.
	declare @debitGLAccountID int
	select @debitGLAccountID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and isSystemAccount = 1
		and GLCode = 'ACCOUNTSRECEIVABLE'
		and [status] = 'A'

	-- ensure amount is abs
	select @amount = cast(abs(@amount) as decimal(10,2))

	declare @statusID int
	select @statusID = dbo.fn_tr_getStatusID(@status)

	-- verify credit account accepts new transactions
	-- ensure we have active debit/credit accts
	IF @debitGLAccountID is null or NOT EXISTS (
		select glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and glaccountID = @creditGLAccountID
		and [status] = 'A')
		RAISERROR('credit account does not accept new transactions', 16, 1);

	-- if invoiceID is null, not an open invoice, or inv profile doesnt match revenue GL, reject sale.
	IF @invoiceID is null 
		OR NOT EXISTS (select invoiceID from dbo.tr_invoices where invoiceID = @invoiceID and statusID = 1)
		OR ((select dbo.fn_tr_doesInvoiceProfileSupportRevenueGL(@invoiceID,@creditGLAccountID)) = 0)
		RAISERROR('invoiceid not eligible', 16, 1);

	-- insert into transactions
	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
		amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
		typeID, accrualDate, debitGLAccountID, creditGLAccountID)
	VALUES (@ownedByOrgID, @recordedOnSiteID, @statusID, @detail, @parentTransactionID, 
		@amount, getdate(), @transactionDate, @assignedToMemberID, @recordedByMemberID, @statsSessionID, 
		dbo.fn_tr_getTypeID('Sale'), @accrualDate, @debitGLAccountID, @creditGLAccountID)
		select @transactionID = SCOPE_IDENTITY()

	-- if stateIDforTax is null, get state on member record to record in the transactionSales table
	if nullif(@stateIDForTax,0) is null BEGIN
		select @stateIDForTax = ma.stateID
		from dbo.ams_memberAddresses as ma 
		inner join dbo.ams_members as m on m.memberID = ma.memberID
		where ma.memberid = @assignedToMemberID
		and ma.addressTypeID = m.billingAddressTypeID
	end

	-- insert into transactionSales
	INSERT INTO dbo.tr_transactionSales (transactionID, cache_amountAfterAdjustment, 
		cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, stateIDForTax)
	VALUES (@transactionID, @amount, 0, 0, nullif(@stateIDForTax,0))

	-- put sale on invoice
	declare @contentVersionID int
	SELECT @contentVersionID = max(cv.contentVersionID)
		FROM dbo.tr_glAccounts as gl
		INNER JOIN dbo.cms_content as c on c.contentID = gl.invoiceContentID
		INNER JOIN dbo.cms_siteResources sr	on sr.siteResourceID = c.siteResourceID 
		INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
		INNER JOIN dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID AND cl.languageID = 1
		INNER JOIN dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID
		WHERE gl.GLAccountID = @creditGLAccountID
		AND cv.isActive = 1
		AND len(cv.rawContent) > 0
	INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, messageContentVersionID)
	VALUES (@transactionID, @invoiceID, @amount, 0, 0, @contentVersionID)


	-- determine sales tax. Just get information - we'll record it after deferred is recorded.
	-- we need to do this before the deferred entries are written because the deferred entries adjust the tr_transactionSales amount used by tax determination.
	declare @TaxNeeded bit
	set @TaxNeeded = 0
	IF @amount > 0 AND @bypassTax = 0 and EXISTS (select glAccountID from dbo.tr_glAccounts where glAccountID = @creditGLAccountID and isTaxable = 1) BEGIN
		DECLARE @tblTax TABLE (row int, taxAuthorityID int, taxGLAccountID int, taxDeferredGLAccountID int, authorityName varchar(200), taxRuleID int, taxRate float, taxAmount money) 
		insert into @tblTax	(row, taxAuthorityID, taxGLAccountID, taxDeferredGLAccountID, authorityName, taxRuleID, taxRate, taxAmount)
		select row, taxAuthorityID, taxGLAccountID, deferredGLAccountID, authorityName, taxRuleID, taxRate, taxAmount
		from dbo.fn_tr_getTaxForSale(@transactionID,@stateIDForTax)
		where taxAmount > 0

		set @TaxNeeded = 1
	END


	-- prepare deferred schedule
	declare @tblDeferredSchedule TABLE (rowID int, amt decimal(10,2), dt datetime, pct decimal(10,2))		
	insert into @tblDeferredSchedule
	select rowID, amt, dt, pct
	from dbo.fn_tr_prepareDeferredScheduleTable(@xmlSchedule, @amount, @transactionDate)

	-- record deferred schedule for sale if necessary
	declare @SaleDeferredGLAccountID int
	select @SaleDeferredGLAccountID = dbo.fn_tr_getDeferredGLAccountID(@creditGLAccountID)
	IF @SaleDeferredGLAccountID is not null BEGIN
		declare @minSchedRow int, @rowAmt decimal(10,2), @rowDt datetime, @ditTransactionID int, @ditTransactionID2 int
		select @minSchedRow = min(rowID) from @tblDeferredSchedule
		WHILE @minSchedRow is not null BEGIN
			select @rowAmt=amt, @rowDt=dt 
				from @tblDeferredSchedule 
				where rowID = @minSchedRow

			-- put all scheduled rows here for auditing, even if they are immediately recognized
			EXEC dbo.tr_createTransaction_dit @recordedOnSiteID=@recordedOnSiteID, 
				@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
				@amount=@rowAmt, @transactionDate=@transactionDate, @recognitionDate=@rowDt, 
				@debitGLAccountID=@creditGLAccountID, @creditGLAccountID=@SaleDeferredGLAccountID, 
				@saleTransactionID=@transactionID, @DITTransactionID=null, 
				@transactionID=@ditTransactionID OUTPUT

			-- if recognition date is today or in past, then move it out immediately back to revenue
			IF @rowDt < getdate() BEGIN
				set @rowAmt = @rowAmt * -1
				EXEC dbo.tr_createTransaction_dit @recordedOnSiteID=@recordedOnSiteID, 
					@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
					@amount=@rowAmt, @transactionDate=@transactionDate, @recognitionDate=@rowDt, 
					@debitGLAccountID=@SaleDeferredGLAccountID, @creditGLAccountID=@creditGLAccountID, 
					@saleTransactionID=@transactionID, @DITTransactionID=@ditTransactionID, 
					@transactionID=@ditTransactionID2 OUTPUT
			END

			select @minSchedRow = min(rowID) from @tblDeferredSchedule where rowID > @minSchedRow
		END
	END
	

	-- record sales tax
	IF @TaxNeeded = 1 BEGIN
		DECLARE @minTaxrow int, @taxTransactionID int, @taxGLAID int, @taxDeferredGLAccountID int, @taxAmt decimal(10,2), @taxdetail varchar(max)
		DECLARE @taxSum decimal(10,2), @taxDiff decimal(10,2), @taxXMLSchedule xml
		select @minTaxrow = min(row) from @tblTax
		WHILE @minTaxrow is not null BEGIN
			set @taxXMLSchedule = null			
			select @taxAmt=taxAmount, @taxGLAID=taxGLAccountID, @taxDeferredGLAccountID = taxDeferredGLAccountID, 
				@taxdetail = 'Sales Tax: ' + authorityName + ' @ ' + cast(taxRate*100 as varchar(10)) + '%'
				from @tblTax 
				where row=@minTaxrow

			-- if deferring tax, calculate percentage of schedule and put remainder on first row
			IF @taxDeferredGLAccountID is not null BEGIN
				update @tblDeferredSchedule set amt = @taxAmt * pct
				select @taxSum = sum(amt) from @tblDeferredSchedule
				select @taxDiff = @taxAmt - @taxSum
				if @taxDiff <> 0
					update @tblDeferredSchedule set amt = amt + @taxDiff where rowID = 1

				SELECT @taxXMLSchedule = (
					select amt, convert(varchar(10),dt,101) as dt
					from @tblDeferredSchedule as row
					for XML AUTO, ROOT('rows'), TYPE
					)
			END

			EXEC dbo.tr_createTransaction_salesTax @ownedByOrgID=@ownedByOrgID, 
				@recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
				@statsSessionID=@statsSessionID, @status=@status, @detail=@taxdetail, 
				@amount=@taxAmt, @transactionDate=@transactionDate, @creditGLAccountID=@taxGLAID, 
				@saleTransactionID=@transactionID, @invoiceID=@invoiceID, @xmlSchedule=@taxXMLSchedule, 
				@transactionID=@taxTransactionID OUTPUT

			INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
			VALUES (dbo.fn_tr_getRelationshipTypeID('PITTaxTrans'), @taxTransactionID, @transactionID)

			select @minTaxrow = min(row) from @tblTax where row > @minTaxrow
		END
	END


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @transactionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_createTransaction_adjustment]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@status varchar(20),
@amount money,
@transactionDate datetime,
@saleTransactionID int,
@invoiceID int,
@transactionID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- init output param
	select @transactionID = 0

	declare @origSaleOwnedByOrgID int, @origSaleAssignedToMemberID int, @origCreditGLAccountID int, 
			@origSaleCreditGLAccountID int, @ARGLAccountID int, @AdjSaleTransactionID int, 
			@TaxForAdjustmentMinRow int, @TaxForAdjustmentGLAID int, @taxTransactionID int,
			@AdjTaxTransactionID int, @AllocPaymentTID int, @AllocSaleTID int, 
			@allocTID int, @SaleAllocTransactionID int, @invoiceProfileID int, @contentVersionID int
	declare @TaxForAdjustmentAmt money, @amtLeftToDeallocate money, @AllocAmt money, @DeallocateAmtNeg money
	declare @invoiceNumber varchar(18)
	declare @origSaleDetail varchar(max), @TaxForAdjustmentDetail varchar(max)
	declare @tblTaxForAdjustment TABLE (row int, taxAuthorityID int, taxGLAccountID int, authorityName varchar(200), taxRuleID int, taxRate float, taxAmount money) 

	-- no zero dollar adjustments
	if @amount = 0
		RAISERROR('amount is 0', 16, 1);

	-- ensure @saleTransactionID is an active sale
	IF EXISTS (select transactionID from dbo.tr_transactions where transactionID = @saleTransactionID and typeID <> 1 and statusID not in (1,3))
		RAISERROR('saleTransactionID is not an active sale', 16, 1);

	-- get data from sale transaction
	select @origSaleOwnedByOrgID=ownedByOrgID, @origSaleAssignedToMemberID=assignedToMemberID, 
		@origSaleDetail=detail, @origCreditGLAccountID=creditGLAccountID
		from dbo.tr_transactions
		where transactionID = @saleTransactionID
		and typeID = 1
		and statusID in (1,3)

	-- dont assume memberid is the active one. get the active one.
	select @origSaleAssignedToMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @origSaleAssignedToMemberID 
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	select @ARGLAccountID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @origSaleOwnedByOrgID
		and isSystemAccount = 1
		and GLCode = 'ACCOUNTSRECEIVABLE'
		and [status] = 'A'
	IF @ARGLAccountID is null
		RAISERROR('ARGLAccountID is null', 16, 1);

	-- if invoiceID is null, not an open/pending invoice, or inv profile doesnt match revenue GL, assume need to create a new one.
	IF @invoiceID is null 
		OR NOT EXISTS (select invoiceID from dbo.tr_invoices where invoiceID = @invoiceID and statusID in (1,2))
		OR ((select dbo.fn_tr_doesInvoiceProfileSupportRevenueGL(@invoiceID,@origCreditGLAccountID)) = 0)
	BEGIN
		select @invoiceProfileID=invoiceProfileID from dbo.tr_GLAccounts where GLAccountID = @origCreditGLAccountID
			IF @invoiceProfileID is null RAISERROR('invoiceProfileID is null', 16, 1);

		EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID,
			@enteredByMemberID=@recordedByMemberID, 
			@assignedToMemberID=@origSaleAssignedToMemberID,
			@dateBilled=@transactionDate, @dateDue=@transactionDate, 
			@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT
	END
	
	-- adjustments up	
	if @amount > 0 BEGIN

		-- credit account of adj is credit account of sale
		select @origSaleCreditGLAccountID = gl.glAccountID
			from dbo.tr_transactions as t
			inner join dbo.tr_GLAccounts as gl on gl.glAccountID = t.creditGLAccountID
				and t.transactionID = @saleTransactionID
				and gl.status = 'A'
		if @origSaleCreditGLAccountID is null
			RAISERROR('origSaleCreditGLAccountID is null', 16, 1);

		-- insert adj into transactions
		-- ensure amount is abs
		INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
			amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
			typeID, accrualDate, debitGLAccountID, creditGLAccountID)
		VALUES (@origSaleOwnedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID(@status), @origSaleDetail, null, 
			@amount, getdate(), @transactionDate, @origSaleAssignedToMemberID, @recordedByMemberID, @statsSessionID, 
			dbo.fn_tr_getTypeID('Adjustment'), @transactionDate, @ARGLAccountID, @origSaleCreditGLAccountID)
			select @AdjSaleTransactionID = SCOPE_IDENTITY()

		-- insert adj into relationships
		INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
		VALUES (dbo.fn_tr_getRelationshipTypeID('AdjustTrans'), @AdjSaleTransactionID, @saleTransactionID)

		-- put adj on invoice
		SELECT @contentVersionID = null
		SELECT @contentVersionID = max(cv.contentVersionID)
			FROM dbo.tr_glAccounts as gl
			INNER JOIN dbo.cms_content as c on c.contentID = gl.invoiceContentID
			INNER JOIN dbo.cms_siteResources sr	on sr.siteResourceID = c.siteResourceID 
			INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
			INNER JOIN dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID AND cl.languageID = 1
			INNER JOIN dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID
			WHERE gl.GLAccountID = @origSaleCreditGLAccountID
			AND cv.isActive = 1
			AND len(cv.rawContent) > 0
		INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, messageContentVersionID)
		VALUES (@AdjSaleTransactionID, @invoiceID, @amount, 0, 0, @contentVersionID)

		-- update cache
		UPDATE dbo.tr_transactionSales
		SET cache_amountAfterAdjustment = cache_amountAfterAdjustment + @amount
		WHERE transactionID = @saleTransactionID

		-- determine sales tax based on adjustment date and @amount
		IF EXISTS (select glAccountID from dbo.tr_glAccounts where glAccountID = @origSaleCreditGLAccountID and isTaxable = 1) BEGIN

			-- determine the tax on the adjusted amount
			insert into @tblTaxForAdjustment (row, taxAuthorityID, taxGLAccountID, authorityName, taxRuleID, taxRate, taxAmount)
			select row, taxAuthorityID, taxGLAccountID, authorityName, taxRuleID, taxRate, taxAmount
			from dbo.fn_tr_getTaxForAdjustment(@AdjSaleTransactionID)
			where taxAmount > 0

			-- loop over taxes
			select @TaxForAdjustmentMinRow = min(row) from @tblTaxForAdjustment
			while @TaxForAdjustmentMinRow is not null BEGIN
				select @TaxForAdjustmentAmt=taxAmount, @TaxForAdjustmentGLAID=taxGLAccountID, 
					@TaxForAdjustmentDetail = 'Sales Tax: ' + authorityName + ' @ ' + cast(taxRate*100 as varchar(10)) + '%'
				from @tblTaxForAdjustment 
				where row=@TaxForAdjustmentMinRow

				-- is there already a tax transaction using this acct code for this sale? if so, adjust it
				SELECT @taxTransactionID = null
				SELECT @taxTransactionID = tTax.transactionID
					FROM dbo.tr_transactions as tTax
					INNER JOIN dbo.tr_relationships AS r ON r.transactionID = tTax.transactionID
						AND tTax.typeID = 7
						AND tTax.statusID in (1,3)
						AND r.appliedToTransactionID = @saleTransactionID
						AND tTax.creditGLAccountID = @TaxForAdjustmentGLAID
					INNER JOIN dbo.tr_relationshipTypes AS rt ON rt.typeID = r.typeID AND rt.type = 'SalesTaxTrans'
				IF @taxTransactionID is not null BEGIN

					-- insert adj into transactions
					INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
						amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
						typeID, accrualDate, debitGLAccountID, creditGLAccountID)
					VALUES (@origSaleOwnedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID(@status), @TaxForAdjustmentDetail, null, 
						@TaxForAdjustmentAmt, getdate(), @transactionDate, @origSaleAssignedToMemberID, @recordedByMemberID, @statsSessionID, 
						dbo.fn_tr_getTypeID('Adjustment'), @transactionDate, @ARGLAccountID, @TaxForAdjustmentGLAID)
						select @AdjTaxTransactionID = SCOPE_IDENTITY()

					-- insert adj into relationships
					-- tie tax to the sale adjustment
					INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
					VALUES (dbo.fn_tr_getRelationshipTypeID('AdjustTrans'), @AdjTaxTransactionID, @taxTransactionID)
					
					INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
					VALUES (dbo.fn_tr_getRelationshipTypeID('PITTaxTrans'), @AdjTaxTransactionID, @AdjSaleTransactionID)

					-- put adj on invoice
					INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount)
					VALUES (@AdjTaxTransactionID, @invoiceID, @TaxForAdjustmentAmt, 0, 0)

					-- update cache
					UPDATE dbo.tr_transactionSales
					SET cache_amountAfterAdjustment = cache_amountAfterAdjustment + @TaxForAdjustmentAmt
					WHERE transactionID = @taxTransactionID

				END 

				-- else, create a new sales tax transaction.
				ELSE BEGIN

					EXEC dbo.tr_createTransaction_salesTax @ownedByOrgID=@origSaleOwnedByOrgID, 
						@recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
						@statsSessionID=@statsSessionID, @status=@status, @detail=@TaxForAdjustmentDetail, 
						@amount=@TaxForAdjustmentAmt, @transactionDate=@transactionDate, 
						@creditGLAccountID=@TaxForAdjustmentGLAID, @saleTransactionID=@saleTransactionID, 
						@invoiceID=@invoiceID, @xmlSchedule=null, @transactionID=@taxTransactionID OUTPUT

					-- tie tax to the sale adjustment
					INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
					VALUES (dbo.fn_tr_getRelationshipTypeID('PITTaxTrans'), @taxTransactionID, @AdjSaleTransactionID)

				END

				select @TaxForAdjustmentMinRow = min(row) from @tblTaxForAdjustment where row > @TaxForAdjustmentMinRow
			END

		END

	END 

	-- adjustments down
	ELSE BEGIN

		declare @amtLeftToAdjust money, @SaleAndAdjustmentsAmt money, @amountToAdjust money,
				@AdjToMakeAmt money, @AdjToMakeUnallocatedAmt money, @AllocationsAmt money,
				@amountToAllocate money, @a_allocAmount money, @a_subtract_allocAmount money,
				@it_amtLeftToAdj money, @it_MaxAmountToTake money, @it_amtToAdj money,
				@amtDueNoPendingOnInvoice money
		declare @SaleAndAdjustmentsMinAutoID int, @SaleAndAdjustmentsTID int, @SaleAndAdjustmentsTypeID int,
				@AdjToMakeAutoID int, @AdjToMakeTID int, @AdjToMakeGLAID int, @newAllocationTID int,
				@a_autoid int, @a_appliedToTID int, @a_paymentTID int, @a_subtract_autoid int, @minITID int,
				@it_invoiceID int
		declare @AdjToMakeIsSale bit
		declare @it_invstatus varchar(50)
		declare @AdjToMakeDetail varchar(max)
		declare @tblSaleAndAdjustments TABLE (autoid int IDENTITY(1,1) PRIMARY KEY, transactionID int, typeID int, amount money)
		declare @tblAdjToMake TABLE (autoid int IDENTITY(1,1), isSale bit, saleTransactionID int, debitGLAID int, detail varchar(max), amountToAdjust numeric(9,2))
		declare @tblAdjToMakeFinal TABLE (autoid int IDENTITY(1,1), isSale bit, saleTransactionID int, debitGLAID int, detail varchar(max), amountToAdjust numeric(9,2))
		declare @tblAllocations TABLE (autoid int IDENTITY(1,1) PRIMARY KEY, appliedToTID int, paymentTID int, allocAmount money, origAllocationTID int, origRelTID int)
		declare @tblAllocationsToMake TABLE (autoid int IDENTITY(1,1) PRIMARY KEY, origAllocationTID int, origRelTID int, newAllocationTID int, newRelTID int)

		-- get all active adjustments to sale in reverse order (daterecorded).
		-- consider the non-written off amounts only and remove any that are 0
		insert into @tblSaleAndAdjustments (transactionID, typeID, amount)
		select tAdj.transactionID, tAdj.typeID, 
			case 
			when gl.isSystemAccount = 1 and gl.GLCode = 'ACCOUNTSRECEIVABLE' then tAdj.amount
			else tAdj.amount * -1
			end - wo.writeOffAmount as maxAmountForNegativeAdjustment 
		from dbo.tr_transactions as tAdj
		inner join dbo.tr_relationships as tr on tr.transactionID = tAdj.transactionID
			and tr.appliedToTransactionID = @saleTransactionID
		inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'AdjustTrans'
		inner join dbo.tr_glAccounts as gl on gl.glAccountID = tAdj.debitGLAccountID
		cross apply dbo.fn_tr_getWriteOffAmountofSaleOrAdj(tAdj.transactionID) as wo
		where tAdj.statusID = 1
		and tAdj.typeID = 3
		and case 
			when gl.isSystemAccount = 1 and gl.GLCode = 'ACCOUNTSRECEIVABLE' then tAdj.amount
			else tAdj.amount * -1
			end - wo.writeOffAmount <> 0
		order by tAdj.daterecorded desc, tAdj.transactionID desc
		
		-- add in the original sale as last entry
		insert into @tblSaleAndAdjustments (transactionID, typeID, amount)
		select tSale.transactionID, tSale.typeID, tSale.amount - wo.writeOffAmount as maxAmountForNegativeAdjustment 
		from dbo.tr_transactions as tSale
		cross apply dbo.fn_tr_getWriteOffAmountofSaleOrAdj(tSale.transactionID) as wo
		where tSale.transactionID = @saleTransactionID
		and tSale.amount - wo.writeOffAmount <> 0

		-- loop over @tblSaleAndAdjustments until we get adjustment amount. grab PIT tax as well.		
		select @amtLeftToAdjust = abs(@amount)
		select @SaleAndAdjustmentsMinAutoID = min(autoid) from @tblSaleAndAdjustments
		while @SaleAndAdjustmentsMinAutoID is not null BEGIN
			select @SaleAndAdjustmentsTID=transactionID, @SaleAndAdjustmentsTypeID=typeID, 
				@SaleAndAdjustmentsAmt=amount
			from @tblSaleAndAdjustments 
			where autoID = @SaleAndAdjustmentsMinAutoID

			-- if amt left can be adjusted in full from this adjustment, take full amt. else take what we can.
			if @SaleAndAdjustmentsAmt < @amtLeftToAdjust
				select @amountToAdjust = @SaleAndAdjustmentsAmt
			ELSE
				select @amountToAdjust = @amtLeftToAdjust

			-- grab orig credit gl. adjustments will use this same gl.
			select @origSaleCreditGLAccountID = gl.glAccountID
				from dbo.tr_transactions as t
				inner join dbo.tr_GLAccounts as gl on gl.glAccountID = t.creditGLAccountID
					and t.transactionID = @saleTransactionID
					and gl.status = 'A'
			if @origSaleCreditGLAccountID is null
				RAISERROR('origSaleCreditGLAccountID is null', 16, 1);

			-- add to adj to make. adj debit acct is orig credit acct
			insert into @tblAdjToMake (saleTransactionID, isSale, debitGLAID, detail, amountToAdjust)
			values (@saleTransactionID, 1, @origSaleCreditGLAccountID, @origSaleDetail, @amountToAdjust*-1)
				
			-- and all its PIT taxes (adj to sales tax and sales tax). adj debit acct is orig credit acct
			-- 1. adjust to sales tax tied to adjust to sale (could be pos or neg so find out based on AR)
			-- 2+3. sales tax tied to sale or adjust to sale (only happens on positive adjustments so just take adj amount)
			insert into @tblAdjToMake (saleTransactionID, isSale, debitGLAID, detail, amountToAdjust)
			SELECT tTax.transactionID, 0, tTax.creditGLAccountID, tTax.detail, 
				case 
				when gl.isSystemAccount = 1 and gl.GLCode = 'ACCOUNTSRECEIVABLE' then ((((tAdj.amount-wo.writeOffAmount)/@SaleAndAdjustmentsAmt)*@amountToAdjust)*-1)
				else (((tAdj.amount-wo.writeOffAmount)/@SaleAndAdjustmentsAmt)*@amountToAdjust)
				end as amountToAdjust
			FROM dbo.tr_transactions AS tAdj 
			inner join dbo.tr_glAccounts as gl on gl.glAccountID = tAdj.debitGLAccountID
			INNER JOIN dbo.tr_relationships AS tr ON tAdj.transactionID = tr.transactionID 
			INNER JOIN dbo.tr_relationshipTypes AS trt ON tr.typeID = trt.typeID 
			INNER JOIN dbo.tr_relationships AS tr2 ON tAdj.transactionID = tr2.transactionID 
			INNER JOIN dbo.tr_relationshipTypes AS trt2 ON tr2.typeID = trt2.typeID 
			INNER JOIN dbo.tr_transactions AS tTax ON tr2.appliedToTransactionID = tTax.transactionID
			cross apply dbo.fn_tr_getWriteOffAmountofSaleOrAdj(tAdj.transactionID) as wo
			WHERE tr.appliedToTransactionID = @SaleAndAdjustmentsTID
			AND trt.type = 'PITTaxTrans'
			AND @SaleAndAdjustmentsTypeID = 3
			AND tAdj.typeID = 3
			AND tAdj.statusID IN (1,3)
			AND trt2.type = 'AdjustTrans'
			AND tTax.typeID = 7
			AND tTax.statusID IN (1,3)
				union all
			SELECT tTax.transactionID, 0, tTax.creditGLAccountID, tTax.detail, ((((tTax.amount-wo.writeOffAmount)/@SaleAndAdjustmentsAmt)*@amountToAdjust)*-1) as amountToAdjust
			FROM dbo.tr_transactions AS tTax 
			INNER JOIN dbo.tr_relationships AS tr ON tTax.transactionID = tr.transactionID 
			INNER JOIN dbo.tr_relationshipTypes AS trt ON tr.typeID = trt.typeID
			cross apply dbo.fn_tr_getWriteOffAmountofSaleOrAdj(tTax.transactionID) as wo
			WHERE tr.appliedToTransactionID = @SaleAndAdjustmentsTID
			AND tTax.typeID = 7 
			AND tTax.statusID IN (1,3)
			AND trt.type = 'PITTaxTrans'
			AND @SaleAndAdjustmentsTypeID in (1,3)

			select @amtLeftToAdjust = @amtLeftToAdjust - @amountToAdjust
			IF @amtLeftToAdjust <= 0
				BREAK

			select @SaleAndAdjustmentsMinAutoID = min(autoid) from @tblSaleAndAdjustments where autoid > @SaleAndAdjustmentsMinAutoID
		END

		-- sum and group transactions by saleTID. these are the adj transactions to record
		insert into @tblAdjToMakeFinal (saleTransactionID, isSale, debitGLAID, detail, amountToAdjust)
		select saleTransactionID, isSale, debitGLAID, detail, sum(amountToAdjust)
		from @tblAdjToMake
		group by saleTransactionID, isSale, debitGLAID, detail
		having sum(amountToAdjust) <> 0
		order by min(autoid)

		-- loop over the final adjustments to make. 
		-- handle necessary deallocations, record adj, and adjust cache amounts
		select @AdjToMakeAutoID = min(autoid) from @tblAdjToMakeFinal
		while @AdjToMakeAutoID is not null BEGIN
			select	@AdjToMakeTID=saleTransactionID, @AdjToMakeAmt=amountToAdjust, 
					@AdjToMakeIsSale=issale, @AdjToMakeGLAID=debitGLAID, @AdjToMakeDetail=detail
			from @tblAdjToMakeFinal 
			where autoid = @AdjToMakeAutoID

			-- get "how much has not been paid/allocated" of sale before adjustment
			select @AdjToMakeUnallocatedAmt = cache_amountAfterAdjustment - cache_activePaymentAllocatedAmount - cache_pendingPaymentAllocatedAmount
				from dbo.tr_transactionSales
				where transactionID = @AdjToMakeTID
			
			-- only need to deallocate if adj amount is gt than @AdjToMakeUnallocatedAmt
			if abs(@AdjToMakeAmt) > @AdjToMakeUnallocatedAmt BEGIN

				-- cleanup temp vars from any previous loops
				delete from @tblAllocations
				
				-- get all active allocations to sale/tax and all adjustments in reverse order.
				-- get the original allocation TID AllocTaxTrans relationship TID
				insert into @tblAllocations (appliedToTID, paymentTID, allocAmount, origAllocationTID, origRelTID)
				select tSaleAdj.transactionID, tPay.transactionID,
					case when glAllocDeb.GLCode = 'ACCOUNTSRECEIVABLE' and glAllocDeb.AccountTypeID = 2 then tAlloc.amount*-1 else tAlloc.amount end,
					tAlloc.transactionID, tATT.transactionID
				from 
					(
					select transactionID from dbo.tr_transactions where transactionID = @AdjToMakeTID
					union
					select adjInner.transactionID
					from dbo.tr_transactions as adjInner
					inner join dbo.tr_relationships as rInner on rInner.transactionID = adjInner.transactionID and rInner.appliedToTransactionID = @AdjToMakeTID
					inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
					where adjInner.statusID in (1,3)
					) as tSaleAdj
				inner join dbo.tr_relationships as rS on rS.appliedToTransactionID = tSaleAdj.transactionID
				inner join dbo.tr_relationshipTypes as rtS on rtS.typeID = rS.typeID and rtS.type = 'AllocSaleTrans'
				inner join dbo.tr_transactions as tAlloc on tAlloc.transactionID = rS.transactionID and tAlloc.statusID in (1,3)
				inner join dbo.tr_GLAccounts as glAllocDeb on glAllocDeb.GLAccountID = tAlloc.debitGLAccountID
				inner join dbo.tr_relationships as rP on rP.transactionID = tAlloc.transactionID
				inner join dbo.tr_relationshipTypes as rtP on rtP.typeID = rP.typeID and rtP.type = 'AllocPayTrans'
				inner join dbo.tr_transactions as tPay on tPay.transactionID = rP.appliedToTransactionID and tPay.statusID in (1,3)
				left outer join dbo.tr_relationships as rATT
					inner join dbo.tr_relationshipTypes as rtATT on rtATT.typeID = rATT.typeID and rtATT.type = 'AllocTaxTrans'
					inner join dbo.tr_transactions as tATT on tATT.transactionID = rATT.appliedToTransactionID and tATT.statusID in (1,3)
					on rATT.transactionID = tAlloc.transactionID
				ORDER BY tAlloc.transactionDate desc, tAlloc.transactionID desc

				-- for each negative allocation in tbl, subtract from closest positive allocation. delete negative allocation.
				IF EXISTS (select autoid from @tblAllocations where allocAmount < 0) BEGIN
				
					select @a_autoid = null
					select @a_autoid = min(autoid) from @tblAllocations where allocAmount < 0
					while @a_autoid is not null BEGIN
						select @a_allocAmount=allocAmount, @a_appliedToTID=appliedToTID, @a_paymentTID=paymentTID
						from @tblAllocations 
						where autoID = @a_autoid

						while @a_allocAmount < 0 BEGIN
							select @a_subtract_autoid = null, @a_subtract_allocAmount = null

							select top 1 @a_subtract_autoid=autoid, @a_subtract_allocAmount=allocAmount
							from @tblAllocations
							where allocAmount > 0
							and appliedToTID = @a_appliedToTID
							and paymentTID = @a_paymentTID
							order by autoid desc

							if @a_subtract_allocAmount < abs(@a_allocAmount) begin
								-- subtract amount from pos allocation (which should bring it to 0)
								update @tblAllocations
								set allocAmount = allocAmount - @a_subtract_allocAmount
								where autoID = @a_subtract_autoid

								-- add to a_allocAmount since it is negative to bring it closer to 0
								select @a_allocAmount = @a_allocAmount + @a_subtract_allocAmount
							end ELSE begin
								-- subtract remaining amount from pos allocation
								update @tblAllocations
								set allocAmount = allocAmount - abs(@a_allocAmount)
								where autoID = @a_subtract_autoid

								-- remove neg allocation since it is now all accounted for
								delete from @tblAllocations
								where autoID = @a_autoid

								-- to get out of loop
								select @a_allocAmount = 0
							end
						end

						select @a_autoid = min(autoid) from @tblAllocations where allocAmount < 0 and autoID > @a_autoid
					end

				END

				-- loop over @tblAllocations until we get deallocation amount. deallocate.
				select @amtLeftToDeallocate = abs(@AdjToMakeAmt)-@AdjToMakeUnallocatedAmt
				select @a_autoid = null
				select @a_autoid = min(autoid) from @tblAllocations
				while @a_autoid is not null BEGIN
					select @a_allocAmount=allocAmount, @a_appliedToTID=appliedToTID, @a_paymentTID=paymentTID
					from @tblAllocations 
					where autoID = @a_autoid

					-- if amt left can be deallocated in full from this allocation, take full amt. else take what we can.
					if @a_allocAmount < @amtLeftToDeallocate
						select @amountToAllocate = @a_allocAmount
					ELSE
						select @amountToAllocate = @amtLeftToDeallocate

					-- if amt > 0 then deallocate that amount.
					select @newAllocationTID = null
					IF @amountToAllocate > 0 BEGIN
						select @DeallocateAmtNeg = @amountToAllocate * -1

						EXEC dbo.tr_createTransaction_allocation @recordedOnSiteID=@recordedOnSiteID, 
							@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
							@status='Active', @amount=@DeallocateAmtNeg, @transactionDate=@transactionDate, 
							@paymentTransactionID=@a_paymentTID, @saleTransactionID=@a_appliedToTID, 
							@ovBatchID=null, @transactionID=@newAllocationTID OUTPUT
					END

					insert into @tblAllocationsToMake (origAllocationTID, origRelTID, newAllocationTID)
					select origAllocationTID, origRelTID, @newAllocationTID
					from @tblAllocations
					where autoID = @a_autoid

					select @amtLeftToDeallocate = @amtLeftToDeallocate - @amountToAllocate
					IF @amtLeftToDeallocate <= 0
						BREAK

					select @a_autoid = min(autoid) from @tblAllocations where autoid > @a_autoid
				end

			end			
	
			-- insert adj into transactions
			-- ensure amount is abs
			INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
				amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
				typeID, accrualDate, debitGLAccountID, creditGLAccountID)
			VALUES (@origSaleOwnedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID(@status), @AdjToMakeDetail, null, 
				abs(@AdjToMakeAmt), getdate(), @transactionDate, @origSaleAssignedToMemberID, @recordedByMemberID, @statsSessionID, 
				dbo.fn_tr_getTypeID('Adjustment'), @transactionDate, @AdjToMakeGLAID, @ARGLAccountID)
			IF @AdjToMakeIsSale = 1
				select @AdjSaleTransactionID = SCOPE_IDENTITY()
			ELSE
				select @AdjTaxTransactionID = SCOPE_IDENTITY()

			-- insert adj into relationships
			-- tie tax to the sale adjustment
			IF @AdjToMakeIsSale = 0	BEGIN
				INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
				VALUES (dbo.fn_tr_getRelationshipTypeID('AdjustTrans'), @AdjTaxTransactionID, @AdjToMakeTID)

				INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
				VALUES (dbo.fn_tr_getRelationshipTypeID('PITTaxTrans'), @AdjTaxTransactionID, @AdjSaleTransactionID)
			END
			ELSE BEGIN
				INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
				VALUES (dbo.fn_tr_getRelationshipTypeID('AdjustTrans'), @AdjSaleTransactionID, @AdjToMakeTID)
			END

			-- put adj on invoice (0 dollars.. no neg amounts here)
			IF @AdjToMakeIsSale = 1 BEGIN			
				SELECT @contentVersionID = null
				SELECT @contentVersionID = max(cv.contentVersionID)
					FROM dbo.tr_glAccounts as gl
					INNER JOIN dbo.cms_content as c on c.contentID = gl.invoiceContentID
					INNER JOIN dbo.cms_siteResources sr	on sr.siteResourceID = c.siteResourceID 
					INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
					INNER JOIN dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID AND cl.languageID = 1
					INNER JOIN dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID
					WHERE gl.GLAccountID = @AdjToMakeGLAID
					AND cv.isActive = 1
					AND len(cv.rawContent) > 0
				INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, messageContentVersionID)
				VALUES (@AdjSaleTransactionID, @invoiceID, 0, 0, 0, @contentVersionID)
			END ELSE BEGIN
				INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount)
				VALUES (@AdjTaxTransactionID, @invoiceID, 0, 0, 0)
			END

			-- update invoiceTransactions to reduce amount of initial sale/adj
			-- get adjToMakeTID and all its adjustments in order of oldest ITID
			select @it_amtLeftToAdj = abs(@AdjToMakeAmt)
			select @minITID = null
			select @minITID = min(it.itID)
				from dbo.tr_invoiceTransactions as it
				inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
				where (t.transactionID = @AdjToMakeTID
				OR t.transactionID in (
					select r.transactionID
					from dbo.tr_relationships as r
					inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
					inner join dbo.tr_transactions as tAdj on tAdj.transactionID = r.transactionID
					where r.appliedToTransactionID = @AdjToMakeTID
					and tAdj.statusID <> 2
				))
				and t.statusID <> 2
				and it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount > 0
			while @minITID is not null begin

				select	@it_invoiceID=it.invoiceID, 
						@it_invstatus=ins.status,
						@it_MaxAmountToTake=it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount
					from dbo.tr_invoiceTransactions as it
					inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
					inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
					where it.itID = @minITID

				if @it_MaxAmountToTake < @it_amtLeftToAdj
					select @it_amtToAdj = @it_MaxAmountToTake
				ELSE
					select @it_amtToAdj = @it_amtLeftToAdj

				update dbo.tr_invoiceTransactions
				set cache_invoiceAmountAfterAdjustment = cache_invoiceAmountAfterAdjustment - @it_amtToAdj
				where itID = @minITID

				IF @AdjToMakeIsSale = 1 BEGIN
					INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID, amount)
					SELECT top 1 dbo.fn_tr_getRelationshipTypeID('AdjustInvTrans'), @AdjSaleTransactionID, transactionID, @it_amtToAdj
					FROM dbo.tr_invoiceTransactions
					WHERE itid = @minITID
				END ELSE BEGIN
					INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID, amount)
					SELECT top 1 dbo.fn_tr_getRelationshipTypeID('AdjustInvTrans'), @AdjTaxTransactionID, transactionID, @it_amtToAdj
					FROM dbo.tr_invoiceTransactions
					WHERE itid = @minITID
				END

				-- cleanup invoice
				-- if invoice is closed and is now fully paid with active payments, mark it as paid
				-- if invoice is paid and is now not fully paid with active payments, mark it as closed
				select @amtDueNoPendingOnInvoice = sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount)
					from dbo.tr_invoiceTransactions as it
					inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
					where it.invoiceID = @it_invoiceID
					and t.statusID <> 2
				IF @it_invstatus = 'closed' and @amtDueNoPendingOnInvoice = 0 BEGIN
					update dbo.tr_invoices
					set statusID = 4, payProfileID = null
					where invoiceID = @it_invoiceID

					insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
					values (@it_invoiceID, getdate(), 4, 3, @recordedByMemberID)
				END
				IF @it_invstatus = 'paid' and @amtDueNoPendingOnInvoice > 0 BEGIN
					update dbo.tr_invoices
					set statusID = 3
					where invoiceID = @it_invoiceID

					insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
					values (@it_invoiceID, getdate(), 3, 4, @recordedByMemberID)
				END

				select @it_amtLeftToAdj = @it_amtLeftToAdj - @it_amtToAdj
				IF @it_amtLeftToAdj <= 0
					BREAK

				select @minITID = min(it.itID)
					from dbo.tr_invoiceTransactions as it
					inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
					where (t.transactionID = @AdjToMakeTID
					OR t.transactionID in (
						select r.transactionID
						from dbo.tr_relationships as r
						inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
						inner join dbo.tr_transactions as tAdj on tAdj.transactionID = r.transactionID
						where r.appliedToTransactionID = @AdjToMakeTID
						and tAdj.statusID <> 2
					))
					and t.statusID <> 2
					and it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount > 0
					and it.itid > @minITID
			end

			-- update cache
			UPDATE dbo.tr_transactionSales
			SET cache_amountAfterAdjustment = cache_amountAfterAdjustment - abs(@AdjToMakeAmt)
			WHERE transactionID = @AdjToMakeTID

			select @AdjToMakeAutoID = min(autoid) from @tblAdjToMakeFinal where autoid > @AdjToMakeAutoID
		END

		-- Record new AllocTaxTrans relationship if deallocations happened
		IF (select count(*) from @tblAllocationsToMake) > 0 BEGIN
			update tbl
			set tbl.newRelTID = tbl2.newAllocationTID
			from @tblAllocationsToMake as tbl
			inner join @tblAllocationsToMake as tbl2 on tbl2.origAllocationTID = tbl.origRelTID
			where tbl.newAllocationTID is not null
			and tbl.origRelTID is not null

			INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
			select dbo.fn_tr_getRelationshipTypeID('AllocTaxTrans'), newAllocationTID, newRelTID
			from @tblAllocationsToMake
			where newAllocationTID is not null 
			and newRelTID is not null
		END

	END


	IF @TranCounter = 0
		COMMIT TRAN;
	SELECT @transactionID = @AdjSaleTransactionID
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @transactionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

DROP FUNCTION dbo.fn_tr_salesWithAmountDue
GO
CREATE FUNCTION [dbo].[fn_tr_salesWithAmountDue] (
	@memberid int,
	@orgid int
)
RETURNS @tblSales TABLE (
	transactionID int,
	paymentDueAmount money,
	activePaymentAllocatedAmount money,
	pendingPaymentAllocatedAmount money,
	numtaxDue int,
	transactionDate datetime,
	detail varchar(max)
)
AS
BEGIN

	DECLARE @tblHold TABLE (
		saleTransactionID int,
		transactionID int,
		transactionDate datetime,
		paymentDueAmount money,
		activePaymentAllocatedAmount money,
		pendingPaymentAllocatedAmount money,
		numtaxDue int,
		detail varchar(max)
	)

	-- sale	
	INSERT INTO @tblHold
	select t.transactionID, t.transactionID, t.transactionDate, ts.cache_amountAfterAdjustment - ts.cache_activePaymentAllocatedAmount,
		ts.cache_activePaymentAllocatedAmount, ts.cache_pendingPaymentAllocatedAmount, 0, t.detail
	from dbo.tr_transactions as t
	inner join dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID 
	inner join dbo.ams_members as m on m.memberid = t.assignedToMemberID
	where t.ownedByOrgID = @orgid
	and t.typeID = 1
	and t.statusID = 1
	and m.activeMemberID = @memberid
	and ts.cache_amountAfterAdjustment - ts.cache_activePaymentAllocatedAmount > 0

	-- tax
	INSERT INTO @tblHold
	select tbl.saleTransactionID, t.transactionID, tbl.transactionDate, ts.cache_amountAfterAdjustment - ts.cache_activePaymentAllocatedAmount,
		ts.cache_activePaymentAllocatedAmount, ts.cache_pendingPaymentAllocatedAmount, 1, tbl.detail
	from dbo.tr_transactions as t
	inner join dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID 
	inner join dbo.tr_relationships as r on r.TransactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'SalesTaxTrans'
	inner join @tblHold as tbl on tbl.saleTransactionID = r.appliedToTransactionID
	where t.statusID = 1
	and ts.cache_amountAfterAdjustment - ts.cache_activePaymentAllocatedAmount > 0

	-- deferred
	INSERT INTO @tblHold
	select tbl.saleTransactionID, t.transactionID, tbl.transactionDate, ts.cache_amountAfterAdjustment - ts.cache_activePaymentAllocatedAmount,
		ts.cache_activePaymentAllocatedAmount, ts.cache_pendingPaymentAllocatedAmount, 0, tbl.detail
	from dbo.tr_transactions as t
	inner join dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID 
	inner join dbo.tr_relationships as r on r.TransactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
	inner join @tblHold as tbl on tbl.transactionID = r.appliedToTransactionID
	where t.statusID = 1
	and ts.cache_amountAfterAdjustment - ts.cache_activePaymentAllocatedAmount > 0

	insert into @tblSales
	select saleTransactionID, sum(paymentDueAmount), sum(activePaymentAllocatedAmount), sum(pendingPaymentAllocatedAmount), sum(numtaxDue), 
		transactionDate, detail
	from @tblHold
	group by saleTransactionID, transactionDate, detail

	RETURN
END
GO

ALTER PROC [dbo].[tr_viewTransaction_sale]
@transactionID int

AS

set nocount on

declare @orgID int, @deferredRevenueGL int
select @orgID = ownedByOrgID from dbo.tr_transactions where transactionID = @transactionID
select @deferredRevenueGL = GLAccountID from dbo.tr_glaccounts where orgID = @orgID and GLCode = 'DEFERREDREVENUE' and status = 'A'

declare @allGLs TABLE (GLAccountID int, thePathExpanded varchar(max), accountCode varchar(200))
insert into @allGLS
select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode
from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl

-- transaction info
select TOP 1 t.transactionid, t.ownedByOrgID, t.recordedOnSiteID, tt.type, t.detail, t.amount, 
	t.transactionDate, t.dateRecorded, ts.status, mAss2.memberID as assignedTomemberID, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullif(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as recordedByMember,
	m2.company as recordedByMemberCompany
from dbo.tr_transactions as t
inner join dbo.tr_types as tt on tt.typeID = t.typeID
inner join dbo.tr_statuses as ts on ts.statusID = t.statusID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join dbo.ams_members as m on m.memberid = t.recordedByMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
where t.transactionID = @transactionID

-- sale info
select top 1 ts.saleID, ts.cache_amountAfterAdjustment as amountAfterAdjustment,
	ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount-ts.cache_pendingPaymentAllocatedAmount as unAllocatedAmount,
	ts.cache_activePaymentAllocatedAmount+ts.cache_pendingPaymentAllocatedAmount as allocatedAmount,
	paymentDueAmount =
		case 
		when t.statusID = 2 then 0
		else ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount
		end,
	btn_canApplyPayment = 
		case 
		when t.statusID = 1 and ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount > 0 then 1 
		else 0 
		end,
	btn_canWriteOff =
		case 
		when t.statusID = 1 and ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount > 0 then 1 
		else 0 
		end,
	btn_canAdjust =
		case 
		when t.statusID = 1 then 1 
		else 0 
		end,
	btn_canVoid = case when t.statusID = 1 then 1 else 0 end,
	hasAdjustments = case when exists (
		select adj.transactionID
		from dbo.tr_transactions as adj
		inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
		inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
		where adj.statusID = 1
		) then 1 else 0 end
from dbo.tr_transactionSales as ts
inner join dbo.tr_transactions as t on t.transactionID = ts.transactionID
where t.transactionID = @transactionID

-- invoices
select distinct i.invoiceID, ins.status, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, i.dateDue, i.invoiceProfileID, ip.profileName, i.invoiceCode
from dbo.tr_transactions as t
inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
inner join dbo.organizations as o on o.orgID = @orgID
where t.transactionID in (
	select @transactionID as transactionID
	union
	select adj.transactionID
	from dbo.tr_transactions as adj
	inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
	inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
)
order by i.dateDue desc, 3 desc

-- current allocations
select t.transactionID, apos.allocatedAmount as allocAmount, t.amount, t.detail,
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL
from dbo.fn_tr_getAllocatedPaymentsofSale(@transactionID) as apos
inner join dbo.tr_transactions as t on t.transactionID = apos.paymentTransactionID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glDeb on glDeb.GLAccountID = t.debitGLAccountID
order by t.transactionDate desc


-- current gl spread
declare @tblTrans TABLE (transactionID int, glAccountID int, debitAmount money, creditAmount money)
insert into @tblTrans
select transactionID, creditGLAccountID, 0, amount
from dbo.tr_transactions
where transactionID = @transactionID
and statusID = 1

insert into @tblTrans
select transactionID, debitGLAccountID, amount, 0
from dbo.tr_transactions
where transactionID = @transactionID
and statusID = 1

insert into @tblTrans
select distinct adj.transactionID, adj.creditGLAccountID, 0, adj.amount
from dbo.tr_transactions as adj
inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
inner join dbo.tr_GLAccounts as glDeb on glDeb.glaccountID = adj.debitGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glDeb.glcode = 'ACCOUNTSRECEIVABLE'
and adj.statusID = 1

insert into @tblTrans
select distinct adj.transactionID, adj.debitGLAccountID, adj.amount, 0
from dbo.tr_transactions as adj
inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
inner join dbo.tr_GLAccounts as glDeb on glDeb.glaccountID = adj.debitGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glDeb.glcode = 'ACCOUNTSRECEIVABLE'
and adj.statusID = 1

insert into @tblTrans
select distinct adj.transactionID, adj.debitGLAccountID, 0, adj.amount * -1
from dbo.tr_transactions as adj
inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
inner join dbo.tr_GLAccounts as glCred on glCred.glaccountID = adj.creditGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glCred.glcode = 'ACCOUNTSRECEIVABLE'
and adj.statusID = 1

insert into @tblTrans
select distinct adj.transactionID, adj.creditGLAccountID, adj.amount * -1, 0
from dbo.tr_transactions as adj
inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
inner join dbo.tr_GLAccounts as glCred on glCred.glaccountID = adj.creditGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glCred.glcode = 'ACCOUNTSRECEIVABLE'
and adj.statusID = 1

insert into @tblTrans
select distinct wo.transactionID, wo.debitGLAccountID, 0, wo.amount * -1
from dbo.tr_transactions as wo
inner join dbo.tr_relationships as rInner on rInner.transactionID = wo.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'WriteOffSaleTrans'
inner join dbo.tr_GLAccounts as glCred on glCred.glaccountID = wo.creditGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glCred.glcode = 'ACCOUNTSRECEIVABLE'
and wo.statusID = 1

insert into @tblTrans
select distinct wo.transactionID, wo.creditGLAccountID, wo.amount * -1, 0
from dbo.tr_transactions as wo
inner join dbo.tr_relationships as rInner on rInner.transactionID = wo.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'WriteOffSaleTrans'
inner join dbo.tr_GLAccounts as glCred on glCred.glaccountID = wo.creditGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glCred.glcode = 'ACCOUNTSRECEIVABLE'
and wo.statusID = 1

insert into @tblTrans
select distinct dit.transactionID, dit.debitGLAccountID, 0, dit.amount * -1
from dbo.tr_transactions as dit
inner join dbo.tr_relationships as rInner on rInner.transactionID = dit.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'DITSaleTrans'
inner join dbo.tr_GLAccounts as glCred on glCred.glaccountID = dit.creditGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glCred.parentGLAccountID = @deferredRevenueGL
and dit.statusID = 1

insert into @tblTrans
select distinct dit.transactionID, dit.creditGLAccountID, 0, dit.amount
from dbo.tr_transactions as dit
inner join dbo.tr_relationships as rInner on rInner.transactionID = dit.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'DITSaleTrans'
inner join dbo.tr_GLAccounts as glCred on glCred.glaccountID = dit.creditGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glCred.parentGLAccountID = @deferredRevenueGL
and dit.statusID = 1

insert into @tblTrans
select distinct dit.transactionID, dit.creditGLAccountID, 0, dit.amount
from dbo.tr_transactions as dit
inner join dbo.tr_relationships as rInner on rInner.transactionID = dit.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'DITSaleTrans'
inner join dbo.tr_GLAccounts as glDeb on glDeb.glaccountID = dit.debitGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glDeb.parentGLAccountID = @deferredRevenueGL
and dit.statusID = 1

insert into @tblTrans
select distinct dit.transactionID, dit.debitGLAccountID, 0, dit.amount * -1
from dbo.tr_transactions as dit
inner join dbo.tr_relationships as rInner on rInner.transactionID = dit.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'DITSaleTrans'
inner join dbo.tr_GLAccounts as glDeb on glDeb.glaccountID = dit.debitGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glDeb.parentGLAccountID = @deferredRevenueGL
and dit.statusID = 1

select summeddata.debitAmount, summeddata.creditAmount, gl.thePathExpanded + isnull(' (' + nullIf(gl.accountCode,'') + ')','') as glexpanded
from (
	select glAccountID, sum(debitAmount) as debitAmount, sum(creditAmount) as creditAmount
	from @tblTrans
	group by glAccountID
	having sum(debitAmount) <> 0 OR sum(creditAmount) <> 0
) as summeddata
inner join @allGLS as gl on gl.GLAccountID = summeddata.GLAccountID
order by 3


-- deferred schedule
select dit.recognitionDate, sum(t.amount) as recogAmt
from dbo.tr_transactions as t
inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
inner join (
	select @transactionID as transactionID
		union
	select adj.transactionID
	from dbo.tr_transactions as adj
	inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
	inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
	where adj.statusID = 1
) as tbl on tbl.transactionID = r.appliedToTransactionID
inner join dbo.tr_transactionDIT as dit on dit.transactionID = t.transactionID
where t.statusID = 1
group by dit.recognitionDate
order by dit.recognitionDate

-- related transactions
EXEC dbo.tr_viewTransaction_related_recursive @transactionID

RETURN 0
GO


ALTER PROC [dbo].[tr_viewTransaction_salestax]
@transactionID int

AS

set nocount on

declare @orgID int, @deferredTaxGL int
select @orgID = ownedByOrgID from dbo.tr_transactions where transactionID = @transactionID
select @deferredTaxGL = GLAccountID from dbo.tr_glaccounts where orgID = @orgID and GLCode = 'DEFERREDTAX' and status = 'A'

declare @allGLs TABLE (GLAccountID int, thePathExpanded varchar(max), accountCode varchar(200))
insert into @allGLS
select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode
from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl

-- transaction info
select TOP 1 t.transactionid, t.ownedByOrgID, t.recordedOnSiteID, tt.type, t.detail, t.amount, 
	t.transactionDate, t.dateRecorded, ts.status, mAss2.memberID as assignedTomemberID, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullif(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as recordedByMember,
	m2.company as recordedByMemberCompany
from dbo.tr_transactions as t
inner join dbo.tr_types as tt on tt.typeID = t.typeID
inner join dbo.tr_statuses as ts on ts.statusID = t.statusID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join dbo.ams_members as m on m.memberid = t.recordedByMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
where t.transactionID = @transactionID

-- salestax info
select top 1 ts.saleID, ts.cache_amountAfterAdjustment as amountAfterAdjustment,
	ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount-ts.cache_pendingPaymentAllocatedAmount as unAllocatedAmount,
	ts.cache_activePaymentAllocatedAmount+ts.cache_pendingPaymentAllocatedAmount as allocatedAmount,
	hasAdjustments = case when exists (
		select adj.transactionID
		from dbo.tr_transactions as adj
		inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
		inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
		where adj.statusID = 1
		) then 1 else 0 end
from dbo.tr_transactionSales as ts
where ts.transactionID = @transactionID

-- invoices
select distinct i.invoiceID, ins.status, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, i.dateDue, i.invoiceProfileID, ip.profileName, i.invoiceCode
from dbo.tr_transactions as t
inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
inner join dbo.organizations as o on o.orgID = @orgID
where t.transactionID in (
	select @transactionID as transactionID
	union
	select adj.transactionID
	from dbo.tr_transactions as adj
	inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
	inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
)
order by i.dateDue desc, 3 desc

-- current allocations
select t.transactionID, apos.allocatedAmount as allocAmount, t.amount, t.detail,
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL
from dbo.fn_tr_getAllocatedPaymentsofSale(@transactionID) as apos
inner join dbo.tr_transactions as t on t.transactionID = apos.paymentTransactionID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glDeb on glDeb.GLAccountID = t.debitGLAccountID
order by t.transactionDate desc


-- current gl spread
declare @tblTrans TABLE (transactionID int, glAccountID int, debitAmount money, creditAmount money)
insert into @tblTrans
select transactionID, creditGLAccountID, 0, amount
from dbo.tr_transactions
where transactionID = @transactionID
and statusID = 1

insert into @tblTrans
select transactionID, debitGLAccountID, amount, 0
from dbo.tr_transactions
where transactionID = @transactionID
and statusID = 1

insert into @tblTrans
select distinct adj.transactionID, adj.creditGLAccountID, 0, adj.amount
from dbo.tr_transactions as adj
inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
inner join dbo.tr_GLAccounts as glDeb on glDeb.glaccountID = adj.debitGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glDeb.glcode = 'ACCOUNTSRECEIVABLE'
and adj.statusID = 1

insert into @tblTrans
select distinct adj.transactionID, adj.debitGLAccountID, adj.amount, 0
from dbo.tr_transactions as adj
inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
inner join dbo.tr_GLAccounts as glDeb on glDeb.glaccountID = adj.debitGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glDeb.glcode = 'ACCOUNTSRECEIVABLE'
and adj.statusID = 1

insert into @tblTrans
select distinct adj.transactionID, adj.debitGLAccountID, 0, adj.amount * -1
from dbo.tr_transactions as adj
inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
inner join dbo.tr_GLAccounts as glCred on glCred.glaccountID = adj.creditGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glCred.glcode = 'ACCOUNTSRECEIVABLE'
and adj.statusID = 1

insert into @tblTrans
select distinct adj.transactionID, adj.creditGLAccountID, adj.amount * -1, 0
from dbo.tr_transactions as adj
inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
inner join dbo.tr_GLAccounts as glCred on glCred.glaccountID = adj.creditGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glCred.glcode = 'ACCOUNTSRECEIVABLE'
and adj.statusID = 1

insert into @tblTrans
select distinct wo.transactionID, wo.debitGLAccountID, 0, wo.amount * -1
from dbo.tr_transactions as wo
inner join dbo.tr_relationships as rInner on rInner.transactionID = wo.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'WriteOffSaleTrans'
inner join dbo.tr_GLAccounts as glCred on glCred.glaccountID = wo.creditGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glCred.glcode = 'ACCOUNTSRECEIVABLE'
and wo.statusID = 1

insert into @tblTrans
select distinct wo.transactionID, wo.creditGLAccountID, wo.amount * -1, 0
from dbo.tr_transactions as wo
inner join dbo.tr_relationships as rInner on rInner.transactionID = wo.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'WriteOffSaleTrans'
inner join dbo.tr_GLAccounts as glCred on glCred.glaccountID = wo.creditGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glCred.glcode = 'ACCOUNTSRECEIVABLE'
and wo.statusID = 1

insert into @tblTrans
select distinct dit.transactionID, dit.debitGLAccountID, 0, dit.amount * -1
from dbo.tr_transactions as dit
inner join dbo.tr_relationships as rInner on rInner.transactionID = dit.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'DITSaleTrans'
inner join dbo.tr_GLAccounts as glCred on glCred.glaccountID = dit.creditGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glCred.parentGLAccountID = @deferredTaxGL
and dit.statusID = 1

insert into @tblTrans
select distinct dit.transactionID, dit.creditGLAccountID, 0, dit.amount
from dbo.tr_transactions as dit
inner join dbo.tr_relationships as rInner on rInner.transactionID = dit.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'DITSaleTrans'
inner join dbo.tr_GLAccounts as glCred on glCred.glaccountID = dit.creditGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glCred.parentGLAccountID = @deferredTaxGL
and dit.statusID = 1

insert into @tblTrans
select distinct dit.transactionID, dit.creditGLAccountID, 0, dit.amount
from dbo.tr_transactions as dit
inner join dbo.tr_relationships as rInner on rInner.transactionID = dit.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'DITSaleTrans'
inner join dbo.tr_GLAccounts as glDeb on glDeb.glaccountID = dit.debitGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glDeb.parentGLAccountID = @deferredTaxGL
and dit.statusID = 1

insert into @tblTrans
select distinct dit.transactionID, dit.debitGLAccountID, 0, dit.amount * -1
from dbo.tr_transactions as dit
inner join dbo.tr_relationships as rInner on rInner.transactionID = dit.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'DITSaleTrans'
inner join dbo.tr_GLAccounts as glDeb on glDeb.glaccountID = dit.debitGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glDeb.parentGLAccountID = @deferredTaxGL
and dit.statusID = 1

select summeddata.debitAmount, summeddata.creditAmount, gl.thePathExpanded + isnull(' (' + nullIf(gl.accountCode,'') + ')','') as glexpanded
from (
	select glAccountID, sum(debitAmount) as debitAmount, sum(creditAmount) as creditAmount
	from @tblTrans
	group by glAccountID
	having sum(debitAmount) <> 0 OR sum(creditAmount) <> 0
) as summeddata
inner join @allGLS as gl on gl.GLAccountID = summeddata.GLAccountID
order by 3


-- deferred schedule
select dit.recognitionDate, sum(t.amount) as recogAmt
from dbo.tr_transactions as t
inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
inner join (
	select @transactionID as transactionID
		union
	select adj.transactionID
	from dbo.tr_transactions as adj
	inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
	inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
	where adj.statusID = 1
) as tbl on tbl.transactionID = r.appliedToTransactionID
inner join dbo.tr_transactionDIT as dit on dit.transactionID = t.transactionID
where t.statusID = 1
group by dit.recognitionDate
order by dit.recognitionDate

-- sale transaction
select top 1 tSale.transactionID, tt.type, tSale.amount, tSale.detail,
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	glCred.thePathExpanded + isnull(' (' + nullIf(glCred.accountCode,'') + ')','') as creditGL
from dbo.tr_transactions as tSale
inner join dbo.tr_types as tt on tt.typeID = tSale.typeID
inner join dbo.tr_relationships as rTax on rTax.appliedToTransactionID = tSale.transactionID
inner join dbo.tr_relationshipTypes as rtTax on rtTax.typeID = rTax.typeID and rtTax.type = 'SalesTaxTrans'
inner join dbo.ams_members as mAss on mAss.memberid = tSale.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glCred on glCred.GLAccountID = tSale.creditGLAccountID
where rTax.transactionID = @transactionID

-- related transactions
EXEC dbo.tr_viewTransaction_related_recursive @transactionID

RETURN 0
GO


ALTER PROC [dbo].[tr_viewTransaction_related]
@transactionID int

AS

declare @tblTrans TABLE (transactionID int)
insert into @tblTrans (transactionID)
select transactionID
from dbo.tr_relationships
where appliedToTransactionID = @transactionID
	union
select appliedToTransactionID
from dbo.tr_relationships
where transactionID = @transactionID

declare @typeID int, @orgID int, @deferredRevenueGL int, @deferredTaxGL int
select @typeID = typeID, @orgID = ownedByOrgID from dbo.tr_transactions where transactionID = @transactionID
select @deferredRevenueGL = GLAccountID from dbo.tr_glaccounts where orgID = @orgID and GLCode = 'DEFERREDREVENUE' and status = 'A'
select @deferredTaxGL = GLAccountID from dbo.tr_glaccounts where orgID = @orgID and GLCode = 'DEFERREDTAX' and status = 'A'

select t.transactionID, tt.type, t.transactionDate, t.amount, 
	case 
	when t.typeID = 3 and gl.glCode = 'ACCOUNTSRECEIVABLE' then 'Negative Adjustment of ' + isnull(t.detail,'')
	when t.typeID = 3 and gl.glCode <> 'ACCOUNTSRECEIVABLE' then 'Adjustment of ' + isnull(t.detail,'')
	when t.typeID = 6 and gl.glCode = 'ACCOUNTSRECEIVABLE' then 'WriteOff of ' + isnull(t.detail,'')
	when t.typeID = 6 and gl.glCode <> 'ACCOUNTSRECEIVABLE' then 'Negative WriteOff of ' + isnull(t.detail,'')
	when t.typeID = 9 then 'NSF of ' + isnull(t.detail,'')
	when t.typeID = 5 and gl.glCode = 'ACCOUNTSRECEIVABLE' then 'Allocation of Payment'
	when t.typeID = 5 and gl.glCode <> 'ACCOUNTSRECEIVABLE' then 'Deallocation of Payment'
	when t.typeID = 8 and tVoidee.typeID = 3 and glVoidee.glCode = 'ACCOUNTSRECEIVABLE' then 'VOID of Negative Adjustment of ' + isnull(tVoidee.detail,'')
	when t.typeID = 8 and tVoidee.typeID = 3 and glVoidee.glCode <> 'ACCOUNTSRECEIVABLE' then 'VOID of Adjustment of ' + isnull(tVoidee.detail,'')
	when t.typeID = 8 and tVoidee.typeID = 6 and glVoidee.glCode = 'ACCOUNTSRECEIVABLE' then 'VOID of WriteOff of ' + isnull(tVoidee.detail,'')
	when t.typeID = 8 and tVoidee.typeID = 6 and glVoidee.glCode <> 'ACCOUNTSRECEIVABLE' then 'VOID of Negative WriteOff of ' + isnull(tVoidee.detail,'')
	when t.typeID = 8 and tVoidee.typeID = 9 then 'VOID of NSF of ' + isnull(tVoidee.detail,'')
	when t.typeID = 8 and tVoidee.typeID = 5 and glVoidee.glCode = 'ACCOUNTSRECEIVABLE' then 'VOID of Allocation of Payment'
	when t.typeID = 8 and tVoidee.typeID = 5 and glVoidee.glCode <> 'ACCOUNTSRECEIVABLE' then 'VOID of Deallocation of Payment'
	when t.typeID = 8 then 'VOID of ' + isnull(t.detail,'')
	when t.typeID = 10 and gl.parentGLAccountID = @deferredRevenueGL then 'Transfer to Deferred Revenue'
	when t.typeID = 10 and gl.parentGLAccountID = @deferredTaxGL then 'Transfer to Deferred Sales Tax'
	when t.typeID = 10 and glDeb.parentGLAccountID = @deferredRevenueGL then 'Transfer to Recognized Revenue'
	when t.typeID = 10 and glDeb.parentGLAccountID = @deferredTaxGL then 'Transfer to Recognized Sales Tax'
	else isnull(t.detail,'') 
	end as detail, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	case 
	when t.typeID = 5 and @typeID = 2 then isnull(tSale.detail,'')
	when t.typeID = 5 and @typeID <> 2 then isnull(tPay.detail,'')
	when t.typeID = 8 and tVoidee.typeID = 5 then isnull(tVoideeSale.detail,'')
	else null
	end as allocDetail,
	case 
	when t.typeID = 5 and @typeID = 2 then tSale.transactionID
	when t.typeID = 5 and @typeID <> 2 then tPay.transactionID
	when t.typeID = 8 and tVoidee.typeID = 5 then tVoideeSale.transactionID
	else null
	end as allocDetailTransactionID,
	case 
	when t.typeID = 5 and @typeID = 2 then mAss2Sale.firstname + isnull(' ' + nullif(mAss2Sale.middlename,''),'') + ' ' + mAss2Sale.lastname + isnull(' ' + nullif(mAss2Sale.suffix,''),'') + ' (' + mAss2Sale.membernumber + ')'
	when t.typeID = 5 and @typeID <> 2 then mAss2Pay.firstname + isnull(' ' + nullif(mAss2Pay.middlename,''),'') + ' ' + mAss2Pay.lastname + isnull(' ' + nullif(mAss2Pay.suffix,''),'') + ' (' + mAss2Pay.membernumber + ')'
	when t.typeID = 8 and tVoidee.typeID = 5 then mAss2VoideeSale.firstname + isnull(' ' + nullif(mAss2VoideeSale.middlename,''),'') + ' ' + mAss2VoideeSale.lastname + isnull(' ' + nullif(mAss2VoideeSale.suffix,''),'') + ' (' + mAss2VoideeSale.membernumber + ')'
	else null
	end as allocDetailAssignedToMember,
	case 
	when t.typeID = 5 and @typeID = 2 then mAss2Sale.company
	when t.typeID = 5 and @typeID <> 2 then mAss2Pay.company
	when t.typeID = 8 and tVoidee.typeID = 5 then mAss2VoideeSale.company
	else null
	end as allocDetailAssignedToMemberCompany
from dbo.tr_transactions as t
inner join @tblTrans as tbl on tbl.transactionID = t.transactionID
INNER JOIN dbo.tr_types as tt on tt.typeID = t.typeID
INNER JOIN dbo.tr_glAccounts as gl on gl.glaccountID = t.creditGLAccountID
INNER JOIN dbo.tr_glAccounts as glDeb on glDeb.glaccountID = t.debitGLAccountID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
LEFT OUTER JOIN dbo.tr_relationships AS rAllocPay 
	INNER JOIN dbo.tr_relationshipTypes AS rtAllocPay ON rtAllocPay.typeID = rAllocPay.typeID AND rtAllocPay.type = 'AllocPayTrans'
	INNER JOIN dbo.tr_transactions as tPay on tPay.transactionID = rAllocPay.appliedToTransactionID
	inner join dbo.ams_members as mAssPay on mAssPay.memberid = tPay.assignedToMemberID
	inner join dbo.ams_members as mAss2Pay on mAss2Pay.memberID = mAssPay.activeMemberID
	on rAllocPay.transactionID = t.transactionID and t.typeID = 5
LEFT OUTER JOIN dbo.tr_relationships AS rAllocSale 
	INNER JOIN dbo.tr_relationshipTypes AS rtAllocSale ON rtAllocSale.typeID = rAllocSale.typeID AND rtAllocSale.type = 'AllocSaleTrans'
	INNER JOIN dbo.tr_transactions as tSale on tSale.transactionID = rAllocSale.appliedToTransactionID
	inner join dbo.ams_members as mAssSale on mAssSale.memberid = tSale.assignedToMemberID
	inner join dbo.ams_members as mAss2Sale on mAss2Sale.memberID = mAssSale.activeMemberID
	on rAllocSale.transactionID = t.transactionID and t.typeID = 5
LEFT OUTER JOIN dbo.tr_relationships AS rVoid
	INNER JOIN dbo.tr_relationshipTypes AS rtVoid ON rtVoid.typeID = rVoid.typeID AND rtVoid.type = 'OffsetTrans'
	INNER JOIN dbo.tr_transactions as tVoidee on tVoidee.transactionID = rVoid.appliedToTransactionID
	INNER JOIN dbo.tr_glAccounts as glVoidee on glVoidee.glaccountID = tVoidee.creditGLAccountID
	LEFT OUTER JOIN dbo.tr_relationships AS rVoideeAllocSale 
		INNER JOIN dbo.tr_relationshipTypes AS rtVoideeAllocSale ON rtVoideeAllocSale.typeID = rVoideeAllocSale.typeID AND rtVoideeAllocSale.type = 'AllocSaleTrans'
		INNER JOIN dbo.tr_transactions as tVoideeSale on tVoideeSale.transactionID = rVoideeAllocSale.appliedToTransactionID
		inner join dbo.ams_members as mAssVoideeSale on mAssVoideeSale.memberid = tVoideeSale.assignedToMemberID
		inner join dbo.ams_members as mAss2VoideeSale on mAss2VoideeSale.memberID = mAssVoideeSale.activeMemberID
		on rVoideeAllocSale.transactionID = tVoidee.transactionID and tVoidee.typeID = 5
	on rVoid.transactionID = t.transactionID and t.typeID = 8
order by t.transactionDate desc, t.transactionID desc

RETURN 0
GO

DROP FUNCTION dbo.fn_tr_getAllocatedPaymentsofSale
GO
CREATE FUNCTION [dbo].[fn_tr_getAllocatedPaymentsofSale] (
	@saleTransactionID int
) 
RETURNS @tblPayments TABLE (
	paymentTransactionID int,
	allocatedAmount money
)
AS
BEGIN

	DECLARE @tblHold TABLE (
		saleTransactionID int,	
		transactionID int
	)

	-- sale	
	INSERT INTO @tblHold
	select transactionID, transactionID
	from dbo.tr_transactions
	WHERE transactionID = @saleTransactionID
	AND typeID in (1,7,10)
	AND statusID = 1

	-- adj
	INSERT INTO @tblHold
	select distinct tbl.saleTransactionID, adj.transactionID
	from dbo.tr_transactions as adj
	inner join dbo.tr_relationships as r on r.transactionID = adj.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
	inner join @tblHold as tbl on tbl.saleTransactionID = r.appliedToTransactionID
	where adj.statusID = 1

	-- dit
	INSERT INTO @tblHold
	select distinct tbl.saleTransactionID, dit.transactionID
	from dbo.tr_transactions as dit
	inner join dbo.tr_relationships as r on r.transactionID = dit.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
	inner join @tblHold as tbl on tbl.transactionID = r.appliedToTransactionID
	where dit.statusID = 1


	insert into @tblPayments
	select paymentTransactionID, sum(allocAmt)
	from (
		select tPay.transactionID as paymentTransactionID, CASE WHEN glAllocDeb.GLCode = 'ACCOUNTSRECEIVABLE' then tAlloc.amount*-1 ELSE tAlloc.amount END as allocAmt
		from @tblHold as tbl
		inner join dbo.tr_relationships as rS on rS.appliedToTransactionID = tbl.transactionID
		inner join dbo.tr_relationshipTypes as rtS on rtS.typeID = rS.typeID and rtS.type = 'AllocSaleTrans'
		inner join dbo.tr_transactions as tAlloc on tAlloc.transactionID = rS.transactionID and tAlloc.statusID = 1
		inner join dbo.tr_GLAccounts as glAllocDeb on glAllocDeb.GLAccountID = tAlloc.debitGLAccountID
		inner join dbo.tr_relationships as rP on rP.transactionID = tAlloc.transactionID
		inner join dbo.tr_relationshipTypes as rtP on rtP.typeID = rP.typeID and rtP.type = 'AllocPayTrans'
		inner join dbo.tr_transactions as tPay on tPay.transactionID = rP.appliedToTransactionID and tPay.statusID = 1
	) as tmp
	group by paymentTransactionID
	having sum(allocAmt) > 0

	RETURN

END
GO

CREATE FUNCTION [dbo].[fn_tr_transactionSalesWithDIT] (
	@saleTransactionID int
) 
RETURNS @tblCache TABLE (
	transactionID int,
	cache_amountAfterAdjustment money,
	cache_activePaymentAllocatedAmount money,
	cache_pendingPaymentAllocatedAmount money
)
AS
BEGIN

	DECLARE @tblHold TABLE (transactionID int)

	-- sale	
	INSERT INTO @tblHold
	select transactionID
	from dbo.tr_transactions
	WHERE transactionID = @saleTransactionID
	AND typeID in (1,7,10)

	-- adj
	INSERT INTO @tblHold
	select distinct adj.transactionID
	from dbo.tr_transactions as adj
	inner join dbo.tr_relationships as r on r.transactionID = adj.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
	inner join @tblHold as tbl on tbl.transactionID = r.appliedToTransactionID

	-- dit
	INSERT INTO @tblHold
	select distinct dit.transactionID
	from dbo.tr_transactions as dit
	inner join dbo.tr_relationships as r on r.transactionID = dit.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
	inner join @tblHold as tbl on tbl.transactionID = r.appliedToTransactionID

	insert into @tblCache
	select @saleTransactionID as transactionID, 
		sum(cache_amountAfterAdjustment) as cache_amountAfterAdjustment,
		sum(cache_activePaymentAllocatedAmount) as cache_activePaymentAllocatedAmount,
		sum(cache_pendingPaymentAllocatedAmount) as cache_pendingPaymentAllocatedAmount
	from @tblHold as tbl
	inner join dbo.tr_transactionSales as ts on ts.transactionID = tbl.transactionID

	RETURN

END
GO

ALTER PROC [dbo].[tr_viewTransaction_sale]
@transactionID int

AS

set nocount on

declare @orgID int, @deferredRevenueGL int
select @orgID = ownedByOrgID from dbo.tr_transactions where transactionID = @transactionID
select @deferredRevenueGL = GLAccountID from dbo.tr_glaccounts where orgID = @orgID and GLCode = 'DEFERREDREVENUE' and status = 'A'

declare @allGLs TABLE (GLAccountID int, thePathExpanded varchar(max), accountCode varchar(200))
insert into @allGLS
select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode
from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl

-- transaction info
select TOP 1 t.transactionid, t.ownedByOrgID, t.recordedOnSiteID, tt.type, t.detail, t.amount, 
	t.transactionDate, t.dateRecorded, ts.status, mAss2.memberID as assignedTomemberID, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullif(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as recordedByMember,
	m2.company as recordedByMemberCompany
from dbo.tr_transactions as t
inner join dbo.tr_types as tt on tt.typeID = t.typeID
inner join dbo.tr_statuses as ts on ts.statusID = t.statusID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join dbo.ams_members as m on m.memberid = t.recordedByMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
where t.transactionID = @transactionID

-- sale info
select top 1 ts.saleID, 
	tsFull.cache_amountAfterAdjustment as amountAfterAdjustment,
	tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount-tsFull.cache_pendingPaymentAllocatedAmount as unAllocatedAmount,
	tsFull.cache_activePaymentAllocatedAmount+tsFull.cache_pendingPaymentAllocatedAmount as allocatedAmount,
	paymentDueAmount =
		case 
		when t.statusID = 2 then 0
		else tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount
		end,
	btn_canApplyPayment = 
		case 
		when t.statusID = 1 and tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount > 0 then 1 
		else 0 
		end,
	btn_canWriteOff =
		case 
		when t.statusID = 1 and tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount > 0 then 1 
		else 0 
		end,
	btn_canAdjust = case when t.statusID = 1 then 1 else 0 end,
	btn_canVoid = case when t.statusID = 1 then 1 else 0 end,
	hasAdjustments = case when exists (
		select adj.transactionID
		from dbo.tr_transactions as adj
		inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
		inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
		where adj.statusID = 1
		) then 1 else 0 end
from dbo.tr_transactionSales as ts
inner join dbo.tr_transactions as t on t.transactionID = ts.transactionID
cross apply dbo.fn_tr_transactionSalesWithDIT(t.transactionID) as tsFull
where t.transactionID = @transactionID

-- invoices
select distinct i.invoiceID, ins.status, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, i.dateDue, i.invoiceProfileID, ip.profileName, i.invoiceCode
from dbo.tr_transactions as t
inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
inner join dbo.organizations as o on o.orgID = @orgID
where t.transactionID in (
	select @transactionID as transactionID
	union
	select adj.transactionID
	from dbo.tr_transactions as adj
	inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
	inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
)
order by i.dateDue desc, 3 desc

-- current allocations
select t.transactionID, apos.allocatedAmount as allocAmount, t.amount, t.detail,
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL
from dbo.fn_tr_getAllocatedPaymentsofSale(@transactionID) as apos
inner join dbo.tr_transactions as t on t.transactionID = apos.paymentTransactionID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glDeb on glDeb.GLAccountID = t.debitGLAccountID
order by t.transactionDate desc


-- current gl spread
declare @tblTrans TABLE (transactionID int, glAccountID int, debitAmount money, creditAmount money)
insert into @tblTrans
select transactionID, creditGLAccountID, 0, amount
from dbo.tr_transactions
where transactionID = @transactionID
and statusID = 1

insert into @tblTrans
select transactionID, debitGLAccountID, amount, 0
from dbo.tr_transactions
where transactionID = @transactionID
and statusID = 1

insert into @tblTrans
select distinct adj.transactionID, adj.creditGLAccountID, 0, adj.amount
from dbo.tr_transactions as adj
inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
inner join dbo.tr_GLAccounts as glDeb on glDeb.glaccountID = adj.debitGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glDeb.glcode = 'ACCOUNTSRECEIVABLE'
and adj.statusID = 1

insert into @tblTrans
select distinct adj.transactionID, adj.debitGLAccountID, adj.amount, 0
from dbo.tr_transactions as adj
inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
inner join dbo.tr_GLAccounts as glDeb on glDeb.glaccountID = adj.debitGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glDeb.glcode = 'ACCOUNTSRECEIVABLE'
and adj.statusID = 1

insert into @tblTrans
select distinct adj.transactionID, adj.debitGLAccountID, 0, adj.amount * -1
from dbo.tr_transactions as adj
inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
inner join dbo.tr_GLAccounts as glCred on glCred.glaccountID = adj.creditGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glCred.glcode = 'ACCOUNTSRECEIVABLE'
and adj.statusID = 1

insert into @tblTrans
select distinct adj.transactionID, adj.creditGLAccountID, adj.amount * -1, 0
from dbo.tr_transactions as adj
inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
inner join dbo.tr_GLAccounts as glCred on glCred.glaccountID = adj.creditGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glCred.glcode = 'ACCOUNTSRECEIVABLE'
and adj.statusID = 1

insert into @tblTrans
select distinct wo.transactionID, wo.debitGLAccountID, 0, wo.amount * -1
from dbo.tr_transactions as wo
inner join dbo.tr_relationships as rInner on rInner.transactionID = wo.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'WriteOffSaleTrans'
inner join dbo.tr_GLAccounts as glCred on glCred.glaccountID = wo.creditGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glCred.glcode = 'ACCOUNTSRECEIVABLE'
and wo.statusID = 1

insert into @tblTrans
select distinct wo.transactionID, wo.creditGLAccountID, wo.amount * -1, 0
from dbo.tr_transactions as wo
inner join dbo.tr_relationships as rInner on rInner.transactionID = wo.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'WriteOffSaleTrans'
inner join dbo.tr_GLAccounts as glCred on glCred.glaccountID = wo.creditGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glCred.glcode = 'ACCOUNTSRECEIVABLE'
and wo.statusID = 1

insert into @tblTrans
select distinct dit.transactionID, dit.debitGLAccountID, 0, dit.amount * -1
from dbo.tr_transactions as dit
inner join dbo.tr_relationships as rInner on rInner.transactionID = dit.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'DITSaleTrans'
inner join dbo.tr_GLAccounts as glCred on glCred.glaccountID = dit.creditGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glCred.parentGLAccountID = @deferredRevenueGL
and dit.statusID = 1

insert into @tblTrans
select distinct dit.transactionID, dit.creditGLAccountID, 0, dit.amount
from dbo.tr_transactions as dit
inner join dbo.tr_relationships as rInner on rInner.transactionID = dit.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'DITSaleTrans'
inner join dbo.tr_GLAccounts as glCred on glCred.glaccountID = dit.creditGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glCred.parentGLAccountID = @deferredRevenueGL
and dit.statusID = 1

insert into @tblTrans
select distinct dit.transactionID, dit.creditGLAccountID, 0, dit.amount
from dbo.tr_transactions as dit
inner join dbo.tr_relationships as rInner on rInner.transactionID = dit.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'DITSaleTrans'
inner join dbo.tr_GLAccounts as glDeb on glDeb.glaccountID = dit.debitGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glDeb.parentGLAccountID = @deferredRevenueGL
and dit.statusID = 1

insert into @tblTrans
select distinct dit.transactionID, dit.debitGLAccountID, 0, dit.amount * -1
from dbo.tr_transactions as dit
inner join dbo.tr_relationships as rInner on rInner.transactionID = dit.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'DITSaleTrans'
inner join dbo.tr_GLAccounts as glDeb on glDeb.glaccountID = dit.debitGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glDeb.parentGLAccountID = @deferredRevenueGL
and dit.statusID = 1

select summeddata.debitAmount, summeddata.creditAmount, gl.thePathExpanded + isnull(' (' + nullIf(gl.accountCode,'') + ')','') as glexpanded
from (
	select glAccountID, sum(debitAmount) as debitAmount, sum(creditAmount) as creditAmount
	from @tblTrans
	group by glAccountID
	having sum(debitAmount) <> 0 OR sum(creditAmount) <> 0
) as summeddata
inner join @allGLS as gl on gl.GLAccountID = summeddata.GLAccountID
order by 3


-- deferred schedule
select dit.recognitionDate, sum(t.amount) as recogAmt
from dbo.tr_transactions as t
inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
inner join (
	select @transactionID as transactionID
		union
	select adj.transactionID
	from dbo.tr_transactions as adj
	inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
	inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
	where adj.statusID = 1
) as tbl on tbl.transactionID = r.appliedToTransactionID
inner join dbo.tr_transactionDIT as dit on dit.transactionID = t.transactionID
where t.statusID = 1
group by dit.recognitionDate
order by dit.recognitionDate

-- related transactions
EXEC dbo.tr_viewTransaction_related_recursive @transactionID

RETURN 0
GO

ALTER PROC [dbo].[tr_viewTransaction_salestax]
@transactionID int

AS

set nocount on

declare @orgID int, @deferredTaxGL int
select @orgID = ownedByOrgID from dbo.tr_transactions where transactionID = @transactionID
select @deferredTaxGL = GLAccountID from dbo.tr_glaccounts where orgID = @orgID and GLCode = 'DEFERREDTAX' and status = 'A'

declare @allGLs TABLE (GLAccountID int, thePathExpanded varchar(max), accountCode varchar(200))
insert into @allGLS
select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode
from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl

-- transaction info
select TOP 1 t.transactionid, t.ownedByOrgID, t.recordedOnSiteID, tt.type, t.detail, t.amount, 
	t.transactionDate, t.dateRecorded, ts.status, mAss2.memberID as assignedTomemberID, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullif(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as recordedByMember,
	m2.company as recordedByMemberCompany
from dbo.tr_transactions as t
inner join dbo.tr_types as tt on tt.typeID = t.typeID
inner join dbo.tr_statuses as ts on ts.statusID = t.statusID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join dbo.ams_members as m on m.memberid = t.recordedByMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
where t.transactionID = @transactionID

-- salestax info
select top 1 ts.saleID, 
	tsFull.cache_amountAfterAdjustment as amountAfterAdjustment,
	tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount-tsFull.cache_pendingPaymentAllocatedAmount as unAllocatedAmount,
	tsFull.cache_activePaymentAllocatedAmount+tsFull.cache_pendingPaymentAllocatedAmount as allocatedAmount,
	hasAdjustments = case when exists (
		select adj.transactionID
		from dbo.tr_transactions as adj
		inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
		inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
		where adj.statusID = 1
		) then 1 else 0 end
from dbo.tr_transactionSales as ts
cross apply dbo.fn_tr_transactionSalesWithDIT(ts.transactionID) as tsFull
where ts.transactionID = @transactionID

-- invoices
select distinct i.invoiceID, ins.status, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, i.dateDue, i.invoiceProfileID, ip.profileName, i.invoiceCode
from dbo.tr_transactions as t
inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
inner join dbo.organizations as o on o.orgID = @orgID
where t.transactionID in (
	select @transactionID as transactionID
	union
	select adj.transactionID
	from dbo.tr_transactions as adj
	inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
	inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
)
order by i.dateDue desc, 3 desc

-- current allocations
select t.transactionID, apos.allocatedAmount as allocAmount, t.amount, t.detail,
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL
from dbo.fn_tr_getAllocatedPaymentsofSale(@transactionID) as apos
inner join dbo.tr_transactions as t on t.transactionID = apos.paymentTransactionID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glDeb on glDeb.GLAccountID = t.debitGLAccountID
order by t.transactionDate desc


-- current gl spread
declare @tblTrans TABLE (transactionID int, glAccountID int, debitAmount money, creditAmount money)
insert into @tblTrans
select transactionID, creditGLAccountID, 0, amount
from dbo.tr_transactions
where transactionID = @transactionID
and statusID = 1

insert into @tblTrans
select transactionID, debitGLAccountID, amount, 0
from dbo.tr_transactions
where transactionID = @transactionID
and statusID = 1

insert into @tblTrans
select distinct adj.transactionID, adj.creditGLAccountID, 0, adj.amount
from dbo.tr_transactions as adj
inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
inner join dbo.tr_GLAccounts as glDeb on glDeb.glaccountID = adj.debitGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glDeb.glcode = 'ACCOUNTSRECEIVABLE'
and adj.statusID = 1

insert into @tblTrans
select distinct adj.transactionID, adj.debitGLAccountID, adj.amount, 0
from dbo.tr_transactions as adj
inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
inner join dbo.tr_GLAccounts as glDeb on glDeb.glaccountID = adj.debitGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glDeb.glcode = 'ACCOUNTSRECEIVABLE'
and adj.statusID = 1

insert into @tblTrans
select distinct adj.transactionID, adj.debitGLAccountID, 0, adj.amount * -1
from dbo.tr_transactions as adj
inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
inner join dbo.tr_GLAccounts as glCred on glCred.glaccountID = adj.creditGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glCred.glcode = 'ACCOUNTSRECEIVABLE'
and adj.statusID = 1

insert into @tblTrans
select distinct adj.transactionID, adj.creditGLAccountID, adj.amount * -1, 0
from dbo.tr_transactions as adj
inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
inner join dbo.tr_GLAccounts as glCred on glCred.glaccountID = adj.creditGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glCred.glcode = 'ACCOUNTSRECEIVABLE'
and adj.statusID = 1

insert into @tblTrans
select distinct wo.transactionID, wo.debitGLAccountID, 0, wo.amount * -1
from dbo.tr_transactions as wo
inner join dbo.tr_relationships as rInner on rInner.transactionID = wo.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'WriteOffSaleTrans'
inner join dbo.tr_GLAccounts as glCred on glCred.glaccountID = wo.creditGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glCred.glcode = 'ACCOUNTSRECEIVABLE'
and wo.statusID = 1

insert into @tblTrans
select distinct wo.transactionID, wo.creditGLAccountID, wo.amount * -1, 0
from dbo.tr_transactions as wo
inner join dbo.tr_relationships as rInner on rInner.transactionID = wo.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'WriteOffSaleTrans'
inner join dbo.tr_GLAccounts as glCred on glCred.glaccountID = wo.creditGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glCred.glcode = 'ACCOUNTSRECEIVABLE'
and wo.statusID = 1

insert into @tblTrans
select distinct dit.transactionID, dit.debitGLAccountID, 0, dit.amount * -1
from dbo.tr_transactions as dit
inner join dbo.tr_relationships as rInner on rInner.transactionID = dit.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'DITSaleTrans'
inner join dbo.tr_GLAccounts as glCred on glCred.glaccountID = dit.creditGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glCred.parentGLAccountID = @deferredTaxGL
and dit.statusID = 1

insert into @tblTrans
select distinct dit.transactionID, dit.creditGLAccountID, 0, dit.amount
from dbo.tr_transactions as dit
inner join dbo.tr_relationships as rInner on rInner.transactionID = dit.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'DITSaleTrans'
inner join dbo.tr_GLAccounts as glCred on glCred.glaccountID = dit.creditGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glCred.parentGLAccountID = @deferredTaxGL
and dit.statusID = 1

insert into @tblTrans
select distinct dit.transactionID, dit.creditGLAccountID, 0, dit.amount
from dbo.tr_transactions as dit
inner join dbo.tr_relationships as rInner on rInner.transactionID = dit.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'DITSaleTrans'
inner join dbo.tr_GLAccounts as glDeb on glDeb.glaccountID = dit.debitGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glDeb.parentGLAccountID = @deferredTaxGL
and dit.statusID = 1

insert into @tblTrans
select distinct dit.transactionID, dit.debitGLAccountID, 0, dit.amount * -1
from dbo.tr_transactions as dit
inner join dbo.tr_relationships as rInner on rInner.transactionID = dit.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'DITSaleTrans'
inner join dbo.tr_GLAccounts as glDeb on glDeb.glaccountID = dit.debitGLAccountID
inner join @tblTrans as tbl on tbl.transactionID = rInner.appliedToTransactionID
where glDeb.parentGLAccountID = @deferredTaxGL
and dit.statusID = 1

select summeddata.debitAmount, summeddata.creditAmount, gl.thePathExpanded + isnull(' (' + nullIf(gl.accountCode,'') + ')','') as glexpanded
from (
	select glAccountID, sum(debitAmount) as debitAmount, sum(creditAmount) as creditAmount
	from @tblTrans
	group by glAccountID
	having sum(debitAmount) <> 0 OR sum(creditAmount) <> 0
) as summeddata
inner join @allGLS as gl on gl.GLAccountID = summeddata.GLAccountID
order by 3


-- deferred schedule
select dit.recognitionDate, sum(t.amount) as recogAmt
from dbo.tr_transactions as t
inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
inner join (
	select @transactionID as transactionID
		union
	select adj.transactionID
	from dbo.tr_transactions as adj
	inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
	inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
	where adj.statusID = 1
) as tbl on tbl.transactionID = r.appliedToTransactionID
inner join dbo.tr_transactionDIT as dit on dit.transactionID = t.transactionID
where t.statusID = 1
group by dit.recognitionDate
order by dit.recognitionDate

-- sale transaction
select top 1 tSale.transactionID, tt.type, tSale.amount, tSale.detail,
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	glCred.thePathExpanded + isnull(' (' + nullIf(glCred.accountCode,'') + ')','') as creditGL
from dbo.tr_transactions as tSale
inner join dbo.tr_types as tt on tt.typeID = tSale.typeID
inner join dbo.tr_relationships as rTax on rTax.appliedToTransactionID = tSale.transactionID
inner join dbo.tr_relationshipTypes as rtTax on rtTax.typeID = rTax.typeID and rtTax.type = 'SalesTaxTrans'
inner join dbo.ams_members as mAss on mAss.memberid = tSale.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glCred on glCred.GLAccountID = tSale.creditGLAccountID
where rTax.transactionID = @transactionID

-- related transactions
EXEC dbo.tr_viewTransaction_related_recursive @transactionID

RETURN 0
GO

CREATE PROC [dbo].[tr_viewTransaction_dit]
@transactionID int

AS

set nocount on

declare @orgID int, @deferredRevenueGL int, @deferredTaxGL int
select @orgID = ownedByOrgID from dbo.tr_transactions where transactionID = @transactionID
select @deferredRevenueGL = GLAccountID from dbo.tr_glaccounts where orgID = @orgID and GLCode = 'DEFERREDREVENUE' and status = 'A'
select @deferredTaxGL = GLAccountID from dbo.tr_glaccounts where orgID = @orgID and GLCode = 'DEFERREDTAX' and status = 'A'

declare @allGLs TABLE (GLAccountID int, parentGLAccountID int, thePathExpanded varchar(max), accountCode varchar(200), glCode varchar(30))
insert into @allGLS
select rgl.GLAccountID, rgl.parentGLAccountID, rgl.thePathExpanded, rgl.accountCode, rgl.glCode
from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl

-- transaction info
select TOP 1 t.transactionid, t.ownedByOrgID, t.recordedOnSiteID, t.amount, tt.type, 
	case 
	when glCred.parentGLAccountID = @deferredRevenueGL then 'Transfer to Deferred Revenue' 
	when glCred.parentGLAccountID = @deferredTaxGL then 'Transfer to Deferred Sales Tax' 
	when glDeb.parentGLAccountID = @deferredRevenueGL then 'Transfer to Recognized Revenue' 
	when glDeb.parentGLAccountID = @deferredTaxGL then 'Transfer to Recognized Sales Tax' 
	else '' end as detail,
	t.transactionDate, t.dateRecorded, ts.status, mAss2.memberID as assignedTomemberID, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullif(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as recordedByMember,
	m2.company as recordedByMemberCompany,
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL,
	glCred.thePathExpanded + isnull(' (' + nullIf(glCred.accountCode,'') + ')','') as creditGL
from dbo.tr_transactions as t
inner join dbo.tr_types as tt on tt.typeID = t.typeID
inner join dbo.tr_statuses as ts on ts.statusID = t.statusID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join dbo.ams_members as m on m.memberid = t.recordedByMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
inner join @allGLS as glDeb on glDeb.GLAccountID = t.debitGLAccountID
inner join @allGLS as glCred on glCred.GLAccountID = t.creditGLAccountID
where t.transactionID = @transactionID

-- sale/tax/adj transaction
select top 1 tSale.transactionID, tt.type, tSale.amount, tSale.detail,
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	glCred.thePathExpanded + isnull(' (' + nullIf(glCred.accountCode,'') + ')','') as creditGL
from dbo.tr_transactions as tSale
inner join dbo.tr_types as tt on tt.typeID = tSale.typeID
inner join dbo.tr_relationships as rDit on rDit.appliedToTransactionID = tSale.transactionID
inner join dbo.tr_relationshipTypes as rtDit on rtDit.typeID = rDit.typeID and rtDit.type = 'DITSaleTrans'
inner join dbo.ams_members as mAss on mAss.memberid = tSale.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glCred on glCred.GLAccountID = tSale.creditGLAccountID
where rDit.transactionID = @transactionID

-- related transactions
EXEC dbo.tr_viewTransaction_related_recursive @transactionID

RETURN 0
GO

