-- ran in less than 10 secs on dev


ALTER TABLE dbo.organizations ADD
	publicGroupPrintID int NULL
GO
ALTER TABLE dbo.organizations ADD CONSTRAINT
	FK_organizations_cache_perms_groupPrints FOREIGN KEY
	(
	publicGroupPrintID
	) REFERENCES dbo.cache_perms_groupPrints
	(
	groupPrintID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER PROC [dbo].[cache_perms_deleteUnusedPrints]
AS

DECLARE @groupPrintsToDelete TABLE (autoid int IDENTITY(1,1), groupPrintID int);
DECLARE @rightPrintsToDelete TABLE (autoid int IDENTITY(1,1), rightPrintID int);

-- find rightPrints in need of deletion
insert into @rightPrintsToDelete (rightPrintID)
select rp.rightPrintID
from dbo.cache_perms_rightPrints rp
left outer join dbo.cache_perms_siteResourceFunctionRightPrints srfrp
	on srfrp.rightPrintID = rp.rightPrintID
where srfrp.siteResourceFunctionRightPrintID is null

-- find groupPrints in need of deletion
insert into @groupPrintsToDelete (groupPrintID)
select gp.groupPrintID
from dbo.cache_perms_groupPrints gp
left outer join ams_members m
	on m.groupPrintID = gp.groupPrintID
where m.memberID is null
except
select publicGroupPrintID as groupPrintID
from organizations


-- delete dbo.cache_perms_groupPrintsRightPrints no longer needed
delete gprp
from dbo.cache_perms_groupPrintsRightPrints gprp
inner join @rightPrintsToDelete rpd on rpd.rightPrintID = gprp.rightPrintID

delete gprp
from dbo.cache_perms_groupPrintsRightPrints gprp
inner join @groupPrintsToDelete gpd on gpd.groupPrintID = gprp.groupPrintID


-- delete dbo.cache_perms_rightPrintsAndGroups entries in need of deletion
delete rpg
from dbo.cache_perms_rightPrintsAndGroups rpg
inner join @rightPrintsToDelete rpd on rpd.rightPrintID = rpg.rightPrintID


-- delete dbo.cache_perms_rightPrints entries in need of deletion
delete rp
from dbo.cache_perms_rightPrints rp
inner join @rightPrintsToDelete rpd on rpd.rightPrintID = rp.rightPrintID

-- delete the cache_perms_groupPrintsAndGroups entries
delete gpg
from dbo.cache_perms_groupPrintsAndGroups gpg
inner join @groupPrintsToDelete gpd on gpd.groupPrintID = gpg.groupPrintID

-- delete dbo.cache_perms_groupPrints entries in need of deletion
delete gp
from dbo.cache_perms_groupPrints gp
inner join @groupPrintsToDelete gpd on gpd.groupPrintID = gp.groupPrintID

RETURN 0
GO


drop PROCEDURE dbo.cache_perms_updateSiteResourceFunctionRightPrints
GO
drop PROCEDURE dbo.cache_perms_updateGroupPrints
GO
ALTER PROC [dbo].[ams_createDefaultGroups]
@orgID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

    DECLARE @rc int, @groupID int, @publicGroupID int, @publicGroupPrintID int

    IF OBJECT_ID('tempdb..#groupPrintsToProcess') IS NOT NULL
	   DROP TABLE #groupPrintsToProcess
    CREATE TABLE #groupPrintsToProcess (groupPrintID int)

    EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode='Public', @groupName='Public', @groupDesc='Visitors that are not logged in', @isSystemGroup=1, @allowManualAssignment=0, @parentGroupID=null, @hideOnGroupLists=0, @groupID=@publicGroupID OUTPUT
    EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode='Guests', @groupName='Guests', @groupDesc='Registered guest users', @isSystemGroup=1, @allowManualAssignment=0, @parentGroupID=null, @hideOnGroupLists=0, @groupID=@groupID OUTPUT
    EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode='Users', @groupName='Users', @groupDesc='Approved site users', @isSystemGroup=1, @allowManualAssignment=0, @parentGroupID=null, @hideOnGroupLists=0, @groupID=@groupID OUTPUT
    EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode='SiteAdmins', @groupName='Site Administrators', @groupDesc='Full Site Administrators', @isSystemGroup=1, @allowManualAssignment=1, @parentGroupID=null, @hideOnGroupLists=0, @groupID=@groupID OUTPUT

    EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode='ClientAdmins', @groupName='Client Administrators', @groupDesc='Client Administrators', @isSystemGroup=1, @allowManualAssignment=1, @parentGroupID=null, @hideOnGroupLists=0, @groupID=@groupID OUTPUT
    UPDATE dbo.ams_groups set isProtected = 1 where groupID = @groupID


    -- create publicGroupPrint for org
    insert into cache_perms_groupPrints(orgID, groupList,isprocessed) values (@orgID,cast(@publicGroupID as varchar(10)),0)
    select @publicGroupPrintID = SCOPE_IDENTITY() 

    insert into cache_perms_groupPrintsandGroups (groupPrintID, groupID) values (@publicGroupPrintID,@publicGroupID )


    update organizations set
    publicGroupPrintID = @publicGroupPrintID
    where orgID = @orgID

    insert into #groupPrintsToProcess (groupPrintID) values (@publicGroupPrintID)
    exec dbo.cache_perms_processGroupPrintsBulk @orgid=@orgid

    IF OBJECT_ID('tempdb..#groupPrintsToProcess') IS NOT NULL
       DROP TABLE #groupPrintsToProcess

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	SELECT @groupID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

-- ran in 8 secs on dev
declare @thisOrgID int, @thisGroupPrintID int
declare @results TABLE (groupPrintID int, orgID int, groupID int)

IF OBJECT_ID('tempdb..#groupPrintsToProcess') IS NOT NULL
	DROP TABLE #groupPrintsToProcess
CREATE TABLE #groupPrintsToProcess (groupPrintID int)

insert into cache_perms_groupPrints(orgID, groupList,isprocessed)
OUTPUT inserted.groupPrintID, inserted.orgID,  cast(inserted.groupList as int) as groupID
into @results
select orgID, cast(groupID as varchar(5)) as groupList, 0 as isProcessed
from ams_groups g
where g.groupcode = 'public' and g.isSystemGroup = 1

insert into cache_perms_groupPrintsandGroups (groupPrintID, groupID)
select groupPrintID, groupID
from @results

update o set
    publicGroupPrintID = r.groupPrintID
from organizations o
inner join @results r
    on r.orgID = o.orgID

select @thisOrgID = min(orgID) from @results
while @thisOrgID is not null
BEGIN
    select @thisGroupPrintID = groupPrintID 
    from @results
    where orgID = @thisOrgID

    insert into #groupPrintsToProcess (groupPrintID) values (@thisGroupPrintID)
    print 'Running dbo.cache_perms_processGroupPrintsBulk @orgID=' + cast(@thisOrgID as varchar(10)) + ' for groupPrintID: ' + cast(@thisGroupPrintID as varchar(10))

    exec dbo.cache_perms_processGroupPrintsBulk @orgid=@thisOrgID

    truncate table #groupPrintsToProcess
    select @thisOrgID = min(orgID) from @results where orgID > @thisOrgID
END

IF OBJECT_ID('tempdb..#groupPrintsToProcess') IS NOT NULL
	DROP TABLE #groupPrintsToProcess
GO