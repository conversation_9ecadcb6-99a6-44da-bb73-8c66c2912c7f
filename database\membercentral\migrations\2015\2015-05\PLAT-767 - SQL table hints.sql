use membercentral
GO
ALTER FUNCTION [dbo].[fn_checkResourceRights] (
	@resourceid int,
	@functionID int,
	@memberID int,
	@siteID int
)
RETURNS bit
AS
BEGIN

	DECLARE @allowAccess bit
	DECLARE @activeMemberID int
	declare @siteSiteResourceID int, @resourceTypeID int

	SELECT @allowAccess = 0
	select @activeMemberID = activeMemberID from ams_members where memberID = @memberID 
	select @activeMemberID = isnull(@activeMemberID,0)

	-- if superuser and not on MC site, return TRUE
	IF EXISTS (
		SELECT mnp.mnpID
		FROM dbo.ams_memberNetworkProfiles AS mnp WITH(NOLOCK)
		INNER JOIN dbo.ams_networkProfiles AS np WITH(NOLOCK) ON mnp.profileID = np.profileID 
		INNER JOIN dbo.ams_members AS m WITH(NOLOCK) ON mnp.memberID = m.memberID and m.memberID = m.activeMemberID
		WHERE m.memberID = @activeMemberID 
		AND np.networkID = 1
		AND mnp.status = 'A'
		AND np.status = 'A'
		AND m.status = 'A'
	) AND NOT EXISTS (SELECT siteID from sites where siteID = @siteID and sitecode='MC')
		SELECT @allowAccess = 1

	-- if siteadmin for site, return TRUE
	IF @allowAccess = 0 BEGIN
		IF EXISTS (
			select memberID
			from dbo.cache_members_groups as mg
			inner join dbo.ams_groups as g on g.groupID = mg.groupID
			where mg.memberID = @activeMemberID
			AND g.groupName = 'Site Administrators'
			AND g.status = 'A'
			and g.isSystemGroup = 1
		)
		SELECT @allowAccess = 1
	END

	-- else do normal check
	IF @allowAccess = 0 BEGIN
		
		select @siteSiteResourceID=s.siteResourceID, @resourceTypeID=sr.resourceTypeID
		from cms_siteResources sr
		inner join sites s on s.siteID = sr.siteID
		and sr.siteResourceID = @resourceid

		SELECT @allowAccess =
			CASE 
				WHEN EXISTS (
						select cachedRightsID
						from dbo.cms_siteResourceRightsCache srrc
						inner join dbo.cache_members_groups mg
							on srrc.groupID = mg.groupID
							and mg.memberID = @activeMemberID
							and srrc.resourceID = @resourceID
							and srrc.functionID = @functionID
							and srrc.include = 1
						union
						--universal roles
						select cachedRightsID
						from dbo.cms_siteResourceRightsCache srrc
						inner join dbo.cache_members_groups mg
							on srrc.groupID = mg.groupID
							and mg.memberID = @activeMemberID
							and srrc.resourceID = @siteSiteResourceID
							and srrc.universalRoleResourceTypeID = @resourceTypeID
							and srrc.functionID = @functionID
							and srrc.include = 1

					) and not exists (
						select cachedRightsID
						from dbo.cms_siteResourceRightsCache srrc
						inner join dbo.cache_members_groups mg
							on srrc.groupID = mg.groupID
							and mg.memberID = @activeMemberID
							and srrc.resourceID = @resourceID
							and srrc.functionID = @functionID
							and srrc.include = 0

						union
						--universal roles
						select cachedRightsID
						from dbo.cms_siteResourceRightsCache srrc
						inner join dbo.cache_members_groups mg
							on srrc.groupID = mg.groupID
							and mg.memberID = @activeMemberID
							and srrc.resourceID = @siteSiteResourceID
							and srrc.universalRoleResourceTypeID = @resourceTypeID
							and srrc.functionID = @functionID
							and srrc.include = 0
					)
					THEN 1
			ELSE 0
			END
	END

	RETURN @allowAccess
END
GO

ALTER FUNCTION [dbo].[fn_IsSuperUser] (@memberID int)
RETURNS bit
AS
BEGIN

	DECLARE @isSuperUser bit

	IF EXISTS (
		SELECT mnp.mnpID
		FROM dbo.ams_memberNetworkProfiles AS mnp WITH(NOLOCK)
		INNER JOIN dbo.ams_networkProfiles AS np WITH(NOLOCK) ON mnp.profileID = np.profileID 
		INNER JOIN dbo.ams_members AS m WITH(NOLOCK) ON mnp.memberID = m.memberID and m.memberID = m.activeMemberID
		WHERE m.memberID = @memberID 
		AND np.networkID = 1
		AND mnp.status = 'A'
		AND np.status = 'A'
		AND m.status = 'A'
	) SELECT @isSuperUser = 1

	ELSE 
		SELECT @isSuperUser = 0

	RETURN @isSuperUser
END
GO

ALTER PROCEDURE [dbo].[cms_getPageStructureByID] 
@siteID int,
@pageID int,
@languageID int,
@ovrMode varchar(30),
@processMobileOverrides bit,
@templateTypeName varchar(100)

AS


DECLARE @templateID int, @sectionID int, @pgResourceTypeID int, @modeID int, @ovrModeID int, @defaultTemplateID int, @defaultModeID int, @rootSectionID int, @pageIncludePlacements int, @siteResourceStatusID int
DECLARE @viewFunctionID int, @editFunctionID int
DECLARE @layoutInfo TABLE (templateID int, templateTypeName varchar(100), modeID int, modeName varchar(100), templateFilename varchar(100), sitecode varchar(10), orgcode varchar(10))
DECLARE @pageSectionPath TABLE (templateID int, modeID int, sectionID int, parentSectionID int, dataLevel int PRIMARY KEY,includePlacements bit)
DECLARE @zoneAssignments TABLE (autoid int IDENTITY(1,1), zoneID int, assignmentLevel varchar(10), sectionID int, siteResourceID int PRIMARY KEY, sortOrder int, dataLevel int, isSSL bit, appInstanceID int, appInstanceName varchar(100), appTypeName varchar(100), appWidgetInstanceID int, appInstanceResourceID int, appWidgetTypeName varchar(100), appWidgetInstanceName varchar(100),contentID int,enableSocialMediaSharing bit, resourceTypeID int, resourceType varchar(100), resourceTypeClassName varchar(100), objectType char(1), viewRightPrintID int, editContentRightPrintID int)

set @defaultTemplateID = 1
set @defaultModeID = 1

select @ovrModeID = modeID from cms_pageModes where modeName = @ovrMode
select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

select @viewFunctionID = functionID from cms_siteResourceFunctions where functionName = 'view' 
select @editFunctionID = functionID from cms_siteResourceFunctions where functionName = 'editContent' 

select 
	@pgResourceTypeID = sr.resourceTypeID, 
	@sectionID = p.sectionID, 
	@modeID = pm.modeID, 
	@templateID = isnull(mobilept.templateID,pt.templateid),
	@pageIncludePlacements = p.inheritPlacements
from cms_pages p WITH(nolock)
inner join sites s
	on s.siteID = p.siteID
inner join cms_siteResources sr WITH(nolock)
	on p.siteResourceID = sr.siteResourceID
	and pageID = @pageID
     and sr.siteResourceStatusID = @siteResourceStatusID
inner join dbo.cache_cms_derivedPageSectionSettings dpss WITH(nolock)
	on dpss.sectionID = p.sectionID
inner join dbo.cms_pageTemplates pt WITH(nolock)
	on pt.templateid = coalesce(p.ovTemplateID,dpss.ovTemplateID,@defaultTemplateID)
inner join dbo.cms_pageModes pm WITH(nolock)
	on pm.modeID = coalesce(@ovrModeID,p.ovModeID,dpss.ovModeID,@defaultModeID)
left outer join dbo.cms_pageTemplates mobilept WITH(nolock)
	on mobilept.templateid = coalesce(p.ovTemplateIDMobile,dpss.ovTemplateIDMobile)
	and mobilept.templateTypeID = pt.templateTypeID
	and @processMobileOverrides = 1


select @rootSectionID = sectionID from cms_pageSections where siteID = @siteID and parentSectionID is null

if (nullif(@ovrMode,'') is not null)
	select @ovrModeID = modeID from cms_pageModes where modeName = @ovrMode

-- find all zone assignments for page, not considering pageMode
insert into @zoneAssignments (zoneid, siteResourceID, assignmentLevel, sectionID, sortOrder, dataLevel,resourceTypeID,	resourceType, resourceTypeClassName,isSSL,contentID,enableSocialMediaSharing,objecttype)
select pzr.zoneid, pzr.siteResourceID, 'page' as assignmentLevel, 0 as sectionID, pzr.sortOrder, 0 as dataLevel, srt.resourceTypeID,srt.resourceType,srtc.resourceTypeClassName, isnull(c.isSSL,1),c.contentID, c.enableSocialMediaSharing,
objecttype = case
	when c.siteresourceID is not null then 'C'
	when ai.siteresourceID is not null then 'A'
	when awi.siteresourceID is not null then 'W'
end

from dbo.cms_pageZonesResources as pzr
inner join dbo.cms_siteResources sr
	on pzr.siteResourceID = sr.siteResourceID
	and pzr.pageID = @pageID
     and sr.siteResourceStatusID = @siteResourceStatusID
inner join dbo.cms_siteResourceTypes srt ON sr.resourceTypeID = srt.resourceTypeID
inner join dbo.cms_siteResourceTypeClasses srtc ON srt.resourceTypeClassID = srtc.resourceTypeClassID
left outer join dbo.cms_content c on sr.siteResourceID = c.siteResourceID 
left outer join dbo.cms_applicationInstances ai on sr.siteResourceID = ai.siteResourceID
left outer join dbo.cms_applicationWidgetInstances awi on sr.siteResourceID = awi.siteResourceID


if @pageIncludePlacements = 1
BEGIN
	insert into @zoneAssignments (zoneid, siteResourceID, assignmentLevel, sectionID, sortOrder, dataLevel,resourceTypeID,	resourceType, resourceTypeClassName,isSSL,contentID,enableSocialMediaSharing,objecttype)
	select pszr.zoneid, pszr.siteResourceID, 
		assignmentLevel = case when rps.sectionID = @rootSectionID then 'site' else 'section' end,
		rps.sectionID, pszr.sortOrder, (rps.depth * -1) as dataLevel, srt.resourceTypeID,	srt.resourceType, srtc.resourceTypeClassName, isnull(c.isSSL,1),c.contentID, c.enableSocialMediaSharing,
		objecttype = case
			when c.siteresourceID is not null then 'C'
			when ai.siteresourceID is not null then 'A'
			when awi.siteresourceID is not null then 'W'
		end
			
	from dbo.cms_pageSectionsZonesResources pszr
	inner join dbo.cache_cms_recursivePageSections rps
		on rps.startSectionID = @sectionID
		and rps.sectionid = pszr.sectionid
		and rps.includePlacements = 1
	inner join dbo.cms_siteResources sr
		on pszr.siteResourceID = sr.siteResourceID
		  and sr.siteResourceStatusID = @siteResourceStatusID
	inner join dbo.cms_siteResourceTypes srt ON sr.resourceTypeID = srt.resourceTypeID
	inner join dbo.cms_siteResourceTypeClasses srtc ON srt.resourceTypeClassID = srtc.resourceTypeClassID
	left outer join dbo.cms_content c on sr.siteResourceID = c.siteResourceID 
	left outer join dbo.cms_applicationInstances ai on sr.siteResourceID = ai.siteResourceID
	left outer join dbo.cms_applicationWidgetInstances awi on sr.siteResourceID = awi.siteResourceID
    where pszr.siteResourceID not in (select siteResourceID from @zoneAssignments)
END

-- update view and editContentPerms
update za set
    viewRightPrintID = srfrp_view.rightPrintID, 
    editContentRightPrintID = srfrp_editContent.rightPrintID
from @zoneAssignments za
left outer join cache_perms_siteResourceFunctionRightPrints srfrp_view
    on srfrp_view.siteResourceID = za.siteResourceID and srfrp_view.functionID= @viewFunctionID
left outer join cache_perms_siteResourceFunctionRightPrints srfrp_editContent
    on srfrp_editContent.siteResourceID = za.siteResourceID and srfrp_editContent.functionID= @editFunctionID


-- update Application related columns
if exists (select top 1 siteresourceID from @zoneAssignments where objecttype = 'A')
	update za
	set
		appInstanceID = ai.applicationInstanceID,
		appInstanceName = ai.applicationInstanceName, 
		appTypeName = at.applicationTypeName, 
		appInstanceResourceID = ai.siteResourceID,
		isSSL = 1
	from @zoneAssignments za
	inner join dbo.cms_applicationInstances ai
		on za.siteResourceID = ai.siteResourceID
		and za.objecttype = 'A'
	inner join dbo.cms_applicationTypes at ON ai.applicationTypeID = at.applicationTypeID

--update Application Widget related columns
if exists (select top 1 siteresourceID from @zoneAssignments where objecttype = 'W')
	update za
	set
		appInstanceID = ai.applicationInstanceID,
		appInstanceName = ai.applicationInstanceName, 
		appTypeName = at.applicationTypeName, 
		appInstanceResourceID = ai.siteResourceID, 
		appWidgetInstanceID = awi.applicationWidgetInstanceID , 
		appWidgetTypeName = awt.applicationWidgetTypeName, 
		appWidgetInstanceName = awi.applicationWidgetInstanceName,
		isSSL = 1
	from @zoneAssignments za
	inner join dbo.cms_applicationWidgetInstances awi
		on za.siteResourceID = awi.siteResourceID
		and za.objecttype = 'W'
	inner join dbo.cms_applicationInstances ai ON awi.applicationInstanceID = ai.applicationInstanceID
	inner join dbo.cms_applicationWidgetTypes awt ON awi.applicationWidgetTypeID = awt.applicationWidgetTypeID
	inner join dbo.cms_applicationTypes at ON ai.applicationTypeID = at.applicationTypeID



-- is page offered in requested page language? if not, use site default language
IF NOT EXISTS (select pageLanguageID from dbo.cms_pageLanguages where pageID = @pageID and languageID = @languageID)
	SELECT @languageID = defaultLanguageID from sites where siteID = @siteID
select cast(isNull((
select page.pageID, page.pageName, page.allowReturnAfterLogin, page.siteResourceID as pageSiteResourceID,
	isnull(sites.siteID,0) as siteID,
	isnull(sites.siteCode,0) as siteCode,
	isnull(organizations.orgID,0) as orgID,
	isnull(organizations.orgCode,0) as orgCode,
	ISNULL(ps.sectionID,'') as sectionID, 
	ISNULL(ps.sectionName,'') as sectionName, 
	ISNULL(ps.sectionCode,'') as sectionCode,
	@languageID as pageLanguageID,
	ISNULL(pl.pageTitle,'') as pageTitle, 
	ISNULL(pl.pageDesc,'') as pageDesc, 
	ISNULL(pl.keywords,'') as keywords, 
	ISNULL(pt.templateFileName,'DefaultTemplate') as layoutFileName,
	ISNULL(pt.templateID,'') as templateID,
	ISNULL(pt.siteResourceID,'') as templateSiteResourceID,
	ISNULL(ptt.templateTypeName,'') as templateTypeName,
	ISNULL(pm.modeName,'Normal') as layoutMode, 
	ISNULL(templateSite.sitecode,'') as layoutSiteCode,
	ISNULL(templateOrg.orgcode,'') as layoutOrgCode, 
	pageZone.zoneName, 
	siteResource.siteResourceID,
	ISNULL(sites.siteID,0) as siteID,
	ISNULL(sites.siteCode,'') as siteCode,
	ISNULL(organizations.orgID,0) as orgID,
	ISNULL(organizations.orgCode,'') as orgCode,
	ISNULL(siteResource.resourceType,'') as resourceType,
	ISNULL(siteResource.resourceTypeClassName,'') as resourceClass,
	ISNULL(siteResource.assignmentLevel,'') as assignmentLevel,
	ISNULL(siteResource.sectionID,'') as assignmentSectionID,
	ISNULL(siteResource.isSSL,'') as isSSL, 
	ISNULL(siteResource.appInstanceID,'') as appInstanceID, 
	ISNULL(siteResource.appInstanceName,'') as appInstanceName, 
	ISNULL(siteResource.appTypeName,'') as appTypeName, 
	ISNULL(siteResource.appWidgetInstanceID,'') as appWidgetInstanceID, 
	ISNULL(siteResource.appInstanceResourceID,'') as appInstanceResourceID, 
	ISNULL(siteResource.appWidgetTypeName,'') as appWidgetTypeName, 
	ISNULL(siteResource.appWidgetInstanceName,'') as appWidgetInstanceName,
	ISNULL(siteResource.contentID,'') as contentID,
	ISNULL(siteResource.enableSocialMediaSharing,'') as enableSocialMediaSharing,
    ISNULL(siteResource.viewRightPrintID,'') as viewRightPrintID,
    ISNULL(siteResource.editContentRightPrintID,'') as editContentRightPrintID
from dbo.cms_pages as page WITH(nolock)
inner join dbo.sites WITH(nolock)
	on sites.siteID = page.siteID
	and page.pageID = @pageID
inner join dbo.organizations WITH(nolock)
	on sites.orgID = organizations.orgID
inner join dbo.cms_pageSections as ps WITH(nolock)
	on ps.sectionID = page.sectionID
inner join dbo.cms_pageTemplates pt WITH(nolock)
	on pt.templateid = @templateID
inner join dbo.cms_pageTemplateTypes ptt WITH(nolock)
	on pt.templateTypeID = ptt.templateTypeID
	and ptt.templateTypeName = @templateTypeName
inner join dbo.cms_pageModes pm WITH(nolock)
	on pm.modeID = @modeID
left outer join dbo.sites templateSite WITH(nolock)
	inner join dbo.organizations templateOrg WITH(nolock)
		on templateSite.orgid = templateOrg.orgid
on templateSite.siteid = pt.siteid
inner join dbo.cms_pageLanguages as pl WITH(nolock) 
	on pl.pageID = page.pageid and pl.languageid = @languageID
inner join dbo.cms_pageTemplatesModesZones as ptmz WITH(nolock)
	on pm.modeid = ptmz.modeid and ptmz.templateID = pt.templateID
inner join dbo.cms_pageZones as pageZone WITH(nolock)
	on pageZone.zoneid = ptmz.zoneid
left outer join @zoneAssignments as siteResource
	on ptmz.zoneID = siteResource.zoneid
order by siteResource.zoneid, siteResource.dataLevel desc, siteResource.sortOrder
for xml auto, root('pageStructure')
),'<pageStructure />') as xml) as pageStructureXML
OPTION(RECOMPILE)


RETURN 0
GO

use platformStats
GO
ALTER PROCEDURE [dbo].[up_pageStats]
	@siteID int = 0,
	@orgID int = 0,
	@pageName varchar(100) = '',
	@statsStart smalldatetime = getdate,
	@statsEnd smalldatetime = getdate

AS

SET NOCOUNT ON

IF OBJECT_ID('tempdb..#statSessions') IS NOT NULL 
	DROP TABLE #statSessions
create table #statSessions (sessionid int PRIMARY KEY, memberid int)

IF OBJECT_ID('tempdb..#statsPageHits') IS NOT NULL 
	DROP TABLE #statsPageHits
create table #statsPageHits (pageHitID int PRIMARY KEY, sessionid int, pageID int, refererpageid int)



-- declare vars
DECLARE @pageid int, @pagecreated datetime, @pageModified datetime, @resourceid int
DECLARE @tmpStats TABLE (
	pageID int,
	pagecreated datetime,
	pagemodified datetime,
	resourceid int,
	numRegisteredMembers int,	
	numTotalMembers int,	
	memberGrossViewsAllowed int,
	guestGrossViewsAllowed int,
	totalGrossViewsAllowed int,
	memberGrossViewsDenied int,
	guestGrossViewsDenied int,
	totalGrossViewsDenied int,
	memberSessions int,
	guestSessions int,
	totalSessions int,
	memberPageViewsPerSession int,
	guestPageViewsPerSession int,
	totalPageViewsPerSession int,
	membersVisited int,
	membersVisitedPctRegistered int,
	membersVisitedPctTotal int
)

-- reset start date to 00:00 of startdate
-- reset end date to 23:59 of enddate (actually 0:00 of next day)
SELECT @statsStart = DATEADD(dd, DATEDIFF(dd,0,@statsStart), 0)
SELECT @statsEnd = DATEADD(dd, DATEDIFF(dd,0,@statsEnd)+1, 0)

-- get pageid
select @pageid = pageid, @resourceid = siteResourceid
from membercentral.dbo.cms_pages with (NOLOCK)
where siteID = @siteID
and pagename = @pageName

INSERT INTO @tmpStats (pageID, resourceid)
VALUES (@pageid, @resourceid)


insert into #statsPageHits (pageHitID,sessionid ,pageID, refererpageid)
select sph.pageHitID,ss.sessionid ,sph.pageID, sph.refererpageid
from dbo.statsSessions ss WITH(nolock)
inner join dbo.statsPageHits sph WITH(nolock)
	on sph.sessionID = ss.sessionID
     and sph.pageID = @pageID
	and sph.dateentered between @statsStart and @statsEnd
option(recompile)

CREATE INDEX IX_statspageHits on #statsPageHits (sessionid asc, pageID asc) include (refererPageid)


insert into #statSessions (sessionid, memberid)
select distinct ss.sessionid, ss.memberid
from #statsPageHits tmp
inner join dbo.statsSessions ss with (NOLOCK)
    on ss.sessionID = tmp.sessionID
where ignore = 0
option(recompile)

CREATE INDEX IX_statSessions on #statSessions (sessionid asc) INCLUDE (memberID)

-- numRegisteredMembers
UPDATE @tmpStats
set numRegisteredMembers = (SELECT COUNT(m.memberID)
	from membercentral.dbo.ams_members m  with (NOLOCK)
		INNER JOIN membercentral.dbo.ams_memberNetworkProfiles AS mnp with (NOLOCK) ON mnp.memberID = m.memberID
	where m.orgID = @orgID
	AND m.status = 'A')

-- numTotalMembers
UPDATE @tmpStats
set numTotalMembers = (SELECT COUNT(memberid)
	FROM membercentral.dbo.ams_members with (NOLOCK)
	WHERE orgID = @orgID
	and status = 'A')



-- memberGrossViewsAllowed
UPDATE @tmpStats
set memberGrossViewsAllowed = (select COUNT(sph.pageHitID)
	FROM #statsPageHits as sph with (NOLOCK) 
	INNER JOIN #statSessions as ss with (NOLOCK) ON sph.sessionid = ss.sessionid
	WHERE ss.memberid IS NOT NULL)

-- guestGrossViewsAllowed
UPDATE @tmpStats
set guestGrossViewsAllowed = (select COUNT(sph.pageHitID)
	FROM #statsPageHits as sph  with (NOLOCK)
	INNER JOIN #statSessions as ss with (NOLOCK) 
	   ON sph.sessionid = ss.sessionid
	WHERE ss.memberid IS NULL)


-- totalGrossViewsAllowed
UPDATE @tmpStats
set totalGrossViewsAllowed = isnull(memberGrossViewsAllowed,0) + isnull(guestGrossViewsAllowed,0)


-- memberSessions
UPDATE @tmpStats
set memberSessions = (select COUNT(DISTINCT sph.sessionid)
	FROM #statsPageHits AS sph  with (NOLOCK)
	INNER JOIN #statSessions AS ss with (NOLOCK) ON sph.sessionid = ss.sessionid
	WHERE ss.memberid IS NOT NULL)

-- guestSessions
UPDATE @tmpStats
set guestSessions = (select COUNT(DISTINCT sph.sessionid)
	FROM #statsPageHits AS sph  with (NOLOCK)
	INNER JOIN #statSessions AS ss with (NOLOCK) ON sph.sessionid = ss.sessionid
	WHERE ss.memberid IS NULL)

-- totalSessions
UPDATE @tmpStats
set totalSessions = isnull(memberSessions,0) + isnull(guestSessions,0)

-- memberPageViewsPerSession, guestPageViewsPerSession, totalPageViewsPerSession
UPDATE @tmpStats
set memberPageViewsPerSession = CASE
	WHEN memberGrossViewsAllowed = 0 or memberSessions = 0 then 0
	ELSE cast(memberGrossViewsAllowed / memberSessions as int)
	END,
	guestPageViewsPerSession = CASE
	WHEN guestGrossViewsAllowed = 0 or guestSessions = 0 then 0
	ELSE cast(guestGrossViewsAllowed / guestSessions as int)
	END,
	totalPageViewsPerSession = CASE
	WHEN totalGrossViewsAllowed = 0 or totalSessions = 0 then 0
	ELSE cast(totalGrossViewsAllowed / totalSessions as int)
	END

-- membersVisited
UPDATE @tmpStats
set membersVisited = (select COUNT(DISTINCT ss.memberid)
	FROM #statsPageHits AS sph  with (NOLOCK)
	INNER JOIN #statSessions AS ss with (NOLOCK) ON sph.sessionid = ss.sessionid
	INNER JOIN membercentral.dbo.ams_members as o with (NOLOCK) ON o.memberid = ss.memberid
	WHERE o.orgID = @orgID)

-- membersVisitedPctRegistered, membersVisitedPctTotal
UPDATE @tmpStats
set membersVisitedPctRegistered = CASE
	WHEN numRegisteredMembers = 0 or membersVisited = 0 then 0
	ELSE cast(membersVisited / numRegisteredMembers * 100 as int)
	END,
	membersVisitedPctTotal = CASE
	WHEN numTotalMembers = 0 or membersVisited = 0 then 0
	ELSE cast(membersVisited / numTotalMembers * 100 as int)
	END

-- return one query for the counts
SELECT * from @tmpStats


-- top referers
SELECT top 10 sph.refererPageid, count(sph.pagehitid) as numhits, p.pagename
FROM #statsPageHits as sph  with (NOLOCK) 
INNER JOIN #statSessions as ss with (NOLOCK) ON sph.sessionid = ss.sessionid
left outer join membercentral.dbo.cms_pages as p with (NOLOCK) ON p.pageid = sph.refererPageid
group by sph.refererpageid, p.pagename
order by count(sph.pagehitid) desc

-- top destinations
SELECT top 10 sph.Pageid, count(sph.pagehitid) as numhits, p.pagename
FROM dbo.statsPageHits as sph   with (NOLOCK)
INNER JOIN #statSessions as ss with (NOLOCK) ON sph.sessionid = ss.sessionid
left outer join membercentral.dbo.cms_pages as p with (NOLOCK) ON p.pageid = sph.Pageid
WHERE sph.refererpageID = @pageid
and sph.dateentered BETWEEN @statsStart and @statsEnd
group by sph.pageid, p.pagename
order by count(sph.pagehitid) desc
option(recompile)


IF OBJECT_ID('tempdb..#statSessions') IS NOT NULL 
	DROP TABLE #statSessions
IF OBJECT_ID('tempdb..#statsPageHits') IS NOT NULL 
	DROP TABLE #statsPageHits

SET NOCOUNT OFF

GO

ALTER PROCEDURE [dbo].[up_SiteStats]
@siteID int = 0,
@orgID int = 0,
@statsStart smalldatetime = getdate,
@statsEnd smalldatetime = getdate,
@onlyReturnFirst bit = '0'

AS

SET NOCOUNT ON


IF OBJECT_ID('tempdb..#statSessions') IS NOT NULL 
	DROP TABLE #statSessions
create table #statSessions (sessionid int PRIMARY KEY, memberid int)

IF OBJECT_ID('tempdb..#statsAppHits') IS NOT NULL 
	DROP TABLE #statsAppHits
create table #statsAppHits (appHitID int PRIMARY KEY, sessionid int, appSectionID int)

IF OBJECT_ID('tempdb..#statsPageHits') IS NOT NULL 
	DROP TABLE #statsPageHits
create table #statsPageHits (pageHitID int PRIMARY KEY, sessionid int, pageID int)

-- declare vars
DECLARE @numMembers int, @numtotalmembers int, @numInactiveMembers int, @grossmemberhits int, @grossmemberhitsApps int
DECLARE @grossmemberdeniedhits int, @uniquememberhits int, @membervisits int, @totalnonmembers int, @grossnonmemberhits int
DECLARE @grossnonmemberhitsapps int, @grossnonmemberdeniedhits int, @uniquenonmemberhits int

declare @minPageHitID int, @maxPageHitID int, @minAppHitID int, @maxAppHitID int

declare @appNames TABLE (appSectionID int PRIMARY KEY, appNameID int, name varchar(200))
declare @highestRankingPages TABLE (numhits int, itemID int, itemName varchar(200), isPageHit bit, isMemberHit bit)

-- reset start date to 00:00 of startdate
-- reset end date to 23:59 of enddate (actually 0:00 of next day)
SELECT @statsStart = DATEADD(dd, DATEDIFF(dd,0,@statsStart), 0)
SELECT @statsEnd = DATEADD(dd, DATEDIFF(dd,0,@statsEnd)+1, 0)

DECLARE @sessionStart smalldatetime
DECLARE @sessionEnd smalldatetime

-- 1hr padding with (NOLOCK) ON session start time just to make sure that we have the sessions for ALL
-- of the hits in the time range
select @sessionStart = dateadd(hour,-1,@statsStart)
select @sessionEnd = @statsEnd


insert into @appNames (appSectionID, appNameID, name)
select sas.appSectionID, san.appNameID, san.name
from statsAppSections sas WITH(nolock)
inner join dbo.statsAppNames san WITH(nolock)
	on san.appNameID = sas.appNameID

insert into #statSessions (sessionid, memberid)
select sessionid, memberid
from dbo.statsSessions WITH(NOLOCK)
where siteID = @siteID
and dateentered between @sessionStart and @sessionEnd
AND ignore = 0


CREATE INDEX IX_statSessions on #statSessions (sessionid asc) INCLUDE (memberID)

insert into #statsAppHits (appHitID,sessionid ,appSectionID)
select sah.appHitID,ss.sessionid ,sah.appSectionID
from #statSessions ss
inner join dbo.statsAppHits sah WITH(nolock)
	on sah.sessionID = ss.sessionID
	--and sah.dateentered between @statsStart and @statsEnd


CREATE INDEX IX_statsAppHits on #statsAppHits (sessionid asc, appSectionID asc)


insert into #statsPageHits (pageHitID,sessionid ,pageID)
select sph.pageHitID,ss.sessionid ,sph.pageID
from #statSessions ss
inner join dbo.statsPageHits sph WITH(nolock)
	on sph.sessionID = ss.sessionID
	--and sph.dateentered between @statsStart and @statsEnd

CREATE INDEX IX_statspageHits on #statsPageHits (sessionid asc, pageID asc)


-- numRegisteredMembers
SELECT @nummembers = COUNT(m.memberID)
	from membercentral.dbo.ams_members m  WITH(NOLOCK)
		INNER JOIN membercentral.dbo.ams_memberNetworkProfiles AS mnp WITH(NOLOCK) ON mnp.memberID = m.memberID
	where m.orgID = @orgID
	AND m.status = 'A'

-- numTotalMembers
SELECT @numtotalmembers = COUNT(memberid)
	FROM membercentral.dbo.ams_members WITH(NOLOCK)
	WHERE orgID = @orgID
	and status = 'A'

-- numInactiveMembers
SELECT @numInactiveMembers = COUNT(memberid)
	FROM membercentral.dbo.ams_members WITH(NOLOCK)
	WHERE orgID = @orgID
	and status = 'I'

-- grossmemberhits  NEW
SELECT @grossmemberhits = COUNT(sph.pageHitID)
	FROM #statsPageHits as sph  WITH(NOLOCK)
	INNER JOIN #statSessions as ss 
		ON sph.sessionid = ss.sessionid 
			AND ss.memberid IS NOT NULL
option (RECOMPILE)

-- grossmemberhitsApps
SELECT @grossmemberhitsApps = COUNT(sah.appHitID)
	FROM #statsAppHits as sah  WITH(NOLOCK)
	INNER JOIN #statSessions as ss 
		ON sah.sessionid = ss.sessionid
		AND ss.memberid IS NOT NULL
option (RECOMPILE)

-- grossmemberdeniedhits
SELECT @grossmemberdeniedhits = COUNT(sph.pageHitID)
	FROM #statsPageHits as sph  WITH(NOLOCK)
	INNER JOIN #statSessions as ss
	ON sph.sessionid = ss.sessionid
		AND ss.memberid IS NOT NULL
option (RECOMPILE)

-- uniquememberhits
SELECT @uniquememberhits = count(*) FROM (
	SELECT DISTINCT ss.memberid
	FROM #statsPageHits AS sph  WITH(NOLOCK)
	INNER JOIN #statSessions AS ss 
		ON sph.sessionid = ss.sessionid
		AND ss.memberid IS NOT NULL
	union
	SELECT DISTINCT ss.memberid
	FROM #statsAppHits AS sah  WITH(NOLOCK)
	INNER JOIN #statSessions AS ss 
		ON sah.sessionid = ss.sessionid
		AND ss.memberid IS NOT NULL
) as t	
option (RECOMPILE)

-- membervisits
SELECT @membervisits = count(*) FROM (
	SELECT DISTINCT sph.sessionid
	FROM #statsPageHits AS sph  WITH(NOLOCK)
	INNER JOIN #statSessions AS ss
		ON sph.sessionid = ss.sessionid
		AND ss.memberid IS NOT NULL
	union
	SELECT DISTINCT sah.sessionid
	FROM #statsAppHits AS sah  WITH(NOLOCK)
	INNER JOIN #statSessions AS ss 
		ON sah.sessionid = ss.sessionid
		AND ss.memberid IS NOT NULL
) as t	
option (RECOMPILE)

-- totalnonmembers
SELECT @totalnonmembers = count(*) FROM (
	SELECT DISTINCT sph.sessionid
	FROM #statsPageHits AS sph  WITH(NOLOCK)
	INNER JOIN #statSessions AS ss 
		ON sph.sessionid = ss.sessionid
		AND ss.memberid IS NULL
	union
	SELECT DISTINCT sah.sessionid
	FROM #statsAppHits AS sah  WITH(NOLOCK)
	INNER JOIN #statSessions AS ss 
		ON sah.sessionid = ss.sessionid
		AND ss.memberid IS NULL
) as t	
option (RECOMPILE)

-- grossnonmemberhits
SELECT @grossnonmemberhits = COUNT(sph.pageHitID)
	FROM #statsPageHits as sph  WITH(NOLOCK)
	INNER JOIN #statSessions as ss
		ON sph.sessionid = ss.sessionid
		AND ss.memberid IS NULL
option (RECOMPILE)

-- grossnonmemberhitsapps
SELECT @grossnonmemberhitsapps = COUNT(sah.appHitID)
	FROM #statsAppHits as sah  WITH(NOLOCK)
	INNER JOIN #statSessions as ss
		ON sah.sessionid = ss.sessionid
		AND ss.memberid IS NULL
option (RECOMPILE)

-- grossnonmemberdeniedhits
SELECT @grossnonmemberdeniedhits = 0


-- uniquenonmemberhits
SELECT @uniquenonmemberhits = @totalnonmembers

-- return one query for the counts
SELECT 
	@orgID as orgID,
	@numMembers as numMembers, 
	@numtotalmembers as numtotalmembers, 
	@numInactiveMembers as numInactiveMembers, 
	@grossmemberhits as grossmemberhits, 
	@grossmemberhitsApps as grossmemberhitsApps, 
	@grossmemberdeniedhits as grossmemberdeniedhits, 
	@uniquememberhits as uniquememberhits, 
	@membervisits as membervisits, 
	@totalnonmembers as totalnonmembers, 
	@grossnonmemberhits as grossnonmemberhits, 
	@grossnonmemberhitsapps as grossnonmemberhitsapps, 
	@grossnonmemberdeniedhits as grossnonmemberdeniedhits, 
	@uniquenonmemberhits as uniquenonmemberhits

if @onlyReturnFirst = '1'
	GOTO on_cleanup


-- HighestRankingPagesNonmemberHits
insert into @highestRankingPages (numhits, itemID, isPageHit,isMemberHit)
SELECT TOP 50 COUNT(sph.pageHitID) AS numhits, sph.pageid as itemID, 1 as isPageHit, 0 as isMemberHit
FROM #statsPageHits AS sph  with (NOLOCK)
INNER JOIN #statSessions AS ss 
	ON sph.sessionid = ss.sessionid 
	AND ss.memberid IS NULL
GROUP BY sph.pageid
order by numhits desc

insert into @highestRankingPages (numhits, itemName, isPageHit,isMemberHit)
SELECT TOP 50 COUNT(sah.appHitID) AS numhits, aname.name as itemName, 0 as isPageHit, 0 as isMemberHit
FROM #statSessions AS ss 
INNER JOIN #statsAppHits as sah with (NOLOCK)
	ON ss.sessionid = sah.sessionid
		AND ss.memberid IS NULL
INNER JOIN @appNames as aname ON sah.appSectionID = aname.appSectionID 
GROUP BY aname.name
ORDER BY numhits DESC



-- HighestRankingPagesMemberHits
insert into @highestRankingPages (numhits, itemID, isPageHit,isMemberHit)
SELECT TOP 50 COUNT(sph.pageHitID) AS numhits, sph.pageid as itemID, 1 as isPageHit, 1 as isMemberHit
FROM #statsPageHits AS sph  with (NOLOCK)
INNER JOIN #statSessions AS ss
	ON sph.sessionid = ss.sessionid
	AND ss.memberid IS NOT NULL
GROUP BY sph.pageid
order by numhits desc

insert into @highestRankingPages (numhits, itemName, isPageHit,isMemberHit)
SELECT TOP 50 COUNT(sah.appHitID) AS numhits, aname.name as itemName, 0 as isPageHit, 1 as isMemberHit
FROM #statSessions AS ss 
INNER JOIN #statsAppHits as sah with (NOLOCK)
	ON ss.sessionid = sah.sessionid
	AND ss.memberid IS NOT NULL
INNER JOIN @appNames as aname ON sah.appSectionID = aname.appSectionID 
GROUP BY aname.name
order by numhits desc

-- get pagenames from IDs
update tmp set
	itemName = p.pagename
from @highestRankingPages tmp
INNER JOIN membercentral.dbo.cms_pages AS p with (NOLOCK) ON p.pageId = tmp.itemID

-- output HighestRankingPagesNonmemberHits
select top 50 numhits, itemName as pageName, type = case isPageHit when 1 then 'page' else 'app' end
from @highestRankingPages tmp
where isMemberHit=0
order by numhits desc

-- output HighestRankingPagesMemberHits
select top 50 numhits, itemName as pageName, type = case isPageHit when 1 then 'page' else 'app' end
from @highestRankingPages tmp
where isMemberHit=1
order by numhits desc


on_cleanup:


IF OBJECT_ID('tempdb..#statSessions') IS NOT NULL 
	DROP TABLE #statSessions

IF OBJECT_ID('tempdb..#statsAppHits') IS NOT NULL 
	DROP TABLE #statsAppHits

IF OBJECT_ID('tempdb..#statsPageHits') IS NOT NULL 
	DROP TABLE #statsPageHits


SET NOCOUNT OFF

GO

