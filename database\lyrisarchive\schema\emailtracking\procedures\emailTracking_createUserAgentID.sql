ALTER PROC dbo.emailTracking_createUserAgentID
@useragentString VARCHAR(500),
@useragentSHA1 VARCHAR(40),
@userAgentID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- Create a table variable to store the OUTPUT result
	DECLARE @userAgentIDVar TABLE (userAgentID INT);

	BEGIN TRAN;
		MERGE INTO dbo.userAgents WITH (HOLDLOCK) AS Target
		USING (SELECT @useragentString AS useragentString, @useragentSHA1 AS useragentSHA1) AS Source
			ON Target.useragentString = Source.useragentString 
			and Target.useragentSHA1 = Source.useragentSHA1 
		WHEN MATCHED THEN
			UPDATE SET @userAgentID = Target.userAgentID
		WHEN NOT MATCHED THEN
			INSERT (useragentString,useragentSHA1) VALUES (source.useragentString,source.useragentSHA1)
			OUTPUT INSERTED.userAgentID INTO @userAgentIDVar;

		SET @userAgentID = (SELECT userAgentID FROM @userAgentIDVar);
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO
