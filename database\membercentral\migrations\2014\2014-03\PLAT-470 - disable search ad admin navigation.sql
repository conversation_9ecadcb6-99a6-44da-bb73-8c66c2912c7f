update n set
	isActive = 0
from dbo.admin_navigation n
where navname = 'Search Ads'
and navareaID = 1

update n2 set
	isActive = 0
from dbo.admin_navigation n
inner join dbo.admin_navigation n2
	on n2.parentNavigationID = n.navigationID
	and n.navname = 'Search Ads'
	and n.navareaID = 1
	and n2.navareaID = 2

update n3 set
	isActive = 0
from dbo.admin_navigation n
inner join dbo.admin_navigation n2
	on n2.parentNavigationID = n.navigationID
	and n.navname = 'Search Ads'
	and n.navareaID = 1
	and n2.navareaID = 2
inner join dbo.admin_navigation n3
	on n3.parentNavigationID = n2.navigationID
	and n3.navareaID = 3