use membercentral
GO

ALTER TABLE dbo.ams_memberPaymentProfiles ADD addedByMemberID int null
GO

DROP PROC ams_createMemberPaymentProfile
GO

ALTER TABLE dbo.ams_memberPaymentProfiles ADD lastUpdatedByMemberID int null
GO
ALTER TABLE dbo.ams_memberPaymentProfiles ADD lastUpdatedDate datetime null
GO
ALTER TABLE dbo.ams_memberPaymentProfiles ADD CONSTRAINT
	FK_ams_memberPaymentProfiles_ams_members2 FOREIGN KEY
	(
	lastUpdatedByMemberID
	) REFERENCES dbo.ams_members
	(
	memberID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO

ALTER TABLE dbo.ams_memberPaymentProfiles ADD cardTypeID int NULL
GO

ALTER TABLE dbo.ams_memberPaymentProfiles ADD CONSTRAINT
	FK_ams_memberPaymentProfiles_mp_cardTypes FOREIGN KEY
	(
	cardTypeID
	) REFERENCES dbo.mp_cardTypes
	(
	cardTypeID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO


ALTER TABLE dbo.ams_memberPaymentProfiles ADD failedSinceDate datetime null, failedLastDate datetime null
GO


update mpp
set mpp.addedByMemberID = ss.memberID
from dbo.ams_memberPaymentProfiles as mpp
inner join platformStats.dbo.statsSessions as ss 
    on ss.sessionID = mpp.addedStatsSessionID
    and ss.memberID is not null
GO


declare @memberID int
select @memberID = memberID from dbo.ams_members where orgID = 1 and membernumber = 'SYSTEM'

update dbo.ams_memberPaymentProfiles
set addedByMemberID = @memberID
where addedByMemberID is null
GO

ALTER TABLE dbo.ams_memberPaymentProfiles ALTER COLUMN addedByMemberID int not null
GO

ALTER TABLE dbo.ams_memberPaymentProfiles ADD CONSTRAINT
	FK_ams_memberPaymentProfiles_ams_members1 FOREIGN KEY
	(
	addedByMemberID
	) REFERENCES dbo.ams_members
	(
	memberID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO


-- update existing card types
select mpp.payProfileID, dbo.mp_getCardTypeID(parseName(replace(replace(right(replace(replace(ph.gatewayResponse.value('(/response/rawresponse/createCustomerProfileTransactionResponse/directResponse)[1]','varchar(3000)'),'||||||||||||||||',''),'|','.'),20),'.....',''),'...',''),1)) as cardTypeID
into #tmpTimCardTypes
from dbo.tr_paymentHistory as ph WITH (NOLOCK)
inner join dbo.ams_memberPaymentProfiles as mpp WITH (NOLOCK) on mpp.payProfileID = ph.memberPaymentProfileID
inner join dbo.mp_profiles as mp WITH (NOLOCK) on mp.profileID = mpp.profileID
inner join dbo.mp_gateways as mg WITH (NOLOCK) on mg.gatewayID = mp.gatewayID
where ph.isSuccess = 1
and mpp.status = 'A'
and mg.gatewayType = 'AuthorizeCCCIM'

update mpp
set mpp.cardTypeID = tmp.cardTypeID
from dbo.ams_memberPaymentProfiles as mpp
inner join #tmpTimCardTypes as tmp on tmp.payProfileID = mpp.payProfileID
where tmp.cardTypeID is not null

DROP TABLE #tmpTimCardTypes
GO



