USE [memberCentral]
GO
/****** Object:  StoredProcedure [dbo].[cms_createDefaultRelationshipCategories]    Script Date: 10/28/2013 09:26:38 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROCEDURE [dbo].[cms_createDefaultRelationshipCategories] 
	@siteID int, 
	@contributingMemberID int
AS
BEGIN TRAN
	declare @categoryTreeID int
	declare @controllingSiteResourceID int

	select @controllingSiteResourceID = dbo.fn_getSiteResourceIDForResourceType('RelationshipAdmin', @siteID)
	IF @controllingSiteResourceID is NOT NULL BEGIN

		select @categoryTreeID = categoryTreeID
		from cms_categoryTrees
		where controllingSiteResourceID = @controllingSiteResourceID

		-- create category tree
		IF @categoryTreeID is NULL
		BEGIN
			exec [cms_createCategoryTree] @siteID=@siteID,@categoryTreeName='RelationshipTypes',
			@categoryTreeDesc='Types of Relationships',
			@categoryTreeCode='',
			@controllingSiteResourceID=@controllingSiteResourceID,
			@categoryTreeID=@categoryTreeID OUTPUT	

			IF @@ERROR <> 0 GOTO on_error
		END


		-- add default categories
		declare @relationshipCategoryID int
		declare @categoryID int


		select @categoryID = categoryID from dbo.cms_categories c inner join cms_categoryTrees ct on ct.categoryTreeID = c.categoryTreeID and ct.categoryTreeID = @categoryTreeID and c.categoryName = 'Recruited By'

		IF @categoryTreeID is NULL
		BEGIN
			exec [cms_createCategory]
			@categoryTreeID=@categoryTreeID,
			@categoryName='Recruited By',
			@categoryDesc='',
			@categoryCode='',
			@parentCategoryID=NULL,
			@contributorMemberID=@contributingMemberID,
			@categoryID=@relationshipCategoryID OUTPUT

			IF @@ERROR <> 0 GOTO on_error
		END

		select @categoryID = categoryID from dbo.cms_categories c inner join cms_categoryTrees ct on ct.categoryTreeID = c.categoryTreeID and ct.categoryTreeID = @categoryTreeID and c.categoryName = 'Charitable Organization'

		IF @categoryTreeID is NULL
		BEGIN
			exec [cms_createCategory]
			@categoryTreeID=@categoryTreeID,
			@categoryName='Charitable Organization',
			@categoryDesc='',
			@categoryCode='',
			@parentCategoryID=NULL,
			@contributorMemberID=@contributingMemberID,
			@categoryID=@relationshipCategoryID OUTPUT

			IF @@ERROR <> 0 GOTO on_error
		END
	END

IF @@TRANCOUNT > 0 COMMIT TRAN	
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1