ALTER PROC dbo.mc_claimListMemberships
@siteID int,
@orgID int,
@orgCode varchar(10),
@claimedEmailAddress varchar(100),
@claimedByMemberNumber varchar(100),
@receiverMemberID int,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @memberNumber varchar(200), @memberID int, @firstname varchar(100), @lastname varchar(100), @company varchar(200), @matchingMemberships int;
	DECLARE @tblMatchingMembers table(MemberID_ int, List_ varchar(60));
					
	SELECT @matchingMemberships = count(*)
	FROM trialslyris1.dbo.members_ lm
	INNER JOIN trialslyris1.dbo.lists_ l ON l.name_ = lm.list_
		AND lm.emailaddr_ = @claimedEmailAddress
		AND isnull(lm.externalMemberID,'') <> @claimedByMemberNumber
	INNER JOIN trialslyris1.dbo.lists_format lf ON lf.[name] = l.name_
		AND lf.orgcode = @orgCode
	INNER JOIN membercentral.membercentral.dbo.lists_lists mcL ON mcl.listname = lf.[name] collate Latin1_General_CI_AI
	INNER JOIN membercentral.membercentral.dbo.cms_siteResources lsr ON mcl.siteResourceID = lsr.siteResourceID
		AND lsr.siteID = @siteID
	INNER JOIN membercentral.membercentral.dbo.cms_siteResourceStatuses srs ON srs.siteResourceStatusID = lsr.siteResourceStatusID
		AND srs.siteResourceStatusDesc = 'Active';

	IF (@matchingMemberships > 0) BEGIN
		/* looks to see if this email address is assigned to anyone other than this member */
		SELECT @memberNumber=lm.externalMemberID, @memberID=m.memberID, @firstname=m.firstname, @lastname=m.lastname, @company=m.company
		FROM trialslyris1.dbo.members_ lm
		INNER JOIN trialslyris1.dbo.lists_ l ON l.name_ = lm.list_
			AND lm.emailaddr_ = @claimedEmailAddress
			AND isnull(lm.externalMemberID,'') <> @claimedByMemberNumber
		INNER JOIN trialslyris1.dbo.lists_format lf ON lf.[name] = l.name_
			AND lf.orgcode = @orgCode
		INNER JOIN membercentral.membercentral.dbo.lists_lists mcL ON mcl.listname = lf.[name] collate Latin1_General_CI_AI
		INNER JOIN membercentral.membercentral.dbo.cms_siteResources lsr ON mcl.siteResourceID = lsr.siteResourceID
			AND lsr.siteID = @siteID
		INNER JOIN membercentral.membercentral.dbo.cms_siteResourceStatuses srs ON srs.siteResourceStatusID = lsr.siteResourceStatusID
			AND srs.siteResourceStatusDesc = 'Active'
		INNER JOIN membercentral.membercentral.dbo.ams_members m ON m.memberNumber = lm.externalMemberID collate Latin1_General_CI_AI
			AND m.orgID = @orgID
			AND m.memberNumber <> @claimedByMemberNumber;
					
		IF (@memberID IS NULL) /* if no valid links on any of the orgs lists for this address, claim memberships */
		BEGIN
			INSERT INTO @tblMatchingMembers(memberID_, List_)
			SELECT DISTINCT lm.MemberID_, lm.List_
			FROM trialslyris1.dbo.members_ lm
			INNER JOIN trialslyris1.dbo.lists_ l ON l.name_ = lm.list_ 
				AND lm.emailaddr_ = @claimedEmailAddress
				AND isnull(lm.externalMemberID,'') <> @claimedByMemberNumber
			INNER JOIN trialslyris1.dbo.lists_format lf ON lf.[name] = l.name_
				AND lf.orgcode = @orgCode
			INNER JOIN membercentral.membercentral.dbo.lists_lists mcL ON mcl.listname = lf.[name] collate Latin1_General_CI_AI
			INNER JOIN membercentral.membercentral.dbo.cms_siteResources lsr ON mcl.siteResourceID = lsr.siteResourceID
				AND lsr.siteID = @siteID
			INNER JOIN membercentral.membercentral.dbo.cms_siteResourceStatuses srs ON srs.siteResourceStatusID = lsr.siteResourceStatusID
				AND srs.siteResourceStatusDesc = 'Active';

			UPDATE lm
			SET externalMemberID = @claimedByMemberNumber
			FROM trialslyris1.dbo.members_ lm
			INNER JOIN @tblMatchingMembers tmp on tmp.memberID_ = lm.memberID_;
						
			INSERT INTO memberCentral.platformQueue.dbo.queue_mongo (msgjson)
			SELECT '{ "c":"historyEntries_SYS_ADMIN_LISTUPDATE", "d": { "HISTORYCODE":"SYS_ADMIN_LISTUPDATE", "SITEID":' + cast(@siteID as varchar(10)) +
				', "ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) +
				', "RECEIVERMEMBERID":' + cast(@receiverMemberID as varchar(10)) +
				', "MAINMESSAGE":"List Membership Claimed"' +
				', "LISTNAME":"'+ tmp.List_ +'"'+
				', "MESSAGES":[ "Non-associated list membership for ['+ @claimedEmailAddress +'] has been claimed." ]'+
				', "UPDATEDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '"'+
				' } }'
			FROM (
				SELECT DISTINCT List_ FROM @tblMatchingMembers
			) AS tmp;
		END
	END

	SELECT CASE WHEN @memberID IS NULL THEN 1 ELSE 0 END AS result, @matchingMemberships AS matchingMemberships,
		@memberNumber AS memberNumber, @memberID AS memberID, @firstname AS firstname, @lastname AS lastname, @company AS company;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
