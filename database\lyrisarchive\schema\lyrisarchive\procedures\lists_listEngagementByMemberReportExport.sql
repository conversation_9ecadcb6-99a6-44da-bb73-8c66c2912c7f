ALTER PROC dbo.lists_listEngagementByMemberReportExport
@orgcode varchar(10),
@startDate datetime = null,
@endDate datetime = null,
@filename varchar(800)

AS

/* Do not change */
if @startdate is null 
	set @startdate = '1/1/1900';

if @enddate is null 
	set @enddate = getdate();

IF OBJECT_ID('tempdb..#tmpListEngagementbyMemberExport') IS NOT NULL 
	DROP TABLE #tmpListEngagementbyMemberExport;

declare @uniqueLists TABLE (listID int PRIMARY KEY, list_ varchar(100), orgcode varchar(10));
declare @uniqueSenders TABLE (id int IDENTITY(1,1) PRIMARY KEY, emailaddr varchar(200), ExternalMemberID varchar(100), 
	listID int, numNewMessages int NOT NULL DEFAULT 0, numReplies int NOT NULL DEFAULT 0);

insert into @uniqueLists (listID, list_, orgcode)
select ml.listID, l.name_, 	lf.orgcode
from trialslyris1.dbo.lists_ l
inner join trialslyris1.dbo.lists_format lf on l.name_ = lf.name
	and l.adminSend_ = 'F'
	and lf.orgcode = isnull(@orgcode,lf.orgcode)
inner join dbo.messageLists ml on ml.list = l.name_;

insert into @uniqueSenders (emailaddr, ExternalMemberID, listID)
select hdrfromspc_, max(mem.ExternalMemberID) as ExternalMemberID, ml.listID
from dbo.messages_ m
inner join @uniqueLists ml on m.listID = ml.listID and m.isVisible=1
	and m.creatStamp_ between @startdate and @enddate
left outer join trialslyris1.dbo.members_ mem on mem.list_ = ml.list_
	and mem.EmailAddr_ = m.HdrFromSpc_
group by hdrfromspc_, ml.listID;

update s 
set s.numNewMessages= temp.numNewMessages
from @uniqueSenders s
inner join (
	select m.listID, m.HdrFromSpc_, count(*) as numNewMessages
	from dbo.messages_ m
	inner join @uniqueLists ml on m.listID = ml.listID and m.isVisible=1
		and m.creatStamp_ between @startdate and @enddate
		and parentID_ = messageID_
	group by m.listID, m.HdrFromSpc_
) temp on temp.listid = s.listID and temp.HdrFromSpc_ = s.emailaddr;

update s 
set s.numReplies= temp.numReplies
from @uniqueSenders s
inner join (
	select m.listID, m.HdrFromSpc_, count(*) as numReplies
	from dbo.messages_ m
	inner join @uniqueLists ml on m.listID = ml.listID and m.isVisible=1
		and m.creatStamp_ between @startdate and @enddate
		and parentID_ <> messageID_
	group by m.listID, m.HdrFromSpc_
) temp on temp.listid = s.listID and temp.HdrFromSpc_ = s.emailaddr;

select ml.orgcode, ml.list_, s.emailaddr, s.ExternalMemberID, s.numNewMessages, s.numReplies 
into #tmpListEngagementbyMemberExport
from @uniqueLists ml 
inner join @uniqueSenders s on s.listID = ml.listID
order by orgcode, emailaddr;

EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql='select * from #tmpListEngagementbyMemberExport order by orgcode, list_';

IF OBJECT_ID('tempdb..#tmpListEngagementbyMemberExport') IS NOT NULL 
	DROP TABLE #tmpListEngagementbyMemberExport;
GO
