use membercentral
GO

ALTER PROC [dbo].[ams_createMemberDataColumn]
@orgID int,
@columnName varchar(255),
@columnDesc varchar(255),
@allowMultiple bit,
@skipImport bit,
@allowNull bit,
@defaultValue varchar(max),
@allowNewValuesOnImport bit,
@dataTypeCode varchar(20),
@displayTypeCode varchar(20),
@isReadOnly bit,
@minChars int,
@maxChars int,
@minSelected int,
@maxSelected int,
@minValueInt int,
@maxValueInt int,
@minValueDecimal2 decimal(9,2),
@maxValueDecimal2 decimal(9,2),
@minValueDate datetime,
@maxValueDate datetime,
@columnID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- check for existing column with same name
	SELECT @columnID = null

	-- if reserved column name, return 0
	IF EXISTS(select emailTypeID from dbo.ams_memberEmailTypes where orgID = @orgID and emailType = @columnName)
	OR EXISTS(select websiteTypeID from dbo.ams_memberWebsiteTypes where orgID = @orgID and websiteType = @columnName)
	OR EXISTS(select columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @columnName)
	OR EXISTS(select C.NAME FROM SYS.COLUMNS C INNER JOIN SYS.OBJECTS O ON C.object_ID = O.object_ID WHERE O.NAME = 'ams_members' and C.NAME = @columnName)
		SELECT @columnID = 0

	-- if not there, add it
	ELSE BEGIN
		DECLARE @rc int, @valueID int
		
		DECLARE @dataTypeID int, @displayTypeID int
		SELECT @dataTypeID = dataTypeID from dbo.ams_memberDataColumnDataTypes where dataTypeCode = @dataTypeCode
			IF @dataTypeID is null RAISERROR('invalid datatypecode', 16, 1);
		SELECT @displayTypeID = displayTypeID from dbo.ams_memberDataColumnDisplayTypes where displayTypeCode = @displayTypeCode
			IF @displayTypeID is null RAISERROR('invalid displaytypecode', 16, 1);

		-- validate display type when multiple
		IF @allowMultiple = 1 and @displayTypeCode = 'RADIO' BEGIN
			SELECT @displayTypeCode = 'CHECKBOX'
			SELECT @displayTypeID = displayTypeID from dbo.ams_memberDataColumnDisplayTypes where displayTypeCode = @displayTypeCode
		END

		-- validations
		IF @displayTypeCode IN ('DOCUMENT','TEXTAREA','HTMLCONTENT') OR @dataTypeCode in ('XML','CONTENTOBJ','DOCUMENTOBJ')
			SELECT @allowNull = 1
		IF @displayTypeCode IN ('DOCUMENT') OR @dataTypeCode in ('DOCUMENTOBJ')
			SELECT @skipImport = 1
		IF @allowNull = 0 and len(isnull(@defaultValue,'')) = 0
			SELECT @allowNull = 1
		IF @skipImport = 1
			SELECT @allowNewValuesOnImport = 1
		IF @allowNull = 1
			SELECT @defaultValue = ''

		-- add column
		INSERT INTO dbo.ams_memberDataColumns (orgID, columnName, columnDesc, skipImport, dataTypeID, displayTypeID, allowNull, 
			allowNewValuesOnImport, defaultValueID, isReadOnly, allowMultiple, minChars, maxChars, minSelected, maxSelected, 
			minValueInt, maxValueInt, minValueDecimal2, maxValueDecimal2, minValueDate, maxValueDate)
		VALUES (@orgID, @columnName, @columnDesc, @skipImport, @dataTypeID, @displayTypeID, @allowNull, @allowNewValuesOnImport, 
			null, @isReadOnly, @allowMultiple, @minChars, @maxChars, @minSelected, @maxSelected, @minValueInt, @maxValueInt, 
			@minValueDecimal2, @maxValueDecimal2, @minValueDate, @maxValueDate)
		SELECT @columnID = SCOPE_IDENTITY()
		
		-- if adding a bit column, add the two values automatically
		IF @dataTypeCode = 'BIT' BEGIN
			EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=0, @valueID=@valueID OUTPUT
				IF @valueID = 0 RAISERROR('unable to create bit column value 0', 16, 1);
			EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=1, @valueID=@valueID OUTPUT
				IF @valueID = 0 RAISERROR('unable to create bit column value 1', 16, 1);
		END

		-- set default valueID if necessary
		IF len(isnull(@defaultValue,'')) > 0 BEGIN
			EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=@defaultValue, @valueID=@valueID OUTPUT
				IF @valueID = 0 RAISERROR('unable to create default column value', 16, 1);
			UPDATE dbo.ams_memberDataColumns 
			SET defaultValueID = @valueID
			WHERE columnID = @columnID
		
			-- Anyone who doesnt have a value for this column needs this value.
			IF OBJECT_ID('tempdb..#tblMDDEF') IS NOT NULL 
				DROP TABLE #tblMDDEF
			CREATE TABLE #tblMDDEF (memberid int PRIMARY KEY);

			insert into #tblMDDEF (memberid)
			select distinct m.memberid
			from dbo.ams_members as m
			left outer join dbo.ams_memberData as md 
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID and mdcv.columnID = @columnID
				on md.memberid = m.memberID
			where m.orgID = @orgID
			and m.memberid = m.activememberid
			and m.status <> 'D'
			and md.dataid is null

			INSERT INTO dbo.ams_memberData (memberid, valueID)
			select memberid, @valueID
			from #tblMDDEF

			UPDATE dbo.ams_members
			SET dateLastUpdated = getdate()
			WHERE memberID in (select memberID from #tblMDDEF)

			IF OBJECT_ID('tempdb..#tblMDDEF') IS NOT NULL 
				DROP TABLE #tblMDDEF
		END 

		-- recreate view for org
		EXEC dbo.ams_createVWMemberData	@orgID=@orgID
	END


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @columnID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[ams_createMemberEmailType]
@orgID int,
@emailType varchar(20),
@emailTypeDesc varchar(max)

AS

-- new email type cannot already exist or conflict with what would be in memberData views
IF EXISTS (select emailTypeID from dbo.ams_memberEmailTypes where emailType = @emailType and orgID = @orgID) 
OR EXISTS (select C.NAME FROM SYS.COLUMNS C INNER JOIN SYS.OBJECTS O ON C.object_ID = O.object_ID WHERE O.NAME = 'ams_members' and C.NAME = @emailType)
OR EXISTS (select websiteTypeID from dbo.ams_memberWebsiteTypes where websiteType = @emailType and orgID = @orgID)
OR EXISTS (select columnID from dbo.ams_memberDataColumns where columnName = @emailType and orgID = @orgID)
	RETURN 0

DECLARE @newOrder int
SELECT @newOrder = isnull(max(emailTypeOrder),0)+1
	from dbo.ams_memberEmailTypes
	where orgID = @orgID

INSERT into dbo.ams_memberEmailTypes (orgid, emailType, emailTypeDesc, emailTypeOrder) 
VALUES (@orgID, @emailType, @emailTypeDesc, @newOrder)
	IF @@ERROR <> 0 GOTO on_error

DECLARE @emailTypeID int

select @emailTypeID=emailTypeID
from dbo.ams_memberEmailTypes
where orgID = @orgID AND emailType=@emailType and emailTypeDesc=@emailTypeDesc

if @emailTypeID is not null
BEGIN

	declare @minSiteID int, @fieldsetID int, @memberMainRID int, @fieldcode varchar(30), @fieldLabel varchar(100), @fieldID int

	select @fieldCode = 'me_' + cast(@emailTypeID as varchar(4)) + '_email', @fieldLabel = @emailType

	select @minSiteID = min(siteID) from sites where orgID = @orgID

	while @minSiteID is not null BEGIN
		select @fieldsetID = NULL, @memberMainRID = NULL

		select top 1 @memberMainRID = sr.siteResourceID 
		from dbo.cms_siteResources as sr
			inner join dbo.cms_siteResourceTypes as srt on srt.resourceTypeID = sr.resourceTypeID and srt.resourceType = 'MemberAdmin'
		where sr.siteID = @minSiteID
		and sr.siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

		select @fieldsetID = mfs.fieldsetID 
		from dbo.ams_memberFieldsets mfs
		inner join dbo.ams_memberFieldUsage mfu on mfu.fieldsetID = mfs.fieldsetID and mfu.area = 'main' and mfu.siteResourceID = @memberMainRID
		where mfs.siteID = @minSiteID 

		if @fieldsetID is not null
			EXEC dbo.ams_createMemberField @fieldsetID, @fieldcode, @fieldLabel, '', 1, 0, @fieldID OUTPUT

		select @minSiteID = min(siteID) from sites where siteID > @minSiteID and orgID = @orgID

	END


END


RETURN 0

on_error:
	RETURN -1
GO

ALTER PROC [dbo].[ams_createMemberWebsiteType]
@orgID int,
@websiteType varchar(20),
@websiteTypeDesc varchar(max)

AS

-- new website type cannot already exist or conflict with what would be in memberData views
IF EXISTS (select websiteTypeID from dbo.ams_memberWebsiteTypes where websiteType = @websiteType and orgID = @orgID)
OR EXISTS (select C.NAME FROM SYS.COLUMNS C INNER JOIN SYS.OBJECTS O ON C.object_ID = O.object_ID WHERE O.NAME = 'ams_members' and C.NAME = @websiteType)
OR EXISTS (select emailTypeID from dbo.ams_memberEmailTypes where emailType = @websiteType and orgID = @orgID)
OR EXISTS (select columnID from dbo.ams_memberDataColumns where columnName = @websiteType and orgID = @orgID)
	RETURN 0

DECLARE @newOrder int
SELECT @newOrder = isnull(max(websiteTypeOrder),0)+1
	from dbo.ams_memberwebsiteTypes
	where orgID = @orgID

INSERT into dbo.ams_memberwebsiteTypes (orgid, websiteType, websiteTypeDesc, websiteTypeOrder) 
VALUES (@orgID, @websiteType, @websiteTypeDesc, @newOrder)
	IF @@ERROR <> 0 GOTO on_error

DECLARE @websiteTypeID int

select @websiteTypeID=websiteTypeID
from dbo.ams_memberWebsiteTypes
where orgID = @orgID AND websiteType=@websiteType and websiteTypeDesc=@websiteTypeDesc

if @websiteTypeID is not null
BEGIN

	declare @minSiteID int, @fieldsetID int, @memberMainRID int, @fieldcode varchar(30), @fieldLabel varchar(100), @fieldID int

	select @fieldCode = 'mw_' + cast(@websiteTypeID as varchar(4)) + '_website', @fieldLabel = @websiteType

	select @minSiteID = min(siteID) from sites where orgID = @orgID

	while @minSiteID is not null BEGIN
		select @fieldsetID = NULL, @memberMainRID = NULL

		select top 1 @memberMainRID = sr.siteResourceID 
		from dbo.cms_siteResources as sr
			inner join dbo.cms_siteResourceTypes as srt on srt.resourceTypeID = sr.resourceTypeID and srt.resourceType = 'MemberAdmin'
		where sr.siteID = @minSiteID
		and sr.siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

		select @fieldsetID = mfs.fieldsetID 
		from dbo.ams_memberFieldsets mfs
		inner join dbo.ams_memberFieldUsage mfu on mfu.fieldsetID = mfs.fieldsetID and mfu.area = 'main' and mfu.siteResourceID = @memberMainRID
		where mfs.siteID = @minSiteID 

		if @fieldsetID is not null
			EXEC dbo.ams_createMemberField @fieldsetID, @fieldcode, @fieldLabel, '', 1, 0, @fieldID OUTPUT

		select @minSiteID = min(siteID) from sites where siteID > @minSiteID and orgID = @orgID

	END


END


RETURN 0

on_error:
	RETURN -1
GO

ALTER PROC [dbo].[ams_OrgSettingsUpdateEmail]
@orgID int,
@emailTypeID int,
@emailType varchar(20), 
@emailTypeDesc varchar(max)

AS

DECLARE @oldemailType varchar(20), @oldemailTypeDesc varchar(max)
declare @rerunview bit, @minRID int, @ruleSQL varchar(max)

select @oldemailType=emailType, @oldemailTypeDesc=emailTypeDesc
	from dbo.ams_memberEmailTypes
	WHERE orgID = @orgID
	AND emailTypeID = @emailTypeID

BEGIN TRAN

IF @oldemailType <> @emailType COLLATE SQL_Latin1_General_Cp1_CS_AS BEGIN
	-- new email type cannot already exist or conflict with what would be in memberData views
	IF EXISTS (select emailTypeID from dbo.ams_memberEmailTypes where emailType = @emailType and orgID = @orgID and emailTypeID <> @emailTypeID) 
	OR EXISTS (select C.NAME FROM SYS.COLUMNS C INNER JOIN SYS.OBJECTS O ON C.object_ID = O.object_ID WHERE O.NAME = 'ams_members' and C.NAME = @emailType)
	OR EXISTS (select websiteTypeID from dbo.ams_memberWebsiteTypes where websiteType = @emailType and orgID = @orgID)
	OR EXISTS (select columnID from dbo.ams_memberDataColumns where columnName = @emailType and orgID = @orgID)
		GOTO on_error

	-- update email type
	update dbo.ams_memberEmailTypes set emailType = @emailType where orgID = @orgID and emailTypeID = @emailTypeID
		IF @@ERROR <> 0 GOTO on_error

	-- update memberfields
	update mf
	set mf.dbField = @emailType
	from dbo.ams_memberFields as mf
	inner join dbo.ams_memberFieldSets as mfs on mfs.fieldsetID = mf.fieldsetID
	inner join dbo.sites as s on s.siteid = mfs.siteid and s.orgID = @orgID
	where mf.fieldCode = 'me_'+cast(@emailTypeID as varchar(10))+'_email'
		IF @@ERROR <> 0 GOTO on_error

	-- update conditions. dont need to rerun conditions if we are just changing the name.
	UPDATE dbo.ams_virtualGroupConditions
	SET [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
	WHERE orgID = @orgID
	AND fieldCode = 'me_'+cast(@emailTypeID as varchar(10))+'_email'
		IF @@ERROR <> 0 GOTO on_error

	-- update rules
	SELECT @minRID = null
	SELECT @minRID = min(r.ruleID)
		from dbo.ams_virtualGroupRules as r
		CROSS APPLY ruleXML.nodes('//condition') as C(condition)
		inner join dbo.ams_virtualGroupConditions as vgc on vgc.uid = C.condition.value('@id','uniqueidentifier')
			and vgc.orgID = r.orgID
		where r.orgID = @orgID
		AND fieldCode = 'me_'+cast(@emailTypeID as varchar(10))+'_email'
	WHILE @minRID is not null BEGIN
		EXEC dbo.ams_updateVirtualGroupRuleSQL @ruleID=@minRID
			IF @@ERROR <> 0 GOTO on_error

		SELECT @minRID = min(r.ruleID)
			from dbo.ams_virtualGroupRules as r
			CROSS APPLY ruleXML.nodes('//condition') as C(condition)
			inner join dbo.ams_virtualGroupConditions as vgc on vgc.uid = C.condition.value('@id','uniqueidentifier')
				and vgc.orgID = r.orgID
			where r.orgID = @orgID
			AND fieldCode = 'me_'+cast(@emailTypeID as varchar(10))+'_email'
			and r.ruleID > @minRID
	END
	
	set @rerunview = 1
END

IF @oldemailTypeDesc <> @emailTypeDesc COLLATE SQL_Latin1_General_Cp1_CS_AS BEGIN
	update dbo.ams_memberEmailTypes 
	set emailTypeDesc = @emailTypeDesc
	where orgID = @orgID 
	and emailTypeID = @emailTypeID
		IF @@ERROR <> 0 GOTO on_error
END

COMMIT TRAN

IF @rerunview = 1 BEGIN
	EXEC dbo.ams_createVWMemberData @orgID=@orgID
END

RETURN 0

on_error:
	ROLLBACK TRAN
	RETURN -1
GO

ALTER PROC [dbo].[ams_OrgSettingsUpdateWebsite]
@orgID int,
@websiteTypeID int,
@websiteType varchar(20), 
@websiteTypeDesc varchar(max)

AS

DECLARE @oldwebsiteType varchar(20), @oldwebsiteTypeDesc varchar(max)
declare @rerunview bit, @minRID int, @ruleSQL varchar(max)

select @oldwebsiteType=websiteType, @oldwebsiteTypeDesc=websiteTypeDesc
	from dbo.ams_memberWebsiteTypes
	WHERE orgID = @orgID
	AND websiteTypeID = @websiteTypeID

BEGIN TRAN

IF @oldwebsiteType <> @websiteType COLLATE SQL_Latin1_General_Cp1_CS_AS BEGIN
	-- new website type cannot already exist or conflict with what would be in memberData views
	IF EXISTS (select websiteTypeID from dbo.ams_memberWebsiteTypes where websiteType = @websiteType and orgID = @orgID and websiteTypeID <> @websiteTypeID) 
	OR EXISTS (select C.NAME FROM SYS.COLUMNS C INNER JOIN SYS.OBJECTS O ON C.object_ID = O.object_ID WHERE O.NAME = 'ams_members' and C.NAME = @websiteType)
	OR EXISTS (select emailTypeID from dbo.ams_memberEmailTypes where emailType = @websiteType and orgID = @orgID)
	OR EXISTS (select columnID from dbo.ams_memberDataColumns where columnName = @websiteType and orgID = @orgID)
		GOTO on_error

	-- update website type
	update dbo.ams_memberwebsiteTypes set websiteType = @websiteType where orgID = @orgID and websiteTypeID = @websiteTypeID
		IF @@ERROR <> 0 GOTO on_error

	-- update memberfields
	update mf
	set mf.dbField = @websiteType
	from dbo.ams_memberFields as mf
	inner join dbo.ams_memberFieldSets as mfs on mfs.fieldsetID = mf.fieldsetID
	inner join dbo.sites as s on s.siteid = mfs.siteid and s.orgID = @orgID
	where mf.fieldCode = 'mw_'+cast(@websiteTypeID as varchar(10))+'_website'
		IF @@ERROR <> 0 GOTO on_error

	-- update conditions. dont need to rerun conditions if we are just changing the name.
	UPDATE dbo.ams_virtualGroupConditions
	SET [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
	WHERE orgID = @orgID
	AND fieldCode = 'mw_'+cast(@websiteTypeID as varchar(10))+'_website'
		IF @@ERROR <> 0 GOTO on_error

	-- update rules
	SELECT @minRID = null
	SELECT @minRID = min(r.ruleID)
		from dbo.ams_virtualGroupRules as r
		CROSS APPLY ruleXML.nodes('//condition') as C(condition)
		inner join dbo.ams_virtualGroupConditions as vgc on vgc.uid = C.condition.value('@id','uniqueidentifier')
			and vgc.orgID = r.orgID
		where r.orgID = @orgID
		AND fieldCode = 'mw_'+cast(@websiteTypeID as varchar(10))+'_website'
	WHILE @minRID is not null BEGIN
		EXEC dbo.ams_updateVirtualGroupRuleSQL @ruleID=@minRID
			IF @@ERROR <> 0 GOTO on_error

		SELECT @minRID = min(r.ruleID)
			from dbo.ams_virtualGroupRules as r
			CROSS APPLY ruleXML.nodes('//condition') as C(condition)
			inner join dbo.ams_virtualGroupConditions as vgc on vgc.uid = C.condition.value('@id','uniqueidentifier')
				and vgc.orgID = r.orgID
			where r.orgID = @orgID
			AND fieldCode = 'mw_'+cast(@websiteTypeID as varchar(10))+'_website'
			and r.ruleID > @minRID
	END
	
	set @rerunview = 1
END

IF @oldwebsiteTypeDesc <> @websiteTypeDesc COLLATE SQL_Latin1_General_Cp1_CS_AS BEGIN
	update dbo.ams_memberwebsiteTypes 
	set websiteTypeDesc = @websiteTypeDesc
	where orgID = @orgID 
	and websiteTypeID = @websiteTypeID
		IF @@ERROR <> 0 GOTO on_error
END

COMMIT TRAN

IF @rerunview = 1 BEGIN
	EXEC dbo.ams_createVWMemberData @orgID=@orgID
END

RETURN 0

on_error:
	ROLLBACK TRAN
	RETURN -1
GO

ALTER PROC [dbo].[ams_updateMemberDataColumn]
@columnID int,
@columnName varchar(255),
@columnDesc varchar(255),
@allowMultiple bit,
@skipImport bit,
@allowNull bit,
@defaultValue varchar(max),
@allowNewValuesOnImport bit,
@dataTypeCode varchar(20),
@displayTypeCode varchar(20),
@isReadOnly bit,
@minChars int,
@maxChars int,
@minSelected int,
@maxSelected int,
@minValueInt int,
@maxValueInt int,
@minValueDecimal2 decimal(9,2),
@maxValueDecimal2 decimal(9,2),
@minValueDate datetime,
@maxValueDate datetime

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	DECLARE @valueID int, @rc int, @orgID int, @displayTypeID int, @dataTypeID int, @newbitvalueID int
	DECLARE @oldColumnName varchar(255), @olddataTypeCode varchar(20), @olddisplayTypeCode varchar(20)
	DECLARE @memberIDList varchar(max), @conditionIDList varchar(max)

	IF @columnID is not null AND @columnName is not null 
	BEGIN

		SELECT @orgID = orgID from dbo.ams_memberdataColumns where columnID = @columnID

		-- check custom field name
		IF EXISTS(select emailTypeID from dbo.ams_memberEmailTypes where orgID = @orgID and emailType = @columnName)
		OR EXISTS(select websiteTypeID from dbo.ams_memberWebsiteTypes where orgID = @orgID and websiteType = @columnName)
		OR EXISTS(select columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @columnName and columnID <> @columnID)
		OR EXISTS (select C.NAME FROM SYS.COLUMNS C INNER JOIN SYS.OBJECTS O ON C.object_ID = O.object_ID WHERE O.NAME = 'ams_members' and C.NAME = @columnName)
			RAISERROR('That column name is already in use.', 16, 1) 

		SELECT @oldColumnName = columnName from dbo.ams_memberDataColumns where columnID = @columnID

		-- validate display type when multiple
		IF @allowMultiple = 1 and @displayTypeCode = 'RADIO'
			SELECT @displayTypeCode = 'CHECKBOX'
		IF @allowMultiple = 0 and @displayTypeCode = 'CHECKBOX'
			SELECT @displayTypeCode = 'RADIO'

		-- validations
		SELECT @olddataTypeCode = dt.dataTypeCode
			from dbo.ams_memberDataColumns as c
			inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = c.dataTypeID
			and c.columnID = @columnID
		SELECT @olddisplayTypeCode = dt.displayTypeCode
			from dbo.ams_memberDataColumns as c
			inner join dbo.ams_memberDataColumnDisplayTypes as dt on dt.displayTypeID = c.displayTypeID
			and c.columnID = @columnID
		SELECT @displayTypeID = displayTypeID
			from dbo.ams_memberDataColumnDisplayTypes
			where displayTypeCode = @displayTypeCode
		SELECT @dataTypeID = dataTypeID
			from dbo.ams_memberDataColumnDataTypes
			where dataTypeCode = @dataTypeCode
		IF @displayTypeCode IN ('DOCUMENT','TEXTAREA','HTMLCONTENT') OR @dataTypeCode in ('XML','CONTENTOBJ','DOCUMENTOBJ')
			SELECT @allowNull = 1
		IF @displayTypeCode IN ('DOCUMENT') OR @dataTypeCode in ('DOCUMENTOBJ')
			SELECT @skipImport = 1
		IF @allowNull = 0 and len(isnull(@defaultValue,'')) = 0
			SELECT @allowNull = 1
		IF @skipImport = 1
			SELECT @allowNewValuesOnImport = 1
		IF @allowNull = 1
			SELECT @defaultValue = ''

		-- set default valueID if necessary
		IF len(isnull(@defaultValue,'')) > 0 BEGIN
			EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=@defaultValue, @valueID=@valueID OUTPUT
				IF @valueID = 0 SELECT @allowNull = 1
		END 

		-- update column info
		UPDATE dbo.ams_memberDataColumns 
		SET columnName = @columnName,
			columnDesc = @columnDesc,
			skipImport = @skipImport,
			allowNull = @allowNull,
			defaultValueID = nullif(@valueID,0),
			allowNewValuesOnImport = @allowNewValuesOnImport,
			isReadOnly = @isReadOnly,
			allowMultiple = @allowMultiple,
			minChars = @minChars,
			maxChars = @maxChars,
			minSelected = @minSelected,
			maxSelected = @maxSelected,
			minValueInt = @minValueInt,
			maxValueInt = @maxValueInt,
			minValueDecimal2 = @minValueDecimal2,
			maxValueDecimal2 = @maxValueDecimal2,
			minValueDate = @minValueDate,
			maxValueDate = @maxValueDate
		WHERE columnID = @columnID

		-- if changing the display type
		IF @displayTypeCode <> @olddisplayTypeCode BEGIN
			UPDATE dbo.ams_memberDataColumns
			SET displayTypeID = @displayTypeID
			WHERE columnID = @columnID

			UPDATE dbo.ams_memberFields
			SET displayTypeID = @displayTypeID
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))

			-- if was a radio/select/checkbox (not bit) and is no longer that, we need to convert valueID to value
			IF @olddataTypeCode <> 'BIT' and @dataTypeCode <> 'BIT' and @olddisplayTypeCode in ('RADIO','SELECT','CHECKBOX') and @displayTypeCode not in ('RADIO','SELECT','CHECKBOX') BEGIN
				UPDATE vgcv
				SET vgcv.conditionValue = coalesce(mdcv.columnValueString, cast(mdcv.columnValueDecimal2 as varchar(15)), cast(mdcv.columnValueInteger as varchar(15)), convert(varchar(10),mdcv.columnvalueDate,101))
				FROM dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
					and cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue
				WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.expressionID in (1,2)
			END

			-- if was NOT a radio/select/checkbox (not bit) and is now that, we need to convert value to valueID
			IF @olddataTypeCode <> 'BIT' and @dataTypeCode <> 'BIT' and @olddisplayTypeCode not in ('RADIO','SELECT','CHECKBOX') and @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
				
				-- err if any expressions are not 1,2,7,8 now that it will be a select
				IF EXISTS (select conditionID from dbo.ams_virtualGroupConditions where fieldCode = 'md_' + cast(@columnID as varchar(10)) and expressionID not in (1,2,7,8))
					RAISERROR('There are group assignment conditions that are not compatible with the selected display type.', 16, 1) 

				-- create column values for those condition values that dont yet exist as column values
				declare @tblVals TABLE (newVal varchar(max))
				insert into @tblVals (newVal)
				select vgcv.conditionValue
				from dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.dataTypeID = 1
				and vgc.expressionID in (1,2)
				and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueString = vgcv.conditionValue)
					union
				select cast(vgcv.conditionValue as varchar(15))
				from dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.dataTypeID = 2
				and vgc.expressionID in (1,2)
				and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueDecimal2 = cast(vgcv.conditionValue as decimal(9,2)))
					union
				select cast(vgcv.conditionValue as varchar(15))
				from dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.dataTypeID = 3
				and vgc.expressionID in (1,2)
				and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueInteger = cast(vgcv.conditionValue as int))
					union
				select convert(varchar(10),vgcv.conditionValue,101)
				from dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.dataTypeID = 4
				and vgc.expressionID in (1,2)
				and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueDate = cast(vgcv.conditionValue as datetime))

				DECLARE @newvalueID int, @minValue varchar(max)
				select @minValue = min(newVal) from @tblVals
				while @minValue is not null BEGIN
					EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=@minValue, @valueID=@newvalueID OUTPUT
					select @minValue = min(newVal) from @tblVals where newVal > @minValue
				END

				-- get the valueID
				UPDATE vgcv
				SET vgcv.conditionValue = tmp.valueID
				FROM dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				INNER JOIN (
					select vgc.conditionID, mdcv.valueID
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
					and vgc.dataTypeID = 1 
					and vgcv.conditionValue = mdcv.columnvalueString
						union
					select vgc.conditionID, mdcv.valueID
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
					and vgc.dataTypeID = 2 
					and cast(vgcv.conditionValue as decimal(9,2)) = mdcv.columnvalueDecimal2
						union
					select vgc.conditionID, mdcv.valueID
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
					and vgc.dataTypeID = 3 
					and cast(vgcv.conditionValue as int) = mdcv.columnvalueInteger
						union
					select vgc.conditionID, mdcv.valueID
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
					and vgc.dataTypeID = 4 
					and cast(vgcv.conditionValue as datetime) = mdcv.columnvalueDate
				) as tmp on tmp.conditionID = vgc.conditionID
				WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.expressionID in (1,2)
			END

			UPDATE dbo.ams_virtualGroupConditions
			SET displayTypeID = @displayTypeID
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))

			UPDATE dbo.ams_virtualGroupConditions
			set [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))
		END

		-- if changing the data type
		IF @dataTypeCode <> @olddataTypeCode BEGIN
			UPDATE dbo.ams_memberDataColumns
			SET dataTypeID = @dataTypeID
			WHERE columnID = @columnID

			UPDATE dbo.ams_memberFields
			SET dataTypeID = @dataTypeID
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))

			-- check ams_virtualGroupConditions for expression conflicts
			IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'DECIMAL2' BEGIN
				BEGIN TRY
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueDecimal2 = cast(columnValueString as decimal(9,2))
					where columnID = @columnID
				END TRY
				BEGIN CATCH
					RAISERROR('There are string values not compatible with the Decimal Number (2) data type.', 16, 1) 
				END CATCH

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueString = null
				where columnID = @columnID

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (9,10)
				) RAISERROR('There are group assignment conditions that are not compatible with the Decimal Number (2) data type.', 16, 1) 
			END
			IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'INTEGER' BEGIN
				BEGIN TRY
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueInteger = cast(columnValueString as int)
					where columnID = @columnID
				END TRY
				BEGIN CATCH
					RAISERROR('There are string values not compatible with the Whole Number data type.', 16, 1) 
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueString = null
				where columnID = @columnID

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (9,10)
				) RAISERROR('There are group assignment conditions that are not compatible with the Whole Number data type.', 16, 1) 
			END
			IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'DATE' BEGIN
				BEGIN TRY
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueDate = cast(columnValueString as datetime)
					where columnID = @columnID
				END TRY
				BEGIN CATCH
					RAISERROR('There are string values not compatible with the Date data type.', 16, 1) 
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueString = null
				where columnID = @columnID

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (9,10)
				) RAISERROR('There are group assignment conditions that are not compatible with the Date data type.', 16, 1) 
			END
			IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'BIT' BEGIN
				BEGIN TRY
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueBit = cast(columnValueString as bit)
					where columnID = @columnID
				END TRY
				BEGIN CATCH
					RAISERROR('There are string values not compatible with the Boolean data type.', 16, 1) 
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueString = null
				where columnID = @columnID

				-- ensure both bit values are there					
				EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='1', @valueID=@newbitvalueID OUTPUT
				EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='0', @valueID=@newbitvalueID OUTPUT

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (3,4,5,6,9,10)
				) RAISERROR('There are group assignment conditions that are not compatible with the Boolean data type.', 16, 1) 

				-- if was a radio/select/checkbox, we need to convert valueID to value because BIT doesnt store valueID
				IF @olddisplayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = mdcv.columnValueBit
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
				END
			END
			IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'XML' BEGIN
				BEGIN TRY
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueXML = cast(columnValueString as xml)
					where columnID = @columnID
				END TRY
				BEGIN CATCH
					RAISERROR('There are string values not compatible with the XML data type.', 16, 1) 
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueString = null
				where columnID = @columnID

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (1,2,3,4,5,6,9,10)
				) RAISERROR('There are group assignment conditions that are not compatible with the XML data type.', 16, 1) 
			END
			IF @olddataTypeCode = 'DECIMAL2' AND @dataTypeCode = 'STRING' BEGIN
				BEGIN TRY
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = cast(columnValueDecimal2 as varchar(255))
					where columnID = @columnID
				END TRY
				BEGIN CATCH
					RAISERROR('There are decimal values not compatible with the Text String data type.', 16, 1) 
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueDecimal2 = null
				where columnID = @columnID
			END
			IF @olddataTypeCode = 'INTEGER' AND @dataTypeCode = 'STRING' BEGIN
				BEGIN TRY
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = cast(columnValueInteger as varchar(255))
					where columnID = @columnID
				END TRY
				BEGIN CATCH
					RAISERROR('There are whole number values not compatible with the Text String data type.', 16, 1) 
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueInteger = null
				where columnID = @columnID
			END
			IF @olddataTypeCode = 'INTEGER' AND @dataTypeCode = 'DECIMAL2' BEGIN
				BEGIN TRY
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueDecimal2 = cast(columnValueInteger as decimal(9,2))
					where columnID = @columnID
				END TRY
				BEGIN CATCH
					RAISERROR('There are whole number values not compatible with the Decimal Number (2) data type.', 16, 1) 
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueInteger = null
				where columnID = @columnID
			END
			IF @olddataTypeCode = 'INTEGER' AND @dataTypeCode = 'BIT' BEGIN
				BEGIN TRY
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueBit = cast(columnValueInteger as bit)
					where columnID = @columnID
				END TRY
				BEGIN CATCH
					RAISERROR('There are whole number values not compatible with the Boolean data type.', 16, 1) 
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueInteger = null
				where columnID = @columnID

				-- ensure both bit values are there					
				EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='1', @valueID=@newbitvalueID OUTPUT
				EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='0', @valueID=@newbitvalueID OUTPUT

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (3,4,5,6)
				) RAISERROR('There are group assignment conditions that are not compatible with the Boolean data type.', 16, 1) 

				-- if was a radio/select/checkbox, we need to convert valueID to value because BIT doesnt store valueID
				IF @olddisplayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = mdcv.columnValueBit
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
				END
			END
			IF @olddataTypeCode = 'DATE' AND @dataTypeCode = 'STRING' BEGIN
				BEGIN TRY
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = convert(varchar(10),columnValueDate,101)
					where columnID = @columnID
				END TRY
				BEGIN CATCH
					RAISERROR('There are date values not compatible with the Text String data type.', 16, 1) 
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueDate = null
				where columnID = @columnID

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (11,12)
				) RAISERROR('There are group assignment conditions that are not compatible with the Text String data type.', 16, 1) 
			END
			IF @olddataTypeCode = 'BIT' AND @dataTypeCode = 'STRING' BEGIN
				BEGIN TRY
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = cast(columnValueBit as varchar(255))
					where columnID = @columnID
				END TRY
				BEGIN CATCH
					RAISERROR('There are boolean values not compatible with the Text String data type.', 16, 1) 
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueBit = null
				where columnID = @columnID

				-- if going to be radio/select/checkbox, we need to convert value to valueID because BIT doesnt store valueID
				IF @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = mdcv.valueID
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and mdcv.columnvalueString = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
				END
			END
			IF @olddataTypeCode = 'BIT' AND @dataTypeCode = 'DECIMAL2' BEGIN
				BEGIN TRY
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueDecimal2 = cast(columnValueBit as decimal(9,2))
					where columnID = @columnID
				END TRY
				BEGIN CATCH
					RAISERROR('There are boolean values not compatible with the Decimal Number (2) data type.', 16, 1) 
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueBit = null
				where columnID = @columnID

				-- if going to be radio/select/checkbox, we need to convert value to valueID because BIT doesnt store valueID
				IF @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = mdcv.valueID
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and mdcv.columnvalueDecimal2 = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
				END
			END
			IF @olddataTypeCode = 'BIT' AND @dataTypeCode = 'INTEGER' BEGIN
				BEGIN TRY
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueInteger = cast(columnValueBit as int)
					where columnID = @columnID
				END TRY
				BEGIN CATCH
					RAISERROR('There are boolean values not compatible with the Whole Number data type.', 16, 1) 
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueBit = null
				where columnID = @columnID

				-- if going to be radio/select/checkbox, we need to convert value to valueID because BIT doesnt store valueID
				IF @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = mdcv.valueID
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and cast(mdcv.columnvalueInteger as varchar(15)) = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
				END
			END

			UPDATE dbo.ams_virtualGroupConditions
			set dataTypeID = @dataTypeID
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))
		
			UPDATE dbo.ams_virtualGroupConditions
			set [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))
		END

		-- if valueID is not null, there is a def value. 
		-- Anyone who doesnt have a value for this column needs this value.
		IF nullif(@valueID,0) is not null BEGIN
			IF OBJECT_ID('tempdb..#tblMDDEF') IS NOT NULL 
				DROP TABLE #tblMDDEF
			CREATE TABLE #tblMDDEF (memberID int PRIMARY KEY)

			insert into #tblMDDEF (memberID)
			select distinct m.memberid
			from dbo.ams_members as m
			where m.orgID = @orgID
			and m.memberid = m.activememberid
			and m.status <> 'D'
				except
			select distinct md.memberID
			from dbo.ams_memberData as md 
			inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
			where mdcv.columnID = @columnID

			INSERT INTO dbo.ams_memberData (memberid, valueID)
			select memberid, @valueID
			from #tblMDDEF

			UPDATE m
			SET m.dateLastUpdated = getdate()
			FROM dbo.ams_members as m
			INNER JOIN #tblMDDEF as tmp on tmp.memberID = m.memberID

			-- queue processing of member groups (@runSchedule=2 indicates delayed processing)
			-- run this at end, outside of transaction, to speed it up 
			SELECT @conditionIDList = COALESCE(@conditionIDList + ',', '') + cast(c.conditionID as varchar(10)) 
				from dbo.ams_virtualGroupConditions as C
				where c.orgID = @orgID
				and C.fieldcode = 'md_' + Cast(@columnID as varchar(10))
				group by c.conditionID
			IF @conditionIDList is not NULL BEGIN				
				SELECT @memberIDList = COALESCE(@memberIDList + ',', '') + cast(m.memberID as varchar(10)) 
					from #tblMDDEF as m 
					group by m.memberID
			END

			IF OBJECT_ID('tempdb..#tblMDDEF') IS NOT NULL 
				DROP TABLE #tblMDDEF
		END

		
		-- check custom field validation ranges against existing data
		IF @minChars is not null and @maxChars is not null BEGIN
			IF @dataTypeCode = 'STRING' BEGIN
				IF EXISTS(
					select top 1 valueID
					from dbo.ams_memberdatacolumnValues
					where columnID = @columnID
					and len(columnValueString) > 0
					and len(columnValueString) not between @minChars and @maxChars
				)
				RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1) 
			END
			IF @dataTypeCode = 'CONTENTOBJ' BEGIN
				IF EXISTS(
					select top 1 mdcv.valueID
					from dbo.ams_memberdatacolumnValues as mdcv
					inner join dbo.cms_content as c on c.siteResourceID = mdcv.columnValueSiteResourceID
					inner join dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID and cl.languageID = 1
					inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
					where mdcv.columnID = @columnID
					and len(cv.rawContent) > 0
					and len(cv.rawContent) not between @minChars and @maxChars
				)
				RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1) 
			END
		END
		IF @minSelected is not null and @maxSelected is not null BEGIN
			IF EXISTS(select columnID from dbo.ams_memberDataColumns where columnID = @columnID and allowMultiple = 1)
			AND EXISTS(
				select top 1 md.memberid
				from dbo.ams_memberData as md
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
				where mdcv.columnID = @columnID
				group by md.memberid
				having count(*) not between @minSelected and @maxSelected
			)
			RAISERROR('There are existing members that have field options that are outside the data validation range.', 16, 1) 
		END
		IF @minValueInt is not null and @maxValueInt is not null BEGIN
			IF EXISTS(
				select top 1 valueID
				from dbo.ams_memberdatacolumnValues
				where columnID = @columnID
				and columnValueInteger is not null
				and columnValueInteger not between @minValueInt and @maxValueInt
			)
			RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1) 
		END
		IF @minValueDecimal2 is not null and @maxValueDecimal2 is not null BEGIN
			IF EXISTS(
				select top 1 valueID
				from dbo.ams_memberdatacolumnValues
				where columnID = @columnID
				and columnValueDecimal2 is not null
				and columnValueDecimal2 not between @minValueDecimal2 and @maxValueDecimal2
			)
			RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1) 
		END
		IF @minValueDate is not null and @maxValueDate is not null BEGIN
			IF EXISTS(
				select top 1 valueID
				from dbo.ams_memberdatacolumnValues
				where columnID = @columnID
				and columnValueDate is not null
				and columnValueDate not between @minValueDate and @maxValueDate
			)
			RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1) 
		END


		-- if there was a change in columnname
		IF @oldColumnName <> @columnName COLLATE Latin1_General_CS_AI BEGIN
			-- update member fields
			UPDATE dbo.ams_memberFields
			SET dbField = @columnName
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))
		
			-- update virtual group conditions
			UPDATE dbo.ams_virtualGroupConditions
			SET [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))
		END

		IF @oldColumnName <> @columnName OR @dataTypeCode <> @olddataTypeCode BEGIN
			EXEC dbo.ams_createVWMemberData	@orgID=@orgID
		END


	END

	IF @TranCounter = 0
		COMMIT TRAN;

	-- if we need to call processMemberGroups
	IF @conditionIDList is not null BEGIN
		declare @itemGroupUID uniqueidentifier
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberIDList, @conditionIDList=@conditionIDList, @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT
	END

	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO


