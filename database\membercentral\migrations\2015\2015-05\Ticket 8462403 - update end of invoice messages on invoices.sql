use membercentral
GO


-- this will update the end of invoice messages
-- all invoices from ORG tied to INVOICE PROFILE with revenue in GL ACCOUNT
declare @orgID int, @invProfileID int, @glAccountID int
select @orgID = orgID from dbo.organizations where orgcode = 'TX'
select @invProfileID = profileID from dbo.tr_invoiceProfiles where orgID = @orgID and profileName = 'Board Dues'
select @glAccountID = glAccountID from dbo.tr_glAccounts where orgID = @orgID and accountTypeID = 3 and accountCode = '4050-20'

update it
set it.messageContentVersionID = cv2.contentVersionID
from dbo.tr_invoices as i
inner join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
inner join dbo.cms_contentVersions as cv on cv.contentVersionID = it.messageContentVersionID
inner join dbo.cms_contentVersions as cv2 on cv2.contentLanguageID = cv.contentLanguageID and cv2.isActive = 1
where i.invoiceProfileID = @invProfileID
and (t.debitGLAccountID = @glAccountID or t.creditGLAccountID = @glAccountID)
and cv.isActive = 0

GO