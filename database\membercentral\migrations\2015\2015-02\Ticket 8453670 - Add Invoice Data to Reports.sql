use membercentral
GO
ALTER PROC [dbo].[tr_report_payment]
@orgID int,
@datetype varchar(15),
@startDate datetime,
@endDate datetime,
@profileID int,
@lowamt money,
@highamt money,
@batch varchar(400),
@pending bit,
@fld4 varchar(4),
@fld18 varchar(20),
@fld19 varchar(20)

AS

-- if pending = 0, status 3 (pending) do not appear here -- they are not accepted transactions
-- if pending = 1, status 3 appears here and not 1,2
-- status 4 (voidedpending) do not appear here -- they are meant to be completely hidden (deprecated)
-- status 2 (voided) do not appear here

if @profileID = 0
	select @profileID = null

if (@lowamt = @highamt and @highamt = 0) or @highamt = 0 begin
	set @lowamt = null
	set @highamt = null
end

declare @payStatuses TABLE (statusid int)
if @pending = 0
	insert into @payStatuses (statusid)
	select 1
else
	insert into @payStatuses (statusid)
	select 3

-- set date to 11:59:59 of enddate
if @endDate is not null
	select @endDate = dateadd(s,-1,dateadd(day,1,DATEADD(dd, DATEDIFF(dd,0,@endDate), 0)))

declare @tblTrans TABLE (rootTransactionID int, transactionID int, typeID int)

-- payments / refunds directly found
insert into @tblTrans (rootTransactionID, transactionID, typeid)
select t.transactionID, t.transactionID, t.typeid
from dbo.tr_transactions as t
inner join dbo.tr_transactionPayments as tp on tp.transactionID = t.transactionID
inner join dbo.tr_batchTransactions as bt on bt.transactionID = t.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID
inner join @payStatuses as ps on ps.statusID = t.statusID
inner join dbo.tr_paymentHistory as ph on ph.historyID = tp.historyID
inner join dbo.tr_GLAccounts as glPay on glPay.GLAccountID = t.debitGLAccountID and glPay.status <> 'D'
inner join dbo.tr_GLAccounts as glRef on glRef.GLAccountID = t.creditGLAccountID and glRef.status <> 'D'
where (@profileID is null or tp.profileID = @profileID)
and (@lowamt is null or t.amount between @lowamt and @highamt)
and t.ownedByOrgID = @orgID
and (
	(@datetype = 'dateRecorded' and t.dateRecorded between @startdate and @endDate)
	or
	(@datetype = 'transactionDate' and t.transactionDate between @startdate and @endDate)
)
and (@batch is null or b.batchName like '%' + @batch + '%')
and (
	@fld4 is null 
	or 
	right(ph.paymentInfo.value('(//args/fld_4_)[1]','varchar(40)'),4) = @fld4
	or
	right(t.detail,8) = '****' + @fld4
	or
	right(t.detail,8) = 'XXXX' + @fld4
	)
and (@fld18 is null or ph.paymentInfo.value('(//args/fld_18_)[1]','varchar(30)') = @fld18)
and (@fld19 is null or ph.paymentInfo.value('(//args/fld_19_)[1]','varchar(30)') = @fld19)

-- all payments tied to refunds
insert into @tblTrans (rootTransactionID, transactionID, typeid)
select tbl.rootTransactionID, tr.appliedToTransactionID, t.typeID
from @tblTrans as tbl
inner join dbo.tr_relationships as tr on tr.TransactionID = tbl.transactionID
INNER JOIN dbo.tr_relationshipTypes AS rt ON rt.typeID = tr.typeID AND rt.type = 'RefundTrans'
inner join dbo.tr_transactions as t on t.transactionID = tr.appliedToTransactionID
where tbl.typeID = 4
and t.statusID in (1,3)

-- allocations, refunds, write offs, nsf trans applied to payments
insert into @tblTrans (rootTransactionID, transactionID, typeid)
select tbl.rootTransactionID, t.transactionid, t.typeID
from @tblTrans as tbl
inner join dbo.tr_relationships as tr on tr.appliedToTransactionID = tbl.transactionID
INNER JOIN dbo.tr_relationshipTypes AS rt ON rt.typeID = tr.typeID AND rt.type in ('AllocPayTrans','RefundTrans','WriteOffPayTrans','NSFTrans')
inner join dbo.tr_transactions as t on t.transactionID = tr.transactionid
where tbl.typeID = 2
and tbl.rootTransactionID = tbl.transactionID
and t.statusID in (1,3)

; with allGLS as (
	select rgl.GLAccountID, gl.glCode, gl.accountCode, rgl.thePathExpanded
	from dbo.fn_getRecursiveGLAccounts(@orgID) as rgl
	inner join dbo.tr_GLAccounts as gl on gl.glaccountID = rgl.glaccountID
	where gl.status <> 'D'
)
select tmp.rootTransactionID, tmp.transactionid, 
	case 
		when tt.type = 'Allocation' and rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then 'Deallocation'
		else tt.type 
		end as [type],
	t.amount,
	t.transactionDate, 
	case 
		when tt.type = 'Allocation' and rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then 'Deallocation from ' + isnull(tSale.detail,'')
		when tt.type = 'Allocation' then 'Allocation to ' + isnull(tSale.detail,'')
		when tt.type = 'Write Off' then 'Write-Off of ' + t.detail
		else t.detail 
		end as Detail,
	case
		when tt.type = 'Allocation' and rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then rglCredSale.thePathExpanded
		else rglDeb.thePathExpanded 
		end as DEBITACCOUNT,
	case
		when tt.type = 'Allocation' and rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then isnull(rglCredSale.accountCode,'')
		else isnull(rglDeb.accountCode,'')
		end as DEBITACCOUNTCODE,
	case
		when tt.type = 'Allocation' and rglCred.GLCode = 'ACCOUNTSRECEIVABLE' then rglCredSale.thePathExpanded
		else rglCred.thePathExpanded 
		end as CREDITACCOUNT,
	case
		when tt.type = 'Allocation' and rglCred.GLCode = 'ACCOUNTSRECEIVABLE' then isnull(rglCredSale.accountCode,'')
		else isnull(rglCred.accountCode,'')
		end as CREDITACCOUNTCODE,
	case when tt.type = 'Allocation' then mSale2.memberID else m2.memberID end as assignedToMemberID,
	case when tt.type = 'Allocation' then mSale2.lastname + ', ' + mSale2.firstname else m2.lastname + ', ' + m2.firstname end as assignedToMember,
	case when tt.type = 'Allocation' then mSale2.membernumber else m2.membernumber end as assignedToMemberNumber,
	case when tt.type = 'Allocation' then o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) else '' end as [Invoice Number], 
	case when tt.type = 'Allocation' then i.dateBilled else null end as [Invoice Date Billed], 
	case when tt.type = 'Allocation' then i.dateDue else null end as [Invoice Date Due]
from @tblTrans as tmp
inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
inner join dbo.tr_types as tt on tt.typeID = t.typeID
inner join dbo.ams_members as m on m.memberid = t.assignedToMemberID
inner join dbo.ams_members as m2 on m2.memberid = m.activeMemberID
INNER JOIN allGLS as rglDeb on rglDeb.GLAccountID = t.debitGLAccountID
INNER JOIN allGLS as rglCred on rglCred.GLAccountID = t.creditGLAccountID
left outer join dbo.tr_relationships as rAlloc
	inner join dbo.tr_relationshipTypes as rtAlloc on rtAlloc.typeID = rAlloc.typeID and rtAlloc.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as tSale on tSale.transactionID = rAlloc.appliedToTransactionID and tSale.statusID = 1
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSale.transactionID
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	inner join dbo.organizations as o on o.orgID = tSale.ownedByOrgID
	INNER JOIN allGLS as rglCredSale on rglCredSale.GLAccountID = tSale.creditGLAccountID
	inner join dbo.ams_members as mSale on mSale.memberid = tSale.assignedToMemberID
	inner join dbo.ams_members as mSale2 on mSale2.memberid = mSale.activeMemberID
	on rAlloc.transactionID = t.transactionID and t.typeID = 5
order by tmp.rootTransactionID, case when tmp.rootTransactionID = tmp.transactionID then 0 else 1 end, t.dateRecorded

RETURN 0
GO

ALTER PROC [dbo].[tr_report_payment_export]
@orgID int,
@datetype varchar(15),
@startDate datetime,
@endDate datetime,
@profileID int,
@lowamt money,
@highamt money,
@batch varchar(400),
@pending bit,
@fld4 varchar(4),
@fld18 varchar(20),
@fld19 varchar(20),
@filename varchar(400)

AS

-- drop the temp table
IF OBJECT_ID('tempdb..##PayReport') IS NOT NULL 
	DROP TABLE ##PayReport

-- put into temp table
CREATE TABLE ##PayReport (autoid int IDENTITY(1,1), rootTransactionID int, transactionid int, 
	[type] varchar(20), transactionDate datetime, assignedToMemberID int, assignedToMember varchar(max), 
	assignedToMemberNumber varchar(50), detail varchar(max), debitAccount varchar(max), debitAccountCode varchar(200), 
	creditAccount varchar(max), creditAccountCode varchar(200), amount money, [Invoice Number] varchar(50), 
	[Invoice Date Billed] datetime, [Invoice Date Due] datetime)
INSERT INTO ##PayReport (rootTransactionID, transactionid, [type], amount, transactionDate, 
	detail, debitAccount, debitAccountCode, creditAccount, creditAccountCode, 
	assignedToMemberID, assignedToMember, assignedToMemberNumber, [Invoice Number], [Invoice Date Billed], [Invoice Date Due])
EXEC dbo.tr_report_payment @orgID, @datetype, @startdate, @enddate, @profileID, @lowamt, @highamt, @batch, @pending, @fld4, @fld18, @fld19

-- sql for export
DECLARE @fullsql varchar(max)
SELECT @fullsql = '
	SELECT rootTransactionID as [GroupingTransactionID], transactionDate as [Transaction Date], [type] as [Type], 
		detail as [Detail], assignedToMember as [Assigned To], assignedToMemberNumber as [Member Number], 
		amount as [Amount], DebitAccount as [Debit Account], 
		DebitAccountCode as [Debit Account Code], CreditAccount as [Credit Account], 
		CreditAccountCode as [Credit Account Code], [Invoice Number], [Invoice Date Billed], [Invoice Date Due]
	FROM ##PayReport
	ORDER BY autoid'

-- export
EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql

-- drop the temp table
IF OBJECT_ID('tempdb..##PayReport') IS NOT NULL 
	DROP TABLE ##PayReport

RETURN 0
GO

