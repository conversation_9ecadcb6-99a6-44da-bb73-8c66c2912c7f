# Sponsor Grouping Functionality - Test Plan

## Overview

This test plan outlines a systematic approach for verifying the sponsor grouping functionality implementation. It provides detailed test cases for each feature, expected results, and verification steps.

## Test Environment Setup

1. **Database Setup**:
   - Execute the migration script `MCENT-1234 - Sponsor Grouping Functionality.sql`
   - Verify all stored procedures are created successfully
   - Create test event with ID for testing

2. **Application Setup**:
   - Deploy updated CFC files and templates
   - Clear application cache
   - Lo<PERSON> as administrator

## Test Cases

### 1. Group Management

#### TC-1.1: Create Sponsor Group

**Steps:**
1. Navigate to Events > [Test Event] > Sponsors tab
2. Click "Create Sponsor Group" button
3. Verify modal title shows "Create Sponsor Group"
4. Enter group name "Gold Sponsors"
5. Click "Create Group" button

**Expected Results:**
- <PERSON><PERSON> appears with empty group name field and create-specific title
- Modal button shows "Create Group"
- After submission, modal closes
- New group appears in the sponsor list with "0 sponsors" badge
- Group has Edit, Move Up/Down, and Remove buttons

**Verification Points:**
- Check database for new record in sponsorGroupings table
- Verify sponsorGroupingOrder is set correctly (should be 1 for first group)
- Confirm UI updates without page refresh
- Verify consolidated form handles create operation correctly

#### TC-1.2: Edit Sponsor Group

**Steps:**
1. Click Edit button on "Gold Sponsors" group
2. Verify modal title shows "Edit Sponsor Group"
3. Verify group name field is pre-filled with "Gold Sponsors"
4. Change name to "Platinum Sponsors"
5. Click "Save Changes" button

**Expected Results:**
- Modal appears with pre-filled group name and edit-specific title
- Modal button shows "Save Changes"
- After submission, modal closes
- Group name updates in the list
- All sponsors under that group remain associated

**Verification Points:**
- Check database for updated record in sponsorGroupings table
- Verify UI updates without page refresh
- Verify consolidated form handles edit operation correctly

#### TC-1.3: Move Sponsor Group

**Steps:**
1. Create a second group "Silver Sponsors"
2. Create a third group "Bronze Sponsors"
3. Click Move Up button on "Bronze Sponsors" group

**Expected Results:**
- Bronze Sponsors moves above Silver Sponsors
- Move Up button on Platinum Sponsors is invisible/disabled
- Move Down button on Bronze Sponsors becomes visible

**Verification Points:**
- Check database for updated sponsorGroupingOrder values
- Verify UI updates without page refresh
- Confirm all groups maintain their sponsors

#### TC-1.4: Delete Sponsor Group

**Steps:**
1. Click Remove button on an empty group
2. Confirm deletion when prompted

**Expected Results:**
- Confirmation appears using mca_initConfirmButton pattern
- After confirmation, group is removed from list
- Remaining groups reorder appropriately

**Verification Points:**
- Check database to confirm record is deleted
- Verify sponsorGroupingOrder values are resequenced
- Confirm UI updates without page refresh

#### TC-1.5: Attempt to Delete Non-Empty Group

**Steps:**
1. Assign a sponsor to "Platinum Sponsors" group
2. Observe Remove button on the group

**Expected Results:**
- Remove button should be disabled
- Tooltip should indicate "Cannot remove group with sponsors"

**Verification Points:**
- Confirm button has "disabled" class
- Verify tooltip text is correct

### 2. Sponsor Assignment

#### TC-2.1: Assign Sponsor to Group via Edit Modal

**Steps:**
1. Click Edit button on an existing sponsor
2. Select "Platinum Sponsors" from Group Assignment dropdown
3. Click "Save Sponsor" button

**Expected Results:**
- Sponsor appears under Platinum Sponsors group
- Sponsor count badge on group updates
- Sponsor maintains its other properties

**Verification Points:**
- Check database for updated sponsorGroupingID in sponsorsUsage table
- Verify UI updates without page refresh
- Confirm sponsor order within group

#### TC-2.2: Move Sponsor Within Group

**Steps:**
1. Add multiple sponsors to Platinum Sponsors group
2. Click Move Up button on the second sponsor

**Expected Results:**
- Sponsor moves up within the Platinum Sponsors group only
- Order of sponsors in other groups is unchanged

**Verification Points:**
- Check database for updated sponsorOrder values
- Verify only sponsors within same group are affected
- Confirm UI updates without page refresh

#### TC-2.3: Remove Sponsor from Event

**Steps:**
1. Click Remove button on a sponsor within a group
2. Confirm deletion when prompted

**Expected Results:**
- Confirmation appears using mca_initConfirmButton pattern
- After confirmation, sponsor is removed from list
- Group's sponsor count badge updates
- If last sponsor in group, Remove button on group becomes enabled

**Verification Points:**
- Check database to confirm record is removed from sponsorsUsage
- Verify sponsor count is updated
- Confirm UI updates without page refresh

### 3. Edge Cases

#### TC-3.1: Large Number of Groups and Sponsors

**Steps:**
1. Create 10+ sponsor groups
2. Add 5+ sponsors to each group
3. Navigate through the sponsor list

**Expected Results:**
- UI remains responsive
- All groups and sponsors display correctly
- All functionality (move, edit, delete) works as expected

**Verification Points:**
- Check for any JavaScript errors in console
- Verify page load time remains reasonable
- Confirm all operations complete successfully

#### TC-3.2: Special Characters in Group Names

**Steps:**
1. Create a group with name containing special characters: "VIP & C-Suite Sponsors"
2. Edit the group and add more special characters: "VIP & C-Suite Sponsors (★★★)"

**Expected Results:**
- Group creates successfully
- Group edits successfully
- Special characters display correctly

**Verification Points:**
- Check database for correct storage of special characters
- Verify display in UI is correct
- Confirm no JavaScript errors occur

#### TC-3.3: Empty States

**Steps:**
1. Remove all sponsors from an event
2. Remove all groups from an event

**Expected Results:**
- "No Sponsors Selected" message appears when all sponsors are removed
- Groups section disappears when all groups are removed
- Create Group button remains visible

**Verification Points:**
- Verify empty state messages display correctly
- Confirm UI remains functional
- Check that new sponsors/groups can be added after emptying

### 4. Error Handling

#### TC-4.1: Form Validation

**Steps:**
1. Attempt to create a group with empty name
2. Attempt to create a group with extremely long name (>200 characters)

**Expected Results:**
- Validation error appears for empty name
- Long name is truncated or validation error appears

**Verification Points:**
- Verify error messages are clear and visible
- Confirm form doesn't submit with invalid data
- Check that user can correct and resubmit

#### TC-4.2: Concurrent Operations

**Steps:**
1. Open edit modal for a group
2. In another browser tab, delete that group
3. Attempt to save changes in the first tab

**Expected Results:**
- Error message appears indicating group no longer exists
- UI refreshes to show current state

**Verification Points:**
- Verify error handling is graceful
- Confirm no orphaned data is created
- Check that UI recovers to consistent state

## Performance Testing

### P-1: Response Time

**Metrics to Measure:**
- Time to load sponsor list with 50+ sponsors in 10+ groups
- Time to create/edit/delete groups and sponsors
- Time to move sponsors between groups

**Acceptance Criteria:**
- All operations complete in under 2 seconds
- UI remains responsive during operations
- No timeout errors occur

### P-2: Resource Usage

**Metrics to Measure:**
- JavaScript memory usage
- Network request size
- Database query execution time

**Acceptance Criteria:**
- No memory leaks after repeated operations
- Network requests remain under 100KB
- Database queries execute in under 500ms

## Cross-Browser Testing

Test the functionality in the following browsers:
- Chrome (latest)
- Firefox (latest)
- Edge (latest)
- Safari (latest)

## Mobile Responsiveness

Test the functionality on the following devices:
- Desktop (1920x1080)
- Tablet (iPad - 768x1024)
- Mobile (iPhone - 375x667)

## Test Reporting

For each test case, document:
1. Pass/Fail status
2. Actual results vs. expected results
3. Screenshots of any issues
4. Steps to reproduce any failures
5. Environment details (browser, OS, screen size)

## Regression Testing

Verify that existing sponsor functionality continues to work:
1. Adding sponsors to events
2. Removing sponsors from events
3. Editing sponsor details
4. Displaying sponsors on event pages

## Final Acceptance Criteria

The sponsor grouping functionality will be considered successfully implemented when:

1. All test cases pass in all supported browsers
2. Performance metrics meet acceptance criteria
3. No high or critical bugs remain open
4. Documentation is complete and accurate
5. Code review has been completed with no major issues

## Rollback Plan

If critical issues are discovered during testing:

1. Execute the rollback section of the migration script
2. Restore previous versions of CFC files and templates
3. Clear application cache
4. Verify original functionality is restored
