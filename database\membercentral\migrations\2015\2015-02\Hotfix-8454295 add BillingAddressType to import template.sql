use membercentral
GO

ALTER PROC [dbo].[ams_createVWMemberData]
@orgID int

-- tl 7/2010 - ensured this causes views to only contain activememberid data

AS

declare @PVTFull varchar(max)
declare @PVTbit varchar(max), @PVTcontent varchar(max), @PVTdate varchar(max), 
		@PVTdecimal2 varchar(max), @PVTdecimal2Multi varchar(max), @PVTdocument varchar(max), 
		@PVTinteger varchar(max), @PVTintegerMulti varchar(max), @PVTstring varchar(max), 
		@PVTstringMulti varchar(max), @PVTxml varchar(max)
declare @sql varchar(max), @cmd varchar(6)
declare @orgcode varchar(10)

-- get columns for pivots
select @PVTbit = COALESCE(@PVTbit + ',', '') + quoteName(mdc.columnName)
	from dbo.ams_memberdatacolumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
		and mdcdt.dataTypeCode = 'BIT'
	where mdc.orgid = @orgid
	order by mdc.columnName
select @PVTcontent = COALESCE(@PVTcontent + ',', '') + quoteName(mdc.columnName)
	from dbo.ams_memberdatacolumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
		and mdcdt.dataTypeCode = 'CONTENTOBJ'
	where mdc.orgid = @orgid
	order by mdc.columnName
select @PVTdate = COALESCE(@PVTdate + ',', '') + quoteName(mdc.columnName)
	from dbo.ams_memberdatacolumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
		and mdcdt.dataTypeCode = 'DATE'
	where mdc.orgid = @orgid
	order by mdc.columnName
select @PVTdecimal2 = COALESCE(@PVTdecimal2 + ',', '') + quoteName(mdc.columnName)
	from dbo.ams_memberdatacolumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
		and mdcdt.dataTypeCode = 'DECIMAL2'
	where mdc.orgid = @orgid
	and mdc.allowMultiple = 0
	order by mdc.columnName
select @PVTdecimal2Multi = COALESCE(@PVTdecimal2Multi + ',', '') + quoteName(mdc.columnName)
	from dbo.ams_memberdatacolumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
		and mdcdt.dataTypeCode = 'DECIMAL2'
	where mdc.orgid = @orgid
	and mdc.allowMultiple = 1
	order by mdc.columnName
select @PVTdocument = COALESCE(@PVTdocument + ',', '') + quoteName(mdc.columnName)
	from dbo.ams_memberdatacolumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
		and mdcdt.dataTypeCode = 'DOCUMENTOBJ'
	where mdc.orgid = @orgid
	order by mdc.columnName
select @PVTinteger = COALESCE(@PVTinteger + ',', '') + quoteName(mdc.columnName)
	from dbo.ams_memberdatacolumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
		and mdcdt.dataTypeCode = 'INTEGER'
	where mdc.orgid = @orgid
	and mdc.allowMultiple = 0
	order by mdc.columnName
select @PVTintegerMulti = COALESCE(@PVTintegerMulti + ',', '') + quoteName(mdc.columnName)
	from dbo.ams_memberdatacolumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
		and mdcdt.dataTypeCode = 'INTEGER'
	where mdc.orgid = @orgid
	and mdc.allowMultiple = 1
	order by mdc.columnName
select @PVTstring = COALESCE(@PVTstring + ',', '') + quoteName(mdc.columnName)
	from dbo.ams_memberdatacolumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
		and mdcdt.dataTypeCode = 'STRING'
	where mdc.orgid = @orgid
	and mdc.allowMultiple = 0
	order by mdc.columnName
select @PVTstringMulti = COALESCE(@PVTstringMulti + ',', '') + quoteName(mdc.columnName)
	from dbo.ams_memberdatacolumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
		and mdcdt.dataTypeCode = 'STRING'
	where mdc.orgid = @orgid
	and mdc.allowMultiple = 1
	order by mdc.columnName
select @PVTxml = COALESCE(@PVTxml + ',', '') + quoteName(mdc.columnName)
	from dbo.ams_memberdatacolumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
		and mdcdt.dataTypeCode = 'XML'
	where mdc.orgid = @orgid
	order by mdc.columnName


-- construct pvtfull
select @PVTFull = ''
if LEN(@PVTbit) > 0
	select @PVTFull = case when len(@PVTFull) > 0 then @PVTFull + ',' else '' end + replace(@PVTbit,'[','vwbit.[')
if LEN(@PVTcontent) > 0
	select @PVTFull = case when len(@PVTFull) > 0 then @PVTFull + ',' else '' end + replace(@PVTcontent,'[','vwcontent.[')
if LEN(@PVTdate) > 0
	select @PVTFull = case when len(@PVTFull) > 0 then @PVTFull + ',' else '' end + replace(@PVTdate,'[','vwdate.[')
if LEN(@PVTdecimal2) > 0
	select @PVTFull = case when len(@PVTFull) > 0 then @PVTFull + ',' else '' end + replace(@PVTdecimal2,'[','vwdecimal2.[')
if LEN(@PVTdecimal2Multi) > 0
	select @PVTFull = case when len(@PVTFull) > 0 then @PVTFull + ',' else '' end + replace(@PVTdecimal2Multi,'[','vwdecimal2M.[')
if LEN(@PVTdocument) > 0
	select @PVTFull = case when len(@PVTFull) > 0 then @PVTFull + ',' else '' end + replace(@PVTdocument,'[','vwdocument.[')
if LEN(@PVTinteger) > 0
	select @PVTFull = case when len(@PVTFull) > 0 then @PVTFull + ',' else '' end + replace(@PVTinteger,'[','vwinteger.[')
if LEN(@PVTintegerMulti) > 0
	select @PVTFull = case when len(@PVTFull) > 0 then @PVTFull + ',' else '' end + replace(@PVTintegerMulti,'[','vwintegerM.[')
if LEN(@PVTstring) > 0
	select @PVTFull = case when len(@PVTFull) > 0 then @PVTFull + ',' else '' end + replace(@PVTstring,'[','vwstring.[')
if LEN(@PVTstringMulti) > 0
	select @PVTFull = case when len(@PVTFull) > 0 then @PVTFull + ',' else '' end + replace(@PVTstringMulti,'[','vwstringM.[')
if LEN(@PVTxml) > 0
	select @PVTFull = case when len(@PVTFull) > 0 then @PVTFull + ',' else '' end + replace(@PVTxml,'[','vwxml.[')


-- get orgcode
select @orgcode = orgcode from organizations where orgID = @orgid

-- loop over websitetypes to generate sql joins and selects
declare @minWTID int, @minWT varchar(20), @WTsqlJoin varchar(max), @WTsqlSelect varchar(max)
select @WTsqlJoin = '', @WTsqlSelect = ''
select @minWTID = min(websiteTypeID) from dbo.ams_memberWebsiteTypes where orgID = @orgID
while @minWTID is not null BEGIN
	select @minWT = websiteType from dbo.ams_memberWebsiteTypes where websiteTypeID = @minWTID
	select @WTsqlSelect = @WTsqlSelect + char(10) + ', mw' + cast(@minWTID as varchar(10)) + '.website as ' + quoteName(@minWT)
	select @WTsqlJoin = @WTsqlJoin + char(10) + 'left outer join dbo.ams_memberWebsites as mw' + cast(@minWTID as varchar(10)) + ' on mw' + cast(@minWTID as varchar(10)) + '.memberid = m.memberid and mw' + cast(@minWTID as varchar(10)) + '.websiteTypeID = ' + cast(@minWTID as varchar(10)) + ' '
	select @minWTID = min(websiteTypeID) from dbo.ams_memberWebsiteTypes where orgID = @orgID and websiteTypeID > @minWTID
END

-- loop over emailtypes to generate sql joins and selects
declare @minETID int, @minET varchar(20), @ETsqlJoin varchar(max), @ETsqlSelect varchar(max)
select @ETsqlJoin = '', @ETsqlSelect = ''
select @minETID = min(emailTypeID) from dbo.ams_memberEmailTypes where orgID = @orgID
while @minETID is not null BEGIN
	select @minET = emailType from dbo.ams_memberEmailTypes where emailTypeID = @minETID
	select @ETsqlSelect = @ETsqlSelect + char(10) + ', me' + cast(@minETID as varchar(10)) + '.email as ' + quoteName(@minET)
	select @ETsqlJoin = @ETsqlJoin + char(10) + 'left outer join dbo.ams_memberEmails as me' + cast(@minETID as varchar(10)) + ' on me' + cast(@minETID as varchar(10)) + '.memberid = m.memberid and me' + cast(@minETID as varchar(10)) + '.emailTypeID = ' + cast(@minETID as varchar(10)) + ' '
	select @minETID = min(emailTypeID) from dbo.ams_memberEmailTypes where orgID = @orgID and emailTypeID > @minETID
END

-- loop over addresstypes and phone types and district types to generate sql joins and selects
declare @minATID int, @minAT varchar(20), @minPTID int, @minPT varchar(20), @minDTID int, @minDT varchar(20)
declare @hasAttn bit, @hasAddress2 bit, @hasAddress3 bit, @hasCounty bit, @districtMatching bit
declare @ATsqlJoin varchar(max), @ATsqlSelect varchar(max)

declare @tblAddrTypes table (addressTypeID int)
insert into @tblAddrTypes (addressTypeID)
select addressTypeID 
from dbo.ams_memberAddressTypes 
where orgID = @orgID
	union
select 0

select @ATsqlJoin = '', @ATsqlSelect = ''
select @minATID = min(addressTypeID) from @tblAddrTypes
while @minATID is not null BEGIN
	IF @minATID = 0 BEGIN
		SET @minAT = 'Designated Billing'
		select @hasAttn=max(cast(hasAttn as tinyint)), @hasAddress2=max(cast(hasAddress2 as tinyint)), 
			@hasAddress3=max(cast(hasAddress3 as tinyint)), @hasCounty=max(cast(hasCounty as tinyint)), 
			@districtMatching=max(cast(districtMatching as tinyint))
		from dbo.ams_memberAddressTypes 
		where orgID = @orgID
	END ELSE BEGIN
		select @minAT=addressType, @hasAttn=hasAttn, @hasAddress2=hasAddress2, @hasAddress3=hasAddress3, 
			@hasCounty=hasCounty, @districtMatching=districtMatching
		from dbo.ams_memberAddressTypes 
		where addressTypeID = @minATID
	END

	select @ATsqlSelect = @ATsqlSelect + char(10) + 
		case when @hasAttn = 1 then ', ma' + cast(@minATID as varchar(5)) + '.attn as [' + @minAT + '_attn]' else '' end +
		', ma' + cast(@minATID as varchar(5)) + '.address1 as [' + @minAT + '_address1]' +
		case when @hasAddress2 = 1 then ', ma' + cast(@minATID as varchar(5)) + '.address2 as [' + @minAT + '_address2]' else '' end +
		case when @hasAddress3 = 1 then ', ma' + cast(@minATID as varchar(5)) + '.address3 as [' + @minAT + '_address3]' else '' end +
		', ma' + cast(@minATID as varchar(5)) + '.city as [' + @minAT + '_city]' +
		', s' + cast(@minATID as varchar(5)) + '.code as [' + @minAT + '_stateprov]' +
		', ma' + cast(@minATID as varchar(5)) + '.postalCode as [' + @minAT + '_postalCode]' +
		case when @hasCounty = 1 then ', ma' + cast(@minATID as varchar(5)) + '.county as [' + @minAT + '_county]' else '' end +
		', c' + cast(@minATID as varchar(5)) + '.country as [' + @minAT + '_country]'
	IF @minATID = 0	
		select @ATsqlSelect = @ATsqlSelect + ', mat' + cast(@minATID as varchar(5)) + '.addressType as [' + @minAT + '_addressType]'
		
	IF @minATID = 0	BEGIN
		select @ATsqlJoin = @ATsqlJoin + char(10) + 'inner join dbo.ams_memberAddresses as ma' + cast(@minATID as varchar(10)) + ' on ma' + cast(@minATID as varchar(10)) + '.memberid = m.memberid and ma' + cast(@minATID as varchar(10)) + '.addressTypeID = m.billingAddressTypeID '
		select @ATsqlJoin = @ATsqlJoin + char(10) + 'inner join dbo.ams_memberAddressTypes as mat' + cast(@minATID as varchar(10)) + ' on mat' + cast(@minATID as varchar(10)) + '.addressTypeID = ma' + cast(@minATID as varchar(10)) + '.addressTypeID '
	END ELSE
		select @ATsqlJoin = @ATsqlJoin + char(10) + 'left outer join dbo.ams_memberAddresses as ma' + cast(@minATID as varchar(10)) + ' on ma' + cast(@minATID as varchar(10)) + '.memberid = m.memberid and ma' + cast(@minATID as varchar(10)) + '.addressTypeID = ' + cast(@minATID as varchar(10))

	select @ATsqlJoin = @ATsqlJoin + char(10) + 'left outer join dbo.ams_states as s' + cast(@minATID as varchar(10)) + ' on s' + cast(@minATID as varchar(10)) + '.stateid = ma' + cast(@minATID as varchar(10)) + '.stateid '
	select @ATsqlJoin = @ATsqlJoin + char(10) + 'left outer join dbo.ams_countries as c' + cast(@minATID as varchar(10)) + ' on c' + cast(@minATID as varchar(10)) + '.countryid = ma' + cast(@minATID as varchar(10)) + '.countryid '

	select @minPTID = min(phoneTypeID) from dbo.ams_memberPhoneTypes where orgID = @orgID
	while @minPTID is not null BEGIN
		select @minPT = phoneType from dbo.ams_memberPhoneTypes where phoneTypeID = @minPTID

		select @ATsqlSelect = @ATsqlSelect + ', mp' + cast(@minATID as varchar(10)) + cast(@minPTID as varchar(10)) + '.phone as [' + @minAT + '_' + @minPT + ']'
		select @ATsqlJoin = @ATsqlJoin + char(10) + 'left outer join dbo.ams_memberPhones as mp' + cast(@minATID as varchar(10)) + cast(@minPTID as varchar(10)) + ' on mp' + cast(@minATID as varchar(10)) + cast(@minPTID as varchar(10)) + '.addressID = ma' + cast(@minATID as varchar(10)) + '.addressID and mp' + cast(@minATID as varchar(10)) + cast(@minPTID as varchar(10)) + '.phoneTypeID = ' + cast(@minPTID as varchar(10)) + ' '

		select @minPTID = min(phoneTypeID) from dbo.ams_memberPhoneTypes where orgID = @orgID and phoneTypeID > @minPTID
	END

	IF @districtMatching = 1 BEGIN
		select @minDTID = min(districtTypeID) from dbo.ams_memberDistrictTypes where orgID = @orgID
		while @minDTID is not null BEGIN
			select @minDT = districtType from dbo.ams_memberDistrictTypes where districtTypeID = @minDTID

			select @ATsqlSelect = @ATsqlSelect + ',
				madd' + cast(@minATID as varchar(10)) + cast(@minDTID as varchar(10)) + '.vendorValue as [' + @minAT + '_' + @minDT + ']'
			select @ATsqlJoin = @ATsqlJoin + char(10) + '
				left outer join (
					select mad' + cast(@minATID as varchar(10)) + cast(@minDTID as varchar(10)) + '.addressID, dbo.PipeList(mdv' + cast(@minATID as varchar(10)) + cast(@minDTID as varchar(10)) + '.vendorValue) as vendorValue
					from dbo.ams_memberAddressData as mad' + cast(@minATID as varchar(10)) + cast(@minDTID as varchar(10)) + '
					inner join dbo.ams_memberDistrictValues as mdv' + cast(@minATID as varchar(10)) + cast(@minDTID as varchar(10)) + '
						on mdv' + cast(@minATID as varchar(10)) + cast(@minDTID as varchar(10)) + '.valueID = mad' + cast(@minATID as varchar(10)) + cast(@minDTID as varchar(10)) + '.valueid 
						and mdv' + cast(@minATID as varchar(10)) + cast(@minDTID as varchar(10)) + '.districtTypeID = ' + cast(@minDTID as varchar(10)) + '
					group by mad' + cast(@minATID as varchar(10)) + cast(@minDTID as varchar(10)) + '.addressID
				) as madd' + cast(@minATID as varchar(10)) + cast(@minDTID as varchar(10)) + ' on madd' + cast(@minATID as varchar(10)) + cast(@minDTID as varchar(10)) + '.addressID = ma' + cast(@minATID as varchar(10)) + '.addressID'

			select @minDTID = min(districtTypeID) from dbo.ams_memberDistrictTypes where orgID = @orgID and districtTypeID > @minDTID
		END
	END

	select @minATID = min(addressTypeID) from @tblAddrTypes where addressTypeID > @minATID
END

-- loop over professional license types to generate sql joins and selects
declare @minPLTID int, @minPL varchar(200)
declare @PLTsqlJoin varchar(max), @PLTsqlSelect varchar(max)
select @PLTsqlJoin = '', @PLTsqlSelect = ''
select @minPLTID = min(PLTypeID) from dbo.ams_memberProfessionalLicenseTypes where orgID = @orgID
while @minPLTID is not null BEGIN
	select @minPL=PLName
		from dbo.ams_memberProfessionalLicenseTypes 
		where PLTypeID = @minPLTID

	select @PLTsqlSelect = @PLTsqlSelect + char(10) + 
		', mpl' + cast(@minPLTID as varchar(10)) + '.licensenumber as [' + @minPL + '_licenseNumber]' +
		', mpl' + cast(@minPLTID as varchar(10)) + '.activeDate as [' + @minPL + '_activeDate]' +
		', mpls' + cast(@minPLTID as varchar(10)) + '.statusName as [' + @minPL + '_status]'
		
	select @PLTsqlJoin = @PLTsqlJoin + char(10) + 'left outer join dbo.ams_memberProfessionalLicenses as mpl' + cast(@minPLTID as varchar(10)) + ' on mpl' + cast(@minPLTID as varchar(10)) + '.memberid = m.memberid and mpl' + cast(@minPLTID as varchar(10)) + '.PLTypeID = ' + cast(@minPLTID as varchar(10))
	select @PLTsqlJoin = @PLTsqlJoin + char(10) + 'left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls' + cast(@minPLTID as varchar(10)) + ' on mpls' + cast(@minPLTID as varchar(10)) + '.PLStatusID = mpl' + cast(@minPLTID as varchar(10)) + '.PLStatusID and mpls' + cast(@minPLTID as varchar(10)) + '.orgID = ' + cast(@orgID as varchar(10))

	select @minPLTID = min(PLTypeID) from dbo.ams_memberProfessionalLicenseTypes where orgID = @orgID and PLTypeID > @minPLTID
END

IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[vw_memberData_' + @orgcode + ']'))
	SELECT @cmd = 'ALTER'
ELSE
	SELECT @cmd = 'CREATE'

select @sql = @cmd + ' VIEW dbo.vw_memberData_' + @orgcode + ' WITH SCHEMABINDING 
AS '

IF len(@PVTFull) > 0 BEGIN
	select @sql = @sql + '
	select m.memberid' + @WTsqlSelect + @ETsqlSelect + @ATsqlSelect + @PLTsqlSelect + ', 
	' + @PVTFull + '
	from dbo.ams_members as m' + @WTsqlJoin + char(10) + @ETsqlJoin + char(10) + @ATsqlJoin + char(10) + @PLTsqlJoin

	IF len(@PVTbit) > 0
		select @sql = @sql + '
		-- bit
		left outer join (
			select memberid, ' + @PVTbit + '
			from (
				select vw.memberid, mdc.columnName, cast(vw.columnValue as tinyint) as columnValue
				from dbo.vw_memberData__bit as vw WITH (NOEXPAND)
				inner join dbo.ams_memberdatacolumns as mdc on mdc.columnID = vw.columnID
					and mdc.orgid = ' + cast(@orgid as varchar(10)) + '
			) as orgvw
			PIVOT (max(columnValue) FOR columnName in (' + @PVTbit + ')) as pvt
		) as vwbit on vwbit.memberid = m.memberid
		'

	IF len(@PVTcontent) > 0
		select @sql = @sql + '
		-- content
		left outer join (
			select memberid, ' + @PVTcontent + '
			from (
				select md.memberid, mdc.columnName, cv.rawContent
				from dbo.ams_memberData as md
				inner join dbo.ams_members as m on m.memberID = md.memberID and m.memberID = m.activeMemberID
				inner join dbo.ams_memberdataColumnValues as mdcv on mdcv.valueID = md.valueID
				inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID and mdc.orgID = ' + cast(@orgid as varchar(10)) + '
				inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID and mdcdt.dataTypeCode = ''CONTENTOBJ''
				inner join dbo.cms_content as c on c.siteResourceID = mdcv.columnValueSiteResourceID
				inner join dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID and cl.languageID = 1
				inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
			) as orgvw
			PIVOT (min(rawContent) FOR columnName in (' + @PVTcontent + ')) as pvt
		) as vwcontent on vwcontent.memberid = m.memberid
		'

	IF len(@PVTdate) > 0
		select @sql = @sql + '
		-- date
		left outer join (
			select memberid, ' + @PVTdate + '
			from (
				select vw.memberid, mdc.columnName, vw.columnValue
				from dbo.vw_memberData__date as vw WITH (NOEXPAND)
				inner join dbo.ams_memberdatacolumns as mdc on mdc.columnID = vw.columnID
					and mdc.orgid = ' + cast(@orgid as varchar(10)) + '
			) as orgvw
			PIVOT (min(columnValue) FOR columnName in (' + @PVTdate + ')) as pvt
		) as vwdate on vwdate.memberid = m.memberid
		'

	IF len(@PVTdecimal2) > 0
		select @sql = @sql + '
		-- decimal2
		left outer join (
			select memberid, ' + @PVTdecimal2 + '
			from (
				select vw.memberid, mdc.columnName, vw.columnValue
				from dbo.vw_memberData__decimal2 as vw WITH (NOEXPAND)
				inner join dbo.ams_memberdatacolumns as mdc on mdc.columnID = vw.columnID
					and mdc.orgid = ' + cast(@orgid as varchar(10)) + ' 
				where mdc.allowMultiple = 0
			) as orgvw
			PIVOT (min(columnValue) FOR columnName in (' + @PVTdecimal2 + ')) as pvt
		) as vwdecimal2 on vwdecimal2.memberid = m.memberid
		'

	IF len(@PVTdecimal2Multi) > 0
		select @sql = @sql + '
		-- decimal2
		left outer join (
			select memberid, ' + @PVTdecimal2Multi + '
			from (
				select vw.memberid, mdc.columnName, dbo.PipeList(vw.columnValue) as columnValue
				from dbo.vw_memberData__decimal2 as vw WITH (NOEXPAND)
				inner join dbo.ams_memberdatacolumns as mdc on mdc.columnID = vw.columnID
					and mdc.orgid = ' + cast(@orgid as varchar(10)) + ' 
				group by vw.memberid, mdc.columnName
			) as orgvw
			PIVOT (min(columnValue) FOR columnName in (' + @PVTdecimal2Multi + ')) as pvt
		) as vwdecimal2M on vwdecimal2M.memberid = m.memberid
		'

	IF len(@PVTdocument) > 0
		select @sql = @sql + '
		-- document
		left outer join (
			select memberid, ' + @PVTdocument + '
			from (
				select md.memberid, mdc.columnName, dv.filename as columnValue
				from dbo.ams_memberData as md
				inner join dbo.ams_members as m on m.memberID = md.memberID and m.memberID = m.activeMemberID
				inner join dbo.ams_memberdataColumnValues as mdcv on mdcv.valueID = md.valueID
				inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID and mdc.orgID = ' + cast(@orgid as varchar(10)) + '
				inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID and mdcdt.dataTypeCode = ''DOCUMENTOBJ''
				inner join dbo.cms_documents as d on d.siteResourceID = mdcv.columnValueSiteResourceID
				inner join dbo.cms_documentLanguages as dl on dl.documentID = d.documentID and dl.languageID = 1
				inner join dbo.cms_documentVersions as dv on dv.documentLanguageID = dl.documentLanguageID and dv.isActive = 1
			) as orgvw
			PIVOT (min(columnValue) FOR columnName in (' + @PVTdocument + ')) as pvt
		) as vwdocument on vwdocument.memberid = m.memberid
		'

	IF len(@PVTinteger) > 0
		select @sql = @sql + '
		-- integer
		left outer join (
			select memberid, ' + @PVTinteger + '
			from (
				select vw.memberid, mdc.columnName, vw.columnValue
				from dbo.vw_memberData__integer as vw WITH (NOEXPAND)
				inner join dbo.ams_memberdatacolumns as mdc on mdc.columnID = vw.columnID
					and mdc.orgid = ' + cast(@orgid as varchar(10)) + ' 
				where mdc.allowMultiple = 0 
			) as orgvw
			PIVOT (min(columnValue) FOR columnName in (' + @PVTinteger + ')) as pvt
		) as vwinteger on vwinteger.memberid = m.memberid
		'

	IF len(@PVTintegerMulti) > 0
		select @sql = @sql + '
		-- integer
		left outer join (
			select memberid, ' + @PVTintegerMulti + '
			from (
				select vw.memberid, mdc.columnName, dbo.PipeList(vw.columnValue) as columnValue
				from dbo.vw_memberData__integer as vw WITH (NOEXPAND)
				inner join dbo.ams_memberdatacolumns as mdc on mdc.columnID = vw.columnID
					and mdc.orgid = ' + cast(@orgid as varchar(10)) + ' 
				where mdc.allowMultiple = 1 
				group by vw.memberid, mdc.columnName
			) as orgvw
			PIVOT (min(columnValue) FOR columnName in (' + @PVTintegerMulti + ')) as pvt
		) as vwintegerM on vwintegerM.memberid = m.memberid
		'

	IF len(@PVTstring) > 0
		select @sql = @sql + '
		-- string
		left outer join (
			select memberid, ' + @PVTstring + '
			from (
				select vw.memberid, mdc.columnName, vw.columnValue
				from dbo.vw_memberData__string as vw WITH (NOEXPAND)
				inner join dbo.ams_memberdatacolumns as mdc on mdc.columnID = vw.columnID
					and mdc.orgid = ' + cast(@orgid as varchar(10)) + ' 
				where mdc.allowMultiple = 0 
			) as orgvw
			PIVOT (min(columnValue) FOR columnName in (' + @PVTstring + ')) as pvt
		) as vwstring on vwstring.memberid = m.memberid
		'

	IF len(@PVTstringMulti) > 0
		select @sql = @sql + '
		-- string
		left outer join (
			select memberid, ' + @PVTstringMulti + '
			from (
				select vw.memberid, mdc.columnName, dbo.PipeList(vw.columnValue) as columnValue
				from dbo.vw_memberData__string as vw WITH (NOEXPAND)
				inner join dbo.ams_memberdatacolumns as mdc on mdc.columnID = vw.columnID
					and mdc.orgid = ' + cast(@orgid as varchar(10)) + ' 
				where mdc.allowMultiple = 1 
				group by vw.memberid, mdc.columnName
			) as orgvw
			PIVOT (min(columnValue) FOR columnName in (' + @PVTstringMulti + ')) as pvt
		) as vwstringM on vwstringM.memberid = m.memberid
		'

	IF len(@PVTxml) > 0
		select @sql = @sql + '
		-- xml
		left outer join (
			select memberid, ' + @PVTxml + '
			from (
				select vw.memberid, mdc.columnName, cast(vw.columnValue as varchar(max)) as columnValue
				from dbo.vw_memberData__xml as vw WITH (NOEXPAND)
				inner join dbo.ams_memberdatacolumns as mdc on mdc.columnID = vw.columnID
					and mdc.orgid = ' + cast(@orgid as varchar(10)) + '
			) as orgvw
			PIVOT (min(columnValue) FOR columnName in (' + @PVTxml + ')) as pvt
		) as vwxml on vwxml.memberid = m.memberid

		'
	select @sql = @sql + char(10) + '
		where m.orgID = ' + cast(@orgid as varchar(10)) + ' 
		and m.memberid = m.activeMemberID 
		and m.status <> ''D'''

END
ELSE
	select @sql = @sql + '
	select m.memberid' + @WTsqlSelect + @ETsqlSelect + @ATsqlSelect + @PLTsqlSelect + ' 
	from dbo.ams_members as m' + @WTsqlJoin + char(10) + @ETsqlJoin + char(10) + @ATsqlJoin + char(10) + @PLTsqlJoin + ' 
	where m.orgid = ' + cast(@orgid as varchar(10)) + ' 
	and m.memberid = m.activeMemberID 
	and m.status <> ''D''
	'


EXEC(@sql)

RETURN 0
GO

declare @orgID int
select @orgID = min(orgID) from organizations
while @orgID is not null BEGIN
	EXEC ams_createVWMemberData @orgID
	select @orgID = min(orgID) from organizations where orgID > @orgID
END
GO

ALTER PROC [dbo].[ams_getFlattenedMemberDataSQL]
@orgID int,
@importDataMode bit,				-- 1=yes. will limit to top 1, active, no mc fields, no skipped fields
@memberIDList varchar(max),
@sql varchar(max) OUTPUT

AS

SET NOCOUNT ON

declare @memberIDListActive varchar(max), @crlf char(2), @orgCode varchar(10), @hasPrefix bit, @hasMiddleName bit, 
	@hasSuffix bit, @hasProfessionalSuffix bit, @hasCompany bit, @startSQL varchar(max), @WTSelectList varchar(max),
	@ETSelectList varchar(max), @minATID int, @minAT varchar(20), @minPTID int, @minPT varchar(20), @minDTID int, 
	@minDT varchar(20), @hasAttn bit, @hasAddress2 bit, @hasAddress3 bit, @hasCounty bit, @districtMatching bit, 
	@ATsqlSelect varchar(max), @PTSelectList varchar(max), @DTSelectList varchar(max), @minPLTID int, 
	@minPLT varchar(200), @PLTsqlSelect varchar(max), @MDSelectList varchar(max), @showRecordTypes bit
set @sql = ''
set @crlf = char(13) + char(10)


select @orgcode=orgcode, @hasPrefix=hasPrefix, @hasMiddleName=hasMiddleName,
	@hasSuffix=hasSuffix, @hasProfessionalSuffix=hasProfessionalSuffix, @hasCompany=hasCompany
from dbo.organizations 
where orgID = @orgid


-- get list of active memberids if needed
select @memberIDListActive = COALESCE(@memberIDListActive + ',', '') + cast(tmpActive.activeMemberID as varchar(10)) 
from (
	select distinct m.activeMemberID
	from dbo.ams_members m
	inner join dbo.fn_intListToTable(isnull(@memberIDList,''),',') as limitm on limitm.listitem = m.memberid 
) as tmpActive
select @memberIDList = isnull(@memberIDListActive,'')

-- loop over websitetypes
select @WTSelectList = COALESCE(@WTSelectList + ',', '') + 'md.' + quotename(websiteType)
	from dbo.ams_memberWebsiteTypes 
	where orgid = @orgid
	order by websiteTypeOrder
select @WTSelectList = isnull(@WTSelectList,'')

-- loop over emailtypes
select @ETSelectList = COALESCE(@ETSelectList + ',', '') + 'md.' + quotename(emailType)
	from dbo.ams_memberEmailTypes 
	where orgid = @orgid
	order by emailTypeOrder
select @ETSelectList = isnull(@ETSelectList,'')

-- loop over address types and phone types and district types
set @ATsqlSelect = ''
select @minATID = min(addressTypeID) from dbo.ams_memberAddressTypes where orgID = @orgID
while @minATID is not null BEGIN
	select @minAT=addressType, @hasAttn=hasAttn, @hasAddress2=hasAddress2, @hasAddress3=hasAddress3, 
		@hasCounty=hasCounty, @districtMatching=districtMatching
	from dbo.ams_memberAddressTypes 
	where addressTypeID = @minATID

	set @ATsqlSelect = @ATsqlSelect + @crlf + 
		case when @hasAttn = 1 then ', md.' + quotename(@minAT + '_attn') else '' end +
		', md.' + quotename(@minAT + '_address1') +
		case when @hasAddress2 = 1 then ', md.' + quotename(@minAT + '_address2') else '' end +
		case when @hasAddress3 = 1 then ', md.' + quotename(@minAT + '_address3') else '' end +
		', md.' + quotename(@minAT + '_city') +
		', md.' + quotename(@minAT + '_stateprov') +
		', md.' + quotename(@minAT + '_postalCode') +
		case when @hasCounty = 1 then ', md.' + quotename(@minAT + '_county') else '' end +
		', md.' + quotename(@minAT + '_country')
		
	set @PTSelectList = ''
	select @PTSelectList = COALESCE(@PTSelectList + ',', '') + 'md.' + quotename(@minAT + '_' + phoneType)
	from dbo.ams_memberPhoneTypes 
	where orgid = @orgid
	order by phoneTypeOrder

	select @PTSelectList = isnull(@PTSelectList,'')
	set @ATsqlSelect = @ATsqlSelect + @PTSelectList

	IF @districtMatching = 1 AND @importDataMode = 0 BEGIN
		set @DTSelectList = ''
		select @DTSelectList = COALESCE(@DTSelectList + ',', '') + 'md.' + quotename(@minAT + '_' + districtType)
		from dbo.ams_memberDistrictTypes 
		where orgid = @orgid
		order by districtTypeOrder
		
		select @DTSelectList = isnull(@DTSelectList,'')
		set @ATsqlSelect = @ATsqlSelect + @DTSelectList
	END

	select @minATID = min(addressTypeID) from dbo.ams_memberAddressTypes where orgID = @orgID and addressTypeID > @minATID
END

-- loop over professional license types
set @PLTsqlSelect = ''
select @minPLTID = min(PLTypeID) from dbo.ams_memberProfessionalLicenseTypes where orgID = @orgID
while @minPLTID is not null BEGIN
	select @minPLT=PLName
	from dbo.ams_memberProfessionalLicenseTypes 
	where PLTypeID = @minPLTID

	select @PLTsqlSelect = @PLTsqlSelect + @crlf + 
		', md.' + quotename(@minPLT + '_licenseNumber') +
		', md.' + quotename(@minPLT + '_activeDate') +
		', md.' + quotename(@minPLT + '_status')
		
	select @minPLTID = min(PLTypeID) from dbo.ams_memberProfessionalLicenseTypes where orgID = @orgID and PLTypeID > @minPLTID
END

-- loop over member data columns 
select @MDSelectList = COALESCE(@MDSelectList + ',', '') + 'md.' + quotename(mdc.columnName)
	from dbo.ams_memberDataColumns as mdc
	where mdc.orgid = @orgid
	and mdc.skipImport = case when @importDataMode = 1 then 0 else mdc.skipImport end
	order by mdc.columnName
select @MDSelectList = isnull(@MDSelectList,'')

-- include recordType if org contains more than 1 recordtype
select @showRecordTypes = case when count(*) > 1 then cast(1 as bit) else cast(0 as bit) end
from ams_recordTypes rt
where orgID = @orgid

-- construct the rest of the query
-- tl 7/2010 vw_memberdata_ already restricts to activememberid so no need to do it here
select @startSQL = 'select ' + 
	case when @importDataMode = 1 then 'TOP 1 ' else '' end + 
	case when @importDataMode = 0 then 'm.memberid as MemberCentralID, ' else '' end +
	case when (@importDataMode = 0 and @showRecordTypes = 1) then 'rt.recordTypeName, rt.recordTypeID, ' else '' end +
	case when @importDataMode = 0 then 'mt.memberType as MemberCentralMemberType, ' else '' end +
	case when @importDataMode = 0 then 'case m.status when ''A'' then ''Active'' else ''Inactive'' end as MemberCentralStatus, ' else '' end +
	case when @hasPrefix = 1 then 'm.prefix, ' else '' end + 
	'm.firstname, ' + 
	case when @hasMiddleName = 1 then 'm.middlename, ' else '' end + 
	'm.lastname, ' + 
	case when @hasSuffix = 1 then 'm.suffix, ' else '' end + 
	case when @hasProfessionalSuffix = 1 then 'm.professionalSuffix, ' else '' end +
	case when @hasCompany = 1 then 'm.company, ' else '' end +
	'm.memberNumber '

select @sql = @startSQL + 
	@ATsqlSelect + @crlf + 
	case when @importDataMode = 1 then ', md.[Designated Billing_addresstype] as BillingAddressType' + @crlf else '' end + 
	@PLTsqlSelect + @crlf + 
	case when len(@WTSelectList) > 0 then ', ' + @WTSelectList + @crlf else '' end +
	case when len(@ETSelectList) > 0 then ', ' + @ETSelectList + @crlf else '' end +
	case when len(@MDSelectList) > 0 then ', ' + @MDSelectList else '' end + ' 
	from membercentral.dbo.ams_members as m
	left outer join membercentral.dbo.ams_recordTypes rt on rt.recordTypeID = m.recordTypeID
	inner join membercentral.dbo.ams_memberTypes as mt on mt.memberTypeID = m.memberTypeID ' +
	case when len(@memberIDList) > 0 then @crlf + 'inner join membercentral.dbo.fn_intListToTable(''' + @memberIDList + ''','','') as limitm on limitm.listitem = m.memberid ' else '' end + 
	'inner join membercentral.dbo.vw_memberData_' + @orgcode + ' as md on md.memberid = m.memberid ' + 
	case when @importDataMode = 1 then @crlf + '		and m.status = ''A'' ' else '' end

RETURN 0
GO

DROP PROC dbo.ams_exportMemberData
GO

CREATE PROC dbo.ams_exportMemberDataTemplate
@orgID int,
@csvfilename varchar(400)

AS

-- get sql used for export
DECLARE @MDsql varchar(max)
EXEC dbo.ams_getFlattenedMemberDataSQL @orgID=@orgID, @importDataMode=1, @memberIDList='', @sql=@MDsql OUTPUT

-- export data
EXEC dbo.up_exportCSV @csvfilename=@csvfilename, @sql=@MDsql

GO

ALTER PROC [dbo].[ams_importMemberData_holdingToPerm]
@orgID int,
@flatfile varchar(160)

AS

SET NOCOUNT ON

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Starting importMemberData_toPerm'

declare @stepNum tinyint
declare @returnVal int
declare @cmd varchar(400), @sql varchar(max), @cmd2 varchar(8000), @createSQL varchar(max)
declare @logTable TABLE (stepNum tinyint, stepMsg varchar(100), stepDate datetime)
DECLARE @orgCode varchar(10), @hasPrefix bit, @hasMiddleName bit, @hasSuffix bit, @hasProfessionalSuffix bit, @hasCompany bit
select @orgcode=orgcode, @hasPrefix=hasPrefix, @hasMiddleName=hasMiddleName,
	@hasSuffix=hasSuffix, @hasProfessionalSuffix=hasProfessionalSuffix, 
	@hasCompany=hasCompany
	from dbo.organizations 
	where orgID = @orgid

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Preparing to Import Flattened Data'

-- see if files exist
if dbo.fn_fileExists(@flatfile + '.sql') = 0
	or dbo.fn_fileExists(@flatfile + '.txt') = 0
	or dbo.fn_fileExists(@flatfile + '.bcp') = 0
	BEGIN
		EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Flattened Data files do not exist'
		RETURN -1
	END

-- **************
-- read in sql create script and create global temp table
-- **************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Creating table for flattened data'
select @createSQL = replace(dbo.fn_ReadFile(@flatfile + '.sql',0,1),'##xxx','##importMemberData')
IF OBJECT_ID('tempdb..##importMemberData') IS NOT NULL
	EXEC('DROP TABLE ##importMemberData')
EXEC(@createSQL)

-- *******************
-- bcp in data
-- *******************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Importing flattened data'
select @cmd = 'bcp ##importMemberData in ' + @flatfile + '.bcp -f ' + @flatfile + '.txt -n -T -S' + CAST(serverproperty('servername') as varchar(40))
exec master..xp_cmdshell @cmd, NO_OUTPUT

-- *******************
-- immediately put into local temp table and drop global temp (memberid is a holder to be updated later)
-- *******************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Importing into local temp table'
select 0 as memberID, * into #importMemberData from ##importMemberData
IF OBJECT_ID('tempdb..##importMemberData') IS NOT NULL
	EXEC('DROP TABLE ##importMemberData')

-- *******************
-- drop all columns that should be skipped upon import
-- *******************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Dropping Columns to be Skipped'
select @sql = null
select @sql = COALESCE(@sql + char(10), '') + 'IF EXISTS (select * from tempdb.sys.columns where name = ''' + columnName + ''' and object_id = object_id(''tempdb..#importMemberData'')) ALTER TABLE #importMemberData DROP COLUMN [' + columnName + ']'
	from dbo.ams_memberDataColumns
	where orgID = @orgID
	and skipImport = 1
IF LEN(@sql) > 0 
	EXEC(@sql)

-- *******************
-- add back the columns we dont use from members (sigh) so the queries dont break
-- *******************
IF @hasPrefix = 0 AND NOT EXISTS (select [name] from tempdb.sys.columns where name = 'prefix' and object_id = object_id('tempdb..#importMemberData')) 
	ALTER TABLE #importMemberData ADD prefix varchar(4) NOT NULL DEFAULT('');
IF @hasMiddleName = 0 AND NOT EXISTS (select [name] from tempdb.sys.columns where name = 'middlename' and object_id = object_id('tempdb..#importMemberData')) 
	ALTER TABLE #importMemberData ADD middlename varchar(4) NOT NULL DEFAULT('');
IF @hasSuffix = 0 AND NOT EXISTS (select [name] from tempdb.sys.columns where name = 'suffix' and object_id = object_id('tempdb..#importMemberData')) 
	ALTER TABLE #importMemberData ADD suffix varchar(4) NOT NULL DEFAULT('');
IF @hasProfessionalSuffix = 0 AND NOT EXISTS (select [name] from tempdb.sys.columns where name = 'ProfessionalSuffix' and object_id = object_id('tempdb..#importMemberData')) 
	ALTER TABLE #importMemberData ADD ProfessionalSuffix varchar(4) NOT NULL DEFAULT('');
IF @hasCompany = 0 AND NOT EXISTS (select [name] from tempdb.sys.columns where name = 'Company' and object_id = object_id('tempdb..#importMemberData')) 
	ALTER TABLE #importMemberData ADD Company varchar(4) NOT NULL DEFAULT('');

-- ensure no null values in data. saves us from having to blank if null inline.
update #importMemberData set prefix = '' where prefix is null
update #importMemberData set middlename = '' where middlename is null
update #importMemberData set suffix = '' where suffix is null
update #importMemberData set professionalsuffix = '' where professionalsuffix is null
update #importMemberData set company = '' where company is null

-- Add index for queries below
EXEC('CREATE NONCLUSTERED INDEX [idx_importMemberData_' + @orgcode + '] ON #importMemberData ([membernumber] ASC) INCLUDE ( [prefix],[firstname],[middlename],[lastname],[suffix],[company],[ProfessionalSuffix]); ')
EXEC('CREATE STATISTICS [stat_importMemberData_' + @orgcode + '] ON #importMemberData([prefix]); ')

-- *******************
-- bcp out copy of current data 
-- have to do it this way since bcp cannot queryout dynamic sql queries
-- *******************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Exporting backup of existing data'
EXEC dbo.ams_getFlattenedMemberDataSQL @orgID=@orgID, @importDataMode=0, @memberIDList='', @sql=@sql OUTPUT
select @cmd2 = 'bcp "SET QUOTED_IDENTIFIER ON; ' + dbo.fn_ConsolidateWhiteSpace(@sql) + '" queryout ' + replace(@flatfile,'_flat_','_export_') + '.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
exec master..xp_cmdshell @cmd2, NO_OUTPUT

-- *******************
-- process data
-- *******************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Processing flattened data'
SELECT @stepNum = max(stepNum)
	from datatransfer.dbo.ams_memberDataImportStatus
	where orgID = @orgID

BEGIN TRAN

	-- Inactivate all non-guest members (the import contains only active people)
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Inactivate non-guest accounts',getdate())

	UPDATE dbo.ams_members
	SET [status] = 'I'
	WHERE orgID = @orgID
	AND membertypeID = 2
	AND memberID = activeMemberID
	AND [status] = 'A'
		IF @@ERROR <> 0 GOTO on_error

	-- update non-guest members already in table based on membernumber
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Update existing non-guest accounts',getdate())

	UPDATE m
	set m.prefix = flat.prefix,
		m.firstname = flat.firstname,
		m.middlename = flat.middlename,
		m.lastname = flat.lastname,
		m.suffix = flat.suffix,
		m.professionalsuffix = flat.professionalsuffix,
		m.company = flat.company,
		m.memberNumber = flat.membernumber,
		m.status = 'A',
		m.billingAddressTypeID = mat.addressTypeID
	FROM dbo.ams_members as m 
	INNER JOIN #importMemberData as flat on flat.memberNumber = m.memberNumber
		AND m.orgID = @orgID
		AND m.membertypeID = 2
		AND m.memberID = m.activeMemberID
		AND m.status <> 'D'
	INNER JOIN dbo.ams_memberAddressTypes as mat on mat.orgID = @orgID and mat.addressType = flat.BillingAddressType
		IF @@ERROR <> 0 GOTO on_error

	-- add new non-guest members
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Inserting new non-guest accounts',getdate())

	INSERT INTO dbo.ams_members (orgID, prefix, firstname, middlename, lastname, suffix, professionalsuffix, company, memberNumber, [status], dateCreated, membertypeID, dateLastUpdated, billingAddressTypeID)
	SELECT @orgID, flat.prefix, flat.Firstname, flat.middlename, flat.LastName, flat.suffix, flat.professionalsuffix, flat.company, flat.membernumber, 'A', getdate(), 2, getdate(), mat.addressTypeID
	from #importMemberData as flat
	INNER JOIN dbo.ams_memberAddressTypes as mat on mat.orgID = @orgID and mat.addressType = flat.BillingAddressType
	WHERE NOT EXISTS(
		select memberNumber 
		from dbo.ams_members as m 
		where m.memberNumber = flat.memberNumber 
		and m.orgID = @orgID
		and m.memberid = m.activeMemberID 
		and m.membertypeID = 2
		AND m.status <> 'D'
	)
	ORDER BY flat.membernumber
		IF @@ERROR <> 0 GOTO on_error

	UPDATE dbo.ams_members 
	SET activeMemberID = memberID 
	WHERE orgid=@orgid 
	and activeMemberID is null 
		IF @@ERROR <> 0 GOTO on_error

	-- ************
	-- update #importMemberData with memberid for easier reference later
	-- ************
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating flat file with memberid',getdate())

	update flat
	set flat.memberID = m.memberID
	from #importMemberData as flat
	inner join dbo.ams_members as m on m.membernumber = flat.membernumber 
		and m.orgid = @orgid
		and m.memberid = m.activememberid
		and m.memberTypeID = 2
		AND m.status <> 'D'
	IF @@ERROR <> 0 GOTO on_error

	-- *******
	-- websites
	-- loop over website types
	-- update website if it exists for member
	-- add website if it doesnt already exist for member
	-- *******
	declare @minWTID int, @minWT varchar(20), @wtsql varchar(max)
	select @minWTID = min(websiteTypeID) from dbo.ams_memberWebsiteTypes where orgID = @orgID
	while @minWTID is not null BEGIN
		select @minWT = websiteType from dbo.ams_memberWebsiteTypes where websiteTypeID = @minWTID

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Updating existing member websites',getdate())

		select @wtsql = ''
		select @wtsql = @wtsql + 'UPDATE mw '
		select @wtsql = @wtsql + 'SET mw.website = isnull(flat.[' + @minWT + '],'''') '
		select @wtsql = @wtsql + 'FROM dbo.ams_memberWebsites as mw '
		select @wtsql = @wtsql + 'inner join dbo.ams_members as m on m.memberID = mw.memberID '
		select @wtsql = @wtsql + '	AND m.orgID = ' + cast(@orgid as varchar(10)) + ' '
		select @wtsql = @wtsql + '	AND m.memberID = m.activeMemberID '
		select @wtsql = @wtsql + '	AND m.membertypeID = 2 '
		select @wtsql = @wtsql + '	AND m.status <> ''D'' '
		select @wtsql = @wtsql + '  AND mw.websiteTypeID = ' + cast(@minWTID as varchar(10)) + ' '
		select @wtsql = @wtsql + 'INNER JOIN #importMemberData as flat on flat.memberNumber = m.memberNumber '
		EXEC(@wtsql)
		IF @@ERROR <> 0 GOTO on_error

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Inserting new member websites',getdate())

		select @wtsql = ''
		select @wtsql = @wtsql + 'INSERT INTO dbo.ams_memberWebsites (memberID, websiteTypeID, website) '
		select @wtsql = @wtsql + 'select m.memberid, ' + cast(@minWTID as varchar(10)) + ', isnull(flat.[' + @minWT + '],'''') '
		select @wtsql = @wtsql + 'from #importMemberData as flat '
		select @wtsql = @wtsql + 'inner join dbo.ams_members as m on m.membernumber = flat.membernumber '
		select @wtsql = @wtsql + '	and m.orgid = ' + cast(@orgid as varchar(10)) + ' '
		select @wtsql = @wtsql + '	and m.memberID = m.activeMemberID '
		select @wtsql = @wtsql + '	and m.membertypeID = 2 '
		select @wtsql = @wtsql + '	AND m.status <> ''D'' '
		select @wtsql = @wtsql + 'WHERE NOT EXISTS( '
		select @wtsql = @wtsql + '	select websiteID '
		select @wtsql = @wtsql + '	from dbo.ams_memberWebsites as mw '
		select @wtsql = @wtsql + '	where mw.websiteTypeID = ' + cast(@minWTID as varchar(10)) + ' '
		select @wtsql = @wtsql + '	and mw.memberID = m.memberID '
		select @wtsql = @wtsql + ') '
		EXEC(@wtsql)
		IF @@ERROR <> 0 GOTO on_error

		select @minWTID = min(websiteTypeID) from dbo.ams_memberWebsiteTypes where orgID = @orgID and websiteTypeID > @minWTID
	END

	-- *******
	-- emails
	-- loop over email types
	-- update email if it exists for member
	-- add email if it doesnt already exist for member
	-- *******
	declare @minETID int, @minET varchar(20), @etsql varchar(max)
	select @minETID = min(emailTypeID) from dbo.ams_memberEmailTypes where orgID = @orgID
	while @minETID is not null BEGIN
		select @minET = emailType from dbo.ams_memberEmailTypes where emailTypeID = @minETID

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Updating existing member emails',getdate())

		select @etsql = ''
		select @etsql = @etsql + 'UPDATE me '
		select @etsql = @etsql + 'SET me.email = isnull(flat.[' + @minET + '],'''') '
		select @etsql = @etsql + 'FROM dbo.ams_memberEmails as me '
		select @etsql = @etsql + 'inner join dbo.ams_members as m on m.memberID = me.memberID '
		select @etsql = @etsql + '	AND m.orgID = ' + cast(@orgid as varchar(10)) + ' '
		select @etsql = @etsql + '	AND m.memberID = m.activeMemberID '
		select @etsql = @etsql + '	AND m.membertypeID = 2 '
		select @etsql = @etsql + '	AND m.status <> ''D'' '
		select @etsql = @etsql + '  AND me.emailTypeID = ' + cast(@minETID as varchar(10)) + ' '
		select @etsql = @etsql + 'INNER JOIN #importMemberData as flat on flat.memberNumber = m.memberNumber '
		EXEC(@etsql)
		IF @@ERROR <> 0 GOTO on_error

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Inserting new member emails',getdate())

		select @etsql = ''
		select @etsql = @etsql + 'INSERT INTO dbo.ams_memberEmails (memberID, emailTypeID, email) '
		select @etsql = @etsql + 'select m.memberid, ' + cast(@minETID as varchar(10)) + ', isnull(flat.[' + @minET + '],'''') '
		select @etsql = @etsql + 'from #importMemberData as flat '
		select @etsql = @etsql + 'inner join dbo.ams_members as m on m.membernumber = flat.membernumber '
		select @etsql = @etsql + '	and m.orgid = ' + cast(@orgid as varchar(10)) + ' '
		select @etsql = @etsql + '	and m.memberID = m.activeMemberID '
		select @etsql = @etsql + '	and m.membertypeID = 2 '
		select @etsql = @etsql + '	AND m.status <> ''D'' '
		select @etsql = @etsql + 'WHERE NOT EXISTS( '
		select @etsql = @etsql + '	select emailID '
		select @etsql = @etsql + '	from dbo.ams_memberEmails as me '
		select @etsql = @etsql + '	where me.emailTypeID = ' + cast(@minETID as varchar(10)) + ' '
		select @etsql = @etsql + '	and me.memberID = m.memberID '
		select @etsql = @etsql + ') '
		EXEC(@etsql)
		IF @@ERROR <> 0 GOTO on_error

		select @minETID = min(emailTypeID) from dbo.ams_memberEmailTypes where orgID = @orgID and emailTypeID > @minETID
	END

	-- *******
	-- addresses and phones
	-- loop over address types (and then phone types)
	-- update address/phone if it exists for member
	-- add address/phone if it doesnt already exist for member
	-- *******
	declare @minATID int, @minAT varchar(20), @atsql varchar(max), @minPTID int, @minPT varchar(20)
	declare @hasAttn bit, @hasAddress2 bit, @hasAddress3 bit, @hasCounty bit
	declare @updateAddressDate varchar(25)
	select @minATID = min(addressTypeID) from dbo.ams_memberAddressTypes where orgID = @orgID
	while @minATID is not null BEGIN
		select @minAT=addressType, @hasAttn=hasAttn, @hasAddress2=hasAddress2, @hasAddress3=hasAddress3, @hasCounty=hasCounty
			from dbo.ams_memberAddressTypes 
			where addressTypeID = @minATID

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Updating existing member addresses',getdate())

		select @updateAddressDate = convert(varchar(30),getdate(),21)

		select @atsql = ''
		select @atsql = @atsql + 'UPDATE ma '
		select @atsql = @atsql + 'SET '
		IF @hasAttn = 1
			select @atsql = @atsql + '  ma.attn = isnull(flat.[' + @minAT + '_attn],''''), '
		select @atsql = @atsql + '	ma.address1 = isnull(flat.[' + @minAT + '_address1],''''), '
		IF @hasAddress2 = 1		
			select @atsql = @atsql + '	ma.address2 = isnull(flat.[' + @minAT + '_address2],''''), '
		IF @hasAddress3 = 1		
			select @atsql = @atsql + '	ma.address3 = isnull(flat.[' + @minAT + '_address3],''''), '
		select @atsql = @atsql + '	ma.city = isnull(flat.[' + @minAT + '_city],''''), '
		select @atsql = @atsql + '	ma.stateID = s.stateID, '
		select @atsql = @atsql + '	ma.postalCode = isnull(flat.[' + @minAT + '_postalcode],''''), '
		IF @hasCounty = 1		
			select @atsql = @atsql + '	ma.county = isnull(flat.[' + @minAT + '_county],''''), '
		select @atsql = @atsql + '	ma.countryID = c.countryID, '
		select @atsql = @atsql + '	ma.dateLastUpdated = ''' + @updateAddressDate + ''' '
		select @atsql = @atsql + 'FROM dbo.ams_memberAddresses as ma '
		select @atsql = @atsql + 'inner join dbo.ams_members as m on m.memberID = ma.memberID '
		select @atsql = @atsql + '	AND m.orgID = ' + cast(@orgid as varchar(10)) + ' '
		select @atsql = @atsql + '	AND m.memberID = m.activeMemberID '
		select @atsql = @atsql + '	AND m.membertypeID = 2 '
		select @atsql = @atsql + '	AND m.status <> ''D'' '
		select @atsql = @atsql + '  AND ma.addressTypeID = ' + cast(@minATID as varchar(10)) + ' '
		select @atsql = @atsql + 'INNER JOIN #importMemberData as flat on flat.memberNumber = m.memberNumber '
		select @atsql = @atsql + 'left outer join dbo.ams_countries as c on c.country = isnull(flat.[' + @minAT + '_country],'''') '
		select @atsql = @atsql + 'left outer join dbo.ams_states as s on s.code = isnull(flat.[' + @minAT + '_stateprov],'''') and s.countryID = c.countryID '
		select @atsql = @atsql + 'WHERE NOT EXISTS ( '
		select @atsql = @atsql + '		SELECT addressID '
		select @atsql = @atsql + '		FROM dbo.ams_memberAddresses '
		select @atsql = @atsql + '		where addressID = ma.addressID '
		IF @hasAttn = 1
			select @atsql = @atsql + '		AND IsNull(attn,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_attn],'''') '
		select @atsql = @atsql + '		AND IsNull(address1,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_address1],'''') '
		IF @hasAddress2 = 1		
			select @atsql = @atsql + '		AND IsNull(address2,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_address2],'''') '
		IF @hasAddress3 = 1		
			select @atsql = @atsql + '		AND IsNull(address3,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_address3],'''') '
		select @atsql = @atsql + '		AND IsNull(city,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_city],'''') '
		select @atsql = @atsql + '		AND IsNull(stateID,-1) = IsNull(s.stateID,-1) '
		select @atsql = @atsql + '		AND IsNull(postalCode,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_postalcode],'''') '
		IF @hasCounty = 1		
			select @atsql = @atsql + '		AND IsNull(county,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_county],'''') '
		select @atsql = @atsql + '		AND IsNull(countryID,-1) = IsNull(c.countryID,-1) '
		select @atsql = @atsql + ') '
		EXEC(@atsql)
		IF @@ERROR <> 0 GOTO on_error
		
		-- clear address data for addresses we just updated
		delete from dbo.ams_memberAddressData
		where addressID in (
			select ma.addressID
			from dbo.ams_memberAddresses as ma
			inner join dbo.ams_members as m on m.memberID = ma.memberID
			where m.orgID = @orgID
			and ma.dateLastUpdated = @updateAddressDate
		)
		IF @@ERROR <> 0 GOTO on_error

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Inserting new member addresses',getdate())

		select @atsql = ''
		select @atsql = @atsql + 'INSERT INTO dbo.ams_memberAddresses (memberID, addressTypeID, attn, address1, address2, address3, city, stateID, postalCode, county, countryID) '
		select @atsql = @atsql + 'select m.memberid, ' + cast(@minATID as varchar(10)) + ', ' + case when @hasAttn = 1 then 'isnull(flat.[' + @minAT + '_attn],''''), ' else ''''', ' end + 'isnull(flat.[' + @minAT + '_address1],''''), ' + case when @hasAddress2 = 1 then 'isnull(flat.[' + @minAT + '_address2],''''), ' else ''''', ' end + case when @hasAddress3 = 1 then 'isnull(flat.[' + @minAT + '_address3],''''), ' else ''''', ' end + 'isnull(flat.[' + @minAT + '_city],''''), s.stateID, isnull(flat.[' + @minAT + '_postalcode],''''), ' + case when @hasCounty = 1 then 'isnull(flat.[' + @minAT + '_county],''''), ' else ''''', ' end + 'c.countryID '
		select @atsql = @atsql + 'from #importMemberData as flat '
		select @atsql = @atsql + 'inner join dbo.ams_members as m on m.membernumber = flat.membernumber '
		select @atsql = @atsql + '	and m.orgid = ' + cast(@orgid as varchar(10)) + ' '
		select @atsql = @atsql + '	and m.memberID = m.activeMemberID '
		select @atsql = @atsql + '	and m.membertypeID = 2 '
		select @atsql = @atsql + '	AND m.status <> ''D'' '
		select @atsql = @atsql + 'left outer join dbo.ams_countries as c on c.country = isnull(flat.[' + @minAT + '_country],'''') '
		select @atsql = @atsql + 'left outer join dbo.ams_states as s on s.code = isnull(flat.[' + @minAT + '_stateprov],'''') and s.countryID = c.countryID '
		select @atsql = @atsql + 'WHERE NOT EXISTS( '
		select @atsql = @atsql + '	select addressID '
		select @atsql = @atsql + '	from dbo.ams_memberAddresses as ma '
		select @atsql = @atsql + '	where ma.addressTypeID = ' + cast(@minATID as varchar(10)) + ' '
		select @atsql = @atsql + '	and ma.memberID = m.memberID '
		select @atsql = @atsql + ') '
		EXEC(@atsql)
		IF @@ERROR <> 0 GOTO on_error

		select @minPTID = min(phoneTypeID) from dbo.ams_memberPhoneTypes where orgID = @orgID
		while @minPTID is not null BEGIN
			select @minPT = phoneType from dbo.ams_memberPhoneTypes where phoneTypeID = @minPTID

			SELECT @stepNum = @stepNum + 1
			INSERT INTO @logTable (stepNum,stepMsg,stepDate)
			VALUES (@stepNum,'Updating existing member phones',getdate())

			select @atsql = ''
			select @atsql = @atsql + 'UPDATE mp '
			select @atsql = @atsql + 'SET phone = isnull(flat.[' + @minAT + '_' + @minPT + '],'''') '
			select @atsql = @atsql + 'FROM dbo.ams_memberPhones as mp '
			select @atsql = @atsql + 'inner join dbo.ams_memberAddresses as ma on ma.addressID = mp.addressid '
			select @atsql = @atsql + '	and ma.addressTypeID = ' + cast(@minATID as varchar(10)) + ' '
			select @atsql = @atsql + '  and mp.phoneTypeID = ' + cast(@minPTID as varchar(10)) + ' '
			select @atsql = @atsql + 'inner join dbo.ams_members as m on m.memberID = ma.memberID '
			select @atsql = @atsql + '	AND m.orgID = ' + cast(@orgid as varchar(10)) + ' '
			select @atsql = @atsql + '	AND m.memberID = m.activeMemberID '
			select @atsql = @atsql + '	AND m.membertypeID = 2 '
			select @atsql = @atsql + '	AND m.status <> ''D'' '
			select @atsql = @atsql + 'INNER JOIN #importMemberData as flat on flat.memberNumber = m.memberNumber '
			EXEC(@atsql)
			IF @@ERROR <> 0 GOTO on_error

			SELECT @stepNum = @stepNum + 1
			INSERT INTO @logTable (stepNum,stepMsg,stepDate)
			VALUES (@stepNum,'Inserting new member phones',getdate())

			select @atsql = ''
			select @atsql = @atsql + 'INSERT INTO dbo.ams_memberPhones (phoneTypeID, addressid, phone) '
			select @atsql = @atsql + 'select ' + cast(@minPTID as varchar(10)) + ', ma.addressid, isnull(flat.[' + @minAT + '_' + @minPT + '],'''') '
			select @atsql = @atsql + 'from #importMemberData as flat '
			select @atsql = @atsql + 'inner join dbo.ams_members as m on m.membernumber = flat.membernumber '
			select @atsql = @atsql + '	and m.orgid = ' + cast(@orgid as varchar(10)) + ' '
			select @atsql = @atsql + '	and m.memberID = m.activeMemberID '
			select @atsql = @atsql + '	and m.membertypeID = 2 '
			select @atsql = @atsql + '	AND m.status <> ''D'' '
			select @atsql = @atsql + 'inner join dbo.ams_memberAddresses as ma on ma.memberid = m.memberid '
			select @atsql = @atsql + '	and ma.addressTypeID = ' + cast(@minATID as varchar(10)) + ' '
			select @atsql = @atsql + 'WHERE NOT EXISTS( '
			select @atsql = @atsql + '	select phoneID '
			select @atsql = @atsql + '	from dbo.ams_memberPhones as mp '
			select @atsql = @atsql + '	where mp.phoneTypeID = ' + cast(@minPTID as varchar(10)) + ' '
			select @atsql = @atsql + '	and mp.addressID = ma.addressID '
			select @atsql = @atsql + ') '
			EXEC(@atsql)
			IF @@ERROR <> 0 GOTO on_error

			select @minPTID = min(phoneTypeID) from dbo.ams_memberPhoneTypes where orgID = @orgID and phoneTypeID > @minPTID
		END

		select @minATID = min(addressTypeID) from dbo.ams_memberAddressTypes where orgID = @orgID and addressTypeID > @minATID
	END

	-- *******
	-- professional licenses
	-- loop over prof license types
	-- update prof license if it exists for member
	-- add prof license if it doesnt already exist for member
	-- *******
	declare @minPLTID int, @minPLT varchar(200), @pltsql varchar(max)
	select @minPLTID = min(PLTypeID) from dbo.ams_memberProfessionalLicenseTypes where orgID = @orgID
	while @minPLTID is not null BEGIN
		select @minPLT=PLName
			from dbo.ams_memberProfessionalLicenseTypes 
			where PLTypeID = @minPLTID

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Updating existing member professional licenses',getdate())

		select @pltsql = ''
		select @pltsql = @pltsql + 'UPDATE mpl '
		select @pltsql = @pltsql + 'SET '
		select @pltsql = @pltsql + '	mpl.licenseNumber = isnull(flat.[' + @minPLT + '_licenseNumber],''''), '
		select @pltsql = @pltsql + '	mpl.activeDate = nullif(flat.[' + @minPLT + '_activeDate],''''), '
		select @pltsql = @pltsql + '	mpl.PLstatusID = pls.PLStatusID '
		select @pltsql = @pltsql + 'FROM dbo.ams_memberProfessionalLicenses as mpl '
		select @pltsql = @pltsql + 'inner join dbo.ams_members as m on m.memberID = mpl.memberID '
		select @pltsql = @pltsql + '	AND m.orgID = ' + cast(@orgid as varchar(10)) + ' '
		select @pltsql = @pltsql + '	AND m.memberID = m.activeMemberID '
		select @pltsql = @pltsql + '	AND m.membertypeID = 2 '
		select @pltsql = @pltsql + '	AND m.status <> ''D'' '
		select @pltsql = @pltsql + '  AND mpl.PLTypeID = ' + cast(@minPLTID as varchar(10)) + ' '
		select @pltsql = @pltsql + 'INNER JOIN #importMemberData as flat on flat.memberNumber = m.memberNumber '
		select @pltsql = @pltsql + 'LEFT OUTER JOIN dbo.ams_memberProfessionalLicenseStatuses as pls on pls.statusName = isnull(flat.[' + @minPLT + '_status],'''') and pls.orgID = ' + cast(@orgid as varchar(10))
		EXEC(@pltsql)
		IF @@ERROR <> 0 GOTO on_error
		
		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Inserting new member professional licenses',getdate())

		select @pltsql = ''
		select @pltsql = @pltsql + 'INSERT INTO dbo.ams_memberProfessionalLicenses (memberID, PLTypeID, LicenseNumber, ActiveDate, PLstatusID) '
		select @pltsql = @pltsql + 'select m.memberid, ' + cast(@minPLTID as varchar(10)) + ', isnull(flat.[' + @minPLT + '_licenseNumber],''''), nullif(flat.[' + @minPLT + '_activeDate],''''), pls.PLStatusID '
		select @pltsql = @pltsql + 'from #importMemberData as flat '
		select @pltsql = @pltsql + 'LEFT OUTER JOIN dbo.ams_memberProfessionalLicenseStatuses as pls on pls.statusName = isnull(flat.[' + @minPLT + '_status],'''') and pls.orgID = ' + cast(@orgid as varchar(10))
		select @pltsql = @pltsql + 'inner join dbo.ams_members as m on m.membernumber = flat.membernumber '
		select @pltsql = @pltsql + '	and m.orgid = ' + cast(@orgid as varchar(10)) + ' '
		select @pltsql = @pltsql + '	and m.memberID = m.activeMemberID '
		select @pltsql = @pltsql + '	and m.membertypeID = 2 '
		select @pltsql = @pltsql + '	AND m.status <> ''D'' '
		select @pltsql = @pltsql + 'WHERE NOT EXISTS( '
		select @pltsql = @pltsql + '	select PLID '
		select @pltsql = @pltsql + '	from dbo.ams_memberProfessionalLicenses as mpl '
		select @pltsql = @pltsql + '	where mpl.PLTypeID = ' + cast(@minPLTID as varchar(10)) + ' '
		select @pltsql = @pltsql + '	and mpl.memberID = m.memberID '
		select @pltsql = @pltsql + ')  '
		select @pltsql = @pltsql + 'AND ( '
		select @pltsql = @pltsql + '	nullIf(flat.[' + @minPLT + '_licenseNumber],'''') is not null OR '
		select @pltsql = @pltsql + '	nullif(flat.[' + @minPLT + '_activeDate],'''') is not null OR '
		select @pltsql = @pltsql + '	nullIf(flat.[' + @minPLT + '_status],'''') is not null '
		select @pltsql = @pltsql + ')  '

		EXEC(@pltsql)
		IF @@ERROR <> 0 GOTO on_error

		select @minPLTID = min(PLTypeID) from dbo.ams_memberProfessionalLicenseTypes where orgID = @orgID and PLTypeID > @minPLTID
	END

	-- delete licenses where everything is now null (could have been updated to null values above)
	delete
	from dbo.ams_memberProfessionalLicenses
	where nullif(licenseNumber,'') is null
	and activedate is null
	and PlStatusID is null
		IF @@ERROR <> 0 GOTO on_error
	
	-- ***********
	-- memberdata
	-- figure out what should be in memberdata
	-- delete what shouldnt be but is
	-- add what should be but isnt
	-- ***********
	
	-- clear memberdataholding for org
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Cleaning up holding table',getdate())

	DELETE FROM datatransfer.dbo.ams_memberDataHolding where orgID = @orgID
		IF @@ERROR <> 0 GOTO on_error

	-- traverse each column and put into holding table
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Inserting custom data into holding table',getdate())

	DECLARE @minCol varchar(255), @minColID int, @coldataTypeCode varchar(20), @colDisplayTypeCode varchar(20), @allowMultiple bit, @colqry varchar(max)
	SELECT @minColID = MIN(mdc.columnID) 
		FROM dbo.ams_memberDataColumns as mdc
		where mdc.orgID = @orgid
		and mdc.skipImport = 0
	WHILE @minColID is not null BEGIN
		select @minCol=mdc.columnName, @coldataTypeCode=dt.dataTypeCode, @allowMultiple=mdc.allowMultiple
			from dbo.ams_memberDataColumns as mdc
			inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			where mdc.columnID = @minColID
			IF @@ERROR <> 0 GOTO on_error

		IF EXISTS (select * from tempdb.sys.columns where name = @minCol and object_id = object_id('tempdb..#importMemberData')) BEGIN
			SELECT @colqry = 'insert into datatransfer.dbo.ams_memberDataHolding (orgID, rowID, columnID, ' + 
				case @coldataTypeCode 
				when 'STRING' then 'columnValueString'
				when 'DECIMAL2' then 'columnValueDecimal2'
				when 'INTEGER' then 'columnValueInteger'
				when 'DATE' then 'columnValueDate'
				when 'BIT' then 'columnValueBit'
				when 'XML' then 'columnValueXML'
				when 'CONTENTOBJ' then 'columnValueContent'
				when 'DOCUMENTOBJ' then 'columnValueSiteResourceID'
				else 'columnValueString'
				end + ') '
			SELECT @colqry = @colqry + 'select ' + cast(@orgID as varchar(6)) + ', rowID, ' + cast(@minColID as varchar(12)) + ', ' + 
				case when @allowMultiple = 1 then 'tbl.listitem'
				else 
					case @coldataTypeCode 
					when 'STRING' then quotename(@minCol)
					when 'DECIMAL2' then 'cast(' + quotename(@minCol) + ' as decimal(9,2))'
					when 'INTEGER' then 'cast(' + quotename(@minCol) + ' as int)'
					when 'DATE' then 'cast(' + quotename(@minCol) + ' as datetime)'
					when 'BIT' then 'cast(' + quotename(@minCol) + ' as bit)'
					when 'XML' then 'cast(' + quotename(@minCol) + ' as xml)'
					when 'CONTENTOBJ' then quotename(@minCol)
					when 'DOCUMENTOBJ' then 'cast(' + quotename(@minCol) + ' as int)'
					else quotename(@minCol)
					end
				end + ' '
			SELECT @colqry = @colqry + 'from #importMemberData '
			IF @allowMultiple = 1 BEGIN
				SELECT @colqry = @colqry + 'cross apply ' + 
					case @coldataTypeCode 
					when 'STRING' then 'dbo.fn_varcharListToTable(' + quotename(@minCol) + ',''|'') as tbl '
					when 'DECIMAL2' then 'dbo.fn_decimal2ListToTable(' + quotename(@minCol) + ',''|'') as tbl '
					when 'INTEGER' then 'dbo.fn_intListToTable(' + quotename(@minCol) + ',''|'') as tbl '
					end + ' '
			END
			SELECT @colqry = @colqry + 'where ' + 
				case @coldataTypeCode 
				when 'STRING' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(255)),'''') is not null'
				when 'DECIMAL2' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(20)),'''') is not null'
				when 'INTEGER' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(20)),'''') is not null'
				when 'DATE' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(10)),'''') is not null'
				when 'BIT' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(1)),'''') is not null'
				when 'XML' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(max)),'''') is not null'
				when 'CONTENTOBJ' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(max)),'''') is not null'
				when 'DOCUMENTOBJ' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(20)),'''') is not null'
				else 'nullif(cast(' + quotename(@minCol) + ' as varchar(255)),'''') is not null'
				end
			EXEC(@colqry)
			IF @@ERROR <> 0 GOTO on_error
		END
				
		SELECT @minColID = MIN(mdc.columnID) 
			FROM dbo.ams_memberDataColumns as mdc
			where mdc.orgID = @orgid
			and mdc.skipImport = 0
			and columnID > @minColID
	END

	-- handle the content object fields in the holding table
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Handling content object custom data in holding table',getdate())

	-- get first site created for the org. We will put content objects on this site.
	declare @siteID int, @isHTML bit, @dataID bigint, @rc int, @contentID int, @contentSiteResourceID int, @rawContent varchar(max)
	select TOP 1 @siteID=siteID from dbo.sites where orgID = @orgID order by siteID
		IF @@ERROR <> 0 GOTO on_error

	SELECT @minColID = null
	SELECT @minColID = MIN(mdc.columnID) 
		FROM dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgid
		and mdc.skipImport = 0
		and dt.dataTypeCode = 'CONTENTOBJ'
		and EXISTS (select * from tempdb.sys.columns where name = mdc.columnName and object_id = object_id('tempdb..#importMemberData'))
	WHILE @minColID is not null BEGIN
		select @colDisplayTypeCode=dt.displayTypeCode
			from dbo.ams_memberDataColumns as mdc
			inner join dbo.ams_memberDataColumnDisplayTypes as dt on dt.displayTypeID = mdc.displayTypeID
			where mdc.columnID = @minColID
			IF @@ERROR <> 0 GOTO on_error

		-- lookup any existing SiteResourceID
		update mdh
		set mdh.columnValueSiteResourceID = mdcv.columnValueSiteResourceID
		from #importMemberData as imp
		inner join datatransfer.dbo.ams_memberDataHolding as mdh on mdh.rowID = imp.rowID
			and mdh.orgID = @orgID
		inner join membercentral.dbo.ams_memberData as md WITH(NOLOCK) on md.memberid = imp.memberid
		inner join membercentral.dbo.ams_memberDataColumnValues as mdcv WITH(NOLOCK) on mdcv.valueID = md.valueID
			and mdcv.columnID = mdh.columnID
		where mdh.columnValueContent is not null
		and mdcv.columnValueSiteResourceID is not null
		and mdcv.columnID = @minColID
			IF @@ERROR <> 0 GOTO on_error

		IF @colDisplayTypeCode = 'HTMLCONTENT'
			SET @isHTML = 1
		ELSE
			SET @isHTML = 0

		-- update content objects for those that already have one
		select @dataID = null
		select @dataID = min(dataid)
			from datatransfer.dbo.ams_memberDataHolding
			where orgID = @orgID
			and columnValueSiteResourceID is not null
		while @dataID is not null BEGIN
			select @contentSiteResourceID = null, @rawContent = null, @contentID = null
			select @contentSiteResourceID = columnValueSiteResourceID, @rawContent = columnValueContent
				from datatransfer.dbo.ams_memberDataHolding
				where dataID = @dataID
				IF @@ERROR <> 0 GOTO on_error
			select @contentID = contentID
				from dbo.cms_content
				where siteResourceID = @contentSiteResourceID
				and siteID = @siteID

			if @contentID is not null BEGIN
				EXEC @rc = dbo.cms_updateContent @contentID=@contentID, @languageID=1, @isSSL=0, 
					@isHTML=@isHTML, @contentTitle='', @contentDesc='', @rawContent=@rawContent, @memberID=NULL
					IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
			end

			select @dataID = min(dataid)
				from datatransfer.dbo.ams_memberDataHolding
				where orgID = @orgID
				and columnValueSiteResourceID is not null
				and dataid > @dataID
		end

		-- create content objects for those that need it
		select @dataID = null
		select @dataID = min(dataid)
			from datatransfer.dbo.ams_memberDataHolding
			where orgID = @orgID
			and columnValueContent is not null
			and columnValueSiteResourceID is null
		while @dataID is not null BEGIN
			select @rawContent = null, @contentID = null, @contentSiteResourceID = null
			select @rawContent = columnValueContent
				from datatransfer.dbo.ams_memberDataHolding
				where dataID = @dataID
				IF @@ERROR <> 0 GOTO on_error

			EXEC @rc = dbo.cms_createContentField @siteID=@siteID, @isSSL=0, @isHTML=@isHTML, @isActive=1, 
				@languageID=1, @contentTitle='', @contentDesc='', @rawContent=@rawContent, 
				@contentID=@contentID OUTPUT, @contentSiteResourceID=@contentSiteResourceID OUTPUT
				IF @@ERROR <> 0 OR @RC <> 0 OR @contentID = 0 GOTO on_error

			UPDATE datatransfer.dbo.ams_memberDataHolding
			SET columnValueSiteResourceID = @contentSiteResourceID
			WHERE dataID = @dataID
				IF @@ERROR <> 0 GOTO on_error

			select @dataID = min(dataid)
				from datatransfer.dbo.ams_memberDataHolding
				where orgID = @orgID
				and columnValueContent is not null
				and columnValueSiteResourceID is null
				and dataID > @dataID
		end

		SELECT @minColID = MIN(mdc.columnID) 
			FROM dbo.ams_memberDataColumns as mdc
			inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			where mdc.orgID = @orgid
			and mdc.skipImport = 0
			and dt.dataTypeCode = 'CONTENTOBJ'
			and EXISTS (select * from tempdb.sys.columns where name = mdc.columnName and object_id = object_id('tempdb..#importMemberData'))
			and mdc.columnID > @minColID
	END

	-- remove empty values in holding table
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Removing empty values in holding table',getdate())

	DELETE FROM datatransfer.dbo.ams_memberDataHolding
	WHERE orgID = @orgID
	AND (columnValueString = '' or columnValueString is null)
	AND columnValueDecimal2 is null
	AND columnValueInteger is null
	AND columnValueDate is null
	AND columnValueBit is null
	AND columnValueXML is null
	AND columnValueSiteResourceID is null
	AND (columnValueContent = '' or columnValueContent is null)
		IF @@ERROR <> 0 GOTO on_error

	-- add new columnValues	
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Inserting new member data column values',getdate())

	INSERT INTO dbo.ams_memberDataColumnValues (columnID, columnValueString, columnValueDecimal2, columnValueInteger, columnValueDate, columnValueBit, columnValueXML, columnValueSiteResourceID)
	select distinct columnID, columnValueString, columnValueDecimal2, columnValueInteger, columnValueDate, columnValueBit, cast(columnValueXML as varchar(max)), columnValueSiteResourceID
	from datatransfer.dbo.ams_memberDataHolding
	where orgID = @orgid
		except
	select mdcv.columnID, mdcv.columnValueString, mdcv.columnValueDecimal2, mdcv.columnValueInteger, mdcv.columnValueDate, mdcv.columnValueBit, cast(mdcv.columnValueXML as varchar(max)), mdcv.columnValueSiteResourceID
	from dbo.ams_memberDataColumnValues as mdcv
	inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
		and mdc.orgID = @orgID
		IF @@ERROR <> 0 GOTO on_error

	-- update holding with valueIDs
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for string',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode = 'STRING'
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and mdcv.columnValueString = mdh.columnValueString
			IF @@ERROR <> 0 GOTO on_error

	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for decimal',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode = 'DECIMAL2'
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and mdcv.columnValueDecimal2 = mdh.columnValueDecimal2
			IF @@ERROR <> 0 GOTO on_error

	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for integer',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode = 'INTEGER'
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and mdcv.columnValueInteger = mdh.columnValueInteger
			IF @@ERROR <> 0 GOTO on_error

	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for date',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode = 'DATE'
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and mdcv.columnValueDate = mdh.columnValueDate
			IF @@ERROR <> 0 GOTO on_error

	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for bit',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode = 'BIT'
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and mdcv.columnValueBit = mdh.columnValueBit
			IF @@ERROR <> 0 GOTO on_error

	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for xml',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode = 'XML'
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and cast(mdcv.columnValueXML as varchar(max)) = cast(mdh.columnValueXML as varchar(max))
			IF @@ERROR <> 0 GOTO on_error

	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for SRID',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode IN ('CONTENTOBJ','DOCUMENTOBJ')
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and mdcv.columnValueSiteResourceID = mdh.columnValueSiteResourceID
			IF @@ERROR <> 0 GOTO on_error

	-- figure out what should be in memberdata
	-- include imported data and current data for columns that were skipped
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Figure out what should be in memberdata',getdate())

	CREATE TABLE #shouldBeMemberData (dataid int IDENTITY(1,1), memberid int, valueid int)
	INSERT INTO #shouldBeMemberData (memberid, valueID)
	SELECT distinct m.memberid, mdcv.valueid
	from #importMemberData as M
	inner join datatransfer.dbo.ams_memberDataHolding as mdh on mdh.rowid = M.rowid 
		and mdh.orgID = @orgID
	inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdh.columnID 
		and mdcv.valueID = mdh.valueID
		union
	select distinct md.memberid, md.valueid
	from dbo.ams_memberData as md
	inner join dbo.ams_members as m on m.memberid = md.memberID
		and m.orgID = @orgID
		and m.memberid = m.activeMemberID
	inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
	inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
		and mdc.skipImport = 1
		IF @@ERROR <> 0 GOTO on_error

	-- delete what shouldnt be but is
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Removing old custom data for members',getdate())

	DELETE FROM dbo.ams_memberData
	where dataid in (
		select md.dataid
		from dbo.ams_memberData as md
		inner join #importMemberData as flat on md.memberid = flat.memberid
		WHERE NOT EXISTS(
			select dataid 
			from #shouldBeMemberData as sb
			where sb.memberid = md.memberid
			and sb.valueid = md.valueid
		)
	)
		IF @@ERROR <> 0 GOTO on_error
	
	-- add what should be but isnt
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Inserting new custom data for members',getdate())

	INSERT INTO dbo.ams_memberData (memberID, valueID)
	select sb.memberid, sb.valueid
	from #shouldBeMemberData as sb
	WHERE NOT EXISTS(
		select dataid 
		from dbo.ams_memberData as md
		where md.memberid = sb.memberid
		and md.valueid = sb.valueid
	)
		IF @@ERROR <> 0 GOTO on_error

	-- clear #shouldBeMemberData
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Cleaning up #shouldBeMemberData',getdate())

	IF OBJECT_ID('tempdb..#shouldBeMemberData') IS NOT NULL BEGIN
		DROP TABLE #shouldBeMemberData
		IF @@ERROR <> 0 GOTO on_error
	END

	-- clear memberdataholding
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Cleaning up holding table',getdate())

	DELETE FROM datatransfer.dbo.ams_memberDataHolding where orgID = @orgID
		IF @@ERROR <> 0 GOTO on_error

	SELECT @returnVal = 0

IF @@TRANCOUNT > 0 COMMIT TRAN
GOTO on_cleanup

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @returnVal = -2
	GOTO on_cleanup

on_cleanup:
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Inserting entries into log table',getdate())

	INSERT INTO dataTransfer.dbo.ams_memberDataImportStatus (orgID,stepNum,stepMsg,stepDate)
	select @orgID, stepNum, stepMsg, stepDate
	from @logTable
	order by stepNum

	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Cleaning up #importMemberData'
	IF OBJECT_ID('tempdb..#importMemberData') IS NOT NULL
		DROP TABLE #importMemberData

	-- populate member group cache (@runSchedule=1 indicates immediate processing)
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Repopulating Member Group Cache'
	declare @itemGroupUID uniqueidentifier
	EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList='', @conditionIDList='', @runSchedule=1, @itemGroupUID=@itemGroupUID OUTPUT
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Import complete'

	RETURN @returnVal
GO

ALTER PROC [dbo].[ams_importMemberData_tempToHolding]
@orgid int, 
@tmptbl varchar(30),
@pathToExport varchar(100),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Starting importMemberData_toHolding'

DECLARE @prefix varchar(50), @exportcmd varchar(400), @dynSQL nvarchar(max), @flatfile varchar(160),
	@columnID int, @mincol varchar(255), @websiteType varchar(20), @emailType varchar(20), 
	@addressType varchar(20), @good bit, @importFileCol varchar(400), @defaultValueID int, 
	@colDataTypeCode varchar(20), @plType varchar(200), @allownull bit, @OrgColDataLength int,
	@ImpColDataLength int
declare @tblMissingCols TABLE (colName varchar(255))
declare @tblErrors TABLE (rowid int IDENTITY(1,1), msg varchar(600), fatal bit)
declare @tblDataChanged TABLE (rowid int IDENTITY(1,1), msg varchar(600))
declare @tblCounts TABLE (rowid int IDENTITY(1,1), countName varchar(50), countNum int)
declare @tblMemList1 TABLE (memberid int, memberNumber varchar(50), firstname varchar(75), lastname varchar(75))
declare @tblMemList2 TABLE (memberid int, memberNumber varchar(50), firstname varchar(75), lastname varchar(75))
declare @tblMemList3 TABLE (rowid int, memberNumber varchar(50), firstname varchar(75), lastname varchar(75))

DECLARE @orgCode varchar(10), @hasPrefix bit, @usePrefixList bit, @hasMiddleName bit, @hasSuffix bit, @hasProfessionalSuffix bit, @hasCompany bit
select @orgcode=orgcode, @hasPrefix=hasPrefix, @usePrefixList=usePrefixList, @hasMiddleName=hasMiddleName,
	@hasSuffix=hasSuffix, @hasProfessionalSuffix=hasProfessionalSuffix, @hasCompany=hasCompany
	from dbo.organizations 
	where orgID = @orgid

declare @var_tmpCols varchar(20), @var_tmpColsSkipped varchar(30)
select @var_tmpCols = '##tmpCols' + @orgcode
select @var_tmpColsSkipped = '##tmpColsSkipped' + @orgcode

-- cleanup
IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
	EXEC('DROP TABLE ' + @var_tmpCols)
IF OBJECT_ID('tempdb..#tblOrgCols') IS NOT NULL 
	DROP TABLE #tblOrgCols
IF OBJECT_ID('tempdb..#tblImportCols') IS NOT NULL 
	DROP TABLE #tblImportCols
IF OBJECT_ID('tempdb..' + @var_tmpColsSkipped) IS NOT NULL 
	EXEC('DROP TABLE ' + @var_tmpColsSkipped)


-- ******************************** 
-- ensure all columns exist (fatal)
-- ******************************** 
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking Required Columns'

-- this will get the columns that should be there based on current setup
CREATE TABLE #tblOrgCols (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
	COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
	SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
	COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
	CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)

	-- Need the temp table code for the export. replace the first FROM occurence 
	select @dynSQL = ''
	EXEC dbo.ams_getFlattenedMemberDataSQL @orgID=@orgID, @importDataMode=1, @memberIDList='', @sql=@dynSQL OUTPUT
	select @dynSQL = stuff(@dynSQL, charIndex('from membercentral',@dynSQL), len('from membercentral'), 'into ' + @var_tmpCols + ' from membercentral')
	EXEC(@dynSQL)

	-- get cols
	INSERT INTO #tblOrgCols
	EXEC tempdb.dbo.SP_COLUMNS @var_tmpCols
	
	-- cleanup table no longer needed
	IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
		EXEC('DROP TABLE ' + @var_tmpCols)

-- this will get the columns that are actually in the import
CREATE TABLE #tblImportCols (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
	COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
	SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
	COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
	CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)

	-- get cols
	INSERT INTO #tblImportCols
	EXEC tempdb.dbo.SP_COLUMNS @tmptbl

INSERT INTO @tblErrors (msg, fatal)
select 'The column ' + org.column_name + ' is missing from your data.', 1
from #tblOrgCols as org
left outer join #tblImportCols as imp on imp.column_name = org.column_name
where imp.table_name is null

insert into @tblMissingCols(colname)
select org.column_name
from #tblOrgCols as org
left outer join #tblImportCols as imp on imp.column_name = org.column_name
where imp.table_name is null


-- ********************************
-- data type checks (fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking DATE columns'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and dt.dataTypeCode = 'DATE'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'datetime')
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			UPDATE ' + @tmptbl + ' set ' + quotename(@mincol) + ' = null where ' + quotename(@mincol) + ' = '''';
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' datetime null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid dates.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and dt.dataTypeCode = 'DATE'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'datetime')
		where mdc.columnname > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking DECIMAL2 columns'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 0
		and dt.dataTypeCode = 'DECIMAL2'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'decimal')
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			UPDATE ' + @tmptbl + ' SET ' + quotename(@mincol) + ' = replace(' + quotename(@mincol) + ','','','''');
			UPDATE ' + @tmptbl + ' SET ' + quotename(@mincol) + ' = null where ' + quotename(@mincol) + ' = '''';
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' decimal(9,2) null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid decimal values.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and mdc.allowMultiple = 0
			and dt.dataTypeCode = 'DECIMAL2'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'decimal')
		where mdc.columnname > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking DECIMAL2 columns (multiple values)'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 1
		and dt.dataTypeCode = 'DECIMAL2'
		and mdc.columnname not in (select colName from @tblMissingCols)
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' varchar(max) null;

			IF EXISTS (
				select top 1 tbl.listItem
				from ' + @tmptbl + ' 
				cross apply dbo.fn_decimal2ListToTable(' + quotename(@mincol) + ',''|'') as tbl
				where nullif(' + quotename(@mincol) + ','''') is not null
			) set @good = @good
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid decimal values.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and mdc.allowMultiple = 1
			and dt.dataTypeCode = 'DECIMAL2'
			and mdc.columnname not in (select colName from @tblMissingCols)
		where mdc.columnname > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking INTEGER columns'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 0
		and dt.dataTypeCode = 'INTEGER'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and mdc.columnname not in (select column_Name from #tblImportCols where left(type_name,3) = 'int')
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			UPDATE ' + @tmptbl + ' SET ' + quotename(@mincol) + ' = replace(' + quotename(@mincol) + ','','','''');
			UPDATE ' + @tmptbl + ' SET ' + quotename(@mincol) + ' = null where ' + quotename(@mincol) + ' = '''';
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' int null
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid whole number values.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and mdc.allowMultiple = 0
			and dt.dataTypeCode = 'INTEGER'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnname not in (select column_Name from #tblImportCols where left(type_name,3) = 'int')
		where mdc.columnname > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking INTEGER columns (multiple values)'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 1
		and dt.dataTypeCode = 'INTEGER'
		and mdc.columnname not in (select colName from @tblMissingCols)
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' varchar(max) null;

			IF EXISTS (			
				select top 1 tbl.listItem
				from ' + @tmptbl + ' 
				cross apply dbo.fn_intListToTable(' + quotename(@mincol) + ',''|'') as tbl
				where nullif(' + quotename(@mincol) + ','''') is not null
			) set @good = @good
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid whole number values.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and mdc.allowMultiple = 1
			and dt.dataTypeCode = 'INTEGER'
			and mdc.columnname not in (select colName from @tblMissingCols)
		where mdc.columnname > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking BIT columns'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and dt.dataTypeCode = 'BIT'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'bit')
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			UPDATE ' + @tmptbl + ' SET ' + quotename(@mincol) + ' = 1 where ' + quotename(@mincol) + ' = ''TRUE'' OR ' + quotename(@mincol) + ' = ''YES'';
			UPDATE ' + @tmptbl + ' SET ' + quotename(@mincol) + ' = 0 where ' + quotename(@mincol) + ' = ''FALSE'' OR ' + quotename(@mincol) + ' = ''NO'';
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' bit null
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid boolean values.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and dt.dataTypeCode = 'BIT'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'bit')
		where mdc.columnname > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking XML columns'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and dt.dataTypeCode = 'XML'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'xml')
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' xml null
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid XML values.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and dt.dataTypeCode = 'XML'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'xml')
		where mdc.columnname > @mincol
END

-- *********************************************************************
-- ensure all varchar columns match data length requirements (non-fatal)
-- *********************************************************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking VARCHAR columns for lengths'
select @mincol = null
select @mincol = min(column_name)
	from #tblOrgCols
	where type_Name = 'varchar'
	and [length] > 0
	and column_name not in (select colName from @tblMissingCols)
	and column_name in (select column_Name from #tblImportCols where type_Name = 'varchar' and [length] > 0)
while @mincol is not null BEGIN
	select @OrgColDataLength = null, @ImpColDataLength = null
	select @OrgColDataLength = [length] from #tblOrgCols where column_name = @mincol
	select @ImpColDataLength = [length] from #tblImportCols where column_name = @mincol

	-- if length of orgcol < length of importcol, report on data truncation
	IF @OrgColDataLength < @ImpColDataLength BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '') has invalid data for ' + quoteName(@mincol) + '. The maximum number of characters for this column is ' + cast(@OrgColDataLength as varchar(5)) + '.'' as msg, 0 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE len(' + quotename(@mincol) + ') > ' + cast(@OrgColDataLength as varchar(5)) + '
			ORDER BY rowID'
		INSERT INTO @tblErrors (msg, fatal)
		EXEC(@dynSQL)

		IF @@ROWCOUNT > 0 BEGIN
			select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '') will truncate ' + quoteName(@mincol) + ' to fit.'' as msg
				FROM ' + @tmptbl + ' 
				WHERE len(' + quotename(@mincol) + ') > ' + cast(@OrgColDataLength as varchar(5)) + '
				ORDER BY rowID'
			INSERT INTO @tblDataChanged (msg)
			EXEC(@dynSQL)

			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@mincol) + ' = left(' + quotename(@mincol) + ',' + cast(@OrgColDataLength as varchar(5)) + ')
				WHERE len(' + quotename(@mincol) + ') > ' + cast(@OrgColDataLength as varchar(5)) + ' '
			EXEC(@dynSQL)
		END
	END

	-- if length of orgcol <> length of importcol, alter table so it matches.
	IF @OrgColDataLength <> @ImpColDataLength BEGIN
		set @good = 1
		set @dynSQL = '
			set @good = 1
			BEGIN TRY
				ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' varchar(' + cast(@OrgColDataLength as varchar(5)) + ') null;
			END TRY
			BEGIN CATCH
				set @good = 0
			END CATCH'
			exec sp_executesql @dynSQL, N'@good bit output', @good output
		IF @good = 0
			INSERT INTO @tblErrors (msg, fatal)
			VALUES ('The column ' + @mincol + ' could not be expanded to support ' + cast(@OrgColDataLength as varchar(5)) + ' characters.', 1)
	END

	select @mincol = min(column_name)
		from #tblOrgCols
		where type_Name = 'varchar'
		and [length] > 0
		and column_name not in (select colName from @tblMissingCols)
		and column_name in (select column_Name from #tblImportCols where type_Name = 'varchar' and [length] > 0)
		and column_name > @mincol
END


-- ********************************
-- no member number (fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying missing member numbers'

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'memberNumber') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '') is missing a Member Number. Member Numbers are required for all members.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE (memberNumber IS NULL OR ltrim(rtrim(memberNumber)) = '''')
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

-- ********************************
-- dupe member numbers (fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying duplicate member numbers'

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'memberNumber') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''The Member Number '' + isnull(memberNumber,'''') + '' appears '' + cast(count(*) as varchar(10)) + '' times. Member Numbers must be unique.'' as msg, 1 as fatal
			FROM ' + @tmptbl + ' 
			GROUP BY memberNumber
			HAVING COUNT(*) > 1
			ORDER BY 1'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

-- ********************************
-- conflicting member numbers with guest accounts (fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying conflicting member numbers'

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'memberNumber') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''The Member Number '' + isnull(tbl.memberNumber,'''') + '' belongs to an existing guest account.'' as msg, 1 as fatal
			FROM ' + @tmptbl + ' as tbl 
			WHERE isnull(tbl.memberNumber,'''')	<> ''''
			AND EXISTS (
				select memberID
				from dbo.ams_members
				where status <> ''D''
				and orgID = ' + cast(@orgID as varchar(6)) + ' 
				and memberTypeID = 1
				and membernumber = tbl.memberNumber
			)
			ORDER BY 1'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

-- ********************************
-- no first name (fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying missing first names'

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'firstname') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') is missing a First Name. First Names are required for all members.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE (firstname IS NULL OR ltrim(rtrim(firstname)) = '''')
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

-- ********************************
-- no last name (fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying missing last names'

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'lastname') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') is missing a Last Name. Last Names are required for all members.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE (lastname IS NULL OR ltrim(rtrim(lastname)) = '''')
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

-- ********************************
-- bad website data (non-fatal)
-- be nice and clean up their website data first (ensure http:// in front)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying invalid website data'

declare @tldList varchar(max)
set @tldList = 'com|edu|gov|int|mil|net|org|arpa|coop|asia|cat|academy|accountants|active|actor|aero|agency|airforce|archi|army|associates|attorney|auction|audio|autos|band|bargains|beer|best|bid|bike|bio|biz|black|blackfriday|blue|boo|boutique|build|builders|business|buzz|cab|camera|camp|cancerresearch|capital|cards|care|career|careers|cash|catering|center|ceo|channel|cheap|christmas|church|city|claims|cleaning|click|clinic|clothing|club|coach|codes|coffee|college|community|company|computer|condos|construction|consulting|contractors|cooking|cool|country|credit|creditcard|cricket|cruises|dad|dance|dating|day|deals|degree|delivery|democrat|dental|dentist|diamonds|diet|digital|direct|directory|discount|domains|eat|education|email|energy|engineer|engineering|equipment|esq|estate|events|exchange|expert|exposed|fail|farm|feedback|finance|financial|fish|fishing|fitness|flights|florist|fly|foo|forsale|foundation|fund|furniture|futbol|gallery|gift|gifts|gives|glass|global|gop|graphics|green|gripe|guide|guitars|guru|healthcare|help|here|hiphop|hiv|holdings|holiday|homes|horse|host|hosting|house|how|info|ing|ink|insure|international|investments|jobs|kim|kitchen|land|lawyer|lease|legal|lgbt|life|lighting|limited|limo|link|loans|lotto|luxe|luxury|management|market|marketing|media|meet|meme|memorial|menu|mobi|moe|money|mortgage|motorcycles|mov|museum|name|navy|network|new|ngo|ninja|ong|onl|ooo|organic|partners|parts|party|pharmacy|photo|photography|photos|physio|pics|pictures|pink|pizza|place|plumbing|poker|post|press|pro|productions|prof|properties|property|qpon|recipes|red|rehab|ren|rentals|repair|report|republican|reviews|rich|rip|rocks|rodeo|rsvp|science|services|sexy|shoes|singles|social|software|solar|solutions|space|supplies|supply|support|surf|surgery|systems|tattoo|tax|technology|tel|tips|tires|today|tools|top|town|toys|trade|training|travel|university|vacations|vet|villas|vision|vodka|vote|voting|voyage|wang|watch|webcam|website|wed|wiki|works|world|wtf|xxx|xyz|zone'

select @websiteType = min(websiteType)
	FROM dbo.ams_memberWebsiteTypes
	WHERE orgID = @orgID
	and websiteType NOT IN (select colName from @tblMissingCols)
while @websiteType is not null BEGIN

	select @dynSQL = 'update ' + @tmptbl + ' 
		set ' + quotename(@websiteType) + ' = ''http://'' + ' + quotename(@websiteType) + ' 
		where len(' + quotename(@websiteType) + ') > 0 and left(' + quotename(@websiteType) + ',4) <> ''http'''
	EXEC(@dynSQL)

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the website ' + @websiteType + ': '' + ' + quoteName(@websiteType) + ' as msg, 0 as fatal
		FROM ' + @tmptbl + ' 
		WHERE len(' + quotename(@websiteType) + ') > 0 and dbo.fn_RegExReplace(' + quotename(@websiteType) + ',''^(http|https|ftp)\://([a-zA-Z0-9\.\-]+(\:[a-zA-Z0-9\.&amp;%\$\-]+)*@)*((25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9])\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[0-9])|localhost|([a-zA-Z0-9\-]+\.)*[a-zA-Z0-9\-]+\.(' + @tldList + '|[a-zA-Z]{2}))(\:[0-9]+)*(/($|[a-zA-Z0-9\.\,\?\''''\\\+&amp;%\!$#\=~_\-]+))*$'','''') <> '''' 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the website ' + @websiteType + '.'' as msg
			FROM ' + @tmptbl + ' 
			WHERE len(' + quotename(@websiteType) + ') > 0 and dbo.fn_RegExReplace(' + quotename(@websiteType) + ',''^(http|https|ftp)\://([a-zA-Z0-9\.\-]+(\:[a-zA-Z0-9\.&amp;%\$\-]+)*@)*((25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9])\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[0-9])|localhost|([a-zA-Z0-9\-]+\.)*[a-zA-Z0-9\-]+\.(' + @tldList + '|[a-zA-Z]{2}))(\:[0-9]+)*(/($|[a-zA-Z0-9\.\,\?\''''\\\+&amp;%\!$#\=~_\-]+))*$'','''') <> '''' 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@websiteType) + ' = '''' 
			WHERE len(' + quotename(@websiteType) + ') > 0 and dbo.fn_RegExReplace(' + quotename(@websiteType) + ',''^(http|https|ftp)\://([a-zA-Z0-9\.\-]+(\:[a-zA-Z0-9\.&amp;%\$\-]+)*@)*((25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9])\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[0-9])|localhost|([a-zA-Z0-9\-]+\.)*[a-zA-Z0-9\-]+\.(' + @tldList + '|[a-zA-Z]{2}))(\:[0-9]+)*(/($|[a-zA-Z0-9\.\,\?\''''\\\+&amp;%\!$#\=~_\-]+))*$'','''') <> '''' '
		EXEC(@dynSQL)
	END

	select @websiteType = min(websiteType)
		FROM dbo.ams_memberWebsiteTypes
		WHERE orgID = @orgID
		and websiteType NOT IN (select colName from @tblMissingCols)
		AND websiteType > @websiteType
END

-- ********************************
-- bad email data (non-fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying invalid e-mail data'

select @emailType = min(emailType)
	FROM dbo.ams_memberEmailTypes
	WHERE orgID = @orgID
	and emailType NOT IN (select colName from @tblMissingCols)
while @emailType is not null BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the e-mail ' + @emailType + ': '' + ' + quotename(@emailType) + ' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE len(' + quotename(@emailType) + ') > 0 and dbo.fn_RegExReplace(' + quotename(@emailType) + ',''^[a-zA-Z_0-9-''''\&\+~]+(\.[a-zA-Z_0-9-''''\&\+~]+)*@([a-zA-Z_0-9-]+\.)+[a-zA-Z]{2,7}$'','''') <> '''' 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the e-mail ' + @emailType + '.'' as msg
			FROM ' + @tmptbl + ' 
			WHERE len(' + quotename(@emailType) + ') > 0 and dbo.fn_RegExReplace(' + quotename(@emailType) + ',''^[a-zA-Z_0-9-''''\&\+~]+(\.[a-zA-Z_0-9-''''\&\+~]+)*@([a-zA-Z_0-9-]+\.)+[a-zA-Z]{2,7}$'','''') <> '''' 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@emailType) + ' = '''' 
			WHERE len(' + quotename(@emailType) + ') > 0 and dbo.fn_RegExReplace(' + quotename(@emailType) + ',''^[a-zA-Z_0-9-''''\&\+~]+(\.[a-zA-Z_0-9-''''\&\+~]+)*@([a-zA-Z_0-9-]+\.)+[a-zA-Z]{2,7}$'','''') <> '''' '
		EXEC(@dynSQL)
	END

	select @emailType = min(emailType)
		FROM dbo.ams_memberEmailTypes
		WHERE orgID = @orgID
		and emailType NOT IN (select colName from @tblMissingCols)
		AND emailType > @emailType
END


-- ********************************
-- bad billing address type (non-fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying invalid billing address types'

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'BillingAddressType') BEGIN
	declare @defaultBillingAddressType varchar(20)
	select @defaultBillingAddressType = addressType from dbo.ams_memberAddressTypes where orgID = @orgID and addressTypeOrder = 1

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(tmp.rowID as varchar(10)) + '' ('' + isnull(tmp.firstname,'''') + '' '' + isnull(tmp.lastname,'''') + '') has invalid data for BillingAddressType.'' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as tmp
		LEFT OUTER JOIN membercentral.dbo.ams_memberAddressTypes as mat on mat.orgID = ' + cast(@orgID as varchar(10)) + ' and mat.addressType = tmp.BillingAddressType 
		WHERE len(tmp.BillingAddressType) > 0 
		AND mat.addressTypeID is null 
		ORDER BY tmp.rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(tmp.rowID as varchar(10)) + '' ('' + isnull(tmp.firstname,'''') + '' '' + isnull(tmp.lastname,'''') + '') will change the BillingAddressType from '' + tmp.BillingAddressType + '' to ' + @defaultBillingAddressType + ''' as msg
			FROM ' + @tmptbl + ' as tmp
			LEFT OUTER JOIN membercentral.dbo.ams_memberAddressTypes as mat on mat.orgID = ' + cast(@orgID as varchar(10)) + ' and mat.addressType = tmp.BillingAddressType 
			WHERE len(tmp.BillingAddressType) > 0 
			AND mat.addressTypeID is null 
			ORDER BY tmp.rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE tmp
			SET tmp.BillingAddressType = '''' 
			FROM ' + @tmptbl + ' as tmp
			LEFT OUTER JOIN membercentral.dbo.ams_memberAddressTypes as mat on mat.orgID = ' + cast(@orgID as varchar(10)) + ' and mat.addressType = tmp.BillingAddressType 
			WHERE len(tmp.BillingAddressType) > 0 
			AND mat.addressTypeID is null '
		EXEC(@dynSQL)
	END

	-- if blank, existing members should retain their existing value.
	select @dynSQL = 'UPDATE tmp 
		SET tmp.BillingAddressType = mat.addressType 
		FROM ' + @tmptbl + ' as tmp 
		INNER JOIN dbo.ams_members as m on m.memberNumber = tmp.memberNumber 
			AND m.orgID = ' + cast(@orgID as varchar(10)) + ' 
			AND m.membertypeID = 2 
			AND m.memberID = m.activeMemberID 
			AND m.status <> ''D'' 
		INNER JOIN dbo.ams_memberAddressTypes as mat on mat.addressTypeID = m.billingAddressTypeID 
		WHERE tmp.BillingAddressType = '''''
	EXEC(@dynSQL)

	-- if blank, new members should get the default value. 
	select @dynSQL = 'UPDATE ' + @tmptbl + ' 
		SET BillingAddressType = ''' + @defaultBillingAddressType + '''  
		WHERE BillingAddressType = '''''
	EXEC(@dynSQL)
END


-- ********************************
-- bad address data (non-fatal)
--  - country specified but not valid country
--  - state and country specified but not valid state
--  - state specified but no country specified
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying invalid address data'

select @addressType = min(addressType)
	FROM dbo.ams_memberAddressTypes
	WHERE orgID = @orgID
	and (
		addressType + '_stateprov' NOT IN (select colName from @tblMissingCols)
		or
		addressType + '_country' NOT IN (select colName from @tblMissingCols)
	)
while @addressType is not null BEGIN

	-- country specified but not valid country
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for ' + @addressType + '_country' + ': '' + ' + quotename(@addressType + '_country') + ' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as tmp 
		WHERE len(isnull(tmp.' + quotename(@addressType + '_country') + ','''')) > 0 
		and not exists (select countryID from dbo.ams_countries where country = isnull(tmp.' + quotename(@addressType + '_country') + ',''''))
		ORDER BY tmp.rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the ' + @addressType + '_country' + '.'' as msg
			FROM ' + @tmptbl + ' as tmp 
			WHERE len(isnull(tmp.' + quotename(@addressType + '_country') + ','''')) > 0 
			and not exists (select countryID from dbo.ams_countries where country = isnull(tmp.' + quotename(@addressType + '_country') + ',''''))
			ORDER BY tmp.rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@addressType + '_country') + ' = '''' 
			WHERE len(isnull(' + quotename(@addressType + '_country') + ','''')) > 0 
			and not exists (select countryID from dbo.ams_countries where country = isnull(' + quotename(@addressType + '_country') + ','''')) '
		EXEC(@dynSQL)
	END

	-- state and country specified but not valid state
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for ' + @addressType + '_stateprov' + ': '' + ' + quotename(@addressType + '_stateprov') + ' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as tmp 
		WHERE len(isnull(tmp.' + quotename(@addressType + '_stateprov') + ','''')) > 0 
		and len(isnull(tmp.' + quotename(@addressType + '_country') + ','''')) > 0 
		and not exists (
			select s.stateID
			from dbo.ams_states as s
			inner join dbo.ams_countries as c on c.countryID = s.countryID
			where s.code = isnull(tmp.' + quotename(@addressType + '_stateprov') + ','''') 
			and c.country = isnull(tmp.' + quotename(@addressType + '_country') + ','''') 
		)
		and exists (select countryID from dbo.ams_countries where country = isnull(tmp.' + quotename(@addressType + '_country') + ',''''))
		ORDER BY tmp.rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the ' + @addressType + '_stateprov' + '.'' as msg
			FROM ' + @tmptbl + ' as tmp 
			WHERE len(isnull(tmp.' + quotename(@addressType + '_stateprov') + ','''')) > 0 
			and len(isnull(tmp.' + quotename(@addressType + '_country') + ','''')) > 0 
			and not exists (
				select s.stateID
				from dbo.ams_states as s
				inner join dbo.ams_countries as c on c.countryID = s.countryID
				where s.code = isnull(tmp.' + quotename(@addressType + '_stateprov') + ','''') 
				and c.country = isnull(tmp.' + quotename(@addressType + '_country') + ','''') 
			)
			and exists (select countryID from dbo.ams_countries where country = isnull(tmp.' + quotename(@addressType + '_country') + ',''''))
			ORDER BY tmp.rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@addressType + '_stateprov') + ' = '''' 
			WHERE len(isnull(' + quotename(@addressType + '_stateprov') + ','''')) > 0 
			and len(isnull(' + quotename(@addressType + '_country') + ','''')) > 0 
			and not exists (
				select s.stateID
				from dbo.ams_states as s
				inner join dbo.ams_countries as c on c.countryID = s.countryID
				where s.code = isnull(' + quotename(@addressType + '_stateprov') + ','''') 
				and c.country = isnull(' + quotename(@addressType + '_country') + ','''') 
			)
			and exists (select countryID from dbo.ams_countries where country = isnull(' + quotename(@addressType + '_country') + ','''')) '
		EXEC(@dynSQL)
	END

	-- state specified but no country specified
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had a missing country value for ' + @addressType + '_stateprov' + ': '' + ' + quotename(@addressType + '_stateprov') + ' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as tmp 
		WHERE len(isnull(tmp.' + quotename(@addressType + '_stateprov') + ','''')) > 0 
		and len(isnull(tmp.' + quotename(@addressType + '_country') + ','''')) = 0 
		ORDER BY tmp.rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the ' + @addressType + '_stateprov' + '.'' as msg
			FROM ' + @tmptbl + ' as tmp 
			WHERE len(isnull(tmp.' + quotename(@addressType + '_stateprov') + ','''')) > 0 
			and len(isnull(tmp.' + quotename(@addressType + '_country') + ','''')) = 0 
			ORDER BY tmp.rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@addressType + '_stateprov') + ' = '''' 
			WHERE len(isnull(' + quotename(@addressType + '_stateprov') + ','''')) > 0 
			and len(isnull(' + quotename(@addressType + '_country') + ','''')) = 0 '
		EXEC(@dynSQL)
	END

	select @addressType = min(addressType)
		FROM dbo.ams_memberAddressTypes
		WHERE orgID = @orgID
		and (
			addressType + '_stateprov' NOT IN (select colName from @tblMissingCols)
			or
			addressType + '_country' NOT IN (select colName from @tblMissingCols)
		)
		AND addressType > @addressType
END

-- ********************************
-- bad prefix data (non-fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying invalid prefix data'

IF @hasPrefix = 1 and @usePrefixList = 1 and NOT EXISTS (select colName from @tblMissingCols where colName = 'prefix') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid prefix value'' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE prefix is not null and len(prefix) > 0 and prefix not in (select prefix from dbo.ams_memberPrefixTypes where orgID = ' + cast(@orgID as varchar(10)) + ')  
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the Prefix.'' as msg
			FROM ' + @tmptbl + ' 
			WHERE prefix is not null and len(prefix) > 0 and prefix not in (select prefix from dbo.ams_memberPrefixTypes where orgID = ' + cast(@orgID as varchar(10)) + ')  
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET prefix = '''' 
			WHERE prefix is not null and len(prefix) > 0 and prefix not in (select prefix from dbo.ams_memberPrefixTypes where orgID = ' + cast(@orgID as varchar(10)) + ') '
		EXEC(@dynSQL)
	END
END

-- ********************************
-- bad prof license data (fatal and non-fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying invalid professional license data'

select @plType = min(PLName)
	FROM dbo.ams_memberProfessionalLicenseTypes
	WHERE orgID = @orgID
	and PLName + '_licenseNumber' NOT IN (select colName from @tblMissingCols)
	and PLName + '_status' NOT IN (select colName from @tblMissingCols)
	and PLName + '_activeDate' NOT IN (select colName from @tblMissingCols)
while @plType is not null BEGIN
	
	-- dates must be valid
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			UPDATE ' + @tmptbl + ' set ' + quotename(@plType + '_activeDate') + ' = null where ' + quotename(@plType + '_activeDate') + ' = ''''
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@plType + '_activeDate') + ' datetime null
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @plType + '_activeDate contains invalid dates.', 1)
	END
	ELSE BEGIN
		-- if status is defined, it must be valid
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for ' + @plType + '_status' + ': '' + ' + quotename(@plType + '_status') + ' as msg, 0 as fatal 
			FROM ' + @tmptbl + ' as tmp 
			WHERE len(isnull(tmp.' + quotename(@plType + '_status') + ','''')) > 0 
			and not exists (
				select PLStatusID
				from dbo.ams_memberProfessionalLicenseStatuses
				where StatusName = isnull(tmp.' + quotename(@plType + '_status') + ','''')
				and orgID = ' + cast(@orgID as varchar(10)) + '
			)'
		INSERT INTO @tblErrors (msg, fatal)
		EXEC(@dynSQL)

		IF @@ROWCOUNT > 0 BEGIN
			select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the ' + @plType + '_status' + '.'' as msg
				FROM ' + @tmptbl + ' as tmp 
				WHERE len(isnull(tmp.' + quotename(@plType + '_status') + ','''')) > 0 
				and not exists (
					select PLStatusID
					from dbo.ams_memberProfessionalLicenseStatuses
					where StatusName = isnull(tmp.' + quotename(@plType + '_status') + ','''')
					and orgID = ' + cast(@orgID as varchar(10)) + '
				)'
			INSERT INTO @tblDataChanged (msg)
			EXEC(@dynSQL)

			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@plType + '_status') + ' = '''' 
				WHERE len(isnull(' + quotename(@plType + '_status') + ','''')) > 0 
				and not exists (
					select PLStatusID
					from dbo.ams_memberProfessionalLicenseStatuses
					where StatusName = isnull(' + quotename(@plType + '_status') + ','''')
					and orgID = ' + cast(@orgID as varchar(10)) + '
				)'
			EXEC(@dynSQL)
		END

		-- if licenseNumber or activeDate is defined, status must be as well
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had a missing value for ' + @plType + '_status'' as msg, 0 as fatal 
			FROM ' + @tmptbl + ' as tmp 
			WHERE (
				len(isnull(tmp.' + quotename(@plType + '_licenseNumber') + ','''')) > 0
				or tmp.' + quotename(@plType + '_activeDate') + ' is not null
			)
			and len(isnull(tmp.' + quotename(@plType + '_status') + ','''')) = 0 '
		INSERT INTO @tblErrors (msg, fatal)
		EXEC(@dynSQL)

		IF @@ROWCOUNT > 0 BEGIN
			select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the ' + @plType + ' information.'' as msg
				FROM ' + @tmptbl + ' as tmp 
				WHERE (
					len(isnull(tmp.' + quotename(@plType + '_licenseNumber') + ','''')) > 0
					or tmp.' + quotename(@plType + '_activeDate') + ' is not null
				)
				and len(isnull(tmp.' + quotename(@plType + '_status') + ','''')) = 0 '
			INSERT INTO @tblDataChanged (msg)
			EXEC(@dynSQL)

			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@plType + '_licenseNumber') + ' = '''',
					' + quotename(@plType + '_activeDate') + ' = null 
				WHERE (
					len(isnull(' + quotename(@plType + '_licenseNumber') + ','''')) > 0
					or ' + quotename(@plType + '_activeDate') + ' is not null
				)
				and len(isnull(' + quotename(@plType + '_status') + ','''')) = 0 '
			EXEC(@dynSQL)
		END
	END

	select @plType = min(PLName)
		FROM dbo.ams_memberProfessionalLicenseTypes
		WHERE orgID = @orgID
		and PLName + '_licenseNumber' NOT IN (select colName from @tblMissingCols)
		and PLName + '_status' NOT IN (select colName from @tblMissingCols)
		and PLName + '_activeDate' NOT IN (select colName from @tblMissingCols)
		and PLName > @plType
END

-- ********************************
-- check custom columns that have a default defined; use that default instead of null
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking columns with default values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	where mdc.allowNull = 0
	and mdc.skipImport = 0
	and mdc.orgID = @orgID
	and mdc.defaultValueID is not null
	and mdc.columnname not in (select colName from @tblMissingCols)
while @mincol is not null BEGIN
	select @defaultValueID = defaultValueID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol
	select @colDataTypeCode = dt.dataTypeCode from dbo.ams_memberDataColumnDataTypes as dt inner join dbo.ams_memberDataColumns as mdc on mdc.dataTypeID = dt.dataTypeID and mdc.orgID = @orgID and mdc.columnName = @mincol

	IF @colDataTypeCode = 'STRING'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueString from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where nullif(' + quoteName(@mincol) + ','''') is null'
	IF @colDataTypeCode = 'DECIMAL2'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueDecimal2 from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where ' + quoteName(@mincol) + ' is null'
	IF @colDataTypeCode = 'INTEGER'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueInteger from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where ' + quoteName(@mincol) + ' is null'
	IF @colDataTypeCode = 'DATE'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueDate from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where ' + quoteName(@mincol) + ' is null'
	IF @colDataTypeCode = 'BIT'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueBit from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where ' + quoteName(@mincol) + ' is null'
	IF @colDataTypeCode = 'XML'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueXML from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where ' + quoteName(@mincol) + ' is null'
	IF @colDataTypeCode = 'CONTENTOBJ' OR @colDataTypeCode = 'DOCUMENTOBJ'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueSiteResourceID from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where ' + quoteName(@mincol) + ' is null'

	BEGIN TRY
		EXEC(@dynSQL)
	END TRY
	BEGIN CATCH
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains null values that could not use the defined default value.', 1)
	END CATCH

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		where mdc.allowNull = 0
		and mdc.skipImport = 0
		and mdc.orgID = @orgID
		and mdc.defaultValueID is not null
		and mdc.columnname not in (select colName from @tblMissingCols)
		and mdc.columnName > @mincol
END

-- ********************************
-- check for any custom columns that don't permit new values on import
-- for these cols, make sure all values in file are in defined set
-- skip columns where there is a fatal error on converting all values to correct datatype
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking STRING columns for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 0
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'STRING'
		and mdc.columnname not in (select colName from @tblMissingCols)
while @mincol is not null BEGIN
	select @columnID = null, @allownull = null
	select @columnID = columnID, @allownull = allowNull from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	IF @allownull = 1 BEGIN
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = null where ' + quoteName(@mincol) + ' = '''''
		EXEC(@dynSQL)
	END

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and mdcv.columnValueString = importfile.' + quoteName(@mincol) + ' 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and mdcv.columnValueString = importfile.' + quoteName(@mincol) + ' 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		IF @allownull = 1 BEGIN		
			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@mincol) + ' = null
				WHERE rowID in (
					SELECT rowID
					FROM ' + @tmptbl + ' as importfile
					LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
						and mdcv.columnValueString = importfile.' + quoteName(@mincol) + ' 
					WHERE mdcv.valueID is null 
					AND importfile.' + quoteName(@mincol) + ' is not null 
				)'
		END ELSE BEGIN
			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@mincol) + ' = '''' 
				WHERE rowID in (
					SELECT rowID
					FROM ' + @tmptbl + ' as importfile
					LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
						and mdcv.columnValueString = importfile.' + quoteName(@mincol) + ' 
					WHERE mdcv.valueID is null 
					AND importfile.' + quoteName(@mincol) + ' is not null 
				)'
		END
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and mdc.allowMultiple = 0
			and mdc.allowNewValuesOnImport = 0
			and dt.dataTypeCode = 'STRING'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking STRING columns (multiple values) for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 1
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'STRING'
		and mdc.columnname not in (select colName from @tblMissingCols)
while @mincol is not null BEGIN
	select @columnID = null, @allownull = null
	select @columnID = columnID, @allownull = allowNull from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	IF @allownull = 1 BEGIN
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = null where ' + quoteName(@mincol) + ' = '''''
		EXEC(@dynSQL)
	END

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		cross apply dbo.fn_varcharListToTable(' + quotename(@mincol) + ',''|'') as tbl
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and mdcv.columnValueString = tbl.listitem  
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
	
	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			cross apply dbo.fn_varcharListToTable(' + quotename(@mincol) + ',''|'') as tbl
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and mdcv.columnValueString = tbl.listitem  
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		IF @allownull = 1 BEGIN		
			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@mincol) + ' = null
				WHERE rowID in (
					SELECT rowID
					FROM ' + @tmptbl + ' as importfile
					cross apply dbo.fn_varcharListToTable(' + quotename(@mincol) + ',''|'') as tbl
					LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
						and mdcv.columnValueString = tbl.listitem  
					WHERE mdcv.valueID is null 
					AND importfile.' + quoteName(@mincol) + ' is not null 
				)'
		END ELSE BEGIN
			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@mincol) + ' = '''' 
				WHERE rowID in (
					SELECT rowID
					FROM ' + @tmptbl + ' as importfile
					cross apply dbo.fn_varcharListToTable(' + quotename(@mincol) + ',''|'') as tbl
					LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
						and mdcv.columnValueString = tbl.listitem  
					WHERE mdcv.valueID is null 
					AND importfile.' + quoteName(@mincol) + ' is not null 
				)'
		END
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and mdc.allowMultiple = 1
			and mdc.allowNewValuesOnImport = 0
			and dt.dataTypeCode = 'STRING'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking DATE columns for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'DATE'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid dates.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and mdcv.columnValueDate = importfile.' + quoteName(@mincol) + ' 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and mdcv.columnValueDate = importfile.' + quoteName(@mincol) + ' 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
					and mdcv.columnValueDate = importfile.' + quoteName(@mincol) + ' 
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END
	
	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'DATE'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid dates.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking DECIMAL2 columns for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowMultiple = 0
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'DECIMAL2'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid decimal values.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and mdcv.columnValueDecimal2 = importfile.' + quoteName(@mincol) + ' 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg 
			FROM ' + @tmptbl + ' as importfile
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and mdcv.columnValueDecimal2 = importfile.' + quoteName(@mincol) + ' 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
					and mdcv.columnValueDecimal2 = importfile.' + quoteName(@mincol) + ' 
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END
	
	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 0
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'DECIMAL2'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid decimal values.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking DECIMAL2 columns (multiple values) for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowMultiple = 1
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'DECIMAL2'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid decimal values.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		cross apply dbo.fn_decimal2ListToTable(' + quotename(@mincol) + ',''|'') as tbl
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + ' 
			and mdcv.columnValueDecimal2 = tbl.listitem 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY importfile.rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
	
	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			cross apply dbo.fn_decimal2ListToTable(' + quotename(@mincol) + ',''|'') as tbl
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + ' 
				and mdcv.columnValueDecimal2 = tbl.listitem 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY importfile.rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				cross apply dbo.fn_decimal2ListToTable(' + quotename(@mincol) + ',''|'') as tbl
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + ' 
					and mdcv.columnValueDecimal2 = tbl.listitem 
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 1
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'DECIMAL2'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid decimal values.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking INTEGER columns for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowMultiple = 0
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'INTEGER'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid whole number values.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and mdcv.columnValueInteger = importfile.' + quoteName(@mincol) + ' 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg 
			FROM ' + @tmptbl + ' as importfile
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and mdcv.columnValueInteger = importfile.' + quoteName(@mincol) + ' 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
					and mdcv.columnValueInteger = importfile.' + quoteName(@mincol) + ' 
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 0
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'INTEGER'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid whole number values.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking INTEGER columns (multiple values) for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowMultiple = 1
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'INTEGER'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid whole number values.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		cross apply dbo.fn_intListToTable(' + quotename(@mincol) + ',''|'') as tbl
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + ' 
			and mdcv.columnValueInteger = tbl.listitem 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			cross apply dbo.fn_intListToTable(' + quotename(@mincol) + ',''|'') as tbl
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + ' 
				and mdcv.columnValueInteger = tbl.listitem 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				cross apply dbo.fn_intListToTable(' + quotename(@mincol) + ',''|'') as tbl
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + ' 
					and mdcv.columnValueInteger = tbl.listitem 
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 1
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'INTEGER'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid whole number values.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking BIT columns for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'BIT'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid boolean values.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and mdcv.columnValueBit = importfile.' + quoteName(@mincol) + ' 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and mdcv.columnValueBit = importfile.' + quoteName(@mincol) + ' 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
					and mdcv.columnValueBit = importfile.' + quoteName(@mincol) + ' 
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'BIT'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid boolean values.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking XML columns for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'XML'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid XML values.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and cast(mdcv.columnValueXML as varchar(max)) = cast(importfile.' + quoteName(@mincol) + ' as varchar(max))  
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and cast(mdcv.columnValueXML as varchar(max)) = cast(importfile.' + quoteName(@mincol) + ' as varchar(max))  
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
					and cast(mdcv.columnValueXML as varchar(max)) = cast(importfile.' + quoteName(@mincol) + ' as varchar(max))  
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'XML'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid XML values.')
		and mdc.columnName > @mincol
END


-- cleanup - these are no longer needed
IF OBJECT_ID('tempdb..#tblOrgCols') IS NOT NULL
	DROP TABLE #tblOrgCols
IF OBJECT_ID('tempdb..#tblImportCols') IS NOT NULL
	DROP TABLE #tblImportCols

-- ******************************** 
-- dump flat table and format file and creation script
-- would love to use xml format files, but it appears column names with spaces cause it to fail
-- ******************************** 
IF NOT EXISTS (select rowID from @tblErrors where fatal = 1) BEGIN
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Backup Flattened Data'

	SELECT @prefix = @orgcode + '_flat_' + convert(varchar(8),getdate(),112) + replace(convert(varchar(8),getdate(),108),':','')
	SELECT @flatfile = @pathToExport + @prefix

	select @exportcmd = 'bcp ' + @tmptbl + ' format nul -f ' + @flatfile + '.txt -n -T -S' + CAST(serverproperty('servername') as varchar(40))
	EXEC master..xp_cmdshell @exportcmd, NO_OUTPUT

	select @exportcmd = 'bcp ' + @tmptbl + ' out ' + @flatfile + '.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
	EXEC master..xp_cmdshell @exportcmd, NO_OUTPUT

	declare @createTableSQLTop varchar(100)          
	declare @coltypes table (datatype varchar(16))          
	insert into @coltypes values('bit')          
	insert into @coltypes values('binary')          
	insert into @coltypes values('bigint')          
	insert into @coltypes values('int')          
	insert into @coltypes values('float')          
	insert into @coltypes values('datetime')          
	insert into @coltypes values('text')          
	insert into @coltypes values('image')          
	insert into @coltypes values('money')          
	insert into @coltypes values('uniqueidentifier')          
	insert into @coltypes values('smalldatetime')          
	insert into @coltypes values('tinyint')          
	insert into @coltypes values('smallint')          
	insert into @coltypes values('sql_variant')          
	select @dynSQL = ''
	select @dynSQL = @dynSQL +           
		case when charindex('(',@dynSQL,1)<=0 then '(' else '' end + '[' + Column_Name + '] ' +Data_Type +
		case when Data_Type in (Select datatype from @coltypes) then '' else  '(' end+
		case when data_type in ('real','decimal','numeric')  then cast(isnull(numeric_precision,'') as varchar)+','+
		case when data_type in ('real','decimal','numeric') then cast(isnull(Numeric_Scale,'') as varchar) end
		when data_type in ('nvarchar','varchar') and cast(isnull(Character_Maximum_Length,'') as varchar) = '-1' then 'max'
		when data_type in ('char','nvarchar','varchar','nchar') then cast(isnull(Character_Maximum_Length,'') as varchar) else '' end+
		case when Data_Type in (Select datatype from @coltypes)then '' else  ')' end+
		case when Is_Nullable='No' then ' Not null,' else ' null,' end
		from tempdb.Information_Schema.COLUMNS where Table_Name=@tmptbl
		order by ordinal_position
	select @createTableSQLTop = 'Create table ##xxx ' 
	select @dynSQL=@createTableSQLTop + substring(@dynSQL,1,len(@dynSQL)-1) +' )'            
	if dbo.fn_WriteFile(@pathToExport + @prefix + '.sql', @dynSQL, 1) = 0 BEGIN
		EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Failed writing table creation script'
		RETURN 0
	END

	-- create index on temp table for numbers below
	EXEC('CREATE NONCLUSTERED INDEX [idx_' + @tmptbl + '_memnum_rowid] ON ' + @tmptbl + '([membernumber] ASC,[rowID] ASC)')
END ELSE BEGIN
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Backup Flattened Data Skipped - Fatal Errors'
END

-- ******************************** 
-- Counts
-- ******************************** 
IF NOT EXISTS (select rowID from @tblErrors where fatal = 1) BEGIN
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Querying Intermediate Totals'

	-- TotalCurrentMembers
	INSERT INTO @tblCounts (countName, countNum)
	SELECT 'TotalCurrentMembers', COUNT(memberid)
	FROM dbo.ams_members
	WHERE orgID = @orgID
	AND status <> 'D'
	and memberTypeID = 2
	AND memberID = activeMemberID

	-- TotalCurrentActive
	INSERT INTO @tblCounts (countName, countNum)
	SELECT 'TotalCurrentActive', COUNT(memberid)
	FROM dbo.ams_members
	WHERE orgID = @orgID
	AND status = 'A'
	and memberTypeID = 2
	AND memberID = activeMemberID

	-- TotalCurrentInActive
	INSERT INTO @tblCounts (countName, countNum)
	SELECT 'TotalCurrentInActive', COUNT(memberid)
	FROM dbo.ams_members
	WHERE orgID = @orgID
	AND status = 'I'
	and memberTypeID = 2
	AND memberID = activeMemberID

	-- TotalNewImported
	select @dynSQL = 'SELECT ''TotalNewImported'', Count(newlist.rowID)
		FROM ' + @tmptbl + ' as newlist
		LEFT OUTER JOIN dbo.ams_members as m on m.memberNumber = newlist.memberNumber
			AND m.orgID = ' + cast(@orgID as varchar(7)) + ' 
			and m.memberID = m.activeMemberID
			and m.memberTypeID = 2
		WHERE m.memberID is null'
	INSERT INTO @tblCounts (countName, countNum)
	EXEC(@dynSQL)
	
	-- TotalCurrentMembersWillActivated
	select @dynSQL = 'SELECT ''TotalCurrentMembersWillBeActivated'', COUNT(distinct m.memberID)
		FROM dbo.ams_members as m
		INNER JOIN ' + @tmptbl + ' as newlist on newlist.memberNumber = m.memberNumber 
			AND m.orgID = ' + cast(@orgID as varchar(7)) + ' 
			AND m.status = ''I''
			and m.memberID = m.activeMemberID
			and m.memberTypeID = 2'
	INSERT INTO @tblCounts (countName, countNum)
	EXEC(@dynSQL)

	-- TotalCurrentMembersWillBeInactivated
	select @dynSQL = 'SELECT ''TotalCurrentMembersWillBeInactivated'', COUNT(distinct m.memberID)
		FROM dbo.organizations as o
		INNER JOIN dbo.ams_members as m on m.orgID = o.orgID
			AND m.orgID = ' + cast(@orgID as varchar(7)) + ' 
			AND m.status = ''A''
			and m.memberID = m.activeMemberID
			and m.memberTypeID = 2
		LEFT OUTER JOIN ' + @tmptbl + ' as newlist on newlist.memberNumber = m.memberNumber 
		WHERE newlist.rowID is null'
	INSERT INTO @tblCounts (countName, countNum)
	EXEC(@dynSQL)

	-- TotalMembersImported
	select @dynSQL = 'SELECT ''TotalMembersImported'', COUNT(rowID)
		FROM ' + @tmptbl
	INSERT INTO @tblCounts (countName, countNum)
	EXEC(@dynSQL)

END ELSE BEGIN
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Querying Intermediate Totals Skipped - Fatal Errors'
END

-- ******************************** 
-- Member Lists
-- ******************************** 
IF NOT EXISTS (select rowID from @tblErrors where fatal = 1) BEGIN
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Querying Member Lists'

	-- members that will be activated 
	select @dynSQL = 'SELECT TOP 100 PERCENT m.memberID, m.memberNumber, m.firstname, m.lastname
		FROM dbo.ams_members as m WITH(NOLOCK)
		INNER JOIN ' + @tmptbl + ' as newlist on newlist.memberNumber = m.memberNumber 
			AND m.orgID = ' + cast(@orgID as varchar(7)) + '
			AND m.status = ''I''
			and m.memberID = m.activeMemberID
			and m.memberTypeID = 2
		ORDER BY newlist.rowID'
	INSERT INTO @tblMemList1 (memberid, memberNumber, firstname, lastname)
	EXEC(@dynSQL)

	-- members that will be inactivated
	select @dynSQL = 'SELECT TOP 100 PERCENT  m.memberID, m.memberNumber, m.firstname, m.lastname
		FROM dbo.organizations as o
		INNER JOIN dbo.ams_members as m on m.orgID = o.orgID
			AND m.orgID = ' + cast(@orgID as varchar(7)) + '
			AND m.status = ''A''
			and m.memberID = m.activeMemberID
			and m.memberTypeID = 2
		LEFT OUTER JOIN ' + @tmptbl + ' as newlist on newlist.memberNumber = m.memberNumber 
		WHERE newlist.rowID is null
		ORDER BY m.memberID'
	INSERT INTO @tblMemList2 (memberid, memberNumber, firstname, lastname)
	EXEC(@dynSQL)

	-- members that will be added
	select @dynSQL = 'SELECT TOP 100 PERCENT newlist.rowID, newlist.memberNumber, newlist.firstname, newlist.lastname
		FROM ' + @tmptbl + ' as newlist
		LEFT OUTER JOIN dbo.ams_members as m on m.memberNumber = newlist.memberNumber
			AND m.orgID = ' + cast(@orgID as varchar(7)) + '
			and m.memberID = m.activeMemberID
			and m.memberTypeID = 2
		WHERE m.memberID is null
		ORDER BY newlist.rowID'
	INSERT INTO @tblMemList3 (rowID, memberNumber, firstname, lastname)
	EXEC(@dynSQL)

END ELSE BEGIN
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Querying Member Lists Skipped - Fatal Errors'
END

-- ********************************
-- generate result xml file 
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Generating XML response'
select @importResult = (
	select getdate() as "@date", @flatfile as "@flatfile",
		isnull((select top 301 dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg", "@severity" = case fatal when 1 then 'fatal' else 'nonfatal' end
		from @tblErrors
		order by rowid
		FOR XML path('error'), root('errors'), type),'<errors/>'),

		isnull((select countName as "@name", countNum as "@num"
		from @tblCounts
		order by rowid
		FOR XML path('count'), root('counts'), type),'<counts/>'),

		isnull((select top 301 memberid as "@memberid", membernumber as "@membernumber", firstname as "@firstname", lastname as "@lastname"
		from @tblMemList1
		order by memberid
		FOR XML path('member'), root('qryactivated'), type),'<qryactivated/>'),

		isnull((select top 301 memberid as "@memberid", membernumber as "@membernumber", firstname as "@firstname", lastname as "@lastname"
		from @tblMemList2
		order by memberid
		FOR XML path('member'), root('qryinactivated'), type),'<qryinactivated/>'),

		isnull((select top 301 rowid as "@rowid", membernumber as "@membernumber", firstname as "@firstname", lastname as "@lastname"
		from @tblMemList3
		order by rowid
		FOR XML path('member'), root('qryadded'), type),'<qryadded/>'),

		isnull((select top 301 dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
		from @tblDataChanged
		order by rowid
		FOR XML path('change'), root('qrychanges'), type),'<qrychanges/>')
	for xml path('import'), TYPE)

-- drop temp tables 
IF OBJECT_ID('tempdb..' + @tmptbl) IS NOT NULL
	EXEC('DROP TABLE ' + @tmptbl)

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Import to Holding Complete'

RETURN 0
GO

ALTER PROC [dbo].[tr_report_transactions_export]
@orgid int,
@datetype varchar(15),
@startdate datetime,
@enddate datetime,
@typeID int,
@voided bit,
@filename varchar(400),
@incMD bit

AS

-- drop the temp table
IF OBJECT_ID('tempdb..##TransReport') IS NOT NULL 
	DROP TABLE ##TransReport
IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
	DROP TABLE ##tmpMembers

-- put into temp table
CREATE TABLE ##TransReport (autoid int IDENTITY(1,1), transactionID int, transactionDate datetime, accrualDate datetime, voidedDate datetime, [type] varchar(20), assignedToMemberID int, assignedToMemberNumber varchar(50), assignedToMember varchar(130), detail varchar(max), DEBITACCOUNT varchar(max), DebitAccountCode varchar(200), CreditACCOUNT varchar(max), CreditAccountCode varchar(200), amount money)
INSERT INTO ##TransReport (transactionID, transactionDate, accrualDate, voidedDate, [type], assignedToMemberID, assignedToMemberNumber, assignedToMember, detail, DebitAccount, DebitAccountCode, creditAccount, creditAccountCode, amount)
EXEC dbo.tr_report_transactions @orgID, @datetype, @startdate, @enddate, @typeID, @voided

-- put member data into temp table
IF @incMD = 1 BEGIN
	declare @memList varchar(max)
	select @memList = COALESCE(@memList + ',', '') + cast(assignedToMemberID as varchar(10)) FROM ##TransReport
	declare @MDsql varchar(max)
	EXEC dbo.ams_getFlattenedMemberDataSQL @orgID=@orgID, @importDataMode=0, @memberIDList=@memList, @sql=@MDsql OUTPUT
	select @MDsql = stuff(@MDsql, charIndex('from membercentral',@MDsql), len('from membercentral'), 'into ##tmpMembers from membercentral')
	EXEC(@MDsql)
END

-- sql for export
DECLARE @fullsql varchar(max)
SELECT @fullsql = '
	SELECT r.transactionID as [Transaction ID], ' + 
		case when @voided = 1 then '''VOIDED'' as Status, ' else '' end + '
		r.transactionDate as [Date of Transaction], 
		r.accrualDate as [Date Recognized], ' + 
		case when @voided = 1 then 'r.voidedDate as [Date Voided], ' else '' end + '
		r.type as [Transaction Type], r.detail as [Detail], 
		r.DebitAccount as [Debit Account], r.DebitAccountCode as [Debit Account Code], 
		r.CreditAccount as [Credit Account], r.CreditAccountCode as [Credit Account Code],
		r.amount as [Debit], 
		r.amount as [Credit]'
IF @incMD = 1 BEGIN
	SELECT @fullsql = @fullsql + ', m.*
		FROM ##TransReport as r
		INNER JOIN ##tmpMembers as m ON m.MemberCentralID = r.assignedToMemberID
		ORDER BY r.autoid'
END ELSE BEGIN
	SELECT @fullsql = @fullsql + ', r.assignedToMemberNumber, r.assignedToMember 
		FROM ##TransReport as r
		ORDER BY r.autoid'
END

-- export
EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql

-- drop the temp table
IF OBJECT_ID('tempdb..##TransReport') IS NOT NULL 
	DROP TABLE ##TransReport
IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
	DROP TABLE ##tmpMembers

RETURN 0
GO

