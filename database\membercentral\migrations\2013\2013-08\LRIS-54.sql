use membercentral

declare 
	@functionName varchar(30),
	@ResourceTypeName varchar(30),
	@functionID int,
	@orgID int,
	@siteID int,
	@membershipTypeUID uniqueIdentifier,
	@activationOptionCode varchar(1),
	@systemMemberID int,
	@rc int

declare @panelNameMap TABLE (
						panelID INT PRIMARY KEY, 
						subscriptionID int, 
						groupID int, 
						panelName varchar(100), 
						groupName varchar(100), 
						subscriptionName varchar(100), 
						parentSubscriptionName varchar(200),
						rateID int, 
						rfid int, 
						glaccountID int
)

declare @parentSubs TABLE (
						subscriptionID int PRIMARY KEY, 
						subscriptionName varchar(100), 
						rateID int, 
						rfid int, 
						glaccountID int
)

declare @subsToCreate TABLE (outerLoopid int identity(1,1) PRIMARY KEY, memberid int, subscriptionID int, parentSubscriptionID int, parentSubscriberID int, RFID int, GLAccountID int, subStartDate datetime, subEndDate datetime, graceEndDate datetime, rootSubscriberID int, isProcessed bit)
declare @subsToCreateForMember TABLE (innerloopid int identity(1,1) PRIMARY KEY, outerLoopID int, memberid int, subscriptionID int, parentSubscriptionID int, parentSubscriberID int, RFID int, GLAccountID int, subStartDate datetime, subEndDate datetime, graceEndDate datetime, rootSubscriberID int, isProcessed bit)
declare @outerloopID int, @innerLoopID int, @memberid int, @subscriptionID int,@parentSubscriptionID int, @parentSubscriberID int, @RFID int, @GLAccountID int, @subStartDate datetime, @subEndDate datetime, @graceEndDate datetime, @subscriberID int, @rootSubscriberID int

set @activationOptionCode = 'P'
set @functionName = 'Participate'
set @ResourceTypeName = 'ReferralPanel'				
set @siteID  = 2
set @orgID = 2
set @membershipTypeUID = 'D46DFDEA-6E46-4043-BA8F-F4AF38DE1B21'

select @systemMemberID = memberID
from ams_members
where orgID = 1 and membernumber = 'SYSTEM'

insert into @parentSubs (subscriptionID, subscriptionName,rateID, rfid, glaccountID )
select subs.subscriptionID , subs.subscriptionName, r.rateID, rf.rfid, subs.glaccountID
from sub_subscriptions subs
inner join sub_rateSchedules rs
	on rs.scheduleID = subs.scheduleID
	and subs.subscriptionName in ('LRIS Pro-Bono Panels Membership','LRIS Panel Membership')
	and subs.typeID = 21
	and subs.status = 'A'
inner join sub_rates r
	on r.scheduleID = rs.scheduleID
	and r.isRenewalRate = 0
	and r.ratename in ('LRIS Panel Rate','LRIS Pro-Bono Panel Membership')
inner join sub_rateFrequencies rf
	on rf.rateID = r.rateID
inner join sub_frequencies f
	on f.frequencyID = rf.frequencyID
	and f.frequencyName = 'Full'

-- select * from @parentSubs

insert into @panelNameMap (panelID , subscriptionID , groupID , panelName , groupName , subscriptionName,parentSubscriptionName ,rateID, rfid, glaccountID)
	select
		p.panelID , subs.subscriptionID , g.groupID , p.name as panelName , g.groupName , subs.subscriptionName
		,parentSubscriptionName = 
			case 
				when subs.subscriptionName IN ('Modest Means','Servicemembers Civil Relief Act Pro Bono (SCRA)') then 'LRIS Pro-Bono Panels Membership'
				else 'LRIS Panel Membership'
			end
		,r.rateID, rf.rfid, subs.glaccountID
	from 
		ams_groups g
		inner join ams_groups parentg
			on g.parentGroupID = parentG.groupID
			and parentG.groupCode = 'REFEligiblePanelists'
		inner join ams_virtualGroupRuleGroups vgrg
			on vgrg.groupID = g.groupID
			and g.orgID = @orgID 
		inner join ams_virtualGroupRules vgr
			on vgr.ruleID = vgrg.ruleID
		cross apply vgr.ruleXML.nodes('//condition') as T(C)
		inner join ams_virtualGroupConditions vgc
			on vgc.uid = C.value('@id', 'uniqueidentifier')
			and vgc.fieldCode like 'sub_9_%'
		inner join sub_subscriptions subs
			on subs.subscriptionID = cast(left(replace(vgc.fieldcode,'sub_9_',''),charindex('_',replace(vgc.fieldcode,'sub_9_',''))-1) as int)
		inner join sub_rateSchedules rs
			on rs.scheduleID = subs.scheduleID
		inner join sub_rates r
			on r.scheduleID = rs.scheduleID
			and r.isRenewalRate = 0
			and r.ratename <> 'Import Rate'
		inner join sub_rateFrequencies rf
			on rf.rateID = r.rateID
		inner join sub_frequencies f
			on f.frequencyID = rf.frequencyID
			and f.frequencyName = 'Full'
		inner join cms_siteResourceRights srr
			on srr.groupID = g.groupID
		inner join cms_siteResources sr
			on sr.siteResourceID = srr.resourceID
		inner join ref_panels p
			on p.siteResourceID = sr.siteResourceID
				and p.name = 'general'


select *
from @panelNameMap pnm
inner join @parentSubs ps
	on ps.subscriptionName = pnm.parentSubscriptionName


select @functionID = dbo.fn_getResourceFunctionID(@functionName, dbo.fn_getResourceTypeID(@ResourceTypeName))

insert into @subsToCreate (memberid, subscriptionID, parentSubscriptionID, parentSubscriberID, RFID, GLAccountID, subStartDate, subEndDate, graceEndDate, rootSubscriberID)

select
	m.memberid,
	pnm.subscriptionID, 
	ps.subscriptionID as parentSubscriptionID, 
	parentss.subscriberID as parentSubscriberID,
	pnm.RFID,
	pnm.GLAccountID, 
	isnull(parentss.subStartDate, rootss.subStartDate) as subStartDate, 
	isnull(parentss.subEndDate, rootss.subEndDate) as subEndDate, 
	isnull(parentss.graceEndDate, rootss.graceEndDate) as graceEndDate, 
	rootss.subscriberID as rootSubscriberID
from 
	dbo.ams_members m
	inner join ams_members merged
		on merged.activememberID = m.memberID
		and m.memberID = m.activeMemberID
		and m.status <> 'D'
	inner join dbo.sites as s 
		on s.siteID = @siteID
	inner join ref_panelMembers pm on
		pm.memberID = merged.memberID
	inner join ref_panels p on
		p.panelID = pm.panelID
	inner join ref_panelMemberStatus pms on
		pms.panelMemberStatusID = pm.statusID
		and pms.statusName = 'Active'
	inner join cms_siteResources sr on 
		sr.siteResourceID = p.siteResourceID
	inner join @panelNameMap pnm
		on pnm.panelName = p.Name
	inner join @parentSubs ps
		on ps.subscriptionName = pnm.parentSubscriptionName
	left outer join dbo.cache_perms_siteResourceFunctionRightPrints srfrp
		inner join dbo.cache_perms_rightPrints rp on 
			rp.rightPrintID = srfrp.rightPrintID
		inner join dbo.cache_perms_groupPrintsRightPrints gprp on
			gprp.rightPrintID = rp.rightPrintID
		inner join dbo.cache_perms_groupPrints gp on
			gp.groupPrintID =  gprp.groupPrintID
	on srfrp.siteResourceID = sr.siteResourceID
		and srfrp.functionID = @functionID
		and gp.groupPrintID =  m.groupPrintID
	left outer join (
		select ss.memberID, ss.subscriberID, subs.subscriptionName, ss.substartDate, ss.subEndDate, st.statusName
		from sites s
		inner join sub_types t
			on t.siteID = s.siteID
			and s.sitecode = 'sdcba'
			and t.typeName = 'LRIS Panels'
		inner join sub_subscriptions subs
			on subs.typeID = t.typeID
		inner join sub_subscribers ss
			on ss.subscriptionID = subs.subscriptionID
		inner join sub_statuses st
			on st.statusID = ss.statusID
			and st.statusCode = 'A'

	) as subscribers
		on subscribers.memberID = merged.memberID
		and subscribers.subscriptionName = pnm.subscriptionName
	left outer join sub_subscriptions parentSub
		inner join sub_subscribers parentss
			on parentss.subscriptionID = parentSub.subscriptionID
		inner join sub_statuses parentSubStatus
			on parentSubStatus.statusID = parentss.statusID
			and parentSubStatus.statuscode in ('A','I')
		inner join ams_members parentMember
			on parentMember.memberID = parentss.memberID
	on parentSub.subscriptionName = pnm.parentSubscriptionName
	and parentMember.activememberID = m.memberID

	left outer join sub_subscriptions rootSub
		inner join sub_types rootT
			on rootT.typeID = rootSub.typeID
			and rootT.uid = @membershipTypeUID
		inner join sub_subscribers rootss
			on rootss.subscriptionID = rootSub.subscriptionID
			and rootss.parentSubscriberID is null
		inner join sub_statuses rootSubStatus
			on rootSubStatus.statusID = rootss.statusID
			and rootSubStatus.statuscode in ('A','I')
		inner join ams_members rootMember
			on rootMember.memberID = rootss.memberID
	on rootMember.activememberID = m.memberID
where	1=1
	and subscribers.memberID is null
	and rootss.subscriberID is not null
	--and parentss.subscriberID is null
	--and m.memberID = 558303
	--and len(ltrim(rtrim(dbo.pipelist(subscribers.subscriptionName)))) > 0
order by
	-- case when gprp.groupPrintID is not null then 1 else 0 end,
	m.lastName, p.name

select * from @subsToCreate 

BEGIN TRAN

print 'CREATE parent subs'
print '======================================================================================'
select @outerloopID=min(outerloopID) from @subsToCreate where parentSubscriberID is null

while @outerloopID is not null BEGIN

	select
		@memberid = null,
		@subscriptionID = null,
		@parentSubscriptionID = null,
		@parentSubscriberID = null,
		@RFID = null,
		@GLAccountID = null,
		@subStartDate = null,
		@subEndDate = null,
		@graceEndDate = null,
		@subscriberID = null,
		@rootSubscriberID = null

	select
		@memberid = memberID,
		@subscriptionID = ps.subscriptionID,
		@parentSubscriptionID = parentSubscriptionID,
		@parentSubscriberID = parentSubscriberID,
		@RFID = ps.RFID,
		@GLAccountID = ps.GLAccountID,
		@subStartDate = subStartDate,
		@subEndDate = subEndDate,
		@graceEndDate = graceEndDate,
		--@subscriberID = subscriberID,
		@rootSubscriberID = rootSubscriberID
	from @subsToCreate sc
	inner join @parentSubs ps
		on ps.subscriptionID = sc.parentSubscriptionID
	where outerLoopID = @outerLoopID

	print '@outerLoopID = ' + cast(@outerLoopID as varchar)
	print '@memberid = ' + cast(@memberid as varchar)
	print '@subscriptionID = ' + cast(@subscriptionID as varchar)
	print '@parentSubscriptionID = ' + cast(@parentSubscriptionID as varchar)
	print '@parentSubscriberID = ' + isNull(cast(@parentSubscriberID as varchar),'NULL')
	print '@RFID = ' + cast(@RFID as varchar)
	print '@GLAccountID = ' + cast(@GLAccountID as varchar)
	print '@subStartDate = ' + cast(@subStartDate as varchar)
	print '@subEndDate = ' + cast(@subEndDate as varchar)
	print '@graceEndDate = ' + cast(@graceEndDate as varchar)
	--print '@subscriberID = ' + cast(@subscriberID as varchar)
	print '@rootSubscriberID = ' + cast(@rootSubscriberID as varchar)

	exec @rc = dbo.sub_addSubscriber
		@orgID=@orgID,
		@memberid=@memberID, 
		@subscriptionID=@parentSubscriptionID, 
		@parentSubscriberID=@rootSubscriberID, 
		@RFID=@RFID, 
		@GLAccountID=@GLACCOUNTID, 
		@status='A', 
		@subStartDate=@subStartDate, 
		@subEndDate=@subEndDate, 
		@graceEndDate=@graceEndDate,
		@pcfree=0,
		@activationOptionCode='F',
		@recordedByMemberID=@systemMemberID, 
		@subscriberID=@subscriberID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	update 
		@subsToCreate 
	set
		parentSubscriberID = @subscriberID
	where 
		memberID = @memberID
		and parentSubscriptionID = @parentSubscriptionID
	IF @@ERROR <> 0 GOTO on_error	

	print '**********************************************************************************************'

   select @outerloopID=min(outerloopID) from @subsToCreate where parentSubscriberID is null and outerloopID > @outerloopID
END

print '======================================================================================'
print 'CREATE children subs'
print '======================================================================================'
set @outerloopID = null

select @outerloopID=min(outerloopID) from @subsToCreate

while @outerloopID is not null BEGIN
	select
		@memberid = null,
		@subscriptionID = null,
		@parentSubscriptionID = null,
		@parentSubscriberID = null,
		@RFID = null,
		@GLAccountID = null,
		@subStartDate = null,
		@subEndDate = null,
		@graceEndDate = null,
		@subscriberID = null,
		@rootSubscriberID = null

	select
		@memberid = memberID,
		@subscriptionID = subscriptionID,
		@parentSubscriptionID = parentSubscriptionID,
		@parentSubscriberID = parentSubscriberID,
		@RFID = RFID,
		@GLAccountID = GLAccountID,
		@subStartDate = subStartDate,
		@subEndDate = subEndDate,
		@graceEndDate = graceEndDate,
		--@subscriberID = subscriberID,
		@rootSubscriberID = rootSubscriberID
	from @subsToCreate
	where outerLoopID = @outerLoopID

	print '@outerLoopID = ' + cast(@outerLoopID as varchar)	
	print '@memberid = ' + cast(@memberid as varchar)
	print '@subscriptionID = ' + cast(@subscriptionID as varchar)
	print '@parentSubscriptionID = ' + cast(@parentSubscriptionID as varchar)
	print '@parentSubscriberID = ' + isNull(cast(@parentSubscriberID as varchar),'NULL')
	print '@RFID = ' + cast(@RFID as varchar)
	print '@GLAccountID = ' + cast(@GLAccountID as varchar)
	print '@subStartDate = ' + cast(@subStartDate as varchar)
	print '@subEndDate = ' + cast(@subEndDate as varchar)
	print '@graceEndDate = ' + cast(@graceEndDate as varchar)
	print '@rootSubscriberID = ' + cast(@rootSubscriberID as varchar)

	exec dbo.sub_addSubscriber
		@orgID=@orgID,
		@memberid=@memberID, 
		@subscriptionID=@subscriptionID, 
		@parentSubscriberID=@parentSubscriberID, 
		@RFID=@RFID, 
		@GLAccountID=@GLACCOUNTID, 
		@status='A', 
		@subStartDate=@subStartDate, 
		@subEndDate=@subEndDate, 
		@graceEndDate=@graceEndDate,
		@pcfree=0,
		@activationOptionCode='F',
		@recordedByMemberID=@systemMemberID, 
		@subscriberID=@subscriberID OUTPUT

	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	update 
		@subsToCreate 
	set
		isProcessed = 1
	where 
		outerLoopID = @outerloopID
	IF @@ERROR <> 0 GOTO on_error

	print '**********************************************************************************************'

   select @outerloopID=min(outerloopID) from @subsToCreate where outerloopID > @outerloopID and isProcessed is null
END

IF @@TRANCOUNT > 0 
begin
	COMMIT TRAN
	print 'start exec dbo.sub_checkActivations @memberid=null'
	exec dbo.sub_checkActivations @memberid=null
end

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN