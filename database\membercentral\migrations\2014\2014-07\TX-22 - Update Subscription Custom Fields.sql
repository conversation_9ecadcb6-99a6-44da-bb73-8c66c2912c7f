use customApps
GO
declare @udid int

insert into dbo.schedTask_memberJoinDates (siteCode, joinDateFieldName, rejoinDateFieldName, droppedDateFieldName, paidThruDateFieldName, lastSuccessDate, lastErrorCode, isActive)
values ('TX', 'Original Join Date', 'Rejoin Date', 'Dropped Date', 'Expiration Date', '1/1/1980', 0, 0)
	select @udid = SCOPE_IDENTITY()

insert into dbo.schedTask_memberJoinDateSubTypes (memberJoinDateUDID, subscriptionTypeUID)
values (@udid, 'C6533FF0-967B-40F5-8095-64171A1BE6A4')
GO

