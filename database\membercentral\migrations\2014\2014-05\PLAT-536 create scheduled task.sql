
-- Create Scheduled task

use [memberCentral]
go

INSERT INTO [memberCentral].[dbo].[scheduledTasks]
           ([name]
           ,[nextRunDate]
           ,[interval]
           ,[intervalTypeID]
           ,[taskCFC]
           ,[timeoutMinutes]
           ,[disabled]
           ,[siteid])
     VALUES
           ('Process SWL Check Partial Completions'
           ,'1/1/2014'
           ,4
           ,4
           ,'model.scheduledTasks.tasks.processSWLCheckPartialCompletions'
           ,10
           ,0
           ,1)

declare @taskID int
SELECT @taskID = @@IDENTITY;


INSERT INTO [platformstats].[dbo].[scheduledTaskHistory]
           ([taskID]
           ,[statusTypeID]
           ,[dateStarted]
           ,[dateEnded]
           ,[serverid]
           ,[dateLastUpdated])
     VALUES
           (@taskID
           , 2
           ,'1/1/2014'
           ,'1/1/2014'
           ,1
           ,'1/1/2014')