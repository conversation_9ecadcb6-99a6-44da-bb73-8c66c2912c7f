use membercentral
GO

declare @orgid int
select @orgid = orgid from organizations where orgcode='TAGD'

select tmp.company, vw.[Mailing Address_address1], vw.[Mailing Address_address2], vw.[Mailing Address_city], s.stateID, vw.[Mailing Address_postalCode], vw.[Mailing Address_country], 
	ROW_NUMBER() OVER (ORDER BY tmp.company) as row, cast(0 as int) as memberID
into datatransfer.dbo.tmp_TAGDFirms
from (
	select m.company, min(m.memberID) as memberID
	from membercentral.dbo.ams_members as m
	inner join membercentral.dbo.vw_memberData_TAGD as vw2 on vw2.memberid = m.memberid
	where len(m.company) > 0
	and left(m.membernumber,4) = 'EXHB'
	group by m.company
) as tmp
inner join membercentral.dbo.vw_memberData_TAGD as vw on vw.memberid = tmp.memberid
left outer join membercentral.dbo.ams_states as s on s.code = vw.[Mailing Address_stateprov] and s.countryID = 1
GO

declare @orgid int
select @orgid = orgid from organizations where orgcode='TAGD'

declare @companyRTID int, @IndivRTID int, @FirmMemberRTRTID int, @FirmRelTID int
select @companyRTID = recordTypeID from dbo.ams_recordTypes where orgID = @orgid and recordTypeCode = 'Vendor'
select @IndivRTID = recordTypeID from dbo.ams_recordTypes where orgID = @orgid and recordTypeCode = 'Individual'
select @FirmRelTID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgid and relationshipTypeCode = 'Representative'
select @FirmMemberRTRTID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes where masterRecordTypeID = @companyRTID and childRecordTypeID = @IndivRTID and relationshipTypeID = @FirmRelTID

BEGIN TRAN

-- update ALL accounts to indiv not just EXHB
update ams_members
set recordTypeID = @IndivRTID
where orgID = @orgid
and recordTypeID is null
	IF @@ERROR <> 0 goto on_error

declare @row int, @company varchar(300), @membernumber varchar(40), @memberID int, @rc int, @recordTypeID int, @RTID int
select @row = min(row) from datatransfer.dbo.tmp_TAGDFirms
while @row is not null BEGIN
	select	@memberID = null, @company = null, @membernumber = null, @RTID = null, @recordTypeID = null

	select @company = company from datatransfer.dbo.tmp_TAGDFirms where row = @row
	select @membernumber = 'EXHBFIRM' + RIGHT('00000' + cast(@row as varchar(3)),5)
	select @recordTypeID = @companyRTID
	select @RTID = @FirmMemberRTRTID

	EXEC @rc = dbo.ams_createMember @orgID=@orgid, @memberTypeID=2, @prefix='', @firstname='Vendor', @middlename='', @lastname='Account', @suffix='', @professionalsuffix='', @company=@company, @memberNumber=@membernumber, @status='A', @memberID=@memberID OUTPUT
		IF @@ERROR <> 0 or @rc <> 0 or @memberID = 0 goto on_error

	UPDATE ams_members
	SET recordTypeID = @recordTypeID
	where memberID = @memberID
		IF @@ERROR <> 0 goto on_error

	UPDATE datatransfer.dbo.tmp_TAGDFirms
	set memberID = @memberID
	where row = @row
		IF @@ERROR <> 0 goto on_error

	INSERT INTO dbo.ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive)
	select @RTID, @memberID, memberid, 1
	from dbo.ams_members
	where orgID = @orgID
	and memberid = activeMemberID
	and status <> 'D'
	and company = @company
	and left(membernumber,4) = 'EXHB'
	and memberID <> @memberID
		IF @@ERROR <> 0 goto on_error

	select @row = min(row) from datatransfer.dbo.tmp_TAGDFirms where row > @row
END

declare @addressTypeID int
select @addressTypeID = addressTypeID from dbo.ams_memberAddressTypes where orgID = @orgID and addressTypeOrder = 1

insert into dbo.ams_memberAddresses (memberID, addressTypeID, address1, address2, address3, city, stateID, postalCode, countryID)
select memberID, @addressTypeID, isnull([Mailing Address_address1],''), 
	isnull([Mailing Address_address2],''), '', 
	isnull([Mailing Address_city],''), stateID, isnull([Mailing Address_postalCode],''), 1
from datatransfer.dbo.tmp_TAGDFirms
	IF @@ERROR <> 0 goto on_error

COMMIT TRAN
goto on_success

on_error:
	ROLLBACK TRAN
	goto on_done

on_success:
	declare @itemGroupUID uniqueidentifier
	EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgid, '', '', 1, @itemGroupUID OUTPUT

on_done:
	DROP TABLE datatransfer.dbo.tmp_TAGDFirms

GO
