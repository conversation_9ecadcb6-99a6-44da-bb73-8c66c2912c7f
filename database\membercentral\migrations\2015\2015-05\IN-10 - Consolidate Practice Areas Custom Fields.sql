/*
In this SQL script the following tasks are performed. PLEASE READ THE COMMENT BELOW IN (2)

-- (1) Update new custom field
-- (2) Remove old custom fields. This is to be run only on PROD. Uncomment the SP ams_removeMemberDataColumn call in this section.

*/

declare 
	@orgID int, 
	@siteID int,  
	@customFieldName varchar(255),
	@rowID int,
	@memberID int, 
	@columnName varchar(255), 
	@newValueID int,
	@columnID int,
	@newValueIDList varchar(1000)

declare @memberDataColumnsTbl as table (
	rowID int identity(1,1), 
	columnID int, 
	columnName varchar (255),
	newValueID int
)

declare @newMemberDataTbl as table (
	rowID int identity(1,1), 
	memberID int, 
	columnName varchar (255),
	newValueID int
)

declare @memberTbl as table (
	rowID int identity(1,1), 
	memberID int
)

set @customFieldName = 'Areas of Practice'

select 
	@orgID = orgID, @siteID = siteID 
from 
	sites 
where 
	siteCode = 'IN'

insert into @memberDataColumnsTbl (columnName)
select 'Appellate Practice'
union
select 'Bankruptcy'
union
select 'Brain Injury Spinal Cord Injury'
union
select 'Civil Rights'
union
select 'Class Action'
union
select 'Construction'
union
select 'Criminal Law'
union
select 'Eminent Domain'
union
select 'Employment Rights'
union
select 'Environmental Litigation Toxic Torts'
union
select 'ERISA'
union
select 'Family Law'
union
select 'Mediation Arbitration'
union
select 'Medical Negligence'
union
select 'Motor Vehicle Collision'
union
select 'Nursing Home'
union
select 'Personal Injury'
union
select 'Probate Law'
union
select 'Product Liability'
union
select 'Social Security'
union
select 'Veterans Disability'
union
select 'Workers Compensation'
union
select 'Wrongful Death'

update 
	temp_mdc
set
	temp_mdc.columnID = mdc.columnID
from 
	ams_memberDataColumns as mdc
	inner join @memberDataColumnsTbl as temp_mdc on
		mdc.columnName = temp_mdc.columnName
		and mdc.orgID = @orgID	
update 
	temp_mdc
set
	temp_mdc.newValueID = mdcv.valueID
from 
	ams_memberDataColumns mdc
	inner join ams_memberdatacolumnvalues mdcv on 
		mdcv.columnID = mdc.columnID
		and mdc.columnName = @customFieldName
	inner join @memberDataColumnsTbl as temp_mdc on
		temp_mdc.columnName = mdcv.columnValueString 
where
	mdc.orgID = @orgID

insert into @newMemberDataTbl ( md.memberID, mdc.columnName, temp_mdc.newValueID)
select 
	md.memberID, mdc.columnName, temp_mdc.newValueID
from 
	ams_memberDataColumns as mdc
	inner join @memberDataColumnsTbl as temp_mdc on
		mdc.columnID = temp_mdc.columnID
		and mdc.orgID = @orgID	
	inner join dbo.ams_memberDataColumnValues as cv on 
		cv.columnID = mdc.columnID 	
	left outer join dbo.ams_memberData as md on 
		md.valueid = cv.valueid

select * from @newMemberDataTbl order by memberID

insert into @memberTbl
select 
	distinct memberID
from 
	@newMemberDataTbl

select * from @memberTbl

-- (1) Update new custom field
print '(1) Update new custom field'
print '-----------------------'
select @memberID = min(rowID) from @newMemberDataTbl
while @memberID is not null begin

	select @newValueIDList = NULL	 

	select 
		@newValueIDList = COALESCE(@newValueIDList+',' ,'') + cast(newValueID as varchar)
	from 
		@newMemberDataTbl 
	where 
		memberID = @memberID
	group by
		newValueID

	print @memberID
	print @newValueIDList 
	
	exec ams_setMemberData 
			@memberID = @memberID,  
			@orgID = @orgID,  
			@columnName = @customFieldName,  
			@columnValue = @newValueIDList,  
			@byPassQueue = 1 
	
	select @memberID = min(memberID) from @newMemberDataTbl where memberID > @memberID
end

select * from @memberDataColumnsTbl where columnID is not null 

-- (2) Remove old custom fields
print '(2) Remove old custom fields'
print '-----------------------'
select @columnID = min(columnID) from @memberDataColumnsTbl where columnID is not null 
while @columnID is not null begin
	print @columnID
	-- Please uncomment this SP call when running it on PROD
	/*
	exec ams_removeMemberDataColumn 
		@orgID = @orgID,  
		@columnIDList =  @columnID
	*/
	select @columnID = min(columnID) from @memberDataColumnsTbl where columnID > @columnID and columnID is not null 
end


