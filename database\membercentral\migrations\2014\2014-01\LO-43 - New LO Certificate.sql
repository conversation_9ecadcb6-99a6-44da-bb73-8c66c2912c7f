select * from crd_certificates order by 1 

insert into dbo.crd_certificates (friendlyName, siteID, networkID, dateCreated, dateLastModified)
values ('CAALA Live Events with Credit', 20, null, getdate(), getdate())

update dbo.crd_authoritySponsorTypes
set LiveApprovedCertificateID = 24
where ASTID in (
	select ast.ASTID
	from dbo.crd_authoritySponsorTypes as ast
	inner join dbo.crd_authoritySponsors as cas on cas.ASID = ast.ASID
	inner join dbo.crd_sponsors as s on s.sponsorID = cas.sponsorID
	where s.orgID = 19
)



