use membercentral
GO
insert into dbo.tr_types (type)
values ('Deferred Transfer')
GO
insert into dbo.tr_relationshipTypes (type)
values ('DITSaleTrans')
GO
insert into dbo.tr_relationshipTypes (type)
values ('DITOffsetTrans')
GO
insert into dbo.tr_relationshipTypes (type)
values ('DITAdjustTrans')
GO

CREATE TABLE [dbo].[tr_transactionDIT](
	[transferID] [int] IDENTITY(1,1) NOT NULL,
	[transactionID] [int] NOT NULL,
	[recognitionDate] [datetime] NOT NULL
 CONSTRAINT [PK_tr_transactionDIT] PRIMARY KEY CLUSTERED 
(
	[transferID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE [dbo].[tr_transactionDIT]  WITH CHECK ADD  CONSTRAINT [FK_tr_transactionDIT_tr_transactions] FOREIGN KEY([transactionID])
REFERENCES [dbo].[tr_transactions] ([transactionID])
GO
ALTER TABLE [dbo].[tr_transactionDIT] CHECK CONSTRAINT [FK_tr_transactionDIT_tr_transactions]
GO

CREATE PROC [dbo].[tr_createTransaction_dit]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@amount money,
@transactionDate datetime,
@recognitionDate datetime,
@debitGLAccountID int,
@creditGLAccountID int,
@saleTransactionID int,	-- this can be a sale, tax, or adjustment TID
@DITTransactionID int, -- optional, only for offsets
@transactionID int OUTPUT

AS

set nocount on

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- reset output param
	select @transactionID = 0

	-- no zero dollar DITs
	if @amount = 0
		RAISERROR('amount is 0', 16, 1);

	-- ensure amount is 2 decimals
	select @amount = cast(@amount as decimal(10,2))

	-- ensure @DITTransactionID is a dit (if passed in)
	IF @DITTransactionID is not null and NOT EXISTS (select transactionID from dbo.tr_transactions where transactionID = @DITTransactionID and typeID = 10)
		RAISERROR('ditTransactionID is not a DIT', 16, 1);
	IF @amount < 0 AND @DITTransactionID IS NULL
		RAISERROR('ditTransactionID is not valid', 16, 1);

	-- ensure @saleTransactionID is either a sale, sales tax, or positive adjustment
	IF NOT EXISTS (
		select t.transactionID
		from dbo.tr_transactions as t
		inner join dbo.tr_glAccounts as glDeb on glDeb.GLAccountID = t.debitGLAccountID
		where t.transactionID = @saleTransactionID
		and t.typeID in (1,3,7)
		and glDeb.GLCode = 'ACCOUNTSRECEIVABLE' 
		and glDeb.isSystemAccount = 1
		)
	RAISERROR('saleTransactionID is not a sale, sales tax, or positive adjustment', 16, 1);

	-- get info from sale transaction
	declare @assignedToMemberID int, @ownedByOrgID int
	select @assignedToMemberID = t.assignedToMemberID, @ownedByOrgID = t.ownedByOrgID
		from dbo.tr_transactions as t
		where t.transactionID = @saleTransactionID

	-- dont assume memberid is the active one. get the active one.
	select @assignedToMemberID = activeMemberID from dbo.ams_members where memberID = @assignedToMemberID 
	select @recordedByMemberID = activeMemberID from dbo.ams_members where memberID = @recordedByMemberID 

	-- ensure we have active debit/credit accts that account accepts new transactions
	IF @debitGLAccountID is null or NOT EXISTS (
		select glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and glaccountID = @debitGLAccountID
		and [status] = 'A')
		RAISERROR('debit account does not accept new transactions', 16, 1);
	IF @creditGLAccountID is null or NOT EXISTS (
		select glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and glaccountID = @creditGLAccountID
		and [status] = 'A')
		RAISERROR('credit account does not accept new transactions', 16, 1);

	-- if amount < 0 and there are allocations to DITtransactionID, deallocate now and reallocate at the end
	IF @amount < 0 BEGIN
		declare @amtAllocatedToDIT money
		select @amtAllocatedToDIT = cache_activePaymentAllocatedAmount + cache_pendingPaymentAllocatedAmount from dbo.tr_transactionSales where transactionID = @DITtransactionID

		IF @amtAllocatedToDIT > 0 BEGIN
			declare @tblAllocations TABLE (paymentTransactionID int, allocatedAmount money)
			insert into @tblAllocations (paymentTransactionID, allocatedAmount)
			select paymentTransactionID, allocatedAmount from dbo.fn_tr_getAllocatedPaymentsofSale(@DITtransactionID)

			declare @minPTID int, @DeallocateAmtNeg money, @newAllocationTID int
			select @minPTID = min(paymentTransactionID) from @tblAllocations
			WHILE @minPTID is not null BEGIN
				select @DeallocateAmtNeg = allocatedAmount*-1 from @tblAllocations where paymentTransactionID = @minPTID

				EXEC dbo.tr_createTransaction_allocation @recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
					@statsSessionID=@statsSessionID, @status='Active', @amount=@DeallocateAmtNeg, @transactionDate=@transactionDate, 
					@paymentTransactionID=@minPTID, @saleTransactionID=@DITtransactionID, @transactionID=@newAllocationTID OUTPUT

				select @minPTID = min(paymentTransactionID) from @tblAllocations where paymentTransactionID > @minPTID
			END 
		END
	END

	-- insert into transactions
	declare @tr_transactionsAmount decimal(10,2)
	set @tr_transactionsAmount = abs(@amount)

	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
		amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
		typeID, accrualDate, debitGLAccountID, creditGLAccountID)
	VALUES (@ownedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID('Active'), null, null, 
		@tr_transactionsAmount, getdate(), @transactionDate, @assignedToMemberID, @recordedByMemberID, @statsSessionID, 
		10, @transactionDate, @debitGLAccountID, @creditGLAccountID)
		select @transactionID = SCOPE_IDENTITY()


	-- insert into transactionSales
	declare @tr_transactionSalesAmount decimal(10,2)
	select @tr_transactionSalesAmount = case when @amount > 0 then @amount else 0 end

	INSERT INTO dbo.tr_transactionSales (transactionID, cache_amountAfterAdjustment, 
		cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, stateIDForTax)
	VALUES (@transactionID, @tr_transactionSalesAmount, 0, 0, null)


	-- insert into transactionDIT
	IF @amount > 0 BEGIN
		INSERT INTO dbo.tr_transactionDIT (transactionID, recognitionDate)
		VALUES (@transactionID, @recognitionDate)
	END


	-- insert into relationships
	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
	VALUES (dbo.fn_tr_getRelationshipTypeID('DITSaleTrans'), @transactionID, @saleTransactionID)

	IF @amount < 0 BEGIN
		INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
		VALUES (dbo.fn_tr_getRelationshipTypeID('DITOffsetTrans'), @transactionID, @DITTransactionID)
	END


	-- update tr_transactionSales for sale/adj
	IF @amount > 0 BEGIN	
		UPDATE dbo.tr_transactionSales
		SET cache_amountAfterAdjustment = cache_amountAfterAdjustment - @amount
		WHERE transactionID = @saleTransactionID
	END
	ELSE BEGIN
		UPDATE dbo.tr_transactionSales
		SET cache_amountAfterAdjustment = cache_amountAfterAdjustment + @tr_transactionsAmount
		WHERE transactionID = @saleTransactionID

		UPDATE dbo.tr_transactionSales
		SET cache_amountAfterAdjustment = cache_amountAfterAdjustment - @tr_transactionsAmount
		WHERE transactionID = @DITTransactionID
	END


	-- if amount < 0 and there were allocations to DITtransactionID, reallocate now 
	IF @amount < 0 AND @amtAllocatedToDIT > 0 BEGIN
		declare @ReallocateAmt money
		select @minPTID = null, @newAllocationTID = null
		select @minPTID = min(paymentTransactionID) from @tblAllocations
		WHILE @minPTID is not null BEGIN
			select @ReallocateAmt = allocatedAmount from @tblAllocations where paymentTransactionID = @minPTID

			EXEC dbo.tr_createTransaction_allocation @recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
				@statsSessionID=@statsSessionID, @status='Active', @amount=@ReallocateAmt, @transactionDate=@transactionDate, 
				@paymentTransactionID=@minPTID, @saleTransactionID=@saleTransactionID, @transactionID=@newAllocationTID OUTPUT

			select @minPTID = min(paymentTransactionID) from @tblAllocations where paymentTransactionID > @minPTID
		END 
	END


	-- check the in-bound rules.
	-- sale - new cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount must be between 0 and cache_amountAfterAdjustment
	IF NOT EXISTS (select saleID from dbo.tr_transactionSales where transactionID = @saleTransactionID and cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount between 0 and cache_amountAfterAdjustment)
		OR (@amount < 0 AND NOT EXISTS (select saleID from dbo.tr_transactionSales where transactionID = @DITTransactionID and cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount between 0 and cache_amountAfterAdjustment))
		RAISERROR('in-bounds checking failed', 16, 1);

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @transactionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

