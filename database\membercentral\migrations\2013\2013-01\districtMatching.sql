USE [membercentral]
GO

INSERT INTO [memberCentral].[dbo].[scheduledTasks] ([name],[nextRunDate],[interval],[intervalTypeID],[taskCFC],[timeoutMinutes],[disabled],[siteid])
VALUES ('MemberCentral District Matching','1/30/2013',1,1,'model.scheduledTasks.tasks.districtMatching',10,1,1)
GO           

declare @taskID int
select @taskID = taskID from scheduledTasks where taskCFC = 'model.scheduledTasks.tasks.districtMatching'

insert into scheduledTaskHistory (taskID, statusTypeID, dateStarted, serverID)
values (@taskID, 2, '1/30/2013', 1)
GO

USE [platformstats]
GO
CREATE TABLE [dbo].[apilog_cicero](
	[apilogID] [int] IDENTITY(1,1) NOT NULL,
	[orgID] [int] NOT NULL,
	[memberID] [int] NOT NULL,
	[addressTypeID] [int] NOT NULL,
	[dateEntered] [datetime] NOT NULL CONSTRAINT [DF_apilog_cicero_dateEntered]  DEFAULT (getdate()),
 CONSTRAINT [PK_apilog_cicero] PRIMARY KEY CLUSTERED 
(
	[apilogID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

use customApps
GO
CREATE TABLE [dbo].[schedTask_districtMatching](
	[addressTypeID] [int] NOT NULL,
	[addressID] [int] NOT NULL,
	[memberID] [int] NOT NULL,
	[orgID] [int] NOT NULL,
	[address] [varchar](300) NOT NULL,
	[city] [varchar](35) NOT NULL,
	[postalCode] [varchar](25) NOT NULL,
	[stateCode] [varchar](4) NULL
) ON [PRIMARY]
GO
