ALTER PROC createInLyrmembersubsets 
 	@segmentName varchar(60),
 	@listName varchar(60),
 	@memberid int
  AS
  BEGIN 
 	SET NOCOUNT ON 
 	
 	INSERT INTO lyrMemberSubsets (SubsetID, List, UserNameLC, Domain, MemberID)
 	SELECT s.SubsetID_, m.List_, m.UserNameLC_, m.Domain_, m.MemberID_
 	FROM subsets_ s, members_ m
 			WHERE s.Name_= @segmentName AND s.List_= @listName AND s.List_=m.List_ AND m.MemberID_= @memberid 
 END
GO
