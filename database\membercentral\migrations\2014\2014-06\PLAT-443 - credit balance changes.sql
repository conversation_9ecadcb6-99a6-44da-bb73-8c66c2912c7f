USE [memberCentral]
GO
ALTER PROC [dbo].[tr_report_creditBalance]
@orgID int,
@reportMode int,
@endDate datetime,
@filename varchar(400)

AS

IF OBJECT_ID('tempdb..#tblCredits') IS NOT NULL 
	DROP TABLE #tblCredits
CREATE TABLE #tblCredits (memberid int, memberName varchar(300), membernumber varchar(50), membercompany varchar(200), 
	totalCreditAmount money, creditAmount money, profileID int, profileName varchar(100))

declare @newendDate datetime
select @newendDate = dateadd(ms,-3,dateadd(day,1,DATEADD(dd, DATEDIFF(dd,0,@endDate), 0)))

-- IF reportMode = 1, based on batch deposit date
IF @reportMode = 1
	insert into #tblCredits (memberid, memberName, membernumber, membercompany,	profileID, profileName, creditAmount)
	select m2.memberid, 
		m2.lastname 
		+ case when o.hasSuffix = 1 then isnull(' ' + nullif(m2.suffix,''),'') else '' end
		+ ', ' + m2.firstname 
		+ case when o.hasMiddleName = 1 then isnull(' ' + nullif(m2.middleName,''),'') else '' end
		as memberName, m2.memberNumber, m2.company, 
		mp.profileID, mp.profileName,
		sum(ref.refundableAmount - alloc.allocatedAmount) as creditAmount
	from dbo.tr_transactions as t
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = t.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID
	inner join dbo.ams_members as m on m.memberid = t.assignedToMemberID
	inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
	inner join dbo.organizations as o on o.orgID = m2.orgID
	inner join dbo.tr_transactionPayments as tp on tp.transactionID = bt.transactionID
	inner join dbo.mp_profiles as mp on mp.profileID = tp.profileID
	cross apply dbo.fn_tr_getAllocatedAmountofPaymentByBatchDate(t.transactionID,@newendDate) as alloc
	cross apply dbo.fn_tr_getRefundableAmountofPaymentByBatchDate(t.transactionID,@newendDate) as ref
	where t.ownedByOrgID = @orgID
	and t.typeID = 2	
	and t.statusID not in (3,4)	
	and b.statusID = 4
	and b.depositDate <= @newendDate
	group by m2.memberid, m2.lastname, o.hasSuffix, m2.suffix, m2.firstname, o.hasMiddleName, m2.middleName, m2.memberNumber, m2.company, mp.profileID, mp.profileName
	having sum(ref.refundableAmount - alloc.allocatedAmount) > 0

-- If reportMode = 2, real time report. use the cache for faster querying
ELSE
	insert into #tblCredits (memberid, memberName, membernumber, membercompany, profileID, profileName, creditAmount)
	select m2.memberid, 
		m2.lastname 
		+ case when o.hasSuffix = 1 then isnull(' ' + nullif(m2.suffix,''),'') else '' end
		+ ', ' + m2.firstname 
		+ case when o.hasMiddleName = 1 then isnull(' ' + nullif(m2.middleName,''),'') else '' end
		as memberName, m2.memberNumber, m2.company, 
		mp.profileID, mp.profileName,
		sum(tp.cache_refundableAmountOfPayment - tp.cache_allocatedAmountOfPayment) as creditAmount
	from dbo.tr_transactions as t
	inner join dbo.ams_members as m on m.memberid = t.assignedToMemberID
	inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
	inner join dbo.organizations as o on o.orgID = m2.orgID
	inner join dbo.tr_transactionPayments as tp on tp.transactionID = t.transactionID
	inner join dbo.mp_profiles as mp on mp.profileID = tp.profileID
	where t.ownedByOrgID = @orgID
	and t.typeID = 2
	and t.statusID not in (3,4)
	group by m2.memberid, m2.lastname, o.hasSuffix, m2.suffix, m2.firstname, o.hasMiddleName, m2.middleName, m2.memberNumber, m2.company, mp.profileID, mp.profileName
	having sum(tp.cache_refundableAmountOfPayment - tp.cache_allocatedAmountOfPayment) > 0

-- add totals row
IF @@rowCount > 0
	insert into #tblCredits (memberid, memberName, membernumber, membercompany, profileID, profileName, creditAmount)
	select null, 'Report Total', null, null, profileID, profileName, sum(creditAmount)
	from #tblCredits
	group by profileID, profileName
	order by profileName

-- update totalCreditAmount by member
update tmp
set tmp.totalCreditAmount = tmp2.totalCreditAmount
from #tblCredits as tmp
inner join (
	select isnull(memberID,0) as memberID, sum(creditAmount) as totalCreditAmount
	from #tblCredits
	group by isnull(memberID,0)
) as tmp2 on isnull(tmp2.memberID,0) = isnull(tmp.memberID,0)

-- export or screen
IF @filename is not null 
BEGIN
	DECLARE @fullsql varchar(max)
	SELECT @fullsql = '
		select memberName as [Member], membernumber as [Member Number], membercompany as [Company], totalCreditAmount as [Total Credit Balance], 
			creditAmount as [Credit Balance by Payment Profile], profileName as [Payment Profile]
		from #tblCredits as tmp
		order by case when memberid is null then 2 else 1 end, memberName, profileName'
	EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql
END 
ELSE
	select memberid, memberName, membernumber, membercompany, totalCreditAmount, creditAmount, profileName
	from #tblCredits
	order by case when memberid is null then 2 else 1 end, memberName, profileName

-- cleanup
IF OBJECT_ID('tempdb..#tblCredits') IS NOT NULL 
	DROP TABLE #tblCredits

RETURN 0
GO