ALTER PROC dbo.emailTracking_createApplicationID
@applicationName VARCHAR(100),
@applicationType VARCHAR(50),
@sendingApplicationID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- Create a table variable to store the OUTPUT result
	DECLARE @sendingApplicationIDVar TABLE (sendingApplicationID INT);

	BEGIN TRAN;
		MERGE INTO dbo.sendingApplications WITH (HOLDLOCK) AS Target
		USING (SELECT @applicationName AS applicationName, @applicationType as applicationType) AS Source
			ON Target.applicationName = Source.applicationName 
			and target.applicationType = source.applicationType
		WHEN MATCHED THEN
			UPDATE SET @sendingApplicationID = Target.sendingApplicationID
		WHEN NOT MATCHED THEN
			INSERT (applicationType, applicationName) VALUES (Source.applicationType, source.applicationName)
			OUTPUT INSERTED.sendingApplicationID INTO @sendingApplicationIDVar;

		SET @sendingApplicationID = (SELECT sendingApplicationID FROM @sendingApplicationIDVar)
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO
