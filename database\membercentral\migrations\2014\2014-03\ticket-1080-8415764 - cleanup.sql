declare 
	@autoID int,
	@siteResourceID int,
	@include bit,
	@functionID int,
	@roleID int,
	@groupID int,
	@inheritedRightsResourceID int,
	@inheritedRightsFunctionID int,
	@siteID int,
	@trashID int


declare @permsToAdd TABLE (autoID int IDENTITY(1,1) PRIMARY KEY, siteID int, siteResourceID int, include bit, functionID int, roleID int, groupID int, inheritedRightsResourceID int, inheritedRightsFunctionID int)

-- write your query to populate the @permsToAdd table
declare @viewfunctionID int, @searchOrgFunctionID int

select @searchOrgFunctionID = dbo.fn_getResourceFunctionID('SearchOrg',dbo.fn_getResourceTypeID('MemberAdmin'))
select @viewfunctionID = dbo.fn_getResourceFunctionID('view',dbo.fn_getResourceTypeID('ApplicationCreatedDocument'))


insert into @permsToAdd (siteID , siteResourceID , include , functionID , roleID , groupID , inheritedRightsResourceID , inheritedRightsFunctionID )
select top 100 d.siteID , d.siteResourceID , 1 as include , @viewfunctionID , null as roleID , null as groupID , toolSR.siteResourceID as inheritedRightsResourceID , @searchOrgFunctionID as inheritedRightsFunctionID 
from cms_documents d (nolock)
inner join cms_siteResources sr (nolock)
	on sr.siteResourceID = d.siteResourceID
inner join dbo.ams_memberDocuments mdocs (nolock)
	on mdocs.documentID = d.documentID
inner join dbo.cms_siteResources toolSR (nolock)
	on toolSR.siteID = d.siteID
inner join dbo.cms_siteResourceTypes srt (nolock)
	on srt.resourceTypeID = toolsr.resourceTypeID
	and srt.resourceType = 'MemberAdmin'
left outer join cms_siteResourceRights srr
	on srr.resourceID = sr.siteResourceID
	and functionID = @viewfunctionID
	and inheritedRightsFunctionID = @searchOrgFunctionID
where srr.resourceID is null

insert into @permsToAdd (siteID , siteResourceID , include , functionID , roleID , groupID , inheritedRightsResourceID , inheritedRightsFunctionID )
select top 100 d.siteID , d.siteResourceID , 1 as include , @viewfunctionID , null as roleID , null as groupID , toolSR.siteResourceID as inheritedRightsResourceID , @searchOrgFunctionID as inheritedRightsFunctionID 
from cms_documents d
inner join cms_siteResources sr (nolock)
	on sr.siteResourceID = d.siteResourceID
inner join dbo.ams_memberDataColumnValues mdcv (nolock)
	on mdcv.columnValueSiteResourceID = sr.siteResourceID
inner join dbo.cms_siteResources toolSR (nolock)
	on toolSR.siteID = d.siteID
inner join dbo.cms_siteResourceTypes srt (nolock)
	on srt.resourceTypeID = toolsr.resourceTypeID
	and srt.resourceType = 'MemberAdmin'
left outer join cms_siteResourceRights srr
	on srr.resourceID = sr.siteResourceID
	and functionID = @viewfunctionID
	and inheritedRightsFunctionID = @searchOrgFunctionID
where srr.resourceID is null

insert into @permsToAdd (siteID , siteResourceID , include , functionID , roleID , groupID , inheritedRightsResourceID , inheritedRightsFunctionID )
select d.siteID , d.siteResourceID , 1 as include , @viewfunctionID , null as roleID , null as groupID , toolSR.siteResourceID as inheritedRightsResourceID , @viewfunctionID as inheritedRightsFunctionID 
from cms_documents d (nolock)
inner join cms_siteResources sr (nolock)
	on sr.siteResourceID = d.siteResourceID
inner join dbo.ref_panelDocuments pd (nolock)
	on pd.documentID = d.documentID
inner join dbo.cms_siteResources toolSR (nolock)
	on toolSR.siteID = d.siteID
inner join dbo.cms_siteResourceTypes srt (nolock)
	on srt.resourceTypeID = toolsr.resourceTypeID
	and srt.resourceType = 'ReferralsAdmin'
left outer join cms_siteResourceRights srr
	on srr.resourceID = sr.siteResourceID
	and functionID = @viewfunctionID
	and inheritedRightsFunctionID = @viewfunctionID
where srr.resourceID is null

-- do not change below this line

select @autoID = min(autoID) from @permsToAdd
while @autoID is not null
begin

	select 
		@siteResourceID = siteResourceID, 
		@include = include, 
		@functionID = functionID, 
		@roleID = roleID, 
		@groupID = groupID, 
		@inheritedRightsResourceID = inheritedRightsResourceID, 
		@inheritedRightsFunctionID = inheritedRightsFunctionID
	from @permsToAdd
	where autoID = @autoID

	exec dbo.cms_createSiteResourceRight
		@siteID=@siteID,
		@siteResourceID=@siteResourceID, 
		@include=@include, 
		@functionID=@functionID, 
		@roleID=@roleID, 
		@groupID=@groupID,
		@memberID = null,
		@inheritedRightsResourceID=@inheritedRightsResourceID, 
		@inheritedRightsFunctionID=@inheritedRightsFunctionID,
		@resourceRightID=@trashID OUTPUT

	select @autoID = min(autoID) from @permsToAdd where autoID > @autoID
end

