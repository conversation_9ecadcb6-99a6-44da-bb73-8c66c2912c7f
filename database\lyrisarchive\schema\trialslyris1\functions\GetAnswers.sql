ALTER FUNCTION GetAnswers(@InResponseID int,@InQuestionID int)
RETURNS varchar(4000)
AS
BEGIN
DECLARE @testCollection varchar(4000)
DECLARE @tempQuestion varchar(4000)
DECLARE @tempFreeForm varchar(4000)
DECLARE @on_element int
SET @on_element = 0
SET @testCollection =''
DECLARE testGet CURSOR FOR
SELECT AnswerText,FreeFormAnswer FROM lyrSurveyAnswers a,lyrSurveyResponseAnswers ra,lyrSurveyResponse r where a.AnswerID = ra.AnswerID and r.ResponseID=ra.ResponseID and ra.QuestionID=@InQuestionID
and r.ResponseID =@InResponseID
OPEN testGet
FETCH NEXT FROM testGet INTO @tempQuestion,@tempFreeForm
WHILE (@@FETCH_STATUS = 0)
BEGIN
IF (@tempQuestion != 'fill-in')
IF (@on_element = 0)
SET @testCollection = @testCollection + '''' + @tempQuestion + ''''
ELSE
SET @testCollection = @testCollection + ', ''' + @tempQuestion + ''''
ELSE
SET @testCollection = @tempFreeForm
SET @on_element=@on_element+1
FETCH NEXT FROM testGet INTO @tempQuestion,@tempFreeForm
END
CLOSE testGet
DEALLOCATE testGet
RETURN convert(text,@testCollection)
END
GO
