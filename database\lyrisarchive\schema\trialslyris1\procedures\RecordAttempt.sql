ALTER PROCEDURE RecordAttempt
  		@NextAttempt smalldatetime,
 		@RecipientID numeric(19),
 		@Now smalldatetime, 
  		@TransactionLog varchar (8000)
 	 AS
 	 begin
  		DECLARE @ptrval binary(16)
 		set nocount on 
 		set xact_abort on
 
 		if ( @TransactionLog is not NULL )
  		begin
  			SELECT @ptrval = TEXTPTR(TransactionLog) 
 			from lyrActiveRecips with ( index ( PK_lyrActiveRecips ) ) 
  			where RecipientID = @RecipientID
  
  			if @@error != 0
  			begin
 				raiserror ( 'Unable to select TEXTPTR from lyrActiveRecips.  Error: %d', 11, 1, @@error )
  				return
  			end
 		end
  
  		Begin transaction RecordAttempt
 
 		if @ptrval is null
 		begin
 	 		update lyrActiveRecips 
 	 		set SendTry = SendTry + 1, 
 	 		NextAttempt = @NextAttempt,
 	 		FirstAttempt = IsNull ( FirstAttempt, @Now ),
 	 		NodeID = NULL,
 	 		NodeSequence = 0,
 			TransactionLog = @TransactionLog
 	 		where RecipientID = @RecipientID
 
 	 		if @@error != 0
  			begin
  				rollback transaction
 				raiserror ( 'Unable to update lyrActiveRecips.  Error: %d', 11, 2, @@error )
  				return
 	 		end
 		end
 		else
 		begin
 	 		update lyrActiveRecips 
 	 		set SendTry = SendTry + 1, 
 	 		NextAttempt = @NextAttempt,
 	 		FirstAttempt = IsNull ( FirstAttempt, @Now ),
 	 		NodeID = NULL,
 	 		NodeSequence = 0 
 	 		where RecipientID = @RecipientID
  
 	 		if @@error != 0
  			begin
  				rollback transaction
 				raiserror ( 'Unable to update lyrActiveRecips.  Error: %d', 11, 3, @@error )
  				return
 	 		end
   
  			UpdateText lyrActiveRecips.TransactionLog @ptrval NULL 0 @TransactionLog
 
 	 		if @@error != 0
  			begin
  				rollback transaction
 				raiserror ( 'Call to UpdateText failed.  Error: %d', 11, 4, @@error )
  				return
 	 		end
  		end
 
  		commit transaction
 	 end
GO
