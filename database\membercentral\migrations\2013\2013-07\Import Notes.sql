use datatransfer
GO
IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[tmp_MongoNotes]') AND type in (N'U'))
DROP TABLE [dbo].[tmp_MongoNotes]
GO
CREATE TABLE [dbo].[tmp_MongoNotes](
	[Siteid] int NOT NULL,
	[NoteID] varchar(50) NOT NULL,
	[entryDate] datetime NOT NULL,
	[actorMemberID] int NOT NULL,
	[receiverMemberID] int NOT NULL,
	[mainmessage] varchar(max) NOT NULL,
	[notetype] varchar(200) NOT NULL,
	[userdate] datetime NULL
) ON [PRIMARY]
GO

declare @qry varchar(1000)
SELECT @qry = 'BULK INSERT datatransfer.dbo.tmp_MongoNotes FROM "c:\temp\Mongodata.txt" WITH (FIELDTERMINATOR = '''+ char(9) + ''', FIRSTROW = 2);'
EXEC(@qry)

update datatransfer.dbo.tmp_MongoNotes
set mainmessage = replace(replace(mainmessage,'*CHR10*',char(10)),'*CHR9*',char(9))

update datatransfer.dbo.tmp_MongoNotes
set notetype = 'General'
where noteType = '[Unknown Type]'

ALTER TABLE datatransfer.dbo.tmp_MongoNotes add catTreeID int;
GO

declare @siteID int, @categoryTreeID int
select @siteID = min(siteID) from datatransfer.dbo.tmp_MongoNotes
while @siteID is not null BEGIN
	select @categoryTreeID = categoryTreeID
	from membercentral.dbo.cms_categoryTrees
	where controllingSiteResourceID = membercentral.dbo.fn_getSiteResourceIDForResourceType('HistoryAdmin', @siteID)
	
	update datatransfer.dbo.tmp_MongoNotes
	set catTreeID = @categoryTreeID
	where siteID = @siteID
	and catTreeID is null 

	INSERT INTO membercentral.dbo.cms_categories (categoryTreeID, categoryCode, categoryName, categoryDesc, parentCategoryID, contributorMemberID)
	select distinct @categoryTreeID, '', notetype, '', null, 461530
	from datatransfer.dbo.tmp_MongoNotes
	where siteID = @siteID

	select @siteID = min(siteID) from datatransfer.dbo.tmp_MongoNotes WHERE siteID > @siteID
END
GO

insert into membercentral.dbo.ams_memberHistory (typeID, memberID, categoryID, subCategoryID, userDate, quantity, dollarAmt, description, dateEntered, enteredByMemberID, linkMemberID, oldMongoID)
select 3, tmp.receiverMemberID, cP.categoryID, null, tmp.userdate, null, null, tmp.mainmessage, tmp.entryDate, tmp.actorMemberID, null, tmp.NoteID
from datatransfer.dbo.tmp_MongoNotes as tmp 
inner join membercentral.dbo.cms_categories as cP on cP.categoryName = tmp.notetype and cP.categoryTreeID = tmp.catTreeID and cP.parentCategoryID is NULL
inner join membercentral.dbo.sites as s on s.siteID = tmp.siteID
inner join membercentral.dbo.ams_members as m on m.memberID = tmp.receiverMemberID and m.orgID = s.orgID
GO

drop table datatransfer.dbo.tmp_MongoNotes
