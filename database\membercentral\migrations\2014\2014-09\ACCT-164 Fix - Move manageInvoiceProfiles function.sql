use membercentral
GO

-- Resource type had wrong name when it was hijacked a long time ago.
UPDATE cms_siteResourceTypes set resourceType = 'InvoiceProfilesAdmin' where resourceTypeID = 151
GO

-- delete invoiceProfileManage function since it was put on the wrong resource type
declare @functionID int
select @functionID = functionID from cms_siteResourceFunctions where functionName = 'invoiceProfileManage'
delete from dbo.cms_siteResourceRightsCache where functionID = @functionID
delete from dbo.cache_perms_siteResourceFunctionRightPrints where functionID = @functionID
delete from dbo.cms_siteResourceRoleFunctions where resourceTypeFunctionID in (select resourceTypeFunctionID from dbo.cms_siteResourceTypeFunctions where functionID = @functionID)
delete from dbo.admin_functionsDeterminingNav where resourceTypeFunctionID in (select resourceTypeFunctionID from dbo.cms_siteResourceTypeFunctions where functionID = @functionID)
delete from dbo.cms_siteResourceTypeFunctions where functionID = @functionID
delete from dbo.cms_siteResourceRights where functionID = @functionID
delete from cms_siteResourceFunctions where functionID = @functionID
GO

-- add manage function back, this time to IPAdmin
declare @resourceTypeID int, @functionID int, @resourceTypeFunctionID int
select @resourceTypeID = dbo.fn_getResourceTypeID('InvoiceProfilesAdmin')

EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='invoiceProfileManage', @displayName='Manage Invoice Profiles', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('invoiceProfileManage',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID
GO

-- set the admin tool to look for this function
declare @resourceTypeID int, @toolTypeID int, @resourceTypeFunctionID int, @navigationID int
select @resourceTypeID = dbo.fn_getResourceTypeID('InvoiceProfilesAdmin')
select @toolTypeID = toolTypeID from dbo.admin_toolTypes where toolType = 'InvoiceProfilesAdmin'
SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('invoiceProfileManage',@resourceTypeID))
select @navigationID = navigationID from dbo.admin_navigation where navName = 'Invoice Profiles'

EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
GO
