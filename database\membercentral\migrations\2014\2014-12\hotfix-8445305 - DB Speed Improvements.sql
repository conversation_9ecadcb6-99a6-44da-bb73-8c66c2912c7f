use seminarweb;
GO
ALTER Function [dbo].[swod_SeminarsInMyCatalog] (
	@OrgCode varchar(5)
) 
returns @tblSeminars table (autoID int IDENTITY(1,1), seminarID int)
AS
begin

/* 
	1st part of union:
	-- my seminars, published, between catalog avail dates

	2nd part of union:
	-- opted in seminars, published, between catalog avail dates, 
	-- publisher allowed syndication and participates in SWOD
*/
	declare @participantID int, @now datetime
	set @participantID = dbo.fn_getParticipantIDFromOrgcode(@OrgCode)
	set @now = getDate()

	INSERT INTO @tblSeminars (seminarID)
	SELECT s.seminarID
	FROM dbo.tblSeminars s
	INNER JOIN dbo.tblSeminarsSWOD swod on swod.seminarID = s.seminarID
	WHERE s.participantID = @participantID
	AND @now between swod.dateCatalogStart and swod.dateCatalogEnd
	AND s.isPublished = 1
	AND s.isDeleted = 0
	union
	SELECT s.seminarID
	FROM dbo.tblSeminars as s
	INNER JOIN dbo.tblSeminarsOptIn as soi on soi.seminarID = s.seminarID
	INNER JOIN dbo.tblSeminarsSWOD as swod on swod.seminarID = s.seminarID
	INNER JOIN dbo.tblParticipants as p on p.participantID = soi.participantID
	WHERE p.orgcode = @OrgCode
	AND @now between swod.dateCatalogStart and swod.dateCatalogEnd
	AND s.isPublished = 1
	AND s.isDeleted = 0
	AND swod.allowSyndication = 1
	AND p.isSWOD = 1
	ORDER BY s.seminarID

	RETURN

end
GO

ALTER Function [dbo].[swb_BundlesInMyCatalog] (
	@OrgCode varchar(5)
) 
RETURNS TABLE 
AS
RETURN 
(
	select TOP 100 PERCENT b.bundleID
	FROM dbo.tblBundles as b
	INNER JOIN dbo.tblBundlesOptIn as boi on boi.bundleID = b.bundleID 
	INNER JOIN dbo.tblParticipants as p
		on p.participantID = boi.participantID
		and p.orgcode = @orgCode
	ORDER BY b.bundleName
)
GO
ALTER FUNCTION [dbo].[swb_BundlesContainingSeminar] (	
	@seminarID int,
	@orgcode varchar(5)
)
RETURNS TABLE 
AS
RETURN 
(
	SELECT top 100 PERCENT b.bundleID, b.bundleName
	FROM dbo.tblBundledItems AS bi 
	INNER JOIN dbo.tblBundles AS b
		ON bi.bundleID = b.bundleID
		and bi.seminarID = @seminarID
	INNER JOIN dbo.tblParticipants as p
		on p.participantID = b.participantID
		and p.orgcode = @orgcode
	ORDER BY b.bundleName
)
GO
ALTER Function [dbo].[swod_SeminarsInMyCatalogMy] (
	@OrgCode varchar(5)
) 
returns @tblSeminars table (autoID int IDENTITY(1,1), seminarID int)
AS
begin

/* 
	1st part of union:
	-- my seminars, published

	2nd part of union:
	-- opted in seminars, published
	-- publisher allowed syndication and participates in SWOD

	DPC, 10/13/2011: removed "AND s.isPublished = 1	AND s.isDeleted = 0"
	from the WHERE clause so users could still 
	access their certificates after programs have been activated or deleted.
*/

	declare @participantID int
	set @participantID = dbo.fn_getParticipantIDFromOrgcode(@OrgCode)

	INSERT INTO @tblSeminars (seminarID)
	SELECT s.seminarID
	FROM dbo.tblSeminars s
	INNER JOIN dbo.tblSeminarsSWOD swod on swod.seminarID = s.seminarID
	WHERE s.participantID = @participantID
	UNION
	SELECT s.seminarID
	FROM dbo.tblSeminars as s
	INNER JOIN dbo.tblSeminarsOptIn as soi on soi.seminarID = s.seminarID
	INNER JOIN dbo.tblSeminarsSWOD as swod on swod.seminarID = s.seminarID
	INNER JOIN dbo.tblParticipants as p on p.participantID = soi.participantID
	WHERE p.orgcode = @OrgCode
	AND swod.allowSyndication = 1
	AND p.isSWOD = 1
	ORDER BY s.seminarID

	RETURN

end
GO
ALTER Function [dbo].[swtl_TitlesInMyCatalog] (
	@OrgCode varchar(5)
) 
returns @tblTitles table (autoID int IDENTITY(1,1), titleID int)
AS
begin

	/* 
	1st part of union:
	-- my titles, published, between catalog avail dates

	2nd part of union:
	-- other assoc titles, published, between catalog avail dates
	-- title allows syndication, publisher participates in SWTL
	-- i opt in to receive their assoc and they opt in to send to me
	*/

	DECLARE @participantID int, @now datetime
	SELECT @participantID = dbo.fn_getParticipantIDFromOrgcode(@OrgCode)
	set @now = getdate()

	INSERT INTO @tblTitles (titleID)
	SELECT t.titleID
	FROM dbo.tblTitles as t
	WHERE t.participantID = @participantID
	AND @now between t.dateCatalogStart and t.dateCatalogEnd
	AND t.isPublished = 1
	AND t.isDeleted = 0
	union
	SELECT t.titleID
	FROM dbo.tblTitles as t
	INNER JOIN dbo.tblSWTLOptInReceive as oir on oir.syndicateParticipantID = t.participantID
	INNER JOIN dbo.tblSWTLOptInSend as ois on ois.participantID = t.participantID
	INNER JOIN dbo.tblParticipants as p on p.participantID = t.participantID
	WHERE t.participantID <> @participantID
	AND oir.participantID = @participantID
	AND ois.syndicateParticipantID = @participantID
	AND @now between t.dateCatalogStart and t.dateCatalogEnd
	AND t.isPublished = 1
	AND t.isDeleted = 0
	AND t.allowSyndication = 1
	AND p.isSWTL = 1

return

end
GO
ALTER Function [dbo].[swtl_TitlesInMyCatalogMy] (
	@OrgCode varchar(5)
) 
returns @tblTitles table (autoID int IDENTITY(1,1), titleID int)
AS
begin

	/* 
	1st part of union:
	-- my titles, published

	2nd part of union:
	-- other assoc titles, published
	-- title allows syndication, publisher participates in SWTL
	-- i opt in to receive their assoc and they opt in to send to me
	*/

	DECLARE @participantID int
	SELECT @participantID = dbo.fn_getParticipantIDFromOrgcode(@OrgCode)

	INSERT INTO @tblTitles (titleID)
	SELECT t.titleID
	FROM dbo.tblTitles as t
	WHERE t.participantID = @participantID
	AND t.isPublished = 1
	AND t.isDeleted = 0
	union
	SELECT t.titleID
	FROM dbo.tblTitles as t
	INNER JOIN dbo.tblSWTLOptInReceive as oir on oir.syndicateParticipantID = t.participantID
	INNER JOIN dbo.tblSWTLOptInSend as ois on ois.participantID = t.participantID
	INNER JOIN dbo.tblParticipants as p on p.participantID = t.participantID
	WHERE t.participantID <> @participantID
	AND oir.participantID = @participantID
	AND ois.syndicateParticipantID = @participantID
	AND t.isPublished = 1
	AND t.isDeleted = 0
	AND t.allowSyndication = 1
	AND p.isSWTL = 1

return

end
GO
ALTER PROC [dbo].[swl_getEnrollmentsForSWAdmin]
@seminarID int,
@orgcode varchar(5)

AS

declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@orgcode)

SELECT TOP 100 PERCENT e.enrollmentID, e.userid, eswl.goToMeetingUID, ps.pin, eswl.swlcode, p.orgcode, p.catalogURL, 
	d.depomemberdataID, d.FirstName, d.LastName, d.Fax, d.Email, e.passed, e.dateEnrolled, eswl.attended, 
	eswl.joinTime, eswl.exitTime, eswl.duration, eswl.durationPhone, eswl.completedPolling, 
	(SELECT COUNT(*) FROM dbo.tblLogSWLive AS log1 WHERE enrollmentID = e.enrollmentID AND seminarID = e.seminarID AND contact LIKE '%@%') AS EmailCount,
	(SELECT COUNT(*) FROM dbo.tblLogSWLive AS log2 WHERE enrollmentID = e.enrollmentID AND seminarID = e.seminarID AND contact NOT LIKE '%@%') AS FaxCount,
	(SELECT COUNT(*) FROM dbo.tblEnrollmentsAndCredit WHERE enrollmentID = e.enrollmentID) AS CreditCount,
	pgtm.parkedID, sswl.phoneAttendee, sswl.codeAttendee
FROM dbo.tblEnrollments AS e 
INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
INNER JOIN dbo.tblSeminarsSWLive AS sswl ON e.seminarID = sswl.seminarID
INNER JOIN dbo.tblSeminars as s on s.seminarid = sswl.seminarID
LEFT OUTER JOIN dbo.tblParkedGTMSeats AS pgtm ON e.enrollmentID = pgtm.enrollmentID
LEFT OUTER JOIN dbo.tblParkedPhoneSeats AS ps ON e.enrollmentID = ps.enrollmentID
WHERE e.seminarID = @seminarID
AND e.isActive = 1
AND (e.participantID = @participantID OR s.participantID = @participantID)
AND (d.adminflag2 is null or d.adminflag2 <> 'Y')
ORDER BY e.dateEnrolled
GO
ALTER PROC [dbo].[swl_getSeminarsForSWAdmin]
@orgcode varchar(5),
@startRow int,
@maxrows int

AS

-- create temp table to store all seminars in catalog
DECLARE @tmpTable TABLE (
	autoID int IDENTITY(1,1),
	seminarID int,
	seminarName varchar(250),
	publisherOrgCode varchar(5),
	dateStart datetime,
	isPast bit,
	totalCount int
)


declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@orgcode)

INSERT INTO @tmpTable (seminarID, seminarName, publisherOrgCode, dateStart, isPast)
select seminarID, seminarName, publisherOrgCode, dateStart, isPast 
from (
	select s.seminarID, s.seminarName, p.orgcode as publisherOrgCode, swl.dateStart,
		CASE WHEN swl.dateStart > getdate() then 0
		ELSE 1
		END as 'isPast'
	FROM dbo.tblSeminars as s
	INNER JOIN dbo.tblSeminarsSWLive as swl on swl.seminarID = s.seminarID
	INNER JOIN dbo.tblParticipants as p on p.participantID = s.participantID
	WHERE p.orgcode = @orgcode
	and s.isDeleted = 0
	and s.isPublished = 1
	union
	SELECT s.seminarID, s.seminarName, p2.orgcode as publisherOrgCode, swl.dateStart,
		CASE WHEN swl.dateStart > getdate() then 0
		ELSE 1
		END as 'isPast'
	FROM dbo.tblParticipants AS p 
	INNER JOIN dbo.tblSeminarsOptIn AS soi ON p.participantID = soi.participantID 
	INNER JOIN dbo.tblSeminars AS s ON soi.seminarID = s.seminarID 
	INNER JOIN dbo.tblSeminarsSWLive AS swl ON s.seminarID = swl.seminarID
	INNER JOIN dbo.tblParticipants AS p2 ON p2.participantID = s.participantID
	WHERE p.orgcode = @orgcode
	and s.isDeleted = 0
	and s.isPublished = 1
) as tmp
ORDER BY dateStart DESC

-- update total count
UPDATE @tmpTable SET totalCount = @@ROWCOUNT

-- get seminarIDList for the export button
declare @seminarIDList varchar(max)
select @seminarIDList = COALESCE(@seminarIDList + ',', '') + cast(seminarID as varchar(10)) FROM @tmpTable

SELECT TOP (@maxrows) tmp.seminarID, tmp.seminarName, tmp.publisherOrgCode, tmp.dateStart, tmp.isPast, tmp.totalCount, (
	SELECT count(e.enrollmentID)
	FROM dbo.tblEnrollments AS e 
	INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
	INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
	INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
	INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
	INNER JOIN dbo.tblSeminarsSWLive AS sswl ON e.seminarID = sswl.seminarID
	INNER JOIN dbo.tblSeminars as s on s.seminarid = sswl.seminarID
	WHERE e.seminarID = tmp.seminarID
	AND e.isActive = 1
	AND (e.participantID = @participantID OR s.participantID = @participantID)
	AND (d.adminflag2 is null or d.adminflag2 <> 'Y')
) as enrolledCount, @seminarIDList as seminarIDList
FROM @tmpTable tmp
WHERE tmp.autoID >= @startRow
ORDER BY tmp.autoID
GO
ALTER PROC [dbo].[swl_getSeminarsOptedOutByOrgcode]
@orgcode varchar(5)

AS

declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@orgcode)

SELECT s.seminarID, s.seminarName, s.isPublished, sswl.dateStart
FROM dbo.tblSeminarsSWLive AS sswl 
INNER JOIN dbo.tblSeminars AS s ON sswl.seminarID = s.seminarID 
LEFT OUTER JOIN dbo.tblSeminarsOptIn AS soi ON s.seminarID = soi.seminarID 
	AND soi.participantID = @participantID
WHERE sswl.dateStart > GETDATE()
AND soi.optInID IS NULL
AND s.isDeleted = 0
ORDER BY sswl.dateStart
GO
ALTER PROC [dbo].[swl_getSeminarsForSWAdminBySearch]
@orgcode varchar(5),
@sd datetime,
@ed datetime,
@pn varchar(200),
@startRow int,
@maxrows int

AS

-- create temp table to store all seminars in catalog
DECLARE @tmpTable TABLE (
	autoID int IDENTITY(1,1),
	seminarID int,
	seminarName varchar(250),
	publisherOrgCode varchar(5),
	dateStart datetime,
	isPast bit,
	totalCount int
)

declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@orgcode)

IF @sd is null 
	SELECT @sd = '1/1/2005'
IF @ed is null 
	SELECT @ed = dateadd(yyyy,1,getdate())

INSERT INTO @tmpTable (seminarID, seminarName, publisherOrgCode, dateStart, isPast)
select seminarID, seminarName, publisherOrgCode, dateStart, isPast 
from (
	select s.seminarID, s.seminarName, p.orgcode as publisherOrgCode, swl.dateStart,
		CASE WHEN swl.dateStart > getdate() then 0
		ELSE 1
		END as 'isPast'
	FROM dbo.tblSeminars as s
	INNER JOIN dbo.tblSeminarsSWLive as swl on swl.seminarID = s.seminarID
	INNER JOIN dbo.tblParticipants as p on p.participantID = s.participantID
	WHERE p.orgcode = @orgcode
	and s.isDeleted = 0
	and s.isPublished = 1
	union
	SELECT s.seminarID, s.seminarName, p2.orgcode as publisherOrgCode, swl.dateStart,
		CASE WHEN swl.dateStart > getdate() then 0
		ELSE 1
		END as 'isPast'
	FROM dbo.tblParticipants AS p 
	INNER JOIN dbo.tblSeminarsOptIn AS soi ON p.participantID = soi.participantID 
	INNER JOIN dbo.tblSeminars AS s ON soi.seminarID = s.seminarID 
	INNER JOIN dbo.tblSeminarsSWLive AS swl ON s.seminarID = swl.seminarID
	INNER JOIN dbo.tblParticipants AS p2 ON p2.participantID = s.participantID
	WHERE p.orgcode = @orgcode
	and s.isDeleted = 0
	and s.isPublished = 1
) as tmp
where seminarName LIKE '%' + @pn + '%'
and dateStart between @sd and @ed
ORDER BY dateStart DESC

-- update total count
UPDATE @tmpTable SET totalCount = @@ROWCOUNT

-- get seminarIDList for the export button
declare @seminarIDList varchar(max)
select @seminarIDList = COALESCE(@seminarIDList + ',', '') + cast(seminarID as varchar(10)) FROM @tmpTable

SELECT TOP (@maxrows) tmp.seminarID, tmp.seminarName, tmp.publisherOrgCode, tmp.dateStart, tmp.isPast, tmp.totalCount, (
	SELECT count(e.enrollmentID)
	FROM dbo.tblEnrollments AS e 
	INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
	INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
	INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
	INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
	INNER JOIN dbo.tblSeminarsSWLive AS sswl ON e.seminarID = sswl.seminarID
	INNER JOIN dbo.tblSeminars as s on s.seminarid = sswl.seminarID
	WHERE e.seminarID = tmp.seminarID
	AND e.isActive = 1
	AND (e.participantID = @participantID OR s.participantID = @participantID)
	AND (d.adminflag2 is null or d.adminflag2 <> 'Y')
) as enrolledCount, @seminarIDList as seminarIDList
FROM @tmpTable tmp
WHERE tmp.autoID >= @startRow
ORDER BY tmp.autoID
GO
ALTER PROC [dbo].[swl_getSeminarsForSWAdmin]
@orgcode varchar(5),
@startRow int,
@maxrows int

AS

-- create temp table to store all seminars in catalog
DECLARE @tmpTable TABLE (
	autoID int IDENTITY(1,1),
	seminarID int,
	seminarName varchar(250),
	publisherOrgCode varchar(5),
	dateStart datetime,
	isPast bit,
	totalCount int
)


declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@orgcode)

INSERT INTO @tmpTable (seminarID, seminarName, publisherOrgCode, dateStart, isPast)
select seminarID, seminarName, publisherOrgCode, dateStart, isPast 
from (
	select s.seminarID, s.seminarName, p.orgcode as publisherOrgCode, swl.dateStart,
		CASE WHEN swl.dateStart > getdate() then 0
		ELSE 1
		END as 'isPast'
	FROM dbo.tblSeminars as s
	INNER JOIN dbo.tblSeminarsSWLive as swl on swl.seminarID = s.seminarID
	INNER JOIN dbo.tblParticipants as p on p.participantID = s.participantID
	WHERE p.orgcode = @orgcode
	and s.isDeleted = 0
	and s.isPublished = 1
	union
	SELECT s.seminarID, s.seminarName, p2.orgcode as publisherOrgCode, swl.dateStart,
		CASE WHEN swl.dateStart > getdate() then 0
		ELSE 1
		END as 'isPast'
	FROM dbo.tblParticipants AS p 
	INNER JOIN dbo.tblSeminarsOptIn AS soi ON p.participantID = soi.participantID 
	INNER JOIN dbo.tblSeminars AS s ON soi.seminarID = s.seminarID 
	INNER JOIN dbo.tblSeminarsSWLive AS swl ON s.seminarID = swl.seminarID
	INNER JOIN dbo.tblParticipants AS p2 ON p2.participantID = s.participantID
	WHERE p.orgcode = @orgcode
	and s.isDeleted = 0
	and s.isPublished = 1
) as tmp
ORDER BY dateStart DESC

-- update total count
UPDATE @tmpTable SET totalCount = @@ROWCOUNT

-- get seminarIDList for the export button
declare @seminarIDList varchar(max)
select @seminarIDList = COALESCE(@seminarIDList + ',', '') + cast(seminarID as varchar(10)) FROM @tmpTable

SELECT TOP (@maxrows) tmp.seminarID, tmp.seminarName, tmp.publisherOrgCode, tmp.dateStart, tmp.isPast, tmp.totalCount, (
	SELECT count(e.enrollmentID)
	FROM dbo.tblEnrollments AS e 
	INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
	INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
	INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
	INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
	INNER JOIN dbo.tblSeminarsSWLive AS sswl ON e.seminarID = sswl.seminarID
	INNER JOIN dbo.tblSeminars as s on s.seminarid = sswl.seminarID
	WHERE e.seminarID = tmp.seminarID
	AND e.isActive = 1
	AND (e.participantID = @participantID OR s.participantID = @participantID)
	AND (d.adminflag2 is null or d.adminflag2 <> 'Y')
) as enrolledCount, @seminarIDList as seminarIDList
FROM @tmpTable tmp
WHERE tmp.autoID >= @startRow
ORDER BY tmp.autoID
GO
ALTER PROC [dbo].[swod_getAuthorsForCatalog]
@catalogorgcode varchar(5),
@letter char(1),
@startRow int,
@maxrows int

AS

-- create temp table
DECLARE @tmpTable TABLE (
	autoID int IDENTITY(1,1),
	authorID int,
	prefix varchar(10),
	firstname varchar(30),
	middlename varchar(30),
	lastname varchar(40),
	suffix varchar(30),
	seminarCount int,
	totalCount int
)

declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@catalogorgcode)

INSERT INTO @tmpTable (authorID, prefix, firstname, middlename, lastname, suffix, seminarCount)
SELECT authorID, prefix, firstname, middlename, lastname, suffix, sum(itemCount)
FROM (	
	SELECT a.authorID, a.prefix, a.firstname, a.middlename, a.lastName, a.suffix, COUNT(DISTINCT smc.seminarID) AS itemCount
	FROM dbo.tblTitlesAndAuthors AS taa 
	INNER JOIN dbo.tblAuthors AS a ON taa.authorID = a.authorID 
	INNER JOIN dbo.tblSeminarsAndTitles AS sat ON taa.titleID = sat.titleID 
	INNER JOIN dbo.swod_SeminarsInMyCatalog(@catalogOrgcode) AS smc ON sat.seminarID = smc.seminarID
	GROUP BY a.authorID, a.prefix, a.firstname, a.middlename, a.lastName, a.suffix
		union all
	SELECT a.authorID, a.prefix, a.firstname, a.middlename, a.lastName, a.suffix, COUNT(DISTINCT b.bundleID) as itemCount
	FROM dbo.tblBundles AS b
	INNER JOIN dbo.swb_BundlesInMyCatalog(@catalogOrgcode) AS bmc ON bmc.bundleID = b.bundleID
	INNER JOIN dbo.tblBundledItems as bi on bi.bundleID = bmc.bundleID
	INNER JOIN dbo.tblSeminars as s on s.seminarID = bi.seminarID
	INNER JOIN dbo.tblSeminarsSWOD as sod on sod.seminarid = s.seminarid
	INNER JOIN dbo.tblSeminarsAndTitles as sat on sat.seminarID = sod.seminarID
	INNER JOIN dbo.tblTitlesAndAuthors AS taa ON taa.titleID = sat.titleID 
	INNER JOIN dbo.tblAuthors AS a on a.authorID = taa.authorID
	WHERE s.isPublished = 1
	AND s.isDeleted = 0
	AND s.participantID = @participantID
	GROUP BY a.authorID, a.prefix, a.firstname, a.middlename, a.lastName, a.suffix
		union all
	SELECT a.authorID, a.prefix, a.firstname, a.middlename, a.lastName, a.suffix, COUNT(DISTINCT b.bundleID) as itemCount
	FROM dbo.tblBundles AS b
	INNER JOIN dbo.swb_BundlesInMyCatalog(@catalogOrgcode) AS bmc ON bmc.bundleID = b.bundleID
	INNER JOIN dbo.tblBundledItems as bi on bi.bundleID = bmc.bundleID
	INNER JOIN dbo.tblTitles as t on t.titleID = bi.titleID	
	INNER JOIN dbo.tblTitlesAndAuthors AS taa ON taa.titleID = t.titleID 
	INNER JOIN dbo.tblAuthors AS a on a.authorID = taa.authorID
	WHERE t.isPublished = 1
	AND t.participantID = @participantID
	GROUP BY a.authorID, a.prefix, a.firstname, a.middlename, a.lastName, a.suffix
) as tmp
WHERE left(tmp.lastname,1) = CASE
	WHEN len(@letter) > 0 THEN @letter
	ELSE left(tmp.lastname,1)
	END
GROUP BY authorID, prefix, firstname, middlename, lastname, suffix
ORDER BY lastname, firstname

-- get total count
UPDATE @tmpTable SET totalCount = @@ROWCOUNT

-- return based on start/max rows
SELECT TOP (@maxrows) *
FROM @tmpTable
WHERE autoID >= @startRow
ORDER BY autoID
GO
ALTER PROC [dbo].[swod_getCategoriesForCatalog]
@catalogorgcode varchar(5),
@letter char(1),
@startRow int,
@maxrows int

AS

-- create temp table
DECLARE @tmpTable TABLE (
	autoID int IDENTITY(1,1),
	categoryName varchar(75),
	seminarCount int,
	totalCount int
)
declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@catalogorgcode)

INSERT INTO @tmpTable (categoryName, seminarCount)
SELECT CategoryName, sum(itemCount)
FROM (	
	SELECT c.CategoryName, COUNT(DISTINCT sac.seminarID) as itemCount
	FROM dbo.tblCategories AS c
	INNER JOIN dbo.tblSeminarsAndCategories AS sac ON sac.categoryID = c.categoryID 
	INNER JOIN dbo.swod_SeminarsInMyCatalog(@catalogorgcode) AS smc ON smc.seminarID = sac.seminarID
	GROUP BY c.categoryName
		union all
	SELECT c.CategoryName, COUNT(DISTINCT b.bundleID) as itemCount
	FROM dbo.tblBundles AS b
	INNER JOIN dbo.swb_BundlesInMyCatalog(@catalogorgcode) AS bmc ON bmc.bundleID = b.bundleID
	INNER JOIN dbo.tblBundledItems as bi on bi.bundleID = bmc.bundleID
	INNER JOIN dbo.tblSeminars as s on s.seminarID = bi.seminarID
	INNER JOIN dbo.tblSeminarsSWOD as sod on sod.seminarid = s.seminarID
	INNER JOIN dbo.tblSeminarsAndCategories AS sac ON sac.seminarID = s.seminarID 
	INNER JOIN dbo.tblCategories AS c on c.categoryID = sac.categoryID
	WHERE s.isPublished = 1
	AND s.isDeleted = 0
	AND s.participantID = @participantID
	GROUP BY c.categoryName
) as tmp
WHERE left(CategoryName,1) = CASE
	WHEN len(@letter) > 0 THEN @letter
	ELSE left(CategoryName,1)
	END
GROUP BY CategoryName
ORDER BY CategoryName

-- get total count
UPDATE @tmpTable SET totalCount = @@ROWCOUNT

-- return based on start/max rows
SELECT TOP (@maxrows) *
FROM @tmpTable
WHERE autoID >= @startRow
ORDER BY autoID
GO
ALTER PROC [dbo].[swod_getCreditAuthoritiesForCatalog]
@catalogorgcode varchar(5),
@letter char(1),
@startRow int,
@maxrows int

AS

-- create temp table
DECLARE @tmpTable TABLE (
	autoID int IDENTITY(1,1),
	authorityID int,
	jurisdiction varchar(80),	
	authorityName varchar(200),
	seminarCount int,
	totalCount int
)
declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@catalogorgcode)

INSERT INTO @tmpTable (authorityID, jurisdiction, authorityName, seminarCount)
SELECT authorityID, jurisdiction, authorityName, sum(itemCount)
FROM (	
	SELECT ca.authorityID, ca.jurisdiction, ca.authorityName, COUNT(DISTINCT sac.seminarID) as itemCount
	FROM dbo.tblSeminarsAndCredit as sac
	INNER JOIN dbo.swod_SeminarsInMyCatalog(@catalogorgcode) AS smc ON sac.seminarID = smc.seminarID
	INNER JOIN dbo.tblCreditSponsorsAndAuthorities as csa on csa.CSALinkID = sac.CSALinkID
	INNER JOIN dbo.tblCreditAuthorities AS ca on ca.authorityID = csa.authorityID
	INNER JOIN dbo.tblCreditStatuses as cs on cs.statusID = sac.statusID
	WHERE cs.status in ('Approved','Pending')
	GROUP BY ca.authorityID, ca.jurisdiction, ca.authorityName
		union all
	SELECT ca.authorityID, ca.jurisdiction, ca.authorityName, COUNT(DISTINCT b.bundleID) as itemCount
	FROM dbo.tblBundles AS b
	INNER JOIN dbo.swb_BundlesInMyCatalog(@catalogorgcode) AS bmc ON bmc.bundleID = b.bundleID
	INNER JOIN dbo.tblBundledItems as bi on bi.bundleID = bmc.bundleID
	INNER JOIN dbo.tblSeminarsAndCredit as sac on sac.seminarID = bi.seminarID
	INNER JOIN dbo.tblSeminars as s on s.seminarID = sac.seminarID
	INNER JOIN dbo.tblSeminarsSWOD as sod on sod.seminarID = s.seminarID
	INNER JOIN dbo.tblCreditSponsorsAndAuthorities as csa on csa.CSALinkID = sac.CSALinkID
	INNER JOIN dbo.tblCreditAuthorities AS ca on ca.authorityID = csa.authorityID
	INNER JOIN dbo.tblCreditStatuses as cs on cs.statusID = sac.statusID
	WHERE cs.status in ('Approved','Pending')
	AND s.participantID = @participantID
	AND s.isPublished = 1
	AND s.isDeleted = 0
	GROUP BY ca.authorityID, ca.jurisdiction, ca.authorityName
) as tmp
WHERE left(authorityName,1) = CASE
	WHEN len(@letter) > 0 THEN @letter
	ELSE left(authorityName,1)
	END
GROUP BY authorityID, jurisdiction, authorityName
ORDER BY jurisdiction, authorityName

-- get total count
UPDATE @tmpTable SET totalCount = @@ROWCOUNT

-- return based on start/max rows
SELECT TOP (@maxrows) *
FROM @tmpTable
WHERE autoID >= @startRow
ORDER BY autoID
GO
ALTER PROC [dbo].[swod_getDatePublishedForCatalog]
@catalogorgcode varchar(5),
@startRow int,
@maxrows int

AS

-- create temp table
DECLARE @tmpTable TABLE (
	autoID int IDENTITY(1,1),
	participantID int,	
	dateOrigPublished datetime,
	seminarCount int,
	totalCount int
)
declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@catalogorgcode)

INSERT INTO @tmpTable (participantID, dateOrigPublished, seminarCount)
SELECT participantID, dateOrigPublished, sum(itemCount)
FROM (	
	SELECT p.participantID, sod.dateOrigPublished, COUNT(DISTINCT s.seminarID) as itemCount
	FROM dbo.tblSeminars AS s
	INNER JOIN dbo.tblSeminarsSWOD as sod on sod.seminarID = s.seminarID
	INNER JOIN dbo.swod_SeminarsInMyCatalog(@catalogorgcode) AS smc ON smc.seminarID = sod.seminarID
	INNER JOIN dbo.tblParticipants as p on p.participantID = s.participantID	
	GROUP BY p.participantID, sod.dateOrigPublished
		union all
	SELECT p.participantID, sod.dateOrigPublished, COUNT(DISTINCT b.bundleID) as itemCount
	FROM dbo.tblBundles AS b
	INNER JOIN dbo.swb_BundlesInMyCatalog(@catalogorgcode) AS bmc ON bmc.bundleID = b.bundleID
	INNER JOIN dbo.tblBundledItems as bi on bi.bundleID = bmc.bundleID
	INNER JOIN dbo.tblSeminars as s on s.seminarID = bi.seminarID
	INNER JOIN dbo.tblSeminarsSWOD as sod on sod.seminarID = s.seminarID
	INNER JOIN dbo.tblParticipants as p on p.participantID = b.participantID
	where s.participantID = @participantID
	AND s.isPublished = 1
	AND s.isDeleted = 0
	GROUP BY p.participantID, sod.dateOrigPublished
) as tmp
GROUP BY participantID, dateOrigPublished
ORDER BY dateOrigPublished DESC

-- get total count
UPDATE @tmpTable SET totalCount = @@ROWCOUNT

-- return based on start/max rows
SELECT TOP (@maxrows) *
FROM @tmpTable
WHERE autoID >= @startRow
ORDER BY autoID
GO
ALTER PROC [dbo].[swod_getEnrollmentsForSWAdmin]
@seminarID int,
@orgcode varchar(5)

AS

declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@orgcode)

SELECT TOP 100 PERCENT e.enrollmentID, e.userid, p.orgcode, p.catalogURL, 
	d.depomemberdataID, d.FirstName, d.LastName, d.Fax, d.Email, e.dateEnrolled, 
	(SELECT COUNT(*) FROM dbo.tblLogSWOD AS log1 WHERE enrollmentID = e.enrollmentID AND seminarID = e.seminarID AND contact LIKE '%@%') AS EmailCount,
	(SELECT COUNT(*) FROM dbo.tblEnrollmentsAndCredit WHERE enrollmentID = e.enrollmentID) AS CreditCount,
	CASE 
	WHEN e.passed = 1 and len(e.datecompleted) > 0 THEN 1
	WHEN e.passed = 0 and len(e.datecompleted) > 0 THEN 2
	ELSE 3
	END as Progress,
	CASE
	WHEN len(e.dateCompleted) > 0 THEN 1
	ELSE 0
	END as showCertButton, eswod.calcTimeSpent
FROM dbo.tblEnrollments AS e 
INNER JOIN dbo.tblEnrollmentsSWOD AS eswod ON e.enrollmentID = eswod.enrollmentID 
INNER JOIN dbo.tblSeminars as s on s.seminarid = e.seminarID
INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
WHERE e.seminarID = @seminarID
AND e.isActive = 1
AND (e.participantID = @participantID OR s.participantID = @participantID)
AND (d.adminflag2 is null or d.adminflag2 <> 'Y')
ORDER BY e.dateEnrolled
GO
ALTER PROC [dbo].[swod_getPublishersForCatalog]
@catalogorgcode varchar(5),
@letter char(1),
@startRow int,
@maxrows int

AS

-- create temp table
DECLARE @tmpTable TABLE (
	autoID int IDENTITY(1,1),
	participantID int,	
	publisherName varchar(200),
	seminarCount int,
	totalCount int
)

declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@catalogorgcode)

INSERT INTO @tmpTable (participantID, publisherName, seminarCount)
SELECT participantID, description, sum(itemCount)
FROM (	
	SELECT p.participantID, tla.description, COUNT(DISTINCT s.seminarID) as itemCount
	FROM dbo.tblSeminars AS s
	INNER JOIN dbo.tblSeminarsSWOD as sod on sod.seminarID = s.seminarID
	INNER JOIN dbo.swod_SeminarsInMyCatalog(@catalogorgcode) AS smc ON smc.seminarID = sod.seminarID
	INNER JOIN dbo.tblParticipants as p on p.participantID = s.participantID
	INNER JOIN trialsmith.dbo.depoTLA as tla on tla.state = p.orgcode	
	GROUP BY p.participantID, tla.description
		union all
	SELECT p.participantID, tla.description, COUNT(DISTINCT b.bundleID) as itemCount
	FROM dbo.tblBundles AS b
	INNER JOIN dbo.swb_BundlesInMyCatalog(@catalogorgcode) AS bmc ON bmc.bundleID = b.bundleID
	INNER JOIN dbo.tblBundledItems as bi on bi.bundleID = bmc.bundleID
	INNER JOIN dbo.tblSeminars as s on s.seminarID = bi.seminarID
	INNER JOIN dbo.tblSeminarsSWOD as sod on sod.seminarID = s.seminarID
	INNER JOIN dbo.tblParticipants as p on p.participantID = b.participantID
	INNER JOIN trialsmith.dbo.depoTLA as tla on tla.state = p.orgcode	
	where s.participantID = @participantID
	AND s.isPublished = 1
	AND s.isDeleted = 0
	GROUP BY p.participantID, tla.description
) as tmp
WHERE left(description,1) = CASE
	WHEN len(@letter) > 0 THEN @letter
	ELSE left(description,1)
	END
GROUP BY participantID, description
ORDER BY description

-- get total count
UPDATE @tmpTable SET totalCount = @@ROWCOUNT

-- return based on start/max rows
SELECT TOP (@maxrows) *
FROM @tmpTable
WHERE autoID >= @startRow
ORDER BY autoID
GO
ALTER PROC [dbo].[swod_getSeminarsForCatalogByAuthor]
@catalogOrgCode varchar(5),
@letter char(1),
@authorID int,
@startRow int,
@maxrows int,
@depomemberdataID int,
@depomemberSiteGroups varchar(max)

AS

-- create temp table to store all seminars in catalog
DECLARE @tmpTable TABLE (
	autoID int IDENTITY(1,1),
	seminarID int,
	bundleID int,
	totalCount int
)

declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@catalogorgcode)


-- insert seminars based on letter if passed in
INSERT INTO @tmpTable (seminarID, bundleID)
select seminarID, bundleID
FROM (	
	SELECT s.seminarID, null as bundleID, s.seminarName as contentName
	FROM dbo.tblSeminars as s
	INNER JOIN dbo.swod_seminarsInMyCatalog(@catalogOrgCode) smc on smc.seminarID = s.seminarID
	INNER JOIN dbo.tblSeminarsAndTitles as sat on sat.seminarID = smc.seminarID
	INNER JOIN dbo.tblTitlesAndAuthors AS taa ON taa.titleID = sat.titleID 
	WHERE taa.authorID = @authorID
		union	
	SELECT null as seminarID, b.bundleID, b.bundleName as contentName
	FROM dbo.tblBundles as b
	INNER JOIN dbo.swb_BundlesInMyCatalog(@catalogOrgCode) AS bmc ON bmc.bundleID = b.bundleID
	INNER JOIN dbo.tblBundledItems as bi on bi.bundleID = bmc.bundleID
	INNER JOIN dbo.tblSeminars as s on s.seminarid = bi.seminarid
	INNER JOIN dbo.tblSeminarsSWOD as sod on sod.seminarid = s.seminarid
	INNER JOIN dbo.tblSeminarsAndTitles as sat on sat.seminarID = sod.seminarID
	INNER JOIN dbo.tblTitlesAndAuthors AS taa ON taa.titleID = sat.titleID 
	WHERE taa.authorID = @authorID
	AND s.isPublished = 1
	AND s.isDeleted = 0
	AND s.participantID = @participantID
		union	
	SELECT null as seminarID, b.bundleID, b.bundleName as contentName
	FROM dbo.tblBundles as b
	INNER JOIN dbo.swb_BundlesInMyCatalog(@catalogOrgCode) AS bmc ON bmc.bundleID = b.bundleID
	INNER JOIN dbo.tblBundledItems as bi on bi.bundleID = bmc.bundleID
	INNER JOIN dbo.tblTitles as t on t.titleID = bi.titleID	
	INNER JOIN dbo.tblTitlesAndAuthors AS taa ON taa.titleID = t.titleID 
	WHERE taa.authorID = @authorID
	AND t.isPublished = 1
	AND t.participantID = @participantID
) as iTemp
WHERE left(itemp.contentName,1) = CASE
	WHEN len(@letter) > 0 THEN @letter
	ELSE left(itemp.contentName,1)
	END
ORDER BY itemp.contentName

-- update total count
UPDATE @tmpTable SET totalCount = @@ROWCOUNT

-- return subset of records for display with all other fields
SELECT TOP (@maxrows) 
	CASE WHEN tmp.seminarID is not null THEN 'SWOD' ELSE 'SWB' END as 'format',
	isnull(tmp.seminarID,tmp.bundleID) as contentID,
	isnull(s.seminarName,b.bundleName) as contentName,
	CASE 
	WHEN tmp.seminarID is not null AND @depomemberdataID > 0 AND dbo.fn_getEnrollmentIDFromDepoMemberDataID(tmp.seminarID,@depoMemberdataid) > 0 THEN 1 
	WHEN tmp.bundleID is not null AND @depomemberdataID > 0 THEN dbo.swb_isRegisteredForBundle(tmp.bundleID,@depoMemberdataid)
	ELSE 0 
	END as isRegistered, 	
	CASE WHEN tmp.seminarID is not null then dbo.swod_getPriceForSeminar(tmp.seminarID,@catalogOrgCode,@depomemberdataid,@depomemberSiteGroups) ELSE dbo.sw_getPriceForBundle(tmp.bundleID,@catalogOrgCode,@depomemberdataid,@depomemberSiteGroups) END as price,
	isnull(tla.description,tla2.description) as description,
	isnull(p.orgcode,p2.orgcode) as publisherOrgCode,
	CASE WHEN tmp.seminarID is not null THEN (select count(*) from dbo.tblSeminarsAndGroups where seminarID = tmp.seminarID) ELSE (select count(*) from dbo.tblBundlesAndGroups where bundleID = tmp.bundleID) END as NumPriceOptions,
	tmp.totalCount,
	isnull(s.offerCertificate,0) as offerCertificate,
	isnull((
		SELECT [file].filetype, count(distinct xmlf.fileID) as fileCount
		FROM dbo.tblSeminarsAndTitles AS xmlsat 
		INNER JOIN dbo.tblTitles AS xmlt ON xmlt.titleID = xmlsat.titleID 
		INNER JOIN dbo.tblTitlesAndFiles AS xmltaf ON xmlt.titleID = xmltaf.titleID 
		INNER JOIN dbo.tblFiles AS xmlf ON xmltaf.fileID = xmlf.fileID
		INNER JOIN dbo.tblFilesTypes AS [file] ON [file].fileTypeID = xmlf.fileTypeID
		WHERE xmlsat.seminarID = tmp.seminarID
		AND xmlt.isPublished = 1
		AND xmlt.isDeleted = 0
		AND xmlf.isDeleted = 0
		group by [file].fileType
		ORDER BY [file].filetype
		FOR XML AUTO, ROOT('files')
	),'<files/>') as componentsXML
FROM @tmpTable as tmp
LEFT OUTER JOIN dbo.tblSeminarsSWOD AS swod 
	INNER JOIN dbo.tblSeminars AS s ON swod.seminarID = s.seminarID 
	INNER JOIN dbo.tblParticipants AS p ON s.participantID = p.participantID 
	INNER JOIN trialsmith.dbo.depoTLA AS tla ON tla.State = p.orgcode 
	ON tmp.seminarID = s.seminarID 
LEFT OUTER JOIN dbo.tblBundles AS b 
	INNER JOIN dbo.tblParticipants AS p2 ON b.participantID = p2.participantID 
	INNER JOIN trialsmith.dbo.depoTLA AS tla2 ON tla2.State = p2.orgcode 
	ON tmp.bundleID = b.bundleID
WHERE tmp.autoID >= @startRow
ORDER BY contentName
GO
ALTER PROC [dbo].[swod_getSeminarsForCatalogByCategory]
@catalogOrgCode varchar(5),
@letter char(1),
@category varchar(50),
@startRow int,
@maxrows int,
@depomemberdataID int,
@depomemberSiteGroups varchar(max)

AS

-- create temp table to store all seminars in catalog
DECLARE @tmpTable TABLE (
	autoID int IDENTITY(1,1),
	seminarID int,
	bundleID int,
	totalCount int
)
declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@catalogorgcode)

-- insert seminars based on letter if passed in
INSERT INTO @tmpTable (seminarID, bundleID)
select seminarID, bundleID
FROM (	
	SELECT s.seminarID, null as bundleID, s.seminarName as contentName
	FROM dbo.tblSeminars as s
	INNER JOIN dbo.swod_seminarsInMyCatalog(@catalogOrgCode) smc on smc.seminarID = s.seminarID
	INNER JOIN dbo.tblSeminarsAndCategories AS sac ON sac.seminarID = smc.seminarID 
	INNER JOIN dbo.tblCategories as c on c.categoryID = sac.categoryID
	WHERE c.categoryName = @category
		union	
	SELECT null as seminarID, b.bundleID, b.bundleName as contentName
	FROM dbo.tblBundles as b
	INNER JOIN dbo.swb_BundlesInMyCatalog(@catalogOrgCode) AS bmc ON bmc.bundleID = b.bundleID
	INNER JOIN dbo.tblBundledItems as bi on bi.bundleID = bmc.bundleID
	INNER JOIN dbo.tblSeminars as s on s.seminarID = bi.seminarID
	INNER JOIN dbo.tblSeminarsSWOD as sod on sod.seminarID = s.seminarID
	INNER JOIN dbo.tblSeminarsAndCategories AS sac ON sac.seminarID = sod.seminarID 
	INNER JOIN dbo.tblCategories as c on c.categoryID = sac.categoryID
	WHERE c.categoryName = @category
	AND s.isPublished = 1
	AND s.isDeleted = 0
	AND s.participantID = @participantID
) as iTemp
WHERE left(itemp.contentName,1) = CASE
	WHEN len(@letter) > 0 THEN @letter
	ELSE left(itemp.contentName,1)
	END
ORDER BY itemp.contentName

-- update total count
UPDATE @tmpTable SET totalCount = @@ROWCOUNT

-- return subset of records for display with all other fields
SELECT TOP (@maxrows) 
	CASE WHEN tmp.seminarID is not null THEN 'SWOD' ELSE 'SWB' END as 'format',
	isnull(tmp.seminarID,tmp.bundleID) as contentID,
	isnull(s.seminarName,b.bundleName) as contentName,
	CASE 
	WHEN tmp.seminarID is not null AND @depomemberdataID > 0 AND dbo.fn_getEnrollmentIDFromDepoMemberDataID(tmp.seminarID,@depoMemberdataid) > 0 THEN 1 
	WHEN tmp.bundleID is not null AND @depomemberdataID > 0 THEN dbo.swb_isRegisteredForBundle(tmp.bundleID,@depoMemberdataid)
	ELSE 0 
	END as isRegistered, 	
	CASE WHEN tmp.seminarID is not null then dbo.swod_getPriceForSeminar(tmp.seminarID,@catalogOrgCode,@depomemberdataid,@depomemberSiteGroups) ELSE dbo.sw_getPriceForBundle(tmp.bundleID,@catalogOrgCode,@depomemberdataid,@depomemberSiteGroups) END as price,
	isnull(tla.description,tla2.description) as description,
	isnull(p.orgcode,p2.orgcode) as publisherOrgCode,
	CASE WHEN tmp.seminarID is not null THEN (select count(*) from dbo.tblSeminarsAndGroups where seminarID = tmp.seminarID) ELSE (select count(*) from dbo.tblBundlesAndGroups where bundleID = tmp.bundleID) END as NumPriceOptions,
	tmp.totalCount,
	isnull(s.offerCertificate,0) as offerCertificate,
	isnull((
		SELECT [file].filetype, count(distinct xmlf.fileID) as fileCount
		FROM dbo.tblSeminarsAndTitles AS xmlsat 
		INNER JOIN dbo.tblTitles AS xmlt ON xmlt.titleID = xmlsat.titleID 
		INNER JOIN dbo.tblTitlesAndFiles AS xmltaf ON xmlt.titleID = xmltaf.titleID 
		INNER JOIN dbo.tblFiles AS xmlf ON xmltaf.fileID = xmlf.fileID
		INNER JOIN dbo.tblFilesTypes AS [file] ON [file].fileTypeID = xmlf.fileTypeID
		WHERE xmlsat.seminarID = tmp.seminarID
		AND xmlt.isPublished = 1
		AND xmlt.isDeleted = 0
		AND xmlf.isDeleted = 0
		group by [file].fileType
		ORDER BY [file].filetype
		FOR XML AUTO, ROOT('files')
	),'<files/>') as componentsXML
FROM @tmpTable as tmp
LEFT OUTER JOIN dbo.tblSeminarsSWOD AS swod 
	INNER JOIN dbo.tblSeminars AS s ON swod.seminarID = s.seminarID 
	INNER JOIN dbo.tblParticipants AS p ON s.participantID = p.participantID 
	INNER JOIN trialsmith.dbo.depoTLA AS tla ON tla.State = p.orgcode 
	ON tmp.seminarID = s.seminarID 
LEFT OUTER JOIN dbo.tblBundles AS b 
	INNER JOIN dbo.tblParticipants AS p2 ON b.participantID = p2.participantID 
	INNER JOIN trialsmith.dbo.depoTLA AS tla2 ON tla2.State = p2.orgcode 
	ON tmp.bundleID = b.bundleID
WHERE tmp.autoID >= @startRow
ORDER BY contentName
GO
ALTER PROC [dbo].[swod_getSeminarsForCatalogByCreditAuthority]
@catalogOrgCode varchar(5),
@letter char(1),
@authorityID int,
@startRow int,
@maxrows int,
@depomemberdataID int,
@depomemberSiteGroups varchar(max)

AS

-- create temp table to store all seminars in catalog
DECLARE @tmpTable TABLE (
	autoID int IDENTITY(1,1),
	seminarID int,
	bundleID int,
	totalCount int
)
declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@catalogorgcode)

-- insert seminars based on letter if passed in
INSERT INTO @tmpTable (seminarID, bundleID)
select seminarID, bundleID
FROM (	
	SELECT s.seminarID, null as bundleID, s.seminarName as contentName
	FROM dbo.tblSeminars as s
	INNER JOIN dbo.swod_seminarsInMyCatalog(@catalogOrgCode) smc on smc.seminarID = s.seminarID
	INNER JOIN dbo.tblSeminarsAndCredit AS sac ON sac.seminarID = smc.seminarID 
	INNER JOIN dbo.tblCreditStatuses as cs on cs.statusID = sac.statusID
	INNER JOIN dbo.tblCreditSponsorsAndAuthorities as csa on csa.CSALinkID = sac.CSALinkID	
	WHERE csa.authorityID = @authorityID
	AND cs.status in ('Pending','Approved')
		union	
	SELECT null as seminarID, b.bundleID, b.bundleName as contentName
	FROM dbo.tblBundles as b
	INNER JOIN dbo.swb_BundlesInMyCatalog(@catalogOrgCode) AS bmc ON bmc.bundleID = b.bundleID
	INNER JOIN dbo.tblBundledItems as bi on bi.bundleID = bmc.bundleID
	INNER JOIN dbo.tblSeminars as s on s.seminarID = bi.seminarID	
	INNER JOIN dbo.tblSeminarsSWOD as sod on sod.seminarID = s.seminarID
	INNER JOIN dbo.tblSeminarsAndCredit AS sac ON sac.seminarID = sod.seminarID 
	INNER JOIN dbo.tblCreditStatuses as cs on cs.statusID = sac.statusID
	INNER JOIN dbo.tblCreditSponsorsAndAuthorities as csa on csa.CSALinkID = sac.CSALinkID	
	WHERE s.participantID = @participantID
	AND s.isPublished = 1
	AND s.isDeleted = 0
	AND csa.authorityID = @authorityID
	AND cs.status in ('Pending','Approved')
) as iTemp
WHERE left(itemp.contentName,1) = CASE
	WHEN len(@letter) > 0 THEN @letter
	ELSE left(itemp.contentName,1)
	END
ORDER BY itemp.contentName

-- update total count
UPDATE @tmpTable SET totalCount = @@ROWCOUNT

-- return subset of records for display with all other fields
SELECT TOP (@maxrows) 
	CASE WHEN tmp.seminarID is not null THEN 'SWOD' ELSE 'SWB' END as 'format',
	isnull(tmp.seminarID,tmp.bundleID) as contentID,
	isnull(s.seminarName,b.bundleName) as contentName,
	CASE 
	WHEN tmp.seminarID is not null AND @depomemberdataID > 0 AND dbo.fn_getEnrollmentIDFromDepoMemberDataID(tmp.seminarID,@depoMemberdataid) > 0 THEN 1 
	WHEN tmp.bundleID is not null AND @depomemberdataID > 0 THEN dbo.swb_isRegisteredForBundle(tmp.bundleID,@depoMemberdataid)
	ELSE 0 
	END as isRegistered, 	
	CASE WHEN tmp.seminarID is not null then dbo.swod_getPriceForSeminar(tmp.seminarID,@catalogOrgCode,@depomemberdataid,@depomemberSiteGroups) ELSE dbo.sw_getPriceForBundle(tmp.bundleID,@catalogOrgCode,@depomemberdataid,@depomemberSiteGroups) END as price,
	isnull(tla.description,tla2.description) as description,
	isnull(p.orgcode,p2.orgcode) as publisherOrgCode,
	CASE WHEN tmp.seminarID is not null THEN (select count(*) from dbo.tblSeminarsAndGroups where seminarID = tmp.seminarID) ELSE (select count(*) from dbo.tblBundlesAndGroups where bundleID = tmp.bundleID) END as NumPriceOptions,
	tmp.totalCount,
	isnull(s.offerCertificate,0) as offerCertificate,
	isnull((
		SELECT [file].filetype, count(distinct xmlf.fileID) as fileCount
		FROM dbo.tblSeminarsAndTitles AS xmlsat 
		INNER JOIN dbo.tblTitles AS xmlt ON xmlt.titleID = xmlsat.titleID 
		INNER JOIN dbo.tblTitlesAndFiles AS xmltaf ON xmlt.titleID = xmltaf.titleID 
		INNER JOIN dbo.tblFiles AS xmlf ON xmltaf.fileID = xmlf.fileID
		INNER JOIN dbo.tblFilesTypes AS [file] ON [file].fileTypeID = xmlf.fileTypeID
		WHERE xmlsat.seminarID = tmp.seminarID
		AND xmlt.isPublished = 1
		AND xmlt.isDeleted = 0
		AND xmlf.isDeleted = 0
		group by [file].fileType
		ORDER BY [file].filetype
		FOR XML AUTO, ROOT('files')
	),'<files/>') as componentsXML
FROM @tmpTable as tmp
LEFT OUTER JOIN dbo.tblSeminarsSWOD AS swod 
	INNER JOIN dbo.tblSeminars AS s ON swod.seminarID = s.seminarID 
	INNER JOIN dbo.tblParticipants AS p ON s.participantID = p.participantID 
	INNER JOIN trialsmith.dbo.depoTLA AS tla ON tla.State = p.orgcode 
	ON tmp.seminarID = s.seminarID 
LEFT OUTER JOIN dbo.tblBundles AS b 
	INNER JOIN dbo.tblParticipants AS p2 ON b.participantID = p2.participantID 
	INNER JOIN trialsmith.dbo.depoTLA AS tla2 ON tla2.State = p2.orgcode 
	ON tmp.bundleID = b.bundleID
WHERE tmp.autoID >= @startRow
ORDER BY contentName
GO
ALTER PROC [dbo].[swod_getSeminarsForCatalogByDatePublished]
@catalogOrgCode varchar(5),
@date datetime,
@startRow int,
@maxrows int,
@depomemberdataID int,
@depomemberSiteGroups varchar(max)

AS

-- create temp table to store all seminars in catalog
DECLARE @tmpTable TABLE (
	autoID int IDENTITY(1,1),
	seminarID int,
	bundleID int,
	totalCount int
)
declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@catalogorgcode)

-- insert seminars based on letter if passed in
INSERT INTO @tmpTable (seminarID, bundleID)
select seminarID, bundleID
FROM (	
	SELECT s.seminarID, null as bundleID, s.seminarName as contentName
	FROM dbo.tblSeminars as s
	INNER JOIN dbo.swod_seminarsInMyCatalog(@catalogOrgCode) smc on smc.seminarID = s.seminarID
	INNER JOIN dbo.tblSeminarsSWOD swod on swod.seminarID = s.seminarID
	WHERE swod.dateOrigPublished = @date
		union	
	SELECT null as seminarID, b.bundleID, b.bundleName as contentName
	FROM dbo.tblBundles as b
	INNER JOIN dbo.swb_BundlesInMyCatalog(@catalogOrgCode) AS bmc ON bmc.bundleID = b.bundleID
	INNER JOIN dbo.tblBundledItems as bi on bi.bundleID = bmc.bundleID
	INNER JOIN dbo.tblSeminars as s on s.seminarID = bi.seminarID
	INNER JOIN dbo.tblSeminarsSWOD as sod on sod.seminarID = s.seminarID
	WHERE sod.dateOrigPublished = @date
	AND s.isPublished = 1
	AND s.isDeleted = 0
	AND s.participantID = @participantID
) as iTemp
ORDER BY itemp.contentName

-- update total count
UPDATE @tmpTable SET totalCount = @@ROWCOUNT

-- return subset of records for display with all other fields
SELECT TOP (@maxrows) 
	CASE WHEN tmp.seminarID is not null THEN 'SWOD' ELSE 'SWB' END as 'format',
	SWOD.dateOrigPublished as dateOrigPublished,
	isnull(tmp.seminarID,tmp.bundleID) as contentID,
	isnull(s.seminarName,b.bundleName) as contentName,
	CASE 
	WHEN tmp.seminarID is not null AND @depomemberdataID > 0 AND dbo.fn_getEnrollmentIDFromDepoMemberDataID(tmp.seminarID,@depoMemberdataid) > 0 THEN 1 
	WHEN tmp.bundleID is not null AND @depomemberdataID > 0 THEN dbo.swb_isRegisteredForBundle(tmp.bundleID,@depoMemberdataid)
	ELSE 0 
	END as isRegistered, 	
	CASE WHEN tmp.seminarID is not null then dbo.swod_getPriceForSeminar(tmp.seminarID,@catalogOrgCode,@depomemberdataid,@depomemberSiteGroups) ELSE dbo.sw_getPriceForBundle(tmp.bundleID,@catalogOrgCode,@depomemberdataid,@depomemberSiteGroups) END as price,
	isnull(tla.description,tla2.description) as description,
	isnull(p.orgcode,p2.orgcode) as publisherOrgCode,
	CASE WHEN tmp.seminarID is not null THEN (select count(*) from dbo.tblSeminarsAndGroups where seminarID = tmp.seminarID) ELSE (select count(*) from dbo.tblBundlesAndGroups where bundleID = tmp.bundleID) END as NumPriceOptions,
	tmp.totalCount,
	isnull(s.offerCertificate,0) as offerCertificate,
	isnull((
		SELECT [file].filetype, count(distinct xmlf.fileID) as fileCount
		FROM dbo.tblSeminarsAndTitles AS xmlsat 
		INNER JOIN dbo.tblTitles AS xmlt ON xmlt.titleID = xmlsat.titleID 
		INNER JOIN dbo.tblTitlesAndFiles AS xmltaf ON xmlt.titleID = xmltaf.titleID 
		INNER JOIN dbo.tblFiles AS xmlf ON xmltaf.fileID = xmlf.fileID
		INNER JOIN dbo.tblFilesTypes AS [file] ON [file].fileTypeID = xmlf.fileTypeID
		WHERE xmlsat.seminarID = tmp.seminarID
		AND xmlt.isPublished = 1
		AND xmlt.isDeleted = 0
		AND xmlf.isDeleted = 0
		group by [file].fileType
		ORDER BY [file].filetype
		FOR XML AUTO, ROOT('files')
	),'<files/>') as componentsXML
FROM @tmpTable as tmp
LEFT OUTER JOIN dbo.tblSeminarsSWOD AS swod 
	INNER JOIN dbo.tblSeminars AS s ON swod.seminarID = s.seminarID 
	INNER JOIN dbo.tblParticipants AS p ON s.participantID = p.participantID 
	INNER JOIN trialsmith.dbo.depoTLA AS tla ON tla.State = p.orgcode 
	ON tmp.seminarID = s.seminarID 
LEFT OUTER JOIN dbo.tblBundles AS b 
	INNER JOIN dbo.tblParticipants AS p2 ON b.participantID = p2.participantID 
	INNER JOIN trialsmith.dbo.depoTLA AS tla2 ON tla2.State = p2.orgcode 
	ON tmp.bundleID = b.bundleID
WHERE tmp.autoID >= @startRow
ORDER BY contentName
GO
ALTER PROC [dbo].[swod_getSeminarsForSWAdmin]
@orgcode varchar(5),
@startRow int,
@maxrows int

AS

-- create temp table to store all seminars in catalog
DECLARE @tmpTable TABLE (
	autoID int IDENTITY(1,1),
	seminarID int,
	seminarName varchar(250),
	publisherOrgCode varchar(5),
	totalCount int
)
declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@orgcode)

INSERT INTO @tmpTable (seminarID, seminarName, publisherOrgCode)
select seminarID, seminarName, publisherOrgCode
from (
	select s.seminarID, s.seminarName, p.orgcode as publisherOrgCode
	FROM dbo.tblSeminars as s
	INNER JOIN dbo.tblSeminarsSWOD as swod on swod.seminarID = s.seminarID
	INNER JOIN dbo.tblParticipants as p on p.participantID = s.participantID
	WHERE p.orgcode = @orgcode
	and s.isDeleted = 0
	and s.isPublished = 1
	union
	SELECT s.seminarID, s.seminarName, p2.orgcode as publisherOrgCode
	FROM dbo.tblParticipants AS p 
	INNER JOIN dbo.tblSeminarsOptIn AS soi ON p.participantID = soi.participantID 
	INNER JOIN dbo.tblSeminars AS s ON soi.seminarID = s.seminarID 
	INNER JOIN dbo.tblSeminarsSWOD AS swod ON s.seminarID = swod.seminarID
	INNER JOIN dbo.tblParticipants AS p2 ON p2.participantID = s.participantID
	WHERE p.orgcode = @orgcode
	and s.isDeleted = 0
	and s.isPublished = 1
) as tmp
ORDER BY seminarName

-- update total count
UPDATE @tmpTable SET totalCount = @@ROWCOUNT

-- get seminarIDList for the export button
declare @seminarIDList varchar(max)
select @seminarIDList = COALESCE(@seminarIDList + ',', '') + cast(seminarID as varchar(10)) FROM @tmpTable

SELECT TOP (@maxrows) tmp.seminarID, tmp.seminarName, tmp.publisherOrgCode, tmp.totalCount, (
	SELECT count(e.enrollmentID)
	FROM dbo.tblEnrollments AS e 
	INNER JOIN dbo.tblEnrollmentsSWOD AS eswod ON e.enrollmentID = eswod.enrollmentID 
	INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
	INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
	INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
	INNER JOIN dbo.tblSeminarsSWOD AS sswod ON e.seminarID = sswod.seminarID
	INNER JOIN dbo.tblSeminars as s on s.seminarid = sswod.seminarID
	WHERE e.seminarID = tmp.seminarID
	AND e.isActive = 1
	AND (e.participantID = @participantID OR s.participantID = @participantID)
	AND (d.adminflag2 is null or d.adminflag2 <> 'Y')
) as enrolledCount, @seminarIDList as seminarIDList
FROM @tmpTable tmp
WHERE tmp.autoID >= @startRow
ORDER BY tmp.autoID
GO
ALTER PROC [dbo].[swod_getSeminarsOptedInByOrgcode]
@orgcode varchar(5)

AS

declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@orgcode)

SELECT s.seminarID, s.seminarName, s.isPublished, p.orgcode
FROM dbo.tblSeminars as s
INNER JOIN dbo.tblSeminarsOptIn as soi on soi.seminarID = s.seminarID
INNER JOIN dbo.tblSeminarsSWOD as swod on swod.seminarID = s.seminarID
INNER JOIN dbo.tblParticipants as p on p.participantID = s.participantID
WHERE soi.participantID = @participantID
AND (swod.allowSyndication = 1 OR p.participantID = @participantID)
AND s.isDeleted = 0
ORDER BY p.orgcode, s.seminarName
GO
ALTER PROC [dbo].[swod_getSeminarsOptedOutByOrgcode]
@orgcode varchar(5)

AS
declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@orgcode)

SELECT s.seminarID, s.seminarName, s.isPublished, p.orgcode
FROM dbo.tblSeminarsSWOD AS sod 
INNER JOIN dbo.tblSeminars AS s ON sod.seminarID = s.seminarID 
INNER JOIN dbo.tblParticipants as p on p.participantID = s.participantID
LEFT OUTER JOIN dbo.tblSeminarsOptIn AS soi ON s.seminarID = soi.seminarID 
	AND soi.participantID = @participantID
WHERE soi.optInID IS NULL
AND s.isPublished = 1
AND s.isDeleted = 0
ORDER BY p.orgcode, s.seminarName
GO
ALTER PROC [dbo].[swod_getSeminarsForSWAdminBySearch]
@orgcode varchar(5),
@pn varchar(200),
@startRow int,
@maxrows int

AS

-- create temp table to store all seminars in catalog
DECLARE @tmpTable TABLE (
	autoID int IDENTITY(1,1),
	seminarID int,
	seminarName varchar(250),
	publisherOrgCode varchar(5),
	totalCount int
)
declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@orgcode)

INSERT INTO @tmpTable (seminarID, seminarName, publisherOrgCode)
select seminarID, seminarName, publisherOrgCode
from (
	select s.seminarID, s.seminarName, p.orgcode as publisherOrgCode
	FROM dbo.tblSeminars as s
	INNER JOIN dbo.tblSeminarsSWOD as swod on swod.seminarID = s.seminarID
	INNER JOIN dbo.tblParticipants as p on p.participantID = s.participantID
	WHERE p.orgcode = @orgcode
	and s.isDeleted = 0
	and s.isPublished = 1
	union
	SELECT s.seminarID, s.seminarName, p2.orgcode as publisherOrgCode
	FROM dbo.tblParticipants AS p 
	INNER JOIN dbo.tblSeminarsOptIn AS soi ON p.participantID = soi.participantID 
	INNER JOIN dbo.tblSeminars AS s ON soi.seminarID = s.seminarID 
	INNER JOIN dbo.tblSeminarsSWOD AS swod ON s.seminarID = swod.seminarID
	INNER JOIN dbo.tblParticipants AS p2 ON p2.participantID = s.participantID
	WHERE p.orgcode = @orgcode
	and s.isDeleted = 0
	and s.isPublished = 1
) as tmp
where seminarName LIKE '%' + @pn + '%'
ORDER BY seminarName

-- update total count
UPDATE @tmpTable SET totalCount = @@ROWCOUNT

-- get seminarIDList for the export button
declare @seminarIDList varchar(max)
select @seminarIDList = COALESCE(@seminarIDList + ',', '') + cast(seminarID as varchar(10)) FROM @tmpTable

SELECT TOP (@maxrows) tmp.seminarID, tmp.seminarName, tmp.publisherOrgCode, tmp.totalCount, (
	SELECT count(e.enrollmentID)
	FROM dbo.tblEnrollments AS e 
	INNER JOIN dbo.tblEnrollmentsSWOD AS eswod ON e.enrollmentID = eswod.enrollmentID 
	INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
	INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
	INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
	INNER JOIN dbo.tblSeminarsSWOD AS sswod ON e.seminarID = sswod.seminarID
	INNER JOIN dbo.tblSeminars as s on s.seminarid = sswod.seminarID
	WHERE e.seminarID = tmp.seminarID
	AND e.isActive = 1
	AND (e.participantID = @participantID OR s.participantID = @participantID)
	AND (d.adminflag2 is null or d.adminflag2 <> 'Y')
) as enrolledCount, @seminarIDList as seminarIDList
FROM @tmpTable tmp
WHERE tmp.autoID >= @startRow
ORDER BY tmp.autoID
GO
ALTER PROC [dbo].[swtl_getAuthorsForCatalog]
@catalogorgcode varchar(5),
@letter char(1),
@startRow int,
@maxrows int

AS

-- create temp table
DECLARE @tmpTable TABLE (
	autoID int IDENTITY(1,1),
	authorID int,
	prefix varchar(10),
	firstname varchar(30),
	middlename varchar(30),
	lastname varchar(40),
	suffix varchar(30),
	titleCount int,
	totalCount int
)
declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@catalogorgcode)

INSERT INTO @tmpTable (authorID, prefix, firstname, middlename, lastname, suffix, titleCount)
SELECT authorID, prefix, firstname, middlename, lastname, suffix, sum(itemCount)
FROM (	
	SELECT a.authorID, a.prefix, a.firstname, a.middlename, a.lastName, a.suffix, COUNT(DISTINCT taa.titleID) as itemCount
	FROM dbo.tblAuthors AS a 
	INNER JOIN dbo.tblTitlesAndAuthors AS taa ON taa.authorID = a.authorID 
	INNER JOIN dbo.swtl_TitlesInMyCatalog(@catalogorgcode) AS tmc ON tmc.titleID = taa.titleID
	GROUP BY a.authorID, a.prefix, a.firstname, a.middlename, a.lastName, a.suffix
		union all
	SELECT a.authorID, a.prefix, a.firstname, a.middlename, a.lastName, a.suffix, COUNT(DISTINCT b.bundleID) as itemCount
	FROM dbo.tblBundles AS b
	INNER JOIN dbo.swb_BundlesInMyCatalog(@catalogorgcode) AS bmc ON bmc.bundleID = b.bundleID
	INNER JOIN dbo.tblBundledItems as bi on bi.bundleID = bmc.bundleID
	INNER JOIN dbo.tbltitles as t on t.titleID = bi.titleID
	INNER JOIN dbo.tblTitlesAndAuthors AS taa ON taa.titleID = t.titleID 
	INNER JOIN dbo.tblAuthors AS a on a.authorID = taa.authorID
	where t.participantID = @participantID
	AND t.isPublished = 1	
	AND t.isDeleted = 0
	GROUP BY a.authorID, a.prefix, a.firstname, a.middlename, a.lastName, a.suffix
) as tmp
WHERE left(tmp.lastname,1) = CASE
	WHEN len(@letter) > 0 THEN @letter
	ELSE left(tmp.lastname,1)
	END
GROUP BY authorID, prefix, firstname, middlename, lastname, suffix
ORDER BY lastname, firstname

-- get total count
UPDATE @tmpTable SET totalCount = @@ROWCOUNT

-- return based on start/max rows
SELECT TOP (@maxrows) *
FROM @tmpTable
WHERE autoID >= @startRow
ORDER BY autoID
GO
ALTER PROC [dbo].[swtl_getCategoriesForCatalog]
@catalogorgcode varchar(5),
@letter char(1),
@startRow int,
@maxrows int

AS

-- create temp table
DECLARE @tmpTable TABLE (
	autoID int IDENTITY(1,1),
	categoryName varchar(75),
	titleCount int,
	totalCount int
)
declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@catalogorgcode)

INSERT INTO @tmpTable (categoryName, titleCount)
SELECT CategoryName, sum(itemCount)
FROM (	
	SELECT c.CategoryName, COUNT(DISTINCT tac.titleID) as itemCount
	FROM dbo.tblCategories AS c
	INNER JOIN dbo.tblTitlesAndCategories AS tac ON c.categoryID = tac.categoryID 
	INNER JOIN dbo.swtl_TitlesInMyCatalog(@catalogorgcode) AS tmc ON tac.titleID = tmc.titleID
	GROUP BY c.categoryName
		union all
	SELECT c.CategoryName, COUNT(DISTINCT b.bundleID) as itemCount
	FROM dbo.tblBundles AS b
	INNER JOIN dbo.swb_BundlesInMyCatalog(@catalogorgcode) AS bmc ON bmc.bundleID = b.bundleID
	INNER JOIN dbo.tblBundledItems as bi on bi.bundleID = bmc.bundleID
	INNER JOIN dbo.tbltitles as t on t.titleID = bi.titleID
	INNER JOIN dbo.tblTitlesAndCategories AS tac ON tac.titleID = t.titleID 
	INNER JOIN dbo.tblCategories AS c on c.categoryID = tac.categoryID
	where t.participantID = @participantID
	AND t.isPublished = 1	
	AND t.isDeleted = 0
	GROUP BY c.categoryName
) as tmp
WHERE left(CategoryName,1) = CASE
	WHEN len(@letter) > 0 THEN @letter
	ELSE left(CategoryName,1)
	END
GROUP BY CategoryName
ORDER BY CategoryName

-- get total count
UPDATE @tmpTable SET totalCount = @@ROWCOUNT

-- return based on start/max rows
SELECT TOP (@maxrows) *
FROM @tmpTable
WHERE autoID >= @startRow
ORDER BY autoID
GO
ALTER PROC [dbo].[swtl_getDatesPublishedForCatalog]
@catalogorgcode varchar(5),
@startRow int,
@maxrows int

AS

-- create temp table
DECLARE @tmpTable TABLE (
	autoID int IDENTITY(1,1),
	participantID int,	
	dateOrigPublished datetime,
	titleCount int,
	totalCount int
)

INSERT INTO @tmpTable (participantID, dateOrigPublished, titleCount)
SELECT participantID, dateOrigPublished, sum(itemCount)
FROM (	
	SELECT p.participantID, t.dateOrigPublished, COUNT(DISTINCT t.titleID) AS itemCount
	FROM dbo.tblTitles AS t
	INNER JOIN dbo.swtl_TitlesInMyCatalog(@catalogorgcode) AS tmc ON tmc.titleID = t.titleID
	INNER JOIN dbo.tblParticipants as p on p.participantID = t.participantID
	GROUP BY p.participantID, t.dateOrigPublished
		union all
	SELECT p.participantID, t.dateOrigPublished, COUNT(DISTINCT b.bundleID) as itemCount
	FROM dbo.tblBundles AS b
	INNER JOIN dbo.swb_BundlesInMyCatalog(@catalogorgcode) AS bmc ON bmc.bundleID = b.bundleID
	INNER JOIN dbo.tblBundledItems as bi on bi.bundleID = bmc.bundleID
	INNER JOIN dbo.tblTitles AS t on t.titleID = bi.titleID
	INNER JOIN dbo.tblParticipants as p on p.participantID = t.participantID	
	where p.orgcode = @catalogOrgCode
	AND t.isPublished = 1
	GROUP BY p.participantID, t.dateOrigPublished
) as tmp
GROUP BY participantID, dateOrigPublished
ORDER BY dateOrigPublished DESC

-- get total count
UPDATE @tmpTable SET totalCount = @@ROWCOUNT

-- return based on start/max rows
SELECT TOP (@maxrows) *
FROM @tmpTable
WHERE autoID >= @startRow
ORDER BY autoID
GO
ALTER PROC [dbo].[swtl_getEnrollmentsForSWAdmin]
@titleID int,
@orgcode varchar(5)

AS
declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@orgcode)

SELECT TOP 100 PERCENT e.enrollmentID, e.userid, p.orgcode, p.catalogURL, 
	d.depomemberdataID, d.FirstName, d.LastName, d.Fax, d.Email, e.dateEnrolled, 
	(SELECT COUNT(*) FROM dbo.tblLogSWTL AS log1 WHERE enrollmentID = e.enrollmentID AND titleID = e.titleID AND contact LIKE '%@%') AS EmailCount
FROM dbo.tblEnrollments AS e 
INNER JOIN dbo.tblTitles as t on t.titleid = e.titleID
INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
WHERE e.titleID = @titleID
AND e.isActive = 1
AND (e.participantID = @participantID OR t.participantID = @participantID)
AND (d.adminflag2 is null or d.adminflag2 <> 'Y')
ORDER BY e.dateEnrolled
GO
ALTER PROC [dbo].[swtl_getParticipantsSyndication]
@orgcode varchar(5)

AS
declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@orgcode)

-- qrySyndTo
SELECT p2.orgcode, tla.Description
FROM dbo.tblSWTLOptInSend AS ois 
INNER JOIN dbo.tblParticipants AS p ON ois.participantID = p.participantID 
INNER JOIN dbo.tblParticipants AS p2 ON ois.syndicateParticipantID = p2.participantID 
INNER JOIN trialsmith.dbo.depoTLA AS tla ON tla.State = p2.orgcode
WHERE p.orgcode = @orgcode
ORDER BY tla.Description

-- qrySyndToRemaining
SELECT p.orgcode, tla.Description
FROM dbo.tblParticipants AS p 
INNER JOIN trialsmith.dbo.depoTLA AS tla ON tla.State = p.orgcode 
LEFT OUTER JOIN dbo.tblSWTLOptInSend AS ois ON p.participantID = ois.syndicateParticipantID 
	AND ois.participantID = @participantID
WHERE p.isSWTL = 1
AND ois.sendID IS NULL
AND p.participantID <> @participantID
ORDER BY tla.Description

-- qrySyndAcceptFrom
SELECT p2.orgcode, tla.Description
FROM dbo.tblSWTLOptInReceive AS oir 
INNER JOIN dbo.tblParticipants AS p ON oir.participantID = p.participantID 
INNER JOIN dbo.tblParticipants AS p2 ON oir.syndicateParticipantID = p2.participantID 
INNER JOIN trialsmith.dbo.depoTLA AS tla ON tla.State = p2.orgcode
WHERE p.orgcode = @orgcode
ORDER BY tla.Description

-- qrySyndAcceptFromRemaining
SELECT p.orgcode, tla.Description
FROM dbo.tblParticipants AS p 
INNER JOIN trialsmith.dbo.depoTLA AS tla ON tla.State = p.orgcode 
LEFT OUTER JOIN dbo.tblSWTLOptInReceive AS oir ON p.participantID = oir.syndicateParticipantID 
	AND oir.participantID = @participantID
WHERE p.isSWTL = 1
AND oir.receiveID IS NULL
AND p.participantID <> @participantID
ORDER BY tla.Description

RETURN
GO
ALTER PROC [dbo].[swtl_getPublishersForCatalog]
@catalogorgcode varchar(5),
@letter char(1),
@startRow int,
@maxrows int

AS

-- create temp table
DECLARE @tmpTable TABLE (
	autoID int IDENTITY(1,1),
	participantID int,	
	publisherName varchar(200),
	titleCount int,
	totalCount int
)
declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@catalogorgcode)

INSERT INTO @tmpTable (participantID, publisherName, titleCount)
SELECT participantID, description, sum(itemCount)
FROM (	
	SELECT p.participantID, tla.description, COUNT(DISTINCT t.titleID) AS itemCount
	FROM dbo.tblTitles AS t
	INNER JOIN dbo.swtl_TitlesInMyCatalog(@catalogorgcode) AS tmc ON tmc.titleID = t.titleID
	INNER JOIN dbo.tblParticipants as p on p.participantID = t.participantID
	INNER JOIN trialsmith.dbo.depoTLA as tla on tla.state = p.orgcode	
	GROUP BY p.participantID, tla.description
		union all
	SELECT p.participantID, tla.description, COUNT(DISTINCT b.bundleID) as itemCount
	FROM dbo.tblBundles AS b
	INNER JOIN dbo.swb_BundlesInMyCatalog(@catalogorgcode) AS bmc ON bmc.bundleID = b.bundleID
	INNER JOIN dbo.tblBundledItems as bi on bi.bundleID = bmc.bundleID
	INNER JOIN dbo.tblTitles AS t on t.titleID = bi.titleID
	INNER JOIN dbo.tblParticipants as p on p.participantID = t.participantID
	INNER JOIN trialsmith.dbo.depoTLA as tla on tla.state = p.orgcode	
	where t.participantID = @participantID
	AND t.isPublished = 1
	GROUP BY p.participantID, tla.description
) as tmp
WHERE left(description,1) = CASE
	WHEN len(@letter) > 0 THEN @letter
	ELSE left(description,1)
	END
GROUP BY participantID, description
ORDER BY description

-- get total count
UPDATE @tmpTable SET totalCount = @@ROWCOUNT

-- return based on start/max rows
SELECT TOP (@maxrows) *
FROM @tmpTable
WHERE autoID >= @startRow
ORDER BY autoID
GO
ALTER PROC [dbo].[swtl_getTitlesForCatalogByAuthor]
@catalogOrgCode varchar(5),
@letter char(1),
@authorID int,
@startRow int,
@maxrows int,
@depomemberdataID int,
@depomemberSiteGroups varchar(max)

AS

-- create temp table to store all titles in catalog
DECLARE @tmpTable TABLE (
	autoID int IDENTITY(1,1),
	titleID int,
	bundleID int,
	totalCount int
)
declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@catalogorgcode)

INSERT INTO @tmpTable (titleID, bundleID)
select titleID, bundleID
FROM (	
	SELECT t.titleID, null as bundleID, t.titleName as contentName
	FROM dbo.tblTitles as t
	INNER JOIN dbo.swtl_titlesInMyCatalog(@catalogOrgCode) tmc on tmc.titleID = t.titleID
	INNER JOIN dbo.tblTitlesAndAuthors AS taa ON taa.titleID = tmc.titleID
	WHERE taa.authorID = @authorID
		union	
	SELECT null as titleID, b.bundleID, b.bundleName as contentName
	FROM dbo.tblBundles as b
	INNER JOIN dbo.swb_BundlesInMyCatalog(@catalogOrgCode) AS bmc ON bmc.bundleID = b.bundleID
	INNER JOIN dbo.tblBundledItems as bi on bi.bundleID = bmc.bundleID
	INNER JOIN dbo.tblTitles as t on t.titleID = bi.titleID	
	INNER JOIN dbo.tblTitlesAndAuthors AS taa ON taa.titleID = t.titleID
	WHERE t.participantID = @participantID
	AND t.isPublished = 1
	AND taa.authorID = @authorID
) as iTemp
WHERE left(itemp.contentName,1) = CASE
	WHEN len(@letter) > 0 THEN @letter
	ELSE left(itemp.contentName,1)
	END
ORDER BY itemp.contentName

-- update total count
UPDATE @tmpTable SET totalCount = @@ROWCOUNT

-- return subset of records for display with all other fields
SELECT TOP (@maxrows) 
	CASE WHEN tmp.titleID is not null THEN 'SWTL' ELSE 'SWB' END as 'format',
	isnull(tmp.titleID,tmp.bundleID) as contentID,
	isnull(t.titleName,b.bundleName) as contentName,
	CASE 
	WHEN tmp.titleID is not null AND @depomemberdataID > 0 THEN dbo.swtl_isRegisteredForTitle(tmp.titleID,@depomemberdataid) 
	WHEN tmp.bundleID is not null AND @depomemberdataID > 0 THEN dbo.swb_isRegisteredForBundle(tmp.bundleID,@depoMemberdataid)
	ELSE 0 
	END as isRegistered, 	
	CASE WHEN tmp.titleID is not null then dbo.swtl_getPriceForTitle(tmp.titleID,@catalogOrgCode,@depomemberdataid,@depomemberSiteGroups) ELSE dbo.sw_getPriceForBundle(tmp.bundleID,@catalogOrgCode,@depomemberdataid,@depomemberSiteGroups) END as price,
	isnull(tla.description,tla2.description) as description,
	isnull(p.orgcode,p2.orgcode) as publisherOrgcode,
	CASE WHEN tmp.titleID is not null THEN (select count(*) from dbo.tblTitlesAndGroups where titleID = tmp.titleID) ELSE (select count(*) from dbo.tblBundlesAndGroups where bundleID = tmp.bundleID) END as NumPriceOptions,
	tmp.totalCount,
	isnull((
		SELECT [file].fileType, count(distinct xmlf.fileID) as fileCount
		FROM dbo.tblTitlesAndFiles AS xmltaf 
		INNER JOIN dbo.tblFiles AS xmlf ON xmltaf.fileID = xmlf.fileID 
		INNER JOIN dbo.tblFilesTypes AS [file] ON xmlf.fileTypeID = [file].filetypeID
		INNER JOIN dbo.tblParticipants as xmlp ON xmlf.participantID = xmlp.participantID
		WHERE xmltaf.titleID = tmp.titleID
		AND xmlf.isDeleted = 0
		group by [file].fileType
		ORDER BY [file].fileType
		FOR XML AUTO, ROOT('files')
	),'<files/>') as componentsXML
FROM @tmpTable as tmp
LEFT OUTER JOIN dbo.tblTitles as t 
	INNER JOIN dbo.tblParticipants AS p ON t.participantID = p.participantID 
	INNER JOIN trialsmith.dbo.depoTLA AS tla ON tla.State = p.orgcode 
	on t.titleID = tmp.titleID
LEFT OUTER JOIN dbo.tblBundles AS b 
	INNER JOIN dbo.tblParticipants AS p2 ON b.participantID = p2.participantID 
	INNER JOIN trialsmith.dbo.depoTLA AS tla2 ON tla2.State = p2.orgcode 
	ON tmp.bundleID = b.bundleID
WHERE tmp.autoID >= @startRow
ORDER BY tmp.autoID
GO
ALTER PROC [dbo].[swtl_addSyndicationReceive]
@orgcode varchar(5),
@org varchar(5)

AS
declare @participantID int, @syndParticipantID int
select 
	@participantID = dbo.fn_getParticipantIDFromOrgcode(@orgcode),
	@syndParticipantID = dbo.fn_getParticipantIDFromOrgcode(@org)

IF NOT EXISTS (SELECT receiveID from dbo.tblSWTLOptInReceive where participantID = @participantID and syndicateParticipantID = @syndParticipantID)
	INSERT INTO dbo.tblSWTLOptInReceive (participantID, syndicateParticipantID)
	VALUES (@participantID, @syndParticipantID)

RETURN
GO
ALTER PROC [dbo].[swtl_getTitlesForSWAdminBySearch]
@OrgCode varchar(5),
@pn varchar(200),
@startRow int,
@maxrows int

AS

-- create temp table to store all titles in catalog
DECLARE @tmpTable TABLE (
	autoID int IDENTITY(1,1),
	titleID int,
	titleName varchar(250),
	publisherOrgCode varchar(5),
	totalCount int
)
declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@OrgCode)

INSERT INTO @tmpTable (titleID, titleName, publisherOrgCode)
select titleID, titleName, publisherOrgCode
from (
	select t.titleID, t.titleName, p.orgcode as publisherOrgCode
	FROM dbo.tblTitles as t
	INNER JOIN dbo.tblParticipants as p on p.participantID = t.participantID
	WHERE p.orgcode = @orgcode
	and t.isDeleted = 0
	and t.isPublished = 1
	and t.datecatalogstart is not null
	union
	SELECT t.titleID, t.titleName, p.orgcode as publisherOrgCode
	FROM dbo.tblTitles as t
	INNER JOIN dbo.tblSWTLOptInReceive as oir on oir.syndicateParticipantID = t.participantID
	INNER JOIN dbo.tblSWTLOptInSend as ois on ois.participantID = t.participantID
	INNER JOIN dbo.tblParticipants as p on p.participantID = t.participantID
	WHERE t.participantID <> @participantID
	AND oir.participantID = @participantID
	AND ois.syndicateParticipantID = @participantID
	AND t.isPublished = 1
	AND t.isDeleted = 0
	AND t.allowSyndication = 1
	and t.datecatalogstart is not null
	AND p.isSWTL = 1
) as tmp
where titleName LIKE '%' + @pn + '%'
ORDER BY titleName

-- update total count
UPDATE @tmpTable SET totalCount = @@ROWCOUNT

-- get titleIDList for the export button
declare @titleIDList varchar(max)
select @titleIDList = COALESCE(@titleIDList + ',', '') + cast(titleID as varchar(10)) FROM @tmpTable

SELECT TOP (@maxrows) tmp.titleID, tmp.titleName, tmp.publisherOrgCode, tmp.totalCount, (
	SELECT count(e.enrollmentID)
	FROM dbo.tblEnrollments AS e 
	INNER JOIN dbo.tblEnrollmentsSWTL AS eswtl ON e.enrollmentID = eswtl.enrollmentID 
	INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
	INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
	INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
	INNER JOIN dbo.tblTitles as t on t.titleid = e.titleID
	WHERE e.titleid = tmp.titleID
	AND e.isActive = 1
	AND (e.participantID = @participantID OR t.participantID = @participantID)
	AND (d.adminflag2 is null or d.adminflag2 <> 'Y')
) as enrolledCount, @titleIDList as titleIDList
FROM @tmpTable tmp
WHERE tmp.autoID >= @startRow
ORDER BY tmp.autoID
GO
ALTER PROC [dbo].[swtl_getTitlesForSWAdmin]
@OrgCode varchar(5),
@startRow int,
@maxrows int

AS

-- create temp table to store all titles in catalog
DECLARE @tmpTable TABLE (
	autoID int IDENTITY(1,1),
	titleID int,
	titleName varchar(250),
	publisherOrgCode varchar(5),
	totalCount int
)
declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@OrgCode)

INSERT INTO @tmpTable (titleID, titleName, publisherOrgCode)
select titleID, titleName, publisherOrgCode
from (
	select t.titleID, t.titleName, p.orgcode as publisherOrgCode
	FROM dbo.tblTitles as t
	INNER JOIN dbo.tblParticipants as p on p.participantID = t.participantID
	WHERE p.orgcode = @orgcode
	and t.isDeleted = 0
	and t.isPublished = 1
	and t.datecatalogstart is not null
	union
	SELECT t.titleID, t.titleName, p.orgcode as publisherOrgCode
	FROM dbo.tblTitles as t
	INNER JOIN dbo.tblSWTLOptInReceive as oir on oir.syndicateParticipantID = t.participantID
	INNER JOIN dbo.tblSWTLOptInSend as ois on ois.participantID = t.participantID
	INNER JOIN dbo.tblParticipants as p on p.participantID = t.participantID
	WHERE t.participantID <> @participantID
	AND oir.participantID = @participantID
	AND ois.syndicateParticipantID = @participantID
	AND t.isPublished = 1
	AND t.isDeleted = 0
	AND t.allowSyndication = 1
	and t.datecatalogstart is not null
	AND p.isSWTL = 1
) as tmp
ORDER BY titleName

-- update total count
UPDATE @tmpTable SET totalCount = @@ROWCOUNT

-- get titleIDList for the export button
declare @titleIDList varchar(max)
select @titleIDList = COALESCE(@titleIDList + ',', '') + cast(titleID as varchar(10)) FROM @tmpTable

SELECT TOP (@maxrows) tmp.titleID, tmp.titleName, tmp.publisherOrgCode, tmp.totalCount, (
	SELECT count(e.enrollmentID)
	FROM dbo.tblEnrollments AS e 
	INNER JOIN dbo.tblEnrollmentsSWTL AS eswtl ON e.enrollmentID = eswtl.enrollmentID 
	INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
	INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
	INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
	INNER JOIN dbo.tblTitles as t on t.titleid = e.titleID
	WHERE e.titleid = tmp.titleID
	AND e.isActive = 1
	AND (e.participantID = @participantID OR t.participantID = @participantID)
	AND (d.adminflag2 is null or d.adminflag2 <> 'Y')
) as enrolledCount, @titleIDList as titleIDList
FROM @tmpTable tmp
WHERE tmp.autoID >= @startRow
ORDER BY tmp.autoID
GO
ALTER PROC [dbo].[swtl_removeSyndicationReceive]
@orgcode varchar(5),
@org varchar(5)

AS
declare @participantID int, @syndParticipantID int
select 
	@participantID = dbo.fn_getParticipantIDFromOrgcode(@orgcode),
	@syndParticipantID = dbo.fn_getParticipantIDFromOrgcode(@org)


DELETE FROM dbo.tblSWTLOptInReceive
WHERE participantID =@participantID
AND syndicateParticipantID = @syndParticipantID

RETURN
GO
ALTER PROC [dbo].[swtl_removeSyndicationSend]
@orgcode varchar(5),
@org varchar(5)

AS
declare @participantID int, @syndParticipantID int
select 
	@participantID = dbo.fn_getParticipantIDFromOrgcode(@orgcode),
	@syndParticipantID = dbo.fn_getParticipantIDFromOrgcode(@org)

DELETE FROM dbo.tblSWTLOptInSend
WHERE participantID = @participantID
AND syndicateParticipantID = @syndParticipantID

RETURN
GO
ALTER PROC [dbo].[sw_getBundlesByOrgCodeWithOptins]
@orgcode varchar(5)

AS
declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@OrgCode)

SELECT b.bundleID, p.orgcode, b.bundleName
FROM dbo.tblBundles AS b 
	INNER JOIN dbo.tblParticipants AS p ON b.participantID = p.participantID 
	INNER JOIN dbo.tblBundlesOptIn as boi on boi.bundleID = b.bundleID
WHERE boi.participantID = @participantID
ORDER BY p.orgcode, b.bundleName

RETURN
GO
ALTER PROC [dbo].[sw_getBundlesContainingTitle]
@titleID int,
@orgcode varchar(5)

AS

SELECT b.bundleID, b.bundleName
FROM dbo.tblBundledItems AS bi 
INNER JOIN dbo.tblBundles AS b ON bi.bundleID = b.bundleID
INNER jOIN dbo.tblParticipants p on p.participantID = b.participantID and p.orgcode = @orgcode
WHERE bi.titleID = @titleID
ORDER BY b.bundleName

RETURN
GO
