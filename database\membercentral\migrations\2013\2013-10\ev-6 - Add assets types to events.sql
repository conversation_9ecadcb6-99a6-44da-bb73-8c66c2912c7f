﻿USE [memberCentral]
GO

CREATE PROCEDURE [dbo].[cms_createDefaultEventRoles]
	@siteID int
AS
BEGIN TRAN

	declare @categoryTreeID int
	declare @controllingSiteResourceID int

	select @controllingSiteResourceID = dbo.fn_getSiteResourceIDForResourceType('EventAdmin', @siteID)

	IF @controllingSiteResourceID is NULL GOTO on_error

	select @categoryTreeID = categoryTreeID
	from cms_categoryTrees
	where controllingSiteResourceID = @controllingSiteResourceID
	and categoryTreeCode = 'EventRoles'

	-- create category tree
	IF @categoryTreeID is NULL
	BEGIN
		exec dbo.cms_createCategoryTree @siteID=@siteID, @categoryTreeName='Event Roles',
			@categoryTreeDesc='Roles to be assigned to registrants',
			@categoryTreeCode='EventRoles',
			@controllingSiteResourceID=@controllingSiteResourceID,
			@categoryTreeID=@categoryTreeID OUTPUT	
		IF @@ERROR <> 0 GOTO on_error
	END

IF @@TRANCOUNT > 0 COMMIT TRAN	

-- normal exit
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO


DECLARE
	@toolTypeID int,
	@toolResourceTypeID int,
	@roleID int,
	@navigationID int,
	@resourceTypeFunctionID int,
	@orgcode varchar(5),
	@orgID int,
	@sitecode varchar(5),
	@siteID int,
	@TopTabNavigationID int,
	@level2EventSettingsNavigationID int,
	@rc int

	-- get top tab ID
	select 
		@TopTabNavigationID = navigationID 
	from 
		dbo.admin_navigation 
	where 
		navName = 'Events' 
		and parentNavigationID is null

	-- create level2 items

	EXEC @rc = dbo.createAdminNavigation
		@navName='Settings',
		@navDesc='Event Settings',
		@parentNavigationID=@TopTabNavigationID,
		@navAreaID=2,
		@cfcMethod=null,
		@isHeader=0,
		@showInNav=1,
		@navigationID=@level2EventSettingsNavigationID OUTPUT

	select @toolTypeID = null, @toolResourceTypeID = null

	-- create Admin tool type
	select @toolTypeID = toolTypeID, @toolResourceTypeID=resourceTypeID 
	from admin_tooltypes tt
	where tooltype = 'EventAdmin'


	DECLARE @manageEventRolesFunctionID int, @manageEventRolesResourceTypeFunctionID int, 
		@superAdminRoleID int, @siteAdminRoleID int

	select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')
	select @siteAdminRoleID = dbo.fn_getResourceRoleID('Site Administrator')

	-- Manage Event Roles permissions
	EXEC @rc = cms_createSiteResourceFunction 
				@resourceTypeID=@toolResourceTypeID, 
				@functionName='manageEventRoles', 
				@displayName='Manage Event Roles', 
				@functionID=@manageEventRolesFunctionID OUTPUT

	select @manageEventRolesResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@toolResourceTypeID,@manageEventRolesFunctionID);

	exec @rc = dbo.cms_createSiteResourceRoleFunction 
				@roleID=@superAdminRoleID, 
				@resourceTypeFunctionID=@manageEventRolesResourceTypeFunctionID;

	exec @rc = dbo.cms_createSiteResourceRoleFunction 
				@roleID=@siteAdminRoleID, 
				@resourceTypeFunctionID=@manageEventRolesResourceTypeFunctionID;

	select @navigationID = null
	EXEC @rc = dbo.createAdminNavigation
		@navName='Event Roles',
		@navDesc='Manage Event Roles',
		@parentNavigationID=@level2EventSettingsNavigationID,
		@navAreaID=3,
		@cfcMethod='listEventRoles',
		@isHeader=0,
		@showInNav=1,
		@navigationID=@navigationID OUTPUT

	EXEC @rc = dbo.createAdminFunctionsDeterminingNav
		@resourceTypeFunctionID=@manageEventRolesResourceTypeFunctionID,
		@toolTypeID=@toolTypeID,
		@navigationID=@navigationID
GO

DECLARE @thisSiteID int

select @thisSiteID = min(siteID) from sites

while @thisSiteID is not null BEGIN
	exec [cms_createDefaultEventRoles] @siteID = @thisSiteID
	select @thisSiteID = min(siteID) from sites where siteID > @thisSiteID
END
GO


CREATE TABLE [dbo].[ev_registrantCategories](
	[registrantCategoryID] [int] IDENTITY(1,1) NOT NULL,
	[registrantID] [int] NOT NULL,
	[categoryID] [int] NOT NULL,
 CONSTRAINT [PK_ev_registrantCategories] PRIMARY KEY CLUSTERED 
(
	[registrantCategoryID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE [dbo].[ev_registrantCategories]  WITH CHECK ADD  CONSTRAINT [FK_ev_registrantCategories_cms_categories] FOREIGN KEY([categoryID])
REFERENCES [dbo].[cms_categories] ([categoryID])
GO
ALTER TABLE [dbo].[ev_registrantCategories] CHECK CONSTRAINT [FK_ev_registrantCategories_cms_categories]
GO
ALTER TABLE [dbo].[ev_registrantCategories]  WITH CHECK ADD  CONSTRAINT [FK_ev_registrantCategories_ev_registrants] FOREIGN KEY([registrantID])
REFERENCES [dbo].[ev_registrants] ([registrantID])
GO
ALTER TABLE [dbo].[ev_registrantCategories] CHECK CONSTRAINT [FK_ev_registrantCategories_ev_registrants]
GO


CREATE FUNCTION [dbo].[fn_getCategoryTreeIDForSiteResourceIDAndTree] (@SiteResourceID varchar(50), @TreeName varchar(50))
RETURNS int
AS
BEGIN
	declare @categoryTreeID int
	
	select @categoryTreeID = isNull(ct.categoryTreeID, 0)
	from cms_categoryTrees ct
	INNER JOIN dbo.cms_siteResources sr 
		on sr.siteResourceID = ct.siteResourceID
	INNER JOIN dbo.cms_siteResourceStatuses srs 
		on srs.siteResourceStatusID = sr.siteResourceStatusID 
		and srs.siteResourceStatusDesc = 'Active'
	where ct.controllingSiteResourceID = @SiteResourceID
	and ct.categoryTreeName = @TreeName

	IF @categoryTreeID is null
		set @categoryTreeID = 0

	return @categoryTreeID
END
GO

CREATE PROC [dbo].[ev_updateEventRoles]
@registrantID int,
@eventRoles varchar(max)
AS


if @eventRoles is null OR len(rtrim(ltrim(@eventRoles))) = 0 begin
	-- if blank clear all roles since none were selected
	delete from dbo.ev_registrantCategories
	where registrantID = @registrantID
end
else begin
	-- otherwise delete all roles and add them back.
	delete from dbo.ev_registrantCategories
	where registrantID = @registrantID

	insert into dbo.ev_registrantCategories(registrantID, categoryID)
	select @registrantID, listitem
	from dbo.fn_intListToTable(@eventRoles, ',') 	
end

RETURN 0
GO

CREATE PROCEDURE [dbo].[cms_createDefaultEventAssetCategories]
	@siteID int
AS
BEGIN TRAN

	declare @categoryTreeID int
	declare @controllingSiteResourceID int

	select @controllingSiteResourceID = dbo.fn_getSiteResourceIDForResourceType('EventAdmin', @siteID)

	IF @controllingSiteResourceID is NULL GOTO on_error

	select @categoryTreeID = categoryTreeID
	from cms_categoryTrees
	where controllingSiteResourceID = @controllingSiteResourceID

	-- create category tree
	IF @categoryTreeID is NULL
	BEGIN
		exec dbo.cms_createCategoryTree @siteID=@siteID, @categoryTreeName='Event Asset Types',
			@categoryTreeDesc='Types of Event Assets and their subcategories',
			@categoryTreeCode='EventAssetTypes',
			@controllingSiteResourceID=@controllingSiteResourceID,
			@categoryTreeID=@categoryTreeID OUTPUT	
		IF @@ERROR <> 0 GOTO on_error
	END

IF @@TRANCOUNT > 0 COMMIT TRAN	

-- normal exit
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

DECLARE
	@toolTypeID int,
	@toolResourceTypeID int,
	@roleID int,
	@navigationID int,
	@resourceTypeFunctionID int,
	@orgcode varchar(5),
	@orgID int,
	@sitecode varchar(5),
	@siteID int,
	@TopTabNavigationID int,
	@level2EventSettingsNavigationID int,
	@rc int

	-- get top tab ID
	select 
		@TopTabNavigationID = navigationID 
	from 
		dbo.admin_navigation 
	where 
		navName = 'Events' 
		and parentNavigationID is null

	-- create level2 items

	EXEC @rc = dbo.createAdminNavigation
		@navName='Settings',
		@navDesc='Event Settings',
		@parentNavigationID=@TopTabNavigationID,
		@navAreaID=2,
		@cfcMethod=null,
		@isHeader=0,
		@showInNav=1,
		@navigationID=@level2EventSettingsNavigationID OUTPUT

	select @toolTypeID = null, @toolResourceTypeID = null

	-- create Admin tool type
	select @toolTypeID = toolTypeID, @toolResourceTypeID=resourceTypeID 
	from admin_tooltypes tt
	where tooltype = 'EventAdmin'


	DECLARE @manageAssetTypesFunctionID int, @manageAssetTypesResourceTypeFunctionID int, 
		@superAdminRoleID int, @siteAdminRoleID int

	select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')
	select @siteAdminRoleID = dbo.fn_getResourceRoleID('Site Administrator')

	-- Manage Event Asset Types permissions
	EXEC @rc = cms_createSiteResourceFunction 
				@resourceTypeID=@toolResourceTypeID, 
				@functionName='manageAssetTypes', 
				@displayName='Manage Event Asset Types', 
				@functionID=@manageAssetTypesFunctionID OUTPUT

	select @manageAssetTypesResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@toolResourceTypeID,@manageAssetTypesFunctionID);

	exec @rc = dbo.cms_createSiteResourceRoleFunction 
				@roleID=@superAdminRoleID, 
				@resourceTypeFunctionID=@manageAssetTypesResourceTypeFunctionID;

	exec @rc = dbo.cms_createSiteResourceRoleFunction 
				@roleID=@siteAdminRoleID, 
				@resourceTypeFunctionID=@manageAssetTypesResourceTypeFunctionID;

	select @navigationID = null
	EXEC @rc = dbo.createAdminNavigation
		@navName='Event Asset Types',
		@navDesc='Manage Event Asset Types',
		@parentNavigationID=@level2EventSettingsNavigationID,
		@navAreaID=3,
		@cfcMethod='listAssetCategories',
		@isHeader=0,
		@showInNav=1,
		@navigationID=@navigationID OUTPUT

	EXEC @rc = dbo.createAdminFunctionsDeterminingNav
		@resourceTypeFunctionID=@manageAssetTypesResourceTypeFunctionID,
		@toolTypeID=@toolTypeID,
		@navigationID=@navigationID

GO

DECLARE @thisSiteID int

select @thisSiteID = min(siteID) from sites

while @thisSiteID is not null BEGIN

	exec [cms_createDefaultEventAssetCategories] @siteID = @thisSiteID
	select @thisSiteID = min(siteID) from sites where siteID > @thisSiteID
END
GO



ALTER PROCEDURE [dbo].[cms_deleteCategory] 
@categoryID int
AS

BEGIN TRAN

-- only delete if it is not in use
-- this will check all foreign keys to the categoryID dynamically.
declare @innerUnion VARCHAR(max)
declare @catInUse bit
declare @dynSQL nvarchar(max)

SELECT @innerUnion = COALESCE(@innerUnion + ' union ' + char(13) + char(10), '') + 
	'select ' + COL_NAME(fc.parent_object_id, fc.parent_column_id) + ' as categoryID from dbo.' + OBJECT_NAME(f.parent_object_id) +
		case 
		when OBJECT_NAME(f.parent_object_id) = 'cms_categories' and COL_NAME(fc.parent_object_id, fc.parent_column_id) = 'parentCategoryID' then ' where isActive = 1' 
		else '' end
	FROM sys.foreign_keys AS f
	INNER JOIN sys.foreign_key_columns AS fc ON f.OBJECT_ID = fc.constraint_object_id
	and OBJECT_NAME (f.referenced_object_id) = 'cms_categories'
	and COL_NAME(fc.referenced_object_id,fc.referenced_column_id) = 'categoryID'
set @catInUse = 1
set @dynSQL = '
	if exists (
		select categoryID from (' + @innerUnion + ') as tmp 
		where categoryID is not null
		and categoryID = ' + cast(@categoryID as varchar(10)) + '
	) set @catInUse = 1
	else 
		set @catInUse = 0
	'
exec sp_executesql @dynSQL, N'@catInUse bit output', @catInUse output

IF @catInUse = 0 BEGIN
	UPDATE cms_categories
	set isActive = 0
	where categoryID = @categoryID
		IF @@ERROR <> 0 GOTO on_error
END

IF @@TRANCOUNT > 0 COMMIT TRAN

-- normal exit
RETURN cast(@catInUse as int)

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

