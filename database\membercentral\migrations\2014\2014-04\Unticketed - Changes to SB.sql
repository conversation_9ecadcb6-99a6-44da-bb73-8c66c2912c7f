use platformQueue
GO

ALTER TABLE dbo.tblQueueLog_processMemberGroups ALTER COLUMN totalID int NULL;
GO

-- Creation of the table to hold SB logs
IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sb_ServiceBrokerLogs]') AND type in (N'U'))
DROP TABLE [dbo].[sb_ServiceBrokerLogs]
GO
CREATE TABLE sb_ServiceBrokerLogs (
	LogID			BIGINT		IDENTITY(1,1)	NOT NULL,
	LogDate			DATETIME					NOT NULL DEFAULT (GETDATE()),
	SPID			INT							NOT NULL DEFAULT (@@SPID),
	LogTreeID		UNIQUEIDENTIFIER			NOT NULL,
	RunningProc		SYSNAME						NULL,
	ErrorSeverity	INT							NOT NULL DEFAULT (0),
	ErrorMessage	NVARCHAR(MAX)				NULL,
	ErrorLine		INT							NULL,
	ErrorProc		SYSNAME						NULL,
	totalMS			int							NOT NULL DEFAULT (0),
	QueueMessage	XML							NULL,
	PRIMARY KEY NONCLUSTERED (LogID)
);
CREATE CLUSTERED INDEX IX_sb_ServiceBrokerLogs ON sb_ServiceBrokerLogs (LogDate ASC) WITH FILLFACTOR=100;
GO

ALTER PROC [dbo].[sb_ProcessMemberGroupsQueueActivated]
AS

DECLARE @dlgID uniqueidentifier
DECLARE @mt nvarchar(256)
DECLARE @msg xml
DECLARE @itemGroupUID uniqueidentifier;
DECLARE @starttime datetime

WHILE 1 = 1
BEGIN

	WAITFOR (
		RECEIVE TOP(1) 
			@dlgID = conversation_handle,
			@mt = message_type_name,
			@msg = cast(message_body as xml)
		FROM dbo.ProcessMemberGroupsQueue
		), TIMEOUT 1000;

	IF @@ROWCOUNT = 0
		BREAK;

	IF @mt = N'PlatformQueue/ProcessMemberGroupsRequestMessage'
	BEGIN
		-- Log
		set @starttime = getdate()
		INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, QueueMessage)
		VALUES (@dlgID, OBJECT_NAME(@@PROCID), 'Start Process Message', @msg);

		BEGIN TRY
			SELECT @itemGroupUID = @msg.value('(/itemGroupUID)[1]','uniqueidentifier')
			IF @itemGroupUID is not null
				EXEC dbo.queue_processMemberGroups_process @itemGroupUID=@itemGroupUID, @logTreeID=@dlgID

			END CONVERSATION @dlgID;
		END TRY
		BEGIN CATCH
			-- Log
			INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorSeverity, ErrorMessage, ErrorLine, ErrorProc)
			VALUES (@dlgID, OBJECT_NAME(@@PROCID), ERROR_SEVERITY(), ERROR_MESSAGE(), ERROR_LINE(), ERROR_PROCEDURE());

			-- if deadlock, put itemGroupUID back into the queue as ready to run again
			IF @itemGroupUID is not null and 
				(
				ERROR_MESSAGE() like '%Errno 1205:%'
				OR 
				ERROR_MESSAGE() like '%Violation of UNIQUE KEY constraint ''IX_cache_members_groups''.%'
				) 
				EXEC dbo.queue_processMemberGroups_requeue @itemGroupUID=@itemGroupUID, @logTreeID=@dlgID
			ELSE 
			BEGIN
				BEGIN TRY
					DECLARE @smtpserver varchar(20), @tier varchar(20), @errorSubject varchar(100), @errmsg nvarchar(2048), @crlf varchar(10)
					SET @crlf = char(13) + char(10)
					SET @tier = 'PRODUCTION'					
					SET @smtpserver = '10.36.18.90'
					IF @@SERVERNAME = 'DEV04\PLATFORM2008' BEGIN
						SET @tier = 'DEVELOPMENT'
						SET @smtpserver = 'mail.trialsmith.com'
					END
					IF @@SERVERNAME = 'DEV03\PLATFORM2008' BEGIN
						SET @tier = 'BETA'
						SET @smtpserver = 'mail.trialsmith.com'
					END
					IF @@SERVERNAME = 'STAGING01\PLATFORM2008' BEGIN
						SET @tier = 'STAGING'
						SET @smtpserver = 'mail.trialsmith.com'
					END
					SET @errorSubject = @tier + ': Error Processing Member Groups'

					SET @errmsg = @errorSubject + @crlf + @crlf + 
									ERROR_MESSAGE() + @crlf + @crlf + 
									'LogTreeID=' + cast(@dlgID as varchar(60)) + @crlf + @crlf + 
									'After addressing error, run: ' + @crlf + 
									'EXEC platformQueue.dbo.queue_processMemberGroups_requeue @itemGroupUID=''' + cast(@itemGroupUID as varchar(60)) + ''', @logTreeID=''' + cast(@dlgID as varchar(60)) + ''';'

					EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
						@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
						@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
				END TRY
				BEGIN CATCH
					-- do nothing
				END CATCH
			END

			END CONVERSATION @dlgID;
		END CATCH;

		-- log
		INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
		VALUES (@dlgID, OBJECT_NAME(@@PROCID), 'End Process Message', Datediff(ms,@starttime,getdate()));
	END
	ELSE
		IF @mt = N'http://schemas.microsoft.com/SQL/ServiceBroker/EndDialog' OR @mt = N'http://schemas.microsoft.com/SQL/ServiceBroker/Error'
			END CONVERSATION @dlgID;
		ELSE
		BEGIN
			--anything other than user type or EndDialog is an unexpected error
			-- Log
			INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
			VALUES (@dlgID, OBJECT_NAME(@@PROCID), 'Unexpected message type received - ' + @mt);

			END CONVERSATION @dlgID;
		END

END;
GO

ALTER PROC [dbo].[queue_processMemberGroups_requeue]
@itemGroupUID uniqueidentifier,
@logTreeID uniqueidentifier

AS

DECLARE @starttime datetime
set @starttime = getdate()

-- Log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start Requeue of ' + cast(@itemGroupUID as varchar(60)));

declare @newstatuspopCondCache int, @newstatuspopMemGroups int, @queueType varchar(20)
select @newstatuspopCondCache = qs.queueStatusID 
	from dbo.tblQueueStatuses as qs WITH(NOLOCK)
	inner join dbo.tblQueueTypes as qt WITH(NOLOCK) on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popCondCache'
	and qs.queueStatus = 'readyToProcess'
select @newstatuspopMemGroups = qs.queueStatusID 
	from dbo.tblQueueStatuses as qs WITH(NOLOCK)
	inner join dbo.tblQueueTypes as qt WITH(NOLOCK) on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popMemGroups'
	and qs.queueStatus = 'readyToProcess'
select top 1 @queueType = qt.queueType
	from dbo.tblQueueItems as qi
	inner join dbo.tblQueueItems_processMemberGroups as qid on qid.itemUID = qi.itemUID
	INNER JOIN dbo.tblQueueStatuses qs on qs.queueStatusID = qi.queueStatusID
	INNER JOIN dbo.tblQueueTypes AS qt ON qt.queueTypeID = qs.queueTypeID
	where qid.itemGroupUID = @itemGroupUID

IF @queueType = 'popCondCache'
	update qi WITH(UPDLOCK, HOLDLOCK)
	set qi.queueStatusID = @newstatuspopCondCache,
		qi.dateUpdated = getdate(),
		qi.jobUID = null,
		qi.jobDateStarted = null,
		qi.jobServerID = null
	from dbo.tblQueueItems as qi
	inner join dbo.tblQueueItems_processMemberGroups as qid on qid.itemUID = qi.itemUID
	WHERE qid.itemGroupUID = @itemGroupUID

IF @queueType = 'popMemGroups'
	update qi WITH(UPDLOCK, HOLDLOCK)
	set qi.queueStatusID = @newstatuspopMemGroups,
		qi.dateUpdated = getdate(),
		qi.jobUID = null,
		qi.jobDateStarted = null,
		qi.jobServerID = null
	from dbo.tblQueueItems as qi
	inner join dbo.tblQueueItems_processMemberGroups as qid on qid.itemUID = qi.itemUID
	WHERE qid.itemGroupUID = @itemGroupUID

-- put back into service broker
declare @msgXML xml
set @msgXML = '<itemGroupUID>' + cast(@itemGroupUID as varchar(60)) + '</itemGroupUID>'
EXEC dbo.sb_ProcessMemberGroupsSendMessage @msgXML

-- Log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End Requeue of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime,getdate()));

RETURN 0
GO


IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[queue_processMemberGroups_checks]') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].[queue_processMemberGroups_checks]
GO
CREATE PROC [dbo].[queue_processMemberGroups_checks]
AS

/* ******************************************************************************** */
/* Queue Checks. Look for stuck items and requeue them								*/
/* items grabbedForProcessing/processingItem with JobDateStarted older than 1 hour	*/
/* ******************************************************************************** */
DECLARE @tblStuck TABLE (autoid int IDENTITY(1,1) PRIMARY KEY, itemGroupUID uniqueidentifier)
declare @minID int, @itemGroupUID_requeue uniqueidentifier

INSERT INTO @tblStuck (itemGroupUID)
select distinct qid.itemGroupUID
from dbo.tblQueueItems as qi WITH (NOLOCK)
INNER JOIN dbo.tblQueueStatuses qs WITH (NOLOCK) on qs.queueStatusID = qi.queueStatusID
inner join dbo.tblQueueItems_processMemberGroups as qid WITH (NOLOCK) on qid.itemUID = qi.itemUID
INNER JOIN dbo.tblQueueTypes AS qt WITH (NOLOCK) ON qt.queueTypeID = qs.queueTypeID
WHERE qt.queueType in ('popCondCache','popMemGroups')
AND qs.queueStatus in ('grabbedForProcessing','processingItem')
AND qi.JobDateStarted < DATEADD(hh, -1, GETDATE())
 
IF @@rowcount > 0 BEGIN
	-- Log
	DECLARE @logTreeID uniqueidentifier
	SET @logTreeID = NEWID()
	INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
	VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Found itemGroupUIDs to requeue');

	select @minID = min(autoid) from @tblStuck
	while @minID is not null BEGIN
		select @itemGroupUID_requeue = itemGroupUID from @tblStuck where autoID = @minID
		EXEC dbo.queue_processMemberGroups_requeue @itemGroupUID=@itemGroupUID_requeue, @logTreeID=@logTreeID
		select @minID = min(autoid) from @tblStuck where autoID > @minID
	END
END
GO


ALTER PROC [dbo].[queue_processMemberGroups_process]
@itemGroupUID uniqueidentifier,
@logTreeID uniqueidentifier

AS

IF @itemGroupUID is null
	RETURN -1

declare @starttime datetime, @totalMS int
set @starttime = getdate()

-- Log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start Process of ' + cast(@itemGroupUID as varchar(60)));

BEGIN TRY
	/* process the queues */
	EXEC dbo.job_popCondCache_process @itemGroupUID=@itemGroupUID, @logTreeID=@logTreeID
	EXEC dbo.job_popMemGrp_process @itemGroupUID=@itemGroupUID, @logTreeID=@logTreeID

	-- Log
	set @totalMS = Datediff(ms,@starttime,getdate())
	insert into dbo.tblQueueLog_processMemberGroups ([procedure], logDate, totalMS, itemGroupUID)
	values (OBJECT_NAME(@@PROCID), @starttime, @totalMS, @itemGroupUID)

	INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
	VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End Process of ' + cast(@itemGroupUID as varchar(60)), @totalMS);
END TRY
BEGIN CATCH
	-- Log
	set @totalMS = Datediff(ms,@starttime,getdate())
	insert into dbo.tblQueueLog_processMemberGroups ([procedure], logDate, totalMS, itemGroupUID)
	values (OBJECT_NAME(@@PROCID), @starttime, @totalMS, @itemGroupUID)

	INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
	VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End Process of ' + cast(@itemGroupUID as varchar(60)), @totalMS);

	-- bubble up
	EXEC membercentral.dbo.up_errorhandler
END CATCH

RETURN 0
GO

ALTER PROC [dbo].[job_popCondCache_process]
@itemGroupUID uniqueidentifier,
@logTreeID uniqueidentifier

AS

SET NOCOUNT ON

IF @itemGroupUID is null
	RETURN -1

declare @starttime datetime, @starttime2 datetime, @totalMS int, @totalID int
declare @jobUID uniqueidentifier, @minitemUID uniqueidentifier, @minOrgID int, 
	@minMemberID int, @itemIDList VARCHAR(max), @nowDate datetime
set @starttime = getdate()
set @jobUID = NEWID()
set @nowDate = getdate()

-- Log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start Process of ' + cast(@itemGroupUID as varchar(60)));

declare @statusReady int, @statusGrabbed int, @statusProcessing int, @statusDone int, @statusReadyPopMemGroups int 
select @statusReady = qs.queueStatusID 
	from platformQueue.dbo.tblQueueStatuses as qs WITH(NOLOCK)
	inner join platformQueue.dbo.tblQueueTypes as qt WITH(NOLOCK) on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popCondCache'
	and qs.queueStatus = 'readyToProcess'
select @statusGrabbed = qs.queueStatusID 
	from platformQueue.dbo.tblQueueStatuses as qs WITH(NOLOCK)
	inner join platformQueue.dbo.tblQueueTypes as qt WITH(NOLOCK) on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popCondCache'
	and qs.queueStatus = 'grabbedForProcessing'
select @statusProcessing = qs.queueStatusID 
	from platformQueue.dbo.tblQueueStatuses as qs WITH(NOLOCK)
	inner join platformQueue.dbo.tblQueueTypes as qt WITH(NOLOCK) on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popCondCache'
	and qs.queueStatus = 'processingItem'
select @statusDone = qs.queueStatusID 
	from platformQueue.dbo.tblQueueStatuses as qs WITH(NOLOCK)
	inner join platformQueue.dbo.tblQueueTypes as qt WITH(NOLOCK) on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popCondCache'
	and qs.queueStatus = 'done'
select @statusReadyPopMemGroups = qs.queueStatusID 
	from platformQueue.dbo.tblQueueStatuses as qs WITH(NOLOCK)
	inner join platformQueue.dbo.tblQueueTypes as qt WITH(NOLOCK) on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popMemGroups'
	and qs.queueStatus = 'readyToProcess'

IF OBJECT_ID('tempdb..#tmpItems') IS NOT NULL 
	DROP TABLE #tmpItems
CREATE TABLE #tmpItems (itemUID uniqueidentifier, orgID int, memberID int NULL, conditionID int NULL)

IF OBJECT_ID('tempdb..#tmpItemSpecific') IS NOT NULL 
	DROP TABLE #tmpItemSpecific
CREATE TABLE #tmpItemSpecific (itemID int)

IF OBJECT_ID('tempdb..#tmpMCNextCache') IS NOT NULL 
	DROP TABLE #tmpMCNextCache
CREATE TABLE #tmpMCNextCache (memberID int)


-- Log
set @starttime2 = getdate()
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start shortcut and finer items of ' + cast(@itemGroupUID as varchar(60)));

-- add shortcuts to queue for easier processing: if org/member has more than 20 conditions, just do org/member
insert into platformQueue.dbo.tblQueueItems_processMemberGroups (itemUID, itemGroupUID, orgID, memberID)
	OUTPUT inserted.itemUID, inserted.dateAdded, @statusReady 
	INTO platformQueue.dbo.tblQueueItems(itemUID, dateAdded, queueStatusID)
select NEWID() as itemUID, @itemGroupUID as itemGroupUID, qid.orgID, qid.memberID
FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
INNER JOIN platformQueue.dbo.tblQueueItems_processMemberGroups as qid WITH(NOLOCK) ON qid.itemUID = qi.itemUID
WHERE qi.queueStatusID = @statusReady
and qid.itemGroupUID = @itemGroupUID
and qid.memberid is not null
and qid.conditionid is not null 
group by orgID, memberID
having count(distinct qid.conditionID) > 20

-- add shortcuts to queue for easier processing: if org/condition has more than 20 members, just do org/condition
insert into platformQueue.dbo.tblQueueItems_processMemberGroups (itemUID, itemGroupUID, orgID, conditionID)
	OUTPUT inserted.itemUID, inserted.dateAdded, @statusReady 
	INTO platformQueue.dbo.tblQueueItems(itemUID, dateAdded, queueStatusID)
select NEWID() as itemUID, @itemGroupUID as itemGroupUID, qid.orgID, qid.conditionID
FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
INNER JOIN platformQueue.dbo.tblQueueItems_processMemberGroups as qid WITH(NOLOCK) ON qid.itemUID = qi.itemUID
WHERE qi.queueStatusID = @statusReady
and qid.itemGroupUID = @itemGroupUID
and qid.memberid is not null
and qid.conditionid is not null 
group by orgID, conditionID
having count(distinct qid.memberID) > 20

-- dequeue items ready for processing
UPDATE qi WITH(UPDLOCK, READPAST)
SET qi.queueStatusID = @statusGrabbed,
	qi.dateUpdated = @nowDate,
	qi.jobUID = @jobUID,
	qi.jobDateStarted = @nowDate,
	qi.jobServerID = 0
	OUTPUT inserted.itemUID, qid.orgID, qid.memberID, qid.conditionID
	INTO #tmpItems
FROM platformQueue.dbo.tblQueueItems as qi
INNER JOIN platformQueue.dbo.tblQueueItems_processMemberGroups as qid ON qid.itemUID = qi.itemUID
WHERE qi.queueStatusID = @statusReady
and qid.itemGroupUID = @itemGroupUID

-- Also grab finer items not assigned to this ItemGroupUID so we don't process them multiple times.
-- if orgID only, grab all others with same org
UPDATE qi WITH(UPDLOCK, READPAST)
SET qi.queueStatusID = @statusGrabbed,
	qi.dateUpdated = @nowDate,
	qi.jobUID = @jobUID,
	qi.jobDateStarted = @nowDate,
	qi.jobServerID = 0
	OUTPUT inserted.itemUID, qid.orgID, qid.memberID, qid.conditionID
	INTO #tmpItems
FROM platformQueue.dbo.tblQueueItems as qi
INNER JOIN platformQueue.dbo.tblQueueItems_processMemberGroups as qid ON qid.itemUID = qi.itemUID
INNER JOIN #tmpItems as tmp on tmp.orgID = qid.orgID
WHERE qi.queueStatusID = @statusReady
and qid.itemGroupUID <> @itemGroupUID
and tmp.memberID is null
and tmp.conditionID is null

-- if memberID only, grab all others with same memberID
UPDATE qi WITH(UPDLOCK, READPAST)
SET qi.queueStatusID = @statusGrabbed,
	qi.dateUpdated = @nowDate,
	qi.jobUID = @jobUID,
	qi.jobDateStarted = @nowDate,
	qi.jobServerID = 0
	OUTPUT inserted.itemUID, qid.orgID, qid.memberID, qid.conditionID
	INTO #tmpItems
FROM platformQueue.dbo.tblQueueItems as qi
INNER JOIN platformQueue.dbo.tblQueueItems_processMemberGroups as qid ON qid.itemUID = qi.itemUID
INNER JOIN #tmpItems as tmp on tmp.orgID = qid.orgID and tmp.memberID = qid.memberID
WHERE qi.queueStatusID = @statusReady
and qid.itemGroupUID <> @itemGroupUID
and tmp.memberID is not null
and tmp.conditionID is null

-- if conditionID only, grab all others with same conditionID
UPDATE qi WITH(UPDLOCK, READPAST)
SET qi.queueStatusID = @statusGrabbed,
	qi.dateUpdated = @nowDate,
	qi.jobUID = @jobUID,
	qi.jobDateStarted = @nowDate,
	qi.jobServerID = 0
	OUTPUT inserted.itemUID, qid.orgID, qid.memberID, qid.conditionID
	INTO #tmpItems
FROM platformQueue.dbo.tblQueueItems as qi
INNER JOIN platformQueue.dbo.tblQueueItems_processMemberGroups as qid ON qid.itemUID = qi.itemUID
INNER JOIN #tmpItems as tmp on tmp.orgID = qid.orgID and tmp.conditionID = qid.conditionID
WHERE qi.queueStatusID = @statusReady
and qid.itemGroupUID <> @itemGroupUID
and tmp.memberID is null
and tmp.conditionID is not null

-- if we grabbed others, change their ItemGroupUID to match
update qid WITH(UPDLOCK, HOLDLOCK)
set qid.itemGroupUID = @itemGroupUID
from #tmpItems as tmp
INNER JOIN platformQueue.dbo.tblQueueItems_processMemberGroups as qid ON qid.itemUID = tmp.itemUID
where qid.itemGroupUID <> @itemGroupUID

-- log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End shortcut and finer items of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));


-- process ORGID only records : if entry for orgid and nothing else, process these and delete any finer items.
IF EXISTS (
	SELECT top 1 tmp.itemUID 
	FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
	INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
	WHERE tmp.memberID is null 
	AND tmp.conditionID is null		
	AND qi.queueStatusID = @statusGrabbed)
BEGIN
	select @minOrgID = null
	SELECT @minOrgID = min(tmp.orgID)
		FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
		INNER JOIN platformQueue.dbo.tblQueueItems_processMemberGroups as qid WITH(NOLOCK) on qid.itemUID = qi.itemUID
		INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
		WHERE tmp.memberID is null 
		AND tmp.conditionID is null		
		AND qi.queueStatusID = @statusGrabbed
	while @minOrgID is not null BEGIN

		-- mark all rows having the same org as processing
		update qi WITH(UPDLOCK, HOLDLOCK)
		set qi.queueStatusID = @statusProcessing,
			qi.dateUpdated = getdate()
		FROM platformQueue.dbo.tblQueueItems as qi
		INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
		where tmp.orgID = @minOrgID
		and qi.queueStatusID = @statusGrabbed

		insert into #tmpMCNextCache (memberID) 
		exec membercentral.dbo.cache_members_populateMemberConditionCache @orgID=@minOrgID, @conditionIDList=null, @memberIDList=null, 
				@processImmediateOnly=1, @itemGroupUID=@itemGroupUID, @logTreeID=@logTreeID

		insert into platformQueue.dbo.tblQueueItems_processMemberGroups (itemUID, itemGroupUID, orgID, memberID)
			OUTPUT inserted.itemUID, inserted.dateAdded, @statusReadyPopMemGroups 
			INTO platformQueue.dbo.tblQueueItems(itemUID, dateAdded, queueStatusID)
		select NEWID() as itemUID, @itemGroupUID as itemGroupUID, @minOrgID as orgID, tmp.memberID
		from #tmpMCNextCache as tmp

		delete from #tmpMCNextCache

		-- mark all rows having the same org as done
		update qi WITH(UPDLOCK, HOLDLOCK)
		set qi.queueStatusID = @statusDone,
			qi.dateUpdated = getdate()
		FROM platformQueue.dbo.tblQueueItems as qi
		INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
		where tmp.orgID = @minOrgID
		and qi.queueStatusID = @statusProcessing

		SELECT @minOrgID = min(tmp.orgID) 
			FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
			INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
			WHERE tmp.memberID is null 
			AND tmp.conditionID is null		
			AND qi.queueStatusID = @statusGrabbed
			AND tmp.orgID > @minOrgID
	END
END

-- process MEMBERID only records : if entry for orgid and memberID, process these and delete any finer items.
IF EXISTS (
	SELECT top 1 tmp.itemUID 
	FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
	INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
	WHERE tmp.memberID is not null 
	AND tmp.conditionID is null		
	AND qi.queueStatusID = @statusGrabbed)
BEGIN
	select @minOrgID = null
	SELECT @minOrgID = min(tmp.orgID)
		FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
		INNER JOIN platformQueue.dbo.tblQueueItems_processMemberGroups as qid WITH(NOLOCK) on qid.itemUID = qi.itemUID
		INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
		WHERE tmp.memberID is not null 
		AND tmp.conditionID is null		
		AND qi.queueStatusID = @statusGrabbed
	while @minOrgID is not null BEGIN

		-- get members for org
		insert into #tmpItemSpecific (itemID)
		select distinct memberid
		FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
		INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
		where tmp.orgID = @minOrgID
		and tmp.memberid is not null
		and tmp.conditionid is null
		AND qi.queueStatusID = @statusGrabbed

		-- mark all rows having the same org/member as processing
		update qi WITH(UPDLOCK, HOLDLOCK)
		set qi.queueStatusID = @statusProcessing,
			qi.dateUpdated = getdate()
		FROM platformQueue.dbo.tblQueueItems as qi
		INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
		INNER JOIN #tmpItemSpecific as tmpM on tmpM.itemID = tmp.memberID
		where tmp.orgID = @minOrgID
		and qi.queueStatusID = @statusGrabbed

		SET @itemIDList = null
		SELECT @itemIDList = COALESCE(@itemIDList + ',', '') + cast(itemID as varchar(10)) from #tmpItemSpecific
		insert into #tmpMCNextCache (memberID) 
		exec membercentral.dbo.cache_members_populateMemberConditionCache @orgID=@minOrgID, @conditionIDList=null, @memberIDList=@itemIDList, 
				@processImmediateOnly=1, @itemGroupUID=@itemGroupUID, @logTreeID=@logTreeID

		insert into platformQueue.dbo.tblQueueItems_processMemberGroups (itemUID, itemGroupUID, orgID, memberID)
			OUTPUT inserted.itemUID, inserted.dateAdded, @statusReadyPopMemGroups 
			INTO platformQueue.dbo.tblQueueItems(itemUID, dateAdded, queueStatusID)
		select NEWID() as itemUID, @itemGroupUID as itemGroupUID, @minOrgID as orgID, tmp.memberID
		from #tmpMCNextCache as tmp

		delete from #tmpMCNextCache

		-- mark all rows having the same org/member as done
		update qi WITH(UPDLOCK, HOLDLOCK)
		set qi.queueStatusID = @statusDone,
			qi.dateUpdated = getdate()
		FROM platformQueue.dbo.tblQueueItems as qi
		INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
		INNER JOIN #tmpItemSpecific as tmpM on tmpM.itemID = tmp.memberID
		where tmp.orgID = @minOrgID
		and qi.queueStatusID = @statusProcessing

		delete from #tmpItemSpecific

		SELECT @minOrgID = min(tmp.orgID) 
			FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
			INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
			WHERE tmp.memberID is not null 
			AND tmp.conditionID is null		
			AND qi.queueStatusID = @statusGrabbed
			AND tmp.orgID > @minOrgID
	END
END

-- process CONDITIONID only records : if entry for orgid and conditionID, process these and delete any finer items.
IF EXISTS (
	SELECT top 1 tmp.itemUID 
	FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
	INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
	WHERE tmp.memberID is null 
	AND tmp.conditionID is not null		
	AND qi.queueStatusID = @statusGrabbed)
BEGIN
	select @minOrgID = null
	SELECT @minOrgID = min(tmp.orgID)
		FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
		INNER JOIN platformQueue.dbo.tblQueueItems_processMemberGroups as qid WITH(NOLOCK) on qid.itemUID = qi.itemUID
		INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
		WHERE tmp.memberID is null 
		AND tmp.conditionID is not null		
		AND qi.queueStatusID = @statusGrabbed
	while @minOrgID is not null BEGIN

		-- get conditions for org
		insert into #tmpItemSpecific (itemID)
		select distinct conditionID
		FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
		INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
		where tmp.orgID = @minOrgID
		and tmp.memberid is null
		and tmp.conditionid is not null
		AND qi.queueStatusID = @statusGrabbed

		-- mark all rows having the same org/condition as processing
		update qi WITH(UPDLOCK, HOLDLOCK)
		set qi.queueStatusID = @statusProcessing,
			qi.dateUpdated = getdate()
		FROM platformQueue.dbo.tblQueueItems as qi
		INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
		INNER JOIN #tmpItemSpecific as tmpC on tmpC.itemID = tmp.conditionID
		where tmp.orgID = @minOrgID
		and qi.queueStatusID = @statusGrabbed

		SET @itemIDList = null
		SELECT @itemIDList = COALESCE(@itemIDList + ',', '') + cast(itemID as varchar(10)) from #tmpItemSpecific
		insert into #tmpMCNextCache (memberID) 
		exec membercentral.dbo.cache_members_populateMemberConditionCache @orgID=@minOrgID, @conditionIDList=@itemIDList, @memberIDList=null, 
				@processImmediateOnly=1, @itemGroupUID=@itemGroupUID, @logTreeID=@logTreeID

		insert into platformQueue.dbo.tblQueueItems_processMemberGroups (itemUID, itemGroupUID, orgID, memberID)
			OUTPUT inserted.itemUID, inserted.dateAdded, @statusReadyPopMemGroups 
			INTO platformQueue.dbo.tblQueueItems(itemUID, dateAdded, queueStatusID)
		select NEWID() as itemUID, @itemGroupUID as itemGroupUID, @minOrgID as orgID, tmp.memberID
		from #tmpMCNextCache as tmp

		delete from #tmpMCNextCache

		-- mark all rows having the same org/member as done
		update qi WITH(UPDLOCK, HOLDLOCK)
		set qi.queueStatusID = @statusDone,
			qi.dateUpdated = getdate()
		FROM platformQueue.dbo.tblQueueItems as qi
		INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
		INNER JOIN #tmpItemSpecific as tmpC on tmpC.itemID = tmp.conditionID
		where tmp.orgID = @minOrgID
		and qi.queueStatusID = @statusProcessing

		delete from #tmpItemSpecific

		SELECT @minOrgID = min(tmp.orgID) 
			FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
			INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
			WHERE tmp.memberID is null 
			AND tmp.conditionID is not null		
			AND qi.queueStatusID = @statusGrabbed
			AND tmp.orgID > @minOrgID
	END
END

-- process remaining MEMBERID/CONDITIONID records by member
select @minMemberID = null, @minOrgID = null
select @minMemberID = min(tmp.memberID)
	FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
	INNER JOIN platformQueue.dbo.tblQueueItems_processMemberGroups as qid WITH(NOLOCK) on qid.itemUID = qi.itemUID
	INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
	WHERE tmp.memberID is not null 
	AND tmp.conditionID is not null
	AND qi.queueStatusID = @statusGrabbed
while @minMemberID is not null BEGIN
	select top 1 @minOrgID = orgID from #tmpItems where memberID = @minMemberID

	-- get conditions for members
	insert into #tmpItemSpecific (itemID)
	select distinct conditionID
	FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
	INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
	where tmp.memberID = @minMemberID
	and tmp.conditionid is not null
	AND qi.queueStatusID = @statusGrabbed

	-- mark all rows having the same member/condition as processing
	update qi WITH(UPDLOCK, HOLDLOCK)
	set qi.queueStatusID = @statusProcessing,
		qi.dateUpdated = getdate()
	FROM platformQueue.dbo.tblQueueItems as qi
	INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
	INNER JOIN #tmpItemSpecific as tmpC on tmpC.itemID = tmp.conditionID
	where tmp.memberID = @minMemberID
	and qi.queueStatusID = @statusGrabbed

	SET @itemIDList = null
	SELECT @itemIDList = COALESCE(@itemIDList + ',', '') + cast(itemID as varchar(10)) from #tmpItemSpecific
	insert into #tmpMCNextCache (memberID) 
	exec membercentral.dbo.cache_members_populateMemberConditionCache @orgID=@minOrgID, @conditionIDList=@itemIDList, @memberIDList=@minMemberID, 
			@processImmediateOnly=1, @itemGroupUID=@itemGroupUID, @logTreeID=@logTreeID

	insert into platformQueue.dbo.tblQueueItems_processMemberGroups (itemUID, itemGroupUID, orgID, memberID)
		OUTPUT inserted.itemUID, inserted.dateAdded, @statusReadyPopMemGroups 
		INTO platformQueue.dbo.tblQueueItems(itemUID, dateAdded, queueStatusID)
	select NEWID() as itemUID, @itemGroupUID as itemGroupUID, @minOrgID as orgID, tmp.memberID
	from #tmpMCNextCache as tmp

	delete from #tmpMCNextCache

	-- mark all rows having the same org/member as done
	update qi WITH(UPDLOCK, HOLDLOCK)
	set qi.queueStatusID = @statusDone,
		qi.dateUpdated = getdate()
	FROM platformQueue.dbo.tblQueueItems as qi
	INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
	INNER JOIN #tmpItemSpecific as tmpC on tmpC.itemID = tmp.conditionID
	where tmp.memberID = @minMemberID
	and qi.queueStatusID = @statusProcessing

	delete from #tmpItemSpecific

	select @minMemberID = min(tmp.memberID)
		FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
		INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
		WHERE tmp.memberID is not null 
		AND tmp.conditionID is not null
		AND qi.queueStatusID = @statusGrabbed
		AND tmp.memberID > @minMemberID
END

-- clear all entries marked as done
DELETE from platformQueue.dbo.tblQueueItems
where queueStatusID = @statusDone

select @totalID = count(*) from #tmpItems

-- cleanup tables
IF OBJECT_ID('tempdb..#tmpItemSpecific') IS NOT NULL 
	DROP TABLE #tmpItemSpecific
IF OBJECT_ID('tempdb..#tmpItems') IS NOT NULL 
	DROP TABLE #tmpItems
IF OBJECT_ID('tempdb..#tmpMCNextCache') IS NOT NULL 
	DROP TABLE #tmpMCNextCache

-- Log
set @totalMS = Datediff(ms,@starttime,getdate())
insert into dbo.tblQueueLog_processMemberGroups ([procedure], logDate, totalMS, totalID, itemGroupUID)
values (OBJECT_NAME(@@PROCID), @starttime, @totalMS, @totalID, @itemGroupUID)

INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End Process of ' + cast(@itemGroupUID as varchar(60)), @totalMS);

RETURN 0
GO

use membercentral
GO

ALTER PROC [dbo].[cache_members_populateMemberConditionCache]
@orgID int,
@conditionIDList varchar(max) = null,
@memberIDList varchar(max) = null,
@processImmediateOnly bit = 1,
@itemGroupUID uniqueidentifier,
@logTreeID uniqueidentifier

as

set nocount on

IF @itemGroupUID is null
	RETURN -1

declare @starttime datetime, @starttime2 datetime, @sql varchar(max), @totalMS int, 
	@totalID int, @totalRemoved int, @totalAdded int
select @starttime = getdate()

set @conditionIDList = isNull(@conditionIDList,'')
set @memberIDList = isNull(@memberIDList,'')

-- Log
INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start Process of orgID=' + cast(@orgID as varchar(10)) + ' conditionID=' + left(@conditionIDList,100) + ' memberID=' + left(@memberIDList,100) + ' of ' + cast(@itemGroupUID as varchar(60)));

IF OBJECT_ID('tempdb..#cache_members_conditions_shouldbe') IS NOT NULL
	DROP TABLE #cache_members_conditions_shouldbe
IF OBJECT_ID('tempdb..#tblCond') IS NOT NULL
	DROP TABLE #tblCond
IF OBJECT_ID('tempdb..#tblCondALL') IS NOT NULL
	DROP TABLE #tblCondALL
IF OBJECT_ID('tempdb..#tblCondALLFinal') IS NOT NULL
	DROP TABLE #tblCondALLFinal
IF OBJECT_ID('tempdb..#tblFCSplit') IS NOT NULL
	DROP TABLE #tblFCSplit
IF OBJECT_ID('tempdb..#tblSubSplit') IS NOT NULL
	DROP TABLE #tblSubSplit
IF OBJECT_ID('tempdb..#tblAccSplit') IS NOT NULL
	DROP TABLE #tblAccSplit
IF OBJECT_ID('tempdb..#tblAccSplitGL') IS NOT NULL
	DROP TABLE #tblAccSplitGL
IF OBJECT_ID('tempdb..#tblRecSplit') IS NOT NULL
	DROP TABLE #tblRecSplit
IF OBJECT_ID('tempdb..#tblRecSplitRoles') IS NOT NULL
	DROP TABLE #tblRecSplitRoles
IF OBJECT_ID('tempdb..#tblCondAccNotExist') IS NOT NULL
	DROP TABLE #tblCondAccNotExist
IF OBJECT_ID('tempdb..#tblCondValues') IS NOT NULL
	DROP TABLE #tblCondValues
IF OBJECT_ID('tempdb..#tblMembers') IS NOT NULL
	DROP TABLE #tblMembers
IF OBJECT_ID('tempdb..#tblMHSplit') IS NOT NULL
	DROP TABLE #tblMHSplit
IF OBJECT_ID('tempdb..#tblMHSubCategoriesSplit') IS NOT NULL
	DROP TABLE #tblMHSubCategoriesSplit

CREATE TABLE #cache_members_conditions_shouldbe (memberid int, conditionID int);
CREATE TABLE #tblCond (conditionID int PRIMARY KEY);
CREATE TABLE #tblFCSplit (fieldcode varchar(50), v1 varchar(30), v2 varchar(30), v3 varchar(30), v4 varchar(30), v5 varchar(30), v6 varchar(30));
CREATE TABLE #tblSubSplit (conditionID int, subStartDateLower datetime, subStartDateUpper datetime, subEndDateLower datetime, subEndDateUpper datetime, subGraceDateLower datetime, subGraceDateUpper datetime);
CREATE TABLE #tblAccSplit (conditionID int, revenueGLs varchar(max), batchDateLower datetime, batchDateUpper datetime, revOrCash varchar(7), conditionValue money, conditionValueLower money, conditionValueUpper money);
CREATE TABLE #tblAccSplitGL (conditionID int, revenueGL int);
CREATE TABLE #tblRecSplit (conditionID int, recordTypeID int, roles varchar(max));
CREATE TABLE #tblRecSplitRoles (conditionID int, [role] int);
CREATE TABLE #tblCondALLFinal (conditionID int PRIMARY KEY);
CREATE TABLE #tblCondValues (conditionID int, conditionKeyID int, conditionValueString varchar(max), conditionValueInteger int, conditionValueBit bit, conditionValueDecimal2 decimal(9,2), conditionValueDate datetime);
CREATE TABLE #tblMembers (memberID int PRIMARY KEY);

CREATE TABLE #tblMHSplit (
	conditionID int,
	historyCategory int,
	historyDateLower datetime,
	historyDateUpper datetime,
	historyEnteredDateLower datetime,
	historyEnteredDateUpper datetime,
	historyQuantityLower int,
	historyQuantityUpper int,
	historyAmountLower money,
	historyAmountUpper money,
	historyDescriptionContains varchar(max));

CREATE TABLE #tblMHSubCategoriesSplit (
	conditionID int,
	historyCategory int,
	historySubCategory int
)

-- split conditions to calculate
-- HONOR THE @processImmediateOnly parameter
IF len(@conditionIDList) > 0
	INSERT INTO #tblCond (conditionID)
	select distinct c.conditionID
	from dbo.fn_intListToTable(@conditionIDList,',') as tmp
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tmp.listitem
	inner join dbo.ams_virtualGroupConditionTypes as ct on ct.conditionTypeID = c.conditionTypeID
	where c.orgID = @orgID
	and 1 = case 
		when @processImmediateOnly = 1 and ct.processImmediately = 0 then 0
		else 1 end
ELSE
	INSERT INTO #tblCond (conditionID)
	select c.conditionID
	from dbo.ams_virtualGroupConditions as c
	inner join dbo.ams_virtualGroupConditionTypes as ct on ct.conditionTypeID = c.conditionTypeID
	where c.orgID = @orgID
	and 1 = case 
		when @processImmediateOnly = 1 and ct.processImmediately = 0 then 0
		else 1 end

-- get all conditions to calculate
select c.conditionID, e.expression, c.fieldCode, fieldCodeArea = case
	when left(c.fieldCode,2) = 'm_' then 'Member Data'	
	when left(c.fieldCode,3) = 'md_' then 'Custom Fields'	
	when left(c.fieldCode,3) = 'ma_' then 'Addresses'	
	when left(c.fieldCode,3) = 'mp_' then 'Phones'	
	when left(c.fieldCode,4) = 'mad_' then 'Districting'	
	when left(c.fieldCode,3) = 'me_' then 'Emails'	
	when left(c.fieldCode,3) = 'mw_' then 'Websites'	
	when left(c.fieldCode,2) = 'e_' then 'Events'	
	when left(c.fieldcode,4) = 'mpl_' then 'Professional Licenses'	
	when left(c.fieldcode,4) = 'grp_' then 'Groups'	-- excluded in where clause
	when left(c.fieldcode,4) = 'sub_' then 'Subscriptions'	
	when left(c.fieldcode,3) = 'rt_' then 'Record Types'	
	when left(c.fieldcode,5) = 'acct_' then 'Accounting'	
	when left(c.fieldcode,3) = 'mh_' then 'Member History'	
	end, dit.displayTypeCode, dat.dataTypeCode,
	fieldCodeAreaID = case 
	when left(c.fieldCode,3) = 'md_' then cast(replace(c.fieldcode,'md_','') as int)
	when left(c.fieldcode,4) = 'mpl_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,3) = 'ma_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,2) = 'e_' then cast(replace(c.fieldcode,'e_','') as int)
	when left(c.fieldCode,3) = 'me_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,3) = 'mw_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,3) = 'mp_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as int)
	when left(c.fieldCode,4) = 'mad_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as int)
	else null end,
	fieldCodeAreaPartA = case
	when left(c.fieldcode,4) = 'mpl_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as varchar(20))
	when left(c.fieldCode,3) = 'ma_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as varchar(20))
	when left(c.fieldCode,3) = 'mp_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as varchar(10))
	when left(c.fieldCode,4) = 'mad_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as varchar(10))
	else null end
into #tblCondALL
from dbo.ams_virtualGroupConditions as c
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.expressionID
inner join dbo.ams_memberDataColumnDataTypes as dat on dat.dataTypeID = c.dataTypeID
inner join dbo.ams_memberDataColumnDisplayTypes as dit on dit.displayTypeID = c.displayTypeID
inner join #tblCond as tblC on tblc.conditionID = c.conditionID
where c.orgID = @orgID
and c.isDefined = 1
and left(c.fieldCode,4) <> 'grp_'

-- add indexes for speed
ALTER TABLE #tblCondALL ADD PRIMARY KEY(conditionID);
CREATE INDEX IX_tblCondALL_areaid ON #tblCondALL (fieldCodeAreaID asc);
CREATE INDEX IX_tblCondALL_areaparta ON #tblCondALL (fieldCodeAreaPartA asc);
CREATE NONCLUSTERED INDEX IX_tblCondALL_DTA2 ON #tblCondALL (fieldCodeArea ASC, dataTypeCode ASC, expression ASC);

-- for the final processing at the end
insert into #tblCondALLFinal (conditionid)
select conditionID from #tblCondALL

-- this is used in subscriptions calculations
insert into #tblFCSplit (fieldcode, v1, v2, v3, v4, v5, v6)
select distinct fieldCode, [1], [2], [3], [4], [5], [6]
from #tblCondALL as tblc  
cross apply dbo.fn_varcharListToTable(tblc.fieldCode,'_') as vclist
PIVOT (min(listItem) FOR autoID in ([1],[2],[3],[4],[5],[6])) as pvt
where fieldCodeArea = 'Subscriptions'

CREATE INDEX IX_tblFCSplit_fc ON #tblFCSplit (fieldcode asc);
CREATE INDEX IX_tblFCSplit_v1 ON #tblFCSplit (v1 asc);
CREATE INDEX IX_tblFCSplit_v2 ON #tblFCSplit (v2 asc);
CREATE INDEX IX_tblFCSplit_v3 ON #tblFCSplit (v3 asc);
CREATE INDEX IX_tblFCSplit_v4 ON #tblFCSplit (v4 asc);
CREATE INDEX IX_tblFCSplit_v5 ON #tblFCSplit (v5 asc);
CREATE INDEX IX_tblFCSplit_v6 ON #tblFCSplit (v6 asc);

insert into #tblSubSplit
select distinct tblc.conditionID, 
	cast(nullif(subStartDateLower.val,'') as datetime) as subStartDateLower, 
	DATEADD(d,1,DATEADD(ms,-3,cast(nullif(subStartDateUpper.val,'') as datetime))) as subStartDateUpper,
	cast(nullif(subEndDateLower.val,'') as datetime) as subEndDateLower, 
	DATEADD(d,1,DATEADD(ms,-3,cast(nullif(subEndDateUpper.val,'') as datetime))) as subEndDateUpper,
	cast(nullif(subGraceDateLower.val,'') as datetime) as subGraceDateLower, 
	DATEADD(d,1,DATEADD(ms,-3,cast(nullif(subGraceDateUpper.val,'') as datetime))) as subGraceDateUpper
from #tblCondALL as tblc
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subStartDateLower'
	WHERE cv.conditionID = tblc.conditionID
) as subStartDateLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subStartDateUpper'
	WHERE cv.conditionID = tblc.conditionID
) as subStartDateUpper(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subEndDateLower'
	WHERE cv.conditionID = tblc.conditionID
) as subEndDateLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subEndDateUpper'
	WHERE cv.conditionID = tblc.conditionID
) as subEndDateUpper(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subGraceDateLower'
	WHERE cv.conditionID = tblc.conditionID
) as subGraceDateLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subGraceDateUpper'
	WHERE cv.conditionID = tblc.conditionID
) as subGraceDateUpper(val)
where fieldCodeArea = 'Subscriptions'


-- this is used in accounting calculations
insert into #tblAccSplit
select distinct tblc.conditionID, LEFT(gllist.list, LEN(gllist.list)-1) as revenueGLs, 
	cast(batchDateLower.val as datetime) as batchDateLower, 
	DATEADD(d,1,DATEADD(ms,-3,cast(batchDateUpper.val as datetime))) as batchDateUpper,
	linkAllocType.val as revOrCash, 
	cast(value.val as money) as conditionValue, 
	cast(valueLower.val as money) as conditionValueLower, 
	cast(valueUpper.val as money) as conditionValueUpper 
from #tblCondALL as tblc
CROSS APPLY ( 
	SELECT cv.conditionValue + ',' AS [text()] 
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'revenueGL'
	WHERE cv.conditionID = tblc.conditionID
	FOR XML PATH('')
) as gllist(list) 
CROSS APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'batchDateLower'
	WHERE cv.conditionID = tblc.conditionID
) as batchDateLower(val)
CROSS APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'batchDateUpper'
	WHERE cv.conditionID = tblc.conditionID
) as batchDateUpper(val)
CROSS APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'linkAllocType'
	WHERE cv.conditionID = tblc.conditionID
) as linkAllocType(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'value'
	WHERE cv.conditionID = tblc.conditionID
) as value(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'valueLower'
	WHERE cv.conditionID = tblc.conditionID
) as valueLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'valueUpper'
	WHERE cv.conditionID = tblc.conditionID
) as valueUpper(val)
where fieldCodeArea = 'Accounting'

CREATE INDEX IX_tblACCSplit_conditionID ON #tblAccSplit (conditionID asc);

insert into #tblAccSplitGL
select conditionID, revGL.listItem as revenueGL
from #tblAccSplit
cross apply dbo.fn_intListToTable(revenueGLs,',') as revGL


-- this is used in recordtype calculations
insert into #tblRecSplit
select distinct tblc.conditionID, cast(recordType.val as int) as recordTypeID, LEFT(rolelist.list, LEN(rolelist.list)-1) as roles 
from #tblCondALL as tblc
CROSS APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'recordType'
	WHERE cv.conditionID = tblc.conditionID
) as recordType(val)
INNER JOIN dbo.ams_recordTypes as rt on rt.recordTypeID = cast(recordType.val as int)
OUTER APPLY ( 
	SELECT cv.conditionValue + ', ' AS [text()] 
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'role'
	WHERE cv.conditionID = tblc.conditionID
	FOR XML PATH('')
) as rolelist(list) 
where fieldCodeArea = 'Record Types'

CREATE INDEX IX_tblRecSplit_conditionID ON #tblRecSplit (conditionID asc);

insert into #tblRecSplitRoles
select conditionID, recRole.listItem as [role]
from #tblRecSplit
cross apply dbo.fn_intListToTable(roles,',') as recRole

-- this is used in Member history calculations

insert into #tblMHSubCategoriesSplit (conditionID,historyCategory,historySubCategory)
select 
	tblc.conditionID,
	cast(historyCategory.val as int),
	cast(historySubCategory.val as int)
from #tblCondALL as tblc
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyCategory'
	WHERE cv.conditionID = tblc.conditionID
) as historyCategory(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historySubCategory'
	WHERE cv.conditionID = tblc.conditionID
) as historySubCategory(val)
where fieldCodeArea = 'Member History'
and nullif(historySubCategory.val,'') is not null


CREATE INDEX IX_tblMHSubCategoriesSplit_conditionID ON #tblMHSubCategoriesSplit (conditionID asc);
CREATE INDEX IX_tblMHSubCategoriesSplit_historyCategory ON #tblMHSubCategoriesSplit (historyCategory asc);
CREATE INDEX IX_tblMHSubCategoriesSplit_historySubCategory ON #tblMHSubCategoriesSplit (historySubCategory asc);

insert into #tblMHSplit (conditionID ,historyCategory ,historyDateLower ,historyDateUpper ,historyEnteredDateLower ,historyEnteredDateUpper ,historyQuantityLower ,historyQuantityUpper ,historyAmountLower ,historyAmountUpper ,historyDescriptionContains)
select 
	tblc.conditionID,
	historyCategory.val,
	cast(nullif(historyDateLower.val,'') as datetime),
	cast(nullif(historyDateUpper.val,'') as datetime),
	cast(nullif(historyEnteredDateLower.val,'') as datetime),
	cast(nullif(historyEnteredDateUpper.val,'') as datetime),
	cast(nullif(historyQuantityLower.val,'') as int),
	cast(nullif(historyQuantityUpper.val,'') as int),
	cast(nullif(historyAmountLower.val,'') as money),
	cast(nullif(historyAmountUpper.val,'') as money),
	nullif(historyDescriptionContains.val,'')
from #tblCondALL as tblc
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyCategory'
	WHERE cv.conditionID = tblc.conditionID
) as historyCategory(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyDateLower'
	WHERE cv.conditionID = tblc.conditionID
) as historyDateLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyDateUpper'
	WHERE cv.conditionID = tblc.conditionID
) as historyDateUpper(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyEnteredDateLower'
	WHERE cv.conditionID = tblc.conditionID
) as historyEnteredDateLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyEnteredDateUpper'
	WHERE cv.conditionID = tblc.conditionID
) as historyEnteredDateUpper(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyQuantityLower'
	WHERE cv.conditionID = tblc.conditionID
) as historyQuantityLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyQuantityUpper'
	WHERE cv.conditionID = tblc.conditionID
) as historyQuantityUpper(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyAmountLower'
	WHERE cv.conditionID = tblc.conditionID
) as historyAmountLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyAmountUpper'
	WHERE cv.conditionID = tblc.conditionID
) as historyAmountUpper(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyDescriptionContains'
	WHERE cv.conditionID = tblc.conditionID
) as historyDescriptionContains(val)
where fieldCodeArea = 'Member History'


CREATE INDEX IX_tblMHSplit_conditionID ON #tblMHSubCategoriesSplit (conditionID asc);
CREATE INDEX IX_tblMHSplit_historyCategory ON #tblMHSubCategoriesSplit (historyCategory asc);



-- put condition values into temp table by datatype to remove all casting in below queries
insert into #tblCondValues (conditionID, conditionKeyID, conditionValueString, conditionValueInteger, conditionValueBit, conditionValueDecimal2, conditionValueDate)
select cv.conditionID, cv.conditionKeyID, null, cv.conditionValue, null, null, null
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where 
(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'STRING' and tblc.displayTypeCode in ('RADIO','SELECT')) or
(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'DECIMAL2' and tblc.displayTypeCode in ('RADIO','SELECT')) or 
(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'DATE' and tblc.displayTypeCode in ('RADIO','SELECT')) or 
(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq','lt','lte','gt','gte') and tblc.dataTypeCode = 'INTEGER') or
(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('datepart','datediff')) or
(tblc.fieldCodeArea = 'Addresses' and tblc.expression in ('eq','neq') and tblc.displayTypeCode in ('RADIO','SELECT')) or
(tblc.fieldCodeArea = 'Professional Licenses' and tblc.expression in ('datepart','datediff')) or
(tblc.fieldCodeArea = 'Member Data' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'INTEGER') or
(tblc.fieldCodeArea = 'Districting' and tblc.expression in ('eq','neq'))
	union all
select cv.conditionID, cv.conditionKeyID, cv.conditionValue, null, null, null, null
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where
(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'STRING' and tblc.displayTypeCode not in ('RADIO','SELECT')) or
(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('lt','lte','gt','gte','contains','contains_regex') and tblc.dataTypeCode = 'STRING') or
(tblc.fieldCodeArea = 'Addresses' and tblc.expression in ('eq','neq') and tblc.displayTypeCode not in ('RADIO','SELECT')) or
(tblc.fieldCodeArea = 'Addresses' and tblc.expression in ('lt','lte','gt','gte','contains','contains_regex')) or
(tblc.fieldCodeArea = 'Professional Licenses' and tblc.expression in ('eq','neq','lt','lte','gt','gte','contains','contains_regex') and tblc.dataTypeCode = 'STRING') or
(tblc.fieldCodeArea = 'Member Data' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'STRING') or
(tblc.fieldCodeArea = 'Member Data' and tblc.expression in ('lt','lte','gt','gte','contains','contains_regex')) or
(tblc.fieldCodeArea = 'Websites') or
(tblc.fieldCodeArea = 'Emails') or
(tblc.fieldCodeArea = 'Phones')
	union all
select cv.conditionID, cv.conditionKeyID, null, null, cv.conditionValue, null, null
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where (tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'BIT')
	union all
select cv.conditionID, cv.conditionKeyID, null, null, null, cv.conditionValue, null
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where
(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'DECIMAL2' and tblc.displayTypeCode not in ('RADIO','SELECT')) or
(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('lt','lte','gt','gte') and tblc.dataTypeCode = 'DECIMAL2')
	union all
select cv.conditionID, cv.conditionKeyID, null, null, null, null, cv.conditionValue
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where
(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'DATE' and tblc.displayTypeCode not in ('RADIO','SELECT')) or
(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('lt','lte','gt','gte') and tblc.dataTypeCode = 'DATE') or
(tblc.fieldCodeArea = 'Professional Licenses' and tblc.expression in ('eq','neq','lt','lte','gt','gte') and tblc.dataTypeCode = 'DATE')

CREATE INDEX IX_tblCondValues_conditionID ON #tblCondValues (conditionID asc);


-- split members to calculate
IF len(@memberIDList) > 0
	INSERT INTO #tblMembers (memberID)
	select distinct m.memberID
	from dbo.fn_intListToTable(@memberIDList,',') as tmp
	inner join dbo.ams_members as m on m.memberID = tmp.listitem
	where m.orgID = @orgID
	and m.status <> 'D'
ELSE
	INSERT INTO #tblMembers (memberID)
	select m.memberID
	from dbo.ams_members as m
	where m.orgID = @orgID
	and m.status <> 'D'


/* These have been arranged in order of their popularity for faster processing. */

/* ******************** */
/* md_xxxx, eq, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.valueID = cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'STRING'
	and tblc.displayTypeCode in ('RADIO','SELECT')

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue = cv.conditionValueString
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'STRING'
	and tblc.displayTypeCode not in ('RADIO','SELECT')

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'eq' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* sub_xxxx, subscribed  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Subscriptions' and expression='subscribed') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblFCSplit as fcsplit on fcsplit.fieldCode = tblc.fieldCode
	inner join #tblSubSplit as subsplit on subsplit.conditionID = tblc.conditionID
	inner join dbo.sub_subscriptions as s on fcsplit.v2 in (0,s.typeID)
		and fcsplit.v3 in (0,s.subscriptionID)
	inner join dbo.sub_rateFrequencies as rf on fcsplit.v4 in (0,rf.rateID)
	inner join dbo.sub_subscribers as sub on rf.rfid = sub.RFID
		and s.subscriptionID = sub.subscriptionID
		and fcsplit.v5 in (0,sub.statusID)
		and fcsplit.v6 in (0,sub.paymentStatusID)
		and 1 = 
			case 
			when subsplit.subStartDateLower is null and subsplit.subStartDateUpper is null then 1
			when subsplit.subStartDateLower is not null and subsplit.subStartDateUpper is not null and sub.subStartDate between subsplit.subStartDateLower and subsplit.subStartDateUpper then 1 
			when subsplit.subStartDateLower is not null and subsplit.subStartDateUpper is null and sub.subStartDate >= subsplit.subStartDateLower then 1
			when subsplit.subStartDateUpper is not null and subsplit.subStartDateLower is null and sub.subStartDate <= subsplit.subStartDateUpper then 1
			else 0 end
		and 1 = 
			case 
			when subsplit.subEndDateLower is null and subsplit.subEndDateUpper is null then 1
			when subsplit.subEndDateLower is not null and subsplit.subEndDateUpper is not null and sub.subEndDate between subsplit.subEndDateLower and subsplit.subEndDateUpper then 1 
			when subsplit.subEndDateLower is not null and subsplit.subEndDateUpper is null and sub.subEndDate >= subsplit.subEndDateLower then 1
			when subsplit.subEndDateUpper is not null and subsplit.subEndDateLower is null and sub.subEndDate <= subsplit.subEndDateUpper then 1
			else 0 end
		and 1 = 
			case 
			when subsplit.subGraceDateLower is null and subsplit.subGraceDateUpper is null then 1
			when subsplit.subGraceDateLower is not null and subsplit.subGraceDateUpper is not null and sub.graceEndDate between subsplit.subGraceDateLower and subsplit.subGraceDateUpper then 1 
			when subsplit.subGraceDateLower is not null and subsplit.subGraceDateUpper is null and sub.graceEndDate >= subsplit.subGraceDateLower then 1
			when subsplit.subGraceDateUpper is not null and subsplit.subGraceDateLower is null and sub.graceEndDate <= subsplit.subGraceDateUpper then 1
			else 0 end
	inner join dbo.ams_members as m2 on m2.memberid = sub.memberid
	inner join #tblMembers as m on m.memberID = m2.activeMemberID
	where tblc.fieldCodeArea = 'Subscriptions' 
	and tblc.expression = 'subscribed' 

	delete from #tblCondALL 
	where fieldCodeArea = 'Subscriptions' and expression = 'subscribed'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, eq, bit  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='BIT') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__bit as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue = cv.conditionValueBit
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'BIT'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'eq' and dataTypeCode = 'BIT'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************ */
/* ma_xxxx, eq  */
/* ************ */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='eq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'eq' 
	and tblc.displayTypeCode not in ('RADIO','SELECT')
	and case tblc.fieldCodeAreaPartA
		when 'address1' then isnull(ma.address1,'') 
		when 'address2' then isnull(ma.address2,'') 
		when 'address3' then isnull(ma.address3,'') 
		when 'city' then isnull(ma.city,'') 
		when 'postalcode' then isnull(ma.postalcode,'') 
		when 'county' then isnull(ma.county,'') 
		else isnull(ma.attn,'') end = cv.conditionValueString

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'eq' 
	and tblc.displayTypeCode in ('RADIO','SELECT')
	and case tblc.fieldCodeAreaPartA
		when 'stateprov' then isnull(ma.stateid,0) 
		else isnull(ma.countryid,0) end = cv.conditionValueInteger

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'eq'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, eq, int  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.valueID = cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'INTEGER'
	and tblc.displayTypeCode in ('RADIO','SELECT')

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue = cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'INTEGER'
	and tblc.displayTypeCode not in ('RADIO','SELECT')

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'eq' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, eq, string  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='eq' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'STRING'
	and case tblc.fieldCodeAreaPartA
		when 'status' then isnull(mpls.statusName,'') 
		else isnull(mpl.licensenumber,'') end = cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'eq' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, datediff  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='datediff') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'eq'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,vw.columnValue,getdate())
			when 'm' then datediff(m,vw.columnValue,getdate())
			when 'wk' then datediff(wk,vw.columnValue,getdate())
			when 'dw' then datediff(dw,vw.columnValue,getdate())
			when 'd' then datediff(d,vw.columnValue,getdate())
			when 'dy' then datediff(dy,vw.columnValue,getdate())
			else datediff(yy,vw.columnValue,getdate()) end = abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'eq'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),vw.columnValue)
			when 'm' then datediff(m,getdate(),vw.columnValue)
			when 'wk' then datediff(wk,getdate(),vw.columnValue)
			when 'dw' then datediff(dw,getdate(),vw.columnValue)
			when 'd' then datediff(d,getdate(),vw.columnValue)
			when 'dy' then datediff(dy,getdate(),vw.columnValue)
			else datediff(yy,getdate(),vw.columnValue) end = abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger < 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'neq'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,vw.columnValue,getdate())
			when 'm' then datediff(m,vw.columnValue,getdate())
			when 'wk' then datediff(wk,vw.columnValue,getdate())
			when 'dw' then datediff(dw,vw.columnValue,getdate())
			when 'd' then datediff(d,vw.columnValue,getdate())
			when 'dy' then datediff(dy,vw.columnValue,getdate())
			else datediff(yy,vw.columnValue,getdate()) end <> abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'neq'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),vw.columnValue)
			when 'm' then datediff(m,getdate(),vw.columnValue)
			when 'wk' then datediff(wk,getdate(),vw.columnValue)
			when 'dw' then datediff(dw,getdate(),vw.columnValue)
			when 'd' then datediff(d,getdate(),vw.columnValue)
			when 'dy' then datediff(dy,getdate(),vw.columnValue)
			else datediff(yy,getdate(),vw.columnValue) end <> abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger < 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lt'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,vw.columnValue,getdate())
			when 'm' then datediff(m,vw.columnValue,getdate())
			when 'wk' then datediff(wk,vw.columnValue,getdate())
			when 'dw' then datediff(dw,vw.columnValue,getdate())
			when 'd' then datediff(d,vw.columnValue,getdate())
			when 'dy' then datediff(dy,vw.columnValue,getdate())
			else datediff(yy,vw.columnValue,getdate()) end < abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lt'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),vw.columnValue)
			when 'm' then datediff(m,getdate(),vw.columnValue)
			when 'wk' then datediff(wk,getdate(),vw.columnValue)
			when 'dw' then datediff(dw,getdate(),vw.columnValue)
			when 'd' then datediff(d,getdate(),vw.columnValue)
			when 'dy' then datediff(dy,getdate(),vw.columnValue)
			else datediff(yy,getdate(),vw.columnValue) end < abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger < 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lte'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,vw.columnValue,getdate())
			when 'm' then datediff(m,vw.columnValue,getdate())
			when 'wk' then datediff(wk,vw.columnValue,getdate())
			when 'dw' then datediff(dw,vw.columnValue,getdate())
			when 'd' then datediff(d,vw.columnValue,getdate())
			when 'dy' then datediff(dy,vw.columnValue,getdate())
			else datediff(yy,vw.columnValue,getdate()) end <= abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lte'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),vw.columnValue)
			when 'm' then datediff(m,getdate(),vw.columnValue)
			when 'wk' then datediff(wk,getdate(),vw.columnValue)
			when 'dw' then datediff(dw,getdate(),vw.columnValue)
			when 'd' then datediff(d,getdate(),vw.columnValue)
			when 'dy' then datediff(dy,getdate(),vw.columnValue)
			else datediff(yy,getdate(),vw.columnValue) end <= abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger < 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gt'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,vw.columnValue,getdate())
			when 'm' then datediff(m,vw.columnValue,getdate())
			when 'wk' then datediff(wk,vw.columnValue,getdate())
			when 'dw' then datediff(dw,vw.columnValue,getdate())
			when 'd' then datediff(d,vw.columnValue,getdate())
			when 'dy' then datediff(dy,vw.columnValue,getdate())
			else datediff(yy,vw.columnValue,getdate()) end > abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gt'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),vw.columnValue)
			when 'm' then datediff(m,getdate(),vw.columnValue)
			when 'wk' then datediff(wk,getdate(),vw.columnValue)
			when 'dw' then datediff(dw,getdate(),vw.columnValue)
			when 'd' then datediff(d,getdate(),vw.columnValue)
			when 'dy' then datediff(dy,getdate(),vw.columnValue)
			else datediff(yy,getdate(),vw.columnValue) end > abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger < 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gte'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,vw.columnValue,getdate())
			when 'm' then datediff(m,vw.columnValue,getdate())
			when 'wk' then datediff(wk,vw.columnValue,getdate())
			when 'dw' then datediff(dw,vw.columnValue,getdate())
			when 'd' then datediff(d,vw.columnValue,getdate())
			when 'dy' then datediff(dy,vw.columnValue,getdate())
			else datediff(yy,vw.columnValue,getdate()) end >= abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gte'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),vw.columnValue)
			when 'm' then datediff(m,getdate(),vw.columnValue)
			when 'wk' then datediff(wk,getdate(),vw.columnValue)
			when 'dw' then datediff(dw,getdate(),vw.columnValue)
			when 'd' then datediff(d,getdate(),vw.columnValue)
			when 'dy' then datediff(dy,getdate(),vw.columnValue)
			else datediff(yy,getdate(),vw.columnValue) end >= abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger < 0

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'datediff'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, exists, string  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='exists' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'exists' 
	and tblc.dataTypeCode = 'STRING'
	and case tblc.fieldCodeAreaPartA
		when 'status' then nullif(mpls.StatusName,'')
		else nullif(mpl.licensenumber,'') end is not null

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'exists' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, exists, bit  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='BIT') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.vw_memberData__bit as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue is not null
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'exists' 
	and tblc.dataTypeCode = 'BIT'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'exists' and dataTypeCode = 'BIT'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* e_xxxx, registered  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Events' and expression='registered') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ev_events as e on e.eventID = tblc.fieldCodeAreaID
	inner join dbo.ev_registration as er on er.eventID = e.eventID and er.status = 'A'
	inner join dbo.ev_registrants as reg on reg.registrationID = er.registrationID and reg.status = 'A'
	inner join dbo.ams_members as m2 on m2.memberid = reg.memberid
	inner join #tblMembers as m on m.memberID = m2.activeMemberID
	where tblc.fieldCodeArea = 'Events' 
	and tblc.expression = 'registered' 

	delete from #tblCondALL 
	where fieldCodeArea = 'Events' and expression = 'registered'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* md_xxxx, exists, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue is not null
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'exists' 
	and tblc.dataTypeCode = 'STRING'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'exists' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc


/* THE FOLLOWING ARE NOT ORDERED BY POPULARITY DUE TO LOW INSTANCES (UNDER 50) */


/* ****************** */
/* mpl_xxxx, datediff  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='datediff') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'eq'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,mpl.activeDate,getdate())
			when 'm' then datediff(m,mpl.activeDate,getdate())
			when 'wk' then datediff(wk,mpl.activeDate,getdate())
			when 'dw' then datediff(dw,mpl.activeDate,getdate())
			when 'd' then datediff(d,mpl.activeDate,getdate())
			when 'dy' then datediff(dy,mpl.activeDate,getdate())
			else datediff(yy,mpl.activeDate,getdate()) end = abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff'
	and cv.conditionValueInteger >= 0 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'eq'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),mpl.activeDate)
			when 'm' then datediff(m,getdate(),mpl.activeDate)
			when 'wk' then datediff(wk,getdate(),mpl.activeDate)
			when 'dw' then datediff(dw,getdate(),mpl.activeDate)
			when 'd' then datediff(d,getdate(),mpl.activeDate)
			when 'dy' then datediff(dy,getdate(),mpl.activeDate)
			else datediff(yy,getdate(),mpl.activeDate) end = abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger < 0 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'neq'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,mpl.activeDate,getdate())
			when 'm' then datediff(m,mpl.activeDate,getdate())
			when 'wk' then datediff(wk,mpl.activeDate,getdate())
			when 'dw' then datediff(dw,mpl.activeDate,getdate())
			when 'd' then datediff(d,mpl.activeDate,getdate())
			when 'dy' then datediff(dy,mpl.activeDate,getdate())
			else datediff(yy,mpl.activeDate,getdate()) end <> abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'neq'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),mpl.activeDate)
			when 'm' then datediff(m,getdate(),mpl.activeDate)
			when 'wk' then datediff(wk,getdate(),mpl.activeDate)
			when 'dw' then datediff(dw,getdate(),mpl.activeDate)
			when 'd' then datediff(d,getdate(),mpl.activeDate)
			when 'dy' then datediff(dy,getdate(),mpl.activeDate)
			else datediff(yy,getdate(),mpl.activeDate) end <> abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger < 0 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lt'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,mpl.activeDate,getdate())
			when 'm' then datediff(m,mpl.activeDate,getdate())
			when 'wk' then datediff(wk,mpl.activeDate,getdate())
			when 'dw' then datediff(dw,mpl.activeDate,getdate())
			when 'd' then datediff(d,mpl.activeDate,getdate())
			when 'dy' then datediff(dy,mpl.activeDate,getdate())
			else datediff(yy,mpl.activeDate,getdate()) end < abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses'
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lt'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),mpl.activeDate)
			when 'm' then datediff(m,getdate(),mpl.activeDate)
			when 'wk' then datediff(wk,getdate(),mpl.activeDate)
			when 'dw' then datediff(dw,getdate(),mpl.activeDate)
			when 'd' then datediff(d,getdate(),mpl.activeDate)
			when 'dy' then datediff(dy,getdate(),mpl.activeDate)
			else datediff(yy,getdate(),mpl.activeDate) end < abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses'
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger < 0 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lte'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,mpl.activeDate,getdate())
			when 'm' then datediff(m,mpl.activeDate,getdate())
			when 'wk' then datediff(wk,mpl.activeDate,getdate())
			when 'dw' then datediff(dw,mpl.activeDate,getdate())
			when 'd' then datediff(d,mpl.activeDate,getdate())
			when 'dy' then datediff(dy,mpl.activeDate,getdate())
			else datediff(yy,mpl.activeDate,getdate()) end <= abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lte'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),mpl.activeDate)
			when 'm' then datediff(m,getdate(),mpl.activeDate)
			when 'wk' then datediff(wk,getdate(),mpl.activeDate)
			when 'dw' then datediff(dw,getdate(),mpl.activeDate)
			when 'd' then datediff(d,getdate(),mpl.activeDate)
			when 'dy' then datediff(dy,getdate(),mpl.activeDate)
			else datediff(yy,getdate(),mpl.activeDate) end <= abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger < 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gt'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,mpl.activeDate,getdate())
			when 'm' then datediff(m,mpl.activeDate,getdate())
			when 'wk' then datediff(wk,mpl.activeDate,getdate())
			when 'dw' then datediff(dw,mpl.activeDate,getdate())
			when 'd' then datediff(d,mpl.activeDate,getdate())
			when 'dy' then datediff(dy,mpl.activeDate,getdate())
			else datediff(yy,mpl.activeDate,getdate()) end > abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gt'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),mpl.activeDate)
			when 'm' then datediff(m,getdate(),mpl.activeDate)
			when 'wk' then datediff(wk,getdate(),mpl.activeDate)
			when 'dw' then datediff(dw,getdate(),mpl.activeDate)
			when 'd' then datediff(d,getdate(),mpl.activeDate)
			when 'dy' then datediff(dy,getdate(),mpl.activeDate)
			else datediff(yy,getdate(),mpl.activeDate) end > abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger < 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gte'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,mpl.activeDate,getdate())
			when 'm' then datediff(m,mpl.activeDate,getdate())
			when 'wk' then datediff(wk,mpl.activeDate,getdate())
			when 'dw' then datediff(dw,mpl.activeDate,getdate())
			when 'd' then datediff(d,mpl.activeDate,getdate())
			when 'dy' then datediff(dy,mpl.activeDate,getdate())
			else datediff(yy,mpl.activeDate,getdate()) end >= abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gte'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),mpl.activeDate)
			when 'm' then datediff(m,getdate(),mpl.activeDate)
			when 'wk' then datediff(wk,getdate(),mpl.activeDate)
			when 'dw' then datediff(dw,getdate(),mpl.activeDate)
			when 'd' then datediff(d,getdate(),mpl.activeDate)
			when 'dy' then datediff(dy,getdate(),mpl.activeDate)
			else datediff(yy,getdate(),mpl.activeDate) end >= abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger < 0

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'datediff'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************* */
/* md_xxxx, neq, string  */
/* ********************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'STRING'
	and tblc.displayTypeCode in ('RADIO','SELECT')
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__string as vw WITH(NOEXPAND)
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
			and vw.valueID = cv.conditionValueInteger
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
	)	

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'STRING'
	and tblc.displayTypeCode not in ('RADIO','SELECT')
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__string as vw WITH(NOEXPAND)
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
			and vw.columnValue = cv.conditionValueString
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'neq' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* md_xxxx, not_exists, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'not_exists' 
	and tblc.dataTypeCode = 'STRING'
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__string as vw WITH(NOEXPAND)
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
		and vw.columnValue is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'not_exists' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************** */
/* md_xxxx, eq, decimal2  */
/* ********************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='DECIMAL2') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.valueID = cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'DECIMAL2'
	and tblc.displayTypeCode in ('RADIO','SELECT')

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue = cv.conditionValueDecimal2
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'DECIMAL2'
	and tblc.displayTypeCode not in ('RADIO','SELECT')

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'eq' and dataTypeCode = 'DECIMAL2'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, eq, date  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.valueID = cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'DATE'
	and tblc.displayTypeCode in ('RADIO','SELECT')

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue = cv.conditionValueDate
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'DATE'
	and tblc.displayTypeCode not in ('RADIO','SELECT')

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'eq' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, neq, bit  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='BIT') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'BIT'
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__bit as vw WITH(NOEXPAND)
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
			and vw.columnValue = cv.conditionValueBit
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'neq' and dataTypeCode = 'BIT'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, neq, int  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'INTEGER'
	and tblc.displayTypeCode in ('RADIO','SELECT')
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__integer as vw WITH(NOEXPAND)
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
			and vw.valueID = cv.conditionValueInteger
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
	)	

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberID = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'INTEGER'
	and tblc.displayTypeCode not in ('RADIO','SELECT')
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__integer as vw WITH(NOEXPAND)
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
			and vw.columnValue = cv.conditionValueInteger
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'neq' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********************** */
/* md_xxxx, neq, decimal2  */
/* *********************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='DECIMAL2') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'DECIMAL2'
	and tblc.displayTypeCode in ('RADIO','SELECT')
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND)
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
			and vw.valueID = cv.conditionValueInteger
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
	)	

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'DECIMAL2'
	and tblc.displayTypeCode not in ('RADIO','SELECT')
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND)
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
			and vw.columnValue = cv.conditionValueDecimal2
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'neq' and dataTypeCode = 'DECIMAL2'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* md_xxxx, neq, date  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'DATE'
	and tblc.displayTypeCode in ('RADIO','SELECT')
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__date as vw WITH(NOEXPAND)
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
			and vw.valueID = cv.conditionValueInteger
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
	)	

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'DATE'
	and tblc.displayTypeCode not in ('RADIO','SELECT')
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__date as vw WITH(NOEXPAND)
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
			and vw.columnValue = cv.conditionValueDate
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'neq' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* md_xxxx, lt, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue < cv.conditionValueString
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'lt' 
	and tblc.dataTypeCode = 'STRING'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'lt' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, lt, int  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue < cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'lt' 
	and tblc.dataTypeCode = 'INTEGER'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'lt' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************** */
/* md_xxxx, lt, decimal2  */
/* ********************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='DECIMAL2') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue < cv.conditionValueDecimal2
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'lt' 
	and tblc.dataTypeCode = 'DECIMAL2'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'lt' and dataTypeCode = 'DECIMAL2'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, lt, date  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue < cv.conditionValueDate
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'lt' 
	and tblc.dataTypeCode = 'DATE'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'lt' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* md_xxxx, lte, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue <= cv.conditionValueString
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'lte' 
	and tblc.dataTypeCode = 'STRING'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'lte' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, lte, int  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue <= cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'lte' 
	and tblc.dataTypeCode = 'INTEGER'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'lte' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************** */
/* md_xxxx, lte, decimal2  */
/* ********************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='DECIMAL2') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue <= cv.conditionValueDecimal2
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'lte' 
	and tblc.dataTypeCode = 'DECIMAL2'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'lte' and dataTypeCode = 'DECIMAL2'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, lte, date  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue <= cv.conditionValueDate
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'lte' 
	and tblc.dataTypeCode = 'DATE'                                                         

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'lte' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* md_xxxx, gt, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue > cv.conditionValueString
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'gt' 
	and tblc.dataTypeCode = 'STRING'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'gt' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, gt, int  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue > cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'gt' 
	and tblc.dataTypeCode = 'INTEGER'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'gt' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************** */
/* md_xxxx, gt, decimal2  */
/* ********************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='DECIMAL2') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue > cv.conditionValueDecimal2
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'gt' 
	and tblc.dataTypeCode = 'DECIMAL2'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'gt' and dataTypeCode = 'DECIMAL2'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, gt, date  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue > cv.conditionValueDate
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'gt' 
	and tblc.dataTypeCode = 'DATE'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'gt' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* md_xxxx, gte, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue >= cv.conditionValueString
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'gte' 
	and tblc.dataTypeCode = 'STRING'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'gte' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, gte, int  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue >= cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'gte' 
	and tblc.dataTypeCode = 'INTEGER'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'gte' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************** */
/* md_xxxx, gte, decimal2  */
/* ********************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='DECIMAL2') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue >= cv.conditionValueDecimal2
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'gte' 
	and tblc.dataTypeCode = 'DECIMAL2'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'gte' and dataTypeCode = 'DECIMAL2'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, gte, date  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue >= cv.conditionValueDate
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'gte' 
	and tblc.dataTypeCode = 'DATE'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'gte' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, exists, int  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue is not null
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'exists' 
	and tblc.dataTypeCode = 'INTEGER'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'exists' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************** */
/* md_xxxx, exists, decimal2  */
/* ********************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='DECIMAL2') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue is not null
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'exists' 
	and tblc.dataTypeCode = 'DECIMAL2'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'exists' and dataTypeCode = 'DECIMAL2'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, exists, date  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue is not null
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'exists' 
	and tblc.dataTypeCode = 'DATE'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'exists' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, exists, CONTENTOBJ  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='CONTENTOBJ') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct md.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = tblc.fieldCodeAreaID
	inner join dbo.ams_memberdataColumnValues as mdcv on mdcv.columnID = mdc.columnID
	inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID and mdcdt.dataTypeCode = 'CONTENTOBJ'
	inner join dbo.ams_memberData as md on md.valueID = mdcv.valueID
	inner join #tblMembers as m on m.memberID = md.memberID
	inner join dbo.cms_content as c on c.siteResourceID = mdcv.columnValueSiteResourceID
	inner join dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID and cl.languageID = 1
	inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'exists' 
	and tblc.dataTypeCode = 'CONTENTOBJ'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'exists' and dataTypeCode = 'CONTENTOBJ'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, exists, DOCUMENTOBJ  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='DOCUMENTOBJ') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct md.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = tblc.fieldCodeAreaID
	inner join dbo.ams_memberdataColumnValues as mdcv on mdcv.columnID = mdc.columnID
	inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID and mdcdt.dataTypeCode = 'DOCUMENTOBJ'
	inner join dbo.ams_memberData as md on md.valueID = mdcv.valueID
	inner join #tblMembers as m on m.memberID = md.memberID
	inner join dbo.cms_documents as d on d.siteResourceID = mdcv.columnValueSiteResourceID
	inner join dbo.cms_documentLanguages as dl on dl.documentID = d.documentID and dl.languageID = 1
	inner join dbo.cms_documentVersions as dv on dv.documentLanguageID = dl.documentLanguageID and dv.isActive = 1
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'exists' 
	and tblc.dataTypeCode = 'DOCUMENTOBJ'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'exists' and dataTypeCode = 'DOCUMENTOBJ'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, not_exists, int  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'not_exists' 
	and tblc.dataTypeCode = 'INTEGER'
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__integer as vw WITH(NOEXPAND)
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
		and vw.columnValue is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'not_exists' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************** */
/* md_xxxx, not_exists, decimal2  */
/* ********************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='DECIMAL2') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'not_exists' 
	and tblc.dataTypeCode = 'DECIMAL2'
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND)
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
		and vw.columnValue is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'not_exists' and dataTypeCode = 'DECIMAL2'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, not_exists, date  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'not_exists' 
	and tblc.dataTypeCode = 'DATE'
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__date as vw WITH(NOEXPAND)
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
		and vw.columnValue is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'not_exists' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, not_exists, bit  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='BIT') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'not_exists' 
	and tblc.dataTypeCode = 'BIT'
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__bit as vw WITH(NOEXPAND)
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
		and vw.columnValue is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'not_exists' and dataTypeCode = 'BIT'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, not_exists, CONTENTOBJ  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='CONTENTOBJ') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'not_exists' 
	and tblc.dataTypeCode = 'CONTENTOBJ'
	and not exists (
		select VWmd.memberID
		from dbo.ams_memberData as VWmd
		inner join dbo.ams_members as VWm on VWm.memberID = VWmd.memberID and VWm.memberID = m.memberid
		inner join dbo.ams_memberdataColumnValues as VWmdcv on VWmdcv.valueID = VWmd.valueID
		inner join dbo.ams_memberDataColumns as VWmdc on VWmdc.columnID = VWmdcv.columnID and VWmdc.columnID = tblc.fieldCodeAreaID
		where VWmdcv.columnValueSiteResourceID is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'not_exists' and dataTypeCode = 'CONTENTOBJ'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, not_exists, DOCUMENTOBJ  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='DOCUMENTOBJ') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'not_exists' 
	and tblc.dataTypeCode = 'DOCUMENTOBJ'
	and not exists (
		select VWmd.memberID
		from dbo.ams_memberData as VWmd
		inner join dbo.ams_members as VWm on VWm.memberID = VWmd.memberID and VWm.memberID = m.memberid
		inner join dbo.ams_memberdataColumnValues as VWmdcv on VWmdcv.valueID = VWmd.valueID
		inner join dbo.ams_memberDataColumns as VWmdc on VWmdc.columnID = VWmdcv.columnID and VWmdc.columnID = tblc.fieldCodeAreaID
		where VWmdcv.columnValueSiteResourceID is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'not_exists' and dataTypeCode = 'DOCUMENTOBJ'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* md_xxxx, contains, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='contains' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue like '%' + cv.conditionValueString + '%'
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'contains' 
	and tblc.dataTypeCode = 'STRING'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'contains' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* md_xxxx, contains_regex, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='contains_regex' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		-- add back when we have a regexfind fn
		-- and dbo.fn_RegExFind(vw.columnValue,tblc.value) = 1
		and vw.columnValue like '%' + cv.conditionValueString + '%'
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'contains_regex' 
	and tblc.dataTypeCode = 'STRING'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'contains_regex' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, datepart  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='datepart') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'eq'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,vw.columnValue) 
			when 'm' then datepart(m,vw.columnValue) 
			when 'wk' then datepart(wk,vw.columnValue)
			when 'dw' then datepart(dw,vw.columnValue)
			when 'd' then datepart(d,vw.columnValue) 
			when 'dy' then datepart(dy,vw.columnValue)
			else datepart(yy,vw.columnValue) end = cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'neq'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,vw.columnValue) 
			when 'm' then datepart(m,vw.columnValue) 
			when 'wk' then datepart(wk,vw.columnValue)
			when 'dw' then datepart(dw,vw.columnValue)
			when 'd' then datepart(d,vw.columnValue) 
			when 'dy' then datepart(dy,vw.columnValue)
			else datepart(yy,vw.columnValue) end <> cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lt'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,vw.columnValue) 
			when 'm' then datepart(m,vw.columnValue) 
			when 'wk' then datepart(wk,vw.columnValue)
			when 'dw' then datepart(dw,vw.columnValue)
			when 'd' then datepart(d,vw.columnValue) 
			when 'dy' then datepart(dy,vw.columnValue)
			else datepart(yy,vw.columnValue) end < cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lte'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,vw.columnValue) 
			when 'm' then datepart(m,vw.columnValue) 
			when 'wk' then datepart(wk,vw.columnValue)
			when 'dw' then datepart(dw,vw.columnValue)
			when 'd' then datepart(d,vw.columnValue) 
			when 'dy' then datepart(dy,vw.columnValue)
			else datepart(yy,vw.columnValue) end <= cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gt'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,vw.columnValue) 
			when 'm' then datepart(m,vw.columnValue) 
			when 'wk' then datepart(wk,vw.columnValue)
			when 'dw' then datepart(dw,vw.columnValue)
			when 'd' then datepart(d,vw.columnValue) 
			when 'dy' then datepart(dy,vw.columnValue)
			else datepart(yy,vw.columnValue) end > cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gte'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,vw.columnValue) 
			when 'm' then datepart(m,vw.columnValue) 
			when 'wk' then datepart(wk,vw.columnValue)
			when 'dw' then datepart(dw,vw.columnValue)
			when 'd' then datepart(d,vw.columnValue) 
			when 'dy' then datepart(dy,vw.columnValue)
			else datepart(yy,vw.columnValue) end >= cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datepart' 

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'datepart'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* m_xxxx, eq, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='eq' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
	inner join dbo.ams_members as m on m.memberID = tmpM.memberID
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'STRING'
	and case tblc.fieldCode
		when 'm_firstname' then isnull(m.firstname,'')
		when 'm_middlename' then isnull(m.middlename,'')
		when 'm_lastname' then isnull(m.lastname,'')
		when 'm_suffix' then isnull(m.suffix,'')
		when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
		when 'm_company' then isnull(m.company,'')
		when 'm_membernumber' then isnull(m.membernumber,'')
		else isnull(m.prefix,'') end = cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'eq' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* m_xxxx, eq, integer  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='eq' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
	inner join dbo.ams_members as m on m.memberID = tmpM.memberID
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'INTEGER'
	and isnull(m.memberTypeID,0) = cv.conditionValueInteger

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'eq' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* m_xxxx, neq, integer */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='neq' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
	inner join dbo.ams_members as m on m.memberID = tmpM.memberID
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'INTEGER'
	and isnull(m.memberTypeID,0) <> cv.conditionValueInteger

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'neq' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* m_xxxx, neq, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='neq' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
	inner join dbo.ams_members as m on m.memberID = tmpM.memberID
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'STRING'
	and case tblc.fieldCode
		when 'm_firstname' then isnull(m.firstname,'')
		when 'm_middlename' then isnull(m.middlename,'')
		when 'm_lastname' then isnull(m.lastname,'')
		when 'm_suffix' then isnull(m.suffix,'')
		when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
		when 'm_company' then isnull(m.company,'')
		when 'm_membernumber' then isnull(m.membernumber,'')
		else isnull(m.prefix,'') end <> cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'neq' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* m_xxxx, lt  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='lt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
	inner join dbo.ams_members as m on m.memberID = tmpM.memberID
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'lt' 
	and case tblc.fieldCode
		when 'm_firstname' then isnull(m.firstname,'')
		when 'm_middlename' then isnull(m.middlename,'')
		when 'm_lastname' then isnull(m.lastname,'')
		when 'm_suffix' then isnull(m.suffix,'')
		when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
		when 'm_company' then isnull(m.company,'')
		when 'm_membernumber' then isnull(m.membernumber,'')
		else isnull(m.prefix,'') end < cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'lt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* m_xxxx, lte  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='lte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
	inner join dbo.ams_members as m on m.memberID = tmpM.memberID
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'lte' 
	and case tblc.fieldCode
		when 'm_firstname' then isnull(m.firstname,'')
		when 'm_middlename' then isnull(m.middlename,'')
		when 'm_lastname' then isnull(m.lastname,'')
		when 'm_suffix' then isnull(m.suffix,'')
		when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
		when 'm_company' then isnull(m.company,'')
		when 'm_membernumber' then isnull(m.membernumber,'')
		else isnull(m.prefix,'') end <= cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'lte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* m_xxxx, gt  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='gt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
	inner join dbo.ams_members as m on m.memberID = tmpM.memberID
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'gt' 
	and case tblc.fieldCode
		when 'm_firstname' then isnull(m.firstname,'')
		when 'm_middlename' then isnull(m.middlename,'')
		when 'm_lastname' then isnull(m.lastname,'')
		when 'm_suffix' then isnull(m.suffix,'')
		when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
		when 'm_company' then isnull(m.company,'')
		when 'm_membernumber' then isnull(m.membernumber,'')
		else isnull(m.prefix,'') end > cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'gt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* m_xxxx, gte  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='gte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
	inner join dbo.ams_members as m on m.memberID = tmpM.memberID
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'gte' 
	and case tblc.fieldCode
		when 'm_firstname' then isnull(m.firstname,'')
		when 'm_middlename' then isnull(m.middlename,'')
		when 'm_lastname' then isnull(m.lastname,'')
		when 'm_suffix' then isnull(m.suffix,'')
		when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
		when 'm_company' then isnull(m.company,'')
		when 'm_membernumber' then isnull(m.membernumber,'')
		else isnull(m.prefix,'') end >= cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'gte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* m_xxxx, exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
	inner join dbo.ams_members as m on m.memberID = tmpM.memberID
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'exists' 
	and case tblc.fieldCode
		when 'm_firstname' then nullif(m.firstname,'')
		when 'm_middlename' then nullif(m.middlename,'')
		when 'm_lastname' then nullif(m.lastname,'')
		when 'm_suffix' then nullif(m.suffix,'')
		when 'm_professionalsuffix' then nullif(m.professionalsuffix,'')
		when 'm_company' then nullif(m.company,'')
		when 'm_membernumber' then nullif(m.membernumber,'')
		else nullif(m.prefix,'') end is not null

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* m_xxxx, not_exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='not_exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
	inner join dbo.ams_members as m on m.memberID = tmpM.memberID
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'not_exists' 
	and case tblc.fieldCode
		when 'm_firstname' then nullif(m.firstname,'')
		when 'm_middlename' then nullif(m.middlename,'')
		when 'm_lastname' then nullif(m.lastname,'')
		when 'm_suffix' then nullif(m.suffix,'')
		when 'm_professionalsuffix' then nullif(m.professionalsuffix,'')
		when 'm_company' then nullif(m.company,'')
		when 'm_membernumber' then nullif(m.membernumber,'')
		else nullif(m.prefix,'') end is null

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'not_exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* m_xxxx, contains  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='contains') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
	inner join dbo.ams_members as m on m.memberID = tmpM.memberID
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'contains' 
	and case tblc.fieldCode
		when 'm_firstname' then isnull(m.firstname,'')
		when 'm_middlename' then isnull(m.middlename,'')
		when 'm_lastname' then isnull(m.lastname,'')
		when 'm_suffix' then isnull(m.suffix,'')
		when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
		when 'm_company' then isnull(m.company,'')
		when 'm_membernumber' then isnull(m.membernumber,'')
		else isnull(m.prefix,'') end like '%' + cv.conditionValueString + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'contains'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* m_xxxx, contains_regex  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='contains_regex') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
	inner join dbo.ams_members as m on m.memberID = tmpM.memberID
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'contains_regex' 
	-- add back when we have a regexfind fn
	-- dbo.fn_RegExFind(vw.columnValue,tblc.value) = 1
	and case tblc.fieldCode
		when 'm_firstname' then isnull(m.firstname,'')
		when 'm_middlename' then isnull(m.middlename,'')
		when 'm_lastname' then isnull(m.lastname,'')
		when 'm_suffix' then isnull(m.suffix,'')
		when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
		when 'm_company' then isnull(m.company,'')
		when 'm_membernumber' then isnull(m.membernumber,'')
		else isnull(m.prefix,'') end like '%' + cv.conditionValueString + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'contains_regex'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, eq  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='eq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberWebsites as mw on mw.websiteTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = mw.memberID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'eq' 
	and isnull(mw.website,'') = cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'eq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, neq  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='neq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
	inner join dbo.ams_members as m on m.memberID = tmpM.memberID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'neq' 
	and not exists (
		select mw.memberID
		from dbo.ams_memberWebsites as mw
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
			and isnull(mw.website,'') = cv.conditionValueString
		where mw.websiteTypeID = tblc.fieldCodeAreaID
		and mw.memberID = m.memberid
	)	

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'neq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, lt  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='lt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberWebsites as mw on mw.websiteTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = mw.memberID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'lt' 
	and isnull(mw.website,'') < cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'lt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, lte  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='lte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberWebsites as mw on mw.websiteTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = mw.memberID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'lte' 
	and isnull(mw.website,'') <= cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'lte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, gt  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='gt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberWebsites as mw on mw.websiteTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = mw.memberID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'gt' 
	and isnull(mw.website,'') > cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'gt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, gte  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='gte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberWebsites as mw on mw.websiteTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = mw.memberID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'gte' 
	and isnull(mw.website,'') >= cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'gte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_memberWebsites as mw on mw.websiteTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = mw.memberID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'exists' 
	and nullif(mw.website,'') is not null

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, not_exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='not_exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberID = m.memberID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'not_exists' 
	and not exists (
		select mw.memberID
		from dbo.ams_memberWebsites as mw
		where mw.websiteTypeID = tblc.fieldCodeAreaID
		and mw.memberID = m.memberid
		and nullif(mw.website,'') is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'not_exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, contains  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='contains') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberWebsites as mw on mw.websiteTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = mw.memberID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'contains' 
	and isnull(mw.website,'') like '%' + cv.conditionValueString + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'contains'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, contains_regex  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='contains_regex') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberWebsites as mw on mw.websiteTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = mw.memberID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'contains_regex' 
	-- add back when we have a regexfind fn
	-- dbo.fn_RegExFind(mw.website,c.value) = 1
	and isnull(mw.website,'') like '%' + cv.conditionValueString + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'contains_regex'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, eq  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='eq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberEmails as me on me.emailTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = me.memberID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'eq' 
	and isnull(me.email,'') = cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'eq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, neq  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='neq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberID = m.memberID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'neq' 
	and not exists (
		select me.memberID
		from dbo.ams_memberEmails as me
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
			and isnull(me.email,'') = cv.conditionValueString
		where me.emailTypeID = tblc.fieldCodeAreaID
		and me.memberID = m.memberid
	)	

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'neq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, lt  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='lt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberEmails as me on me.emailTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = me.memberID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'lt' 
	and isnull(me.email,'') < cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'lt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, lte  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='lte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberEmails as me on me.emailTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = me.memberID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'lte' 
	and isnull(me.email,'') <= cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'lte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, gt  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='gt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberEmails as me on me.emailTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = me.memberID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'gt' 
	and isnull(me.email,'') > cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'gt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, gte  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='gte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberEmails as me on me.emailTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = me.memberID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'gte' 
	and isnull(me.email,'') >= cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'gte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_memberEmails as me on me.emailTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = me.memberID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'exists' 
	and nullif(me.email,'') is not null

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, not_exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='not_exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberID = m.memberID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'not_exists' 
	and not exists (
		select me.memberID
		from dbo.ams_memberEmails as me
		where me.emailTypeID = tblc.fieldCodeAreaID
		and me.memberID = m.memberid
		and nullif(me.email,'') is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'not_exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, contains  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='contains') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberEmails as me on me.emailTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = me.memberID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'contains' 
	and isnull(me.email,'') like '%' + cv.conditionValueString + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'contains'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, contains_regex  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='contains_regex') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberEmails as me on me.emailTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = me.memberID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'contains_regex' 
	-- add back when we have a regexfind fn
	-- dbo.fn_RegExFind(me.email,tblc.value) = 1
	and isnull(me.email,'') like '%' + cv.conditionValueString + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'contains_regex'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, eq  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='eq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID and mp.phoneTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'eq' 
	and isnull(mp.phone,'') = cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'eq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, neq  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='neq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberID = m.memberID
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'neq' 
	and not exists (
		select ma.memberid
		from dbo.ams_memberAddresses as ma 
		inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID
			and mp.phoneTypeID = tblc.fieldCodeAreaID
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
			and isnull(mp.phone,'') = cv.conditionValueString
		where ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaPartA
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'neq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, lt  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='lt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID and mp.phoneTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'lt' 
	and isnull(mp.phone,'') < cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'lt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, lte  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='lte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID and mp.phoneTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'lte' 
	and isnull(mp.phone,'') <= cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'lte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, gt  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='gt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID and mp.phoneTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'gt' 
	and isnull(mp.phone,'') > cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'gt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, gte  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='gte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID and mp.phoneTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'gte' 
	and isnull(mp.phone,'') >= cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'gte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID and mp.phoneTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'exists' 
	and nullif(mp.phone,'') is not null

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, not_exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='not_exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberID = m.memberID
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'not_exists' 
	and not exists (
		select ma.memberid
		from dbo.ams_memberAddresses as ma 
		inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID
			and mp.phoneTypeID = tblc.fieldCodeAreaID
		where ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaPartA
		and nullif(mp.phone,'') is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'not_exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, contains  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='contains') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID and mp.phoneTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'contains' 
	and isnull(mp.phone,'') like '%' + cv.conditionValueString + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'contains'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, contains_regex  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='contains_regex') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID and mp.phoneTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'contains_regex' 
	-- add back when we have a regexfind fn
	-- dbo.fn_RegExFind(mp.phone,tblc.value) = 1
	and isnull(mp.phone,'') like '%' + cv.conditionValueString + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'contains_regex'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************ */
/* ma_xxxx, neq  */
/* ************ */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='neq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberID = m.memberID
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'neq' 
	and tblc.displayTypeCode not in ('RADIO','SELECT')
	and not exists (
		select ma.memberid
		from dbo.ams_memberAddresses as ma
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
		where ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaID
		and tblc.displayTypeCode not in ('RADIO','SELECT')
		and case tblc.fieldCodeAreaPartA
			when 'address1' then isnull(ma.address1,'') 
			when 'address2' then isnull(ma.address2,'') 
			when 'address3' then isnull(ma.address3,'') 
			when 'city' then isnull(ma.city,'') 
			when 'postalcode' then isnull(ma.postalcode,'') 
			when 'county' then isnull(ma.county,'') 
			else isnull(ma.attn,'') end = cv.conditionValueString
	)

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberID = m.memberID
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'neq' 
	and tblc.displayTypeCode in ('RADIO','SELECT')
	and not exists (
		select ma.memberid
		from dbo.ams_memberAddresses as ma
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
		where ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaID
		and tblc.displayTypeCode in ('RADIO','SELECT')
		and case tblc.fieldCodeAreaPartA
			when 'stateprov' then isnull(ma.stateid,0) 
			else isnull(ma.countryid,0) end = cv.conditionValueInteger
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'neq'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************ */
/* ma_xxxx, lt  */
/* ************ */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='lt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'lt' 
	and case tblc.fieldCodeAreaPartA
		when 'address1' then isnull(ma.address1,'') 
		when 'address2' then isnull(ma.address2,'') 
		when 'address3' then isnull(ma.address3,'') 
		when 'city' then isnull(ma.city,'') 
		when 'postalcode' then isnull(ma.postalcode,'') 
		when 'county' then isnull(ma.county,'') 
		else isnull(ma.attn,'') end < cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'lt'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************ */
/* ma_xxxx, lte  */
/* ************ */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='lte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'lte' 
	and case tblc.fieldCodeAreaPartA
		when 'address1' then isnull(ma.address1,'') 
		when 'address2' then isnull(ma.address2,'') 
		when 'address3' then isnull(ma.address3,'') 
		when 'city' then isnull(ma.city,'') 
		when 'postalcode' then isnull(ma.postalcode,'') 
		when 'county' then isnull(ma.county,'') 
		else isnull(ma.attn,'') end <= cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'lte'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************ */
/* ma_xxxx, gt  */
/* ************ */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='gt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'gt' 
	and case tblc.fieldCodeAreaPartA
		when 'address1' then isnull(ma.address1,'') 
		when 'address2' then isnull(ma.address2,'') 
		when 'address3' then isnull(ma.address3,'') 
		when 'city' then isnull(ma.city,'') 
		when 'postalcode' then isnull(ma.postalcode,'') 
		when 'county' then isnull(ma.county,'') 
		else isnull(ma.attn,'') end > cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'gt'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************ */
/* ma_xxxx, gte  */
/* ************ */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='gte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'gte' 
	and case tblc.fieldCodeAreaPartA
		when 'address1' then isnull(ma.address1,'') 
		when 'address2' then isnull(ma.address2,'') 
		when 'address3' then isnull(ma.address3,'') 
		when 'city' then isnull(ma.city,'') 
		when 'postalcode' then isnull(ma.postalcode,'') 
		when 'county' then isnull(ma.county,'') 
		else isnull(ma.attn,'') end >= cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'gte'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************ */
/* ma_xxxx, exists  */
/* ************ */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'exists' 
	and tblc.displayTypeCode not in ('RADIO','SELECT')
	and case tblc.fieldCodeAreaPartA
		when 'address1' then nullif(ma.address1,'') 
		when 'address2' then nullif(ma.address2,'') 
		when 'address3' then nullif(ma.address3,'') 
		when 'city' then nullif(ma.city,'') 
		when 'postalcode' then nullif(ma.postalcode,'') 
		when 'county' then nullif(ma.county,'') 
		else nullif(ma.attn,'') end is not null

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'exists' 
	and tblc.displayTypeCode in ('RADIO','SELECT')
	and case tblc.fieldCodeAreaPartA
		when 'stateprov' then ma.stateid
		else ma.countryid end is not null

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'exists'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* ma_xxxx, not_exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='not_exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberID = m.memberID
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'not_exists' 
	and tblc.displayTypeCode not in ('RADIO','SELECT')
	and not exists (
		select ma.memberid
		from dbo.ams_memberAddresses as ma
		where ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaID
		and tblc.displayTypeCode not in ('RADIO','SELECT')
		and case tblc.fieldCodeAreaPartA
			when 'address1' then nullif(ma.address1,'') 
			when 'address2' then nullif(ma.address2,'') 
			when 'address3' then nullif(ma.address3,'') 
			when 'city' then nullif(ma.city,'') 
			when 'postalcode' then nullif(ma.postalcode,'') 
			when 'county' then nullif(ma.county,'') 
			else nullif(ma.attn,'') end is not null
	)

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberID = m.memberID
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'not_exists' 
	and tblc.displayTypeCode in ('RADIO','SELECT')
	and not exists (
		select ma.memberid
		from dbo.ams_memberAddresses as ma
		where ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaID
		and tblc.displayTypeCode in ('RADIO','SELECT')
		and case tblc.fieldCodeAreaPartA
			when 'stateprov' then ma.stateid
			else ma.countryid end is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'not_exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************ */
/* ma_xxxx, contains  */
/* ************ */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='contains') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'contains' 
	and case tblc.fieldCodeAreaPartA
		when 'address1' then isnull(ma.address1,'') 
		when 'address2' then isnull(ma.address2,'') 
		when 'address3' then isnull(ma.address3,'') 
		when 'city' then isnull(ma.city,'') 
		when 'postalcode' then isnull(ma.postalcode,'') 
		when 'county' then isnull(ma.county,'') 
		else isnull(ma.attn,'') end like '%' + cv.conditionValueString + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'contains'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************ */
/* ma_xxxx, contains_regex  */
/* ************ */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='contains_regex') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'contains_regex' 
	and case tblc.fieldCodeAreaPartA
		when 'address1' then isnull(ma.address1,'') 
		when 'address2' then isnull(ma.address2,'') 
		when 'address3' then isnull(ma.address3,'') 
		when 'city' then isnull(ma.city,'') 
		when 'postalcode' then isnull(ma.postalcode,'') 
		when 'county' then isnull(ma.county,'') 
		else isnull(ma.attn,'') end like '%' + cv.conditionValueString + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'contains_regex'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, neq, string  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='neq' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberID = m.memberID
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'STRING'
	and not exists (
		select mpl.memberid
		from dbo.ams_memberProfessionalLicenses as mpl
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
		left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
		where mpl.PLTypeID = tblc.fieldCodeAreaID
		and mpl.memberid = m.memberid
		and case tblc.fieldCodeAreaPartA
			when 'status' then isnull(mpls.statusName,'') 
			else isnull(mpl.licensenumber,'') end = cv.conditionValueString
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'neq' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, lt, string  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='lt' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'lt' 
	and tblc.dataTypeCode = 'STRING'
	and case tblc.fieldCodeAreaPartA
		when 'status' then isnull(mpls.statusName,'') 
		else isnull(mpl.licensenumber,'') end < cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'lt' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, lte, string  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='lte' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'lte' 
	and tblc.dataTypeCode = 'STRING'
	and case tblc.fieldCodeAreaPartA
		when 'status' then isnull(mpls.statusName,'') 
		else isnull(mpl.licensenumber,'') end <= cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'lte' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, gt, string  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='gt' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'gt' 
	and tblc.dataTypeCode = 'STRING'
	and case tblc.fieldCodeAreaPartA
		when 'status' then isnull(mpls.statusName,'') 
		else isnull(mpl.licensenumber,'') end > cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'gt' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, gte, string  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='gte' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'gte' 
	and tblc.dataTypeCode = 'STRING'
	and case tblc.fieldCodeAreaPartA
		when 'status' then isnull(mpls.statusName,'') 
		else isnull(mpl.licensenumber,'') end >= cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'gte' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, not_exists, string  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='not_exists' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberID = m.memberID
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'not_exists' 
	and tblc.dataTypeCode = 'STRING'
	and not exists (
		select mpl.memberid
		from dbo.ams_memberProfessionalLicenses as mpl 
		where mpl.PLTypeID = tblc.fieldCodeAreaID
		and mpl.memberid = m.memberid
		and case tblc.fieldCodeAreaPartA
			when 'status' then mpl.PLStatusID
			else nullif(mpl.licensenumber,'') end is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'not_exists' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, contains  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='contains') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'contains' 
	and case tblc.fieldCodeAreaPartA
		when 'status' then isnull(mpls.statusName,'') 
		else isnull(mpl.licensenumber,'') end like '%' + cv.conditionValueString + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'contains'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, contains_regex  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='contains_regex') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'contains_regex' 
	and case tblc.fieldCodeAreaPartA
		when 'status' then isnull(mpls.statusName,'') 
		else isnull(mpl.licensenumber,'') end like '%' + cv.conditionValueString + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'contains_regex'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, eq, date  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='eq' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'DATE'
	and mpl.activeDate = cv.conditionValueDate

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'eq' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, neq, date  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='neq' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'DATE'
	and not exists (
		select mpl.memberid
		from dbo.ams_memberProfessionalLicenses as mpl 
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
			and mpl.activeDate = cv.conditionValueDate
		where mpl.PLTypeID = tblc.fieldCodeAreaID
		and mpl.memberid = m.memberid
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'neq' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, lt, date  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='lt' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'lt' 
	and tblc.dataTypeCode = 'DATE'
	and mpl.activeDate < cv.conditionValueDate

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'lt' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, lte, date  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='lte' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'lte' 
	and tblc.dataTypeCode = 'DATE'
	and mpl.activeDate <= cv.conditionValueDate

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'lte' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, gt, date  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='gt' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'gt' 
	and tblc.dataTypeCode = 'DATE'
	and mpl.activeDate > cv.conditionValueDate

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'gt' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, gte, date  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='gte' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'gte' 
	and tblc.dataTypeCode = 'DATE'
	and mpl.activeDate >= cv.conditionValueDate

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'gte' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, exists, date  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='exists' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'exists' 
	and tblc.dataTypeCode = 'DATE'
	and mpl.activeDate is not null

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'exists' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, not_exists, date  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='not_exists' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'not_exists' 
	and tblc.dataTypeCode = 'DATE'
	and not exists (
		select mpl.memberid
		from dbo.ams_memberProfessionalLicenses as mpl 
		where mpl.PLTypeID = tblc.fieldCodeAreaID
		and mpl.memberid = m.memberid
		and mpl.activeDate is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'not_exists' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* mpl_xxxx, datepart  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='datepart') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'eq'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,mpl.activeDate) 
			when 'm' then datepart(m,mpl.activeDate) 
			when 'wk' then datepart(wk,mpl.activeDate)
			when 'dw' then datepart(dw,mpl.activeDate)
			when 'd' then datepart(d,mpl.activeDate) 
			when 'dy' then datepart(dy,mpl.activeDate)
			else datepart(yy,mpl.activeDate) end = cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'neq'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,mpl.activeDate) 
			when 'm' then datepart(m,mpl.activeDate) 
			when 'wk' then datepart(wk,mpl.activeDate)
			when 'dw' then datepart(dw,mpl.activeDate)
			when 'd' then datepart(d,mpl.activeDate) 
			when 'dy' then datepart(dy,mpl.activeDate)
			else datepart(yy,mpl.activeDate) end <> cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lt'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,mpl.activeDate) 
			when 'm' then datepart(m,mpl.activeDate) 
			when 'wk' then datepart(wk,mpl.activeDate)
			when 'dw' then datepart(dw,mpl.activeDate)
			when 'd' then datepart(d,mpl.activeDate) 
			when 'dy' then datepart(dy,mpl.activeDate)
			else datepart(yy,mpl.activeDate) end < cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lte'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,mpl.activeDate) 
			when 'm' then datepart(m,mpl.activeDate) 
			when 'wk' then datepart(wk,mpl.activeDate)
			when 'dw' then datepart(dw,mpl.activeDate)
			when 'd' then datepart(d,mpl.activeDate) 
			when 'dy' then datepart(dy,mpl.activeDate)
			else datepart(yy,mpl.activeDate) end <= cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gt'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,mpl.activeDate) 
			when 'm' then datepart(m,mpl.activeDate) 
			when 'wk' then datepart(wk,mpl.activeDate)
			when 'dw' then datepart(dw,mpl.activeDate)
			when 'd' then datepart(d,mpl.activeDate) 
			when 'dy' then datepart(dy,mpl.activeDate)
			else datepart(yy,mpl.activeDate) end > cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gte'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,mpl.activeDate) 
			when 'm' then datepart(m,mpl.activeDate) 
			when 'wk' then datepart(wk,mpl.activeDate)
			when 'dw' then datepart(dw,mpl.activeDate)
			when 'd' then datepart(d,mpl.activeDate) 
			when 'dy' then datepart(dy,mpl.activeDate)
			else datepart(yy,mpl.activeDate) end >= cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datepart' 

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'datepart'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* e_xxxx, attended  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Events' and expression='attended') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ev_events as e on e.eventID = tblc.fieldCodeAreaID
	inner join dbo.ev_registration as er on er.eventID = e.eventID and er.status = 'A'
	inner join dbo.ev_registrants as reg on reg.registrationID = er.registrationID and reg.status = 'A'
		and reg.attended = 1
	inner join dbo.ams_members as m2 on m2.memberid = reg.memberid
	inner join #tblMembers as m on m.memberID = m2.activeMemberID
	where tblc.fieldCodeArea = 'Events' 
	and tblc.expression = 'attended' 

	delete from #tblCondALL 
	where fieldCodeArea = 'Events' and expression = 'attended'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* e_xxxx, awarded  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Events' and expression='awarded') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ev_events as e on e.eventID = tblc.fieldCodeAreaID
	inner join dbo.ev_registration as er on er.eventID = e.eventID and er.status = 'A'
	inner join dbo.ev_registrants as reg on reg.registrationID = er.registrationID and reg.status = 'A'
		and reg.attended = 1
	inner join dbo.crd_requests as rc on rc.registrantID = reg.registrantID
		and rc.creditAwarded = 1
	inner join dbo.ams_members as m2 on m2.memberid = reg.memberid
	inner join #tblMembers as m on m.memberID = m2.activeMemberID
	where tblc.fieldCodeArea = 'Events' 
	and tblc.expression = 'awarded' 

	delete from #tblCondALL 
	where fieldCodeArea = 'Events' and expression = 'awarded'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************* */
/* mad_xxxx, eq  */
/* ************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Districting' and expression='eq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join #tblMembers as m on m.memberid = ma.memberid
	inner join dbo.ams_memberAddressData as mad on mad.addressid = ma.addressID
	inner join dbo.ams_memberDistrictValues as mdv on mdv.valueID = mad.valueID
		and mdv.districtTypeID = tblc.fieldCodeAreaID
		and mdv.valueID = cv.conditionValueInteger
	where tblc.fieldCodeArea = 'Districting' 
	and tblc.expression = 'eq' 

	delete from #tblCondALL 
	where fieldCodeArea = 'Districting' and expression = 'eq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************** */
/* mad_xxxx, neq  */
/* ************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Districting' and expression='neq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberID
	where tblc.fieldCodeArea = 'Districting' 
	and tblc.expression = 'neq' 
	and not exists (
		select ma.memberid
		from dbo.ams_memberAddresses as ma
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
		inner join dbo.ams_memberAddressData as mad on mad.addressid = ma.addressID
		inner join dbo.ams_memberDistrictValues as mdv on mdv.valueID = mad.valueID
			and mdv.districtTypeID = tblc.fieldCodeAreaID
			and mdv.valueID = cv.conditionValueInteger
		where ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaPartA
	)
	
	delete from #tblCondALL 
	where fieldCodeArea = 'Districting' and expression = 'neq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mad_xxxx, exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Districting' and expression='exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join #tblMembers as m on m.memberid = ma.memberid
	inner join dbo.ams_memberAddressData as mad on mad.addressid = ma.addressID
	inner join dbo.ams_memberDistrictValues as mdv on mdv.valueID = mad.valueID
		and mdv.districtTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Districting' 
	and tblc.expression = 'exists' 

	delete from #tblCondALL 
	where fieldCodeArea = 'Districting' and expression = 'exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mad_xxxx, not_exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Districting' and expression='not_exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Districting' 
	and tblc.expression = 'not_exists' 
	and not exists (
		select ma.memberid
		from dbo.ams_memberAddresses as ma
		inner join dbo.ams_memberAddressData as mad on mad.addressid = ma.addressID
		inner join dbo.ams_memberDistrictValues as mdv on mdv.valueID = mad.valueID
			and mdv.districtTypeID = tblc.fieldCodeAreaID
		where ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaPartA
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Districting' and expression = 'not_exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* acct_allocSum, eq */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='eq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct memberid, conditionID
	from (
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = @orgID and allocT.typeID = 5
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'eq' 
			union all
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = @orgID and VOT.typeID = 8
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
		inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'eq' 
	) as tmp
	group by memberid, conditionID, conditionValue
	having sum(allocAmt) = conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Accounting' and fieldCode='acct_allocsum' and expression='eq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* acct_allocSum, neq */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='neq') BEGIN

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct memberid, conditionID
	from (
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = @orgID and allocT.typeID = 5
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'neq' 
			union all
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = @orgID and VOT.typeID = 8
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
		inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'neq' 
	) as tmp
	group by memberid, conditionID, conditionValue
	having sum(allocAmt) <> conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Accounting' and fieldCode='acct_allocsum' and expression='neq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* acct_allocSum, lt */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='lt') BEGIN

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct memberid, conditionID
	from (
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = @orgID and allocT.typeID = 5
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'lt' 
			union all
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = @orgID and VOT.typeID = 8
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
		inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'lt' 
	) as tmp
	group by memberid, conditionID, conditionValue
	having sum(allocAmt) < conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Accounting' and fieldCode='acct_allocsum' and expression='lt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* acct_allocSum, lte */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='lte') BEGIN

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct memberid, conditionID
	from (
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = @orgID and allocT.typeID = 5
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'lte' 
			union all
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = @orgID and VOT.typeID = 8
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
		inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'lte' 
	) as tmp
	group by memberid, conditionID, conditionValue
	having sum(allocAmt) <= conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Accounting' and fieldCode='acct_allocsum' and expression='lte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* acct_allocSum, gt */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='gt') BEGIN

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct memberid, conditionID
	from (
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = @orgID and allocT.typeID = 5
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'gt' 
			union all
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = @orgID and VOT.typeID = 8
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
		inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'gt' 
	) as tmp
	group by memberid, conditionID, conditionValue
	having sum(allocAmt) > conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Accounting' and fieldCode='acct_allocsum' and expression='gt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* acct_allocSum, gte */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='gte') BEGIN

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct memberid, conditionID
	from (
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = @orgID and allocT.typeID = 5
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'gte' 
			union all
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = @orgID and VOT.typeID = 8
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
		inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'gte' 
	) as tmp
	group by memberid, conditionID, conditionValue
	having sum(allocAmt) >= conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Accounting' and fieldCode='acct_allocsum' and expression='gte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************** */
/* acct_allocSum, between */
/* ********************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='between') BEGIN

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct memberid, conditionID
	from (
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValueLower, accsplit.conditionValueUpper
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = @orgID and allocT.typeID = 5
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'between' 
			union all
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValueLower, accsplit.conditionValueUpper
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = @orgID and VOT.typeID = 8
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
		inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'between' 
	) as tmp
	group by memberid, conditionID, conditionValueLower, conditionValueUpper
	having sum(allocAmt) between conditionValueLower and conditionValueUpper

	delete from #tblCondALL 
	where fieldCodeArea = 'Accounting' and fieldCode='acct_allocsum' and expression='between'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************* */
/* acct_allocSum, exists */
/* ********************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='exists') BEGIN

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = @orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.expression = 'exists' 
		union
	select m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = @orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.expression = 'exists' 

	delete from #tblCondALL 
	where fieldCodeArea = 'Accounting' and fieldCode='acct_allocsum' and expression='exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************************* */
/* acct_allocSum, not_exists */
/* ************************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='not_exists') BEGIN

	select m.memberid, tblc.conditionID
	into #tblCondAccNotExist
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = @orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.expression = 'not_exists' 
		union
	select m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = @orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.expression = 'not_exists' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct mOuter.memberid, tblcOuter.conditionID
	from #tblCondALL as tblcOuter
	inner join #tblMembers as mOuter on mOuter.memberid = mOuter.memberID
	where tblcOuter.expression = 'not_exists' 
	except
	select memberid, conditionID
	from #tblCondAccNotExist

	delete from #tblCondALL 
	where fieldCodeArea = 'Accounting' and fieldCode='acct_allocsum' and expression='not_exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *************** */
/* rt_role, linked */
/* *************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Record Types' and fieldCode='rt_role' and expression='linked') BEGIN

	-- no roles defined just a record type	
	IF (select count(*) from #tblRecSplitRoles) = 0 BEGIN
		insert into #cache_members_conditions_shouldbe (memberid, conditionID)
		select distinct m.memberid, tblc.conditionID	
		from #tblCondALL as tblc
		inner join #tblRecSplit as recsplit on recsplit.conditionID = tblc.conditionID
		inner join dbo.ams_members as m2 on m2.recordtypeID = recsplit.recordTypeID
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		where tblc.expression = 'linked' 
	END ELSE BEGIN
		-- roles defined
		insert into #cache_members_conditions_shouldbe (memberid, conditionID)
		select distinct m.memberid, tblc.conditionID	
		from #tblCondALL as tblc
		inner join #tblRecSplit as recsplit on recsplit.conditionID = tblc.conditionID
		inner join dbo.ams_members as m2 on m2.recordtypeID = recsplit.recordTypeID
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join dbo.ams_recordRelationships as rr on rr.masterMemberID = m.memberid or rr.childMemberID = m.memberid
		inner join dbo.ams_recordTypesRelationshipTypes as rtrt on rtrt.recordTypeRelationshipTypeID = rr.recordTypeRelationshipTypeID 
		inner join #tblRecSplitRoles as roles on roles.role = rtrt.recordTypeRelationshipTypeID
		where rr.isactive = 1
		and rtrt.isActive = 1
		and tblc.expression = 'linked' 
	END

	delete from #tblCondALL 
	where fieldCodeArea = 'Record Types' and fieldCode='rt_role' and expression='linked'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc


/* **************** */
/* mh_entry, exists */
/* **************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member History' and fieldCode='mh_entry' and expression='exists') BEGIN

	-- when no subcategories defined	
	insert into #cache_members_conditions_shouldbe  (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMHSplit as mhsplit on mhsplit.conditionID = tblc.conditionID
	inner join dbo.ams_memberHistory as mh on mh.categoryID = mhsplit.historyCategory
		and mh.typeID = 1
		and mh.dateEntered >= isnull(mhsplit.historyEnteredDateLower,mh.dateEntered)
		and mh.dateEntered <= isnull(mhsplit.historyEnteredDateUpper,mh.dateEntered)
		and (
			(mh.userDate is null and mhsplit.historyDateLower is null and mhsplit.historyDateUpper is null)
			or 
			(mh.userDate >= isnull(mhsplit.historyDateLower,mh.userDate) and mh.userDate <= isnull(mhsplit.historyDateUpper,mh.userDate))
		)
		and (
			(mh.quantity is null and mhsplit.historyQuantityLower is null and mhsplit.historyQuantityUpper is null)
			or 
			(mh.quantity >= isnull(mhsplit.historyQuantityLower,mh.quantity) and mh.quantity <= isnull(mhsplit.historyQuantityUpper,mh.quantity))
		)
		and (
			(mh.dollarAmt is null and mhsplit.historyAmountLower is null and mhsplit.historyAmountUpper is null)
			or 
			(mh.dollarAmt >= isnull(mhsplit.historyAmountLower,mh.dollarAmt) and mh.dollarAmt <= isnull(mhsplit.historyAmountUpper,mh.dollarAmt))
		)
		and (
			(mh.description is null and mhsplit.historyDescriptionContains is null)
			or 
			(mh.description like '%' + isnull(mhsplit.historyDescriptionContains,mh.description) + '%')
		)
	inner join dbo.cms_categories as c on c.categoryID = mh.categoryID
	inner join dbo.cms_categoryTrees as ct on ct.categoryTreeID = c.categoryTreeID
	inner join dbo.sites as s on s.siteID = ct.siteID and s.orgID = @orgID
	inner join dbo.ams_members as m2 on m2.memberID = mh.memberID
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	left outer join #tblMHSubCategoriesSplit as mhsubcats on mhsubcats.historyCategory = mhsplit.historyCategory
		and mhsubcats.conditionID = mhsplit.conditionID
	where tblc.expression = 'exists'
	and mhsubcats.conditionID is null

	-- when subcategories defined	
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID	
	from #tblCondALL as tblc
	inner join #tblMHSplit as mhsplit on mhsplit.conditionID = tblc.conditionID
	inner join #tblMHSubCategoriesSplit as mhsubcats on mhsubcats.historyCategory = mhsplit.historyCategory
		and mhsubcats.conditionID = mhsplit.conditionID
	inner join dbo.ams_memberHistory as mh on mh.categoryID = mhsplit.historyCategory
		and mh.subcategoryID = mhsubcats.historySubCategory
		and mh.typeID = 1
		and mh.dateEntered >= isnull(mhsplit.historyEnteredDateLower,mh.dateEntered)
		and mh.dateEntered <= isnull(mhsplit.historyEnteredDateUpper,mh.dateEntered)
		and (
			(mh.userDate is null and mhsplit.historyDateLower is null and mhsplit.historyDateUpper is null)
			or 
			(mh.userDate >= isnull(mhsplit.historyDateLower,mh.userDate) and mh.userDate <= isnull(mhsplit.historyDateUpper,mh.userDate))
		)
		and (
			(mh.quantity is null and mhsplit.historyQuantityLower is null and mhsplit.historyQuantityUpper is null)
			or 
			(mh.quantity >= isnull(mhsplit.historyQuantityLower,mh.quantity) and mh.quantity <= isnull(mhsplit.historyQuantityUpper,mh.quantity))
		)
		and (
			(mh.dollarAmt is null and mhsplit.historyAmountLower is null and mhsplit.historyAmountUpper is null)
			or 
			(mh.dollarAmt >= isnull(mhsplit.historyAmountLower,mh.dollarAmt) and mh.dollarAmt <= isnull(mhsplit.historyAmountUpper,mh.dollarAmt))
		)
		and (
			(mh.description is null and mhsplit.historyDescriptionContains is null)
			or 
			(mh.description like '%' + isnull(mhsplit.historyDescriptionContains,mh.description) + '%')
		)
	inner join dbo.cms_categories as c on c.categoryID = mh.categoryID
	inner join dbo.cms_categoryTrees as ct on ct.categoryTreeID = c.categoryTreeID
	inner join dbo.sites as s on s.siteID = ct.siteID and s.orgID = @orgID
	inner join dbo.ams_members as m2 on m2.memberID = mh.memberID
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	where tblc.expression = 'exists'

	delete from #tblCondALL 
	where fieldCodeArea = 'Member History' and fieldCode='mh_entry' and expression='exists'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc


donecalc:
	select @totalID = Count(*) from #cache_members_conditions_shouldbe
	
	-- Log
	set @starttime2 = getdate()
	INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
	VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start changes to real tables of ' + cast(@itemGroupUID as varchar(60)));

	-- delete member/conditions that should not be there
	delete cmc
	from dbo.cache_members_conditions as cmc
	inner join #tblCondALLFinal as caf on caf.conditionID = cmc.conditionID
	inner join #tblMembers as m on m.memberid = cmc.memberid
	and not exists (
		select *
		from #cache_members_conditions_shouldbe
		where conditionID = cmc.conditionID
		and memberid = cmc.memberid
	)	
		select @totalRemoved = @@rowcount
		
	-- insert member/conditions that should be but arent already there
	insert into dbo.cache_members_conditions (memberID, conditionID)
	select distinct cache.memberID, cache.conditionID
	from #cache_members_conditions_shouldbe as cache
	where not exists (
		select memberid, conditionid
		from dbo.cache_members_conditions
		where memberid = cache.memberid
		and conditionID = cache.conditionID
	)
		select @totalAdded = @@rowcount

	-- log
	INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
	VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End changes to real tables of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));

	-- return query of members affected
	select distinct memberID
	from #tblMembers
	
	-- cleanup temp tables
	IF OBJECT_ID('tempdb..#cache_members_conditions_shouldbe') IS NOT NULL
		DROP TABLE #cache_members_conditions_shouldbe
	IF OBJECT_ID('tempdb..#tblCond') IS NOT NULL
		DROP TABLE #tblCond
	IF OBJECT_ID('tempdb..#tblCondALL') IS NOT NULL
		DROP TABLE #tblCondALL
	IF OBJECT_ID('tempdb..#tblCondALLFinal') IS NOT NULL
		DROP TABLE #tblCondALLFinal
	IF OBJECT_ID('tempdb..#tblFCSplit') IS NOT NULL
		DROP TABLE #tblFCSplit
	IF OBJECT_ID('tempdb..#tblSubSplit') IS NOT NULL
		DROP TABLE #tblSubSplit
	IF OBJECT_ID('tempdb..#tblAccSplit') IS NOT NULL
		DROP TABLE #tblAccSplit
	IF OBJECT_ID('tempdb..#tblAccSplitGL') IS NOT NULL
		DROP TABLE #tblAccSplitGL
	IF OBJECT_ID('tempdb..#tblRecSplit') IS NOT NULL
		DROP TABLE #tblRecSplit
	IF OBJECT_ID('tempdb..#tblRecSplitRoles') IS NOT NULL
		DROP TABLE #tblRecSplitRoles
	IF OBJECT_ID('tempdb..#tblCondAccNotExist') IS NOT NULL
		DROP TABLE #tblCondAccNotExist
	IF OBJECT_ID('tempdb..#tblCondValues') IS NOT NULL
		DROP TABLE #tblCondValues
	IF OBJECT_ID('tempdb..#tblMembers') IS NOT NULL
		DROP TABLE #tblMembers
	IF OBJECT_ID('tempdb..#tblMHSplit') IS NOT NULL
		DROP TABLE #tblMHSplit
	IF OBJECT_ID('tempdb..#tblMHSubCategoriesSplit') IS NOT NULL
		DROP TABLE #tblMHSubCategoriesSplit

	-- Log
	set @totalMS = Datediff(ms,@starttime,getdate())
	insert into platformQueue.dbo.tblQueueLog_processMemberGroups ([procedure], logDate, totalMS, totalID, totalRemoved, totalAdded, orgID, conditionIDList, memberIDList, itemGroupUID)
	values (OBJECT_NAME(@@PROCID), @starttime, @totalMS, @totalID, @totalRemoved, @totalAdded, @orgID, nullif(@conditionIDList,''), nullif(@memberIDList,''), @itemGroupUID)

	INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
	VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End Process of orgID=' + cast(@orgID as varchar(10)) + ' conditionID=' + left(@conditionIDList,100) + ' memberID=' + left(@memberIDList,100) + ' of ' + cast(@itemGroupUID as varchar(60)), @totalMS);

	set nocount off
GO


USE [platformQueue]
GO
ALTER PROC [dbo].[job_popMemGrp_process]
@itemGroupUID uniqueidentifier,
@logTreeID uniqueidentifier

AS

SET NOCOUNT ON

IF @itemGroupUID is null
	RETURN -1

declare @starttime datetime, @starttime2 datetime, @totalMS int, @totalID int
declare @jobUID uniqueidentifier
declare @procname varchar(100)
set @starttime = getdate()
set @jobUID = NEWID()

-- Log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start Process of ' + cast(@itemGroupUID as varchar(60)));

declare @statusReady int, @statusGrabbed int, @statusProcessing int, @statusDone int
select @statusReady = qs.queueStatusID 
	from platformQueue.dbo.tblQueueStatuses as qs
	inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popMemGroups'
	and qs.queueStatus = 'readyToProcess'
select @statusGrabbed = qs.queueStatusID 
	from platformQueue.dbo.tblQueueStatuses as qs
	inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popMemGroups'
	and qs.queueStatus = 'grabbedForProcessing'
select @statusProcessing = qs.queueStatusID 
	from platformQueue.dbo.tblQueueStatuses as qs
	inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popMemGroups'
	and qs.queueStatus = 'processingItem'
select @statusDone = qs.queueStatusID 
	from platformQueue.dbo.tblQueueStatuses as qs
	inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popMemGroups'
	and qs.queueStatus = 'done'

IF OBJECT_ID('tempdb..#tmpItems') IS NOT NULL 
	DROP TABLE #tmpItems
IF OBJECT_ID('tempdb..#tblRGM') IS NOT NULL
	DROP TABLE #tblRGM
IF OBJECT_ID('tempdb..#tblmultiCondRules') IS NOT NULL
	DROP TABLE #tblmultiCondRules
IF OBJECT_ID('tempdb..#tmpNewMemberGroups') IS NOT NULL
	DROP TABLE #tmpNewMemberGroups
IF OBJECT_ID('tempdb..#tmpNewMemberGroups2') IS NOT NULL
	DROP TABLE #tmpNewMemberGroups2
IF OBJECT_ID('tempdb..#tmpCurrentMemberGroups') IS NOT NULL
	DROP TABLE #tmpCurrentMemberGroups
IF OBJECT_ID('tempdb..#tmpDelete') IS NOT NULL
	DROP TABLE #tmpDelete
IF OBJECT_ID('tempdb..#tmpInsert') IS NOT NULL
	DROP TABLE #tmpInsert
IF OBJECT_ID('tempdb..#membersToUpdate') IS NOT NULL
	DROP TABLE #membersToUpdate

CREATE TABLE #tmpItems (itemUID uniqueidentifier, orgID int, memberID int)
CREATE TABLE #tblRGM (memberID int, groupID int);
CREATE TABLE #tblmultiCondRules (orgID int, ruleID int PRIMARY KEY, ruleSQL varchar(max));
CREATE TABLE #tmpNewMemberGroups (orgID int, memberID int, groupID int, isManualDirect tinyint, isManualIndirect tinyint, isVirtualDirect tinyint, isVirtualIndirect tinyint)
CREATE TABLE #tmpNewMemberGroups2 (autoid int IDENTITY(1,1), orgID int, memberID int, groupID int, isManualDirect bit, isManualIndirect bit, isVirtualDirect bit, isVirtualIndirect bit)
CREATE TABLE #tmpCurrentMemberGroups (autoid int, orgid int, memberid int, groupid int, isManualDirect bit, isManualIndirect bit, isVirtualDirect bit, isVirtualIndirect bit)
CREATE TABLE #tmpDelete (autoid int, memberid int, groupID int)
CREATE TABLE #tmpInsert (orgID int, memberID int, groupID int, isManualDirect bit, isManualIndirect bit, isVirtualDirect bit, isVirtualIndirect bit)
CREATE TABLE #membersToUpdate (memberID int)


/* ********************************** */
/* dequeue items ready for processing */
/* ********************************** */
UPDATE qi WITH (UPDLOCK, READPAST)
SET qi.queueStatusID = @statusProcessing,
	qi.dateUpdated = getdate(),
	qi.jobUID = @jobUID,
	qi.jobDateStarted = getdate(),
	qi.jobServerID = 0
	OUTPUT inserted.itemUID, qid.orgID, qid.memberID
	INTO #tmpItems
FROM dbo.tblQueueItems as qi
INNER JOIN dbo.tblQueueItems_processMemberGroups as qid ON qid.itemUID = qi.itemUID
WHERE qi.queueStatusID = @statusReady
and qid.itemGroupUID = @itemGroupUID


-- indexes for speed
create index tmp_orgID on #tmpItems (orgID);
create index tmp_MemberID on #tmpItems (memberID);


/* *********************************** */
/* get virtual assignments for members */
/* *********************************** */
-- Log
set @starttime2 = getdate()
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start one-condition rules of ' + cast(@itemGroupUID as varchar(60)));

-- process in mass all one-condition rules
; WITH all1CondRules AS (
	select r.orgID, r.ruleXML.value('(//condition/@id)[1]','uniqueidentifier') as conditionUID, rg.groupID
	from membercentral.dbo.ams_virtualGroupRules as r WITH(NOLOCK)
	inner join membercentral.dbo.ams_virtualGroupRuleGroups as rg WITH(NOLOCK) on rg.ruleID = r.ruleID
	where r.orgID in (select distinct orgID from #tmpItems)
	and r.isActive = 1
	and r.ruleTypeID = 1
	and r.ruleXML.value('count(//condition)','int') = 1
)
insert into #tblRGM (memberid, groupID)
select distinct cmc.memberid, vg.groupID
from #tmpItems as tmp
inner join all1CondRules as vg on vg.orgID = tmp.orgID
inner join membercentral.dbo.ams_virtualGroupConditions as vgc WITH(NOLOCK) on vgc.uid = vg.conditionUID
inner join membercentral.dbo.cache_members_conditions as cmc WITH(NOLOCK) on cmc.conditionID = vgc.conditionID
	and cmc.memberid = tmp.memberID
inner join membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = cmc.memberid
	and m.memberid = m.activeMemberID
	and m.status <> 'D'
OPTION (RECOMPILE)

-- log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End one-condition rules of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));

-- Log
set @starttime2 = getdate()
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start multi-condition rules of ' + cast(@itemGroupUID as varchar(60)));

-- loop over multi-condition rules with conditions tied to these members
declare @orgID int, @minRuleID int, @ruleSQL varchar(max), @dynsql nvarchar(max)

insert into #tblmultiCondRules (orgID, ruleID, ruleSQL)
select distinct r.orgID, r.ruleID, r.ruleSQL
from membercentral.dbo.ams_virtualGroupRules as r WITH(NOLOCK)
inner join membercentral.dbo.ams_virtualGroupRuleGroups as rg WITH(NOLOCK) on rg.ruleID = r.ruleID
INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.groupID = rg.groupid and g.status <> 'D'
CROSS APPLY r.ruleXML.nodes('//condition') as C(condition)
inner join membercentral.dbo.ams_virtualGroupConditions as vgc WITH(NOLOCK) on vgc.uid = C.condition.value('@id','uniqueidentifier')
inner join membercentral.dbo.cache_members_conditions as cmc WITH(NOLOCK) on cmc.conditionID = vgc.conditionID
inner join #tmpItems as tmp on tmp.memberID = cmc.memberID
where r.isActive = 1
and r.ruleTypeID = 1
and r.ruleXML.value('count(//condition)','int') > 1
and len(r.ruleSQL) > 0
OPTION (RECOMPILE)

select @minRuleID = min(ruleID) from #tblmultiCondRules
while @minRuleID is not null BEGIN
	select @ruleSQL = ruleSQL, @orgID = orgID from #tblmultiCondRules where ruleID = @minRuleID
	select @dynsql = ''
	select @dynsql = '
			use membercentral;
			insert into #tblRGM (memberid, groupID)
			select distinct m.memberID, rg.groupID
			from membercentral.dbo.ams_members as m WITH(NOLOCK)
			inner join #tmpItems as tmp on tmp.memberID = m.memberID 
			inner join membercentral.dbo.ams_virtualGroupRules as r WITH(NOLOCK) on r.ruleID = ' + cast(@minRuleID as varchar(10)) + '
			inner join membercentral.dbo.ams_virtualGroupRuleGroups as rg WITH(NOLOCK) on rg.ruleID = r.ruleID
			INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.groupID = rg.groupid and g.status <> ''D''
			where m.orgID = ' + cast(@orgID as varchar(10)) + ' 
			and m.memberid = m.activeMemberID
			and m.status <> ''D''
			and (' + @ruleSQL + ')
			'
	exec sp_executesql @dynsql

	select @minRuleID = min(ruleID) from #tblmultiCondRules where ruleID > @minRuleID
END

-- log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End multi-condition rules of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));

create index RGM_GroupID on #tblRGM (groupID);

INSERT INTO #tmpNewMemberGroups (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
select DISTINCT rg.orgID, dg.memberID, rg.groupID, 0, 0, isVirtualDirect = rg.isBaseGroup, isVirtualIndirect = (~rg.isBaseGroup)
FROM #tblRGM as dg
INNER JOIN membercentral.dbo.cache_ams_recursiveGroups as rg WITH(NOLOCK) ON dg.groupid = rg.startGroupid
OPTION (RECOMPILE)

/* ********************************** */
/* get manual assignments for members */
/* ********************************** */
-- Log
set @starttime2 = getdate()
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start manual assignment of ' + cast(@itemGroupUID as varchar(60)));

-- you could be directly assigned and indirectly assigned to the same group.
INSERT INTO #tmpNewMemberGroups (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
select distinct tmp.orgID, tmp.memberID, g.groupID, isManualDirect = rg.isBaseGroup, isManualIndirect = (~rg.isBaseGroup), 0, 0
from #tmpItems as tmp
inner join membercentral.dbo.ams_memberGroups AS mg WITH(NOLOCK) on mg.memberID = tmp.memberID
INNER JOIN membercentral.dbo.cache_ams_recursiveGroups as rg WITH(NOLOCK) ON mg.groupid = rg.startGroupid
INNER JOIN membercentral.dbo.ams_groups AS g WITH(NOLOCK) ON g.groupID = rg.groupID
INNER JOIN membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = mg.memberID
where g.status <> 'D'
AND m.memberID = m.activeMemberID
AND m.status <> 'D'

-- public group for all members
INSERT INTO #tmpNewMemberGroups (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
SELECT distinct tmp.orgID, tmp.memberID, g.groupID, 0, 0, 0, 1
FROM #tmpItems as tmp
INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.orgID = tmp.orgID
INNER JOIN membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = tmp.memberID and m.orgID = g.orgID
WHERE g.isSystemGroup = 1
AND g.groupName = 'Public'
AND g.status = 'A'
AND m.memberid = m.activeMemberID
AND m.status <> 'D'
	union all
SELECT distinct tmp.orgID, 0 as memberID, g.groupID, 0, 0, 0, 1
FROM #tmpItems as tmp
INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.orgID = tmp.orgID
WHERE g.isSystemGroup = 1
AND g.groupName = 'Public'
AND g.status = 'A'

-- guest group for all guests
INSERT INTO #tmpNewMemberGroups (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
SELECT distinct tmp.orgID, tmp.memberID, g.groupID, 0, 0, 0, 1
FROM #tmpItems as tmp
INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.orgID = tmp.orgID
INNER JOIN membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = tmp.memberID and m.orgID = g.orgID
INNER JOIN membercentral.dbo.ams_memberTypes as mt WITH(NOLOCK) on mt.memberTypeID = m.memberTypeID
WHERE g.isSystemGroup = 1
AND g.groupName = 'Guests'
AND g.status = 'A'
AND m.memberid = m.activeMemberID
AND m.status <> 'D'
AND mt.memberType = 'Guest'

-- users group for all users
INSERT INTO #tmpNewMemberGroups (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
SELECT distinct tmp.orgID, tmp.memberID, g.groupID, 0, 0, 0, 1
FROM #tmpItems as tmp
INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.orgID = tmp.orgID
INNER JOIN membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = tmp.memberID and m.orgID = g.orgID
INNER JOIN membercentral.dbo.ams_memberTypes as mt WITH(NOLOCK) on mt.memberTypeID = m.memberTypeID
WHERE g.isSystemGroup = 1
AND g.groupName = 'Users'
AND g.status = 'A'
AND m.memberid = m.activeMemberID
AND m.status <> 'D'
AND mt.memberType = 'User'

-- new member groups compacted
INSERT INTO #tmpNewMemberGroups2 (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
select orgID, memberID, groupID, 
	max(isManualDirect) as isManualDirect, 
	max(isManualIndirect) as isManualIndirect, 
	max(isVirtualDirect) as isVirtualDirect, 
	max(isVirtualIndirect) as isVirtualIndirect
from #tmpNewMemberGroups
group by orgID, memberID, groupID

-- log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End manual assignment of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));


/* ******************** */
/* Modify member groups */
/* ******************** */
-- current member groups
INSERT INTO #tmpCurrentMemberGroups (autoid, orgid, memberid, groupid, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
select mg.autoid, mg.orgid, mg.memberid, mg.groupid, mg.isManualDirect, mg.isManualIndirect, mg.isVirtualDirect, mg.isVirtualIndirect
from #tmpItems as tmp
inner join membercentral.dbo.cache_members_groups as mg WITH(NOLOCK) on mg.memberID = tmp.memberID
	union
select mg.autoid, mg.orgid, mg.memberid, mg.groupid, mg.isManualDirect, mg.isManualIndirect, mg.isVirtualDirect, mg.isVirtualIndirect
from #tmpItems as tmp
inner join membercentral.dbo.cache_members_groups as mg WITH(NOLOCK) on mg.orgID = tmp.orgID and mg.memberID = 0

-- rows to be deleted: #tmpCurrentMemberGroups but not in #tmpNewMemberGroups2
-- also delete rows where memberID is no longer the activeMemberID (for cleanup)
INSERT INTO #tmpDelete (autoID, memberID, groupID)
SELECT curr.autoid, curr.memberID, curr.groupID
from #tmpCurrentMemberGroups as curr
where not exists (
	select autoid 
	from #tmpNewMemberGroups2
	where orgID = curr.orgID
	and memberID = curr.memberID
	and groupID = curr.groupID
	and isManualDirect = curr.isManualDirect
	and isManualIndirect = curr.isManualIndirect
	and isVirtualDirect = curr.isVirtualDirect
	and isVirtualIndirect = curr.isVirtualIndirect
)
	union
select mg.autoid, mg.memberID, mg.groupID
from #tmpItems as tmp
inner join membercentral.dbo.cache_members_groups as mg WITH(NOLOCK) on mg.memberID = tmp.memberID
inner join membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = mg.memberID
where m.memberID <> m.activeMemberID
or m.status = 'D'

-- rows to be added: mem/grp in #tmpNewMemberGroups2 but not in #tmpCurrentMemberGroups
INSERT INTO #tmpInsert (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
SELECT distinct new.orgid, new.memberid, new.groupid, new.isManualDirect, new.isManualIndirect, new.isVirtualDirect, new.isVirtualIndirect
from #tmpNewMemberGroups2 as new
where not exists (
	select autoid
	from #tmpCurrentMemberGroups
	where orgID = new.orgID
	and memberID = new.memberID
	and groupID = new.groupID
	and isManualDirect = new.isManualDirect
	and isManualIndirect = new.isManualIndirect
	and isVirtualDirect = new.isVirtualDirect
	and isVirtualIndirect = new.isVirtualIndirect
)

-- Log
set @starttime2 = getdate()
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start changes to real tables of ' + cast(@itemGroupUID as varchar(60)));

-- delete rows that shouldnt be there anymore
DELETE FROM vw
FROM membercentral.dbo.cache_members_groups as vw
INNER JOIN #tmpDelete as del on del.autoID = vw.autoID

-- add rows that should be there now
INSERT INTO membercentral.dbo.cache_members_groups (orgid, memberid, groupid, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
SELECT distinct orgid, memberid, groupid, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect
FROM #tmpInsert

-- log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End changes to real tables of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));


/* ******************* */
/* Modify group prints */
/* ******************* */
-- Log
set @starttime2 = getdate()
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start changes to group prints of ' + cast(@itemGroupUID as varchar(60)));

-- update group prints
insert into #membersToUpdate (memberID)
select d.memberID 
from #tmpDelete as d
left outer join #tmpInsert as i on i.memberid = d.memberid and i.groupid = d.groupID
where i.orgid is null
	union 
select i.memberID 
from #tmpInsert as i
left outer join #tmpDelete as d on i.memberid = d.memberid and i.groupid = d.groupID
where d.autoID is null

select @totalID = count(*) from #membersToUpdate

if exists (select top 1 memberID from #membersToUpdate) BEGIN
	CREATE INDEX IX_membersToUpdate_MID ON #membersToUpdate (memberID); 
	
	select @orgID = null
	select @orgID = min(orgID) from #tmpItems
	while @orgID is not null BEGIN
		exec membercentral.dbo.cache_perms_updateGroupPrintsForMembersBulk @orgid=@orgID
		exec membercentral.dbo.ams_createMemberSiteDefaultsByOrgID @orgID=@orgid
		select @orgID = min(orgID) from #tmpItems where orgID > @orgID
	end
END

-- log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End changes to group prints of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));


/* **************** */
/* mark all as done */
/* **************** */
update qi WITH (UPDLOCK, HOLDLOCK)
set qi.queueStatusID = @statusDone,
	qi.dateUpdated = getdate()
FROM platformQueue.dbo.tblQueueItems as qi
INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
where qi.queueStatusID = @statusProcessing

-- clear all entries marked as done
DELETE from platformQueue.dbo.tblQueueItems
where queueStatusID = @statusDone

select @totalID = count(*) from #tmpItems

-- cleanup temp tables
IF OBJECT_ID('tempdb..#tmpItems') IS NOT NULL 
	DROP TABLE #tmpItems
IF OBJECT_ID('tempdb..#tblRGM') IS NOT NULL
	DROP TABLE #tblRGM
IF OBJECT_ID('tempdb..#tblmultiCondRules') IS NOT NULL
	DROP TABLE #tblmultiCondRules
IF OBJECT_ID('tempdb..#tmpNewMemberGroups') IS NOT NULL
	DROP TABLE #tmpNewMemberGroups
IF OBJECT_ID('tempdb..#tmpNewMemberGroups2') IS NOT NULL
	DROP TABLE #tmpNewMemberGroups2
IF OBJECT_ID('tempdb..#tmpCurrentMemberGroups') IS NOT NULL
	DROP TABLE #tmpCurrentMemberGroups
IF OBJECT_ID('tempdb..#tmpDelete') IS NOT NULL
	DROP TABLE #tmpDelete
IF OBJECT_ID('tempdb..#tmpInsert') IS NOT NULL
	DROP TABLE #tmpInsert
IF OBJECT_ID('tempdb..#membersToUpdate') IS NOT NULL
	DROP TABLE #membersToUpdate

-- Log
set @totalMS = Datediff(ms,@starttime,getdate())
insert into dbo.tblQueueLog_processMemberGroups ([procedure], logDate, totalMS, totalID, itemGroupUID)
values (OBJECT_NAME(@@PROCID), @starttime, @totalMS, @totalID, @itemGroupUID)

INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End Process of ' + cast(@itemGroupUID as varchar(60)), @totalMS);

RETURN 0
GO

ALTER PROC [dbo].[queue_processMemberGroups_insert]
@orgID int,
@memberIDList varchar(max),
@conditionIDList varchar(max),
@runSchedule smallint,
@itemGroupUID uniqueidentifier OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @statusInserting int, @statusReady int
	select @statusInserting = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'popCondCache'
		and qs.queueStatus = 'insertingItems'
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'popCondCache'
		and qs.queueStatus = 'readyToProcess'
	
	-- if not passed in, there should only be one itemGroupUID for these inserts
	IF @itemGroupUID is null
		set @itemGroupUID = NEWID()

	-- holding table
	IF OBJECT_ID('tempdb..#tmpQueue') IS NOT NULL 
		DROP TABLE #tmpQueue
	CREATE TABLE #tmpQueue (orgID int, memberID int, conditionID int, itemUID uniqueidentifier DEFAULT NEWID())

	insert into #tmpQueue (orgID, memberID, conditionID)
	select o.orgID, nullIf(tmpM.listitem,0) as memberID, nullIf(tmpC.listitem,0) as conditionID
	from (select @orgID as orgID) as o
	outer apply membercentral.dbo.fn_intListToTable(@memberIDList,',') as tmpM
	outer apply membercentral.dbo.fn_intListToTable(@conditionIDList,',') as tmpC
		except
	select qid.orgID, qid.memberID, qid.conditionID
	from platformQueue.dbo.tblQueueItems_processMemberGroups as qid
	inner join platformQueue.dbo.tblQueueItems as qi on qi.itemUID = qid.itemUID
	inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.queueStatusID
	inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popCondCache'
	and qs.queueStatus not in ('grabbedForProcessing','processingItem','done')

	-- queue items
	insert into platformQueue.dbo.tblQueueItems_processMemberGroups (itemUID, itemGroupUID, orgID, memberID, conditionID)
		OUTPUT inserted.itemUID, inserted.dateAdded, @statusInserting 
		INTO platformQueue.dbo.tblQueueItems(itemUID, dateAdded, queueStatusID)
	select itemUID, @itemGroupUID, orgID, memberID, conditionID
	from #tmpQueue

	-- update queue item groups to show ready to process
	update qi WITH (UPDLOCK, HOLDLOCK)
	set qi.queueStatusID = @statusReady,
		qi.dateUpdated = getdate()
	from platformQueue.dbo.tblQueueItems as qi
	inner join #tmpQueue as s on s.itemUID = qi.itemUID

	IF OBJECT_ID('tempdb..#tmpQueue') IS NOT NULL 
		DROP TABLE #tmpQueue

	IF @TranCounter = 0
		COMMIT TRAN;

END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH


declare @msgXML xml
set @msgXML = '<itemGroupUID>' + cast(@itemGroupUID as varchar(60)) + '</itemGroupUID>'

-- if we are processing immediately
IF @runSchedule = 1 
BEGIN
	-- Log
	declare @logTreeID uniqueidentifier
	set @logTreeID = NEWID()
	INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, QueueMessage)
	VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Starting immediate processing', @msgXML);

	EXEC dbo.queue_processMemberGroups_process @itemGroupUID=@itemGroupUID, @logTreeID=@logTreeID
END
ELSE 
	-- if we are delayed processing, use service broker
	IF @runSchedule = 2 BEGIN
		EXEC dbo.sb_ProcessMemberGroupsSendMessage @msgXML
	END

RETURN 0
GO

ALTER PROC [dbo].[queue_processMemberGroups_requeue]
@itemGroupUID uniqueidentifier,
@logTreeID uniqueidentifier

AS

DECLARE @starttime datetime
set @starttime = getdate()

declare @newstatuspopCondCache int, @newstatuspopMemGroups int, @queueType varchar(20)
select @newstatuspopCondCache = qs.queueStatusID 
	from dbo.tblQueueStatuses as qs WITH(NOLOCK)
	inner join dbo.tblQueueTypes as qt WITH(NOLOCK) on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popCondCache'
	and qs.queueStatus = 'readyToProcess'
select @newstatuspopMemGroups = qs.queueStatusID 
	from dbo.tblQueueStatuses as qs WITH(NOLOCK)
	inner join dbo.tblQueueTypes as qt WITH(NOLOCK) on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popMemGroups'
	and qs.queueStatus = 'readyToProcess'
select top 1 @queueType = qt.queueType
	from dbo.tblQueueItems as qi WITH(NOLOCK)
	inner join dbo.tblQueueItems_processMemberGroups as qid WITH(NOLOCK) on qid.itemUID = qi.itemUID
	INNER JOIN dbo.tblQueueStatuses as qs WITH(NOLOCK) on qs.queueStatusID = qi.queueStatusID
	INNER JOIN dbo.tblQueueTypes AS qt WITH(NOLOCK) ON qt.queueTypeID = qs.queueTypeID
	where qid.itemGroupUID = @itemGroupUID

IF @queueType is not null BEGIN
	-- Log
	INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
	VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start Requeue of ' + cast(@itemGroupUID as varchar(60)));

	IF @queueType = 'popCondCache'
		update qi WITH(UPDLOCK, HOLDLOCK)
		set qi.queueStatusID = @newstatuspopCondCache,
			qi.dateUpdated = getdate(),
			qi.jobUID = null,
			qi.jobDateStarted = null,
			qi.jobServerID = null
		from dbo.tblQueueItems as qi
		inner join dbo.tblQueueItems_processMemberGroups as qid on qid.itemUID = qi.itemUID
		WHERE qid.itemGroupUID = @itemGroupUID

	IF @queueType = 'popMemGroups'
		update qi WITH(UPDLOCK, HOLDLOCK)
		set qi.queueStatusID = @newstatuspopMemGroups,
			qi.dateUpdated = getdate(),
			qi.jobUID = null,
			qi.jobDateStarted = null,
			qi.jobServerID = null
		from dbo.tblQueueItems as qi
		inner join dbo.tblQueueItems_processMemberGroups as qid on qid.itemUID = qi.itemUID
		WHERE qid.itemGroupUID = @itemGroupUID

	-- put back into service broker
	declare @msgXML xml
	set @msgXML = '<itemGroupUID>' + cast(@itemGroupUID as varchar(60)) + '</itemGroupUID>'
	EXEC dbo.sb_ProcessMemberGroupsSendMessage @msgXML

	-- Log
	INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
	VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End Requeue of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime,getdate()));
END

RETURN 0
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sb_ServiceBrokerErrorLog]') AND type in (N'U'))
DROP TABLE dbo.sb_ServiceBrokerErrorLog
GO

USE [memberCentral]
GO
ALTER PROC [dbo].[ams_createVirtualGroupCondition]
@orgID int,
@conditionTypeID int,
@fieldCode varchar(40), 
@expression varchar(20),
@datepart varchar(8) = null,
@dateExpression varchar(20) = null,
@value xml = null,
@isDefined bit = 1,
@byPassQueue bit = 0,
@conditionID int OUTPUT

AS

DECLARE @expressionID int, @dateExpressionID int, @fieldCodeDataTypeID int, @fieldCodeDisplayTypeID int,
	@hashValue varbinary(max)

SELECT @conditionID = null

-- get expressionid from expression
select @expressionID = expressionID
	from dbo.ams_virtualGroupExpressions
	where expression = @expression
	IF @@ERROR <> 0 or @expressionID is null GOTO on_error
IF @dateExpression is not null BEGIN
	select @dateExpressionID = expressionID
		from dbo.ams_virtualGroupExpressions
		where expression = @dateExpression
		IF @@ERROR <> 0 GOTO on_error
END

-- dedupe, order, and hash the incoming value for comparison
select @value = isnull((
	SELECT distinct V.item.value('@key','varchar(30)') as [key], V.item.value('@value','varchar(100)') as [value]
	FROM @value.nodes('//value') AS V(item)
	INNER JOIN dbo.ams_virtualGroupConditionKeys as k on k.conditionKey = V.item.value('@key','varchar(30)')
	ORDER BY 1, 2
	FOR XML RAW('value'), ROOT('values'), TYPE)
	,'<values/>')
select @hashValue = HashBytes('MD5',CONVERT(varchar(8000),@value))

-- see if exact condition already exists.
SELECT @conditionID = conditionID
	FROM dbo.ams_virtualGroupConditions
	WHERE orgID = @orgID
	AND conditionTypeID = @conditionTypeID
	AND fieldCode = @fieldCode
	AND expressionID = @expressionID
	AND isNull(datePart,'') = isnull(@datePart,'')
	AND isNull(dateexpressionid,0) = isnull(@dateExpressionID,0)
	AND hashValue = @hashValue
IF @conditionID IS NULL BEGIN

	-- get data/display types from fieldCode
	select @fieldCodeDataTypeID=dat.dataTypeID, @fieldCodeDisplayTypeID=dit.displayTypeID
		from dbo.fn_ams_getVirtualGroupConditionVerboseFields(@orgID,@fieldCode) as fields
		inner join dbo.ams_memberDataColumnDataTypes as dat on dat.dataTypeCode = fields.dataTypeCode
		inner join dbo.ams_memberDataColumnDisplayTypes as dit on dit.displayTypeCode = fields.displayTypeCode
		IF @@ERROR <> 0 or @fieldCodeDataTypeID is null or @fieldCodeDisplayTypeID is null GOTO on_error

	insert into dbo.ams_virtualGroupConditions (orgID, dataTypeID, displayTypeID, expressionID, 
		[datePart], dateExpressionID, [verbose], fieldCode, [uid], conditionTypeID, isDefined, hashValue)
	values (@orgID, @fieldCodeDataTypeID, @fieldCodeDisplayTypeID, @expressionID, @datePart, 
		@dateExpressionID, null, @fieldCode, newID(), @conditionTypeID, @isDefined, @hashValue)
		IF @@ERROR <> 0 GOTO on_error
		SELECT @conditionID = SCOPE_IDENTITY()

	insert into dbo.ams_virtualGroupConditionValues (conditionID, conditionKeyID, conditionValue)
	SELECT @conditionID, k.conditionKeyID, V.item.value('@value','varchar(100)') as [value]
	FROM @value.nodes('//value') AS V(item)
	INNER JOIN dbo.ams_virtualGroupConditionKeys as k on k.conditionKey = V.item.value('@key','varchar(30)')
	ORDER BY 1, 2
		IF @@ERROR <> 0 GOTO on_error

	UPDATE dbo.ams_virtualGroupConditions
	SET [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
	where conditionID = @conditionID
		IF @@ERROR <> 0 GOTO on_error

	-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
	if @byPassQueue = 0 and @isDefined = 1 AND EXISTS (select conditionTypeID from dbo.ams_virtualGroupConditionTypes where conditionTypeID = @conditionTypeID and processImmediately = 1)
	BEGIN
		declare @itemGroupUID uniqueidentifier
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList='', @conditionIDList=@conditionID, @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT
	end

END

RETURN 0

on_error:
	SELECT @conditionID = 0
	RETURN -1
GO

USE [memberCentral]
GO
ALTER PROC [dbo].[migrate_customfields]
@orgcode varchar(5)

AS

-- this can be called if orgcode on old, sitecode on new, and orgcode on new are all the same

declare @newcolumnid int, @oldcolumnID int, @valueID int, @mingrp int, @minODID int, @mingrpNew int,
	@errCode int, @orgID int, @rc int, @conditionID int, @ruleID int
declare @colList varchar(max), @mincol varchar(255), @minOD varchar(max), @fieldcode varchar(60),
	@x varchar(400), @s varchar(400), @rulename varchar(400), @ruleSQL varchar(max), @valueXML xml
declare @conditionUID uniqueidentifier
SET @errCode = 0
SET @orgID = dbo.fn_getOrgIDFromOrgCode(@orgcode)
	
BEGIN TRAN

select @colList = replace(replace((
	select replace(optionDescription,' ','^') as [data()]
	from tlasites.trialsmith.dbo.orgMemberOptionNames
	where orgcode = @orgcode
	FOR XML PATH ('')), ' ', ','),'^',' ')
select @mincol = min(listitem) from membercentral.dbo.fn_varCharListToTable(@colList,',')
while @minCol is not null BEGIN	
	select @oldcolumnID = null
	select @newcolumnID = null
	select @oldcolumnID = optionNameID from tlasites.trialsmith.dbo.orgmemberOptionNames where orgcode=@orgcode and optionDescription=@minCol
	select @newcolumnID = columnID from dbo.ams_memberDataColumns where orgID=@orgid and columnName=@minCol
	IF @newcolumnID is null BEGIN

		-- create it as a select/string field if there are options on old
		IF EXISTS (
			select omoc.optionCodeID
			from tlasites.trialsmith.dbo.orgMemberOptionCodes as omoc
			where omoc.optionNameID = @oldcolumnID
			) BEGIN
			EXEC @rc = dbo.ams_createMemberDataColumn @orgID=@orgID, @columnName=@minCol, @columnDesc=@minCol, @allowMultiple=0, @skipImport=0, @allowNull=1, @defaultValue=null, @allowNewValuesOnImport=1, @dataTypeCode='STRING', @displayTypeCode='SELECT', @isReadOnly=0, @columnID=@newcolumnID OUTPUT
				IF @@ERROR <> 0 OR @RC <> 0 OR @newColumnID = 0 BEGIN
					SET @errCode = 900
					GOTO on_done
				END

			select @minOD = null
			select @minOD = min(optionCodeDescription) from tlasites.trialsmith.dbo.orgMemberOptionCodes where optionNameID = @oldcolumnID
			while @minOD is not null BEGIN
				EXEC @rc = dbo.ams_createMemberDataColumnValue @columnID=@newcolumnID, @columnValue=@minOD, @valueID=@valueID OUTPUT
					IF @@ERROR <> 0 OR @RC <> 0 OR @valueID = 0 BEGIN
						SET @errCode = 901
						GOTO on_done
					END

				-- if there are groups tied to the option on old, create those links on new
				IF EXISTS (
					SELECT omog.usergroupid
					from tlasites.trialsmith.dbo.orgMemberOptionGroups as omog
					inner join tlasites.trialsmith.dbo.orgMemberOptionCodes as omoc on omoc.optionCodeID = omog.optionCodeID
					where omoc.optionCodeDescription = @minOD
					AND omoc.optionNameID = @oldcolumnID
				) BEGIN

					-- create condition
					select @fieldcode = null
					select @fieldcode = 'md_' + cast(@newcolumnID as varchar(5))
					set @valueXML = '<values><value key="value" value="' + cast(@valueID as varchar(20)) + '"/></values>'

					EXEC @rc = dbo.ams_createVirtualGroupCondition @orgID=@orgID, @conditionTypeID=1, @fieldCode=@fieldcode, @expression='eq', @datepart=null, @dateExpression=null, @value=@valueXML, @isDefined=1, @bypassQueue=1, @conditionID=@conditionID OUTPUT
						IF @@ERROR <> 0 OR @RC <> 0 OR @conditionID = 0 BEGIN
							SET @errCode = 902
							GOTO on_done
						END
					SELECT @conditionUID = [uid] from dbo.ams_virtualGroupConditions where conditionID = @conditionID

					-- create rule
					SELECT @x = null
					SELECT @s = null
					SELECT @rulename = null
					SELECT @x = '<rule><conditionset op="AND" act="include" id="' + cast(newid() as varchar(255)) + '"><condition id="' + cast(@conditionUID as varchar(50)) + '" /></conditionset></rule>'; 
					select @rulename = @minCol + ' - ' + @minOD
					EXEC @RC = dbo.ams_createVirtualGroupRule @orgID, 1, @rulename, @x, '', @ruleID OUTPUT
						IF @@ERROR <> 0 OR @RC <> 0 OR @ruleID = 0 BEGIN
							SET @errCode = 903
							GOTO on_done
						END
					EXEC dbo.ams_updateVirtualGroupRuleSQL @ruleID=@ruleID
						IF @@ERROR <> 0 BEGIN
							SET @errCode = 903
							GOTO on_done
						END
								
					-- loop over the linked groups				
					select @mingrp = null
					select @mingrp = min(omog.usergroupid) 
						from tlasites.trialsmith.dbo.orgMemberOptionGroups as omog
						inner join tlasites.trialsmith.dbo.orgMemberOptionCodes as omoc on omoc.optionCodeID = omog.optionCodeID
						where omoc.optionCodeDescription = @minOD
						AND omoc.optionNameID = @oldcolumnID
					while @mingrp is not null begin

						-- create rulegroup
						select @mingrpNew = g.groupID 
							from dbo.ams_groups as g
							inner join tlasites.trialsmith.dbo.usergroups as oldg on oldg.description = g.groupName COLLATE Latin1_General_CI_AI
								and oldg.orgcode = @orgcode
							where g.orgID = @orgID 
							and oldg.usergroupID = @mingrp
							IF @@ERROR <> 0 BEGIN
								SET @errCode = 904
								GOTO on_done
							END

						EXEC dbo.ams_createVirtualGroupRuleGroup @ruleID, @mingrpNew
							IF @@ERROR <> 0 BEGIN
								SET @errCode = 905
								GOTO on_done
							END

						select @mingrp = min(omog.usergroupid) 
							from tlasites.trialsmith.dbo.orgMemberOptionGroups as omog
							inner join tlasites.trialsmith.dbo.orgMemberOptionCodes as omoc on omoc.optionCodeID = omog.optionCodeID
							where omoc.optionCodeDescription = @minOD
							AND omoc.optionNameID = @oldcolumnID
							AND omog.usergroupid > @mingrp
					END

				END

				select @minOD = min(optionCodeDescription) from tlasites.trialsmith.dbo.orgMemberOptionCodes where optionNameID = @oldcolumnID and optionCodeDescription > @minOD
			END

			END	
	
		-- else, create it as a textbox question
		ELSE BEGIN
			EXEC @rc = dbo.ams_createMemberDataColumn @orgID=@orgID, @columnName=@minCol, @columnDesc=@minCol, 
				@allowMultiple=0, @skipImport=0, @allowNull=1, @defaultValue=null, @allowNewValuesOnImport=1, @dataTypeCode='STRING', 
				@displayTypeCode='TEXTBOX', @isReadOnly=0, @columnID=@newcolumnID OUTPUT
				IF @@ERROR <> 0 OR @RC <> 0 OR @newcolumnID = 0 BEGIN
					SET @errCode = 906
					GOTO on_done
				END
		END
	END

	select @mincol = min(listitem) from membercentral.dbo.fn_varCharListToTable(@colList,',') where listitem > @mincol
END

COMMIT TRAN

-- Activate all rules
UPDATE ams_virtualGroupRules set isActive=1
where orgid = @orgID

-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
declare @itemGroupUID uniqueidentifier
EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList='', @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT


on_done:
	IF @errCode = 900
		print 'Migration stopped. Unable to create column ' + @minCol + '.'
	IF @errCode = 901
		print 'Migration stopped. Unable to create column value ' + @minOD + ' for column ' + @minCol + '.'
	IF @errCode = 902
		print 'Migration stopped. Unable to create virtual group condition for value ' + @minOD + ' for column ' + @minCol + '.'
	IF @errCode = 903
		print 'Migration stopped. Unable to create virtual group rule ' + @rulename + '.'
	IF @errCode = 904
		print 'Migration stopped. Unable to get group id for virtual group rule ' + @rulename + '.'
	IF @errCode = 905
		print 'Migration stopped. Unable to create virtual group rule ' + @rulename + '.'
	IF @errCode = 906
		print 'Migration stopped. Unable to create column ' + @minCol + '.'

	IF @errCode >= 900
		ROLLBACK TRAN

	print 'Migration migrate_customfields finished.'
GO

USE [memberCentral]
GO
ALTER PROC [dbo].[ams_updateVirtualGroupCondition]
@orgID int,
@conditionID int,
@expression varchar(20),
@datepart varchar(8) = null,
@dateExpression varchar(20) = null,
@value xml = null,
@bypassQueue bit = 0

AS

DECLARE @expressionID int, @dateExpressionID int, @fieldCodeDataTypeID int, @checkIsDefined bit, 
	@checkConditionID int, @checkconditionTypeID int, @checkFieldCode varchar(40),
	@hashValue varbinary(max)

-- get expressionid from expression
select @expressionID = expressionID
	from dbo.ams_virtualGroupExpressions
	where expression = @expression
	IF @@ERROR <> 0 or @expressionID is null GOTO on_error
IF @dateExpression is not null BEGIN
	select @dateExpressionID = expressionID
		from dbo.ams_virtualGroupExpressions
		where expression = @dateExpression
		IF @@ERROR <> 0 GOTO on_error
END

-- dedupe, order, and hash the incoming value for comparison
select @value = isnull((
	SELECT distinct V.item.value('@key','varchar(30)') as [key], V.item.value('@value','varchar(100)') as [value]
	FROM @value.nodes('//value') AS V(item)
	INNER JOIN dbo.ams_virtualGroupConditionKeys as k on k.conditionKey = V.item.value('@key','varchar(30)')
	ORDER BY 1, 2
	FOR XML RAW('value'), ROOT('values'), TYPE)
	,'<values/>')
select @hashValue = HashBytes('MD5',CONVERT(varchar(8000),@value))

select @checkconditionTypeID=conditionTypeID, @checkFieldCode=fieldCode, @checkIsDefined=isDefined
	FROM dbo.ams_virtualGroupConditions
	WHERE orgID = @orgID
	AND conditionID = @conditionID

-- see if exact condition already exists.
SELECT @checkConditionID = conditionID
	FROM dbo.ams_virtualGroupConditions
	WHERE orgID = @orgID
	AND conditionTypeID = @checkconditionTypeID
	AND fieldCode = @checkFieldCode
	AND expressionID = @expressionID
	AND isNull(datePart,'') = isnull(@datePart,'')
	AND isNull(dateexpressionid,0) = isnull(@dateExpressionID,0)
	AND hashValue = @hashValue
	AND conditionID <> @conditionID
IF @checkConditionID IS NULL BEGIN
	UPDATE dbo.ams_virtualGroupConditions
	SET	expressionID = @expressionID,
		[datePart] = @datepart,
		dateExpressionID = @dateExpressionID,
		hashValue = @hashValue
	WHERE conditionID = @conditionID
		IF @@ERROR <> 0 GOTO on_error

	delete from dbo.ams_virtualGroupConditionValues
	where conditionID = @conditionID
		IF @@ERROR <> 0 GOTO on_error

	insert into dbo.ams_virtualGroupConditionValues (conditionID, conditionKeyID, conditionValue)
	SELECT @conditionID, k.conditionKeyID, V.item.value('@value','varchar(100)') as [value]
	FROM @value.nodes('//value') AS V(item)
	INNER JOIN dbo.ams_virtualGroupConditionKeys as k on k.conditionKey = V.item.value('@key','varchar(30)')
	ORDER BY 1, 2

	UPDATE dbo.ams_virtualGroupConditions
	SET [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
	where conditionID = @conditionID
		IF @@ERROR <> 0 GOTO on_error

	-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
	if @bypassQueue = 0 AND @checkIsDefined = 1 AND EXISTS (select conditionTypeID from dbo.ams_virtualGroupConditionTypes where conditionTypeID = @checkconditionTypeID and processImmediately = 1)
	BEGIN
		declare @itemGroupUID uniqueidentifier
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList='', @conditionIDList=@conditionID, @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT
			IF @@ERROR <> 0 GOTO on_error
	END
END ELSE BEGIN
	RETURN -2
END

RETURN 0

on_error:
	RETURN -1
GO

USE [memberCentral]
GO
ALTER PROC [dbo].[ams_deleteVirtualGroupCondition]
@orgID int,
@conditionIDList varchar(max)

AS

DECLARE @tblConditions TABLE (conditionID int)
DECLARE @tblRules TABLE (ruleID int)

-- split conditions
INSERT INTO @tblConditions (conditionID)
select c.conditionID
from dbo.fn_intListToTable(@conditionIDList,',') as tmp
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tmp.listitem
where c.orgID = @orgID

-- get rules this condition is in
INSERT INTO @tblRules (ruleID)
select r.ruleID
from dbo.ams_virtualGroupRules as r
CROSS APPLY r.ruleXML.nodes('//condition') as Conditions(Condition)
inner join dbo.ams_virtualGroupConditions as c on c.uid = Condition.value('@id','uniqueidentifier')
inner join @tblConditions as tblC on tblC.conditionID = c.conditionID
where ruleXML.exist('//condition[@id=sql:column("c.uid")]') = 1

BEGIN TRAN
	
	-- del from rules
	update r
	set ruleXML.modify('delete //condition[@id=sql:column("c.uid")]')
	from dbo.ams_virtualGroupRules as r
	CROSS APPLY r.ruleXML.nodes('//condition') as Conditions(Condition)
	inner join dbo.ams_virtualGroupConditions as c on c.uid = Condition.value('@id','uniqueidentifier')
	inner join @tblConditions as tblC on tblC.conditionID = c.conditionID
		IF @@ERROR <> 0 GOTO on_error

	-- ensure there are no empty condition sets now. if so, inactivate those rules
	update r
	set r.isActive = 0
	from dbo.ams_virtualGroupRules as r
	inner join @tblRules as tr on tr.ruleID = r.ruleID
	where ruleXML.exist('//conditionset[not(node())]') = 1
		IF @@ERROR <> 0 GOTO on_error

	-- queue processing of member groups by processing the conditions in the rules AND also the members that were in that condition (@runSchedule=2 indicates delayed processing) 
	declare @itemGroupUID uniqueidentifier, @allconditionIDList varchar(max), @memberIDList varchar(max)
	SELECT @allconditionIDList = COALESCE(@allconditionIDList + ',', '') + cast(vgc.conditionID as varchar(10)) 
		from dbo.ams_virtualGroupRules as r
		inner join @tblRules as tmpR on tmpR.ruleID = r.ruleID
		inner join dbo.ams_virtualGroupRuleGroups as rg on rg.ruleID = r.ruleID
		CROSS APPLY r.ruleXML.nodes('//condition') as C(condition)
		inner join dbo.ams_virtualGroupConditions as vgc on vgc.uid = C.condition.value('@id','uniqueidentifier')
		group by vgc.conditionID
	IF @allconditionIDList is not null BEGIN
		set @itemGroupUID = null
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList='', @conditionIDList=@allconditionIDList, @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT
	END
	SELECT @memberIDList = COALESCE(@memberIDList + ',', '') + cast(cmc.memberid as varchar(10)) 
		from dbo.cache_members_conditions as cmc
		inner join @tblConditions as c on c.conditionID = cmc.conditionID
		group by cmc.memberid
	IF @memberIDList is not null BEGIN
		set @itemGroupUID = null
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberIDList, @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT
	END

	-- regenerate SQL for all updated rules
	declare @minRuleID int, @ruleSQL varchar(max)
	select @minRuleID = min(ruleID) from @tblRules
	while @minRuleID is not null BEGIN
		EXEC dbo.ams_updateVirtualGroupRuleSQL @ruleID=@minRuleID
			IF @@ERROR <> 0 GOTO on_error
		select @minRuleID = min(ruleID) from @tblRules where ruleID > @minRuleID
	END

	-- del from cache
	delete from dbo.cache_members_conditions
	where conditionID in (select conditionID from @tblConditions)
		IF @@ERROR <> 0 GOTO on_error

	-- del from queue (update as done for natural queue cleanup)
	declare @statusDone int
	select @statusDone = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'popCondCache'
		and qs.queueStatus = 'done'
	update qi WITH (UPDLOCK, HOLDLOCK)
	set qi.queueStatusID = @statusDone,
		qi.dateUpdated = getdate()
	FROM platformQueue.dbo.tblQueueItems as qi
	inner join platformQueue.dbo.tblQueueItems_processMemberGroups as qid on qid.itemUID = qi.itemUID
	inner join @tblConditions as c on c.conditionID = qid.conditionID
		IF @@ERROR <> 0 GOTO on_error

	-- del from conditionValues
	DELETE FROM dbo.ams_virtualGroupConditionValues
	where conditionID in (select conditionID from @tblConditions)
		IF @@ERROR <> 0 GOTO on_error

	-- del from conditions
	DELETE FROM dbo.ams_virtualGroupConditions
	where conditionID in (select conditionID from @tblConditions)
		IF @@ERROR <> 0 GOTO on_error

IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

USE [platformQueue]
GO
ALTER PROC [dbo].[queue_processMemberGroups_process]
@itemGroupUID uniqueidentifier,
@logTreeID uniqueidentifier

AS

IF @itemGroupUID is null
	RETURN -1

declare @starttime datetime, @totalMS int
set @starttime = getdate()

-- Log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start Process of ' + cast(@itemGroupUID as varchar(60)));

BEGIN TRY
	-- check to see if the itemGroupUID is still in the queue. It may have been cleared by another process.
	IF EXISTS (
		select qi.itemUID
		from dbo.tblQueueItems as qi WITH(NOLOCK)
		inner join dbo.tblQueueItems_processMemberGroups as qid WITH(NOLOCK) on qid.itemUID = qi.itemUID
		inner join dbo.tblQueueStatuses as qs WITH(NOLOCK) on qs.queuestatusID = qi.queuestatusID
		inner join dbo.tblQueueTypes as qt WITH(NOLOCK) on qt.queueTypeID = qs.queueTypeID
		where qid.itemGroupUID = @itemGroupUID
		and qt.queueType in ('popCondCache','popMemGroups')
		and qs.queueStatus = 'readyToProcess'
	) 
	BEGIN
		-- process the queues */
		EXEC dbo.job_popCondCache_process @itemGroupUID=@itemGroupUID, @logTreeID=@logTreeID
		EXEC dbo.job_popMemGrp_process @itemGroupUID=@itemGroupUID, @logTreeID=@logTreeID
	END

	-- Log
	INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
	VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End Process of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime,getdate()));
END TRY
BEGIN CATCH
	-- Log
	INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
	VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End Process of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime,getdate()));

	-- bubble up
	EXEC membercentral.dbo.up_errorhandler
END CATCH

RETURN 0
GO

USE [platformQueue]
GO
ALTER PROC [dbo].[queue_processMemberGroups_insert]
@orgID int,
@memberIDList varchar(max),
@conditionIDList varchar(max),
@runSchedule smallint,
@itemGroupUID uniqueidentifier OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @statusInserting int, @statusReady int
	select @statusInserting = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'popCondCache'
		and qs.queueStatus = 'insertingItems'
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'popCondCache'
		and qs.queueStatus = 'readyToProcess'
	
	-- if not passed in, there should only be one itemGroupUID for these inserts
	IF @itemGroupUID is null
		set @itemGroupUID = NEWID()

	-- holding table
	IF OBJECT_ID('tempdb..#tmpQueue') IS NOT NULL 
		DROP TABLE #tmpQueue
	CREATE TABLE #tmpQueue (orgID int, memberID int, conditionID int, itemUID uniqueidentifier DEFAULT NEWID())

	insert into #tmpQueue (orgID, memberID, conditionID)
	select o.orgID, nullIf(tmpM.listitem,0) as memberID, nullIf(tmpC.listitem,0) as conditionID
	from (select @orgID as orgID) as o
	outer apply membercentral.dbo.fn_intListToTable(@memberIDList,',') as tmpM
	outer apply membercentral.dbo.fn_intListToTable(@conditionIDList,',') as tmpC
		except
	select qid.orgID, qid.memberID, qid.conditionID
	from platformQueue.dbo.tblQueueItems_processMemberGroups as qid
	inner join platformQueue.dbo.tblQueueItems as qi on qi.itemUID = qid.itemUID
	inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.queueStatusID
	inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popCondCache'
	and qs.queueStatus not in ('grabbedForProcessing','processingItem','done')

	-- queue items
	insert into platformQueue.dbo.tblQueueItems_processMemberGroups (itemUID, itemGroupUID, orgID, memberID, conditionID)
		OUTPUT inserted.itemUID, inserted.dateAdded, @statusInserting 
		INTO platformQueue.dbo.tblQueueItems(itemUID, dateAdded, queueStatusID)
	select itemUID, @itemGroupUID, orgID, memberID, conditionID
	from #tmpQueue

	-- update queue item groups to show ready to process
	update qi WITH (UPDLOCK, HOLDLOCK)
	set qi.queueStatusID = @statusReady,
		qi.dateUpdated = getdate()
	from platformQueue.dbo.tblQueueItems as qi
	inner join #tmpQueue as s on s.itemUID = qi.itemUID

	IF OBJECT_ID('tempdb..#tmpQueue') IS NOT NULL 
		DROP TABLE #tmpQueue

	IF @TranCounter = 0
		COMMIT TRAN;

END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH


declare @msgXML xml
set @msgXML = '<itemGroupUID>' + cast(@itemGroupUID as varchar(60)) + '</itemGroupUID>'

-- if we are processing immediately
IF @runSchedule = 1 
BEGIN
	BEGIN TRY
		-- Log
		declare @logTreeID uniqueidentifier
		set @logTreeID = NEWID()
		INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, QueueMessage)
		VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Starting immediate processing', @msgXML);

		EXEC dbo.queue_processMemberGroups_process @itemGroupUID=@itemGroupUID, @logTreeID=@logTreeID
	END TRY
	BEGIN CATCH
		EXEC membercentral.dbo.up_errorhandler
		RETURN -1
	END CATCH
END
ELSE 
	-- if we are delayed processing, use service broker
	IF @runSchedule = 2 BEGIN
		EXEC dbo.sb_ProcessMemberGroupsSendMessage @msgXML
	END

RETURN 0
GO

use platformQueue
GO

ALTER PROC [dbo].[job_popCondCache_process]
@itemGroupUID uniqueidentifier,
@logTreeID uniqueidentifier

AS

SET NOCOUNT ON

IF @itemGroupUID is null
	RETURN -1

declare @starttime datetime, @starttime2 datetime, @totalMS int, @totalID int
declare @jobUID uniqueidentifier, @minitemUID uniqueidentifier, @minOrgID int, 
	@minMemberID int, @itemIDList VARCHAR(max), @nowDate datetime
set @starttime = getdate()
set @jobUID = NEWID()
set @nowDate = getdate()

-- Log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start Process of ' + cast(@itemGroupUID as varchar(60)));

declare @statusReady int, @statusGrabbed int, @statusProcessing int, @statusDone int, @statusReadyPopMemGroups int 
select @statusReady = qs.queueStatusID 
	from platformQueue.dbo.tblQueueStatuses as qs WITH(NOLOCK)
	inner join platformQueue.dbo.tblQueueTypes as qt WITH(NOLOCK) on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popCondCache'
	and qs.queueStatus = 'readyToProcess'
select @statusGrabbed = qs.queueStatusID 
	from platformQueue.dbo.tblQueueStatuses as qs WITH(NOLOCK)
	inner join platformQueue.dbo.tblQueueTypes as qt WITH(NOLOCK) on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popCondCache'
	and qs.queueStatus = 'grabbedForProcessing'
select @statusProcessing = qs.queueStatusID 
	from platformQueue.dbo.tblQueueStatuses as qs WITH(NOLOCK)
	inner join platformQueue.dbo.tblQueueTypes as qt WITH(NOLOCK) on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popCondCache'
	and qs.queueStatus = 'processingItem'
select @statusDone = qs.queueStatusID 
	from platformQueue.dbo.tblQueueStatuses as qs WITH(NOLOCK)
	inner join platformQueue.dbo.tblQueueTypes as qt WITH(NOLOCK) on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popCondCache'
	and qs.queueStatus = 'done'
select @statusReadyPopMemGroups = qs.queueStatusID 
	from platformQueue.dbo.tblQueueStatuses as qs WITH(NOLOCK)
	inner join platformQueue.dbo.tblQueueTypes as qt WITH(NOLOCK) on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popMemGroups'
	and qs.queueStatus = 'readyToProcess'

IF OBJECT_ID('tempdb..#tmpItems') IS NOT NULL 
	DROP TABLE #tmpItems
CREATE TABLE #tmpItems (itemUID uniqueidentifier, orgID int, memberID int NULL, conditionID int NULL)

IF OBJECT_ID('tempdb..#tmpItemSpecific') IS NOT NULL 
	DROP TABLE #tmpItemSpecific
CREATE TABLE #tmpItemSpecific (itemID int)

IF OBJECT_ID('tempdb..#tmpMCNextCache') IS NOT NULL 
	DROP TABLE #tmpMCNextCache
CREATE TABLE #tmpMCNextCache (memberID int)


-- Log
set @starttime2 = getdate()
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start shortcut and finer items of ' + cast(@itemGroupUID as varchar(60)));

-- add shortcuts to queue for easier processing: if org/member has more than 20 conditions, just do org/member
insert into platformQueue.dbo.tblQueueItems_processMemberGroups (itemUID, itemGroupUID, orgID, memberID)
	OUTPUT inserted.itemUID, inserted.dateAdded, @statusReady 
	INTO platformQueue.dbo.tblQueueItems(itemUID, dateAdded, queueStatusID)
select NEWID() as itemUID, @itemGroupUID as itemGroupUID, qid.orgID, qid.memberID
FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
INNER JOIN platformQueue.dbo.tblQueueItems_processMemberGroups as qid WITH(NOLOCK) ON qid.itemUID = qi.itemUID
WHERE qi.queueStatusID = @statusReady
and qid.itemGroupUID = @itemGroupUID
and qid.memberid is not null
and qid.conditionid is not null 
group by orgID, memberID
having count(distinct qid.conditionID) > 20

-- add shortcuts to queue for easier processing: if org/condition has more than 20 members, just do org/condition
insert into platformQueue.dbo.tblQueueItems_processMemberGroups (itemUID, itemGroupUID, orgID, conditionID)
	OUTPUT inserted.itemUID, inserted.dateAdded, @statusReady 
	INTO platformQueue.dbo.tblQueueItems(itemUID, dateAdded, queueStatusID)
select NEWID() as itemUID, @itemGroupUID as itemGroupUID, qid.orgID, qid.conditionID
FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
INNER JOIN platformQueue.dbo.tblQueueItems_processMemberGroups as qid WITH(NOLOCK) ON qid.itemUID = qi.itemUID
WHERE qi.queueStatusID = @statusReady
and qid.itemGroupUID = @itemGroupUID
and qid.memberid is not null
and qid.conditionid is not null 
group by orgID, conditionID
having count(distinct qid.memberID) > 20

-- dequeue items ready for processing
UPDATE qi WITH(UPDLOCK, READPAST)
SET qi.queueStatusID = @statusGrabbed,
	qi.dateUpdated = @nowDate,
	qi.jobUID = @jobUID,
	qi.jobDateStarted = @nowDate,
	qi.jobServerID = 0
	OUTPUT inserted.itemUID, qid.orgID, qid.memberID, qid.conditionID
	INTO #tmpItems
FROM platformQueue.dbo.tblQueueItems as qi
INNER JOIN platformQueue.dbo.tblQueueItems_processMemberGroups as qid ON qid.itemUID = qi.itemUID
WHERE qi.queueStatusID = @statusReady
and qid.itemGroupUID = @itemGroupUID

-- Also grab finer items not assigned to this ItemGroupUID so we don't process them multiple times.
-- if orgID only, grab all others with same org
UPDATE qi WITH(UPDLOCK, READPAST)
SET qi.queueStatusID = @statusGrabbed,
	qi.dateUpdated = @nowDate,
	qi.jobUID = @jobUID,
	qi.jobDateStarted = @nowDate,
	qi.jobServerID = 0
	OUTPUT inserted.itemUID, qid.orgID, qid.memberID, qid.conditionID
	INTO #tmpItems
FROM platformQueue.dbo.tblQueueItems as qi
INNER JOIN platformQueue.dbo.tblQueueItems_processMemberGroups as qid ON qid.itemUID = qi.itemUID
INNER JOIN #tmpItems as tmp on tmp.orgID = qid.orgID
WHERE qi.queueStatusID = @statusReady
and qid.itemGroupUID <> @itemGroupUID
and tmp.memberID is null
and tmp.conditionID is null

-- if memberID only, grab all others with same memberID
UPDATE qi WITH(UPDLOCK, READPAST)
SET qi.queueStatusID = @statusGrabbed,
	qi.dateUpdated = @nowDate,
	qi.jobUID = @jobUID,
	qi.jobDateStarted = @nowDate,
	qi.jobServerID = 0
	OUTPUT inserted.itemUID, qid.orgID, qid.memberID, qid.conditionID
	INTO #tmpItems
FROM platformQueue.dbo.tblQueueItems as qi
INNER JOIN platformQueue.dbo.tblQueueItems_processMemberGroups as qid ON qid.itemUID = qi.itemUID
INNER JOIN #tmpItems as tmp on tmp.orgID = qid.orgID and tmp.memberID = qid.memberID
WHERE qi.queueStatusID = @statusReady
and qid.itemGroupUID <> @itemGroupUID
and tmp.memberID is not null
and tmp.conditionID is null

-- if conditionID only, grab all others with same conditionID
UPDATE qi WITH(UPDLOCK, READPAST)
SET qi.queueStatusID = @statusGrabbed,
	qi.dateUpdated = @nowDate,
	qi.jobUID = @jobUID,
	qi.jobDateStarted = @nowDate,
	qi.jobServerID = 0
	OUTPUT inserted.itemUID, qid.orgID, qid.memberID, qid.conditionID
	INTO #tmpItems
FROM platformQueue.dbo.tblQueueItems as qi
INNER JOIN platformQueue.dbo.tblQueueItems_processMemberGroups as qid ON qid.itemUID = qi.itemUID
INNER JOIN #tmpItems as tmp on tmp.orgID = qid.orgID and tmp.conditionID = qid.conditionID
WHERE qi.queueStatusID = @statusReady
and qid.itemGroupUID <> @itemGroupUID
and tmp.memberID is null
and tmp.conditionID is not null

-- if we grabbed others, change their ItemGroupUID to match
update qid WITH(UPDLOCK, HOLDLOCK)
set qid.itemGroupUID = @itemGroupUID
from #tmpItems as tmp
INNER JOIN platformQueue.dbo.tblQueueItems_processMemberGroups as qid ON qid.itemUID = tmp.itemUID
where qid.itemGroupUID <> @itemGroupUID

-- log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End shortcut and finer items of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));


-- process ORGID only records : if entry for orgid and nothing else, process these and delete any finer items.
IF EXISTS (
	SELECT top 1 tmp.itemUID 
	FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
	INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
	WHERE tmp.memberID is null 
	AND tmp.conditionID is null		
	AND qi.queueStatusID = @statusGrabbed)
BEGIN
	select @minOrgID = null
	SELECT @minOrgID = min(tmp.orgID)
		FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
		INNER JOIN platformQueue.dbo.tblQueueItems_processMemberGroups as qid WITH(NOLOCK) on qid.itemUID = qi.itemUID
		INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
		WHERE tmp.memberID is null 
		AND tmp.conditionID is null		
		AND qi.queueStatusID = @statusGrabbed
	while @minOrgID is not null BEGIN

		-- mark all rows having the same org as processing
		update qi WITH(UPDLOCK, HOLDLOCK)
		set qi.queueStatusID = @statusProcessing,
			qi.dateUpdated = getdate()
		FROM platformQueue.dbo.tblQueueItems as qi
		INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
		where tmp.orgID = @minOrgID
		and qi.queueStatusID = @statusGrabbed

		insert into #tmpMCNextCache (memberID) 
		exec membercentral.dbo.cache_members_populateMemberConditionCache @orgID=@minOrgID, @conditionIDList=null, @memberIDList=null, 
				@processImmediateOnly=1, @itemGroupUID=@itemGroupUID, @logTreeID=@logTreeID

		insert into platformQueue.dbo.tblQueueItems_processMemberGroups (itemUID, itemGroupUID, orgID, memberID)
			OUTPUT inserted.itemUID, inserted.dateAdded, @statusReadyPopMemGroups 
			INTO platformQueue.dbo.tblQueueItems(itemUID, dateAdded, queueStatusID)
		select NEWID() as itemUID, @itemGroupUID as itemGroupUID, @minOrgID as orgID, tmp.memberID
		from #tmpMCNextCache as tmp

		delete from #tmpMCNextCache

		-- mark all rows having the same org as done
		update qi WITH(UPDLOCK, HOLDLOCK)
		set qi.queueStatusID = @statusDone,
			qi.dateUpdated = getdate()
		FROM platformQueue.dbo.tblQueueItems as qi
		INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
		where tmp.orgID = @minOrgID
		and qi.queueStatusID = @statusProcessing

		SELECT @minOrgID = min(tmp.orgID) 
			FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
			INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
			WHERE tmp.memberID is null 
			AND tmp.conditionID is null		
			AND qi.queueStatusID = @statusGrabbed
			AND tmp.orgID > @minOrgID
	END
END

-- process MEMBERID only records : if entry for orgid and memberID, process these and delete any finer items.
IF EXISTS (
	SELECT top 1 tmp.itemUID 
	FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
	INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
	WHERE tmp.memberID is not null 
	AND tmp.conditionID is null		
	AND qi.queueStatusID = @statusGrabbed)
BEGIN
	select @minOrgID = null
	SELECT @minOrgID = min(tmp.orgID)
		FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
		INNER JOIN platformQueue.dbo.tblQueueItems_processMemberGroups as qid WITH(NOLOCK) on qid.itemUID = qi.itemUID
		INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
		WHERE tmp.memberID is not null 
		AND tmp.conditionID is null		
		AND qi.queueStatusID = @statusGrabbed
	while @minOrgID is not null BEGIN

		-- get members for org
		insert into #tmpItemSpecific (itemID)
		select distinct memberid
		FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
		INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
		where tmp.orgID = @minOrgID
		and tmp.memberid is not null
		and tmp.conditionid is null
		AND qi.queueStatusID = @statusGrabbed

		-- mark all rows having the same org/member as processing
		update qi WITH(UPDLOCK, HOLDLOCK)
		set qi.queueStatusID = @statusProcessing,
			qi.dateUpdated = getdate()
		FROM platformQueue.dbo.tblQueueItems as qi
		INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
		INNER JOIN #tmpItemSpecific as tmpM on tmpM.itemID = tmp.memberID
		where tmp.orgID = @minOrgID
		and qi.queueStatusID = @statusGrabbed

		SET @itemIDList = null
		SELECT @itemIDList = COALESCE(@itemIDList + ',', '') + cast(itemID as varchar(10)) from #tmpItemSpecific
		insert into #tmpMCNextCache (memberID) 
		exec membercentral.dbo.cache_members_populateMemberConditionCache @orgID=@minOrgID, @conditionIDList=null, @memberIDList=@itemIDList, 
				@processImmediateOnly=1, @itemGroupUID=@itemGroupUID, @logTreeID=@logTreeID

		insert into platformQueue.dbo.tblQueueItems_processMemberGroups (itemUID, itemGroupUID, orgID, memberID)
			OUTPUT inserted.itemUID, inserted.dateAdded, @statusReadyPopMemGroups 
			INTO platformQueue.dbo.tblQueueItems(itemUID, dateAdded, queueStatusID)
		select NEWID() as itemUID, @itemGroupUID as itemGroupUID, @minOrgID as orgID, tmp.memberID
		from #tmpMCNextCache as tmp

		delete from #tmpMCNextCache

		-- mark all rows having the same org/member as done
		update qi WITH(UPDLOCK, HOLDLOCK)
		set qi.queueStatusID = @statusDone,
			qi.dateUpdated = getdate()
		FROM platformQueue.dbo.tblQueueItems as qi
		INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
		INNER JOIN #tmpItemSpecific as tmpM on tmpM.itemID = tmp.memberID
		where tmp.orgID = @minOrgID
		and qi.queueStatusID = @statusProcessing

		delete from #tmpItemSpecific

		SELECT @minOrgID = min(tmp.orgID) 
			FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
			INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
			WHERE tmp.memberID is not null 
			AND tmp.conditionID is null		
			AND qi.queueStatusID = @statusGrabbed
			AND tmp.orgID > @minOrgID
	END
END

-- process CONDITIONID only records : if entry for orgid and conditionID, process these and delete any finer items.
IF EXISTS (
	SELECT top 1 tmp.itemUID 
	FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
	INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
	WHERE tmp.memberID is null 
	AND tmp.conditionID is not null		
	AND qi.queueStatusID = @statusGrabbed)
BEGIN
	select @minOrgID = null
	SELECT @minOrgID = min(tmp.orgID)
		FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
		INNER JOIN platformQueue.dbo.tblQueueItems_processMemberGroups as qid WITH(NOLOCK) on qid.itemUID = qi.itemUID
		INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
		WHERE tmp.memberID is null 
		AND tmp.conditionID is not null		
		AND qi.queueStatusID = @statusGrabbed
	while @minOrgID is not null BEGIN

		-- get conditions for org
		insert into #tmpItemSpecific (itemID)
		select distinct conditionID
		FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
		INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
		where tmp.orgID = @minOrgID
		and tmp.memberid is null
		and tmp.conditionid is not null
		AND qi.queueStatusID = @statusGrabbed

		-- mark all rows having the same org/condition as processing
		update qi WITH(UPDLOCK, HOLDLOCK)
		set qi.queueStatusID = @statusProcessing,
			qi.dateUpdated = getdate()
		FROM platformQueue.dbo.tblQueueItems as qi
		INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
		INNER JOIN #tmpItemSpecific as tmpC on tmpC.itemID = tmp.conditionID
		where tmp.orgID = @minOrgID
		and qi.queueStatusID = @statusGrabbed

		SET @itemIDList = null
		SELECT @itemIDList = COALESCE(@itemIDList + ',', '') + cast(itemID as varchar(10)) from #tmpItemSpecific
		insert into #tmpMCNextCache (memberID) 
		exec membercentral.dbo.cache_members_populateMemberConditionCache @orgID=@minOrgID, @conditionIDList=@itemIDList, @memberIDList=null, 
				@processImmediateOnly=1, @itemGroupUID=@itemGroupUID, @logTreeID=@logTreeID

		insert into platformQueue.dbo.tblQueueItems_processMemberGroups (itemUID, itemGroupUID, orgID, memberID)
			OUTPUT inserted.itemUID, inserted.dateAdded, @statusReadyPopMemGroups 
			INTO platformQueue.dbo.tblQueueItems(itemUID, dateAdded, queueStatusID)
		select NEWID() as itemUID, @itemGroupUID as itemGroupUID, @minOrgID as orgID, tmp.memberID
		from #tmpMCNextCache as tmp

		delete from #tmpMCNextCache

		-- mark all rows having the same org/member as done
		update qi WITH(UPDLOCK, HOLDLOCK)
		set qi.queueStatusID = @statusDone,
			qi.dateUpdated = getdate()
		FROM platformQueue.dbo.tblQueueItems as qi
		INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
		INNER JOIN #tmpItemSpecific as tmpC on tmpC.itemID = tmp.conditionID
		where tmp.orgID = @minOrgID
		and qi.queueStatusID = @statusProcessing

		delete from #tmpItemSpecific

		SELECT @minOrgID = min(tmp.orgID) 
			FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
			INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
			WHERE tmp.memberID is null 
			AND tmp.conditionID is not null		
			AND qi.queueStatusID = @statusGrabbed
			AND tmp.orgID > @minOrgID
	END
END

-- process remaining MEMBERID/CONDITIONID records by member
select @minMemberID = null, @minOrgID = null
select @minMemberID = min(tmp.memberID)
	FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
	INNER JOIN platformQueue.dbo.tblQueueItems_processMemberGroups as qid WITH(NOLOCK) on qid.itemUID = qi.itemUID
	INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
	WHERE tmp.memberID is not null 
	AND tmp.conditionID is not null
	AND qi.queueStatusID = @statusGrabbed
while @minMemberID is not null BEGIN
	select top 1 @minOrgID = orgID from #tmpItems where memberID = @minMemberID

	-- get conditions for members
	insert into #tmpItemSpecific (itemID)
	select distinct conditionID
	FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
	INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
	where tmp.memberID = @minMemberID
	and tmp.conditionid is not null
	AND qi.queueStatusID = @statusGrabbed

	-- mark all rows having the same member/condition as processing
	update qi WITH(UPDLOCK, HOLDLOCK)
	set qi.queueStatusID = @statusProcessing,
		qi.dateUpdated = getdate()
	FROM platformQueue.dbo.tblQueueItems as qi
	INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
	INNER JOIN #tmpItemSpecific as tmpC on tmpC.itemID = tmp.conditionID
	where tmp.memberID = @minMemberID
	and qi.queueStatusID = @statusGrabbed

	SET @itemIDList = null
	SELECT @itemIDList = COALESCE(@itemIDList + ',', '') + cast(itemID as varchar(10)) from #tmpItemSpecific
	insert into #tmpMCNextCache (memberID) 
	exec membercentral.dbo.cache_members_populateMemberConditionCache @orgID=@minOrgID, @conditionIDList=@itemIDList, @memberIDList=@minMemberID, 
			@processImmediateOnly=1, @itemGroupUID=@itemGroupUID, @logTreeID=@logTreeID

	insert into platformQueue.dbo.tblQueueItems_processMemberGroups (itemUID, itemGroupUID, orgID, memberID)
		OUTPUT inserted.itemUID, inserted.dateAdded, @statusReadyPopMemGroups 
		INTO platformQueue.dbo.tblQueueItems(itemUID, dateAdded, queueStatusID)
	select NEWID() as itemUID, @itemGroupUID as itemGroupUID, @minOrgID as orgID, tmp.memberID
	from #tmpMCNextCache as tmp

	delete from #tmpMCNextCache

	-- mark all rows having the same org/member as done
	update qi WITH(UPDLOCK, HOLDLOCK)
	set qi.queueStatusID = @statusDone,
		qi.dateUpdated = getdate()
	FROM platformQueue.dbo.tblQueueItems as qi
	INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
	INNER JOIN #tmpItemSpecific as tmpC on tmpC.itemID = tmp.conditionID
	where tmp.memberID = @minMemberID
	and qi.queueStatusID = @statusProcessing

	delete from #tmpItemSpecific

	select @minMemberID = min(tmp.memberID)
		FROM platformQueue.dbo.tblQueueItems as qi WITH(NOLOCK)
		INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
		WHERE tmp.memberID is not null 
		AND tmp.conditionID is not null
		AND qi.queueStatusID = @statusGrabbed
		AND tmp.memberID > @minMemberID
END

-- clear all entries marked as done
BEGIN TRY
	DELETE from platformQueue.dbo.tblQueueItems
	where queueStatusID = @statusDone
END TRY
BEGIN CATCH
	-- do nothing. it will be cleaned up in a subsequent call
END CATCH

select @totalID = count(*) from #tmpItems

-- cleanup tables
IF OBJECT_ID('tempdb..#tmpItemSpecific') IS NOT NULL 
	DROP TABLE #tmpItemSpecific
IF OBJECT_ID('tempdb..#tmpItems') IS NOT NULL 
	DROP TABLE #tmpItems
IF OBJECT_ID('tempdb..#tmpMCNextCache') IS NOT NULL 
	DROP TABLE #tmpMCNextCache

-- Log
set @totalMS = Datediff(ms,@starttime,getdate())
insert into dbo.tblQueueLog_processMemberGroups ([procedure], logDate, totalMS, totalID, itemGroupUID)
values (OBJECT_NAME(@@PROCID), @starttime, @totalMS, @totalID, @itemGroupUID)

INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End Process of ' + cast(@itemGroupUID as varchar(60)), @totalMS);

RETURN 0
GO

USE [platformQueue]
GO
ALTER PROC [dbo].[sb_ProcessMemberGroupsQueueActivated]
AS

DECLARE @dlgID uniqueidentifier
DECLARE @mt nvarchar(256)
DECLARE @msg xml
DECLARE @itemGroupUID uniqueidentifier;
DECLARE @starttime datetime

DECLARE @smtpserver varchar(20), @tier varchar(20), @errorSubject varchar(100), @errmsg nvarchar(2048), @crlf varchar(10)
SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'					
SET @smtpserver = '10.36.18.90'
IF @@SERVERNAME = 'DEV04\PLATFORM2008' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'DEV03\PLATFORM2008' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'STAGING01\PLATFORM2008' BEGIN
	SET @tier = 'STAGING'
	SET @smtpserver = 'mail.trialsmith.com'
END
SET @errorSubject = @tier + ': Error Processing Member Groups'

WHILE 1 = 1
BEGIN

	WAITFOR (
		RECEIVE TOP(1) 
			@dlgID = conversation_handle,
			@mt = message_type_name,
			@msg = cast(message_body as xml)
		FROM dbo.ProcessMemberGroupsQueue
		), TIMEOUT 1000;

	IF @@ROWCOUNT = 0
		BREAK;

	IF @mt = N'PlatformQueue/ProcessMemberGroupsRequestMessage'
	BEGIN
		-- Log
		set @starttime = getdate()
		INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, QueueMessage)
		VALUES (@dlgID, OBJECT_NAME(@@PROCID), 'Start Process Message', @msg);

		BEGIN TRY
			SELECT @itemGroupUID = @msg.value('(/itemGroupUID)[1]','uniqueidentifier')
			IF @itemGroupUID is not null
				EXEC dbo.queue_processMemberGroups_process @itemGroupUID=@itemGroupUID, @logTreeID=@dlgID

			END CONVERSATION @dlgID;
		END TRY
		BEGIN CATCH
			-- Log
			INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorSeverity, ErrorMessage, ErrorLine, ErrorProc)
			VALUES (@dlgID, OBJECT_NAME(@@PROCID), ERROR_SEVERITY(), ERROR_MESSAGE(), ERROR_LINE(), ERROR_PROCEDURE());

			-- if deadlock, put itemGroupUID back into the queue as ready to run again
			IF @itemGroupUID is not null and 
				(
				ERROR_MESSAGE() like '%Errno 1205:%'
				OR 
				ERROR_MESSAGE() like '%Violation of UNIQUE KEY constraint ''IX_cache_members_groups''.%'
				) 
				BEGIN TRY				
					EXEC dbo.queue_processMemberGroups_requeue @itemGroupUID=@itemGroupUID, @logTreeID=@dlgID
				END TRY
				BEGIN CATCH
					BEGIN TRY					
						SET @errmsg = @errorSubject + @crlf + @crlf + 
										ERROR_MESSAGE() + @crlf + @crlf + 
										'LogTreeID=' + cast(@dlgID as varchar(60)) + @crlf + @crlf + 
										'Unable to requeue. After addressing error, run: ' + @crlf + 
										'EXEC platformQueue.dbo.queue_processMemberGroups_requeue @itemGroupUID=''' + cast(@itemGroupUID as varchar(60)) + ''', @logTreeID=''' + cast(@dlgID as varchar(60)) + ''';'

						EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
							@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
							@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
					END TRY
					BEGIN CATCH
						-- do nothing
					END CATCH
				END CATCH
			ELSE 
			BEGIN
				BEGIN TRY
					SET @errmsg = @errorSubject + @crlf + @crlf + 
									ERROR_MESSAGE() + @crlf + @crlf + 
									'LogTreeID=' + cast(@dlgID as varchar(60)) + @crlf + @crlf + 
									'After addressing error, run: ' + @crlf + 
									'EXEC platformQueue.dbo.queue_processMemberGroups_requeue @itemGroupUID=''' + cast(@itemGroupUID as varchar(60)) + ''', @logTreeID=''' + cast(@dlgID as varchar(60)) + ''';'

					EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
						@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
						@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
				END TRY
				BEGIN CATCH
					-- do nothing
				END CATCH
			END

			END CONVERSATION @dlgID;
		END CATCH;

		-- log
		INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
		VALUES (@dlgID, OBJECT_NAME(@@PROCID), 'End Process Message', Datediff(ms,@starttime,getdate()));
	END
	ELSE
		IF @mt = N'http://schemas.microsoft.com/SQL/ServiceBroker/EndDialog' OR @mt = N'http://schemas.microsoft.com/SQL/ServiceBroker/Error'
			END CONVERSATION @dlgID;
		ELSE
		BEGIN
			--anything other than user type or EndDialog is an unexpected error
			-- Log
			INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
			VALUES (@dlgID, OBJECT_NAME(@@PROCID), 'Unexpected message type received - ' + @mt);

			END CONVERSATION @dlgID;
		END

END;
GO

USE [platformQueue]
GO
ALTER PROC [dbo].[job_popMemGrp_process]
@itemGroupUID uniqueidentifier,
@logTreeID uniqueidentifier

AS

SET NOCOUNT ON

IF @itemGroupUID is null
	RETURN -1

declare @starttime datetime, @starttime2 datetime, @totalMS int, @totalID int
declare @jobUID uniqueidentifier
declare @procname varchar(100)
set @starttime = getdate()
set @jobUID = NEWID()

-- Log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start Process of ' + cast(@itemGroupUID as varchar(60)));

declare @statusReady int, @statusGrabbed int, @statusProcessing int, @statusDone int
select @statusReady = qs.queueStatusID 
	from platformQueue.dbo.tblQueueStatuses as qs
	inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popMemGroups'
	and qs.queueStatus = 'readyToProcess'
select @statusGrabbed = qs.queueStatusID 
	from platformQueue.dbo.tblQueueStatuses as qs
	inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popMemGroups'
	and qs.queueStatus = 'grabbedForProcessing'
select @statusProcessing = qs.queueStatusID 
	from platformQueue.dbo.tblQueueStatuses as qs
	inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popMemGroups'
	and qs.queueStatus = 'processingItem'
select @statusDone = qs.queueStatusID 
	from platformQueue.dbo.tblQueueStatuses as qs
	inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popMemGroups'
	and qs.queueStatus = 'done'

IF OBJECT_ID('tempdb..#tmpItems') IS NOT NULL 
	DROP TABLE #tmpItems
IF OBJECT_ID('tempdb..#tblRGM') IS NOT NULL
	DROP TABLE #tblRGM
IF OBJECT_ID('tempdb..#tblmultiCondRules') IS NOT NULL
	DROP TABLE #tblmultiCondRules
IF OBJECT_ID('tempdb..#tmpNewMemberGroups') IS NOT NULL
	DROP TABLE #tmpNewMemberGroups
IF OBJECT_ID('tempdb..#tmpNewMemberGroups2') IS NOT NULL
	DROP TABLE #tmpNewMemberGroups2
IF OBJECT_ID('tempdb..#tmpCurrentMemberGroups') IS NOT NULL
	DROP TABLE #tmpCurrentMemberGroups
IF OBJECT_ID('tempdb..#tmpDelete') IS NOT NULL
	DROP TABLE #tmpDelete
IF OBJECT_ID('tempdb..#tmpInsert') IS NOT NULL
	DROP TABLE #tmpInsert
IF OBJECT_ID('tempdb..#membersToUpdate') IS NOT NULL
	DROP TABLE #membersToUpdate

CREATE TABLE #tmpItems (itemUID uniqueidentifier, orgID int, memberID int)
CREATE TABLE #tblRGM (memberID int, groupID int);
CREATE TABLE #tblmultiCondRules (orgID int, ruleID int PRIMARY KEY, ruleSQL varchar(max));
CREATE TABLE #tmpNewMemberGroups (orgID int, memberID int, groupID int, isManualDirect tinyint, isManualIndirect tinyint, isVirtualDirect tinyint, isVirtualIndirect tinyint)
CREATE TABLE #tmpNewMemberGroups2 (autoid int IDENTITY(1,1), orgID int, memberID int, groupID int, isManualDirect bit, isManualIndirect bit, isVirtualDirect bit, isVirtualIndirect bit)
CREATE TABLE #tmpCurrentMemberGroups (autoid int, orgid int, memberid int, groupid int, isManualDirect bit, isManualIndirect bit, isVirtualDirect bit, isVirtualIndirect bit)
CREATE TABLE #tmpDelete (autoid int, memberid int, groupID int)
CREATE TABLE #tmpInsert (orgID int, memberID int, groupID int, isManualDirect bit, isManualIndirect bit, isVirtualDirect bit, isVirtualIndirect bit)
CREATE TABLE #membersToUpdate (memberID int)


/* ********************************** */
/* dequeue items ready for processing */
/* ********************************** */
UPDATE qi WITH (UPDLOCK, READPAST)
SET qi.queueStatusID = @statusProcessing,
	qi.dateUpdated = getdate(),
	qi.jobUID = @jobUID,
	qi.jobDateStarted = getdate(),
	qi.jobServerID = 0
	OUTPUT inserted.itemUID, qid.orgID, qid.memberID
	INTO #tmpItems
FROM dbo.tblQueueItems as qi
INNER JOIN dbo.tblQueueItems_processMemberGroups as qid ON qid.itemUID = qi.itemUID
WHERE qi.queueStatusID = @statusReady
and qid.itemGroupUID = @itemGroupUID


-- indexes for speed
create index tmp_orgID on #tmpItems (orgID);
create index tmp_MemberID on #tmpItems (memberID);


/* *********************************** */
/* get virtual assignments for members */
/* *********************************** */
-- Log
set @starttime2 = getdate()
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start one-condition rules of ' + cast(@itemGroupUID as varchar(60)));

-- process in mass all one-condition rules
; WITH all1CondRules AS (
	select r.orgID, r.ruleXML.value('(//condition/@id)[1]','uniqueidentifier') as conditionUID, rg.groupID
	from membercentral.dbo.ams_virtualGroupRules as r WITH(NOLOCK)
	inner join membercentral.dbo.ams_virtualGroupRuleGroups as rg WITH(NOLOCK) on rg.ruleID = r.ruleID
	where r.orgID in (select distinct orgID from #tmpItems)
	and r.isActive = 1
	and r.ruleTypeID = 1
	and r.ruleXML.value('count(//condition)','int') = 1
)
insert into #tblRGM (memberid, groupID)
select distinct cmc.memberid, vg.groupID
from #tmpItems as tmp
inner join all1CondRules as vg on vg.orgID = tmp.orgID
inner join membercentral.dbo.ams_virtualGroupConditions as vgc WITH(NOLOCK) on vgc.uid = vg.conditionUID
inner join membercentral.dbo.cache_members_conditions as cmc WITH(NOLOCK) on cmc.conditionID = vgc.conditionID
	and cmc.memberid = tmp.memberID
inner join membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = cmc.memberid
	and m.memberid = m.activeMemberID
	and m.status <> 'D'
OPTION (RECOMPILE)

-- log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End one-condition rules of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));

-- Log
set @starttime2 = getdate()
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start multi-condition rules of ' + cast(@itemGroupUID as varchar(60)));

-- loop over multi-condition rules with conditions tied to these members
declare @orgID int, @minRuleID int, @ruleSQL varchar(max), @dynsql nvarchar(max)

insert into #tblmultiCondRules (orgID, ruleID, ruleSQL)
select distinct r.orgID, r.ruleID, r.ruleSQL
from membercentral.dbo.ams_virtualGroupRules as r WITH(NOLOCK)
inner join membercentral.dbo.ams_virtualGroupRuleGroups as rg WITH(NOLOCK) on rg.ruleID = r.ruleID
INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.groupID = rg.groupid and g.status <> 'D'
CROSS APPLY r.ruleXML.nodes('//condition') as C(condition)
inner join membercentral.dbo.ams_virtualGroupConditions as vgc WITH(NOLOCK) on vgc.uid = C.condition.value('@id','uniqueidentifier')
inner join membercentral.dbo.cache_members_conditions as cmc WITH(NOLOCK) on cmc.conditionID = vgc.conditionID
inner join #tmpItems as tmp on tmp.memberID = cmc.memberID
where r.isActive = 1
and r.ruleTypeID = 1
and r.ruleXML.value('count(//condition)','int') > 1
and len(r.ruleSQL) > 0
OPTION (RECOMPILE)

select @minRuleID = min(ruleID) from #tblmultiCondRules
while @minRuleID is not null BEGIN
	select @ruleSQL = ruleSQL, @orgID = orgID from #tblmultiCondRules where ruleID = @minRuleID
	select @dynsql = ''
	select @dynsql = '
			use membercentral;
			insert into #tblRGM (memberid, groupID)
			select distinct m.memberID, rg.groupID
			from membercentral.dbo.ams_members as m WITH(NOLOCK)
			inner join #tmpItems as tmp on tmp.memberID = m.memberID 
			inner join membercentral.dbo.ams_virtualGroupRules as r WITH(NOLOCK) on r.ruleID = ' + cast(@minRuleID as varchar(10)) + '
			inner join membercentral.dbo.ams_virtualGroupRuleGroups as rg WITH(NOLOCK) on rg.ruleID = r.ruleID
			INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.groupID = rg.groupid and g.status <> ''D''
			where m.orgID = ' + cast(@orgID as varchar(10)) + ' 
			and m.memberid = m.activeMemberID
			and m.status <> ''D''
			and (' + @ruleSQL + ')
			'
	exec sp_executesql @dynsql

	select @minRuleID = min(ruleID) from #tblmultiCondRules where ruleID > @minRuleID
END

-- log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End multi-condition rules of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));

create index RGM_GroupID on #tblRGM (groupID);

INSERT INTO #tmpNewMemberGroups (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
select DISTINCT rg.orgID, dg.memberID, rg.groupID, 0, 0, isVirtualDirect = rg.isBaseGroup, isVirtualIndirect = (~rg.isBaseGroup)
FROM #tblRGM as dg
INNER JOIN membercentral.dbo.cache_ams_recursiveGroups as rg WITH(NOLOCK) ON dg.groupid = rg.startGroupid
OPTION (RECOMPILE)

/* ********************************** */
/* get manual assignments for members */
/* ********************************** */
-- Log
set @starttime2 = getdate()
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start manual assignment of ' + cast(@itemGroupUID as varchar(60)));

-- you could be directly assigned and indirectly assigned to the same group.
INSERT INTO #tmpNewMemberGroups (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
select distinct tmp.orgID, tmp.memberID, g.groupID, isManualDirect = rg.isBaseGroup, isManualIndirect = (~rg.isBaseGroup), 0, 0
from #tmpItems as tmp
inner join membercentral.dbo.ams_memberGroups AS mg WITH(NOLOCK) on mg.memberID = tmp.memberID
INNER JOIN membercentral.dbo.cache_ams_recursiveGroups as rg WITH(NOLOCK) ON mg.groupid = rg.startGroupid
INNER JOIN membercentral.dbo.ams_groups AS g WITH(NOLOCK) ON g.groupID = rg.groupID
INNER JOIN membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = mg.memberID
where g.status <> 'D'
AND m.memberID = m.activeMemberID
AND m.status <> 'D'

-- public group for all members
INSERT INTO #tmpNewMemberGroups (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
SELECT distinct tmp.orgID, tmp.memberID, g.groupID, 0, 0, 0, 1
FROM #tmpItems as tmp
INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.orgID = tmp.orgID
INNER JOIN membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = tmp.memberID and m.orgID = g.orgID
WHERE g.isSystemGroup = 1
AND g.groupName = 'Public'
AND g.status = 'A'
AND m.memberid = m.activeMemberID
AND m.status <> 'D'
	union all
SELECT distinct tmp.orgID, 0 as memberID, g.groupID, 0, 0, 0, 1
FROM #tmpItems as tmp
INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.orgID = tmp.orgID
WHERE g.isSystemGroup = 1
AND g.groupName = 'Public'
AND g.status = 'A'

-- guest group for all guests
INSERT INTO #tmpNewMemberGroups (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
SELECT distinct tmp.orgID, tmp.memberID, g.groupID, 0, 0, 0, 1
FROM #tmpItems as tmp
INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.orgID = tmp.orgID
INNER JOIN membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = tmp.memberID and m.orgID = g.orgID
INNER JOIN membercentral.dbo.ams_memberTypes as mt WITH(NOLOCK) on mt.memberTypeID = m.memberTypeID
WHERE g.isSystemGroup = 1
AND g.groupName = 'Guests'
AND g.status = 'A'
AND m.memberid = m.activeMemberID
AND m.status <> 'D'
AND mt.memberType = 'Guest'

-- users group for all users
INSERT INTO #tmpNewMemberGroups (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
SELECT distinct tmp.orgID, tmp.memberID, g.groupID, 0, 0, 0, 1
FROM #tmpItems as tmp
INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.orgID = tmp.orgID
INNER JOIN membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = tmp.memberID and m.orgID = g.orgID
INNER JOIN membercentral.dbo.ams_memberTypes as mt WITH(NOLOCK) on mt.memberTypeID = m.memberTypeID
WHERE g.isSystemGroup = 1
AND g.groupName = 'Users'
AND g.status = 'A'
AND m.memberid = m.activeMemberID
AND m.status <> 'D'
AND mt.memberType = 'User'

-- new member groups compacted
INSERT INTO #tmpNewMemberGroups2 (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
select orgID, memberID, groupID, 
	max(isManualDirect) as isManualDirect, 
	max(isManualIndirect) as isManualIndirect, 
	max(isVirtualDirect) as isVirtualDirect, 
	max(isVirtualIndirect) as isVirtualIndirect
from #tmpNewMemberGroups
group by orgID, memberID, groupID

-- log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End manual assignment of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));


/* ******************** */
/* Modify member groups */
/* ******************** */
-- current member groups
INSERT INTO #tmpCurrentMemberGroups (autoid, orgid, memberid, groupid, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
select mg.autoid, mg.orgid, mg.memberid, mg.groupid, mg.isManualDirect, mg.isManualIndirect, mg.isVirtualDirect, mg.isVirtualIndirect
from #tmpItems as tmp
inner join membercentral.dbo.cache_members_groups as mg WITH(NOLOCK) on mg.memberID = tmp.memberID
	union
select mg.autoid, mg.orgid, mg.memberid, mg.groupid, mg.isManualDirect, mg.isManualIndirect, mg.isVirtualDirect, mg.isVirtualIndirect
from #tmpItems as tmp
inner join membercentral.dbo.cache_members_groups as mg WITH(NOLOCK) on mg.orgID = tmp.orgID and mg.memberID = 0

-- rows to be deleted: #tmpCurrentMemberGroups but not in #tmpNewMemberGroups2
-- also delete rows where memberID is no longer the activeMemberID (for cleanup)
INSERT INTO #tmpDelete (autoID, memberID, groupID)
SELECT curr.autoid, curr.memberID, curr.groupID
from #tmpCurrentMemberGroups as curr
where not exists (
	select autoid 
	from #tmpNewMemberGroups2
	where orgID = curr.orgID
	and memberID = curr.memberID
	and groupID = curr.groupID
	and isManualDirect = curr.isManualDirect
	and isManualIndirect = curr.isManualIndirect
	and isVirtualDirect = curr.isVirtualDirect
	and isVirtualIndirect = curr.isVirtualIndirect
)
	union
select mg.autoid, mg.memberID, mg.groupID
from #tmpItems as tmp
inner join membercentral.dbo.cache_members_groups as mg WITH(NOLOCK) on mg.memberID = tmp.memberID
inner join membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = mg.memberID
where m.memberID <> m.activeMemberID
or m.status = 'D'

-- rows to be added: mem/grp in #tmpNewMemberGroups2 but not in #tmpCurrentMemberGroups
INSERT INTO #tmpInsert (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
SELECT distinct new.orgid, new.memberid, new.groupid, new.isManualDirect, new.isManualIndirect, new.isVirtualDirect, new.isVirtualIndirect
from #tmpNewMemberGroups2 as new
where not exists (
	select autoid
	from #tmpCurrentMemberGroups
	where orgID = new.orgID
	and memberID = new.memberID
	and groupID = new.groupID
	and isManualDirect = new.isManualDirect
	and isManualIndirect = new.isManualIndirect
	and isVirtualDirect = new.isVirtualDirect
	and isVirtualIndirect = new.isVirtualIndirect
)

-- Log
set @starttime2 = getdate()
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start changes to real tables of ' + cast(@itemGroupUID as varchar(60)));

-- delete rows that shouldnt be there anymore
DELETE FROM vw
FROM membercentral.dbo.cache_members_groups as vw
INNER JOIN #tmpDelete as del on del.autoID = vw.autoID

-- add rows that should be there now
INSERT INTO membercentral.dbo.cache_members_groups (orgid, memberid, groupid, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
SELECT distinct orgid, memberid, groupid, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect
FROM #tmpInsert

-- log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End changes to real tables of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));


/* ******************* */
/* Modify group prints */
/* ******************* */
-- Log
set @starttime2 = getdate()
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start changes to group prints of ' + cast(@itemGroupUID as varchar(60)));

-- update group prints
insert into #membersToUpdate (memberID)
select d.memberID 
from #tmpDelete as d
left outer join #tmpInsert as i on i.memberid = d.memberid and i.groupid = d.groupID
where i.orgid is null
	union 
select i.memberID 
from #tmpInsert as i
left outer join #tmpDelete as d on i.memberid = d.memberid and i.groupid = d.groupID
where d.autoID is null

select @totalID = count(*) from #membersToUpdate

if exists (select top 1 memberID from #membersToUpdate) BEGIN
	CREATE INDEX IX_membersToUpdate_MID ON #membersToUpdate (memberID); 
	
	select @orgID = null
	select @orgID = min(orgID) from #tmpItems
	while @orgID is not null BEGIN
		exec membercentral.dbo.cache_perms_updateGroupPrintsForMembersBulk @orgid=@orgID
		exec membercentral.dbo.ams_createMemberSiteDefaultsByOrgID @orgID=@orgid
		select @orgID = min(orgID) from #tmpItems where orgID > @orgID
	end
END

-- log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End changes to group prints of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));


/* **************** */
/* mark all as done */
/* **************** */
update qi WITH (UPDLOCK, HOLDLOCK)
set qi.queueStatusID = @statusDone,
	qi.dateUpdated = getdate()
FROM platformQueue.dbo.tblQueueItems as qi
INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
where qi.queueStatusID = @statusProcessing

-- clear all entries marked as done
BEGIN TRY
	DELETE from platformQueue.dbo.tblQueueItems
	where queueStatusID = @statusDone
END TRY
BEGIN CATCH
	-- do nothing. it will be cleaned up in a subsequent call
END CATCH

select @totalID = count(*) from #tmpItems

-- cleanup temp tables
IF OBJECT_ID('tempdb..#tmpItems') IS NOT NULL 
	DROP TABLE #tmpItems
IF OBJECT_ID('tempdb..#tblRGM') IS NOT NULL
	DROP TABLE #tblRGM
IF OBJECT_ID('tempdb..#tblmultiCondRules') IS NOT NULL
	DROP TABLE #tblmultiCondRules
IF OBJECT_ID('tempdb..#tmpNewMemberGroups') IS NOT NULL
	DROP TABLE #tmpNewMemberGroups
IF OBJECT_ID('tempdb..#tmpNewMemberGroups2') IS NOT NULL
	DROP TABLE #tmpNewMemberGroups2
IF OBJECT_ID('tempdb..#tmpCurrentMemberGroups') IS NOT NULL
	DROP TABLE #tmpCurrentMemberGroups
IF OBJECT_ID('tempdb..#tmpDelete') IS NOT NULL
	DROP TABLE #tmpDelete
IF OBJECT_ID('tempdb..#tmpInsert') IS NOT NULL
	DROP TABLE #tmpInsert
IF OBJECT_ID('tempdb..#membersToUpdate') IS NOT NULL
	DROP TABLE #membersToUpdate

-- Log
set @totalMS = Datediff(ms,@starttime,getdate())
insert into dbo.tblQueueLog_processMemberGroups ([procedure], logDate, totalMS, totalID, itemGroupUID)
values (OBJECT_NAME(@@PROCID), @starttime, @totalMS, @totalID, @itemGroupUID)

INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End Process of ' + cast(@itemGroupUID as varchar(60)), @totalMS);

RETURN 0
GO

USE [platformQueue]
GO
ALTER PROC [dbo].[job_popMemGrp_process]
@itemGroupUID uniqueidentifier,
@logTreeID uniqueidentifier

AS

SET NOCOUNT ON

IF @itemGroupUID is null
	RETURN -1

declare @starttime datetime, @starttime2 datetime, @totalMS int, @totalID int
declare @jobUID uniqueidentifier
declare @procname varchar(100)
set @starttime = getdate()
set @jobUID = NEWID()

-- Log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start Process of ' + cast(@itemGroupUID as varchar(60)));

declare @statusReady int, @statusGrabbed int, @statusProcessing int, @statusDone int
select @statusReady = qs.queueStatusID 
	from platformQueue.dbo.tblQueueStatuses as qs
	inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popMemGroups'
	and qs.queueStatus = 'readyToProcess'
select @statusGrabbed = qs.queueStatusID 
	from platformQueue.dbo.tblQueueStatuses as qs
	inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popMemGroups'
	and qs.queueStatus = 'grabbedForProcessing'
select @statusProcessing = qs.queueStatusID 
	from platformQueue.dbo.tblQueueStatuses as qs
	inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popMemGroups'
	and qs.queueStatus = 'processingItem'
select @statusDone = qs.queueStatusID 
	from platformQueue.dbo.tblQueueStatuses as qs
	inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popMemGroups'
	and qs.queueStatus = 'done'

IF OBJECT_ID('tempdb..#tmpItems') IS NOT NULL 
	DROP TABLE #tmpItems
IF OBJECT_ID('tempdb..#cache_ams_virtualGroupRuleInfo') IS NOT NULL
	DROP TABLE #cache_ams_virtualGroupRuleInfo
IF OBJECT_ID('tempdb..#cache_ams_virtualGroupRuleConditions') IS NOT NULL
	DROP TABLE #cache_ams_virtualGroupRuleConditions
IF OBJECT_ID('tempdb..#tblRGM') IS NOT NULL
	DROP TABLE #tblRGM
IF OBJECT_ID('tempdb..#tblmultiCondRules') IS NOT NULL
	DROP TABLE #tblmultiCondRules
IF OBJECT_ID('tempdb..#tmpNewMemberGroups') IS NOT NULL
	DROP TABLE #tmpNewMemberGroups
IF OBJECT_ID('tempdb..#tmpNewMemberGroups2') IS NOT NULL
	DROP TABLE #tmpNewMemberGroups2
IF OBJECT_ID('tempdb..#tmpCurrentMemberGroups') IS NOT NULL
	DROP TABLE #tmpCurrentMemberGroups
IF OBJECT_ID('tempdb..#tmpDelete') IS NOT NULL
	DROP TABLE #tmpDelete
IF OBJECT_ID('tempdb..#tmpInsert') IS NOT NULL
	DROP TABLE #tmpInsert
IF OBJECT_ID('tempdb..#membersToUpdate') IS NOT NULL
	DROP TABLE #membersToUpdate

CREATE TABLE #tmpItems (itemUID uniqueidentifier, orgID int, memberID int)
CREATE TABLE #cache_ams_virtualGroupRuleInfo (orgID int, ruleID int PRIMARY KEY, conditionCount int, usesOR bit, usesAND bit, usesExclude bit)
CREATE TABLE #cache_ams_virtualGroupRuleConditions (ruleID int, conditionID int)
CREATE TABLE #tblRGM (memberID int, groupID int);
CREATE TABLE #tblmultiCondRules (orgID int, ruleID int PRIMARY KEY, ruleSQL varchar(max));
CREATE TABLE #tmpNewMemberGroups (orgID int, memberID int, groupID int, isManualDirect tinyint, isManualIndirect tinyint, isVirtualDirect tinyint, isVirtualIndirect tinyint)
CREATE TABLE #tmpNewMemberGroups2 (autoid int IDENTITY(1,1), orgID int, memberID int, groupID int, isManualDirect bit, isManualIndirect bit, isVirtualDirect bit, isVirtualIndirect bit)
CREATE TABLE #tmpCurrentMemberGroups (autoid int, orgid int, memberid int, groupid int, isManualDirect bit, isManualIndirect bit, isVirtualDirect bit, isVirtualIndirect bit)
CREATE TABLE #tmpDelete (autoid int, memberid int, groupID int)
CREATE TABLE #tmpInsert (orgID int, memberID int, groupID int, isManualDirect bit, isManualIndirect bit, isVirtualDirect bit, isVirtualIndirect bit)
CREATE TABLE #membersToUpdate (memberID int)



/* ********************************** */
/* dequeue items ready for processing */
/* ********************************** */
UPDATE qi WITH (UPDLOCK, READPAST)
SET qi.queueStatusID = @statusProcessing,
	qi.dateUpdated = getdate(),
	qi.jobUID = @jobUID,
	qi.jobDateStarted = getdate(),
	qi.jobServerID = 0
	OUTPUT inserted.itemUID, qid.orgID, qid.memberID
	INTO #tmpItems
FROM dbo.tblQueueItems as qi
INNER JOIN dbo.tblQueueItems_processMemberGroups as qid ON qid.itemUID = qi.itemUID
WHERE qi.queueStatusID = @statusReady
and qid.itemGroupUID = @itemGroupUID


-- indexes for speed
create index tmp_orgID on #tmpItems (orgID);
create index tmp_MemberID on #tmpItems (memberID);


/* *********************************** */
/* get virtual assignments for members */
/* *********************************** */
-- Log
set @starttime2 = getdate()
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start create temp condition tables of ' + cast(@itemGroupUID as varchar(60)));

-- get rule info
insert into #cache_ams_virtualGroupRuleInfo (orgID, ruleID, conditionCount, usesOR, usesAND, usesExclude)
select r.orgID, r.ruleID, 
	min(r.ruleXML.value('count(//condition)','int')) as conditionCount,
	max(cast(r.ruleXML.exist('//conditionset[@op = "OR"]') as int)) as usesOR,
	max(cast(r.ruleXML.exist('//conditionset[@op = "AND"]') as int)) as usesAND,
	max(cast(r.ruleXML.exist('//conditionset[@act = "exclude"]') as int)) as usesExclude
from membercentral.dbo.ams_virtualGroupRules as r WITH(NOLOCK)
inner join membercentral.dbo.ams_virtualGroupRuleGroups as rg WITH(NOLOCK) on rg.ruleID = r.ruleID
INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.groupID = rg.groupid and g.status <> 'D'
cross apply r.ruleXML.nodes('//condition') as T(C)
where r.orgID in (select distinct orgID from #tmpItems)
and r.isActive = 1
and r.ruleTypeID = 1
group by r.orgID, r.ruleID

-- expand rules and conditions
insert into #cache_ams_virtualGroupRuleConditions (ruleID, conditionID)
select distinct r.ruleID, vc.conditionID
from membercentral.dbo.ams_virtualGroupRules as r WITH(NOLOCK)
inner join #cache_ams_virtualGroupRuleInfo as vgri on vgri.ruleID = r.ruleID
cross apply r.ruleXML.nodes('//condition') as T(C)
inner join membercentral.dbo.ams_virtualGroupConditions as vc WITH(NOLOCK) on vc.uid = C.value('@id', 'uniqueidentifier')

-- expand multi condition rules for looping. only get rules with conditions linked to members in tmpItems
insert into #tblmultiCondRules (orgID, ruleID, ruleSQL)
select distinct r.orgId, r.ruleID, r.ruleSQL
from #cache_ams_virtualGroupRuleInfo as vgri
inner join membercentral.dbo.ams_virtualGroupRules as r WITH(NOLOCK) on r.ruleID = vgri.ruleID
inner join #cache_ams_virtualGroupRuleConditions as rc on rc.ruleID = r.ruleID
inner join membercentral.dbo.cache_members_conditions as cmc WITH(NOLOCK) on cmc.conditionID = rc.conditionID
inner join #tmpItems as ti on ti.memberID = cmc.memberID and ti.orgID = vgri.orgID
where ((vgri.usesOR = 1 and vgri.usesAnd = 1) or vgri.usesExclude = 1)
and r.ruleSQL <> ''

-- log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End create temp condition tables of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));


-- process all one-condition rules
select @totalID = count(*) from #cache_ams_virtualGroupRuleInfo where conditionCount = 1
IF @totalID > 0 BEGIN
	-- Log
	set @starttime2 = getdate()
	INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
	VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start one-condition rules (' + cast(@totalID as varchar(10)) + ') of ' + cast(@itemGroupUID as varchar(60)));

	insert into #tblRGM (memberid, groupID)
	select distinct cmc.memberID, rg.groupID
	from #cache_ams_virtualGroupRuleInfo as vgri
	inner join #cache_ams_virtualGroupRuleConditions as rc on rc.ruleID = vgri.ruleID
	inner join membercentral.dbo.cache_members_conditions as cmc WITH(NOLOCK) on cmc.conditionID = rc.conditionID
	inner join #tmpItems as ti on ti.memberID = cmc.memberID and ti.orgID = vgri.orgID
	inner join membercentral.dbo.ams_virtualGroupRuleGroups as rg WITH(NOLOCK) on rg.ruleID = vgri.ruleID
	INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.groupID = rg.groupid and g.status <> 'D'
	inner join membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = cmc.memberid
	where vgri.conditionCount = 1
	and m.memberid = m.activeMemberID
	and m.status <> 'D'
	OPTION (RECOMPILE)

	-- log
	INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
	VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End one-condition rules (' + cast(@totalID as varchar(10)) + ') of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));
END


-- run all AND multi condition rules
select @totalID = count(*) from #cache_ams_virtualGroupRuleInfo where conditionCount > 1 and usesOR = 0 and usesAND = 1 and usesExclude = 0
IF @totalID > 0 BEGIN
	-- Log
	set @starttime2 = getdate()
	INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
	VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start multi-condition AND rules (' + cast(@totalID as varchar(10)) + ') of ' + cast(@itemGroupUID as varchar(60)));

	insert into #tblRGM (memberid, groupID)
	select distinct m.memberID, rg.groupID
	from #cache_ams_virtualGroupRuleInfo as vgri
	inner join #cache_ams_virtualGroupRuleConditions as rc on rc.ruleID = vgri.ruleID
	inner join membercentral.dbo.cache_members_conditions as cmc WITH(NOLOCK) on cmc.conditionID = rc.conditionID
	inner join #tmpItems as ti on ti.memberID = cmc.memberID and ti.orgID = vgri.orgID
	inner join membercentral.dbo.ams_virtualGroupRuleGroups as rg WITH(NOLOCK) on rg.ruleID = vgri.ruleID
	INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.groupID = rg.groupid and g.status <> 'D'
	inner join membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = cmc.memberid
	where vgri.conditionCount > 1
	and vgri.usesOR = 0
	and vgri.usesAND = 1
	and vgri.usesExclude = 0
	and m.memberid = m.activeMemberID
	and m.status <> 'D'
	group by vgri.ruleID, m.memberID, vgri.conditionCount, rg.groupID
	having count(*) = vgri.conditionCount
	OPTION (RECOMPILE)

	-- log
	INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
	VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End multi-condition AND rules (' + cast(@totalID as varchar(10)) + ') of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));
END


-- run all OR multi condition rules
select @totalID = count(*) from #cache_ams_virtualGroupRuleInfo where conditionCount > 1 and usesOR = 1 and usesAND = 0 and usesExclude = 0
IF @totalID > 0 BEGIN
	-- Log
	set @starttime2 = getdate()
	INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
	VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start multi-condition OR rules (' + cast(@totalID as varchar(10)) + ') of ' + cast(@itemGroupUID as varchar(60)));

	insert into #tblRGM (memberid, groupID)
	select distinct m.memberID, rg.groupID
	from #cache_ams_virtualGroupRuleInfo as vgri
	inner join #cache_ams_virtualGroupRuleConditions as rc on rc.ruleID = vgri.ruleID
	inner join membercentral.dbo.cache_members_conditions as cmc WITH(NOLOCK) on cmc.conditionID = rc.conditionID
	inner join #tmpItems as ti on ti.memberID = cmc.memberID and ti.orgID = vgri.orgID
	inner join membercentral.dbo.ams_virtualGroupRuleGroups as rg WITH(NOLOCK) on rg.ruleID = vgri.ruleID
	INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.groupID = rg.groupid and g.status <> 'D'
	inner join membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = cmc.memberid
	where vgri.conditionCount > 1
	and vgri.usesOR = 1
	and vgri.usesAND = 0
	and vgri.usesExclude = 0
	and m.memberid = m.activeMemberID
	and m.status <> 'D'
	OPTION (RECOMPILE)

	-- log
	INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
	VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End multi-condition OR rules (' + cast(@totalID as varchar(10)) + ') of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));
END


-- loop over multi-condition rules 
select @totalID = count(*) from #tblmultiCondRules
IF @totalID > 0 BEGIN
	-- Log
	set @starttime2 = getdate()
	INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
	VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start multi-condition rules (' + cast(@totalID as varchar(10)) + ') of ' + cast(@itemGroupUID as varchar(60)));

	declare @orgID int, @minRuleID int, @ruleSQL varchar(max), @SQLString nvarchar(max), @ParamDefinition nvarchar(100)
	set @ParamDefinition = N'@ruleID int, @orgID int'
	select @minRuleID = min(ruleID) from #tblmultiCondRules
	while @minRuleID is not null BEGIN
		select @ruleSQL = ruleSQL, @orgID = orgID from #tblmultiCondRules where ruleID = @minRuleID
		set @SQLString = ''
		set @SQLString = N'use membercentral; 
				insert into #tblRGM (memberid, groupID) 
				select distinct m.memberID, rg.groupID 
				from membercentral.dbo.ams_members as m WITH(NOLOCK) 
				inner join #tmpItems as tmp on tmp.memberID = m.memberID 
				inner join membercentral.dbo.ams_virtualGroupRules as r WITH(NOLOCK) on r.ruleID = @ruleID 
				inner join membercentral.dbo.ams_virtualGroupRuleGroups as rg WITH(NOLOCK) on rg.ruleID = r.ruleID 
				INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.groupID = rg.groupid and g.status <> ''D'' 
				where m.orgID = @orgID 
				and m.memberid = m.activeMemberID 
				and m.status <> ''D'' 
				and (' + @ruleSQL + ') 
				OPTION (RECOMPILE)'
		exec sp_executesql @SQLString, @ParamDefinition, @ruleID=@minRuleID, @orgID=@orgID

		select @minRuleID = min(ruleID) from #tblmultiCondRules where ruleID > @minRuleID
	END

	-- log
	INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
	VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End multi-condition rules (' + cast(@totalID as varchar(10)) + ') of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));
END


-- Log
set @starttime2 = getdate()
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start expand groups of ' + cast(@itemGroupUID as varchar(60)));

-- expand groups
create index RGM_GroupID on #tblRGM (groupID);

INSERT INTO #tmpNewMemberGroups (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
select DISTINCT rg.orgID, dg.memberID, rg.groupID, 0, 0, isVirtualDirect = rg.isBaseGroup, isVirtualIndirect = (~rg.isBaseGroup)
FROM #tblRGM as dg
INNER JOIN membercentral.dbo.cache_ams_recursiveGroups as rg WITH(NOLOCK) ON dg.groupid = rg.startGroupid
OPTION (RECOMPILE)

-- log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End expand groups of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));



/* ********************************** */
/* get manual assignments for members */
/* ********************************** */
-- Log
set @starttime2 = getdate()
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start manual assignment of ' + cast(@itemGroupUID as varchar(60)));

-- you could be directly assigned and indirectly assigned to the same group.
INSERT INTO #tmpNewMemberGroups (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
select distinct tmp.orgID, tmp.memberID, g.groupID, isManualDirect = rg.isBaseGroup, isManualIndirect = (~rg.isBaseGroup), 0, 0
from #tmpItems as tmp
inner join membercentral.dbo.ams_memberGroups AS mg WITH(NOLOCK) on mg.memberID = tmp.memberID
INNER JOIN membercentral.dbo.cache_ams_recursiveGroups as rg WITH(NOLOCK) ON mg.groupid = rg.startGroupid
INNER JOIN membercentral.dbo.ams_groups AS g WITH(NOLOCK) ON g.groupID = rg.groupID
INNER JOIN membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = mg.memberID
where g.status <> 'D'
AND m.memberID = m.activeMemberID
AND m.status <> 'D'

-- public group for all members
INSERT INTO #tmpNewMemberGroups (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
SELECT distinct tmp.orgID, tmp.memberID, g.groupID, 0, 0, 0, 1
FROM #tmpItems as tmp
INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.orgID = tmp.orgID
INNER JOIN membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = tmp.memberID and m.orgID = g.orgID
WHERE g.isSystemGroup = 1
AND g.groupName = 'Public'
AND g.status = 'A'
AND m.memberid = m.activeMemberID
AND m.status <> 'D'
	union all
SELECT distinct tmp.orgID, 0 as memberID, g.groupID, 0, 0, 0, 1
FROM #tmpItems as tmp
INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.orgID = tmp.orgID
WHERE g.isSystemGroup = 1
AND g.groupName = 'Public'
AND g.status = 'A'

-- guest group for all guests
INSERT INTO #tmpNewMemberGroups (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
SELECT distinct tmp.orgID, tmp.memberID, g.groupID, 0, 0, 0, 1
FROM #tmpItems as tmp
INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.orgID = tmp.orgID
INNER JOIN membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = tmp.memberID and m.orgID = g.orgID
INNER JOIN membercentral.dbo.ams_memberTypes as mt WITH(NOLOCK) on mt.memberTypeID = m.memberTypeID
WHERE g.isSystemGroup = 1
AND g.groupName = 'Guests'
AND g.status = 'A'
AND m.memberid = m.activeMemberID
AND m.status <> 'D'
AND mt.memberType = 'Guest'

-- users group for all users
INSERT INTO #tmpNewMemberGroups (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
SELECT distinct tmp.orgID, tmp.memberID, g.groupID, 0, 0, 0, 1
FROM #tmpItems as tmp
INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.orgID = tmp.orgID
INNER JOIN membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = tmp.memberID and m.orgID = g.orgID
INNER JOIN membercentral.dbo.ams_memberTypes as mt WITH(NOLOCK) on mt.memberTypeID = m.memberTypeID
WHERE g.isSystemGroup = 1
AND g.groupName = 'Users'
AND g.status = 'A'
AND m.memberid = m.activeMemberID
AND m.status <> 'D'
AND mt.memberType = 'User'

-- new member groups compacted
INSERT INTO #tmpNewMemberGroups2 (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
select orgID, memberID, groupID, 
	max(isManualDirect) as isManualDirect, 
	max(isManualIndirect) as isManualIndirect, 
	max(isVirtualDirect) as isVirtualDirect, 
	max(isVirtualIndirect) as isVirtualIndirect
from #tmpNewMemberGroups
group by orgID, memberID, groupID

-- log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End manual assignment of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));


/* ******************** */
/* Modify member groups */
/* ******************** */
-- current member groups
INSERT INTO #tmpCurrentMemberGroups (autoid, orgid, memberid, groupid, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
select mg.autoid, mg.orgid, mg.memberid, mg.groupid, mg.isManualDirect, mg.isManualIndirect, mg.isVirtualDirect, mg.isVirtualIndirect
from #tmpItems as tmp
inner join membercentral.dbo.cache_members_groups as mg WITH(NOLOCK) on mg.memberID = tmp.memberID
	union
select mg.autoid, mg.orgid, mg.memberid, mg.groupid, mg.isManualDirect, mg.isManualIndirect, mg.isVirtualDirect, mg.isVirtualIndirect
from #tmpItems as tmp
inner join membercentral.dbo.cache_members_groups as mg WITH(NOLOCK) on mg.orgID = tmp.orgID and mg.memberID = 0

-- rows to be deleted: #tmpCurrentMemberGroups but not in #tmpNewMemberGroups2
-- also delete rows where memberID is no longer the activeMemberID (for cleanup)
INSERT INTO #tmpDelete (autoID, memberID, groupID)
SELECT curr.autoid, curr.memberID, curr.groupID
from #tmpCurrentMemberGroups as curr
where not exists (
	select autoid 
	from #tmpNewMemberGroups2
	where orgID = curr.orgID
	and memberID = curr.memberID
	and groupID = curr.groupID
	and isManualDirect = curr.isManualDirect
	and isManualIndirect = curr.isManualIndirect
	and isVirtualDirect = curr.isVirtualDirect
	and isVirtualIndirect = curr.isVirtualIndirect
)
	union
select mg.autoid, mg.memberID, mg.groupID
from #tmpItems as tmp
inner join membercentral.dbo.cache_members_groups as mg WITH(NOLOCK) on mg.memberID = tmp.memberID
inner join membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = mg.memberID
where m.memberID <> m.activeMemberID
or m.status = 'D'

-- rows to be added: mem/grp in #tmpNewMemberGroups2 but not in #tmpCurrentMemberGroups
INSERT INTO #tmpInsert (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
SELECT distinct new.orgid, new.memberid, new.groupid, new.isManualDirect, new.isManualIndirect, new.isVirtualDirect, new.isVirtualIndirect
from #tmpNewMemberGroups2 as new
where not exists (
	select autoid
	from #tmpCurrentMemberGroups
	where orgID = new.orgID
	and memberID = new.memberID
	and groupID = new.groupID
	and isManualDirect = new.isManualDirect
	and isManualIndirect = new.isManualIndirect
	and isVirtualDirect = new.isVirtualDirect
	and isVirtualIndirect = new.isVirtualIndirect
)

-- Log
set @starttime2 = getdate()
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start changes to real tables of ' + cast(@itemGroupUID as varchar(60)));

-- delete rows that shouldnt be there anymore
DELETE FROM vw
FROM membercentral.dbo.cache_members_groups as vw
INNER JOIN #tmpDelete as del on del.autoID = vw.autoID

-- add rows that should be there now
INSERT INTO membercentral.dbo.cache_members_groups (orgid, memberid, groupid, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
SELECT distinct orgid, memberid, groupid, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect
FROM #tmpInsert

-- log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End changes to real tables of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));


/* ******************* */
/* Modify group prints */
/* ******************* */
-- Log
set @starttime2 = getdate()
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start changes to group prints of ' + cast(@itemGroupUID as varchar(60)));

-- update group prints
insert into #membersToUpdate (memberID)
select d.memberID 
from #tmpDelete as d
left outer join #tmpInsert as i on i.memberid = d.memberid and i.groupid = d.groupID
where i.orgid is null
	union 
select i.memberID 
from #tmpInsert as i
left outer join #tmpDelete as d on i.memberid = d.memberid and i.groupid = d.groupID
where d.autoID is null

select @totalID = count(*) from #membersToUpdate

if exists (select top 1 memberID from #membersToUpdate) BEGIN
	CREATE INDEX IX_membersToUpdate_MID ON #membersToUpdate (memberID); 
	
	select @orgID = null
	select @orgID = min(orgID) from #tmpItems
	while @orgID is not null BEGIN
		exec membercentral.dbo.cache_perms_updateGroupPrintsForMembersBulk @orgid=@orgID
		exec membercentral.dbo.ams_createMemberSiteDefaultsByOrgID @orgID=@orgid
		select @orgID = min(orgID) from #tmpItems where orgID > @orgID
	end
END

-- log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End changes to group prints of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));


/* **************** */
/* mark all as done */
/* **************** */
update qi WITH (UPDLOCK, HOLDLOCK)
set qi.queueStatusID = @statusDone,
	qi.dateUpdated = getdate()
FROM platformQueue.dbo.tblQueueItems as qi
INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
where qi.queueStatusID = @statusProcessing

-- clear all entries marked as done
BEGIN TRY
	DELETE from platformQueue.dbo.tblQueueItems
	where queueStatusID = @statusDone
END TRY
BEGIN CATCH
	-- do nothing. it will be cleaned up in a subsequent call
END CATCH

select @totalID = count(*) from #tmpItems

-- cleanup temp tables
IF OBJECT_ID('tempdb..#tmpItems') IS NOT NULL 
	DROP TABLE #tmpItems
IF OBJECT_ID('tempdb..#cache_ams_virtualGroupRuleInfo') IS NOT NULL
	DROP TABLE #cache_ams_virtualGroupRuleInfo
IF OBJECT_ID('tempdb..#cache_ams_virtualGroupRuleConditions') IS NOT NULL
	DROP TABLE #cache_ams_virtualGroupRuleConditions
IF OBJECT_ID('tempdb..#tblRGM') IS NOT NULL
	DROP TABLE #tblRGM
IF OBJECT_ID('tempdb..#tblmultiCondRules') IS NOT NULL
	DROP TABLE #tblmultiCondRules
IF OBJECT_ID('tempdb..#tmpNewMemberGroups') IS NOT NULL
	DROP TABLE #tmpNewMemberGroups
IF OBJECT_ID('tempdb..#tmpNewMemberGroups2') IS NOT NULL
	DROP TABLE #tmpNewMemberGroups2
IF OBJECT_ID('tempdb..#tmpCurrentMemberGroups') IS NOT NULL
	DROP TABLE #tmpCurrentMemberGroups
IF OBJECT_ID('tempdb..#tmpDelete') IS NOT NULL
	DROP TABLE #tmpDelete
IF OBJECT_ID('tempdb..#tmpInsert') IS NOT NULL
	DROP TABLE #tmpInsert
IF OBJECT_ID('tempdb..#membersToUpdate') IS NOT NULL
	DROP TABLE #membersToUpdate

-- Log
set @totalMS = Datediff(ms,@starttime,getdate())
insert into dbo.tblQueueLog_processMemberGroups ([procedure], logDate, totalMS, totalID, itemGroupUID)
values (OBJECT_NAME(@@PROCID), @starttime, @totalMS, @totalID, @itemGroupUID)

INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End Process of ' + cast(@itemGroupUID as varchar(60)), @totalMS);

RETURN 0
GO

USE [platformQueue]
GO
ALTER PROC [dbo].[queue_processMemberGroups_insert]
@orgID int,
@memberIDList varchar(max),
@conditionIDList varchar(max),
@runSchedule smallint,
@itemGroupUID uniqueidentifier OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @statusInserting int, @statusReady int
	select @statusInserting = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'popCondCache'
		and qs.queueStatus = 'insertingItems'
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'popCondCache'
		and qs.queueStatus = 'readyToProcess'
	
	-- if not passed in, there should only be one itemGroupUID for these inserts
	IF @itemGroupUID is null
		set @itemGroupUID = NEWID()

	-- holding table
	IF OBJECT_ID('tempdb..#tmpQueue') IS NOT NULL 
		DROP TABLE #tmpQueue
	CREATE TABLE #tmpQueue (orgID int, memberID int, conditionID int, itemUID uniqueidentifier DEFAULT NEWID())

	insert into #tmpQueue (orgID, memberID, conditionID)
	select o.orgID, nullIf(tmpM.listitem,0) as memberID, nullIf(tmpC.listitem,0) as conditionID
	from (select @orgID as orgID) as o
	outer apply membercentral.dbo.fn_intListToTable(@memberIDList,',') as tmpM
	outer apply membercentral.dbo.fn_intListToTable(@conditionIDList,',') as tmpC
		except
	select qid.orgID, qid.memberID, qid.conditionID
	from platformQueue.dbo.tblQueueItems_processMemberGroups as qid
	inner join platformQueue.dbo.tblQueueItems as qi on qi.itemUID = qid.itemUID
	inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.queueStatusID
	inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popCondCache'
	and qs.queueStatus not in ('grabbedForProcessing','processingItem','done')

	-- queue items
	insert into platformQueue.dbo.tblQueueItems_processMemberGroups (itemUID, itemGroupUID, orgID, memberID, conditionID)
		OUTPUT inserted.itemUID, inserted.dateAdded, @statusInserting 
		INTO platformQueue.dbo.tblQueueItems(itemUID, dateAdded, queueStatusID)
	select itemUID, @itemGroupUID, orgID, memberID, conditionID
	from #tmpQueue

	-- update queue item groups to show ready to process
	update qi WITH (UPDLOCK, HOLDLOCK)
	set qi.queueStatusID = @statusReady,
		qi.dateUpdated = getdate()
	from platformQueue.dbo.tblQueueItems as qi
	inner join #tmpQueue as s on s.itemUID = qi.itemUID

	IF OBJECT_ID('tempdb..#tmpQueue') IS NOT NULL 
		DROP TABLE #tmpQueue

	IF @TranCounter = 0
		COMMIT TRAN;

END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH


declare @msgXML xml
set @msgXML = '<itemGroupUID>' + cast(@itemGroupUID as varchar(60)) + '</itemGroupUID>'

-- if we are processing immediately
IF @runSchedule = 1 
BEGIN
	BEGIN TRY
		-- Log
		declare @logTreeID uniqueidentifier
		set @logTreeID = NEWID()
		INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, QueueMessage)
		VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Starting immediate processing', @msgXML);

		EXEC dbo.queue_processMemberGroups_process @itemGroupUID=@itemGroupUID, @logTreeID=@logTreeID
	END TRY
	BEGIN CATCH
		-- Log
		INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorSeverity, ErrorMessage, ErrorLine, ErrorProc)
		VALUES (@logTreeID, OBJECT_NAME(@@PROCID), ERROR_SEVERITY(), ERROR_MESSAGE(), ERROR_LINE(), ERROR_PROCEDURE());

		EXEC membercentral.dbo.up_errorhandler
		RETURN -1
	END CATCH
END
ELSE 
	-- if we are delayed processing, use service broker
	IF @runSchedule = 2 BEGIN
		EXEC dbo.sb_ProcessMemberGroupsSendMessage @msgXML
	END

RETURN 0
GO

USE [platformQueue]
GO
ALTER PROC [dbo].[queue_processMemberGroups_insert]
@orgID int,
@memberIDList varchar(max),
@conditionIDList varchar(max),
@runSchedule smallint,
@itemGroupUID uniqueidentifier OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @statusInserting int, @statusReady int
	select @statusInserting = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'popCondCache'
		and qs.queueStatus = 'insertingItems'
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'popCondCache'
		and qs.queueStatus = 'readyToProcess'
	
	-- if not passed in, there should only be one itemGroupUID for these inserts
	IF @itemGroupUID is null
		set @itemGroupUID = NEWID()

	-- holding table
	IF OBJECT_ID('tempdb..#tmpQueue') IS NOT NULL 
		DROP TABLE #tmpQueue
	CREATE TABLE #tmpQueue (orgID int, memberID int, conditionID int, itemUID uniqueidentifier DEFAULT NEWID())

	insert into #tmpQueue (orgID, memberID, conditionID)
	select o.orgID, nullIf(tmpM.listitem,0) as memberID, nullIf(tmpC.listitem,0) as conditionID
	from (select @orgID as orgID) as o
	outer apply membercentral.dbo.fn_intListToTable(@memberIDList,',') as tmpM
	outer apply membercentral.dbo.fn_intListToTable(@conditionIDList,',') as tmpC
		except
	select qid.orgID, qid.memberID, qid.conditionID
	from platformQueue.dbo.tblQueueItems_processMemberGroups as qid
	inner join platformQueue.dbo.tblQueueItems as qi on qi.itemUID = qid.itemUID
	inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.queueStatusID
	inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popCondCache'
	and qs.queueStatus not in ('grabbedForProcessing','processingItem','done')

	-- queue items
	insert into platformQueue.dbo.tblQueueItems_processMemberGroups (itemUID, itemGroupUID, orgID, memberID, conditionID)
		OUTPUT inserted.itemUID, inserted.dateAdded, @statusInserting 
		INTO platformQueue.dbo.tblQueueItems(itemUID, dateAdded, queueStatusID)
	select itemUID, @itemGroupUID, orgID, memberID, conditionID
	from #tmpQueue

	-- update queue item groups to show ready to process
	update qi WITH (UPDLOCK, HOLDLOCK)
	set qi.queueStatusID = @statusReady,
		qi.dateUpdated = getdate()
	from platformQueue.dbo.tblQueueItems as qi
	inner join #tmpQueue as s on s.itemUID = qi.itemUID

	IF OBJECT_ID('tempdb..#tmpQueue') IS NOT NULL 
		DROP TABLE #tmpQueue

	IF @TranCounter = 0
		COMMIT TRAN;

END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH


declare @msgXML xml
set @msgXML = '<itemGroupUID>' + cast(@itemGroupUID as varchar(60)) + '</itemGroupUID>'

-- if we are processing immediately
IF @runSchedule = 1 
BEGIN
	BEGIN TRY
		-- Log
		declare @logTreeID uniqueidentifier
		set @logTreeID = NEWID()
		INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, QueueMessage)
		VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Starting immediate processing', @msgXML);

		EXEC dbo.queue_processMemberGroups_process @itemGroupUID=@itemGroupUID, @logTreeID=@logTreeID
	END TRY
	BEGIN CATCH
		-- Log
		INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorSeverity, ErrorMessage, ErrorLine, ErrorProc)
		VALUES (@logTreeID, OBJECT_NAME(@@PROCID), ERROR_SEVERITY(), ERROR_MESSAGE(), ERROR_LINE(), ERROR_PROCEDURE());

		-- if deadlock, put itemGroupUID into the queue as ready to run when it can
		IF (ERROR_MESSAGE() like '%Errno 1205:%'
			OR ERROR_MESSAGE() like '%Violation of UNIQUE KEY constraint ''IX_cache_members_groups''.%' 
			OR ERROR_MESSAGE() like '%Violation of PRIMARY KEY constraint ''PK_cache_members_conditions''.%') 
			EXEC dbo.sb_ProcessMemberGroupsSendMessage @msgXML
		ELSE BEGIN
			EXEC membercentral.dbo.up_errorhandler
			RETURN -1
		END	
	END CATCH
END
ELSE 
	-- if we are delayed processing, use service broker
	IF @runSchedule = 2 BEGIN
		EXEC dbo.sb_ProcessMemberGroupsSendMessage @msgXML
	END

RETURN 0
GO

USE [platformQueue]
GO
ALTER PROC [dbo].[sb_ProcessMemberGroupsQueueActivated]
AS

DECLARE @dlgID uniqueidentifier
DECLARE @mt nvarchar(256)
DECLARE @msg xml
DECLARE @itemGroupUID uniqueidentifier;
DECLARE @starttime datetime

DECLARE @smtpserver varchar(20), @tier varchar(20), @errorSubject varchar(100), @errmsg nvarchar(2048), @crlf varchar(10)
SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'					
SET @smtpserver = '10.36.18.90'
IF @@SERVERNAME = 'DEV04\PLATFORM2008' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'DEV03\PLATFORM2008' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'STAGING01\PLATFORM2008' BEGIN
	SET @tier = 'STAGING'
	SET @smtpserver = 'mail.trialsmith.com'
END
SET @errorSubject = @tier + ': Error Processing Member Groups'

WHILE 1 = 1
BEGIN

	WAITFOR (
		RECEIVE TOP(1) 
			@dlgID = conversation_handle,
			@mt = message_type_name,
			@msg = cast(message_body as xml)
		FROM dbo.ProcessMemberGroupsQueue
		), TIMEOUT 1000;

	IF @@ROWCOUNT = 0
		BREAK;

	IF @mt = N'PlatformQueue/ProcessMemberGroupsRequestMessage'
	BEGIN
		-- Log
		set @starttime = getdate()
		INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, QueueMessage)
		VALUES (@dlgID, OBJECT_NAME(@@PROCID), 'Start Process Message', @msg);

		BEGIN TRY
			SELECT @itemGroupUID = @msg.value('(/itemGroupUID)[1]','uniqueidentifier')
			IF @itemGroupUID is not null
				EXEC dbo.queue_processMemberGroups_process @itemGroupUID=@itemGroupUID, @logTreeID=@dlgID

			END CONVERSATION @dlgID;
		END TRY
		BEGIN CATCH
			-- Log
			INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorSeverity, ErrorMessage, ErrorLine, ErrorProc)
			VALUES (@dlgID, OBJECT_NAME(@@PROCID), ERROR_SEVERITY(), ERROR_MESSAGE(), ERROR_LINE(), ERROR_PROCEDURE());

			-- if deadlock, put itemGroupUID back into the queue as ready to run again
			IF @itemGroupUID is not null and 
				(ERROR_MESSAGE() like '%Errno 1205:%'
				 OR ERROR_MESSAGE() like '%Violation of UNIQUE KEY constraint ''IX_cache_members_groups''.%'
				 OR ERROR_MESSAGE() like '%Violation of PRIMARY KEY constraint ''PK_cache_members_conditions''.%') 
				BEGIN TRY				
					EXEC dbo.queue_processMemberGroups_requeue @itemGroupUID=@itemGroupUID, @logTreeID=@dlgID
				END TRY
				BEGIN CATCH
					BEGIN TRY					
						SET @errmsg = @errorSubject + @crlf + @crlf + 
										ERROR_MESSAGE() + @crlf + @crlf + 
										'LogTreeID=' + cast(@dlgID as varchar(60)) + @crlf + @crlf + 
										'Unable to requeue. After addressing error, run: ' + @crlf + 
										'EXEC platformQueue.dbo.queue_processMemberGroups_requeue @itemGroupUID=''' + cast(@itemGroupUID as varchar(60)) + ''', @logTreeID=''' + cast(@dlgID as varchar(60)) + ''';'

						EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
							@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
							@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
					END TRY
					BEGIN CATCH
						-- do nothing
					END CATCH
				END CATCH
			ELSE 
			BEGIN
				BEGIN TRY
					SET @errmsg = @errorSubject + @crlf + @crlf + 
									ERROR_MESSAGE() + @crlf + @crlf + 
									'LogTreeID=' + cast(@dlgID as varchar(60)) + @crlf + @crlf + 
									'After addressing error, run: ' + @crlf + 
									'EXEC platformQueue.dbo.queue_processMemberGroups_requeue @itemGroupUID=''' + cast(@itemGroupUID as varchar(60)) + ''', @logTreeID=''' + cast(@dlgID as varchar(60)) + ''';'

					EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
						@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
						@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
				END TRY
				BEGIN CATCH
					-- do nothing
				END CATCH
			END

			END CONVERSATION @dlgID;
		END CATCH;

		-- log
		INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
		VALUES (@dlgID, OBJECT_NAME(@@PROCID), 'End Process Message', Datediff(ms,@starttime,getdate()));
	END
	ELSE
		IF @mt = N'http://schemas.microsoft.com/SQL/ServiceBroker/EndDialog' OR @mt = N'http://schemas.microsoft.com/SQL/ServiceBroker/Error'
			END CONVERSATION @dlgID;
		ELSE
		BEGIN
			--anything other than user type or EndDialog is an unexpected error
			-- Log
			INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
			VALUES (@dlgID, OBJECT_NAME(@@PROCID), 'Unexpected message type received - ' + @mt);

			END CONVERSATION @dlgID;
		END

END;
GO

ALTER PROC [dbo].[job_popMemGrp_process]
@itemGroupUID uniqueidentifier,
@logTreeID uniqueidentifier

AS

SET NOCOUNT ON

IF @itemGroupUID is null
	RETURN -1

declare @starttime datetime, @starttime2 datetime, @totalMS int, @totalID int
declare @jobUID uniqueidentifier
declare @procname varchar(100)
set @starttime = getdate()
set @jobUID = NEWID()

-- Log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start Process of ' + cast(@itemGroupUID as varchar(60)));

declare @statusReady int, @statusGrabbed int, @statusProcessing int, @statusDone int
select @statusReady = qs.queueStatusID 
	from platformQueue.dbo.tblQueueStatuses as qs
	inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popMemGroups'
	and qs.queueStatus = 'readyToProcess'
select @statusGrabbed = qs.queueStatusID 
	from platformQueue.dbo.tblQueueStatuses as qs
	inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popMemGroups'
	and qs.queueStatus = 'grabbedForProcessing'
select @statusProcessing = qs.queueStatusID 
	from platformQueue.dbo.tblQueueStatuses as qs
	inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popMemGroups'
	and qs.queueStatus = 'processingItem'
select @statusDone = qs.queueStatusID 
	from platformQueue.dbo.tblQueueStatuses as qs
	inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popMemGroups'
	and qs.queueStatus = 'done'

IF OBJECT_ID('tempdb..#tmpItems') IS NOT NULL 
	DROP TABLE #tmpItems
IF OBJECT_ID('tempdb..#cache_ams_virtualGroupRuleInfo') IS NOT NULL
	DROP TABLE #cache_ams_virtualGroupRuleInfo
IF OBJECT_ID('tempdb..#cache_ams_virtualGroupRuleConditions') IS NOT NULL
	DROP TABLE #cache_ams_virtualGroupRuleConditions
IF OBJECT_ID('tempdb..#tblRGM') IS NOT NULL
	DROP TABLE #tblRGM
IF OBJECT_ID('tempdb..#tblmultiCondRules') IS NOT NULL
	DROP TABLE #tblmultiCondRules
IF OBJECT_ID('tempdb..#tmpNewMemberGroups') IS NOT NULL
	DROP TABLE #tmpNewMemberGroups
IF OBJECT_ID('tempdb..#tmpNewMemberGroups2') IS NOT NULL
	DROP TABLE #tmpNewMemberGroups2
IF OBJECT_ID('tempdb..#tmpCurrentMemberGroups') IS NOT NULL
	DROP TABLE #tmpCurrentMemberGroups
IF OBJECT_ID('tempdb..#tmpDelete') IS NOT NULL
	DROP TABLE #tmpDelete
IF OBJECT_ID('tempdb..#tmpInsert') IS NOT NULL
	DROP TABLE #tmpInsert
IF OBJECT_ID('tempdb..#membersToUpdate') IS NOT NULL
	DROP TABLE #membersToUpdate

CREATE TABLE #tmpItems (itemUID uniqueidentifier, orgID int, memberID int)
CREATE TABLE #cache_ams_virtualGroupRuleInfo (orgID int, ruleID int PRIMARY KEY, conditionCount int, usesOR bit, usesAND bit, usesExclude bit)
CREATE TABLE #cache_ams_virtualGroupRuleConditions (ruleID int, conditionID int)
CREATE TABLE #tblRGM (memberID int, groupID int);
CREATE TABLE #tblmultiCondRules (orgID int, ruleID int PRIMARY KEY, ruleSQL varchar(max));
CREATE TABLE #tmpNewMemberGroups (orgID int, memberID int, groupID int, isManualDirect tinyint, isManualIndirect tinyint, isVirtualDirect tinyint, isVirtualIndirect tinyint)
CREATE TABLE #tmpNewMemberGroups2 (autoid int IDENTITY(1,1), orgID int, memberID int, groupID int, isManualDirect bit, isManualIndirect bit, isVirtualDirect bit, isVirtualIndirect bit)
CREATE TABLE #tmpCurrentMemberGroups (autoid int, orgid int, memberid int, groupid int, isManualDirect bit, isManualIndirect bit, isVirtualDirect bit, isVirtualIndirect bit)
CREATE TABLE #tmpDelete (autoid int, memberid int, groupID int)
CREATE TABLE #tmpInsert (orgID int, memberID int, groupID int, isManualDirect bit, isManualIndirect bit, isVirtualDirect bit, isVirtualIndirect bit)
CREATE TABLE #membersToUpdate (memberID int)



/* ********************************** */
/* dequeue items ready for processing */
/* ********************************** */
UPDATE qi WITH (UPDLOCK, READPAST)
SET qi.queueStatusID = @statusProcessing,
	qi.dateUpdated = getdate(),
	qi.jobUID = @jobUID,
	qi.jobDateStarted = getdate(),
	qi.jobServerID = 0
	OUTPUT inserted.itemUID, qid.orgID, qid.memberID
	INTO #tmpItems
FROM dbo.tblQueueItems as qi
INNER JOIN dbo.tblQueueItems_processMemberGroups as qid ON qid.itemUID = qi.itemUID
WHERE qi.queueStatusID = @statusReady
and qid.itemGroupUID = @itemGroupUID


-- indexes for speed
create index tmp_orgID on #tmpItems (orgID);
create index tmp_MemberID on #tmpItems (memberID);


/* *********************************** */
/* get virtual assignments for members */
/* *********************************** */
-- Log
set @starttime2 = getdate()
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start create temp condition tables of ' + cast(@itemGroupUID as varchar(60)));

-- get rule info
insert into #cache_ams_virtualGroupRuleInfo (orgID, ruleID, conditionCount, usesOR, usesAND, usesExclude)
select r.orgID, r.ruleID, 
	min(r.ruleXML.value('count(//condition)','int')) as conditionCount,
	max(cast(r.ruleXML.exist('//conditionset[@op = "OR"]') as int)) as usesOR,
	max(cast(r.ruleXML.exist('//conditionset[@op = "AND"]') as int)) as usesAND,
	max(cast(r.ruleXML.exist('//conditionset[@act = "exclude"]') as int)) as usesExclude
from membercentral.dbo.ams_virtualGroupRules as r WITH(NOLOCK)
inner join membercentral.dbo.ams_virtualGroupRuleGroups as rg WITH(NOLOCK) on rg.ruleID = r.ruleID
INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.groupID = rg.groupid and g.status <> 'D'
cross apply r.ruleXML.nodes('//condition') as T(C)
where r.orgID in (select distinct orgID from #tmpItems)
and r.isActive = 1
and r.ruleTypeID = 1
group by r.orgID, r.ruleID

-- expand rules and conditions
insert into #cache_ams_virtualGroupRuleConditions (ruleID, conditionID)
select distinct r.ruleID, vc.conditionID
from membercentral.dbo.ams_virtualGroupRules as r WITH(NOLOCK)
inner join #cache_ams_virtualGroupRuleInfo as vgri on vgri.ruleID = r.ruleID
cross apply r.ruleXML.nodes('//condition') as T(C)
inner join membercentral.dbo.ams_virtualGroupConditions as vc WITH(NOLOCK) on vc.uid = C.value('@id', 'uniqueidentifier')

-- expand multi condition rules for looping. only get rules with conditions linked to members in tmpItems
insert into #tblmultiCondRules (orgID, ruleID, ruleSQL)
select distinct r.orgId, r.ruleID, r.ruleSQL
from #cache_ams_virtualGroupRuleInfo as vgri
inner join membercentral.dbo.ams_virtualGroupRules as r WITH(NOLOCK) on r.ruleID = vgri.ruleID
inner join #cache_ams_virtualGroupRuleConditions as rc on rc.ruleID = r.ruleID
inner join membercentral.dbo.cache_members_conditions as cmc WITH(NOLOCK) on cmc.conditionID = rc.conditionID
inner join #tmpItems as ti on ti.memberID = cmc.memberID and ti.orgID = vgri.orgID
where ((vgri.usesOR = 1 and vgri.usesAnd = 1) or vgri.usesExclude = 1)
and r.ruleSQL <> ''

-- log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End create temp condition tables of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));


-- process all one-condition rules
select @totalID = count(*) from #cache_ams_virtualGroupRuleInfo where conditionCount = 1
IF @totalID > 0 BEGIN
	-- Log
	set @starttime2 = getdate()
	INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
	VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start one-condition rules (' + cast(@totalID as varchar(10)) + ') of ' + cast(@itemGroupUID as varchar(60)));

	insert into #tblRGM (memberid, groupID)
	select distinct cmc.memberID, rg.groupID
	from #cache_ams_virtualGroupRuleInfo as vgri
	inner join #cache_ams_virtualGroupRuleConditions as rc on rc.ruleID = vgri.ruleID
	inner join membercentral.dbo.cache_members_conditions as cmc WITH(NOLOCK) on cmc.conditionID = rc.conditionID
	inner join #tmpItems as ti on ti.memberID = cmc.memberID and ti.orgID = vgri.orgID
	inner join membercentral.dbo.ams_virtualGroupRuleGroups as rg WITH(NOLOCK) on rg.ruleID = vgri.ruleID
	INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.groupID = rg.groupid and g.status <> 'D'
	inner join membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = cmc.memberid
	where vgri.conditionCount = 1
	and m.memberid = m.activeMemberID
	and m.status <> 'D'
	OPTION (RECOMPILE)

	-- log
	INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
	VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End one-condition rules (' + cast(@totalID as varchar(10)) + ') of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));
END


-- run all AND multi condition rules
select @totalID = count(*) from #cache_ams_virtualGroupRuleInfo where conditionCount > 1 and usesOR = 0 and usesAND = 1 and usesExclude = 0
IF @totalID > 0 BEGIN
	-- Log
	set @starttime2 = getdate()
	INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
	VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start multi-condition AND rules (' + cast(@totalID as varchar(10)) + ') of ' + cast(@itemGroupUID as varchar(60)));

	insert into #tblRGM (memberid, groupID)
	select distinct m.memberID, rg.groupID
	from #cache_ams_virtualGroupRuleInfo as vgri
	inner join #cache_ams_virtualGroupRuleConditions as rc on rc.ruleID = vgri.ruleID
	inner join membercentral.dbo.cache_members_conditions as cmc WITH(NOLOCK) on cmc.conditionID = rc.conditionID
	inner join #tmpItems as ti on ti.memberID = cmc.memberID and ti.orgID = vgri.orgID
	inner join membercentral.dbo.ams_virtualGroupRuleGroups as rg WITH(NOLOCK) on rg.ruleID = vgri.ruleID
	INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.groupID = rg.groupid and g.status <> 'D'
	inner join membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = cmc.memberid
	where vgri.conditionCount > 1
	and vgri.usesOR = 0
	and vgri.usesAND = 1
	and vgri.usesExclude = 0
	and m.memberid = m.activeMemberID
	and m.status <> 'D'
	group by vgri.ruleID, m.memberID, vgri.conditionCount, rg.groupID
	having count(*) = vgri.conditionCount
	OPTION (RECOMPILE)

	-- log
	INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
	VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End multi-condition AND rules (' + cast(@totalID as varchar(10)) + ') of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));
END


-- run all OR multi condition rules
select @totalID = count(*) from #cache_ams_virtualGroupRuleInfo where conditionCount > 1 and usesOR = 1 and usesAND = 0 and usesExclude = 0
IF @totalID > 0 BEGIN
	-- Log
	set @starttime2 = getdate()
	INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
	VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start multi-condition OR rules (' + cast(@totalID as varchar(10)) + ') of ' + cast(@itemGroupUID as varchar(60)));

	insert into #tblRGM (memberid, groupID)
	select distinct m.memberID, rg.groupID
	from #cache_ams_virtualGroupRuleInfo as vgri
	inner join #cache_ams_virtualGroupRuleConditions as rc on rc.ruleID = vgri.ruleID
	inner join membercentral.dbo.cache_members_conditions as cmc WITH(NOLOCK) on cmc.conditionID = rc.conditionID
	inner join #tmpItems as ti on ti.memberID = cmc.memberID and ti.orgID = vgri.orgID
	inner join membercentral.dbo.ams_virtualGroupRuleGroups as rg WITH(NOLOCK) on rg.ruleID = vgri.ruleID
	INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.groupID = rg.groupid and g.status <> 'D'
	inner join membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = cmc.memberid
	where vgri.conditionCount > 1
	and vgri.usesOR = 1
	and vgri.usesAND = 0
	and vgri.usesExclude = 0
	and m.memberid = m.activeMemberID
	and m.status <> 'D'
	OPTION (RECOMPILE)

	-- log
	INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
	VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End multi-condition OR rules (' + cast(@totalID as varchar(10)) + ') of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));
END


-- loop over multi-condition rules 
select @totalID = count(*) from #tblmultiCondRules
IF @totalID > 0 BEGIN
	-- Log
	set @starttime2 = getdate()
	INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
	VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start multi-condition rules (' + cast(@totalID as varchar(10)) + ') of ' + cast(@itemGroupUID as varchar(60)));

	declare @orgID int, @minRuleID int, @ruleSQL varchar(max), @SQLString nvarchar(max), @ParamDefinition nvarchar(100)
	set @ParamDefinition = N'@ruleID int, @orgID int'
	select @minRuleID = min(ruleID) from #tblmultiCondRules
	while @minRuleID is not null BEGIN
		select @ruleSQL = ruleSQL, @orgID = orgID from #tblmultiCondRules where ruleID = @minRuleID
		set @SQLString = ''
		set @SQLString = N'use membercentral; 
				insert into #tblRGM (memberid, groupID) 
				select distinct m.memberID, rg.groupID 
				from membercentral.dbo.ams_members as m WITH(NOLOCK) 
				inner join #tmpItems as tmp on tmp.memberID = m.memberID 
				inner join membercentral.dbo.ams_virtualGroupRules as r WITH(NOLOCK) on r.ruleID = @ruleID 
				inner join membercentral.dbo.ams_virtualGroupRuleGroups as rg WITH(NOLOCK) on rg.ruleID = r.ruleID 
				INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.groupID = rg.groupid and g.status <> ''D'' 
				where m.orgID = @orgID 
				and m.memberid = m.activeMemberID 
				and m.status <> ''D'' 
				and (' + @ruleSQL + ') 
				OPTION (RECOMPILE)'
		exec sp_executesql @SQLString, @ParamDefinition, @ruleID=@minRuleID, @orgID=@orgID

		select @minRuleID = min(ruleID) from #tblmultiCondRules where ruleID > @minRuleID
	END

	-- log
	INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
	VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End multi-condition rules (' + cast(@totalID as varchar(10)) + ') of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));
END


-- Log
set @starttime2 = getdate()
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start expand groups of ' + cast(@itemGroupUID as varchar(60)));

-- expand groups
create index RGM_GroupID on #tblRGM (groupID);

INSERT INTO #tmpNewMemberGroups (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
select DISTINCT rg.orgID, dg.memberID, rg.groupID, 0, 0, isVirtualDirect = rg.isBaseGroup, isVirtualIndirect = (~rg.isBaseGroup)
FROM #tblRGM as dg
INNER JOIN membercentral.dbo.cache_ams_recursiveGroups as rg WITH(NOLOCK) ON dg.groupid = rg.startGroupid
OPTION (RECOMPILE)

-- log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End expand groups of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));



/* ********************************** */
/* get manual assignments for members */
/* ********************************** */
-- Log
set @starttime2 = getdate()
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start manual assignment of ' + cast(@itemGroupUID as varchar(60)));

-- you could be directly assigned and indirectly assigned to the same group.
INSERT INTO #tmpNewMemberGroups (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
select distinct tmp.orgID, tmp.memberID, g.groupID, isManualDirect = rg.isBaseGroup, isManualIndirect = (~rg.isBaseGroup), 0, 0
from #tmpItems as tmp
inner join membercentral.dbo.ams_memberGroups AS mg WITH(NOLOCK) on mg.memberID = tmp.memberID
INNER JOIN membercentral.dbo.cache_ams_recursiveGroups as rg WITH(NOLOCK) ON mg.groupid = rg.startGroupid
INNER JOIN membercentral.dbo.ams_groups AS g WITH(NOLOCK) ON g.groupID = rg.groupID
INNER JOIN membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = mg.memberID
where g.status <> 'D'
AND m.memberID = m.activeMemberID
AND m.status <> 'D'

-- public group for all members
INSERT INTO #tmpNewMemberGroups (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
SELECT distinct tmp.orgID, tmp.memberID, g.groupID, 0, 0, 0, 1
FROM #tmpItems as tmp
INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.orgID = tmp.orgID
INNER JOIN membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = tmp.memberID and m.orgID = g.orgID
WHERE g.isSystemGroup = 1
AND g.groupName = 'Public'
AND g.status = 'A'
AND m.memberid = m.activeMemberID
AND m.status <> 'D'
	union all
SELECT distinct tmp.orgID, 0 as memberID, g.groupID, 0, 0, 0, 1
FROM #tmpItems as tmp
INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.orgID = tmp.orgID
WHERE g.isSystemGroup = 1
AND g.groupName = 'Public'
AND g.status = 'A'

-- guest group for all guests
INSERT INTO #tmpNewMemberGroups (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
SELECT distinct tmp.orgID, tmp.memberID, g.groupID, 0, 0, 0, 1
FROM #tmpItems as tmp
INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.orgID = tmp.orgID
INNER JOIN membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = tmp.memberID and m.orgID = g.orgID
INNER JOIN membercentral.dbo.ams_memberTypes as mt WITH(NOLOCK) on mt.memberTypeID = m.memberTypeID
WHERE g.isSystemGroup = 1
AND g.groupName = 'Guests'
AND g.status = 'A'
AND m.memberid = m.activeMemberID
AND m.status <> 'D'
AND mt.memberType = 'Guest'

-- users group for all users
INSERT INTO #tmpNewMemberGroups (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
SELECT distinct tmp.orgID, tmp.memberID, g.groupID, 0, 0, 0, 1
FROM #tmpItems as tmp
INNER JOIN membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.orgID = tmp.orgID
INNER JOIN membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = tmp.memberID and m.orgID = g.orgID
INNER JOIN membercentral.dbo.ams_memberTypes as mt WITH(NOLOCK) on mt.memberTypeID = m.memberTypeID
WHERE g.isSystemGroup = 1
AND g.groupName = 'Users'
AND g.status = 'A'
AND m.memberid = m.activeMemberID
AND m.status <> 'D'
AND mt.memberType = 'User'

-- new member groups compacted
INSERT INTO #tmpNewMemberGroups2 (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
select orgID, memberID, groupID, 
	max(isManualDirect) as isManualDirect, 
	max(isManualIndirect) as isManualIndirect, 
	max(isVirtualDirect) as isVirtualDirect, 
	max(isVirtualIndirect) as isVirtualIndirect
from #tmpNewMemberGroups
group by orgID, memberID, groupID

-- log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End manual assignment of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));


/* ******************** */
/* Modify member groups */
/* ******************** */
-- current member groups
INSERT INTO #tmpCurrentMemberGroups (autoid, orgid, memberid, groupid, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
select mg.autoid, mg.orgid, mg.memberid, mg.groupid, mg.isManualDirect, mg.isManualIndirect, mg.isVirtualDirect, mg.isVirtualIndirect
from #tmpItems as tmp
inner join membercentral.dbo.cache_members_groups as mg WITH(NOLOCK) on mg.memberID = tmp.memberID
	union
select mg.autoid, mg.orgid, mg.memberid, mg.groupid, mg.isManualDirect, mg.isManualIndirect, mg.isVirtualDirect, mg.isVirtualIndirect
from #tmpItems as tmp
inner join membercentral.dbo.cache_members_groups as mg WITH(NOLOCK) on mg.orgID = tmp.orgID and mg.memberID = 0

-- rows to be deleted: #tmpCurrentMemberGroups but not in #tmpNewMemberGroups2
-- also delete rows where memberID is no longer the activeMemberID (for cleanup)
INSERT INTO #tmpDelete (autoID, memberID, groupID)
SELECT curr.autoid, curr.memberID, curr.groupID
from #tmpCurrentMemberGroups as curr
where not exists (
	select autoid 
	from #tmpNewMemberGroups2
	where orgID = curr.orgID
	and memberID = curr.memberID
	and groupID = curr.groupID
	and isManualDirect = curr.isManualDirect
	and isManualIndirect = curr.isManualIndirect
	and isVirtualDirect = curr.isVirtualDirect
	and isVirtualIndirect = curr.isVirtualIndirect
)
	union
select mg.autoid, mg.memberID, mg.groupID
from #tmpItems as tmp
inner join membercentral.dbo.cache_members_groups as mg WITH(NOLOCK) on mg.memberID = tmp.memberID
inner join membercentral.dbo.ams_members as m WITH(NOLOCK) on m.memberid = mg.memberID
where m.memberID <> m.activeMemberID
or m.status = 'D'

-- rows to be added: mem/grp in #tmpNewMemberGroups2 but not in #tmpCurrentMemberGroups
INSERT INTO #tmpInsert (orgID, memberID, groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
SELECT distinct new.orgid, new.memberid, new.groupid, new.isManualDirect, new.isManualIndirect, new.isVirtualDirect, new.isVirtualIndirect
from #tmpNewMemberGroups2 as new
where not exists (
	select autoid
	from #tmpCurrentMemberGroups
	where orgID = new.orgID
	and memberID = new.memberID
	and groupID = new.groupID
	and isManualDirect = new.isManualDirect
	and isManualIndirect = new.isManualIndirect
	and isVirtualDirect = new.isVirtualDirect
	and isVirtualIndirect = new.isVirtualIndirect
)

-- Log
set @starttime2 = getdate()
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start changes to real tables of ' + cast(@itemGroupUID as varchar(60)));

-- delete rows that shouldnt be there anymore
DELETE FROM vw
FROM membercentral.dbo.cache_members_groups as vw
INNER JOIN #tmpDelete as del on del.autoID = vw.autoID

-- add rows that should be there now
INSERT INTO membercentral.dbo.cache_members_groups (orgid, memberid, groupid, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect)
SELECT distinct orgid, memberid, groupid, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect
FROM #tmpInsert as tmp
WHERE NOT EXISTS (select autoid from membercentral.dbo.cache_members_groups where memberid = tmp.memberid and groupID = tmp.groupID)

-- log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End changes to real tables of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));


/* ******************* */
/* Modify group prints */
/* ******************* */
-- Log
set @starttime2 = getdate()
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Start changes to group prints of ' + cast(@itemGroupUID as varchar(60)));

-- update group prints
insert into #membersToUpdate (memberID)
select d.memberID 
from #tmpDelete as d
left outer join #tmpInsert as i on i.memberid = d.memberid and i.groupid = d.groupID
where i.orgid is null
	union 
select i.memberID 
from #tmpInsert as i
left outer join #tmpDelete as d on i.memberid = d.memberid and i.groupid = d.groupID
where d.autoID is null

select @totalID = count(*) from #membersToUpdate

if exists (select top 1 memberID from #membersToUpdate) BEGIN
	CREATE INDEX IX_membersToUpdate_MID ON #membersToUpdate (memberID); 
	
	select @orgID = null
	select @orgID = min(orgID) from #tmpItems
	while @orgID is not null BEGIN
		exec membercentral.dbo.cache_perms_updateGroupPrintsForMembersBulk @orgid=@orgID
		exec membercentral.dbo.ams_createMemberSiteDefaultsByOrgID @orgID=@orgid
		select @orgID = min(orgID) from #tmpItems where orgID > @orgID
	end
END

-- log
INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End changes to group prints of ' + cast(@itemGroupUID as varchar(60)), Datediff(ms,@starttime2,getdate()));


/* **************** */
/* mark all as done */
/* **************** */
update qi WITH (UPDLOCK, HOLDLOCK)
set qi.queueStatusID = @statusDone,
	qi.dateUpdated = getdate()
FROM platformQueue.dbo.tblQueueItems as qi
INNER JOIN #tmpItems as tmp on tmp.itemUID = qi.itemUID
where qi.queueStatusID = @statusProcessing

-- clear all entries marked as done
BEGIN TRY
	DELETE from platformQueue.dbo.tblQueueItems
	where queueStatusID = @statusDone
END TRY
BEGIN CATCH
	-- do nothing. it will be cleaned up in a subsequent call
END CATCH

select @totalID = count(*) from #tmpItems

-- cleanup temp tables
IF OBJECT_ID('tempdb..#tmpItems') IS NOT NULL 
	DROP TABLE #tmpItems
IF OBJECT_ID('tempdb..#cache_ams_virtualGroupRuleInfo') IS NOT NULL
	DROP TABLE #cache_ams_virtualGroupRuleInfo
IF OBJECT_ID('tempdb..#cache_ams_virtualGroupRuleConditions') IS NOT NULL
	DROP TABLE #cache_ams_virtualGroupRuleConditions
IF OBJECT_ID('tempdb..#tblRGM') IS NOT NULL
	DROP TABLE #tblRGM
IF OBJECT_ID('tempdb..#tblmultiCondRules') IS NOT NULL
	DROP TABLE #tblmultiCondRules
IF OBJECT_ID('tempdb..#tmpNewMemberGroups') IS NOT NULL
	DROP TABLE #tmpNewMemberGroups
IF OBJECT_ID('tempdb..#tmpNewMemberGroups2') IS NOT NULL
	DROP TABLE #tmpNewMemberGroups2
IF OBJECT_ID('tempdb..#tmpCurrentMemberGroups') IS NOT NULL
	DROP TABLE #tmpCurrentMemberGroups
IF OBJECT_ID('tempdb..#tmpDelete') IS NOT NULL
	DROP TABLE #tmpDelete
IF OBJECT_ID('tempdb..#tmpInsert') IS NOT NULL
	DROP TABLE #tmpInsert
IF OBJECT_ID('tempdb..#membersToUpdate') IS NOT NULL
	DROP TABLE #membersToUpdate

-- Log
set @totalMS = Datediff(ms,@starttime,getdate())
insert into dbo.tblQueueLog_processMemberGroups ([procedure], logDate, totalMS, totalID, itemGroupUID)
values (OBJECT_NAME(@@PROCID), @starttime, @totalMS, @totalID, @itemGroupUID)

INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'End Process of ' + cast(@itemGroupUID as varchar(60)), @totalMS);

RETURN 0
GO

USE [platformQueue]
GO
ALTER PROC [dbo].[queue_processMemberGroups_insert]
@orgID int,
@memberIDList varchar(max),
@conditionIDList varchar(max),
@runSchedule smallint,
@itemGroupUID uniqueidentifier OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @statusInserting int, @statusReady int
	select @statusInserting = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'popCondCache'
		and qs.queueStatus = 'insertingItems'
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'popCondCache'
		and qs.queueStatus = 'readyToProcess'
	
	-- if not passed in, there should only be one itemGroupUID for these inserts
	IF @itemGroupUID is null
		set @itemGroupUID = NEWID()

	-- holding table
	IF OBJECT_ID('tempdb..#tmpQueue') IS NOT NULL 
		DROP TABLE #tmpQueue
	CREATE TABLE #tmpQueue (orgID int, memberID int, conditionID int, itemUID uniqueidentifier DEFAULT NEWID())

	insert into #tmpQueue (orgID, memberID, conditionID)
	select o.orgID, nullIf(tmpM.listitem,0) as memberID, nullIf(tmpC.listitem,0) as conditionID
	from (select @orgID as orgID) as o
	outer apply membercentral.dbo.fn_intListToTable(@memberIDList,',') as tmpM
	outer apply membercentral.dbo.fn_intListToTable(@conditionIDList,',') as tmpC
		except
	select qid.orgID, qid.memberID, qid.conditionID
	from platformQueue.dbo.tblQueueItems_processMemberGroups as qid
	inner join platformQueue.dbo.tblQueueItems as qi on qi.itemUID = qid.itemUID
	inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.queueStatusID
	inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'popCondCache'
	and qs.queueStatus not in ('grabbedForProcessing','processingItem','done')

	-- queue items
	insert into platformQueue.dbo.tblQueueItems_processMemberGroups (itemUID, itemGroupUID, orgID, memberID, conditionID)
		OUTPUT inserted.itemUID, inserted.dateAdded, @statusInserting 
		INTO platformQueue.dbo.tblQueueItems(itemUID, dateAdded, queueStatusID)
	select itemUID, @itemGroupUID, orgID, memberID, conditionID
	from #tmpQueue

	-- update queue item groups to show ready to process
	update qi WITH (UPDLOCK, HOLDLOCK)
	set qi.queueStatusID = @statusReady,
		qi.dateUpdated = getdate()
	from platformQueue.dbo.tblQueueItems as qi
	inner join #tmpQueue as s on s.itemUID = qi.itemUID

	IF OBJECT_ID('tempdb..#tmpQueue') IS NOT NULL 
		DROP TABLE #tmpQueue

	IF @TranCounter = 0
		COMMIT TRAN;

END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH


declare @msgXML xml
set @msgXML = '<itemGroupUID>' + cast(@itemGroupUID as varchar(60)) + '</itemGroupUID>'

-- if we are processing immediately
IF @runSchedule = 1 
BEGIN
	BEGIN TRY
		-- Log
		declare @logTreeID uniqueidentifier
		set @logTreeID = NEWID()
		INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, QueueMessage)
		VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Starting immediate processing', @msgXML);

		EXEC dbo.queue_processMemberGroups_process @itemGroupUID=@itemGroupUID, @logTreeID=@logTreeID
	END TRY
	BEGIN CATCH
		-- Log
		INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorSeverity, ErrorMessage, ErrorLine, ErrorProc)
		VALUES (@logTreeID, OBJECT_NAME(@@PROCID), ERROR_SEVERITY(), ERROR_MESSAGE(), ERROR_LINE(), ERROR_PROCEDURE());

		-- if deadlock, put itemGroupUID into the queue as ready to run when it can
		IF (ERROR_MESSAGE() like '%Errno 1205:%'
			OR ERROR_MESSAGE() like '%Violation of UNIQUE KEY constraint ''IX_cache_members_groups''.%' 
			OR ERROR_MESSAGE() like '%Violation of PRIMARY KEY constraint ''PK_cache_members_conditions''.%') 
			EXEC dbo.queue_processMemberGroups_requeue @itemGroupUID, @logTreeID			
		ELSE BEGIN
			EXEC membercentral.dbo.up_errorhandler
			RETURN -1
		END	
	END CATCH
END
ELSE 
	-- if we are delayed processing, use service broker
	IF @runSchedule = 2 BEGIN
		EXEC dbo.sb_ProcessMemberGroupsSendMessage @msgXML
	END

RETURN 0
GO

USE [platformQueue]
GO
ALTER PROC [dbo].[queue_processMemberGroups_insert]
@orgID int,
@memberIDList varchar(max),
@conditionIDList varchar(max),
@runSchedule smallint,
@itemGroupUID uniqueidentifier OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @statusInserting int, @statusReady int
	select @statusInserting = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'popCondCache'
		and qs.queueStatus = 'insertingItems'
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'popCondCache'
		and qs.queueStatus = 'readyToProcess'
	
	-- there should only be one itemGroupUID for these inserts. should have a new itemGroupUID.
	set @itemGroupUID = NEWID()

	-- holding table
	IF OBJECT_ID('tempdb..#tmpQueue') IS NOT NULL 
		DROP TABLE #tmpQueue
	CREATE TABLE #tmpQueue (orgID int, memberID int, conditionID int, itemUID uniqueidentifier DEFAULT NEWID())

	-- if we are processing immediately, ignore the cases where it is already in the queue. do it anyway. You can do it!
	IF @runSchedule = 1
		insert into #tmpQueue (orgID, memberID, conditionID)
		select o.orgID, nullIf(tmpM.listitem,0) as memberID, nullIf(tmpC.listitem,0) as conditionID
		from (select @orgID as orgID) as o
		outer apply membercentral.dbo.fn_intListToTable(@memberIDList,',') as tmpM
		outer apply membercentral.dbo.fn_intListToTable(@conditionIDList,',') as tmpC
	ELSE
		insert into #tmpQueue (orgID, memberID, conditionID)
		select o.orgID, nullIf(tmpM.listitem,0) as memberID, nullIf(tmpC.listitem,0) as conditionID
		from (select @orgID as orgID) as o
		outer apply membercentral.dbo.fn_intListToTable(@memberIDList,',') as tmpM
		outer apply membercentral.dbo.fn_intListToTable(@conditionIDList,',') as tmpC
			except
		select qid.orgID, qid.memberID, qid.conditionID
		from platformQueue.dbo.tblQueueItems_processMemberGroups as qid
		inner join platformQueue.dbo.tblQueueItems as qi on qi.itemUID = qid.itemUID
		inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.queueStatusID
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'popCondCache'
		and qs.queueStatus not in ('grabbedForProcessing','processingItem','done')

	-- queue items
	insert into platformQueue.dbo.tblQueueItems_processMemberGroups (itemUID, itemGroupUID, orgID, memberID, conditionID)
		OUTPUT inserted.itemUID, inserted.dateAdded, @statusInserting 
		INTO platformQueue.dbo.tblQueueItems(itemUID, dateAdded, queueStatusID)
	select itemUID, @itemGroupUID, orgID, memberID, conditionID
	from #tmpQueue

	-- update queue item groups to show ready to process
	update qi WITH (UPDLOCK, HOLDLOCK)
	set qi.queueStatusID = @statusReady,
		qi.dateUpdated = getdate()
	from platformQueue.dbo.tblQueueItems as qi
	inner join #tmpQueue as s on s.itemUID = qi.itemUID

	IF OBJECT_ID('tempdb..#tmpQueue') IS NOT NULL 
		DROP TABLE #tmpQueue

	IF @TranCounter = 0
		COMMIT TRAN;

END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH


declare @msgXML xml
set @msgXML = '<itemGroupUID>' + cast(@itemGroupUID as varchar(60)) + '</itemGroupUID>'

-- if we are processing immediately
IF @runSchedule = 1 
BEGIN
	BEGIN TRY
		-- Log
		declare @logTreeID uniqueidentifier
		set @logTreeID = NEWID()
		INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorMessage, QueueMessage)
		VALUES (@logTreeID, OBJECT_NAME(@@PROCID), 'Starting immediate processing', @msgXML);

		EXEC dbo.queue_processMemberGroups_process @itemGroupUID=@itemGroupUID, @logTreeID=@logTreeID
	END TRY
	BEGIN CATCH
		-- Log
		INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, RunningProc, ErrorSeverity, ErrorMessage, ErrorLine, ErrorProc)
		VALUES (@logTreeID, OBJECT_NAME(@@PROCID), ERROR_SEVERITY(), ERROR_MESSAGE(), ERROR_LINE(), ERROR_PROCEDURE());

		-- if deadlock, put itemGroupUID into the queue as ready to run when it can
		IF (ERROR_MESSAGE() like '%Errno 1205:%'
			OR ERROR_MESSAGE() like '%Violation of UNIQUE KEY constraint ''IX_cache_members_groups''.%' 
			OR ERROR_MESSAGE() like '%Violation of PRIMARY KEY constraint ''PK_cache_members_conditions''.%') 
			EXEC dbo.queue_processMemberGroups_requeue @itemGroupUID, @logTreeID			
		ELSE BEGIN
			EXEC membercentral.dbo.up_errorhandler
			RETURN -1
		END	
	END CATCH
END
ELSE 
	-- if we are delayed processing, use service broker
	IF @runSchedule = 2 BEGIN
		EXEC dbo.sb_ProcessMemberGroupsSendMessage @msgXML
	END

RETURN 0
GO

