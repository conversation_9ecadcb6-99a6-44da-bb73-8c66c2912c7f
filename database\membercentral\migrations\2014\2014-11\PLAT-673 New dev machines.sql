use membercentral
GO

CREATE FUNCTION dbo.fn_getServerSettings()
RETURNS TABLE
AS
RETURN (
	select 
		smtpserver = 
			case 
			when @@SERVERNAME IN ('DEV04\PLATFORM2008','MCDEV01\PLATFORM2008','DEV03\PLATFORM2008','MCBETA01\PLATFORM2008') then 'mail.trialsmith.com'
			else '10.36.18.90'
			end,
		tier = 
			case
			when @@SERVERNAME IN ('DEV04\PLATFORM2008','MCDEV01\PLATFORM2008') then 'DEVELOPMENT'
			when @@SERVERNAME IN ('DEV03\PLATFORM2008','MCBETA01\PLATFORM2008') then 'BETA'
			else 'PRODUCTION'
			end,
		pathToRaidWebTemp =
			case
			when @@SERVERNAME IN ('DEV04\PLATFORM2008','MCDEV01\PLATFORM2008') then '\\mcdev01\wwwRoot\membercentral\temp\'
			when @@SERVERNAME IN ('DEV03\PLATFORM2008','MCBETA01\PLATFORM2008') then '\\mcbeta01\wwwRoot\membercentral\temp\'
			else '\\tsfile1\platform\membercentral\temp\'
			end,
		urlToWebTemp =
			case
			when @@SERVERNAME IN ('DEV04\PLATFORM2008','MCDEV01\PLATFORM2008') then 'http://mc.dev.membercentral.com/temp/'
			when @@SERVERNAME IN ('DEV03\PLATFORM2008','MCBETA01\PLATFORM2008') then 'http://mc.beta.membercentral.com/temp/'
			else 'http://mc.prod.membercentral.com/temp/'
			end,
		pathToTlasitesRoot =
			case
			when @@SERVERNAME IN ('DEV04\PLATFORM2008','MCDEV01\PLATFORM2008') then '\\mcdev01\tlasites\www\'
			when @@SERVERNAME IN ('DEV03\PLATFORM2008','MCBETA01\PLATFORM2008') then '\\mcbeta01\tlasites\www\'
			else '\\tsfile1\f$\tlasites\www\'
			end
)
GO

ALTER PROC [dbo].[migrate_store]
@orgcode varchar(5)

AS
-- this can be called if orgcode on old, sitecode on new, and orgcode on new are all the same
-- requires store to have been created

set nocount on

DECLARE @errCode int, @siteID int, @storeID int, @storeContentID int, @rc int, @minCatID int, @minRateID int,
	@newCatID int, @minPCID int, @newPCID int, @minItemID int, @appCreatedContentResourceTypeID int,
	@appInstanceResourceID int, @activeSiteResourceStatusID int, @contentID int,  @summaryContentID int, 
	@contentSiteResourceID int, @summaryContentSiteResourceID int, @newItemID int, @minFormatID int, @newFormatID int
DECLARE @oldRoot varchar(26), @ContentPath varchar(60), @ptitle varchar(200),
	@pid varchar(50), @pImage varchar(400), @tImage varchar(400)
DECLARE @rawContent varchar(max), @pDesc varchar(max)
DECLARE @showQuantity bit
DECLARE @store_Categories TABLE (oldcategoryID int, newcategoryID int)
DECLARE @store_PriceCodes TABLE (oldPriceCodeid int, newPriceCodeID int)
DECLARE @store_products TABLE (olditemID int, newItemID int)
DECLARE @store_productFormats TABLE (oldformatid int, newformatid int)
DECLARE @store_Rates TABLE (id int IDENTITY(1,1) NOT NULL, newformatid int, price money, shippingcosts money, ratename varchar(100))
DECLARE @tblProducts TABLE (ItemID int, ProductID varchar(50), ProductName varchar(200), 
	ProductDesc varchar(max), productImage varchar(400), thumbnailImage varchar(400), 
	ShowQuantity bit)

SET @errCode = 0
SET @siteID = dbo.fn_getSiteIDFromSiteCode(@orgcode)
SET @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')
SET @activeSiteResourceStatusID = dbo.fn_getResourceStatusId('Active')
SELECT @oldRoot = pathToTlasitesRoot from membercentral.dbo.fn_getServerSettings()

-- store needed
SELECT @storeID = storeID from dbo.store where siteID = @siteID
IF @storeID is null BEGIN
	SET @errCode = 900
	GOTO on_done
END

-- merchant profile needed
IF NOT EXISTS (select storeID from dbo.store_merchantProfiles where storeID = @storeID) BEGIN
	SET @errCode = 901
	GOTO on_done
END

-- shipping method needed
IF NOT EXISTS (select shippingID from dbo.store_ShippingMethods where storeID = @storeID) BEGIN
	SET @errCode = 901
	GOTO on_done
END


SELECT @appInstanceResourceID = siteResourceID
	FROM dbo.cms_applicationInstances as ai
	INNER JOIN dbo.store as s on s.applicationInstanceID = ai.applicationInstanceID
	WHERE s.storeID = @storeID
SELECT @storeContentID = mainContentID from dbo.store where siteID = @siteID
SELECT @ContentPath = @oldRoot + @orgcode + '\views\StoreMainContent.cfm'
SELECT @rawContent = dbo.fn_readFile(@ContentPath,0,0)
	IF @@ERROR <> 0 BEGIN
		SET @errCode = 902
		GOTO on_done
	END

BEGIN TRAN

	-- store main content
	IF @storeContentID is not null BEGIN
		exec @rc = dbo.cms_updateContent @contentID=@storeContentID, @languageID=1, @isSSL=0,
			@isHTML=1, @contentTitle='StoreMain', @contentDesc='', @rawcontent=@rawContent
			IF @@ERROR <> 0 OR @rc <> 0 BEGIN
				SET @errCode = 903
				GOTO on_done
			END
	END

	-- store categories
	SELECT @minCatID = min(categoryID)
		FROM tlasites.trialsmith.dbo.storeCategories
		WHERE orgCode = @orgcode
	WHILE @minCatID is not null BEGIN
		INSERT INTO dbo.store_Categories (storeID, CategoryName)
		SELECT @storeID, categoryName
		FROM tlasites.trialsmith.dbo.storeCategories
		WHERE orgCode = @orgcode
		AND categoryID = @minCatID
			IF @@ERROR <> 0 BEGIN
				SET @errCode = 904
				GOTO on_done
			END
			select @newCatID = SCOPE_IDENTITY()

		INSERT INTO @store_Categories (oldcategoryID, newCategoryID)
		VALUES (@minCatID, @newCatID)
			IF @@ERROR <> 0 BEGIN
				SET @errCode = 905
				GOTO on_done
			END

		SELECT @minCatID = min(categoryID)
			FROM tlasites.trialsmith.dbo.storeCategories
			WHERE orgCode = @orgcode
			AND categoryID > @minCatID
	END

	-- get products
	INSERT INTO @tblProducts (ItemID, ProductID, ProductName, ProductDesc, productImage, thumbnailImage, ShowQuantity)
	SELECT ItemID, ProductID, ProductName, Details, ImageURL, Thumbnail, ShowQuantity = case ShowQuantity WHEN 'Yes' THEN 1 ELSE 0 END
	FROM tlasites.trialsmith.dbo.storeProducts
	WHERE orgCode = @orgcode
		IF @@ERROR <> 0 BEGIN
			SET @errCode = 908
			GOTO on_done
		END

	select @minItemID = min(itemID) from @tblProducts
	while @minItemID is not null BEGIN
		select @ptitle = ProductName, @pDesc = ProductDesc, @pID = ProductID,
			@pImage = productImage, @tImage = thumbnailImage, @showQuantity = ShowQuantity
			from @tblProducts
			where ItemID = @minItemID

		-- create contentID
		EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, 
			@resourceTypeID=@appCreatedContentResourceTypeID, 
			@parentSiteResourceID=@appInstanceResourceID,
			@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=1, 
			@isActive=1, @contentTitle=@ptitle, @contentDesc=null, @rawContent=@pDesc, @memberID=NULL,
			@contentID=@contentID OUTPUT, @siteResourceID=@contentSiteResourceID OUTPUT
			IF @@ERROR <> 0 OR @RC <> 0 OR @contentID = 0 BEGIN
				SET @errCode = 909
				GOTO on_done
			END


		-- create summary contentID  Create empty summaries.
		EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, 
			@resourceTypeID=@appCreatedContentResourceTypeID, 
			@parentSiteResourceID=@appInstanceResourceID,
			@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=0, @languageID=1, 
			@isActive=1, @contentTitle=@ptitle, @contentDesc=null, @rawContent='', @memberID=NULL,
			@contentID=@summaryContentID OUTPUT, @siteResourceID=@summaryContentSiteResourceID OUTPUT
			IF @@ERROR <> 0 OR @RC <> 0 OR @contentID = 0 BEGIN
				SET @errCode = 909
				GOTO on_done
			END

		-- add product
		INSERT INTO dbo.store_products (storeID, productID, productContentID, summaryContentID, productImage, thumbnailImage, showQuantity, status)
		VALUES (@storeid, @pID, @contentID, @summaryContentID, @pImage, @tImage, @showQuantity, 'A')
			IF @@ERROR <> 0 BEGIN
				SET @errCode = 910
				GOTO on_done
			END
			SELECT @newItemID = SCOPE_IDENTITY()


		INSERT INTO @store_products (oldItemID, newItemID)
		VALUES (@minItemID, @newItemID)
			IF @@ERROR <> 0 BEGIN
				SET @errCode = 911
				GOTO on_done
			END

		select @minItemID = min(itemID) from @tblProducts where itemID > @minItemID
	END

	-- products and categories
	INSERT INTO dbo.store_ProductCategoryLinks(ItemID, CategoryID)
	SELECT newp.newItemID, newc.newCategoryID
	FROM tlasites.trialsmith.dbo.storeProductCategoryLinks as oldpl
	INNER JOIN tlasites.trialsmith.dbo.storeProducts as oldp ON oldp.itemID = oldpl.itemID AND oldp.orgCode = @orgcode
	inner join @store_products as newp on newp.oldItemID = oldp.itemid
	inner join @store_Categories as newc on newc.oldcategoryID = oldpl.categoryID
		IF @@ERROR <> 0 BEGIN
			SET @errCode = 912
			GOTO on_done
		END

	-- product formats
	select @minFormatID = min(pf.formatid)
		FROM tlasites.trialsmith.dbo.storeProductFormats as pf
		INNER JOIN tlasites.trialsmith.dbo.storeProducts as p ON p.itemID = pf.itemID 
			AND p.orgCode = @orgcode
	while @minFormatID is not null BEGIN
		INSERT INTO dbo.store_ProductFormats(Itemid, Name, status)
		select newp.newItemID, pf.name, 'A'
		FROM tlasites.trialsmith.dbo.storeProductFormats as pf
		inner join @store_products as newp on newp.oldItemID = pf.Itemid
		where pf.formatID = @minFormatID
			IF @@ERROR <> 0 BEGIN
				SET @errCode = 913
				GOTO on_done
			END
			SELECT @newformatID = SCOPE_IDENTITY()

		INSERT INTO @store_ProductFormats (oldformatid, newformatid)
		VALUES (@minFormatID, @newformatID)
			IF @@ERROR <> 0 BEGIN
				SET @errCode = 914
				GOTO on_done
			END

		select @minFormatID = min(pf.formatid)
			FROM tlasites.trialsmith.dbo.storeProductFormats as pf
			INNER JOIN tlasites.trialsmith.dbo.storeProducts as p ON p.itemID = pf.itemID 
				AND p.orgCode = @orgcode
			WHERE pf.formatid > @minFormatID
	END

	-- store rates
	INSERT INTO @store_Rates (newformatid, price, shippingcosts, ratename)
	SELECT newpf.newformatid, pp.price, pp.ShippingCosts, spc.name
	FROM tlasites.trialsmith.dbo.storeProductPrices pp
	INNER JOIN tlasites.trialsmith.dbo.storeProductFormats pf ON pp.formatID = pf.formatID
	INNER JOIN tlasites.trialsmith.dbo.storeProducts p ON p.itemID = pf.itemID AND p.orgCode = @orgcode
	INNER JOIN @store_ProductFormats as newpf on newpf.oldformatid = pp.formatid
	INNER JOIN tlasites.trialsmith.dbo.storePriceCodes spc on spc.pricecodeid = pp.pricecodeid

	declare @price money, @rateName varchar(100), @trashRID int, @trashSRID int

	SELECT @minRateID = min(id)
		FROM @store_Rates
	WHILE @minRateID is not null BEGIN
		SELECT @newFormatID = newformatid, @price=price, @rateName=rateName
			FROM @store_Rates
			WHERE ID = @minRateID

		EXEC dbo.store_createRate
			@formatID=@newFormatID, 
			@GLAccountID = NULL, 
			@ShippingGLAccountID = NULL, 
			@rateName = @rateName, 
			@reportCode = NULL, 
			@rate = @price, 
			@startDate = '1/1/2014', 
			@endDate=NULL, 
			@rateID=@trashRID, 
			@siteResourceID=@trashSRID
			
			IF @@ERROR <> 0 BEGIN
				SET @errCode = 917
				GOTO on_done
			END

		SELECT @minRateID = min(ID)
			FROM @store_Rates
			WHERE  ID > @minRateID
	END

COMMIT TRAN


on_done:
	IF @errCode = 900
		print 'Migration stopped. Store instance is not installed.' 
	IF @errCode = 901
		print 'Migration stopped. Store has no merchant profiles.' 
	IF @errCode = 916
		print 'Migration stopped. Store has no shipping methods.' 
	IF @errCode = 902
		print 'Migration stopped. Unable to read StoreMainContent.' 
	IF @errCode = 903
		print 'Migration stopped. Unable to update store content.' 
	IF @errCode = 904
		print 'Migration stopped. Unable to create new category.' 
	IF @errCode = 905
		print 'Migration stopped. Unable to insert into @store_Categories.' 
	IF @errCode = 906
		print 'Migration stopped. Unable to create new price code.' 
	IF @errCode = 907
		print 'Migration stopped. Unable to insert into @store_PriceCodes.' 
	IF @errCode = 908
		print 'Migration stopped. Unable to get products.' 
	IF @errCode = 909
		print 'Migration stopped. Unable to create new content object for ' + @ptitle + '.' 
	IF @errCode = 910
		print 'Migration stopped. Unable to insert into store_products.' 
	IF @errCode = 911
		print 'Migration stopped. Unable to insert into @store_products.' 
	IF @errCode = 912
		print 'Migration stopped. Unable to insert into store_ProductCategoryLinks.' 
	IF @errCode = 913
		print 'Migration stopped. Unable to insert into store_ProductFormats.' 
	IF @errCode = 914
		print 'Migration stopped. Unable to insert into @store_ProductFormats.' 
	IF @errCode = 915
		print 'Migration stopped. Unable to insert into store_ProductPrices.' 
	IF @errCode = 917
		print 'Migration stopped. Unable to insert into store_rates.' 

	IF @errCode >= 903
		ROLLBACK TRAN

	print 'Migration migrate_store finished.'
GO

ALTER PROC [dbo].[sub_setupIssuesReport]
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20), @errorContent varchar(max)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)
SET @crlf = char(13) + char(10)
SELECT @smtpserver = smtpserver, @tier = tier from membercentral.dbo.fn_getServerSettings()

/* ************************************************* */
/* Subscription Rate Frequencies with no Pay Profile */
/* ************************************************* */
IF EXISTS (select top 1 * from dbo.fn_sub_getSubscriptionRateFrequenciesWithoutPayProfile()) BEGIN
	BEGIN TRY
		SET @errorContent = ''
		SET @errorContent = '' + 
			replace(Stuff((
				SELECT '||' + sitecode + char(9) + 'Schedule: ' + scheduleName + '|' + char(9) + 'Rate: ' + ratename + '|' + char(9) + 'Freq: ' + frequencyName + '|' + char(9) + 'Subs: ' + replace(affectedSubscriptions,'|','; ') AS [text()]
				from dbo.fn_sub_getSubscriptionRateFrequenciesWithoutPayProfile()
				order by sitecode, scheduleName, rateName, frequencyName
				FOR XML PATH ('')
			),1,1,''),'|',@crlf)
		SET @errorSubject = @tier + ' - Non-Developer Needed - Subscription Rate Frequencies with no Pay Profile'
		SET @errorContent = @tier + ' - There are subscription rate frequencies with no payment profile defined. These need to be defined for successful processing of subscriptions.' + @crlf + @errorContent
		EXEC membercentral.dbo.sys_sendEmail 
			@from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errorContent, 
			@priority='high', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END TRY
	BEGIN CATCH
		SET @errorSubject = @tier + ': Error Running Subscription Rate Frequencies with no Pay Profile'
		SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
		SELECT @errorContent = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
		EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END CATCH
END

/* ******************************* */
/* Subscription Dates Out of Order */
/* ******************************* */
IF EXISTS (select top 1 * from dbo.fn_sub_getSubscriptionDatesOutOfOrder()) BEGIN
	BEGIN TRY
		SET @errorContent = ''
		SET @errorContent = '' + 
			replace(Stuff((
				SELECT '||' + sitecode + char(9) + 'Schedule: ' + scheduleName + '|' + char(9) + 'Rate: ' + ratename + '|' + char(9) + 'Subs: ' + replace(affectedSubscriptions,'|','; ') AS [text()]
				from dbo.fn_sub_getSubscriptionDatesOutOfOrder()
				order by sitecode, scheduleName, rateName
				FOR XML PATH ('')
			),1,1,''),'|',@crlf)
		SET @errorSubject = @tier + ' - Non-Developer Needed - Subscription Dates Out of Order'
		SET @errorContent = @tier + ' - There are subscriptions with dates out of order -- grace end dates before subscription end dates, availability end dates before start dates, etc. These need to be corrected for successful processing of subscriptions. It may be an indicator of missing advance formulas.' + @crlf + @errorContent
		EXEC membercentral.dbo.sys_sendEmail 
			@from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errorContent, 
			@priority='high', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END TRY
	BEGIN CATCH
		SET @errorSubject = @tier + ': Error Running Subscription Dates Out of Order'
		SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
		SELECT @errorContent = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
		EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END CATCH
END

/* ******************************************* */
/* Subscription Rates Missing Advance Formulas */
/* ******************************************* */
IF EXISTS (select top 1 * from dbo.fn_sub_getSubscriptionRatesMissingAdvanceFormulas()) BEGIN
	BEGIN TRY
		SET @errorContent = ''
		SET @errorContent = '' + 
			replace(Stuff((
				SELECT '||' + sitecode + char(9) + 'Schedule: ' + scheduleName + '|' + char(9) + 'Rate: ' + ratename
				from dbo.fn_sub_getSubscriptionRatesMissingAdvanceFormulas()
				order by sitecode, scheduleName, rateName
				FOR XML PATH ('')
			),1,1,''),'|',@crlf)
		SET @errorSubject = @tier + ' - Non-Developer Needed - Subscription Rates Missing Advance Formulas'
		SET @errorContent = @tier + ' - There are subscriptions with rates that are missing at least one advance formula. These need to be corrected for successful processing of subscriptions.' + @crlf + @errorContent
		EXEC membercentral.dbo.sys_sendEmail 
			@from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errorContent, 
			@priority='high', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END TRY
	BEGIN CATCH
		SET @errorSubject = @tier + ': Error Running Subscription Rates Missing Advance Formulas'
		SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
		SELECT @errorContent = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
		EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END CATCH
END
GO

DROP PROC dbo.sub_importSubscriptions_toPermNOQUEUE
GO

ALTER PROC [dbo].[sub_importSubscriptions_toPerm]
@siteID int,
@recordedByMemberID int,
@flatfile varchar(160),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

DECLARE @orgID int, @createSQL varchar(max), @cmd varchar(400), @minRowID int, @currMemberID int, 
	@currSubscriptionID int, @currRFID int, @currGLAccountID int, @currStartDate datetime, @currEndDate datetime, 
	@currGraceEndDate datetime, @currActivationOptionCode varchar(1), @currSubscriberID int, @subCost money, @currStatus char(1),
	@parentSubscriberID int, @StoreModifiedRate varchar(6), @rc int, @renewLinkPath varchar(200), @renewLinkUrl varchar(200),
	@renewLinkText varchar(max), @renewLinkTrash bit, @renewLinkUrltext nvarchar(max)
select @orgID = orgID from dbo.sites where siteID = @siteID
SELECT @renewLinkPath = pathToRaidWebTemp, @renewLinkUrl = urlToWebTemp from membercentral.dbo.fn_getServerSettings()

IF OBJECT_ID('tempdb..#tblSubImpErrors') IS NOT NULL 
	DROP TABLE #tblSubImpErrors
CREATE TABLE #tblSubImpErrors (rowid int IDENTITY(1,1), msg varchar(max), fatal bit)

-- see if files exist
if dbo.fn_fileExists(@flatfile + '.sql') = 0
	or dbo.fn_fileExists(@flatfile + '.txt') = 0
	or dbo.fn_fileExists(@flatfile + '.bcp') = 0
BEGIN
	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES ('Unable to read subscription data files.',1)

	GOTO on_done
END

-- **************
-- read in sql create script and create global temp table
-- **************
BEGIN TRY
	SET @createSQL = replace(dbo.fn_ReadFile(@flatfile + '.sql',0,1),'##xxx','##importSubsData')
	IF OBJECT_ID('tempdb..##importSubsData') IS NOT NULL
		DROP TABLE ##importSubsData
	EXEC(@createSQL)
END TRY
BEGIN CATCH
	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES ('Unable to create holding table for subscription data.',1)

	GOTO on_done
END CATCH


-- *******************
-- bcp in data
-- *******************
BEGIN TRY
	set @cmd = 'bcp ##importSubsData in ' + @flatfile + '.bcp -f ' + @flatfile + '.txt -n -T -S' + CAST(serverproperty('servername') as varchar(40))
	exec master..xp_cmdshell @cmd, NO_OUTPUT
END TRY
BEGIN CATCH
	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES ('Unable to import data into holding table.',1)

	GOTO on_done
END CATCH


-- *******************
-- immediately put into local temp table and drop global temp
-- *******************
BEGIN TRY
	IF OBJECT_ID('tempdb..#importSubsData') IS NOT NULL
		DROP TABLE #importSubsData
	select * into #importSubsData from ##importSubsData
	IF OBJECT_ID('tempdb..##importSubsData') IS NOT NULL
		DROP TABLE ##importSubsData
END TRY
BEGIN CATCH
	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES ('Unable to import data into local temporary table.',1)

	GOTO on_done
END CATCH


/* ************** */
/* populate queue */
/* ************** */
BEGIN TRY
	IF OBJECT_ID('tempdb..#tmpSubscriberTrees') IS NOT NULL 
		DROP TABLE #tmpSubscriberTrees

	CREATE TABLE #tmpSubscriberTrees (memberID int, treeCode varchar(50), itemUID uniqueidentifier DEFAULT NEWID())
	INSERT INTO #tmpSubscriberTrees (memberID, treeCode)
	select distinct memberID, treeCode
	from #importSubsData

	-- all get the same ItemGroupUID
	declare @itemGroupUID uniqueidentifier
	select @itemGroupUID = NEWID()

	declare @statusInserting int, @statusReady int
	select @statusInserting = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'addSubscribers'
		and qs.queueStatus = 'insertingItems'
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'addSubscribers'
		and qs.queueStatus = 'readyToProcess'

	insert into platformQueue.dbo.tblQueueItems_addSubscribers (itemUID, itemGroupUID, recordedByMemberID, orgID, siteID, memberID, treeCode)
		OUTPUT inserted.itemUID, @statusInserting 
		INTO platformQueue.dbo.tblQueueItems(itemUID, queueStatusID)
	select distinct itemUID, @itemGroupUID, @recordedByMemberID, @orgID, @siteID, memberID, treeCode
	from #tmpSubscriberTrees

	insert into platformQueue.dbo.tblQueueItems_addSubscribersDetail (itemUID, rowID, subscriptionID, parentSubscriptionID, RFID, 
		rateUID, subStartDate, subEndDate, graceEndDate, [status], lastPrice, storeModifiedRate)
	select tmpT.itemUID, tmp.rowID, tmp.subID, tmp.parentSubID, tmp.RFID, tmp.rateUID, tmp.StartDate, tmp.EndDate, tmp.graceEndDate, 
		tmp.status, tmp.lastPrice, tmp.storeModifiedRate
	from #importSubsData as tmp
	inner join #tmpSubscriberTrees as tmpT on tmpT.memberID = tmp.memberID and tmpT.treeCode = tmp.TreeCode

	-- update queue item groups to show ready to process
	update qi WITH (UPDLOCK, HOLDLOCK)
	set qi.queueStatusID = @statusReady,
		dateUpdated = getdate()
	from platformQueue.dbo.tblQueueItems as qi
	inner join #tmpSubscriberTrees as tmpT on tmpT.itemUID = qi.itemUID

	IF OBJECT_ID('tempdb..#tmpSubscriberTrees') IS NOT NULL 
		DROP TABLE #tmpSubscriberTrees
END TRY
BEGIN CATCH
	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES ('Unable to add subscriptions to the queue.',1)

	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES (error_message(),0)

	GOTO on_done
END CATCH


on_done:
	select @importResult = (
		select getdate() as "@date", @flatfile as "@flatfile", 
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg", "@severity" = case fatal when 1 then 'fatal' else 'nonfatal' end
			from #tblSubImpErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE)

	IF OBJECT_ID('tempdb..#tblSubImpErrors') IS NOT NULL 
		DROP TABLE #tblSubImpErrors
	IF OBJECT_ID('tempdb..##importSubsData') IS NOT NULL
		DROP TABLE ##importSubsData
	IF OBJECT_ID('tempdb..#importSubsData') IS NOT NULL
		DROP TABLE #importSubsData
	IF OBJECT_ID('tempdb..#tmpSubscriberTrees') IS NOT NULL 
		DROP TABLE #tmpSubscriberTrees
GO

ALTER PROC [dbo].[ams_adminCountReport]
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20), @errorContent varchar(max)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)
SET @crlf = char(13) + char(10)
SELECT @smtpserver = smtpserver, @tier = tier from membercentral.dbo.fn_getServerSettings()


/* ********************************** */
/* Orgs exceeding defined admin count */
/* ********************************** */
BEGIN TRY
	IF OBJECT_ID('tempdb..#tblOrgs') IS NOT NULL 
		DROP TABLE #tblOrgs
	select o.orgcode, o.orgname, o.allowedAdminCount, count(distinct m.memberID) as memberCount
	into #tblOrgs
	from dbo.ams_groups as g
	inner join dbo.organizations as o on o.orgID = g.orgID
	left outer join dbo.cache_members_groups as cmg 
		inner join dbo.ams_members as m on m.memberID = cmg.memberID and m.status <> 'D' and m.memberID = m.activeMemberID
		on cmg.groupID = g.groupID
	where g.groupCode = 'SiteAdmins'
	and g.isSystemGroup = 1
	and nullIf(o.allowedAdminCount,0) is not null
	group by o.orgcode, o.orgname, o.allowedAdminCount
	having count(distinct m.memberID) > o.allowedAdminCount 

	IF EXISTS (select top 1 orgcode from #tblOrgs) BEGIN
		SET @errorContent = ''
		SET @errorContent = '' + 
			replace(Stuff((
				SELECT '||' + orgcode + ' - ' + orgname + '|allowed count: ' + cast(allowedAdminCount as varchar(5)) + '|actual count: ' + cast(memberCount as varchar(5))
				from #tblOrgs
				order by orgcode
				FOR XML PATH ('')
			),1,1,''),'|',@crlf)
		SET @errorSubject = @tier + ' - Allowed Admin Count Exceeded'
		SET @errorContent = @tier + ' - The allowed admin count has been exceeded for the following organizations.' + @crlf + @errorContent
		EXEC membercentral.dbo.sys_sendEmail 
			@from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errorContent, 
			@priority='normal', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END

	IF OBJECT_ID('tempdb..#tblOrgs') IS NOT NULL 
		DROP TABLE #tblOrgs
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Orgs exceeding defined admin count'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errorContent = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* ******************************** */
/* Orgs missing defined admin count */
/* ******************************** */
BEGIN TRY
	IF OBJECT_ID('tempdb..#tblOrgs2') IS NOT NULL 
		DROP TABLE #tblOrgs2
	select distinct o.orgcode + ' - ' + o.orgName as org
	into #tblOrgs2
	from dbo.organizations as o
	inner join dbo.sites as s on s.orgID = o.orgID
	inner join dbo.sub_types as st on st.siteID = s.siteID
	where nullIf(o.allowedAdminCount,0) is null

	IF EXISTS (select top 1 org from #tblOrgs2) BEGIN
		SET @errorContent = ''
		SET @errorContent = '' + 
			replace(Stuff((
				SELECT '||' + org
				from #tblOrgs2
				order by org
				FOR XML PATH ('')
			),1,1,''),'|',@crlf)
		SET @errorSubject = @tier + ' - Allowed Admin Count Missing'
		SET @errorContent = @tier + ' - The allowed admin count has not been set for the following organizations.' + @crlf + @errorContent
		EXEC membercentral.dbo.sys_sendEmail 
			@from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errorContent, 
			@priority='normal', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END

	IF OBJECT_ID('tempdb..#tblOrgs2') IS NOT NULL 
		DROP TABLE #tblOrgs2
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Orgs missing defined admin count'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errorContent = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH
GO

ALTER PROC [dbo].[ams_createMemberSiteDefaultsByOrgID]
@orgID int

AS

-- tl 7/2010 - only consider where memberid=activememberid when populating

DECLARE @tmpMembers TABLE (autoid int IDENTITY(1,1), siteID int, [status] char(1), memberID int)
DECLARE @memCount int, @memCount2 int, @message varchar(max)
DECLARE @tblUN TABLE (autoid int IDENTITY(1,1), un varchar(40))
DECLARE @tblPW TABLE (autoid int IDENTITY(1,1), pw varchar(40))
DECLARE @orgcode varchar(10)
SELECT @orgcode = orgcode from dbo.organizations where orgID = @orgID

select @message = 'Running ams_createMemberSiteDefaultsByOrgID for orgcode ' + @orgcode + char(10) + char(10)

-- members in groups that are not linked to a profile and do not have def u/p
INSERT INTO @tmpMembers (siteID, [status], memberID)
select distinct lg.siteid, m.status, mg.memberid
from (
	-- sites in this org and the groups that can login
	select s.siteid, srr.groupID
	from dbo.sites as s
	inner join dbo.cms_siteResourceRightsCache AS srr on srr.resourceID = s.siteResourceID
	INNER JOIN dbo.cms_siteResourceFunctions AS srf ON srf.functionID = srr.functionID and srf.functionname = 'Login'
	INNER JOIN dbo.cms_siteResourceTypeFunctions AS srtf ON srtf.functionID = srf.functionID
	INNER JOIN dbo.cms_siteResourceTypes AS srt ON srt.resourceTypeID = srtf.resourceTypeID and srt.resourceType = 'Site'
	where s.orgid = @orgID
) as lg
inner join dbo.cache_members_groups as mg on mg.groupid = lg.groupid
inner join dbo.ams_groups as g on g.groupid = mg.groupID AND g.status <> 'D'
inner join dbo.ams_members as m on m.memberid = mg.memberid 
	and m.status <> 'D'
	and m.memberid = m.activeMemberID
left outer join dbo.ams_memberNetworkProfiles as mnp
	inner join dbo.ams_networkProfiles as np on np.profileid = mnp.profileid and np.status <> 'D' and mnp.status <> 'D'
	on mnp.memberid = mg.memberid
left outer join dbo.ams_memberSiteDefaults as msd on msd.memberid = mg.memberid and msd.siteid = lg.siteid and msd.status <> 'D'
where mnp.mnpID is null
and msd.defaultID is null
order by siteid
	select @memCount = @@ROWCOUNT
	select @message = @message + cast(@memCount as varchar(6)) + ' members found that need defaults.' + char(10)

-- create a pool of default usernames
INSERT INTO @tblUN (un)
SELECT TOP (@memCount) rntbl2.un
FROM (
	select randomNumber, @orgcode + randomNumber as un
	from (
		SELECT cast(abs(cast(newid() as binary(6)) %999999) + 1 as varchar(6)) as randomNumber
		FROM dbo.F_TABLE_NUMBER_RANGE(1,@memCount*5)
	) as rntbl
) as rntbl2
LEFT OUTER JOIN dbo.ams_memberSiteDefaults as msd on msd.defaultusername = rntbl2.un
LEFT OUTER JOIN dbo.ams_networkprofiles as np on np.username = rntbl2.un 
WHERE msd.defaultID is null
and np.profileID is null
GROUP BY rntbl2.randomNumber, rntbl2.un
HAVING rntbl2.randomNumber > 100000
AND Count(rntbl2.un) = 1
	select @message = @message + cast(@@ROWCOUNT as varchar(6)) + ' default usernames created.' + char(10)

-- create a pool of default passwords
INSERT INTO @tblPW (pw)
SELECT TOP (@memCount) sample.randomNumber
FROM (
	SELECT cast(abs(cast(newid() as binary(6)) %999999) + 1 as varchar(6)) as randomNumber
	FROM dbo.F_TABLE_NUMBER_RANGE(1,@memCount*5)) as sample
GROUP BY sample.randomNumber
HAVING sample.randomNumber > 100000
AND Count(sample.randomNumber) = 1
	select @message = @message + cast(@@ROWCOUNT as varchar(6)) + ' default passwords created.' + char(10)

-- add defaults
INSERT INTO dbo.ams_memberSiteDefaults (memberID, siteID, defaultUsername, defaultPassword, [status], dateCreated)
SELECT m.memberID, m.siteID, u.un, p.pw, m.status, getdate()
FROM @tmpMembers as m
inner join @tblUN as u on u.autoID = m.autoID
inner join @tblPW as p on p.autoID = m.autoID
where dbo.fn_checkConstraint_UsernamePassword(m.siteID,null,u.un,p.pw) = 1
	select @memCount2 = @@ROWCOUNT
	select @message = @message + cast(@memCount2 as varchar(6)) + ' defaults entered into database.' + char(10)

-- if counts are different, email about difference
IF @memCount2 <> @memCount BEGIN
	DECLARE @smtpserver varchar(20)
	SELECT @smtpserver = smtpserver	from membercentral.dbo.fn_getServerSettings()

	select @message = @message + 'Need to manually run "EXEC ams_createMemberSiteDefaultsByOrgID @orgID=' + cast(@orgID as varchar(8)) + '"' + char(10)

	EXEC dbo.sys_sendEmail 
		@from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject='createMemberSiteDefaultsByOrgID difference detected',
		@message=@message, @priority='high', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END

RETURN 0
GO


ALTER PROCEDURE [dbo].[cache_perms_autoCorrect] 
AS
BEGIN

	DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
	SELECT @smtpserver = smtpserver, @tier = tier from membercentral.dbo.fn_getServerSettings()

	declare @correctionID int, @thisSiteID int, @startDate datetime, @endDate datetime
	declare @affectedSites TABLE ([siteID] [int] NOT NULL)

	declare @myCorrections TABLE (
		[siteID] [int] NOT NULL,
		[roleID] [int] NOT NULL,
		[resourceID] [int] NOT NULL,
		[functionID] [int] NOT NULL,
		[groupID] [int] NOT NULL,
		[include] [bit] NOT NULL,
		[universalRoleResourceRightsID] [int] NULL,
		[universalRoleResourceTypeID] [int] NULL)

	set @startDate = getdate()

	IF OBJECT_ID('tempdb..#siteResourcesToCheck') IS NOT NULL
		DROP TABLE #siteResourcesToCheck

	select distinct 
		s.siteID,roles.roleID,
		srr.resourceID, srtf.functionID, srr.groupID, srr.include, srr.resourceRightsID as universalRoleResourceRightsID, srt.resourceTypeID as universalRoleResourceTypeID
	into #siteResourcesToCheck
	from dbo.cms_siteResourceRoles roles
	inner join dbo.cms_siteResourceRoleTypes srrt
		on srrt.roleTypeID = roles.roleTypeID
		and srrt.roleTypeName = 'UniversalRole'
	inner join dbo.cms_siteResourceRights srr
		on srr.roleID = roles.roleID
	inner join dbo.sites s
		on s.siteResourceID = srr.resourceID
	inner join dbo.cms_siteResourceRoleFunctions srrf
		on roles.roleID = srrf.roleID
	inner join dbo.cms_siteResourceTypeFunctions srtf
		on srtf.resourceTypeFunctionID = srrf.resourceTypeFunctionID
	inner join cms_siteResourceTypes srt
		on srt.resourceTypeID = srtf.resourceTypeID
	inner join cms_siteResourceFunctions srf
		on srf.functionID = srtf.functionID
	inner join cms_siteResources sr
		on sr.resourceTypeID = srt.resourceTypeID
		and s.siteID = sr.siteID

	insert into @myCorrections (
		[siteID],
		[roleID],
		[resourceID],
		[functionID],
		[groupID],
		[include],
		[universalRoleResourceRightsID],
		[universalRoleResourceTypeID])

	select t.siteID, t.roleID, t.resourceID, t.functionID, t.groupID, t.[include], t.universalRoleResourceRightsID, t.universalRoleResourceTypeID
	from #siteResourcesToCheck t
	left outer join cms_siteResourceRightsCache srrc with(nolock)
		on t.universalRoleResourceRightsID = srrc.universalRoleResourceRightsID
		and srrc.universalRoleResourceTypeID =  t.universalRoleResourceTypeID
		and srrc.functionID = t.functionID
	where srrc.functionID is null

	IF OBJECT_ID('tempdb..#siteResourcesToCheck') IS NOT NULL
		DROP TABLE #siteResourcesToCheck


	if exists (select * from @myCorrections)
	BEGIN
		SET @errorSubject = @tier + ': cache_perms_autoCorrect: Detected Missing Role Entries'
		exec membercentral.dbo.sys_sendEmail
			@from='<EMAIL>',
			@to='<EMAIL>',
			@cc='', @bcc='',
			@subject=@errorSubject,
			@message='There are entries missing in cms_siteResourceRightsCache -- which is the first level of the permission cache. Missing entries are being added and the second level of the cache is being updated for the affected siteResources. Check dbo.cms_siteResourceRightsCacheAutoCorrectDetail for more info.', 
			@priority='high', 
			@smtpserver=@smtpserver, 
			@authUsername='', @authPassword=''

		IF OBJECT_ID('tempdb..#siteResourcesToProcess') IS NOT NULL
			DROP TABLE #siteResourcesToProcess
		CREATE TABLE #siteResourcesToProcess (autoid int IDENTITY(1,1), siteResourceID int);

		CREATE INDEX IX_siteResourcesToProcess_SRID ON #siteResourcesToProcess (siteResourceID); 

		insert into @affectedSites (siteID)
		select distinct siteID from @myCorrections

		insert into dbo.cms_siteResourceRightsCacheAutoCorrect(dateEntered) values (getdate())
		select @correctionID = SCOPE_IDENTITY()

		insert into dbo.cms_siteResourceRightsCacheAutoCorrectDetail (correctionID,[siteID],[roleID],[resourceID],[functionID],[groupID],[include],[universalRoleResourceRightsID],[universalRoleResourceTypeID])
		select @correctionID,[siteID],[roleID],[resourceID],[functionID],[groupID],[include],[universalRoleResourceRightsID],[universalRoleResourceTypeID]
		from @myCorrections


		select @thisSiteID = min(siteID) from @affectedSites

		while @thisSiteID is not null begin


			insert into dbo.cms_siteResourceRightsCache (resourceID, functionID, groupID, memberID, include, universalRoleResourceRightsID, universalRoleResourceTypeID)
			select [resourceID],[functionID],[groupID],null, [include],[universalRoleResourceRightsID],[universalRoleResourceTypeID]
			from @myCorrections mC
			where siteID = @thisSiteID


			delete from #siteResourcesToProcess

			insert into #siteResourcesToProcess (siteResourceID)
			select distinct sr.siteresourceID
			from @myCorrections mc
			inner join cms_siteResources sr
				on mc.universalRoleResourceTypeID = sr.resourceTypeID
				and sr.siteID = mc.siteID
				and mc.siteID = @thisSiteID


			exec dbo.cache_perms_updateSiteResourceFunctionRightPrintsForSiteResourcesBulk
				@siteID = @thisSiteID, @processNewPrints = 1


			select @thisSiteID = min(siteID) from @affectedSites where siteID > @thisSiteID

		end

		IF OBJECT_ID('tempdb..#siteResourcesToProcess') IS NOT NULL
			DROP TABLE #siteResourcesToProcess

	END

	
	-- FIX missing groupprints and rightprints
	declare @groupPrints TABLE (groupPrintID int PRIMARY KEY)
	declare @membersAndOrgs TABLE (memberID int PRIMARY KEY, orgID int)
	declare @currentOrgID int

	IF OBJECT_ID('tempdb..#membersToUpdate') IS NOT NULL
		DROP TABLE #membersToUpdate
	CREATE TABLE #membersToUpdate (memberID int PRIMARY KEY);

	insert into @groupPrints (groupPrintID)
	select gp.groupPrintID
	from cache_perms_groupPrints gp
	left outer join cache_perms_groupPrintsAndGroups gpg on gpg.groupPrintID = gp.groupPrintID
	where gpg.groupPrintID is null and groupList is not null

	if exists (select * from @groupPrints)
	BEGIN
		SET @errorSubject = @tier + ': cache_perms_autoCorrect: Detected Missing groupprints and rightprints'
		exec membercentral.dbo.sys_sendEmail
			@from='<EMAIL>',
			@to='<EMAIL>',
			@cc='', @bcc='',
			@subject=@errorSubject,
			@message='Detected missing groupprints and rightprints. Auto correcting them now.', 
			@priority='high', 
			@smtpserver=@smtpserver, 
			@authUsername='', @authPassword=''
	END

	insert into @membersAndOrgs (memberID, orgID)
	select m.memberID, m.orgID
	from ams_members m
	inner join @groupPrints temp on temp.groupPrintID = m.groupPrintID

	update m 
	set groupPrintID = null
	from ams_members m
	inner join @groupPrints temp on temp.groupPrintID = m.groupPrintID

	delete gprp
	from cache_perms_groupPrintsRightPrints gprp
	inner join @groupPrints temp on temp.groupPrintID = gprp.groupPrintID

	delete gpg
	from cache_perms_groupPrintsAndGroups gpg
	inner join @groupPrints temp on temp.groupPrintID = gpg.groupPrintID

	delete gp
	from cache_perms_groupPrints gp
	inner join @groupPrints temp on temp.groupPrintID = gp.groupPrintID

	-- process group print
	select @currentOrgID = min(orgID) from @membersAndOrgs
	while @currentOrgID is not null
	begin
		delete from #membersToUpdate
		
		insert into #membersToUpdate (memberID)
		select memberID
		from @membersAndOrgs
		where orgID = @currentOrgID

		exec dbo.cache_perms_updateGroupPrintsForMembersBulk @orgID=@currentOrgID

		select @currentOrgID = min(orgID) from @membersAndOrgs where orgID > @currentOrgID
	end

	IF OBJECT_ID('tempdb..#membersToUpdate') IS NOT NULL
		DROP TABLE #membersToUpdate	


	set @endDate = getdate()


	if datediff(second, @startDate, @endDate) > 60
	BEGIN

		declare @messagetext varchar(500)
		set @messagetext = 'The proc completed, but it took too long. Runtime in seconds: ' + cast(datediff(second, @startDate, @endDate) as varchar(50))

		SET @errorSubject = @tier + ': cache_perms_autoCorrect: Long running time'
		exec membercentral.dbo.sys_sendEmail
			@from='<EMAIL>',
			@to='<EMAIL>',
			@cc='', @bcc='',
			@subject=@errorSubject,
			@message=@messagetext, 
			@priority='high', 
			@smtpserver=@smtpserver, 
			@authUsername='', @authPassword=''
	END

END
GO

ALTER PROC [dbo].[ev_setupIssuesReport]
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20), @errorContent varchar(max)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)
SET @crlf = char(13) + char(10)
SELECT @smtpserver = smtpserver, @tier = tier from membercentral.dbo.fn_getServerSettings()

/* ********************************** */
/* Events with non-active GL Accounts */
/* ********************************** */
IF EXISTS (select top 1 * from dbo.fn_ev_getEventsWithNonActiveGLAccounts()) BEGIN
	BEGIN TRY
		SET @errorContent = ''
		SET @errorContent = '' + 
			replace(Stuff((
				SELECT '|' + sitecode + char(9) + convert(varchar(10),startTime,101) + ' - ' + eventName + ' - ' + errMessage AS [text()]
				from dbo.fn_ev_getEventsWithNonActiveGLAccounts()
				order by sitecode, startTime, eventName
				FOR XML PATH ('')
			),1,1,''),'|',@crlf)
		SET @errorSubject = @tier + ' - Non-Developer Needed - Events with non-active GL Accounts'
		SET @errorContent = @tier + ' - There are events that are tied to non-active GL Accounts. These need to be corrected for successful member registrations.' + @crlf + @crlf + @errorContent
		EXEC membercentral.dbo.sys_sendEmail 
			@from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errorContent, 
			@priority='high', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END TRY
	BEGIN CATCH
		SET @errorSubject = @tier + ': Error Running Events with non-active GL Accounts'
		SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
		SELECT @errorContent = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
		EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END CATCH
END
GO

ALTER PROC [dbo].[job_runQuarterHourMaintenanceJobs]
AS

DECLARE @tier varchar(20), @errorSubjectRoot varchar(100), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SELECT @smtpserver = smtpserver, @tier = tier from membercentral.dbo.fn_getServerSettings()
SET @errorSubjectRoot = @tier + ' - Developer Needed - '



/* Check Subscription Activations */
BEGIN TRY
	EXEC membercentral.dbo.sub_checkActivations @memberid=null
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Checking Subscription Activations'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* Add search strings to search.dbo.cms_documentVersions */
IF @tier = 'PRODUCTION' BEGIN
	BEGIN TRY
		EXEC membercentral.dbo.up_documentVersions_addSearchStrings
	END TRY
	BEGIN CATCH
		SET @errorSubject = @errorSubjectRoot + 'Error Adding Search Strings to documentVersions'

		SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
		SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END CATCH
END

/* Add newly uploaded depos to Amazon S3 upload queue */
IF @tier = 'PRODUCTION' BEGIN
	BEGIN TRY
		EXEC tlasites.trialsmith.dbo.job_addDeposToS3UploadQueue
	END TRY
	BEGIN CATCH
		SET @errorSubject = @errorSubjectRoot + 'Error adding newly uploaded depos to Amazon S3 upload queue'

		SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
		SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END CATCH
END

RETURN 0
GO

ALTER PROCEDURE [dbo].[lists_getListMembersForLyris] 

AS

IF OBJECT_ID('tempdb..#ListsForLyris') IS NOT NULL
	DROP TABLE #ListsForLyris
CREATE TABLE #ListsForLyris (siteID int, siteCode varchar(10), orgID int, orgCode varchar(10), 
	list_ varchar(100), isAutoManageActive bit)

IF OBJECT_ID('tempdb..#ListMembersForLyris') IS NOT NULL
	DROP TABLE #ListMembersForLyris
CREATE TABLE #ListMembersForLyris (siteID int, siteCode varchar(10), orgID int, orgCode varchar(10), 
	list_ varchar(100), externalMemberID varchar(100), emailaddr_ varchar(250), fullname_ varchar(250), 
	functionName varchar(100))

insert into #ListsForLyris (siteID, siteCode, orgID, orgCode , list_,isAutoManageActive)
select distinct s.siteID, s.siteCode, o.orgID, o.orgCode, lists.listname, lists.isAutoManageActive 
from dbo.lists_lists as lists
inner join dbo.cms_siteResources as sr on sr.siteResourceID = lists.siteResourceID
inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
inner join dbo.sites as s on s.siteID = sr.siteID
inner join dbo.organizations as o on s.orgID = o.orgID
inner join dbo.cache_perms_siteResourceFunctionRightPrints as srfrp on sr.siteResourceID = srfrp.siteResourceID
inner join dbo.cache_perms_groupPrintsRightPrints as gprp on srfrp.rightPrintID = gprp.rightPrintID
inner join dbo.cms_siteResourceFunctions as srf on srf.functionID = srfrp.functionID
	and srf.functionName in ('managePopulation','manageStatus')

insert into #ListMembersForLyris (siteID, siteCode, orgID, orgCode , list_, externalMemberID , emailaddr_ , fullname_ , functionName)
select distinct s.siteID, s.siteCode, o.orgID, o.orgCode, lists.listname, m.memberNumber, me.email, m.firstname + ' ' + m.lastname as fullname_, srf.functionName
from dbo.lists_lists as lists
inner join dbo.cms_siteResources as sr on sr.siteResourceID = lists.siteResourceID
inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
inner join dbo.sites as s on s.siteID = sr.siteID
inner join dbo.organizations as o on s.orgID = o.orgID
inner join dbo.cache_perms_siteResourceFunctionRightPrints as srfrp on sr.siteResourceID = srfrp.siteResourceID
inner join dbo.cache_perms_groupPrintsRightPrints as gprp on srfrp.rightPrintID = gprp.rightPrintID
inner join dbo.cms_siteResourceFunctions as srf on srf.functionID = srfrp.functionID
	and srf.functionName in ('managePopulation','manageStatus')
inner join dbo.ams_members as m on gprp.groupPrintID = m.groupPrintID and m.status='A' 
left outer join dbo.ams_memberEmails as me
	inner join dbo.ams_memberEmailTypes as met on me.emailTypeID = met.emailTypeID and met.emailTypeOrder = 1
	on m.memberID = me.memberID and me.Email like '%@%'

if exists (select emailaddr_ from #ListMembersForLyris)
	BEGIN
		delete from datatransfer.dbo.ListsForLyris

		insert into datatransfer.dbo.ListsForLyris (siteID, siteCode, orgID, orgCode , list_, isAutoManageActive)
		select siteID, siteCode, orgID, orgCode , list_, isAutoManageActive
		from #ListsForLyris

		delete from datatransfer.dbo.ListMembersForLyris

		insert into datatransfer.dbo.ListMembersForLyris  (siteID, siteCode, orgID, orgCode , list_, externalMemberID , emailaddr_ , fullname_ , functionName)
		select siteID, siteCode, orgID, orgCode , list_, externalMemberID , emailaddr_ , fullname_ , functionName
		from #ListMembersForLyris
	END
ELSE
	BEGIN
		DECLARE @smtpserver varchar(20)
		SELECT @smtpserver = smtpserver	from membercentral.dbo.fn_getServerSettings()
		
		EXEC dbo.sys_sendEmail 
			@from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject='membercentral.dbo.lists_getListMembersForLyris returned 0 rows',
			@message='Population returned zero rows. Previous data kept so that Lyris lists would not be blank.',
			@priority='high', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END

IF OBJECT_ID('tempdb..#ListMembersForLyris') IS NOT NULL
	DROP TABLE #ListMembersForLyris
IF OBJECT_ID('tempdb..#ListsForLyris') IS NOT NULL
	DROP TABLE #ListsForLyris
GO

ALTER PROC [dbo].[job_runDailyMaintenanceJobs]
AS

DECLARE @tier varchar(20), @errorSubjectRoot varchar(100), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SELECT @smtpserver = smtpserver, @tier = tier from membercentral.dbo.fn_getServerSettings()
SET @errorSubjectRoot = @tier + ' - Developer Needed - '


/* Recalc Date Based Virtual Group Rules */
BEGIN TRY
	EXEC dbo.ams_recalcVirtualGroupsBasedOnDateConditions
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Recalculating Date Based Conditions'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup resetPasswordRequests */
BEGIN TRY
	delete from dbo.ams_resetPasswordRequest
	where dateentered < dateadd(day,-30,getdate())
	and expire = 1

	delete from dbo.ams_resetPasswordRequest
	where dateentered < dateadd(day,-30,getdate())
	and expire = 0 
	and hasBeenUsed = 1
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of resetPasswordRequests'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Viewed Member Times */
BEGIN TRY
	delete tmp
	from dbo.ams_viewedMemberTimes as tmp
	inner join (
		select adminMemberViewedID, memRow
		from (
			select adminMemberViewedID, ROW_NUMBER() OVER (PARTITION BY adminID, viewedOrgID order by lastViewedDateTime desc) as memRow
			from dbo.ams_viewedMemberTimes
		) as innerTMP
		where memRow > 20
	) as t2 on t2.adminMemberViewedID = tmp.adminMemberViewedID
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of viewedMemberTimes'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Orphaned Member Data Content */
BEGIN TRY
	EXEC dbo.ams_cleanupOrphanedMemberDataContent
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of member data content'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Orphaned Network Profiles */
BEGIN TRY
	EXEC dbo.ams_deleteOrphanedNetworkProfiles
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of orphaned network profiles'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Orphaned Virtual Group Conditions */
BEGIN TRY
	EXEC dbo.ams_cleanupOrphanedVirtualGroupConditions
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of orphaned virtual group conditions'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Search Site Resource Cache entries */
BEGIN TRY
	delete from search.dbo.tblSearchSiteResourceCache
	where dateCreated < dateadd(day,-1,getdate())
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of Search Site Resource Cache'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup BuyNow log entries */
BEGIN TRY
	delete from dbo.buyNow_Log
	where insertDate < dateadd(day,-7,getdate())
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of BuyNow log entries'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Auto Post System Batches */
BEGIN TRY
	EXEC dbo.tr_autoPostSystemBatches
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Auto Posting System Batches'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Mark paid closed invoices as paid */
BEGIN TRY
	EXEC dbo.tr_autoMarkClosedInvoicesAsPaid
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Marking paid closed invoices as paid'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Close Pending Invoices */
BEGIN TRY
	EXEC dbo.sub_closePendingInvoices
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Closing Pending Invoices'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Close empty open invoices */
BEGIN TRY
	EXEC dbo.tr_autoCloseEmptyOpenInvoices
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Closing Empty Open Invoices'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Advance Subscription Rates */
BEGIN TRY
	EXEC dbo.sub_advanceRates
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Advancing Subscription Rates'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Expire Subscription Offers */
BEGIN TRY
	EXEC dbo.sub_expireOffers
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Expiring Subscription Offers'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Expire Subscriptions */
BEGIN TRY
	EXEC dbo.sub_expireSubscriptions
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Expiring Subscriptions'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Dupe Sub History Dates */
BEGIN TRY
	EXEC dbo.sub_cleanupDuplicateSubHistoryDates
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleaning Up Duplicate Sub History Dates'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Add autoPay Invoices to Invoice Payment Queue */
BEGIN TRY
	EXEC dbo.tr_autoPayInvoices
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Adding autoPay Invoices to Invoice Payment Queue'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Add items to LRIS Client Surveys Queue */
IF @tier = 'PRODUCTION' BEGIN
	BEGIN TRY
		declare @referralID int
		set @referralID = 1
		EXEC dbo.ref_addClientSurveyQueue @referralID = @referralID
	END TRY
	BEGIN CATCH
		SET @errorSubject = @errorSubjectRoot + 'Error Adding items to Client Surveys Queue'

		SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
		SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END CATCH
END

/* Cleanup tblQueueItems_processMemberGroups */
BEGIN TRY
	delete from platformQueue.dbo.tblQueueItems_processMemberGroups
	where dateadded < dateadd(day,-1,getdate())
	and itemUID not in (select itemUID from platformQueue.dbo.tblQueueItems)
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of tblQueueItems_processMemberGroups'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* Add missing ad zones or update ad zone names */
BEGIN TRY
	EXEC dbo.ad_populateAdZones
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Populating Ad Zones'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* Admin Count checks - Run on first of month only */
IF datepart(d,getdate()) = 1 BEGIN
	BEGIN TRY
		EXEC dbo.ams_adminCountReport
	END TRY
	BEGIN CATCH
		SET @errorSubject = @errorSubjectRoot + 'Error Running Admin Count Report'

		SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
		SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END CATCH
END

RETURN 0
GO

ALTER PROC [dbo].[job_runDailyMaintenanceChecks]
AS

-- Do not lock anything, and do not get held up by any locks.
SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

DECLARE @tier varchar(20), @errorSubjectRoot varchar(100), @errorSubjectRootNonDev varchar(100), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)
DECLARE @tableHTML VARCHAR(MAX)
DECLARE @startTime datetime
declare @fnCount int

/* variables */
SET @crlf = char(13) + char(10)
SELECT @smtpserver = smtpserver, @tier = tier from membercentral.dbo.fn_getServerSettings()
SET @errorSubjectRoot = @tier + ' - Developer Needed - '
SET @errorSubjectRootNonDev = @tier + ' - Non-Developer Needed - '


/* CHECK - subscription child parent mismatch */
set @startTime = getdate()
BEGIN TRY
	select @fnCount = count(*) from dbo.fn_sub_getMismatchedParentChildSubscribers()
	IF @fnCount > 0 BEGIN
		SET @errorSubject = @errorSubjectRoot + 'child/parent subscription mismatch'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are child subscriptions linked to parent subscriptions belonging to another member. Run:  select * from dbo.fn_sub_getMismatchedParentChildSubscribers()    to view the mismatches.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR child/parent subscription mismatch'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH
print RTRIM(CAST(DATEDIFF(MS,@starttime,GETDATE()) AS varchar(20))) + 'ms - CHECK - subscription child parent mismatch'

/* CHECK - duplicate subscriptions */
set @startTime = getdate()
BEGIN TRY
	select @fnCount = count(*) from dbo.fn_sub_getDuplicateSubscriptions()
	IF @fnCount > 0 BEGIN
		SET @tableHTML = '' + 
			replace(Stuff((
				SELECT '|' + orgcode + char(9) + memberName + ' (' + membernumber + ') - ' + subscriptionName + ' (' + statusName + ')' AS [text()]
				from dbo.fn_sub_getDuplicateSubscriptions()
				order by orgcode, membername, subscriptionName
				FOR XML PATH ('')
			),1,1,''),'|',char(13)+char(10))
		SET @errorSubject = @errorSubjectRootNonDev + 'Member with Duplicate Subscriptions'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are members with duplicate subscriptions.' + @crlf + @crlf + @tableHTML
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Member with Duplicate Subscriptions'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH
print RTRIM(CAST(DATEDIFF(MS,@starttime,GETDATE()) AS varchar(20))) + 'ms - CHECK - duplicate subscriptions'

/* CHECK - subscribers with NULL RFIDs  */
set @startTime = getdate()
BEGIN TRY
	select @fnCount = count(*) from dbo.fn_sub_getMissingSubscriberRFID()
	IF @fnCount > 0 BEGIN
		SET @tableHTML = '' + 
			replace(Stuff((
				SELECT '|' + orgcode + char(9) + m2.lastname + ', ' + m2.firstname + isnull(' ' + nullif(m2.middleName,''),'') + ' (' + m2.membernumber + ') - ' + subscriptionName + ' (' + statusName + ')' AS [text()]
					from dbo.sub_subscribers as sub
					inner join dbo.fn_sub_getMissingSubscriberRFID() sub2 on sub2.subscriberID = sub.subscriberID
					inner join dbo.sub_subscriptions as s on s.subscriptionID = sub.subscriptionID
					inner join dbo.sub_statuses as ss on ss.statusID = sub.statusID
					inner join dbo.ams_members as m on m.memberID = sub.memberID
					inner join dbo.ams_members as m2 on m2.memberID = m.activememberID
					inner join dbo.organizations as o on o.orgID = m2.orgID
				order by orgcode, m2.lastname, subscriptionName
				FOR XML PATH ('')
			),1,1,''),'|',char(13)+char(10))
		SET @errorSubject = @errorSubjectRoot + 'Member Subscriptions with NULL Rate Frequency'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are members with NULL Rate Frequencies in their subscriptions found in the [fn_sub_getMissingSubscriberRFID] function.' + @crlf + @crlf + @tableHTML
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Member Subscriptions with NULL Rate Frequency'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH
print RTRIM(CAST(DATEDIFF(MS,@starttime,GETDATE()) AS varchar(20))) + 'ms - CHECK - subscribers with NULL RFIDs'

/* CHECK - duplicate member numbers */
set @startTime = getdate()
BEGIN TRY
	select @fnCount = count(*) from dbo.fn_ams_getOrgDuplicateMemberNumbers()
	IF @fnCount > 0 BEGIN
		SET @errorSubject = @errorSubjectRoot + 'Duplicate Member Numbers'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are members that share the same member number for an org. Run    select * from dbo.fn_ams_getOrgDuplicateMemberNumbers()   to view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Duplicate Member Numbers'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH
print RTRIM(CAST(DATEDIFF(MS,@starttime,GETDATE()) AS varchar(20))) + 'ms - CHECK - duplicate member numbers'

/* CHECK - duplicate member site defaults */
set @startTime = getdate()
BEGIN TRY
	select @fnCount = count(*) from dbo.fn_ams_getSiteDuplicateMemberSiteDefaults()
	IF @fnCount > 0 BEGIN
		SET @errorSubject = @errorSubjectRoot + 'Duplicate Member Site Defaults'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are members that have duplicate active site defaults for a site. Run    select * from dbo.fn_ams_getSiteDuplicateMemberSiteDefaults()  to view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Duplicate Member Site Defaults'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH
print RTRIM(CAST(DATEDIFF(MS,@starttime,GETDATE()) AS varchar(20))) + 'ms - CHECK - duplicate member site defaults'

/* CHECK - duplicate search cms_documentversions */
set @startTime = getdate()
BEGIN TRY
	select @fnCount = count(*) from search.dbo.[vw_Duplicate Document Versions]
	IF @fnCount > 0 BEGIN
		SET @errorSubject = @errorSubjectRoot + 'search.cms_documentversions has duplicate entries'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are document versions that appear multiple times in the search table. Run the search.dbo.[vw_Duplicate Document Versions] view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR search.cms_documentversions has duplicate entries'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH
print RTRIM(CAST(DATEDIFF(MS,@starttime,GETDATE()) AS varchar(20))) + 'ms - CHECK - duplicate search cms_documentversions'

/* CHECK - Future active subscriptions */
set @startTime = getdate()
BEGIN TRY
	select @fnCount = count(*) from dbo.fn_sub_getActiveSubscribersStartingInFuture()
	IF @fnCount > 0 BEGIN
		SET @errorSubject = @errorSubjectRoot + 'future dated active subscriptions'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are members with active subscriptions that have not started yet. Run   select * from dbo.fn_sub_getActiveSubscribersStartingInFuture()  to view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR future dated active subscriptions'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH
print RTRIM(CAST(DATEDIFF(MS,@starttime,GETDATE()) AS varchar(20))) + 'ms - CHECK - Future active subscriptions'

/* CHECK - Member data org mismatch */
set @startTime = getdate()
BEGIN TRY
	select @fnCount = count(*) from dbo.fn_ams_getMismatchedOrgMemberData()
	IF @fnCount > 0 BEGIN
		SET @errorSubject = @errorSubjectRoot + 'Org Member Data Mismatch'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are org mismatches in member data. Run   select * from dbo.fn_ams_getMismatchedOrgMemberData()   to view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Org Member Data Mismatch'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH
print RTRIM(CAST(DATEDIFF(MS,@starttime,GETDATE()) AS varchar(20))) + 'ms - CHECK - Member data org mismatch'

/* CHECK - org permission mismatch */
set @startTime = getdate()
BEGIN TRY
	select @fnCount = count(*) from dbo.fn_ams_getMismatchedOrgPermissions()
	IF @fnCount > 0 BEGIN
		SET @errorSubject = @errorSubjectRoot + 'Org Member Permission Mismatch'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are org mismatches in member permissions. Run   select * from dbo.fn_ams_getMismatchedOrgPermissions()   to view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Org Member Permission Mismatch'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH
print RTRIM(CAST(DATEDIFF(MS,@starttime,GETDATE()) AS varchar(20))) + 'ms - CHECK - org permission mismatch'

/* CHECK - group assignment rules non existent conditions */
set @startTime = getdate()
BEGIN TRY
	select @fnCount = count(*) from dbo.fn_ams_getGroupAssignmentRulesWithMissingConditions()
	IF @fnCount > 0 BEGIN
		SET @tableHTML = 'orgcode'+char(9)+'ruleName'+@crlf+
			replace(Stuff((
				SELECT DISTINCT ',' + orgcode+char(9)+char(9) + ruleName AS [text()]
				from dbo.fn_ams_getGroupAssignmentRulesWithMissingConditions()
				FOR XML PATH ('')
			),1,1,''),',',@crlf)
		SET @errorSubject = @errorSubjectRootNonDev + 'Group Rules with non-existent conditions'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are group assignment rules with with non-existent conditions. Run EXEC dbo.ams_cleanupMissingVirtualGroupConditions to fix.' + @crlf + @crlf + @tableHTML
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Group Rules with non-existent conditions'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH
print RTRIM(CAST(DATEDIFF(MS,@starttime,GETDATE()) AS varchar(20))) + 'ms - CHECK - group assignment rules non existent conditions'

/* CHECK - Org Cache Enabled Status */
set @startTime = getdate()
BEGIN TRY
	IF EXISTS (select * from dbo.organizations where cache_perms_status <> 'enabled') BEGIN
		SET @errorSubject = @errorSubjectRoot + 'cache_perms_status not enabled'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'cache_perms_status is not enabled for some organizations.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR cache_perms_status not enabled'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH
print RTRIM(CAST(DATEDIFF(MS,@starttime,GETDATE()) AS varchar(20))) + 'ms - CHECK - Org Cache Enabled Status'

/* CHECK - invalid or mismatched pro license statuses */
set @startTime = getdate()
BEGIN TRY
	select @fnCount = count(*) from dbo.fn_ams_getMissingOrInvalidPLStatuses()
	IF @fnCount > 0 BEGIN
		SET @errorSubject = @errorSubjectRoot + 'invalid or mismatched pro license statuses'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are members with professional licenses with no status or linked to the status of a different license type. Run   select * from dbo.fn_ams_getMissingOrInvalidPLStatuses()   to view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR invalid or mismatched pro license statuses'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH
print RTRIM(CAST(DATEDIFF(MS,@starttime,GETDATE()) AS varchar(20))) + 'ms - CHECK - invalid or mismatched pro license statuses'

/* CHECK - duplicate entries in siteResourceFunctionRightPrints cache */
set @startTime = getdate()
BEGIN TRY
	select @fnCount = count(*) from dbo.fn_ams_getDuplicateEntriesInSiteResourceRightsCache()
	IF @fnCount > 0 BEGIN
		SET @errorSubject = @errorSubjectRoot + 'duplicate entries in the siteResourceFunctionRightPrints cache'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are duplicate entries in the siteResourceFunctionRightPrints cache. Run   select * from dbo.fn_ams_getDuplicateEntriesInSiteResourceRightsCache()   to view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR duplicate entries in the siteResourceFunctionRightPrints cache'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH
print RTRIM(CAST(DATEDIFF(MS,@starttime,GETDATE()) AS varchar(20))) + 'ms - CHECK - duplicate entries in siteResourceFunctionRightPrints cache'

/* CHECK - Subscriptions Setup Issues Report */
set @startTime = getdate()
BEGIN TRY
	EXEC dbo.sub_setupIssuesReport
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Running Subscription Setup Issues Report'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH
print RTRIM(CAST(DATEDIFF(MS,@starttime,GETDATE()) AS varchar(20))) + 'ms - CHECK - Subscriptions Setup Issues Report'

/* CHECK - events setup issues report */
set @startTime = getdate()
BEGIN TRY
	EXEC dbo.ev_setupIssuesReport
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Running Events Setup Issues Report'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH
print RTRIM(CAST(DATEDIFF(MS,@starttime,GETDATE()) AS varchar(20))) + 'ms - CHECK - events setup issues report'

/* CHECK - multiple member network profiles */
set @startTime = getdate()
BEGIN TRY
	select @fnCount = count(*) from dbo.fn_ams_getMultipleMemberNetworkProfiles()
	IF @fnCount > 0 BEGIN
		SET @tableHTML = 'mnpCount'+char(9)+'siteid'+char(9)+'networkid'+char(9)+'memberid'+@crlf+
			replace(Stuff((
				SELECT DISTINCT ',' + cast(mnpCount as varchar(5)) + char(9) + cast(siteid as varchar(10)) + char(9) + cast(networkid as varchar(10)) + char(9) + cast(memberid as varchar(10)) AS [text()]
				from dbo.fn_ams_getMultipleMemberNetworkProfiles()
				FOR XML PATH ('')
			),1,1,''),',',@crlf)
		SET @errorSubject = @errorSubjectRoot + 'Members with multiple active mnp'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'The following members are linked to multiple active member network profiles in the same network.' + @crlf + @crlf + @tableHTML
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Members with multiple active mnp'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH
print RTRIM(CAST(DATEDIFF(MS,@starttime,GETDATE()) AS varchar(20))) + 'ms - CHECK - multiple member network profiles'

/* CHECK - Depo Accounts linked to multiple Network Profiles */
set @startTime = getdate()
BEGIN TRY
	select @fnCount = count(*) from dbo.fn_ams_getDepoAccountsLinkedToMultipleNetworkProfiles()
	IF @fnCount > 0 BEGIN
		SET @errorSubject = @errorSubjectRoot + 'Depo Accounts linked to multiple profiles'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are depo accounts linked to multiple active network profiles. This is usually an indication that one TS Admin account is linked to two accounts in MemberCentral under different networks. Run    select * from dbo.fn_ams_getDepoAccountsLinkedToMultipleNetworkProfiles()   to view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Depo Accounts linked to multiple profiles'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH
print RTRIM(CAST(DATEDIFF(MS,@starttime,GETDATE()) AS varchar(20))) + 'ms - CHECK - Depo Accounts linked to multiple Network Profiles'

/* CHECK - Paid Invoices with Amounts Due */
set @startTime = getdate()
BEGIN TRY
	select @fnCount = count(*) from dbo.fn_tr_getPaidInvoicesWithAmountDue()
	IF @fnCount > 0 BEGIN
		SET @errorSubject = @errorSubjectRoot + 'paid invoices with amount due'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are paid invoices with amounts due. These need to be set to closed. Run   select * from dbo.fn_tr_getPaidInvoicesWithAmountDue()   to view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR paid invoices with amount due'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH
print RTRIM(CAST(DATEDIFF(MS,@starttime,GETDATE()) AS varchar(20))) + 'ms - CHECK - Paid Invoices with Amounts Due'

/* CHECK - Payment History with no Payment Transaction */
set @startTime = getdate()
BEGIN TRY
	select @fnCount = count(*) from dbo.fn_tr_getPaymentHistoryWithoutPaymentTransaction()
	IF @fnCount > 0 BEGIN
		SET @errorSubject = @errorSubjectRoot + 'Payment History with No Payment Transaction'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are successful payments without payment transactions. Run    select * from dbo.fn_tr_getPaymentHistoryWithoutPaymentTransaction()   to view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Payment History with No Payment Transaction'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH
print RTRIM(CAST(DATEDIFF(MS,@starttime,GETDATE()) AS varchar(20))) + 'ms - CHECK - Payment History with no Payment Transaction'

/* CHECK - Payments with bad cache allocated amounts */
set @startTime = getdate()
BEGIN TRY
	select @fnCount = count(*) from dbo.fn_tr_getPaymentsWithBadCachedAllocatedAmounts()
	IF @fnCount > 0 BEGIN
		SET @errorSubject = @errorSubjectRoot + 'Payments with bad cached allocated amounts'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are payments with bad cached allocated amounts. Run    select * from dbo.fn_tr_getPaymentsWithBadCachedAllocatedAmounts()   to view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Payments with bad cached allocated amounts'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH
print RTRIM(CAST(DATEDIFF(MS,@starttime,GETDATE()) AS varchar(20))) + 'ms - CHECK - Payments with bad cache allocated amounts'

/* CHECK - Payments with bad cache refundable amounts */
set @startTime = getdate()
BEGIN TRY
	select @fnCount = count(*) from dbo.fn_tr_getPaymentsWithBadCachedRefundableAmounts()
	IF @fnCount > 0 BEGIN
		SET @errorSubject = @errorSubjectRoot + 'Payments with bad cached refundable amounts'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are payments with bad cached refundable amounts. Run    select * from dbo.fn_tr_getPaymentsWithBadCachedRefundableAmounts()   to view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Payments with bad cached refundable amounts'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH
print RTRIM(CAST(DATEDIFF(MS,@starttime,GETDATE()) AS varchar(20))) + 'ms - CHECK - Payments with bad cache refundable amounts'

/* CHECK - Payments with misallocated funds */
set @startTime = getdate()
BEGIN TRY
	select @fnCount = count(*) from dbo.fn_tr_getMisallocatedPayments()
	IF @fnCount > 0 BEGIN
		SET @errorSubject = @errorSubjectRoot + 'Payments with misallocated funds'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are payments with misallocated funds. Run   select * from dbo.fn_tr_getMisallocatedPayments()   to view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Payments with misallocated funds'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH
print RTRIM(CAST(DATEDIFF(MS,@starttime,GETDATE()) AS varchar(20))) + 'ms - CHECK - Payments with misallocated funds'

/* CHECK - disallowed multiple values for custom fields */
set @startTime = getdate()
BEGIN TRY
	select @fnCount = count(*) from dbo.fn_ams_getMembersWithMultipleValuesforCustomField()
	IF @fnCount > 0 BEGIN
		SET @errorSubject = @errorSubjectRoot + 'Multiple Custom Field Values'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are members with multiple values for the same custom field. Run    select * from dbo.fn_ams_getMembersWithMultipleValuesforCustomField()   to view.'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Multiple Custom Field Values'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH
print RTRIM(CAST(DATEDIFF(MS,@starttime,GETDATE()) AS varchar(20))) + 'ms - CHECK - disallowed multiple values for custom fields'

/* CHECK - depos to Amazon S3 queue for entries older than 24 hrs */
set @startTime = getdate()
BEGIN TRY
	declare @amazonDepoUpload_onedayago datetime, @amazonDepoUpload_count int
	set @amazonDepoUpload_onedayago = dateadd(day,-1,getdate())
	select @amazonDepoUpload_count = count(*) from tlasites.transfer.dbo.depoDocumentsS3UploadQueue where dateCreated < @amazonDepoUpload_onedayago
	IF @amazonDepoUpload_count > 0 BEGIN
		SET @errorSubject = @errorSubjectRoot + 'Old entries in Depo Amazon Upload Queue'
		SET @errmsg = @errorSubject + @crlf + @crlf + 'There are documents in the upload queue that are older than 24 hours. Look in tlasites.transfer.dbo.depoDocumentsS3UploadQueue to investigate. Processing script runs on TSFILE1'
		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'ERROR Old entries in Depo Amazon Upload Queue'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH
print RTRIM(CAST(DATEDIFF(MS,@starttime,GETDATE()) AS varchar(20))) + 'ms - CHECK - depos to Amazon S3 queue for entries older than 24 hrs'


RETURN 0
GO




use customApps
GO

ALTER PROC [dbo].[abota_job_importMembers]
AS

DECLARE @smtpserver varchar(20)
SELECT @smtpserver = smtpserver	from membercentral.dbo.fn_getServerSettings()

BEGIN TRY
	DECLARE @orgid int, @importResult xml, @flatfile varchar(160), @finalMSG varchar(max), @emailtouse varchar(300)
	SELECT @orgID = membercentral.dbo.fn_getOrgIDFromOrgCode('ABOTA')
	EXEC membercentral.dbo.ams_importMemberDataClearStatusLog @orgID
	EXEC customApps.dbo.abota_importMemberData 'e:\temp\abotafiles\', 'E:\databasebackup\memberImportBackupFiles\', @importResult OUTPUT

	DECLARE @thresholdReached bit
	SET @thresholdReached = 0
	IF @importResult.value('(/import/counts/count[@name="TotalCurrentMembersWillBeInactivated"]/@num)[1]','int') >= 1000
		SET @thresholdReached = 1

	IF @importResult.value('count(/import/errors/error[@severity="fatal"])','int') = 0  AND @thresholdReached = 0 
	BEGIN
		SELECT @flatfile = @importResult.value('(/import/@flatfile)[1]','varchar(160)')
		EXEC membercentral.dbo.ams_importMemberData_holdingToPerm @orgID=@orgID, @flatfile=@flatfile
		EXEC customApps.dbo.abota_importGiftHistory 'e:\temp\abotafiles\'
	END

	EXEC membercentral.dbo.ams_importMemberDataHoldingResultReport @importResult=@importResult, @finalMSG=@finalMSG OUTPUT
	IF @thresholdReached = 1 BEGIN
		SET @finalMSG = '*** NOTE: THE INACTIVATION THRESHOLD WAS MET AND THE IMPORT WAS ABORTED. ***' 
			+ char(10) + 'The following shows what would have happened if the import continued.'
			+ char(10) + char(10) + @finalMSG
	END
	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject='ABOTA Membership Import Results',
		@message=@finalMSG, @priority='normal', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null

	SELECT @emailtouse = emailImportResults from membercentral.dbo.organizations where orgID = @orgID	
	IF len(@emailtouse) > 0 BEGIN
		EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to=@emailtouse,
			@cc=null, @bcc='<EMAIL>', @subject='ABOTA Membership Import Results',
			@message=@finalMSG, @priority='normal', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY  
BEGIN CATCH      
	DECLARE @ErrorMessage VARCHAR(max)      
	DECLARE @ErrorSeverity INT      
	DECLARE @ErrorState INT        
	SELECT @ErrorMessage = 'Error running ABOTA Membership Import job' + char(13) + char(10) + ERROR_MESSAGE(),   
		@ErrorSeverity = ERROR_SEVERITY(),   
		@ErrorState = ERROR_STATE()        
	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject='Error Importing ABOTA Data',
		@message=@ErrorMessage, @priority='high', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)  
END CATCH

RETURN 0
GO

ALTER PROC [dbo].[job_runHourlyCustomJobs]
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SELECT @smtpserver = smtpserver, @tier = tier from membercentral.dbo.fn_getServerSettings()

/* ********************** */
/* va_updateBarAndBirthDates */
/* ********************** */
BEGIN TRY
	EXEC customApps.dbo.va_updateBarAndBirthDates
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Bar and Birth Date - VA'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* va_updateVTLADistrictAndTaskforce */
/* ********************** */
BEGIN TRY
	EXEC customApps.dbo.va_updateVTLADistrictAndTaskforce
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating VTLA District and Taskforcing Areas - VA'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* *************** */
/* ny_updateRegion */
/* *************** */
BEGIN TRY
	EXEC customApps.dbo.ny_updateRegion
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Region - NY'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* *************** */
/* cache_lyris_updateIntegratedMembers */
/* *************** */
BEGIN TRY
	EXEC datatransfer.dbo.cache_lyris_updateIntegratedMembers @restrictToOrgcode=null
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': datatransfer.dbo.cache_lyris_updateIntegratedMembers'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

RETURN 0
GO

ALTER PROC [dbo].[job_runDailyCustomJobs]
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SELECT @smtpserver = smtpserver, @tier = tier from membercentral.dbo.fn_getServerSettings()


/* ********************** */
/* tn_updateOldestBarDate */
/* ********************** */
BEGIN TRY
	EXEC customApps.dbo.tn_updateOldestBarDate
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Oldest Bar Date - TN'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* ky_updateOldestBarDate */
/* ********************** */
BEGIN TRY
	EXEC customApps.dbo.ky_updateOldestBarDate
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Oldest Bar Date - KY'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ***************** */
/* MEMBER JOIN DATES */
/* ***************** */
BEGIN TRY
	declare @mjd_udid int, @mjd_error_code int, @mjd_emsg varchar(70)
	select @mjd_udid = min(udid) from customApps.dbo.schedTask_memberJoinDates where isActive = 1
	while @mjd_udid is not null BEGIN
		EXEC customApps.dbo.job_memberJoinDates @udid=@mjd_udid, @error_code=@mjd_error_code OUTPUT
		IF @mjd_error_code <> 0 BEGIN
			select @mjd_emsg = 'UDID ' + cast(@mjd_udid as varchar(10)) + ' was not successful. Error ' + cast(@mjd_error_code as varchar(10)) + '.'
			RAISERROR(@mjd_emsg,16,1)
		END
		select @mjd_udid = min(udid) from customApps.dbo.schedTask_memberJoinDates where isActive = 1 and udid > @mjd_udid
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Member Join Dates'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ************************ */
/* caaa_updateHomeChapterRO */
/* ************************ */
BEGIN TRY
	EXEC customApps.dbo.caaa_updateHomeChapterRO
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Home Chapter - CAAA'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* *********************** */
/* caaa_checkInvoice90Days */
/* *********************** */
BEGIN TRY
	EXEC customApps.dbo.caaa_checkInvoice90Days
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running 90 Day Invoice Check - CAAA'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ************************************ */
/* ny_updateCLECreditsSinceLastBirthday */
/* ************************************ */
BEGIN TRY
	EXEC customApps.dbo.ny_updateCLECreditsSinceLastBirthday
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating CLE Credit Since Last Birthday - NY'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* *************************** */
/* sdcba_updateGeographicAreas */
/* *************************** */
BEGIN TRY
	exec customApps.dbo.sdcba_updateGeographicAreas
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Geographic Areas - SDCBA'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ******************************* */
/* sdcba_updateSpecialDesignations */
/* ******************************* */
BEGIN TRY
	exec customApps.dbo.sdcba_updateSpecialDesignations
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Special Designations - SDCBA'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************* */
/* ky_syncListMemberData */
/* ********************* */
BEGIN TRY
	EXEC customApps.dbo.ky_syncListMemberData
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Lyris Member Data - KY'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************************* */
/* lo_updateAffiliateIndivualRecords */
/* ********************************* */
BEGIN TRY
	EXEC customApps.dbo.lo_updateAffiliateIndivualRecords
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Active Affiliate Individual Records - LO'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* *************** */
/* tx_updateRegion */
/* *************** */
BEGIN TRY
	EXEC customApps.dbo.tx_updateRegion
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Region - TX'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

RETURN 0
GO


ALTER PROC [dbo].[la_job_importMembers]
AS

DECLARE @smtpserver varchar(20)
SELECT @smtpserver = smtpserver from membercentral.dbo.fn_getServerSettings()

BEGIN TRY
	DECLARE @orgid int, @importResult xml, @flatfile varchar(160), @finalMSG varchar(max), @emailtouse varchar(300)
	SELECT @orgID = membercentral.dbo.fn_getOrgIDFromOrgCode('LA')
	EXEC membercentral.dbo.ams_importMemberDataClearStatusLog @orgID
	EXEC customApps.dbo.la_importMemberData 'e:\temp\', 'E:\databasebackup\memberImportBackupFiles\', @importResult OUTPUT

	DECLARE @thresholdReached bit
	SET @thresholdReached = 0
	IF @importResult.value('(/import/counts/count[@name="TotalCurrentMembersWillBeInactivated"]/@num)[1]','int') >= 200
		SET @thresholdReached = 1

	IF @importResult.value('count(/import/errors/error[@severity="fatal"])','int') = 0 AND @thresholdReached = 0
	BEGIN
		SELECT @flatfile = @importResult.value('(/import/@flatfile)[1]','varchar(160)')
		EXEC membercentral.dbo.ams_importMemberData_holdingToPerm @orgID=@orgID, @flatfile=@flatfile
	END

	EXEC membercentral.dbo.ams_importMemberDataHoldingResultReport @importResult=@importResult, @finalMSG=@finalMSG OUTPUT
	IF @thresholdReached = 1 BEGIN
		SET @finalMSG = '*** NOTE: THE INACTIVATION THRESHOLD WAS MET AND THE IMPORT WAS ABORTED. ***' 
			+ char(10) + 'The following shows what would have happened if the import continued.'
			+ char(10) + char(10) + @finalMSG
	END
	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject='LAFJ Membership Import Results',
		@message=@finalMSG, @priority='normal', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null

	SELECT @emailtouse = emailImportResults from membercentral.dbo.organizations where orgID = @orgID	
	IF len(@emailtouse) > 0 BEGIN
		EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to=@emailtouse,
			@cc=null, @bcc='<EMAIL>', @subject='LAFJ Membership Import Results',
			@message=@finalMSG, @priority='normal', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END
END TRY  
BEGIN CATCH      
	DECLARE @ErrorMessage VARCHAR(max)      
	DECLARE @ErrorSeverity INT      
	DECLARE @ErrorState INT        
	SELECT @ErrorMessage = 'Error running LAFJ Membership Import job' + char(13) + char(10) + ERROR_MESSAGE(),   
		@ErrorSeverity = ERROR_SEVERITY(),   
		@ErrorState = ERROR_STATE()        
	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject='Error Importing LAFJ Data',
		@message=@ErrorMessage, @priority='high', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	RAISERROR(@ErrorMessage,@ErrorSeverity,@ErrorState)  
END CATCH

RETURN 0
GO

DROP PROC dbo.sdcba_getListMembers
GO

ALTER PROC [dbo].[TAGD_importNationalData]
@batchDate datetime,
@recordedByMemberID int,
@statsSessionID int,
@pathToImport varchar(200), 
@pathToExport varchar(200),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

DECLARE @orgID int, @siteID int, @cmd varchar(600), @IDList VARCHAR(max), @finalMSG varchar(max), @emailtouse varchar(300),
	@flatfile varchar(200)
SELECT @orgID = membercentral.dbo.fn_getOrgIDFromOrgCode('TAGD')
SELECT @siteID = membercentral.dbo.fn_getSiteIDFromSiteCode('TAGD')

-- create temp tables
IF OBJECT_ID('tempdb..##tagd_nationals_alpha') IS NOT NULL
	DROP TABLE ##tagd_nationals_alpha
CREATE TABLE ##tagd_nationals_alpha (
	[rowid] [int] NOT NULL,
	[ID] [varchar](50) NOT NULL,
	[MEMBER TYPE] [varchar](10) NOT NULL DEFAULT (''),
	[MEMBER TYPE DESCRIPTION] [varchar](100) NOT NULL DEFAULT (''),
	[CATEGORY DESCRIPTION] [varchar](200) NOT NULL DEFAULT (''),
	[FULL NAME] [varchar](200) NOT NULL DEFAULT (''),
	[LAST FIRST] [varchar](200) NOT NULL DEFAULT (''),
	[FIRST NAME] [varchar](75) NOT NULL DEFAULT (''),
	[MIDDLE NAME] [varchar](25) NOT NULL DEFAULT (''),
	[LAST NAME] [varchar](75) NOT NULL DEFAULT (''),
	[SUFFIX] [varchar](50) NOT NULL DEFAULT (''),
	[DESIGNATION] [varchar](max) NULL,
	[WORK PHONE] [varchar](40) NULL,
	[HOME PHONE] [varchar](40) NULL,
	[FAX] [varchar](40) NULL,
	[FULL ADDRESS] [varchar](200) NOT NULL DEFAULT (''),
	[ADDRESS 1] [varchar](100) NULL,
	[ADDRESS 2] [varchar](100) NULL,
	[CITY] [varchar](35) NULL,
	[STATE PROVINCE] [varchar](25) NULL,
	[ZIP] [varchar](25) NULL,
	[GRAD DATE] datetime NULL,
	[DENTAL SCHOOL] [varchar](300) NULL,
	[JOIN DATE] datetime NULL,
	[PAID THRU] datetime NULL,
	[DO NOT MAIL] [varchar](300) NOT NULL DEFAULT (''),
	[EMAIL] [varchar](255) NULL,
	[COMPONENT TITLE] [varchar](300) NOT NULL DEFAULT (''),
	[BIRTH DATE] datetime NULL,
	[BAD ADDRESS] [varchar](300) NOT NULL DEFAULT ('')
)

IF OBJECT_ID('tempdb..##tagd_nationals_new') IS NOT NULL
	DROP TABLE ##tagd_nationals_new
CREATE TABLE ##tagd_nationals_new (
	[rowid] [int] NOT NULL,
	[ID] [varchar](50) NOT NULL,
	[FIRST NAME] [varchar](75) NOT NULL DEFAULT (''), 
	[MIDDLE NAME] [varchar](25) NOT NULL DEFAULT (''), 
	[LAST NAME] [varchar](75) NOT NULL DEFAULT (''), 
	[WORK ADDRESS 1] [varchar](100) NULL, 
	[WORK ADDRESS 2] [varchar](100) NULL, 
	[WORK CITY] [varchar](35) NULL, 
	[WORK STATE] [varchar](25) NULL, 
	[WORK ZIP] [varchar](25) NULL, 
	[WORK PHONE] [varchar](40) NULL, 
	[WORK FAX] [varchar](40) NULL, 
	[WORK EMAIL] [varchar](200) NULL, 
	[HOME ADDRESS 1] [varchar](100) NULL, 
	[HOME ADDRESS 2] [varchar](100) NULL, 
	[HOME CITY] [varchar](35) NULL, 
	[HOME STATE] [varchar](25) NULL, 
	[HOME ZIP] [varchar](25) NULL, 
	[HOME PHONE] [varchar](40) NULL, 
	[HOME FAX] [varchar](40) NULL, 
	[HOME EMAIL] [varchar](255) NULL, 
	[BIRTH DATE] datetime NULL,
	[WEBSITE] [varchar](200) NULL, 
	[PREFERRED ADDRESS] [varchar](200) NOT NULL DEFAULT (''), 
	[MEMBER TYPE] [varchar](200) NOT NULL DEFAULT (''), 
	[MEMBER TYPE CATEGORY] [varchar](200) NOT NULL DEFAULT (''), 
	[GENDER] [varchar](200) NULL , 
	[ETHNICITY] [varchar](200) NULL, 
	[MEMBER OF OTHER DENTAL ORGANIZATION] [varchar](200) NOT NULL DEFAULT (''), 
	[PREVIOUS MEMBER] [varchar](200) NOT NULL DEFAULT (''), 
	[JOIN DATE] datetime NULL,
	[PAID THRU] datetime NULL,
	[COMPONENT] [varchar](200) NOT NULL DEFAULT (''), 
	[DENTAL DEGREE] [varchar](200) NOT NULL DEFAULT (''), 
	[DENTAL SCHOOL] [varchar](200) NOT NULL DEFAULT (''), 
	[GRAD_DATE] datetime NULL,
	[GRADE] [varchar](200) NOT NULL DEFAULT (''), 
	[US RESIDENT] [varchar](200) NOT NULL DEFAULT (''), 
	[POSTDOCTORAL INSTITUTION] [varchar](200) NOT NULL DEFAULT (''), 
	[USA LICENSE] [varchar](200) NOT NULL DEFAULT (''), 
	[LICENSENO] [varchar](200) NULL, 
	[FEDERAL SERVICES] [varchar](200) NOT NULL DEFAULT (''), 
	[BRANCH] [varchar](200) NOT NULL DEFAULT (''), 
	[PRACTICE ENVIRONMENT] [varchar](200) NOT NULL DEFAULT (''), 
	[SPECIALTY] [varchar](200) NOT NULL DEFAULT ('')
)


IF OBJECT_ID('tempdb..##tagd_nationals_dues') IS NOT NULL
	DROP TABLE ##tagd_nationals_dues
CREATE TABLE ##tagd_nationals_dues (
	[rowid] [int] NOT NULL,
	[ID] [varchar](50) NOT NULL,
	[CHAPTER] [varchar](200) NOT NULL DEFAULT (''), 
	[FIRST NAME] [varchar](75) NOT NULL DEFAULT (''), 
	[MIDDLE NAME] [varchar](25) NOT NULL DEFAULT (''), 
	[LAST NAME] [varchar](75) NOT NULL DEFAULT (''), 
	[SUFFIX] [varchar](50) NOT NULL DEFAULT (''), 
	[DESIGNATION] [varchar](max) NULL, 
	[ADDRESS 1] [varchar](100) NOT NULL DEFAULT (''), 
	[ADDRESS 2] [varchar](100) NOT NULL DEFAULT (''), 
	[CITY] [varchar](35) NOT NULL DEFAULT (''), 
	[STATE] [varchar](25) NOT NULL DEFAULT (''), 
	[ZIP] [varchar](25) NOT NULL DEFAULT (''), 
	[WORK PHONE] [varchar](40) NOT NULL DEFAULT (''), 
	[FAX] [varchar](40) NOT NULL DEFAULT (''), 
	[EMAIL] [varchar](255) NULL, 
	[MEMBER TYPE] [varchar](200) NOT NULL DEFAULT (''), 
	[MEMBER TYPE DESCRIPTION] [varchar](200) NOT NULL DEFAULT (''), 
	[CATEGORY] [varchar](200) NOT NULL DEFAULT (''), 
	[CATEGORY DESCRIPTION] [varchar](200) NOT NULL DEFAULT (''), 
	[MEMBER TYPE BREAKDOWN] [varchar](200) NOT NULL DEFAULT (''), 
	[GRAD DATE] datetime NULL,
	[JOIN DATE] datetime NULL,
	[PAID THRU] datetime NULL,
	[CONSTITUENT] [varchar](200) NOT NULL DEFAULT (''), 
	[CONSTITUENT DUES] [decimal](9,2) NOT NULL, 
	[CONSTITUENT PAID] [decimal](9,2) NOT NULL, 
	[CONSTITUENT ABBREVIATION] [varchar](200) NOT NULL DEFAULT ('')
)


-- import csv files into temp tables.
BEGIN TRY
	SELECT @cmd = 'BULK INSERT ##tagd_nationals_alpha FROM ''' + @pathToImport + '1.csv'' WITH (DATAFILETYPE = ''widechar'', FIELDTERMINATOR = '''+ char(7) + ''', FIRSTROW = 2);'
	exec(@cmd)
	SELECT @cmd = 'BULK INSERT ##tagd_nationals_new FROM ''' + @pathToImport + '2.csv'' WITH (DATAFILETYPE = ''widechar'', FIELDTERMINATOR = '''+ char(7) + ''', FIRSTROW = 2);'
	exec(@cmd)
	SELECT @cmd = 'BULK INSERT ##tagd_nationals_dues FROM ''' + @pathToImport + '3.csv'' WITH (DATAFILETYPE = ''widechar'', FIELDTERMINATOR = '''+ char(7) + ''', FIRSTROW = 2);'
	exec(@cmd)
END TRY
BEGIN CATCH
	SET @importResult = '<import date="" flatfile=""><errors><error msg="Unable to import data. Ensure the columns are in the correct order and match previous imports." severity="fatal" /></errors></import>'
	GOTO on_cleanup
END CATCH


/* *********** */
/* DATA CHECKS */
/* *********** */
-- ensure IDs appear only once and set as primary key
BEGIN TRY
	ALTER TABLE ##tagd_nationals_alpha ADD CONSTRAINT PK__tagd_nationals_alpha PRIMARY KEY CLUSTERED (ID) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
END TRY
BEGIN CATCH
	SET @IDList = null
	SELECT @IDList = COALESCE(@IDList + ', ', '') + ID 
		from ##tagd_nationals_alpha
		group by ID
		having count(*) > 1
	SET @importResult = '<import date="" flatfile=""><errors><error msg="The file uploaded for Constituent Alpha Roster contains multiple rows for the following IDs: ' + @IDList + '. Ensure each ID appears only once in the file." severity="fatal" /></errors></import>'
	GOTO on_cleanup
END CATCH

-- ensure IDs appear only once and set as primary key
BEGIN TRY
	ALTER TABLE ##tagd_nationals_new ADD CONSTRAINT PK__tagd_nationals_new PRIMARY KEY CLUSTERED (ID) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
END TRY
BEGIN CATCH
	SET @IDList = null
	SELECT @IDList = COALESCE(@IDList + ', ', '') + ID 
		from ##tagd_nationals_new
		group by ID
		having count(*) > 1
	SET @importResult = '<import date="" flatfile=""><errors><error msg="The file uploaded for Constituent New Members contains multiple rows for the following IDs: ' + @IDList + '. Ensure each ID appears only once in the file." severity="fatal" /></errors></import>'
	GOTO on_cleanup
END CATCH

-- no missing PAID THRU dates for accounting
IF EXISTS (select top 1 ID from ##tagd_nationals_dues where [PAID THRU] is null) BEGIN
	SET @IDList = null
	SELECT @IDList = COALESCE(@IDList + ', ', '') + ID 
		from ##tagd_nationals_dues
		where [PAID THRU] is null
		group by ID
	SET @importResult = '<import date="" flatfile=""><errors><error msg="The file uploaded for Constituent Dues contains missing Paid Thru Dates for the following IDs: ' + @IDList + '. Ensure each row has a Paid Thru Date in the file." severity="fatal" /></errors></import>'
	GOTO on_cleanup
END

-- no negative amounts for accounting
IF EXISTS (select top 1 ID from ##tagd_nationals_dues where [Constituent PAID] < 0) BEGIN
	SET @IDList = null
	SELECT @IDList = COALESCE(@IDList + ', ', '') + ID 
		from ##tagd_nationals_dues
		where [Constituent PAID] < 0
		group by ID
	SET @importResult = '<import date="" flatfile=""><errors><error msg="The file uploaded for Constituent Dues contains negative CONSTITUENT PAID amounts for the following IDs: ' + @IDList + '. Negative amounts are not permitted." severity="fatal" /></errors></import>'
	GOTO on_cleanup
END



/* ****************** */
/* MEMBER DATA IMPORT */
/* ****************** */
-- flatten alpha table
IF OBJECT_ID('tempdb..##tagd_nationals_Members') IS NOT NULL
	DROP TABLE ##tagd_nationals_Members
select 
	m.prefix, 
	alpha.[FIRST NAME] as firstname, alpha.[MIDDLE NAME] as middlename, alpha.[LAST NAME] as lastname, alpha.SUFFIX as suffix, 
	cast(alpha.[DESIGNATION] as varchar(100)) as professionalSuffix, alpha.[ID] as memberNumber,
	m.company,
	
	vw.[Mailing Address_attn], 
	alpha.[ADDRESS 1] as [Mailing Address_address1], alpha.[ADDRESS 2] as [Mailing Address_address2], alpha.[CITY] as [Mailing Address_city],
	alpha.[STATE PROVINCE] as [Mailing Address_stateprov], alpha.[ZIP] as [Mailing Address_postalCode], cast('' as varchar(50)) as [Mailing Address_county],
	cast('United States' as varchar(100)) as [Mailing Address_country], alpha.[WORK PHONE] as [Mailing Address_Phone], alpha.[FAX] as [Mailing Address_Fax],
	vw.[Mailing Address_Cell], vw.[Mailing Address_2nd Phone],
 
	vw.[Office Address_address1], vw.[Office Address_address2], vw.[Office Address_city], vw.[Office Address_stateprov],
	vw.[Office Address_postalCode], vw.[Office Address_county], vw.[Office Address_country], vw.[Office Address_Phone], 
	vw.[Office Address_Fax], vw.[Office Address_Cell], vw.[Office Address_2nd Phone],

	vw.[Other Address_address1], vw.[Other Address_address2], vw.[Other Address_city], vw.[Other Address_stateprov], vw.[Other Address_postalCode],
	vw.[Other Address_county], vw.[Other Address_country], vw.[Other Address_Phone], vw.[Other Address_Fax], vw.[Other Address_Cell],
	vw.[Other Address_2nd Phone],

	coalesce(new.[HOME ADDRESS 1],vw.[Home Address_address1]) as [Home Address_address1],
	coalesce(new.[HOME ADDRESS 2],vw.[Home Address_address2]) as [Home Address_address2],	
	coalesce(new.[HOME CITY],vw.[Home Address_city]) as [Home Address_city],	
	coalesce(new.[HOME STATE],vw.[Home Address_stateprov]) as [Home Address_stateprov], 
	coalesce(new.[HOME ZIP],vw.[Home Address_postalCode]) as [Home Address_postalCode],
	vw.[Home Address_county], 
	case when new.[HOME STATE] is not null then 'United States' else vw.[Home Address_country] end as [Home Address_country], 
	alpha.[HOME PHONE] as [Home Address_Phone], 
	coalesce(new.[HOME FAX],vw.[Home Address_Fax]) as [Home Address_Fax],
	vw.[Home Address_Cell], vw.[Home Address_2nd Phone],

	coalesce(new.WEBSITE,vw.[Business Website]) as [Business Website],
	alpha.Email as [Email],
	coalesce(new.[HOME EMAIL],vw.[Personal Email]) as [Personal Email],
	vw.[Other Email],

	alpha.[JOIN DATE] as [AGD Join Date],
	cast(alpha.[ID] as varchar(255)) as [AGD Member Number], 
	alpha.[PAID THRU] as [AGD Paid Thru Date],
	cast(alpha.[FIRST NAME] as varchar(255)) as [Badge Name], 

	cast(case
	when alpha.[MEMBER TYPE] = 'AC' and alpha.[CATEGORY DESCRIPTION] = 'Disability Waiver' then 'General Dentist|Dues Waived'
	when alpha.[MEMBER TYPE] IN ('AC','EM','HM') then 'General Dentist'
	when alpha.[MEMBER TYPE] = 'AF' then 'Affiliate'
	when alpha.[MEMBER TYPE] = 'AS' then 'General Dentist'
	when alpha.[MEMBER TYPE] = 'RE' then 'Retired'
	when alpha.[MEMBER TYPE] = 'ST' then 'Student'
	else '' end as varchar(max)) as [Contact Type],
	alpha.[BIRTH DATE] as [Date of Birth],
	replace(replace(alpha.[DESIGNATION],',','|'),' ','') as [Designation],
	case 
	when new.ETHNICITY is not null and new.ETHNICITY = 'African American' then 'African-American'
	when new.ETHNICITY is not null and new.ETHNICITY = 'Hispanic or Latino' then 'Hispanic'
	when new.ETHNICITY is not null and new.ETHNICITY = 'White or Caucasian' then 'Caucasian'
	when new.ETHNICITY is not null and new.ETHNICITY = 'Asian or Pacific IslANDer' then 'Asian'
	else coalesce(new.ETHNICITY,vw.[Ethnicity])	end as [Ethnicity],
	coalesce(new.GENDER,vw.[Gender]) as [Gender],
	alpha.[GRAD DATE] as [Graduation Date],
	coalesce(new.LICENSENO,vw.[License Number]) as [License Number],
	cast(case
	when alpha.[MEMBER TYPE] = 'AC' and alpha.[CATEGORY DESCRIPTION] = 'Disability Waiver' then 'Disability Waiver'
	when alpha.[MEMBER TYPE] = 'AC' and alpha.[CATEGORY DESCRIPTION] = 'Residency' then 'Resident'
	when alpha.[MEMBER TYPE] = 'AC' then 'Active General Dentist'
	when alpha.[MEMBER TYPE] = 'AF' then 'Affiliate'
	when alpha.[MEMBER TYPE] = 'AS' then 'Associate'
	when alpha.[MEMBER TYPE] = 'EM' then 'Emeritus'
	when alpha.[MEMBER TYPE] = 'HM' then 'Honorary Member'
	when alpha.[MEMBER TYPE] = 'RE' then 'Retired'
	when alpha.[MEMBER TYPE] = 'ST' then 'Student Member'
	else '' end as varchar(255)) as [MemberType],
	cast(case 
	when alpha.[DENTAL SCHOOL] = 'Texas A&M Health Science Center Baylor College of Dentistry' then null
	when alpha.[DENTAL SCHOOL] = 'University of Texas Dental Branch at Houston' then null
	when alpha.[DENTAL SCHOOL] = 'University of Texas Health Science Center at San Antonio Dental School' then null
	else alpha.[DENTAL SCHOOL] end as varchar(255)) as [Other Dental School],
	cast(case alpha.[DENTAL SCHOOL]
	when 'Texas A&M Health Science Center Baylor College of Dentistry' then 'Baylor College of Dentistry'
	when 'University of Texas Dental Branch at Houston' then 'UTHSCS Houston'
	when 'University of Texas Health Science Center at San Antonio Dental School' then 'UTHSC San Antonio'
	else null end as varchar(255)) as [Texas Dental School],
	coalesce(new.[PRACTICE ENVIRONMENT],vw.[Type of Practice]) as [Type of Practice]
into ##tagd_nationals_Members
from ##tagd_nationals_alpha as alpha
left outer join membercentral.dbo.ams_members as m
	inner join membercentral.dbo.vw_memberdata_TAGD as vw on vw.memberID = m.memberID
	on m.orgID = @orgID and m.membernumber = alpha.[ID] and m.status <> 'D' and m.memberid = m.activeMemberID
left outer join ##tagd_nationals_new as new on new.ID = alpha.ID

-- get active members in database now so we dont inactivate them
insert into ##tagd_nationals_Members (
	[prefix],[firstname],[middlename],[lastname],[suffix],[professionalSuffix],[memberNumber],[company],[Mailing Address_address1],[Mailing Address_address2],
	[Mailing Address_city],[Mailing Address_stateprov],[Mailing Address_postalCode],[Mailing Address_county],[Mailing Address_country],[Mailing Address_Phone],
	[Mailing Address_Fax],[Mailing Address_Cell],[Mailing Address_2nd Phone],[Office Address_address1],[Office Address_address2],[Office Address_city],
	[Office Address_stateprov],[Office Address_postalCode],[Office Address_county],[Office Address_country],[Office Address_Phone],[Office Address_Fax],
	[Office Address_Cell],[Office Address_2nd Phone],[Other Address_address1],[Other Address_address2],[Other Address_city],[Other Address_stateprov],
	[Other Address_postalCode],[Other Address_county],[Other Address_country],[Other Address_Phone],[Other Address_Fax],[Other Address_Cell],
	[Other Address_2nd Phone],[Home Address_address1],[Home Address_address2],[Home Address_city],[Home Address_stateprov],[Home Address_postalCode],
	[Home Address_county],[Home Address_country],[Home Address_Phone],[Home Address_Fax],[Home Address_Cell],[Home Address_2nd Phone],[Business Website],
	[Email],[Personal Email],[Other Email],[AGD Join Date],[AGD Member Number],[AGD Paid Thru Date],[Badge Name],[Contact Type],[Date of Birth],[Designation],
	[Ethnicity],[Gender],[Graduation Date],[License Number],[MemberType],[Other Dental School],[Texas Dental School],[Type of Practice]
)
select m.prefix, m.firstname, m.middlename, m.lastname, m.suffix, m.professionalSuffix, m.membernumber, m.company, vw.[Mailing Address_address1], vw.[Mailing Address_address2],
	vw.[Mailing Address_city],vw.[Mailing Address_stateprov],vw.[Mailing Address_postalCode],vw.[Mailing Address_county],vw.[Mailing Address_country],vw.[Mailing Address_Phone],
	vw.[Mailing Address_Fax],vw.[Mailing Address_Cell],vw.[Mailing Address_2nd Phone],vw.[Office Address_address1],vw.[Office Address_address2],vw.[Office Address_city],
	vw.[Office Address_stateprov],vw.[Office Address_postalCode],vw.[Office Address_county],vw.[Office Address_country],vw.[Office Address_Phone],vw.[Office Address_Fax],
	vw.[Office Address_Cell],vw.[Office Address_2nd Phone],vw.[Other Address_address1],vw.[Other Address_address2],vw.[Other Address_city],vw.[Other Address_stateprov],
	vw.[Other Address_postalCode],vw.[Other Address_county],vw.[Other Address_country],vw.[Other Address_Phone],vw.[Other Address_Fax],vw.[Other Address_Cell],
	vw.[Other Address_2nd Phone],vw.[Home Address_address1],vw.[Home Address_address2],vw.[Home Address_city],vw.[Home Address_stateprov],vw.[Home Address_postalCode],
	vw.[Home Address_county],vw.[Home Address_country],vw.[Home Address_Phone],vw.[Home Address_Fax],vw.[Home Address_Cell],vw.[Home Address_2nd Phone],vw.[Business Website],
	vw.[Email],vw.[Personal Email],vw.[Other Email],vw.[AGD Join Date],vw.[AGD Member Number],vw.[AGD Paid Thru Date],vw.[Badge Name],vw.[Contact Type],vw.[Date of Birth],vw.[Designation],
	vw.[Ethnicity],vw.[Gender],vw.[Graduation Date],vw.[License Number],vw.[MemberType],vw.[Other Dental School],vw.[Texas Dental School],vw.[Type of Practice]
from membercentral.dbo.ams_members as m
inner join membercentral.dbo.vw_memberdata_TAGD as vw on vw.memberID = m.memberID
where m.orgID = @orgID
and m.status = 'A'
and m.memberid = m.activeMemberID
and m.memberTypeID = 2
and not exists (select membernumber from ##tagd_nationals_Members where membernumber = m.membernumber)

-- add rowID
ALTER TABLE ##tagd_nationals_Members ADD rowIDtemp int IDENTITY(1,1), rowID int NULL;
update ##tagd_nationals_Members set rowID = rowIDtemp;
ALTER TABLE ##tagd_nationals_Members DROP COLUMN rowIDtemp;

-- move data to holding tables
EXEC memberCentral.dbo.ams_importMemberData_tempToHolding @orgid=@orgID, @tmptbl='##tagd_nationals_Members', @pathToExport=@pathToExport, @importResult=@importResult OUTPUT

-- if no errors, run member import
IF @importResult.value('count(/import/errors/error)','int') = 0
BEGIN
	SELECT @flatfile = @importResult.value('(/import/@flatfile)[1]','varchar(160)')
	EXEC membercentral.dbo.ams_importMemberData_holdingToPerm @orgID=@orgID, @flatfile=@flatfile

	DECLARE @smtpserver varchar(20)
	SELECT @smtpserver = smtpserver from membercentral.dbo.fn_getServerSettings()		

	BEGIN TRY
		EXEC membercentral.dbo.ams_importMemberDataHoldingResultReport @importResult=@importResult, @finalMSG=@finalMSG OUTPUT
		EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject='TAGD Membership Import Results',
			@message=@finalMSG, @priority='normal', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
		SELECT @emailtouse = emailImportResults from membercentral.dbo.organizations where orgID = @orgID	
		IF len(@emailtouse) > 0 BEGIN
			EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to=@emailtouse,
				@cc=null, @bcc='<EMAIL>', @subject='TAGD Membership Import Results',
				@message=@finalMSG, @priority='normal', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
		END
	END TRY
	BEGIN CATCH      
		DECLARE @ErrorMessage VARCHAR(max)      
		DECLARE @ErrorSeverity INT      
		DECLARE @ErrorState INT        
		SELECT @ErrorMessage = 'Error running TAGD Membership Import' + char(13) + char(10) + ERROR_MESSAGE(),   
			@ErrorSeverity = ERROR_SEVERITY(),   
			@ErrorState = ERROR_STATE()        
		EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject='Error Importing TAGD Data',
			@message=@ErrorMessage, @priority='high', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END CATCH


	/* ***************** */
	/* ACCOUNTING IMPORT */
	/* ***************** */
	-- prep acct table (ignore $0)
	IF OBJECT_ID('tempdb..##tagd_nationals_acct') IS NOT NULL
		DROP TABLE ##tagd_nationals_acct
	select 'TAGD Dues ' + cast(year([PAID THRU]) as varchar(4)) as SaleDescription,
		@batchDate as saleDate,
		[ID] as saleMemberID,
		[Constituent PAID] as saleAmount,
		'4010' as saleRevenueGL,
		rowID as saleID,
		rowID as rowID
	into ##tagd_nationals_acct
	from ##tagd_nationals_dues
	where [Constituent PAID] <> 0

	set @importResult = null
	EXEC membercentral.dbo.tr_importTransactions_toTemp @orgid=@orgID, @payProfileCode='AGDimport', @tmptbl='##tagd_nationals_acct', @pathToExport=@pathToExport, @importResult=@importResult OUTPUT

	-- if no errors, run accounting import
	IF @importResult.value('count(/import/errors/error)','int') = 0
	BEGIN
		SELECT @flatfile = @importResult.value('(/import/@flatfile)[1]','varchar(160)')
		set @importResult = null
		EXEC membercentral.dbo.tr_importTransactions_toPerm @orgID=@orgID, @siteID=@siteID, @recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, @flatfile=@flatfile, @importResult=@importResult OUTPUT


		IF @importResult.value('count(/import/errors/error)','int') = 0
		BEGIN
			/* ******************* */
			/* SUBSCRIPTION IMPORT */
			/* ******************* */
			-- Start prep work for importing subs
			IF OBJECT_ID('tempdb..#tagd_sub_renewals') IS NOT NULL
				DROP TABLE #tagd_sub_renewals
			IF OBJECT_ID('tempdb..#tagd_sub_newmembers') IS NOT NULL
				DROP TABLE #tagd_sub_newmembers
			IF OBJECT_ID('tempdb..#tagd_sub_newComponent') IS NOT NULL
				DROP TABLE #tagd_sub_newComponent
			IF OBJECT_ID('tempdb..#tagd_sub_switchedComponent') IS NOT NULL
				DROP TABLE #tagd_sub_switchedComponent
			IF OBJECT_ID('tempdb..#tagd_memberType') IS NOT NULL
				DROP TABLE #tagd_memberType
			IF OBJECT_ID('tempdb..##tagd_import_subscriptions') IS NOT NULL
				DROP TABLE ##tagd_import_subscriptions

			declare @subscriberIDList varchar(8000), @membershipSubscriptionType varchar(100), 
				@componentSubscriptionType varchar(100), @membershipSubscriptionName varchar(100),
				@thismemberid int, @thissubscriptionID int, @thisparentSubscriberID int, 
				@thisRFID int, @thisGLAccountID int, @thisstatus varchar(1), @thissubStartDate datetime,
				@thissubEndDate datetime, @thisgraceEndDate datetime, @thispcfree bit, 
				@thisactivationOptionCode char(1), @thisrecordedByMemberID int,  @thisbypassQueue bit,
				@thissubscriberID int, @thisexistingComponentSubscriberID int, @trashID int,
				@autoID int, @currentTimeStamp datetime
			set @currentTimeStamp = getdate()
			set @membershipSubscriptionType = 'Membership Dues'
			set @componentSubscriptionType = 'Components'
			set @membershipSubscriptionName = 'TAGD Member'

			CREATE TABLE #tagd_sub_renewals (memberID int, memberNumber varchar(100), subStartDate datetime,
				subEndDate datetime, rootSubscriptionID int, componentSubscriptionID int, treecode uniqueIdentifier)
			CREATE TABLE #tagd_sub_newmembers (memberID int, memberNumber varchar(100), subStartDate datetime,
				subEndDate datetime, rootSubscriptionID int, componentSubscriptionID int, treecode uniqueIdentifier)
			CREATE TABLE #tagd_sub_newComponent (autoID int IDENTITY(1,1) PRIMARY KEY, memberID int,
				memberNumber varchar(100), existingRootSubscriberID int, existingComponentSubscriberID int,
				subStartDate datetime, subEndDate datetime, graceEndDate datetime, rootSubscriptionID int,
				componentSubscriptionID int, rfid int, GLAccountID int, status char(1), pcfree bit, 
				activationOptionCode varchar(1), existingComponentSubscriptionID int)
			CREATE TABLE #tagd_sub_switchedComponent (autoID int IDENTITY(1,1) PRIMARY KEY, memberID int,
				memberNumber varchar(100), existingRootSubscriberID int, existingComponentSubscriberID int,
				subStartDate datetime, subEndDate datetime, graceEndDate datetime, rootSubscriptionID int,
				componentSubscriptionID int, rfid int, GLAccountID int, status char(1), pcfree bit, 
				activationOptionCode varchar(1), existingComponentSubscriptionID int)
			CREATE TABLE #tagd_memberType (tempMemberTypeId int NOT NULL identity(1,1),
				ValueForMemberTypeCustomField varchar(255) NOT NULL DEFAULT (''), 
				MemberTypeValueFromAGDAlphaRoster varchar(10) NOT NULL DEFAULT (''), 
				CategoryDescriptionValueFromAGDAlphaRoster varchar(255) NULL DEFAULT (''), 
				MembershipDuesTAGDMemberSubRate varchar(255) NOT NULL DEFAULT ('')
			)

			insert into #tagd_sub_renewals (memberID, memberNumber, subStartDate, subEndDate, rootSubscriptionID, 
				componentSubscriptionID, treecode)
			select activeMember.memberID, na.id, 
				newStartDate = case 
					when (isnull(na.[JOIN DATE],'1/1/1900') > cast (cast(YEAR(na.[PAID THRU]) as nvarchar)+'0101' as datetime)) and (na.[JOIN DATE] > DATEADD(dd, DATEDIFF(dd, 0,dateadd(d,1,max(ss.subenddate))), 0))
						then isnull(na.[JOIN DATE],'1/1/1900')
					when (max(ss.subenddate) > cast (cast(YEAR(na.[PAID THRU]) as nvarchar)+'0101' as datetime)) and (DATEADD(dd, DATEDIFF(dd, 0,dateadd(d,1,max(ss.subenddate))), 0) > isnull(na.[JOIN DATE],'1/1/1900'))
						then DATEADD(dd, DATEDIFF(dd, 0,dateadd(d,1,max(ss.subenddate))), 0)
					else
						cast (cast(YEAR(na.[PAID THRU]) as nvarchar)+'0101' as datetime) 
					end,
				newSubEndDate = dateadd(ms,-3,dateadd(day,1,[Paid Thru])),
				subs.subscriptionID as rootSubscriptionID, 
				csubs.subscriptionID as componentSubscripionID,
				treecode = newid()
			from membercentral.dbo.sub_types t
			inner join membercentral.dbo.sub_subscriptions subs on subs.typeID = t.typeID
				and t.typeName = @membershipSubscriptionType
				and subs.subscriptionName = @membershipSubscriptionName
				and t.siteID = @siteID
			inner join membercentral.dbo.sub_subscribers ss on ss.subscriptionID = subs.subscriptionID
				and ss.parentSubscriberID is null
			inner join membercentral.dbo.sub_statuses st on st.statusID = ss.statusID
				and st.statuscode in ('A','I','P')
			inner join membercentral.dbo.ams_members m on m.memberID = ss.memberID
			inner join membercentral.dbo.ams_members activeMember on activeMember.memberID = m.activeMemberID
				and activeMember.status in ('A','I')
			--make sure we have subscriberID with the latest end date
			left outer join membercentral.dbo.sub_subscribers otherterms_ss
				inner join membercentral.dbo.sub_statuses otherterms_st on otherterms_st.statusID = otherterms_ss.statusID
					and otherterms_st.statuscode in ('A','I','P')
				inner join membercentral.dbo.ams_members otherterms_m on otherterms_m.memberID = otherterms_ss.memberID
				on ss.subscriptionID = otherterms_ss.subscriptionID
					and ss.subenddate < otherterms_ss.subenddate
					and otherterms_m.activeMemberID = activeMember.memberID
			inner join ##tagd_nationals_alpha na on na.id = activeMember.membernumber
				and na.[Paid Thru] is not null
				and ss.subenddate < na.[paid thru]
				and na.[paid thru] > @currentTimeStamp
			inner join membercentral.dbo.sub_types ct on ct.siteID = t.siteID
				and ct.typeName = @componentSubscriptionType
			inner join membercentral.dbo.sub_subscriptions csubs on csubs.typeID = ct.typeID
				and csubs.subscriptionName = na.[COMPONENT TITLE]
			where otherterms_ss.subscriberID is null
			group by activeMember.memberID,na.id,  na.[paid thru], na.[JOIN DATE], subs.subscriptionID, csubs.subscriptionID
			order by na.[JOIN DATE] desc

			insert into #tagd_sub_newmembers (memberID, memberNumber,subStartDate,subEndDate, rootSubscriptionID, componentSubscriptionID,treecode)
			select 
				activeMember.memberID,
				na.id,
				newStartDate = case 
					when (isnull(na.[JOIN DATE],'1/1/1900') > cast (cast(YEAR(na.[PAID THRU]) as nvarchar)+'0101' as datetime))
						then isnull(na.[JOIN DATE],'1/1/1900')
					else
						cast (cast(YEAR(na.[PAID THRU]) as nvarchar)+'0101' as datetime) 
					end,
				newSubEndDate = dateadd(ms,-3,dateadd(day,1,[Paid Thru])),
				subs.subscriptionID as rootSubscriptionID, 
				csubs.subscriptionID as componentSubscripionID,
				treecode = newid()
			from 
				##tagd_nationals_alpha na
				inner join membercentral.dbo.ams_members activeMember
					on activeMember.memberNumber = na.id
					and activeMember.orgID = @orgID
					and activeMember.status in ('A','I')
					and activeMember.memberID = activeMember.activeMemberID
					and na.[paid thru] > @currentTimeStamp
				inner join membercentral.dbo.ams_members m
					on activeMember.memberID = m.activeMemberID
				inner join membercentral.dbo.sub_types t
					on t.typeName = @membershipSubscriptionType
					and t.siteID = @siteID
				inner join membercentral.dbo.sub_subscriptions subs
					on subs.typeID = t.typeID
					and subs.subscriptionName = @membershipSubscriptionName
				left outer join membercentral.dbo.sub_subscribers ss
					inner join membercentral.dbo.sub_statuses st
						on st.statusID = ss.statusID
						and st.statuscode in ('A','I','P')
						and ss.parentSubscriberID is null
				on m.memberID = ss.memberID
					and ss.subscriptionID = subs.subscriptionID
				inner join membercentral.dbo.sub_types ct
					on ct.siteID = @siteID
					and ct.typeName = @componentSubscriptionType
				inner join membercentral.dbo.sub_subscriptions csubs
					on csubs.typeID = ct.typeID
					and csubs.subscriptionName = na.[COMPONENT TITLE]
			group by 
				activeMember.memberID, na.id,  na.[paid thru], na.[JOIN DATE], subs.subscriptionID, csubs.subscriptionID
			having 
				max(isnull(ss.subscriberID,0)) = 0
			order by 
				na.[JOIN DATE] desc

			insert into #tagd_sub_newComponent (
				memberID, 
				memberNumber,
				subStartDate,
				subEndDate, 
				graceEndDate, 
				rootSubscriptionID, 
				componentSubscriptionID, 
				existingRootSubscriberID, 
				existingComponentSubscriberID,
				existingComponentSubscriptionID
			)
			select 
				activeMember.memberID,
				na.id,
				-- case statement to handle changing future Accepted subscriptions
				newStartDate = case 
					when (@currentTimeStamp > ss.substartDate)
						then @currentTimeStamp
					else
						ss.substartDate
					end,
				newSubEndDate = ss.subEndDate,
				graceEndDate = cast (cast(YEAR(ss.subEndDate) + 1 as nvarchar)+'0331' as datetime),
				subs.subscriptionID as rootSubscriptionID, 
				correct_csubs.subscriptionID as componentSubscripionID,
				ss.rootSubscriberID as existingRootSubscriberID, 
				current_css.subscriberID as existingComponentSubscriberID,
				current_css.subscriptionID as existingComponentSubscriptionID
			from 
				membercentral.dbo.sub_types t
				inner join membercentral.dbo.sub_subscriptions subs
					on subs.typeID = t.typeID
					and t.typeName = @membershipSubscriptionType
					and subs.subscriptionName = @membershipSubscriptionName
					and t.siteID = @siteID
				inner join membercentral.dbo.sub_subscribers ss
					on ss.subscriptionID = subs.subscriptionID
					and ss.parentSubscriberID is null
				inner join membercentral.dbo.sub_statuses st
					on st.statusID = ss.statusID
					and st.statuscode in ('A','I','P')
				inner join membercentral.dbo.ams_members m
					on m.memberID = ss.memberID
				inner join membercentral.dbo.ams_members activeMember
					on activeMember.memberID = m.activeMemberID
					and activeMember.status in ('A','I')
				inner join ##tagd_nationals_alpha na
					on na.id = activeMember.membernumber
					and na.[Paid Thru] is not null
				inner join membercentral.dbo.sub_types ct
					on ct.siteID = t.siteID
					and ct.typeName = @componentSubscriptionType
				inner join membercentral.dbo.sub_subscriptions correct_csubs
					on correct_csubs.typeID = ct.typeID
					and correct_csubs.subscriptionName = na.[COMPONENT TITLE]
				left outer join membercentral.dbo.sub_subscribers current_css
					inner join membercentral.dbo.sub_statuses current_cst
						on current_cst.statusID = current_css.statusID
						and current_cst.statuscode in ('A','I','P')
					on current_css.parentSubscriberID = ss.subscriberID
				and correct_csubs.subscriptionID = current_css.subscriptionID
			where
				current_css.subscriptionID is null
			group by 
				current_cst.statuscode, activeMember.memberID,na.id,  na.[paid thru], na.[JOIN DATE], subs.subscriptionID, 
				correct_csubs.subscriptionID, ss.rootSubscriberID, current_css.subscriberID,
				current_css.subscriptionID, ss.substartDate, ss.subEndDate
			order by na.[JOIN DATE] desc

			insert into #tagd_sub_switchedComponent (
				memberID, 
				memberNumber,
				subStartDate,
				subEndDate, 
				graceEndDate, 
				rootSubscriptionID, 
				componentSubscriptionID, 
				existingRootSubscriberID, 
				existingComponentSubscriberID,
				existingComponentSubscriptionID
			)
			select 
				activeMember.memberID,
				na.id,
				-- case statement to handle changing future Accepted subscriptions
				newStartDate = case 
					when (@currentTimeStamp > ss.substartDate)
						then @currentTimeStamp
					else
						ss.substartDate
					end,
				newSubEndDate = ss.subEndDate,
				graceEndDate = cast (cast(YEAR(ss.subEndDate) + 1 as nvarchar)+'0331' as datetime),
				subs.subscriptionID as rootSubscriptionID, 
				correct_csubs.subscriptionID as componentSubscripionID,
				ss.rootSubscriberID as existingRootSubscriberID, 
				current_css.subscriberID as existingComponentSubscriberID,
				current_css.subscriptionID as existingComponentSubscriptionID
			from 
				membercentral.dbo.sub_types t
				inner join membercentral.dbo.sub_subscriptions subs
					on subs.typeID = t.typeID
					and t.typeName = @membershipSubscriptionType
					and subs.subscriptionName = @membershipSubscriptionName
					and t.siteID = @siteID
				inner join membercentral.dbo.sub_subscribers ss
					on ss.subscriptionID = subs.subscriptionID
					and ss.parentSubscriberID is null
				inner join membercentral.dbo.sub_statuses st
					on st.statusID = ss.statusID
					and st.statuscode in ('A','I','P')
				inner join membercentral.dbo.ams_members m
					on m.memberID = ss.memberID
				inner join membercentral.dbo.ams_members activeMember
					on activeMember.memberID = m.activeMemberID
					and activeMember.status in ('A','I')
				inner join ##tagd_nationals_alpha na
					on na.id = activeMember.membernumber
					and na.[Paid Thru] is not null
				inner join membercentral.dbo.sub_types ct
					on ct.siteID = t.siteID
					and ct.typeName = @componentSubscriptionType
				inner join membercentral.dbo.sub_subscriptions correct_csubs
					on correct_csubs.typeID = ct.typeID
					and correct_csubs.subscriptionName = na.[COMPONENT TITLE]
				inner join membercentral.dbo.sub_subscribers current_css
					on current_css.parentSubscriberID = ss.subscriberID
					and correct_csubs.subscriptionID <> current_css.subscriptionID
				inner join membercentral.dbo.sub_statuses current_cst
					on current_cst.statusID = current_css.statusID
					and current_cst.statuscode in ('A','I','P')
			group by 
				activeMember.memberID,na.id,  na.[paid thru], na.[JOIN DATE], subs.subscriptionID, 
				correct_csubs.subscriptionID, ss.rootSubscriberID, current_css.subscriberID,
				current_css.subscriptionID, ss.substartDate, ss.subEndDate
			order by na.[JOIN DATE] desc

			update sc set
				GLAccountID = case
					when subs.allowRateGLAccountOverride = 1 then isnull(r.GLAccountID,subs.GLAccountID) 
					else subs.GLAccountID
				end, 
				rfid=rf.rfID,
				pcfree=0, 
				activationOptionCode = sao.subActivationCode,
				Status = case 
					when subStartDate > @currentTimeStamp then 'P'
					else 'A'
				end
			from 
				#tagd_sub_switchedComponent sc
				inner join membercentral.dbo.sub_subscriptions subs
					on subs.subscriptionID = sc.componentSubscriptionID
				inner join membercentral.dbo.sub_rateSchedules as rs 
					on subs.scheduleID = rs.scheduleID 
					and rs.status = 'A'
				inner join membercentral.dbo.sub_rates r
					on r.scheduleID = rs.scheduleID
					and r.status = 'A'
					and r.isRenewalRate = 0 
					and r.rateName = 'Annual Term'
				inner join membercentral.dbo.sub_rateFrequencies rf on
					rf.rateID = r.rateID
					and rf.status = 'A'
				inner join membercentral.dbo.sub_frequencies f on
					f.frequencyID = rf.frequencyID
					and f.status = 'A'
				inner join membercentral.dbo.sub_rateFrequenciesMerchantProfiles as rfmp on 
					rfmp.rfid = rf.rfID 
					and rfmp.status = 'A' 
				inner join membercentral.dbo.mp_profiles as mp on 
					mp.profileID = rfmp.profileID 
					and mp.status = 'A' 
				inner join membercentral.dbo.sub_activationOptions sao
					on sao.subActivationID = subs.subAlternateActivationID

			update nc set
				GLAccountID = case
					when subs.allowRateGLAccountOverride = 1 then isnull(r.GLAccountID,subs.GLAccountID) 
					else subs.GLAccountID
				end, 
				rfid=rf.rfID,
				pcfree=0, 
				activationOptionCode = sao.subActivationCode,
				Status = case 
					when subStartDate > @currentTimeStamp then 'P'
					else 'A'
				end
			from 
				#tagd_sub_newComponent nc
				inner join membercentral.dbo.sub_subscriptions subs
					on subs.subscriptionID = nc.componentSubscriptionID
				inner join membercentral.dbo.sub_rateSchedules as rs 
					on subs.scheduleID = rs.scheduleID 
					and rs.status = 'A'
				inner join membercentral.dbo.sub_rates r
					on r.scheduleID = rs.scheduleID
					and r.status = 'A'
					and r.isRenewalRate = 0 
					and r.rateName = 'Annual Term'
				inner join membercentral.dbo.sub_rateFrequencies rf on
					rf.rateID = r.rateID
					and rf.status = 'A'
				inner join membercentral.dbo.sub_frequencies f on
					f.frequencyID = rf.frequencyID
					and f.status = 'A'
				inner join membercentral.dbo.sub_rateFrequenciesMerchantProfiles as rfmp on 
					rfmp.rfid = rf.rfID 
					and rfmp.status = 'A' 
				inner join membercentral.dbo.mp_profiles as mp on 
					mp.profileID = rfmp.profileID 
					and mp.status = 'A' 
				inner join membercentral.dbo.sub_activationOptions sao
					on sao.subActivationID = subs.subAlternateActivationID

			insert into #tagd_memberType (
				ValueForMemberTypeCustomField,
				MemberTypeValueFromAGDAlphaRoster,
				CategoryDescriptionValueFromAGDAlphaRoster,
				MembershipDuesTAGDMemberSubRate
			)
			select 'Active Member', 'AC', '',	'Active General Dentist'
			union
			select 'Affiliate', 'AF', '',	'Affiliate'
			union
			select 'Associate', 'AS', '',	'Associate'
			union
			select 'Disability Waiver', 'AC', 'Disability Waiver',	'Dues Waiver'
			union
			select 'Emeritus Member', 'EM', '',	'Emeritus'
			union
			select 'Honorary Member', 'HM', '',	'Honorary Member'
			union
			select 'Retired', 'RE', '',	'Retired'
			union
			select 'Resident', 'AC', 'Residency',	'Resident'
			union
			select 'Student Member', 'ST', '',	'Student'
			union
			select 'Financial Waiver', 'AC', 'Financial Waiver','Dues Waiver'

			IF OBJECT_ID('tempdb..#temp_tagd_nationals_alpha') IS NOT NULL
				DROP TABLE #temp_tagd_nationals_alpha

			-- Inner join alpha with memberType so we can get the right rate
			select * 
			into #temp_tagd_nationals_alpha
			from ##tagd_nationals_alpha na
			left outer join #tagd_memberType mt on ltrim(rtrim(mt.MemberTypeValueFromAGDAlphaRoster)) = ltrim(rtrim(na.[member type]))
				and ltrim(rtrim(mt.CategoryDescriptionValueFromAGDAlphaRoster )) = ltrim(rtrim(na.[category description]))
			order by na.[category description] desc

			-- Get the query to be passed to import subscriptions stored proc
			select *  
			into ##tagd_import_subscriptions 
			from (
				select 
					distinct renewals.memberNumber
					,@membershipSubscriptionType as SubscriptionType
					, subs.SubscriptionName
					,r.RateName
					, rf.rateAmt as LastPrice
					, f.frequencyShortName as Frequency
					, 'NO' as StoreModifiedRate
					, renewals.subStartDate as StartDate
					, renewals.subEndDate as EndDate
					, cast (cast(YEAR(renewals.subEndDate) + 1 as nvarchar)+'0331' as datetime) as graceEndDate
					, Status = case 
						when renewals.subStartDate > @currentTimeStamp then 'P'
						else 'A'
					end
					, NULL as ParentSubscriptionType
					, NULL  as ParentSubscriptionName
					, renewals.treecode as TreeCode
				from (
					select memberID, memberNumber,subStartDate,subEndDate, rootSubscriptionID, componentSubscriptionID, treecode from #tagd_sub_renewals
					union
					select memberID, memberNumber,subStartDate,subEndDate, rootSubscriptionID, componentSubscriptionID, treecode from #tagd_sub_newmembers 
				) as renewals
				inner join membercentral.dbo.sub_subscriptions as subs
					on renewals.rootSubscriptionID = subs.subscriptionID
					and subs.status = 'A' 
				inner join membercentral.dbo.sub_rateSchedules as rs 
					on subs.scheduleID = rs.scheduleID 
					and rs.status = 'A'
				inner join membercentral.dbo.sub_rates r
					on r.scheduleID = rs.scheduleID
					and r.status = 'A'
					and r.isRenewalRate = 0 
				inner join #tagd_memberType mt on 
					ltrim(rtrim(mt.MembershipDuesTAGDMemberSubRate)) = ltrim(rtrim(r.rateName))
				inner join #temp_tagd_nationals_alpha tmt on
					tmt.tempMemberTypeId = mt.tempMemberTypeId	
					and tmt.id = renewals.memberNumber	
				inner join membercentral.dbo.sub_rateFrequencies rf on
					rf.rateID = r.rateID
					and rf.status = 'A'
				inner join membercentral.dbo.sub_frequencies f on
					f.frequencyID = rf.frequencyID
					and f.status = 'A'
				inner join membercentral.dbo.sub_rateFrequenciesMerchantProfiles as rfmp on 
					rfmp.rfid = rf.rfID 
					and rfmp.status = 'A' 
				inner join membercentral.dbo.mp_profiles as mp on 
					mp.profileID = rfmp.profileID 
					and mp.status = 'A' 
				union
				select 
					distinct renewals.memberNumber
					,@componentSubscriptionType as SubscriptionType
					, subs.SubscriptionName
					,r.RateName
					, rf.rateAmt as LastPrice
					, f.frequencyShortName as Frequency
					, 'NO' as StoreModifiedRate
					, renewals.subStartDate as StartDate
					, renewals.subEndDate as EndDate
					, cast (cast(YEAR(renewals.subEndDate) + 1 as nvarchar)+'0331' as datetime) as graceEndDate
					, Status = case 
						when renewals.subStartDate > @currentTimeStamp then 'P'
						else 'A'
					end
					, @membershipSubscriptionType as ParentSubscriptionType
					, @membershipSubscriptionName  as ParentSubscriptionName
					, renewals.treecode as TreeCode
				from (
					select memberID, memberNumber,subStartDate,subEndDate, rootSubscriptionID, componentSubscriptionID, treecode from #tagd_sub_renewals
					union
					select memberID, memberNumber,subStartDate,subEndDate, rootSubscriptionID, componentSubscriptionID, treecode from #tagd_sub_newmembers 
				) as renewals
				inner join membercentral.dbo.sub_subscriptions as subs
					on renewals.componentSubscriptionID = subs.subscriptionID
					and subs.status = 'A' 
				inner join membercentral.dbo.sub_rateSchedules as rs 
					on subs.scheduleID = rs.scheduleID 
					and rs.status = 'A'
				inner join membercentral.dbo.sub_rates r
					on r.scheduleID = rs.scheduleID
					and r.status = 'A'
					and r.isRenewalRate = 0 
					and r.rateName = 'Annual Term'
				inner join membercentral.dbo.sub_rateFrequencies rf on
					rf.rateID = r.rateID
					and rf.status = 'A'
				inner join membercentral.dbo.sub_frequencies f on
					f.frequencyID = rf.frequencyID
					and f.status = 'A'
				inner join membercentral.dbo.sub_rateFrequenciesMerchantProfiles as rfmp on 
					rfmp.rfid = rf.rfID 
					and rfmp.status = 'A' 
				inner join membercentral.dbo.mp_profiles as mp on 
					mp.profileID = rfmp.profileID 
					and mp.status = 'A' 
			) tmp
			order by tmp.MemberNumber, tmp.ParentSubscriptionType

			DECLARE @qry varchar(400)
			SELECT @qry = 'ALTER TABLE ##tagd_import_subscriptions ADD rowID int NULL, rowID2 int identity(1,1);'
			EXEC(@qry)
			SELECT @qry = 'update ##tagd_import_subscriptions set rowID = rowID2;'
			EXEC(@qry)
			SELECT @qry = 'ALTER TABLE ##tagd_import_subscriptions DROP COLUMN rowID2;'
			EXEC(@qry)
			SELECT @qry = 'ALTER TABLE ##tagd_import_subscriptions ALTER COLUMN rowID int NOT NULL;'
			EXEC(@qry)

			-- Change components 
			select @autoID = min(autoID) from #tagd_sub_switchedComponent
			while @autoID is not null
			begin
				select 
					@thismemberid=NULL, 
					@thissubscriptionID=NULL, 
					@thisparentSubscriberID=NULL, 
					@thisRFID=NULL, 
					@thisGLAccountID=NULL, 
					@thisstatus=NULL, 
					@thissubStartDate=NULL, 
					@thissubEndDate=NULL, 
					@thisgraceEndDate = NULL, 
					@thispcfree=NULL, 
					@thisactivationOptionCode=NULL, 
					@thisbypassQueue=NULL, 
					@thisexistingComponentSubscriberID = NULL

				select 
					@thismemberid=memberID, 
					@thissubscriptionID=componentSubscriptionID, 
					@thisparentSubscriberID=existingRootSubscriberID, 
					@thisRFID=rfID, 
					@thisGLAccountID=GLAccountID, 
					@thisstatus=status, 
					@thissubStartDate=subStartDate, 
					@thissubEndDate=subEndDate, 
					@thisgraceEndDate = graceEndDate , 
					@thispcfree=pcfree, 
					@thisactivationOptionCode=activationOptionCode, 
					@thisbypassQueue=0, 
					@thisexistingComponentSubscriberID = existingComponentSubscriberID
				from #tagd_sub_switchedComponent
				where autoID = @autoID


				exec membercentral.dbo.sub_expireSubscriber
					@subscriberID=@thisexistingComponentSubscriberID, 
					@memberID=@thismemberid, 
					@siteID=@siteID, 
					@enteredByMemberID=@recordedByMemberID, 
					@statsSessionID=0, 
					@AROption='C', 
					@fReturnQuery=0

				exec membercentral.dbo.sub_addSubscriber
					@orgID=@orgID, 
					@memberid=@thismemberid, 
					@subscriptionID=@thissubscriptionID, 
					@parentSubscriberID=@thisparentSubscriberID, 
					@RFID=@thisRFID, 
					@GLAccountID=@thisGLAccountID, 
					@status=@thisstatus, 
					@subStartDate=@thissubStartDate, 
					@subEndDate=@thissubEndDate, 
					@graceEndDate=@thisgraceEndDate, 
					@pcfree=@thispcfree, 
					@activationOptionCode=@thisactivationOptionCode,
					@recordedByMemberID=@recordedByMemberID, 
					@bypassQueue=@thisbypassQueue, 
					@subscriberID=@trashID OUTPUT

				select @autoID = min(autoID) from #tagd_sub_switchedComponent where autoID > @autoID
			end

			-- Add individual components 
			set @autoID = null
			select @autoID = min(autoID) from #tagd_sub_newComponent
			while @autoID is not null
			begin

				select 
					@thismemberid=NULL, 
					@thissubscriptionID=NULL, 
					@thisparentSubscriberID=NULL, 
					@thisRFID=NULL, 
					@thisGLAccountID=NULL, 
					@thisstatus=NULL, 
					@thissubStartDate=NULL, 
					@thissubEndDate=NULL, 
					@thisgraceEndDate = NULL, 
					@thispcfree=NULL, 
					@thisactivationOptionCode=NULL, 
					@thisbypassQueue=NULL, 
					@thisexistingComponentSubscriberID = NULL

				select 
					@thismemberid=memberID, 
					@thissubscriptionID=componentSubscriptionID, 
					@thisparentSubscriberID=existingRootSubscriberID, 
					@thisRFID=rfID, 
					@thisGLAccountID=GLAccountID, 
					@thisstatus=status, 
					@thissubStartDate=subStartDate, 
					@thissubEndDate=subEndDate, 
					@thisgraceEndDate = graceEndDate , 
					@thispcfree=pcfree, 
					@thisactivationOptionCode=activationOptionCode, 
					@thisbypassQueue=0, 
					@thisexistingComponentSubscriberID = existingComponentSubscriberID
				from #tagd_sub_newComponent
				where autoID = @autoID

				exec membercentral.dbo.sub_addSubscriber
					@orgID=@orgID, 
					@memberid=@thismemberid, 
					@subscriptionID=@thissubscriptionID, 
					@parentSubscriberID=@thisparentSubscriberID, 
					@RFID=@thisRFID, 
					@GLAccountID=@thisGLAccountID, 
					@status=@thisstatus, 
					@subStartDate=@thissubStartDate, 
					@subEndDate=@thissubEndDate, 
					@graceEndDate=@thisgraceEndDate, 
					@pcfree=@thispcfree, 
					@activationOptionCode=@thisactivationOptionCode,
					@recordedByMemberID=@recordedByMemberID, 
					@bypassQueue=@thisbypassQueue, 
					@subscriberID=@trashID OUTPUT

				select @autoID = min(autoID) from #tagd_sub_newComponent where autoID > @autoID
			end

			set @importResult = null
			EXEC membercentral.dbo.sub_importSubscriptions_toTemp @siteID=@siteID, @tmptbl='##tagd_import_subscriptions', @pathToExport=@pathToExport, @importResult=@importResult OUTPUT
		
			-- if no errors, run subscription import
			IF @importResult.value('count(/import/errors/error)','int') = 0
			BEGIN
				SELECT @flatfile = @importResult.value('(/import/@flatfile)[1]','varchar(160)')
				set @importResult = null
				EXEC membercentral.dbo.sub_importSubscriptions_toPerm @siteID=@siteID, @recordedByMemberID=@recordedByMemberID, @flatfile=@flatfile, @importResult=@importResult OUTPUT
			END
		END
	END
END

-- cleanup
on_cleanup:
	IF OBJECT_ID('tempdb..##tagd_nationals_alpha') IS NOT NULL
		DROP TABLE ##tagd_nationals_alpha
	IF OBJECT_ID('tempdb..##tagd_nationals_new') IS NOT NULL
		DROP TABLE ##tagd_nationals_new
	IF OBJECT_ID('tempdb..##tagd_nationals_dues') IS NOT NULL
		DROP TABLE ##tagd_nationals_dues
	IF OBJECT_ID('tempdb..##tagd_nationals_Members') IS NOT NULL
		DROP TABLE ##tagd_nationals_Members
	IF OBJECT_ID('tempdb..##tagd_nationals_acct') IS NOT NULL
		DROP TABLE ##tagd_nationals_acct
	IF OBJECT_ID('tempdb..#tagd_memberType') IS NOT NULL
		DROP TABLE #tagd_memberType
	IF OBJECT_ID('tempdb..#temp_tagd_nationals_alpha') IS NOT NULL
		DROP TABLE #temp_tagd_nationals_alpha
	IF OBJECT_ID('tempdb..##tagd_import_subscriptions') IS NOT NULL
		DROP TABLE ##tagd_import_subscriptions
	IF OBJECT_ID('tempdb..#tempTreeCodeTbl') IS NOT NULL
		DROP TABLE #tempTreeCodeTbl
	IF OBJECT_ID('tempdb..#tagd_sub_renewals') IS NOT NULL
		DROP TABLE #tagd_sub_renewals
	IF OBJECT_ID('tempdb..#tagd_sub_newmembers') IS NOT NULL
		DROP TABLE #tagd_sub_newmembers
	IF OBJECT_ID('tempdb..#tagd_sub_switchedComponent') IS NOT NULL
		DROP TABLE #tagd_sub_switchedComponent
	IF OBJECT_ID('tempdb..#tagd_sub_newComponent') IS NOT NULL
		DROP TABLE #tagd_sub_newComponent
GO


use platformQueue
GO

ALTER PROC [dbo].[sb_ProcessMemberGroupsQueueActivated]
AS

SET DEADLOCK_PRIORITY -10

DECLARE @dlgID uniqueidentifier
DECLARE @mt nvarchar(256)
DECLARE @msg xml
DECLARE @itemGroupUID uniqueidentifier;
DECLARE @starttime datetime

DECLARE @smtpserver varchar(20), @tier varchar(20), @errorSubject varchar(100), @errmsg nvarchar(2048), @crlf varchar(10)
SET @crlf = char(13) + char(10)
SELECT @smtpserver = smtpserver, @tier = tier from membercentral.dbo.fn_getServerSettings()
SET @errorSubject = @tier + ': Error Processing Member Groups'

WHILE 1 = 1
BEGIN

	WAITFOR (
		RECEIVE TOP(1) 
			@dlgID = conversation_handle,
			@mt = message_type_name,
			@msg = cast(message_body as xml)
		FROM dbo.ProcessMemberGroupsQueue
		), TIMEOUT 1000;

	IF @@ROWCOUNT = 0
		BREAK;

	IF @mt = N'PlatformQueue/ProcessMemberGroupsRequestMessage'
	BEGIN
		BEGIN TRY
			SELECT @itemGroupUID = @msg.value('(/itemGroupUID)[1]','uniqueidentifier')

			-- Log
			set @starttime = getdate()
			INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage, QueueMessage)
			VALUES (@dlgID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'Start Process Message', @msg);

			IF @itemGroupUID is not null
				EXEC dbo.queue_processMemberGroups_process @itemGroupUID=@itemGroupUID, @logTreeID=@dlgID, @optimizeQueue=1

			END CONVERSATION @dlgID;
		END TRY
		BEGIN CATCH
			-- Log
			INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorSeverity, ErrorMessage, ErrorLine, ErrorProc)
			VALUES (@dlgID, @itemGroupUID, OBJECT_NAME(@@PROCID), ERROR_SEVERITY(), ERROR_MESSAGE(), ERROR_LINE(), ERROR_PROCEDURE());

			-- if deadlock, put itemGroupUID back into the queue as ready to run again
			IF @itemGroupUID is not null and 
				(ERROR_MESSAGE() like '%Errno 1205:%'
				 OR ERROR_MESSAGE() like '%Violation of UNIQUE KEY constraint ''IX_cache_members_groups''.%'
				 OR ERROR_MESSAGE() like '%Violation of PRIMARY KEY constraint ''PK_cache_members_conditions''.%') 
				BEGIN TRY				
					EXEC dbo.queue_processMemberGroups_requeue @itemGroupUID=@itemGroupUID, @logTreeID=@dlgID
				END TRY
				BEGIN CATCH
					BEGIN TRY					
						SET @errmsg = @errorSubject + @crlf + @crlf + 
										ERROR_MESSAGE() + @crlf + @crlf + 
										'LogTreeID=' + cast(@dlgID as varchar(60)) + @crlf + @crlf + 
										'Unable to requeue. After addressing error, run: ' + @crlf + 
										'EXEC platformQueue.dbo.queue_processMemberGroups_requeue @itemGroupUID=''' + cast(@itemGroupUID as varchar(60)) + ''', @logTreeID=''' + cast(@dlgID as varchar(60)) + ''';'

						EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
							@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
							@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
					END TRY
					BEGIN CATCH
						-- do nothing
					END CATCH
				END CATCH
			ELSE 
			BEGIN
				BEGIN TRY
					SET @errmsg = @errorSubject + @crlf + @crlf + 
									ERROR_MESSAGE() + @crlf + @crlf + 
									'LogTreeID=' + cast(@dlgID as varchar(60)) + @crlf + @crlf + 
									'After addressing error, run: ' + @crlf + 
									'EXEC platformQueue.dbo.queue_processMemberGroups_requeue @itemGroupUID=''' + cast(@itemGroupUID as varchar(60)) + ''', @logTreeID=''' + cast(@dlgID as varchar(60)) + ''';'

					EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
						@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
						@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
				END TRY
				BEGIN CATCH
					-- do nothing
				END CATCH
			END

			END CONVERSATION @dlgID;
		END CATCH;

		-- log
		INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage, totalMS)
		VALUES (@dlgID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'End Process Message', Datediff(ms,@starttime,getdate()));
	END
	ELSE
		IF @mt = N'http://schemas.microsoft.com/SQL/ServiceBroker/EndDialog' OR @mt = N'http://schemas.microsoft.com/SQL/ServiceBroker/Error'
			END CONVERSATION @dlgID;
		ELSE
		BEGIN
			-- Log
			INSERT INTO dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage)
			VALUES (@dlgID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'Unexpected message type received - ' + @mt);

			END CONVERSATION @dlgID;
		END

END;
GO


