USE [seminarWeb]
GO
CREATE PROCEDURE [dbo].[swl_buySeminarCD]
@seminarID int,
@depomemberdataid int,
@billingcity varchar(100),
@billingstate varchar(5),
@linksource varchar(50),
@linkterms varchar(100),
@orgcode varchar(5),
@price money,
@salestax money,
@transactionID int OUTPUT

AS

SET NOCOUNT ON

BEGIN TRAN
	DECLARE @saleDesc varchar(600), @taxTableID int
	SELECT @taxTableID = trialsmith.dbo.fn_SalesTax_getTaxRule(@orgcode,@billingstate,'')
	SELECT top 1 @saleDesc = s.seminarname + ' | ' + p.orgcode + ' | ' + 
		case 
		when @billingState <> 'CA' then @billingState
		when @billingCity = 'San Francisco' then 'SF'
		when @billingCity = 'San Diego' then 'SA'
		when @billingCity = 'Los Angeles' then 'LO'
		else 'CA' end
		from dbo.tblSeminars as s
		inner join dbo.tblParticipants as p on p.participantID = s.participantID 
		WHERE s.seminarID = @seminarID

	-- insert sale
	INSERT INTO trialsmith.dbo.depoTransactions (depomemberdataid, [Description], AmountBilled, AccountCode, SourceState, 
		salestaxamount, taxTableID, orgcode, linksource, linkterms, purchasedItemID, purchasedItemTableName)
	SELECT TOP 1 @depomemberdataid, @saleDesc, @price, dt.acctcode, 'TS', @salestax, @taxTableID, @orgcode, @linksource, 
		@linkterms, @seminarID, 'SeminarWebLive'
	FROM trialsmith.dbo.DepoDocumentTypes dt
	WHERE dt.acctcode = '7004'
		IF @@ERROR <> 0 GOTO on_error

	-- get transaction id
	SELECT @transactionID = IDENT_CURRENT('trialsmith.dbo.depoTransactions')

COMMIT TRAN
RETURN 1

on_error:
	ROLLBACK TRAN
	RETURN 0
GO