CREATE TRIGGER [dbo].[members_DeleteTrig] 
ON [dbo].[members_] 
AFTER Delete AS 
BEGIN 
	SET NOCOUNT ON 
    DELETE FROM lyrMemberSubsets FROM subsets_ s, DELETED d WHERE lyrMemberSubsets.SubsetID = s.SubsetID_ AND lyrMemberSubsets.MemberID = d.MemberID_ AND lyrMemberSubsets.UserNameLC = d.UserNameLC_ AND 
       s.List_ = d.List_ AND lyrMemberSubsets.Domain = d.Domain_ AND lyrMemberSubsets.List = d.List_; 
	SET NOCOUNT OFF 
END
ALTER TABLE [dbo].[members_] ENABLE TRIGGER [members_DeleteTrig]
GO
