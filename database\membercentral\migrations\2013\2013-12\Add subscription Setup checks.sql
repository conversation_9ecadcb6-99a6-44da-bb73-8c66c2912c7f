CREATE VIEW dbo.[vw_Subscription Rate Frequencies with no Pay Profile]
AS
select s.sitecode, rs.schedulename, r.ratename, f.frequencyname, dbo.pipelist(subs.subscriptionname) as affectedSubscriptions
from dbo.sites as s
inner join dbo.sub_types as t on s.siteID = t.siteID and t.status = 'A'
inner join dbo.sub_subscriptions as subs on subs.typeID = t.typeID and subs.status = 'A'
inner join dbo.sub_rateSchedules as rs on rs.scheduleID = subs.scheduleID and rs.status = 'A'
inner join dbo.sub_rates as r on r.scheduleID = rs.scheduleID and r.status = 'A'
inner join dbo.sub_rateFrequencies as rf on rf.rateID = r.rateID and rf.status = 'A'
inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
left outer join dbo.sub_rateFrequenciesMerchantProfiles as rfmp on rfmp.rfid = rf.rfid
where rfmp.rfid is null
group by s.sitecode, rs.schedulename, r.ratename, f.frequencyname
GO

CREATE VIEW dbo.[vw_Subscription Grace EndDate Before Sub EndDate]
AS
select s.sitecode, rs.schedulename, r.ratename, r.termAFStartDate, r.termAFEndDate, r.graceEndDate, dbo.pipelist(subs.subscriptionname) as affectedSubscriptions
from dbo.sites as s
inner join dbo.sub_types as t on s.siteID = t.siteID and t.status = 'A'
inner join dbo.sub_subscriptions as subs on subs.typeID = t.typeID and subs.status = 'A'
inner join dbo.sub_rateSchedules as rs on rs.scheduleID = subs.scheduleID and rs.status = 'A'
inner join dbo.sub_rates as r on r.scheduleID = rs.scheduleID and r.status = 'A' and r.graceEndDate < r.termAFEndDate
group by s.sitecode, rs.schedulename, r.ratename, r.termAFStartDate, r.termAFEndDate, r.graceEndDate
GO

CREATE VIEW dbo.[vw_Subscription Rates Missing Advance Formulas]
AS
select s.sitecode, rs.schedulename, r.ratename, r.termAFStartDate, r.termAFEndDate, r.graceEndDate
from dbo.sites as s
inner join dbo.sub_types as t on s.siteID = t.siteID and t.status = 'A'
inner join dbo.sub_subscriptions as subs on subs.typeID = t.typeID and subs.status = 'A'
inner join dbo.sub_rateSchedules as rs on rs.scheduleID = subs.scheduleID and rs.status = 'A'
inner join dbo.sub_rates as r on r.scheduleID = rs.scheduleID and r.status = 'A' and r.ratename not like ('%Import%')
and (
	r.termStartDateAFID is null or
	r.termEndDateAFID is null or
	r.rateStartDateAFID is null or
	r.rateEndDateAFID is null or
	(r.graceAFID is null and r.graceEndDate is not null)
)
group by s.sitecode, rs.schedulename, r.ratename, r.termAFStartDate, r.termAFEndDate, r.graceEndDate, termStartDateAFID, termEndDateAFID, rateStartDateAFID, rateEndDateAFID, graceAFID
GO



CREATE PROC dbo.sub_setupIssuesReport
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20), @errorContent varchar(max)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)
SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '***********'
IF @@SERVERNAME = 'DEV04\PLATFORM2008' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'DEV03\PLATFORM2008' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'STAGING01\PLATFORM2008' BEGIN
	SET @tier = 'STAGING'
	SET @smtpserver = 'mail.trialsmith.com'
END

/* ************************************************* */
/* Subscription Rate Frequencies with no Pay Profile */
/* ************************************************* */
IF EXISTS (select * from dbo.[vw_Subscription Rate Frequencies with no Pay Profile]) BEGIN
	BEGIN TRY
		SET @errorContent = ''
		SET @errorContent = '' + 
			replace(Stuff((
				SELECT '||' + sitecode + char(9) + 'Schedule: ' + scheduleName + '|' + char(9) + 'Rate: ' + ratename + '|' + char(9) + 'Freq: ' + frequencyName + '|' + char(9) + 'Subs: ' + replace(affectedSubscriptions,'|','; ') AS [text()]
				from dbo.[vw_Subscription Rate Frequencies with no Pay Profile]
				order by sitecode, scheduleName, rateName, frequencyName
				FOR XML PATH ('')
			),1,1,''),'|',@crlf)
		SET @errorSubject = @tier + ' - Non-Developer Needed - Subscription Rate Frequencies with no Pay Profile'
		SET @errorContent = @tier + ' - There are subscription rate frequencies with no payment profile defined. These need to be defined for successful processing of subscriptions.' + @crlf + @errorContent
		EXEC membercentral.dbo.sys_sendEmail 
			@from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errorContent, 
			@priority='high', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END TRY
	BEGIN CATCH
		SET @errorSubject = @tier + ': Error Running Subscription Rate Frequencies with no Pay Profile'
		SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
		SELECT @errorContent = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
		EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END CATCH
END

/* ********************************************* */
/* Subscription Grace EndDate Before Sub EndDate */
/* ********************************************* */
IF EXISTS (select * from dbo.[vw_Subscription Grace EndDate Before Sub EndDate]) BEGIN
	BEGIN TRY
		SET @errorContent = ''
		SET @errorContent = '' + 
			replace(Stuff((
				SELECT '||' + sitecode + char(9) + 'Schedule: ' + scheduleName + '|' + char(9) + 'Rate: ' + ratename + '|' + char(9) + 'Subs: ' + replace(affectedSubscriptions,'|','; ') AS [text()]
				from dbo.[vw_Subscription Grace EndDate Before Sub EndDate]
				order by sitecode, scheduleName, rateName
				FOR XML PATH ('')
			),1,1,''),'|',@crlf)
		SET @errorSubject = @tier + ' - Non-Developer Needed - Subscription Grace End Dates are Before Subscription End Dates'
		SET @errorContent = @tier + ' - There are subscriptions with grace end dates before subscription end dates. These need to be corrected for successful processing of subscriptions. It may be an indicator of missing advance formulas.' + @crlf + @errorContent
		EXEC membercentral.dbo.sys_sendEmail 
			@from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errorContent, 
			@priority='high', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END TRY
	BEGIN CATCH
		SET @errorSubject = @tier + ': Error Running Subscription Grace EndDate Before Sub EndDate'
		SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
		SELECT @errorContent = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
		EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END CATCH
END

/* ******************************************* */
/* Subscription Rates Missing Advance Formulas */
/* ******************************************* */
IF EXISTS (select * from dbo.[vw_Subscription Rates Missing Advance Formulas]) BEGIN
	BEGIN TRY
		SET @errorContent = ''
		SET @errorContent = '' + 
			replace(Stuff((
				SELECT '||' + sitecode + char(9) + 'Schedule: ' + scheduleName + '|' + char(9) + 'Rate: ' + ratename
				from dbo.[vw_Subscription Rates Missing Advance Formulas]
				order by sitecode, scheduleName, rateName
				FOR XML PATH ('')
			),1,1,''),'|',@crlf)
		SET @errorSubject = @tier + ' - Non-Developer Needed - Subscription Rates Missing Advance Formulas'
		SET @errorContent = @tier + ' - There are subscriptions with rates that are missing at least one advance formula. These need to be corrected for successful processing of subscriptions.' + @crlf + @errorContent
		EXEC membercentral.dbo.sys_sendEmail 
			@from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errorContent, 
			@priority='high', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END TRY
	BEGIN CATCH
		SET @errorSubject = @tier + ': Error Running Subscription Rates Missing Advance Formulas'
		SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
		SELECT @errorContent = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
		EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END CATCH
END


GO


EXEC sub_setupIssuesReport