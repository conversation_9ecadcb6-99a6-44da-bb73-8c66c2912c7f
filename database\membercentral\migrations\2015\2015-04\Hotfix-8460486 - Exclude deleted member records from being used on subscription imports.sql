USE [memberCentral]
GO

ALTER PROC [dbo].[sub_importSubscriptions_toTemp]
@siteid int, 
@tmptbl varchar(30),
@pathToExport varchar(100),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

IF OBJECT_ID('tempdb..#tblSubErrors') IS NOT NULL 
	DROP TABLE #tblSubErrors
CREATE TABLE #tblSubErrors (rowid int IDENTITY(1,1), msg varchar(300), fatal bit)

declare @var_tmpCols varchar(20), @orgID int, @orgCode varchar(10), @bcpprefix varchar(50), @flatfile varchar(160),
	@dynSQL nvarchar(max), @good bit, @exportcmd varchar(400), @counter int

select @orgID=o.orgID, @orgcode=o.orgcode 
	from dbo.organizations as o
	inner join dbo.sites as s on s.orgID = o.orgID
	where s.siteID = @siteID
set @var_tmpCols = '##tmpSubCols' + @orgcode
set @bcpprefix = @orgcode + '_sub_flat_' + convert(varchar(8),getdate(),112) + replace(convert(varchar(8),getdate(),108),':','')
set @flatfile = @pathToExport + @bcpprefix


-- ******************************** 
-- ensure all columns exist (fatal)
-- ******************************** 
BEGIN TRY
	IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
		EXEC('DROP TABLE ' + @var_tmpCols)
	IF OBJECT_ID('tempdb..#tblSubOrgCols') IS NOT NULL 
		DROP TABLE #tblSubOrgCols
	IF OBJECT_ID('tempdb..#tblSubImportCols') IS NOT NULL 
		DROP TABLE #tblSubImportCols

	-- this will get the columns that should be there
	CREATE TABLE #tblSubOrgCols (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
		COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
		SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
		COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
		CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)

		select @dynSQL = '	
			select cast(null as varchar(50)) as MemberNumber, cast(null as varchar(100)) as SubscriptionType,
				cast(null as varchar(300)) as SubscriptionName, cast(null as varchar(200)) as RateName, 
				cast(null as money) as LastPrice, cast(null as varchar(10)) as Frequency, 
				cast(null as varchar(3)) as StoreModifiedRate, cast(null as datetime) as StartDate, 
				cast(null as datetime) as EndDate, cast(null as datetime) as GraceEndDate, 
				cast(null as varchar(1)) as Status, cast(null as varchar(100)) as ParentSubscriptionType, 
				cast(null as varchar(300)) as ParentSubscriptionName, cast(null as varchar(50)) as TreeCode,
				cast(null as int) as rowID
			into ' + @var_tmpCols + '
			from dbo.sub_subscriptions
			where 1=0 '
		EXEC(@dynSQL)

		-- get cols
		INSERT INTO #tblSubOrgCols
		EXEC tempdb.dbo.SP_COLUMNS @var_tmpCols
		
		-- cleanup table no longer needed
		IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
			EXEC('DROP TABLE ' + @var_tmpCols)

	-- this will get the columns that are actually in the import
	CREATE TABLE #tblSubImportCols (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
		COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
		SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
		COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
		CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)

		-- get cols
		INSERT INTO #tblSubImportCols
		EXEC tempdb.dbo.SP_COLUMNS @tmptbl

	INSERT INTO #tblSubErrors (msg, fatal)
	select 'The column ' + org.column_name + ' is missing from your data.', 1
	from #tblSubOrgCols as org
	left outer join #tblSubImportCols as imp on imp.column_name = org.column_name
	where imp.table_name is null

	IF @@ROWCOUNT > 0
		GOTO on_done
END TRY
BEGIN CATCH
	INSERT INTO #tblSubErrors (msg, fatal)
	VALUES ('Unable to process subscription file.',1)

	GOTO on_done
END CATCH


-- **********************
-- columns checks (fatal)
-- **********************
BEGIN TRY
	-- put in unique treecode if none defined
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [TreeCode] varchar(50) null;
			update ' + @tmptbl + ' set TreeCode = cast(NEWID() as varchar(50)) where nullIf(TreeCode,'''') is null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO #tblSubErrors (msg, fatal)
		VALUES ('The column TreeCode has empty entries and could not be populated automatically.', 1)

	select @dynSQL = 'ALTER TABLE ' + @tmptbl + ' ADD memberID int null, subTypeUID uniqueidentifier null, subUID uniqueidentifier null, 
		subID int null, parentSubTypeUID uniqueidentifier null, parentSubUID uniqueidentifier null, parentSubID int null,  
		rateUID uniqueidentifier null, RFID int null, rootSubID int null, subscriberID int null'
	EXEC(@dynSQL)

	-- ensure startDate is datetime (allow nulls for this check)
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [StartDate] datetime null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO #tblSubErrors (msg, fatal)
		VALUES ('The column StartDate contains invalid dates.', 1)

	-- check for null startDate
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [StartDate] datetime not null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		set @counter = 0
		set @dynSQL = 'select @counter = count(rowID) from ' + @tmptbl + ' WHERE StartDate IS NULL'
		exec sp_executesql @dynSQL, N'@counter int output', @counter output
		IF @counter > 50 BEGIN
			INSERT INTO #tblSubErrors (msg, fatal)
			VALUES ('There are ' + cast(@counter as varchar(10)) + ' rows missing the required StartDate.', 1)
		END ELSE BEGIN
			select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' is missing the required StartDate.'' as msg, 1 as fatal 
				FROM ' + @tmptbl + ' 
				WHERE StartDate IS NULL
				ORDER BY rowID'
			INSERT INTO #tblSubErrors (msg, fatal)
			EXEC(@dynSQL)
		END
	END

	-- ensure endDate is datetime (allow nulls for this check)
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [EndDate] datetime null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO #tblSubErrors (msg, fatal)
		VALUES ('The column EndDate contains invalid dates.', 1)

	-- check for null endDate
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [EndDate] datetime not null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		set @counter = 0
		set @dynSQL = 'select @counter = count(rowID) from ' + @tmptbl + ' WHERE EndDate IS NULL'
		exec sp_executesql @dynSQL, N'@counter int output', @counter output
		IF @counter > 50 BEGIN
			INSERT INTO #tblSubErrors (msg, fatal)
			VALUES ('There are ' + cast(@counter as varchar(10)) + ' rows missing the required EndDate.', 1)
		END ELSE BEGIN
			select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' is missing the required EndDate.'' as msg, 1 as fatal 
				FROM ' + @tmptbl + ' 
				WHERE EndDate IS NULL
				ORDER BY rowID'
			INSERT INTO #tblSubErrors (msg, fatal)
			EXEC(@dynSQL)
		END
	END

	-- ensure graceEndDate is datetime (allow nulls for this check)
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [GraceEndDate] datetime null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO #tblSubErrors (msg, fatal)
		VALUES ('The column GraceEndDate contains invalid dates.', 1)

	-- clean amounts
	select @dynSQL = 'update ' + @tmptbl + ' set LastPrice = membercentral.dbo.fn_regexReplace(LastPrice,''[^0-9\.\-\(\)]'','''') where LastPrice is not null;'
	EXEC(@dynSQL)

	-- ensure LastPrice is money (allow nulls for this check)
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [LastPrice] money null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO #tblSubErrors (msg, fatal)
		VALUES ('The column LastPrice contains invalid amounts.', 1)

	-- check for null or negative LastPrice
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [LastPrice] money not null;
			ALTER TABLE ' + @tmptbl + ' ADD CONSTRAINT lastPriceamtCheck CHECK (LastPrice >= 0);
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(memberNumber,'''') + '') does not have a positive LastPrice amount.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE LastPrice IS NULL OR LastPrice < 0
			ORDER BY rowID'
		INSERT INTO #tblSubErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	-- match on membernumber
	select @dynSQL = 'update tmp
		set tmp.memberID = m.memberid
		from ' + @tmptbl + ' as tmp
		inner join membercentral.dbo.ams_members as m on m.memberNumber = tmp.memberNumber
			and m.orgID = ' + cast(@orgID as varchar(5)) + '
			and m.memberID = m.activeMemberID 
			and m.status <> ''D'' '
	EXEC(@dynSQL)

	-- check for missing member matches
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN memberID int not null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(membernumber,'''') + '') does not match an existing MemberNumber.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE memberID IS NULL
			ORDER BY rowID'
		INSERT INTO #tblSubErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	-- match on subscription type
	select @dynSQL = '
		update tmp
		set tmp.subtypeUID = t.uid
		from ' + @tmptbl + ' as tmp
		inner join membercentral.dbo.sub_types as t on t.typeName = tmp.SubscriptionType 
			and t.status = ''A'' 
			and t.siteID = ' + cast(@siteID as varchar(5))
	EXEC(@dynSQL)

	-- check for missing sub type matches
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN subtypeUID uniqueidentifier not null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT DISTINCT ''SubscriptionType ['' + isnull(SubscriptionType,'''') + ''] does not match an existing Subscription Type.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE subtypeUID IS NULL
			ORDER BY 1'
		INSERT INTO #tblSubErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	-- match on parent subscription type
	select @dynSQL = '
		update tmp
		set tmp.parentSubtypeUID = t.uid
		from ' + @tmptbl + ' as tmp
		inner join membercentral.dbo.sub_types as t on t.typeName = tmp.ParentSubscriptionType 
			and t.status = ''A'' 
			and t.siteID = ' + cast(@siteID as varchar(5)) + ' 
		where tmp.ParentSubscriptionType is not null'
	EXEC(@dynSQL)

	-- check for missing parent sub type matches
	select @dynSQL = 'SELECT DISTINCT ''ParentSubscriptionType ['' + isnull(ParentSubscriptionType,'''') + ''] does not match an existing Subscription Type.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE parentSubtypeUID is null and ParentSubscriptionType is not null
		ORDER BY 1'
	INSERT INTO #tblSubErrors (msg, fatal)
	EXEC(@dynSQL)

	-- match on subscription 
	select @dynSQL = '
		update tmp
		set tmp.subUID = subs.uid,
			tmp.subID = subs.subscriptionID
		from ' + @tmptbl + ' as tmp
		inner join membercentral.dbo.sub_subscriptions as subs on subs.subscriptionName = tmp.SubscriptionName 
			and subs.status = ''A''
		inner join membercentral.dbo.sub_types as t on subs.typeID = t.typeID 
			and t.uid = tmp.subtypeUID'
	EXEC(@dynSQL)

	-- check for missing sub matches
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN subUID uniqueidentifier not null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT DISTINCT ''SubscriptionName ['' + isnull(SubscriptionName,'''') + ''] does not match an existing Subscription.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE subUID IS NULL
			ORDER BY 1'
		INSERT INTO #tblSubErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	-- match on parent subscription 
	select @dynSQL = '
		update tmp
		set tmp.parentSubUID = subs.uid,
			tmp.parentSubID = subs.subscriptionID
		from ' + @tmptbl + ' as tmp
		inner join membercentral.dbo.sub_subscriptions as subs on subs.subscriptionName = tmp.ParentSubscriptionName 
			and subs.status = ''A''
		inner join membercentral.dbo.sub_types as t on subs.typeID = t.typeID 
			and t.uid = tmp.ParentSubtypeUID
		where tmp.ParentSubscriptionName is not null'
	EXEC(@dynSQL)

	-- check for missing parent sub matches
	select @dynSQL = 'SELECT DISTINCT ''ParentSubscriptionName ['' + isnull(ParentSubscriptionName,'''') + ''] does not match an existing Subscription.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE parentSubUID is null and ParentSubscriptionName is not null
		ORDER BY 1'
	INSERT INTO #tblSubErrors (msg, fatal)
	EXEC(@dynSQL)

	-- check for bad data - parent subs and subs the same
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(membernumber,'''') + '') has a parent subscription of itself.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE parentSubUID is not null and subUID is not null and parentSubUID = subUID
		ORDER BY rowID'
	INSERT INTO #tblSubErrors (msg, fatal)
	EXEC(@dynSQL)
	
	-- check for frequencies
	select @dynSQL = 'SELECT DISTINCT ''Frequency ['' + isnull(tmp.Frequency,'''') + ''] does not match an existing Frequency.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' as tmp
		left outer join membercentral.dbo.sub_frequencies as f on f.frequencyShortName = tmp.frequency 
			and f.siteID = ' + cast(@siteID as varchar(5)) + ' 
			and f.status = ''A''
		WHERE f.frequencyID is null
		ORDER BY 1'
	INSERT INTO #tblSubErrors (msg, fatal)
	EXEC(@dynSQL)

	-- check for status
	select @dynSQL = 'SELECT DISTINCT ''Status ['' + isnull(Status,'''') + ''] does not match an existing Status.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' as tmp
		left outer join membercentral.dbo.sub_statuses as s on s.statusCode = tmp.Status 
		where s.statusID is null
		ORDER BY 1'
	INSERT INTO #tblSubErrors (msg, fatal)
	EXEC(@dynSQL)

	-- match on rate 
	select @dynSQL = '
		update tmp
		set tmp.rateUID = r.uid,
			tmp.RFID = rf.rfid
		from ' + @tmptbl + ' as tmp
		inner join membercentral.dbo.sub_subscriptions as subs on subs.uid = tmp.subUID
		inner join membercentral.dbo.sub_rates as r on r.scheduleID = subs.scheduleID and r.rateName = tmp.rateName
		inner join membercentral.dbo.sub_rateFrequencies as rf on rf.rateID = r.rateID
		inner join membercentral.dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID and f.frequencyShortName = tmp.frequency'
	EXEC(@dynSQL)

	-- check for missing rate matches
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN rateUID uniqueidentifier not null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT DISTINCT ''RateName ['' + isnull(RateName,'''') + ''] does not match an existing rate with frequency ['' + frequency + ''] and subscription ['' + isnull(SubscriptionName,'''') + ''].'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE rateUID IS NULL
			ORDER BY 1'
		INSERT INTO #tblSubErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	-- check dates
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(membernumber,'''') + '') has a StartDate after the EndDate.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE startDate > EndDate
		ORDER BY rowID'
	INSERT INTO #tblSubErrors (msg, fatal)
	EXEC(@dynSQL)

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(membernumber,'''') + '') has a GraceEndDate before the EndDate.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE GraceEndDate is not null and EndDate > GraceEndDate
		ORDER BY rowID'
	INSERT INTO #tblSubErrors (msg, fatal)
	EXEC(@dynSQL)

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(membernumber,'''') + '') is an expired subscription incorrectly ending in the future.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE endDate > getdate() and [status] = ''E''
		ORDER BY rowID'
	INSERT INTO #tblSubErrors (msg, fatal)
	EXEC(@dynSQL)

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(membernumber,'''') + '') is an active subscription incorrectly starting in the future.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE startDate > getdate() and [status] = ''A''
		ORDER BY rowID'
	INSERT INTO #tblSubErrors (msg, fatal)
	EXEC(@dynSQL)

	select @dynSQL = 'update ' + @tmptbl + ' set EndDate = cast(convert(varchar(10),EndDate,101) + '' 23:59:59.997'' AS datetime);'
	EXEC(@dynSQL)

	-- check child subs
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(tmp.rowID as varchar(10)) + '' ('' + isnull(tmp.membernumber,'''') + '') cannot locate matching parent subscription.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' as tmp 
		left outer join ' + @tmptbl + ' as tmp2 on tmp2.memberid = tmp.memberid 
			and tmp2.subID = tmp.parentSubID
			and tmp2.TreeCode = tmp.TreeCode
		where tmp.parentSubID is not null
		and tmp2.rowID is null
		ORDER BY tmp.rowID'
	INSERT INTO #tblSubErrors (msg, fatal)
	EXEC(@dynSQL)

	-- ensure treecode has only one root
	select @dynSQL = 'SELECT TOP 100 PERCENT ''MemberNumber '' + tmp.MemberNumber + '' has multiple root subscriptions in TreeCode '' + tmp.TreeCode as msg, 1 as fatal 
		FROM ' + @tmptbl + ' as tmp 
		where parentSubID is null
		group by MemberNumber, TreeCode
		having count(*) > 1
		order by 1'
	INSERT INTO #tblSubErrors (msg, fatal)
	EXEC(@dynSQL)

	-- get the rootSubID for each treecode
	select @dynSQL = '
		update a
		set a.rootSubID = b.subID
		from ' + @tmptbl + ' as a
		inner join (
			select distinct memberID, TreeCode, subID
			from ' + @tmptbl + '
			where parentSubID is null
		) as b on b.memberID = a.memberID and b.TreeCode = a.TreeCode'
	EXEC(@dynSQL)

	-- reorder import file based on subscription path
	select @dynSQL = '
		update a
		set a.rowID = b.newRowID
		from ' + @tmptbl + ' as a
		inner join (
			select orig.rowID, row_number() OVER (ORDER by orig.membernumber, tmp2.rootSubscriptionID, tmp2.subscriptionPath, orig.rowID) as newRowID
			from ' + @tmptbl + ' as orig
			inner join (
				select tmp.subID as rootSubscriptionID, sto.subscriptionID, sto.subscriptionPath
				from (
					select distinct subID
					from ' + @tmptbl + '
					where parentSubID is null
				) as tmp
				cross apply membercentral.dbo.fn_sub_getSubscriptionTreeOrder(tmp.subID) as sto
			) as tmp2 on tmp2.subscriptionID = orig.subID and tmp2.rootSubscriptionID = orig.rootSubID
		) as b on b.rowID = a.rowID'
	EXEC(@dynSQL)

	select @dynSQL = '
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ADD PRIMARY KEY (rowID);
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSubErrors (msg, fatal)
			VALUES (''Reordering based on subscription path has failed.'', 1)

			/*
			INSERT INTO #tblSubErrors (msg, fatal)
			select ''Duplicate RowIDs after reordering: RowID '' + cast(tmp.rowID as varchar(10)) + '' ('' + (select top 1 membernumber + '' - '' + subscriptionName from ' + @tmptbl + ' where rowID = tmp.rowID) + '')'', 1
			from ' + @tmptbl + ' as tmp
			group by tmp.rowID 
			having count(*) > 1
			order by 1
			*/

			INSERT INTO #tblSubErrors (msg, fatal)
			VALUES (error_message(),0)
		END CATCH'
	EXEC(@dynSQL)
END TRY
BEGIN CATCH
	INSERT INTO #tblSubErrors (msg, fatal)
	VALUES ('Unable to validate subscription file.',1)

	INSERT INTO #tblSubErrors (msg, fatal)
	VALUES (error_message(),0)

	GOTO on_done
END CATCH

IF (select count(*) from #tblSubErrors) = 0 BEGIN
	/* ***************** */
	/* get expanded data */
	/* ***************** */
	BEGIN TRY
		IF OBJECT_ID('tempdb..##tmpSubImportFull') IS NOT NULL 
			DROP TABLE ##tmpSubImportFull
		CREATE TABLE ##tmpSubImportFull (rowID int, MemberNumber varchar(50), SubscriptionType varchar(100),
			SubscriptionName varchar(300), RateName varchar(200), LastPrice money, Frequency varchar(10),
			StoreModifiedRate varchar(3), StartDate datetime, EndDate datetime, GraceEndDate datetime, 
			[Status] varchar(1), ParentSubscriptionType varchar(100), ParentSubscriptionName varchar(300), 
			TreeCode varchar(50), memberID int, subTypeUID uniqueidentifier, subUID uniqueidentifier, 
			subID int, parentSubTypeUID uniqueidentifier, parentSubUID uniqueidentifier, parentSubID int, 
			rateUID uniqueidentifier, RFID int, subscriberID int)

		select @dynSQL = ' 
			select rowID, MemberNumber, SubscriptionType, SubscriptionName, RateName, LastPrice, Frequency,
				StoreModifiedRate, StartDate, EndDate, GraceEndDate, [Status], ParentSubscriptionType, 
				ParentSubscriptionName, TreeCode, memberID, subTypeUID, subUID, subID, parentSubTypeUID, 
				parentSubUID, parentSubID, rateUID, RFID, subscriberID 
			from ' + @tmptbl + '
			order by rowid'
		insert into ##tmpSubImportFull
		EXEC(@dynSQL)
	END TRY
	BEGIN CATCH
		INSERT INTO #tblSubErrors (msg, fatal)
		VALUES ('Unable to prepare final subscription data for final import.',1)

		INSERT INTO #tblSubErrors (msg, fatal)
		VALUES (error_message(),0)

		set @flatfile = ''

		GOTO on_done
	END CATCH


	-- ******************************** 
	-- dump flat table and format file and creation script
	-- would love to use xml format files, but it appears column names with spaces cause it to fail
	-- ******************************** 
	BEGIN TRY
		select @exportcmd = 'bcp ##tmpSubImportFull format nul -f ' + @flatfile + '.txt -n -T -S' + CAST(serverproperty('servername') as varchar(40))
		EXEC master..xp_cmdshell @exportcmd, NO_OUTPUT
		select @exportcmd = 'bcp ##tmpSubImportFull out ' + @flatfile + '.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
		EXEC master..xp_cmdshell @exportcmd, NO_OUTPUT

		declare @coltypes table (datatype varchar(16))          
		insert into @coltypes values('bit')          
		insert into @coltypes values('binary')          
		insert into @coltypes values('bigint')          
		insert into @coltypes values('int')          
		insert into @coltypes values('float')          
		insert into @coltypes values('datetime')          
		insert into @coltypes values('text')          
		insert into @coltypes values('image')          
		insert into @coltypes values('money')          
		insert into @coltypes values('uniqueidentifier')          
		insert into @coltypes values('smalldatetime')          
		insert into @coltypes values('tinyint')          
		insert into @coltypes values('smallint')          
		insert into @coltypes values('sql_variant')          
		select @dynSQL = ''
		select @dynSQL = @dynSQL +           
			case when charindex('(',@dynSQL,1)<=0 then '(' else '' end + '[' + Column_Name + '] ' +Data_Type +
			case when Data_Type in (Select datatype from @coltypes) then '' else  '(' end+
			case when data_type in ('real','decimal','numeric')  then cast(isnull(numeric_precision,'') as varchar)+','+
			case when data_type in ('real','decimal','numeric') then cast(isnull(Numeric_Scale,'') as varchar) end
			when data_type in ('nvarchar','varchar') and cast(isnull(Character_Maximum_Length,'') as varchar) = '-1' then 'max'
			when data_type in ('char','nvarchar','varchar','nchar') then cast(isnull(Character_Maximum_Length,'') as varchar) else '' end+
			case when Data_Type in (Select datatype from @coltypes)then '' else  ')' end+
			case when Is_Nullable='No' then ' Not null,' else ' null,' end
			from tempdb.Information_Schema.COLUMNS where Table_Name='##tmpSubImportFull'
			order by ordinal_position
		select @dynSQL='Create table ##xxx ' + substring(@dynSQL,1,len(@dynSQL)-1) +' )'            
		if dbo.fn_WriteFile(@flatfile + '.sql', @dynSQL, 1) = 0 BEGIN
			RAISERROR('Error raised in TRY block.', 16, 1);
		END
	END TRY
	BEGIN CATCH
		INSERT INTO #tblSubErrors (msg, fatal)
		VALUES ('Unable to save subscription data for final import.',1)

		set @flatfile = ''

		GOTO on_done
	END CATCH
END ELSE
	set @flatfile = ''

-- ************************
-- generate result xml file 
-- ************************
on_done:
	select @importResult = (
		select getdate() as "@date", @flatfile as "@flatfile", 
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg", "@severity" = case fatal when 1 then 'fatal' else 'nonfatal' end
			from #tblSubErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE)
	
	IF OBJECT_ID('tempdb..#tblSubOrgCols') IS NOT NULL 
		DROP TABLE #tblSubOrgCols
	IF OBJECT_ID('tempdb..#tblSubImportCols') IS NOT NULL 
		DROP TABLE #tblSubImportCols
	IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
		EXEC('DROP TABLE ' + @var_tmpCols)
	IF OBJECT_ID('tempdb..#tblSubErrors') IS NOT NULL 
		DROP TABLE #tblSubErrors
	IF OBJECT_ID('tempdb..##tmpSubImportFull') IS NOT NULL 
		DROP TABLE ##tmpSubImportFull

RETURN 0
GO