USE [seminarWeb]
GO
/****** Object:  Table [dbo].[tblNationalPrograms]    Script Date: 05/13/2014 10:09:44 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
SET ANSI_PADDING ON
GO
CREATE TABLE [dbo].[tblNationalPrograms](
	[programID] [int] IDENTITY(1,1) NOT NULL,
	[programName] [varchar](100) NOT NULL,
 CONSTRAINT [PK_tblNationalPrograms] PRIMARY KEY CLUSTERED 
(
	[programID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
SET ANSI_PADDING OFF

USE [seminarWeb]
GO
/****** Object:  Table [dbo].[tblNationalProgramCSALinks]    Script Date: 05/13/2014 10:18:08 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblNationalProgramCSALinks](
	[programCSALinkID] [int] IDENTITY(1,1) NOT NULL,
	[programID] [int] NOT NULL,
	[CSALinkID] [int] NOT NULL,
 CONSTRAINT [PK_tblNationalProgramCSALinks] PRIMARY KEY CLUSTERED 
(
	[programCSALinkID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

USE [seminarWeb]
GO
/****** Object:  Index [IX_tblNationalProgramCSALinks]    Script Date: 05/13/2014 10:18:45 ******/
CREATE NONCLUSTERED INDEX [IX_tblNationalProgramCSALinks] ON [dbo].[tblNationalProgramCSALinks] 
(
	[programID] ASC,
	[CSALinkID] ASC
)WITH (PAD_INDEX  = OFF, STA


INSERT INTO [seminarWeb].[dbo].[tblNationalPrograms]
           ([programName])
     VALUES ('NATLE')
INSERT INTO [seminarWeb].[dbo].[tblNationalPrograms]
           ([programName])
     VALUES ('SAE')
INSERT INTO [seminarWeb].[dbo].[tblNationalPrograms]
           ([programName])
     VALUES ('Pharmacy')
INSERT INTO [seminarWeb].[dbo].[tblNationalPrograms]
           ([programName])
     VALUES ('Dentistry')
INSERT INTO [seminarWeb].[dbo].[tblNationalPrograms]
           ([programName])
     VALUES ('Physical Therapy')
INSERT INTO [seminarWeb].[dbo].[tblNationalPrograms]
           ([programName])
     VALUES ('Psychology')
INSERT INTO [seminarWeb].[dbo].[tblNationalPrograms]
           ([programName])
     VALUES ('Green Property')
	 
	 
-- Generated from the Excel Spreadsheet
-- Maps the CSALinkID to the National Program

declare @tmp table (sponsorID int, authorityid int)

INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(1, 1)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(53, 63)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(3, 3)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(2, 2)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(41, 54)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(4, 4)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(5, 5)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(72, 30)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(23, 21)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(75, 27)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(24, 22)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(6, 6)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(83, 43)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(25, 23)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(7, 7)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(29, 4)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(8, 8)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(9, 9)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(37, 44)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(10, 10)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(95, 32)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(47, 57)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(11, 11)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(62, 41)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(66, 37)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(76, 76)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(65, 34)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(13, 13)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(21, 4)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(22, 4)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(14, 14)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(15, 15)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(61, 33)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(17, 17)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(16, 16)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(26, 24)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(19, 19)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(18, 18)
INSERT INTO @tmp ([sponsorID],[AuthorityID]) VALUES(20, 20)

insert into dbo.tblNationalProgramCSALinks (programID, CSALinkID)
select 1, csa.CSALinkID 
from @tmp t
inner join dbo.tblCreditSponsorsAndAuthorities csa on csa.authorityID = t.authorityID and csa.sponsorID = t.sponsorID




USE [seminarWeb]
GO
/****** Object:  StoredProcedure [dbo].[sw_getNationalPrograms]    Script Date: 05/14/2014 08:20:36 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROC [dbo].[sw_getNationalPrograms]

AS

SELECT np.programID, np.programName, count(csa.programID) as csaCount
FROM dbo.tblNationalPrograms np
LEFT OUTER JOIN dbo.tblNationalProgramCSALinks csa on csa.programID = np.programid
group by np.programID, np.programName
ORDER BY np.programName

RETURN





USE [seminarWeb]
GO
/****** Object:  StoredProcedure [dbo].[sw_addSeminarAndCreditExpress]    Script Date: 05/14/2014 09:06:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROC [dbo].[sw_addSeminarAndCreditExpress]
@seminarID int,
@programID int

AS

DECLARE @statusID int
SELECT @statusID = dbo.fn_getStatusIDFromStatus('Not Submitted')

DECLARE @SWType varchar(4), @startdate datetime, @enddate datetime
SELECT @SWType = CASE
	WHEN sswod.ondemandid is null then 'SWL'
	WHEN sswl.liveid is null then 'SWOD'
	ELSE ''
	END, @startdate = sswl.datestart, @enddate = sswl.dateEnd
	FROM dbo.tblSeminars as s
	LEFT OUTER JOIN dbo.tblSeminarsSWLive as sswl on sswl.seminarID = s.seminarID
	LEFT OUTER JOIN dbo.tblSeminarsSWOD as sswod on sswod.seminarID = s.seminarID
	WHERE s.seminarID = @seminarID

INSERT INTO dbo.tblSeminarsAndCredit (seminarID, CSALinkID, statusID, courseapproval, wddxcreditsAvailable, 
	creditOfferedStartDate, creditOfferedEndDate, creditCompleteByDate, 
	isCreditRequired, isIDRequired, isCreditDefaulted)
SELECT @seminarID, csa.CSALinkID, @statusID, '', '', 
	CASE WHEN @SWType = 'SWOD' THEN getdate() ELSE DATEADD(dd, DATEDIFF(dd,0,@startdate), 0) END,
	CASE WHEN @SWType = 'SWOD' THEN dateadd(year,1,getdate()) ELSE DATEADD(ss,86399,DATEADD(dd, DATEDIFF(dd,0,@enddate), 0)) END,
	CASE WHEN @SWType = 'SWOD' THEN dateadd(year,1,getdate()) ELSE DATEADD(ss,86399,DATEADD(dd, DATEDIFF(dd,0,@enddate), 0)) END,
	0, 1, 0
FROM dbo.tblCreditSponsorsAndAuthorities AS csa
INNER join dbo.tblNationalProgramCSALinks as pl on pl.programID = @programID and pl.CSALinkID = csa.CSALinkID
WHERE NOT EXISTS (select seminarCreditID from dbo.tblSeminarsAndCredit where seminarID = @seminarID and CSALinkID = csa.CSALinkID)





USE [seminarWeb]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROC [dbo].[sw_getLinkedNationalPrograms]
@csaLinkIDs varchar(max)
AS

SELECT np.programID, np.programName, 
	(select count(csa.CSALinkID) 
		FROM dbo.tblNationalProgramCSALinks csa		
		INNER JOIN dbo.fn_IntListToTable(@csaLinkIDs) as s on s.intValue = csa.CSALinkID 
		where csa.programID = np.programid) as csaCount  
FROM dbo.tblNationalPrograms np
ORDER BY np.programName

RETURN


USE [seminarWeb]
GO
/****** Object:  Table [dbo].[tblNationalProgramParticipants]    Script Date: 05/15/2014 08:26:17 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tblNationalProgramParticipants](
	[programParticipantID] [int] IDENTITY(1,1) NOT NULL,
	[participantID] [int] NOT NULL,
	[programID] [int] NOT NULL,
 CONSTRAINT [PK_tblNationalProgramParticipants] PRIMARY KEY CLUSTERED 
(
	[programParticipantID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

USE [seminarWeb]
GO
USE [seminarWeb]
GO
/****** Object:  Index [IX_tblNationalProgramParticipants]    Script Date: 05/15/2014 13:50:34 ******/
CREATE NONCLUSTERED INDEX [IX_tblNationalProgramParticipants] ON [dbo].[tblNationalProgramParticipants] 
(
	[programID] ASC,
	[participantID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]



USE [seminarWeb]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROC [dbo].[sw_getParticipantNationalPrograms]
@participantID int
AS

SELECT np.programID, np.programName, count(npp.programID) as nppCount
FROM dbo.tblNationalPrograms np
LEFT OUTER JOIN dbo.tblNationalProgramParticipants npp on npp.programID = np.programid
	AND npp.participantID = @participantID
group by np.programID, np.programName
ORDER BY np.programName

RETURN
