USE [platformQueue]
GO
CREATE TABLE [dbo].[tblQueueTypesDataColumnDataTypes](
	[dataTypeID] [int] IDENTITY(1,1) NOT NULL,
	[dataType] [varchar](20) NOT NULL,
	[dataTypeCode] [varchar](20) NOT NULL,
 CONSTRAINT [PK_tblQueueTypesDataColumnDataTypes] PRIMARY KEY CLUSTERED 
(
	[dataTypeID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON, FILLFACTOR = 90) ON [PRIMARY],
 CONSTRAINT [IX_tblQueueTypesDataColumnDataTypes] UNIQUE NONCLUSTERED 
(
	[dataTypeCode] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON, FILLFACTOR = 90) ON [PRIMARY]
) ON [PRIMARY]
GO


CREATE TABLE [dbo].[tblQueueTypeDataColumns](
	[columnID] [int] IDENTITY(1,1) NOT NULL,
	[queueTypeID] [int] NOT NULL,
	[columnName] [varchar](255) NOT NULL,
	[dataTypeID] [int] NOT NULL,
	[ColumnDesc] [varchar](255) NULL,
 CONSTRAINT [PK_tblQueueTypeDataColumns] PRIMARY KEY CLUSTERED 
(
	[columnID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON, FILLFACTOR = 90) ON [PRIMARY],
 CONSTRAINT [IX_tblQueueTypeDataColumns] UNIQUE NONCLUSTERED 
(
	[queueTypeID] ASC,
	[columnName] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON, FILLFACTOR = 90) ON [PRIMARY]
) ON [PRIMARY]
GO
ALTER TABLE [dbo].[tblQueueTypeDataColumns]  WITH CHECK ADD  CONSTRAINT [FK_tblQueueTypeDataColumns_tblQueueTypes] FOREIGN KEY([queueTypeID])
REFERENCES [dbo].[tblQueueTypes] ([queueTypeID])
GO
ALTER TABLE [dbo].[tblQueueTypeDataColumns] CHECK CONSTRAINT [FK_tblQueueTypeDataColumns_tblQueueTypes]
GO
ALTER TABLE dbo.tblQueueTypeDataColumns ADD CONSTRAINT
	FK_tblQueueTypeDataColumns_tblQueueTypesDataColumnDataTypes FOREIGN KEY
	(
	dataTypeID
	) REFERENCES dbo.tblQueueTypesDataColumnDataTypes
	(
	dataTypeID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
GO


CREATE TABLE [dbo].[tblQueueItemData](
	[dataID] [bigint] IDENTITY(1,1) NOT NULL,
	[itemGroupUID] [uniqueidentifier] NOT NULL,
	[itemUID] [uniqueidentifier] NOT NULL,
	[recordedByMemberID] [int] NOT NULL,
	[siteID] [int] NOT NULL,
	[columnID] [int] NOT NULL,
	[dataKey] [varchar](255) NULL,
	[columnValueString] [varchar](255) NULL,
	[columnValueDecimal2] [decimal](9, 2) NULL,
	[columnValueInteger] [int] NULL,
	[columnvalueDate] [datetime] NULL,
	[columnValueBit] [bit] NULL,
	[columnValueXML] [xml] NULL,
	[columnValueText] [varchar](max) NULL,
 CONSTRAINT [PK_tblQueueItemData] PRIMARY KEY CLUSTERED 
(
	[dataID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON, FILLFACTOR = 90) ON [PRIMARY]
) ON [PRIMARY]
GO


insert into tblQueueTypesDataColumnDataTypes (dataType, dataTypeCode)
select dataType, dataTypeCode
from membercentral.dbo.ams_memberDataColumnDataTypes
where dataTypeCode in ('STRING','DECIMAL2','INTEGER','DATE','BIT','XML')
order by dataTypeID
GO

insert into tblQueueTypesDataColumnDataTypes (dataType, dataTypeCode) values ('Long Text','TEXT')
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[queue_createQueueTypeDataColumn]') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].[queue_createQueueTypeDataColumn]
GO
CREATE PROC dbo.queue_createQueueTypeDataColumn
@queueTypeID int,
@columnName varchar(255),
@columnDesc varchar(255),
@dataTypeCode varchar(20),
@columnID int OUTPUT

AS

-- check for existing column with same name
SELECT @columnID = null

-- if there, return 0
IF EXISTS(select columnID from dbo.tblQueueTypeDataColumns where queueTypeID = @queueTypeID and columnName = @columnName)
	SELECT @columnID = 0

-- if not there, add it
ELSE BEGIN
	DECLARE @rc int, @valueID int
	
	DECLARE @dataTypeID int
	SELECT @dataTypeID = dataTypeID from dbo.tblQueueTypesDataColumnDataTypes where dataTypeCode = @dataTypeCode
		IF @@ERROR <> 0 OR @dataTypeID is null GOTO on_error

	-- add column
	INSERT INTO dbo.tblQueueTypeDataColumns (queueTypeID, columnName, dataTypeID, ColumnDesc)
	VALUES (@queueTypeID, @columnName, @dataTypeID, @columnDesc)
		IF @@ERROR <> 0 GOTO on_error
		SELECT @columnID = SCOPE_IDENTITY()
END

-- normal return
RETURN 0

-- error exit
on_error:
	SELECT @columnID = 0
	RETURN -1
GO



if not exists (select queueTypeID from dbo.tblQueueTypes where queueType='FirmSubStatements')
	insert into dbo.tblQueueTypes (queueType) values ('FirmSubStatements')
GO



declare @queueTypeID int, @trashID int
select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType='FirmSubStatements'

exec dbo.queue_createQueueTypeDataColumn @queueTypeID=@queueTypeID, @columnName='FirmChildSub', @columnDesc='rootSubscriptionID of a firm childAccount (firmMemberID in datakey)', @dataTypeCode='INTEGER', @columnID=@trashID OUTPUT
exec dbo.queue_createQueueTypeDataColumn @queueTypeID=@queueTypeID, @columnName='FirmChildNoSub', @columnDesc='memberID of a firm childAccount with no subscriptions (firmMemberID in datakey)', @dataTypeCode='INTEGER', @columnID=@trashID OUTPUT
exec dbo.queue_createQueueTypeDataColumn @queueTypeID=@queueTypeID, @columnName='ConfigParam', @columnDesc='config option value (configkey in datakey)', @dataTypeCode='STRING', @columnID=@trashID OUTPUT
exec dbo.queue_createQueueTypeDataColumn @queueTypeID=@queueTypeID, @columnName='ConfigText', @columnDesc='config varchar(max) value (configkey in datakey)', @dataTypeCode='TEXT', @columnID=@trashID OUTPUT
GO


declare @queueTypeID int
select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType='FirmSubStatements'

insert into platformQueue.dbo.tblQueueStatuses (queueTypeID,queueStatus) values (@queueTypeID,'insertingItems')
insert into platformQueue.dbo.tblQueueStatuses (queueTypeID,queueStatus) values (@queueTypeID,'readyToProcess')
insert into platformQueue.dbo.tblQueueStatuses (queueTypeID,queueStatus) values (@queueTypeID,'grabbedForProcessing')
insert into platformQueue.dbo.tblQueueStatuses (queueTypeID,queueStatus) values (@queueTypeID,'processingFirm')
insert into platformQueue.dbo.tblQueueStatuses (queueTypeID,queueStatus) values (@queueTypeID,'readyToNotify')
insert into platformQueue.dbo.tblQueueStatuses (queueTypeID,queueStatus) values (@queueTypeID,'grabbedForNotifying')
insert into platformQueue.dbo.tblQueueStatuses (queueTypeID,queueStatus) values (@queueTypeID,'done')
GO



IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].job_firmSubStatements_grabForProcessing') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].job_firmSubStatements_grabForProcessing
GO
CREATE PROC job_firmSubStatements_grabForProcessing
@serverID int

AS

SET NOCOUNT ON


-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	DECLARE @batchSize int
	set @batchSize = 50

	declare @statusReady int, @statusGrabbed int
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'FirmSubStatements'
		and qs.queueStatus = 'readyToProcess'
	select @statusGrabbed = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'FirmSubStatements'
		and qs.queueStatus = 'grabbedForProcessing'

	declare @jobUID uniqueidentifier
	set @jobUID = NEWID()


	-- dequeue in order of dateAdded. get @batchsize firms
	IF OBJECT_ID('tempdb..#tmpTblQueueItems_firmbilling') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_firmbilling
	CREATE TABLE #tmpTblQueueItems_firmbilling (itemUID uniqueidentifier, itemGroupUID uniqueidentifier, 
		jobUID uniqueidentifier, recordedByMemberID int, siteID int, memberID int, memberNumber varchar(50), 
		firstname varchar(75), lastname varchar(75), company varchar(200), address1 varchar(100), 
		address2 varchar(100), address3 varchar(100), city varchar(35), stateProv varchar(4),
		postalCode varchar(25), country varchar(100), xmlConfigParam xml, xmlFirms xml)

	update qi WITH (UPDLOCK, READPAST)
	set qi.queueStatusID = @statusGrabbed,
		qi.dateUpdated = getdate(),
		qi.jobUID = @jobUID,
		qi.jobDateStarted = getdate(),
		qi.jobServerID = @serverID
		OUTPUT inserted.itemUID, null, inserted.jobUID, null, null, null, null, null, null, null, 
			null, null, null, null, null, null, null, null, null
		INTO #tmpTblQueueItems_firmbilling
	from platformQueue.dbo.tblQueueItems as qi
	inner join (
		select top(@BatchSize) qi2.itemUID 
		from platformQueue.dbo.tblQueueItems as qi2
		where qi2.queueStatusID = @statusReady
		order by qi2.dateAdded, qi2.itemUID
		) as batch on batch.itemUID = qi.itemUID
	where qi.queueStatusID = @statusReady


	-- get memberID, itemGroupUID, siteID, recordedByMemberID from item data
	update tmp
	set tmp.memberID = idata.memberID,
		tmp.itemGroupUID = idata.itemGroupUID,
		tmp.recordedByMemberID = idata.recordedByMemberID,
		tmp.siteID = idata.siteID
	from #tmpTblQueueItems_firmbilling as tmp
	cross apply (
		select min(cast(dataKey as int)) as memberID, min(cast(itemGroupUID as varchar(60))) as itemGroupUID, 
			min(recordedByMemberID) as recordedByMemberID, min(siteID) as siteID
		from platformQueue.dbo.tblQueueItemData as qid
		inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
			and dc.columnname in ('FirmChildSub','FirmChildNoSub')
		where qid.itemUID = tmp.itemUID
	) as idata


	-- get address info for firm accounts
	update tmp 
	set	tmp.memberNumber = m.memberNumber, 
		tmp.firstname = m.firstname, 
		tmp.lastname = m.lastname, 
		tmp.company = m.company, 
		tmp.address1 = ma.address1, 
		tmp.address2 = ma.address2, 
		tmp.address3 = ma.address3, 
		tmp.city = ma.city, 
		tmp.stateProv = s.code, 
		tmp.postalCode = ma.postalCode,
		tmp.country = c.country
	from #tmpTblQueueItems_firmbilling as tmp
	inner join membercentral.dbo.ams_members as m on m.memberID = tmp.memberID
	left outer join membercentral.dbo.ams_memberAddresses as ma on ma.memberID = m.memberID 
		and ma.addressTypeID = m.billingAddressTypeID
	left outer join membercentral.dbo.ams_states as s on s.stateID = ma.stateID
	left outer join membercentral.dbo.ams_countries as c on c.countryID = ma.countryID


	-- get config params for each item
	update tmp
	set tmp.xmlConfigParam = config.configXML
	from #tmpTblQueueItems_firmbilling as tmp
	cross apply (
		select cast(isnull((
			select reportParam, paramvalue	
			from (
				select datakey as reportParam, cast(columnValueString as varchar(max)) as paramvalue
				from platformQueue.dbo.tblQueueItemData qid
				inner join platformQueue.dbo.tblQueueTypeDataColumns dc on dc.columnID = qid.columnID
					and dc.columnname = 'ConfigParam'
				where qid.itemUID = tmp.itemUID
					union all
				select datakey as reportParam, columnValuetext as paramvalue
				from platformQueue.dbo.tblQueueItemData qid
				inner join platformQueue.dbo.tblQueueTypeDataColumns dc on dc.columnID = qid.columnID
					and dc.columnname = 'ConfigText'
				where qid.itemUID = tmp.itemUID
			) as tmp
			for xml path('param'), root('params'), type
		),'<params/>') as xml) as configXML
	) as config


	-- get all firm members
	IF OBJECT_ID('tempdb..#qryAllFirmMembers') IS NOT NULL 
		DROP TABLE #qryAllFirmMembers
	CREATE TABLE #qryAllFirmMembers (itemUID uniqueidentifier, childMemberID int, childMemberNumber varchar(50), 
		prefix varchar(50), firstname varchar(75), lastname varchar(75), company varchar(200), suffix varchar(50),
		address1 varchar(100), address2 varchar(100), address3 varchar(100), city varchar(35), stateProv varchar(4),
				postalCode varchar(25), country varchar(100)
	)
	insert into #qryAllFirmMembers (itemUID, childMemberID)
	select distinct tmp.itemUID, qid.columnValueInteger
	from #tmpTblQueueItems_firmbilling as tmp
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = tmp.itemUID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
		and dc.columnname = 'FirmChildNoSub'
		union
	select distinct tmp.itemUID, m.activeMemberID
	from #tmpTblQueueItems_firmbilling as tmp
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = tmp.itemUID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
		and dc.columnname = 'FirmChildSub'
	inner join membercentral.dbo.sub_subscribers as ss on ss.subscriberID = qid.columnValueInteger
	inner join membercentral.dbo.ams_members as m on m.memberID = ss.memberID

	update fm 
	set childMemberNumber = m.MemberNumber, 
		prefix = m.prefix,
		firstname = m.firstname, 
		lastname = m.lastname, 
		company = m.company,
		suffix = m.suffix,
		address1 = ma.address1, 
		address2 = ma.address2, 
		address3 = ma.address3, 
		city = ma.city, 
		stateProv = s.code, 
		postalCode = ma.postalCode,
		country = c.country

	from #qryAllFirmMembers as fm
	inner join membercentral.dbo.ams_members as m on m.memberID = fm.childMemberID
	left outer join membercentral.dbo.ams_memberAddresses as ma on ma.memberID = m.memberID 
		and ma.addressTypeID = m.billingAddressTypeID
	left outer join membercentral.dbo.ams_states as s on s.stateID = ma.stateID
	left outer join membercentral.dbo.ams_countries as c on c.countryID = ma.countryID


	-- get all subs
	IF OBJECT_ID('tempdb..#qrySubs') IS NOT NULL 
		DROP TABLE #qrySubs
	CREATE TABLE #qrySubs (itemUID uniqueidentifier, subscriberID int, memberID int, subscriptionID int, typeID int,
		typeName varchar(100), subscriptionName varchar(300), rateName varchar(200), frequencyName varchar(50),
		[status] varchar(1), statusName varchar(50), subStartDate datetime, subEndDate datetime, graceEndDate datetime,
		parentSubscriberID int, rootSubscriberID int, lastPrice money, rfid int, thePath varchar(max), 
		subAmount money, subAmountDue money, subAmountPaid money, subscriberPath varchar(200), saleTransactionID int, 
		glaccountID int, invoiceContentVersionID int, invoiceProfileID int, invoiceProfileImageExt varchar(5))

	insert into #qrySubs (itemUID, subscriberID, memberID, subscriptionID, typeID, typeName, subscriptionName, rateName, 
		frequencyName, [status], statusName, subStartDate, subEndDate, graceEndDate, parentSubscriberID, rfid, 
		rootSubscriberID, lastPrice, subscriberPath, glaccountID)
	select tmp.itemUID, ss.subscriberID, m.activeMemberID, ss.subscriptionID, t.typeID, t.typeName, sub.subscriptionName, 
		r.rateName, f.frequencyName, st.statusCode, st.statusName, ss.subStartDate, ss.subEndDate, ss.graceEndDate, 
		ss.parentSubscriberID, ss.rfid, ss.rootSubscriberID, ss.lastprice, ss.subscriberPath, ss.glaccountID
	from #tmpTblQueueItems_firmbilling as tmp
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = tmp.itemUID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
		and dc.columnname = 'FirmChildSub'
	inner join membercentral.dbo.sub_subscribers as rootss on rootss.subscriberID = qid.columnValueInteger
	inner join membercentral.dbo.sub_subscribers as ss on rootss.subscriberID = ss.rootSubscriberID
	inner join membercentral.dbo.sub_subscriptions as sub on sub.subscriptionID = ss.subscriptionID
	inner join membercentral.dbo.sub_types as t on sub.typeiD = t.typeID
	inner join membercentral.dbo.ams_members as m on ss.memberID = m.memberID
	inner join membercentral.dbo.sub_statuses as st on st.statusID = ss.statusID 
		and st.statuscode in ('A','I','O','P')
	inner join membercentral.dbo.sub_rateFrequencies as rf on rf.rfid = ss.rfid
	inner join membercentral.dbo.sub_rates as r on r.rateID = rf.rateID 
	inner join membercentral.dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID

	update qs 
	set saleTransactionID = t.transactionID,
		subAmount = ts.cache_amountAfterAdjustment,
		subAmountDue = ts.cache_amountAfterAdjustment - ts.cache_activePaymentAllocatedAmount,
		subAmountPaid = ts.cache_activePaymentAllocatedAmount,
		invoiceContentVersionID = it.messageContentVersionID
	from #qrySubs as qs
	inner join membercentral.dbo.tr_applications as app on app.ItemID = qs.subscriberID 
		and app.applicationTypeID = 17 
		and app.itemType = 'Dues' 
		and app.status = 'A'
	inner join membercentral.dbo.tr_transactions as t on t.transactionID = app.transactionID
	inner join membercentral.dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID
	inner join membercentral.dbo.tr_invoiceTransactions it on it.transactionID = t.transactionID

	-- update invoiceProfileID and invoice image (billed subs have no transactions to join against)
	update qs 
	set invoiceProfileID = ip.profileID, 
		invoiceProfileImageExt = ip.imageExt
	from #qrySubs as qs
	inner join membercentral.dbo.tr_glaccounts as gl on gl.glaccountID = qs.glaccountID
	inner join membercentral.dbo.tr_invoiceProfiles ip on ip.profileID = gl.invoiceProfileID

	-- update invoiceContentVersionID for subs with no transactions
	update qs 
	set invoiceContentVersionID = cv.contentVersionID
	from #qrySubs as qs
	inner join membercentral.dbo.tr_glaccounts as gl on gl.glaccountID = qs.glaccountID
		and qs.saleTransactionID is null
	inner join membercentral.dbo.cms_content as c on c.contentID = gl.invoiceContentID
	inner join membercentral.dbo.cms_contentLanguages as cl on cl.contentID = c.contentID
	inner join membercentral.dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID

	
	-- get sub tree totals
	IF OBJECT_ID('tempdb..#tblSubTreeTotals') IS NOT NULL 
		DROP TABLE #tblSubTreeTotals
	CREATE TABLE #tblSubTreeTotals (itemUID uniqueidentifier, rootsubscriberID int, tree_subAmount money, 
		tree_subAmountDue money, tree_subAmountPaid money)

	insert into #tblSubTreeTotals (itemUID, rootsubscriberID, tree_subAmount, tree_subAmountDue, tree_subAmountPaid)
	select itemUID, rootSubscriberID, sum(subAmount) as tree_subAmount, sum(subAmountDue) as tree_subAmountDue, sum(subAmountPaid) as tree_subAmountPaid
	from #qrySubs 
	group by itemUID, rootSubscriberID


	-- get sub payments
	IF OBJECT_ID('tempdb..#tblSubPayments') IS NOT NULL 
		DROP TABLE #tblSubPayments
	CREATE TABLE #tblSubPayments (itemUID uniqueidentifier, subscriberID int, rootsubscriberID int, transactionID int, 
		allocatedAmount money, detail varchar(max), depositdate datetime)

	insert into #tblSubPayments (itemUID, subscriberID, rootsubscriberID, transactionID, allocatedAmount, detail, depositdate)
	select s.itemUID, s.subscriberID, s.rootsubscriberID, t.transactionID, st.allocatedAmount, t.detail, b.depositDate
	from #qrySubs as s
	inner join membercentral.dbo.tr_applications as app on app.ItemID = s.subscriberID 
		and app.applicationTypeID = 17 
		and app.itemType = 'Dues' 
		and app.status = 'A'
	CROSS APPLY membercentral.dbo.fn_tr_getAllocatedPaymentsofSale(app.transactionID) AS st
	inner join membercentral.dbo.tr_transactions as t on t.transactionID = st.paymentTransactionID
	inner join membercentral.dbo.tr_batchTransactions as bt on bt.transactionID = t.transactionID
	inner join membercentral.dbo.tr_batches as b on b.batchID = bt.batchID


	-- get sub invoices
	IF OBJECT_ID('tempdb..#tblSubInvoices') IS NOT NULL 
		DROP TABLE #tblSubInvoices
	CREATE TABLE #tblSubInvoices (itemUID uniqueidentifier, subscriberID int, rootsubscriberID int, invoiceID int,
		invoiceCode char(8), datebilled datetime, datedue datetime, statusID int, [status] varchar(50), amount money,
		amountDue money)

	insert into #tblSubInvoices (itemUID, subscriberID, rootsubscriberID, invoiceID, invoiceCode, datebilled, datedue,
		statusID, [status], amount, amountDue)
	select s.itemUID, s.subscriberID, s.rootsubscriberID, i.invoiceID, i.invoiceCode, i.datebilled, i.datedue, ist.statusID, 
		ist.status, sum(st.amount) as amount, sum(st.amountDue) as amountDue 
	from #qrySubs as s
	CROSS APPLY membercentral.dbo.fn_sub_subscriberTransactions(s.subscriberID) AS st 
	inner join membercentral.dbo.tr_invoices as i on i.invoiceID = st.invoiceID
	inner join membercentral.dbo.tr_invoiceStatuses as ist on ist.statusID = i.statusID
		and ist.status <> 'open'
	group by s.itemUID, s.subscriberID, s.rootsubscriberID, i.invoiceID, i.invoiceCode, i.datebilled, i.datedue, 
		ist.statusID, ist.status


	-- populate table var of contentVersions referenced by qrySubs
	declare @invoiceContent table (contentVersionID int PRIMARY KEY, rawcontent varchar(max))
	declare @invoiceContentandSubtrees table (contentVersionID int, rootSubscriberID int, messageNumber int)

	insert into @invoiceContent (contentVersionID, rawcontent)
	select distinct cv.contentVersionID, cv.rawcontent
	from #qrySubs as qs
	inner join membercentral.dbo.cms_contentVersions as cv on cv.contentVersionID = qs.invoiceContentVersionID

	insert into @invoiceContentandSubtrees (contentVersionID, rootSubscriberID, messageNumber)
	select ic.contentVersionID, qs.rootsubscriberID, row_number() over (partition by qs.rootsubscriberID order by min(qs.subscriberPath))
	from #qrySubs as qs
	inner join @invoiceContent as ic on ic.contentVersionID = qs.invoiceContentVersionID
	group by ic.contentVersionID, qs.rootsubscriberID


	-- firms xml
	update tmp
	set tmp.xmlFirms = firms.firmXML
	from #tmpTblQueueItems_firmbilling as tmp
	cross apply (
		select cast(isnull((		
			select company.memberID as '@firmmemberid',
				company.memberNumber as '@firmmembernumber' ,
				company.firstname as '@firmfirstname' ,
				company.lastname as '@firmlastname' ,
				company.company as '@firmcompany' ,
				company.address1 as '@firmaddress1' ,
				company.address2 as '@firmaddress2' ,
				company.address3 as '@firmaddress3' ,
				company.city as '@firmcity' ,
				company.stateProv as '@firmstate' ,
				company.postalCode as '@firmpostalcode',
				company.country as '@firmcountry',
				( select
					account.childmemberid as '@childmemberID',
					account.childMemberNumber as '@childmembernumber',
					isnull(company.memberID,0) as '@firmmemberid',
					case when exists (select * from #qrySubs as st2 where st2.memberID = account.childmemberid and st2.itemUID = tmp.itemUID) then 1 else 0 end as '@hassubscription',
					account.firstname as '@firstname', 
					account.lastname as '@lastname',
					account.prefix as '@prefix',
					account.suffix as '@suffix',
					account.lastname + ', ' + account.firstname + ' (' + account.childMemberNumber + ')' as '@namestring',
					account.company as '@company',
					account.address1 as '@address1' ,
					account.address2 as '@address2' ,
					account.address3 as '@address3' ,
					account.city as '@city' ,
					account.stateProv as '@state' ,
					account.postalCode as '@postalcode',
					account.country as '@country',

					( select
						subscriptionTree.rootsubscriberid as '@rootsubscriberid',
						case when subscriptionTree.rootsubscriberid is null then null else account.childmemberid end as '@activeMemberID',
						qst.tree_subAmount as '@subamount_total',
						qst.tree_subAmountPaid as '@subamountpaid_total',
						qst.tree_subAmountDue as '@subamountdue_total',
						CONVERT(VARCHAR(8),subscriptionTree.substartdate, 1) + ' - ' + CONVERT(VARCHAR(8),subscriptionTree.subenddate, 1) as '@subtreedaterange',
						cast(subscriptionTree.invoiceProfileID as varchar(10)) + '.' + subscriptionTree.invoiceProfileImageExt as '@subtreeinvoiceprofileimage',
						( select
							subscriber.subscriberid as '@subscriberID',
							account.childmemberid as '@activeMemberID',
							subscriber.subscriptionid as '@subscriptionid',
							subscriber.typeid as '@typeid',
							subscriber.typename as '@typename',
							subscriber.subscriptionname as '@subscriptionname',
							subscriber.ratename as '@ratename',
							subscriber.frequencyname as '@frequencyname',
							subscriber.status as '@status',
							subscriber.statusname as '@statusname',
							subscriber.substartdate as '@substartdate',
							subscriber.subenddate as '@subenddate',
							CONVERT(VARCHAR(8),subscriber.substartdate, 1) + ' - ' + CONVERT(VARCHAR(8),subscriber.subenddate, 1) as '@subdaterange',
							subscriber.graceenddate as '@graceenddate',
							subscriber.parentsubscriberid as '@parentsubscriberid',
							subscriber.rootsubscriberid as '@rootsubscriberid',
							subscriber.rfid as '@rfid',
							subscriber.thepath as '@thepath',
							subscriber.lastprice as '@lastprice',
							subscriber.subAmount as '@subamount',
							subscriber.subAmountPaid as '@subamountpaid',
							subscriber.subAmountDue as '@subamountdue',
							subscriber.subscriberPath as '@subscriberpath',
							subscriber.invoiceContentVersionID as '@invoicecontentversionid',
							icst.messageNumber as '@invoicecontentfootnotenumber'
						from #qrySubs as subscriber
						left outer join @invoiceContentandSubtrees as icst on icst.rootsubscriberID = subscriber.rootsubscriberID
							and subscriber.invoiceContentVersionID = icst.contentVersionID
						where subscriber.itemUID = tmp.itemUID
						and subscriber.rootSubscriberID = subscriptionTree.subscriberID
						order by subscriber.subscriberPath
						for xml path('subscriber'), type
						),
						( select
							rootsubscriberID as '@rootsubscriberID',
							invoiceID as '@invoiceid',
							invoiceCode as '@invoicecode',
							datebilled as '@datebilled',
							datedue as '@datedue',
							statusID as '@statusid',
							[status] as '@status',
							sum(amount) as '@amount',
							sum(amountDue) as '@amountDue'
						from #tblSubInvoices as si
						where si.itemUID = tmp.itemUID
						and si.rootSubscriberID = subscriptionTree.rootsubscriberID
						group by rootsubscriberID, invoiceID, invoiceCode, datebilled, datedue, statusID, [status]
						order by datedue
						for xml path ('invoice'),root('invoices'), type
						),
						( select
							rootsubscriberID as '@rootsubscriberID',
							transactionID as '@transactionID',
							depositdate as '@datebilled',
							detail as '@detail',
							sum(allocatedAmount) as '@allocatedAmount'
						from #tblSubPayments sp
						where sp.itemUID = tmp.itemUID
						and sp.rootSubscriberID = subscriptionTree.rootsubscriberID
						group by rootsubscriberID ,transactionID ,detail,depositdate
						order by depositdate
						for xml path('payment'), root('payments'), type
						),
						( select
							icst.messageNumber as '@invoicecontentfootnotenumber',
							ic.contentversionid as '@contentversionid',
							ic.rawcontent as '@rawcontent'
						from #qrySubs as subscriber
						inner join @invoiceContent as ic on ic.contentVersionID = subscriber.invoiceContentVersionID
							and subscriber.itemUID = tmp.itemUID
							and subscriber.rootSubscriberID = subscriptionTree.subscriberID
						inner join @invoiceContentandSubtrees as icst on icst.rootsubscriberID = subscriber.rootsubscriberID
							and icst.contentVersionID = ic.contentVersionID
						group by icst.messageNumber, ic.contentversionid, ic.rawcontent
						order by icst.messageNumber
						for xml path('invoicemessage'), root('invoicemessages'), type
						)
						from #qrySubs as subscriptionTree
						inner join #tblSubTreeTotals as qst on qst.rootSubscriberID = subscriptionTree.rootSubscriberID
						where subscriptionTree.itemUID = tmp.itemUID
						and subscriptionTree.memberID = account.childMemberID
						and subscriptionTree.subscriberID = subscriptionTree.rootsubscriberID
						order by subscriptionTree.rootsubscriberid
						for xml path('subscriptiontree'), type
					)
					from #qryAllFirmMembers as account
					where account.itemUID = company.itemUID
					order by account.lastname, account.firstname, account.childMemberID
					for xml path('account'), type
				)
			from #tmpTblQueueItems_firmbilling as company
			where company.itemUID = tmp.itemUID
			order by company.company, company.memberID
			for xml path('company'), root('companies'), type
		),'<companies/>') as xml) as firmXML
	) as firms


	-- final data
	select itemUID, itemGroupUID, jobUID, memberNumber, company, xmlConfigParam, xmlFirms
	from #tmpTblQueueItems_firmbilling
	order by itemUID


	IF OBJECT_ID('tempdb..#tblSubInvoices') IS NOT NULL 
		DROP TABLE #tblSubInvoices
	IF OBJECT_ID('tempdb..#tblSubPayments') IS NOT NULL 
		DROP TABLE #tblSubPayments
	IF OBJECT_ID('tempdb..#tblSubTreeTotals') IS NOT NULL 
		DROP TABLE #tblSubTreeTotals
	IF OBJECT_ID('tempdb..#qrySubs') IS NOT NULL 
		DROP TABLE #qrySubs
	IF OBJECT_ID('tempdb..#qryAllFirmMembers') IS NOT NULL 
		DROP TABLE #qryAllFirmMembers
	IF OBJECT_ID('tempdb..#tmpTblQueueItems_firmbilling') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_firmbilling

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH
GO


IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[job_firmSubStatements_clearDone]') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].[job_firmSubStatements_clearDone]
GO
CREATE PROC [dbo].[job_firmSubStatements_clearDone]
AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @statusDone int
	select @statusDone = qs.queueStatusID
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'FirmSubStatements'
		and qs.queueStatus = 'done'

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID = @statusDone
			except
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID <> @statusDone
	)
	DELETE from platformQueue.dbo.tblQueueItems
	where itemUID in (
		select qi.itemUID
		FROM platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qid.itemGroupUID
		WHERE qi.queueStatusID = @statusDone
	)

	DELETE from platformQueue.dbo.tblQueueItemData
	where itemUID not in (select itemUID from platformQueue.dbo.tblQueueItems)


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH
GO



IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[job_firmSubStatements_grabForNotification]') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].[job_firmSubStatements_grabForNotification]
GO
CREATE PROC [dbo].[job_firmSubStatements_grabForNotification]
AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @statusReady int, @statusGrabbed int, @queueTypeID int
	select @statusReady = qs.queueStatusID , @queueTypeID = qt.queueTypeID
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'FirmSubStatements'
		and qs.queueStatus = 'readyToNotify'
	select @statusGrabbed = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'FirmSubStatements'
		and qs.queueStatus = 'grabbedForNotifying'

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL 
		DROP TABLE #tmpNotify
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier)

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID = @statusReady
			except
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID <> @statusReady
	)
	UPDATE platformQueue.dbo.tblQueueItems WITH (UPDLOCK, READPAST)
	SET queueStatusID = @statusGrabbed,
		dateUpdated = getdate()
		OUTPUT qid.itemGroupUID
		INTO #tmpNotify
	FROM platformQueue.dbo.tblQueueItems as qi
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qid.itemGroupUID
	where qi.queueStatusID = @statusReady


	-- return itemGroupUIDs that can be marked as done
	select distinct tmpN.itemGroupUID, me.email as reportEmail, s.siteName, s.siteCode
	from (select distinct itemGroupUID from #tmpNotify) as tmpN
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = qid.recordedByMemberID
	LEFT OUTER JOIN membercentral.dbo.ams_memberEmails as me 
		INNER JOIN membercentral.dbo.ams_memberEmailTypes as met on met.emailTypeID = me.emailTypeID and met.emailTypeOrder = 1
		on me.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.sites as s on s.siteID = qid.siteID
	order by tmpN.itemGroupUID


	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL 
		DROP TABLE #tmpNotify

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH
GO


USE [platformQueue]
GO
ALTER PROC [dbo].[job_firmSubStatements_grabForProcessing]
@serverID int

AS

SET NOCOUNT ON


-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	DECLARE @batchSize int
	set @batchSize = 50

	declare @statusReady int, @statusGrabbed int
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'FirmSubStatements'
		and qs.queueStatus = 'readyToProcess'
	select @statusGrabbed = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'FirmSubStatements'
		and qs.queueStatus = 'grabbedForProcessing'

	declare @jobUID uniqueidentifier
	set @jobUID = NEWID()


	-- dequeue in order of dateAdded. get @batchsize firms
	IF OBJECT_ID('tempdb..#tmpTblQueueItems_firmbilling') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_firmbilling
	CREATE TABLE #tmpTblQueueItems_firmbilling (itemUID uniqueidentifier, itemGroupUID uniqueidentifier, 
		jobUID uniqueidentifier, recordedByMemberID int, siteID int, memberID int, memberNumber varchar(50), 
		firstname varchar(75), lastname varchar(75), company varchar(200), address1 varchar(100), 
		address2 varchar(100), address3 varchar(100), city varchar(35), stateProv varchar(4),
		postalCode varchar(25), country varchar(100), xmlConfigParam xml, xmlFirms xml)

	update qi WITH (UPDLOCK, READPAST)
	set --qi.queueStatusID = @statusGrabbed,
		qi.dateUpdated = getdate(),
		qi.jobUID = @jobUID,
		qi.jobDateStarted = getdate(),
		qi.jobServerID = @serverID
		OUTPUT inserted.itemUID, null, inserted.jobUID, null, null, null, null, null, null, null, 
			null, null, null, null, null, null, null, null, null
		INTO #tmpTblQueueItems_firmbilling
	from platformQueue.dbo.tblQueueItems as qi
	inner join (
		select top(@BatchSize) qi2.itemUID 
		from platformQueue.dbo.tblQueueItems as qi2
		where qi2.queueStatusID = @statusReady
		order by qi2.dateAdded, qi2.itemUID
		) as batch on batch.itemUID = qi.itemUID
	where qi.queueStatusID = @statusReady


	-- get memberID, itemGroupUID, siteID, recordedByMemberID from item data
	update tmp
	set tmp.memberID = idata.memberID,
		tmp.itemGroupUID = idata.itemGroupUID,
		tmp.recordedByMemberID = idata.recordedByMemberID,
		tmp.siteID = idata.siteID
	from #tmpTblQueueItems_firmbilling as tmp
	cross apply (
		select min(cast(dataKey as int)) as memberID, min(cast(itemGroupUID as varchar(60))) as itemGroupUID, 
			min(recordedByMemberID) as recordedByMemberID, min(siteID) as siteID
		from platformQueue.dbo.tblQueueItemData as qid
		inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
			and dc.columnname in ('FirmChildSub','FirmChildNoSub')
		where qid.itemUID = tmp.itemUID
	) as idata


	-- get address info for firm accounts
	update tmp 
	set	tmp.memberNumber = m.memberNumber, 
		tmp.firstname = m.firstname, 
		tmp.lastname = m.lastname, 
		tmp.company = m.company, 
		tmp.address1 = ma.address1, 
		tmp.address2 = ma.address2, 
		tmp.address3 = ma.address3, 
		tmp.city = ma.city, 
		tmp.stateProv = s.code, 
		tmp.postalCode = ma.postalCode,
		tmp.country = c.country
	from #tmpTblQueueItems_firmbilling as tmp
	inner join membercentral.dbo.ams_members as m on m.memberID = tmp.memberID
	left outer join membercentral.dbo.ams_memberAddresses as ma on ma.memberID = m.memberID 
		and ma.addressTypeID = m.billingAddressTypeID
	left outer join membercentral.dbo.ams_states as s on s.stateID = ma.stateID
	left outer join membercentral.dbo.ams_countries as c on c.countryID = ma.countryID


	-- get config params for each item
	update tmp
	set tmp.xmlConfigParam = config.configXML
	from #tmpTblQueueItems_firmbilling as tmp
	cross apply (
		select cast(isnull((
			select reportParam, paramvalue	
			from (
				select datakey as reportParam, cast(columnValueString as varchar(max)) as paramvalue
				from platformQueue.dbo.tblQueueItemData qid
				inner join platformQueue.dbo.tblQueueTypeDataColumns dc on dc.columnID = qid.columnID
					and dc.columnname = 'ConfigParam'
				where qid.itemUID = tmp.itemUID
					union all
				select datakey as reportParam, columnValuetext as paramvalue
				from platformQueue.dbo.tblQueueItemData qid
				inner join platformQueue.dbo.tblQueueTypeDataColumns dc on dc.columnID = qid.columnID
					and dc.columnname = 'ConfigText'
				where qid.itemUID = tmp.itemUID
			) as tmp
			for xml path('param'), root('params'), type
		),'<params/>') as xml) as configXML
	) as config


	-- get all firm members
	IF OBJECT_ID('tempdb..#qryAllFirmMembers') IS NOT NULL 
		DROP TABLE #qryAllFirmMembers
	CREATE TABLE #qryAllFirmMembers (itemUID uniqueidentifier, childMemberID int, childMemberNumber varchar(50), 
		prefix varchar(50), firstname varchar(75), lastname varchar(75), company varchar(200), suffix varchar(50),
		address1 varchar(100), address2 varchar(100), address3 varchar(100), city varchar(35), stateProv varchar(4),
				postalCode varchar(25), country varchar(100)
	)
	insert into #qryAllFirmMembers (itemUID, childMemberID)
	select distinct tmp.itemUID, qid.columnValueInteger
	from #tmpTblQueueItems_firmbilling as tmp
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = tmp.itemUID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
		and dc.columnname = 'FirmChildNoSub'
		union
	select distinct tmp.itemUID, m.activeMemberID
	from #tmpTblQueueItems_firmbilling as tmp
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = tmp.itemUID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
		and dc.columnname = 'FirmChildSub'
	inner join membercentral.dbo.sub_subscribers as ss on ss.subscriberID = qid.columnValueInteger
	inner join membercentral.dbo.ams_members as m on m.memberID = ss.memberID

	update fm 
	set childMemberNumber = m.MemberNumber, 
		prefix = m.prefix,
		firstname = m.firstname, 
		lastname = m.lastname, 
		company = m.company,
		suffix = m.suffix,
		address1 = ma.address1, 
		address2 = ma.address2, 
		address3 = ma.address3, 
		city = ma.city, 
		stateProv = s.code, 
		postalCode = ma.postalCode,
		country = c.country

	from #qryAllFirmMembers as fm
	inner join membercentral.dbo.ams_members as m on m.memberID = fm.childMemberID
	left outer join membercentral.dbo.ams_memberAddresses as ma on ma.memberID = m.memberID 
		and ma.addressTypeID = m.billingAddressTypeID
	left outer join membercentral.dbo.ams_states as s on s.stateID = ma.stateID
	left outer join membercentral.dbo.ams_countries as c on c.countryID = ma.countryID


	-- get all subs
	IF OBJECT_ID('tempdb..#qrySubs') IS NOT NULL 
		DROP TABLE #qrySubs
	CREATE TABLE #qrySubs (itemUID uniqueidentifier, subscriberID int, memberID int, subscriptionID int, typeID int,
		typeName varchar(100), subscriptionName varchar(300), rateName varchar(200), frequencyName varchar(50),
		[status] varchar(1), statusName varchar(50), subStartDate datetime, subEndDate datetime, graceEndDate datetime,
		parentSubscriberID int, rootSubscriberID int, lastPrice money, rfid int, thePath varchar(max), 
		subAmount money, subAmountDue money, subAmountPaid money, subscriberPath varchar(200), saleTransactionID int, 
		glaccountID int, invoiceContentVersionID int, invoiceProfileID int, invoiceProfileImageExt varchar(5))

	insert into #qrySubs (itemUID, subscriberID, memberID, subscriptionID, typeID, typeName, subscriptionName, rateName, 
		frequencyName, [status], statusName, subStartDate, subEndDate, graceEndDate, parentSubscriberID, rfid, 
		rootSubscriberID, lastPrice, subscriberPath, glaccountID)
	select tmp.itemUID, ss.subscriberID, m.activeMemberID, ss.subscriptionID, t.typeID, t.typeName, sub.subscriptionName, 
		r.rateName, f.frequencyName, st.statusCode, st.statusName, ss.subStartDate, ss.subEndDate, ss.graceEndDate, 
		ss.parentSubscriberID, ss.rfid, ss.rootSubscriberID, ss.lastprice, ss.subscriberPath, ss.glaccountID
	from #tmpTblQueueItems_firmbilling as tmp
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = tmp.itemUID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
		and dc.columnname = 'FirmChildSub'
	inner join membercentral.dbo.sub_subscribers as rootss on rootss.subscriberID = qid.columnValueInteger
	inner join membercentral.dbo.sub_subscribers as ss on rootss.subscriberID = ss.rootSubscriberID
	inner join membercentral.dbo.sub_subscriptions as sub on sub.subscriptionID = ss.subscriptionID
	inner join membercentral.dbo.sub_types as t on sub.typeiD = t.typeID
	inner join membercentral.dbo.ams_members as m on ss.memberID = m.memberID
	inner join membercentral.dbo.sub_statuses as st on st.statusID = ss.statusID 
		and st.statuscode in ('A','I','O','P')
	inner join membercentral.dbo.sub_rateFrequencies as rf on rf.rfid = ss.rfid
	inner join membercentral.dbo.sub_rates as r on r.rateID = rf.rateID 
	inner join membercentral.dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID

	update qs 
	set saleTransactionID = t.transactionID,
		subAmount = ts.cache_amountAfterAdjustment,
		subAmountDue = ts.cache_amountAfterAdjustment - ts.cache_activePaymentAllocatedAmount,
		subAmountPaid = ts.cache_activePaymentAllocatedAmount,
		invoiceContentVersionID = it.messageContentVersionID
	from #qrySubs as qs
	inner join membercentral.dbo.tr_applications as app on app.ItemID = qs.subscriberID 
		and app.applicationTypeID = 17 
		and app.itemType = 'Dues' 
		and app.status = 'A'
	inner join membercentral.dbo.tr_transactions as t on t.transactionID = app.transactionID
	inner join membercentral.dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID
	inner join membercentral.dbo.tr_invoiceTransactions it on it.transactionID = t.transactionID

	-- update invoiceProfileID and invoice image (billed subs have no transactions to join against)
	update qs 
	set invoiceProfileID = ip.profileID, 
		invoiceProfileImageExt = ip.imageExt
	from #qrySubs as qs
	inner join membercentral.dbo.tr_glaccounts as gl on gl.glaccountID = qs.glaccountID
	inner join membercentral.dbo.tr_invoiceProfiles ip on ip.profileID = gl.invoiceProfileID

	-- update invoiceContentVersionID for subs with no transactions
	update qs 
	set invoiceContentVersionID = cv.contentVersionID
	from #qrySubs as qs
	inner join membercentral.dbo.tr_glaccounts as gl on gl.glaccountID = qs.glaccountID
		and qs.saleTransactionID is null
	inner join membercentral.dbo.cms_content as c on c.contentID = gl.invoiceContentID
	inner join membercentral.dbo.cms_contentLanguages as cl on cl.contentID = c.contentID
	inner join membercentral.dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID

	
	-- get sub tree totals
	IF OBJECT_ID('tempdb..#tblSubTreeTotals') IS NOT NULL 
		DROP TABLE #tblSubTreeTotals
	CREATE TABLE #tblSubTreeTotals (itemUID uniqueidentifier, rootsubscriberID int, tree_subAmount money, 
		tree_subAmountDue money, tree_subAmountPaid money)

	insert into #tblSubTreeTotals (itemUID, rootsubscriberID, tree_subAmount, tree_subAmountDue, tree_subAmountPaid)
	select itemUID, rootSubscriberID, sum(subAmount) as tree_subAmount, sum(subAmountDue) as tree_subAmountDue, sum(subAmountPaid) as tree_subAmountPaid
	from #qrySubs 
	group by itemUID, rootSubscriberID


	-- get sub payments
	IF OBJECT_ID('tempdb..#tblSubPayments') IS NOT NULL 
		DROP TABLE #tblSubPayments
	CREATE TABLE #tblSubPayments (itemUID uniqueidentifier, subscriberID int, rootsubscriberID int, transactionID int, 
		allocatedAmount money, detail varchar(max), depositdate datetime)

	insert into #tblSubPayments (itemUID, subscriberID, rootsubscriberID, transactionID, allocatedAmount, detail, depositdate)
	select s.itemUID, s.subscriberID, s.rootsubscriberID, t.transactionID, st.allocatedAmount, t.detail, b.depositDate
	from #qrySubs as s
	inner join membercentral.dbo.tr_applications as app on app.ItemID = s.subscriberID 
		and app.applicationTypeID = 17 
		and app.itemType = 'Dues' 
		and app.status = 'A'
	CROSS APPLY membercentral.dbo.fn_tr_getAllocatedPaymentsofSale(app.transactionID) AS st
	inner join membercentral.dbo.tr_transactions as t on t.transactionID = st.paymentTransactionID
	inner join membercentral.dbo.tr_batchTransactions as bt on bt.transactionID = t.transactionID
	inner join membercentral.dbo.tr_batches as b on b.batchID = bt.batchID


	-- get sub invoices
	IF OBJECT_ID('tempdb..#tblSubInvoices') IS NOT NULL 
		DROP TABLE #tblSubInvoices
	CREATE TABLE #tblSubInvoices (itemUID uniqueidentifier, subscriberID int, rootsubscriberID int, invoiceID int,
		invoiceCode char(8), datebilled datetime, datedue datetime, statusID int, [status] varchar(50), amount money,
		amountDue money)

	insert into #tblSubInvoices (itemUID, subscriberID, rootsubscriberID, invoiceID, invoiceCode, datebilled, datedue,
		statusID, [status], amount, amountDue)
	select s.itemUID, s.subscriberID, s.rootsubscriberID, i.invoiceID, i.invoiceCode, i.datebilled, i.datedue, ist.statusID, 
		ist.status, sum(st.amount) as amount, sum(st.amountDue) as amountDue 
	from #qrySubs as s
	CROSS APPLY membercentral.dbo.fn_sub_subscriberTransactions(s.subscriberID) AS st 
	inner join membercentral.dbo.tr_invoices as i on i.invoiceID = st.invoiceID
	inner join membercentral.dbo.tr_invoiceStatuses as ist on ist.statusID = i.statusID
		and ist.status <> 'open'
	group by s.itemUID, s.subscriberID, s.rootsubscriberID, i.invoiceID, i.invoiceCode, i.datebilled, i.datedue, 
		ist.statusID, ist.status


	-- populate table var of contentVersions referenced by qrySubs
	declare @invoiceContent table (contentVersionID int PRIMARY KEY, rawcontent varchar(max))
	declare @invoiceContentandSubtrees table (contentVersionID int, rootSubscriberID int, messageNumber int)

	insert into @invoiceContent (contentVersionID, rawcontent)
	select distinct cv.contentVersionID, cv.rawcontent
	from #qrySubs as qs
	inner join membercentral.dbo.cms_contentVersions as cv on cv.contentVersionID = qs.invoiceContentVersionID

	insert into @invoiceContentandSubtrees (contentVersionID, rootSubscriberID, messageNumber)
	select ic.contentVersionID, qs.rootsubscriberID, row_number() over (partition by qs.rootsubscriberID order by min(qs.subscriberPath))
	from #qrySubs as qs
	inner join @invoiceContent as ic on ic.contentVersionID = qs.invoiceContentVersionID
	group by ic.contentVersionID, qs.rootsubscriberID


	-- firms xml
	update tmp
	set tmp.xmlFirms = firms.firmXML
	from #tmpTblQueueItems_firmbilling as tmp
	cross apply (
		select cast(isnull((		
			select company.memberID as '@firmmemberid',
				company.memberNumber as '@firmmembernumber' ,
				company.firstname as '@firmfirstname' ,
				company.lastname as '@firmlastname' ,
				company.company as '@firmcompany' ,
				company.address1 as '@firmaddress1' ,
				company.address2 as '@firmaddress2' ,
				company.address3 as '@firmaddress3' ,
				company.city as '@firmcity' ,
				company.stateProv as '@firmstate' ,
				company.postalCode as '@firmpostalcode',
				company.country as '@firmcountry',
				( select
					account.childmemberid as '@childmemberID',
					account.childMemberNumber as '@childmembernumber',
					isnull(company.memberID,0) as '@firmmemberid',
					case when exists (select * from #qrySubs as st2 where st2.memberID = account.childmemberid and st2.itemUID = tmp.itemUID) then 1 else 0 end as '@hassubscription',
					account.firstname as '@firstname', 
					account.lastname as '@lastname',
					account.prefix as '@prefix',
					account.suffix as '@suffix',
					account.lastname + ', ' + account.firstname + ' (' + account.childMemberNumber + ')' as '@namestring',
					account.company as '@company',
					account.address1 as '@address1' ,
					account.address2 as '@address2' ,
					account.address3 as '@address3' ,
					account.city as '@city' ,
					account.stateProv as '@state' ,
					account.postalCode as '@postalcode',
					account.country as '@country',

					( select
						subscriptionTree.rootsubscriberid as '@rootsubscriberid',
						case when subscriptionTree.rootsubscriberid is null then null else account.childmemberid end as '@activeMemberID',
						qst.tree_subAmount as '@subamount_total',
						qst.tree_subAmountPaid as '@subamountpaid_total',
						qst.tree_subAmountDue as '@subamountdue_total',
						CONVERT(VARCHAR(8),subscriptionTree.substartdate, 1) + ' - ' + CONVERT(VARCHAR(8),subscriptionTree.subenddate, 1) as '@subtreedaterange',
						cast(subscriptionTree.invoiceProfileID as varchar(10)) + '.' + subscriptionTree.invoiceProfileImageExt as '@subtreeinvoiceprofileimage',
						( select
							subscriber.subscriberid as '@subscriberID',
							account.childmemberid as '@activeMemberID',
							subscriber.subscriptionid as '@subscriptionid',
							subscriber.typeid as '@typeid',
							subscriber.typename as '@typename',
							subscriber.subscriptionname as '@subscriptionname',
							subscriber.ratename as '@ratename',
							subscriber.frequencyname as '@frequencyname',
							subscriber.status as '@status',
							subscriber.statusname as '@statusname',
							subscriber.substartdate as '@substartdate',
							subscriber.subenddate as '@subenddate',
							CONVERT(VARCHAR(8),subscriber.substartdate, 1) + ' - ' + CONVERT(VARCHAR(8),subscriber.subenddate, 1) as '@subdaterange',
							subscriber.graceenddate as '@graceenddate',
							subscriber.parentsubscriberid as '@parentsubscriberid',
							subscriber.rootsubscriberid as '@rootsubscriberid',
							subscriber.rfid as '@rfid',
							subscriber.thepath as '@thepath',
							subscriber.lastprice as '@lastprice',
							subscriber.subAmount as '@subamount',
							subscriber.subAmountPaid as '@subamountpaid',
							subscriber.subAmountDue as '@subamountdue',
							subscriber.subscriberPath as '@subscriberpath',
							subscriber.invoiceContentVersionID as '@invoicecontentversionid',
							icst.messageNumber as '@invoicecontentfootnotenumber'
						from #qrySubs as subscriber
						left outer join @invoiceContentandSubtrees as icst on icst.rootsubscriberID = subscriber.rootsubscriberID
							and subscriber.invoiceContentVersionID = icst.contentVersionID
						where subscriber.itemUID = tmp.itemUID
						and subscriber.rootSubscriberID = subscriptionTree.subscriberID
						order by subscriber.subscriberPath
						for xml path('subscriber'), type
						),
						( select
							rootsubscriberID as '@rootsubscriberID',
							invoiceID as '@invoiceid',
							invoiceCode as '@invoicecode',
							datebilled as '@datebilled',
							datedue as '@datedue',
							statusID as '@statusid',
							[status] as '@status',
							sum(amount) as '@amount',
							sum(amountDue) as '@amountDue'
						from #tblSubInvoices as si
						where si.itemUID = tmp.itemUID
						and si.rootSubscriberID = subscriptionTree.rootsubscriberID
						group by rootsubscriberID, invoiceID, invoiceCode, datebilled, datedue, statusID, [status]
						order by datedue
						for xml path ('invoice'),root('invoices'), type
						),
						( select
							rootsubscriberID as '@rootsubscriberID',
							transactionID as '@transactionID',
							depositdate as '@datebilled',
							detail as '@detail',
							sum(allocatedAmount) as '@allocatedAmount'
						from #tblSubPayments sp
						where sp.itemUID = tmp.itemUID
						and sp.rootSubscriberID = subscriptionTree.rootsubscriberID
						group by rootsubscriberID ,transactionID ,detail,depositdate
						order by depositdate
						for xml path('payment'), root('payments'), type
						),
						( select
							subscriber.rootSubscriberID as '@rootsubscriberID',
							icst.messageNumber as '@invoicecontentfootnotenumber',
							ic.contentversionid as '@contentversionid',
							ic.rawcontent as '@rawcontent'
						from #qrySubs as subscriber
						inner join @invoiceContent as ic on ic.contentVersionID = subscriber.invoiceContentVersionID
							and subscriber.itemUID = tmp.itemUID
							and subscriber.rootSubscriberID = subscriptionTree.subscriberID
						inner join @invoiceContentandSubtrees as icst on icst.rootsubscriberID = subscriber.rootsubscriberID
							and icst.contentVersionID = ic.contentVersionID
						group by subscriber.rootSubscriberID, icst.messageNumber, ic.contentversionid, ic.rawcontent
						order by icst.messageNumber
						for xml path('invoicemessage'), root('invoicemessages'), type
						)
						from #qrySubs as subscriptionTree
						inner join #tblSubTreeTotals as qst on qst.rootSubscriberID = subscriptionTree.rootSubscriberID
						where subscriptionTree.itemUID = tmp.itemUID
						and subscriptionTree.memberID = account.childMemberID
						and subscriptionTree.subscriberID = subscriptionTree.rootsubscriberID
						order by subscriptionTree.rootsubscriberid
						for xml path('subscriptiontree'), type
					)
					from #qryAllFirmMembers as account
					where account.itemUID = company.itemUID
					order by account.lastname, account.firstname, account.childMemberID
					for xml path('account'), type
				)
			from #tmpTblQueueItems_firmbilling as company
			where company.itemUID = tmp.itemUID
			order by company.company, company.memberID
			for xml path('company'), root('companies'), type
		),'<companies/>') as xml) as firmXML
	) as firms


	-- final data
	select itemUID, itemGroupUID, jobUID, memberNumber, company, xmlConfigParam, xmlFirms
	from #tmpTblQueueItems_firmbilling
	order by itemUID


	IF OBJECT_ID('tempdb..#tblSubInvoices') IS NOT NULL 
		DROP TABLE #tblSubInvoices
	IF OBJECT_ID('tempdb..#tblSubPayments') IS NOT NULL 
		DROP TABLE #tblSubPayments
	IF OBJECT_ID('tempdb..#tblSubTreeTotals') IS NOT NULL 
		DROP TABLE #tblSubTreeTotals
	IF OBJECT_ID('tempdb..#qrySubs') IS NOT NULL 
		DROP TABLE #qrySubs
	IF OBJECT_ID('tempdb..#qryAllFirmMembers') IS NOT NULL 
		DROP TABLE #qryAllFirmMembers
	IF OBJECT_ID('tempdb..#tmpTblQueueItems_firmbilling') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_firmbilling

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[job_firmSubStatements_grabForProcessing]
@serverID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	DECLARE @batchSize int
	set @batchSize = 50

	declare @statusReady int, @statusGrabbed int
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'FirmSubStatements'
		and qs.queueStatus = 'readyToProcess'
	select @statusGrabbed = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'FirmSubStatements'
		and qs.queueStatus = 'grabbedForProcessing'

	declare @jobUID uniqueidentifier
	set @jobUID = NEWID()


	-- dequeue in order of dateAdded. get @batchsize firms
	IF OBJECT_ID('tempdb..#tmpTblQueueItems_firmbilling') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_firmbilling
	CREATE TABLE #tmpTblQueueItems_firmbilling (itemUID uniqueidentifier, itemGroupUID uniqueidentifier, 
		jobUID uniqueidentifier, recordedByMemberID int, siteID int, memberID int, memberNumber varchar(50), 
		firstname varchar(75), lastname varchar(75), company varchar(200), address1 varchar(100), 
		address2 varchar(100), address3 varchar(100), city varchar(35), stateProv varchar(4),
		postalCode varchar(25), country varchar(100), xmlConfigParam xml, xmlFirms xml)

	update qi WITH (UPDLOCK, READPAST)
	set qi.queueStatusID = @statusGrabbed,
		qi.dateUpdated = getdate(),
		qi.jobUID = @jobUID,
		qi.jobDateStarted = getdate(),
		qi.jobServerID = @serverID
		OUTPUT inserted.itemUID, null, inserted.jobUID, null, null, null, null, null, null, null, 
			null, null, null, null, null, null, null, null, null
		INTO #tmpTblQueueItems_firmbilling
	from platformQueue.dbo.tblQueueItems as qi
	inner join (
		select top(@BatchSize) qi2.itemUID 
		from platformQueue.dbo.tblQueueItems as qi2
		where qi2.queueStatusID = @statusReady
		order by qi2.dateAdded, qi2.itemUID
		) as batch on batch.itemUID = qi.itemUID
	where qi.queueStatusID = @statusReady


	-- get memberID, itemGroupUID, siteID, recordedByMemberID from item data
	update tmp
	set tmp.memberID = idata.memberID,
		tmp.itemGroupUID = idata.itemGroupUID,
		tmp.recordedByMemberID = idata.recordedByMemberID,
		tmp.siteID = idata.siteID
	from #tmpTblQueueItems_firmbilling as tmp
	cross apply (
		select min(cast(dataKey as int)) as memberID, min(cast(itemGroupUID as varchar(60))) as itemGroupUID, 
			min(recordedByMemberID) as recordedByMemberID, min(siteID) as siteID
		from platformQueue.dbo.tblQueueItemData as qid
		inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
			and dc.columnname in ('FirmChildSub','FirmChildNoSub')
		where qid.itemUID = tmp.itemUID
	) as idata


	-- get address info for firm accounts
	update tmp 
	set	tmp.memberNumber = m.memberNumber, 
		tmp.firstname = m.firstname, 
		tmp.lastname = m.lastname, 
		tmp.company = m.company, 
		tmp.address1 = ma.address1, 
		tmp.address2 = ma.address2, 
		tmp.address3 = ma.address3, 
		tmp.city = ma.city, 
		tmp.stateProv = s.code, 
		tmp.postalCode = ma.postalCode,
		tmp.country = c.country
	from #tmpTblQueueItems_firmbilling as tmp
	inner join membercentral.dbo.ams_members as m on m.memberID = tmp.memberID
	left outer join membercentral.dbo.ams_memberAddresses as ma on ma.memberID = m.memberID 
		and ma.addressTypeID = m.billingAddressTypeID
	left outer join membercentral.dbo.ams_states as s on s.stateID = ma.stateID
	left outer join membercentral.dbo.ams_countries as c on c.countryID = ma.countryID


	-- get config params for each item
	update tmp
	set tmp.xmlConfigParam = config.configXML
	from #tmpTblQueueItems_firmbilling as tmp
	cross apply (
		select cast(isnull((
			select reportParam, paramvalue	
			from (
				select datakey as reportParam, cast(columnValueString as varchar(max)) as paramvalue
				from platformQueue.dbo.tblQueueItemData qid
				inner join platformQueue.dbo.tblQueueTypeDataColumns dc on dc.columnID = qid.columnID
					and dc.columnname = 'ConfigParam'
				where qid.itemUID = tmp.itemUID
					union all
				select datakey as reportParam, columnValuetext as paramvalue
				from platformQueue.dbo.tblQueueItemData qid
				inner join platformQueue.dbo.tblQueueTypeDataColumns dc on dc.columnID = qid.columnID
					and dc.columnname = 'ConfigText'
				where qid.itemUID = tmp.itemUID
			) as tmp
			for xml path('param'), root('params'), type
		),'<params/>') as xml) as configXML
	) as config


	-- get all firm members
	IF OBJECT_ID('tempdb..#qryAllFirmMembers') IS NOT NULL 
		DROP TABLE #qryAllFirmMembers
	CREATE TABLE #qryAllFirmMembers (itemUID uniqueidentifier, childMemberID int, childMemberNumber varchar(50), 
		prefix varchar(50), firstname varchar(75), lastname varchar(75), company varchar(200), suffix varchar(50),
		address1 varchar(100), address2 varchar(100), address3 varchar(100), city varchar(35), stateProv varchar(4),
				postalCode varchar(25), country varchar(100)
	)
	insert into #qryAllFirmMembers (itemUID, childMemberID)
	select distinct tmp.itemUID, qid.columnValueInteger
	from #tmpTblQueueItems_firmbilling as tmp
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = tmp.itemUID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
		and dc.columnname = 'FirmChildNoSub'
		union
	select distinct tmp.itemUID, m.activeMemberID
	from #tmpTblQueueItems_firmbilling as tmp
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = tmp.itemUID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
		and dc.columnname = 'FirmChildSub'
	inner join membercentral.dbo.sub_subscribers as ss on ss.subscriberID = qid.columnValueInteger
	inner join membercentral.dbo.ams_members as m on m.memberID = ss.memberID

	update fm 
	set childMemberNumber = m.MemberNumber, 
		prefix = m.prefix,
		firstname = m.firstname, 
		lastname = m.lastname, 
		company = m.company,
		suffix = m.suffix,
		address1 = ma.address1, 
		address2 = ma.address2, 
		address3 = ma.address3, 
		city = ma.city, 
		stateProv = s.code, 
		postalCode = ma.postalCode,
		country = c.country

	from #qryAllFirmMembers as fm
	inner join membercentral.dbo.ams_members as m on m.memberID = fm.childMemberID
	left outer join membercentral.dbo.ams_memberAddresses as ma on ma.memberID = m.memberID 
		and ma.addressTypeID = m.billingAddressTypeID
	left outer join membercentral.dbo.ams_states as s on s.stateID = ma.stateID
	left outer join membercentral.dbo.ams_countries as c on c.countryID = ma.countryID


	-- get all subs
	IF OBJECT_ID('tempdb..#qrySubs') IS NOT NULL 
		DROP TABLE #qrySubs
	CREATE TABLE #qrySubs (itemUID uniqueidentifier, subscriberID int, memberID int, subscriptionID int, typeID int,
		typeName varchar(100), subscriptionName varchar(300), rateName varchar(200), frequencyName varchar(50),
		[status] varchar(1), statusName varchar(50), subStartDate datetime, subEndDate datetime, graceEndDate datetime,
		parentSubscriberID int, rootSubscriberID int, lastPrice money, rfid int, thePath varchar(max), 
		subAmount money, subAmountDue money, subAmountPaid money, subscriberPath varchar(200), saleTransactionID int, 
		glaccountID int, invoiceContentVersionID int, invoiceProfileID int, invoiceProfileImageExt varchar(5))

	insert into #qrySubs (itemUID, subscriberID, memberID, subscriptionID, typeID, typeName, subscriptionName, rateName, 
		frequencyName, [status], statusName, subStartDate, subEndDate, graceEndDate, parentSubscriberID, rfid, 
		rootSubscriberID, lastPrice, subscriberPath, glaccountID)
	select tmp.itemUID, ss.subscriberID, m.activeMemberID, ss.subscriptionID, t.typeID, t.typeName, sub.subscriptionName, 
		r.rateName, f.frequencyName, st.statusCode, st.statusName, ss.subStartDate, ss.subEndDate, ss.graceEndDate, 
		ss.parentSubscriberID, ss.rfid, ss.rootSubscriberID, ss.lastprice, ss.subscriberPath, ss.glaccountID
	from #tmpTblQueueItems_firmbilling as tmp
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = tmp.itemUID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
		and dc.columnname = 'FirmChildSub'
	inner join membercentral.dbo.sub_subscribers as rootss on rootss.subscriberID = qid.columnValueInteger
	inner join membercentral.dbo.sub_subscribers as ss on rootss.subscriberID = ss.rootSubscriberID
	inner join membercentral.dbo.sub_subscriptions as sub on sub.subscriptionID = ss.subscriptionID
	inner join membercentral.dbo.sub_types as t on sub.typeiD = t.typeID
	inner join membercentral.dbo.ams_members as m on ss.memberID = m.memberID
	inner join membercentral.dbo.sub_statuses as st on st.statusID = ss.statusID 
		and st.statuscode in ('A','I','O','P')
	inner join membercentral.dbo.sub_rateFrequencies as rf on rf.rfid = ss.rfid
	inner join membercentral.dbo.sub_rates as r on r.rateID = rf.rateID 
	inner join membercentral.dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID

	update qs 
	set saleTransactionID = t.transactionID,
		subAmount = ts.cache_amountAfterAdjustment,
		subAmountDue = ts.cache_amountAfterAdjustment - ts.cache_activePaymentAllocatedAmount,
		subAmountPaid = ts.cache_activePaymentAllocatedAmount,
		invoiceContentVersionID = it.messageContentVersionID
	from #qrySubs as qs
	inner join membercentral.dbo.tr_applications as app on app.ItemID = qs.subscriberID 
		and app.applicationTypeID = 17 
		and app.itemType = 'Dues' 
		and app.status = 'A'
	inner join membercentral.dbo.tr_transactions as t on t.transactionID = app.transactionID
	inner join membercentral.dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID
	inner join membercentral.dbo.tr_invoiceTransactions it on it.transactionID = t.transactionID

	-- update invoiceProfileID and invoice image (billed subs have no transactions to join against)
	update qs 
	set invoiceProfileID = ip.profileID, 
		invoiceProfileImageExt = ip.imageExt
	from #qrySubs as qs
	inner join membercentral.dbo.tr_glaccounts as gl on gl.glaccountID = qs.glaccountID
	inner join membercentral.dbo.tr_invoiceProfiles ip on ip.profileID = gl.invoiceProfileID

	-- update invoiceContentVersionID for subs with no transactions
	update qs 
	set invoiceContentVersionID = cv.contentVersionID
	from #qrySubs as qs
	inner join membercentral.dbo.tr_glaccounts as gl on gl.glaccountID = qs.glaccountID
		and qs.saleTransactionID is null
	inner join membercentral.dbo.cms_content as c on c.contentID = gl.invoiceContentID
	inner join membercentral.dbo.cms_contentLanguages as cl on cl.contentID = c.contentID
	inner join membercentral.dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID

	
	-- get sub tree totals
	IF OBJECT_ID('tempdb..#tblSubTreeTotals') IS NOT NULL 
		DROP TABLE #tblSubTreeTotals
	CREATE TABLE #tblSubTreeTotals (itemUID uniqueidentifier, rootsubscriberID int, tree_subAmount money, 
		tree_subAmountDue money, tree_subAmountPaid money)

	insert into #tblSubTreeTotals (itemUID, rootsubscriberID, tree_subAmount, tree_subAmountDue, tree_subAmountPaid)
	select itemUID, rootSubscriberID, sum(subAmount) as tree_subAmount, sum(subAmountDue) as tree_subAmountDue, sum(subAmountPaid) as tree_subAmountPaid
	from #qrySubs 
	group by itemUID, rootSubscriberID


	-- get sub payments
	IF OBJECT_ID('tempdb..#tblSubPayments') IS NOT NULL 
		DROP TABLE #tblSubPayments
	CREATE TABLE #tblSubPayments (itemUID uniqueidentifier, subscriberID int, rootsubscriberID int, transactionID int, 
		allocatedAmount money, detail varchar(max), depositdate datetime)

	insert into #tblSubPayments (itemUID, subscriberID, rootsubscriberID, transactionID, allocatedAmount, detail, depositdate)
	select s.itemUID, s.subscriberID, s.rootsubscriberID, t.transactionID, st.allocatedAmount, t.detail, b.depositDate
	from #qrySubs as s
	inner join membercentral.dbo.tr_applications as app on app.ItemID = s.subscriberID 
		and app.applicationTypeID = 17 
		and app.itemType = 'Dues' 
		and app.status = 'A'
	CROSS APPLY membercentral.dbo.fn_tr_getAllocatedPaymentsofSale(app.transactionID) AS st
	inner join membercentral.dbo.tr_transactions as t on t.transactionID = st.paymentTransactionID
	inner join membercentral.dbo.tr_batchTransactions as bt on bt.transactionID = t.transactionID
	inner join membercentral.dbo.tr_batches as b on b.batchID = bt.batchID


	-- get sub invoices
	IF OBJECT_ID('tempdb..#tblSubInvoices') IS NOT NULL 
		DROP TABLE #tblSubInvoices
	CREATE TABLE #tblSubInvoices (itemUID uniqueidentifier, subscriberID int, rootsubscriberID int, invoiceID int,
		invoiceCode char(8), datebilled datetime, datedue datetime, statusID int, [status] varchar(50), amount money,
		amountDue money, payProfileDesc varchar(100))

	insert into #tblSubInvoices (itemUID, subscriberID, rootsubscriberID, invoiceID, invoiceCode, datebilled, datedue,
		statusID, [status], amount, amountDue,payProfileDesc)
	select s.itemUID, s.subscriberID, s.rootsubscriberID, i.invoiceID, i.invoiceCode, i.datebilled, i.datedue, ist.statusID, 
		ist.status, sum(st.amount) as amount, sum(st.amountDue) as amountDue,
		payProfileDesc = g.gatewayclass + ' - (' + mpp.detail + ')'
	from #qrySubs as s
	CROSS APPLY membercentral.dbo.fn_sub_subscriberTransactions(s.subscriberID) AS st 
	inner join membercentral.dbo.tr_invoices as i on i.invoiceID = st.invoiceID
	inner join membercentral.dbo.tr_invoiceStatuses as ist on ist.statusID = i.statusID
		and ist.status <> 'open'
	left outer join membercentral.dbo.ams_memberPaymentProfiles mpp
		inner join membercentral.dbo.mp_profiles mp
			on mp.profileID = mpp.profileID
		inner join membercentral.dbo.mp_gateways g
			on g.gatewayID = mp.gatewayID
	on mpp.payProfileID = i.payProfileID
	group by s.itemUID, s.subscriberID, s.rootsubscriberID, i.invoiceID, i.invoiceCode, i.datebilled, i.datedue, 
		ist.statusID, ist.status, g.gatewayclass, mpp.detail


	-- populate table var of contentVersions referenced by qrySubs
	declare @invoiceContent table (contentVersionID int PRIMARY KEY, rawcontent varchar(max))
	declare @invoiceContentandSubtrees table (contentVersionID int, rootSubscriberID int, messageNumber int)

	insert into @invoiceContent (contentVersionID, rawcontent)
	select distinct cv.contentVersionID, cv.rawcontent
	from #qrySubs as qs
	inner join membercentral.dbo.cms_contentVersions as cv on cv.contentVersionID = qs.invoiceContentVersionID

	insert into @invoiceContentandSubtrees (contentVersionID, rootSubscriberID, messageNumber)
	select ic.contentVersionID, qs.rootsubscriberID, row_number() over (partition by qs.rootsubscriberID order by min(qs.subscriberPath))
	from #qrySubs as qs
	inner join @invoiceContent as ic on ic.contentVersionID = qs.invoiceContentVersionID
	group by ic.contentVersionID, qs.rootsubscriberID


	-- firms xml
	update tmp
	set tmp.xmlFirms = firms.firmXML
	from #tmpTblQueueItems_firmbilling as tmp
	cross apply (
		select cast(isnull((		
			select company.memberID as '@firmmemberid',
				company.memberNumber as '@firmmembernumber' ,
				company.firstname as '@firmfirstname' ,
				company.lastname as '@firmlastname' ,
				company.company as '@firmcompany' ,
				company.address1 as '@firmaddress1' ,
				company.address2 as '@firmaddress2' ,
				company.address3 as '@firmaddress3' ,
				company.city as '@firmcity' ,
				company.stateProv as '@firmstate' ,
				company.postalCode as '@firmpostalcode',
				company.country as '@firmcountry',
				( select
					account.childmemberid as '@childmemberID',
					account.childMemberNumber as '@childmembernumber',
					isnull(company.memberID,0) as '@firmmemberid',
					case when exists (select * from #qrySubs as st2 where st2.memberID = account.childmemberid and st2.itemUID = tmp.itemUID) then 1 else 0 end as '@hassubscription',
					account.firstname as '@firstname', 
					account.lastname as '@lastname',
					account.prefix as '@prefix',
					account.suffix as '@suffix',
					account.lastname + ', ' + account.firstname + ' (' + account.childMemberNumber + ')' as '@namestring',
					account.company as '@company',
					account.address1 as '@address1' ,
					account.address2 as '@address2' ,
					account.address3 as '@address3' ,
					account.city as '@city' ,
					account.stateProv as '@state' ,
					account.postalCode as '@postalcode',
					account.country as '@country',

					( select
						subscriptionTree.rootsubscriberid as '@rootsubscriberid',
						case when subscriptionTree.rootsubscriberid is null then null else account.childmemberid end as '@activeMemberID',
						case when subscriptionTree.rootsubscriberid is null then null else account.lastname + ', ' + account.firstname + ' (' + account.childMemberNumber + ')' end as '@namestring',
						subscriptionTree.subscriptionname as '@subscriptionname',
						qst.tree_subAmount as '@subamount_total',
						qst.tree_subAmountPaid as '@subamountpaid_total',
						qst.tree_subAmountDue as '@subamountdue_total',
						CONVERT(VARCHAR(8),subscriptionTree.substartdate, 1) + ' - ' + CONVERT(VARCHAR(8),subscriptionTree.subenddate, 1) as '@subtreedaterange',
						cast(subscriptionTree.invoiceProfileID as varchar(10)) + '.' + subscriptionTree.invoiceProfileImageExt as '@subtreeinvoiceprofileimage',
						( select
							subscriber.subscriberid as '@subscriberID',
							account.childmemberid as '@activeMemberID',
							subscriber.subscriptionid as '@subscriptionid',
							subscriber.typeid as '@typeid',
							subscriber.typename as '@typename',
							subscriber.subscriptionname as '@subscriptionname',
							subscriber.ratename as '@ratename',
							subscriber.frequencyname as '@frequencyname',
							subscriber.status as '@status',
							subscriber.statusname as '@statusname',
							subscriber.substartdate as '@substartdate',
							subscriber.subenddate as '@subenddate',
							CONVERT(VARCHAR(8),subscriber.substartdate, 1) + ' - ' + CONVERT(VARCHAR(8),subscriber.subenddate, 1) as '@subdaterange',
							subscriber.graceenddate as '@graceenddate',
							subscriber.parentsubscriberid as '@parentsubscriberid',
							subscriber.rootsubscriberid as '@rootsubscriberid',
							subscriber.rfid as '@rfid',
							subscriber.thepath as '@thepath',
							subscriber.lastprice as '@lastprice',
							subscriber.subAmount as '@subamount',
							subscriber.subAmountPaid as '@subamountpaid',
							subscriber.subAmountDue as '@subamountdue',
							subscriber.subscriberPath as '@subscriberpath',
							subscriber.invoiceContentVersionID as '@invoicecontentversionid',
							icst.messageNumber as '@invoicecontentfootnotenumber'
						from #qrySubs as subscriber
						left outer join @invoiceContentandSubtrees as icst on icst.rootsubscriberID = subscriber.rootsubscriberID
							and subscriber.invoiceContentVersionID = icst.contentVersionID
						where subscriber.itemUID = tmp.itemUID
						and subscriber.rootSubscriberID = subscriptionTree.subscriberID
						order by subscriber.subscriberPath
						for xml path('subscriber'), type
						),
						( select
							rootsubscriberID as '@rootsubscriberID',
							invoiceID as '@invoiceid',
							invoiceCode as '@invoicecode',
							datebilled as '@datebilled',
							datedue as '@datedue',
							statusID as '@statusid',
							[status] as '@status',
							sum(amount) as '@amount',
							sum(amountDue) as '@amountDue',
							isnull(payProfileDesc,'') as '@payprofiledesc'
						from #tblSubInvoices as si
						where si.itemUID = tmp.itemUID
						and si.rootSubscriberID = subscriptionTree.rootsubscriberID
						group by rootsubscriberID, invoiceID, invoiceCode, datebilled, datedue, statusID, [status], payProfileDesc
						order by datedue
						for xml path ('invoice'),root('invoices'), type
						),
						( select
							rootsubscriberID as '@rootsubscriberID',
							transactionID as '@transactionID',
							depositdate as '@datebilled',
							detail as '@detail',
							sum(allocatedAmount) as '@allocatedAmount'
						from #tblSubPayments sp
						where sp.itemUID = tmp.itemUID
						and sp.rootSubscriberID = subscriptionTree.rootsubscriberID
						group by rootsubscriberID ,transactionID ,detail,depositdate
						order by depositdate
						for xml path('payment'), root('payments'), type
						),
						( select
							subscriber.rootSubscriberID as '@rootsubscriberID',
							icst.messageNumber as '@invoicecontentfootnotenumber',
							ic.contentversionid as '@contentversionid',
							ic.rawcontent as '@rawcontent'
						from #qrySubs as subscriber
						inner join @invoiceContent as ic on ic.contentVersionID = subscriber.invoiceContentVersionID
							and subscriber.itemUID = tmp.itemUID
							and subscriber.rootSubscriberID = subscriptionTree.subscriberID
						inner join @invoiceContentandSubtrees as icst on icst.rootsubscriberID = subscriber.rootsubscriberID
							and icst.contentVersionID = ic.contentVersionID
						group by subscriber.rootSubscriberID, icst.messageNumber, ic.contentversionid, ic.rawcontent
						order by icst.messageNumber
						for xml path('invoicemessage'), root('invoicemessages'), type
						)
						from #qrySubs as subscriptionTree
						inner join #tblSubTreeTotals as qst on qst.rootSubscriberID = subscriptionTree.rootSubscriberID
						where subscriptionTree.itemUID = tmp.itemUID
						and subscriptionTree.memberID = account.childMemberID
						and subscriptionTree.subscriberID = subscriptionTree.rootsubscriberID
						order by subscriptionTree.rootsubscriberid
						for xml path('subscriptiontree'), type
					)
					from #qryAllFirmMembers as account
					where account.itemUID = company.itemUID
					order by account.lastname, account.firstname, account.childMemberID
					for xml path('account'), type
				)
			from #tmpTblQueueItems_firmbilling as company
			where company.itemUID = tmp.itemUID
			order by company.company, company.memberID
			for xml path('company'), root('companies'), type
		),'<companies/>') as xml) as firmXML
	) as firms


	-- final data
	select itemUID, itemGroupUID, jobUID, memberNumber, company, xmlConfigParam, xmlFirms
	from #tmpTblQueueItems_firmbilling
	order by itemUID


	IF OBJECT_ID('tempdb..#tblSubInvoices') IS NOT NULL 
		DROP TABLE #tblSubInvoices
	IF OBJECT_ID('tempdb..#tblSubPayments') IS NOT NULL 
		DROP TABLE #tblSubPayments
	IF OBJECT_ID('tempdb..#tblSubTreeTotals') IS NOT NULL 
		DROP TABLE #tblSubTreeTotals
	IF OBJECT_ID('tempdb..#qrySubs') IS NOT NULL 
		DROP TABLE #qrySubs
	IF OBJECT_ID('tempdb..#qryAllFirmMembers') IS NOT NULL 
		DROP TABLE #qryAllFirmMembers
	IF OBJECT_ID('tempdb..#tmpTblQueueItems_firmbilling') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_firmbilling

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH
