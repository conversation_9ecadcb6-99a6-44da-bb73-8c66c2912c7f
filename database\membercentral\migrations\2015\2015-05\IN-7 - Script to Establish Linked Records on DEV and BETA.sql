use membercentral
GO

declare
	@orgID int,
	@siteID int

select @orgID = orgID, @siteID = siteID from sites where siteCode = 'IN'

print 'orgid = ' + convert(varchar(20), @orgid)

select 
	tmp.company, 
	vw.Office_address1, 
	vw.Office_address2, 
	vw.Office_address3,
	vw.Office_city, 
	s.stateID, 
	vw.Office_postalCode, 
	vw.Office_country, 
	vw.[Contact Type],
	ROW_NUMBER() OVER (ORDER BY tmp.company) as row
into datatransfer.dbo.tmp_INFirms
from (
	select 
		m.company, min(m.memberID) as memberID
	from 
		membercentral.dbo.ams_members as m
		inner join membercentral.dbo.vw_memberData_IN as vw2 on 
			vw2.memberid = m.memberid
	where 
		(company not in ('Attorney at Law','n/a') and len(m.company) > 0)
	group by m.company
	having count(*) > 1
) as tmp
inner join membercentral.dbo.vw_memberData_IN as vw on 
	vw.memberid = tmp.memberid
left outer join membercentral.dbo.ams_states as s on 
	s.code = vw.Office_stateprov 
	and s.countryID = 1

GO

ALTER TABLE datatransfer.dbo.tmp_INFirms ADD memberid int NULL
GO

declare @orgid int
select @orgid = orgid from sites where sitecode='IN'

declare @companyRTID int, @IndivRTID int, @FirmMemberRTRTID int, @FirmRelTID int
select 
	@companyRTID = recordTypeID 
from 
	dbo.ams_recordTypes 
where 
	orgID = @orgid and recordTypeCode = 'LawFirm'

select 
	@IndivRTID = recordTypeID 
from 
	dbo.ams_recordTypes 
where 
	orgID = @orgid 
	and recordTypeCode = 'Individual'

select 
	@FirmRelTID = relationshipTypeID 
from 
	dbo.ams_recordRelationshipTypes 
where 
	orgID = @orgid 
	and relationshipTypeCode = 'FirmMember'

select 
	@FirmMemberRTRTID = recordTypeRelationshipTypeID 
from 
	dbo.ams_recordTypesRelationshipTypes 
where 
	masterRecordTypeID = @companyRTID 
	and childRecordTypeID = @IndivRTID 
	and relationshipTypeID = @FirmRelTID


-- clear recordTypeID 
update ams_members set recordTypeid = null where orgID = @orgID
-- clear releationships 
delete from ams_recordRelationships 
where recordTypeRelationshipTypeID in (@FirmMemberRTRTID)


BEGIN TRAN

declare 
	@row int, @firstname varchar(75), @company varchar(300), 
	@ct varchar(200), @membernumber varchar(40), @memberID int, 
	@rc int, @recordTypeID int, @RTID int

select @row = min(row) from datatransfer.dbo.tmp_INFirms 
while @row is not null BEGIN
	select	
		@memberID = null, @ct = null, @firstname = null, 
		@company = null, @membernumber = null, 
		@RTID = null, @recordTypeID = null

	select 
		@company = company, 
		@ct = [Contact Type]
	from 
		datatransfer.dbo.tmp_INFirms
	where 
		row = @row

	select @firstname = 'Firm'
	select @membernumber = 'FIRM' + RIGHT('00000' + cast(@row as varchar(3)),5)
	select @recordTypeID = @companyRTID
	select @RTID = @FirmMemberRTRTID

	print 'company = ' + @company
	print 'contact type = ' + isNull(@ct, 'blank')
	print @firstname
	print @membernumber
	print @recordTypeID
	print @RTID
	
	EXEC @rc = dbo.ams_createMember 
				@orgID=@orgid, 
				@memberTypeID=2, 
				@prefix='', 
				@firstname=@firstname, 
				@middlename='', 
				@lastname='Account', 
				@suffix='', 
				@professionalsuffix='', 
				@company=@company, 
				@memberNumber=@membernumber, 
				@status='A', 
				@memberID=@memberID OUTPUT
		IF @@ERROR <> 0 or @rc <> 0 or @memberID = 0 goto on_error

	UPDATE 
		ams_members
	SET 
		recordTypeID = @recordTypeID
	where 
		memberID = @memberID
	IF @@ERROR <> 0 goto on_error

	UPDATE 
		datatransfer.dbo.tmp_INFirms
	set 
		memberID = @memberID
	where 
		row = @row
	IF @@ERROR <> 0 goto on_error

	UPDATE 
		ams_members
	SET 
		recordTypeID = @IndivRTID
	where 
		orgID = @orgID
		and memberid = activeMemberID
		and status <> 'D'
		and company = @company
		and recordTypeID is null
	IF @@ERROR <> 0 goto on_error

	-- add only if contact type is Attorney 
	INSERT INTO dbo.ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive)
	select 
		@RTID, @memberID, m.memberid, 1
	from 
		membercentral.dbo.ams_members m
		inner join membercentral.dbo.vw_memberData_IN as vw2 on 
			vw2.memberid = m.memberid
			and vw2.[Contact Type] = 'Attorney'
	where 
		m.orgID = @orgID
		and m.memberid = m.activeMemberID
		and m.status <> 'D'
		and m.company = @company
		and m.memberID <> @memberID
	IF @@ERROR <> 0 goto on_error

	select @row = min(row) from datatransfer.dbo.tmp_INFirms where row > @row 
END

declare @addressTypeID int
select @addressTypeID = addressTypeID from dbo.ams_memberAddressTypes where orgID = @orgID and addressTypeOrder = 1

insert into dbo.ams_memberAddresses (memberID, addressTypeID, address1, address2, address3, city, stateID, postalCode, countryID)
select 
	memberID, 
	@addressTypeID, 
	isnull(Office_address1,''), 
	isnull(Office_address2,''), 
	isnull(Office_address3,''), 
	isnull(Office_city,''), 
	stateID, 
	isnull(Office_postalCode,''), 
	1
from 
	datatransfer.dbo.tmp_INFirms
IF @@ERROR <> 0 goto on_error

-- All other records are set to Individual
UPDATE 
	ams_members
SET 
	recordTypeID = @IndivRTID
where 
	orgID = @orgID
	and memberid = activeMemberID
	and status <> 'D'
	and recordTypeID is null
IF @@ERROR <> 0 goto on_error

COMMIT TRAN
goto on_success

on_error:
	ROLLBACK TRAN
	goto on_done

on_success:
	declare @itemGroupUID uniqueidentifier
	EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgid, '', '', 1, @itemGroupUID OUTPUT

on_done:
	DROP TABLE datatransfer.dbo.tmp_INFirms

GO	