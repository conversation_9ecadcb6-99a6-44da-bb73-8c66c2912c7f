ALTER PROC dbo.openTracking_track
@ipNumber int, 
@memberID int,
@messageID int

AS

SET XACT_ABORT ON;
BEGIN TRY

	BEGIN TRAN;
		INSERT INTO trialslyris1.dbo.clicktracking_ (IPAddress_, MemberID_, MessageID_, TimeClicked_, GroupID_)
		values (@ipNumber, @memberID, @messageID, getdate(), 0);

		UPDATE trialslyris1.dbo.members_ 
		SET ReadsHtml_ = 'T' 
		WHERE MemberID_ = @memberID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO
