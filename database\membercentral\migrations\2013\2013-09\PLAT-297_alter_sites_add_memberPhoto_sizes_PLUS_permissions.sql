﻿/*
   Wednesday, September 04, 201310:43:22 AM
   User: alongnion
   Server: DEV04\PLATFORM2008
   Database: memberCentral
   Application: 
*/

/* To prevent any potential data loss issues, you should review this script in detail before running it outside the context of the database designer.*/
ALTER TABLE dbo.sites ADD
	memberPhotoWidth smallint NOT NULL CONSTRAINT DF_sites_memberPhotoWidth DEFAULT 80,
	memberPhotoHeight smallint NOT NULL CONSTRAINT DF_sites_memberPhotoHeight DEFAULT 100
GO

-- set rights for using the photo upload feature for Members to use outside of Control Panel
declare @resourceTypeID int, @resourceTypeName varchar(50), @functionID int, @functionName varchar(50), @functionDisplayName varchar(50), @addToSuperUserRole bit, @addToSiteAdminRole bit

DECLARE @rc int, @siteAdminRoleID int, @superAdminRoleID int, @ResourceTypeFunctionID int

set @addToSuperUserRole = 1
set @addToSiteAdminRole = 1
set @resourceTypeName = 'UpdateMember'
set @functionName = 'uploadPhoto'
set @functionDisplayName = 'Manage Photos for Update Member Tool'

--- Do not change below this line

select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)
select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')
select @siteAdminRoleID = dbo.fn_getResourceRoleID('Site Administrator')



BEGIN TRAN

	EXEC @rc = cms_createSiteResourceFunction
		@resourceTypeID=@resourceTypeID,
		@functionName=@functionName,
		@displayName=@functionDisplayName,
		@functionID=@functionID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);
				IF @@ERROR <> 0 GOTO on_error


	IF (@addToSuperUserRole = 1)
		exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@superAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	IF (@addToSiteAdminRole = 1)
		exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@siteAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

IF @@TRANCOUNT > 0 COMMIT TRAN


-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
GO