use membercentral
GO

/* because this script was already run in dev and beta, we need to clear the firm accounts */
declare @orgID int, @companyRTID int
select @orgID = orgID from sites where sitecode = 'TX'
select @companyRTID = recordTypeID from dbo.ams_recordTypes where orgID = @orgID and recordTypeCode = 'Firm'

select m.memberID
into #tmpTXDelete
from ams_members as m
where m.orgID = @orgID
and m.recordTypeID = @companyRTID
and m.status <> 'D'

UPDATE m
SET m.status = 'D', m.dateLastUpdated = getdate()
FROM dbo.ams_members as m
inner join #tmpTXDelete as tmp on tmp.memberID = m.memberID

UPDATE m
SET m.status = 'D'
FROM dbo.ams_memberSiteDefaults as m
inner join #tmpTXDelete as tmp on tmp.memberID = m.memberID

UPDATE m
SET m.status = 'D'
FROM dbo.ams_memberNetworkProfiles as m
inner join #tmpTXDelete as tmp on tmp.memberID = m.memberID

DELETE FROM ams_recordRelationships
where master<PERSON>ember<PERSON> in (select memberID from #tmpTXDelete)
or childMemberID in (select memberID from #tmpTXDelete)

DROP TABLE #tmpTXDelete
GO


-- Candidates should be changed to PAC Expense Entity
declare @orgID int
select @orgID = orgID from sites where sitecode = 'TX'
declare @PACRTID int
select @PACRTID = recordTypeID from ams_recordTypes where orgID = @orgID and recordTypeCode = 'PACExpenseEntity'

update m
set m.recordTypeID = @PACRTID
from ams_members as m
inner join vw_memberdata_TX as vw on vw.memberID = m.memberID
where vw.[Contact Type] like '%Legislator%'
GO


-- all other records should be individual
declare @orgID int
select @orgID = orgID from sites where sitecode = 'TX'
declare @IndRTID int
select @IndRTID = recordTypeID from ams_recordTypes where orgID = @orgID and recordTypeCode = 'Individual'

update ams_members
set recordTypeID = @IndRTID
where orgID = @orgID
and recordTypeID is null
GO


-- Identify Firms
declare @orgID int
select @orgID = orgID from sites where sitecode = 'TX'
declare @IndRTID int
select @IndRTID = recordTypeID from ams_recordTypes where orgID = @orgID and recordTypeCode = 'Individual'

select tmp.company, vw.[Publishing Address_address1], vw.[Publishing Address_address2], vw.[Publishing Address_city], s.stateID, vw.[Publishing Address_postalCode], 
	cast(null as int) as memberid, ROW_NUMBER() OVER (ORDER BY tmp.company, s.stateID, tmp.memberID) as row
into datatransfer.dbo.tmp_TXFirms
from (
	select m.company, s.stateID, min(m.memberID) as memberID
	from dbo.ams_members as m
	inner join dbo.ams_memberData as md on md.memberID = m.memberID
	inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
	inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
	inner join dbo.ams_memberData as md2 on md2.memberID = m.memberID
	inner join dbo.ams_memberDataColumnValues as mdcv2 on mdcv2.valueID = md2.valueID
	inner join dbo.ams_memberDataColumns as mdc2 on mdc2.columnID = mdcv2.columnID
	inner join dbo.vw_memberData_TX as vw on vw.memberid = m.memberid
	inner join dbo.ams_states as s on s.code = vw.[Publishing Address_stateprov] and s.countryID = 1
	where m.orgID = @orgID
	and m.status <> 'D'
	and len(m.company) > 0
	and m.company not in ('Attorney at Law','Attorney','Attorney & Counselor','Attorney & Counselor At Law','Attorney / Mediator','Attorney And Counselor','Attorney And Counselor At Law','Attorney And Mediator','Atty At Law','Attorney-at-law','Attorney-mediator','Attorney-mediator-arbitrator','Attorney/mediator')
	and m.recordTypeID = @IndRTID
	and mdc.columnName = 'Contact Type'
	and mdcv.columnValueString in ('Attorney','Legal Assistant','Defense Attorney','Government Attorney','Legal Nurse Consultant','Out of State','Professor','Retired Attorney')
	and mdc2.columnName = 'Record Type'
	and mdcv2.columnValueString in ('AAM','AAP','AAS','AE','AF','AM','AMP','AS')
	group by m.company, s.stateID
	having count(*) > 1
) as tmp
inner join dbo.vw_memberData_TX as vw on vw.memberid = tmp.memberid
inner join dbo.ams_states as s on s.stateID = tmp.stateID
GO


-- create firms
declare @orgID int, @companyRTID int, @IndivRTID int, @AttorneyTID int, @AttorneyRTRTID int, @LegalTID int, @LegalRTRTID int
select @orgID = orgID from sites where sitecode = 'TX'
select @companyRTID = recordTypeID from dbo.ams_recordTypes where orgID = @orgID and recordTypeCode = 'Firm'
select @IndivRTID = recordTypeID from dbo.ams_recordTypes where orgID = @orgID and recordTypeCode = 'Individual'
select @AttorneyTID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgID and relationshipTypeCode = 'Attorney'
select @AttorneyRTRTID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes where masterRecordTypeID = @companyRTID and childRecordTypeID = @IndivRTID and relationshipTypeID = @AttorneyTID
select @LegalTID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgID and relationshipTypeCode = 'LegalAssistant'
select @LegalRTRTID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes where masterRecordTypeID = @companyRTID and childRecordTypeID = @IndivRTID and relationshipTypeID = @LegalTID

BEGIN TRAN

declare @row int, @membernumber varchar(50), @rc int, @memberID int, @company varchar(300), @stateID int
select @row = min(row) from datatransfer.dbo.tmp_TXFirms
while @row is not null BEGIN
	select @company = null, @membernumber = null, @memberID = null, @rc = null, @stateID = null
	select @company = company, @stateID = stateID from datatransfer.dbo.tmp_TXFirms where row = @row
	select @membernumber = 'FIRM' + RIGHT('00000' + cast(@row as varchar(4)),5)

	EXEC @rc = dbo.ams_createMember @orgID=@orgID, @memberTypeID=2, @prefix='', @firstname='Firm', @middlename='', @lastname='Account', @suffix='', @professionalsuffix='', @company=@company, @memberNumber=@membernumber, @status='A', @memberID=@memberID OUTPUT
		IF @@ERROR <> 0 or @rc <> 0 or @memberID = 0 goto on_error

	UPDATE dbo.ams_members
	SET recordTypeID = @companyRTID
	where memberID = @memberID
		IF @@ERROR <> 0 goto on_error

	UPDATE datatransfer.dbo.tmp_TXFirms
	set memberID = @memberID
	where row = @row
		IF @@ERROR <> 0 goto on_error

	INSERT INTO dbo.ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive)
	select distinct @AttorneyRTRTID, @memberID, m.memberid, 1
	from dbo.ams_members as m
	inner join dbo.ams_memberData as md on md.memberID = m.memberID
	inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
	inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
	inner join dbo.vw_memberData_TX as vw on vw.memberid = m.memberid
	inner join dbo.ams_states as s on s.code = vw.[Publishing Address_stateprov] and s.countryID = 1
	where m.orgID = @orgID
	and m.status <> 'D'
	and m.company = @company
	and s.stateID = @stateID
	and m.recordTypeID = @IndivRTID
	and mdc.columnName = 'Contact Type'
	and mdcv.columnValueString in ('Attorney','Defense Attorney','Government Attorney','Out of State','Professor','Retired Attorney')
		IF @@ERROR <> 0 goto on_error

	INSERT INTO dbo.ams_recordRelationships (recordTypeRelationshipTypeID, masterMemberID, childMemberID, isActive)
	select distinct @LegalRTRTID, @memberID, m.memberid, 1
	from dbo.ams_members as m
	inner join dbo.ams_memberData as md on md.memberID = m.memberID
	inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
	inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
	inner join dbo.vw_memberData_TX as vw on vw.memberid = m.memberid
	inner join dbo.ams_states as s on s.code = vw.[Publishing Address_stateprov] and s.countryID = 1
	where m.orgID = @orgID
	and m.status <> 'D'
	and m.company = @company
	and s.stateID = @stateID
	and m.recordTypeID = @IndivRTID
	and mdc.columnName = 'Contact Type'
	and mdcv.columnValueString in ('Legal Assistant','Legal Nurse Consultant')
		IF @@ERROR <> 0 goto on_error

	select @row = min(row) from datatransfer.dbo.tmp_TXFirms where row > @row
END

declare @addressTypeID int
select @addressTypeID = addressTypeID from dbo.ams_memberAddressTypes where orgID = @orgID and addressTypeOrder = 1

insert into dbo.ams_memberAddresses (memberID, addressTypeID, address1, address2, address3, city, stateID, postalCode, countryID)
select memberID, @addressTypeID, isnull([Publishing Address_address1],''), isnull([Publishing Address_address2],''), '', 
	isnull([Publishing Address_city],''), stateID, isnull([Publishing Address_postalCode],''), 1
from datatransfer.dbo.tmp_TXFirms
	IF @@ERROR <> 0 goto on_error

COMMIT TRAN
goto on_success

on_error:
	ROLLBACK TRAN
	goto on_done

on_success:
	-- queue member groups (@runSchedule=2 indicates delayed processing) 
	declare @itemGroupUID uniqueidentifier, @memberIDList varchar(max)
	EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList='', @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

on_done:
	DROP TABLE datatransfer.dbo.tmp_TXFirms

GO
