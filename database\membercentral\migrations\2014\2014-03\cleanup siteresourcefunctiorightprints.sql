
declare @siteID int
set @siteID = 39

IF OBJECT_ID('tempdb..#siteResourcesToProcess') IS NOT NULL
	DROP TABLE #siteResourcesToProcess

--CREATE TABLE #siteResourcesToProcess (autoid int IDENTITY(1,1), siteResourceID int);

--insert into #siteResourcesToProcess (siteResourceID)
select  distinct sr.siteResourceID --s.siteID, s.sitecode, srt.resourceType, sr.siteResourceID, srfrp.functionID, count(*), min(srfrp.rightPrintID) , max(srfrp.rightPrintID)
from dbo.cache_perms_siteResourceFunctionRightPrints srfrp
inner join cms_siteResources sr
	on sr.siteResourceID = srfrp.siteResourceID
inner join cms_siteResourceTypes srt
	on sr.resourceTypeID = srt.resourceTypeID
inner join sites s
	on s.siteID = sr.siteID
	and s.siteID = @siteID
group by s.siteID, s.sitecode, srt.resourceType, sr.siteResourceID, srfrp.functionID
having count(*) > 1
	and min(srfrp.rightPrintID) <> max(srfrp.rightPrintID)


delete srfrp 
from cache_perms_siteResourceFunctionRightPrints srfrp
inner join #siteResourcesToProcess sr
	on sr.siteResourceID = srfrp.siteResourceID

exec [cache_perms_updateSiteResourceFunctionRightPrintsForSiteResourcesBulk] @siteID=@siteID, @processNewPrints = 0


IF OBJECT_ID('tempdb..#siteResourcesToProcess') IS NOT NULL
	DROP TABLE #siteResourcesToProcess
