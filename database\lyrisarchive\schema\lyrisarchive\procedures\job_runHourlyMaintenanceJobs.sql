ALTER PROC dbo.job_runHourlyMaintenanceJobs
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	/* Clear inmailMessageFullSource */
	BEGIN TRY
		SET XACT_ABORT OFF;
		EXEC dbo.lyris_clearInmailMessageFullSource @batchSize=50;
		SET XACT_ABORT ON;
	END TRY
	BEGIN CATCH
		SET XACT_ABORT ON;
		EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
	END CATCH

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
