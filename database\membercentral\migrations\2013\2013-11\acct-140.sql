use membercentral;

ALTER TABLE dbo.tr_invoiceProfiles ADD
	enableAutoPay bit NOT NULL CONSTRAINT DF_tr_invoiceProfiles_enableAutoPay DEFAULT 1
GO

use customApps;

ALTER TABLE dbo.schedTask_autoPayInvoices ADD
	isAutoCreated bit NOT NULL CONSTRAINT DF_schedTask_autoPayInvoices_isAutoCreated DEFAULT 1
GO


USE [customApps]
GO
CREATE TABLE [dbo].[schedTask_payInvoicesProcessBatches](
	[processBatchID] [int] IDENTITY(1,1) NOT NULL,
	[processBatchDateCreated] [datetime] NOT NULL,
	[machineID] [int] NOT NULL,
 CONSTRAINT [PK_schedTask_payInvoicesProcessBatches] PRIMARY KEY CLUSTERED 
(
	[processBatchID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO


ALTER TABLE dbo.schedTask_autoPayInvoices ADD
	[processBatchID] int NULL 
GO

ALTER TABLE dbo.schedTask_autoPayInvoices ADD CONSTRAINT
	FK_schedTask_autoPayInvoices_schedTask_payInvoicesProcessBatches FOREIGN KEY
	(
	processBatchID
	) REFERENCES dbo.schedTask_payInvoicesProcessBatches
	(
	processBatchID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO

CREATE PROC [dbo].[job_createPayInvoicesProcessBatch]
	@machineID int
AS

SET NOCOUNT ON

DECLARE @batchSize int, @processBatchID int
set @batchSize = 1

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;



BEGIN TRY

	if exists (
		select * 
		from customApps.dbo.schedTask_autoPayInvoices 
		where responseCode is null 
		and processBatchID is null
	)
	BEGIN

		insert into customApps.dbo.schedTask_payInvoicesProcessBatches (processBatchDateCreated, machineID) values (getDate(),@machineID)
		select @processBatchID = SCOPE_IDENTITY()

		update api 
			set processBatchID = @processBatchID
		from customApps.dbo.schedTask_autoPayInvoices api
		inner join (
			select top (@batchSize) autoID
			from customApps.dbo.schedTask_autoPayInvoices
			where responseCode is null 
				and processBatchID is null
			order by autoID
		) as temp on temp.autoID = api.autoID
	END

	-- invoices to be paid
	select *
	from customApps.dbo.schedTask_autoPayInvoices
	where processBatchID = @processBatchID

	-- charges to be run
	select jobID, orgid, siteid, sitename, memberID, gatewayID, profileID, profileCode, customerProfileID, paymentProfileID, useBatches, sum(chargeAmount) as totalChargeAmount
	from customApps.dbo.schedTask_autoPayInvoices
	where processBatchID = @processBatchID
	group by jobID, orgid, siteid, sitename, memberID, gatewayID, profileID, profileCode, customerProfileID, paymentProfileID, useBatches
	order by 1


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH

GO

USE [memberCentral]
GO
ALTER PROC [dbo].[tr_autoPayInvoices]
AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @jobUID uniqueidentifier, @isAutoCreated bit
	select @jobUID = NewID()
	set @isAutoCreated = 1

	; WITH innerTbl as (
		select i.invoiceID, m2.memberID, o.orgID, s.siteID, mp.gatewayID, mp.profileID,
			o.useBatches, mpp.customerProfileID, mpp.paymentProfileID, mp.profileCode,
			s.siteName, i.dateCreated, i.dateBilled, i.dateDue, 
			o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber,
			isnull(m2.lastname,'') + ', ' + isnull(m2.firstname,'') + case when o.hasMiddleName = 1 then isnull(' ' + m2.middlename,'') else '' end + ' (' + m2.membernumber + ')' as memberName,
			mpp.detail, mp.profileName, o.accountingEmail
		from dbo.tr_invoices as i
		inner join dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = i.payProfileID
		inner join dbo.ams_members as m on m.memberid = i.assignedToMemberID
		inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
		inner join dbo.organizations as o on o.orgID = m2.orgID
		inner join dbo.mp_profiles as mp on mp.profileID = mpp.profileID
		inner join dbo.sites as s on s.siteID = mp.siteID
		where i.statusID = 3
		and i.dateDue < getdate()
		and mpp.status = 'A'
		and mp.status = 'A'
		and mp.allowPayments = 1
	)

	insert into customApps.dbo.schedTask_autoPayInvoices (jobID, invoiceID, memberID, orgID,
		siteID, gatewayID, profileID, useBatches, customerProfileID, paymentProfileID,
		profileCode, siteName, chargeAmount, dateCreated, dateBilled, dateDue, invoiceNumber, 
		memberName, detail, profileName, accountingEmail, isAutoCreated)
	select @jobUID, tmp.invoiceID, tmp.memberID, tmp.orgID, tmp.siteID, tmp.gatewayID, tmp.profileID, 
		tmp.useBatches, tmp.customerProfileID, tmp.paymentProfileID, tmp.profileCode, tmp.siteName, 
		sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) as chargeAmount,
		tmp.dateCreated, tmp.dateBilled, tmp.dateDue, tmp.invoiceNumber, tmp.memberName, tmp.detail,
		tmp.profileName, tmp.accountingEmail, @isAutoCreated as isAutoCreated
	from innerTbl as tmp
	left outer join dbo.tr_invoiceTransactions as it on it.invoiceID = tmp.invoiceID
	group by tmp.invoiceID, tmp.memberID, tmp.orgID, tmp.siteID, tmp.gatewayID, tmp.profileID, 
		tmp.useBatches, tmp.customerProfileID, tmp.paymentProfileID, tmp.profileCode, tmp.siteName, 
		tmp.dateCreated, tmp.dateBilled, tmp.dateDue, tmp.invoiceNumber, tmp.memberName, tmp.detail,
		tmp.profileName, tmp.accountingEmail
	having sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) > 0
	order by 1, tmp.customerProfileID, tmp.invoiceID, tmp.memberID



	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO


USE [customApps]
GO
ALTER PROC [dbo].[job_createPayInvoicesProcessBatch]
	@machineID int
AS

SET NOCOUNT ON

DECLARE @batchSize int, @processBatchID int
set @batchSize = 1

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;



BEGIN TRY

	if exists (
		select * 
		from customApps.dbo.schedTask_autoPayInvoices 
		where responseCode is null 
		and processBatchID is null
	)
	BEGIN

		insert into customApps.dbo.schedTask_payInvoicesProcessBatches (processBatchDateCreated, machineID) values (getDate(),@machineID)
		select @processBatchID = SCOPE_IDENTITY()

		update api 
			set processBatchID = @processBatchID
		from customApps.dbo.schedTask_autoPayInvoices api
		inner join (
			select top (@batchSize) autoID
			from customApps.dbo.schedTask_autoPayInvoices
			where responseCode is null 
				and processBatchID is null
			order by autoID
		) as temp on temp.autoID = api.autoID
	END

	-- invoices to be paid
	select *
	from customApps.dbo.schedTask_autoPayInvoices
	where processBatchID = @processBatchID

	-- charges to be run
	select jobID, orgid, siteid, sitename, memberID, gatewayID, profileID, profileCode, customerProfileID, paymentProfileID, useBatches, sum(chargeAmount) as totalChargeAmount
	from customApps.dbo.schedTask_autoPayInvoices
	where processBatchID = @processBatchID
	group by jobID, orgid, siteid, sitename, memberID, gatewayID, profileID, profileCode, customerProfileID, paymentProfileID, useBatches
	order by 1

	-- distinct jobs in batch
	select distinct jobID
	from customApps.dbo.schedTask_autoPayInvoices
	where processBatchID = @processBatchID



	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH

GO

USE [customApps]
GO
CREATE TABLE [dbo].[schedTask_payInvoicesProcessStatuses](
	[processStatusID] [int] IDENTITY(1,1) NOT NULL,
	[statusName] [varchar](50) NOT NULL,
	[statusOrder] [int] NOT NULL,
 CONSTRAINT [PK_schedTask_payInvoicesProcessStatuses] PRIMARY KEY CLUSTERED 
(
	[processStatusID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[schedTask_payInvoicesProcessJobs](
	[jobID] [uniqueidentifier] NOT NULL,
	[dateCreated] [datetime] NOT NULL,
	[isAutoCreated] [bit] NOT NULL CONSTRAINT [DF_schedTask_payInvoicesProcessJobs_isAutoCreated]  DEFAULT ((1)),
	[processStatusID] [int] NOT NULL,
 CONSTRAINT [PK_schedTask_payInvoicesProcessJobs] PRIMARY KEY CLUSTERED 
(
	[jobID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

/****** Object:  ForeignKey [FK_schedTask_payInvoicesProcessJobs_schedTask_payInvoicesProcessStatuses]    Script Date: 11/01/2013 10:02:26 ******/
ALTER TABLE [dbo].[schedTask_payInvoicesProcessJobs]  WITH CHECK ADD  CONSTRAINT [FK_schedTask_payInvoicesProcessJobs_schedTask_payInvoicesProcessStatuses] FOREIGN KEY([processStatusID])
REFERENCES [dbo].[schedTask_payInvoicesProcessStatuses] ([processStatusID])
GO
ALTER TABLE [dbo].[schedTask_payInvoicesProcessJobs] CHECK CONSTRAINT [FK_schedTask_payInvoicesProcessJobs_schedTask_payInvoicesProcessStatuses]
GO

insert into [customApps].dbo.[schedTask_payInvoicesProcessStatuses] (statusOrder,statusName) values (1,'creating')
insert into [customApps].dbo.[schedTask_payInvoicesProcessStatuses] (statusOrder,statusName) values (2,'queuedPayment')
insert into [customApps].dbo.[schedTask_payInvoicesProcessStatuses] (statusOrder,statusName) values (3,'processingPayment')
insert into [customApps].dbo.[schedTask_payInvoicesProcessStatuses] (statusOrder,statusName) values (4,'queuedNotification')
insert into [customApps].dbo.[schedTask_payInvoicesProcessStatuses] (statusOrder,statusName) values (5,'processingNotification')
insert into [customApps].dbo.[schedTask_payInvoicesProcessStatuses] (statusOrder,statusName) values (6,'Done')

GO


USE [memberCentral]
GO
ALTER PROC [dbo].[tr_autoPayInvoices]
AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @jobUID uniqueidentifier, @isAutoCreated bit
	declare @jobStatusCreating int, @jobStatusQueuedPayment int

	select @jobStatusCreating = processStatusID 
	from customApps.dbo.schedTask_payInvoicesProcessStatuses
	where statusName = 'creating'

	select @jobStatusQueuedPayment = processStatusID 
	from customApps.dbo.schedTask_payInvoicesProcessStatuses
	where statusName = 'queuedPayment'

	select @jobUID = NewID()
	set @isAutoCreated = 1

	insert into customApps.dbo.schedTask_payInvoicesProcessJobs (jobID, dateCreated, isAutoCreated, processStatusID)
	VALUES (@jobUID,getDate(),@isAutoCreated,@jobStatusCreating)


	; WITH innerTbl as (
		select i.invoiceID, m2.memberID, o.orgID, s.siteID, mp.gatewayID, mp.profileID,
			o.useBatches, mpp.customerProfileID, mpp.paymentProfileID, mp.profileCode,
			s.siteName, i.dateCreated, i.dateBilled, i.dateDue, 
			o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber,
			isnull(m2.lastname,'') + ', ' + isnull(m2.firstname,'') + case when o.hasMiddleName = 1 then isnull(' ' + m2.middlename,'') else '' end + ' (' + m2.membernumber + ')' as memberName,
			mpp.detail, mp.profileName, o.accountingEmail
		from dbo.tr_invoices as i
		inner join dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = i.payProfileID
		inner join dbo.ams_members as m on m.memberid = i.assignedToMemberID
		inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
		inner join dbo.organizations as o on o.orgID = m2.orgID
		inner join dbo.mp_profiles as mp on mp.profileID = mpp.profileID
		inner join dbo.sites as s on s.siteID = mp.siteID
		where i.statusID = 3
		and i.dateDue < getdate()
		and mpp.status = 'A'
		and mp.status = 'A'
		and mp.allowPayments = 1
	)

	insert into customApps.dbo.schedTask_autoPayInvoices (jobID, invoiceID, memberID, orgID,
		siteID, gatewayID, profileID, useBatches, customerProfileID, paymentProfileID,
		profileCode, siteName, chargeAmount, dateCreated, dateBilled, dateDue, invoiceNumber, 
		memberName, detail, profileName, accountingEmail, isAutoCreated)
	select @jobUID, tmp.invoiceID, tmp.memberID, tmp.orgID, tmp.siteID, tmp.gatewayID, tmp.profileID, 
		tmp.useBatches, tmp.customerProfileID, tmp.paymentProfileID, tmp.profileCode, tmp.siteName, 
		sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) as chargeAmount,
		tmp.dateCreated, tmp.dateBilled, tmp.dateDue, tmp.invoiceNumber, tmp.memberName, tmp.detail,
		tmp.profileName, tmp.accountingEmail, @isAutoCreated as isAutoCreated
	from innerTbl as tmp
	left outer join dbo.tr_invoiceTransactions as it on it.invoiceID = tmp.invoiceID
	group by tmp.invoiceID, tmp.memberID, tmp.orgID, tmp.siteID, tmp.gatewayID, tmp.profileID, 
		tmp.useBatches, tmp.customerProfileID, tmp.paymentProfileID, tmp.profileCode, tmp.siteName, 
		tmp.dateCreated, tmp.dateBilled, tmp.dateDue, tmp.invoiceNumber, tmp.memberName, tmp.detail,
		tmp.profileName, tmp.accountingEmail
	having sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) > 0
	order by 1, tmp.customerProfileID, tmp.invoiceID, tmp.memberID


	update customApps.dbo.schedTask_payInvoicesProcessJobs set
		processStatusID = @jobStatusQueuedPayment
	where jobID = @jobUID


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO


USE [customApps]
GO

ALTER PROC [dbo].[job_createPayInvoicesProcessBatch]
	@machineID int
AS

SET NOCOUNT ON

DECLARE @batchSize int, @processBatchID int
set @batchSize = 1

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;



BEGIN TRY

	if exists (
		select * 
		from customApps.dbo.schedTask_autoPayInvoices 
		where responseCode is null 
		and processBatchID is null
	)
	BEGIN

		insert into customApps.dbo.schedTask_payInvoicesProcessBatches (processBatchDateCreated, machineID) values (getDate(),@machineID)
		select @processBatchID = SCOPE_IDENTITY()

		update api 
			set processBatchID = @processBatchID
		from customApps.dbo.schedTask_autoPayInvoices api
		inner join (
			select top (@batchSize) autoID
			from customApps.dbo.schedTask_autoPayInvoices api2
			inner join customApps.dbo.schedTask_payInvoicesProcessJobs j
				on j.jobID = api2.jobID
				and api2.responseCode is null 
				and api2.processBatchID is null
			inner join customApps.dbo.schedTask_payInvoicesProcessStatuses ps
				on ps.processStatusID = j.processStatusID
				and ps.statusName = 'queuedPayment'
			order by autoID
		) as temp on temp.autoID = api.autoID
	END

	-- invoices to be paid
	select *
	from customApps.dbo.schedTask_autoPayInvoices
	where processBatchID = @processBatchID

	-- charges to be run
	select jobID, orgid, siteid, sitename, memberID, gatewayID, profileID, profileCode, customerProfileID, paymentProfileID, useBatches, sum(chargeAmount) as totalChargeAmount
	from customApps.dbo.schedTask_autoPayInvoices
	where processBatchID = @processBatchID
	group by jobID, orgid, siteid, sitename, memberID, gatewayID, profileID, profileCode, customerProfileID, paymentProfileID, useBatches
	order by 1

	-- distinct jobs in batch
	select distinct jobID
	from customApps.dbo.schedTask_autoPayInvoices
	where processBatchID = @processBatchID



	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH

GO

USE [customApps]
GO
CREATE PROC [dbo].[job_managePayInvoicesProcess]
AS
	-- move job to next step after processingPayment finished

	declare @jobStatusQueuedNotification int

	select @jobStatusQueuedNotification = processStatusID 
	from customApps.dbo.schedTask_payInvoicesProcessStatuses
	where statusName = 'queuedNotification'

	update j set
		processStatusID = @jobStatusQueuedNotification
	from customApps.dbo.schedTask_payInvoicesProcessJobs j
	inner join customApps.dbo.schedTask_payInvoicesProcessStatuses ps
		on ps.processStatusID = j.processStatusID
		and ps.statusName = 'processingPayment'
	left outer join customApps.dbo.schedTask_autoPayInvoices api
		on api.jobID = j.jobID
		and api.responseCode is null 
	where api.autoID is null

RETURN 0

GO


USE [customApps]
GO

ALTER PROC [dbo].[job_createPayInvoicesProcessBatch]
	@machineID int
AS

SET NOCOUNT ON

DECLARE @batchSize int, @processBatchID int
set @batchSize = 1

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

	declare @jobStatusProcessingPayment int, @jobStatusQueuedPayment int

	select @jobStatusProcessingPayment = processStatusID 
	from customApps.dbo.schedTask_payInvoicesProcessStatuses
	where statusName = 'processingPayment'

	select @jobStatusQueuedPayment = processStatusID 
	from customApps.dbo.schedTask_payInvoicesProcessStatuses
	where statusName = 'queuedPayment'



BEGIN TRY

	if exists (
		select * 
		from customApps.dbo.schedTask_autoPayInvoices 
		where responseCode is null 
		and processBatchID is null
	)
	BEGIN

		insert into customApps.dbo.schedTask_payInvoicesProcessBatches (processBatchDateCreated, machineID) values (getDate(),@machineID)
		select @processBatchID = SCOPE_IDENTITY()

		update api 
			set processBatchID = @processBatchID
		from customApps.dbo.schedTask_autoPayInvoices api
		inner join (
			select top (@batchSize) autoID
			from customApps.dbo.schedTask_autoPayInvoices api2
			inner join customApps.dbo.schedTask_payInvoicesProcessJobs j
				on j.jobID = api2.jobID
				and api2.responseCode is null 
				and api2.processBatchID is null
			inner join customApps.dbo.schedTask_payInvoicesProcessStatuses ps
				on ps.processStatusID = j.processStatusID
				and ps.statusName in ('queuedPayment','processingPayment')
			order by autoID
		) as temp on temp.autoID = api.autoID
	END

	-- move any jobs in batch from queuedPayment to processingPayment that are being worked for the first time

	update j set
		processStatusID = @jobStatusProcessingPayment
	from customApps.dbo.schedTask_autoPayInvoices api
	inner join customApps.dbo.schedTask_payInvoicesProcessJobs j
		on j.jobID = api.jobID
		and j.processStatusID = @jobStatusQueuedPayment
	where processBatchID = @processBatchID

	-- invoices to be paid
	select *
	from customApps.dbo.schedTask_autoPayInvoices
	where processBatchID = @processBatchID

	-- charges to be run
	select jobID, orgid, siteid, sitename, memberID, gatewayID, profileID, profileCode, customerProfileID, paymentProfileID, useBatches, sum(chargeAmount) as totalChargeAmount
	from customApps.dbo.schedTask_autoPayInvoices
	where processBatchID = @processBatchID
	group by jobID, orgid, siteid, sitename, memberID, gatewayID, profileID, profileCode, customerProfileID, paymentProfileID, useBatches
	order by 1


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH

GO





USE [customApps]
GO
CREATE PROC [dbo].[job_payInvoicesProcess_getJobsReadyForNotifcation]
AS
	set nocount on;

	declare @jobStatusProcessingNotification int
	declare @affectedJobs TABLE (jobID uniqueIdentifier PRIMARY KEY)

	select @jobStatusProcessingNotification = processStatusID 
	from customApps.dbo.schedTask_payInvoicesProcessStatuses
	where statusName = 'processingNotification'


	update j set
		processStatusID = @jobStatusProcessingNotification
	output
		inserted.jobID
	into @affectedJobs
	from customApps.dbo.schedTask_payInvoicesProcessJobs j
	inner join customApps.dbo.schedTask_payInvoicesProcessStatuses ps
		on ps.processStatusID = j.processStatusID
		and ps.statusName = 'queuedNotification'


	select jobID
	from @affectedJobs

	set nocount off;

RETURN 0

GO
USE [customApps]
GO

declare @processStatusID int

select @processStatusID = processStatusID 
from customApps.dbo.schedTask_payInvoicesProcessStatuses
where statusName = 'Done'

insert into customapps.dbo.schedTask_payInvoicesProcessJobs (jobID, dateCreated, isAutoCreated, processStatusID)
select i.jobID, min(t.dateRecorded), cast(1 as bit), @processStatusID
from customapps.dbo.schedTask_autoPayInvoices i
inner join membercentral.dbo.tr_transactions t
	on t.transactionID = i.paymentTransactionID
left outer join customapps.dbo.schedTask_payInvoicesProcessJobs j
	on j.jobID = i.jobID
where j.jobID is null
group by i.jobID
order by min(t.dateRecorded) desc

GO


USE [memberCentral]
GO
ALTER PROC [dbo].[tr_autoPayInvoices]
AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @jobUID uniqueidentifier, @isAutoCreated bit
	declare @jobStatusCreating int, @jobStatusQueuedPayment int

	select @jobStatusCreating = processStatusID 
	from customApps.dbo.schedTask_payInvoicesProcessStatuses
	where statusName = 'creating'

	select @jobStatusQueuedPayment = processStatusID 
	from customApps.dbo.schedTask_payInvoicesProcessStatuses
	where statusName = 'queuedPayment'

	select @jobUID = NewID()
	set @isAutoCreated = 1

	insert into customApps.dbo.schedTask_payInvoicesProcessJobs (jobID, dateCreated, isAutoCreated, processStatusID)
	VALUES (@jobUID,getDate(),@isAutoCreated,@jobStatusCreating)


	; WITH innerTbl as (
		select i.invoiceID, m2.memberID, o.orgID, s.siteID, mp.gatewayID, mp.profileID,
			o.useBatches, mpp.customerProfileID, mpp.paymentProfileID, mp.profileCode,
			s.siteName, i.dateCreated, i.dateBilled, i.dateDue, 
			o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber,
			isnull(m2.lastname,'') + ', ' + isnull(m2.firstname,'') + case when o.hasMiddleName = 1 then isnull(' ' + m2.middlename,'') else '' end + ' (' + m2.membernumber + ')' as memberName,
			mpp.detail, mp.profileName, o.accountingEmail
		from dbo.tr_invoices as i
		left outer join customapps.dbo.schedTask_autoPayInvoices api
			on api.invoiceID = i.invoiceID
			and api.responseCode is null
		inner join dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = i.payProfileID
		inner join dbo.ams_members as m on m.memberid = i.assignedToMemberID
		inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
		inner join dbo.organizations as o on o.orgID = m2.orgID
		inner join dbo.mp_profiles as mp on mp.profileID = mpp.profileID
		inner join dbo.sites as s on s.siteID = mp.siteID
		where 
			i.statusID = 3
			and i.dateDue < getdate()
			and mpp.status = 'A'
			and mp.status = 'A'
			and mp.allowPayments = 1
			and api.invoiceID is null
	)

	insert into customApps.dbo.schedTask_autoPayInvoices (jobID, invoiceID, memberID, orgID,
		siteID, gatewayID, profileID, useBatches, customerProfileID, paymentProfileID,
		profileCode, siteName, chargeAmount, dateCreated, dateBilled, dateDue, invoiceNumber, 
		memberName, detail, profileName, accountingEmail, isAutoCreated)
	select @jobUID, tmp.invoiceID, tmp.memberID, tmp.orgID, tmp.siteID, tmp.gatewayID, tmp.profileID, 
		tmp.useBatches, tmp.customerProfileID, tmp.paymentProfileID, tmp.profileCode, tmp.siteName, 
		sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) as chargeAmount,
		tmp.dateCreated, tmp.dateBilled, tmp.dateDue, tmp.invoiceNumber, tmp.memberName, tmp.detail,
		tmp.profileName, tmp.accountingEmail, @isAutoCreated as isAutoCreated
	from innerTbl as tmp
	left outer join dbo.tr_invoiceTransactions as it on it.invoiceID = tmp.invoiceID
	group by tmp.invoiceID, tmp.memberID, tmp.orgID, tmp.siteID, tmp.gatewayID, tmp.profileID, 
		tmp.useBatches, tmp.customerProfileID, tmp.paymentProfileID, tmp.profileCode, tmp.siteName, 
		tmp.dateCreated, tmp.dateBilled, tmp.dateDue, tmp.invoiceNumber, tmp.memberName, tmp.detail,
		tmp.profileName, tmp.accountingEmail
	having sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) > 0
	order by 1, tmp.customerProfileID, tmp.invoiceID, tmp.memberID


	update customApps.dbo.schedTask_payInvoicesProcessJobs set
		processStatusID = @jobStatusQueuedPayment
	where jobID = @jobUID


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO


USE [customApps]
GO

ALTER PROC [dbo].[job_createPayInvoicesProcessBatch]
	@machineID int
AS

SET NOCOUNT ON

DECLARE @batchSize int, @processBatchID int
set @batchSize = 250

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

	declare @jobStatusProcessingPayment int, @jobStatusQueuedPayment int

	select @jobStatusProcessingPayment = processStatusID 
	from customApps.dbo.schedTask_payInvoicesProcessStatuses
	where statusName = 'processingPayment'

	select @jobStatusQueuedPayment = processStatusID 
	from customApps.dbo.schedTask_payInvoicesProcessStatuses
	where statusName = 'queuedPayment'



BEGIN TRY

	if exists (
		select * 
		from customApps.dbo.schedTask_autoPayInvoices 
		where responseCode is null 
		and processBatchID is null
	)
	BEGIN

		insert into customApps.dbo.schedTask_payInvoicesProcessBatches (processBatchDateCreated, machineID) values (getDate(),@machineID)
		select @processBatchID = SCOPE_IDENTITY()

		update api 
			set processBatchID = @processBatchID
		from customApps.dbo.schedTask_autoPayInvoices api
		inner join (
			select top (@batchSize) autoID
			from customApps.dbo.schedTask_autoPayInvoices api2
			inner join customApps.dbo.schedTask_payInvoicesProcessJobs j
				on j.jobID = api2.jobID
				and api2.responseCode is null 
				and api2.processBatchID is null
			inner join customApps.dbo.schedTask_payInvoicesProcessStatuses ps
				on ps.processStatusID = j.processStatusID
				and ps.statusName in ('queuedPayment','processingPayment')
			order by autoID
		) as temp on temp.autoID = api.autoID
	END

	-- move any jobs in batch from queuedPayment to processingPayment that are being worked for the first time

	update j set
		processStatusID = @jobStatusProcessingPayment
	from customApps.dbo.schedTask_autoPayInvoices api
	inner join customApps.dbo.schedTask_payInvoicesProcessJobs j
		on j.jobID = api.jobID
		and j.processStatusID = @jobStatusQueuedPayment
	where processBatchID = @processBatchID

	-- invoices to be paid
	select *
	from customApps.dbo.schedTask_autoPayInvoices
	where processBatchID = @processBatchID

	-- charges to be run
	select jobID, orgid, siteid, sitename, memberID, gatewayID, profileID, profileCode, customerProfileID, paymentProfileID, useBatches, sum(chargeAmount) as totalChargeAmount
	from customApps.dbo.schedTask_autoPayInvoices
	where processBatchID = @processBatchID
	group by jobID, orgid, siteid, sitename, memberID, gatewayID, profileID, profileCode, customerProfileID, paymentProfileID, useBatches
	order by 1


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH

GO
use membercentral;
GO

update membercentral.dbo.scheduledTasks set
intervalTypeID = 5
where taskCFC = 'model.scheduledTasks.tasks.autoPayInvoices'
GO

USE [memberCentral]
GO

ALTER PROC [dbo].[tr_autoPayInvoices]
AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @jobUID uniqueidentifier, @isAutoCreated bit
	declare @jobStatusCreating int, @jobStatusQueuedPayment int

	select @jobStatusCreating = processStatusID 
	from customApps.dbo.schedTask_payInvoicesProcessStatuses
	where statusName = 'creating'

	select @jobStatusQueuedPayment = processStatusID 
	from customApps.dbo.schedTask_payInvoicesProcessStatuses
	where statusName = 'queuedPayment'

	select @jobUID = NewID()
	set @isAutoCreated = 1

	insert into customApps.dbo.schedTask_payInvoicesProcessJobs (jobID, dateCreated, isAutoCreated, processStatusID)
	VALUES (@jobUID,getDate(),@isAutoCreated,@jobStatusCreating)


	; WITH innerTbl as (
		select i.invoiceID, m2.memberID, o.orgID, s.siteID, mp.gatewayID, mp.profileID,
			o.useBatches, mpp.customerProfileID, mpp.paymentProfileID, mp.profileCode,
			s.siteName, i.dateCreated, i.dateBilled, i.dateDue, 
			o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber,
			isnull(m2.lastname,'') + ', ' + isnull(m2.firstname,'') + case when o.hasMiddleName = 1 then isnull(' ' + m2.middlename,'') else '' end + ' (' + m2.membernumber + ')' as memberName,
			mpp.detail, mp.profileName, o.accountingEmail
		from dbo.tr_invoices as i
		left outer join customapps.dbo.schedTask_autoPayInvoices api
			on api.invoiceID = i.invoiceID
			and api.responseCode is null
		inner join dbo.tr_invoiceProfiles ip on ip.profileID = i.invoiceProfileID and ip.enableAutoPay = 1
		inner join dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = i.payProfileID
		inner join dbo.ams_members as m on m.memberid = i.assignedToMemberID
		inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
		inner join dbo.organizations as o on o.orgID = m2.orgID
		inner join dbo.mp_profiles as mp on mp.profileID = mpp.profileID
		inner join dbo.sites as s on s.siteID = mp.siteID
		where 
			i.statusID = 3
			and i.dateDue < getdate()
			and mpp.status = 'A'
			and mp.status = 'A'
			and mp.allowPayments = 1
			and api.invoiceID is null
	)

	insert into customApps.dbo.schedTask_autoPayInvoices (jobID, invoiceID, memberID, orgID,
		siteID, gatewayID, profileID, useBatches, customerProfileID, paymentProfileID,
		profileCode, siteName, chargeAmount, dateCreated, dateBilled, dateDue, invoiceNumber, 
		memberName, detail, profileName, accountingEmail, isAutoCreated)
	select @jobUID, tmp.invoiceID, tmp.memberID, tmp.orgID, tmp.siteID, tmp.gatewayID, tmp.profileID, 
		tmp.useBatches, tmp.customerProfileID, tmp.paymentProfileID, tmp.profileCode, tmp.siteName, 
		sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) as chargeAmount,
		tmp.dateCreated, tmp.dateBilled, tmp.dateDue, tmp.invoiceNumber, tmp.memberName, tmp.detail,
		tmp.profileName, tmp.accountingEmail, @isAutoCreated as isAutoCreated
	from innerTbl as tmp
	left outer join dbo.tr_invoiceTransactions as it on it.invoiceID = tmp.invoiceID
	group by tmp.invoiceID, tmp.memberID, tmp.orgID, tmp.siteID, tmp.gatewayID, tmp.profileID, 
		tmp.useBatches, tmp.customerProfileID, tmp.paymentProfileID, tmp.profileCode, tmp.siteName, 
		tmp.dateCreated, tmp.dateBilled, tmp.dateDue, tmp.invoiceNumber, tmp.memberName, tmp.detail,
		tmp.profileName, tmp.accountingEmail
	having sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) > 0
	order by 1, tmp.customerProfileID, tmp.invoiceID, tmp.memberID


	update customApps.dbo.schedTask_payInvoicesProcessJobs set
		processStatusID = @jobStatusQueuedPayment
	where jobID = @jobUID


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

