USE [trialsmith]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROCEDURE [dbo].[report_MTDYTDSales]
@enddate smalldatetime

AS

-- set end date to real end
declare @newendDate datetime
select @newendDate = dateadd(s,-1,dateadd(day,1,DATEADD(dd, DATEDIFF(dd,0,@endDate), 0)))

declare @currMTDStartDate datetime, @currMTDEndDate datetime
declare @currYTDStartDate datetime, @currYTDEndDate datetime
declare @PrevMTDStartDate datetime, @PrevMTDEndDate datetime
declare @PrevYTDStartDate datetime, @PrevYTDEndDate datetime

select @currMTDEndDate = @newendDate
select @currMTDStartDate = DATEADD(mm,DATEDIFF(mm,0,@currMTDEndDate),0)
select @currYTDEndDate = @newendDate
select @currYTDStartDate = DATEADD(yy,DATEDIFF(yy,0,@currYTDEndDate),0)
select @PrevMTDEndDate = DATEADD(yy,-1,@CurrMTDEndDate)
select @PrevMTDStartDate = DATEADD(yy,-1,@CurrMTDStartDate)
select @PrevYTDEndDate = DATEADD(yy,-1,@CurrYTDEndDate)
select @PrevYTDStartDate = DATEADD(yy,-1,@CurrYTDStartDate)

-- holder table
declare @rptTable TABLE (section int, accountCode varchar(5), accountName varchar(200), 
	CurrMTDNetAmt money DEFAULT(0), PrevMTDNetAmt money DEFAULT(0),
	CurrYTDNetAmt money DEFAULT(0), PrevYTDNetAmt money DEFAULT(0),
	MTDDiff float DEFAULT(0), YTDDiff float DEFAULT(0))

-- accounts to track
insert into @rptTable (section, accountCode, accountName)
select distinct 1, mt.acctcode, mt.membertype
From membertype as mt
inner join depoTransactions as dt on dt.accountCode = mt.acctcode
where mt.acctcode is not null
and mt.acctcode <> '3735'
	union all
select 2, '3000', 'Depositions'
	union all
select 2, '4001', 'JurySmith Research'
	union all
select 2, '5016', 'Expert Witness Profiler Report'
	union all
select 2, '5018', 'Corporate Knowledge Map Report'
	union all
select 2, '5001', 'Verdicts'
	union all
select 2, '5014', 'Asbestos Assessment'
	union all
select 2, '5023', 'MNIEG Group Annual Assessment'
	union all
select 2, '5024', 'Expert Summary Report'
	union all
select 2, '3219', 'OtherCombine'
	union all
select 2, '3230', 'OtherCombine'
	union all
select 2, '5005', 'OtherCombine'
	union all
select 2, '5007', 'OtherCombine'
	union all
select 2, '5011', 'OtherCombine'
	union all
select 2, '5017', 'OtherCombine'
	union all
select 2, '5019', 'OtherCombine'
	union all
select 2, '5020', 'OtherCombine'
	union all
select 3, '5004', 'Web Site Hosting Fees'
	union all
select 3, '5015', 'MemberCentral'
	union all
select 3, '5021', 'District Matching'
	union all
select 3, '5022', 'Email Blast'
	union all
select 3, '5009', 'Listserv Hosting'
	union all
select 3, '5008', 'Other Billable Services'
	union all
select 3, '5013', 'Digital Demand'
	union all
select 4, '7000', 'SeminarWeb On Demand'
	union all
select 4, '7001', 'SeminarWeb Live'
	union all
select 4, '7003', 'SeminarWeb Title Library'
	union all
select 4, '7005', 'SeminarWeb Bundles'
	union all
select 4, '7002', 'OtherCombine'
	union all
select 4, '7004', 'OtherCombine'
order by 1, 2

insert into @rptTable (section, accountCode, accountName)
select distinct 5, ddt.acctcode, 'OtherCombine'
from depoDocumentTypes as ddt
inner join depoTransactions as dt on dt.accountCode = ddt.acctcode
left outer join @rptTable as rpt on rpt.accountcode = dt.accountCode
where ddt.acctcode is not null
and ddt.acctcode <> '8300'
and rpt.accountcode is null
	union all
select distinct 2, ddt.acctcode, 'OtherCombine'
from documentCartDeliveryTypes as ddt
inner join depoTransactions as dt on dt.accountCode = cast(ddt.acctcode as varchar(5))
left outer join @rptTable as rpt on rpt.accountcode = dt.accountCode
where ddt.acctcode is not null
and rpt.accountcode is null
order by 1, 2

-- CurrMTDNetAmt
update rpt
set rpt.CurrMTDNetAmt = sdt.amtBilled
from @rptTable as rpt
inner join (
	select dt.accountCode, sum(isnull(dt.AmountBilled,0)) as amtBilled
	from depoTransactions as dt
	inner join @rptTable as tmp on tmp.accountCode = dt.accountCode
	and dt.DatePurchased between @currMTDStartDate and @currMTDEndDate
	group by dt.accountCode
) as sdt on sdt.accountCode = rpt.accountCode

-- CurrYTDNetAmt
update rpt
set rpt.CurrYTDNetAmt = sdt.amtBilled
from @rptTable as rpt
inner join (
	select dt.accountCode, sum(isnull(dt.AmountBilled,0)) as amtBilled
	from depoTransactions as dt
	inner join @rptTable as tmp on tmp.accountCode = dt.accountCode
	and dt.DatePurchased between @currYTDStartDate and @currYTDEndDate
	group by dt.accountCode
) as sdt on sdt.accountCode = rpt.accountCode

-- PrevMTDNetAmt
update rpt
set rpt.PrevMTDNetAmt = sdt.amtBilled
from @rptTable as rpt
inner join (
	select dt.accountCode, sum(isnull(dt.AmountBilled,0)) as amtBilled
	from depoTransactions as dt
	inner join @rptTable as tmp on tmp.accountCode = dt.accountCode
	and dt.DatePurchased between @PrevMTDStartDate and @PrevMTDEndDate
	group by dt.accountCode
) as sdt on sdt.accountCode = rpt.accountCode

-- PrevYTDNetAmt
update rpt
set rpt.PrevYTDNetAmt = sdt.amtBilled
from @rptTable as rpt
inner join (
	select dt.accountCode, sum(isnull(dt.AmountBilled,0)) as amtBilled
	from depoTransactions as dt
	inner join @rptTable as tmp on tmp.accountCode = dt.accountCode
	and dt.DatePurchased between @PrevYTDStartDate and @PrevYTDEndDate
	group by dt.accountCode
) as sdt on sdt.accountCode = rpt.accountCode

-- combine other
insert into @rptTable (section, accountCode, accountName, CurrMTDNetAmt, PrevMTDNetAmt, CurrYTDNetAmt, PrevYTDNetAmt)
select 2, 'xComb', 'Daubert, MDEX, USLF, DispAct, Other Docs, CD/Laser Print, Exhibitview, ALG Misc, AAJ Packets', sum(CurrMTDNetAmt), sum(PrevMTDNetAmt), sum(CurrYTDNetAmt), sum(PrevYTDNetAmt)
from @rptTable
where section = 2
and accountName = 'OtherCombine'

delete from @rptTable
where section = 2
and accountName = 'OtherCombine'

insert into @rptTable (section, accountCode, accountName, CurrMTDNetAmt, PrevMTDNetAmt, CurrYTDNetAmt, PrevYTDNetAmt)
select 4, 'xComb', 'SWL DVD, CD-ROM', sum(CurrMTDNetAmt), sum(PrevMTDNetAmt), sum(CurrYTDNetAmt), sum(PrevYTDNetAmt)
from @rptTable
where section = 4
and accountName = 'OtherCombine'

delete from @rptTable
where section = 4
and accountName = 'OtherCombine'

insert into @rptTable (section, accountCode, accountName, CurrMTDNetAmt, PrevMTDNetAmt, CurrYTDNetAmt, PrevYTDNetAmt)
select 5, 'xComb', 'Other Misc Sales', sum(CurrMTDNetAmt), sum(PrevMTDNetAmt), sum(CurrYTDNetAmt), sum(PrevYTDNetAmt)
from @rptTable
where section = 5
and accountName = 'OtherCombine'

delete from @rptTable
where section = 5
and accountName = 'OtherCombine'

-- section totals
insert into @rptTable (section, accountCode, accountName, CurrMTDNetAmt, PrevMTDNetAmt, CurrYTDNetAmt, PrevYTDNetAmt)
select 1, 'xxxx', 'Subscription Totals', sum(CurrMTDNetAmt), sum(PrevMTDNetAmt), sum(CurrYTDNetAmt), sum(PrevYTDNetAmt)
from @rptTable
where section = 1
	union all
select 2, 'xxxx', 'TrialSmith Services Totals', sum(CurrMTDNetAmt), sum(PrevMTDNetAmt), sum(CurrYTDNetAmt), sum(PrevYTDNetAmt)
from @rptTable
where section = 2
	union all
select 2, 'xxxx', 'TrialSmith Services/Subscriptions Totals', sum(CurrMTDNetAmt), sum(PrevMTDNetAmt), sum(CurrYTDNetAmt), sum(PrevYTDNetAmt)
from @rptTable
where section in (1,2)
	union all
select 3, 'xxxx', 'Hosting Totals', sum(CurrMTDNetAmt), sum(PrevMTDNetAmt), sum(CurrYTDNetAmt), sum(PrevYTDNetAmt)
from @rptTable
where section = 3
	union all
select 4, 'xxxx', 'SeminarWeb Totals', sum(CurrMTDNetAmt), sum(PrevMTDNetAmt), sum(CurrYTDNetAmt), sum(PrevYTDNetAmt)
from @rptTable
where section = 4

insert into @rptTable (section, accountCode, accountName, CurrMTDNetAmt, PrevMTDNetAmt, CurrYTDNetAmt, PrevYTDNetAmt)
select 5, 'xxxx', 'Business Totals', sum(CurrMTDNetAmt), sum(PrevMTDNetAmt), sum(CurrYTDNetAmt), sum(PrevYTDNetAmt)
from @rptTable
where (accountCode = 'xxxx' or accountName = 'Other Misc Sales')
and accountName <> 'TrialSmith Services/Subscriptions Totals'

-- payments
insert into @rptTable (section, accountCode, accountName, CurrMTDNetAmt, PrevMTDNetAmt, CurrYTDNetAmt, PrevYTDNetAmt)
select 6, 'xxxx', 'Payments', 
	(SELECT sum(T.AmountBilled + T.salestaxamount) * -1
	FROM depoTransactions T 
	WHERE T.isPayment = 1
	AND T.DatePurchased between @currMTDStartDate and @currMTDEndDate),
	(SELECT sum(T.AmountBilled + T.salestaxamount) * -1
	FROM depoTransactions T 
	WHERE T.isPayment = 1
	AND T.DatePurchased between @prevMTDStartDate and @prevMTDEndDate),
	(SELECT sum(T.AmountBilled + T.salestaxamount) * -1
	FROM depoTransactions T 
	WHERE T.isPayment = 1
	AND T.DatePurchased between @currYTDStartDate and @currYTDEndDate),
	(SELECT sum(T.AmountBilled + T.salestaxamount) * -1
	FROM depoTransactions T 
	WHERE T.isPayment = 1
	AND T.DatePurchased between @prevYTDStartDate and @prevYTDEndDate)

-- diffs
update @rptTable
set MTDDiff = ((CurrMTDNetAmt-PrevMTDNetAmt)/PrevMTDNetAmt) * 100
where PrevMTDNetAmt <> 0

update @rptTable
set MTDDiff = 99999
where PrevMTDNetAmt = 0 and CurrMTDNetAmt <> 0

update @rptTable
set YTDDiff = ((CurrYTDNetAmt-PrevYTDNetAmt)/PrevYTDNetAmt) * 100
where PrevYTDNetAmt <> 0

update @rptTable
set YTDDiff = 99999
where PrevYTDNetAmt = 0 and CurrYTDNetAmt <> 0

-- del empty rows
delete from @rptTable
where CurrMTDNetAmt = 0 and PrevMTDNetAmt = 0 and CurrYTDNetAmt = 0 and PrevYTDNetAmt = 0

declare @outstandingBalance money, @creditBalance money
select 
	@outstandingBalance = sum(case when totalDue > 0 then totalDue else 0 end),
	@creditBalance = sum(case when totalDue < 0 then totalDue else 0 end)
from (
	SELECT depomemberdataid, sum(AmountBilled + salesTaxAmount) as totalDue
	from dbo.depoTransactions 
	where datePurchased <= @currMTDEndDate
	group by depoMemberDataID
	having sum(AmountBilled + salesTaxAmount) <> 0
) as tmp

-- outstanding balances
insert into @rptTable (section, accountCode, accountName, CurrMTDNetAmt)
select 7, 'xxxx', 'Outstanding Balance', @outstandingBalance
	union all
select 8, 'xxxx', 'Credit Balance', @creditBalance
	union all
select 9, 'xxxx', 'Net Balance', @outstandingBalance+@creditBalance

-- return dates
select	@endDate as EndDate, @newEndDate as newEndDate,
		@currMTDStartDate as currMTDStartDate, @currMTDEndDate as currMTDEndDate,
		@prevMTDStartDate as prevMTDStartDate, @prevMTDEndDate as prevMTDEndDate,
		@currYTDStartDate as currYTDStartDate, @currYTDEndDate as currYTDEndDate,
		@prevYTDStartDate as prevYTDStartDate, @prevYTDEndDate as prevYTDEndDate

-- return monster results
select section, accountCode, accountName, currMTDNetAmt, prevMTDNetAmt, CurrYTDNetAmt, PrevYTDNetAmt,
	MTDDiff, YTDDiff
from @rptTable
order by section, accountCode, accountName

RETURN 0