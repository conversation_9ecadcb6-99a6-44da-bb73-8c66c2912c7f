USE [trialslyris1]
GO
-- Update seminarweb_sae
update ss set
	ClauseFrom_ = 'sw_marketing',
	ClauseSelect_ = 'email',
	ClauseWhere ='-- sw_marketing support    
sw_marketing.list = members_.list_ and upper(sw_marketing.orgcode) = upper(members_.association_) and upper(members_.association_) =''' + convert(varchar(20), sw.orgcode) + ''' '
from dbo.subsets_ ss
inner join sw_marketing sw 
	on	sw.orgcode=ss.name_
	and ss.list_ = 'seminarweb_sae'

-- Update seminarweb_rx
update ss set
	ClauseFrom_ = 'sw_marketing',
	ClauseSelect_ = 'email',
	ClauseWhere ='-- sw_marketing support    
sw_marketing.list = members_.list_ and upper(sw_marketing.orgcode) = upper(members_.association_) and upper(members_.association_) =''' + convert(varchar(20), sw.orgcode) + ''' '
from dbo.subsets_ ss
inner join sw_marketing sw 
	on	sw.orgcode=ss.name_
	and ss.list_ = 'seminarweb_rx'

-- Update single segment lists
if not exists ( select name_ from subsets_ where list_='seminarweb_bwc' and name_ = 'bwc') begin
	if exists (select name_ from lists_  where name_ ='seminarweb_bwc') begin
		insert subsets_ (ClauseFrom_, ClauseSelect_, ClauseWhere_, Desc_, List_, Name_, NumTestRecords_, AddWhereList_, AddWhereMemberType_, AddWhereSubType_, Type_)
		values ('sw_marketing', 
			'email', 
			'members_.EmailAddr_ IS NOT NULL AND sw_marketing.list = members_.list_ and upper(sw_marketing.orgcode) = ''BWC'' ', 
			'TSAdmin Updated', 'seminarweb_bwc', 'bwc', 50, 'T', 'T', 'T', 'normal')
		
		update lists_ set ReplyTo_ = '<EMAIL>', SMTPFrom_ ='%%merge sw_marketing.email%%' where name_ ='seminarweb_bwc'
	end
end

if not exists ( select name_ from subsets_ where list_='seminarweb_cch' and name_ = 'cch') begin
	if exists (select name_ from lists_  where name_ ='seminarweb_cch') begin
		insert subsets_ (ClauseFrom_, ClauseSelect_, ClauseWhere_, Desc_, List_, Name_, NumTestRecords_, AddWhereList_, AddWhereMemberType_, AddWhereSubType_, Type_)
		values ('sw_marketing', 
			'email', 
			'members_.EmailAddr_ IS NOT NULL AND sw_marketing.list = members_.list_ and upper(sw_marketing.orgcode) = ''CCH'' ', 
			'TSAdmin Updated', 'seminarweb_cch', 'cch', 50, 'T', 'T', 'T', 'normal')
		
		update lists_ set ReplyTo_ = '<EMAIL>', SMTPFrom_ ='%%merge sw_marketing.email%%' where name_ ='seminarweb_cch'
	end
end

if not exists ( select name_ from subsets_ where list_='seminarweb_inpta' and name_ = 'inpta') begin
	if exists (select name_ from lists_  where name_ ='seminarweb_inpta') begin
		insert subsets_ (ClauseFrom_, ClauseSelect_, ClauseWhere_, Desc_, List_, Name_, NumTestRecords_, AddWhereList_, AddWhereMemberType_, AddWhereSubType_, Type_)
		values ('sw_marketing', 
			'email', 
			'members_.EmailAddr_ IS NOT NULL AND sw_marketing.list = members_.list_ and upper(sw_marketing.orgcode) = ''INPTA'' ', 
			'TSAdmin Updated', 'seminarweb_inpta', 'inpta', 50, 'T', 'T', 'T', 'normal')
		
		update lists_ set ReplyTo_ = '<EMAIL>', SMTPFrom_ ='%%merge sw_marketing.email%%' where name_ ='seminarweb_inpta'
	end
end


if not exists ( select name_ from subsets_ where list_='seminarweb_nystla' and name_ = 'nystla') begin
	if exists (select name_ from lists_  where name_ ='seminarweb_nystla') begin
		insert subsets_ (ClauseFrom_, ClauseSelect_, ClauseWhere_, Desc_, List_, Name_, NumTestRecords_, AddWhereList_, AddWhereMemberType_, AddWhereSubType_, Type_)
		values ('sw_marketing', 
			'email', 
			'members_.EmailAddr_ IS NOT NULL AND sw_marketing.list = members_.list_ and upper(sw_marketing.orgcode) = ''NYSTLA'' ', 
			'TSAdmin Updated', 'seminarweb_nystla', 'nystla', 50, 'T', 'T', 'T', 'normal')
		
		update lists_ set ReplyTo_ = '<EMAIL>', SMTPFrom_ ='%%merge sw_marketing.email%%' where name_ ='seminarweb_nystla'
	end
end


if not exists ( select name_ from subsets_ where list_='seminarweb_opta' and name_ = 'opta') begin
	if exists (select name_ from lists_  where name_ ='seminarweb_opta') begin
		insert subsets_ (ClauseFrom_, ClauseSelect_, ClauseWhere_, Desc_, List_, Name_, NumTestRecords_, AddWhereList_, AddWhereMemberType_, AddWhereSubType_, Type_)
		values ('sw_marketing', 
			'email', 
			'members_.EmailAddr_ IS NOT NULL AND sw_marketing.list = members_.list_ and upper(sw_marketing.orgcode) = ''OPTA'' ', 
			'TSAdmin Updated', 'seminarweb_opta', 'opta', 50, 'T', 'T', 'T', 'normal')
		
		update lists_ set ReplyTo_ = '<EMAIL>', SMTPFrom_ ='%%merge sw_marketing.email%%' where name_ ='seminarweb_opta'
	end
end

if not exists ( select name_ from subsets_ where list_='seminarweb_reca' and name_ = 'reca') begin
	if exists (select name_ from lists_  where name_ ='seminarweb_reca') begin
		insert subsets_ (ClauseFrom_, ClauseSelect_, ClauseWhere_, Desc_, List_, Name_, NumTestRecords_, AddWhereList_, AddWhereMemberType_, AddWhereSubType_, Type_)
		values ('sw_marketing', 
			'email', 
			'members_.EmailAddr_ IS NOT NULL AND sw_marketing.list = members_.list_ and upper(sw_marketing.orgcode) = ''RECA'' ', 
			'TSAdmin Updated', 'seminarweb_reca', 'reca', 50, 'T', 'T', 'T', 'normal')
		
		update lists_ set ReplyTo_ = '<EMAIL>', SMTPFrom_ ='%%merge sw_marketing.email%%' where name_ ='seminarweb_reca'
	end
end

if not exists ( select name_ from subsets_ where list_='seminarweb_tacca' and name_ = 'tacca') begin
	if exists (select name_ from lists_  where name_ ='seminarweb_tacca') begin
		insert subsets_ (ClauseFrom_, ClauseSelect_, ClauseWhere_, Desc_, List_, Name_, NumTestRecords_, AddWhereList_, AddWhereMemberType_, AddWhereSubType_, Type_)
		values ('sw_marketing', 
			'email', 
			'members_.EmailAddr_ IS NOT NULL AND sw_marketing.list = members_.list_ and upper(sw_marketing.orgcode) = ''TACCA'' ', 
			'TSAdmin Updated', 'seminarweb_tacca', 'tacca', 50, 'T', 'T', 'T', 'normal')
		
		update lists_ set ReplyTo_ = '<EMAIL>', SMTPFrom_ ='%%merge sw_marketing.email%%' where name_ ='seminarweb_tacca'
	end
end

if not exists ( select name_ from subsets_ where list_='seminarweb_tcra' and name_ = 'tcra') begin
	if exists (select name_ from lists_  where name_ ='seminarweb_tcra') begin
		insert subsets_ (ClauseFrom_, ClauseSelect_, ClauseWhere_, Desc_, List_, Name_, NumTestRecords_, AddWhereList_, AddWhereMemberType_, AddWhereSubType_, Type_)
		values ('sw_marketing', 
			'email', 
			'members_.EmailAddr_ IS NOT NULL AND sw_marketing.list = members_.list_ and upper(sw_marketing.orgcode) = ''TCRA'' ', 
			'TSAdmin Updated', 'seminarweb_tcra', 'tcra', 50, 'T', 'T', 'T', 'normal')
		
		update lists_ set ReplyTo_ = '<EMAIL>', SMTPFrom_ ='%%merge sw_marketing.email%%' where name_ ='seminarweb_tcra'
	end
end

if not exists ( select name_ from subsets_ where list_='seminarweb_tht' and name_ = 'tht') begin
	if exists (select name_ from lists_  where name_ ='seminarweb_tht') begin
		insert subsets_ (ClauseFrom_, ClauseSelect_, ClauseWhere_, Desc_, List_, Name_, NumTestRecords_, AddWhereList_, AddWhereMemberType_, AddWhereSubType_, Type_)
		values ('sw_marketing', 
			'email', 
			'members_.EmailAddr_ IS NOT NULL AND sw_marketing.list = members_.list_ and upper(sw_marketing.orgcode) = ''THT'' ', 
			'TSAdmin Updated', 'seminarweb_tht', 'tht', 50, 'T', 'T', 'T', 'normal')
		
		update lists_ set ReplyTo_ = '<EMAIL>', SMTPFrom_ ='%%merge sw_marketing.email%%' where name_ ='seminarweb_tht'
	end
end


-- Update logos to new format
update sw_marketing
	set imageURL = '<center><IMG ALT="" SRC="' + ltrim(rtrim(imageURL)) + '"></center>'
where imageURL like 'http%'

