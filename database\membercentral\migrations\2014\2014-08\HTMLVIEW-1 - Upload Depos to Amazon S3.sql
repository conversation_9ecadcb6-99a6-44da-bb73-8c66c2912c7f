use transfer
GO
CREATE TABLE dbo.depoDocumentsS3UploadQueue
	(
	uploadID int NOT NULL IDENTITY (1, 1),
	documentID int NOT NULL,
	fileExt varchar(50) NOT NULL,
	dateCreated datetime NOT NULL
	)  ON [PRIMARY]
GO
ALTER TABLE dbo.depoDocumentsS3UploadQueue ADD CONSTRAINT
	DF_depoDocumentsS3UploadQueue_dateCreated DEFAULT getdate() FOR dateCreated
GO
ALTER TABLE dbo.depoDocumentsS3UploadQueue ADD CONSTRAINT
	PK_depoDocumentsS3UploadQueue PRIMARY KEY CLUSTERED 
	(
	uploadID
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

GO

CREATE NONCLUSTERED INDEX [Index_depoDocumentsS3UploadQueue_documentID_fileExt] ON [dbo].[depoDocumentsS3UploadQueue] 
(
	[documentID] ASC,
	[fileExt] ASC
)WITH (STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO

USE [trialsmith]
GO
CREATE PROC [dbo].[job_addDeposToS3UploadQueue]

AS

declare @dateLastUpdated datetime, @now datetime

set @now = getdate()

select @dateLastUpdated = dateLastUpdated
from search.dbo.tblSearchUpdateStatus
where [name] = 'depoDocumentsS3Backup'

insert into transfer.dbo.depoDocumentsS3UploadQueue (documentID, fileExt)
(
select documentID, 'pdf' as fileExt
from trialsmith.dbo.depodocuments 
where uploadpdfdatemodified >= @dateLastUpdated
union
select documentID, 'tif' as fileExt
from trialsmith.dbo.depodocuments 
where uploadtifdatemodified >= @dateLastUpdated
union
select documentID, 'txt' as fileExt
from trialsmith.dbo.depodocuments 
where uploadtxtdatemodified >= @dateLastUpdated
)
except

select documentID, fileExt
from transfer.dbo.depoDocumentsS3UploadQueue

update search.dbo.tblSearchUpdateStatus 
set	dateLastUpdated = @now
where [name] = 'depoDocumentsS3Backup'

GO