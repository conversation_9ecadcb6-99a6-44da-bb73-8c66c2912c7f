USE [seminarWeb]
GO
ALTER PROC [dbo].[general_ExpiredCreditReport]
@participantID int

AS

SET NOCOUNT ON

DECLARE @startdate datetime, @enddate datetime

SELECT @startdate = getDate()
SELECT @enddate = dateadd(mm,1,getDate())

--Get Active Programs with Expired Credit
SELECT DISTINCT s.seminarName AS [Title], sc.creditOfferedEndDate AS [CreditEndDate]
FROM dbo.tblSeminarsSWOD swod 
	INNER JOIN dbo.tblSeminarsAndCredit sc ON swod.seminarID = sc.seminarID
	INNER JOIN dbo.tblCreditStatuses cs ON sc.statusID = cs.statusID
		AND cs.status = 'Approved'
	INNER JOIN dbo.tblSeminars s on sc.seminarID = s.seminarID 
			AND s.participantID = @participantID
			AND s.isPublished = 1
WHERE creditOfferedEndDate < @startdate
ORDER BY creditOfferedEndDate, Title

--Get Active Programs w/ credit that expire over the next month
SELECT DISTINCT s.seminarName AS [Title], sc.creditOfferedEndDate AS [CreditEndDate]
FROM dbo.tblSeminarsSWOD swod 
	INNER JOIN dbo.tblSeminarsAndCredit sc ON swod.seminarID = sc.seminarID
	INNER JOIN dbo.tblCreditStatuses cs ON sc.statusID = cs.statusID
		AND cs.status = 'Approved'
	INNER JOIN dbo.tblSeminars s on sc.seminarID = s.seminarID 
			AND s.participantID = @participantID
			AND s.isPublished = 1
WHERE creditOfferedEndDate >= @startdate
AND creditOfferedEndDate <= @enddate
ORDER BY creditOfferedEndDate, Title

--Get Active Programs with a past Catalog End Date
SELECT DISTINCT s.seminarName AS [Title], swod.dateCatalogEnd AS [CatalogEndDate]
FROM dbo.tblSeminarsSWOD swod 
	INNER JOIN dbo.tblSeminarsAndCredit sc ON swod.seminarID = sc.seminarID
	INNER JOIN dbo.tblCreditStatuses cs ON sc.statusID = cs.statusID
		AND cs.status = 'Approved'
	INNER JOIN dbo.tblSeminars s on sc.seminarID = s.seminarID 
			AND s.participantID = @participantID
			AND s.isPublished = 1
WHERE dateCatalogEnd < @startdate
ORDER BY CatalogEndDate, Title

--Get Active Programs w/ Catalog End Date over the next month
SELECT DISTINCT s.seminarName AS [Title], swod.dateCatalogEnd AS [CatalogEndDate]
FROM dbo.tblSeminarsSWOD swod 
	INNER JOIN dbo.tblSeminarsAndCredit sc ON swod.seminarID = sc.seminarID
	INNER JOIN dbo.tblCreditStatuses cs ON sc.statusID = cs.statusID
		AND cs.status = 'Approved'
	INNER JOIN dbo.tblSeminars s on sc.seminarID = s.seminarID 
			AND s.participantID = @participantID
			AND s.isPublished = 1
WHERE dateCatalogEnd >= @startdate
AND dateCatalogEnd <= @enddate
ORDER BY CatalogEndDate, Title