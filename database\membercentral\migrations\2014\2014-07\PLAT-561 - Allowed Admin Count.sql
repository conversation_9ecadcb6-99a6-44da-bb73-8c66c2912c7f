use membercentral
GO
ALTER TABLE dbo.organizations ADD allowedAdminCount int null;
GO

CREATE PROC [dbo].[ams_adminCountReport]
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20), @errorContent varchar(max)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)
SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '10.36.18.90'
IF @@SERVERNAME = 'DEV04\PLATFORM2008' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'DEV03\PLATFORM2008' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'STAGING01\PLATFORM2008' BEGIN
	SET @tier = 'STAGING'
	SET @smtpserver = 'mail.trialsmith.com'
END


/* ********************************** */
/* Orgs exceeding defined admin count */
/* ********************************** */
BEGIN TRY
	IF OBJECT_ID('tempdb..#tblOrgs') IS NOT NULL 
		DROP TABLE #tblOrgs
	select o.orgcode, o.orgname, o.allowedAdminCount, count(distinct m.memberID) as memberCount
	into #tblOrgs
	from dbo.ams_groups as g
	inner join dbo.organizations as o on o.orgID = g.orgID
	left outer join dbo.cache_members_groups as cmg 
		inner join dbo.ams_members as m on m.memberID = cmg.memberID and m.status <> 'D' and m.memberID = m.activeMemberID
		on cmg.groupID = g.groupID
	where g.groupCode = 'SiteAdmins'
	and g.isSystemGroup = 1
	and nullIf(o.allowedAdminCount,0) is not null
	group by o.orgcode, o.orgname, o.allowedAdminCount
	having count(distinct m.memberID) > o.allowedAdminCount 

	IF EXISTS (select top 1 orgcode from #tblOrgs) BEGIN
		SET @errorContent = ''
		SET @errorContent = '' + 
			replace(Stuff((
				SELECT '||' + orgcode + ' - ' + orgname + '|allowed count: ' + cast(allowedAdminCount as varchar(5)) + '|actual count: ' + cast(memberCount as varchar(5))
				from #tblOrgs
				order by orgcode
				FOR XML PATH ('')
			),1,1,''),'|',@crlf)
		SET @errorSubject = @tier + ' - Allowed Admin Count Exceeded'
		SET @errorContent = @tier + ' - The allowed admin count has been exceeded for the following organizations.' + @crlf + @errorContent
		EXEC membercentral.dbo.sys_sendEmail 
			@from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errorContent, 
			@priority='normal', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END

	IF OBJECT_ID('tempdb..#tblOrgs') IS NOT NULL 
		DROP TABLE #tblOrgs
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Orgs exceeding defined admin count'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errorContent = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* ******************************** */
/* Orgs missing defined admin count */
/* ******************************** */
BEGIN TRY
	IF OBJECT_ID('tempdb..#tblOrgs2') IS NOT NULL 
		DROP TABLE #tblOrgs2
	select distinct o.orgcode + ' - ' + o.orgName as org
	into #tblOrgs2
	from dbo.organizations as o
	inner join dbo.sites as s on s.orgID = o.orgID
	inner join dbo.sub_types as st on st.siteID = s.siteID
	where nullIf(o.allowedAdminCount,0) is null

	IF EXISTS (select top 1 org from #tblOrgs2) BEGIN
		SET @errorContent = ''
		SET @errorContent = '' + 
			replace(Stuff((
				SELECT '||' + org
				from #tblOrgs2
				order by org
				FOR XML PATH ('')
			),1,1,''),'|',@crlf)
		SET @errorSubject = @tier + ' - Allowed Admin Count Missing'
		SET @errorContent = @tier + ' - The allowed admin count has not been set for the following organizations.' + @crlf + @errorContent
		EXEC membercentral.dbo.sys_sendEmail 
			@from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errorContent, 
			@priority='normal', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END

	IF OBJECT_ID('tempdb..#tblOrgs2') IS NOT NULL 
		DROP TABLE #tblOrgs2
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running Orgs missing defined admin count'
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errorContent = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg
	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

GO

ALTER PROC [dbo].[job_runDailyMaintenanceJobs]
AS

DECLARE @tier varchar(20), @errorSubjectRoot varchar(100), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '10.36.18.90'
IF @@SERVERNAME = 'DEV04\PLATFORM2008' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'DEV03\PLATFORM2008' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'STAGING01\PLATFORM2008' BEGIN
	SET @tier = 'STAGING'
	SET @smtpserver = 'mail.trialsmith.com'
END
SET @errorSubjectRoot = @tier + ' - Developer Needed - '


/* Recalc Date Based Virtual Group Rules */
BEGIN TRY
	EXEC dbo.ams_recalcVirtualGroupsBasedOnDateConditions
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Recalculating Date Based Conditions'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup resetPasswordRequests */
BEGIN TRY
	delete from dbo.ams_resetPasswordRequest
	where dateentered < dateadd(day,-30,getdate())
	and expire = 1

	delete from dbo.ams_resetPasswordRequest
	where dateentered < dateadd(day,-30,getdate())
	and expire = 0 
	and hasBeenUsed = 1
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of resetPasswordRequests'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Viewed Member Times */
BEGIN TRY
	delete tmp
	from dbo.ams_viewedMemberTimes as tmp
	inner join (
		select adminMemberViewedID, memRow
		from (
			select adminMemberViewedID, ROW_NUMBER() OVER (PARTITION BY adminID, viewedOrgID order by lastViewedDateTime desc) as memRow
			from dbo.ams_viewedMemberTimes
		) as innerTMP
		where memRow > 20
	) as t2 on t2.adminMemberViewedID = tmp.adminMemberViewedID
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of viewedMemberTimes'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Orphaned Member Data Content */
BEGIN TRY
	EXEC dbo.ams_cleanupOrphanedMemberDataContent
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of member data content'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Orphaned Network Profiles */
BEGIN TRY
	EXEC dbo.ams_deleteOrphanedNetworkProfiles
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of orphaned network profiles'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Orphaned Virtual Group Conditions */
BEGIN TRY
	EXEC dbo.ams_cleanupOrphanedVirtualGroupConditions
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of orphaned virtual group conditions'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Search Site Resource Cache entries */
BEGIN TRY
	delete from search.dbo.tblSearchSiteResourceCache
	where dateCreated < dateadd(day,-1,getdate())
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of Search Site Resource Cache'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup BuyNow log entries */
BEGIN TRY
	delete from dbo.buyNow_Log
	where insertDate < dateadd(day,-7,getdate())
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of BuyNow log entries'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Auto Post System Batches */
BEGIN TRY
	EXEC dbo.tr_autoPostSystemBatches
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Auto Posting System Batches'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Mark paid closed invoices as paid */
BEGIN TRY
	EXEC dbo.tr_autoMarkClosedInvoicesAsPaid
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Marking paid closed invoices as paid'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Close Pending Invoices */
BEGIN TRY
	EXEC dbo.sub_closePendingInvoices
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Closing Pending Invoices'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Close empty open invoices */
BEGIN TRY
	EXEC dbo.tr_autoCloseEmptyOpenInvoices
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Closing Empty Open Invoices'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Advance Subscription Rates */
BEGIN TRY
	EXEC dbo.sub_advanceRates
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Advancing Subscription Rates'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Expire Subscription Offers */
BEGIN TRY
	EXEC dbo.sub_expireOffers
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Expiring Subscription Offers'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Expire Subscriptions */
BEGIN TRY
	EXEC dbo.sub_expireSubscriptions
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Expiring Subscriptions'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Add autoPay Invoices to Invoice Payment Queue */
BEGIN TRY
	EXEC dbo.tr_autoPayInvoices
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Adding autoPay Invoices to Invoice Payment Queue'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Add items to LRIS Client Surveys Queue */
IF @tier = 'PRODUCTION' BEGIN
	BEGIN TRY
		declare @referralID int
		set @referralID = 1
		EXEC dbo.ref_addClientSurveyQueue @referralID = @referralID
	END TRY
	BEGIN CATCH
		SET @errorSubject = @errorSubjectRoot + 'Error Adding items to Client Surveys Queue'

		SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
		SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END CATCH
END

/* Cleanup tblQueueItems_processMemberGroups */
BEGIN TRY
	delete from platformQueue.dbo.tblQueueItems_processMemberGroups
	where dateadded < dateadd(day,-1,getdate())
	and itemUID not in (select itemUID from platformQueue.dbo.tblQueueItems)
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of tblQueueItems_processMemberGroups'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* Add missing ad zones or update ad zone names */
BEGIN TRY
	EXEC dbo.ad_populateAdZones
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Populating Ad Zones'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* Admin Count checks */
-- Run on first of month only
IF datepart(d,getdate()) = 1 BEGIN
	BEGIN TRY
		EXEC dbo.ams_adminCountReport
	END TRY
	BEGIN CATCH
		SET @errorSubject = @errorSubjectRoot + 'Error Running Admin Count Report'

		SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
		SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END CATCH
END

RETURN 0
GO



