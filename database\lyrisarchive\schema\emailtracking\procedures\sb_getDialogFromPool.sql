ALTER PROC dbo.sb_getDialogFromPool
@fromService SYSNAME,
@toService SYSNAME,
@onContract SYSNAME,
@dialogHandle UNIQUEIDENTIFIER OUTPUT,
@sendCount BIGINT OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- Reuse a free dialog in the pool or create a new one in case no free dialogs exist.
	DECLARE @tblDialog TABLE (FromService SYSNAME NOT NULL, ToService SYSNAME NOT NULL, OnContract SYSNAME NOT NULL, 
		Handle UNIQUEIDENTIFIER NOT NULL, OwnerSPID INT NOT NULL, CreationTime DATETIME NOT NULL, SendCount BIGINT NOT NULL);

	-- Try to claim an unused dialog in sb_dialogPool
	-- READPAST option avoids blocking on locked dialogs.
	BEGIN TRAN;
		DELETE FROM @tblDialog;

		UPDATE TOP(1) dbo.sb_dialogPool WITH(READPAST)
		SET OwnerSPID = @@SPID
			OUTPUT INSERTED.* INTO @tblDialog
		WHERE FromService = @fromService
		AND ToService = @toService
		AND OnContract = @OnContract
		AND OwnerSPID = -1;

		IF @@ROWCOUNT > 0 BEGIN
			SET @dialogHandle = (SELECT Handle FROM @tblDialog);
			SET @sendCount = (SELECT SendCount FROM @tblDialog);
		END
		ELSE BEGIN
			-- No free dialogs: need to create a new one
			BEGIN DIALOG CONVERSATION @dialogHandle
			FROM SERVICE @fromService
			TO SERVICE @toService
			ON CONTRACT @onContract
			WITH ENCRYPTION = OFF;

			INSERT INTO dbo.sb_dialogPool (FromService, ToService, OnContract, Handle, OwnerSPID, CreationTime, SendCount)
			VALUES (@fromService, @toService, @onContract, @dialogHandle, @@SPID, GETDATE(), 0);

			SET @sendCount = 0;
		END
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO
