use membercentral;
GO
ALTER PROCEDURE [dbo].[sub_checkActivations] 
	@memberid int,
	@bypassQueue bit = 0
AS

set nocount on

IF OBJECT_ID('tempdb..#firstInvoice') IS NOT NULL 
	DROP TABLE #firstInvoice
create table #firstInvoice (subscriberID int PRIMARY KEY, orgID int, invoiceNumber int)

IF OBJECT_ID('tempdb..#tblSubsToCheck') IS NOT NULL 
	DROP TABLE #tblSubsToCheck
create table #tblSubsToCheck (subscriberID int PRIMARY KEY, rootSubscriberID int, memberID int, orgID int, siteID int, subActivationCode char(1), statusCode char(1))


declare @membersToCheck TABLE (id int IDENTITY(1,1), memberID int PRIMARY KEY)
declare @tblSubsToMove TABLE (subscriberID int, statusCode char(1), reason varchar(50))
declare @activatedStatusID int, @nonActivatedStatusID int, @enteredByMemberID int, @followParentActivationOptionID int

select @activatedStatusID = statusID from dbo.sub_paymentStatuses where statusCode = 'P'
select @nonActivatedStatusID = statusID from dbo.sub_paymentStatuses where statusCode = 'N'
select @followParentActivationOptionID = subActivationID from dbo.sub_activationOptions where subActivationCode = 'F'
select @enteredByMemberID = memberID from ams_members where memberNumber = 'SYSTEM' and orgID = 1	

IF @memberID is null
BEGIN
	insert into #tblSubsToCheck (subscriberID, rootSubscriberID, memberID, orgID, siteID, subActivationCode, statusCode)
	select sub.subscriberID, sub.rootSubscriberID, m.activememberID as memberID, m.orgID, t.siteid, ao.subActivationCode, s.statusCode
	from sub_subscribers as sub
	inner join dbo.sub_statuses as s on s.statusID = sub.statusID
	inner join dbo.sub_activationOptions as ao on ao.subActivationID = sub.subActivationID
	inner join dbo.ams_members as m on m.memberid = sub.memberID
	inner join dbo.sub_subscriptions as ss on ss.subscriptionID = sub.subscriptionID
	inner join dbo.sub_Types as t on t.typeID = ss.typeID
	where sub.paymentStatusID = @nonActivatedStatusID
	and s.statusCode in ('A','P','I','E')
	and ao.subActivationCode in ('C','P','I','E','N','T') 
	order by sub.subscriberID
END
ELSE BEGIN
	insert into @membersToCheck (memberID)
	select m2.memberID
	from ams_members m
	inner join ams_members m2 on m.activeMemberID = m2.activeMemberID
		and m.memberID = @memberID

	insert into #tblSubsToCheck (subscriberID, rootSubscriberID, memberID, orgID, siteID, subActivationCode, statusCode)
	select sub.subscriberID, sub.rootSubscriberID, m.activememberID as memberID, m.orgID, t.siteid, ao.subActivationCode, s.statusCode
	from @membersToCheck mtc
	inner join sub_subscribers as sub on mtc.memberID = sub.memberID
	inner join dbo.sub_statuses as s on s.statusID = sub.statusID
	inner join dbo.sub_activationOptions as ao on ao.subActivationID = sub.subActivationID
	inner join dbo.ams_members as m on m.memberid = sub.memberID
	inner join dbo.sub_subscriptions as ss on ss.subscriptionID = sub.subscriptionID
	inner join dbo.sub_Types as t on t.typeID = ss.typeID
	where sub.paymentStatusID = @nonActivatedStatusID
	and s.statusCode in ('A','P','I','E')
	and ao.subActivationCode in ('C','P','I','E','N','T') 
	order by sub.subscriberID

END


CREATE INDEX IX_tblSubsToCheck on #tblSubsToCheck (subscriberID asc) INCLUDE (statusCode,subActivationCode)

-- N: No Reliance on payment
insert into @tblSubsToMove (subscriberID, statusCode, reason)
select tbl.subscriberID, tbl.statusCode, 'No reliance'
from #tblSubsToCheck as tbl
where tbl.subActivationCode = 'N'

-- P: This sub paid in full
insert into @tblSubsToMove (subscriberID, statusCode, reason)
select tbl.subscriberID, tbl.statusCode, 'this paid in full'
from #tblSubsToCheck as tbl
inner join dbo.tr_applications as tra on tra.itemID = tbl.subscriberID
	and tra.applicationTypeID = 17
	and tra.itemType = 'Dues'
	and tra.status = 'A'
cross apply dbo.fn_tr_transactionSalesWithDIT(tra.transactionID) as tsFull
where tbl.subActivationCode = 'P'
and tsFull.cache_amountAfterAdjustment = tsFull.cache_activePaymentAllocatedAmount
OPTION(RECOMPILE)

-- C: This sub and all children subs paid in full

; with addons as (
    select subscriberID as topLevelSubscriberID, subscriberID
    from #tblSubsToCheck tbl
    where tbl.subActivationCode = 'C'
    union all
    select topLevelSubscriberID, ss.subscriberID
    from addons s
    inner join sub_subscribers ss
	   on ss.parentSubscriberID = s.subscriberID
)
insert into @tblSubsToMove (subscriberID, statusCode, reason)
select tbl.subscriberID, tbl.statusCode, 'this and children paid in full'
from #tblSubsToCheck as tbl
inner join addons as rms
    on tbl.subscriberID = rms.topLevelSubscriberID
inner join dbo.tr_applications as tra on tra.itemID = rms.subscriberID
	and tra.applicationTypeID = 17
	and tra.itemType = 'Dues'
	and tra.status = 'A'
cross apply dbo.fn_tr_transactionSalesWithDIT(tra.transactionID) as tsFull
where tbl.subActivationCode = 'C'
group by tbl.subscriberID, tbl.statusCode
having sum(tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount) = 0
OPTION(RECOMPILE)

-- I: First invoice this sub appears on is paid
insert into @tblSubsToMove (subscriberID, statusCode, reason)
select tmp.subscriberID, tmp.statusCode, 'First invoice this sub'
from (
	select tbl.subscriberID, tbl.statusCode, i.assignedToMemberID, tbl.orgID, min(i.invoiceNumber) as firstInvNumber
	from #tblSubsToCheck as tbl
	inner join dbo.tr_applications as tra on tra.itemID = tbl.subscriberID
		and tra.applicationTypeID = 17
		and tra.itemType = 'Dues'
		and tra.status = 'A'
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = tra.transactionID
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	where tbl.subActivationCode = 'I'
	group by tbl.subscriberID, tbl.statusCode, i.assignedToMemberID, tbl.orgID
) as tmp 
inner join dbo.tr_invoices as i2 on i2.invoiceNumber = tmp.firstInvNumber and i2.statusID = 4
inner join ams_members m on m.memberID = i2.assignedToMemberID
	and m.orgID = tmp.orgID

-- E: First invoice this entire sub appears on is paid
insert into #firstInvoice (subscriberID, orgID, invoiceNumber)
select s.subscriberID, tstc.orgID, min(i.invoiceNumber) as invoiceNumber
from #tblSubsToCheck tstc
inner join dbo.sub_subscribers s on s.rootSubscriberID = tstc.rootSubscriberID
	and tstc.subActivationCode = 'E'
inner join dbo.tr_applications as tra on tra.itemID = s.subscriberID
	and tra.applicationTypeID = 17
	and tra.itemType = 'Dues'
	and tra.status = 'A'
inner join dbo.tr_invoiceTransactions as it on it.transactionID = tra.transactionID
inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
group by s.rootSubscriberID, s.subscriberID, tstc.orgID

CREATE INDEX IX_invoiceNumber on #firstInvoice (invoiceNumber asc)

insert into @tblSubsToMove (subscriberID, statusCode, reason)
select distinct tmp2.subscriberID, ts.statusCode, 'First invoice this entire sub'
from #firstInvoice as tmp2
inner join dbo.tr_invoices as i2 on i2.invoiceNumber = tmp2.invoiceNumber
	and i2.statusID = 4
inner join #tblSubsToCheck ts on ts.subscriberID = tmp2.subscriberID and ts.subActivationCode = 'E'
inner join ams_members m on m.memberID = i2.assignedToMemberID
	and m.orgID = tmp2.orgID

-- T: First Payment on This Sub
insert into @tblSubsToMove (subscriberID, statusCode, reason)
select tbl.subscriberID, tbl.statusCode, 'First payment this sub'
from #tblSubsToCheck as tbl
inner join dbo.tr_applications as tra on tra.itemID = tbl.subscriberID
	and tra.applicationTypeID = 17
	and tra.itemType = 'Dues'
	and tra.status = 'A'
cross apply dbo.fn_tr_transactionSalesWithDIT(tra.transactionID) as tsFull
where tbl.subActivationCode = 'T'
and ( tsFull.cache_activePaymentAllocatedAmount > 0 or tsFull.cache_amountAfterAdjustment = 0)
OPTION(RECOMPILE)

-- get the follow parents of subs already marked as activated
insert into @tblSubsToMove (subscriberID, statusCode, reason)
select s.subscriberID, st.statusCode, 'follow parents'
from sub_subscribers alreadyActivated
inner join sub_subscribers s
    on alreadyActivated.subscriberID = s.parentSubscriberID
    and alreadyActivated.paymentStatusID = @activatedStatusID
    and s.paymentStatusID = @nonActivatedStatusID
    and s.subActivationID = @followParentActivationOptionID
inner join dbo.sub_statuses st
	on st.statusID = s.statusID and st.statusCode <> 'D'
left outer join @tblSubsToMove alreadyFound
    on alreadyFound.subscriberID = s.subscriberID
where alreadyFound.subscriberID is null

-- get the follow parents of subs identified by previous inserts
insert into @tblSubsToMove (subscriberID, statusCode, reason)
select s.subscriberID, st.statusCode, 'follow parents'
from @tblSubsToMove ssmove
inner join sub_subscribers s
    on ssmove.subscriberID = s.parentSubscriberID
    and s.paymentStatusID = @nonActivatedStatusID
    and s.subActivationID = @followParentActivationOptionID
inner join dbo.sub_statuses st
	on st.statusID = s.statusID and st.statusCode <> 'D'
left outer join @tblSubsToMove alreadyFound
    on alreadyFound.subscriberID = s.subscriberID
where alreadyFound.subscriberID is null

if exists (select subscriberID from @tblSubsToMove) BEGIN

    while @@RowCount > 0
    begin
	   -- get the nested follow parents of subs identified by previous inserts
	   insert into @tblSubsToMove (subscriberID, statusCode, reason)
	   select s.subscriberID, st.statusCode, 'follow parents'
	   from @tblSubsToMove ssmove
	   inner join sub_subscribers s
		  on ssmove.subscriberID = s.parentSubscriberID
		  and s.paymentStatusID = @nonActivatedStatusID
		  and s.subActivationID = @followParentActivationOptionID
	   inner join dbo.sub_statuses st
		   on st.statusID = s.statusID and st.statusCode <> 'D'
	   left outer join @tblSubsToMove alreadyFound
		  on alreadyFound.subscriberID = s.subscriberID
	   where alreadyFound.subscriberID is null
    end


    update subs
    set subs.paymentStatusID = @activatedStatusID
    from dbo.sub_subscribers subs
    inner join @tblSubsToMove ts on ts.subscriberID = subs.subscriberID

    insert into dbo.sub_paymentStatusHistory(subscriberID, paymentStatusID, enteredByMemberID)
    select subscriberID, @activatedStatusID, @enteredByMemberID
    from @tblSubsToMove

    declare @minSubscriberID int, @currLoopMemberID int, @currLoopSubscriptionID int, @currLoopStatusCode char(1), @currLoopGroupID int, @tempCount int
    declare @prevSubscriberID int, @currLoopSiteID int, @insideResult int

    select @minSubscriberID=min(subscriberID)
    from @tblSubsToMove
    where statusCode in ('A','P')


    while @minSubscriberID is not null
    begin

	    select @currLoopSubscriptionID=s.subscriptionID, @currLoopMemberID=m.activeMemberID, @currLoopStatusCode=t.statusCode, @currLoopSiteID=st.siteID
	    from dbo.sub_subscribers s
	    inner join @tblSubsToMove t on t.subscriberID = s.subscriberID
	    inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID
	    inner join dbo.sub_types st on st.typeID = subs.typeID
	    inner join dbo.ams_members m on m.memberID = s.memberID
	    where s.subscriberID = @minSubScriberID
    	

	    select @tempCount=count(s.subscriberID)
	    from dbo.sub_subscribers s
	    inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID AND pst.statusCode = 'N'
	    inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @currLoopMemberID
	    where s.subscriptionID = @currLoopSubscriptionID
    	
	    IF @tempCount = 0
	    begin
		    select @currLoopGroupID=null
		    select @currLoopGroupID=groupID
		    from ams_groups
		    where groupCode like 'subWaitingPay_' + convert(varchar, @currLoopSubscriptionID) + '_tracking'

		    IF @currLoopGroupID is not null BEGIN
			    exec dbo.ams_deleteMemberGroup @currLoopMemberID, @currLoopGroupID
		    END
	    end

	    IF @currLoopStatusCode = 'A'
	    begin

		    select @prevSubscriberID=null
		    select @prevSubscriberID=s.subscriberID
		    from dbo.sub_subscribers s
		    inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode in ('A', 'I')
		    inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID 
		    inner join dbo.sub_types t on t.typeID = subs.typeID and t.siteID = @currLoopSiteID
		    inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @currLoopMemberID											
		    where s.subscriptionID = @currLoopSubscriptionID
		    and s.subscriberID <> @minSubscriberID

		    IF @prevSubscriberID is not null
		    BEGIN

			    EXEC sub_updateSubscriberStatus @prevSubscriberID, 'E', @currLoopSiteID, @enteredByMemberID, @insideResult OUTPUT

		    END

		    select @currLoopGroupID=null
		    select @currLoopGroupID=groupID
		    from ams_groups
		    where groupCode like 'subActive_' + convert(varchar, @currLoopSubscriptionID) + '_tracking'

		    IF @currLoopGroupID is not null
			    exec dbo.ams_createMemberGroup @currLoopMemberID, @currLoopGroupID, @bypassQueue
	    end
	    ELSE IF @currLoopStatusCode = 'P'
	    begin
		    select @currLoopGroupID=null
		    select @currLoopGroupID=groupID
		    from ams_groups
		    where groupCode like 'subPending_' + convert(varchar, @currLoopSubscriptionID) + '_tracking'

		    IF @currLoopGroupID is not null
			    exec dbo.ams_createMemberGroup @currLoopMemberID, @currLoopGroupID, @bypassQueue
	    end

	    select @minSubscriberID=min(subscriberID)
	    from @tblSubsToMove
	    where statusCode in ('A','P') and subscriberID > @minSubscriberID
    end
END

IF OBJECT_ID('tempdb..#firstInvoice') IS NOT NULL 
	DROP TABLE #firstInvoice

IF OBJECT_ID('tempdb..#tblSubsToCheck') IS NOT NULL 
	DROP TABLE #tblSubsToCheck


set nocount off

GO