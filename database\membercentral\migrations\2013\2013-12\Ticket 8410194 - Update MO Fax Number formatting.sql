set nocount on

/* STANDARDIZE PHONE FORMAT */

declare @tblPhones TABLE (phoneID int, phone varchar(40), phonenumonly varchar(40))
insert into @tblPhones (phoneID, phone, phonenumonly)

select mp.phoneID, mp.phone, dbo.fn_RegExReplace(mp.phone,'[^0-9]','')
from ams_memberPhones as mp
inner join ams_memberAddresses as ma on ma.addressID = mp.addressID
inner join ams_members as m on m.memberid = ma.memberid
inner join organizations as o on o.orgID = m.orgID
where o.orgcode = 'MO'
and len(mp.phone) = 10

update mp
set mp.phone = substring(tbl.phonenumonly,1,3) + '-' + substring(tbl.phonenumonly,4,3) + '-' + substring(tbl.phonenumonly,7,4)
from dbo.ams_memberPhones as mp
inner join @tblPhones as tbl on tbl.phoneID = mp.phoneID
where len(tbl.phonenumonly) = 10
GO

set nocount off