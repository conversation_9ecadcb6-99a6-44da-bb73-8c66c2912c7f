use seminarWeb
GO

ALTER PROC [dbo].[sw_getCreditsforSeminarList]
@seminarIDList varchar(max)

AS

-- get approved or pending credits for all seminars in list
SELECT distinct sac.seminarID, ca.code
FROM dbo.tblSeminarsAndCredit AS sac 
INNER JOIN dbo.fn_IntListToTable(@seminarIDList) as s on s.intValue = sac.seminarID
INNER JOIN dbo.tblCreditSponsorsAndAuthorities as csa on csa.CSALinkID = sac.CSALinkID
INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
INNER JOIN dbo.tblCreditStatuses AS cstat ON sac.statusID = cstat.statusID 
LEFT OUTER JOIN dbo.tblSeminarsSWLive as sswl on sswl.seminarID = s.intValue
LEFT OUTER JOIN dbo.tblSeminarsSWOD as sswod on sswod.seminarID = s.intValue
WHERE cstat.status IN ('Approved', 'Pending', 'Self-Submitting')
AND (
	sswl.liveid is not null
	OR
	sswod.ondemandID is not null and getdate() between sac.creditOfferedStartDate and sac.creditOfferedEndDate
	)
ORDER BY sac.seminarID, ca.code
GO
