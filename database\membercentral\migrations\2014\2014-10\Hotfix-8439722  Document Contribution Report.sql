select d.documentid, d.expertname, d.category, d.style as [Case Name], d.documentdate, d.state as [Contributing Association], d.dateentered, d.uploadpdfdate, 
	t.typeid, t.description as doctype, c.description as casetype, m.tlamemberstate, m.lastname, m.firstname,
	m.email, m.depoMemberDataID, d.disabled
FROM depoDocuments d 
INNER JOIN depomemberdata m ON d.DepomemberdataID = m.depomemberdataID 
INNER JOIN depoDocumentTypes t ON d.DocumentTypeID = t.TypeID
INNER JOIN depocasetypes c on d.casetypeid = c.casetypeid
WHERE d.disabled <> 'Y' 
and t.TypeID = 1
and t.TypeID = d.DocumentTypeID
and d.dateentered between '6/17/2014' and '10/13/2014 23:59:59.997'
ORDER BY m.LastName, m.FirstName