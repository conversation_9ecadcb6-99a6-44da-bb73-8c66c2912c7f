ALTER FUNCTION [dbo].[fn_varCharListToTable] (@varcharlist varchar(max), @delimiter varchar(1)) 
returns @VarcharTable table (autoid int IDENTITY (1,1), listitem varchar(max))
AS
begin

	declare @strAsXML as xml
	set @strAsXML = cast(('<x>'+replace(replace(@varcharlist,'&','&amp;'),@delimiter,'</x><x>')+'</x>') as xml)
	
	insert into @varchartable(listitem)
	select N.value('.','varchar(max)') as value
	from @strAsXML.nodes('x') as T(N)

	return
end
GO
