use membercentral
GO

if not exists(select * from sys.columns 
            where Name = N'clientFeeMemberID' and Object_ID = Object_ID(N'ref_referrals'))
begin
	alter table ref_referrals
		add clientFeeMemberID int
end
GO

declare 
	@row int, 
	@company varchar(300), 
	@membernumber varchar(40), 
	@memberID int, 
	@rc int,
	@referralID int

set @membernumber = 'LRISClientFees001'

exec @rc = dbo.ams_createMember 
				@orgID=2, 
				@memberTypeID=2, 
				@prefix='', 
				@firstname='Client', 
				@middlename='', 
				@lastname='Account', 
				@suffix='', 
				@professionalsuffix='', 
				@company='LRIS', 
				@memberNumber=@membernumber, 
				@status='A', 
				@memberID=@memberID OUTPUT
IF @@ERROR <> 0 or @rc <> 0 or @memberID = 0 or @memberID is null  goto on_error

if @memberID is not null and @memberID > 0 begin

	select
		@referralID = referralID
	from 
		ref_referrals r
		inner join 	cms_applicationInstances ai on
			ai.applicationInstanceID = r.applicationInstanceID		
			and ai.siteID = 2
		inner join cms_applicationTypes at on 
			at.applicationTypeID = ai.applicationTypeID

	update
		ref_referrals
	set
		clientFeeMemberID = @memberID
	where
		referralID = @referralID

end

goto on_success

on_error:
	print	'ERROR adding member account!'

on_success:
	print 'SUCCESS adding member account!'
GO


if not exists(select * from sys.columns 
            where Name = N'clientFeeGLAccountID' and Object_ID = Object_ID(N'ref_panels'))
begin
	alter table ref_panels
		add clientFeeGLAccountID int
end
GO

if not exists(select * from sys.columns 
            where Name = N'clientReferralAmount' and Object_ID = Object_ID(N'ref_panels'))
begin
	alter table ref_panels
		add clientReferralAmount money
end
GO

update 
	ref_panels
set
	clientReferralAmount = 20.00
where
	panelParentID is null

update 
	ref_panels
set
	clientReferralAmount = 0.00
where
	panelParentID is null
	and name in(	'Workers'' Compensation', 
					'Personal Injury - Defendant', 
					'Personal Injury - Plaintiff', 
					'Small Claims', 
					'Legal Malpractice - G', 
					'Medical Malpractice', 
					'Maritime - G', 
					'Elder Law - Physical Abuse',
					'DMV/Traffic',
					'Servicemembers Civil Relief Act',
					'Family Law - Modest Means',
					'SSI')

declare @clientFeeGLAccountID int

select @clientFeeGLAccountID = GLAccountID from tr_GLAccounts where orgID = 2 and accountName = 'LRIS Intake Fees' and status = 'A'

if @clientFeeGLAccountID is not null
	update 
		ref_panels
	set
		clientFeeGLAccountID  = @clientFeeGLAccountID
	where
		panelParentID is null

GO


/* ----------------------------------------------------------------------------------------------------------------------------- */

ALTER PROCEDURE [dbo].[ref_createPanel]
	@uid	varchar(50),
	@referralID	int,
	@name	varchar(255),
	@shortDesc	varchar(255) = NULL,
	@longDesc	varchar(max) = NULL,
	@statusID	int,
	@internalNotes	varchar(max) = NULL,
	@dateCreated	datetime = NULL,
	@dateCommitteeApproved	datetime = NULL,
	@dateBoardApproved	datetime = NULL,
	@dateBoardNotified	datetime = NULL,
	@dateReviewed	datetime = NULL,
	@surveyUrl	varchar(255) = NULL,
	@sendMail	bit = NULL,
	@maxNumMembers	int = NULL,
	@referralFeePercent	decimal(4,2) = NULL,
	@deductExpenseDesc	varchar(max) = NULL,
	@referralAmount	money = NULL,
	@panelParentID	int = NULL,
	@GLAccountID	int = NULL,
	@clientFeeGLAccountID int = NULL,
	@clientReferralAmount money = NULL,
	@isActive	bit = NULL,	
	@panelID int OUTPUT
AS

declare
	@rc int,
	@resourceTypeID int,
	@siteID int,
	@parentSiteResourceID int,
	@siteResourceID int

IF ( EXISTS (select panelID FROM dbo.ref_panels where name = @name and referralID = @referralID and statusID = 1))
	GOTO on_error
ELSE begin

	set @parentSiteResourceID = NULL

	select @resourceTypeID = dbo.fn_getResourceTypeID('ReferralPanel')

	select 
		@siteID = ai.siteID
	from 
		cms_applicationInstances ai
		inner join ref_referrals r on 
			r.applicationInstanceID = ai.applicationInstanceID
			and r.referralID = @referralID

	IF @panelParentID is not null
	begin
		select @parentSiteResourceID = siteResourceID from ref_panels where panelid = @panelParentID 
	end

	BEGIN TRAN

		exec @rc = cms_createSiteResource
				@resourceTypeID = @resourceTypeID,
				@siteResourceStatusID = 1,
				@siteID = @siteID,
				@isVisible = 1,
				@parentSiteResourceID = @parentSiteResourceID,
				@siteResourceID = @siteResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

		insert into dbo.ref_panels (
			[uid],
			referralID,
			name,
			shortDesc,
			longDesc,
			statusID,
			internalNotes,
			dateCreated,
			dateCommitteeApproved,
			dateBoardApproved,
			dateBoardNotified,
			dateReviewed,
			surveyUrl,
			sendMail,
			maxNumMembers,
			referralFeePercent,
			deductExpenseDesc,
			referralAmount,
			panelParentID,
			GLAccountID,
			isActive,
			siteResourceID,
			clientFeeGLAccountID,
			clientReferralAmount
		)
		values (
			@uid,
			@referralID,
			@name,
			@shortDesc,
			@longDesc,
			@statusID,
			@internalNotes,
			@dateCreated,
			@dateCommitteeApproved,
			@dateBoardApproved,
			@dateBoardNotified,
			@dateReviewed,
			@surveyUrl,
			@sendMail,
			@maxNumMembers,
			@referralFeePercent,
			@deductExpenseDesc,
			@referralAmount,
			@panelParentID,
			@GLAccountID,
			@isActive,
			@siteResourceID,
			@clientFeeGLAccountID,
			@clientReferralAmount
		)

		IF @@ERROR <> 0 GOTO on_error
		SELECT @panelID = SCOPE_IDENTITY()	

		IF @@TRANCOUNT > 0 COMMIT TRAN
		GOTO on_success
	
end

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @panelID = 0
	RETURN -1

on_success:
	RETURN 0
GO

/* ----------------------------------------------------------------------------------------------------------------------------- */

ALTER PROCEDURE [dbo].[ref_createClientReferral]
	@clientID	int,
	@representativeID	int = NULL,
	@memberID	int	= NULL,
	@enteredByMemberID	int,
	@sourceID	int,
	@otherSource varchar(100) = NULL,
	@counselorNotes	varchar(max) = NULL,
	@attorneyNotes	varchar(max) = NULL,
	@communicateLanguageID	int,
	@issueDesc	varchar(max),
	@agencyID	int = NULL,
	@sendSurvey	bit = NULL,
	@statusID	int,
	@typeID	int,
	@sendNewsBlog	bit = NULL,
	@clientReferralID int OUTPUT
AS

declare 
	@clientReferralDate	datetime,
	@dateCreated	datetime,
	@statusName varchar(255),
	@isReferred bit,
	@thisUUID varchar (36)
	
set @clientReferralDate = getDate()
set @dateCreated = getDate()
set @clientReferralDate = NULL

select
	@isReferred = isReferred
from
	ref_clientReferralStatus
where	
	clientReferralStatusID = @statusID
	
IF @isReferred = 1
	set @clientReferralDate = getDate()

set @thisUUID = cast(newid() as varchar (36))
set @thisUUID = (left(@thisUUID, 23) + right(@thisUUID,12))

insert into dbo.ref_clientReferrals (
	clientID,
	representativeID,
	memberID,
	enteredByMemberID,
	sourceID,
	otherSource,
	counselorNotes,
	attorneyNotes,
	communicateLanguageID,
	issueDesc,
	agencyID,
	sendSurvey,
	statusID,
	typeID,
	clientReferralDate,
	dateCreated,
	sendNewsBlog,
	uid
)
values (
	@clientID,
	@representativeID,
	@memberID,
	@enteredByMemberID,
	@sourceID,
	@otherSource,
	@counselorNotes,
	@attorneyNotes,
	@communicateLanguageID,
	@issueDesc,
	@agencyID,
	@sendSurvey,
	@statusID,
	@typeID,
	@clientReferralDate,
	@dateCreated,
	@sendNewsBlog,
	@thisUUID
)

IF @@ERROR <> 0 GOTO on_error
SELECT @clientReferralID = SCOPE_IDENTITY()	
GOTO on_success

-- error exit
on_error:
	SELECT @clientReferralID = 0
	RETURN -1

on_success:
	RETURN 0
GO

-- add scheduled task for daily cleanup
insert into membercentral.dbo.scheduledTasks (name, nextRunDate, interval, intervalTypeID, taskCFC, timeoutMinutes, disabled, siteid)
values ('Delete Referral Cards on File', '1/10/2015 1:00AM', 1, 1, 'model.scheduledTasks.tasks.deleteReferralCardsOnFile', 5, 1, 1)
GO

alter table ref_clients
	add callPaymentProcessed bit default(0)
GO


