use membercentral
SET nocount on

DECLARE @oldRoot varchar(500),@newRoot varchar(500), @oldDocumentRoot varchar(500), 
		@newDocumentRoot varchar(500), @oldorgcode varchar(10), @orgcode varchar(10), 
		@sitecode varchar(10), @siteID int, @orgID int, @rootSectionID int, 
		@userCreatedSectionResourceID int, @minSection varchar(50), @thisSectionID int, 
		@minpageID int, @pgResourceTypeID int, 
		@appPgResourceTypeID int, @siteResourceStatusID int, @thisPageID int, 
		@HTMLResourceTypeID int, @customPageApplicationTypeID int, @zoneIDMain int,
		@ppageTypeID int, @ppageName varchar(100), @ppageTitle varchar(100), @ppagemode int,
		@ppageDescription varchar(100), @pContentPath varchar(300), 
		@applicationInstanceID int, @siteresourceID int, 
		@penableSSL bit, @presourceid int, @rawContent varchar(max), @thisContentID int, 
		@thisResourceID int, @trashID int, @minpgGroupID int, @documentResourceTypeID int, 
		@doc_orgdocumentId int, @doc_sectionID int, @doc_contributorMemberID int, 
		@doc_docTitle varchar(200), @doc_docDesc varchar(200), @doc_filename varchar(100), 
		@doc_fileExt varchar(20), @doc_dateCreated datetime, @doc_newSiteResourceID int, 
		@minID int, @maxDocID int, @rc int, @documentLanguageID int, @documentVersionID int,
		@param1 varchar(max), @param2 varchar(max)
declare @cmd VARCHAR(1000)
DECLARE @tmpPages TABLE (pageID int, newPageID int, pageTypeID int, pageName varchar(100), pageTitle varchar(100), sortDescription varchar(500), pageStatusID int, dateCreated datetime, datemodified datetime, resourceid int, sectionTitle varchar(200), pageDescription varchar(100), enableSSL bit, pagemode smallint, ContentPath varchar(300), CFMLPath varchar(300), CFMLDirectoryPath varchar(300))
DECLARE @sections TABLE (sectionName varchar(50), sectionID int)
DECLARE @pgGroupID TABLE (groupID int)
DECLARE @docs TABLE (id int IDENTITY(1,1), orgdocumentId int, sectionID int, contributorMemberID int, docTitle varchar(200), docDesc varchar(200), filename varchar(100), fileExt varchar(20), dateCreated datetime, dateModified datetime);
DECLARE @filesToCopy TABLE (id int IDENTITY(1,1), param1 varchar(max), param2 varchar(max));
DECLARE @sectionsCheck TABLE (sectionName varchar(50), sectionCode varchar(50));
DECLARE @environ varchar(5)

/* ***** set codes *************** */
select @oldorgcode = 'WV'
select @orgcode = 'WV'
select @sitecode = 'WV'
select @environ = 'DEV'   -- DEV or PROD
/* ***** set codes *************** */


-- maximum orgdocumentID cannot be higher than 40000
select @maxDocID = max(orgdocumentID)
	from tlasites.trialsmith.dbo.orgdocuments d
	where d.orgcode = @oldorgcode
	and d.statusID = 1
IF @maxDocID > 40000 BEGIN
	print 'STOPPED - maximum orgdocumentID cannot be higher than 40000'
	GOTO on_done
END

-- duplicate section code check
-- this query should not return any rows. If so, need to clean up record 

insert into @sectionsCheck(sectionName, sectionCode)
select distinct ltrim(rtrim(isnull(p.sectionTitle,''))) as sectionTitle, dbo.fn_regExReplace(isnull(ltrim(rtrim(isnull(p.sectionTitle,''))) ,''),'[^A-Za-z0-9]','') sectionTitleCode
from tlasites.trialsmith.dbo.Pages as p
where p.orgcode = @oldorgcode
and p.pageTypeID = 11
and p.pagestatusID = 1
and p.pagename not in ('login','events','main','storemain','unavailable','norights','InactiveUser','SiteAgreement','MemberEditText','accountalreadysetup','fsArchive','ToySafetyInfo')
and p.pageid not in (
	select searchPageID from tlasites.trialsmith.dbo.orgMemberConfig where orgcode = p.orgcode and searchPageID is not null
	union
	select editPageID from tlasites.trialsmith.dbo.orgMemberConfig where orgcode = p.orgcode and editPageID is not null
	union
	select searchPageID from tlasites.trialsmith.dbo.jobBank where orgcode = p.orgcode and searchPageID is not null
	union
	select editPageID from tlasites.trialsmith.dbo.jobBank where orgcode = p.orgcode and editPageID is not null
	)

IF EXISTS (
	select sectionCode, count(*)
	from @sectionsCheck 
	group by sectionCode
	having count(*) > 1
) BEGIN
	select sectionCode, count(*)
	from @sectionsCheck 
	group by sectionCode
	having count(*) > 1

	print 'STOPPED - duplicate section codes would be created'
	GOTO on_done
END



IF @environ = 'PROD' BEGIN
	set @oldRoot = '\\tsfile1\f$\tlasites\www\'
	set @oldDocumentRoot = '\\tsfile1\f$\tlasites\docCatalog\'
	set @newRoot = '\\tsfile1\f$\platform\membercentral\'
	set @newDocumentRoot = '\\tsfile1\f$\platform\siteDocuments\'
END ELSE BEGIN
	set @oldRoot = '\\devserver\tlasites\www\'
	set @oldDocumentRoot = '\\devserver\tlasites\docCatalog\'
	set @newRoot = '\\devserver\f\platform\membercentral\'
	set @newDocumentRoot = '\\devserver\f\platform\siteDocuments\'
END

BEGIN TRAN

select @siteID = dbo.fn_getSiteIDFromSiteCode(@sitecode)
	IF @@ERROR <> 0 GOTO on_error
select @orgID = dbo.fn_getOrgIDFromOrgCode(@orgcode)
	IF @@ERROR <> 0 GOTO on_error
select @rootSectionID = dbo.fn_getRootSectionID(@siteid)
	IF @@ERROR <> 0 GOTO on_error
select @userCreatedSectionResourceID = dbo.fn_getResourceTypeID('UserCreatedSection')
	IF @@ERROR <> 0 GOTO on_error
select @pgResourceTypeID = dbo.fn_getResourceTypeId('UserCreatedPage')
	IF @@ERROR <> 0 GOTO on_error
select @appPgResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedPage')
	IF @@ERROR <> 0 GOTO on_error
select @HTMLResourceTypeID = dbo.fn_getResourceTypeID('UserCreatedContent')
	IF @@ERROR <> 0 GOTO on_error
select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')
	IF @@ERROR <> 0 GOTO on_error
select @customPageApplicationTypeID = dbo.fn_getApplicationTypeIDFromName('CustomPage')
	IF @@ERROR <> 0 GOTO on_error
select @zoneIDMain = dbo.fn_getZoneID('Main')
	IF @@ERROR <> 0 GOTO on_error
select @documentResourceTypeID = dbo.fn_getResourceTypeID('UserCreatedDocument')
	IF @@ERROR <> 0 GOTO on_error

-- Populate Page temp table
INSERT INTO @tmpPages (pageID, newPageID, pageTypeID, pageName, pageTitle, sortDescription, pageStatusID, dateCreated, datemodified, resourceid, sectionTitle, pageDescription, enableSSL, pagemode, ContentPath)
select p.pageid, null, p.pageTypeID, p.pageName, isnull(nullif(p.pageHeader,''),p.pagename) as pageTitle, p.sortDescription, p.pageStatusId, p.datecreated, p.datemodified, p.resourceid, ltrim(rtrim(isnull(p.sectionTitle,''))) as sectionTitle,
	left(p.pageDescription,100), isnull(p.enableSSL,0) as enableSSL, 
	case when p.showfullpage = 1 then dbo.fn_getModeId('Full') else null end,
	ContentPath = @oldRoot + p.orgcode + '\views\' + p.pageName + 'content.cfm'
from tlasites.trialsmith.dbo.Pages as p
where p.orgcode = @oldorgcode
and p.pageTypeID = 11
and p.pagestatusID = 1
and p.pagename not in ('login','events','main','storemain','unavailable','norights','InactiveUser','SiteAgreement','MemberEditText','accountalreadysetup','fsArchive','ToySafetyInfo')
and p.pageid not in (
	select searchPageID from tlasites.trialsmith.dbo.orgMemberConfig where orgcode = p.orgcode and searchPageID is not null
	union
	select editPageID from tlasites.trialsmith.dbo.orgMemberConfig where orgcode = p.orgcode and editPageID is not null
	union
	select searchPageID from tlasites.trialsmith.dbo.jobBank where orgcode = p.orgcode and searchPageID is not null
	union
	select editPageID from tlasites.trialsmith.dbo.jobBank where orgcode = p.orgcode and editPageID is not null
	)
order by p.pageName
	IF @@ERROR <> 0 GOTO on_error

/* ********************************* */
/* Create Sections */
/* ********************************* */
insert into @sections (sectionName)
select distinct sectionTitle from @tmpPages where len(sectionTitle) > 0
	IF @@ERROR <> 0 GOTO on_error

select @minSection = min(sectionName) from @sections
while @minSection is not null BEGIN
	print 'section: ' + @minSection

	EXEC @rc = dbo.cms_createPageSection @siteID=@siteID, 
		@sectionResourceTypeID = @userCreatedSectionResourceID, @ovTemplateID = null, @ovTemplateIDMobile = null,
		@ovModeID = null, @parentSectionID = @rootSectionID, @sectionName = @minSection,
		@sectionCode = @minSection, @inheritPlacements = 1, @sectionID = @thisSectionID OUTPUT
		IF @@ERROR <> 0 or @RC <> 0 OR @thisSectionID = 0 GOTO on_error
	update @sections set sectionID = @thisSectionID where sectionName = @minSection
		IF @@ERROR <> 0 GOTO on_error

	select @minSection = min(sectionName) from @sections where sectionName > @minSection
END

/* ********************************* */
/* Create Pages */
/* ********************************* */
SELECT @minpageID = min(pageID) from @tmpPages
WHILE @minpageID is not null BEGIN

	SELECT @ppageTypeID = pageTypeID, @ppageName = pageName, @ppageTitle = pageTitle,
		@ppagemode = pagemode, @ppageDescription = pageDescription, @pContentPath = ContentPath,
		@penableSSL = enableSSL, @presourceid = resourceid, 
		@thisSectionID = isnull(s.sectionID,@rootSectionID)
	FROM @tmpPages tmp
	LEFT OUTER JOIN @sections as s on s.sectionName = tmp.sectionTitle
	WHERE tmp.pageID = @minpageID

	print 'Page: ' + isnull(@ppageName,'')

	EXEC @rc = dbo.cms_createPage @siteid=@siteID, @languageID=1, @resourceTypeID=@pgResourceTypeID,
		@siteResourceStatusID=@siteResourceStatusID, @pgParentResourceID=null, 
		@isVisible=1, @sectionID=@thisSectionID, @ovTemplateID=null, @ovTemplateIDMobile=null, @ovModeID=@ppagemode,
		@pageName=@ppageName, @pageTitle=@ppageTitle, @pageDesc=@ppageDescription,
		@keywords='', @inheritPlacements=1,	@allowReturnAfterLogin=1, @checkReservedNames=1,
		@pageID=@thisPageID OUTPUT
	IF @@ERROR <> 0 or @RC <> 0 OR @thisPageID = 0 BEGIN
		print '*** ' + isnull(@ppageName,'') + ' page not migrated'
	END
	ELSE BEGIN
		select @rawContent = ltrim(dbo.fn_readfile(@pContentPath,0,0))

		exec @rc = dbo.cms_createContentObject
			@siteID = @siteID,
			@resourceTypeID = @HTMLResourceTypeID,
			@parentSiteResourceID = null,
			@siteResourceStatusID = @siteResourceStatusID,
			@isSSL = @penableSSL, 
			@isHTML=1, 
			@languageID = 1, 
			@isActive = 1, 
			@contentTitle = @ppageTitle, 
			@contentDesc = @ppageDescription, 
			@rawContent = @rawContent,
			@memberID = 3, 
			@contentID = @thisContentID OUTPUT,
			@siteResourceID = @thisResourceID OUTPUT
		IF @@ERROR <> 0 or @RC <> 0 OR @thisContentID = 0 GOTO on_error

		exec @rc = dbo.cms_createPageZoneResource
			@pageID = @thisPageID,
			@zoneID = @zoneIDMain,
			@siteResourceID = @thisResourceID,
			@pzrID = @trashID OUTPUT
		IF @@ERROR <> 0 or @RC <> 0 OR @trashID = 0 GOTO on_error

		insert into @pgGroupID (groupID)
		select g.groupID
		from tlasites.trialsmith.dbo.siteresourcerights as srr
		inner join tlasites.trialsmith.dbo.usergroups as ug on ug.usergroupid = srr.groupID 
			and ug.orgcode = @orgcode
		inner join dbo.ams_groups as g on g.groupName = 
			case ug.description when 'Guest Group' then 'Public' else ug.description end COLLATE Latin1_General_CI_AI
			and orgID = @orgID
		where srr.resourceID = @presourceid
		and srr.functionID = 1
			IF @@ERROR <> 0 GOTO on_error

		select @minpgGroupID = min(groupID) from @pgGroupID
		WHILE @minpgGroupID is not null BEGIN
			exec dbo.cms_createSiteResourceRight
				@siteID = @siteID,
				@siteResourceID = @thisResourceID, 
				@include = 1, 
				@functionID = 4, 
				@roleID = null, 
				@groupID = @minpgGroupID, 
				@memberID = null, 
				@inheritedRightsResourceID = null, 
				@inheritedRightsFunctionID = null, 
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 GOTO on_error
			select @minpgGroupID = min(groupID) from @pgGroupID where groupID > @minpgGroupID
		END

		DELETE FROM @pgGroupID
	END	

	SELECT @minpageID = min(pageID) from @tmpPages where pageID > @minpageID
END


SET nocount off

COMMIT TRAN

print 'Import completed successfully'
GOTO on_done

on_error:
	ROLLBACK TRAN
	print 'Import NOT completed successfully'

on_done:
