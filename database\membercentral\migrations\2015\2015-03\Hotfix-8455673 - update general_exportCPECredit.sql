use seminarweb
GO

ALTER PROC [dbo].[general_exportCPECredit]
	@sponsorID int,
	@publishingOrg varchar(15),
	@startdate datetime,
	@enddate datetime,
	@filename varchar(400)

as

declare @siteID int, @defaultTimeZoneID int

select @siteID = siteid, @defaultTimeZoneID = defaultTimeZoneID from membercentral.membercentral.dbo.sites where sitecode = @publishingOrg

-- set startdate to 00:00:00 of startdate, 00:00:00 of enddate
SELECT @startdate = DATEADD(dd, DATEDIFF(dd,0,@startdate), 0)
SELECT @enddate = DATEADD(dd, DATEDIFF(dd,0,dateadd(dd,1,@enddate)), 0)

IF OBJECT_ID('tempdb..#tmpEnrollments') IS NOT NULL 
	DROP TABLE #tmpEnrollments
IF OBJECT_ID('tempdb..#tmpCPE') IS NOT NULL 
	DROP TABLE #tmpCPE
IF OBJECT_ID('tempdb..##tmpCPE') IS NOT NULL 
	DROP TABLE ##tmpCPE

CREATE TABLE #tmpCPE(
	[memberNumber] [varchar](50) NOT NULL,
	[firstName] [varchar](75) NOT NULL,
	[lastName] [varchar](75) NOT NULL,
	[email] [varchar](255) NULL,
	[Address_Phone] [varchar](40) NULL,
	[NABP_ePID] [varchar](255) NULL,
	[DOB] [varchar](4) NULL,
	[orgCode] [varchar](5) NOT NULL,
	-- changed from not null to null
	[participantID] [int]  NULL,
	[pubOrg] [varchar](5) NOT NULL,
	[dateEnrolled] [smalldatetime] NOT NULL,
	[dateCompleted] [smalldatetime] NULL,
	[joinTime] [varchar](20) NULL,
	[exitTime] [varchar](20) NULL,
	[finaltimespent] [int] NULL,
	[seminarID] [int] NOT NULL,
	[seminarName] [varchar](250) NOT NULL,
	[SeminarType] [varchar](9) NULL,
	[ACPE_UAN] [varchar](80) NOT NULL,
	[Date_Of_Participation] [varchar](10) NULL,
	[Participant_Type] [varchar](1) NULL
)

-- get enrollments
SELECT u.depomemberdataid
	, pEnrolled.orgCode
	, pEnrolled.participantID
	, pPublisher.orgCode as pubOrg
	, e.dateEnrolled
	, e.dateCompleted
	, eswl.joinTime
	, eswl.exitTime
	, eac.finaltimespent
	, eac.idNumber
	, sem.seminarID
	, sem.seminarName
	, CASE WHEN sswod.onDemandID IS NOT NULL THEN 'On-Demand' WHEN swl.liveID IS NOT NULL THEN 'Webinar' ELSE NULL END AS SeminarType
	, sac.courseApproval AS ACPE_UAN
	, CASE WHEN swl.dateEnd IS NOT NULL THEN convert(varchar(10),swl.dateEnd,101) ELSE convert(varchar(10),e.datecompleted,101) END AS Date_Of_Participation
	, CASE 
		WHEN CSA.CSALinkID = 197 THEN 'P'
		WHEN CSA.CSALinkID = 205 THEN 'P'
		WHEN CSA.CSALinkID = 207 THEN 'T' 
		-- AZPA 
		WHEN CSA.CSALinkID = 212 THEN 'P'
		WHEN CSA.CSALinkID = 213 THEN 'T'
		-- TXRX
		WHEN CSA.CSALinkID = 149 THEN 'P'
		WHEN CSA.CSALinkID = 204 THEN 'P'
		WHEN CSA.CSALinkID = 183 THEN 'P' 
		WHEN CSA.CSALinkID = 208 THEN 'T'
		WHEN CSA.CSALinkID = 206 THEN 'T'
		ELSE NULL
		END AS Participant_Type	
INTO #tmpEnrollments
FROM dbo.tblenrollments as e
INNER JOIN dbo.tblParticipants pEnrolled on e.participantID = pEnrolled.participantID
INNER JOIN dbo.tblSeminars as sem on sem.seminarID = e.seminarID
LEFT OUTER JOIN dbo.tblSeminarsSWOD as sswod on sswod.seminarID = sem.seminarID
LEFT OUTER JOIN dbo.tblSeminarsSWLive as swl on swl.seminarID = sem.seminarID
INNER JOIN dbo.tblParticipants pPublisher on pPublisher.participantID = sem.participantID
LEFT OUTER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
INNER JOIN dbo.tblenrollmentsandcredit as eac on eac.enrollmentid = e.enrollmentid
INNER JOIN dbo.tblSeminarsAndCredit as sac on eac.seminarCreditID = sac.seminarCreditID
INNER JOIN dbo.tblCreditSponsorsAndAuthorities as csa on csa.csalinkid = sac.csalinkid
INNER JOIN dbo.tblCreditSponsors as cs on cs.sponsorid = csa.sponsorid and cs.sponsorID = @sponsorID
INNER JOIN dbo.tblCreditAuthorities as ca on ca.authorityid = csa.authorityid
INNER JOIN dbo.tblUsers as u on u.userid = e.userid
INNER JOIN trialsmith.dbo.depomemberdata as d on d.depomemberdataid = u.depomemberdataid 
WHERE e.passed = 1
AND e.datecompleted between @startdate and @enddate
AND e.isActive = 1
AND pPublisher.orgCode = @publishingOrg
AND eac.earnedCertificate = 1
AND (d.adminflag2 is null or d.adminflag2 <> 'Y')

-- get data where there is a link to the new platform via networkProfiles
insert into #tmpCPE (
	memberNumber, firstName, lastName, email, Address_Phone, NABP_ePID, DOB, orgCode, participantID, pubOrg, dateEnrolled, dateCompleted,
	joinTime, exitTime, finaltimespent, seminarID, seminarName, SeminarType, ACPE_UAN, Date_Of_Participation, Participant_Type 
)
select m.memberNumber, m.firstName, m.lastName
	, coalesce(d.email, MORXmd.email, OPAmd.email,ACAmd.email, IACPmd.email, TXRXmd.email, WSPAmd.email, AZPAmd.email) as email
	, coalesce(MORXmd.Address_Phone, OPAmd.Address_Phone, ACAmd.Address_Phone, IACPmd.Address_Phone, TXRXmd.[Address_Home Phone], WSPAmd.Address_Phone, AZPAmd.Address_Phone) AS Address_Phone
	, CASE 
		WHEN coalesce(MORXmd.nabpNumber, OPAmd.nabpNumber, ACAmd.nabpNumber, IACPmd.nabpNumber, TXRXmd.nabpNumber, WSPAmd.nabpNumber, AZPAmd.nabpNumber) IS NULL THEN tmp.idNumber
		ELSE coalesce(MORXmd.nabpNumber, OPAmd.nabpNumber, ACAmd.nabpNumber, IACPmd.nabpNumber, TXRXmd.nabpNumber, WSPAmd.nabpNumber, AZPAmd.nabpNumber) END AS NABP_ePID
	, CASE 
		WHEN isDate(MORXmd.dateOfBirth) = 1 THEN right('0' + rtrim(month(MORXmd.dateOfBirth)),2) + right('0' + rtrim(day(MORXmd.dateOfBirth)),2)
		WHEN isDate(OPAmd.dateOfBirth) = 1 THEN right('0' + rtrim(month(OPAmd.dateOfBirth)),2) + right('0' + rtrim(day(OPAmd.dateOfBirth)),2)
		WHEN isDate(ACAmd.dateOfBirth) = 1 THEN right('0' + rtrim(month(ACAmd.dateOfBirth)),2) + right('0' + rtrim(day(ACAmd.dateOfBirth)),2)
		WHEN isDate(AZPAmd.dateOfBirth) = 1 THEN right('0' + rtrim(month(AZPAmd.dateOfBirth)),2) + right('0' + rtrim(day(AZPAmd.dateOfBirth)),2)
		WHEN isDate(IACPmd.dateOfBirth) = 1 THEN right('0' + rtrim(month(IACPmd.dateOfBirth)),2) + right('0' + rtrim(day(IACPmd.dateOfBirth)),2)
		WHEN isDate(TXRXmd.dateOfBirth) = 1 THEN right('0' + rtrim(month(TXRXmd.dateOfBirth)),2) + right('0' + rtrim(day(TXRXmd.dateOfBirth)),2)
		WHEN isDate(WSPAmd.dateOfBirth) = 1 THEN right('0' + rtrim(month(WSPAmd.dateOfBirth)),2) + right('0' + rtrim(day(WSPAmd.dateOfBirth)),2)
		ELSE NULL END AS DOB
	, tmp.orgCode, tmp.participantID, tmp.pubOrg, tmp.dateEnrolled, tmp.dateCompleted
	, tmp.joinTime, tmp.exitTime, tmp.finaltimespent, tmp.seminarID, tmp.seminarName, tmp.SeminarType, tmp.ACPE_UAN
	, tmp.Date_Of_Participation, tmp.Participant_Type 
from #tmpEnrollments as tmp
INNER JOIN membercentral.membercentral.dbo.ams_networkProfiles as np ON tmp.depomemberdataID = np.depomemberdataID AND np.status = 'A'
INNER JOIN membercentral.membercentral.dbo.ams_memberNetworkProfiles as mnp ON np.profileID = mnp.profileID AND mnp.status = 'A'
INNER JOIN membercentral.membercentral.dbo.ams_members as m ON m.memberID = mnp.memberID AND m.status <> 'D'
LEFT OUTER JOIN membercentral.membercentral.dbo.vw_memberData_MORX as MORXmd on MORXmd.memberID = m.memberID
LEFT OUTER JOIN membercentral.membercentral.dbo.vw_memberData_ACA as ACAmd on ACAmd.memberID = m.memberID
LEFT OUTER JOIN membercentral.membercentral.dbo.vw_memberData_OPA as OPAmd on OPAmd.memberID = m.memberID
LEFT OUTER JOIN membercentral.membercentral.dbo.vw_memberData_IACP as IACPmd on IACPmd.memberID = m.memberID
LEFT OUTER JOIN membercentral.membercentral.dbo.vw_memberData_TXRX as TXRXmd on TXRXmd.memberID = m.memberID
LEFT OUTER JOIN membercentral.membercentral.dbo.vw_memberData_WSPA as WSPAmd on WSPAmd.memberID = m.memberID
LEFT OUTER JOIN membercentral.membercentral.dbo.vw_memberData_AZPA as AZPAmd on AZPAmd.memberID = m.memberID
LEFT OUTER JOIN trialsmith.dbo.depomemberdata as d on d.depomemberdataid = np.depomemberdataid

-- get data where there is a link to the new platform via acct in waiting
insert into #tmpCPE (
	memberNumber, firstName, lastName, email, Address_Phone, NABP_ePID, DOB, orgCode, participantID, pubOrg, dateEnrolled, dateCompleted,
	joinTime, exitTime, finaltimespent, seminarID, seminarName, SeminarType, ACPE_UAN, Date_Of_Participation, Participant_Type 
)
select m.memberNumber, m.firstName, m.lastName
	, coalesce(d.email, MORXmd.email, OPAmd.email,ACAmd.email, IACPmd.email, TXRXmd.email, WSPAmd.email, AZPAmd.email) as email
	, coalesce(MORXmd.Address_Phone, OPAmd.Address_Phone, ACAmd.Address_Phone, IACPmd.Address_Phone, TXRXmd.[Address_Home Phone], WSPAmd.Address_Phone, AZPAmd.Address_Phone) AS Address_Phone
	, CASE 
		WHEN coalesce(MORXmd.nabpNumber, OPAmd.nabpNumber, ACAmd.nabpNumber, IACPmd.nabpNumber, TXRXmd.nabpNumber, WSPAmd.nabpNumber, AZPAmd.nabpNumber) IS NULL THEN tmp.idNumber
		ELSE coalesce(MORXmd.nabpNumber, OPAmd.nabpNumber, ACAmd.nabpNumber, IACPmd.nabpNumber, TXRXmd.nabpNumber, WSPAmd.nabpNumber, AZPAmd.nabpNumber) END AS NABP_ePID
	, CASE 
		WHEN isDate(MORXmd.dateOfBirth) = 1 THEN right('0' + rtrim(month(MORXmd.dateOfBirth)),2) + right('0' + rtrim(day(MORXmd.dateOfBirth)),2)
		WHEN isDate(OPAmd.dateOfBirth) = 1 THEN right('0' + rtrim(month(OPAmd.dateOfBirth)),2) + right('0' + rtrim(day(OPAmd.dateOfBirth)),2)
		WHEN isDate(ACAmd.dateOfBirth) = 1 THEN right('0' + rtrim(month(ACAmd.dateOfBirth)),2) + right('0' + rtrim(day(ACAmd.dateOfBirth)),2)
		WHEN isDate(AZPAmd.dateOfBirth) = 1 THEN right('0' + rtrim(month(AZPAmd.dateOfBirth)),2) + right('0' + rtrim(day(AZPAmd.dateOfBirth)),2)
		WHEN isDate(IACPmd.dateOfBirth) = 1 THEN right('0' + rtrim(month(IACPmd.dateOfBirth)),2) + right('0' + rtrim(day(IACPmd.dateOfBirth)),2)
		WHEN isDate(TXRXmd.dateOfBirth) = 1 THEN right('0' + rtrim(month(TXRXmd.dateOfBirth)),2) + right('0' + rtrim(day(TXRXmd.dateOfBirth)),2)
		WHEN isDate(WSPAmd.dateOfBirth) = 1 THEN right('0' + rtrim(month(WSPAmd.dateOfBirth)),2) + right('0' + rtrim(day(WSPAmd.dateOfBirth)),2)
		ELSE NULL END AS DOB
	, tmp.orgCode, tmp.participantID, tmp.pubOrg, tmp.dateEnrolled, tmp.dateCompleted,
	tmp.joinTime, tmp.exitTime, tmp.finaltimespent, tmp.seminarID, tmp.seminarName, tmp.SeminarType, tmp.ACPE_UAN, 
	tmp.Date_Of_Participation, tmp.Participant_Type 
from #tmpEnrollments as tmp
INNER JOIN trialsmith.dbo.depomemberdata as d on d.depomemberdataid = tmp.depomemberdataid and d.MCMemberIDtemp is not null
INNER JOIN membercentral.membercentral.dbo.ams_members as m ON m.memberID = d.MCMemberIDtemp AND m.status <> 'D'
LEFT OUTER JOIN membercentral.membercentral.dbo.vw_memberData_MORX as MORXmd on MORXmd.memberID = m.memberID
LEFT OUTER JOIN membercentral.membercentral.dbo.vw_memberData_ACA as ACAmd on ACAmd.memberID = m.memberID
LEFT OUTER JOIN membercentral.membercentral.dbo.vw_memberData_OPA as OPAmd on OPAmd.memberID = m.memberID
LEFT OUTER JOIN membercentral.membercentral.dbo.vw_memberData_IACP as IACPmd on IACPmd.memberID = m.memberID
LEFT OUTER JOIN membercentral.membercentral.dbo.vw_memberData_TXRX as TXRXmd on TXRXmd.memberID = m.memberID
LEFT OUTER JOIN membercentral.membercentral.dbo.vw_memberData_WSPA as WSPAmd on WSPAmd.memberID = m.memberID
LEFT OUTER JOIN membercentral.membercentral.dbo.vw_memberData_AZPA as AZPAmd on AZPAmd.memberID = m.memberID

-- get events data
insert into #tmpCPE (
	memberNumber, firstName, lastName, email, Address_Phone, NABP_ePID, DOB, orgCode, participantID, pubOrg, dateEnrolled, dateCompleted,
	joinTime, exitTime, finaltimespent, seminarID, seminarName, SeminarType, ACPE_UAN, Date_Of_Participation, Participant_Type 
)
SELECT m2.memberNumber, m2.firstName, m2.lastName, md.email
	, CASE 
		WHEN md.[Address_Home Phone] is not null and len(md.[Address_Home Phone]) > 0 THEN md.[Address_Home Phone]
		WHEN md.[Address_Work Phone] is not null and len(md.[Address_Work Phone]) > 0 THEN md.[Address_Work Phone]
		ELSE ''
		END AS Address_Phone
	, replace(md.NABPNumber, 'nabp', '') AS NABP_ePID
	, right('0' + rtrim(month(md.dateOfBirth)),2) + right('0' + rtrim(day(md.dateOfBirth)),2) AS DOB
	, o.orgcode, null as participantID, o.orgcode as pubOrg, convert(varchar(10),et.endTime,101) as dateEnrolled
	, convert(varchar(10),et.endTime,101) AS dateCompleted, null as joinTime, null as exitTime, null as finaltimespent
	, e.eventid as seminarID, cl.contentTitle as seminarName, 'Live' AS SeminarType
	, ec.approvalNum AS ACPE_UAN, convert(varchar(10),et.endTime,101) AS Date_Of_Participation
	, CASE WHEN ec.ASID = 5 THEN 'P' WHEN ec.ASID = 38 THEN 'T' ELSE NULL END AS Participant_Type
FROM membercentral.membercentral.dbo.ev_events as e	
INNER JOIN membercentral.membercentral.dbo.ev_registration as r ON e.eventID = r.eventID AND e.siteID = @siteID
INNER JOIN membercentral.membercentral.dbo.ev_times as et ON et.eventID = e.eventID AND et.timeZoneID = @defaultTimeZoneID
INNER JOIN membercentral.membercentral.dbo.ev_registrants as er ON r.registrationID = er.registrationID
INNER JOIN membercentral.membercentral.dbo.ams_members as m ON m.memberID = er.memberID
INNER JOIN membercentral.membercentral.dbo.ams_members as m2 ON m2.memberID = m.activeMemberID
INNER JOIN membercentral.membercentral.dbo.organizations as o ON o.orgID = m.orgID
LEFT OUTER JOIN membercentral.membercentral.dbo.vw_memberData_TXRX as md on md.memberID = m2.memberID
INNER JOIN membercentral.membercentral.dbo.crd_requests as erc ON erc.registrantID = er.registrantID and erc.creditAwarded = 1
INNER JOIN membercentral.membercentral.dbo.crd_offeringTypes as ect ON ect.offeringTypeID = erc.offeringTypeID 
INNER JOIN membercentral.membercentral.dbo.crd_offerings as ec ON ec.offeringID = ect.offeringID 
INNER JOIN membercentral.membercentral.dbo.cms_content as c on c.contentid = e.eventcontentID
INNER JOIN membercentral.membercentral.dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID AND cl.languageID = 1
WHERE et.endTime BETWEEN @startdate AND @enddate

-- final results
SELECT DISTINCT
	dbo.fn_csvSafeString(memberNumber) as [Member Number]
	, dbo.fn_csvSafeString(firstName) AS [First Name]
	, dbo.fn_csvSafeString(lastName) AS [Last Name]
	, dbo.fn_csvSafeString(seminarName) AS [SeminarName]
	, dbo.fn_csvSafeString(seminarType) AS [Seminar Type]
	, orgCode AS [Signed Up On]
	, dbo.fn_csvSafeString(email) AS [Email]
	, dbo.fn_csvSafeString(Address_Phone) AS [Phone Number]
	, convert(varchar(10),dateEnrolled,101) as [Date Enrolled]
	, convert(varchar(10),dateCompleted,101) as [Date Completed]
	, dbo.fn_csvSafeString(joinTime) AS [Join Time]
	, dbo.fn_csvSafeString(exitTime) AS [Exit Time]
	, finaltimespent AS [Minutes Spent In Course]
	, dbo.fn_csvSafeString('I') as [Action] 
	, dbo.fn_csvSafeString(NABP_ePID) as NABP_ePID
	, dbo.fn_csvSafeString(DOB) as DOB
	, dbo.fn_csvSafeString(ACPE_UAN) as ACPE_UAN
	, dbo.fn_csvSafeString(Date_Of_Participation) as Date_Of_Participation
	, Participant_Type as Participant_Type
	, '' AS Participant_Count
INTO ##tmpCPE
FROM #tmpCPE
ORDER BY [Last Name], OrgCode

-- export data
DECLARE @cmd varchar(6000)
declare @tmpBCP TABLE (theoutput varchar(max))
set @cmd = 'bcp ##tmpCPE out ' + @filename + ' -c -t, -T -S' + CAST(serverproperty('servername') as varchar(20))
insert into @tmpBCP (theoutput)
exec master..xp_cmdshell @cmd	

-- return count of records
SELECT count(*) AS returnCount
FROM ##tmpCPE

-- get fields returned
EXEC tempdb.dbo.SP_COLUMNS ##tmpCPE


IF OBJECT_ID('tempdb..#tmpEnrollments') IS NOT NULL 
	DROP TABLE #tmpEnrollments
IF OBJECT_ID('tempdb..#tmpCPE') IS NOT NULL 
	DROP TABLE #tmpCPE
IF OBJECT_ID('tempdb..##tmpCPE') IS NOT NULL 
	DROP TABLE ##tmpCPE
GO
