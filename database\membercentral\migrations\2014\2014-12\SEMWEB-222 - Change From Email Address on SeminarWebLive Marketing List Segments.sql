use seminarWeb
GO

ALTER PROC [dbo].[sw_addParticipant]
@orgcode varchar(5)

AS

-- lookup participantID
DECLARE @participantID int
SELECT @participantID = dbo.fn_getParticipantIDFromOrgcode(@orgcode)

-- add to participants if not there
IF @participantID = 0 BEGIN

	INSERT INTO dbo.tblParticipants (orgcode, showUSD, supportPhone, supportEmail, 
		isConf, isSWOD, isSWTL, isSWL, isSearch, isMyCLE, catalogURL, wddxTimeZones, 
		emailFrom, brandHome, brandHomeTab, brandConf, brandConfTab, brandSWL, 
		brandSWLTab, brandSWOD, brandSWODTab, brandSWTL, brandSWTLTab, brandMyCle, 
		brandMyCleTab)
	SELECT @orgcode, 0, '************', '<EMAIL>', 0, 0, 0, 0, 0, 0, 
		(select top 1 url + '/seminarweb' from trialsmith.dbo.depoTLA where state = @orgcode), 
		'<wddxPacket version=''1.0''><header/><data><struct><var name=''supporting''><array length=''3''><string>P</string><string>M</string><string>E</string></array></var><var name=''default''><string>C</string></var></struct></data></wddxPacket>', 
		'SeminarWeb', 'CLE Catalog', 'CLE Catalog', 'Live Conferences & Events',
		'Conferences & Events', 'Live Webinars & Teleconferences', 'Live Webinars', 
		'Self-Paced Online Seminars', 'Self-Paced Online', 'Library of Titles, Papers, and Multimedia', 
		'Title Library', 'My CLE', 'My CLE'

	insert into lyris.trialslyris1.dbo.sw_marketing (orgcode,list,email)
	values (@orgcode,'seminarweblive','<EMAIL>')

END
GO
