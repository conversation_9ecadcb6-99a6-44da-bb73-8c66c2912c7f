use membercentral
GO

ALTER PROC [dbo].[tr_createTransaction_allocation]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@status varchar(20),
@amount money,
@transactionDate datetime,
@paymentTransactionID int,
@saleTransactionID int,	-- this can be a sale, tax, adjustment, or DIT
@ovBatchID int, -- optional override
@transactionID int OUTPUT

AS

set nocount on

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- reset output param
	select @transactionID = 0

	-- no zero dollar allocations
	if @amount = 0
		RAISERROR('amount is 0', 16, 1);

	-- ensure amount is 2 decimals
	select @amount = cast(@amount as decimal(10,2))

	-- ensure @paymentTransactionID is a payment
	IF NOT EXISTS (select transactionID from dbo.tr_transactions where transactionID = @paymentTransactionID and typeID = 2)
		RAISERROR('paymentTransactionID is not a payment', 16, 1);

	-- ensure @saleTransactionID is either a sale, sales tax, positive adjustment, or pos DIT
	IF NOT EXISTS (
		select t.transactionID
		from dbo.tr_transactions as t
		inner join dbo.tr_glAccounts as glDeb on glDeb.GLAccountID = t.debitGLAccountID
		where t.transactionID = @saleTransactionID
		and t.typeID in (1,3,7)
		and glDeb.GLCode = 'ACCOUNTSRECEIVABLE' 
		and glDeb.isSystemAccount = 1
		) AND NOT EXISTS (
			select t.transactionID
			from dbo.tr_transactions as t
			inner join dbo.tr_glAccounts as glDeb on glDeb.GLAccountID = t.debitGLAccountID
			inner join dbo.tr_glAccountTypes as glt on glt.accountTypeID = glDeb.accountTypeID
			where t.transactionID = @saleTransactionID
			and t.typeID = 10
			and glt.accountType = 'Liability' 
			and glDeb.isSystemAccount = 1
		)
	RAISERROR('saleTransactionID is not a sale, sales tax, positive adjustment, or deferred transfer', 16, 1);

	-- get info from payment transaction
	declare @assignedToMemberID int, @ownedByOrgID int, @payProfileID int, @payProfileCode varchar(20),
		@payCashGLID int, @payCashGLName varchar(200)
	select @assignedToMemberID = t.assignedToMemberID, 
		@ownedByOrgID = t.ownedByOrgID,
		@payProfileID = mp.profileID,
		@payProfileCode = mp.profileCode,
		@payCashGLID = glDeb.GLAccountID,
		@payCashGLName = glDeb.AccountName
	from dbo.tr_transactions as t
	inner join dbo.tr_transactionPayments as tp on tp.transactionID = t.transactionID
	inner join dbo.mp_profiles as mp on mp.profileID = tp.profileID
	inner join dbo.tr_GLAccounts as glDeb on glDeb.GLAccountID = t.debitGLAccountID
	where t.transactionID = @paymentTransactionID

	-- dont assume memberid is the active one. get the active one.
	select @assignedToMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @assignedToMemberID 
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	-- if amount is positive, allocating. Debit is DEP, Credit is AR.
	-- if amount is negative, deallocating. Debit is AR, Credit is DEP.
	declare @debitGLAccountID int, @creditGLAccountID int, @ARGLAID int, @DEPGLAID int
	select @ARGLAID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and isSystemAccount = 1
		and GLCode = 'ACCOUNTSRECEIVABLE'
		and [status] = 'A'
	select @DEPGLAID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and isSystemAccount = 1
		and GLCode = 'DEPOSITS'
		and [status] = 'A'
	if @amount > 0 BEGIN
		select @debitGLAccountID = @DEPGLAID
		select @creditGLAccountID = @ARGLAID
	END
	ELSE BEGIN
		select @debitGLAccountID = @ARGLAID
		select @creditGLAccountID = @DEPGLAID
	END

	-- ensure we have active debit/credit accts
	IF @debitGLAccountID is null or @creditGLAccountID is null
		RAISERROR('debitGLAccountID or creditGLAccountID is null', 16, 1);

	-- if allocating to a sale on an open/pending invoice, close it. Only closed inv can accept payment.
	-- no invoice if allocating to a DIT
	declare @invoiceID int, @invstatus varchar(50)
	select @invoiceID=i.invoiceID, @invstatus=ins.status
		from dbo.tr_invoices as i
		inner join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
		inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
		where it.transactionID = @saleTransactionID
	IF @invoiceID is not null and @amount > 0 AND @invstatus <> 'Closed'
		EXEC dbo.tr_closeInvoice @enteredByMemberID=@recordedByMemberID, @invoiceIDList=@invoiceID

	-- insert into transactions
	-- ensure amount is abs
	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
		amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
		typeID, accrualDate, debitGLAccountID, creditGLAccountID)
	VALUES (@ownedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID(@status), null, null, 
		abs(@amount), getdate(), @transactionDate, @assignedToMemberID, @recordedByMemberID, @statsSessionID, 
		5, @transactionDate, @debitGLAccountID, @creditGLAccountID)
		select @transactionID = SCOPE_IDENTITY()

	-- insert into relationships
	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
	VALUES (dbo.fn_tr_getRelationshipTypeID('AllocPayTrans'), @transactionID, @paymentTransactionID)

	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
	VALUES (dbo.fn_tr_getRelationshipTypeID('AllocSaleTrans'), @transactionID, @saleTransactionID)

	-- add allocation to batch
	-- if batch is passed in, put on that batch (if that batch is open).
	-- if no batch passed in, put on same batch as payment (if that batch is open).
	-- otherwise, put on daily exception batch
	declare @allocBatchID int, @batchCode varchar(40), @batchName varchar(400)
	IF @ovBatchID is not null
		select @allocBatchID = batchID
		from dbo.tr_batches 
		where batchID = @ovBatchID
		and statusID = 1
	ELSE	
		select @allocBatchID = b.batchID
		from dbo.tr_batchTransactions as bt
		inner join dbo.tr_batches as b on b.batchID = bt.batchID
		where bt.transactionID = @paymentTransactionID
		and b.statusID = 1
	IF @allocBatchID is null BEGIN
		select @batchCode = CONVERT(CHAR(8),@transactionDate,112) + '_' + cast(@payProfileID as varchar(10)) + '_' + cast(@payCashGLID as varchar(10)) + '_EX'
		select @batchName = CONVERT(CHAR(8),@transactionDate,112) + ' ' + @payProfileCode + ' ' + @payCashGLName + ' Exceptions'
		select @allocBatchID = b.batchID
			from dbo.tr_batches as b
			where b.orgID = @ownedByOrgID
			and b.batchCode = @batchCode
			and b.statusID = 1
			and b.isSystemCreated = 1
		IF @allocBatchID is null 
			EXEC dbo.tr_createBatch @orgID=@ownedByOrgID, @payProfileID=@payProfileID, @batchCode=@batchCode, 
				@batchName=@batchName, @controlAmt=0, @controlCount=0, @depositDate=@transactionDate, 
				@isSystemCreated=1, @createdByMemberID=null, @batchID=@allocBatchID OUTPUT 
	END
	INSERT INTO dbo.tr_batchTransactions (batchID, transactionID)
	VALUES (@allocBatchID, @transactionID)

	-- update payment cache	
	declare @refundableAmount money, @allocatedAmount money
	select @refundableAmount = refundableAmount from dbo.fn_tr_getRefundableAmountofPayment(@paymentTransactionID,null)
	select @allocatedAmount = allocatedAmount from dbo.fn_tr_getAllocatedAmountofPayment(@paymentTransactionID,null)

	update dbo.tr_transactionPayments
	set cache_allocatedAmountOfPayment = @allocatedAmount,
		cache_refundableAmountOfPayment = @refundableAmount
	where transactionID = @paymentTransactionID

	-- update sales cache
	-- if allocating to an adjustment, update cache of sale or tax it is adjusting
	declare @stTypeID int, @stID int, @activePaymentAllocatedAmount money, @pendingPaymentAllocatedAmount money
	select @stTypeID = typeID from dbo.tr_transactions where transactionID = @saleTransactionID 
	IF @stTypeID in (1,7,10)
		select @stID = @saleTransactionID
	ELSE
		select @stID = tSale.transactionID
			from dbo.tr_transactions as tSale
			inner join dbo.tr_relationships as tR on tR.appliedToTransactionID = tSale.transactionID and tR.transactionID = @saleTransactionID
			inner join dbo.tr_relationshipTypes as trt on trt.typeID = tR.typeID and trt.type = 'AdjustTrans'

	select @activePaymentAllocatedAmount = activePaymentAllocatedAmount,
		@pendingPaymentAllocatedAmount = pendingPaymentAllocatedAmount
	from dbo.fn_tr_getAllocatedAmountofSale(@stID)

	UPDATE dbo.tr_transactionSales
	SET cache_activePaymentAllocatedAmount = @activePaymentAllocatedAmount,
		cache_pendingPaymentAllocatedAmount = @pendingPaymentAllocatedAmount
	WHERE transactionID = @stID

	-- update invoiceTransactions cache
	select @activePaymentAllocatedAmount = null, @pendingPaymentAllocatedAmount = null
	select @activePaymentAllocatedAmount = activePaymentAllocatedAmount,
		@pendingPaymentAllocatedAmount = pendingPaymentAllocatedAmount
	from dbo.fn_tr_getAllocatedAmountofSaleOrAdj(@saleTransactionID,null)
	
	UPDATE dbo.tr_invoiceTransactions
	SET cache_activePaymentAllocatedAmount = @activePaymentAllocatedAmount,
		cache_pendingPaymentAllocatedAmount = @pendingPaymentAllocatedAmount
	WHERE transactionID = @saleTransactionID

	-- check the in-bound rules.
	-- sale - new cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount must be between 0 and cache_amountAfterAdjustment
	-- payment - new cache_allocatedAmountOfPayment must be between 0 and cache_refundableAmountOfPayment
	IF NOT EXISTS (select saleID from dbo.tr_transactionSales where transactionID = @stID and cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount between 0 and cache_amountAfterAdjustment)
		OR NOT EXISTS (select itID from dbo.tr_invoiceTransactions where transactionID = @saleTransactionID and cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount between 0 and cache_invoiceAmountAfterAdjustment)
		OR NOT EXISTS (select paymentID from dbo.tr_transactionPayments where transactionID = @paymentTransactionID and cache_allocatedAmountOfPayment between 0 and cache_refundableAmountOfPayment)
		RAISERROR('in-bounds checking failed', 16, 1);

	-- cleanup invoice
	-- if invoice is closed and is now fully paid with active payments, mark it as paid
	-- if invoice is paid and is now not fully paid with active payments, mark it as closed
	declare @amtDueNoPendingOnInvoice money
	select @invoiceID=i.invoiceID, @invstatus=ins.status
		from dbo.tr_invoices as i
		inner join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
		inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
		where it.transactionID = @saleTransactionID
	select @amtDueNoPendingOnInvoice = sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount)
		from dbo.tr_invoiceTransactions as it
		inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
		where it.invoiceID = @invoiceID
		and t.statusID <> 2
	IF @invoiceID is not null and @invstatus = 'closed' and @amtDueNoPendingOnInvoice = 0 BEGIN
		update dbo.tr_invoices
		set statusID = 4, payProfileID = null
		where invoiceID = @invoiceID
		
		insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
		values (@invoiceID, getdate(), 4, 3, @recordedByMemberID)
	END
	IF @invoiceID is not null and @invstatus = 'paid' and @amtDueNoPendingOnInvoice > 0 BEGIN
		update dbo.tr_invoices
		set statusID = 3
		where invoiceID = @invoiceID

		insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
		values (@invoiceID, getdate(), 3, 4, @recordedByMemberID)
	END

	-- update credit balances
	EXEC dbo.tr_updateCreditBalanceByMember @memberID=@assignedToMemberID

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @transactionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[tr_allocateToInvoice]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@amount money,
@transactionDate datetime,
@paymentTransactionID int,
@invoiceID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- amt needs to be positive.
	IF @amount <= 0
		RAISERROR('amount is not more than 0', 16, 1);

	-- invoice must be closed to accept payment.
	IF NOT EXISTS (select invoiceID from dbo.tr_invoices where invoiceID = @invoiceID and statusID = 3)
		RAISERROR('invoice is not closed', 16, 1);

	-- dont assume memberid is the active one. get the active one.
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	-- dont assume entire amount can be allocated from payment. adjust the cap if needed.
	declare @unallocatedAmount money
	select @unallocatedAmount = tp.cache_refundableAmountOfPayment - tp.cache_allocatedAmountOfPayment
		from dbo.tr_transactionPayments as tp
		where tp.transactionID = @paymentTransactionID
	IF @amount > @unallocatedAmount
		set @amount = @unallocatedAmount

	-- ensure amount is 2 decimals
	select @amount = cast(@amount as decimal(10,2))

	-- use numeric(9,2) instead of money to prevent 4 digit money values
	declare @tblOpenTran TABLE (autoid int IDENTITY(1,1), itID int, transactionid int, maxAmtToAllocate numeric(10,2), PITSaleTID int, PITAllocAmt numeric(10,2), newAllocTID int)

	-- get open transactions on invoice
	insert into @tblOpenTran (itID, transactionID, maxAmtToAllocate)
	select it.itID, it.transactionID, it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount
	from dbo.tr_invoiceTransactions as it
	inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
	where it.invoiceID = @invoiceID
	and t.statusID <> 2
	and t.typeID <> 8
	order by it.itID

	-- group by PITTaxTrans. sales and adj to sales just put the transactionid
	update tbl
	set tbl.PITSaleTID = tbl.transactionID
	from @tblOpenTran as tbl
	inner join dbo.tr_transactions as t on t.transactionID = tbl.transactionID and t.typeID = 1

	update tbl
	set tbl.PITSaleTID = tbl.transactionID
	from @tblOpenTran as tbl
	inner join dbo.tr_transactions as t on t.transactionID = tbl.transactionID and t.typeID = 3
	inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
	inner join dbo.tr_transactions as tSale on tSale.transactionID = r.appliedToTransactionID and tSale.typeID = 1
	where tbl.PITSaleTID is null

	update tbl
	set tbl.PITSaleTID = tbl2.transactionID
	from @tblOpenTran as tbl
	inner join dbo.tr_relationships as r on r.transactionID = tbl.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'PITTaxTrans'
	inner join @tblOpenTran as tbl2 on tbl2.transactionID = r.appliedToTransactionID
	where tbl.PITSaleTID is null

	-- did we miss any PITTaxTrans records?
	IF EXISTS (select itID from @tblOpenTran where PITSaleTID is null)
		RAISERROR('PITSaleTID is missing', 16, 1);
	
	-- loop over each group to get allocation amount and % split
	declare @totalAmountLeftToAllocate money, @grpTID int, @grpMaxAmtToAllocate money, @a_amount money
	select @totalAmountLeftToAllocate = @amount
	select @grpTID = min(PITSaleTID) from @tblOpenTran
	while @grpTID is not null BEGIN

		-- how much can we allocate to this group of transactions?
		select @grpMaxAmtToAllocate = sum(MaxAmtToAllocate) from @tblOpenTran where PITSaleTID = @grpTID
		IF @totalAmountLeftToAllocate > @grpMaxAmtToAllocate
			select @a_amount = @grpMaxAmtToAllocate
		ELSE
			select @a_amount = @totalAmountLeftToAllocate

		-- calc amount to allocate for each transaction
		update @tblOpenTran
		set PITAllocAmt = case 
						when @a_amount = 0 then 0 
						else ((maxAmtToAllocate/ @grpMaxAmtToAllocate) * @a_amount) 
						end
		WHERE PITSaleTID = @grpTID

		-- handle remainders from percentage calculations
		-- put any diff in the 1st item that can accept it
		declare @sumAllocAmount numeric(10,2), @amountDiff numeric(10,2)
		select @sumAllocAmount = sum(PITAllocAmt) from @tblOpenTran WHERE PITSaleTID = @grpTID
		IF @sumAllocAmount <> abs(@a_amount) BEGIN
			select @amountDiff = abs(@a_amount) - @sumAllocAmount

			update top (1) @tblOpenTran
			set PITAllocAmt = PITAllocAmt + @amountDiff
			where PITAllocAmt + @amountDiff <= maxAmtToAllocate
		END

		select @totalAmountLeftToAllocate = @totalAmountLeftToAllocate - @a_amount
		if @totalAmountLeftToAllocate <= 0
			break

		select @grpTID = min(PITSaleTID) from @tblOpenTran where PITSaleTID > @grpTID
	END

	-- loop over and make allocations
	declare @minAutoID int, @a_TID int, @newAllocationTID int
	select @minAutoID = min(autoID) from @tblOpenTran where PITAllocAmt > 0
	while @minAutoID is not null BEGIN
		select @a_amount=PITAllocAmt, @a_TID=transactionID
			from @tblOpenTran
			where autoID = @minAutoID

		EXEC dbo.tr_createTransaction_allocation @recordedOnSiteID=@recordedOnSiteID, 
			@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
			@status='Active', @amount=@a_amount, @transactionDate=@transactionDate, 
			@paymentTransactionID=@paymentTransactionID, @saleTransactionID=@a_TID, 
			@ovBatchID=null, @transactionID=@newAllocationTID OUTPUT

		update @tblOpenTran
		set newAllocTID = @newAllocationTID
		where autoID = @minAutoID

		select @minAutoID = min(autoID) from @tblOpenTran where PITAllocAmt > 0 and autoID > @minAutoID
	END

	-- insert AllocTaxTrans relationships
	IF EXISTS (select autoid from @tblOpenTran where PITSaleTID <> transactionID) BEGIN
		INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
		select dbo.fn_tr_getRelationshipTypeID('AllocTaxTrans'), tbl.newAllocTID, tbl2.newAllocTID
		from @tblOpenTran as tbl
		inner join @tblOpenTran as tbl2 on tbl2.transactionID = tbl.PITSaleTID
		where tbl.PITSaleTID <> tbl.transactionID
		and tbl.newAllocTID is not null
		and tbl2.newAllocTID is not null
	END	


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[tr_allocateToSale]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@amount money,
@transactionDate datetime,
@paymentTransactionID int,
@saleTransactionID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- amt needs to be positive.
	IF @amount <= 0
		RAISERROR('amount is not more than 0', 16, 1);

	-- dont assume memberid is the active one. get the active one.
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	-- ensure amount is 2 decimals
	select @amount = cast(@amount as decimal(10,2))

	-- use numeric(9,2) instead of money to prevent 4 digit money values
	-- get sale, child transactions, tax, and adjustments
	declare @tblOpenTran TABLE (autoid int IDENTITY(1,1), transactionid int, maxAmtToAllocate numeric(10,2), PITSaleTID int, PITAllocAmt numeric(10,2), newAllocTID int)

	;with innerTbl as (
		select t.transactionID, ts.cache_amountAfterAdjustment - ts.cache_activePaymentAllocatedAmount as salesDue
		from dbo.tr_transactions as t
		inner join dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID
		where t.typeID = 1
		and t.statusID not in (2,4)
		and t.transactionID = @saleTransactionID
			union all
		select t.transactionid, ts.cache_amountAfterAdjustment - ts.cache_activePaymentAllocatedAmount as salesDue
		from dbo.tr_transactions as t
		inner join dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID
		inner join innerTbl on innerTbl.transactionID = t.parentTransactionID
		where t.typeID = 1
		and t.statusID not in (2,4)
			union all
		select t.transactionID, ts.cache_amountAfterAdjustment - ts.cache_activePaymentAllocatedAmount as salesDue
		from dbo.tr_transactions as t
		inner join dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID
		inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'SalesTaxTrans'
		inner join innerTbl on innerTbl.transactionID = r.appliedToTransactionID
		where t.typeID = 7
		and t.statusID not in (2,4)
			union all
		select t.transactionID, it.cache_invoiceAmountAfterAdjustment - it.cache_activePaymentAllocatedAmount as salesDue
		from dbo.tr_transactions as t
		inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
		inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
		inner join innerTbl on innerTbl.transactionID = r.appliedToTransactionID
		where t.typeID = 3
		and t.statusID not in (2,4)
	)	
	insert into @tblOpenTran (transactionID, maxAmtToAllocate)
	select it.transactionID, sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) as maxAmtToAllocate
	from dbo.tr_invoiceTransactions as it
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	inner join innerTbl on innerTbl.transactionID = it.transactionid
	where innerTbl.salesDue > 0
	group by it.transactionID
	having sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) > 0
	order by it.transactionID

	-- group by PITTaxTrans. sales and adj to sales just put the transactionid
	update tbl
	set tbl.PITSaleTID = tbl.transactionID
	from @tblOpenTran as tbl
	inner join dbo.tr_transactions as t on t.transactionID = tbl.transactionID and t.typeID = 1

	update tbl
	set tbl.PITSaleTID = tbl.transactionID
	from @tblOpenTran as tbl
	inner join dbo.tr_transactions as t on t.transactionID = tbl.transactionID and t.typeID = 3
	inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
	inner join dbo.tr_transactions as tSale on tSale.transactionID = r.appliedToTransactionID and tSale.typeID = 1
	where tbl.PITSaleTID is null

	update tbl
	set tbl.PITSaleTID = tbl2.transactionID
	from @tblOpenTran as tbl
	inner join dbo.tr_relationships as r on r.transactionID = tbl.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'PITTaxTrans'
	inner join @tblOpenTran as tbl2 on tbl2.transactionID = r.appliedToTransactionID
	where tbl.PITSaleTID is null

	-- did we miss any PITTaxTrans records?
	IF EXISTS (select autoid from @tblOpenTran where PITSaleTID is null)
		RAISERROR('PITSaleTID is missing', 16, 1);

	-- loop over each group to get allocation amount and % split
	declare @totalAmountLeftToAllocate money, @grpTID int, @grpMaxAmtToAllocate money, @a_amount money
	select @totalAmountLeftToAllocate = @amount
	select @grpTID = min(PITSaleTID) from @tblOpenTran
	while @grpTID is not null BEGIN

		-- how much can we allocate to this group of transactions?
		select @grpMaxAmtToAllocate = sum(MaxAmtToAllocate) from @tblOpenTran where PITSaleTID = @grpTID
		IF @totalAmountLeftToAllocate > @grpMaxAmtToAllocate
			select @a_amount = @grpMaxAmtToAllocate
		ELSE
			select @a_amount = @totalAmountLeftToAllocate

		-- calc amount to allocate for each transaction
		update @tblOpenTran
		set PITAllocAmt = case 
						when @a_amount = 0 then 0 
						else ((maxAmtToAllocate/ @grpMaxAmtToAllocate) * @a_amount) 
						end
		WHERE PITSaleTID = @grpTID

		-- handle remainders from percentage calculations
		-- put any diff in the 1st item that can accept it
		declare @sumAllocAmount numeric(10,2), @amountDiff numeric(10,2)
		select @sumAllocAmount = sum(PITAllocAmt) from @tblOpenTran WHERE PITSaleTID = @grpTID
		IF @sumAllocAmount <> abs(@a_amount) BEGIN
			select @amountDiff = abs(@a_amount) - @sumAllocAmount

			update top (1) @tblOpenTran
			set PITAllocAmt = PITAllocAmt + @amountDiff
			where PITAllocAmt + @amountDiff <= maxAmtToAllocate
		END

		select @totalAmountLeftToAllocate = @totalAmountLeftToAllocate - @a_amount
		if @totalAmountLeftToAllocate <= 0
			break

		select @grpTID = min(PITSaleTID) from @tblOpenTran where PITSaleTID > @grpTID
	END

	-- loop over and make allocations
	declare @minAutoID int, @a_TID int, @newAllocationTID int
	select @minAutoID = min(autoID) from @tblOpenTran where PITAllocAmt > 0
	while @minAutoID is not null BEGIN
		select @a_amount=PITAllocAmt, @a_TID=transactionID
			from @tblOpenTran
			where autoID = @minAutoID

		EXEC dbo.tr_createTransaction_allocation @recordedOnSiteID=@recordedOnSiteID, 
			@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
			@status='Active', @amount=@a_amount, @transactionDate=@transactionDate, 
			@paymentTransactionID=@paymentTransactionID, @saleTransactionID=@a_TID, 
			@ovBatchID=null, @transactionID=@newAllocationTID OUTPUT

		update @tblOpenTran
		set newAllocTID = @newAllocationTID
		where autoID = @minAutoID

		select @minAutoID = min(autoID) from @tblOpenTran where PITAllocAmt > 0 and autoID > @minAutoID
	END	

	-- insert AllocTaxTrans relationships
	IF EXISTS (select autoid from @tblOpenTran where PITSaleTID <> transactionID) BEGIN
		INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
		select dbo.fn_tr_getRelationshipTypeID('AllocTaxTrans'), tbl.newAllocTID, tbl2.newAllocTID
		from @tblOpenTran as tbl
		inner join @tblOpenTran as tbl2 on tbl2.transactionID = tbl.PITSaleTID
		where tbl.PITSaleTID <> tbl.transactionID
		and tbl.newAllocTID is not null
		and tbl2.newAllocTID is not null
	END	


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[tr_createTransaction_adjustment]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@status varchar(20),
@amount money,
@transactionDate datetime,
@saleTransactionID int,
@invoiceID int,
@transactionID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- init output param
	select @transactionID = 0

	declare @origSaleOwnedByOrgID int, @origSaleAssignedToMemberID int, @origCreditGLAccountID int, 
			@origSaleCreditGLAccountID int, @ARGLAccountID int, @AdjSaleTransactionID int, 
			@TaxForAdjustmentMinRow int, @TaxForAdjustmentGLAID int, @taxTransactionID int,
			@AdjTaxTransactionID int, @AllocPaymentTID int, @AllocSaleTID int, 
			@allocTID int, @SaleAllocTransactionID int, @invoiceProfileID int, @contentVersionID int
	declare @TaxForAdjustmentAmt money, @amtLeftToDeallocate money, @AllocAmt money, @DeallocateAmtNeg money
	declare @invoiceNumber varchar(18)
	declare @origSaleDetail varchar(max), @TaxForAdjustmentDetail varchar(max)
	declare @tblTaxForAdjustment TABLE (row int, taxAuthorityID int, taxGLAccountID int, authorityName varchar(200), taxRuleID int, taxRate float, taxAmount money) 

	-- no zero dollar adjustments
	if @amount = 0
		RAISERROR('amount is 0', 16, 1);

	-- ensure @saleTransactionID is an active sale
	IF EXISTS (select transactionID from dbo.tr_transactions where transactionID = @saleTransactionID and typeID <> 1 and statusID not in (1,3))
		RAISERROR('saleTransactionID is not an active sale', 16, 1);

	-- get data from sale transaction
	select @origSaleOwnedByOrgID=ownedByOrgID, @origSaleAssignedToMemberID=assignedToMemberID, 
		@origSaleDetail=detail, @origCreditGLAccountID=creditGLAccountID
		from dbo.tr_transactions
		where transactionID = @saleTransactionID
		and typeID = 1
		and statusID in (1,3)

	-- dont assume memberid is the active one. get the active one.
	select @origSaleAssignedToMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @origSaleAssignedToMemberID 
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	select @ARGLAccountID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @origSaleOwnedByOrgID
		and isSystemAccount = 1
		and GLCode = 'ACCOUNTSRECEIVABLE'
		and [status] = 'A'
	IF @ARGLAccountID is null
		RAISERROR('ARGLAccountID is null', 16, 1);

	-- if invoiceID is null, not an open/pending invoice, or inv profile doesnt match revenue GL, assume need to create a new one.
	IF @invoiceID is null 
		OR NOT EXISTS (select invoiceID from dbo.tr_invoices where invoiceID = @invoiceID and statusID in (1,2))
		OR ((select dbo.fn_tr_doesInvoiceProfileSupportRevenueGL(@invoiceID,@origCreditGLAccountID)) = 0)
	BEGIN
		select @invoiceProfileID=invoiceProfileID from dbo.tr_GLAccounts where GLAccountID = @origCreditGLAccountID
			IF @invoiceProfileID is null RAISERROR('invoiceProfileID is null', 16, 1);

		EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID,
			@enteredByMemberID=@recordedByMemberID, 
			@assignedToMemberID=@origSaleAssignedToMemberID,
			@dateBilled=@transactionDate, @dateDue=@transactionDate, 
			@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT
	END
	
	-- adjustments up	
	if @amount > 0 BEGIN

		-- credit account of adj is credit account of sale
		select @origSaleCreditGLAccountID = gl.glAccountID
			from dbo.tr_transactions as t
			inner join dbo.tr_GLAccounts as gl on gl.glAccountID = t.creditGLAccountID
				and t.transactionID = @saleTransactionID
				and gl.status = 'A'
		if @origSaleCreditGLAccountID is null
			RAISERROR('origSaleCreditGLAccountID is null', 16, 1);

		-- insert adj into transactions
		-- ensure amount is abs
		INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
			amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
			typeID, accrualDate, debitGLAccountID, creditGLAccountID)
		VALUES (@origSaleOwnedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID(@status), @origSaleDetail, null, 
			@amount, getdate(), @transactionDate, @origSaleAssignedToMemberID, @recordedByMemberID, @statsSessionID, 
			dbo.fn_tr_getTypeID('Adjustment'), @transactionDate, @ARGLAccountID, @origSaleCreditGLAccountID)
			select @AdjSaleTransactionID = SCOPE_IDENTITY()

		-- insert adj into relationships
		INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
		VALUES (dbo.fn_tr_getRelationshipTypeID('AdjustTrans'), @AdjSaleTransactionID, @saleTransactionID)

		-- put adj on invoice
		SELECT @contentVersionID = null
		SELECT @contentVersionID = max(cv.contentVersionID)
			FROM dbo.tr_glAccounts as gl
			INNER JOIN dbo.cms_content as c on c.contentID = gl.invoiceContentID
			INNER JOIN dbo.cms_siteResources sr	on sr.siteResourceID = c.siteResourceID 
			INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
			INNER JOIN dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID AND cl.languageID = 1
			INNER JOIN dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID
			WHERE gl.GLAccountID = @origSaleCreditGLAccountID
			AND cv.isActive = 1
			AND len(cv.rawContent) > 0
		INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, messageContentVersionID)
		VALUES (@AdjSaleTransactionID, @invoiceID, @amount, 0, 0, @contentVersionID)

		-- update cache
		UPDATE dbo.tr_transactionSales
		SET cache_amountAfterAdjustment = cache_amountAfterAdjustment + @amount
		WHERE transactionID = @saleTransactionID

		-- determine sales tax based on adjustment date and @amount
		IF EXISTS (select glAccountID from dbo.tr_glAccounts where glAccountID = @origSaleCreditGLAccountID and isTaxable = 1) BEGIN

			-- determine the tax on the adjusted amount
			insert into @tblTaxForAdjustment (row, taxAuthorityID, taxGLAccountID, authorityName, taxRuleID, taxRate, taxAmount)
			select row, taxAuthorityID, taxGLAccountID, authorityName, taxRuleID, taxRate, taxAmount
			from dbo.fn_tr_getTaxForAdjustment(@AdjSaleTransactionID)
			where taxAmount > 0

			-- loop over taxes
			select @TaxForAdjustmentMinRow = min(row) from @tblTaxForAdjustment
			while @TaxForAdjustmentMinRow is not null BEGIN
				select @TaxForAdjustmentAmt=taxAmount, @TaxForAdjustmentGLAID=taxGLAccountID, 
					@TaxForAdjustmentDetail = 'Sales Tax: ' + authorityName + ' @ ' + cast(taxRate*100 as varchar(10)) + '%'
				from @tblTaxForAdjustment 
				where row=@TaxForAdjustmentMinRow

				-- is there already a tax transaction using this acct code for this sale? if so, adjust it
				SELECT @taxTransactionID = null
				SELECT @taxTransactionID = tTax.transactionID
					FROM dbo.tr_transactions as tTax
					INNER JOIN dbo.tr_relationships AS r ON r.transactionID = tTax.transactionID
						AND tTax.typeID = 7
						AND tTax.statusID in (1,3)
						AND r.appliedToTransactionID = @saleTransactionID
						AND tTax.creditGLAccountID = @TaxForAdjustmentGLAID
					INNER JOIN dbo.tr_relationshipTypes AS rt ON rt.typeID = r.typeID AND rt.type = 'SalesTaxTrans'
				IF @taxTransactionID is not null BEGIN

					-- insert adj into transactions
					INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
						amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
						typeID, accrualDate, debitGLAccountID, creditGLAccountID)
					VALUES (@origSaleOwnedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID(@status), @TaxForAdjustmentDetail, null, 
						@TaxForAdjustmentAmt, getdate(), @transactionDate, @origSaleAssignedToMemberID, @recordedByMemberID, @statsSessionID, 
						dbo.fn_tr_getTypeID('Adjustment'), @transactionDate, @ARGLAccountID, @TaxForAdjustmentGLAID)
						select @AdjTaxTransactionID = SCOPE_IDENTITY()

					-- insert adj into relationships
					-- tie tax to the sale adjustment
					INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
					VALUES (dbo.fn_tr_getRelationshipTypeID('AdjustTrans'), @AdjTaxTransactionID, @taxTransactionID)
					
					INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
					VALUES (dbo.fn_tr_getRelationshipTypeID('PITTaxTrans'), @AdjTaxTransactionID, @AdjSaleTransactionID)

					-- put adj on invoice
					INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount)
					VALUES (@AdjTaxTransactionID, @invoiceID, @TaxForAdjustmentAmt, 0, 0)

					-- update cache
					UPDATE dbo.tr_transactionSales
					SET cache_amountAfterAdjustment = cache_amountAfterAdjustment + @TaxForAdjustmentAmt
					WHERE transactionID = @taxTransactionID

				END 

				-- else, create a new sales tax transaction.
				ELSE BEGIN

					EXEC dbo.tr_createTransaction_salesTax @ownedByOrgID=@origSaleOwnedByOrgID, 
						@recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
						@statsSessionID=@statsSessionID, @status=@status, @detail=@TaxForAdjustmentDetail, 
						@amount=@TaxForAdjustmentAmt, @transactionDate=@transactionDate, 
						@creditGLAccountID=@TaxForAdjustmentGLAID, @saleTransactionID=@saleTransactionID, 
						@invoiceID=@invoiceID, @transactionID=@taxTransactionID OUTPUT

					-- tie tax to the sale adjustment
					INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
					VALUES (dbo.fn_tr_getRelationshipTypeID('PITTaxTrans'), @taxTransactionID, @AdjSaleTransactionID)

				END

				select @TaxForAdjustmentMinRow = min(row) from @tblTaxForAdjustment where row > @TaxForAdjustmentMinRow
			END

		END

	END 

	-- adjustments down
	ELSE BEGIN

		declare @amtLeftToAdjust money, @SaleAndAdjustmentsAmt money, @amountToAdjust money,
				@AdjToMakeAmt money, @AdjToMakeUnallocatedAmt money, @AllocationsAmt money,
				@amountToAllocate money, @a_allocAmount money, @a_subtract_allocAmount money,
				@it_amtLeftToAdj money, @it_MaxAmountToTake money, @it_amtToAdj money,
				@amtDueNoPendingOnInvoice money
		declare @SaleAndAdjustmentsMinAutoID int, @SaleAndAdjustmentsTID int, @SaleAndAdjustmentsTypeID int,
				@AdjToMakeAutoID int, @AdjToMakeTID int, @AdjToMakeGLAID int, @newAllocationTID int,
				@a_autoid int, @a_appliedToTID int, @a_paymentTID int, @a_subtract_autoid int, @minITID int,
				@it_invoiceID int
		declare @AdjToMakeIsSale bit
		declare @it_invstatus varchar(50)
		declare @AdjToMakeDetail varchar(max)
		declare @tblSaleAndAdjustments TABLE (autoid int IDENTITY(1,1) PRIMARY KEY, transactionID int, typeID int, amount money)
		declare @tblAdjToMake TABLE (autoid int IDENTITY(1,1), isSale bit, saleTransactionID int, debitGLAID int, detail varchar(max), amountToAdjust numeric(9,2))
		declare @tblAdjToMakeFinal TABLE (autoid int IDENTITY(1,1), isSale bit, saleTransactionID int, debitGLAID int, detail varchar(max), amountToAdjust numeric(9,2))
		declare @tblAllocations TABLE (autoid int IDENTITY(1,1) PRIMARY KEY, appliedToTID int, paymentTID int, allocAmount money, origAllocationTID int, origRelTID int)
		declare @tblAllocationsToMake TABLE (autoid int IDENTITY(1,1) PRIMARY KEY, origAllocationTID int, origRelTID int, newAllocationTID int, newRelTID int)

		-- get all active adjustments to sale in reverse order (daterecorded).
		-- consider the non-written off amounts only and remove any that are 0
		insert into @tblSaleAndAdjustments (transactionID, typeID, amount)
		select tAdj.transactionID, tAdj.typeID, 
			case 
			when gl.isSystemAccount = 1 and gl.GLCode = 'ACCOUNTSRECEIVABLE' then tAdj.amount
			else tAdj.amount * -1
			end - wo.writeOffAmount as maxAmountForNegativeAdjustment 
		from dbo.tr_transactions as tAdj
		inner join dbo.tr_relationships as tr on tr.transactionID = tAdj.transactionID
			and tr.appliedToTransactionID = @saleTransactionID
		inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'AdjustTrans'
		inner join dbo.tr_glAccounts as gl on gl.glAccountID = tAdj.debitGLAccountID
		cross apply dbo.fn_tr_getWriteOffAmountofSaleOrAdj(tAdj.transactionID) as wo
		where tAdj.statusID = 1
		and tAdj.typeID = 3
		and case 
			when gl.isSystemAccount = 1 and gl.GLCode = 'ACCOUNTSRECEIVABLE' then tAdj.amount
			else tAdj.amount * -1
			end - wo.writeOffAmount <> 0
		order by tAdj.daterecorded desc, tAdj.transactionID desc
		
		-- add in the original sale as last entry
		insert into @tblSaleAndAdjustments (transactionID, typeID, amount)
		select tSale.transactionID, tSale.typeID, tSale.amount - wo.writeOffAmount as maxAmountForNegativeAdjustment 
		from dbo.tr_transactions as tSale
		cross apply dbo.fn_tr_getWriteOffAmountofSaleOrAdj(tSale.transactionID) as wo
		where tSale.transactionID = @saleTransactionID
		and tSale.amount - wo.writeOffAmount <> 0

		-- loop over @tblSaleAndAdjustments until we get adjustment amount. grab PIT tax as well.		
		select @amtLeftToAdjust = abs(@amount)
		select @SaleAndAdjustmentsMinAutoID = min(autoid) from @tblSaleAndAdjustments
		while @SaleAndAdjustmentsMinAutoID is not null BEGIN
			select @SaleAndAdjustmentsTID=transactionID, @SaleAndAdjustmentsTypeID=typeID, 
				@SaleAndAdjustmentsAmt=amount
			from @tblSaleAndAdjustments 
			where autoID = @SaleAndAdjustmentsMinAutoID

			-- if amt left can be adjusted in full from this adjustment, take full amt. else take what we can.
			if @SaleAndAdjustmentsAmt < @amtLeftToAdjust
				select @amountToAdjust = @SaleAndAdjustmentsAmt
			ELSE
				select @amountToAdjust = @amtLeftToAdjust

			-- grab orig credit gl. adjustments will use this same gl.
			select @origSaleCreditGLAccountID = gl.glAccountID
				from dbo.tr_transactions as t
				inner join dbo.tr_GLAccounts as gl on gl.glAccountID = t.creditGLAccountID
					and t.transactionID = @saleTransactionID
					and gl.status = 'A'
			if @origSaleCreditGLAccountID is null
				RAISERROR('origSaleCreditGLAccountID is null', 16, 1);

			-- add to adj to make. adj debit acct is orig credit acct
			insert into @tblAdjToMake (saleTransactionID, isSale, debitGLAID, detail, amountToAdjust)
			values (@saleTransactionID, 1, @origSaleCreditGLAccountID, @origSaleDetail, @amountToAdjust*-1)
				
			-- and all its PIT taxes (adj to sales tax and sales tax). adj debit acct is orig credit acct
			-- 1. adjust to sales tax tied to adjust to sale (could be pos or neg so find out based on AR)
			-- 2+3. sales tax tied to sale or adjust to sale (only happens on positive adjustments so just take adj amount)
			insert into @tblAdjToMake (saleTransactionID, isSale, debitGLAID, detail, amountToAdjust)
			SELECT tTax.transactionID, 0, tTax.creditGLAccountID, tTax.detail, 
				case 
				when gl.isSystemAccount = 1 and gl.GLCode = 'ACCOUNTSRECEIVABLE' then ((((tAdj.amount-wo.writeOffAmount)/@SaleAndAdjustmentsAmt)*@amountToAdjust)*-1)
				else (((tAdj.amount-wo.writeOffAmount)/@SaleAndAdjustmentsAmt)*@amountToAdjust)
				end as amountToAdjust
			FROM dbo.tr_transactions AS tAdj 
			inner join dbo.tr_glAccounts as gl on gl.glAccountID = tAdj.debitGLAccountID
			INNER JOIN dbo.tr_relationships AS tr ON tAdj.transactionID = tr.transactionID 
			INNER JOIN dbo.tr_relationshipTypes AS trt ON tr.typeID = trt.typeID 
			INNER JOIN dbo.tr_relationships AS tr2 ON tAdj.transactionID = tr2.transactionID 
			INNER JOIN dbo.tr_relationshipTypes AS trt2 ON tr2.typeID = trt2.typeID 
			INNER JOIN dbo.tr_transactions AS tTax ON tr2.appliedToTransactionID = tTax.transactionID
			cross apply dbo.fn_tr_getWriteOffAmountofSaleOrAdj(tAdj.transactionID) as wo
			WHERE tr.appliedToTransactionID = @SaleAndAdjustmentsTID
			AND trt.type = 'PITTaxTrans'
			AND @SaleAndAdjustmentsTypeID = 3
			AND tAdj.typeID = 3
			AND tAdj.statusID IN (1,3)
			AND trt2.type = 'AdjustTrans'
			AND tTax.typeID = 7
			AND tTax.statusID IN (1,3)
				union all
			SELECT tTax.transactionID, 0, tTax.creditGLAccountID, tTax.detail, ((((tTax.amount-wo.writeOffAmount)/@SaleAndAdjustmentsAmt)*@amountToAdjust)*-1) as amountToAdjust
			FROM dbo.tr_transactions AS tTax 
			INNER JOIN dbo.tr_relationships AS tr ON tTax.transactionID = tr.transactionID 
			INNER JOIN dbo.tr_relationshipTypes AS trt ON tr.typeID = trt.typeID
			cross apply dbo.fn_tr_getWriteOffAmountofSaleOrAdj(tTax.transactionID) as wo
			WHERE tr.appliedToTransactionID = @SaleAndAdjustmentsTID
			AND tTax.typeID = 7 
			AND tTax.statusID IN (1,3)
			AND trt.type = 'PITTaxTrans'
			AND @SaleAndAdjustmentsTypeID in (1,3)

			select @amtLeftToAdjust = @amtLeftToAdjust - @amountToAdjust
			IF @amtLeftToAdjust <= 0
				BREAK

			select @SaleAndAdjustmentsMinAutoID = min(autoid) from @tblSaleAndAdjustments where autoid > @SaleAndAdjustmentsMinAutoID
		END

		-- sum and group transactions by saleTID. these are the adj transactions to record
		insert into @tblAdjToMakeFinal (saleTransactionID, isSale, debitGLAID, detail, amountToAdjust)
		select saleTransactionID, isSale, debitGLAID, detail, sum(amountToAdjust)
		from @tblAdjToMake
		group by saleTransactionID, isSale, debitGLAID, detail
		having sum(amountToAdjust) <> 0
		order by min(autoid)

		-- loop over the final adjustments to make. 
		-- handle necessary deallocations, record adj, and adjust cache amounts
		select @AdjToMakeAutoID = min(autoid) from @tblAdjToMakeFinal
		while @AdjToMakeAutoID is not null BEGIN
			select	@AdjToMakeTID=saleTransactionID, @AdjToMakeAmt=amountToAdjust, 
					@AdjToMakeIsSale=issale, @AdjToMakeGLAID=debitGLAID, @AdjToMakeDetail=detail
			from @tblAdjToMakeFinal 
			where autoid = @AdjToMakeAutoID

			-- get "how much has not been paid/allocated" of sale before adjustment
			select @AdjToMakeUnallocatedAmt = cache_amountAfterAdjustment - cache_activePaymentAllocatedAmount - cache_pendingPaymentAllocatedAmount
				from dbo.tr_transactionSales
				where transactionID = @AdjToMakeTID
			
			-- only need to deallocate if adj amount is gt than @AdjToMakeUnallocatedAmt
			if abs(@AdjToMakeAmt) > @AdjToMakeUnallocatedAmt BEGIN

				-- cleanup temp vars from any previous loops
				delete from @tblAllocations
				
				-- get all active allocations to sale/tax and all adjustments in reverse order.
				-- get the original allocation TID AllocTaxTrans relationship TID
				insert into @tblAllocations (appliedToTID, paymentTID, allocAmount, origAllocationTID, origRelTID)
				select tSaleAdj.transactionID, tPay.transactionID,
					case when glAllocDeb.GLCode = 'ACCOUNTSRECEIVABLE' and glAllocDeb.AccountTypeID = 2 then tAlloc.amount*-1 else tAlloc.amount end,
					tAlloc.transactionID, tATT.transactionID
				from 
					(
					select transactionID from dbo.tr_transactions where transactionID = @AdjToMakeTID
					union
					select adjInner.transactionID
					from dbo.tr_transactions as adjInner
					inner join dbo.tr_relationships as rInner on rInner.transactionID = adjInner.transactionID and rInner.appliedToTransactionID = @AdjToMakeTID
					inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
					where adjInner.statusID in (1,3)
					) as tSaleAdj
				inner join dbo.tr_relationships as rS on rS.appliedToTransactionID = tSaleAdj.transactionID
				inner join dbo.tr_relationshipTypes as rtS on rtS.typeID = rS.typeID and rtS.type = 'AllocSaleTrans'
				inner join dbo.tr_transactions as tAlloc on tAlloc.transactionID = rS.transactionID and tAlloc.statusID in (1,3)
				inner join dbo.tr_GLAccounts as glAllocDeb on glAllocDeb.GLAccountID = tAlloc.debitGLAccountID
				inner join dbo.tr_relationships as rP on rP.transactionID = tAlloc.transactionID
				inner join dbo.tr_relationshipTypes as rtP on rtP.typeID = rP.typeID and rtP.type = 'AllocPayTrans'
				inner join dbo.tr_transactions as tPay on tPay.transactionID = rP.appliedToTransactionID and tPay.statusID in (1,3)
				left outer join dbo.tr_relationships as rATT
					inner join dbo.tr_relationshipTypes as rtATT on rtATT.typeID = rATT.typeID and rtATT.type = 'AllocTaxTrans'
					inner join dbo.tr_transactions as tATT on tATT.transactionID = rATT.appliedToTransactionID and tATT.statusID in (1,3)
					on rATT.transactionID = tAlloc.transactionID
				ORDER BY tAlloc.transactionDate desc, tAlloc.transactionID desc

				-- for each negative allocation in tbl, subtract from closest positive allocation. delete negative allocation.
				IF EXISTS (select autoid from @tblAllocations where allocAmount < 0) BEGIN
				
					select @a_autoid = null
					select @a_autoid = min(autoid) from @tblAllocations where allocAmount < 0
					while @a_autoid is not null BEGIN
						select @a_allocAmount=allocAmount, @a_appliedToTID=appliedToTID, @a_paymentTID=paymentTID
						from @tblAllocations 
						where autoID = @a_autoid

						while @a_allocAmount < 0 BEGIN
							select @a_subtract_autoid = null, @a_subtract_allocAmount = null

							select top 1 @a_subtract_autoid=autoid, @a_subtract_allocAmount=allocAmount
							from @tblAllocations
							where allocAmount > 0
							and appliedToTID = @a_appliedToTID
							and paymentTID = @a_paymentTID
							order by autoid desc

							if @a_subtract_allocAmount < abs(@a_allocAmount) begin
								-- subtract amount from pos allocation (which should bring it to 0)
								update @tblAllocations
								set allocAmount = allocAmount - @a_subtract_allocAmount
								where autoID = @a_subtract_autoid

								-- add to a_allocAmount since it is negative to bring it closer to 0
								select @a_allocAmount = @a_allocAmount + @a_subtract_allocAmount
							end ELSE begin
								-- subtract remaining amount from pos allocation
								update @tblAllocations
								set allocAmount = allocAmount - abs(@a_allocAmount)
								where autoID = @a_subtract_autoid

								-- remove neg allocation since it is now all accounted for
								delete from @tblAllocations
								where autoID = @a_autoid

								-- to get out of loop
								select @a_allocAmount = 0
							end
						end

						select @a_autoid = min(autoid) from @tblAllocations where allocAmount < 0 and autoID > @a_autoid
					end

				END

				-- loop over @tblAllocations until we get deallocation amount. deallocate.
				select @amtLeftToDeallocate = abs(@AdjToMakeAmt)-@AdjToMakeUnallocatedAmt
				select @a_autoid = null
				select @a_autoid = min(autoid) from @tblAllocations
				while @a_autoid is not null BEGIN
					select @a_allocAmount=allocAmount, @a_appliedToTID=appliedToTID, @a_paymentTID=paymentTID
					from @tblAllocations 
					where autoID = @a_autoid

					-- if amt left can be deallocated in full from this allocation, take full amt. else take what we can.
					if @a_allocAmount < @amtLeftToDeallocate
						select @amountToAllocate = @a_allocAmount
					ELSE
						select @amountToAllocate = @amtLeftToDeallocate

					-- if amt > 0 then deallocate that amount.
					select @newAllocationTID = null
					IF @amountToAllocate > 0 BEGIN
						select @DeallocateAmtNeg = @amountToAllocate * -1

						EXEC dbo.tr_createTransaction_allocation @recordedOnSiteID=@recordedOnSiteID, 
							@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
							@status='Active', @amount=@DeallocateAmtNeg, @transactionDate=@transactionDate, 
							@paymentTransactionID=@a_paymentTID, @saleTransactionID=@a_appliedToTID, 
							@ovBatchID=null, @transactionID=@newAllocationTID OUTPUT
					END

					insert into @tblAllocationsToMake (origAllocationTID, origRelTID, newAllocationTID)
					select origAllocationTID, origRelTID, @newAllocationTID
					from @tblAllocations
					where autoID = @a_autoid

					select @amtLeftToDeallocate = @amtLeftToDeallocate - @amountToAllocate
					IF @amtLeftToDeallocate <= 0
						BREAK

					select @a_autoid = min(autoid) from @tblAllocations where autoid > @a_autoid
				end

			end			
	
			-- insert adj into transactions
			-- ensure amount is abs
			INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
				amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
				typeID, accrualDate, debitGLAccountID, creditGLAccountID)
			VALUES (@origSaleOwnedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID(@status), @AdjToMakeDetail, null, 
				abs(@AdjToMakeAmt), getdate(), @transactionDate, @origSaleAssignedToMemberID, @recordedByMemberID, @statsSessionID, 
				dbo.fn_tr_getTypeID('Adjustment'), @transactionDate, @AdjToMakeGLAID, @ARGLAccountID)
			IF @AdjToMakeIsSale = 1
				select @AdjSaleTransactionID = SCOPE_IDENTITY()
			ELSE
				select @AdjTaxTransactionID = SCOPE_IDENTITY()

			-- insert adj into relationships
			-- tie tax to the sale adjustment
			IF @AdjToMakeIsSale = 0	BEGIN
				INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
				VALUES (dbo.fn_tr_getRelationshipTypeID('AdjustTrans'), @AdjTaxTransactionID, @AdjToMakeTID)

				INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
				VALUES (dbo.fn_tr_getRelationshipTypeID('PITTaxTrans'), @AdjTaxTransactionID, @AdjSaleTransactionID)
			END
			ELSE BEGIN
				INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
				VALUES (dbo.fn_tr_getRelationshipTypeID('AdjustTrans'), @AdjSaleTransactionID, @AdjToMakeTID)
			END

			-- put adj on invoice (0 dollars.. no neg amounts here)
			IF @AdjToMakeIsSale = 1 BEGIN			
				SELECT @contentVersionID = null
				SELECT @contentVersionID = max(cv.contentVersionID)
					FROM dbo.tr_glAccounts as gl
					INNER JOIN dbo.cms_content as c on c.contentID = gl.invoiceContentID
					INNER JOIN dbo.cms_siteResources sr	on sr.siteResourceID = c.siteResourceID 
					INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
					INNER JOIN dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID AND cl.languageID = 1
					INNER JOIN dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID
					WHERE gl.GLAccountID = @AdjToMakeGLAID
					AND cv.isActive = 1
					AND len(cv.rawContent) > 0
				INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, messageContentVersionID)
				VALUES (@AdjSaleTransactionID, @invoiceID, 0, 0, 0, @contentVersionID)
			END ELSE BEGIN
				INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount)
				VALUES (@AdjTaxTransactionID, @invoiceID, 0, 0, 0)
			END

			-- update invoiceTransactions to reduce amount of initial sale/adj
			-- get adjToMakeTID and all its adjustments in order of oldest ITID
			select @it_amtLeftToAdj = abs(@AdjToMakeAmt)
			select @minITID = null
			select @minITID = min(it.itID)
				from dbo.tr_invoiceTransactions as it
				inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
				where (t.transactionID = @AdjToMakeTID
				OR t.transactionID in (
					select r.transactionID
					from dbo.tr_relationships as r
					inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
					inner join dbo.tr_transactions as tAdj on tAdj.transactionID = r.transactionID
					where r.appliedToTransactionID = @AdjToMakeTID
					and tAdj.statusID <> 2
				))
				and t.statusID <> 2
				and it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount > 0
			while @minITID is not null begin

				select	@it_invoiceID=it.invoiceID, 
						@it_invstatus=ins.status,
						@it_MaxAmountToTake=it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount
					from dbo.tr_invoiceTransactions as it
					inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
					inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
					where it.itID = @minITID

				if @it_MaxAmountToTake < @it_amtLeftToAdj
					select @it_amtToAdj = @it_MaxAmountToTake
				ELSE
					select @it_amtToAdj = @it_amtLeftToAdj

				update dbo.tr_invoiceTransactions
				set cache_invoiceAmountAfterAdjustment = cache_invoiceAmountAfterAdjustment - @it_amtToAdj
				where itID = @minITID

				IF @AdjToMakeIsSale = 1 BEGIN
					INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID, amount)
					SELECT top 1 dbo.fn_tr_getRelationshipTypeID('AdjustInvTrans'), @AdjSaleTransactionID, transactionID, @it_amtToAdj
					FROM dbo.tr_invoiceTransactions
					WHERE itid = @minITID
				END ELSE BEGIN
					INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID, amount)
					SELECT top 1 dbo.fn_tr_getRelationshipTypeID('AdjustInvTrans'), @AdjTaxTransactionID, transactionID, @it_amtToAdj
					FROM dbo.tr_invoiceTransactions
					WHERE itid = @minITID
				END

				-- cleanup invoice
				-- if invoice is closed and is now fully paid with active payments, mark it as paid
				-- if invoice is paid and is now not fully paid with active payments, mark it as closed
				select @amtDueNoPendingOnInvoice = sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount)
					from dbo.tr_invoiceTransactions as it
					inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
					where it.invoiceID = @it_invoiceID
					and t.statusID <> 2
				IF @it_invstatus = 'closed' and @amtDueNoPendingOnInvoice = 0 BEGIN
					update dbo.tr_invoices
					set statusID = 4, payProfileID = null
					where invoiceID = @it_invoiceID

					insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
					values (@it_invoiceID, getdate(), 4, 3, @recordedByMemberID)
				END
				IF @it_invstatus = 'paid' and @amtDueNoPendingOnInvoice > 0 BEGIN
					update dbo.tr_invoices
					set statusID = 3
					where invoiceID = @it_invoiceID

					insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
					values (@it_invoiceID, getdate(), 3, 4, @recordedByMemberID)
				END

				select @it_amtLeftToAdj = @it_amtLeftToAdj - @it_amtToAdj
				IF @it_amtLeftToAdj <= 0
					BREAK

				select @minITID = min(it.itID)
					from dbo.tr_invoiceTransactions as it
					inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
					where (t.transactionID = @AdjToMakeTID
					OR t.transactionID in (
						select r.transactionID
						from dbo.tr_relationships as r
						inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
						inner join dbo.tr_transactions as tAdj on tAdj.transactionID = r.transactionID
						where r.appliedToTransactionID = @AdjToMakeTID
						and tAdj.statusID <> 2
					))
					and t.statusID <> 2
					and it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount > 0
					and it.itid > @minITID
			end

			-- update cache
			UPDATE dbo.tr_transactionSales
			SET cache_amountAfterAdjustment = cache_amountAfterAdjustment - abs(@AdjToMakeAmt)
			WHERE transactionID = @AdjToMakeTID

			select @AdjToMakeAutoID = min(autoid) from @tblAdjToMakeFinal where autoid > @AdjToMakeAutoID
		END

		-- Record new AllocTaxTrans relationship if deallocations happened
		IF (select count(*) from @tblAllocationsToMake) > 0 BEGIN
			update tbl
			set tbl.newRelTID = tbl2.newAllocationTID
			from @tblAllocationsToMake as tbl
			inner join @tblAllocationsToMake as tbl2 on tbl2.origAllocationTID = tbl.origRelTID
			where tbl.newAllocationTID is not null
			and tbl.origRelTID is not null

			INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
			select dbo.fn_tr_getRelationshipTypeID('AllocTaxTrans'), newAllocationTID, newRelTID
			from @tblAllocationsToMake
			where newAllocationTID is not null 
			and newRelTID is not null
		END

	END


	IF @TranCounter = 0
		COMMIT TRAN;
	SELECT @transactionID = @AdjSaleTransactionID
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @transactionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[tr_createTransaction_dit]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@amount money,
@transactionDate datetime,
@recognitionDate datetime,
@debitGLAccountID int,
@creditGLAccountID int,
@saleTransactionID int,	-- this can be a sale, tax, or adjustment TID
@DITTransactionID int, -- optional, only for offsets
@transactionID int OUTPUT

AS

set nocount on

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- reset output param
	select @transactionID = 0

	-- no zero dollar DITs
	if @amount = 0
		RAISERROR('amount is 0', 16, 1);

	-- ensure amount is 2 decimals
	select @amount = cast(@amount as decimal(10,2))

	-- ensure @DITTransactionID is a dit (if passed in)
	IF @DITTransactionID is not null and NOT EXISTS (select transactionID from dbo.tr_transactions where transactionID = @DITTransactionID and typeID = 10)
		RAISERROR('ditTransactionID is not a DIT', 16, 1);
	IF @amount < 0 AND @DITTransactionID IS NULL
		RAISERROR('ditTransactionID is not valid', 16, 1);

	-- ensure @saleTransactionID is either a sale, sales tax, or positive adjustment
	IF NOT EXISTS (
		select t.transactionID
		from dbo.tr_transactions as t
		inner join dbo.tr_glAccounts as glDeb on glDeb.GLAccountID = t.debitGLAccountID
		where t.transactionID = @saleTransactionID
		and t.typeID in (1,3,7)
		and glDeb.GLCode = 'ACCOUNTSRECEIVABLE' 
		and glDeb.isSystemAccount = 1
		)
	RAISERROR('saleTransactionID is not a sale, sales tax, or positive adjustment', 16, 1);

	-- get info from sale transaction
	declare @assignedToMemberID int, @ownedByOrgID int
	select @assignedToMemberID = t.assignedToMemberID, @ownedByOrgID = t.ownedByOrgID
		from dbo.tr_transactions as t
		where t.transactionID = @saleTransactionID

	-- dont assume memberid is the active one. get the active one.
	select @assignedToMemberID = activeMemberID from dbo.ams_members where memberID = @assignedToMemberID 
	select @recordedByMemberID = activeMemberID from dbo.ams_members where memberID = @recordedByMemberID 

	-- ensure we have active debit/credit accts that account accepts new transactions
	IF @debitGLAccountID is null or NOT EXISTS (
		select glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and glaccountID = @debitGLAccountID
		and [status] = 'A')
		RAISERROR('debit account does not accept new transactions', 16, 1);
	IF @creditGLAccountID is null or NOT EXISTS (
		select glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and glaccountID = @creditGLAccountID
		and [status] = 'A')
		RAISERROR('credit account does not accept new transactions', 16, 1);

	-- if amount < 0 and there are allocations to DITtransactionID, deallocate now and reallocate at the end
	IF @amount < 0 BEGIN
		declare @amtAllocatedToDIT money
		select @amtAllocatedToDIT = cache_activePaymentAllocatedAmount + cache_pendingPaymentAllocatedAmount from dbo.tr_transactionSales where transactionID = @DITtransactionID

		IF @amtAllocatedToDIT > 0 BEGIN
			declare @tblAllocations TABLE (paymentTransactionID int, allocatedAmount money)
			insert into @tblAllocations (paymentTransactionID, allocatedAmount)
			select paymentTransactionID, allocatedAmount from dbo.fn_tr_getAllocatedPaymentsofSale(@DITtransactionID)

			declare @minPTID int, @DeallocateAmtNeg money, @newAllocationTID int
			select @minPTID = min(paymentTransactionID) from @tblAllocations
			WHILE @minPTID is not null BEGIN
				select @DeallocateAmtNeg = allocatedAmount*-1 from @tblAllocations where paymentTransactionID = @minPTID

				EXEC dbo.tr_createTransaction_allocation @recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
					@statsSessionID=@statsSessionID, @status='Active', @amount=@DeallocateAmtNeg, @transactionDate=@transactionDate, 
					@paymentTransactionID=@minPTID, @saleTransactionID=@DITtransactionID, @ovBatchID=null, 
					@transactionID=@newAllocationTID OUTPUT

				select @minPTID = min(paymentTransactionID) from @tblAllocations where paymentTransactionID > @minPTID
			END 
		END
	END

	-- insert into transactions
	declare @tr_transactionsAmount decimal(10,2)
	set @tr_transactionsAmount = abs(@amount)

	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
		amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
		typeID, accrualDate, debitGLAccountID, creditGLAccountID)
	VALUES (@ownedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID('Active'), null, null, 
		@tr_transactionsAmount, getdate(), @transactionDate, @assignedToMemberID, @recordedByMemberID, @statsSessionID, 
		10, @transactionDate, @debitGLAccountID, @creditGLAccountID)
		select @transactionID = SCOPE_IDENTITY()


	-- insert into transactionSales
	declare @tr_transactionSalesAmount decimal(10,2)
	select @tr_transactionSalesAmount = case when @amount > 0 then @amount else 0 end

	INSERT INTO dbo.tr_transactionSales (transactionID, cache_amountAfterAdjustment, 
		cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, stateIDForTax)
	VALUES (@transactionID, @tr_transactionSalesAmount, 0, 0, null)


	-- insert into transactionDIT
	IF @amount > 0 BEGIN
		INSERT INTO dbo.tr_transactionDIT (transactionID, recognitionDate)
		VALUES (@transactionID, @recognitionDate)
	END


	-- insert into relationships
	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
	VALUES (dbo.fn_tr_getRelationshipTypeID('DITSaleTrans'), @transactionID, @saleTransactionID)

	IF @amount < 0 BEGIN
		INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
		VALUES (dbo.fn_tr_getRelationshipTypeID('DITOffsetTrans'), @transactionID, @DITTransactionID)
	END


	-- update tr_transactionSales for sale/adj
	IF @amount > 0 BEGIN	
		UPDATE dbo.tr_transactionSales
		SET cache_amountAfterAdjustment = cache_amountAfterAdjustment - @amount
		WHERE transactionID = @saleTransactionID
	END
	ELSE BEGIN
		UPDATE dbo.tr_transactionSales
		SET cache_amountAfterAdjustment = cache_amountAfterAdjustment + @tr_transactionsAmount
		WHERE transactionID = @saleTransactionID

		UPDATE dbo.tr_transactionSales
		SET cache_amountAfterAdjustment = cache_amountAfterAdjustment - @tr_transactionsAmount
		WHERE transactionID = @DITTransactionID
	END


	-- if amount < 0 and there were allocations to DITtransactionID, reallocate now 
	IF @amount < 0 AND @amtAllocatedToDIT > 0 BEGIN
		declare @ReallocateAmt money
		select @minPTID = null, @newAllocationTID = null
		select @minPTID = min(paymentTransactionID) from @tblAllocations
		WHILE @minPTID is not null BEGIN
			select @ReallocateAmt = allocatedAmount from @tblAllocations where paymentTransactionID = @minPTID

			EXEC dbo.tr_createTransaction_allocation @recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
				@statsSessionID=@statsSessionID, @status='Active', @amount=@ReallocateAmt, @transactionDate=@transactionDate, 
				@paymentTransactionID=@minPTID, @saleTransactionID=@saleTransactionID, @ovBatchID=null, 
				@transactionID=@newAllocationTID OUTPUT

			select @minPTID = min(paymentTransactionID) from @tblAllocations where paymentTransactionID > @minPTID
		END 
	END


	-- check the in-bound rules.
	-- sale - new cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount must be between 0 and cache_amountAfterAdjustment
	IF NOT EXISTS (select saleID from dbo.tr_transactionSales where transactionID = @saleTransactionID and cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount between 0 and cache_amountAfterAdjustment)
		OR (@amount < 0 AND NOT EXISTS (select saleID from dbo.tr_transactionSales where transactionID = @DITTransactionID and cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount between 0 and cache_amountAfterAdjustment))
		RAISERROR('in-bounds checking failed', 16, 1);

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @transactionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[tr_createTransaction_nsf]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@nsfAmount money,
@transactionDate datetime,
@batchID int,
@paymentTransactionID int,
@transactionID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @tblAllocations TABLE (autoid int IDENTITY(1,1) PRIMARY KEY, transactionID int, PITTaxTID int, allocAmount money, NewAllocationTID int, newRelTID int)
	declare @assignedToMemberID int, @ownedByOrgID int, @creditGLAccountID int, @detail varchar(max)
	declare @debitGLAccountID int
	declare @refundableAmt money, @allocatedAmt money
	declare @a_autoid int, @a_appliedToTID int, @a_allocAmount money, @newAllocationTID int

	-- init output param
	select @transactionID = 0

	-- no zero or negative dollar amounts
	if @nsfAmount <= 0
		RAISERROR('nsfAmount is not gt 0', 16, 1);

	-- ensure @paymentTransactionID is an active payment
	IF NOT EXISTS (select transactionID from dbo.tr_transactions where transactionID = @paymentTransactionID and typeID = 2 and statusID = 1)
		RAISERROR('paymentTransactionID is not an active payment', 16, 1);

	-- get assignedToMemberID from payment transaction
	select @assignedToMemberID = assignedToMemberID, @ownedByOrgID = ownedByOrgID, 
		@creditGLAccountID = debitGLAccountID, @detail = detail
		from tr_transactions 
		where transactionID = @paymentTransactionID

	-- dont assume memberid is the active one. get the active one.
	select @assignedToMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @assignedToMemberID 
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	-- get DEP account. this is the debit account.
	select @debitGLAccountID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and isSystemAccount = 1
		and GLCode = 'DEPOSITS'
		and [status] = 'A'

	-- if batchID is null or not an open batch, reject nsf.
	IF @batchID is null OR NOT EXISTS (
		select batchID
		from dbo.tr_batches
		where orgID = @ownedByOrgID
		and batchID = @batchID
		and statusID = 1)
	RAISERROR('batchID is null or not an open batch', 16, 1);

	-- the maximum amount of the nsf is the refundable amount of the payment
	select @refundableAmt = tp.cache_refundableAmountOfPayment, @allocatedAmt = tp.cache_allocatedAmountOfPayment
		from dbo.tr_transactions as t
		inner join dbo.tr_transactionPayments as tp on tp.transactionID = t.transactionID
			and t.transactionID = @paymentTransactionID
	IF (@nsfAmount > @refundableAmt)
		RAISERROR('nsfAmount greater than refundableAmt', 16, 1);

	-- verify credit account accepts new transactions
	-- ensure we have active debit/credit accts
	IF @debitGLAccountID is null or NOT EXISTS (
		select glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and glaccountID = @creditGLAccountID
		and [status] = 'A')
		RAISERROR('debitGLAccountID is null or creditGLAccountID is not active', 16, 1);

	-- if anything is allocated to this payment, we need to deallocate it first
	IF @allocatedAmt > 0 BEGIN
		insert into @tblAllocations (transactionID, PITTaxTID, allocAmount)
		select transactionID, PITTaxTID, allocAmount
		from dbo.fn_tr_getAllocatedTransactionsofPayment(@paymentTransactionID)

		select @a_autoid = min(autoid) from @tblAllocations
		while @a_autoid is not null BEGIN
			select @a_allocAmount=allocAmount*-1, @a_appliedToTID=transactionID
			from @tblAllocations 
			where autoID = @a_autoid

			EXEC dbo.tr_createTransaction_allocation @recordedOnSiteID=@recordedOnSiteID, 
				@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
				@status='Active', @amount=@a_allocAmount, @transactionDate=@transactionDate, 
				@paymentTransactionID=@paymentTransactionID, @saleTransactionID=@a_appliedToTID, 
				@ovBatchID=null, @transactionID=@newAllocationTID OUTPUT

			UPDATE @tblAllocations
			set NewAllocationTID = @newAllocationTID
			where autoID = @a_autoid

			select @a_autoid = min(autoid) from @tblAllocations where autoid > @a_autoid
		end

		update tbl
		set tbl.newRelTID = tbl2.newAllocationTID
		from @tblAllocations as tbl
		inner join @tblAllocations as tbl2 on tbl2.transactionID = tbl.PITTaxTID
		where tbl.PITTaxTID is not null

		INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
		select dbo.fn_tr_getRelationshipTypeID('AllocTaxTrans'), newAllocationTID, newRelTID
		from @tblAllocations
		where newRelTID is not null
	END

	-- insert into transactions 
	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
		amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
		typeID, accrualDate, debitGLAccountID, creditGLAccountID)
	VALUES (@ownedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID('Active'), @detail, null, 
		@nsfAmount, getdate(), @transactionDate, @assignedToMemberID, @recordedByMemberID, @statsSessionID, 
		dbo.fn_tr_getTypeID('NSF'), @transactionDate, @debitGLAccountID, @creditGLAccountID)
		select @transactionID = SCOPE_IDENTITY()

	-- insert into batchTransactions 
	INSERT INTO dbo.tr_batchTransactions (batchID, transactionID)
	VALUES (@batchID, @transactionID)

	-- insert into relationships
	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
	VALUES (dbo.fn_tr_getRelationshipTypeID('NSFTrans'), @transactionID, @paymentTransactionID)

	-- update payment cache for @paymentTransactionID
	update dbo.tr_transactionPayments
	set cache_refundableAmountOfPayment = cache_refundableAmountOfPayment - @nsfAmount
	where transactionID = @paymentTransactionID

	-- update credit balances
	EXEC dbo.tr_updateCreditBalanceByMember @memberID=@assignedToMemberID

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @transactionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[tr_deallocateFromSale]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@amount money,
@transactionDate datetime,
@paymentTransactionID int,
@saleTransactionID int

AS

set nocount on

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- no zero or negative dollar amounts
	if @amount <= 0
		RAISERROR('amount is not gt 0', 16, 1);

	-- dont assume memberid is the active one. get the active one.
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	-- ensure amount is 2 decimals
	select @amount = cast(@amount as decimal(10,2))

	IF OBJECT_ID('tempdb..#allTrans') IS NOT NULL 
		DROP TABLE #allTrans

	-- get all allocated transactions of payment matching PaymentTID and SaleTID.
	select 
		ROW_NUMBER() OVER (ORDER BY SaleAdjTID desc, allocDate desc) as autoID,
		transactionID, allocDate, SaleAdjTID, PITTaxTID, allocAmount, 
		DENSE_RANK() OVER (ORDER BY SaleAdjTID desc, allocDate desc) as TransGroupNum,
		cast(0 as numeric(10,2)) as deallocAmount, cast(null as int) as newDeAllocTID
	into #allTrans
	from (
		select 
			case 
			when t.typeID = 1 then t.transactionID 
			when t.typeID = 7 then rTax.AppliedToTransactionID 
			when t.typeID = 3 and tAdjee.typeID = 1 then tAdjee.transactionID 
			when t.typeID = 3 and tAdjee.typeID = 7 then rAdjTax.AppliedToTransactionID 
			else null end as rollupTransactionID,
			t.transactionID as transactionID,
			isnull(atop.PITTaxTID,t.transactionID) as SaleAdjTID,
			atop.PITTaxTID as PITTaxTID,
			atop.allocAmount, atop.allocDate
		from dbo.fn_tr_getAllocatedTransactionsofPayment(@paymentTransactionID) as atop
		inner join dbo.tr_transactions as t on t.transactionID = atop.transactionID
		left outer join dbo.tr_relationships as rAdj 
			inner join dbo.tr_relationshipTypes as rtAdj on rtAdj.typeID = rAdj.typeID and rtAdj.type = 'AdjustTrans'
			inner join dbo.tr_transactions as tAdjee on tAdjee.transactionID = rAdj.AppliedToTransactionID
			left outer join dbo.tr_relationships as rAdjTax
				inner join dbo.tr_relationshipTypes as rtAdjTax on rtAdjTax.typeID = rAdjTax.typeID and rtAdjTax.type = 'SalesTaxTrans'
				on rAdjTax.transactionID = tAdjee.transactionID
			on rAdj.transactionID = t.transactionID
		left outer join dbo.tr_relationships as rTax
			inner join dbo.tr_relationshipTypes as rtTax on rtTax.typeID = rTax.typeID and rtTax.type = 'SalesTaxTrans'
			on rTax.transactionID = t.transactionID
	) as allTrans
	where rollupTransactionID = @saleTransactionID
	order by TransGroupNum, transactionID

	-- loop over the allocated trans to determine how much we can deallocate from each	
	declare @totalAmountLeftToDeallocate money, @TransGroupNum int, @grpMaxAmtToDeallocate money, @d_amount money
	select @totalAmountLeftToDeallocate = @amount
	select @TransGroupNum = min(TransGroupNum) from #allTrans
	while @TransGroupNum is not null BEGIN

		-- how much can we deallocate from this group of transactions?
		select @grpMaxAmtToDeallocate = sum(allocAmount) from #allTrans where TransGroupNum = @TransGroupNum
		IF @totalAmountLeftToDeallocate > @grpMaxAmtToDeallocate
			select @d_amount = @grpMaxAmtToDeallocate
		ELSE
			select @d_amount = @totalAmountLeftToDeallocate

		-- calc amount to deallocate for each transaction
		update #allTrans
		set deallocAmount = case 
						when @d_amount = 0 then 0 
						else ((allocAmount/ @grpMaxAmtToDeallocate) * @d_amount) 
						end
		WHERE TransGroupNum = @TransGroupNum

		-- handle remainders from percentage calculations
		-- put any diff in the 1st item that can accept it
		declare @sumDeAllocAmount numeric(10,2), @amountDiff numeric(10,2)
		select @sumDeAllocAmount = sum(deallocAmount) from #allTrans WHERE TransGroupNum = @TransGroupNum
		IF @sumDeAllocAmount <> abs(@d_amount) BEGIN
			select @amountDiff = abs(@d_amount) - @sumDeAllocAmount

			update top (1) #allTrans
			set deallocAmount = deallocAmount + @amountDiff
			where deallocAmount + @amountDiff <= allocAmount
		END

		select @totalAmountLeftToDeallocate = @totalAmountLeftToDeallocate - @d_amount
		if @totalAmountLeftToDeallocate <= 0
			break

		select @TransGroupNum = min(TransGroupNum) from #allTrans where TransGroupNum > @TransGroupNum
	
	end

	-- loop over and make deallocations
	declare @minAutoID int, @d_TID int, @newDeAllocationTID int
	select @minAutoID = min(autoID) from #allTrans where deallocAmount > 0
	while @minAutoID is not null BEGIN
		select @d_amount=deallocAmount*-1, @d_TID=transactionID
			from #allTrans
			where autoID = @minAutoID

		EXEC dbo.tr_createTransaction_allocation @recordedOnSiteID=@recordedOnSiteID, 
			@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
			@status='Active', @amount=@d_amount, @transactionDate=@transactionDate, 
			@paymentTransactionID=@paymentTransactionID, @saleTransactionID=@d_TID, 
			@ovBatchID=null, @transactionID=@newDeAllocationTID OUTPUT

		update #allTrans
		set newDeAllocTID = @newDeAllocationTID
		where autoID = @minAutoID

		select @minAutoID = min(autoID) from #allTrans where deallocAmount > 0 and autoID > @minAutoID
	END	
		
	-- insert AllocTaxTrans relationships
	IF EXISTS (select autoid from #allTrans where SaleAdjTID <> transactionID) BEGIN
		INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
		select dbo.fn_tr_getRelationshipTypeID('AllocTaxTrans'), tbl.newDeAllocTID, tbl2.newDeAllocTID
		from #allTrans as tbl
		inner join #allTrans as tbl2 on tbl2.transactionID = tbl.SaleAdjTID
		where tbl.SaleAdjTID <> tbl.transactionID
		and tbl.newDeAllocTID is not null
		and tbl2.newDeAllocTID is not null
	END	

	IF OBJECT_ID('tempdb..#allTrans') IS NOT NULL 
		DROP TABLE #allTrans

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO


