declare 
	@newTemplateID int,
	@currentTemplateID int, 
	@newTemplateName varchar(100),
	@newTemplateDesc varchar(100),
	@newTemplateFilename varchar(100)

select @currentTemplateID = templateID
from cms_pageTemplates pt
where pt.templatename = 'Simple Mobile'
and siteID is null

-- rename Simple Mobile
update cms_pageTemplates set
	templateName = 'Standard Mobile',
	templateDesc = 'Standard Mobile'
where templateID = @currentTemplateID


-- create new version
select
	@newTemplateName ='Standard Mobile Homepage',
	@newTemplateDesc ='Standard Mobile Template with Controlled Widths in Main Zone',
	@newTemplateFilename ='standardMobileHomepage'

insert into cms_pageTemplates (templateTypeID, siteID, templateName, templateDesc, templateFilename, status, isMobile)
select pt.templateTypeID, pt.siteID, @newTemplateName as templateName, @newTemplateDesc as templateDesc, @newTemplateFilename as templateFilename, pt.status, pt.isMobile
from cms_pageTemplates pt
where pt.templateID = @currentTemplateID 
SELECT @newTemplateID = SCOPE_IDENTITY()

insert into cms_pageTemplatesModesZones (templateID, modeID, zoneID, placementDesc)
select @newTemplateID as templateID, ptmz.modeID, ptmz.zoneID, ptmz.placementDesc
from cms_pageTemplates pt
inner join dbo.cms_pageTemplatesModesZones ptmz
	on ptmz.templateID = pt.templateID
	and pt.templateID = @currentTemplateID 

insert into cms_pageTemplateMenuUsageTypes (templateID, usageTypeID, usageDescription)
select @newTemplateID as templateID, ptmut.usageTypeID, ptmut.usageDescription
from cms_pageTemplates pt
inner join dbo.cms_pageTemplateMenuUsageTypes ptmut
	on ptmut.templateID = pt.templateID
	and pt.templateID = @currentTemplateID 



-- create new version
select
	@newTemplateName ='Standard Mobile Dark',
	@newTemplateDesc ='Standard Mobile Template with Inverted Navbars',
	@newTemplateFilename ='standardMobileDark'


insert into cms_pageTemplates (templateTypeID, siteID, templateName, templateDesc, templateFilename, status, isMobile)
select pt.templateTypeID, pt.siteID, @newTemplateName as templateName, @newTemplateDesc as templateDesc, @newTemplateFilename as templateFilename, pt.status, pt.isMobile
from cms_pageTemplates pt
where pt.templateID = @currentTemplateID 
SELECT @newTemplateID = SCOPE_IDENTITY()

insert into cms_pageTemplatesModesZones (templateID, modeID, zoneID, placementDesc)
select @newTemplateID as templateID, ptmz.modeID, ptmz.zoneID, ptmz.placementDesc
from cms_pageTemplates pt
inner join dbo.cms_pageTemplatesModesZones ptmz
	on ptmz.templateID = pt.templateID
	and pt.templateID = @currentTemplateID 

insert into cms_pageTemplateMenuUsageTypes (templateID, usageTypeID, usageDescription)
select @newTemplateID as templateID, ptmut.usageTypeID, ptmut.usageDescription
from cms_pageTemplates pt
inner join dbo.cms_pageTemplateMenuUsageTypes ptmut
	on ptmut.templateID = pt.templateID
	and pt.templateID = @currentTemplateID 


-- create new version
select
	@newTemplateName ='Standard Mobile Dark Homepage',
	@newTemplateDesc ='Standard Mobile Template with Controlled Widths in Main Zone and Inverted nav bars',
	@newTemplateFilename ='standardMobileDarkHomepage'


insert into cms_pageTemplates (templateTypeID, siteID, templateName, templateDesc, templateFilename, status, isMobile)
select pt.templateTypeID, pt.siteID, @newTemplateName as templateName, @newTemplateDesc as templateDesc, @newTemplateFilename as templateFilename, pt.status, pt.isMobile
from cms_pageTemplates pt
where pt.templateID = @currentTemplateID 
SELECT @newTemplateID = SCOPE_IDENTITY()

insert into cms_pageTemplatesModesZones (templateID, modeID, zoneID, placementDesc)
select @newTemplateID as templateID, ptmz.modeID, ptmz.zoneID, ptmz.placementDesc
from cms_pageTemplates pt
inner join dbo.cms_pageTemplatesModesZones ptmz
	on ptmz.templateID = pt.templateID
	and pt.templateID = @currentTemplateID 

insert into cms_pageTemplateMenuUsageTypes (templateID, usageTypeID, usageDescription)
select @newTemplateID as templateID, ptmut.usageTypeID, ptmut.usageDescription
from cms_pageTemplates pt
inner join dbo.cms_pageTemplateMenuUsageTypes ptmut
	on ptmut.templateID = pt.templateID
	and pt.templateID = @currentTemplateID 

