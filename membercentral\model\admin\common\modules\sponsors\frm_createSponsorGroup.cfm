<cfparam name="url.eventID" type="numeric">
<cfparam name="url.selectorID" type="string">

<cfset local.eventID = url.eventID>
<cfset local.selectorID = url.selectorID>

<cfsavecontent variable="local.customJS">
	<cfoutput>
	<script type="text/javascript">
		function saveSponsorGroup() {
			mca_hideAlert('err_frmcreatesponsorgroup');
			var saveSponsorGroupResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					top.onSponsorGroupCreated_#local.selectorID#();
				} else {
					let errMsg = r.errmsg ? r.errmsg : 'We were unable to create this sponsor group. Please try again.';
					mca_showAlert('err_frmcreatesponsorgroup', errMsg);
					top.$('##btnMCModalSave').prop('disabled', false).html('Create Group');
				}
			};
			if($('##sponsorGroupName').val().trim() != ''){
				top.$('##btnMCModalSave').prop('disabled', true).html('Creating...');
				var objParams = { 
					eventID: #local.eventID#, 
					sponsorGrouping: $('##sponsorGroupName').val().trim()
				};
				TS_AJX('SPONSORS','createSponsorGrouping',objParams,saveSponsorGroupResult,saveSponsorGroupResult,10000,saveSponsorGroupResult);
			} else {
				mca_showAlert('err_frmcreatesponsorgroup', 'Please enter a name for the sponsor group.');
				return false;
			}
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.customJS)#">

<cfoutput>
<form name="frmCreateSponsorGroup" id="frmCreateSponsorGroup" onsubmit="saveSponsorGroup(); return false;">
	
	<div id="err_frmcreatesponsorgroup" class="alert alert-danger mb-3 d-none"></div>
	
	<div class="form-label-group mb-3">
		<input type="text" name="sponsorGroupName" id="sponsorGroupName" class="form-control" maxlength="100" required>
		<label for="sponsorGroupName">Sponsor Group Name</label>
	</div>
	
	<div class="form-text text-muted mb-3">
		<small>Enter a descriptive name for this sponsor group (e.g., "Gold Level Sponsors", "Premium Partners", etc.)</small>
	</div>
	
	<!--- Hidden submit button triggered from parent modal --->
	<button type="submit" class="d-none"></button>
</form>
</cfoutput>
