ALTER PROC dbo.dash_lists_newTopicsPerYear
@siteid int,
@sitecode varchar(10),
@numpreviousyears int,
@listnameList varchar(max),
@xmlDataset xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @AsOfReportDate datetime = getdate(), @reportRangeStartDate smalldatetime, @yearrangestart INT, @yearrangeend INT;

	-- make @asOfReportDate time portion 23:59:59.997
	set @AsOfReportDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate),MONTH(@AsOfReportDate),DAY(@AsOfReportDate),23,59,59,997);
	set @yearrangeend = YEAR(@AsOfReportDate);
	set @yearrangestart = @yearrangeend-@numpreviousyears;
	set @reportRangeStartDate = DATETIMEFROMPARTS(YEAR(@AsOfReportDate)-@numpreviousyears,1,1,0,0,0,0);

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	CREATE TABLE #tmpLists (listID int PRIMARY KEY, list varchar(100));
	CREATE TABLE #tmpListCounts (yr int, reportCount int NOT NULL DEFAULT 0);
	CREATE TABLE #tmpListCountsExpectedRows (yr int);

	with yearrange as (
		select @yearrangestart as yearinrange
			union all
		select yearinrange + 1 as yearinrange
		from yearrange
		where yearinrange < @yearrangeend
	)
	insert into #tmpListCountsExpectedRows (yr)
	select yearinrange
	from yearrange;

	-- determine the subscription pool
	INSERT INTO #tmpLists (listID, list)
	select ml.listid, ml.list
	from dbo.fn_varcharListToTable(@listnameList,',') tmp
	inner join dbo.messagelists ml on ml.list = tmp.listItem collate Latin1_General_CI_AI
	inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI
		and lf.orgcode = @sitecode
	inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
	inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
		and sr.siteID = @siteID
		and sr.siteResourceStatusID = 1
	group by ml.listid, ml.list;

	IF ISNULL(@listnameList,'') = ''
		INSERT INTO #tmpLists (listID, list)
		select ml.listid, ml.list
		from dbo.messagelists ml
		inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name collate Latin1_General_CI_AI
			and lf.orgcode = @sitecode
		inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
		inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
			and sr.siteID = @siteID
			and sr.siteResourceStatusID = 1
		group by ml.listid, ml.list;

	insert into #tmpListCounts (yr, reportCount)
	select year(creatStamp_) as yr, count(*) as reportCount
	from #tmpLists ml
	inner join dbo.messages_ m on m.listID = ml.listID and m.isVisible=1
		and m.MessageID_ = m.ParentID_
		and m.creatStamp_ > @reportRangeStartDate
	group by year(creatStamp_);

	-- fill in missing expected rows
	insert into #tmpListCounts (yr)
	select yr
	from #tmpListCountsExpectedRows
		except
	select yr
	from #tmpListCounts;

	select @xmlDataset = ISNULL((
		select yr as reportyear, reportcount
		from #tmpListCounts as datarow
		order by yr
		FOR XML AUTO, ELEMENTS, ROOT('data')
	),'<data/>');

	IF OBJECT_ID('tempdb..#tmpLists') IS NOT NULL 
		DROP TABLE #tmpLists;
	IF OBJECT_ID('tempdb..#tmpListCounts') IS NOT NULL 
		DROP TABLE #tmpListCounts;
	IF OBJECT_ID('tempdb..#tmpListCountsExpectedRows') IS NOT NULL 
		DROP TABLE #tmpListCountsExpectedRows;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
