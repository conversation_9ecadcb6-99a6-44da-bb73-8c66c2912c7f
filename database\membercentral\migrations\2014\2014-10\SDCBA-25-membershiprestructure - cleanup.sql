

update subs set 
	allowRateGlAccountOverride=1,subscriptionName = rtrim(left(subs.subscriptionname,len(subs.subscriptionname)-5)), scheduleID = bsubs.scheduleID
from sub_types t
inner join sub_subscriptions subs
	on subs.typeID = t.typeID
	and t.siteID = 2
	and t.typeName = 'membership'
	and subs.subscriptionName like '%(all)'
inner join sub_subscriptions bsubs
	on bsubs.typeID = t.typeID
	and t.siteID = 2
	and bsubs.subscriptionName = rtrim(left(subs.subscriptionname,len(subs.subscriptionname)-5)) + ' basic'

GO


select rs.schedulename, r.isRenewalRate, r.ratename, r.uid, r.rateID, r.linkedNonRenewalRateID, r.fallbackRenewalRateID
from sub_rateSchedules rs
inner join sub_rates r
	on r.scheduleID = rs.scheduleID
	and rs.siteID = 2
	and rs.schedulename in ('Affiliate Member Rate','CA Membership Rate','Legal Community Membership Rate')
order by rs.schedulename, r.isRenewalRate, r.ratename


declare @rateinfo TABLE (uid uniqueidentifier,linkedUID uniqueidentifier,fallbackUID uniqueidentifier)

insert into @rateinfo (uid,linkedUID,fallbackUID) values ('80B7CAEF-B1DA-41F2-B103-9E36E9B4E950',null,null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('51B5E79F-1386-477D-8232-F09CE51395DB',null,null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('606D5726-26E8-4550-BF40-344A10658D1E',null,null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('712B9C0F-8EC8-447C-91F0-96742EBF9250',null,null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('6EE20A35-40F5-47A1-877C-55C61E18DA26','80B7CAEF-B1DA-41F2-B103-9E36E9B4E950',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('935ECFAE-88AA-493E-BDB1-DEC74085D411',null,null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('5DB4575C-FB2B-4825-B525-10C71CD81CE1','80B7CAEF-B1DA-41F2-B103-9E36E9B4E950',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('74135B32-8666-4A18-B0F1-D5F4083F8138','80B7CAEF-B1DA-41F2-B103-9E36E9B4E950',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('3B5EA91F-0632-41B9-9CD8-51025740C629','51B5E79F-1386-477D-8232-F09CE51395DB',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('70E748CC-8D57-44AC-8550-83AD4780D826','606D5726-26E8-4550-BF40-344A10658D1E',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('B6D5F4F8-1F3D-4D77-8EC4-CA37AC94A4B4','712B9C0F-8EC8-447C-91F0-96742EBF9250',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('1C45391A-6138-4A71-A086-96F51CF68661',null,null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('FA38721D-0DAF-4995-A36D-42D2C9550B14',null,null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('E8B96DB5-95FD-47E8-8662-1E866A13F2B4',null,null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('EDE406E9-7982-410C-B8F4-1B83D38293EE',null,null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('D47532C3-2391-4ED7-AA33-30AE12F431D4',null,null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('5517D08D-8669-4DF1-8C68-5BD6579314AC','D47532C3-2391-4ED7-AA33-30AE12F431D4',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('0B297E22-27DA-4024-99F0-3471648760A5','27BA2A65-8CD1-4E96-89F1-13F2CAE9D408',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('81007E1C-F7D1-4F73-9216-664ACDA0E4B4','5584B9E4-B728-4F3C-8841-292DBB011BB7','D09166F4-D090-4A77-89FA-797F1EA2B553')
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('3DC9FB48-9993-44B9-813F-C1BC4D51287E','8B186D98-C035-4ED4-8308-FDEEF03173A6',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('88B0662B-B755-4AE1-8869-19267EB0F725','9C365864-B3AF-4CAC-9A6C-A855024CDEC2','8945650F-5F7C-4BCB-B685-DDE9B2B3A0B4')
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('070DB04A-00B4-4ECD-A983-E6129F92C6CB','EC3B2FBB-1AFB-4781-BF9F-6AE79196D218','26A59C73-0B38-4859-8AFA-A17412DE5EFB')
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('B436781A-02D5-4BE9-9813-2556567673B2','99E08628-9DAF-4D49-8C8C-CB71D9D7993A','60EB03EA-0F9B-4B29-9C0B-41605CF6E205')
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('A42FAB41-E714-46D4-8843-25C0D29D9CCB',null,null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('003C8976-6A2A-4A5B-BFEC-14F1CA2D75AB','D47532C3-2391-4ED7-AA33-30AE12F431D4',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('EFAD05EC-17C7-4163-9E28-C606A9388D6B','27BA2A65-8CD1-4E96-89F1-13F2CAE9D408',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('EB643C77-0B34-4A55-B2B6-31E5A4F8CDB8','5584B9E4-B728-4F3C-8841-292DBB011BB7','D09166F4-D090-4A77-89FA-797F1EA2B553')
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('555C221E-23CD-4DBA-B3F6-12907A6D84FC','8B186D98-C035-4ED4-8308-FDEEF03173A6',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('742E471C-D87A-4180-963F-6B73E5DA6BFA','9C365864-B3AF-4CAC-9A6C-A855024CDEC2','8945650F-5F7C-4BCB-B685-DDE9B2B3A0B4')
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('E76F7D3B-9847-49CE-B83F-8077CA216184','EC3B2FBB-1AFB-4781-BF9F-6AE79196D218','26A59C73-0B38-4859-8AFA-A17412DE5EFB')
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('CAE0E658-6599-4759-84E2-FCA75D8BA05F','99E08628-9DAF-4D49-8C8C-CB71D9D7993A','60EB03EA-0F9B-4B29-9C0B-41605CF6E205')
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('27BA2A65-8CD1-4E96-89F1-13F2CAE9D408',null,null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('5584B9E4-B728-4F3C-8841-292DBB011BB7',null,'D09166F4-D090-4A77-89FA-797F1EA2B553')
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('8B186D98-C035-4ED4-8308-FDEEF03173A6',null,null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('9C365864-B3AF-4CAC-9A6C-A855024CDEC2',null,'8945650F-5F7C-4BCB-B685-DDE9B2B3A0B4')
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('EC3B2FBB-1AFB-4781-BF9F-6AE79196D218',null,'26A59C73-0B38-4859-8AFA-A17412DE5EFB')
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('99E08628-9DAF-4D49-8C8C-CB71D9D7993A',null,'60EB03EA-0F9B-4B29-9C0B-41605CF6E205')
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('6A86F477-9DFC-419F-8B3C-BE6CE440BA32','1C45391A-6138-4A71-A086-96F51CF68661',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('3DB569B8-6FC7-48B5-A0F0-6E12E0DB0052','FA38721D-0DAF-4995-A36D-42D2C9550B14',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('44932F5B-BBD9-43DC-8486-790F8E5A672A','E8B96DB5-95FD-47E8-8662-1E866A13F2B4',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('ABD0D7A1-C93A-4DA0-BEAF-7DCAA0E9DF18','EDE406E9-7982-410C-B8F4-1B83D38293EE',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('86068CF2-3DDB-43AE-9D05-5D3DB9B0F04B','D47532C3-2391-4ED7-AA33-30AE12F431D4',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('AB8C5932-D578-4B57-931F-0135FEDDE29E','27BA2A65-8CD1-4E96-89F1-13F2CAE9D408',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('4123BF54-3523-41CF-8B84-AAD75241811D','5584B9E4-B728-4F3C-8841-292DBB011BB7','D09166F4-D090-4A77-89FA-797F1EA2B553')
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('BA2378DF-4186-4125-9BFF-4EAF02E56612','5584B9E4-B728-4F3C-8841-292DBB011BB7','D09166F4-D090-4A77-89FA-797F1EA2B553')
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('60EB03EA-0F9B-4B29-9C0B-41605CF6E205','8B186D98-C035-4ED4-8308-FDEEF03173A6',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('2BB7966B-4F7A-4CF0-A60A-49CCB01F11F1','8B186D98-C035-4ED4-8308-FDEEF03173A6',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('D09166F4-D090-4A77-89FA-797F1EA2B553','9C365864-B3AF-4CAC-9A6C-A855024CDEC2','8945650F-5F7C-4BCB-B685-DDE9B2B3A0B4')
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('4BB3D95A-776F-413E-8E66-CDEFEB337D17','9C365864-B3AF-4CAC-9A6C-A855024CDEC2','8945650F-5F7C-4BCB-B685-DDE9B2B3A0B4')
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('8945650F-5F7C-4BCB-B685-DDE9B2B3A0B4','EC3B2FBB-1AFB-4781-BF9F-6AE79196D218','26A59C73-0B38-4859-8AFA-A17412DE5EFB')
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('C1E1CFB1-59CC-4AF6-9139-9AC4113D8904','EC3B2FBB-1AFB-4781-BF9F-6AE79196D218','26A59C73-0B38-4859-8AFA-A17412DE5EFB')
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('26A59C73-0B38-4859-8AFA-A17412DE5EFB','99E08628-9DAF-4D49-8C8C-CB71D9D7993A','60EB03EA-0F9B-4B29-9C0B-41605CF6E205')
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('95E7132E-83CF-410C-9908-BE326A066F76','99E08628-9DAF-4D49-8C8C-CB71D9D7993A','60EB03EA-0F9B-4B29-9C0B-41605CF6E205')
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('1144746F-398A-4B40-830F-8CFC395595FE',null,null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('C313B994-007D-4672-ABFA-18F36C3C2D23','1144746F-398A-4B40-830F-8CFC395595FE',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('CE24FD84-1543-4398-BAC4-BF59EF721EA0','3934BD60-339B-4722-A7F5-DE2295FDE8A0',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('5F95F6D7-22B4-4208-BB28-3318C6B9859C','C4D0E69E-1C01-42EA-B507-723E3317A7BE',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('93DCA7AB-B774-44EE-B62B-539F37E9ECBE','DC84469A-1E26-4348-A56C-8B2FCF3719B6',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('32DC0CF9-2480-451A-B002-4ACFA504948F','5255DF6A-0B71-4F83-9FA9-97388BDDE5E3',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('84B0B435-29F2-48D3-88A8-457176B3E70D',null,null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('3934BD60-339B-4722-A7F5-DE2295FDE8A0',null,null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('562D941E-E1FC-4768-B3A6-02866E7442AA','1144746F-398A-4B40-830F-8CFC395595FE',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('67B97940-E2BB-42C8-A462-AE782DBDB001','3934BD60-339B-4722-A7F5-DE2295FDE8A0',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('B1BCF304-576C-4411-BBA9-442052DE1D3D','C4D0E69E-1C01-42EA-B507-723E3317A7BE',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('AF46780D-5477-4F04-90D5-42D35282811A','DC84469A-1E26-4348-A56C-8B2FCF3719B6',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('C0779345-EA27-483E-ABCA-EB049C02B857','5255DF6A-0B71-4F83-9FA9-97388BDDE5E3',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('C4D0E69E-1C01-42EA-B507-723E3317A7BE',null,null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('DC84469A-1E26-4348-A56C-8B2FCF3719B6',null,null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('68D84203-B5C1-404F-9F48-D824F957FD1E',null,null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('B5918FF8-96BE-4E1B-90B6-82FC44048E68',null,null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('C9D77CEC-280F-4282-A2BA-9AF246774BFF',null,null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('5255DF6A-0B71-4F83-9FA9-97388BDDE5E3',null,null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('6E199E8F-DEF9-4B63-9BEB-DDD63E4C6ED3','1144746F-398A-4B40-830F-8CFC395595FE',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('D709C0F9-AC3A-47F4-B687-D097B3AACED5','3934BD60-339B-4722-A7F5-DE2295FDE8A0',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('0903615E-6707-4053-9C98-2D4C8824DB89','C4D0E69E-1C01-42EA-B507-723E3317A7BE',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('EDD02B1B-F0FA-4C15-8D90-DD4AEE27609F','DC84469A-1E26-4348-A56C-8B2FCF3719B6',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('040004C2-8226-499F-AA7E-C103BB2A6945','68D84203-B5C1-404F-9F48-D824F957FD1E',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('5BB9F175-389F-41BD-B2E5-89AB40F7FFA3','B5918FF8-96BE-4E1B-90B6-82FC44048E68',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('902E6DDB-62FE-4685-AA56-9F69B92A5C4D','C9D77CEC-280F-4282-A2BA-9AF246774BFF',null)
insert into @rateinfo (uid,linkedUID,fallbackUID) values ('65E7AA37-ABF9-4B29-8F5B-624502A9A659','5255DF6A-0B71-4F83-9FA9-97388BDDE5E3',null)



--select r.isRenewalRate, r.ratename, fallback.ratename, linked.ratename
update r set
	linkedNonRenewalRateID = linked.rateID, fallbackRenewalRateID = fallback.rateID
from sub_rates r
inner join @rateinfo ri
	on ri.uid = r.uid
left outer join sub_rates fallback
	on fallback.uid = ri.fallbackUID
left outer join sub_rates linked
	on linked.uid = ri.linkedUID



update ss set 
	subscriberPath = '0001'
from sub_types t
inner join sub_subscriptions subs
	on subs.typeID = t.typeID
	and t.siteID = 2
inner join sub_subscribers ss
	on ss.subscriptionID = subs.subscriptionID
	and ss.rootsubscriberID = ss.subscriberID
	and ss.subscriberPath = '9999'
left outer join sub_subscribers sstree
	inner join sub_subscriptions substree
		on sstree.subscriptionID = substree.subscriptionID
on sstree.rootsubscriberID = ss.subscriberID
and sstree.subscriberID <> ss.subscriberID
where sstree.subscriptionID is not null
order by ss.subscriberID, sstree.subscriberPath







declare @subsToDelete TABLE (rootSubscriberID int)
declare @performDelete int, @sitecode varchar(5), @subscriptionTypeName varchar(100), @subscriptionName varchar(100)

set @performDelete = 1

insert into @subsToDelete (rootSubscriberID)
select ss.subscriberID
from sub_types t
inner join sub_subscriptions subs
	on subs.typeID = t.typeID
	and t.siteID = 2
inner join sub_subscribers ss
	on ss.subscriptionID = subs.subscriptionID
	and ss.rootsubscriberID = ss.subscriberID
	and ss.subscriberPath = '9999'
left outer join sub_subscribers sstree
	inner join sub_subscriptions substree
		on sstree.subscriptionID = substree.subscriptionID
on sstree.rootsubscriberID = ss.subscriberID
and sstree.subscriberID <> ss.subscriberID


insert into @subsToDelete (rootSubscriberID)
select ss.subscriberID
from sub_types t
inner join sub_subscriptions subs
	on subs.typeID = t.typeID
	and t.siteID = 2
	and t.typeName = 'membership'
	and subs.subscriptionName not like 'Honorary%'
inner join sub_subscribers ss
	on ss.subscriptionID = subs.subscriptionID
	and ss.rootsubscriberID = ss.subscriberID
inner join sub_rateFrequencies rf
	 on rf.rfid = ss.rfid
inner join sub_rates r
	on r.rateID = rf.rateID
inner join sub_rateSchedules rs
	on rs.scheduleID = r.scheduleID
	and rs.scheduleID <> subs.scheduleID
left outer join sub_subscribers sstree
	inner join sub_subscriptions substree
		on sstree.subscriptionID = substree.subscriptionID
on sstree.rootsubscriberID = ss.subscriberID
and sstree.subscriberID <> ss.subscriberID
where sstree.subscriptionID is null


insert into @subsToDelete (rootSubscriberID)
select ss.subscriberID
from sub_types t
inner join sub_subscriptions subs
	on subs.typeID = t.typeID
	and t.typename like 'membership'
	and subs.subscriptionName like 'Hono%'
	and t.siteID = 2
	and subs.soldSeparately = 1
inner join datatransfer.dbo.sdcbaSubscribers ssold
	on ssold.subscriptionID = subs.subscriptionID
	and ssold.parentSubscriberID is null
inner join sub_subscribers ss
	on ss.subscriberID = ssold.subscriberID
left outer join sub_subscribers sstree
	on ss.subscriberID = sstree.rootsubscriberID
	and ss.subscriberID <> sstree.subscriberID
where sstree.subscriberID is null



-- do not change below this line

declare @siteID int, @enteredByMemberID int, @transactionID int, @vidPool xml, @tids xml
if @performDelete <> 1
BEGIN
	select *
	from @subsToDelete sd
	inner join sub_subscribers ss
		on ss.rootSubscriberID = sd.rootSubscriberID

END

if @performDelete = 1
BEGIN
	
	update ta set
		status = 'D'
	from @subsToDelete sd
	inner join sub_subscribers ss
		on ss.rootSubscriberID = sd.rootSubscriberID
	inner join dbo.tr_applications ta
		on ta.itemID = ss.subscriberID
		and ta.itemType = 'Dues'

	-- wipe statusHistory table for subtype
	delete sh
	from @subsToDelete sd
	inner join sub_subscribers ss
		on ss.rootSubscriberID = sd.rootSubscriberID
	inner join sub_statusHistory sh
		on sh.subscriberID = ss.subscriberID

	-- wipe paymentStatusHistory table for subtype
	delete psh
	from @subsToDelete sd
	inner join sub_subscribers ss
		on ss.rootSubscriberID = sd.rootSubscriberID
	inner join sub_paymentStatusHistory psh
		on psh.subscriberID = ss.subscriberID


	-- wipe paymentStatusHistory table for subtype
	delete el
	from @subsToDelete sd
	inner join sub_subscribers ss
		on ss.rootSubscriberID = sd.rootSubscriberID
	inner join ams_emaillog el
		on ss.subscriberID = el.subscriberID

	-- wipe actual subscribers
	while exists (select sd.rootsubscriberID from @subsToDelete sd inner join sub_subscribers ss on ss.subscriberID = sd.rootsubscriberID)
	BEGIN
		delete ss
		from (
			select top (100) sd2.rootsubscriberID
			from @subsToDelete sd2
			inner join sub_subscribers ss2
				on ss2.subscriberID = sd2.rootSubscriberID
		) sd
		inner join sub_subscribers ss
			on ss.subscriberID = sd.rootSubscriberID
	END


	exec dbo.sub_fixGroups @siteID=2, @bypassQueue=1

	declare @itemGroupUID uniqueidentifier
	EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=2, @memberIDList='', @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT
END