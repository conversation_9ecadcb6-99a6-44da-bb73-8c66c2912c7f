USE [platformstats]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ev_calendarSubscriptionLog](
	[calendarSubscriptionLogID] [int] IDENTITY(1,1) NOT NULL,
	[memberID] [int] NOT NULL,
	[calendarID] [int] NOT NULL,
	[dateCreated] [datetime] NOT NULL CONSTRAINT [DF_ev_calendarSubscriptions_dateCreated]  DEFAULT (getdate()),
	[dateRequested] [datetime] NOT NULL CONSTRAINT [DF_ev_calendarSubscriptions_dateRequested]  DEFAULT (getdate()),
 CONSTRAINT [PK_ev_calendarSubscriptionLog] PRIMARY KEY CLUSTERED 
(
	[calendarSubscriptionLogID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO


CREATE NONCLUSTERED INDEX [IX_ev_calendarSubscriptionLog] ON [dbo].[ev_calendarSubscriptionLog] 
(
	[memberID] ASC,
	[calendarID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]

GO





USE [platformstats]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROC [dbo].[ev_insertCalendarSubscriptionLog]
@memberID int,
@calendarID int,
@calendarSubscriptionLogID int OUTPUT

AS

declare @dateCreated datetime, @dateRequested datetime

-- init output variable
set @calendarSubscriptionLogID = -1

BEGIN TRY
SET NOCOUNT ON
	select top 1 @dateCreated = evcs.dateCreated
		from dbo.ev_calendarSubscriptionLog as evcs
		inner join membercentral.dbo.ams_members as m on m.memberid = evcs.memberid
		inner join membercentral.dbo.ams_members as m2 on m2.memberid = @memberID
			and m2.activeMemberID = m.activeMemberID
			and evcs.memberid = m2.memberid
			and evcs.calendarID = @calendarID
	
	IF @memberID is not null AND @calendarID is not null BEGIN
		set @dateRequested = getDate()
		IF @dateCreated is null
			set @dateCreated = @dateRequested

		INSERT INTO dbo.ev_calendarSubscriptionLog (memberID, calendarID, dateCreated, dateRequested)
		VALUES (@memberID, @calendarID, @dateCreated, @dateRequested)
		select @calendarSubscriptionLogID = SCOPE_IDENTITY()
	END
	ELSE
		RAISERROR('memberID and calendarID must not be null',16,1)
SET NOCOUNT OFF
END TRY
BEGIN CATCH
	set @calendarSubscriptionLogID = null
	EXEC membercentral.dbo.up_errorhandler
END CATCH



-- CAAA-9 dependent on Hotfix-8429613
USE [memberCentral]
GO
/****** Object:  StoredProcedure [dbo].[ev_getCalendarsViewableByGroupPrintID]    Script Date: 07/22/2014 11:19:53 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROCEDURE [dbo].[ev_getCalendarsViewableByGroupPrintID]
	@siteID int,
	@groupPrintID int = null
AS

declare @siteResourceStatusID int, @viewFunctionID int, @orgID int
select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')
select @viewFunctionID = dbo.fn_getResourceFunctionID('view', dbo.fn_getResourceTypeID('events'))
select @orgID = dbo.fn_getOrgIDFromSiteID(@siteID)

if @groupPrintID is null
	BEGIN
		select c.calendarID, sr.siteResourceID, cals.calendarName
		from ev_calendars c
		inner join cms_applicationInstances ai
			on ai.applicationInstanceID = c.applicationInstanceID
			and ai.siteID = @siteID
		inner join cms_siteResources sr
			on sr.siteResourceID = ai.siteResourceID
			and sr.siteResourceStatusID = @siteResourceStatusID
		inner join cms_siteResourceRightsCache srrc
			on srrc.resourceID = sr.siteResourceID
			and srrc.functionID = @viewFunctionID
			and srrc.include = 1
		inner join ams_groups g
			on g.groupID = srrc.groupID
			and g.orgID = @orgID
			and g.groupCode = 'Public'
		inner join dbo.fn_ev_getCalendarsOnSite(@siteID) cals
			on cals.calendarID = c.calendarID
		option(recompile)
	END
else
	BEGIN
		select c.calendarID, sr.siteResourceID, cals.calendarName
		from ev_calendars c
		inner join cms_applicationInstances ai
			on ai.applicationInstanceID = c.applicationInstanceID
			and ai.siteID = @siteID
		inner join cms_siteResources sr
			on sr.siteResourceID = ai.siteResourceID
			and sr.siteResourceStatusID = @siteResourceStatusID
		inner join dbo.cache_perms_siteResourceFunctionRightPrints srfrp
			on srfrp.siteResourceID = sr.siteResourceID
			and srfrp.functionID = @viewfunctionID
		inner join dbo.cache_perms_groupPrintsRightPrints gprp
			on gprp.rightPrintID = srfrp.rightPrintID
			and gprp.groupPrintID = @groupPrintID
		inner join dbo.fn_ev_getCalendarsOnSite(@siteID) cals
			on cals.calendarID = c.calendarID
		option(recompile)
	END





USE [memberCentral]
GO
/****** Object:  StoredProcedure [dbo].[ev_calendarGridXMLByDateRange]    Script Date: 07/22/2014 11:32:37 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROCEDURE [dbo].[ev_calendarGridXMLByDateRange]
	@begindate datetime,
	@enddate datetime,
	@calendarID int,
	@languageID int,
	@siteID varchar(20),
	@groupPrintID int = null
AS

SET NOCOUNT ON

DECLARE @today datetime
DECLARE @minDOW int
DECLARE @maxDOW int
DECLARE @returnXML xml, @returnCategoryXML xml, @isSWL bit

DECLARE @infoTable TABLE (minDOW int, maxDOW int, startDate datetime, endDate datetime)
DECLARE @daysTable TABLE (id int IDENTITY(1,1), date datetime, dayOfMonth int, dayOfWeek int, weeksSinceStart int, monthofYear int, year int, isToday bit)
	
select @isSWL = isSWL
	from ev_calendars
	where calendarID = @calendarID

IF OBJECT_ID('tempdb..#tempevents') IS NOT NULL 
	DROP TABLE #tempevents

CREATE TABLE #tempevents (
	autoID int IDENTITY(1,1),
	isSWL bit,
	eventID int,
	startTime datetime, 
	endTime datetime, 
	timezoneID int, 
	timeZoneCode varchar(50), 
	timeZoneAbbr varchar(10),
	displaystartTime datetime,
	displayendTime datetime, 
	displaytimezoneID int, 
	displaytimeZoneCode varchar(50), 
	displaytimeZoneAbbr varchar(10),
	altregistrationURL  varchar(500),
	isAllDayEvent bit,
	categoryID int, 
	categoryName varchar(100), 
	categoryColor varchar(100),
	eventTitle varchar(200),
	locationTitle varchar(200))

set @today = getdate()
;WITH cte AS (
	SELECT 0 i, @begindate AS date, datepart(d,@begindate) as dayOfMonth, datepart(dw,@begindate) as dayofWeek, 0 as weeksSinceStart, datepart(month,@begindate) as monthOfYear, datepart(year,@begindate) as Year, isToday = case when datediff(day,@begindate,@today) = 0 then 1 else 0 end
	UNION ALL
	SELECT i + 1, DATEADD(day,i+1,@begindate) as date, datepart(d,DATEADD(day,i+1,@begindate)) as dayOfMonth, datepart(dw,DATEADD(day,i+1,@begindate)) as dayofWeek, datediff(week,@begindate,DATEADD(day,i+1,@begindate)) as weeksSinceStart, datepart(month,DATEADD(day,i+1,@begindate)) as monthOfYear, datepart(year,DATEADD(day,i+1,@begindate)) as Year, isToday = case when datediff(day,DATEADD(day,i+1,@begindate),@today) = 0 then 1 else 0 end
	FROM cte
	WHERE datediff(day,DATEADD( day, i, @begindate ),@enddate) > 0
)
INSERT INTO @daystable (date, dayOfMonth, dayOfWeek, weeksSinceStart, monthOfYear, year,isToday)
SELECT date, dayOfMonth, dayOfWeek, weeksSinceStart, monthOfYear, year, isToday
FROM cte
option (MAXRECURSION 366)

select @minDOW = min(dayOfWeek) from @daystable
select @maxDOW = max(dayOfWeek) from @daystable

insert into @infoTable (minDOW, maxDOW, startDate, endDate)
values (@minDOW,@maxDOW,@begindate,@enddate)


declare @viewableCalendars TABLE (calendarID int PRIMARY KEY, siteResourceID int, calendarName varchar(max))

insert into @viewableCalendars (calendarID, siteResourceID, calendarName)
exec dbo.[ev_getCalendarsViewableByGroupPrintID]
	@siteID = @siteID ,
	@groupPrintID = @groupPrintID



insert into #tempevents (eventID, isSWL, startTime, endTime, timezoneID, timeZoneCode, 
	timeZoneAbbr, displaystartTime, displayendTime, displaytimezoneID, displaytimeZoneCode, 
	displaytimeZoneAbbr, altregistrationURL, isAllDayEvent, categoryID, categoryName, 
	categoryColor, eventTitle, locationTitle)
select events.eventID,
	0 as isSWL,
	CONVERT(varchar(30),
	events.startTime,121) as startTime, 
	CONVERT(varchar(30),events.endTime,121) as endTime, 
	events.timezoneID, 
	events.timeZoneCode, 
	events.timeZoneAbbr,
	CONVERT(varchar(30),
	events.displaystartTime,121) as displaystartTime,
	CONVERT(varchar(30),events.displayendTime,121) as displayendTime, 
	events.displaytimezoneID, 
	events.displaytimeZoneCode, 
	events.displaytimeZoneAbbr,
	events.altregistrationURL,
	events.isAllDayEvent,
	events.categoryID , 
	events.categoryName , 
	events.categoryColor,
	isnull(eventContent.contentTitle,'') as eventTitle,
	isnull(locationContent.contentTitle,'') as locationTitle
from dbo.fn_ev_getEventsOnCalendar(@calendarID,@languageID,@siteID,DEFAULT,DEFAULT) as events 
inner join @viewableCalendars vc
	on vc.calendarID = events.sourceCalendarID
cross apply dbo.fn_getContent(events.eventContentID,1) as eventContent
cross apply dbo.fn_getContent(events.locationContentID,1) as locationContent
where ((startTime between @begindate and @enddate) or (endTime between @begindate and @enddate))
	AND events.status = 'A'
order by events.startTime, events.isAllDayEvent

if @isSWL = 1 
BEGIN
	insert into #tempevents (eventID, isSWL, startTime, endTime, timezoneID, timeZoneCode, 
		timeZoneAbbr, displaystartTime, displayendTime, displaytimezoneID, displaytimeZoneCode, 
		displaytimeZoneAbbr, altregistrationURL, isAllDayEvent, categoryID, categoryName, 
		categoryColor, eventTitle, locationTitle)
	select matchingSeminars.seminarID as eventID,
		isSWL = 1,
		startTime = matchingSeminars.dateStart,
		endTime = matchingSeminars.dateEnd,
		matchingSeminars.timezoneID,
		matchingSeminars.timezoneCode,
		matchingSeminars.timezoneAbbr,
		displayStartTime = cast(replace(replace(startdate.value('(./node())[1]','varchar(100)'),'T',' '),'-6:0','') as datetime),
		displayEndTime = cast(replace(replace(enddate.value('(./node())[1]','varchar(100)'),'T',' '),'-6:0','') as datetime),
		displaytimezoneID = matchingSeminars.timezoneID,
		displaytimezoneCode = matchingSeminars.timezoneCode,
		displaytimezoneAbbr = matchingSeminars.timezoneAbbr,
		altregistrationURL = '',
		isAllDayEvent = cast(0 as bit),
		matchingSeminars.categoryID,
		isnull(matchingSeminars.category,'') as categoryName,
		isnull(matchingSeminars.calColor,'') as categoryColor, 
		matchingSeminars.seminarName as eventTitle ,
		'' as locationTitle
	from (
		SELECT 
			s.seminarID,
			s.seminarName,
			swl.dateStart,
			swl.dateEnd,
			cast(swl.wddxTimeZones as xml) as wddxTimeZones,
			swl.offerCredit,
			s.offerCertificate,
			stz.code,
			tz.*,
			cat.*
		FROM dbo.ev_calendars as cal
		inner join membercentral.dbo.sites sites
			on sites.siteID = @siteID
			and cal.calendarID = @calendarID
			and cal.isSWL = 1
		inner join ev_categories cat
			on cat.categoryID = cal.categoryID
		inner join dbo.organizations o
			on o.orgID = sites.orgID
		inner join membercentral.dbo.timezones tz
			on sites.defaultTimeZoneID = tz.timeZoneID
		INNER JOIN tlasites.seminarweb.dbo.tblParticipants p
			on p.orgcode = o.orgcode
		INNER JOIN tlasites.seminarweb.dbo.tblSeminarsOptIn soi
			on p.participantID = soi.participantID
		inner join tlasites.seminarweb.dbo.tblSeminars as s
			on soi.seminarID = s.seminarID
			and s.isPublished = 1
			AND s.isDeleted = 0
		INNER JOIN tlasites.seminarweb.dbo.vw_tblSeminarsSWLive swl
			on swl.seminarID = s.seminarID
			AND swl.dateStart between @begindate and @enddate
		inner join tlasites.seminarweb.dbo.tblTimeZones stz
			on tz.timeZoneCode = stz.id
		) as matchingSeminars
	CROSS APPLY matchingSeminars.wddxTimeZones.nodes(N'/wddxPacket/data/struct/var[@name=''START'']//var[@name=sql:column("matchingSeminars.code")]/dateTime') startt(startdate)
	CROSS APPLY matchingSeminars.wddxTimeZones.nodes(N'/wddxPacket/data/struct/var[@name=''END'']//var[@name=sql:column("matchingSeminars.code")]/dateTime') endt(enddate)
END

set @returnXML = (
	select 
		dateRange.minDOW, 
			dateRange.maxDOW, 
			CONVERT(nvarchar(30), dateRange.startDate, 121) as startDate, 
			CONVERT(varchar(30), dateRange.endDate, 121) as endDate, 
		weeks.weeksSinceStart,
		days.dayOfMonth,
			days.dayofWeek, 
			days.monthOfYear, 
			days.year, 
			days.isToday, 
			CONVERT(nvarchar(30), days.date, 121) as date, 
		events.isSWL,
			events.eventID ,
			CONVERT(varchar(30),events.startTime,121) as startTime, 
			CONVERT(varchar(30),events.endTime,121) as endTime , 
			events.timezoneID , 
			events.timeZoneCode , 
			events.timeZoneAbbr ,
			CONVERT(varchar(30),events.displaystartTime,121) as displaystartTime ,
			CONVERT(varchar(30),events.displayendTime,121) as displayendTime , 
			events.displaytimezoneID , 
			events.displaytimeZoneCode , 
			events.displaytimeZoneAbbr ,
			events.altregistrationURL  ,
			events.isAllDayEvent ,
			events.categoryID , 
			events.categoryName , 
			events.categoryColor ,
			events.eventTitle ,
			events.locationTitle
	from @infoTable as dateRange
	inner join (select distinct weeksSinceStart from @daystable) as weeks on 1=1
	inner join @daystable as days on weeks.weeksSinceStart = days.weeksSinceStart
	left outer join #tempevents events 
		on (datediff(day,events.startTime,days.date) >= 0 and datediff(day,days.date,events.endTime) >= 0)
	order by weeks.weeksSinceStart, days.date, events.isAllDayEvent, events.startTime
	for xml auto, root('calendar')
)
select @returnXML as dateXML


set @returnCategoryXML = (
	select distinct categoryID, categoryName, categoryColor
	from #tempevents
	order by categoryName
	for xml auto, root('categories')
)

select isnull(@returnCategoryXML,cast('<categories/>' as xml)) as categoryXML

IF OBJECT_ID('tempdb..#tempevents') IS NOT NULL 
	DROP TABLE #tempevents

set nocount off

RETURN 0



USE [memberCentral]
GO
/****** Object:  StoredProcedure [dbo].[ev_eventListXMLByDateRange]    Script Date: 07/22/2014 13:14:53 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO






ALTER PROCEDURE [dbo].[ev_eventListXMLByDateRange]
	@begindate datetime,
	@enddate datetime,
	@calendarID int,
	@languageID int,
	@siteID int,
	@groupPrintID int = null
AS

set nocount on

DECLARE @returnXML xml, @returnCategoryXML xml, @isSWL bit

select @isSWL = isSWL
	from ev_calendars
	where calendarID = @calendarID

IF OBJECT_ID('tempdb..#tempevents') IS NOT NULL 
	DROP TABLE #tempevents

CREATE TABLE #tempevents (
		autoID int IDENTITY(1,1),
		isSWL bit,
		eventID int,
		startTime datetime, 
		endTime datetime, 
		timezoneID int, 
		timeZoneCode varchar(50), 
		timeZoneAbbr varchar(10),
		displaystartTime datetime,
		displayendTime datetime, 
		displaytimezoneID int, 
		displaytimeZoneCode varchar(50), 
		displaytimeZoneAbbr varchar(10),
		altregistrationURL  varchar(500),
		isAllDayEvent bit,
		categoryID int, 
		categoryName varchar(100), 
		categoryColor varchar(100),
		eventTitle varchar(200),
		locationTitle varchar(200)
		)


declare @viewableCalendars TABLE (calendarID int PRIMARY KEY, siteResourceID int, calendarName varchar(max))

insert into @viewableCalendars (calendarID, siteResourceID, calendarName)
exec dbo.[ev_getCalendarsViewableByGroupPrintID]
	@siteID = @siteID ,
	@groupPrintID = @groupPrintID


insert into #tempevents (eventID, isSWL, startTime, endTime, timezoneID, timeZoneCode, 
	timeZoneAbbr, displaystartTime, displayendTime, displaytimezoneID, displaytimeZoneCode, 
	displaytimeZoneAbbr, altregistrationURL, isAllDayEvent, categoryID, categoryName, 
	categoryColor, eventTitle, locationTitle)
select events.eventID,
		0 as isSWL,
		CONVERT(varchar(30),
		events.startTime,121) as startTime, 
		CONVERT(varchar(30),events.endTime,121) as endTime, 
		events.timezoneID, 
		events.timeZoneCode, 
		events.timeZoneAbbr,
		CONVERT(varchar(30),
		events.displaystartTime,121) as displaystartTime,
		CONVERT(varchar(30),events.displayendTime,121) as displayendTime, 
		events.displaytimezoneID, 
		events.displaytimeZoneCode, 
		events.displaytimeZoneAbbr,
		events.altregistrationURL,
		events.isAllDayEvent,
		events.categoryID , 
		events.categoryName , 
		events.categoryColor,
		isnull(eventContent.contentTitle,'') as eventTitle,
		isnull(locationContent.contentTitle,'') as locationTitle 
	from dbo.fn_ev_getEventsOnCalendar(@calendarID,@languageID,@siteID,DEFAULT,DEFAULT) as events 
	inner join @viewableCalendars vc
		on vc.calendarID = events.sourceCalendarID
	cross apply dbo.fn_getContent(events.eventContentID,1) as eventContent
	cross apply dbo.fn_getContent(events.locationContentID,1) as locationContent
	where ((startTime between @begindate and @enddate) or (endTime between @begindate and @enddate) or (startTime < @begindate and endTime > @enddate))
		AND events.status = 'A'
	order by events.startTime, events.isAllDayEvent

if @isSWL = 1 
BEGIN
	insert into #tempevents (eventID, isSWL, startTime, endTime, timezoneID, timeZoneCode, 
		timeZoneAbbr, displaystartTime, displayendTime, displaytimezoneID, displaytimeZoneCode, 
		displaytimeZoneAbbr, altregistrationURL, isAllDayEvent, categoryID, categoryName, 
		categoryColor, eventTitle, locationTitle)

	select matchingSeminars.seminarID as eventID,
		isSWL = 1,
		startTime = matchingSeminars.dateStart,
		endTime = matchingSeminars.dateEnd,
		matchingSeminars.timezoneID,
		matchingSeminars.timezoneCode,
		matchingSeminars.timezoneAbbr,
		displayStartTime = cast(replace(replace(startdate.value('(./node())[1]','varchar(100)'),'T',' '),'-6:0','') as datetime),
		displayEndTime = cast(replace(replace(enddate.value('(./node())[1]','varchar(100)'),'T',' '),'-6:0','') as datetime),
		displaytimezoneID = matchingSeminars.timezoneID,
		displaytimezoneCode = matchingSeminars.timezoneCode,
		displaytimezoneAbbr = matchingSeminars.timezoneAbbr,
		altregistrationURL = '',
		isAllDayEvent = cast(0 as bit),
		matchingSeminars.categoryID,
		isnull(matchingSeminars.category,'') as categoryName,
		isnull(matchingSeminars.calColor,'') as categoryColor, 
		matchingSeminars.seminarName as eventTitle ,
		'' as locationTitle
	from (
		SELECT 
			s.seminarID,
			s.seminarName,
			swl.dateStart,
			swl.dateEnd,
			cast(swl.wddxTimeZones as xml) as wddxTimeZones,
			swl.offerCredit,
			s.offerCertificate,
			stz.code,
			tz.*,
			cat.*
		FROM dbo.ev_calendars as cal
		inner join membercentral.dbo.sites sites
			on sites.siteID = @siteID
			and cal.calendarID = @calendarID
			and cal.isSWL = 1
		inner join ev_categories cat
			on cat.categoryID = cal.categoryID
		inner join dbo.organizations o
			on o.orgID = sites.orgID
		inner join membercentral.dbo.timezones tz
			on sites.defaultTimeZoneID = tz.timeZoneID
		INNER JOIN tlasites.seminarweb.dbo.tblParticipants p
			on p.orgcode = o.orgcode
		INNER JOIN tlasites.seminarweb.dbo.tblSeminarsOptIn soi
			on p.participantID = soi.participantID
		inner join tlasites.seminarweb.dbo.tblSeminars as s
			on soi.seminarID = s.seminarID
			and s.isPublished = 1
			AND s.isDeleted = 0
		INNER JOIN tlasites.seminarweb.dbo.vw_tblSeminarsSWLive swl
			on swl.seminarID = s.seminarID
			AND swl.dateStart between @begindate and @enddate
		inner join tlasites.seminarweb.dbo.tblTimeZones stz
			on tz.timeZoneCode = stz.id
		) as matchingSeminars
	CROSS APPLY matchingSeminars.wddxTimeZones.nodes(N'/wddxPacket/data/struct/var[@name=''START'']//var[@name=sql:column("matchingSeminars.code")]/dateTime') startt(startdate)
	CROSS APPLY matchingSeminars.wddxTimeZones.nodes(N'/wddxPacket/data/struct/var[@name=''END'']//var[@name=sql:column("matchingSeminars.code")]/dateTime') endt(enddate)
END

select @returnXML = (
	select 
		dateRange.calendarID, 
		CONVERT(varchar(30),dateRange.startDate,121) as startDate, 
		CONVERT(varchar(30),dateRange.enddate,121) as endDate, 
		events.isSWL,
		events.eventID ,
		CONVERT(varchar(30),events.startTime,121) as startTime, 
		CONVERT(varchar(30),events.endTime,121) as endTime , 
		events.timezoneID , 
		events.timeZoneCode , 
		events.timeZoneAbbr ,
		CONVERT(varchar(30),events.displaystartTime,121) as displaystartTime ,
		CONVERT(varchar(30),events.displayendTime,121) as displayendTime , 
		events.displaytimezoneID , 
		events.displaytimeZoneCode , 
		events.displaytimeZoneAbbr ,
		events.altregistrationURL  ,
		events.isAllDayEvent ,
		events.categoryID , 
		events.categoryName , 
		events.categoryColor ,
		events.eventTitle ,
		events.locationTitle
	from #tempevents events
	inner join ( select @calendarID as calendarID, @begindate as startDate, @enddate as endDate) dateRange
		on 1=1 
	order by startTime, isAllDayEvent
	for xml auto, root('calendar')
)
select isnull(@returnXML,cast('<calendar/>' as xml)) as dateXML

select @returnCategoryXML = (
	select distinct categoryID, categoryName, categoryColor
	from #tempevents as category
	order by categoryName
	for xml auto, root('categories')
)
select isnull(@returnCategoryXML,cast('<categories/>' as xml)) as categoryXML

IF OBJECT_ID('tempdb..#tempevents') IS NOT NULL 
	DROP TABLE #tempevents

set nocount off

RETURN 0




USE [memberCentral]
GO
/****** Object:  StoredProcedure [dbo].[ev_eventListByDateRange]    Script Date: 07/23/2014 09:05:30 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[ev_eventListByDateRange]
	@begindate datetime,
	@enddate datetime,
	@calendarID int,
	@languageID int,
	@siteID int,
	@groupPrintID int = null
AS

set nocount on

DECLARE @returnXML xml, @returnCategoryXML xml, @isSWL bit

select @isSWL = isSWL
	from ev_calendars
	where calendarID = @calendarID

IF OBJECT_ID('tempdb..#tempevents') IS NOT NULL 
	DROP TABLE #tempevents

CREATE TABLE #tempevents (
		autoID int IDENTITY(1,1),
		isSWL bit,
		eventID int,
		startTime datetime, 
		endTime datetime, 
		timezoneID int, 
		timeZoneCode varchar(50), 
		timeZoneAbbr varchar(10),
		displaystartTime datetime,
		displayendTime datetime, 
		displaytimezoneID int, 
		displaytimeZoneCode varchar(50), 
		displaytimeZoneAbbr varchar(10),
		altregistrationURL  varchar(500),
		isAllDayEvent bit,
		categoryID int, 
		categoryName varchar(100), 
		categoryColor varchar(100),
		eventTitle varchar(200),
		eventContent varchar(max),
		locationTitle varchar(200),
		locationContent varchar(max)
		)


declare @viewableCalendars TABLE (calendarID int PRIMARY KEY, siteResourceID int, calendarName varchar(max))

insert into @viewableCalendars (calendarID, siteResourceID, calendarName)
exec dbo.[ev_getCalendarsViewableByGroupPrintID]
	@siteID = @siteID ,
	@groupPrintID = @groupPrintID


insert into #tempevents (eventID, isSWL, startTime, endTime, timezoneID, timeZoneCode, 
	timeZoneAbbr, displaystartTime, displayendTime, displaytimezoneID, displaytimeZoneCode, 
	displaytimeZoneAbbr, altregistrationURL, isAllDayEvent, categoryID, categoryName, 
	categoryColor, eventTitle, eventContent, locationTitle, locationContent)
select events.eventID,
		0 as isSWL,
		CONVERT(varchar(30),
		events.startTime,121) as startTime, 
		CONVERT(varchar(30),events.endTime,121) as endTime, 
		events.timezoneID, 
		events.timeZoneCode, 
		events.timeZoneAbbr,
		CONVERT(varchar(30),
		events.displaystartTime,121) as displaystartTime,
		CONVERT(varchar(30),events.displayendTime,121) as displayendTime, 
		events.displaytimezoneID, 
		events.displaytimeZoneCode, 
		events.displaytimeZoneAbbr,
		events.altregistrationURL,
		events.isAllDayEvent,
		events.categoryID , 
		events.categoryName , 
		events.categoryColor,
		isnull(eventContent.contentTitle,'') as eventTitle,
		isnull(eventContent.rawContent, '') as eventContent,
		isnull(locationContent.contentTitle,'') as locationTitle,
		isnull(locationContent.rawContent,'') as locationContent
	from dbo.fn_ev_getEventsOnCalendar(@calendarID,@languageID,@siteID,DEFAULT,DEFAULT) as events 
	inner join @viewableCalendars vc
		on vc.calendarID = events.sourceCalendarID
	cross apply dbo.fn_getContent(events.eventContentID,1) as eventContent
	cross apply dbo.fn_getContent(events.locationContentID,1) as locationContent
	where ((startTime between @begindate and @enddate) or (endTime between @begindate and @enddate) or (startTime < @begindate and endTime > @enddate))
		AND events.status = 'A'
	order by events.startTime, events.isAllDayEvent

if @isSWL = 1 
BEGIN
	insert into #tempevents (eventID, isSWL, startTime, endTime, timezoneID, timeZoneCode, 
		timeZoneAbbr, displaystartTime, displayendTime, displaytimezoneID, displaytimeZoneCode, 
		displaytimeZoneAbbr, altregistrationURL, isAllDayEvent, categoryID, categoryName, 
		categoryColor, eventTitle, locationTitle)

	select matchingSeminars.seminarID as eventID,
		isSWL = 1,
		startTime = matchingSeminars.dateStart,
		endTime = matchingSeminars.dateEnd,
		matchingSeminars.timezoneID,
		matchingSeminars.timezoneCode,
		matchingSeminars.timezoneAbbr,
		displayStartTime = cast(replace(replace(startdate.value('(./node())[1]','varchar(100)'),'T',' '),'-6:0','') as datetime),
		displayEndTime = cast(replace(replace(enddate.value('(./node())[1]','varchar(100)'),'T',' '),'-6:0','') as datetime),
		displaytimezoneID = matchingSeminars.timezoneID,
		displaytimezoneCode = matchingSeminars.timezoneCode,
		displaytimezoneAbbr = matchingSeminars.timezoneAbbr,
		altregistrationURL = '',
		isAllDayEvent = cast(0 as bit),
		matchingSeminars.categoryID,
		isnull(matchingSeminars.category,'') as categoryName,
		isnull(matchingSeminars.calColor,'') as categoryColor, 
		matchingSeminars.seminarName as eventTitle ,
		'' as locationTitle
	from (
		SELECT 
			s.seminarID,
			s.seminarName,
			swl.dateStart,
			swl.dateEnd,
			cast(swl.wddxTimeZones as xml) as wddxTimeZones,
			swl.offerCredit,
			s.offerCertificate,
			stz.code,
			tz.*,
			cat.*
		FROM dbo.ev_calendars as cal
		inner join membercentral.dbo.sites sites
			on sites.siteID = @siteID
			and cal.calendarID = @calendarID
			and cal.isSWL = 1
		inner join ev_categories cat
			on cat.categoryID = cal.categoryID
		inner join dbo.organizations o
			on o.orgID = sites.orgID
		inner join membercentral.dbo.timezones tz
			on sites.defaultTimeZoneID = tz.timeZoneID
		INNER JOIN tlasites.seminarweb.dbo.tblParticipants p
			on p.orgcode = o.orgcode
		INNER JOIN tlasites.seminarweb.dbo.tblSeminarsOptIn soi
			on p.participantID = soi.participantID
		inner join tlasites.seminarweb.dbo.tblSeminars as s
			on soi.seminarID = s.seminarID
			and s.isPublished = 1
			AND s.isDeleted = 0
		INNER JOIN tlasites.seminarweb.dbo.vw_tblSeminarsSWLive swl
			on swl.seminarID = s.seminarID
			AND swl.dateStart between @begindate and @enddate
		inner join tlasites.seminarweb.dbo.tblTimeZones stz
			on tz.timeZoneCode = stz.id
		) as matchingSeminars
	CROSS APPLY matchingSeminars.wddxTimeZones.nodes(N'/wddxPacket/data/struct/var[@name=''START'']//var[@name=sql:column("matchingSeminars.code")]/dateTime') startt(startdate)
	CROSS APPLY matchingSeminars.wddxTimeZones.nodes(N'/wddxPacket/data/struct/var[@name=''END'']//var[@name=sql:column("matchingSeminars.code")]/dateTime') endt(enddate)
END

select 
	dateRange.calendarID, 
	CONVERT(varchar(30),dateRange.startDate,121) as startDate, 
	CONVERT(varchar(30),dateRange.enddate,121) as endDate, 
	events.isSWL,
	events.eventID ,
	CONVERT(varchar(30),events.startTime,121) as startTime, 
	CONVERT(varchar(30),events.endTime,121) as endTime , 
	events.timezoneID , 
	events.timeZoneCode , 
	events.timeZoneAbbr ,
	CONVERT(varchar(30),events.displaystartTime,121) as displaystartTime ,
	CONVERT(varchar(30),events.displayendTime,121) as displayendTime , 
	events.displaytimezoneID , 
	events.displaytimeZoneCode , 
	events.displaytimeZoneAbbr ,
	events.altregistrationURL  ,
	events.isAllDayEvent ,
	events.categoryID , 
	events.categoryName , 
	events.categoryColor ,
	events.eventTitle ,
	events.eventContent ,
	events.locationTitle,
	events.locationContent
from #tempevents events
inner join ( select @calendarID as calendarID, @begindate as startDate, @enddate as endDate) dateRange
	on 1=1 
order by startTime, isAllDayEvent

select distinct categoryID, categoryName, categoryColor
from #tempevents as category
order by categoryName

IF OBJECT_ID('tempdb..#tempevents') IS NOT NULL 
	DROP TABLE #tempevents

set nocount off

RETURN 0

GO


ALTER TABLE dbo.ev_calendars ADD
	allowWebCalSubscriptions bit NOT NULL CONSTRAINT DF_ev_calendars_allowWebCalSubscriptions DEFAULT 0
GO



