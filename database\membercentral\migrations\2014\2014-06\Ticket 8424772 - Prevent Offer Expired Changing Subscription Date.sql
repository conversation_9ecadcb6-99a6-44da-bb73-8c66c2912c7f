USE [memberCentral]
GO
ALTER PROCEDURE [dbo].[sub_updateSubscriberStatus] 
	@subscriberID int,
	@newStatusCode varchar(1),
	@siteID int,
	@enteredByMemberID int,
	@result int OUTPUT
AS
BEGIN
	declare @itemGroupUID uniqueidentifier

	select @enteredByMemberID = nullif(@enteredByMemberID,0)

	/*
		Results:
		0 - nothing changed
		1 - successfully changed
		-1 - error occured
		-2 - status not found


		Statuses:
		A - Active
		I - Inactive
		R - Renewal Not Sent
		O - Billed
		P - Accepted
		E - Expired
		X - Offer Expired
		D - Deleted
	*/

	select @result = 0

	declare @orgID int, @currStatusID int, @currStatusCode varchar(1), @currPaymentStatusCode varchar(1), @currSubscriptionID int, @currSubEndDate DATETIME, @memberID int, @activeMemberID int, @prevSubscriberID int, @minSubscriberID int, @insideResult int
	declare @newStatusID int, @enteredByActiveMemberID int, @tempCount int, @tempID int, @tempAcceptedStatusID int, @endDate DATETIME
	declare @tempEndDate DATETIME, @tempGraceEndDate DATETIME
	declare @loopCurrSubActiveGroupID int, @loopCurrSubWaitingGroupID int, @loopCurrSubInactiveGroupID int, @loopCurrSubPendingGroupID int, @loopCurrSubRenewGroupID int
	declare @tempUpdateSubscriberStatus table(subscriberID int, 
												subscriptionID int, 
												status varchar(1), 
												subStartDate datetime,
												subEndDate datetime,
												graceEndDate datetime,
												parentSubscriberID int)
	declare @tempUpdatePrevSubscriberStatus table(subscriberID int, 
												subscriptionID int, 
												status varchar(1), 
												subStartDate datetime,
												subEndDate datetime,
												graceEndDate datetime,
												parentSubscriberID int)

	select @endDate = getDate()

	select @orgID=orgID
	from dbo.sites
	where siteID = @siteID
	
	/* make sure we have the active memberID for the member changing the status */
	select @enteredByActiveMemberID=dbo.fn_getActiveMemberID(@enteredByMemberID)

	select @newStatusID=statusID
	from dbo.sub_statuses
	where statusCode = @newStatusCode

	if @newStatusID is null
		goto on_error

	/* cannot return from children from a status of D or E */
	select @currSubscriptionID=s.subscriptionID, @memberID=s.memberID, @activeMemberID=m.activeMemberID, @currSubEndDate=s.subEndDate, @currStatusID=s.statusID, 
		@currStatusCode=ss.statusCode, @currPaymentStatusCode=ps.statusCode
	from dbo.sub_subscribers s
	inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID 
	inner join dbo.sub_types t on t.typeID = subs.typeID and t.siteID = @siteID	
	inner join dbo.sub_statuses ss on ss.statusID = s.statusID
	inner join dbo.sub_paymentStatuses ps on ps.statusID = s.paymentStatusID
	inner join dbo.ams_members m on m.memberID = s.memberID
	where s.subscriberID = @subscriberID

	IF @memberID is not null
	BEGIN

		insert into @tempUpdateSubscriberStatus
		select subscriberID, subscriptionID, status, subStartDate, subEndDate, graceEndDate, parentSubscriberID
		from dbo.fn_getRecursiveMemberSubscriptions(ISNull(@memberID,0), @siteID, @subscriberID) grms
		where grms.status not in ('E', 'D')
		and grms.subscriberID <> @subscriberID


		IF @newStatusCode = 'A'
		BEGIN
			IF @currStatusCode is not null
			BEGIN
				IF @currStatusCode = 'I'
				BEGIN
					/*
						print 'Inactive to Active'
						update the subscription that was sent, 
						put in active or waiting group, based on payment status
						check if member has a subscription of same ID that is inactive or expired.  If not, remove from inactive group
						then loop over the others to call status update
					*/
					update dbo.sub_subscribers
					set statusID = @newStatusID
					where subscriberID = @subscriberID
					
					insert into dbo.sub_statusHistory(subscriberID, oldStatusID, statusID, enteredByMemberID)
					values(@subscriberID, @currStatusID, @newStatusID, @enteredByActiveMemberID)

					IF @currPaymentStatusCode = 'N'
					BEGIN
						-- update groups.  Add to active, would also check on payment status
						select @loopCurrSubWaitingGroupID=null
						select @loopCurrSubWaitingGroupID=groupID
						from ams_groups
						where groupCode like 'subWaitingPay_' + convert(varchar, @currSubscriptionID) + '_tracking'

						IF @loopCurrSubWaitingGroupID is not null BEGIN
							exec dbo.ams_createMemberGroup @memberID, @loopCurrSubWaitingGroupID, 1
						END
					END
					ELSE
					BEGIN					
						-- update groups.  Add to active, would also check on payment status
						select @loopCurrSubActiveGroupID=null
						select @loopCurrSubActiveGroupID=groupID
						from ams_groups
						where groupCode like 'subActive_' + convert(varchar, @currSubscriptionID) + '_tracking'

						IF @loopCurrSubActiveGroupID is not null BEGIN
							exec dbo.ams_createMemberGroup @memberID, @loopCurrSubActiveGroupID, 1
						END
					END

					-- update groups.  Check if this subscription still exists in a I or E status, if not then remove
					select @tempCount=count(s.subscriberID)
					from dbo.sub_subscribers s
					inner join dbo.sub_statuses st on st.statusID = s.statusID AND st.statusCode in ('I','E')
					inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
					where s.subscriptionID = @currSubscriptionID

					IF @tempCount = 0
					begin
						select @loopCurrSubWaitingGroupID=null
						select @loopCurrSubWaitingGroupID=groupID
						from ams_groups
						where groupCode like 'subInactive_' + convert(varchar, @currSubscriptionID) + '_tracking'

						IF @loopCurrSubWaitingGroupID is not null BEGIN
							exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubWaitingGroupID
						END
					end

					select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus
					while @minSubscriberID is not null
					begin
						EXEC dbo.sub_updateSubscriberStatus @minSubscriberID, @newStatusCode, @siteID, @enteredByActiveMemberID, @insideResult OUTPUT
						select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus where subscriberID > @minSubscriberID
					end
					
					-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
					EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberID, @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

					select @result = 1
				END
				ELSE IF @currStatusCode = 'O'
				BEGIN
					-- before we make this active, we need to see if there is another active/inactive that needs to expire
					select @prevSubscriberID=s.subscriberID
					from dbo.sub_subscribers s
					inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode in ('A', 'I')
					inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID 
					inner join dbo.sub_types t on t.typeID = subs.typeID and t.siteID = @siteID											
					where s.subscriptionID = @currSubscriptionID
					and s.subscriberID <> @subscriberID
					and s.memberID = @memberID

					IF @prevSubscriberID is not null AND @currPaymentStatusCode = 'P'
					BEGIN
						EXEC sub_updateSubscriberStatus @prevSubscriberID, 'E', @siteID, @enteredByActiveMemberID, @insideResult OUTPUT
					END

					/*
						print 'Offered to Active'
						update the subscription that was sent, 
						put in active or waiting group, based on payment status
						check if member has a subscription of same ID that is of Status R or O.  If not, remove from renew group
						then loop over the others to call status update
					*/
					update dbo.sub_subscribers
					set statusID = @newStatusID
					where subscriberID = @subscriberID

					select @tempAcceptedStatusID=statusID
					from dbo.sub_statuses
					where statusCode = 'P'

					insert into dbo.sub_statusHistory(subscriberID, oldStatusID, statusID, enteredByMemberID)
					values(@subscriberID, @currStatusID, @tempAcceptedStatusID, @enteredByActiveMemberID)
					
					insert into dbo.sub_statusHistory(subscriberID, oldStatusID, statusID, enteredByMemberID)
					values(@subscriberID, @tempAcceptedStatusID, @newStatusID, @enteredByActiveMemberID)
					
					IF @currPaymentStatusCode = 'N'
					BEGIN
						-- update groups.  Add to active, would also check on payment status
						select @loopCurrSubWaitingGroupID=null
						select @loopCurrSubWaitingGroupID=groupID
						from ams_groups
						where groupCode like 'subWaitingPay_' + convert(varchar, @currSubscriptionID) + '_tracking'

						IF @loopCurrSubWaitingGroupID is not null BEGIN
							exec dbo.ams_createMemberGroup @memberID, @loopCurrSubWaitingGroupID, 1
						END
					END
					ELSE
					BEGIN					
						-- update groups.  Add to active, would also check on payment status
						select @loopCurrSubActiveGroupID=null
						select @loopCurrSubActiveGroupID=groupID
						from ams_groups
						where groupCode like 'subActive_' + convert(varchar, @currSubscriptionID) + '_tracking'

						IF @loopCurrSubActiveGroupID is not null BEGIN
							exec dbo.ams_createMemberGroup @memberID, @loopCurrSubActiveGroupID, 1
						END
					END
					
					-- update groups.  Check if this subscription still exists in a I or E status, if not then remove
					select @tempCount=count(s.subscriberID)
					from dbo.sub_subscribers s
					inner join dbo.sub_statuses st on st.statusID = s.statusID AND st.statusCode in ('R','O')
					inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
					where s.subscriptionID = @currSubscriptionID
					
					IF @tempCount = 0
					begin
						select @loopCurrSubRenewGroupID=null
						select @loopCurrSubRenewGroupID=groupID
						from ams_groups
						where groupCode like 'subRenew_' + convert(varchar, @currSubscriptionID) + '_tracking'

						IF @loopCurrSubRenewGroupID is not null BEGIN
							exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubRenewGroupID
						END
					end
					
					select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus
					while @minSubscriberID is not null
					begin
						EXEC sub_updateSubscriberStatus @minSubscriberID, @newStatusCode, @siteID, @enteredByActiveMemberID, @insideResult OUTPUT
						select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus where subscriberID > @minSubscriberID
					end
					
					-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
					EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberID, @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

					select @result = 1
				END
				ELSE IF @currStatusCode = 'P'
				BEGIN
					-- before we make this active, we need to see if there is another active/inactive that needs to expire
					select @prevSubscriberID=s.subscriberID
					from dbo.sub_subscribers s
					inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode in ('A', 'I')
					inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID 
					inner join dbo.sub_types t on t.typeID = subs.typeID and t.siteID = @siteID											
					where s.subscriptionID = @currSubscriptionID
					and s.subscriberID <> @subscriberID
					and s.memberID = @memberID

					IF @prevSubscriberID is not null AND @currPaymentStatusCode = 'P'
					BEGIN
						EXEC sub_updateSubscriberStatus @prevSubscriberID, 'E', @siteID, @enteredByActiveMemberID, @insideResult OUTPUT
					END

					/*
						update the subscription that was sent, 
						put in active or waiting group, based on payment status
						check if member has a subscription of same ID that is of status P.  If not, remove from pending group
						then loop over the others to call status update
					*/
					update dbo.sub_subscribers
					set statusID = @newStatusID
					where subscriberID = @subscriberID

					insert into dbo.sub_statusHistory(subscriberID, oldStatusID, statusID, enteredByMemberID)
					values(@subscriberID, @currStatusID, @newStatusID, @enteredByActiveMemberID)

					IF @currPaymentStatusCode = 'N'
					BEGIN
						-- update groups.  Add to active, would also check on payment status
						select @loopCurrSubWaitingGroupID=null
						select @loopCurrSubWaitingGroupID=groupID
						from ams_groups
						where groupCode like 'subWaitingPay_' + convert(varchar, @currSubscriptionID) + '_tracking'

						IF @loopCurrSubWaitingGroupID is not null BEGIN
							exec dbo.ams_createMemberGroup @memberID, @loopCurrSubWaitingGroupID, 1
						END
					END
					ELSE
					BEGIN					
						-- update groups.  Check if this subscription still exists in a N payment status, if not then remove
						select @tempCount=count(subscriberID)
						from dbo.sub_subscribers s
						inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID and pst.statusCode = 'N'
						inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
						where s.subscriptionID = @currSubscriptionID

						IF @tempCount = 0
						begin
							
							select @loopCurrSubWaitingGroupID=null
							select @loopCurrSubWaitingGroupID=groupID
							from ams_groups
							where groupCode like 'subWaitingPay_' + convert(varchar, @currSubscriptionID) + '_tracking'

							IF @loopCurrSubWaitingGroupID is not null BEGIN
								exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubWaitingGroupID
							END
						end

						-- update groups.  Add to active, would also check on payment status
						select @loopCurrSubActiveGroupID=null
						select @loopCurrSubActiveGroupID=groupID
						from ams_groups
						where groupCode like 'subActive_' + convert(varchar, @currSubscriptionID) + '_tracking'

						IF @loopCurrSubActiveGroupID is not null BEGIN
							exec dbo.ams_createMemberGroup @memberID, @loopCurrSubActiveGroupID, 1
						END
					END

					-- update groups.  Check if this subscription still exists in a P status, if not then remove
					select @tempCount=count(subscriberID)
					from dbo.sub_subscribers s
					inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode = 'P'
					inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
					where s.subscriptionID = @currSubscriptionID

					IF @tempCount = 0
					begin
						select @loopCurrSubPendingGroupID=null
						select @loopCurrSubPendingGroupID=groupID
						from ams_groups
						where groupCode like 'subPending_' + convert(varchar, @currSubscriptionID) + '_tracking'

						IF @loopCurrSubPendingGroupID is not null BEGIN
							exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubPendingGroupID
						END
					end

					select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus
					while @minSubscriberID is not null
					begin
						EXEC sub_updateSubscriberStatus @minSubscriberID, @newStatusCode, @siteID, @enteredByActiveMemberID, @insideResult OUTPUT
						select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus where subscriberID > @minSubscriberID
					end
					
					-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
					EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberID, @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

					select @result = 1
				END
				ELSE IF @currStatusCode = 'D'
				BEGIN
					-- before we make this active, we need to see if there is another active/inactive that needs to expire
					select @prevSubscriberID=s.subscriberID
					from dbo.sub_subscribers s
					inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode in ('A', 'I')
					inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID 
					inner join dbo.sub_types t on t.typeID = subs.typeID and t.siteID = @siteID											
					where s.subscriptionID = @currSubscriptionID
					and s.subscriberID <> @subscriberID
					and s.memberID = @memberID

					IF @prevSubscriberID is not null
					BEGIN
						select @result = 0
					END
					ELSE
					BEGIN
						/* Don't undo the tree, just the current one */
						select @tempEndDate=sParent.subEndDate, @tempGraceEndDate=sParent.graceEndDate
						from dbo.fn_getRecursiveMemberSubscriptions(@memberID, @siteID, @subscriberID) rms
						left outer join dbo.sub_subscribers sParent on sParent.subscriberID = rms.rootSubscriberID
						where rms.status = 'D'
						
						update dbo.sub_subscribers
						set statusID = @newStatusID,
						subEndDate=@tempEndDate,
						graceEndDate=@tempGraceEndDate
						where subscriberID = @subscriberID

						insert into dbo.sub_statusHistory(subscriberID, oldStatusID, statusID, enteredByMemberID)
						values(@subscriberID, @currStatusID, @newStatusID, @enteredByActiveMemberID)
						
						IF @currPaymentStatusCode = 'N'
						BEGIN
							-- update groups.  
							select @loopCurrSubWaitingGroupID=null
							select @loopCurrSubActiveGroupID=groupID
							from ams_groups
							where groupCode like 'subWaitingPay_' + convert(varchar, @currSubscriptionID) + '_tracking'

							IF @loopCurrSubWaitingGroupID is not null BEGIN
								exec dbo.ams_createMemberGroup @memberID, @loopCurrSubWaitingGroupID, 0
							END
						END
						ELSE
						BEGIN					
							-- update groups.  
							select @loopCurrSubActiveGroupID=null
							select @loopCurrSubActiveGroupID=groupID
							from ams_groups
							where groupCode like 'subActive_' + convert(varchar, @currSubscriptionID) + '_tracking'

							IF @loopCurrSubActiveGroupID is not null BEGIN
								exec dbo.ams_createMemberGroup @memberID, @loopCurrSubActiveGroupID, 0
							END
						END

						select @result = 0
					END

				END
			END
		END
		ELSE IF @newStatusCode = 'I'
		BEGIN
			IF @currStatusCode is not null
			BEGIN
				IF @currStatusCode = 'A'
				BEGIN
					/*
						update the subscription that was sent, 
						put in inactive or waiting group, based on payment status
						check if member has a subscription of same ID that is active.  If not, remove from active group
						then loop over the others to call status update
					*/
					update dbo.sub_subscribers
					set statusID = @newStatusID
					where subscriberID = @subscriberID
					
					insert into dbo.sub_statusHistory(subscriberID, oldStatusID, statusID, enteredByMemberID)
					values(@subscriberID, @currStatusID, @newStatusID, @enteredByActiveMemberID)

					-- update groups.  Add to inactive, would also check on payment status
					select @loopCurrSubInactiveGroupID=null
					select @loopCurrSubInactiveGroupID=groupID
					from ams_groups
					where groupCode like 'subInactive_' + convert(varchar, @currSubscriptionID) + '_tracking'

					IF @loopCurrSubInactiveGroupID is not null BEGIN
						exec dbo.ams_createMemberGroup @memberID, @loopCurrSubInactiveGroupID, 1
					END

					-- update groups.  Check if this subscription still exists in an A status, if not then remove
					select @tempCount=count(s.subscriberID)
					from dbo.sub_subscribers s
					inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode = 'A'
					inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
					where s.subscriptionID = @currSubscriptionID

					IF @tempCount = 0
					begin
						select @loopCurrSubActiveGroupID=null
						select @loopCurrSubActiveGroupID=groupID
						from ams_groups
						where groupCode like 'subActive_' + convert(varchar, @currSubscriptionID) + '_tracking'

						IF @loopCurrSubActiveGroupID is not null BEGIN
							exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubActiveGroupID
						END
					end

					-- update groups.  Check if this subscription still exists in an N payment status, if not then remove
					select @tempCount=count(s.subscriberID)
					from dbo.sub_subscribers s
					inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID and pst.statusCode = 'N'
					inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
					where s.subscriptionID = @currSubscriptionID

					IF @tempCount = 0
					begin
						select @loopCurrSubWaitingGroupID=null
						select @loopCurrSubWaitingGroupID=groupID
						from ams_groups
						where groupCode like 'subWaitingPay_' + convert(varchar, @currSubscriptionID) + '_tracking'

						IF @loopCurrSubWaitingGroupID is not null BEGIN
							exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubWaitingGroupID
						END
					end

					select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus
					while @minSubscriberID is not null
					begin
						EXEC sub_updateSubscriberStatus @minSubscriberID, @newStatusCode, @siteID, @enteredByActiveMemberID, @insideResult OUTPUT
						select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus where subscriberID > @minSubscriberID
					end
					
					-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
					EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberID, @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

					select @result = 1
				END
			END
		END
		ELSE IF @newStatusCode = 'R'
		BEGIN
			IF @currStatusCode is not null
			BEGIN
				IF @currStatusCode = 'D'
				BEGIN
					/* Don't undo the tree, just the current one */
					select @tempEndDate=sParent.subEndDate, @tempGraceEndDate=sParent.graceEndDate
					from dbo.fn_getRecursiveMemberSubscriptions(@memberID, @siteID, @subscriberID) rms
					left outer join dbo.sub_subscribers sParent on sParent.subscriberID = rms.parentSubscriberID
					where rms.status = 'D'
					
					update dbo.sub_subscribers
					set statusID = @newStatusID,
					subEndDate=@tempEndDate,
					graceEndDate=@tempGraceEndDate
					where subscriberID = @subscriberID

					insert into dbo.sub_statusHistory(subscriberID, oldStatusID, statusID, enteredByMemberID)
					values(@subscriberID, @currStatusID, @newStatusID, @enteredByActiveMemberID)
					
					-- update groups.  Add to active, would also check on payment status
					select @loopCurrSubRenewGroupID=null
					select @loopCurrSubRenewGroupID=groupID
					from ams_groups
					where groupCode like 'subRenew_' + convert(varchar, @currSubscriptionID) + '_tracking'

					IF @loopCurrSubRenewGroupID is not null BEGIN
						exec dbo.ams_createMemberGroup @memberID, @loopCurrSubRenewGroupID, 0
					END
				END
			END
		END
		ELSE IF @newStatusCode = 'O'
		BEGIN
			IF @currStatusCode is not null
			BEGIN
				IF @currStatusCode = 'R'
				BEGIN
					/*
						print 'Renewal Not Sent to Offered'
						update the subscription that was sent, 
						put in renew or waiting group, based on payment status
						O and R are both in renew group, so no removal
						then loop over the others to call status update
					*/

					update dbo.sub_subscribers
					set statusID = @newStatusID
					where subscriberID = @subscriberID

					insert into dbo.sub_statusHistory(subscriberID, oldStatusID, statusID, enteredByMemberID)
					values(@subscriberID, @currStatusID, @newStatusID, @enteredByActiveMemberID)

					-- update groups.  Add to inactive, would also check on payment status
					-- should already be in active, but this will make sure
					select @loopCurrSubInactiveGroupID=null
					select @loopCurrSubInactiveGroupID=groupID
					from ams_groups
					where groupCode like 'subInactive_' + convert(varchar, @currSubscriptionID) + '_tracking'

					IF @loopCurrSubInactiveGroupID is not null BEGIN
						exec dbo.ams_createMemberGroup @memberID, @loopCurrSubInactiveGroupID, 1
					END

					select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus
					while @minSubscriberID is not null
					begin
						EXEC sub_updateSubscriberStatus @minSubscriberID, @newStatusCode, @siteID, @enteredByActiveMemberID, @insideResult OUTPUT
						select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus where subscriberID > @minSubscriberID
					end
					
					-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
					EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberID, @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

					select @result = 1
				END
				ELSE IF @currStatusCode = 'D'
				BEGIN
					/* Don't undo the tree, just the current one */
					select @tempEndDate=sParent.subEndDate, @tempGraceEndDate=sParent.graceEndDate
					from dbo.fn_getRecursiveMemberSubscriptions(@memberID, @siteID, @subscriberID) rms
					left outer join dbo.sub_subscribers sParent on sParent.subscriberID = rms.parentSubscriberID
					where rms.status = 'D'
					
					update dbo.sub_subscribers
					set statusID = @newStatusID,
					subEndDate=@tempEndDate,
					graceEndDate=@tempGraceEndDate
					where subscriberID = @subscriberID

					insert into dbo.sub_statusHistory(subscriberID, oldStatusID, statusID, enteredByMemberID)
					values(@subscriberID, @currStatusID, @newStatusID, @enteredByActiveMemberID)
					
					-- update groups.  Add to active, would also check on payment status
					select @loopCurrSubRenewGroupID=null
					select @loopCurrSubRenewGroupID=groupID
					from ams_groups
					where groupCode like 'subRenew_' + convert(varchar, @currSubscriptionID) + '_tracking'

					IF @loopCurrSubRenewGroupID is not null BEGIN
						exec dbo.ams_createMemberGroup @memberID, @loopCurrSubRenewGroupID, 0
					END
				END
			END
		END
		ELSE IF @newStatusCode = 'P'
		BEGIN
			IF @currStatusCode is not null
			BEGIN
				IF @currStatusCode = 'O'
				BEGIN
					/*
						print 'Offered to Accepted'
						update the subscription that was sent, 
						put in pending or waiting group, based on payment status
						check if member has a subscription of same ID that is O or R.  If not, remove from renew group
						then loop over the others to call status update
					*/
					update dbo.sub_subscribers
					set statusID = @newStatusID
					where subscriberID = @subscriberID

					insert into dbo.sub_statusHistory(subscriberID, oldStatusID, statusID, enteredByMemberID)
					values(@subscriberID, @currStatusID, @newStatusID, @enteredByActiveMemberID)

					IF @currPaymentStatusCode = 'N'
					BEGIN
						-- update groups.  Add to active, would also check on payment status
						select @loopCurrSubWaitingGroupID=null
						select @loopCurrSubActiveGroupID=groupID
						from ams_groups
						where groupCode like 'subWaitingPay_' + convert(varchar, @currSubscriptionID) + '_tracking'

						IF @loopCurrSubWaitingGroupID is not null BEGIN
							exec dbo.ams_createMemberGroup @memberID, @loopCurrSubWaitingGroupID, 1
						END
					END
					ELSE
					BEGIN					
						-- update groups.  Add to active, would also check on payment status
						select @loopCurrSubPendingGroupID=null
						select @loopCurrSubPendingGroupID=groupID
						from ams_groups
						where groupCode like 'subPending_' + convert(varchar, @currSubscriptionID) + '_tracking'

						IF @loopCurrSubPendingGroupID is not null BEGIN
							exec dbo.ams_createMemberGroup @memberID, @loopCurrSubPendingGroupID, 1
						END
					END
					
					-- update groups.  Check if this subscription still exists in a R or O status, if not then remove
					select @tempCount=count(s.subscriberID)
					from dbo.sub_subscribers s
					inner join dbo.sub_statuses st on st.statusID = s.statusID AND st.statusCode in ('R','O')
					inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
					where s.subscriptionID = @currSubscriptionID
					
					IF @tempCount = 0
					begin
						select @loopCurrSubRenewGroupID=null
						select @loopCurrSubRenewGroupID=groupID
						from ams_groups
						where groupCode like 'subRenew_' + convert(varchar, @currSubscriptionID) + '_tracking'

						IF @loopCurrSubRenewGroupID is not null BEGIN
							exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubRenewGroupID
						END
					end

					select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus
					while @minSubscriberID is not null
					begin
						EXEC sub_updateSubscriberStatus @minSubscriberID, @newStatusCode, @siteID, @enteredByActiveMemberID, @insideResult OUTPUT
						select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus where subscriberID > @minSubscriberID
					end
					
					-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
					EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberID, @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

					select @result = 1
				END
				ELSE IF @currStatusCode = 'D'
				BEGIN
					/* Don't undo the tree, just the current one */
					select @tempEndDate=sParent.subEndDate, @tempGraceEndDate=sParent.graceEndDate
					from dbo.fn_getRecursiveMemberSubscriptions(@memberID, @siteID, @subscriberID) rms
					left outer join dbo.sub_subscribers sParent on sParent.subscriberID = rms.parentSubscriberID
					where rms.status = 'D'
					
					update dbo.sub_subscribers
					set statusID = @newStatusID,
					subEndDate=@tempEndDate,
					graceEndDate=@tempGraceEndDate
					where subscriberID = @subscriberID

					insert into dbo.sub_statusHistory(subscriberID, oldStatusID, statusID, enteredByMemberID)
					values(@subscriberID, @currStatusID, @newStatusID, @enteredByActiveMemberID)
					
					IF @currPaymentStatusCode = 'N'
					BEGIN
						-- update groups.  Add to active, would also check on payment status
						select @loopCurrSubWaitingGroupID=null
						select @loopCurrSubActiveGroupID=groupID
						from ams_groups
						where groupCode like 'subWaitingPay_' + convert(varchar, @currSubscriptionID) + '_tracking'

						IF @loopCurrSubWaitingGroupID is not null BEGIN
							exec dbo.ams_createMemberGroup @memberID, @loopCurrSubWaitingGroupID, 0
						END
					END
					ELSE
					BEGIN					
						-- update groups.  Add to active, would also check on payment status
						select @loopCurrSubPendingGroupID=null
						select @loopCurrSubPendingGroupID=groupID
						from ams_groups
						where groupCode like 'subPending_' + convert(varchar, @currSubscriptionID) + '_tracking'

						IF @loopCurrSubPendingGroupID is not null BEGIN
							exec dbo.ams_createMemberGroup @memberID, @loopCurrSubPendingGroupID, 0
						END
					END
				END
			END
		END
		ELSE IF @newStatusCode = 'E'  
		BEGIN
			IF @currStatusCode is not null
			BEGIN
				IF @currStatusCode = 'A' OR @currStatusCode = 'I'
				BEGIN
					/*
						update the subscription that was sent, 
						put in expired or waiting group, based on payment status
						check if member has a subscription of same ID that is active.  If not, remove from active group
						then loop over the others to call status update
					*/

					update dbo.sub_subscribers
					set statusID = @newStatusID
					where subscriberID = @subscriberID

					if @currSubEndDate > @endDate
					BEGIN 
						update dbo.sub_subscribers
						set subEndDate = @endDate
						where subscriberID = @subscriberID

						update dbo.sub_subscribers
						set graceEndDate = @endDate
						where subscriberID = @subscriberID
						and graceEndDate is not null
					END

					insert into dbo.sub_statusHistory(subscriberID, oldStatusID, statusID, enteredByMemberID)
					values(@subscriberID, @currStatusID, @newStatusID, @enteredByActiveMemberID)

					-- update groups.  Add to active, would also check on payment status
					select @loopCurrSubInactiveGroupID=null
					select @loopCurrSubInactiveGroupID=groupID
					from ams_groups
					where groupCode like 'subInactive_' + convert(varchar, @currSubscriptionID) + '_tracking'

					IF @loopCurrSubInactiveGroupID is not null BEGIN
						exec dbo.ams_createMemberGroup @memberID, @loopCurrSubInactiveGroupID, 1
					END

					-- waiting group
					select @tempCount=count(s.subscriberID)
					from dbo.sub_subscribers s
					inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID AND pst.statusCode = 'N'
					inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
					where s.subscriptionID = @currSubscriptionID

					IF @tempCount = 0
					begin
						select @loopCurrSubWaitingGroupID=null
						select @loopCurrSubWaitingGroupID=groupID
						from ams_groups
						where groupCode like 'subWaitingPay_' + convert(varchar, @currSubscriptionID) + '_tracking'

						IF @loopCurrSubWaitingGroupID is not null BEGIN
							exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubWaitingGroupID
						END
					end

					-- update groups.  Check if this subscription still exists in a A status, if not then remove
					select @tempCount=count(s.subscriberID)
					from dbo.sub_subscribers s
					inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode = 'A'
					inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
					where s.subscriptionID = @currSubscriptionID

					IF @tempCount = 0
					begin
						select @loopCurrSubActiveGroupID=null
						select @loopCurrSubActiveGroupID=groupID
						from ams_groups
						where groupCode like 'subActive_' + convert(varchar, @currSubscriptionID) + '_tracking'

						IF @loopCurrSubActiveGroupID is not null BEGIN
							exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubActiveGroupID
						END
					end

					select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus
					while @minSubscriberID is not null
					begin
						EXEC sub_updateSubscriberStatus @minSubscriberID, @newStatusCode, @siteID, @enteredByActiveMemberID, @insideResult OUTPUT
						select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus where subscriberID > @minSubscriberID
					end
					
					-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
					EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberID, @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

					select @result = 1
				END
				ELSE IF @currStatusCode = 'P'
				BEGIN
					/*
						update the subscription that was sent, 
						put in expired or waiting group, based on payment status
						check if member has a subscription of same ID that is pending.  If not, remove from pending group
						then loop over the others to call status update
					*/

					update dbo.sub_subscribers
					set statusID = @newStatusID
					where subscriberID = @subscriberID

					if @currSubEndDate > @endDate
					BEGIN 
						update dbo.sub_subscribers
						set subEndDate = @endDate
						where subscriberID = @subscriberID

						update dbo.sub_subscribers
						set graceEndDate = @endDate
						where subscriberID = @subscriberID
						and graceEndDate is not null
					END

					insert into dbo.sub_statusHistory(subscriberID, oldStatusID, statusID, enteredByMemberID)
					values(@subscriberID, @currStatusID, @newStatusID, @enteredByActiveMemberID)

					-- update groups.  Add to active, would also check on payment status
					select @loopCurrSubInactiveGroupID=null
					select @loopCurrSubInactiveGroupID=groupID
					from ams_groups
					where groupCode like 'subInactive_' + convert(varchar, @currSubscriptionID) + '_tracking'

					IF @loopCurrSubInactiveGroupID is not null BEGIN
						exec dbo.ams_createMemberGroup @memberID, @loopCurrSubInactiveGroupID, 1
					END

					-- waiting group
					select @tempCount=count(s.subscriberID)
					from dbo.sub_subscribers s
					inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID AND pst.statusCode = 'N'
					inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
					where s.subscriptionID = @currSubscriptionID

					IF @tempCount = 0
					begin
						select @loopCurrSubWaitingGroupID=null
						select @loopCurrSubWaitingGroupID=groupID
						from ams_groups
						where groupCode like 'subWaitingPay_' + convert(varchar, @currSubscriptionID) + '_tracking'

						IF @loopCurrSubWaitingGroupID is not null BEGIN
							exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubWaitingGroupID
						END
					end

					-- update groups.  Check if this subscription still exists in a P status, if not then remove
					select @tempCount=count(s.subscriberID)
					from dbo.sub_subscribers s
					inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode = 'P'
					inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
					where s.subscriptionID = @currSubscriptionID

					IF @tempCount = 0
					begin
						select @loopCurrSubPendingGroupID=null
						select @loopCurrSubPendingGroupID=groupID
						from ams_groups
						where groupCode like 'subPending_' + convert(varchar, @currSubscriptionID) + '_tracking'

						IF @loopCurrSubPendingGroupID is not null BEGIN
							exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubPendingGroupID
						END
					end

					select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus
					while @minSubscriberID is not null
					begin
						EXEC sub_updateSubscriberStatus @minSubscriberID, @newStatusCode, @siteID, @enteredByActiveMemberID, @insideResult OUTPUT
						select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus where subscriberID > @minSubscriberID
					end
					
					-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
					EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberID, @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

					select @result = 1
				END
			END
		END
		ELSE IF @newStatusCode = 'X'
		BEGIN
			IF @currStatusCode is not null
			BEGIN
				IF @currStatusCode = 'R' OR @currStatusCode = 'O'
				BEGIN
					/*
						update the subscription that was sent, 
						check if member has a subscription of same ID that is R or O.  If not, remove from renew group
						then loop over the others to call status update
					*/
					update dbo.sub_subscribers
					set statusID = @newStatusID
					where subscriberID = @subscriberID

					insert into dbo.sub_statusHistory(subscriberID, oldStatusID, statusID, enteredByMemberID)
					values(@subscriberID, @currStatusID, @newStatusID, @enteredByActiveMemberID)

					-- update groups.  Check if this subscription still exists in a I or E status, if not then remove
					select @tempCount=count(s.subscriberID)
					from dbo.sub_subscribers s
					inner join dbo.sub_statuses st on st.statusID = s.statusID AND st.statusCode in ('O','R')
					inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
					where s.subscriptionID = @currSubscriptionID

					IF @tempCount = 0
					begin
						select @loopCurrSubRenewGroupID=null
						select @loopCurrSubRenewGroupID=groupID
						from ams_groups
						where groupCode like 'subRenew_' + convert(varchar, @currSubscriptionID) + '_tracking'

						IF @loopCurrSubRenewGroupID is not null BEGIN
							exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubRenewGroupID
						END
					end

					select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus
					while @minSubscriberID is not null
					begin
						EXEC sub_updateSubscriberStatus @minSubscriberID, @newStatusCode, @siteID, @enteredByActiveMemberID, @insideResult OUTPUT
						select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus where subscriberID > @minSubscriberID
					end

					-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
					EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberID, @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

					select @result = 1
				END
			END
		END
		ELSE IF @newStatusCode = 'D'
		BEGIN
			IF (@currStatusCode is not null) AND (@currStatusCode <> 'D')
			BEGIN
				/*
					update the subscription that was sent, 
					check if member has a subscription of same ID that in any status.  remove from sub groups where applicable
					then loop over the others to call status update
				*/

				update dbo.sub_subscribers
				set statusID = @newStatusID
				where subscriberID = @subscriberID

				if @currSubEndDate > @endDate
				BEGIN 
					update dbo.sub_subscribers
					set subEndDate = @endDate
					where subscriberID = @subscriberID

					update dbo.sub_subscribers
					set graceEndDate = @endDate
					where subscriberID = @subscriberID
					and graceEndDate is not null
				END

				insert into dbo.sub_statusHistory(subscriberID, oldStatusID, statusID, enteredByMemberID)
				values(@subscriberID, @currStatusID, @newStatusID, @enteredByActiveMemberID)

				-- update groups.  Check if this subscription still exists in A status, if not then remove
				select @tempCount=count(s.subscriberID)
				from dbo.sub_subscribers s
				inner join dbo.sub_statuses st on st.statusID = s.statusID AND st.statusCode = 'A'
				inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
				where s.subscriptionID = @currSubscriptionID

				IF @tempCount = 0
				begin
					select @loopCurrSubActiveGroupID=null
					select @loopCurrSubActiveGroupID=groupID
					from ams_groups
					where groupCode like 'subActive_' + convert(varchar, @currSubscriptionID) + '_tracking'

					IF @loopCurrSubActiveGroupID is not null BEGIN
						exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubActiveGroupID
					END
				end

				-- waiting group
				select @tempCount=count(s.subscriberID)
				from dbo.sub_subscribers s
				inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID AND pst.statusCode = 'N'
				inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
				where s.subscriptionID = @currSubscriptionID

				IF @tempCount = 0
				begin
					select @loopCurrSubWaitingGroupID=null
					select @loopCurrSubWaitingGroupID=groupID
					from ams_groups
					where groupCode like 'subWaitingPay_' + convert(varchar, @currSubscriptionID) + '_tracking'

					IF @loopCurrSubWaitingGroupID is not null BEGIN
						exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubWaitingGroupID
					END
				end

				-- update groups.  Check if this subscription still exists in a I or E status, if not then remove
				select @tempCount=count(s.subscriberID)
				from dbo.sub_subscribers s
				inner join dbo.sub_statuses st on st.statusID = s.statusID AND st.statusCode in ('I','E')
				inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
				where s.subscriptionID = @currSubscriptionID
				
				IF @tempCount = 0
				begin
					select @loopCurrSubInactiveGroupID=null
					select @loopCurrSubInactiveGroupID=groupID
					from ams_groups
					where groupCode like 'subInactive_' + convert(varchar, @currSubscriptionID) + '_tracking'

					IF @loopCurrSubInactiveGroupID is not null BEGIN
						exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubInactiveGroupID
					END
				end

				-- update groups.  Check if this subscription still exists in a R or O status, if not then remove
				select @tempCount=count(s.subscriberID)
				from dbo.sub_subscribers s
				inner join dbo.sub_statuses st on st.statusID = s.statusID AND st.statusCode in ('R','O')
				inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
				where s.subscriptionID = @currSubscriptionID

				IF @tempCount = 0
				begin
					select @loopCurrSubRenewGroupID=null
					select @loopCurrSubRenewGroupID=groupID
					from ams_groups
					where groupCode like 'subRenew_' + convert(varchar, @currSubscriptionID) + '_tracking'

					IF @loopCurrSubRenewGroupID is not null BEGIN
						exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubRenewGroupID
					END
				end

				-- update groups.  Check if this subscription still exists in P status, if not then remove
				select @tempCount=count(s.subscriberID)
				from dbo.sub_subscribers s
				inner join dbo.sub_statuses st on st.statusID = s.statusID AND st.statusCode = 'P'
				inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
				where s.subscriptionID = @currSubscriptionID
		
				IF @tempCount = 0
				begin
					select @loopCurrSubPendingGroupID=null
					select @loopCurrSubPendingGroupID=groupID
					from ams_groups
					where groupCode like 'subPending_' + convert(varchar, @currSubscriptionID) + '_tracking'

					IF @loopCurrSubPendingGroupID is not null BEGIN
						exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubPendingGroupID
					END
				end
		
				select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus
				while @minSubscriberID is not null
				begin
					EXEC sub_updateSubscriberStatus @minSubscriberID, @newStatusCode, @siteID, @enteredByActiveMemberID, @insideResult OUTPUT
					select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus where subscriberID > @minSubscriberID
				end
				
				-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
				EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberID, @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

				select @result = 1
			END
		END

	END

END

goto done

on_error:
select @result = -1

done:
GO
