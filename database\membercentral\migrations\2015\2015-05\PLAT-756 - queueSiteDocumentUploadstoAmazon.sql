use dataTransfer;
GO



IF NOT EXISTS(SELECT name FROM sys.sysobjects  
          WHERE Name = N'siteDocumentsS3UploadQueue' AND xtype = N'U')
BEGIN
    CREATE TABLE dbo.siteDocumentsS3UploadQueue
	    (
	    documentVersionID int NOT NULL,
	    documentID int NOT NULL,
	    siteID int NOT NULL,
	    fileExt varchar(20) NOT NULL,
	    hasFailed bit NOT NULL,
	    nextAttemptDate datetime NOT NULL
	    )  ON [PRIMARY]
END

GO

IF NOT EXISTS(SELECT name 
    FROM sys.indexes 
    WHERE name='PK_siteDocumentsS3UploadQueue' AND object_id = OBJECT_ID('siteDocumentsS3UploadQueue'))
BEGIN
    ALTER TABLE dbo.siteDocumentsS3UploadQueue ADD CONSTRAINT
	    PK_siteDocumentsS3UploadQueue PRIMARY KEY CLUSTERED 
	    (
	    documentVersionID
	    ) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
END
GO


use membercentral;
GO
insert into dataTransfer.dbo.siteDocumentsS3UploadQueue(documentVersionID, documentID, siteID, fileExt, hasFailed, nextAttemptDate)
select dv.documentVersionID, d.documentID, d.siteID, dv.fileExt, 0 as hasfailed, getdate() as nextAttemptDate
from cms_documentVersions dv with (nolock)
inner join cms_documentLanguages dl with (nolock)
    on dl.documentLanguageID = dv.documentLanguageID
    and dv.dateCreated > '5/8/2015'
inner join cms_documents d with (nolock)
    on d.documentID = dl.documentID

GO
ALTER PROC [dbo].[cms_createDocumentVersion]
@documentLanguageID int,
@filename varchar(255),
@fileExt varchar(20),
<AUTHOR>
@contributorMemberID int,
@recordedByMemberID int,
@isActive bit,
@publicationDate datetime,
@documentVersionID int OUTPUT

AS

SET NOCOUNT ON

DECLARE @siteID int, @documentID int

 

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

    select @siteID=d.siteID, @documentID=d.documentID
    from cms_documentLanguages dl
    inner join cms_documents d
	   on d.documentID = dl.documentID
	   and dl.documentLanguageID = @documentLanguageID

	-- ensure @documentVersionID is null (can be passed in)
	SELECT @documentVersionID = null

	-- ensure active contributer memberid
	SELECT @contributorMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @contributorMemberID

	-- if isactive = 1, then deactivate all other versions of this documentLanguageID
	IF @isactive = 1 BEGIN
		UPDATE dbo.cms_documentVersions
		SET isActive = 0
		WHERE documentLanguageID = @documentLanguageID
	END

	-- add document version
	INSERT INTO dbo.cms_documentVersions (documentLanguageID, [fileName], fileExt, publicationDate, contributorMemberID, isActive, dateCreated, dateModified, author, recordedByMemberID)
	VALUES (@documentLanguageID, @fileName, @fileExt, @publicationDate, @contributorMemberID, @isactive, getdate(), getdate(), @author, @recordedByMemberID)
		SELECT @documentVersionID = SCOPE_IDENTITY()

	-- update documentLanguages dateModified
	UPDATE dbo.cms_documentLanguages 
	SET dateModified = getdate()
	WHERE documentLanguageID = @documentLanguageID


	insert into datatransfer.dbo.siteDocumentsS3UploadQueue (documentVersionID, documentID, siteID, fileExt, hasFailed, nextAttemptDate)
	values (@documentVersionID, @documentID, @siteID, @fileExt, 0, getdate())

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	SELECT @documentVersionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO
