use membercentral
GO
declare @toolTypeID int, @toolResourceTypeID int, @level3NavID int, @navigationID int, @resourceTypeFunctionID int, @siteID int
set @level3NavID = 99
SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('Admin'),dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('Admin')))

EXEC dbo.createAdminToolType @toolType='EventScheduleReport', @toolCFC='Reports.events.EventScheduleReport', @toolDesc='Event Schedule', @toolTypeID=@toolTypeID OUTPUT, @resourceTypeID=@toolResourceTypeID OUTPUT
	EXEC dbo.createAdminNavigation @navName='Event Schedule', @navDesc='Calendar Event Schedule by Day for a given date range.', @parentNavigationID=@level3NavID, @navAreaID=4, @cfcMethod='showReport', @isHeader=0, @showInNav=1, @navigationID=@navigationID OUTPUT
		EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID=@resourceTypeFunctionID, @toolTypeID=@toolTypeID, @navigationID=@navigationID

insert into dbo.admin_siteToolRestrictions (toolTypeID, siteID)
select @toolTypeID, siteID
from dbo.admin_siteToolRestrictions as stre
inner join dbo.admin_toolTypes as tt on tt.toolTypeID = stre.toolTypeID
where tt.tooltype = 'CreditsByProgramReport'

select @siteID = min(siteID) from dbo.sites
while @siteID is not null BEGIN
	exec dbo.createAdminSuite @siteID
	select @siteID = min(siteID) from dbo.sites where siteID > @siteID
END
GO
