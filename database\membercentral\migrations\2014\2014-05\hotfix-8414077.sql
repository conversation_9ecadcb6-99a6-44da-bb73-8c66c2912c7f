USE [seminarWeb]
GO
CREATE PROC [dbo].[swl_completeWebinar]
@enrollmentID int
AS

-- get Seminar Progress
DECLARE @allPostTestCompleted bit, @allEvaluationCompleted bit, @hasCertificate bit, @allowAutoSendOfCertificate bit
DECLARE @SWLdaysToCompleteExam int, @SWLdaysToCompleteEvaluation int, @CreditCount int, @CreditFormNotRequired int

SELECT @allPostTestCompleted = 
	CASE 
	WHEN (select count(saf.formID)
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblSeminarsAndForms AS saf ON e.seminarID = saf.seminarID
			LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
				INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
				INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
					and f.isPublished = 1
					and getdate() between f.dateStartPublish and f.dateEndPublish	
					and r.passingPct >= f.passingPct				
				on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e.enrollmentID
			WHERE e.enrollmentID = @enrollmentID
			AND saf.loadPoint = 'postTest'
			AND saf.isRequired = 1
			AND safr.responseID is null) > 0 THEN 0
	ELSE 1
	END

SELECT @allEvaluationCompleted = 
	CASE 
	WHEN (select count(saf.formID)
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblSeminarsAndForms AS saf ON e.seminarID = saf.seminarID
			LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
				INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
				INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
					and f.isPublished = 1
					and getdate() between f.dateStartPublish and f.dateEndPublish	
				on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e.enrollmentID
			WHERE e.enrollmentID = @enrollmentID
			AND saf.loadPoint = 'evaluation'
			AND (safr.responseID is null or r.dateCompleted is null)) > 0 THEN 0
	ELSE 1
	END

SELECT @CreditCount = COUNT(*) FROM dbo.tblEnrollmentsAndCredit WHERE enrollmentID = @enrollmentID

-- Credit requested but Authority does not require survey
SELECT @CreditFormNotRequired = COUNT(sac.seminarCreditID)
				FROM dbo.tblEnrollmentsAndCredit AS eac 
				INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
				INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID 
				INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
				INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID 
				INNER JOIN dbo.tblCreditStatuses AS cstat ON sac.statusID = cstat.statusID
				INNER JOIN dbo.tblCreditAuthoritiesSWLive as caswl on caswl.authorityID = ca.authorityID
				WHERE eac.enrollmentID = @enrollmentID
				and evaluationRequired = 0

if (@allPostTestCompleted = 1 and @allEvaluationCompleted = 1) OR @CreditCount = 0 OR @CreditFormNotRequired > 0 begin
	-- if enrollment not marked passed then we need to update the flags and dates
	declare @testEnrollment int
	select @testEnrollment = e.enrollmentID
	FROM dbo.tblEnrollments AS e 
	INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
	WHERE e.enrollmentID = @enrollmentID
		AND e.passed = 0
		AND eswl.attended = 1

	if @testEnrollment is not null begin
		update dbo.tblEnrollments set passed = 1, dateCompleted = getDate()
		WHERE enrollmentID = @enrollmentID 
	end


	-- check date requirements for exams and 
	-- if failed then mark they did not pass
	SELECT	
		@SWLdaysToCompleteExam = caswl.daysToCompleteExam,
		@SWLdaysToCompleteEvaluation = caswl.daysToCompleteEvaluation
	FROM dbo.tblEnrollmentsAndCredit AS eac 
	INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
	INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID 
	INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
	INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID 
	INNER JOIN dbo.tblCreditStatuses AS cstat ON sac.statusID = cstat.statusID
	LEFT OUTER JOIN dbo.tblCreditAuthoritiesSWLive AS caswl ON ca.authorityID = caswl.authorityID 
	WHERE eac.enrollmentID = @enrollmentID


	-- create temp table to store all forms and exams
	DECLARE @tmpTable TABLE (
		loadPoint varchar(255),
		enrollmentid int,
		dateCompleted datetime,
		formCompleteDate datetime,
		completeByDate datetime
	)

	if @SWLdaysToCompleteExam > 0 BEGIN
		insert into @tmpTable
		select saf.loadPoint, e.enrollmentid, e.dateCompleted, r.datecompleted as formCompleteDate, s.dateEnd + @SWLdaysToCompleteExam as completeByDate
		FROM dbo.tblEnrollments AS e 
		INNER JOIN dbo.tblSeminarsAndForms AS saf ON e.seminarID = saf.seminarID
		INNER JOIN dbo.tblSeminarsSWLive s on s.seminarID = e.seminarID
		LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
			INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
			INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
				and f.isPublished = 1
				and getdate() between f.dateStartPublish and f.dateEndPublish	
				and r.passingPct >= f.passingPct				
			on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e.enrollmentID
		WHERE e.enrollmentID = @enrollmentID
		AND saf.loadPoint = 'postTest'
		AND saf.isRequired = 1
	END

	if @SWLdaysToCompleteEvaluation > 0 BEGIN
		insert into @tmpTable
		select saf.loadPoint, e.enrollmentid, e.dateCompleted, r.datecompleted as formCompleteDate, s.dateEnd + @SWLdaysToCompleteEvaluation as completeByDate
		FROM dbo.tblEnrollments AS e 
		INNER JOIN dbo.tblSeminarsAndForms AS saf ON e.seminarID = saf.seminarID
		INNER JOIN dbo.tblSeminarsSWLive s on s.seminarID = e.seminarID
		LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
			INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
			INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
				and f.isPublished = 1
				and getdate() between f.dateStartPublish and f.dateEndPublish	
			on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e.enrollmentID
		WHERE e.enrollmentID = @enrollmentID
		AND saf.loadPoint = 'evaluation'
		AND saf.isRequired = 1
	END

	-- If any forms completed passed due date then flag as NOT passed
	IF EXISTS(select * from @tmpTable where formCompleteDate > completeByDate) BEGIN
		print 'Failed Dates'

		update dbo.tblEnrollments set passed = 0
		WHERE enrollmentID = @enrollmentID 
	END


	-- If polling required then the earnedCert is already set
	UPDATE dbo.tblEnrollmentsAndCredit
	SET earnedCertificate = 1
	WHERE enrollCreditID IN (
		SELECT enrollCreditID
		FROM dbo.tblEnrollmentsAndCredit as eac
		WHERE dbo.fn_isPollingRequiredBasedOnSCID(eac.seminarCreditID) = 0
		and enrollmentID = @enrollmentID 
	)

end


SELECT @hasCertificate = 
	CASE 
	WHEN (SELECT count(e.enrollmentID)
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
			INNER JOIN dbo.tblSeminars AS s ON e.seminarID = s.seminarID 
			INNER JOIN dbo.tblSeminarsSWLive AS sswl ON s.seminarID = sswl.seminarID
			WHERE e.enrollmentID = @enrollmentID 
			AND s.isDeleted = 0
			AND e.isActive = 1
			AND eswl.attended = 1
			AND s.offerCertificate = 1
			AND e.passed = 1
			AND LEN(e.dateCompleted) > 0) > 0 THEN 1
	ELSE 0
	END
SELECT @allowAutoSendOfCertificate = 
	CASE 
	WHEN (select count(*) from tblLogSWLive where enrollmentID = @enrollmentID and typeID = 37) > 0 THEN 0
	WHEN (SELECT count(sac.seminarCreditID)
				FROM dbo.tblEnrollmentsAndCredit AS eac 
				INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
				INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID 
				INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
				INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID 
				INNER JOIN dbo.tblCreditStatuses AS cstat ON sac.statusID = cstat.statusID
				INNER JOIN dbo.tblCreditAuthoritiesSWLive as caswl on caswl.authorityID = ca.authorityID
				WHERE eac.enrollmentID = @enrollmentID
				and evaluationRequired = 1) > 0 THEN 1
	WHEN @CreditCount = 0 THEN 1
	WHEN (SELECT count(sac.seminarCreditID)
				FROM dbo.tblEnrollmentsAndCredit AS eac 
				INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
				INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID 
				INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
				INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID 
				INNER JOIN dbo.tblCreditStatuses AS cstat ON sac.statusID = cstat.statusID
				INNER JOIN dbo.tblCreditAuthoritiesSWLive as caswl on caswl.authorityID = ca.authorityID
				WHERE eac.enrollmentID = @enrollmentID
				and evaluationRequired = 0) > 0 THEN 1
	ELSE 0
	END

SELECT e.*, @hasCertificate as hasCertificate, @allowAutoSendOfCertificate as allowAutoSendOfCertificate
FROM dbo.tblEnrollments AS e 
INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
INNER JOIN dbo.tblSeminars AS s ON e.seminarID = s.seminarID 
INNER JOIN dbo.tblSeminarsSWLive AS sswl ON s.seminarID = sswl.seminarID
WHERE e.enrollmentID = @enrollmentID 
AND s.isDeleted = 0
AND e.isActive = 1
/*
AND eswl.attended = 1
AND s.offerCertificate = 1
AND e.passed = 1
AND LEN(e.dateCompleted) > 0
*/
GO

ALTER PROC [dbo].[swl_importWebAttendance]
@seminarID int

AS

-- remove double quotes and adjust attended, polling
update ##tmpAttendeeReport set [First Name] = replace([First Name],'""',''''), [Last Name] = replace([Last Name],'""','''')
update ##tmpAttendeeReport set [First Name] = replace([First Name],'"',''), [Last Name] = replace([Last Name],'"','')
update ##tmpAttendeeReport set [attended] = case [attended] when 'Yes' then 1 else 0 end
update ##tmpAttendeeReport set [polling] = case when polling is null then 0 when polling = 'Yes' then 1 else 0 end

-- match on email address (reserved seats)
-- do this first so reserved seats will override any normal enrollments
UPDATE tmp
SET tmp.enrollmentID = ps.enrollmentID
FROM ##tmpAttendeeReport as tmp
INNER JOIN (
	select p.enrollmentID, 'swlive_l' + cast(p.seminarID as varchar(6)) + '_p' + cast(p.seatNum as varchar(6)) + '@seminarweblive.com' as email
	FROM dbo.tblParkedGTMSeats as p
	where p.seminarID = @seminarID
	and p.enrollmentID is not null
) ps on tmp.[Email] = ps.email

-- match on email address (normal enrollment)
UPDATE tmp
SET tmp.enrollmentID = swle.enrollmentID
FROM ##tmpAttendeeReport as tmp
INNER JOIN (
	select e.enrollmentID, 'swlive_l' + cast(e.seminarID as varchar(6)) + '_r' + cast(e.enrollmentID as varchar(6)) + '@seminarweblive.com' as email
	FROM dbo.tblEnrollments e
	where e.seminarID = @seminarID
) swle on tmp.[Email] = swle.email
WHERE swle.enrollmentID not in (
	select enrollmentID
	from ##tmpAttendeeReport
	where enrollmentID is not null
)

BEGIN TRAN
	-- update 1: update attendance data in swl table
	UPDATE dbo.tblEnrollmentsSWLive
	set dbo.tblEnrollmentsSWLive.attended = tmp.[attended],
		dbo.tblEnrollmentsSWLive.joinTime = case when len(tmp.[Join Time]) > 0 then right(convert(varchar(25),convert(smalldatetime, replace(replace(tmp.[Join Time],'CDT',''),'CST','')),100),7) else '' end,
		dbo.tblEnrollmentsSWLive.exitTime = case when len(tmp.[Leave Time]) > 0 then right(convert(varchar(25),convert(smalldatetime, replace(replace(tmp.[Leave Time],'CDT',''),'CST','')),100),7) else '' end,
		dbo.tblEnrollmentsSWLive.duration = tmp.[In Session Duration* (minutes) ],
		dbo.tblEnrollmentsSWLive.completedPolling = CAST(tmp.[polling] as bit)
	FROM dbo.tblEnrollmentsSWLive
	INNER JOIN ##tmpAttendeeReport as tmp on tmp.enrollmentID = dbo.tblEnrollmentsSWLive.enrollmentID
		IF (@@ERROR <> 0) GOTO on_error

	-- get date end and pin usage
	DECLARE @dateEnd datetime, @usePIN bit
	SELECT TOP 1 @dateEnd = dateEnd, @usePIN = premiereUsePIN 
	FROM dbo.tblSeminarsSWLive WHERE seminarID = @seminarID
		IF (@@ERROR <> 0) GOTO on_error

	-- update 2: mark date completed as the end date of the seminar	
/*	
	UPDATE dbo.tblEnrollments
	SET dateCompleted = @dateEnd
	WHERE enrollmentID IN (SELECT enrollmentID from ##tmpAttendeeReport where [attended] = 1)
		IF (@@ERROR <> 0) GOTO on_error

	-- update 3: update enrollments and mark passed if 1) attended web portion and 2) phone portion if PIN numbers used
	UPDATE dbo.tblEnrollments
	SET passed = 1
	WHERE enrollmentID IN (SELECT enrollmentID from ##tmpAttendeeReport where [attended] = 1)
	AND @usePIN = 0
		IF (@@ERROR <> 0) GOTO on_error
	UPDATE dbo.tblEnrollments
	SET passed = 1
	WHERE enrollmentID IN (SELECT enrollmentID from ##tmpAttendeeReport where [attended] = 1)
	AND enrollmentID IN (SELECT enrollmentID from dbo.tblEnrollmentsSWLive where enrollmentID = dbo.tblEnrollments.enrollmentID and attendedPhone = 1)
	AND @usePIN = 1
		IF (@@ERROR <> 0) GOTO on_error
*/
	-- update 4: update enrollmentAndCredit table
	UPDATE dbo.tblEnrollmentsAndCredit
	set dbo.tblEnrollmentsAndCredit.finalTimeSpent = round(tmp.[In Session Duration* (minutes) ],0),
		dbo.tblEnrollmentsAndCredit.earnedCertificate = 0
	FROM dbo.tblEnrollmentsAndCredit as eac
	INNER JOIN ##tmpAttendeeReport as tmp on tmp.enrollmentID = eac.enrollmentID
		IF (@@ERROR <> 0) GOTO on_error

	-- update 5: update enrollmentAndCredit and set earnedCertificate for each creditselection
/*	Disconnect the earning a certificate from web attendance upload

	UPDATE dbo.tblEnrollmentsAndCredit
	SET earnedCertificate = 1
	WHERE enrollCreditID IN (
		SELECT enrollCreditID
		FROM dbo.tblEnrollmentsAndCredit as eac
		INNER JOIN ##tmpAttendeeReport as tmp on tmp.enrollmentID = eac.enrollmentID
		WHERE dbo.fn_isPollingRequiredBasedOnSCID(eac.seminarCreditID) = 0
	)
		IF (@@ERROR <> 0) GOTO on_error

	UPDATE dbo.tblEnrollmentsAndCredit
	SET earnedCertificate = 1
	WHERE enrollCreditID IN (
		SELECT enrollCreditID
		FROM dbo.tblEnrollmentsAndCredit as eac
		INNER JOIN ##tmpAttendeeReport as tmp on tmp.enrollmentID = eac.enrollmentID
		WHERE dbo.fn_isPollingRequiredBasedOnSCID(eac.seminarCreditID) = 1
		AND tmp.polling = 1
	)
		IF (@@ERROR <> 0) GOTO on_error
*/

	-- next to final step: put all unmatched records in the unmatched table
	INSERT INTO dbo.tblUnmatchedGoToMeetingAttendance (seminarID, attended, FirstName, LastName, Email, InSessionTime, Duration, completedPolling)
	SELECT @seminarID, attended, [First Name], [Last Name], [Email], 'Join: ' + right(convert(varchar(25),convert(smalldatetime, replace(replace([Join Time],'CDT',''),'CST','')),100),7) + ' Exit: ' + right(convert(varchar(25),convert(smalldatetime, replace(replace([Leave Time],'CDT',''),'CST','')),100),7), [In Session Duration* (minutes) ] + 'Min', [polling]
	FROM ##tmpAttendeeReport
	WHERE enrollmentID is null
	AND attended = 1
		IF (@@ERROR <> 0) GOTO on_error

	-- final step: run completion checks for the enrollments
	exec dbo.swl_completeWebinar 
	SELECT enrollmentID from ##tmpAttendeeReport where [attended] = 1 and enrollmentID is not null
		IF (@@ERROR <> 0) GOTO on_error

COMMIT TRAN

-- clear old data
DROP TABLE ##tmpAttendeeReport

RETURN 1

on_error:
ROLLBACK TRAN

IF OBJECT_ID('tempdb..##tmpAttendeeReport') IS NOT NULL 
	DROP TABLE ##tmpAttendeeReport

RETURN 0
GO

ALTER PROC [dbo].[swl_matchGTMEnrollment]
@attendanceID int,
@enrollmentID int

AS

DECLARE @attended bit
DECLARE @joinTime varchar(20)
DECLARE @exitTime varchar(20)
DECLARE @duration float
DECLARE @completedPolling bit
SELECT @attended = attended, 
	@joinTime = (select joinTime from dbo.fn_InSessionTimeToTable(InSessionTime)),
	@exitTime = (select exitTime from dbo.fn_InSessionTimeToTable(InSessionTime)),
	@duration = replace(duration,'Min',''),
	@completedPolling = completedPolling
	FROM dbo.tblUnmatchedGoToMeetingAttendance
	WHERE attendanceID = @attendanceID

BEGIN TRAN

-- update 1: update swl
UPDATE dbo.tblEnrollmentsSWLive
set attended = @attended, joinTime = @joinTime, exitTime = @exitTime, duration = @duration, completedPolling = @completedPolling
WHERE enrollmentID = @enrollmentID
IF (@@ERROR <> 0) GOTO on_error

IF @attended = 0
	BEGIN
		-- clear the dateCompleted field
		UPDATE dbo.tblEnrollments
		SET dateCompleted = null, passed = 0
		WHERE enrollmentID = @enrollmentID
			IF (@@ERROR <> 0) GOTO on_error
		
		-- update credits by removing timespent and earnedCert
		UPDATE dbo.tblEnrollmentsAndCredit
		SET earnedCertificate = 0, 
			finalTimeSpent = NULL
		WHERE enrollmentID = @enrollmentID
			IF (@@ERROR <> 0) GOTO on_error
	END

-- if attended 
ELSE
	BEGIN
		DECLARE @dateEnd datetime
		SELECT @dateEnd = sswl.dateEnd
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblSeminarsSWLive AS sswl ON e.seminarID = sswl.seminarID
			WHERE e.enrollmentID = @enrollmentID
			IF (@@ERROR <> 0) GOTO on_error
		
		-- mark date completed as the end date of the seminar	
/*	Disconnect the earning a certificate from web attendance upload
		UPDATE dbo.tblEnrollments
		SET dateCompleted = @dateEnd, passed = 1
		WHERE enrollmentID = @enrollmentID
			IF (@@ERROR <> 0) GOTO on_error
*/	
		-- update credits with final time spent
		UPDATE dbo.tblEnrollmentsAndCredit
		SET earnedCertificate = 0, 
			finalTimeSpent = ROUND(@duration,0)
		WHERE enrollmentID = @enrollmentID
			IF (@@ERROR <> 0) GOTO on_error

/*	Disconnect the earning a certificate from web attendance upload
		UPDATE dbo.tblEnrollmentsAndCredit
		SET earnedCertificate = 1
		WHERE enrollmentID = @enrollmentID
		AND enrollCreditID IN (
			SELECT enrollCreditID
			FROM dbo.tblEnrollmentsAndCredit as eac
			WHERE eac.enrollmentID = @enrollmentID
			AND dbo.fn_isPollingRequiredBasedOnSCID(eac.seminarCreditID) = 0
		)
			IF (@@ERROR <> 0) GOTO on_error
		
		IF @completedPolling = 1
		BEGIN
			UPDATE dbo.tblEnrollmentsAndCredit
			SET earnedCertificate = 1
			WHERE enrollmentID = @enrollmentID
			AND enrollCreditID IN (
				SELECT enrollCreditID
				FROM dbo.tblEnrollmentsAndCredit as eac
				WHERE eac.enrollmentID = @enrollmentID
				AND dbo.fn_isPollingRequiredBasedOnSCID(eac.seminarCreditID) = 1
			)
			IF (@@ERROR <> 0) GOTO on_error
		END
*/
	END

COMMIT TRAN

-- delete from unmatched
EXEC dbo.swl_deleteGTMAttendance @attendanceID

RETURN 1

on_error:
ROLLBACK TRAN
RETURN 0
GO

ALTER PROC [dbo].[swl_hasNotMetDaysToComplete]
@enrollmentID int,
@daysToCompleteEvaluation int,
@daysToCompleteExam int
AS

-- create temp table to store all forms and exams
DECLARE @tmpTable TABLE (
	loadPoint varchar(255),
	enrollmentid int,
	dateCompleted datetime,
	formCompleteDate datetime,
	completeByDate datetime
)

if @daysToCompleteExam > 0 BEGIN
	insert into @tmpTable
	select saf.loadPoint, e.enrollmentid, e.dateCompleted, r.datecompleted as formCompleteDate, s.dateEnd + @daysToCompleteExam as completeByDate
	FROM dbo.tblEnrollments AS e 
	INNER JOIN dbo.tblSeminarsAndForms AS saf ON e.seminarID = saf.seminarID
	INNER JOIN dbo.tblSeminarsSWLive s on s.seminarID = e.seminarID
	LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
		INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
		INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
			and f.isPublished = 1
			and getdate() between f.dateStartPublish and f.dateEndPublish	
			and r.passingPct >= f.passingPct				
		on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e.enrollmentID
	WHERE e.enrollmentID = @enrollmentID
	AND saf.loadPoint = 'postTest'
	AND saf.isRequired = 1
END

if @daysToCompleteEvaluation > 0 BEGIN
	insert into @tmpTable
	select saf.loadPoint, e.enrollmentid, e.dateCompleted, r.datecompleted as formCompleteDate, s.dateEnd + @daysToCompleteEvaluation as completeByDate
	FROM dbo.tblEnrollments AS e 
	INNER JOIN dbo.tblSeminarsAndForms AS saf ON e.seminarID = saf.seminarID
	INNER JOIN dbo.tblSeminarsSWLive s on s.seminarID = e.seminarID

	LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
		INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
		INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
			and f.isPublished = 1
			and getdate() between f.dateStartPublish and f.dateEndPublish	
		on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e.enrollmentID
	WHERE e.enrollmentID = @enrollmentID
	AND saf.loadPoint = 'evaluation'
	AND saf.isRequired = 1
END

select * from @tmpTable
where formCompleteDate > completeByDate
GO

ALTER PROC [dbo].[swl_getSeminarProgress]
@enrollmentID int

AS

-- qry1: seminar settings
SELECT swl.isOpen
from dbo.tblEnrollments as e
inner join dbo.tblSeminars as s on s.seminarID = e.seminarID
inner join dbo.tblSeminarsSWLive as swl on swl.seminarID = s.seminarID
where e.enrollmentID = @enrollmentID

-- qry2: data used to determine progress
DECLARE @allPreTestCompleted bit, @allPostTestCompleted bit, @allEvaluationCompleted bit, @hasCertificate bit, @showSurvey bit, @allowAutoSendOfCertificate bit, @CreditCount int

SELECT @CreditCount = COUNT(*) FROM dbo.tblEnrollmentsAndCredit WHERE enrollmentID = @enrollmentID

SELECT @allPreTestCompleted = 
	CASE 
	WHEN (select count(saf.formID)
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblSeminarsAndForms AS saf ON e.seminarID = saf.seminarID
			LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
				INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
				INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
					and f.isPublished = 1
					and getdate() between f.dateStartPublish and f.dateEndPublish	
					and r.passingPct >= f.passingPct				
				on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e.enrollmentID
			WHERE e.enrollmentID = @enrollmentID
			AND saf.loadPoint = 'preTest'
			AND saf.isRequired = 1
			AND safr.responseID is null) > 0 THEN 0
	ELSE 1
	END
SELECT @allPostTestCompleted = 
	CASE 
	WHEN (select count(saf.formID)
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblSeminarsAndForms AS saf ON e.seminarID = saf.seminarID
			LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
				INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
				INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
					and f.isPublished = 1
					and getdate() between f.dateStartPublish and f.dateEndPublish	
					and r.passingPct >= f.passingPct				
				on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e.enrollmentID
			WHERE e.enrollmentID = @enrollmentID
			AND saf.loadPoint = 'postTest'
			AND saf.isRequired = 1
			AND safr.responseID is null) > 0 THEN 0
	ELSE 1
	END
SELECT @showSurvey = 
	CASE 
	WHEN (select count(saf.formID)
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID
			INNER JOIN dbo.tblSeminarsAndForms AS saf ON e.seminarID = saf.seminarID
			LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
				INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
				INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
					and f.isPublished = 1
					and getdate() between f.dateStartPublish and f.dateEndPublish	
				on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e.enrollmentID
			WHERE e.enrollmentID = @enrollmentID
			AND saf.loadPoint = 'evaluation'
			AND eswl.attended = 1
			AND (safr.responseID is null)) > 0 THEN 1
	ELSE 0
	END
SELECT @allEvaluationCompleted = 
	CASE 
	WHEN (select count(saf.formID)
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblSeminarsAndForms AS saf ON e.seminarID = saf.seminarID
			LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
				INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
				INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
					and f.isPublished = 1
					and getdate() between f.dateStartPublish and f.dateEndPublish	
				on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e.enrollmentID
			WHERE e.enrollmentID = @enrollmentID
			AND saf.loadPoint = 'evaluation'
			AND (safr.responseID is null or r.dateCompleted is null)) > 0 THEN 0
	ELSE 1
	END
SELECT @hasCertificate = 
	CASE 
	WHEN (SELECT count(e.enrollmentID)
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
			INNER JOIN dbo.tblSeminars AS s ON e.seminarID = s.seminarID 
			INNER JOIN dbo.tblSeminarsSWLive AS sswl ON s.seminarID = sswl.seminarID
			WHERE e.enrollmentID = @enrollmentID 
			AND s.isDeleted = 0
			AND e.isActive = 1
			AND eswl.attended = 1
			AND s.offerCertificate = 1
			AND e.passed = 1
			AND LEN(e.dateCompleted) > 0) > 0 THEN 1
	ELSE 0
	END
SELECT @allowAutoSendOfCertificate = 
	CASE 
	WHEN (select count(*) from tblLogSWLive where enrollmentID = @enrollmentID and typeID = 37) > 0 THEN 0
	WHEN (SELECT count(sac.seminarCreditID)
				FROM dbo.tblEnrollmentsAndCredit AS eac 
				INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
				INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID 
				INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
				INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID 
				INNER JOIN dbo.tblCreditStatuses AS cstat ON sac.statusID = cstat.statusID
				INNER JOIN dbo.tblCreditAuthoritiesSWLive as caswl on caswl.authorityID = ca.authorityID
				WHERE eac.enrollmentID = @enrollmentID
				and evaluationRequired = 1) > 0 THEN 1
	WHEN @CreditCount = 0 THEN 1
	WHEN (SELECT count(sac.seminarCreditID)
				FROM dbo.tblEnrollmentsAndCredit AS eac 
				INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
				INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID 
				INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
				INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID 
				INNER JOIN dbo.tblCreditStatuses AS cstat ON sac.statusID = cstat.statusID
				INNER JOIN dbo.tblCreditAuthoritiesSWLive as caswl on caswl.authorityID = ca.authorityID
				WHERE eac.enrollmentID = @enrollmentID
				and evaluationRequired = 0) > 0 THEN 1
	ELSE 0
	END
SELECT @allPreTestCompleted as allPreTestCompleted, 
	@allPostTestCompleted as allPostTestCompleted,
	@allEvaluationCompleted as allEvaluationCompleted,
	@hasCertificate as hasCertificate,
	@showSurvey as showSurvey,
	@allowAutoSendOfCertificate as allowAutoSendOfCertificate
GO

ALTER PROC [dbo].[swl_updateWebAttendance]
@enrollmentID int,
@attended bit,
@jointime varchar(20),
@exittime varchar(20),
@duration int,
@completedPolling bit

AS

-- if not a webinar, dont update attendance
DECLARE @swlTypeID int
SELECT @swlTypeID = sswl.swlTypeID
	FROM dbo.tblEnrollments AS e 
	INNER JOIN dbo.tblSeminarsSWLive AS sswl ON e.seminarID = sswl.seminarID
	WHERE e.enrollmentID = @enrollmentID
IF @swlTypeID <> 1
	RETURN 1

BEGIN TRAN

-- update 1: update swl
UPDATE dbo.tblEnrollmentsSWLive
SET attended = @attended,
	joinTime = @jointime,
	exitTime = @exitTime,
	duration = @duration,
	completedPolling = @completedPolling
WHERE enrollmentID = @enrollmentID
	IF (@@ERROR <> 0) GOTO on_error

-- if did not attend
IF @attended = 0
	BEGIN
		-- clear the dateCompleted field
		UPDATE dbo.tblEnrollments
		SET dateCompleted = null, passed = 0
		WHERE enrollmentID = @enrollmentID
			IF (@@ERROR <> 0) GOTO on_error

		-- update credits by removing timespent and earnedCert
		UPDATE dbo.tblEnrollmentsAndCredit
		SET earnedCertificate = 0, 
			finalTimeSpent = NULL
		WHERE enrollmentID = @enrollmentID
			IF (@@ERROR <> 0) GOTO on_error
	END

-- if attended 
ELSE
	BEGIN
		DECLARE @dateEnd datetime
		SELECT @dateEnd = sswl.dateEnd
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblSeminarsSWLive AS sswl ON e.seminarID = sswl.seminarID
			WHERE e.enrollmentID = @enrollmentID
			IF (@@ERROR <> 0) GOTO on_error

		-- mark date completed as the end date of the seminar	
/*
		UPDATE dbo.tblEnrollments
		SET dateCompleted = @dateEnd
		WHERE enrollmentID = @enrollmentID
			IF (@@ERROR <> 0) GOTO on_error
*/
		-- update enrollments and mark passed if also attended phone portion (if PIN numbers used)
/*
		DECLARE @usePIN bit
		SELECT @usePIN = sswl.premiereUsePIN
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblSeminarsSWLive AS sswl ON e.seminarID = sswl.seminarID
			WHERE e.enrollmentID = @enrollmentID
			IF (@@ERROR <> 0) GOTO on_error
		UPDATE dbo.tblEnrollments
		SET passed = 1
		WHERE enrollmentID = @enrollmentID
		AND (
			@usePIN = 0
			OR 
			(enrollmentID IN (SELECT enrollmentID from dbo.tblEnrollmentsSWLive where enrollmentID = @enrollmentID and attendedPhone = 1)
			AND @usePIN = 1)
			)
			IF (@@ERROR <> 0) GOTO on_error
*/
		-- update credits with final time spent
		UPDATE dbo.tblEnrollmentsAndCredit
		SET earnedCertificate = 0, 
			finalTimeSpent = @duration
		WHERE enrollmentID = @enrollmentID
			IF (@@ERROR <> 0) GOTO on_error

		-- if credit is based on polling, dont earn certificate yet
		UPDATE dbo.tblEnrollmentsAndCredit
		SET earnedCertificate = 1
		WHERE enrollmentID = @enrollmentID
		AND enrollCreditID IN (
			SELECT enrollCreditID
			FROM dbo.tblEnrollmentsAndCredit as eac
			WHERE eac.enrollmentID = @enrollmentID
			AND dbo.fn_isPollingRequiredBasedOnSCID(eac.seminarCreditID) = 0
		)
			IF (@@ERROR <> 0) GOTO on_error
		
		IF @completedPolling = 1
		BEGIN
			UPDATE dbo.tblEnrollmentsAndCredit
			SET earnedCertificate = 1
			WHERE enrollmentID = @enrollmentID
			AND enrollCreditID IN (
				SELECT enrollCreditID
				FROM dbo.tblEnrollmentsAndCredit as eac
				WHERE eac.enrollmentID = @enrollmentID
				AND dbo.fn_isPollingRequiredBasedOnSCID(eac.seminarCreditID) = 1
			)
				IF (@@ERROR <> 0) GOTO on_error
		END

	-- call completion check
	EXEC dbo.swl_completeWebinar @enrollmentID

	END

COMMIT TRAN
RETURN 1

on_error:
ROLLBACK TRAN
RETURN 0
GO