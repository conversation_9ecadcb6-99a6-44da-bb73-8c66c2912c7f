USE [seminarWeb]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROCEDURE [dbo].[swl_getSeminarForCatalog]
@seminarID int,
@billingstate varchar(5),
@catalogOrgCode varchar(5),
@depomemberdataid int,
@depomemberSiteGroups varchar(max)

AS

-- use 1-1-2005 and 1-1-2015 as dates so we can still support viewing details of past programs.
-- qry1: get seminar
select top 1 s.seminarID, s.seminarName, s.seminarDesc, s.seminarDescText, s.isPublished, s.offerCertificate, s.allowregistrants,
	CASE 
	WHEN @depomemberdataID > 0 AND dbo.fn_getEnrollmentIDFromDepoMemberDataID(s.seminarID,@depoMemberdataid) > 0 THEN 1
	ELSE 0
	END as isRegistered,
	dbo.fn_getEnrollmentIDFromDepoMemberDataID(s.seminarID,@depoMemberdataid) as enrollmentID,
	swl.gotomeetingID, swl.agenda, swl.dateStart, swl.dateEnd, swl.wddxTimeZones, swl.programPassword, 
	swl.phoneAdmin, swl.phoneAttendee, swl.codeAdmin, swl.codeAttendee, swl.surveyLink, swl.offerCredit, 
	swl.offerDVD, swl.sendCountToPremiere, swl.premiereConfID, swl.premiereUsePIN, swl.lineprovider, p.orgcode as publisherOrgCode, 
	swl.swltypeid, tla.description,
	(select count(*) from dbo.tblSeminarsAndGroups where seminarID = @seminarID) as numPriceOptions,
	s.isPriceBasedOnActual
from dbo.tblSeminars s
inner join dbo.tblSeminarsSWLive swl on swl.seminarID = s.seminarID
INNER JOIN dbo.tblParticipants as p on p.participantID = s.participantID
INNER JOIN trialsmith.dbo.depoTLA as tla on tla.State = p.OrgCode 
INNER JOIN dbo.swl_SeminarsInMyCatalog(@catalogOrgCode,'1-1-2005',DATEADD(yy, 2, getDate())) smc on smc.seminarID = swl.seminarID
where s.seminarID = @seminarID
AND s.isDeleted = 0

-- qry2: get prices for detail page
EXEC swl_getPricesForCatalog @seminarID, @catalogOrgCode, @billingstate

-- qry3: get prices for buy now pages
EXEC swl_getPricesForBuyNow @seminarID, @catalogOrgCode, @billingstate, @depomemberdataid, @depomemberSiteGroups

