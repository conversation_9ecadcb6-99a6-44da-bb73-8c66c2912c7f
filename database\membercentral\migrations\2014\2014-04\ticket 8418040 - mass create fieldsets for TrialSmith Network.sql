

declare 
	@nameFormat varchar(10), 
	@showHelp int, 
	@fieldsetname varchar(200),
	@displayType_textbox int,
	@displayType_selectbox int,
	@dataType_textstring int,
	@testMode bit

set @testMode = 1

declare @siteList TABLE (siteID int PRIMARY KEY)
declare @orgList TABLE (orgID int PRIMARY KEY)


insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('AK'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('AR'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('AZ'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('CA'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('CO'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('DC'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('DE'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('GA'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('IA'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('ID'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('IN'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('KS'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('KY'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('LA'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('LO'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('MD'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('MI'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('MO'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('NC'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('ND'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('NE'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('NH'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('NJ'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('NM'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('NV'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('OH'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('OK'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('OR'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('PA'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('SC'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('SF'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('TX'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('UT'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('VA'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('VT'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('WI'))
insert into @siteList (siteID) values (dbo.fn_getSiteIDFromSiteCode('WY'))


/*
insert into @siteList (siteID)
select s.siteID
from sites s
inner join networkSites ns
	on ns.siteID = s.siteID
	and ns.isLoginNetwork = 1
inner join networks n
	on n.networkID = ns.networkID
	and n.networkName = 'TrialSmith'
*/

insert into @orgList (orgID)
select distinct s.orgID
from sites s
inner join @siteList sl
	on sl.siteID = s.siteID

/* *******************************

Mass create fieldsets

******************************* */

select @nameFormat = 'PFMLSX', @showHelp = 0, @fieldsetname = 'TrialSmith Sponsorship Export'
select @displayType_textbox = displayTypeID from dbo.ams_memberDataColumnDisplayTypes where displayTypeCode = 'TEXTBOX'
select @displayType_selectbox = displayTypeID from dbo.ams_memberDataColumnDisplayTypes where displayTypeCode = 'SELECT'
select @dataType_textstring = dataTypeID from dbo.ams_memberDataColumnDataTypes where dataTypeCode = 'STRING'

declare @nameParts table (autoID int identity(1,1), fieldLabel varchar(50), columnName varchar(20), displayTypeID int, datatypeid int, fieldorder int)
insert into @nameParts (fieldLabel, columnName, displayTypeID, datatypeID,fieldorder) values ('Prefix', 'prefix', @displayType_textbox, @dataType_textstring,1)
insert into @nameParts (fieldLabel, columnName, displayTypeID, datatypeID,fieldorder) values ('First Name', 'firstname', @displayType_textbox, @dataType_textstring,2)
insert into @nameParts (fieldLabel, columnName, displayTypeID, datatypeID,fieldorder) values ('Middle Name', 'middlename', @displayType_textbox, @dataType_textstring,3)
insert into @nameParts (fieldLabel, columnName, displayTypeID, datatypeID,fieldorder) values ('Last Name', 'lastname', @displayType_textbox, @dataType_textstring,4)
insert into @nameParts (fieldLabel, columnName, displayTypeID, datatypeID,fieldorder) values ('Suffix', 'suffix', @displayType_textbox, @dataType_textstring,5)
insert into @nameParts (fieldLabel, columnName, displayTypeID, datatypeID,fieldorder) values ('Professional Suffix', 'professionalsuffix', @displayType_selectbox, @dataType_textstring,6)
insert into @nameParts (fieldLabel, columnName, displayTypeID, datatypeID,fieldorder) values ('Company', 'company', @displayType_textbox, @dataType_textstring,7)


declare @addressParts table (autoID int identity(1,1), fieldLabel varchar(50), columnSuffix varchar(20), displayTypeID int, datatypeid int, fieldorder int)
insert into @addressParts (fieldLabel, columnSuffix, displayTypeID, datatypeID,fieldorder) values ('Attn', 'attn', @displayType_textbox, @dataType_textstring,8)
insert into @addressParts (fieldLabel, columnSuffix, displayTypeID, datatypeID,fieldorder) values ('Address 1', 'address1', @displayType_textbox, @dataType_textstring,9)
insert into @addressParts (fieldLabel, columnSuffix, displayTypeID, datatypeID,fieldorder) values ('Address 2', 'address2', @displayType_textbox, @dataType_textstring,10)
insert into @addressParts (fieldLabel, columnSuffix, displayTypeID, datatypeID,fieldorder) values ('Address 3', 'address3', @displayType_textbox, @dataType_textstring,11)
insert into @addressParts (fieldLabel, columnSuffix, displayTypeID, datatypeID,fieldorder) values ('City', 'city', @displayType_textbox, @dataType_textstring,12)
insert into @addressParts (fieldLabel, columnSuffix, displayTypeID, datatypeID,fieldorder) values ('State', 'stateprov', @displayType_selectbox, @dataType_textstring,13)
insert into @addressParts (fieldLabel, columnSuffix, displayTypeID, datatypeID,fieldorder) values ('Zip', 'postalCode', @displayType_textbox, @dataType_textstring,14)
insert into @addressParts (fieldLabel, columnSuffix, displayTypeID, datatypeID,fieldorder) values ('Country', 'country', @displayType_selectbox, @dataType_textstring,15)

declare @tmpFieldSets TABLE (autoID int IDENTITY(1,1), siteID int, fieldsetName varchar(200), nameformat varchar(10), showHelp int, uid uniqueIdentifier)
declare @tmpFields TABLE(
	autoID [int] IDENTITY(1,1) NOT NULL,
	[fieldsetUID] [uniqueidentifier] NOT NULL,
	[dbObject] [varchar](40) NOT NULL,
	[dbObjectAlias] [varchar](50) NOT NULL,
	[dbField] [varchar](300) NOT NULL,
	[fieldCode] [varchar](30) NOT NULL,
	[fieldLabel] [varchar](420) NOT NULL,
	[displayTypeID] [int] NOT NULL,
	[dataTypeID] [int] NOT NULL,
	[isRequired] [bit] NOT NULL ,
	[fieldOrder] [smallint] NOT NULL,
	[fieldDescription] [varchar](300) NOT NULL
)

INSERT INTO @tmpFieldSets (siteID, fieldsetName, nameformat, showHelp, [uid])
select s.siteID, @fieldsetname, @nameFormat, @showHelp,  newid()
from @siteList s

insert into @tmpFields (fieldsetUID, dbObject, dbObjectAlias, dbField, fieldCode, fieldLabel, displayTypeID, dataTypeID, isRequired, fieldOrder, fieldDescription)
select 
	fieldsetUID = fs.uid, 
	dbObject = 'vw_memberData_' + o.orgcode, 
	dbObjectAlias = 'md',
	dbfield = mat.addressType + '_' + ap.columnSuffix,
	fieldcode = 'ma_' + cast(mat.addressTypeID as varchar(10)) + '_' + ap.columnSuffix,
	ap.fieldLabel,
	ap.displayTypeID,
	ap.dataTypeID,
	isRequired = 0,
	ap.fieldOrder,
	fieldDescription = ''

from @tmpFieldSets fs
inner join sites s
	on s.siteID = fs.siteID
inner join organizations o
	on s.orgID = o.orgID
inner join ams_memberAddressTypes mat
	on mat.orgID = o.orgID
	and mat.addressTypeOrder = 1
cross join @addressParts ap



insert into @tmpFields (fieldsetUID, dbObject, dbObjectAlias, dbField, fieldCode, fieldLabel, displayTypeID, dataTypeID, isRequired, fieldOrder, fieldDescription)
select 
	fieldsetUID = fs.uid, 
	dbObject = 'ams_members', 
	dbObjectAlias = 'm',
	dbfield = np.columnname,
	fieldcode = 'm_' + np.columnname,
	np.fieldLabel,
	displayTypeID = case when np.columnname = 'prefix' and o.usePrefixList = 1 then @displayType_selectbox else np.displayTypeID end,
	np.dataTypeID,
	isRequired = 0,
	np.fieldOrder,
	fieldDescription = ''

from @tmpFieldSets fs
inner join sites s
	on s.siteID = fs.siteID
inner join organizations o
	on s.orgID = o.orgID
cross join @nameParts np

insert into @tmpFields (fieldsetUID, dbObject, dbObjectAlias, dbField, fieldCode, fieldLabel, displayTypeID, dataTypeID, isRequired, fieldOrder, fieldDescription)
select 
	fieldsetUID = fs.uid, 
	dbObject = 'vw_memberData_' + o.orgcode, 
	dbObjectAlias = 'md',
	dbfield = met.emailType,
	fieldcode = 'me_' + cast(met.emailTypeID as varchar(10)) + '_email',
	fieldLabel = 'Email',
	displayTypeID=@displayType_textbox,
	dataTypeID=@dataType_textstring,
	isRequired = 0,
	fieldOrder=16,
	fieldDescription = ''

from @tmpFieldSets fs
inner join sites s
	on s.siteID = fs.siteID
inner join organizations o
	on s.orgID = o.orgID
inner join ams_memberEmailTypes met
	on met.orgID = o.orgID
	and met.emailTypeOrder = 1




if @testMode = 1 BEGIN
	select * from @tmpFieldSets
	select * from @tmpFields order by fieldsetuid, fieldorder
END
if @testMode = 0 BEGIN
	insert into dbo.ams_memberFieldSets (siteID, fieldsetName, nameFormat, showHelp, uid)
	select siteID, fieldsetName, nameFormat, showHelp, uid
	from @tmpFieldSets

	insert into dbo.ams_memberFields (fieldsetID, dbObject, dbObjectAlias, dbField, fieldCode, fieldLabel, displayTypeID, dataTypeID, isRequired, fieldOrder, fieldDescription, uid)
	select fs.fieldsetID, tf.dbObject, tf.dbObjectAlias, tf.dbField, tf.fieldCode, tf.fieldLabel, tf.displayTypeID, tf.dataTypeID, tf.isRequired, tf.fieldOrder, tf.fieldDescription, newid()
	from @tmpFields tf
	inner join dbo.ams_memberFieldSets fs
		on fs.uid = tf.fieldsetUID

END

/* *******************************

Mass create groups

******************************* */
/*
declare
	@createGroup_orgID int,
	@createGroup_groupCode varchar(30),
	@createGroup_groupName varchar(115),
	@createGroup_groupDesc varchar(200),
	@createGroup_isSystemGroup bit,
	@createGroup_allowManualAssignment bit,
	@createGroup_parentGroupID int,
	@createGroup_hideOnGroupLists bit,
	@createGroup_groupID int

select
	@createGroup_groupCode = 'TSExportOK',
	@createGroup_groupName = 'TrialSmith Approved Marketing List',
	@createGroup_groupDesc = 'Members who are exported for TrialSmith''s marketing program',
	@createGroup_isSystemGroup = 0,
	@createGroup_allowManualAssignment = 0,
	@createGroup_parentGroupID = null,
	@createGroup_hideOnGroupLists = 0


select @createGroup_orgID = min(orgID) from @orglist
while @createGroup_orgID is not null
BEGIN

	select @createGroup_groupID = null

	if @testMode = 1 BEGIN
		print 'Create Group for orgID: ' + cast(@createGroup_orgID as varchar(10))
	END
	if @testMode = 0 BEGIN
		exec [dbo].[ams_createGroup]
			@orgID=@createGroup_orgID ,
			@groupCode=@createGroup_groupCode ,
			@groupName=@createGroup_groupName ,
			@groupDesc=@createGroup_groupDesc, 
			@isSystemGroup=@createGroup_isSystemGroup,
			@allowManualAssignment=@createGroup_allowManualAssignment,
			@parentGroupID=@createGroup_parentGroupID,
			@hideOnGroupLists=@createGroup_hideOnGroupLists,
			@groupID=@createGroup_groupID  OUTPUT
	END

	select @createGroup_orgID = min(orgID) from @orglist where orgID > @createGroup_orgID
END
*/

/*

Working copy of Member Export query --- do not delete this

select
	m.memberID,
	o.orgcode,
	m.memberNumber,
	orgcode_Membernumber = o.orgcode + '_' + m.memberNumber,
	prefix = case when o.hasprefix = 1 then m.prefix else null end,
	m.firstname,
	middlename = case when o.hasMiddleName = 1 then m.middlename else null end,
	m.lastname,
	suffix = case when o.hassuffix = 1 then m.suffix else null end,
	professionalSuffix = case when o.hasprofessionalSuffix = 1 then m.professionalSuffix else null end,
	attn = case when mat.hasAttn = 1 then ma.attn else null end,
	ma.address1,
	address2 = case when mat.hasaddress2 = 1 then ma.address2 else null end,
	address3 = case when mat.hasaddress3 = 1 then ma.address3 else null end,
	ma.city,
	st.name as stateprov,
	ma.postalcode,
	c.country,
	me.email
from ams_groups g
inner join cache_members_groups mg
	on mg.groupID = g.groupID
	and g.groupCode = 'TSExportOK'
inner join ams_members m
	on m.memberID = mg.memberID
	and m.memberID = m.activeMemberID
	and m.status = 'A'
inner join organizations o
	on o.orgID = g.orgID
inner join tlasites.trialsmith.dbo.depotla tla
	on o.orgcode = tla.state
	and tla.isLiveOnNewPlatform = 1
	and tla.state <> 'ts'
left outer join ams_memberAddresses ma
	inner join ams_memberAddressTypes mat
		on mat.addressTypeOrder = 1
		and mat.addressTypeID = ma.addressTypeID
	left outer join dbo.ams_states st
		inner join ams_countries c
			on c.countryID = st.countryID
	on st.stateID = ma.stateID
on ma.memberID = m.memberID
left outer join ams_memberEmails me
	inner join ams_memberEmailTypes met
		on met.emailTypeOrder = 1
		and met.emailTypeID = me.emailTypeID
on me.memberID = m.memberID
*/