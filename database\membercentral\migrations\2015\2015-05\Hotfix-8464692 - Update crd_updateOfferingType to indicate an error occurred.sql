use membercentral
GO

ALTER PROC [dbo].[crd_updateOfferingType]
@offeringID int,
@ASTID int,
@creditValue decimal(6,2),
@updated int OUTPUT

AS

select @updated = 1
-- if 0 and noon<PERSON> has signed up for it, delete it
IF @creditValue = 0 
BEGIN
	IF NOT EXISTS (
		select r.requestID
		from dbo.crd_requests as r
		inner join dbo.crd_offeringTypes as ot on ot.offeringTypeID = r.offeringTypeID
		where ot.offeringID = @offeringID
		and ot.ASTID = @ASTID
	)
		DELETE FROM dbo.crd_offeringTypes
		WHERE offeringID = @offeringID
		AND ASTID = @ASTID
	ELSE
		select @updated = 0
END
ELSE 
BEGIN
	IF EXISTS (select offeringTypeID from dbo.crd_offeringTypes where offeringID = @offeringID and ASTID = @ASTID)
		UPDATE dbo.crd_offeringTypes
		SET creditValue = @creditValue
		WHERE offeringID = @offeringID
		AND ASTID = @ASTID

	-- else add it	
	ELSE
		INSERT INTO dbo.crd_offeringTypes (offeringID, ASTID, creditValue)
		VALUES (@offeringID, @ASTID, @creditValue)

END

RETURN 0
GO
