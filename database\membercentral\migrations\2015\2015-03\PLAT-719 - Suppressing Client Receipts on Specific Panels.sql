use MemberCentral
GO

if not exists(select * from sys.columns 
            where Name = N'sendReceiptEmail' and Object_ID = Object_ID(N'ref_panels'))
begin
	alter table ref_panels
		add sendReceiptEmail bit default (0)
end
GO

update 
	ref_panels
set
	sendReceiptEmail = 1
where
	panel<PERSON>arent<PERSON> is null

GO

update 
	ref_panels
set
	sendReceiptEmail = 0
where
	panelParentID is null
	and name in(	'Workers'' Compensation', 
					'Personal Injury - Defendant', 
					'Personal Injury - Plaintiff', 
					'Small Claims', 
					'Legal Malpractice - G', 
					'Medical Malpractice', 
					'Maritime - G', 
					'Elder Law - Physical Abuse',
					'DMV/Traffic',
					'Servicemembers Civil Relief Act',
					'Family Law - Modest Means',
					'SSI')
GO

ALTER PROCEDURE [dbo].[ref_createPanel]
	@uid	varchar(50),
	@referralID	int,
	@name	varchar(255),
	@shortDesc	varchar(255) = NULL,
	@longDesc	varchar(max) = NULL,
	@statusID	int,
	@internalNotes	varchar(max) = NULL,
	@dateCreated	datetime = NULL,
	@dateCommitteeApproved	datetime = NULL,
	@dateBoardApproved	datetime = NULL,
	@dateBoardNotified	datetime = NULL,
	@dateReviewed	datetime = NULL,
	@surveyUrl	varchar(255) = NULL,
	@sendMail	bit = NULL,
	@maxNumMembers	int = NULL,
	@referralFeePercent	decimal(4,2) = NULL,
	@deductExpenseDesc	varchar(max) = NULL,
	@referralAmount	money = NULL,
	@panelParentID	int = NULL,
	@GLAccountID	int = NULL,
	@clientFeeGLAccountID int = NULL,
	@clientReferralAmount money = NULL,
	@sendReceiptEmail	bit = NULL,
	@isActive	bit = NULL,	
	@panelID int OUTPUT
AS

declare
	@rc int,
	@resourceTypeID int,
	@siteID int,
	@parentSiteResourceID int,
	@siteResourceID int

IF ( EXISTS (select panelID FROM dbo.ref_panels where name = @name and referralID = @referralID and statusID = 1))
	GOTO on_error
ELSE begin

	set @parentSiteResourceID = NULL

	select @resourceTypeID = dbo.fn_getResourceTypeID('ReferralPanel')

	select 
		@siteID = ai.siteID
	from 
		cms_applicationInstances ai
		inner join ref_referrals r on 
			r.applicationInstanceID = ai.applicationInstanceID
			and r.referralID = @referralID

	IF @panelParentID is not null
	begin
		select @parentSiteResourceID = siteResourceID from ref_panels where panelid = @panelParentID 
	end

	BEGIN TRAN

		exec @rc = cms_createSiteResource
				@resourceTypeID = @resourceTypeID,
				@siteResourceStatusID = 1,
				@siteID = @siteID,
				@isVisible = 1,
				@parentSiteResourceID = @parentSiteResourceID,
				@siteResourceID = @siteResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

		insert into dbo.ref_panels (
			[uid],
			referralID,
			name,
			shortDesc,
			longDesc,
			statusID,
			internalNotes,
			dateCreated,
			dateCommitteeApproved,
			dateBoardApproved,
			dateBoardNotified,
			dateReviewed,
			surveyUrl,
			sendMail,
			maxNumMembers,
			referralFeePercent,
			deductExpenseDesc,
			referralAmount,
			panelParentID,
			GLAccountID,
			isActive,
			siteResourceID,
			clientFeeGLAccountID,
			clientReferralAmount,
			sendReceiptEmail
		)
		values (
			@uid,
			@referralID,
			@name,
			@shortDesc,
			@longDesc,
			@statusID,
			@internalNotes,
			@dateCreated,
			@dateCommitteeApproved,
			@dateBoardApproved,
			@dateBoardNotified,
			@dateReviewed,
			@surveyUrl,
			@sendMail,
			@maxNumMembers,
			@referralFeePercent,
			@deductExpenseDesc,
			@referralAmount,
			@panelParentID,
			@GLAccountID,
			@isActive,
			@siteResourceID,
			@clientFeeGLAccountID,
			@clientReferralAmount,
			@sendReceiptEmail
		)

		IF @@ERROR <> 0 GOTO on_error
		SELECT @panelID = SCOPE_IDENTITY()	

		IF @@TRANCOUNT > 0 COMMIT TRAN
		GOTO on_success
	
end

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @panelID = 0
	RETURN -1

on_success:
	RETURN 0
GO



