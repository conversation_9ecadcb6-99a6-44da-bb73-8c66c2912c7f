USE [platformstats]
GO
CREATE TABLE [dbo].[sub_renewQueueBatch](
	[queueID] [int] IDENTITY(1,1) NOT NULL,
	[batchID] [uniqueidentifier] NOT NULL,
	[batchCreatedDate] [datetime] NOT NULL,
	[rescindDate] [datetime] NULL,
	[overrideStartDate] [datetime] NULL,
	[siteID] [int] NOT NULL,
	[orgID] [int] NOT NULL,
	[actorMemberID] [int] NOT NULL,
	[actorEmail] [varchar](200) NULL, 
 CONSTRAINT [PK_sub_renewQueueBatch] PRIMARY KEY CLUSTERED 
(
	[queueID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE TABLE [dbo].[sub_renewQueue](
	[autoid] [int] IDENTITY(1,1) NOT NULL,
	[queueID] [int] NOT NULL,
	[subscriberID] [int] NOT NULL,
	[processedDate] [datetime] NULL,
	[success] [bit] NULL,
	[wddxMessages] [xml] NULL,
	[processQueueUID] [uniqueidentifier] NULL,
) ON [PRIMARY]
GO
ALTER TABLE dbo.sub_renewQueue ADD CONSTRAINT
	FK_sub_renewQueue_sub_renewQueueBatch FOREIGN KEY
	(
	queueID
	) REFERENCES dbo.sub_renewQueueBatch
	(
	queueID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
GO

CREATE PROC dbo.sub_getQueueToProcess
@batchSize int,
@restrictToBatchID uniqueIdentifier

AS

declare @workerUUID uniqueIdentifier
set @workerUUID = newID()

declare @tblQueue table(
	autoid int NOT NULL,
	queueid int NOT NULL,
	subscriberID int NOT NULL
	);

-- mark queue entries
update r 
set r.processQueueUID = @workerUUID
output	inserted.autoid,
		inserted.queueID,
		inserted.subscriberID
into @tblQueue
from dbo.sub_renewQueue as r
inner join (
	select TOP (@batchSize) rq.autoid
	from dbo.sub_renewQueueBatch as rqb
	inner join dbo.sub_renewQueue as rq on rq.queueID = rqb.queueID
	where rq.processedDate is null
	and rq.processQueueUID is null
	and rqb.batchID = isnull(@restrictToBatchID,rqb.batchID)
	order by rq.autoid
) as temp on temp.autoid = r.autoid

-- return queue entries
select rq.autoid, rq.subscriberID, rqb.batchID, rqb.rescindDate, rqb.overrideStartDate, rqb.siteid, rqb.orgID, rqb.actorMemberID, rqb.actorEmail
from @tblQueue as rq
inner join dbo.sub_renewQueueBatch as rqb on rqb.queueID = rq.queueID
order by rq.autoid

RETURN 0
GO



USE membercentral
GO
insert into dbo.scheduledTasks (name, nextRunDate, interval, intervalTypeID, taskCFC, timeoutMinutes, disabled, siteid)
values ('Process Subscription Renewal Queue', '9/25/2014', 1, 3, 'model.scheduledTasks.tasks.processSubRenewalQueue', 10, 1, 1)
GO

USE [platformstats]
GO
ALTER PROC [dbo].[sub_getQueueToProcess]
@batchSize int,
@restrictToBatchID uniqueIdentifier

AS

declare @workerUUID uniqueIdentifier
set @workerUUID = newID()

declare @tblQueue table(
	autoid int NOT NULL,
	queueid int NOT NULL,
	subscriberID int NOT NULL
	);

-- mark queue entries
update r 
set r.processQueueUID = @workerUUID
output	inserted.autoid,
		inserted.queueID,
		inserted.subscriberID
into @tblQueue
from dbo.sub_renewQueue as r
inner join (
	select TOP (@batchSize) rq.autoid
	from dbo.sub_renewQueueBatch as rqb
	inner join dbo.sub_renewQueue as rq on rq.queueID = rqb.queueID
	where rq.processedDate is null
	and rq.processQueueUID is null
	and rqb.batchID = isnull(@restrictToBatchID,rqb.batchID)
	order by rq.autoid
) as temp on temp.autoid = r.autoid
where r.processQueueUID is null

-- return queue entries
select rq.autoid, rq.subscriberID, rqb.batchID, rqb.rescindDate, rqb.overrideStartDate, rqb.siteid, rqb.orgID, rqb.actorMemberID, rqb.actorEmail
from @tblQueue as rq
inner join dbo.sub_renewQueueBatch as rqb on rqb.queueID = rq.queueID
order by rq.autoid

RETURN 0
GO

