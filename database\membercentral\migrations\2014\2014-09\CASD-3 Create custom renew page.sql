
-- add custom page
declare @siteID int, @sectionID int, @zoneID int, @pgResourceTypeID int, @applicationInstanceID int, @siteResourceID int, 
	@pageID int, @resourceRightID int
		      
select @siteID = siteID from sites where siteCode = 'SA'
select @sectionID = sectionID from cms_pageSections where siteID = @siteID and sectionName = 'Membership' and sectionCode = 'Membership'
SELECT @zoneID = dbo.fn_getZoneID('Main')
SELECT @pgResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedPage')


EXEC dbo.cms_createApplicationInstance @siteid=@siteID, @languageID=1, @sectionID=@sectionID, @applicationTypeID=18,
	@isVisible=1, @pageName='Renew', @pageTitle='Renew Membership', @pagedesc='Renew Membership', @zoneID=@zoneID, @pageTemplateID=null,
	@pageModeID=null, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1,
	@applicationInstanceName='Renew Membership', @applicationInstanceDesc='Renew Membership', @applicationInstanceID=@applicationInstanceID OUTPUT,
	@siteResourceID=@siteResourceID OUTPUT, @pageID=@pageID OUTPUT
								
INSERT INTO cms_customPages(appInstanceID, customFileName)
VALUES(@applicationInstanceID,'Renew')

-- Apply permissions.  Currenlty, the new group is not defined in production.
GO
