CREATE PROC dbo.tr_importAuthorizeCIM
@orgID int,
@importFile varchar(400),
@importResult xml OUTPUT

AS

set nocount on

DECLARE @qry varchar(400)
SET @importResult = null

IF OBJECT_ID('tempdb..#tblAccCIMErrors') IS NOT NULL 
	DROP TABLE #tblAccCIMErrors
CREATE TABLE #tblAccCIMErrors (rowid int IDENTITY(1,1), msg varchar(max))

-- this should only run if the table does not exist
IF OBJECT_ID('datatransfer.dbo.tmp_AuthCIMCards') IS NOT NULL BEGIN
	INSERT INTO #tblAccCIMErrors (msg)
	VALUES ('Unable to run import. Another process may already be running.')

	GOTO on_done
END

/* *********** */
/* import data */
/* *********** */
IF OBJECT_ID('tempdb..##tmpAccCIMImport') IS NOT NULL 
	DROP TABLE ##tmpAccCIMImport
IF OBJECT_ID('tempdb..#tmpAccCIMImport2') IS NOT NULL 
	DROP TABLE #tmpAccCIMImport2
CREATE TABLE ##tmpAccCIMImport (MemberNumber varchar(50) NULL, CardNumber varchar(20) NULL, 
	ExpirationMonth varchar(20) NULL, ExpirationYear varchar(20) NULL, FirstNameOnCard varchar(75) NULL, 
	LastNameOnCard varchar(75) NULL, BillingAddress varchar(200) NULL, BillingCity varchar(75) NULL, 
	BillingState varchar(30) NULL, BillingZIP varchar(20) NULL, BillingCountry varchar(30) NULL, 
	NickName varchar(30) NULL)

BEGIN TRY
	SELECT @qry = 'BULK INSERT ##tmpAccCIMImport FROM ''' + @importFile + ''' WITH (FIELDTERMINATOR = ''' + char(9) + ''', FIRSTROW = 2);'
	EXEC(@qry)

	select *, 
		cast(null as int) as MCMemberID,
		ROW_NUMBER() OVER (ORDER BY MemberNumber, CardNumber) as rowID
	into #tmpAccCIMImport2 
	from ##tmpAccCIMImport

	IF OBJECT_ID('tempdb..##tmpAccCIMImport') IS NOT NULL 
		DROP TABLE ##tmpAccCIMImport
END TRY
BEGIN CATCH
	INSERT INTO #tblAccCIMErrors (msg)
	VALUES ('Unable to import data. ' + error_message())

	GOTO on_done
END CATCH


/* ********** */
/* check data */
/* ********** */
-- membernumber
BEGIN TRY
	ALTER TABLE #tmpAccCIMImport2 ALTER COLUMN MemberNumber varchar(50) not null;
	update #tmpAccCIMImport2 set MemberNumber = null where MemberNumber = '';
END TRY
BEGIN CATCH
	INSERT INTO #tblAccCIMErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing the required MemberNumber.' as msg
	FROM #tmpAccCIMImport2
	WHERE (MemberNumber IS NULL OR MemberNumber = '')
	ORDER BY rowID
END CATCH

update tmp
set tmp.MCMemberID = m.memberid
from #tmpAccCIMImport2 as tmp
inner join dbo.ams_members as m on m.memberNumber = tmp.MemberNumber
	and m.orgID = @orgID
	and m.memberID = m.activeMemberID
	and m.status <> 'D'

BEGIN TRY
	ALTER TABLE #tmpAccCIMImport2 ALTER COLUMN MCMemberID int not null;
END TRY
BEGIN CATCH
	INSERT INTO #tblAccCIMErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' (' + membernumber + ') does not match an existing MemberNumber.' as msg
	FROM #tmpAccCIMImport2
	WHERE (MCMemberID IS NULL)
	ORDER BY rowID
END CATCH

-- cardnumber
BEGIN TRY
	ALTER TABLE #tmpAccCIMImport2 ALTER COLUMN CardNumber varchar(20) not null;
	update #tmpAccCIMImport2 set CardNumber = null where CardNumber = '';
END TRY
BEGIN CATCH
	INSERT INTO #tblAccCIMErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing the required CardNumber.' as msg
	FROM #tmpAccCIMImport2
	WHERE (CardNumber IS NULL OR CardNumber = '')
	ORDER BY rowID
END CATCH

update #tmpAccCIMImport2 set CardNumber = membercentral.dbo.fn_regexReplace(CardNumber,'[^0-9]','')

-- expiration
BEGIN TRY
	ALTER TABLE #tmpAccCIMImport2 ALTER COLUMN ExpirationMonth varchar(20) not null;
	update #tmpAccCIMImport2 set ExpirationMonth = null where ExpirationMonth = '';
END TRY
BEGIN CATCH
	INSERT INTO #tblAccCIMErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing the required ExpirationMonth.' as msg
	FROM #tmpAccCIMImport2
	WHERE (ExpirationMonth IS NULL OR ExpirationMonth = '')
	ORDER BY rowID
END CATCH

BEGIN TRY
	ALTER TABLE #tmpAccCIMImport2 ALTER COLUMN ExpirationMonth int not null;
	update #tmpAccCIMImport2 set ExpirationMonth = null where ExpirationMonth <= 0 OR ExpirationMonth > 12;
	ALTER TABLE #tmpAccCIMImport2 ALTER COLUMN ExpirationMonth varchar(2) not null;
END TRY
BEGIN CATCH
	INSERT INTO #tblAccCIMErrors (msg)
	VALUES ('The file contains invalid values for ExpirationMonth.')
END CATCH

BEGIN TRY
	ALTER TABLE #tmpAccCIMImport2 ALTER COLUMN ExpirationYear varchar(20) not null;
	update #tmpAccCIMImport2 set ExpirationYear = null where ExpirationYear = '';
END TRY
BEGIN CATCH
	INSERT INTO #tblAccCIMErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing the required ExpirationYear.' as msg
	FROM #tmpAccCIMImport2
	WHERE (ExpirationYear IS NULL OR ExpirationYear = '')
	ORDER BY rowID
END CATCH

BEGIN TRY
	update #tmpAccCIMImport2 set ExpirationYear = '20' + ExpirationYear where len(ExpirationYear) = 2
	ALTER TABLE #tmpAccCIMImport2 ALTER COLUMN ExpirationYear int not null;
	update #tmpAccCIMImport2 set ExpirationYear = null where ExpirationYear < 2014 OR ExpirationYear > 2030;
	ALTER TABLE #tmpAccCIMImport2 ALTER COLUMN ExpirationYear varchar(4) not null;
END TRY
BEGIN CATCH
	INSERT INTO #tblAccCIMErrors (msg)
	VALUES ('The file contains invalid values for ExpirationYear.')
END CATCH

-- names
BEGIN TRY
	ALTER TABLE #tmpAccCIMImport2 ALTER COLUMN FirstNameOnCard varchar(75) not null;
	update #tmpAccCIMImport2 set FirstNameOnCard = null where FirstNameOnCard = '';
END TRY
BEGIN CATCH
	INSERT INTO #tblAccCIMErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing the required FirstNameOnCard.' as msg
	FROM #tmpAccCIMImport2
	WHERE (FirstNameOnCard IS NULL OR FirstNameOnCard = '')
	ORDER BY rowID
END CATCH

BEGIN TRY
	ALTER TABLE #tmpAccCIMImport2 ALTER COLUMN LastNameOnCard varchar(75) not null;
	update #tmpAccCIMImport2 set LastNameOnCard = null where LastNameOnCard = '';
END TRY
BEGIN CATCH
	INSERT INTO #tblAccCIMErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing the required LastNameOnCard.' as msg
	FROM #tmpAccCIMImport2
	WHERE (LastNameOnCard IS NULL OR LastNameOnCard = '')
	ORDER BY rowID
END CATCH


/* ************************** */
/* If no errors, add to table */
/* ************************** */
IF (select count(*) from #tblAccCIMErrors) = 0 BEGIN

	BEGIN TRY
		CREATE TABLE datatransfer.dbo.tmp_AuthCIMCards (autoid int IDENTITY(1,1) PRIMARY KEY, MemberID int, 
			profileMemberNumber varchar(50), profileFirstName varchar(75), profileLastName varchar(75), 
			CardNumber varchar(20), Expiration char(5), FirstNameOnCard varchar(75), LastNameOnCard varchar(75), 
			BillingAddress varchar(200), BillingCity varchar(75), BillingState varchar(30), 
			BillingZIP varchar(20), BillingCountry varchar(30), NickName varchar(30), added bit DEFAULT(0))

		INSERT INTO datatransfer.dbo.tmp_AuthCIMCards (MemberID, profileMemberNumber, profileFirstName, profileLastName,
			CardNumber, Expiration, FirstNameOnCard, LastNameOnCard, BillingAddress, BillingCity, BillingState, BillingZIP, 
			BillingCountry, NickName)
		select tmp.MCMemberID, tmp.MemberNumber, m.firstname, m.lastname, tmp.CardNumber, 
			right('00' + tmp.ExpirationMonth,2) + '/' + right('0000' + tmp.ExpirationYear,2), 
			tmp.FirstNameOnCard, tmp.LastNameOnCard, tmp.BillingAddress, tmp.BillingCity, tmp.BillingState, 
			tmp.BillingZIP, tmp.BillingCountry, tmp.NickName
		from #tmpAccCIMImport2 as tmp
		inner join dbo.ams_members as m on m.memberId = tmp.MCMemberID
		order by tmp.rowID
	END TRY
	BEGIN CATCH
		INSERT INTO #tblAccCIMErrors (msg)
		VALUES ('Unable to add cards to the processing table. ' + error_message())
	END CATCH

END

on_done:
	select @importResult = (
		select getdate() as "@date", 
			isnull((select top 301 dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblAccCIMErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE)

	IF OBJECT_ID('tempdb..##tmpAccCIMImport') IS NOT NULL 
		DROP TABLE ##tmpAccCIMImport
	IF OBJECT_ID('tempdb..#tmpAccCIMImport2') IS NOT NULL 
		DROP TABLE #tmpAccCIMImport2

RETURN 0
GO










