CREATE FUNCTION dbo.fn_tr_showInvoicePayOnlineLink (@invoiceID int)
RETURNS bit
AS
BEGIN

	declare @mpCount int, @showLink bit
	set @showLink = 0

	select @mpCount=count(*)
		from dbo.tr_invoiceMerchantProfiles as imp
		where imp.invoiceID = @invoiceID
	IF @mpCount = 0
		set @showLink = 1
	ELSE BEGIN
		select @mpCount=count(*)
			from dbo.tr_invoiceMerchantProfiles as imp
			inner join dbo.mp_profiles as mp on mp.profileID = imp.profileID
			inner join dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
			where imp.invoiceID = @invoiceID
			and g.gatewayClass <> 'offline'
		IF @mpCount > 0
			set @showLink = 1
	END

	RETURN @showLink

END
GO

ALTER PROC [dbo].[tr_viewInvoice]
@invoiceID int

AS

set nocount on

-- invoice info
SELECT i.invoiceID, i.dateCreated, i.dateBilled, i.dateDue, istat.status, i.invoiceCode, 
	o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber,
	m2.memberid as assignedToMemberID,
	case when i.payProfileID is null then 0 else 1 end as hasCard, 
	isnull(m2.lastname,'') + ', ' + isnull(m2.firstname,'') + case when o.hasMiddleName = 1 then isnull(' ' + m2.middlename,'') else '' end + ' (' + m2.membernumber + ')' as memberName,
	m2.company as memberCompany,
	ip.profileID as invoiceProfileID, ip.profileName as invoiceProfile,
	btn_canEdit = case when istat.status = 'Paid' then 0 else 1 end,
	btn_canEmail = case when istat.status in ('Closed','Paid') then 1 else 0 end,
	btn_canPay = case when sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) > 0 then 1 else 0 end,
	sum(it.cache_invoiceAmountAfterAdjustment) as InvAmt,
	sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) as InvDue,
	dbo.fn_tr_showInvoicePayOnlineLink(i.invoiceID) as showLink
from dbo.tr_invoices as i
inner join dbo.tr_invoiceStatuses as istat on istat.statusID = i.statusID
inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
inner join dbo.ams_members as m on m.memberid = i.assignedToMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
inner join dbo.organizations as o on o.orgID = m2.orgID
left outer join dbo.tr_invoiceTransactions as it 
	inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
	on it.invoiceID = i.invoiceID
where i.invoiceID = @invoiceID
group by i.invoiceID, i.dateCreated, i.dateBilled, i.dateDue, istat.status, i.invoiceCode, o.orgcode, 
	i.invoiceNumber, m2.memberid, i.payProfileID, m2.lastname, m2.firstname, o.hasMiddleName, m2.middlename, 
	m2.membernumber, m2.company, ip.profileID, ip.profileName

-- invoice history
select ish.updateDate, istat.status, 
	isnull(m2.lastname,'') + ', ' + isnull(m2.firstname,'') + case when o.hasMiddleName = 1 then isnull(' ' + m2.middlename,'') else '' end + ' (' + m2.membernumber + ')' as memberName,
	m2.company as memberCompany
from dbo.tr_invoiceStatusHistory as ish
inner join dbo.tr_invoiceStatuses as istat on istat.statusID = ish.statusID
inner join dbo.ams_members as m on m.memberid = ish.enteredByMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
inner join dbo.organizations as o on o.orgID = m2.orgID
where ish.invoiceID = @invoiceID
order by ish.updateDate, ish.statusHistoryID

RETURN 0
GO

