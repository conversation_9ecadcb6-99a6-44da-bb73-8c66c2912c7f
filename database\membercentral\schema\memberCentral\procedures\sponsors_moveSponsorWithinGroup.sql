CREATE PROC dbo.sponsors_moveSponsorWithinGroup
@sponsorUsageID int,
@sponsorGroupingID int,
@direction varchar(10),
@referenceType varchar(50),
@referenceID int

AS

SET XACT_ABORT, NOCOUNT ON;

BEGIN TRY
	BEGIN TRANSACTION;

	DECLARE @currentOrder int
	DECLARE @targetOrder int
	DECLARE @targetUsageID int
	
	-- Get current sponsor order
	SELECT @currentOrder = sponsorOrder
	FROM dbo.sponsorsUsage
	WHERE sponsorUsageID = @sponsorUsageID
	
	-- Calculate target order based on direction
	IF @direction = 'up'
		SET @targetOrder = @currentOrder - 1
	ELSE
		SET @targetOrder = @currentOrder + 1
		
	-- Find the sponsor at the target position within the same group
	SELECT @targetUsageID = sponsorUsageID
	FROM dbo.sponsorsUsage
	WHERE referenceType = @referenceType
	AND referenceID = @referenceID
	AND sponsorOrder = @targetOrder
	AND ISNULL(sponsorGroupingID, 0) = ISNULL(@sponsorGroupingID, 0)
	
	-- Swap positions if target exists
	IF @targetUsageID IS NOT NULL
	BEGIN
		-- Update target sponsor order
		UPDATE dbo.sponsorsUsage
		SET sponsorOrder = @currentOrder
		WHERE sponsorUsageID = @targetUsageID
		
		-- Update current sponsor order
		UPDATE dbo.sponsorsUsage
		SET sponsorOrder = @targetOrder
		WHERE sponsorUsageID = @sponsorUsageID
	END

	COMMIT TRANSACTION;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
