ALTER PROC dbo.lists_outgoingListMessagesReportExport
@startDate datetime,
@endDate datetime,
@orgcode varchar(10),
@listType varchar(20),
@includeLists varchar(max),
@filename varchar(800)

AS

	declare @optionalIncludeListFilter TABLE (listname varchar(100));
	insert into @optionalIncludeListFilter (listname)
	select distinct listitem from dbo.fn_varcharListToTable(@includeLists,',') where listitem <> '';

	IF OBJECT_ID('tempdb..#tmpListData') IS NOT NULL 
		DROP TABLE #tmpListData;
	CREATE TABLE #tmpListData (orgcode varchar(10), listName varchar(60), listType varchar(25), numRecipients int, reportMonth char(6));

	-- list usage
	INSERT INTO #tmpListData (orgcode, listName, listType, numRecipients, reportMonth)
	select lf.orgcode, ml.list, l.MCSetting_ListType, sum(rld.numRecipients), FORMAT(rld.reportDate,'yyyyMM')
	from lyrisarchive.lyrisarchive.dbo.mc_recipientsByListByDay as rld
	inner join lyrisarchive.lyrisarchive.dbo.messageLists as ml on ml.listID = rld.listID
		and 1 = case when NOT EXISTS (select listname from @optionalIncludeListFilter) then 1
			when ml.list in (select listName COLLATE Latin1_General_CI_AI from @optionalIncludeListFilter) then 1
			else 0 end
	inner join lyrisarchive.trialslyris1.dbo.lists_ as l on l.name_ = ml.list COLLATE Latin1_General_CI_AI 
		and rld.reportDate between @startDate and @enddate
		and l.MCSetting_ListType = case when @listType <> '' then @listType else l.MCSetting_ListType end
	inner join lyrisarchive.trialslyris1.dbo.lists_format as lf on lf.name = l.name_
		and lf.orgcode = case when @orgcode <> '' then @orgcode else lf.orgcode end
	group by lf.orgcode, ml.list, l.MCSetting_ListType, FORMAT(rld.reportDate,'yyyyMM');

	declare @monthlist varchar(500);
	with monthsCTE as (
		select @startDate as themonth, format(@startDate,'yyyyMM') as reportMonth
			union all
		select dateadd(mm,1,monthsCTE.themonth) as themonth, format(dateadd(mm,1,monthsCTE.themonth),'yyyyMM') as reportMonth
		from monthsCTE
		where dateadd(mm,1,monthsCTE.themonth) < @enddate
	)
	SELECT @monthlist = COALESCE(@monthlist + ',','') + quoteName(reportMonth)
	FROM monthsCTE;

	declare @sql varchar(8000);
	set @sql = 'select * from #tmpListData as tmp pivot (min(numRecipients) for reportMonth in ('+@monthlist+')) as pvt	order by orgcode, listName, listType';
	EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql=@sql;

	IF OBJECT_ID('tempdb..#tmpListData') IS NOT NULL 
		DROP TABLE #tmpListData;
GO
