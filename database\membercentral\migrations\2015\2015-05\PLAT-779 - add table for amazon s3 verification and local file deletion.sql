use datatransfer;
GO
CREATE TABLE dbo.queue_verifyS3UploadAndDelete
	(
	fileID int NOT NULL IDENTITY (1, 1),
	s3bucketName varchar(100) NOT NULL,
	objectKey varchar(200) NOT NULL,
	filePath varchar(200) NOT NULL,
	dateAdded datetime NOT NULL,
	nextAttemptDate datetime NOT NULL,
	hasFailed bit NOT NULL
	)  ON [PRIMARY]
GO
ALTER TABLE dbo.queue_verifyS3UploadAndDelete ADD CONSTRAINT
	DF_queue_verifyS3UploadAndDelete_dateAdded DEFAULT getdate() FOR dateAdded
GO
ALTER TABLE dbo.queue_verifyS3UploadAndDelete ADD CONSTRAINT
	DF_queue_verifyS3UploadAndDelete_nextAttemptDate DEFAULT getdate() FOR nextAttemptDate
GO
ALTER TABLE dbo.queue_verifyS3UploadAndDelete ADD CONSTRAINT
	DF_queue_verifyS3UploadAndDelete_hasFailed DEFAULT 0 FOR hasFailed
GO
ALTER TABLE dbo.queue_verifyS3UploadAndDelete ADD CONSTRAINT
	PK_queue_verifyS3UploadAndDelete PRIMARY KEY CLUSTERED 
	(
	fileID
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

GO
