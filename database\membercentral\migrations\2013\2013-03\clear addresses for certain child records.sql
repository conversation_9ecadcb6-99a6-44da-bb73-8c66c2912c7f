declare @addressTypeID int, @j int, @thisaddressTypeID int, @thisMemberID int, @returnedAddressID int

-- use this table for looping and updating each record
declare @childMembersToClear table (autoID int IDENTITY PRIMARY KEY, addressTypeID int, memberID int);

-- delete the table and put the data in fresh each time
delete from @childMembersToClear
insert into @childMembersToClear (addressTypeID, memberID)
	select mat.addressTypeID
		, cm.memberID
		--, ma.*
	from organizations o
	inner join dbo.ams_members m
		on m.orgID = o.orgID
		and o.orgcode = 'md'
		and m.membernumber = '10773260'
	inner join ams_recordTypes masterrt
		on masterrt.recordTypeID = m.recordTypeID
		and o.orgcode = 'md'
	inner join dbo.ams_recordTypesRelationshipTypes rtrt
		on rtrt.masterRecordTypeID = masterrt.recordTypeID
	inner join ams_recordTypes childrt
		on childrt.recordTypeID = rtrt.childRecordTypeID
	inner join ams_recordRelationShipTypes rrt
		on rrt.relationshipTypeID = rtrt.relationshipTypeID
	inner join dbo.ams_recordRelationships rr
		on rr.recordTypeRelationshipTypeID = rtrt.recordTypeRelationshipTypeID
		and rr.masterMemberID = m.memberId
	inner join ams_members cm
		on cm.memberID = rr.childMemberID
		and cm.membernumber not in ('10781720','10788220','10788270','10787650','10788260','10788250','10788780','7734530')
	left outer join ams_memberAddresses ma
		inner join ams_memberAddressTypes mat
			on mat.addressTypeID = ma.addressTypeID
			and mat.addressType = 'Office Address'
	on ma.memberID = cm.memberID


-- loop and save/clear each member
select @j = min(autoID) from @childMembersToClear
while @j is not null
begin	
	
	select @thisaddressTypeID = addressTypeID, @thismemberID = memberID
	from @childMembersToClear
	where autoID = @j
	
	exec dbo.ams_saveMemberAddress
		@memberID=@thismemberID, 
		@addressTypeID=@thisaddressTypeID, 
		@attn='',  
		@address1='',  
		@address2='',  
		@address3='',  
		@city='',  
		@stateID=null,  
		@postalCode='',  
		@county='', 
		@countryID=null,  
		@addressID=@returnedAddressID OUTPUT

	print @thismemberID
	print 'saved'

	select @j = min(autoID) from @childMembersToClear where autoID > @j

end -- while @j is not null
