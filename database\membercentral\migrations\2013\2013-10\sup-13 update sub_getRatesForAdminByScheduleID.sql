USE [memberCentral]
GO
/****** Object:  StoredProcedure [dbo].[sub_getRatesForAdminByScheduleID]    Script Date: 10/21/2013 09:33:18 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROCEDURE [dbo].[sub_getRatesForAdminByScheduleID] 
	@scheduleID int
AS
BEGIN
declare @rtid int, @rfid int
select @rtid = dbo.fn_getResourceTypeID('SubscriptionRate')
select @rfid = dbo.fn_getResourceFunctionID('Qualify',@rtid) 

select case when LEN(r.rateName) = 0 then '(name not set)' else r.rateName end as rateName,
	r.rateID, r.status, r.rateStartDate, r.rateEndDate, r.rateAFStartDate, r.rateAFEndDate,
	r.termStartDate,r.termEndDate,r.termAFStartDate,r.termAFEndDate,r.siteResourceID,
	r.is<PERSON><PERSON>wal<PERSON>ate, r.force<PERSON>pfront, g.groupID, g.groupName, srrc.include
from dbo.sub_rates r
inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc in ('Active', 'Deleted')
left outer join dbo.cms_siteResourceRightsCache as srrc
	INNER JOIN dbo.ams_groups as g on g.groupID = srrc.groupID 
on srrc.resourceid = r.siteresourceID AND srrc.functionID = @rfid and g.status = 'A'
where r.scheduleID = @scheduleID
ORDER BY r.rateID

RETURN 0

END

