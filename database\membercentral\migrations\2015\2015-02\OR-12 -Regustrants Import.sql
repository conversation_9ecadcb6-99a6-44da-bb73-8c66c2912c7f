use datatransfer
GO


/* START SET VARIABLES HERE */
declare 
	@sitecode varchar(10), @tmpFile varchar(400), @timeZoneID int, 
	@replyToEmail varchar(100), @deleteMissingCalendars bit, @deleteMissingMembers bit,	
	@deleteMissingCategories bit, @stopBeforeImport bit

set @sitecode = 'OR'
set @tmpFile = 'c:\temp\Registrants Import.txt'
set @timeZoneID = 3
set @replyToEmail = '<EMAIL> '
set @deleteMissingCalendars = 0
set @deleteMissingMembers = 1
set @deleteMissingCategories = 0
set @stopBeforeImport = 0
/* END SET VARIABLES HERE */

SET NOCOUNT ON

declare 
		@orgID int, @siteID int, @errSection varchar(30), 
		@qry varchar(2000), @minID int, @MCEventID int, 
		@MCCalendarID int, @MCsystemMemberID int, @MCAllDayEvent bit, 
		@eventCode varchar(15), @starttime datetime, @endtime datetime, 
		@eventContentID int, @eventName varchar(400), @expirationContentID int, 
		@maxEventID int, @MCRegistrationID int, @customQID int,
		@registrantCap int, @eventLocation varchar (max), @creditsGeneral decimal(6,2),	
		@creditsPractical decimal(6,2),	@creditsEthics decimal(6,2), @creditsAbuse decimal(6,2),
		@creditsAccess decimal(6,2), @internalNotes varchar (max), @programNumber varchar (50),
		@eventCreditType varchar(100), @eventCreditType2 varchar(100), @eventCreditType3 varchar(100),
		@eventCreditType4 varchar(100), @eventCreditType5 varchar(100), @ASID int,
		@locationContentID int, @rawcontent varchar(max)


select @orgID = orgID, @siteID = siteID from membercentral.dbo.sites where sitecode = @siteCode
select @MCsystemMemberID = memberID from membercentral.dbo.ams_members where memberNumber = 'SYSTEM' and orgID = 1
set @eventCreditType = 'General'
select @eventCreditType2 = 'Practice Skills'
select @eventCreditType3 = 'Professional Responsibility - Ethics'
select @eventCreditType4 = 'Professional Responsibility - Child Abuse Reporting'
select @eventCreditType5 = 'Professional Responsibility - Access to Justice'

/* ************** */
/* initial checks */
/* ************** */
IF @siteID is null BEGIN
	set @errSection = 'initialchecks'
	GOTO on_error	
END

/* ********************* */
/* import data from file */
/* ********************* */
IF OBJECT_ID('tempdb..##tmpEvImport') IS NOT NULL 
	DROP TABLE ##tmpEvImport
IF OBJECT_ID('tempdb..#tmpEvImport') IS NOT NULL 
	DROP TABLE #tmpEvImport
IF OBJECT_ID('tempdb..#tmpEvImportEV') IS NOT NULL 
	DROP TABLE #tmpEvImportEV

IF OBJECT_ID('tempdb..##tmpRegImport') IS NOT NULL 
	DROP TABLE ##tmpRegImport

IF OBJECT_ID('tempdb..#tmpRegImport') IS NOT NULL 
	DROP TABLE #tmpRegImport


CREATE TABLE ##tmpEvImport (
	[calendarName] [varchar](200) NULL,
	[eventCode] [varchar](15) NULL,
	[eventCategory] [varchar](50) NULL,
	[eventName] [varchar](400) NULL,
	[eventStartDate] [datetime] NULL,
	[eventEndDate] [datetime] NULL,
	[programNumber] [varchar](50) NULL,
	[eventLocation] [varchar](max) NULL,
	[registrantCap] [int] NULL,
	[creditsGeneral] [decimal](6,2),	
	[creditsPractical] [decimal](6,2),	
	[creditsEthics] [decimal](6,2), 
	[creditsAbuse] [decimal](6,2),
	[creditsAccess] [decimal](6,2), 
	[internalNotes] [varchar] (max)
)

CREATE TABLE ##tmpRegImport (
	[eventCode] [varchar](15) NULL,
	[memberNumber] [varchar](100) NULL,
	[paid] [bit] NULL,
	[phone_in] [bit] NULL,
	[attending] [bit] NULL,
	[guests] [varchar](max) NULL,
	[notes] [varchar](max) NULL
)


BEGIN TRY
	SELECT @qry = 'BULK INSERT ##tmpRegImport FROM ''' + @tmpFile + ''' WITH (FIELDTERMINATOR = ''' + char(9) + ''', FIRSTROW = 2);'
	EXEC(@qry)
END TRY
BEGIN CATCH
	EXEC membercentral.dbo.up_errorhandler
	set @errSection = 'importfromfile'
	GOTO on_error	
END CATCH

--select * from ##tmpRegImport
--
-- GOTO on_good

select *,
	--cast(null as int) as MCCalendarID,
	cast(null as int) as MCMemberID,
	cast(null as int) as MCEventID,
	--cast(null as int) as MCCategoryID,
	cast(null as int) as MCRegistrationID,
	cast(1 as bit) as MCAllDayEvent,
	cast(null as int) as paidQID,
	cast(null as int) as phone_inQID,
	cast(null as int) as guestsQID,
	cast(null as int) as notesQID,
	cast(null as int) as MCCreditOfferingID,
	cast(null as int) as MCRegistrantID,
	ROW_NUMBER() OVER(order by eventCode) as autoID
into #tmpRegImport 
from ##tmpRegImport
where eventCode <> 0

IF OBJECT_ID('tempdb..##tmpRegImport') IS NOT NULL 
	DROP TABLE ##tmpRegImport

/* ************************* */
/* try to match by eventcode */
/* ************************* */
BEGIN TRY
	update tmp
	set tmp.MCEventID = e.eventID
	from #tmpRegImport as tmp
	inner join membercentral.dbo.ev_events as e on 
		e.reportCode = tmp.eventCode
		and e.status = 'A'
		and e.siteID = @siteID
	inner join membercentral.dbo.ev_calendarEvents as ce on ce.sourceEventID = e.eventID
		--and ce.calendarID = tmp.MCCalendarID
		and ce.calendarID = ce.sourceCalendarID

	delete from #tmpRegImport where MCEventID is null

	update tmp
	set tmp.MCRegistrationID = r.registrationID
	from #tmpRegImport as tmp
	inner join membercentral.dbo.ev_registration as r on r.eventID = tmp.MCEventID and r.status = 'A'
	where tmp.MCEventID is not null
END TRY
BEGIN CATCH
	set @errSection = 'matchingeventcode'
	GOTO on_error
END CATCH

/* **************************** */
/* try to match by membernumber */
/* **************************** */
BEGIN TRY
	update tmp
	set tmp.MCMemberID = m.memberid
	from #tmpRegImport as tmp
	inner join membercentral.dbo.ams_members as m on m.memberNumber = tmp.memberNumber
		and m.orgID = @orgID
		and m.memberID = m.activeMemberID

	IF @deleteMissingMembers = 1
		delete from #tmpRegImport where MCMemberID is null

	IF EXISTS (select * from #tmpRegImport where MCMemberID is null) BEGIN
		select distinct memberNumber as MemberNumberNotFound
		from #tmpRegImport
		where MCMemberID is null
		order by 1

		RAISERROR('Error raised in TRY block.', 16, 1);
	END
END TRY
BEGIN CATCH
	set @errSection = 'membernotfound'
	GOTO on_error
END CATCH


/* ********************** */
/* try to match by custom */
/* ********************** */
BEGIN TRY
	update tmp
	set tmp.paidQID = rc.customID
	from #tmpRegImport as tmp
	inner join membercentral.dbo.ev_registrationCustom as rc on rc.registrationID = tmp.MCRegistrationID
		and rc.status = 'A'
	where rc.titleOnInvoice = 'Paid'

	update tmp
	set tmp.phone_inQID = rc.customID
	from #tmpRegImport as tmp
	inner join membercentral.dbo.ev_registrationCustom as rc on rc.registrationID = tmp.MCRegistrationID
		and rc.status = 'A'
	where rc.titleOnInvoice = 'Phone_in'

	update tmp
	set tmp.guestsQID = rc.customID
	from #tmpRegImport as tmp
	inner join membercentral.dbo.ev_registrationCustom as rc on rc.registrationID = tmp.MCRegistrationID
		and rc.status = 'A'
	where rc.titleOnInvoice = 'Guests'

	update tmp
	set tmp.notesQID = rc.customID
	from #tmpRegImport as tmp
	inner join membercentral.dbo.ev_registrationCustom as rc on rc.registrationID = tmp.MCRegistrationID
		and rc.status = 'A'
	where rc.titleOnInvoice = 'Notes'

END TRY
BEGIN CATCH
	set @errSection = 'matchingcustom'
	GOTO on_error
END CATCH

/* ****************** */
/* Add Missing Credit */
/* ****************** */
select @ASID = cas.ASID
	from membercentral.dbo.crd_authoritySponsors as cas
	inner join membercentral.dbo.crd_authorities as ca on ca.authorityID = cas.authorityID and ca.authorityName = 'Oregon State Bar Association'
	inner join membercentral.dbo.crd_sponsors as cs on cs.sponsorID = cas.sponsorID and cs.orgID = @orgID

update tmp
set tmp.MCCreditOfferingID = co.offeringID
from #tmpRegImport as tmp
inner join membercentral.dbo.crd_offerings as co on 
	co.ASID = @ASID 
	and co.eventID = tmp.MCEventID and co.statusID = 4

select *
from #tmpRegImport tmp

/* ********************** */
/* check dupe enrollments */
/* ********************** */
IF EXISTS (select MCMemberID, MCEventID from #tmpRegImport GROUP BY MCMemberID, MCEventID HAVING COUNT(*) > 1) BEGIN
	/*
	select 'Duplicate enrollments in file' as errReason, * 
	from (
		select MCMemberID, MCEventID 
		from #tmpRegImport 
		GROUP BY MCMemberID, MCEventID 
		HAVING COUNT(*) > 1
	) as tmp2
	inner join #tmpRegImport as tmp on tmp.MCMemberID = tmp2.MCMemberID and tmp.MCEventID = tmp2.MCEventID
	ORDER BY tmp.MCMemberID, tmp.MCEventID
	*/
	IF OBJECT_ID('tempdb..#tmpEnrollmentDups') IS NOT NULL 
		DROP TABLE #tmpEnrollmentDups

	select tmp.MCMemberID, tmp.MCEventID  into #tmpEnrollmentDups
	from (
		select MCMemberID, MCEventID 
		from #tmpRegImport 
		GROUP BY MCMemberID, MCEventID 
		HAVING COUNT(*) > 1
	) as tmp2
	inner join #tmpRegImport as tmp on 
		tmp.MCMemberID = tmp2.MCMemberID 
		and tmp.MCEventID = tmp2.MCEventID
	ORDER BY tmp.MCMemberID, tmp.MCEventID
	
	-- Skipping duplicates
	delete from tmp
	from #tmpRegImport tmp
	inner join #tmpEnrollmentDups dups on 
		dups.MCMemberID = tmp.MCMemberID 
		and dups.MCEventID = tmp.MCEventID	
	
	IF OBJECT_ID('tempdb..#tmpEnrollmentDups') IS NOT NULL 
		DROP TABLE #tmpEnrollmentDups	

	--set @errSection = 'dupeenrollments'
	-- GOTO on_error
END

select *
from #tmpRegImport tmp

select distinct eventCode, MCEventID, MCRegistrationID, paidQID, phone_inQID, guestsQID,notesQID
	into #tmpEvImportEV
from #tmpRegImport


/* ********** */
/* IF TESTING */
/* ********** */
IF @stopBeforeImport = 1 BEGIN
	set @errSection = 'testing'
	GOTO on_error
END

-- BEGIN TRAN

begin try

EXEC membercentral.dbo.cache_perms_setStatus @orgID, 'disabled'

/* ****************** */
/* Add Missing Custom */
/* ****************** */
insert into membercentral.dbo.ev_registrationCustom (registrationID, areaID, fieldDesc, titleOnInvoice, customTypeID, 
	isRequired, requiredMSG, fieldOrder, [status], offerQty, amount, GLAccountID)
SELECT MCRegistrationID, 2, 'Paid', 'Paid', 1, 0, null, 1, 'A', 0, null, null
FROM #tmpEvImportEV 
where paidQID is null

update tmp
set tmp.paidQID = rc.customID
from #tmpRegImport as tmp
inner join membercentral.dbo.ev_registrationCustom as rc on rc.registrationID = tmp.MCRegistrationID
	and rc.status = 'A'
where rc.titleOnInvoice = 'Paid'
and tmp.paidQID is null

insert into membercentral.dbo.ev_registrationCustom (registrationID, areaID, fieldDesc, titleOnInvoice, customTypeID, 
	isRequired, requiredMSG, fieldOrder, [status], offerQty, amount, GLAccountID)
SELECT MCRegistrationID, 2, 'Phone_in', 'Phone_in', 1, 0, null, 1, 'A', 0, null, null
FROM #tmpEvImportEV 
where phone_inQID is null

update tmp
set tmp.phone_inQID = rc.customID
from #tmpRegImport as tmp
inner join membercentral.dbo.ev_registrationCustom as rc on rc.registrationID = tmp.MCRegistrationID
	and rc.status = 'A'
where rc.titleOnInvoice = 'Phone_in'
and tmp.phone_inQID is null

insert into membercentral.dbo.ev_registrationCustom (registrationID, areaID, fieldDesc, titleOnInvoice, customTypeID, 
	isRequired, requiredMSG, fieldOrder, [status], offerQty, amount, GLAccountID)
SELECT MCRegistrationID, 2, 'Guests', 'Guests', 1, 0, null, 1, 'A', 0, null, null
FROM #tmpEvImportEV 
where guestsQID is null

update tmp
set tmp.guestsQID = rc.customID
from #tmpRegImport as tmp
inner join membercentral.dbo.ev_registrationCustom as rc on rc.registrationID = tmp.MCRegistrationID
	and rc.status = 'A'
where rc.titleOnInvoice = 'Guests'
and tmp.guestsQID is null

insert into membercentral.dbo.ev_registrationCustom (registrationID, areaID, fieldDesc, titleOnInvoice, customTypeID, 
	isRequired, requiredMSG, fieldOrder, [status], offerQty, amount, GLAccountID)
SELECT MCRegistrationID, 2, 'Notes', 'Notes', 1, 0, null, 1, 'A', 0, null, null
FROM #tmpEvImportEV 
where notesQID is null

update tmp
set tmp.notesQID = rc.customID
from #tmpRegImport as tmp
inner join membercentral.dbo.ev_registrationCustom as rc on rc.registrationID = tmp.MCRegistrationID
	and rc.status = 'A'
where rc.titleOnInvoice = 'Notes'
and tmp.notesQID is null



/* *********************** */
/* Add Missing Registrants */
/* *********************** */
IF OBJECT_ID('tempdb..#tmpNewReg') IS NOT NULL 
	DROP TABLE #tmpNewReg
CREATE TABLE #tmpNewReg (registrantID int)

insert into membercentral.dbo.ev_registrants (registrationID, memberID, recordedOnSiteID, rateID, dateRegistered, status, attended)
	OUTPUT inserted.registrantID
	INTO #tmpNewReg
select distinct MCRegistrationID, MCMemberID, @siteID, null, reg.startDate, 'A', 0
from #tmpRegImport as e
inner join membercentral.dbo.ev_registration as reg on
	reg.registrationID = e.MCRegistrationID
where not exists (
	select r.registrantID
	from membercentral.dbo.ev_registrants as r
	inner join membercentral.dbo.ams_members as m on m.memberID = r.memberID
	where r.registrationID = e.MCRegistrationID
	and m.activeMemberID = e.MCMemberID
	and r.status <> 'D'
)

update tmp
set tmp.MCRegistrantID = r.registrantID
from #tmpRegImport as tmp
inner join membercentral.dbo.ev_registrants as r on r.registrationID = tmp.MCRegistrationID
inner join membercentral.dbo.ams_members as m on m.memberID = r.memberID
	and m.activeMemberID = tmp.MCMemberID
where r.status = 'A'

IF EXISTS (select * from #tmpRegImport where MCRegistrantID is null) BEGIN
	select 'RegistrantID is missing' as errReason, * 
	from #tmpRegImport
	where MCRegistrantID is null
END


/* ********************************************* */
/* CUSTOM Q for Registrants only when just added */
/* ********************************************* */
INSERT INTO membercentral.dbo.ev_registrantDetails (registrantID, customID, customOptionID, customText, status)
select distinct r.registrantID, tmp2.paidQID, null, 'Yes', 'A'
from membercentral.dbo.ev_registrants as r
inner join #tmpNewReg as tmp on tmp.registrantID = r.registrantID
inner join #tmpRegImport as tmp2 on tmp2.MCRegistrantID = r.registrantID
-- where isnull(tmp2.custom1,'') <> '' Marling all as 'Paid'

INSERT INTO membercentral.dbo.ev_registrantDetails (registrantID, customID, customOptionID, customText, status)
select distinct r.registrantID, tmp2.phone_inQID, null, case tmp2.phone_in when 1 then 'Yes' else 'No' end, 'A'
from membercentral.dbo.ev_registrants as r
inner join #tmpNewReg as tmp on tmp.registrantID = r.registrantID
inner join #tmpRegImport as tmp2 on tmp2.MCRegistrantID = r.registrantID
-- where isnull(tmp2.phone_in,'') <> ''


INSERT INTO membercentral.dbo.ev_registrantDetails (registrantID, customID, customOptionID, customText, status)
select distinct r.registrantID, tmp2.guestsQID, null, tmp2.guests, 'A'
from membercentral.dbo.ev_registrants as r
inner join #tmpNewReg as tmp on tmp.registrantID = r.registrantID
inner join #tmpRegImport as tmp2 on tmp2.MCRegistrantID = r.registrantID
-- where isnull(tmp2.guests,'') <> ''

INSERT INTO membercentral.dbo.ev_registrantDetails (registrantID, customID, customOptionID, customText, status)
select distinct r.registrantID, tmp2.notesQID, null, tmp2.notes, 'A'
from membercentral.dbo.ev_registrants as r
inner join #tmpNewReg as tmp on tmp.registrantID = r.registrantID
inner join #tmpRegImport as tmp2 on tmp2.MCRegistrantID = r.registrantID
-- where isnull(tmp2.notes,'') <> ''


/* ************************** */
/* ATTENDANCE for Registrants */
/* ************************** */
update r
set r.attended = 1
from membercentral.dbo.ev_registrants as r
inner join #tmpRegImport as tmp on 
	tmp.MCRegistrantID = r.registrantID


/* *********************** */
/* CREDITS for Registrants */
/* *********************** */

insert into membercentral.dbo.crd_requests (offeringTypeID, IDNumber, lastDateToComplete, creditAwarded, creditValueAwarded, addedViaAward, registrantID)
SELECT distinct ot.offeringTypeID, '', reg.endDate, 1, ot.creditValue, 1, tmp.MCRegistrantID
from #tmpRegImport as tmp
inner join membercentral.dbo.ev_registration as reg on
	reg.registrationID = tmp.MCRegistrationID
inner join membercentral.dbo.crd_offeringTypes as ot on ot.offeringID = tmp.MCCreditOfferingID
inner join membercentral.dbo.crd_authoritySponsorTypes as ast on ast.ASTID = ot.ASTID
inner join membercentral.dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
	and cat.typeName = @eventCreditType
where tmp.MCCreditOfferingID is not null
 and ot.creditValue > 0
except (
	select offeringTypeID, IDNumber, lastDateToComplete, creditAwarded,  creditValueAwarded,  addedViaAward,  registrantID
	from membercentral.dbo.crd_requests
)
	union all
SELECT distinct ot.offeringTypeID, '', reg.endDate, 1, ot.creditValue, 1, tmp.MCRegistrantID
from #tmpRegImport as tmp
inner join membercentral.dbo.ev_registration as reg on
	reg.registrationID = tmp.MCRegistrationID
inner join membercentral.dbo.crd_offeringTypes as ot on ot.offeringID = tmp.MCCreditOfferingID
inner join membercentral.dbo.crd_authoritySponsorTypes as ast on ast.ASTID = ot.ASTID
inner join membercentral.dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
	and cat.typeName = @eventCreditType2
where tmp.MCCreditOfferingID is not null
 and ot.creditValue > 0
except (
	select offeringTypeID, IDNumber, lastDateToComplete, creditAwarded,  creditValueAwarded,  addedViaAward,  registrantID
	from membercentral.dbo.crd_requests
)
	union all
SELECT distinct ot.offeringTypeID, '', reg.endDate, 1, ot.creditValue, 1, tmp.MCRegistrantID
from #tmpRegImport as tmp
inner join membercentral.dbo.ev_registration as reg on
	reg.registrationID = tmp.MCRegistrationID
inner join membercentral.dbo.crd_offeringTypes as ot on ot.offeringID = tmp.MCCreditOfferingID
inner join membercentral.dbo.crd_authoritySponsorTypes as ast on ast.ASTID = ot.ASTID
inner join membercentral.dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
	and cat.typeName = @eventCreditType3
where tmp.MCCreditOfferingID is not null
 and ot.creditValue > 0
except (
	select offeringTypeID, IDNumber, lastDateToComplete, creditAwarded,  creditValueAwarded,  addedViaAward,  registrantID
	from membercentral.dbo.crd_requests
)
	union all
SELECT distinct ot.offeringTypeID, '', reg.endDate, 1, ot.creditValue, 1, tmp.MCRegistrantID
from #tmpRegImport as tmp
inner join membercentral.dbo.ev_registration as reg on
	reg.registrationID = tmp.MCRegistrationID
inner join membercentral.dbo.crd_offeringTypes as ot on ot.offeringID = tmp.MCCreditOfferingID
inner join membercentral.dbo.crd_authoritySponsorTypes as ast on ast.ASTID = ot.ASTID
inner join membercentral.dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
	and cat.typeName = @eventCreditType4
where tmp.MCCreditOfferingID is not null
 and ot.creditValue > 0
except (
	select offeringTypeID, IDNumber, lastDateToComplete, creditAwarded,  creditValueAwarded,  addedViaAward,  registrantID
	from membercentral.dbo.crd_requests
)
	union all
SELECT distinct ot.offeringTypeID, '', reg.endDate, 1, ot.creditValue, 1, tmp.MCRegistrantID
from #tmpRegImport as tmp
inner join membercentral.dbo.ev_registration as reg on
	reg.registrationID = tmp.MCRegistrationID
inner join membercentral.dbo.crd_offeringTypes as ot on ot.offeringID = tmp.MCCreditOfferingID
inner join membercentral.dbo.crd_authoritySponsorTypes as ast on ast.ASTID = ot.ASTID
inner join membercentral.dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
	and cat.typeName = @eventCreditType5
where tmp.MCCreditOfferingID is not null
 and ot.creditValue > 0
except (
	select offeringTypeID, IDNumber, lastDateToComplete, creditAwarded,  creditValueAwarded,  addedViaAward,  registrantID
	from membercentral.dbo.crd_requests
)

/* ************ */
/* Final Checks */
/* ************ */
IF EXISTS (select * from #tmpRegImport where MCRegistrationID is null) BEGIN
	select 'Registration is missing' as errReason, * 
	from #tmpRegImport
	where MCRegistrationID is null

	set @errSection = 'registrationmissing'
	GOTO on_error
END


/* ************************** */

-- IF @@TRANCOUNT > 0 COMMIT TRAN

end try

begin catch
    --returns the complete original error message as a result set
    SELECT 
        ERROR_NUMBER() AS ErrorNumber
        ,ERROR_SEVERITY() AS ErrorSeverity
        ,ERROR_STATE() AS ErrorState
        ,ERROR_PROCEDURE() AS ErrorProcedure
        ,ERROR_LINE() AS ErrorLine
        ,ERROR_MESSAGE() AS ErrorMessage

    --will return the complete original error message as an error message
    DECLARE @ErrorMessage nvarchar(400), @ErrorNumber int, @ErrorSeverity int, @ErrorState int, @ErrorLine int
    SELECT @ErrorMessage = N'Error %d, Line %d, Message: '+ERROR_MESSAGE(),@ErrorNumber = ERROR_NUMBER(),@ErrorSeverity = ERROR_SEVERITY(),@ErrorState = ERROR_STATE(),@ErrorLine = ERROR_LINE()
    RAISERROR (@ErrorMessage, @ErrorSeverity, @ErrorState, @ErrorNumber,@ErrorLine)

	--IF @@TRANCOUNT > 0 ROLLBACK TRAN

end catch


GOTO on_good

on_error:
	print 'import stopped at ' + @errSection
	--IF @@TRANCOUNT > 1 COMMIT TRAN
	--ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	goto on_done

on_good:
	print 'import success.'

on_done:
	print 'end of import.'

	IF OBJECT_ID('tempdb..##tmpEvImport') IS NOT NULL 
		DROP TABLE ##tmpEvImport
	IF OBJECT_ID('tempdb..#tmpEvImport') IS NOT NULL 
		DROP TABLE #tmpEvImport
	IF OBJECT_ID('tempdb..#tmpEvImportEV') IS NOT NULL 
		DROP TABLE #tmpEvImportEV
	IF OBJECT_ID('tempdb..#tmpNewReg') IS NOT NULL 
		DROP TABLE #tmpNewReg

	IF OBJECT_ID('tempdb..##tmpRegImport') IS NOT NULL 
		DROP TABLE ##tmpRegImport

	IF OBJECT_ID('tempdb..#tmpRegImport') IS NOT NULL 
		DROP TABLE #tmpRegImport

  EXEC membercentral.dbo.cache_perms_setStatus @orgID, 'enabled'

SET NOCOUNT OFF