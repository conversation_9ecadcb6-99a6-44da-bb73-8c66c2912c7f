/*
=============================================================================
MCENT-1234 - Sponsor Grouping Functionality
=============================================================================
Description: Adds sponsor grouping functionality to the Events module
Author: Nitin Yadav
Date: December 2023
=============================================================================
*/

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

BEGIN TRANSACTION

BEGIN TRY

    PRINT '-- Starting migration for MCENT-1234 - Sponsor Grouping Functionality'
    PRINT '-- Creating schema changes'

    /*
    =============================================================================
    SCHEMA CHANGES
    =============================================================================
    */

    -- Check if sponsorGroupings table exists
    IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sponsorGroupings]') AND type in (N'U'))
    BEGIN
        PRINT '-- Creating sponsorGroupings table'
        
        CREATE TABLE [dbo].[sponsorGroupings](
            [sponsorGroupingID] [int] IDENTITY(1,1) NOT NULL,
            [eventID] [int] NOT NULL,
            [sponsorGrouping] [nvarchar](200) NOT NULL,
            [sponsorGroupingOrder] [int] NOT NULL DEFAULT(0),
            [dateCreated] [datetime] NOT NULL DEFAULT(GETDATE()),
            [dateUpdated] [datetime] NOT NULL DEFAULT(GETDATE()),
            CONSTRAINT [PK_sponsorGroupings] PRIMARY KEY CLUSTERED 
            (
                [sponsorGroupingID] ASC
            )
        )
        
        -- Add foreign key constraint
        ALTER TABLE [dbo].[sponsorGroupings] WITH CHECK ADD CONSTRAINT [FK_sponsorGroupings_events] 
        FOREIGN KEY([eventID]) REFERENCES [dbo].[events] ([eventID])
        ON DELETE CASCADE
        
        -- Create index for faster lookups
        CREATE NONCLUSTERED INDEX [IX_sponsorGroupings_eventID] ON [dbo].[sponsorGroupings]
        (
            [eventID] ASC
        )
    END
    ELSE
    BEGIN
        PRINT '-- sponsorGroupings table already exists'
    END

    -- Check if sponsorGroupingID column exists in sponsorsUsage table
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[sponsorsUsage]') AND name = 'sponsorGroupingID')
    BEGIN
        PRINT '-- Adding sponsorGroupingID column to sponsorsUsage table'
        
        ALTER TABLE [dbo].[sponsorsUsage] ADD [sponsorGroupingID] [int] NULL
        
        -- Add foreign key constraint
        ALTER TABLE [dbo].[sponsorsUsage] WITH CHECK ADD CONSTRAINT [FK_sponsorsUsage_sponsorGroupings] 
        FOREIGN KEY([sponsorGroupingID]) REFERENCES [dbo].[sponsorGroupings] ([sponsorGroupingID])
        
        -- Create index for faster lookups
        CREATE NONCLUSTERED INDEX [IX_sponsorsUsage_sponsorGroupingID] ON [dbo].[sponsorsUsage]
        (
            [sponsorGroupingID] ASC
        )
    END
    ELSE
    BEGIN
        PRINT '-- sponsorGroupingID column already exists in sponsorsUsage table'
    END

    /*
    =============================================================================
    STORED PROCEDURES
    =============================================================================
    */

    PRINT '-- Creating/updating stored procedures'

    -- 1. Create sponsor grouping
    IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sponsors_createSponsorGrouping]') AND type in (N'P', N'PC'))
        DROP PROCEDURE [dbo].[sponsors_createSponsorGrouping]
    GO

    CREATE PROCEDURE [dbo].[sponsors_createSponsorGrouping]
        @eventID int,
        @sponsorGrouping nvarchar(200)
    AS
    BEGIN
        SET NOCOUNT ON;
        
        DECLARE @maxOrder int = 0
        
        -- Get the maximum order value
        SELECT @maxOrder = ISNULL(MAX(sponsorGroupingOrder), 0)
        FROM [dbo].[sponsorGroupings]
        WHERE eventID = @eventID
        
        -- Insert the new grouping
        INSERT INTO [dbo].[sponsorGroupings]
            (eventID, sponsorGrouping, sponsorGroupingOrder)
        VALUES
            (@eventID, @sponsorGrouping, @maxOrder + 1)
            
        -- Return the new grouping ID
        SELECT SCOPE_IDENTITY() AS sponsorGroupingID
    END
    GO

    -- 2. Update sponsor grouping
    IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sponsors_updateSponsorGrouping]') AND type in (N'P', N'PC'))
        DROP PROCEDURE [dbo].[sponsors_updateSponsorGrouping]
    GO

    CREATE PROCEDURE [dbo].[sponsors_updateSponsorGrouping]
        @sponsorGroupingID int,
        @sponsorGrouping nvarchar(200)
    AS
    BEGIN
        SET NOCOUNT ON;
        
        UPDATE [dbo].[sponsorGroupings]
        SET sponsorGrouping = @sponsorGrouping,
            dateUpdated = GETDATE()
        WHERE sponsorGroupingID = @sponsorGroupingID
    END
    GO

    -- 3. Move sponsor grouping
    IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sponsors_moveSponsorGrouping]') AND type in (N'P', N'PC'))
        DROP PROCEDURE [dbo].[sponsors_moveSponsorGrouping]
    GO

    CREATE PROCEDURE [dbo].[sponsors_moveSponsorGrouping]
        @sponsorGroupingID int,
        @direction varchar(10)
    AS
    BEGIN
        SET NOCOUNT ON;
        
        DECLARE @eventID int
        DECLARE @currentOrder int
        DECLARE @targetOrder int
        DECLARE @targetGroupingID int
        
        -- Get current grouping info
        SELECT @eventID = eventID, @currentOrder = sponsorGroupingOrder
        FROM [dbo].[sponsorGroupings]
        WHERE sponsorGroupingID = @sponsorGroupingID
        
        -- Calculate target order based on direction
        IF @direction = 'up'
            SET @targetOrder = @currentOrder - 1
        ELSE
            SET @targetOrder = @currentOrder + 1
            
        -- Find the grouping at the target position
        SELECT @targetGroupingID = sponsorGroupingID
        FROM [dbo].[sponsorGroupings]
        WHERE eventID = @eventID AND sponsorGroupingOrder = @targetOrder
        
        -- Swap positions if target exists
        IF @targetGroupingID IS NOT NULL
        BEGIN
            BEGIN TRANSACTION
                
                -- Update target grouping order
                UPDATE [dbo].[sponsorGroupings]
                SET sponsorGroupingOrder = @currentOrder,
                    dateUpdated = GETDATE()
                WHERE sponsorGroupingID = @targetGroupingID
                
                -- Update current grouping order
                UPDATE [dbo].[sponsorGroupings]
                SET sponsorGroupingOrder = @targetOrder,
                    dateUpdated = GETDATE()
                WHERE sponsorGroupingID = @sponsorGroupingID
                
            COMMIT TRANSACTION
        END
    END
    GO

    -- 4. Delete sponsor grouping
    IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sponsors_deleteSponsorGrouping]') AND type in (N'P', N'PC'))
        DROP PROCEDURE [dbo].[sponsors_deleteSponsorGrouping]
    GO

    CREATE PROCEDURE [dbo].[sponsors_deleteSponsorGrouping]
        @sponsorGroupingID int
    AS
    BEGIN
        SET NOCOUNT ON;
        
        -- Check if grouping has sponsors
        IF EXISTS (SELECT 1 FROM [dbo].[sponsorsUsage] WHERE sponsorGroupingID = @sponsorGroupingID)
        BEGIN
            RAISERROR('Cannot delete grouping with assigned sponsors', 16, 1)
            RETURN
        END
        
        DECLARE @eventID int
        DECLARE @currentOrder int
        
        -- Get current grouping info
        SELECT @eventID = eventID, @currentOrder = sponsorGroupingOrder
        FROM [dbo].[sponsorGroupings]
        WHERE sponsorGroupingID = @sponsorGroupingID
        
        BEGIN TRANSACTION
            
            -- Delete the grouping
            DELETE FROM [dbo].[sponsorGroupings]
            WHERE sponsorGroupingID = @sponsorGroupingID
            
            -- Reorder remaining groupings
            UPDATE [dbo].[sponsorGroupings]
            SET sponsorGroupingOrder = sponsorGroupingOrder - 1,
                dateUpdated = GETDATE()
            WHERE eventID = @eventID
            AND sponsorGroupingOrder > @currentOrder
            
        COMMIT TRANSACTION
    END
    GO

    -- 5. Move sponsor within group
    IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sponsors_moveSponsorWithinGroup]') AND type in (N'P', N'PC'))
        DROP PROCEDURE [dbo].[sponsors_moveSponsorWithinGroup]
    GO

    CREATE PROCEDURE [dbo].[sponsors_moveSponsorWithinGroup]
        @sponsorUsageID int,
        @sponsorGroupingID int,
        @direction varchar(10),
        @referenceType varchar(50),
        @referenceID int
    AS
    BEGIN
        SET NOCOUNT ON;
        
        DECLARE @currentOrder int
        DECLARE @targetOrder int
        DECLARE @targetUsageID int
        
        -- Get current sponsor order
        SELECT @currentOrder = sponsorOrder
        FROM [dbo].[sponsorsUsage]
        WHERE sponsorUsageID = @sponsorUsageID
        
        -- Calculate target order based on direction
        IF @direction = 'up'
            SET @targetOrder = @currentOrder - 1
        ELSE
            SET @targetOrder = @currentOrder + 1
            
        -- Find the sponsor at the target position within the same group
        SELECT @targetUsageID = sponsorUsageID
        FROM [dbo].[sponsorsUsage]
        WHERE referenceType = @referenceType
        AND referenceID = @referenceID
        AND sponsorOrder = @targetOrder
        AND ISNULL(sponsorGroupingID, 0) = ISNULL(@sponsorGroupingID, 0)
        
        -- Swap positions if target exists
        IF @targetUsageID IS NOT NULL
        BEGIN
            BEGIN TRANSACTION
                
                -- Update target sponsor order
                UPDATE [dbo].[sponsorsUsage]
                SET sponsorOrder = @currentOrder
                WHERE sponsorUsageID = @targetUsageID
                
                -- Update current sponsor order
                UPDATE [dbo].[sponsorsUsage]
                SET sponsorOrder = @targetOrder
                WHERE sponsorUsageID = @sponsorUsageID
                
            COMMIT TRANSACTION
        END
    END
    GO

    -- 6. Enhanced get sponsors by reference ID
    IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sponsors_getSponsorsByReferenceIDFull]') AND type in (N'P', N'PC'))
        DROP PROCEDURE [dbo].[sponsors_getSponsorsByReferenceIDFull]
    GO

    CREATE PROCEDURE [dbo].[sponsors_getSponsorsByReferenceIDFull]
        @siteID int,
        @referenceType varchar(50),
        @referenceID int
    AS
    BEGIN
        SET NOCOUNT ON;
        
        SELECT 
            su.sponsorUsageID,
            s.sponsorID,
            s.sponsorName,
            su.sponsorOrder,
            sg.sponsorGroupingID,
            sg.sponsorGrouping,
            sg.sponsorGroupingOrder
        FROM [dbo].[sponsorsUsage] su
        INNER JOIN [dbo].[sponsors] s ON s.sponsorID = su.sponsorID
        LEFT JOIN [dbo].[sponsorGroupings] sg ON sg.sponsorGroupingID = su.sponsorGroupingID
        WHERE su.referenceType = @referenceType
        AND su.referenceID = @referenceID
        AND s.siteID = @siteID
        ORDER BY 
            ISNULL(sg.sponsorGroupingOrder, 999),
            su.sponsorOrder
    END
    GO

    -- 7. Get sponsor groupings by event ID
    IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sponsors_getSponsorGroupingsByEventID]') AND type in (N'P', N'PC'))
        DROP PROCEDURE [dbo].[sponsors_getSponsorGroupingsByEventID]
    GO

    CREATE PROCEDURE [dbo].[sponsors_getSponsorGroupingsByEventID]
        @eventID int
    AS
    BEGIN
        SET NOCOUNT ON;
        
        SELECT 
            sponsorGroupingID,
            sponsorGrouping,
            sponsorGroupingOrder
        FROM [dbo].[sponsorGroupings]
        WHERE eventID = @eventID
        ORDER BY sponsorGroupingOrder
    END
    GO

    /*
    =============================================================================
    AJAX COMPONENT REGISTRATION
    =============================================================================
    */

    PRINT '-- Registering AJAX components'

    -- Check if AJAX components are already registered
    IF NOT EXISTS (SELECT 1 FROM dbo.ajax_components WHERE component_name = 'SPONSORS' AND method_name = 'createSponsorGrouping')
    BEGIN
        INSERT INTO dbo.ajax_components (component_name, method_name, component_path, is_secure, date_created)
        VALUES ('SPONSORS', 'createSponsorGrouping', 'model.admin.common.modules.sponsors.sponsors', 1, GETDATE())
    END

    IF NOT EXISTS (SELECT 1 FROM dbo.ajax_components WHERE component_name = 'SPONSORS' AND method_name = 'updateSponsorGrouping')
    BEGIN
        INSERT INTO dbo.ajax_components (component_name, method_name, component_path, is_secure, date_created)
        VALUES ('SPONSORS', 'updateSponsorGrouping', 'model.admin.common.modules.sponsors.sponsors', 1, GETDATE())
    END

    IF NOT EXISTS (SELECT 1 FROM dbo.ajax_components WHERE component_name = 'SPONSORS' AND method_name = 'moveSponsorGrouping')
    BEGIN
        INSERT INTO dbo.ajax_components (component_name, method_name, component_path, is_secure, date_created)
        VALUES ('SPONSORS', 'moveSponsorGrouping', 'model.admin.common.modules.sponsors.sponsors', 1, GETDATE())
    END

    IF NOT EXISTS (SELECT 1 FROM dbo.ajax_components WHERE component_name = 'SPONSORS' AND method_name = 'deleteSponsorGrouping')
    BEGIN
        INSERT INTO dbo.ajax_components (component_name, method_name, component_path, is_secure, date_created)
        VALUES ('SPONSORS', 'deleteSponsorGrouping', 'model.admin.common.modules.sponsors.sponsors', 1, GETDATE())
    END

    IF NOT EXISTS (SELECT 1 FROM dbo.ajax_components WHERE component_name = 'SPONSORS' AND method_name = 'moveSponsorWithinGroup')
    BEGIN
        INSERT INTO dbo.ajax_components (component_name, method_name, component_path, is_secure, date_created)
        VALUES ('SPONSORS', 'moveSponsorWithinGroup', 'model.admin.common.modules.sponsors.sponsors', 1, GETDATE())
    END

    PRINT '-- Migration completed successfully'
    COMMIT TRANSACTION

END TRY
BEGIN CATCH
    PRINT '-- Error occurred during migration:'
    PRINT ERROR_MESSAGE()
    
    IF @@TRANCOUNT > 0
        ROLLBACK TRANSACTION
        
    /*
    =============================================================================
    ROLLBACK PROCEDURES
    =============================================================================
    */
    
    PRINT '-- Executing rollback procedures'

    -- Remove AJAX component registrations
    DELETE FROM dbo.ajax_components WHERE component_name = 'SPONSORS' AND method_name IN (
        'createSponsorGrouping', 'updateSponsorGrouping', 'moveSponsorGrouping',
        'deleteSponsorGrouping', 'moveSponsorWithinGroup'
    )

    -- Drop the new stored procedures
    IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sponsors_createSponsorGrouping]') AND type in (N'P', N'PC'))
        DROP PROCEDURE [dbo].[sponsors_createSponsorGrouping]
        
    IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sponsors_updateSponsorGrouping]') AND type in (N'P', N'PC'))
        DROP PROCEDURE [dbo].[sponsors_updateSponsorGrouping]
        
    IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sponsors_moveSponsorGrouping]') AND type in (N'P', N'PC'))
        DROP PROCEDURE [dbo].[sponsors_moveSponsorGrouping]
        
    IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sponsors_deleteSponsorGrouping]') AND type in (N'P', N'PC'))
        DROP PROCEDURE [dbo].[sponsors_deleteSponsorGrouping]
        
    IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sponsors_moveSponsorWithinGroup]') AND type in (N'P', N'PC'))
        DROP PROCEDURE [dbo].[sponsors_moveSponsorWithinGroup]
        
    IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sponsors_getSponsorsByReferenceIDFull]') AND type in (N'P', N'PC'))
        DROP PROCEDURE [dbo].[sponsors_getSponsorsByReferenceIDFull]
        
    IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sponsors_getSponsorGroupingsByEventID]') AND type in (N'P', N'PC'))
        DROP PROCEDURE [dbo].[sponsors_getSponsorGroupingsByEventID]
    
    -- Remove foreign key constraint if it exists
    IF EXISTS (SELECT * FROM sys.foreign_keys WHERE object_id = OBJECT_ID(N'[dbo].[FK_sponsorsUsage_sponsorGroupings]') AND parent_object_id = OBJECT_ID(N'[dbo].[sponsorsUsage]'))
        ALTER TABLE [dbo].[sponsorsUsage] DROP CONSTRAINT [FK_sponsorsUsage_sponsorGroupings]
    
    -- Remove sponsorGroupingID column from sponsorsUsage if it exists
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[sponsorsUsage]') AND name = 'sponsorGroupingID')
        ALTER TABLE [dbo].[sponsorsUsage] DROP COLUMN [sponsorGroupingID]
    
    -- Drop sponsorGroupings table if it exists
    IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sponsorGroupings]') AND type in (N'U'))
        DROP TABLE [dbo].[sponsorGroupings]
    
    PRINT '-- Rollback completed'
END CATCH
GO
