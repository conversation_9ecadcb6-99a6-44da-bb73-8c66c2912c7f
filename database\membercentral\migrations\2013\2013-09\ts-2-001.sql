USE [memberCentral]
GO

declare 
	@classID int, 
	@resourceTypeID int, 
	@functionID int, 
	@applicationTypeID int,
	@applicationTypeName varchar(50), 
	@applicationTypeDesc varchar(50), 
	@suggestedPageName varchar(50),
	@maxInstancesPerSite int, 
	@allowPageNameChange bit, 
	@addToSuperUserRole bit, 
	@addToSiteAdminRole bit,
	@isHidden bit

declare 
	@rc int, 
	@siteAdminRoleID int, 
	@superAdminRoleID int, 
	@ResourceTypeFunctionID int

set @applicationTypeName = 'searchReport';
set @applicationTypeDesc = 'Search Summary';
set @suggestedPageName = 'searchReport';
set @maxInstancesPerSite = 1;
set @allowPageNameChange = 1;
set @addToSuperUserRole = 1
set @addToSiteAdminRole = 0
set @isHidden = 0

select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')
select @siteAdminRoleID = dbo.fn_getResourceRoleID('Site Administrator')

select @classID = dbo.fn_getResourceTypeClassID('application');
BEGIN TRAN
	EXEC @rc = cms_createSiteResourceType
		@resourceTypeClassID=@classID,
		@resourceType=@applicationTypeName,
		@resourceTypeID=@resourceTypeID OUTPUT;
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = cms_createApplicationType 
		@resourceTypeID=@resourceTypeID,
		@maxInstancesPerSite=1,
		@allowPageNameChange=0,
		@applicationTypeName=@applicationTypeName,
		@applicationTypeDesc=@applicationTypeDesc,
		@suggestedPageName=@suggestedPageName,
		@settingsXML = '<settings />',
		@isHidden=@isHidden,
		@applicationTypeID=@applicationTypeID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = cms_createSiteResourceFunction
		@resourceTypeID=@resourceTypeID,
		@functionName='View',
		@displayName='View',
		@functionID=@functionID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);
				IF @@ERROR <> 0 GOTO on_error

	IF (@addToSuperUserRole = 1)
		exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@superAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	IF (@addToSiteAdminRole = 1)
		exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@siteAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error



IF @@TRANCOUNT > 0 COMMIT TRAN


-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
GO


declare 
	@siteID	int,
	@siteCode	varchar(10),
	@languageID int,
	@sectionID int,
	@isVisible bit,
	@pageName varchar(50), 
	@pageTitle varchar(200),
	@pagedesc varchar(400),
	@zoneID int,
	@pageTemplateID int,
	@pageModeID int,
	@pgResourceTypeID int,
	@allowReturnAfterLogin bit,
	@applicationInstanceName varchar(100),
	@applicationInstanceDesc varchar(200),	      	
	@applicationInstanceID int, 
	@siteResourceID int, 
	@pageID int,
	@defaultLanguageID int,
	@defaultTimeZoneID int,
	@rc int,
	@resourceRightID int,
	@inheritedRightsResourceID int,
	@applicationTypeID int

declare @sitesTbl table (siteID int)

SELECT @siteID 	= 2
SELECT @languageID	= 1
SELECT @isVisible = 1
SELECT @pageName	= 'searchReport'
SELECT @pageTitle	= 'Search Summary'
SELECT @pagedesc	= 'Search Summary'
SELECT @zoneID	= dbo.fn_getZoneID('Main')
SELECT @pageTemplateID	= NULL
SELECT @pageModeID	= 2
SELECT @allowReturnAfterLogin	= 1
SELECT @applicationInstanceName	= 'Search Summary'
SELECT @applicationInstanceDesc	= 'Search Summary'
SELECT @sectionID	= 2
SELECT @pgResourceTypeID	= dbo.fn_getResourceTypeId('ApplicationCreatedPage')	
select @applicationTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'searchReport'

insert into @sitesTbl
select 
	s.siteID 
from 
	openQuery(tlasites, 'SELECT distinct siteID FROM search.dbo.tblSearchBuckets where siteID is not null and bucketTypeID = 5 order by siteID') AS a
	inner join dbo.sites s on
		s.siteID =  a.siteID
		and s.siteID in (6,8)

select @siteID = min(siteID) from @sitesTbl

while @siteID is not null
begin

	select 
		@siteCode = siteCode
	from 
		dbo.sites
	where
		siteID = @siteID

	select @sectionID = sectionID from dbo.cms_pageSections where sectionName = 'Root' and parentSectionID is null and siteID = @siteID	

	print 'exec rc = dbo.cms_createApplicationInstance '
	print '@siteid	= ' + isNull(cast (@siteid as varchar),'null') 
	print '@languageID	= ' + isNull(cast (@languageID as varchar),'null') 							
	print '@sectionID	= ' + isNull(cast (@sectionID as varchar),'null') 	
	print '@applicationTypeID	= ' + isNull(cast (@applicationTypeID as varchar),'null') 	
	print '@isVisible	= ' + isNull(cast (@isVisible as varchar),'null') 
	print '@pageName	= ' + isNull(@pageName,'null')
	print '@pageTitle	= ' + isNull(@pageTitle,'null')
	print '@pagedesc	= ' + isNull(@pagedesc,'null')
	print '@zoneID	= ' + isNull(cast (@zoneID as varchar),'null') 
	print '@pageTemplateID	= ' + isNull(cast (@pageTemplateID as varchar),'null') 
	print '@pgParentResourceID	=  NULL'
	print '@pageModeID	= ' + isNull(cast (@pageModeID as varchar),'null') 						
	print '@pgResourceTypeID	= ' + isNull(cast (@pgResourceTypeID as varchar),'null') 	
	print '@allowReturnAfterLogin	= ' + isNull(cast (@allowReturnAfterLogin as varchar),'null') 
	print '@applicationInstanceName	= ' + isNull(@applicationInstanceName,'null')
	print '@applicationInstanceDesc	= ' + isNull(@applicationInstanceDesc,'null')	
	print '===================================================================================================='							

	exec @rc = dbo.cms_createApplicationInstance 
		@siteid	= @siteid, 
		@languageID	= @languageID,							
		@sectionID	= @sectionID, 
		@applicationTypeID = @applicationTypeID,		
		@isVisible	= @isVisible,
		@pageName	= @pageName,
		@pageTitle	= @pageTitle,
		@pagedesc	= @pagedesc,
		@zoneID	= @zoneID,
		@pageTemplateID	= @pageTemplateID,
		@pageModeID	= @pageModeID,							
		@pgResourceTypeID =	@pgResourceTypeID,		
		@pgParentResourceID = NULL,
		@allowReturnAfterLogin = @allowReturnAfterLogin,
		@applicationInstanceName = @applicationInstanceName,
		@applicationInstanceDesc = @applicationInstanceDesc,						
		@applicationInstanceID = @applicationInstanceID output,
		@siteResourceID	= @siteResourceID output,
		@pageID	= @pageID output	
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	
	-- permission here
	SELECT @inheritedRightsResourceID = null

	SELECT @inheritedRightsResourceID = ai.siteResourceID
		from dbo.cms_applicationInstances as ai
		where ai.siteID = @siteid
		and ai.applicationTypeID = 9

	IF @inheritedRightsResourceID is not null BEGIN

		print 'exec rc = dbo.cms_createSiteResourceRight '
		print '@siteid	= ' + isNull(cast (@siteid as varchar),'null') 
		print '@siteResourceID	= ' + isNull(cast (@siteResourceID as varchar),'null') 							
		print '@include	= 1' 
		print '@functionID	= 4' 
		print '@roleID	= null'  
		print '@groupID = null' 
		print '@memberID	= null'
		print '@inheritedRightsResourceID	= ' + isNull(cast (@inheritedRightsResourceID as varchar),'null') 	
		print '@inheritedRightsFunctionID	=  4'
		print '*****************************************************************************************'		

		EXEC dbo.cms_createSiteResourceRight 
			@siteID=@siteid, 
			@siteResourceID=@siteResourceID, 
			@include=1, 
			@functionID=4, 
			@roleID=null, 
			@groupID=null, 
			@memberID=null, 
			@inheritedRightsResourceID=@inheritedRightsResourceID, 
			@inheritedRightsFunctionID=4, 
			@resourceRightID=@resourceRightID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	END

	select @siteID = min(siteID) from @sitesTbl where siteID > @siteID

end	-- while @siteID is not null

-- error exit
on_error:
		

set nocount off
GO

