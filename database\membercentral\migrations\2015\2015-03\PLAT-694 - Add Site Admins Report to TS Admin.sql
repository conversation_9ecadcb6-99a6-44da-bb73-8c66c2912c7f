use customApps
GO

CREATE PROC dbo.ts_SiteAdminsReport
@filename varchar(400)

AS

declare @fullsql varchar(max)
select @fullsql = '
	select m.firstname, m.lastname, o.orgcode, o.orgName, me.email, ''MEMBERCENTRAL'' as [Platform]
	from membercentral.dbo.ams_groups as g
	inner join membercentral.dbo.organizations as o on o.orgID = g.orgID
	inner join membercentral.dbo.cache_members_groups as mg on mg.groupID = g.groupID
	inner join membercentral.dbo.ams_members as m on m.memberID = mg.memberID
	inner join membercentral.dbo.ams_memberEmails as me on me.memberID = m.memberID
	inner join membercentral.dbo.ams_memberEmailTypes as met on met.emailTypeID = me.emailTypeID and met.emailTypeOrder = 1
	inner join tlasites.trialsmith.dbo.depotla as tla on tla.state = o.orgcode
	where g.groupCode = ''SiteAdmins''
	and g.isSystemGroup = 1
	and m.status = ''A''
	and tla.isLiveonnewplatform = 1
	and isnull(me.email,'''') <> ''''
		union all
	select o.firstname, o.lastname, tla.state as orgcode, tla.description as orgName, o.email, ''TLASITES'' as [Platform]
	from tlasites.trialsmith.dbo.depotla as tla
	inner join tlasites.trialsmith.dbo.orgmemberdata as o on tla.state = o.orgcode
	where tla.isLiveonnewplatform = 0
	and tla.isPortalLaunched = ''Y''
	and o.status = ''A''
	and o.isAdmin = 1
	and isnull(o.email,'''') <> ''''
	order by [platform], orgcode, lastname, firstname'

-- export data and return fields
EXEC membercentral.dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql

RETURN 0
GO