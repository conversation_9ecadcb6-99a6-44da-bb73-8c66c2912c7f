-- Updated script also set the graceEndDate the [sub_expireSubscriptions] uses this date as well.

declare @subscriberIDs TABLE (subscriberID int PRIMARY KEY, statusHistoryID int,oldStatusID int)

insert into @subscriberIDs (subscriberID,statusHistoryID,oldStatusID)
select s.subscriberID, sh.statusHistoryID, sh.oldStatusID
from dbo.sub_statusHistory as sh
inner join dbo.sub_subscribers as s on s.subscriberID = sh.subscriberID
where sh.updatedate between '5/17/2014' and '5/17/2014 1AM'
and sh.enteredByMemberID = 461530
and s.subscriptionID = 4723
and sh.statusID = 6
and sh.oldStatusID = 1

insert into @subscriberIDs (subscriberID,statusHistoryID,oldStatusID)
select s.subscriberID, sh.statusHistoryID, sh.oldStatusID
from dbo.sub_statusHistory as sh
inner join dbo.sub_subscribers as s on s.subscriberID = sh.subscriberID
inner join @subscriberIDs as tmp on tmp.subscriberID = s.rootSubscriberID
where sh.updatedate between '5/17/2014' and '5/17/2014 1AM'
and sh.enteredByMemberID = 461530
and sh.statusID = 6
and sh.oldStatusID = 1
and not exists (select subscriberID from @subscriberIDs where subscriberID = s.subscriberID)

select distinct m.memberid, m.lastname, m.firstname, m.membernumber
from @subscriberIDs as tmp
inner join dbo.sub_subscribers as s on s.subscriberID = tmp.subscriberID
inner join dbo.ams_members as m on m.memberid = s.memberid
inner join dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
order by 2, 3, 4

select m.memberid, m.lastname, m.firstname, m.membernumber, s.subscriberID, s.subscriptionID, s.subStartDate, s.subEndDate, sub.subscriptionName
from @subscriberIDs as tmp
inner join dbo.sub_subscribers as s on s.subscriberID = tmp.subscriberID
inner join dbo.ams_members as m on m.memberid = s.memberid
inner join dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
order by 2, 3, 4, s.subscriberPath

update ss 
set	statusID = sh.oldStatusID, subEndDate = '6/30/2014 23:59:59', graceEndDate = '6/30/2014 23:59:59'
from @subscriberIDs temp
inner join sub_subscribers ss on ss.subscriberID = temp.subscriberID
inner join sub_statusHistory sh on sh.statusHistoryID = temp.statusHistoryID

delete sh
from @subscriberIDs temp
inner join sub_statusHistory sh on sh.statusHistoryID = temp.statusHistoryID

update sub_subscribers
set paymentStatusID = 2
where statusID = 5
and paymentStatusID = 1
and subStartDate = '7/1/2014'

delete sh
from dbo.sub_paymentStatusHistory as sh
inner join (select subscriberID from sub_subscribers where statusID = 5 and paymentStatusID = 1 and subStartDate = '7/1/2014') as tmp on tmp.subscriberID = sh.subscriberID
where sh.paymentStatusID = 1

exec dbo.sub_fixGroups 154


declare @itemGroupUID uniqueidentifier
EXEC platformQueue.dbo.queue_processMemberGroups_insert 147, '', '', 1, @itemGroupUID OUTPUT
