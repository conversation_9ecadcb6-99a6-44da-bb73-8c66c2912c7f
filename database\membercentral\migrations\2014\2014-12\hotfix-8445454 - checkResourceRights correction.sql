use membercentral;
GO
ALTER FUNCTION [dbo].[fn_checkResourceRights] (
	@resourceid int,
	@functionID int,
	@memberID int,
	@siteID int
)
RETURNS bit
AS
BEGIN

	DECLARE @allowAccess bit
	DECLARE @activeMemberID int
	declare @siteSiteResourceID int, @resourceTypeID int

	SELECT @allowAccess = 0
	select @activeMemberID = activeMemberID from ams_members where memberID = @memberID 
	select @activeMemberID = isnull(@activeMemberID,0)

	-- if superuser and not on MC site, return TRUE
	IF EXISTS (
		SELECT mnp.mnpID
		FROM dbo.ams_memberNetworkProfiles AS mnp (NOLOCK)
		INNER JOIN dbo.ams_networkProfiles AS np (NOLOCK) ON mnp.profileID = np.profileID 
		INNER JOIN dbo.ams_members AS m (NOLOCK) ON mnp.memberID = m.memberID and m.memberID = m.activeMemberID
		WHERE m.memberID = @activeMemberID 
		AND np.networkID = 1
		AND mnp.status = 'A'
		AND np.status = 'A'
		AND m.status = 'A'
	) AND NOT EXISTS (SELECT siteID from sites where siteID = @siteID and sitecode='MC')
		SELECT @allowAccess = 1

	-- if siteadmin for site, return TRUE
	IF @allowAccess = 0 BEGIN
		IF EXISTS (
			select memberID
			from dbo.cache_members_groups as mg
			inner join dbo.ams_groups as g on g.groupID = mg.groupID
			where mg.memberID = @activeMemberID
			AND g.groupName = 'Site Administrators'
			AND g.status = 'A'
			and g.isSystemGroup = 1
		)
		SELECT @allowAccess = 1
	END

	-- else do normal check
	IF @allowAccess = 0 BEGIN
		
		select @siteSiteResourceID=s.siteResourceID, @resourceTypeID=sr.resourceTypeID
		from cms_siteResources sr
		inner join sites s on s.siteID = sr.siteID
		and sr.siteResourceID = @resourceid

		SELECT @allowAccess =
			CASE 
				WHEN EXISTS (
						select cachedRightsID
						from dbo.cms_siteResourceRightsCache srrc
						inner join dbo.cache_members_groups mg
							on srrc.groupID = mg.groupID
							and mg.memberID = @activeMemberID
							and srrc.resourceID = @resourceID
							and srrc.functionID = @functionID
							and srrc.include = 1
						union
						--universal roles
						select cachedRightsID
						from dbo.cms_siteResourceRightsCache srrc
						inner join dbo.cache_members_groups mg
							on srrc.groupID = mg.groupID
							and mg.memberID = @activeMemberID
							and srrc.resourceID = @siteSiteResourceID
							and srrc.universalRoleResourceTypeID = @resourceTypeID
							and srrc.functionID = @functionID
							and srrc.include = 1

					) and not exists (
						select cachedRightsID
						from dbo.cms_siteResourceRightsCache srrc
						inner join dbo.cache_members_groups mg
							on srrc.groupID = mg.groupID
							and mg.memberID = @activeMemberID
							and srrc.resourceID = @resourceID
							and srrc.functionID = @functionID
							and srrc.include = 0

						union
						--universal roles
						select cachedRightsID
						from dbo.cms_siteResourceRightsCache srrc
						inner join dbo.cache_members_groups mg
							on srrc.groupID = mg.groupID
							and mg.memberID = @activeMemberID
							and srrc.resourceID = @siteSiteResourceID
							and srrc.universalRoleResourceTypeID = @resourceTypeID
							and srrc.functionID = @functionID
							and srrc.include = 0
					)
					THEN 1
			ELSE 0
			END
	END

	RETURN @allowAccess
END
GO
