ALTER PROC [dbo].[tr_viewTransaction_related]
@transactionID int

AS

declare @tblTrans TABLE (transactionID int)
insert into @tblTrans (transactionID)
select transactionID
from dbo.tr_relationships
where appliedToTransactionID = @transactionID
	union
select appliedToTransactionID
from dbo.tr_relationships
where transactionID = @transactionID

declare @typeID int
select @typeID = typeID from dbo.tr_transactions where transactionID = @transactionID

select t.transactionID, tt.type, t.transactionDate, t.amount, 
	case 
	when t.typeID = 3 and gl.glCode = 'ACCOUNTSRECEIVABLE' then 'Negative Adjustment of ' + isnull(t.detail,'')
	when t.typeID = 3 and gl.glCode <> 'ACCOUNTSRECEIVABLE' then 'Adjustment of ' + isnull(t.detail,'')
	when t.typeID = 6 and gl.glCode = 'ACCOUNTSRECEIVABLE' then 'WriteOff of ' + isnull(t.detail,'')
	when t.typeID = 6 and gl.glCode <> 'ACCOUNTSRECEIVABLE' then 'Negative WriteOff of ' + isnull(t.detail,'')
	when t.typeID = 9 then 'NSF of ' + isnull(t.detail,'')
	when t.typeID = 5 and gl.glCode = 'ACCOUNTSRECEIVABLE' then 'Allocation of Payment'
	when t.typeID = 5 and gl.glCode <> 'ACCOUNTSRECEIVABLE' then 'Deallocation of Payment'
	when t.typeID = 8 and tVoidee.typeID = 3 and glVoidee.glCode = 'ACCOUNTSRECEIVABLE' then 'VOID of Negative Adjustment of ' + isnull(tVoidee.detail,'')
	when t.typeID = 8 and tVoidee.typeID = 3 and glVoidee.glCode <> 'ACCOUNTSRECEIVABLE' then 'VOID of Adjustment of ' + isnull(tVoidee.detail,'')
	when t.typeID = 8 and tVoidee.typeID = 6 and glVoidee.glCode = 'ACCOUNTSRECEIVABLE' then 'VOID of WriteOff of ' + isnull(tVoidee.detail,'')
	when t.typeID = 8 and tVoidee.typeID = 6 and glVoidee.glCode <> 'ACCOUNTSRECEIVABLE' then 'VOID of Negative WriteOff of ' + isnull(tVoidee.detail,'')
	when t.typeID = 8 and tVoidee.typeID = 9 then 'VOID of NSF of ' + isnull(tVoidee.detail,'')
	when t.typeID = 8 and tVoidee.typeID = 5 and glVoidee.glCode = 'ACCOUNTSRECEIVABLE' then 'VOID of Allocation of Payment'
	when t.typeID = 8 and tVoidee.typeID = 5 and glVoidee.glCode <> 'ACCOUNTSRECEIVABLE' then 'VOID of Deallocation of Payment'
	when t.typeID = 8 then 'VOID of ' + isnull(t.detail,'')
	else isnull(t.detail,'') 
	end as detail, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	case 
	when t.typeID = 5 and @typeID = 2 then isnull(tSale.detail,'')
	when t.typeID = 5 and @typeID <> 2 then isnull(tPay.detail,'')
	when t.typeID = 8 and tVoidee.typeID = 5 then isnull(tVoideeSale.detail,'')
	else null
	end as allocDetail,
	case 
	when t.typeID = 5 and @typeID = 2 then tSale.transactionID
	when t.typeID = 5 and @typeID <> 2 then tPay.transactionID
	when t.typeID = 8 and tVoidee.typeID = 5 then tVoideeSale.transactionID
	else null
	end as allocDetailTransactionID,
	case 
	when t.typeID = 5 and @typeID = 2 then mAss2Sale.firstname + isnull(' ' + nullif(mAss2Sale.middlename,''),'') + ' ' + mAss2Sale.lastname + isnull(' ' + nullif(mAss2Sale.suffix,''),'') + ' (' + mAss2Sale.membernumber + ')'
	when t.typeID = 5 and @typeID <> 2 then mAss2Pay.firstname + isnull(' ' + nullif(mAss2Pay.middlename,''),'') + ' ' + mAss2Pay.lastname + isnull(' ' + nullif(mAss2Pay.suffix,''),'') + ' (' + mAss2Pay.membernumber + ')'
	when t.typeID = 8 and tVoidee.typeID = 5 then mAss2VoideeSale.firstname + isnull(' ' + nullif(mAss2VoideeSale.middlename,''),'') + ' ' + mAss2VoideeSale.lastname + isnull(' ' + nullif(mAss2VoideeSale.suffix,''),'') + ' (' + mAss2VoideeSale.membernumber + ')'
	else null
	end as allocDetailAssignedToMember,
	case 
	when t.typeID = 5 and @typeID = 2 then mAss2Sale.company
	when t.typeID = 5 and @typeID <> 2 then mAss2Pay.company
	when t.typeID = 8 and tVoidee.typeID = 5 then mAss2VoideeSale.company
	else null
	end as allocDetailAssignedToMemberCompany
from dbo.tr_transactions as t
inner join @tblTrans as tbl on tbl.transactionID = t.transactionID
INNER JOIN dbo.tr_types as tt on tt.typeID = t.typeID
INNER JOIN dbo.tr_glAccounts as gl on gl.glaccountID = t.creditGLAccountID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
LEFT OUTER JOIN dbo.tr_relationships AS rAllocPay 
	INNER JOIN dbo.tr_relationshipTypes AS rtAllocPay ON rtAllocPay.typeID = rAllocPay.typeID AND rtAllocPay.type = 'AllocPayTrans'
	INNER JOIN dbo.tr_transactions as tPay on tPay.transactionID = rAllocPay.appliedToTransactionID
	inner join dbo.ams_members as mAssPay on mAssPay.memberid = tPay.assignedToMemberID
	inner join dbo.ams_members as mAss2Pay on mAss2Pay.memberID = mAssPay.activeMemberID
	on rAllocPay.transactionID = t.transactionID and t.typeID = 5
LEFT OUTER JOIN dbo.tr_relationships AS rAllocSale 
	INNER JOIN dbo.tr_relationshipTypes AS rtAllocSale ON rtAllocSale.typeID = rAllocSale.typeID AND rtAllocSale.type = 'AllocSaleTrans'
	INNER JOIN dbo.tr_transactions as tSale on tSale.transactionID = rAllocSale.appliedToTransactionID
	inner join dbo.ams_members as mAssSale on mAssSale.memberid = tSale.assignedToMemberID
	inner join dbo.ams_members as mAss2Sale on mAss2Sale.memberID = mAssSale.activeMemberID
	on rAllocSale.transactionID = t.transactionID and t.typeID = 5
LEFT OUTER JOIN dbo.tr_relationships AS rVoid
	INNER JOIN dbo.tr_relationshipTypes AS rtVoid ON rtVoid.typeID = rVoid.typeID AND rtVoid.type = 'OffsetTrans'
	INNER JOIN dbo.tr_transactions as tVoidee on tVoidee.transactionID = rVoid.appliedToTransactionID
	INNER JOIN dbo.tr_glAccounts as glVoidee on glVoidee.glaccountID = tVoidee.creditGLAccountID
	LEFT OUTER JOIN dbo.tr_relationships AS rVoideeAllocSale 
		INNER JOIN dbo.tr_relationshipTypes AS rtVoideeAllocSale ON rtVoideeAllocSale.typeID = rVoideeAllocSale.typeID AND rtVoideeAllocSale.type = 'AllocSaleTrans'
		INNER JOIN dbo.tr_transactions as tVoideeSale on tVoideeSale.transactionID = rVoideeAllocSale.appliedToTransactionID
		inner join dbo.ams_members as mAssVoideeSale on mAssVoideeSale.memberid = tVoideeSale.assignedToMemberID
		inner join dbo.ams_members as mAss2VoideeSale on mAss2VoideeSale.memberID = mAssVoideeSale.activeMemberID
		on rVoideeAllocSale.transactionID = tVoidee.transactionID and tVoidee.typeID = 5
	on rVoid.transactionID = t.transactionID and t.typeID = 8
order by t.transactionDate desc, t.transactionID desc

RETURN 0
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'dbo.tr_viewTransaction_related_recursive') AND type in (N'P', N'PC'))
DROP PROCEDURE dbo.tr_viewTransaction_related_recursive
GO
CREATE PROC dbo.tr_viewTransaction_related_recursive
@transactionID int

AS

declare @relatedTrans TABLE (
	transactionID int,
	type varchar(30),
	transactionDate datetime,
	amount money,
	detail varchar(max),
	assignedToMember varchar(max),
	assignedToMemberCompany varchar(400),
	parentTransactionID int
)
declare @tmpRelatedTrans TABLE (
	transactionID int,
	type varchar(30),
	transactionDate datetime,
	amount money,
	detail varchar(max),
	assignedToMember varchar(max),
	assignedToMemberCompany varchar(400),
	allocDetail varchar(max),
	allocDetailTransactionID int,
	allocDetailAssignedToMember varchar(max),
	allocDetailAssignedToMemberCompany varchar(400)
)
declare @tmpTIDs TABLE (transactionID int)
declare @tmpRelatedTIDs TABLE (transactionID int)
declare @tmpProcessedTIDs TABLE (transactionID int)
declare @rootTransactionID int
set @rootTransactionID = @transactionID

insert into @tmpTIDs values (@rootTransactionID)

select @transactionID = min(transactionID) from @tmpTIDs
while @transactionID is not null BEGIN
	insert into @tmpRelatedTrans
	EXEC dbo.tr_viewTransaction_related @transactionID

	insert into @relatedTrans (transactionID, type, transactionDate, amount, detail, assignedToMember, assignedToMemberCompany, parentTransactionID)
	output inserted.transactionid into @tmpRelatedTIDs
	select transactionID, type, transactionDate, amount, detail, assignedToMember, assignedToMemberCompany, @transactionID
	from @tmpRelatedTrans
	where transactionID <> @rootTransactionID
	and transactionID not in (select transactionID from @relatedTrans)

	insert into @tmpTIDs
	select transactionID from @tmpRelatedTIDs

	delete from @tmpRelatedTrans
	delete from @tmpTIDs where transactionID = @transactionID

	insert into @tmpProcessedTIDs values (@transactionID)

	select @transactionID = min(transactionID) from @tmpTIDs where transactionID not in (select transactionID from @tmpProcessedTIDs)
END

;WITH tree AS (
	select transactionID, type, transactionDate, amount, detail, assignedToMember, assignedToMemberCompany, parentTransactionID, CAST(RIGHT(1000+ROW_NUMBER() OVER (ORDER BY transactionDate, transactionID),3) AS varchar(max)) AS bpath
	from @relatedTrans
	where parentTransactionID = @rootTransactionID
		union all
	select tmp.transactionID, tmp.type, tmp.transactionDate, tmp.amount, tmp.detail, tmp.assignedToMember, tmp.assignedToMemberCompany, tmp.parentTransactionID, tree.bpath + '.' + CAST(RIGHT(1000+ROW_NUMBER() OVER (ORDER BY tmp.transactionDate, tmp.transactionID),3) AS varchar(max))
	from @relatedTrans as tmp
	inner join tree on tmp.parentTransactionID = tree.transactionID
)
select * from tree
order by bPath

GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'dbo.tr_viewTransaction_payment') AND type in (N'P', N'PC'))
DROP PROCEDURE dbo.tr_viewTransaction_payment
GO
CREATE PROC [dbo].[tr_viewTransaction_payment]
@transactionID int

AS

set nocount on

declare @orgID int
select @orgID = ownedByOrgID from dbo.tr_transactions where transactionID = @transactionID

declare @allGLs TABLE (GLAccountID int, thePathExpanded varchar(max), accountCode varchar(200))
insert into @allGLS
select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode
from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl

-- transaction info
select TOP 1 t.transactionid, t.ownedByOrgID, t.recordedOnSiteID, tt.type, t.detail, t.amount, 
	t.transactionDate, t.dateRecorded, ts.status, t.assignedTomemberID, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullif(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as recordedByMember,
	m2.company as recordedByMemberCompany,
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL,
	glCred.thePathExpanded + isnull(' (' + nullIf(glCred.accountCode,'') + ')','') as creditGL
from dbo.tr_transactions as t
inner join dbo.tr_types as tt on tt.typeID = t.typeID
inner join dbo.tr_statuses as ts on ts.statusID = t.statusID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join dbo.ams_members as m on m.memberid = t.recordedByMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
inner join @allGLS as glDeb on glDeb.GLAccountID = t.debitGLAccountID
inner join @allGLS as glCred on glCred.GLAccountID = t.creditGLAccountID
where t.transactionID = @transactionID

-- payment info
select top 1 tp.paymentID, tp.profileID, mp.profileName, tp.historyID, tp.cache_allocatedAmountOfPayment as allocatedAmount, 
	tp.cache_refundableAmountOfPayment-tp.cache_allocatedAmountOfPayment as unallocatedAmount,
	b.batchID, b.batchName, b.depositDate, bs.status, ph.datePaid, ph.gatewayTransactionID, ph.paymentInfo, ph.gatewayResponse
from dbo.tr_transactionPayments as tp
inner join dbo.mp_profiles as mp on mp.profileID = tp.profileID
inner join dbo.tr_paymentHistory as ph on ph.historyID = tp.historyID
inner join dbo.tr_batchTransactions as bt on bt.transactionID = tp.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID
inner join dbo.tr_batchStatuses as bs on bs.statusID = b.statusID
where tp.transactionID = @transactionID

-- current allocations
select t.transactionID, atop.allocAmount, case when t.typeID = 3 then 'Adjustment of ' else '' end + atop.detail as detail,  
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	glCred.thePathExpanded + isnull(' (' + nullIf(glCred.accountCode,'') + ')','') as creditGL
from dbo.fn_tr_getAllocatedTransactionsofPayment(@transactionID) as atop
inner join dbo.tr_transactions as t on t.transactionID = atop.transactionID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glCred on glCred.GLAccountID = t.creditGLAccountID
order by atop.allocDate desc

-- related transactions
EXEC dbo.tr_viewTransaction_related_recursive @transactionID

RETURN 0
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'dbo.tr_viewTransaction_sale') AND type in (N'P', N'PC'))
DROP PROCEDURE dbo.tr_viewTransaction_sale
GO
CREATE PROC [dbo].[tr_viewTransaction_sale]
@transactionID int

AS

set nocount on

declare @orgID int
select @orgID = ownedByOrgID from dbo.tr_transactions where transactionID = @transactionID

declare @allGLs TABLE (GLAccountID int, thePathExpanded varchar(max), accountCode varchar(200))
insert into @allGLS
select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode
from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl

-- transaction info
select TOP 1 t.transactionid, t.ownedByOrgID, t.recordedOnSiteID, tt.type, t.detail, t.amount, 
	t.transactionDate, t.dateRecorded, ts.status, t.assignedTomemberID, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullif(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as recordedByMember,
	m2.company as recordedByMemberCompany
from dbo.tr_transactions as t
inner join dbo.tr_types as tt on tt.typeID = t.typeID
inner join dbo.tr_statuses as ts on ts.statusID = t.statusID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join dbo.ams_members as m on m.memberid = t.recordedByMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
where t.transactionID = @transactionID

-- sale info
select top 1 ts.saleID, ts.cache_amountAfterAdjustment as amountAfterAdjustment,
	ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount-ts.cache_pendingPaymentAllocatedAmount as unAllocatedAmount,
	ts.cache_activePaymentAllocatedAmount+ts.cache_pendingPaymentAllocatedAmount as allocatedAmount
from dbo.tr_transactionSales as ts
where ts.transactionID = @transactionID

-- invoices
select i.invoiceID, ins.status, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, i.dateDue, i.invoiceProfileID, ip.profileName, i.invoiceCode
from dbo.tr_transactions as t
inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
inner join dbo.organizations as o on o.orgID = @orgID
where t.transactionID in (
	select @transactionID as transactionID
	union
	select adj.transactionID
	from dbo.tr_transactions as adj
	inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
	inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
)
order by i.dateDue desc, 3 desc

-- current allocations
select t.transactionID, apos.allocatedAmount as allocAmount, t.detail,
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL
from dbo.fn_tr_getAllocatedPaymentsofSale(@transactionID) as apos
inner join dbo.tr_transactions as t on t.transactionID = apos.paymentTransactionID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glDeb on glDeb.GLAccountID = t.debitGLAccountID
order by t.transactionDate desc

-- gl activity
select tmp.amount, tmp.glaccountID, tmp.gltype, gl2.thePathExpanded + isnull(' (' + nullIf(gl2.accountCode,'') + ')','') as glexpanded
from (
	select sum(t.amount) as amount, t.debitGLAccountID as glAccountID, 'debit' as gltype
	from dbo.tr_transactions as t
	where t.transactionID in (
		select @transactionID as transactionID
		union
		select adj.transactionID
		from dbo.tr_transactions as adj
		inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
		inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
	)
	group by t.debitGLAccountID
		union all
	select sum(t.amount) as amount, t.creditGLAccountID as glAccountID, 'credit' as gltype
	from dbo.tr_transactions as t
	where t.transactionID in (
		select @transactionID as transactionID
		union
		select adj.transactionID
		from dbo.tr_transactions as adj
		inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
		inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
	)
	group by t.creditGLAccountID
) as tmp
inner join @allGLS as gl2 on gl2.GLAccountID = tmp.GLAccountID
order by tmp.gltype, glexpanded

-- related transactions
EXEC dbo.tr_viewTransaction_related_recursive @transactionID

RETURN 0
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'dbo.tr_viewTransaction_adjustment') AND type in (N'P', N'PC'))
DROP PROCEDURE dbo.tr_viewTransaction_adjustment
GO
CREATE PROC [dbo].[tr_viewTransaction_adjustment]
@transactionID int

AS

set nocount on

declare @orgID int
select @orgID = ownedByOrgID from dbo.tr_transactions where transactionID = @transactionID

declare @allGLs TABLE (GLAccountID int, thePathExpanded varchar(max), accountCode varchar(200), glCode varchar(30))
insert into @allGLS
select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode, gl.glCode
from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl
inner join dbo.tr_GLAccounts as gl on gl.GLAccountID = rgl.GLAccountID

-- transaction info
select TOP 1 t.transactionid, t.ownedByOrgID, t.recordedOnSiteID, t.amount, 
	case when glCred.glCode = 'ACCOUNTSRECEIVABLE' then 'Negative Adjustment' else 'Adjustment' end as [type],
	case when glCred.glCode = 'ACCOUNTSRECEIVABLE' then 'Negative Adjustment of ' else 'Adjustment of ' end + isnull(t.detail,'') as detail,
	t.transactionDate, t.dateRecorded, ts.status, t.assignedTomemberID, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullif(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as recordedByMember,
	m2.company as recordedByMemberCompany,
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL,
	glCred.thePathExpanded + isnull(' (' + nullIf(glCred.accountCode,'') + ')','') as creditGL
from dbo.tr_transactions as t
inner join dbo.tr_types as tt on tt.typeID = t.typeID
inner join dbo.tr_statuses as ts on ts.statusID = t.statusID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join dbo.ams_members as m on m.memberid = t.recordedByMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
inner join @allGLs as glDeb on glDeb.GLAccountID = t.debitGLAccountID
inner join @allGLs as glCred on glCred.GLAccountID = t.creditGLAccountID
where t.transactionID = @transactionID

-- adjustment info
select top 1 i.invoiceID, ins.status, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, i.dateDue, i.invoiceProfileID, ip.profileName, i.invoiceCode
from dbo.tr_invoiceTransactions as it
inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
inner join dbo.organizations as o on o.orgID = @orgID
where it.transactionID = @transactionID

-- adjusting transaction
select top 1 tSaleTax.transactionID, tt.type, tSaleTax.amount, tSaleTax.detail,
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	glCred.thePathExpanded + isnull(' (' + nullIf(glCred.accountCode,'') + ')','') as creditGL
from dbo.tr_transactions as tSaleTax
inner join dbo.tr_types as tt on tt.typeID = tSaleTax.typeID
inner join dbo.tr_relationships as rAdj on rAdj.appliedToTransactionID = tSaleTax.transactionID
inner join dbo.tr_relationshipTypes as rtAdj on rtAdj.typeID = rAdj.typeID and rtAdj.type = 'AdjustTrans'
inner join dbo.ams_members as mAss on mAss.memberid = tSaleTax.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glCred on glCred.GLAccountID = tSaleTax.creditGLAccountID
where rAdj.transactionID = @transactionID

-- related transactions
EXEC dbo.tr_viewTransaction_related_recursive @transactionID

RETURN 0
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'dbo.tr_viewTransaction_refund') AND type in (N'P', N'PC'))
DROP PROCEDURE dbo.tr_viewTransaction_refund
GO
CREATE PROC [dbo].[tr_viewTransaction_refund]
@transactionID int

AS

set nocount on

declare @orgID int
select @orgID = ownedByOrgID from dbo.tr_transactions where transactionID = @transactionID

-- transaction info
; WITH allGLs AS (
	select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode
	from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl
)
select TOP 1 t.transactionid, t.ownedByOrgID, t.recordedOnSiteID, tt.type, t.detail, t.amount, 
	t.transactionDate, t.dateRecorded, ts.status, t.assignedTomemberID, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullif(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as recordedByMember,
	m2.company as recordedByMemberCompany,
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL,
	glCred.thePathExpanded + isnull(' (' + nullIf(glCred.accountCode,'') + ')','') as creditGL
from dbo.tr_transactions as t
inner join dbo.tr_types as tt on tt.typeID = t.typeID
inner join dbo.tr_statuses as ts on ts.statusID = t.statusID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join dbo.ams_members as m on m.memberid = t.recordedByMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
inner join allGLs as glDeb on glDeb.GLAccountID = t.debitGLAccountID
inner join allGLs as glCred on glCred.GLAccountID = t.creditGLAccountID
where t.transactionID = @transactionID

-- refund info
select tp.paymentID, tp.profileID, mp.profileName, tp.historyID, 
	b.batchID, b.batchName, b.depositDate, bs.status, ph.datePaid, ph.gatewayTransactionID, ph.paymentInfo, ph.gatewayResponse
from dbo.tr_transactionPayments as tp
inner join dbo.mp_profiles as mp on mp.profileID = tp.profileID
inner join dbo.tr_paymentHistory as ph on ph.historyID = tp.historyID
inner join dbo.tr_batchTransactions as bt on bt.transactionID = tp.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID
inner join dbo.tr_batchStatuses as bs on bs.statusID = b.statusID
where tp.transactionID = @transactionID

-- related transactions
EXEC dbo.tr_viewTransaction_related_recursive @transactionID

RETURN 0
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'dbo.tr_viewTransaction_nsf') AND type in (N'P', N'PC'))
DROP PROCEDURE dbo.tr_viewTransaction_nsf
GO
CREATE PROC [dbo].[tr_viewTransaction_nsf]
@transactionID int

AS

set nocount on

declare @orgID int
select @orgID = ownedByOrgID from dbo.tr_transactions where transactionID = @transactionID

-- transaction info
; WITH allGLs AS (
	select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode
	from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl
)
select TOP 1 t.transactionid, t.ownedByOrgID, t.recordedOnSiteID, tt.type, 'NSF of ' + t.detail as detail, t.amount, 
	t.transactionDate, t.dateRecorded, ts.status, t.assignedTomemberID, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullif(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as recordedByMember,
	m2.company as recordedByMemberCompany,
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL,
	glCred.thePathExpanded + isnull(' (' + nullIf(glCred.accountCode,'') + ')','') as creditGL
from dbo.tr_transactions as t
inner join dbo.tr_types as tt on tt.typeID = t.typeID
inner join dbo.tr_statuses as ts on ts.statusID = t.statusID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join dbo.ams_members as m on m.memberid = t.recordedByMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
inner join allGLs as glDeb on glDeb.GLAccountID = t.debitGLAccountID
inner join allGLs as glCred on glCred.GLAccountID = t.creditGLAccountID
where t.transactionID = @transactionID

-- nsf info
select b.batchID, b.batchName, b.depositDate, bs.status
from dbo.tr_batchTransactions as bt
inner join dbo.tr_batches as b on b.batchID = bt.batchID
inner join dbo.tr_batchStatuses as bs on bs.statusID = b.statusID
where bt.transactionID = @transactionID

-- related transactions
EXEC dbo.tr_viewTransaction_related_recursive @transactionID

RETURN 0
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'dbo.tr_viewTransaction_salestax') AND type in (N'P', N'PC'))
DROP PROCEDURE dbo.tr_viewTransaction_salestax
GO
CREATE PROC [dbo].[tr_viewTransaction_salestax]
@transactionID int

AS

set nocount on

declare @orgID int
select @orgID = ownedByOrgID from dbo.tr_transactions where transactionID = @transactionID

declare @allGLs TABLE (GLAccountID int, thePathExpanded varchar(max), accountCode varchar(200))
insert into @allGLS
select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode
from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl

-- transaction info
select TOP 1 t.transactionid, t.ownedByOrgID, t.recordedOnSiteID, tt.type, t.detail, t.amount, 
	t.transactionDate, t.dateRecorded, ts.status, t.assignedTomemberID, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullif(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as recordedByMember,
	m2.company as recordedByMemberCompany
from dbo.tr_transactions as t
inner join dbo.tr_types as tt on tt.typeID = t.typeID
inner join dbo.tr_statuses as ts on ts.statusID = t.statusID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join dbo.ams_members as m on m.memberid = t.recordedByMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
where t.transactionID = @transactionID

-- salestax info
select top 1 ts.saleID, ts.cache_amountAfterAdjustment as amountAfterAdjustment,
	ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount-ts.cache_pendingPaymentAllocatedAmount as unAllocatedAmount,
	ts.cache_activePaymentAllocatedAmount+ts.cache_pendingPaymentAllocatedAmount as allocatedAmount
from dbo.tr_transactionSales as ts
where ts.transactionID = @transactionID

-- invoices
select i.invoiceID, ins.status, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, i.dateDue, i.invoiceProfileID, ip.profileName, i.invoiceCode
from dbo.tr_transactions as t
inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
inner join dbo.organizations as o on o.orgID = @orgID
where t.transactionID in (
	select @transactionID as transactionID
	union
	select adj.transactionID
	from dbo.tr_transactions as adj
	inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
	inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
)
order by i.dateDue desc, 3 desc

-- current allocations
select t.transactionID, apos.allocatedAmount as allocAmount, t.detail,
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL
from dbo.fn_tr_getAllocatedPaymentsofSale(@transactionID) as apos
inner join dbo.tr_transactions as t on t.transactionID = apos.paymentTransactionID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glDeb on glDeb.GLAccountID = t.debitGLAccountID
order by t.transactionDate desc

-- gl activity
select tmp.amount, tmp.glaccountID, tmp.gltype, gl2.thePathExpanded + isnull(' (' + nullIf(gl2.accountCode,'') + ')','') as glexpanded
from (
	select sum(t.amount) as amount, t.debitGLAccountID as glAccountID, 'debit' as gltype
	from dbo.tr_transactions as t
	where t.transactionID in (
		select @transactionID as transactionID
		union
		select adj.transactionID
		from dbo.tr_transactions as adj
		inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
		inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
	)
	group by t.debitGLAccountID
		union all
	select sum(t.amount) as amount, t.creditGLAccountID as glAccountID, 'credit' as gltype
	from dbo.tr_transactions as t
	where t.transactionID in (
		select @transactionID as transactionID
		union
		select adj.transactionID
		from dbo.tr_transactions as adj
		inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
		inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
	)
	group by t.creditGLAccountID
) as tmp
inner join @allGLS as gl2 on gl2.GLAccountID = tmp.GLAccountID
order by tmp.gltype, glexpanded

-- sale transaction
select top 1 tSale.transactionID, tt.type, tSale.amount, tSale.detail,
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	glCred.thePathExpanded + isnull(' (' + nullIf(glCred.accountCode,'') + ')','') as creditGL
from dbo.tr_transactions as tSale
inner join dbo.tr_types as tt on tt.typeID = tSale.typeID
inner join dbo.tr_relationships as rTax on rTax.appliedToTransactionID = tSale.transactionID
inner join dbo.tr_relationshipTypes as rtTax on rtTax.typeID = rTax.typeID and rtTax.type = 'SalesTaxTrans'
inner join dbo.ams_members as mAss on mAss.memberid = tSale.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glCred on glCred.GLAccountID = tSale.creditGLAccountID
where rTax.transactionID = @transactionID

-- related transactions
EXEC dbo.tr_viewTransaction_related_recursive @transactionID

RETURN 0
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'dbo.tr_viewTransaction_writeoff_sale') AND type in (N'P', N'PC'))
DROP PROCEDURE dbo.tr_viewTransaction_writeoff_sale
GO
CREATE PROC [dbo].[tr_viewTransaction_writeoff_sale]
@transactionID int

AS

set nocount on

declare @orgID int
select @orgID = ownedByOrgID from dbo.tr_transactions where transactionID = @transactionID

-- transaction info
; WITH allGLs AS (
	select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode
	from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl
)
select TOP 1 t.transactionid, t.ownedByOrgID, t.recordedOnSiteID, tt.type, 'WriteOff of ' + t.detail as detail, t.amount, 
	t.transactionDate, t.dateRecorded, ts.status, t.assignedTomemberID, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullif(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as recordedByMember,
	m2.company as recordedByMemberCompany,
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL,
	glCred.thePathExpanded + isnull(' (' + nullIf(glCred.accountCode,'') + ')','') as creditGL
from dbo.tr_transactions as t
inner join dbo.tr_types as tt on tt.typeID = t.typeID
inner join dbo.tr_statuses as ts on ts.statusID = t.statusID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join dbo.ams_members as m on m.memberid = t.recordedByMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
inner join allGLs as glDeb on glDeb.GLAccountID = t.debitGLAccountID
inner join allGLs as glCred on glCred.GLAccountID = t.creditGLAccountID
where t.transactionID = @transactionID

-- salestax info
select 0 as otherInfo

-- related transactions
EXEC dbo.tr_viewTransaction_related_recursive @transactionID

RETURN 0
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'dbo.tr_viewTransaction_writeoff_payment') AND type in (N'P', N'PC'))
DROP PROCEDURE dbo.tr_viewTransaction_writeoff_payment
GO
CREATE PROC [dbo].[tr_viewTransaction_writeoff_payment]
@transactionID int

AS

set nocount on

declare @orgID int
select @orgID = ownedByOrgID from dbo.tr_transactions where transactionID = @transactionID

-- transaction info
; WITH allGLs AS (
	select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode
	from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl
)
select TOP 1 t.transactionid, t.ownedByOrgID, t.recordedOnSiteID, 'Negative Write Off' as type, 'Negative Write Off of ' + t.detail as detail, t.amount, 
	t.transactionDate, t.dateRecorded, ts.status, t.assignedTomemberID, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullif(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as recordedByMember,
	m2.company as recordedByMemberCompany,
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL,
	glCred.thePathExpanded + isnull(' (' + nullIf(glCred.accountCode,'') + ')','') as creditGL
from dbo.tr_transactions as t
inner join dbo.tr_types as tt on tt.typeID = t.typeID
inner join dbo.tr_statuses as ts on ts.statusID = t.statusID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join dbo.ams_members as m on m.memberid = t.recordedByMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
inner join allGLs as glDeb on glDeb.GLAccountID = t.debitGLAccountID
inner join allGLs as glCred on glCred.GLAccountID = t.creditGLAccountID
where t.transactionID = @transactionID

-- writeoff info
select b.batchID, b.batchName, b.depositDate, bs.status
from dbo.tr_batchTransactions as bt 
inner join dbo.tr_batches as b on b.batchID = bt.batchID
inner join dbo.tr_batchStatuses as bs on bs.statusID = b.statusID
where bt.transactionID = @transactionID

-- related transactions
EXEC dbo.tr_viewTransaction_related_recursive @transactionID

RETURN 0
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'dbo.tr_viewTransaction_allocation') AND type in (N'P', N'PC'))
DROP PROCEDURE dbo.tr_viewTransaction_allocation
GO
CREATE PROC [dbo].[tr_viewTransaction_allocation]
@transactionID int

AS

set nocount on

declare @orgID int
select @orgID = ownedByOrgID from dbo.tr_transactions where transactionID = @transactionID

declare @allGLs TABLE (GLAccountID int, thePathExpanded varchar(max), accountCode varchar(200), glCode varchar(30))
insert into @allGLS
select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode, gl.glCode
from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl
inner join dbo.tr_GLAccounts as gl on gl.GLAccountID = rgl.GLAccountID

-- transaction info
select TOP 1 t.transactionid, t.ownedByOrgID, t.recordedOnSiteID, t.amount,
	case when glCred.glCode = 'ACCOUNTSRECEIVABLE' then 'Allocation' else 'Deallocation' end as [type],
	case when glCred.glCode = 'ACCOUNTSRECEIVABLE' then 'Allocation ' else 'Deallocation ' end + 'of Payment' as detail,
	t.transactionDate, t.dateRecorded, ts.status, t.assignedTomemberID, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullif(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as recordedByMember,
	m2.company as recordedByMemberCompany,
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL,
	glCred.thePathExpanded + isnull(' (' + nullIf(glCred.accountCode,'') + ')','') as creditGL
from dbo.tr_transactions as t
inner join dbo.tr_types as tt on tt.typeID = t.typeID
inner join dbo.tr_statuses as ts on ts.statusID = t.statusID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join dbo.ams_members as m on m.memberid = t.recordedByMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
inner join @allGLS as glDeb on glDeb.GLAccountID = t.debitGLAccountID
inner join @allGLS as glCred on glCred.GLAccountID = t.creditGLAccountID
where t.transactionID = @transactionID

-- allocation info
select b.batchID, b.batchName, b.depositDate, bs.status
from dbo.tr_batchTransactions as bt 
inner join dbo.tr_batches as b on b.batchID = bt.batchID
inner join dbo.tr_batchStatuses as bs on bs.statusID = b.statusID
where bt.transactionID = @transactionID

-- current allocations
select tSaleAdj.transactionID as revenueTransactionID, tSaleAdj.amount as revenueAmount, ttSaleAdj.type as revenueType, 
	case when ttSaleAdj.typeid = 3 then 'Adjustment of ' else '' end + tSaleAdj.detail as revenueDetail,
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as revenueAssignedToMember,
	mAss2.company as revenueAssignedToMemberCompany,
	glCred.thePathExpanded + isnull(' (' + nullIf(glCred.accountCode,'') + ')','') as revenueCreditGL,
	tPay.transactionID as cashTransactionID, tPay.amount as cashAmount, ttPay.type as cashType, 
	tPay.detail as cashDetail,
	mAssPay2.firstname + isnull(' ' + nullif(mAssPay2.middlename,''),'') + ' ' + mAssPay2.lastname + isnull(' ' + nullif(mAssPay2.suffix,''),'') + ' (' + mAssPay2.membernumber + ')' as cashAssignedToMember,
	mAssPay2.company as cashAssignedToMemberCompany,
	glDebPay.thePathExpanded + isnull(' (' + nullIf(glDebPay.accountCode,'') + ')','') as cashDebitGL
from dbo.tr_transactions as tAlloc
inner join dbo.tr_relationships as rSaleAdj on rSaleAdj.transactionID = tAlloc.transactionID
inner join dbo.tr_relationshipTypes as rtSaleAdj on rtSaleAdj.typeID = rSaleAdj.typeID and rtSaleAdj.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as tSaleAdj on tSaleAdj.transactionID = rSaleAdj.appliedToTransactionID
inner join dbo.tr_types as ttSaleAdj on ttSaleAdj.typeID = tSaleAdj.typeID
inner join dbo.ams_members as mAss on mAss.memberid = tSaleAdj.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glCred on glCred.GLAccountID = tSaleAdj.creditGLAccountID
inner join dbo.tr_relationships as rPay on rPay.transactionID = tAlloc.transactionID
inner join dbo.tr_relationshipTypes as rtPay on rtPay.typeID = rPay.typeID and rtPay.type = 'AllocPayTrans'
inner join dbo.tr_transactions as tPay on tPay.transactionID = rPay.appliedToTransactionID
inner join dbo.tr_types as ttPay on ttPay.typeID = tPay.typeID
inner join dbo.ams_members as mAssPay on mAssPay.memberid = tPay.assignedToMemberID
inner join dbo.ams_members as mAssPay2 on mAssPay2.memberID = mAssPay.activeMemberID
inner join @allGLS as glDebPay on glDebPay.GLAccountID = tPay.debitGLAccountID
where tAlloc.transactionID = @transactionID

-- related transactions
EXEC dbo.tr_viewTransaction_related_recursive @transactionID

RETURN 0
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'dbo.tr_viewTransaction_void') AND type in (N'P', N'PC'))
DROP PROCEDURE dbo.tr_viewTransaction_void
GO
CREATE PROC [dbo].[tr_viewTransaction_void]
@transactionID int

AS

set nocount on

declare @orgID int
select @orgID = ownedByOrgID from dbo.tr_transactions where transactionID = @transactionID

-- transaction info
; WITH allGLs AS (
	select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode, gl.glCode
	from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl
	inner join dbo.tr_GLAccounts as gl on gl.GLAccountID = rgl.GLAccountID
)
select TOP 1 t.transactionid, t.ownedByOrgID, t.recordedOnSiteID, tt.type, t.amount, 
	case
	when tVoidee.typeID = 3 and glVoidee.glCode = 'ACCOUNTSRECEIVABLE' then 'VOID of Negative Adjustment of ' + isnull(tVoidee.detail,'')
	when tVoidee.typeID = 3 and glVoidee.glCode <> 'ACCOUNTSRECEIVABLE' then 'VOID of Adjustment of ' + isnull(tVoidee.detail,'')
	when tVoidee.typeID = 6 and glVoidee.glCode = 'ACCOUNTSRECEIVABLE' then 'VOID of WriteOff of ' + isnull(tVoidee.detail,'')
	when tVoidee.typeID = 6 and glVoidee.glCode <> 'ACCOUNTSRECEIVABLE' then 'VOID of Negative WriteOff of ' + isnull(tVoidee.detail,'')
	when tVoidee.typeID = 9 then 'VOID of NSF of ' + isnull(tVoidee.detail,'')
	when tVoidee.typeID = 5 and glVoidee.glCode = 'ACCOUNTSRECEIVABLE' then 'VOID of Allocation to ' + isnull(tVoideeSale.detail,'')
	when tVoidee.typeID = 5 and glVoidee.glCode <> 'ACCOUNTSRECEIVABLE' then 'VOID of Deallocation from ' + isnull(tVoideeSale.detail,'')
	else 'VOID of ' + isnull(t.detail,'')
	end as detail,
	t.transactionDate, t.dateRecorded, ts.status, t.assignedTomemberID, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullif(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as recordedByMember,
	m2.company as recordedByMemberCompany,
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL,
	glCred.thePathExpanded + isnull(' (' + nullIf(glCred.accountCode,'') + ')','') as creditGL
from dbo.tr_transactions as t
inner join dbo.tr_types as tt on tt.typeID = t.typeID
inner join dbo.tr_statuses as ts on ts.statusID = t.statusID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join dbo.ams_members as m on m.memberid = t.recordedByMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
inner join allGLs as glDeb on glDeb.GLAccountID = t.debitGLAccountID
inner join allGLs as glCred on glCred.GLAccountID = t.creditGLAccountID
inner join dbo.tr_relationships AS rVoid on rVoid.transactionID = t.transactionID
inner join dbo.tr_relationshipTypes AS rtVoid ON rtVoid.typeID = rVoid.typeID AND rtVoid.type = 'OffsetTrans'
inner join dbo.tr_transactions as tVoidee on tVoidee.transactionID = rVoid.appliedToTransactionID
inner join allGLs as glVoidee on glVoidee.GLAccountID = tVoidee.creditGLAccountID
left outer join dbo.tr_relationships AS rVoideeAllocSale 
	inner join dbo.tr_relationshipTypes AS rtVoideeAllocSale ON rtVoideeAllocSale.typeID = rVoideeAllocSale.typeID AND rtVoideeAllocSale.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as tVoideeSale on tVoideeSale.transactionID = rVoideeAllocSale.appliedToTransactionID
	on rVoideeAllocSale.transactionID = tVoidee.transactionID and tVoidee.typeID = 5
where t.transactionID = @transactionID

-- void info
IF (
	select tVoidee.typeID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships AS rVoid on rVoid.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes AS rtVoid ON rtVoid.typeID = rVoid.typeID AND rtVoid.type = 'OffsetTrans'
	inner join dbo.tr_transactions as tVoidee on tVoidee.transactionID = rVoid.appliedToTransactionID
	where t.transactionID = @transactionID
	) in (1,7,3)
	select it.itID, it.messageContentVersionID,
		i.invoiceID, ins.status, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, i.dateDue, i.invoiceProfileID, ip.profileName, i.invoiceCode
	from dbo.tr_invoiceTransactions as it
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
	inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
	inner join dbo.organizations as o on o.orgID = @orgID
	where it.transactionID = @transactionID
ELSE
	select b.batchID, b.batchName, b.depositDate, bs.status
	from dbo.tr_batchTransactions as bt 
	inner join dbo.tr_batches as b on b.batchID = bt.batchID
	inner join dbo.tr_batchStatuses as bs on bs.statusID = b.statusID
	where bt.transactionID = @transactionID

-- related transactions
EXEC dbo.tr_viewTransaction_related_recursive @transactionID

RETURN 0
GO

