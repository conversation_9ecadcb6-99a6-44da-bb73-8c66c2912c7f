use membercentral
GO
DECLARE	@toolTypeID int, @toolResourceTypeID int, @rc int, @level3NavigationID int, @level4NavigationID int, @resourceTypeFunctionID int

select @level3NavigationID = pNav.navigationID
	from dbo.admin_navigation as pNav
	inner join dbo.admin_navigation as pNav2 on pNav2.navigationID = pNav.parentNavigationID
	where pNav.navName = 'General'
	and pNav.navAreaID = 3
	and pNav2.navName = 'Accounting'
select @toolTypeID = null, @toolResourceTypeID = null

BEGIN TRAN
	EXEC @rc = dbo.createAdminToolType 
		@toolType='AGRAggregateAllocationSummary',
		@toolCFC='Reports.Accounting.AggregateAllocationSummary',
		@toolDesc='Aggregate Allocation Summary',
		@toolTypeID=@toolTypeID OUTPUT,
		@resourceTypeID=@toolResourceTypeID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = dbo.createAdminNavigation
		@navName='Aggregate Payment Summary',
		@navDesc='This report details the allocation of payments to general ledger accounts based on the Batch Deposit Date for Posted Batches only including aggregate amounts per allocation.',
		@parentNavigationID=@level3NavigationID,
		@navAreaID=4,
		@cfcMethod='showReport',
		@isHeader=0,
		@showInNav=1,
		@navigationID=@level4NavigationID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('Admin'),dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('Admin')))
	EXEC @rc = dbo.createAdminFunctionsDeterminingNav
		@resourceTypeFunctionID=@resourceTypeFunctionID,
		@toolTypeID=@toolTypeID,
		@navigationID=@level4NavigationID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	insert into dbo.admin_siteToolRestrictions (toolTypeID, siteID)	
	select @toolTypeID, siteID
	from dbo.admin_siteToolRestrictions
	where toolTypeID = 119

IF @@TRANCOUNT > 0 COMMIT TRAN

declare @siteID int
select @siteID=min(siteID) from dbo.sites
while @siteID is not null begin
	exec dbo.createAdminSuite @siteID=@siteID
	select @siteID = min(siteID) from dbo.sites where siteID > @siteID
end


GOTO on_done

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN

on_done:

GO