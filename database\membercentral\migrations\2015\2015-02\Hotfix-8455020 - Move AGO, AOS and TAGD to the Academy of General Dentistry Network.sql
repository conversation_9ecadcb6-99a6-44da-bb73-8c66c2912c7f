-- Hotfix-8455020 - Move AGO, AOS and TAGD to the Academy of General Dentistry Network
use membercentral
go

declare @newNetworkID int, @tagdNetworkID int, @agoNetworkID int, @aosNetworkID int

select @newNetworkID = networkID from networks where networkName = 'Academy of General Dentistry'
select @agoNetworkID = networkID from networks where networkName = 'Academy of Gp Orthodontics'
select @aosNetworkID = networkID from networks where networkName = 'American Orthodontic Society'
select @tagdNetworkID = networkID from networks where networkName = 'Texas Academy of General Dentistry'

print 'Update site / network'

update ams_networkprofiles set networkID = @newNetworkID where networkID in (@agoNetworkID,@aosNetworkID,@tagdNetworkID)
update networkSites set networkID = @newNetworkID where networkID in (@agoNetworkID,@aosNetworkID,@tagdNetworkID)
update networkSites set isMasterSite = 0 where networkID in (@tagdNetworkID)

print 'Deleting networks'

delete from networks  where networkID in (@agoNetworkID,@aosNetworkID,@tagdNetworkID)