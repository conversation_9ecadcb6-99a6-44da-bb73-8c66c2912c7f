-- ON PROD SET TASK TO ACTIVE

USE [customApps]
GO
CREATE TABLE [dbo].[NC_Blog_Categories](
	[blogCategoryID] [int] IDENTITY(1,1) NOT NULL,
	[blogCategoryDescription] [varchar](50) NOT NULL,
	[sortOrder] [int] NULL,
 CONSTRAINT [PK_NC_BlogCategories] PRIMARY KEY CLUSTERED 
(
	[blogCategoryID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

CREATE TABLE [dbo].[NC_Blog_Entries](
	[blogEntryID] [int] IDENTITY(1,1) NOT NULL,
	[blogID] [int] NOT NULL,
	[title] [varchar](250) NULL,
	[link] [varchar](500) NULL,
	[pubDate] [datetime] NULL,
	[guid] [varchar](500) NULL,
	[description] [varchar](max) NULL,
	[shortDesc] [varchar](400) NULL,
 CONSTRAINT [PK_NC_Blog_Entries] PRIMARY KEY CLUSTERED 
(
	[blogEntryID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

CREATE TABLE [dbo].[NC_Blog_Feeds](
	[blogID] [int] IDENTITY(1,1) NOT NULL,
	[blogCategoryID] [int] NULL,
	[blogURL] [varchar](350) NULL,
	[blogDescription] [varchar](50) NULL,
	[addAuth] [bit] NOT NULL CONSTRAINT [DF_NC_Blog_Feeds_addAuth]  DEFAULT ((0)),
	[sortOrder] [int] NULL,
	[isValid] [int] NOT NULL CONSTRAINT [DF_NC_Blog_Feeds_isValid]  DEFAULT ((0)),
 CONSTRAINT [PK_NC_BlogFeeds] PRIMARY KEY CLUSTERED 
(
	[blogID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO


use membercentral
GO
insert into dbo.scheduledTasks (name, nextRunDate, interval, intervalTypeID, taskCFC, timeoutMinutes, disabled, siteid)
values ('NC Blog Updater', getdate(), 1, 4, 'model.scheduledTasks.tasks.NC-BlogUpdater', 5, 1, 154)
GO
declare @toolTypeID int, @resourceTypeID int
EXEC dbo.createAdminToolType 'NCBlogAdmin', 'Custom.NC.NC.BlogAdmin', 'NC Blog Administration', @toolTypeID OUTPUT, @resourceTypeID OUTPUT
INSERT INTO dbo.admin_siteToolRestrictions (toolTypeID, siteID) VALUES(@toolTypeID, 154)
INSERT INTO dbo.admin_functionsDeterminingNav (resourceTypeFunctionID, toolTypeID, navigationID) VALUES (58, @toolTypeID, 86)
EXEC dbo.createAdminSuite 154
GO


