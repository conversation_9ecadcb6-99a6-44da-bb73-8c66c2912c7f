select *
from sites where sitecode = 'TXRX'

select m.memberid, m.activeMemberid, m.membernumber, m.lastname, m.firstname
into dataTransfer.dbo.txrxBackup
from ams_members m
where m.orgid = 10

select *
from dataTransfer.dbo.txrxUpdate
where [Member Guest] = 'Member'
where [Constituent ID] = ''

select m.memberid, m.activeMemberid, m.membernumber, m.lastname, m.firstname, u.[Constituent ID], u.[Member ID]from ams_members m
inner join dataTransfer.dbo.txrxUpdate u on u.[Constituent ID] = m.memberNumber
where m.orgid = 10
and [Constituent ID] <> ''

-- Best case.  The constituent ID matches the member number  about 6K
update m 
	set MemberNumber =  u.[Member ID]
from ams_members m
inner join dataTransfer.dbo.txrxUpdate u on u.[Constituent ID] = m.memberNumber
where m.orgid = 10
and [Constituent ID] <> ''

-- remove these from processiong
delete u
from ams_members m
inner join dataTransfer.dbo.txrxUpdate u on u.[Member ID] = m.memberNumber
where m.orgid = 10


-- Match on License number 79 in dev
select m.memberid, m.activeMemberid, m.membernumber, m.lastname, m.firstname, v.licenseNumber, u.*
from ams_members m
inner join dbo.vw_memberData_TXRX v on v.memberid = m.memberid and v.licenseNumber <> ''
inner join dataTransfer.dbo.txrxUpdate u on u.[License Number] = v.licenseNumber and u.[License Number] not in ('', 'NA') and m.lastname=u.[last name] and m.firstname = u.[first name]
where m.orgid = 10

update m 
	set m.MemberNumber =  u.[Member ID]
from ams_members m
inner join dbo.vw_memberData_TXRX v on v.memberid = m.memberid and v.licenseNumber <> ''
inner join dataTransfer.dbo.txrxUpdate u on u.[License Number] = v.licenseNumber and u.[License Number] not in ('', 'NA') and m.lastname=u.[last name] and m.firstname = u.[first name]
where m.orgid = 10


-- remove these from processiong
delete u
from ams_members m
inner join dataTransfer.dbo.txrxUpdate u on u.[Member ID] = m.memberNumber
where m.orgid = 10

-- Prod 5K
select m.memberid, m.activeMemberid, m.membernumber, m.lastname, m.firstname, v.email, u.[Constituent ID], u.[Member ID]
from ams_members m
inner join dbo.vw_memberData_TXRX v on v.memberid = m.memberid and v.email <> ''
inner join dataTransfer.dbo.txrxUpdate u on u.email = v.email and u.email not in ('') and u.[last name] = m.lastname and u.[first name] = m.firstname
where m.orgid = 10


update m 
	set m.MemberNumber =  u.[Member ID]
from ams_members m
inner join dbo.vw_memberData_TXRX v on v.memberid = m.memberid and v.email <> ''
inner join dataTransfer.dbo.txrxUpdate u on u.email = v.email and u.email not in ('') and u.[last name] = m.lastname and u.[first name] = m.firstname
where m.orgid = 10

delete u
from ams_members m
inner join dataTransfer.dbo.txrxUpdate u on u.[Member ID] = m.memberNumber
where m.orgid = 10
