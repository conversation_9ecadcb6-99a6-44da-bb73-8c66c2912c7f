ALTER PROC dbo.ev_createSponsorGrouping
@registrationID int,
@sponsorGrouping varchar(200),
@sponsorGroupingID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	set @sponsorGroupingID = null;

	IF EXISTS (select sponsorGroupingID from dbo.ev_sponsorGrouping where registrationID=@registrationID and sponsorGrouping=@sponsorGrouping)
		RAISERROR('Sponsor Grouping exists.',16,1);

	DECLARE @sponsorGroupingOrder int;
	SELECT @sponsorGroupingOrder = isnull(max(sponsorGroupingOrder),0)+1 from dbo.ev_sponsorGrouping where registrationID=@registrationID;

	INSERT INTO dbo.ev_sponsorGrouping (registrationID, sponsorGrouping, sponsorGroupingOrder)
	VALUES (@registrationID, @sponsorGrouping, @sponsorGroupingOrder);

	SELECT @sponsorGroupingID = SCOPE_IDENTITY();

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
