USE [memberCentral]
GO
IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ev_uploadRegistrantCredit]') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].[ev_uploadRegistrantCredit]
GO
CREATE PROC [dbo].[ev_uploadRegistrantCredit]
@eventID int,
@csvfilename varchar(200),
@strFileColumnNames varchar(max),
@pathToExport varchar(100),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

BEGIN TRY

	declare @sitecode varchar(10), @siteid int, @orgID int, @tmptbl varchar(17), @var_tmpCols varchar(20), @qry varchar(max),
		@createSQL varchar(max), @cmd varchar(400), @customID int, @customTypeID int, @titleOnInvoice varchar(max), @registrationID int,
		@creditType varchar(300), @creditRow int
	declare @dynSQL nvarchar(max), @prefix varchar(50), @flatfile varchar(160), @exportcmd varchar(400)
	declare @tblCredits TABLE (row int IDENTITY(1,1), creditType varchar(300), offeringTypeID int)
	declare @tblRegfields TABLE (autoID int, fieldName varchar(300))
	declare @tblCustFields TABLE (row int, customID int, titleOnInvoice varchar(max), customTypeID int)
	declare @tblMissingCols TABLE (colName varchar(255))
	declare @tblErrors TABLE (rowid int IDENTITY(1,1), msg varchar(300), fatal bit)
	declare @tblCounts TABLE (rowid int IDENTITY(1,1), countName varchar(50), countNum int)

	-- get any credits offered
	insert into @tblCredits	(creditType, offeringTypeID)
	select distinct ca.authorityName + '/' + isnull(ast.ovTypeName,cat.typeName), ect.offeringTypeID
	from dbo.crd_offerings AS ec
	inner join dbo.crd_offeringTypes as ect on ect.offeringID = ec.offeringID
	inner join dbo.crd_authoritySponsorTypes as ast on ast.ASTID = ect.ASTID
	inner join dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
	inner join dbo.crd_authorities as ca on ca.authorityID = cat.authorityID
	WHERE ec.statusID = 4
	and ec.eventID = @eventID

	-- get all colums that should be there
	insert into @tblRegfields (autoID, fieldName)
	EXEC dbo.ev_uploadRegistrantCreditTemplate @eventID, null

	-- set table name
	select @sitecode=s.sitecode, @siteID=s.siteID, @orgID=s.orgID
		from dbo.ev_events as e
		inner join dbo.sites as s on s.siteID = e.siteID
		where e.eventID = @eventID
	select @registrationID = registrationID from dbo.ev_registration where eventID = @eventID and status = 'A'
	select @tmptbl = '##tmpEvents' + @sitecode
	select @var_tmpCols = '##tmpColsEvents' + @sitecode

	-- delete temp table if exists
	IF OBJECT_ID('tempdb..' + @tmptbl) IS NOT NULL 
		EXEC('DROP TABLE ' + @tmptbl)
	IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
		EXEC('DROP TABLE ' + @var_tmpCols)

	-- create temp table using columnlist
	select @qry = null
	select @qry = COALESCE(@qry + ', ', '') + quotename(listitem) + ' varchar(max) '
		FROM dbo.fn_varCharListToTable(@strFileColumnNames,char(7))
		order by autoid
		EXEC('CREATE TABLE ' + @tmptbl + ' (' + @qry + ')')

	select @qry = null
	select @qry = COALESCE(@qry + ', ', '') + quotename(fieldName) + ' varchar(max) '
		FROM @tblRegfields
		order by autoid
		EXEC('CREATE TABLE ' + @var_tmpCols + ' (' + @qry + ')')

	-- rowID and membernumber is in a key column of an index and needs to not be varchar(max)
	declare @trash bit
	set @trash = 1
	EXEC('ALTER TABLE ' + @tmptbl + ' ALTER COLUMN rowID int') 
	BEGIN TRY
		EXEC('ALTER TABLE ' + @tmptbl + ' ALTER COLUMN membernumber varchar(800)') 
	END TRY
	BEGIN CATCH
		set @trash = 0
	END CATCH

	-- Execute a bulk insert into previously defined temporary table
	SELECT @qry = 'BULK INSERT ' + @tmptbl + ' FROM ''' + @csvfilename + ''' WITH (FIELDTERMINATOR = '''+ char(7) + ''', FIRSTROW = 2);'
	EXEC(@qry)
	
	IF OBJECT_ID('tempdb..#tblOrgCols') IS NOT NULL 
		DROP TABLE #tblOrgCols
	IF OBJECT_ID('tempdb..#tblImportCols') IS NOT NULL 
		DROP TABLE #tblImportCols

	-- ******************************** 
	-- ensure all columns exist (fatal)
	-- ******************************** 
	-- this will get the columns that should be there
	CREATE TABLE #tblOrgCols (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
		COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
		SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
		COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
		CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)

		-- get cols
		INSERT INTO #tblOrgCols
		EXEC tempdb.dbo.SP_COLUMNS @var_tmpCols
			
		-- cleanup table no longer needed
		IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
			EXEC('DROP TABLE ' + @var_tmpCols)

	-- this will get the columns that are actually in the import
	CREATE TABLE #tblImportCols (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
		COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
		SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
		COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
		CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)

		-- get cols
		INSERT INTO #tblImportCols
		EXEC tempdb.dbo.SP_COLUMNS @tmptbl

	INSERT INTO @tblErrors (msg, fatal)
	select 'The column ' + org.column_name + ' is missing from your data.', 1
	from #tblOrgCols as org
	left outer join #tblImportCols as imp on imp.column_name = org.column_name
	where imp.table_name is null

	insert into @tblMissingCols(colname)
	select org.column_name
	from #tblOrgCols as org
	left outer join #tblImportCols as imp on imp.column_name = org.column_name
	where imp.table_name is null


	-- get memberid and registrant
	select @qry = 'ALTER TABLE ' + @tmptbl + ' ADD MCMemberID int NULL, MCRegistrantID int NULL;'
	EXEC(@qry)

	-- ********************************
	-- no/bad member number (fatal)
	-- ********************************
	IF NOT EXISTS (select colName from @tblMissingCols where colName = 'memberNumber') BEGIN
		select @qry = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' is missing a Member Number. Member Numbers are required for all entries.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE (memberNumber IS NULL OR ltrim(rtrim(memberNumber)) = '''')
			ORDER BY rowID'
		INSERT INTO @tblErrors (msg, fatal)
		EXEC(@qry)

		select @qry = '		
			update tmp
			set tmp.MCMemberID = m.memberid
			from ' + @tmptbl + ' as tmp
			inner join dbo.ams_members as m on m.memberNumber = tmp.memberNumber
				and m.orgID = ' + cast(@orgID as varchar(10)) + '
				and m.memberID = m.activeMemberID
			'
		EXEC(@qry)

		select @qry = '		
			update tmp
			set tmp.MCRegistrantID = r.registrantID
			from ' + @tmptbl + ' as tmp
			inner join dbo.ams_members as m on m.memberid = tmp.MCMemberID
			inner join dbo.ams_members as m2 on m2.activeMemberID = m.memberID
			inner join dbo.ev_registrants as r on r.memberID = m2.memberID
			where r.registrationID = ' + cast(@registrationID as varchar(10)) + '
			and r.status = ''A''
			'
		EXEC(@qry)

		select @qry = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' Member Number ('' + tb.membernumber + '') is not a registrant of this event.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' as tb
			WHERE MCRegistrantID is null
			ORDER BY rowID'
		INSERT INTO @tblErrors (msg, fatal)
		EXEC(@qry)
	END

	-- ******************
	-- attendance (fatal)
	-- *******************
	IF NOT EXISTS (select colName from @tblMissingCols where colName = 'attended') BEGIN
		select @qry = '
			UPDATE ' + @tmptbl + ' set attended = 1 where attended in (''Yes'',''Y'',''TRUE'',''T'');
			UPDATE ' + @tmptbl + ' set attended = 0 where attended in (''No'',''N'',''FALSE'',''F'');
		'
		EXEC(@qry)

		select @qry = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' is missing an attendance indicator. Attendance must either be Yes or No for all entries.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE (attended is null or attended = '''')
			ORDER BY rowID'
		INSERT INTO @tblErrors (msg, fatal)
		EXEC(@qry)

		select @qry = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' has an invalid attendance indicator. Attendance must either be Yes or No for all entries.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE (attended is not null and attended not in (''1'',''0''))
			ORDER BY rowID'
		INSERT INTO @tblErrors (msg, fatal)
		EXEC(@qry)
	END

	-- *********************
	-- credit values (fatal)
	-- *********************
	IF NOT EXISTS (select colName from @tblMissingCols where colName = 'attended') and EXISTS (select top 1 row from @tblCredits) BEGIN
		select @creditRow = min(row) from @tblCredits
		while @creditRow is not null BEGIN
			select @creditType = creditType from @tblCredits where row = @creditRow

			IF NOT EXISTS (select colName from @tblMissingCols where colName = @creditType) BEGIN
				BEGIN TRY
					select @qry = 'ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [' + @creditType + '] decimal(6,2);'
					EXEC(@qry)
				END TRY
				BEGIN CATCH
					INSERT INTO @tblErrors (msg, fatal)
					VALUES ('There are invalid credit values for [' + @creditType + '].', 1)
				END CATCH

				select @qry = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' is not marked as attended but has credit values defined.'' as msg, 1 as fatal 
					FROM ' + @tmptbl + ' 
					WHERE attended = ''0'' and [' + @creditType + '] is not null and [' + @creditType + '] > 0
					ORDER BY rowID'
				INSERT INTO @tblErrors (msg, fatal)
				EXEC(@qry)
			END

			select @creditRow = min(row) from @tblCredits where row > @creditRow
		END
	END

	-- cleanup - these are no longer needed
	IF OBJECT_ID('tempdb..#tblOrgCols') IS NOT NULL
		DROP TABLE #tblOrgCols
	IF OBJECT_ID('tempdb..#tblImportCols') IS NOT NULL
		DROP TABLE #tblImportCols

	IF NOT EXISTS (select rowID from @tblErrors where fatal = 1) BEGIN
		-- ******************************** 
		-- dump flat table and format file and creation script
		-- would love to use xml format files, but it appears column names with spaces cause it to fail
		-- ******************************** 
		SELECT @prefix = @sitecode + '_EV_flat_' + convert(varchar(8),getdate(),112) + replace(convert(varchar(8),getdate(),108),':','')
		SELECT @flatfile = @pathToExport + @prefix

		select @exportcmd = 'bcp ' + @tmptbl + ' format nul -f ' + @flatfile + '.txt -n -T -S' + CAST(serverproperty('servername') as varchar(40))
		EXEC master..xp_cmdshell @exportcmd, NO_OUTPUT

		select @exportcmd = 'bcp ' + @tmptbl + ' out ' + @flatfile + '.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
		EXEC master..xp_cmdshell @exportcmd, NO_OUTPUT

		declare @createTableSQLTop varchar(100)          
		declare @coltypes table (datatype varchar(16))          
		insert into @coltypes values('bit')          
		insert into @coltypes values('binary')          
		insert into @coltypes values('bigint')          
		insert into @coltypes values('int')          
		insert into @coltypes values('float')          
		insert into @coltypes values('datetime')          
		insert into @coltypes values('text')          
		insert into @coltypes values('image')          
		insert into @coltypes values('money')          
		insert into @coltypes values('uniqueidentifier')          
		insert into @coltypes values('smalldatetime')          
		insert into @coltypes values('tinyint')          
		insert into @coltypes values('smallint')          
		insert into @coltypes values('sql_variant')          
		select @dynSQL = ''
		select @dynSQL = @dynSQL +           
			case when charindex('(',@dynSQL,1)<=0 then '(' else '' end + '[' + Column_Name + '] ' +Data_Type +
			case when Data_Type in (Select datatype from @coltypes) then '' else  '(' end+
			case when data_type in ('real','decimal','numeric')  then cast(isnull(numeric_precision,'') as varchar)+','+
			case when data_type in ('real','decimal','numeric') then cast(isnull(Numeric_Scale,'') as varchar) end
			when data_type in ('nvarchar','varchar') and cast(isnull(Character_Maximum_Length,'') as varchar) = '-1' then 'max'
			when data_type in ('char','nvarchar','varchar','nchar') then cast(isnull(Character_Maximum_Length,'') as varchar) else '' end+
			case when Data_Type in (Select datatype from @coltypes)then '' else  ')' end+
			case when Is_Nullable='No' then ' Not null,' else ' null,' end
			from tempdb.Information_Schema.COLUMNS where Table_Name=@tmptbl
			order by ordinal_position
		select @createTableSQLTop = 'Create table ##xxx ' 
		select @dynSQL=@createTableSQLTop + substring(@dynSQL,1,len(@dynSQL)-1) +' )'            
		if dbo.fn_WriteFile(@pathToExport + @prefix + '.sql', @dynSQL, 1) = 0 BEGIN
			RETURN 0
		END

		-- create index on temp table for numbers below
		EXEC('CREATE NONCLUSTERED INDEX [idx_' + @tmptbl + '_memnum_rowid] ON ' + @tmptbl + '([membernumber] ASC,[rowID] ASC)')

		-- ******************************** 
		-- Counts
		-- ******************************** 
		-- TotalEntriesImported
		select @dynSQL = 'SELECT ''TotalEntriesImported'', COUNT(rowID) FROM ' + @tmptbl
		INSERT INTO @tblCounts (countName, countNum)
		EXEC(@dynSQL)
	END

	-- ********************************
	-- generate result xml file 
	-- ********************************
	select @importResult = (
		select getdate() as "@date", @flatfile as "@flatfile",
			isnull((select top 301 dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg", "@severity" = case fatal when 1 then 'fatal' else 'nonfatal' end
			from @tblErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>'),

			isnull((select countName as "@name", countNum as "@num"
			from @tblCounts
			order by rowid
			FOR XML path('count'), root('counts'), type),'<counts/>')
		for xml path('import'), TYPE)

	-- drop temp tables 
	IF OBJECT_ID('tempdb..' + @tmptbl) IS NOT NULL
		EXEC('DROP TABLE ' + @tmptbl)

END TRY
BEGIN CATCH
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

-- ********************************
-- go ahead and do actual import if no fatal errors 
-- ********************************
IF @importResult.value('count(/import/errors/error[@severity="fatal"])','int') > 0
	RETURN -1

BEGIN TRY
	-- **************
	-- read in sql create script and create global temp table
	-- **************
	select @createSQL = replace(dbo.fn_ReadFile(@flatfile + '.sql',0,1),'##xxx','##importEventCreditData')
	IF OBJECT_ID('tempdb..##importEventCreditData') IS NOT NULL
		DROP TABLE ##importEventCreditData
	EXEC(@createSQL)

	-- *******************
	-- bcp in data
	-- *******************
	select @cmd = 'bcp ##importEventCreditData in ' + @flatfile + '.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
	exec master..xp_cmdshell @cmd, NO_OUTPUT

	-- *******************
	-- immediately put into local temp table and drop global temp
	-- *******************
	select * 
	into #importEventsCreditData 
	from ##importEventCreditData
	IF OBJECT_ID('tempdb..##importEventCreditData') IS NOT NULL
		DROP TABLE ##importEventCreditData

	-- Add index for queries below
	CREATE NONCLUSTERED INDEX [idx_importEventsCreditData] ON #importEventsCreditData ([membernumber] ASC);

	/* ************************************** */
	/* ATTENDANCE for All Registrants in File */
	/* ************************************** */
	update evr
	set evr.attended = tmp.attended
	from dbo.ev_registrants as evr
	inner join #importEventsCreditData as tmp on tmp.MCRegistrantID = evr.registrantID

	/* **************************************** */
	/* AWARD CREDIT for All Registrants in File */
	/* **************************************** */
	IF EXISTS (select top 1 row from @tblCredits) BEGIN
		DECLARE @creditColumns VARCHAR(max)
		select @creditColumns = COALESCE(@creditColumns + ',', '') + quoteName(creditType) from @tblCredits
		
		IF OBJECT_ID('tempdb..#importEventsCreditData2') IS NOT NULL
			DROP TABLE #importEventsCreditData2
		CREATE TABLE #importEventsCreditData2 (MCRegistrantID int, creditType varchar(300), creditValue decimal(6,2), offeringTypeID int)

		declare @fullSql varchar(max)
		set @fullSql = '
			select MCRegistrantID, creditType, creditValue, cast(0 as int) as [offeringTypeID]
			from #importEventsCreditData 
			unpivot (creditValue for creditType in (' + @creditColumns +')) u'
		insert into #importEventsCreditData2 (MCRegistrantID, creditType, creditValue, offeringTypeID)
		exec(@fullSql)

		update t
		set t.offeringTypeID = tmp.offeringTypeID
		from #importEventsCreditData2 as t
		inner join @tblCredits as tmp on tmp.creditType = t.creditType

		-- if nothing specified for a credit type or is 0, then dont add it
		delete from #importEventsCreditData2 where creditValue <= 0

		declare @lastDateToComplete datetime
		select @lastDateToComplete = max(endTime) from dbo.ev_times where eventID=@eventID
		SELECT @lastDateToComplete = DATEADD(ss, 86340, DATEADD(dd, DATEDIFF(dd,0,@lastDateToComplete), 0))

		-- update credits where they already have it
		update crd
		set crd.creditAwarded = 1,
			crd.creditValueAwarded = tmp.creditValue
		from dbo.crd_requests as crd
		inner join #importEventsCreditData2 as tmp on tmp.MCRegistrantID = crd.registrantID
			and tmp.offeringTypeID = crd.offeringTypeID

		-- add credits where they dont already have it
		INSERT INTO dbo.crd_requests (offeringTypeID, IDNumber, lastDateToComplete, creditAwarded, creditValueAwarded, addedViaAward, registrantID)
		select tmp.offeringTypeID, '', @lastDateToComplete, 1, tmp.creditValue, 1, tmp.MCRegistrantID
		from #importEventsCreditData2 as tmp
		where not exists (
			select requestID 
			from dbo.crd_requests
			where offeringTypeID = tmp.offeringTypeID
			and registrantID = tmp.MCRegistrantID
		)

		-- delete/update credits where they should no longer have it
		IF OBJECT_ID('tempdb..#tmpCreditToDelete') IS NOT NULL
			DROP TABLE #tmpCreditToDelete
		select crd.requestID, crd.addedViaAward
		into #tmpCreditToDelete
		from dbo.crd_requests as crd
		inner join #importEventsCreditData as tmp on tmp.MCRegistrantID = crd.registrantID
		where crd.addedViaAward = 1
		and not exists (
			select MCRegistrantID
			from #importEventsCreditData2
			where MCRegistrantID = crd.registrantID
			and offeringTypeID = crd.offeringTypeID
		)

		delete from dbo.crd_requests
		where requestID in (select requestID from #tmpCreditToDelete where addedViaAward = 1)

		update dbo.crd_requests
		set creditAwarded = 0, creditValueAwarded = 0
		where requestID in (select requestID from #tmpCreditToDelete where addedViaAward = 0)
	END

	/* ****************** */
	/* Process Conditions */
	/* ****************** */
	-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
	declare @itemGroupUID uniqueidentifier, @conditionIDList varchar(max)
	SELECT @conditionIDList = COALESCE(@conditionIDList + ',', '') + cast(c.conditionID as varchar(10)) 
		from dbo.ams_virtualGroupConditions as c
		inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.expressionID
		inner join dbo.ev_events as ev on ev.eventID = replace(c.fieldcode,'e_','')
			and ev.eventID = @eventID
		inner join dbo.ev_registration as er on er.eventID = ev.eventID and er.status = 'A'
		inner join dbo.ev_registrants as reg on reg.registrationID = er.registrationID and reg.status = 'A'
			and reg.attended = 1
		where left(c.fieldCode,2) = 'e_' 
		and e.expression in ('attended','awarded')
		group by c.conditionID
	IF @conditionIDList is not null
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList='', @conditionIDList=@conditionIDList, @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT


	IF OBJECT_ID('tempdb..#importEventsCreditData2') IS NOT NULL
		DROP TABLE #importEventsCreditData2
	IF OBJECT_ID('tempdb..#importEventCreditData') IS NOT NULL
		DROP TABLE #importEventCreditData

	RETURN 0
END TRY
BEGIN CATCH
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ev_uploadRegistrantCreditTemplate]') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].[ev_uploadRegistrantCreditTemplate]
GO
CREATE PROC dbo.ev_uploadRegistrantCreditTemplate
@eventID int,
@pathToExport varchar(200)

AS

IF OBJECT_ID('tempdb..#tmpCols') IS NOT NULL 
	DROP TABLE #tmpCols
IF OBJECT_ID('tempdb..##tmpColsFlat') IS NOT NULL 
	DROP TABLE ##tmpColsFlat
IF OBJECT_ID('tempdb..#tmpCurrentCredits') IS NOT NULL 
	DROP TABLE #tmpCurrentCredits
CREATE TABLE #tmpCols (autoID int IDENTITY(1,1), fieldName varchar(300), isCredit bit)
CREATE TABLE #tmpCurrentCredits (registrantID int, offeringTypeID int, creditValueAwarded decimal(6,2))
declare @tblCredits TABLE (row int IDENTITY(1,1), creditType varchar(300), offeringTypeID int)

-- membernumber, attended are required
IF @pathToExport is not null BEGIN
	insert into #tmpCols (fieldName, isCredit)
	values ('MemberName', 0)
END
insert into #tmpCols (fieldName, isCredit)
values ('MemberNumber', 0)
insert into #tmpCols (fieldName, isCredit)
values ('attended', 0)

-- get any credits offered
insert into @tblCredits	(creditType, offeringTypeID)
select distinct ca.authorityName + '/' + isnull(ast.ovTypeName,cat.typeName), ect.offeringTypeID
from dbo.crd_offerings AS ec
inner join dbo.crd_offeringTypes as ect on ect.offeringID = ec.offeringID
inner join dbo.crd_authoritySponsorTypes as ast on ast.ASTID = ect.ASTID
inner join dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
inner join dbo.crd_authorities as ca on ca.authorityID = cat.authorityID
WHERE ec.statusID = 4
and ec.eventID = @eventID

-- add credit columns
insert into #tmpCols (fieldName, isCredit)
select creditType, 1
from @tblCredits
order by row

IF @pathToExport is not null BEGIN
	-- put into temp table
	DECLARE @qry varchar(max)	
	select @qry = COALESCE(@qry + ', ', '') + quotename(fieldName) + ' varchar(max) '
		FROM #tmpCols
		order by autoid
	EXEC('CREATE TABLE ##tmpColsFlat (' + @qry + '); ALTER TABLE ##tmpColsFlat ADD registrantID int NULL; ')

	-- put existing registrants into table
	insert into ##tmpColsFlat (registrantID, membernumber, attended, memberName)
	select r.registrantID, m.membernumber, CASE WHEN r.status <> 'D' AND r.attended = 1 THEN 'Yes' ELSE 'No' END,
		m.lastname + ', ' + m.firstname
	from dbo.ev_registrants as r
	INNER JOIN dbo.ev_registration as rn ON rn.registrationID = r.registrationID AND rn.status = 'A'
	INNER JOIN dbo.ams_members as m2 on m2.memberID = r.memberID
	INNER JOIN dbo.ams_members as m on m.memberID = m2.activeMemberID
	where rn.eventID = @eventID
	and r.status = 'A'

	-- get credits
	insert into #tmpCurrentCredits (registrantID, offeringTypeID, creditValueAwarded)
	select r.registrantID, rc.offeringTypeID, rc.creditValueAwarded
	from dbo.ev_registrants as r
	INNER JOIN dbo.ev_registration as rn ON rn.registrationID = r.registrationID AND rn.status = 'A'
	INNER JOIN dbo.crd_requests as rc on rc.registrantID = r.registrantID
	where rn.eventID = @eventID
	and r.status = 'A'

	-- put existing credits into table
	declare @row int, @creditType varchar(300), @offeringTypeID int
	select @row = min(row) from @tblCredits
	while @row is not null BEGIN
		SELECT @creditType = creditType, @offeringTypeID = offeringTypeID from @tblCredits where row = @row

		select @qry = '
			UPDATE flat
			set ' + quoteName(@creditType) + ' = cc.creditValueAwarded
			FROM ##tmpColsFlat as flat
			INNER JOIN #tmpCurrentCredits as cc on cc.registrantID = flat.registrantID
			WHERE cc.offeringTypeID = ' + cast(@offeringTypeID as varchar(10))
		EXEC(@qry)

		select @row = min(row) from @tblCredits where row > @row
	end

	EXEC('ALTER TABLE ##tmpColsFlat DROP COLUMN registrantID;')

	-- export data and return fields
	set @qry = 'select * from ##tmpColsFlat ORDER BY MemberNumber'
	EXEC dbo.up_exportCSV @csvfilename=@pathToExport, @sql=@qry
END
ELSE BEGIN
	select autoID, fieldName
	from #tmpCols
	order by autoID
END

IF OBJECT_ID('tempdb..#tmpCols') IS NOT NULL 
	DROP TABLE #tmpCols
IF OBJECT_ID('tempdb..##tmpColsFlat') IS NOT NULL 
	DROP TABLE ##tmpColsFlat
IF OBJECT_ID('tempdb..#tmpCurrentCredits') IS NOT NULL 
	DROP TABLE #tmpCurrentCredits
GO
