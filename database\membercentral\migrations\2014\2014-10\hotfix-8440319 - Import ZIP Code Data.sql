/*
Download files from:
https://zipcodedownload.com/Products/Product/Z5Commercial/Standard/Overview
https://zipcodedownload.com/Products/Product/C6Commercial/Standard/Overview

or combined (code below works with combined):
https://zipcodedownload.com/Products/Product/Z5C6Bundle/Standard/Overview/
*/

USE [dataTransfer]
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[tmp_zipCodes]') AND type in (N'U'))
DROP TABLE [dbo].tmp_zipCodes
GO

CREATE TABLE [dbo].tmp_zipCodes(
	[country] [varchar](12) NOT NULL,
	[zipcode] [varchar](7) NOT NULL,
	[ZIPType] [char](1) NULL,
	[cityName] [varchar](64) NULL,
	[cityType] [char](1) NULL,
	[countyName] [varchar](64) NULL,
	[countyFIPS] [char](5) NULL,
	[stateName] [varchar](64) NULL,
	[stateAbbr] [char](2) NULL,
	[stateFIPS] [char](2) NULL,
	[MSACode] [char](4) NULL,
	[areaCode] [varchar](16) NULL,
	[timeZone] [varchar](16) NULL,
	[UTC] [decimal](3, 1) NULL,
	[DST] [char](1) NULL,
	[latitude] [decimal](9, 6) NOT NULL,
	[longitude] [decimal](9, 6) NOT NULL
) ON [PRIMARY]
GO
declare @qry varchar(1000)
SELECT @qry = 'BULK INSERT dbo.tmp_zipCodes FROM "e:\temp\USA 5-digit & Canadian 6-digit.txt" WITH (FIELDTERMINATOR = '''+ char(9) + ''', FIRSTROW = 2);'
EXEC(@qry)
GO

ALTER TABLE dbo.tmp_zipCodes ADD stateID int NULL;
ALTER TABLE dbo.tmp_zipCodes ADD countryID int NULL;
GO

UPDATE dbo.tmp_zipCodes set countryID = 1 where country = 'USA'
UPDATE dbo.tmp_zipCodes set countryID = 2 where country = 'CAN'
GO

update tz
set tz.stateID = s.stateID
from dbo.tmp_zipCodes as tz
inner join membercentral.dbo.ams_states as s on s.name = case
	when tz.StateName = 'Armed Forces - Europe/Africa/Canada' then 'Armed Forces Europe'
	when tz.StateName = 'Armed Forces - Americas' then 'Armed Forces America'
	when tz.StateName = 'Armed Forces - Pacific' then 'Armed Forces Pacific'
	when tz.StateName = 'Newfoundland' then 'Newfoundland and Labrador'
	else tz.StateName end
	and s.countryID = tz.countryID
where tz.stateID is null
GO

TRUNCATE TABLE membercentral.dbo.ams_zipCodes
GO

insert into membercentral.dbo.ams_zipCodes (zipcode, ZIPType, cityName, cityType, countyName, countyFIPS, stateID, stateFIPS, countryID, MSACode, areaCode, timeZone, UTC, DST, latitude, longitude)
select tmp.zipcode, tmp.ZIPType, tmp.cityName, tmp.cityType, tmp.countyName, tmp.countyFIPS, tmp.stateID, tmp.stateFIPS, tmp.countryID, tmp.MSACode, tmp.areaCode, tmp.timeZone, tmp.UTC, tmp.DST, tmp.latitude, tmp.longitude
from dataTransfer.dbo.tmp_zipCodes as tmp
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[tmp_zipCodes]') AND type in (N'U'))
DROP TABLE [dbo].tmp_zipCodes
GO
