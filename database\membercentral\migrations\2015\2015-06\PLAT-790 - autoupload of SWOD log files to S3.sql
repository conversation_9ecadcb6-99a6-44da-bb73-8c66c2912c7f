use seminarweb;
GO

ALTER PROC dbo.swod_cleanupLogTables
@pathToLogs varchar(400)

AS

SET NOCOUNT ON

declare @awsS3prefix varchar(100), @s3bucket varchar(100)
set @awsS3prefix = 'swodaccesslogs/'
set @s3bucket = 'seminarweb'

/* *********************************************************************************** */
/* Write out activityLog, debugLog older than 1 day as text files and clear from table */
/* *********************************************************************************** */
IF OBJECT_ID('tempdb..#tmpLogAccess') IS NOT NULL 
	DROP TABLE #tmpLogAccess
CREATE TABLE #tmpLogAccess (logAccessID int PRIMARY KEY, folder char(4), debug bit DEFAULT(0), activity bit DEFAULT(0),debugLogPath varchar(250), activityLogPath varchar(250),debugLogS3Key varchar(250), activityLogS3Key varchar(250));

-- limit to this range so the query runs faster
declare @oneDayAgo datetime, @twoDaysAgo datetime
set @oneDayAgo = dateadd(d,-1,getdate()) 
set @twoDaysAgo = dateadd(d,-2,getdate()) 

insert into #tmpLogAccess (logAccessID, folder)
select logaccessid, right('0000' + cast(logAccessID%1000 as varchar(4)),4)
from dbo.tbllogaccessswod
where datelastmodified between @twoDaysAgo and @oneDayAgo

update tmp set 
    tmp.debug = trialsmith.dbo.fn_writefile(@pathToLogs + tmp.folder + '\' + cast(tmp.logAccessID as varchar(10)) + '-debug.txt',tbl.debuglog,1),
    tmp.debugLogPath = @pathToLogs + tmp.folder + '\' + cast(tmp.logAccessID as varchar(10)) + '-debug.txt',
    tmp.debugLogS3Key = @awsS3prefix + tmp.folder + '/' + cast(tmp.logAccessID as varchar(10)) + '-debug.txt'
from #tmpLogAccess as tmp
inner join dbo.tbllogaccessswod as tbl on tbl.logAccessID = tmp.logAccessID
where tbl.debuglog is not null 

update tmp set 
    tmp.activity = trialsmith.dbo.fn_writefile(@pathToLogs + tmp.folder + '\' + cast(tmp.logAccessID as varchar(10)) + '-activity.txt',tbl.activityLog,1),
    tmp.activityLogPath = @pathToLogs + tmp.folder + '\' + cast(tmp.logAccessID as varchar(10)) + '-activity.txt',
    tmp.activityLogS3Key = @awsS3prefix + tmp.folder + '/' + cast(tmp.logAccessID as varchar(10)) + '-activity.txt'
from #tmpLogAccess as tmp
inner join dbo.tbllogaccessswod as tbl on tbl.logAccessID = tmp.logAccessID
where tbl.activityLog is not null 

-- add to Amazon S3 upload queue
insert into membercentral.platformQueue.dbo.queue_S3Upload (s3bucketName, objectKey, filePath, deleteOnSuccess)
select @s3bucket, debugLogS3Key, debugLogPath, 0
from #tmpLogAccess as tmp
where tmp.debug = 1

insert into membercentral.platformQueue.dbo.queue_S3Upload (s3bucketName, objectKey, filePath, deleteOnSuccess)
select @s3bucket, activityLogS3Key, activityLogPath, 0
from #tmpLogAccess as tmp
where tmp.activity = 1

update tbl
set tbl.debuglog = null
from #tmpLogAccess as tmp
inner join dbo.tbllogaccessswod as tbl on tbl.logAccessID = tmp.logAccessID
where tmp.debug = 1

update tbl
set tbl.activityLog = null
from #tmpLogAccess as tmp
inner join dbo.tbllogaccessswod as tbl on tbl.logAccessID = tmp.logAccessID
where tmp.activity = 1

IF OBJECT_ID('tempdb..#tmpLogAccess') IS NOT NULL 
	DROP TABLE #tmpLogAccess


SET NOCOUNT OFF

GO

