use trialsmith;
GO
ALTER PROC [dbo].[account_memberInit]
@depomemberdataid int

AS

-- FirmPlanInfo
SELECT top 1 fp.firmPlanID, fp.firmPlan, fpl2.isMaster, fpl.depoMemberDataID
FROM tlaFirmPlan AS fp 
INNER JOIN tlaFirmPlanLink AS fpl ON fp.firmPlanID = fpl.firmPlanID 
INNER JOIN tlaFirmPlanLink AS fpl2 ON fpl.firmPlanID = fpl2.firmPlanID
WHERE fpl.isMaster = 1
AND fpl2.depoMemberDataID = @depomemberdataid

-- NoteTypes
SELECT notetypeid, description
FROM customerNoteTypes
order by description

-- qCreditCard
Select payProfileID, depomemberdataid, orgcode, detail, customerProfileID, paymentProfileID, dateAdded, declined, cardType
From dbo.ccMemberPaymentProfiles
Where depomemberdataid = @depomemberdataid
order by orgcode

-- linkedSites
select tla.shortName, tla.state, tla.description, tla.url as tlaURL, o.orgmemberdataid, o.membernumber, o.firstname, o.lastname, o.email, o.telephone, o.fax, o.isAdmin, o.firm, o.status, o.orgCode
From depotla tla, orgmemberdata o
where o.orgcode = tla.state 
and o.depomemberdataid = @depomemberdataid
order by tla.description

-- linkedSitesNew
select o.orgcode, o.orgname, s.siteCode, sh.hostname as mainHostName, s.siteName, m.memberID, m.membernumber, mnp.mnpID, m.firstname, m.lastname, m.status, me.email, np.networkID
from membercentral.membercentral.dbo.ams_networkProfiles as np
inner join membercentral.membercentral.dbo.ams_membernetworkProfiles as mnp on mnp.profileID = np.profileID and mnp.status = 'A'
inner join membercentral.membercentral.dbo.ams_members as m on m.memberID = mnp.memberID 
	and m.memberID = m.activeMemberID
	and m.status <> 'D'
inner join membercentral.membercentral.dbo.sites as s on s.siteID = mnp.siteID
inner join membercentral.membercentral.dbo.organizations as o on o.orgID = s.orgID
inner join membercentral.membercentral.dbo.siteHostnames as sh on sh.siteID = s.siteID and sh.hostnameID = sh.mainhostnameID
left outer join membercentral.membercentral.dbo.ams_memberEmails as me
	inner join membercentral.membercentral.dbo.ams_memberEmailTypes as met on met.emailTypeID = me.emailTypeID and met.emailTypeOrder = 1
	on me.memberID = m.memberID
where np.depomemberdataid = @depomemberdataid
AND np.status = 'A'
order by s.siteCode

-- new platform accounts in waiting
select m.memberid, m.firstname, m.lastname, m.membernumber, m.status, me.email, o.orgcode, o.orgname
from membercentral.membercentral.dbo.ams_members as m
inner join dbo.depomemberdata as d on d.MCmemberIDtemp = m.memberid and d.depomemberdataid = @depomemberdataid
inner join membercentral.membercentral.dbo.organizations as o on o.orgID = m.orgID
left outer join membercentral.membercentral.dbo.ams_memberEmails as me
	inner join membercentral.membercentral.dbo.ams_memberEmailTypes as met on met.emailTypeID = me.emailTypeID and met.emailTypeOrder = 1
	on me.memberID = m.memberID
where m.memberid = m.activeMemberID
and m.status <> 'D'
order by orgCode

-- orgCodesAll
SELECT url, state, shortName, description, isPortalCreated
FROM depotla 
order by description, state

-- getnotes
SELECT NoteID, Note, DateEntered
FROM CustomerNotes n
WHERE depomemberdataid = @depomemberdataid
ORDER BY DateEntered DESC


-- loginHistory

declare @logins TABLE (autoID int IDENTITY(1,1), depomemberdataid int, serverID int, dateentered datetime, APPCode varchar(50), useragent varchar(3000), ipaddress varchar(100), platform varchar(20))

insert into @logins (depomemberdataid, serverID, dateentered, APPCode, useragent, ipaddress, platform)
SELECT TOP 101 depomemberdataid, serverID, dateentered, application as APPCode, useragent, ipaddress, 'TLASITES' as platform
FROM userloginhistory
WHERE depomemberdataid = @depomemberdataid
ORDER BY dateentered DESC

insert into @logins (depomemberdataid, serverID, dateentered, APPCode, useragent, ipaddress, platform)
SELECT TOP 101 np.depomemberdataid, ml.serverID, ml.dateentered, s.siteCode as APPCode, ua.useragent, ss.ipaddress, 'MEMBERCENTRAL' as platform
FROM membercentral.membercentral.dbo.ams_networkProfiles np
inner join membercentral.membercentral.dbo.ams_memberNetworkProfiles mnp
	on mnp.profileID = np.profileID
	and np.depomemberdataid = @depomemberdataID
	and np.status = 'A' 
	and mnp.status = 'A' 
inner join membercentral.platformstats.dbo.ams_memberLogins ml
	on ml.memberID = mnp.memberID
	and ml.siteID = mnp.siteID
inner join membercentral.platformstats.dbo.statsSessions ss
	on ss.sessionID = ml.statsSessionID
inner join membercentral.platformstats.dbo.statsUserAgents ua
	on ss.useragentID = ua.useragentID
inner join membercentral.membercentral.dbo.sites s
	on s.siteID = ss.siteID
ORDER BY ml.dateentered DESC


SELECT COUNT(autoID) as LoginCount, MAX(dateentered) AS LastLoginDate
FROM @logins
GROUP BY depomemberdataid

-- fastcase history
select count(*) as accessCount, max(dateentered) as lastAcccess
FROM (
	SELECT sah.dateentered
	FROM platformstats.dbo.statsAppHits AS sah 
	INNER JOIN platformstats.dbo.statsAppSections AS sas ON sah.appSectionID = sas.appSectionID 
	INNER JOIN platformstats.dbo.statsAppNames AS san ON sas.appNameID = san.appNameID 
	INNER JOIN platformstats.dbo.statsSessions AS ss ON sah.sessionid = ss.sessionid
	WHERE ss.depomemberdataid = @depomemberdataid
	AND sas.name = 'caselaw'
	AND san.name = 'caselaw'

	union

	SELECT sah.dateentered
	FROM membercentral.membercentral.dbo.ams_networkProfiles np
	inner join membercentral.membercentral.dbo.ams_memberNetworkProfiles mnp
		on mnp.profileID = np.profileID
		and np.depomemberdataid = @depomemberdataid
	INNER JOIN membercentral.platformstats.dbo.statsSessions AS ss
		ON ss.memberID = mnp.memberID
		and mnp.siteID = ss.siteID
	inner join membercentral.platformstats.dbo.statsAppHits AS sah 
		on ss.sessionID = sah.sessionID
	INNER JOIN membercentral.platformstats.dbo.statsAppSections AS sas
		ON sah.appSectionID = sas.appSectionID 
		AND sas.name = 'caselaw'
	INNER JOIN membercentral.platformstats.dbo.statsAppNames AS san
		ON sas.appNameID = san.appNameID 
		AND san.name = 'caselaw'


) stats


RETURN 0
GO
