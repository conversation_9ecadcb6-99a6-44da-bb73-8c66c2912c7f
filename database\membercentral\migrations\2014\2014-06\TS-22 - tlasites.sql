use trialsmith
GO

ALTER TABLE dbo.depoTLA ADD
	includeInSWEmailMarketing bit NOT NULL CONSTRAINT DF_depoTLA_includeInSWEmailMarketing DEFAULT 0
GO


update depotla set
	includeInSWEmailMarketing = 1
where state in ('AB','AL','AP','AR','AZ','BC','CA','CC','CO','DC','DE','FL','GA','HTX','IA','ID','IL','IN','KS','KY','LA','LO','MA','MD','ME','MI','MO','MS','MT','NC','NE','NH','NJ','NV','OH','ON','OR','RI','SA','SC','SF','SK','TN','TS','TX','UT','VA','VT','WA','WI','WP','WV','WY')

GO