USE [memberCentral]
GO

ALTER PROCEDURE [dbo].[cms_createApplicationInstanceAdmin]
	@siteid int,
	@languageID int,
	@sectionID int,
	@isVisible bit,
	@pageName varchar(50),
	@pageTitle varchar(200),
	@pagedesc varchar(400),
	@zoneID int,
	@pageTemplateID int,
	@pageModeID int,
	@pgResourceTypeID int,
	@pgParentResourceID int = null,
	@allowReturnAfterLogin bit,
	@applicationInstanceName varchar(100),
	@applicationInstanceDesc varchar(200),
	@applicationInstanceID int OUTPUT,
	@siteResourceID int OUTPUT,
	@pageID int OUTPUT
AS

-- null OUTPUT vars
SELECT @applicationInstanceID = null, @siteResourceID = null, @pageID = null

DECLARE @appCreatedSectionResourceTypeID int, @applicationTypeID int, @rootSectionID int, @rc int
DECLARE @documentSectionName varchar(50), @enteredByMemberID int

select @appCreatedSectionResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedSection')
select @applicationTypeID = applicationTypeID from cms_applicationTypes where applicationTypeName = 'Admin'
select @enteredByMemberID=activeMemberID from ams_members where orgid = 1 and lastname like 'System'
	
BEGIN TRAN

exec @rc = dbo.cms_createApplicationInstance
		@siteid = @siteid,
		@languageID = @languageID,
		@sectionID = @sectionID,
		@applicationTypeID = @applicationTypeID,
		@isVisible = @isVisible,
		@pageName = @pageName,
		@pageTitle = @pageTitle,
		@pagedesc = @pagedesc,
		@zoneID = @zoneID,
		@pageTemplateID = @pageTemplateID,
		@pageModeID = @pageModeID,
		@pgResourceTypeID = @pgResourceTypeID,
		@pgParentResourceID = @pgParentResourceID,
		@allowReturnAfterLogin = @allowReturnAfterLogin,
		@applicationInstanceName = @applicationInstanceName,
		@applicationInstanceDesc = @applicationInstanceDesc,
		@applicationInstanceID = @applicationInstanceID OUTPUT,
		@siteResourceID = @siteResourceID OUTPUT,
		@pageID = @pageID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

select @documentSectionName = 'MemberDocument'

exec @rc = dbo.cms_createPageSection
		@siteID = @siteID, 
		@sectionResourceTypeID = @appCreatedSectionResourceTypeID, 
		@ovTemplateID = NULL,
		@ovTemplateIDMobile=NULL,
		@ovModeID = NULL, 
		@parentSectionID = @sectionID, 
		@sectionName = @documentSectionName, 
		@sectionCode = @documentSectionName,
		@inheritPlacements = 1,
		@sectionID = @rootSectionID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- update parentSiteResourceID of section
UPDATE sr
SET sr.parentSiteResourceID = @siteResourceID
FROM cms_pageSections s
INNER JOIN cms_siteResources sr on s.siteResourceID = sr.siteResourceID
	AND s.sectionID = @rootSectionID
	IF @@ERROR <> 0 GOTO on_error

-- update settings xml
UPDATE dbo.cms_applicationInstances
SET settingsXML = '<settings><setting name="memberDocumentSectionID" value="' + cast(@rootSectionID as varchar(8)) + + '" /><setting name="showMemberDocuments" value="false" /><setting name="showNotes" value="false" /><setting name="showRelationships" value="false" /><setting name="showMemberHistory" value="false" /><setting name="showSubscriptions" value="false" /><setting name="showReferrals" value="false" /><setting name="showApptTracker" value="false" /><setting name="showMemberPhotosInSearchResults" value="true" /><setting name="numPerPageInSearchResults" value="25" /><setting name="reportHeaderImage" value="gif" /><setting name="useAccrualAccounting" value="false" /></settings>'
WHERE applicationInstanceID = @applicationInstanceID
	IF @@ERROR <> 0 GOTO on_error

-- create the suite of tools
EXEC @rc = dbo.createAdminSuite @siteID
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- create admin member lookup fieldset
declare @memberAdminSRID int, @fieldSetID int, @fieldID int, @useID int
select @memberAdminSRID = dbo.fn_getSiteResourceIDForResourceType('MemberAdmin',@siteID)
EXEC @rc = dbo.ams_createMemberFieldSet @siteID=@siteID, @fieldsetName='Member Admin Search Form', @nameformat='LSXPFM', @showHelp=0, @enteredByMemberID=@enteredByMemberID, @defaultLanguageID=@languageID, @fieldsetID=@fieldSetID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldSetID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_firstname', @fieldLabel='First Name', @fieldDescription='', @isRequired=0, @isGrouped=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_lastname', @fieldLabel='Last Name', @fieldDescription='', @isRequired=0, @isGrouped=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_company', @fieldLabel='Company', @fieldDescription='', @isRequired=0, @isGrouped=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_membernumber', @fieldLabel='Member Number', @fieldDescription='', @isRequired=0, @isGrouped=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = ams_createMemberFieldUsage @siteResourceID=@memberAdminSRID, @fieldsetID=@fieldSetID, @area='search', @createSiteResourceID=0, @useID=@useID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @useID = 0 GOTO on_error

EXEC @rc = dbo.ams_createMemberFieldSet @siteID=@siteID, @fieldsetName='Member Admin Search Results', @nameformat='LSXPFM', @showHelp=0, @enteredByMemberID=@enteredByMemberID, @defaultLanguageID=@languageID, @fieldsetID=@fieldSetID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldSetID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_prefix', @fieldLabel='Prefix', @fieldDescription='', @isRequired=0, @isGrouped=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_middlename', @fieldLabel='Middle Name', @fieldDescription='', @isRequired=0, @isGrouped=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_suffix', @fieldLabel='Suffix', @fieldDescription='', @isRequired=0, @isGrouped=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = ams_createMemberFieldUsage @siteResourceID=@memberAdminSRID, @fieldsetID=@fieldSetID, @area='results', @createSiteResourceID=0, @useID=@useID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @useID = 0 GOTO on_error

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO




ALTER PROC [dbo].[cms_createDefaultFieldsets]
@siteID int

AS

DECLARE @rc int, @srid int, @useID int
DECLARE @fsid1 int, @fsid2 int, @fsid3 int, @fsid4 int, @fsid5 int, @fsid6 int, @fieldID int, @enteredByMemberID int
DECLARE @fieldcode varchar(30), @fieldLabel varchar(100)
DECLARE @addressTypeID int, @addressType varchar(20)
DECLARE @xml varchar(max)

select @enteredByMemberID=activeMemberID from ams_members where orgid = 1 and lastname like 'System'

BEGIN TRAN

	-- account locator
	EXEC @rc = dbo.ams_createMemberFieldset @siteID, 'Account Locator Search Form', 'LSXPFM', 0, @enteredByMemberID, 1, @fsid1 OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fsid1 = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid1, 'm_lastname', 'Last Name', '', 1, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error

	EXEC dbo.ams_createMemberFieldset @siteID, 'Account Locator Search Results', 'LSXPFM', 0, @enteredByMemberID, 1, @fsid2 OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fsid2 = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid2, 'm_firstname', 'First Name', '', 0, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid2, 'm_lastname', 'Last Name', '', 0, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid2, 'm_company', 'Company', '', 0, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error

	EXEC dbo.ams_createMemberFieldset @siteID, 'Account Locator New Account Form', 'LSXPFM', 0, @enteredByMemberID, 1, @fsid3 OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fsid3 = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid3, 'm_firstname', 'First Name', '', 1, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid3, 'm_lastname', 'Last Name', '', 1, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	select TOP 1 @fieldcode = 'me_' + cast(met.emailTypeID as varchar(4)) + '_email',
		   @fieldLabel = met.emailType
		from dbo.sites as s 
		inner join dbo.ams_memberEmailTypes as met on met.orgID = s.orgID and met.emailTypeOrder = 1
		where s.siteid = @siteid
		IF @@ERROR <> 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid3, @fieldcode, @fieldLabel, '', 1, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	select TOP 1 @addressTypeID = mat.addressTypeID, @addressType = mat.addressType
		from dbo.sites as s		
		inner join dbo.ams_memberAddressTypes as mat on mat.orgID = s.orgID and mat.addressTypeOrder = 1
		where s.siteID = @siteID
		IF @@ERROR <> 0 GOTO on_error
	IF @addressTypeID > 0 BEGIN
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_address1'
		SELECT @fieldLabel = 'Address'
		EXEC @rc = dbo.ams_createMemberField @fsid3, @fieldcode, @fieldLabel, '', 1, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_city'
		SELECT @fieldLabel = 'City'
		EXEC @rc = dbo.ams_createMemberField @fsid3, @fieldcode, @fieldLabel, '', 1, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_stateprov'
		SELECT @fieldLabel = 'State'
		EXEC @rc = dbo.ams_createMemberField @fsid3, @fieldcode, @fieldLabel, '', 1, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_postalcode'
		SELECT @fieldLabel = 'Postal Code'
		EXEC @rc = dbo.ams_createMemberField @fsid3, @fieldcode, @fieldLabel, '', 1, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_country'
		SELECT @fieldLabel = 'Country'
		EXEC @rc = dbo.ams_createMemberField @fsid3, @fieldcode, @fieldLabel, '', 1, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	END

	select @srid = siteResourceID from dbo.sites where siteID = @siteID
	EXEC @rc = dbo.ams_createMemberFieldUsage @srid, @fsid1, 'search', 0, @useID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @useID = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberFieldUsage @srid, @fsid2, 'results', 0, @useID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @useID = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberFieldUsage @srid, @fsid3, 'newacct', 0, @useID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @useID = 0 GOTO on_error

	-- member main
	declare @memberMainRID int
	declare @minEmailTypeID int
	declare @minWebsiteTypeID int
	select top 1 @memberMainRID = sr.siteResourceID 
		from dbo.cms_siteResources as sr
		inner join dbo.cms_siteResourceTypes as srt on srt.resourceTypeID = sr.resourceTypeID and srt.resourceType = 'MemberAdmin'
		where sr.siteID = @siteID
		and sr.siteResourceStatusID = dbo.fn_getResourceStatusID('Active')
	IF @memberMainRID is not null BEGIN
		EXEC @rc = dbo.ams_createMemberFieldset @siteID, 'Member Main Display', 'LSXPFM', 0, @enteredByMemberID, 1, @fsid4 OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fsid4 = 0 GOTO on_error

		select @minEmailTypeID = min(met.emailTypeID) 
			from dbo.sites as s 
			inner join dbo.ams_memberEmailTypes as met on met.orgID = s.orgID
			where s.siteid = @siteID
		while @minEmailTypeID is not null BEGIN
			select @fieldcode = 'me_' + cast(met.emailTypeID as varchar(4)) + '_email',
				   @fieldLabel = met.emailType
				from dbo.sites as s 
				inner join dbo.ams_memberEmailTypes as met on met.orgID = s.orgID
				where s.siteid = @siteid
				and met.emailTypeID = @minEmailTypeID
		      
			EXEC @rc = dbo.ams_createMemberField @fsid4, @fieldcode, @fieldLabel, '', 1, 0, @fieldID OUTPUT
				IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error

			select @minEmailTypeID = min(met.emailTypeID) 
				from dbo.sites as s 
				inner join dbo.ams_memberEmailTypes as met on met.orgID = s.orgID
				where s.siteid = @siteID 
				and met.emailTypeID > @minEmailTypeID
		END

		select @minWebsiteTypeID = min(wet.websiteTypeID) 
			from dbo.sites as s 
			inner join dbo.ams_memberWebsiteTypes as wet on wet.orgID = s.orgID
			where s.siteid = @siteID
		while @minWebsiteTypeID is not null BEGIN
			select @fieldcode = 'mw_' + cast(wet.websiteTypeID as varchar(4)) + '_website',
				   @fieldLabel = wet.websiteType
				from dbo.sites as s 
				inner join dbo.ams_memberWebsiteTypes as wet on wet.orgID = s.orgID
				where s.siteid = @siteid
				and wet.websiteTypeID = @minWebsiteTypeID
		      
			EXEC @rc = dbo.ams_createMemberField @fsid4, @fieldcode, @fieldLabel, '', 1, 0, @fieldID OUTPUT
				IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error

			select @minWebsiteTypeID = min(wet.websiteTypeID) 
				from dbo.sites as s 
				inner join dbo.ams_memberWebsiteTypes as wet on wet.orgID = s.orgID
				where s.siteid = @siteID 
				and wet.websiteTypeID > @minWebsiteTypeID
		END

		EXEC @rc = dbo.ams_createMemberFieldUsage @memberMainRID, @fsid4, 'main', 0, @useID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @useID = 0 GOTO on_error

	END

	-- event reg download
	EXEC @rc = dbo.ams_createMemberFieldset @siteID, 'Events Download Registrants Standard', 'LSXPFM', 0, @enteredByMemberID, 1, @fsid5 OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fsid5 = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid5, 'm_firstname', 'First Name', '', 0, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid5, 'm_lastname', 'Last Name', '', 0, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid5, 'm_company', 'Company', '', 0, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	select TOP 1 @addressTypeID = mat.addressTypeID, @addressType = mat.addressType
		from dbo.sites as s		
		inner join dbo.ams_memberAddressTypes as mat on mat.orgID = s.orgID and mat.addressTypeOrder = 1
		where s.siteID = @siteID
		IF @@ERROR <> 0 GOTO on_error
	IF @addressTypeID > 0 BEGIN
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_address1'
		SELECT @fieldLabel = 'Address'
		EXEC @rc = dbo.ams_createMemberField @fsid5, @fieldcode, @fieldLabel, '', 0, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_city'
		SELECT @fieldLabel = 'City'
		EXEC @rc = dbo.ams_createMemberField @fsid5, @fieldcode, @fieldLabel, '', 0, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_stateprov'
		SELECT @fieldLabel = 'State'
		EXEC @rc = dbo.ams_createMemberField @fsid5, @fieldcode, @fieldLabel, '', 0, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_postalcode'
		SELECT @fieldLabel = 'Postal Code'
		EXEC @rc = dbo.ams_createMemberField @fsid5, @fieldcode, @fieldLabel, '', 0, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_country'
		SELECT @fieldLabel = 'Country'
		EXEC @rc = dbo.ams_createMemberField @fsid5, @fieldcode, @fieldLabel, '', 0, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	END
	select @minEmailTypeID = min(met.emailTypeID) 
		from dbo.sites as s 
		inner join dbo.ams_memberEmailTypes as met on met.orgID = s.orgID
		where s.siteid = @siteID
	while @minEmailTypeID is not null BEGIN
		select @fieldcode = 'me_' + cast(met.emailTypeID as varchar(4)) + '_email',
			   @fieldLabel = met.emailType
			from dbo.sites as s 
			inner join dbo.ams_memberEmailTypes as met on met.orgID = s.orgID
			where s.siteid = @siteid
			and met.emailTypeID = @minEmailTypeID
	      
		EXEC @rc = dbo.ams_createMemberField @fsid5, @fieldcode, @fieldLabel, '', 0, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error

		select @minEmailTypeID = min(met.emailTypeID) 
			from dbo.sites as s 
			inner join dbo.ams_memberEmailTypes as met on met.orgID = s.orgID
			where s.siteid = @siteID 
			and met.emailTypeID > @minEmailTypeID
	END

	-- group members download
	EXEC @rc = dbo.ams_createMemberFieldset @siteID, 'Groups Download Members Standard', 'LSXPFM', 0, @enteredByMemberID, 1, @fsid6 OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fsid6 = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid6, 'm_firstname', 'First Name', '', 0, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid6, 'm_lastname', 'Last Name', '', 0, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid6, 'm_company', 'Company', '', 0, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid6, 'm_membernumber', 'Member Number', '', 0, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error

	select TOP 1 @addressTypeID = mat.addressTypeID, @addressType = mat.addressType
		from dbo.sites as s		
		inner join dbo.ams_memberAddressTypes as mat on mat.orgID = s.orgID and mat.addressTypeOrder = 1
		where s.siteID = @siteID
		IF @@ERROR <> 0 GOTO on_error
	IF @addressTypeID > 0 BEGIN
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_address1'
		SELECT @fieldLabel = 'Address'
		EXEC @rc = dbo.ams_createMemberField @fsid6, @fieldcode, @fieldLabel, '', 0, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_city'
		SELECT @fieldLabel = 'City'
		EXEC @rc = dbo.ams_createMemberField @fsid6, @fieldcode, @fieldLabel, '', 0, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_stateprov'
		SELECT @fieldLabel = 'State'
		EXEC @rc = dbo.ams_createMemberField @fsid6, @fieldcode, @fieldLabel, '', 0, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_postalcode'
		SELECT @fieldLabel = 'Postal Code'
		EXEC @rc = dbo.ams_createMemberField @fsid6, @fieldcode, @fieldLabel, '', 0, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_country'
		SELECT @fieldLabel = 'Country'
		EXEC @rc = dbo.ams_createMemberField @fsid6, @fieldcode, @fieldLabel, '', 0, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	END
	select @minEmailTypeID = min(met.emailTypeID) 
		from dbo.sites as s 
		inner join dbo.ams_memberEmailTypes as met on met.orgID = s.orgID
		where s.siteid = @siteID
	while @minEmailTypeID is not null BEGIN
		select @fieldcode = 'me_' + cast(met.emailTypeID as varchar(4)) + '_email',
			   @fieldLabel = met.emailType
			from dbo.sites as s 
			inner join dbo.ams_memberEmailTypes as met on met.orgID = s.orgID
			where s.siteid = @siteid
			and met.emailTypeID = @minEmailTypeID
	      
		EXEC @rc = dbo.ams_createMemberField @fsid6, @fieldcode, @fieldLabel, '', 0, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error

		select @minEmailTypeID = min(met.emailTypeID) 
			from dbo.sites as s 
			inner join dbo.ams_memberEmailTypes as met on met.orgID = s.orgID
			where s.siteid = @siteID 
			and met.emailTypeID > @minEmailTypeID
	END

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO
