use membercentral
GO

CREATE PROC [dbo].[ev_importRegistrants_prepTable]
@importResult xml OUTPUT

AS

SET NOCOUNT ON

declare @creditColName varchar(50)
set @importResult = null


-- *********************************
-- ensure all required columns exist 
-- *********************************
BEGIN TRY
	-- this will get the columns that are required
	IF OBJECT_ID('tempdb..#tblEvRegOrgCols') IS NOT NULL 
		DROP TABLE #tblEvRegOrgCols
	CREATE TABLE #tblEvRegOrgCols (COLUMN_NAME sysname)

	insert into #tblEvRegOrgCols
	select 'rowID' union all
	select 'MemberNumber' union all
	select 'EventCode'

	-- this will get the columns that are actually in the import
	IF OBJECT_ID('tempdb..#tblEvRegImportCols') IS NOT NULL 
		DROP TABLE #tblEvRegImportCols
	CREATE TABLE #tblEvRegImportCols (ORDINAL_POSITION int, COLUMN_NAME sysname)

	insert into #tblEvRegImportCols
	select column_id, [name] 
	from tempdb.sys.columns 
	where object_id = object_id('tempdb..#mc_RegImport');

	INSERT INTO #tblEvRegErrors (msg)
	select 'The required column ' + org.column_name + ' is missing from your data.'
	from #tblEvRegOrgCols as org
	left outer join #tblEvRegImportCols as imp on imp.column_name = org.column_name
	where imp.ORDINAL_POSITION is null
		IF @@ROWCOUNT > 0 GOTO on_done

	delete from #tblEvRegImportCols 
	where column_name in (select COLUMN_NAME from #tblEvRegOrgCols)

	IF OBJECT_ID('tempdb..#tblEvRegOrgCols') IS NOT NULL 
		DROP TABLE #tblEvRegOrgCols
END TRY
BEGIN CATCH
	INSERT INTO #tblEvRegErrors (msg)
	VALUES ('Unable to validate file contains all required columns.')

	GOTO on_done
END CATCH


-- **********
-- prep table 
-- **********
BEGIN TRY
	-- add holding columns
	ALTER TABLE #mc_RegImport ADD MCMemberID int null, MCEventID int null, MCRegistrationID int null, 
		MCRegistrantID int null, newMCRegistrantID bit NOT NULL DEFAULT(1), 
		itemUID uniqueidentifier NOT NULL DEFAULT(NEWID());

	-- ensure rowID is an int
	ALTER TABLE #mc_RegImport ALTER COLUMN RowID int not null;

	-- add missing columns
	IF NOT EXISTS (select ORDINAL_POSITION from #tblEvRegImportCols where column_name = 'InternalNotes')
		ALTER TABLE #mc_RegImport ADD InternalNotes varchar(max) NULL;
	ELSE
		delete from #tblEvRegImportCols where column_name = 'InternalNotes'

	IF NOT EXISTS (select ORDINAL_POSITION from #tblEvRegImportCols where column_name = 'Attended')
		ALTER TABLE #mc_RegImport ADD Attended bit NULL;
	ELSE
		delete from #tblEvRegImportCols where column_name = 'Attended'

	IF NOT EXISTS (select ORDINAL_POSITION from #tblEvRegImportCols where column_name = 'DateRegistered')
		ALTER TABLE #mc_RegImport ADD DateRegistered datetime NULL;
	ELSE
		delete from #tblEvRegImportCols where column_name = 'DateRegistered'

	-- add missing credit columns
	select @creditColName = min(column_name) from #tblPossibleCreditCols
	while @creditColName is not null begin
		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvRegImportCols where column_name = @creditColName)
			EXEC('ALTER TABLE #mc_RegImport ADD ' + @creditColName + ' decimal(6,2) NULL;')
		ELSE
			delete from #tblEvRegImportCols where column_name = @creditColName
		select @creditColName = min(column_name) from #tblPossibleCreditCols where column_name > @creditColName
	end

END TRY
BEGIN CATCH
	INSERT INTO #tblEvRegErrors (msg)
	VALUES ('Unable to prepare import table by adding missing columns.')

	INSERT INTO #tblEvRegErrors (msg)
	VALUES (left(error_message(),300))

	GOTO on_done
END CATCH


-- *************
-- extra columns 
-- *************
BEGIN TRY
	-- extra columns should stop import to prevent accidental misnamed columns
	IF EXISTS (select column_name from #tblEvRegImportCols) BEGIN
		INSERT INTO #tblEvRegErrors (msg)
		SELECT TOP 100 PERCENT 'The imported file contains the extra column ' + cast(column_name as varchar(300)) + '. Remove this column from your file.' 
		FROM #tblEvRegImportCols  
		ORDER BY column_name
	END
END TRY
BEGIN CATCH
	INSERT INTO #tblEvRegErrors (msg)
	VALUES ('Unable to validate import file for extra columns.')

	INSERT INTO #tblEvRegErrors (msg)
	VALUES (left(error_message(),300))

	GOTO on_done
END CATCH


-- ************************
-- generate result xml file 
-- ************************
on_done:
	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblEvRegErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE)
	
	IF OBJECT_ID('tempdb..#tblEvRegOrgCols') IS NOT NULL 
		DROP TABLE #tblEvRegOrgCols
	IF OBJECT_ID('tempdb..#tblEvRegImportCols') IS NOT NULL 
		DROP TABLE #tblEvRegImportCols

RETURN 0

GO

CREATE PROC [dbo].[ev_importRegistrants_validate]
@siteid int, 
@importResult xml OUTPUT

AS

SET NOCOUNT ON

declare @orgID int, @creditColName varchar(50), @dynSQL nvarchar(max)
select @orgID=orgID from dbo.sites where siteID = @siteID
set @importResult = null


-- ***********
-- clean table 
-- ***********
BEGIN TRY
	-- delete empty rows
	delete from #mc_RegImport where MemberNumber is null and EventCode is null;
END TRY
BEGIN CATCH
	INSERT INTO #tblEvRegErrors (msg)
	VALUES ('Unable to clean import table.')

	INSERT INTO #tblEvRegErrors (msg)
	VALUES (left(error_message(),300))

	GOTO on_done
END CATCH


-- ****************
-- required columns 
-- ****************
BEGIN TRY
	-- no blank membernumbers
	update #mc_RegImport set membernumber = '' where membernumber is null;

	INSERT INTO #tblEvRegErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing MemberNumber.'
	FROM #mc_RegImport
	WHERE membernumber = ''
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- match on member
	update tmp 
	set tmp.MCMemberID = m.memberid
	from #mc_RegImport as tmp 
	inner join membercentral.dbo.ams_members as m on m.memberNumber = tmp.memberNumber
		and m.orgID = @orgID
		and m.memberID = m.activeMemberID
		and m.status <> 'D'
	
	-- check for missing members
	BEGIN TRY
		ALTER TABLE #mc_RegImport ALTER COLUMN MCMemberID int not null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvRegErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' does not match an existing member.'
		FROM #mc_RegImport
		WHERE MCMemberID IS NULL
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done
	END CATCH

	-- no blank eventCodes
	update #mc_RegImport set eventCode = '' where eventCode is null;

	INSERT INTO #tblEvRegErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing EventCode.'
	FROM #mc_RegImport
	WHERE eventCode = ''
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- eventcode must be at or under 15 chars
	INSERT INTO #tblEvRegErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an invalid EventCode.'
	FROM #mc_RegImport
	WHERE len(eventCode) > 15 
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- eventcode must match no more than 1 event
	INSERT INTO #tblEvRegErrors (msg)
	SELECT TOP 100 PERCENT 'EventCode ' + tmp.eventCode + ' matches more than 1 event in Control Panel.'
	FROM #mc_RegImport as tmp
	inner join membercentral.dbo.ev_events as e on e.reportCode = tmp.eventCode
		and e.status = 'A'
		and e.siteID = @siteID
	GROUP BY tmp.eventCode
	HAVING count(distinct e.eventID) > 1
	ORDER BY tmp.eventCode
		IF @@ROWCOUNT > 0 GOTO on_done

	-- match on eventCode
	update tmp 
	set tmp.MCEventID = e.eventID
	from #mc_RegImport as tmp 
	inner join membercentral.dbo.ev_events as e on e.reportCode = tmp.eventCode
		and e.status = 'A'
		and e.siteID = @siteID
	
	-- check for missing eventCodes
	BEGIN TRY
		ALTER TABLE #mc_RegImport ALTER COLUMN MCEventID int not null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvRegErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' does not match an existing EventCode.'
		FROM #mc_RegImport
		WHERE MCEventID IS NULL
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done
	END CATCH

	-- duplicate registrants
	INSERT INTO #tblEvRegErrors (msg)
	SELECT TOP 100 PERCENT 'MemberNumber ' + tmp2.MemberNumber + ' has duplicate entries for EventCode ' + tmp2.EventCode + '.'
	from (
		select MCMemberID, MCEventID, MemberNumber, EventCode
		from #mc_RegImport
		GROUP BY MCMemberID, MCEventID, MemberNumber, EventCode
		HAVING COUNT(*) > 1
	) as tmp2
	ORDER BY tmp2.MemberNumber, tmp2.EventCode
		IF @@ROWCOUNT > 0 GOTO on_done

	-- lookup registration
	update tmp 
	set tmp.MCRegistrationID = r.registrationID
	from #mc_RegImport as tmp 
	inner join membercentral.dbo.ev_registration as r on r.eventID = tmp.MCEventID 
		and r.status = 'A'
		and r.registrationTypeID = 1
	
	-- check for missing registration
	BEGIN TRY
		ALTER TABLE #mc_RegImport ALTER COLUMN MCRegistrationID int not null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvRegErrors (msg)
		SELECT distinct 'EventCode ' + EventCode + ' does not have registration enabled.'
		FROM #mc_RegImport
		WHERE MCRegistrationID IS NULL
		ORDER BY 1
			IF @@ROWCOUNT > 0 GOTO on_done
	END CATCH

	-- lookup registrantID
	update tmp 
	set tmp.MCRegistrantID = r.registrantID, tmp.newMCRegistrantID = 0
	from #mc_RegImport as tmp 
	inner join membercentral.dbo.ev_registrants as r on r.registrationID = tmp.MCRegistrationID
	inner join membercentral.dbo.ams_members as m on m.memberID = r.memberID
		and m.activeMemberID = tmp.MCMemberID
	where r.status = 'A'
END TRY
BEGIN CATCH
	INSERT INTO #tblEvRegErrors (msg)
	VALUES ('Unable to validate data in required columns.')

	INSERT INTO #tblEvRegErrors (msg)
	VALUES (left(error_message(),300))

	GOTO on_done
END CATCH


-- ****************
-- optional columns 
-- ****************
BEGIN TRY
	IF NOT EXISTS (
		SELECT st.name 
		FROM tempdb.sys.columns as sc 
		inner join sys.types as st on st.system_type_id=sc.system_type_id 
		WHERE [object_id] = OBJECT_ID('tempdb..#mc_RegImport')
		AND sc.name = 'Attended'
		AND st.name = 'bit') 
	BEGIN
		UPDATE #mc_RegImport set Attended = '1' where Attended in ('Yes','Y','TRUE');
		UPDATE #mc_RegImport set Attended = '0' where Attended in ('No','N','FALSE');
		UPDATE #mc_RegImport set Attended = null where Attended = '';
	END
	ALTER TABLE #mc_RegImport ALTER COLUMN Attended bit NULL;
END TRY
BEGIN CATCH
	INSERT INTO #tblEvRegErrors (msg)
	VALUES ('There are invalid values in the Attended column. This column supports YES or NO.')

	GOTO on_done
END CATCH

BEGIN TRY
	ALTER TABLE #mc_RegImport ALTER COLUMN DateRegistered datetime null;
END TRY
BEGIN CATCH
	INSERT INTO #tblEvRegErrors (msg)
	VALUES ('There are invalid dates in the DateRegistered column.')
END CATCH


-- **************
-- credit columns
-- **************
BEGIN TRY
	IF OBJECT_ID('tempdb..#tblEventCreditCols') IS NOT NULL 
		DROP TABLE #tblEventCreditCols
	CREATE TABLE #tblEventCreditCols (MCEventID int, column_name sysname, offeringTypeID int)

	INSERT INTO #tblEventCreditCols
	select tmp.MCEventID, tmpC.column_name, ot.offeringTypeID
	from #mc_RegImport as tmp
	inner join dbo.crd_offerings as co on co.eventID = tmp.MCEventID
	inner join dbo.crd_offeringTypes as ot on ot.offeringID = co.offeringID
	inner join #tblPossibleCreditCols as tmpC on tmpC.ASTID = ot.ASTID

	select @creditColName = min(column_name) from #tblPossibleCreditCols
	while @creditColName is not null begin
		BEGIN TRY
			IF NOT EXISTS (
				SELECT st.name 
				FROM tempdb.sys.columns as sc 
				inner join sys.types as st on st.system_type_id=sc.system_type_id 
				WHERE [object_id] = OBJECT_ID('tempdb..#mc_RegImport')
				AND sc.name = @creditColName
				AND st.name = 'decimal') 
			BEGIN
				EXEC('UPDATE #mc_RegImport set ' + @creditColName + ' = null where ' + @creditColName + ' = ''''');
			END

			EXEC('ALTER TABLE #mc_RegImport ALTER COLUMN ' + @creditColName + ' decimal(6,2) NULL;');
			EXEC('UPDATE #mc_RegImport set attended = 1 where ' + @creditColName + ' > 0;');
	
			select @dynSQL = '
				select distinct ''EventCode '' + tmp.eventCode + '' has credit awarded for ' + @creditColName + '. That credit type is not offered for this event.''
				from #mc_RegImport as tmp
				left outer join #tblEventCreditCols as c on c.MCEventID = tmp.MCEventID and c.column_name = ''' + @creditColName + '''
				where tmp.' + @creditColName + ' > 0
				and c.offeringTypeID is null
				order by 1'
			INSERT INTO #tblEvRegErrors (msg)
			EXEC(@dynSQL)	
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvRegErrors (msg)
			VALUES ('There are invalid values in the ' + @creditColName + ' column.')

			INSERT INTO #tblEvRegErrors (msg)
			VALUES (left(error_message(),300))
		END CATCH
		select @creditColName = min(column_name) from #tblPossibleCreditCols where column_name > @creditColName
	end
END TRY
BEGIN CATCH
	INSERT INTO #tblEvRegErrors (msg)
	VALUES ('Unable to validate import file for credit columns.')

	INSERT INTO #tblEvRegErrors (msg)
	VALUES (left(error_message(),300))

	GOTO on_done
END CATCH


-- ************************
-- generate result xml file 
-- ************************
on_done:
	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblEvRegErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE)
	
	IF OBJECT_ID('tempdb..#tblEventCreditCols') IS NOT NULL 
		DROP TABLE #tblEventCreditCols

RETURN 0

GO

CREATE PROC [dbo].[ev_importRegistrants_import]
@siteID int,
@ovAction char(1),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

declare @nowDate datetime, @rowCount int, @creditColName varchar(50), @colList varchar(max), @dynSQL nvarchar(max)
set @nowDate = getdate()


DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- add new registrants
	IF OBJECT_ID('tempdb..#tmpNewReg') IS NOT NULL 
		DROP TABLE #tmpNewReg
	CREATE TABLE #tmpNewReg (registrantID int, MCRegistrationID int, MCMemberID int)

	insert into dbo.ev_registrants (registrationID, memberID, recordedOnSiteID, rateID, dateRegistered, [status], attended, internalNotes)
		OUTPUT inserted.registrantID, inserted.registrationID, inserted.memberID
		INTO #tmpNewReg
	select MCRegistrationID, MCMemberID, @siteID, null, isnull(DateRegistered,@nowDate), 'A', isnull(Attended,0), isnull(InternalNotes,'')
	from #mc_RegImport
	where MCRegistrantID is null

	update tmp 
	set tmp.MCRegistrantID = tmp2.registrantID
	from #mc_RegImport as tmp 
	inner join #tmpNewReg as tmp2 on tmp2.MCMemberID = tmp.MCMemberID and tmp2.MCRegistrationID = tmp.MCRegistrationID
	where tmp.MCRegistrantID is null

	INSERT INTO #tblEvRegErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' could not be added as a registrant.'
	FROM #mc_RegImport
	WHERE MCRegistrantID IS NULL
	ORDER BY rowID
		IF @@ROWCOUNT > 0 RAISERROR('One or more registrants could not be added.', 16,1);


	-- update registrants based on override setting
	IF @ovAction = 'o'
		update r
		set r.internalNotes = isnull(tmp.InternalNotes,r.internalNotes),
			r.attended = isnull(tmp.Attended,r.attended),
			r.dateRegistered = isnull(tmp.DateRegistered,r.dateRegistered)
		from dbo.ev_registrants as r
		inner join #mc_RegImport as tmp on tmp.MCRegistrantID = r.registrantID
		where tmp.newMCRegistrantID = 0


	-- add credit for new registrants
	IF OBJECT_ID('tempdb..#tblEventCreditCols') IS NOT NULL 
		DROP TABLE #tblEventCreditCols
	CREATE TABLE #tblEventCreditCols (MCEventID int, column_name sysname, offeringTypeID int)

	INSERT INTO #tblEventCreditCols
	select tmp.MCEventID, tmpC.column_name, ot.offeringTypeID
	from #mc_RegImport as tmp
	inner join dbo.crd_offerings as co on co.eventID = tmp.MCEventID
	inner join dbo.crd_offeringTypes as ot on ot.offeringID = co.offeringID
	inner join #tblPossibleCreditCols as tmpC on tmpC.ASTID = ot.ASTID

	set @colList = null
	select @colList = COALESCE(@colList + ',', '') + column_name 
		from #tblEventCreditCols 
		group by column_name

	select @dynSQL = '
		select c.offeringTypeID, '''', getdate(), 1, unpvt.creditValue, 1, unpvt.MCRegistrantID
		from (
			select MCRegistrantID, MCEventID, column_name, creditValue
			from #mc_RegImport
			unpivot(creditValue for column_name in (' + @colList + ')) as pvt
			where newMCRegistrantID = 1
			and creditValue > 0
		) as unpvt
		inner join #tblEventCreditCols as c on c.column_name = unpvt.column_name and c.MCeventID = unpvt.MCEventID'
	insert into dbo.crd_requests (offeringTypeID, IDNumber, lastDateToComplete, creditAwarded, creditValueAwarded, addedViaAward, registrantID)
	EXEC(@dynSQL)


	-- update credits based on override setting
	IF @ovAction = 'o' BEGIN
		delete from dbo.crd_requests
		where registrantID in (
			select MCRegistrantID 
			from #mc_RegImport as tmp
			left outer join #tmpNewReg as tmpNR on tmpNR.registrantID = tmp.MCRegistrantID
			where tmpNR.MCMemberID is null
		)

		select @dynSQL = '
			select c.offeringTypeID, '''', getdate(), 1, unpvt.creditValue, 1, unpvt.MCRegistrantID
			from (
				select MCRegistrantID, MCEventID, column_name, creditValue
				from #mc_RegImport
				unpivot(creditValue for column_name in (' + @colList + ')) as pvt
				where newMCRegistrantID = 0		
				and creditValue > 0
			) as unpvt
			inner join #tblEventCreditCols as c on c.column_name = unpvt.column_name and c.MCeventID = unpvt.MCEventID'
		insert into dbo.crd_requests (offeringTypeID, IDNumber, lastDateToComplete, creditAwarded, creditValueAwarded, addedViaAward, registrantID)
		EXEC(@dynSQL)
	END


	IF @TranCounter = 0
		COMMIT TRAN;
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	INSERT INTO #tblEvRegErrors (msg)
	VALUES ('Unable to import registrants.')

	INSERT INTO #tblEvRegErrors (msg)
	VALUES (left(error_message(),300))
END CATCH


on_done:
	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblEvRegErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE)

	IF OBJECT_ID('tempdb..#tmpNewReg') IS NOT NULL 
		DROP TABLE #tmpNewReg
	IF OBJECT_ID('tempdb..#tblEventCreditCols') IS NOT NULL 
		DROP TABLE #tblEventCreditCols

RETURN 0

GO

CREATE PROC [dbo].[ev_importRegistrants]
@siteid int, 
@ovAction char(1),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

IF OBJECT_ID('tempdb..#tblEvRegErrors') IS NOT NULL 
	DROP TABLE #tblEvRegErrors
IF OBJECT_ID('tempdb..#tblPossibleCreditCols') IS NOT NULL 
	DROP TABLE #tblPossibleCreditCols
CREATE TABLE #tblEvRegErrors (rowid int IDENTITY(1,1), msg varchar(300))
CREATE TABLE #tblPossibleCreditCols (ASID int, column_name sysname, ASTID int)

declare @orgID int
select @orgID=orgID from dbo.sites where siteID = @siteID

insert into #tblPossibleCreditCols (ASID, column_name, ASTID)
select crdAS.ASID, crdA.authorityCode + '_' + crdAT.typeCode, crdAST.ASTID
from dbo.crd_sponsors as crdS
inner join dbo.crd_authoritySponsors as crdAS on crdAS.sponsorID = crdS.sponsorID
inner join dbo.crd_authorities as crdA on crdA.authorityID = crdAS.authorityID
inner join dbo.crd_authorityTypes as crdAT on crdAT.authorityID = crdA.authorityID
inner join dbo.crd_authoritySponsorTypes as crdAST on crdAST.ASID = crdAS.ASID and crdAST.typeID = crdAT.typeID
where crdS.orgID = @orgID


-- ensure temp table exists
IF OBJECT_ID('tempdb..#mc_RegImport') IS NULL BEGIN
	INSERT INTO #tblEvRegErrors (msg)
	VALUES ('Unable to locate the imported data for processing.')

	GOTO on_done
END

-- ensure all columns exist
set @importResult = null
EXEC dbo.ev_importRegistrants_prepTable @importResult=@importResult OUTPUT
IF @importResult.value('count(/import/errors/error)','int') > 0
	GOTO on_done

-- validate data
set @importResult = null
EXEC dbo.ev_importRegistrants_validate @siteID=@siteID, @importResult=@importResult OUTPUT
IF @importResult.value('count(/import/errors/error)','int') > 0
	GOTO on_done

-- import data
set @importResult = null
EXEC dbo.ev_importRegistrants_import @siteID=@siteID, @ovAction=@ovAction, @importResult=@importResult OUTPUT


-- cleanup
on_done:
	IF OBJECT_ID('tempdb..#tblEvRegErrors') IS NOT NULL 
		DROP TABLE #tblEvRegErrors
	IF OBJECT_ID('tempdb..#tblPossibleCreditCols') IS NOT NULL 
		DROP TABLE #tblPossibleCreditCols

RETURN 0

GO

