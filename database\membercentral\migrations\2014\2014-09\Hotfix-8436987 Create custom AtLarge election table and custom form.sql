USE [customApps]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
SET ANSI_PADDING ON
GO
CREATE TABLE [dbo].[CA_Elections_AtLarge](
	[voteID] [int] IDENTITY(1,1) NOT NULL,
	[memberID] [int] NOT NULL,
	[dateRecorded] [datetime] NOT NULL CONSTRAINT [DF_CA_Elections_AtLarge_dateRecorded]  DEFAULT (getdate()),
	[boardS] [varchar](max) NULL,
	[boardN] [varchar](max) NULL,
	[electionYear] [int] NOT NULL,
	[district] [int] NULL,
 CONSTRAINT [PK_CA_Elections_AtLarge] PRIMARY KEY CLUSTERED 
(
	[voteID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
SET ANSI_PADDING OFF





USE [membercentral]
GO
SET ANSI_PADDING ON
GO
-- add custom page
declare @siteID int, @sectionID int, @zoneID int, @pgResourceTypeID int, @applicationInstanceID int, @siteResourceID int, 
	@pageID int, @resourceRightID int
		      
select @siteID = siteID from sites where siteCode = 'CA'
select @sectionID = sectionID from cms_pageSections where siteID = @siteID and sectionName = 'Elections'
SELECT @zoneID = dbo.fn_getZoneID('Main')
SELECT @pgResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedPage')


EXEC dbo.cms_createApplicationInstance @siteid=@siteID, @languageID=1, @sectionID=@sectionID, @applicationTypeID=18,
	@isVisible=1, @pageName='electionVoteAtLarge', @pageTitle='Election Vote At Large', @pagedesc='Election Vote At Large', @zoneID=@zoneID, @pageTemplateID=null,
	@pageModeID=2, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1,
	@applicationInstanceName='Election Vote At Large', @applicationInstanceDesc='Election Vote At Large', @applicationInstanceID=@applicationInstanceID OUTPUT,
	@siteResourceID=@siteResourceID OUTPUT, @pageID=@pageID OUTPUT
								
INSERT INTO cms_customPages(appInstanceID, customFileName)
VALUES(@applicationInstanceID,'electionVoteAtLarge')

EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1, @functionID=4, @roleID=null, 
	@groupID=9118, @memberID=null, @inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, @resourceRightID=@resourceRightID OUTPUT
GO
