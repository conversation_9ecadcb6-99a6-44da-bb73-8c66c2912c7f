USE [seminarWeb]
GO

/****** Object:  Table [dbo].[tblTimeZones]    Script Date: 06/17/2013 09:12:05 ******/

CREATE TABLE [dbo].[tblTimeZones](
	[code] [char](1) NOT NULL,
	[long] [varchar](100) NOT NULL,
	[short] [varchar](10) NOT NULL,
	[id] [varchar](50) NOT NULL,
 CONSTRAINT [PK_tblTimeZones] PRIMARY KEY CLUSTERED 
(
	[code] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

SET ANSI_PADDING OFF
GO


insert into [tblTimeZones] (code, long,short,id) values ('H','Hawaiian','HT','US/Hawaii')
insert into [tblTimeZones] (code, long,short,id) values ('A','Alaskan','AKT','US/Alaska')
insert into [tblTimeZones] (code, long,short,id) values ('E','Eastern','ET','US/Eastern')
insert into [tblTimeZones] (code, long,short,id) values ('M','Mountain','MT','US/Mountain')
insert into [tblTimeZones] (code, long,short,id) values ('C','Central','CT','US/Central')
insert into [tblTimeZones] (code, long,short,id) values ('P','Pacific','PT','US/Pacific')
insert into [tblTimeZones] (code, long,short,id) values ('N','Newfoundland','NT','Canada/Newfoundland')
insert into [tblTimeZones] (code, long,short,id) values ('T','Atlantic','AT','Canada/Atlantic')
insert into [tblTimeZones] (code, long,short,id) values ('S','Central-SK','SK','Canada/Saskatchewan')
insert into [tblTimeZones] (code, long,short,id) values ('Z','Arizona','AZ','US/Arizona')

GO

