
-- Script to copy documents to new file share.  
use membercentral
SET nocount on

DECLARE @oldRoot varchar(500),@newRoot varchar(500), @oldDocumentRoot varchar(500), 
		@newDocumentRoot varchar(500), @oldorgcode varchar(10), @orgcode varchar(10), 
		@sitecode varchar(10), @siteID int, @orgID int, @rootSectionID int, 
		@userCreatedSectionResourceID int, @minSection varchar(50), @thisSectionID int, 
		@minpageID int, @pgResourceTypeID int, 
		@appPgResourceTypeID int, @siteResourceStatusID int, @thisPageID int, 
		@HTMLResourceTypeID int, @customPageApplicationTypeID int, @zoneIDMain int,
		@ppageTypeID int, @ppageName varchar(100), @ppageTitle varchar(100), @ppagemode int,
		@ppageDescription varchar(100), @pContentPath varchar(300), 
		@applicationInstanceID int, @siteresourceID int, 
		@penableSSL bit, @presourceid int, @rawContent varchar(max), @thisContentID int, 
		@thisResourceID int, @trashID int, @minpgGroupID int, @documentResourceTypeID int, 
		@doc_orgdocumentId int, @doc_sectionID int, @doc_contributorMemberID int, 
		@doc_docTitle varchar(200), @doc_docDesc varchar(200), @doc_filename varchar(100), 
		@doc_fileExt varchar(20), @doc_dateCreated datetime, @doc_newSiteResourceID int, 
		@minID int, @maxDocID int, @rc int, @documentLanguageID int, @documentVersionID int,
		@param1 varchar(max), @param2 varchar(max), @fsInstanceName varchar(100),
		@doc_contributorDepomemberDataID int, @doc_dateModified datetime, @doc_ctDesc varchar(255), @doc_style varchar(255), 
		@doc_dtDesc varchar(255), @doc_jurisdiction varchar(50), @doc_pages int, @doc_hot varchar(3), @doc_fileExists bit,
		@fsSiteResourceID int, @doc_newDocumentID int, @viewFunctionID int, @doc_olddocumentId int,
		@hotColumnID int, @styleColumnID int, @pagesColumnID int, @jurisdicationColumnId int, @oldDocIDColumnId int, @depoMemberDataIDColumnId int,
		@categoryID int, @categoryTreeID1 int, @bankgroupID int
declare @cmd VARCHAR(1000)
DECLARE @tmpPages TABLE (pageID int, newPageID int, pageTypeID int, pageName varchar(100), pageTitle varchar(100), sortDescription varchar(500), pageStatusID int, dateCreated datetime, datemodified datetime, resourceid int, sectionTitle varchar(200), pageDescription varchar(100), enableSSL bit, pagemode smallint, ContentPath varchar(300), CFMLPath varchar(300), CFMLDirectoryPath varchar(300))
DECLARE @sections TABLE (sectionName varchar(50), sectionID int)
DECLARE @pgGroupID TABLE (groupID int)
DECLARE @docs TABLE (id int IDENTITY(1,1), olddocumentId int, style varchar(255), contributorDepomemberDataID int, docTitle varchar(500), docDesc varchar(max), filename varchar(100), fileExt varchar(20), documentDate datetime, dateCreated datetime, dateModified datetime, ctDesc varchar(255), dtDesc varchar(255), jurisdiction varchar(50), pages int, hot varchar(3), fileExists bit, newdocumentid int);
DECLARE @filesToCopy TABLE (id int IDENTITY(1,1), param1 varchar(max), param2 varchar(max));
DECLARE @sectionsCheck TABLE (sectionName varchar(50), sectionCode varchar(50));
DECLARE @environ varchar(5)

/* ***** set codes *************** */
select @oldorgcode = 'DC'
select @orgcode = 'DC'
select @sitecode = 'DC'
select @environ = 'PROD'   	-- DEV or PROD
set @rootSectionID = 5884	-- Root fileshare section
set @bankgroupID = 1112		-- const Court doc

/* ***** set codes *************** */

-- application.paths.RAIDSiteDocuments.path & arguments.orgCode & "\" & arguments.siteCode
IF @environ = 'PROD' BEGIN
	set @oldRoot = '\\tsfile1\f$\tlasites\www\'
	set @oldDocumentRoot = '\\tsfile1\D$\pdfs'
	set @newRoot = '\\tsfile1\f$\platform\membercentral\'
	set @newDocumentRoot = '\\tsfile1\f$\platform\siteDocuments\'
END ELSE BEGIN
	set @oldRoot = '\\devserver\tlasites\www\'
	set @oldDocumentRoot = '\\devserver\E$\imageraid\pdfs'
	set @newRoot = '\\devserver\f\platform\membercentral\'
	set @newDocumentRoot = '\\devserver\f\platform\siteDocuments\'
END



select @siteID = dbo.fn_getSiteIDFromSiteCode(@sitecode)
	IF @@ERROR <> 0 GOTO on_error
select @orgID = dbo.fn_getOrgIDFromOrgCode(@orgcode)
	IF @@ERROR <> 0 GOTO on_error
select @userCreatedSectionResourceID = dbo.fn_getResourceTypeID('UserCreatedSection')
	IF @@ERROR <> 0 GOTO on_error
select @pgResourceTypeID = dbo.fn_getResourceTypeId('UserCreatedPage')
	IF @@ERROR <> 0 GOTO on_error
select @appPgResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedPage')
	IF @@ERROR <> 0 GOTO on_error
select @HTMLResourceTypeID = dbo.fn_getResourceTypeID('UserCreatedContent')
	IF @@ERROR <> 0 GOTO on_error
select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')
	IF @@ERROR <> 0 GOTO on_error
select @customPageApplicationTypeID = dbo.fn_getApplicationTypeIDFromName('CustomPage')
	IF @@ERROR <> 0 GOTO on_error
select @zoneIDMain = dbo.fn_getZoneID('Main')
	IF @@ERROR <> 0 GOTO on_error
select @documentResourceTypeID = dbo.fn_getResourceTypeID('UserCreatedDocument')
	IF @@ERROR <> 0 GOTO on_error
select @viewFunctionID = dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('UserCreatedDocument'))
	IF @@ERROR <> 0 GOTO on_error

exec dbo.cache_perms_setStatus @orgid=@orgid, @status='disabled'

/* ********************************* */
/* Import Documents */
/* ********************************* */
insert into @docs (olddocumentid , Style , contributorDepomemberDataID , docTitle , docDesc , filename , fileExt , documentdate, dateCreated , dateModified, ctDesc, dtDesc, jurisdiction, pages, hot, fileExists, newdocumentid)
select documentId, Style, depomemberdataid,  case when dt.description = 'depositions' then 'Deposition' else dt.description end + ' of ' + expertName, Style, cast(documentID as varchar(10)) + '.pdf'  as filename, 'pdf', documentdate,  dateEntered, dateLastModified, ct.description, dt.description,  jurisdiction, pages, hot, 0, 0
from tlasites.trialsmith.dbo.depoDocumentsView as d
		inner join tlasites.trialsmith.dbo.depoCaseTypes as ct on d.caseTypeId = ct.caseTypeID 
		inner join tlasites.trialsmith.dbo.depoDocumentTypes dt on dt.typeid = d.documentTypeID
where
documentid in (
1974463,
1974460,
1974458,
1974455,
1974454,
1974452,
1974451,
1974449,
1974448,
1915943,
1915940,
1915938,
1915936,
1915933,
1915931,
1915930,
1915929,
1915928,
1915925,
1915924,
1915920,
1915919,
2640898,
2640883,
2640879,
2640877,
2640875,
2208257,
2208253,
2208239,
2208203,
2208201,
2208200,
2208199,
2208198,
2208197,
2208194,
2208193,
2208192,
2208189,
2208185,
2208183,
2208182,
2854423,
2476654,
2476640,
2476631,
2476621,
2476616,
2476614,
2476596,
2476583,
2476573,
2476567,
2476560,
2476546,
2476510,
2476505,
2474531,
2468103,
146041,
3256922,
3256921,
3256920,
3256919,
3256918,
3256917,
3256916,
3256915,
3256914,
3256913,
3256911,
3256910,
3256909,
3256908,
3256907,
3256906,
3256801,
3256800,
3256799,
3256798,
3256797,
3256796,
3256795,
3256794,
3256793,
3256692,
3256679,
3256675,
3256664,
3256663,
3256662,
3256661,
3256660,
3256659,
3256658,
3256657,
3256656,
3256655,
3256654,
3256653,
3256652,
3256651,
3256650,
3256649,
3256648,
3256647,
3256646,
3256645,
3234456,
3234452,
3234449,
3234446,
3234443,
3234440,
3234437,
3234434,
3234432,
3234429,
3234425,
3234422,
3234419,
3234414,
3231366,
3231358,
3220859,
3220845,
3219903,
3219886,
3219885,
3219884,
3200022,
3199810,
3199809,
3199808,
3199806,
3199805,
3199804,
3199803,
3199802,
3199801,
3199800,
3197723,
3197722,
3197721,
3196989,
3196988,
3196987,
3196986,
3196985,
3196984,
3196858,
3196846,
3196843,
3196842,
3196841,
3196840,
3194654,
3194653,
3194652,
3194651,
3194650,
3194649,
3194648,
3194647,
3194646,
3194645,
3194644,
3194643,
3194642,
3194641,
3194640,
3194639,
3194636,
3139981,
3139980,
3139979,
3139978,
3139977,
3139976,
3139975,
3139974,
3139973,
3139972,
3139968,
3136329,
3136327,
3136322,
3136320,
3136317,
3136313,
3135207,
3135205,
3135172,
3135171,
3135170,
3135169,
3135155,
3134309,
3134308,
3134307,
3134305,
3134073,
3129930,
3129929,
3129326,
3129325,
3129324,
3129323,
3129322,
3129321,
3129320,
3129319,
3129318,
3129317,
3129316,
3129315,
3129314,
3129313,
3129312,
3129311,
3129310,
3129309,
3129308,
3129307,
3129306,
3129305,
3127977,
3127974,
3127971,
3127969,
3127738,
3127737,
3127735,
3119014,
3119013,
3119012,
3119011,
3119010,
3119009,
3119008,
3119007,
3119006,
3119005,
3119004,
3105100,
3105099,
3105098,
3105097,
3105096,
3105095,
3105094,
3105074,
3105071,
3105070,
3105056,
3105055,
3105054,
3104950,
3104948,
3104947,
3104900,
3104899,
3104898,
3104897,
3104896,
3104895,
3104892,
3104889,
3104884,
3104882,
3104880,
3104871,
3104866,
3104860,
3104858,
3104855,
3104767,
3104763,
3104756,
3104737,
3104736,
3104732,
3104725,
3099803,
2993074,
2993073,
2993060,
2993051,
2993047,
2993039,
2993034,
2993011,
2993004,
2992996,
2992992,
2992986,
2992980,
2992973,
2992959,
2992954,
2992947,
2992939,
2992895,
2992890,
2992882,
2992874,
2958008,
2958003,
2958000,
2957995,
2957992,
2957990,
2957985,
2957982,
2957973,
2957959,
2957957,
2957950,
2957947,
2957940,
2957932,
2957928,
2957925,
2957919,
2957917,
2957913,
2957903,
2957785,
2953983,
2953979,
2953792,
2953791,
2953789,
2953783,
2953778,
2953774,
2953765,
2785100,
2785081,
2785050,
2785041,
2785032,
2785019,
2785010,
2784990,
2784963,
2784958,
2784950,
2784835,
2784831,
2784819,
2782251,
2782226,
2782208,
2782117,
2782107,
2782094,
2782076,
2782066,
2782062,
2782054,
2782050,
2782045,
2782034,
2782024,
2780851,
2780826,
2780817,
2780808,
2780800,
2780792,
2780775,
2780730,
2780727,
2780704,
2778934,
2778911,
2778889,
2778762,
2778746,
2778738,
2778727,
2778708,
2778682,
2778099,
2776605,
2176972,
2136789,
2136787,
2089835,
2089833,
2089831,
2089830,
2089829,
2089828,
2089827,
2089826,
2089825,
2009807,
2009806,
2009805,
2009804,
2009803,
2009802,
2009801,
2009800,
2009799,
1972103,
1972102,
1870501,
1870499,
1870498,
1870494,
1870492,
1870490,
1870487,
1870480,
1870479,
1870478,
1870476,
1870465,
1870458,
1870439,
1870372,
1870369,
1870368,
1820817,
296634,
292547,
292546,
292545,
292544,
292543,
292542,
292541,
292540,
292539,
292538,
292537,
292536,
292535,
292534,
292533,
292216,
292215,
292214,
292213,
292212,
292210,
292209,
292208,
292207,
292206,
292205,
292204,
292203,
292202,
292201,
292200,
292199,
292198,
292197,
292196,
291082,
291079,
291076,
291074,
291073,
291072,
291071,
291068,
263286,
263285,
263284,
263282,
263280,
262666,
262665,
262664,
262663,
262662,
262661,
260520,
260518,
260516,
260514,
260512,
260510,
253518,
253516,
253514,
236817,
236816,
229745,
229744,
229743,
229742,
229741,
229740,
229739,
229738,
229737,
229736,
229735,
229734,
183337,
183336,
183335,
183333,
183332,
183331,
183190,
183189,
183188,
183187,
183186,
183184,
183182,
183181,
183180,
183179,
183178,
183177,
183176,
183175,
183174,
183173,
183172,
3207826,
3207822,
3207821,
3207676,
3207675,
3207674,
3207673,
3207672,
3207671,
3207670,
3207669,
3207668,
3207667,
3207666,
3207665,
3207664,
3207663,
3207662,
3207661,
3207660,
3207659,
3207658,
3207657,
3207656,
3207655,
3207654,
3207653,
3207652,
3207651,
3207650,
3207649,
3207648,
3207647,
3207646,
3207645,
3207644,
3207643,
3207642,
3207641,
3207640,
3207639,
3207638,
3207637,
3207636,
3207635,
3207634,
3207633,
3207632,
3207631,
3207630,
3207629,
3207628,
3207627,
3207626,
3207625,
3207624,
3207623,
3207622,
3207621,
3207620,
3207619,
3207618,
3207617,
3207616,
3207615,
3207614,
3207613,
3207612,
3207611,
3207610,
3207609,
3207605,
3207604,
3207603,
3207602,
3207601,
3207600,
3207599,
3207598,
3207597,
3207596,
3207595,
3207594,
3207593,
3207592,
3207591,
3207590,
3207589,
3207588,
3207587,
3207586,
3207585,
3207584,
3207583,
3207582,
3207581,
3207580,
3207579,
3207578,
3207576,
3207575,
3207574,
3207573,
3207572,
3207571,
3207570,
3207569,
3207567,
3207566,
3207565,
3207564,
3207563,
3207562,
3207561,
3207560,
3207559,
3207558,
3207557,
3207556,
3207555,
3207554,
3207553,
3207552,
3207551,
3207550,
3207549,
3207548,
3207547,
3207546,
3207545,
3207544,
3207543,
3207542,
3207541,
3207540,
3207539,
3207538,
3207537,
3207536,
3207535,
3207534,
3207533,
3207532,
3207531,
3207530,
3207529,
3207528,
3207527,
3207526,
3207525,
3207524,
3207523,
3207522,
3207521,
3207520,
3207519,
3207518,
3207517,
3207516,
3207515,
3207514,
3207513,
3207512,
3207511,
3207510,
3207509,
3207508,
3207507,
3207506,
3207505,
3207504,
3207503,
3207502,
3207501,
3207500,
3207499,
3207498,
3207497,
3207496,
3207495,
3207494,
3207493,
3207492,
3207491,
3207490,
3207489,
3207488,
3207487,
3207486,
3207485,
3207484,
3207483,
3207482,
3207481,
3207480,
3207479,
3207478,
3207477,
3207476,
3207475,
3207474,
3207473,
3207472,
3207471,
3207470,
3207469,
3207468,
3207467,
3207466,
3207465,
3207464,
3207463,
3207462,
3207461,
3207460,
3207459,
3207458,
3207457,
3207456,
3207455,
3207454,
3207453,
3207452,
3207451,
3207450,
3207449,
3207448,
3207447,
3207446,
3207445,
3207444,
3207443,
3207442,
3207441,
3207440,
3207439,
3207438,
3207437,
3207436,
3207435,
3207434,
3207433,
3207432,
3207431,
3207430,
3207429,
3207428,
3207427,
3207426,
3207425,
3207424,
3207423,
3207422,
3207421,
3207420,
3207419,
3207418,
3207417,
3207416,
3207415,
3207414,
3207413,
3207412,
3207411,
3207410,
3207409,
3207408,
3207407,
3207406,
3207405,
3207404,
3207403,
3207402,
3207401,
3207400,
3207399,
3207398,
3207397,
3207396,
3207395,
3207394,
3207393,
3207392,
3207391,
3207390,
3207389,
3207388,
3207387,
3207386,
3207385,
3207384,
3207383,
3207382,
3207381,
3207380,
3207379,
3207378,
3207377,
3207376,
3207375,
3207374,
3207373,
3207372,
3207371,
3207370,
3207369,
3207368,
3207361,
3207360,
3207359,
3207358,
3207357,
3207356,
3207355,
3207354,
3207353,
3207352,
3207351,
3207350,
3207349,
3207348,
3207347,
3207346,
3207345,
3207344,
3207343,
3207342,
3207341,
3207340,
3207339,
3207338,
3207337,
3207336,
3207335,
3207334,
3207333,
3207332,
3207331,
3207330,
3207329,
3207328,
3207326,
3207323,
3207320,
3207317,
3207316,
3207315,
3207314,
3207313,
3207312,
3207311,
2583192,
2583186,
2583181,
2583180,
2583177,
2583176,
2583174,
2583171,
2583168,
2583167,
2583163,
2583158,
2583157,
2583156,
2583154,
2583153,
2583151,
2583147,
2583140,
2583138,
2583136,
2583134,
2583131,
2583130,
2583128,
2583126,
2583125,
2583116,
2583113,
2583111,
2583109,
2583107,
2583106,
2583104,
2583100,
2583098,
2583097,
2583094,
2583091,
2583090,
2583089,
2583088,
2583087,
2583085,
2583083,
2583082,
2583079,
2583077,
2583076,
2583074,
2583072,
2583068,
2583067,
2583065,
2583061,
2583059,
2583054,
2583051,
2583038,
2583036,
2583032,
2583029,
2583026,
2583023,
2583020,
2583016,
2583014,
2583011,
2583010,
2583005,
2583003,
2583001,
2582996,
2582994,
2582991,
2582989,
2582986,
2582984,
2582972,
2582971,
2582970,
2582967,
2582964,
2582962,
2582958,
2582957,
2582954,
2582952,
2582951,
2582949,
2582945,
2582943,
2582941,
2582938,
2582936,
2582929,
2582352,
2582350,
2582348,
2582341,
2582336,
2582333,
2582328,
2582316,
2582310,
2582280,
2582275,
2582266,
2582263,
2582261,
2582255,
2582235,
2582233,
2582230,
2582228,
2582197,
2582195,
2582193,
2582189,
2582182,
2582176,
2582170,
2582166,
2582164,
2582162,
2582154,
2582152,
2582150,
2582149,
2582146,
2582145,
2582141,
2582115,
2582110,
2582106,
2582103,
2582102,
2582064,
2582061,
2582060,
2582057,
2582053,
2582052,
2582045,
2582043,
2582042,
2582030,
2582028,
2582024,
2582023,
2582019,
2582016,
2582015,
2582014,
2582013,
2582012,
2582011,
2582010,
2582009,
2582008,
2582005,
2582003,
2582002,
2581999,
2581998,
2581994,
2581991,
2581988,
2581984,
2581979,
2581975,
2581969,
2581962,
2581959,
2581955,
2581950,
2581946,
2581939,
2581937,
2581934,
2581933,
2581930,
2581926,
2581920,
2581918,
2581879,
2581876,
2581870,
2581869,
2581865,
2581863,
2581859,
2581858,
2581856,
2581854,
2581851,
2581848,
2581844,
2581841,
2581839,
2581832,
2581828,
2581824,
2581819,
2581816,
2581809,
2581807,
2581804,
2581802,
2581800,
2581797,
2581796,
2581794,
2581793,
2581790,
2581788,
2581786,
2581784,
2581779,
2581775,
2581769,
2581768,
2581764,
2581762,
2581754,
2581747,
2581745,
2581742,
2581741,
2581737,
2581732,
2581731,
2581730,
2581729,
2581728,
2581726,
2581725,
2581704,
2581703,
2581700,
2581697,
2581696,
2581694,
2581693,
2581691,
2581689,
2581686,
2581683,
2581682,
2581680,
2581678,
2581662,
2581660,
2581659,
2581652,
2581650,
2581647,
2581646,
2581644,
2581143,
2581141,
2581139,
2581135,
2581134,
2581130,
2581125,
2581123,
2581120,
2581117,
2581115,
2581113,
2581106,
2581101,
2581098,
2581095,
2581090,
2581088,
2581086,
2581084,
2581082,
2581077,
2581047,
2581046,
2581045,
2581042,
2581039,
2581038,
2581037,
2581036,
2581031,
2581028,
2581026,
2581022,
2581020,
2581013,
2581011,
2581009,
2581005,
2581003,
2581000,
2580996,
2580978,
2580975,
2580965,
2580963,
2580961,
2580960,
2580955,
2580951,
2580948,
2580944,
2580937,
2580933,
2580931,
2580928,
2580926,
2580913,
2580906,
2580904,
2580901,
2580900,
2580895,
2580889,
2580885,
2580882,
2580878,
2580875,
2580870,
2580869,
2580868,
2580856,
2580848,
2580844,
2580843,
2580841,
2580837,
2580836,
2580831,
2580825,
2580822,
2580821,
2580817,
2580816,
2580814,
2580812,
2580808,
2580788,
2580787,
2580786,
2580782,
2580780,
2580769,
2580754,
2580752,
2580748,
2580747,
2580741,
2580689,
2580688,
2580684,
2580682,
2580680,
2580679,
2580678,
2580676,
2580675,
2580673,
2580671,
2580669,
2580665,
2580663,
2580662,
2580660,
2580658,
2580657,
2580655,
2580653,
2580650,
2580643,
2580639,
2580637,
2580636,
2580635,
2580634,
2580618,
2580617,
2580616,
2580611,
2580608,
2580605,
2580600,
2580597,
2580593,
2580587,
2580585,
2580583,
2580573,
2580571,
2580563,
2580555,
2580551,
2580550,
2580547,
2580546,
2580543,
2580526,
2580524,
2580520,
2580488,
2580485,
2580482,
2580480,
2580478,
2580475,
2580474,
2580470,
2580467,
2580465,
2580461,
2580454,
2580451,
2580449,
2580445,
2580444,
2580443,
2580441,
2580440,
2580439,
2580437,
2580434,
2580433,
2580432,
2580431,
2580427,
2580425,
2580423,
2580415,
2579950,
2579948,
2579945,
2579944,
2579939,
2579936,
2579934,
2579931,
2579915,
2579913,
2579911,
2579909,
2579908,
2579903,
2579896,
2579892,
2579890,
2579887,
2579882,
2579880,
2579877,
2579875,
2579871,
2579870,
2579864,
2579863,
2579855,
2579853,
2579851,
2579849,
2579844,
2579839,
2579835,
2579834,
2579829,
2579826,
2579821,
2579815,
2579812,
2579810,
2579806,
2579802,
2579794,
2579788,
2579784,
2579724,
2579723,
2579721,
2579718,
2579715,
2579714,
2579712,
2579711,
2579710,
2579706,
2579699,
2579696,
2579695,
2579691,
2579686,
2579684,
2579675,
2579672,
2579670,
2579669,
2579668,
2579665,
2579664,
2579645,
2579641,
2579640,
2579637,
2579635,
2579634,
2579632,
2579627,
2579612,
2579611,
2579610,
2579608,
2579606,
2579604,
2579601,
2579598,
2579595,
2579594,
2579593,
2579592,
2579591,
2579590,
2579587,
2579583,
2579582,
2579578,
2579576,
2579575,
2579572,
2579570,
2579569,
2579568,
2579567,
2579562,
2579556,
2579553,
2579551,
2579549,
2579545,
2579509,
2579506,
2579502,
2579500,
2579499,
2579495,
2579494,
2579492,
2579490,
2579487,
2579483,
2579481,
2579479,
2579477,
2579475,
2579474,
2579470,
2579468,
2579466,
2579464,
2579462,
2579460,
2579458,
2579454,
2579452,
2579431,
2579427,
2579421,
2579420,
2579418,
2579410,
2579406,
2579380,
2579377,
2579376,
2579374,
2579373,
2579371,
2579370,
2579367,
2579363,
2579361,
2579357,
2579348,
2579346,
2579344,
2579343,
2579341,
2579340,
2579338,
2579334,
2579332,
2579330,
2579329,
2579296,
2579295,
2579293,
2579290,
2579287,
2579283,
2579280,
2579276,
2579262,
2579261,
2579259,
2579254,
2579248,
2579246,
2579243,
2579239,
2579233,
2579230,
2579225,
2579216,
2579215,
2579211,
2579207,
2579206,
2579205,
2579204,
2579202,
2579200,
2579197,
2579193,
2579191,
2579190,
2579188,
2303735,
2303732,
2303731,
2303728,
2303725,
2303721,
2303717,
2303716,
2303714,
2303709,
2303704,
2303701,
2303698,
2303695,
2303692,
2303688,
2303686,
2303681,
2303680,
2303678,
2303676,
2303674,
2303672,
2303671,
2303668,
2303667,
2303666,
2303665,
2303661,
2303656,
2303648,
2303644,
2303640,
2303630,
2303627,
2303621,
2303616,
2303609,
2303603,
2302247,
2302245,
2302244,
2302242,
2302241,
2302236,
2302233,
2302230,
2302212,
2302206,
2302204,
2302199,
2302198,
2302195,
2302189,
2302185,
2302144,
2302142,
2302135,
2302130,
2302125,
2302123,
2302122,
2302121,
2302119,
2302118,
2302116,
2302114,
2302110,
2302107,
2302105,
2302104,
2301400,
2301398,
2301394,
2301388,
2301384,
2301377,
2301372,
2301368,
2301367,
2301363,
2301359,
2301356,
2301351,
2301344,
2301330,
2301320,
2301309,
2301305,
2301302,
2301293,
2300420,
2300417,
2300411,
2300408,
2300402,
2300380,
2300336,
2300328,
2300323,
2300315,
2300306,
2300299,
2300296,
2300290,
2300288,
2300275,
2300255,
2300252,
2300250,
2300240,
2300237,
2300235,
2300222,
2300217,
2300213,
2299412,
2299409,
2299405,
2299401,
2299396,
2299388,
2299385,
2299380,
2299379,
2299377,
2299371,
2299368,
2299366,
2299363,
2299362,
2299360,
2299358,
2299357,
2299352,
2299345,
2299343,
2298693,
2298690,
2298674,
2298673,
2298671,
2298669,
2298667,
2298665,
2298663,
2298654,
2298650,
2298648,
2297629,
2297628,
2297623,
2297622,
2297621,
2297616,
2297612,
2297609,
2297605,
2297603,
2297599,
2297595,
2297592,
2297588,
2297586,
2297582,
2297575,
2297567,
2297563,
2297558,
2297552,
2297546,
2297542,
2297537,
2297530,
2297524,
2297521,
2297517,
2297516,
2297506,
2297483,
2297459,
2297448,
2297445,
2297442,
2297439,
2297434,
2297378,
2297369,
2297342,
2297338,
2297320,
2297299,
2297138,
2297094,
2297092,
2297077,
2296502,
2296493,
2296490,
2296485,
2296483,
2296480,
2296477,
2296473,
2296471,
2296466,
2296458,
2296454,
2296426,
2296393,
2296389,
2296384,
2296381,
2296370,
2296359,
2296353,
2296352,
2296349,
2296346,
2296338,
2296334,
2296329,
2296326,
2296321,
2296314,
2296310,
2296298,
2296290,
2296288,
2296279,
2296276,
2296273,
2296270,
2296265,
2296260,
2296232,
2296223,
2296218,
2296216,
2296169,
2296166,
2296161,
2296158,
2296157,
2296147,
2296144,
2296136,
2296133,
2295604,
2295601,
2295599,
2295598,
2295597,
2295595,
2295592,
2295591,
2295587,
2295580,
2295577,
2295574,
2295572,
2295570,
2295569,
2295568,
2295567,
2295564,
2295563,
2295561,
2295560,
2295559,
2295556,
2295555,
2295552,
2295551,
2295550,
2295549,
2295539,
2295536,
2295057,
2295047,
2295046,
2295044,
2295042,
2295035,
2295032,
2295029,
2295023,
2295015,
2295013,
2295011,
2295007,
2295003,
2295001,
2294997,
2294993,
2294992,
2294975,
2294965,
2294950,
2294936,
2294933,
2294930,
2294926,
2294917,
2294914,
2294894,
2294891,
2294889,
2294886,
2294883,
2294872,
2294866,
2294855,
2294822,
2294817,
2294811,
2294808,
2294802,
2294799,
2294795,
2294203,
2294202,
2294198,
2294189,
2294185,
2294181,
2294180,
2294177,
2294174,
2294170,
2294166,
2294070,
2294055,
2294043,
2294038,
2294037,
2294032,
2294023,
2294000,
2293999,
2293977,
2293970,
2293961,
2293957,
2293952,
2293948,
2293945,
2293939,
2293934,
2293931,
2293907,
2293878,
2293877,
2293869,
2293844,
2293811,
2293789,
2293784,
2293761,
2293739,
2293728,
2293252,
2293250,
2293247,
2293245,
2293188,
2293184,
2293147,
2293139,
2293076,
2293072,
2293063,
2293058,
2293055,
2293054,
2293046,
2293038,
2293037,
2293036,
2293033,
2293030,
2293007,
2293000,
2292997,
2292989,
2292986,
2292938,
2292910,
2292903,
2292891,
2292872,
2292868,
2292861,
2292859,
2292855,
2292843,
2292821,
2292214,
2292208,
2292206,
2292205,
2292201,
2292196,
2292194,
2292190,
2292185,
2292180,
2292173,
2292170,
2292160,
2292159,
2292151,
2292148,
2292142,
2292137,
2292134,
2292132,
2292125,
2292121,
2292117,
2292114,
2292108,
2292104,
2292101,
2292095,
2292092,
2292085,
2292083,
2292080,
2292050,
2292046,
2292042,
2292033,
2292032,
2290627,
2290624,
2290623,
2290620,
2290619,
2290615,
2290611,
2290589,
2290585,
2290582,
2290580,
2290578,
2290575,
2290574,
2290570,
2290568,
2290511,
2290506,
2290495,
2290492,
2290491,
2290463,
2289699,
2289695,
2289693,
2289691,
2289689,
2289687,
2289682,
2289675,
2289670,
2289668,
2289666,
2289664,
2289660,
2289657,
2289650,
2289646,
2289644,
2289640,
2289638,
2289614,
2289606,
2289603,
2289600,
2289599,
2289596,
2289593,
2289589,
2289580,
2289576,
2289574,
2289572,
2289569,
2289566,
2289562,
2289539,
2289537,
2289517,
2289490,
2289489,
2289484,
2289482,
2289478,
2289456,
2289443,
2289438,
2289417,
2288913,
2288835,
2288816,
2288815,
2288814,
2288813,
2288811,
2288807,
2288803,
2288798,
2288794,
2288788,
2288787,
2288785,
2288779,
2288777,
2288774,
2288772,
2288770,
2288765,
2288750,
2288747,
2288740,
2287777,
2287773,
2287771,
2287769,
2287764,
2287763,
2287756,
2287752,
2287748,
2287743,
2287739,
2287737,
2287730,
2287724,
2287717,
2287714,
2287705,
2287703,
2287697,
2287688,
2287682,
2287677,
2287674,
2287669,
2287668,
2287665,
2287662,
2287660,
2287622,
2287599,
2287595,
2287584,
2287563,
2286833,
2286831,
2286830,
2286827,
2286825,
2286824,
2286819,
2286817,
2286815,
2286807,
2286805,
2286803,
2286801,
2286798,
2286794,
2286792,
2286790,
2286788,
2286784,
2286780,
2286776,
2286773,
2286770,
2286764,
2286761,
2285984,
2285977,
2285972,
2285968,
2285964,
2285962,
2285959,
2285956,
2285951,
2285929,
2285926,
2285919,
2285891,
2285855,
2285852,
2285841,
2285835,
2285833,
2285827,
2285815,
2285811,
2285740,
2285737,
2285731,
2285726,
2285723,
2285721,
2285716,
2285710,
2285705,
2285702,
2285699,
2285697,
2284998,
2284989,
2284988,
2284985,
2284980,
2284977,
2284975,
2284974,
2284968,
2284963,
2284958,
2284952,
2284945,
2284916,
2284911,
2284887,
2284733,
2284729,
2284725,
2284711,
2284702,
2284700,
2284699,
2284697,
2284691,
2284684,
2284677,
2284675,
2284670,
2284664,
2284661,
2284652,
2284649,
2284644,
2284633,
2284624,
2283968,
2283962,
2283959,
2283957,
2283956,
2283936,
2283932,
2283923,
2283911,
2283907,
2283901,
2283898,
2283894,
2283891,
2283883,
2283876,
2283872,
2283773,
2283769,
2283724,
2283721,
2283715,
2283712,
2283706,
2283701,
2283698,
2283695,
2283693,
2283099,
2283094,
2283090,
2283087,
2283085,
2283084,
2283083,
2283081,
2283076,
2283070,
2283067,
2283065,
2283034,
2283033,
2283029,
2283026,
2283022,
2281300,
2281298,
2281296,
2281295,
2281292,
2281288,
2281284,
2280416,
2280413,
2280412,
2280407,
2280404,
2280402,
2280398,
2280396,
2280395,
2280391,
2280387,
2280379,
2280376,
2132945,
2132396,
2132393,
2132392,
2132391,
2132389,
2132387,
2131539,
2131537,
2131535,
2131533,
2131530,
2131524,
2131520,
2131516,
2131209,
2131160,
2131158,
2131156,
2131154,
2131153,
2131151,
2131149,
2131144,
2131142,
2131136,
2131133,
2131132,
2131131,
2131127,
2131126,
2131123,
2131122,
2131120,
2131116,
2131113,
2131111,
2131107,
2131105,
2131103,
2131101,
2131099,
2131096,
2131094,
2131093,
2131091,
2131077,
2131075,
2131074,
2131072,
2131067,
2131063,
2131062,
2131060,
2131059,
2131058,
2131057,
2131056,
2131051,
2131050,
2131048,
2131030,
2131028,
2131026,
2131018,
2131017,
2131016,
2131015,
2131011,
2131010,
2131009,
2131002,
2130998,
2130996,
2130994,
2130991,
2130978,
2130975,
2130965,
2130963,
2130961,
2130959,
2130958,
2130957,
2130953,
2130947,
2130946,
2130943,
2130940,
2130939,
2130933,
2130931,
2130928,
2130927,
2130923,
2130922,
2130920,
2130919,
2130917,
2130916,
2130914,
2130911,
2130910,
2130897,
2130895,
2130894,
2130414,
2129241,
2129240,
2129238,
2129236,
2129209,
2129207,
2129205,
2129201,
2129197,
2129195,
2129194,
2129193,
2129192,
2129191,
2129190,
2129189,
2129188,
2129187,
2129186,
2129185,
2129184,
2129183,
2129182,
2129180,
2129179,
2129177,
2129176,
2129174,
2129172,
2129171,
2129170,
2129169,
2129168,
2129167,
2129166,
2129165,
2129164,
2129163,
2129162,
2129161,
2129160,
2129158,
2129153,
2129152,
2129151,
2129148,
2129146,
2129145,
2129137,
2129136,
2129135,
2129132,
2129129,
2129125,
2129120,
2129115,
2129113,
2129112,
2129110,
2129109,
2129107,
2129105,
2129103,
2129099,
2129097,
2129094,
2129093,
2129090,
2129089,
2129088,
2129087,
2129085,
2129084,
2129078,
2129077,
2129075,
2129060,
2129058,
2129055,
2129053,
2129048,
2129045,
2129044,
2129042,
2129041,
2129038,
2129037,
2129035,
2129034,
2129031,
2129029,
2129027,
2129019,
2129017,
2129016,
2129015,
2129012,
2129011,
2129009,
2129006,
2129005,
2129004,
2129003,
2129002,
2128998,
2128997,
2128996,
2128995,
2128993,
2128990,
2128989,
2128988,
2128987,
2128986,
2128803,
2128802,
2128801,
2128799,
2128798,
2128794,
2128792,
2128790,
2128789,
2128788,
2128786,
2128751,
2128749,
2128748,
2128743,
2128739,
2128736,
2128733,
2128730,
2128726,
2128723,
2128721,
2128719,
2128718,
2128717,
2128715,
2128705,
2128703,
2128700,
2128698,
2128695,
2128691,
2128690,
2128682,
2128679,
2128675,
2128669,
2128668,
2128649,
2128645,
2128644,
2128639,
2128638,
2128632,
2128619,
2128618,
2128613,
2128610,
2128609,
2128605,
2128604,
2128602,
2128600,
2128598,
2128596,
2128590,
2128588,
2128585,
2128584,
2128582,
2128579,
2128577,
2128575,
2128572,
2128569,
2128568,
2128555,
2128554,
2128547,
2128539,
2128538,
2128534,
2128532,
2128529,
2128527,
2128523,
2128519,
2128517,
2128516,
2128513,
2128498,
2128497,
2128496,
2128494,
2128492,
2128491,
2128489,
2128487,
2128486,
2128485,
2128484,
2128481,
2128478,
2128476,
2128475,
2128473,
2128472,
2128469,
2128468,
2128467,
2128465,
2128461,
2128459,
2128458,
2128456,
2128454,
2128453,
2128442,
2128441,
2128440,
2128436,
2128432,
2128427,
2128425,
2128423,
2128422,
2128420,
2128419,
2128418,
2128417,
2128410,
2128409,
2128408,
2128407,
2128406,
2128401,
2128399,
2128398,
2128397,
2128390,
2128386,
2128384,
2128383,
2128018,
2128017,
2128014,
2127785,
2127782,
2127775,
2127770,
2127768,
2127766,
2127764,
2127762,
2127760,
2127756,
2127747,
2127739,
2127733,
2127730,
2127723,
2127716,
2127714,
2127712,
2127711,
2127707,
2127704,
2127703,
2127700,
2127699,
2127690,
2127688,
2127680,
2127679,
2127673,
2127668,
2127667,
2127666,
2127665,
2127664,
2127663,
2127650,
2127649,
2127648,
2127643,
2127642,
2127641,
2127638,
2127634,
2127630,
2127628,
2127626,
2127625,
2127619,
2127616,
2127614,
2127613,
2127612,
2127610,
2127607,
2127602,
2127594,
2127593,
2127586,
2127583,
2127582,
2127581,
2127579,
2127578,
2127575,
2127573,
2127572,
2127567,
2127565,
2127564,
2127563,
2127560,
2127558,
2127556,
2127555,
2127553,
2127552,
2127551,
2127550,
2127548,
2127547,
2127546,
2127545,
2127544,
2127543,
2127542,
2127540,
2127539,
2127536,
2127534,
2127533,
2127532,
2127531,
2127530,
2127527,
2127516,
2127514,
2127512,
2127511,
2127510,
2127509,
2127508,
2127507,
2127505,
2127504,
2127503,
2127502,
2127501,
2127500,
2127499,
2127495,
2127493,
2127491,
2127485,
2127483,
2127482,
2127481,
2127480,
2127479,
2127478,
2127253,
2127251,
2127249,
2127246,
2127244,
2127239,
2127237,
2127235,
2127233,
2127232,
2127228,
2127227,
2127224,
2127221,
2127217,
2127214,
2127212,
2127193,
2127190,
2127186,
2127177,
2127170,
2127168,
2127166,
2127162,
2127155,
2127152,
2127149,
2127144,
2127143,
2127139,
2127137,
2127134,
2127122,
2127119,
2127081,
2127077,
2127076,
2127073,
2127069,
2127068,
2127066,
2127064,
2127061,
2127060,
2127056,
2127054,
2127053,
2127052,
2127050,
2127049,
2127048,
2127046,
2127044,
2127040,
2126980,
2126977,
2126975,
2126971,
2126967,
2126964,
2126963,
2126961,
2126959,
2126957,
2126956,
2126953,
2126950,
2126949,
2126948,
2126946,
2126944,
2126942,
2126941,
2126937,
2126934,
2126932,
2126930,
2126928,
2126919,
2126918,
2126916,
2126914,
2126911,
2126910,
2126908,
2126907,
2126746,
2126743,
2126737,
2126734,
2126731,
2126730,
2126726,
2126724,
2126721,
2126719,
2126717,
2126715,
2126711,
2126690,
2126687,
2126685,
2126682,
2126681,
2126670,
2126667,
2126666,
2126665,
2126664,
2126662,
2126660,
2126658,
2126657,
2126656,
2126655,
2126653,
2126650,
2126646,
2126645,
2126642,
2126639,
2126631,
2126628,
2126625,
2126624,
1851851,
1845733,
1845700,
1845698,
1845696,
1845695,
1845694,
1845692,
1845690,
1845688,
1845686,
1845685,
1845683,
1845681,
1845680,
1845679,
1845678,
1845677,
1845676,
1845675,
1845674,
1845673,
1845672,
1845671,
1845670,
1845668,
1845393,
1845391,
1845390,
1845389,
1845388,
1845387,
1845386,
1845385,
1845384,
1845379,
1845377,
1845375,
1845370,
1845368,
1845366,
1845363,
1845361,
1845360,
1845359,
1845358,
1845357,
1845356,
1845355,
1845354,
1845353,
1845351,
1845350,
1845349,
1845348,
1845346,
1845345,
1845344,
1845343,
1845341,
1845338,
1845337,
1845336,
1845334,
1845329,
1845327,
1845326,
1845325,
1845323,
1845322,
1845321,
1845319,
1845317,
1845316,
1845314,
1845313,
1845306,
1845303,
1845301,
1845299,
1845298,
1845296,
1845294,
1845291,
1845289,
1845286,
1845285,
1845284,
1845282,
1845280,
1845279,
1845276,
1845274,
1845272,
1845270,
1845267,
1845265,
1845264,
1845262,
1845261,
1845260,
1845258,
1845254,
1845252,
1845250,
1845248,
1845247,
1845244,
1845241,
1845235,
1845232,
1845227,
1845225,
1845222,
1845219,
1845218,
1845217,
1845214,
1845211,
1845210,
1845203,
1845199,
1845194,
1845188,
1845186,
1845184,
1845183,
1845178,
1845175,
1845174,
1845170,
1845168,
1845166,
1845163,
1845162,
1845159,
1845158,
1845156,
1845154,
1845152,
1845151,
1845150,
1845148,
1845140,
1845138,
1845134,
1845131,
1845130,
1845128,
1845125,
1845120,
1845114,
1845111,
1845108,
1845106,
1845105,
1845103,
1845102,
1845100,
1845098,
1845097,
1845096,
1845094,
1845092,
1845090,
1845088,
1844893,
1844890,
1844889,
1844888,
1844883,
1844882,
1844881,
1844879,
1844878,
1844876,
1844871,
1844862,
1844861,
1844859,
1844858,
1844856,
1844854,
1844853,
1844852,
1844851,
1844850,
1844849,
1844848,
1844847,
1844846,
1844845,
1844844,
1844843,
1844841,
1844840,
1844838,
1844837,
1844833,
1844830,
1844828,
1844827,
1844826,
1844825,
1844824,
1844818,
1844816,
1844815,
1844814,
1844813,
1844812,
1844809,
1844808,
1844805,
1844804,
1844803,
1844801,
1844797,
1844795,
1844794,
1844793,
1844790,
1844787,
1844786,
1844783,
1844780,
1844777,
1844774,
1844771,
1844768,
1844766,
1844763,
1844762,
1844761,
1844758,
1844757,
1844756,
1844755,
1844754,
1844753,
1844748,
1844746,
1844743,
1844742,
1844741,
1844737,
1844736,
1844735,
1844734,
1844733,
1844731,
1844730,
1844725,
1844722,
1844719,
1844716,
1844715,
1844713,
1844711,
1844710,
1844708,
1844706,
1844705,
1844704,
1844658,
1844657,
1844656,
1844655,
1844654,
1844653,
1844652,
1844651,
1844650,
1844649,
1844647,
1844645,
1844644,
1844642,
1844641,
1844640,
1844639,
1844638,
1844637,
1844636,
1844634,
1844632,
1844631,
1844630,
1844628,
1844625,
1844624,
1844623,
1844622,
1844621,
1844619,
1844616,
1844614,
1844613,
1844612,
1844611,
1844610,
1844609,
1844608,
1844607,
1844606,
1844605,
1844601,
1844599,
1844598,
1844596,
1844594,
1844593,
1844592,
1844591,
1844588,
1844586,
1844585,
1844583,
1844582,
1844581,
1844580,
1844579,
1844573,
1844570,
1844569,
1844567,
1844566,
1844565,
1844564,
1844562,
1844560,
1844559,
1844558,
1844557,
1844556,
1844555,
1844554,
1844553,
1844551,
1844550,
1844549,
1844548,
1844546,
1844545,
1844544,
1844543,
1844542,
1844540,
1844538,
1844535,
1844534,
1844532,
1844531,
1844530,
1844529,
1844528,
1844397,
1844396,
1844394,
1844393,
1844392,
1844391,
1844390,
1844389,
1844388,
1844387,
1844386,
1844385,
1844384,
1844383,
1844382,
1844381,
1844380,
1844379,
1844378,
1844377,
1844376,
1844375,
1844374,
1844283,
1844280,
1844276,
1844271,
1844270,
1844269,
1844268,
1844267,
1844266,
1844264,
1844263,
1844262,
1844260,
1844259,
1844258,
1844257,
1844252,
1844251,
1844250,
1844249,
1844248,
1844247,
1844246,
1844244,
1844243,
1844241,
1844240,
1844238,
1844236,
1844195,
1844194,
1844192,
1844191,
1844189,
1844188,
1844186,
1844185,
1844183,
1844182,
1844179,
1844177,
1844172,
1844170,
1844169,
1844168,
1844166,
1844164,
1844160,
1844159,
1844158,
1844157,
1844155,
1844152,
1844151,
1844150,
1844149,
1844148,
1844147,
1844141,
1844138,
1844137,
1844135,
1844133,
1844130,
1844129,
1844127,
1844126,
1844125,
1844123,
1844121,
1844119,
1844117,
1844116,
1844114,
1844113,
1844108,
1844107,
1844104,
1844101,
1844099,
1844096,
1844091,
1844089,
1844086,
1844084,
1844080,
1844077,
1844076,
1844074,
1844056,
1844053,
1844052,
1844051,
1844049,
1844048,
1844046,
1844045,
1844044,
1844043,
1844042,
1844041,
1844040,
1844038,
1844037,
1844036,
1844034,
1844033,
1844031,
1844019,
1844017,
1844015,
1844013,
1844011,
1844009,
1844008,
1844002,
1844001,
1844000,
1843999,
1843989,
1843984,
1843978,
1843977,
1843976,
1843971,
1843968,
1843967,
1843966,
1843964,
1843962,
1843961,
1843959,
1843957,
1843956,
1843954,
1843952,
1843951,
1843949,
1843948,
1843946,
1843945,
1843944,
1843940,
1843938,
1843937,
1843936,
1843934,
1843932,
1843930,
1843928,
1843925,
1843924,
1843923,
1843921,
1843920,
1843918,
1843917,
1843916,
1843914,
1843912,
1843910,
1843909,
1843906,
1843905,
1843904,
1843903,
1843902,
1843901,
1843899,
1843896,
1843895,
1843665,
1843662,
1843661,
1843660,
1843656,
1843649,
1843647,
1843646,
1843645,
1843643,
1843642,
1843641,
1843638,
1843636,
1843635,
1843634,
1843633,
1843632,
1843631,
1843630,
1843629,
1843628,
1843625,
1843623,
1843622,
1843621,
1843620,
1843617,
1843615,
1843613,
1843612,
1843611,
1843609,
1843608,
1843607,
1843606,
1843605,
1843604,
1843600,
1843599,
1843598,
1843597,
1843596,
1843595,
1843594,
1843593,
1843592,
1843591,
1843589,
1843588,
1843587,
1843586,
1843585,
1843584,
1843583,
1843582,
1843581,
1843580,
1843579,
1843577,
1843576,
1843575,
1843573,
1843571,
1843569,
1843568,
1843567,
1843566,
1843564,
1843563,
1843562,
1843561,
1843560,
1843559,
1843558,
1843557,
1843556,
1843555,
1843554,
1843553,
1843552,
1843551,
1843550,
1843549,
1843548,
1843547,
1843546,
1843545,
1843544,
1843543,
1843542,
1843541,
1843540,
1843537,
1843535,
1843534,
1843532,
1843530,
1843529,
1843527,
1843525,
1843522,
1843521,
1843520,
1843519,
1843518,
1843517,
1843516,
1843501,
1843500,
1843497,
1843491,
1843490,
1843486,
1843484,
1843481,
1843479,
1843478,
1843476,
1843475,
1843473,
1843472,
1843471,
1843470,
1843469,
1843468,
1843467,
1843465,
1843464,
1843462,
1843460,
1843452,
1843451,
1843449,
1843448,
1843447,
1843445,
1843444,
1843441,
1843438,
1843436,
1843433,
1843429,
1843427,
1843425,
1843423,
1843422,
1843415,
1843414,
1843411,
1843410,
1843407,
1843406,
1843405,
1843403,
1843402,
1843400,
1843399,
1843398,
1843397,
1843396,
1843395,
1843394,
1843393,
1843392,
1843390,
1843389,
1843387,
1843386,
1843385,
1843383,
1843382,
1843379,
1843378,
1843375,
1843373,
1843210,
1843209,
1843208,
1843206,
1843205,
1843204,
1843203,
1843202,
1843201,
1843200,
1843199,
1843198,
1843197,
1843196,
1843195,
1843194,
1843193,
1843192,
1843191,
1843190,
1843189,
1843187,
1843186,
1843185,
1843184,
1843180,
1843179,
1843177,
1843176,
1843172,
1843171,
1843170,
1843168,
1843166,
1843165,
1843164,
1843162,
1843161,
1843160,
1843159,
1843158,
1843157,
1843155,
1843154,
1843153,
1843152,
1843151,
1843150,
1843149,
1843148,
1843147,
1843146,
1843145,
1843144,
1843143,
1843142,
1843141,
1843140,
1843139,
1843138,
1843137,
1843136,
1843135,
1843134,
1843133,
1843132,
1843131,
1843130,
1843129,
1843128,
1843127,
1843126,
1843125,
1843124,
1843123,
1843122,
1843121,
1843120,
1843119,
1843118,
1843117,
1843116,
1843115,
1843114,
1843113,
1843112,
1843111,
1843109,
1843108,
1843107,
1843106,
1843105,
1843104,
1843103,
1843102,
1843101,
1843100,
1843097,
1843073,
1843071,
1843069,
1843068,
1843067,
1843066,
1843063,
1843060,
1843053,
1843052,
1843050,
1843049,
1843048,
1843047,
1843046,
1843045,
1843044,
1843043,
1843042,
1843039,
1843038,
1843037,
1843035,
1843034,
1843033,
1843032,
1843031,
1843030,
1843029,
1843027,
1843026,
1843025,
1843024,
1842729,
1842728,
1842727,
1842726,
1842725,
1842724,
1842722,
1842721,
1842718,
1842717,
1842716,
1842712,
1842709,
1842703,
1842700,
1842697,
1842696,
1842695,
1842693,
1842692,
1842690,
1842685,
1842684,
1842680,
1842679,
1842674,
1842673,
1842672,
1842668,
1842667,
1842666,
1842665,
1842664,
1842663,
1842661,
1842660,
1842659,
1842658,
1842657,
1842656,
1842654,
1842649,
1842645,
1842640,
1842638,
1842635,
1842633,
1842632,
1842627,
1842625,
1842623,
1842622,
1842621,
1842620,
1842619,
1842616,
1842615,
1842614,
1842612,
1842609,
1842608,
1842606,
1842604,
1842602,
1842601,
1842600,
1842598,
1842597,
1842596,
1842595,
1842593,
1842592,
1842590,
1842588,
1842586,
1842584,
1842583,
1842582,
1842581,
1842580,
1842577,
1842575,
1842574,
1842573,
1842572,
1842571,
1842569,
1842568,
1842564,
1842561,
1842558,
1842556,
1842554,
1842552,
1842551,
1842550,
1842549,
1842547,
1842546,
1842545,
1842541,
1842539,
1842537,
1842536,
1842531,
1842530,
1842527,
1842525,
1842523,
1842521,
1842520,
1842518,
1842517,
1842516,
1842514,
1842513,
1842510,
1842509,
1842508,
1842507,
1842506,
1842505,
1842502,
1842501,
1842500,
1842499,
1842488,
1842487,
1842484,
1842480,
1842475,
1842474,
1842473,
1842469,
1842468,
1842467,
1842466,
1842464,
1842463,
1842462,
1842461,
1842459,
1842455,
1842454,
1842451,
1842448,
1842447,
1842446,
1842237,
1842236,
1842234,
1842232,
1842231,
1842230,
1842228,
1842223,
1842221,
1842219,
1842217,
1842213,
1842211,
1842208,
1842205,
1842204,
1842203,
1842202,
1842201,
1842199,
1842197,
1842196,
1842186,
1842185,
1842184,
1842180,
1842179,
1842178,
1842177,
1842176,
1842175,
1842174,
1842173,
1842171,
1842170,
1842168,
1842167,
1842166,
1842165,
1842164,
1842162,
1842159,
1842157,
1842155,
1842152,
1842150,
1842148,
1842147,
1842146,
1842144,
1842143,
1842142,
1842140,
1842139,
1842138,
1842134,
1842132,
1842131,
1842129,
1842128,
1842127,
1842125,
1842124,
1842122,
1842121,
1842120,
1842119,
1842118,
1842116,
1842115,
1842114,
1842113,
1842112,
1842110,
1842109,
1842108,
1842106,
1842105,
1842104,
1842103,
1842102,
1842100,
1842099,
1842098,
1842097,
1842096,
1842094,
1842093,
1842089,
1842088,
1842086,
1842085,
1842083,
1842081,
1842080,
1842079,
1842077,
1842075,
1842074,
1842073,
1842071,
1842067,
1842066,
1842065,
1842062,
1842059,
1842058,
1842057,
1842055,
1842052,
1842050,
1842049,
1842041,
1842039,
1842037,
1842035,
1842034,
1842033,
1842031,
1842030,
1842029,
1842028,
1842026,
1842024,
1842023,
1842022,
1842021,
1842020,
1842017,
1842016,
1842015,
1842010,
1842009,
1842008,
1842007,
1842005,
1842003,
1842002,
1842001,
1842000,
1841999,
1841751,
1841750,
1841749,
1841747,
1841745,
1841744,
1841743,
1841742,
1841739,
1841737,
1841734,
1841733,
1841732,
1841729,
1841719,
1841717,
1841714,
1841712,
1841709,
1841704,
1841696,
1841693,
1841691,
1841688,
1841687,
1841686,
1841685,
1841684,
1841683,
1841681,
1841679,
1841675,
1841642,
1841637,
1841635,
1841632,
1841631,
1841629,
1841627,
1841625,
1841624,
1841623,
1841622,
1841621,
1841619,
1841618,
1841617,
1841615,
1841614,
1841612,
1841611,
1841609,
1841608,
1841607,
1841606,
1841605,
1841604,
1841601,
1841600,
1841597,
1841595,
1841594,
1841593,
1841590,
1841586,
1841585,
1841584,
1841582,
1841581,
1841580,
1841579,
1841578,
1841576,
1841575,
1841574,
1841573,
1841572,
1841570,
1841568,
1841567,
1841565,
1841564,
1841563,
1841561,
1841560,
1841559,
1841558,
1841557,
1841556,
1841555,
1841554,
1841552,
1841551,
1841550,
1841548,
1841547,
1841546,
1841545,
1841544,
1841542,
1841541,
1841540,
1841539,
1841538,
1841536,
1841534,
1841531,
1841528,
1841527,
1841524,
1841523,
1841522,
1841521,
1841519,
1841518,
1841517,
1841516,
1841515,
1841514,
1841266,
1841264,
1841263,
1841260,
1841259,
1841258,
1841257,
1841251,
1841249,
1841246,
1841245,
1841244,
1841243,
1841240,
1841239,
1841237,
1841236,
1841235,
1841234,
1841232,
1841231,
1841229,
1841228,
1841225,
1841220,
1841218,
1841211,
1841210,
1841209,
1841208,
1841207,
1841206,
1841204,
1841203,
1841202,
1841200,
1841199,
1841196,
1841195,
1841193,
1841174,
1841173,
1841172,
1841171,
1841169,
1841167,
1841163,
1841161,
1841160,
1841159,
1841157,
1841154,
1841151,
1841150,
1841148,
1841146,
1841145,
1841144,
1841143,
1841140,
1841139,
1841137,
1841136,
1841134,
1841132,
1841127,
1841126,
1841124,
1841122,
1841121,
1841119,
1841118,
1841116,
1841114,
1841113,
1841112,
1841111,
1841108,
1841106,
1841104,
1841102,
1841100,
1841098,
1841097,
1841095,
1841094,
1841092,
1841091,
1841090,
1841088,
1841087,
1841084,
1841083,
1841080,
1841078,
1841076,
1841074,
1841072,
1841070,
1841069,
1841067,
1841066,
1841065,
1841064,
1841063,
1841062,
1841061,
1841060,
1841059,
1841058,
1841056,
1841054,
1841053,
1841051,
1841050,
1841048,
1841047,
1841046,
1841045,
1841044,
1841043,
1841042,
1841041,
1841040,
1841036,
1841034,
1841033,
1841031,
1841030,
1841029,
1841028,
1841027,
1841026,
1841024,
1841023,
1841022,
1841021,
1841020,
1841019,
1841018,
1841017,
1841016,
1841015,
1841014,
1841013,
1841012,
1840803,
1840801,
1840800,
1840799,
1840797,
1840796,
1840795,
1840791,
1840790,
1840789,
1840787,
1840785,
1840784,
1840783,
1840780,
1840777,
1840775,
1840774,
1840773,
1840771,
1840768,
1840767,
1840766,
1840710,
1840709,
1840707,
1840700,
1840697,
1840695,
1840694,
1840692,
1840691,
1840688,
1840685,
1840684,
1840683,
1840681,
1840679,
1840678,
1840672,
1840668,
1840667,
1840666,
1840664,
1840663,
1840661,
1840660,
1840658,
1840657,
1840656,
1840655,
1840654,
1840653,
1840649,
1840643,
1840640,
1840639,
1840638,
1840637,
1840636,
1840635,
1840634,
1840633,
1840632,
1840631,
1840628,
1840627,
1840624,
1840623,
1840622,
1840621,
1840619,
1840617,
1840615,
1840614,
1840613,
1840610,
1840609,
1840607,
1840604,
1840602,
1840601,
1840599,
1840597,
1840594,
1840592,
1840589,
1840588,
1840586,
1840585,
1840583,
1840582,
1840581,
1840580,
1840578,
1840576,
1840575,
1840574,
1840573,
1840572,
1840571,
1840568,
1840561,
1840558,
1840557,
1840556,
1840555,
1840553,
1840552,
1840551,
1840550,
1840549,
1840548,
1840547,
1840546,
1840545,
1840544,
1840542,
1840541,
1840540,
1836062,
1835730,
1835729,
1835727,
1835725,
1835724,
1835723,
1835720,
1835718,
1835717,
1835716,
1835713,
1835712,
1835711,
1835710,
1835709,
1835708,
1835707,
1835706,
1835705,
1835704,
1835703,
1835702,
1835700,
1835699,
1835698,
1835697,
1835696,
1835695,
1835694,
1835687,
1835686,
1835685,
1835684,
1835683,
1835682,
1835681,
1835679,
1835678,
1835677,
1835676,
1835675,
1835674,
1835673,
1835672,
1835671,
1835670,
1835667,
1835665,
1835664,
1835663,
1835662,
1835661,
1835659,
1835658,
1835657,
1835655,
1835654,
1835652,
1835651,
1835650,
1835649,
1835648,
1835647,
1835646,
1835645,
1835643,
1835641,
1835640,
1835639,
1835638,
1835637,
1835636,
1835635,
1835634,
1835633,
1835632,
1835631,
1835630,
1835629,
1835626,
1835624,
1835619,
1835618,
1835617,
1835616,
1835615,
1835613,
1835612,
1835611,
1835610,
1835609,
1835608,
1835607,
1835606,
1835605,
1835604,
1835603,
1835602,
1835601,
1835600,
1835599,
1835598,
1835597,
1835596,
1835594,
1835593,
1835592,
1835591,
1835590,
1835589,
1835586,
1835584,
1835581,
1835579,
1835575,
1835573,
1835571,
1835570,
1835569,
1835567,
1835566,
1835565,
1835564,
1835562,
1835561,
1835559,
1835558,
1835557,
1835556,
1835555,
1835553,
1835549,
1835547,
1835541,
1835540,
1835538,
1835537,
1835536,
1835535,
1835533,
1835531,
1835530,
1835528,
1835525,
1835524,
1835523,
1835522,
1835317,
1835316,
1835315,
1835314,
1835313,
1835311,
1835310,
1835309,
1835306,
1835304,
1835301,
1835299,
1835298,
1835297,
1835294,
1835293,
1835292,
1835291,
1835290,
1835288,
1835286,
1835282,
1835280,
1835278,
1835277,
1835275,
1835274,
1835273,
1835272,
1835271,
1835270,
1835269,
1835267,
1835266,
1835265,
1835263,
1835262,
1835261,
1835260,
1835259,
1835258,
1835257,
1835256,
1835255,
1835253,
1835252,
1835249,
1835247,
1835246,
1835245,
1835244,
1835243,
1835242,
1835241,
1835240,
1835239,
1835238,
1835237,
1835236,
1835235,
1835234,
1835229,
1835228,
1835227,
1835226,
1835225,
1835223,
1835220,
1835219,
1835218,
1835217,
1835216,
1835215,
1835214,
1835213,
1835212,
1835211,
1835209,
1835208,
1835207,
1835206,
1835204,
1835203,
1835202,
1835201,
1835200,
1835199,
1835198,
1835197,
1835196,
1835194,
1835193,
1835192,
1835191,
1835190,
1835189,
1835188,
1835187,
1835185,
1835183,
1835182,
1835181,
1835180,
1835178,
1835177,
1835176,
1835174,
1835172,
1835171,
1835170,
1835169,
1835166,
1835163,
1835162,
1835161,
1835160,
1835159,
1835158,
1835157,
1835156,
1835155,
1835154,
1835153,
1835152,
1835151,
1835150,
1835149,
1835147,
1835146,
1835145,
1835143,
1835142,
1835141,
1835140,
1835139,
1835138,
1835137,
1835136,
1835135,
1835134,
1835133,
1835131,
1835130,
1835129,
1835128,
1835127,
1835125,
1835124,
1835123,
1835122,
1835121,
1835120,
1835119,
1835118,
1835115,
1835114,
1835113,
1835111,
1835110,
1835109,
1835108,
1835105,
1835103,
1835097,
1835091,
1835089,
1835087,
1835085,
1835084,
1835081,
1835080,
1835079,
1835078,
1835077,
1835074,
1835073,
1835072,
1835071,
1835070,
1835069,
1835068,
1835067,
1835066,
1835065,
1834872,
1834868,
1834866,
1834863,
1834862,
1834861,
1834860,
1834859,
1834857,
1834856,
1834855,
1834854,
1834853,
1834852,
1834851,
1834850,
1834849,
1834848,
1834847,
1834846,
1834845,
1834844,
1834843,
1834842,
1834841,
1834840,
1834839,
1834838,
1834837,
1834836,
1834835,
1834834,
1834833,
1834830,
1834828,
1834827,
1834826,
1834825,
1834824,
1834823,
1834822,
1834821,
1834820,
1834819,
1834818,
1834817,
1834798,
1834796,
1834787,
1834786,
1834785,
1834782,
1834778,
1834777,
1834775,
1834774,
1834772,
1834769,
1834767,
1834765,
1834761,
1834757,
1834756,
1834754,
1834753,
1834751,
1834750,
1834749,
1834747,
1834742,
1834741,
1834740,
1834739,
1834738,
1834737,
1834736,
1834735,
1834734,
1834733,
1834731,
1834729,
1834728,
1834727,
1834726,
1834725,
1834724,
1834723,
1834722,
1834721,
1834718,
1834716,
1834715,
1834712,
1834711,
1834709,
1834707,
1834706,
1834705,
1834704,
1834703,
1834702,
1834701,
1834700,
1834699,
1834698,
1834697,
1834696,
1834695,
1834694,
1834693,
1834692,
1834691,
1834690,
1834689,
1834688,
1834687,
1834686,
1834685,
1834684,
1834683,
1834681,
1834680,
1834679,
1834675,
1834674,
1834673,
1834670,
1834669,
1834666,
1834665,
1834663,
1834662,
1834660,
1834658,
1834656,
1834655,
1834651,
1834649,
1834648,
1834647,
1834646,
1834645,
1834644,
1834643,
1834642,
1834641,
1834640,
1834639,
1834638,
1834637,
1834636,
1834634,
1834633,
1834632,
1834631,
1834630,
1834629,
1834628,
1834627,
1834626,
1834625,
1834624,
1834623,
1834622,
1834621,
1834620,
1834619,
1834404,
1834403,
1834402,
1834401,
1834399,
1834395,
1834393,
1834391,
1834390,
1834389,
1834388,
1834387,
1834386,
1834385,
1834384,
1834383,
1834382,
1834381,
1834380,
1834379,
1834378,
1834377,
1834376,
1834375,
1834374,
1834373,
1834372,
1834371,
1834370,
1834369,
1834366,
1834362,
1834361,
1834358,
1834357,
1834356,
1834355,
1834354,
1834353,
1834352,
1834350,
1834349,
1834348,
1834347,
1834346,
1834345,
1834344,
1834343,
1834342,
1834341,
1834340,
1834339,
1834338,
1834337,
1834336,
1834335,
1834332,
1834329,
1834298,
1834294,
1834291,
1834287,
1834284,
1834279,
1834275,
1834273,
1834269,
1834268,
1834267,
1834266,
1834265,
1834264,
1834263,
1834262,
1834261,
1834260,
1834258,
1834257,
1834256,
1834255,
1834254,
1834253,
1834251,
1834248,
1834245,
1834242,
1834240,
1834238,
1834235,
1834233,
1834230,
1834229,
1834227,
1834225,
1834219,
1834215,
1834211,
1834210,
1834207,
1834206,
1834203,
1834199,
1834195,
1834193,
1834192,
1834188,
1834186,
1834184,
1834181,
1834179,
1834177,
1834176,
1834174,
1834172,
1834159,
1834154,
1834151,
1834148,
1834146,
1834145,
1834144,
1834142,
1834139,
1834136,
1834122,
1834121,
1834120,
1834117,
1834116,
1834113,
1834111,
1834110,
1834108,
1834107,
1834105,
1834104,
1834103,
1834102,
1834101,
1834100,
1834099,
1834098,
1834097,
1834096,
1834095,
1834094,
1834093,
1834092,
1834091,
1834090,
1834089,
1834088,
1834087,
1834085,
1834084,
1834081,
1834079,
1833759,
1833757,
1833755,
1833753,
1833752,
1833751,
1833749,
1833748,
1833747,
1833746,
1833745,
1833744,
1833726,
1833725,
1833724,
1833722,
1833721,
1833720,
1833719,
1833716,
1833714,
1833713,
1833709,
1833703,
1833697,
1833689,
1833679,
1833667,
1833661,
1833654,
1833651,
1833649,
1833643,
1833630,
1833626,
1833622,
1833620,
1833611,
1833607,
1833536,
1833352,
1833351,
1833350,
1833349,
1833347,
1833345,
1833344,
1833343,
1833341,
1833338,
1833337,
1833336,
1833335,
1833334,
1833332,
1833331,
1833329,
1833328,
1833326,
1833324,
1833323,
1833316,
1833315,
1833313,
1833311,
1833309,
1833308,
1833306,
1833303,
1833302,
1833300,
1833299,
1833297,
1833294,
1833292,
1833289,
1833288,
1833286,
1833283,
1833279,
1833278,
1833274,
1833270,
1833269,
1833268,
1833267,
1833266,
1833265,
1833263,
1833262,
1833261,
1833259,
1833258,
1833257,
1833255,
1833252,
1833251,
1833250,
1833249,
1833248,
1833247,
1833246,
1833243,
1833240,
1833237,
1833232,
1833229,
1833228,
1833223,
1833222,
1833221,
1833219,
1833218,
1833217,
1833216,
1833215,
1833213,
1833212,
1833211,
1833210,
1833209,
1833206,
1833205,
1833204,
1833201,
1833199,
1833198,
1833194,
1833193,
1833192,
1833191,
1833190,
1833189,
1833188,
1833187,
1833186,
1833185,
1833183,
1833181,
1833180,
1833175,
1833174,
1833173,
1833172,
1833171,
1833170,
1833169,
1833168,
1833167,
1833166,
1833165,
1833164,
1833163,
1833162,
1833161,
1833160,
1833159,
1833158,
1833157,
1833156,
1833155,
1833154,
1833153,
1833152,
1833151,
1833150,
1833109,
1833108,
1833107,
1833106,
1833105,
1833104,
1833103,
1833102,
1833101,
1833100,
1833099,
1833098,
1833097,
1833096,
1833095,
1833094,
1833093,
1833092,
1833091,
1833090,
1833089,
1833088,
1833087,
1833086,
1833085,
1833084,
1832983,
1832894,
1832893,
1832891,
1832890,
1832889,
1832888,
1832887,
1832886,
1832885,
1832884,
1832882,
1832881,
1832880,
1832879,
1832878,
1832877,
1832876,
1832874,
1832873,
1832872,
1832871,
1832870,
1832869,
1832868,
1832867,
1832866,
1832865,
1832863,
1832862,
1832860,
1832859,
1832858,
1832857,
1832856,
1832854,
1832852,
1832851,
1832850,
1832849,
1832848,
1832846,
1832845,
1832844,
1832843,
1832842,
1832840,
1832839,
1832838,
1832837,
1832836,
1832835,
1832833,
1832832,
1832830,
1832825,
1832824,
1832821,
1832820,
1832819,
1832818,
1832817,
1832816,
1832815,
1832813,
1832812,
1832810,
1832809,
1832808,
1832807,
1832804,
1832803,
1832802,
1832801,
1832800,
1832799,
1832798,
1832797,
1832796,
1832795,
1832794,
1832793,
1832792,
1832790,
1832789,
1832788,
1832787,
1832786,
1832784,
1832783,
1832782,
1832781,
1832779,
1832778,
1832777,
1832776,
1832773,
1832772,
1832770,
1832769,
1832768,
1832767,
1832766,
1832765,
1832763,
1832762,
1832761,
1832760,
1832759,
1832758,
1832757,
1832756,
1832755,
1832754,
1832753,
1832752,
1832751,
1832750,
1832749,
1832748,
1832747,
1832746,
1832745,
1832744,
1832743,
1832742,
1832741,
1832740,
1832739,
1832738,
1832737,
1832736,
1832735,
1832734,
1832733,
1832732,
1832731,
1832730,
1832729,
1832728,
1832727,
1832726,
1832725,
1832723,
1832722,
1832720,
1832719,
1832718,
1832717,
1832716,
1832715,
1832714,
1832713,
1832712,
1832711,
1832710,
1832709,
1832708,
1832707,
1832705,
1832704,
1832703,
1832700,
1832695,
1832694,
1832693,
1832692,
1832691,
1832689,
1832688,
1832687,
1832686,
1832685,
1832558,
1832557,
1832554,
1832553,
1832552,
1832549,
1832547,
1832544,
1832542,
1832541,
1832540,
1832538,
1832537,
1832536,
1832535,
1832534,
1832533,
1832532,
1832531,
1832529,
1832528,
1832527,
1832526,
1832524,
1832523,
1832521,
1832520,
1832519,
1832517,
1832516,
1832515,
1832514,
1832513,
1832512,
1832510,
1832509,
1832508,
1832506,
1832501,
1832500,
1832499,
1832498,
1832496,
1832495,
1832494,
1832493,
1832492,
1832491,
1832490,
1832489,
1832488,
1832487,
1832485,
1832482,
1832480,
1832479,
1832478,
1832476,
1832475,
3209428,
3209427,
3203437,
3203436,
3201147,
3201146,
3201145,
3201144,
3196376,
3196375,
3196373,
3196372,
3115078,
3115077,
3115076,
3115075,
3115074,
3099761,
3099760,
3099757,
3099756,
3099755,
3099754,
3099752,
3099748,
3099745,
3099739,
3099736,
3099731,
3099729,
3099728,
3099727,
3099725,
3099721,
3099719,
3099718,
3099715,
3099714,
3099713,
3099712,
3099708,
3099705,
3099701,
3099696,
2833053,
2768805,
2752181,
2683328,
2655891,
2655884,
2655879,
2655876,
2655870,
2655866,
2655862,
2655860,
2655858,
2655856,
2655854,
2655850,
2655849,
2655845,
2655842,
2655839,
2655831,
2508816,
2508815,
2508814,
2508813,
2508812,
2508811,
2508810,
2508809,
2508808,
2487006,
2487003,
2487002,
2487001,
2486998,
2486996,
2486993,
2486990,
2486986,
2486983,
2486981,
2486976,
2486973,
2486967,
2486966,
2486964,
2486961,
2486958,
2486955,
2486952,
2486950,
2486948,
2486945,
2486944,
2486927,
2486926,
2486924,
2486923,
2486922,
2486921,
2486918,
2486917,
2486915,
2486912,
2486908,
2486906,
2486905,
2486904,
2486903,
2486902,
2486900,
2486897,
2486895,
2486893,
2486892,
2486891,
2486890,
2486887,
2363570,
2363568,
2363566,
2329038,
2329037,
2329036,
2329027,
2329026,
2329024,
2329021,
2329016,
2329012,
2329010,
2329008,
2329007,
2329004,
2329003,
2328999,
2243612,
2243605,
2243604,
2243601,
2243600,
2243590,
2243589,
2243588,
2243585,
2243584,
2243583,
2243581,
2243580,
2243572,
2243571,
2243570,
2243568,
2243567,
2243564,
2243563,
2243562,
2243561,
2243558,
2243555,
2243553,
2243550,
2243549,
2243548,
2243547,
2243545,
2243544,
2243543,
2243542,
2243540,
2243539,
2243538,
2243536,
2217899,
2217898,
2217896,
2217894,
2217891,
2217889,
2217887,
2217886,
2217870,
2217869,
2217868,
2217866,
2217864,
2217863,
2217862,
2217859,
2217856,
2217852,
2217848,
2217842,
2217837,
2217835,
2217833,
2217831,
2217830,
2217829,
2217828,
2217827,
2217826,
2217825,
2217819,
2217818,
2217814,
2217813,
2217812,
2217809,
2217801,
2217800,
2193597,
2193595,
2193594,
2193590,
2193588,
2193585,
2193580,
2187879,
2187878,
2187873,
2187867,
2187862,
2187860,
2187856,
2187854,
2187851,
2187846,
2112688,
2112687,
2112686,
2112681,
2112680,
2112678,
2112677,
2112675,
2112672,
2112671,
2112670,
2112664,
2112663,
2112662,
2112660,
2112658,
2112636,
2112634,
2112633,
2112631,
2112629,
2112628,
2112623,
2112622,
2112620,
2112619,
2112618,
2112617,
2112616,
2112615,
2112613,
2112611,
2112609,
2112608,
2112607,
2112606,
2112583,
2112581,
2112579,
2112575,
2112573,
2112570,
2112568,
2112566,
2112563,
2112561,
2112559,
2112558,
2112557,
2112555,
2112554,
2112538,
2112537,
2112535,
2112534,
2112533,
2112531,
2112530,
2112528,
2112525,
2112522,
2112521,
2112517,
2112510,
2112505,
2112503,
2112502,
2112495,
2112494,
2112493,
2112491,
2112488,
2112487,
2112486,
2112484,
2112482,
2112481,
2112477,
2112475,
2112473,
2112472,
2112470,
2112468,
2112465,
2112455,
2112449,
2112446,
2112435,
2112434,
2112432,
2112430,
2112429,
2112428,
2112427,
2112426,
2112422,
2112420,
2112416,
2112414,
2102496,
2016258,
2016254,
2016253,
2016252,
2016250,
2016249,
2016248,
2016246,
2016244,
2016242,
2016241,
2016239,
2016238,
2016237,
2016236,
2016235,
2016234,
2016232,
2016231,
2016229,
2016227,
2016226,
2016225,
2016224,
2016223,
2016222,
2016221,
2016220,
2016218,
2016217,
2016216,
2016215,
2016214,
2016213,
2016212,
2016210,
2016208,
2016207,
2016206,
2016205,
2016202,
2016199,
2016197,
2016196,
2016195,
2016194,
2016193,
2016192,
2016190,
1979194,
1979193,
1979191,
1979189,
153101,
153100,
153099,
153092,
153091,
153088,
153086,
153085,
153083,
153082,
153080,
153078,
153077,
153076,
153074,
153070,
153066,
153062,
153060,
153059,
153056,
153054,
153051,
153049,
153046,
153044,
153043,
153042,
153040,
153039,
153037,
153032,
153030,
153029,
153027,
153026,
153025,
153023,
153022,
153021,
153018,
153017,
153015,
153008,
153005,
153000,
152996,
152991,
152990,
152986,
152984,
152983,
152981,
152977,
152976,
152974,
152970,
152969,
152966,
152960,
152955,
152952,
152943,
152941,
152938,
152843,
152841,
152840,
152839,
152838,
152836,
152835,
152833,
152832,
152830,
152829,
152827,
152826,
152824,
152823,
152822,
152821,
152820,
152819,
152817,
152816,
152815,
152814,
152813,
152812,
152809,
152807,
152804,
152802,
152797,
152796
)

	and disabled='N'
	IF @@ERROR <> 0 GOTO on_error


update @docs set fileExists = 1
where dbo.fn_fileExists(@oldDocumentRoot + '\' + filename) = 1


declare @docCount int 
set @docCount = 1

SELECT @minID = min(id) from @docs
WHILE @minID is not null BEGIN

	SELECT @doc_olddocumentId = olddocumentid,
		@doc_contributorDepomemberDataID = contributorDepomemberDataID,
		@doc_docTitle = docTitle,
		@doc_docDesc = docDesc,
		@doc_filename = filename,
		@doc_fileExt = fileExt,
		@doc_dateCreated = dateCreated,
		@doc_dateModified = dateModified,
		@doc_ctDesc = ctDesc,
		@doc_dtDesc = dtDesc,
		@doc_jurisdiction = jurisdiction,
		@doc_pages = pages,
		@doc_hot = hot,
		@doc_style = style,
		@doc_fileExists = fileExists
	from @docs
	WHERE ID = @minID

BEGIN TRAN

	select @doc_newSiteResourceID = null
	select @doc_newDocumentID = null
	select @documentLanguageID = null
	select @documentVersionID = null

	select @doc_contributorMemberID = m.memberid
	from ams_members m
	inner join ams_membernetworkprofiles mp on mp.memberid = m.memberid and m.memberID = m.activeMemberID
	inner join ams_networkProfiles np on np.profileid = mp.profileID and np.depomemberdataid = @doc_contributorDepomemberDataID 
	where orgid = @orgID
	
	IF (@doc_contributorMemberID is null) BEGIN
		set @doc_contributorDepomemberDataID = 461530
	END
	print 'DocCount: ' + cast(@docCount as varchar(10))
	print 'Contributor ID: ' + cast(@doc_contributorDepomemberDataID as varchar(10))
	print 'docTitle: ' + @doc_docTitle
--	print 'docDesc: ' + @doc_docDesc

	-- first create a resourceID for the document
	EXEC dbo.cms_createSiteResource @resourceTypeID=@documentResourceTypeID, 
		@siteResourceStatusID=@siteResourceStatusID, @siteID=@siteid, @isVisible=1, 
		@parentSiteResourceID=null, @siteResourceID=@doc_newSiteResourceID OUTPUT
	IF @@ERROR <> 0 GOTO on_error

	-- add the document
	INSERT INTO dbo.cms_documents (siteID, siteResourceID, sectionID, dateCreated)
	VALUES (@siteid, @doc_newSiteResourceID, @rootSectionID, @doc_dateCreated)
		IF @@ERROR <> 0 GOTO on_error
		SELECT @doc_newDocumentID = SCOPE_IDENTITY()		
	
	-- capture new id so we can map the categoryies later.	
	update @docs set newdocumentid = @doc_newDocumentID WHERE ID = @minID
		IF @@ERROR <> 0 GOTO on_error
	

	-- add document language
	INSERT INTO dbo.cms_documentLanguages (documentID, languageID, docTitle, docDesc, dateCreated, dateModified)
	VALUES (@doc_newDocumentID, 1, isnull(@doc_docTitle,''), isnull(@doc_docDesc,''), @doc_dateCreated, getdate())
		IF @@ERROR <> 0 GOTO on_error
		SELECT @documentLanguageID = IDENT_CURRENT('cms_documentLanguages')

	-- add content version
	EXEC @rc = dbo.cms_createDocumentVersion @documentLanguageID=@documentLanguageID, @fileName=@doc_filename, @fileExt=@doc_fileExt, @author=NULL, @contributorMemberID=@doc_contributorMemberID, @recordedByMemberID=461530, @isActive=1, @publicationDate=@doc_dateCreated, @documentVersionID=@documentVersionID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	IF (@doc_fileExists = 1) begin
		-- insert data to do copy outside transaction
		select @param1 = @oldDocumentRoot + '\' + @doc_filename
		select @param2 = @newDocumentRoot + @orgcode + '\' + @siteCode + '\' +  cast(@documentVersionID as varchar(10)) + '.pdf'
		INSERT INTO @filesToCopy (param1, param2)
		VALUES (@param1, @param2)
			IF @@ERROR <> 0 GOTO on_error
	END

	-- Add rights
	exec dbo.cms_createSiteResourceRight
		@siteID = @siteID,
		@siteResourceID = @doc_newSiteResourceID, 
		@include = 1, 
		@functionID = @viewFunctionID, 
		@roleID = null, 
		@groupID = null, 
		@memberID = null, 
		@inheritedRightsResourceID = @fsSiteResourceID, 
		@inheritedRightsFunctionID = @viewFunctionID, 
		@resourceRightID = @trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	COMMIT TRAN

	WAITFOR DELAY '00:00:00:300'	
	print '--------------------'

	set @docCount = @docCount + 1
	SELECT @minID = min(ID) from @docs where ID > @minID
END


SET nocount off

SELECT * from @docs

SELECT * from @filesToCopy

IF dbo.fn_createDirectory(@newDocumentRoot + @orgcode + '\' + @siteCode + '\') = 1 BEGIN
	-- Move files
	SELECT @minID = min(id) from @filesToCopy
	WHILE @minID is not null BEGIN
		SELECT @param1 = param1,
			@param2 = param2
		from @filesToCopy
		WHERE ID = @minID

		-- copy the file
			select @trashID = dbo.fn_copyFile(@param1, @param2, 1)
			IF @@ERROR <> 0 GOTO on_error

		SELECT @minID = min(ID) from @filesToCopy where ID > @minID
	END
END

exec dbo.cache_perms_setStatus @orgid=@orgid, @status='enabled'

print 'Import completed successfully'
GOTO on_done

on_error:
	ROLLBACK TRAN
	print 'Import NOT completed successfully'

on_done:
