use platformQueue
GO
CREATE TABLE [dbo].[tblQueueItems_renewSubscribers](
	[itemUID] [uniqueidentifier] NOT NULL CONSTRAINT [DF_tblQueueItems_renewSubscribers_itemUID]  DEFAULT (newid()),
	[itemGroupUID] [uniqueidentifier] NOT NULL CONSTRAINT [DF_tblQueueItems_renewSubscribers_itemGroupUID]  DEFAULT (newid()),
	[recordedByMemberID] [int] NOT NULL,
	[orgID] [int] NOT NULL,
	[siteID] [int] NOT NULL,
	[subscriberID] [int] NOT NULL,
	[rescindDate] [datetime] NULL,
	[overrideStartDate] [datetime] NULL,
	[errorMessage] [varchar](max) NULL,
	[wddxMessages] [xml] NULL
 CONSTRAINT [PK_tblQueueItems_renewSubscribers] PRIMARY KEY CLUSTERED 
(
	[itemUID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO


declare @queueTypeID int

INSERT INTO dbo.tblQueueTypes (queueType)
VALUES ('renewSubscribers')
	select @queueTypeID = SCOPE_IDENTITY()

INSERT INTO dbo.tblQueueStatuses (queueTypeID, queueStatus)
select @queueTypeID, queueStatus
from dbo.tblQueueStatuses 
where queueTypeID = 7
GO

CREATE PROC [dbo].[job_renewSubscribers_grabForProcessing]
@serverID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	DECLARE @batchSize int
	set @batchSize = 250

	declare @statusReady int, @statusGrabbed int
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'renewSubscribers'
		and qs.queueStatus = 'readyToProcess'
	select @statusGrabbed = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'renewSubscribers'
		and qs.queueStatus = 'grabbedForProcessing'

	IF OBJECT_ID('tempdb..#tmpTblQueueItems_renewSubscribers') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_renewSubscribers
	CREATE TABLE #tmpTblQueueItems_renewSubscribers (itemUID uniqueidentifier, jobUID uniqueidentifier, 
		recordedByMemberID int, orgID int, siteID int, subscriberID int, rescindDate datetime, overrideStartDate datetime)

	declare @jobUID uniqueidentifier
	set @jobUID = NEWID()

	-- dequeue in order of dateAdded. get @batchsize subs
	update qi WITH (UPDLOCK, READPAST)
	set qi.queueStatusID = @statusGrabbed,
		qi.dateUpdated = getdate(),
		qi.jobUID = @jobUID,
		qi.jobDateStarted = getdate(),
		qi.jobServerID = @serverID
		OUTPUT inserted.itemUID, inserted.jobUID, qid.recordedByMemberID, qid.orgID, qid.siteID, qid.subscriberID, qid.rescindDate, qid.overrideStartDate
		INTO #tmpTblQueueItems_renewSubscribers
	from platformQueue.dbo.tblQueueItems as qi
	inner join platformQueue.dbo.tblQueueItems_renewSubscribers as qid ON qid.itemUID = qi.itemUID
	inner join (
		select top(@BatchSize) qi2.itemUID 
		from platformQueue.dbo.tblQueueItems as qi2
		inner join platformQueue.dbo.tblQueueItems_renewSubscribers as qid2 ON qid2.itemUID = qi2.itemUID
		where qi2.queueStatusID = @statusReady
		order by qi2.dateAdded, qi2.itemUID
		) as batch on batch.itemUID = qi.itemUID
	where qi.queueStatusID = @statusReady

	-- final data
	select qid.itemUID, qid.jobUID, qid.recordedByMemberID, qid.orgID, qid.siteID, qid.subscriberID, qid.rescindDate, qid.overrideStartDate
	from #tmpTblQueueItems_renewSubscribers as qid
	order by qid.itemUID

	IF OBJECT_ID('tempdb..#tmpTblQueueItems_renewSubscribers') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_renewSubscribers

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH
GO

CREATE PROC [dbo].[job_renewSubscribers_grabForNotification]
AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @statusReady int, @statusGrabbed int, @queueTypeID int
	select @statusReady = qs.queueStatusID , @queueTypeID = qt.queueTypeID
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'renewSubscribers'
		and qs.queueStatus = 'readyToNotify'
	select @statusGrabbed = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'renewSubscribers'
		and qs.queueStatus = 'grabbedForNotifying'

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL 
		DROP TABLE #tmpNotify
	
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier)

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueStatuses qs on qs.queueStatusID = qi.queueStatusID
			and qs.queueTypeID = @queueTypeID
		inner join platformQueue.dbo.tblQueueItems_renewSubscribers as qid ON qid.itemUID = qi.itemUID
		where qi.queueStatusID = @statusReady
			except
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueStatuses qs on qs.queueStatusID = qi.queueStatusID
			and qs.queueTypeID = @queueTypeID
		inner join platformQueue.dbo.tblQueueItems_renewSubscribers as qid ON qid.itemUID = qi.itemUID
		where qi.queueStatusID <> @statusReady
	)
	UPDATE platformQueue.dbo.tblQueueItems WITH (UPDLOCK, READPAST)
	SET queueStatusID = @statusGrabbed,
		dateUpdated = getdate()
		OUTPUT qid.itemGroupUID
		INTO #tmpNotify
	FROM platformQueue.dbo.tblQueueItems as qi
	inner join platformQueue.dbo.tblQueueStatuses qs on qs.queueStatusID = qi.queueStatusID
		and qs.queueTypeID = @queueTypeID
	INNER JOIN platformQueue.dbo.tblQueueItems_renewSubscribers as qid ON qid.itemUID = qi.itemUID
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qid.itemGroupUID
	where qi.queueStatusID = @statusReady

	-- return itemGroupUIDs that can be marked as done
	select distinct tmpN.itemGroupUID, qid.recordedByMemberID, qid.orgID, me.email as reportEmail, s.siteName, s.siteCode
	from (select distinct itemGroupUID from #tmpNotify) as tmpN
	INNER JOIN platformQueue.dbo.tblQueueItems_renewSubscribers as qid ON qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN membercentral.dbo.ams_members as m3 on m3.memberID = qid.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as m4 on m4.memberid = m3.activeMemberID
	LEFT OUTER JOIN membercentral.dbo.ams_memberEmails as me 
		INNER JOIN membercentral.dbo.ams_memberEmailTypes as met on met.emailTypeID = me.emailTypeID and met.emailTypeOrder = 1
		on me.memberID = m4.memberID
	INNER JOIN membercentral.dbo.sites as s on s.siteID = qid.siteID
	order by tmpN.itemGroupUID

	-- return report information for failures
	select tmpN.itemGroupUID, qid.itemUID, qid.errorMessage, qid.wddxMessages
	from (select distinct itemGroupUID from #tmpNotify) as tmpN
	INNER JOIN platformQueue.dbo.tblQueueItems_renewSubscribers as qid ON qid.itemGroupUID = tmpN.itemGroupUID
	order by tmpN.itemGroupUID

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL 
		DROP TABLE #tmpNotify

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH
GO

CREATE PROC [dbo].[job_renewSubscribers_clearDone]
AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @statusDone int, @queueTypeID int
	select @statusDone = qs.queueStatusID, @queueTypeID = qt.queueTypeID
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'renewSubscribers'
		and qs.queueStatus = 'done'

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueStatuses qs on qs.queueStatusID = qi.queueStatusID
			and qs.queueTypeID = @queueTypeID
		inner join platformQueue.dbo.tblQueueItems_renewSubscribers as qid ON qid.itemUID = qi.itemUID
		where qi.queueStatusID = @statusDone
			except
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueStatuses qs on qs.queueStatusID = qi.queueStatusID
			and qs.queueTypeID = @queueTypeID
		inner join platformQueue.dbo.tblQueueItems_renewSubscribers as qid ON qid.itemUID = qi.itemUID
		where qi.queueStatusID <> @statusDone
	)
	DELETE from platformQueue.dbo.tblQueueItems
	where itemUID in (
		select qi.itemUID
		FROM platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueStatuses qs on qs.queueStatusID = qi.queueStatusID
			and qs.queueTypeID = @queueTypeID
		INNER JOIN platformQueue.dbo.tblQueueItems_renewSubscribers as qid ON qid.itemUID = qi.itemUID
		INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qid.itemGroupUID
		WHERE qi.queueStatusID = @statusDone
	)

	DELETE from platformQueue.dbo.tblQueueItems_renewSubscribers 
	where itemUID not in (select itemUID from platformQueue.dbo.tblQueueItems)

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH
GO


USE membercentral
GO
CREATE PROC [dbo].[sub_queueRenewals]
@recordedByMemberID int,
@rescindDate datetime,
@overrideStartDate datetime, 
@subscriberIDList varchar(max)

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL 
		DROP TABLE #tmpSubscribers
	CREATE TABLE #tmpSubscribers (subscriberID int, orgID int, siteID int, itemUID uniqueidentifier DEFAULT NEWID())

	declare @statusInserting int, @statusReady int
	select @statusInserting = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'renewSubscribers'
		and qs.queueStatus = 'insertingItems'
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'renewSubscribers'
		and qs.queueStatus = 'readyToProcess'

	-- there should only be one itemGroupUID for these subscribers
	declare @itemGroupUID uniqueidentifier
	set @itemGroupUID = NEWID()

	-- get subscribers to renew
	insert into #tmpSubscribers (subscriberID)
	select s.subscriberID
	from dbo.fn_intListToTable(@subscriberIDList,',') as tmp
	inner join dbo.sub_subscribers as s on s.subscriberID = tmp.listitem
		except
	select qid.subscriberID
	from platformQueue.dbo.tblQueueItems_renewSubscribers as qid
	inner join platformQueue.dbo.tblQueueItems as qi on qi.itemUID = qid.itemUID
	inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.queueStatusID
	where qs.queueStatus not in ('readyToNotify','grabbedForNotifying','done')

	-- get siteID, orgID
	update tmp
	set tmp.orgID = sites.orgID,
		tmp.siteID = sites.siteID
	from #tmpSubscribers as tmp
	inner join dbo.sub_subscribers as s on s.subscriberID = tmp.subscriberID
	inner join dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
	inner join dbo.sub_types as t on t.typeID = sub.typeID
	inner join dbo.sites on sites.siteID = t.siteID

	-- queue items
	insert into platformQueue.dbo.tblQueueItems_renewSubscribers (itemUID, itemGroupUID, recordedByMemberID, orgID, siteID, subscriberID, rescindDate, overrideStartDate)
		OUTPUT inserted.itemUID, @statusInserting 
		INTO platformQueue.dbo.tblQueueItems(itemUID, queueStatusID)
	select itemUID, @itemGroupUID, @recordedByMemberID, orgID, siteID, subscriberID, @rescindDate, @overrideStartDate
	from #tmpSubscribers

	-- update queue item groups to show ready to process
	update qi WITH (UPDLOCK, HOLDLOCK)
	set qi.queueStatusID = @statusReady,
		qi.dateUpdated = getdate()
	from platformQueue.dbo.tblQueueItems as qi
	inner join #tmpSubscribers as s on s.itemUID = qi.itemUID

	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL 
		DROP TABLE #tmpSubscribers

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

USE platformStats
GO
DROP TABLE dbo.sub_renewQueue
GO
DROP TABLE dbo.sub_renewQueueBatch
GO
DROP PROC dbo.sub_getQueueToProcess
GO
