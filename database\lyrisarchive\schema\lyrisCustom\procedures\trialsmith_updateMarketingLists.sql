ALTER PROC dbo.trialsmith_updateMarketingLists
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#memberPool') IS NOT NULL
		DROP TABLE #memberPool;
	IF OBJECT_ID('tempdb..#updatedMembers') IS NOT NULL
		DROP TABLE #updatedMembers;
	IF OBJECT_ID('tempdb..#membershipsToDelete') IS NOT NULL
		DROP TABLE #membershipsToDelete;
	IF OBJECT_ID('tempdb..#unsubs') IS NOT NULL
		DROP TABLE #unsubs;
	CREATE TABLE #memberPool (poolid INT identity(1,1), DateJoined_ DATETIME, domain_ VARCHAR(250), emailaddr_ VARCHAR(100),
		fullname_ VARCHAR(100), list_ VARCHAR(60), usernameLc_ VARCHAR(100), ExternalMemberID VARCHAR(100),
		association_ VARCHAR(10), depomemberdataid INT);
	CREATE TABLE #updatedMembers (id INT identity(1,1), poolid INT, memberID_ INT);
	CREATE TABLE #membershipsToDelete (id INT identity(1,1), memberid_ INT);
	CREATE TABLE #unsubs (id INT identity(1,1), emailaddr_ VARCHAR(100));

	DECLARE @errorSubject VARCHAR(100), @errmsg varchar(200);
	
	EXEC membercentral.trialsmith.dbo.trialsmith_getMarketingListMembers;

	IF NOT EXISTS (SELECT emailaddr_ FROM membercentral.datatransfer.dbo.trialsmithMarketingListPopulation) BEGIN
		SET @errorSubject = 'Error Updating TrialSmith Marketing Lists';
		SET @errmsg = 'trialsmith.dbo.trialsmith_getMarketingListMembers ended with no rows in table trialsmith.dbo.trialsmith_getMarketingListMembers. Check for timeout or other issues' ;
		EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
	END ELSE BEGIN
		EXEC dbo.trialsmith_syncListUnsubs 'trialsmith','trialsmith_subscribers';

		INSERT INTO #unsubs (emailaddr_)
		SELECT emailaddr_
		FROM trialslyris1.dbo.members_ m 
		WHERE list_ in ('trialsmith','trialsmith_subscribers') 
		AND m.membertype_ = 'unsub';

		INSERT INTO #memberPool (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID,association_,depomemberdataid)
		SELECT lp.DateJoined_,lp.domain_,lp.emailaddr_,lp.fullname_ ,lp.list_ ,lp.usernameLc_ ,lp.ExternalMemberID, association_, depomemberdataid
		FROM membercentral.datatransfer.dbo.trialsmithMarketingListPopulation lp
		LEFT OUTER JOIN #unsubs u ON u.emailaddr_ = lp.emailaddr_ COLLATE Latin1_General_CI_AI
		WHERE u.emailaddr_ IS NULL;

		-- delete subscribed members with email addresses that are no longer in the pool
		INSERT INTO #membershipsToDelete (memberID_)
		SELECT m.memberID_
		FROM trialslyris1.dbo.members_ m
		LEFT OUTER JOIN #memberPool pool ON m.emailaddr_ = pool.emailaddr_ COLLATE Latin1_General_CI_AI
			AND m.list_ = pool.list_ COLLATE Latin1_General_CI_AI
		WHERE m.list_ in ('trialsmith','trialsmith_subscribers') 
		AND pool.poolid IS NULL 
		AND m.membertype_ <> 'unsub';

		DELETE m
		FROM trialslyris1.dbo.members_ m
		INNER JOIN #membershipsToDelete md ON m.memberid_ = md.memberid_;

		-- update
		INSERT INTO #updatedMembers (poolid, memberid_)
		SELECT pool.poolid, m.memberID_
		FROM trialslyris1.dbo.members_ m WITH(NOLOCK)
		INNER JOIN #memberPool pool ON m.emailaddr_ = pool.emailaddr_ COLLATE Latin1_General_CI_AI
			AND m.list_ = pool.list_ COLLATE Latin1_General_CI_AI
			AND (
					m.emailaddr_ <> pool.emailaddr_ COLLATE Latin1_General_CI_AI
					or m.fullname_ <> pool.fullname_ COLLATE Latin1_General_CI_AI
					or m.ExternalMemberID <> pool.ExternalMemberID COLLATE Latin1_General_CI_AI
					or m.association_ <> pool.association_ COLLATE Latin1_General_CI_AI
					or ISNULL(m.depomemberdataID,0) <> ISNULL(pool.depomemberdataid,0)
			)
			AND m.list_ in ('trialsmith','trialsmith_subscribers')
			AND m.membertype_ <> 'unsub';

		UPDATE m 
		SET DateJoined_ = pool.DateJoined_,
			fullname_= pool.fullname_,
			list_= pool.list_,
			ExternalMemberID = pool.ExternalMemberID,
			association_ = pool.association_,
			depomemberdataid = pool.depomemberdataid
		FROM trialslyris1.dbo.members_ m WITH(NOLOCK)
		INNER JOIN #updatedMembers updated ON m.memberid_ = updated.memberid_
		INNER JOIN #memberPool pool ON updated.poolid = pool.poolid
		WHERE m.list_ in ('trialsmith','trialsmith_subscribers') 
		AND m.membertype_ <> 'unsub';

		-- delete all preexisting memberships from pool, leaving only entries that need to be created
		DELETE pool
		FROM #memberPool pool
		INNER JOIN trialslyris1.dbo.members_ m WITH(NOLOCK) ON m.emailaddr_ = pool.emailaddr_ COLLATE Latin1_General_CI_AI
			AND m.list_ = pool.list_ COLLATE Latin1_General_CI_AI
		WHERE m.list_ in ('trialsmith','trialsmith_subscribers');

		-- insert new memberships
		INSERT INTO trialslyris1.dbo.members_ (DateJoined_, domain_, emailaddr_, fullname_ , list_ , usernameLc_ ,
			ExternalMemberID, association_, depomemberdataid, mcemailkey,MCEmailKey_usernameLC,MCEmailKey_domain)
		SELECT DateJoined_, domain_, emailaddr_, fullname_ , list_ , usernameLc_ , ExternalMemberID, association_,
			depomemberdataid, mcemailkey = convert(varchar(75),HASHBYTES('SHA2_256',list_ + '|' + usernamelc_ + '@' + domain_),2),
			usernameLc_, domain_
		FROM #memberPool;

		-- update trialsmithUsage
		truncate table trialslyris1.dbo.tsdata
		INSERT INTO trialslyris1.dbo.tsdata (depoID, LDepoBuy, LDepoGive, LDepoSrc, numDepos, LListSrc, numCredits, 
			expList, numBadSrc, subType, expires)
		SELECT distinct depomemberdataid, dateLastPurchasedDepo, dateLastContributedDepo, dateLastSearchedDepo, 
			numDeposContributedInLastYear, dateLastSearchedLists, numPurchaseCreditsAvailable, 
			last10FailedDepoSearchesPast3Months, numFailedDepoSearchPast3Months, SubscriberType_, dateTrialsmithExpires
		FROM membercentral.datatransfer.dbo.trialsmithMarketingListPopulation
		WHERE depomemberdataid IS NOT NULL;

		EXEC dbo.trialsmith_syncListUnsubs 'trialsmith', 'trialsmith_subscribers';
	END

	IF OBJECT_ID('tempdb..#memberPool') IS NOT NULL
		DROP TABLE #memberPool;
	IF OBJECT_ID('tempdb..#updatedMembers') IS NOT NULL
		DROP TABLE #updatedMembers;
	IF OBJECT_ID('tempdb..#membershipsToDelete') IS NOT NULL
		DROP TABLE #membershipsToDelete;
	IF OBJECT_ID('tempdb..#unsubs') IS NOT NULL
		DROP TABLE #unsubs;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
