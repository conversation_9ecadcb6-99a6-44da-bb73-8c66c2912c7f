use membercentral
GO

ALTER PROC [dbo].[ev_importEventFromQueue]
@itemUID uniqueidentifier

AS

SET NOCOUNT ON

declare @recordedByMemberID int, @siteID int, @rowID int, @orgID int, @timeZoneID int
declare @ovAction char(1), @MCEventID int, @MCCalendarID int, @EventAllDay bit, @EventCode varchar(15), @EventHidden bit, 
	@ContactInclude bit, @LocationInclude bit, @CancellationInclude bit, @TravelInclude bit, @EventStart datetime, 
	@EventEnd datetime,	@eventContentID int, @EventTitle varchar(200), @EventDescription varchar(max), @MCCategoryID int,
	@origCalendarID int, @InternalNotes varchar(max), @RegistrationReplyEmail varchar(200), @MCRegistrationID int,
	@DisplayCredits bit, @expirationContentID int, @ContactTitle varchar(200), @Contact varchar(max), @contentTitle varchar(200), 
	@rawContent varchar(max), @LocationTitle varchar(200), @Location varchar(max), @CancellationTitle varchar(200), 
	@Cancellation varchar(max), @TravelTitle varchar(200), @Travel varchar(max), @InformationTitle varchar(200), 
	@Information varchar(max), @locationContentID int, @cancellationPolicyContentID int, @travelContentID int,
	@informationContentID int, @subEventID int, @existingEvent bit, @ASID int, @crdAuthorityCode varchar(20),
	@crdApproval varchar(50), @crdStatus varchar(20), @crdOfferedID int, @crdStatusID int, @ASTID int, @crdColName sysname,
	@crdValue decimal(6,2), @origCategoryID int, @contactContentID int, @MCParentEventID int, @ParentEventCode varchar(15)
declare @tblPossibleCredits TABLE (ASID int, authorityID int, authorityCode varchar(20))
declare @tblPossibleCreditCols TABLE (ASID int, column_name sysname, ASTID int)

-- update status and get event info
BEGIN TRY
	EXEC platformQueue.dbo.queue_setStatus @queueType='importEvents', @itemUID=@itemUID, @queueStatus='processingEvent'

	select top 1 @recordedByMemberID=recordedByMemberID, @siteID=siteID, @rowID=dataKey
	from platformQueue.dbo.tblQueueItemData
	where itemUID = @itemUID

	select @orgID=orgID, @timeZoneID=defaultTimeZoneID from dbo.sites where siteID=@siteID

	IF OBJECT_ID('tempdb..#tmpEVQueueData') IS NOT NULL 
		DROP TABLE #tmpEVQueueData		
	select dc.columnname, qid.columnValueString, qid.columnValueDecimal2, qid.columnValueInteger, qid.columnvalueDate, 
		qid.columnValueBit, qid.columnValueXML, qid.columnValueText
	into #tmpEVQueueData
	from platformQueue.dbo.tblQueueItemData as qid
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
	where qid.itemUID = @itemUID

END TRY
BEGIN CATCH
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH


-- setup transaction to import event
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	select @ovAction = columnValueString from #tmpEVQueueData where columnname = 'ovAction'
	select @MCEventID = columnValueInteger from #tmpEVQueueData where columnname = 'MCEventID'
	select @MCRegistrationID = columnValueInteger from #tmpEVQueueData where columnname = 'MCRegistrationID'
	select @EventStart = columnValueDate from #tmpEVQueueData where columnname = 'EventStart'
	select @EventEnd = columnValueDate from #tmpEVQueueData where columnname = 'EventEnd'

	IF @MCEventID is not null
		set @existingEvent = 1
	ELSE
		set @existingEvent = 0

	-- if we are updating event with overwrite setting
	IF @existingEvent = 1 and @ovAction = 'o' BEGIN
		select @MCCalendarID = columnValueInteger from #tmpEVQueueData where columnname = 'MCCalendarID'
		select @MCCategoryID = columnValueInteger from #tmpEVQueueData where columnname = 'MCCategoryID'
		select @origCalendarID = calendarID from dbo.ev_calendarEvents where sourceEventID = @MCEventID and calendarID = sourceCalendarID
		select @origCategoryID = categoryID from dbo.ev_eventCategories where eventID = @MCEventID

		-- move to diff calendar if necessary (this also moves categories)
		IF (@MCCalendarID != @origCalendarID) BEGIN
			EXEC dbo.ev_moveCalendarEvent @eventID=@MCEventID, @fromCalendarID=@origCalendarID, @fromCategoryID=@origCategoryID, @toCalendarID=@MCCalendarID, @toCategoryID=@MCCategoryID
		END

		-- move category if necessary
		IF (@MCCalendarID = @origCalendarID AND @MCCategoryID != @origCategoryID) BEGIN
			update dbo.ev_eventCategories
			set categoryID = @MCCategoryID
			where eventID = @MCEventID
			and categoryID = @origCategoryID
		END

		-- update changed fields
		select @EventAllDay = columnValueBit from #tmpEVQueueData where columnname = 'EventAllDay'
		select @EventHidden = columnValueBit from #tmpEVQueueData where columnname = 'EventHidden'
		select @ContactInclude = columnValueBit from #tmpEVQueueData where columnname = 'ContactInclude'
		select @LocationInclude = columnValueBit from #tmpEVQueueData where columnname = 'LocationInclude'
		select @CancellationInclude = columnValueBit from #tmpEVQueueData where columnname = 'CancellationInclude'
		select @TravelInclude = columnValueBit from #tmpEVQueueData where columnname = 'TravelInclude'
		select @InternalNotes = columnValueText from #tmpEVQueueData where columnname = 'InternalNotes'

		--determine if times need to be forced to full day, if allDayEvent
		select @EventAllDay = isnull(@EventAllDay,isAllDayEvent)
		from dbo.ev_events
		where eventID = @MCEventID

		if (@EventAllDay = 1) BEGIN
			set @EventStart = Convert(DateTime, DATEDIFF(DAY, 0, @EventStart))
			set @EventEnd = dateadd(ms,-3,Convert(DateTime, DATEDIFF(DAY, -1, @EventEnd)))
		END

		update dbo.ev_events
		set isAllDayEvent = @EventAllDay,
			hiddenFromCalendar = isnull(@EventHidden,hiddenFromCalendar),
			emailContactContent = isnull(@ContactInclude,emailContactContent),
			emailLocationContent = isnull(@LocationInclude,emailLocationContent),
			emailCancelContent = isnull(@CancellationInclude,emailCancelContent),
			emailTravelContent = isnull(@TravelInclude,emailTravelContent),
			internalNotes = isnull(@InternalNotes,internalNotes)
		where eventID = @MCEventID

		-- update event times
		delete from dbo.ev_times where eventID = @MCEventID
		EXEC dbo.ev_createTime @eventID=@MCEventID, @timeZoneID=@timeZoneID, @startTime=@EventStart, @endTime=@EventEnd

		-- update title/description
		select @EventTitle = columnValueString from #tmpEVQueueData where columnname = 'EventTitle'
		select @EventDescription = columnValueText from #tmpEVQueueData where columnname = 'EventDescription'

		select @eventContentID = eventContentID FROM dbo.ev_events WHERE eventID=@MCEventID
		select @contentTitle=contentTitle, @rawContent=rawContent from dbo.fn_getContent(@eventContentID,1)
		set @EventDescription = isnull(@EventDescription,@rawContent)
		EXEC dbo.cms_updateContent @contentID=@eventContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@EventTitle, 
			@contentDesc='', @rawcontent=@EventDescription

		-- update other content objects
		select @ContactTitle = columnValueString from #tmpEVQueueData where columnname = 'ContactTitle'
		select @Contact = columnValueText from #tmpEVQueueData where columnname = 'Contact'
		select @LocationTitle = columnValueString from #tmpEVQueueData where columnname = 'LocationTitle'
		select @Location = columnValueText from #tmpEVQueueData where columnname = 'Location'
		select @CancellationTitle = columnValueString from #tmpEVQueueData where columnname = 'CancellationTitle'
		select @Cancellation = columnValueText from #tmpEVQueueData where columnname = 'Cancellation'
		select @TravelTitle = columnValueString from #tmpEVQueueData where columnname = 'TravelTitle'
		select @Travel = columnValueText from #tmpEVQueueData where columnname = 'Travel'
		select @InformationTitle = columnValueString from #tmpEVQueueData where columnname = 'InformationTitle'
		select @Information = columnValueText from #tmpEVQueueData where columnname = 'Information'

		select @contactContentID = contactContentID FROM dbo.ev_events WHERE eventID=@MCEventID
		select @contentTitle=null, @rawContent=null
		select @contentTitle=contentTitle, @rawContent=rawContent from dbo.fn_getContent(@contactContentID,1)
		set @ContactTitle = isnull(@ContactTitle,@contentTitle)
		set @Contact = isnull(@Contact,@rawContent)
		EXEC dbo.cms_updateContent @contentID=@contactContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@ContactTitle, 
			@contentDesc='', @rawcontent=@Contact

		select @locationContentID = locationContentID FROM dbo.ev_events WHERE eventID=@MCEventID
		select @contentTitle=null, @rawContent=null
		select @contentTitle=contentTitle, @rawContent=rawContent from dbo.fn_getContent(@locationContentID,1)
		set @LocationTitle = isnull(@LocationTitle,@contentTitle)
		set @Location = isnull(@Location,@rawContent)
		EXEC dbo.cms_updateContent @contentID=@locationContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@LocationTitle, 
			@contentDesc='', @rawcontent=@Location

		select @cancellationPolicyContentID = cancellationPolicyContentID FROM dbo.ev_events WHERE eventID=@MCEventID
		select @contentTitle=null, @rawContent=null
		select @contentTitle=contentTitle, @rawContent=rawContent from dbo.fn_getContent(@cancellationPolicyContentID,1)
		set @CancellationTitle = isnull(@CancellationTitle,@contentTitle)
		set @Cancellation = isnull(@Cancellation,@rawContent)
		EXEC dbo.cms_updateContent @contentID=@cancellationPolicyContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@CancellationTitle, 
			@contentDesc='', @rawcontent=@Cancellation

		select @travelContentID = travelContentID FROM dbo.ev_events WHERE eventID=@MCEventID
		select @contentTitle=null, @rawContent=null
		select @contentTitle=contentTitle, @rawContent=rawContent from dbo.fn_getContent(@travelContentID,1)
		set @TravelTitle = isnull(@TravelTitle,@contentTitle)
		set @Travel = isnull(@Travel,@rawContent)
		EXEC dbo.cms_updateContent @contentID=@travelContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@TravelTitle, 
			@contentDesc='', @rawcontent=@Travel

		select @informationContentID = informationContentID FROM dbo.ev_events WHERE eventID=@MCEventID
		select @contentTitle=null, @rawContent=null
		select @contentTitle=contentTitle, @rawContent=rawContent from dbo.fn_getContent(@informationContentID,1)
		set @InformationTitle = isnull(@InformationTitle,@contentTitle)
		set @Information = isnull(@Information,@rawContent)
		EXEC dbo.cms_updateContent @contentID=@informationContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@InformationTitle, 
			@contentDesc='', @rawcontent=@Information
	END

	-- if we are adding new event
	IF @existingEvent = 0 BEGIN
		select @MCCalendarID = columnValueInteger from #tmpEVQueueData where columnname = 'MCCalendarID'
		select @MCCategoryID = columnValueInteger from #tmpEVQueueData where columnname = 'MCCategoryID'
		select @EventAllDay = isnull(columnValueBit,0) from #tmpEVQueueData where columnname = 'EventAllDay'
		select @EventCode = columnValueString from #tmpEVQueueData where columnname = 'EventCode'
		select @EventHidden = isnull(columnValueBit,0) from #tmpEVQueueData where columnname = 'EventHidden'
		select @ContactInclude = isnull(columnValueBit,0) from #tmpEVQueueData where columnname = 'ContactInclude'
		select @LocationInclude = isnull(columnValueBit,0) from #tmpEVQueueData where columnname = 'LocationInclude'
		select @CancellationInclude = isnull(columnValueBit,0) from #tmpEVQueueData where columnname = 'CancellationInclude'
		select @TravelInclude = isnull(columnValueBit,0) from #tmpEVQueueData where columnname = 'TravelInclude'
		select @EventTitle = columnValueString from #tmpEVQueueData where columnname = 'EventTitle'
		select @EventDescription = isnull(columnValueText,'') from #tmpEVQueueData where columnname = 'EventDescription'
		select @InternalNotes = isnull(columnValueText,'') from #tmpEVQueueData where columnname = 'InternalNotes'
		select @ContactTitle = isnull(columnValueString,'') from #tmpEVQueueData where columnname = 'ContactTitle'
		select @Contact = isnull(columnValueText,'') from #tmpEVQueueData where columnname = 'Contact'
		select @LocationTitle = isnull(columnValueString,'') from #tmpEVQueueData where columnname = 'LocationTitle'
		select @Location = isnull(columnValueText,'') from #tmpEVQueueData where columnname = 'Location'
		select @CancellationTitle = isnull(columnValueString,'') from #tmpEVQueueData where columnname = 'CancellationTitle'
		select @Cancellation = isnull(columnValueText,'') from #tmpEVQueueData where columnname = 'Cancellation'
		select @TravelTitle = isnull(columnValueString,'') from #tmpEVQueueData where columnname = 'TravelTitle'
		select @Travel = isnull(columnValueText,'') from #tmpEVQueueData where columnname = 'Travel'
		select @InformationTitle = isnull(columnValueString,'') from #tmpEVQueueData where columnname = 'InformationTitle'
		select @Information = isnull(columnValueText,'') from #tmpEVQueueData where columnname = 'Information'

		--determine if times need to be forced to full day, if allDayEvent
		if (@EventAllDay = 1) BEGIN
			set @EventStart = Convert(DateTime, DATEDIFF(DAY, 0, @EventStart))
			set @EventEnd = dateadd(ms,-3,Convert(DateTime, DATEDIFF(DAY, -1, @EventEnd)))
		END

		EXEC dbo.ev_createEvent @siteID=@siteID, @calendarid=@MCCalendarID, @eventTypeID=1, @enteredByMemberID=@recordedByMemberID, 
			@lockTimeZoneID=null, @isAllDayEvent=@EventAllDay, @altRegistrationURL=null, @status='A', @reportCode=@EventCode, 
			@hiddenFromCalendar=@EventHidden, @emailContactContent=@ContactInclude, @emailLocationContent=@LocationInclude, 
			@emailCancelContent=@CancellationInclude, @emailTravelContent=@TravelInclude, @eventID=@MCEventID OUTPUT
		EXEC dbo.ev_createTime @eventID=@MCEventID, @timeZoneID=@timeZoneID, @startTime=@EventStart, @endTime=@EventEnd

		IF len(@InternalNotes) > 0
			update dbo.ev_events
			set internalNotes = @InternalNotes
			where eventID = @MCEventID

		INSERT INTO dbo.ev_eventCategories (eventID, categoryID)
		VALUES (@MCEventID, @MCCategoryID)

		select @eventContentID = eventContentID FROM dbo.ev_events WHERE eventID=@MCEventID
		EXEC dbo.cms_updateContent @contentID=@eventContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@EventTitle, 
			@contentDesc='', @rawcontent=@EventDescription

		IF LEN(@ContactTitle) > 0 or LEN(@Contact) > 0 BEGIN
			select @contactContentID = contactContentID FROM dbo.ev_events WHERE eventID=@MCEventID
			EXEC dbo.cms_updateContent @contentID=@contactContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@ContactTitle, 
				@contentDesc='', @rawcontent=@Contact
		END

		IF LEN(@LocationTitle) > 0 or LEN(@Location) > 0 BEGIN
			select @locationContentID = locationContentID FROM dbo.ev_events WHERE eventID=@MCEventID
			EXEC dbo.cms_updateContent @contentID=@locationContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@LocationTitle, 
				@contentDesc='', @rawcontent=@Location
		END

		IF LEN(@CancellationTitle) > 0 or LEN(@Cancellation) > 0 BEGIN
			select @cancellationPolicyContentID = cancellationPolicyContentID FROM dbo.ev_events WHERE eventID=@MCEventID
			EXEC dbo.cms_updateContent @contentID=@cancellationPolicyContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@CancellationTitle, 
				@contentDesc='', @rawcontent=@Cancellation
		END

		IF LEN(@TravelTitle) > 0 or LEN(@Travel) > 0 BEGIN
			select @travelContentID = travelContentID FROM dbo.ev_events WHERE eventID=@MCEventID
			EXEC dbo.cms_updateContent @contentID=@travelContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@TravelTitle, 
				@contentDesc='', @rawcontent=@Travel
		END

		IF LEN(@InformationTitle) > 0 or LEN(@Information) > 0 BEGIN
			select @informationContentID = informationContentID FROM dbo.ev_events WHERE eventID=@MCEventID
			EXEC dbo.cms_updateContent @contentID=@informationContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@InformationTitle, 
				@contentDesc='', @rawcontent=@Information
		END
	END 



	-- if we are updating registration with overwrite setting
	IF @existingEvent = 1 and @MCRegistrationID is not null and @ovAction = 'o' BEGIN
		select @RegistrationReplyEmail = columnValueString from #tmpEVQueueData where columnname = 'RegistrationReplyEmail'
		select @DisplayCredits = columnValueBit from #tmpEVQueueData where columnname = 'DisplayCredits'

		update dbo.ev_registration
		set replyToEmail = @RegistrationReplyEmail,
			showCredit = isnull(@DisplayCredits,showCredit)
		where registrationID = @MCRegistrationID
	END

	-- if event does not have registration
	IF @MCRegistrationID is null BEGIN
		select @RegistrationReplyEmail = columnValueString from #tmpEVQueueData where columnname = 'RegistrationReplyEmail'
		select @DisplayCredits = columnValueBit from #tmpEVQueueData where columnname = 'DisplayCredits'

		-- it could have rsvp or alt reg, so we need to switch it
		IF EXISTS(select registrationID from dbo.ev_registration where eventID = @MCEventID and status = 'A')
			UPDATE dbo.ev_registration
			SET status = 'D'
			WHERE eventID = @MCEventID 
			and status = 'A'

		UPDATE dbo.ev_events
		SET altRegistrationURL = NULL
		WHERE eventID = @MCEventID
		AND altRegistrationURL is not null

		EXEC dbo.ev_createRegistration @eventID=@MCEventID, @registrationTypeID=1, @startDate=@EventStart, @endDate=@EventEnd, 
			@registrantCap=null, @replyToEmail=@RegistrationReplyEmail, @notifyEmail='', @isPriceBasedOnActual=1, @bulkCountByRate=0, 
			@registrationID=@MCRegistrationID OUTPUT

		update dbo.ev_registration
		set showCredit = isnull(@DisplayCredits,1)
		where registrationID = @MCRegistrationID

		select @expirationContentID = expirationContentID FROM dbo.ev_registration WHERE registrationID = @MCRegistrationID
		EXEC dbo.cms_updateContent @contentID=@expirationContentID, @languageID=1, @isSSL=0, @isHTML=1, 
			@contentTitle='Expiration Message', @contentDesc='', @rawcontent='Registration for this event has closed.'
	END


	-- handle sub events
	IF @existingEvent = 0 OR (@existingEvent = 1 and @ovAction = 'o') BEGIN
		select @MCParentEventID = columnValueInteger from #tmpEVQueueData where columnname = 'MCParentEventID'
		select @ParentEventCode = nullIf(columnValueString,'') from #tmpEVQueueData where columnname = 'ParentEventCode'

		-- if @MCParentEventID is null but @ParentEventCode is not null, this event should be a child of a recently added event
		IF @MCParentEventID is null and @ParentEventCode is not null BEGIN
			select @MCCalendarID = columnValueInteger from #tmpEVQueueData where columnname = 'MCCalendarID'

			select @MCParentEventID = e.eventID
			from dbo.ev_events as e
			inner join membercentral.dbo.ev_calendarEvents as ce on ce.sourceEventID = e.eventID
				and ce.calendarID = @MCCalendarID
				and ce.calendarID = ce.sourceCalendarID
			where e.reportCode = @ParentEventCode
			and e.status = 'A'
			and e.siteID = @siteID
		END

		-- if @MCParentEventID is not null, this event should be a child of that event	
		IF @MCParentEventID is not null BEGIN
			select @subEventID = subEventID from dbo.ev_subEvents where parentEventID=@MCParentEventID and eventID=@MCEventID
			IF @subEventID is null 
				insert into dbo.ev_subEvents (parentEventID, eventID)
				values (@MCParentEventID, @MCEventID)
		END
	END


	-- credits
	IF @existingEvent = 0 OR (@existingEvent = 1 and @ovAction = 'o') BEGIN
		insert into @tblPossibleCredits (ASID, authorityID, authorityCode)
		select distinct crdAS.ASID, crdA.authorityID, crdA.authorityCode
		from dbo.crd_sponsors as crdS
		inner join dbo.crd_authoritySponsors as crdAS on crdAS.sponsorID = crdS.sponsorID
		inner join dbo.crd_authorities as crdA on crdA.authorityID = crdAS.authorityID
		where crdS.orgID = @orgID

		insert into @tblPossibleCreditCols (ASID, column_name, ASTID)
		select crdAS.ASID, crdAS.authorityCode + '_' + crdAT.typeCode, crdAST.ASTID 
		from @tblPossibleCredits as crdAS
		inner join dbo.crd_authorityTypes as crdAT on crdAT.authorityID = crdAS.authorityID
		inner join dbo.crd_authoritySponsorTypes as crdAST on crdAST.ASID = crdAS.ASID and crdAST.typeID = crdAT.typeID

		select @ASID = min(ASID) from @tblPossibleCredits
		while @ASID is not null begin
			select @crdAuthorityCode=null, @crdApproval=null, @crdStatus=null, @crdOfferedID=null, @crdStatusID=null, @ASTID=null

			select @crdAuthorityCode = authorityCode from @tblPossibleCredits where ASID = @ASID
			select @crdApproval = columnValueString from #tmpEVQueueData where columnname = @crdAuthorityCode + '_approval'
			select @crdStatus = columnValueString from #tmpEVQueueData where columnname = @crdAuthorityCode + '_status'

			IF @crdApproval is not null and @crdStatus is not null BEGIN
				select @crdOfferedID = offeringID from dbo.crd_offerings where ASID = @ASID and eventID = @MCEventID		
				IF @crdOfferedID is null
					EXEC dbo.crd_addOffering @applicationType='Events', @itemID=@MCEventID, @ASID=@ASID, @offeredID=@crdOfferedID OUTPUT

				select @crdStatusID = statusID from dbo.crd_statuses where [status] = @crdStatus

				UPDATE dbo.crd_offerings
				SET statusID = @crdStatusID, approvalNum = @crdApproval
				WHERE offeringID = @crdOfferedID

				select @ASTID = min(ASTID) from @tblPossibleCreditCols where ASID = @ASID
				while @ASTID is not null begin
					select @crdColName=null, @crdValue=null

					select @crdColName = column_name from @tblPossibleCreditCols where ASTID = @ASTID
					select @crdValue = columnValueDecimal2 from #tmpEVQueueData where columnname = @crdColName

					IF @crdValue is not null
						EXEC dbo.crd_updateOfferingType @offeringID=@crdOfferedID, @ASTID=@ASTID, @creditValue=@crdValue

					select @ASTID = min(ASTID) from @tblPossibleCreditCols where ASID = @ASID and ASTID > @ASTID
				end
			END

			select @ASID = min(ASID) from @tblPossibleCredits where ASID > @ASID
		end

	END


	IF @TranCounter = 0
		COMMIT TRAN;
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH



-- update status
BEGIN TRY
	EXEC platformQueue.dbo.queue_setStatus @queueType='importEvents', @itemUID=@itemUID, @queueStatus='readyToNotify'

	IF OBJECT_ID('tempdb..#tmpEVQueueData') IS NOT NULL 
		DROP TABLE #tmpEVQueueData		
END TRY
BEGIN CATCH
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[ev_importEvents_validate]
@siteid int, 
@importResult xml OUTPUT

AS

SET NOCOUNT ON

declare @ASID int, @crdAuthorityCode varchar(20), @creditColName varchar(50), @dynSQL nvarchar(max), @queueTypeID int
select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'importEvents'
set @importResult = null


-- ***********
-- clean table 
-- ***********
BEGIN TRY
	-- delete empty rows
	delete from #mc_EvImport where Calendar is null and EventTitle is null;
END TRY
BEGIN CATCH
	INSERT INTO #tblEvErrors (msg)
	VALUES ('Unable to clean import table.')

	INSERT INTO #tblEvErrors (msg)
	VALUES (left(error_message(),300))

	GOTO on_done
END CATCH


-- ****************
-- required columns 
-- ****************
BEGIN TRY
	-- match on calendar
	update tmp 
	set tmp.MCCalendarID = c.calendarID 
	from #mc_EvImport as tmp 
	inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceName = isnull(tmp.calendar,'') and ai.siteID = @siteID
	inner join dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID and sr.siteResourceStatusID = 1 
	inner join dbo.ev_calendars as c on c.applicationInstanceID = ai.applicationInstanceID

	-- check for missing calendars
	BEGIN TRY
		ALTER TABLE #mc_EvImport ALTER COLUMN MCCalendarID int not null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' does not match an existing calendar.'
		FROM #mc_EvImport
		WHERE MCCalendarID IS NULL
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done
	END CATCH

	-- no blank eventTitles
	update #mc_EvImport set eventTitle = '' where eventTitle is null;

	INSERT INTO #tblEvErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing EventTitle.'
	FROM #mc_EvImport
	WHERE eventTitle = ''
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- eventTitles must be at or under 200 chars
	INSERT INTO #tblEvErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an invalid EventTitle. EventTitles must be 200 characters or less.'
	FROM #mc_EvImport
	WHERE len(EventTitle) > 200 
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- no blank eventCodes
	update #mc_EvImport set eventCode = '' where eventCode is null;

	INSERT INTO #tblEvErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing EventCode.'
	FROM #mc_EvImport
	WHERE eventCode = ''
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- eventcode must be at or under 15 chars
	INSERT INTO #tblEvErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an invalid EventCode. EventCodes must be 15 characters or less.'
	FROM #mc_EvImport
	WHERE len(eventCode) > 15 
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- eventcode must be unique in file
	BEGIN TRY
		ALTER TABLE #mc_EvImport ALTER COLUMN eventCode varchar(15) not null;
		ALTER TABLE #mc_EvImport ADD PRIMARY KEY (eventCode);
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'EventCode ' + eventCode + ' appears in the file multiple times. EventCodes must be unique.'
		FROM #mc_EvImport
		GROUP BY eventCode
		HAVING count(*) > 1
		ORDER BY eventCode
			IF @@ROWCOUNT > 0 GOTO on_done
	END CATCH

	-- match on eventCode
	update tmp 
	set tmp.MCEventID = e.eventID
	from #mc_EvImport as tmp 
	inner join dbo.ev_events as e on e.reportCode = tmp.eventCode
		and e.status = 'A'
		and e.siteID = @siteID
	inner join dbo.ev_calendarEvents as ce on ce.sourceEventID = e.eventID
		and ce.calendarID = tmp.MCCalendarID
		and ce.calendarID = ce.sourceCalendarID

	-- lookup registration
	update tmp 
	set tmp.MCRegistrationID = r.registrationID
	from #mc_EvImport as tmp 
	inner join dbo.ev_registration as r on r.eventID = tmp.MCEventID 
		and r.status = 'A'
		and r.registrationTypeID = 1
	where tmp.MCEventID is not null

	-- no blank EventCategory
	update #mc_EvImport set EventCategory = '' where EventCategory is null;

	INSERT INTO #tblEvErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing EventCategory.'
	FROM #mc_EvImport
	WHERE EventCategory = ''
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- match on EventCategory
	update tmp 
	set tmp.MCCategoryID = cat.categoryID  
	from #mc_EvImport as tmp 
	inner join dbo.ev_calendars as c on c.calendarID = tmp.MCCalendarID
	inner join dbo.ev_categories as cat on cat.calendarID = c.calendarID and cat.category = tmp.EventCategory

	-- check for missing EventCategory
	BEGIN TRY
		ALTER TABLE #mc_EvImport ALTER COLUMN MCCategoryID int not null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' does not match an existing category for that calendar.'
		FROM #mc_EvImport
		WHERE MCCategoryID IS NULL
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done
	END CATCH

	-- ensure EventStart is datetime (allow nulls for this check)
	BEGIN TRY
		ALTER TABLE #mc_EvImport ALTER COLUMN EventStart datetime null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('The column EventStart contains invalid dates.')
			GOTO on_done
	END CATCH

	-- check for null EventStart
	BEGIN TRY
		ALTER TABLE #mc_EvImport ALTER COLUMN EventStart datetime not null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing the required EventStart.'
		FROM #mc_EvImport
		WHERE EventStart IS NULL
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done
	END CATCH

	-- ensure EventEnd is datetime (allow nulls for this check)
	BEGIN TRY
		ALTER TABLE #mc_EvImport ALTER COLUMN EventEnd datetime null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('The column EventEnd contains invalid dates.')
			GOTO on_done
	END CATCH

	-- check for null EventEnd
	BEGIN TRY
		ALTER TABLE #mc_EvImport ALTER COLUMN EventEnd datetime not null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing the required EventEnd.'
		FROM #mc_EvImport
		WHERE EventEnd IS NULL
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done
	END CATCH

	-- check dates
	INSERT INTO #tblEvErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a EventStart after the EventEnd.'
	FROM #mc_EvImport
	WHERE EventStart > EventEnd
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

	-- no blank RegistrationReplyEmail
	update #mc_EvImport set RegistrationReplyEmail = '' where RegistrationReplyEmail is null;

	INSERT INTO #tblEvErrors (msg)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing RegistrationReplyEmail.'
	FROM #mc_EvImport
	WHERE RegistrationReplyEmail = ''
	ORDER BY rowID
		IF @@ROWCOUNT > 0 GOTO on_done

END TRY
BEGIN CATCH
	INSERT INTO #tblEvErrors (msg)
	VALUES ('Unable to validate data in required columns.')

	INSERT INTO #tblEvErrors (msg)
	VALUES (left(error_message(),300))

	GOTO on_done
END CATCH


-- ****************
-- optional columns 
-- ****************
BEGIN TRY
	-- bit columns provided in the original upload need a value for each row
	IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'EventHidden') BEGIN
		UPDATE #mc_EvImport set EventHidden = '' where EventHidden is null;
		UPDATE #mc_EvImport set EventHidden = '1' where EventHidden in ('Yes','Y','TRUE');
		UPDATE #mc_EvImport set EventHidden = '0' where EventHidden in ('No','N','FALSE');
		
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the EventHidden column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
		FROM #mc_EvImport
		WHERE EventHidden = ''
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done

		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN EventHidden bit NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			VALUES ('There are invalid values in the EventHidden column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.')
				GOTO on_done
		END CATCH
	END

	IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'EventAllDay') BEGIN
		UPDATE #mc_EvImport set EventAllDay = '' where EventAllDay is null;
		UPDATE #mc_EvImport set EventAllDay = '1' where EventAllDay in ('Yes','Y','TRUE');
		UPDATE #mc_EvImport set EventAllDay = '0' where EventAllDay in ('No','N','FALSE');
		UPDATE #mc_EvImport set EventAllDay = '1' where EventStart = EventEnd;

		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the EventAllDay column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
		FROM #mc_EvImport
		WHERE EventAllDay = ''
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done

		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN EventAllDay bit NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			VALUES ('There are invalid values in the EventAllDay column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.')
				GOTO on_done
		END CATCH
	END

	IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'DisplayCredits') BEGIN
		UPDATE #mc_EvImport set DisplayCredits = '' where DisplayCredits is null;
		UPDATE #mc_EvImport set DisplayCredits = '1' where DisplayCredits in ('Yes','Y','TRUE');
		UPDATE #mc_EvImport set DisplayCredits = '0' where DisplayCredits in ('No','N','FALSE');

		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the DisplayCredits column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
		FROM #mc_EvImport
		WHERE DisplayCredits = ''
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done

		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN DisplayCredits bit NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			VALUES ('There are invalid values in the DisplayCredits column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.')
				GOTO on_done
		END CATCH
	END

	IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'ContactInclude') BEGIN
		UPDATE #mc_EvImport set ContactInclude = '' where ContactInclude is null;
		UPDATE #mc_EvImport set ContactInclude = '1' where ContactInclude in ('Yes','Y','TRUE');
		UPDATE #mc_EvImport set ContactInclude = '0' where ContactInclude in ('No','N','FALSE');

		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the ContactInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
		FROM #mc_EvImport
		WHERE ContactInclude = ''
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done

		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN ContactInclude bit NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			VALUES ('There are invalid values in the ContactInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.')
				GOTO on_done
		END CATCH
	END

	IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'LocationInclude') BEGIN
		UPDATE #mc_EvImport set LocationInclude = '' where LocationInclude is null;
		UPDATE #mc_EvImport set LocationInclude = '1' where LocationInclude in ('Yes','Y','TRUE');
		UPDATE #mc_EvImport set LocationInclude = '0' where LocationInclude in ('No','N','FALSE');

		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the LocationInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
		FROM #mc_EvImport
		WHERE LocationInclude = ''
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done

		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN LocationInclude bit NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			VALUES ('There are invalid values in the LocationInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.')
				GOTO on_done
		END CATCH
	END

	IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'CancellationInclude') BEGIN
		UPDATE #mc_EvImport set CancellationInclude = '' where CancellationInclude is null;
		UPDATE #mc_EvImport set CancellationInclude = '1' where CancellationInclude in ('Yes','Y','TRUE');
		UPDATE #mc_EvImport set CancellationInclude = '0' where CancellationInclude in ('No','N','FALSE');

		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the CancellationInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
		FROM #mc_EvImport
		WHERE CancellationInclude = ''
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done

		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN CancellationInclude bit NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			VALUES ('There are invalid values in the CancellationInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.')
				GOTO on_done
		END CATCH
	END

	IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'TravelInclude') BEGIN
		UPDATE #mc_EvImport set TravelInclude = '' where TravelInclude is null;
		UPDATE #mc_EvImport set TravelInclude = '1' where TravelInclude in ('Yes','Y','TRUE');
		UPDATE #mc_EvImport set TravelInclude = '0' where TravelInclude in ('No','N','FALSE');

		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the TravelInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
		FROM #mc_EvImport
		WHERE TravelInclude = ''
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done

		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN TravelInclude bit NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			VALUES ('There are invalid values in the TravelInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.')
				GOTO on_done
		END CATCH
	END

	IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'ParentEventCode') BEGIN
		-- parenteventcode must be at or under 15 chars
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an invalid ParentEventCode. ParentEventCode must be 15 characters or less.'
		FROM #mc_EvImport
		WHERE len(isnull(ParentEventCode,'')) > 15 
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done

		-- ParentEventCode cannot be itself
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an invalid ParentEventCode. An event cannot be a child event of itself.'
		FROM #mc_EvImport
		WHERE len(isnull(ParentEventCode,'')) > 0
		and ParentEventCode = EventCode
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done

		-- match on ParentEventCode for events created before
		update tmp 
		set tmp.MCParentEventID = e.eventID
		from #mc_EvImport as tmp 
		inner join dbo.ev_events as e on e.reportCode = tmp.ParentEventCode
			and e.status = 'A'
			and e.siteID = @siteID
		inner join dbo.ev_calendarEvents as ce on ce.sourceEventID = e.eventID
			and ce.calendarID = tmp.MCCalendarID
			and ce.calendarID = ce.sourceCalendarID
		where len(isnull(tmp.ParentEventCode,'')) > 0

		-- match on ParentEventCode for events in this file
		update tmp 
		set tmp.MCParentRowID = tmp2.rowID
		from #mc_EvImport as tmp 
		inner join #mc_EvImport as tmp2 on tmp2.eventCode = tmp.ParentEventCode and tmp.MCCalendarID = tmp2.MCCalendarID
		where len(isnull(tmp.ParentEventCode,'')) > 0

		-- ParentEventCode cannot be bad
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an invalid ParentEventCode. No matching EventCode in the file or Control Panel.'
		FROM #mc_EvImport
		WHERE len(isnull(ParentEventCode,'')) > 0
		and MCParentEventID is null
		and MCParentRowID is null
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done

		-- events with a parent event code must appear in the file after the parent event
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is a sub-event but appears after the master event in the file. Ensure all master events are listed before sub-events.'
		FROM #mc_EvImport
		WHERE MCParentRowID is not null
		and rowID < MCParentRowID
		ORDER BY rowID
			IF @@ROWCOUNT > 0 GOTO on_done

		-- sub events cannot be parent events
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(tmp.rowID as varchar(10)) + ' is a sub-event but also appears as a parent event. Sub-events cannot contain sub-events.'
		FROM #mc_EvImport as tmp
		inner join #mc_EvImport as tmp2 on tmp2.ParentEventCode = tmp.EventCode
		WHERE (tmp.MCParentRowID is not null OR tmp.MCParentEventID is not null)
		ORDER BY tmp.rowID
			IF @@ROWCOUNT > 0 GOTO on_done
	END

END TRY
BEGIN CATCH
	INSERT INTO #tblEvErrors (msg)
	VALUES ('Unable to validate data in optional columns.')

	INSERT INTO #tblEvErrors (msg)
	VALUES (left(error_message(),300))

	GOTO on_done
END CATCH


-- **************
-- credit columns
-- **************
BEGIN TRY

	select @ASID = min(ASID) from #tblPossibleCredits
	while @ASID is not null begin
		select @crdAuthorityCode=null, @creditColName = null

		select @crdAuthorityCode = authorityCode from #tblPossibleCredits where ASID = @ASID

		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = @crdAuthorityCode + '_approval') BEGIN
			select @dynSQL = 'UPDATE #mc_EvImport set [' + @crdAuthorityCode + '_approval] = '''' where [' + @crdAuthorityCode + '_approval] is null;'
			EXEC(@dynSQL)

			select @dynSQL = '
				SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' has an invalid value for ' + @crdAuthorityCode + '_approval. Approvals must be 50 characters or less.''
				FROM #mc_EvImport  
				WHERE len(isnull([' + @crdAuthorityCode + '_approval],'''')) > 50 
				ORDER BY rowID'
			INSERT INTO #tblEvErrors (msg)
			EXEC(@dynSQL)
		END

		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = @crdAuthorityCode + '_status') BEGIN
			select @dynSQL = '
				SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' has an invalid value for ' + @crdAuthorityCode + '_status. Status must be Approved, Denied, Not Submitted, or Pending.''
				FROM #mc_EvImport  
				WHERE [' + @crdAuthorityCode + '_status] not in ('''',''Approved'',''Denied'',''Not Submitted'',''Pending'') 
				ORDER BY rowID'
			INSERT INTO #tblEvErrors (msg)
			EXEC(@dynSQL)
		END

		select @creditColName = min(column_name) from #tblPossibleCreditCols where ASID = @ASID
		while @creditColName is not null begin
			IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = @creditColName) BEGIN
				set @dynSQL = '
					BEGIN TRY
						UPDATE #mc_EvImport set [' + @creditColName + '] = null where [' + @creditColName + '] is not null and [' + @creditColName + '] = '''';
						ALTER TABLE #mc_EvImport ALTER COLUMN [' + @creditColName + '] decimal(6,2) NULL;
					END TRY
					BEGIN CATCH
						INSERT INTO #tblEvErrors (msg)
						VALUES (''There are invalid values in the ' + @creditColName + ' column.'')
					END CATCH'
				EXEC(@dynSQL)
			END

			select @creditColName = min(column_name) from #tblPossibleCreditCols where ASID = @ASID and column_name > @creditColName
		end

		select @ASID = min(ASID) from #tblPossibleCredits where ASID > @ASID
	end

END TRY
BEGIN CATCH
	INSERT INTO #tblEvErrors (msg)
	VALUES ('Unable to validate data in credit columns.')

	INSERT INTO #tblEvErrors (msg)
	VALUES (left(error_message(),300))

	GOTO on_done
END CATCH

-- ensure credit columns are included in the queue tables
BEGIN TRY
	insert into platformQueue.dbo.tblQueueTypeDataColumns (queueTypeID, columnName, dataTypeID) 
	select @queueTypeID, tmp.column_name, tmp.dataTypeID
	from (
		select authorityCode + '_approval' as column_name, 1 as dataTypeID
		from #tblPossibleCredits
			union all
		select authorityCode + '_status' as column_name, 1 as dataTypeID
		from #tblPossibleCredits
			union all	
		select column_name, 2
		from #tblPossibleCreditCols
	) as tmp
		except 
	select queueTypeID, columnName, dataTypeID
	from platformQueue.dbo.tblQueueTypeDataColumns
	where queueTypeID = @queueTypeID
END TRY
BEGIN CATCH
	INSERT INTO #tblEvErrors (msg)
	VALUES ('Unable to add credit columns to the queue tables.')

	INSERT INTO #tblEvErrors (msg)
	VALUES (left(error_message(),300))

	GOTO on_done
END CATCH


-- *************************
-- prepare table for unpivot
-- *************************
IF NOT EXISTS (select top 1 * from #tblEvErrors) BEGIN
	BEGIN TRY
		ALTER TABLE #mc_EvImport ALTER COLUMN EventCode varchar(200) NOT NULL;
		ALTER TABLE #mc_EvImport ALTER COLUMN ParentEventCode varchar(200) NULL;
		ALTER TABLE #mc_EvImport ALTER COLUMN EventTitle varchar(200) NOT NULL;
		ALTER TABLE #mc_EvImport ALTER COLUMN RegistrationReplyEmail varchar(200) NOT NULL;
		ALTER TABLE #mc_EvImport ALTER COLUMN ContactTitle varchar(200) NULL;
		ALTER TABLE #mc_EvImport ALTER COLUMN LocationTitle varchar(200) NULL;
		ALTER TABLE #mc_EvImport ALTER COLUMN CancellationTitle varchar(200) NULL;
		ALTER TABLE #mc_EvImport ALTER COLUMN TravelTitle varchar(200) NULL;
		ALTER TABLE #mc_EvImport ALTER COLUMN InformationTitle varchar(200) NULL;

		select @ASID = null, @crdAuthorityCode=null

		select @ASID = min(ASID) from #tblPossibleCredits
		while @ASID is not null begin
			select @crdAuthorityCode = authorityCode from #tblPossibleCredits where ASID = @ASID

			select @dynSQL = 'ALTER TABLE #mc_EvImport ALTER COLUMN [' + @crdAuthorityCode + '_approval] varchar(50) NULL;'
			EXEC(@dynSQL)
			select @dynSQL = 'ALTER TABLE #mc_EvImport ALTER COLUMN [' + @crdAuthorityCode + '_status] varchar(50) NULL;'
			EXEC(@dynSQL)

			select @ASID = min(ASID) from #tblPossibleCredits where ASID > @ASID
		end
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to prepare data for unpivot.')

		INSERT INTO #tblEvErrors (msg)
		VALUES (left(error_message(),300))

		GOTO on_done
	END CATCH
END


-- ************************
-- generate result xml file 
-- ************************
on_done:
	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblEvErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE)
	
	IF OBJECT_ID('tempdb..#tblEventCreditCols') IS NOT NULL 
		DROP TABLE #tblEventCreditCols

RETURN 0
GO

