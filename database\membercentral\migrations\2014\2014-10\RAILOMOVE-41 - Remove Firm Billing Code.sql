use membercentral
GO

declare @navigationID int, @functionID int, @resourceTypeFunctionID int
select @navigationID = navigationID from admin_navigation where navName = 'Firm Billing' and cfcMethod = 'listFirmReports'
select @functionID = functionID from cms_siteResourceFunctions where functionName = 'viewFirmReports'
select @resourceTypeFunctionID = resourceTypeFunctionID from cms_siteResourceTypeFunctions where functionID = @functionID

delete from admin_functionsDeterminingNav where navigationID = @navigationID
EXEC dbo.cms_deleteSiteResourceRoleFunction 9, @resourceTypeFunctionID
EXEC dbo.cms_deleteSiteResourceRoleFunction 10, @resourceTypeFunctionID
delete from cms_siteResourceTypeFunctions where functionID = @functionID
delete from admin_navigation where navigationID = @navigationID
GO

DROP PROC dbo.sub_firmBillingData
GO
DROP PROC dbo.sub_firmBillingList
GO




