use membercentral
GO

ALTER PROC [dbo].[ams_importMemberData_tempToHolding]
@orgid int, 
@tmptbl varchar(30),
@pathToExport varchar(100),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Starting importMemberData_toHolding'

DECLARE @prefix varchar(50), @exportcmd varchar(400), @dynSQL nvarchar(max), @flatfile varchar(160),
	@columnID int, @mincol varchar(255), @websiteType varchar(20), @emailType varchar(20), 
	@addressType varchar(20), @good bit, @importFileCol varchar(400), @defaultValueID int, 
	@colDataTypeCode varchar(20), @plType varchar(200), @allownull bit, @OrgColDataLength int,
	@ImpColDataLength int
declare @tblMissingCols TABLE (colName varchar(255))
declare @tblErrors TABLE (rowid int IDENTITY(1,1), msg varchar(600), fatal bit)
declare @tblDataChanged TABLE (rowid int IDENTITY(1,1), msg varchar(600))
declare @tblCounts TABLE (rowid int IDENTITY(1,1), countName varchar(50), countNum int)
declare @tblMemList1 TABLE (memberid int, memberNumber varchar(50), firstname varchar(75), lastname varchar(75))
declare @tblMemList2 TABLE (memberid int, memberNumber varchar(50), firstname varchar(75), lastname varchar(75))
declare @tblMemList3 TABLE (rowid int, memberNumber varchar(50), firstname varchar(75), lastname varchar(75))

DECLARE @orgCode varchar(10), @hasPrefix bit, @usePrefixList bit, @hasMiddleName bit, @hasSuffix bit, @hasProfessionalSuffix bit, @hasCompany bit
select @orgcode=orgcode, @hasPrefix=hasPrefix, @usePrefixList=usePrefixList, @hasMiddleName=hasMiddleName,
	@hasSuffix=hasSuffix, @hasProfessionalSuffix=hasProfessionalSuffix, @hasCompany=hasCompany
	from dbo.organizations 
	where orgID = @orgid

declare @var_tmpCols varchar(20), @var_tmpColsSkipped varchar(30)
select @var_tmpCols = '##tmpCols' + @orgcode
select @var_tmpColsSkipped = '##tmpColsSkipped' + @orgcode

-- cleanup
IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
	EXEC('DROP TABLE ' + @var_tmpCols)
IF OBJECT_ID('tempdb..#tblOrgCols') IS NOT NULL 
	DROP TABLE #tblOrgCols
IF OBJECT_ID('tempdb..#tblImportCols') IS NOT NULL 
	DROP TABLE #tblImportCols
IF OBJECT_ID('tempdb..' + @var_tmpColsSkipped) IS NOT NULL 
	EXEC('DROP TABLE ' + @var_tmpColsSkipped)


-- ******************************** 
-- ensure all columns exist (fatal)
-- ******************************** 
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking Required Columns'

-- this will get the columns that should be there based on current setup
CREATE TABLE #tblOrgCols (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
	COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
	SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
	COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
	CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)

	-- Need the temp table code for the export. replace the first FROM occurence 
	select @dynSQL = ''
	EXEC dbo.ams_getFlattenedMemberDataSQL @orgID=@orgID, @limitTop=1, @limitActive=1, @limitMID='', @showMCFields=0, @showSkippedFields=0, @sql=@dynSQL OUTPUT
	select @dynSQL = stuff(@dynSQL, charIndex('from membercentral',@dynSQL), len('from membercentral'), 'into ' + @var_tmpCols + ' from membercentral')
	EXEC(@dynSQL)

	-- get cols
	INSERT INTO #tblOrgCols
	EXEC tempdb.dbo.SP_COLUMNS @var_tmpCols
	
	-- cleanup table no longer needed
	IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
		EXEC('DROP TABLE ' + @var_tmpCols)

-- this will get the columns that are actually in the import
CREATE TABLE #tblImportCols (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
	COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
	SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
	COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
	CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)

	-- get cols
	INSERT INTO #tblImportCols
	EXEC tempdb.dbo.SP_COLUMNS @tmptbl

INSERT INTO @tblErrors (msg, fatal)
select 'The column ' + org.column_name + ' is missing from your data.', 1
from #tblOrgCols as org
left outer join #tblImportCols as imp on imp.column_name = org.column_name
where imp.table_name is null

insert into @tblMissingCols(colname)
select org.column_name
from #tblOrgCols as org
left outer join #tblImportCols as imp on imp.column_name = org.column_name
where imp.table_name is null

-- dont drop these temp tables here because we use them for data length checks below

-- ********************************
-- data type checks (fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking DATE columns'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and dt.dataTypeCode = 'DATE'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'datetime')
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			UPDATE ' + @tmptbl + ' set ' + quotename(@mincol) + ' = null where ' + quotename(@mincol) + ' = '''';
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' datetime null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid dates.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and dt.dataTypeCode = 'DATE'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'datetime')
		where mdc.columnname > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking DECIMAL2 columns'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 0
		and dt.dataTypeCode = 'DECIMAL2'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'decimal')
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			UPDATE ' + @tmptbl + ' SET ' + quotename(@mincol) + ' = replace(' + quotename(@mincol) + ','','','''');
			UPDATE ' + @tmptbl + ' SET ' + quotename(@mincol) + ' = null where ' + quotename(@mincol) + ' = '''';
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' decimal(9,2) null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid decimal values.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and mdc.allowMultiple = 0
			and dt.dataTypeCode = 'DECIMAL2'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'decimal')
		where mdc.columnname > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking DECIMAL2 columns (multiple values)'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 1
		and dt.dataTypeCode = 'DECIMAL2'
		and mdc.columnname not in (select colName from @tblMissingCols)
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' varchar(max) null;

			IF EXISTS (
				select top 1 tbl.listItem
				from ' + @tmptbl + ' 
				cross apply dbo.fn_decimal2ListToTable(' + quotename(@mincol) + ',''|'') as tbl
				where nullif(' + quotename(@mincol) + ','''') is not null
			) set @good = @good
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid decimal values.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and mdc.allowMultiple = 1
			and dt.dataTypeCode = 'DECIMAL2'
			and mdc.columnname not in (select colName from @tblMissingCols)
		where mdc.columnname > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking INTEGER columns'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 0
		and dt.dataTypeCode = 'INTEGER'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and mdc.columnname not in (select column_Name from #tblImportCols where left(type_name,3) = 'int')
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			UPDATE ' + @tmptbl + ' SET ' + quotename(@mincol) + ' = replace(' + quotename(@mincol) + ','','','''');
			UPDATE ' + @tmptbl + ' SET ' + quotename(@mincol) + ' = null where ' + quotename(@mincol) + ' = '''';
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' int null
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid whole number values.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and mdc.allowMultiple = 0
			and dt.dataTypeCode = 'INTEGER'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnname not in (select column_Name from #tblImportCols where left(type_name,3) = 'int')
		where mdc.columnname > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking INTEGER columns (multiple values)'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 1
		and dt.dataTypeCode = 'INTEGER'
		and mdc.columnname not in (select colName from @tblMissingCols)
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' varchar(max) null;

			IF EXISTS (			
				select top 1 tbl.listItem
				from ' + @tmptbl + ' 
				cross apply dbo.fn_intListToTable(' + quotename(@mincol) + ',''|'') as tbl
				where nullif(' + quotename(@mincol) + ','''') is not null
			) set @good = @good
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid whole number values.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and mdc.allowMultiple = 1
			and dt.dataTypeCode = 'INTEGER'
			and mdc.columnname not in (select colName from @tblMissingCols)
		where mdc.columnname > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking BIT columns'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and dt.dataTypeCode = 'BIT'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'bit')
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			UPDATE ' + @tmptbl + ' SET ' + quotename(@mincol) + ' = 1 where ' + quotename(@mincol) + ' = ''TRUE'' OR ' + quotename(@mincol) + ' = ''YES'';
			UPDATE ' + @tmptbl + ' SET ' + quotename(@mincol) + ' = 0 where ' + quotename(@mincol) + ' = ''FALSE'' OR ' + quotename(@mincol) + ' = ''NO'';
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' bit null
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid boolean values.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and dt.dataTypeCode = 'BIT'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'bit')
		where mdc.columnname > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking XML columns'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and dt.dataTypeCode = 'XML'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'xml')
while @mincol is not null BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' xml null
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains invalid XML values.', 1)

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and dt.dataTypeCode = 'XML'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnname not in (select column_Name from #tblImportCols where type_name = 'xml')
		where mdc.columnname > @mincol
END

-- *********************************************************************
-- ensure all varchar columns match data length requirements (non-fatal)
-- *********************************************************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking VARCHAR columns for lengths'
select @mincol = null
select @mincol = min(column_name)
	from #tblOrgCols
	where type_Name = 'varchar'
	and [length] > 0
	and column_name not in (select colName from @tblMissingCols)
	and column_name in (select column_Name from #tblImportCols where type_Name = 'varchar' and [length] > 0)
while @mincol is not null BEGIN
	select @OrgColDataLength = null, @ImpColDataLength = null
	select @OrgColDataLength = [length] from #tblOrgCols where column_name = @mincol
	select @ImpColDataLength = [length] from #tblImportCols where column_name = @mincol

	-- if length of orgcol < length of importcol, report on data truncation
	IF @OrgColDataLength < @ImpColDataLength BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '') has invalid data for ' + quoteName(@mincol) + '. The maximum number of characters for this column is ' + cast(@OrgColDataLength as varchar(5)) + '.'' as msg, 0 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE len(' + quotename(@mincol) + ') > ' + cast(@OrgColDataLength as varchar(5)) + '
			ORDER BY rowID'
		INSERT INTO @tblErrors (msg, fatal)
		EXEC(@dynSQL)

		IF @@ROWCOUNT > 0 BEGIN
			select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '') will truncate ' + quoteName(@mincol) + ' to fit.'' as msg
				FROM ' + @tmptbl + ' 
				WHERE len(' + quotename(@mincol) + ') > ' + cast(@OrgColDataLength as varchar(5)) + '
				ORDER BY rowID'
			INSERT INTO @tblDataChanged (msg)
			EXEC(@dynSQL)

			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@mincol) + ' = left(' + quotename(@mincol) + ',' + cast(@OrgColDataLength as varchar(5)) + ')
				WHERE len(' + quotename(@mincol) + ') > ' + cast(@OrgColDataLength as varchar(5)) + ' '
			EXEC(@dynSQL)
		END
	END

	-- if length of orgcol <> length of importcol, alter table so it matches.
	IF @OrgColDataLength <> @ImpColDataLength BEGIN
		set @good = 1
		set @dynSQL = '
			set @good = 1
			BEGIN TRY
				ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@mincol) + ' varchar(' + cast(@OrgColDataLength as varchar(5)) + ') null;
			END TRY
			BEGIN CATCH
				set @good = 0
			END CATCH'
			exec sp_executesql @dynSQL, N'@good bit output', @good output
		IF @good = 0
			INSERT INTO @tblErrors (msg, fatal)
			VALUES ('The column ' + @mincol + ' could not be expanded to support ' + cast(@OrgColDataLength as varchar(5)) + ' characters.', 1)
	END

	select @mincol = min(column_name)
		from #tblOrgCols
		where type_Name = 'varchar'
		and [length] > 0
		and column_name not in (select colName from @tblMissingCols)
		and column_name in (select column_Name from #tblImportCols where type_Name = 'varchar' and [length] > 0)
		and column_name > @mincol
END


-- ********************************
-- no member number (fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying missing member numbers'

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'memberNumber') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '') is missing a Member Number. Member Numbers are required for all members.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE (memberNumber IS NULL OR ltrim(rtrim(memberNumber)) = '''')
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

-- ********************************
-- dupe member numbers (fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying duplicate member numbers'

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'memberNumber') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''The Member Number '' + isnull(memberNumber,'''') + '' appears '' + cast(count(*) as varchar(10)) + '' times. Member Numbers must be unique.'' as msg, 1 as fatal
			FROM ' + @tmptbl + ' 
			GROUP BY memberNumber
			HAVING COUNT(*) > 1
			ORDER BY 1'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

-- ********************************
-- conflicting member numbers with guest accounts (fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying conflicting member numbers'

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'memberNumber') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''The Member Number '' + isnull(tbl.memberNumber,'''') + '' belongs to an existing guest account.'' as msg, 1 as fatal
			FROM ' + @tmptbl + ' as tbl 
			WHERE isnull(tbl.memberNumber,'''')	<> ''''
			AND EXISTS (
				select memberID
				from dbo.ams_members
				where status <> ''D''
				and orgID = ' + cast(@orgID as varchar(6)) + ' 
				and memberTypeID = 1
				and membernumber = tbl.memberNumber
			)
			ORDER BY 1'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

-- ********************************
-- no first name (fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying missing first names'

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'firstname') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') is missing a First Name. First Names are required for all members.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE (firstname IS NULL OR ltrim(rtrim(firstname)) = '''')
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

-- ********************************
-- no last name (fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying missing last names'

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'lastname') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') is missing a Last Name. Last Names are required for all members.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE (lastname IS NULL OR ltrim(rtrim(lastname)) = '''')
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

-- ********************************
-- bad website data (non-fatal)
-- be nice and clean up their website data first (ensure http:// in front)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying invalid website data'

declare @tldList varchar(max)
set @tldList = 'com|edu|gov|int|mil|net|org|arpa|coop|asia|cat|academy|accountants|active|actor|aero|agency|airforce|archi|army|associates|attorney|auction|audio|autos|band|bargains|beer|best|bid|bike|bio|biz|black|blackfriday|blue|boo|boutique|build|builders|business|buzz|cab|camera|camp|cancerresearch|capital|cards|care|career|careers|cash|catering|center|ceo|channel|cheap|christmas|church|city|claims|cleaning|click|clinic|clothing|club|coach|codes|coffee|college|community|company|computer|condos|construction|consulting|contractors|cooking|cool|country|credit|creditcard|cricket|cruises|dad|dance|dating|day|deals|degree|delivery|democrat|dental|dentist|diamonds|diet|digital|direct|directory|discount|domains|eat|education|email|energy|engineer|engineering|equipment|esq|estate|events|exchange|expert|exposed|fail|farm|feedback|finance|financial|fish|fishing|fitness|flights|florist|fly|foo|forsale|foundation|fund|furniture|futbol|gallery|gift|gifts|gives|glass|global|gop|graphics|green|gripe|guide|guitars|guru|healthcare|help|here|hiphop|hiv|holdings|holiday|homes|horse|host|hosting|house|how|info|ing|ink|insure|international|investments|jobs|kim|kitchen|land|lawyer|lease|legal|lgbt|life|lighting|limited|limo|link|loans|lotto|luxe|luxury|management|market|marketing|media|meet|meme|memorial|menu|mobi|moe|money|mortgage|motorcycles|mov|museum|name|navy|network|new|ngo|ninja|ong|onl|ooo|organic|partners|parts|party|pharmacy|photo|photography|photos|physio|pics|pictures|pink|pizza|place|plumbing|poker|post|press|pro|productions|prof|properties|property|qpon|recipes|red|rehab|ren|rentals|repair|report|republican|reviews|rich|rip|rocks|rodeo|rsvp|science|services|sexy|shoes|singles|social|software|solar|solutions|space|supplies|supply|support|surf|surgery|systems|tattoo|tax|technology|tel|tips|tires|today|tools|top|town|toys|trade|training|travel|university|vacations|vet|villas|vision|vodka|vote|voting|voyage|wang|watch|webcam|website|wed|wiki|works|world|wtf|xxx|xyz|zone'

select @websiteType = min(websiteType)
	FROM dbo.ams_memberWebsiteTypes
	WHERE orgID = @orgID
	and websiteType NOT IN (select colName from @tblMissingCols)
while @websiteType is not null BEGIN

	select @dynSQL = 'update ' + @tmptbl + ' 
		set ' + quotename(@websiteType) + ' = ''http://'' + ' + quotename(@websiteType) + ' 
		where len(' + quotename(@websiteType) + ') > 0 and left(' + quotename(@websiteType) + ',4) <> ''http'''
	EXEC(@dynSQL)

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the website ' + @websiteType + ': '' + ' + quoteName(@websiteType) + ' as msg, 0 as fatal
		FROM ' + @tmptbl + ' 
		WHERE len(' + quotename(@websiteType) + ') > 0 and dbo.fn_RegExReplace(' + quotename(@websiteType) + ',''^(http|https|ftp)\://([a-zA-Z0-9\.\-]+(\:[a-zA-Z0-9\.&amp;%\$\-]+)*@)*((25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9])\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[0-9])|localhost|([a-zA-Z0-9\-]+\.)*[a-zA-Z0-9\-]+\.(' + @tldList + '|[a-zA-Z]{2}))(\:[0-9]+)*(/($|[a-zA-Z0-9\.\,\?\''''\\\+&amp;%\!$#\=~_\-]+))*$'','''') <> '''' 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the website ' + @websiteType + '.'' as msg
			FROM ' + @tmptbl + ' 
			WHERE len(' + quotename(@websiteType) + ') > 0 and dbo.fn_RegExReplace(' + quotename(@websiteType) + ',''^(http|https|ftp)\://([a-zA-Z0-9\.\-]+(\:[a-zA-Z0-9\.&amp;%\$\-]+)*@)*((25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9])\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[0-9])|localhost|([a-zA-Z0-9\-]+\.)*[a-zA-Z0-9\-]+\.(' + @tldList + '|[a-zA-Z]{2}))(\:[0-9]+)*(/($|[a-zA-Z0-9\.\,\?\''''\\\+&amp;%\!$#\=~_\-]+))*$'','''') <> '''' 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@websiteType) + ' = '''' 
			WHERE len(' + quotename(@websiteType) + ') > 0 and dbo.fn_RegExReplace(' + quotename(@websiteType) + ',''^(http|https|ftp)\://([a-zA-Z0-9\.\-]+(\:[a-zA-Z0-9\.&amp;%\$\-]+)*@)*((25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9])\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[0-9])|localhost|([a-zA-Z0-9\-]+\.)*[a-zA-Z0-9\-]+\.(' + @tldList + '|[a-zA-Z]{2}))(\:[0-9]+)*(/($|[a-zA-Z0-9\.\,\?\''''\\\+&amp;%\!$#\=~_\-]+))*$'','''') <> '''' '
		EXEC(@dynSQL)
	END

	select @websiteType = min(websiteType)
		FROM dbo.ams_memberWebsiteTypes
		WHERE orgID = @orgID
		and websiteType NOT IN (select colName from @tblMissingCols)
		AND websiteType > @websiteType
END

-- ********************************
-- bad email data (non-fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying invalid e-mail data'

select @emailType = min(emailType)
	FROM dbo.ams_memberEmailTypes
	WHERE orgID = @orgID
	and emailType NOT IN (select colName from @tblMissingCols)
while @emailType is not null BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the e-mail ' + @emailType + ': '' + ' + quotename(@emailType) + ' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE len(' + quotename(@emailType) + ') > 0 and dbo.fn_RegExReplace(' + quotename(@emailType) + ',''^[a-zA-Z_0-9-''''\&\+~]+(\.[a-zA-Z_0-9-''''\&\+~]+)*@([a-zA-Z_0-9-]+\.)+[a-zA-Z]{2,7}$'','''') <> '''' 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the e-mail ' + @emailType + '.'' as msg
			FROM ' + @tmptbl + ' 
			WHERE len(' + quotename(@emailType) + ') > 0 and dbo.fn_RegExReplace(' + quotename(@emailType) + ',''^[a-zA-Z_0-9-''''\&\+~]+(\.[a-zA-Z_0-9-''''\&\+~]+)*@([a-zA-Z_0-9-]+\.)+[a-zA-Z]{2,7}$'','''') <> '''' 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@emailType) + ' = '''' 
			WHERE len(' + quotename(@emailType) + ') > 0 and dbo.fn_RegExReplace(' + quotename(@emailType) + ',''^[a-zA-Z_0-9-''''\&\+~]+(\.[a-zA-Z_0-9-''''\&\+~]+)*@([a-zA-Z_0-9-]+\.)+[a-zA-Z]{2,7}$'','''') <> '''' '
		EXEC(@dynSQL)
	END

	select @emailType = min(emailType)
		FROM dbo.ams_memberEmailTypes
		WHERE orgID = @orgID
		and emailType NOT IN (select colName from @tblMissingCols)
		AND emailType > @emailType
END

-- ********************************
-- bad address data (non-fatal)
--  - country specified but not valid country
--  - state and country specified but not valid state
--  - state specified but no country specified
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying invalid address data'

select @addressType = min(addressType)
	FROM dbo.ams_memberAddressTypes
	WHERE orgID = @orgID
	and (
		addressType + '_stateprov' NOT IN (select colName from @tblMissingCols)
		or
		addressType + '_country' NOT IN (select colName from @tblMissingCols)
	)
while @addressType is not null BEGIN

	-- country specified but not valid country
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for ' + @addressType + '_country' + ': '' + ' + quotename(@addressType + '_country') + ' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as tmp 
		WHERE len(isnull(tmp.' + quotename(@addressType + '_country') + ','''')) > 0 
		and not exists (select countryID from dbo.ams_countries where country = isnull(tmp.' + quotename(@addressType + '_country') + ',''''))
		ORDER BY tmp.rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the ' + @addressType + '_country' + '.'' as msg
			FROM ' + @tmptbl + ' as tmp 
			WHERE len(isnull(tmp.' + quotename(@addressType + '_country') + ','''')) > 0 
			and not exists (select countryID from dbo.ams_countries where country = isnull(tmp.' + quotename(@addressType + '_country') + ',''''))
			ORDER BY tmp.rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@addressType + '_country') + ' = '''' 
			WHERE len(isnull(' + quotename(@addressType + '_country') + ','''')) > 0 
			and not exists (select countryID from dbo.ams_countries where country = isnull(' + quotename(@addressType + '_country') + ','''')) '
		EXEC(@dynSQL)
	END

	-- state and country specified but not valid state
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for ' + @addressType + '_stateprov' + ': '' + ' + quotename(@addressType + '_stateprov') + ' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as tmp 
		WHERE len(isnull(tmp.' + quotename(@addressType + '_stateprov') + ','''')) > 0 
		and len(isnull(tmp.' + quotename(@addressType + '_country') + ','''')) > 0 
		and not exists (
			select s.stateID
			from dbo.ams_states as s
			inner join dbo.ams_countries as c on c.countryID = s.countryID
			where s.code = isnull(tmp.' + quotename(@addressType + '_stateprov') + ','''') 
			and c.country = isnull(tmp.' + quotename(@addressType + '_country') + ','''') 
		)
		and exists (select countryID from dbo.ams_countries where country = isnull(tmp.' + quotename(@addressType + '_country') + ',''''))
		ORDER BY tmp.rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the ' + @addressType + '_stateprov' + '.'' as msg
			FROM ' + @tmptbl + ' as tmp 
			WHERE len(isnull(tmp.' + quotename(@addressType + '_stateprov') + ','''')) > 0 
			and len(isnull(tmp.' + quotename(@addressType + '_country') + ','''')) > 0 
			and not exists (
				select s.stateID
				from dbo.ams_states as s
				inner join dbo.ams_countries as c on c.countryID = s.countryID
				where s.code = isnull(tmp.' + quotename(@addressType + '_stateprov') + ','''') 
				and c.country = isnull(tmp.' + quotename(@addressType + '_country') + ','''') 
			)
			and exists (select countryID from dbo.ams_countries where country = isnull(tmp.' + quotename(@addressType + '_country') + ',''''))
			ORDER BY tmp.rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@addressType + '_stateprov') + ' = '''' 
			WHERE len(isnull(' + quotename(@addressType + '_stateprov') + ','''')) > 0 
			and len(isnull(' + quotename(@addressType + '_country') + ','''')) > 0 
			and not exists (
				select s.stateID
				from dbo.ams_states as s
				inner join dbo.ams_countries as c on c.countryID = s.countryID
				where s.code = isnull(' + quotename(@addressType + '_stateprov') + ','''') 
				and c.country = isnull(' + quotename(@addressType + '_country') + ','''') 
			)
			and exists (select countryID from dbo.ams_countries where country = isnull(' + quotename(@addressType + '_country') + ','''')) '
		EXEC(@dynSQL)
	END

	-- state specified but no country specified
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had a missing country value for ' + @addressType + '_stateprov' + ': '' + ' + quotename(@addressType + '_stateprov') + ' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as tmp 
		WHERE len(isnull(tmp.' + quotename(@addressType + '_stateprov') + ','''')) > 0 
		and len(isnull(tmp.' + quotename(@addressType + '_country') + ','''')) = 0 
		ORDER BY tmp.rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the ' + @addressType + '_stateprov' + '.'' as msg
			FROM ' + @tmptbl + ' as tmp 
			WHERE len(isnull(tmp.' + quotename(@addressType + '_stateprov') + ','''')) > 0 
			and len(isnull(tmp.' + quotename(@addressType + '_country') + ','''')) = 0 
			ORDER BY tmp.rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@addressType + '_stateprov') + ' = '''' 
			WHERE len(isnull(' + quotename(@addressType + '_stateprov') + ','''')) > 0 
			and len(isnull(' + quotename(@addressType + '_country') + ','''')) = 0 '
		EXEC(@dynSQL)
	END

	select @addressType = min(addressType)
		FROM dbo.ams_memberAddressTypes
		WHERE orgID = @orgID
		and (
			addressType + '_stateprov' NOT IN (select colName from @tblMissingCols)
			or
			addressType + '_country' NOT IN (select colName from @tblMissingCols)
		)
		AND addressType > @addressType
END

-- ********************************
-- bad prefix data (non-fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying invalid prefix data'

IF @hasPrefix = 1 and @usePrefixList = 1 and NOT EXISTS (select colName from @tblMissingCols where colName = 'prefix') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid prefix value'' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE prefix is not null and len(prefix) > 0 and prefix not in (select prefix from dbo.ams_memberPrefixTypes where orgID = ' + cast(@orgID as varchar(10)) + ')  
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the Prefix.'' as msg
			FROM ' + @tmptbl + ' 
			WHERE prefix is not null and len(prefix) > 0 and prefix not in (select prefix from dbo.ams_memberPrefixTypes where orgID = ' + cast(@orgID as varchar(10)) + ')  
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET prefix = '''' 
			WHERE prefix is not null and len(prefix) > 0 and prefix not in (select prefix from dbo.ams_memberPrefixTypes where orgID = ' + cast(@orgID as varchar(10)) + ') '
		EXEC(@dynSQL)
	END
END

-- ********************************
-- bad prof license data (fatal and non-fatal)
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Identifying invalid professional license data'

select @plType = min(PLName)
	FROM dbo.ams_memberProfessionalLicenseTypes
	WHERE orgID = @orgID
	and PLName + '_licenseNumber' NOT IN (select colName from @tblMissingCols)
	and PLName + '_status' NOT IN (select colName from @tblMissingCols)
	and PLName + '_activeDate' NOT IN (select colName from @tblMissingCols)
while @plType is not null BEGIN
	
	-- dates must be valid
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			UPDATE ' + @tmptbl + ' set ' + quotename(@plType + '_activeDate') + ' = null where ' + quotename(@plType + '_activeDate') + ' = ''''
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN ' + quotename(@plType + '_activeDate') + ' datetime null
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @plType + '_activeDate contains invalid dates.', 1)
	END
	ELSE BEGIN
		-- if status is defined, it must be valid
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for ' + @plType + '_status' + ': '' + ' + quotename(@plType + '_status') + ' as msg, 0 as fatal 
			FROM ' + @tmptbl + ' as tmp 
			WHERE len(isnull(tmp.' + quotename(@plType + '_status') + ','''')) > 0 
			and not exists (
				select PLStatusID
				from dbo.ams_memberProfessionalLicenseStatuses
				where StatusName = isnull(tmp.' + quotename(@plType + '_status') + ','''')
				and orgID = ' + cast(@orgID as varchar(10)) + '
			)'
		INSERT INTO @tblErrors (msg, fatal)
		EXEC(@dynSQL)

		IF @@ROWCOUNT > 0 BEGIN
			select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the ' + @plType + '_status' + '.'' as msg
				FROM ' + @tmptbl + ' as tmp 
				WHERE len(isnull(tmp.' + quotename(@plType + '_status') + ','''')) > 0 
				and not exists (
					select PLStatusID
					from dbo.ams_memberProfessionalLicenseStatuses
					where StatusName = isnull(tmp.' + quotename(@plType + '_status') + ','''')
					and orgID = ' + cast(@orgID as varchar(10)) + '
				)'
			INSERT INTO @tblDataChanged (msg)
			EXEC(@dynSQL)

			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@plType + '_status') + ' = '''' 
				WHERE len(isnull(' + quotename(@plType + '_status') + ','''')) > 0 
				and not exists (
					select PLStatusID
					from dbo.ams_memberProfessionalLicenseStatuses
					where StatusName = isnull(' + quotename(@plType + '_status') + ','''')
					and orgID = ' + cast(@orgID as varchar(10)) + '
				)'
			EXEC(@dynSQL)
		END

		-- if licenseNumber or activeDate is defined, status must be as well
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had a missing value for ' + @plType + '_status'' as msg, 0 as fatal 
			FROM ' + @tmptbl + ' as tmp 
			WHERE (
				len(isnull(tmp.' + quotename(@plType + '_licenseNumber') + ','''')) > 0
				or tmp.' + quotename(@plType + '_activeDate') + ' is not null
			)
			and len(isnull(tmp.' + quotename(@plType + '_status') + ','''')) = 0 '
		INSERT INTO @tblErrors (msg, fatal)
		EXEC(@dynSQL)

		IF @@ROWCOUNT > 0 BEGIN
			select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the ' + @plType + ' information.'' as msg
				FROM ' + @tmptbl + ' as tmp 
				WHERE (
					len(isnull(tmp.' + quotename(@plType + '_licenseNumber') + ','''')) > 0
					or tmp.' + quotename(@plType + '_activeDate') + ' is not null
				)
				and len(isnull(tmp.' + quotename(@plType + '_status') + ','''')) = 0 '
			INSERT INTO @tblDataChanged (msg)
			EXEC(@dynSQL)

			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@plType + '_licenseNumber') + ' = '''',
					' + quotename(@plType + '_activeDate') + ' = null 
				WHERE (
					len(isnull(' + quotename(@plType + '_licenseNumber') + ','''')) > 0
					or ' + quotename(@plType + '_activeDate') + ' is not null
				)
				and len(isnull(' + quotename(@plType + '_status') + ','''')) = 0 '
			EXEC(@dynSQL)
		END
	END

	select @plType = min(PLName)
		FROM dbo.ams_memberProfessionalLicenseTypes
		WHERE orgID = @orgID
		and PLName + '_licenseNumber' NOT IN (select colName from @tblMissingCols)
		and PLName + '_status' NOT IN (select colName from @tblMissingCols)
		and PLName + '_activeDate' NOT IN (select colName from @tblMissingCols)
		and PLName > @plType
END

-- ********************************
-- check custom columns that have a default defined; use that default instead of null
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking columns with default values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	where mdc.allowNull = 0
	and mdc.skipImport = 0
	and mdc.orgID = @orgID
	and mdc.defaultValueID is not null
	and mdc.columnname not in (select colName from @tblMissingCols)
while @mincol is not null BEGIN
	select @defaultValueID = defaultValueID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol
	select @colDataTypeCode = dt.dataTypeCode from dbo.ams_memberDataColumnDataTypes as dt inner join dbo.ams_memberDataColumns as mdc on mdc.dataTypeID = dt.dataTypeID and mdc.orgID = @orgID and mdc.columnName = @mincol

	IF @colDataTypeCode = 'STRING'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueString from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where nullif(' + quoteName(@mincol) + ','''') is null'
	IF @colDataTypeCode = 'DECIMAL2'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueDecimal2 from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where ' + quoteName(@mincol) + ' is null'
	IF @colDataTypeCode = 'INTEGER'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueInteger from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where ' + quoteName(@mincol) + ' is null'
	IF @colDataTypeCode = 'DATE'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueDate from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where ' + quoteName(@mincol) + ' is null'
	IF @colDataTypeCode = 'BIT'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueBit from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where ' + quoteName(@mincol) + ' is null'
	IF @colDataTypeCode = 'XML'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueXML from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where ' + quoteName(@mincol) + ' is null'
	IF @colDataTypeCode = 'CONTENTOBJ' OR @colDataTypeCode = 'DOCUMENTOBJ'
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = (select columnValueSiteResourceID from dbo.ams_memberDataColumnValues where valueID = ' + cast(@defaultValueID as varchar(10)) + ') where ' + quoteName(@mincol) + ' is null'

	BEGIN TRY
		EXEC(@dynSQL)
	END TRY
	BEGIN CATCH
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column ' + @mincol + ' contains null values that could not use the defined default value.', 1)
	END CATCH

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		where mdc.allowNull = 0
		and mdc.skipImport = 0
		and mdc.orgID = @orgID
		and mdc.defaultValueID is not null
		and mdc.columnname not in (select colName from @tblMissingCols)
		and mdc.columnName > @mincol
END

-- ********************************
-- check for any custom columns that don't permit new values on import
-- for these cols, make sure all values in file are in defined set
-- skip columns where there is a fatal error on converting all values to correct datatype
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking STRING columns for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 0
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'STRING'
		and mdc.columnname not in (select colName from @tblMissingCols)
while @mincol is not null BEGIN
	select @columnID = null, @allownull = null
	select @columnID = columnID, @allownull = allowNull from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	IF @allownull = 1 BEGIN
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = null where ' + quoteName(@mincol) + ' = '''''
		EXEC(@dynSQL)
	END

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and mdcv.columnValueString = importfile.' + quoteName(@mincol) + ' 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and mdcv.columnValueString = importfile.' + quoteName(@mincol) + ' 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		IF @allownull = 1 BEGIN		
			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@mincol) + ' = null
				WHERE rowID in (
					SELECT rowID
					FROM ' + @tmptbl + ' as importfile
					LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
						and mdcv.columnValueString = importfile.' + quoteName(@mincol) + ' 
					WHERE mdcv.valueID is null 
					AND importfile.' + quoteName(@mincol) + ' is not null 
				)'
		END ELSE BEGIN
			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@mincol) + ' = '''' 
				WHERE rowID in (
					SELECT rowID
					FROM ' + @tmptbl + ' as importfile
					LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
						and mdcv.columnValueString = importfile.' + quoteName(@mincol) + ' 
					WHERE mdcv.valueID is null 
					AND importfile.' + quoteName(@mincol) + ' is not null 
				)'
		END
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and mdc.allowMultiple = 0
			and mdc.allowNewValuesOnImport = 0
			and dt.dataTypeCode = 'STRING'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking STRING columns (multiple values) for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		and mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 1
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'STRING'
		and mdc.columnname not in (select colName from @tblMissingCols)
while @mincol is not null BEGIN
	select @columnID = null, @allownull = null
	select @columnID = columnID, @allownull = allowNull from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	IF @allownull = 1 BEGIN
		select @dynSQL = 'update ' + @tmptbl + ' set ' + quoteName(@mincol) + ' = null where ' + quoteName(@mincol) + ' = '''''
		EXEC(@dynSQL)
	END

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		cross apply dbo.fn_varcharListToTable(' + quotename(@mincol) + ',''|'') as tbl
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and mdcv.columnValueString = tbl.listitem  
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
	
	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			cross apply dbo.fn_varcharListToTable(' + quotename(@mincol) + ',''|'') as tbl
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and mdcv.columnValueString = tbl.listitem  
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		IF @allownull = 1 BEGIN		
			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@mincol) + ' = null
				WHERE rowID in (
					SELECT rowID
					FROM ' + @tmptbl + ' as importfile
					cross apply dbo.fn_varcharListToTable(' + quotename(@mincol) + ',''|'') as tbl
					LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
						and mdcv.columnValueString = tbl.listitem  
					WHERE mdcv.valueID is null 
					AND importfile.' + quoteName(@mincol) + ' is not null 
				)'
		END ELSE BEGIN
			select @dynSQL = 'UPDATE ' + @tmptbl + ' 
				SET ' + quotename(@mincol) + ' = '''' 
				WHERE rowID in (
					SELECT rowID
					FROM ' + @tmptbl + ' as importfile
					cross apply dbo.fn_varcharListToTable(' + quotename(@mincol) + ',''|'') as tbl
					LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
						and mdcv.columnValueString = tbl.listitem  
					WHERE mdcv.valueID is null 
					AND importfile.' + quoteName(@mincol) + ' is not null 
				)'
		END
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			and mdc.orgID = @orgID
			and mdc.skipImport = 0
			and mdc.allowMultiple = 1
			and mdc.allowNewValuesOnImport = 0
			and dt.dataTypeCode = 'STRING'
			and mdc.columnname not in (select colName from @tblMissingCols)
			and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking DATE columns for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'DATE'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid dates.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and mdcv.columnValueDate = importfile.' + quoteName(@mincol) + ' 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and mdcv.columnValueDate = importfile.' + quoteName(@mincol) + ' 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
					and mdcv.columnValueDate = importfile.' + quoteName(@mincol) + ' 
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END
	
	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'DATE'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid dates.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking DECIMAL2 columns for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowMultiple = 0
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'DECIMAL2'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid decimal values.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and mdcv.columnValueDecimal2 = importfile.' + quoteName(@mincol) + ' 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg 
			FROM ' + @tmptbl + ' as importfile
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and mdcv.columnValueDecimal2 = importfile.' + quoteName(@mincol) + ' 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
					and mdcv.columnValueDecimal2 = importfile.' + quoteName(@mincol) + ' 
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END
	
	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 0
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'DECIMAL2'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid decimal values.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking DECIMAL2 columns (multiple values) for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowMultiple = 1
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'DECIMAL2'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid decimal values.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		cross apply dbo.fn_decimal2ListToTable(' + quotename(@mincol) + ',''|'') as tbl
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + ' 
			and mdcv.columnValueDecimal2 = tbl.listitem 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY importfile.rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
	
	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			cross apply dbo.fn_decimal2ListToTable(' + quotename(@mincol) + ',''|'') as tbl
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + ' 
				and mdcv.columnValueDecimal2 = tbl.listitem 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY importfile.rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				cross apply dbo.fn_decimal2ListToTable(' + quotename(@mincol) + ',''|'') as tbl
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + ' 
					and mdcv.columnValueDecimal2 = tbl.listitem 
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 1
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'DECIMAL2'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid decimal values.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking INTEGER columns for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowMultiple = 0
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'INTEGER'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid whole number values.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and mdcv.columnValueInteger = importfile.' + quoteName(@mincol) + ' 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg 
			FROM ' + @tmptbl + ' as importfile
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and mdcv.columnValueInteger = importfile.' + quoteName(@mincol) + ' 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
					and mdcv.columnValueInteger = importfile.' + quoteName(@mincol) + ' 
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 0
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'INTEGER'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid whole number values.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking INTEGER columns (multiple values) for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowMultiple = 1
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'INTEGER'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid whole number values.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		cross apply dbo.fn_intListToTable(' + quotename(@mincol) + ',''|'') as tbl
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + ' 
			and mdcv.columnValueInteger = tbl.listitem 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			cross apply dbo.fn_intListToTable(' + quotename(@mincol) + ',''|'') as tbl
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + ' 
				and mdcv.columnValueInteger = tbl.listitem 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				cross apply dbo.fn_intListToTable(' + quotename(@mincol) + ',''|'') as tbl
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + ' 
					and mdcv.columnValueInteger = tbl.listitem 
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowMultiple = 1
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'INTEGER'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid whole number values.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking BIT columns for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'BIT'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid boolean values.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and mdcv.columnValueBit = importfile.' + quoteName(@mincol) + ' 
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and mdcv.columnValueBit = importfile.' + quoteName(@mincol) + ' 
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
					and mdcv.columnValueBit = importfile.' + quoteName(@mincol) + ' 
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'BIT'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid boolean values.')
		and mdc.columnName > @mincol
END

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Checking XML columns for disallowed values'
select @mincol = null
select @mincol = min(mdc.columnname)
	from dbo.ams_memberDataColumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
	where mdc.orgID = @orgID
	and mdc.skipImport = 0
	and mdc.allowNewValuesOnImport = 0
	and dt.dataTypeCode = 'XML'
	and mdc.columnname not in (select colName from @tblMissingCols)
	and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid XML values.')
while @mincol is not null BEGIN
	select @columnID = columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @mincol

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') had an invalid value for the column ' + @mincol + ''' as msg, 0 as fatal 
		FROM ' + @tmptbl + ' as importfile
		LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
			and cast(mdcv.columnValueXML as varchar(max)) = cast(importfile.' + quoteName(@mincol) + ' as varchar(max))  
		WHERE mdcv.valueID is null 
		AND importfile.' + quoteName(@mincol) + ' is not null 
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	IF @@ROWCOUNT > 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(firstname,'''') + '' '' + isnull(lastname,'''') + '' - '' + isnull(membernumber,'''') + '') will clear the column ' + @mincol + '.'' as msg
			FROM ' + @tmptbl + ' as importfile
			LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
				and cast(mdcv.columnValueXML as varchar(max)) = cast(importfile.' + quoteName(@mincol) + ' as varchar(max))  
			WHERE mdcv.valueID is null 
			AND importfile.' + quoteName(@mincol) + ' is not null 
			ORDER BY rowID'
		INSERT INTO @tblDataChanged (msg)
		EXEC(@dynSQL)

		select @dynSQL = 'UPDATE ' + @tmptbl + ' 
			SET ' + quotename(@mincol) + ' = null
			WHERE rowID in (
				SELECT rowID
				FROM ' + @tmptbl + ' as importfile
				LEFT OUTER JOIN dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = ' + cast(@columnID as varchar(10)) + '
					and cast(mdcv.columnValueXML as varchar(max)) = cast(importfile.' + quoteName(@mincol) + ' as varchar(max))  
				WHERE mdcv.valueID is null 
				AND importfile.' + quoteName(@mincol) + ' is not null 
			)'
		EXEC(@dynSQL)
	END

	select @mincol = min(mdc.columnname)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.skipImport = 0
		and mdc.allowNewValuesOnImport = 0
		and dt.dataTypeCode = 'XML'
		and mdc.columnname not in (select colName from @tblMissingCols)
		and not exists (select * from @tblErrors where fatal = 1 and msg = 'The column ' + mdc.columnname + ' contains invalid XML values.')
		and mdc.columnName > @mincol
END


-- cleanup - these are no longer needed
IF OBJECT_ID('tempdb..#tblOrgCols') IS NOT NULL
	DROP TABLE #tblOrgCols
IF OBJECT_ID('tempdb..#tblImportCols') IS NOT NULL
	DROP TABLE #tblImportCols

-- ******************************** 
-- dump flat table and format file and creation script
-- would love to use xml format files, but it appears column names with spaces cause it to fail
-- ******************************** 
IF NOT EXISTS (select rowID from @tblErrors where fatal = 1) BEGIN
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Backup Flattened Data'

	SELECT @prefix = @orgcode + '_flat_' + convert(varchar(8),getdate(),112) + replace(convert(varchar(8),getdate(),108),':','')
	SELECT @flatfile = @pathToExport + @prefix

	select @exportcmd = 'bcp ' + @tmptbl + ' format nul -f ' + @flatfile + '.txt -n -T -S' + CAST(serverproperty('servername') as varchar(40))
	EXEC master..xp_cmdshell @exportcmd, NO_OUTPUT

	select @exportcmd = 'bcp ' + @tmptbl + ' out ' + @flatfile + '.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
	EXEC master..xp_cmdshell @exportcmd, NO_OUTPUT

	declare @createTableSQLTop varchar(100)          
	declare @coltypes table (datatype varchar(16))          
	insert into @coltypes values('bit')          
	insert into @coltypes values('binary')          
	insert into @coltypes values('bigint')          
	insert into @coltypes values('int')          
	insert into @coltypes values('float')          
	insert into @coltypes values('datetime')          
	insert into @coltypes values('text')          
	insert into @coltypes values('image')          
	insert into @coltypes values('money')          
	insert into @coltypes values('uniqueidentifier')          
	insert into @coltypes values('smalldatetime')          
	insert into @coltypes values('tinyint')          
	insert into @coltypes values('smallint')          
	insert into @coltypes values('sql_variant')          
	select @dynSQL = ''
	select @dynSQL = @dynSQL +           
		case when charindex('(',@dynSQL,1)<=0 then '(' else '' end + '[' + Column_Name + '] ' +Data_Type +
		case when Data_Type in (Select datatype from @coltypes) then '' else  '(' end+
		case when data_type in ('real','decimal','numeric')  then cast(isnull(numeric_precision,'') as varchar)+','+
		case when data_type in ('real','decimal','numeric') then cast(isnull(Numeric_Scale,'') as varchar) end
		when data_type in ('nvarchar','varchar') and cast(isnull(Character_Maximum_Length,'') as varchar) = '-1' then 'max'
		when data_type in ('char','nvarchar','varchar','nchar') then cast(isnull(Character_Maximum_Length,'') as varchar) else '' end+
		case when Data_Type in (Select datatype from @coltypes)then '' else  ')' end+
		case when Is_Nullable='No' then ' Not null,' else ' null,' end
		from tempdb.Information_Schema.COLUMNS where Table_Name=@tmptbl
		order by ordinal_position
	select @createTableSQLTop = 'Create table ##xxx ' 
	select @dynSQL=@createTableSQLTop + substring(@dynSQL,1,len(@dynSQL)-1) +' )'            
	if dbo.fn_WriteFile(@pathToExport + @prefix + '.sql', @dynSQL, 1) = 0 BEGIN
		EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Failed writing table creation script'
		RETURN 0
	END

	-- create index on temp table for numbers below
	EXEC('CREATE NONCLUSTERED INDEX [idx_' + @tmptbl + '_memnum_rowid] ON ' + @tmptbl + '([membernumber] ASC,[rowID] ASC)')
END ELSE BEGIN
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Backup Flattened Data Skipped - Fatal Errors'
END

-- ******************************** 
-- Counts
-- ******************************** 
IF NOT EXISTS (select rowID from @tblErrors where fatal = 1) BEGIN
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Querying Intermediate Totals'

	-- TotalCurrentMembers
	INSERT INTO @tblCounts (countName, countNum)
	SELECT 'TotalCurrentMembers', COUNT(memberid)
	FROM dbo.ams_members
	WHERE orgID = @orgID
	AND status <> 'D'
	and memberTypeID = 2
	AND memberID = activeMemberID

	-- TotalCurrentActive
	INSERT INTO @tblCounts (countName, countNum)
	SELECT 'TotalCurrentActive', COUNT(memberid)
	FROM dbo.ams_members
	WHERE orgID = @orgID
	AND status = 'A'
	and memberTypeID = 2
	AND memberID = activeMemberID

	-- TotalCurrentInActive
	INSERT INTO @tblCounts (countName, countNum)
	SELECT 'TotalCurrentInActive', COUNT(memberid)
	FROM dbo.ams_members
	WHERE orgID = @orgID
	AND status = 'I'
	and memberTypeID = 2
	AND memberID = activeMemberID

	-- TotalNewImported
	select @dynSQL = 'SELECT ''TotalNewImported'', Count(newlist.rowID)
		FROM ' + @tmptbl + ' as newlist
		LEFT OUTER JOIN dbo.ams_members as m on m.memberNumber = newlist.memberNumber
			AND m.orgID = ' + cast(@orgID as varchar(7)) + ' 
			and m.memberID = m.activeMemberID
			and m.memberTypeID = 2
		WHERE m.memberID is null'
	INSERT INTO @tblCounts (countName, countNum)
	EXEC(@dynSQL)
	
	-- TotalCurrentMembersWillActivated
	select @dynSQL = 'SELECT ''TotalCurrentMembersWillBeActivated'', COUNT(distinct m.memberID)
		FROM dbo.ams_members as m
		INNER JOIN ' + @tmptbl + ' as newlist on newlist.memberNumber = m.memberNumber 
			AND m.orgID = ' + cast(@orgID as varchar(7)) + ' 
			AND m.status = ''I''
			and m.memberID = m.activeMemberID
			and m.memberTypeID = 2'
	INSERT INTO @tblCounts (countName, countNum)
	EXEC(@dynSQL)

	-- TotalCurrentMembersWillBeInactivated
	select @dynSQL = 'SELECT ''TotalCurrentMembersWillBeInactivated'', COUNT(distinct m.memberID)
		FROM dbo.organizations as o
		INNER JOIN dbo.ams_members as m on m.orgID = o.orgID
			AND m.orgID = ' + cast(@orgID as varchar(7)) + ' 
			AND m.status = ''A''
			and m.memberID = m.activeMemberID
			and m.memberTypeID = 2
		LEFT OUTER JOIN ' + @tmptbl + ' as newlist on newlist.memberNumber = m.memberNumber 
		WHERE newlist.rowID is null'
	INSERT INTO @tblCounts (countName, countNum)
	EXEC(@dynSQL)

	-- TotalMembersImported
	select @dynSQL = 'SELECT ''TotalMembersImported'', COUNT(rowID)
		FROM ' + @tmptbl
	INSERT INTO @tblCounts (countName, countNum)
	EXEC(@dynSQL)

END ELSE BEGIN
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Querying Intermediate Totals Skipped - Fatal Errors'
END

-- ******************************** 
-- Member Lists
-- ******************************** 
IF NOT EXISTS (select rowID from @tblErrors where fatal = 1) BEGIN
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Querying Member Lists'

	-- members that will be activated 
	select @dynSQL = 'SELECT TOP 100 PERCENT m.memberID, m.memberNumber, m.firstname, m.lastname
		FROM dbo.ams_members as m WITH(NOLOCK)
		INNER JOIN ' + @tmptbl + ' as newlist on newlist.memberNumber = m.memberNumber 
			AND m.orgID = ' + cast(@orgID as varchar(7)) + '
			AND m.status = ''I''
			and m.memberID = m.activeMemberID
			and m.memberTypeID = 2
		ORDER BY newlist.rowID'
	INSERT INTO @tblMemList1 (memberid, memberNumber, firstname, lastname)
	EXEC(@dynSQL)

	-- members that will be inactivated
	select @dynSQL = 'SELECT TOP 100 PERCENT  m.memberID, m.memberNumber, m.firstname, m.lastname
		FROM dbo.organizations as o
		INNER JOIN dbo.ams_members as m on m.orgID = o.orgID
			AND m.orgID = ' + cast(@orgID as varchar(7)) + '
			AND m.status = ''A''
			and m.memberID = m.activeMemberID
			and m.memberTypeID = 2
		LEFT OUTER JOIN ' + @tmptbl + ' as newlist on newlist.memberNumber = m.memberNumber 
		WHERE newlist.rowID is null
		ORDER BY m.memberID'
	INSERT INTO @tblMemList2 (memberid, memberNumber, firstname, lastname)
	EXEC(@dynSQL)

	-- members that will be added
	select @dynSQL = 'SELECT TOP 100 PERCENT newlist.rowID, newlist.memberNumber, newlist.firstname, newlist.lastname
		FROM ' + @tmptbl + ' as newlist
		LEFT OUTER JOIN dbo.ams_members as m on m.memberNumber = newlist.memberNumber
			AND m.orgID = ' + cast(@orgID as varchar(7)) + '
			and m.memberID = m.activeMemberID
			and m.memberTypeID = 2
		WHERE m.memberID is null
		ORDER BY newlist.rowID'
	INSERT INTO @tblMemList3 (rowID, memberNumber, firstname, lastname)
	EXEC(@dynSQL)

END ELSE BEGIN
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Querying Member Lists Skipped - Fatal Errors'
END

-- ********************************
-- generate result xml file 
-- ********************************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Generating XML response'
select @importResult = (
	select getdate() as "@date", @flatfile as "@flatfile",
		isnull((select top 301 dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg", "@severity" = case fatal when 1 then 'fatal' else 'nonfatal' end
		from @tblErrors
		order by rowid
		FOR XML path('error'), root('errors'), type),'<errors/>'),

		isnull((select countName as "@name", countNum as "@num"
		from @tblCounts
		order by rowid
		FOR XML path('count'), root('counts'), type),'<counts/>'),

		isnull((select top 301 memberid as "@memberid", membernumber as "@membernumber", firstname as "@firstname", lastname as "@lastname"
		from @tblMemList1
		order by memberid
		FOR XML path('member'), root('qryactivated'), type),'<qryactivated/>'),

		isnull((select top 301 memberid as "@memberid", membernumber as "@membernumber", firstname as "@firstname", lastname as "@lastname"
		from @tblMemList2
		order by memberid
		FOR XML path('member'), root('qryinactivated'), type),'<qryinactivated/>'),

		isnull((select top 301 rowid as "@rowid", membernumber as "@membernumber", firstname as "@firstname", lastname as "@lastname"
		from @tblMemList3
		order by rowid
		FOR XML path('member'), root('qryadded'), type),'<qryadded/>'),

		isnull((select top 301 dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
		from @tblDataChanged
		order by rowid
		FOR XML path('change'), root('qrychanges'), type),'<qrychanges/>')
	for xml path('import'), TYPE)

-- drop temp tables 
IF OBJECT_ID('tempdb..' + @tmptbl) IS NOT NULL
	EXEC('DROP TABLE ' + @tmptbl)

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Import to Holding Complete'

RETURN 0
GO
