USE membercentral
GO

declare @siteID int, @storeID int
select @siteID = siteID from membercentral.dbo.sites where siteCode = 'NY'

update r
set r.rate = tt.rate
from membercentral.dbo.store s
inner join dbo.store_Products as p on p.storeID = s.storeID                
inner join dbo.store_ProductFormats as spf on spf.itemID = p.itemID
inner join dbo.store_Rates r on r.formatID = spf.formatID
inner join dataTransfer.dbo.NY_June2014StoreWideDiscount tt on tt.rateID = r.rateID and tt.formatID = r.formatID
where s.siteID = @siteID	
GO


use datatransfer
GO
IF OBJECT_ID('NY_June2014StoreWideDiscount') IS NOT NULL
	DROP TABLE NY_June2014StoreWideDiscount

GO

