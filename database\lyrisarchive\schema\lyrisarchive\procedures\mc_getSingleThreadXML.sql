ALTER PROCEDURE [dbo].[mc_getSingleThreadXML]
@listname varchar(60),
@messageID int

as

SET NOCOUNT ON

DECLARE @messagesXML varchar(max);
DECLARE @headXML varchar(max);
DECLARE @messageCount int;

SELECT @messageCount = count(*)
FROM	messages_ m
	INNER JOIN messages_ p on m.parentID_ = p.parentID_ and p.isVisible=1
WHERE	m.messageID_ = @messageID and m.isVisible=1


select @headXML = '<rows parent="0" pos="0" total_count="' + cast(@messageCount as varchar(10)) + '"><head>
			<column align="left" sort="na" type="datetime" width="150">Date</column>
			<column align="left" sort="na" type="ro" width="150">Contributor</column>
			<column align="center" sort="na" type="img" width="25">#cspan</column>
			<column align="left" sort="na" type="tree" width="*">Subject</column>
			<beforeInit>
				<call command="setImagePath"><param>/assets/common/javascript/dhtmlxgrid/imgs/</param></call>
				<call command="setSkin"><param>modern</param></call>
				<call command="enableColumnMove"><param>false</param></call>
				<call command="enableRowsHover"><param>true</param><param>grid_hover</param></call>
				<call command="attachEvent"><param>onXLS</param><param>mcg_xls</param></call>
				<call command="attachEvent"><param>onXLE</param><param>mcg_xle</param></call>
				<call command="enableSmartRendering"><param>true</param></call>
			</beforeInit>
			<settings>
				<colwidth>px</colwidth>
			</settings>
		</head>';


 select @messagesXML = isNull(((
	select
		thread.messageID_ as "@id",
		'tsAppBodyText' as "@class",
		1 as "@open",
		'tsAppBodyText' as "mc_timecell/@class",
		thread.creatStamp_ as "mc_timecell",
		'tsAppBodyText' as "mc_fromcell/@class",
		thread.hdrfrom_ as "mc_fromcell",
		"mc_attachmentcell" = case when thread.attachmentflag = 1 then '/assets/common/images/paperclip.gif^View Message^javascript:mcg_getMessage(' + cast(thread.messageID_ as varchar(10)) + ')^_self' else '/assets/common/images/spacer.gif' end,
		'tsAppBodyText' as "mc_subjectcell/@class",
		thread.hdrSubject_ as "mc_subjectcell/@title",
		'<a href="javascript:mcg_getMessage(' + cast(thread.messageID_ as varchar(10)) + ')">' + thread.hdrSubject_ + '</a>' as "mc_subjectcell",
		(
			select
				m.messageID_ as "@id",
				'tsAppBodyText' as "@class",
				'tsAppBodyText' as "mc_timecell/@class",
				m.creatStamp_ as "mc_timecell",
				'tsAppBodyText' as "mc_fromcell/@class",
				m.hdrfrom_ as "mc_fromcell",
				mc_attachmentcell = case when m.attachmentflag = 1 then '/assets/common/images/paperclip.gif^View Message^javascript:mcg_getMessage(' + cast(m.messageID_ as varchar(10)) + ')^_self' else '/assets/common/images/spacer.gif' end,
				'tsAppBodyText' as "mc_subjectcell/@class",
				m.hdrSubject_ as "mc_subjectcell/@title",
				'<a href="javascript:mcg_getMessage(' + cast(m.messageID_ as varchar(10)) + ')">' + m.hdrSubject_ + '</a>' as "mc_subjectcell"
			from messages_ m
			inner join messageLists ml
				on m.listID = ml.listID
				and ml.list = @listname
				and m.parentID_ = thread.messageID_
				and m.parentID_ <> m.messageID_
				order by m.creatStamp_
				for xml path('row'), type)
			
	FROM messages_ thread
		INNER JOIN messageLists threadml
			ON thread.listID = threadml.listID
			AND threadml.list = @listname
			AND thread.messageID_ = thread.parentID_
		INNER JOIN messages_ p on thread.parentID_ = p.parentID_ and p.isVisible=1
	WHERE	thread.messageID_ = @messageID and thread.isVisible=1
	ORDER BY thread.creatStamp_ desc
	for xml path('row'), root('rows')
)),'<rows></rows>')

select @messagesXML = replace(@messagesXML, '<rows>', @headXML);

select convert(xml,dbo.fn_RegExReplace(@messagesXML,'(mc_attachmentcell|mc_timecell|mc_fromcell|mc_subjectcell)','cell')) as messagesXML

SET NOCOUNT OFF
GO
