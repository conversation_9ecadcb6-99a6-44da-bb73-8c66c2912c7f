ALTER PROCEDURE [dbo].[mc_getThreadedXML]
@listname varchar(60),
@startdate smalldatetime = null

as

SET NOCOUNT ON

-- set startdate if null passed in so we dont have to duplicate queries below
if (@startdate is null) 
	SELECT @startdate = '1-1-2008'

DECLARE @messagesXML varchar(max);
DECLARE @headXML varchar(max);
DECLARE @messageCount int;

select @messageCount = count(*)
from dbo.messages_ m
inner join dbo.messageLists ml on m.listID = ml.listID
	and ml.list = @listname
	and m.creatStamp_ > @startdate
	and m.isVisible=1
option(recompile)

select @headXML = '<rows parent="0" pos="0" total_count="' + cast(@messageCount as varchar(10)) + '"><head>
			<column align="left" sort="na" type="ro" width="150">Date</column>
			<column align="left" sort="na" type="ro" width="150">Contributor</column>
			<column align="center" sort="na" type="img" width="25">#cspan</column>
			<column align="left" sort="na" type="tree" width="*">Subject</column>
			<settings>
				<colwidth>px</colwidth>
			</settings>
		</head>';

IF OBJECT_ID('tempdb..#tmpThread') IS NOT NULL 
	DROP TABLE #tmpThread

select thread.messageID_, dbo.fn_RegExReplace(thread.hdrSubject_,'[^\x20-\x7E]','') as hdrSubject_
into #tmpThread
from dbo.messages_ thread
inner join dbo.messageLists threadml on thread.listID = threadml.listID
	and threadml.list = @listname
	and thread.creatStamp_ > @startdate
	and thread.isVisible=1
order by thread.creatStamp_

select @messagesXML = isNull(((
     select
		thread.messageID_ as "@id",
		'tsAppBodyText' as "@class",
		1 as "@open",
		'tsAppBodyText' as "mc_timecell/@class",
		CONVERT(VARCHAR(10),thread.creatStamp_,101) + ' ' + RIGHT(CONVERT(VARCHAR,thread.creatStamp_,100),7) as "mc_timecell",
		'tsAppBodyText' as "mc_fromcell/@class",
		thread.hdrfrom_ as "mc_fromcell",
		"mc_attachmentcell" = case when thread.attachmentflag = 1 then '/assets/common/images/paperclip.gif^View Message^javascript:mcg_getMessage(' + cast(thread.messageID_ as varchar(10)) + ')^_self' else '/assets/common/images/spacer.gif' end,
		'tsAppBodyText' as "mc_subjectcell/@class",
		tmp.hdrSubject_ as "mc_subjectcell/@title",
		'<a href="javascript:mcg_getMessage(' + cast(thread.messageID_ as varchar(10)) + ')">' + tmp.hdrSubject_ + '</a>' as "mc_subjectcell",
		(
			select
				m.messageID_ as "@id",
				'tsAppBodyText' as "@class",
				'tsAppBodyText' as "mc_timecell/@class",
				CONVERT(VARCHAR(10),m.creatStamp_,101) + ' ' + RIGHT(CONVERT(VARCHAR,m.creatStamp_,100),7) as "mc_timecell",
				'tsAppBodyText' as "mc_fromcell/@class",
				m.hdrfrom_ as "mc_fromcell",
				mc_attachmentcell = case when m.attachmentflag = 1 then '/assets/common/images/paperclip.gif^View Message^javascript:mcg_getMessage(' + cast(m.messageID_ as varchar(10)) + ')^_self' else '/assets/common/images/spacer.gif' end,
				'tsAppBodyText' as "mc_subjectcell/@class",
				tmpM.hdrSubject_ as "mc_subjectcell/@title",
				'<a href="javascript:mcg_getMessage(' + cast(m.messageID_ as varchar(10)) + ')">' + tmpM.hdrSubject_ + '</a>' as "mc_subjectcell"
            from #tmpThread as tmpM
			inner join messages_ as m on m.messageID_ = tmpM.messageID_
			where m.parentID_ = thread.messageID_
			and m.parentID_ <> m.messageID_
			and m.isVisible=1
			order by m.creatStamp_
			for xml path('row'), type)

	from #tmpThread as tmp
	inner join messages_ as thread on thread.messageID_ = tmp.messageID_
	where thread.messageID_ = thread.parentID_ and thread.isVisible=1

	order by thread.creatStamp_ desc
	for xml path('row'), root('rows')
)),'<rows></rows>')
option(recompile)

IF OBJECT_ID('tempdb..#tmpThread') IS NOT NULL 
	DROP TABLE #tmpThread

select @messagesXML = replace(@messagesXML, '<rows>', @headXML);
select convert(xml,dbo.fn_RegExReplace(@messagesXML,'(mc_attachmentcell|mc_timecell|mc_fromcell|mc_subjectcell)','cell')) as messagesXML

SET NOCOUNT OFF
GO
