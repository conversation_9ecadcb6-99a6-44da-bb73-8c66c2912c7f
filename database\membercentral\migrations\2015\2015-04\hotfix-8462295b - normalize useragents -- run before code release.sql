--DROP TABLE dbo.statsUserAgents
CREATE TABLE dbo.statsUserAgents
	(
	useragentID int NOT NULL IDENTITY (1, 1),
	useragent varchar(500) NOT NULL,
     useragentMD5 BINARY(32) NOT NULL,
	isBot bit NULL,

	)  ON [PRIMARY]
GO
ALTER TABLE dbo.statsUserAgents ADD CONSTRAINT
	PK_statsUserAgents PRIMARY KEY CLUSTERED 
	(
	useragentID
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

GO


insert into dbo.statsUserAgents(useragent,useragentMD5)
select tmp.useragent, hashbytes('md5',tmp.useragent) as useragentMD5
from
(
select useragent
from statssessions ss (nolock)
group by useragent
except
select useragent
from statsUserAgents
) tmp
order by tmp.useragent
GO

CREATE INDEX IX_statsUserAgents_useragentMD5 on platformstats.dbo.statsUserAgents (useragentMD5 asc)

ALTER TABLE dbo.statsSessions ADD
	userAgentID int NULL
GO
ALTER TABLE dbo.statsSessions ADD CONSTRAINT
	FK_statsSessions_statsUserAgents FOREIGN KEY
	(
	userAgentID
	) REFERENCES dbo.statsUserAgents
	(
	useragentID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO