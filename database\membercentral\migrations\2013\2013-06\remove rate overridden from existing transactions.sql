declare @sitecode varchar(5)

set @sitecode = null

update tr set 	
	detail = replace(tr.detail,' - rate overridden','')
from sites s
inner join sub_types t
	on s.siteID = t.siteID
	and s.sitecode = isnull(@sitecode,s.sitecode)
inner join sub_subscriptions subs
	on subs.typeID = t.typeID
inner join sub_subscribers ss
	on ss.subscriptionID = subs.subscriptionID
inner join tr_applications ta
	on ta.itemID = ss.subscriberID
	and ta.itemType = 'Dues'
	and ta.applicationTypeID = 17
inner join tr_transactions tr
	on tr.transactionID = ta.transactionID
	and tr.detail like '%rate overridden%'

-- the above doesnt catch adjustments or voids of sales or voids of adjustments, so catch them this way (I know)
update tr_transactions
set detail = replace(detail,' - rate overridden','')
where detail like '%rate overridden%'