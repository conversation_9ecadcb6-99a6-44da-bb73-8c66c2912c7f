CREATE PROC dbo.sponsors_getSponsorGroupings
@referenceType varchar(50),
@referenceID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- Determine context and execute appropriate logic
	IF @referenceType = 'Events'
	BEGIN
		SELECT sponsorGroupingID, sponsorGrouping, sponsorGroupingOrder
		FROM dbo.ev_sponsorGrouping
		WHERE eventID = @referenceID
		ORDER BY sponsorGroupingOrder;
	END
	ELSE IF @referenceType IN ('swlProgram', 'swodProgram', 'swbprogram')
	BEGIN
		SELECT sponsorGroupingID, sponsorGrouping, sponsorGroupingOrder
		FROM seminarWeb.dbo.sw_sponsorGrouping
		WHERE seminarID = @referenceID AND participantID = 0
		ORDER BY sponsorGroupingOrder;
	END
	ELSE
	BEGIN
		RAISERROR('Unsupported reference type: %s', 16, 1, @referenceType);
		RETURN -1;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
