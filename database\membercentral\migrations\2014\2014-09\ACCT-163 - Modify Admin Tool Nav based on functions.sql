use membercentral
GO

-- add new manage view accounting reports function
declare @resourceTypeID int, @functionID int, @resourceTypeFunctionID int
select @resourceTypeID = dbo.fn_getResourceTypeID('Admin')

EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='viewAccountingReports', @displayName='View Accounting Reports', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('viewAccountingReports',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID
GO

-- delete batchManage function - no longer needed now that we have granular controls on batch actions
declare @functionID int
select @functionID = functionID from cms_siteResourceFunctions where functionName = 'batchManage'
delete from dbo.cms_siteResourceRightsCache where functionID = @functionID
delete from dbo.cache_perms_siteResourceFunctionRightPrints where functionID = @functionID
delete from dbo.cms_siteResourceRoleFunctions where resourceTypeFunctionID in (select resourceTypeFunctionID from dbo.cms_siteResourceTypeFunctions where functionID = @functionID)
delete from dbo.admin_functionsDeterminingNav where resourceTypeFunctionID in (select resourceTypeFunctionID from dbo.cms_siteResourceTypeFunctions where functionID = @functionID)
delete from dbo.cms_siteResourceTypeFunctions where functionID = @functionID
delete from cms_siteResourceFunctions where functionID = @functionID
GO

-- set the Manage Batches nav to look for specific functions
declare @resourceTypeID int, @toolTypeID int, @resourceTypeFunctionID int, @navigationID int
select @resourceTypeID = dbo.fn_getResourceTypeID('BatchAdmin')
select @toolTypeID = toolTypeID from dbo.admin_toolTypes where toolType = 'BatchAdmin'
select @navigationID = navigationID from dbo.admin_navigation where navName = 'Manage Batches'
SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('batchCreate',@resourceTypeID))
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('batchEdit',@resourceTypeID))
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('batchClose',@resourceTypeID))
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('batchPost',@resourceTypeID))
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('batchOpen',@resourceTypeID))
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('batchDownload',@resourceTypeID))
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('batchMoveTransaction',@resourceTypeID))
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
select @resourceTypeID = dbo.fn_getResourceTypeID('Admin')
SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('viewAccountingReports',@resourceTypeID))
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
GO

-- delete invoiceManage function - no longer needed now that we have granular controls on invoice actions
declare @functionID int
select @functionID = functionID from cms_siteResourceFunctions where functionName = 'invoiceManage'
delete from dbo.cms_siteResourceRightsCache where functionID = @functionID
delete from dbo.cache_perms_siteResourceFunctionRightPrints where functionID = @functionID
delete from dbo.cms_siteResourceRoleFunctions where resourceTypeFunctionID in (select resourceTypeFunctionID from dbo.cms_siteResourceTypeFunctions where functionID = @functionID)
delete from dbo.admin_functionsDeterminingNav where resourceTypeFunctionID in (select resourceTypeFunctionID from dbo.cms_siteResourceTypeFunctions where functionID = @functionID)
delete from dbo.cms_siteResourceTypeFunctions where functionID = @functionID
delete from cms_siteResourceFunctions where functionID = @functionID
GO

-- set the Manage Invoices nav to look for specific functions
declare @resourceTypeID int, @toolTypeID int, @resourceTypeFunctionID int, @navigationID int
select @resourceTypeID = dbo.fn_getResourceTypeID('InvoiceAdmin')
select @toolTypeID = toolTypeID from dbo.admin_toolTypes where toolType = 'InvoiceAdmin'
select @navigationID = navigationID from dbo.admin_navigation where navName = 'Manage Invoices'
SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('invoiceCreate',@resourceTypeID))
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('invoiceEdit',@resourceTypeID))
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('invoiceClose',@resourceTypeID))
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('invoiceDownload',@resourceTypeID))
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('invoiceEmail',@resourceTypeID))
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('invoiceMoveTransaction',@resourceTypeID))
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
GO

-- accounting reports should be based on viewAccountingReports only
declare @resourceTypeID int, @resourceTypeFunctionID int, @toolTypeID int, @navigationID int
select @resourceTypeID = dbo.fn_getResourceTypeID('Admin')
SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('viewAccountingReports',@resourceTypeID))
select @toolTypeID = toolTypeID from dbo.admin_toolTypes where toolType = 'ReceiptsReport'
select @navigationID = navigationID from dbo.admin_navigation where navName = 'Allocation Summary'
	delete from dbo.admin_functionsDeterminingNav where navigationID = @navigationID
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
select @toolTypeID = toolTypeID from dbo.admin_toolTypes where toolType = 'CreditBalancesReport'
select @navigationID = navigationID from dbo.admin_navigation where navName = 'Credit Balances'
	delete from dbo.admin_functionsDeterminingNav where navigationID = @navigationID
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
select @toolTypeID = toolTypeID from dbo.admin_toolTypes where toolType = 'SalesReport'
select @navigationID = navigationID from dbo.admin_navigation where navName = 'Gross Sales'
	delete from dbo.admin_functionsDeterminingNav where navigationID = @navigationID
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
select @toolTypeID = toolTypeID from dbo.admin_toolTypes where toolType = 'PaymentReport'
select @navigationID = navigationID from dbo.admin_navigation where navName = 'Payments'
	delete from dbo.admin_functionsDeterminingNav where navigationID = @navigationID
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
select @toolTypeID = toolTypeID from dbo.admin_toolTypes where toolType = 'PaymentReport'
select @navigationID = navigationID from dbo.admin_navigation where navName = 'Pending Payments'
	delete from dbo.admin_functionsDeterminingNav where navigationID = @navigationID
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
select @toolTypeID = toolTypeID from dbo.admin_toolTypes where toolType = 'ReconciliationReport'
select @navigationID = navigationID from dbo.admin_navigation where navName = 'Reconciliations'
	delete from dbo.admin_functionsDeterminingNav where navigationID = @navigationID
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
select @toolTypeID = toolTypeID from dbo.admin_toolTypes where toolType = 'TransactionsReport'
select @navigationID = navigationID from dbo.admin_navigation where navName = 'Transactions'
	delete from dbo.admin_functionsDeterminingNav where navigationID = @navigationID
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
select @toolTypeID = toolTypeID from dbo.admin_toolTypes where toolType = 'TransactionsReport'
select @navigationID = navigationID from dbo.admin_navigation where navName = 'Voided Transactions'
	delete from dbo.admin_functionsDeterminingNav where navigationID = @navigationID
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
select @toolTypeID = toolTypeID from dbo.admin_toolTypes where toolType = 'BatchSummaryReport'
select @navigationID = navigationID from dbo.admin_navigation where navName = 'Batch Summary'
	delete from dbo.admin_functionsDeterminingNav where navigationID = @navigationID
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
select @toolTypeID = toolTypeID from dbo.admin_toolTypes where toolType = 'ARReport'
select @navigationID = navigationID from dbo.admin_navigation where navName = 'Accounts Receivable'
	delete from dbo.admin_functionsDeterminingNav where navigationID = @navigationID
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
select @toolTypeID = toolTypeID from dbo.admin_toolTypes where toolType = 'InvoiceAgingReport'
select @navigationID = navigationID from dbo.admin_navigation where navName = 'Invoice Aging'
	delete from dbo.admin_functionsDeterminingNav where navigationID = @navigationID
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
select @toolTypeID = toolTypeID from dbo.admin_toolTypes where toolType = 'WriteOffReport'
select @navigationID = navigationID from dbo.admin_navigation where navName = 'Write-Offs'
	delete from dbo.admin_functionsDeterminingNav where navigationID = @navigationID
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
select @toolTypeID = toolTypeID from dbo.admin_toolTypes where toolType = 'WriteOffReport'
select @navigationID = navigationID from dbo.admin_navigation where navName = 'Negative Write-Offs'
	delete from dbo.admin_functionsDeterminingNav where navigationID = @navigationID
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
select @toolTypeID = toolTypeID from dbo.admin_toolTypes where toolType = 'MTDYTDPerformanceReport'
select @navigationID = navigationID from dbo.admin_navigation where navName = 'MTD/YTD Performance'
	delete from dbo.admin_functionsDeterminingNav where navigationID = @navigationID
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
select @toolTypeID = toolTypeID from dbo.admin_toolTypes where toolType = 'AGRGivingHistory'
select @navigationID = navigationID from dbo.admin_navigation where navName = 'Giving History with Billing and Payments by Member or Firm'
	delete from dbo.admin_functionsDeterminingNav where navigationID = @navigationID
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
select @toolTypeID = toolTypeID from dbo.admin_toolTypes where toolType = 'AGRAllocationSummary'
select @navigationID = navigationID from dbo.admin_navigation where navName = 'Payment Summary by Member, Firm or Revenue Account'
	delete from dbo.admin_functionsDeterminingNav where navigationID = @navigationID
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
select @toolTypeID = toolTypeID from dbo.admin_toolTypes where toolType = 'AGRAggregateAllocationSummary'
select @navigationID = navigationID from dbo.admin_navigation where navName = 'Aggregate Payment Summary'
	delete from dbo.admin_functionsDeterminingNav where navigationID = @navigationID
	EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID, @toolTypeID, @navigationID
GO
