-- Hotfix-8451904 FS1 -> FS2 Converter needs to convert widget Instances

ALTER PROC [dbo].[migrate_FS1_to_FS2]
@fileShareID int,
@siteID int
AS

-- use this stored procedure to convert a FS1 to FS2.  This stored procedure can be run once.  
-- WARNING:  Once this this stored procedure is run you will not be able to recover the FS1.

declare @errCode int, @rc int, @testFileShareID int, @testAppType varchar(100), @fs2TypeName varchar(25),
		@newApplicationTypeID int, @newResourceTypeID int, @fsSRID int, @applicationInstanceID int,
		@newCategoryTreeID int, @appCreatedSectionResourceTypeID int, @rootSectionID int, @memberID int,
		@defaultCategoryID int
declare @documentSectionName varchar(50)

SET @errCode = 0
SET @fs2TypeName = 'FileShare2'

select @newApplicationTypeID = applicationTypeID from cms_applicationTypes where applicationTypeName = @fs2TypeName
	IF @@ERROR <> 0 OR @@ROWCOUNT = 0 BEGIN
		SET @errCode = 900
		GOTO on_done
	END
select @newResourceTypeID = resourceTypeID from cms_siteResourceTypes where resourceType = @fs2TypeName
	IF @@ERROR <> 0 OR @@ROWCOUNT = 0 BEGIN
		SET @errCode = 901
		GOTO on_done
	END
select @appCreatedSectionResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedSection')
	IF @@ERROR <> 0 OR @@ROWCOUNT = 0 BEGIN
		SET @errCode = 905
		GOTO on_done
	END
select @memberID=activeMemberID from ams_members where orgid = 1 and lastname like 'System'
	IF @@ERROR <> 0 OR @@ROWCOUNT = 0 BEGIN
		SET @errCode = 906
		GOTO on_done
	END

select @testFileShareID = fileshareID, @testAppType = at.applicationTypeName, @fsSRID = ai.siteResourceID,
	@applicationInstanceID = ai.applicationInstanceID, @rootSectionID = rootSectionID
from fs_fileshare fs
inner join cms_applicationInstances ai on ai.applicationInstanceID = fs.applicationInstanceID
	and ai.siteID = @siteID
	and fs.fileShareID = @fileShareID
inner join cms_applicationTypes at on at.applicationTypeID = ai.applicationTypeID
	and at.applicationTypeName in ('FileShare', @fs2TypeName)
	IF @@ERROR <> 0 OR @@ROWCOUNT = 0 BEGIN
		SET @errCode = 902
		GOTO on_done
	END

print 'App Type Detected: ' + @testAppType
IF @testAppType = 'FileShare2' BEGIN
	SET @errCode = 903
	GOTO on_done
END

BEGIN TRAN FS1

print 'Root Section: ' + convert(varchar(20), @rootSectionID)
-- Update the resourceType
print 'site Resource ID: ' + convert(varchar(20), @fsSRID)

-- Update the applicationTypeID 
print 'New AppTypeID: ' + convert(varchar(20), @newApplicationTypeID)
update cms_applicationInstances set applicationTypeID=@newApplicationTypeID where applicationInstanceID = @applicationInstanceID

-- Update the resourceType
print 'New ResTypeID: ' + convert(varchar(20), @newResourceTypeID)
update cms_siteResources set resourceTypeID=@newResourceTypeID where siteResourceID = @fsSRID

-- Update document section name
select @documentSectionName = 'FileShare2 ' + cast(@applicationInstanceID as varchar(8))
print 'New Root Section Name: ' + @documentSectionName
update cms_pageSections set sectionName=@documentSectionName, sectionCode=@documentSectionName where sectionID = @rootSectionID


-- Create Category Tree
exec @rc = cms_createCategoryTreeForFileShare2 @siteID = @siteID,@fs2SiteResourceID = @fsSRID,
												@categoryTreeName = 'Default', @categoryTreeID = @newCategoryTreeID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 BEGIN
		SET @errCode = 904
		GOTO on_done
	END	
print 'New Category Tree: '  + convert(varchar(20), @newCategoryTreeID)

-- Reuse the default category that is created.
select @defaultCategoryID = categoryID from cms_categories where categoryTreeID = @newCategoryTreeID and categoryName = 'Sample'
update cms_categories set categoryName='Fileshare Home', categoryDesc='Fileshare Home' where categoryTreeID = @newCategoryTreeID and categoryID = @defaultCategoryID

-- Map FS1 sections to category tree
declare @tblSections table (sectionID int, sectionName varchar(200), parentSectionID int, siteResourceID int, newCategoryID int)
insert into @tblSections 
select sectionID, sectionName, parentSectionID, sect.siteResourceID, 0 as newCategoryID
from dbo.fn_getRecursiveSections(@siteID,@rootSectionID,DEFAULT) as sect
inner join dbo.cms_siteResources as sr on sr.siteResourceID = sect.siteResourceID
INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'


declare @currentSectionID int,  @thisSectionName varchar(200), @thisParentSectionID int, @thisParentCategoryID int,
		@parentCategoryID int
select @currentSectionID = min(sectionID) from @tblSections where sectionID <> @rootSectionID
while @currentSectionID is not null begin
	select @thisSectionName = sectionName, 
		   @thisParentSectionID = parentSectionID
	from @tblSections 
	where sectionID = @currentSectionID

	print 'Create category: ' + @thisSectionName
	if @thisParentSectionID = @rootSectionID begin
		print 'Root category'
		exec [cms_createCategory]
			@categoryTreeID=@newCategoryTreeID,
			@categoryName=@thisSectionName,
			@categoryDesc=@thisSectionName,
			@categoryCode='',
			@parentCategoryID=NULL,
			@contributorMemberID=@memberID,
			@categoryID=@parentCategoryID OUTPUT
	
		update @tblSections set newCategoryID = @parentCategoryID where sectionID = @currentSectionID
	end
	else begin
		print 'Sub category'
		select @thisParentCategoryID = newCategoryID
		from @tblSections 
		where sectionID = @thisParentSectionID
		exec [cms_createCategory]
			@categoryTreeID=@newCategoryTreeID,
			@categoryName=@thisSectionName,
			@categoryDesc=@thisSectionName,
			@categoryCode='',
			@parentCategoryID=@thisParentCategoryID,
			@contributorMemberID=@memberID,
			@categoryID=@parentCategoryID OUTPUT
	
		update @tblSections set newCategoryID = @parentCategoryID where sectionID = @currentSectionID

	end
	
	select @currentSectionID = min(sectionID) from @tblSections where sectionID > @currentSectionID
END

-- Just in case any documents stored on root section.
update @tblSections set newCategoryID = @defaultCategoryID where sectionID = @rootSectionID

-- document section
declare @tbldocs table (documentID int, siteResourceID int, sectionName varchar(200), docCatID int, newSectionID int)

insert into @tbldocs (documentID, siteResourceID, sectionName, newSectionID, docCatID)
SELECT d.documentID, d.siteResourceID, tmps.sectionName, tmps.sectionID, sec.newCategoryID
FROM dbo.cms_documents AS d 
INNER JOIN dbo.cms_documentLanguages dl on d.documentID = dl.documentID
INNER JOIN dbo.cms_documentVersions v on dl.documentLanguageID = v.documentLanguageID AND v.isActive = 1
INNER JOIN (
	select sect.sectionID, sect.thePathExpanded, sect.sectionName, sect.parentSectionID
	from dbo.fn_getRecursiveSections(@siteID,@rootSectionID,DEFAULT) as sect
	inner join dbo.cms_siteResources as sr on sr.siteResourceID = sect.siteResourceID
	INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
) as tmps on tmps.sectionID = d.sectionID
inner join @tblSections sec on  sec.sectionID = tmps.sectionID

-- set section to root of fileshare
update d 
set d.sectionID = @rootSectionID 
from dbo.cms_documents as d 
inner join @tbldocs as tmp on tmp.documentID = d.documentID

-- link to categories
insert into dbo.cms_categorySiteResources (categoryID, siteResourceID)
select tmp.docCatID, d.siteResourceID
from @tbldocs as tmp
inner join dbo.cms_documents as d on d.documentID = tmp.documentID
where tmp.docCatID is not null

-- Delete all other sections
update sr
	set siteResourceStatusID = 3
from dbo.fn_getRecursiveSections(@siteID,@rootSectionID,DEFAULT) as sect
inner join dbo.cms_siteResources as sr on sr.siteResourceID = sect.siteResourceID
INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
where sect.sectionID <> @rootSectionID

-- Convert the widget instance to FS2 for recent file support.
update sr
	set resourceTypeID = @newResourceTypeID
from cms_applicationInstances ai
inner join dbo.cms_applicationWidgetInstances wi 
	on wi.applicationInstanceID = ai.applicationInstanceID
	and ai.applicationInstanceID = @applicationInstanceID
	and ai.siteID=@siteID
	and ai.applicationTypeID = @newApplicationTypeID
inner join dbo.cms_applicationWidgetTypes wt 
	on wt.applicationWidgetTypeID = wi.applicationWidgetTypeID
	and wt.applicationWidgetTypeName = 'recentFiles'
inner join dbo.cms_siteResources sr 
	on sr.siteResourceID = wi.siteResourceID

COMMIT TRAN FS1

on_done:
	IF @errCode = 900
		print 'Migration stopped. Application Type ID could not be found for ' + @fs2TypeName + '.'
	IF @errCode = 901
		print 'Migration stopped. Resource Type ID could not be found for ' + @fs2TypeName + '.'
	IF @errCode = 902
		print 'Migration stopped. Fileshare(' + convert(varchar(20), @fileshareID) + ') could not be found.'
	IF @errCode = 903
		print 'Migration stopped. Fileshare(' + convert(varchar(20), @fileshareID) + ') is not a Fileshare1.'
	IF @errCode = 904
		print 'Migration stopped. Could not create default category tree.'
	IF @errCode = 905
		print 'Migration stopped. Failed to retrieve Section Resource Type ID for ' + @fs2TypeName + '.'
	IF @errCode = 906
		print 'Migration stopped. MemberCentral System account not found.'

	IF @errCode >= 904
		ROLLBACK TRAN FS1
	ELSE BEGIN
		-- Repopulate rights cache
		exec dbo.cms_populateSiteResourceRightsCache @siteID=@siteID
	END
	
	print 'WARNING: Search buckets are not automatically updated because bucketSettings is an XML column'
	print 'Migration from FS1 to FS2 finished.'

GO


--- Update all the widgets which were recently converted to fileshare2
update sr
	set resourceTypeID = 106
from cms_applicationInstances ai
inner join dbo.cms_applicationWidgetInstances wi 
	on wi.applicationInstanceID = ai.applicationInstanceID
	and ai.siteID=154
	and ai.applicationTypeID = 49
inner join dbo.cms_applicationWidgetTypes wt 
	on wt.applicationWidgetTypeID = wi.applicationWidgetTypeID
	and wt.applicationWidgetTypeName = 'recentFiles'
inner join dbo.cms_siteResources sr 
	on sr.siteResourceID = wi.siteResourceID

