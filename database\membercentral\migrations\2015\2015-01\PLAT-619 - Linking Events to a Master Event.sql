/* ------------------------------------------------------------------------------------------------- */ /*
PLAT-619 - Linking Events to a Master Event.sql

In this script, the following tasks are performed:

1) create table ev_subEvents
2) Create relationship FK_ev_events_ev_subEventsParent
3) Create relationship FK_ev_events_ev_subEventsChild
4) ALTER PROCEDURE ev_createEvent
5) CREATE PROC ev_getParentMetaByEventID
6) ALTER PROCEDURE ev_getEvent
7) ALTER PROC ev_copyEvent

*/ /* ------------------------------------------------------------------------------------------------- */

use membercentral
GO

IF  EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME ='FK_ev_events_ev_subEventsParent')
BEGIN
	print 'Relationship FK_ev_events_ev_subEventsParent Exists! Deleting...'
	ALTER TABLE [ev_events] DROP CONSTRAINT [FK_ev_events_ev_subEventsParent]
END
GO

IF  EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME ='FK_ev_events_ev_subEventsChild')
BEGIN
	print 'Relationship FK_ev_events_ev_subEventsChild Exists! Deleting...'
	ALTER TABLE [ev_events] DROP CONSTRAINT [FK_ev_events_ev_subEventsChild]
END
GO

IF OBJECT_ID('ev_subEvents') IS NOT NULL 
	EXEC('DROP TABLE  ev_subEvents')
GO

create table ev_subEvents(
	subEventID	int IDENTITY(1,1) NOT NULL PRIMARY KEY CLUSTERED,
	parentEventID	int	NOT NULL,
	eventID	int	NOT NULL
)
GO

print 'Creating relationship FK_ev_events_ev_subEventsParent...'
ALTER TABLE [ev_events]  WITH NOCHECK ADD  CONSTRAINT [FK_ev_events_ev_subEventsParent] FOREIGN KEY([parentEventID])
REFERENCES [dbo].[ev_events] ([eventID])
GO

print 'Creating relationship FK_ev_events_ev_subEventsChild...'
ALTER TABLE [ev_events]  WITH NOCHECK ADD  CONSTRAINT [FK_ev_events_ev_subEventsChild] FOREIGN KEY([eventID])
REFERENCES [dbo].[ev_events] ([eventID])
GO

/* ------------------------------------------------------------------------------------------------- */

ALTER PROCEDURE [dbo].[ev_createEvent] 
@siteID int,
@calendarID int,
@eventTypeID int,
@enteredByMemberID int,
@lockTimeZoneID int,
@isAllDayEvent bit,
@altRegistrationURL varchar(300),
@status char(1),
@reportCode varchar(15),
@hiddenFromCalendar bit,
@emailContactContent bit,
@emailLocationContent bit,
@emailCancelContent	bit,
@emailTravelContent	bit,
@parentEventID int = NULL,
@eventID int OUTPUT

AS

declare @rc int
select @eventID = null

BEGIN TRAN

declare @appCreatedContentResourceTypeID int, @activeSiteResourceStatusID int, @defaultLanguageID int
declare @eventSiteResourceID int, @eventResourceTypeID int, @eventcontentID int, @eventcontentSiteResourceID int
declare @locationContentID int, @travelcontentid int, @contactcontentid int, @cancellationPolicyContentID int, @informationContentID int
declare @defaultGLAccountID int
select @eventResourceTypeID = dbo.fn_getResourceTypeId('Event')
select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')
select @activeSiteResourceStatusID = dbo.fn_getResourceStatusId('Active')
select @defaultLanguageID = defaultLanguageID from dbo.sites where siteID = @siteID

-- create a resourceID for the event
exec dbo.cms_createSiteResource
	@resourceTypeID = @eventResourceTypeID,
	@siteResourceStatusID = @activeSiteResourceStatusID,
	@siteID = @siteid,
	@isVisible = 1,
	@parentSiteResourceID = null,
	@siteResourceID   = @eventSiteResourceID OUTPUT

EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@defaultLanguageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='',
	@memberID=@enteredByMemberID,
	@contentID=@eventcontentID OUTPUT, 
	@siteResourceID=@eventcontentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- create eventcontentid
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@defaultLanguageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', 
	@memberID=@enteredByMemberID,
	@contentID=@eventcontentID OUTPUT, 
	@siteResourceID=@eventcontentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- create locationcontentid
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@defaultLanguageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', 
	@memberID=@enteredByMemberID,
	@contentID=@locationContentID OUTPUT, 
	@siteResourceID=@eventcontentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- create travelcontentid
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@defaultLanguageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', 
	@memberID=@enteredByMemberID,
	@contentID=@travelcontentid OUTPUT, 
	@siteResourceID=@eventcontentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- create contactcontentid
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@defaultLanguageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', 
	@memberID=@enteredByMemberID,
	@contentID=@contactcontentid OUTPUT, 
	@siteResourceID=@eventcontentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- create cancelcontentid
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@defaultLanguageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', 
	@memberID=@enteredByMemberID,
	@contentID=@cancellationPolicyContentID OUTPUT, 
	@siteResourceID=@eventcontentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- create informationcontentid
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@defaultLanguageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', 
	@memberID=@enteredByMemberID,
	@contentID=@informationContentID OUTPUT, 
	@siteResourceID=@eventcontentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- get GLAccountID from home calendar
select @defaultGLAccountID = defaultGLAccountID
from dbo.ev_calendars
where calendarID = @calendarID
	IF @@ERROR <> 0 OR @defaultGLAccountID is null GOTO on_error

-- add event
insert into dbo.ev_events (
	eventTypeID, siteID, siteResourceID, eventContentID, locationContentID, travelContentId, 
	contactContentID, cancellationPolicyContentID, enteredByMemberID, lockTimeZoneID, isAllDayEvent, 
	altRegistrationURL, GLAccountID, [status], reportCode, informationContentID, emailContactContent, 
	emailLocationContent, emailCancelContent, emailTravelContent, hiddenFromCalendar)
values (@eventTypeID, @siteID, @eventSiteResourceID, @eventContentID, @locationContentID, @travelContentId, 
	@contactContentID, @cancellationPolicyContentID, @enteredByMemberID, @lockTimeZoneID, @isAllDayEvent, 
	@altRegistrationURL, @defaultGLAccountID, @status, nullif(@reportCode,''), @informationContentID, @emailContactContent, 
	@emailLocationContent, @emailCancelContent, @emailTravelContent, @hiddenFromCalendar)
	IF @@ERROR <> 0 GOTO on_error
	select @eventID = SCOPE_IDENTITY()

-- add parent - children relationship
if @parentEventID is not null and @parentEventID > 0
	insert into ev_subEvents (
		parentEventID,
		eventID
	)
	values(
		@parentEventID,
		@eventID
	)

-- add permissions for event management
declare @resourceRightID int, @calApplicationSiteResourceID int
declare @eventfunctionid int, @inheritedRightsFunctionID int, @calApplicationInstanceID int
select @calApplicationSiteResourceID = ai.siteResourceID, @calApplicationInstanceID = c.applicationInstanceID
	FROM dbo.ev_calendars AS c 
	INNER JOIN dbo.cms_applicationInstances AS ai ON c.applicationInstanceID = ai.applicationInstanceID
	WHERE c.calendarID = @calendarID
SELECT @eventfunctionid = dbo.fn_getResourceFunctionID('EditEvent',dbo.fn_getResourceTypeID('Event'))
SELECT @inheritedRightsFunctionID = dbo.fn_getResourceFunctionID('EditEventByDefault',dbo.fn_getResourceTypeID('Events'))
EXEC @rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@eventSiteResourceID, @functionID=@eventfunctionid, @inheritedRightsResourceID=@calApplicationSiteResourceID, @inheritedRightsFunctionID=@inheritedRightsFunctionID, @resourceRightID=@resourceRightID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
SELECT @eventfunctionid = dbo.fn_getResourceFunctionID('DeleteEvent',dbo.fn_getResourceTypeID('Event'))
SELECT @inheritedRightsFunctionID = dbo.fn_getResourceFunctionID('DeleteEventByDefault',dbo.fn_getResourceTypeID('Events'))
EXEC @rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@eventSiteResourceID, @functionID=@eventfunctionid, @inheritedRightsResourceID=@calApplicationSiteResourceID, @inheritedRightsFunctionID=@inheritedRightsFunctionID, @resourceRightID=@resourceRightID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
SELECT @eventfunctionid = dbo.fn_getResourceFunctionID('ViewRegistrants',dbo.fn_getResourceTypeID('Event'))
SELECT @inheritedRightsFunctionID = dbo.fn_getResourceFunctionID('ViewRegistrantsByDefault',dbo.fn_getResourceTypeID('Events'))
EXEC @rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@eventSiteResourceID, @functionID=@eventfunctionid, @inheritedRightsResourceID=@calApplicationSiteResourceID, @inheritedRightsFunctionID=@inheritedRightsFunctionID, @resourceRightID=@resourceRightID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
SELECT @eventfunctionid = dbo.fn_getResourceFunctionID('EditRegistrants',dbo.fn_getResourceTypeID('Event'))
SELECT @inheritedRightsFunctionID = dbo.fn_getResourceFunctionID('EditRegistrantsByDefault',dbo.fn_getResourceTypeID('Events'))
EXEC @rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@eventSiteResourceID, @functionID=@eventfunctionid, @inheritedRightsResourceID=@calApplicationSiteResourceID, @inheritedRightsFunctionID=@inheritedRightsFunctionID, @resourceRightID=@resourceRightID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
SELECT @eventfunctionid = dbo.fn_getResourceFunctionID('ManageFreeRates',dbo.fn_getResourceTypeID('Event'))
SELECT @inheritedRightsFunctionID = dbo.fn_getResourceFunctionID('ManageFreeRatesByDefault',dbo.fn_getResourceTypeID('Events'))
EXEC @rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@eventSiteResourceID, @functionID=@eventfunctionid, @inheritedRightsResourceID=@calApplicationSiteResourceID, @inheritedRightsFunctionID=@inheritedRightsFunctionID, @resourceRightID=@resourceRightID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
SELECT @eventfunctionid = dbo.fn_getResourceFunctionID('ManagePaidRates',dbo.fn_getResourceTypeID('Event'))
SELECT @inheritedRightsFunctionID = dbo.fn_getResourceFunctionID('ManagePaidRatesByDefault',dbo.fn_getResourceTypeID('Events'))
EXEC @rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@eventSiteResourceID, @functionID=@eventfunctionid, @inheritedRightsResourceID=@calApplicationSiteResourceID, @inheritedRightsFunctionID=@inheritedRightsFunctionID, @resourceRightID=@resourceRightID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- add to its main calendar
EXEC @rc = dbo.ev_createCalendarEvent @calendarID=@calendarID, @sourceCalendarID=@calendarID, @sourceCategoryID=null, @sourceEventID=@eventID, @ovCategoryID=null
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error


-- get applicationTypeID
DECLARE @applicationTypeID int
select @applicationTypeID = dbo.fn_getApplicationTypeIDFromName('Events')

-- create activity log entry
EXEC platformstats.dbo.act_recordLog @memberID=@enteredByMemberID, @activityType='post', 
	@applicationTypeID=@applicationTypeID, @applicationInstanceID=@calApplicationInstanceID,
	@supportSiteResourceID=@eventSiteResourceID, @supportMemberID=null, @supportMessage=null
	IF @@ERROR <> 0 GOTO on_error


-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @eventID = 0
	RETURN -1

GO

/* ------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('ev_getParentMetaByEventID') IS NOT NULL
	DROP PROCEDURE ev_getParentMetaByEventID
GO

CREATE PROC [dbo].[ev_getParentMetaByEventID]
@eventID int,
@languageID int

AS

select ce.calendarID, ev.eventID, ev.eventTypeID, et.eventType, ev.siteID, ev.enteredByMemberID, ev.isAllDayEvent, 
	ev.lockTimeZoneID, ev.GLAccountID, ev.altRegistrationURL, ev.status, ev.reportCode, ev.internalNotes, 
	ev.emailContactContent, ev.emailLocationContent, ev.emailCancelContent, ev.emailTravelContent,
	ai.applicationInstanceID as calendarApplicationInstanceID, ai.applicationInstanceName as calendarName,
	c.showAddCalendarLinks, 
	ev.eventContentID, eventcontent.contentTitle as eventContentTitle, eventcontent.rawContent as eventContent,
	ev.locationContentID, locationcontent.contentTitle as locationContentTitle, locationcontent.rawContent as locationContent,
	ev.travelContentID, travelcontent.contentTitle as travelContentTitle, travelcontent.rawContent as travelContent,
	ev.contactContentID, contactcontent.contentTitle as contactContentTitle, contactcontent.rawContent as contactContent,
	ev.cancellationPolicyContentID as cancelContentID, cancelcontent.contentTitle as cancelContentTitle, cancelcontent.rawContent as cancelContent,
	ev.informationContentID, informationcontent.contentTitle as informationContentTitle, informationcontent.rawContent as informationContent,
	ev.hiddenFromCalendar
from dbo.ev_events as ev
INNER JOIN dbo.ev_eventTypes AS et ON ev.eventTypeID = et.eventTypeID
INNER JOIN dbo.ev_calendarEvents as ce on ce.sourceEventID = ev.eventID and ce.calendarID = ce.sourceCalendarID
INNER JOIN dbo.ev_calendars as c on c.calendarID = ce.calendarID
inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = c.applicationInstanceID
cross apply dbo.fn_getContent(ev.eventcontentID,@languageID) as eventcontent
cross apply dbo.fn_getContent(ev.locationcontentID,@languageID) as locationcontent
cross apply dbo.fn_getContent(ev.travelcontentID,@languageID) as travelcontent
cross apply dbo.fn_getContent(ev.contactcontentID,@languageID) as contactcontent
cross apply dbo.fn_getContent(ev.cancellationPolicycontentID,@languageID) as cancelcontent
cross apply dbo.fn_getContent(ev.informationContentID,@languageID) as informationcontent
inner join dbo.ev_subEvents as subEvent on 
	subEvent.parentEventID = ev.eventID 	
	and subEvent.eventID = @eventID
	and ev.status <> 'D'

RETURN 0
GO

/* ------------------------------------------------------------------------------------------------- */

ALTER PROCEDURE [dbo].[ev_getEvent]
@siteid int,
@eventID int,
@languageID int

AS

-- check event. event must be linked to a calendar for the site.
if (dbo.fn_canSiteViewEvent(@siteid,@eventID) = 0)
	SELECT @eventID = 0

-- event metadata
EXEC dbo.ev_getMetaByEventID @eventID=@eventID, @languageID=@languageID

-- event sponsors
EXEC dbo.ev_getSponsorsByEventID @eventID=@eventID, @languageID=@languageID

-- event categories
EXEC dbo.ev_getCategoriesByEventID @eventID=@eventID

-- event times
EXEC dbo.ev_getTimesByEventID @eventID=@eventID

-- event registration metadata
EXEC dbo.ev_getRegistrationMetaByEventID @eventID=@eventID, @languageID=@languageID

-- parent event metadata
EXEC dbo.ev_getParentMetaByEventID @eventID=@eventID, @languageID=@languageID

RETURN 0
GO

/* ------------------------------------------------------------------------------------------------- */

ALTER PROC [dbo].[ev_copyEvent]
@eventid int,
@copiedByMemberID int,
@newEventID int OUTPUT

AS

DECLARE @rc int
DECLARE @siteID int, @eventTypeID int, @lockTimeZoneID int, @isAllDayEvent bit, @hiddenFromCalendar bit,
	@altRegistrationURL varchar(300), @GLAccountID int, @calendarID int, @status char(1), @reportCode varchar(15),
	@internalNotes varchar(max)
DECLARE @eventContentID int, @contactContentID int, @locationContentID int, @cancelContentID int, @travelContentID int, @informationContentID int
DECLARE @neweventContentID int, @newcontactContentID int, @newlocationContentID int, @newcancelContentID int, @newtravelContentID int, @newinformationContentID int
DECLARE @languageID int, @isSSL bit, @isHTML bit, @contentTitle varchar(200), @contentDesc varchar(400), @rawcontent varchar(max)
DECLARE @emailContactContent bit, @emailLocationContent bit, @emailCancelContent bit, @emailTravelContent	bit
DECLARE @registrationID int, @registrationTypeID int, @startDate datetime, @endDate datetime, @registrantCap int,
	@ReplyToEmail varchar(200), @notifyEmail varchar(200), @isPriceBasedOnActual bit, @bulkCountByRate bit, @newregistrationID int
DECLARE @expirationContentID int, @registrantCapContentID int
DECLARE @newexpirationContentID int, @newregistrantCapContentID int
DECLARE @minofferingID int, @newofferingID int
DECLARE @minRateId int, @rateGroupingID int, @rateGLAccountID int, @rateName varchar(100),
	@rate money, @ratestartDate datetime, @rateendDate datetime, @newRateID int, @newRatesiteResourceID int,
	@ratereportCode varchar(15), @rateQty int
DECLARE @minBulkRateID int, @bulkrate money, @bulksiteResourceID int, @bulkrateqty int, @newBulkRateID int,
	@newBulkRatesiteResourceID int, @bulkresourceRightID int
DECLARE @siteResourceID int, @newrateGroupingID int
DECLARE @srr_rightsID int, @srr_roleid int, @srr_functionID int, @srr_groupid int, @srr_memberid int, @srr_include bit, @srr_inheritedRightsResourceID int, @srr_inheritedRightsFunctionID int, @resourceRightID int
DECLARE @minCustomID int, @newCustomID int, @showCredit bit
DECLARE @isOnlineMeeting bit, @onlineEmbedCode varchar(max), @onlineEmbedOverrideLink varchar(400), @onlineEnterStartTime datetime, @onlineEnterEndTime datetime
SELECT @newEventID = null

BEGIN TRAN

-- get the event we are copying
SELECT @siteID=siteID, @eventTypeID=eventTypeID, 
	@lockTimeZoneID=lockTimeZoneID, @isAllDayEvent=isAllDayEvent, @altRegistrationURL=altRegistrationURL,
	@GLAccountID=GLAccountID, @status=status, @reportCode=reportcode, @internalNotes=internalNotes,
	@emailContactContent = emailContactContent, @emailLocationContent = emailLocationContent,
	@emailCancelContent = emailCancelContent, @emailTravelContent = emailTravelContent,
	@hiddenFromCalendar = hiddenFromCalendar
	FROM dbo.ev_events
	WHERE eventID = @eventID
	IF @@ERROR <> 0 GOTO on_error
SELECT TOP 1 @calendarID = calendarID
	FROM dbo.ev_calendarEvents
	WHERE calendarID = sourceCalendarID
	AND sourceEventID = @eventID
	IF @@ERROR <> 0 GOTO on_error

-- create event
EXEC @rc = dbo.ev_createEvent @siteID=@siteID, @calendarID=@calendarID, @eventTypeID=@eventTypeID, 
	@enteredByMemberID=@copiedByMemberID, @lockTimeZoneID=@lockTimeZoneID, @isAllDayEvent=@isAllDayEvent, 
	@altRegistrationURL=@altRegistrationURL, @status=@status, @reportCode=@reportCode, @hiddenFromCalendar=@hiddenFromCalendar,
	@emailContactContent=@emailContactContent, @emailLocationContent=@emailLocationContent,
	@emailCancelContent=@emailCancelContent, @emailTravelContent=@emailTravelContent,
	@eventID=@newEventID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

update dbo.ev_events
set GLAccountID = @GLAccountID,
	internalNotes = @internalNotes
where eventID = @newEventID
	IF @@ERROR <> 0 GOTO on_error

select @neweventContentID=eventContentID, @newcontactContentID=contactContentID, @newlocationContentID=locationContentID,
	@newcancelContentID=cancellationPolicyContentID, @newtravelContentID=travelContentID, @newinformationContentID=informationContentID
FROM dbo.ev_events
where eventID = @newEventID
	IF @@ERROR <> 0 GOTO on_error

select @eventContentID=eventContentID, @contactContentID=contactContentID, @locationContentID=locationContentID,
	@cancelContentID=cancellationPolicyContentID, @travelContentID=travelContentID, @informationContentID=informationContentID
FROM dbo.ev_events
where eventID = @EventID
	IF @@ERROR <> 0 GOTO on_error

-- make event inactive
UPDATE dbo.ev_events
SET [status] = 'I'
WHERE eventID = @newEventID
	IF @@ERROR <> 0 GOTO on_error

-- copy sub-event links
insert into ev_subEvents
select parentEventID,@newEventID
from ev_subEvents
where eventid = @eventid

-- copy event category
INSERT INTO dbo.ev_eventcategories (eventID, categoryID)
select @newEventID, categoryID
from dbo.ev_eventcategories
where eventID = @eventID
	IF @@ERROR <> 0 GOTO on_error

-- copy event times
INSERT INTO dbo.ev_Times (eventid, timeZoneID, startTime, endTime)
select @newEventID, timeZoneID, startTime, endTime
from dbo.ev_times
where eventid = @eventID
	IF @@ERROR <> 0 GOTO on_error

-- copy content objects
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle='Copy of ' + contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@eventContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@neweventContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@contactContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newcontactContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@locationContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newlocationContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@cancelContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newcancelContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@travelContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newtravelContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@informationContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newinformationContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- copy sponsors
insert into dbo.ev_sponsors (eventID, sponsorContentID, sponsorOrder)
select @newEventID, sponsorContentID, sponsorOrder
from dbo.ev_sponsors
where eventID = @eventID
	IF @@ERROR <> 0 GOTO on_error

-- does orig event have registration?
SELECT @registrationID=registrationid, @registrationTypeID=registrationTypeID, @startDate=startdate, 
	@endDate=endDate, @registrantCap=registrantCap, @ReplyToEmail=ReplyToEmail, @notifyEmail=notifyEmail, 
	@isPriceBasedOnActual=isPriceBasedOnActual, @bulkCountByRate=bulkCountByRate, @expirationContentID=expirationContentID, 
	@registrantCapContentID=registrantCapContentID, @isOnlineMeeting=isOnlineMeeting, @onlineEmbedCode=onlineEmbedCode,
	@onlineEmbedOverrideLink=onlineEmbedOverrideLink, @onlineEnterStartTime=onlineEnterStartTime, @onlineEnterEndTime=onlineEnterEndTime,
	@showCredit=showCredit
	from dbo.ev_registration
	where eventID = @eventID
	and [status] <> 'D'
IF @registrationID is not null BEGIN

	-- insert registration
	EXEC @rc = dbo.ev_createRegistration @eventID=@newEventID, @registrationTypeID=@registrationTypeID, 
			@startDate=@startDate, @endDate=@endDate, @registrantCap=@registrantCap, @ReplyToEmail=@ReplyToEmail,
			@notifyEmail=@notifyEmail, @isPriceBasedOnActual=@isPriceBasedOnActual, @bulkCountByRate=@bulkCountByRate,
			@registrationID=@newregistrationID OUTPUT
		IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

	select @newexpirationContentID=expirationContentID, @newregistrantCapContentID=registrantCapContentID
	FROM dbo.ev_registration
	where registrationID = @newregistrationID
		IF @@ERROR <> 0 GOTO on_error

	-- copy content objects
	select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@expirationContentID,1)
	EXEC @rc = dbo.cms_updateContent @contentID=@newexpirationContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
		IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
	select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@registrantCapContentID,1)
	EXEC @rc = dbo.cms_updateContent @contentID=@newregistrantCapContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
		IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

	-- if reg type
	IF @registrationTypeID = 1 BEGIN

		-- other registration fields
		update dbo.ev_registration
		set isOnlineMeeting = @isOnlineMeeting,
			onlineEmbedCode = @onlineEmbedCode,
			onlineEmbedOverrideLink = @onlineEmbedOverrideLink,
			onlineEnterStartTime = @onlineEnterStartTime,
			onlineEnterEndTime = @onlineEnterEndTime,
			showCredit = @showCredit
		where registrationID = @newregistrationID
			IF @@ERROR <> 0 GOTO on_error

		-- merchant profiles
		insert into dbo.ev_registrationMerchantProfiles (registrationID, profileID)
		select @newregistrationID, rmp.profileID
		from dbo.ev_registrationMerchantProfiles as rmp
		inner join dbo.mp_profiles as mp on mp.profileID = rmp.profileID
		inner join dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
		where rmp.registrationID = @registrationID
		and mp.status = 'A'
		and mp.allowPayments = 1
		and g.isActive = 1
			IF @@ERROR <> 0 GOTO on_error

		-- credit offered
		select @minofferingID = min(offeringID) from dbo.crd_offerings where eventID = @eventID
		while @minofferingID is not null BEGIN
			INSERT INTO dbo.crd_offerings (ASID, statusID, ApprovalNum, offeredStartDate, offeredEndDate, completeByDate, isCreditRequired, isIDRequired, isCreditDefaulted, eventID)
			SELECT ASID, statusID, ApprovalNum, offeredStartDate, offeredEndDate, completeByDate, isCreditRequired, isIDRequired, isCreditDefaulted, @newEventID
			FROM dbo.crd_offerings
			WHERE offeringID = @minofferingID
				IF @@ERROR <> 0 GOTO on_error
				SELECT @newofferingID = SCOPE_IDENTITY()

			INSERT INTO dbo.crd_offeringTypes (offeringID, ASTID, creditValue)
			SELECT @newofferingID, ASTID, creditValue
			FROM dbo.crd_offeringTypes
			WHERE offeringID = @minofferingID
				IF @@ERROR <> 0 GOTO on_error

			select @minofferingID = min(offeringID) from dbo.crd_offerings where eventID = @eventID and offeringID > @minofferingID
		END

		-- rate groupings
		insert into dbo.ev_rateGrouping (rateGrouping, registrationID, rateGroupingOrder)
		select rateGrouping, @newregistrationID, rateGroupingOrder
		from dbo.ev_rateGrouping
		where registrationID = @registrationID
			IF @@ERROR <> 0 GOTO on_error

		-- active rates and permissions
		select @minRateId = min(r.rateID) 
			from dbo.ev_rates as r
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			where r.registrationID = @registrationID
			and r.parentRateID is null
		while @minRateID is not null BEGIN
			select @rateGroupingID = null, @rateGLAccountID = null, @rateName = null, 
				@ratereportCode = null, @rate = null, @ratestartDate = null, @rateendDate = null, 
				@siteResourceID = null, @rateqty = null

			select @rateGroupingID=rateGroupingID, @rateGLAccountID=GLAccountID, @rateName=rateName, 
				@ratereportCode=reportCode, @rate=rate, @ratestartDate=startDate, @rateendDate=endDate, 
				@siteResourceID=siteResourceID, @rateqty=bulkQty
			from dbo.ev_rates 
			where rateID = @minRateID

			select @newrateGroupingID = rg1.rateGroupingID
			from dbo.ev_rateGrouping as rg1
			inner join dbo.ev_rateGrouping as rg2 
				on rg2.rateGrouping = rg1.rateGrouping
				and rg1.registrationID = @newregistrationID
				and rg2.registrationID = @registrationID
				and isnull(rg2.rateGroupingID,0) = isnull(@rateGroupingID,0)
			
			IF @rateName is not null and @rate is not null BEGIN
				EXEC @rc = dbo.ev_createRate @registrationID=@newregistrationID,  
					@rateGroupingID=@newrateGroupingID, @GLAccountID=@rateGLAccountID, @rateName=@rateName, 
					@reportCode=@ratereportCode, @rate=@rate, @startDate=@ratestartDate, @endDate=@rateendDate,
					@parentRateID=null, @qty=@rateqty, 
					@rateID=@newRateID OUTPUT, @siteResourceID=@newRatesiteResourceID OUTPUT
					IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

				select @minBulkRateID = min(rateID)
				from dbo.ev_rates
				where parentRateID = @minRateID

				while @minBulkRateID is not null begin
					select @bulkrate=rate, @bulksiteResourceID=siteResourceID, @bulkrateqty=bulkQty
					from dbo.ev_rates 
					where rateID = @minBulkRateID

					EXEC @rc = dbo.ev_createRate @registrationID=@newregistrationID,  
						@rateGroupingID=@newrateGroupingID, @GLAccountID=@rateGLAccountID, @rateName=@rateName, 
						@reportCode=@ratereportCode, @rate=@rate, @startDate=@ratestartDate, @endDate=@rateendDate,
						@parentRateID=@newRateID, @qty=@bulkrateqty, 
						@rateID=@newBulkRateID OUTPUT, @siteResourceID=@newBulkRatesiteResourceID OUTPUT
						IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

					select @minBulkRateID = min(rateID)
					from dbo.ev_rates
					where parentRateID = @minRateID
					and rateID > @minBulkRateID
				end

				-- copy resource rights for this resource		
				SELECT @srr_rightsID = null	
				SELECT @srr_rightsID = min(resourceRightsID) from dbo.cms_siteResourceRights where resourceID = @siteResourceID
				WHILE @srr_rightsID IS NOT NULL BEGIN
					SELECT @srr_roleid=roleID, @srr_functionID=functionID, @srr_groupid=groupID, 
						@srr_memberid=memberID, @srr_include=[include], @srr_inheritedRightsResourceID=inheritedRightsResourceID, 
						@srr_inheritedRightsFunctionID=inheritedRightsFunctionID
					FROM dbo.cms_siteResourceRights
					WHERE resourceRightsID = @srr_rightsID

					EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@newRatesiteResourceID, @include=@srr_include, 
						@functionID=@srr_functionID, @roleID=@srr_roleid, @groupID=@srr_groupid, @memberID=@srr_memberid, 
						@inheritedRightsResourceID=@srr_inheritedRightsResourceID, @inheritedRightsFunctionID=@srr_inheritedRightsFunctionID, 
						@resourceRightID=@resourceRightID OUTPUT
					IF @@ERROR <> 0 GOTO on_error

					select @minBulkRateID = min(rateID)
					from dbo.ev_rates
					where parentRateID = @newRateID

					while @minBulkRateID is not null begin
						select @bulksiteResourceID=siteResourceID
						from dbo.ev_rates 
						where rateID = @minBulkRateID

						EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@bulksiteResourceID, @include=@srr_include, 
							@functionID=@srr_functionID, @roleID=@srr_roleid, @groupID=@srr_groupid, @memberID=@srr_memberid, 
							@inheritedRightsResourceID=@srr_inheritedRightsResourceID, @inheritedRightsFunctionID=@srr_inheritedRightsFunctionID, 
							@resourceRightID=@bulkresourceRightID OUTPUT
						IF @@ERROR <> 0 GOTO on_error

						select @minBulkRateID = min(rateID)
						from dbo.ev_rates
						where parentRateID = @minRateID
						and rateID > @newRateID
					end
		
					SELECT @srr_rightsID = min(resourceRightsID) from dbo.cms_siteResourceRights where resourceID = @siteResourceID and resourceRightsID > @srr_rightsID
				END

			END
			
			select @minRateId = min(rateID) 
				from dbo.ev_rates as r
				inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
				inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
					and srs.siteResourceStatusDesc = 'Active'
				where r.registrationID = @registrationID
				and r.parentRateID is null
				and rateID > @minRateID
		END

		-- custom questions
		SELECT @minCustomID = min(CustomID) FROM dbo.ev_registrationCustom WHERE registrationID = @registrationID
		WHILE @minCustomID is not null BEGIN
			INSERT INTO dbo.ev_registrationCustom (registrationID, areaID, FieldDesc, titleOnInvoice, customTypeID, IsRequired, RequiredMsg, fieldOrder, Status, amount, offerQTY, GLAccountID)
			SELECT	@newregistrationID, areaID, FieldDesc, titleOnInvoice, customTypeID, IsRequired, RequiredMsg, fieldOrder, Status, amount, offerQTY, GLAccountID
			FROM	dbo.ev_registrationCustom	
			WHERE	customID = @minCustomID
				IF @@ERROR <> 0 GOTO on_error
				SELECT @newCustomID = SCOPE_IDENTITY()
				
			INSERT INTO dbo.ev_customOptions(customID, optionDesc, optionOrder, status, amount)
			SELECT	@newCustomID, optionDesc, optionOrder, status, amount
			FROM	dbo.ev_customOptions
			WHERE	customID = @minCustomID
				IF @@ERROR <> 0 GOTO on_error			
	
			SELECT @minCustomID = min(CustomID) FROM dbo.ev_registrationCustom WHERE registrationID = @registrationID and CustomID > @minCustomID	
		END

	END

END

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @newEventID = 0
	RETURN -1
GO

