use memberCentral;
GO
if not exists (select * from dbo.ams_emailLogStatuses where statusCode = 'D')
insert into dbo.ams_emailLogStatuses (statusCode, statusDescription) values ('D','Email Bypassed by Administrator')

GO
CREATE NONCLUSTERED INDEX IX_ams_emailLog ON dbo.ams_emailLog
	(
	subscriberID
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO

ALTER PROC [dbo].[sub_queueMarkAccepted]
@recordedByMemberID int,
@subscriberIDList varchar(max),
@suppressEmails bit = 0

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL 
		DROP TABLE #tmpSubscribers
	CREATE TABLE #tmpSubscribers (subscriberID int, itemUID uniqueidentifier DEFAULT NEWID())

	declare @statusInserting int, @statusReady int, @queueTypeID int
	declare @emailStatusID int, @rightnow datetime

	select @queueTypeID = queueTypeID
	from platformQueue.dbo.tblQueueTypes as qt
	where qt.queueType = 'subsMarkAccepted'

	select @statusInserting = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs 
		where qs.queueTypeID = @queueTypeID
		and qs.queueStatus = 'insertingItems'
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		where qs.queueTypeID = @queueTypeID
		and qs.queueStatus = 'readyToProcess'

	-- there should only be one itemGroupUID for these emails
	declare @itemGroupUID uniqueidentifier
	set @itemGroupUID = NEWID()

	-- get subscribers to Mark Accepted
	insert into #tmpSubscribers (subscriberID)
	select s.subscriberID
	from dbo.fn_intListToTable(@subscriberIDList,',') as tmp
	inner join dbo.sub_subscribers as s on s.subscriberID = tmp.listitem
		except
	select qid.subscriberID
	from platformQueue.dbo.tblQueueItems_subscriptionStatusChanges as qid
	inner join platformQueue.dbo.tblQueueItems as qi on qi.itemUID = qid.itemUID
	inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.queueStatusID
		and qs.queueTypeID = @queueTypeID
		and qs.queueStatus not in ('readyToNotify','grabbedForNotifying','done')


    --suppress emails
    if @suppressEmails = 1
    BEGIN
	   set @rightnow = getdate()

	   select @emailStatusID = statusID 
	   from ams_emailLogStatuses
	   where statusCode = 'D'

	   insert into dbo.ams_emailLog (memberID, subscriberID, emailTemplateID, dateSent, actorMemberID, emailStatusID)
	   select m.activeMemberID, s.subscriberID, subs.renewEmailTemplateID, @rightnow, @recordedByMemberID, @emailStatusID
	   from #tmpSubscribers temp
	   inner join dbo.sub_subscribers s
		  on s.rootsubscriberID = temp.subscriberID
	   inner join dbo.ams_members m 
		  on m.memberID = s.memberID
	   inner join dbo.sub_rateFrequencies rf
		  on rf.rfid = s.rfid
	   inner join sub_rates r
		  on r.rateID = rf.rateID
		  and r.isRenewalRate=1
	   inner join dbo.sub_subscriptions subs 
		  on subs.subscriptionID = s.subscriptionID 
	   inner join dbo.et_emailTemplates et 
		  on et.templateID = subs.renewEmailTemplateID

	   union

	   select m.activeMemberID, s.subscriberID, subs.emailTemplateID, @rightnow, @recordedByMemberID, @emailStatusID
	   from #tmpSubscribers temp
	   inner join dbo.sub_subscribers s
		  on s.rootsubscriberID = temp.subscriberID
	   inner join dbo.ams_members m 
		  on m.memberID = s.memberID
	   inner join dbo.sub_rateFrequencies rf
		  on rf.rfid = s.rfid
	   inner join sub_rates r
		  on r.rateID = rf.rateID
		  and r.isRenewalRate=0
	   inner join dbo.sub_subscriptions subs 
		  on subs.subscriptionID = s.subscriptionID 
	   inner join dbo.et_emailTemplates et 
		  on et.templateID = subs.emailTemplateID
    END




	-- queue items
	insert into platformQueue.dbo.tblQueueItems_subscriptionStatusChanges (queueTypeID,itemUID, itemGroupUID,  subscriberID, recordedByMemberID)
		OUTPUT inserted.itemUID, inserted.dateAdded, @statusInserting 
		INTO platformQueue.dbo.tblQueueItems(itemUID, dateAdded, queueStatusID)
	select @queueTypeID, itemUID, @itemGroupUID, subscriberID, @recordedByMemberID
	from #tmpSubscribers

	-- update queue item groups to show ready to process
	update qi WITH (UPDLOCK, HOLDLOCK)
	set qi.queueStatusID = @statusReady,
		qi.dateUpdated = getdate()
	from platformQueue.dbo.tblQueueItems as qi
	inner join #tmpSubscribers as s on s.itemUID = qi.itemUID

	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL 
		DROP TABLE #tmpSubscribers

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO
CREATE PROC [dbo].[sub_suppressSubscriberAutomatedEmail]
@subscriberID int,
@enteredByMemberID int,
@limitToEmailTemplateID int = NULL

AS

set nocount on

declare @rightnow datetime, @emailStatusID int, @isRenewalRate bit, @memberID int, @emailTemplateID int

set @rightnow = getdate()

select @emailStatusID = statusID 
from ams_emailLogStatuses
where statusCode = 'D'

select @isRenewalRate = r.isRenewalRate, @memberID = m.activeMemberID, 
@emailTemplateID = case 
    when r.isRenewalRate = 0 and subs.emailTemplateID = isnull(@limitToEmailTemplateID,subs.emailTemplateID)
	   then subs.emailTemplateID
    when r.isRenewalRate = 1 and subs.renewEmailTemplateID = isnull(@limitToEmailTemplateID,subs.renewEmailTemplateID)
	   then subs.renewEmailTemplateID
    else null
    end
from dbo.sub_subscribers ss
inner join dbo.sub_subscriptions subs 
    on subs.subscriptionID = ss.subscriptionID 
    and ss.subscriberID = @subscriberID
inner join dbo.ams_members m 
    on m.memberID = ss.memberID
inner join dbo.sub_rateFrequencies rf
  on rf.rfid = ss.rfid
inner join sub_rates r
  on r.rateID = rf.rateID

if (@emailTemplateID is not null and not exists (select emailSentID from dbo.ams_emailLog where subscriberID=@subscriberID and emailTemplateID=@emailTemplateID))
BEGIN
    insert into dbo.ams_emailLog (memberID, subscriberID, emailTemplateID, dateSent, actorMemberID, emailStatusID)
    values (@memberID, @subscriberID, @emailTemplateID, @rightnow, @enteredByMemberID, @emailStatusID)
END
GO

use platformQueue;
GO
ALTER TABLE dbo.tblQueueItems_addSubscribers ADD
	skipEmailTemplateNotifications bit NOT NULL CONSTRAINT DF_tblQueueItems_addSubscribers_skipEmailTemplateNotifications DEFAULT 0
GO

use membercentral;
GO
ALTER PROC [dbo].[sub_importSubscriptions_toPerm]
@siteID int,
@recordedByMemberID int,
@flatfile varchar(160),
@skipEmailTemplateNotifications bit,
@importResult xml OUTPUT

AS

SET NOCOUNT ON

DECLARE @orgID int, @createSQL varchar(max), @cmd varchar(400)
select @orgID = orgID from dbo.sites where siteID = @siteID

IF OBJECT_ID('tempdb..#tblSubImpErrors') IS NOT NULL 
	DROP TABLE #tblSubImpErrors
CREATE TABLE #tblSubImpErrors (rowid int IDENTITY(1,1), msg varchar(max), fatal bit)

-- see if files exist
if dbo.fn_fileExists(@flatfile + '.sql') = 0
	or dbo.fn_fileExists(@flatfile + '.txt') = 0
	or dbo.fn_fileExists(@flatfile + '.bcp') = 0
BEGIN
	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES ('Unable to read subscription data files.',1)

	GOTO on_done
END

-- **************
-- read in sql create script and create global temp table
-- **************
BEGIN TRY
	SET @createSQL = replace(dbo.fn_ReadFile(@flatfile + '.sql',0,1),'##xxx','##importSubsData')
	IF OBJECT_ID('tempdb..##importSubsData') IS NOT NULL
		DROP TABLE ##importSubsData
	EXEC(@createSQL)
END TRY
BEGIN CATCH
	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES ('Unable to create holding table for subscription data.',1)

	GOTO on_done
END CATCH


-- *******************
-- bcp in data
-- *******************
BEGIN TRY
	set @cmd = 'bcp ##importSubsData in ' + @flatfile + '.bcp -f ' + @flatfile + '.txt -n -T -S' + CAST(serverproperty('servername') as varchar(40))
	exec master..xp_cmdshell @cmd, NO_OUTPUT
END TRY
BEGIN CATCH
	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES ('Unable to import data into holding table.',1)

	GOTO on_done
END CATCH


-- *******************
-- immediately put into local temp table and drop global temp
-- *******************
BEGIN TRY
	IF OBJECT_ID('tempdb..#importSubsData') IS NOT NULL
		DROP TABLE #importSubsData
	select * into #importSubsData from ##importSubsData
	IF OBJECT_ID('tempdb..##importSubsData') IS NOT NULL
		DROP TABLE ##importSubsData
END TRY
BEGIN CATCH
	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES ('Unable to import data into local temporary table.',1)

	GOTO on_done
END CATCH


/* ************** */
/* populate queue */
/* ************** */
BEGIN TRY
	IF OBJECT_ID('tempdb..#tmpSubscriberTrees') IS NOT NULL 
		DROP TABLE #tmpSubscriberTrees

	CREATE TABLE #tmpSubscriberTrees (memberID int, treeCode varchar(50), itemUID uniqueidentifier DEFAULT NEWID())
	INSERT INTO #tmpSubscriberTrees (memberID, treeCode)
	select distinct memberID, treeCode
	from #importSubsData

	-- all get the same ItemGroupUID
	declare @itemGroupUID uniqueidentifier
	select @itemGroupUID = NEWID()

	declare @statusInserting int, @statusReady int
	select @statusInserting = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'addSubscribers'
		and qs.queueStatus = 'insertingItems'
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'addSubscribers'
		and qs.queueStatus = 'readyToProcess'

	insert into platformQueue.dbo.tblQueueItems_addSubscribers (itemUID, itemGroupUID, recordedByMemberID, orgID, siteID, memberID, treeCode, skipEmailTemplateNotifications)
		OUTPUT inserted.itemUID, @statusInserting 
		INTO platformQueue.dbo.tblQueueItems(itemUID, queueStatusID)
	select distinct itemUID, @itemGroupUID, @recordedByMemberID, @orgID, @siteID, memberID, treeCode, @skipEmailTemplateNotifications
	from #tmpSubscriberTrees

	insert into platformQueue.dbo.tblQueueItems_addSubscribersDetail (itemUID, rowID, subscriptionID, parentSubscriptionID, RFID, 
		rateUID, subStartDate, subEndDate, graceEndDate, [status], lastPrice, storeModifiedRate)
	select tmpT.itemUID, tmp.rowID, tmp.subID, tmp.parentSubID, tmp.RFID, tmp.rateUID, tmp.StartDate, tmp.EndDate, tmp.graceEndDate, 
		tmp.status, tmp.lastPrice, tmp.storeModifiedRate
	from #importSubsData as tmp
	inner join #tmpSubscriberTrees as tmpT on tmpT.memberID = tmp.memberID and tmpT.treeCode = tmp.TreeCode

	-- update queue item groups to show ready to process
	update qi WITH (UPDLOCK, HOLDLOCK)
	set qi.queueStatusID = @statusReady,
		dateUpdated = getdate()
	from platformQueue.dbo.tblQueueItems as qi
	inner join #tmpSubscriberTrees as tmpT on tmpT.itemUID = qi.itemUID

	IF OBJECT_ID('tempdb..#tmpSubscriberTrees') IS NOT NULL 
		DROP TABLE #tmpSubscriberTrees
END TRY
BEGIN CATCH
	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES ('Unable to add subscriptions to the queue.',1)

	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES (error_message(),0)

	GOTO on_done
END CATCH


on_done:
	select @importResult = (
		select getdate() as "@date", @flatfile as "@flatfile", 
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg", "@severity" = case fatal when 1 then 'fatal' else 'nonfatal' end
			from #tblSubImpErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE)

	IF OBJECT_ID('tempdb..#tblSubImpErrors') IS NOT NULL 
		DROP TABLE #tblSubImpErrors
	IF OBJECT_ID('tempdb..##importSubsData') IS NOT NULL
		DROP TABLE ##importSubsData
	IF OBJECT_ID('tempdb..#importSubsData') IS NOT NULL
		DROP TABLE #importSubsData
	IF OBJECT_ID('tempdb..#tmpSubscriberTrees') IS NOT NULL 
		DROP TABLE #tmpSubscriberTrees
GO

use platformQueue;
GO
ALTER PROC [dbo].[job_addSubscribers_grabForProcessing]
@serverID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	DECLARE @batchSize int
	set @batchSize = 500

	declare @statusReady int, @statusGrabbed int
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'addSubscribers'
		and qs.queueStatus = 'readyToProcess'
	select @statusGrabbed = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'addSubscribers'
		and qs.queueStatus = 'grabbedForProcessing'

	IF OBJECT_ID('tempdb..#tmpTblQueueItems_addSubscribers') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_addSubscribers
	CREATE TABLE #tmpTblQueueItems_addSubscribers (itemUID uniqueidentifier, jobUID uniqueidentifier, 
		recordedByMemberID int, orgID int, siteID int, memberID int, treeCode varchar(50), skipEmailTemplateNotifications bit)

	declare @jobUID uniqueidentifier
	set @jobUID = NEWID()

	-- dequeue in order of dateAdded. get @batchsize trees
	update qi WITH (UPDLOCK, READPAST)
	set qi.queueStatusID = @statusGrabbed,
		qi.dateUpdated = getdate(),
		qi.jobUID = @jobUID,
		qi.jobDateStarted = getdate(),
		qi.jobServerID = @serverID
		OUTPUT inserted.itemUID, inserted.jobUID, qid.recordedByMemberID, qid.orgID, qid.siteID, qid.memberID, qid.treeCode, qid.skipEmailTemplateNotifications
		INTO #tmpTblQueueItems_addSubscribers
	from platformQueue.dbo.tblQueueItems as qi
	inner join platformQueue.dbo.tblQueueItems_addSubscribers as qid ON qid.itemUID = qi.itemUID
	inner join (
		select top(@BatchSize) qi2.itemUID 
		from platformQueue.dbo.tblQueueItems as qi2
		inner join platformQueue.dbo.tblQueueItems_addSubscribers as qid2 ON qid2.itemUID = qi2.itemUID
		where qi2.queueStatusID = @statusReady
		order by qi2.dateAdded, qi2.itemUID
		) as batch on batch.itemUID = qi.itemUID
	where qi.queueStatusID = @statusReady

	-- final data
	select qid.itemUID, qid.jobUID, qid.recordedByMemberID, qid.orgID, qid.siteID, qid.memberID, qid.skipEmailTemplateNotifications,
		qidd.rowID, qidd.subscriptionID, qidd.parentSubscriptionID, qidd.RFID, qidd.subStartDate, 
		qidd.subEndDate, qidd.graceEndDate, qidd.status, qidd.lastPrice, qidd.storeModifiedRate, qidd.subscriberID,
		GLAccountID = case when subs.allowRateGLAccountOverride = 1 and r.GLAccountID is not null then r.GLAccountID else subs.GLAccountID end,
		ActivationOptionCode = case when qidd.status in ('R','O') then ao.subActivationCode else 'N' end
	from #tmpTblQueueItems_addSubscribers as qid
	inner join platformQueue.dbo.tblQueueItems_addSubscribersDetail as qidd on qidd.itemUID = qid.itemUID
	inner join membercentral.dbo.sub_subscriptions as subs on subs.subscriptionID = qidd.subscriptionID
	inner join membercentral.dbo.sub_activationOptions as ao on ao.subActivationID = subs.subAlternateActivationID
	inner join membercentral.dbo.sub_rates as r on r.uid = qidd.rateUID
	order by qid.itemUID, qidd.rowID

	IF OBJECT_ID('tempdb..#tmpTblQueueItems_addSubscribers') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_addSubscribers

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH
GO
