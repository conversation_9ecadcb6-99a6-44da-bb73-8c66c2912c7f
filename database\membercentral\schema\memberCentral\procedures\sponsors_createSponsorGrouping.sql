CREATE PROC dbo.sponsors_createSponsorGrouping
@referenceType varchar(50),
@referenceID int,
@sponsorGrouping varchar(200),
@sponsorGroupingID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET @sponsorGroupingID = null;

	-- Determine context and execute appropriate logic
	IF @referenceType = 'Events'
	BEGIN
		-- Check if grouping already exists for Events
		IF EXISTS (SELECT sponsorGroupingID FROM dbo.ev_sponsorGrouping WHERE eventID=@referenceID AND sponsorGrouping=@sponsorGrouping)
			RAISERROR('Sponsor Grouping exists.',16,1);

		DECLARE @sponsorGroupingOrder int;
		SELECT @sponsorGroupingOrder = ISNULL(MAX(sponsorGroupingOrder),0)+1 FROM dbo.ev_sponsorGrouping WHERE eventID=@referenceID;

		INSERT INTO dbo.ev_sponsorGrouping (eventID, sponsorGrouping, sponsorGroupingOrder)
		VALUES (@referenceID, @sponsorGrouping, @sponsorGroupingOrder);

		SELECT @sponsorGroupingID = SCOPE_IDENTITY();
	END
	ELSE IF @referenceType IN ('swlProgram', 'swodProgram', 'swbprogram')
	BEGIN
		-- Check if grouping already exists for SeminarWeb
		IF EXISTS (SELECT sponsorGroupingID FROM seminarWeb.dbo.sw_sponsorGrouping WHERE seminarID=@referenceID AND participantID=0 AND sponsorGrouping=@sponsorGrouping)
			RAISERROR('Sponsor Grouping exists.',16,1);

		DECLARE @swSponsorGroupingOrder int;
		SELECT @swSponsorGroupingOrder = ISNULL(MAX(sponsorGroupingOrder),0)+1 FROM seminarWeb.dbo.sw_sponsorGrouping WHERE seminarID=@referenceID AND participantID=0;

		INSERT INTO seminarWeb.dbo.sw_sponsorGrouping (seminarID, participantID, sponsorGrouping, sponsorGroupingOrder)
		VALUES (@referenceID, 0, @sponsorGrouping, @swSponsorGroupingOrder);

		SELECT @sponsorGroupingID = SCOPE_IDENTITY();
	END
	ELSE
	BEGIN
		RAISERROR('Unsupported reference type: %s', 16, 1, @referenceType);
		RETURN -1;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
