CREATE PROC dbo.sponsors_createSponsorGrouping
@eventID int,
@sponsorGrouping varchar(200),
@sponsorGroupingID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;

BEGIN TRY
	BEGIN TRANSACTION;

	DECLARE @maxOrder int = 0;
	
	-- Get the maximum order value
	SELECT @maxOrder = ISNULL(MAX(sponsorGroupingOrder), 0)
	FROM dbo.ev_sponsorGrouping
	WHERE eventID = @eventID;
	
	-- Insert the new grouping
	INSERT INTO dbo.ev_sponsorGrouping (eventID, sponsorGrouping, sponsorGroupingOrder)
	VALUES (@eventID, @sponsorGrouping, @maxOrder + 1);
	
	SET @sponsorGroupingID = SCOPE_IDENTITY();

	COMMIT TRANSACTION;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
