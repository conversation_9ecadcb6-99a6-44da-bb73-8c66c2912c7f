use membercentral
GO
ALTER TABLE dbo.tr_paymentHistory ADD
	payerMemberID int NULL,
	memberPaymentProfileID int NULL,
	isSuccess bit NULL
GO
ALTER TABLE dbo.tr_paymentHistory ADD CONSTRAINT
	FK_tr_paymentHistory_ams_members FOREIGN KEY
	(
	payerMemberID
	) REFERENCES dbo.ams_members
	(
	memberID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.tr_paymentHistory ADD CONSTRAINT
	FK_tr_paymentHistory_ams_memberPaymentProfiles FOREIGN KEY
	(
	memberPaymentProfileID
	) REFERENCES dbo.ams_memberPaymentProfiles
	(
	payProfileID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO

update dbo.tr_paymentHistory
set isSuccess = 1
where isSuccess is null
and gatewayResponse.value('(/response/responsecode)[1]','varchar(10)') = '1'
GO

update dbo.tr_paymentHistory
set isSuccess = 0
where isSuccess is null
and gatewayResponse.value('(/response/responsecode)[1]','varchar(10)') <> '1'
GO

update ph
set ph.memberPaymentProfileID = mpp.payProfileID, ph.payerMemberID = mpp.memberID
from dbo.tr_paymentHistory as ph
inner join dbo.mp_profiles as mp on mp.profileID = ph.paymentInfo.value('(/payment/@profileid)[1]','int')
inner join dbo.mp_gateways as mg on mg.gatewayID = mp.gatewayID
inner join dbo.ams_memberPaymentProfiles as mpp on mpp.profileID = mp.profileID 
	and mpp.customerProfileID = ph.paymentInfo.value('(/payment/gateway/createCustomerProfileTransactionRequest/transaction/profileTransAuthCapture/customerProfileId)[1]','varchar(50)')
	and mpp.paymentProfileID = ph.paymentInfo.value('(/payment/gateway/createCustomerProfileTransactionRequest/transaction/profileTransAuthCapture/customerPaymentProfileId)[1]','varchar(50)')
where ph.memberPaymentProfileID is null
and mg.gatewayType = 'AuthorizeCCCIM'
and mpp.status = 'A'
GO

update ph
set ph.memberPaymentProfileID = mpp.payProfileID, ph.payerMemberID = mpp.memberID
from dbo.tr_paymentHistory as ph
inner join dbo.mp_profiles as mp on mp.profileID = ph.paymentInfo.value('(/payment/@profileid)[1]','int')
inner join dbo.mp_gateways as mg on mg.gatewayID = mp.gatewayID
inner join dbo.ams_memberPaymentProfiles as mpp on mpp.profileID = mp.profileID 
	and mpp.paymentProfileID = ph.paymentInfo.value('(/payment/args/paymentprofileid)[1]','varchar(50)')
where ph.memberPaymentProfileID is null
and mg.gatewayType = 'SageCCCIM'
and mpp.status = 'A'
GO

update ph
set ph.memberPaymentProfileID = mpp.payProfileID, ph.payerMemberID = mpp.memberID
from dbo.tr_paymentHistory as ph
inner join dbo.mp_profiles as mp on mp.profileID = ph.paymentInfo.value('(/payment/@profileid)[1]','int')
inner join dbo.mp_gateways as mg on mg.gatewayID = mp.gatewayID
inner join dbo.ams_memberPaymentProfiles as mpp on mpp.profileID = mp.profileID 
	and mpp.customerProfileID = ph.paymentInfo.value('(/payment/gateway/fld_9_)[1]','varchar(50)')
	and mpp.paymentProfileID = ph.paymentInfo.value('(/payment/gateway/fld_10_)[1]','varchar(50)')
where ph.memberPaymentProfileID is null
and mg.gatewayType = 'BankDraft'
and mpp.status = 'A'
GO

update ph
set ph.payerMemberID = t.assignedToMemberID
from dbo.tr_paymentHistory as ph
inner join dbo.tr_transactionPayments as tp on tp.historyID = ph.historyID
inner join dbo.tr_transactions as t on t.transactionID = tp.transactionID
where ph.payerMemberID is null
GO


ALTER PROC [dbo].[tr_importTransactions_toPerm]
@orgID int,
@siteID int,
@recordedByMemberID int,
@statsSessionID int,
@flatfile varchar(160),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

DECLARE @paymentcreditGLAccountID int, @saledebitGLAccountID int, @allocCreditGLAccountID int, @allocDebitGLAccountID int,
	@createSQL varchar(max), @cmd varchar(400), @invoicenum int

select @paymentcreditGLAccountID = glaccountid 
	from dbo.tr_GLAccounts 
	where orgID = @orgID
	and isSystemAccount = 1
	and GLCode = 'DEPOSITS'
	and [status] = 'A'
select @saledebitGLAccountID = glaccountid 
	from dbo.tr_GLAccounts 
	where orgID = @orgID
	and isSystemAccount = 1
	and GLCode = 'ACCOUNTSRECEIVABLE'
	and [status] = 'A'
select @allocCreditGLAccountID = @saledebitGLAccountID
select @allocDebitGLAccountID = @paymentcreditGLAccountID


IF OBJECT_ID('tempdb..#tblAccImpErrors') IS NOT NULL 
	DROP TABLE #tblAccImpErrors
CREATE TABLE #tblAccImpErrors (rowid int IDENTITY(1,1), msg varchar(300), fatal bit)

-- see if files exist
if dbo.fn_fileExists(@flatfile + '.sql') = 0
	or dbo.fn_fileExists(@flatfile + '.txt') = 0
	or dbo.fn_fileExists(@flatfile + '.bcp') = 0
BEGIN
	INSERT INTO #tblAccImpErrors (msg, fatal)
	VALUES ('Unable to read accounting data files.',1)

	GOTO on_done
END

-- **************
-- read in sql create script and create global temp table
-- **************
BEGIN TRY
	SET @createSQL = replace(dbo.fn_ReadFile(@flatfile + '.sql',0,1),'##xxx','##importAcctData')
	IF OBJECT_ID('tempdb..##importAcctData') IS NOT NULL
		DROP TABLE ##importAcctData
	EXEC(@createSQL)
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	VALUES ('Unable to create holding table for accounting data.',1)

	GOTO on_done
END CATCH


-- *******************
-- bcp in data
-- *******************
BEGIN TRY
	set @cmd = 'bcp ##importAcctData in ' + @flatfile + '.bcp -f ' + @flatfile + '.txt -n -T -S' + CAST(serverproperty('servername') as varchar(40))
	exec master..xp_cmdshell @cmd, NO_OUTPUT
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	VALUES ('Unable to import data into holding table.',1)

	GOTO on_done
END CATCH


-- *******************
-- immediately put into local temp table and drop global temp (memberid is a holder to be updated later)
-- *******************
BEGIN TRY
	IF OBJECT_ID('tempdb..#importAcctData') IS NOT NULL
		DROP TABLE #importAcctData
	select * into #importAcctData from ##importAcctData
	IF OBJECT_ID('tempdb..##importAcctData') IS NOT NULL
		DROP TABLE ##importAcctData
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	VALUES ('Unable to import data into local temporary table.',1)

	GOTO on_done
END CATCH


-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;


-- ******************************
-- Add temp fields to the tables
-- ******************************
BEGIN TRY
	SET @createSQL = '
		IF NOT EXISTS(SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N''dbo.tr_transactions'') AND name = ''tmpID'')
			ALTER TABLE dbo.tr_transactions ADD tmpID int NULL, tmpID2 int NULL, tmpID5 int NULL;
		IF NOT EXISTS(SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N''dbo.tr_paymentHistory'') AND name = ''tmpID2'')
			ALTER TABLE dbo.tr_paymentHistory ADD tmpID2 int NULL;
		IF NOT EXISTS(SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N''dbo.tr_invoices'') AND name = ''tmpID'')
			ALTER TABLE dbo.tr_invoices ADD tmpID int NULL;
		'
	EXEC(@createSQL)
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	VALUES ('Unable to add temporary fields.',1)

	GOTO on_doneT
END CATCH


/* ******* */
/* BATCHES */
/* ******* */
BEGIN TRY
	INSERT INTO dbo.tr_batches (orgID, statusID, batchCode, batchName, controlAmt, controlCount, depositDate, isSystemCreated, createdByMemberID, payProfileID)
	select distinct @orgID, 4, batchCode, batchName, 0, 0, batchDate, 0, @recordedByMemberID, payProfileID
	from #importAcctData
	where batchCode not in (select batchCode from dbo.tr_batches where orgID = @orgID and batchCode is not null)
	order by batchDate

	update tmp
	set tmp.batchID = b.batchID
	from #importAcctData as tmp
	inner join dbo.tr_batches as b on b.batchCode = tmp.batchCode and b.orgid = @orgID 

	IF EXISTS (select top 1 * from #importAcctData where batchID is null) BEGIN
		RAISERROR('Error raised in TRY block.', 16, 1);
	END
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	SELECT distinct top 100 PERCENT 'Could not identify required batch: ' + batchName, 1
	from #importAcctData 
	where batchID is null
	order by batchName

	GOTO on_doneT
END CATCH


/* ******** */
/* PAYMENTS */
/* ******** */
BEGIN TRY
	IF OBJECT_ID('tempdb..#tmpPayments') IS NOT NULL
		DROP TABLE #tmpPayments
	CREATE TABLE #tmpPayments (transactionID int, payID int)

	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, 
		parentTransactionID, amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, 
		statsSessionID, typeID, accrualDate, debitGLAccountID, creditGLAccountID, tmpID2)
		OUTPUT inserted.transactionid, inserted.tmpID2 
		INTO #tmpPayments(transactionID, payID)
	SELECT distinct @orgID, @siteID, 1, payDescription, null, payAmount, getdate(), payDate, payMemberID, 
		@recordedByMemberID, @statsSessionID, 2, payDate, payCashGLAID, @paymentcreditGLAccountID, payID
	from #importAcctData
	where payAmount > 0

	update tmp
	set tmp.payTID = tmpP.transactionID
	from #importAcctData as tmp
	inner join #tmpPayments as tmpP on tmpP.payID = tmp.payID

	IF OBJECT_ID('tempdb..#tmpPayments') IS NOT NULL
		DROP TABLE #tmpPayments

	IF EXISTS (select top 1 * from #importAcctData where payAmount > 0 and payTID is null) BEGIN
		RAISERROR('Error raised in TRY block.', 16, 1);
	END

	IF OBJECT_ID('tempdb..#tmpPaymentsHistory') IS NOT NULL
		DROP TABLE #tmpPaymentsHistory
	CREATE TABLE #tmpPaymentsHistory (historyID int, payID int)

	INSERT INTO dbo.tr_paymentHistory (paymentInfo, gatewayResponse, datePaid, statsSessionID, gatewayTransactionID, gatewayApprovalCode, 
		payerMemberID, memberPaymentProfileID, isSuccess, tmpID2)
		OUTPUT inserted.historyID, inserted.tmpID2 
		INTO #tmpPaymentsHistory(historyID, payID)
	select distinct 
		paymentInfo = '<payment gatewayid="' + cast(payProfileGatewayID as varchar(10)) + '" profileid="' + cast(payProfileID as varchar(10)) + '"><args><x_amount>' + cast(payAmount as varchar(20)) + '</x_amount><x_description>' + replace(isnull(payDescription,''),'&','&amp;') + '</x_description><fld_19_>' + replace(isnull(payReference,''),'&','&amp;') + '</fld_19_></args><gateway><x_amount>' + cast(payAmount as varchar(20)) + '</x_amount><x_description>' + replace(isnull(payDescription,''),'&','&amp;') + '</x_description><fld_19_>' + replace(isnull(payReference,''),'&','&amp;') + '</fld_19_></gateway></payment>',
		gatewayResponse = '<response><rawresponse/><responsecode>1</responsecode><responsereasontext/><responsereasoncode/><transactionid/><approvalcode/><transactiondetail/><status>Active</status><glaccountid>' + cast(payCashGLAID as varchar(10)) + '</glaccountid><historyid/></response>',
		payDate, @statsSessionID, '', '', payMemberID, null, 1, payID
	from #importAcctData
	where payTID is not null

	update tmp
	set tmp.payHistoryID = tmpP.historyID
	from #importAcctData as tmp
	inner join #tmpPaymentsHistory as tmpP on tmpP.payID = tmp.payID

	IF OBJECT_ID('tempdb..#tmpPaymentsHistory') IS NOT NULL
		DROP TABLE #tmpPaymentsHistory

	IF EXISTS (select top 1 * from #importAcctData where payAmount > 0 and payHistoryID is null) BEGIN
		RAISERROR('Error raised in TRY block.', 16, 1);
	END

	INSERT INTO dbo.tr_transactionPayments (transactionID, profileID, historyID, cache_allocatedAmountOfPayment, cache_refundableAmountOfPayment)
	select distinct payTID, payProfileID, payHistoryID, payAmount, payAmount
	from #importAcctData
	where payTID is not null

	INSERT INTO dbo.tr_batchTransactions (batchID, transactionID)
	select distinct batchID, payTID
	from #importAcctData
	where payTID is not null
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	SELECT distinct top 100 PERCENT 'Unable to add payment transaction for row ' + cast(autoid as varchar(10)), 1
	from #importAcctData 
	where payAmount > 0 and payTID is null
	order by autoid

	INSERT INTO #tblAccImpErrors (msg, fatal)
	SELECT distinct top 100 PERCENT 'Unable to add payment history record for row ' + cast(autoid as varchar(10)), 1
	from #importAcctData 
	where payAmount > 0 and payHistoryID is null
	order by autoid

	GOTO on_doneT
END CATCH


/* ******** */
/* INVOICES */
/* ******** */
BEGIN TRY
	IF OBJECT_ID('tempdb..#tmpInvoices') IS NOT NULL
		DROP TABLE #tmpInvoices
	CREATE TABLE #tmpInvoices (invoiceID int, autoID int)

	select @invoicenum = isnull(max(i.invoiceNumber),0)
		from dbo.tr_invoices as i 
		inner join dbo.ams_members as m on m.memberID = i.assignedToMemberID
		where m.orgID = @orgID

	INSERT INTO dbo.tr_invoices (datecreated, statusID, invoiceNumber, dateBilled, dateDue, assignedToMemberID, invoiceCode, payProfileID, invoiceProfileID, tmpID)
		OUTPUT inserted.invoiceID, inserted.tmpID 
		INTO #tmpInvoices(invoiceID, autoID)
	select getdate(), 4, autoid + @invoicenum, DATEADD(dd,DATEDIFF(dd,0,saleDate),0), DATEADD(dd,DATEDIFF(dd,0,saleDate),0), saleMemberID, '', null, invoiceProfileID, autoID
	from #importAcctData

	update tmp
	set tmp.invoiceID = tmpI.invoiceID
	from #importAcctData as tmp
	inner join #tmpInvoices as tmpI on tmpI.autoID = tmp.autoID

	IF OBJECT_ID('tempdb..#tmpInvoices') IS NOT NULL
		DROP TABLE #tmpInvoices

	IF EXISTS (select top 1 * from #importAcctData where invoiceID is null) BEGIN
		RAISERROR('Error raised in TRY block.', 16, 1);
	END

	INSERT INTO dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
	select invoiceID, DATEADD(dd,DATEDIFF(dd,0,saleDate),0), 1, null, @recordedByMemberID
	from #importAcctData

	INSERT INTO dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
	select invoiceID, DATEADD(ss,5,DATEADD(dd,DATEDIFF(dd,0,saleDate),0)), 3, 1, @recordedByMemberID
	from #importAcctData

	INSERT INTO dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
	select invoiceID, DATEADD(ss,10,DATEADD(dd,DATEDIFF(dd,0,saleDate),0)), 4, 3, @recordedByMemberID
	from #importAcctData

	;WITH numbers AS (select NUMBER as n, Abs(Checksum(Newid()))%26 as c from dbo.F_TABLE_NUMBER_RANGE(1,800000))
	update i
	set i.invoiceCode = tmpnum.code
	from dbo.tr_invoices as i
	inner join #importAcctData as tmp on tmp.invoiceID = i.invoiceID
	inner join (
		SELECT n, CAST((SELECT TOP 8 CHAR(65 + case c 
					when 0 then 2 -- A = C
					when 1 then 22 -- B = W
					when 4 then 10 -- E = K
					when 8 then 7 -- I = H
					when 14 then 15 -- O = P
					when 18 then 19 -- S = T
					when 20 then 3 -- U = D
					when 25 then 17 -- Z = R
					else c end)
					 FROM   numbers n1
					 WHERE  n1.n >= -n2.n
					 FOR XML PATH('')) AS CHAR(8)) as code
		FROM numbers n2  
	) as tmpnum on tmpnum.n = i.invoiceID
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	SELECT distinct top 100 PERCENT 'Unable to add invoice for row ' + cast(autoid as varchar(10)), 1
	from #importAcctData 
	where invoiceID is null
	order by autoid

	GOTO on_doneT
END CATCH


/* ***** */
/* SALES */
/* ***** */
BEGIN TRY
	IF OBJECT_ID('tempdb..#tmpSales') IS NOT NULL
		DROP TABLE #tmpSales
	CREATE TABLE #tmpSales (transactionID int, autoid int)

	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, 
		parentTransactionID, amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, 
		statsSessionID, typeID, accrualDate, debitGLAccountID, creditGLAccountID, tmpID)
		OUTPUT inserted.transactionid, inserted.tmpID 
		INTO #tmpSales(transactionID, autoid)
	select @orgID, @siteID, 1, saleDescription, null, saleAmount, getdate(), saleDate, saleMemberID, 
		@recordedByMemberID, @statsSessionID, 1, saleDate, @saledebitGLAccountID, saleRevenueGLAID, autoid
	from #importAcctData

	update tmp
	set tmp.saleTID = tmpS.transactionID
	from #importAcctData as tmp
	inner join #tmpSales as tmpS on tmpS.autoid = tmp.autoid

	IF OBJECT_ID('tempdb..#tmpSales') IS NOT NULL
		DROP TABLE #tmpSales

	IF EXISTS (select top 1 * from #importAcctData where saleTID is null) BEGIN
		RAISERROR('Error raised in TRY block.', 16, 1);
	END

	INSERT INTO dbo.tr_transactionSales (transactionID, cache_amountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, stateIDForTax)
	select saleTID, saleAmount, saleAmount, 0, null
	from #importAcctData

	INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount)
	select saleTID, invoiceID, saleAmount, saleAmount, 0
	from #importAcctData
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	SELECT distinct top 100 PERCENT 'Unable to add sale transaction for row ' + cast(autoid as varchar(10)), 1
	from #importAcctData 
	where saleTID is null
	order by autoid

	GOTO on_doneT
END CATCH


/* *********** */
/* ALLOCATIONS */
/* *********** */
BEGIN TRY
	IF OBJECT_ID('tempdb..#tmpAllocations') IS NOT NULL
		DROP TABLE #tmpAllocations
	CREATE TABLE #tmpAllocations (transactionID int, autoid int)

	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, 
		parentTransactionID, amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, 
		statsSessionID, typeID, accrualDate, debitGLAccountID, creditGLAccountID, tmpID5)
		OUTPUT inserted.transactionid, inserted.tmpID5 
		INTO #tmpAllocations(transactionID, autoid)
	select distinct @orgID, @siteID, 1, null, null, saleAmount, getdate(), saleDate, payMemberID,
		@recordedByMemberID, @statsSessionID, 5, saleDate, @allocDebitGLAccountID, @allocCreditGLAccountID, autoid
	from #importAcctData
	where payTID is not null

	update tmp
	set tmp.allocTID = tmpA.transactionID
	from #importAcctData as tmp
	inner join #tmpAllocations as tmpA on tmpA.autoid = tmp.autoid

	IF OBJECT_ID('tempdb..#tmpAllocations') IS NOT NULL
		DROP TABLE #tmpAllocations

	IF EXISTS (select top 1 * from #importAcctData where payTID is not null and allocTID is null) BEGIN
		RAISERROR('Error raised in TRY block.', 16, 1);
	END

	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)	--allocpaytrans
	select 2, allocTID, payTID
	from #importAcctData
	where allocTID is not null

	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)	--allocpaytrans
	select 3, allocTID, saleTID
	from #importAcctData
	where allocTID is not null
	
	INSERT INTO dbo.tr_batchTransactions (batchID, transactionID)
	select distinct batchID, allocTID
	from #importAcctData
	where allocTID is not null
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	SELECT distinct top 100 PERCENT 'Unable to add allocation transaction for row ' + cast(autoid as varchar(10)), 1
	from #importAcctData 
	where payTID is not null and allocTID is null
	order by autoid

	GOTO on_doneT
END CATCH


/* ******************* */
/* UPDATE BATCH COUNTS */
/* ******************* */
BEGIN TRY
	update b
	set b.controlCount = act.actualCount,
		b.controlAmt = act.actualAmount
	from dbo.tr_batches as b
	inner join (select distinct batchID from #importAcctData) as tmp on tmp.batchID = b.batchID
	cross apply dbo.fn_tr_getBatchActual(tmp.batchID) as act
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	VALUES ('Unable to update actual batch counts.',1)

	GOTO on_doneT
END CATCH


-- ******************************
-- Remove temp fields to the tables
-- ******************************
BEGIN TRY
	SET @createSQL = '
		IF EXISTS(SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N''dbo.tr_transactions'') AND name = ''tmpID'')
			ALTER TABLE dbo.tr_transactions DROP COLUMN tmpID, tmpID2, tmpID5;
		IF EXISTS(SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N''dbo.tr_paymentHistory'') AND name = ''tmpID2'')
			ALTER TABLE dbo.tr_paymentHistory DROP COLUMN tmpID2;
		IF EXISTS(SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N''dbo.tr_invoices'') AND name = ''tmpID'')
			ALTER TABLE dbo.tr_invoices DROP COLUMN tmpID;
		'
	EXEC(@createSQL)
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	VALUES ('Unable to remove temporary fields.',1)

	GOTO on_done
END CATCH

IF @TranCounter = 0
	COMMIT TRAN;
GOTO on_done


on_doneT:
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

on_done:
	select @importResult = (
		select getdate() as "@date", @flatfile as "@flatfile", 
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg", "@severity" = case fatal when 1 then 'fatal' else 'nonfatal' end
			from #tblAccImpErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE)

	IF OBJECT_ID('tempdb..#tblAccImpErrors') IS NOT NULL 
		DROP TABLE #tblAccImpErrors
	IF OBJECT_ID('tempdb..##importAcctData') IS NOT NULL
		DROP TABLE ##importAcctData
	IF OBJECT_ID('tempdb..#importAcctData') IS NOT NULL
		DROP TABLE #importAcctData
	IF OBJECT_ID('tempdb..#tmpPayments') IS NOT NULL
		DROP TABLE #tmpPayments
	IF OBJECT_ID('tempdb..#tmpPaymentsHistory') IS NOT NULL
		DROP TABLE #tmpPaymentsHistory
	IF OBJECT_ID('tempdb..#tmpInvoices') IS NOT NULL
		DROP TABLE #tmpInvoices
	IF OBJECT_ID('tempdb..#tmpSales') IS NOT NULL
		DROP TABLE #tmpSales
	IF OBJECT_ID('tempdb..#tmpAllocations') IS NOT NULL
		DROP TABLE #tmpAllocations
GO

ALTER TABLE dbo.tr_paymentHistory ALTER COLUMN isSuccess bit NOT NULL
GO

