﻿ALTER PROCEDURE [dbo].[ev_calendarGridXMLByMonth]
	@month int,
	@year int,
	@calendarID int,
	@languageID int,
	@siteID int
AS

declare @begindate datetime
declare @enddate datetime

select @begindate = cast(cast(@month as varchar(2)) + '/01/' + cast(@year as varchar(4)) as datetime)
select @enddate = dateadd(second,-1,dateadd(month,1,@begindate))

exec dbo.ev_calendarGridXMLByDateRange @begindate, @enddate, @calendarID, @languageID, @siteID

GO
