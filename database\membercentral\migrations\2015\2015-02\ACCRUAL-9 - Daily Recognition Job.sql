use membercentral
GO
ALTER PROC [dbo].[tr_createTransaction_dit]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@amount money,
@transactionDate datetime,
@recognitionDate datetime, -- requires for positive dit only
@debitGLAccountID int,
@creditGLAccountID int,
@saleTransactionID int,	-- this can be a sale, tax, or adjustment TID
@DITTransactionID int, -- required for negative dit only
@batchAsRecogJob bit, -- required for negative dit only
@transactionID int OUTPUT

AS

set nocount on

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- reset output param
	select @transactionID = 0

	-- zero dollar DITs are ok. they serve as placeholders when negative adjustments need to respread dits
	
	-- ensure amount is 2 decimals
	select @amount = cast(@amount as decimal(10,2))

	-- ensure @DITTransactionID is a dit (if passed in)
	IF @DITTransactionID is not null and NOT EXISTS (select transactionID from dbo.tr_transactions where transactionID = @DITTransactionID and typeID = 10)
		RAISERROR('ditTransactionID is not a DIT', 16, 1);
	IF @amount < 0 AND @DITTransactionID IS NULL
		RAISERROR('ditTransactionID is not valid', 16, 1);

	-- ensure @saleTransactionID is either a sale, sales tax, or positive adjustment
	IF NOT EXISTS (
		select t.transactionID
		from dbo.tr_transactions as t
		inner join dbo.tr_glAccounts as glDeb on glDeb.GLAccountID = t.debitGLAccountID
		where t.transactionID = @saleTransactionID
		and t.typeID in (1,3,7)
		and glDeb.GLCode = 'ACCOUNTSRECEIVABLE' 
		and glDeb.isSystemAccount = 1
		)
	RAISERROR('saleTransactionID is not a sale, sales tax, or positive adjustment', 16, 1);

	-- get info from sale/tax/adj transaction
	declare @assignedToMemberID int, @ownedByOrgID int, @saleTransactionTypeID int
	select @assignedToMemberID = t.assignedToMemberID, @ownedByOrgID = t.ownedByOrgID, @saleTransactionTypeID = t.typeID
		from dbo.tr_transactions as t
		where t.transactionID = @saleTransactionID

	-- dont assume memberid is the active one. get the active one.
	select @assignedToMemberID = activeMemberID from dbo.ams_members where memberID = @assignedToMemberID 
	select @recordedByMemberID = activeMemberID from dbo.ams_members where memberID = @recordedByMemberID 

	-- ensure we have active debit/credit accts that account accepts new transactions
	IF @debitGLAccountID is null or NOT EXISTS (
		select glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and glaccountID = @debitGLAccountID
		and [status] = 'A')
		RAISERROR('debit account does not accept new transactions', 16, 1);
	IF @creditGLAccountID is null or NOT EXISTS (
		select glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and glaccountID = @creditGLAccountID
		and [status] = 'A')
		RAISERROR('credit account does not accept new transactions', 16, 1);

	-- if amount < 0 and there are allocations to DITtransactionID, deallocate now and reallocate at the end
	IF @amount < 0 BEGIN
		declare @amtAllocatedToDIT money
		select @amtAllocatedToDIT = cache_activePaymentAllocatedAmount + cache_pendingPaymentAllocatedAmount 
		from dbo.tr_transactionSales 
		where transactionID = @DITtransactionID

		IF @amtAllocatedToDIT > 0 BEGIN
			declare @tblAllocations TABLE (paymentTransactionID int, allocatedAmount money, profileID int, batchCode varchar(40), batchName varchar(400), batchID int)

			insert into @tblAllocations (paymentTransactionID, allocatedAmount, profileID, batchCode, batchName)
			select apos.paymentTransactionID, apos.allocatedAmount, mp.profileID, 
				batchCode = CONVERT(CHAR(8),@transactionDate,112) + '_' + cast(mp.profileID as varchar(10)) + '_' + cast(t.debitGLAccountID as varchar(10)) + '_DIT',
				batchName = CONVERT(CHAR(8),@transactionDate,112) + ' ' + mp.profileCode + ' ' + gl.accountName + ' Recognitions'
			from dbo.fn_tr_getAllocatedPaymentsofSale(@DITtransactionID) as apos
			inner join dbo.tr_transactions as t on t.transactionID = apos.paymentTransactionID
			inner join dbo.tr_transactionPayments as tp on tp.transactionID = t.transactionID
			inner join dbo.mp_profiles as mp on mp.profileID = tp.profileID
			inner join dbo.tr_glAccounts as gl on gl.glAccountID = t.debitGLAccountID

			declare @minPTID int, @DeallocateAmtNeg money, @newAllocationTID int, @ovBatchID int, @ovprofileID int, @ovBatchCode varchar(40), @ovBatchName varchar(400)
			select @minPTID = min(paymentTransactionID) from @tblAllocations
			WHILE @minPTID is not null BEGIN
				select @DeallocateAmtNeg = allocatedAmount*-1, @ovprofileID=profileID, @ovBatchCode=batchCode, @ovBatchName=batchName
				from @tblAllocations 
				where paymentTransactionID = @minPTID

				-- if this dit is created as part of the daily recognition job, batch all those up 
				SET @ovBatchID = null
				IF @batchAsRecogJob = 1 BEGIN
					select @ovBatchID = b.batchID
						from dbo.tr_batches as b
						where b.orgID = @ownedByOrgID
						and b.batchCode = @ovBatchCode
						and b.statusID = 1
						and b.isSystemCreated = 1
					IF @ovBatchID is null
						EXEC dbo.tr_createBatch @orgID=@ownedByOrgID, @payProfileID=@ovprofileID, @batchCode=@ovBatchCode, 
							@batchName=@ovBatchName, @controlAmt=0, @controlCount=0, @depositDate=@transactionDate, 
							@isSystemCreated=1, @createdByMemberID=null, @batchID=@ovBatchID OUTPUT 

					UPDATE @tblAllocations
					SET batchID = @ovBatchID
					WHERE paymentTransactionID = @minPTID
				END 

				EXEC dbo.tr_createTransaction_allocation @recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
					@statsSessionID=@statsSessionID, @status='Active', @amount=@DeallocateAmtNeg, @transactionDate=@transactionDate, 
					@paymentTransactionID=@minPTID, @saleTransactionID=@DITtransactionID, @ovBatchID=@ovBatchID, 
					@transactionID=@newAllocationTID OUTPUT

				select @minPTID = min(paymentTransactionID) from @tblAllocations where paymentTransactionID > @minPTID
			END 
		END
	END

	-- insert into transactions
	declare @tr_transactionsAmount decimal(10,2)
	set @tr_transactionsAmount = abs(@amount)

	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
		amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
		typeID, accrualDate, debitGLAccountID, creditGLAccountID)
	VALUES (@ownedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID('Active'), null, null, 
		@tr_transactionsAmount, getdate(), @transactionDate, @assignedToMemberID, @recordedByMemberID, @statsSessionID, 
		10, @transactionDate, @debitGLAccountID, @creditGLAccountID)
		select @transactionID = SCOPE_IDENTITY()


	-- insert into transactionSales
	declare @tr_transactionSalesAmount decimal(10,2)
	select @tr_transactionSalesAmount = case when @amount > 0 then @amount else 0 end

	INSERT INTO dbo.tr_transactionSales (transactionID, cache_amountAfterAdjustment, 
		cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, stateIDForTax)
	VALUES (@transactionID, @tr_transactionSalesAmount, 0, 0, null)


	-- insert into transactionDIT
	IF @amount >= 0 and @DITTransactionID is null BEGIN
		INSERT INTO dbo.tr_transactionDIT (transactionID, recognitionDate)
		VALUES (@transactionID, @recognitionDate)
	END


	-- insert into relationships
	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
	VALUES (dbo.fn_tr_getRelationshipTypeID('DITSaleTrans'), @transactionID, @saleTransactionID)

	IF @amount <= 0 and @DITTransactionID is not null BEGIN
		INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
		VALUES (dbo.fn_tr_getRelationshipTypeID('DITOffsetTrans'), @transactionID, @DITTransactionID)
	END


	-- if @saleTransactionID is an adjustment, get sale or tax for the transactionSales updates
	DECLARE @tsTransactionID int	
	IF @saleTransactionTypeID = 3
		SELECT @tsTransactionID = r.appliedToTransactionID
		from dbo.tr_relationships as r 
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
		where r.transactionID = @saleTransactionID		
	ELSE
		SET @tsTransactionID = @saleTransactionID


	-- update tr_transactionSales for sale/tax
	IF @amount > 0 	
		UPDATE dbo.tr_transactionSales
		SET cache_amountAfterAdjustment = cache_amountAfterAdjustment - @amount
		WHERE transactionID = @tsTransactionID

	IF @amount < 0 	
	BEGIN
		UPDATE dbo.tr_transactionSales
		SET cache_amountAfterAdjustment = cache_amountAfterAdjustment + @tr_transactionsAmount
		WHERE transactionID = @tsTransactionID

		UPDATE dbo.tr_transactionSales
		SET cache_amountAfterAdjustment = cache_amountAfterAdjustment - @tr_transactionsAmount
		WHERE transactionID = @DITTransactionID
	END


	-- if amount < 0 and there were allocations to DITtransactionID, reallocate now 
	IF @amount < 0 AND @amtAllocatedToDIT > 0 BEGIN
		declare @ReallocateAmt money, @reAllocateBatchID int
		select @minPTID = null, @newAllocationTID = null
		select @minPTID = min(paymentTransactionID) from @tblAllocations
		WHILE @minPTID is not null BEGIN
			select @ReallocateAmt=allocatedAmount, @reAllocateBatchID=batchID
			from @tblAllocations 
			where paymentTransactionID = @minPTID

			SET @ovBatchID = null
			IF @batchAsRecogJob = 1 
				SET @ovBatchID = @reAllocateBatchID

			EXEC dbo.tr_createTransaction_allocation @recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
				@statsSessionID=@statsSessionID, @status='Active', @amount=@ReallocateAmt, @transactionDate=@transactionDate, 
				@paymentTransactionID=@minPTID, @saleTransactionID=@saleTransactionID, @ovBatchID=@ovBatchID, 
				@transactionID=@newAllocationTID OUTPUT

			select @minPTID = min(paymentTransactionID) from @tblAllocations where paymentTransactionID > @minPTID
		END 
	END


	-- check the in-bound rules.
	-- sale - new cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount must be between 0 and cache_amountAfterAdjustment
	IF NOT EXISTS (select saleID from dbo.tr_transactionSales where transactionID = @tsTransactionID and cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount between 0 and cache_amountAfterAdjustment)
		OR (@amount < 0 AND NOT EXISTS (select saleID from dbo.tr_transactionSales where transactionID = @DITTransactionID and cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount between 0 and cache_amountAfterAdjustment))
		RAISERROR('in-bounds checking failed', 16, 1);

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @transactionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO


ALTER PROC [dbo].[tr_createTransaction_adjustment_down]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@status varchar(20),
@amount money,
@transactionDate datetime,
@saleTransactionID int,
@invoiceID int,
@xmlSchedule xml = null, 
@transactionID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- init output param
	select @transactionID = 0

	declare @origSaleOwnedByOrgID int, @origSaleAssignedToMemberID int, @origCreditGLAccountID int, 
			@ARGLAccountID int, @AdjSaleTransactionID int, @AdjTaxTransactionID int, @invoiceProfileID int, 
			@contentVersionID int, @ditTransactionID int, @ditTransactionID2 int, @SaleAndAdjustmentsMinAutoID int, 
			@SaleAndAdjustmentsTID int, @SaleAndAdjustmentsTypeID int, @AdjToMakeAutoID int, @AdjToMakeTID int, 
			@AdjToMakeGLAID int, @a_autoid int, @a_paymentTID int, @it_invoiceID int, @AdjToMakeSaleAdjTID int, 
			@ditspreadAutoID int, @ditSpreadDITTID int, @ditSpreadDebitGL int, @ditSpreadCreditGL int, 
			@newdittransactionID int, @SaleAndAdjustmentsInvID int, @respreadFirstAutoID int,
			@respreadSaleAdjTID int, @respreadAutoID int, @respreadDebitGL int, @respreadCreditGL int
	declare @amtLeftToDeallocate money, @AllocAmt money, @amtLeftToAdjust money, @SaleAndAdjustmentsAmt money, 
			@amountToAdjust money, @AdjToMakeAmt money, @AdjToMakeUnallocatedAmt money, @amountToAllocate money, 
			@a_allocAmount money, @AdjToMakeDITSum money, @amtDueNoPendingOnInvoice money, 
			@ditSpreadDITCacheAmount money, @amtToRespread money, @respreadSum money, @respreadDiff money
	declare @invoiceNumber varchar(18), @it_invstatus varchar(50)
	declare @origSaleDetail varchar(max), @AdjToMakeDetail varchar(max)
	declare @AdjToMakeIsSale bit
	declare @ditSpreadRecogDt datetime
	declare @tblSaleAndAdjustments TABLE (autoid int IDENTITY(1,1) PRIMARY KEY, transactionID int, typeID int, amount money, invoiceID int)
	declare @tblAdjToMake TABLE (autoid int IDENTITY(1,1), isSale bit, saleTransactionID int, saleAdjTID int, debitGLAID int, detail varchar(max), amountToAdjust numeric(9,2), adjTransactionID int, SaleAdjInvoiceID int)
	declare @tblAdjToMakeFinal TABLE (autoid int IDENTITY(1,1), isSale bit, saleTransactionID int, debitGLAID int, detail varchar(max), amountToAdjust numeric(9,2), adjTransactionID int)
	declare @tblAllocations TABLE (autoid int IDENTITY(1,1) PRIMARY KEY, transactionID int, allocAmount money)
	declare @tblDITSpread TABLE (autoID int IDENTITY(1,1) PRIMARY KEY, saleAdjTID int, ditTransactionID int, ditAmount money, ditCacheAmount money, recogDt datetime, pct numeric(10,2), ditdebitGLAID int, ditcreditGLAID int, respreadAmount money)

	-- get data from sale transaction
	select @origSaleOwnedByOrgID=ownedByOrgID, @origSaleAssignedToMemberID=assignedToMemberID, 
		@origSaleDetail=detail, @origCreditGLAccountID=creditGLAccountID
		from dbo.tr_transactions
		where transactionID = @saleTransactionID

	-- dont assume memberid is the active one. get the active one.
	select @origSaleAssignedToMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @origSaleAssignedToMemberID 
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	select @ARGLAccountID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @origSaleOwnedByOrgID
		and isSystemAccount = 1
		and GLCode = 'ACCOUNTSRECEIVABLE'
		and [status] = 'A'
	IF @ARGLAccountID is null
		RAISERROR('ARGLAccountID is null', 16, 1);

	-- if invoiceID is null, not an open/pending invoice, or inv profile doesnt match revenue GL, assume need to create a new one.
	IF @invoiceID is null 
		OR NOT EXISTS (select invoiceID from dbo.tr_invoices where invoiceID = @invoiceID and statusID in (1,2))
		OR ((select dbo.fn_tr_doesInvoiceProfileSupportRevenueGL(@invoiceID,@origCreditGLAccountID)) = 0)
	BEGIN
		select @invoiceProfileID=invoiceProfileID from dbo.tr_GLAccounts where GLAccountID = @origCreditGLAccountID
			IF @invoiceProfileID is null RAISERROR('invoiceProfileID is null', 16, 1);

		EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID,
			@enteredByMemberID=@recordedByMemberID, 
			@assignedToMemberID=@origSaleAssignedToMemberID,
			@dateBilled=@transactionDate, @dateDue=@transactionDate, 
			@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT
	END
	

	-- get all active adjustments to sale in reverse order (daterecorded).
	-- consider the non-written off amounts only and remove any that are 0
	insert into @tblSaleAndAdjustments (transactionID, typeID, amount, invoiceID)
	select tAdj.transactionID, tAdj.typeID, it.cache_invoiceAmountAfterAdjustment-wo.writeOffAmount, it.invoiceID
	from dbo.tr_transactions as tAdj
	inner join dbo.tr_relationships as tr on tr.transactionID = tAdj.transactionID and tr.appliedToTransactionID = @saleTransactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'AdjustTrans'
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = tAdj.transactionID
	cross apply dbo.fn_tr_getWriteOffAmountofSaleOrAdj(tAdj.transactionID) as wo
	where tAdj.statusID = 1
	and it.cache_invoiceAmountAfterAdjustment-wo.writeOffAmount > 0
	order by tAdj.daterecorded desc, tAdj.transactionID desc
	
	-- add in the original sale as last entry
	insert into @tblSaleAndAdjustments (transactionID, typeID, amount, invoiceID)
	select tSale.transactionID, tSale.typeID, it.cache_invoiceAmountAfterAdjustment-wo.writeOffAmount, it.invoiceID
	from dbo.tr_transactions as tSale
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSale.transactionID
	cross apply dbo.fn_tr_getWriteOffAmountofSaleOrAdj(tSale.transactionID) as wo
	where tSale.transactionID = @saleTransactionID
	and it.cache_invoiceAmountAfterAdjustment-wo.writeOffAmount > 0


	-- loop over @tblSaleAndAdjustments until we get adjustment amount. grab PIT tax as well.		
	set @amtLeftToAdjust = abs(@amount)
	select @SaleAndAdjustmentsMinAutoID = min(autoid) from @tblSaleAndAdjustments
	while @SaleAndAdjustmentsMinAutoID is not null BEGIN
		select @SaleAndAdjustmentsTID=transactionID, @SaleAndAdjustmentsTypeID=typeID, @SaleAndAdjustmentsAmt=amount, @SaleAndAdjustmentsInvID=invoiceID
		from @tblSaleAndAdjustments 
		where autoID = @SaleAndAdjustmentsMinAutoID

		-- if amt left can be adjusted in full from this adjustment, take full amt. else take what we can.
		if @SaleAndAdjustmentsAmt < @amtLeftToAdjust
			set @amountToAdjust = @SaleAndAdjustmentsAmt
		ELSE
			set @amountToAdjust = @amtLeftToAdjust

		-- add to adj to make. adj debit acct is orig credit acct
		insert into @tblAdjToMake (saleTransactionID, saleAdjTID, isSale, debitGLAID, detail, amountToAdjust, SaleAdjInvoiceID)
		values (@saleTransactionID, @SaleAndAdjustmentsTID, 1, @origCreditGLAccountID, @origSaleDetail, @amountToAdjust*-1, @SaleAndAdjustmentsInvID)
			
		-- and all its PIT taxes (adj to sales tax and sales tax). adj debit acct is orig credit acct
		-- 1. adjust to sales tax tied to adjust to sale (could be pos or neg so find out based on AR)
		-- 2+3. sales tax tied to sale or adjust to sale (only happens on positive adjustments so just take adj amount)
		insert into @tblAdjToMake (saleTransactionID, saleAdjTID, isSale, debitGLAID, detail, amountToAdjust, SaleAdjInvoiceID)
		SELECT tTax.transactionID, tAdj.transactionID, 0, tTax.creditGLAccountID, tTax.detail, 
			case 
			when gl.isSystemAccount = 1 and gl.GLCode = 'ACCOUNTSRECEIVABLE' then ((((it.cache_invoiceAmountAfterAdjustment-wo.writeOffAmount)/@SaleAndAdjustmentsAmt)*@amountToAdjust)*-1)
			else (((it.cache_invoiceAmountAfterAdjustment-wo.writeOffAmount)/@SaleAndAdjustmentsAmt)*@amountToAdjust)
			end as amountToAdjust, it.invoiceID
		FROM dbo.tr_transactions AS tAdj 
		inner join dbo.tr_glAccounts as gl on gl.glAccountID = tAdj.debitGLAccountID
		INNER JOIN dbo.tr_relationships AS tr ON tAdj.transactionID = tr.transactionID and tr.appliedToTransactionID = @SaleAndAdjustmentsTID
		INNER JOIN dbo.tr_relationshipTypes AS trt ON tr.typeID = trt.typeID and trt.type = 'PITTaxTrans'
		INNER JOIN dbo.tr_relationships AS tr2 ON tAdj.transactionID = tr2.transactionID 
		INNER JOIN dbo.tr_relationshipTypes AS trt2 ON tr2.typeID = trt2.typeID and trt2.type = 'AdjustTrans'
		INNER JOIN dbo.tr_transactions AS tTax ON tr2.appliedToTransactionID = tTax.transactionID
		INNER JOIN dbo.tr_invoiceTransactions as it on it.transactionID = tAdj.transactionID
		cross apply dbo.fn_tr_getWriteOffAmountofSaleOrAdj(tAdj.transactionID) as wo
		WHERE @SaleAndAdjustmentsTypeID = 3
		AND tAdj.typeID = 3
		AND tAdj.statusID = 1
		AND tTax.typeID = 7
		AND tTax.statusID = 1
			union all
		SELECT tTax.transactionID, tTax.transactionID, 0, tTax.creditGLAccountID, tTax.detail, 
			((((it.cache_invoiceAmountAfterAdjustment-wo.writeOffAmount)/@SaleAndAdjustmentsAmt)*@amountToAdjust)*-1) as amountToAdjust, it.invoiceID
		FROM dbo.tr_transactions AS tTax 
		INNER JOIN dbo.tr_relationships AS tr ON tTax.transactionID = tr.transactionID and tr.appliedToTransactionID = @SaleAndAdjustmentsTID
		INNER JOIN dbo.tr_relationshipTypes AS trt ON tr.typeID = trt.typeID AND trt.type = 'PITTaxTrans'
		INNER JOIN dbo.tr_invoiceTransactions as it on it.transactionID = tTax.transactionID
		cross apply dbo.fn_tr_getWriteOffAmountofSaleOrAdj(tTax.transactionID) as wo
		WHERE tTax.typeID = 7 
		AND tTax.statusID = 1
		AND @SaleAndAdjustmentsTypeID in (1,3)

		set @amtLeftToAdjust = @amtLeftToAdjust - @amountToAdjust
		IF @amtLeftToAdjust <= 0
			BREAK

		select @SaleAndAdjustmentsMinAutoID = min(autoid) from @tblSaleAndAdjustments where autoid > @SaleAndAdjustmentsMinAutoID
	END


	-- loop over the @tblAdjToMake to ensure everything is recognized (not deferred) and enough is unallocated for adjustment
	select @AdjToMakeAutoID = min(autoid) from @tblAdjToMake
	while @AdjToMakeAutoID is not null BEGIN
		select @AdjToMakeSaleAdjTID=saleAdjTID, @AdjToMakeAmt=amountToAdjust
		from @tblAdjToMake 
		where autoid = @AdjToMakeAutoID

		-- Get all active DIT linked to tr_transactionDIT with dit amount, tr_transactionSales amount, and dit recog date.			
		insert into @tblDITSpread (saleAdjTID, ditTransactionID, ditAmount, ditCacheAmount, recogDt, ditdebitGLAID, ditcreditGLAID)
		select r.appliedToTransactionID, t.transactionID, t.amount, ts.cache_amountAfterAdjustment, dit.recognitionDate, t.debitGLAccountID, t.creditGLAccountID
		from dbo.tr_transactions as t
		inner join dbo.tr_relationships as r on r.transactionID = t.transactionID and r.appliedToTransactionID = @AdjToMakeSaleAdjTID
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
		inner join dbo.tr_transactionDIT as dit on dit.transactionID = t.transactionID and dit.isActive = 1
		inner join dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID
		where t.statusID = 1

		-- update pct split and respread amt
		select @AdjToMakeDITSum = sum(ditAmount) from @tblDITSpread where saleAdjTID = @AdjToMakeSaleAdjTID
		update @tblDITSpread set pct = ditAmount / @AdjToMakeDITSum where saleAdjTID = @AdjToMakeSaleAdjTID
		update @tblDITSpread set respreadAmount = cast((@AdjToMakeDITSum-abs(@AdjToMakeAmt)) * pct as numeric(10,2)) where saleAdjTID = @AdjToMakeSaleAdjTID

		-- put any remainders on the first respread
		select @respreadSum = sum(respreadAmount), @respreadFirstAutoID = min(autoid) from @tblDITSpread where saleAdjTID = @AdjToMakeSaleAdjTID
		set @respreadDiff = @AdjToMakeDITSum - abs(@AdjToMakeAmt) - @respreadSum
		if @respreadDiff <> 0
			update @tblDITSpread set respreadAmount = respreadAmount + @respreadDiff where autoID = @respreadFirstAutoID


		-- If transaction has any non-recognized revenue, we need to recognize it all now.
		select @ditspreadAutoID = min(autoID) from @tblDITSpread where saleAdjTID = @AdjToMakeSaleAdjTID
		while @ditspreadAutoID is not null begin
			select @ditSpreadDITCacheAmount = ditCacheAmount*-1, @ditSpreadDITTID = ditTransactionID,
					@ditSpreadRecogDt = recogDt, @ditSpreadDebitGL = ditdebitGLAID, @ditSpreadCreditGL = ditcreditGLAID
			from @tblDITSpread
			where autoID = @ditspreadAutoID

			if abs(@ditSpreadDITCacheAmount) > 0
				EXEC dbo.tr_createTransaction_dit @recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
					@statsSessionID=@statsSessionID, @amount=@ditSpreadDITCacheAmount, @transactionDate=@transactionDate, 
					@recognitionDate=null, @debitGLAccountID=@ditSpreadCreditGL, @creditGLAccountID=@ditSpreadDebitGL, 
					@saleTransactionID=@AdjToMakeSaleAdjTID, @DITTransactionID=@ditSpreadDITTID, @batchAsRecogJob=0, 
					@transactionID=@newdittransactionID OUTPUT

			update dbo.tr_transactionDIT
			set isActive = 0
			where transactionID = @ditSpreadDITTID

			select @ditspreadAutoID = min(autoID) from @tblDITSpread where saleAdjTID = @AdjToMakeSaleAdjTID and autoID > @ditspreadAutoID
		end


		-- see if we need to deallocate. only need to deallocate if adj amount is gt than unallocated amt of sale/adj
		select @AdjToMakeUnallocatedAmt = cache_invoiceAmountAfterAdjustment - cache_activePaymentAllocatedAmount - cache_pendingPaymentAllocatedAmount
		from dbo.tr_invoiceTransactions
		where transactionID = @AdjToMakeSaleAdjTID

		if abs(@AdjToMakeAmt) > @AdjToMakeUnallocatedAmt BEGIN
			delete from @tblAllocations

			-- get all active allocations to this sale/adj in reverse order
			insert into @tblAllocations (transactionID, allocAmount)
			select transactionID, allocAmount
			from dbo.fn_tr_getAllocatedPaymentsofSaleOrAdj(@AdjToMakeSaleAdjTID)
			order by allocDate desc

			select @amtLeftToDeallocate = abs(@AdjToMakeAmt)-@AdjToMakeUnallocatedAmt
			select @a_autoid = null
			select @a_autoid = min(autoid) from @tblAllocations
			while @a_autoid is not null BEGIN
				select @a_allocAmount=allocAmount, @a_paymentTID=transactionID
				from @tblAllocations 
				where autoID = @a_autoid

				-- if amt left can be deallocated in full from this payment, take full amt. else take what we can.
				if @a_allocAmount < @amtLeftToDeallocate
					set @amountToAllocate = @a_allocAmount
				ELSE
					set @amountToAllocate = @amtLeftToDeallocate

				EXEC dbo.tr_deallocateFromSaleOrAdj @recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
					@statsSessionID=@statsSessionID, @amount=@amountToAllocate, @transactionDate=@transactionDate, 
					@paymentTransactionID=@a_paymentTID, @saleTransactionID=@AdjToMakeSaleAdjTID

				select @amtLeftToDeallocate = @amtLeftToDeallocate - @amountToAllocate
				IF @amtLeftToDeallocate <= 0
					BREAK

				select @a_autoid = min(autoid) from @tblAllocations where autoID > @a_autoid
			end
		end

		select @AdjToMakeAutoID = min(autoid) from @tblAdjToMake where autoID > @AdjToMakeAutoID
	end

	-- sum and group transactions by saleTID. these are the adj transactions to record
	insert into @tblAdjToMakeFinal (saleTransactionID, isSale, debitGLAID, detail, amountToAdjust)
	select saleTransactionID, isSale, debitGLAID, detail, sum(amountToAdjust)
	from @tblAdjToMake
	group by saleTransactionID, isSale, debitGLAID, detail
	having sum(amountToAdjust) <> 0
	order by min(autoid)


	-- loop over the final adjustments to make. 
	set @AdjToMakeAutoID = null
	select @AdjToMakeAutoID = min(autoid) from @tblAdjToMakeFinal
	while @AdjToMakeAutoID is not null BEGIN
		select	@AdjToMakeTID=saleTransactionID, @AdjToMakeAmt=amountToAdjust, 
				@AdjToMakeIsSale=issale, @AdjToMakeGLAID=debitGLAID, @AdjToMakeDetail=detail
		from @tblAdjToMakeFinal 
		where autoid = @AdjToMakeAutoID

		-- insert adj into transactions (ensure amount is abs)
		INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
			amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
			typeID, accrualDate, debitGLAccountID, creditGLAccountID)
		VALUES (@origSaleOwnedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID(@status), @AdjToMakeDetail, null, 
			abs(@AdjToMakeAmt), getdate(), @transactionDate, @origSaleAssignedToMemberID, @recordedByMemberID, @statsSessionID, 
			dbo.fn_tr_getTypeID('Adjustment'), @transactionDate, @AdjToMakeGLAID, @ARGLAccountID)
		IF @AdjToMakeIsSale = 1
			select @AdjSaleTransactionID = SCOPE_IDENTITY()
		ELSE
			select @AdjTaxTransactionID = SCOPE_IDENTITY()

		-- insert adj into relationships
		-- tie tax to the sale adjustment
		IF @AdjToMakeIsSale = 0	BEGIN
			INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
			VALUES (dbo.fn_tr_getRelationshipTypeID('AdjustTrans'), @AdjTaxTransactionID, @AdjToMakeTID)

			INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
			VALUES (dbo.fn_tr_getRelationshipTypeID('PITTaxTrans'), @AdjTaxTransactionID, @AdjSaleTransactionID)
		END
		ELSE BEGIN
			INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
			VALUES (dbo.fn_tr_getRelationshipTypeID('AdjustTrans'), @AdjSaleTransactionID, @AdjToMakeTID)
		END

		-- put adj on invoice (0 dollars.. no neg amounts here)
		IF @AdjToMakeIsSale = 1 BEGIN			
			SELECT @contentVersionID = null
			SELECT @contentVersionID = max(cv.contentVersionID)
				FROM dbo.tr_glAccounts as gl
				INNER JOIN dbo.cms_content as c on c.contentID = gl.invoiceContentID
				INNER JOIN dbo.cms_siteResources sr	on sr.siteResourceID = c.siteResourceID 
				INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
				INNER JOIN dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID AND cl.languageID = 1
				INNER JOIN dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID
				WHERE gl.GLAccountID = @AdjToMakeGLAID
				AND cv.isActive = 1
				AND len(cv.rawContent) > 0
			INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, messageContentVersionID)
			VALUES (@AdjSaleTransactionID, @invoiceID, 0, 0, 0, @contentVersionID)
		END ELSE BEGIN
			INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount)
			VALUES (@AdjTaxTransactionID, @invoiceID, 0, 0, 0)
		END

		IF @AdjToMakeIsSale = 1 begin	
			update @tblAdjToMake set adjTransactionID = @AdjSaleTransactionID where saleTransactionID = @AdjToMakeTID
			update @tblAdjToMakeFinal set adjTransactionID = @AdjSaleTransactionID where autoID = @AdjToMakeAutoID
		end ELSE begin
			update @tblAdjToMake set adjTransactionID = @AdjTaxTransactionID where saleTransactionID = @AdjToMakeTID
			update @tblAdjToMakeFinal set adjTransactionID = @AdjTaxTransactionID where autoID = @AdjToMakeAutoID
		end		

		select @AdjToMakeAutoID = min(autoid) from @tblAdjToMakeFinal where autoid > @AdjToMakeAutoID
	end


	-- update tr_invoiceTransaction cache, add AdjustInvTrans relationship, and cleanup invoices
	update it
	set it.cache_invoiceAmountAfterAdjustment = it.cache_invoiceAmountAfterAdjustment - abs(tbl.amountToAdjust)
	from dbo.tr_invoiceTransactions as it
	inner join @tblAdjToMake as tbl on tbl.saleAdjTID = it.transactionID

	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID, amount)
	SELECT dbo.fn_tr_getRelationshipTypeID('AdjustInvTrans'), tbl.adjTransactionID, tbl.saleAdjTID, abs(tbl.amountToAdjust)
	FROM @tblAdjToMake as tbl

	-- if invoice is closed and is now fully paid with active payments, mark it as paid
	-- if invoice is paid and is now not fully paid with active payments, mark it as closed
	select @it_invoiceID = min(SaleAdjInvoiceID) from @tblAdjToMake
	while @it_invoiceID is not null begin
		select @it_invstatus=ins.status
		from dbo.tr_invoices as i 
		inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID			
		where i.invoiceID = @it_invoiceID

		select @amtDueNoPendingOnInvoice = sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount)
			from dbo.tr_invoiceTransactions as it
			inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
			where it.invoiceID = @it_invoiceID
			and t.statusID = 1
		IF @it_invstatus = 'closed' and @amtDueNoPendingOnInvoice = 0 BEGIN
			update dbo.tr_invoices
			set statusID = 4, payProfileID = null
			where invoiceID = @it_invoiceID

			insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
			values (@it_invoiceID, getdate(), 4, 3, @recordedByMemberID)
		END
		IF @it_invstatus = 'paid' and @amtDueNoPendingOnInvoice > 0 BEGIN
			update dbo.tr_invoices
			set statusID = 3
			where invoiceID = @it_invoiceID

			insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
			values (@it_invoiceID, getdate(), 3, 4, @recordedByMemberID)
		END
					
		select @it_invoiceID = min(SaleAdjInvoiceID) from @tblAdjToMake where SaleAdjInvoiceID > @it_invoiceID
	end


	-- update tr_transactionSales cache, add AdjustTSaleTrans relationship
	update ts
	set ts.cache_amountAfterAdjustment = ts.cache_amountAfterAdjustment - abs(tbl.amountToAdjust)
	from dbo.tr_transactionSales as ts
	inner join @tblAdjToMakeFinal as tbl on tbl.saleTransactionID = ts.transactionID

	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID, amount)
	SELECT dbo.fn_tr_getRelationshipTypeID('AdjustTSaleTrans'), tbl.adjTransactionID, tbl.saleTransactionID, abs(tbl.amountToAdjust)
	FROM @tblAdjToMakeFinal as tbl


	-- respread any amounts over recognition dates. respread 0 amounts as well to serve as placeholders.
	select @respreadAutoID = min(autoID) from @tblDITSpread
	while @respreadAutoID is not null begin
		set @ditTransactionID = null

		select @amtToRespread=respreadAmount, @ditSpreadRecogDt=recogDt, @respreadDebitGL=ditdebitGLAID,
			@respreadCreditGL=ditcreditGLAID, @respreadsaleAdjTID=saleAdjTID
		from @tblDITSpread 
		where autoID = @respreadAutoID
	
		-- put all scheduled rows here for auditing, even if they are immediately recognized
		EXEC dbo.tr_createTransaction_dit @recordedOnSiteID=@recordedOnSiteID, 
			@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
			@amount=@amtToRespread, @transactionDate=@transactionDate, @recognitionDate=@ditSpreadRecogDt, 
			@debitGLAccountID=@respreadDebitGL, @creditGLAccountID=@respreadCreditGL, 
			@saleTransactionID=@respreadsaleAdjTID, @DITTransactionID=null, @batchAsRecogJob=0, 
			@transactionID=@ditTransactionID OUTPUT

		-- if recognition date is today or in past, then move it out immediately back to revenue
		IF @ditSpreadRecogDt < getdate() BEGIN
			set @amtToRespread = @amtToRespread * -1
			EXEC dbo.tr_createTransaction_dit @recordedOnSiteID=@recordedOnSiteID, 
				@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
				@amount=@amtToRespread, @transactionDate=@transactionDate, @recognitionDate=null, 
				@debitGLAccountID=@respreadCreditGL, @creditGLAccountID=@respreadDebitGL, 
				@saleTransactionID=@respreadsaleAdjTID, @DITTransactionID=@ditTransactionID, @batchAsRecogJob=0, 
				@transactionID=@ditTransactionID2 OUTPUT
		END

		select @respreadAutoID = min(autoID) from @tblDITSpread where autoID > @respreadAutoID
	end


	IF @TranCounter = 0
		COMMIT TRAN;
	SELECT @transactionID = @AdjSaleTransactionID
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @transactionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[tr_createTransaction_adjustment_up]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@status varchar(20),
@amount money,
@transactionDate datetime,
@saleTransactionID int,
@invoiceID int,
@xmlSchedule xml = null, 
@transactionID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- init output param
	select @transactionID = 0

	declare @origSaleOwnedByOrgID int, @origSaleAssignedToMemberID int, @origCreditGLAccountID int, @ARGLAccountID int, 
			@AdjSaleTransactionID int, @taxTransactionID int, @AdjTaxTransactionID int, @invoiceProfileID int, 
			@contentVersionID int, @ditTransactionID int, @ditTransactionID2 int, @AdjDeferredGLAccountID int, 
			@minSchedRow int, @minTaxrow int, @taxGLAID int, @taxDeferredGLAccountID int, @TaxAdjDeferredGLAccountID int, 
			@TaxminSchedRow int, @taxditTransactionID int, @taxditTransactionID2 int, @PITTaxTrans int, @PITTaxTrans2 int
	declare @amtLeftToDeallocate money, @AllocAmt money, @DeallocateAmtNeg money
	declare @invoiceNumber varchar(18)
	declare @origSaleDetail varchar(max), @taxdetail varchar(max)
	declare @TaxNeeded bit
	declare @rowAmt decimal(10,2), @taxAmt decimal(10,2), @taxSum decimal(10,2), @taxDiff decimal(10,2), @taxrowAmt decimal(10,2)
	declare @rowDt datetime, @taxrowDt datetime
	declare @taxXMLSchedule xml
	declare @tblTax TABLE (row int, taxAuthorityID int, taxGLAccountID int, taxDeferredGLAccountID int, authorityName varchar(200), taxRuleID int, taxRate float, taxAmount money) 
	declare @tblDeferredSchedule TABLE (rowID int, amt decimal(10,2), dt datetime, pct decimal(10,2), ditTID int, dit2TID int)		
	declare @tblTaxDeferredSchedule TABLE (rowID int, amt decimal(10,2), dt datetime, pct decimal(10,2), ditTID int, dit2TID int)		


	-- get data from sale transaction
	select @origSaleOwnedByOrgID=ownedByOrgID, @origSaleAssignedToMemberID=assignedToMemberID, 
		@origSaleDetail=detail, @origCreditGLAccountID=creditGLAccountID
		from dbo.tr_transactions
		where transactionID = @saleTransactionID

	-- dont assume memberid is the active one. get the active one.
	select @origSaleAssignedToMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @origSaleAssignedToMemberID 
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	select @ARGLAccountID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @origSaleOwnedByOrgID
		and isSystemAccount = 1
		and GLCode = 'ACCOUNTSRECEIVABLE'
		and [status] = 'A'
	IF @ARGLAccountID is null
		RAISERROR('ARGLAccountID is null', 16, 1);

	-- if invoiceID is null, not an open/pending invoice, or inv profile doesnt match revenue GL, assume need to create a new one.
	IF @invoiceID is null 
		OR NOT EXISTS (select invoiceID from dbo.tr_invoices where invoiceID = @invoiceID and statusID in (1,2))
		OR ((select dbo.fn_tr_doesInvoiceProfileSupportRevenueGL(@invoiceID,@origCreditGLAccountID)) = 0)
	BEGIN
		select @invoiceProfileID=invoiceProfileID from dbo.tr_GLAccounts where GLAccountID = @origCreditGLAccountID
			IF @invoiceProfileID is null RAISERROR('invoiceProfileID is null', 16, 1);

		EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID,
			@enteredByMemberID=@recordedByMemberID, 
			@assignedToMemberID=@origSaleAssignedToMemberID,
			@dateBilled=@transactionDate, @dateDue=@transactionDate, 
			@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT
	END
	
	-- insert adj into transactions
	-- ensure amount is abs
	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
		amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
		typeID, accrualDate, debitGLAccountID, creditGLAccountID)
	VALUES (@origSaleOwnedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID(@status), @origSaleDetail, null, 
		@amount, getdate(), @transactionDate, @origSaleAssignedToMemberID, @recordedByMemberID, @statsSessionID, 
		dbo.fn_tr_getTypeID('Adjustment'), @transactionDate, @ARGLAccountID, @origCreditGLAccountID)
		select @AdjSaleTransactionID = SCOPE_IDENTITY()

	-- insert adj into relationships
	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
	VALUES (dbo.fn_tr_getRelationshipTypeID('AdjustTrans'), @AdjSaleTransactionID, @saleTransactionID)

	-- put adj on invoice
	SELECT @contentVersionID = null
	SELECT @contentVersionID = max(cv.contentVersionID)
		FROM dbo.tr_glAccounts as gl
		INNER JOIN dbo.cms_content as c on c.contentID = gl.invoiceContentID
		INNER JOIN dbo.cms_siteResources sr	on sr.siteResourceID = c.siteResourceID 
		INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
		INNER JOIN dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID AND cl.languageID = 1
		INNER JOIN dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID
		WHERE gl.GLAccountID = @origCreditGLAccountID
		AND cv.isActive = 1
		AND len(cv.rawContent) > 0
	INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, messageContentVersionID)
	VALUES (@AdjSaleTransactionID, @invoiceID, @amount, 0, 0, @contentVersionID)

	-- update cache
	UPDATE dbo.tr_transactionSales
	SET cache_amountAfterAdjustment = cache_amountAfterAdjustment + @amount
	WHERE transactionID = @saleTransactionID

	
	-- determine sales tax. Just get information - we'll record it after deferred is recorded.
	-- we need to do this before the deferred entries are written because the deferred entries adjust the tr_transactionSales amount used by tax determination.
	set @TaxNeeded = 0
	IF EXISTS (select glAccountID from dbo.tr_glAccounts where glAccountID = @origCreditGLAccountID and isTaxable = 1) BEGIN
		insert into @tblTax	(row, taxAuthorityID, taxGLAccountID, taxDeferredGLAccountID, authorityName, taxRuleID, taxRate, taxAmount)
		select row, taxAuthorityID, taxGLAccountID, deferredGLAccountID, authorityName, taxRuleID, taxRate, taxAmount
		from dbo.fn_tr_getTaxForAdjustment(@AdjSaleTransactionID)
		where taxAmount > 0

		set @TaxNeeded = 1
	END


	-- prepare deferred schedule
	insert into @tblDeferredSchedule (rowID, amt, dt, pct, ditTID, dit2TID)
	select rowID, amt, dt, pct, ditTID, dit2TID
	from dbo.fn_tr_prepareDeferredScheduleTable(@xmlSchedule, @amount, @transactionDate)

	-- record deferred schedule for sale if necessary
	select @AdjDeferredGLAccountID = dbo.fn_tr_getDeferredGLAccountID(@origCreditGLAccountID)
	IF @AdjDeferredGLAccountID is not null BEGIN
		select @minSchedRow = min(rowID) from @tblDeferredSchedule
		WHILE @minSchedRow is not null BEGIN
			select @rowAmt=amt, @rowDt=dt 
			from @tblDeferredSchedule 
			where rowID = @minSchedRow

			-- put all scheduled rows here for auditing, even if they are immediately recognized
			EXEC dbo.tr_createTransaction_dit @recordedOnSiteID=@recordedOnSiteID, 
				@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
				@amount=@rowAmt, @transactionDate=@transactionDate, @recognitionDate=@rowDt, 
				@debitGLAccountID=@origCreditGLAccountID, @creditGLAccountID=@AdjDeferredGLAccountID, 
				@saleTransactionID=@AdjSaleTransactionID, @DITTransactionID=null, @batchAsRecogJob=0, 
				@transactionID=@ditTransactionID OUTPUT
			update @tblDeferredSchedule set ditTID = @ditTransactionID where rowID = @minSchedRow

			-- if recognition date is today or in past, then move it out immediately back to revenue
			IF @rowDt < getdate() BEGIN
				set @rowAmt = @rowAmt * -1
				EXEC dbo.tr_createTransaction_dit @recordedOnSiteID=@recordedOnSiteID, 
					@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
					@amount=@rowAmt, @transactionDate=@transactionDate, @recognitionDate=null, 
					@debitGLAccountID=@AdjDeferredGLAccountID, @creditGLAccountID=@origCreditGLAccountID, 
					@saleTransactionID=@AdjSaleTransactionID, @DITTransactionID=@ditTransactionID, 
					@batchAsRecogJob=0, @transactionID=@ditTransactionID2 OUTPUT
				update @tblDeferredSchedule set dit2TID = @ditTransactionID2 where rowID = @minSchedRow
			END

			select @minSchedRow = min(rowID) from @tblDeferredSchedule where rowID > @minSchedRow
		END
	END


	-- record sales tax
	IF @TaxNeeded = 1 BEGIN
		select @minTaxrow = min(row) from @tblTax
		WHILE @minTaxrow is not null BEGIN
			set @taxXMLSchedule = null			
			
			select @taxAmt=taxAmount, @taxGLAID=taxGLAccountID, @taxDeferredGLAccountID = taxDeferredGLAccountID, 
				@taxdetail = 'Sales Tax: ' + authorityName + ' @ ' + cast(taxRate*100 as varchar(10)) + '%'
			from @tblTax 
			where row=@minTaxrow

			-- if deferring tax, calculate percentage of SAME schedule as adj and put remainder on first row
			IF @taxDeferredGLAccountID is not null BEGIN
				update @tblDeferredSchedule set amt = @taxAmt * pct
				select @taxSum = sum(amt) from @tblDeferredSchedule
				set @taxDiff = @taxAmt - @taxSum
				if @taxDiff <> 0
					update @tblDeferredSchedule set amt = amt + @taxDiff where rowID = 1

				SELECT @taxXMLSchedule = (
					select amt, convert(varchar(10),dt,101) as dt, ditTID as dittid, dit2TID as dit2tid
					from @tblDeferredSchedule as row
					for XML AUTO, ROOT('rows'), TYPE
					)
			END

			-- is there already a tax transaction using this acct code for this sale? if so, adjust it
			SELECT @taxTransactionID = null
			SELECT @taxTransactionID = tTax.transactionID
				FROM dbo.tr_transactions as tTax
				INNER JOIN dbo.tr_relationships AS r ON r.transactionID = tTax.transactionID AND r.appliedToTransactionID = @saleTransactionID
				INNER JOIN dbo.tr_relationshipTypes AS rt ON rt.typeID = r.typeID AND rt.type = 'SalesTaxTrans'
				WHERE tTax.typeID = 7
				AND tTax.statusID = 1
				AND tTax.creditGLAccountID = @taxGLAID
			IF @taxTransactionID is not null BEGIN

				-- insert adj into transactions
				INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
					amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
					typeID, accrualDate, debitGLAccountID, creditGLAccountID)
				VALUES (@origSaleOwnedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID(@status), @taxdetail, null, 
					@taxAmt, getdate(), @transactionDate, @origSaleAssignedToMemberID, @recordedByMemberID, @statsSessionID, 
					dbo.fn_tr_getTypeID('Adjustment'), @transactionDate, @ARGLAccountID, @taxGLAID)
					select @AdjTaxTransactionID = SCOPE_IDENTITY()

				-- insert adj into relationships
				-- tie tax to the sale adjustment
				INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
				VALUES (dbo.fn_tr_getRelationshipTypeID('AdjustTrans'), @AdjTaxTransactionID, @taxTransactionID)
				
				INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
				VALUES (dbo.fn_tr_getRelationshipTypeID('PITTaxTrans'), @AdjTaxTransactionID, @AdjSaleTransactionID)

				-- put adj on invoice
				INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount)
				VALUES (@AdjTaxTransactionID, @invoiceID, @taxAmt, 0, 0)

				-- update cache
				UPDATE dbo.tr_transactionSales
				SET cache_amountAfterAdjustment = cache_amountAfterAdjustment + @taxAmt
				WHERE transactionID = @taxTransactionID


				-- prepare deferred schedule
				insert into @tblTaxDeferredSchedule (rowID, amt, dt, pct, ditTID, dit2TID)
				select rowID, amt, dt, pct, ditTID, dit2TID
				from dbo.fn_tr_prepareDeferredScheduleTable(@taxXMLSchedule, @taxAmt, @transactionDate)

				-- record deferred schedule for tax if necessary
				select @TaxAdjDeferredGLAccountID = dbo.fn_tr_getDeferredGLAccountID(@taxGLAID)
				IF @TaxAdjDeferredGLAccountID is not null BEGIN
					select @TaxminSchedRow = min(rowID) from @tblTaxDeferredSchedule
					WHILE @TaxminSchedRow is not null BEGIN
						select @taxrowAmt=null, @taxrowDt=null, @PITTaxTrans=null, @PITTaxTrans2=null
						select @taxrowAmt=amt, @taxrowDt=dt, @PITTaxTrans=ditTID, @PITTaxTrans2=dit2TID
						from @tblTaxDeferredSchedule 
						where rowID = @TaxminSchedRow

						-- put all scheduled rows here for auditing, even if they are immediately recognized
						EXEC dbo.tr_createTransaction_dit @recordedOnSiteID=@recordedOnSiteID, 
							@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
							@amount=@taxrowAmt, @transactionDate=@transactionDate, @recognitionDate=@taxrowDt, 
							@debitGLAccountID=@taxGLAID, @creditGLAccountID=@TaxAdjDeferredGLAccountID, 
							@saleTransactionID=@AdjTaxTransactionID, @DITTransactionID=null, @batchAsRecogJob=0, 
							@transactionID=@taxditTransactionID OUTPUT

						IF @PITTaxTrans is not null 
							INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
							VALUES (dbo.fn_tr_getRelationshipTypeID('PITTaxTrans'), @taxditTransactionID, @PITTaxTrans)

						-- if recognition date is today or in past, then move it out immediately back to revenue
						IF @taxrowDt < getdate() BEGIN
							set @taxrowAmt = @taxrowAmt * -1
							EXEC dbo.tr_createTransaction_dit @recordedOnSiteID=@recordedOnSiteID, 
								@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
								@amount=@taxrowAmt, @transactionDate=@transactionDate, @recognitionDate=null, 
								@debitGLAccountID=@TaxAdjDeferredGLAccountID, @creditGLAccountID=@taxGLAID, 
								@saleTransactionID=@AdjTaxTransactionID, @DITTransactionID=@taxditTransactionID, 
								@batchAsRecogJob=0, @transactionID=@taxditTransactionID2 OUTPUT

							IF @PITTaxTrans2 is not null 
								INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
								VALUES (dbo.fn_tr_getRelationshipTypeID('PITTaxTrans'), @taxditTransactionID2, @PITTaxTrans2)
						END

						select @TaxminSchedRow = min(rowID) from @tblTaxDeferredSchedule where rowID > @TaxminSchedRow
					END
				END

			END 

			-- else, create a new sales tax transaction.
			ELSE BEGIN

				EXEC dbo.tr_createTransaction_salesTax @ownedByOrgID=@origSaleOwnedByOrgID, 
					@recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
					@statsSessionID=@statsSessionID, @status=@status, @detail=@taxdetail, 
					@amount=@taxAmt, @transactionDate=@transactionDate, 
					@creditGLAccountID=@taxGLAID, @saleTransactionID=@saleTransactionID, 
					@invoiceID=@invoiceID, @xmlSchedule=@taxXMLSchedule, @transactionID=@taxTransactionID OUTPUT

				-- tie tax to the sale adjustment
				INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
				VALUES (dbo.fn_tr_getRelationshipTypeID('PITTaxTrans'), @taxTransactionID, @AdjSaleTransactionID)

			END

			select @minTaxrow = min(row) from @tblTax where row > @minTaxrow
		END
	END

	IF @TranCounter = 0
		COMMIT TRAN;
	SELECT @transactionID = @AdjSaleTransactionID
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @transactionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_createTransaction_sale]
@ownedByOrgID int,
@recordedOnSiteID int,
@assignedToMemberID int,
@recordedByMemberID int,
@statsSessionID int,
@status varchar(20),
@detail varchar(max),
@parentTransactionID int,
@amount money,
@transactionDate datetime,
@accrualDate datetime,
@creditGLAccountID int,
@invoiceID int,
@stateIDForTax int = null, -- null to use stateid from member record
@bypassTax bit = 0,
@xmlSchedule xml = null, 
@transactionID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- init output param
	select @transactionID = 0

	-- dont assume memberid is the active one. get the active one.
	select @assignedToMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @assignedToMemberID 
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	-- get AR account. this is the debit account.
	declare @debitGLAccountID int
	select @debitGLAccountID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and isSystemAccount = 1
		and GLCode = 'ACCOUNTSRECEIVABLE'
		and [status] = 'A'

	-- ensure amount is abs
	select @amount = cast(abs(@amount) as decimal(10,2))

	declare @statusID int
	select @statusID = dbo.fn_tr_getStatusID(@status)

	-- verify credit account accepts new transactions
	-- ensure we have active debit/credit accts
	IF @debitGLAccountID is null or NOT EXISTS (
		select glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and glaccountID = @creditGLAccountID
		and [status] = 'A')
		RAISERROR('credit account does not accept new transactions', 16, 1);

	-- if invoiceID is null, not an open invoice, or inv profile doesnt match revenue GL, reject sale.
	IF @invoiceID is null 
		OR NOT EXISTS (select invoiceID from dbo.tr_invoices where invoiceID = @invoiceID and statusID = 1)
		OR ((select dbo.fn_tr_doesInvoiceProfileSupportRevenueGL(@invoiceID,@creditGLAccountID)) = 0)
		RAISERROR('invoiceid not eligible', 16, 1);

	-- insert into transactions
	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
		amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
		typeID, accrualDate, debitGLAccountID, creditGLAccountID)
	VALUES (@ownedByOrgID, @recordedOnSiteID, @statusID, @detail, @parentTransactionID, 
		@amount, getdate(), @transactionDate, @assignedToMemberID, @recordedByMemberID, @statsSessionID, 
		dbo.fn_tr_getTypeID('Sale'), @accrualDate, @debitGLAccountID, @creditGLAccountID)
		select @transactionID = SCOPE_IDENTITY()

	-- if stateIDforTax is null, get state on member record to record in the transactionSales table
	if nullif(@stateIDForTax,0) is null BEGIN
		select @stateIDForTax = ma.stateID
		from dbo.ams_memberAddresses as ma 
		inner join dbo.ams_members as m on m.memberID = ma.memberID
		where ma.memberid = @assignedToMemberID
		and ma.addressTypeID = m.billingAddressTypeID
	end

	-- insert into transactionSales
	INSERT INTO dbo.tr_transactionSales (transactionID, cache_amountAfterAdjustment, 
		cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, stateIDForTax)
	VALUES (@transactionID, @amount, 0, 0, nullif(@stateIDForTax,0))

	-- put sale on invoice
	declare @contentVersionID int
	SELECT @contentVersionID = max(cv.contentVersionID)
		FROM dbo.tr_glAccounts as gl
		INNER JOIN dbo.cms_content as c on c.contentID = gl.invoiceContentID
		INNER JOIN dbo.cms_siteResources sr	on sr.siteResourceID = c.siteResourceID 
		INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
		INNER JOIN dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID AND cl.languageID = 1
		INNER JOIN dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID
		WHERE gl.GLAccountID = @creditGLAccountID
		AND cv.isActive = 1
		AND len(cv.rawContent) > 0
	INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, messageContentVersionID)
	VALUES (@transactionID, @invoiceID, @amount, 0, 0, @contentVersionID)


	-- determine sales tax. Just get information - we'll record it after deferred is recorded.
	-- we need to do this before the deferred entries are written because the deferred entries adjust the tr_transactionSales amount used by tax determination.
	declare @TaxNeeded bit
	set @TaxNeeded = 0
	IF @amount > 0 AND @bypassTax = 0 and EXISTS (select glAccountID from dbo.tr_glAccounts where glAccountID = @creditGLAccountID and isTaxable = 1) BEGIN
		DECLARE @tblTax TABLE (row int, taxAuthorityID int, taxGLAccountID int, taxDeferredGLAccountID int, authorityName varchar(200), taxRuleID int, taxRate float, taxAmount money) 
		insert into @tblTax	(row, taxAuthorityID, taxGLAccountID, taxDeferredGLAccountID, authorityName, taxRuleID, taxRate, taxAmount)
		select row, taxAuthorityID, taxGLAccountID, deferredGLAccountID, authorityName, taxRuleID, taxRate, taxAmount
		from dbo.fn_tr_getTaxForSale(@transactionID,@stateIDForTax)
		where taxAmount > 0

		set @TaxNeeded = 1
	END


	-- prepare deferred schedule
	declare @tblDeferredSchedule TABLE (rowID int, amt decimal(10,2), dt datetime, pct decimal(10,2), ditTID int, dit2TID int)		
	insert into @tblDeferredSchedule (rowID, amt, dt, pct, ditTID, dit2TID)
	select rowID, amt, dt, pct, ditTID, dit2TID
	from dbo.fn_tr_prepareDeferredScheduleTable(@xmlSchedule, @amount, @transactionDate)

	-- record deferred schedule for sale if necessary
	declare @SaleDeferredGLAccountID int
	select @SaleDeferredGLAccountID = dbo.fn_tr_getDeferredGLAccountID(@creditGLAccountID)
	IF @SaleDeferredGLAccountID is not null BEGIN
		declare @minSchedRow int, @rowAmt decimal(10,2), @rowDt datetime, @ditTransactionID int, @ditTransactionID2 int
		select @minSchedRow = min(rowID) from @tblDeferredSchedule
		WHILE @minSchedRow is not null BEGIN
			select @rowAmt=amt, @rowDt=dt 
				from @tblDeferredSchedule 
				where rowID = @minSchedRow

			-- put all scheduled rows here for auditing, even if they are immediately recognized
			EXEC dbo.tr_createTransaction_dit @recordedOnSiteID=@recordedOnSiteID, 
				@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
				@amount=@rowAmt, @transactionDate=@transactionDate, @recognitionDate=@rowDt, 
				@debitGLAccountID=@creditGLAccountID, @creditGLAccountID=@SaleDeferredGLAccountID, 
				@saleTransactionID=@transactionID, @DITTransactionID=null, @batchAsRecogJob=0, 
				@transactionID=@ditTransactionID OUTPUT
			update @tblDeferredSchedule set ditTID = @ditTransactionID where rowID = @minSchedRow

			-- if recognition date is today or in past, then move it out immediately back to revenue
			IF @rowDt < getdate() BEGIN
				set @rowAmt = @rowAmt * -1
				EXEC dbo.tr_createTransaction_dit @recordedOnSiteID=@recordedOnSiteID, 
					@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
					@amount=@rowAmt, @transactionDate=@transactionDate, @recognitionDate=null, 
					@debitGLAccountID=@SaleDeferredGLAccountID, @creditGLAccountID=@creditGLAccountID, 
					@saleTransactionID=@transactionID, @DITTransactionID=@ditTransactionID, @batchAsRecogJob=0, 
					@transactionID=@ditTransactionID2 OUTPUT
				update @tblDeferredSchedule set dit2TID = @ditTransactionID2 where rowID = @minSchedRow
			END

			select @minSchedRow = min(rowID) from @tblDeferredSchedule where rowID > @minSchedRow
		END
	END
	

	-- record sales tax
	IF @TaxNeeded = 1 BEGIN
		DECLARE @minTaxrow int, @taxTransactionID int, @taxGLAID int, @taxDeferredGLAccountID int, @taxAmt decimal(10,2), @taxdetail varchar(max)
		DECLARE @taxSum decimal(10,2), @taxDiff decimal(10,2), @taxXMLSchedule xml
		select @minTaxrow = min(row) from @tblTax
		WHILE @minTaxrow is not null BEGIN
			set @taxXMLSchedule = null			
			select @taxAmt=taxAmount, @taxGLAID=taxGLAccountID, @taxDeferredGLAccountID = taxDeferredGLAccountID, 
				@taxdetail = 'Sales Tax: ' + authorityName + ' @ ' + cast(taxRate*100 as varchar(10)) + '%'
				from @tblTax 
				where row=@minTaxrow

			-- if deferring tax, calculate percentage of SAME schedule as sale and put remainder on first row
			IF @taxDeferredGLAccountID is not null BEGIN
				update @tblDeferredSchedule set amt = @taxAmt * pct
				select @taxSum = sum(amt) from @tblDeferredSchedule
				select @taxDiff = @taxAmt - @taxSum
				if @taxDiff <> 0
					update @tblDeferredSchedule set amt = amt + @taxDiff where rowID = 1

				SELECT @taxXMLSchedule = (
					select amt, convert(varchar(10),dt,101) as dt, ditTID as dittid, dit2TID as dit2tid
					from @tblDeferredSchedule as row
					for XML AUTO, ROOT('rows'), TYPE
					)
			END

			EXEC dbo.tr_createTransaction_salesTax @ownedByOrgID=@ownedByOrgID, 
				@recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
				@statsSessionID=@statsSessionID, @status=@status, @detail=@taxdetail, 
				@amount=@taxAmt, @transactionDate=@transactionDate, @creditGLAccountID=@taxGLAID, 
				@saleTransactionID=@transactionID, @invoiceID=@invoiceID, @xmlSchedule=@taxXMLSchedule, 
				@transactionID=@taxTransactionID OUTPUT

			INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
			VALUES (dbo.fn_tr_getRelationshipTypeID('PITTaxTrans'), @taxTransactionID, @transactionID)

			select @minTaxrow = min(row) from @tblTax where row > @minTaxrow
		END
	END


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @transactionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO


ALTER PROC [dbo].[tr_createTransaction_salesTax]
@ownedByOrgID int,
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@status varchar(20),
@detail varchar(max),
@amount money,
@transactionDate datetime,
@creditGLAccountID int,
@saleTransactionID int,
@invoiceID int,
@xmlSchedule xml = null, 
@transactionID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- init output param
	select @transactionID = 0

	-- ensure @saleTransactionID is a sale
	IF EXISTS (select transactionID from dbo.tr_transactions where transactionID = @saleTransactionID and typeID <> 1)
		RAISERROR('saleTransactionID is not a sale', 16, 1);

	-- get assignedToMemberID, stateIDforTax from sale transaction
	declare @assignedToMemberID int, @stateIDForTax int
	select @assignedToMemberID = t.assignedToMemberID, @stateIDForTax = ts.stateIDForTax
		from dbo.tr_transactions as t
		inner join dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID
		where t.transactionID = @saleTransactionID

	-- dont assume memberid is the active one. get the active one.
	select @assignedToMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @assignedToMemberID 
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	-- get AR account. this is the debit account.
	declare @debitGLAccountID int
	select @debitGLAccountID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and isSystemAccount = 1
		and GLCode = 'ACCOUNTSRECEIVABLE'
		and [status] = 'A'

	-- ensure amount is abs
	select @amount = cast(abs(@amount) as decimal(10,2))

	-- verify credit account accepts new transactions
	-- ensure we have active debit/credit accts
	IF @debitGLAccountID is null or NOT EXISTS (
		select glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and glaccountID = @creditGLAccountID
		and [status] = 'A')
		RAISERROR('credit account does not accept new transactions', 16, 1);

	-- if invoiceID is null or not an open invoice, reject tax.
	IF @invoiceID is null OR NOT EXISTS (
		select invoiceID
		from dbo.tr_invoices
		where invoiceID = @invoiceID
		and statusID = 1)
		RAISERROR('invoiceID not an open invoice', 16, 1);

	-- insert into transactions
	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
		amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
		typeID, accrualDate, debitGLAccountID, creditGLAccountID)
	VALUES (@ownedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID(@status), @detail, null, 
		@amount, getdate(), @transactionDate, @assignedToMemberID, @recordedByMemberID, @statsSessionID, 
		dbo.fn_tr_getTypeID('Sales Tax'), @transactionDate, @debitGLAccountID, @creditGLAccountID)
		select @transactionID = SCOPE_IDENTITY()

	-- insert into transactionSales
	INSERT INTO dbo.tr_transactionSales (transactionID, cache_amountAfterAdjustment, 
		cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, stateIDForTax)
	VALUES (@transactionID, @amount, 0, 0, @stateIDForTax)

	-- insert into relationships
	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
	VALUES (dbo.fn_tr_getRelationshipTypeID('SalesTaxTrans'), @transactionID, @saleTransactionID)

	-- put on invoice
	INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount)
	VALUES (@transactionID, @invoiceID, @amount, 0, 0)


	-- record deferred schedule if necessary
	declare @DeferredGLAccountID int
	select @DeferredGLAccountID = dbo.fn_tr_getDeferredGLAccountID(@creditGLAccountID)
	IF @DeferredGLAccountID is not null BEGIN

		-- prepare deferred schedule
		declare @tblDeferredSchedule TABLE (rowID int, amt decimal(10,2), dt datetime, pct decimal(10,2), ditTID int, dit2TID int)		
		insert into @tblDeferredSchedule (rowID, amt, dt, pct, ditTID, dit2TID)
		select rowID, amt, dt, pct, ditTID, dit2TID
		from dbo.fn_tr_prepareDeferredScheduleTable(@xmlSchedule, @amount, @transactionDate)

		declare @minSchedRow int, @rowAmt decimal(10,2), @rowDt datetime, @PITTaxTrans int, @PITTaxTrans2 int, @ditTransactionID int, @ditTransactionID2 int
		select @minSchedRow = min(rowID) from @tblDeferredSchedule
		WHILE @minSchedRow is not null BEGIN
			select @rowAmt=null, @rowDt=null, @PITTaxTrans=null, @PITTaxTrans2=null
			select @rowAmt=amt, @rowDt=dt, @PITTaxTrans=ditTID, @PITTaxTrans2=dit2TID
				from @tblDeferredSchedule 
				where rowID = @minSchedRow

			-- put all scheduled rows here for auditing, even if they are immediately recognized
			EXEC dbo.tr_createTransaction_dit @recordedOnSiteID=@recordedOnSiteID, 
				@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
				@amount=@rowAmt, @transactionDate=@transactionDate, @recognitionDate=@rowDt, 
				@debitGLAccountID=@creditGLAccountID, @creditGLAccountID=@DeferredGLAccountID, 
				@saleTransactionID=@transactionID, @DITTransactionID=null, @batchAsRecogJob=0, 
				@transactionID=@ditTransactionID OUTPUT

			IF @PITTaxTrans is not null 
				INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
				VALUES (dbo.fn_tr_getRelationshipTypeID('PITTaxTrans'), @ditTransactionID, @PITTaxTrans)

			-- if recognition date is today or in past, then move it out immediately back to revenue
			IF @rowDt < getdate() BEGIN
				set @rowAmt = @rowAmt * -1
				EXEC dbo.tr_createTransaction_dit @recordedOnSiteID=@recordedOnSiteID, 
					@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
					@amount=@rowAmt, @transactionDate=@transactionDate, @recognitionDate=null, 
					@debitGLAccountID=@DeferredGLAccountID, @creditGLAccountID=@creditGLAccountID, 
					@saleTransactionID=@transactionID, @DITTransactionID=@ditTransactionID, 
					@batchAsRecogJob=0, @transactionID=@ditTransactionID2 OUTPUT

				IF @PITTaxTrans2 is not null 
					INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
					VALUES (dbo.fn_tr_getRelationshipTypeID('PITTaxTrans'), @ditTransactionID2, @PITTaxTrans2)
			END

			select @minSchedRow = min(rowID) from @tblDeferredSchedule where rowID > @minSchedRow
		END
	END


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @transactionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO


ALTER PROC [dbo].[tr_voidTransaction_adjustment]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@transactionID int,
@vidPool xml OUTPUT,
@tids xml OUTPUT

AS

/* ***********************************************
	DO NOT CALL THIS PROC DIRECTLY!
	THIS SHOULD ONLY BE CALLED FROM WITHIN 
	tr_voidTransaction
************************************************** */
set nocount on

declare @offsetTransactionID int, @statusID int, @minTID int, @minAID int
declare @tblVoided TABLE (transactionid int)
declare @tidsdeep xml
select @statusID = dbo.fn_tr_getStatusID('Voided')
select @tids = null


-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- create offset trans
	EXEC dbo.tr_createTransaction_voidOffset @recordedOnSiteID=@recordedOnSiteID,
		@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID,
		@appliedToTransactionID=@transactionID, @vidPool=@vidPool OUTPUT,
		@transactionID=@offsetTransactionID OUTPUT
	insert into @tblVoided (transactionid) 
	values (@offsetTransactionID)

	-- update transaction status
	UPDATE dbo.tr_transactions
	SET statusID = @statusID
	WHERE transactionID = @transactionID

	insert into @tblVoided (transactionid) 
	values (@transactionID)

	-- void any related transactions
	declare @toVoid TABLE (autoid int IDENTITY(1,1), transactionID int, dateRecorded datetime)
	insert into @toVoid (transactionID, dateRecorded)
	select transactionID, dateRecorded
	from dbo.fn_tr_getRelatedTransactionsToVoid_adjustment(@transactionID)
	ORDER BY dateRecorded desc, transactionID desc	

	SELECT @minAID = null
	SELECT @minAID = min(autoid) FROM @toVoid
	while @minAID is not null BEGIN
		SELECT @minTID = null
		SELECT @minTID = transactionID from @toVoid where autoID = @minAID

		-- checkInBounds should be 0 on these nested voids
		EXEC dbo.tr_voidTransaction @recordedOnSiteID=@recordedOnSiteID, 
			@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
			@transactionID=@minTID, @checkInBounds=0, @vidPool=@vidPool OUTPUT, 
			@tids=@tidsdeep OUTPUT
		insert into @tblVoided (transactionid)
		select T.item.value('@tid','int')
		FROM @tidsdeep.nodes('/tr/t') as T(item)

		SELECT @minAID = min(autoid) FROM @toVoid where autoID > @minAID
	END

	-- get amount of adjustment
	declare @adjAmt money
	select @adjAmt = amount 
	from dbo.tr_transactions 
	where transactionID = @transactionID
		IF @adjAmt is null RAISERROR('adjAmt is null', 16, 1);

	-- if debit acct is AR, then adjustment was up (positive amt).
	-- if not, adjustment was down (negative amt).
	IF NOT EXISTS (select transactionID 
				from dbo.tr_transactions as t
				inner join dbo.tr_glAccounts as gl on gl.glAccountID = t.debitGLAccountID
					and gl.GLCode = 'ACCOUNTSRECEIVABLE'
					and gl.isSystemAccount = 1
					and t.transactionID = @transactionID) BEGIN
		SELECT @adjAmt = @adjAmt * -1
	END


	-- get sale that has been adjusted
	declare @saleTID int
	select @saleTID = r.appliedToTransactionID 
		from dbo.tr_relationships as r
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID
			and r.transactionID = @transactionID
			and rt.type = 'AdjustTrans'
		IF @saleTID is null RAISERROR('saleTID is null', 16, 1);
	
	-- update invoicetransactions
	UPDATE dbo.tr_invoiceTransactions
	SET cache_invoiceAmountAfterAdjustment = 0,
		cache_ActivePaymentAllocatedAmount = 0,
		cache_PendingPaymentAllocatedAmount = 0
	WHERE transactionID = @transactionID

	-- if negative adjustment that we're voiding, loop over adjustInvTrans and add back amounts
	IF @adjAmt < 0 BEGIN
		update it
		set it.cache_invoiceAmountAfterAdjustment = it.cache_invoiceAmountAfterAdjustment + abs(tmp.amount)
		from dbo.tr_invoiceTransactions as it
		inner join (		
			select r.appliedToTransactionID, r.amount
			from dbo.tr_invoiceTransactions as it2
			inner join dbo.tr_relationships as r on r.transactionID = it2.transactionID and it2.transactionID = @transactionID
			inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustInvTrans'
		) as tmp on tmp.appliedToTransactionID = it.transactionID
	END

	-- update cache
	IF @adjAmt > 0 BEGIN
		UPDATE dbo.tr_transactionSales
		SET cache_amountAfterAdjustment = cache_amountAfterAdjustment - @adjAmt
		WHERE transactionID = @saleTID
	END 
	ELSE BEGIN
		UPDATE dbo.tr_transactionSales
		SET cache_amountAfterAdjustment = cache_amountAfterAdjustment + abs(@adjAmt)
		WHERE transactionID = @saleTID
	END


	-- if negative adjustment, see if we need to respread deferred schedules
	IF @adjAmt < 0 BEGIN
		declare @tblAdjusted TABLE (autoid int IDENTITY(1,1), transactionID int, amount money)
		declare @tblDITSpread TABLE (autoID int IDENTITY(1,1) PRIMARY KEY, saleAdjTID int, ditTransactionID int, ditAmount money, ditCacheAmount money, recogDt datetime, pct numeric(10,2), ditdebitGLAID int, ditcreditGLAID int, respreadAmount money)
		declare @negadj_AutoID int, @negadj_tid int, @negadj_amount money, @negadj_DITCount int, @negadj_DITSum money, 
			@negadj_respreadSum money, @negadj_respreadFirstAutoID int, @negadj_respreadDiff money, @ditspreadAutoID int, 
			@ditSpreadDITCacheAmount money, @ditSpreadDITTID int, @ditSpreadRecogDt datetime, @ditSpreadDebitGL int, 
			@ditSpreadCreditGL int, @transactionDate datetime, @newdittransactionID int, @ditTransactionID int, @amtToRespread money
		set @transactionDate = getdate()

		-- get sale/adj transactions this neg adj adjusted
		INSERT INTO @tblAdjusted		
		SELECT r.appliedToTransactionID, r.amount
		FROM dbo.tr_relationships AS r
		INNER JOIN dbo.tr_relationshipTypes AS rt ON rt.typeID = r.typeID AND rt.type = 'AdjustInvTrans'
		WHERE r.transactionID = @transactionID
		ORDER BY r.relationshipID asc
	
		select @negadj_AutoID = min(autoid) from @tblAdjusted
		while @negadj_AutoID is not null begin
			select @negadj_tid = transactionID, @negadj_amount = amount
			from @tblAdjusted
			where autoid = @negadj_AutoID

			delete from @tblDITSpread

			-- Get all active DIT linked to tr_transactionDIT with dit amount, tr_transactionSales amount, and dit recog date.			
			insert into @tblDITSpread (ditTransactionID, ditAmount, ditCacheAmount, recogDt, ditdebitGLAID, ditcreditGLAID)
			select t.transactionID, t.amount, ts.cache_amountAfterAdjustment, dit.recognitionDate, t.debitGLAccountID, t.creditGLAccountID
			from dbo.tr_transactions as t
			inner join dbo.tr_relationships as r on r.transactionID = t.transactionID and r.appliedToTransactionID = @negadj_tid
			inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
			inner join dbo.tr_transactionDIT as dit on dit.transactionID = t.transactionID and dit.isActive = 1
			inner join dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID
			where t.statusID = 1

			-- update pct split and respread amt. we dont know original pct if everything is 0 so respread evenly
			select @negadj_DITCount = count(*) from @tblDITSpread
			select @negadj_DITSum = sum(ditAmount) from @tblDITSpread
			if @negadj_DITSum = 0 
				update @tblDITSpread set pct = (cast(100 as numeric(10,2)) / @negadj_DITCount) / 100
			else
				update @tblDITSpread set pct = ditAmount / @negadj_DITSum 
			update @tblDITSpread set respreadAmount = cast((@negadj_DITSum+@negadj_amount) * pct as numeric(10,2))

			-- put any remainders on the first respread
			select @negadj_respreadSum = sum(respreadAmount), @negadj_respreadFirstAutoID = min(autoid) from @tblDITSpread
			set @negadj_respreadDiff = (@negadj_DITSum+@negadj_amount) - @negadj_respreadSum
			if @negadj_respreadDiff <> 0
				update @tblDITSpread set respreadAmount = respreadAmount + @negadj_respreadDiff where autoID = @negadj_respreadFirstAutoID

			-- If any non-recognized revenue, we need to recognize it all now.
			select @ditspreadAutoID = min(autoID) from @tblDITSpread
			while @ditspreadAutoID is not null begin
				select @ditSpreadDITCacheAmount = ditCacheAmount*-1, @ditSpreadDITTID = ditTransactionID,
						@ditSpreadRecogDt = recogDt, @ditSpreadDebitGL = ditdebitGLAID, @ditSpreadCreditGL = ditcreditGLAID
				from @tblDITSpread
				where autoID = @ditspreadAutoID

				if abs(@ditSpreadDITCacheAmount) > 0
					EXEC dbo.tr_createTransaction_dit @recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
						@statsSessionID=@statsSessionID, @amount=@ditSpreadDITCacheAmount, @transactionDate=@transactionDate, 
						@recognitionDate=null, @debitGLAccountID=@ditSpreadCreditGL, @creditGLAccountID=@ditSpreadDebitGL, 
						@saleTransactionID=@negadj_tid, @DITTransactionID=@ditSpreadDITTID, @batchAsRecogJob=0, 
						@transactionID=@newdittransactionID OUTPUT

				update dbo.tr_transactionDIT
				set isActive = 0
				where transactionID = @ditSpreadDITTID

				select @ditspreadAutoID = min(autoID) from @tblDITSpread where autoID > @ditspreadAutoID
			end

			-- respread any amounts over recognition dates.
			set @ditspreadAutoID = null			
			select @ditspreadAutoID = min(autoID) from @tblDITSpread
			while @ditspreadAutoID is not null begin
				set @ditTransactionID = null

				select @amtToRespread=respreadAmount, @ditSpreadRecogDt=recogDt, @ditSpreadDebitGL=ditdebitGLAID,
					@ditSpreadCreditGL=ditcreditGLAID
				from @tblDITSpread 
				where autoID = @ditspreadAutoID
			
				-- put all scheduled rows here for auditing, even if they are immediately recognized
				EXEC dbo.tr_createTransaction_dit @recordedOnSiteID=@recordedOnSiteID, 
					@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
					@amount=@amtToRespread, @transactionDate=@transactionDate, @recognitionDate=@ditSpreadRecogDt, 
					@debitGLAccountID=@ditSpreadDebitGL, @creditGLAccountID=@ditSpreadCreditGL, 
					@saleTransactionID=@negadj_tid, @DITTransactionID=null, @batchAsRecogJob=0,
					@transactionID=@ditTransactionID OUTPUT

				-- if recognition date is today or in past, then move it out immediately back to revenue
				IF @ditSpreadRecogDt < getdate() BEGIN
					set @amtToRespread = @amtToRespread * -1
					EXEC dbo.tr_createTransaction_dit @recordedOnSiteID=@recordedOnSiteID, 
						@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
						@amount=@amtToRespread, @transactionDate=@transactionDate, @recognitionDate=null, 
						@debitGLAccountID=@ditSpreadCreditGL, @creditGLAccountID=@ditSpreadDebitGL, 
						@saleTransactionID=@negadj_tid, @DITTransactionID=@ditTransactionID, 
						@batchAsRecogJob=0, @transactionID=@newdittransactionID OUTPUT
				END

				select @ditspreadAutoID = min(autoID) from @tblDITSpread where autoID > @ditspreadAutoID
			end

			select @negadj_AutoID = min(autoid) from @tblAdjusted where autoid > @negadj_AutoID
		end
	END


	IF @TranCounter = 0
		COMMIT TRAN;
	SELECT @tids = (SELECT transactionid as tid FROM @tblVoided FOR XML RAW('t'), root('tr'))
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	SELECT @tids = '<tr/>'
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO


CREATE PROC [dbo].[tr_autoRecognizeDeferred]
AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @ditTransactionID int, @recogAmount money, @recordedOnSiteID int, @recordedByMemberID int, @nowDate datetime,
		@recogdebitGLAccountID int, @recogcreditGLAccountID int, @recogsaleAdjTID int, @recogTransactionID int

	IF OBJECT_ID('tempdb..#tmpDIT') IS NOT NULL 
		DROP TABLE #tmpDIT
	CREATE TABLE #tmpDIT (transactionID int PRIMARY KEY, amount money, recordedOnSiteID int, debitGLAccountID int, creditGLAccountID int, saleAdjTID int)

	select @recordedByMemberID = memberID from dbo.ams_members where memberNumber = 'SYSTEM' and orgID = 1
	select @nowDate = getdate()

	-- get deferred entries we need to recognize
	insert into #tmpDIT (transactionID, amount, recordedOnSiteID, debitGLAccountID, creditGLAccountID, saleAdjTID)
	select tdit.transactionID, ts.cache_amountAfterAdjustment, t.recordedOnSiteID, t.debitGLAccountID, t.creditGLAccountID, r.appliedToTransactionID
	from dbo.tr_transactions as t
	inner join dbo.tr_transactionDIT as tdit on tdit.transactionID = t.transactionID
	inner join dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID
	inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
	where t.statusID = 1
	and ts.cache_amountAfterAdjustment > 0
	and tdit.isActive = 1
	and tdit.recognitionDate < @nowDate

	-- loop over transactions and recognize them
	select @ditTransactionID = min(transactionID) from #tmpDIT
	while @ditTransactionID is not null begin
		select @recogAmount=amount*-1, @recordedOnSiteID=recordedOnSiteID, @recogdebitGLAccountID=creditGLAccountID,
			@recogcreditGLAccountID=debitGLAccountID, @recogsaleAdjTID=saleAdjTID
		from #tmpDIT 
		where transactionID = @ditTransactionID

		-- recognize revenue
		EXEC dbo.tr_createTransaction_dit @recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
			@statsSessionID=0, @amount=@recogAmount, @transactionDate=@nowDate, @recognitionDate=null, 
			@debitGLAccountID=@recogdebitGLAccountID, @creditGLAccountID=@recogcreditGLAccountID, 
			@saleTransactionID=@recogsaleAdjTID, @DITTransactionID=@ditTransactionID, @batchAsRecogJob=1, 
			@transactionID=@recogTransactionID OUTPUT

		select @ditTransactionID = min(transactionID) from #tmpDIT where transactionID > @ditTransactionID
	end 


	IF OBJECT_ID('tempdb..#tmpDIT') IS NOT NULL 
		DROP TABLE #tmpDIT


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[job_runDailyMaintenanceJobs]
AS

DECLARE @tier varchar(20), @errorSubjectRoot varchar(100), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SELECT @smtpserver = smtpserver, @tier = tier from membercentral.dbo.fn_getServerSettings()
SET @errorSubjectRoot = @tier + ' - Developer Needed - '


/* Recalc Date Based Virtual Group Rules */
BEGIN TRY
	EXEC dbo.ams_recalcVirtualGroupsBasedOnDateConditions
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Recalculating Date Based Conditions'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup resetPasswordRequests */
BEGIN TRY
	delete from dbo.ams_resetPasswordRequest
	where dateentered < dateadd(day,-30,getdate())
	and expire = 1

	delete from dbo.ams_resetPasswordRequest
	where dateentered < dateadd(day,-30,getdate())
	and expire = 0 
	and hasBeenUsed = 1
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of resetPasswordRequests'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Viewed Member Times */
BEGIN TRY
	delete tmp
	from dbo.ams_viewedMemberTimes as tmp
	inner join (
		select adminMemberViewedID, memRow
		from (
			select adminMemberViewedID, ROW_NUMBER() OVER (PARTITION BY adminID, viewedOrgID order by lastViewedDateTime desc) as memRow
			from dbo.ams_viewedMemberTimes
		) as innerTMP
		where memRow > 20
	) as t2 on t2.adminMemberViewedID = tmp.adminMemberViewedID
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of viewedMemberTimes'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Orphaned Member Data Content */
BEGIN TRY
	EXEC dbo.ams_cleanupOrphanedMemberDataContent
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of member data content'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Orphaned Network Profiles */
BEGIN TRY
	EXEC dbo.ams_deleteOrphanedNetworkProfiles
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of orphaned network profiles'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Orphaned Virtual Group Conditions */
BEGIN TRY
	EXEC dbo.ams_cleanupOrphanedVirtualGroupConditions
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of orphaned virtual group conditions'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Search Site Resource Cache entries */
BEGIN TRY
	delete from search.dbo.tblSearchSiteResourceCache
	where dateCreated < dateadd(day,-1,getdate())
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of Search Site Resource Cache'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup BuyNow log entries */
BEGIN TRY
	delete from dbo.buyNow_Log
	where insertDate < dateadd(day,-7,getdate())
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of BuyNow log entries'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Auto Post System Batches */
BEGIN TRY
	EXEC dbo.tr_autoPostSystemBatches
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Auto Posting System Batches'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Mark paid closed invoices as paid */
BEGIN TRY
	EXEC dbo.tr_autoMarkClosedInvoicesAsPaid
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Marking paid closed invoices as paid'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Close Pending Invoices */
BEGIN TRY
	EXEC dbo.sub_closePendingInvoices
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Closing Pending Invoices'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Close empty open invoices */
BEGIN TRY
	EXEC dbo.tr_autoCloseEmptyOpenInvoices
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Closing Empty Open Invoices'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Advance Subscription Rates */
BEGIN TRY
	EXEC dbo.sub_advanceRates
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Advancing Subscription Rates'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Expire Subscription Offers */
BEGIN TRY
	EXEC dbo.sub_expireOffers
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Expiring Subscription Offers'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Expire Subscriptions */
BEGIN TRY
	EXEC dbo.sub_expireSubscriptions
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Expiring Subscriptions'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Dupe Sub History Dates */
BEGIN TRY
	EXEC dbo.sub_cleanupDuplicateSubHistoryDates
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleaning Up Duplicate Sub History Dates'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Add autoPay Invoices to Invoice Payment Queue */
BEGIN TRY
	EXEC dbo.tr_autoPayInvoices
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Adding autoPay Invoices to Invoice Payment Queue'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Daily Deferred Recognition */
BEGIN TRY
	EXEC dbo.tr_autoRecognizeDeferred
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Running Daily Deferred Recognition Job'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Add items to LRIS Client Surveys Queue */
IF @tier = 'PRODUCTION' BEGIN
	BEGIN TRY
		declare @referralID int
		set @referralID = 1
		EXEC dbo.ref_addClientSurveyQueue @referralID = @referralID
	END TRY
	BEGIN CATCH
		SET @errorSubject = @errorSubjectRoot + 'Error Adding items to Client Surveys Queue'

		SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
		SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END CATCH
END

/* Cleanup tblQueueItems_processMemberGroups */
BEGIN TRY
	delete from platformQueue.dbo.tblQueueItems_processMemberGroups
	where dateadded < dateadd(day,-1,getdate())
	and itemUID not in (select itemUID from platformQueue.dbo.tblQueueItems)
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of tblQueueItems_processMemberGroups'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* Add missing ad zones or update ad zone names */
BEGIN TRY
	EXEC dbo.ad_populateAdZones
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Populating Ad Zones'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* Admin Count checks - Run on first of month only */
IF datepart(d,getdate()) = 1 BEGIN
	BEGIN TRY
		EXEC dbo.ams_adminCountReport
	END TRY
	BEGIN CATCH
		SET @errorSubject = @errorSubjectRoot + 'Error Running Admin Count Report'

		SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
		SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END CATCH
END

RETURN 0
GO

