ALTER PROCEDURE [dbo].[lyris_processLyrisArchiveChangeQueue]

AS

DECLARE @currentMessageID int  
DECLARE @currentAutoID int  
DECLARE @currentOperationID int  
DECLARE @batchSize int  

Select @batchSize = 10000    

DECLARE @lastRecordProcessed int  
DECLARE @newLastRecordProcessed int    
select @lastRecordProcessed = lastRecordProcessed 
from TLASITES.search.dbo.tblSearchUpdateStatus 
where name = 'listmessageChangeQueue'      

DECLARE @MessageQueue TABLE (autoid int IDENTITY(1,1) PRIMARY KEY, messageid_ int, changeID int, operationID int)    
INSERT INTO @MessageQueue (messageid_, changeID, operationID)  
select top (@batchSize) messageid_, changeID, operationID 
from dbo.messageChangeQueue  
where changeID > @lastRecordProcessed  
order by changeID    

select @newLastRecordProcessed = max(changeID) from @MessageQueue      
select top 1 @currentMessageID = messageid_, @currentOperationID = operationID, @currentAutoID = autoid 
from @MessageQueue 
order by autoid   

while @currentMessageID is not null  begin   
	if @currentOperationID = 1   
		begin
			update mst set
				searchtext = dbo.fn_RegExReplace(searchtext, '\btslistname[^\s]+xxx', 'tslistname' + ml.list + 'xxx')
			from messageSearchText mst
			inner join messages_ m
				on m.MessageID_ = mst.messageid_
			inner join messageLists ml
				on ml.listID = m.listid
				and m.MessageID_ = @currentMessageID
		end     
	if exists (select top 1 messageid_ from @MessageQueue where autoid > @currentAutoID)
		select top 1 @currentMessageID = messageid_, @currentOperationID = operationID, @currentAutoID = autoid from @MessageQueue where autoid > @currentAutoID order by autoid   
	else    
		select @currentMessageID = null  
end    
if @newLastRecordProcessed is not null   
	update TLASITES.search.dbo.tblSearchUpdateStatus set lastRecordProcessed = @newLastRecordProcessed 
	where name = 'listmessageChangeQueue'


RETURN 0
GO
