USE [seminarWeb]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
SET ANSI_PADDING ON
GO
ALTER TABLE dbo.tblParticipants ADD
	logoFileExtension varchar(30) NULL,
	usePublisherLogo bit NOT NULL CONSTRAINT [DF_tblParticipants_usePublisherLogo]  DEFAULT ((0))
GO

ALTER TABLE dbo.tblSeminars ADD
	adID int NULL
GO

CREATE TABLE [dbo].[tblParticipantAds](
	[adID] [int] IDENTITY(1,1) NOT NULL,
	[participantID] [int] NOT NULL,
	[adName] [varchar](500) NULL,
	[URL] [varchar](500) NULL,
	[description] [varchar](500) NULL,
	[alternateText] [varchar](250) NULL,
	[fileExtension] [varchar](250) NULL,
 CONSTRAINT [PK_tblParticipantAds] PRIMARY KEY CLUSTERED 
(
	[adID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
SET ANSI_PADDING OFF
GO
ALTER TABLE [dbo].[tblParticipantAds]  WITH CHECK ADD  CONSTRAINT [FK_tblParticipantAds_tblParticipantAds] FOREIGN KEY([participantID])
REFERENCES [dbo].[tblParticipants] ([participantID])
GO
ALTER TABLE [dbo].[tblParticipantAds] CHECK CONSTRAINT [FK_tblParticipantAds_tblParticipantAds]




ALTER PROC [dbo].[sw_getAssociationDetails]
@orgcode varchar(5)

AS

SELECT top 1 p.participantID, p.orgcode, p.showUSD, p.supportPhone, p.supportEmail, p.isConf, p.isSWOD, p.isSWTL, p.isSWL, 
	p.isSearch, p.isMyCLE, p.catalogURL, p.wddxTimeZones, p.emailFrom, p.brandHome, p.brandHomeTab, p.brandConf, 
	p.brandConfTab, p.brandSWL, p.brandSWLTab, p.brandSWOD, p.brandSWODTab, p.brandSWTL, p.brandSWTLTab, p.brandMyCLE, 
	p.brandMyCLETab, p.brandHomeText, p.brandHomeRegister, p.isSelfReportCredit, p.brandFAQ, p.brandFAQTab, p.brandSWODNameLink, 
	p.brandSWODCategoryLink, p.brandSWODPresenterLink, p.brandSWODCreditLink, p.brandSWODPublisherLink, 
	p.brandSWODDatePublishedLink, p.brandSWTLCategoryLink, p.brandSWTLPresenterLink, p.brandSWTLPublisherLink, 
	p.brandSWTLDatePublishedLink, p.brandSWLDateLink, p.brandSWLSpeakerLink, p.brandSWLCreditLink, p.showSWBranding, 
	tla.shortname, tla.description, tla.url, sr.storageRate, p.logoFileExtension, p.usePublisherLogo
FROM dbo.tblParticipants AS p 
INNER JOIN trialsmith.dbo.depoTLA AS tla ON tla.State = p.orgcode 
LEFT OUTER JOIN dbo.tblStorageRates as sr on sr.participantID = p.participantID
WHERE p.orgcode = @orgcode

RETURN
GO


CREATE PROC [dbo].[sw_getSecondaryAds]
@orgcode varchar(5)

AS

SELECT pa.adID, pa.participantID, pa.adName, pa.URL, pa.description, pa.alternateText, pa.fileExtension,
	(select count(*) from tblSeminars where adID = pa.adID) as adCount
FROM dbo.tblParticipants AS p 
INNER JOIN dbo.tblParticipantAds AS pa ON pa.participantID = p.participantID
INNER JOIN trialsmith.dbo.depoTLA AS tla ON tla.State = p.orgcode 
WHERE p.orgcode = @orgcode

RETURN
GO


CREATE PROC [dbo].[sw_getAds]

AS

SELECT p.orgCode, pa.adID, pa.participantID, pa.adName, pa.URL, pa.description, pa.alternateText, pa.fileExtension
FROM dbo.tblParticipants AS p 
INNER JOIN dbo.tblParticipantAds AS pa ON pa.participantID = p.participantID
INNER JOIN trialsmith.dbo.depoTLA AS tla ON tla.State = p.orgcode 

RETURN



ALTER PROC [dbo].[swod_getSeminar]
@seminarID int

AS

SELECT TOP 1 s.seminarID, 'SWOD' as SWType, s.seminarName, s.seminarDesc, s.isPublished, s.offerCertificate, s.allowRegistrants, 
	s.productCode, s.seminarDescText, sod.dateCatalogStart, sod.dateCatalogEnd, sod.dateOrigPublished, sod.offerQA, 
	sod.priceSyndication, sod.blankOnInactivity, sod.allowSyndication, tla.Description, 
	p.showUSD, l.layout, sod.seminarLength, sod.layoutID, sod.endofSeminarText,
	(select top 1 preReqSeminarID from dbo.tblSeminarsPreReqs where seminarID = @seminarID) as preReq,
	s.isPriceBasedOnActual, s.adID
FROM dbo.tblSeminars AS s 
INNER JOIN dbo.tblSeminarsSWOD AS sod ON s.seminarID = sod.seminarID 
INNER JOIN dbo.tblParticipants AS p ON s.participantID = p.participantID 
INNER JOIN trialsmith.dbo.depoTLA AS tla ON tla.State = p.orgcode
INNER JOIN dbo.tblSeminarsSWODLayouts as l on l.layoutID = sod.layoutID
WHERE s.seminarID = @seminarID
AND s.isDeleted = 0



ALTER PROC [dbo].[swl_getSeminar]
@seminarID int

AS

select top 1 'SWL' as SWType, s.seminarName, s.seminarDesc, s.isPublished, s.offerCertificate, 
	s.allowRegistrants, s.isPriceBasedOnActual, s.productCode, s.seminarDescText, 
	swl.liveID, swl.seminarID, swl.swlTypeID, swl.gotoMeetingID, swl.agenda, swl.dateStart, swl.dateEnd, 
	swl.wddxTimeZones, swl.programPassword, swl.lineProvider, swl.phoneAdmin, swl.phoneAttendee, swl.codeAdmin, 
	swl.codeAttendee, swl.codeSpeaker, swl.surveyLink, swl.offerCredit, swl.offerDVD, swl.sendCountToPremiere, 
	swl.premiereUsePIN, swl.premiereConfID, swl.priceSyndication, swl.isOpen, swl.isNATLE, swl.materialsLink,
	s.adID
from dbo.tblSeminars s
inner join dbo.tblSeminarsSWLive swl on swl.seminarID = s.seminarID
where s.seminarID = @seminarID
AND s.isDeleted = 0
