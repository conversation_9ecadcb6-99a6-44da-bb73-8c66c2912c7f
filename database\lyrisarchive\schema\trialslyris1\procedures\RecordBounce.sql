ALTER PROCEDURE RecordBounce
  		@MailingID int,
  		@MemberID int,
 		@BounceMailingID int,
  		@TransactionLog varchar (8000)
 	 AS 
 	 begin 
 		SET NOCOUNT ON 
 		set xact_abort on
 		if @TransactionLog is null 
 		begin 
 			raiserror ( '@TransactionLog cannot be NULL!', 11, 1 ) 
 			return 
 		end 
 	 
  		DECLARE @ptrval binary(16), 
 		@RecipientID numeric(19) 
 	 
 		select @RecipientID = RecipientID, 
 		@ptrval = TEXTPTR(TransactionLog)  
         from lyrCompletedRecips WITH (INDEX = IX_CompletedRecipsMailMember) 
 		where MailingID = @MailingID 
 		and MemberID = @MemberID 
 	 
 		if @@error > 0 or @RecipientID is NULL 
 			return 
 	 
  		Begin transaction RecordBounce
 	 
 		if @ptrval is null 
 			update lyrCompletedRecips  
 			set CompletionStatusID = 305,  
 			BounceMailingID = @BounceMailingID, 
 			TransactionLog = @TransactionLog  
 			where RecipientID = @RecipientID 
 		else 
 			update lyrCompletedRecips  
 			set CompletionStatusID = 305,  
 			BounceMailingID = @BounceMailingID 
 			where RecipientID = @RecipientID 
 	  
  		if @@error != 0 
  		begin 
  			rollback transaction 
 			raiserror ( 'Error updating lyrCompletedRecips: %d', 11, 3, @@error ) 
  			return 
  		end 
 	  
 		if @ptrval is NOT NULL 
 			UpdateText lyrCompletedRecips.TransactionLog @ptrval NULL 0 @TransactionLog 
 	  
  		if @@error != 0 
 		begin 
  			rollback transaction 
 			raiserror ( 'Error when calling UpdateText: ', 11, 3, @@error ) 
 		end 
  		else 
  			commit transaction 
 	 end
GO
