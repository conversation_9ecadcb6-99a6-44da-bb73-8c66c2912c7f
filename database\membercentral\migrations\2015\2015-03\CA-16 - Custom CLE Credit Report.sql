
USE memberCentral
GO

declare 
	@toolTypeID int, 
	@resourceTypeID int, 
	@siteID int,
	@parentnavigationID int, 
	@navigationID int, 
	@resourceTypeFunctionID int

select @siteID = siteID from  sites where siteCode = 'ca'

EXEC dbo.createAdminToolType 
		@toolType='ERRRegistrantCLECredit', 
		@toolCFC='Reports.events.RegistrantCLECredit', 
		@toolDesc='Registrant CLE Credit Report', 
		@toolTypeID=@toolTypeID OUTPUT, 
		@resourceTypeID=@resourceTypeID OUTPUT

select 
	@parentNavigationID=n.navigationID
from 
	admin_navigation n
	inner join admin_navigation n2 on
		n2.navigationID = n.parentNavigationID
		and n2.navName = 'Events'
		and n2.navAreaID = 2
where 
	n.navname = 'Registrants'
	and n.navAreaID = 3

EXEC dbo.createAdminNavigation 
		@navName='Registrant CLE Credit', 
		@navDesc='List of registrants that have been awarded CLE credit.', 
		@parentNavigationID=@parentnavigationID, 
		@navAreaID=4, 
		@cfcMethod='showReport', 
		@isHeader=0, 
		@showInNav=1, 
		@navigationID=@navigationID OUTPUT

select 
	@resourceTypeFunctionID = f.resourceTypeFunctionID 
from 
	cms_siteResourceTypeFunctions f
	inner join dbo.cms_siteResourceTypes rt on
		rt.resourceTypeID = f.resourceTypeID
		and resourceType = 'Admin'
	inner join dbo.cms_siteResourceFunctions rf on
		rf.functionID = f.functionID
		and rf.functionName = 'View'
		and rf.displayName = 'View'

EXEC dbo.createAdminFunctionsDeterminingNav 
		@resourceTypeFunctionID=@resourceTypeFunctionID, 
		@toolTypeID=@toolTypeID, 
		@navigationID=@navigationID

exec dbo.createAdminSuite @siteID=@siteID

insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
values(@tooltypeID, @siteID)
GO

USE [platformQueue]
GO

if not exists (select queueTypeID from dbo.tblQueueTypes where queueType='caCLECredits')
	insert into dbo.tblQueueTypes (queueType) values ('caCLECredits')
GO

declare @queueTypeID int, @trashID int
select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType='caCLECredits'

exec dbo.queue_createQueueTypeDataColumn @queueTypeID=@queueTypeID, @columnName='registrantID', @columnDesc='regustrantID of a member in an event (MemberID in datakey)', @dataTypeCode='INTEGER', @columnID=@trashID OUTPUT
exec dbo.queue_createQueueTypeDataColumn @queueTypeID=@queueTypeID, @columnName='ConfigParam', @columnDesc='config option value (configkey in datakey)', @dataTypeCode='STRING', @columnID=@trashID OUTPUT
exec dbo.queue_createQueueTypeDataColumn @queueTypeID=@queueTypeID, @columnName='ConfigText', @columnDesc='config varchar(max) value (configkey in datakey)', @dataTypeCode='TEXT', @columnID=@trashID OUTPUT
GO

declare @queueTypeID int
select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType='caCLECredits'

insert into platformQueue.dbo.tblQueueStatuses (queueTypeID,queueStatus) values (@queueTypeID,'insertingItems')
insert into platformQueue.dbo.tblQueueStatuses (queueTypeID,queueStatus) values (@queueTypeID,'readyToProcess')
insert into platformQueue.dbo.tblQueueStatuses (queueTypeID,queueStatus) values (@queueTypeID,'grabbedForProcessing')
insert into platformQueue.dbo.tblQueueStatuses (queueTypeID,queueStatus) values (@queueTypeID,'processingReport')
insert into platformQueue.dbo.tblQueueStatuses (queueTypeID,queueStatus) values (@queueTypeID,'readyToNotify')
insert into platformQueue.dbo.tblQueueStatuses (queueTypeID,queueStatus) values (@queueTypeID,'grabbedForNotifying')
insert into platformQueue.dbo.tblQueueStatuses (queueTypeID,queueStatus) values (@queueTypeID,'done')
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].job_CLEReport_grabForProcessing') AND type in (N'P', N'PC'))
	DROP PROCEDURE [dbo].job_CLEReport_grabForProcessing
GO

CREATE PROC dbo.job_CLEReport_grabForProcessing
@serverID int,
@batchSize int,
@filePath varchar(400)

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max)
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @subTypeOrdering TABLE (itemUID uniqueidentifier, typeID int, typeSortOrder int)

	declare @statusReady int, @statusGrabbed int
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'caCLECredits'
		and qs.queueStatus = 'readyToProcess'

	select @statusGrabbed = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'caCLECredits'
		and qs.queueStatus = 'grabbedForProcessing'

	declare @jobUID uniqueidentifier
	set @jobUID = NEWID()

	-- dequeue in order of dateAdded. get @batchsize members
	IF OBJECT_ID('tempdb..#tmpTblQueueItems_clereport') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_clereport

	CREATE TABLE #tmpTblQueueItems_clereport (itemUID uniqueidentifier, itemGroupUID uniqueidentifier, 
		jobUID uniqueidentifier, recordedByMemberID int, siteID int, memberID int, memberNumber varchar(50), 
		firstname varchar(75), lastname varchar(75), prefix varchar	(50), suffix varchar(50), 
		company varchar(200), address1 varchar(100), 
		address2 varchar(100), address3 varchar(100), city varchar(35), stateProv varchar(4),
		postalCode varchar(25), country varchar(100), phone varchar(40), fax varchar(40),
		xmlFieldSets varchar(max), 
		xmlConfigParam varchar(max), xmlMembers varchar(max))

	update qi WITH (UPDLOCK, READPAST)
	set qi.queueStatusID = @statusGrabbed,
		qi.dateUpdated = getdate(),
		qi.jobUID = @jobUID,
		qi.jobDateStarted = getdate(),
		qi.jobServerID = @serverID
		OUTPUT inserted.itemUID, null, inserted.jobUID, null, null, null, null, null, null, null, 
			null, null, null, null, null, null, null, null, null, null, null, null, null, null
		INTO #tmpTblQueueItems_clereport
	from platformQueue.dbo.tblQueueItems as qi
	inner join (
		select top(@BatchSize) qi2.itemUID 
		from platformQueue.dbo.tblQueueItems as qi2
		where qi2.queueStatusID = @statusReady
		order by qi2.dateAdded, qi2.itemUID
		) as batch on batch.itemUID = qi.itemUID
	where qi.queueStatusID = @statusReady

	IF @TranCounter = 0
		COMMIT TRAN;

END TRY
BEGIN CATCH
	IF OBJECT_ID('tempdb..#tmpTblQueueItems_clereport') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_clereport

	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH


BEGIN TRY
	-- get memberID, itemGroupUID, siteID, recordedByMemberID from item data
	-- outer apply is required in cases where there is no match so the itemGroupUID gets updated
	update tmp
	set tmp.memberID = idata.memberID,
		tmp.itemGroupUID = idata.itemGroupUID,
		tmp.recordedByMemberID = idata.recordedByMemberID,
		tmp.siteID = idata.siteID
	from #tmpTblQueueItems_clereport as tmp
	outer apply (
		select min(cast(dataKey as int)) as memberID, min(cast(itemGroupUID as varchar(60))) as itemGroupUID, 
			min(recordedByMemberID) as recordedByMemberID, min(siteID) as siteID
		from platformQueue.dbo.tblQueueItemData as qid
		inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
			and dc.columnname = 'registrantID'
		where qid.itemUID = tmp.itemUID
	) as idata

	-- get address info for member accounts
	update tmp 
	set	tmp.memberNumber = m.memberNumber, 
		tmp.firstname = m.firstname, 
		tmp.lastname = m.lastname, 
		tmp.prefix = m.prefix,
		tmp.suffix = m.suffix,
		tmp.company = m.company, 
		tmp.address1 = ma.address1, 
		tmp.address2 = ma.address2, 
		tmp.address3 = ma.address3, 
		tmp.city = ma.city, 
		tmp.stateProv = s.code, 
		tmp.postalCode = ma.postalCode,
		tmp.country = c.country
	from #tmpTblQueueItems_clereport as tmp
	inner join membercentral.dbo.ams_members as m on m.memberID = tmp.memberID
	left outer join membercentral.dbo.ams_memberAddresses as ma on ma.memberID = m.memberID 
		and ma.addressTypeID = m.billingAddressTypeID
	left outer join membercentral.dbo.ams_states as s on s.stateID = ma.stateID
	left outer join membercentral.dbo.ams_countries as c on c.countryID = ma.countryID

	-- get phone and fax numbers info for member accounts
	update tmp 
	set	
		tmp.phone = mp.phone
	from #tmpTblQueueItems_clereport as tmp
	inner join membercentral.dbo.ams_members as m on m.memberID = tmp.memberID
	left outer join membercentral.dbo.ams_memberAddresses as ma 
		inner join membercentral.dbo.ams_memberPhones mp on
			mp.addressID = ma.addressID
		inner join membercentral.dbo.ams_memberPhoneTypes  pt on
			pt.phoneTypeID = mp.phoneTypeID
			and pt.phoneType = 'Phone'
		on ma.memberID = m.memberID and ma.addressTypeID = m.billingAddressTypeID

	update tmp 
	set	
		tmp.fax = mp.phone
	from #tmpTblQueueItems_clereport as tmp
	inner join membercentral.dbo.ams_members as m on m.memberID = tmp.memberID
	left outer join membercentral.dbo.ams_memberAddresses as ma  
		inner join membercentral.dbo.ams_memberPhones mp on
			mp.addressID = ma.addressID
		inner join membercentral.dbo.ams_memberPhoneTypes  pt on
			pt.phoneTypeID = mp.phoneTypeID
			and pt.phoneType = 'fax'
		on ma.memberID = m.memberID and ma.addressTypeID = m.billingAddressTypeID

	-- get config params for each item. casting to varchar(max) because it speed up final data query return
	update tmp
	set tmp.xmlConfigParam = config.configXML
	from #tmpTblQueueItems_clereport as tmp
	cross apply (
		select cast(isnull((
			select reportParam, paramvalue	
			from (
				select datakey as reportParam, cast(isnull(columnValueString,'') as varchar(max)) as paramvalue
				from platformQueue.dbo.tblQueueItemData qid
				inner join platformQueue.dbo.tblQueueTypeDataColumns dc on dc.columnID = qid.columnID
					and dc.columnname = 'ConfigParam'
				where qid.itemUID = tmp.itemUID
					union all
				select datakey as reportParam, isnull(columnValuetext,'') as paramvalue
				from platformQueue.dbo.tblQueueItemData qid
				inner join platformQueue.dbo.tblQueueTypeDataColumns dc on dc.columnID = qid.columnID
					and dc.columnname = 'ConfigText'
				where qid.itemUID = tmp.itemUID
			) as tmp
			for xml path('param'), root('params'), type
		),'<params/>') as varchar(max)) as configXML
	) as config

	-- get all members
	IF OBJECT_ID('tempdb..#qryMembersEvents') IS NOT NULL 
		DROP TABLE #qryMembersEvents

	CREATE TABLE #qryMembersEvents (itemUID uniqueidentifier, memberID int, memberNumber varchar(50),
		prefix varchar(50), firstname varchar(75), lastname varchar(75), company varchar(200), suffix varchar(50),
		address1 varchar(100), address2 varchar(100), address3 varchar(100), city varchar(35), stateProv varchar(4),
		postalCode varchar(25), country varchar(100),
		eventID int, masterevent bit, eventName varchar(200), 
		eventDateFrom datetime, eventDateTo datetime,
		eventLocation varchar(1000), isAllDayEvent bit
	)
	insert into #qryMembersEvents (itemUID, memberID, eventID)
	select distinct tmp.itemUID, qid.dataKey, qid.columnValueInteger
	from #tmpTblQueueItems_clereport as tmp
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = tmp.itemUID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
		and dc.columnname = 'registrantID'

	update me 
	set 
		MemberNumber = m.MemberNumber, 
		prefix = m.prefix,
		firstname = m.firstname, 
		lastname = m.lastname, 
		company = m.company,
		suffix = m.suffix,
		address1 = ma.address1, 
		address2 = ma.address2, 
		address3 = ma.address3, 
		city = ma.city, 
		stateProv = s.code, 
		postalCode = ma.postalCode,
		country = c.country
	from #qryMembersEvents as me
	inner join membercentral.dbo.ams_members as m on m.memberID = me.memberID
	left outer join membercentral.dbo.ams_memberAddresses as ma on ma.memberID = m.memberID 
		and ma.addressTypeID = m.billingAddressTypeID
	left outer join membercentral.dbo.ams_states as s on s.stateID = ma.stateID
	left outer join membercentral.dbo.ams_countries as c on c.countryID = ma.countryID

	declare @siteID int, @orgID int
	select 
		@siteID = e.siteID,
		@orgID = s.orgID
	from 
		#qryMembersEvents as me
		inner join membercentral.dbo.ev_events as e on 
			e.eventID = me.eventID	
		inner join membercentral.dbo.sites as s on
			s.siteID = e.siteID

	update me 
	set 
		eventName = isnull(eventContent.contentTitle,''), 
		masterevent = (select case when count (*) > 0 then 0 else 1 end from  membercentral.dbo.ev_subEvents te where te.eventID = e.eventID), 
		eventLocation = locationcontent.contentTitle,
		eventDateFrom =  eOnS.startTime, 
		eventDateTo = eOnS.endTime,
		isAllDayEvent = e.isAllDayEvent
	from 
		#qryMembersEvents as me
		inner join membercentral.dbo.ev_events as e on e.eventID = me.eventID
		inner join membercentral.dbo.ev_eventCategories as ecat on ecat.eventID = e.eventID
		inner join membercentral.dbo.ev_categories as evcat on evcat.categoryID = ecat.categoryID
		inner join membercentral.dbo.fn_ev_getEventsOnSite(@siteID,1,DEFAULT,DEFAULT) as eOnS on eOnS.eventid = e.eventID
		cross apply membercentral.dbo.fn_getContent(e.eventContentID,1) as eventContent
		cross apply membercentral.dbo.fn_getContent(e.locationcontentID,1) as locationcontent
 
	-- members  xml. casting to varchar(max) because it speed up final data query return
	update tmp
	set tmp.xmlMembers = allmembers.memberXML
	from #tmpTblQueueItems_clereport as tmp
	cross apply (
		select cast(isnull((		
			select member.memberID as '@memberID',
				member.memberNumber as '@membernumber',
				member.firstname as '@firstname',
				member.lastname  as '@lastname',
				member.lastname + ', ' + member.firstname as '@namestring',
				member.prefix as '@prefix',
				member.suffix as '@suffix',
				member.company as '@company' ,
				member.address1 as '@address1' ,
				member.address2 as '@address2' ,
				member.address3 as '@address3' ,
				member.city as '@city' ,
				member.stateProv as '@state' ,
				member.postalCode as '@postalcode',
				member.country as '@country',
				member.phone as '@phone',
				member.fax as '@fax',
				(select
					memberevent.eventID as '@eventID',
					memberevent.memberID as '@memberID',
					memberevent.eventname as '@eventname', 
					convert(varchar, memberevent.eventdatefrom, 101) as '@eventdatefrom',
					convert(varchar, memberevent.eventdateto, 101) as '@eventdateto',
					memberevent.eventLocation as '@eventlocation',
					memberevent.isAllDayEvent as '@isalldayevent',
					(select 
						authorities.authorityID as '@authorityID',
						authorities.authorityName as '@authorityName',
						memberevent.eventID as '@eventID',
						(select 
							at.typeID as '@authorityTypeID' ,
							authorities.authorityID as '@authorityID',
							memberevent.eventID as '@eventID',
							isNull(cat.ovTypeName, at.typeName) as '@authorityTypeName', 
							isNull(ot.creditValue, 0) as '@creditValue',
							isNull(req.creditAwarded, 0) as '@creditAwarded',
							isNull(req.creditValueAwarded, 0) as '@creditValueAwarded',	
							isNull(req.registrantID, 0) as '@registrantID'	
						from
							membercentral.dbo.crd_sponsors cs
							inner join membercentral.dbo.crd_authoritySponsors cas on
								cas.sponsorID = cs.sponsorID
								and cs.orgID = @orgID
							inner join membercentral.dbo.crd_authorities ca on
								ca.authorityID = cas.authorityID
								and ca.authorityID = authorities.authorityID
							inner join membercentral.dbo.crd_authoritySponsorTypes cat on
								cat.asid = cas.asid
							inner join membercentral.dbo.crd_authorityTypes at on
								at.typeID = cat.typeID	
							inner join membercentral.dbo.crd_offerings o on
								o.asid = cat.asid 
								and o.eventID = memberevent.eventID 
							left outer join membercentral.dbo.crd_offeringTypes ot 
								inner join membercentral.dbo.crd_requests req on
									req.offeringTypeID = ot.offeringTypeID
									and req.creditAwarded = 1
								inner join membercentral.dbo.ev_registrants r on
									r.registrantID = req.registrantID 
									and r.status = 'A'
								inner join membercentral.dbo.ams_members m on
									m.memberID = r.memberID
									and membernumber = member.memberNumber
									and m.memberID = m.activeMemberID
									and m.status <> 'D'
									and m.orgID = @orgID
								on ot.offeringID = o.offeringID
									and ot.ASTID = cat.ASTID
						group by 
							at.typeID, isNull(cat.ovTypeName, at.typeName), isNull(ot.creditValue, 0),
							isNull(req.creditAwarded, 0), isNull(req.creditValueAwarded, 0), isNull(req.registrantID, 0)
						order by
							isNull(cat.ovTypeName, at.typeName)
						for xml path('authoritytype'), type
						)
						from
							membercentral.dbo.crd_sponsors cs
							inner join membercentral.dbo.crd_authoritySponsors cas on
								cas.sponsorID = cs.sponsorID
								and cs.orgID = @orgID
							inner join membercentral.dbo.crd_authorities authorities on
								authorities.authorityID = cas.authorityID
								and authorities.authorityName = 'State Bar Of California'
						order by 
							authorities.authorityName
						for xml path('authorities'), type
					)
					from #qryMembersEvents as memberevent
					where memberevent.itemUID = member.itemUID
					order by memberevent.eventdatefrom
					for xml path('event'), type
				),
				(select
					member.memberID as '@memberID',
					(select 
						ca.authorityID as '@authorityID',
						ca.authorityName as '@authorityName',
						member.memberID as '@memberID',
						(select 
							at.typeID as '@authorityTypeID' ,
							ca.authorityID as '@authorityID',
							member.memberID as '@memberID',
							isNull(cat.ovTypeName, at.typeName) as '@authorityTypeName', 
							sum(isNull(req.creditValueAwarded, 0)) as '@creditValueAwarded'
						from
							membercentral.dbo.crd_sponsors cs
							inner join membercentral.dbo.crd_authoritySponsors cas on
								cas.sponsorID = cs.sponsorID
								and cs.orgID = @orgID
							inner join membercentral.dbo.crd_authorities ca2 on
								ca2.authorityID = cas.authorityID
								and ca2.authorityID = ca.authorityID
							inner join membercentral.dbo.crd_authoritySponsorTypes cat on
								cat.asid = cas.asid
							inner join membercentral.dbo.crd_authorityTypes at on
								at.typeID = cat.typeID	
							inner join membercentral.dbo.crd_offerings o on
								o.asid = cat.asid 
							inner join #qryMembersEvents as memberevents on
								memberevents.eventID = o.eventID
								and memberevents.memberID = member.memberID
							left outer join membercentral.dbo.crd_offeringTypes ot 
								inner join membercentral.dbo.crd_requests req on
									req.offeringTypeID = ot.offeringTypeID
									and req.creditAwarded = 1
								inner join membercentral.dbo.ev_registrants r on
									r.registrantID = req.registrantID 
									and r.status = 'A'
								inner join membercentral.dbo.ams_members m on
									m.memberID = r.memberID
									and membernumber = member.memberNumber
									and m.memberID = m.activeMemberID
									and m.status <> 'D'
									and m.orgID = @orgID
								on ot.offeringID = o.offeringID
									and ot.ASTID = cat.ASTID
						group by 
							at.typeID, isNull(cat.ovTypeName, at.typeName)
						order by
							isNull(cat.ovTypeName, at.typeName)
						for xml path('authoritytype'), type
						)
					from
						membercentral.dbo.crd_sponsors cs
						inner join membercentral.dbo.crd_authoritySponsors cas on
							cas.sponsorID = cs.sponsorID
							and cs.orgID = @orgID
						inner join membercentral.dbo.crd_authorities ca on
							ca.authorityID = cas.authorityID
							and ca.authorityName = 'State Bar Of California'
					order by 
						ca.authorityName
					for xml path('authorities'), type
					)
				for xml path('totals'), type
				)
			from #tmpTblQueueItems_clereport as member
			where member.itemUID = tmp.itemUID
				and tmp.itemGroupUID is not null
			order by member.memberID
			for xml path('member'), root('members'), type
		),'<members/>') as varchar(max)) as memberXML
	) as allmembers

	-- create directories to store xmls
	IF OBJECT_ID('tempdb..#tblDirs') IS NOT NULL 
		DROP TABLE #tblDirs
	select distinct itemGroupUID, membercentral.dbo.fn_createDirectory(@filePath + cast(itemGroupUID as varchar(50))) as cdResult
	into #tblDirs
	from #tmpTblQueueItems_clereport
	where membercentral.dbo.fn_DirectoryExists(@filePath + cast(itemGroupUID as varchar(50))) = 0

	-- final data
	select itemUID, itemGroupUID, jobUID, siteID, memberNumber, lastname + '_' + firstname as 'fullname', xmlFieldSets, xmlConfigParam, 
		membercentral.dbo.fn_writefile(@filePath + cast(itemGroupUID as varchar(50)) + '\' + cast(itemUID as varchar(50)) + '.xml',xmlMembers,1) as writeFileResult
	from #tmpTblQueueItems_clereport
	where itemGroupUID is not null
	order by itemUID

	IF OBJECT_ID('tempdb..#tblDirs') IS NOT NULL 
		DROP TABLE #tblDirs
	IF OBJECT_ID('tempdb..#tblSubInvoices') IS NOT NULL 
		DROP TABLE #tblSubInvoices
	IF OBJECT_ID('tempdb..#tblSubPayments') IS NOT NULL 
		DROP TABLE #tblSubPayments
	IF OBJECT_ID('tempdb..#tblSubTreeTotals') IS NOT NULL 
		DROP TABLE #tblSubTreeTotals
	IF OBJECT_ID('tempdb..#qrySubs') IS NOT NULL 
		DROP TABLE #qrySubs
	IF OBJECT_ID('tempdb..#qryMembersEvents') IS NOT NULL 
		DROP TABLE #qryMembersEvents
	IF OBJECT_ID('tempdb..#tmpTblQueueItems_clereport') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_clereport

	 RETURN 0
END TRY
BEGIN CATCH
	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH
GO


IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].job_CLEReport_grabForNotification') AND type in (N'P', N'PC'))
	DROP PROCEDURE [dbo].job_CLEReport_grabForNotification
GO

CREATE PROC dbo.job_CLEReport_grabForNotification
	@batchSize int
AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @statusReady int, @statusGrabbed int, @queueTypeID int
	select @statusReady = qs.queueStatusID , @queueTypeID = qt.queueTypeID
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'caCLECredits'
		and qs.queueStatus = 'readyToNotify'
	select @statusGrabbed = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'caCLECredits'
		and qs.queueStatus = 'grabbedForNotifying'

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL 
		DROP TABLE #tmpNotify
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier)

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct top(@BatchSize) qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID = @statusReady
			except
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID <> @statusReady
	)
	UPDATE platformQueue.dbo.tblQueueItems WITH (UPDLOCK, READPAST)
	SET queueStatusID = @statusGrabbed,
		dateUpdated = getdate()
		OUTPUT qid.itemGroupUID
		INTO #tmpNotify
	FROM platformQueue.dbo.tblQueueItems as qi
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qid.itemGroupUID
	where qi.queueStatusID = @statusReady


	-- return itemGroupUIDs that can be marked as done
	select distinct tmpN.itemGroupUID, me.email as reportEmail, s.siteName, s.siteCode, m.firstname, m.lastname, m.memberNumber
	from (select distinct itemGroupUID from #tmpNotify) as tmpN
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = qid.recordedByMemberID
	LEFT OUTER JOIN membercentral.dbo.ams_memberEmails as me 
		INNER JOIN membercentral.dbo.ams_memberEmailTypes as met on met.emailTypeID = me.emailTypeID and met.emailTypeOrder = 1
		on me.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.sites as s on s.siteID = qid.siteID
	order by tmpN.itemGroupUID


	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL 
		DROP TABLE #tmpNotify

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].job_CLEReport_clearDone') AND type in (N'P', N'PC'))
	DROP PROCEDURE [dbo].job_CLEReport_clearDone
GO

CREATE PROC dbo.job_CLEReport_clearDone
AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @statusDone int
	select @statusDone = qs.queueStatusID
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'caCLECredits'
		and qs.queueStatus = 'done'

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID = @statusDone
			except
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID <> @statusDone
	)
	DELETE from platformQueue.dbo.tblQueueItems
	where itemUID in (
		select qi.itemUID
		FROM platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qid.itemGroupUID
		WHERE qi.queueStatusID = @statusDone
	)

	DELETE from platformQueue.dbo.tblQueueItemData
	where itemUID not in (select itemUID from platformQueue.dbo.tblQueueItems)


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH
GO
