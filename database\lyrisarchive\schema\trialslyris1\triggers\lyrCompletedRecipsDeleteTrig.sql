CREATE TRIGGER lyrCompletedRecipsDeleteTrig
ON lyrCompletedRecips
AFTER DELETE AS
BEGIN
	SET NOCOUNT ON 
INSERT INTO lyrUnsummarizedRecips (RecipientID, MemberID, CompletionStatusID, MailingID, FirstAttempt, FinalAttempt, RecordCount)
SELECT RecipientID, MemberID, CompletionStatusID, MailingID, FirstAttempt, FinalAttempt, -1 FROM deleted
	SET NOCOUNT OFF 
END;
ALTER TABLE [dbo].[lyrCompletedRecips] ENABLE TRIGGER [lyrCompletedRecipsDeleteTrig]
GO
