ALTER PROC dbo.emailTracking_getApplicationID
@applicationName VARCHAR(100),
@applicationType VARCHAR(50),
@sendingApplicationID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SELECT @sendingApplicationID = sendingApplicationID 
	FROM dbo.sendingApplications 
	WHERE applicationType = @applicationType 
	and applicationName=@applicationName;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLL<PERSON>CK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO
