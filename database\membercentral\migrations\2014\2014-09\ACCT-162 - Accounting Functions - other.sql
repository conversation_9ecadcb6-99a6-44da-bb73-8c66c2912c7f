-- 17 min to run in dev

-- create new functions
-- link to siteadmin and superadmin roles

use membercentral
GO

declare @resourceTypeID int, @functionID int, @resourceTypeFunctionID int
select @resourceTypeID = dbo.fn_getResourceTypeID('TransactionsAdmin')

EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='transAddPayment', @displayName='Add Payment', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('transAddPayment',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID

EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='transAllocatePayment', @displayName='Allocate Payment', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('transAllocatePayment',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID

EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='transRefundPayment', @displayName='Refund Payment', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('transRefundPayment',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID

EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='transAddSale', @displayName='Add Sale', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('transAddSale',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID

EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='transAdjustSale', @displayName='Adjust Sale', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('transAdjustSale',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID

EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='transVoid', @displayName='Void Transaction', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('transVoid',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID

EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='transNSF', @displayName='Add NSF', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('transNSF',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID

EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='transWriteOffSale', @displayName='Writeoff Revenue', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('transWriteOffSale',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID

EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='transWriteOffPayment', @displayName='Writeoff Cash', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('transWriteOffPayment',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID
GO

update admin_toolTypes set toolDesc = 'Transactions Management' where toolTypeID = 14
GO

