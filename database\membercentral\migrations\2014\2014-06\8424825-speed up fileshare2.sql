/*

IF  EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[vw_cms_documentVersionsInfo]') AND name = N'vw_cms_documentVersionsInfo_documentVersionID_include_documentID')
DROP INDEX [vw_cms_documentVersionsInfo_documentVersionID_include_documentID] ON [dbo].[vw_cms_documentVersionsInfo] WITH ( ONLINE = OFF )

IF  EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[vw_cms_documentVersionsInfo]') AND name = N'vw_cms_documentVersionsInfo_documentVersionID_documentID')
DROP INDEX [vw_cms_documentVersionsInfo_documentVersionID_documentID] ON [dbo].[vw_cms_documentVersionsInfo] WITH ( ONLINE = OFF )


DROP INDEX [vw_cms_documentVersions_documentVersionID_include_contributorMemberID_author] ON [dbo].[cms_documentVersions] WITH ( ONLINE = OFF )
DROP INDEX [vw_cms_documentVersions_documentVersionID_include_contributorMemberID_author_dateModified_publicationDate] ON [dbo].[cms_documentVersions] WITH ( ONLINE = OFF )


IF  EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[vw_cms_activeDocumentVersionsInfo]'))
DROP VIEW [dbo].[vw_cms_activeDocumentVersionsInfo]

*/


USE [memberCentral]
GO
CREATE VIEW [dbo].[vw_cms_activeDocumentVersionsInfo] WITH SCHEMABINDING
AS

select d.siteID, d.documentID, dv.documentVersionID
from dbo.cms_documentVersions dv
inner join dbo.cms_documentLanguages dl	on dl.documentLanguageID = dv.documentLanguageID
	and dv.isActive = 1
inner join dbo.cms_documents d on dl.documentID = d.documentID
GO

/****** Object:  Index [vw_memberData__bit_1_2_3]    Script Date: 06/11/2014 09:30:17 ******/
CREATE UNIQUE CLUSTERED INDEX [vw_cms_activeDocumentVersionsInfo_documentVersionID_documentID] ON [dbo].[vw_cms_activeDocumentVersionsInfo] 
(
	documentVersionID ASC
)
WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = ON, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO

/****** Object:  Index [vw_memberData__bit_1_2_3]    Script Date: 06/11/2014 09:30:17 ******/
CREATE NONCLUSTERED INDEX [vw_cms_activeDocumentVersionsInfo_documentVersionID_include_documentID] ON [dbo].[vw_cms_activeDocumentVersionsInfo] 
(
	documentVersionID ASC
)
include (documentID,siteID)
WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = ON, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO

CREATE NONCLUSTERED INDEX [vw_cms_documentVersions_documentVersionID_include_contributorMemberID_author_dateModified_publicationDate] ON [dbo].[cms_documentVersions] 
(
	documentVersionID ASC
)
include (contributorMemberID, author,dateModified,publicationDate)
WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = ON, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO



