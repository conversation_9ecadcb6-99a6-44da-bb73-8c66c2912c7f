-- create new functions
-- link to siteadmin and superadmin roles

use membercentral
GO

declare @resourceTypeID int, @functionID int, @resourceTypeFunctionID int
select @resourceTypeID = dbo.fn_getResourceTypeID('InvoiceAdmin')

EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='invoiceCreate', @displayName='Create Invoice', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('invoiceCreate',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID

EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='invoiceEdit', @displayName='Edit Invoice Infomation', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('invoiceEdit',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID

EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='invoiceClose', @displayName='Close Invoice', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('invoiceClose',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID

EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='invoiceDownload', @displayName='Download Invoice', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('invoiceDownload',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID

EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='invoiceEmail', @displayName='Email Invoice', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('invoiceEmail',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID

EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='invoiceMoveTransaction', @displayName='Move Invoice Transaction', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('invoiceMoveTransaction',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID

EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='invoiceProfileManage', @displayName='Manage Invoice Profiles', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('invoiceProfileManage',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID

GO
