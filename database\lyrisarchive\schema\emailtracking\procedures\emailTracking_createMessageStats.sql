ALTER PROC dbo.emailTracking_createMessageStats
@sendingApplicationID int,
@sendingApplicationMessageID int,
@siteID int,
@ipPoolID int,
@sendgrid_subuserID int,
@sendgrid_subuserDomainID int,
@periodCode int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

	BEGIN TRAN;
		INSERT INTO dbo.messageStats (sendingApplicationID, sendingApplicationMessageID, siteID, ipPoolID,
			sendgrid_subuserID, sendgrid_subuserDomainID, periodCode, uniqueOpens, uniqueClicks, totalOpens,
			totalClicks, totalSpamReports)
		SELECT @sendingApplicationID, @sendingApplicationMessageID, @siteID, @ipPoolID, @sendgrid_subuserID,
			@sendgrid_subuserDomainID, @periodCode, 0, 0, 0, 0, 0
		WHERE NOT EXISTS (
			SELECT 1
			FROM dbo.messageStats
			WHERE sendingApplicationID=@sendingApplicationID 
			and sendingApplicationMessageID=@sendingApplicationMessageID
		);
	COMMIT TRAN;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO
