ALTER TABLE dbo.sites ADD enableDeviceDetection bit NOT NULL CONSTRAINT DF_sites_enableDeviceDetection DEFAULT 0
GO

ALTER PROC [dbo].[createSite]
	@orgID int,
	@sitecode varchar(10),
	@siteName varchar(60),
	@mainNetworkID int,
	@isLoginNetwork bit,
	@isMasterSite bit,
	@defaultLanguageID int,
	@defaultTimeZoneID int,
	@defaultCurrencyTypeID int,
	@showCurrencyType bit,
	@hasSSL bit,
	@isSSL bit,
	@allowGuestAccounts bit,
	@forceLoginPage bit,
	@useRemoteLogin bit,
	@affiliationRequired bit,
	@providesFreeFastCase bit,
	@enforceSiteAgreement bit,
	@allowMemberUpdates bit,
	@immediateMemberUpdates bit,
	@emailMemberUpdates varchar(200),
	@defaultPostalState varchar(10),
	@joinURL varchar(100),
	@pdfPassword varchar(30),
	@alternateGuestAccountCreationLink varchar(400),
	@alternateGuestAccountPopup bit,
	@alternateForgotPasswordLink varchar(400),
	@mainhostname varchar(80),
	@norightsContent varchar(max),
	@norightsNotLoggedInContent varchar(max),
	@inactiveUserContent varchar(max),
	@siteagreementContent varchar(max),
	@welcomeMessageContent varchar(max),
	@firstTimeLoginContent varchar(max),
	@siteID int OUTPUT

AS

DECLARE @rc int, @templateID int, @modeID int, @sectionID int, 
	@sectionResourceTypeID int, @siteAdminRoleID int, @superAdminRoleID int, 
	@siteAdminGroupID int, @superAdminGroupID int, @trashID int

BEGIN TRAN
	-- check for existing sitecode
	SELECT @siteID = null
	SELECT @siteID = siteID FROM dbo.sites where sitecode = @sitecode

	-- if not there, add it
	IF @siteID is not null
		GOTO on_error

	-- insert sites
	INSERT INTO dbo.sites (orgID, sitecode, siteName, defaultLanguageID, defaultTimeZoneId, defaultCurrencyTypeID,
		hasSSL, allowGuestAccounts, forceLoginPage, useRemoteLogin, affiliationRequired, 
		providesFreeFastCase, allowMemberUpdates, enforceSiteAgreement, immediateMemberUpdates, emailMemberUpdates, 
		defaultPostalState, joinURL, pdfPassword, alternateGuestAccountCreationLink, alternateGuestAccountPopup, 
		alternateForgotPasswordLink, showCurrencyType,isSSL, enableMobile, enableDeviceDetection)
	VALUES (@orgID, @sitecode, @siteName, @defaultLanguageID, @defaultTimeZoneId, @defaultCurrencyTypeID, 
		@hasSSL, @allowGuestAccounts, @forceLoginPage, @useRemoteLogin, @affiliationRequired, 
		@providesFreeFastCase, @allowMemberUpdates, @enforceSiteAgreement, @immediateMemberUpdates, @emailMemberUpdates, 
		@defaultPostalState, @joinURL, @pdfPassword, @alternateGuestAccountCreationLink, @alternateGuestAccountPopup, 
		@alternateForgotPasswordLink, @showCurrencyType, @isSSL, 0, 0)
		IF @@ERROR <> 0 GOTO on_error
		SELECT @siteID = SCOPE_IDENTITY()

	-- createSiteLanguage		
	EXEC @rc = dbo.createSiteLanguage @siteID=@siteID, @languageID=@defaultLanguageID		
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- get resourceType for site
	declare @siteResourceTypeID int
	select @siteResourceTypeID = dbo.fn_getResourceTypeID('Site')

	-- get active resource status
	declare @siteResourceStatusID int
	select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

	-- create a resourceID for the site
	DECLARE @siteResourceID int	
	exec dbo.cms_createSiteResource
		@resourceTypeID = @siteResourceTypeID,
		@siteResourceStatusID = @siteResourceStatusID,
		@siteID = @siteid,
		@isVisible = 1,
		@parentSiteResourceID = null,
		@siteResourceID = @siteResourceID OUTPUT
		IF @@ERROR <> 0 OR @siteResourceID = 0 GOTO on_error
	
	-- update site with new resource
	UPDATE dbo.sites
	SET siteResourceID = @siteResourceID
	WHERE siteID = @siteID
		IF @@ERROR <> 0 GOTO on_error
		
	-- roles
	select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')
	select @siteAdminRoleID = dbo.fn_getResourceRoleID('Site Administrator')
	select @siteAdminGroupID = groupID 
		from dbo.ams_groups 
		where groupCode = 'SiteAdmins' 
		and orgID = @orgID
	select @superAdminGroupID = groupID 
		from dbo.ams_groups 
		where groupCode = 'SuperAdmins' 
		and orgID = 1

	-- give siteAdmin Role to siteAdmin Group
	EXEC @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteID,
		@siteResourceID=@siteResourceID,
		@include=1,
		@functionID=null,
		@roleID=@siteAdminRoleID,
		@groupID=@siteAdminGroupID,
		@memberID=null,
		@inheritedRightsResourceID=null,
		@inheritedRightsFunctionID=null,
		@resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- give superAdmin Role to superAdmin Group
	EXEC @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteID,
		@siteResourceID=@siteResourceID,
		@include=1,
		@functionID=null,
		@roleID=@superAdminRoleID,
		@groupID=@superAdminGroupID,
		@memberID=null,
		@inheritedRightsResourceID=null,
		@inheritedRightsFunctionID=null,
		@resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- add default hostname
	DECLARE @hostnameID int	
	EXEC @rc = dbo.createSiteHostName @siteID=@siteID, @hostname=@mainhostname, @useRedirect=null, @hostnameID=@hostnameID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- add default template
	DECLARE @templateTypeID int
	SELECT @templateTypeID = dbo.fn_getTemplateTypeID('Page')
	EXEC @rc = dbo.cms_CreatePageTemplate @siteid=@siteID, @templateTypeID=@templateTypeID, @templateName='Default', @templateDesc='Default site template', @templateFileName='Default', @templateID=@templateID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @templateID = 0 GOTO on_error2
	
	-- add default page section
	SELECT @modeID = dbo.fn_getModeID('Normal')
	SELECT @sectionResourceTypeID = dbo.fn_getResourceTypeID('SystemCreatedSection')
	EXEC @rc = dbo.cms_createPageSection @siteID=@siteID, @sectionResourceTypeID=@sectionResourceTypeID, @ovTemplateID=@templateID, @ovTemplateIDMobile=null, @ovModeID=@modeID, @parentSectionID=null, @sectionName='Root', @sectionCode='Root', @inheritPlacements=1, @sectionID=@sectionID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- default fieldsets
	EXEC @rc = dbo.cms_createDefaultFieldsets @siteid=@siteID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add default pages
	EXEC @rc = dbo.cms_createDefaultPages @siteid=@siteID, @sectionid=@sectionID, @languageID=@defaultLanguageID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add default Relationship Categories
	EXEC @rc = dbo.cms_createDefaultRelationshipCategories @siteid=@siteID, @contributingMemberID=461530
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add default History (notes) Categories
	EXEC @rc = dbo.cms_createDefaultHistoryCategories @siteid=@siteID, @contributingMemberID=461530
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add to network
	EXEC @rc = dbo.createNetworkSite @networkID=@mainNetworkID, @siteID=@siteID, @isLoginNetwork=@isLoginNetwork, @isMasterSite=@isMasterSite
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add default FULL Frequency
	insert into dbo.sub_frequencies(frequencyName, frequency, frequencyShortName, uid, 
		rateRequired, hasInstallments, monthlyInterval, isSystemRate, siteID, status)
	values('Full', 1, 'F', newid(), 1, 1, 1, 1, @siteID, 'A')	
		IF @@ERROR <> 0 GOTO on_error2

	-- add content objects
	DECLARE @sysCreatedContentResourceTypeID int, @activesiteResourceStatusID int, @newContentid int, @newresourceid int
	select @sysCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('SystemCreatedContent')
	select @activesiteResourceStatusID = dbo.fn_getResourceStatusId('Active')
	EXEC @rc = dbo.cms_createContentObject 
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'NoRights',
		@contentDesc = null,
		@rawContent = @norightsContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET noRightsContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject 
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'NoRightsNotLoggedIn',
		@contentDesc = null,
		@rawContent = @norightsNotLoggedInContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET noRightsNotLoggedInContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'InactiveUser',
		@contentDesc = null,
		@rawContent = @inactiveUserContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET InactiveUserContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'SiteAgreement',
		@contentDesc = null,
		@rawContent = @siteagreementContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET SiteAgreementContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'Welcome Message',
		@contentDesc = null,
		@rawContent = @welcomeMessageContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET welcomeMessageContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'First Time Login Message',
		@contentDesc = null,
		@rawContent = @firstTimeLoginContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET firstTimeLoginContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2

	-- link up superusers to all new sites
	INSERT INTO dbo.ams_memberNetworkProfiles (memberID, profileID, [status], dateCreated, siteID)
	SELECT distinct mnp.memberID, mnp.profileID, 'A', getdate(), @siteID
	FROM dbo.ams_memberNetworkProfiles AS mnp 
	INNER JOIN dbo.ams_networkProfiles AS np ON mnp.profileID = np.profileID
	WHERE mnp.status = 'A'
	AND np.networkID = dbo.fn_getNetworkID('MemberCentral Super Administrators')
	AND np.status = 'A'
	AND mnp.siteID <> @siteID
		IF @@ERROR <> 0 GOTO on_error2

	EXEC @rc = dbo.cms_populateSiteResourceRightsCache @siteID=@siteID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @siteID = 0
	RETURN -1

on_error2:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

