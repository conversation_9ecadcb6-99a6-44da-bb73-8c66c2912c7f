ALTER PROC [dbo].[datadog_lyrisSendingSpeedByNode]
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- Sending Speed by Node
	declare @midnightToday datetime, @oldest datetime, @segmentlength int
	set @midnightToday = Convert(DateTime, DATEDIFF(DAY, 0, GETDATE()))
	set @oldest = dateadd(minute,-2,getdate())
	set @segmentlength = 1

	select 'lyris.sendingSpeedByNode' as metric, 'gauge' as [type], messagecount as value, 'shortname:' + shortname + ', nodename:' + nodename as tags
	FROM (
		select TOP 4 timesegment = dateadd(minute, datediff(minute,@midnightToday,eventtime)/ @segmentlength * @segmentlength, @midnightToday), sum(e.value) as messagecount, n.nodename, m.shortname
		from dbo.lyrPerformanceMetrics m
		inner join dbo.lyrMetricEvents e
			on e.metricKey = m.metrickey
			and m.shortname in ('mailsentgood','mailsentbad')
		inner join dbo.lyrConfigNodeSettings n
			on n.nodeID = e.nodeID
		where eventtime > @oldest
		group by dateadd(minute, datediff(minute,@midnightToday,eventtime)/ @segmentlength * @segmentlength, @midnightToday),n.nodename, m.shortname
		order by timesegment
	) xx

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	RETURN -1;
END CATCH
GO
