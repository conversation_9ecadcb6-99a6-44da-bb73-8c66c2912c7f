use [customApps]
GO

-- Update Kerr County Region mapping

update dbo.TX_Regions set Region = 4 where County='Kerr'
GO


-- Update only Kerr County for new mapping 

DECLARE @orgID int, @minMemberID int, @region varchar(8)
SELECT @orgID = orgID FROM membercentral.dbo.organizations WHERE orgcode = 'TX'

IF OBJECT_ID('tempdb..#tmpTXMembers') IS NOT NULL
	DROP TABLE #tmpTXMembers
CREATE TABLE #tmpTXMembers (memberID int, region varchar(8), currValue varchar(200) DEFAULT '')


insert into #tmpTXMembers (memberID, region)
select distinct m.memberid, 'Region ' + cast(r.region as varchar(1))
from membercentral.dbo.ams_members as m WITH(NOLOCK)
inner join membercentral.dbo.ams_memberAddresses as ma WITH(NOLOCK) on ma.memberid = m.memberid
inner join membercentral.dbo.ams_memberAddressTypes as mat WITH(NOLOCK) on mat.addressTypeID = ma.addressTypeID and mat.addressType = 'Publishing Address'
inner join membercentral.dbo.ams_states as s WITH(NOLOCK) on s.stateID = ma.stateID and s.code = 'TX' and s.countryID = 1
inner join customApps.dbo.TX_Regions as r WITH(NOLOCK) on r.county = ma.county and ma.county = 'Kerr'
inner join membercentral.dbo.cache_members_groups as cmg WITH(NOLOCK) on cmg.memberID = m.memberID
inner join membercentral.dbo.ams_groups as g WITH(NOLOCK) on g.groupID = cmg.groupID
where m.orgID = @orgID
and m.status = 'A'
and g.groupCode = 'Autoregion' 

update tmp
set tmp.currValue = isnull(mdcv.columnValueString,'')
from #tmpTXMembers as tmp
inner join membercentral.dbo.ams_memberData as md WITH(NOLOCK) on md.memberid = tmp.memberid
inner join membercentral.dbo.ams_memberDataColumnValues as mdcv WITH(NOLOCK) on mdcv.valueID = md.valueID
inner join membercentral.dbo.ams_memberDataColumns as mdc WITH(NOLOCK) on mdc.columnid = mdcv.columnID and mdc.columnName = 'Region'

delete from #tmpTXMembers
where region = currValue

SELECT @minMemberID = min(memberID) FROM #tmpTXMembers
WHILE @minMemberID is not null BEGIN
	select @region=region from #tmpTXMembers where memberID = @minMemberID	
	exec membercentral.dbo.ams_setMemberData @minMemberID, @orgID, 'Region', @region, 1
	select @minMemberID = min(memberID) from #tmpTXMembers where memberID > @minMemberID
END

declare @memberIDList varchar(max), @itemGroupUID uniqueidentifier
SELECT @memberIDList = COALESCE(@memberIDList + ',', '') + cast(memberID as varchar(10)) 
	from #tmpTXMembers
	group by memberid
IF @memberIDList is not null BEGIN
	set @itemGroupUID = null
	EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberIDList, @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT
END

IF OBJECT_ID('tempdb..#tmpTXMembers') IS NOT NULL
	DROP TABLE #tmpTXMembers
