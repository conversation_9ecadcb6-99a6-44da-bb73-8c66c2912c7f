use seminarWeb
GO
CREATE FUNCTION dbo.fn_getMCAssnDataForEnrollment (@depoMemberDataID int, @siteCode varchar(10))
RETURNS @tblMemberData TABLE
(
	firstname varchar(100),
	lastname varchar(100),
	company varchar(200), 
	membernumber varchar(50),
	email varchar(255), 
	[address] varchar(450), 
	city varchar(100),
	[state] varchar(100),
	postalCode varchar(25), 
	phone varchar(40), 
	fax varchar(40)
)
AS
BEGIN
	
	insert into @tblMemberData
	select TOP 1 m.firstname, m.lastname, nullif(m.company,'') as company, m.membernumber, nullif(me.email,'') as email, 
		nullif(ltrim(ma.address1 + isnull(' ' + nullif(ma.address2,''),'') + isnull(' ' + nullif(ma.address3,''),'')),'') as [address], 
		nullif(ma.city,'') as city, mas.code as [state], nullif(ma.postalCode,'') as postalCode, 
		nullif(mp.phone,'') as phone, nullif(mp2.phone,'') as fax
	from membercentral.membercentral.dbo.ams_networkProfiles as np
	inner join membercentral.membercentral.dbo.ams_membernetworkProfiles as mnp on mnp.profileID = np.profileID 
	inner join membercentral.membercentral.dbo.sites as s on s.siteID = mnp.siteID 
	inner join membercentral.membercentral.dbo.ams_members as m on m.memberID = mnp.memberID 
	left outer join membercentral.membercentral.dbo.ams_memberEmails as me
		inner join membercentral.membercentral.dbo.ams_memberEmailTypes as met on met.emailTypeID = me.emailTypeID and met.emailTypeOrder = 1
		on me.memberID = m.memberID
	left outer join membercentral.membercentral.dbo.ams_memberAddresses as ma on ma.memberID = m.memberID and ma.addressTypeID = m.billingAddressTypeID
	left outer join membercentral.membercentral.dbo.ams_states as mas on mas.stateID = ma.stateID
	left outer join membercentral.membercentral.dbo.ams_memberPhones as mp
		inner join membercentral.membercentral.dbo.ams_memberPhoneTypes as mpt on mpt.phoneTypeID = mp.phoneTypeID and mpt.phoneType = 'Phone'
		on mp.addressID = ma.addressID
	left outer join membercentral.membercentral.dbo.ams_memberPhones as mp2
		inner join membercentral.membercentral.dbo.ams_memberPhoneTypes as mpt2 on mpt2.phoneTypeID = mp2.phoneTypeID and mpt2.phoneType = 'Fax'
		on mp2.addressID = ma.addressID
	where np.depomemberdataid = @depoMemberDataID
	and np.status = 'A'
	and mnp.status = 'A'
	and s.siteCode = @siteCode
	and m.memberID = m.activeMemberID
	and m.status <> 'D'

	IF @@ROWCOUNT = 0
		insert into @tblMemberData
		select TOP 1 m.firstname, m.lastname, nullif(m.company,'') as company, m.membernumber, nullif(me.email,'') as email, 
			nullif(ltrim(ma.address1 + isnull(' ' + nullif(ma.address2,''),'') + isnull(' ' + nullif(ma.address3,''),'')),'') as [address], 
			nullif(ma.city,'') as city, mas.code as [state], nullif(ma.postalCode,'') as postalCode, 
			nullif(mp.phone,'') as phone, nullif(mp2.phone,'') as fax
		from trialsmith.dbo.depomemberdata as d
		inner join membercentral.membercentral.dbo.ams_members as m on m.memberID = d.MCmemberIDtemp
		inner join membercentral.membercentral.dbo.ams_members as m2 on m2.memberID = m.activeMemberID
		inner join membercentral.membercentral.dbo.organizations as o on o.orgID = m2.orgID
		left outer join membercentral.membercentral.dbo.ams_memberEmails as me
			inner join membercentral.membercentral.dbo.ams_memberEmailTypes as met on met.emailTypeID = me.emailTypeID and met.emailTypeOrder = 1
			on me.memberID = m2.memberID
		left outer join membercentral.membercentral.dbo.ams_memberAddresses as ma on ma.memberID = m2.memberID and ma.addressTypeID = m2.billingAddressTypeID
		left outer join membercentral.membercentral.dbo.ams_states as mas on mas.stateID = ma.stateID
		left outer join membercentral.membercentral.dbo.ams_memberPhones as mp
			inner join membercentral.membercentral.dbo.ams_memberPhoneTypes as mpt on mpt.phoneTypeID = mp.phoneTypeID and mpt.phoneType = 'Phone'
			on mp.addressID = ma.addressID
		left outer join membercentral.membercentral.dbo.ams_memberPhones as mp2
			inner join membercentral.membercentral.dbo.ams_memberPhoneTypes as mpt2 on mpt2.phoneTypeID = mp2.phoneTypeID and mpt2.phoneType = 'Fax'
			on mp2.addressID = ma.addressID
		where d.depomemberdataid = @depoMemberDataID
		and o.orgCode = @siteCode
		and m2.status <> 'D'
	
	RETURN 
END
GO

CREATE FUNCTION dbo.fn_getEnrolleeMemberData (@enrollmentID int, @orgcodeForData varchar(10))
RETURNS TABLE 
AS
RETURN 
(
	select top 1 
		case when tla.isLiveOnNewPlatform = 1 then coalesce(assnData.firstname,d.firstName) else d.firstName end as firstName,
		case when tla.isLiveOnNewPlatform = 1 then coalesce(assnData.lastname,d.lastname) else d.lastname end as lastname,
		case when tla.isLiveOnNewPlatform = 1 then coalesce(assnData.company,d.billingfirm) else d.billingfirm end as company,
		case when tla.isLiveOnNewPlatform = 1 then assnData.membernumber else null end as memberNumber,
		case when tla.isLiveOnNewPlatform = 1 then coalesce(assnData.email,d.email) else d.email end as email,
		case when tla.isLiveOnNewPlatform = 1 then coalesce(assnData.address,ltrim(d.billingAddress + isnull(' ' + nullif(d.billingAddress2,''),''))) else billingCity end as [address],
		case when tla.isLiveOnNewPlatform = 1 then coalesce(assnData.city,d.billingCity) else d.billingCity end as city,
		case when tla.isLiveOnNewPlatform = 1 then coalesce(assnData.state,d.billingState) else d.billingState end as [state],
		case when tla.isLiveOnNewPlatform = 1 then coalesce(assnData.postalCode,d.billingZip) else d.billingZip end as zip,
		case when tla.isLiveOnNewPlatform = 1 then coalesce(assnData.phone,d.phone) else d.phone end as phone,
		case when tla.isLiveOnNewPlatform = 1 then coalesce(assnData.fax,d.fax) else d.fax end as fax
	from dbo.tblEnrollments AS e 
	INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
	INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
	INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
	INNER JOIN trialsmith.dbo.depoTLA AS tla ON tla.state = p.orgcode
	OUTER APPLY dbo.fn_getMCAssnDataForEnrollment(d.depomemberdataID,isnull(@orgcodeForData,tla.state)) as assnData
	where e.enrollmentID = @enrollmentID

)
GO

ALTER Function [dbo].[fn_swl_getEnrollments] (@seminarID int, @orgcodeForData varchar(10)) 
returns table
AS
RETURN (

	SELECT TOP 100 PERCENT e.enrollmentID, e.userid, eswl.goToMeetingUID, ps.pin, eswl.swlcode, p.orgcode, p.catalogURL, u.depomemberdataID, 
		md.FirstName, md.LastName, md.Company, md.membernumber, md.Email, md.address, md.city, md.state, md.zip, md.phone, md.Fax, 
		e.dateEnrolled, eswl.attended, eswl.attendedPhone, eswl.joinTime, eswl.exitTime, eswl.duration, eswl.durationPhone, 
		eswl.completedPolling, e.dateCompleted, e.passed, 
		(SELECT COUNT(*) FROM dbo.tblLogSWLive AS log1 WHERE enrollmentID = e.enrollmentID AND seminarID = e.seminarID AND contact LIKE '%@%') AS EmailCount,
		(SELECT COUNT(*) FROM dbo.tblLogSWLive AS log2 WHERE enrollmentID = e.enrollmentID AND seminarID = e.seminarID AND contact NOT LIKE '%@%') AS FaxCount,
		(SELECT COUNT(*) FROM dbo.tblEnrollmentsAndCredit WHERE enrollmentID = e.enrollmentID) AS CreditCount,
		pgtm.parkedID, sswl.phoneAttendee, sswl.codeAttendee,
		CASE 
		WHEN (select count(saf.formID)
				FROM dbo.tblEnrollments AS e2
				INNER JOIN dbo.tblSeminarsAndForms AS saf ON e2.seminarID = saf.seminarID
				LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
					INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
					INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
						and f.isPublished = 1
						and getdate() between f.dateStartPublish and f.dateEndPublish	
					on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e2.enrollmentID
				WHERE e2.enrollmentID = e.enrollmentID
				AND saf.loadPoint = 'evaluation'
				AND exists(
					SELECT sac.seminarCreditID, eac.idNumber, eac.earnedCertificate, caswl.evaluationRequired
					FROM dbo.tblEnrollmentsAndCredit AS eac 
					INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
					INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID 
					INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
					INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID 
					INNER JOIN dbo.tblCreditStatuses AS cstat ON sac.statusID = cstat.statusID
					INNER JOIN dbo.tblCreditAuthoritiesSWLive as caswl on caswl.authorityID = ca.authorityID
					WHERE eac.enrollmentID = e.enrollmentID
					and evaluationRequired = 1)
				AND (safr.responseID is null or r.dateCompleted is null)) > 0 THEN 0
		ELSE 1
		END as allEvaluationCompleted
	FROM dbo.tblEnrollments AS e 
	INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
	INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
	INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
	INNER JOIN dbo.tblSeminarsSWLive AS sswl ON e.seminarID = sswl.seminarID
	LEFT OUTER JOIN dbo.tblParkedGTMSeats AS pgtm ON e.enrollmentID = pgtm.enrollmentID
	LEFT OUTER JOIN dbo.tblParkedPhoneSeats AS ps ON e.enrollmentID = ps.enrollmentID
	CROSS APPLY dbo.fn_getEnrolleeMemberData(e.enrollmentID,@orgcodeForData) as md
	WHERE e.seminarID = @seminarID
	AND e.isActive = 1
	ORDER BY e.dateEnrolled

)
GO

ALTER PROC [dbo].[swl_exportAllRegistrants]
@seminarID int,
@pathToTempFolder varchar(max)

AS

SET NOCOUNT ON

-- drop if exists
IF OBJECT_ID('tempdb..##tmpSWLRegistrants') IS NOT NULL 
	DROP TABLE ##tmpSWLRegistrants

-- put data into temp table 
SELECT e.depomemberdataid, 
	dbo.fn_csvSafeString(e.firstname) as firstname, 
	dbo.fn_csvSafeString(e.lastname) as lastname, 
	dbo.fn_csvSafeString(e.membernumber) as membernumber, 
	dbo.fn_csvSafeString(e.company) as company, 
	dbo.fn_csvSafeString(e.address) as [address], 
	dbo.fn_csvSafeString(e.city) as city,
	dbo.fn_csvSafeString(e.state) as [state],
	dbo.fn_csvSafeString(e.zip) as zip,
	dbo.fn_csvSafeString(e.phone) as phone,
	dbo.fn_csvSafeString(e.fax) as fax,
	dbo.fn_csvSafeString(e.email) as email,
	e.dateEnrolled,
	e.attended as webAttended, 
	dbo.fn_csvSafeString(e.joinTime) as webJoinTime, 
	dbo.fn_csvSafeString(e.exitTime) as webExitTime, 
	e.duration as webDuration, 
	e.attendedPhone as phoneAttended, 
	e.durationPhone as phoneDuration, 
	dbo.fn_csvSafeString(e.orgcode) as signedUpOn,
	e.pin, 
	dbo.fn_csvSafeString(e.swlcode) as SWLCode, 
	dbo.fn_csvSafeString(e.phoneAttendee) as phoneAttendee, 
	dbo.fn_csvSafeString(e.codeAttendee) as codeAttendee
INTO ##tmpSWLRegistrants
FROM dbo.fn_swl_getEnrollments(@seminarID,null) as e
ORDER BY e.dateEnrolled

-- export data
DECLARE @csv varchar(400), @cmd varchar(6000)
SELECT @csv = @pathToTempFolder + '\SWLRegExport.csv'
set @cmd = 'bcp ##tmpSWLRegistrants out ' + @csv + ' -c -t, -T -S' + CAST(serverproperty('servername') as varchar(20))
exec master..xp_cmdshell @cmd	

-- get fields returned
EXEC tempdb.dbo.SP_COLUMNS ##tmpSWLRegistrants

-- drop temp table
IF OBJECT_ID('tempdb..##tmpSWLRegistrants') IS NOT NULL 
	DROP TABLE ##tmpSWLRegistrants

SET NOCOUNT OFF

GO

ALTER PROC [dbo].[swl_exportAllRegistrantsSWAdmin]
@seminarID varchar(max),
@pathToTempFolder varchar(max),
@limitToOrgCode varchar(5)

AS

SET NOCOUNT ON

-- drop if exists
IF OBJECT_ID('tempdb..##tmpSWLRegistrants') IS NOT NULL 
	DROP TABLE ##tmpSWLRegistrants

declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@limittoorgcode)

-- put data into temp table 
SELECT distinct sswl.datestart as ProgramDate,
	dbo.fn_csvSafeString(sem.seminarName) as SeminarName,
	dbo.fn_csvSafeString(e2.firstname) as firstname, 
	dbo.fn_csvSafeString(e2.lastname) as lastname, 
	dbo.fn_csvSafeString(e2.membernumber) as membernumber, 
	dbo.fn_csvSafeString(e2.company) as company, 
	dbo.fn_csvSafeString(e2.address) as [address], 
	dbo.fn_csvSafeString(e2.city) as city,
	dbo.fn_csvSafeString(e2.state) as [state],
	dbo.fn_csvSafeString(e2.zip) as zip,
	dbo.fn_csvSafeString(e2.phone) as phone,
	dbo.fn_csvSafeString(e2.fax) as fax,
	dbo.fn_csvSafeString(e2.email) as email,
	e2.dateEnrolled,
	e2.attended as webAttended, 
	dbo.fn_csvSafeString(e2.joinTime) as webJoinTime, 
	dbo.fn_csvSafeString(e2.exitTime) as webExitTime, 
	e2.duration as webDuration, 
	e2.attendedPhone as phoneAttended, 
	e2.durationPhone as phoneDuration
INTO ##tmpSWLRegistrants
FROM dbo.fn_IntListToTable(@seminarID) as list
INNER JOIN dbo.tblSeminars as sem on list.intValue = sem.seminarID
INNER JOIN dbo.tblSeminarsSWLive AS sswl ON sem.seminarid = sswl.seminarID
CROSS APPLY dbo.fn_swl_getEnrollments(sem.seminarID,@limitToOrgCode) as e2
INNER JOIN dbo.tblEnrollments AS e on e.enrollmentID = e2.enrollmentID
INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = e2.depoMemberDataID 
WHERE (e.participantID = @participantID OR sem.participantID = @participantID)
AND (d.adminflag2 is null or d.adminflag2 <> 'Y')
ORDER BY 1 desc, 2, 14

-- export data
DECLARE @csv varchar(400), @cmd varchar(6000)
SELECT @csv = @pathToTempFolder + '\SWLRegExport.csv'
set @cmd = 'bcp ##tmpSWLRegistrants out ' + @csv + ' -c -t, -T -S' + CAST(serverproperty('servername') as varchar(20))
exec master..xp_cmdshell @cmd	

-- get fields returned
EXEC tempdb.dbo.SP_COLUMNS ##tmpSWLRegistrants

-- drop temp table
DROP TABLE ##tmpSWLRegistrants

SET NOCOUNT OFF

GO

ALTER PROCEDURE [dbo].[swl_getEnrollments]
@seminarID int

AS

SELECT *
FROM dbo.fn_swl_getEnrollments(@seminarID,null)
ORDER BY dateEnrolled

RETURN
GO

ALTER PROC [dbo].[swl_getEnrollmentsForSWAdmin]
@seminarID int,
@orgcode varchar(5)

AS

declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@orgcode)

SELECT e.enrollmentID, e.userid, e.goToMeetingUID, e.pin, e.swlcode, e.orgcode, e.catalogURL, e.depomemberdataID, 
	e.FirstName, e.LastName, e.Company, e.membernumber, e.Email, e.address, e.city, e.state, e.zip, e.phone, e.Fax, 
	e.dateEnrolled, e.attended, e.attendedPhone, e.joinTime, e.exitTime, e.duration, e.durationPhone, 
	e.completedPolling, e.dateCompleted, e.passed, e.EmailCount, e.FaxCount, e.CreditCount, e.parkedID, 
	e.phoneAttendee, e.codeAttendee, e.allEvaluationCompleted
FROM dbo.fn_swl_getEnrollments(@seminarID,@orgcode) as e
INNER JOIN dbo.tblEnrollments as e2 on e2.enrollmentID = e.enrollmentID
INNER JOIN dbo.tblSeminars as s on s.seminarid = e2.seminarID
INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = e.depoMemberDataID 
WHERE (e2.participantID = @participantID OR s.participantID = @participantID)
AND (d.adminflag2 is null or d.adminflag2 <> 'Y')
ORDER BY e.dateEnrolled

GO

ALTER PROCEDURE [dbo].[swl_getEnrollmentByEnrollmentID]
@enrollmentID int

AS

SELECT top 1 e.enrollmentID, u.depomemberdataID, md.FirstName, md.LastName, md.Email, md.Email AS assocEmail, md.fax, 
	sswl.wddxTimeZones, sswl.dateStart, sswl.dateEnd, s.seminarID, s.seminarName, sswl.swltypeID, 
	eswl.goToMeetingUID, eswl.swlcode, p.orgcode, p.catalogURL, p.wddxTimeZones as OrgWddxTimeZones,
	sswl.gotoMeetingID, sswl.programPassword, sswl.phoneAttendee, sswl.codeAttendee, sswl.offerDVD, 
	sswl.premiereUsePIN, ps.pin, sswl.lineProvider
FROM dbo.tblEnrollments AS e 
INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
INNER JOIN dbo.tblSeminars AS s ON e.seminarID = s.seminarID and s.isDeleted = 0
INNER JOIN dbo.tblSeminarsSWLive AS sswl ON s.seminarID = sswl.seminarID 
INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID
INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
CROSS APPLY dbo.fn_getEnrolleeMemberData(e.enrollmentID,null) as md
LEFT OUTER JOIN dbo.tblParkedPhoneSeats as ps on ps.enrollmentID = e.enrollmentID
WHERE e.enrollmentID = @enrollmentID
AND e.isActive = 1

GO

ALTER PROC [dbo].[sw_getEnrollmentByEnrollmentID]
@enrollmentID int

AS

select e.enrollmentID, e.userID, 
	CASE
	WHEN eswod.swodID is not null THEN 'SWOD'
	WHEN eswl.swlID is not null THEN 'SWL'
	WHEN eswtl.swtlID is not null THEN 'SWTL'
	ELSE ''
	END as format,
	CASE 
	WHEN e.seminarID is not null THEN e.seminarID
	WHEN e.titleID is not null then e.titleID
	ELSE ''
	END as contentID,
	CASE 
	WHEN e.seminarID is not null THEN s.seminarName
	WHEN e.titleID is not null then t.titleName
	ELSE ''
	END as contentName,
	CASE
	WHEN e.seminarID is not null THEN sTLA.Description
	WHEN e.titleID is not null then tTLA.Description
	ELSE ''
	END as publisher,
	p.orgcode AS signupOrgCode, eTLA.Description as signuporgDescription,
	md.FirstName + ' ' + md.LastName AS fullname, d.depomemberdataID, d.sourceID,
	e.dateEnrolled, e.dateCompleted, e.passed, md.company as billingFirm, md.address as billingaddress,
	null as billingaddress2, null as billingaddress3, md.city as billingcity, md.state as billingstate, 
	md.zip as billingzip, eswl.joinTime, eswl.exitTime, eswl.duration, eswl.durationphone,
	md.Email, md.Fax, eswod.calcTimeSpent
from dbo.tblEnrollments as e
INNER JOIN dbo.tblUsers AS u ON u.userID = e.userID 
INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
INNER JOIN dbo.tblParticipants AS p ON p.participantID = e.participantID 
INNER JOIN trialsmith.dbo.depoTLA AS eTLA ON eTLA.State = p.orgcode 
CROSS APPLY dbo.fn_getEnrolleeMemberData(e.enrollmentID,null) as md
LEFT OUTER JOIN dbo.tblEnrollmentsSWTL AS eswtl ON e.enrollmentID = eswtl.enrollmentID 
LEFT OUTER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
LEFT OUTER JOIN dbo.tblEnrollmentsSWOD AS eswod ON e.enrollmentID = eswod.enrollmentID
LEFT OUTER JOIN dbo.tblSeminars as s 
	INNER JOIN dbo.tblParticipants AS sP ON sP.participantID = s.participantID 
	INNER JOIN trialsmith.dbo.depoTLA AS sTLA ON sTLA.State = sP.orgcode 
	on s.seminarID = e.seminarID and s.isDeleted = 0
LEFT OUTER JOIN dbo.tblTitles as t 
	INNER JOIN dbo.tblParticipants AS tP ON tP.participantID = t.participantID 
	INNER JOIN trialsmith.dbo.depoTLA AS tTLA ON tTLA.State = tP.orgcode 
	on t.titleID = e.titleID and t.isDeleted = 0
where e.enrollmentID = @enrollmentID
AND e.isActive = 1

GO

ALTER procedure [dbo].[swl_getFormBuilderEnrollments]
	@seminarID int
as

;with tmp as (
	select 
		saf.seminarFormID, f.formtypeid, r.responseID, 
		r.depomemberdataid, r.dateDelivered, r.dateCompleted, 
		r.passingPct, r.isActive, rd.questionID, rd.isCorrect
	from 
		seminarweb.dbo.tblSeminarsAndFormResponses as safr
		inner join seminarweb.dbo.tblSeminarsAndForms as saf on saf.seminarFormID = safr.seminarFormID
			and saf.seminarID = @seminarID
		inner join formbuilder.dbo.tblForms as f on f.formid = saf.formid
		inner join formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID
		inner join formbuilder.dbo.tblResponseDetails as rd on rd.responseid = r.responseid
), tmpCorr AS (
	select responseID, count(questionID) as correctCount
	from tmp
	where isCorrect = 1
	group by responseID
)

-- need enrollmentID, attended only
select top 100 percent e.enrollmentID, eswl.attended
from dbo.tblEnrollments AS e 
inner join dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
INNER JOIN dbo.tblUsers AS u ON u.userID = e.userID 
left outer join (
	select count(seminarFormID) as thisCount, depomemberdataid
	from tmp
	left outer join tmpCorr on tmpCorr.responseID = tmp.responseID
	group by depomemberdataid
	) tmp2 on tmp2.depomemberdataID = u.depomemberdataID
where e.seminarID = @seminarID
AND e.isActive = 1
and (tmp2.thisCount = 0 or tmp2.thisCount is null)
and eswl.attended = 1
order by e.dateEnrolled

return
GO

ALTER PROC [dbo].[swl_exportAttendeesForCredit]
@seminarID int,
@pathToTempFolder varchar(max)

AS

SET NOCOUNT ON

-- drop if exists
IF OBJECT_ID('tempdb..##tmpRegistrants') IS NOT NULL 
	DROP TABLE ##tmpRegistrants

-- does seminar use PINS?
DECLARE @usePIN bit
SELECT @usePIN = premiereUsePIN FROM dbo.tblSeminarsSWLive where seminarID = @seminarID

-- get data
select dbo.fn_csvSafeString(spon.webjoinTime) as webjoinTime, 
	dbo.fn_csvSafeString(spon.webexitTime) as webExitTime, 
	cast(spon.webduration as int) as webduration, 
	cast(spon.durationPhone as int) as phoneduration, 
	spon.completedPolling, 
	dbo.fn_csvSafeString(spon.idnumber) as idnumber, 
	spon.sponsorID, 
	dbo.fn_csvSafeString(md.LastName) as lastName, 
	dbo.fn_csvSafeString(md.FirstName) as FirstName, 
	dbo.fn_csvSafeString(md.company) as company, 
	dbo.fn_csvSafeString(md.address) as address, 
	dbo.fn_csvSafeString(md.city) as city, 
	dbo.fn_csvSafeString(md.state) as state, 
	dbo.fn_csvSafeString(md.zip) as zip, 
	dbo.fn_csvSafeString(md.Email) as email, 
	dbo.fn_csvSafeString(md.Phone) as phone, 
	dbo.fn_csvSafeString(md.Fax) as fax
INTO ##tmpAttendees
FROM (
	SELECT e.enrollmentID, eswl.joinTime as webjointime, eswl.exitTime as webexittime, 
		eswl.duration as webduration, eswl.completedpolling, eac.idNumber, csa.sponsorID, 
		eswl.durationPhone
	FROM dbo.tblUsers AS u 
	INNER JOIN dbo.tblEnrollments AS e ON u.userID = e.userID 
	INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
	INNER JOIN dbo.tblEnrollmentsAndCredit AS eac ON e.enrollmentID = eac.enrollmentID 
	INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
	INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID
	WHERE e.seminarID = @seminarID
	AND e.isActive = 1
	AND eswl.attended = 1
	AND (@usePIN = 0 OR (@usePIN = 1 AND eswl.attendedPhone = 1))
	) as spon
CROSS APPLY dbo.fn_getEnrolleeMemberData(spon.enrollmentID,null) as md
ORDER BY spon.sponsorID, md.lastname, md.firstname

-- get fields returned
EXEC tempdb.dbo.SP_COLUMNS ##tmpAttendees

-- export attendee list for each org
DECLARE @minSpon int, @sql varchar(max), @csv varchar(max), @cmd varchar(6000)
SELECT @minSpon = min(sponsorID) from ##tmpAttendees
WHILE @minSpon IS NOT NULL
BEGIN
	SELECT @sql = 'SELECT * from ##tmpAttendees where sponsorID = ' + CAST(@minSpon as varchar(6))
	SELECT @csv = @pathToTempFolder + '\' + CAST(@minSpon as varchar(6)) + '.csv'

	set @cmd = 'bcp "' + @sql + '" QUERYOUT ' + @csv + ' -c -t, -T -S' + CAST(serverproperty('servername') as varchar(20))
	exec master..xp_cmdshell @cmd	

	SELECT @minSpon = min(sponsorID) from ##tmpAttendees WHERE sponsorID > @minSpon
END

-- drop temp table
DROP TABLE ##tmpAttendees

SET NOCOUNT OFF

GO

ALTER Function [dbo].[fn_swl_getEnrollment] (
	@enrollmentID int
) 
returns table
AS
RETURN (

	SELECT e.seminarID, e.enrollmentID, e.userid, eswl.goToMeetingUID, ps.pin, eswl.swlcode, p.orgcode, p.catalogURL, u.depomemberdataID, 
		md.FirstName, md.LastName, md.Company, md.membernumber, md.Email, md.address, md.city, md.state, md.zip, md.phone, md.Fax, 
		e.dateEnrolled, eswl.attended, eswl.attendedPhone, eswl.joinTime, eswl.exitTime, eswl.duration, eswl.durationPhone, 
		eswl.completedPolling, e.dateCompleted, e.passed, 
		(SELECT COUNT(*) FROM dbo.tblLogSWLive AS log1 WHERE enrollmentID = e.enrollmentID AND seminarID = e.seminarID AND contact LIKE '%@%') AS EmailCount,
		(SELECT COUNT(*) FROM dbo.tblLogSWLive AS log2 WHERE enrollmentID = e.enrollmentID AND seminarID = e.seminarID AND contact NOT LIKE '%@%') AS FaxCount,
		(SELECT COUNT(*) FROM dbo.tblEnrollmentsAndCredit WHERE enrollmentID = e.enrollmentID) AS CreditCount,
		pgtm.parkedID, sswl.phoneAttendee, sswl.codeAttendee,
		CASE 
		WHEN (select count(saf.formID)
				FROM dbo.tblEnrollments AS e2
				INNER JOIN dbo.tblSeminarsAndForms AS saf ON e2.seminarID = saf.seminarID
				LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
					INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
					INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
						and f.isPublished = 1
						and getdate() between f.dateStartPublish and f.dateEndPublish	
					on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e2.enrollmentID
				WHERE e2.enrollmentID = e.enrollmentID
				AND saf.loadPoint = 'evaluation'
				AND exists(
					SELECT sac.seminarCreditID, eac.idNumber, eac.earnedCertificate, caswl.evaluationRequired
					FROM dbo.tblEnrollmentsAndCredit AS eac 
					INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
					INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID 
					INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
					INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID 
					INNER JOIN dbo.tblCreditStatuses AS cstat ON sac.statusID = cstat.statusID
					INNER JOIN dbo.tblCreditAuthoritiesSWLive as caswl on caswl.authorityID = ca.authorityID
					WHERE eac.enrollmentID = e.enrollmentID
					and evaluationRequired = 1)
				AND (safr.responseID is null or r.dateCompleted is null)) > 0 THEN 0
		ELSE 1
		END as allEvaluationCompleted
	FROM dbo.tblEnrollments AS e 
	INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
	INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
	INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
	INNER JOIN dbo.tblSeminarsSWLive AS sswl ON e.seminarID = sswl.seminarID
	LEFT OUTER JOIN dbo.tblParkedGTMSeats AS pgtm ON e.enrollmentID = pgtm.enrollmentID
	LEFT OUTER JOIN dbo.tblParkedPhoneSeats AS ps ON e.enrollmentID = ps.enrollmentID
	CROSS APPLY dbo.fn_getEnrolleeMemberData(e.enrollmentID,null) as md
	WHERE e.enrollmentID = @enrollmentID
	AND e.isActive = 1
	
)
GO

CREATE FUNCTION dbo.fn_getMemberDataForOrg (@depomemberdataID int, @orgcodeForData varchar(10))
RETURNS TABLE 
AS
RETURN 
(
	select top 1 
		case when tla.isLiveOnNewPlatform = 1 then coalesce(assnData.firstname,d.firstName) else d.firstName end as firstName,
		case when tla.isLiveOnNewPlatform = 1 then coalesce(assnData.lastname,d.lastname) else d.lastname end as lastname,
		case when tla.isLiveOnNewPlatform = 1 then coalesce(assnData.company,d.billingfirm) else d.billingfirm end as company,
		case when tla.isLiveOnNewPlatform = 1 then assnData.membernumber else null end as memberNumber,
		case when tla.isLiveOnNewPlatform = 1 then coalesce(assnData.email,d.email) else d.email end as email,
		case when tla.isLiveOnNewPlatform = 1 then coalesce(assnData.address,ltrim(d.billingAddress + isnull(' ' + nullif(d.billingAddress2,''),''))) else billingCity end as [address],
		case when tla.isLiveOnNewPlatform = 1 then coalesce(assnData.city,d.billingCity) else d.billingCity end as city,
		case when tla.isLiveOnNewPlatform = 1 then coalesce(assnData.state,d.billingState) else d.billingState end as [state],
		case when tla.isLiveOnNewPlatform = 1 then coalesce(assnData.postalCode,d.billingZip) else d.billingZip end as zip,
		case when tla.isLiveOnNewPlatform = 1 then coalesce(assnData.phone,d.phone) else d.phone end as phone,
		case when tla.isLiveOnNewPlatform = 1 then coalesce(assnData.fax,d.fax) else d.fax end as fax
	from trialsmith.dbo.depoTLA AS tla
	INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = @depomemberdataID
	OUTER APPLY dbo.fn_getMCAssnDataForEnrollment(d.depomemberdataID,tla.state) as assnData
	where tla.state = @orgcodeForData
)
GO

ALTER PROCEDURE [dbo].[swod_getEnrollmentByEnrollmentID]
@enrollmentID int,
@includeInactive bit = 0

AS

SELECT top 1 e.enrollmentID, e.userid, u.depomemberdataID, md.FirstName, md.LastName, md.Email, 
	s.seminarID, s.seminarName, p.catalogURL, p.orgcode, e.dateEnrolled, ec.lastDateToComplete
FROM dbo.tblEnrollments AS e 
INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
INNER JOIN dbo.tblSeminars AS s ON e.seminarID = s.seminarID 
INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
CROSS APPLY dbo.fn_getEnrolleeMemberData(e.enrollmentID,null) as md
LEFT OUTER JOIN dbo.tblEnrollmentsAndCredit ec on e.enrollmentID = ec.enrollmentID
WHERE e.enrollmentID = @enrollmentID
AND e.isActive = CASE when @includeInactive = 0 THEN 1 else e.isActive END
ORDER BY e.dateEnrolled DESC

GO

ALTER PROCEDURE [dbo].[swod_getIncompleteEnrollments]
	@orgcode varchar(10)
AS

SELECT TOP 100 PERCENT md.LastName, md.FirstName, e.UserID as UserId, md.Email as Email,
	s.seminarName as Seminar, convert(varchar(10),e.dateEnrolled,101) as dateEnrolled,
	CASE WHEN e.dateCompleted IS NOT NULL THEN convert(varchar(10),e.dateCompleted,101) ELSE 'In Progress' END AS [Date Completed]
FROM dbo.tblEnrollments AS e 
INNER JOIN dbo.tblSeminars as s on s.seminarID = e.seminarID
INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
INNER JOIN dbo.tblEnrollmentsSWOD AS eswod ON e.enrollmentID = eswod.enrollmentID 
INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
CROSS APPLY dbo.fn_getEnrolleeMemberData(e.enrollmentID,null) as md
WHERE e.isActive = 1
AND (d.adminflag2 is null or d.adminflag2 <> 'Y')
AND e.dateCompleted IS NULL
AND p.orgcode = @orgcode
ORDER BY e.dateEnrolled desc, md.lastName, md.firstName, s.seminarName

GO

ALTER Function [dbo].[fn_swod_getEnrollments] (
	@seminarID int,
	@orgcodeForData varchar(10)
) 
returns table
AS
RETURN (

	SELECT TOP 100 PERCENT e.userid, e.enrollmentID, p.orgcode, u.depomemberdataID, 
		md.FirstName, md.LastName, md.Company, md.membernumber, md.Email, md.address, md.city, md.state, md.zip, md.phone, md.Fax, 
		e.dateEnrolled,  
		(SELECT COUNT(*) FROM dbo.tblLogSWOD AS log1 WHERE enrollmentID = e.enrollmentID AND seminarID = e.seminarID AND contact LIKE '%@%') AS EmailCount,
		(SELECT COUNT(*) FROM dbo.tblEnrollmentsAndCredit WHERE enrollmentID = e.enrollmentID) AS CreditCount,
		CASE 
		WHEN e.passed = 1 and len(e.datecompleted) > 0 THEN 1
		WHEN e.passed = 0 and len(e.datecompleted) > 0 THEN 2
		ELSE 3
		END as Progress,
		CASE
		WHEN len(e.dateCompleted) > 0 THEN 1
		ELSE 0
		END as showCertButton, eswod.calcTimeSpent
	FROM dbo.tblEnrollments AS e 
	INNER JOIN dbo.tblEnrollmentsSWOD AS eswod ON e.enrollmentID = eswod.enrollmentID 
	INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
	INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
	CROSS APPLY dbo.fn_getEnrolleeMemberData(e.enrollmentID,@orgcodeForData) as md
	WHERE e.seminarID = @seminarID
	AND e.isActive = 1
	ORDER BY e.dateEnrolled	

)
GO

ALTER FUNCTION [dbo].[swcp_getSeminarStatusString] (
	@programid int, 
	@userid int		-- optional
)
RETURNS @tblsemstatusstring TABLE (
	userid int, 
	semstatusstring varchar(max)
)
AS
BEGIN
	
	declare @tblsemstatus table (autoid int IDENTITY(1,1), userid int, semstatus int)
	
	IF @userid is null
	BEGIN
		DECLARE @tmpitems TABLE (autoID int IDENTITY(1,1), contentid int, contenttype varchar(4), seminarcreditid int, itemorder int)
		INSERT INTO @tmpitems (contentid, contenttype, seminarcreditid, itemorder)		
		select top 100 percent contentid, contenttype, seminarcreditid, itemorder 
		from dbo.swcp_getItemsByProgramID(@programID)
		where contentPublished = 1
		order by itemOrder

		INSERT INTO @tblsemstatus (userid, semstatus)
		SELECT tmp.userid, CASE
			WHEN e.dateCompleted is not null and e.passed = 0 then 3 -- red
			WHEN e.dateCompleted is not null and e.passed = 1 and ti.seminarcreditID is not null and (eac.earnedCertificate = 0 or eac.earnedCertificate is null) then 3 -- red
			WHEN e.dateCompleted is not null and e.passed = 1 and ti.seminarcreditID is null then 2 -- green
			WHEN e.dateCompleted is not null and e.passed = 1 and ti.seminarcreditID is not null and eac.earnedCertificate = 1 then 2 -- green
			ELSE 1 -- empty
			END as semstatus
		from (
			SELECT e.userid
			FROM @tmpitems as tmp
			CROSS APPLY dbo.fn_swod_getEnrollments(tmp.contentID,null) as e
			WHERE tmp.contentType = 'SWOD'
			union
			SELECT e.userid
			FROM @tmpitems as tmp
			CROSS APPLY dbo.fn_swl_getEnrollments(tmp.contentID,null) as e
			WHERE tmp.contentType = 'SWL'
		) as tmp
		cross join @tmpitems as ti
		left outer join dbo.tblEnrollments as e on e.seminarid = ti.contentid and e.userid = tmp.userid and e.isactive = 1
		left outer join dbo.tblEnrollmentsAndCredit as eac on eac.seminarCreditID = ti.seminarCreditID and eac.enrollmentID = e.enrollmentid
		order by tmp.userid, ti.itemOrder
	END	
	ELSE
		INSERT INTO @tblsemstatus (userid, semstatus)
		SELECT tmp.userid, CASE
			WHEN e.dateCompleted is not null and e.passed = 0 then 3 -- red
			WHEN e.dateCompleted is not null and e.passed = 1 and ti.seminarcreditID is not null and (eac.earnedCertificate = 0 or eac.earnedCertificate is null) then 3 -- red
			WHEN e.dateCompleted is not null and e.passed = 1 and ti.seminarcreditID is null then 2 -- green
			WHEN e.dateCompleted is not null and e.passed = 1 and ti.seminarcreditID is not null and eac.earnedCertificate = 1 then 2 -- green
			ELSE 1 -- empty
			END as semstatus
		from (SELECT @userid as userid) as tmp
		cross join (
			select top 100 percent contentid, seminarcreditid, itemorder 
			from dbo.swcp_getItemsByProgramID(@programID)
			where contentPublished = 1
			order by itemOrder
		) as ti
		left outer join dbo.tblEnrollments as e on e.seminarid = ti.contentid and e.userid = tmp.userid and e.isactive = 1
		left outer join dbo.tblEnrollmentsAndCredit as eac on eac.seminarCreditID = ti.seminarCreditID and eac.enrollmentID = e.enrollmentid
		order by ti.itemOrder

	insert into @tblsemstatusstring (userid, semstatusstring)
	select distinct t2.userid, REPLACE((
		SELECT semStatus as [data()]
		FROM @tblsemstatus
		WHERE userid = t2.userid
		ORDER by autoid
		FOR XML PATH ('')
		), ' ', ',') as semStatusString
	from @tblsemstatus as t2

	RETURN
END
GO

ALTER PROC [dbo].[swcp_getEnrollments]
@programID int

AS

SET NOCOUNT ON

-- get items in program
declare @tblItems TABLE (contentid int, contentType varchar(4))
insert into @tblItems (contentid, contentType)
select contentid, contentType
from dbo.swcp_getItemsByProgramID(@programID)
where contentPublished = 1

-- return enrollments
select d.depomemberdataid, u.userid, d.lastname, d.firstname, sss.semStatusString
from (
	SELECT e.userid
	FROM @tblItems as tmp
	CROSS APPLY dbo.fn_swod_getEnrollments(tmp.contentID,null) as e
	where contentType = 'SWOD'
	union
	SELECT e.userid
	FROM @tblItems as tmp
	CROSS APPLY dbo.fn_swl_getEnrollments(tmp.contentID,null) as e
	where contentType = 'SWL'
) as tmpu
inner join dbo.tblUsers as u on u.userid = tmpu.userid
inner join trialsmith.dbo.depomemberdata as d on d.depomemberdataid = u.depomemberdataid
inner join dbo.swcp_getSeminarStatusString(@programid,null) as sss on sss.userid = u.userid
order by d.lastname, firstname
GO

ALTER PROC [dbo].[swcp_getEnrollmentsForSWAdmin]
@programID int

AS

SET NOCOUNT ON

-- get items in program
declare @tblItems TABLE (contentid int, contentType varchar(4))
insert into @tblItems (contentid, contentType)
select contentid, contentType
from dbo.swcp_getItemsByProgramID(@programID)
where contentPublished = 1

-- return enrollments
select d.depomemberdataid, u.userid, d.lastname, d.firstname--, sss.semStatusString
from (
	SELECT e.userid
	FROM @tblItems as tmp
	CROSS APPLY dbo.fn_swod_getEnrollments(tmp.contentID,null) as e
	where contentType = 'SWOD'
	union
	SELECT e.userid
	FROM @tblItems as tmp
	CROSS APPLY dbo.fn_swl_getEnrollments(tmp.contentID,null) as e
	where contentType = 'SWL'
) as tmpu
inner join dbo.tblUsers as u on u.userid = tmpu.userid
inner join trialsmith.dbo.depomemberdata as d on d.depomemberdataid = u.depomemberdataid
where (d.adminflag2 is null or d.adminflag2 <> 'Y')
order by d.lastname, firstname
GO

ALTER PROC [dbo].[swcp_getProgramsForSWAdmin]
@orgcode varchar(5),
@startRow int,
@maxrows int

AS

-- create temp table to store all programs in catalog
DECLARE @tmpTable TABLE (
	autoID int IDENTITY(1,1),
	programID int,
	programName varchar(250),
	enrolledCount int,
	totalCount int
)

INSERT INTO @tmpTable (programID, programName)
EXEC sw_getCertProgramsByOrgCode @orgcode

-- update total count
UPDATE @tmpTable SET totalCount = @@ROWCOUNT

-- used to store program items and enrollments
DECLARE @tmpItems TABLE (itemID int, itemOrder int, seminarCreditID int, contentID int, contentName varchar(200), contentDesc varchar(max), contentPublished bit, contentType varchar(6), sponsorName varchar(200), authCode varchar(40))
DECLARE @tmpEnroll TABLE (depomemberdataid int)

-- loop over programs to get enrolledcount
DECLARE @minprogramID int
SELECT @minprogramID = min(programID) from @tmpTable
WHILE @minprogramID is not null
BEGIN
	-- get items in program
	INSERT INTO @tmpItems (itemID, itemOrder, seminarCreditID, contentID, contentName, contentDesc, contentPublished, contentType, sponsorName, authCode)
	EXEC dbo.sw_getCertProgramItemsByProgramID @minprogramID

	-- get people enrolled in those items
	INSERT INTO @tmpEnroll (depomemberdataid)
	SELECT distinct e.depomemberdataid
	FROM @tmpItems as tmp
	CROSS APPLY dbo.fn_swod_getEnrollments(tmp.contentID,null) as e
	WHERE tmp.contentType = 'SWOD'
	union
	SELECT distinct e.depomemberdataid
	FROM @tmpItems as tmp
	CROSS APPLY dbo.fn_swl_getEnrollments(tmp.contentID,null) as e
	WHERE tmp.contentType = 'SWL'

	-- update count
	UPDATE @tmpTable SET enrolledCount = @@ROWCOUNT where programID = @minprogramID

	-- clear tmp tables
	DELETE FROM @tmpEnroll
	DELETE FROM @tmpItems

	SELECT @minprogramID = min(programID) from @tmpTable where programID > @minprogramID
END

SELECT TOP (@maxrows) * 
FROM @tmpTable
WHERE autoID >= @startRow
ORDER BY autoID
GO

ALTER PROC [dbo].[swcp_getProgramsForSWAdminBySearch]
@orgcode varchar(5),
@cpn varchar(200),
@startRow int,
@maxrows int

AS

-- create temp table to store all programs in catalog
DECLARE @tmpTable TABLE (
	autoID int IDENTITY(1,1),
	programID int,
	programName varchar(250),
	enrolledCount int,
	totalCount int
)

INSERT INTO @tmpTable (programID, programName)
EXEC sw_getCertProgramsByOrgCode @orgcode

-- update total count
UPDATE @tmpTable SET totalCount = @@ROWCOUNT

-- remove names that dont match
DELETE FROM @tmpTable 
where programname not like '%' + @cpn + '%'

-- used to store program items and enrollments
DECLARE @tmpItems TABLE (itemID int, itemOrder int, seminarCreditID int, contentID int, contentName varchar(200), contentDesc varchar(max), contentPublished bit, contentType varchar(6), sponsorName varchar(200), authCode varchar(40))
DECLARE @tmpEnroll TABLE (depomemberdataid int)

-- loop over programs to get enrolledcount
DECLARE @minprogramID int
SELECT @minprogramID = min(programID) from @tmpTable
WHILE @minprogramID is not null
BEGIN
	-- get items in program
	INSERT INTO @tmpItems (itemID, itemOrder, seminarCreditID, contentID, contentName, contentPublished, contentType, sponsorName, authCode)
	EXEC dbo.sw_getCertProgramItemsByProgramID @minprogramID

	-- get people enrolled in those items
	INSERT INTO @tmpEnroll (depomemberdataid)
	SELECT distinct e.depomemberdataid
	FROM @tmpItems as tmp
	CROSS APPLY dbo.fn_swod_getEnrollments(tmp.contentID,null) as e
	WHERE tmp.contentType = 'SWOD'
	union
	SELECT distinct e.depomemberdataid
	FROM @tmpItems as tmp
	CROSS APPLY dbo.fn_swl_getEnrollments(tmp.contentID,null) as e
	WHERE tmp.contentType = 'SWL'

	-- update count
	UPDATE @tmpTable SET enrolledCount = @@ROWCOUNT where programID = @minprogramID

	-- clear tmp tables
	DELETE FROM @tmpEnroll
	DELETE FROM @tmpItems

	SELECT @minprogramID = min(programID) from @tmpTable where programID > @minprogramID
END

SELECT TOP (@maxrows) * 
FROM @tmpTable
WHERE autoID >= @startRow
ORDER BY autoID
GO

ALTER PROCEDURE [dbo].[swod_getEnrollments]
@seminarID int

AS

SELECT *
FROM dbo.fn_swod_getEnrollments(@seminarID,null)
ORDER BY dateEnrolled

GO

ALTER PROC [dbo].[swod_getEnrollmentsForSWAdmin]
@seminarID int,
@orgcode varchar(5)

AS

declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@orgcode)

SELECT e.userid, e.enrollmentID, e.orgcode, e.depomemberdataID, 
	e.FirstName, e.LastName, e.Company, e.membernumber, e.Email, e.address, e.city, e.state, e.zip, e.phone, e.Fax, 
	e.dateEnrolled, e.EmailCount, e.CreditCount, e.Progress, e.showCertButton, e.calcTimeSpent
FROM dbo.fn_swod_getEnrollments(@seminarID,@orgcode) as e
INNER JOIN dbo.tblEnrollments as e2 on e2.enrollmentID = e.enrollmentID
INNER JOIN dbo.tblSeminars as s on s.seminarid = e2.seminarID
INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = e.depoMemberDataID 
WHERE (e2.participantID = @participantID OR s.participantID = @participantID)
AND (d.adminflag2 is null or d.adminflag2 <> 'Y')
ORDER BY e.dateEnrolled

GO

ALTER PROC [dbo].[swod_exportAllRegistrants]
@seminarID int,
@pathToTempFolder varchar(max)

AS

SET NOCOUNT ON

-- drop if exists
IF OBJECT_ID('tempdb..##tmpRegistrants') IS NOT NULL 
	DROP TABLE ##tmpRegistrants

-- put data into temp table 
SELECT e.depomemberdataid, 
	dbo.fn_csvSafeString(e.firstname) as firstname, 
	dbo.fn_csvSafeString(e.lastname) as lastname, 
	dbo.fn_csvSafeString(e.membernumber) as membernumber, 
	dbo.fn_csvSafeString(e.company) as company, 
	dbo.fn_csvSafeString(e.address) as [address], 
	dbo.fn_csvSafeString(e.city) as city,
	dbo.fn_csvSafeString(e.state) as [state],
	dbo.fn_csvSafeString(e.zip) as zip,
	dbo.fn_csvSafeString(e.phone) as phone,
	dbo.fn_csvSafeString(e.fax) as fax,
	dbo.fn_csvSafeString(e.email) as email,
	e.dateEnrolled,
	e.calcTimeSpent, 
	dbo.fn_csvSafeString(e.orgcode) as signedUpOn
INTO ##tmpRegistrants
FROM dbo.fn_swod_getEnrollments(@seminarID,null) as e
ORDER BY e.dateEnrolled

-- export data
DECLARE @csv varchar(400), @cmd varchar(6000)
SELECT @csv = @pathToTempFolder + '\SWODRegExport.csv'
set @cmd = 'bcp ##tmpRegistrants out ' + @csv + ' -c -t, -T -S' + CAST(serverproperty('servername') as varchar(20))
exec master..xp_cmdshell @cmd	

-- get fields returned
EXEC tempdb.dbo.SP_COLUMNS ##tmpRegistrants

-- drop temp table
DROP TABLE ##tmpRegistrants

SET NOCOUNT OFF
GO

ALTER PROC [dbo].[swod_exportAllRegistrantsSWAdmin]
@seminarID varchar(max),
@pathToTempFolder varchar(max),
@limitToOrgCode varchar(5)

AS

SET NOCOUNT ON

-- drop if exists
IF OBJECT_ID('tempdb..##tmpRegistrants') IS NOT NULL 
	DROP TABLE ##tmpRegistrants

declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@limittoorgcode)

SELECT distinct 
	dbo.fn_csvSafeString(sem.seminarName) as SeminarName,
	dbo.fn_csvSafeString(e2.firstname) as firstname, 
	dbo.fn_csvSafeString(e2.lastname) as lastname, 
	dbo.fn_csvSafeString(e2.membernumber) as membernumber, 
	dbo.fn_csvSafeString(e2.company) as company, 
	dbo.fn_csvSafeString(e2.address) as [address], 
	dbo.fn_csvSafeString(e2.city) as city,
	dbo.fn_csvSafeString(e2.state) as [state],
	dbo.fn_csvSafeString(e2.zip) as zip,
	dbo.fn_csvSafeString(e2.phone) as phone,
	dbo.fn_csvSafeString(e2.fax) as fax,
	dbo.fn_csvSafeString(e2.email) as email,
	e2.dateEnrolled, e.dateCompleted, 
	dbo.fn_csvSafeString(e2.orgcode) as signedUpOn
INTO ##tmpRegistrants
FROM dbo.fn_IntListToTable(@seminarID) as list
INNER JOIN dbo.tblSeminars as sem on list.intValue = sem.seminarID
INNER JOIN dbo.tblSeminarsSWOD AS sswod ON sem.seminarid = sswod.seminarID
CROSS APPLY dbo.fn_swod_getEnrollments(sem.seminarID,@limitToOrgCode) as e2
INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = e2.depoMemberDataID 
INNER JOIN dbo.tblEnrollments AS e on e.enrollmentID = e2.enrollmentID
WHERE (e.participantID = @participantID OR sem.participantID = @participantID)
AND (d.adminflag2 is null or d.adminflag2 <> 'Y')
ORDER BY 1, 13

-- export data
DECLARE @csv varchar(400), @cmd varchar(6000)
SELECT @csv = @pathToTempFolder + '\SWODRegExport.csv'
set @cmd = 'bcp ##tmpRegistrants out ' + @csv + ' -c -t, -T -S' + CAST(serverproperty('servername') as varchar(20))
exec master..xp_cmdshell @cmd	

-- get fields returned
EXEC tempdb.dbo.SP_COLUMNS ##tmpRegistrants

-- drop temp table
DROP TABLE ##tmpRegistrants

SET NOCOUNT OFF
GO

ALTER PROC [dbo].[swod_exportIncompleteRegistrantsSWAdmin]
@pathToTempFolder varchar(max),
@orgCode varchar(5)

AS

SET NOCOUNT ON

-- drop if exists
IF OBJECT_ID('tempdb..##tmpRegistrants') IS NOT NULL 
	DROP TABLE ##tmpRegistrants

SELECT TOP 100 PERCENT 
	dbo.fn_csvSafeString(s.seminarName) as Seminar,
	dbo.fn_csvSafeString(md.firstname) as firstname, 
	dbo.fn_csvSafeString(md.lastname) as lastname, 
	dbo.fn_csvSafeString(md.company) as company, 
	dbo.fn_csvSafeString(md.address) as address, 
	dbo.fn_csvSafeString(md.city) as city,
	dbo.fn_csvSafeString(md.state) as state,
	dbo.fn_csvSafeString(md.zip) as zip,
	dbo.fn_csvSafeString(md.phone) as phone,
	dbo.fn_csvSafeString(md.fax) as fax,
	dbo.fn_csvSafeString(md.email) as email,
	convert(varchar(10),e.dateEnrolled,101) as [Date Enrolled],
	CASE WHEN e.dateCompleted IS NOT NULL THEN convert(varchar(10),e.dateCompleted,101) ELSE 'In Progress' END AS [Date Completed]
INTO ##tmpRegistrants
FROM dbo.tblEnrollments AS e 
INNER JOIN dbo.tblSeminars as s on s.seminarID = e.seminarID
INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
INNER JOIN dbo.tblEnrollmentsSWOD AS eswod ON e.enrollmentID = eswod.enrollmentID 
INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
CROSS APPLY dbo.fn_getEnrolleeMemberData(e.enrollmentID,null) as md
WHERE e.isActive = 1
AND (d.adminflag2 is null or d.adminflag2 <> 'Y')
AND e.dateCompleted IS NULL
AND p.orgcode = @orgcode
ORDER BY e.dateEnrolled desc, md.lastName, md.firstName, s.seminarName

-- export data
DECLARE @csv varchar(400), @cmd varchar(6000)
SELECT @csv = @pathToTempFolder + '\SWODReg' + @orgCode + '.csv'
set @cmd = 'bcp ##tmpRegistrants out ' + @csv + ' -c -t, -T -S' + CAST(serverproperty('servername') as varchar(20))
exec master..xp_cmdshell @cmd	

-- get fields returned
EXEC tempdb.dbo.SP_COLUMNS ##tmpRegistrants

-- drop temp table
DROP TABLE ##tmpRegistrants

SET NOCOUNT OFF
GO

ALTER Function [dbo].[fn_swtl_getEnrollments] (
	@titleID int,
	@orgcodeForData varchar(10)
) 
returns table
AS
RETURN (

	SELECT TOP 100 PERCENT e.enrollmentID, p.orgcode, u.depomemberdataID, 
		md.FirstName, md.LastName, md.Company, md.membernumber, md.Email, md.address, md.city, md.state, md.zip, md.phone, md.Fax, 
		e.dateEnrolled, 
		(SELECT COUNT(*) FROM dbo.tblLogSWTL AS log1 WHERE enrollmentID = e.enrollmentID AND titleID = e.titleID AND contact LIKE '%@%') AS EmailCount
	FROM dbo.tblEnrollments AS e 
	INNER JOIN dbo.tblEnrollmentsSWTL AS eswtl ON e.enrollmentID = eswtl.enrollmentID 
	INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
	INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
	CROSS APPLY dbo.fn_getEnrolleeMemberData(e.enrollmentID,@orgcodeForData) as md
	WHERE e.titleID = @titleID
	AND e.isActive = 1
	ORDER BY e.dateEnrolled	

)
GO

ALTER PROCEDURE [dbo].[swtl_getEnrollments]
@titleID int

AS

SELECT *
FROM dbo.fn_swtl_getEnrollments(@titleID,null)
ORDER BY dateEnrolled

GO

ALTER PROC [dbo].[swtl_getEnrollmentsForSWAdmin]
@titleID int,
@orgcode varchar(5)

AS

declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@orgcode)

SELECT e2.userid, e.enrollmentID, e.orgcode, p.catalogURL, e.depomemberdataID, 
	e.FirstName, e.LastName, e.Company, e.membernumber, e.Email, e.address, e.city, e.state, e.zip, e.phone, e.Fax, 
	e.dateEnrolled, e.EmailCount
FROM dbo.fn_swtl_getEnrollments(@titleID,@orgcode) as e
INNER JOIN dbo.tblEnrollments as e2 on e2.enrollmentID = e.enrollmentID
INNER JOIN dbo.tblParticipants AS p ON e2.participantID = p.participantID 
INNER JOIN dbo.tblTitles as t on t.titleid = e2.titleID
INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = e.depoMemberDataID 
WHERE (e2.participantID = @participantID OR t.participantID = @participantID)
AND (d.adminflag2 is null or d.adminflag2 <> 'Y')
ORDER BY e.dateEnrolled

GO

ALTER PROC [dbo].[swtl_exportAllRegistrants]
@titleID int,
@pathToTempFolder varchar(max)

AS

SET NOCOUNT ON

-- drop if exists
IF OBJECT_ID('tempdb..##tmpRegistrants') IS NOT NULL 
	DROP TABLE ##tmpRegistrants

SELECT e.depomemberdataid, 
	dbo.fn_csvSafeString(e.firstname) as firstname, 
	dbo.fn_csvSafeString(e.lastname) as lastname, 
	dbo.fn_csvSafeString(e.membernumber) as membernumber, 
	dbo.fn_csvSafeString(e.company) as company, 
	dbo.fn_csvSafeString(e.address) as [address], 
	dbo.fn_csvSafeString(e.city) as city,
	dbo.fn_csvSafeString(e.state) as [state],
	dbo.fn_csvSafeString(e.zip) as zip,
	dbo.fn_csvSafeString(e.phone) as phone,
	dbo.fn_csvSafeString(e.fax) as fax,
	dbo.fn_csvSafeString(e.email) as email,
	e.dateEnrolled
INTO ##tmpRegistrants
FROM dbo.fn_swtl_getEnrollments(@titleID,null) as e
ORDER BY e.dateEnrolled

-- export data
DECLARE @csv varchar(400), @cmd varchar(6000)
SELECT @csv = @pathToTempFolder + '\SWTLRegExport.csv'
set @cmd = 'bcp ##tmpRegistrants out ' + @csv + ' -c -t, -T -S' + CAST(serverproperty('servername') as varchar(20))
exec master..xp_cmdshell @cmd	

-- get fields returned
EXEC tempdb.dbo.SP_COLUMNS ##tmpRegistrants

-- drop temp table
DROP TABLE ##tmpRegistrants

SET NOCOUNT OFF

GO

ALTER PROC [dbo].[swtl_exportAllRegistrantsSWAdmin]
@titleID varchar(max),
@pathToTempFolder varchar(max),
@limitToOrgCode varchar(5)

AS

SET NOCOUNT ON

-- drop if exists
IF OBJECT_ID('tempdb..##tmpRegistrants') IS NOT NULL 
	DROP TABLE ##tmpRegistrants

declare @participantID int
select @participantID = dbo.fn_getParticipantIDFromOrgcode(@limittoorgcode)

SELECT distinct 
	dbo.fn_csvSafeString(t.titleName) as ProgramName,
	dbo.fn_csvSafeString(e2.firstname) as firstname, 
	dbo.fn_csvSafeString(e2.lastname) as lastname, 
	dbo.fn_csvSafeString(e2.membernumber) as membernumber, 
	dbo.fn_csvSafeString(e2.company) as company, 
	dbo.fn_csvSafeString(e2.address) as [address], 
	dbo.fn_csvSafeString(e2.city) as city,
	dbo.fn_csvSafeString(e2.state) as [state],
	dbo.fn_csvSafeString(e2.zip) as zip,
	dbo.fn_csvSafeString(e2.phone) as phone,
	dbo.fn_csvSafeString(e2.fax) as fax,
	dbo.fn_csvSafeString(e2.email) as email,
	e2.dateEnrolled
INTO ##tmpRegistrants
FROM dbo.fn_IntListToTable(@titleID) as list
INNER JOIN dbo.tblTitles as t on list.intValue = t.titleid
CROSS APPLY dbo.fn_swtl_getEnrollments(t.titleid,@limitToOrgCode) as e2
INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = e2.depoMemberDataID 
INNER JOIN dbo.tblEnrollments AS e on e.enrollmentID = e2.enrollmentID
WHERE (e.participantID = @participantID OR t.participantID = @participantID)
AND (d.adminflag2 is null or d.adminflag2 <> 'Y')
ORDER BY 1, 13

-- export data
DECLARE @csv varchar(400), @cmd varchar(6000)
SELECT @csv = @pathToTempFolder + '\SWTLRegExport.csv'
set @cmd = 'bcp ##tmpRegistrants out ' + @csv + ' -c -t, -T -S' + CAST(serverproperty('servername') as varchar(20))
exec master..xp_cmdshell @cmd	

-- get fields returned
EXEC tempdb.dbo.SP_COLUMNS ##tmpRegistrants

-- drop temp table
DROP TABLE ##tmpRegistrants

SET NOCOUNT OFF

GO

ALTER PROCEDURE [dbo].[swtl_getEnrollmentByEnrollmentID]
@enrollmentID int

AS

SELECT top 1 e.enrollmentID, u.depomemberdataid, md.FirstName, md.LastName, md.Email, 
	t.titleName, p.catalogURL, p.orgcode, t.titleID
FROM dbo.tblEnrollments AS e 
INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
INNER JOIN dbo.tblTitles AS t ON t.titleID = e.titleID 
INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
CROSS APPLY dbo.fn_getEnrolleeMemberData(e.enrollmentID,null) as md
WHERE e.enrollmentID = @enrollmentID
AND e.isActive = 1
AND t.isDeleted = 0
ORDER BY e.dateEnrolled DESC

GO

