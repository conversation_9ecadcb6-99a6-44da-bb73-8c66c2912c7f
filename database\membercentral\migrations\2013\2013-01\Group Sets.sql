use membercentral
GO
CREATE TABLE dbo.ams_memberGroupSets
	(
	groupSetID int NOT NULL IDENTITY (1, 1),
	orgID int NOT NULL,
	groupSetName varchar(50) NOT NULL,
	uid uniqueidentifier NOT NULL,
	dateCreated datetime NOT NULL
	)  ON [PRIMARY]
GO
ALTER TABLE dbo.ams_memberGroupSets ADD CONSTRAINT
	DF_ams_memberGroupSets_uid DEFAULT newID() FOR uid
GO
ALTER TABLE dbo.ams_memberGroupSets ADD CONSTRAINT
	DF_ams_memberGroupSets_dateCreated DEFAULT getdate() FOR dateCreated
GO
ALTER TABLE dbo.ams_memberGroupSets ADD CONSTRAINT
	PK_ams_memberGroupSets PRIMARY KEY CLUSTERED 
	(
	groupSetID
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

GO
ALTER TABLE dbo.ams_memberGroupSets ADD CONSTRAINT
	FK_ams_memberGroupSets_organizations FOREIGN KEY
	(
	orgID
	) REFERENCES dbo.organizations
	(
	orgID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
CREATE TABLE [dbo].[ams_memberGroupSetGroups](
	[groupSetGroupID] [int] IDENTITY(1,1) NOT NULL,
	[groupSetID] [int] NOT NULL,
	[groupID] [int] NOT NULL,
	[labelOverride] [varchar](100) NULL,
 CONSTRAINT [PK_ams_memberGroupSetGroups] PRIMARY KEY CLUSTERED 
(
	[groupSetGroupID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE [dbo].[ams_memberGroupSetGroups]  WITH CHECK ADD  CONSTRAINT [FK_ams_memberGroupSetGroups_ams_groups] FOREIGN KEY([groupID])
REFERENCES [dbo].[ams_groups] ([groupID])
GO
ALTER TABLE [dbo].[ams_memberGroupSetGroups] CHECK CONSTRAINT [FK_ams_memberGroupSetGroups_ams_groups]
GO
ALTER TABLE [dbo].[ams_memberGroupSetGroups]  WITH CHECK ADD  CONSTRAINT [FK_ams_memberGroupSetGroups_ams_memberGroupSets] FOREIGN KEY([groupSetID])
REFERENCES [dbo].[ams_memberGroupSets] ([groupSetID])
GO
ALTER TABLE [dbo].[ams_memberGroupSetGroups] CHECK CONSTRAINT [FK_ams_memberGroupSetGroups_ams_memberGroupSets]
GO

CREATE PROC [dbo].[ams_createMemberGroupSet]
@orgID int,
@groupsetName varchar(200),
@groupsetID int OUTPUT

AS

SELECT @groupsetID = null

INSERT INTO dbo.ams_memberGroupSets (orgID, groupsetName, [uid], datecreated)
VALUES (@orgID, @groupsetName, newid(), getdate())
	IF @@ERROR <> 0 GOTO on_error
	SELECT @groupsetID = SCOPE_IDENTITY()

RETURN 0

-- error exit
on_error:
	SELECT @groupsetID = 0
	RETURN -1
GO

CREATE PROC [dbo].[ams_createMemberGroupSetGroup]
@groupsetID int,
@groupID int,
@labelOverride varchar(200),
@groupSetGroupID int OUTPUT

AS

SELECT @groupSetGroupID = null

INSERT INTO dbo.ams_memberGroupSetsGroups (groupsetID, groupID, labelOverride)
VALUES (@groupsetID, @groupID, @labelOverride)
	IF @@ERROR <> 0 GOTO on_error
	SELECT @groupSetGroupID = SCOPE_IDENTITY()

RETURN 0

-- error exit
on_error:
	SELECT @groupSetGroupID = 0
	RETURN -1
GO

ALTER TABLE dbo.ams_memberDirectoryClassifications ADD
	groupSetID int NULL
GO
ALTER TABLE dbo.ams_memberDirectoryClassifications ADD CONSTRAINT
	FK_ams_memberDirectoryClassifications_ams_memberGroupSets FOREIGN KEY
	(
	groupSetID
	) REFERENCES dbo.ams_memberGroupSets
	(
	groupSetID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.ams_memberGroupSets
	DROP CONSTRAINT FK_ams_memberGroupSets_organizations
GO
ALTER TABLE dbo.ams_memberGroupSets
	DROP CONSTRAINT DF_ams_memberGroupSets_uid
GO
ALTER TABLE dbo.ams_memberGroupSets
	DROP CONSTRAINT DF_ams_memberGroupSets_dateCreated
GO
CREATE TABLE dbo.Tmp_ams_memberGroupSets
	(
	groupSetID int NOT NULL IDENTITY (1, 1),
	orgID int NOT NULL,
	groupSetName varchar(200) NOT NULL,
	uid uniqueidentifier NOT NULL,
	dateCreated datetime NOT NULL
	)  ON [PRIMARY]
GO
ALTER TABLE dbo.Tmp_ams_memberGroupSets ADD CONSTRAINT
	DF_ams_memberGroupSets_uid DEFAULT (newid()) FOR uid
GO
ALTER TABLE dbo.Tmp_ams_memberGroupSets ADD CONSTRAINT
	DF_ams_memberGroupSets_dateCreated DEFAULT (getdate()) FOR dateCreated
GO
SET IDENTITY_INSERT dbo.Tmp_ams_memberGroupSets ON
GO
IF EXISTS(SELECT * FROM dbo.ams_memberGroupSets)
	 EXEC('INSERT INTO dbo.Tmp_ams_memberGroupSets (groupSetID, orgID, groupSetName, uid, dateCreated)
		SELECT groupSetID, orgID, groupSetName, uid, dateCreated FROM dbo.ams_memberGroupSets WITH (HOLDLOCK TABLOCKX)')
GO
SET IDENTITY_INSERT dbo.Tmp_ams_memberGroupSets OFF
GO
ALTER TABLE dbo.ams_memberGroupSetGroups
	DROP CONSTRAINT FK_ams_memberGroupSetGroups_ams_memberGroupSets
GO
ALTER TABLE dbo.ams_memberDirectoryClassifications
	DROP CONSTRAINT FK_ams_memberDirectoryClassifications_ams_memberGroupSets
GO
DROP TABLE dbo.ams_memberGroupSets
GO
EXECUTE sp_rename N'dbo.Tmp_ams_memberGroupSets', N'ams_memberGroupSets', 'OBJECT' 
GO
ALTER TABLE dbo.ams_memberGroupSets ADD CONSTRAINT
	PK_ams_memberGroupSets PRIMARY KEY CLUSTERED 
	(
	groupSetID
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

GO
ALTER TABLE dbo.ams_memberGroupSets ADD CONSTRAINT
	FK_ams_memberGroupSets_organizations FOREIGN KEY
	(
	orgID
	) REFERENCES dbo.organizations
	(
	orgID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.ams_memberDirectoryClassifications ADD CONSTRAINT
	FK_ams_memberDirectoryClassifications_ams_memberGroupSets FOREIGN KEY
	(
	groupSetID
	) REFERENCES dbo.ams_memberGroupSets
	(
	groupSetID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.ams_memberGroupSetGroups ADD CONSTRAINT
	FK_ams_memberGroupSetGroups_ams_memberGroupSets FOREIGN KEY
	(
	groupSetID
	) REFERENCES dbo.ams_memberGroupSets
	(
	groupSetID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO

insert into dbo.ams_memberGroupSets (orgID, groupSetName)
select s.orgID, cast(mdc.classificationID as varchar(5))
from dbo.ams_memberDirectoryClassifications mdc
inner join dbo.ams_memberDirectories md on md.memberDirectoryID = mdc.memberDirectoryID
inner join cms_applicationInstances ai on ai.applicationInstanceID = md.applicationInstanceID
inner join sites s on s.siteID = ai.siteID 
inner join cms_siteResources sr on sr.siteResourceID = ai.siteResourceID
inner join cms_siteResources pageResource on pageResource.siteResourceID = sr.parentSiteResourceID
inner join cms_pages p on p.siteResourceID = pageResource.siteResourceID
inner join cms_pageLanguages pl on pl.pageID = p.pageID
left outer join cms_siteResources commResource
	inner join cms_applicationInstances commApp on commResource.siteResourceID = commApp.siteResourceID
	on commResource.siteResourceID = pageResource.parentSiteResourceID

insert into dbo.ams_memberGroupSetGroups (groupSetID, groupID, labelOverride)
select mgs.groupSetID, mdcl.groupID, mdcl.label
from ams_memberGroupSets mgs
inner join dbo.ams_memberDirectoryClassifications mdc on mdc.classificationID = cast(mgs.groupSetName as int)
inner join dbo.ams_memberDirectoryClassificationLinks mdcl on mdcl.classificationID = mdc.classificationID
order by linkid

update mdc 
set mdc.groupSetID = mgs.groupSetID
from ams_memberGroupSets mgs
inner join dbo.ams_memberDirectoryClassifications mdc on mdc.classificationID = cast(mgs.groupSetName as int)

update mgs 
set groupSetName = mdc.name + ' - ' + isnull(commApp.applicationInstanceName, ai.applicationInstanceName)
from ams_memberGroupSets mgs
inner join dbo.ams_memberDirectoryClassifications mdc on mdc.classificationID = cast(mgs.groupSetName as int)
inner join dbo.ams_memberDirectories md on md.memberDirectoryID = mdc.memberDirectoryID
inner join cms_applicationInstances ai on ai.applicationInstanceID = md.applicationInstanceID
inner join sites s on s.siteID = ai.siteID 
inner join cms_siteResources sr on sr.siteResourceID = ai.siteResourceID
inner join cms_siteResources pageResource on pageResource.siteResourceID = sr.parentSiteResourceID
inner join cms_pages p on p.siteResourceID = pageResource.siteResourceID
inner join cms_pageLanguages pl on pl.pageID = p.pageID
left outer join cms_siteResources commResource 
	inner join cms_applicationInstances commApp on commResource.siteResourceID = commApp.siteResourceID
	on commResource.siteResourceID = pageResource.parentSiteResourceID
GO

drop table dbo.ams_memberDirectoryClassificationLinks
GO


-- repurpose admin tooltypeID 63 (EHRBasic)   toolResourceTypeID 147
update dbo.admin_toolTypes
set toolType = 'MemberGroupSets',
	toolCFC = 'MemberGroupSets.MemberGroupSetsAdmin',
	toolDesc = 'Member Group Sets'
where toolTypeID = 63

-- repurpose navigationID 103 
update dbo.admin_navigation
set navName = 'Member Group Sets',
	navDesc = null,
	parentNavigationID = 48,
	navAreaID = 3,
	orderBy = 7,
	cfcMethod = 'list',
	isHeader = 0,
	isActive = 1,
	showInNav = 1,
	helpLink = null
where navigationID = 103

delete from dbo.admin_functionsDeterminingNav
where toolTypeID = 63

declare @resourceTypeFunctionID int
SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('Site'),dbo.fn_getResourceFunctionID('EditSite',dbo.fn_getResourceTypeID('Site')))
EXEC dbo.createAdminFunctionsDeterminingNav
	@resourceTypeFunctionID=@resourceTypeFunctionID,
	@toolTypeID=63,
	@navigationID=103

declare @siteID int
select @siteID = min(siteID) from dbo.sites
while @siteID is not null BEGIN
	EXEC dbo.createAdminSuite @siteid=@siteID
	select @siteID = min(siteID) from dbo.sites where siteID > @siteID
END


DECLARE @tmp TABLE (neworder int IDENTITY(1,1) NOT NULL, navigationID int NOT NULL, fieldOrder int NOT NULL)

INSERT INTO @tmp (navigationID, fieldOrder)
SELECT	navigationID, orderBy
FROM	dbo.admin_navigation
WHERE	parentNavigationID = 48
ORDER BY orderBy, navName

UPDATE	mf
SET		mf.orderBy = t.neworder
FROM	dbo.admin_navigation mf 
INNER JOIN @tmp t on mf.navigationID = t.navigationID
WHERE	mf.parentNavigationID = 48
GO


