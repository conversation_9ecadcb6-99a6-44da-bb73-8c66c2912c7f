use formbuilder
GO

ALTER PROC [dbo].[up_exportEvaluationsIndiv]
	@formID int,
	@enabledOnly bit,
	@seminarID int = 0,
	@keyword varchar(max),
	@orgCode varchar(255),
	@cStartDate datetime,
	@cEndDate datetime,
	@filename varchar(1000)
AS

SET NOCOUNT ON

-- drop if exists
IF OBJECT_ID('tempdb..#tmpQuestions') IS NOT NULL 
      DROP TABLE #tmpQuestions
IF OBJECT_ID('tempdb..#tmpOptions') IS NOT NULL 
      DROP TABLE #tmpOptions
IF OBJECT_ID('tempdb..#tmpOptionsx') IS NOT NULL 
      DROP TABLE #tmpOptionsx
IF OBJECT_ID('tempdb..#tmpQForList') IS NOT NULL 
      DROP TABLE #tmpQForList
IF OBJECT_ID('tempdb..#tmpResponses') IS NOT NULL 
      DROP TABLE #tmpResponses
IF OBJECT_ID('tempdb..#qryRawData') IS NOT NULL 
      DROP TABLE #qryRawData
IF OBJECT_ID('tempdb..##tmpExportEvalQry') IS NOT NULL 
      DROP TABLE ##tmpExportEvalQry

-- get questions table
;WITH CTE 
AS (
      SELECT q.questionID, q.questionTypeID, q.questionText, q.formerQuestionID, q.questionID as useQuestionID
      FROM formbuilder.dbo.tblQuestions as q 
      inner join formbuilder.dbo.tblSections as s on s.sectionID = q.sectionID
      where s.formID = @formID
      and q.isEnabled = 1
            UNION ALL
      SELECT q.questionID, q.questionTypeID, q.questionText, q.formerQuestionID, CTE.useQuestionID
      FROM formBuilder.dbo.tblQuestions as q
      INNER JOIN CTE ON q.questionID = CTE.formerQuestionID
)
select CTE.questionID, CTE.questionTypeID, CTE.questionText, CTE.formerQuestionID, CTE.useQuestionID, replace(q.questionText,',','') as useQuestionText, 
	ROW_NUMBER() OVER (ORDER BY s.sectionOrder, q.questionOrder, CTE.questionID) as questionOrder
into #tmpQuestions
from CTE
inner join formBuilder.dbo.tblQuestions as q on q.questionID = CTE.useQuestionID
inner join formbuilder.dbo.tblSections as s on s.sectionID = q.sectionID

-- get options table
;WITH CTE 
AS (
      SELECT o.optionID, o.optionText, o.formerOptionID, o.optionID as useOptionID
      FROM formBuilder.dbo.tblOptions o
      inner join formbuilder.dbo.tblQuestions as q on q.questionID = o.questionID
      inner join formbuilder.dbo.tblSections as s on s.sectionID = q.sectionID
      where s.formID = @formID
    and o.isEnabled = 1
            UNION ALL
      SELECT o.optionID, o.optionText, o.formerOptionID, CTE.useOptionID
      FROM formBuilder.dbo.tblOptions as o
      INNER JOIN CTE ON o.optionID = CTE.formerOptionID
)
select CTE.optionID, CTE.optionText, CTE.formerOptionID, CTE.useOptionID, replace(o.optionText,',','') as useOptionText, o.questionID, o.optionOrder
into #tmpOptions
from CTE
inner join formBuilder.dbo.tblOptions as o on o.optionID = CTE.useOptionID

-- get optionsX table
;WITH CTE 
AS (
      SELECT o.optionID, o.optionText, o.formerOptionID, o.optionID as useOptionID
      FROM formBuilder.dbo.tblOptionsX o
      inner join formbuilder.dbo.tblQuestions as q on q.questionID = o.questionID
      inner join formbuilder.dbo.tblSections as s on s.sectionID = q.sectionID
      where s.formID = @formID
    and o.isEnabled = 1
            UNION ALL
      SELECT o.optionID, o.optionText, o.formerOptionID, CTE.useOptionID
      FROM formBuilder.dbo.tblOptionsX as o
      INNER JOIN CTE ON o.optionID = CTE.formerOptionID
)
select CTE.optionID, CTE.optionText, CTE.formerOptionID, CTE.useOptionID, o.optionText as useOptionText, o.questionID, o.optionOrder
into #tmpOptionsX
from CTE
inner join formBuilder.dbo.tblOptionsX as o on o.optionID = CTE.useOptionID

-- get pivot list of questions. must be 128 chars or less because these will be column names
select left(case when qt.questionType like '%matrix%' then q.useQuestionText + ' / ' + o.useOptionText else q.useQuestionText end,128) as questionText,
      ROW_NUMBER() OVER(ORDER BY q.questionOrder, o.optionOrder) as row
into #tmpQForList
from #tmpQuestions q
inner join formbuilder.dbo.tblQuestionTypes as qt on qt.questionTypeId = q.questionTypeId
left outer join #tmpOptions o 
      inner join formbuilder.dbo.tblOptions as op on op.optionID = o.optionID
      on o.questionID = q.questionID and o.optionID = o.useOptionID and qt.questionType like '%matrix%'
where q.questionID = q.useQuestionID

declare @qpvtList varchar(max) 
select @qpvtList = COALESCE(@qpvtList + ',', '') + quotename(questionText)
      from #tmpQForList
      order by row

-- get filtered responses
CREATE TABLE #tmpResponses (responseID int) 
DECLARE @respsql varchar(max)
select @respsql = '
	select r.responseID
	FROM formbuilder.dbo.tblResponses as r
	left outer join seminarweb.dbo.tblSeminarsAndFormResponses as safr 
	    inner join seminarweb.dbo.tblSeminarsAndForms as saf on saf.seminarFormID = safr.seminarFormID
	    inner join seminarweb.dbo.tblSeminars s on s.seminarID = saf.seminarID
	    inner join seminarweb.dbo.tblParticipants as p on p.participantID = s.participantID
	    on safr.responseID = r.responseID
	where r.formID = ' + cast(@formID as varchar(10)) + ' 
	and r.isActive = 1
	AND r.dateCompleted is not null '
if @seminarID > 0
      set @respsql = @respsql + ' and s.seminarID = ' + cast(@seminarID as varchar(10)) 
if len(@keyword) > 0
      set @respsql = @respsql + ' and s.seminarName like ''%' + @keyword + '%'''
if len(@orgCode) > 0
      set @respsql = @respsql + ' and p.orgCode = ''' + @orgCode + ''''
if len(@cStartDate) > 0 and len(@cEndDate) > 0
      set @respsql = @respsql + ' and r.dateCompleted between ''' + convert(varchar(10),@cStartDate, 101) + ''' and ''' + convert(varchar(10),dateadd(DD,1,@cEndDate), 101) + ''''
insert into #tmpResponses (responseID)
EXEC(@respsql)

-- get raw response data. 
-- TODO: questions in general need to support multiple answers
select r.responseID, q.useQuestionID as questionID, q.useQuestionText as questionText, 
      o.useOptionID as optionID, rd.responseText, o.useOptionText as optionText
into #qryRawData
FROM #tmpResponses as r
inner join formbuilder.dbo.tblResponseDetails as rd on rd.responseID = r.responseID
inner join #tmpQuestions as q on q.questionID = rd.questionID
inner join formbuilder.dbo.tblQuestionTypes as qt on qt.questionTypeId = q.questionTypeId
left outer join #tmpOptions as o on o.optionID = rd.optionID
where qt.questionType not like '%matrix%'
      union all
select r.responseID, q.useQuestionID as questionID, q.useQuestionText + ' / ' + o.UseOptionText as questionText, 
      ox.useOptionID as optionID, rd.responseText, ox.useOptionText as optionText
FROM #tmpResponses as r
inner join formbuilder.dbo.tblResponseDetails as rd on rd.responseID = r.responseID
inner join #tmpQuestions as q on q.questionID = rd.questionID
inner join formbuilder.dbo.tblQuestionTypes as qt on qt.questionTypeId = q.questionTypeId
inner join #tmpOptions as o on o.optionID = rd.optionID
inner join #tmpOptionsX as ox on ox.optionID = rd.optionXID
where qt.questionType like '%matrix%'

-- pivot for export
declare @formTypeAbbr char(1)
select @formTypeAbbr = ft.formTypeAbbr 
      from formbuilder.dbo.tblFormTypes as ft
      inner join formbuilder.dbo.tblForms as f on f.formTypeID = ft.formTypeID
      where f.formID = @formID

DECLARE @pvtsql varchar(max)
select @pvtsql = '
	select * into ##tmpExportEvalQry from (
		select 
			r.responseID, 
			s.seminarid as [Seminar ID], 
			seminarWeb.dbo.fn_csvSafeString(s.seminarname) as [Seminar Name], 
			seminarWeb.dbo.fn_csvSafeString(ca.authorityName + '' via '' + cs.sponsorName) as [Credit Sponsor], 
            seminarWeb.dbo.fn_csvSafeString(sac.courseApproval) as [Course Approval Num], 
			seminarWeb.dbo.fn_csvSafeString(eac.idnumber) as [ID Number], 
            seminarWeb.dbo.fn_csvSafeString(d.lastname) as [Last Name], 
			seminarWeb.dbo.fn_csvSafeString(d.firstname) as [First Name], 
			seminarWeb.dbo.fn_csvSafeString(d.billingaddress) as [Address Line 1], 
            seminarWeb.dbo.fn_csvSafeString(d.billingaddress2) as [Address Line 2], 
			seminarWeb.dbo.fn_csvSafeString(d.billingaddress3) as [Address Line 3], 
            seminarWeb.dbo.fn_csvSafeString(d.billingCity) as City, 
			seminarWeb.dbo.fn_csvSafeString(d.billingState) as State, 
			seminarWeb.dbo.fn_csvSafeString(d.billingZip) as ZIP, 
			seminarWeb.dbo.fn_csvSafeString(d.phone) as Phone,
			seminarWeb.dbo.fn_csvSafeString(d.Fax) as Fax, 
			seminarWeb.dbo.fn_csvSafeString(d.email) as Email,
			convert(varchar(10),r.datecompleted,101) as [Date Completed],'
IF @formTypeAbbr = 'E'
      select @pvtsql = @pvtsql + 'r.passingPct as [Score], case when r.passingPct >= f.passingPct then ''Pass'' else ''Fail'' end as PassFail, '
select @pvtsql = @pvtsql + @qpvtList + ' 
      from ( 
            select responseID, questionText, seminarWeb.dbo.fn_csvSafeString(isnull(optionText,responseText)) as questionResponse
            from #qryRawData
      ) as at 
      PIVOT (max(questionResponse) for questionText in (' + @qpvtList + ')) as pvt 
      inner join formbuilder.dbo.tblResponses as r on r.responseID = pvt.responseID
      inner join formbuilder.dbo.tblForms as f on f.formID = r.formID
      left outer join trialsmith.dbo.depomemberdata as d on d.depomemberdataid = r.depomemberdataid
      left outer join seminarweb.dbo.tblSeminarsAndFormResponses as safr 
            inner join seminarweb.dbo.tblSeminarsAndForms as saf on saf.seminarFormID = safr.seminarFormID
            inner join seminarweb.dbo.tblSeminars s on s.seminarID = saf.seminarID
            inner join seminarweb.dbo.tblParticipants as p on p.participantID = s.participantID
            inner join seminarweb.dbo.tblenrollments as e on e.enrollmentid = safr.enrollmentid and e.isactive = 1
            left outer join seminarweb.dbo.tblenrollmentsandcredit as eac 
                  inner join seminarweb.dbo.tblSeminarsAndCredit as sac on sac.seminarCreditID = eac.seminarCreditID
                        inner join seminarweb.dbo.tblCreditSponsorsAndAuthorities as csa on csa.CSALinkID = sac.CSALinkID
                        inner join seminarweb.dbo.tblCreditSponsors as cs on cs.sponsorID = csa.sponsorID
                        inner join seminarweb.dbo.tblCreditAuthorities as ca on ca.authorityID = csa.authorityID
                        on eac.enrollmentid = e.enrollmentid
            on safr.responseID = r.responseID
	) as tmp
	order by tmp.responseID '
EXEC(@pvtsql)

-- export data
DECLARE @cmd varchar(6000)
declare @tmpBCP TABLE (theoutput varchar(max))
set @cmd = 'bcp ##tmpExportEvalQry out ' + @filename + ' -c -t, -T -S' + CAST(serverproperty('servername') as varchar(40))
insert into @tmpBCP (theoutput)
exec master..xp_cmdshell @cmd	

-- return count of records
SELECT count(*) AS returnCount
FROM ##tmpExportEvalQry

-- get fields returned
EXEC tempdb.dbo.SP_COLUMNS ##tmpExportEvalQry

-- drop if exists
IF OBJECT_ID('tempdb..#tmpQuestions') IS NOT NULL 
      DROP TABLE #tmpQuestions
IF OBJECT_ID('tempdb..#tmpResponses') IS NOT NULL 
      DROP TABLE #tmpResponses
IF OBJECT_ID('tempdb..#qryRawData') IS NOT NULL 
      DROP TABLE #qryRawData
IF OBJECT_ID('tempdb..#tmpOptions') IS NOT NULL 
      DROP TABLE #tmpOptions
IF OBJECT_ID('tempdb..#tmpOptionsx') IS NOT NULL 
      DROP TABLE #tmpOptionsx
IF OBJECT_ID('tempdb..#tmpQForList') IS NOT NULL 
      DROP TABLE #tmpQForList
IF OBJECT_ID('tempdb..##tmpExportEvalQry') IS NOT NULL 
      DROP TABLE ##tmpExportEvalQry
GO


use trialsmith
GO
ALTER PROCEDURE [dbo].[siteadmin_memberImport_restore_from_file]
	@orgCode varchar(5),
	@OMDRestoreFile varchar(400),
	@OMORestoreFile varchar(400)
AS

SET NOCOUNT ON

DECLARE @qry VARCHAR(8000)

BEGIN TRY
	-- drop temp holding table		
	IF OBJECT_ID('tempdb..##orgMemberDataTemp') IS NOT NULL 
		DROP TABLE ##orgMemberDataTemp

	-- drop temp holding table		
	IF OBJECT_ID('tempdb..##orgMemberOptionsTemp') IS NOT NULL 
		DROP TABLE ##orgMemberOptionsTemp

	-- copy structure of orgmemberdata into temp table
	CREATE TABLE ##orgMemberDataTemp (
		[orgmemberdataid] [int] NOT NULL,
		[orgcode] [varchar](5) NOT NULL,
		[AllowTS] [bit] NULL,
		[orgmembertype] [int] NULL,
		[status] [varchar](50) NULL,
		[prefix] [varchar](50) NULL,
		[firstname] [varchar](100) NULL,
		[middlename] [varchar](100) NULL,
		[lastname] [varchar](100) NULL,
		[suffix] [varchar](50) NULL,
		[professionSuffix] [varchar](50) NULL,
		[firm] [varchar](150) NULL,
		[address1] [varchar](100) NULL,
		[address2] [varchar](100) NULL,
		[address3] [varchar](100) NULL,
		[city] [varchar](100) NULL,
		[state] [varchar](50) NULL,
		[zipcode] [varchar](25) NULL,
		[telephone] [varchar](100) NULL,
		[fax] [varchar](100) NULL,
		[email] [varchar](150) NULL,
		[defaultusername] [varchar](50) NULL,
		[defaultpassword] [varchar](50) NULL,
		[bio] [varchar](3000) NULL,
		[memberNumber] [varchar](100) NULL,
		[barno] [varchar](50) NULL,
		[memberDate] [datetime] NULL,
		[casduid] [int] NULL,
		[Disabled] [bit] NULL,
		[memberCode] [varchar](100) NULL,
		[county] [varchar](100) NULL,
		[website] [varchar](200) NULL,
		[isGuest] [bit] NULL,
		[changed] [bit] NULL,
		[createDate] [datetime] NOT NULL,
		[fixed] [datetime] NULL
	)

	-- copy structure of orgMemberOptions into temp table
	CREATE TABLE ##orgMemberOptionsTemp (
		[uid] [int] NOT NULL,
		[optionCodeID] [int] NOT NULL,
		[orgMemberDataID] [int] NOT NULL,
		[optionNameID] [int] NOT NULL,
		[optionCodeValue] [varchar](250) NULL,
		[createDate] [datetime] NULL
	)

	-- Execute a bulk insert into previously defined temporary table
	SET @qry = 'bcp ##orgMemberDataTemp in ' + @OMDRestoreFile + ' -c -T -S' + CAST(serverproperty('servername') as varchar(40))
	execute xp_cmdshell @qry

	SET @qry = 'bcp ##orgMemberOptionsTemp in ' + @OMORestoreFile + ' -c -T -S' + CAST(serverproperty('servername') as varchar(40))
	execute xp_cmdshell @qry
END TRY
BEGIN CATCH
	SELECT ERROR_MESSAGE() AS ErrorMessage
	RETURN -1
END CATCH	

BEGIN TRANSACTION
	-- delete from orgmemberoptions
	DELETE FROM dbo.orgMemberOptions
	FROM orgMemberOptions O 
	INNER JOIN dbo.orgMemberData D ON O.orgMemberDataID = D.orgMemberDataID 
	WHERE D.orgCode = @orgCode 
		IF @@ERROR <> 0 GOTO on_error

	-- DELETE NEW MEMBERS
	DELETE FROM dbo.orgMemberData 
	WHERE orgCode = @orgCode 
	AND orgMemberDataID NOT IN (SELECT orgMemberDataID FROM ##orgMemberDataTemp WHERE orgCode = @orgCode)
		IF @@ERROR <> 0 GOTO on_error
	
	-- UPDATE from saved file data
	UPDATE dbo.orgMemberData 
	SET [status] = i.status, prefix = i.prefix, firstName = i.firstName, middleName = i.middleName,
		lastName = i.lastName, Suffix = i.suffix, ProfessionSuffix = i.ProfessionSuffix,
		Firm = i.firm, address1 = i.address1, address2 = i.address2, address3 = i.address3,
		city = i.city, [state] = i.state, zipcode = i.zipcode, county = i.county,
		telephone = i.telephone, fax = i.fax, email = i.email, website = i.website,
		bio = i.bio, memberDate = i.memberDate, allowTS = i.allowTS, orgmembertype = i.orgmembertype, 
		defaultusername = i.defaultusername, defaultpassword = i.defaultpassword, barno = i.barno, 
		casduid = i.casduid, [Disabled] = i.Disabled, memberCode = i.memberCode, isGuest = i.isGuest, 
		changed = i.changed, createDate = i.createDate, fixed = i.fixed
	FROM dbo.orgMemberData as d 
	INNER JOIN ##orgMemberDataTemp as i ON d.orgMemberDataID = i.orgMemberDataID
	WHERE d.orgCode = @orgCode
		IF @@ERROR <> 0 GOTO on_error

	-- insert options		
	SET IDENTITY_INSERT dbo.orgMemberOptions ON 
		INSERT INTO dbo.orgMemberOptions ([uid], optionCodeID, orgMemberDataID, optionNameID, optionCodeValue, createDate)
		SELECT [uid], optionCodeID, orgMemberDataID, optionNameID, optionCodeValue, createDate 
		FROM ##orgMemberOptionsTemp
			IF @@ERROR <> 0 GOTO on_error
	SET IDENTITY_INSERT dbo.orgMemberOptions OFF
COMMIT TRANSACTION

-- drop temp tables
DROP TABLE ##orgMemberDataTemp
DROP TABLE ##orgMemberOptionsTemp

RETURN 1

on_error:
ROLLBACK TRANSACTION
RETURN -1
GO

ALTER PROCEDURE [dbo].[up_exportCSV]
@temptablename varchar(30),
@csvfilename varchar(100),
@sql varchar(max)

AS

-- drop the temp table
IF OBJECT_ID('tempdb..' + @temptablename) IS NOT NULL 
	EXEC('DROP TABLE ' + @temptablename)

-- save the query to a temp table
EXEC(@sql)

DECLARE @cmd varchar(6000)
set @cmd = 'bcp ' + @temptablename + ' out ' + @csvfilename + ' -c -t, -T -S' + CAST(serverproperty('servername') as varchar(40))
exec master..xp_cmdshell @cmd	

-- get fields returned
EXEC('tempdb.dbo.SP_COLUMNS ' + @temptablename)

-- drop temp table
EXEC('DROP TABLE ' + @temptablename)
GO

ALTER PROCEDURE [dbo].[up_exportCSV_new]
@csvfilename varchar(400),
@sql varchar(max)

AS

-- drop the temp tables
IF OBJECT_ID('tempdb..##tmpCSVExport') IS NOT NULL 
	DROP TABLE ##tmpCSVExport
IF OBJECT_ID('tempdb..#tmpCol') IS NOT NULL 
	DROP TABLE #tmpCol

-- run query into temp table
select @sql = stuff(@sql, charIndex('from',@sql), len('from'), 'into ##tmpCSVExport from')
EXEC(@sql)

-- export as chr(31) column delimiter and chr(30) row delimiter
DECLARE @cmd varchar(7000)
set @cmd = 'bcp ##tmpCSVExport out "' + @csvfilename + '" -c -t0x1F -r0x1E -T -S' + CAST(serverproperty('servername') as varchar(40))
exec master..xp_cmdshell @cmd, NO_OUTPUT

-- read in file to replace beeps with commas and escape quotes and replace nulls with empty string
declare @tmpFile varchar(max), @trash bit
select @tmpFile = dbo.fn_ReadFile(@csvfilename,0,1)
if len(@tmpFile) > 0 BEGIN
	select @tmpFile = replace(left(@tmpFile,len(@tmpFile)-1),'"','""') -- escape all quotes
	select @tmpFile = replace(replace(@tmpFile,char(13),' '),char(10),' ') -- replace cr and lf with space
	select @tmpFile = replace(@tmpFile COLLATE SQL_Latin1_General_CP1_CS_AS,char(0),'')		-- replace nulls with empty string
	select @tmpFile = replace(@tmpFile,char(31),'","')	-- replace 31 with column separator
	select @tmpFile = replace(@tmpFile,char(30),'"' + char(13) + char(10) + '"') -- replace 30 with crlf
	select @tmpFile = '"' + @tmpFile + '"'
end

-- record 1st row as field names and save file
CREATE TABLE #tmpCol (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
	COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
	SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
	COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
	CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)
INSERT INTO #tmpCol
EXEC tempdb.dbo.SP_COLUMNS ##tmpCSVExport
declare @colList varchar(max)
select @colList = COALESCE(@colList + ',', '') + '"' + column_name + '"'
	from #tmpCol 
	order by ORDINAL_POSITION
select @trash = dbo.fn_WriteFile(@csvfilename,@colList + char(13) + char(10) + @tmpFile,1)

-- get fields returned
SELECT COLUMN_NAME 
FROM #tmpCol
order by ORDINAL_POSITION

-- drop temp tables
IF OBJECT_ID('tempdb..#tmpCol') IS NOT NULL 
	DROP TABLE #tmpCol
IF OBJECT_ID('tempdb..##tmpCSVExport') IS NOT NULL 
	DROP TABLE ##tmpCSVExport
GO

use uslegalforms
GO
ALTER PROCEDURE [dbo].[up_importData]
@csvlocation varchar(100),
@bcplocation varchar(100)

AS

SET NOCOUNT ON 

-- create format file
/*
declare @cmd varchar(6000)
set @cmd = 'bcp uslegalForms.dbo.import_usLegalDataDump format nul -T -t"|" -c -S ' + CAST(serverproperty('servername') as varchar(20)) + ' -f e:\uslegal\import_usLegalDataDump.fmt'
exec master..xp_cmdshell @cmd
*/

DECLARE @fileexists int
EXECUTE master..xp_fileexist @csvlocation, @fileexists OUTPUT
IF @fileexists = 0
	RETURN -99

-- clear old tsforms data
TRUNCATE TABLE dbo.import_usLegalDataDump

-- import new data into tsforms
declare @cmd varchar(6000)
set @cmd = 'bcp uslegalForms.dbo.import_usLegalDataDump in ' + @csvlocation + ' -S' + CAST(serverproperty('servername') as varchar(40)) + ' -T -f ' + @bcplocation
exec master..xp_cmdshell @cmd

-- run checks
IF EXISTS (SELECT ControlNum FROM dbo.import_usLegalDataDump GROUP BY ControlNum HAVING (COUNT(ControlNum) > 1))
	RETURN -98
-- IF EXISTS (SELECT ControlNum FROM dbo.import_usLegalDataDump WHERE hasPDF is null and hasDOC is null and hasWPD is null and hasTXT is null and hasRTF is null)
	-- RETURN -97 (non fatal)
IF EXISTS (SELECT ControlNum FROM dbo.import_usLegalDataDump WHERE formprice is null and courtprice is null)
	RETURN -96
-- IF EXISTS (SELECT ControlNum FROM dbo.import_usLegalDataDump WHERE formdescription like '%' + char(13) + '%')
	-- RETURN -95 (non fatal)
-- IF EXISTS (SELECT ControlNum FROM dbo.import_usLegalDataDump WHERE formdescription like '%\%')
	-- RETURN -94 (non fatal)
IF EXISTS (SELECT ControlNum FROM dbo.import_usLegalDataDump WHERE formtitle is null)
	RETURN -95

-- load the categories
EXEC dbo.up_createCatTree
EXEC dbo.up_createCatTreeByState

-- load the forms
EXEC dbo.up_createForms
EXEC dbo.up_createFormsCatLink

-- move data to live
TRUNCATE TABLE dbo.tblFormsAndCategories
TRUNCATE TABLE dbo.tblCategories
TRUNCATE TABLE dbo.tblForms

SET IDENTITY_INSERT dbo.tblCategories ON
	INSERT INTO dbo.tblCategories (categoryID,category,parentCategoryID,browseBy,subCatCount,catFormsCount,treeFormsCount,categoryExpanded)
	SELECT categoryID,category,parentCategoryID,browseBy,subCatCount,catFormsCount,treeFormsCount,categoryExpanded
	FROM dbo.import_tblCategories
SET IDENTITY_INSERT dbo.tblCategories OFF

SET IDENTITY_INSERT dbo.tblForms ON
	INSERT INTO dbo.tblForms (formID,formTitle,formDescription,controlNum, price, formTypeID, formPreview, hasDOC, hasTXT, hasPDF, hasWPD, hasRTF)
	SELECT formID,formTitle,formDescription,controlNum, price, formTypeID, formPreview, hasDOC, hasTXT, hasPDF, hasWPD, hasRTF
	FROM dbo.import_tblForms
SET IDENTITY_INSERT dbo.tblForms OFF

SET IDENTITY_INSERT dbo.tblFormsAndCategories ON
	INSERT INTO dbo.tblFormsAndCategories (linkID, formID, categoryID)
	SELECT linkID, formID, categoryID
	FROM dbo.import_tblFormsAndCategories
SET IDENTITY_INSERT dbo.tblFormsAndCategories OFF

DROP TABLE dbo.import_tblcategories
DROP TABLE dbo.import_tblforms
DROP TABLE dbo.import_tblformsAndCategories

-- record successful import
INSERT INTO tblImports (importdate)
VALUES (getdate())

-- repopulate full text index
ALTER FULLTEXT CATALOG USLFSearching REBUILD
ALTER FULLTEXT INDEX ON tblForms START FULL POPULATION

-- clear old tsforms data
TRUNCATE TABLE dbo.import_usLegalDataDump
GO

