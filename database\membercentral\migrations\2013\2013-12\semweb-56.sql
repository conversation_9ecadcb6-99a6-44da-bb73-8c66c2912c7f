use [customApps]
GO

-- Correct errors found while coding the report.

/* To prevent any potential data loss issues, you should review this script in detail before running it outside the context of the database designer.*/
BEGIN TRANSACTION
SET QUOTED_IDENTIFIER ON
SET ARITHABORT ON
SET NUMERIC_ROUNDABORT OFF
SET CONCAT_NULL_YIELDS_NULL ON
SET ANSI_NULLS ON
SET ANSI_PADDING ON
SET ANSI_WARNINGS ON
COMMIT
BEGIN TRANSACTION
GO
ALTER TABLE dbo.TXRX_Evaluation_Responses
	DROP CONSTRAINT DF_TXRX_Evaluation_Responses_DateCompleted
GO
CREATE TABLE dbo.Tmp_TXRX_Evaluation_Responses
	(
	evaluationID int NOT NULL IDENTITY (1, 1),
	memberID int NOT NULL,
	eventID int NOT NULL,
	overallOuality int NOT NULL,
	educationalNeeds int NOT NULL,
	contentValue int NOT NULL,
	effectiveness int NOT NULL,
	appropriateness int NOT NULL,
	materials int NOT NULL,
	ReinforcedCurrentPractice bit NOT NULL,
	ProvidedNewIdeas bit NOT NULL,
	ImproveMyPractice bit NOT NULL,
	EnhancedCurrentKnowledge bit NOT NULL,
	ProgramIncreasedKnowledge bit NOT NULL,
	futureActivities bit NOT NULL,
	notPromote bit NOT NULL,
	didPromote text NULL,
	metLearningObjectives bit NOT NULL,
	notMetlearningObjectives text NULL,
	InfoCauseChanges bit NOT NULL,
	ThingsDifferently text NULL,
	HowCommitted int NOT NULL,
	ActivityEnjoyedMost text NULL,
	ActivityEnjoyedLeast text NULL,
	AdditionalComments text NULL,
	subjectMatterKnowledge int NOT NULL,
	CommPresentationSkills int NOT NULL,
	OverallResponsiveness int NOT NULL,
	SpeakerQuality int NOT NULL,
	FollowUp bit NOT NULL,
	DateCompleted datetime NOT NULL
	)  ON [PRIMARY]
	 TEXTIMAGE_ON [PRIMARY]
GO
ALTER TABLE dbo.Tmp_TXRX_Evaluation_Responses ADD CONSTRAINT
	DF_TXRX_Evaluation_Responses_DateCompleted DEFAULT (getdate()) FOR DateCompleted
GO
SET IDENTITY_INSERT dbo.Tmp_TXRX_Evaluation_Responses ON
GO
IF EXISTS(SELECT * FROM dbo.TXRX_Evaluation_Responses)
	 EXEC('INSERT INTO dbo.Tmp_TXRX_Evaluation_Responses (evaluationID, memberID, eventID, overallOuality, educationalNeeds, contentValue, effectiveness, appropriateness, materials, ReinforcedCurrentPractice, ProvidedNewIdeas, ImproveMyPractice, EnhancedCurrentKnowledge, ProgramIncreasedKnowledge, futureActivities, notPromote, metLearningObjectives, notMetlearningObjectives, InfoCauseChanges, ThingsDifferently, HowCommitted, ActivityEnjoyedMost, ActivityEnjoyedLeast, AdditionalComments, subjectMatterKnowledge, CommPresentationSkills, OverallResponsiveness, SpeakerQuality, FollowUp, DateCompleted)
		SELECT evaluationID, memberID, eventID, overallOuality, educationalNeeds, contentValue, effectiveness, appropriateness, materials, ReinforcedCurrentPractice, ProvidedNewIdeas, ImproveMyPractice, EnhancedCurrentKnowledge, ProgramIncreasedKnowledge, futureActivities, notPromote, metLearningObjectives, notMetlearningObjectives, InfoCauseChanges, ThingsDifferently, CONVERT(int, HowCommitted), ActivityEnjoyedMost, ActivityEnjoyedLeast, AdditionalComments, subjectMatterKnowledge, CommPresentationSkills, OverallResponsiveness, SpeakerQuality, FollowUp, DateCompleted FROM dbo.TXRX_Evaluation_Responses WITH (HOLDLOCK TABLOCKX)')
GO
SET IDENTITY_INSERT dbo.Tmp_TXRX_Evaluation_Responses OFF
GO
DROP TABLE dbo.TXRX_Evaluation_Responses
GO
EXECUTE sp_rename N'dbo.Tmp_TXRX_Evaluation_Responses', N'TXRX_Evaluation_Responses', 'OBJECT' 
GO
COMMIT
-- Since the data was not captured correctly set the answers all to 5
UPDATE dbo.TXRX_Evaluation_Responses set HowCommitted = 5
GO










USE [customApps]
GO
/****** Object:  StoredProcedure [dbo].[txrx_conferenceEvaluations]    Script Date: 12/02/2013 13:36:59 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROC [dbo].[txrx_conferenceEvaluations]
@eventID int
AS

DECLARE @orgID int, @siteID int, @languageID int 
SELECT @orgID = membercentral.dbo.fn_getOrgIDFromOrgCode('TXRX')
SELECT @siteID = membercentral.dbo.fn_getSiteIDFromSiteCode('TXRX')
SELECT @languageID = 1

DECLARE @numAttendees int, @numResponses decimal(10,2), @eventTitle as varchar(250), @location as varchar(250)
DECLARE @eventDate as dateTime, @ACPENumber as varchar(250)

declare @tmpActivityEvaluation table (
	questionID int,
	col int,
	totalCount int
)

declare @activityEvaluation table (
	questionID int,
	question varchar(500),
	[1] int,
	[2] int,
	[3] int,
	[4] int,
	[5] int,
	average decimal(10,2)
)

declare @tmpHowCommitted table (
	questionID int,
	col int,
	totalCount int
)
declare @howCommitted table (
	questionID int,
	question varchar(500),
	[1] int,
	[2] int,
	[3] int,
	[4] int,
	[5] int,
	average decimal(10,2)
)


declare @tmpActivitySpeaker table (
	questionID int,
	col int,
	totalCount int
)
declare @activitySpeaker table (
	questionID int,
	question varchar(500),
	[1] int,
	[2] int,
	[3] int,
	[4] int,
	[5] int,
	average decimal(10,2)
)

declare @impactActivity table (
	questionID int,
	question varchar(500),
	numYes int,
	pctYes int,
	numNo int,
	pctNo int
)


insert into @activityEvaluation (questionID, question, [1], [2], [3], [4], [5], average)
values(1, '1. Overall quality of this activity', 0, 0, 0, 0, 0, 0)
insert into @activityEvaluation (questionID, question, [1], [2], [3], [4], [5], average)
values(2, '2. Met individual educational needs', 0, 0, 0, 0, 0, 0)
insert into @activityEvaluation (questionID, question, [1], [2], [3], [4], [5], average)
values(3, '3. Value of content', 0, 0, 0, 0, 0, 0)
insert into @activityEvaluation (questionID, question, [1], [2], [3], [4], [5], average)
values(4, '4. Active learning exercises', 0, 0, 0, 0, 0, 0)
insert into @activityEvaluation (questionID, question, [1], [2], [3], [4], [5], average)
values(5, '5. Final exam questions', 0, 0, 0, 0, 0, 0)
insert into @activityEvaluation (questionID, question, [1], [2], [3], [4], [5], average)
values(6, '6. Usefulness of educational materials', 0, 0, 0, 0, 0, 0)

insert into @howCommitted (questionID, question, [1], [2], [3], [4], [5], average)
values(1, 'G. How committed are you to making these changes?', 0, 0, 0, 0, 0, 0)

insert into @activitySpeaker (questionID, question, [1], [2], [3], [4], [5], average)
values(1, 'Knowledge of subject matter', 0, 0, 0, 0, 0, 0)
insert into @activitySpeaker (questionID, question, [1], [2], [3], [4], [5], average)
values(2, 'Communication and presentation skills', 0, 0, 0, 0, 0, 0)
insert into @activitySpeaker (questionID, question, [1], [2], [3], [4], [5], average)
values(3, 'Overall responsiveness to audience''s questions', 0, 0, 0, 0, 0, 0)
insert into @activitySpeaker (questionID, question, [1], [2], [3], [4], [5], average)
values(4, 'Overall speaker quality', 0, 0, 0, 0, 0, 0)


select @numResponses = count(*)
from dbo.TXRX_Evaluation_Responses
where eventID = @eventID


SELECT @numAttendees = count(*)
FROM membercentral.dbo.ev_events e	
	INNER JOIN  membercentral.dbo.ev_registration r ON e.eventID = r.eventID 
		AND e.siteID = @siteID
	INNER JOIN  membercentral.dbo.ev_times et ON et.eventID = e.eventID 
		AND e.eventID = @eventID
		AND et.timeZoneID = 6
	INNER JOIN  membercentral.dbo.ev_registrants er ON r.registrationID = er.registrationID
		AND er.attended = 1

SELECT	@ACPENumber = ec.approvalNum, @eventDate = et.endTime
FROM membercentral.dbo.ev_events e	
	INNER JOIN  membercentral.dbo.ev_registration r ON e.eventID = r.eventID 
		AND e.siteID = @siteID
	INNER JOIN  membercentral.dbo.ev_times et ON et.eventID = e.eventID 
		AND e.eventID = @eventID
		AND et.timeZoneID = 6
	INNER JOIN membercentral.dbo.ev_registrants er ON r.registrationID = er.registrationID
	INNER JOIN membercentral.dbo.crd_requests erc ON erc.registrantID = er.registrantID
	INNER JOIN membercentral.dbo.crd_offeringTypes ect ON ect.offeringTypeID = erc.offeringTypeID 
	INNER JOIN membercentral.dbo.crd_offerings ec ON ec.offeringID = ect.offeringID 


select @eventTitle = eventcontent.contentTitle, @location = locationcontent.contentTitle
from membercentral.dbo.ev_events as ev
	INNER JOIN membercentral.dbo.ev_eventTypes AS et ON ev.eventTypeID = et.eventTypeID and ev.eventID = @eventID		
		AND ev.siteID = @siteID
	INNER JOIN membercentral.dbo.ev_calendarEvents as ce on ce.sourceEventID = ev.eventID and ce.calendarID = ce.sourceCalendarID
	INNER JOIN membercentral.dbo.ev_calendars as c on c.calendarID = ce.calendarID
	inner join membercentral.dbo.cms_applicationInstances as ai on ai.applicationInstanceID = c.applicationInstanceID
	cross apply membercentral.dbo.fn_getContent(ev.eventcontentID,@languageID) as eventcontent
	cross apply membercentral.dbo.fn_getContent(ev.locationcontentID,@languageID) as locationcontent


select getDate() as dateRun, @numAttendees as numAttendees, cast(@numResponses as int) as numResponses, @eventTitle as eventTitle, 
@location as location, @eventDate as eventDate, '' as speakerName, @ACPENumber as ACPENumber


insert into @tmpActivityEvaluation
select *
from (
	select 1 as questionID, overallOuality as col, count(overallOuality) as totCount
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	group by overallOuality
	union
	select 2, educationalNeeds, count(educationalNeeds)
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	group by educationalNeeds
	union
	select 3, contentValue, count(contentValue)
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	group by contentValue
	union
	select 4, effectiveness, count(effectiveness)
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	group by effectiveness
	union
	select 5, appropriateness, count(appropriateness)
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	group by appropriateness
	union
	select 6, materials, count(materials)
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	group by materials
) as tmp

update ae set [1] = ta.totalCount
from @tmpActivityEvaluation ta
inner join @activityEvaluation ae on ae.questionID = ta.questionID
and ta.col = 1

update ae set [2] = ta.totalCount
from @tmpActivityEvaluation ta
inner join @activityEvaluation ae on ae.questionID = ta.questionID
and ta.col = 2

update ae set [3] = ta.totalCount
from @tmpActivityEvaluation ta
inner join @activityEvaluation ae on ae.questionID = ta.questionID
and ta.col = 3

update ae set [4] = ta.totalCount
from @tmpActivityEvaluation ta
inner join @activityEvaluation ae on ae.questionID = ta.questionID
and ta.col = 4

update ae set [5] = ta.totalCount
from @tmpActivityEvaluation ta
inner join @activityEvaluation ae on ae.questionID = ta.questionID
and ta.col = 5


if @numResponses > 0 BEGIN
	update ae
	  set average = tmp.average
	from (
		select 1 as questionID, cast(round((sum(overallOuality) / @numResponses),2) as decimal(10,2)) as average
		from dbo.TXRX_Evaluation_Responses
		where eventID = @eventID
		union
		select 2, cast(round((sum(educationalNeeds) / @numResponses),2) as decimal(10,2)) as average
		from dbo.TXRX_Evaluation_Responses
		where eventID = @eventID
		union
		select 3, cast(round((sum(contentValue) / @numResponses),2) as decimal(10,2)) as average
		from dbo.TXRX_Evaluation_Responses
		where eventID = @eventID
		union
		select 4, cast(round((sum(effectiveness) / @numResponses),2) as decimal(10,2)) as average
		from dbo.TXRX_Evaluation_Responses
		where eventID = @eventID
		union
		select 5, cast(round((sum(appropriateness) / @numResponses),2) as decimal(10,2)) as average
		from dbo.TXRX_Evaluation_Responses
		where eventID = @eventID
		union
		select 6, cast(round((sum(materials) / @numResponses),2) as decimal(10,2)) as average
		from dbo.TXRX_Evaluation_Responses
		where eventID = @eventID
	) as tmp
	inner join @activityEvaluation ae on ae.questionid = tmp.questionid
END

select * from @activityEvaluation

declare @numYes int, @pctYes decimal(10,0), @numNo int, @pctNo decimal(10,0)

-- A1
select @numYes = 0, @pctYes = 0, @numNo = 0, @pctNo = 0
insert into @impactActivity(questionID, question, numYes, pctYes, numNo, pctNo)
values (1, 'Reinforced current practice/treatment habits', @numYes, @pctYes, @numNo, @pctNo)

if @numResponses > 0 BEGIN
	select @numYes = count(ReinforcedCurrentPractice), @pctYes = round((count(ReinforcedCurrentPractice) * 100.0 / @numResponses), 0)
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	and ReinforcedCurrentPractice = 1

	select @numNo = count(ReinforcedCurrentPractice), @pctNo = round((count(ReinforcedCurrentPractice) * 100.0 / @numResponses), 0)
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	and ReinforcedCurrentPractice = 0

	update @impactActivity set numYes = @numYes, pctYes = @pctYes, numNo = @numNo, pctNo = @pctNo 
	where questionID = 1
END


-- A2
select @numYes = 0, @pctYes = 0, @numNo = 0, @pctNo = 0
insert into @impactActivity(questionID, question, numYes, pctYes, numNo, pctNo)
values (2, 'Will improve my practice/patient outcomes', @numYes, @pctYes, @numNo, @pctNo)

if @numResponses > 0 BEGIN
	select @numYes = count(ImproveMyPractice), @pctYes = round((count(ImproveMyPractice) * 100.0 / @numResponses), 0)
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	and ImproveMyPractice = 1

	select @numNo = count(ImproveMyPractice), @pctNo = round((count(ImproveMyPractice) * 100.0 / @numResponses), 0)
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	and ImproveMyPractice = 0

	update @impactActivity set numYes = @numYes, pctYes = @pctYes, numNo = @numNo, pctNo = @pctNo 
	where questionID = 2
END

-- A3
select @numYes = 0, @pctYes = 0, @numNo = 0, @pctNo = 0
insert into @impactActivity(questionID, question, numYes, pctYes, numNo, pctNo)
values (3, 'Provided new ideas/information I expect to use', @numYes, @pctYes, @numNo, @pctNo)

if @numResponses > 0 BEGIN
	select @numYes = count(ProvidedNewIdeas), @pctYes = round((count(ProvidedNewIdeas) * 100.0 / @numResponses), 0)
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	and ProvidedNewIdeas = 1

	select @numNo = count(ProvidedNewIdeas), @pctNo = round((count(ProvidedNewIdeas) * 100.0 / @numResponses), 0)
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	and ProvidedNewIdeas = 0

	update @impactActivity set numYes = @numYes, pctYes = @pctYes, numNo = @numNo, pctNo = @pctNo 
	where questionID = 3
END

-- A4
select @numYes = 0, @pctYes = 0, @numNo = 0, @pctNo = 0
insert into @impactActivity(questionID, question, numYes, pctYes, numNo, pctNo)
values (4, 'Enhanced my current knowledge base', @numYes, @pctYes, @numNo, @pctNo)

if @numResponses > 0 BEGIN
	select @numYes = count(EnhancedCurrentKnowledge), @pctYes = round((count(EnhancedCurrentKnowledge) * 100.0 / @numResponses), 0)
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	and EnhancedCurrentKnowledge = 1

	select @numNo = count(EnhancedCurrentKnowledge), @pctNo = round((count(EnhancedCurrentKnowledge) * 100.0 / @numResponses), 0)
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	and EnhancedCurrentKnowledge = 0

	update @impactActivity set numYes = @numYes, pctYes = @pctYes, numNo = @numNo, pctNo = @pctNo 
	where questionID = 4
END

-- B
select @numYes = 0, @pctYes = 0, @numNo = 0, @pctNo = 0
insert into @impactActivity(questionID, question, numYes, pctYes, numNo, pctNo)
values (5, 'B. The program increased my knowledge in the subject areas', @numYes, @pctYes, @numNo, @pctNo)

if @numResponses > 0 BEGIN
	select @numYes = count(ProgramIncreasedKnowledge), @pctYes = round((count(ProgramIncreasedKnowledge) * 100.0 / @numResponses), 0)
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	and ProgramIncreasedKnowledge = 1

	select @numNo = count(ProgramIncreasedKnowledge), @pctNo = round((count(ProgramIncreasedKnowledge) * 100.0 / @numResponses), 0)
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	and ProgramIncreasedKnowledge = 0

	update @impactActivity set numYes = @numYes, pctYes = @pctYes, numNo = @numNo, pctNo = @pctNo 
	where questionID = 5
END

-- C
select @numYes = 0, @pctYes = 0, @numNo = 0, @pctNo = 0
insert into @impactActivity(questionID, question, numYes, pctYes, numNo, pctNo)
values (6, 'C. I feel future activities on this subject matter are necessary and/or important to my practice', @numYes, @pctYes, @numNo, @pctNo)

if @numResponses > 0 BEGIN
	select @numYes = count(futureActivities), @pctYes = round((count(futureActivities) * 100.0 / @numResponses), 0)
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	and futureActivities = 1

	select @numNo = count(futureActivities), @pctNo = round((count(futureActivities) * 100.0 / @numResponses), 0)
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	and futureActivities = 0

	update @impactActivity set numYes = @numYes, pctYes = @pctYes, numNo = @numNo, pctNo = @pctNo 
	where questionID = 6
END

-- D
select @numYes = 0, @pctYes = 0, @numNo = 0, @pctNo = 0
insert into @impactActivity(questionID, question, numYes, pctYes, numNo, pctNo)
values (7, 'D. The program did not promote a particular product/company', @numYes, @pctYes, @numNo, @pctNo)

if @numResponses > 0 BEGIN
	select @numYes = count(notPromote), @pctYes = round((count(notPromote) * 100.0 / @numResponses), 0)
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	and notPromote = 1

	select @numNo = count(notPromote), @pctNo = round((count(notPromote) * 100.0 / @numResponses), 0)
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	and notPromote = 0

	update @impactActivity set numYes = @numYes, pctYes = @pctYes, numNo = @numNo, pctNo = @pctNo 
	where questionID = 7
END

-- E
select @numYes = 0, @pctYes = 0, @numNo = 0, @pctNo = 0
insert into @impactActivity(questionID, question, numYes, pctYes, numNo, pctNo)
values (8, 'E. Did this activity meet the stated learning objectives', @numYes, @pctYes, @numNo, @pctNo)

if @numResponses > 0 BEGIN
	select @numYes = count(metLearningObjectives), @pctYes = round((count(metLearningObjectives) * 100.0 / @numResponses), 0)
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	and metLearningObjectives = 1

	select @numNo = count(metLearningObjectives), @pctNo = round((count(metLearningObjectives) * 100.0 / @numResponses), 0)
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	and metLearningObjectives = 0

	update @impactActivity set numYes = @numYes, pctYes = @pctYes, numNo = @numNo, pctNo = @pctNo 
	where questionID = 8
END

-- F
select @numYes = 0, @pctYes = 0, @numNo = 0, @pctNo = 0
insert into @impactActivity(questionID, question, numYes, pctYes, numNo, pctNo)
values (9, 'F. Will the information presented cause you to make any changes in your practice?', @numYes, @pctYes, @numNo, @pctNo)

if @numResponses > 0 BEGIN
	select @numYes = count(InfoCauseChanges), @pctYes = round((count(InfoCauseChanges) * 100.0 / @numResponses), 0)
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	and InfoCauseChanges = 1

	select @numNo = count(InfoCauseChanges), @pctNo = round((count(InfoCauseChanges) * 100.0 / @numResponses), 0)
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	and InfoCauseChanges = 0

	update @impactActivity set numYes = @numYes, pctYes = @pctYes, numNo = @numNo, pctNo = @pctNo 
	where questionID = 9
END

select * from @impactActivity

select didPromote
from dbo.TXRX_Evaluation_Responses
where eventID = @eventID
and DATALENGTH(didPromote) > 1

select notMetlearningObjectives
from dbo.TXRX_Evaluation_Responses
where eventID = @eventID
and metLearningObjectives = 0

select ThingsDifferently
from dbo.TXRX_Evaluation_Responses
where eventID = @eventID
and DATALENGTH(ThingsDifferently) > 1


select ActivityEnjoyedMost
from dbo.TXRX_Evaluation_Responses
where eventID = @eventID
and DATALENGTH(ActivityEnjoyedMost) > 1

select ActivityEnjoyedLeast
from dbo.TXRX_Evaluation_Responses
where eventID = @eventID
and DATALENGTH(ActivityEnjoyedLeast) > 1

select AdditionalComments
from dbo.TXRX_Evaluation_Responses
where eventID = @eventID
and DATALENGTH(AdditionalComments) > 1



insert into @tmpHowCommitted
select *
from (
	select 1 as questionID, HowCommitted as col, count(HowCommitted) as totCount
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	group by HowCommitted
) as tmp

update ae set [1] = ta.totalCount
from @tmpHowCommitted ta
inner join @howCommitted ae on ae.questionID = ta.questionID
and ta.col = 1

update ae set [2] = ta.totalCount
from @tmpHowCommitted ta
inner join @howCommitted ae on ae.questionID = ta.questionID
and ta.col = 2

update ae set [3] = ta.totalCount
from @tmpHowCommitted ta
inner join @howCommitted ae on ae.questionID = ta.questionID
and ta.col = 3

update ae set [4] = ta.totalCount
from @tmpHowCommitted ta
inner join @howCommitted ae on ae.questionID = ta.questionID
and ta.col = 4

update ae set [5] = ta.totalCount
from @tmpHowCommitted ta
inner join @howCommitted ae on ae.questionID = ta.questionID
and ta.col = 5

if @numResponses > 0 BEGIN
	update ae
	  set average = tmp.average
	from (
		select 1 as questionID, cast(round((sum(HowCommitted) / @numResponses),2) as decimal(10,2)) as average
		from dbo.TXRX_Evaluation_Responses
		where eventID = @eventID
	) as tmp
	inner join @howCommitted ae on ae.questionid = tmp.questionid
END
select * from @howCommitted

insert into @tmpActivitySpeaker
select *
from (
	select 1 as questionID, subjectMatterKnowledge as col, count(subjectMatterKnowledge) as totCount
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	group by subjectMatterKnowledge
	union
	select 2, CommPresentationSkills, count(CommPresentationSkills)
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	group by CommPresentationSkills
	union
	select 3, OverallResponsiveness, count(OverallResponsiveness)
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	group by OverallResponsiveness
	union
	select 4, SpeakerQuality, count(SpeakerQuality)
	from dbo.TXRX_Evaluation_Responses
	where eventID = @eventID
	group by SpeakerQuality
) as tmp

update ae set [1] = ta.totalCount
from @tmpActivitySpeaker ta
inner join @activitySpeaker ae on ae.questionID = ta.questionID
and ta.col = 1

update ae set [2] = ta.totalCount
from @tmpActivitySpeaker ta
inner join @activitySpeaker ae on ae.questionID = ta.questionID
and ta.col = 2

update ae set [3] = ta.totalCount
from @tmpActivitySpeaker ta
inner join @activitySpeaker ae on ae.questionID = ta.questionID
and ta.col = 3

update ae set [4] = ta.totalCount
from @tmpActivitySpeaker ta
inner join @activitySpeaker ae on ae.questionID = ta.questionID
and ta.col = 4

update ae set [5] = ta.totalCount
from @tmpActivitySpeaker ta
inner join @activitySpeaker ae on ae.questionID = ta.questionID
and ta.col = 5


if @numResponses > 0 BEGIN
	update ae
	  set average = tmp.average
	from (
		select 1 as questionID, cast(round((sum(subjectMatterKnowledge) / @numResponses),2) as decimal(10,2)) as average
		from dbo.TXRX_Evaluation_Responses
		where eventID = @eventID
		union
		select 2, cast(round((sum(CommPresentationSkills) / @numResponses),2) as decimal(10,2)) as average
		from dbo.TXRX_Evaluation_Responses
		where eventID = @eventID
		union
		select 3, cast(round((sum(OverallResponsiveness) / @numResponses),2) as decimal(10,2)) as average
		from dbo.TXRX_Evaluation_Responses
		where eventID = @eventID
		union
		select 4, cast(round((sum(SpeakerQuality) / @numResponses),2) as decimal(10,2)) as average
		from dbo.TXRX_Evaluation_Responses
		where eventID = @eventID
	) as tmp
	inner join @activitySpeaker ae on ae.questionid = tmp.questionid
END


select * from @activitySpeaker

RETURN 0

