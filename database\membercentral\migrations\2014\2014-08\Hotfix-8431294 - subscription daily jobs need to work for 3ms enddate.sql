USE [memberCentral]
GO
/****** Object:  StoredProcedure [dbo].[sub_expireOffers]    Script Date: 08/01/2014 15:21:46 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROCEDURE [dbo].[sub_expireOffers] 
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @dtStart DATETIME, @dtEnd DATETIME, @dateToUse DATETIME
	select @dateToUse = dateadd(DAY, -1, getdate())
	SELECT @dtStart = dateadd(DAY, datediff(DAY, 0, @dateToUse), 0) 
	SELECT @dtEnd = dateadd(ms, -3, dateadd(DAY, datediff(DAY, 0, @dateToUse)+1, 0))

	IF OBJECT_ID('tempdb..##tempExpireOfferJob') IS NOT NULL 
		DROP TABLE ##tempExpireOfferJob

	select t.siteID, sc.subscriberID
	INTO ##tempExpireOfferJob
	from dbo.sub_subscribers sc
	inner join dbo.sub_subscriptions s on s.subscriptionID = sc.subscriptionID
	inner join dbo.sub_types t on t.typeID = s.typeID
	inner join dbo.sites st on st.siteID = t.siteID
	inner join dbo.sub_statuses ss on ss.statusID = sc.statusID and ss.statusCode in ('R','O')
	where (sc.offerRescindDate is not null and sc.offerRescindDate <= @dtEnd)

	declare @minSubscriberID int, @currTempResult int, @currSiteID int, @subXOStatusCode varchar(1)
	
	select @subXOStatusCode=statusCode
	from dbo.sub_statuses 
	where statusCode = 'X'

	select @minSubscriberID = min(subscriberID) from ##tempExpireOfferJob
	while @minSubscriberID is not null BEGIN
		select @currSiteID=siteID
		from ##tempExpireOfferJob
		where subscriberID = @minSubscriberID
		
		EXEC dbo.sub_updateSubscriberStatus @subscriberID=@minSubscriberID,
											@newStatusCode=@subXOStatusCode,
											@siteID=@currSiteID,
											@enteredByMemberID=461530,
											@result=@currTempResult OUTPUT 

		
		select @minSubscriberID = min(subscriberID)
		from ##tempExpireOfferJob
		where subscriberID > @minSubscriberID
	END

	IF OBJECT_ID('tempdb..##tempExpireOfferJob') IS NOT NULL 
		DROP TABLE ##tempExpireOfferJob
END

GO

USE [memberCentral]
GO
/****** Object:  StoredProcedure [dbo].[sub_advanceRates]    Script Date: 08/01/2014 14:56:54 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROCEDURE [dbo].[sub_advanceRates]
AS
BEGIN
	SET NOCOUNT ON

	DECLARE @dtStart DATETIME, @dtEnd DATETIME, @dateToUse DATETIME

	select @dateToUse = dateadd(DAY, -1, getdate())
	SELECT @dtStart = dateadd(DAY, datediff(DAY, 0, @dateToUse), 0) 
	SELECT @dtEnd = dateadd(ms, -3, dateadd(DAY, datediff(DAY, 0, @dateToUse)+1, 0))

	IF OBJECT_ID('tempdb..##tempRatesAdvance') IS NOT NULL 
		DROP TABLE ##tempRatesAdvance

	SELECT *
	INTO ##tempRatesAdvance
	FROM dbo.sub_rates
	WHERE ((rateAdvanceOnTermEnd = 0 AND 
			(rateAFEndDate <= @dtEnd AND 
				(rateStartDateAFID is not null OR rateEndDateAFID is not null OR 
					termStartDateAFID is not null OR termEndDateAFID is not null)))
			OR (rateAdvanceOnTermEnd = 1 AND 
				(termAFEndDate <= @dtEnd AND 
				(rateStartDateAFID is not null OR rateEndDateAFID is not null OR 
					termStartDateAFID is not null OR termEndDateAFID is not null))))
	AND [status] <> 'D'

	declare @datePart varchar(20), @dateNum INT, @startOfPeriod INT, @endOfPeriod INT, @nextWeekday INT, @retDate DATETIME
	declare @rateStartDate DATETIME, @rateEndDate DATETIME, @termStartDate DATETIME, @termEndDate DATETIME, @graceEndDate DATETIME
	declare @rateStartDateAFID INT, @rateEndDateAFID INT, @termStartDateAFID INT, @termEndDateAFID INT, @graceAFID INT
	declare @minRateID int

	select @minRateID = min(rateID) from ##tempRatesAdvance
	while @minRateID is not null BEGIN
		select @rateStartDate=rateAFStartDate, @rateEndDate=rateAFEndDate, @termStartDate=termAFStartDate, 
				@termEndDate=termAFEndDate, @graceEndDate=graceEndDate,
				@rateStartDateAFID=rateStartDateAFID, @rateEndDateAFID=rateEndDateAFID, @termStartDateAFID=termStartDateAFID, 
				@termEndDateAFID=termEndDateAFID, @graceAFID=graceAFID
		from ##tempRatesAdvance
		where rateID = @minRateID

		IF @rateStartDateAFID is not null BEGIN
			select @retDate = NULL

			select @datePart=[datePart], @dateNum=dateNum, @startOfPeriod=startOfPeriod, @endOfPeriod=endOfPeriod, @nextWeekday=nextWeekday
			from dbo.sub_advanceFormulas
			where AFID = @rateStartDateAFID

			EXEC dbo.sub_getAFDate @baseDate=@rateStartDate, @datePart=@datePart, @dateNum=@dateNum, @startOfPeriod=@startOfPeriod, 
									@endOfPeriod=@endOfPeriod, @nextWeekday=@nextWeekday, @retDate=@retDate OUTPUT

			update dbo.sub_rates
			set rateAFStartDate = @retDate
			where rateID = @minRateID
		END

		IF @rateEndDateAFID is not null BEGIN
			select @retDate = NULL

			select @datePart=[datePart], @dateNum=dateNum, @startOfPeriod=startOfPeriod, @endOfPeriod=endOfPeriod, @nextWeekday=nextWeekday
			from dbo.sub_advanceFormulas
			where AFID = @rateEndDateAFID

			EXEC dbo.sub_getAFDate @baseDate=@rateEndDate, @datePart=@datePart, @dateNum=@dateNum, @startOfPeriod=@startOfPeriod, 
									@endOfPeriod=@endOfPeriod, @nextWeekday=@nextWeekday, @retDate=@retDate OUTPUT

			update dbo.sub_rates
			set rateAFEndDate = @retDate
			where rateID = @minRateID
		END

		IF @termStartDateAFID is not null BEGIN
			select @retDate = NULL

			select @datePart=[datePart], @dateNum=dateNum, @startOfPeriod=startOfPeriod, @endOfPeriod=endOfPeriod, @nextWeekday=nextWeekday
			from dbo.sub_advanceFormulas
			where AFID = @termStartDateAFID

			EXEC dbo.sub_getAFDate @baseDate=@termStartDate, @datePart=@datePart, @dateNum=@dateNum, @startOfPeriod=@startOfPeriod, 
									@endOfPeriod=@endOfPeriod, @nextWeekday=@nextWeekday, @retDate=@retDate OUTPUT

			update dbo.sub_rates
			set termAFStartDate = @retDate
			where rateID = @minRateID
		END

		IF @termEndDateAFID is not null BEGIN
			select @retDate = NULL

			select @datePart=[datePart], @dateNum=dateNum, @startOfPeriod=startOfPeriod, @endOfPeriod=endOfPeriod, @nextWeekday=nextWeekday
			from dbo.sub_advanceFormulas
			where AFID = @termEndDateAFID

			EXEC dbo.sub_getAFDate @baseDate=@termEndDate, @datePart=@datePart, @dateNum=@dateNum, @startOfPeriod=@startOfPeriod, 
									@endOfPeriod=@endOfPeriod, @nextWeekday=@nextWeekday, @retDate=@retDate OUTPUT

			update dbo.sub_rates
			set termAFEndDate = @retDate
			where rateID = @minRateID
		END

		IF @graceAFID is not null BEGIN
			select @retDate = NULL

			select @datePart=[datePart], @dateNum=dateNum, @startOfPeriod=startOfPeriod, @endOfPeriod=endOfPeriod, @nextWeekday=nextWeekday
			from dbo.sub_advanceFormulas
			where AFID = @graceAFID

			EXEC dbo.sub_getAFDate @baseDate=@graceEndDate, @datePart=@datePart, @dateNum=@dateNum, @startOfPeriod=@startOfPeriod, 
									@endOfPeriod=@endOfPeriod, @nextWeekday=@nextWeekday, @retDate=@retDate OUTPUT

			update dbo.sub_rates
			set graceEndDate = @retDate
			where rateID = @minRateID
		END

		select @minRateID = min(rateID) from ##tempRatesAdvance where rateID > @minRateID
	END

	IF OBJECT_ID('tempdb..##tempRatesAdvance') IS NOT NULL 
		DROP TABLE ##tempRatesAdvance

END
