/*
update aajpackets set 
updatedabstract = ltrim(right(abstract,len(abstract)-charindex(char(10),abstract,5)))
where abstract like '%order form)%' and updatedabstract is null

update aajpackets set 
updatedabstract = ltrim(right(updatedabstract,len(updatedabstract)-charindex(char(10),updatedabstract,5)))
where updatedabstract like '%order form)%'

update aajpackets set updatedabstract = right(updatedabstract,len(updatedabstract)-1) where left(updatedabstract,1) = char(10)
update aajpackets set updatedabstract = right(updatedabstract,len(updatedabstract)-1) where left(updatedabstract,1) = char(10)

update aajpackets set updatedabstract = replace(updatedabstract,char(10)+char(10),char(10))

select * from aajpackets where left(updatedabstract,1) = char(13)

select * from aajpackets where updatedabstract like '%' + char(10) + char(10) + '%'


select ascii(updatedabstract), * from aajpackets

update aajpackets 
set updatedabstract = abstract
where updatedabstract is null


select updatedabstract, replace(updatedabstract,char(13)+char(10)+char(13)+char(10),char(13)+char(10)) from aajpackets

*/


declare @documentTypeID int

select @documentTypeID=typeID
from trialsmith.dbo.depodocumenttypes where acctcode = '5020'

select DepomemberdataID, GroupID, DocumentDate, Availability, Notes, Summary, Category, State, DocumentTypeID, CaseTypeID, ExpertName, DateEntered, DateLastModified, disabled, keywords
from trialsmith.dbo.depodocuments
where documenttypeID=@documentTypeID

delete from trialsmith.dbo.depodocuments
where documenttypeID=@documentTypeID

insert into trialsmith.dbo.depodocuments (DepomemberdataID, GroupID, DocumentDate, Availability, Notes, Style, State, DocumentTypeID, CaseTypeID, ExpertName, DateEntered, DateLastModified, disabled, keywords)
select
	135600 as DepomemberdataID,
	0 as GroupID,
	date as DocumentDate,
	'I' as Availability,
	updatedabstract as Notes,
	'AAJ Record ID: ' + cast(recordID as varchar(25)) as style,
	'AA' as State,
	@documentTypeID as DocumentTypeID,
	52 as CaseTypeID,
	packetName as ExpertName,
	getdate() as DateEntered,
	getdate() as DateLastModified,
	'N' as disabled,
	keyword as keywords
from aajpackets