use membercentral
GO

update 
	ai 
set 
	ai.applicationInstanceName = 'Diversity & Inclusion Committee'
from 
	dbo.comm_communities c 
	inner join dbo.cms_applicationInstances ai on
		ai.applicationInstanceID = c.applicationInstanceID
	inner join sites s on
		s.siteID = ai.siteID
		and siteCode = 'nj'
where 
	c.communityName = 'Minority Concerns Committee'
GO

update 
	ai
set
	ai.applicationInstanceName = 'Diversity & Inclusion Committee'
from 
	comm_Communities comm
	inner join cms_applicationInstances ai on 
		ai.applicationInstanceID = comm.applicationInstanceID
		and comm.communityID = 710
	inner join cms_siteResources sr on 
		ai.siteResourceID = sr.siteResourceID
	inner join cms_siteResources pageResource on 
		sr.parentSiteResourceID = pageResource.siteResourceID
	inner join cms_siteResourceTypes srt on 
		pageResource.resourceTypeID = srt.resourceTypeID
	inner join sites s on 
		ai.siteID = s.siteID
	inner join organizations o on 
		s.orgID = o.orgID
	left outer join cms_siteResources thisCommunityGrandParentResource
		inner join cms_siteResourceTypes grandParentType on thisCommunityGrandParentResource.resourceTypeID = grandParentType.resourceTypeID
	on thisCommunityGrandParentResource.siteResourceID = pageResource.parentSiteResourceID

GO
