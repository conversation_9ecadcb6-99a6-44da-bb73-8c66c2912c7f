use platformstats
GO
truncate table dbo.apilog_cicero
GO
ALTER TABLE dbo.apilog_cicero ADD apiResult xml NOT NULL
GO

use membercentral
GO
TRUNCATE TABLE dbo.ams_memberAddressData
GO
DELETE FROM dbo.ams_memberDistrictValues
GO
/*
select * 
from ams_virtualGroupConditions
where left(fieldcode,4) = 'mad_'

EXEC dbo.ams_deleteVirtualGroupCondition 110, '13157'
GO
*/

use customApps
GO
TRUNCATE TABLE dbo.schedTask_districtMatching
GO

USE [memberCentral]
GO
ALTER PROC [dbo].[ams_saveMemberAddress]
@memberID int,
@addressTypeID int,
@attn varchar(100) = NULL,
@address1 varchar(100) = NULL,
@address2 varchar(100) = NULL,
@address3 varchar(100) = NULL,
@city varchar(35) = NULL,
@stateID int = NULL,
@postalCode varchar(25) = NULL,
@county varchar(50) = NULL,
@countryID int = NULL,
@addressID int OUTPUT

as

declare @changeMade bit
set @changeMade = 0

SELECT @addressID = null
SELECT @addressID = addressID FROM dbo.ams_memberAddresses where memberID = @memberid and addressTypeID = @addressTypeID

IF @addressID IS NOT NULL BEGIN
	-- get current info
	DECLARE @CURRattn varchar(100), @CURRaddress1 varchar(100), @CURRaddress2 varchar(100), 
		@CURRaddress3 varchar(100), @CURRcity varchar(35), @CURRstateID int, @CURRpostalCode varchar(25), 
		@CURRcounty varchar(50), @CURRcountryID int
	select @CURRattn = attn, @CURRaddress1 = address1, @CURRaddress2 = address2, @CURRaddress3 = address3,
		@CURRcity = city, @CURRstateID = stateID, @CURRpostalCode = postalCode, @CURRcounty = county,
		@CURRcountryID = countryID
	from dbo.ams_memberAddresses
	where addressID = @addressID

	-- if null parameters, use what the member currently has defined
	-- this allows only certain parts of addresses to be updated
	SET @attn = isNull(@attn,@CURRattn)
	SET @address1 = isNull(@address1,@CURRaddress1)
	SET @address2 = isNull(@address2,@CURRaddress2)
	SET @address3 = isNull(@address3,@CURRaddress3)
	SET @city = isNull(@city,@CURRcity)
	SET @stateID = isNull(@stateID,@CURRstateID)
	SET @postalCode = isNull(@postalCode,@CURRpostalCode)
	SET @county = isNull(@county,@CURRcounty)
	SET @countryID = isNull(@countryID,@CURRcountryID)

	UPDATE dbo.ams_memberAddresses
	SET attn = @attn, address1 = @address1, address2 = @address2, address3 = @address3, 
		city = @city, stateID = @stateID, postalCode = @postalCode, county = @county, 
		countryID = @countryID, dateLastUpdated = getdate()
	where addressID = @addressID
	AND NOT EXISTS (
		SELECT addressID
		FROM dbo.ams_memberAddresses 
		where addressID = @addressID
		AND IsNull(attn,'') COLLATE Latin1_General_CS_AS = IsNull(@attn,'')
		AND IsNull(address1,'') COLLATE Latin1_General_CS_AS = IsNull(@address1,'')
		AND IsNull(address2,'') COLLATE Latin1_General_CS_AS = IsNull(@address2,'')
		AND IsNull(address3,'') COLLATE Latin1_General_CS_AS = IsNull(@address3,'')
		AND IsNull(city,'') COLLATE Latin1_General_CS_AS = IsNull(@city,'')
		AND IsNull(stateID,-1) = IsNull(@stateID,-1)
		AND IsNull(postalCode,'') COLLATE Latin1_General_CS_AS = IsNull(@postalCode,'')
		AND IsNull(county,'') COLLATE Latin1_General_CS_AS = IsNull(@county,'')
		AND IsNull(countryID,-1) = IsNull(@countryID,-1)
	)

	IF @@ROWCOUNT > 0 	
		set @changeMade = 1

END ELSE BEGIN
	INSERT INTO dbo.ams_memberAddresses (memberID, addressTypeID, attn, address1, address2, 
		address3, city, stateID, postalCode, county, countryID)
	VALUES (@memberID, @addressTypeID, isnull(@attn,''), isnull(@address1,''), isnull(@address2,''),
		isnull(@address3,''), isnull(@city,''), @stateID, isnull(@postalCode,''),
		isnull(@county,''), @countryID)

	SELECT @addressID = SCOPE_IDENTITY()

	set @changeMade = 1
END

IF @changeMade = 1 BEGIN
	-- set member as updated
	update dbo.ams_members
	set dateLastUpdated = getdate()
	where memberID = @memberID

	-- delete any districting data tied to this address
	delete from dbo.ams_memberAddressData
	where addressID = @addressID

	-- queue processing of member groups
	insert into dbo.queue_processMemberGroups (orgID, memberID, conditionID)
	select c.orgID, @memberID, c.conditionID
	from dbo.ams_virtualGroupConditions as c
	inner join dbo.ams_memberAddressTypes as mat on mat.orgID = c.orgID 
		and mat.addressTypeID = @addressTypeID
	where c.fieldcode like 'ma\_' + cast(mat.addressTypeID as varchar(10)) + '\_%' ESCAPE('\')
	or c.fieldcode like 'mad\_' + cast(mat.addressTypeID as varchar(10)) + '\_%' ESCAPE('\')
END

return 0
GO

ALTER PROC [dbo].[ams_removeMemberAddressType]
@orgID int,
@addressTypeID int

AS

DECLARE @tblCond TABLE (conditionID int)
declare @minCID int, @rc int, @firstAddressTypeID int

-- ensure org matches addresstypeid
IF NOT EXISTS (select addressTypeID from dbo.ams_memberAddressTypes where orgID = @orgID AND addressTypeID = @addressTypeID)
	RETURN -1

-- get org's first address typeid
select @firstAddressTypeID = addressTypeID from dbo.ams_memberAddressTypes where orgID = @orgID and addressTypeOrder = 1

BEGIN TRAN			

-- update billing address tid
update dbo.ams_members
set billingAddressTypeID = @firstAddressTypeID
where orgID = @orgID
and billingAddressTypeID = @addressTypeID
	IF @@ERROR <> 0 goto on_error

-- phones
DELETE FROM dbo.ams_memberPhones
WHERE addressID in (
	select addressID
	FROM dbo.ams_memberAddresses
	WHERE addressTypeID = @addressTypeID
)
	IF @@ERROR <> 0 goto on_error

-- address data
DELETE FROM dbo.ams_memberAddressData
WHERE addressID in (
	select addressID
	FROM dbo.ams_memberAddresses
	WHERE addressTypeID = @addressTypeID
)
	IF @@ERROR <> 0 goto on_error

-- addresses
DELETE FROM dbo.ams_memberAddresses
WHERE addressTypeID = @addressTypeID
	IF @@ERROR <> 0 goto on_error

-- member fields
DELETE FROM dbo.ams_memberFields 
WHERE (
	fieldCode like 'ma\_' + cast(@addressTypeID as varchar(10)) + '\_%' escape '\'
	or
	fieldCode like 'mad\_' + cast(@addressTypeID as varchar(10)) + '\_%' escape '\'
)
and fieldsetID in (
	select fieldSetID 
	from dbo.ams_memberFieldSets as mfs 
	inner join dbo.sites as s on s.siteid = mfs.siteid and s.orgid = @orgid
)
	IF @@ERROR <> 0 goto on_error

-- group assignment conditions
INSERT INTO @tblCond (conditionID) 
SELECT conditionID 
FROM dbo.ams_virtualGroupConditions 
WHERE orgID = @orgID 
AND (
	fieldcode like 'ma\_' + cast(@addressTypeID as varchar(10)) + '\_%' escape '\'
	or
	fieldcode like 'mad\_' + cast(@addressTypeID as varchar(10)) + '\_%' escape '\'
)
	IF @@ERROR <> 0 goto on_error

declare @CIDList varchar(max)
select @CIDList = COALESCE(@CIDList + ',', '') + cast(conditionID as varchar(10)) from @tblCond group by conditionID
IF @CIDList is not null BEGIN
	EXEC @rc = dbo.ams_deleteVirtualGroupCondition @orgID=@orgID, @conditionIDList=@CIDList
		IF @@ERROR <> 0 OR @RC <> 0 goto on_error
END

-- address type
DELETE FROM dbo.ams_memberAddressTypes
WHERE addressTypeID = @addressTypeID
	IF @@ERROR <> 0 goto on_error

-- reorder address types
EXEC dbo.ams_reorderMemberAddressTypes @orgID
	IF @@ERROR <> 0 goto on_error

IF @@TRANCOUNT > 0 COMMIT TRAN
EXEC dbo.ams_createVWMemberData @orgID=@orgID
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

ALTER PROC [dbo].[ams_importMemberData_holdingToPerm]
@orgID int,
@flatfile varchar(160)

AS

SET NOCOUNT ON

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Starting importMemberData_toPerm'

declare @stepNum tinyint
declare @returnVal int
declare @cmd varchar(400), @sql varchar(max), @cmd2 varchar(8000), @createSQL varchar(max)
declare @logTable TABLE (stepNum tinyint, stepMsg varchar(100), stepDate datetime)
DECLARE @orgCode varchar(10), @hasPrefix bit, @hasMiddleName bit, @hasSuffix bit, @hasProfessionalSuffix bit, @hasCompany bit
select @orgcode=orgcode, @hasPrefix=hasPrefix, @hasMiddleName=hasMiddleName,
	@hasSuffix=hasSuffix, @hasProfessionalSuffix=hasProfessionalSuffix, 
	@hasCompany=hasCompany
	from dbo.organizations 
	where orgID = @orgid

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Preparing to Import Flattened Data'

-- see if files exist
if dbo.fn_fileExists(@flatfile + '.sql') = 0
	or dbo.fn_fileExists(@flatfile + '.txt') = 0
	or dbo.fn_fileExists(@flatfile + '.bcp') = 0
	BEGIN
		EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Flattened Data files do not exist'
		RETURN -1
	END

-- **************
-- read in sql create script and create global temp table
-- **************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Creating table for flattened data'
select @createSQL = replace(dbo.fn_ReadFile(@flatfile + '.sql',0,1),'##xxx','##importMemberData')
IF OBJECT_ID('tempdb..##importMemberData') IS NOT NULL
	EXEC('DROP TABLE ##importMemberData')
EXEC(@createSQL)

-- *******************
-- bcp in data
-- *******************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Importing flattened data'
select @cmd = 'bcp ##importMemberData in ' + @flatfile + '.bcp -f ' + @flatfile + '.txt -n -T -S' + CAST(serverproperty('servername') as varchar(40))
exec master..xp_cmdshell @cmd, NO_OUTPUT

-- *******************
-- immediately put into local temp table and drop global temp
-- *******************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Importing into local temp table'
select * into #importMemberData from ##importMemberData
IF OBJECT_ID('tempdb..##importMemberData') IS NOT NULL
	EXEC('DROP TABLE ##importMemberData')

-- *******************
-- drop all columns that should be skipped upon import
-- *******************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Dropping Columns to be Skipped'
select @sql = null
select @sql = COALESCE(@sql + char(10), '') + 'IF EXISTS (select * from tempdb.sys.columns where name = ''' + columnName + ''' and object_id = object_id(''tempdb..#importMemberData'')) ALTER TABLE #importMemberData DROP COLUMN [' + columnName + ']'
	from dbo.ams_memberDataColumns
	where orgID = @orgID
	and skipImport = 1
IF LEN(@sql) > 0 
	EXEC(@sql)

-- *******************
-- add back the columns we dont use from members (sigh) so the queries dont break
-- *******************
IF @hasPrefix = 0 AND NOT EXISTS (select [name] from tempdb.sys.columns where name = 'prefix' and object_id = object_id('tempdb..#importMemberData')) 
	ALTER TABLE #importMemberData ADD prefix varchar(4) NOT NULL DEFAULT('');
IF @hasMiddleName = 0 AND NOT EXISTS (select [name] from tempdb.sys.columns where name = 'middlename' and object_id = object_id('tempdb..#importMemberData')) 
	ALTER TABLE #importMemberData ADD middlename varchar(4) NOT NULL DEFAULT('');
IF @hasSuffix = 0 AND NOT EXISTS (select [name] from tempdb.sys.columns where name = 'suffix' and object_id = object_id('tempdb..#importMemberData')) 
	ALTER TABLE #importMemberData ADD suffix varchar(4) NOT NULL DEFAULT('');
IF @hasProfessionalSuffix = 0 AND NOT EXISTS (select [name] from tempdb.sys.columns where name = 'ProfessionalSuffix' and object_id = object_id('tempdb..#importMemberData')) 
	ALTER TABLE #importMemberData ADD ProfessionalSuffix varchar(4) NOT NULL DEFAULT('');
IF @hasCompany = 0 AND NOT EXISTS (select [name] from tempdb.sys.columns where name = 'Company' and object_id = object_id('tempdb..#importMemberData')) 
	ALTER TABLE #importMemberData ADD Company varchar(4) NOT NULL DEFAULT('');

-- ensure no null values in data. saves us from having to blank if null inline.
update #importMemberData set prefix = '' where prefix is null
update #importMemberData set middlename = '' where middlename is null
update #importMemberData set suffix = '' where suffix is null
update #importMemberData set professionalsuffix = '' where professionalsuffix is null
update #importMemberData set company = '' where company is null

-- Add index for queries below
EXEC('CREATE NONCLUSTERED INDEX [idx_importMemberData_' + @orgcode + '] ON #importMemberData ([membernumber] ASC) INCLUDE ( [prefix],[firstname],[middlename],[lastname],[suffix],[company],[ProfessionalSuffix]); ')
EXEC('CREATE STATISTICS [stat_importMemberData_' + @orgcode + '] ON #importMemberData([prefix]); ')

-- *******************
-- bcp out copy of current data 
-- have to do it this way since bcp cannot queryout dynamic sql queries
-- *******************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Exporting backup of existing data'
EXEC dbo.ams_getFlattenedMemberDataSQL @orgID=@orgID, @limitTop=0, @limitActive=0, @limitMID='', @showMCFields=1, @showSkippedFields=1, @sql=@sql OUTPUT
select @cmd2 = 'bcp "SET QUOTED_IDENTIFIER ON; ' + dbo.fn_ConsolidateWhiteSpace(@sql) + '" queryout ' + replace(@flatfile,'_flat_','_export_') + '.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
exec master..xp_cmdshell @cmd2, NO_OUTPUT

-- *******************
-- process data
-- *******************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Processing flattened data'
SELECT @stepNum = max(stepNum)
	from datatransfer.dbo.ams_memberDataImportStatus
	where orgID = @orgID

BEGIN TRAN

	-- Inactivate all non-guest members (the import contains only active people)
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Inactivate non-guest accounts',getdate())

	UPDATE dbo.ams_members
	SET [status] = 'I'
	WHERE orgID = @orgID
	AND membertypeID = 2
	AND memberID = activeMemberID
	AND [status] = 'A'
		IF @@ERROR <> 0 GOTO on_error

	-- update non-guest members already in table based on membernumber
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Update existing non-guest accounts',getdate())

	UPDATE m
	set m.prefix = flat.prefix,
		m.firstname = flat.firstname,
		m.middlename = flat.middlename,
		m.lastname = flat.lastname,
		m.suffix = flat.suffix,
		m.professionalsuffix = flat.professionalsuffix,
		m.company = flat.company,
		m.memberNumber = flat.membernumber,
		m.status = 'A'
	FROM dbo.ams_members as m 
	INNER JOIN #importMemberData as flat on flat.memberNumber = m.memberNumber
		AND m.orgID = @orgID
		AND m.membertypeID = 2
		AND m.memberID = m.activeMemberID
		AND m.status <> 'D'
		IF @@ERROR <> 0 GOTO on_error

	-- add new non-guest members
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Inserting new non-guest accounts',getdate())

	-- get first address type for the billing ATID
	declare @billingaddressTypeID int
	select @billingaddressTypeID = addressTypeID from dbo.ams_memberAddressTypes where orgID = @orgID and addressTypeOrder = 1

	INSERT INTO dbo.ams_members (orgID, prefix, firstname, middlename, lastname, suffix, professionalsuffix, company, memberNumber, [status], dateCreated, membertypeID, dateLastUpdated, billingAddressTypeID)
	SELECT @orgID, flat.prefix, flat.Firstname, flat.middlename, flat.LastName, flat.suffix, flat.professionalsuffix, flat.company, flat.membernumber, 'A', getdate(), 2, getdate(), @billingaddressTypeID
	from #importMemberData as flat
	WHERE NOT EXISTS(
		select memberNumber 
		from dbo.ams_members as m 
		where m.memberNumber = flat.memberNumber 
		and m.orgID = @orgID
		and m.memberid = m.activeMemberID 
		and m.membertypeID = 2
		AND m.status <> 'D'
	)
	ORDER BY flat.membernumber
		IF @@ERROR <> 0 GOTO on_error
	UPDATE dbo.ams_members 
	SET activeMemberID = memberID 
	WHERE orgid=@orgid 
	and activeMemberID is null 
		IF @@ERROR <> 0 GOTO on_error

	-- *******
	-- websites
	-- loop over website types
	-- update website if it exists for member
	-- add website if it doesnt already exist for member
	-- *******
	declare @minWTID int, @minWT varchar(20), @wtsql varchar(max)
	select @minWTID = min(websiteTypeID) from dbo.ams_memberWebsiteTypes where orgID = @orgID
	while @minWTID is not null BEGIN
		select @minWT = websiteType from dbo.ams_memberWebsiteTypes where websiteTypeID = @minWTID

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Updating existing member websites',getdate())

		select @wtsql = ''
		select @wtsql = @wtsql + 'UPDATE mw '
		select @wtsql = @wtsql + 'SET mw.website = isnull(flat.[' + @minWT + '],'''') '
		select @wtsql = @wtsql + 'FROM dbo.ams_memberWebsites as mw '
		select @wtsql = @wtsql + 'inner join dbo.ams_members as m on m.memberID = mw.memberID '
		select @wtsql = @wtsql + '	AND m.orgID = ' + cast(@orgid as varchar(10)) + ' '
		select @wtsql = @wtsql + '	AND m.memberID = m.activeMemberID '
		select @wtsql = @wtsql + '	AND m.membertypeID = 2 '
		select @wtsql = @wtsql + '	AND m.status <> ''D'' '
		select @wtsql = @wtsql + '  AND mw.websiteTypeID = ' + cast(@minWTID as varchar(10)) + ' '
		select @wtsql = @wtsql + 'INNER JOIN #importMemberData as flat on flat.memberNumber = m.memberNumber '
		EXEC(@wtsql)
		IF @@ERROR <> 0 GOTO on_error

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Inserting new member websites',getdate())

		select @wtsql = ''
		select @wtsql = @wtsql + 'INSERT INTO dbo.ams_memberWebsites (memberID, websiteTypeID, website) '
		select @wtsql = @wtsql + 'select m.memberid, ' + cast(@minWTID as varchar(10)) + ', isnull(flat.[' + @minWT + '],'''') '
		select @wtsql = @wtsql + 'from #importMemberData as flat '
		select @wtsql = @wtsql + 'inner join dbo.ams_members as m on m.membernumber = flat.membernumber '
		select @wtsql = @wtsql + '	and m.orgid = ' + cast(@orgid as varchar(10)) + ' '
		select @wtsql = @wtsql + '	and m.memberID = m.activeMemberID '
		select @wtsql = @wtsql + '	and m.membertypeID = 2 '
		select @wtsql = @wtsql + '	AND m.status <> ''D'' '
		select @wtsql = @wtsql + 'WHERE NOT EXISTS( '
		select @wtsql = @wtsql + '	select websiteID '
		select @wtsql = @wtsql + '	from dbo.ams_memberWebsites as mw '
		select @wtsql = @wtsql + '	where mw.websiteTypeID = ' + cast(@minWTID as varchar(10)) + ' '
		select @wtsql = @wtsql + '	and mw.memberID = m.memberID '
		select @wtsql = @wtsql + ') '
		EXEC(@wtsql)
		IF @@ERROR <> 0 GOTO on_error

		select @minWTID = min(websiteTypeID) from dbo.ams_memberWebsiteTypes where orgID = @orgID and websiteTypeID > @minWTID
	END

	-- *******
	-- emails
	-- loop over email types
	-- update email if it exists for member
	-- add email if it doesnt already exist for member
	-- *******
	declare @minETID int, @minET varchar(20), @etsql varchar(max)
	select @minETID = min(emailTypeID) from dbo.ams_memberEmailTypes where orgID = @orgID
	while @minETID is not null BEGIN
		select @minET = emailType from dbo.ams_memberEmailTypes where emailTypeID = @minETID

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Updating existing member emails',getdate())

		select @etsql = ''
		select @etsql = @etsql + 'UPDATE me '
		select @etsql = @etsql + 'SET me.email = isnull(flat.[' + @minET + '],'''') '
		select @etsql = @etsql + 'FROM dbo.ams_memberEmails as me '
		select @etsql = @etsql + 'inner join dbo.ams_members as m on m.memberID = me.memberID '
		select @etsql = @etsql + '	AND m.orgID = ' + cast(@orgid as varchar(10)) + ' '
		select @etsql = @etsql + '	AND m.memberID = m.activeMemberID '
		select @etsql = @etsql + '	AND m.membertypeID = 2 '
		select @etsql = @etsql + '	AND m.status <> ''D'' '
		select @etsql = @etsql + '  AND me.emailTypeID = ' + cast(@minETID as varchar(10)) + ' '
		select @etsql = @etsql + 'INNER JOIN #importMemberData as flat on flat.memberNumber = m.memberNumber '
		EXEC(@etsql)
		IF @@ERROR <> 0 GOTO on_error

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Inserting new member emails',getdate())

		select @etsql = ''
		select @etsql = @etsql + 'INSERT INTO dbo.ams_memberEmails (memberID, emailTypeID, email) '
		select @etsql = @etsql + 'select m.memberid, ' + cast(@minETID as varchar(10)) + ', isnull(flat.[' + @minET + '],'''') '
		select @etsql = @etsql + 'from #importMemberData as flat '
		select @etsql = @etsql + 'inner join dbo.ams_members as m on m.membernumber = flat.membernumber '
		select @etsql = @etsql + '	and m.orgid = ' + cast(@orgid as varchar(10)) + ' '
		select @etsql = @etsql + '	and m.memberID = m.activeMemberID '
		select @etsql = @etsql + '	and m.membertypeID = 2 '
		select @etsql = @etsql + '	AND m.status <> ''D'' '
		select @etsql = @etsql + 'WHERE NOT EXISTS( '
		select @etsql = @etsql + '	select emailID '
		select @etsql = @etsql + '	from dbo.ams_memberEmails as me '
		select @etsql = @etsql + '	where me.emailTypeID = ' + cast(@minETID as varchar(10)) + ' '
		select @etsql = @etsql + '	and me.memberID = m.memberID '
		select @etsql = @etsql + ') '
		EXEC(@etsql)
		IF @@ERROR <> 0 GOTO on_error

		select @minETID = min(emailTypeID) from dbo.ams_memberEmailTypes where orgID = @orgID and emailTypeID > @minETID
	END

	-- *******
	-- addresses and phones
	-- loop over address types (and then phone types)
	-- update address/phone if it exists for member
	-- add address/phone if it doesnt already exist for member
	-- *******
	declare @minATID int, @minAT varchar(20), @atsql varchar(max), @minPTID int, @minPT varchar(20)
	declare @hasAttn bit, @hasAddress2 bit, @hasAddress3 bit, @hasCounty bit
	declare @updateAddressDate varchar(25)
	select @minATID = min(addressTypeID) from dbo.ams_memberAddressTypes where orgID = @orgID
	while @minATID is not null BEGIN
		select @minAT=addressType, @hasAttn=hasAttn, @hasAddress2=hasAddress2, @hasAddress3=hasAddress3, @hasCounty=hasCounty
			from dbo.ams_memberAddressTypes 
			where addressTypeID = @minATID

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Updating existing member addresses',getdate())

		select @updateAddressDate = convert(varchar(30),getdate(),21)

		select @atsql = ''
		select @atsql = @atsql + 'UPDATE ma '
		select @atsql = @atsql + 'SET '
		IF @hasAttn = 1
			select @atsql = @atsql + '  ma.attn = isnull(flat.[' + @minAT + '_attn],''''), '
		select @atsql = @atsql + '	ma.address1 = isnull(flat.[' + @minAT + '_address1],''''), '
		IF @hasAddress2 = 1		
			select @atsql = @atsql + '	ma.address2 = isnull(flat.[' + @minAT + '_address2],''''), '
		IF @hasAddress3 = 1		
			select @atsql = @atsql + '	ma.address3 = isnull(flat.[' + @minAT + '_address3],''''), '
		select @atsql = @atsql + '	ma.city = isnull(flat.[' + @minAT + '_city],''''), '
		select @atsql = @atsql + '	ma.stateID = s.stateID, '
		select @atsql = @atsql + '	ma.postalCode = isnull(flat.[' + @minAT + '_postalcode],''''), '
		IF @hasCounty = 1		
			select @atsql = @atsql + '	ma.county = isnull(flat.[' + @minAT + '_county],''''), '
		select @atsql = @atsql + '	ma.countryID = c.countryID, '
		select @atsql = @atsql + '	ma.dateLastUpdated = ''' + @updateAddressDate + ''' '
		select @atsql = @atsql + 'FROM dbo.ams_memberAddresses as ma '
		select @atsql = @atsql + 'inner join dbo.ams_members as m on m.memberID = ma.memberID '
		select @atsql = @atsql + '	AND m.orgID = ' + cast(@orgid as varchar(10)) + ' '
		select @atsql = @atsql + '	AND m.memberID = m.activeMemberID '
		select @atsql = @atsql + '	AND m.membertypeID = 2 '
		select @atsql = @atsql + '	AND m.status <> ''D'' '
		select @atsql = @atsql + '  AND ma.addressTypeID = ' + cast(@minATID as varchar(10)) + ' '
		select @atsql = @atsql + 'INNER JOIN #importMemberData as flat on flat.memberNumber = m.memberNumber '
		select @atsql = @atsql + 'left outer join dbo.ams_countries as c on c.country = isnull(flat.[' + @minAT + '_country],'''') '
		select @atsql = @atsql + 'left outer join dbo.ams_states as s on s.code = isnull(flat.[' + @minAT + '_stateprov],'''') and s.countryID = c.countryID '
		select @atsql = @atsql + 'WHERE NOT EXISTS ( '
		select @atsql = @atsql + '		SELECT addressID '
		select @atsql = @atsql + '		FROM dbo.ams_memberAddresses '
		select @atsql = @atsql + '		where addressID = ma.addressID '
		IF @hasAttn = 1
			select @atsql = @atsql + '		AND IsNull(attn,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_attn],'''') '
		select @atsql = @atsql + '		AND IsNull(address1,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_address1],'''') '
		IF @hasAddress2 = 1		
			select @atsql = @atsql + '		AND IsNull(address2,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_address2],'''') '
		IF @hasAddress3 = 1		
			select @atsql = @atsql + '		AND IsNull(address3,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_address3],'''') '
		select @atsql = @atsql + '		AND IsNull(city,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_city],'''') '
		select @atsql = @atsql + '		AND IsNull(stateID,-1) = IsNull(s.stateID,-1) '
		select @atsql = @atsql + '		AND IsNull(postalCode,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_postalcode],'''') '
		IF @hasCounty = 1		
			select @atsql = @atsql + '		AND IsNull(county,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_county],'''') '
		select @atsql = @atsql + '		AND IsNull(countryID,-1) = IsNull(c.countryID,-1) '
		select @atsql = @atsql + ') '
		EXEC(@atsql)
		IF @@ERROR <> 0 GOTO on_error
		
		-- clear address data for addresses we just updated
		delete from dbo.ams_memberAddressData
		where addressID in (
			select ma.addressID
			from dbo.ams_memberAddresses as ma
			inner join dbo.ams_members as m on m.memberID = ma.memberID
			where m.orgID = @orgID
			and ma.dateLastUpdated = @updateAddressDate
		)
		IF @@ERROR <> 0 GOTO on_error

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Inserting new member addresses',getdate())

		select @atsql = ''
		select @atsql = @atsql + 'INSERT INTO dbo.ams_memberAddresses (memberID, addressTypeID, attn, address1, address2, address3, city, stateID, postalCode, county, countryID) '
		select @atsql = @atsql + 'select m.memberid, ' + cast(@minATID as varchar(10)) + ', ' + case when @hasAttn = 1 then 'isnull(flat.[' + @minAT + '_attn],''''), ' else ''''', ' end + 'isnull(flat.[' + @minAT + '_address1],''''), ' + case when @hasAddress2 = 1 then 'isnull(flat.[' + @minAT + '_address2],''''), ' else ''''', ' end + case when @hasAddress3 = 1 then 'isnull(flat.[' + @minAT + '_address3],''''), ' else ''''', ' end + 'isnull(flat.[' + @minAT + '_city],''''), s.stateID, isnull(flat.[' + @minAT + '_postalcode],''''), ' + case when @hasCounty = 1 then 'isnull(flat.[' + @minAT + '_county],''''), ' else ''''', ' end + 'c.countryID '
		select @atsql = @atsql + 'from #importMemberData as flat '
		select @atsql = @atsql + 'inner join dbo.ams_members as m on m.membernumber = flat.membernumber '
		select @atsql = @atsql + '	and m.orgid = ' + cast(@orgid as varchar(10)) + ' '
		select @atsql = @atsql + '	and m.memberID = m.activeMemberID '
		select @atsql = @atsql + '	and m.membertypeID = 2 '
		select @atsql = @atsql + '	AND m.status <> ''D'' '
		select @atsql = @atsql + 'left outer join dbo.ams_countries as c on c.country = isnull(flat.[' + @minAT + '_country],'''') '
		select @atsql = @atsql + 'left outer join dbo.ams_states as s on s.code = isnull(flat.[' + @minAT + '_stateprov],'''') and s.countryID = c.countryID '
		select @atsql = @atsql + 'WHERE NOT EXISTS( '
		select @atsql = @atsql + '	select addressID '
		select @atsql = @atsql + '	from dbo.ams_memberAddresses as ma '
		select @atsql = @atsql + '	where ma.addressTypeID = ' + cast(@minATID as varchar(10)) + ' '
		select @atsql = @atsql + '	and ma.memberID = m.memberID '
		select @atsql = @atsql + ') '
		EXEC(@atsql)
		IF @@ERROR <> 0 GOTO on_error

		select @minPTID = min(phoneTypeID) from dbo.ams_memberPhoneTypes where orgID = @orgID
		while @minPTID is not null BEGIN
			select @minPT = phoneType from dbo.ams_memberPhoneTypes where phoneTypeID = @minPTID

			SELECT @stepNum = @stepNum + 1
			INSERT INTO @logTable (stepNum,stepMsg,stepDate)
			VALUES (@stepNum,'Updating existing member phones',getdate())

			select @atsql = ''
			select @atsql = @atsql + 'UPDATE mp '
			select @atsql = @atsql + 'SET phone = isnull(flat.[' + @minAT + '_' + @minPT + '],'''') '
			select @atsql = @atsql + 'FROM dbo.ams_memberPhones as mp '
			select @atsql = @atsql + 'inner join dbo.ams_memberAddresses as ma on ma.addressID = mp.addressid '
			select @atsql = @atsql + '	and ma.addressTypeID = ' + cast(@minATID as varchar(10)) + ' '
			select @atsql = @atsql + '  and mp.phoneTypeID = ' + cast(@minPTID as varchar(10)) + ' '
			select @atsql = @atsql + 'inner join dbo.ams_members as m on m.memberID = ma.memberID '
			select @atsql = @atsql + '	AND m.orgID = ' + cast(@orgid as varchar(10)) + ' '
			select @atsql = @atsql + '	AND m.memberID = m.activeMemberID '
			select @atsql = @atsql + '	AND m.membertypeID = 2 '
			select @atsql = @atsql + '	AND m.status <> ''D'' '
			select @atsql = @atsql + 'INNER JOIN #importMemberData as flat on flat.memberNumber = m.memberNumber '
			EXEC(@atsql)
			IF @@ERROR <> 0 GOTO on_error

			SELECT @stepNum = @stepNum + 1
			INSERT INTO @logTable (stepNum,stepMsg,stepDate)
			VALUES (@stepNum,'Inserting new member phones',getdate())

			select @atsql = ''
			select @atsql = @atsql + 'INSERT INTO dbo.ams_memberPhones (phoneTypeID, addressid, phone) '
			select @atsql = @atsql + 'select ' + cast(@minPTID as varchar(10)) + ', ma.addressid, isnull(flat.[' + @minAT + '_' + @minPT + '],'''') '
			select @atsql = @atsql + 'from #importMemberData as flat '
			select @atsql = @atsql + 'inner join dbo.ams_members as m on m.membernumber = flat.membernumber '
			select @atsql = @atsql + '	and m.orgid = ' + cast(@orgid as varchar(10)) + ' '
			select @atsql = @atsql + '	and m.memberID = m.activeMemberID '
			select @atsql = @atsql + '	and m.membertypeID = 2 '
			select @atsql = @atsql + '	AND m.status <> ''D'' '
			select @atsql = @atsql + 'inner join dbo.ams_memberAddresses as ma on ma.memberid = m.memberid '
			select @atsql = @atsql + '	and ma.addressTypeID = ' + cast(@minATID as varchar(10)) + ' '
			select @atsql = @atsql + 'WHERE NOT EXISTS( '
			select @atsql = @atsql + '	select phoneID '
			select @atsql = @atsql + '	from dbo.ams_memberPhones as mp '
			select @atsql = @atsql + '	where mp.phoneTypeID = ' + cast(@minPTID as varchar(10)) + ' '
			select @atsql = @atsql + '	and mp.addressID = ma.addressID '
			select @atsql = @atsql + ') '
			EXEC(@atsql)
			IF @@ERROR <> 0 GOTO on_error

			select @minPTID = min(phoneTypeID) from dbo.ams_memberPhoneTypes where orgID = @orgID and phoneTypeID > @minPTID
		END

		select @minATID = min(addressTypeID) from dbo.ams_memberAddressTypes where orgID = @orgID and addressTypeID > @minATID
	END

	-- *******
	-- professional licenses
	-- loop over prof license types
	-- update prof license if it exists for member
	-- add prof license if it doesnt already exist for member
	-- *******
	declare @minPLTID int, @minPLT varchar(200), @pltsql varchar(max)
	select @minPLTID = min(PLTypeID) from dbo.ams_memberProfessionalLicenseTypes where orgID = @orgID
	while @minPLTID is not null BEGIN
		select @minPLT=PLName
			from dbo.ams_memberProfessionalLicenseTypes 
			where PLTypeID = @minPLTID

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Updating existing member professional licenses',getdate())

		select @pltsql = ''
		select @pltsql = @pltsql + 'UPDATE mpl '
		select @pltsql = @pltsql + 'SET '
		select @pltsql = @pltsql + '	mpl.licenseNumber = isnull(flat.[' + @minPLT + '_licenseNumber],''''), '
		select @pltsql = @pltsql + '	mpl.activeDate = nullif(flat.[' + @minPLT + '_activeDate],''''), '
		select @pltsql = @pltsql + '	mpl.PLstatusID = pls.PLStatusID '
		select @pltsql = @pltsql + 'FROM dbo.ams_memberProfessionalLicenses as mpl '
		select @pltsql = @pltsql + 'inner join dbo.ams_members as m on m.memberID = mpl.memberID '
		select @pltsql = @pltsql + '	AND m.orgID = ' + cast(@orgid as varchar(10)) + ' '
		select @pltsql = @pltsql + '	AND m.memberID = m.activeMemberID '
		select @pltsql = @pltsql + '	AND m.membertypeID = 2 '
		select @pltsql = @pltsql + '	AND m.status <> ''D'' '
		select @pltsql = @pltsql + '  AND mpl.PLTypeID = ' + cast(@minPLTID as varchar(10)) + ' '
		select @pltsql = @pltsql + 'INNER JOIN #importMemberData as flat on flat.memberNumber = m.memberNumber '
		select @pltsql = @pltsql + 'LEFT OUTER JOIN dbo.ams_memberProfessionalLicenseStatuses as pls on pls.statusName = isnull(flat.[' + @minPLT + '_status],'''') and pls.orgID = ' + cast(@orgid as varchar(10))
		EXEC(@pltsql)
		IF @@ERROR <> 0 GOTO on_error
		
		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Inserting new member professional licenses',getdate())

		select @pltsql = ''
		select @pltsql = @pltsql + 'INSERT INTO dbo.ams_memberProfessionalLicenses (memberID, PLTypeID, LicenseNumber, ActiveDate, PLstatusID) '
		select @pltsql = @pltsql + 'select m.memberid, ' + cast(@minPLTID as varchar(10)) + ', isnull(flat.[' + @minPLT + '_licenseNumber],''''), nullif(flat.[' + @minPLT + '_activeDate],''''), pls.PLStatusID '
		select @pltsql = @pltsql + 'from #importMemberData as flat '
		select @pltsql = @pltsql + 'LEFT OUTER JOIN dbo.ams_memberProfessionalLicenseStatuses as pls on pls.statusName = isnull(flat.[' + @minPLT + '_status],'''') and pls.orgID = ' + cast(@orgid as varchar(10))
		select @pltsql = @pltsql + 'inner join dbo.ams_members as m on m.membernumber = flat.membernumber '
		select @pltsql = @pltsql + '	and m.orgid = ' + cast(@orgid as varchar(10)) + ' '
		select @pltsql = @pltsql + '	and m.memberID = m.activeMemberID '
		select @pltsql = @pltsql + '	and m.membertypeID = 2 '
		select @pltsql = @pltsql + '	AND m.status <> ''D'' '
		select @pltsql = @pltsql + 'WHERE NOT EXISTS( '
		select @pltsql = @pltsql + '	select PLID '
		select @pltsql = @pltsql + '	from dbo.ams_memberProfessionalLicenses as mpl '
		select @pltsql = @pltsql + '	where mpl.PLTypeID = ' + cast(@minPLTID as varchar(10)) + ' '
		select @pltsql = @pltsql + '	and mpl.memberID = m.memberID '
		select @pltsql = @pltsql + ')  '
		select @pltsql = @pltsql + 'AND ( '
		select @pltsql = @pltsql + '	nullIf(flat.[' + @minPLT + '_licenseNumber],'''') is not null OR '
		select @pltsql = @pltsql + '	nullif(flat.[' + @minPLT + '_activeDate],'''') is not null OR '
		select @pltsql = @pltsql + '	nullIf(flat.[' + @minPLT + '_status],'''') is not null '
		select @pltsql = @pltsql + ')  '

		EXEC(@pltsql)
		IF @@ERROR <> 0 GOTO on_error

		select @minPLTID = min(PLTypeID) from dbo.ams_memberProfessionalLicenseTypes where orgID = @orgID and PLTypeID > @minPLTID
	END

	-- delete where everything is null (could have been updated to null values above)
	delete
	from dbo.ams_memberProfessionalLicenses
	where nullif(licenseNumber,'') is null
	and activedate is null
	and PlStatusID is null
		IF @@ERROR <> 0 GOTO on_error
	
	-- ***********
	-- memberdata
	-- figure out what should be in memberdata
	-- delete what shouldnt be but is
	-- add what should be but isnt
	-- ***********
	
	-- clear memberdataholding for org
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Cleaning up holding table',getdate())

	DELETE FROM datatransfer.dbo.ams_memberDataHolding where orgID = @orgID
		IF @@ERROR <> 0 GOTO on_error

	-- traverse each column and put into holding table
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Inserting custom data into holding table',getdate())

	DECLARE @minCol varchar(255), @minColID int, @coldataTypeCode varchar(20), @allowMultiple bit, @colqry varchar(max)
	SELECT @minColID = MIN(mdc.columnID) 
		FROM dbo.ams_memberDataColumns as mdc
		where mdc.orgID = @orgid
		and mdc.skipImport = 0
	WHILE @minColID is not null BEGIN
		select @minCol=mdc.columnName, @coldataTypeCode=dt.dataTypeCode, @allowMultiple=mdc.allowMultiple
			from dbo.ams_memberDataColumns as mdc
			inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			where mdc.columnID = @minColID

		IF EXISTS (select * from tempdb.sys.columns where name = @minCol and object_id = object_id('tempdb..#importMemberData')) BEGIN
			SELECT @colqry = 'insert into datatransfer.dbo.ams_memberDataHolding (orgID, rowID, columnID, ' + 
				case @coldataTypeCode 
				when 'STRING' then 'columnValueString'
				when 'DECIMAL2' then 'columnValueDecimal2'
				when 'INTEGER' then 'columnValueInteger'
				when 'DATE' then 'columnValueDate'
				when 'BIT' then 'columnValueBit'
				when 'XML' then 'columnValueXML'
				when 'CONTENTOBJ' then 'columnValueSiteResourceID'
				when 'DOCUMENTOBJ' then 'columnValueSiteResourceID'
				else 'columnValueString'
				end + ') '
			SELECT @colqry = @colqry + 'select ' + cast(@orgID as varchar(6)) + ', rowID, ' + cast(@minColID as varchar(12)) + ', ' + 
				case when @allowMultiple = 1 then 'tbl.listitem'
				else 
					case @coldataTypeCode 
					when 'STRING' then quotename(@minCol)
					when 'DECIMAL2' then 'cast(' + quotename(@minCol) + ' as decimal(9,2))'
					when 'INTEGER' then 'cast(' + quotename(@minCol) + ' as int)'
					when 'DATE' then 'cast(' + quotename(@minCol) + ' as datetime)'
					when 'BIT' then 'cast(' + quotename(@minCol) + ' as bit)'
					when 'XML' then 'cast(' + quotename(@minCol) + ' as xml)'
					when 'CONTENTOBJ' then 'cast(' + quotename(@minCol) + ' as int)'
					when 'DOCUMENTOBJ' then 'cast(' + quotename(@minCol) + ' as int)'
					else quotename(@minCol)
					end
				end + ' '
			SELECT @colqry = @colqry + 'from #importMemberData '
			IF @allowMultiple = 1 BEGIN
				SELECT @colqry = @colqry + 'cross apply ' + 
					case @coldataTypeCode 
					when 'STRING' then 'dbo.fn_varcharListToTable(' + quotename(@minCol) + ',''|'') as tbl '
					when 'DECIMAL2' then 'dbo.fn_decimal2ListToTable(' + quotename(@minCol) + ',''|'') as tbl '
					when 'INTEGER' then 'dbo.fn_intListToTable(' + quotename(@minCol) + ',''|'') as tbl '
					end + ' '
			END
			SELECT @colqry = @colqry + 'where ' + 
				case @coldataTypeCode 
				when 'STRING' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(255)),'''') is not null'
				when 'DECIMAL2' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(20)),'''') is not null'
				when 'INTEGER' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(20)),'''') is not null'
				when 'DATE' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(10)),'''') is not null'
				when 'BIT' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(1)),'''') is not null'
				when 'XML' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(max)),'''') is not null'
				when 'CONTENTOBJ' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(20)),'''') is not null'
				when 'DOCUMENTOBJ' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(20)),'''') is not null'
				else 'nullif(cast(' + quotename(@minCol) + ' as varchar(255)),'''') is not null'
				end
			EXEC(@colqry)
			IF @@ERROR <> 0 GOTO on_error
		END
				
		SELECT @minColID = MIN(mdc.columnID) 
			FROM dbo.ams_memberDataColumns as mdc
			where mdc.orgID = @orgid
			and mdc.skipImport = 0
			and columnID > @minColID
	END

	-- remove empty values in holding table
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Removing empty values in holding table',getdate())

	DELETE FROM datatransfer.dbo.ams_memberDataHolding
	WHERE orgID = @orgID
	AND (columnValueString = '' or columnValueString is null)
	AND columnValueDecimal2 is null
	AND columnValueInteger is null
	AND columnValueDate is null
	AND columnValueBit is null
	AND columnValueXML is null
	AND columnValueSiteResourceID is null
		IF @@ERROR <> 0 GOTO on_error

	-- add new columnValues	
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Inserting new member data column values',getdate())

	INSERT INTO dbo.ams_memberDataColumnValues (columnID, columnValueString, columnValueDecimal2, columnValueInteger, columnValueDate, columnValueBit, columnValueXML, columnValueSiteResourceID)
	select distinct columnID, columnValueString, columnValueDecimal2, columnValueInteger, columnValueDate, columnValueBit, cast(columnValueXML as varchar(max)), columnValueSiteResourceID
	from datatransfer.dbo.ams_memberDataHolding
	where orgID = @orgid
		except
	select mdcv.columnID, mdcv.columnValueString, mdcv.columnValueDecimal2, mdcv.columnValueInteger, mdcv.columnValueDate, mdcv.columnValueBit, cast(mdcv.columnValueXML as varchar(max)), mdcv.columnValueSiteResourceID
	from dbo.ams_memberDataColumnValues as mdcv
	inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
		and mdc.orgID = @orgID
		IF @@ERROR <> 0 GOTO on_error

	-- update holding with valueIDs
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for string',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode = 'STRING'
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and mdcv.columnValueString = mdh.columnValueString
			IF @@ERROR <> 0 GOTO on_error

	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for decimal',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode = 'DECIMAL2'
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and mdcv.columnValueDecimal2 = mdh.columnValueDecimal2
			IF @@ERROR <> 0 GOTO on_error

	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for integer',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode = 'INTEGER'
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and mdcv.columnValueInteger = mdh.columnValueInteger
			IF @@ERROR <> 0 GOTO on_error

	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for date',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode = 'DATE'
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and mdcv.columnValueDate = mdh.columnValueDate
			IF @@ERROR <> 0 GOTO on_error

	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for bit',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode = 'BIT'
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and mdcv.columnValueBit = mdh.columnValueBit
			IF @@ERROR <> 0 GOTO on_error

	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for xml',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode = 'XML'
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and cast(mdcv.columnValueXML as varchar(max)) = cast(mdh.columnValueXML as varchar(max))
			IF @@ERROR <> 0 GOTO on_error

	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for SRID',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode IN ('CONTENTOBJ','DOCUMENTOBJ')
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and mdcv.columnValueSiteResourceID = mdh.columnValueSiteResourceID
			IF @@ERROR <> 0 GOTO on_error

	-- figure out what should be in memberdata
	-- include imported data and current data for columns that were skipped
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Figure out what should be in memberdata',getdate())

	CREATE TABLE #shouldBeMemberData (dataid int IDENTITY(1,1), memberid int, valueid int)
	; WITH MembersImported AS (
		select m.memberid, flat.rowID
		from dbo.ams_members as m
		inner join #importMemberData as flat on m.membernumber = flat.membernumber 
			and m.orgid = @orgid
			and m.memberid = m.activememberid
			and m.memberTypeID = 2
			AND m.status <> 'D'
	)
	INSERT INTO #shouldBeMemberData (memberid, valueID)
	SELECT distinct m.memberid, mdcv.valueid
	from MembersImported as M
	inner join datatransfer.dbo.ams_memberDataHolding as mdh on mdh.rowid = M.rowid 
		and mdh.orgID = @orgID
	inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdh.columnID 
		and mdcv.valueID = mdh.valueID
		union
	select distinct md.memberid, md.valueid
	from dbo.ams_memberData as md
	inner join dbo.ams_members as m on m.memberid = md.memberID
		and m.orgID = @orgID
		and m.memberid = m.activeMemberID
	inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
	inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
		and mdc.skipImport = 1
		IF @@ERROR <> 0 GOTO on_error

	-- delete what shouldnt be but is
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Removing old custom data for members',getdate())

	DELETE FROM dbo.ams_memberData
	where dataid in (
		select md.dataid
		from dbo.ams_memberData as md
		inner join dbo.ams_members as m on m.memberid = md.memberid
		inner join #importMemberData as flat on m.membernumber = flat.membernumber 
			and m.orgid = @orgid
			and m.memberid = m.activememberid
			and m.memberTypeID = 2
			AND m.status <> 'D'
		WHERE NOT EXISTS(
			select dataid 
			from #shouldBeMemberData as sb
			where sb.memberid = md.memberid
			and sb.valueid = md.valueid
		)
	)
		IF @@ERROR <> 0 GOTO on_error
	
	-- add what should be but isnt
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Inserting new custom data for members',getdate())

	INSERT INTO dbo.ams_memberData (memberID, valueID)
	select sb.memberid, sb.valueid
	from #shouldBeMemberData as sb
	WHERE NOT EXISTS(
		select dataid 
		from dbo.ams_memberData as md
		where md.memberid = sb.memberid
		and md.valueid = sb.valueid
	)
		IF @@ERROR <> 0 GOTO on_error

	-- clear #shouldBeMemberData
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Cleaning up #shouldBeMemberData',getdate())

	IF OBJECT_ID('tempdb..#shouldBeMemberData') IS NOT NULL BEGIN
		DROP TABLE #shouldBeMemberData
		IF @@ERROR <> 0 GOTO on_error
	END

	-- clear memberdataholding
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Cleaning up holding table',getdate())

	DELETE FROM datatransfer.dbo.ams_memberDataHolding where orgID = @orgID
		IF @@ERROR <> 0 GOTO on_error

	SELECT @returnVal = 0

IF @@TRANCOUNT > 0 COMMIT TRAN
GOTO on_cleanup

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @returnVal = -2
	GOTO on_cleanup

on_cleanup:
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Inserting entries into log table',getdate())

	INSERT INTO dataTransfer.dbo.ams_memberDataImportStatus (orgID,stepNum,stepMsg,stepDate)
	select @orgID, stepNum, stepMsg, stepDate
	from @logTable
	order by stepNum

	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Cleaning up #importMemberData'
	IF OBJECT_ID('tempdb..#importMemberData') IS NOT NULL
		DROP TABLE #importMemberData

	-- populate cache
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Repopulating Member Group Cache'
	INSERT INTO dbo.queue_processMemberGroups (orgID) VALUES (@orgID)
	exec dbo.ams_populateVwMemberGroups @orgid=@orgID, @memberid=-1

	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Import complete'

	RETURN @returnVal
GO


update dbo.ams_memberAddresses
set dateLastUpdatedDistrict = null
where dateLastUpdatedDistrict is not null
GO


use membercentral
GO

declare @taskID int

insert into dbo.scheduledTasks(name, nextRunDate, interval, intervalTypeID, taskCFC, timeoutMinutes, disabled, siteid)
values ('MemberCentral District Matching Report', '3/1/2013 7:00AM', 1, 2, 'model.scheduledTasks.tasks.monthlyCiceroAPIReport', 5, 0, 1)

select @taskID = IDENT_CURRENT('dbo.scheduledTasks')

insert into dbo.scheduledTaskHistory (taskID, statusTypeID, dateStarted, dateEnded, serverid, dateLastUpdated)
values (@taskID, 2, getdate(), getdate(), 1, getdate())
GO
