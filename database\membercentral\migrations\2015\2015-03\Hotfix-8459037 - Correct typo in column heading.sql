use membercentral
GO

ALTER PROC [dbo].[tr_report_invoiceaging]
@orgid int,
@endDate datetime,
@groupBy varchar(7),
@display varchar(7),
@filename varchar(400) = null

AS

SET NOCOUNT ON

-- group by must be valid
IF @groupBy not in ('member','invoice')
	select @groupBy = 'member'

-- display must be valid
IF @display not in ('summary','detail')
	select @display = 'summary'


-- setup holding tables
DECLARE @orgCode varchar(10), @fullsql varchar(max)
SELECT @orgcode = orgcode from dbo.organizations where orgID = @orgID

IF OBJECT_ID('tempdb..#tmpARReport') IS NOT NULL 
	DROP TABLE #tmpARReport
CREATE TABLE #tmpARReport (invoiceID int, revenueGLAccountID int, AREnd money, openInvoices int)

IF OBJECT_ID('tempdb..#tmpINVAGEReport') IS NOT NULL 
	DROP TABLE #tmpINVAGEReport
CREATE TABLE #tmpINVAGEReport (invoiceid int, invoiceNumber varchar(30), dateCreated datetime, dateBilled datetime, 
	dateDue datetime, revenueGLAID int,	revenueGL varchar(max),	revenueGLCode varchar(max), assignedToMemberId int, 
	memberName varchar(300), membernumber varchar(50), company varchar(200),
	AREnd money, ageFut bit, age030 bit, age3160 bit, age6190 bit, age91120 bit, age120over bit, openInvoices int)


-- get data
IF @display = 'detail' 
BEGIN
	INSERT INTO #tmpARReport (invoiceID, revenueGLAccountID, AREnd, openInvoices)
	EXEC dbo.tr_report_baseAR @orgid, @enddate, null, 'invgl'

	CREATE CLUSTERED INDEX IX_tmpARReport_invGLAID ON #tmpARReport (invoiceID asc, revenueGLAccountID asc);

	IF OBJECT_ID('tempdb..#tblGL') IS NOT NULL 
		DROP TABLE #tblGL
	CREATE TABLE #tblGL (GLAccountID int PRIMARY KEY, AccountCode varchar(200), thePathExpanded varchar(max))
	insert into #tblGL
	select GLAccountID, AccountCode, thePathExpanded
	from dbo.fn_getRecursiveGLAccounts(@orgID)
	
	insert into #tmpINVAGEReport (invoiceid, invoiceNumber, dateCreated, dateBilled, dateDue, revenueGLAID, revenueGL, revenueGLCode, 
		assignedToMemberId, memberName, memberNumber, company, AREnd, ageFut, age030, age3160, age6190, age91120, age120over, openInvoices)
	select i.invoiceID, @orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, 
		i.dateCreated, i.dateBilled, i.dateDue, 
		gl.glAccountID, gl.thePathExpanded, gl.accountCode,
		m2.memberID as assignedToMemberID, 
		m2.lastname + ', ' + m2.firstname + isnull(' ' + nullif(m2.middlename,''),''), m2.membernumber,
		m2.company as company, tmp.AREnd,
		ageFut = case when datediff(dd,i.dateDue,@enddate) < 0 then 1 else 0 end,
		age030 = case when datediff(dd,i.dateDue,@enddate) between 0 and 30 then 1 else 0 end,
		age3160 = case when datediff(dd,i.dateDue,@enddate) between 31 and 60 then 1 else 0 end,
		age6190 = case when datediff(dd,i.dateDue,@enddate) between 61 and 90 then 1 else 0 end,
		age91120 = case when datediff(dd,i.dateDue,@enddate) between 91 and 120 then 1 else 0 end,
		age120over = case when datediff(dd,i.dateDue,@enddate) > 120 then 1 else 0 end,
		tmp.openInvoices
	from #tmpARReport as tmp
	inner join dbo.tr_invoices AS i on i.invoiceID = tmp.invoiceID
	inner join dbo.ams_members as m on m.memberID = i.assignedToMemberID
	inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
	inner join #tblGL as gl on gl.glAccountID = tmp.revenueGLAccountID

	IF OBJECT_ID('tempdb..#tblGL') IS NOT NULL 
		DROP TABLE #tblGL
END
IF @display = 'summary'
BEGIN
	INSERT INTO #tmpARReport (invoiceID, AREnd, openInvoices)
	EXEC dbo.tr_report_baseAR @orgid, @enddate, null, 'inv'

	CREATE CLUSTERED INDEX IX_tmpARReport_invID ON #tmpARReport (invoiceID asc);

	insert into #tmpINVAGEReport (invoiceid, invoiceNumber, dateCreated, dateBilled, dateDue, assignedToMemberId, memberName, memberNumber, 
		company, AREnd, ageFut, age030, age3160, age6190, age91120, age120over, openInvoices)
	select i.invoiceID, @orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, 
		i.dateCreated, i.dateBilled, i.dateDue, m2.memberID as assignedToMemberID, 
		m2.lastname + ', ' + m2.firstname + isnull(' ' + nullif(m2.middlename,''),''), m2.membernumber,
		m2.company as company, tmp.AREnd,
		ageFut = case when datediff(dd,i.dateDue,@enddate) < 0 then 1 else 0 end,
		age030 = case when datediff(dd,i.dateDue,@enddate) between 0 and 30 then 1 else 0 end,
		age3160 = case when datediff(dd,i.dateDue,@enddate) between 31 and 60 then 1 else 0 end,
		age6190 = case when datediff(dd,i.dateDue,@enddate) between 61 and 90 then 1 else 0 end,
		age91120 = case when datediff(dd,i.dateDue,@enddate) between 91 and 120 then 1 else 0 end,
		age120over = case when datediff(dd,i.dateDue,@enddate) > 120 then 1 else 0 end,
		tmp.openInvoices
	from #tmpARReport as tmp
	inner join dbo.tr_invoices AS i on i.invoiceID = tmp.invoiceID
	inner join dbo.ams_members as m on m.memberID = i.assignedToMemberID
	inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
END


-- final data 
IF @groupBy = 'invoice' and @display = 'summary' and @filename is not null BEGIN
	SELECT @fullsql = '
		select invoiceNumber as [Invoice], 
			convert(varchar(10),dateCreated,101) as [Date Created], convert(varchar(10),dateBilled,101) as [Date Billed], 
			convert(varchar(10),dateDue,101) as [Date Due], memberName as [Member], 
			memberNumber as [Member Number], company as [Company], 
			case when ageFut = 1 then AREnd else null end as [Due Future],
			case when age030 = 1 then AREnd else null end as [0-30 Days],
			case when age3160 = 1 then AREnd else null end as [31-60 Days],
			case when age6190 = 1 then AREnd else null end as [61-90 Days],
			case when age91120 = 1 then AREnd else null end as [91-120 Days],
			case when age120over = 1 then AREnd else null end as [120+ Days]
		from #tmpINVAGEReport
		order by dateDue, invoiceNumber'
	EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql
END 
IF @groupBy = 'invoice' and @display = 'detail' and @filename is not null BEGIN
	SELECT @fullsql = '
		select invoiceNumber as [Invoice], 
			convert(varchar(10),dateCreated,101) as [Date Created], convert(varchar(10),dateBilled,101) as [Date Billed], 
			convert(varchar(10),dateDue,101) as [Date Due], memberName as [Member], 
			memberNumber as [Member Number], company as [Company], 
			revenueGL as [Revenue Account], revenueGLCode as [Revenue Account Code], 
			case when ageFut = 1 then AREnd else null end as [Due Future],
			case when age030 = 1 then AREnd else null end as [0-30 Days],
			case when age3160 = 1 then AREnd else null end as [31-60 Days],
			case when age6190 = 1 then AREnd else null end as [61-90 Days],
			case when age91120 = 1 then AREnd else null end as [91-120 Days],
			case when age120over = 1 then AREnd else null end as [120+ Days]
		from #tmpINVAGEReport
		order by dateDue, invoiceNumber, revenueGL'
	EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql
END 
IF @groupBy = 'invoice' and @filename is null BEGIN
	select *
	from (
		select invoiceID, invoiceNumber, dateBilled, dateDue, assignedToMemberId, memberName, membernumber, company, revenueGLAID, revenueGL, revenueGLCode, 
			ageFut, age030, age3160, age6190, age91120, age120over, 
			case when ageFut = 1 then AREnd else null end as invFut,
			case when age030 = 1 then AREnd else null end as inv030,
			case when age3160 = 1 then AREnd else null end as inv3160,
			case when age6190 = 1 then AREnd else null end as inv6190,
			case when age91120 = 1 then AREnd else null end as inv91120,
			case when age120over = 1 then AREnd else null end as inv120over, openInvoices
		from #tmpINVAGEReport
			union all
		select null as invoiceid, null as invoiceNumber, null, null, ********* as assignedToMemberId, 'Report Total' as memberName, null, null, revenueGLAID, revenueGL, revenueGLCode, 
			1, 1, 1, 1, 1, 1, 
			sum(case when ageFut = 1 then AREnd else 0 end) as invFut,
			sum(case when age030 = 1 then AREnd else 0 end) as inv030,
			sum(case when age3160 = 1 then AREnd else 0 end) as inv3160,
			sum(case when age6190 = 1 then AREnd else 0 end) as inv6190,
			sum(case when age91120 = 1 then AREnd else 0 end) as inv91120,
			sum(case when age120over = 1 then AREnd else 0 end) as inv120over, null
		from #tmpINVAGEReport
		group by revenueGLAID, revenueGL, revenueGLCode
	) as tmp
	order by case when invoiceid is null then 1 else 0 end, dateDue, invoiceNumber
END
IF @groupBy = 'member' and @display = 'summary' and @filename is not null BEGIN
	SELECT @fullsql = '
		select memberName as [Member], memberNumber as [Member Number], company as [Company], invoiceNumber as [Invoice], 
			convert(varchar(10),dateCreated,101) as [Date Created], convert(varchar(10),dateBilled,101) as [Date Billed], 
			convert(varchar(10),dateDue,101) as [Date Due], 
			case when ageFut = 1 then AREnd else null end as [Due Future],
			case when age030 = 1 then AREnd else null end as [0-30 Days],
			case when age3160 = 1 then AREnd else null end as [31-60 Days],
			case when age6190 = 1 then AREnd else null end as [61-90 Days],
			case when age91120 = 1 then AREnd else null end as [91-120 Days],
			case when age120over = 1 then AREnd else null end as [120+ Days]
		from #tmpINVAGEReport
		order by memberName, membernumber, assignedToMemberId, invoiceNumber'
	EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql
END 
IF @groupBy = 'member' and @display = 'detail' and @filename is not null BEGIN
	SELECT @fullsql = '
		select memberName as [Member], memberNumber as [Member Number], company as [Company], invoiceNumber as [Invoice], 
			convert(varchar(10),dateCreated,101) as [Date Created], convert(varchar(10),dateBilled,101) as [Date Billed], 
			convert(varchar(10),dateDue,101) as [Date Due], 
			revenueGL as [Revenue Account], revenueGLCode as [Revenue Account Code], 
			case when ageFut = 1 then AREnd else null end as [Due Future],
			case when age030 = 1 then AREnd else null end as [0-30 Days],
			case when age3160 = 1 then AREnd else null end as [31-60 Days],
			case when age6190 = 1 then AREnd else null end as [61-90 Days],
			case when age91120 = 1 then AREnd else null end as [91-120 Days],
			case when age120over = 1 then AREnd else null end as [120+ Days]
		from #tmpINVAGEReport
		order by memberName, membernumber, assignedToMemberId, invoiceNumber, revenueGL'
	EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql
END 
IF @groupBy = 'member' and @filename is null BEGIN
	select *
	from (
		select invoiceID, invoiceNumber, dateBilled, dateDue, assignedToMemberId, memberName, membernumber, company, revenueGLAID, revenueGL, revenueGLCode, 
			ageFut, age030, age3160, age6190, age91120, age120over, 
			case when ageFut = 1 then AREnd else null end as invFut,
			case when age030 = 1 then AREnd else null end as inv030,
			case when age3160 = 1 then AREnd else null end as inv3160,
			case when age6190 = 1 then AREnd else null end as inv6190,
			case when age91120 = 1 then AREnd else null end as inv91120,
			case when age120over = 1 then AREnd else null end as inv120over, openInvoices
		from #tmpINVAGEReport
			union all
		select null as invoiceid, null as invoiceNumber, null, null, ********* as assignedToMemberId, 'Report Total' as memberName, null, null, revenueGLAID, revenueGL, revenueGLCode, 
			1, 1, 1, 1, 1, 1, 
			sum(case when ageFut = 1 then AREnd else 0 end) as invFut,
			sum(case when age030 = 1 then AREnd else 0 end) as inv030,
			sum(case when age3160 = 1 then AREnd else 0 end) as inv3160,
			sum(case when age6190 = 1 then AREnd else 0 end) as inv6190,
			sum(case when age91120 = 1 then AREnd else 0 end) as inv91120,
			sum(case when age120over = 1 then AREnd else 0 end) as inv120over, null
		from #tmpINVAGEReport
		group by revenueGLAID, revenueGL, revenueGLCode
	) as tmp
	order by case when invoiceid is null then 1 else 0 end, memberName, membernumber, assignedToMemberId, invoiceNumber, revenueGL
END


-- drop the final temp table
IF OBJECT_ID('tempdb..#tmpINVAGEReport') IS NOT NULL 
	DROP TABLE #tmpINVAGEReport
IF OBJECT_ID('tempdb..#tmpARReport') IS NOT NULL 
	DROP TABLE #tmpARReport

RETURN 0
GO
