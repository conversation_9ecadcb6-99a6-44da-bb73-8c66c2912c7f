ALTER PROCEDURE dbo.trialsmith_syncListUnsubs
@list1 varchar(100),
@list2 varchar(100)

AS

SET NOCOUNT ON;

update L1members
set membertype_ = 'unsub'
from trialslyris1.dbo.members_ L1members
inner join trialslyris1.dbo.members_ L2members on L1members.emailaddr_ = L2members.emailaddr_
	and L2members.membertype_ = 'unsub'
	and L1members.list_ = @list1
	and L2members.list_ = @list2;

update L2members
set membertype_ = 'unsub'
from trialslyris1.dbo.members_ L1members
inner join trialslyris1.dbo.members_ L2members on L1members.emailaddr_ = L2members.emailaddr_
	and L1members.membertype_ = 'unsub'
	and L1members.list_ = @list1
	and L2members.list_ = @list2;
GO
