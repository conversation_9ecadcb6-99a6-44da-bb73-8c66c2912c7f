use membercentral
GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_EQ_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.valueID = cv.conditionValueInteger
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_EQ_STRING'
and tblc.displayTypeCode in ('RADIO','SELECT')

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.columnValue = cv.conditionValueString
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_EQ_STRING'
and tblc.displayTypeCode not in ('RADIO','SELECT')

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_EQ_BIT
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.vw_memberData__bit as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.columnValue = cv.conditionValueBit
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_EQ_BIT'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_EQ_INTEGER
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.valueID = cv.conditionValueInteger
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_EQ_INTEGER'
and tblc.displayTypeCode in ('RADIO','SELECT')

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.columnValue = cv.conditionValueInteger
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_EQ_INTEGER'
and tblc.displayTypeCode not in ('RADIO','SELECT')

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_DATEDIFF
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'eq'
inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datediff(q,vw.columnValue,getdate())
		when 'm' then datediff(m,vw.columnValue,getdate())
		when 'wk' then datediff(wk,vw.columnValue,getdate())
		when 'dw' then datediff(dw,vw.columnValue,getdate())
		when 'd' then datediff(d,vw.columnValue,getdate())
		when 'dy' then datediff(dy,vw.columnValue,getdate())
		else datediff(yy,vw.columnValue,getdate()) end = abs(cv.conditionValueInteger)
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_DATEDIFF'
and cv.conditionValueInteger >= 0

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'eq'
inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datediff(q,getdate(),vw.columnValue)
		when 'm' then datediff(m,getdate(),vw.columnValue)
		when 'wk' then datediff(wk,getdate(),vw.columnValue)
		when 'dw' then datediff(dw,getdate(),vw.columnValue)
		when 'd' then datediff(d,getdate(),vw.columnValue)
		when 'dy' then datediff(dy,getdate(),vw.columnValue)
		else datediff(yy,getdate(),vw.columnValue) end = abs(cv.conditionValueInteger)
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_DATEDIFF'
and cv.conditionValueInteger < 0

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'neq'
inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datediff(q,vw.columnValue,getdate())
		when 'm' then datediff(m,vw.columnValue,getdate())
		when 'wk' then datediff(wk,vw.columnValue,getdate())
		when 'dw' then datediff(dw,vw.columnValue,getdate())
		when 'd' then datediff(d,vw.columnValue,getdate())
		when 'dy' then datediff(dy,vw.columnValue,getdate())
		else datediff(yy,vw.columnValue,getdate()) end <> abs(cv.conditionValueInteger)
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_DATEDIFF'
and cv.conditionValueInteger >= 0

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'neq'
inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datediff(q,getdate(),vw.columnValue)
		when 'm' then datediff(m,getdate(),vw.columnValue)
		when 'wk' then datediff(wk,getdate(),vw.columnValue)
		when 'dw' then datediff(dw,getdate(),vw.columnValue)
		when 'd' then datediff(d,getdate(),vw.columnValue)
		when 'dy' then datediff(dy,getdate(),vw.columnValue)
		else datediff(yy,getdate(),vw.columnValue) end <> abs(cv.conditionValueInteger)
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_DATEDIFF'
and cv.conditionValueInteger < 0

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lt'
inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datediff(q,vw.columnValue,getdate())
		when 'm' then datediff(m,vw.columnValue,getdate())
		when 'wk' then datediff(wk,vw.columnValue,getdate())
		when 'dw' then datediff(dw,vw.columnValue,getdate())
		when 'd' then datediff(d,vw.columnValue,getdate())
		when 'dy' then datediff(dy,vw.columnValue,getdate())
		else datediff(yy,vw.columnValue,getdate()) end < abs(cv.conditionValueInteger)
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_DATEDIFF'
and cv.conditionValueInteger >= 0

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lt'
inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datediff(q,getdate(),vw.columnValue)
		when 'm' then datediff(m,getdate(),vw.columnValue)
		when 'wk' then datediff(wk,getdate(),vw.columnValue)
		when 'dw' then datediff(dw,getdate(),vw.columnValue)
		when 'd' then datediff(d,getdate(),vw.columnValue)
		when 'dy' then datediff(dy,getdate(),vw.columnValue)
		else datediff(yy,getdate(),vw.columnValue) end < abs(cv.conditionValueInteger)
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_DATEDIFF'
and cv.conditionValueInteger < 0

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lte'
inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datediff(q,vw.columnValue,getdate())
		when 'm' then datediff(m,vw.columnValue,getdate())
		when 'wk' then datediff(wk,vw.columnValue,getdate())
		when 'dw' then datediff(dw,vw.columnValue,getdate())
		when 'd' then datediff(d,vw.columnValue,getdate())
		when 'dy' then datediff(dy,vw.columnValue,getdate())
		else datediff(yy,vw.columnValue,getdate()) end <= abs(cv.conditionValueInteger)
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_DATEDIFF'
and cv.conditionValueInteger >= 0

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lte'
inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datediff(q,getdate(),vw.columnValue)
		when 'm' then datediff(m,getdate(),vw.columnValue)
		when 'wk' then datediff(wk,getdate(),vw.columnValue)
		when 'dw' then datediff(dw,getdate(),vw.columnValue)
		when 'd' then datediff(d,getdate(),vw.columnValue)
		when 'dy' then datediff(dy,getdate(),vw.columnValue)
		else datediff(yy,getdate(),vw.columnValue) end <= abs(cv.conditionValueInteger)
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_DATEDIFF'
and cv.conditionValueInteger < 0

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gt'
inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datediff(q,vw.columnValue,getdate())
		when 'm' then datediff(m,vw.columnValue,getdate())
		when 'wk' then datediff(wk,vw.columnValue,getdate())
		when 'dw' then datediff(dw,vw.columnValue,getdate())
		when 'd' then datediff(d,vw.columnValue,getdate())
		when 'dy' then datediff(dy,vw.columnValue,getdate())
		else datediff(yy,vw.columnValue,getdate()) end > abs(cv.conditionValueInteger)
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_DATEDIFF'
and cv.conditionValueInteger >= 0

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gt'
inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datediff(q,getdate(),vw.columnValue)
		when 'm' then datediff(m,getdate(),vw.columnValue)
		when 'wk' then datediff(wk,getdate(),vw.columnValue)
		when 'dw' then datediff(dw,getdate(),vw.columnValue)
		when 'd' then datediff(d,getdate(),vw.columnValue)
		when 'dy' then datediff(dy,getdate(),vw.columnValue)
		else datediff(yy,getdate(),vw.columnValue) end > abs(cv.conditionValueInteger)
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_DATEDIFF'
and cv.conditionValueInteger < 0

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gte'
inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datediff(q,vw.columnValue,getdate())
		when 'm' then datediff(m,vw.columnValue,getdate())
		when 'wk' then datediff(wk,vw.columnValue,getdate())
		when 'dw' then datediff(dw,vw.columnValue,getdate())
		when 'd' then datediff(d,vw.columnValue,getdate())
		when 'dy' then datediff(dy,vw.columnValue,getdate())
		else datediff(yy,vw.columnValue,getdate()) end >= abs(cv.conditionValueInteger)
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_DATEDIFF'
and cv.conditionValueInteger >= 0

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gte'
inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datediff(q,getdate(),vw.columnValue)
		when 'm' then datediff(m,getdate(),vw.columnValue)
		when 'wk' then datediff(wk,getdate(),vw.columnValue)
		when 'dw' then datediff(dw,getdate(),vw.columnValue)
		when 'd' then datediff(d,getdate(),vw.columnValue)
		when 'dy' then datediff(dy,getdate(),vw.columnValue)
		else datediff(yy,getdate(),vw.columnValue) end >= abs(cv.conditionValueInteger)
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_DATEDIFF'
and cv.conditionValueInteger < 0

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_EXISTS_BIT
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join dbo.vw_memberData__bit as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.columnValue is not null
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_EXISTS_BIT'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_EXISTS_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.columnValue is not null
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_EXISTS_STRING'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MA_EQ
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MA_EQ'
and tblc.displayTypeCode not in ('RADIO','SELECT')
and case tblc.fieldCodeAreaPartA
	when 'address1' then isnull(ma.address1,'') 
	when 'address2' then isnull(ma.address2,'') 
	when 'address3' then isnull(ma.address3,'') 
	when 'city' then isnull(ma.city,'') 
	when 'postalcode' then isnull(ma.postalcode,'') 
	when 'county' then isnull(ma.county,'') 
	else isnull(ma.attn,'') end = cv.conditionValueString

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MA_EQ'
and tblc.displayTypeCode in ('RADIO','SELECT')
and case tblc.fieldCodeAreaPartA
	when 'stateprov' then isnull(ma.stateid,0) 
	else isnull(ma.countryid,0) end = cv.conditionValueInteger

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MPL_EQ_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_EQ_STRING'
and case tblc.fieldCodeAreaPartA
	when 'status' then isnull(mpls.statusName,'') 
	else isnull(mpl.licensenumber,'') end = cv.conditionValueString

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MPL_EXISTS_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_EXISTS_STRING'
and case tblc.fieldCodeAreaPartA
	when 'status' then nullif(mpls.StatusName,'')
	else nullif(mpl.licensenumber,'') end is not null

GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_E_REGISTERED
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join dbo.ev_events as e on e.eventID = tblc.fieldCodeAreaID
inner join dbo.ev_registration as er on er.eventID = e.eventID and er.status = 'A'
inner join dbo.ev_registrants as reg on reg.registrationID = er.registrationID and reg.status = 'A'
inner join dbo.ams_members as m2 on m2.memberid = reg.memberid
inner join #tblMembers as m on m.memberID = m2.activeMemberID
where tblc.subProc = 'E_REGISTERED'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MPL_DATEDIFF
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'eq'
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datediff(q,mpl.activeDate,getdate())
		when 'm' then datediff(m,mpl.activeDate,getdate())
		when 'wk' then datediff(wk,mpl.activeDate,getdate())
		when 'dw' then datediff(dw,mpl.activeDate,getdate())
		when 'd' then datediff(d,mpl.activeDate,getdate())
		when 'dy' then datediff(dy,mpl.activeDate,getdate())
		else datediff(yy,mpl.activeDate,getdate()) end = abs(cv.conditionValueInteger)
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_DATEDIFF'
and cv.conditionValueInteger >= 0 

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'eq'
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datediff(q,getdate(),mpl.activeDate)
		when 'm' then datediff(m,getdate(),mpl.activeDate)
		when 'wk' then datediff(wk,getdate(),mpl.activeDate)
		when 'dw' then datediff(dw,getdate(),mpl.activeDate)
		when 'd' then datediff(d,getdate(),mpl.activeDate)
		when 'dy' then datediff(dy,getdate(),mpl.activeDate)
		else datediff(yy,getdate(),mpl.activeDate) end = abs(cv.conditionValueInteger)
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_DATEDIFF'
and cv.conditionValueInteger < 0 

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'neq'
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datediff(q,mpl.activeDate,getdate())
		when 'm' then datediff(m,mpl.activeDate,getdate())
		when 'wk' then datediff(wk,mpl.activeDate,getdate())
		when 'dw' then datediff(dw,mpl.activeDate,getdate())
		when 'd' then datediff(d,mpl.activeDate,getdate())
		when 'dy' then datediff(dy,mpl.activeDate,getdate())
		else datediff(yy,mpl.activeDate,getdate()) end <> abs(cv.conditionValueInteger)
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_DATEDIFF'
and cv.conditionValueInteger >= 0

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'neq'
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datediff(q,getdate(),mpl.activeDate)
		when 'm' then datediff(m,getdate(),mpl.activeDate)
		when 'wk' then datediff(wk,getdate(),mpl.activeDate)
		when 'dw' then datediff(dw,getdate(),mpl.activeDate)
		when 'd' then datediff(d,getdate(),mpl.activeDate)
		when 'dy' then datediff(dy,getdate(),mpl.activeDate)
		else datediff(yy,getdate(),mpl.activeDate) end <> abs(cv.conditionValueInteger)
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_DATEDIFF'
and cv.conditionValueInteger < 0 

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lt'
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datediff(q,mpl.activeDate,getdate())
		when 'm' then datediff(m,mpl.activeDate,getdate())
		when 'wk' then datediff(wk,mpl.activeDate,getdate())
		when 'dw' then datediff(dw,mpl.activeDate,getdate())
		when 'd' then datediff(d,mpl.activeDate,getdate())
		when 'dy' then datediff(dy,mpl.activeDate,getdate())
		else datediff(yy,mpl.activeDate,getdate()) end < abs(cv.conditionValueInteger)
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_DATEDIFF'
and cv.conditionValueInteger >= 0

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lt'
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datediff(q,getdate(),mpl.activeDate)
		when 'm' then datediff(m,getdate(),mpl.activeDate)
		when 'wk' then datediff(wk,getdate(),mpl.activeDate)
		when 'dw' then datediff(dw,getdate(),mpl.activeDate)
		when 'd' then datediff(d,getdate(),mpl.activeDate)
		when 'dy' then datediff(dy,getdate(),mpl.activeDate)
		else datediff(yy,getdate(),mpl.activeDate) end < abs(cv.conditionValueInteger)
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_DATEDIFF'
and cv.conditionValueInteger < 0 

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lte'
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datediff(q,mpl.activeDate,getdate())
		when 'm' then datediff(m,mpl.activeDate,getdate())
		when 'wk' then datediff(wk,mpl.activeDate,getdate())
		when 'dw' then datediff(dw,mpl.activeDate,getdate())
		when 'd' then datediff(d,mpl.activeDate,getdate())
		when 'dy' then datediff(dy,mpl.activeDate,getdate())
		else datediff(yy,mpl.activeDate,getdate()) end <= abs(cv.conditionValueInteger)
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_DATEDIFF'
and cv.conditionValueInteger >= 0

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lte'
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datediff(q,getdate(),mpl.activeDate)
		when 'm' then datediff(m,getdate(),mpl.activeDate)
		when 'wk' then datediff(wk,getdate(),mpl.activeDate)
		when 'dw' then datediff(dw,getdate(),mpl.activeDate)
		when 'd' then datediff(d,getdate(),mpl.activeDate)
		when 'dy' then datediff(dy,getdate(),mpl.activeDate)
		else datediff(yy,getdate(),mpl.activeDate) end <= abs(cv.conditionValueInteger)
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_DATEDIFF'
and cv.conditionValueInteger < 0

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gt'
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datediff(q,mpl.activeDate,getdate())
		when 'm' then datediff(m,mpl.activeDate,getdate())
		when 'wk' then datediff(wk,mpl.activeDate,getdate())
		when 'dw' then datediff(dw,mpl.activeDate,getdate())
		when 'd' then datediff(d,mpl.activeDate,getdate())
		when 'dy' then datediff(dy,mpl.activeDate,getdate())
		else datediff(yy,mpl.activeDate,getdate()) end > abs(cv.conditionValueInteger)
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_DATEDIFF'
and cv.conditionValueInteger >= 0

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gt'
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datediff(q,getdate(),mpl.activeDate)
		when 'm' then datediff(m,getdate(),mpl.activeDate)
		when 'wk' then datediff(wk,getdate(),mpl.activeDate)
		when 'dw' then datediff(dw,getdate(),mpl.activeDate)
		when 'd' then datediff(d,getdate(),mpl.activeDate)
		when 'dy' then datediff(dy,getdate(),mpl.activeDate)
		else datediff(yy,getdate(),mpl.activeDate) end > abs(cv.conditionValueInteger)
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_DATEDIFF'
and cv.conditionValueInteger < 0

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gte'
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datediff(q,mpl.activeDate,getdate())
		when 'm' then datediff(m,mpl.activeDate,getdate())
		when 'wk' then datediff(wk,mpl.activeDate,getdate())
		when 'dw' then datediff(dw,mpl.activeDate,getdate())
		when 'd' then datediff(d,mpl.activeDate,getdate())
		when 'dy' then datediff(dy,mpl.activeDate,getdate())
		else datediff(yy,mpl.activeDate,getdate()) end >= abs(cv.conditionValueInteger)
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_DATEDIFF'
and cv.conditionValueInteger >= 0

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gte'
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datediff(q,getdate(),mpl.activeDate)
		when 'm' then datediff(m,getdate(),mpl.activeDate)
		when 'wk' then datediff(wk,getdate(),mpl.activeDate)
		when 'dw' then datediff(dw,getdate(),mpl.activeDate)
		when 'd' then datediff(d,getdate(),mpl.activeDate)
		when 'dy' then datediff(dy,getdate(),mpl.activeDate)
		else datediff(yy,getdate(),mpl.activeDate) end >= abs(cv.conditionValueInteger)
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_DATEDIFF'
and cv.conditionValueInteger < 0

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_NEQ_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberid = m.memberid
where tblc.subProc = 'MD_NEQ_STRING'
and tblc.displayTypeCode in ('RADIO','SELECT')
and not exists (
	select vw.memberID
	from dbo.vw_memberData__string as vw WITH(NOEXPAND)
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
		and vw.valueID = cv.conditionValueInteger
	where vw.columnID = tblc.fieldCodeAreaID
	and vw.memberID = m.memberid
)	

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberid = m.memberid
where tblc.subProc = 'MD_NEQ_STRING'
and tblc.displayTypeCode not in ('RADIO','SELECT')
and not exists (
	select vw.memberID
	from dbo.vw_memberData__string as vw WITH(NOEXPAND)
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
		and vw.columnValue = cv.conditionValueString
	where vw.columnID = tblc.fieldCodeAreaID
	and vw.memberID = m.memberid
)

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_NOTEXISTS_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberid = m.memberid
where tblc.subProc = 'MD_NOTEXISTS_STRING'
and not exists (
	select vw.memberID
	from dbo.vw_memberData__string as vw WITH(NOEXPAND)
	where vw.columnID = tblc.fieldCodeAreaID
	and vw.memberID = m.memberid
	and vw.columnValue is not null
)

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_EQ_DECIMAL2
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.valueID = cv.conditionValueInteger
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_EQ_DECIMAL2'
and tblc.displayTypeCode in ('RADIO','SELECT')

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.columnValue = cv.conditionValueDecimal2
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_EQ_DECIMAL2'
and tblc.displayTypeCode not in ('RADIO','SELECT')

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_EQ_DATE
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.valueID = cv.conditionValueInteger
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_EQ_DATE'
and tblc.displayTypeCode in ('RADIO','SELECT')

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.columnValue = cv.conditionValueDate
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_EQ_DATE'
and tblc.displayTypeCode not in ('RADIO','SELECT')

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_NEQ_BIT
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberid = m.memberid
where tblc.subProc = 'MD_NEQ_BIT'
and not exists (
	select vw.memberID
	from dbo.vw_memberData__bit as vw WITH(NOEXPAND)
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
		and vw.columnValue = cv.conditionValueBit
	where vw.columnID = tblc.fieldCodeAreaID
	and vw.memberID = m.memberid
)

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_NEQ_INTEGER
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberid = m.memberid
where tblc.subProc = 'MD_NEQ_INTEGER'
and tblc.displayTypeCode in ('RADIO','SELECT')
and not exists (
	select vw.memberID
	from dbo.vw_memberData__integer as vw WITH(NOEXPAND)
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
		and vw.valueID = cv.conditionValueInteger
	where vw.columnID = tblc.fieldCodeAreaID
	and vw.memberID = m.memberid
)	

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberID = m.memberid
where tblc.subProc = 'MD_NEQ_INTEGER'
and tblc.displayTypeCode not in ('RADIO','SELECT')
and not exists (
	select vw.memberID
	from dbo.vw_memberData__integer as vw WITH(NOEXPAND)
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
		and vw.columnValue = cv.conditionValueInteger
	where vw.columnID = tblc.fieldCodeAreaID
	and vw.memberID = m.memberid
)

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_NEQ_DECIMAL2
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberid = m.memberid
where tblc.subProc = 'MD_NEQ_DECIMAL2'
and tblc.displayTypeCode in ('RADIO','SELECT')
and not exists (
	select vw.memberID
	from dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND)
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
		and vw.valueID = cv.conditionValueInteger
	where vw.columnID = tblc.fieldCodeAreaID
	and vw.memberID = m.memberid
)	

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberid = m.memberid
where tblc.subProc = 'MD_NEQ_DECIMAL2'
and tblc.displayTypeCode not in ('RADIO','SELECT')
and not exists (
	select vw.memberID
	from dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND)
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
		and vw.columnValue = cv.conditionValueDecimal2
	where vw.columnID = tblc.fieldCodeAreaID
	and vw.memberID = m.memberid
)

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_NEQ_DATE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberid = m.memberid
where tblc.subProc = 'MD_NEQ_DATE'
and tblc.displayTypeCode in ('RADIO','SELECT')
and not exists (
	select vw.memberID
	from dbo.vw_memberData__date as vw WITH(NOEXPAND)
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
		and vw.valueID = cv.conditionValueInteger
	where vw.columnID = tblc.fieldCodeAreaID
	and vw.memberID = m.memberid
)	

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberid = m.memberid
where tblc.subProc = 'MD_NEQ_DATE'
and tblc.displayTypeCode not in ('RADIO','SELECT')
and not exists (
	select vw.memberID
	from dbo.vw_memberData__date as vw WITH(NOEXPAND)
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
		and vw.columnValue = cv.conditionValueDate
	where vw.columnID = tblc.fieldCodeAreaID
	and vw.memberID = m.memberid
)

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_LT_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.columnValue < cv.conditionValueString
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_LT_STRING'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_LT_INTEGER
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.columnValue < cv.conditionValueInteger
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_LT_INTEGER'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_LT_DECIMAL2
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.columnValue < cv.conditionValueDecimal2
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_LT_DECIMAL2'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_LT_DATE
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.columnValue < cv.conditionValueDate
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_LT_DATE'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_LTE_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.columnValue <= cv.conditionValueString
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_LTE_STRING'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_LTE_INTEGER
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.columnValue <= cv.conditionValueInteger
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_LTE_INTEGER'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_LTE_DECIMAL2
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.columnValue <= cv.conditionValueDecimal2
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_LTE_DECIMAL2'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_LTE_DATE
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.columnValue <= cv.conditionValueDate
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_LTE_DATE'                                                  

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_GT_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.columnValue > cv.conditionValueString
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_GT_STRING'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_GT_INTEGER
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.columnValue > cv.conditionValueInteger
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_GT_INTEGER'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_GT_DECIMAL2
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.columnValue > cv.conditionValueDecimal2
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_GT_DECIMAL2'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_GT_DATE
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.columnValue > cv.conditionValueDate
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_GT_DATE'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_GTE_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.columnValue >= cv.conditionValueString
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_GTE_STRING'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_GTE_INTEGER
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.columnValue >= cv.conditionValueInteger
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_GTE_INTEGER'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_GTE_DECIMAL2
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.columnValue >= cv.conditionValueDecimal2
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_GTE_DECIMAL2'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_GTE_DATE
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.columnValue >= cv.conditionValueDate
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_GTE_DATE'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_EXISTS_INTEGER
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.columnValue is not null
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_EXISTS_INTEGER'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_EXISTS_DECIMAL2
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.columnValue is not null
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_EXISTS_DECIMAL2'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_EXISTS_DATE
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.columnValue is not null
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_EXISTS_DATE'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_EXISTS_CONTENTOBJ
AS

insert into #cache_members_conditions_shouldbe
select distinct md.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = tblc.fieldCodeAreaID
inner join dbo.ams_memberdataColumnValues as mdcv on mdcv.columnID = mdc.columnID
inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID and mdcdt.dataTypeCode = 'CONTENTOBJ'
inner join dbo.ams_memberData as md on md.valueID = mdcv.valueID
inner join #tblMembers as m on m.memberID = md.memberID
inner join dbo.cms_content as c on c.siteResourceID = mdcv.columnValueSiteResourceID
inner join dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID and cl.languageID = 1
inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
where tblc.subProc = 'MD_EXISTS_CONTENTOBJ'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_EXISTS_DOCUMENTOBJ
AS

insert into #cache_members_conditions_shouldbe
select distinct md.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = tblc.fieldCodeAreaID
inner join dbo.ams_memberdataColumnValues as mdcv on mdcv.columnID = mdc.columnID
inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID and mdcdt.dataTypeCode = 'DOCUMENTOBJ'
inner join dbo.ams_memberData as md on md.valueID = mdcv.valueID
inner join #tblMembers as m on m.memberID = md.memberID
inner join dbo.cms_documents as d on d.siteResourceID = mdcv.columnValueSiteResourceID
inner join dbo.cms_documentLanguages as dl on dl.documentID = d.documentID and dl.languageID = 1
inner join dbo.cms_documentVersions as dv on dv.documentLanguageID = dl.documentLanguageID and dv.isActive = 1
where tblc.subProc = 'MD_EXISTS_DOCUMENTOBJ'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_NOTEXISTS_INTEGER
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberid = m.memberid
where tblc.subProc = 'MD_NOTEXISTS_INTEGER'
and not exists (
	select vw.memberID
	from dbo.vw_memberData__integer as vw WITH(NOEXPAND)
	where vw.columnID = tblc.fieldCodeAreaID
	and vw.memberID = m.memberid
	and vw.columnValue is not null
)

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_NOTEXISTS_DECIMAL2
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberid = m.memberid
where tblc.subProc = 'MD_NOTEXISTS_DECIMAL2'
and not exists (
	select vw.memberID
	from dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND)
	where vw.columnID = tblc.fieldCodeAreaID
	and vw.memberID = m.memberid
	and vw.columnValue is not null
)

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_NOTEXISTS_DATE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberid = m.memberid
where tblc.subProc = 'MD_NOTEXISTS_DATE'
and not exists (
	select vw.memberID
	from dbo.vw_memberData__date as vw WITH(NOEXPAND)
	where vw.columnID = tblc.fieldCodeAreaID
	and vw.memberID = m.memberid
	and vw.columnValue is not null
)

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_NOTEXISTS_BIT
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberid = m.memberid
where tblc.subProc = 'MD_NOTEXISTS_BIT'
and not exists (
	select vw.memberID
	from dbo.vw_memberData__bit as vw WITH(NOEXPAND)
	where vw.columnID = tblc.fieldCodeAreaID
	and vw.memberID = m.memberid
	and vw.columnValue is not null
)

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_NOTEXISTS_CONTENTOBJ
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberid = m.memberid
where tblc.subProc = 'MD_NOTEXISTS_CONTENTOBJ'
and not exists (
	select VWmd.memberID
	from dbo.ams_memberData as VWmd
	inner join dbo.ams_members as VWm on VWm.memberID = VWmd.memberID and VWm.memberID = m.memberid
	inner join dbo.ams_memberdataColumnValues as VWmdcv on VWmdcv.valueID = VWmd.valueID
	inner join dbo.ams_memberDataColumns as VWmdc on VWmdc.columnID = VWmdcv.columnID and VWmdc.columnID = tblc.fieldCodeAreaID
	where VWmdcv.columnValueSiteResourceID is not null
)

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_NOTEXISTS_DOCUMENTOBJ
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberid = m.memberid
where tblc.subProc = 'MD_NOTEXISTS_DOCUMENTOBJ'
and not exists (
	select VWmd.memberID
	from dbo.ams_memberData as VWmd
	inner join dbo.ams_members as VWm on VWm.memberID = VWmd.memberID and VWm.memberID = m.memberid
	inner join dbo.ams_memberdataColumnValues as VWmdcv on VWmdcv.valueID = VWmd.valueID
	inner join dbo.ams_memberDataColumns as VWmdc on VWmdc.columnID = VWmdcv.columnID and VWmdc.columnID = tblc.fieldCodeAreaID
	where VWmdcv.columnValueSiteResourceID is not null
)

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_CONTAINS_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and vw.columnValue like '%' + cv.conditionValueString + '%'
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_CONTAINS_STRING'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_CONTAINSREGEX_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	-- add back when we have a regexfind fn
	-- and dbo.fn_RegExFind(vw.columnValue,tblc.value) = 1
	and vw.columnValue like '%' + cv.conditionValueString + '%'
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_CONTAINSREGEX_STRING'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MD_DATEPART
AS

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'eq'
inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datepart(q,vw.columnValue) 
		when 'm' then datepart(m,vw.columnValue) 
		when 'wk' then datepart(wk,vw.columnValue)
		when 'dw' then datepart(dw,vw.columnValue)
		when 'd' then datepart(d,vw.columnValue) 
		when 'dy' then datepart(dy,vw.columnValue)
		else datepart(yy,vw.columnValue) end = cv.conditionValueInteger
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_DATEPART'

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'neq'
inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datepart(q,vw.columnValue) 
		when 'm' then datepart(m,vw.columnValue) 
		when 'wk' then datepart(wk,vw.columnValue)
		when 'dw' then datepart(dw,vw.columnValue)
		when 'd' then datepart(d,vw.columnValue) 
		when 'dy' then datepart(dy,vw.columnValue)
		else datepart(yy,vw.columnValue) end <> cv.conditionValueInteger
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_DATEPART'

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lt'
inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datepart(q,vw.columnValue) 
		when 'm' then datepart(m,vw.columnValue) 
		when 'wk' then datepart(wk,vw.columnValue)
		when 'dw' then datepart(dw,vw.columnValue)
		when 'd' then datepart(d,vw.columnValue) 
		when 'dy' then datepart(dy,vw.columnValue)
		else datepart(yy,vw.columnValue) end < cv.conditionValueInteger
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_DATEPART'

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lte'
inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datepart(q,vw.columnValue) 
		when 'm' then datepart(m,vw.columnValue) 
		when 'wk' then datepart(wk,vw.columnValue)
		when 'dw' then datepart(dw,vw.columnValue)
		when 'd' then datepart(d,vw.columnValue) 
		when 'dy' then datepart(dy,vw.columnValue)
		else datepart(yy,vw.columnValue) end <= cv.conditionValueInteger
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_DATEPART'

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gt'
inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datepart(q,vw.columnValue) 
		when 'm' then datepart(m,vw.columnValue) 
		when 'wk' then datepart(wk,vw.columnValue)
		when 'dw' then datepart(dw,vw.columnValue)
		when 'd' then datepart(d,vw.columnValue) 
		when 'dy' then datepart(dy,vw.columnValue)
		else datepart(yy,vw.columnValue) end > cv.conditionValueInteger
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_DATEPART'

insert into #cache_members_conditions_shouldbe
select distinct vw.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gte'
inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datepart(q,vw.columnValue) 
		when 'm' then datepart(m,vw.columnValue) 
		when 'wk' then datepart(wk,vw.columnValue)
		when 'dw' then datepart(dw,vw.columnValue)
		when 'd' then datepart(d,vw.columnValue) 
		when 'dy' then datepart(dy,vw.columnValue)
		else datepart(yy,vw.columnValue) end >= cv.conditionValueInteger
inner join #tblMembers as m on m.memberid = vw.memberid
where tblc.subProc = 'MD_DATEPART'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_M_EQ_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
inner join dbo.ams_members as m on m.memberID = tmpM.memberID
where tblc.subProc = 'M_EQ_STRING'
and case tblc.fieldCode
	when 'm_firstname' then isnull(m.firstname,'')
	when 'm_middlename' then isnull(m.middlename,'')
	when 'm_lastname' then isnull(m.lastname,'')
	when 'm_suffix' then isnull(m.suffix,'')
	when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
	when 'm_company' then isnull(m.company,'')
	when 'm_membernumber' then isnull(m.membernumber,'')
	else isnull(m.prefix,'') end = cv.conditionValueString

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_M_EQ_INTEGER
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
inner join dbo.ams_members as m on m.memberID = tmpM.memberID
where tblc.subProc = 'M_EQ_INTEGER'
and isnull(m.memberTypeID,0) = cv.conditionValueInteger

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_M_NEQ_INTEGER
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
inner join dbo.ams_members as m on m.memberID = tmpM.memberID
where tblc.subProc = 'M_NEQ_INTEGER'
and isnull(m.memberTypeID,0) <> cv.conditionValueInteger

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_M_NEQ_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
inner join dbo.ams_members as m on m.memberID = tmpM.memberID
where tblc.subProc = 'M_NEQ_STRING'
and case tblc.fieldCode
	when 'm_firstname' then isnull(m.firstname,'')
	when 'm_middlename' then isnull(m.middlename,'')
	when 'm_lastname' then isnull(m.lastname,'')
	when 'm_suffix' then isnull(m.suffix,'')
	when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
	when 'm_company' then isnull(m.company,'')
	when 'm_membernumber' then isnull(m.membernumber,'')
	else isnull(m.prefix,'') end <> cv.conditionValueString

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_M_LT
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
inner join dbo.ams_members as m on m.memberID = tmpM.memberID
where tblc.subProc = 'M_LT'
and case tblc.fieldCode
	when 'm_firstname' then isnull(m.firstname,'')
	when 'm_middlename' then isnull(m.middlename,'')
	when 'm_lastname' then isnull(m.lastname,'')
	when 'm_suffix' then isnull(m.suffix,'')
	when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
	when 'm_company' then isnull(m.company,'')
	when 'm_membernumber' then isnull(m.membernumber,'')
	else isnull(m.prefix,'') end < cv.conditionValueString

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_M_LTE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
inner join dbo.ams_members as m on m.memberID = tmpM.memberID
where tblc.subProc = 'M_LTE'
and case tblc.fieldCode
	when 'm_firstname' then isnull(m.firstname,'')
	when 'm_middlename' then isnull(m.middlename,'')
	when 'm_lastname' then isnull(m.lastname,'')
	when 'm_suffix' then isnull(m.suffix,'')
	when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
	when 'm_company' then isnull(m.company,'')
	when 'm_membernumber' then isnull(m.membernumber,'')
	else isnull(m.prefix,'') end <= cv.conditionValueString

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_M_GT
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
inner join dbo.ams_members as m on m.memberID = tmpM.memberID
where tblc.subProc = 'M_GT'
and case tblc.fieldCode
	when 'm_firstname' then isnull(m.firstname,'')
	when 'm_middlename' then isnull(m.middlename,'')
	when 'm_lastname' then isnull(m.lastname,'')
	when 'm_suffix' then isnull(m.suffix,'')
	when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
	when 'm_company' then isnull(m.company,'')
	when 'm_membernumber' then isnull(m.membernumber,'')
	else isnull(m.prefix,'') end > cv.conditionValueString

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_M_GTE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
inner join dbo.ams_members as m on m.memberID = tmpM.memberID
where tblc.subProc = 'M_GTE'
and case tblc.fieldCode
	when 'm_firstname' then isnull(m.firstname,'')
	when 'm_middlename' then isnull(m.middlename,'')
	when 'm_lastname' then isnull(m.lastname,'')
	when 'm_suffix' then isnull(m.suffix,'')
	when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
	when 'm_company' then isnull(m.company,'')
	when 'm_membernumber' then isnull(m.membernumber,'')
	else isnull(m.prefix,'') end >= cv.conditionValueString
GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_M_EXISTS
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
inner join dbo.ams_members as m on m.memberID = tmpM.memberID
where tblc.subProc = 'M_EXISTS'
and case tblc.fieldCode
	when 'm_firstname' then nullif(m.firstname,'')
	when 'm_middlename' then nullif(m.middlename,'')
	when 'm_lastname' then nullif(m.lastname,'')
	when 'm_suffix' then nullif(m.suffix,'')
	when 'm_professionalsuffix' then nullif(m.professionalsuffix,'')
	when 'm_company' then nullif(m.company,'')
	when 'm_membernumber' then nullif(m.membernumber,'')
	else nullif(m.prefix,'') end is not null

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_M_NOTEXISTS
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
inner join dbo.ams_members as m on m.memberID = tmpM.memberID
where tblc.subProc = 'M_NOTEXISTS'
and case tblc.fieldCode
	when 'm_firstname' then nullif(m.firstname,'')
	when 'm_middlename' then nullif(m.middlename,'')
	when 'm_lastname' then nullif(m.lastname,'')
	when 'm_suffix' then nullif(m.suffix,'')
	when 'm_professionalsuffix' then nullif(m.professionalsuffix,'')
	when 'm_company' then nullif(m.company,'')
	when 'm_membernumber' then nullif(m.membernumber,'')
	else nullif(m.prefix,'') end is null

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_M_CONTAINS
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
inner join dbo.ams_members as m on m.memberID = tmpM.memberID
where tblc.subProc = 'M_CONTAINS'
and case tblc.fieldCode
	when 'm_firstname' then isnull(m.firstname,'')
	when 'm_middlename' then isnull(m.middlename,'')
	when 'm_lastname' then isnull(m.lastname,'')
	when 'm_suffix' then isnull(m.suffix,'')
	when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
	when 'm_company' then isnull(m.company,'')
	when 'm_membernumber' then isnull(m.membernumber,'')
	else isnull(m.prefix,'') end like '%' + cv.conditionValueString + '%'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_M_CONTAINSREGEX
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
inner join dbo.ams_members as m on m.memberID = tmpM.memberID
where tblc.subProc = 'M_CONTAINSREGEX'
-- add back when we have a regexfind fn
-- dbo.fn_RegExFind(vw.columnValue,tblc.value) = 1
and case tblc.fieldCode
	when 'm_firstname' then isnull(m.firstname,'')
	when 'm_middlename' then isnull(m.middlename,'')
	when 'm_lastname' then isnull(m.lastname,'')
	when 'm_suffix' then isnull(m.suffix,'')
	when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
	when 'm_company' then isnull(m.company,'')
	when 'm_membernumber' then isnull(m.membernumber,'')
	else isnull(m.prefix,'') end like '%' + cv.conditionValueString + '%'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_SUB_SUBSCRIBED
AS

-- split sub conditions to get dates
IF OBJECT_ID('tempdb..#tblSubSplit') IS NOT NULL
	DROP TABLE #tblSubSplit
CREATE TABLE #tblSubSplit (conditionID int, subStartDateLower datetime, subStartDateUpper datetime, subEndDateLower datetime, 
	subEndDateUpper datetime, subGraceDateLower datetime, subGraceDateUpper datetime);

insert into #tblSubSplit
select distinct tblc.conditionID, 
	cast(nullif(subStartDateLower.val,'') as datetime) as subStartDateLower, 
	DATEADD(d,1,DATEADD(ms,-3,cast(nullif(subStartDateUpper.val,'') as datetime))) as subStartDateUpper,
	cast(nullif(subEndDateLower.val,'') as datetime) as subEndDateLower, 
	DATEADD(d,1,DATEADD(ms,-3,cast(nullif(subEndDateUpper.val,'') as datetime))) as subEndDateUpper,
	cast(nullif(subGraceDateLower.val,'') as datetime) as subGraceDateLower, 
	DATEADD(d,1,DATEADD(ms,-3,cast(nullif(subGraceDateUpper.val,'') as datetime))) as subGraceDateUpper
from #tblCondALL as tblc
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subStartDateLower'
	WHERE cv.conditionID = tblc.conditionID
) as subStartDateLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subStartDateUpper'
	WHERE cv.conditionID = tblc.conditionID
) as subStartDateUpper(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subEndDateLower'
	WHERE cv.conditionID = tblc.conditionID
) as subEndDateLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subEndDateUpper'
	WHERE cv.conditionID = tblc.conditionID
) as subEndDateUpper(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subGraceDateLower'
	WHERE cv.conditionID = tblc.conditionID
) as subGraceDateLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subGraceDateUpper'
	WHERE cv.conditionID = tblc.conditionID
) as subGraceDateUpper(val)
where subProc = 'SUB_SUBSCRIBED'


-- split sub types
IF OBJECT_ID('tempdb..#tblSubTypeSplit') IS NOT NULL
	DROP TABLE #tblSubTypeSplit
CREATE TABLE #tblSubTypeSplit (conditionID int, subTypeID int);

insert into #tblSubTypeSplit (conditionID, subTypeID)
select tblc.conditionID, cast(subType.val as int)
from #tblCondALL as tblc
outer apply (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID 
		and k.conditionKey = 'subSubType'
	WHERE cv.conditionID = tblc.conditionID
) as subType(val)
where subProc = 'SUB_SUBSCRIBED'

CREATE INDEX IX_tblSubTypeSplit_conditionID ON #tblSubTypeSplit (conditionID asc);
CREATE INDEX IX_tblSubTypeSplit_subTypeID ON #tblSubTypeSplit (subTypeID asc);


-- split sub subscriptions
IF OBJECT_ID('tempdb..#tblSubSubcriptionSplit') IS NOT NULL
	DROP TABLE #tblSubSubcriptionSplit
CREATE TABLE #tblSubSubcriptionSplit (conditionID int, subscriptionID int);

insert into #tblSubSubcriptionSplit (conditionID, subscriptionID)
select tblc.conditionID, cast(sub.val as int)
from #tblCondALL as tblc
outer apply (
	SELECT isnull(nullif(cv.conditionValue,''),0) as conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID 
		and k.conditionKey = 'subSubscription'
	WHERE cv.conditionID = tblc.conditionID
) as sub(val)
where subProc = 'SUB_SUBSCRIBED'

CREATE INDEX IX_tblSubSubcriptionSplit_conditionID ON #tblSubSubcriptionSplit (conditionID asc);
CREATE INDEX IX_tblSubSubcriptionSplit_subscriptionID ON #tblSubSubcriptionSplit (subscriptionID asc);


-- split sub rates
IF OBJECT_ID('tempdb..#tblSubRateSplit') IS NOT NULL
	DROP TABLE #tblSubRateSplit
CREATE TABLE #tblSubRateSplit (conditionID int, rateID int);

insert into #tblSubRateSplit (conditionID, rateID)
select tblc.conditionID, cast(subRate.val as int)
from #tblCondALL as tblc
outer apply (
	SELECT isnull(nullif(cv.conditionValue,''),0) as conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID 
		and k.conditionKey = 'subRate'
	WHERE cv.conditionID = tblc.conditionID
) as subRate(val)
where subProc = 'SUB_SUBSCRIBED'

CREATE INDEX IX_tblSubRateSplit_conditionID ON #tblSubRateSplit (conditionID asc);
CREATE INDEX IX_tblSubRateSplit_rateID ON #tblSubRateSplit (rateID asc);


-- split sub statuses
IF OBJECT_ID('tempdb..#tblSubStatusSplit') IS NOT NULL
	DROP TABLE #tblSubStatusSplit
CREATE TABLE #tblSubStatusSplit (conditionID int, statusID int);

insert into #tblSubStatusSplit (conditionID, statusID)
select tblc.conditionID, isnull(nullif(subStatus.val,''),0) as conditionValue
from #tblCondALL as tblc
outer apply (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID 
		and k.conditionKey = 'subStatus'
	WHERE cv.conditionID = tblc.conditionID
) as subStatus(val)
where subProc = 'SUB_SUBSCRIBED'

CREATE INDEX IX_tblSubStatusSplit_conditionID ON #tblSubStatusSplit (conditionID asc);
CREATE INDEX IX_tblSubStatusSplit_statusID ON #tblSubStatusSplit (statusID asc);


-- split pay statuses
IF OBJECT_ID('tempdb..#tblSubPaymentStatusSplit') IS NOT NULL
	DROP TABLE #tblSubPaymentStatusSplit
CREATE TABLE #tblSubPaymentStatusSplit (conditionID int, statusID int);

insert into #tblSubPaymentStatusSplit (conditionID, statusID)
select tblc.conditionID, isnull(nullif(subPaymentStatus.val,''),0) as conditionValue
from #tblCondALL as tblc
outer apply (
	SELECT isnull(nullif(cv.conditionValue,''),0) as conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID 
		and k.conditionKey = 'subPaymentStatus'
	WHERE cv.conditionID = tblc.conditionID
) as subPaymentStatus(val)
where subProc = 'SUB_SUBSCRIBED'

CREATE INDEX IX_tblSubPaymentStatusSplit_conditionID ON #tblSubPaymentStatusSplit (conditionID asc);
CREATE INDEX IX_tblSubPaymentStatusSplit_statusID ON #tblSubPaymentStatusSplit (statusID asc);


-- split frequencies
IF OBJECT_ID('tempdb..#tblSubFrequencySplit') IS NOT NULL
	DROP TABLE #tblSubFrequencySplit
CREATE TABLE #tblSubFrequencySplit (conditionID int, frequencyID int);

insert into #tblSubFrequencySplit (conditionID, frequencyID)
select tblc.conditionID, isnull(nullif(subFreq.val,''),0) as conditionValue
from #tblCondALL as tblc
outer apply (
	SELECT isnull(nullif(cv.conditionValue,''),0) as conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID 
		and k.conditionKey = 'subFrequency'
	WHERE cv.conditionID = tblc.conditionID
) as subFreq(val)
where subProc = 'SUB_SUBSCRIBED'

CREATE INDEX IX_tblSubFrequencySplit_conditionID ON #tblSubFrequencySplit (conditionID asc);
CREATE INDEX IX_tblSubFrequencySplit_frequencyID ON #tblSubFrequencySplit (frequencyID asc);


insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
	inner join #tblSubTypeSplit as subTypeSplit on subTypeSplit.conditionID = tblc.conditionID
	inner join #tblSubSubcriptionSplit as subSubscriptionSplit on subSubscriptionSplit.conditionID = tblc.conditionID
	inner join #tblSubRateSplit as subRateSplit on subRateSplit.conditionID = tblc.conditionID
	inner join #tblSubStatusSplit as subStatusSplit on subStatusSplit.conditionID = tblc.conditionID
	inner join #tblSubPaymentStatusSplit as subPaymentStatusSplit on subPaymentStatusSplit.conditionID = tblc.conditionID
	inner join #tblSubFrequencySplit as subFrequencySplit on subFrequencySplit.conditionID = tblc.conditionID
inner join #tblSubSplit as subsplit on subsplit.conditionID = tblc.conditionID
inner join dbo.sub_subscriptions as s on subTypeSplit.subTypeID = s.typeID
	and subSubscriptionSplit.subscriptionID in (0,s.subscriptionID)
inner join dbo.sub_rateFrequencies as rf on subRateSplit.rateID in (0,rf.rateID)
inner join dbo.sub_frequencies f on rf.frequencyID = f.frequencyID 
	and subFrequencySplit.frequencyID in (0,f.frequencyID)
inner join dbo.sub_subscribers as sub on rf.rfid = sub.RFID
	and s.subscriptionID = sub.subscriptionID
	and subStatusSplit.statusID = sub.statusID
	and subPaymentStatusSplit.statusID in (0,sub.paymentStatusID)
	and 1 = 
		case 
		when subsplit.subStartDateLower is null and subsplit.subStartDateUpper is null then 1
		when subsplit.subStartDateLower is not null and subsplit.subStartDateUpper is not null and sub.subStartDate between subsplit.subStartDateLower and subsplit.subStartDateUpper then 1 
		when subsplit.subStartDateLower is not null and subsplit.subStartDateUpper is null and sub.subStartDate >= subsplit.subStartDateLower then 1
		when subsplit.subStartDateUpper is not null and subsplit.subStartDateLower is null and sub.subStartDate <= subsplit.subStartDateUpper then 1
		else 0 end
	and 1 = 
		case 
		when subsplit.subEndDateLower is null and subsplit.subEndDateUpper is null then 1
		when subsplit.subEndDateLower is not null and subsplit.subEndDateUpper is not null and sub.subEndDate between subsplit.subEndDateLower and subsplit.subEndDateUpper then 1 
		when subsplit.subEndDateLower is not null and subsplit.subEndDateUpper is null and sub.subEndDate >= subsplit.subEndDateLower then 1
		when subsplit.subEndDateUpper is not null and subsplit.subEndDateLower is null and sub.subEndDate <= subsplit.subEndDateUpper then 1
		else 0 end
	and 1 = 
		case 
		when subsplit.subGraceDateLower is null and subsplit.subGraceDateUpper is null then 1
		when subsplit.subGraceDateLower is not null and subsplit.subGraceDateUpper is not null and sub.graceEndDate between subsplit.subGraceDateLower and subsplit.subGraceDateUpper then 1 
		when subsplit.subGraceDateLower is not null and subsplit.subGraceDateUpper is null and sub.graceEndDate >= subsplit.subGraceDateLower then 1
		when subsplit.subGraceDateUpper is not null and subsplit.subGraceDateLower is null and sub.graceEndDate <= subsplit.subGraceDateUpper then 1
		else 0 end
inner join dbo.ams_members as m2 on m2.memberid = sub.memberid
inner join #tblMembers as m on m.memberID = m2.activeMemberID
where tblc.subProc = 'SUB_SUBSCRIBED'

IF OBJECT_ID('tempdb..#tblSubSplit') IS NOT NULL
	DROP TABLE #tblSubSplit
IF OBJECT_ID('tempdb..#tblSubTypeSplit') IS NOT NULL
	DROP TABLE #tblSubTypeSplit
IF OBJECT_ID('tempdb..#tblSubSubcriptionSplit') IS NOT NULL
	DROP TABLE #tblSubSubcriptionSplit
IF OBJECT_ID('tempdb..#tblSubRateSplit') IS NOT NULL
	DROP TABLE #tblSubRateSplit
IF OBJECT_ID('tempdb..#tblSubStatusSplit') IS NOT NULL
	DROP TABLE #tblSubStatusSplit
IF OBJECT_ID('tempdb..#tblSubPaymentStatusSplit') IS NOT NULL
	DROP TABLE #tblSubPaymentStatusSplit
IF OBJECT_ID('tempdb..#tblSubFrequencySplit') IS NOT NULL
	DROP TABLE #tblSubFrequencySplit

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MW_EQ
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberWebsites as mw on mw.websiteTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberID = mw.memberID
where tblc.subProc = 'MW_EQ'
and isnull(mw.website,'') = cv.conditionValueString

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MW_NEQ
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
inner join dbo.ams_members as m on m.memberID = tmpM.memberID
where tblc.subProc = 'MW_NEQ'
and not exists (
	select mw.memberID
	from dbo.ams_memberWebsites as mw
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
		and isnull(mw.website,'') = cv.conditionValueString
	where mw.websiteTypeID = tblc.fieldCodeAreaID
	and mw.memberID = m.memberid
)	

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MW_LT
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberWebsites as mw on mw.websiteTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberID = mw.memberID
where tblc.subProc = 'MW_LT'
and isnull(mw.website,'') < cv.conditionValueString

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MW_LTE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberWebsites as mw on mw.websiteTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberID = mw.memberID
where tblc.subProc = 'MW_LTE'
and isnull(mw.website,'') <= cv.conditionValueString

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MW_GT
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberWebsites as mw on mw.websiteTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberID = mw.memberID
where tblc.subProc = 'MW_GT'
and isnull(mw.website,'') > cv.conditionValueString

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MW_GTE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberWebsites as mw on mw.websiteTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberID = mw.memberID
where tblc.subProc = 'MW_GTE'
and isnull(mw.website,'') >= cv.conditionValueString

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MW_EXISTS
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join dbo.ams_memberWebsites as mw on mw.websiteTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberID = mw.memberID
where tblc.subProc = 'MW_EXISTS'
and nullif(mw.website,'') is not null

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MW_NOTEXISTS
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberID = m.memberID
where tblc.subProc = 'MW_NOTEXISTS'
and not exists (
	select mw.memberID
	from dbo.ams_memberWebsites as mw
	where mw.websiteTypeID = tblc.fieldCodeAreaID
	and mw.memberID = m.memberid
	and nullif(mw.website,'') is not null
)

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MW_CONTAINS
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberWebsites as mw on mw.websiteTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberID = mw.memberID
where tblc.subProc = 'MW_CONTAINS'
and isnull(mw.website,'') like '%' + cv.conditionValueString + '%'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MW_CONTAINSREGEX
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberWebsites as mw on mw.websiteTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberID = mw.memberID
where tblc.subProc = 'MW_CONTAINSREGEX'
-- add back when we have a regexfind fn
-- dbo.fn_RegExFind(mw.website,c.value) = 1
and isnull(mw.website,'') like '%' + cv.conditionValueString + '%'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_ME_EQ
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberEmails as me on me.emailTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberID = me.memberID
where tblc.subProc = 'ME_EQ'
and isnull(me.email,'') = cv.conditionValueString

GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_ME_NEQ
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberID = m.memberID
where tblc.subProc = 'ME_NEQ'
and not exists (
	select me.memberID
	from dbo.ams_memberEmails as me
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
		and isnull(me.email,'') = cv.conditionValueString
	where me.emailTypeID = tblc.fieldCodeAreaID
	and me.memberID = m.memberid
)	

GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_ME_LT
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberEmails as me on me.emailTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberID = me.memberID
where tblc.subProc = 'ME_LT'
and isnull(me.email,'') < cv.conditionValueString

GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_ME_LTE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberEmails as me on me.emailTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberID = me.memberID
where tblc.subProc = 'ME_LTE'
and isnull(me.email,'') <= cv.conditionValueString

GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_ME_GT
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberEmails as me on me.emailTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberID = me.memberID
where tblc.subProc = 'ME_GT'
and isnull(me.email,'') > cv.conditionValueString

GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_ME_GTE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberEmails as me on me.emailTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberID = me.memberID
where tblc.subProc = 'ME_GTE'
and isnull(me.email,'') >= cv.conditionValueString

GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_ME_EXISTS
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join dbo.ams_memberEmails as me on me.emailTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberID = me.memberID
where tblc.subProc = 'ME_EXISTS'
and nullif(me.email,'') is not null

GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_ME_NOTEXISTS
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberID = m.memberID
where tblc.subProc = 'ME_NOTEXISTS'
and not exists (
	select me.memberID
	from dbo.ams_memberEmails as me
	where me.emailTypeID = tblc.fieldCodeAreaID
	and me.memberID = m.memberid
	and nullif(me.email,'') is not null
)

GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_ME_CONTAINS
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberEmails as me on me.emailTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberID = me.memberID
where tblc.subProc = 'ME_CONTAINS'
and isnull(me.email,'') like '%' + cv.conditionValueString + '%'

GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_ME_CONTAINSREGEX
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberEmails as me on me.emailTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberID = me.memberID
where tblc.subProc = 'ME_CONTAINSREGEX'
-- add back when we have a regexfind fn
-- dbo.fn_RegExFind(me.email,tblc.value) = 1
and isnull(me.email,'') like '%' + cv.conditionValueString + '%'

GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_MP_EQ
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID and mp.phoneTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MP_EQ'
and isnull(mp.phone,'') = cv.conditionValueString

GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_MP_NEQ
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberID = m.memberID
where tblc.subProc = 'MP_NEQ'
and not exists (
	select ma.memberid
	from dbo.ams_memberAddresses as ma 
	inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID
		and mp.phoneTypeID = tblc.fieldCodeAreaID
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
		and isnull(mp.phone,'') = cv.conditionValueString
	where ma.memberid = m.memberid
	and ma.addressTypeID = tblc.fieldCodeAreaPartA
)

GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_MP_LT
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID and mp.phoneTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MP_LT'
and isnull(mp.phone,'') < cv.conditionValueString

GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_MP_LTE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID and mp.phoneTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MP_LTE'
and isnull(mp.phone,'') <= cv.conditionValueString

GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_MP_GT
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID and mp.phoneTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MP_GT'
and isnull(mp.phone,'') > cv.conditionValueString

GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_MP_GTE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID and mp.phoneTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MP_GTE'
and isnull(mp.phone,'') >= cv.conditionValueString

GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_MP_EXISTS
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID and mp.phoneTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MP_EXISTS'
and nullif(mp.phone,'') is not null

GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_MP_NOTEXISTS
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberID = m.memberID
where tblc.subProc = 'MP_NOTEXISTS'
and not exists (
	select ma.memberid
	from dbo.ams_memberAddresses as ma 
	inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID
		and mp.phoneTypeID = tblc.fieldCodeAreaID
	where ma.memberid = m.memberid
	and ma.addressTypeID = tblc.fieldCodeAreaPartA
	and nullif(mp.phone,'') is not null
)

GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_MP_CONTAINS
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID and mp.phoneTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MP_CONTAINS'
and isnull(mp.phone,'') like '%' + cv.conditionValueString + '%'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MP_CONTAINSREGEX
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID and mp.phoneTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MP_CONTAINSREGEX'
-- add back when we have a regexfind fn
-- dbo.fn_RegExFind(mp.phone,tblc.value) = 1
and isnull(mp.phone,'') like '%' + cv.conditionValueString + '%'

GO



CREATE PROC dbo.cache_members_populateMemberConditionCache_MA_NEQ
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberID = m.memberID
where tblc.subProc = 'MA_NEQ'
and tblc.displayTypeCode not in ('RADIO','SELECT')
and not exists (
	select ma.memberid
	from dbo.ams_memberAddresses as ma
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	where ma.memberid = m.memberid
	and ma.addressTypeID = tblc.fieldCodeAreaID
	and tblc.displayTypeCode not in ('RADIO','SELECT')
	and case tblc.fieldCodeAreaPartA
		when 'address1' then isnull(ma.address1,'') 
		when 'address2' then isnull(ma.address2,'') 
		when 'address3' then isnull(ma.address3,'') 
		when 'city' then isnull(ma.city,'') 
		when 'postalcode' then isnull(ma.postalcode,'') 
		when 'county' then isnull(ma.county,'') 
		else isnull(ma.attn,'') end = cv.conditionValueString
)

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberID = m.memberID
where tblc.subProc = 'MA_NEQ'
and tblc.displayTypeCode in ('RADIO','SELECT')
and not exists (
	select ma.memberid
	from dbo.ams_memberAddresses as ma
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	where ma.memberid = m.memberid
	and ma.addressTypeID = tblc.fieldCodeAreaID
	and tblc.displayTypeCode in ('RADIO','SELECT')
	and case tblc.fieldCodeAreaPartA
		when 'stateprov' then isnull(ma.stateid,0) 
		else isnull(ma.countryid,0) end = cv.conditionValueInteger
)

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MA_LT
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MA_LT'
and case tblc.fieldCodeAreaPartA
	when 'address1' then isnull(ma.address1,'') 
	when 'address2' then isnull(ma.address2,'') 
	when 'address3' then isnull(ma.address3,'') 
	when 'city' then isnull(ma.city,'') 
	when 'postalcode' then isnull(ma.postalcode,'') 
	when 'county' then isnull(ma.county,'') 
	else isnull(ma.attn,'') end < cv.conditionValueString

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MA_LTE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MA_LTE'
and case tblc.fieldCodeAreaPartA
	when 'address1' then isnull(ma.address1,'') 
	when 'address2' then isnull(ma.address2,'') 
	when 'address3' then isnull(ma.address3,'') 
	when 'city' then isnull(ma.city,'') 
	when 'postalcode' then isnull(ma.postalcode,'') 
	when 'county' then isnull(ma.county,'') 
	else isnull(ma.attn,'') end <= cv.conditionValueString

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MA_GT
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MA_GT'
and case tblc.fieldCodeAreaPartA
	when 'address1' then isnull(ma.address1,'') 
	when 'address2' then isnull(ma.address2,'') 
	when 'address3' then isnull(ma.address3,'') 
	when 'city' then isnull(ma.city,'') 
	when 'postalcode' then isnull(ma.postalcode,'') 
	when 'county' then isnull(ma.county,'') 
	else isnull(ma.attn,'') end > cv.conditionValueString

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MA_GTE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MA_GTE'
and case tblc.fieldCodeAreaPartA
	when 'address1' then isnull(ma.address1,'') 
	when 'address2' then isnull(ma.address2,'') 
	when 'address3' then isnull(ma.address3,'') 
	when 'city' then isnull(ma.city,'') 
	when 'postalcode' then isnull(ma.postalcode,'') 
	when 'county' then isnull(ma.county,'') 
	else isnull(ma.attn,'') end >= cv.conditionValueString

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MA_EXISTS
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MA_EXISTS'
and tblc.displayTypeCode not in ('RADIO','SELECT')
and case tblc.fieldCodeAreaPartA
	when 'address1' then nullif(ma.address1,'') 
	when 'address2' then nullif(ma.address2,'') 
	when 'address3' then nullif(ma.address3,'') 
	when 'city' then nullif(ma.city,'') 
	when 'postalcode' then nullif(ma.postalcode,'') 
	when 'county' then nullif(ma.county,'') 
	else nullif(ma.attn,'') end is not null

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MA_EXISTS'
and tblc.displayTypeCode in ('RADIO','SELECT')
and case tblc.fieldCodeAreaPartA
	when 'stateprov' then ma.stateid
	else ma.countryid end is not null

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MA_NOTEXISTS
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberID = m.memberID
where tblc.subProc = 'MA_NOTEXISTS'
and tblc.displayTypeCode not in ('RADIO','SELECT')
and not exists (
	select ma.memberid
	from dbo.ams_memberAddresses as ma
	where ma.memberid = m.memberid
	and ma.addressTypeID = tblc.fieldCodeAreaID
	and tblc.displayTypeCode not in ('RADIO','SELECT')
	and case tblc.fieldCodeAreaPartA
		when 'address1' then nullif(ma.address1,'') 
		when 'address2' then nullif(ma.address2,'') 
		when 'address3' then nullif(ma.address3,'') 
		when 'city' then nullif(ma.city,'') 
		when 'postalcode' then nullif(ma.postalcode,'') 
		when 'county' then nullif(ma.county,'') 
		else nullif(ma.attn,'') end is not null
)

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberID = m.memberID
where tblc.subProc = 'MA_NOTEXISTS'
and tblc.displayTypeCode in ('RADIO','SELECT')
and not exists (
	select ma.memberid
	from dbo.ams_memberAddresses as ma
	where ma.memberid = m.memberid
	and ma.addressTypeID = tblc.fieldCodeAreaID
	and tblc.displayTypeCode in ('RADIO','SELECT')
	and case tblc.fieldCodeAreaPartA
		when 'stateprov' then ma.stateid
		else ma.countryid end is not null
)

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MA_CONTAINS
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MA_CONTAINS'
and case tblc.fieldCodeAreaPartA
	when 'address1' then isnull(ma.address1,'') 
	when 'address2' then isnull(ma.address2,'') 
	when 'address3' then isnull(ma.address3,'') 
	when 'city' then isnull(ma.city,'') 
	when 'postalcode' then isnull(ma.postalcode,'') 
	when 'county' then isnull(ma.county,'') 
	else isnull(ma.attn,'') end like '%' + cv.conditionValueString + '%'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MA_CONTAINSREGEX
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MA_CONTAINSREGEX'
and case tblc.fieldCodeAreaPartA
	when 'address1' then isnull(ma.address1,'') 
	when 'address2' then isnull(ma.address2,'') 
	when 'address3' then isnull(ma.address3,'') 
	when 'city' then isnull(ma.city,'') 
	when 'postalcode' then isnull(ma.postalcode,'') 
	when 'county' then isnull(ma.county,'') 
	else isnull(ma.attn,'') end like '%' + cv.conditionValueString + '%'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MPL_NEQ_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberID = m.memberID
where tblc.subProc = 'MPL_NEQ_STRING'
and not exists (
	select mpl.memberid
	from dbo.ams_memberProfessionalLicenses as mpl
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
	where mpl.PLTypeID = tblc.fieldCodeAreaID
	and mpl.memberid = m.memberid
	and case tblc.fieldCodeAreaPartA
		when 'status' then isnull(mpls.statusName,'') 
		else isnull(mpl.licensenumber,'') end = cv.conditionValueString
)

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MPL_LT_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_LT_STRING'
and case tblc.fieldCodeAreaPartA
	when 'status' then isnull(mpls.statusName,'') 
	else isnull(mpl.licensenumber,'') end < cv.conditionValueString

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MPL_LTE_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_LTE_STRING'
and case tblc.fieldCodeAreaPartA
	when 'status' then isnull(mpls.statusName,'') 
	else isnull(mpl.licensenumber,'') end <= cv.conditionValueString

GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_MPL_GT_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_GT_STRING'
and case tblc.fieldCodeAreaPartA
	when 'status' then isnull(mpls.statusName,'') 
	else isnull(mpl.licensenumber,'') end > cv.conditionValueString

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MPL_GTE_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_GTE_STRING'
and case tblc.fieldCodeAreaPartA
	when 'status' then isnull(mpls.statusName,'') 
	else isnull(mpl.licensenumber,'') end >= cv.conditionValueString

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MPL_NOTEXISTS_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberID = m.memberID
where tblc.subProc = 'MPL_NOTEXISTS_STRING'
and not exists (
	select mpl.memberid
	from dbo.ams_memberProfessionalLicenses as mpl 
		left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on 
			mpls.PLStatusID = mpl.PLStatusID
	where mpl.PLTypeID = tblc.fieldCodeAreaID
	and mpl.memberid = m.memberid
	and case tblc.fieldCodeAreaPartA
		when 'status' then nullif(mpls.StatusName,'')
		else nullif(mpl.licensenumber,'') end is not null
)

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MPL_CONTAINS
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_CONTAINS'
and case tblc.fieldCodeAreaPartA
	when 'status' then isnull(mpls.statusName,'') 
	else isnull(mpl.licensenumber,'') end like '%' + cv.conditionValueString + '%'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MPL_CONTAINSREGEX
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_CONTAINSREGEX'
and case tblc.fieldCodeAreaPartA
	when 'status' then isnull(mpls.statusName,'') 
	else isnull(mpl.licensenumber,'') end like '%' + cv.conditionValueString + '%'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MPL_EQ_DATE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_EQ_DATE'
and mpl.activeDate = cv.conditionValueDate

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MPL_NEQ_DATE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberid = m.memberid
where tblc.subProc = 'MPL_NEQ_DATE'
and not exists (
	select mpl.memberid
	from dbo.ams_memberProfessionalLicenses as mpl 
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
		and mpl.activeDate = cv.conditionValueDate
	where mpl.PLTypeID = tblc.fieldCodeAreaID
	and mpl.memberid = m.memberid
)

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MPL_LT_DATE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_LT_DATE'
and mpl.activeDate < cv.conditionValueDate

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MPL_LTE_DATE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_LTE_DATE'
and mpl.activeDate <= cv.conditionValueDate

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MPL_GT_DATE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_GT_DATE'
and mpl.activeDate > cv.conditionValueDate

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MPL_GTE_DATE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_GTE_DATE'
and mpl.activeDate >= cv.conditionValueDate

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MPL_EXISTS_DATE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_EXISTS_DATE'
and mpl.activeDate is not null

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MPL_NOTEXISTS_DATE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberid = m.memberid
where tblc.subProc = 'MPL_NOTEXISTS_DATE'
and not exists (
	select mpl.memberid
	from dbo.ams_memberProfessionalLicenses as mpl 
	where mpl.PLTypeID = tblc.fieldCodeAreaID
	and mpl.memberid = m.memberid
	and mpl.activeDate is not null
)

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MPL_DATEPART
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'eq'
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datepart(q,mpl.activeDate) 
		when 'm' then datepart(m,mpl.activeDate) 
		when 'wk' then datepart(wk,mpl.activeDate)
		when 'dw' then datepart(dw,mpl.activeDate)
		when 'd' then datepart(d,mpl.activeDate) 
		when 'dy' then datepart(dy,mpl.activeDate)
		else datepart(yy,mpl.activeDate) end = cv.conditionValueInteger
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_DATEPART'

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'neq'
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datepart(q,mpl.activeDate) 
		when 'm' then datepart(m,mpl.activeDate) 
		when 'wk' then datepart(wk,mpl.activeDate)
		when 'dw' then datepart(dw,mpl.activeDate)
		when 'd' then datepart(d,mpl.activeDate) 
		when 'dy' then datepart(dy,mpl.activeDate)
		else datepart(yy,mpl.activeDate) end <> cv.conditionValueInteger
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_DATEPART'

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lt'
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datepart(q,mpl.activeDate) 
		when 'm' then datepart(m,mpl.activeDate) 
		when 'wk' then datepart(wk,mpl.activeDate)
		when 'dw' then datepart(dw,mpl.activeDate)
		when 'd' then datepart(d,mpl.activeDate) 
		when 'dy' then datepart(dy,mpl.activeDate)
		else datepart(yy,mpl.activeDate) end < cv.conditionValueInteger
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_DATEPART'

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lte'
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datepart(q,mpl.activeDate) 
		when 'm' then datepart(m,mpl.activeDate) 
		when 'wk' then datepart(wk,mpl.activeDate)
		when 'dw' then datepart(dw,mpl.activeDate)
		when 'd' then datepart(d,mpl.activeDate) 
		when 'dy' then datepart(dy,mpl.activeDate)
		else datepart(yy,mpl.activeDate) end <= cv.conditionValueInteger
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_DATEPART'

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gt'
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datepart(q,mpl.activeDate) 
		when 'm' then datepart(m,mpl.activeDate) 
		when 'wk' then datepart(wk,mpl.activeDate)
		when 'dw' then datepart(dw,mpl.activeDate)
		when 'd' then datepart(d,mpl.activeDate) 
		when 'dy' then datepart(dy,mpl.activeDate)
		else datepart(yy,mpl.activeDate) end > cv.conditionValueInteger
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_DATEPART'

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gte'
inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	and case c.[datePart]
		when 'q' then datepart(q,mpl.activeDate) 
		when 'm' then datepart(m,mpl.activeDate) 
		when 'wk' then datepart(wk,mpl.activeDate)
		when 'dw' then datepart(dw,mpl.activeDate)
		when 'd' then datepart(d,mpl.activeDate) 
		when 'dy' then datepart(dy,mpl.activeDate)
		else datepart(yy,mpl.activeDate) end >= cv.conditionValueInteger
inner join #tblMembers as m on m.memberid = mpl.memberid
where tblc.subProc = 'MPL_DATEPART'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_E_ATTENDED
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join dbo.ev_events as e on e.eventID = tblc.fieldCodeAreaID
inner join dbo.ev_registration as er on er.eventID = e.eventID and er.status = 'A'
inner join dbo.ev_registrants as reg on reg.registrationID = er.registrationID and reg.status = 'A'
	and reg.attended = 1
inner join dbo.ams_members as m2 on m2.memberid = reg.memberid
inner join #tblMembers as m on m.memberID = m2.activeMemberID
where tblc.subProc = 'E_ATTENDED'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_E_AWARDED
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join dbo.ev_events as e on e.eventID = tblc.fieldCodeAreaID
inner join dbo.ev_registration as er on er.eventID = e.eventID and er.status = 'A'
inner join dbo.ev_registrants as reg on reg.registrationID = er.registrationID and reg.status = 'A'
	and reg.attended = 1
inner join dbo.crd_requests as rc on rc.registrantID = reg.registrantID
	and rc.creditAwarded = 1
inner join dbo.ams_members as m2 on m2.memberid = reg.memberid
inner join #tblMembers as m on m.memberID = m2.activeMemberID
where tblc.subProc = 'E_AWARDED'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MAD_EQ
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
inner join #tblMembers as m on m.memberid = ma.memberid
inner join dbo.ams_memberAddressData as mad on mad.addressid = ma.addressID
inner join dbo.ams_memberDistrictValues as mdv on mdv.valueID = mad.valueID
	and mdv.districtTypeID = tblc.fieldCodeAreaID
	and mdv.valueID = cv.conditionValueInteger
where tblc.subProc = 'MAD_EQ'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MAD_NEQ
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberid = m.memberID
where tblc.subProc = 'MAD_NEQ'
and not exists (
	select ma.memberid
	from dbo.ams_memberAddresses as ma
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddressData as mad on mad.addressid = ma.addressID
	inner join dbo.ams_memberDistrictValues as mdv on mdv.valueID = mad.valueID
		and mdv.districtTypeID = tblc.fieldCodeAreaID
		and mdv.valueID = cv.conditionValueInteger
	where ma.memberid = m.memberid
	and ma.addressTypeID = tblc.fieldCodeAreaPartA
)
	
GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MAD_EXISTS
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
inner join #tblMembers as m on m.memberid = ma.memberid
inner join dbo.ams_memberAddressData as mad on mad.addressid = ma.addressID
inner join dbo.ams_memberDistrictValues as mdv on mdv.valueID = mad.valueID
	and mdv.districtTypeID = tblc.fieldCodeAreaID
where tblc.subProc = 'MAD_EXISTS'

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MAD_NOTEXISTS
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberid = m.memberid
where tblc.subProc = 'MAD_NOTEXISTS'
and not exists (
	select ma.memberid
	from dbo.ams_memberAddresses as ma
	inner join dbo.ams_memberAddressData as mad on mad.addressid = ma.addressID
	inner join dbo.ams_memberDistrictValues as mdv on mdv.valueID = mad.valueID
		and mdv.districtTypeID = tblc.fieldCodeAreaID
	where ma.memberid = m.memberid
	and ma.addressTypeID = tblc.fieldCodeAreaPartA
)

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_splitAcct
AS

insert into #tblAccSplit
select distinct tblc.conditionID, LEFT(gllist.list, LEN(gllist.list)-1) as revenueGLs, 
	cast(batchDateLower.val as datetime) as batchDateLower, 
	DATEADD(d,1,DATEADD(ms,-3,cast(batchDateUpper.val as datetime))) as batchDateUpper,
	linkAllocType.val as revOrCash, 
	cast(value.val as money) as conditionValue, 
	cast(valueLower.val as money) as conditionValueLower, 
	cast(valueUpper.val as money) as conditionValueUpper 
from #tblCondALL as tblc
CROSS APPLY ( 
	SELECT cv.conditionValue + ',' AS [text()] 
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'revenueGL'
	WHERE cv.conditionID = tblc.conditionID
	FOR XML PATH('')
) as gllist(list) 
CROSS APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'batchDateLower'
	WHERE cv.conditionID = tblc.conditionID
) as batchDateLower(val)
CROSS APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'batchDateUpper'
	WHERE cv.conditionID = tblc.conditionID
) as batchDateUpper(val)
CROSS APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'linkAllocType'
	WHERE cv.conditionID = tblc.conditionID
) as linkAllocType(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'value'
	WHERE cv.conditionID = tblc.conditionID
) as value(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'valueLower'
	WHERE cv.conditionID = tblc.conditionID
) as valueLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'valueUpper'
	WHERE cv.conditionID = tblc.conditionID
) as valueUpper(val)
where fieldCodeArea = 'Accounting'

CREATE INDEX IX_tblACCSplit_conditionID ON #tblAccSplit (conditionID asc);

insert into #tblAccSplitGL
select conditionID, revGL.listItem as revenueGL
from #tblAccSplit
cross apply dbo.fn_intListToTable(revenueGLs,',') as revGL

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_ACCT_EQ_ALLOCSUM
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_EQ_ALLOCSUM'	
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_EQ_ALLOCSUM'	
) as tmp
group by memberid, conditionID, conditionValue
having sum(allocAmt) = conditionValue

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_ACCT_NEQ_ALLOCSUM
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_NEQ_ALLOCSUM'
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_NEQ_ALLOCSUM'	
) as tmp
group by memberid, conditionID, conditionValue
having sum(allocAmt) <> conditionValue

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_ACCT_LT_ALLOCSUM
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_LT_ALLOCSUM'	
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_LT_ALLOCSUM'	
) as tmp
group by memberid, conditionID, conditionValue
having sum(allocAmt) < conditionValue

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_ACCT_LTE_ALLOCSUM
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_LTE_ALLOCSUM'	
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_LTE_ALLOCSUM'	
) as tmp
group by memberid, conditionID, conditionValue
having sum(allocAmt) <= conditionValue

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_ACCT_GT_ALLOCSUM
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_GT_ALLOCSUM'	
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_GT_ALLOCSUM'	
) as tmp
group by memberid, conditionID, conditionValue
having sum(allocAmt) > conditionValue

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_ACCT_GTE_ALLOCSUM
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_GTE_ALLOCSUM'	
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_GTE_ALLOCSUM'	
) as tmp
group by memberid, conditionID, conditionValue
having sum(allocAmt) >= conditionValue

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_ACCT_BETWEEN_ALLOCSUM
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValueLower, accsplit.conditionValueUpper
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_BETWEEN_ALLOCSUM'
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValueLower, accsplit.conditionValueUpper
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_BETWEEN_ALLOCSUM'
) as tmp
group by memberid, conditionID, conditionValueLower, conditionValueUpper
having sum(allocAmt) between conditionValueLower and conditionValueUpper

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_ACCT_EXISTS_ALLOCSUM
AS

insert into #cache_members_conditions_shouldbe
select m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
inner join #tblMembers as m on m.memberid = m2.activeMemberID
inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
where tblc.subProc = 'ACCT_EXISTS_ALLOCSUM'
	union
select m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
inner join #tblMembers as m on m.memberid = m2.activeMemberID
inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
where tblc.subProc = 'ACCT_EXISTS_ALLOCSUM' 

GO

	
CREATE PROC dbo.cache_members_populateMemberConditionCache_ACCT_NOTEXISTS_ALLOCSUM
AS

IF OBJECT_ID('tempdb..#tblCondAccNotExist') IS NOT NULL
	DROP TABLE #tblCondAccNotExist

select m.memberid, tblc.conditionID
into #tblCondAccNotExist
from #tblCondALL as tblc
inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
inner join #tblMembers as m on m.memberid = m2.activeMemberID
inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
where tblc.subProc = 'ACCT_NOTEXISTS_ALLOCSUM' 
	union
select m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
inner join #tblMembers as m on m.memberid = m2.activeMemberID
inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
where tblc.subProc = 'ACCT_NOTEXISTS_ALLOCSUM' 

insert into #cache_members_conditions_shouldbe
select distinct mOuter.memberid, tblcOuter.conditionID
from #tblCondALL as tblcOuter
inner join #tblMembers as mOuter on mOuter.memberid = mOuter.memberID
where tblcOuter.subProc = 'ACCT_NOTEXISTS_ALLOCSUM' 
	except
select memberid, conditionID
from #tblCondAccNotExist

IF OBJECT_ID('tempdb..#tblCondAccNotExist') IS NOT NULL
	DROP TABLE #tblCondAccNotExist

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_RT_LINKED
AS

IF OBJECT_ID('tempdb..#tblRecSplit') IS NOT NULL
	DROP TABLE #tblRecSplit
CREATE TABLE #tblRecSplit (conditionID int, recordTypeID int, roles varchar(max));

insert into #tblRecSplit
select distinct tblc.conditionID, cast(recordType.val as int) as recordTypeID, LEFT(rolelist.list, LEN(rolelist.list)-1) as roles 
from #tblCondALL as tblc
CROSS APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'recordType'
	WHERE cv.conditionID = tblc.conditionID
) as recordType(val)
INNER JOIN dbo.ams_recordTypes as rt on rt.recordTypeID = cast(recordType.val as int)
OUTER APPLY ( 
	SELECT cv.conditionValue + ', ' AS [text()] 
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'role'
	WHERE cv.conditionID = tblc.conditionID
	FOR XML PATH('')
) as rolelist(list) 
where subProc = 'RT_LINKED'

CREATE INDEX IX_tblRecSplit_conditionID ON #tblRecSplit (conditionID asc);


IF OBJECT_ID('tempdb..#tblRecSplitRoles') IS NOT NULL
	DROP TABLE #tblRecSplitRoles
CREATE TABLE #tblRecSplitRoles (conditionID int, [role] int);

insert into #tblRecSplitRoles
select conditionID, recRole.listItem as [role]
from #tblRecSplit
cross apply dbo.fn_intListToTable(roles,',') as recRole


-- no roles defined just a record type	
IF (select count(*) from #tblRecSplitRoles) = 0 
	insert into #cache_members_conditions_shouldbe
	select distinct m.memberid, tblc.conditionID	
	from #tblCondALL as tblc
	inner join #tblRecSplit as recsplit on recsplit.conditionID = tblc.conditionID
	inner join dbo.ams_members as m2 on m2.recordtypeID = recsplit.recordTypeID
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	where tblc.subProc = 'RT_LINKED'

-- roles defined
ELSE
	insert into #cache_members_conditions_shouldbe
	select distinct m.memberid, tblc.conditionID	
	from #tblCondALL as tblc
	inner join #tblRecSplit as recsplit on recsplit.conditionID = tblc.conditionID
	inner join dbo.ams_members as m2 on m2.recordtypeID = recsplit.recordTypeID
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join dbo.ams_recordRelationships as rr on rr.masterMemberID = m.memberid or rr.childMemberID = m.memberid
	inner join dbo.ams_recordTypesRelationshipTypes as rtrt on rtrt.recordTypeRelationshipTypeID = rr.recordTypeRelationshipTypeID 
	inner join #tblRecSplitRoles as roles on roles.role = rtrt.recordTypeRelationshipTypeID
	where rr.isactive = 1
	and rtrt.isActive = 1
	and tblc.subProc = 'RT_LINKED'


IF OBJECT_ID('tempdb..#tblRecSplit') IS NOT NULL
	DROP TABLE #tblRecSplit
IF OBJECT_ID('tempdb..#tblRecSplitRoles') IS NOT NULL
	DROP TABLE #tblRecSplitRoles

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_MH_EXISTS
AS

IF OBJECT_ID('tempdb..#tblMHSubCategoriesSplit') IS NOT NULL
	DROP TABLE #tblMHSubCategoriesSplit
CREATE TABLE #tblMHSubCategoriesSplit (conditionID int, historyCategory int, historySubCategory int);

insert into #tblMHSubCategoriesSplit (conditionID,historyCategory,historySubCategory)
select tblc.conditionID, cast(historyCategory.val as int), cast(historySubCategory.val as int)
from #tblCondALL as tblc
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyCategory'
	WHERE cv.conditionID = tblc.conditionID
) as historyCategory(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historySubCategory'
	WHERE cv.conditionID = tblc.conditionID
) as historySubCategory(val)
where subProc = 'MH_EXISTS'
and nullif(historySubCategory.val,'') is not null

CREATE INDEX IX_tblMHSubCategoriesSplit_conditionID ON #tblMHSubCategoriesSplit (conditionID asc);
CREATE INDEX IX_tblMHSubCategoriesSplit_historyCategory ON #tblMHSubCategoriesSplit (historyCategory asc);
CREATE INDEX IX_tblMHSubCategoriesSplit_historySubCategory ON #tblMHSubCategoriesSplit (historySubCategory asc);


IF OBJECT_ID('tempdb..#tblMHSplit') IS NOT NULL
	DROP TABLE #tblMHSplit
CREATE TABLE #tblMHSplit (conditionID int, historyCategory int, historyDateLower datetime, historyDateUpper datetime, 
	historyEnteredDateLower datetime, historyEnteredDateUpper datetime, historyQuantityLower int, historyQuantityUpper int, 
	historyAmountLower money, historyAmountUpper money, historyDescriptionContains varchar(max));

insert into #tblMHSplit (conditionID ,historyCategory ,historyDateLower ,historyDateUpper ,historyEnteredDateLower ,historyEnteredDateUpper ,historyQuantityLower ,historyQuantityUpper ,historyAmountLower ,historyAmountUpper ,historyDescriptionContains)
select tblc.conditionID, historyCategory.val,
	cast(nullif(historyDateLower.val,'') as datetime),
	cast(nullif(historyDateUpper.val,'') as datetime),
	cast(nullif(historyEnteredDateLower.val,'') as datetime),
	cast(nullif(historyEnteredDateUpper.val,'') as datetime),
	cast(nullif(historyQuantityLower.val,'') as int),
	cast(nullif(historyQuantityUpper.val,'') as int),
	cast(nullif(historyAmountLower.val,'') as money),
	cast(nullif(historyAmountUpper.val,'') as money),
	nullif(historyDescriptionContains.val,'')
from #tblCondALL as tblc
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyCategory'
	WHERE cv.conditionID = tblc.conditionID
) as historyCategory(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyDateLower'
	WHERE cv.conditionID = tblc.conditionID
) as historyDateLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyDateUpper'
	WHERE cv.conditionID = tblc.conditionID
) as historyDateUpper(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyEnteredDateLower'
	WHERE cv.conditionID = tblc.conditionID
) as historyEnteredDateLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyEnteredDateUpper'
	WHERE cv.conditionID = tblc.conditionID
) as historyEnteredDateUpper(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyQuantityLower'
	WHERE cv.conditionID = tblc.conditionID
) as historyQuantityLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyQuantityUpper'
	WHERE cv.conditionID = tblc.conditionID
) as historyQuantityUpper(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyAmountLower'
	WHERE cv.conditionID = tblc.conditionID
) as historyAmountLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyAmountUpper'
	WHERE cv.conditionID = tblc.conditionID
) as historyAmountUpper(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyDescriptionContains'
	WHERE cv.conditionID = tblc.conditionID
) as historyDescriptionContains(val)
where subProc = 'MH_EXISTS'

CREATE INDEX IX_tblMHSplit_conditionID ON #tblMHSplit (conditionID asc);
CREATE INDEX IX_tblMHSplit_historyCategory ON #tblMHSplit (historyCategory asc);


-- when no subcategories defined	
insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMHSplit as mhsplit on mhsplit.conditionID = tblc.conditionID
inner join dbo.ams_memberHistory as mh on mh.categoryID = mhsplit.historyCategory
	and mh.typeID = 1
	and mh.dateEntered >= isnull(mhsplit.historyEnteredDateLower,mh.dateEntered)
	and mh.dateEntered <= isnull(mhsplit.historyEnteredDateUpper,mh.dateEntered)
	and (
		(mh.userDate is null and mhsplit.historyDateLower is null and mhsplit.historyDateUpper is null)
		or 
		(mh.userDate >= isnull(mhsplit.historyDateLower,mh.userDate) and mh.userDate <= isnull(mhsplit.historyDateUpper,mh.userDate))
	)
	and (
		(mh.quantity is null and mhsplit.historyQuantityLower is null and mhsplit.historyQuantityUpper is null)
		or 
		(mh.quantity >= isnull(mhsplit.historyQuantityLower,mh.quantity) and mh.quantity <= isnull(mhsplit.historyQuantityUpper,mh.quantity))
	)
	and (
		(mh.dollarAmt is null and mhsplit.historyAmountLower is null and mhsplit.historyAmountUpper is null)
		or 
		(mh.dollarAmt >= isnull(mhsplit.historyAmountLower,mh.dollarAmt) and mh.dollarAmt <= isnull(mhsplit.historyAmountUpper,mh.dollarAmt))
	)
	and (
		(mh.description is null and mhsplit.historyDescriptionContains is null)
		or 
		(mh.description like '%' + isnull(mhsplit.historyDescriptionContains,mh.description) + '%')
	)
inner join dbo.cms_categories as c on c.categoryID = mh.categoryID
inner join dbo.cms_categoryTrees as ct on ct.categoryTreeID = c.categoryTreeID
inner join dbo.sites as s on s.siteID = ct.siteID and s.orgID = tblc.orgID
inner join dbo.ams_members as m2 on m2.memberID = mh.memberID
inner join #tblMembers as m on m.memberid = m2.activeMemberID
left outer join #tblMHSubCategoriesSplit as mhsubcats on mhsubcats.historyCategory = mhsplit.historyCategory
	and mhsubcats.conditionID = mhsplit.conditionID
where tblc.subProc = 'MH_EXISTS'
and mhsubcats.conditionID is null

-- when subcategories defined	
insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID	
from #tblCondALL as tblc
inner join #tblMHSplit as mhsplit on mhsplit.conditionID = tblc.conditionID
inner join #tblMHSubCategoriesSplit as mhsubcats on mhsubcats.historyCategory = mhsplit.historyCategory
	and mhsubcats.conditionID = mhsplit.conditionID
inner join dbo.ams_memberHistory as mh on mh.categoryID = mhsplit.historyCategory
	and mh.subcategoryID = mhsubcats.historySubCategory
	and mh.typeID = 1
	and mh.dateEntered >= isnull(mhsplit.historyEnteredDateLower,mh.dateEntered)
	and mh.dateEntered <= isnull(mhsplit.historyEnteredDateUpper,mh.dateEntered)
	and (
		(mh.userDate is null and mhsplit.historyDateLower is null and mhsplit.historyDateUpper is null)
		or 
		(mh.userDate >= isnull(mhsplit.historyDateLower,mh.userDate) and mh.userDate <= isnull(mhsplit.historyDateUpper,mh.userDate))
	)
	and (
		(mh.quantity is null and mhsplit.historyQuantityLower is null and mhsplit.historyQuantityUpper is null)
		or 
		(mh.quantity >= isnull(mhsplit.historyQuantityLower,mh.quantity) and mh.quantity <= isnull(mhsplit.historyQuantityUpper,mh.quantity))
	)
	and (
		(mh.dollarAmt is null and mhsplit.historyAmountLower is null and mhsplit.historyAmountUpper is null)
		or 
		(mh.dollarAmt >= isnull(mhsplit.historyAmountLower,mh.dollarAmt) and mh.dollarAmt <= isnull(mhsplit.historyAmountUpper,mh.dollarAmt))
	)
	and (
		(mh.description is null and mhsplit.historyDescriptionContains is null)
		or 
		(mh.description like '%' + isnull(mhsplit.historyDescriptionContains,mh.description) + '%')
	)
inner join dbo.cms_categories as c on c.categoryID = mh.categoryID
inner join dbo.cms_categoryTrees as ct on ct.categoryTreeID = c.categoryTreeID
inner join dbo.sites as s on s.siteID = ct.siteID and s.orgID = tblc.orgID
inner join dbo.ams_members as m2 on m2.memberID = mh.memberID
inner join #tblMembers as m on m.memberid = m2.activeMemberID
where tblc.subProc = 'MH_EXISTS'

IF OBJECT_ID('tempdb..#tblMHSubCategoriesSplit') IS NOT NULL
	DROP TABLE #tblMHSubCategoriesSplit
IF OBJECT_ID('tempdb..#tblMHSplit') IS NOT NULL
	DROP TABLE #tblMHSplit

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_L_ONLIST
AS

IF OBJECT_ID('tempdb..#tblLyrisListSplit') IS NOT NULL
	DROP TABLE #tblLyrisListSplit
CREATE TABLE #tblLyrisListSplit (conditionID int, list_ varchar(60));

insert into #tblLyrisListSplit (conditionID, list_)
select tblc.conditionID, cast(listName.val as varchar(60))
from #tblCondALL as tblc
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listList'
	WHERE cv.conditionID = tblc.conditionID
) as listName(val)
where subProc = 'L_ONLIST'

CREATE INDEX IX_tblLyrisListSplit_conditionID ON #tblLyrisListSplit (conditionID asc);
CREATE INDEX IX_tblLyrisListSplit_listname ON #tblLyrisListSplit (list_ asc);


IF OBJECT_ID('tempdb..#tblLyrisMemTypeSplit') IS NOT NULL
	DROP TABLE #tblLyrisMemTypeSplit
CREATE TABLE #tblLyrisMemTypeSplit (conditionID int, membertype_ varchar(60));

insert into #tblLyrisMemTypeSplit (conditionID, membertype_)
select tblc.conditionID, cast(memberType.val as varchar(20))
from #tblCondALL as tblc
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listMemberType'
	WHERE cv.conditionID = tblc.conditionID
) as memberType(val)
where subProc = 'L_ONLIST'

CREATE INDEX IX_tblLyrisMemTypeSplit_conditionID ON #tblLyrisMemTypeSplit (conditionID asc);
CREATE INDEX IX_tblLyrisMemTypeSplit_memberType ON #tblLyrisMemTypeSplit (membertype_ asc);


IF OBJECT_ID('tempdb..#tblLyrisSubTypeSplit') IS NOT NULL
	DROP TABLE #tblLyrisSubTypeSplit
CREATE TABLE #tblLyrisSubTypeSplit (conditionID int, subtype_ varchar(60));

insert into #tblLyrisSubTypeSplit (conditionID, subtype_)
select tblc.conditionID, cast(subType.val as varchar(20))
from #tblCondALL as tblc
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listSubType'
	WHERE cv.conditionID = tblc.conditionID
) as subType(val)
where subProc = 'L_ONLIST'

CREATE INDEX IX_tblLyrisSubTypeSplit_conditionID ON #tblLyrisSubTypeSplit (conditionID asc);
CREATE INDEX IX_tblLyrisSubTypeSplit_memberType ON #tblLyrisSubTypeSplit (subtype_ asc);


IF OBJECT_ID('tempdb..#tblLyrisOptionalSplit') IS NOT NULL
	DROP TABLE #tblLyrisOptionalSplit
CREATE TABLE #tblLyrisOptionalSplit (conditionID int, listLockAddress bit, listKeepActive bit, listJoinDateLower datetime, 
	listJoinDateUpper datetime, listExpireDateLower datetime, listExpireDateUpper datetime);

insert into #tblLyrisOptionalSplit (conditionID ,listLockAddress ,listKeepActive ,listJoinDateLower ,listJoinDateUpper ,listExpireDateLower ,listExpireDateUpper)
select tblc.conditionID,
	cast(nullif(listLockAddress.val,'-1') as bit),
	cast(nullif(listKeepActive.val,'-1') as bit),
	cast(nullif(listJoinDateLower.val,'') as datetime),
	cast(nullif(listJoinDateUpper.val,'') as datetime),
	cast(nullif(listExpireDateLower.val,'') as datetime),
	cast(nullif(listExpireDateUpper.val,'') as datetime)
from #tblCondALL as tblc
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listLockAddress'
	WHERE cv.conditionID = tblc.conditionID
) as listLockAddress(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listKeepActive'
	WHERE cv.conditionID = tblc.conditionID
) as listKeepActive(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listJoinDateLower'
	WHERE cv.conditionID = tblc.conditionID
) as listJoinDateLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listJoinDateUpper'
	WHERE cv.conditionID = tblc.conditionID
) as listJoinDateUpper(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listExpireDateLower'
	WHERE cv.conditionID = tblc.conditionID
) as listExpireDateLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listExpireDateUpper'
	WHERE cv.conditionID = tblc.conditionID
) as listExpireDateUpper(val)
where subProc = 'L_ONLIST'

CREATE INDEX IX_tblLyrisOptionalSplit_conditionID ON #tblLyrisOptionalSplit (conditionID asc);


insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID	
from #tblCondALL as tblc
inner join #tblLyrisListSplit as listsplit on listsplit.conditionID = tblc.conditionID
inner join #tblLyrisMemTypeSplit as msplit on msplit.conditionID = tblc.conditionID
inner join #tblLyrisSubTypeSplit as ssplit on ssplit.conditionID = tblc.conditionID
inner join #tblLyrisOptionalSplit as osplit on osplit.conditionID = tblc.conditionID
inner join dbo.organizations o on o.orgID = tblc.orgID
inner join datatransfer.dbo.cache_lyris_IntegratedMembers im on im.list_ = listsplit.list_
	and im.orgcode = o.orgcode
	and im.membertype_ = msplit.membertype_
	and im.subtype_ = ssplit.subtype_
	and im.mcoption_lockAddress = isnull(osplit.listLockAddress,im.mcoption_lockAddress)
	and im.mcoption_keepactive = isnull(osplit.listKeepActive,im.mcoption_keepactive)
	and im.dateJoined_ >= isnull(osplit.listJoinDateLower,im.dateJoined_)
	and im.dateJoined_ <= isnull(osplit.listJoinDateUpper,im.dateJoined_)
	and (
		(osplit.listExpireDateLower is null and osplit.listExpireDateUpper is null)
		or
		(im.expireDate_ >= isnull(osplit.listExpireDateLower,im.expireDate_) and im.expireDate_ <= isnull(osplit.listExpireDateUpper,im.expireDate_))
	)
inner join dbo.ams_members as m2 on m2.memberNumber = im.externalMemberID
	and m2.orgID = o.orgID
inner join #tblMembers as m on m.memberid = m2.activeMemberID
where tblc.subProc = 'L_ONLIST'


IF OBJECT_ID('tempdb..#tblLyrisListSplit') IS NOT NULL
	DROP TABLE #tblLyrisListSplit
IF OBJECT_ID('tempdb..#tblLyrisMemTypeSplit') IS NOT NULL
	DROP TABLE #tblLyrisMemTypeSplit
IF OBJECT_ID('tempdb..#tblLyrisSubTypeSplit') IS NOT NULL
	DROP TABLE #tblLyrisSubTypeSplit
IF OBJECT_ID('tempdb..#tblLyrisOptionalSplit') IS NOT NULL
	DROP TABLE #tblLyrisOptionalSplit

GO


ALTER PROC [dbo].[cache_members_populateMemberConditionCache]
@orgID int,
@conditionIDList varchar(max) = null,
@memberIDList varchar(max) = null,
@processImmediateOnly bit = 1,
@itemGroupUID uniqueidentifier,
@logTreeID uniqueidentifier

as

set nocount on

IF @itemGroupUID is null
	RETURN -1

declare @starttime datetime, @starttime2 datetime, @sql varchar(max), @totalMS int, @totalID int
select @starttime = getdate()
set @conditionIDList = isNull(@conditionIDList,'')
set @memberIDList = isNull(@memberIDList,'')


-- Log
INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage)
VALUES (@logTreeID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'Start Process of orgID=' + cast(@orgID as varchar(10)) + ' conditionID=' + left(@conditionIDList,100) + ' memberID=' + left(@memberIDList,100))


-- split members to calculate
IF OBJECT_ID('tempdb..#tblMembers') IS NOT NULL
	DROP TABLE #tblMembers
CREATE TABLE #tblMembers (memberID int PRIMARY KEY);

IF len(@memberIDList) > 0
	INSERT INTO #tblMembers
	select distinct m.memberID
	from dbo.fn_intListToTable(@memberIDList,',') as tmp
	inner join dbo.ams_members as m on m.memberID = tmp.listitem
	where m.orgID = @orgID
	and m.status <> 'D'
ELSE
	INSERT INTO #tblMembers
	select m.memberID
	from dbo.ams_members as m
	where m.orgID = @orgID
	and m.status <> 'D'

select @totalID = count(*) from #tblMembers 
INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage)
VALUES (@logTreeID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'Found ' + cast(@totalID as varchar(10)) + ' members to process');


-- split conditions to calculate. HONOR THE @processImmediateOnly parameter
IF OBJECT_ID('tempdb..#tblCond') IS NOT NULL
	DROP TABLE #tblCond
CREATE TABLE #tblCond (conditionID int PRIMARY KEY);

IF len(@conditionIDList) > 0
	INSERT INTO #tblCond
	select distinct c.conditionID
	from dbo.fn_intListToTable(@conditionIDList,',') as tmp
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tmp.listitem
	inner join dbo.ams_virtualGroupConditionTypes as ct on ct.conditionTypeID = c.conditionTypeID
	where c.orgID = @orgID
	and c.isDefined = 1
	and 1 = case 
		when @processImmediateOnly = 1 and ct.processImmediately = 0 then 0
		else 1 end
ELSE
	INSERT INTO #tblCond
	select c.conditionID
	from dbo.ams_virtualGroupConditions as c
	inner join dbo.ams_virtualGroupConditionTypes as ct on ct.conditionTypeID = c.conditionTypeID
	where c.orgID = @orgID
	and c.isDefined = 1	
	and 1 = case 
		when @processImmediateOnly = 1 and ct.processImmediately = 0 then 0
		else 1 end


-- get all conditions to calculate
IF OBJECT_ID('tempdb..#tblCondALL') IS NOT NULL
	DROP TABLE #tblCondALL
CREATE TABLE #tblCondALL (conditionID int PRIMARY KEY, orgID int, expression varchar(20), fieldCode varchar(40), fieldCodeArea varchar(25), 
	displayTypeCode varchar(20), dataTypeCode varchar(20), fieldCodeAreaID int, fieldCodeAreaPartA varchar(20), subProc varchar(30));

INSERT INTO #tblCondALL
select c.conditionID, c.orgID, e.expression, c.fieldCode, fieldCodeArea = case
	when left(c.fieldCode,2) = 'm_' then 'Member Data'	
	when left(c.fieldCode,3) = 'md_' then 'Custom Fields'	
	when left(c.fieldCode,3) = 'ma_' then 'Addresses'	
	when left(c.fieldCode,3) = 'mp_' then 'Phones'	
	when left(c.fieldCode,4) = 'mad_' then 'Districting'	
	when left(c.fieldCode,3) = 'me_' then 'Emails'	
	when left(c.fieldCode,3) = 'mw_' then 'Websites'	
	when left(c.fieldCode,2) = 'e_' then 'Events'	
	when left(c.fieldcode,4) = 'mpl_' then 'Professional Licenses'	
	when left(c.fieldcode,4) = 'grp_' then 'Groups'	-- excluded in where clause
	when left(c.fieldcode,4) = 'sub_' then 'Subscriptions'	
	when left(c.fieldcode,3) = 'rt_' then 'Record Types'	
	when left(c.fieldcode,5) = 'acct_' then 'Accounting'	
	when left(c.fieldcode,3) = 'mh_' then 'Member History'	
	when left(c.fieldcode,2) = 'l_' then 'Listserver Memberships'
	end, dit.displayTypeCode, dat.dataTypeCode,
	fieldCodeAreaID = case 
	when left(c.fieldCode,3) = 'md_' then cast(replace(c.fieldcode,'md_','') as int)
	when left(c.fieldcode,4) = 'mpl_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,3) = 'ma_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,2) = 'e_' then cast(replace(c.fieldcode,'e_','') as int)
	when left(c.fieldCode,3) = 'me_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,3) = 'mw_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,3) = 'mp_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as int)
	when left(c.fieldCode,4) = 'mad_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as int)
	else null end,
	fieldCodeAreaPartA = case
	when left(c.fieldcode,4) = 'mpl_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as varchar(20))
	when left(c.fieldCode,3) = 'ma_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as varchar(20))
	when left(c.fieldCode,3) = 'mp_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as varchar(10))
	when left(c.fieldCode,4) = 'mad_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as varchar(10))
	else null end,
	subProc = ''
from dbo.ams_virtualGroupConditions as c
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.expressionID
inner join dbo.ams_memberDataColumnDataTypes as dat on dat.dataTypeID = c.dataTypeID
inner join dbo.ams_memberDataColumnDisplayTypes as dit on dit.displayTypeID = c.displayTypeID
inner join #tblCond as tblC on tblc.conditionID = c.conditionID
where left(c.fieldCode,4) <> 'grp_'

-- add indexes for speed
CREATE INDEX IX_tblCondALL_areaid ON #tblCondALL (fieldCodeAreaID asc);
CREATE INDEX IX_tblCondALL_areaparta ON #tblCondALL (fieldCodeAreaPartA asc);
CREATE NONCLUSTERED INDEX IX_tblCondALL_DTA2 ON #tblCondALL (fieldCodeArea ASC, expression ASC, dataTypeCode ASC);

-- update subProcs to run
update #tblCondALL
set subProc = case 
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='between' then 'ACCT_BETWEEN_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='eq' then 'ACCT_EQ_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='exists' then 'ACCT_EXISTS_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='gt' then 'ACCT_GT_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='gte' then 'ACCT_GTE_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='lt' then 'ACCT_LT_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='lte' then 'ACCT_LTE_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='neq' then 'ACCT_NEQ_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='not_exists' then 'ACCT_NOTEXISTS_ALLOCSUM'
	when fieldCodeArea='Addresses' and expression='contains' then 'MA_CONTAINS'
	when fieldCodeArea='Addresses' and expression='contains_regex' then 'MA_CONTAINSREGEX'
	when fieldCodeArea='Addresses' and expression='eq' then 'MA_EQ'
	when fieldCodeArea='Addresses' and expression='exists' then 'MA_EXISTS'
	when fieldCodeArea='Addresses' and expression='gt' then 'MA_GT'
	when fieldCodeArea='Addresses' and expression='gte' then 'MA_GTE'
	when fieldCodeArea='Addresses' and expression='lt' then 'MA_LT'
	when fieldCodeArea='Addresses' and expression='lte' then 'MA_LTE'
	when fieldCodeArea='Addresses' and expression='neq' then 'MA_NEQ'
	when fieldCodeArea='Addresses' and expression='not_exists' then 'MA_NOTEXISTS'
	when fieldCodeArea='Custom Fields' and expression='datediff' then 'MD_DATEDIFF'
	when fieldCodeArea='Custom Fields' and expression='datepart' then 'MD_DATEPART'
	when fieldCodeArea='Custom Fields' and expression='contains' and dataTypeCode='STRING' then 'MD_CONTAINS_STRING'
	when fieldCodeArea='Custom Fields' and expression='contains_regex' and dataTypeCode='STRING' then 'MD_CONTAINSREGEX_STRING'
	when fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='STRING' then 'MD_EQ_STRING'
	when fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='BIT' then 'MD_EQ_BIT'
	when fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='INTEGER' then 'MD_EQ_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='DECIMAL2' then 'MD_EQ_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='DATE' then 'MD_EQ_DATE'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='STRING' then 'MD_EXISTS_STRING'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='BIT' then 'MD_EXISTS_BIT'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='INTEGER' then 'MD_EXISTS_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='DECIMAL2' then 'MD_EXISTS_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='DATE' then 'MD_EXISTS_DATE'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='CONTENTOBJ' then 'MD_EXISTS_CONTENTOBJ'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='DOCUMENTOBJ' then 'MD_EXISTS_DOCUMENTOBJ'
	when fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='STRING' then 'MD_GT_STRING'
	when fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='INTEGER' then 'MD_GT_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='DECIMAL2' then 'MD_GT_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='DATE' then 'MD_GT_DATE'
	when fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='STRING' then 'MD_GTE_STRING'
	when fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='INTEGER' then 'MD_GTE_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='DECIMAL2' then 'MD_GTE_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='DATE' then 'MD_GTE_DATE'
	when fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='STRING' then 'MD_LT_STRING'
	when fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='INTEGER' then 'MD_LT_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='DECIMAL2' then 'MD_LT_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='DATE' then 'MD_LT_DATE'
	when fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='STRING' then 'MD_LTE_STRING'
	when fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='INTEGER' then 'MD_LTE_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='DECIMAL2' then 'MD_LTE_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='DATE' then 'MD_LTE_DATE'
	when fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='STRING' then 'MD_NEQ_STRING'
	when fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='BIT' then 'MD_NEQ_BIT'
	when fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='INTEGER' then 'MD_NEQ_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='DECIMAL2' then 'MD_NEQ_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='DATE' then 'MD_NEQ_DATE'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='STRING' then 'MD_NOTEXISTS_STRING'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='INTEGER' then 'MD_NOTEXISTS_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='DECIMAL2' then 'MD_NOTEXISTS_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='DATE' then 'MD_NOTEXISTS_DATE'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='BIT' then 'MD_NOTEXISTS_BIT'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='CONTENTOBJ' then 'MD_NOTEXISTS_CONTENTOBJ'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='DOCUMENTOBJ' then 'MD_NOTEXISTS_DOCUMENTOBJ'
	when fieldCodeArea='Districting' and expression='eq' then 'MAD_EQ'
	when fieldCodeArea='Districting' and expression='exists' then 'MAD_EXISTS'
	when fieldCodeArea='Districting' and expression='neq' then 'MAD_NEQ'
	when fieldCodeArea='Districting' and expression='not_exists' then 'MAD_NOTEXISTS'
	when fieldCodeArea='Emails' and expression='contains' then 'ME_CONTAINS'
	when fieldCodeArea='Emails' and expression='contains_regex' then 'ME_CONTAINSREGEX'
	when fieldCodeArea='Emails' and expression='eq' then 'ME_EQ'
	when fieldCodeArea='Emails' and expression='exists' then 'ME_EXISTS'
	when fieldCodeArea='Emails' and expression='gt' then 'ME_GT'
	when fieldCodeArea='Emails' and expression='gte' then 'ME_GTE'
	when fieldCodeArea='Emails' and expression='lt' then 'ME_LT'
	when fieldCodeArea='Emails' and expression='lte' then 'ME_LTE'
	when fieldCodeArea='Emails' and expression='neq' then 'ME_NEQ'
	when fieldCodeArea='Emails' and expression='not_exists' then 'ME_NOTEXISTS'
	when fieldCodeArea='Events' and expression='registered' then 'E_REGISTERED'
	when fieldCodeArea='Events' and expression='attended' then 'E_ATTENDED'
	when fieldCodeArea='Events' and expression='awarded' then 'E_AWARDED'
	when fieldCodeArea='Listserver Memberships' and fieldCode='l_entry' and expression='onlist' then 'L_ONLIST'
	when fieldCodeArea='Member Data' and expression='contains' then 'M_CONTAINS'
	when fieldCodeArea='Member Data' and expression='contains_regex' then 'M_CONTAINSREGEX'
	when fieldCodeArea='Member Data' and expression='eq' and dataTypeCode='STRING' then 'M_EQ_STRING'
	when fieldCodeArea='Member Data' and expression='eq' and dataTypeCode='INTEGER' then 'M_EQ_INTEGER'
	when fieldCodeArea='Member Data' and expression='exists' then 'M_EXISTS'
	when fieldCodeArea='Member Data' and expression='gt' then 'M_GT'
	when fieldCodeArea='Member Data' and expression='gte' then 'M_GTE'
	when fieldCodeArea='Member Data' and expression='lt' then 'M_LT'
	when fieldCodeArea='Member Data' and expression='lte' then 'M_LTE'
	when fieldCodeArea='Member Data' and expression='neq' and dataTypeCode='STRING' then 'M_NEQ_STRING'
	when fieldCodeArea='Member Data' and expression='neq' and dataTypeCode='INTEGER' then 'M_NEQ_INTEGER'
	when fieldCodeArea='Member Data' and expression='not_exists' then 'M_NOTEXISTS'
	when fieldCodeArea='Member History' and fieldCode='mh_entry' and expression='exists' then 'MH_EXISTS'
	when fieldCodeArea='Phones' and expression='contains' then 'MP_CONTAINS'
	when fieldCodeArea='Phones' and expression='contains_regex' then 'MP_CONTAINSREGEX'
	when fieldCodeArea='Phones' and expression='eq' then 'MP_EQ'
	when fieldCodeArea='Phones' and expression='exists' then 'MP_EXISTS'
	when fieldCodeArea='Phones' and expression='gt' then 'MP_GT'
	when fieldCodeArea='Phones' and expression='gte' then 'MP_GTE'
	when fieldCodeArea='Phones' and expression='lt' then 'MP_LT'
	when fieldCodeArea='Phones' and expression='lte' then 'MP_LTE'
	when fieldCodeArea='Phones' and expression='neq' then 'MP_NEQ'
	when fieldCodeArea='Phones' and expression='not_exists' then 'MP_NOTEXISTS'
	when fieldCodeArea='Professional Licenses' and expression='contains' then 'MPL_CONTAINS'
	when fieldCodeArea='Professional Licenses' and expression='contains_regex' then 'MPL_CONTAINSREGEX'
	when fieldCodeArea='Professional Licenses' and expression='datepart' then 'MPL_DATEPART'
	when fieldCodeArea='Professional Licenses' and expression='datediff' then 'MPL_DATEDIFF'
	when fieldCodeArea='Professional Licenses' and expression='eq' and dataTypeCode='DATE' then 'MPL_EQ_DATE'
	when fieldCodeArea='Professional Licenses' and expression='eq' and dataTypeCode='STRING' then 'MPL_EQ_STRING'
	when fieldCodeArea='Professional Licenses' and expression='exists' and dataTypeCode='DATE' then 'MPL_EXISTS_DATE'
	when fieldCodeArea='Professional Licenses' and expression='exists' and dataTypeCode='STRING' then 'MPL_EXISTS_STRING'
	when fieldCodeArea='Professional Licenses' and expression='gt' and dataTypeCode='DATE' then 'MPL_GT_DATE'
	when fieldCodeArea='Professional Licenses' and expression='gt' and dataTypeCode='STRING' then 'MPL_GT_STRING'
	when fieldCodeArea='Professional Licenses' and expression='gte' and dataTypeCode='DATE' then 'MPL_GTE_DATE'
	when fieldCodeArea='Professional Licenses' and expression='gte' and dataTypeCode='STRING' then 'MPL_GTE_STRING'
	when fieldCodeArea='Professional Licenses' and expression='lt' and dataTypeCode='DATE' then 'MPL_LT_DATE'
	when fieldCodeArea='Professional Licenses' and expression='lt' and dataTypeCode='STRING' then 'MPL_LT_STRING'
	when fieldCodeArea='Professional Licenses' and expression='lte' and dataTypeCode='DATE' then 'MPL_LTE_DATE'
	when fieldCodeArea='Professional Licenses' and expression='lte' and dataTypeCode='STRING' then 'MPL_LTE_STRING'
	when fieldCodeArea='Professional Licenses' and expression='neq' and dataTypeCode='DATE' then 'MPL_NEQ_DATE'
	when fieldCodeArea='Professional Licenses' and expression='neq' and dataTypeCode='STRING' then 'MPL_NEQ_STRING'
	when fieldCodeArea='Professional Licenses' and expression='not_exists' and dataTypeCode='DATE' then 'MPL_NOTEXISTS_DATE'
	when fieldCodeArea='Professional Licenses' and expression='not_exists' and dataTypeCode='STRING' then 'MPL_NOTEXISTS_STRING'
	when fieldCodeArea='Record Types' and fieldCode='rt_role' and expression='linked' then 'RT_LINKED'
	when fieldCodeArea='Subscriptions' and expression='subscribed' then 'SUB_SUBSCRIBED'
	when fieldCodeArea='Websites' and expression='contains' then 'MW_CONTAINS'
	when fieldCodeArea='Websites' and expression='contains_regex' then 'MW_CONTAINSREGEX'
	when fieldCodeArea='Websites' and expression='eq' then 'MW_EQ'
	when fieldCodeArea='Websites' and expression='exists' then 'MW_EXISTS'
	when fieldCodeArea='Websites' and expression='gt' then 'MW_GT'
	when fieldCodeArea='Websites' and expression='gte' then 'MW_GTE'
	when fieldCodeArea='Websites' and expression='lt' then 'MW_LT'
	when fieldCodeArea='Websites' and expression='lte' then 'MW_LTE'
	when fieldCodeArea='Websites' and expression='neq' then 'MW_NEQ'
	when fieldCodeArea='Websites' and expression='not_exists' then 'MW_NOTEXISTS'
	else ''
	end

-- for the final processing at the end
IF OBJECT_ID('tempdb..#tblCondALLFinal') IS NOT NULL
	DROP TABLE #tblCondALLFinal
CREATE TABLE #tblCondALLFinal (conditionID int PRIMARY KEY);

insert into #tblCondALLFinal
select conditionID from #tblCondALL


-- put condition values into temp table by datatype to remove all casting in individual processing queries
IF OBJECT_ID('tempdb..#tblCondValues') IS NOT NULL
	DROP TABLE #tblCondValues
CREATE TABLE #tblCondValues (conditionID int, conditionKeyID int, conditionValueString varchar(max), conditionValueInteger int, conditionValueBit bit, conditionValueDecimal2 decimal(9,2), conditionValueDate datetime);

insert into #tblCondValues
select cv.conditionID, cv.conditionKeyID, null, cv.conditionValue, null, null, null
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where 
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'STRING' and tblc.displayTypeCode in ('RADIO','SELECT')) or
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'DECIMAL2' and tblc.displayTypeCode in ('RADIO','SELECT')) or 
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'DATE' and tblc.displayTypeCode in ('RADIO','SELECT')) or 
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq','lt','lte','gt','gte') and tblc.dataTypeCode = 'INTEGER') or
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('datepart','datediff')) or
	(tblc.fieldCodeArea = 'Addresses' and tblc.expression in ('eq','neq') and tblc.displayTypeCode in ('RADIO','SELECT')) or
	(tblc.fieldCodeArea = 'Professional Licenses' and tblc.expression in ('datepart','datediff')) or
	(tblc.fieldCodeArea = 'Member Data' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'INTEGER') or
	(tblc.fieldCodeArea = 'Districting' and tblc.expression in ('eq','neq'))
	union all
select cv.conditionID, cv.conditionKeyID, cv.conditionValue, null, null, null, null
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'STRING' and tblc.displayTypeCode not in ('RADIO','SELECT')) or
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('lt','lte','gt','gte','contains','contains_regex') and tblc.dataTypeCode = 'STRING') or
	(tblc.fieldCodeArea = 'Addresses' and tblc.expression in ('eq','neq') and tblc.displayTypeCode not in ('RADIO','SELECT')) or
	(tblc.fieldCodeArea = 'Addresses' and tblc.expression in ('lt','lte','gt','gte','contains','contains_regex')) or
	(tblc.fieldCodeArea = 'Professional Licenses' and tblc.expression in ('eq','neq','lt','lte','gt','gte','contains','contains_regex') and tblc.dataTypeCode = 'STRING') or
	(tblc.fieldCodeArea = 'Member Data' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'STRING') or
	(tblc.fieldCodeArea = 'Member Data' and tblc.expression in ('lt','lte','gt','gte','contains','contains_regex')) or
	(tblc.fieldCodeArea = 'Websites') or
	(tblc.fieldCodeArea = 'Emails') or
	(tblc.fieldCodeArea = 'Phones')
	union all
select cv.conditionID, cv.conditionKeyID, null, null, cv.conditionValue, null, null
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where (tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'BIT')
	union all
select cv.conditionID, cv.conditionKeyID, null, null, null, cv.conditionValue, null
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'DECIMAL2' and tblc.displayTypeCode not in ('RADIO','SELECT')) or
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('lt','lte','gt','gte') and tblc.dataTypeCode = 'DECIMAL2')
	union all
select cv.conditionID, cv.conditionKeyID, null, null, null, null, cv.conditionValue
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'DATE' and tblc.displayTypeCode not in ('RADIO','SELECT')) or
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('lt','lte','gt','gte') and tblc.dataTypeCode = 'DATE') or
	(tblc.fieldCodeArea = 'Professional Licenses' and tblc.expression in ('eq','neq','lt','lte','gt','gte') and tblc.dataTypeCode = 'DATE')

CREATE INDEX IX_tblCondValues_conditionID ON #tblCondValues (conditionID asc);


-- split ACCT data if necessary. This is here because several subProcs can use this data.
declare @acctCount int
select @acctCount = count(*) from #tblCondALL where fieldCodeArea = 'Accounting'
IF @acctCount > 0 BEGIN
	IF OBJECT_ID('tempdb..#tblAccSplit') IS NOT NULL
		DROP TABLE #tblAccSplit
	CREATE TABLE #tblAccSplit (conditionID int, revenueGLs varchar(max), batchDateLower datetime, batchDateUpper datetime, 
		revOrCash varchar(7), conditionValue money, conditionValueLower money, conditionValueUpper money);

	IF OBJECT_ID('tempdb..#tblAccSplitGL') IS NOT NULL
		DROP TABLE #tblAccSplitGL
	CREATE TABLE #tblAccSplitGL (conditionID int, revenueGL int);

	EXEC dbo.cache_members_populateMemberConditionCache_splitAcct
END


-- loop over subProcs to run
IF OBJECT_ID('tempdb..#cache_members_conditions_shouldbe') IS NOT NULL
	DROP TABLE #cache_members_conditions_shouldbe
CREATE TABLE #cache_members_conditions_shouldbe (memberid int, conditionID int);

declare @subProc varchar(30), @dynsql nvarchar(100)
select @subProc = min(subProc) from #tblCondALL where subProc <> ''
while @subProc is not null BEGIN
	SET @dynsql = 'EXEC dbo.cache_members_populateMemberConditionCache_' + @subProc
	EXEC sp_executesql @dynsql

	delete from #tblCondALL where subProc = @subProc

	select @subProc = min(subProc) from #tblCondALL where subProc <> '' and subProc > @subProc
END


-- Log
set @starttime2 = getdate()
INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage)
VALUES (@logTreeID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'Start changes to real tables');

-- delete member/conditions that should not be there
delete cmc
from dbo.cache_members_conditions as cmc
inner join #tblCondALLFinal as caf on caf.conditionID = cmc.conditionID
inner join #tblMembers as m on m.memberid = cmc.memberid
and not exists (
	select *
	from #cache_members_conditions_shouldbe
	where conditionID = cmc.conditionID
	and memberid = cmc.memberid
)	
		
-- insert member/conditions that should be but arent already there
insert into dbo.cache_members_conditions (memberID, conditionID)
select distinct cache.memberID, cache.conditionID
from #cache_members_conditions_shouldbe as cache
where not exists (
	select memberid, conditionid
	from dbo.cache_members_conditions
	where memberid = cache.memberid
	and conditionID = cache.conditionID
)

-- log
INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'End changes to real tables', Datediff(ms,@starttime2,getdate()));

-- return query of members affected
select distinct memberID
from #tblMembers
	
-- cleanup temp tables
IF OBJECT_ID('tempdb..#cache_members_conditions_shouldbe') IS NOT NULL
	DROP TABLE #cache_members_conditions_shouldbe
IF OBJECT_ID('tempdb..#tblCond') IS NOT NULL
	DROP TABLE #tblCond
IF OBJECT_ID('tempdb..#tblCondALL') IS NOT NULL
	DROP TABLE #tblCondALL
IF OBJECT_ID('tempdb..#tblCondALLFinal') IS NOT NULL
	DROP TABLE #tblCondALLFinal
IF OBJECT_ID('tempdb..#tblAccSplit') IS NOT NULL
	DROP TABLE #tblAccSplit
IF OBJECT_ID('tempdb..#tblAccSplitGL') IS NOT NULL
	DROP TABLE #tblAccSplitGL
IF OBJECT_ID('tempdb..#tblCondValues') IS NOT NULL
	DROP TABLE #tblCondValues
IF OBJECT_ID('tempdb..#tblMembers') IS NOT NULL
	DROP TABLE #tblMembers

-- Log
set @totalMS = Datediff(ms,@starttime,getdate())
INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'End Process of orgID=' + cast(@orgID as varchar(10)) + ' conditionID=' + left(@conditionIDList,100) + ' memberID=' + left(@memberIDList,100), @totalMS);

set nocount off
GO

