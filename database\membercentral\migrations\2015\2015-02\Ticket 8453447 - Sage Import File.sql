use membercentral
GO

ALTER PROC [dbo].[tr_report_batchDetail]
@orgID int,
@batchIDList varchar(max),
@filename varchar(400) = null,
@format char(3) = null

AS

DECLARE @fullsql varchar(max), @tmpFileName varchar(400), @bcpcmd varchar(7000), @FinalFormat char(3)

IF @filename is not null and @format not in ('csv','iif','ach') 
	set @format = 'csv'

IF @filename is null
	set @FinalFormat = 'scr'
ELSE
	set @FinalFormat = @format


-- drop any existing temp tables
IF OBJECT_ID('tempdb..#tblB') IS NOT NULL 
	DROP TABLE #tblB
IF OBJECT_ID('tempdb..#tblGL') IS NOT NULL 
	DROP TABLE #tblGL
IF OBJECT_ID('tempdb..#tmpBatchDetailReport') IS NOT NULL 
	DROP TABLE #tmpBatchDetailReport
IF OBJECT_ID('tempdb..#tmpGLallTransSum') IS NOT NULL 
	DROP TABLE #tmpGLallTransSum
IF OBJECT_ID('tempdb..#tmpGLallTransSumNoBatch') IS NOT NULL 
	DROP TABLE #tmpGLallTransSumNoBatch
IF OBJECT_ID('tempdb..#tmpBatchDetailGLActivity') IS NOT NULL 
	DROP TABLE #tmpBatchDetailGLActivity
IF OBJECT_ID('tempdb..#tmpBatchDetailGLActivityNoBatch') IS NOT NULL 
	DROP TABLE #tmpBatchDetailGLActivityNoBatch
IF @FinalFormat = 'csv' BEGIN
	IF OBJECT_ID('tempdb..#tmpBatchDetailReportExport') IS NOT NULL 
		DROP TABLE #tmpBatchDetailReportExport
	IF OBJECT_ID('tempdb..#tmpBatchDetailReportExpandedExport') IS NOT NULL 
		DROP TABLE #tmpBatchDetailReportExpandedExport
	IF OBJECT_ID('tempdb..#tmpSAGEExport') IS NOT NULL 
		DROP TABLE #tmpSAGEExport
END
IF @FinalFormat = 'ach' BEGIN
	IF OBJECT_ID('tempdb..#tmpBatchDetailACH') IS NOT NULL 
		DROP TABLE #tmpBatchDetailACH
	IF OBJECT_ID('tempdb..##tmpACHExport') IS NOT NULL 
		DROP TABLE ##tmpACHExport
END
IF @FinalFormat = 'iif' BEGIN
	IF OBJECT_ID('tempdb..#tmpIIFInnerMagic') IS NOT NULL 
		DROP TABLE #tmpIIFInnerMagic
	IF OBJECT_ID('tempdb..##tmpIIFExport') IS NOT NULL 
		DROP TABLE ##tmpIIFExport
END


-- split batchIDs
-- if csv or iif, remove all non-posted batches
-- if ach, remove all non-posted and non-closed batches
CREATE TABLE #tblB (batchID int, payProfileID int)
INSERT INTO #tblB (batchID, payProfileID)
select b.batchID, b.payProfileID
from dbo.fn_intListToTable(@batchIDList,',') as tmp
inner join dbo.tr_batches as b on b.batchID = tmp.listitem
where b.orgID = @orgID
and 1 =  
	case 
	when @FinalFormat in ('csv','iif') and b.statusID <> 4 then 0
	when @FinalFormat = 'ach' and b.statusID not in (3,4) then 0
	else 1
	end

CREATE TABLE #tblGL (GLAccountID int, AccountCode varchar(200), thePathExpanded varchar(max))
insert into #tblGL (GLAccountID, AccountCode, thePathExpanded)
select GLAccountID, AccountCode, thePathExpanded
from dbo.fn_getRecursiveGLAccounts(@orgID)

-- if IIF/ACH, payProfile must not be null
IF @FinalFormat in ('iif','ach')
	delete from #tblB where payProfileID is null

-- get raw data
select b.batchID, b.batchname, b.batchCode, b.depositDate, bt.transactionID, bt.payTID, 
	bt.transactionDate, bt.[type], bt.amount, bt.detail, bt.gatewayTransactionID, bt.gatewayApprovalCode, 
	bt.debitGLAccountID, bt.creditGLAccountID, bt.CashGLAccountID, bt.RevenueGLAccountID, 
	bt.CashTransactionID, bt.RevenueTransactionID, 
	bt.memberid, bt.memberName, bt.membernumber, bt.memberCompany, 
	bt.recordedByMemberid, bt.recordedByMemberName, bt.recordedByMemberNumber
into #tmpBatchDetailReport
from #tblB as tb
inner join dbo.tr_batches as b on b.batchID = tb.batchID
cross apply dbo.tr_getBatchTransactions(@orgID,b.batchID) as bt



-- batch detail
IF @FinalFormat = 'scr' BEGIN
	select batchid, depositDate, batchname, transactionID, payTID, transactionDate, 
		[type], amount, detail, memberid, memberName, membernumber, memberCompany, 
		rglCash.thePathExpanded as CashPathExpanded,
		rglCash.AccountCode as CashAccountCode,
		rglRevenue.thePathExpanded as RevenuePathExpanded,
		rglRevenue.AccountCode as RevenueAccountCode,
		countAsItem = case
			when tmp.[type] in ('Payment','Refund','NSF') then 1
			when tmp.[type] = 'VoidOffset' and right(batchcode,3) = '_VD' and (detail like 'VOID of Payment%' or detail like 'VOID of Refund%' or detail like 'VOID of NSF%') then 1
			else 0
			end
	from #tmpBatchDetailReport as tmp
	LEFT OUTER JOIN #tblGL as rglCash on rglCash.GLAccountID = tmp.CashGLAccountID
	LEFT OUTER JOIN #tblGL as rglRevenue on rglRevenue.GLAccountID = tmp.RevenueGLAccountID
	order by depositDate, batchname, batchID, payTID, case when tmp.cashGLAccountID is null then 0 else 1 end desc, transactionID
END
IF @FinalFormat = 'csv' BEGIN
	select tmp.batchID, tmp.depositDate, tmp.batchname, tmp.transactionID, tmp.payTID, 
		tmp.transactionDate, tmp.type, tmp.amount, 
		tmp.detail, tmp.gatewayTransactionID, tmp.gatewayApprovalCode, 
		rglCash.thePathExpanded as CashPathExpanded,
		rglCash.AccountCode as CashAccountCode,
		rglRevenue.thePathExpanded as RevenuePathExpanded,
		rglRevenue.AccountCode as RevenueAccountCode,
		tmp.memberid, tmp.memberName, tmp.memberNumber, tmp.memberCompany, 
		tmp.recordedByMemberid, tmp.recordedByMemberName, tmp.recordedByMemberNumber
	into #tmpBatchDetailReportExport
	from #tmpBatchDetailReport as tmp
	LEFT OUTER JOIN #tblGL as rglCash on rglCash.GLAccountID = tmp.CashGLAccountID
	LEFT OUTER JOIN #tblGL as rglRevenue on rglRevenue.GLAccountID = tmp.RevenueGLAccountID

	-- sql for export
	SELECT @fullsql = '
		select convert(varchar(10),depositDate,101) as [Deposit Date], 
			batchName as [Batch Name], 
			TransactionID, payTID as [Payment TransactionID], 
			convert(varchar(20),transactionDate,22) as [Transaction Date], 
			[Type], Detail, GatewayTransactionID as [Gateway TransactionID], 
			GatewayApprovalCode as [Gateway Approval Code], 
			CashPathExpanded as [Cash GL Account], 
			CashAccountCode as [Cash GL Account Code], 
			RevenuePathExpanded as [Revenue GL Account], 
			RevenueAccountCode as [Revenue GL Account Code], 
			Amount, memberName as [Member], memberNumber as [MemberNumber], memberCompany as [Company], 
			recordedByMemberName as [Recorded By Member], recordedByMemberNumber as [Recorded By MemberNumber]
		from #tmpBatchDetailReportExport
		order by depositDate, batchName, batchID, payTID, transactionID'

	-- export
	set @tmpFilename = replace(@filename,'.csv','Detail.csv')
	EXEC dbo.up_exportCSV @csvfilename=@tmpFilename, @sql=@fullsql
END 



-- batch detail expanded 
IF @FinalFormat = 'csv' BEGIN
	select convert(varchar(10),tmp.depositDate,101) as [BT_Batch Deposit Date], 
		tmp.batchname as [BT_Batch Name], 
		tmp.transactionID as [BT_TransactionID], 
		convert(varchar(20),tmp.transactionDate,22) as [BT_Transaction Date],
		tmp.type as [BT_Transaction Type],
		tmp.amount as [BT_Transaction Amount],
		tmp.detail as [BT_Transaction Detail],
		tmp.memberName as [BT_Member Name], 
		tmp.memberNumber as [BT_Member Number], 
		tmp.memberCompany as [BT_Member Company],
		tmp.recordedByMemberName as [BT_Recorded By Member Name], 
		tmp.recordedByMemberNumber as [BT_Recorded By Member Number], 
		rglDeb.thePathExpanded as [BT_Debit GL Account],
		rglDeb.AccountCode as [BT_Debit GL Account Code],
		rglCred.thePathExpanded as [BT_Credit GL Account],
		rglCred.AccountCode as [BT_Credit GL Account Code],

		tRev.transactionID as [Rev_Transaction ID],
		convert(varchar(20),tRev.transactionDate,22) as [Rev_Transaction Date],
		ttRev.type as [Rev_Transaction Type],
		tRev.amount as [Rev_Transaction Amount],
		tRev.detail as [Rev_Transaction Detail],
		m2Rev.lastname 
			+ case when oRev.hasSuffix = 1 then isnull(' ' + nullif(m2Rev.suffix,''),'') else '' end
			+ ', ' + m2Rev.firstname 
			+ case when oRev.hasMiddleName = 1 then isnull(' ' + nullif(m2Rev.middlename,''),'') else '' end 
			as [Rev_Member Name], 
		m2Rev.memberNumber as [Rev_Member Number], 
		m2Rev.company as [Rev_Member Company], 
		recordedByM2Rev.lastname 
			+ case when oRev.hasSuffix = 1 then isnull(' ' + nullif(recordedByM2Rev.suffix,''),'') else '' end
			+ ', ' + recordedByM2Rev.firstname 
			+ case when oRev.hasMiddleName = 1 then isnull(' ' + nullif(recordedByM2Rev.middlename,''),'') else '' end 
		as [Rev_Recorded By Member Name], 
		recordedByM2Rev.membernumber as [Rev_Recorded By Member Number], 
		rglDebRev.thePathExpanded as [Rev_Debit GL Account],
		rglDebRev.AccountCode as [Rev_Debit GL Account Code],
		rglCredRev.thePathExpanded as [Rev_Credit GL Account],
		rglCredRev.AccountCode as [Rev_Credit GL Account Code],

		tCash.transactionID as [Cash_Transaction ID],
		convert(varchar(10),tCash.transactionDate,101) as [Cash_Transaction Date],
		ttCash.type as [Cash_Transaction Type],
		tCash.amount as [Cash_Transaction Amount],
		tCash.detail as [Cash_Transaction Detail],
		m2Cash.lastname 
			+ case when oCash.hasSuffix = 1 then isnull(' ' + nullif(m2Cash.suffix,''),'') else '' end
			+ ', ' + m2Cash.firstname 
			+ case when oCash.hasMiddleName = 1 then isnull(' ' + nullif(m2Cash.middlename,''),'') else '' end 
			as [Cash_Member Name], 
		m2Cash.memberNumber as [Cash_Member Number], 
		m2Cash.company as [Cash_Member Company], 
		recordedByM2Cash.lastname 
			+ case when oCash.hasSuffix = 1 then isnull(' ' + nullif(recordedByM2Cash.suffix,''),'') else '' end
			+ ', ' + recordedByM2Cash.firstname 
			+ case when oCash.hasMiddleName = 1 then isnull(' ' + nullif(recordedByM2Cash.middlename,''),'') else '' end 
		as [Cash_Recorded By Member Name], 
		recordedByM2Cash.membernumber as [Cash_Recorded By Member Number], 
		rglDebCash.thePathExpanded as [Cash_Debit GL Account],
		rglDebCash.AccountCode as [Cash_Debit GL Account Code],
		rglCredCash.thePathExpanded as [Cash_Credit GL Account],
		rglCredCash.AccountCode as [Cash_Credit GL Account Code],
		phCash.gatewayTransactionID as [Cash_Gateway Transaction ID],
		phCash.gatewayApprovalCode as [Cash_Gateway Approval Code]
	into #tmpBatchDetailReportExpandedExport	
	from #tmpBatchDetailReport as tmp
	INNER JOIN #tblGL as rglDeb on rglDeb.GLAccountID = tmp.debitGLAccountID
	INNER JOIN #tblGL as rglCred on rglCred.GLAccountID = tmp.creditGLAccountID
	LEFT OUTER JOIN dbo.tr_transactions as tRev 
		inner join dbo.tr_types as ttRev on ttRev.typeID = tRev.typeID
		inner join dbo.ams_members as mRev on mRev.memberID = tRev.assignedToMemberID
		inner join dbo.ams_members as m2Rev on m2Rev.memberID = mRev.activeMemberID
		inner join dbo.ams_members as recordedByMRev on recordedBymRev.memberID = tRev.recordedByMemberID
		inner join dbo.ams_members as recordedByM2Rev on recordedByM2Rev.memberID = recordedByMRev.activeMemberID
		inner join dbo.organizations as oRev on oRev.orgID = m2Rev.orgID
		INNER JOIN #tblGL as rglDebRev on rglDebRev.GLAccountID = tRev.debitGLAccountID
		INNER JOIN #tblGL as rglCredRev on rglCredRev.GLAccountID = tRev.creditGLAccountID
		on tRev.transactionID = tmp.RevenueTransactionID
	LEFT OUTER JOIN dbo.tr_transactions as tCash 
		inner join dbo.tr_types as ttCash on ttCash.typeID = tCash.typeID
		inner join dbo.ams_members as mCash on mCash.memberID = tCash.assignedToMemberID
		inner join dbo.ams_members as m2Cash on m2Cash.memberID = mCash.activeMemberID
		inner join dbo.ams_members as recordedByMCash on recordedBymCash.memberID = tCash.recordedByMemberID
		inner join dbo.ams_members as recordedByM2Cash on recordedByM2Cash.memberID = recordedByMCash.activeMemberID
		inner join dbo.organizations as oCash on oCash.orgID = m2Cash.orgID
		INNER JOIN #tblGL as rglDebCash on rglDebCash.GLAccountID = tCash.debitGLAccountID
		INNER JOIN #tblGL as rglCredCash on rglCredCash.GLAccountID = tCash.creditGLAccountID
		INNER JOIN dbo.tr_transactionPayments as tpCash on tpCash.transactionID = tCash.transactionID
		inner join dbo.tr_paymentHistory as phCash on phCash.historyID = tpCash.historyID
		on tCash.transactionID = tmp.CashTransactionID

	-- sql for export
	SELECT @fullsql = 'select * from #tmpBatchDetailReportExpandedExport order by [BT_Batch Deposit Date], [BT_Batch Name], [BT_TransactionID]'

	-- export
	set @tmpFilename = replace(@filename,'.csv','DetailExpanded.csv')
	EXEC dbo.up_exportCSV @csvfilename=@tmpFilename, @sql=@fullsql
END



-- payments/refunds for ACH file
IF @FinalFormat = 'ach' BEGIN
	declare @orgName varchar(23), @orgShortName varchar(16)
	select @orgName = UPPER(orgName), @orgShortName = UPPER(orgShortName) from dbo.organizations where orgID = @orgID

	select cast(
		'6' +																							-- 1
		case when det.type = 'Payment' then '27' else '22' end +										-- 2
		ph.paymentInfo.value('(//gateway/fld_9_)[1]','varchar(9)') +									-- 8+1
		left(ph.paymentInfo.value('(//gateway/fld_10_)[1]','varchar(17)') + '                 ',17) +	-- 17
		right('**********' + replace(cast(det.amount as varchar(11)),'.',''),10) +						-- 10
		left(UPPER(det.membernumber) + '               ',15) +											-- 15
		case																							-- 22
		when det.batchCode like '%CCD%' then left(UPPER(det.membercompany) + '                      ',22)
		else left(UPPER(det.membername) + '                      ',22)
		end + 
		case																							-- 2+1
		when det.batchCode like '%CCD%' then '  0' 
		when det.batchCode like '%PPD%' then '  0' 
		else 'S 0'
		end + 
		left(mp.bankTRN,8) + right('0000000' + cast(ROW_NUMBER() OVER (PARTITION BY det.batchID ORDER BY det.batchID, det.transactionID) as varchar(7)),7) + -- 15
		'' as char(94)) as entryDetailRecord,
		cast(ph.paymentInfo.value('(//gateway/fld_9_)[1]','varchar(8)') as bigint) as entryhash,
		case when det.type = 'Payment' then det.amount else 0 end as entrydebit,
		case when det.type = 'Payment' then 0 else det.amount end as entrycredit,
		ph.paymentInfo.value('(//gateway/seccode)[1]','varchar(3)') as entryseccode,
		convert(varchar(6),b.depositDate,12) as batchdepositdate,
		b.batchid,
		mp.bankAccountName, mp.bankTRN, mp.bankCompanyNumber, 
		ROW_NUMBER() OVER (ORDER BY det.batchID, det.transactionID) as entryOrder
	into #tmpBatchDetailACH
	from #tmpBatchDetailReport as det
	inner join dbo.tr_batches as b on b.batchID = det.batchID
	inner join dbo.tr_transactions as t on t.transactionID = det.transactionID
	inner join dbo.tr_transactionPayments as tp on tp.transactionID = det.transactionID
	inner join dbo.tr_paymentHistory as ph on ph.historyID = tp.historyID
	inner join dbo.mp_profiles as mp on mp.profileID = b.payProfileID
	where det.type in ('Payment','Refund')
	and t.statusID = 1
	order by det.batchID, det.transactionID		-- MUST be ordered by trace number in the file

	declare @lineCount int, @blockCount int, @fillersToAdd int
	select @lineCount = count(*) + (count(distinct batchID)*2) + 2 from #tmpBatchDetailACH
	select @blockCount = ceiling( cast(@lineCount as float) / cast(10 as float) )
	select @fillersToAdd = @blockCount * 10 - @lineCount

	declare @bankName varchar(23), @bankTRN char(9), @bankCompanyNumber char(10)
	select top 1 @bankName = UPPER(bankAccountName), @bankTRN = bankTRN, @bankCompanyNumber = bankCompanyNumber from #tmpBatchDetailACH

	select fileHeaderRecord
	into ##tmpACHExport
	from (
		select cast(
			'1' +																							-- 1
			'01' +																							-- 2
			' ' + @bankTRN +																				-- 10	
			left(@bankCompanyNumber,10) +																	-- 10
			convert(varchar(6),getdate(),12) +																-- 6
			replace(convert(varchar(5),getdate(),108),':','') +												-- 4
			'A' +																							-- 1
			'094' +																							-- 3
			'10' +																							-- 2
			'1' +																							-- 1
			left(@bankName + '                       ',23) +												-- 23
			left(@orgName + '                       ',23) +													-- 23
			'        ' +																					-- 8
			'' as char(94)) as fileHeaderRecord,
			'0001' as fileOrder
		union all
		select cast(
			'5' +																							-- 1
			'200' +																							-- 3	
			left(@orgName + '                ',16) + 														-- 16
			'                    ' +																		-- 20
			left(@bankCompanyNumber,10) +																	-- 10
			left(distbatches.entryseccode + '   ',3) +														-- 3
			left(@orgShortName + '          ',10) +															-- 10
			distbatches.batchdepositdate +																	-- 6
			distbatches.batchdepositdate +																	-- 6
			'   ' +																							-- 3
			'1' +																							-- 1
			left(@bankTRN,8) +																				-- 8
			right('0000000' + cast(ROW_NUMBER() OVER (ORDER BY distbatches.minOrder) as varchar(7)),7) +	-- 7
			'' as char(94)) as batchHeaderRecord,
			right('0000' + cast(distbatches.minorder as varchar(10)),4) + '.0000' as fileOrder
		from (
			select batchID, entryseccode, batchdepositdate, min(entryOrder) as minOrder
			from #tmpBatchDetailACH
			group by batchID, entryseccode, batchdepositdate
			) as distbatches
		union all
		select ach.entryDetailRecord, right('0000' + cast(distbatches.minorder as varchar(10)),4) + '.' + right('0000' + cast(ach.entryOrder as varchar(10)),4) as fileOrder
		from #tmpBatchDetailACH as ach
		inner join (
			select batchID, min(entryOrder) as minOrder
			from #tmpBatchDetailACH
			group by batchID, entryseccode, batchdepositdate
			) as distbatches on distbatches.batchID = ach.batchID
		union all
		select cast(
			'8' +																							-- 1
			'200' +																							-- 3	
			right('000000' + cast(distbatches.detailCount as varchar(6)),6) +								-- 6
			right('**********' + cast(distbatches.entryHashTtl as varchar(20)),10) +						-- 10
			right('************' + replace(cast(distbatches.entrydebitTtl as varchar(13)),'.',''),12) +		-- 12
			right('************' + replace(cast(distbatches.entrycreditTtl as varchar(13)),'.',''),12) +	-- 12
			left(@bankCompanyNumber,10) +																	-- 10
			'                   ' +																			-- 19
			'      ' +																						-- 6
			left(@bankTRN,8) +																				-- 8
			right('0000000' + cast(ROW_NUMBER() OVER (ORDER BY distbatches.minOrder) as varchar(7)),7) +	-- 7
			'' as char(94)) as batchControlRecord,
			right('0000' + cast(distbatches.minorder as varchar(10)),4) + '.9999' as fileOrder
		from (
			select batchID, min(entryOrder) as minOrder, count(*) as detailCount, sum(entryhash) as entryHashTtl, 
				sum(entrydebit) as entrydebitTtl, sum(entrycredit) as entrycreditTtl
			from #tmpBatchDetailACH
			group by batchID
		) as distbatches
		union all
		select cast(
			'9' +																							-- 1
			right('000000' + cast(allACH.batchCount as varchar(6)),6) +										-- 6
			right('000000' + cast(ceiling(cast(allACH.entryCount + (allACH.batchCount*2) + 2 as float)/cast(10 as float)) as varchar(6)),6) +		-- 6
			right('********' + cast(allACH.entryCount as varchar(8)),8) +									-- 8
			right('**********' + cast(allACH.entryHashTtl as varchar(20)),10) +								-- 10
			right('************' + replace(cast(allACH.entrydebitTtl as varchar(13)),'.',''),12) +			-- 12
			right('************' + replace(cast(allACH.entrycreditTtl as varchar(13)),'.',''),12) +			-- 12
			'                                       ' +														-- 39
			'' as char(94)) as fileControlRecord,
			'9998' as fileOrder
		from (
			select count(distinct batchID) as batchCount, count(*) as entryCount, sum(entryhash) as entryHashTtl, 
				sum(entrydebit) as entrydebitTtl, sum(entrycredit) as entrycreditTtl
			from #tmpBatchDetailACH
		) as allACH
		union all
		select top (@fillersToAdd) REPLICATE('9',94), '9999' as fileOrder
		from dbo.F_TABLE_NUMBER_RANGE(1,10)
	) as tmp
	order by fileOrder

	set @bcpcmd = 'bcp ##tmpACHExport out "' + @filename + '" -c -t0x09 -T -S' + CAST(serverproperty('servername') as varchar(20))
	exec master..xp_cmdshell @bcpcmd, NO_OUTPUT
END



IF @FinalFormat in ('scr','csv','iif') BEGIN
	; with allTransDetl AS (
		select batchID, debitGLAccountID as glAccountID, abs(amount) as debit, null as credit
		from #tmpBatchDetailReport
			union all
		select batchID, creditGLAccountID as glAccountID, null as debit, abs(amount) as credit
		from #tmpBatchDetailReport
			union all
		select batchID, creditGLAccountID as glAccountID, abs(amount) as debit, null as credit
		from #tmpBatchDetailReport
		where [type] = 'Allocation' or ([type] = 'VoidOffset' and detail like 'VOID of Deallocation%')
			union all
		select batchID, revenueGLAccountID as glAccountID, null as debit, abs(amount) as credit
		from #tmpBatchDetailReport
		where [type] = 'Allocation' or ([type] = 'VoidOffset' and detail like 'VOID of Deallocation%')
			union all
		select batchID, debitGLAccountID as glAccountID, null as debit, abs(amount) as credit
		from #tmpBatchDetailReport
		where [type] = 'Deallocation' or ([type] = 'VoidOffset' and detail like 'VOID of Allocation%')
			union all
		select batchID, revenueGLAccountID as glAccountID, abs(amount) as debit, null as credit
		from #tmpBatchDetailReport
		where [type] = 'Deallocation' or ([type] = 'VoidOffset' and detail like 'VOID of Allocation%')
	)
	select batchID, glAccountID, isnull(sum(debit),0) as debitAmt, isnull(sum(credit),0) as creditAmt
	into #tmpGLallTransSum
	from allTransDetl
	group by batchID, glAccountID

	select null as batchID, glAccountID, sum(debitAmt) as debitAmt, sum(creditAmt) as creditAmt
	into #tmpGLallTransSumNoBatch
	from #tmpGLallTransSum
	group by glAccountID

	select BatchID, GLAccountID, AccountCode, accountType, thePathExpanded, thePath, debits, credits
	into #tmpBatchDetailGLActivity
	from (
		select ats.batchID, gl.GLAccountID, gl.AccountCode, glt.accountType, rgl.thePathExpanded, rgl.thePath, 
			case 
			when glt.accountType = 'Cash' and ats.debitAmt - ats.creditAmt >= 0 then ats.debitAmt - ats.creditAmt
			when glt.accountType = 'Asset' and gl.GLCode = 'ACCOUNTSRECEIVABLE' and ats.debitAmt - ats.creditAmt > 0 then ats.debitAmt - ats.creditAmt
			when glt.accountType = 'Liability' and gl.GLCode = 'DEPOSITS' and ats.creditAmt - ats.debitAmt <= 0 then abs(ats.creditAmt - ats.debitAmt)
			when glt.accountType = 'Revenue' and ats.creditAmt - ats.debitAmt < 0 then abs(ats.creditAmt - ats.debitAmt)
			when glt.accountType = 'Expense' and ats.debitAmt - ats.creditAmt >= 0 then ats.debitAmt - ats.creditAmt
			when glt.accountType = 'Liability' and ats.creditAmt - ats.debitAmt <= 0 then abs(ats.creditAmt - ats.debitAmt)
			else null
			end as debits,
			case 
			when glt.accountType = 'Cash' and ats.debitAmt - ats.creditAmt < 0 then abs(ats.debitAmt - ats.creditAmt)
			when glt.accountType = 'Asset' and gl.GLCode = 'ACCOUNTSRECEIVABLE' and ats.debitAmt - ats.creditAmt <= 0 then abs(ats.debitAmt - ats.creditAmt)
			when glt.accountType = 'Liability' and gl.GLCode = 'DEPOSITS' and ats.creditAmt - ats.debitAmt > 0 then ats.creditAmt - ats.debitAmt
			when glt.accountType = 'Revenue' and ats.creditAmt - ats.debitAmt >= 0 then ats.creditAmt - ats.debitAmt
			when glt.accountType = 'Expense' and ats.debitAmt - ats.creditAmt < 0 then abs(ats.debitAmt - ats.creditAmt)
			when glt.accountType = 'Liability' and ats.creditAmt - ats.debitAmt > 0 then ats.creditAmt - ats.debitAmt
			else null
			end as credits
		from #tmpGLallTransSum as ats
		INNER JOIN dbo.tr_GLAccounts as gl on gl.GLAccountID = ats.GLAccountID
		INNER JOIN dbo.tr_GLAccountTypes as glt on glt.accountTypeID = gl.accountTypeID
		INNER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) as rgl on rgl.GLAccountID = gl.GLAccountID
	) tmp
	where isnull(debits,0)+isnull(credits,0) <> 0
	order by thePath

	select BatchID, GLAccountID, AccountCode, accountType, thePathExpanded, thePath, debits, credits
	into #tmpBatchDetailGLActivityNoBatch
	from (
		select ats.batchID, gl.GLAccountID, gl.AccountCode, glt.accountType, rgl.thePathExpanded, rgl.thePath, 
			case 
			when glt.accountType = 'Cash' and ats.debitAmt - ats.creditAmt >= 0 then ats.debitAmt - ats.creditAmt
			when glt.accountType = 'Asset' and gl.GLCode = 'ACCOUNTSRECEIVABLE' and ats.debitAmt - ats.creditAmt > 0 then ats.debitAmt - ats.creditAmt
			when glt.accountType = 'Liability' and gl.GLCode = 'DEPOSITS' and ats.creditAmt - ats.debitAmt <= 0 then abs(ats.creditAmt - ats.debitAmt)
			when glt.accountType = 'Revenue' and ats.creditAmt - ats.debitAmt < 0 then abs(ats.creditAmt - ats.debitAmt)
			when glt.accountType = 'Expense' and ats.debitAmt - ats.creditAmt >= 0 then ats.debitAmt - ats.creditAmt
			when glt.accountType = 'Liability' and ats.creditAmt - ats.debitAmt <= 0 then abs(ats.creditAmt - ats.debitAmt)
			else null
			end as debits,
			case 
			when glt.accountType = 'Cash' and ats.debitAmt - ats.creditAmt < 0 then abs(ats.debitAmt - ats.creditAmt)
			when glt.accountType = 'Asset' and gl.GLCode = 'ACCOUNTSRECEIVABLE' and ats.debitAmt - ats.creditAmt <= 0 then abs(ats.debitAmt - ats.creditAmt)
			when glt.accountType = 'Liability' and gl.GLCode = 'DEPOSITS' and ats.creditAmt - ats.debitAmt > 0 then ats.creditAmt - ats.debitAmt
			when glt.accountType = 'Revenue' and ats.creditAmt - ats.debitAmt >= 0 then ats.creditAmt - ats.debitAmt
			when glt.accountType = 'Expense' and ats.debitAmt - ats.creditAmt < 0 then abs(ats.debitAmt - ats.creditAmt)
			when glt.accountType = 'Liability' and ats.creditAmt - ats.debitAmt > 0 then ats.creditAmt - ats.debitAmt
			else null
			end as credits
		from #tmpGLallTransSumNoBatch as ats
		INNER JOIN dbo.tr_GLAccounts as gl on gl.GLAccountID = ats.GLAccountID
		INNER JOIN dbo.tr_GLAccountTypes as glt on glt.accountTypeID = gl.accountTypeID
		INNER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) as rgl on rgl.GLAccountID = gl.GLAccountID
	) tmp
	where isnull(debits,0)+isnull(credits,0) <> 0
	order by thePath

	IF @FinalFormat = 'scr' BEGIN
		-- qryGLActivity
		select GLAccountID, AccountCode, accountType, thePathExpanded, debits, credits
		from #tmpBatchDetailGLActivityNoBatch
		order by thePath

		-- qryGLActivitySum
		select sum(debits) as debitsSum, sum(credits) as creditsSum
		from #tmpBatchDetailGLActivityNoBatch
	END

	IF @FinalFormat = 'csv' BEGIN
		SELECT @fullsql = '
			select thePathExpanded as [GL Account], AccountCode as [GL AccountCode], Debits, Credits
			from #tmpBatchDetailGLActivityNoBatch
			order by thePath'

		set @tmpFilename = replace(@filename,'.csv','GL.csv')
		EXEC dbo.up_exportCSV @csvfilename=@tmpFilename, @sql=@fullsql

		select ROW_NUMBER() OVER (ORDER BY tmp.batchID, tmp.thePath) as finalRow,
			convert(varchar(10),b.depositDate,101) as colA, 
			right(convert(varchar(10),b.depositDate,101),4) + '-' + cast(tmp.batchID as varchar(10)) as colB,
			(select count(*) from #tmpBatchDetailGLActivity where batchID = tmp.batchID) as colC, 
			tmp.AccountCode as colD, 
			mp.profileName as colE, 
			case 
			when tmp.Debits is not null then tmp.Debits
			when tmp.Credits is not null then tmp.Credits*-1
			else 0
			end as colF, 
			'0' as colG, '0' as colH
		into #tmpSAGEExport	
		from #tmpBatchDetailGLActivity as tmp
		inner join dbo.tr_batches as b on b.batchID = tmp.batchID
		inner join dbo.mp_profiles as mp on mp.profileID = b.payProfileID

		SELECT @fullsql = '
			select colA as [Date], colB as [Reference], colC as [Number of Distributions], colD as [G/L Account], colE as [Description], 
				colF as [Amount], colG as [Recur Number], colH as [Recur Frequency]
			from #tmpSAGEExport
			order by finalrow'

		set @tmpFilename = replace(@filename,'.csv','GL-Sage.csv')
		EXEC dbo.up_exportCSV @csvfilename=@tmpFilename, @sql=@fullsql
	END

	IF @FinalFormat = 'iif' BEGIN
		select distinct b.batchID, b.batchName, b.depositDate, p.profileName, null as GLAccountID, 
			isnull(p.bankAccountName,'') as accountCode, null as accountName, cast('' as varchar(400)) as accountClass,
			'Batch' as accountType, null as thePathExpanded,
			cast(0 as varchar(max)) as thePath, 
			case when actual.actualAmount >= 0 then abs(actual.actualAmount) else null end as debits,
			case when actual.actualAmount >= 0 then null else abs(actual.actualAmount) end as credits,
			case when actual.actualAmount < 0 then 'CHECK' ELSE 'DEPOSIT' end as IIFTRNSTYPE
		into #tmpIIFInnerMagic
		from #tmpBatchDetailGLActivity as tmp
		inner join dbo.tr_batches as b on b.batchID = tmp.batchID
		inner join dbo.mp_profiles as p on p.profileID = b.payProfileID
		outer apply dbo.fn_tr_getBatchActual(b.batchID) as actual
			union all
		select b.batchID, b.batchName, b.depositDate, p.profileName, tmp.GLAccountID, 
			replace(isnull(tmp.AccountCode,''),'|'+replace(parseName(replace(replace(isnull(tmp.AccountCode,''),'.',char(7)),'|','.'),1),char(7),'.'),'') as accountCode,
			tmp.thePathExpanded as accountName, 
			replace(parseName(replace(replace(isnull(tmp.accountCode,''),'.',char(7)),'|','.'),1),char(7),'.') as accountClass, 
			tmp.accountType, tmp.thePathExpanded, tmp.thePath,
			tmp.debits, tmp.credits, '' as IIFTRNSTYPE
		from #tmpBatchDetailGLActivity as tmp
		inner join dbo.tr_batches as b on b.batchID = tmp.batchID
		inner join dbo.mp_profiles as p on p.profileID = b.payProfileID
		where tmp.accountType <> 'Cash'
		order by batchID, thePath

		-- nullIf here because passing empty string caused file to not be importable
		select 
			nullIf(colA, '') as colA, 
			nullIf(colB, '') as colB, 
			nullIf(colC, '') as colC, 
			nullIf(colD, '') as colD, 
			nullIf(colE, '') as colE, 
			nullIf(colF, '') as colF, 
			nullIf(colG, '') as colG, 
			nullIf(colH, '') as colH, 
			nullIf(colI, '') as colI, 
			nullIf(colJ, '') as colJ 
		into ##tmpIIFExport
		from (
			select 1 as finalrow, '!TRNS' as colA, 'TRNSID' as colB, 'TRNSTYPE' as colC, 'DATE' as colD, 'ACCNT' as colE, 'NAME' as colF, 'CLASS' as colG, 'MEMO' as colH, 'AMOUNT' as colI, 'CLEAR' as colJ
				union all
			select 2, '!SPL', 'SPLID', 'TRNSTYPE', 'DATE', 'ACCNT', 'NAME', 'CLASS', 'MEMO', 'AMOUNT', 'CLEAR'
				union all
			select 3, '!ENDTRNS', '', '', '', '', '', '', '', '', ''
				union all
			select 3+row, 
				colA = case when accountType = 'Batch' then 'TRNS' when thePath = '***********' then 'ENDTRNS' else 'SPL' end,
				colB = '',
				colC = case 
						when accountType = 'Batch' then IIFTRNSTYPE 
						when thePath = '***********' then '' 
						else (select top 1 td.IIFTRNSTYPE from #tmpIIFInnerMagic as td where td.batchID = theData.batchID and len(td.IIFTRNSTYPE) > 0)
						end,
				colD = isnull(LTRIM(STR(MONTH(depositDate)))+'/'+LTRIM(STR(DAY(depositDate)))+'/'+RIGHT(STR(YEAR(depositDate),4),2),''),
				colE = AccountCode,
				colF = case when accountType = 'Batch' then profileName else '' end,
				colG = case when accountClass = accountCode then '' else accountClass end,
				colH = case 
						when accountType = 'Batch' then batchName 
						when credits is not null then 'Credit ' + thePathExpanded + isnull(' (' + nullif(AccountCode,'') + ')','') 
						when debits is not null then 'Debit ' + thePathExpanded + isnull(' (' + nullif(AccountCode,'') + ')','')
						else '' end,
				colI = isnull(cast(case
						when thePath = '***********' then null
						when credits is not null then credits * -1
						when debits is not null then debits
						else 0 end as varchar(30)),''),
				colJ = case when thePath = '***********' then '' else 'N' end
			from (
				select batchID, batchName, depositDate, profileName, AccountCode, AccountName, accountClass,
					AccountType, thePathExpanded, thePath, debits, credits, IIFTRNSTYPE, 
					ROW_NUMBER() OVER (order by batchID, thePath) as row
				from (
					select batchID, batchName, depositDate, profileName, GLAccountID, AccountCode, accountName, 
						accountClass, accountType, thePathExpanded, thePath, debits, credits, IIFTRNSTYPE
					from #tmpIIFInnerMagic
						union all
					select distinct batchID, '', null, '', null, '', '', '', '', '', '***********', null, null, null
					from #tmpIIFInnerMagic
				) tmp
				where isnull(debits,0)+isnull(credits,0) <> 0 OR thePath = '***********' OR accountType = 'Batch'
			) as theData
		) as finaldata
		order by finalrow
		
		set @bcpcmd = 'bcp ##tmpIIFExport out "' + @filename + '" -c -t0x09 -T -S' + CAST(serverproperty('servername') as varchar(20))
		exec master..xp_cmdshell @bcpcmd, NO_OUTPUT
	END
END


-- drop any existing temp tables
IF OBJECT_ID('tempdb..#tblB') IS NOT NULL 
	DROP TABLE #tblB
IF OBJECT_ID('tempdb..#tblGL') IS NOT NULL 
	DROP TABLE #tblGL
IF OBJECT_ID('tempdb..#tmpBatchDetailReport') IS NOT NULL 
	DROP TABLE #tmpBatchDetailReport
IF OBJECT_ID('tempdb..#tmpGLallTransSum') IS NOT NULL 
	DROP TABLE #tmpGLallTransSum
IF OBJECT_ID('tempdb..#tmpGLallTransSumNoBatch') IS NOT NULL 
	DROP TABLE #tmpGLallTransSumNoBatch
IF OBJECT_ID('tempdb..#tmpBatchDetailGLActivity') IS NOT NULL 
	DROP TABLE #tmpBatchDetailGLActivity
IF OBJECT_ID('tempdb..#tmpBatchDetailGLActivityNoBatch') IS NOT NULL 
	DROP TABLE #tmpBatchDetailGLActivityNoBatch
IF @FinalFormat = 'csv' BEGIN
	IF OBJECT_ID('tempdb..#tmpBatchDetailReportExport') IS NOT NULL 
		DROP TABLE #tmpBatchDetailReportExport
	IF OBJECT_ID('tempdb..#tmpBatchDetailReportExpandedExport') IS NOT NULL 
		DROP TABLE #tmpBatchDetailReportExpandedExport
	IF OBJECT_ID('tempdb..#tmpSAGEExport') IS NOT NULL 
		DROP TABLE #tmpSAGEExport
END
IF @FinalFormat = 'ach' BEGIN
	IF OBJECT_ID('tempdb..#tmpBatchDetailACH') IS NOT NULL 
		DROP TABLE #tmpBatchDetailACH
	IF OBJECT_ID('tempdb..##tmpACHExport') IS NOT NULL 
		DROP TABLE ##tmpACHExport
END
IF @FinalFormat = 'iif' BEGIN
	IF OBJECT_ID('tempdb..#tmpIIFInnerMagic') IS NOT NULL 
		DROP TABLE #tmpIIFInnerMagic
	IF OBJECT_ID('tempdb..##tmpIIFExport') IS NOT NULL 
		DROP TABLE ##tmpIIFExport
END

	
RETURN 0
GO