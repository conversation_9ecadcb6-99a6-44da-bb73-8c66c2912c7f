use membercentral
GO
ALTER TABLE dbo.cms_documentVersions ADD recordedByMemberId int NULL
GO
update dbo.cms_documentVersions
set recordedByMemberId = contributorMemberID
GO
ALTER TABLE dbo.cms_documentVersions ALTER COLUMN recordedByMemberId int NOT NULL
GO
ALTER TABLE dbo.cms_documentVersions ADD CONSTRAINT
	FK_cms_documentVersions_ams_members1 FOREIGN KEY
	(
	recordedByMemberId
	) REFERENCES dbo.ams_members
	(
	memberID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
GO

ALTER PROC [dbo].[cms_createDocumentVersion]
@documentLanguageID int,
@filename varchar(255),
@fileExt varchar(20),
<AUTHOR>
@contributorMemberID int,
@recordedByMemberID int,
@isActive bit,
@publicationDate datetime,
@documentVersionID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- ensure @documentVersionID is null (can be passed in)
	SELECT @documentVersionID = null

	-- ensure active contributer memberid
	SELECT @contributorMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @contributorMemberID

	-- if isactive = 1, then deactivate all other versions of this documentLanguageID
	IF @isactive = 1 BEGIN
		UPDATE dbo.cms_documentVersions
		SET isActive = 0
		WHERE documentLanguageID = @documentLanguageID
	END

	-- add document version
	INSERT INTO dbo.cms_documentVersions (documentLanguageID, [fileName], fileExt, publicationDate, contributorMemberID, isActive, dateCreated, dateModified, author, recordedByMemberID)
	VALUES (@documentLanguageID, @fileName, @fileExt, @publicationDate, @contributorMemberID, @isactive, getdate(), getdate(), @author, @recordedByMemberID)
		SELECT @documentVersionID = SCOPE_IDENTITY()

	-- update documentLanguages dateModified
	UPDATE dbo.cms_documentLanguages 
	SET dateModified = getdate()
	WHERE documentLanguageID = @documentLanguageID

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	SELECT @documentVersionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[cms_createDocumentObject]
@siteID int,
@resourceTypeID int,
@parentSiteResourceID int = NULL,
@siteResourceStatusID int,
@sectionID int,
@languageID int,
@contributorMemberID int,
@recordedByMemberID int,
@isActive bit,
@isVisible bit,
@docTitle varchar(200),
@docDesc varchar(max),
<AUTHOR>
@publicationDate datetime,
@fileName varchar(255),
@fileExt varchar(20),
@documentID int OUTPUT,
@documentVersionID int OUTPUT,
@siteResourceID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @documentLanguageID int

	-- ensure documentID, documentVersionID and siteResourceID are null (can be passed in)
	SELECT @documentID = null
	SELECT @documentVersionID = null
	SELECT @siteResourceID = null

	-- ensure active contributer memberid
	SELECT @contributorMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @contributorMemberID

	-- first create a resourceID for the document
	exec dbo.cms_createSiteResource @resourceTypeID = @resourceTypeID, @siteResourceStatusID = @siteResourceStatusID, @siteID = @siteid,
			@isVisible = @isVisible, @parentSiteResourceID = @parentSiteResourceID, @siteResourceID   = @siteResourceID OUTPUT

	-- add the document
	INSERT INTO dbo.cms_documents (siteID, siteResourceID, sectionID, dateCreated)
	VALUES (@siteid, @siteResourceID, @sectionID, getdate())
		SELECT @documentID = SCOPE_IDENTITY()

	-- add document language
	INSERT INTO dbo.cms_documentLanguages (documentID, languageID, docTitle, docDesc, dateCreated, dateModified)
	VALUES (@documentID, @languageID, isnull(@docTitle,''), isnull(@docDesc,''), getdate(), getdate())
		SELECT @documentLanguageID = SCOPE_IDENTITY()

	-- add content version
	EXEC dbo.cms_createDocumentVersion @documentLanguageID=@documentLanguageID, @fileName=@fileName, @fileExt=@fileExt, @author=@author, 
		@contributorMemberID=@contributorMemberID, @recordedByMemberID=@recordedByMemberID, @isActive=@isActive, @publicationDate=@publicationDate, 
		@documentVersionID=@documentVersionID OUTPUT


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	SELECT @documentID = 0
	SELECT @documentVersionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[cms_createDocument]
@siteID int,
@resourceTypeID int,
@siteResourceStatusID int,
@languageID int,
@sectionID int,
@contributorMemberID int,
@recordedByMemberID int,
@isActive bit,
@isVisible bit,
@docTitle varchar(200),
@docDesc varchar(max),
<AUTHOR>
@publicationDate datetime,
@fileName varchar(255),
@fileExt varchar(20),
@documentID int OUTPUT,
@documentVersionID int OUTPUT,
@documentSiteResourceID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	DECLARE @siteResourceID int

	-- ensure documentID is null (can be passed in)
	SELECT @documentVersionID = null, @documentID = null, @documentSiteResourceID = null

	-- ensure active contributer memberid
	SELECT @contributorMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @contributorMemberID

	-- ensure the siteID/sectionID combo is valid
	DECLARE	@SID int
	SELECT TOP 1 @SID = sectionID
		FROM dbo.cms_pageSections
		WHERE siteID = @siteID 
		AND sectionID = @sectionID
	IF (@SID IS NOT NULL) BEGIN
		EXEC dbo.cms_createDocumentObject @siteID=@siteID, @resourceTypeID=@resourceTypeID, @siteResourceStatusID=@siteResourceStatusID, 
			@sectionID=@sectionID, @languageID=@languageID, @contributorMemberID=@contributorMemberID, @recordedByMemberID=@recordedByMemberID, 
			@isActive=@isActive, @isVisible=@isVisible, @docTitle=@docTitle, @docDesc=@docDesc, @author=@author, @publicationDate=@publicationDate, 
			@fileName=@fileName, @fileExt=@fileExt, @documentID=@documentID OUTPUT, @documentVersionID=@documentVersionID OUTPUT, 
			@siteResourceID=@documentSiteResourceID OUTPUT
	END

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	SELECT @documentID = 0
	SELECT @documentVersionID = 0
	SELECT @documentSiteResourceID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[cms_updateDocument]
@documentID int,
@languageID int,
@siteID int,
@sectionID int,
@contributorMemberID int,
@recordedByMemberID int,
<AUTHOR>
@publicationDate datetime,
@docTitle varchar(255),
@docDesc varchar(max),
@fileName varchar(255),
@fileExt varchar(20),
@newFileUploaded bit,
@docVersionID int OUTPUT

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- ensure @documentVersionID is null (can be passed in)
	SELECT @docVersionID = 0

	-- ensure active contributer memberid
	SELECT @contributorMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @contributorMemberID

	-- ensure the siteID/sectionID combo is valid
	DECLARE	@SID int
	SELECT	TOP 1 @SID = sectionID
		FROM	dbo.cms_pageSections
		WHERE	siteID = @siteID 
		AND sectionID = @sectionID

	IF (@SID IS NOT NULL OR @sectionID=0) BEGIN
		IF (@sectionID<>0) BEGIN
			UPDATE	dbo.cms_documents
			SET		sectionID=@sectionID 
			WHERE	documentID = @documentID
		END 

		-- if language is there, update it if anything changed -- otherwise add it if new
		DECLARE	@documentLanguageID int, @title varchar(255), @desc varchar(max)
		SELECT	@documentLanguageID = documentLanguageID, @title = docTitle, @desc = docDesc from dbo.cms_documentLanguages where documentID = @documentID and languageID = @languageID
		IF @documentLanguageID is null BEGIN
			INSERT INTO dbo.cms_documentLanguages (documentID, languageID, docTitle, docDesc, dateCreated, dateModified)
			VALUES (@documentID, @languageID, @docTitle, @docDesc, getdate(), getdate())
				SELECT @documentLanguageID = SCOPE_IDENTITY()
		END
		ELSE BEGIN
			IF @title <> @docTitle OR @desc <> @docDesc BEGIN
				UPDATE	dbo.cms_documentLanguages
				SET		docTitle = @docTitle, 
						docDesc = @docDesc,
						dateModified = getdate()
				WHERE documentLanguageID = @documentLanguageID
			END
		END
		
		-- Are they uploading a new file? If so, create a new version of it
		-- Otherwise, just update the publicationDate and author fields
		DECLARE	@documentVersionID int

		IF @newFileUploaded = 'TRUE' BEGIN
			EXEC dbo.cms_createDocumentVersion @documentLanguageID=@documentLanguageID, @fileName=@fileName, @fileExt=@fileExt, 
				@publicationDate=@publicationDate, @contributorMemberID=@contributorMemberID, @recordedByMemberID=@recordedByMemberID, 
				@isActive=1, @author=@author, @documentVersionID=@documentVersionID OUTPUT
			SELECT @docVersionID = @documentVersionID
		END ELSE BEGIN
			SELECT @docVersionID = documentVersionID 
			FROM dbo.cms_documentVersions
			WHERE documentLanguageID = @documentLanguageID
			AND isActive = 1
		END

		UPDATE dbo.cms_documentVersions
		SET	publicationDate = @publicationDate,
			author = @author,
			dateModified = getDate()
		WHERE documentVersionID = @docVersionID
	END


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

