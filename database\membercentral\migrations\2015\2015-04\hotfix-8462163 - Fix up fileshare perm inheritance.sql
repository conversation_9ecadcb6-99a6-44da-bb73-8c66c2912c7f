
-- clear groupID from perms that have both inheritance and direct group assigned
update srr set     groupID = null
from fs_fileshare fs
inner join cms_applicationinstances ai
    on ai.applicationInstanceID = fs.applicationInstanceID
inner join cms_pageSections ps
    on ps.sectionID = fs.rootSectionID
inner join dbo.cache_cms_recursivePageSections rps
    on rps.sectionID = ps.sectionID
inner join cms_documents d
    on d.sectionID = rps.startsectionID
inner join cms_siteResourceRights srr
    on srr.resourceID = d.siteResourceID
    and srr.groupID is not null
    and inheritedRightsResourceID is not null
inner join cms_siteResourceRightsCache srrc
    on srrc.resourceID = d.siteResourceID
inner join cms_siteResourceRights srrinh
    on srrinh.resourceID = srr.inheritedRightsResourceID
    and srrinh.functionID = srr.inheritedRightsFunctionID

GO

declare 
	@autoID int,
	@siteResourceID int,
	@include bit,
	@functionID int,
	@roleID int,
	@groupID int,
	@inheritedRightsResourceID int,
	@inheritedRightsFunctionID int,
	@siteID int,
	@trashID int,
    @siteResourceRightID int,
	@totalCount int,
    @message varchar(100)


declare @permsToRemove TABLE (autoID int IDENTITY(1,1) PRIMARY KEY, siteID int, siteResourceID int, siteResourceRightID int)
declare @permsToAdd TABLE (autoID int IDENTITY(1,1) PRIMARY KEY, siteID int, siteResourceID int, include bit, functionID int, roleID int, groupID int, inheritedRightsResourceID int, inheritedRightsFunctionID int)

-- write your query to populate the @permsToAdd table
declare @viewfunctionID int, @searchOrgFunctionID int

select @viewfunctionID = dbo.fn_getResourceFunctionID('view',dbo.fn_getResourceTypeID('ApplicationCreatedDocument'))


insert into @permsToAdd (siteID , siteResourceID , include , functionID , roleID , groupID , inheritedRightsResourceID , inheritedRightsFunctionID )
select d.siteID , d.siteResourceID , 1 as include , @viewfunctionID , null , null , ai.siteResourceID , @viewfunctionID
from fs_fileshare fs
inner join cms_applicationinstances ai
    on ai.applicationInstanceID = fs.applicationInstanceID
inner join cms_pageSections ps
    on ps.sectionID = fs.rootSectionID
inner join dbo.cache_cms_recursivePageSections rps
    on rps.sectionID = ps.sectionID
inner join cms_documents d
    on d.sectionID = rps.startsectionID
inner join cms_siteResourceRights srr
    on srr.resourceID = d.siteResourceID
--    and srr.groupID is not null
    and inheritedRightsResourceID is null

insert into @permsToAdd (siteID , siteResourceID , include , functionID , roleID , groupID , inheritedRightsResourceID , inheritedRightsFunctionID )
select d.siteID , d.siteResourceID , 1 as include , @viewfunctionID , null , null , ai.siteResourceID , @viewfunctionID
from fs_fileshare fs
inner join cms_applicationinstances ai
    on ai.applicationInstanceID = fs.applicationInstanceID
inner join cms_pageSections ps
    on ps.sectionID = fs.rootSectionID
inner join dbo.cache_cms_recursivePageSections rps
    on rps.sectionID = ps.sectionID
inner join cms_documents d
    on d.sectionID = rps.startsectionID
left outer join cms_siteResourceRights srr
    on srr.resourceID = d.siteResourceID
where srr.resourceID is null


-- remove rights with that have no inheritance 
insert into @permsToRemove (siteID , siteResourceID , siteResourceRightID )
select d.siteID , d.siteResourceID , srr.resourceRightsID
from fs_fileshare fs
inner join cms_applicationinstances ai
    on ai.applicationInstanceID = fs.applicationInstanceID
inner join cms_pageSections ps
    on ps.sectionID = fs.rootSectionID
inner join dbo.cache_cms_recursivePageSections rps
    on rps.sectionID = ps.sectionID
inner join cms_documents d
    on d.sectionID = rps.startsectionID
inner join cms_siteResourceRights srr
    on srr.resourceID = d.siteResourceID
    and inheritedRightsResourceID is null



select @totalCount = count(*) from @permsToAdd

select @autoID = min(autoID) from @permsToAdd
while @autoID is not null
begin

	select 
		@siteResourceID = siteResourceID, 
		@include = include, 
		@functionID = functionID, 
		@roleID = roleID, 
		@groupID = groupID, 
		@inheritedRightsResourceID = inheritedRightsResourceID, 
		@inheritedRightsFunctionID = inheritedRightsFunctionID
	from @permsToAdd
	where autoID = @autoID

	exec dbo.cms_createSiteResourceRight
		@siteID=@siteID,
		@siteResourceID=@siteResourceID, 
		@include=@include, 
		@functionID=@functionID, 
		@roleID=@roleID, 
		@groupID=@groupID,
		@memberID = null,
		@inheritedRightsResourceID=@inheritedRightsResourceID, 
		@inheritedRightsFunctionID=@inheritedRightsFunctionID,
		@resourceRightID=@trashID OUTPUT

    
     set @message = 'Loop Count Added ' + convert(varchar(10),@autoID) + ' of ' + convert(varchar(10),@totalCount)

	RAISERROR(@message,0,1)

	select @autoID = min(autoID) from @permsToAdd where autoID > @autoID
end




select @totalCount = count(*) from @permsToRemove

select @autoID = min(autoID) from @permsToRemove
while @autoID is not null
begin
	select 
	    @siteResourceID = siteResourceID ,
	    @siteResourceRightID = siteResourceRightID ,
	    @siteID = siteID
	from @permsToRemove
	where autoID = @autoID

	exec dbo.cms_deleteSiteResourceRight
		@siteID=@siteID,
		@siteResourceID=@siteResourceID, 
		@siteResourceRightID=@siteResourceRightID
    
     set @message = 'Loop Count Deleted ' + convert(varchar(10),@autoID) + ' of ' + convert(varchar(10),@totalCount)

	RAISERROR(@message,0,1)

	select @autoID = min(autoID) from @permsToRemove where autoID > @autoID
end