
-- Need to update log types table to have <PERSON>builder as message

USE [seminarWeb]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[sw_toggleRequired]
@seminarID int,
@seminarformID int

AS

DECLARE @currentValue bit
SELECT @currentValue = isRequired from dbo.tblSeminarsAndForms where seminarID = @seminarID and seminarformID = @seminarformID

IF @currentValue = 1 BEGIN
	UPDATE dbo.tblSeminarsAndForms
	set isRequired = 0
	where seminarID = @seminarID
	and seminarformID = @seminarformID
		IF @@ERROR <> 0 GOTO on_error
END
ELSE BEGIN
	UPDATE dbo.tblSeminarsAndForms
	set isRequired = 1
	where seminarID = @seminarID
	and seminarformID = @seminarformID
		IF @@ERROR <> 0 GOTO on_error
END

RETURN 1

on_error:
RETURN 0


USE [seminarWeb]
GO
/****** Object:  StoredProcedure [dbo].[swl_getSeminarProgress]    Script Date: 01/21/2014 13:55:15 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROC [dbo].[swl_getSeminarProgress]
@enrollmentID int

AS

-- qry1: seminar settings
SELECT swl.isOpen
from dbo.tblEnrollments as e
inner join dbo.tblSeminars as s on s.seminarID = e.seminarID
inner join dbo.tblSeminarsSWLive as swl on swl.seminarID = s.seminarID
where e.enrollmentID = @enrollmentID

-- qry2: data used to determine progress
DECLARE @allPreTestCompleted bit, @allPostTestCompleted bit, @allEvaluationCompleted bit, @hasCertificate bit
SELECT @allPreTestCompleted = 
	CASE 
	WHEN (select count(saf.formID)
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblSeminarsAndForms AS saf ON e.seminarID = saf.seminarID
			LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
				INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
				INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
					and f.isPublished = 1
					and getdate() between f.dateStartPublish and f.dateEndPublish	
					and r.passingPct >= f.passingPct				
				on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e.enrollmentID
			WHERE e.enrollmentID = @enrollmentID
			AND saf.loadPoint = 'preTest'
			AND saf.isRequired = 1
			AND safr.responseID is null) > 0 THEN 0
	ELSE 1
	END
SELECT @allPostTestCompleted = 
	CASE 
	WHEN (select count(saf.formID)
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblSeminarsAndForms AS saf ON e.seminarID = saf.seminarID
			LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
				INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
				INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
					and f.isPublished = 1
					and getdate() between f.dateStartPublish and f.dateEndPublish	
					and r.passingPct >= f.passingPct				
				on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e.enrollmentID
			WHERE e.enrollmentID = @enrollmentID
			AND saf.loadPoint = 'postTest'
			AND saf.isRequired = 1
			AND safr.responseID is null) > 0 THEN 0
	ELSE 1
	END
SELECT @allEvaluationCompleted = 
	CASE 
	WHEN (select count(saf.formID)
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblSeminarsAndForms AS saf ON e.seminarID = saf.seminarID
			LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
				INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
				INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
					and f.isPublished = 1
					and getdate() between f.dateStartPublish and f.dateEndPublish	
				on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e.enrollmentID
			WHERE e.enrollmentID = @enrollmentID
			AND saf.loadPoint = 'evaluation'
			AND exists(
				SELECT sac.seminarCreditID, eac.idNumber, eac.earnedCertificate, caswl.evaluationRequired
				FROM dbo.tblEnrollmentsAndCredit AS eac 
				INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
				INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID 
				INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
				INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID 
				INNER JOIN dbo.tblCreditStatuses AS cstat ON sac.statusID = cstat.statusID
				INNER JOIN dbo.tblCreditAuthoritiesSWLive as caswl on caswl.authorityID = ca.authorityID
				WHERE eac.enrollmentID = @enrollmentID
				and evaluationRequired = 1)
			AND (safr.responseID is null or r.dateCompleted is null)) > 0 THEN 0
	ELSE 1
	END
SELECT @hasCertificate = 
	CASE 
	WHEN (SELECT count(e.enrollmentID)
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
			INNER JOIN dbo.tblSeminars AS s ON e.seminarID = s.seminarID 
			INNER JOIN dbo.tblSeminarsSWLive AS sswl ON s.seminarID = sswl.seminarID
			WHERE e.enrollmentID = @enrollmentID 
			AND s.isDeleted = 0
			AND e.isActive = 1
			AND s.offerCertificate = 1
			AND e.passed = 1
			AND LEN(e.dateCompleted) > 0) > 0 THEN 1
	ELSE 0
	END
SELECT @allPreTestCompleted as allPreTestCompleted, 
	@allPostTestCompleted as allPostTestCompleted,
	@allEvaluationCompleted as allEvaluationCompleted,
	@hasCertificate as hasCertificate





	
USE [seminarWeb]
GO
/****** Object:  StoredProcedure [dbo].[swl_verifySWLCode]    Script Date: 01/09/2014 16:21:23 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROC [dbo].[swl_verifySWLCode]
@SWLCode char(10)

AS

SELECT TOP 1 e.enrollmentID, p.catalogURL, sswl.gotoMeetingID, eswl.goToMeetingUID, eswl.SWLCode
FROM dbo.tblEnrollments AS e 
INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
INNER JOIN dbo.tblSeminars AS s ON e.seminarID = s.seminarID 
INNER JOIN dbo.tblSeminarsSWLive AS sswl ON s.seminarID = sswl.seminarID 
WHERE eswl.SWLCode = @SWLCode
AND LEN(eswl.SWLCode) > 0
AND LEN(eswl.goToMeetingUID) > 0
AND e.isActive = 1
-- Removed for SemWeb 109
--AND e.dateCompleted is null
--AND eswl.attended = 0


USE [seminarWeb]
GO
/****** Object:  UserDefinedFunction [dbo].[fn_swl_getEnrollments]    Script Date: 01/22/2014 08:50:04 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER Function [dbo].[fn_swl_getEnrollments] (
	@seminarID int
) 
returns table
AS
RETURN (

	SELECT TOP 100 PERCENT e.enrollmentID, e.userid, eswl.goToMeetingUID, ps.pin, eswl.swlcode, p.orgcode, p.catalogURL, 
	d.depomemberdataID, d.FirstName, d.LastName, d.Fax, d.Email, e.dateEnrolled, eswl.attended, eswl.attendedPhone, 
	eswl.joinTime, eswl.exitTime, eswl.duration, eswl.durationPhone, eswl.completedPolling, e.dateCompleted, e.passed, 
	(SELECT COUNT(*) FROM dbo.tblLogSWLive AS log1 WHERE enrollmentID = e.enrollmentID AND seminarID = e.seminarID AND contact LIKE '%@%') AS EmailCount,
	(SELECT COUNT(*) FROM dbo.tblLogSWLive AS log2 WHERE enrollmentID = e.enrollmentID AND seminarID = e.seminarID AND contact NOT LIKE '%@%') AS FaxCount,
	(SELECT COUNT(*) FROM dbo.tblEnrollmentsAndCredit WHERE enrollmentID = e.enrollmentID) AS CreditCount,
	pgtm.parkedID, sswl.phoneAttendee, sswl.codeAttendee,
	CASE 
	WHEN (select count(saf.formID)
			FROM dbo.tblEnrollments AS e2
			INNER JOIN dbo.tblSeminarsAndForms AS saf ON e2.seminarID = saf.seminarID
			LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
				INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
				INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
					and f.isPublished = 1
					and getdate() between f.dateStartPublish and f.dateEndPublish	
				on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e2.enrollmentID
			WHERE e2.enrollmentID = e.enrollmentID
			AND saf.loadPoint = 'evaluation'
			AND exists(
				SELECT sac.seminarCreditID, eac.idNumber, eac.earnedCertificate, caswl.evaluationRequired
				FROM dbo.tblEnrollmentsAndCredit AS eac 
				INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
				INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID 
				INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
				INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID 
				INNER JOIN dbo.tblCreditStatuses AS cstat ON sac.statusID = cstat.statusID
				INNER JOIN dbo.tblCreditAuthoritiesSWLive as caswl on caswl.authorityID = ca.authorityID
				WHERE eac.enrollmentID = e.enrollmentID
				and evaluationRequired = 1)
			AND (safr.responseID is null or r.dateCompleted is null)) > 0 THEN 0
	ELSE 1
	END as allEvaluationCompleted
	FROM dbo.tblEnrollments AS e 
		INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
		INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
		INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
		INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
		INNER JOIN dbo.tblSeminarsSWLive AS sswl ON e.seminarID = sswl.seminarID
		LEFT OUTER JOIN dbo.tblParkedGTMSeats AS pgtm ON e.enrollmentID = pgtm.enrollmentID
		LEFT OUTER JOIN dbo.tblParkedPhoneSeats AS ps ON e.enrollmentID = ps.enrollmentID
	WHERE e.seminarID = @seminarID
	AND e.isActive = 1
	ORDER BY e.dateEnrolled

)

USE [seminarWeb]
GO
/****** Object:  UserDefinedFunction [dbo].[fn_swl_getEnrollment]    Script Date: 01/22/2014 09:08:26 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE Function [dbo].[fn_swl_getEnrollment] (
	@enrollmentID int
) 
returns table
AS
RETURN (

	SELECT TOP 100 PERCENT e.seminarID, e.enrollmentID, e.userid, eswl.goToMeetingUID, ps.pin, eswl.swlcode, p.orgcode, p.catalogURL, 
	d.depomemberdataID, d.FirstName, d.LastName, d.Fax, d.Email, e.dateEnrolled, eswl.attended, eswl.attendedPhone, 
	eswl.joinTime, eswl.exitTime, eswl.duration, eswl.durationPhone, eswl.completedPolling, e.dateCompleted, e.passed, 
	(SELECT COUNT(*) FROM dbo.tblLogSWLive AS log1 WHERE enrollmentID = e.enrollmentID AND seminarID = e.seminarID AND contact LIKE '%@%') AS EmailCount,
	(SELECT COUNT(*) FROM dbo.tblLogSWLive AS log2 WHERE enrollmentID = e.enrollmentID AND seminarID = e.seminarID AND contact NOT LIKE '%@%') AS FaxCount,
	(SELECT COUNT(*) FROM dbo.tblEnrollmentsAndCredit WHERE enrollmentID = e.enrollmentID) AS CreditCount,
	pgtm.parkedID, sswl.phoneAttendee, sswl.codeAttendee,
	CASE 
	WHEN (select count(saf.formID)
			FROM dbo.tblEnrollments AS e2
			INNER JOIN dbo.tblSeminarsAndForms AS saf ON e2.seminarID = saf.seminarID
			LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
				INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
				INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
					and f.isPublished = 1
					and getdate() between f.dateStartPublish and f.dateEndPublish	
				on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e2.enrollmentID
			WHERE e2.enrollmentID = e.enrollmentID
			AND saf.loadPoint = 'evaluation'
			AND exists(
				SELECT sac.seminarCreditID, eac.idNumber, eac.earnedCertificate, caswl.evaluationRequired
				FROM dbo.tblEnrollmentsAndCredit AS eac 
				INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
				INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID 
				INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
				INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID 
				INNER JOIN dbo.tblCreditStatuses AS cstat ON sac.statusID = cstat.statusID
				INNER JOIN dbo.tblCreditAuthoritiesSWLive as caswl on caswl.authorityID = ca.authorityID
				WHERE eac.enrollmentID = e.enrollmentID
				and evaluationRequired = 1)
			AND (safr.responseID is null or r.dateCompleted is null)) > 0 THEN 0
	ELSE 1
	END as allEvaluationCompleted
	FROM dbo.tblEnrollments AS e 
		INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
		INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
		INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
		INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
		INNER JOIN dbo.tblSeminarsSWLive AS sswl ON e.seminarID = sswl.seminarID
		LEFT OUTER JOIN dbo.tblParkedGTMSeats AS pgtm ON e.enrollmentID = pgtm.enrollmentID
		LEFT OUTER JOIN dbo.tblParkedPhoneSeats AS ps ON e.enrollmentID = ps.enrollmentID
	WHERE e.enrollmentID = @enrollmentID
	AND e.isActive = 1
	ORDER BY e.dateEnrolled

)


USE [seminarWeb]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [dbo].[swl_getEnrollment]
@enrollmentID int

AS

SELECT *
FROM dbo.fn_swl_getEnrollment(@enrollmentID)

RETURN




USE [seminarWeb]
GO

BEGIN TRANSACTION
SET QUOTED_IDENTIFIER ON
SET ARITHABORT ON
SET NUMERIC_ROUNDABORT OFF
SET CONCAT_NULL_YIELDS_NULL ON
SET ANSI_NULLS ON
SET ANSI_PADDING ON
SET ANSI_WARNINGS ON
COMMIT
BEGIN TRANSACTION
GO
ALTER TABLE dbo.tblCreditAuthoritiesSWLive ADD
	daysToCompleteExam int NULL,
	daysToCompleteEvaluation int NULL
GO
COMMIT

USE [seminarWeb]
GO
/****** Object:  StoredProcedure [dbo].[sw_getCreditAuthority]    Script Date: 01/17/2014 10:28:01 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROC [dbo].[sw_getCreditAuthority]
@authorityID int

AS

SELECT ca.authorityID, ca.code, ca.jurisdiction, ca.authorityName, ca.contact, 
	ca.address, ca.city, ca.state, ca.ZIP, ca.phone, ca.fax, ca.email, ca.website, 
	ca.wddxCreditTypes, ca.creditIDText, 

	caswl.promptInterval as SWLpromptInterval, 
	caswl.promptTypeID as SWLpromptTypeID, 
	caswl.creditBaseID as SWLcreditBaseID, 
	caswl.mustAttend as SWLmustAttend, 
	caswl.mustAttendMinutes as SWLmustAttendMinutes,
	caswl.preExamRequired as SWLPreExamRequired,
	caswl.examRequired as SWLexamRequired,
	caswl.evaluationRequired as SWLEvaluationRequired,
	caswl.daysToCompleteExam as SWLdaysToCompleteExam,
	caswl.daysToCompleteEvaluation as SWLdaysToCompleteEvaluation,
	
	casod.promptInterval AS swodpromptInterval, 
	casod.promptTypeID AS swodpromptTypeID, 
	casod.creditBaseID AS swodcreditBaseID, 
	casod.QARequired AS swodQARequired, 
	casod.preExamRequired AS swodPreExamRequired, 
	casod.examRequired AS swodexamRequired, 
	casod.evaluationRequired AS swodevaluationRequired, 
	casod.mediaRequiredPct AS swodmediaRequiredPct, 
	casod.mustCompleteByRollover AS swodmustCompleteByRollover, 
	casod.rolloverDate AS swodrolloverDate, 
	casod.daysToComplete AS swoddaysToComplete, 
	casod.fastforwardPermitted AS swodfastforwardPermitted, 
	casod.mustAttendMinutes as swodMustAttendMinutes
FROM dbo.tblCreditAuthorities AS ca 
LEFT OUTER JOIN dbo.tblCreditAuthoritiesSWLive AS caswl ON ca.authorityID = caswl.authorityID 
LEFT OUTER JOIN dbo.tblCreditAuthoritiesSWOD AS casod ON ca.authorityID = casod.authorityID
WHERE ca.authorityID = @authorityID

RETURN



USE [seminarWeb]
GO
/****** Object:  StoredProcedure [dbo].[sw_getCreditsByEnrollmentID]    Script Date: 01/17/2014 13:51:15 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROC [dbo].[sw_getCreditsByEnrollmentID]
@enrollmentid int

AS

SELECT sac.seminarCreditID, csa.CSALinkID, eac.idNumber, eac.earnedCertificate, 
	ca.authorityID, ca.authorityName, cs.sponsorName, 
	CASE cstat.status 
	WHEN 'Approved' THEN cs.statementAppProgram 
	WHEN 'Pending' THEN cs.statementPendProgram 
	ELSE '' 
	END AS 'statement', 
	csa.certificateMessage, sac.courseApproval, ca.wddxCreditTypes, sac.wddxCreditsAvailable,
	ca.creditIDText, dbo.fn_isPollingRequiredBasedOnSCID(sac.seminarCreditID) as isPollingRequired,
	eac.earnedCertificate as completedPolling,
	eac.finalTimeSpent,
	sac.creditOfferedEndDate,
	sac.creditCompleteByDate
FROM dbo.tblEnrollmentsAndCredit AS eac 
INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID 
INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID 
INNER JOIN dbo.tblCreditStatuses AS cstat ON sac.statusID = cstat.statusID
WHERE eac.enrollmentID = @enrollmentID







USE [seminarWeb]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROC [dbo].[swl_hasNotMetDaysToComplete]
@enrollmentID int,
@daysToCompleteEvaluation int,
@daysToCompleteExam int
AS

-- create temp table to store all forms and exams
DECLARE @tmpTable TABLE (
	loadPoint varchar(255),
	enrollmentid int,
	dateCompleted datetime,
	formCompleteDate datetime,
	completeByDate datetime
)

if @daysToCompleteExam > 0 BEGIN
	insert into @tmpTable
	select saf.loadPoint, e.enrollmentid, e.dateCompleted, r.datecompleted as formCompleteDate, e.dateCompleted + @daysToCompleteExam as completeByDate
	FROM dbo.tblEnrollments AS e 
	INNER JOIN dbo.tblSeminarsAndForms AS saf ON e.seminarID = saf.seminarID
	LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
		INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
		INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
			and f.isPublished = 1
			and getdate() between f.dateStartPublish and f.dateEndPublish	
			and r.passingPct >= f.passingPct				
		on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e.enrollmentID
	WHERE e.enrollmentID = @enrollmentID
	AND saf.loadPoint = 'postTest'
	AND saf.isRequired = 1
END

if @daysToCompleteEvaluation > 0 BEGIN
	insert into @tmpTable
	select saf.loadPoint, e.enrollmentid, e.dateCompleted, r.datecompleted as formCompleteDate, e.dateCompleted + @daysToCompleteEvaluation as completeByDate
		FROM dbo.tblEnrollments AS e 
		INNER JOIN dbo.tblSeminarsAndForms AS saf ON e.seminarID = saf.seminarID
		LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
			INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
			INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
				and f.isPublished = 1
				and getdate() between f.dateStartPublish and f.dateEndPublish	
			on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e.enrollmentID
		WHERE e.enrollmentID = @enrollmentID
		AND saf.loadPoint = 'evaluation'
		AND saf.isRequired = 1
END

select * from @tmpTable
where formCompleteDate > completeByDate