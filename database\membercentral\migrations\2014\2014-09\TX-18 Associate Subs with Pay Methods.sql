USE [dataTransfer]
GO
IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[tmpTXSubs]') AND type in (N'U'))
DROP TABLE [dbo].[tmpTXSubs]
GO

CREATE TABLE [dbo].[tmpTXSubs](
	[membernumber] [varchar](30) NULL,
	[SubscriptionType] [varchar](100) NULL,
	[PayProfileCode] [varchar](100) NULL,
	[last4] [varchar](20) NULL
) ON [PRIMARY]
GO

DECLARE @qry varchar(2000)
SELECT @qry = 'BULK INSERT datatransfer.dbo.tmpTXSubs FROM ''e:\temp\Associate Subscriptions with Payment Methods on File.txt'' WITH (FIELDTERMINATOR = '''+ char(9) + ''', FIRSTROW = 2);'
EXEC(@qry)
GO

ALTER TABLE datatransfer.dbo.tmpTXSubs ADD memberID int, subtypeUID uniqueidentifier, payProfileID int, mppID int
GO

update tmp
set tmp.memberID = m.memberID
from datatransfer.dbo.tmpTXSubs as tmp
inner join membercentral.dbo.ams_members as m on m.membernumber = tmp.membernumber
	and m.orgID = 7
	and m.status = 'A'

if EXISTS (select * from datatransfer.dbo.tmpTXSubs where memberID is null) BEGIN
	print 'not all members found'
END ELSE BEGIN

	update tmp
	set tmp.subTypeUID = t.uid
	from datatransfer.dbo.tmpTXSubs as tmp
	inner join membercentral.dbo.sub_types as t on t.typeName = tmp.subscriptionType
		and t.siteID = 8
		and t.status = 'A'

	if EXISTS (select * from datatransfer.dbo.tmpTXSubs where subTypeUID is null) BEGIN
		print 'not all types found'
	END ELSE BEGIN

		update tmp
		set tmp.payprofileID = p.profileID
		from datatransfer.dbo.tmpTXSubs as tmp
		inner join membercentral.dbo.mp_profiles as p on p.profileCode = tmp.PayProfileCode
			and p.siteID = 8
			and p.status = 'A'
		
		if EXISTS (select * from datatransfer.dbo.tmpTXSubs where payprofileID is null) BEGIN
			print 'not all pay profiles found'
		END ELSE BEGIN

			update tmp
			set tmp.mppID = mpp.payProfileID
			from datatransfer.dbo.tmpTXSubs as tmp
			inner join membercentral.dbo.ams_memberPaymentProfiles as mpp on mpp.memberID = tmp.memberID
				and mpp.profileID = tmp.payProfileID
				and right(mpp.detail,4) = tmp.last4
				and mpp.status = 'A'

			if EXISTS (select * from datatransfer.dbo.tmpTXSubs where mppID is null) BEGIN
				print 'not all pay methods found'
			END

			update NEWs
			set NEWs.payProfileID = tmp2.mppID
			from membercentral.dbo.sub_subscribers as NEWs
			inner join (
				select s.subscriberID, tmp.mppID
				from datatransfer.dbo.tmpTXSubs as tmp
				inner join membercentral.dbo.sub_subscribers as s on s.memberID = tmp.memberID and s.rootsubscriberID = s.subscriberID
				inner join membercentral.dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
				inner join membercentral.dbo.sub_types as t on t.typeID = sub.typeID and t.uid = tmp.subTypeUID
				inner join membercentral.dbo.sub_statuses as ss on ss.statusID = s.statusID
				where tmp.mppID is not null
				and ss.statusname in ('Active', 'Accepted', 'Billed', 'Renewal Not Sent')
			) as tmp2 on tmp2.subscriberID = NEWs.subscriberID
	
		END

	END

END
GO

select * from datatransfer.dbo.tmpTXSubs 
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[tmpTXSubs]') AND type in (N'U'))
DROP TABLE [dbo].[tmpTXSubs]
GO
