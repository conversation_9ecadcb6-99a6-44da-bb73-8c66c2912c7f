ALTER PROCEDURE [dbo].[mc_updateListMemberships_error_handler]
	@messageToAppend varchar(1000),
	@message varchar(max) OUTPUT
AS
 
DECLARE
	@errmsg   nvarchar(2048),
	@severity tinyint,
	@state    tinyint,
	@errno    int,
	@proc     sysname,
	@lineno   int,
	@errorEntry varchar(max),
	@CRLF varchar(2),
	@messageDelimiter varchar(100)
          
SELECT @errmsg = error_message(), @severity = error_severity(),   -- 10
       @state  = error_state(), @errno = error_number(),
       @proc   = error_procedure(), @lineno = error_line()
       
IF @errmsg NOT LIKE '***%'                                        -- 11  
BEGIN 
   SELECT @errmsg = '*** ' + coalesce(quotename(@proc), '<dynamic SQL>') + 
                    ', ' + ltrim(str(@lineno)) + '. Errno ' + 
                    ltrim(str(@errno)) + ': ' + @errmsg
END

set @crlf = char(13) + char(10);
set @messageDelimiter = @crlf + @crlf + '-------------------------------' + @crlf + @crlf



set @errorEntry = 
	'Error received:' + @crlf + @crlf
	+ 'Last checkpoint: ' + @messageToAppend + @crlf + @crlf
	+ 'Error Message: ' + isnull(@errmsg,'') + @crlf + @crlf
	+ 'Severity: ' + isnull(cast(@severity as varchar(10)),'') + @crlf + @crlf
	+ 'State: ' + isnull(cast(@state as varchar(10)),'') + @crlf + @crlf
	+ 'ErrNo: ' + isnull(cast(@errno as varchar(10)),'') + @crlf + @crlf
	+ 'Proc: ' + isnull(cast(@proc as varchar(2000)),'') + @crlf + @crlf
	+ 'LineNo: ' + isnull(cast(@lineno as varchar(10)),'') + @crlf;

set @errorEntry = 'LYRIS DB: MC Update List Memberships Error - ' + isnull(@messageToAppend,'') + @crlf + @crlf + isnull(@errorEntry,'');

RAISERROR(@errorEntry,0,1)

set @message = @message + @messageDelimiter + ISNULL(@errorEntry,'')




return 0
GO
