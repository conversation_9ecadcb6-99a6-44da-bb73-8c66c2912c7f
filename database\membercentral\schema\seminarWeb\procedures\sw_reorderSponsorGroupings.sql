ALTER PROC dbo.sw_reorderSponsorGroupings
@seminarID INT,
@participantID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @tmp TABLE (neworder INT NOT NULL, sponsorGroupingID INT NOT NULL, sponsorGroupingOrder INT NOT NULL);
	
	INSERT INTO @tmp (sponsorGroupingID, sponsorGroupingOrder, newOrder)
	SELECT sponsorGroupingID, sponsorGroupingOrder, ROW_NUMBER() OVER(ORDER BY sponsorGroupingOrder) AS newOrder
	FROM dbo.sw_sponsorGrouping
	WHERE seminarID = @seminarID
	AND participantID = @participantID;
	
	UPDATE sg
	SET sg.sponsorGroupingOrder = t.neworder
	FROM dbo.sw_sponsorGrouping AS sg 
	INNER JOIN @tmp AS t ON sg.sponsorGroupingID = t.sponsorGroupingID
	WHERE sg.participantID = @participantID;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 R<PERSON>L<PERSON><PERSON><PERSON> TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
