use customApps
GO

CREATE PROCEDURE [dbo].[va_updateBarAndBirthDates] 
AS
BEGIN

declare @tblMemberDates TABLE (id int identity(1,1), memberID int, barYear int, birthYear int, barDate datetime, birthDate datetime)
declare @orgID int, @siteID int, @dtLastRan datetime, @taskID int, @historyID int
select @orgID=orgID, @siteID=siteID
from membercentral.dbo.sites 
where siteCode = 'VA'

SELECT @taskID=taskid
FROM membercentral.dbo.scheduledTasks
WHERE taskCFC is NULL
and [name] = 'VA Bar and Birth Date Updater'

SELECT @dtLastRan=ISNULL(MAX(dateStarted), '01/01/2012')
FROM membercentral.dbo.scheduledTaskHistory
WHERE statusTypeID = 2 
AND taskID = @taskID

select @dtLastRan as dtLastRan

insert into membercentral.dbo.scheduledTaskHistory (taskid,statusTypeID,dateStarted,serverid,dateLastUpdated)			
select @taskID as taskid, stst.statusTypeID, getDate(), 1 as serverID, getdate()
from membercentral.dbo.scheduledTaskStatusTypes stst
where stst.statusName = 'Pending'

select @historyID=SCOPE_IDENTITY()


declare @BarYearColID int, @BirthYearColID int, @BarDateColID int, @BirthDateColID int
declare @BarYearValue int, @BirthYearValue int, @BarDateValue datetime, @BirthDateValue datetime

select @BarYearColID = columnID
FROM membercentral.dbo.ams_memberDataColumns
where orgID = @orgID
and columnName = 'Bar_Year'

select @BirthYearColID = columnID
FROM membercentral.dbo.ams_memberDataColumns
where orgID = @orgID
and columnName = 'Birth_Year'

select @BarDateColID = columnID
FROM membercentral.dbo.ams_memberDataColumns
where orgID = @orgID
and columnName = 'Bar Date'

select @BirthDateColID = columnID
FROM membercentral.dbo.ams_memberDataColumns
where orgID = @orgID
and columnName = 'Birth Date'

insert into @tblMemberDates(memberID)
select m.memberID
from membercentral.dbo.ams_members m
where m.orgID = @orgID
and m.memberID = m.activeMemberID
and m.status <> 'D'
and m.dateLastUpdated > @dtLastRan

update tblMD
set tblMD.BarYear = mdcvBarYear.columnValueInteger
from @tblMemberDates tblMD
inner join membercentral.dbo.ams_memberData md
	on md.memberID = tblMD.memberID
inner join membercentral.dbo.ams_memberDataColumnValues mdcvBarYear
	on mdcvBarYear.valueID = md.valueID
	and mdcvBarYear.columnID = @BarYearColID

update tblMD
set tblMD.BirthYear = mdcvBirthYear.columnValueInteger
from @tblMemberDates tblMD
inner join membercentral.dbo.ams_memberData md
	on md.memberID = tblMD.memberID
inner join membercentral.dbo.ams_memberDataColumnValues mdcvBirthYear
	on mdcvBirthYear.valueID = md.valueID
	and mdcvBirthYear.columnID = @BirthYearColID

update tblMD
set tblMD.BarDate = mdcvBarDate.columnValueDate
from @tblMemberDates tblMD
inner join membercentral.dbo.ams_memberData md
	on md.memberID = tblMD.memberID
inner join membercentral.dbo.ams_memberDataColumnValues mdcvBarDate
	on mdcvBarDate.valueID = md.valueID
	and mdcvBarDate.columnID = @BarDateColID

update tblMD
set tblMD.BirthDate = mdcvBirthDate.columnValueDate
from @tblMemberDates tblMD
inner join membercentral.dbo.ams_memberData md
	on md.memberID = tblMD.memberID
inner join membercentral.dbo.ams_memberDataColumnValues mdcvBirthDate
	on mdcvBirthDate.valueID = md.valueID
	and mdcvBirthDate.columnID = @BirthDateColID

declare @minBarID int, @minBirthID int, @currMemberID int, @currBarYear int, @currBirthYear int, 
		@currBarDate varchar(100), @currBirthDate varchar(100), @good int, @oldBarDate dateTime, @oldBirthDate datetime


select @good = 1

select @minBarID = min(tmd.id)
from @tblMemberDates tmd
where tmd.barYear is not null

while @minBarID is not null
begin
	select @currMemberID = null, @currBarYear = null, @currBarDate = null, @oldBarDate = null

	select @currMemberID = memberID, @currBarYear = barYear, @oldBarDate = barDate,
		@currBarDate = '06/01/' + cast(@currBarYear as varchar)
	from @tblMemberDates
	where id = @minBarID

	BEGIN TRY
		IF @oldBarDate <> cast('06/01/' + cast(@currBarYear as varchar) as datetime)
		begin
			exec membercentral.dbo.ams_deleteMemberData @currMemberID, @BarDateColID
			exec membercentral.dbo.ams_saveMemberData @currMemberID, @BarDateColID, NULL, @currBarDate
		end
	END TRY
	BEGIN CATCH
		-- don't do anything with it
		set @good = 0
	END CATCH

	select @minBarID = min(tmd.id)
	from @tblMemberDates tmd
	where tmd.barYear is not null
	and id > @minBarID
end

select @minBirthID = min(tmd.id)
from @tblMemberDates tmd
where tmd.birthYear is not null

while @minBirthID is not null
begin
	select @currMemberID = null, @currBirthYear = null, @currBirthDate = null, @oldBirthDate = null

	select @currMemberID = memberID, @currBirthYear = birthYear, @oldBirthDate = birthDate,
		@currBirthDate = '01/01/' + cast(@currBirthYear as varchar)
	from @tblMemberDates
	where id = @minBirthID

	BEGIN TRY
		IF @oldBirthDate <> cast('01/01/' + cast(@currBirthYear as varchar) as datetime)
		begin
			exec membercentral.dbo.ams_deleteMemberData @currMemberID, @BirthDateColID
			exec membercentral.dbo.ams_saveMemberData @currMemberID, @BirthDateColID, NULL, @currBirthDate
		end
	END TRY
	BEGIN CATCH
		-- don't do anything with it
		set @good = 0
	END CATCH

	select @minBirthID = min(tmd.id)
	from @tblMemberDates tmd
	where tmd.birthYear is not null
	and id > @minBirthID
end

declare @statusTypeID int

select @statusTypeID = statusTypeID
from membercentral.dbo.scheduledTaskStatusTypes
where statusName = 'Success'

if (@statusTypeID is not null)
	update membercentral.dbo.scheduledTaskHistory set
		statusTypeID = @statusTypeID,
		dateLastUpdated = getdate(),
		dateEnded = getdate()
	where historyID  = @historyID
	and taskID = @taskID

	SET NOCOUNT OFF;

END
GO

USE [customApps]
GO
ALTER PROCEDURE [dbo].[va_updateBarAndBirthDates] 
AS

SET NOCOUNT ON

declare @tblMembersToUpdateBar TABLE (memberID int, barYear int, barDate datetime)
declare @tblMembersToUpdateBirth TABLE (memberID int, BirthYear int, BirthDate datetime)
declare @orgID int
declare @BarYearColID int, @BirthYearColID int, @BarDateColID int, @BirthDateColID int
declare @memberID int, @BarYear int, @barDate datetime, @BirthYear int, @birthDate datetime

select @orgID=orgID from membercentral.dbo.organizations where orgCode = 'VA'

select @BarYearColID = columnID
	FROM membercentral.dbo.ams_memberDataColumns
	where orgID = @orgID
	and columnName = 'Bar_Year'

select @BirthYearColID = columnID
	FROM membercentral.dbo.ams_memberDataColumns
	where orgID = @orgID
	and columnName = 'Birth_Year'

select @BarDateColID = columnID
	FROM membercentral.dbo.ams_memberDataColumns
	where orgID = @orgID
	and columnName = 'Bar Date'

select @BirthDateColID = columnID
	FROM membercentral.dbo.ams_memberDataColumns
	where orgID = @orgID
	and columnName = 'Birth Date'

insert into @tblMembersToUpdateBar (memberID, BarYear, BarDate)
select m.memberID, mdcvBarYear.columnValueInteger as BarYear, mdcvBarDate.columnValueDate as BarDate
from membercentral.dbo.ams_members as m
left outer join membercentral.dbo.ams_memberData as mdBarYear
	inner join membercentral.dbo.ams_memberDataColumnValues as mdcvBarYear on mdcvBarYear.valueID = mdBarYear.valueID
		and mdcvBarYear.columnID = @BarYearColID
	on mdBarYear.memberID = m.memberID
left outer join membercentral.dbo.ams_memberData as mdBarDate
	inner join membercentral.dbo.ams_memberDataColumnValues as mdcvBarDate on mdcvBarDate.valueID = mdBarDate.valueID
		and mdcvBarDate.columnID = @BarDateColID
	on mdBarDate.memberID = m.memberID
where m.orgID = @orgID
and m.memberID = m.activeMemberID
and m.status <> 'D'
and isnull(DatePart(year,mdcvBarDate.columnValueDate),0) <> isnull(mdcvBarYear.columnValueInteger,0)

insert into @tblMembersToUpdateBirth (memberID, BirthYear, BirthDate)
select m.memberID, mdcvBirthYear.columnValueInteger as BirthYear, mdcvBirthDate.columnValueDate as BirthDate
from membercentral.dbo.ams_members as m
left outer join membercentral.dbo.ams_memberData as mdBirthYear
	inner join membercentral.dbo.ams_memberDataColumnValues as mdcvBirthYear on mdcvBirthYear.valueID = mdBirthYear.valueID
		and mdcvBirthYear.columnID = @BirthYearColID
	on mdBirthYear.memberID = m.memberID
left outer join membercentral.dbo.ams_memberData as mdBirthDate
	inner join membercentral.dbo.ams_memberDataColumnValues as mdcvBirthDate on mdcvBirthDate.valueID = mdBirthDate.valueID
		and mdcvBirthDate.columnID = @BirthDateColID
	on mdBirthDate.memberID = m.memberID
where m.orgID = @orgID
and m.memberID = m.activeMemberID
and m.status <> 'D'
and isnull(DatePart(year,mdcvBirthDate.columnValueDate),0) <> isnull(mdcvBirthYear.columnValueInteger,0)

-- loop over members to update BAR
select @memberID = min(memberid) from @tblMembersToUpdateBar
while @memberID is not null BEGIN
	select @BarYear	= barYear, @barDate = barDate from @tblMembersToUpdateBar where memberID = @memberID

	if @baryear is null and @barDate is not null begin
		exec membercentral.dbo.ams_deleteMemberData @memberID, @BarDateColID
	end else begin
		set @barDate = cast('6/1/' + cast(@BarYear as char(4)) as datetime)
		exec membercentral.dbo.ams_setMemberData @memberID, @orgID, 'Bar Date', @barDate
	end

	select @memberID = min(memberid) from @tblMembersToUpdateBar where memberid > @memberID
END

-- loop over members to update BIRTH
select @memberID = min(memberid) from @tblMembersToUpdateBirth
while @memberID is not null BEGIN
	select @BirthYear = birthYear, @birthDate = birthDate from @tblMembersToUpdateBirth where memberID = @memberID

	if @BirthYear is null and @birthDate is not null begin
		exec membercentral.dbo.ams_deleteMemberData @memberID, @BirthDateColID
	end else begin
		set @birthDate = cast('1/1/' + cast(@BirthYear as char(4)) as datetime)
		exec membercentral.dbo.ams_setMemberData @memberID, @orgID, 'Birth Date', @birthDate
	end

	select @memberID = min(memberid) from @tblMembersToUpdateBirth where memberid > @memberID
END

SET NOCOUNT OFF

RETURN 0
GO
