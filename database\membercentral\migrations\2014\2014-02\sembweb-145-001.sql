create procedure dbo.swl_getFormBuilderEnrollments
	@seminarID int
as

;with tmp as (
	select 
		saf.seminarFormID, f.formtypeid, r.responseID, 
		r.depomemberdataid, r.dateDelivered, r.dateCompleted, 
		r.passingPct, r.isActive, rd.questionID, rd.isCorrect
	from 
		seminarweb.dbo.tblSeminarsAndFormResponses as safr
		inner join seminarweb.dbo.tblSeminarsAndForms as saf on saf.seminarFormID = safr.seminarFormID
			and saf.seminarID = @seminarID
		inner join formbuilder.dbo.tblForms as f on f.formid = saf.formid
		inner join formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID
		inner join formbuilder.dbo.tblResponseDetails as rd on rd.responseid = r.responseid
), tmpCorr AS (
	select responseID, count(questionID) as correctCount
	from tmp
	where isCorrect = 1
	group by responseID
)

select 
	top 100 percent 
	tmp.thisCount , e.enrollmentID, e.userid, 
	eswl.goToMeetingUID, ps.pin, eswl.swlcode, p.orgcode, p.catalogURL, 
	d.depomemberdataID, d.FirstName, d.LastName, d.Fax, 
	d.Email, e.dateEnrolled, eswl.attended, eswl.attendedPhone, 
	eswl.joinTime, eswl.exitTime, eswl.duration, 
	eswl.durationPhone, eswl.completedPolling, e.dateCompleted, e.passed, 
	(select count(*) from dbo.tblLogSWLive as log1 where enrollmentID = e.enrollmentID and seminarID = e.seminarID and contact like '%@%') as EmailCount,
	(select count(*) from dbo.tblLogSWLive as log2 where enrollmentID = e.enrollmentID and seminarID = e.seminarID and contact not like '%@%') as FaxCount,
	(select count(*) from dbo.tblEnrollmentsAndCredit where enrollmentID = e.enrollmentID) as CreditCount,
	pgtm.parkedID, sswl.phoneAttendee, sswl.codeAttendee,
	case 
		when (	select count(saf.formID)
				from dbo.tblEnrollments AS e2
					inner join dbo.tblSeminarsAndForms as saf on e2.seminarID = saf.seminarID
					left outer join dbo.tblSeminarsAndFormResponses as safr 
					inner join formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
					inner join formbuilder.dbo.tblForms as f on f.formID = r.formID
						and f.isPublished = 1
						and getdate() between f.dateStartPublish and f.dateEndPublish	
					on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e2.enrollmentID
				where e2.enrollmentID = e.enrollmentID
					and saf.loadPoint = 'evaluation'
					and exists(
						select sac.seminarCreditID, eac.idNumber, eac.earnedCertificate, caswl.evaluationRequired
						from dbo.tblEnrollmentsAndCredit as eac 
							inner join dbo.tblSeminarsAndCredit as sac on eac.seminarCreditID = sac.seminarCreditID 
							inner join dbo.tblCreditSponsorsAndAuthorities as csa on sac.CSALinkID = csa.CSALinkID 
							inner join dbo.tblCreditAuthorities AS ca on csa.authorityID = ca.authorityID 
							inner join dbo.tblCreditSponsors as cs on csa.sponsorID = cs.sponsorID 
							inner join dbo.tblCreditStatuses as cstat on sac.statusID = cstat.statusID
							inner join dbo.tblCreditAuthoritiesSWLive as caswl on caswl.authorityID = ca.authorityID
						where eac.enrollmentID = e.enrollmentID
							and evaluationRequired = 1)
					and (safr.responseID is null or r.dateCompleted is null)) > 0 then 0
		else 1
	end as allEvaluationCompleted
from dbo.tblEnrollments AS e 
		inner join dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
		inner join dbo.tblParticipants AS p ON e.participantID = p.participantID 
		inner join dbo.tblUsers AS u ON e.userID = u.userID 
		inner join trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
		inner join dbo.tblSeminarsSWLive AS sswl ON e.seminarID = sswl.seminarID
		left outer join dbo.tblParkedGTMSeats AS pgtm ON e.enrollmentID = pgtm.enrollmentID
		left outer join dbo.tblParkedPhoneSeats AS ps ON e.enrollmentID = ps.enrollmentID
		left outer join (
			select count (seminarFormID) thisCount, depomemberdataid
			from tmp
				left outer join tmpCorr on tmpCorr.responseID = tmp.responseID
			group by depomemberdataid
		) tmp on tmp.depomemberdataID = d.depomemberdataID
where 
	e.seminarID = @seminarID
	AND e.isActive = 1
	and (tmp.thisCount = 0 or tmp.thisCount is null)
	and eswl.attended = 1
order by 
	e.dateEnrolled

return
GO