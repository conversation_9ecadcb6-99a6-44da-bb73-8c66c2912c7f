-- NY-24 Insert weeklySWODSalesReport scheduled job
use MemberCentral
GO

declare @taskID int
IF NOT EXISTS (select taskID from membercentral.dbo.scheduledTasks where taskCFC = 'model.scheduledTasks.tasks.weeklySWODSalesReport') BEGIN
	insert into membercentral.dbo.scheduledTasks (name, nextRunDate, interval, intervalTypeID, taskCFC, timeoutMinutes, disabled, siteid)
	values ('Weekly SWOD Sales Report', '2/21/2015 11:45 PM', 7, 1, 'model.scheduledTasks.tasks.weeklySWODSalesReport', 10, 1, 1) 
		select @taskID = SCOPE_IDENTITY()

	insert into platformstats.dbo.scheduledTaskHistory (taskID, statusTypeID, dateStarted, dateEnded, serverid, dateLastUpdated)
	values (@taskID, 2, '1/1/2000', '1/1/2000', 1, '1/1/2000')
END
GO

