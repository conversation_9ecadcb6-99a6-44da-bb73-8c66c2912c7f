ALTER PROC dbo.lists_syncListDigestSettings

@orgcode varchar(10) = null,
@listName VARCHAR(200) = null

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
    
	IF OBJECT_ID('tempdb..#tmpListsToProcess') IS NOT NULL 
		DROP TABLE #tmpListsToProcess;

	CREATE TABLE #tmpListsToProcess (autoid int IDENTITY(1,1), listname varchar(100), siteID int, orgID int, orgcode varchar(20), supportsMCThreadDigest bit, supportsMCThreadIndex bit, isLaunchedMCThreadDigest bit, isLaunchedMCThreadIndex bit);
	DECLARE @siteID int, @orgID int;

	IF @orgcode is null AND @listName is null BEGIN
        print 'Run system wide';

		insert #tmpListsToProcess(listname, siteID, orgID, orgcode, supportsMCThreadDigest, supportsMCThreadIndex, isLaunchedMCThreadDigest, isLaunchedMCThreadIndex)
		select distinct listName, s.siteID, s.orgID, lf.orgcode, l.supportsMCThreadDigest, l.supportsMCThreadIndex, l.isLaunchedMCThreadDigest, l.isLaunchedMCThreadIndex
		from membercentral.membercentral.dbo.lists_lists l
		inner join trialslyris1.dbo.lists_format lf 
			on lf.name = l.listName collate Latin1_General_CI_AI
		inner join membercentral.membercentral.dbo.cms_siteResources sr 
			on l.siteResourceID = sr.siteResourceID
			and sr.siteResourceStatusID = 1
		inner join membercentral.membercentral.dbo.sites s 
			on s.siteID = sr.siteID 
			and s.sitecode = lf.orgcode collate Latin1_General_CI_AI
		
    END
    ELSE IF @orgcode is NOT NULL AND @listName is null BEGIN
        print 'Run org wide for ' +@orgcode;
		select @siteID = siteID from membercentral.membercentral.dbo.sites where sitecode = @orgcode;

		insert #tmpListsToProcess(listname, siteID, orgID,orgcode, supportsMCThreadDigest, supportsMCThreadIndex, isLaunchedMCThreadDigest, isLaunchedMCThreadIndex)
		select distinct listName, s.siteID, s.orgID, @orgcode, l.supportsMCThreadDigest, l.supportsMCThreadIndex, l.isLaunchedMCThreadDigest, l.isLaunchedMCThreadIndex
		from trialslyris1.dbo.lists_format lf 
		inner join membercentral.membercentral.dbo.lists_lists l on l.listName = lf.name collate Latin1_General_CI_AI
			and lf.orgcode = @orgcode
		inner join membercentral.membercentral.dbo.cms_siteResources sr on l.siteResourceID = sr.siteResourceID
			and sr.siteID = @siteID
			and sr.siteResourceStatusID = 1
		inner join membercentral.memberCentral.dbo.sites s on s.siteID = @siteID 
			and s.sitecode = lf.orgcode collate Latin1_General_CI_AI
			and lf.orgcode = @orgcode;
    END
    ELSE IF @orgcode is NOT NULL AND @listName is NOT NULL  BEGIN
        print 'Run org/list for ' + @orgcode + ' on list ' + @listName;
		select @siteID = siteID from membercentral.membercentral.dbo.sites where sitecode = @orgcode;

		insert #tmpListsToProcess(listname, siteID, orgID, orgcode, supportsMCThreadDigest, supportsMCThreadIndex, isLaunchedMCThreadDigest, isLaunchedMCThreadIndex)
		select distinct listName, s.siteID, s.orgID, @orgcode, l.supportsMCThreadDigest, l.supportsMCThreadIndex, l.isLaunchedMCThreadDigest, l.isLaunchedMCThreadIndex
		from membercentral.membercentral.dbo.lists_lists l
		inner join trialslyris1.dbo.lists_format lf on lf.name = l.listName collate Latin1_General_CI_AI
			and l.listName = @listName
		inner join membercentral.membercentral.dbo.cms_siteResources sr 
			on l.siteResourceID = sr.siteResourceID
			and sr.siteResourceStatusID = 1
			and sr.siteID = @siteID
		inner join membercentral.memberCentral.dbo.sites s on s.siteID = @siteID 
			and s.sitecode = lf.orgcode collate Latin1_General_CI_AI
			and lf.orgcode = @orgcode;

    END
    ELSE IF @orgcode is NULL AND @listName is NOT NULL  BEGIN
        print 'Orgcode is required if listname is passed in.';
		IF OBJECT_ID('tempdb..#tmpListsToProcess') IS NOT NULL 
			DROP TABLE #tmpListsToProcess;
        return 0;
    END

	--------------------------------------------------------------------------------------------------------------------------------
	-- Moving entire list from MC Digest back to Lyris Index or Digest if neither MCThreadIndex or Digest are supported
	--------------------------------------------------------------------------------------------------------------------------------

	update lm set lm.SubType_ = 'digest', lm.receiveMCThreadDigest = 0, lm.receiveMCThreadIndex = 0
	from #tmpListsToProcess l
	inner join trialslyris1.dbo.members_ lm 
		on lm.List_ = l.listname collate Latin1_General_CI_AI
		and lm.receiveMCThreadDigest =1
	where (l.supportsMCThreadDigest=0 and l.supportsMCThreadIndex=0)

	update lm set lm.SubType_ = 'index', lm.receiveMCThreadDigest = 0, lm.receiveMCThreadIndex = 0
	from #tmpListsToProcess l
	inner join trialslyris1.dbo.members_ lm 
		on lm.List_ = l.listname collate Latin1_General_CI_AI
		and lm.receiveMCThreadIndex =1
	where (l.supportsMCThreadDigest=0 and l.supportsMCThreadIndex=0)


	--------------------------------------------------------------------------------------------------------------------------------
	-- Moving members from Lyris Digests to MC Digests (Lists that only launched MC Digest)
	--------------------------------------------------------------------------------------------------------------------------------

	update lm set lm.subtype_ = 'nomail', lm.receiveMCThreadDigest =1
	from #tmpListsToProcess l
	inner join  trialslyris1.dbo.members_ lm
		on lm.List_ = l.listname collate Latin1_General_CI_AI
		and l.isLaunchedMCThreadDigest = 1
		and l.isLaunchedMCThreadIndex = 0
	inner join membercentral.membercentral.dbo.ams_members am on am.memberNumber = lm.ExternalMemberID collate Latin1_General_CI_AI
		and am.memberID = am.activeMemberID
		and am.orgID = l.orgID
		and am.status = 'A'
	where lm.membertype_ in ('normal', 'held')
	and lm.subtype_ in ('digest','mimedigest','index')


	--------------------------------------------------------------------------------------------------------------------------------
	-- Moving members from Lyris Digests to MC Digests (Lists that only launched MC Index)
	--------------------------------------------------------------------------------------------------------------------------------

	update lm set lm.subtype_ = 'nomail', lm.receiveMCThreadIndex =1
	from #tmpListsToProcess l
	inner join  trialslyris1.dbo.members_ lm
		on lm.List_ = l.listname collate Latin1_General_CI_AI
		and l.isLaunchedMCThreadDigest = 0
		and l.isLaunchedMCThreadIndex = 1
	inner join membercentral.membercentral.dbo.ams_members am on am.memberNumber = lm.ExternalMemberID collate Latin1_General_CI_AI
		and am.memberID = am.activeMemberID
		and am.orgID = l.orgID
		and am.status = 'A'
	where lm.membertype_ in ('normal', 'held')
	and lm.subtype_ in ('digest','mimedigest','index')


	--------------------------------------------------------------------------------------------------------------------------------
	-- Moving members from Lyris Digests to MC Digests (Lists that launched both flavors)
	--------------------------------------------------------------------------------------------------------------------------------

	update lm set lm.subtype_ = 'nomail', lm.receiveMCThreadIndex = 0, lm.receiveMCThreadDigest = 1
	from #tmpListsToProcess l
	inner join  trialslyris1.dbo.members_ lm
		on lm.List_ = l.listname collate Latin1_General_CI_AI
		and l.isLaunchedMCThreadDigest = 1
		and l.isLaunchedMCThreadIndex = 1
	inner join membercentral.membercentral.dbo.ams_members am on am.memberNumber = lm.ExternalMemberID collate Latin1_General_CI_AI
		and am.memberID = am.activeMemberID
		and am.orgID = l.orgID
		and am.status = 'A'
	where lm.membertype_ in ('normal', 'held')
	and lm.subtype_ in ('digest','mimedigest')


	update lm set lm.subtype_ = 'nomail', lm.receiveMCThreadIndex = 1, lm.receiveMCThreadDigest = 0
	from #tmpListsToProcess l
	inner join  trialslyris1.dbo.members_ lm
		on lm.List_ = l.listname collate Latin1_General_CI_AI
		and l.isLaunchedMCThreadDigest = 1
		and l.isLaunchedMCThreadIndex = 1
	inner join membercentral.membercentral.dbo.ams_members am on am.memberNumber = lm.ExternalMemberID collate Latin1_General_CI_AI
		and am.memberID = am.activeMemberID
		and am.orgID = l.orgID
		and am.status = 'A'
	where lm.membertype_ in ('normal', 'held')
	and lm.subtype_ in ('index')


	--------------------------------------------------------------------------------------------------------------------------------
	-- Moving members between MC Digest and Index
	--------------------------------------------------------------------------------------------------------------------------------

	-- Digest active and index disabled
	update lm set lm.receiveMCThreadIndex = 0, lm.receiveMCThreadDigest = 1
	from  #tmpListsToProcess l
	inner join trialslyris1.dbo.members_ lm 
		on lm.List_ = l.listname collate Latin1_General_CI_AI
		and lm.receiveMCThreadIndex=1
	where (l.isLaunchedMCThreadDigest=1 and l.supportsMCThreadIndex=0)
	
	-- Digest disabled and index enabled
	update lm set lm.receiveMCThreadIndex = 1, lm.receiveMCThreadDigest = 0
	from #tmpListsToProcess l
	inner join trialslyris1.dbo.members_ lm 
		on lm.List_ = l.listname collate Latin1_General_CI_AI
		and lm.receiveMCThreadDigest =1
	where (l.supportsMCThreadDigest=0 and l.isLaunchedMCThreadIndex=1)

	
	-- Moving individual members_ from MC Digest back to Lyris Digest
	update lm set lm.SubType_ = 'digest', lm.receiveMCThreadDigest = 0, lm.receiveMCThreadIndex = 0
	from #tmpListsToProcess l
	inner join trialslyris1.dbo.members_ lm 
		on lm.List_ = l.listname collate Latin1_General_CI_AI
		and lm.receiveMCThreadDigest = 1
		and l.isLaunchedMCThreadDigest=1
	left outer join membercentral.membercentral.dbo.ams_members am on am.memberNumber = lm.ExternalMemberID collate Latin1_General_CI_AI
		and am.memberID = am.activeMemberID
		and am.orgID = l.orgID
		and am.status = 'A'
	where am.memberNumber is NULL;

	update lm set lm.SubType_ = 'index', lm.receiveMCThreadDigest = 0, lm.receiveMCThreadIndex = 0
	from #tmpListsToProcess l
	inner join trialslyris1.dbo.members_ lm 
		on lm.List_ = l.listname collate Latin1_General_CI_AI
		and lm.receiveMCThreadIndex = 1
		and l.isLaunchedMCThreadIndex=1
	left outer join membercentral.membercentral.dbo.ams_members am on am.memberNumber = lm.ExternalMemberID collate Latin1_General_CI_AI
		and am.memberID = am.activeMemberID
		and am.orgID = l.orgID
		and am.status = 'A'
	where am.memberNumber is NULL;

	IF OBJECT_ID('tempdb..#tmpListsToProcess') IS NOT NULL 
		DROP TABLE #tmpListsToProcess;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO
