USE [memberCentral]
GO
CREATE TABLE [dbo].[ams_virtualGroupConditionKeys](
	[conditionKeyID] [int] IDENTITY(1,1) NOT NULL,
	[conditionKey] [varchar](30) NOT NULL,
 CONSTRAINT [PK_ams_virtualGroupConditionKeys] PRIMARY KEY CLUSTERED 
(
	[conditionKeyID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE dbo.ams_virtualGroupConditionKeys ADD CONSTRAINT
	IX_ams_virtualGroupConditionKeys UNIQUE NONCLUSTERED 
	(
	conditionKey
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

GO
CREATE TABLE [dbo].[ams_virtualGroupConditionValues](
	[cvid] [int] IDENTITY(1,1) NOT NULL,
	[conditionID] [int] NOT NULL,
	[conditionKeyID] [int] NOT NULL,
	[conditionValue] [varchar](100) NOT NULL,
 CONSTRAINT [PK_ams_virtualGroupConditionValues] PRIMARY KEY CLUSTERED 
(
	[cvid] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE dbo.ams_virtualGroupConditionValues ADD CONSTRAINT
	FK_ams_virtualGroupConditionValues_ams_virtualGroupConditions FOREIGN KEY
	(
	conditionID
	) REFERENCES dbo.ams_virtualGroupConditions
	(
	conditionID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.ams_virtualGroupConditionValues ADD CONSTRAINT
	FK_ams_virtualGroupConditionValues_ams_virtualGroupConditionKeys FOREIGN KEY
	(
	conditionKeyID
	) REFERENCES dbo.ams_virtualGroupConditionKeys
	(
	conditionKeyID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.ams_virtualGroupConditionValues ADD CONSTRAINT
	IX_ams_virtualGroupConditionValues UNIQUE NONCLUSTERED 
	(
	conditionID,
	conditionKeyID,
	conditionValue
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

GO

INSERT INTO dbo.ams_virtualGroupConditionKeys (conditionKey)
values ('value')
GO

INSERT INTO dbo.ams_virtualGroupConditionValues (conditionID, conditionKeyID, conditionValue)
select distinct conditionID, 1, [value]
from dbo.ams_virtualGroupConditions
where nullif([value],'') is not null
GO

ALTER FUNCTION [dbo].[ams_getVirtualGroupConditionVerbose] (@conditionID int)
RETURNS varchar(max)
AS
BEGIN

	DECLARE @fieldCode varchar(40), @orgid int, @verbose varchar(max), @hasVerbose bit, 
		@expression varchar(20), @fieldLabel varchar(max), @displayTypeCode varchar(20),
		@dataTypeCode varchar(20)
	set @hasVerbose = 0

	select @fieldCode=c.fieldCode, @orgID=c.orgID, @expression=e.expression, 
		@fieldLabel=fields.fieldLabel, @displayTypeCode=fields.displayTypeCode,
		@dataTypeCode=fields.dataTypeCode
		from dbo.ams_virtualGroupConditions as c
		inner join dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		cross apply dbo.fn_ams_getVirtualGroupConditionVerboseFields(c.orgID,c.fieldCode) as fields
		where c.conditionID = @conditionID

	-- events
	IF @hasVerbose = 0 and left(@fieldCode,2) = 'e_' BEGIN
		select top 1 @verbose = e.expressionVerbose + ' ' + @fieldLabel
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		where c.conditionID = @conditionID
		
		set @hasVerbose = 1
	END

	-- group membership
	IF @hasVerbose = 0 and left(@fieldcode,4) = 'grp_' BEGIN
		set @verbose = 'Member of ' + @fieldLabel
		set @hasVerbose = 1
	END

	-- subscriptions
	IF @hasVerbose = 0 and left(@fieldcode,4) = 'sub_' BEGIN
		set @verbose = 'Has Subscription ' + @fieldLabel
		set @hasVerbose = 1
	END

	-- exists/not exists
	IF @hasVerbose = 0 and @expression in ('exists','not_exists') BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + e.expressionVerbose
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		where c.conditionID = @conditionID

		set @hasVerbose = 1
	END
	
	-- membertypeID (multi-select not allowed)
	IF @hasVerbose = 0 and @fieldCode = 'm_membertypeid' BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + e.expressionVerbose + ' ' + mt.memberType
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
		INNER JOIN dbo.ams_memberTypes as mt on mt.memberTypeID = cv.conditionValue
		where c.conditionID = @conditionID
		and cvk.conditionKey = 'value'

		set @hasVerbose = 1
	END

	-- stateprov (multi-select allowed)
	IF @hasVerbose = 0 and left(@fieldCode,3) = 'ma_' and right(@fieldCode,10) = '_stateprov' BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + e.expressionVerbose + ' ' + replace(replace(dbo.PipeList(replace(s.Name,'|',char(7))),'|',' OR '),char(7),'|')
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
		INNER JOIN dbo.ams_states as s on cast(s.stateID as varchar(10)) = cv.conditionValue
		where c.conditionID = @conditionID
		and cvk.conditionKey = 'value'
		group by e.expressionVerbose

		set @hasVerbose = 1
	END

	-- country (multi-select allowed)
	IF @hasVerbose = 0 and left(@fieldCode,3) = 'ma_' and right(@fieldCode,8) = '_country' BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + e.expressionVerbose + ' ' + replace(replace(dbo.PipeList(replace(country.country,'|',char(7))),'|',' OR '),char(7),'|')
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
		INNER JOIN dbo.ams_countries as country on cast(country.countryID as varchar(10)) = cv.conditionValue
		where c.conditionID = @conditionID
		and cvk.conditionKey = 'value'
		group by e.expressionVerbose

		set @hasVerbose = 1
	END

	-- districting data (multi-select allowed)
	IF @hasVerbose = 0 and left(@fieldCode,4) = 'mad_' BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + e.expressionVerbose + ' ' + replace(replace(dbo.PipeList(replace(dv.vendorvalue,'|',char(7))),'|',' OR '),char(7),'|')
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
		INNER JOIN dbo.ams_memberDistrictValues as dv on cast(dv.valueid as varchar(10)) = cv.ConditionValue
		where c.conditionID = @conditionID
		and cvk.conditionKey = 'value'
		group by e.expressionVerbose

		set @hasVerbose = 1
	END

	-- prof license status (multi-select allowed)
	IF @hasVerbose = 0 and left(@fieldCode,4) = 'mpl_' and right(@fieldCode,7) = '_status' BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + e.expressionVerbose + ' ' + replace(replace(dbo.PipeList(replace(cv.conditionValue,'|',char(7))),'|',' OR '),char(7),'|')
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
		where c.conditionID = @conditionID
		and cvk.conditionKey = 'value'
		group by e.expressionVerbose

		set @hasVerbose = 1
	END

	-- datediff (multi-select not allowed)
	IF @hasVerbose = 0 and @expression = 'datediff' BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + de.expressionVerbose + ' ' + cast(abs(cv.conditionValue) as varchar(10)) + ' ' + 
			case c.[datepart]
			when 'yy' then 'year'
			when 'q' then 'quarter'
			when 'm' then 'month'
			when 'w' then 'week'
			when 'dw' then 'weekday'
			when 'd' then 'day'
			when 'dy' then 'day of year'
			else 'unknown'
			end + 
			case when cv.conditionValue not in (1,-1) then 's' else '' end + 
			case when cv.conditionValue > 0 then ' in the past' else ' in the future' end
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS de ON c.dateexpressionID = de.expressionID
		INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
		where c.conditionID = @conditionID
		and cvk.conditionKey = 'value'

		set @hasVerbose = 1
	END

	-- datepart (multi-select allowed)
	IF @hasVerbose = 0 and @expression = 'datepart' BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + 
			case c.[datepart]
			when 'yy' then 'year'
			when 'q' then 'quarter'
			when 'm' then 'month'
			when 'w' then 'week'
			when 'dw' then 'weekday'
			when 'd' then 'day'
			when 'dy' then 'day of year'
			else 'unknown'
			end + ' ' + de.expressionVerbose + ' ' + replace(replace(dbo.PipeList(replace(cv.conditionValue,'|',char(7))),'|',' OR '),char(7),'|')
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS de ON c.dateexpressionID = de.expressionID
		INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
		where c.conditionID = @conditionID
		and cvk.conditionKey = 'value'
		group by c.[datepart], de.expressionVerbose

		set @hasVerbose = 1
	END

	-- memberdata SELECT/RADIO - BIT (multi-select not allowed)
	IF @hasVerbose = 0 and left(@fieldCode,3) = 'md_' and @displayTypeCode in ('SELECT','RADIO') and @dataTypeCode = 'BIT' BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + e.expressionVerbose + ' ' + case when cv.ConditionValue = 1 then 'YES' else 'NO' end
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
		where c.conditionID = @conditionID
		and cvk.conditionKey = 'value'

		set @hasVerbose = 1
	END

	-- memberdata SELECT/RADIO (multi-select allowed)
	IF @hasVerbose = 0 and left(@fieldCode,3) = 'md_' and @displayTypeCode in ('SELECT','RADIO') BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + tmp.expressionVerbose + ' ' + replace(replace(dbo.PipeList(replace(tmp.valueVerbose,'|',char(7))),'|',' OR '),char(7),'|')
		from (
			select e.expressionVerbose, 
				valueVerbose = case 
					when @dataTypeCode = 'STRING' then mdcv.columnValueString
					when @dataTypeCode = 'DECIMAL2' then cast(mdcv.columnValueDecimal2 as varchar(max))
					when @dataTypeCode = 'INTEGER' then cast(mdcv.columnValueInteger as varchar(max))
					when @dataTypeCode = 'DATE' then convert(varchar(10),mdcv.columnValueDate,101)
					else cv.conditionValue
				end
			from dbo.ams_virtualGroupConditions as c
			INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
			INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
			INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
			INNER JOIN dbo.ams_memberDataColumnValues as mdcv on cast(mdcv.valueid as varchar(10)) = cv.ConditionValue
			where c.conditionID = @conditionID
			and cvk.conditionKey = 'value'
		) as tmp
		group by tmp.expressionVerbose

		set @hasVerbose = 1
	END

	-- All others
	IF @hasVerbose = 0 BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + e.expressionVerbose + ' ' + replace(replace(dbo.PipeList(replace(cv.conditionValue,'|',char(7))),'|',' OR '),char(7),'|')
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
		where c.conditionID = @conditionID
		and cvk.conditionKey = 'value'
		group by e.expressionVerbose

		set @hasVerbose = 1
	END

	RETURN @verbose
END
GO

UPDATE dbo.ams_virtualGroupConditions
SET [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
GO

ALTER PROC [dbo].[ams_deleteVirtualGroupCondition]
@orgID int,
@conditionIDList varchar(max)

AS

DECLARE @tblConditions TABLE (conditionID int)
DECLARE @tblRules TABLE (ruleID int)

-- split conditions
INSERT INTO @tblConditions (conditionID)
select c.conditionID
from dbo.fn_intListToTable(@conditionIDList,',') as tmp
inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tmp.listitem
where c.orgID = @orgID

-- get rules this condition is in
INSERT INTO @tblRules (ruleID)
select r.ruleID
from dbo.ams_virtualGroupRules as r
CROSS APPLY r.ruleXML.nodes('//condition') as Conditions(Condition)
inner join dbo.ams_virtualGroupConditions as c on c.uid = Condition.value('@id','uniqueidentifier')
inner join @tblConditions as tblC on tblC.conditionID = c.conditionID
where ruleXML.exist('//condition[@id=sql:column("c.uid")]') = 1

BEGIN TRAN
	
	-- del from rules
	update r
	set ruleXML.modify('delete //condition[@id=sql:column("c.uid")]')
	from dbo.ams_virtualGroupRules as r
	CROSS APPLY r.ruleXML.nodes('//condition') as Conditions(Condition)
	inner join dbo.ams_virtualGroupConditions as c on c.uid = Condition.value('@id','uniqueidentifier')
	inner join @tblConditions as tblC on tblC.conditionID = c.conditionID
		IF @@ERROR <> 0 GOTO on_error

	-- ensure there are no empty condition sets now. if so, inactivate those rules
	update r
	set r.isActive = 0
	from dbo.ams_virtualGroupRules as r
	inner join @tblRules as tr on tr.ruleID = r.ruleID
	where ruleXML.exist('//conditionset[not(node())]') = 1
		IF @@ERROR <> 0 GOTO on_error

	-- if rules are tied to groups, need to rerun populate on those orgs
	-- queue processing of member groups
	insert into dbo.queue_processMemberGroups (orgID, ruleID)
	select distinct @orgID, r.ruleID
	from @tblRules as r
	inner join dbo.ams_virtualGroupRuleGroups as rg on rg.ruleID = r.ruleID

	-- regenerate SQL for all updated rules
	declare @minRuleID int, @ruleSQL varchar(max)
	select @minRuleID = min(ruleID) from @tblRules
	while @minRuleID is not null BEGIN
		EXEC dbo.ams_updateVirtualGroupRuleSQL @ruleID=@minRuleID
			IF @@ERROR <> 0 GOTO on_error
			
		select @minRuleID = min(ruleID) from @tblRules where ruleID > @minRuleID
	END

	-- del from queue
	delete from cache_members_conditions
	where conditionID in (select conditionID from @tblConditions)
		IF @@ERROR <> 0 GOTO on_error

	-- del from queue
	delete from dbo.queue_processMemberGroups
	where conditionID in (select conditionID from @tblConditions)
		IF @@ERROR <> 0 GOTO on_error

	-- del from conditionValues
	DELETE FROM dbo.ams_virtualGroupConditionValues
	where conditionID in (select conditionID from @tblConditions)
		IF @@ERROR <> 0 GOTO on_error

	-- del from conditions
	DELETE FROM dbo.ams_virtualGroupConditions
	where conditionID in (select conditionID from @tblConditions)
		IF @@ERROR <> 0 GOTO on_error

IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO



/* ******************************************** 
THIS SHOULD RESULT IN NO BAD CONDITIONS
*********************************************** */
select o.orgcode, c.*
from ams_virtualGroupConditions as c
inner join organizations as o on o.orgID = c.orgID
where verbose is null
GO






ALTER PROC [dbo].[ams_createVirtualGroupCondition]
@orgID int,
@conditionTypeID int,
@fieldCode varchar(40), 
@expression varchar(20),
@datepart varchar(8) = null,
@dateExpression varchar(20) = null,
@value varchar(max) = null, -- pipe delimited value list
@isDefined bit = 1,
@conditionID int OUTPUT

AS

DECLARE @expressionID int, @dateExpressionID int, @fieldCodeDataTypeID int, @fieldCodeDisplayTypeID int
SELECT @conditionID = null

-- get expressionid from expression
select @expressionID = expressionID
	from dbo.ams_virtualGroupExpressions
	where expression = @expression
	IF @@ERROR <> 0 or @expressionID is null GOTO on_error
select @dateExpressionID = expressionID
	from dbo.ams_virtualGroupExpressions
	where expression = @dateExpression
	IF @@ERROR <> 0 GOTO on_error

-- see if exact condition already exists. @value must be passed in as a clean pipelist if multi-select
SELECT @conditionID = c.conditionID
	FROM dbo.ams_virtualGroupConditions as c
	LEFT OUTER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionid = c.conditionid
	WHERE c.orgID = @orgID
	AND c.conditionTypeID = @conditionTypeID
	AND c.fieldCode = @fieldCode
	AND c.expressionID = @expressionID
	AND isNull(c.datePart,'') = isnull(@datePart,'')
	AND isNull(c.dateexpressionid,0) = isnull(@dateExpressionID,0)
	GROUP BY c.conditionID
	HAVING isNull(dbo.pipeList(replace(cv.conditionValue,'|',char(7))),'') = isnull(@value,'')
IF @conditionID IS NULL BEGIN

	-- get data/display types from fieldCode
	select @fieldCodeDataTypeID=dat.dataTypeID, @fieldCodeDisplayTypeID=dit.displayTypeID
		from dbo.fn_ams_getVirtualGroupConditionVerboseFields(@orgID,@fieldCode) as fields
		inner join dbo.ams_memberDataColumnDataTypes as dat on dat.dataTypeCode = fields.dataTypeCode
		inner join dbo.ams_memberDataColumnDisplayTypes as dit on dit.displayTypeCode = fields.displayTypeCode
		IF @@ERROR <> 0 or @fieldCodeDataTypeID is null or @fieldCodeDisplayTypeID is null GOTO on_error

	insert into dbo.ams_virtualGroupConditions (orgID, dataTypeID, displayTypeID, expressionID, 
		[datePart], dateExpressionID, [verbose], fieldCode, [uid], conditionTypeID, isDefined)
	values (@orgID, @fieldCodeDataTypeID, @fieldCodeDisplayTypeID, @expressionID, @datePart, 
		@dateExpressionID, null, @fieldCode, newID(), @conditionTypeID, @isDefined)
		IF @@ERROR <> 0 GOTO on_error
		SELECT @conditionID = SCOPE_IDENTITY()

	insert into dbo.ams_virtualGroupConditionValues (conditionID, conditionKeyID, conditionValue)
	select @conditionID, 1, replace(tmp.listItem,char(7),'|')
	from dbo.fn_varcharListToTable(@value,'|') as tmp
		IF @@ERROR <> 0 GOTO on_error

	UPDATE dbo.ams_virtualGroupConditions
	SET [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
	where conditionID = @conditionID
		IF @@ERROR <> 0 GOTO on_error

	-- queue processing of member groups
	if @isDefined = 1 BEGIN
		insert into dbo.queue_processMemberGroups (orgID, conditionID)
		values (@orgID, @conditionID)
			IF @@ERROR <> 0 GOTO on_error
	end

END

RETURN 0

on_error:
	SELECT @conditionID = 0
	RETURN -1
GO

ALTER PROC [dbo].[ams_updateVirtualGroupCondition]
@orgID int,
@conditionID int,
@expression varchar(20),
@datepart varchar(8) = null,
@dateExpression varchar(20) = null,
@value varchar(max) = null -- pipe delimited value list

AS

DECLARE @expressionID int, @dateExpressionID int, @fieldCodeDataTypeID int
DECLARE @checkConditionID int, @checkconditionTypeID int, @checkFieldCode varchar(40)

-- get expressionid from expression
select @expressionID = expressionID
	from dbo.ams_virtualGroupExpressions
	where expression = @expression
	IF @@ERROR <> 0 or @expressionID is null GOTO on_error
select @dateExpressionID = expressionID
	from dbo.ams_virtualGroupExpressions
	where expression = @dateExpression
	IF @@ERROR <> 0 GOTO on_error

select @checkconditionTypeID=conditionTypeID, @checkFieldCode=fieldCode
	FROM dbo.ams_virtualGroupConditions
	WHERE orgID = @orgID
	AND conditionID = @conditionID

-- see if exact condition already exists. @value must be passed in as a clean pipelist if multi-select
SELECT @checkConditionID = c.conditionID
	FROM dbo.ams_virtualGroupConditions as c
	LEFT OUTER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionid = c.conditionid
	WHERE c.orgID = @orgID
	AND c.conditionTypeID = @checkconditionTypeID
	AND c.fieldCode = @checkFieldCode
	AND c.expressionID = @expressionID
	AND isNull(c.datePart,'') = isnull(@datePart,'')
	AND isNull(c.dateexpressionid,0) = isnull(@dateExpressionID,0)
	AND c.conditionID <> @conditionID
	GROUP BY c.conditionID
	HAVING isNull(dbo.pipeList(replace(cv.conditionValue,'|',char(7))),'') = isnull(@value,'')
IF @checkConditionID IS NULL BEGIN
	UPDATE dbo.ams_virtualGroupConditions
	SET	expressionID = @expressionID,
		[datePart] = @datepart,
		dateExpressionID = @dateExpressionID
	WHERE conditionID = @conditionID
		IF @@ERROR <> 0 GOTO on_error

	delete from dbo.ams_virtualGroupConditionValues
	where conditionID = @conditionID
		IF @@ERROR <> 0 GOTO on_error

	insert into dbo.ams_virtualGroupConditionValues (conditionID, conditionKeyID, conditionValue)
	select @conditionID, 1, replace(tmp.listItem,char(7),'|')
	from dbo.fn_varcharListToTable(@value,'|') as tmp
		IF @@ERROR <> 0 GOTO on_error

	UPDATE dbo.ams_virtualGroupConditions
	SET [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
	where conditionID = @conditionID
		IF @@ERROR <> 0 GOTO on_error

	-- queue processing of member groups
	if (select isDefined from dbo.ams_virtualGroupConditions where conditionID = @conditionID) = 1 BEGIN
		insert into dbo.queue_processMemberGroups (orgID, conditionID)
		values (@orgID, @conditionID)
			IF @@ERROR <> 0 GOTO on_error
	end
END ELSE BEGIN
	RETURN -2
END

RETURN 0

on_error:
	RETURN -1
GO

ALTER FUNCTION [dbo].[fn_getVirtualGroupRulesStructureXML] (@orgID int)
RETURNS xml
AS
BEGIN
	DECLARE @xmlStructure xml
	
	SELECT @xmlStructure = (
		select (
			select condition.conditionID, condition.datatypeid, condition.displaytypeid, condition.expressionid, isnull(condition.[datepart],'') as [datepart], isnull(condition.dateexpressionid,0) as dateexpressionid, condition.[verbose], condition.fieldCode, condition.[uid], condvalue.cvid, condvalue.conditionkeyid, condvalue.conditionvalue
			from dbo.ams_virtualGroupConditions as condition
			left outer join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
			where condition.orgID = @orgID
			and condition.conditionTypeID = 1
			order by condition.conditionID, condvalue.conditionKeyID, condvalue.conditionValue
			FOR XML AUTO, root('conditions'), TYPE
			),
			(
			select vgrule.rulename, cast(vgrule.rulexml as varchar(max)) as rulexml, vgrule.ruleSQL, vgrule.isActive, vgrule.uid, [group].groupid
			from dbo.ams_virtualGroupRules as vgrule
			left outer join dbo.ams_virtualGroupRuleGroups as [group] on [group].ruleID = vgrule.ruleid
			where vgrule.orgID = @orgID
			and vgrule.ruleTypeID = 1
			order by vgrule.rulename
			FOR XML AUTO, root('rules'), TYPE
			),
			(
			select distinct vgroup.groupID, isnull(vgroup.groupcode,'') as groupcode, vgroup.groupname, vgroup.thePathExpanded, isnull(g.uid,'') as [uid]
			from dbo.ams_virtualGroupRuleGroups as rg
			inner join dbo.ams_virtualGroupRules as r on r.ruleID = rg.ruleID
			inner join dbo.ams_groups as g on g.groupID = rg.groupID
			inner join dbo.fn_getRecursiveGroups(@orgID,null,null,0) as vgroup on vgroup.groupID = g.groupID
			where r.orgID = @orgID
			and r.ruleTypeID = 1
			order by vgroup.groupID
			FOR XML AUTO, root('allrulegroups'), TYPE
			),
			(
			select distinct field.fieldCode, isnull(vf.fieldLabel,'') as fieldLabel,
				[uid_t] = case when left(field.fieldCode,4) = 'sub_' then 
						isnull((select cast(t.uid as varchar(36))
						from dbo.fn_varCharListToTable(field.fieldCode,'_') as items
						inner join dbo.sub_Types as t on t.typeID = items.listitem
						where items.autoID = 2),'') else '' end,
				[uid_s] = case when left(field.fieldCode,4) = 'sub_' then 
						isNull((select cast(s.uid as varchar(36))
						from dbo.fn_varCharListToTable(field.fieldCode,'_') as items
						inner join dbo.sub_subscriptions as s on s.subscriptionID = items.listitem
						where items.autoID = 3),'') else '' end,
				[uid_r] = case when left(field.fieldCode,4) = 'sub_' then 
						isNull((select cast(r.uid as varchar(36))
						from dbo.fn_varCharListToTable(field.fieldCode,'_') as items
						inner join dbo.sub_rates as r on r.rateID = items.listitem
						where items.autoID = 4),'') else '' end
			from dbo.ams_virtualGroupConditions as field
			cross apply dbo.fn_ams_getVirtualGroupConditionVerboseFields(@orgID,field.fieldCode) as vf
			where field.orgID = @orgID
			and field.conditionTypeID = 1
			order by field.fieldCode			
			FOR XML AUTO, root('allfields'), TYPE
			),
			(
			select condition.conditionid, 
				valueid = case 
					when left(condition.fieldCode,3) = 'md_' then mdcv.valueid
					when left(condition.fieldcode,4) = 'mad_' then dv.valueid
					when right(condition.fieldcode,10) = '_stateprov' then s.stateid
					when right(condition.fieldcode,8) = '_country' then c.countryid
					else 0 end,
				columnValue = case 
					when left(condition.fieldCode,3) = 'md_' then coalesce(mdcv.columnValueString,cast(mdcv.columnValueDecimal2 as varchar(10)),cast(mdcv.columnValueInteger as varchar(10)),convert(varchar(10),mdcv.columnValueDate,101))
					when left(condition.fieldcode,4) = 'mad_' then dv.vendorValue
					when right(condition.fieldcode,10) = '_stateprov' then s.name
					when right(condition.fieldcode,8) = '_country' then c.country
					else '' end
			from dbo.ams_virtualGroupConditions as condition
			inner join dbo.ams_virtualGroupConditionValues as condvalue on condvalue.conditionID = condition.conditionID
			left outer join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = condvalue.conditionvalue and left(condition.fieldCode,3) = 'md_'
			left outer join dbo.ams_states as s on s.stateid = condvalue.conditionvalue and right(condition.fieldcode,10) = '_stateprov'
			left outer join dbo.ams_countries as c on c.countryid = condvalue.conditionvalue and right(condition.fieldcode,8) = '_country'
			left outer join dbo.ams_memberDistrictValues as dv on dv.valueID = condvalue.conditionvalue and left(condition.fieldCode,4) = 'mad_'
			where condition.orgID = @orgID
			and condition.conditionTypeID = 1
			and (left(condition.fieldCode,3) = 'md_' or left(condition.fieldCode,4) = 'mad_' or right(condition.fieldcode,10) = '_stateprov' or right(condition.fieldcode,8) = '_country')
			and condition.displayTypeId in (2,3)
			and condition.expressionID in (1,2)
			and condition.dataTypeID <> 5
			FOR XML AUTO, root('allvalueids'), TYPE
			),
			(
			select distinct mwt.websitetypeid, mwt.websitetype
			from dbo.ams_memberWebsiteTypes as mwt
			inner join dbo.ams_virtualGroupConditions as field on parsename(replace(field.fieldCode,'_','.'),2) = mwt.websitetypeid
			where mwt.orgID = @orgID
			and left(field.fieldcode,3) = 'mw_'
			and field.conditionTypeID = 1
			FOR XML AUTO, root('websitetypes'), TYPE
			),
			(
			select distinct met.emailtypeid, met.emailtype
			from dbo.ams_memberEmailTypes as met
			inner join dbo.ams_virtualGroupConditions as field on parsename(replace(field.fieldCode,'_','.'),2) = met.emailtypeid
			where met.orgID = @orgID
			and left(field.fieldcode,3) = 'me_'
			and field.conditionTypeID = 1
			FOR XML AUTO, root('emailtypes'), TYPE
			),
			(
			select distinct mat.addresstypeid, mat.addresstype
			from dbo.ams_memberAddressTypes as mat
			inner join dbo.ams_virtualGroupConditions as field on parsename(replace(field.fieldCode,'_','.'),2) = mat.addresstypeid
			where mat.orgID = @orgID
			and left(field.fieldcode,3) in ('ma_','mp_')
			and field.conditionTypeID = 1
			FOR XML AUTO, root('addresstypes'), TYPE
			),
			(
			select distinct mpt.phonetypeid, mpt.phonetype
			from dbo.ams_memberPhoneTypes as mpt
			inner join dbo.ams_virtualGroupConditions as field on parsename(replace(field.fieldCode,'_','.'),1) = mpt.phonetypeid
			where mpt.orgID = @orgID
			and left(field.fieldcode,3) = 'mp_'
			and field.conditionTypeID = 1
			FOR XML AUTO, root('phonetypes'), TYPE
			),
			(
			select distinct mdt.districttypeid, mdt.districttype
			from dbo.ams_memberDistrictTypes as mdt
			inner join dbo.ams_virtualGroupConditions as field on parsename(replace(field.fieldCode,'_','.'),1) = mdt.districttypeid
			where mdt.orgID = @orgID
			and left(field.fieldcode,4) = 'mad_'
			and field.conditionTypeID = 1
			FOR XML AUTO, root('districttypes'), TYPE
			),
			(
			select distinct mpl.PLTypeID, mpl.PLName
			from dbo.ams_memberProfessionalLicenseTypes as mpl
			inner join dbo.ams_virtualGroupConditions as field on parsename(replace(field.fieldCode,'_','.'),2) = mpl.PLTypeID
			where mpl.orgID = @orgID
			and left(field.fieldcode,4) = 'mpl_'
			and field.conditionTypeID = 1
			FOR XML AUTO, root('licensetypes'), TYPE
			)
		FOR XML RAW('groupAssignmentStructure'), TYPE
		)

	RETURN @xmlStructure

END
GO

ALTER PROC [dbo].[ams_OrgSettingsUpdateLicenseStatus]
@orgID int,
@PLStatusID int,
@StatusName varchar(200)

AS

DECLARE @oldStatusName varchar(200)
declare @rerunview bit, @minRID int, @ruleSQL varchar(max)
DECLARE @tblCond TABLE (orgID int, conditionID int)

select @oldStatusName=StatusName
	from dbo.ams_memberProfessionalLicenseStatuses
	WHERE orgID = @orgID
	AND PLStatusID = @PLStatusID

BEGIN TRAN

IF @oldStatusName <> @StatusName COLLATE SQL_Latin1_General_Cp1_CS_AS BEGIN
	-- new professional license status cannot already exist
	IF EXISTS (select PLStatusID from dbo.ams_memberProfessionalLicenseStatuses where StatusName = @StatusName and orgID = @orgID and PLStatusID <> @PLStatusID) 
		GOTO on_error

	INSERT INTO @tblCond (orgID, conditionID)
	select distinct mplt.orgID, vgc.conditionID
	from dbo.ams_memberProfessionalLicenseTypes as mplt
	inner join dbo.ams_virtualGroupConditions as vgc on vgc.fieldcode = 'mpl_' + cast(mplt.PLTypeID as varchar(10)) + '_status'
	inner join dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = vgc.conditionID
	where mplt.orgID = @orgID
	and cv.conditionValue = @oldStatusName
		IF @@ERROR <> 0 GOTO on_error

	-- update professional license status
	update dbo.ams_memberProfessionalLicenseStatuses set StatusName = @StatusName where orgID = @orgID and PLStatusID = @PLStatusID
		IF @@ERROR <> 0 GOTO on_error

	UPDATE cv
	set cv.conditionValue = @StatusName
	from dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditions as vgc on vgc.conditionID = cv.conditionID
	inner join @tblCond as tbl on tbl.conditionID = vgc.conditionID
	where cv.conditionValue = @oldStatusName
		IF @@ERROR <> 0 GOTO on_error

	UPDATE vgc
	set vgc.verbose = dbo.ams_getVirtualGroupConditionVerbose(vgc.conditionID)
	from dbo.ams_virtualGroupConditions as vgc
	inner join @tblCond as tbl on tbl.conditionID = vgc.conditionID
		IF @@ERROR <> 0 GOTO on_error

END

COMMIT TRAN

RETURN 0

on_error:
	ROLLBACK TRAN
	RETURN -1
GO

ALTER PROC [dbo].[ams_removeMemberDataColumnValue]
@valueID int

AS

declare @orgID int
select @orgID = mdc.orgID
	from dbo.ams_memberDataColumnValues as mdcv
	inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
	where mdcv.valueID = @valueID

-- update members with this value
UPDATE dbo.ams_members
SET dateLastUpdated = getdate()
WHERE memberID in (
	select memberID 
	from dbo.ams_memberData 
	where valueID = @valueID
)	

-- what conditions use this value?
declare @tblCond TABLE (conditionID int, cvid int)
INSERT INTO @tblCond (conditionID, cvid)
select distinct vgc.conditionID, vgcv.cvid
from dbo.ams_memberDataColumnValues as mdcv
inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
inner join dbo.ams_virtualGroupConditions as vgc on vgc.fieldcode = 'md_' + cast(mdc.columnID as varchar(10))
inner join dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
	and vgcv.conditionValue = cast(@valueID as varchar(10))
where mdcv.valueID = @valueID

-- if tied to conditions, need to recalc members who have this value
IF EXISTS (select * from @tblCond) BEGIN
	INSERT INTO dbo.queue_processMemberGroups (orgID, memberid)
	select distinct m.orgID, m.activeMemberID
	from dbo.ams_memberData as md
	inner join dbo.ams_members as m on m.memberid = md.memberid
	where md.valueID = @valueID
END

-- delete from memberdata
DELETE FROM dbo.ams_memberData
where valueID = @valueID

-- remove value
DELETE from dbo.ams_memberDataColumnValues
where valueID = @valueID

-- remove value from conditions
DELETE cv
from dbo.ams_virtualGroupConditionValues as cv
inner join @tblCond as tmp on tmp.cvid = cv.cvid

-- delete condition if they no longer have values
declare @CIDList varchar(max)
select @CIDList = COALESCE(@CIDList + ',', '') + cast(tmp.conditionID as varchar(10)) 
	from @tblCond as tmp
	where exists (
		select count(cvid)
		from dbo.ams_virtualGroupConditionValues
		where conditionID = tmp.conditionID
		having count(cvid) = 0
	)
	group by tmp.conditionID
IF @CIDList is not null
	EXEC dbo.ams_deleteVirtualGroupCondition @orgID=@orgID, @conditionIDList=@CIDList

-- update verbose if condition has other values
UPDATE vgc
set vgc.verbose = dbo.ams_getVirtualGroupConditionVerbose(vgc.conditionID)
from dbo.ams_virtualGroupConditions as vgc
inner join @tblCond as tbl on tbl.conditionID = vgc.conditionID
where exists (
	select count(cvid)
	from dbo.ams_virtualGroupConditionValues
	where conditionID = tbl.conditionID
	having count(cvid) > 0
)

RETURN 0
GO


ALTER PROCEDURE [dbo].[ams_removeMemberProfessionalLicenseStatus]
@orgID int,
@PLStatusID int

AS

declare @minCID int, @rc int
declare @statusName varchar(200)

-- ensure org matches plstatusid
IF NOT EXISTS (select PLStatusID from dbo.ams_memberProfessionalLicenseStatuses where orgID = @orgID AND PLStatusID = @PLStatusID)
	RETURN -1

BEGIN TRAN			

	select @statusName = statusName
		from dbo.ams_memberProfessionalLicenseStatuses
		where orgID = @orgID
		and PLStatusID = @PLStatusID
		IF @@ERROR <> 0 goto on_error

	-- update members with this value
	UPDATE dbo.ams_members
	SET dateLastUpdated = getdate()
	WHERE memberID in (
		select memberID 
		from dbo.ams_memberProfessionalLicenses 
		where PLStatusID = @PLStatusID
	)	
		IF @@ERROR <> 0 goto on_error

	-- what conditions use this value?
	declare @tblCond TABLE (conditionID int, cvid int)
	INSERT INTO @tblCond (conditionID, cvid)
	select distinct vgc.conditionID, vgcv.cvid
	from dbo.ams_memberProfessionalLicenseTypes as mplt
	inner join dbo.ams_virtualGroupConditions as vgc on vgc.fieldcode = 'mpl_' + cast(mplt.PLTypeID as varchar(10)) + '_status'
	inner join dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
	where vgcv.conditionValue = @statusName
	and mplt.orgID = @orgID
		IF @@ERROR <> 0 goto on_error

	-- if tied to conditions, need to recalc members who have this value
	IF EXISTS (select * from @tblCond) BEGIN
		INSERT INTO dbo.queue_processMemberGroups (orgID, memberid)
		select distinct m.orgID, m.activeMemberID
		from dbo.ams_memberProfessionalLicenses as mpl
		inner join dbo.ams_members as m on m.memberid = mpl.memberid
		where mpl.PLStatusID = @PLStatusID
			IF @@ERROR <> 0 goto on_error
	END

	-- Set status to nothing
	UPDATE dbo.ams_memberProfessionalLicenses 
	set PLStatusID = null
	WHERE PLStatusID = @PLStatusID
		IF @@ERROR <> 0 goto on_error

	-- delete any licenses where all 3 fields are now null
	delete
	from dbo.ams_memberProfessionalLicenses
	where nullif(licenseNumber,'') is null
	and activedate is null
	and PlStatusID is null
		IF @@ERROR <> 0 goto on_error

	DELETE FROM dbo.ams_memberProfessionalLicenseStatuses
	WHERE PLStatusID = @PLStatusID
		IF @@ERROR <> 0 goto on_error

	EXEC dbo.ams_reorderMemberProfessionalLicenseStatuses @orgID
		IF @@ERROR <> 0 goto on_error

	-- remove value from conditions
	DELETE cv
	from dbo.ams_virtualGroupConditionValues as cv
	inner join @tblCond as tmp on tmp.cvid = cv.cvid
		IF @@ERROR <> 0 goto on_error

	-- delete condition if they no longer have values
	declare @CIDList varchar(max)
	select @CIDList = COALESCE(@CIDList + ',', '') + cast(tmp.conditionID as varchar(10)) 
		from @tblCond as tmp
		where exists (
			select count(cvid)
			from dbo.ams_virtualGroupConditionValues
			where conditionID = tmp.conditionID
			having count(cvid) = 0
		)
		group by tmp.conditionID
		IF @@ERROR <> 0 goto on_error
	IF @CIDList is not null BEGIN
		EXEC dbo.ams_deleteVirtualGroupCondition @orgID=@orgID, @conditionIDList=@CIDList
			IF @@ERROR <> 0 goto on_error
	END

	-- update verbose if condition has other values
	UPDATE vgc
	set vgc.verbose = dbo.ams_getVirtualGroupConditionVerbose(vgc.conditionID)
	from dbo.ams_virtualGroupConditions as vgc
	inner join @tblCond as tbl on tbl.conditionID = vgc.conditionID
	where exists (
		select count(cvid)
		from dbo.ams_virtualGroupConditionValues
		where conditionID = tbl.conditionID
		having count(cvid) > 0
	)
		IF @@ERROR <> 0 goto on_error

IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

ALTER PROC [dbo].[ams_updateMemberDataColumn]
@columnID int,
@columnName varchar(255),
@columnDesc varchar(255),
@allowMultiple bit,
@skipImport bit,
@allowNull bit,
@defaultValue varchar(max),
@allowNewValuesOnImport bit,
@dataTypeCode varchar(20),
@displayTypeCode varchar(20),
@isReadOnly bit = 0

AS

DECLARE @valueID int, @rc int, @orgID int, @retValue int, @displayTypeID int, @dataTypeID int, @newbitvalueID int
DECLARE @oldColumnName varchar(255), @olddataTypeCode varchar(20), @olddisplayTypeCode varchar(20)
SET @retValue = 0

IF @columnID is not null AND @columnName is not null BEGIN
	SELECT @orgID = orgID from dbo.ams_memberdataColumns where columnID = @columnID

	IF EXISTS(select emailTypeID from dbo.ams_memberEmailTypes where orgID = @orgID and emailType = @columnName)
	OR EXISTS(select websiteTypeID from dbo.ams_memberWebsiteTypes where orgID = @orgID and websiteType = @columnName)
	OR EXISTS(select columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @columnName and columnID <> @columnID)
	OR EXISTS(select C.NAME FROM SYSCOLUMNS C INNER JOIN SYSOBJECTS O ON C.ID = O.ID WHERE O.NAME = 'ams_members' and  C.NAME = @columnName)
		set @retValue = 1
	ELSE BEGIN
		SELECT @oldColumnName = columnName from dbo.ams_memberDataColumns where columnID = @columnID

		-- validate display type when multiple
		IF @allowMultiple = 1 and @displayTypeCode = 'RADIO'
			SELECT @displayTypeCode = 'CHECKBOX'
		IF @allowMultiple = 0 and @displayTypeCode = 'CHECKBOX'
			SELECT @displayTypeCode = 'RADIO'

		-- validations
		SELECT @olddataTypeCode = dt.dataTypeCode
			from dbo.ams_memberDataColumns as c
			inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = c.dataTypeID
			and c.columnID = @columnID
		SELECT @olddisplayTypeCode = dt.displayTypeCode
			from dbo.ams_memberDataColumns as c
			inner join dbo.ams_memberDataColumnDisplayTypes as dt on dt.displayTypeID = c.displayTypeID
			and c.columnID = @columnID
		SELECT @displayTypeID = displayTypeID
			from dbo.ams_memberDataColumnDisplayTypes
			where displayTypeCode = @displayTypeCode
		SELECT @dataTypeID = dataTypeID
			from dbo.ams_memberDataColumnDataTypes
			where dataTypeCode = @dataTypeCode
		IF @displayTypeCode IN ('DOCUMENT','TEXTAREA','HTMLCONTENT') OR @dataTypeCode in ('XML','CONTENTOBJ','DOCUMENTOBJ') BEGIN
			SELECT @allowNull = 1
		END
		IF @displayTypeCode IN ('DOCUMENT') OR @dataTypeCode in ('DOCUMENTOBJ') BEGIN
			SELECT @skipImport = 1
		END
		IF @allowNull = 0 and len(isnull(@defaultValue,'')) = 0
			SELECT @allowNull = 1
		IF @skipImport = 1
			SELECT @allowNewValuesOnImport = 1
		IF @allowNull = 1
			SELECT @defaultValue = ''

		-- set default valueID if necessary
		IF len(isnull(@defaultValue,'')) > 0 BEGIN
			EXEC @rc = dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=@defaultValue, @valueID=@valueID OUTPUT
				IF @@ERROR <> 0 or @rc <> 0 or @valueID = 0 SELECT @allowNull = 1
		END 

		BEGIN TRY
		BEGIN TRAN		
			-- update column info
			UPDATE dbo.ams_memberDataColumns 
			SET columnName = @columnName,
				columnDesc = @columnDesc,
				skipImport = @skipImport,
				allowNull = @allowNull,
				defaultValueID = nullif(@valueID,0),
				allowNewValuesOnImport = @allowNewValuesOnImport,
				isReadOnly = @isReadOnly,
				allowMultiple = @allowMultiple
			WHERE columnID = @columnID

			-- if changing the display type
			IF @displayTypeCode <> @olddisplayTypeCode BEGIN
				UPDATE dbo.ams_memberDataColumns
				SET displayTypeID = @displayTypeID
				WHERE columnID = @columnID

				UPDATE dbo.ams_memberFields
				SET displayTypeID = @displayTypeID
				WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))

				-- if was a radio/select (not bit) and is no longer that, we need to convert valueID to value
				IF @olddataTypeCode <> 'BIT' and @dataTypeCode <> 'BIT' and @olddisplayTypeCode in ('RADIO','SELECT') and @displayTypeCode not in ('RADIO','SELECT') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = coalesce(mdcv.columnValueString, cast(mdcv.columnValueDecimal2 as varchar(15)), cast(mdcv.columnValueInteger as varchar(15)), convert(varchar(10),mdcv.columnvalueDate,101))
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
				END

				-- if was NOT a radio/select (not bit) and is now that, we need to convert value to valueID
				IF @olddataTypeCode <> 'BIT' and @dataTypeCode <> 'BIT' and @olddisplayTypeCode not in ('RADIO','SELECT') and @displayTypeCode in ('RADIO','SELECT') BEGIN
					
					-- err if any expressions are not 1,2,7,8 now that it will be a select
					IF EXISTS (select conditionID from dbo.ams_virtualGroupConditions where fieldCode = 'md_' + cast(@columnID as varchar(10)) and expressionID not in (1,2,7,8))
						RAISERROR('There are group assignment conditions that are not compatible with the selected display type.', 16, 1) 

					-- create column values for those condition values that dont yet exist as column values
					declare @tblVals TABLE (newVal varchar(max))
					insert into @tblVals (newVal)
					select vgcv.conditionValue
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.dataTypeID = 1
					and vgc.expressionID in (1,2)
					and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueString = vgcv.conditionValue)
						union
					select cast(vgcv.conditionValue as varchar(15))
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.dataTypeID = 2
					and vgc.expressionID in (1,2)
					and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueDecimal2 = cast(vgcv.conditionValue as decimal(9,2)))
						union
					select cast(vgcv.conditionValue as varchar(15))
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.dataTypeID = 3
					and vgc.expressionID in (1,2)
					and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueInteger = cast(vgcv.conditionValue as int))
						union
					select convert(varchar(10),vgcv.conditionValue,101)
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.dataTypeID = 4
					and vgc.expressionID in (1,2)
					and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueDate = cast(vgcv.conditionValue as datetime))

					DECLARE @newvalueID int, @minValue varchar(max)
					select @minValue = min(newVal) from @tblVals
					while @minValue is not null BEGIN
						EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=@minValue, @valueID=@newvalueID OUTPUT
						select @minValue = min(newVal) from @tblVals where newVal > @minValue
					END

					-- get the valueID
					UPDATE vgcv
					SET vgcv.conditionValue = tmp.valueID
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					INNER JOIN (
						select vgc.conditionID, mdcv.valueID
						from dbo.ams_virtualGroupConditions as vgc
						INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
						and vgc.expressionID in (1,2)
						and vgc.dataTypeID = 1 
						and vgcv.conditionValue = mdcv.columnvalueString
							union
						select vgc.conditionID, mdcv.valueID
						from dbo.ams_virtualGroupConditions as vgc
						INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
						and vgc.expressionID in (1,2)
						and vgc.dataTypeID = 2 
						and cast(vgcv.conditionValue as decimal(9,2)) = mdcv.columnvalueDecimal2
							union
						select vgc.conditionID, mdcv.valueID
						from dbo.ams_virtualGroupConditions as vgc
						INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
						and vgc.expressionID in (1,2)
						and vgc.dataTypeID = 3 
						and cast(vgcv.conditionValue as int) = mdcv.columnvalueInteger
							union
						select vgc.conditionID, mdcv.valueID
						from dbo.ams_virtualGroupConditions as vgc
						INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
						and vgc.expressionID in (1,2)
						and vgc.dataTypeID = 4 
						and cast(vgcv.conditionValue as datetime) = mdcv.columnvalueDate
					) as tmp on tmp.conditionID = vgc.conditionID
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
				END

				UPDATE dbo.ams_virtualGroupConditions
				SET displayTypeID = @displayTypeID
				WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))

				UPDATE dbo.ams_virtualGroupConditions
				set [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
				WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))
			END

			-- if changing the data type
			IF @dataTypeCode <> @olddataTypeCode BEGIN
				UPDATE dbo.ams_memberDataColumns
				SET dataTypeID = @dataTypeID
				WHERE columnID = @columnID

				UPDATE dbo.ams_memberFields
				SET dataTypeID = @dataTypeID
				WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))

				-- check ams_virtualGroupConditions for expression conflicts
				IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'DECIMAL2' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueDecimal2 = cast(columnValueString as decimal(9,2))
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are string values not compatible with the Decimal Number (2) data type.', 16, 1) 
					END CATCH

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = null
					where columnID = @columnID

					IF EXISTS (
						select conditionID
						from dbo.ams_virtualGroupConditions
						where fieldCode = 'md_' + cast(@columnID as varchar(10))
						and expressionID in (9,10)
					) RAISERROR('There are group assignment conditions that are not compatible with the Decimal Number (2) data type.', 16, 1) 
				END
				IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'INTEGER' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueInteger = cast(columnValueString as int)
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are string values not compatible with the Whole Number data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = null
					where columnID = @columnID

					IF EXISTS (
						select conditionID
						from dbo.ams_virtualGroupConditions
						where fieldCode = 'md_' + cast(@columnID as varchar(10))
						and expressionID in (9,10)
					) RAISERROR('There are group assignment conditions that are not compatible with the Whole Number data type.', 16, 1) 
				END
				IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'DATE' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueDate = cast(columnValueString as datetime)
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are string values not compatible with the Date data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = null
					where columnID = @columnID

					IF EXISTS (
						select conditionID
						from dbo.ams_virtualGroupConditions
						where fieldCode = 'md_' + cast(@columnID as varchar(10))
						and expressionID in (9,10)
					) RAISERROR('There are group assignment conditions that are not compatible with the Date data type.', 16, 1) 
				END
				IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'BIT' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueBit = cast(columnValueString as bit)
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are string values not compatible with the Boolean data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = null
					where columnID = @columnID

					-- ensure both bit values are there					
					EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='1', @valueID=@newbitvalueID OUTPUT
					EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='0', @valueID=@newbitvalueID OUTPUT

					IF EXISTS (
						select conditionID
						from dbo.ams_virtualGroupConditions
						where fieldCode = 'md_' + cast(@columnID as varchar(10))
						and expressionID in (3,4,5,6,9,10)
					) RAISERROR('There are group assignment conditions that are not compatible with the Boolean data type.', 16, 1) 

					-- if was a radio/select, we need to convert valueID to value because BIT doesnt store valueID
					IF @olddisplayTypeCode in ('RADIO','SELECT') BEGIN
						UPDATE vgcv
						SET vgcv.conditionValue = mdcv.columnValueBit
						FROM dbo.ams_virtualGroupConditions as vgc
						INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
							and cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue
						WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
						and vgc.expressionID in (1,2)
					END
				END
				IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'XML' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueXML = cast(columnValueString as xml)
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are string values not compatible with the XML data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = null
					where columnID = @columnID

					IF EXISTS (
						select conditionID
						from dbo.ams_virtualGroupConditions
						where fieldCode = 'md_' + cast(@columnID as varchar(10))
						and expressionID in (1,2,3,4,5,6,9,10)
					) RAISERROR('There are group assignment conditions that are not compatible with the XML data type.', 16, 1) 
				END
				IF @olddataTypeCode = 'DECIMAL2' AND @dataTypeCode = 'STRING' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueString = cast(columnValueDecimal2 as varchar(255))
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are decimal values not compatible with the Text String data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueDecimal2 = null
					where columnID = @columnID
				END
				IF @olddataTypeCode = 'INTEGER' AND @dataTypeCode = 'STRING' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueString = cast(columnValueInteger as varchar(255))
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are whole number values not compatible with the Text String data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueInteger = null
					where columnID = @columnID
				END
				IF @olddataTypeCode = 'INTEGER' AND @dataTypeCode = 'DECIMAL2' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueDecimal2 = cast(columnValueInteger as decimal(9,2))
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are whole number values not compatible with the Decimal Number (2) data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueInteger = null
					where columnID = @columnID
				END
				IF @olddataTypeCode = 'INTEGER' AND @dataTypeCode = 'BIT' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueBit = cast(columnValueInteger as bit)
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are whole number values not compatible with the Boolean data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueInteger = null
					where columnID = @columnID

					-- ensure both bit values are there					
					EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='1', @valueID=@newbitvalueID OUTPUT
					EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='0', @valueID=@newbitvalueID OUTPUT

					IF EXISTS (
						select conditionID
						from dbo.ams_virtualGroupConditions
						where fieldCode = 'md_' + cast(@columnID as varchar(10))
						and expressionID in (3,4,5,6)
					) RAISERROR('There are group assignment conditions that are not compatible with the Boolean data type.', 16, 1) 

					-- if was a radio/select, we need to convert valueID to value because BIT doesnt store valueID
					IF @olddisplayTypeCode in ('RADIO','SELECT') BEGIN
						UPDATE vgcv
						SET vgcv.conditionValue = mdcv.columnValueBit
						FROM dbo.ams_virtualGroupConditions as vgc
						INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
							and cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue
						WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
						and vgc.expressionID in (1,2)
					END
				END
				IF @olddataTypeCode = 'DATE' AND @dataTypeCode = 'STRING' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueString = convert(varchar(10),columnValueDate,101)
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are date values not compatible with the Text String data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueDate = null
					where columnID = @columnID

					IF EXISTS (
						select conditionID
						from dbo.ams_virtualGroupConditions
						where fieldCode = 'md_' + cast(@columnID as varchar(10))
						and expressionID in (11,12)
					) RAISERROR('There are group assignment conditions that are not compatible with the Text String data type.', 16, 1) 
				END
				IF @olddataTypeCode = 'BIT' AND @dataTypeCode = 'STRING' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueString = cast(columnValueBit as varchar(255))
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are boolean values not compatible with the Text String data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueBit = null
					where columnID = @columnID

					-- if going to be radio/select, we need to convert value to valueID because BIT doesnt store valueID
					IF @displayTypeCode in ('RADIO','SELECT') BEGIN
						UPDATE vgcv
						SET vgcv.conditionValue = mdcv.valueID
						FROM dbo.ams_virtualGroupConditions as vgc
						INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
							and mdcv.columnvalueString = vgcv.conditionValue
						WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
						and vgc.expressionID in (1,2)
					END
				END
				IF @olddataTypeCode = 'BIT' AND @dataTypeCode = 'DECIMAL2' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueDecimal2 = cast(columnValueBit as decimal(9,2))
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are boolean values not compatible with the Decimal Number (2) data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueBit = null
					where columnID = @columnID

					-- if going to be radio/select, we need to convert value to valueID because BIT doesnt store valueID
					IF @displayTypeCode in ('RADIO','SELECT') BEGIN
						UPDATE vgcv
						SET vgcv.conditionValue = mdcv.valueID
						FROM dbo.ams_virtualGroupConditions as vgc
						INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
							and mdcv.columnvalueDecimal2 = vgcv.conditionValue
						WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
						and vgc.expressionID in (1,2)
					END
				END
				IF @olddataTypeCode = 'BIT' AND @dataTypeCode = 'INTEGER' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueInteger = cast(columnValueBit as int)
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are boolean values not compatible with the Whole Number data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueBit = null
					where columnID = @columnID

					-- if going to be radio/select, we need to convert value to valueID because BIT doesnt store valueID
					IF @displayTypeCode in ('RADIO','SELECT') BEGIN
						UPDATE vgcv
						SET vgcv.conditionValue = mdcv.valueID
						FROM dbo.ams_virtualGroupConditions as vgc
						INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
							and cast(mdcv.columnvalueInteger as varchar(15)) = vgcv.conditionValue
						WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
						and vgc.expressionID in (1,2)
					END
				END

				UPDATE dbo.ams_virtualGroupConditions
				set dataTypeID = @dataTypeID
				WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))
			
				UPDATE dbo.ams_virtualGroupConditions
				set [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
				WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))
			END

			-- if valueID is not null, there is a def value. 
			-- Anyone who doesnt have a value for this column needs this value.
			IF nullif(@valueID,0) is not null BEGIN
				declare @tblMDDEF TABLE (memberid int PRIMARY KEY)
				insert into @tblMDDEF (memberid)
				select distinct m.memberid
				from dbo.ams_members as m
				left outer join dbo.ams_memberData as md 
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID and mdcv.columnID = @columnID
					on md.memberid = m.memberID
				where m.orgID = @orgID
				and m.memberid = m.activememberid
				and m.status <> 'D'
				and md.dataid is null

				INSERT INTO dbo.ams_memberData (memberid, valueID)
				select memberid, @valueID
				from @tblMDDEF

				UPDATE dbo.ams_members
				SET dateLastUpdated = getdate()
				WHERE memberID in (select memberID from @tblMDDEF)

				-- queue processing of member groups
				insert into dbo.queue_processMemberGroups (orgID, memberID, conditionID)
				select c.orgID, m.memberid, c.conditionID
				from dbo.ams_virtualGroupConditions as C
				cross apply @tblMDDEF as m 
				where c.orgID = @orgID
				and C.fieldcode = 'md_' + Cast(@columnID as varchar(10))
			END

			-- if there was a change in columnname
			IF @oldColumnName <> @columnName COLLATE Latin1_General_CS_AI BEGIN
				-- update member fields
				UPDATE dbo.ams_memberFields
				SET dbField = @columnName
				WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))
			
				-- update virtual group conditions
				UPDATE dbo.ams_virtualGroupConditions
				SET [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
				WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))
			END

		IF @@TRANCOUNT > 0 COMMIT TRAN
		END TRY
		BEGIN CATCH
			IF @@TRANCOUNT > 1 COMMIT TRAN
			ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
			SELECT ERROR_MESSAGE() as errorMessage
			RETURN -1
		END CATCH

		IF @oldColumnName <> @columnName OR @dataTypeCode <> @olddataTypeCode BEGIN
			EXEC dbo.ams_createVWMemberData	@orgID=@orgID
		END

	END
END

RETURN @retValue
GO

ALTER PROC [dbo].[ams_updateMemberDataColumnValue]
@valueID int,
@columnValue varchar(255)

AS

DECLARE @newvalueID int, @columnID int, @recalc bit
DECLARE @tblCond TABLE (orgID int, conditionID int)
SET @recalc = 0

-- if valueID not passed do nothing.
IF @valueID is null or @columnValue is null
	GOTO on_error

-- get data type code for column
DECLARE @columnDataTypeCode varchar(20)
SELECT @columnDataTypeCode = dt.dataTypeCode, @columnID = c.columnID
	from dbo.ams_memberDataColumnValues as cv
	inner join dbo.ams_memberDataColumns as c on c.columnID = cv.columnID
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = c.dataTypeID
	where cv.valueID = @valueID

IF @columnDataTypeCode = 'STRING' BEGIN
	DECLARE @realColumnValue varchar(255)
	SELECT @realColumnValue = @columnValue
		IF @@ERROR <> 0 GOTO on_error
	SELECT @newvalueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueString = @realColumnValue
	IF @newvalueID is null BEGIN
		UPDATE dbo.ams_memberDataColumnValues 
		SET columnValueString = @realColumnValue
		WHERE valueID = @valueID
			IF @@ERROR <> 0 GOTO on_error
		SET @recalc = 1
	END
END
IF @columnDataTypeCode = 'DECIMAL2' BEGIN
	DECLARE @realColumnValue2 decimal(9,2)
	SELECT @realColumnValue2 = cast(@columnValue as decimal(9,2))
		IF @@ERROR <> 0 GOTO on_error
	SELECT @newvalueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueDecimal2 = @realColumnValue2
	IF @newvalueID is null BEGIN
		UPDATE dbo.ams_memberDataColumnValues 
		SET columnValueDecimal2 = @realColumnValue2
		WHERE valueID = @valueID
			IF @@ERROR <> 0 GOTO on_error
		SET @recalc = 1
	END
END
IF @columnDataTypeCode = 'INTEGER' BEGIN
	DECLARE @realColumnValue3 int
	SELECT @realColumnValue3 = cast(@columnValue as int)
		IF @@ERROR <> 0 GOTO on_error
	SELECT @newvalueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueInteger = @realColumnValue3
	IF @newvalueID is null BEGIN
		UPDATE dbo.ams_memberDataColumnValues 
		SET columnValueInteger = @realColumnValue3
		WHERE valueID = @valueID
			IF @@ERROR <> 0 GOTO on_error
		SET @recalc = 1
	END
END
IF @columnDataTypeCode = 'DATE' BEGIN
	DECLARE @realColumnValue4 datetime
	SELECT @realColumnValue4 = cast(@columnValue as datetime)
		IF @@ERROR <> 0 GOTO on_error
	SELECT @newvalueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueDate = @realColumnValue4
	IF @newvalueID is null BEGIN
		UPDATE dbo.ams_memberDataColumnValues 
		SET columnValueDate = @realColumnValue4
		WHERE valueID = @valueID
			IF @@ERROR <> 0 GOTO on_error
		SET @recalc = 1
	END
END
IF @columnDataTypeCode = 'BIT' BEGIN
	DECLARE @realColumnValue5 bit
	SELECT @realColumnValue5 = cast(@columnValue as bit)
		IF @@ERROR <> 0 GOTO on_error
	SELECT @newvalueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueBit = @realColumnValue5
	IF @newvalueID is null BEGIN
		UPDATE dbo.ams_memberDataColumnValues 
		SET columnValueBit = @realColumnValue5
		WHERE valueID = @valueID
			IF @@ERROR <> 0 GOTO on_error
		SET @recalc = 1
	END
END
IF @columnDataTypeCode = 'XML' BEGIN
	DECLARE @realColumnValue6 xml
	SELECT @realColumnValue6 = cast(@columnValue as xml)
		IF @@ERROR <> 0 GOTO on_error
	SELECT @newvalueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and cast(columnValueXML as varchar(max)) = cast(@realColumnValue6 as varchar(max))
	IF @newvalueID is null BEGIN
		UPDATE dbo.ams_memberDataColumnValues 
		SET columnValueXML = @realColumnValue6
		WHERE valueID = @valueID
			IF @@ERROR <> 0 GOTO on_error
		SET @recalc = 1
	END
END
IF @columnDataTypeCode = 'CONTENTOBJ' OR @columnDataTypeCode = 'DOCUMENTOBJ' BEGIN
	DECLARE @realColumnValue7 int
	SELECT @realColumnValue7 = cast(@columnValue as int)
		IF @@ERROR <> 0 GOTO on_error
	SELECT @newvalueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueSiteResourceID = @realColumnValue7
	IF @newvalueID is null BEGIN
		UPDATE dbo.ams_memberDataColumnValues 
		SET columnValueSiteResourceID = @realColumnValue7
		WHERE valueID = @valueID
			IF @@ERROR <> 0 GOTO on_error
		SET @recalc = 1
	END
END

IF @recalc = 1 BEGIN
	INSERT INTO @tblCond (orgID, conditionID)
	select mdc.orgID, vgc.conditionID
	from dbo.ams_memberDataColumnValues as mdcv
	inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
	inner join dbo.ams_virtualGroupConditions as vgc on vgc.fieldcode = 'md_' + cast(mdc.columnID as varchar(10))
	inner join dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
		and vgcv.conditionValue = cast(@valueID as varchar(10))
	where mdcv.valueID = @valueID
		IF @@ERROR <> 0 GOTO on_error

	UPDATE vgc
	set vgc.verbose = dbo.ams_getVirtualGroupConditionVerbose(vgc.conditionID)
	from dbo.ams_virtualGroupConditions as vgc
	inner join @tblCond as tbl on tbl.conditionID = vgc.conditionID
		IF @@ERROR <> 0 GOTO on_error

	INSERT INTO dbo.queue_processMemberGroups (orgID, conditionID)
	SELECT orgID, conditionID
	from @tblCond
		IF @@ERROR <> 0 GOTO on_error
END

RETURN 0

-- error exit
on_error:
	RETURN -1
GO

DROP INDEX _dta_index_ams_virtualGroupConditions_9_1987888732__K6_K1_K5_K2_K11_K9 ON dbo.ams_virtualGroupConditions
GO
ALTER TABLE dbo.ams_virtualGroupConditions DROP COLUMN value
GO
DROP STATISTICS dbo.ams_virtualGroupConditions._dta_stat_1076601507_1_5_15
GO
DROP STATISTICS dbo.ams_virtualGroupConditions._dta_stat_1076601507_1_6_5_15_2_14
GO
ALTER TABLE dbo.ams_virtualGroupConditions ALTER COLUMN displayTypeID int NOT NULL
GO




ALTER PROC [dbo].[cache_members_populateConditionCache]
@orgID int,
@conditionIDList varchar(max) = null,
@memberID int = null

as

set nocount on

declare @starttime datetime, @endtime datetime, @finalCount int
DECLARE @tblConditions TABLE (conditionID int)
select @starttime = getdate()

print 'Running cache_members_populateConditionCache for orgID ' + Cast(@orgID as varchar(10)) + ', conditionID ' + isnull(@conditionIDList,'--') + ', memberID ' + isnull(cast(@memberid as varchar(10)),'--')

IF OBJECT_ID('tempdb..#cache_members_conditions_shouldbe') IS NOT NULL
	DROP TABLE #cache_members_conditions_shouldbe
IF OBJECT_ID('tempdb..#tblCondALL') IS NOT NULL
	DROP TABLE #tblCondALL
IF OBJECT_ID('tempdb..#tblCondALLFinal') IS NOT NULL
	DROP TABLE #tblCondALLFinal
IF OBJECT_ID('tempdb..#tblFCSplit') IS NOT NULL
	DROP TABLE #tblFCSplit
CREATE TABLE #cache_members_conditions_shouldbe (memberid int, conditionID int)
CREATE TABLE #tblFCSplit (fieldcode varchar(50), v1 varchar(30), v2 varchar(30), v3 varchar(30), v4 varchar(30), v5 varchar(30), v6 varchar(30))
CREATE TABLE #tblCondALLFinal (conditionID int PRIMARY KEY)

-- split conditions to calculate
INSERT INTO @tblConditions (conditionID)
select distinct c.conditionID
from dbo.fn_intListToTable(@conditionIDList,',') as tmp
inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tmp.listitem
where c.orgID = @orgID

IF @@rowcount = 0
	INSERT INTO @tblConditions (conditionID)
	select c.conditionID
	from dbo.ams_virtualGroupConditions as c WITH(NOLOCK)
	where c.orgID = @orgID

-- get all conditions to calculate
select c.conditionID, e.expression, c.fieldCode, fieldCodeArea = case
	when left(c.fieldCode,2) = 'm_' then 'Member Data'	
	when left(c.fieldCode,3) = 'md_' then 'Custom Fields'	
	when left(c.fieldCode,3) = 'ma_' then 'Addresses'	
	when left(c.fieldCode,3) = 'mp_' then 'Phones'	
	when left(c.fieldCode,4) = 'mad_' then 'Districting'	
	when left(c.fieldCode,3) = 'me_' then 'Emails'	
	when left(c.fieldCode,3) = 'mw_' then 'Websites'	
	when left(c.fieldCode,2) = 'e_' then 'Events'	
	when left(c.fieldcode,4) = 'mpl_' then 'Professional Licenses'	
	when left(c.fieldcode,4) = 'grp_' then 'Groups'	-- excluded in where clause
	when left(c.fieldcode,4) = 'sub_' then 'Subscriptions'	
	end, dit.displayTypeCode, dat.dataTypeCode,
	fieldCodeAreaID = case 
	when left(c.fieldCode,3) = 'md_' then cast(replace(c.fieldcode,'md_','') as int)
	when left(c.fieldcode,4) = 'mpl_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,3) = 'ma_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,2) = 'e_' then cast(replace(c.fieldcode,'e_','') as int)
	when left(c.fieldCode,3) = 'me_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,3) = 'mw_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,3) = 'mp_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as int)
	when left(c.fieldCode,4) = 'mad_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as int)
	else null end,
	fieldCodeAreaPartA = case
	when left(c.fieldcode,4) = 'mpl_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as varchar(20))
	when left(c.fieldCode,3) = 'ma_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as varchar(20))
	when left(c.fieldCode,3) = 'mp_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as varchar(10))
	when left(c.fieldCode,4) = 'mad_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as varchar(10))
	else null end
into #tblCondALL
from dbo.ams_virtualGroupConditions as c WITH(NOLOCK)
inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.expressionID
inner join dbo.ams_memberDataColumnDataTypes as dat WITH(NOLOCK) on dat.dataTypeID = c.dataTypeID
inner join dbo.ams_memberDataColumnDisplayTypes as dit WITH(NOLOCK) on dit.displayTypeID = c.displayTypeID
inner join @tblConditions as tblC on tblc.conditionID = c.conditionID
where c.orgID = @orgID
and c.isDefined = 1
and left(c.fieldCode,4) <> 'grp_'

-- add indexes for speed
ALTER TABLE #tblCondALL ADD PRIMARY KEY(conditionID);
CREATE INDEX IX_tblCondALL_areaid ON #tblCondALL (fieldCodeAreaID asc);
CREATE INDEX IX_tblCondALL_areaparta ON #tblCondALL (fieldCodeAreaPartA asc);
CREATE NONCLUSTERED INDEX IX_tblCondALL_DTA2 ON #tblCondALL (fieldCodeArea ASC, dataTypeCode ASC, expression ASC);

-- for the final processing at the end
insert into #tblCondALLFinal (conditionid)
select conditionID from #tblCondALL

-- this is used in subscriptions calculations
insert into #tblFCSplit (fieldcode, v1, v2, v3, v4, v5, v6)
select distinct fieldCode, [1], [2], [3], [4], [5], [6]
from #tblCondALL as tblc  
cross apply dbo.fn_varcharListToTable(tblc.fieldCode,'_') as vclist
PIVOT (min(listItem) FOR autoID in ([1],[2],[3],[4],[5],[6])) as pvt
where fieldCodeArea = 'Subscriptions'

CREATE INDEX IX_tblFCSplit_fc ON #tblFCSplit (fieldcode asc);
CREATE INDEX IX_tblFCSplit_v1 ON #tblFCSplit (v1 asc);
CREATE INDEX IX_tblFCSplit_v2 ON #tblFCSplit (v2 asc);
CREATE INDEX IX_tblFCSplit_v3 ON #tblFCSplit (v3 asc);
CREATE INDEX IX_tblFCSplit_v4 ON #tblFCSplit (v4 asc);
CREATE INDEX IX_tblFCSplit_v5 ON #tblFCSplit (v5 asc);
CREATE INDEX IX_tblFCSplit_v6 ON #tblFCSplit (v6 asc);


/* These have been arranged in order of their popularity for faster processing. */

/* ******************** */
/* md_xxxx, eq, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and cast(vw.valueID as varchar(10)) = cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'STRING'
	and tblc.displayTypeCode in ('RADIO','SELECT')

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue = cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'STRING'
	and tblc.displayTypeCode not in ('RADIO','SELECT')

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'eq' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* sub_xxxx, subscribed  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Subscriptions' and expression='subscribed') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblFCSplit as fcsplit on fcsplit.fieldCode = tblc.fieldCode
	inner join dbo.sub_subscriptions as s WITH(NOLOCK) on fcsplit.v2 in (0,s.typeID)
       and fcsplit.v3 in (0,s.subscriptionID)
	inner join dbo.sub_rateFrequencies as rf WITH(NOLOCK) on fcsplit.v4 in (0,rf.rateID)
	inner join dbo.sub_subscribers as sub WITH(NOLOCK) on rf.rfid = sub.RFID
       and s.subscriptionID = sub.subscriptionID
       and fcsplit.v5 in (0,sub.statusID)
       and fcsplit.v6 in (0,sub.paymentStatusID)
	inner join dbo.ams_members as m2 WITH(NOLOCK) on m2.memberid = sub.memberid
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberID = m2.activeMemberID
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.activememberid = isnull(@memberid,m.activememberid)
	where tblc.fieldCodeArea = 'Subscriptions' 
	and tblc.expression = 'subscribed' 

	delete from #tblCondALL 
	where fieldCodeArea = 'Subscriptions' and expression = 'subscribed'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, eq, bit  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='BIT') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__bit as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue = cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'BIT'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'eq' and dataTypeCode = 'BIT'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************ */
/* ma_xxxx, eq  */
/* ************ */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='eq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma WITH(NOLOCK) on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = ma.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'eq' 
	and tblc.displayTypeCode not in ('RADIO','SELECT')
	and case tblc.fieldCodeAreaPartA
		when 'address1' then isnull(ma.address1,'') 
		when 'address2' then isnull(ma.address2,'') 
		when 'address3' then isnull(ma.address3,'') 
		when 'city' then isnull(ma.city,'') 
		when 'postalcode' then isnull(ma.postalcode,'') 
		when 'county' then isnull(ma.county,'') 
		else isnull(ma.attn,'') end = cv.conditionValue

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma WITH(NOLOCK) on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = ma.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'eq' 
	and tblc.displayTypeCode in ('RADIO','SELECT')
	and case tblc.fieldCodeAreaPartA
		when 'stateprov' then isnull(ma.stateid,0) 
		else isnull(ma.countryid,0) end = cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'eq'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, eq, int  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.valueID = cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'INTEGER'
	and tblc.displayTypeCode in ('RADIO','SELECT')

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue = cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'INTEGER'
	and tblc.displayTypeCode not in ('RADIO','SELECT')

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'eq' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, eq, string  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='eq' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
	left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls WITH(NOLOCK) on mpls.PLStatusID = mpl.PLStatusID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'STRING'
	and case tblc.fieldCodeAreaPartA
		when 'status' then isnull(mpls.statusName,'') 
		else isnull(mpl.licensenumber,'') end = cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'eq' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, datediff  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='datediff') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'eq'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,vw.columnValue,getdate())
			when 'm' then datediff(m,vw.columnValue,getdate())
			when 'wk' then datediff(wk,vw.columnValue,getdate())
			when 'dw' then datediff(dw,vw.columnValue,getdate())
			when 'd' then datediff(d,vw.columnValue,getdate())
			when 'dy' then datediff(dy,vw.columnValue,getdate())
			else datediff(yy,vw.columnValue,getdate()) end = abs(cv.conditionValue)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValue >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'eq'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),vw.columnValue)
			when 'm' then datediff(m,getdate(),vw.columnValue)
			when 'wk' then datediff(wk,getdate(),vw.columnValue)
			when 'dw' then datediff(dw,getdate(),vw.columnValue)
			when 'd' then datediff(d,getdate(),vw.columnValue)
			when 'dy' then datediff(dy,getdate(),vw.columnValue)
			else datediff(yy,getdate(),vw.columnValue) end = abs(cv.conditionValue)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValue < 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'neq'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,vw.columnValue,getdate())
			when 'm' then datediff(m,vw.columnValue,getdate())
			when 'wk' then datediff(wk,vw.columnValue,getdate())
			when 'dw' then datediff(dw,vw.columnValue,getdate())
			when 'd' then datediff(d,vw.columnValue,getdate())
			when 'dy' then datediff(dy,vw.columnValue,getdate())
			else datediff(yy,vw.columnValue,getdate()) end <> abs(cv.conditionValue)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValue >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'neq'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),vw.columnValue)
			when 'm' then datediff(m,getdate(),vw.columnValue)
			when 'wk' then datediff(wk,getdate(),vw.columnValue)
			when 'dw' then datediff(dw,getdate(),vw.columnValue)
			when 'd' then datediff(d,getdate(),vw.columnValue)
			when 'dy' then datediff(dy,getdate(),vw.columnValue)
			else datediff(yy,getdate(),vw.columnValue) end <> abs(cv.conditionValue)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValue < 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'lt'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,vw.columnValue,getdate())
			when 'm' then datediff(m,vw.columnValue,getdate())
			when 'wk' then datediff(wk,vw.columnValue,getdate())
			when 'dw' then datediff(dw,vw.columnValue,getdate())
			when 'd' then datediff(d,vw.columnValue,getdate())
			when 'dy' then datediff(dy,vw.columnValue,getdate())
			else datediff(yy,vw.columnValue,getdate()) end < abs(cv.conditionValue)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValue >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'lt'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),vw.columnValue)
			when 'm' then datediff(m,getdate(),vw.columnValue)
			when 'wk' then datediff(wk,getdate(),vw.columnValue)
			when 'dw' then datediff(dw,getdate(),vw.columnValue)
			when 'd' then datediff(d,getdate(),vw.columnValue)
			when 'dy' then datediff(dy,getdate(),vw.columnValue)
			else datediff(yy,getdate(),vw.columnValue) end < abs(cv.conditionValue)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValue < 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'lte'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,vw.columnValue,getdate())
			when 'm' then datediff(m,vw.columnValue,getdate())
			when 'wk' then datediff(wk,vw.columnValue,getdate())
			when 'dw' then datediff(dw,vw.columnValue,getdate())
			when 'd' then datediff(d,vw.columnValue,getdate())
			when 'dy' then datediff(dy,vw.columnValue,getdate())
			else datediff(yy,vw.columnValue,getdate()) end <= abs(cv.conditionValue)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValue >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'lte'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),vw.columnValue)
			when 'm' then datediff(m,getdate(),vw.columnValue)
			when 'wk' then datediff(wk,getdate(),vw.columnValue)
			when 'dw' then datediff(dw,getdate(),vw.columnValue)
			when 'd' then datediff(d,getdate(),vw.columnValue)
			when 'dy' then datediff(dy,getdate(),vw.columnValue)
			else datediff(yy,getdate(),vw.columnValue) end <= abs(cv.conditionValue)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValue < 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'gt'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,vw.columnValue,getdate())
			when 'm' then datediff(m,vw.columnValue,getdate())
			when 'wk' then datediff(wk,vw.columnValue,getdate())
			when 'dw' then datediff(dw,vw.columnValue,getdate())
			when 'd' then datediff(d,vw.columnValue,getdate())
			when 'dy' then datediff(dy,vw.columnValue,getdate())
			else datediff(yy,vw.columnValue,getdate()) end > abs(cv.conditionValue)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValue >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'gt'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),vw.columnValue)
			when 'm' then datediff(m,getdate(),vw.columnValue)
			when 'wk' then datediff(wk,getdate(),vw.columnValue)
			when 'dw' then datediff(dw,getdate(),vw.columnValue)
			when 'd' then datediff(d,getdate(),vw.columnValue)
			when 'dy' then datediff(dy,getdate(),vw.columnValue)
			else datediff(yy,getdate(),vw.columnValue) end > abs(cv.conditionValue)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValue < 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'gte'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,vw.columnValue,getdate())
			when 'm' then datediff(m,vw.columnValue,getdate())
			when 'wk' then datediff(wk,vw.columnValue,getdate())
			when 'dw' then datediff(dw,vw.columnValue,getdate())
			when 'd' then datediff(d,vw.columnValue,getdate())
			when 'dy' then datediff(dy,vw.columnValue,getdate())
			else datediff(yy,vw.columnValue,getdate()) end >= abs(cv.conditionValue)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValue >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'gte'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),vw.columnValue)
			when 'm' then datediff(m,getdate(),vw.columnValue)
			when 'wk' then datediff(wk,getdate(),vw.columnValue)
			when 'dw' then datediff(dw,getdate(),vw.columnValue)
			when 'd' then datediff(d,getdate(),vw.columnValue)
			when 'dy' then datediff(dy,getdate(),vw.columnValue)
			else datediff(yy,getdate(),vw.columnValue) end >= abs(cv.conditionValue)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValue < 0

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'datediff'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, exists, string  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='exists' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
	left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls WITH(NOLOCK) on mpls.PLStatusID = mpl.PLStatusID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'exists' 
	and tblc.dataTypeCode = 'STRING'
	and case tblc.fieldCodeAreaPartA
		when 'status' then nullif(mpls.StatusName,'')
		else nullif(mpl.licensenumber,'') end is not null

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'exists' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, exists, bit  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='BIT') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.vw_memberData__bit as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue is not null
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'exists' 
	and tblc.dataTypeCode = 'BIT'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'exists' and dataTypeCode = 'BIT'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* e_xxxx, registered  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Events' and expression='registered') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ev_events as e WITH(NOLOCK) on e.eventID = tblc.fieldCodeAreaID
	inner join dbo.ev_registration as er WITH(NOLOCK) on er.eventID = e.eventID and er.status = 'A'
	inner join dbo.ev_registrants as reg WITH(NOLOCK) on reg.registrationID = er.registrationID and reg.status = 'A'
	inner join dbo.ams_members as m2 WITH(NOLOCK) on m2.memberid = reg.memberid
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberID = m2.activeMemberID
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.activememberid = isnull(@memberid,m.activememberid)
	where tblc.fieldCodeArea = 'Events' 
	and tblc.expression = 'registered' 

	delete from #tblCondALL 
	where fieldCodeArea = 'Events' and expression = 'registered'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* md_xxxx, exists, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue is not null
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'exists' 
	and tblc.dataTypeCode = 'STRING'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'exists' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc


/* THE FOLLOWING ARE NOT ORDERED BY POPULARITY DUE TO LOW INSTANCES (UNDER 50) */


/* ****************** */
/* mpl_xxxx, datediff  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='datediff') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'eq'
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,mpl.activeDate,getdate())
			when 'm' then datediff(m,mpl.activeDate,getdate())
			when 'wk' then datediff(wk,mpl.activeDate,getdate())
			when 'dw' then datediff(dw,mpl.activeDate,getdate())
			when 'd' then datediff(d,mpl.activeDate,getdate())
			when 'dy' then datediff(dy,mpl.activeDate,getdate())
			else datediff(yy,mpl.activeDate,getdate()) end = abs(cv.conditionValue)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff'
	and cv.conditionValue >= 0 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'eq'
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),mpl.activeDate)
			when 'm' then datediff(m,getdate(),mpl.activeDate)
			when 'wk' then datediff(wk,getdate(),mpl.activeDate)
			when 'dw' then datediff(dw,getdate(),mpl.activeDate)
			when 'd' then datediff(d,getdate(),mpl.activeDate)
			when 'dy' then datediff(dy,getdate(),mpl.activeDate)
			else datediff(yy,getdate(),mpl.activeDate) end = abs(cv.conditionValue)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff' 
	and cv.conditionValue < 0 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'neq'
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,mpl.activeDate,getdate())
			when 'm' then datediff(m,mpl.activeDate,getdate())
			when 'wk' then datediff(wk,mpl.activeDate,getdate())
			when 'dw' then datediff(dw,mpl.activeDate,getdate())
			when 'd' then datediff(d,mpl.activeDate,getdate())
			when 'dy' then datediff(dy,mpl.activeDate,getdate())
			else datediff(yy,mpl.activeDate,getdate()) end <> abs(cv.conditionValue)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff' 
	and cv.conditionValue >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'neq'
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),mpl.activeDate)
			when 'm' then datediff(m,getdate(),mpl.activeDate)
			when 'wk' then datediff(wk,getdate(),mpl.activeDate)
			when 'dw' then datediff(dw,getdate(),mpl.activeDate)
			when 'd' then datediff(d,getdate(),mpl.activeDate)
			when 'dy' then datediff(dy,getdate(),mpl.activeDate)
			else datediff(yy,getdate(),mpl.activeDate) end <> abs(cv.conditionValue)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff' 
	and cv.conditionValue < 0 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'lt'
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,mpl.activeDate,getdate())
			when 'm' then datediff(m,mpl.activeDate,getdate())
			when 'wk' then datediff(wk,mpl.activeDate,getdate())
			when 'dw' then datediff(dw,mpl.activeDate,getdate())
			when 'd' then datediff(d,mpl.activeDate,getdate())
			when 'dy' then datediff(dy,mpl.activeDate,getdate())
			else datediff(yy,mpl.activeDate,getdate()) end < abs(cv.conditionValue)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses'
	and tblc.expression = 'datediff' 
	and cv.conditionValue >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'lt'
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),mpl.activeDate)
			when 'm' then datediff(m,getdate(),mpl.activeDate)
			when 'wk' then datediff(wk,getdate(),mpl.activeDate)
			when 'dw' then datediff(dw,getdate(),mpl.activeDate)
			when 'd' then datediff(d,getdate(),mpl.activeDate)
			when 'dy' then datediff(dy,getdate(),mpl.activeDate)
			else datediff(yy,getdate(),mpl.activeDate) end < abs(cv.conditionValue)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses'
	and tblc.expression = 'datediff' 
	and cv.conditionValue < 0 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'lte'
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,mpl.activeDate,getdate())
			when 'm' then datediff(m,mpl.activeDate,getdate())
			when 'wk' then datediff(wk,mpl.activeDate,getdate())
			when 'dw' then datediff(dw,mpl.activeDate,getdate())
			when 'd' then datediff(d,mpl.activeDate,getdate())
			when 'dy' then datediff(dy,mpl.activeDate,getdate())
			else datediff(yy,mpl.activeDate,getdate()) end <= abs(cv.conditionValue)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff' 
	and cv.conditionValue >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'lte'
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),mpl.activeDate)
			when 'm' then datediff(m,getdate(),mpl.activeDate)
			when 'wk' then datediff(wk,getdate(),mpl.activeDate)
			when 'dw' then datediff(dw,getdate(),mpl.activeDate)
			when 'd' then datediff(d,getdate(),mpl.activeDate)
			when 'dy' then datediff(dy,getdate(),mpl.activeDate)
			else datediff(yy,getdate(),mpl.activeDate) end <= abs(cv.conditionValue)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff' 
	and cv.conditionValue < 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'gt'
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,mpl.activeDate,getdate())
			when 'm' then datediff(m,mpl.activeDate,getdate())
			when 'wk' then datediff(wk,mpl.activeDate,getdate())
			when 'dw' then datediff(dw,mpl.activeDate,getdate())
			when 'd' then datediff(d,mpl.activeDate,getdate())
			when 'dy' then datediff(dy,mpl.activeDate,getdate())
			else datediff(yy,mpl.activeDate,getdate()) end > abs(cv.conditionValue)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff' 
	and cv.conditionValue >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'gt'
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),mpl.activeDate)
			when 'm' then datediff(m,getdate(),mpl.activeDate)
			when 'wk' then datediff(wk,getdate(),mpl.activeDate)
			when 'dw' then datediff(dw,getdate(),mpl.activeDate)
			when 'd' then datediff(d,getdate(),mpl.activeDate)
			when 'dy' then datediff(dy,getdate(),mpl.activeDate)
			else datediff(yy,getdate(),mpl.activeDate) end > abs(cv.conditionValue)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff' 
	and cv.conditionValue < 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'gte'
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,mpl.activeDate,getdate())
			when 'm' then datediff(m,mpl.activeDate,getdate())
			when 'wk' then datediff(wk,mpl.activeDate,getdate())
			when 'dw' then datediff(dw,mpl.activeDate,getdate())
			when 'd' then datediff(d,mpl.activeDate,getdate())
			when 'dy' then datediff(dy,mpl.activeDate,getdate())
			else datediff(yy,mpl.activeDate,getdate()) end >= abs(cv.conditionValue)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff' 
	and cv.conditionValue >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'gte'
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),mpl.activeDate)
			when 'm' then datediff(m,getdate(),mpl.activeDate)
			when 'wk' then datediff(wk,getdate(),mpl.activeDate)
			when 'dw' then datediff(dw,getdate(),mpl.activeDate)
			when 'd' then datediff(d,getdate(),mpl.activeDate)
			when 'dy' then datediff(dy,getdate(),mpl.activeDate)
			else datediff(yy,getdate(),mpl.activeDate) end >= abs(cv.conditionValue)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff' 
	and cv.conditionValue < 0

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'datediff'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************* */
/* md_xxxx, neq, string  */
/* ********************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activeMemberID
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'STRING'
	and tblc.displayTypeCode in ('RADIO','SELECT')
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__string as vw WITH(NOEXPAND)
		inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
			and vw.valueID = cv.conditionValue
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
	)	

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activeMemberID
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'STRING'
	and tblc.displayTypeCode not in ('RADIO','SELECT')
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__string as vw WITH(NOEXPAND)
		inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
			and vw.columnValue = cv.conditionValue
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'neq' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* md_xxxx, not_exists, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activeMemberID
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'not_exists' 
	and tblc.dataTypeCode = 'STRING'
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__string as vw WITH(NOEXPAND)
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
		and vw.columnValue is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'not_exists' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************** */
/* md_xxxx, eq, decimal2  */
/* ********************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='DECIMAL2') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.valueID = cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'DECIMAL2'
	and tblc.displayTypeCode in ('RADIO','SELECT')

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue = cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'DECIMAL2'
	and tblc.displayTypeCode not in ('RADIO','SELECT')

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'eq' and dataTypeCode = 'DECIMAL2'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, eq, date  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.valueID = cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'DATE'
	and tblc.displayTypeCode in ('RADIO','SELECT')

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue = cast(cv.conditionValue as datetime)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'DATE'
	and tblc.displayTypeCode not in ('RADIO','SELECT')

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'eq' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, neq, bit  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='BIT') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activeMemberID
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'BIT'
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__bit as vw WITH(NOEXPAND)
		inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
			and vw.columnValue = cv.conditionValue
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'neq' and dataTypeCode = 'BIT'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, neq, int  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activeMemberID
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'INTEGER'
	and tblc.displayTypeCode in ('RADIO','SELECT')
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__integer as vw WITH(NOEXPAND)
		inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
			and vw.valueID = cv.conditionValue
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
	)	

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activeMemberID
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'INTEGER'
	and tblc.displayTypeCode not in ('RADIO','SELECT')
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__integer as vw WITH(NOEXPAND)
		inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
			and vw.columnValue = cv.conditionValue
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'neq' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********************** */
/* md_xxxx, neq, decimal2  */
/* *********************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='DECIMAL2') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activeMemberID
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'DECIMAL2'
	and tblc.displayTypeCode in ('RADIO','SELECT')
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND)
		inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
			and vw.valueID = cv.conditionValue
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
	)	

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activeMemberID
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'DECIMAL2'
	and tblc.displayTypeCode not in ('RADIO','SELECT')
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND)
		inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
			and vw.columnValue = cv.conditionValue
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'neq' and dataTypeCode = 'DECIMAL2'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* md_xxxx, neq, date  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activeMemberID
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'DATE'
	and tblc.displayTypeCode in ('RADIO','SELECT')
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__date as vw WITH(NOEXPAND)
		inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
			and vw.valueID = cv.conditionValue
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
	)	

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activeMemberID
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'DATE'
	and tblc.displayTypeCode not in ('RADIO','SELECT')
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__date as vw WITH(NOEXPAND)
		inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
			and vw.columnValue = cast(cv.conditionValue as datetime)
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'neq' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* md_xxxx, lt, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue < cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'lt' 
	and tblc.dataTypeCode = 'STRING'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'lt' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, lt, int  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue < cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'lt' 
	and tblc.dataTypeCode = 'INTEGER'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'lt' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************** */
/* md_xxxx, lt, decimal2  */
/* ********************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='DECIMAL2') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue < cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'lt' 
	and tblc.dataTypeCode = 'DECIMAL2'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'lt' and dataTypeCode = 'DECIMAL2'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, lt, date  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue < cast(cv.conditionValue as datetime)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'lt' 
	and tblc.dataTypeCode = 'DATE'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'lt' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* md_xxxx, lte, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue <= cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'lte' 
	and tblc.dataTypeCode = 'STRING'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'lte' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, lte, int  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue <= cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'lte' 
	and tblc.dataTypeCode = 'INTEGER'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'lte' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************** */
/* md_xxxx, lte, decimal2  */
/* ********************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='DECIMAL2') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue <= cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'lte' 
	and tblc.dataTypeCode = 'DECIMAL2'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'lte' and dataTypeCode = 'DECIMAL2'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, lte, date  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue <= cast(cv.conditionValue as datetime)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'lte' 
	and tblc.dataTypeCode = 'DATE'                                                         

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'lte' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* md_xxxx, gt, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue > cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'gt' 
	and tblc.dataTypeCode = 'STRING'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'gt' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, gt, int  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue > cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'gt' 
	and tblc.dataTypeCode = 'INTEGER'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'gt' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************** */
/* md_xxxx, gt, decimal2  */
/* ********************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='DECIMAL2') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue > cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'gt' 
	and tblc.dataTypeCode = 'DECIMAL2'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'gt' and dataTypeCode = 'DECIMAL2'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, gt, date  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue > cast(cv.conditionValue as datetime)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'gt' 
	and tblc.dataTypeCode = 'DATE'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'gt' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* md_xxxx, gte, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue >= cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'gte' 
	and tblc.dataTypeCode = 'STRING'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'gte' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, gte, int  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue >= cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'gte' 
	and tblc.dataTypeCode = 'INTEGER'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'gte' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************** */
/* md_xxxx, gte, decimal2  */
/* ********************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='DECIMAL2') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue >= cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'gte' 
	and tblc.dataTypeCode = 'DECIMAL2'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'gte' and dataTypeCode = 'DECIMAL2'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, gte, date  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue >= cast(cv.conditionValue as datetime)
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'gte' 
	and tblc.dataTypeCode = 'DATE'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'gte' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, exists, int  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue is not null
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'exists' 
	and tblc.dataTypeCode = 'INTEGER'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'exists' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************** */
/* md_xxxx, exists, decimal2  */
/* ********************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='DECIMAL2') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue is not null
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'exists' 
	and tblc.dataTypeCode = 'DECIMAL2'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'exists' and dataTypeCode = 'DECIMAL2'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, exists, date  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue is not null
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'exists' 
	and tblc.dataTypeCode = 'DATE'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'exists' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, exists, CONTENTOBJ  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='CONTENTOBJ') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.vw_memberData__content as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValueSiteResourceID is not null
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'exists' 
	and tblc.dataTypeCode = 'CONTENTOBJ'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'exists' and dataTypeCode = 'CONTENTOBJ'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, exists, DOCUMENTOBJ  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='DOCUMENTOBJ') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.vw_memberData__document as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.actualColumnValue is not null
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'exists' 
	and tblc.dataTypeCode = 'DOCUMENTOBJ'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'exists' and dataTypeCode = 'DOCUMENTOBJ'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, not_exists, int  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activeMemberID
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'not_exists' 
	and tblc.dataTypeCode = 'INTEGER'
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__integer as vw WITH(NOEXPAND)
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
		and vw.columnValue is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'not_exists' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************** */
/* md_xxxx, not_exists, decimal2  */
/* ********************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='DECIMAL2') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activeMemberID
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'not_exists' 
	and tblc.dataTypeCode = 'DECIMAL2'
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND)
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
		and vw.columnValue is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'not_exists' and dataTypeCode = 'DECIMAL2'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, not_exists, date  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activeMemberID
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'not_exists' 
	and tblc.dataTypeCode = 'DATE'
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__date as vw WITH(NOEXPAND)
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
		and vw.columnValue is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'not_exists' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, not_exists, bit  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='BIT') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activeMemberID
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'not_exists' 
	and tblc.dataTypeCode = 'BIT'
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__bit as vw WITH(NOEXPAND)
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
		and vw.columnValue is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'not_exists' and dataTypeCode = 'BIT'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, not_exists, CONTENTOBJ  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='CONTENTOBJ') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activeMemberID
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'not_exists' 
	and tblc.dataTypeCode = 'CONTENTOBJ'
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__content as vw WITH(NOEXPAND)
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
		and vw.columnValueSiteResourceID is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'not_exists' and dataTypeCode = 'CONTENTOBJ'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, not_exists, DOCUMENTOBJ  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='DOCUMENTOBJ') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activeMemberID
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'not_exists' 
	and tblc.dataTypeCode = 'DOCUMENTOBJ'
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__document as vw WITH(NOEXPAND)
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
		and vw.actualColumnValue is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'not_exists' and dataTypeCode = 'DOCUMENTOBJ'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* md_xxxx, contains, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='contains' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue like '%' + cv.conditionValue + '%'
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'contains' 
	and tblc.dataTypeCode = 'STRING'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'contains' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* md_xxxx, contains_regex, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='contains_regex' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		-- add back when we have a regexfind fn
		-- and dbo.fn_RegExFind(vw.columnValue,tblc.value) = 1
		and vw.columnValue like '%' + cv.conditionValue + '%'
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'contains_regex' 
	and tblc.dataTypeCode = 'STRING'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'contains_regex' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, datepart  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='datepart') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'eq'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,vw.columnValue) 
			when 'm' then datepart(m,vw.columnValue) 
			when 'wk' then datepart(wk,vw.columnValue)
			when 'dw' then datepart(dw,vw.columnValue)
			when 'd' then datepart(d,vw.columnValue) 
			when 'dy' then datepart(dy,vw.columnValue)
			else datepart(yy,vw.columnValue) end = cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'neq'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,vw.columnValue) 
			when 'm' then datepart(m,vw.columnValue) 
			when 'wk' then datepart(wk,vw.columnValue)
			when 'dw' then datepart(dw,vw.columnValue)
			when 'd' then datepart(d,vw.columnValue) 
			when 'dy' then datepart(dy,vw.columnValue)
			else datepart(yy,vw.columnValue) end <> cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'lt'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,vw.columnValue) 
			when 'm' then datepart(m,vw.columnValue) 
			when 'wk' then datepart(wk,vw.columnValue)
			when 'dw' then datepart(dw,vw.columnValue)
			when 'd' then datepart(d,vw.columnValue) 
			when 'dy' then datepart(dy,vw.columnValue)
			else datepart(yy,vw.columnValue) end < cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'lte'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,vw.columnValue) 
			when 'm' then datepart(m,vw.columnValue) 
			when 'wk' then datepart(wk,vw.columnValue)
			when 'dw' then datepart(dw,vw.columnValue)
			when 'd' then datepart(d,vw.columnValue) 
			when 'dy' then datepart(dy,vw.columnValue)
			else datepart(yy,vw.columnValue) end <= cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'gt'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,vw.columnValue) 
			when 'm' then datepart(m,vw.columnValue) 
			when 'wk' then datepart(wk,vw.columnValue)
			when 'dw' then datepart(dw,vw.columnValue)
			when 'd' then datepart(d,vw.columnValue) 
			when 'dy' then datepart(dy,vw.columnValue)
			else datepart(yy,vw.columnValue) end > cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'gte'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,vw.columnValue) 
			when 'm' then datepart(m,vw.columnValue) 
			when 'wk' then datepart(wk,vw.columnValue)
			when 'dw' then datepart(dw,vw.columnValue)
			when 'd' then datepart(d,vw.columnValue) 
			when 'dy' then datepart(dy,vw.columnValue)
			else datepart(yy,vw.columnValue) end >= cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = vw.memberid
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datepart' 

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'datepart'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* m_xxxx, eq, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='eq' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'STRING'
	and case tblc.fieldCode
		when 'm_firstname' then isnull(m.firstname,'')
		when 'm_middlename' then isnull(m.middlename,'')
		when 'm_lastname' then isnull(m.lastname,'')
		when 'm_suffix' then isnull(m.suffix,'')
		when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
		when 'm_company' then isnull(m.company,'')
		when 'm_membernumber' then isnull(m.membernumber,'')
		else isnull(m.prefix,'') end = cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'eq' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* m_xxxx, eq, integer  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='eq' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'INTEGER'
	and isnull(m.memberTypeID,0) = cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'eq' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* m_xxxx, neq, integer */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='neq' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'INTEGER'
	and isnull(m.memberTypeID,0) <> cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'neq' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* m_xxxx, neq, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='neq' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'STRING'
	and case tblc.fieldCode
		when 'm_firstname' then isnull(m.firstname,'')
		when 'm_middlename' then isnull(m.middlename,'')
		when 'm_lastname' then isnull(m.lastname,'')
		when 'm_suffix' then isnull(m.suffix,'')
		when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
		when 'm_company' then isnull(m.company,'')
		when 'm_membernumber' then isnull(m.membernumber,'')
		else isnull(m.prefix,'') end <> cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'neq' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* m_xxxx, lt  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='lt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'lt' 
	and case tblc.fieldCode
		when 'm_firstname' then isnull(m.firstname,'')
		when 'm_middlename' then isnull(m.middlename,'')
		when 'm_lastname' then isnull(m.lastname,'')
		when 'm_suffix' then isnull(m.suffix,'')
		when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
		when 'm_company' then isnull(m.company,'')
		when 'm_membernumber' then isnull(m.membernumber,'')
		else isnull(m.prefix,'') end < cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'lt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* m_xxxx, lte  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='lte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'lte' 
	and case tblc.fieldCode
		when 'm_firstname' then isnull(m.firstname,'')
		when 'm_middlename' then isnull(m.middlename,'')
		when 'm_lastname' then isnull(m.lastname,'')
		when 'm_suffix' then isnull(m.suffix,'')
		when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
		when 'm_company' then isnull(m.company,'')
		when 'm_membernumber' then isnull(m.membernumber,'')
		else isnull(m.prefix,'') end <= cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'lte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* m_xxxx, gt  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='gt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'gt' 
	and case tblc.fieldCode
		when 'm_firstname' then isnull(m.firstname,'')
		when 'm_middlename' then isnull(m.middlename,'')
		when 'm_lastname' then isnull(m.lastname,'')
		when 'm_suffix' then isnull(m.suffix,'')
		when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
		when 'm_company' then isnull(m.company,'')
		when 'm_membernumber' then isnull(m.membernumber,'')
		else isnull(m.prefix,'') end > cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'gt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* m_xxxx, gte  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='gte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'gte' 
	and case tblc.fieldCode
		when 'm_firstname' then isnull(m.firstname,'')
		when 'm_middlename' then isnull(m.middlename,'')
		when 'm_lastname' then isnull(m.lastname,'')
		when 'm_suffix' then isnull(m.suffix,'')
		when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
		when 'm_company' then isnull(m.company,'')
		when 'm_membernumber' then isnull(m.membernumber,'')
		else isnull(m.prefix,'') end >= cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'gte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* m_xxxx, exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'exists' 
	and case tblc.fieldCode
		when 'm_firstname' then nullif(m.firstname,'')
		when 'm_middlename' then nullif(m.middlename,'')
		when 'm_lastname' then nullif(m.lastname,'')
		when 'm_suffix' then nullif(m.suffix,'')
		when 'm_professionalsuffix' then nullif(m.professionalsuffix,'')
		when 'm_company' then nullif(m.company,'')
		when 'm_membernumber' then nullif(m.membernumber,'')
		else nullif(m.prefix,'') end is not null

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* m_xxxx, not_exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='not_exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'not_exists' 
	and case tblc.fieldCode
		when 'm_firstname' then nullif(m.firstname,'')
		when 'm_middlename' then nullif(m.middlename,'')
		when 'm_lastname' then nullif(m.lastname,'')
		when 'm_suffix' then nullif(m.suffix,'')
		when 'm_professionalsuffix' then nullif(m.professionalsuffix,'')
		when 'm_company' then nullif(m.company,'')
		when 'm_membernumber' then nullif(m.membernumber,'')
		else nullif(m.prefix,'') end is null

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'not_exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* m_xxxx, contains  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='contains') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'contains' 
	and case tblc.fieldCode
		when 'm_firstname' then isnull(m.firstname,'')
		when 'm_middlename' then isnull(m.middlename,'')
		when 'm_lastname' then isnull(m.lastname,'')
		when 'm_suffix' then isnull(m.suffix,'')
		when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
		when 'm_company' then isnull(m.company,'')
		when 'm_membernumber' then isnull(m.membernumber,'')
		else isnull(m.prefix,'') end like '%' + cv.conditionValue + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'contains'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* m_xxxx, contains_regex  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='contains_regex') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'contains_regex' 
	-- add back when we have a regexfind fn
	-- dbo.fn_RegExFind(vw.columnValue,tblc.value) = 1
	and case tblc.fieldCode
		when 'm_firstname' then isnull(m.firstname,'')
		when 'm_middlename' then isnull(m.middlename,'')
		when 'm_lastname' then isnull(m.lastname,'')
		when 'm_suffix' then isnull(m.suffix,'')
		when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
		when 'm_company' then isnull(m.company,'')
		when 'm_membernumber' then isnull(m.membernumber,'')
		else isnull(m.prefix,'') end like '%' + cv.conditionValue + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'contains_regex'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, eq  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='eq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	inner join dbo.ams_memberWebsites as mw WITH(NOLOCK) on mw.memberid = m.memberid
		and mw.websiteTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'eq' 
	and isnull(mw.website,'') = cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'eq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, neq  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='neq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'neq' 
	and not exists (
		select mw.memberID
		from dbo.ams_memberWebsites as mw
		inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
			and isnull(mw.website,'') = cv.conditionValue
		where mw.websiteTypeID = tblc.fieldCodeAreaID
		and mw.memberID = m.memberid
	)	

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'neq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, lt  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='lt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	inner join dbo.ams_memberWebsites as mw WITH(NOLOCK) on mw.memberid = m.memberid
		and mw.websiteTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'lt' 
	and isnull(mw.website,'') < cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'lt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, lte  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='lte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	inner join dbo.ams_memberWebsites as mw WITH(NOLOCK) on mw.memberid = m.memberid
		and mw.websiteTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'lte' 
	and isnull(mw.website,'') <= cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'lte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, gt  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='gt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	inner join dbo.ams_memberWebsites as mw WITH(NOLOCK) on mw.memberid = m.memberid
		and mw.websiteTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'gt' 
	and isnull(mw.website,'') > cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'gt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, gte  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='gte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	inner join dbo.ams_memberWebsites as mw WITH(NOLOCK) on mw.memberid = m.memberid
		and mw.websiteTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'gte' 
	and isnull(mw.website,'') >= cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'gte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	inner join dbo.ams_memberWebsites as mw WITH(NOLOCK) on mw.memberid = m.memberid
		and mw.websiteTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'exists' 
	and nullif(mw.website,'') is not null

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, not_exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='not_exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'not_exists' 
	and not exists (
		select mw.memberID
		from dbo.ams_memberWebsites as mw
		where mw.websiteTypeID = tblc.fieldCodeAreaID
		and mw.memberID = m.memberid
		and nullif(mw.website,'') is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'not_exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, contains  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='contains') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	inner join dbo.ams_memberWebsites as mw WITH(NOLOCK) on mw.memberid = m.memberid
		and mw.websiteTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'contains' 
	and isnull(mw.website,'') like '%' + cv.conditionValue + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'contains'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, contains_regex  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='contains_regex') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	inner join dbo.ams_memberWebsites as mw WITH(NOLOCK) on mw.memberid = m.memberid
		and mw.websiteTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'contains_regex' 
	-- add back when we have a regexfind fn
	-- dbo.fn_RegExFind(mw.website,c.value) = 1
	and isnull(mw.website,'') like '%' + cv.conditionValue + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'contains_regex'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, eq  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='eq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	inner join dbo.ams_memberEmails as me WITH(NOLOCK) on me.memberid = m.memberid
		and me.emailTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'eq' 
	and isnull(me.email,'') = cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'eq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, neq  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='neq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'neq' 
	and not exists (
		select me.memberID
		from dbo.ams_memberEmails as me WITH(NOLOCK)
		inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
			and isnull(me.email,'') = cv.conditionValue
		where me.emailTypeID = tblc.fieldCodeAreaID
		and me.memberID = m.memberid
	)	

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'neq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, lt  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='lt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	inner join dbo.ams_memberEmails as me WITH(NOLOCK) on me.memberid = m.memberid
		and me.emailTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'lt' 
	and isnull(me.email,'') < cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'lt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, lte  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='lte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	inner join dbo.ams_memberEmails as me WITH(NOLOCK) on me.memberid = m.memberid
		and me.emailTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'lte' 
	and isnull(me.email,'') <= cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'lte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, gt  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='gt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	inner join dbo.ams_memberEmails as me WITH(NOLOCK) on me.memberid = m.memberid
		and me.emailTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'gt' 
	and isnull(me.email,'') > cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'gt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, gte  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='gte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	inner join dbo.ams_memberEmails as me WITH(NOLOCK) on me.memberid = m.memberid
		and me.emailTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'gte' 
	and isnull(me.email,'') >= cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'gte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	inner join dbo.ams_memberEmails as me WITH(NOLOCK) on me.memberid = m.memberid
		and me.emailTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'exists' 
	and nullif(me.email,'') is not null

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, not_exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='not_exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'not_exists' 
	and not exists (
		select me.memberID
		from dbo.ams_memberEmails as me WITH(NOLOCK)
		where me.emailTypeID = tblc.fieldCodeAreaID
		and me.memberID = m.memberid
		and nullif(me.email,'') is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'not_exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, contains  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='contains') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	inner join dbo.ams_memberEmails as me WITH(NOLOCK) on me.memberid = m.memberid
		and me.emailTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'contains' 
	and isnull(me.email,'') like '%' + cv.conditionValue + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'contains'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, contains_regex  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='contains_regex') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	inner join dbo.ams_memberEmails as me WITH(NOLOCK) on me.memberid = m.memberid
		and me.emailTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'contains_regex' 
	-- add back when we have a regexfind fn
	-- dbo.fn_RegExFind(me.email,tblc.value) = 1
	and isnull(me.email,'') like '%' + cv.conditionValue + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'contains_regex'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, eq  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='eq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	inner join dbo.ams_memberAddresses as ma WITH(NOLOCK) on ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join dbo.ams_memberPhones as mp WITH(NOLOCK) on mp.addressid = ma.addressID
		and mp.phoneTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'eq' 
	and isnull(mp.phone,'') = cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'eq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, neq  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='neq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'neq' 
	and not exists (
		select ma.memberid
		from dbo.ams_memberAddresses as ma WITH(NOLOCK) 
		inner join dbo.ams_memberPhones as mp WITH(NOLOCK) on mp.addressid = ma.addressID
			and mp.phoneTypeID = tblc.fieldCodeAreaID
		inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
			and isnull(mp.phone,'') = cv.conditionValue
		where ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaPartA
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'neq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, lt  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='lt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	inner join dbo.ams_memberAddresses as ma WITH(NOLOCK) on ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join dbo.ams_memberPhones as mp WITH(NOLOCK) on mp.addressid = ma.addressID
		and mp.phoneTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'lt' 
	and isnull(mp.phone,'') < cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'lt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, lte  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='lte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	inner join dbo.ams_memberAddresses as ma WITH(NOLOCK) on ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join dbo.ams_memberPhones as mp WITH(NOLOCK) on mp.addressid = ma.addressID
		and mp.phoneTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'lte' 
	and isnull(mp.phone,'') <= cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'lte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, gt  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='gt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	inner join dbo.ams_memberAddresses as ma WITH(NOLOCK) on ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join dbo.ams_memberPhones as mp WITH(NOLOCK) on mp.addressid = ma.addressID
		and mp.phoneTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'gt' 
	and isnull(mp.phone,'') > cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'gt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, gte  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='gte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	inner join dbo.ams_memberAddresses as ma WITH(NOLOCK) on ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join dbo.ams_memberPhones as mp WITH(NOLOCK) on mp.addressid = ma.addressID
		and mp.phoneTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'gte' 
	and isnull(mp.phone,'') >= cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'gte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	inner join dbo.ams_memberAddresses as ma WITH(NOLOCK) on ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join dbo.ams_memberPhones as mp WITH(NOLOCK) on mp.addressid = ma.addressID
		and mp.phoneTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'exists' 
	and nullif(mp.phone,'') is not null

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, not_exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='not_exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'not_exists' 
	and not exists (
		select ma.memberid
		from dbo.ams_memberAddresses as ma WITH(NOLOCK) 
		inner join dbo.ams_memberPhones as mp WITH(NOLOCK) on mp.addressid = ma.addressID
			and mp.phoneTypeID = tblc.fieldCodeAreaID
		where ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaPartA
		and nullif(mp.phone,'') is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'not_exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, contains  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='contains') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	inner join dbo.ams_memberAddresses as ma WITH(NOLOCK) on ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join dbo.ams_memberPhones as mp WITH(NOLOCK) on mp.addressid = ma.addressID
		and mp.phoneTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'contains' 
	and isnull(mp.phone,'') like '%' + cv.conditionValue + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'contains'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, contains_regex  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='contains_regex') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	inner join dbo.ams_memberAddresses as ma WITH(NOLOCK) on ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join dbo.ams_memberPhones as mp WITH(NOLOCK) on mp.addressid = ma.addressID
		and mp.phoneTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'contains_regex' 
	-- add back when we have a regexfind fn
	-- dbo.fn_RegExFind(mp.phone,tblc.value) = 1
	and isnull(mp.phone,'') like '%' + cv.conditionValue + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'contains_regex'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************ */
/* ma_xxxx, neq  */
/* ************ */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='neq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'neq' 
	and tblc.displayTypeCode not in ('RADIO','SELECT')
	and not exists (
		select ma.memberid
		from dbo.ams_memberAddresses as ma WITH(NOLOCK)
		inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
		where ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaID
		and tblc.displayTypeCode not in ('RADIO','SELECT')
		and case tblc.fieldCodeAreaPartA
			when 'address1' then isnull(ma.address1,'') 
			when 'address2' then isnull(ma.address2,'') 
			when 'address3' then isnull(ma.address3,'') 
			when 'city' then isnull(ma.city,'') 
			when 'postalcode' then isnull(ma.postalcode,'') 
			when 'county' then isnull(ma.county,'') 
			else isnull(ma.attn,'') end = cv.conditionValue
	)

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'neq' 
	and tblc.displayTypeCode in ('RADIO','SELECT')
	and not exists (
		select ma.memberid
		from dbo.ams_memberAddresses as ma WITH(NOLOCK)
		inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
		where ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaID
		and tblc.displayTypeCode in ('RADIO','SELECT')
		and case tblc.fieldCodeAreaPartA
			when 'stateprov' then isnull(ma.stateid,0) 
			else isnull(ma.countryid,0) end = cv.conditionValue
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'neq'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************ */
/* ma_xxxx, lt  */
/* ************ */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='lt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma WITH(NOLOCK) on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = ma.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'lt' 
	and case tblc.fieldCodeAreaPartA
		when 'address1' then isnull(ma.address1,'') 
		when 'address2' then isnull(ma.address2,'') 
		when 'address3' then isnull(ma.address3,'') 
		when 'city' then isnull(ma.city,'') 
		when 'postalcode' then isnull(ma.postalcode,'') 
		when 'county' then isnull(ma.county,'') 
		else isnull(ma.attn,'') end < cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'lt'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************ */
/* ma_xxxx, lte  */
/* ************ */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='lte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma WITH(NOLOCK) on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = ma.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'lte' 
	and case tblc.fieldCodeAreaPartA
		when 'address1' then isnull(ma.address1,'') 
		when 'address2' then isnull(ma.address2,'') 
		when 'address3' then isnull(ma.address3,'') 
		when 'city' then isnull(ma.city,'') 
		when 'postalcode' then isnull(ma.postalcode,'') 
		when 'county' then isnull(ma.county,'') 
		else isnull(ma.attn,'') end <= cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'lte'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************ */
/* ma_xxxx, gt  */
/* ************ */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='gt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma WITH(NOLOCK) on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = ma.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'gt' 
	and case tblc.fieldCodeAreaPartA
		when 'address1' then isnull(ma.address1,'') 
		when 'address2' then isnull(ma.address2,'') 
		when 'address3' then isnull(ma.address3,'') 
		when 'city' then isnull(ma.city,'') 
		when 'postalcode' then isnull(ma.postalcode,'') 
		when 'county' then isnull(ma.county,'') 
		else isnull(ma.attn,'') end > cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'gt'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************ */
/* ma_xxxx, gte  */
/* ************ */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='gte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma WITH(NOLOCK) on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = ma.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'gte' 
	and case tblc.fieldCodeAreaPartA
		when 'address1' then isnull(ma.address1,'') 
		when 'address2' then isnull(ma.address2,'') 
		when 'address3' then isnull(ma.address3,'') 
		when 'city' then isnull(ma.city,'') 
		when 'postalcode' then isnull(ma.postalcode,'') 
		when 'county' then isnull(ma.county,'') 
		else isnull(ma.attn,'') end >= cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'gte'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************ */
/* ma_xxxx, exists  */
/* ************ */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_memberAddresses as ma WITH(NOLOCK) on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = ma.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'exists' 
	and tblc.displayTypeCode not in ('RADIO','SELECT')
	and case tblc.fieldCodeAreaPartA
		when 'address1' then nullif(ma.address1,'') 
		when 'address2' then nullif(ma.address2,'') 
		when 'address3' then nullif(ma.address3,'') 
		when 'city' then nullif(ma.city,'') 
		when 'postalcode' then nullif(ma.postalcode,'') 
		when 'county' then nullif(ma.county,'') 
		else nullif(ma.attn,'') end is not null

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_memberAddresses as ma WITH(NOLOCK) on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = ma.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'exists' 
	and tblc.displayTypeCode in ('RADIO','SELECT')
	and case tblc.fieldCodeAreaPartA
		when 'stateprov' then ma.stateid
		else ma.countryid end is not null

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'exists'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* ma_xxxx, not_exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='not_exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'not_exists' 
	and tblc.displayTypeCode not in ('RADIO','SELECT')
	and not exists (
		select ma.memberid
		from dbo.ams_memberAddresses as ma WITH(NOLOCK)
		where ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaID
		and tblc.displayTypeCode not in ('RADIO','SELECT')
		and case tblc.fieldCodeAreaPartA
			when 'address1' then nullif(ma.address1,'') 
			when 'address2' then nullif(ma.address2,'') 
			when 'address3' then nullif(ma.address3,'') 
			when 'city' then nullif(ma.city,'') 
			when 'postalcode' then nullif(ma.postalcode,'') 
			when 'county' then nullif(ma.county,'') 
			else nullif(ma.attn,'') end is not null
	)

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'not_exists' 
	and tblc.displayTypeCode in ('RADIO','SELECT')
	and not exists (
		select ma.memberid
		from dbo.ams_memberAddresses as ma WITH(NOLOCK)
		where ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaID
		and tblc.displayTypeCode in ('RADIO','SELECT')
		and case tblc.fieldCodeAreaPartA
			when 'stateprov' then ma.stateid
			else ma.countryid end is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'not_exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************ */
/* ma_xxxx, contains  */
/* ************ */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='contains') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma WITH(NOLOCK) on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = ma.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'contains' 
	and case tblc.fieldCodeAreaPartA
		when 'address1' then isnull(ma.address1,'') 
		when 'address2' then isnull(ma.address2,'') 
		when 'address3' then isnull(ma.address3,'') 
		when 'city' then isnull(ma.city,'') 
		when 'postalcode' then isnull(ma.postalcode,'') 
		when 'county' then isnull(ma.county,'') 
		else isnull(ma.attn,'') end like '%' + cv.conditionValue + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'contains'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************ */
/* ma_xxxx, contains_regex  */
/* ************ */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='contains_regex') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma WITH(NOLOCK) on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = ma.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'contains_regex' 
	and case tblc.fieldCodeAreaPartA
		when 'address1' then isnull(ma.address1,'') 
		when 'address2' then isnull(ma.address2,'') 
		when 'address3' then isnull(ma.address3,'') 
		when 'city' then isnull(ma.city,'') 
		when 'postalcode' then isnull(ma.postalcode,'') 
		when 'county' then isnull(ma.county,'') 
		else isnull(ma.attn,'') end like '%' + cv.conditionValue + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'contains_regex'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, neq, string  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='neq' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'STRING'
	and not exists (
		select mpl.memberid
		from dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK)
		inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
		left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls WITH(NOLOCK) on mpls.PLStatusID = mpl.PLStatusID
		where mpl.PLTypeID = tblc.fieldCodeAreaID
		and mpl.memberid = m.memberid
		and case tblc.fieldCodeAreaPartA
			when 'status' then isnull(mpls.statusName,'') 
			else isnull(mpl.licensenumber,'') end = cv.conditionValue
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'neq' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, lt, string  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='lt' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
	left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls WITH(NOLOCK) on mpls.PLStatusID = mpl.PLStatusID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'lt' 
	and tblc.dataTypeCode = 'STRING'
	and case tblc.fieldCodeAreaPartA
		when 'status' then isnull(mpls.statusName,'') 
		else isnull(mpl.licensenumber,'') end < cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'lt' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, lte, string  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='lte' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
	left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls WITH(NOLOCK) on mpls.PLStatusID = mpl.PLStatusID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'lte' 
	and tblc.dataTypeCode = 'STRING'
	and case tblc.fieldCodeAreaPartA
		when 'status' then isnull(mpls.statusName,'') 
		else isnull(mpl.licensenumber,'') end <= cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'lte' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, gt, string  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='gt' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
	left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls WITH(NOLOCK) on mpls.PLStatusID = mpl.PLStatusID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'gt' 
	and tblc.dataTypeCode = 'STRING'
	and case tblc.fieldCodeAreaPartA
		when 'status' then isnull(mpls.statusName,'') 
		else isnull(mpl.licensenumber,'') end > cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'gt' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, gte, string  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='gte' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
	left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls WITH(NOLOCK) on mpls.PLStatusID = mpl.PLStatusID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'gte' 
	and tblc.dataTypeCode = 'STRING'
	and case tblc.fieldCodeAreaPartA
		when 'status' then isnull(mpls.statusName,'') 
		else isnull(mpl.licensenumber,'') end >= cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'gte' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, not_exists, string  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='not_exists' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'not_exists' 
	and tblc.dataTypeCode = 'STRING'
	and not exists (
		select mpl.memberid
		from dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) 
		where mpl.PLTypeID = tblc.fieldCodeAreaID
		and mpl.memberid = m.memberid
		and case tblc.fieldCodeAreaPartA
			when 'status' then mpl.PLStatusID
			else nullif(mpl.licensenumber,'') end is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'not_exists' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, contains  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='contains') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
	left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls WITH(NOLOCK) on mpls.PLStatusID = mpl.PLStatusID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'contains' 
	and case tblc.fieldCodeAreaPartA
		when 'status' then isnull(mpls.statusName,'') 
		else isnull(mpl.licensenumber,'') end like '%' + cv.conditionValue + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'contains'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, contains_regex  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='contains_regex') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
	left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls WITH(NOLOCK) on mpls.PLStatusID = mpl.PLStatusID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'contains_regex' 
	and case tblc.fieldCodeAreaPartA
		when 'status' then isnull(mpls.statusName,'') 
		else isnull(mpl.licensenumber,'') end like '%' + cv.conditionValue + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'contains_regex'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, eq, date  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='eq' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'DATE'
	and mpl.activeDate = cast(cv.conditionValue as datetime)

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'eq' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, neq, date  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='neq' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'DATE'
	and not exists (
		select mpl.memberid
		from dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) 
		inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
			and mpl.activeDate = cast(cv.conditionValue as datetime)
		where mpl.PLTypeID = tblc.fieldCodeAreaID
		and mpl.memberid = m.memberid
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'neq' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, lt, date  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='lt' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'lt' 
	and tblc.dataTypeCode = 'DATE'
	and mpl.activeDate < cast(cv.conditionValue as datetime)

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'lt' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, lte, date  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='lte' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'lte' 
	and tblc.dataTypeCode = 'DATE'
	and mpl.activeDate <= cast(cv.conditionValue as datetime)

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'lte' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, gt, date  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='gt' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'gt' 
	and tblc.dataTypeCode = 'DATE'
	and mpl.activeDate > cast(cv.conditionValue as datetime)

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'gt' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, gte, date  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='gte' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'gte' 
	and tblc.dataTypeCode = 'DATE'
	and mpl.activeDate >= cast(cv.conditionValue as datetime)

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'gte' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, exists, date  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='exists' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'exists' 
	and tblc.dataTypeCode = 'DATE'
	and mpl.activeDate is not null

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'exists' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, not_exists, date  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='not_exists' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'not_exists' 
	and tblc.dataTypeCode = 'DATE'
	and not exists (
		select mpl.memberid
		from dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) 
		where mpl.PLTypeID = tblc.fieldCodeAreaID
		and mpl.memberid = m.memberid
		and mpl.activeDate is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'not_exists' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* mpl_xxxx, datepart  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='datepart') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'eq'
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,mpl.activeDate) 
			when 'm' then datepart(m,mpl.activeDate) 
			when 'wk' then datepart(wk,mpl.activeDate)
			when 'dw' then datepart(dw,mpl.activeDate)
			when 'd' then datepart(d,mpl.activeDate) 
			when 'dy' then datepart(dy,mpl.activeDate)
			else datepart(yy,mpl.activeDate) end = cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'neq'
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,mpl.activeDate) 
			when 'm' then datepart(m,mpl.activeDate) 
			when 'wk' then datepart(wk,mpl.activeDate)
			when 'dw' then datepart(dw,mpl.activeDate)
			when 'd' then datepart(d,mpl.activeDate) 
			when 'dy' then datepart(dy,mpl.activeDate)
			else datepart(yy,mpl.activeDate) end <> cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'lt'
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,mpl.activeDate) 
			when 'm' then datepart(m,mpl.activeDate) 
			when 'wk' then datepart(wk,mpl.activeDate)
			when 'dw' then datepart(dw,mpl.activeDate)
			when 'd' then datepart(d,mpl.activeDate) 
			when 'dy' then datepart(dy,mpl.activeDate)
			else datepart(yy,mpl.activeDate) end < cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'lte'
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,mpl.activeDate) 
			when 'm' then datepart(m,mpl.activeDate) 
			when 'wk' then datepart(wk,mpl.activeDate)
			when 'dw' then datepart(dw,mpl.activeDate)
			when 'd' then datepart(d,mpl.activeDate) 
			when 'dy' then datepart(dy,mpl.activeDate)
			else datepart(yy,mpl.activeDate) end <= cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'gt'
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,mpl.activeDate) 
			when 'm' then datepart(m,mpl.activeDate) 
			when 'wk' then datepart(wk,mpl.activeDate)
			when 'dw' then datepart(dw,mpl.activeDate)
			when 'd' then datepart(d,mpl.activeDate) 
			when 'dy' then datepart(dy,mpl.activeDate)
			else datepart(yy,mpl.activeDate) end > cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c WITH(NOLOCK) on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e WITH(NOLOCK) on e.expressionID = c.dateexpressionID and e.expression = 'gte'
	inner join dbo.ams_memberProfessionalLicenses as mpl WITH(NOLOCK) on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,mpl.activeDate) 
			when 'm' then datepart(m,mpl.activeDate) 
			when 'wk' then datepart(wk,mpl.activeDate)
			when 'dw' then datepart(dw,mpl.activeDate)
			when 'd' then datepart(d,mpl.activeDate) 
			when 'dy' then datepart(dy,mpl.activeDate)
			else datepart(yy,mpl.activeDate) end >= cv.conditionValue
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberid = mpl.memberid
		and m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datepart' 

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'datepart'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* e_xxxx, attended  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Events' and expression='attended') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ev_events as e WITH(NOLOCK) on e.eventID = tblc.fieldCodeAreaID
	inner join dbo.ev_registration as er WITH(NOLOCK) on er.eventID = e.eventID and er.status = 'A'
	inner join dbo.ev_registrants as reg WITH(NOLOCK) on reg.registrationID = er.registrationID and reg.status = 'A'
		and reg.attended = 1
	inner join dbo.ams_members as m2 WITH(NOLOCK) on m2.memberid = reg.memberid
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberID = m2.activeMemberID
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.activememberid = isnull(@memberid,m.activememberid)
	where tblc.fieldCodeArea = 'Events' 
	and tblc.expression = 'attended' 

	delete from #tblCondALL 
	where fieldCodeArea = 'Events' and expression = 'attended'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* e_xxxx, awarded  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Events' and expression='awarded') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ev_events as e WITH(NOLOCK) on e.eventID = tblc.fieldCodeAreaID
	inner join dbo.ev_registration as er WITH(NOLOCK) on er.eventID = e.eventID and er.status = 'A'
	inner join dbo.ev_registrants as reg WITH(NOLOCK) on reg.registrationID = er.registrationID and reg.status = 'A'
		and reg.attended = 1
	inner join dbo.crd_requests as rc WITH(NOLOCK) on rc.registrantID = reg.registrantID
		and rc.creditAwarded = 1
	inner join dbo.ams_members as m2 WITH(NOLOCK) on m2.memberid = reg.memberid
	inner join dbo.ams_members as m WITH(NOLOCK) on m.memberID = m2.activeMemberID
		and m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.activememberid = isnull(@memberid,m.activememberid)
	where tblc.fieldCodeArea = 'Events' 
	and tblc.expression = 'awarded' 

	delete from #tblCondALL 
	where fieldCodeArea = 'Events' and expression = 'awarded'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************* */
/* mad_xxxx, eq  */
/* ************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Districting' and expression='eq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	inner join dbo.ams_memberAddresses as ma WITH(NOLOCK) on ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join dbo.ams_memberAddressData as mad WITH(NOLOCK) on mad.addressid = ma.addressID
	inner join dbo.ams_memberDistrictValues as mdv WITH(NOLOCK) on mdv.valueID = mad.valueID
		and mdv.districtTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Districting' 
	and tblc.expression = 'eq' 
	and mdv.valueID = cv.conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Districting' and expression = 'eq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************** */
/* mad_xxxx, neq  */
/* ************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Districting' and expression='neq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Districting' 
	and tblc.expression = 'neq' 
	and not exists (
		select ma.memberid
		from dbo.ams_memberAddresses as ma
		inner join dbo.ams_virtualGroupConditionValues as cv WITH(NOLOCK) on cv.conditionID = tblc.conditionID
		inner join dbo.ams_memberAddressData as mad WITH(NOLOCK) on mad.addressid = ma.addressID
		inner join dbo.ams_memberDistrictValues as mdv WITH(NOLOCK) on mdv.valueID = mad.valueID
			and mdv.districtTypeID = tblc.fieldCodeAreaID
			and mdv.valueID = cv.conditionValue
		where ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaPartA
	)
	
	delete from #tblCondALL 
	where fieldCodeArea = 'Districting' and expression = 'neq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mad_xxxx, exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Districting' and expression='exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	inner join dbo.ams_memberAddresses as ma on ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join dbo.ams_memberAddressData as mad on mad.addressid = ma.addressID
	inner join dbo.ams_memberDistrictValues as mdv on mdv.valueID = mad.valueID
		and mdv.districtTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Districting' 
	and tblc.expression = 'exists' 

	delete from #tblCondALL 
	where fieldCodeArea = 'Districting' and expression = 'exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mad_xxxx, not_exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Districting' and expression='not_exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
		and m.status <> 'D'
		and m.memberid = m.activememberid
		and m.memberid = isnull(@memberid,m.memberid)
	where tblc.fieldCodeArea = 'Districting' 
	and tblc.expression = 'not_exists' 
	and not exists (
		select ma.memberid
		from dbo.ams_memberAddresses as ma
		inner join dbo.ams_memberAddressData as mad on mad.addressid = ma.addressID
		inner join dbo.ams_memberDistrictValues as mdv on mdv.valueID = mad.valueID
			and mdv.districtTypeID = tblc.fieldCodeAreaID
		where ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaPartA
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Districting' and expression = 'not_exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

donecalc:
	select @finalCount = Count(*) from #cache_members_conditions_shouldbe
	print Cast(@finalCount as varchar(10)) + ' records identified for cache.'

	-- delete member/conditions that should not be there
	delete cmc
	from dbo.cache_members_conditions as cmc
	inner join #tblCondALLFinal as caf on caf.conditionID = cmc.conditionID
	where cmc.memberid = isnull(@memberid,cmc.memberid)
	and not exists (
		select *
		from #cache_members_conditions_shouldbe
		where conditionID = cmc.conditionID
		and memberid = cmc.memberid
	)	
		print Cast(@@rowcount as varchar(10)) + ' records removed from cache.'

	-- insert member/conditions that should be but arent already there
	insert into dbo.cache_members_conditions (memberID, conditionID)
	select distinct cache.memberID, cache.conditionID
	from #cache_members_conditions_shouldbe as cache
	where not exists (
		select *
		from dbo.cache_members_conditions
		where memberid = cache.memberid
		and conditionID = cache.conditionID
	)
		print Cast(@@rowcount as varchar(10)) + ' records added to cache.'

	IF OBJECT_ID('tempdb..#cache_members_conditions_shouldbe') IS NOT NULL
		DROP TABLE #cache_members_conditions_shouldbe
	IF OBJECT_ID('tempdb..#tblCondALL') IS NOT NULL
		DROP TABLE #tblCondALL
	IF OBJECT_ID('tempdb..#tblCondALLFinal') IS NOT NULL
		DROP TABLE #tblCondALLFinal
	IF OBJECT_ID('tempdb..#tblFCSplit') IS NOT NULL
		DROP TABLE #tblFCSplit

	select @endtime = getdate()
	print cast(Datediff(ms,@starttime,@endTime) as varchar(10)) + ' milliseconds to run'

	set nocount off
GO


ALTER PROC [dbo].[cache_members_processQueue]
@orgID int,
@limitToMemberID int

AS

declare @memberid int, @conditionid int, @queueid bigint, @batchID uniqueidentifier, @batchExpirationInMinutes int

set @batchID=newid()
set @batchExpirationInMinutes = -15

-- if entry for orgid and nothing else, process whole org. no need to do individual ones
-- ignore ruleID (treat it like a whole org refresh for now)
if @limitToMemberID is null and exists (
	select queueid
	from dbo.queue_processMemberGroups
	where orgID = @orgID
	and memberid is null
	and conditionid is null
	and (batchID is null or batchStartDate < dateadd(minute,@batchExpirationInMinutes,getdate()))
) BEGIN
	update dbo.queue_processMemberGroups 
	set batchID = @batchID,
		batchStartDate = getdate()
	where orgID = @orgID
	and (batchID is null or batchStartDate < dateadd(minute,@batchExpirationInMinutes,getdate()))

	exec dbo.cache_members_populateConditionCache @orgID=@orgID, @conditionIDList=null, @memberID=null

	delete from dbo.queue_processMemberGroups
	where orgID = @orgID
	and batchID = @batchID
END

-- if entry for orgid and member, process these and delete any finer items.
select @memberid = min(memberid)
	from dbo.queue_processMemberGroups
	where orgID = @orgID
	and memberid = isnull(@limitToMemberID,memberID)
	and conditionid is null
	and (batchID is null or batchStartDate < dateadd(minute,@batchExpirationInMinutes,getdate()))
while @memberid is not null BEGIN
	update dbo.queue_processMemberGroups 
	set	batchID = @batchID,
		batchStartDate = getdate()
	where orgID = @orgID
	and memberid = @memberid
	and (batchID is null or batchStartDate < dateadd(minute,@batchExpirationInMinutes,getdate()))

	exec dbo.cache_members_populateConditionCache @orgID=@orgID, @conditionIDList=null, @memberID=@memberid

	if not exists (
		select queueid
		from dbo.queue_processMemberGroups
		where orgID = @orgID
		and memberid is null
		and conditionid is null
		and batchID is not null
		and batchStartDate >= dateadd(minute,@batchExpirationInMinutes,getdate())
		and batchID <> @batchID
	)
	BEGIN
		delete from dbo.queue_processMemberGroups
		where orgID = @orgID
		and memberid = @memberid
		and batchID = @batchID
	END

	update dbo.queue_processMemberGroups
	set batchID = null,
		batchStartDate = null
	where orgID = @orgID
	and memberid = @memberid
	and batchID = @batchID

	select @memberid = min(memberid)
		from dbo.queue_processMemberGroups
		where orgID = @orgID
		and memberid = isnull(@limitToMemberID,memberID)
		and conditionid is null
		and memberid > @memberid
		and (batchID is null or batchStartDate < dateadd(minute,@batchExpirationInMinutes,getdate()))
END

-- if entry for orgid and condition, process these and delete any finer items.
if @limitToMemberID is null
BEGIN
	select @conditionid = min(conditionID)
		from dbo.queue_processMemberGroups
		where orgID = @orgID
		and conditionid is not null
		and memberid is null
		and (batchID is null or batchStartDate < dateadd(minute,@batchExpirationInMinutes,getdate()))
	while @conditionid is not null BEGIN
		update dbo.queue_processMemberGroups 
		set	batchID = @batchID,
			batchStartDate = getdate()
		where orgID = @orgID
		and conditionid = @conditionid
		and (batchID is null or batchStartDate < dateadd(minute,@batchExpirationInMinutes,getdate()))

		exec dbo.cache_members_populateConditionCache @orgID=@orgID, @conditionIDList=@conditionid, @memberID=null

		if not exists (
			select queueid
			from dbo.queue_processMemberGroups
			where orgID = @orgID
			and conditionid = @conditionID
			and batchID is not null
			and batchStartDate >= dateadd(minute,@batchExpirationInMinutes,getdate())
			and batchID <> @batchID
		)
		BEGIN
			delete from dbo.queue_processMemberGroups
			where orgID = @orgID
			and conditionID = @conditionID
			and batchID = @batchID
		END

		update dbo.queue_processMemberGroups
		set batchID = null,
			batchStartDate = null
		where orgID = @orgID
		and conditionid = @conditionID
		and batchID = @batchID

		select @conditionID = min(conditionID)
			from dbo.queue_processMemberGroups
			where orgID = @orgID
			and conditionid is not null
			and memberid is null
			and (batchID is null or batchStartDate < dateadd(minute,@batchExpirationInMinutes,getdate()))
			and conditionID > @conditionID
	END
END

-- if entry for orgid and member and conditionid, process these.
select @queueid = min(queueid)
	from dbo.queue_processMemberGroups
	where orgID = @orgID
	and memberid = isnull(@limitToMemberID,memberID)
	and conditionID is not null
	and (batchID is null or batchStartDate < dateadd(minute,@batchExpirationInMinutes,getdate()))
while @queueid is not null BEGIN
	select @conditionID = conditionID, @memberid = memberid
		from dbo.queue_processMemberGroups 
		where queueID = @queueid

	if @conditionID is not null
	BEGIN
		update dbo.queue_processMemberGroups 
		set	batchID = @batchID,
			batchStartDate = getdate()
		where orgID = @orgID
		and memberid = @memberid
		and conditionid = @conditionid
		and (batchID is null or batchStartDate < dateadd(minute,@batchExpirationInMinutes,getdate()))

		exec dbo.cache_members_populateConditionCache @orgID=@orgID, @conditionIDList=@conditionid, @memberID=@memberid

		if not exists (
			select queueid
			from dbo.queue_processMemberGroups
			where orgID = @orgID
			and batchID is not null
			and batchStartDate >= dateadd(minute,@batchExpirationInMinutes,getdate())
			and batchID <> @batchID
		)
		BEGIN
			delete from dbo.queue_processMemberGroups
			where orgID = @orgID
			and conditionID = @conditionID
			and memberid = @memberid
			and batchID = @batchID
		END

		update dbo.queue_processMemberGroups
		set batchID = null,
			batchStartDate = null
		where orgID = @orgID
		and memberid = @memberid
		and conditionid = @conditionID
		and batchID = @batchID
	END

	select @queueid = min(queueid)
		from dbo.queue_processMemberGroups
		where orgID = @orgID
		and memberid = isnull(@limitToMemberID,memberID)
		and conditionID is not null
		and queueid > @queueid
		and (batchID is null or batchStartDate < dateadd(minute,@batchExpirationInMinutes,getdate()))
END

RETURN 0
GO

