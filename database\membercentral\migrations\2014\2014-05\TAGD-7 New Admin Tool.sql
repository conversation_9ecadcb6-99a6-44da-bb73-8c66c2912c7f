DECLARE	@siteID int, @orgID int, @toolTypeID int, @toolResourceTypeID int, @rc int, @level2NavigationID int, @level3NavigationID int, @resourceTypeFunctionID int

select @siteID = dbo.fn_getSiteIDfromSiteCode('tagd')
select @orgID = dbo.fn_getOrgIDfromOrgCode('tagd')
select @level2NavigationID = pNav.navigationID
	from dbo.admin_navigation as pNav
	inner join dbo.admin_navigation as pNav2 on pNav2.navigationID = pNav.parentNavigationID
	where pNav.navName = 'Subscriptions'
	and pNav.navAreaID = 2
	and pNav2.navName = 'Members'
select @toolTypeID = null, @toolResourceTypeID = null

BEGIN TRAN
	EXEC @rc = dbo.createAdminToolType 
		@toolType='TAGDNationalImport',
		@toolCFC='custom.tagd.tagd.TAGDNationalImportAdmin',
		@toolDesc='TAGD National Membership Import',
		@toolTypeID=@toolTypeID OUTPUT,
		@resourceTypeID=@toolResourceTypeID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	insert into admin_siteToolRestrictions (toolTypeID, siteID) values (@toolTypeID, @siteID)
		IF @@ERROR <> 0 GOTO on_error

	EXEC @rc = dbo.createAdminNavigation
		@navName='Import National Files',
		@navDesc='Import National Files',
		@parentNavigationID=@level2NavigationID,
		@navAreaID=3,
		@cfcMethod='showForm',
		@isHeader=0,
		@showInNav=0,
		@navigationID=@level3NavigationID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('Admin'),dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('Admin')))
	EXEC @rc = dbo.createAdminFunctionsDeterminingNav
		@resourceTypeFunctionID=@resourceTypeFunctionID,
		@toolTypeID=@toolTypeID,
		@navigationID=@level3NavigationID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

IF @@TRANCOUNT > 0 COMMIT TRAN

EXEC dbo.createAdminSuite @siteid=@siteID

GOTO on_done

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN

on_done:

GO




USE [customApps]
GO
CREATE PROC [dbo].[TAGD_importNationalData]
@pathToImport varchar(200), 
@pathToExport varchar(200),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

DECLARE @orgID int, @cmd varchar(600), @IDList VARCHAR(max), @finalMSG varchar(max), @emailtouse varchar(300),
	@flatfile varchar(200)
SELECT @orgID = membercentral.dbo.fn_getOrgIDFromOrgCode('TAGD')

-- create temp tables
IF OBJECT_ID('tempdb..##tagd_alpha') IS NOT NULL
	DROP TABLE ##tagd_alpha
CREATE TABLE ##tagd_alpha (
	[rowid] [int] NOT NULL,
	[ID] [varchar](50) NOT NULL,
	[MEMBER TYPE] [varchar](10) NOT NULL DEFAULT (''),
	[MEMBER TYPE DESCRIPTION] [varchar](100) NOT NULL DEFAULT (''),
	[CATEGORY DESCRIPTION] [varchar](200) NOT NULL DEFAULT (''),
	[FULL NAME] [varchar](200) NOT NULL DEFAULT (''),
	[LAST FIRST] [varchar](200) NOT NULL DEFAULT (''),
	[FIRST NAME] [varchar](75) NOT NULL DEFAULT (''),
	[MIDDLE NAME] [varchar](25) NOT NULL DEFAULT (''),
	[LAST NAME] [varchar](75) NOT NULL DEFAULT (''),
	[SUFFIX] [varchar](50) NOT NULL DEFAULT (''),
	[DESIGNATION] [varchar](max) NULL,
	[WORK PHONE] [varchar](40) NULL,
	[HOME PHONE] [varchar](40) NULL,
	[FAX] [varchar](40) NULL,
	[FULL ADDRESS] [varchar](200) NOT NULL DEFAULT (''),
	[ADDRESS 1] [varchar](100) NULL,
	[ADDRESS 2] [varchar](100) NULL,
	[CITY] [varchar](35) NULL,
	[STATE PROVINCE] [varchar](25) NULL,
	[ZIP] [varchar](25) NULL,
	[GRAD DATE] datetime NULL,
	[DENTAL SCHOOL] [varchar](300) NULL,
	[JOIN DATE] datetime NULL,
	[PAID THRU] datetime NULL,
	[DO NOT MAIL] [varchar](300) NOT NULL DEFAULT (''),
	[EMAIL] [varchar](255) NULL,
	[COMPONENT TITLE] [varchar](300) NOT NULL DEFAULT (''),
	[BIRTH DATE] datetime NULL,
	[BAD ADDRESS] [varchar](300) NOT NULL DEFAULT ('')
)

IF OBJECT_ID('tempdb..##tagd_new') IS NOT NULL
	DROP TABLE ##tagd_new
CREATE TABLE ##tagd_new (
	[rowid] [int] NOT NULL,
	[ID] [varchar](50) NOT NULL,
	[FIRST NAME] [varchar](75) NOT NULL DEFAULT (''), 
	[MIDDLE NAME] [varchar](25) NOT NULL DEFAULT (''), 
	[LAST NAME] [varchar](75) NOT NULL DEFAULT (''), 
	[WORK ADDRESS 1] [varchar](100) NULL, 
	[WORK ADDRESS 2] [varchar](100) NULL, 
	[WORK CITY] [varchar](35) NULL, 
	[WORK STATE] [varchar](25) NULL, 
	[WORK ZIP] [varchar](25) NULL, 
	[WORK PHONE] [varchar](40) NULL, 
	[WORK FAX] [varchar](40) NULL, 
	[WORK EMAIL] [varchar](200) NULL, 
	[HOME ADDRESS 1] [varchar](100) NULL, 
	[HOME ADDRESS 2] [varchar](100) NULL, 
	[HOME CITY] [varchar](35) NULL, 
	[HOME STATE] [varchar](25) NULL, 
	[HOME ZIP] [varchar](25) NULL, 
	[HOME PHONE] [varchar](40) NULL, 
	[HOME FAX] [varchar](40) NULL, 
	[HOME EMAIL] [varchar](255) NULL, 
	[BIRTH DATE] datetime NULL,
	[WEBSITE] [varchar](200) NULL, 
	[PREFERRED ADDRESS] [varchar](200) NOT NULL DEFAULT (''), 
	[MEMBER TYPE] [varchar](200) NOT NULL DEFAULT (''), 
	[MEMBER TYPE CATEGORY] [varchar](200) NOT NULL DEFAULT (''), 
	[GENDER] [varchar](200) NULL , 
	[ETHNICITY] [varchar](200) NULL, 
	[MEMBER OF OTHER DENTAL ORGANIZATION] [varchar](200) NOT NULL DEFAULT (''), 
	[PREVIOUS MEMBER] [varchar](200) NOT NULL DEFAULT (''), 
	[JOIN DATE] datetime NULL,
	[PAID THRU] datetime NULL,
	[COMPONENT] [varchar](200) NOT NULL DEFAULT (''), 
	[DENTAL DEGREE] [varchar](200) NOT NULL DEFAULT (''), 
	[DENTAL SCHOOL] [varchar](200) NOT NULL DEFAULT (''), 
	[GRAD_DATE] datetime NULL,
	[GRADE] [varchar](200) NOT NULL DEFAULT (''), 
	[US RESIDENT] [varchar](200) NOT NULL DEFAULT (''), 
	[POSTDOCTORAL INSTITUTION] [varchar](200) NOT NULL DEFAULT (''), 
	[USA LICENSE] [varchar](200) NOT NULL DEFAULT (''), 
	[LICENSENO] [varchar](200) NULL, 
	[FEDERAL SERVICES] [varchar](200) NOT NULL DEFAULT (''), 
	[BRANCH] [varchar](200) NOT NULL DEFAULT (''), 
	[PRACTICE ENVIRONMENT] [varchar](200) NOT NULL DEFAULT (''), 
	[SPECIALTY] [varchar](200) NOT NULL DEFAULT ('')
)


IF OBJECT_ID('tempdb..##tagd_dues') IS NOT NULL
	DROP TABLE ##tagd_dues
CREATE TABLE ##tagd_dues (
	[rowid] [int] NOT NULL,
	[ID] [varchar](50) NOT NULL,
	[CHAPTER] [varchar](200) NOT NULL DEFAULT (''), 
	[FIRST NAME] [varchar](75) NOT NULL DEFAULT (''), 
	[MIDDLE NAME] [varchar](25) NOT NULL DEFAULT (''), 
	[LAST NAME] [varchar](75) NOT NULL DEFAULT (''), 
	[SUFFIX] [varchar](50) NOT NULL DEFAULT (''), 
	[DESIGNATION] [varchar](max) NULL, 
	[ADDRESS 1] [varchar](100) NOT NULL DEFAULT (''), 
	[ADDRESS 2] [varchar](100) NOT NULL DEFAULT (''), 
	[CITY] [varchar](35) NOT NULL DEFAULT (''), 
	[STATE] [varchar](25) NOT NULL DEFAULT (''), 
	[ZIP] [varchar](25) NOT NULL DEFAULT (''), 
	[WORK PHONE] [varchar](40) NOT NULL DEFAULT (''), 
	[FAX] [varchar](40) NOT NULL DEFAULT (''), 
	[EMAIL] [varchar](255) NULL, 
	[MEMBER TYPE] [varchar](200) NOT NULL DEFAULT (''), 
	[MEMBER TYPE DESCRIPTION] [varchar](200) NOT NULL DEFAULT (''), 
	[CATEGORY] [varchar](200) NOT NULL DEFAULT (''), 
	[CATEGORY DESCRIPTION] [varchar](200) NOT NULL DEFAULT (''), 
	[MEMBER TYPE BREAKDOWN] [varchar](200) NOT NULL DEFAULT (''), 
	[GRAD DATE] datetime NULL,
	[JOIN DATE] datetime NULL,
	[PAID THRU] datetime NULL,
	[CONSTITUENT] [varchar](200) NOT NULL DEFAULT (''), 
	[CONSTITUENT DUES] [decimal](9,2) NOT NULL, 
	[CONSTITUENT PAID] [decimal](9,2) NOT NULL, 
	[CONSTITUENT ABBREVIATION] [varchar](200) NOT NULL DEFAULT ('')
)


-- import csv files into temp tables.
BEGIN TRY
	SELECT @cmd = 'BULK INSERT ##tagd_alpha FROM ''' + @pathToImport + '1.csv'' WITH (FIELDTERMINATOR = '''+ char(7) + ''', FIRSTROW = 2);'
	exec(@cmd)
	SELECT @cmd = 'BULK INSERT ##tagd_new FROM ''' + @pathToImport + '2.csv'' WITH (FIELDTERMINATOR = '''+ char(7) + ''', FIRSTROW = 2);'
	exec(@cmd)
	SELECT @cmd = 'BULK INSERT ##tagd_dues FROM ''' + @pathToImport + '3.csv'' WITH (FIELDTERMINATOR = '''+ char(7) + ''', FIRSTROW = 2);'
	exec(@cmd)
END TRY
BEGIN CATCH
	SET @importResult = '<import date="" flatfile=""><errors><error msg="Unable to import data. Ensure the columns are in the correct order and match previous imports." severity="fatal" /></errors></import>'
	IF OBJECT_ID('tempdb..##tagd_alpha') IS NOT NULL
		DROP TABLE ##tagd_alpha
	IF OBJECT_ID('tempdb..##tagd_new') IS NOT NULL
		DROP TABLE ##tagd_new
	IF OBJECT_ID('tempdb..##tagd_dues') IS NOT NULL
		DROP TABLE ##tagd_dues
	RETURN -1
END CATCH


-- ensure IDs appear only once and set as primary key
BEGIN TRY
	ALTER TABLE ##tagd_alpha ADD CONSTRAINT PK__tagd_alpha PRIMARY KEY CLUSTERED (ID) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
END TRY
BEGIN CATCH
	SET @IDList = null
	SELECT @IDList = COALESCE(@IDList + ', ', '') + ID 
		from ##tagd_alpha
		group by ID
		having count(*) > 1
	SET @importResult = '<import date="" flatfile=""><errors><error msg="The file uploaded for Constituent Alpha Roster contains multiple rows for the following IDs: ' + @IDList + '. Ensure each ID appears only once in the file." severity="fatal" /></errors></import>'
	IF OBJECT_ID('tempdb..##tagd_alpha') IS NOT NULL
		DROP TABLE ##tagd_alpha
	IF OBJECT_ID('tempdb..##tagd_new') IS NOT NULL
		DROP TABLE ##tagd_new
	IF OBJECT_ID('tempdb..##tagd_dues') IS NOT NULL
		DROP TABLE ##tagd_dues
	RETURN -1
END CATCH

BEGIN TRY
	ALTER TABLE ##tagd_new ADD CONSTRAINT PK__tagd_new PRIMARY KEY CLUSTERED (ID) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
END TRY
BEGIN CATCH
	SET @IDList = null
	SELECT @IDList = COALESCE(@IDList + ', ', '') + ID 
		from ##tagd_new
		group by ID
		having count(*) > 1
	SET @importResult = '<import date="" flatfile=""><errors><error msg="The file uploaded for Constituent New Members contains multiple rows for the following IDs: ' + @IDList + '. Ensure each ID appears only once in the file." severity="fatal" /></errors></import>'
	IF OBJECT_ID('tempdb..##tagd_alpha') IS NOT NULL
		DROP TABLE ##tagd_alpha
	IF OBJECT_ID('tempdb..##tagd_new') IS NOT NULL
		DROP TABLE ##tagd_new
	IF OBJECT_ID('tempdb..##tagd_dues') IS NOT NULL
		DROP TABLE ##tagd_dues
	RETURN -1
END CATCH


-- flatten alpha table
IF OBJECT_ID('tempdb..##tagdMembers') IS NOT NULL
	DROP TABLE ##tagdMembers
select 
	m.prefix, 
	alpha.[FIRST NAME] as firstname, alpha.[MIDDLE NAME] as middlename, alpha.[LAST NAME] as lastname, alpha.SUFFIX as suffix, 
	cast(alpha.[DESIGNATION] as varchar(100)) as professionalSuffix, alpha.[ID] as memberNumber,
	m.company,
	
	alpha.[ADDRESS 1] as [Mailing Address_address1], alpha.[ADDRESS 2] as [Mailing Address_address2], alpha.[CITY] as [Mailing Address_city],
	alpha.[STATE PROVINCE] as [Mailing Address_stateprov], alpha.[ZIP] as [Mailing Address_postalCode], cast('' as varchar(50)) as [Mailing Address_county],
	cast('United States' as varchar(100)) as [Mailing Address_country], alpha.[WORK PHONE] as [Mailing Address_Phone], alpha.[FAX] as [Mailing Address_Fax],
	vw.[Mailing Address_Cell], vw.[Mailing Address_2nd Phone],
 
	vw.[Office Address_address1], vw.[Office Address_address2], vw.[Office Address_city], vw.[Office Address_stateprov],
	vw.[Office Address_postalCode], vw.[Office Address_county], vw.[Office Address_country], alpha.[WORK PHONE] as [Office Address_Phone], 
	vw.[Office Address_Fax], vw.[Office Address_Cell], vw.[Office Address_2nd Phone],

	vw.[Other Address_address1], vw.[Other Address_address2], vw.[Other Address_city], vw.[Other Address_stateprov], vw.[Other Address_postalCode],
	vw.[Other Address_county], vw.[Other Address_country], vw.[Other Address_Phone], vw.[Other Address_Fax], vw.[Other Address_Cell],
	vw.[Other Address_2nd Phone],

	coalesce(new.[HOME ADDRESS 1],vw.[Home Address_address1]) as [Home Address_address1],
	coalesce(new.[HOME ADDRESS 2],vw.[Home Address_address2]) as [Home Address_address2],	
	coalesce(new.[HOME CITY],vw.[Home Address_city]) as [Home Address_city],	
	coalesce(new.[HOME STATE],vw.[Home Address_stateprov]) as [Home Address_stateprov], 
	coalesce(new.[HOME ZIP],vw.[Home Address_postalCode]) as [Home Address_postalCode],
	vw.[Home Address_county], 
	case when new.[HOME STATE] is not null then 'United States' else vw.[Home Address_country] end as [Home Address_country], 
	alpha.[HOME PHONE] as [Home Address_Phone], 
	coalesce(new.[HOME FAX],vw.[Home Address_Fax]) as [Home Address_Fax],
	vw.[Home Address_Cell], vw.[Home Address_2nd Phone],

	coalesce(new.WEBSITE,vw.[Business Website]) as [Business Website],
	alpha.Email as [Email],
	coalesce(new.[HOME EMAIL],vw.[Personal Email]) as [Personal Email],
	vw.[Other Email],

	alpha.[JOIN DATE] as [AGD Join Date],
	cast(alpha.[ID] as varchar(255)) as [AGD Member Number], 
	alpha.[PAID THRU] as [AGD Paid Thru Date],
	cast(alpha.[FIRST NAME] as varchar(255)) as [Badge Name], 

	cast(case
	when alpha.[MEMBER TYPE] = 'AC' and alpha.[CATEGORY DESCRIPTION] = 'Disability Waiver' then 'General Dentist|Dues Waived'
	when alpha.[MEMBER TYPE] IN ('AC','EM','HM') then 'General Dentist'
	when alpha.[MEMBER TYPE] = 'AF' then 'Affiliate'
	when alpha.[MEMBER TYPE] = 'AS' then 'General Dentist'
	when alpha.[MEMBER TYPE] = 'RE' then 'Retired'
	when alpha.[MEMBER TYPE] = 'ST' then 'Student'
	else '' end as varchar(max)) as [Contact Type],
	alpha.[BIRTH DATE] as [Date of Birth],
	replace(replace(alpha.[DESIGNATION],',','|'),' ','') as [Designation],
	case 
	when new.ETHNICITY is not null and new.ETHNICITY = 'African American' then 'African-American'
	when new.ETHNICITY is not null and new.ETHNICITY = 'Hispanic or Latino' then 'Hispanic'
	when new.ETHNICITY is not null and new.ETHNICITY = 'White or Caucasian' then 'Caucasian'
	when new.ETHNICITY is not null and new.ETHNICITY = 'Asian or Pacific IslANDer' then 'Asian'
	else coalesce(new.ETHNICITY,vw.[Ethnicity])	end as [Ethnicity],
	coalesce(new.GENDER,vw.[Gender]) as [Gender],
	alpha.[GRAD DATE] as [Graduation Date],
	coalesce(new.LICENSENO,vw.[License Number]) as [License Number],
	cast(case
	when alpha.[MEMBER TYPE] = 'AC' and alpha.[CATEGORY DESCRIPTION] = 'Disability Waiver' then 'Disability Waiver'
	when alpha.[MEMBER TYPE] = 'AC' and alpha.[CATEGORY DESCRIPTION] = 'Residency' then 'Resident'
	when alpha.[MEMBER TYPE] = 'AC' then 'Active General Dentist'
	when alpha.[MEMBER TYPE] = 'AF' then 'Affiliate'
	when alpha.[MEMBER TYPE] = 'AS' then 'Associate'
	when alpha.[MEMBER TYPE] = 'EM' then 'Emeritus'
	when alpha.[MEMBER TYPE] = 'HM' then 'Honorary Member'
	when alpha.[MEMBER TYPE] = 'RE' then 'Retired'
	when alpha.[MEMBER TYPE] = 'ST' then 'Student Member'
	else '' end as varchar(255)) as [MemberType],
	cast(case 
	when alpha.[DENTAL SCHOOL] = 'Texas A&M Health Science Center Baylor College of Dentistry' then null
	when alpha.[DENTAL SCHOOL] = 'University of Texas Dental Branch at Houston' then null
	when alpha.[DENTAL SCHOOL] = 'University of Texas Health Science Center at San Antonio Dental School' then null
	else alpha.[DENTAL SCHOOL] end as varchar(255)) as [Other Dental School],
	cast(case alpha.[DENTAL SCHOOL]
	when 'Texas A&M Health Science Center Baylor College of Dentistry' then 'Baylor College of Dentistry'
	when 'University of Texas Dental Branch at Houston' then 'UTHSCS Houston'
	when 'University of Texas Health Science Center at San Antonio Dental School' then 'UTHSC San Antonio'
	else null end as varchar(255)) as [Texas Dental School],
	coalesce(new.[PRACTICE ENVIRONMENT],vw.[Type of Practice]) as [Type of Practice]
into ##tagdMembers
from ##tagd_alpha as alpha
left outer join membercentral.dbo.ams_members as m
	inner join membercentral.dbo.vw_memberdata_TAGD as vw on vw.memberID = m.memberID
	on m.orgID = @orgID and m.membernumber = alpha.[ID] and m.status <> 'D' and m.memberid = m.activeMemberID
left outer join ##tagd_new as new on new.ID = alpha.ID

-- get active members in database now so we dont inactivate them
insert into ##tagdMembers (
	[prefix],[firstname],[middlename],[lastname],[suffix],[professionalSuffix],[memberNumber],[company],[Mailing Address_address1],[Mailing Address_address2],
	[Mailing Address_city],[Mailing Address_stateprov],[Mailing Address_postalCode],[Mailing Address_county],[Mailing Address_country],[Mailing Address_Phone],
	[Mailing Address_Fax],[Mailing Address_Cell],[Mailing Address_2nd Phone],[Office Address_address1],[Office Address_address2],[Office Address_city],
	[Office Address_stateprov],[Office Address_postalCode],[Office Address_county],[Office Address_country],[Office Address_Phone],[Office Address_Fax],
	[Office Address_Cell],[Office Address_2nd Phone],[Other Address_address1],[Other Address_address2],[Other Address_city],[Other Address_stateprov],
	[Other Address_postalCode],[Other Address_county],[Other Address_country],[Other Address_Phone],[Other Address_Fax],[Other Address_Cell],
	[Other Address_2nd Phone],[Home Address_address1],[Home Address_address2],[Home Address_city],[Home Address_stateprov],[Home Address_postalCode],
	[Home Address_county],[Home Address_country],[Home Address_Phone],[Home Address_Fax],[Home Address_Cell],[Home Address_2nd Phone],[Business Website],
	[Email],[Personal Email],[Other Email],[AGD Join Date],[AGD Member Number],[AGD Paid Thru Date],[Badge Name],[Contact Type],[Date of Birth],[Designation],
	[Ethnicity],[Gender],[Graduation Date],[License Number],[MemberType],[Other Dental School],[Texas Dental School],[Type of Practice]
)
select m.prefix, m.firstname, m.middlename, m.lastname, m.suffix, m.professionalSuffix, m.membernumber, m.company, vw.[Mailing Address_address1], vw.[Mailing Address_address2],
	vw.[Mailing Address_city],vw.[Mailing Address_stateprov],vw.[Mailing Address_postalCode],vw.[Mailing Address_county],vw.[Mailing Address_country],vw.[Mailing Address_Phone],
	vw.[Mailing Address_Fax],vw.[Mailing Address_Cell],vw.[Mailing Address_2nd Phone],vw.[Office Address_address1],vw.[Office Address_address2],vw.[Office Address_city],
	vw.[Office Address_stateprov],vw.[Office Address_postalCode],vw.[Office Address_county],vw.[Office Address_country],vw.[Office Address_Phone],vw.[Office Address_Fax],
	vw.[Office Address_Cell],vw.[Office Address_2nd Phone],vw.[Other Address_address1],vw.[Other Address_address2],vw.[Other Address_city],vw.[Other Address_stateprov],
	vw.[Other Address_postalCode],vw.[Other Address_county],vw.[Other Address_country],vw.[Other Address_Phone],vw.[Other Address_Fax],vw.[Other Address_Cell],
	vw.[Other Address_2nd Phone],vw.[Home Address_address1],vw.[Home Address_address2],vw.[Home Address_city],vw.[Home Address_stateprov],vw.[Home Address_postalCode],
	vw.[Home Address_county],vw.[Home Address_country],vw.[Home Address_Phone],vw.[Home Address_Fax],vw.[Home Address_Cell],vw.[Home Address_2nd Phone],vw.[Business Website],
	vw.[Email],vw.[Personal Email],vw.[Other Email],vw.[AGD Join Date],vw.[AGD Member Number],vw.[AGD Paid Thru Date],vw.[Badge Name],vw.[Contact Type],vw.[Date of Birth],vw.[Designation],
	vw.[Ethnicity],vw.[Gender],vw.[Graduation Date],vw.[License Number],vw.[MemberType],vw.[Other Dental School],vw.[Texas Dental School],vw.[Type of Practice]
from membercentral.dbo.ams_members as m
inner join membercentral.dbo.vw_memberdata_TAGD as vw on vw.memberID = m.memberID
where m.orgID = @orgID
and m.status = 'A'
and m.memberid = m.activeMemberID
and m.memberTypeID = 2
and not exists (select membernumber from ##tagdMembers where membernumber = m.membernumber)

-- add rowID
ALTER TABLE ##tagdMembers ADD rowIDtemp int IDENTITY(1,1), rowID int NULL;
update ##tagdMembers set rowID = rowIDtemp;
ALTER TABLE ##tagdMembers DROP COLUMN rowIDtemp;

-- move data to holding tables
EXEC memberCentral.dbo.ams_importMemberData_tempToHolding @orgid=@orgID, @tmptbl='##tagdMembers', @pathToExport=@pathToExport, @importResult=@importResult OUTPUT

-- if no errors, run member import
IF @importResult.value('count(/import/errors/error)','int') = 0
BEGIN
	SELECT @flatfile = @importResult.value('(/import/@flatfile)[1]','varchar(160)')
	EXEC membercentral.dbo.ams_importMemberData_holdingToPerm @orgID=@orgID, @flatfile=@flatfile

	BEGIN TRY
		DECLARE @smtpserver varchar(20)
		IF @@SERVERNAME IN ('DEV04\PLATFORM2008','DEV03\PLATFORM2008','STAGING01\PLATFORM2008')
			SELECT @smtpserver = 'mail.trialsmith.com'
		ELSE
			SELECT @smtpserver = '***********'
		EXEC membercentral.dbo.ams_importMemberDataHoldingResultReport @importResult=@importResult, @finalMSG=@finalMSG OUTPUT
		EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject='TAGD Membership Import Results',
			@message=@finalMSG, @priority='normal', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
		SELECT @emailtouse = emailImportResults from membercentral.dbo.organizations where orgID = @orgID	
		IF len(@emailtouse) > 0 BEGIN
			EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to=@emailtouse,
				@cc=null, @bcc='<EMAIL>', @subject='TAGD Membership Import Results',
				@message=@finalMSG, @priority='normal', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
		END
	END TRY
	BEGIN CATCH      
		DECLARE @ErrorMessage VARCHAR(max)      
		DECLARE @ErrorSeverity INT      
		DECLARE @ErrorState INT        
		SELECT @ErrorMessage = 'Error running TAGD Membership Import' + char(13) + char(10) + ERROR_MESSAGE(),   
			@ErrorSeverity = ERROR_SEVERITY(),   
			@ErrorState = ERROR_STATE()        
		EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject='Error Importing TAGD Data',
			@message=@ErrorMessage, @priority='high', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END CATCH
END

-- cleanup
IF OBJECT_ID('tempdb..##tagd_alpha') IS NOT NULL
	DROP TABLE ##tagd_alpha
IF OBJECT_ID('tempdb..##tagd_new') IS NOT NULL
	DROP TABLE ##tagd_new
IF OBJECT_ID('tempdb..##tagd_dues') IS NOT NULL
	DROP TABLE ##tagd_dues
IF OBJECT_ID('tempdb..##tagdMembers') IS NOT NULL
	DROP TABLE ##tagdMembers

RETURN 0
GO

