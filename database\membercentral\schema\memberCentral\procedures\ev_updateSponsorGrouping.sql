ALTER PROC dbo.ev_updateSponsorGrouping
@sponsorGroupingID int,
@sponsorGrouping varchar(200)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF NOT EXISTS (select sponsorGroupingID from dbo.ev_sponsorGrouping where sponsorGroupingID=@sponsorGroupingID)
		RAISERROR('Sponsor Grouping does not exist.',16,1);

	DECLARE @registrationID int;
	SELECT @registrationID = registrationID from dbo.ev_sponsorGrouping where sponsorGroupingID=@sponsorGroupingID;

	IF EXISTS (select sponsorGroupingID from dbo.ev_sponsorGrouping where registrationID=@registrationID and sponsorGrouping=@sponsorGrouping and sponsorGroupingID != @sponsorGroupingID)
		RAISERROR('Sponsor Grouping name already exists.',16,1);

	UPDATE dbo.ev_sponsorGrouping 
	SET sponsorGrouping = @sponsorGrouping
	WHERE sponsorGroupingID = @sponsorGroupingID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLL<PERSON><PERSON><PERSON> TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
