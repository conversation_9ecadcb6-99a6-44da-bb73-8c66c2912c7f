USE [memberCentral]
GO
ALTER PROC [dbo].[ad_selectAdsToShow]
	@zoneTypeName varchar(255),
	@sitecode varchar(5),
	@NumberOfAdsToShow int,
	@uniqueCode varchar(50) = NULL
AS

update adpr2 WITH (UPDLOCK, READPAST)
set
	adpr2.lastshown = getdate()
OUTPUT
	selectedAds.adID, selectedAds.adName, selectedAds.imageUrl, selectedAds.adLink, selectedAds.adShortText, selectedAds.adContentObjectID, selectedAds.imageHeight, selectedAds.imageWidth,
	o.orgname, nextAds.googleUACode, nextAds.zoneTypeName, nextAds.zoneName
from platformstats.dbo.ad_adPlacementRotation adpr2
inner join (
		select top (@NumberOfAdsToShow) adp.adPlacementID, adp.adID, adv.googleUACode, azt.zoneTypeName, adz.zoneName
		from dbo.ad_zones adz
		inner join dbo.ad_zoneTypes azt
			on azt.zoneTypeID = adz.zoneTypeID
			and azt.zoneTypeName = @zoneTypeName
			and adz.sitecode = @sitecode
			and adz.uniqueCode = isNULL(@uniqueCode, adz.uniqueCode)
		inner join dbo.ad_zoneAdTypes azat
			on azat.zoneTypeID = azt.zoneTypeID
		inner join dbo.ad_Types adt
			on azat.adTypeID = adt.adTypeID
		inner join dbo.ad_adPlacements adp
			on adp.zoneID = adz.zoneID
		inner join dbo.ad_advertiserZones adtz
			on adp.zoneID = adtz.zoneID
		inner join dbo.ad_advertisers adv
			on adv.advertiserID = adtz.advertiserID
			and adv.status = 'A'
		inner join ad_ads ad
			on ad.adTypeID = adt.adTypeID
			and adp.adID = ad.adID
			and adtz.advertiserID = ad.advertiserID
			and ad.status = 'A'
		inner join platformstats.dbo.ad_adPlacementRotation adpr
			on adpr.adPlacementID = adp.adPlacementID
		order by adpr.lastshown
	) as nextAds on nextAds.adPlacementID = adpr2.adPlacementID
inner join membercentral.dbo.ad_ads selectedAds
	on selectedAds.adID = nextAds.adID
inner join membercentral.dbo.sites s
	on s.sitecode = @sitecode
inner join membercentral.dbo.organizations o
	on o.orgID = s.orgID
