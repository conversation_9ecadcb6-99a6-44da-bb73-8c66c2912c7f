use membercentral
GO

ALTER TABLE dbo.ams_memberFields ADD isGrouped bit DEFAULT(0) NOT NULL
GO


ALTER PROC [dbo].[ams_createMemberField]
@fieldsetID int,
@fieldCode varchar(30), 
@fieldLabel varchar(100), 
@fieldDescription varchar(300), 
@isRequired bit,
@isGrouped bit,
@fieldID int OUTPUT

AS

SELECT @fieldID = null

-- only add if doesnt already exist
IF EXISTS (select fieldID from dbo.ams_memberFields where fieldsetID=@fieldsetID and fieldCode=@fieldCode)
	GOTO on_error

-- get max order
declare @maxorder int
select @maxorder = isnull(MAX(fieldOrder), 0) + 1 
	FROM dbo.ams_memberFields
	WHERE fieldsetID = @fieldsetID

-- get org from fieldset
declare @orgID int
select @orgID = s.orgID
	from dbo.sites as s
	inner join dbo.ams_memberFieldSets as mfs on mfs.siteID = s.siteID
	and mfs.fieldsetID = @fieldsetID

-- insert field
INSERT INTO dbo.ams_memberFields (fieldsetID, dbObject, dbObjectAlias, dbField, fieldCode, fieldLabel, fieldDescription, displayTypeID, dataTypeID, isRequired, isGrouped, fieldOrder, [uid])
select top 1 @fieldsetID, p.dbObject, p.dbObjectAlias, p.dbField, p.fieldCode, @fieldLabel, @fieldDescription, dt.displayTypeID, ddt.dataTypeID, @isRequired, @isGrouped, @maxorder, NEWID()
from dbo.fn_ams_getPossibleMemberFields(@orgID) as p
inner join dbo.ams_memberDataColumnDisplayTypes as dt on dt.displayTypeCode = p.displayTypeCode
inner join dbo.ams_memberDataColumnDataTypes as ddt on ddt.dataTypeCode = p.dataTypeCode
where p.fieldCode = @fieldCode
	IF @@ERROR <> 0 GOTO on_error
	SELECT @fieldID = SCOPE_IDENTITY()

RETURN 0

on_error:
	SELECT @fieldID = 0
	RETURN -1
GO

ALTER PROC [dbo].[ams_createMemberEmailType]
@orgID int,
@emailType varchar(20),
@emailTypeDesc varchar(max)

AS

-- new email type cannot already exist or conflict with what would be in memberData views
IF EXISTS (select emailTypeID from dbo.ams_memberEmailTypes where emailType = @emailType and orgID = @orgID) 
OR EXISTS (select C.NAME FROM SYSCOLUMNS C INNER JOIN SYSOBJECTS O ON C.ID = O.ID WHERE O.NAME = 'ams_members' AND C.NAME = @emailType)
OR EXISTS (select websiteTypeID from dbo.ams_memberWebsiteTypes where websiteType = @emailType and orgID = @orgID)
OR EXISTS (select columnID from dbo.ams_memberDataColumns where columnName = @emailType and orgID = @orgID)
	RETURN 0

DECLARE @newOrder int
SELECT @newOrder = isnull(max(emailTypeOrder),0)+1
	from dbo.ams_memberEmailTypes
	where orgID = @orgID

INSERT into dbo.ams_memberEmailTypes (orgid, emailType, emailTypeDesc, emailTypeOrder) 
VALUES (@orgID, @emailType, @emailTypeDesc, @newOrder)
	IF @@ERROR <> 0 GOTO on_error

DECLARE @emailTypeID int

select @emailTypeID=emailTypeID
from dbo.ams_memberEmailTypes
where orgID = @orgID AND emailType=@emailType and emailTypeDesc=@emailTypeDesc

if @emailTypeID is not null
BEGIN

	declare @minSiteID int, @fieldsetID int, @memberMainRID int, @fieldcode varchar(30), @fieldLabel varchar(100), @fieldID int

	select @fieldCode = 'me_' + cast(@emailTypeID as varchar(4)) + '_email', @fieldLabel = @emailType

	select @minSiteID = min(siteID) from sites where orgID = @orgID

	while @minSiteID is not null BEGIN
		select @fieldsetID = NULL, @memberMainRID = NULL

		select top 1 @memberMainRID = sr.siteResourceID 
		from dbo.cms_siteResources as sr
			inner join dbo.cms_siteResourceTypes as srt on srt.resourceTypeID = sr.resourceTypeID and srt.resourceType = 'MemberAdmin'
		where sr.siteID = @minSiteID
		and sr.siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

		select @fieldsetID = mfs.fieldsetID 
		from dbo.ams_memberFieldsets mfs
		inner join dbo.ams_memberFieldUsage mfu on mfu.fieldsetID = mfs.fieldsetID and mfu.area = 'main' and mfu.siteResourceID = @memberMainRID
		where mfs.siteID = @minSiteID 

		if @fieldsetID is not null
			EXEC dbo.ams_createMemberField @fieldsetID, @fieldcode, @fieldLabel, '', 1, 0, @fieldID OUTPUT

		select @minSiteID = min(siteID) from sites where siteID > @minSiteID and orgID = @orgID

	END


END


RETURN 0

on_error:
	RETURN -1
GO

ALTER PROC [dbo].[ams_createMemberWebsiteType]
@orgID int,
@websiteType varchar(20),
@websiteTypeDesc varchar(max)

AS

-- new website type cannot already exist or conflict with what would be in memberData views
IF EXISTS (select websiteTypeID from dbo.ams_memberWebsiteTypes where websiteType = @websiteType and orgID = @orgID)
OR EXISTS (select C.NAME FROM SYSCOLUMNS C INNER JOIN SYSOBJECTS O ON C.ID = O.ID WHERE O.NAME = 'ams_members' AND C.NAME = @websiteType)
OR EXISTS (select emailTypeID from dbo.ams_memberEmailTypes where emailType = @websiteType and orgID = @orgID)
OR EXISTS (select columnID from dbo.ams_memberDataColumns where columnName = @websiteType and orgID = @orgID)
	RETURN 0

DECLARE @newOrder int
SELECT @newOrder = isnull(max(websiteTypeOrder),0)+1
	from dbo.ams_memberwebsiteTypes
	where orgID = @orgID

INSERT into dbo.ams_memberwebsiteTypes (orgid, websiteType, websiteTypeDesc, websiteTypeOrder) 
VALUES (@orgID, @websiteType, @websiteTypeDesc, @newOrder)
	IF @@ERROR <> 0 GOTO on_error

DECLARE @websiteTypeID int

select @websiteTypeID=websiteTypeID
from dbo.ams_memberWebsiteTypes
where orgID = @orgID AND websiteType=@websiteType and websiteTypeDesc=@websiteTypeDesc

if @websiteTypeID is not null
BEGIN

	declare @minSiteID int, @fieldsetID int, @memberMainRID int, @fieldcode varchar(30), @fieldLabel varchar(100), @fieldID int

	select @fieldCode = 'mw_' + cast(@websiteTypeID as varchar(4)) + '_website', @fieldLabel = @websiteType

	select @minSiteID = min(siteID) from sites where orgID = @orgID

	while @minSiteID is not null BEGIN
		select @fieldsetID = NULL, @memberMainRID = NULL

		select top 1 @memberMainRID = sr.siteResourceID 
		from dbo.cms_siteResources as sr
			inner join dbo.cms_siteResourceTypes as srt on srt.resourceTypeID = sr.resourceTypeID and srt.resourceType = 'MemberAdmin'
		where sr.siteID = @minSiteID
		and sr.siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

		select @fieldsetID = mfs.fieldsetID 
		from dbo.ams_memberFieldsets mfs
		inner join dbo.ams_memberFieldUsage mfu on mfu.fieldsetID = mfs.fieldsetID and mfu.area = 'main' and mfu.siteResourceID = @memberMainRID
		where mfs.siteID = @minSiteID 

		if @fieldsetID is not null
			EXEC dbo.ams_createMemberField @fieldsetID, @fieldcode, @fieldLabel, '', 1, 0, @fieldID OUTPUT

		select @minSiteID = min(siteID) from sites where siteID > @minSiteID and orgID = @orgID

	END


END


RETURN 0

on_error:
	RETURN -1
GO


ALTER PROCEDURE [dbo].[cms_createApplicationInstanceAdmin]
	@siteid int,
	@languageID int,
	@sectionID int,
	@isVisible bit,
	@pageName varchar(50),
	@pageTitle varchar(200),
	@pagedesc varchar(400),
	@zoneID int,
	@pageTemplateID int,
	@pageModeID int,
	@pgResourceTypeID int,
	@pgParentResourceID int = null,
	@allowReturnAfterLogin bit,
	@applicationInstanceName varchar(100),
	@applicationInstanceDesc varchar(200),
	@applicationInstanceID int OUTPUT,
	@siteResourceID int OUTPUT,
	@pageID int OUTPUT
AS

-- null OUTPUT vars
SELECT @applicationInstanceID = null, @siteResourceID = null, @pageID = null

DECLARE @appCreatedSectionResourceTypeID int, @applicationTypeID int, @rootSectionID int, @rc int
DECLARE @documentSectionName varchar(50)

select @appCreatedSectionResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedSection')
select @applicationTypeID = applicationTypeID from cms_applicationTypes where applicationTypeName = 'Admin'
	
BEGIN TRAN

exec @rc = dbo.cms_createApplicationInstance
		@siteid = @siteid,
		@languageID = @languageID,
		@sectionID = @sectionID,
		@applicationTypeID = @applicationTypeID,
		@isVisible = @isVisible,
		@pageName = @pageName,
		@pageTitle = @pageTitle,
		@pagedesc = @pagedesc,
		@zoneID = @zoneID,
		@pageTemplateID = @pageTemplateID,
		@pageModeID = @pageModeID,
		@pgResourceTypeID = @pgResourceTypeID,
		@pgParentResourceID = @pgParentResourceID,
		@allowReturnAfterLogin = @allowReturnAfterLogin,
		@applicationInstanceName = @applicationInstanceName,
		@applicationInstanceDesc = @applicationInstanceDesc,
		@applicationInstanceID = @applicationInstanceID OUTPUT,
		@siteResourceID = @siteResourceID OUTPUT,
		@pageID = @pageID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

select @documentSectionName = 'MemberDocument'

exec @rc = dbo.cms_createPageSection
		@siteID = @siteID, 
		@sectionResourceTypeID = @appCreatedSectionResourceTypeID, 
		@ovTemplateID = NULL,
		@ovTemplateIDMobile=NULL,
		@ovModeID = NULL, 
		@parentSectionID = @sectionID, 
		@sectionName = @documentSectionName, 
		@sectionCode = @documentSectionName,
		@inheritPlacements = 1,
		@sectionID = @rootSectionID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- update parentSiteResourceID of section
UPDATE sr
SET sr.parentSiteResourceID = @siteResourceID
FROM cms_pageSections s
INNER JOIN cms_siteResources sr on s.siteResourceID = sr.siteResourceID
	AND s.sectionID = @rootSectionID
	IF @@ERROR <> 0 GOTO on_error

-- update settings xml
UPDATE dbo.cms_applicationInstances
SET settingsXML = '<settings><setting name="memberDocumentSectionID" value="' + cast(@rootSectionID as varchar(8)) + + '" /><setting name="showMemberDocuments" value="false" /><setting name="showNotes" value="false" /><setting name="showRelationships" value="false" /><setting name="showMemberHistory" value="false" /><setting name="showSubscriptions" value="false" /><setting name="showReferrals" value="false" /><setting name="showApptTracker" value="false" /><setting name="showMemberPhotosInSearchResults" value="true" /><setting name="numPerPageInSearchResults" value="25" /><setting name="reportHeaderImage" value="gif" /><setting name="useAccrualAccounting" value="false" /></settings>'
WHERE applicationInstanceID = @applicationInstanceID
	IF @@ERROR <> 0 GOTO on_error

-- create the suite of tools
EXEC @rc = dbo.createAdminSuite @siteID
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- create admin member lookup fieldset
declare @memberAdminSRID int, @fieldSetID int, @fieldID int, @useID int
select @memberAdminSRID = dbo.fn_getSiteResourceIDForResourceType('MemberAdmin',@siteID)
EXEC @rc = dbo.ams_createMemberFieldSet @siteID=@siteID, @fieldsetName='Member Admin Search Form', @nameformat='LSXPFM', @showHelp=0, @fieldsetID=@fieldSetID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldSetID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_firstname', @fieldLabel='First Name', @fieldDescription='', @isRequired=0, @isGrouped=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_lastname', @fieldLabel='Last Name', @fieldDescription='', @isRequired=0, @isGrouped=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_company', @fieldLabel='Company', @fieldDescription='', @isRequired=0, @isGrouped=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_membernumber', @fieldLabel='Member Number', @fieldDescription='', @isRequired=0, @isGrouped=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = ams_createMemberFieldUsage @siteResourceID=@memberAdminSRID, @fieldsetID=@fieldSetID, @area='search', @createSiteResourceID=0, @useID=@useID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @useID = 0 GOTO on_error

EXEC @rc = dbo.ams_createMemberFieldSet @siteID=@siteID, @fieldsetName='Member Admin Search Results', @nameformat='LSXPFM', @showHelp=0, @fieldsetID=@fieldSetID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldSetID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_prefix', @fieldLabel='Prefix', @fieldDescription='', @isRequired=0, @isGrouped=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_middlename', @fieldLabel='Middle Name', @fieldDescription='', @isRequired=0, @isGrouped=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_suffix', @fieldLabel='Suffix', @fieldDescription='', @isRequired=0, @isGrouped=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = ams_createMemberFieldUsage @siteResourceID=@memberAdminSRID, @fieldsetID=@fieldSetID, @area='results', @createSiteResourceID=0, @useID=@useID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @useID = 0 GOTO on_error

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

ALTER PROC [dbo].[cms_createDefaultFieldsets]
@siteID int

AS

DECLARE @rc int, @srid int, @useID int
DECLARE @fsid1 int, @fsid2 int, @fsid3 int, @fsid4 int, @fsid5 int, @fsid6 int, @fieldID int
DECLARE @fieldcode varchar(30), @fieldLabel varchar(100)
DECLARE @addressTypeID int, @addressType varchar(20)
DECLARE @xml varchar(max)

BEGIN TRAN

	-- account locator
	EXEC @rc = dbo.ams_createMemberFieldset @siteID, 'Account Locator Search Form', 'LSXPFM', 0, @fsid1 OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fsid1 = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid1, 'm_lastname', 'Last Name', '', 1, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error

	EXEC dbo.ams_createMemberFieldset @siteID, 'Account Locator Search Results', 'LSXPFM', 0, @fsid2 OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fsid2 = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid2, 'm_firstname', 'First Name', '', 0, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid2, 'm_lastname', 'Last Name', '', 0, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid2, 'm_company', 'Company', '', 0, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error

	EXEC dbo.ams_createMemberFieldset @siteID, 'Account Locator New Account Form', 'LSXPFM', 0, @fsid3 OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fsid3 = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid3, 'm_firstname', 'First Name', '', 1, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid3, 'm_lastname', 'Last Name', '', 1, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	select TOP 1 @fieldcode = 'me_' + cast(met.emailTypeID as varchar(4)) + '_email',
		   @fieldLabel = met.emailType
		from dbo.sites as s 
		inner join dbo.ams_memberEmailTypes as met on met.orgID = s.orgID and met.emailTypeOrder = 1
		where s.siteid = @siteid
		IF @@ERROR <> 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid3, @fieldcode, @fieldLabel, '', 1, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	select TOP 1 @addressTypeID = mat.addressTypeID, @addressType = mat.addressType
		from dbo.sites as s		
		inner join dbo.ams_memberAddressTypes as mat on mat.orgID = s.orgID and mat.addressTypeOrder = 1
		where s.siteID = @siteID
		IF @@ERROR <> 0 GOTO on_error
	IF @addressTypeID > 0 BEGIN
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_address1'
		SELECT @fieldLabel = 'Address'
		EXEC @rc = dbo.ams_createMemberField @fsid3, @fieldcode, @fieldLabel, '', 1, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_city'
		SELECT @fieldLabel = 'City'
		EXEC @rc = dbo.ams_createMemberField @fsid3, @fieldcode, @fieldLabel, '', 1, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_stateprov'
		SELECT @fieldLabel = 'State'
		EXEC @rc = dbo.ams_createMemberField @fsid3, @fieldcode, @fieldLabel, '', 1, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_postalcode'
		SELECT @fieldLabel = 'Postal Code'
		EXEC @rc = dbo.ams_createMemberField @fsid3, @fieldcode, @fieldLabel, '', 1, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_country'
		SELECT @fieldLabel = 'Country'
		EXEC @rc = dbo.ams_createMemberField @fsid3, @fieldcode, @fieldLabel, '', 1, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	END

	select @srid = siteResourceID from dbo.sites where siteID = @siteID
	EXEC @rc = dbo.ams_createMemberFieldUsage @srid, @fsid1, 'search', 0, @useID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @useID = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberFieldUsage @srid, @fsid2, 'results', 0, @useID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @useID = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberFieldUsage @srid, @fsid3, 'newacct', 0, @useID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @useID = 0 GOTO on_error

	-- member main
	declare @memberMainRID int
	declare @minEmailTypeID int
	declare @minWebsiteTypeID int
	select top 1 @memberMainRID = sr.siteResourceID 
		from dbo.cms_siteResources as sr
		inner join dbo.cms_siteResourceTypes as srt on srt.resourceTypeID = sr.resourceTypeID and srt.resourceType = 'MemberAdmin'
		where sr.siteID = @siteID
		and sr.siteResourceStatusID = dbo.fn_getResourceStatusID('Active')
	IF @memberMainRID is not null BEGIN
		EXEC @rc = dbo.ams_createMemberFieldset @siteID, 'Member Main Display', 'LSXPFM', 0, @fsid4 OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fsid4 = 0 GOTO on_error

		select @minEmailTypeID = min(met.emailTypeID) 
			from dbo.sites as s 
			inner join dbo.ams_memberEmailTypes as met on met.orgID = s.orgID
			where s.siteid = @siteID
		while @minEmailTypeID is not null BEGIN
			select @fieldcode = 'me_' + cast(met.emailTypeID as varchar(4)) + '_email',
				   @fieldLabel = met.emailType
				from dbo.sites as s 
				inner join dbo.ams_memberEmailTypes as met on met.orgID = s.orgID
				where s.siteid = @siteid
				and met.emailTypeID = @minEmailTypeID
		      
			EXEC @rc = dbo.ams_createMemberField @fsid4, @fieldcode, @fieldLabel, '', 1, 0, @fieldID OUTPUT
				IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error

			select @minEmailTypeID = min(met.emailTypeID) 
				from dbo.sites as s 
				inner join dbo.ams_memberEmailTypes as met on met.orgID = s.orgID
				where s.siteid = @siteID 
				and met.emailTypeID > @minEmailTypeID
		END

		select @minWebsiteTypeID = min(wet.websiteTypeID) 
			from dbo.sites as s 
			inner join dbo.ams_memberWebsiteTypes as wet on wet.orgID = s.orgID
			where s.siteid = @siteID
		while @minWebsiteTypeID is not null BEGIN
			select @fieldcode = 'mw_' + cast(wet.websiteTypeID as varchar(4)) + '_website',
				   @fieldLabel = wet.websiteType
				from dbo.sites as s 
				inner join dbo.ams_memberWebsiteTypes as wet on wet.orgID = s.orgID
				where s.siteid = @siteid
				and wet.websiteTypeID = @minWebsiteTypeID
		      
			EXEC @rc = dbo.ams_createMemberField @fsid4, @fieldcode, @fieldLabel, '', 1, 0, @fieldID OUTPUT
				IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error

			select @minWebsiteTypeID = min(wet.websiteTypeID) 
				from dbo.sites as s 
				inner join dbo.ams_memberWebsiteTypes as wet on wet.orgID = s.orgID
				where s.siteid = @siteID 
				and wet.websiteTypeID > @minWebsiteTypeID
		END

		EXEC @rc = dbo.ams_createMemberFieldUsage @memberMainRID, @fsid4, 'main', 0, @useID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @useID = 0 GOTO on_error

	END

	-- event reg download
	EXEC @rc = dbo.ams_createMemberFieldset @siteID, 'Events Download Registrants Standard', 'LSXPFM', 0, @fsid5 OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fsid5 = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid5, 'm_firstname', 'First Name', '', 0, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid5, 'm_lastname', 'Last Name', '', 0, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid5, 'm_company', 'Company', '', 0, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	select TOP 1 @addressTypeID = mat.addressTypeID, @addressType = mat.addressType
		from dbo.sites as s		
		inner join dbo.ams_memberAddressTypes as mat on mat.orgID = s.orgID and mat.addressTypeOrder = 1
		where s.siteID = @siteID
		IF @@ERROR <> 0 GOTO on_error
	IF @addressTypeID > 0 BEGIN
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_address1'
		SELECT @fieldLabel = 'Address'
		EXEC @rc = dbo.ams_createMemberField @fsid5, @fieldcode, @fieldLabel, '', 0, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_city'
		SELECT @fieldLabel = 'City'
		EXEC @rc = dbo.ams_createMemberField @fsid5, @fieldcode, @fieldLabel, '', 0, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_stateprov'
		SELECT @fieldLabel = 'State'
		EXEC @rc = dbo.ams_createMemberField @fsid5, @fieldcode, @fieldLabel, '', 0, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_postalcode'
		SELECT @fieldLabel = 'Postal Code'
		EXEC @rc = dbo.ams_createMemberField @fsid5, @fieldcode, @fieldLabel, '', 0, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_country'
		SELECT @fieldLabel = 'Country'
		EXEC @rc = dbo.ams_createMemberField @fsid5, @fieldcode, @fieldLabel, '', 0, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	END
	select @minEmailTypeID = min(met.emailTypeID) 
		from dbo.sites as s 
		inner join dbo.ams_memberEmailTypes as met on met.orgID = s.orgID
		where s.siteid = @siteID
	while @minEmailTypeID is not null BEGIN
		select @fieldcode = 'me_' + cast(met.emailTypeID as varchar(4)) + '_email',
			   @fieldLabel = met.emailType
			from dbo.sites as s 
			inner join dbo.ams_memberEmailTypes as met on met.orgID = s.orgID
			where s.siteid = @siteid
			and met.emailTypeID = @minEmailTypeID
	      
		EXEC @rc = dbo.ams_createMemberField @fsid5, @fieldcode, @fieldLabel, '', 0, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error

		select @minEmailTypeID = min(met.emailTypeID) 
			from dbo.sites as s 
			inner join dbo.ams_memberEmailTypes as met on met.orgID = s.orgID
			where s.siteid = @siteID 
			and met.emailTypeID > @minEmailTypeID
	END

	-- group members download
	EXEC @rc = dbo.ams_createMemberFieldset @siteID, 'Groups Download Members Standard', 'LSXPFM', 0, @fsid6 OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fsid6 = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid6, 'm_firstname', 'First Name', '', 0, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid6, 'm_lastname', 'Last Name', '', 0, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid6, 'm_company', 'Company', '', 0, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	EXEC @rc = dbo.ams_createMemberField @fsid6, 'm_membernumber', 'Member Number', '', 0, 0, @fieldID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error

	select TOP 1 @addressTypeID = mat.addressTypeID, @addressType = mat.addressType
		from dbo.sites as s		
		inner join dbo.ams_memberAddressTypes as mat on mat.orgID = s.orgID and mat.addressTypeOrder = 1
		where s.siteID = @siteID
		IF @@ERROR <> 0 GOTO on_error
	IF @addressTypeID > 0 BEGIN
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_address1'
		SELECT @fieldLabel = 'Address'
		EXEC @rc = dbo.ams_createMemberField @fsid6, @fieldcode, @fieldLabel, '', 0, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_city'
		SELECT @fieldLabel = 'City'
		EXEC @rc = dbo.ams_createMemberField @fsid6, @fieldcode, @fieldLabel, '', 0, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_stateprov'
		SELECT @fieldLabel = 'State'
		EXEC @rc = dbo.ams_createMemberField @fsid6, @fieldcode, @fieldLabel, '', 0, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_postalcode'
		SELECT @fieldLabel = 'Postal Code'
		EXEC @rc = dbo.ams_createMemberField @fsid6, @fieldcode, @fieldLabel, '', 0, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
		SELECT @fieldcode = 'ma_' + cast(@addressTypeID as varchar(5)) + '_country'
		SELECT @fieldLabel = 'Country'
		EXEC @rc = dbo.ams_createMemberField @fsid6, @fieldcode, @fieldLabel, '', 0, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
	END
	select @minEmailTypeID = min(met.emailTypeID) 
		from dbo.sites as s 
		inner join dbo.ams_memberEmailTypes as met on met.orgID = s.orgID
		where s.siteid = @siteID
	while @minEmailTypeID is not null BEGIN
		select @fieldcode = 'me_' + cast(met.emailTypeID as varchar(4)) + '_email',
			   @fieldLabel = met.emailType
			from dbo.sites as s 
			inner join dbo.ams_memberEmailTypes as met on met.orgID = s.orgID
			where s.siteid = @siteid
			and met.emailTypeID = @minEmailTypeID
	      
		EXEC @rc = dbo.ams_createMemberField @fsid6, @fieldcode, @fieldLabel, '', 0, 0, @fieldID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error

		select @minEmailTypeID = min(met.emailTypeID) 
			from dbo.sites as s 
			inner join dbo.ams_memberEmailTypes as met on met.orgID = s.orgID
			where s.siteid = @siteID 
			and met.emailTypeID > @minEmailTypeID
	END

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

ALTER FUNCTION [dbo].[fn_getMemberFieldsetStructureXML] (@siteID int)
RETURNS xml
AS
BEGIN
	DECLARE @xmlStructure xml
	
	DECLARE @orgID int
	SELECT @orgID = orgid from dbo.sites where siteID = @siteID

	SELECT @xmlStructure = (
		select (
			select fieldset.fieldsetname, fieldset.nameFormat, fieldset.showHelp, fieldset.uid,
				field.dbField, field.fieldCode, field.fieldLabel, field.displayTypeID, field.dataTypeID, field.isRequired, 
				field.isGrouped, field.fieldOrder, field.fieldDescription, field.uid
			from dbo.ams_memberFieldSets as fieldset
			left outer join dbo.ams_memberFields as field on field.fieldsetID = fieldset.fieldsetID
			where fieldset.siteID = @siteid
			order by fieldset.fieldsetID, field.fieldOrder
			FOR XML AUTO, root('fieldsets'), TYPE
			),
			(
			select distinct field.fieldCode, field.dbfield
			from dbo.ams_memberFieldSets as fieldset
			left outer join dbo.ams_memberFields as field on field.fieldsetID = fieldset.fieldsetID
			where fieldset.siteID = @siteid
			order by field.fieldCode
			FOR XML AUTO, root('allfields'), TYPE
			),
			(
			select distinct mwt.websitetypeid, mwt.websitetype
			from dbo.ams_memberWebsiteTypes as mwt
			inner join dbo.ams_memberFields as field on parsename(replace(field.fieldCode,'_','.'),2) = mwt.websitetypeid
			where mwt.orgID = @orgID
			and left(field.fieldcode,3) in ('mw_')
			FOR XML AUTO, root('websitetypes'), TYPE
			),
			(
			select distinct met.emailtypeid, met.emailtype
			from dbo.ams_memberEmailTypes as met
			inner join dbo.ams_memberFields as field on parsename(replace(field.fieldCode,'_','.'),2) = met.emailtypeid
			where met.orgID = @orgID
			and left(field.fieldcode,3) in ('me_')
			FOR XML AUTO, root('emailtypes'), TYPE
			),
			(
			select distinct mat.addresstypeid, mat.addresstype
			from (
				select addressTypeID, addressType
				from dbo.ams_memberAddressTypes
				where orgID = @orgID
					union
				select 0, 'Designated Billing'
			) as mat
			inner join dbo.ams_memberFields as field on parsename(replace(field.fieldCode,'_','.'),2) = mat.addresstypeid
			where (left(field.fieldcode,3) in ('ma_','mp_') or left(field.fieldcode,4) = 'mad_')
			FOR XML AUTO, root('addresstypes'), TYPE
			),
			(
			select distinct mpt.phonetypeid, mpt.phonetype
			from dbo.ams_memberPhoneTypes as mpt
			inner join dbo.ams_memberFields as field on parsename(replace(field.fieldCode,'_','.'),1) = mpt.phonetypeid
			where mpt.orgID = @orgID
			and left(field.fieldcode,3) = 'mp_'
			FOR XML AUTO, root('phonetypes'), TYPE
			),
			(
			select distinct mdt.districttypeid, mdt.districttype
			from dbo.ams_memberDistrictTypes as mdt
			inner join dbo.ams_memberFields as field on parsename(replace(field.fieldCode,'_','.'),1) = mdt.districttypeid
			where mdt.orgID = @orgID
			and left(field.fieldcode,4) = 'mad_'
			FOR XML AUTO, root('districttypes'), TYPE
			),
			(
			select distinct mpl.PLTypeID, mpl.PLName
			from dbo.ams_memberProfessionalLicenseTypes as mpl
			inner join dbo.ams_memberFields as field on parsename(replace(field.fieldCode,'_','.'),2) = mpl.PLTypeID
			where mpl.orgID = @orgID
			and left(field.fieldcode,4) = 'mpl_'
			FOR XML AUTO, root('licensetypes'), TYPE
			),
			(
			select distinct mp.profileID, mp.profileName
			from dbo.mp_profiles as mp
			inner join dbo.ams_memberFields as field on parsename(replace(field.fieldCode,'_','.'),1) = mp.profileID
			where mp.siteID = @siteID
			and left(field.fieldcode,13) = 'acct_balance_'
			FOR XML AUTO, root('merchantprofiles'), TYPE
			),
			(
			select distinct g.groupid, g.uid
			from dbo.ams_memberFieldSets as fs
			inner join dbo.ams_memberFields as f on f.fieldsetid = fs.fieldsetid
			inner join dbo.ams_groups as g on g.groupid = parsename( replace(f.dbField,'_','.') ,1)
			inner join dbo.sites as s on s.siteid = fs.siteid
			where s.orgid = @orgID
			and f.dbObjectAlias = 'grps'
			FOR XML AUTO, root('groups'), TYPE
			),
			(
			select distinct g.groupid as group_id, isNull(sub.subscriptionID, '') as sub_id, 
				isNull(cast (sub.uid as varchar (50)), '') as sub_uid, 
				isNull(t.typeid, '') as [type_id], 
				isNull(cast (t.uid as varchar (50)), '') as type_uid 	
			from dbo.ams_memberFieldSets as fs
			inner join dbo.ams_memberFields as f on f.fieldsetid = fs.fieldsetid
			inner join dbo.ams_groups as g on g.groupid = parsename( replace(f.fieldCode,'_','.') ,1)
			left outer join dbo.sub_subscriptions as sub on sub.subscriptionID = parsename( replace(f.dbField,'_','.') ,2)
				and left(f.dbField,3) = 'Sub'
			left outer join dbo.sub_types as t on t.typeid = parsename( replace(f.dbField,'_','.') ,2)
				and left(f.dbField,3) <> 'Sub'
			inner join dbo.sites as s on s.siteid = fs.siteid
			where s.orgid = @orgID
			and f.dbObjectAlias = 'subs'
			FOR XML AUTO, root('subs'), TYPE
			)
		FOR XML RAW('fieldsetStructure'), TYPE
		)

	RETURN @xmlStructure

END
GO

CREATE FUNCTION dbo.fn_varCharListNoEmpty (
	@origvarcharlist varchar(max), 
	@origdelimiter varchar(1), 
	@newdelimiter varchar(1)
)
RETURNS varchar(max)
AS
BEGIN
	DECLARE @newvarcharlist varchar(max)

	SELECT @newvarcharlist = COALESCE(@newvarcharlist + @newdelimiter, '') + listItem
	FROM dbo.fn_varCharListToTable(@origvarcharlist, @origdelimiter)
	WHERE listItem <> ''
	order by autoid

	RETURN @newvarcharlist
END
GO

ALTER PROC [dbo].[ams_getMemberFields]
@fieldsetID int

AS

select isnull(cast((
	select mfs.fieldsetName as '@fieldsetName', mfs.nameformat as '@nameformat', mfs.fieldSetID as '@fieldsetID', mfs.showHelp as '@showHelp',
		(

		select mf.fieldID, mf.dbObject, mf.dbObjectAlias, mf.dbField, mf.fieldCode, mdColumnID,
			mf.fieldLabel, mf.fieldDescription, mf.isRequired, mf.isGrouped, mf.displayTypeCode, mf.dataTypeCode, mf.isReadOnly,
			mf.allowMultiple, opt.columnValueDecimal2, opt.columnValueInteger, opt.columnvalueDate, opt.columnValueBit, 
			opt.columnValueXML, opt.columnValueSiteResourceID, 
			case when left(mf.fieldCode,4) = 'grp_' or left(mf.fieldCode,5) = 'acct_' then 0 else opt.valueID end as valueID, 
			case 
				when mf.dbObjectAlias = 'subs' and left(mf.dbField,3) = 'sub' then (select coalesce(reportCode,subscriptionName) from dbo.sub_subscriptions where subscriptionID = parseName(replace(mf.dbField,'_','.'),2)) 
				when mf.dbObjectAlias = 'subs' then (select typeName from dbo.sub_Types where typeID = parseName(replace(mf.dbField,'_','.'),2)) 
				when mf.dbObjectAlias = 'grps' then (select coalesce(groupcode,groupName) from dbo.ams_groups where groupID = parseName(replace(mf.fieldCode,'_','.'),1)) 
				when mf.dbObjectAlias = 'acct' and left(mf.dbField,13) = 'acct_balance_' then isnull((select profileName from dbo.mp_profiles where profileID = parseName(replace(mf.dbField,'_','.'),1)),'Total') 
				else opt.columnValueString 
				end as columnValueString
		from (
			select amf.fieldID, amf.dbObject, amf.dbObjectAlias, amf.dbField, amf.fieldCode, amf.fieldLabel, amf.fieldDescription, 
				amf.isRequired, amf.isGrouped, dt.displayTypeCode, ddt.dataTypeCode, amf.fieldOrder,
				case 
				when left(amf.fieldcode,4) = 'mad_' then 1
				when left(amf.fieldCode,3) = 'md_' then (select isReadOnly from ams_memberDataColumns where columnID = cast(replace(amf.fieldCode,'md_','') as int)) 
				else 0 
				end as isReadOnly,
				case when left(amf.fieldCode,3) = 'md_' then (select allowMultiple from ams_memberDataColumns where columnID = cast(replace(amf.fieldCode,'md_','') as int)) else 0 end as allowMultiple,
				case when dt.displayTypeCode in ('SELECT','RADIO','CHECKBOX') and left(amf.fieldCode,3) = 'md_' then replace(amf.fieldCode,'md_','') else 0 end as mdColumnID
			from dbo.ams_memberFields as amf
			inner join dbo.ams_memberDataColumnDisplayTypes as dt on dt.displayTypeID = amf.displayTypeID
			inner join dbo.ams_memberDataColumnDataTypes as ddt on ddt.dataTypeID = amf.dataTypeID
			where amf.fieldsetID = mfs.fieldsetID		
		) as mf
		left outer join dbo.ams_memberDataColumnValues as opt on opt.columnID = mf.mdColumnID and mf.mdColumnID > 0	
		
		order by mf.isGrouped desc, mf.fieldOrder, opt.columnValueString, opt.columnValueDecimal2,
			opt.columnValueInteger, opt.columnvalueDate, opt.columnValueBit,
			cast(opt.columnValueXML as varchar(max)), opt.columnValueSiteResourceID
		FOR XML AUTO, TYPE
		)
	from dbo.ams_memberFieldSets as mfs
	where mfs.fieldsetID = @fieldsetid
	FOR XML PATH('fields')
) as XML),'<fields/>') as fieldsXML

RETURN 0
GO


