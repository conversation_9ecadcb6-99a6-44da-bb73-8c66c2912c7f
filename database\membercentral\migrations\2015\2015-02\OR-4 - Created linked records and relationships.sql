use membercentral
GO

BEGIN TRAN

declare @tmp table (memberID int, recordTypeID int, member<PERSON><PERSON><PERSON> varchar(max), [contact type] varchar(max), [firm id] varchar(max))

declare 
	@LawFirmRTID int, 
	@IndivRTID int, 
	@CourtOfficeRTID int, 
	@VendorRTID int, 
	@orgID int,
	@firmID varchar(max),
	@recordTypeRelationshipTypeID int,
	@AttorneyRelTypeID int,
	@LegalStaffRelTypeID int,
	@JudgeRelTypeID int,
	@CourtStaffRelTypeID int,
	@VendorStaffRelTypeID int,
	@AttorneyRTRTID int,
	@LegalStaffRTRTID int,
	@JudgeRTRTID int,
	@CourtStaffRTRTID int,
	@VendorRTRTID int

select @orgID = orgID from sites where sitecode = 'OR'

--Record Types
select @LawFirmRTID = recordTypeID from dbo.ams_recordTypes where orgID = @orgID and recordTypeCode = 'LawFirm'
select @IndivRTID = recordTypeID from dbo.ams_recordTypes where orgID = @orgID and recordTypeCode = 'Individual'
select @CourtOfficeRTID = recordTypeID from dbo.ams_recordTypes where orgID = @orgID and recordTypeCode = 'CourtOffice'
select @VendorRTID = recordTypeID from dbo.ams_recordTypes where orgID = @orgID and recordTypeCode = 'Vendor'

--Relationship Types
select @AttorneyRelTypeID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgID and relationshipTypeCode = 'Attorney'
select @LegalStaffRelTypeID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgID and relationshipTypeCode = 'LegalStaff'
select @JudgeRelTypeID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgID and relationshipTypeCode = 'Judge'
select @CourtStaffRelTypeID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgID and relationshipTypeCode = 'CourtStaff'
select @VendorStaffRelTypeID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgID and relationshipTypeCode = 'VendorStaff'

--RecordType Relationship Types
select @AttorneyRTRTID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes 
where masterRecordTypeID = @LawFirmRTID 
	and childRecordTypeID = @IndivRTID 
	and relationshipTypeID = @AttorneyRelTypeID

select @LegalStaffRTRTID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes 
where masterRecordTypeID = @LawFirmRTID 
	and childRecordTypeID = @IndivRTID 
	and relationshipTypeID = @LegalStaffRelTypeID

select @JudgeRTRTID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes 
where masterRecordTypeID = @CourtOfficeRTID 
	and childRecordTypeID = @IndivRTID 
	and relationshipTypeID = @JudgeRelTypeID

select @CourtStaffRTRTID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes 
where masterRecordTypeID = @CourtOfficeRTID 
	and childRecordTypeID = @IndivRTID 
	and relationshipTypeID = @CourtStaffRelTypeID

select @VendorRTRTID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes 
where masterRecordTypeID = @VendorRTID 
	and childRecordTypeID = @IndivRTID 
	and relationshipTypeID = @VendorStaffRelTypeID

insert into @tmp
select *
from (
	select m.memberID, m.recordTypeID, m.memberNumber, vw.[contact Type], vw.[firm id]
	from dbo.vw_memberData_OR vw
	inner join dbo.ams_members m
	on m.memberID = vw.memberID
) as tmp


update dbo.ams_members
set recordTypeID = @LawFirmRTID
where memberID IN (
	select memberID from @tmp
	where memberNumber like '%FIRM'
)
IF @@ERROR <> 0 goto on_error

update dbo.ams_members
set recordTypeID = @CourtOfficeRTID
where memberID IN (
	select memberID from @tmp 
	where [contact type] IN ('Court Office','Court Staff')
	AND memberNumber like '%FIRM'
)
IF @@ERROR <> 0 goto on_error

update dbo.ams_members
set recordTypeID = @VendorRTID
where memberID IN (
	select memberID from @tmp
	where [contact type] like '%Vendor%'
	AND memberNumber like '%FIRM'
)
IF @@ERROR <> 0 goto on_error

update dbo.ams_members
set recordTypeID = @IndivRTID
where memberID IN (
	select memberID from @tmp
	where recordTypeID IS NULL
	AND memberNumber NOT like '%FIRM'
)
IF @@ERROR <> 0 goto on_error

------------------END RECORD TYPE SETTING--------------------------

------------------BEGIN RELATIONSHIP TYPE SETTING------------------

/* Law Firms */

-------Add Attorney Relationships

insert into dbo.ams_recordRelationships (
	recordTypeRelationshipTypeID, 
	masterMemberID, 
	childMemberID, 
	isActive)
select 
	@AttorneyRTRTID as recordTypeRelationshipTypeID,
	m.memberID as MasterMemberID, 
	tmp.memberID as ChildMemberID,
	1 as isActive
from @tmp tmp
	inner join ams_members m
	on m.memberNumber = tmp.[firm id]
		and m.status <> 'D'
		and m.orgID = @orgID	
where tmp.[contact type] IN ('Attorney','Retired Attorney','Out of State Attorney') 
	AND tmp.[firm id] IS NOT NULL
IF @@ERROR <> 0 goto on_error


-------Add Legal Staff Relationships

insert into dbo.ams_recordRelationships (
	recordTypeRelationshipTypeID, 
	masterMemberID, 
	childMemberID, 
	isActive)
select 
	@LegalStaffRTRTID as recordTypeRelationshipTypeID, 
	m.memberID as MasterMemberID, 
	tmp.memberID as ChildMemberID, 
	1 as isActive
from @tmp tmp
	inner join ams_members m
	on m.memberNumber = tmp.[firm id]
		and m.status <> 'D'
		and m.orgID = @orgID	
where tmp.[contact type] like '%paralegal'
	AND tmp.[firm id] IS NOT NULL
IF @@ERROR <> 0 goto on_error

/* Court Offices */

-------Add Judge Relationships

insert into dbo.ams_recordRelationships (
	recordTypeRelationshipTypeID, 
	masterMemberID, 
	childMemberID, 
	isActive)
select 
	@JudgeRTRTID as recordTypeRelationshipTypeID, 
	m.memberID as MasterMemberID, 
	tmp.memberID as ChildMemberID, 
	1 as isActive
from @tmp tmp
	inner join ams_members m
	on m.memberNumber = tmp.[firm id]
		and m.status <> 'D'
		and m.orgID = @orgID	
where tmp.[contact type] IN ('Administrative Law Judge','Circuit Court Judge','Federal Court Judge','Municipal Court Judge','Supreme Court Justice') 
	AND tmp.[firm id] IS NOT NULL
IF @@ERROR <> 0 goto on_error

-------Add Court Staff Relationships

insert into dbo.ams_recordRelationships (
	recordTypeRelationshipTypeID, 
	masterMemberID, 
	childMemberID, 
	isActive)
select 
	@CourtStaffRTRTID as recordTypeRelationshipTypeID, 
	m.memberID as MasterMemberID, 
	tmp.memberID as ChildMemberID, 
	1 as isActive
from @tmp tmp
	inner join ams_members m
	on m.memberNumber = tmp.[firm id]
		and m.status <> 'D'
		and m.orgID = @orgID
where tmp.[contact type] LIKE 'Court%'
	AND tmp.[firm id] IS NOT NULL
IF @@ERROR <> 0 goto on_error

/* Vendors */

-------Add Vendor Staff Relationships

insert into dbo.ams_recordRelationships (
	recordTypeRelationshipTypeID, 
	masterMemberID, 
	childMemberID, 
	isActive)
select 
	@VendorRTRTID as recordTypeRelationshipTypeID, 
	m.memberID as MasterMemberID, 
	tmp.memberID as ChildMemberID, 
	1 as isActive
from @tmp tmp
	inner join ams_members m
	on m.memberNumber = tmp.[firm id]
		and m.status <> 'D'
		and m.orgID = @orgID	
where tmp.[contact type] LIKE '%Vendor%'
	AND tmp.[firm id] IS NOT NULL
IF @@ERROR <> 0 goto on_error


COMMIT TRAN
goto on_success

on_error:
	ROLLBACK TRAN

on_success:
	-- queue member groups (@runSchedule=1 indicates immediate processing) 
	declare @itemGroupUID uniqueidentifier, @memberIDList varchar(max)
	select 
		@memberIDList = COALESCE(@memberIDList + ',', '') + cast(memberID as varchar(10)) 
	from 
		@tmp
	group by 
		memberID
	
	exec platformQueue.dbo.queue_processMemberGroups_insert 
			@orgID=@orgID, 
			@memberIDList=@memberIDList, 
			@conditionIDList='', 
			@runSchedule=1, 
			@itemGroupUID=@itemGroupUID OUTPUT
