IF  EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME ='FK_ams_memberFieldUsageProfessionalLicenses_ams_memberProfessionalLicenseTypes')
BEGIN
	print 'Relationship ref_clientReferrals_ref_cases Exists! Deleting...'
	ALTER TABLE [ams_memberFieldUsageProfessionalLicenses] DROP CONSTRAINT [FK_ams_memberFieldUsageProfessionalLicenses_ams_memberProfessionalLicenseTypes]
END
GO

IF  EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME ='FK_ams_memberFieldUsageProfessionalLicenses_cms_siteResources')
BEGIN
	print 'Relationship ref_clientReferrals_ref_cases Exists! Deleting...'
	ALTER TABLE [ams_memberFieldUsageProfessionalLicenses] DROP CONSTRAINT [FK_ams_memberFieldUsageProfessionalLicenses_cms_siteResources]
END
GO

print '********************************************'
print 'CREATING TABLE ams_memberFieldUsageProfessionalLicenses'

IF OBJECT_ID('ams_memberFieldUsageProfessionalLicenses') IS NOT NULL 
	EXEC('DROP TABLE  ams_memberFieldUsageProfessionalLicenses')
GO

create table ams_memberFieldUsageProfessionalLicenses (
	fieldUsageID int identity(1,1),
	PLTypeID int not null, 
	siteResourceID int not null
)
GO

print 'Creating relationship [FK_ams_memberFieldUsageProfessionalLicenses_ams_memberProfessionalLicenses]...'
ALTER TABLE [ams_memberFieldUsageProfessionalLicenses]  WITH NOCHECK ADD  CONSTRAINT [FK_ams_memberFieldUsageProfessionalLicenses_ams_memberProfessionalLicenseTypes] FOREIGN KEY([PLTypeID])
REFERENCES [dbo].[ams_memberProfessionalLicenseTypes] ([PLTypeID])
GO

print 'Creating relationship FK_ams_memberFieldUsageProfessionalLicenses_cms_siteResources...'
ALTER TABLE [ams_memberFieldUsageProfessionalLicenses]  WITH NOCHECK ADD  CONSTRAINT [FK_ams_memberFieldUsageProfessionalLicenses_cms_siteResources] FOREIGN KEY([siteResourceID])
REFERENCES [dbo].[cms_siteResources] ([siteResourceID])
GO


USE [memberCentral]
GO
ALTER PROCEDURE [dbo].[ams_removeMemberProfessionalLicenseType]
@orgID int,
@PLTypeID int

AS

DECLARE @tblCond TABLE (conditionID int)
declare @minCID int, @rc int

-- ensure org matches pltypeid
IF NOT EXISTS (select PLTypeID from dbo.ams_memberProfessionalLicenseTypes where orgID = @orgID AND PLTypeID = @PLTypeID)
	RETURN -1

BEGIN TRAN			

	DELETE FROM dbo.ams_memberProfessionalLicenses
	WHERE PLTypeID = @PLTypeID
		IF @@ERROR <> 0 goto on_error

	-- member fields
	DELETE FROM dbo.ams_memberFields 
	WHERE fieldCode like 'mpl\_' + cast(@PLTypeID as varchar(10)) + '\_%' escape '\'
	and fieldsetID in (
		select fieldSetID 
		from dbo.ams_memberFieldSets as mfs 
		inner join dbo.sites as s on s.siteid = mfs.siteid and s.orgid = @orgid
	)
		IF @@ERROR <> 0 goto on_error

	-- group assignment conditions
	INSERT INTO @tblCond (conditionID) 
	SELECT conditionID 
	FROM dbo.ams_virtualGroupConditions 
	WHERE orgID = @orgID 
	AND fieldCode like 'mpl\_' + cast(@PLTypeID as varchar(10)) + '\_%' escape '\'
		IF @@ERROR <> 0 goto on_error

	declare @CIDList varchar(max)
	select @CIDList = COALESCE(@CIDList + ',', '') + cast(conditionID as varchar(10)) from @tblCond group by conditionID
	IF @CIDList is not null BEGIN
		EXEC @rc = dbo.ams_deleteVirtualGroupCondition @orgID=@orgID, @conditionIDList=@CIDList
			IF @@ERROR <> 0 OR @RC <> 0 goto on_error
	END

	-- new account form and update member
	delete 
	from ams_memberFieldUsageProfessionalLicenses
	where PlTypeID = @PLTypeID
		IF @@ERROR <> 0 goto on_error

	DELETE FROM dbo.ams_memberProfessionalLicenseTypes
	WHERE PLTypeID = @PLTypeID
		IF @@ERROR <> 0 goto on_error

	EXEC dbo.ams_reorderMemberProfessionalLicenseTypes @orgID
		IF @@ERROR <> 0 goto on_error

IF @@TRANCOUNT > 0 COMMIT TRAN
EXEC dbo.ams_createVWMemberData @orgID=@orgID
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1

GO