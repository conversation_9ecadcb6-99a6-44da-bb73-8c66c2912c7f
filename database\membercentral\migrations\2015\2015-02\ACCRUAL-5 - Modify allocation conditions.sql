use membercentral
GO
ALTER PROC dbo.cache_members_populateMemberConditionCache_ACCT_EQ_ALLOCSUM
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on (gl.revenueGL = saleT.creditGLAccountID or gl.revenueGL = saleT.debitGLAccountID) and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_EQ_ALLOCSUM'	
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on (gl.revenueGL = saleT.creditGLAccountID or gl.revenueGL = saleT.debitGLAccountID) and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_EQ_ALLOCSUM'	
) as tmp
group by memberid, conditionID, conditionValue
having sum(allocAmt) = conditionValue

GO

ALTER PROC dbo.cache_members_populateMemberConditionCache_ACCT_NEQ_ALLOCSUM
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on (gl.revenueGL = saleT.creditGLAccountID or gl.revenueGL = saleT.debitGLAccountID) and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_NEQ_ALLOCSUM'
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on (gl.revenueGL = saleT.creditGLAccountID or gl.revenueGL = saleT.debitGLAccountID) and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_NEQ_ALLOCSUM'	
) as tmp
group by memberid, conditionID, conditionValue
having sum(allocAmt) <> conditionValue

GO

ALTER PROC dbo.cache_members_populateMemberConditionCache_ACCT_LTE_ALLOCSUM
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on (gl.revenueGL = saleT.creditGLAccountID or gl.revenueGL = saleT.debitGLAccountID) and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_LTE_ALLOCSUM'	
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on (gl.revenueGL = saleT.creditGLAccountID or gl.revenueGL = saleT.debitGLAccountID) and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_LTE_ALLOCSUM'	
) as tmp
group by memberid, conditionID, conditionValue
having sum(allocAmt) <= conditionValue

GO

ALTER PROC dbo.cache_members_populateMemberConditionCache_ACCT_LT_ALLOCSUM
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on (gl.revenueGL = saleT.creditGLAccountID or gl.revenueGL = saleT.debitGLAccountID) and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_LT_ALLOCSUM'	
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on (gl.revenueGL = saleT.creditGLAccountID or gl.revenueGL = saleT.debitGLAccountID) and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_LT_ALLOCSUM'	
) as tmp
group by memberid, conditionID, conditionValue
having sum(allocAmt) < conditionValue

GO

ALTER PROC dbo.cache_members_populateMemberConditionCache_ACCT_GTE_ALLOCSUM
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on (gl.revenueGL = saleT.creditGLAccountID or gl.revenueGL = saleT.debitGLAccountID) and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_GTE_ALLOCSUM'	
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on (gl.revenueGL = saleT.creditGLAccountID or gl.revenueGL = saleT.debitGLAccountID) and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_GTE_ALLOCSUM'	
) as tmp
group by memberid, conditionID, conditionValue
having sum(allocAmt) >= conditionValue

GO

ALTER PROC dbo.cache_members_populateMemberConditionCache_ACCT_GT_ALLOCSUM
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on (gl.revenueGL = saleT.creditGLAccountID or gl.revenueGL = saleT.debitGLAccountID) and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_GT_ALLOCSUM'	
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on (gl.revenueGL = saleT.creditGLAccountID or gl.revenueGL = saleT.debitGLAccountID) and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_GT_ALLOCSUM'	
) as tmp
group by memberid, conditionID, conditionValue
having sum(allocAmt) > conditionValue

GO

ALTER PROC dbo.cache_members_populateMemberConditionCache_ACCT_EXISTS_ALLOCSUM
AS

insert into #cache_members_conditions_shouldbe
select m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
inner join #tblMembers as m on m.memberid = m2.activeMemberID
inner join #tblAccSplitGL as gl on (gl.revenueGL = saleT.creditGLAccountID or gl.revenueGL = saleT.debitGLAccountID) and gl.conditionID = tblc.conditionID
inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
where tblc.subProc = 'ACCT_EXISTS_ALLOCSUM'
	union
select m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
inner join #tblMembers as m on m.memberid = m2.activeMemberID
inner join #tblAccSplitGL as gl on (gl.revenueGL = saleT.creditGLAccountID or gl.revenueGL = saleT.debitGLAccountID) and gl.conditionID = tblc.conditionID
inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
where tblc.subProc = 'ACCT_EXISTS_ALLOCSUM' 

GO

ALTER PROC dbo.cache_members_populateMemberConditionCache_ACCT_BETWEEN_ALLOCSUM
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValueLower, accsplit.conditionValueUpper
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on (gl.revenueGL = saleT.creditGLAccountID or gl.revenueGL = saleT.debitGLAccountID) and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_BETWEEN_ALLOCSUM'
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValueLower, accsplit.conditionValueUpper
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on (gl.revenueGL = saleT.creditGLAccountID or gl.revenueGL = saleT.debitGLAccountID) and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_BETWEEN_ALLOCSUM'
) as tmp
group by memberid, conditionID, conditionValueLower, conditionValueUpper
having sum(allocAmt) between conditionValueLower and conditionValueUpper

GO


ALTER PROC dbo.cache_members_populateMemberConditionCache_ACCT_NOTEXISTS_ALLOCSUM
AS

IF OBJECT_ID('tempdb..#tblCondAccNotExist') IS NOT NULL
	DROP TABLE #tblCondAccNotExist

select m.memberid, tblc.conditionID
into #tblCondAccNotExist
from #tblCondALL as tblc
inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
inner join #tblMembers as m on m.memberid = m2.activeMemberID
inner join #tblAccSplitGL as gl on (gl.revenueGL = saleT.creditGLAccountID or gl.revenueGL = saleT.debitGLAccountID) and gl.conditionID = tblc.conditionID
inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
where tblc.subProc = 'ACCT_NOTEXISTS_ALLOCSUM' 
	union
select m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
inner join #tblMembers as m on m.memberid = m2.activeMemberID
inner join #tblAccSplitGL as gl on (gl.revenueGL = saleT.creditGLAccountID or gl.revenueGL = saleT.debitGLAccountID) and gl.conditionID = tblc.conditionID
inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
where tblc.subProc = 'ACCT_NOTEXISTS_ALLOCSUM' 

insert into #cache_members_conditions_shouldbe
select distinct mOuter.memberid, tblcOuter.conditionID
from #tblCondALL as tblcOuter
inner join #tblMembers as mOuter on mOuter.memberid = mOuter.memberID
where tblcOuter.subProc = 'ACCT_NOTEXISTS_ALLOCSUM' 
	except
select memberid, conditionID
from #tblCondAccNotExist

IF OBJECT_ID('tempdb..#tblCondAccNotExist') IS NOT NULL
	DROP TABLE #tblCondAccNotExist

GO

