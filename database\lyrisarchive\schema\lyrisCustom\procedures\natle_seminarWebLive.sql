ALTER PROC dbo.natle_seminarWebLive

AS

DECLARE @errorSubject VARCHAR(100), @errmsg nvarchar(2048), @listName VARCHAR(100), @now DATETIME = GETDATE();
SET @listName = 'seminarweblive';

IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList;
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList2') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList2;
CREATE TABLE #swl_eligibleForNatleMarketingList (platform VARCHAR(20), orgcode VARCHAR(10), memberID INT, 
	membernumber VARCHAR(100), fullname VARCHAR(100), email VARCHAR(100), usernameLC_ VARCHAR(100), 
	domain_ VARCHAR(250));

EXEC membercentral.customApps.dbo.natle_seminarWebLiveEligible;

IF NOT EXISTS (SELECT * FROM membercentral.dataTransfer.dbo.natle_seminarWebLiveEligible) BEGIN
	SET @errorSubject = 'Error Updating SeminarWebLive Marketing List for NATLE';
	SET @errmsg = 'customApps.dbo.natle_seminarWebLiveEligible ended with no rows in table dataTransfer.dbo.natle_seminarWebLiveEligible. Check for timeout or other issues.';
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;	
END ELSE BEGIN

	INSERT INTO #swl_eligibleForNatleMarketingList (platform, orgcode, memberID, membernumber, fullname, email)
	SELECT platform, orgcode, memberID, membernumber, fullname, email
	FROM membercentral.dataTransfer.dbo.natle_seminarWebLiveEligible;

	CREATE INDEX IX_swl_eligibleForNatleMarketingList_email ON #swl_eligibleForNatleMarketingList (email asc);

	UPDATE #swl_eligibleForNatleMarketingList 
	SET usernameLC_ = LEFT(email,CHARINDEX('@',email)-1),
		domain_ = RIGHT(email,LEN(email)-CHARINDEX('@',email));

	-- update fullname/association based on matching email address
	UPDATE m 
	SET association_ = tmp.orgcode,
		fullname_ = tmp.fullname
	FROM #swl_eligibleForNatleMarketingList tmp
	INNER JOIN trialslyris1.dbo.members_ m ON m.list_ = @listName
		AND m.emailaddr_ = tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS
		AND (m.association_ <> tmp.orgcode COLLATE SQL_Latin1_General_CP1_CI_AS or m.fullname_ <> tmp.fullname COLLATE SQL_Latin1_General_CP1_CI_AS);

	-- mark email addresses that are NOT in temp table as expired (and not admins)
	UPDATE m 
	SET membertype_ = 'expired',
		ExpireDate_ = @now
	FROM trialslyris1.dbo.members_ m 
	LEFT OUTER JOIN #swl_eligibleForNatleMarketingList tmp ON m.emailaddr_ = tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS
	WHERE m.list_ = @listName 
	AND tmp.email IS NULL
	AND isListAdm_ <> 'T'
	AND m.membertype_ in ('normal','held');

	-- reactivate previously expired email addresses that are in temp table
	UPDATE m 
	SET membertype_ = 'normal',
		ExpireDate_ = NULL
	FROM trialslyris1.dbo.members_ m 
	INNER JOIN #swl_eligibleForNatleMarketingList tmp ON m.emailaddr_ = tmp.email COLLATE SQL_Latin1_General_CP1_CI_AS
		AND m.list_ = @listName
		AND m.membertype_ = 'expired';

	-- remove all rows from temp table for which email address is already on list (prep for inserting new rows)
	DELETE ec
	FROM #swl_eligibleForNatleMarketingList ec
	WHERE exists (
		SELECT usernameLc_, domain_
		FROM trialslyris1.dbo.members_
		WHERE list_ = @listname 
		AND usernameLc_ = ec.usernameLc_ COLLATE SQL_Latin1_General_CP1_CI_AS
		AND domain_ = ec.domain_ COLLATE SQL_Latin1_General_CP1_CI_AS
	);

	-- delete dupes in temp table caused by SEMWEB purchases (accounts that should be merged in TS Admin or sharing emails)
	SELECT *, ROW_NUMBER() OVER(PARTITION BY usernameLC_, domain_ ORDER BY memberNumber) AS rowNum
	INTO #swl_eligibleForNatleMarketingList2
	FROM #swl_eligibleForNatleMarketingList;

	DELETE FROM #swl_eligibleForNatleMarketingList2
	WHERE rowNum > 1;

	INSERT INTO trialslyris1.dbo.members_ (DateJoined_, domain_, emailaddr_, fullname_, list_, usernameLc_, ExternalMemberID, association_,MCEmailKey,MCEmailKey_usernameLC,MCEmailKey_domain)
	SELECT @now, domain_, email, fullname, @listName, usernameLc_, memberNumber, orgcode,
		MCEmailKey=convert(varchar(75),HASHBYTES('SHA2_256',@listName + '|' + usernameLC_ + '@' + domain_),2),
		usernameLc_,domain_
	FROM #swl_eligibleForNatleMarketingList2;
END
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList;
IF OBJECT_ID('tempdb..#swl_eligibleForNatleMarketingList2') IS NOT NULL
	DROP TABLE #swl_eligibleForNatleMarketingList2;
GO
