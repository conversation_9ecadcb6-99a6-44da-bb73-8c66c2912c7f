
declare @orgID int, @siteID int

select @orgID=orgID, @siteID=siteID
from dbo.sites 
where siteCode = 'MD'

declare @errTable TABLE (id int identity(1,1), errReason varchar(100), memberID int)
declare @tblComm TABLE (id int identity(1,1), subscriberID int, memberID int, subStartDate datetime, subEndDate datetime, graceEndDate datetime,
							newRootSubscriberID int, newSubStartDate datetime, newSubEndDate datetime, newGraceEndDate datetime)

declare @SectionsRootUID varchar(100)
declare @SectionsTypeUID varchar(100)

select @SectionsTypeUID = t.uid, @SectionsRootUID = s.uid
from dbo.sub_types t
inner join dbo.sub_subscriptions s on s.typeid = t.typeID
where t.siteID = @siteID
and t.typeName = 'Sections'
and s.subscriptionName = 'Sections'

insert into @tblComm (subscriberID, memberID, subStartDate, subEndDate, graceEndDate)
select s.subscriberID, s.memberID, s.subStartDate, s.subEndDate, s.graceEndDate --, *
from dbo.sub_subscribers s
inner join dbo.sub_subscriptions subs
	on subs.subscriptionID = s.subscriptionID
	and s.parentSubscriberID is null
	and subs.uid <> @SectionsRootUID
	and s.statusid = 1
inner join dbo.sub_types t
	on t.typeID = subs.typeID
	and t.siteID = @siteID
	and t.uid = @SectionsTypeUID
order by s.memberid


--select *
update tc
	set tc.newRootSubscriberID = s.subscriberID,
	tc.newSubStartDate = s.subStartDate,
	tc.newSubEndDate = s.subEndDate,
	tc.newGraceEndDate = s.graceEndDate
from @tblComm tc
inner join dbo.sub_subscribers s
	on s.memberID = tc.memberID
inner join dbo.sub_subscriptions subs
	on subs.subscriptionID = s.subscriptionID
	and subs.uid = @SectionsRootUID
where s.statusID = 1


update s
	set	
		s.rootSubscriberID = tc.newRootSubscriberID,
		s.parentSubscriberID = tc.newRootSubscriberID
from dbo.sub_subscribers s
inner join @tblComm tc
	on tc.subscriberID = s.subscriberID

-- update subs to have the correct root.
update s
	set	
		s.rootSubscriberID = tc.newRootSubscriberID
from dbo.sub_subscribers s
inner join @tblComm tc
	on tc.subscriberID = s.parentsubscriberID 

exec sub_fixSubscriberTreeOrderBulk @siteID, null, null

select * from @tblComm
