use membercentral;
GO

ALTER PROC [dbo].[ams_deleteVirtualGroupRuleGroup]
@orgID int,
@ruleGroupIDList varchar(max)

AS

-- split rulegroups
DECLARE @tblRG TABLE (ruleGroupID int)
INSERT INTO @tblRG (ruleGroupID)
select rg.ruleGroupID
from dbo.fn_intListToTable(@ruleGroupIDList,',') as tmp
inner join dbo.ams_virtualGroupRuleGroups as rg on rg.ruleGroupID = tmp.listitem
inner join dbo.ams_virtualGroupRules as r on r.ruleID = rg.ruleID
where r.orgID = @orgID

-- queue member groups over people in the removed groups (@runSchedule=2 indicates delayed processing) 
declare @itemGroupUID uniqueidentifier, @memberIDList varchar(max)
SELECT @memberIDList = COALESCE(@memberIDList + ',', '') + cast(cmg.memberID as varchar(10)) 
from @tblRG as tmp
inner join dbo.ams_virtualGroupRuleGroups as vgrg 
	on tmp.ruleGroupID = vgrg.ruleGroupID
inner join dbo.cache_members_groups as cmg 
	on vgrg.groupID = cmg.groupID
	and cmg.isVirtualDirect = 1
group by cmg.memberID

-- delete ruleGroup
DELETE from dbo.ams_virtualGroupRuleGroups
where ruleGroupID in (select ruleGroupID from @tblRG)

IF @memberIDList is not null
	EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberIDList, @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

RETURN 0
GO