ALTER PROC insertIntoLyrmembersubsets
                      @segNameStr varchar(60),
                      @siteID int,
                      @memberIdsStr varchar(4000),
                      @iteration varchar(10)
                 AS
                  BEGIN
 	                 DECLARE @segmentName varchar(60)
                      DECLARE @listName varchar(60)
                      DECLARE @memberIds varchar(4000)
                      DECLARE @memberIdStr varchar(16)
                      DECLARE @next_comma_pos int
              	     DECLARE @id_curr_pos int
                      DECLARE @memberId int
              	     DECLARE @num_of_successes int
              	     DECLARE @num_of_failures int
              	     DECLARE @total_processed int
              	     DECLARE @bad_memberIds varchar(200)
                      DECLARE @summary varchar(250)
                      DECLARE @subsetID int
 					 
 					 SET NOCOUNT ON 
  					 
                      SET @memberIds = RTRIM(@memberIdsStr)
                      SET @segmentName = RTRIM(@segNameStr) SET @num_of_successes = 0
              	     SET @num_of_failures = 0
              	     SET @total_processed = 0
                      SET @bad_memberIds = ''
                      IF @iteration='continue' or @iteration='last'
                      BEGIN
                         DECLARE @stringInt varchar(16)
                         DECLARE @storedvalues varchar(250)
                         SELECT @storedvalues = CTSummary_ FROM clicksummary_ WHERE SegmentName_ = @segmentName AND SiteID_ = @siteID
                         SET @next_comma_pos = CHARINDEX(',',@storedvalues,1)
                         SET @stringInt = SUBSTRING(@storedvalues,1,@next_comma_pos-1)
                         SET @num_of_successes = CAST(@stringInt AS int)
                         SET @id_curr_pos = 1 + @next_comma_pos
                         SET @next_comma_pos = CHARINDEX(',',@storedvalues,@id_curr_pos)
                         SET @stringInt = SUBSTRING(@storedvalues,@id_curr_pos,@next_comma_pos-@id_curr_pos)
                         SET @num_of_failures = CAST(@stringInt AS int)
                         IF @num_of_failures > 0
                         BEGIN
                           SET @id_curr_pos = 1 + @next_comma_pos
                           SET @next_comma_pos = CHARINDEX(',',@storedvalues,@id_curr_pos)
                           SET @stringInt = SUBSTRING(@storedvalues,@id_curr_pos,@next_comma_pos-@id_curr_pos)
                           SET @total_processed = CAST(@stringInt AS int)
                           SET @bad_memberIds = SUBSTRING(@storedvalues,@next_comma_pos+1,LEN(@storedvalues)-@next_comma_pos)
                         END
                         ELSE
                         BEGIN
                           SET @stringInt = SUBSTRING(@storedvalues,1+@next_comma_pos,LEN(@storedvalues)-@next_comma_pos)
                           SET @total_processed = CAST(@stringInt AS int)
                         END
                      END SET @id_curr_pos = 1
                      WHILE @id_curr_pos > 0
                      BEGIN
                          SET @next_comma_pos = CHARINDEX(',',@memberIds,@id_curr_pos)
                          IF @next_comma_pos>@id_curr_pos
                          BEGIN
                              SET @memberIdStr = SUBSTRING(@memberIds,@id_curr_pos,@next_comma_pos-@id_curr_pos)
                              SET @id_curr_pos = @next_comma_pos+1
                          END
                          ELSE
                          BEGIN
                              SET @memberIdStr = SUBSTRING(@memberIds,@id_curr_pos,LEN(@memberIds))
                              SET @id_curr_pos = 0
                          END
                          SET @memberId = CAST(@memberIdStr AS int)
                          SET @listName = NULL SELECT @listName = m.List_ FROM members_ m, lists_ l, topics_ t, sites_ s WHERE m.MemberID_ = @memberId AND m.List_ = l.Name_ AND l.Topic_ = t.Title_ AND t.SiteName_ = s.Name_ AND s.SiteID_= @siteID
                          IF @listName IS NOT NULL AND @listName != ''
                          BEGIN
                              EXEC @subsetID = createInSubsets @segmentName, @listName
                              EXEC createInLyrmembersubsets @segmentName, @listName, @memberId
 			                 SET @num_of_successes = @num_of_successes + 1
                          END
                          ELSE
                          BEGIN
 			                 SET @num_of_failures = @num_of_failures + 1
 			                 IF @num_of_failures<11
 					             SET @bad_memberIds = @bad_memberIds + ' ' + @memberIdStr
 		                 END
 	                     SET @total_processed = @total_processed + 1
                      END IF @iteration='last' or @iteration='one'
                      BEGIN
                        SET @summary = 'Successes: ' + CAST(@num_of_successes AS varchar) + '
' + 'Failures: ' + CAST(@num_of_failures AS varchar) + '
' + 'Total processed: ' + CAST(@total_processed AS varchar)
 	                   IF @num_of_failures>0
 	                   BEGIN
 	                     IF @num_of_failures > 10
 		                     SET @bad_memberIds = @bad_memberIds + '...'
 	                     SET @summary = @summary + '
' + 'Bad member ids:' + @bad_memberIds
 	                   END
                      END
                      ELSE
                      BEGIN
                         SET @summary = CAST(@num_of_successes AS varchar) + ',' + CAST(@num_of_failures AS varchar) + ',' + CAST(@total_processed AS varchar)
                         IF @num_of_failures > 0
                           SET @summary = @summary + ',' + @bad_memberIds
                      END
                      IF @iteration='one' OR @iteration='first' 
                        INSERT INTO clicksummary_ (SegmentName_, CTSummary_, SiteID_) VALUES (@segmentName,@summary,@siteID) 
                      ELSE 
                        UPDATE clicksummary_ SET CTSummary_ = @summary WHERE SegmentName_ = @segmentName AND SiteID_ = @siteID 
                  END
GO
