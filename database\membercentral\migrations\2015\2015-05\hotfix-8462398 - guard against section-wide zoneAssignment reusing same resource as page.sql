use membercentral;
GO
ALTER PROCEDURE [dbo].[cms_getPageStructureByID] 
@siteID int,
@pageID int,
@languageID int,
@ovrMode varchar(30),
@processMobileOverrides bit,
@templateTypeName varchar(100)

AS


DECLARE @templateID int, @sectionID int, @pgResourceTypeID int, @modeID int, @ovrModeID int, @defaultTemplateID int, @defaultModeID int, @rootSectionID int, @pageIncludePlacements int, @siteResourceStatusID int
DECLARE @viewFunctionID int, @editFunctionID int
DECLARE @layoutInfo TABLE (templateID int, templateTypeName varchar(100), modeID int, modeName varchar(100), templateFilename varchar(100), sitecode varchar(10), orgcode varchar(10))
DECLARE @pageSectionPath TABLE (templateID int, modeID int, sectionID int, parentSectionID int, dataLevel int PRIMARY KEY,includePlacements bit)
DECLARE @zoneAssignments TABLE (autoid int IDENTITY(1,1), zoneID int, assignmentLevel varchar(10), sectionID int, siteResourceID int PRIMARY KEY, sortOrder int, dataLevel int, isSSL bit, appInstanceID int, appInstanceName varchar(100), appTypeName varchar(100), appWidgetInstanceID int, appInstanceResourceID int, appWidgetTypeName varchar(100), appWidgetInstanceName varchar(100),contentID int,enableSocialMediaSharing bit, resourceTypeID int, resourceType varchar(100), resourceTypeClassName varchar(100), objectType char(1), viewRightPrintID int, editContentRightPrintID int)

set @defaultTemplateID = 1
set @defaultModeID = 1

select @ovrModeID = modeID from cms_pageModes where modeName = @ovrMode
select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

select @viewFunctionID = functionID from cms_siteResourceFunctions where functionName = 'view' 
select @editFunctionID = functionID from cms_siteResourceFunctions where functionName = 'editContent' 

select 
	@pgResourceTypeID = sr.resourceTypeID, 
	@sectionID = p.sectionID, 
	@modeID = pm.modeID, 
	@templateID = isnull(mobilept.templateID,pt.templateid),
	@pageIncludePlacements = p.inheritPlacements
from cms_pages p (nolock)
inner join sites s
	on s.siteID = p.siteID
inner join cms_siteResources sr (nolock)
	on p.siteResourceID = sr.siteResourceID
	and pageID = @pageID
     and sr.siteResourceStatusID = @siteResourceStatusID
inner join dbo.cache_cms_derivedPageSectionSettings dpss (nolock)
	on dpss.sectionID = p.sectionID
inner join dbo.cms_pageTemplates pt (nolock)
	on pt.templateid = coalesce(p.ovTemplateID,dpss.ovTemplateID,@defaultTemplateID)
inner join dbo.cms_pageModes pm (nolock)
	on pm.modeID = coalesce(@ovrModeID,p.ovModeID,dpss.ovModeID,@defaultModeID)
left outer join dbo.cms_pageTemplates mobilept (nolock)
	on mobilept.templateid = coalesce(p.ovTemplateIDMobile,dpss.ovTemplateIDMobile)
	and mobilept.templateTypeID = pt.templateTypeID
	and @processMobileOverrides = 1


select @rootSectionID = sectionID from cms_pageSections where siteID = @siteID and parentSectionID is null

if (nullif(@ovrMode,'') is not null)
	select @ovrModeID = modeID from cms_pageModes where modeName = @ovrMode

-- find all zone assignments for page, not considering pageMode
insert into @zoneAssignments (zoneid, siteResourceID, assignmentLevel, sectionID, sortOrder, dataLevel,resourceTypeID,	resourceType, resourceTypeClassName,isSSL,contentID,enableSocialMediaSharing,objecttype)
select pzr.zoneid, pzr.siteResourceID, 'page' as assignmentLevel, 0 as sectionID, pzr.sortOrder, 0 as dataLevel, srt.resourceTypeID,srt.resourceType,srtc.resourceTypeClassName, isnull(c.isSSL,1),c.contentID, c.enableSocialMediaSharing,
objecttype = case
	when c.siteresourceID is not null then 'C'
	when ai.siteresourceID is not null then 'A'
	when awi.siteresourceID is not null then 'W'
end

from dbo.cms_pageZonesResources as pzr
inner join dbo.cms_siteResources sr
	on pzr.siteResourceID = sr.siteResourceID
	and pzr.pageID = @pageID
     and sr.siteResourceStatusID = @siteResourceStatusID
inner join dbo.cms_siteResourceTypes srt ON sr.resourceTypeID = srt.resourceTypeID
inner join dbo.cms_siteResourceTypeClasses srtc ON srt.resourceTypeClassID = srtc.resourceTypeClassID
left outer join dbo.cms_content c on sr.siteResourceID = c.siteResourceID 
left outer join dbo.cms_applicationInstances ai on sr.siteResourceID = ai.siteResourceID
left outer join dbo.cms_applicationWidgetInstances awi on sr.siteResourceID = awi.siteResourceID


if @pageIncludePlacements = 1
BEGIN
	insert into @zoneAssignments (zoneid, siteResourceID, assignmentLevel, sectionID, sortOrder, dataLevel,resourceTypeID,	resourceType, resourceTypeClassName,isSSL,contentID,enableSocialMediaSharing,objecttype)
	select pszr.zoneid, pszr.siteResourceID, 
		assignmentLevel = case when rps.sectionID = @rootSectionID then 'site' else 'section' end,
		rps.sectionID, pszr.sortOrder, (rps.depth * -1) as dataLevel, srt.resourceTypeID,	srt.resourceType, srtc.resourceTypeClassName, isnull(c.isSSL,1),c.contentID, c.enableSocialMediaSharing,
		objecttype = case
			when c.siteresourceID is not null then 'C'
			when ai.siteresourceID is not null then 'A'
			when awi.siteresourceID is not null then 'W'
		end
			
	from dbo.cms_pageSectionsZonesResources pszr
	inner join dbo.cache_cms_recursivePageSections rps
		on rps.startSectionID = @sectionID
		and rps.sectionid = pszr.sectionid
		and rps.includePlacements = 1
	inner join dbo.cms_siteResources sr
		on pszr.siteResourceID = sr.siteResourceID
		  and sr.siteResourceStatusID = @siteResourceStatusID
	inner join dbo.cms_siteResourceTypes srt ON sr.resourceTypeID = srt.resourceTypeID
	inner join dbo.cms_siteResourceTypeClasses srtc ON srt.resourceTypeClassID = srtc.resourceTypeClassID
	left outer join dbo.cms_content c on sr.siteResourceID = c.siteResourceID 
	left outer join dbo.cms_applicationInstances ai on sr.siteResourceID = ai.siteResourceID
	left outer join dbo.cms_applicationWidgetInstances awi on sr.siteResourceID = awi.siteResourceID
    where pszr.siteResourceID not in (select siteResourceID from @zoneAssignments)
END

-- update view and editContentPerms
update za set
    viewRightPrintID = srfrp_view.rightPrintID, 
    editContentRightPrintID = srfrp_editContent.rightPrintID
from @zoneAssignments za
left outer join cache_perms_siteResourceFunctionRightPrints srfrp_view
    on srfrp_view.siteResourceID = za.siteResourceID and srfrp_view.functionID= @viewFunctionID
left outer join cache_perms_siteResourceFunctionRightPrints srfrp_editContent
    on srfrp_editContent.siteResourceID = za.siteResourceID and srfrp_editContent.functionID= @editFunctionID


-- update Application related columns
if exists (select top 1 siteresourceID from @zoneAssignments where objecttype = 'A')
	update za
	set
		appInstanceID = ai.applicationInstanceID,
		appInstanceName = ai.applicationInstanceName, 
		appTypeName = at.applicationTypeName, 
		appInstanceResourceID = ai.siteResourceID,
		isSSL = 1
	from @zoneAssignments za
	inner join dbo.cms_applicationInstances ai
		on za.siteResourceID = ai.siteResourceID
		and za.objecttype = 'A'
	inner join dbo.cms_applicationTypes at ON ai.applicationTypeID = at.applicationTypeID

--update Application Widget related columns
if exists (select top 1 siteresourceID from @zoneAssignments where objecttype = 'W')
	update za
	set
		appInstanceID = ai.applicationInstanceID,
		appInstanceName = ai.applicationInstanceName, 
		appTypeName = at.applicationTypeName, 
		appInstanceResourceID = ai.siteResourceID, 
		appWidgetInstanceID = awi.applicationWidgetInstanceID , 
		appWidgetTypeName = awt.applicationWidgetTypeName, 
		appWidgetInstanceName = awi.applicationWidgetInstanceName,
		isSSL = 1
	from @zoneAssignments za
	inner join dbo.cms_applicationWidgetInstances awi
		on za.siteResourceID = awi.siteResourceID
		and za.objecttype = 'W'
	inner join dbo.cms_applicationInstances ai ON awi.applicationInstanceID = ai.applicationInstanceID
	inner join dbo.cms_applicationWidgetTypes awt ON awi.applicationWidgetTypeID = awt.applicationWidgetTypeID
	inner join dbo.cms_applicationTypes at ON ai.applicationTypeID = at.applicationTypeID



-- is page offered in requested page language? if not, use site default language
IF NOT EXISTS (select pageLanguageID from dbo.cms_pageLanguages where pageID = @pageID and languageID = @languageID)
	SELECT @languageID = defaultLanguageID from sites where siteID = @siteID
select cast(isNull((
select page.pageID, page.pageName, page.allowReturnAfterLogin, page.siteResourceID as pageSiteResourceID,
	isnull(sites.siteID,0) as siteID,
	isnull(sites.siteCode,0) as siteCode,
	isnull(organizations.orgID,0) as orgID,
	isnull(organizations.orgCode,0) as orgCode,
	ISNULL(ps.sectionID,'') as sectionID, 
	ISNULL(ps.sectionName,'') as sectionName, 
	ISNULL(ps.sectionCode,'') as sectionCode,
	@languageID as pageLanguageID,
	ISNULL(pl.pageTitle,'') as pageTitle, 
	ISNULL(pl.pageDesc,'') as pageDesc, 
	ISNULL(pl.keywords,'') as keywords, 
	ISNULL(pt.templateFileName,'DefaultTemplate') as layoutFileName,
	ISNULL(pt.templateID,'') as templateID,
	ISNULL(pt.siteResourceID,'') as templateSiteResourceID,
	ISNULL(ptt.templateTypeName,'') as templateTypeName,
	ISNULL(pm.modeName,'Normal') as layoutMode, 
	ISNULL(templateSite.sitecode,'') as layoutSiteCode,
	ISNULL(templateOrg.orgcode,'') as layoutOrgCode, 
	pageZone.zoneName, 
	siteResource.siteResourceID,
	ISNULL(sites.siteID,0) as siteID,
	ISNULL(sites.siteCode,'') as siteCode,
	ISNULL(organizations.orgID,0) as orgID,
	ISNULL(organizations.orgCode,'') as orgCode,
	ISNULL(siteResource.resourceType,'') as resourceType,
	ISNULL(siteResource.resourceTypeClassName,'') as resourceClass,
	ISNULL(siteResource.assignmentLevel,'') as assignmentLevel,
	ISNULL(siteResource.sectionID,'') as assignmentSectionID,
	ISNULL(siteResource.isSSL,'') as isSSL, 
	ISNULL(siteResource.appInstanceID,'') as appInstanceID, 
	ISNULL(siteResource.appInstanceName,'') as appInstanceName, 
	ISNULL(siteResource.appTypeName,'') as appTypeName, 
	ISNULL(siteResource.appWidgetInstanceID,'') as appWidgetInstanceID, 
	ISNULL(siteResource.appInstanceResourceID,'') as appInstanceResourceID, 
	ISNULL(siteResource.appWidgetTypeName,'') as appWidgetTypeName, 
	ISNULL(siteResource.appWidgetInstanceName,'') as appWidgetInstanceName,
	ISNULL(siteResource.contentID,'') as contentID,
	ISNULL(siteResource.enableSocialMediaSharing,'') as enableSocialMediaSharing,
    ISNULL(siteResource.viewRightPrintID,'') as viewRightPrintID,
    ISNULL(siteResource.editContentRightPrintID,'') as editContentRightPrintID
from dbo.cms_pages as page (nolock)
inner join dbo.sites (nolock)
	on sites.siteID = page.siteID
	and page.pageID = @pageID
inner join dbo.organizations (nolock)
	on sites.orgID = organizations.orgID
inner join dbo.cms_pageSections (nolock) as ps
	on ps.sectionID = page.sectionID
inner join dbo.cms_pageTemplates pt (nolock)
	on pt.templateid = @templateID
inner join dbo.cms_pageTemplateTypes ptt (nolock)
	on pt.templateTypeID = ptt.templateTypeID
	and ptt.templateTypeName = @templateTypeName
inner join dbo.cms_pageModes pm (nolock)
	on pm.modeID = @modeID
left outer join dbo.sites templateSite (nolock)
	inner join dbo.organizations templateOrg (nolock)
		on templateSite.orgid = templateOrg.orgid
on templateSite.siteid = pt.siteid
inner join dbo.cms_pageLanguages as pl (nolock) 
	on pl.pageID = page.pageid and pl.languageid = @languageID
inner join dbo.cms_pageTemplatesModesZones as ptmz (nolock)
	on pm.modeid = ptmz.modeid and ptmz.templateID = pt.templateID
inner join dbo.cms_pageZones as pageZone (nolock)
	on pageZone.zoneid = ptmz.zoneid
left outer join @zoneAssignments as siteResource
	on ptmz.zoneID = siteResource.zoneid
order by siteResource.zoneid, siteResource.dataLevel desc, siteResource.sortOrder
for xml auto, root('pageStructure')
),'<pageStructure />') as xml) as pageStructureXML
OPTION(RECOMPILE)


RETURN 0
