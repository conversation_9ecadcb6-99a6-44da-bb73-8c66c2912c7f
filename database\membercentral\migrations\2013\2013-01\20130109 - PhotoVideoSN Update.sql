ALTER PROCEDURE [dbo].[cms_createApplicationInstancePhotoGallery]
	@siteid int,
	@languageID int,
	@sectionID int,
	@isVisible bit,
	@pageName varchar(50),
	@pageTitle varchar(200),
	@pagedesc varchar(400),
	@zoneID int,
	@pageTemplateID int,
	@pageModeID int,
	@pgResourceTypeID int,
	@pgParentResourceID int = null,
	@allowReturnAfterLogin bit,
	@applicationInstanceName varchar(100),
	@applicationInstanceDesc varchar(200),
	@allowSubGalleries bit,
	@creatorMemberID int,
	@applicationInstanceID int OUTPUT,
	@siteResourceID int OUTPUT,
	@pageID int OUTPUT
AS
BEGIN

	SET NOCOUNT ON;

	DECLARE 
		@applicationTypeID int, 
		@rootAlbumID int,
		@albumName varchar(50),
		@galleryID int,
		@addInheritance bit

	select @albumName = 'Root'

	select @applicationTypeID = applicationTypeID from cms_applicationTypes where applicationTypeName = 'PhotoGallery';
	
	exec dbo.cms_createApplicationInstance
		@siteid = @siteid,
		@languageID = @languageID,
		@sectionID = @sectionID,
		@applicationTypeID = @applicationTypeID,
		@isVisible = @isVisible,
		@pageName = @pageName,
		@pageTitle = @pageTitle,
		@pagedesc = @pagedesc,
		@zoneID = @zoneID,
		@pageTemplateID = @pageTemplateID,
		@pageModeID = @pageModeID,
		@pgResourceTypeID = @pgResourceTypeID,
		@pgParentResourceID = @pgParentResourceID,
		@allowReturnAfterLogin = @allowReturnAfterLogin,
		@applicationInstanceName = @applicationInstanceName,
		@applicationInstanceDesc = @applicationInstanceDesc,
		@applicationInstanceID = @applicationInstanceID OUTPUT,
		@siteResourceID = @siteResourceID OUTPUT,
		@pageID = @pageID OUTPUT


	insert into dbo.pg_gallery (applicationInstanceID, allowSubGalleries)
	values (@applicationInstanceID, @allowSubGalleries)

	SELECT @galleryID = SCOPE_IDENTITY()
	SELECT @addInheritance = 1
	
	EXEC dbo.pg_createAlbum
		@siteID = @siteID, 
		@galleryID = @galleryID, 
		@parentAlbumID = NULL, 
		@albumName = @albumName, 
		@albumDesc = NULL, 
		@columnsPerPage = 6,
		@itemsPerPage = 18, 
		@creatorMemberID=@creatorMemberID,
		@addInheritance=@addInheritance,
		@albumID = @rootAlbumID OUTPUT
	
	UPDATE dbo.pg_gallery
	SET rootAlbumID = @rootAlbumID
	WHERE galleryID = @galleryID


END
GO

ALTER PROCEDURE [dbo].[cms_createApplicationInstanceVideoGallery]
	@siteid int,
	@languageID int,
	@sectionID int,
	@isVisible bit,
	@pageName varchar(50),
	@pageTitle varchar(200),
	@pagedesc varchar(400),
	@zoneID int,
	@pageTemplateID int,
	@pageModeID int,
	@pgResourceTypeID int,
	@pgParentResourceID int = null,
	@allowReturnAfterLogin bit,
	@applicationInstanceName varchar(100),
	@applicationInstanceDesc varchar(200),
	@allowSubGalleries bit,
	@creatorMemberID int,
	@applicationInstanceID int OUTPUT,
	@siteResourceID int OUTPUT,
	@pageID int OUTPUT
AS
BEGIN

	SET NOCOUNT ON;

	DECLARE 
		@applicationTypeID int, 
		@rootAlbumID int,
		@albumName varchar(50),
		@galleryID int,
		@addInheritance int

	select @albumName = 'Root'

	select @applicationTypeID = applicationTypeID from cms_applicationTypes where applicationTypeName = 'VideoGallery';
	
	exec dbo.cms_createApplicationInstance
		@siteid = @siteid,
		@languageID = @languageID,
		@sectionID = @sectionID,
		@applicationTypeID = @applicationTypeID,
		@isVisible = @isVisible,
		@pageName = @pageName,
		@pageTitle = @pageTitle,
		@pagedesc = @pagedesc,
		@zoneID = @zoneID,
		@pageTemplateID = @pageTemplateID,
		@pageModeID = @pageModeID,
		@pgResourceTypeID = @pgResourceTypeID,
		@pgParentResourceID = @pgParentResourceID,
		@allowReturnAfterLogin = @allowReturnAfterLogin,
		@applicationInstanceName = @applicationInstanceName,
		@applicationInstanceDesc = @applicationInstanceDesc,
		@applicationInstanceID = @applicationInstanceID OUTPUT,
		@siteResourceID = @siteResourceID OUTPUT,
		@pageID = @pageID OUTPUT


	insert into dbo.vg_gallery (applicationInstanceID, allowSubGalleries)
	values (@applicationInstanceID, @allowSubGalleries)

	SELECT @galleryID = SCOPE_IDENTITY()
	SELECT @addInheritance = 1
	
	EXEC dbo.vg_createAlbum
		@siteID = @siteID, 
		@galleryID = @galleryID, 
		@parentAlbumID = NULL, 
		@albumName = @albumName, 
		@albumDesc = NULL, 
		@columnsPerPage = 6,
		@itemsPerPage = 18, 
		@creatorMemberID=@creatorMemberID,
		@addInheritance=@addInheritance,
		@albumID = @rootAlbumID OUTPUT
	
	UPDATE dbo.vg_gallery
	SET rootAlbumID = @rootAlbumID
	WHERE galleryID = @galleryID


END
GO

ALTER PROCEDURE [dbo].[cms_createApplicationInstanceSocialNetwork] 
@siteid int,
@languageID int,
@sectionID int,
@isVisible bit,
@pageName varchar(50),
@pageTitle varchar(200),
@pagedesc varchar(400),
@zoneID int,
@pageTemplateID int,
@subPageTemplateID int,
@masterSiteID int,
@addressTypeID int,
@emailTypeID int,
@phoneTypeID int,
@pageModeID int,
@pgResourceTypeID int,
@pgParentResourceID int=null,
@allowReturnAfterLogin bit,
@applicationInstanceName varchar(100),
@applicationInstanceDesc varchar(200),
@creatorMemberID int,
@applicationInstanceID int OUTPUT,
@siteResourceID int OUTPUT,
@pageID int OUTPUT

AS

BEGIN TRAN

declare @rc int, @snApplicationTypeID int, @socialNetworkID int, @appCreatedSectionResourceTypeID int
declare @masterOrgID int, @masterSocialNetworkGroupID int, @masterSocialNetworkID int, @masterSocialNetworkApplicationInstanceID int
declare @rootSectionID int
declare @appWidgetTypeID int, @applicationWidgetInstanceID int, @applicationWidgetSiteResourceID int, @zoneIDA int, @zoneIDB int, @trashID int;
declare @participateFunctioniD int, @viewFunctionID int, @SNResourceTypeID int, @AddBlogFunctionID int, @editOwnFunctionID int, @deleteOwnFunctionID int, @canCommentFunctionID int


declare 
	@poolTypeID int,
	@SNMasterInstancePoolRoleTypeID int,
	@SNChildInstancePoolRoleTypeID int,
	@SNPoolID int



select @applicationInstanceID = null
select @siteResourceID = null
select @masterOrgID = null
select @pageID = null
select @snApplicationTypeID = applicationTypeID, @SNResourceTypeID = resourceTypeID from dbo.cms_applicationTypes where applicationTypeName = 'SocialNetwork'
select @appWidgetTypeID = applicationWidgetTypeID from dbo.cms_applicationWidgetTypes where applicationWidgetTypeName='socialNetworkNavigation' and applicationTypeID = @snApplicationTypeID
select @zoneIDA = dbo.fn_getZoneID('A')
select @zoneIDB = dbo.fn_getZoneID('B')

select 
	@participateFunctioniD = dbo.fn_getResourceFunctionID('Participate', @SNResourceTypeID),
	@viewFunctionID = dbo.fn_getResourceFunctionID('View', @SNResourceTypeID),
	@AddBlogFunctionID = dbo.fn_getResourceFunctionID('View', dbo.fn_getResourceTypeID('Blog')),
	@editOwnFunctionID = dbo.fn_getResourceFunctionID('editOwn', dbo.fn_getResourceTypeID('Blog')),
	@deleteOwnFunctionID = dbo.fn_getResourceFunctionID('deleteOwn', dbo.fn_getResourceTypeID('Blog')),
	@canCommentFunctionID = dbo.fn_getResourceFunctionID('canComment', dbo.fn_getResourceTypeID('Blog')),
	@appCreatedSectionResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedSection'),
	@masterOrgID =dbo.fn_getOrgIDFromSiteID(@masterSiteID),
	@poolTypeID = dbo.fn_getResourcePoolTypeID ('SNConfig'),
	@SNMasterInstancePoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'MasterInstance'),
	@SNChildInstancePoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'ChildInstance')

if @masterSiteID <> @siteID
	select 
		@masterSocialNetworkID=socialNetworkID,
		@masterSocialNetworkGroupID = masterSocialNetworkGroupID,
		@masterSocialNetworkApplicationInstanceID = ai.applicationInstanceID,
		@SNPoolID = srpm.poolID
	from sn_socialNetworks sn
	inner join cms_applicationInstances ai
		on sn.applicationinstanceID = ai.applicationInstanceID
		and ai.siteID = @masterSiteID
	inner join cms_siteResources sr on ai.siteResourceID = sr.siteResourceID
	inner join cms_siteResourceStatuses srs
		on sr.siteResourceStatusID = srs.siteResourceStatusID
		and srs.siteResourceStatusDesc = 'Active'
	inner join cms_siteResourcePoolMembers srpm
		on srpm.siteResourceID = ai.siteResourceID
		and srpm.poolRoleTypeID = @SNMasterInstancePoolRoleTypeID
	inner join cms_siteResourcePools srp
		on srpm.poolID = srp.poolID
else
	select @masterSocialNetworkID=null,
		@masterSocialNetworkGroupID = null,
		@masterSocialNetworkApplicationInstanceID = null,
		@SNPoolID = null


-- create instance
EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, 
		@applicationTypeID=@snApplicationTypeID, @isVisible=@isVisible, @pageName=@pageName, 
		@pageTitle=@pageTitle, @pageDesc=@pagedesc, @zoneID=@zoneID, @pagetemplateid=@pageTemplateID,
		@pageModeID=@pageModeID, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID = @pgParentResourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
		@applicationInstanceName=@applicationInstanceName, @applicationInstanceDesc=@applicationInstanceDesc, 
		@applicationInstanceID=@applicationInstanceID OUTPUT, 
		@siteresourceID=@siteResourceID OUTPUT, 
		@pageID=@pageID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
print 'Created SN instance'
-- create section for Social Network
EXEC @rc = dbo.cms_createPageSection 
		@siteID=@siteID, 
		@sectionResourceTypeID=@appCreatedSectionResourceTypeID,
		@ovTemplateID=@subPageTemplateID, 
		@ovModeID=NULL, 
		@parentSectionID=@sectionID, 
		@sectionName=@pageName,
		@sectionCode=@pageName, 
		@inheritPlacements=0,
		@sectionID=@rootSectionID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


-- update parentSiteResourceID of section
update sr 
set sr.parentSiteResourceID = @siteResourceID
from cms_pageSections s
inner join cms_siteResources sr on s.siteResourceID = sr.siteResourceID	and s.sectionID = @rootSectionID
IF @@ERROR <> 0 GOTO on_error

-- create Navigation Widget and assign to zone A of section


EXEC @rc = dbo.cms_createApplicationWidgetInstance
	@siteid = @siteid,
	@applicationInstanceID = @applicationInstanceID,
	@applicationWidgetTypeID = @appWidgetTypeID,
	@applicationWidgetInstanceName = 'Social Network Navigation',
	@applicationWidgetInstanceDesc = 'Navigation',
	@applicationWidgetInstanceID = @applicationWidgetInstanceID OUTPUT,
	@siteResourceID = @applicationWidgetSiteResourceID  OUTPUT
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

exec @rc = dbo.cms_createPageSectionZoneResource
	@sectionID=@rootSectionID,
	@zoneID=@zoneIDA,
	@siteResourceID = @applicationWidgetSiteResourceID,
	@pszrID = @trashID OUTPUT
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- create a group for social network users if the masterSiteID and siteID are the same, this implies
-- a master site is being created.
IF @masterSiteID = @siteid BEGIN
	EXEC @rc = dbo.ams_createGroup 
			@orgID=@masterOrgID, 
			@groupCode=null, 
			@groupName='Social Network Users', 
			@groupDesc='Social Network Users', 
			@isSystemGroup=0, 
			@allowManualAssignment=0, 
			@parentGroupID=null, 
			@hideOnGroupLists=1,
			@groupID=@masterSocialNetworkGroupID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	update dbo.ams_groups
	set hideOnGroupLists = 1
	where groupID = @masterSocialNetworkGroupID
	IF @@ERROR <> 0 GOTO on_error


	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@siteResourceID, @include=1, @functionID=@participateFunctionID, @roleID=null, @groupID=@masterSocialNetworkGroupID, @memberID=null, @inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error



END

-- add Social Network
INSERT INTO dbo.sn_socialNetworks (applicationInstanceID, masterSiteID, rootSectionID, masterOrgID, masterSocialNetworkGroupID, masterSocialNetworkID,defaultPageAlias, addressTypeID, emailTypeID,phoneTypeID)
VALUES (@applicationInstanceID, @masterSiteID, @rootSectionID, @masterOrgID, @masterSocialNetworkGroupID,@masterSocialNetworkID,'home', @addressTypeID, @emailTypeID,@phoneTypeID)
	IF @@ERROR <> 0 GOTO on_error
	select @socialNetworkID = SCOPE_IDENTITY()

IF @masterSiteID <> @siteID BEGIN
	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@siteResourceID, @poolRoleTypeID=@SNChildInstancePoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
END
ELSE BEGIN

	print 'Doing Master Instance stuff'

	update sn_socialNetworks
	set masterSocialNetworkID = @socialNetworkID
	where socialNetworkID = @socialNetworkID

	set @masterSocialNetworkID = @socialNetworkID;


	declare
		@MemberManagerPoolRoleTypeID int,
		@SharedAppWallPoolRoleTypeID int,
		@SharedWidgetWallAddEntryPoolRoleTypeID int,
		@HomePoolRoleTypeID int,
		@ProfileEditorPoolRoleTypeID int,
		@ProfileViewerPoolRoleTypeID int,
		@OrgProfileViewerPoolRoleTypeID int,
		@SharedAppBlogPoolRoleTypeID int,
		@SharedAppPhotosPoolRoleTypeID int,
		@SharedAppVideosPoolRoleTypeID int,
		@SharedAppCommunityManagerPoolRoleTypeID int,
		@SharedAppWelcomePoolRoleTypeID int;


	select @MemberManagerPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'MemberManager')
	select @SharedAppWallPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'SharedAppWall')
	select @SharedWidgetWallAddEntryPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'SharedWidgetWallAddEntry')
	select @HomePoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'Home')
	select @ProfileEditorPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'ProfileEditor')
	select @ProfileViewerPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'ProfileViewer')
	select @OrgProfileViewerPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'OrgProfileViewer')
	select @SharedAppBlogPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'SharedAppBlog')
	select @SharedAppPhotosPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'SharedAppPhotos')
	select @SharedAppVideosPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'SharedAppVideos')
	select @SharedAppCommunityManagerPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'SharedAppCommunityManager')
	select @SharedAppWelcomePoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'SharedAppWelcome')

	-- create SNConfig Resource Pool
	exec dbo.cms_createSiteResourcePool
		@poolTypeID=@poolTypeID, @poolName='Social Network Config', @poolID = @SNPoolID OUTPUT

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@siteResourceID, @poolRoleTypeID=@SNMasterInstancePoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	
	declare
		@appSubPageResourceTypeID int,
		@subAppPageName varchar(100),
		@subAppPageID int,
		@snNetworkHomePageID int,
		@snNetworkHomeApplicationInstanceID int,
		@snViewProfilePageID int,
		@subAppApplicationInstanceID int,
		@subAppResourceID int,
		@snHomeAppTypeID int,
		@snmemberManagerAppTypeID int,
		@sneditProfileAppTypeID int,
		@snviewProfileAppTypeID int,
		@snviewOrgProfileAppTypeID int,
		@snblogAppTypeID int,
		@snstatusAppTypeID int,
		@snphotosAppTypeID int,
		@snPhotosApplicationInstanceID int,
		@snVideosAppTypeID int,
		@communityManagerAppTypeID int,
		@SNWelcomeApplicationTypeID int,
		@MasterInstanceUserGroupID int;

	declare
		@upcomingEventsWidgetTypeID int,
		@recentUploadsWidgetTypeID int,
		@recentPhotosWidgetTypeID int,
		@profilePictureWidgetTypeID int,
		@myStatusWidgetTypeID int,
		@myFriendsWidgetTypeID int,
		@activityFeedWidgetTypeID int,
		@applicationWidgetInstanceSiteResourceID int,
		@myFriendsWidgetInstanceSiteResourceID int,
		@profilePicWidgetInstanceSiteResourceID int,
		@myStatusWidgetInstanceSiteResourceID int,
		@activityFeedWidgetInstanceSiteResourceID int,
		@recentVideosWidgetTypeID int,
		@recentBlogsWidgetTypeID int,
		@recentCenterListMessagesWidgetTypeID int,
		@recentActiveMembersWidgetTypeID int;

	select @appSubPageResourceTypeID = dbo.fn_getResourceTypeID('ApplicationSubPage')
	select @snHomeAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'socialNetworkHome'
	select @snmemberManagerAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'MemberManager'
	select @sneditProfileAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'ProfileManager'
	select @snviewProfileAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'profileViewer'
	select @snviewOrgProfileAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'orgProfileViewer'

	select @snblogAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'Blog'
	select @snstatusAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'Blog'
	select @snphotosAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'PhotoGallery'
	select @snVideosAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'VideoGallery'
	select @communityManagerAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'CommunityCenter'
	select @SNWelcomeApplicationTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'SocialNetworkWelcome'


	select @recentPhotosWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationTypeID = @snphotosAppTypeID and applicationWidgetTypeName = 'recentPhotos'
	select @profilePictureWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationTypeID = @snApplicationTypeID and applicationWidgetTypeName = 'profilePic'
	select @myStatusWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationTypeID = @snstatusAppTypeID and applicationWidgetTypeName = 'addEntry'
	select @myFriendsWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationTypeID = @snApplicationTypeID and applicationWidgetTypeName = 'myFriends'
	select @activityFeedWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationTypeID = @snApplicationTypeID and applicationWidgetTypeName = 'socialNetworkActivity'
	select @recentVideosWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationTypeID = @snVideosAppTypeID and applicationWidgetTypeName = 'recentVideos'
	select @recentBlogsWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationTypeID = @snblogAppTypeID and applicationWidgetTypeName = 'recentBlogs'
	select @recentCenterListMessagesWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationTypeID = @communityManagerAppTypeID and applicationWidgetTypeName = 'recentCenterListMessages'
	select @recentActiveMembersWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationTypeID = @snmemberManagerAppTypeID and applicationWidgetTypeName = 'recentActiveMembers'


	select @MasterInstanceUserGroupID = groupID
	from ams_groups
	where parentGroupID is null
	and groupName = 'public'
	and orgID = @masterOrgID

	-- create shared Widgets for Master Network

	EXEC @rc = dbo.cms_createApplicationWidgetInstance
		@siteid=@siteID,
		@applicationInstanceID=@applicationInstanceID,
		@applicationWidgetTypeID=@profilePictureWidgetTypeID,
		@applicationWidgetInstanceName='Social Network Profile Picture',
		@applicationWidgetInstanceDesc='Profile Picture',
		@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
		@siteResourceID=@profilePicWidgetInstanceSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

/*
	EXEC @rc = dbo.cms_createApplicationWidgetInstance
		@siteid=@siteID,
		@applicationInstanceID=@applicationInstanceID,
		@applicationWidgetTypeID=@myFriendsWidgetTypeID,
		@applicationWidgetInstanceName='Social Network Friends',
		@applicationWidgetInstanceDesc='Friends',
		@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
		@siteResourceID=@myFriendsWidgetInstanceSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
*/
	EXEC @rc = dbo.cms_createApplicationWidgetInstance
		@siteid=@siteID,
		@applicationInstanceID=@applicationInstanceID,
		@applicationWidgetTypeID=@activityFeedWidgetTypeID ,
		@applicationWidgetInstanceName='Activity Feed',
		@applicationWidgetInstanceDesc='Social Networking Activity Feed',
		@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
		@siteResourceID=@activityFeedWidgetInstanceSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	-- create shared subApplications for Master network
	set @subAppPageName = @pageName + 'Wall';
	EXEC @rc = dbo.cms_createApplicationInstanceBlog @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@isVisible=@isVisible, @pageName=@subAppPageName, 
			@pageTitle='Social Network Wall', @pageDesc='Wall for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,@subPageTemplateID = null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@applicationInstanceName='Social Network Wall', @applicationInstanceDesc='Wall for Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@subAppPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@AddBlogFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@editOwnFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@deleteOwnFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@canCommentFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error




	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@subAppPageID,@socialNetworkID,'wall',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@SharedAppWallPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = dbo.cms_createApplicationWidgetInstance
		@siteid=@siteID,
		@applicationInstanceID=@subAppApplicationInstanceID,
		@applicationWidgetTypeID=@myStatusWidgetTypeID ,
		@applicationWidgetInstanceName='My Status',
		@applicationWidgetInstanceDesc='Social Networking Status Tool',
		@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
		@siteResourceID=@myStatusWidgetInstanceSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	
	update dbo.sn_socialNetworks
	set statusApplicationWidgetInstanceID = @applicationWidgetInstanceID
	where socialNetworkID = @socialNetworkID

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@myStatusWidgetInstanceSiteResourceID, @poolRoleTypeID=@SharedWidgetWallAddEntryPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error



	set @subAppPageName = @pageName + 'Welcome';
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@applicationTypeID=@SNWelcomeApplicationTypeID,
			@isVisible=@isVisible, @pageName=@subAppPageName, 
			@pageTitle='Social Network Welcome', @pageDesc='Welcome for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@applicationInstanceName='Social Network Welcome', @applicationInstanceDesc='Welcome for Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@subAppPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=@MasterInstanceUserGroupID, @memberID=null, @inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@subAppPageID,@socialNetworkID,'welcomePage',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@SharedAppWelcomePoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	set @subAppPageName = @pageName + 'Home';
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@applicationTypeID=@snHomeAppTypeID,
			@isVisible=@isVisible, @pageName=@subAppPageName, 
			@pageTitle='Social Network Home', @pageDesc='Homepage for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@applicationInstanceName='Social Network Home', @applicationInstanceDesc='Homepage for Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@snNetworkHomePageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@snNetworkHomePageID,@socialNetworkID,'home',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@HomePoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


/*	
	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snNetworkHomePageID, @zoneID=@zoneIDB, @siteResourceID=@myFriendsWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
*/


	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snNetworkHomePageID, @zoneID=@zoneIDB, @siteResourceID=@profilePicWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snNetworkHomePageID, @zoneID=@zoneID, @siteResourceID=@myStatusWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snNetworkHomePageID, @zoneID=@zoneID, @siteResourceID=@activityFeedWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	set @subAppPageName = @pageName + 'CommunityManager';
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@applicationTypeID=@communityManagerAppTypeID,
			@isVisible=@isVisible, @pageName=@subAppPageName, 
			@pageTitle='Communities', @pageDesc='Communities for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@applicationInstanceName='Communities', @applicationInstanceDesc='Communities for the Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@subAppPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@subAppPageID,@socialNetworkID,'communitymanager',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@SharedAppCommunityManagerPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = dbo.cms_createApplicationWidgetInstance
		@siteid=@siteID,
		@applicationInstanceID=@subAppApplicationInstanceID,
		@applicationWidgetTypeID=@recentCenterListMessagesWidgetTypeID ,
		@applicationWidgetInstanceName='Recent List Messages',
		@applicationWidgetInstanceDesc='Recent List Messages',
		@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
		@siteResourceID=@applicationWidgetInstanceSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	


	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snNetworkHomePageID, @zoneID=@zoneID, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	set @subAppPageName = @pageName + 'MemberManager';
	EXEC @rc = dbo.cms_createApplicationInstanceMemberManager @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@isVisible=@isVisible, @pageName=@subAppPageName, 
			@pageTitle='Social Network Member Manager', @pageDesc=' Member Manager for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,@subPageTemplateID = null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@applicationInstanceName='Social Network Member Manager', @applicationInstanceDesc=' Member Manager for Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@subAppPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@subAppPageID,@socialNetworkID,'membermanager',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@MemberManagerPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error




	EXEC @rc = dbo.cms_createApplicationWidgetInstance
		@siteid=@siteID,
		@applicationInstanceID=@subAppApplicationInstanceID,
		@applicationWidgetTypeID=@recentActiveMembersWidgetTypeID ,
		@applicationWidgetInstanceName='Recently Active Users',
		@applicationWidgetInstanceDesc='Recently Active Users',
		@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
		@siteResourceID=@applicationWidgetInstanceSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	


	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snNetworkHomePageID, @zoneID=@zoneID, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error



	set @subAppPageName = @pageName + 'ProfileEditor';
	EXEC @rc = dbo.cms_createApplicationInstanceProfileManager @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@isVisible=@isVisible, @pageName=@subAppPageName, 
			@pageTitle='Social Network Profile Manager', @pageDesc=' Profile Manager for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,@subPageTemplateID = null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@applicationInstanceName='Social Network Profile Manager', @applicationInstanceDesc=' Profile Manager for Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@subAppPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@subAppPageID,@socialNetworkID,'editProfile',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error


	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@ProfileEditorPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error



	set @subAppPageName = @pageName + 'ProfileViewer';
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@applicationTypeID=@snviewProfileAppTypeID,
			@isVisible=@isVisible, @pageName=@subAppPageName, 
			@pageTitle='Social Network Profile Viewer', @pageDesc='Profile Viewer for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@applicationInstanceName='Social Network Profile Viewer', @applicationInstanceDesc='Profile Viewer for Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@snViewProfilePageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@snViewProfilePageID,@socialNetworkID,'viewProfile',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snViewProfilePageID, @zoneID=@zoneIDB, @siteResourceID=@profilePicWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

/*
	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snViewProfilePageID, @zoneID=@zoneIDB, @siteResourceID=@myFriendsWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
*/

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@ProfileViewerPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error



	set @subAppPageName = @pageName + 'OrgProfileViewer';
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@applicationTypeID=@snviewOrgProfileAppTypeID,
			@isVisible=@isVisible, @pageName=@subAppPageName, 
			@pageTitle='Social Network Organization Profile Viewer', @pageDesc='Organization Profile Viewer for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@applicationInstanceName='Social Network Organization Profile Viewer', @applicationInstanceDesc='Organization Profile Viewer for Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@subAppPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@subAppPageID,@socialNetworkID,'viewOrgProfile',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@OrgProfileViewerPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error



	set @subAppPageName = @pageName + 'Photos';
	EXEC @rc = dbo.cms_createApplicationInstancePhotoGallery @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@isVisible=@isVisible,@pageName=@subAppPageName, 
			@pageTitle='Social Network Photos', @pageDesc='Photos for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@allowSubGalleries = 1,
			@applicationInstanceName='Social Network Photos', @applicationInstanceDesc='Photos for Social Network', @creatorMemberID=@creatorMemberID,
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@subAppPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@subAppPageID,@socialNetworkID,'photos',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@SharedAppPhotosPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	EXEC @rc = dbo.cms_createApplicationWidgetInstance
		@siteid=@siteID,
		@applicationInstanceID=@subAppApplicationInstanceID,
		@applicationWidgetTypeID=@recentPhotosWidgetTypeID,
		@applicationWidgetInstanceName='Social Network Recent Photos',
		@applicationWidgetInstanceDesc='Recent Photos',
		@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
		@siteResourceID=@applicationWidgetInstanceSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	
	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snNetworkHomePageID, @zoneID=@zoneIDB, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snViewProfilePageID, @zoneID=@zoneIDB, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	set @subAppPageName = @pageName + 'Videos';
	EXEC @rc = dbo.cms_createApplicationInstanceVideoGallery @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@isVisible=@isVisible,@pageName=@subAppPageName, 
			@pageTitle='Social Network Videos', @pageDesc='Videos for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@allowSubGalleries = 1, @creatorMemberID=@creatorMemberID,
			@applicationInstanceName='Social Network Videos', @applicationInstanceDesc='Videos for Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@subAppPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@subAppPageID,@socialNetworkID,'videos',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@SharedAppVideosPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = dbo.cms_createApplicationWidgetInstance
		@siteid=@siteID,
		@applicationInstanceID=@subAppApplicationInstanceID,
		@applicationWidgetTypeID=@recentVideosWidgetTypeID,
		@applicationWidgetInstanceName='Social Network Recent Videos',
		@applicationWidgetInstanceDesc='Recent Videos',
		@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
		@siteResourceID=@applicationWidgetInstanceSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	
	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snNetworkHomePageID, @zoneID=@zoneIDB, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snViewProfilePageID, @zoneID=@zoneIDB, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error



	set @subAppPageName = @pageName + 'Blog';
	EXEC @rc = dbo.cms_createApplicationInstanceBlog @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@isVisible=@isVisible, @pageName=@subAppPageName, 
			@pageTitle='Social Network Blog', @pageDesc='Blog for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,@subPageTemplateID = null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@applicationInstanceName='Social Network Blog', @applicationInstanceDesc='Blog for Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@subAppPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@AddBlogFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@editOwnFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@deleteOwnFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@canCommentFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error




	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@subAppPageID,@socialNetworkID,'blog',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error


	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@SharedAppBlogPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = dbo.cms_createApplicationWidgetInstance
		@siteid=@siteID,
		@applicationInstanceID=@subAppApplicationInstanceID,
		@applicationWidgetTypeID=@recentBlogsWidgetTypeID,
		@applicationWidgetInstanceName='Social Network Recent Blogs',
		@applicationWidgetInstanceDesc='Recent Blogs',
		@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
		@siteResourceID=@applicationWidgetInstanceSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	
	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snNetworkHomePageID, @zoneID=@zoneIDB, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snViewProfilePageID, @zoneID=@zoneIDB, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- CREATE USER GUIDE FOR THE SOCIAL NETWORK
	DECLARE @userGuidePageID int, @siteResourceStatusID int
	DECLARE @contentID int, @userGuideContentSiteResourceID int, @resourceTypeID int

	SELECT	@resourceTypeID = dbo.fn_getResourceTypeID('ApplicationSubPage') 
	SELECT	@siteResourceStatusID = dbo.fn_getResourceStatusID('Active') 

	EXEC  @rc = dbo.cms_createPage @siteid=@siteID, @languageID=@languageID, @resourceTypeID=@resourceTypeID
		, @siteResourceStatusID=@siteResourceStatusID
		, @pgParentResourceID=@siteResourceID
		, @isVisible=1, @sectionID=@rootSectionID, @ovTemplateID=null, @ovModeID=null, @pageName='UserGuide'
		, @pageTitle='UserGuide'
		, @pageDesc='User Guide', @keywords='', @inheritPlacements=1, @allowReturnAfterLogin=1
		, @checkReservedNames=1, @pageID=@userGuidePageID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	INSERT INTO sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	VALUES (@userGuidePageID,@socialNetworkID,'userguide',1,null)
	IF @@ERROR <> 0 GOTO on_error

	SELECT	@resourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedContent')

	EXEC  @rc = cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @resourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = 1,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'UserGuide',
		@contentDesc = 'User Guide',
		@rawContent = '',
		@contentID = @contentID OUTPUT,
		@siteResourceID = @userGuideContentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	SELECT @zoneID = dbo.fn_getZoneID('Main')

	EXEC  @rc = cms_createPageZoneResource
		@pageID = @userGuidePageID,
		@zoneID = @zoneID,
		@siteResourceID = @userGuideContentSiteResourceID,
		@pzrID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	SELECT	@viewFunctionID = dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('ApplicationCreatedContent'))

	EXEC	@rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@userGuideContentSiteResourceID, @include=1
	, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID
	, @inheritedRightsFunctionID=@viewFunctionID, @resourceRightID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

END

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO



