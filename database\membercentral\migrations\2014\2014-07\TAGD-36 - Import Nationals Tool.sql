use membercentral
GO

-- turn on navigation item
/*
update admin_navigation
set isActive = 1, showInNav=1
where navname = 'Import National Files'
GO
*/

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[tr_importTransactions_toTemp]') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].[tr_importTransactions_toTemp]
GO

CREATE PROC dbo.tr_importTransactions_toTemp
@orgid int, 
@payProfileCode varchar(20),
@tmptbl varchar(30),
@pathToExport varchar(100),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

IF OBJECT_ID('tempdb..#tblAccErrors') IS NOT NULL 
	DROP TABLE #tblAccErrors
CREATE TABLE #tblAccErrors (rowid int IDENTITY(1,1), msg varchar(300), fatal bit)

declare @var_tmpCols varchar(20), @orgCode varchar(10), @payProfileID int, @payCashGLID int, @payCashGLName varchar(200),
	@payProfileGatewayID int, @dynSQL nvarchar(max), @good bit, @flatfile varchar(160), @bcpprefix varchar(50),
	@exportcmd varchar(400)

select @orgcode=orgcode from dbo.organizations where orgID = @orgid
set @var_tmpCols = '##tmpAccCols' + @orgcode
set @bcpprefix = @orgcode + '_acc_flat_' + convert(varchar(8),getdate(),112) + replace(convert(varchar(8),getdate(),108),':','')
set @flatfile = @pathToExport + @bcpprefix
select TOP 1 @payProfileID = mp.profileID, @payCashGLID = mp.GLAccountID, @payCashGLName = gl.accountname, @payProfileGatewayID = mp.gatewayID
	from dbo.mp_profiles as mp
	inner join dbo.tr_glAccounts as gl on gl.glAccountID = mp.glAccountID
	inner join dbo.sites as s on s.siteID = mp.siteID
	where s.orgID = @orgID
	and mp.profileCode = @payProfileCode 
	and mp.status = 'A'
	and gl.status = 'A'


/* ************** */
/* initial checks */
/* ************** */
IF @payProfileID is null or @payCashGLID is null or @payCashGLName is null BEGIN
	INSERT INTO #tblAccErrors (msg, fatal)
	VALUES ('Unable to locate Payment Method or Cash GL Account.',1)

	GOTO on_done
END


-- ******************************** 
-- ensure all columns exist (fatal)
-- ******************************** 
BEGIN TRY
	IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
		EXEC('DROP TABLE ' + @var_tmpCols)
	IF OBJECT_ID('tempdb..#tblAccOrgCols') IS NOT NULL 
		DROP TABLE #tblAccOrgCols
	IF OBJECT_ID('tempdb..#tblAccImportCols') IS NOT NULL 
		DROP TABLE #tblAccImportCols

	-- this will get the columns that should be there
	CREATE TABLE #tblAccOrgCols (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
		COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
		SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
		COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
		CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)

		select @dynSQL = '	
			select cast(null as varchar(max)) as saleDescription, cast(null as datetime) as saleDate,
				cast(null as varchar(50)) as saleMemberID, cast(null as money) as saleAmount,
				cast(null as varchar(200)) as saleRevenueGL, cast(null as varchar(30)) as saleID,
				cast(null as int) as rowID
			into ' + @var_tmpCols + '
			from dbo.tr_glAccounts
			where 1=0 '
		EXEC(@dynSQL)

		-- get cols
		INSERT INTO #tblAccOrgCols
		EXEC tempdb.dbo.SP_COLUMNS @var_tmpCols
		
		-- cleanup table no longer needed
		IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
			EXEC('DROP TABLE ' + @var_tmpCols)

	-- this will get the columns that are actually in the import
	CREATE TABLE #tblAccImportCols (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
		COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
		SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
		COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
		CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)

		-- get cols
		INSERT INTO #tblAccImportCols
		EXEC tempdb.dbo.SP_COLUMNS @tmptbl

	INSERT INTO #tblAccErrors (msg, fatal)
	select 'The column ' + org.column_name + ' is missing from your data.', 1
	from #tblAccOrgCols as org
	left outer join #tblAccImportCols as imp on imp.column_name = org.column_name
	where imp.table_name is null

	IF @@ROWCOUNT > 0
		GOTO on_done
END TRY
BEGIN CATCH
	INSERT INTO #tblAccErrors (msg, fatal)
	VALUES ('Unable to process accounting file.',1)

	GOTO on_done
END CATCH


-- **********************
-- columns checks (fatal)
-- **********************
BEGIN TRY
	select @dynSQL = 'ALTER TABLE ' + @tmptbl + ' ADD saleMID int null, saleRevenueGLAID int null, invoiceProfileID int null'
	EXEC(@dynSQL)

	-- check for null descriptions
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [saleDescription] varchar(max) not null;
			update ' + @tmptbl + ' set saleDescription = null where saleDescription = '''';
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' is missing the required SaleDescription.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE (saleDescription IS NULL OR ltrim(rtrim(saleDescription)) = '''')
			ORDER BY rowID'
		INSERT INTO #tblAccErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	-- ensure saleDate is datetime (allow nulls for this check)
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [saleDate] datetime null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO #tblAccErrors (msg, fatal)
		VALUES ('The column saleDate contains invalid dates.', 1)

	-- check for null dates
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [saleDate] datetime not null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' is missing the required SaleDate.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE saleDate IS NULL
			ORDER BY rowID'
		INSERT INTO #tblAccErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	-- match on membernumber
	select @dynSQL = 'update tmp
		set tmp.saleMID = m.memberid
		from ' + @tmptbl + ' as tmp
		inner join membercentral.dbo.ams_members as m on m.memberNumber = tmp.saleMemberID
			and m.orgID = ' + cast(@orgID as varchar(5)) + '
			and m.memberID = m.activeMemberID'
	EXEC(@dynSQL)

	-- check for missing member matches
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN saleMID int not null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(saleMemberID,'''') + '') does not match an existing MemberNumber.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE saleMID IS NULL
			ORDER BY rowID'
		INSERT INTO #tblAccErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	-- clean amounts
	select @dynSQL = 'update ' + @tmptbl + ' set saleAmount = membercentral.dbo.fn_regexReplace(saleAmount,''[^0-9\.\-\(\)]'','''') where saleAmount is not null;'
	EXEC(@dynSQL)

	-- ensure saleAmount is money (allow nulls for this check)
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [saleAmount] money null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO #tblAccErrors (msg, fatal)
		VALUES ('The column saleAmount contains invalid amounts.', 1)

	-- check for null or negative amounts
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [saleAmount] money not null;
			ALTER TABLE ' + @tmptbl + ' ADD CONSTRAINT amtCheck CHECK (saleAmount >= 0);
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(saleMemberID,'''') + '') does not have a positive sale amount.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE saleAmount IS NULL OR saleAmount < 0
			ORDER BY rowID'
		INSERT INTO #tblAccErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	-- match on GL
	select @dynSQL = 'update tmp
		set tmp.saleRevenueGLAID = gl.glAccountID,
			tmp.invoiceProfileID = gl.invoiceProfileID
		from ' + @tmptbl + ' as tmp
		inner join membercentral.dbo.tr_GlAccounts as gl on gl.accountCode = tmp.saleRevenueGL
			and gl.accountTypeID = 3
			and gl.status = ''A''
			and gl.orgID = ' + cast(@orgID as varchar(5))
	EXEC(@dynSQL)

	-- check for null GLs
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN saleRevenueGLAID int not null;
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN invoiceProfileID int not null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(saleMemberID,'''') + '') does not match an existing revenue GL Account Code.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE saleRevenueGLAID IS NULL OR invoiceProfileID is null
			ORDER BY rowID'
		INSERT INTO #tblAccErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ADD PRIMARY KEY (saleID);
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO #tblAccErrors (msg, fatal)
		VALUES ('The column saleID contains duplicate values.', 1)
END TRY
BEGIN CATCH
	INSERT INTO #tblAccErrors (msg, fatal)
	VALUES ('Unable to validate accounting file.',1)

	INSERT INTO #tblAccErrors (msg, fatal)
	VALUES (error_message(),0)

	GOTO on_done
END CATCH


IF (select count(*) from #tblAccErrors) = 0 BEGIN
	/* ***************** */
	/* get expanded data */
	/* ***************** */
	BEGIN TRY
		IF OBJECT_ID('tempdb..##tmpAccImportFull') IS NOT NULL 
			DROP TABLE ##tmpAccImportFull
		CREATE TABLE ##tmpAccImportFull (autoid int, batchID int, batchDate datetime, batchCode varchar(40), batchName varchar(400), 
			payTID int, payHistoryID int, payMemberID int, payDate datetime, payAmount money, payCashGLAID int, payID varchar(30), payReference varchar(max), 
			payDescription varchar(max), invoiceID int, invoiceProfileID int, saleTID int, saleDescription varchar(max), 
			saleDate datetime, saleMemberID int, saleAmount money, saleRevenueGLAID int, saleID varchar(30), allocTID int,
			payProfileID int, payProfileGatewayID int)

		select @dynSQL = ' 
			select rowID as autoid,
				cast(null as int) as batchID, DATEADD(dd,DATEDIFF(dd,0,saleDate),0) as batchDate, 
				convert(varchar(8),saleDate,112) + ''_' + cast(@payProfileID as varchar(10)) + '_' + cast(@payCashGLID as varchar(10)) + ''' as batchCode,
				convert(varchar(8),saleDate,112) + '' ' + @payProfileCode + ' ' + @payCashGLName + ''' as batchName,
				cast(null as int) as payTID, cast(null as int) as payHistoryID, saleMID as payMemberID, saleDate as payDate, 
				saleAmount as payAmount, ' + cast(@payCashGLID as varchar(10)) + ' as payCashGLAID, 
				saleID as payID, null as payReference, ''Payment for '' + saleDescription as payDescription, 
				cast(null as int) as invoiceID, invoiceProfileID, cast(null as int) as saleTID, 
				saleDescription, saleDate, saleMID as saleMemberID, saleAmount, saleRevenueGLAID, saleID,
				cast(null as int) as allocTID, ' + cast(@payProfileID as varchar(10)) + ' as payProfileID, ' + 
				cast(@payProfileGatewayID as varchar(10)) + ' as payProfileGatewayID  
			from ' + @tmptbl
		insert into ##tmpAccImportFull
		EXEC(@dynSQL)

	END TRY
	BEGIN CATCH
		INSERT INTO #tblAccErrors (msg, fatal)
		VALUES ('Unable to prepare final accounting data for final import.',1)

		INSERT INTO #tblAccErrors (msg, fatal)
		VALUES (error_message(),0)

		set @flatfile = ''

		GOTO on_done
	END CATCH


	-- *****************************
	-- check batches. cannot put new payments on an already-created and NOT OPEN batch.
	-- *****************************
	INSERT INTO #tblAccErrors (msg, fatal)
	select TOP 100 PERCENT 'We cannot add transactions from ' + convert(varchar(10),batchDate,101) + ' to closed or posted batches.', 1
	from ##tmpAccImportFull as tmp 
	where payAmount > 0
	and exists (
		select batchID 
		from dbo.tr_batches 
		where orgID = @orgID 
		and (batchCode = tmp.batchCode OR batchName = tmp.batchName)
		and statusID <> 1
	)
	group by convert(varchar(10),batchDate,101)
	order by convert(varchar(10),batchDate,101)
	
	IF @@ROWCOUNT > 0 BEGIN
		set @flatfile = ''
		GOTO on_done
	END


	-- ******************************** 
	-- dump flat table and format file and creation script
	-- would love to use xml format files, but it appears column names with spaces cause it to fail
	-- ******************************** 
	BEGIN TRY
		select @exportcmd = 'bcp ##tmpAccImportFull format nul -f ' + @flatfile + '.txt -n -T -S' + CAST(serverproperty('servername') as varchar(40))
		EXEC master..xp_cmdshell @exportcmd, NO_OUTPUT
		select @exportcmd = 'bcp ##tmpAccImportFull out ' + @flatfile + '.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
		EXEC master..xp_cmdshell @exportcmd, NO_OUTPUT

		declare @coltypes table (datatype varchar(16))          
		insert into @coltypes values('bit')          
		insert into @coltypes values('binary')          
		insert into @coltypes values('bigint')          
		insert into @coltypes values('int')          
		insert into @coltypes values('float')          
		insert into @coltypes values('datetime')          
		insert into @coltypes values('text')          
		insert into @coltypes values('image')          
		insert into @coltypes values('money')          
		insert into @coltypes values('uniqueidentifier')          
		insert into @coltypes values('smalldatetime')          
		insert into @coltypes values('tinyint')          
		insert into @coltypes values('smallint')          
		insert into @coltypes values('sql_variant')          
		select @dynSQL = ''
		select @dynSQL = @dynSQL +           
			case when charindex('(',@dynSQL,1)<=0 then '(' else '' end + '[' + Column_Name + '] ' +Data_Type +
			case when Data_Type in (Select datatype from @coltypes) then '' else  '(' end+
			case when data_type in ('real','decimal','numeric')  then cast(isnull(numeric_precision,'') as varchar)+','+
			case when data_type in ('real','decimal','numeric') then cast(isnull(Numeric_Scale,'') as varchar) end
			when data_type in ('nvarchar','varchar') and cast(isnull(Character_Maximum_Length,'') as varchar) = '-1' then 'max'
			when data_type in ('char','nvarchar','varchar','nchar') then cast(isnull(Character_Maximum_Length,'') as varchar) else '' end+
			case when Data_Type in (Select datatype from @coltypes)then '' else  ')' end+
			case when Is_Nullable='No' then ' Not null,' else ' null,' end
			from tempdb.Information_Schema.COLUMNS where Table_Name='##tmpAccImportFull'
			order by ordinal_position
		select @dynSQL='Create table ##xxx ' + substring(@dynSQL,1,len(@dynSQL)-1) +' )'            
		if dbo.fn_WriteFile(@flatfile + '.sql', @dynSQL, 1) = 0 BEGIN
			RAISERROR('Error raised in TRY block.', 16, 1);
		END
	END TRY
	BEGIN CATCH
		INSERT INTO #tblAccErrors (msg, fatal)
		VALUES ('Unable to save accounting data for final import.',1)

		set @flatfile = ''

		GOTO on_done
	END CATCH
END ELSE
	set @flatfile = ''


-- ************************
-- generate result xml file 
-- ************************
on_done:
	select @importResult = (
		select getdate() as "@date", @flatfile as "@flatfile", 
			isnull((select top 301 dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg", "@severity" = case fatal when 1 then 'fatal' else 'nonfatal' end
			from #tblAccErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE)
	
	IF OBJECT_ID('tempdb..#tblAccOrgCols') IS NOT NULL 
		DROP TABLE #tblAccOrgCols
	IF OBJECT_ID('tempdb..#tblAccImportCols') IS NOT NULL 
		DROP TABLE #tblAccImportCols
	IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
		EXEC('DROP TABLE ' + @var_tmpCols)
	IF OBJECT_ID('tempdb..#tblAccErrors') IS NOT NULL 
		DROP TABLE #tblAccErrors
	IF OBJECT_ID('tempdb..##tmpAccImportFull') IS NOT NULL 
		DROP TABLE ##tmpAccImportFull

RETURN 0
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[tr_importTransactions_toPerm]') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].[tr_importTransactions_toPerm]
GO

CREATE PROC dbo.tr_importTransactions_toPerm
@orgID int,
@siteID int,
@recordedByMemberID int,
@statsSessionID int,
@flatfile varchar(160),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

DECLARE @paymentcreditGLAccountID int, @saledebitGLAccountID int, @allocCreditGLAccountID int, @allocDebitGLAccountID int,
	@createSQL varchar(max), @cmd varchar(400), @invoicenum int

select @paymentcreditGLAccountID = glaccountid 
	from dbo.tr_GLAccounts 
	where orgID = @orgID
	and isSystemAccount = 1
	and GLCode = 'DEPOSITS'
	and [status] = 'A'
select @saledebitGLAccountID = glaccountid 
	from dbo.tr_GLAccounts 
	where orgID = @orgID
	and isSystemAccount = 1
	and GLCode = 'ACCOUNTSRECEIVABLE'
	and [status] = 'A'
select @allocCreditGLAccountID = @saledebitGLAccountID
select @allocDebitGLAccountID = @paymentcreditGLAccountID


IF OBJECT_ID('tempdb..#tblAccImpErrors') IS NOT NULL 
	DROP TABLE #tblAccImpErrors
CREATE TABLE #tblAccImpErrors (rowid int IDENTITY(1,1), msg varchar(300), fatal bit)

-- see if files exist
if dbo.fn_fileExists(@flatfile + '.sql') = 0
	or dbo.fn_fileExists(@flatfile + '.txt') = 0
	or dbo.fn_fileExists(@flatfile + '.bcp') = 0
BEGIN
	INSERT INTO #tblAccImpErrors (msg, fatal)
	VALUES ('Unable to read accounting data files.',1)

	GOTO on_done
END

-- **************
-- read in sql create script and create global temp table
-- **************
BEGIN TRY
	SET @createSQL = replace(dbo.fn_ReadFile(@flatfile + '.sql',0,1),'##xxx','##importAcctData')
	IF OBJECT_ID('tempdb..##importAcctData') IS NOT NULL
		DROP TABLE ##importAcctData
	EXEC(@createSQL)
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	VALUES ('Unable to create holding table for accounting data.',1)

	GOTO on_done
END CATCH


-- *******************
-- bcp in data
-- *******************
BEGIN TRY
	set @cmd = 'bcp ##importAcctData in ' + @flatfile + '.bcp -f ' + @flatfile + '.txt -n -T -S' + CAST(serverproperty('servername') as varchar(40))
	exec master..xp_cmdshell @cmd, NO_OUTPUT
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	VALUES ('Unable to import data into holding table.',1)

	GOTO on_done
END CATCH


-- *******************
-- immediately put into local temp table and drop global temp (memberid is a holder to be updated later)
-- *******************
BEGIN TRY
	IF OBJECT_ID('tempdb..#importAcctData') IS NOT NULL
		DROP TABLE #importAcctData
	select * into #importAcctData from ##importAcctData
	IF OBJECT_ID('tempdb..##importAcctData') IS NOT NULL
		DROP TABLE ##importAcctData
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	VALUES ('Unable to import data into local temporary table.',1)

	GOTO on_done
END CATCH


-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;


-- ******************************
-- Add temp fields to the tables
-- ******************************
BEGIN TRY
	SET @createSQL = '
		IF NOT EXISTS(SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N''dbo.tr_transactions'') AND name = ''tmpID'')
			ALTER TABLE dbo.tr_transactions ADD tmpID int NULL, tmpID2 int NULL, tmpID5 int NULL;
		IF NOT EXISTS(SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N''dbo.tr_paymentHistory'') AND name = ''tmpID2'')
			ALTER TABLE dbo.tr_paymentHistory ADD tmpID2 int NULL;
		IF NOT EXISTS(SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N''dbo.tr_invoices'') AND name = ''tmpID'')
			ALTER TABLE dbo.tr_invoices ADD tmpID int NULL;
		'
	EXEC(@createSQL)
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	VALUES ('Unable to add temporary fields.',1)

	GOTO on_doneT
END CATCH


/* ******* */
/* BATCHES */
/* ******* */
BEGIN TRY
	INSERT INTO dbo.tr_batches (orgID, statusID, batchCode, batchName, controlAmt, controlCount, depositDate, isSystemCreated, createdByMemberID, payProfileID)
	select distinct @orgID, 4, batchCode, batchName, 0, 0, batchDate, 0, @recordedByMemberID, payProfileID
	from #importAcctData
	where batchCode not in (select batchCode from dbo.tr_batches where orgID = @orgID and batchCode is not null)
	order by batchDate

	update tmp
	set tmp.batchID = b.batchID
	from #importAcctData as tmp
	inner join dbo.tr_batches as b on b.batchCode = tmp.batchCode and b.orgid = @orgID 

	IF EXISTS (select top 1 * from #importAcctData where batchID is null) BEGIN
		RAISERROR('Error raised in TRY block.', 16, 1);
	END
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	SELECT distinct top 100 PERCENT 'Could not identify required batch: ' + batchName, 1
	from #importAcctData 
	where batchID is null
	order by batchName

	GOTO on_doneT
END CATCH


/* ******** */
/* PAYMENTS */
/* ******** */
BEGIN TRY
	IF OBJECT_ID('tempdb..#tmpPayments') IS NOT NULL
		DROP TABLE #tmpPayments
	CREATE TABLE #tmpPayments (transactionID int, payID int)

	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, 
		parentTransactionID, amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, 
		statsSessionID, typeID, accrualDate, debitGLAccountID, creditGLAccountID, tmpID2)
		OUTPUT inserted.transactionid, inserted.tmpID2 
		INTO #tmpPayments(transactionID, payID)
	SELECT distinct @orgID, @siteID, 1, payDescription, null, payAmount, getdate(), payDate, payMemberID, 
		@recordedByMemberID, @statsSessionID, 2, payDate, payCashGLAID, @paymentcreditGLAccountID, payID
	from #importAcctData
	where payAmount > 0

	update tmp
	set tmp.payTID = tmpP.transactionID
	from #importAcctData as tmp
	inner join #tmpPayments as tmpP on tmpP.payID = tmp.payID

	IF OBJECT_ID('tempdb..#tmpPayments') IS NOT NULL
		DROP TABLE #tmpPayments

	IF EXISTS (select top 1 * from #importAcctData where payAmount > 0 and payTID is null) BEGIN
		RAISERROR('Error raised in TRY block.', 16, 1);
	END

	IF OBJECT_ID('tempdb..#tmpPaymentsHistory') IS NOT NULL
		DROP TABLE #tmpPaymentsHistory
	CREATE TABLE #tmpPaymentsHistory (historyID int, payID int)

	INSERT INTO dbo.tr_paymentHistory (paymentInfo, gatewayResponse, datePaid, statsSessionID, gatewayTransactionID, tmpID2)
		OUTPUT inserted.historyID, inserted.tmpID2 
		INTO #tmpPaymentsHistory(historyID, payID)
	select distinct 
		paymentInfo = '<payment gatewayid="' + cast(payProfileGatewayID as varchar(10)) + '" profileid="' + cast(payProfileID as varchar(10)) + '"><args><x_amount>' + cast(payAmount as varchar(20)) + '</x_amount><x_description>' + replace(isnull(payDescription,''),'&','&amp;') + '</x_description><fld_19_>' + replace(isnull(payReference,''),'&','&amp;') + '</fld_19_></args><gateway><x_amount>' + cast(payAmount as varchar(20)) + '</x_amount><x_description>' + replace(isnull(payDescription,''),'&','&amp;') + '</x_description><fld_19_>' + replace(isnull(payReference,''),'&','&amp;') + '</fld_19_></gateway></payment>',
		gatewayResponse = '<response><rawresponse/><responsecode>1</responsecode><responsereasontext/><responsereasoncode/><transactionid/><approvalcode/><transactiondetail/><status>Active</status><glaccountid>' + cast(payCashGLAID as varchar(10)) + '</glaccountid><historyid/></response>',
		payDate, @statsSessionID, null, payID
	from #importAcctData
	where payTID is not null

	update tmp
	set tmp.payHistoryID = tmpP.historyID
	from #importAcctData as tmp
	inner join #tmpPaymentsHistory as tmpP on tmpP.payID = tmp.payID

	IF OBJECT_ID('tempdb..#tmpPaymentsHistory') IS NOT NULL
		DROP TABLE #tmpPaymentsHistory

	IF EXISTS (select top 1 * from #importAcctData where payAmount > 0 and payHistoryID is null) BEGIN
		RAISERROR('Error raised in TRY block.', 16, 1);
	END

	INSERT INTO dbo.tr_transactionPayments (transactionID, profileID, historyID, cache_allocatedAmountOfPayment, cache_refundableAmountOfPayment)
	select distinct payTID, payProfileID, payHistoryID, payAmount, payAmount
	from #importAcctData
	where payTID is not null

	INSERT INTO dbo.tr_batchTransactions (batchID, transactionID)
	select distinct batchID, payTID
	from #importAcctData
	where payTID is not null
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	SELECT distinct top 100 PERCENT 'Unable to add payment transaction for row ' + cast(autoid as varchar(10)), 1
	from #importAcctData 
	where payAmount > 0 and payTID is null
	order by autoid

	INSERT INTO #tblAccImpErrors (msg, fatal)
	SELECT distinct top 100 PERCENT 'Unable to add payment history record for row ' + cast(autoid as varchar(10)), 1
	from #importAcctData 
	where payAmount > 0 and payHistoryID is null
	order by autoid

	GOTO on_doneT
END CATCH


/* ******** */
/* INVOICES */
/* ******** */
BEGIN TRY
	IF OBJECT_ID('tempdb..#tmpInvoices') IS NOT NULL
		DROP TABLE #tmpInvoices
	CREATE TABLE #tmpInvoices (invoiceID int, autoID int)

	select @invoicenum = isnull(max(i.invoiceNumber),0)
		from dbo.tr_invoices as i 
		inner join dbo.ams_members as m on m.memberID = i.assignedToMemberID
		where m.orgID = @orgID

	INSERT INTO dbo.tr_invoices (datecreated, statusID, invoiceNumber, dateBilled, dateDue, assignedToMemberID, invoiceCode, payProfileID, invoiceProfileID, tmpID)
		OUTPUT inserted.invoiceID, inserted.tmpID 
		INTO #tmpInvoices(invoiceID, autoID)
	select getdate(), 4, autoid + @invoicenum, DATEADD(dd,DATEDIFF(dd,0,saleDate),0), DATEADD(dd,DATEDIFF(dd,0,saleDate),0), saleMemberID, '', null, invoiceProfileID, autoID
	from #importAcctData

	update tmp
	set tmp.invoiceID = tmpI.invoiceID
	from #importAcctData as tmp
	inner join #tmpInvoices as tmpI on tmpI.autoID = tmp.autoID

	IF OBJECT_ID('tempdb..#tmpInvoices') IS NOT NULL
		DROP TABLE #tmpInvoices

	IF EXISTS (select top 1 * from #importAcctData where invoiceID is null) BEGIN
		RAISERROR('Error raised in TRY block.', 16, 1);
	END

	INSERT INTO dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
	select invoiceID, DATEADD(dd,DATEDIFF(dd,0,saleDate),0), 1, null, @recordedByMemberID
	from #importAcctData

	INSERT INTO dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
	select invoiceID, DATEADD(ss,5,DATEADD(dd,DATEDIFF(dd,0,saleDate),0)), 3, 1, @recordedByMemberID
	from #importAcctData

	INSERT INTO dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
	select invoiceID, DATEADD(ss,10,DATEADD(dd,DATEDIFF(dd,0,saleDate),0)), 4, 3, @recordedByMemberID
	from #importAcctData

	;WITH numbers AS (select NUMBER as n, Abs(Checksum(Newid()))%26 as c from dbo.F_TABLE_NUMBER_RANGE(1,800000))
	update i
	set i.invoiceCode = tmpnum.code
	from dbo.tr_invoices as i
	inner join #importAcctData as tmp on tmp.invoiceID = i.invoiceID
	inner join (
		SELECT n, CAST((SELECT TOP 8 CHAR(65 + case c 
					when 0 then 2 -- A = C
					when 1 then 22 -- B = W
					when 4 then 10 -- E = K
					when 8 then 7 -- I = H
					when 14 then 15 -- O = P
					when 18 then 19 -- S = T
					when 20 then 3 -- U = D
					when 25 then 17 -- Z = R
					else c end)
					 FROM   numbers n1
					 WHERE  n1.n >= -n2.n
					 FOR XML PATH('')) AS CHAR(8)) as code
		FROM numbers n2  
	) as tmpnum on tmpnum.n = i.invoiceID
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	SELECT distinct top 100 PERCENT 'Unable to add invoice for row ' + cast(autoid as varchar(10)), 1
	from #importAcctData 
	where invoiceID is null
	order by autoid

	GOTO on_doneT
END CATCH


/* ***** */
/* SALES */
/* ***** */
BEGIN TRY
	IF OBJECT_ID('tempdb..#tmpSales') IS NOT NULL
		DROP TABLE #tmpSales
	CREATE TABLE #tmpSales (transactionID int, autoid int)

	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, 
		parentTransactionID, amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, 
		statsSessionID, typeID, accrualDate, debitGLAccountID, creditGLAccountID, tmpID)
		OUTPUT inserted.transactionid, inserted.tmpID 
		INTO #tmpSales(transactionID, autoid)
	select @orgID, @siteID, 1, saleDescription, null, saleAmount, getdate(), saleDate, saleMemberID, 
		@recordedByMemberID, @statsSessionID, 1, saleDate, @saledebitGLAccountID, saleRevenueGLAID, autoid
	from #importAcctData

	update tmp
	set tmp.saleTID = tmpS.transactionID
	from #importAcctData as tmp
	inner join #tmpSales as tmpS on tmpS.autoid = tmp.autoid

	IF OBJECT_ID('tempdb..#tmpSales') IS NOT NULL
		DROP TABLE #tmpSales

	IF EXISTS (select top 1 * from #importAcctData where saleTID is null) BEGIN
		RAISERROR('Error raised in TRY block.', 16, 1);
	END

	INSERT INTO dbo.tr_transactionSales (transactionID, cache_amountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount, stateIDForTax)
	select saleTID, saleAmount, saleAmount, 0, null
	from #importAcctData

	INSERT INTO dbo.tr_invoiceTransactions (transactionID, invoiceID, cache_invoiceAmountAfterAdjustment, cache_activePaymentAllocatedAmount, cache_pendingPaymentAllocatedAmount)
	select saleTID, invoiceID, saleAmount, saleAmount, 0
	from #importAcctData
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	SELECT distinct top 100 PERCENT 'Unable to add sale transaction for row ' + cast(autoid as varchar(10)), 1
	from #importAcctData 
	where saleTID is null
	order by autoid

	GOTO on_doneT
END CATCH


/* *********** */
/* ALLOCATIONS */
/* *********** */
BEGIN TRY
	IF OBJECT_ID('tempdb..#tmpAllocations') IS NOT NULL
		DROP TABLE #tmpAllocations
	CREATE TABLE #tmpAllocations (transactionID int, autoid int)

	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, 
		parentTransactionID, amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, 
		statsSessionID, typeID, accrualDate, debitGLAccountID, creditGLAccountID, tmpID5)
		OUTPUT inserted.transactionid, inserted.tmpID5 
		INTO #tmpAllocations(transactionID, autoid)
	select distinct @orgID, @siteID, 1, null, null, saleAmount, getdate(), saleDate, payMemberID,
		@recordedByMemberID, @statsSessionID, 5, saleDate, @allocDebitGLAccountID, @allocCreditGLAccountID, autoid
	from #importAcctData
	where payTID is not null

	update tmp
	set tmp.allocTID = tmpA.transactionID
	from #importAcctData as tmp
	inner join #tmpAllocations as tmpA on tmpA.autoid = tmp.autoid

	IF OBJECT_ID('tempdb..#tmpAllocations') IS NOT NULL
		DROP TABLE #tmpAllocations

	IF EXISTS (select top 1 * from #importAcctData where payTID is not null and allocTID is null) BEGIN
		RAISERROR('Error raised in TRY block.', 16, 1);
	END

	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)	--allocpaytrans
	select 2, allocTID, payTID
	from #importAcctData
	where allocTID is not null

	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)	--allocpaytrans
	select 3, allocTID, saleTID
	from #importAcctData
	where allocTID is not null
	
	INSERT INTO dbo.tr_batchTransactions (batchID, transactionID)
	select distinct batchID, allocTID
	from #importAcctData
	where allocTID is not null
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	SELECT distinct top 100 PERCENT 'Unable to add allocation transaction for row ' + cast(autoid as varchar(10)), 1
	from #importAcctData 
	where payTID is not null and allocTID is null
	order by autoid

	GOTO on_doneT
END CATCH


/* ******************* */
/* UPDATE BATCH COUNTS */
/* ******************* */
BEGIN TRY
	update b
	set b.controlCount = act.actualCount,
		b.controlAmt = act.actualAmount
	from dbo.tr_batches as b
	inner join (select distinct batchID from #importAcctData) as tmp on tmp.batchID = b.batchID
	cross apply dbo.fn_tr_getBatchActual(tmp.batchID) as act
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	VALUES ('Unable to update actual batch counts.',1)

	GOTO on_doneT
END CATCH


-- ******************************
-- Remove temp fields to the tables
-- ******************************
BEGIN TRY
	SET @createSQL = '
		IF EXISTS(SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N''dbo.tr_transactions'') AND name = ''tmpID'')
			ALTER TABLE dbo.tr_transactions DROP COLUMN tmpID, tmpID2, tmpID5;
		IF EXISTS(SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N''dbo.tr_paymentHistory'') AND name = ''tmpID2'')
			ALTER TABLE dbo.tr_paymentHistory DROP COLUMN tmpID2;
		IF EXISTS(SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N''dbo.tr_invoices'') AND name = ''tmpID'')
			ALTER TABLE dbo.tr_invoices DROP COLUMN tmpID;
		'
	EXEC(@createSQL)
END TRY
BEGIN CATCH
	INSERT INTO #tblAccImpErrors (msg, fatal)
	VALUES ('Unable to remove temporary fields.',1)

	GOTO on_done
END CATCH

IF @TranCounter = 0
	COMMIT TRAN;
GOTO on_done


on_doneT:
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

on_done:
	select @importResult = (
		select getdate() as "@date", @flatfile as "@flatfile", 
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg", "@severity" = case fatal when 1 then 'fatal' else 'nonfatal' end
			from #tblAccImpErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE)

	IF OBJECT_ID('tempdb..#tblAccImpErrors') IS NOT NULL 
		DROP TABLE #tblAccImpErrors
	IF OBJECT_ID('tempdb..##importAcctData') IS NOT NULL
		DROP TABLE ##importAcctData
	IF OBJECT_ID('tempdb..#importAcctData') IS NOT NULL
		DROP TABLE #importAcctData
	IF OBJECT_ID('tempdb..#tmpPayments') IS NOT NULL
		DROP TABLE #tmpPayments
	IF OBJECT_ID('tempdb..#tmpPaymentsHistory') IS NOT NULL
		DROP TABLE #tmpPaymentsHistory
	IF OBJECT_ID('tempdb..#tmpInvoices') IS NOT NULL
		DROP TABLE #tmpInvoices
	IF OBJECT_ID('tempdb..#tmpSales') IS NOT NULL
		DROP TABLE #tmpSales
	IF OBJECT_ID('tempdb..#tmpAllocations') IS NOT NULL
		DROP TABLE #tmpAllocations
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sub_importSubscriptions_toTemp]') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].[sub_importSubscriptions_toTemp]
GO
CREATE PROC [dbo].[sub_importSubscriptions_toTemp]
@siteid int, 
@tmptbl varchar(30),
@pathToExport varchar(100),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

IF OBJECT_ID('tempdb..#tblSubErrors') IS NOT NULL 
	DROP TABLE #tblSubErrors
CREATE TABLE #tblSubErrors (rowid int IDENTITY(1,1), msg varchar(300), fatal bit)

declare @var_tmpCols varchar(20), @orgID int, @orgCode varchar(10), @bcpprefix varchar(50), @flatfile varchar(160),
	@dynSQL nvarchar(max), @good bit, @exportcmd varchar(400)

select @orgID=o.orgID, @orgcode=o.orgcode 
	from dbo.organizations as o
	inner join dbo.sites as s on s.orgID = o.orgID
	where s.siteID = @siteID
set @var_tmpCols = '##tmpSubCols' + @orgcode
set @bcpprefix = @orgcode + '_sub_flat_' + convert(varchar(8),getdate(),112) + replace(convert(varchar(8),getdate(),108),':','')
set @flatfile = @pathToExport + @bcpprefix


-- ******************************** 
-- ensure all columns exist (fatal)
-- ******************************** 
BEGIN TRY
	IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
		EXEC('DROP TABLE ' + @var_tmpCols)
	IF OBJECT_ID('tempdb..#tblSubOrgCols') IS NOT NULL 
		DROP TABLE #tblSubOrgCols
	IF OBJECT_ID('tempdb..#tblSubImportCols') IS NOT NULL 
		DROP TABLE #tblSubImportCols

	-- this will get the columns that should be there
	CREATE TABLE #tblSubOrgCols (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
		COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
		SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
		COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
		CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)

		select @dynSQL = '	
			select cast(null as varchar(50)) as MemberNumber, cast(null as varchar(100)) as SubscriptionType,
				cast(null as varchar(300)) as SubscriptionName, cast(null as varchar(200)) as RateName, 
				cast(null as money) as LastPrice, cast(null as varchar(10)) as Frequency, 
				cast(null as varchar(3)) as StoreModifiedRate, cast(null as datetime) as StartDate, 
				cast(null as datetime) as EndDate, cast(null as datetime) as GraceEndDate, 
				cast(null as varchar(1)) as Status, cast(null as varchar(100)) as ParentSubscriptionType, 
				cast(null as varchar(300)) as ParentSubscriptionName, cast(null as varchar(50)) as TreeCode,
				cast(null as int) as rowID
			into ' + @var_tmpCols + '
			from dbo.sub_subscriptions
			where 1=0 '
		EXEC(@dynSQL)

		-- get cols
		INSERT INTO #tblSubOrgCols
		EXEC tempdb.dbo.SP_COLUMNS @var_tmpCols
		
		-- cleanup table no longer needed
		IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
			EXEC('DROP TABLE ' + @var_tmpCols)

	-- this will get the columns that are actually in the import
	CREATE TABLE #tblSubImportCols (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
		COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
		SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
		COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
		CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)

		-- get cols
		INSERT INTO #tblSubImportCols
		EXEC tempdb.dbo.SP_COLUMNS @tmptbl

	INSERT INTO #tblSubErrors (msg, fatal)
	select 'The column ' + org.column_name + ' is missing from your data.', 1
	from #tblSubOrgCols as org
	left outer join #tblSubImportCols as imp on imp.column_name = org.column_name
	where imp.table_name is null

	IF @@ROWCOUNT > 0
		GOTO on_done
END TRY
BEGIN CATCH
	INSERT INTO #tblSubErrors (msg, fatal)
	VALUES ('Unable to process subscription file.',1)

	GOTO on_done
END CATCH


-- **********************
-- columns checks (fatal)
-- **********************
BEGIN TRY
	select @dynSQL = 'ALTER TABLE ' + @tmptbl + ' ADD memberID int null, subTypeUID uniqueidentifier null, subUID uniqueidentifier null, 
		subID int null, parentSubTypeUID uniqueidentifier null, parentSubUID uniqueidentifier null, parentSubID int null, 
		rateUID uniqueidentifier null, RFID int null, subscriberID int null'
	EXEC(@dynSQL)

	-- ensure startDate is datetime (allow nulls for this check)
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [StartDate] datetime null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO #tblSubErrors (msg, fatal)
		VALUES ('The column StartDate contains invalid dates.', 1)

	-- check for null startDate
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [StartDate] datetime not null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' is missing the required StartDate.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE StartDate IS NULL
			ORDER BY rowID'
		INSERT INTO #tblSubErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	-- ensure endDate is datetime (allow nulls for this check)
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [EndDate] datetime null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO #tblSubErrors (msg, fatal)
		VALUES ('The column EndDate contains invalid dates.', 1)

	-- check for null endDate
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [EndDate] datetime not null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' is missing the required EndDate.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE EndDate IS NULL
			ORDER BY rowID'
		INSERT INTO #tblSubErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	-- ensure graceEndDate is datetime (allow nulls for this check)
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [GraceEndDate] datetime null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO #tblSubErrors (msg, fatal)
		VALUES ('The column GraceEndDate contains invalid dates.', 1)

	-- clean amounts
	select @dynSQL = 'update ' + @tmptbl + ' set LastPrice = membercentral.dbo.fn_regexReplace(LastPrice,''[^0-9\.\-\(\)]'','''') where LastPrice is not null;'
	EXEC(@dynSQL)

	-- ensure LastPrice is money (allow nulls for this check)
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [LastPrice] money null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO #tblSubErrors (msg, fatal)
		VALUES ('The column LastPrice contains invalid amounts.', 1)

	-- check for null or negative LastPrice
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [LastPrice] money not null;
			ALTER TABLE ' + @tmptbl + ' ADD CONSTRAINT lastPriceamtCheck CHECK (LastPrice >= 0);
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(memberNumber,'''') + '') does not have a positive LastPrice amount.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE LastPrice IS NULL OR LastPrice < 0
			ORDER BY rowID'
		INSERT INTO #tblSubErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	-- match on membernumber
	select @dynSQL = 'update tmp
		set tmp.memberID = m.memberid
		from ' + @tmptbl + ' as tmp
		inner join membercentral.dbo.ams_members as m on m.memberNumber = tmp.memberNumber
			and m.orgID = ' + cast(@orgID as varchar(5)) + '
			and m.memberID = m.activeMemberID'
	EXEC(@dynSQL)

	-- check for missing member matches
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN memberID int not null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(membernumber,'''') + '') does not match an existing MemberNumber.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE memberID IS NULL
			ORDER BY rowID'
		INSERT INTO #tblSubErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	-- match on subscription type
	select @dynSQL = '
		update tmp
		set tmp.subtypeUID = t.uid
		from ' + @tmptbl + ' as tmp
		inner join membercentral.dbo.sub_types as t on t.typeName = tmp.SubscriptionType 
			and t.status = ''A'' 
			and t.siteID = ' + cast(@siteID as varchar(5))
	EXEC(@dynSQL)

	-- check for missing sub type matches
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN subtypeUID uniqueidentifier not null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT DISTINCT ''SubscriptionType ['' + isnull(SubscriptionType,'''') + ''] does not match an existing Subscription Type.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE subtypeUID IS NULL
			ORDER BY 1'
		INSERT INTO #tblSubErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	-- match on parent subscription type
	select @dynSQL = '
		update tmp
		set tmp.parentSubtypeUID = t.uid
		from ' + @tmptbl + ' as tmp
		inner join membercentral.dbo.sub_types as t on t.typeName = tmp.ParentSubscriptionType 
			and t.status = ''A'' 
			and t.siteID = ' + cast(@siteID as varchar(5)) + ' 
		where tmp.ParentSubscriptionType is not null'
	EXEC(@dynSQL)

	-- check for missing parent sub type matches
	select @dynSQL = 'SELECT DISTINCT ''ParentSubscriptionType ['' + isnull(ParentSubscriptionType,'''') + ''] does not match an existing Subscription Type.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE parentSubtypeUID is null and ParentSubscriptionType is not null
		ORDER BY 1'
	INSERT INTO #tblSubErrors (msg, fatal)
	EXEC(@dynSQL)

	-- match on subscription 
	select @dynSQL = '
		update tmp
		set tmp.subUID = subs.uid,
			tmp.subID = subs.subscriptionID
		from ' + @tmptbl + ' as tmp
		inner join membercentral.dbo.sub_subscriptions as subs on subs.subscriptionName = tmp.SubscriptionName 
			and subs.status = ''A''
		inner join membercentral.dbo.sub_types as t on subs.typeID = t.typeID 
			and t.uid = tmp.subtypeUID'
	EXEC(@dynSQL)

	-- check for missing sub matches
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN subUID uniqueidentifier not null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT DISTINCT ''SubscriptionName ['' + isnull(SubscriptionName,'''') + ''] does not match an existing Subscription.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE subUID IS NULL
			ORDER BY 1'
		INSERT INTO #tblSubErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	-- match on parent subscription 
	select @dynSQL = '
		update tmp
		set tmp.parentSubUID = subs.uid,
			tmp.parentSubID = subs.subscriptionID
		from ' + @tmptbl + ' as tmp
		inner join membercentral.dbo.sub_subscriptions as subs on subs.subscriptionName = tmp.ParentSubscriptionName 
			and subs.status = ''A''
		inner join membercentral.dbo.sub_types as t on subs.typeID = t.typeID 
			and t.uid = tmp.ParentSubtypeUID
		where tmp.ParentSubscriptionName is not null'
	EXEC(@dynSQL)

	-- check for missing parent sub matches
	select @dynSQL = 'SELECT DISTINCT ''ParentSubscriptionName ['' + isnull(ParentSubscriptionName,'''') + ''] does not match an existing Subscription.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE parentSubUID is null and ParentSubscriptionName is not null
		ORDER BY 1'
	INSERT INTO #tblSubErrors (msg, fatal)
	EXEC(@dynSQL)

	-- check for bad data - parent subs and subs the same
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(membernumber,'''') + '') has a parent subscription of itself.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE parentSubUID is not null and subUID is not null and parentSubUID = subUID
		ORDER BY rowID'
	INSERT INTO #tblSubErrors (msg, fatal)
	EXEC(@dynSQL)
	
	-- check for frequencies
	select @dynSQL = 'SELECT DISTINCT ''Frequency ['' + isnull(tmp.Frequency,'''') + ''] does not match an existing Frequency.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' as tmp
		left outer join membercentral.dbo.sub_frequencies as f on f.frequencyShortName = tmp.frequency 
			and f.siteID = ' + cast(@siteID as varchar(5)) + ' 
			and f.status = ''A''
		WHERE f.frequencyID is null
		ORDER BY 1'
	INSERT INTO #tblSubErrors (msg, fatal)
	EXEC(@dynSQL)

	-- check for status
	select @dynSQL = 'SELECT DISTINCT ''Status ['' + isnull(Status,'''') + ''] does not match an existing Status.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' as tmp
		left outer join membercentral.dbo.sub_statuses as s on s.statusCode = tmp.Status 
		where s.statusID is null
		ORDER BY 1'
	INSERT INTO #tblSubErrors (msg, fatal)
	EXEC(@dynSQL)

	-- match on rate 
	select @dynSQL = '
		update tmp
		set tmp.rateUID = r.uid,
			tmp.RFID = rf.rfid
		from ' + @tmptbl + ' as tmp
		inner join membercentral.dbo.sub_subscriptions as subs on subs.uid = tmp.subUID
		inner join membercentral.dbo.sub_rates as r on r.scheduleID = subs.scheduleID and r.rateName = tmp.rateName
		inner join membercentral.dbo.sub_rateFrequencies as rf on rf.rateID = r.rateID
		inner join membercentral.dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID and f.frequencyShortName = tmp.frequency'
	EXEC(@dynSQL)

	-- check for missing rate matches
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN rateUID uniqueidentifier not null;
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0 BEGIN
		select @dynSQL = 'SELECT DISTINCT ''RateName ['' + isnull(RateName,'''') + ''] does not match an existing Rate.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE rateUID IS NULL
			ORDER BY 1'
		INSERT INTO #tblSubErrors (msg, fatal)
		EXEC(@dynSQL)
	END

	-- check dates
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(membernumber,'''') + '') has a StartDate after the EndDate.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE startDate > EndDate
		ORDER BY rowID'
	INSERT INTO #tblSubErrors (msg, fatal)
	EXEC(@dynSQL)

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(membernumber,'''') + '') has a GraceEndDate before the EndDate.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE GraceEndDate is not null and EndDate > GraceEndDate
		ORDER BY rowID'
	INSERT INTO #tblSubErrors (msg, fatal)
	EXEC(@dynSQL)

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(membernumber,'''') + '') is an expired subscription incorrectly ending in the future.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE endDate > getdate() and [status] = ''E''
		ORDER BY rowID'
	INSERT INTO #tblSubErrors (msg, fatal)
	EXEC(@dynSQL)

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ('' + isnull(membernumber,'''') + '') is an active subscription incorrectly starting in the future.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE startDate > getdate() and [status] = ''A''
		ORDER BY rowID'
	INSERT INTO #tblSubErrors (msg, fatal)
	EXEC(@dynSQL)

	select @dynSQL = 'update ' + @tmptbl + ' set EndDate = cast(convert(varchar(10),EndDate,101) + '' 23:59:59.997'' AS datetime);'
	EXEC(@dynSQL)

	-- check child subs
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(tmp.rowID as varchar(10)) + '' ('' + isnull(tmp.membernumber,'''') + '') cannot locate matching parent subscription.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' as tmp 
		left outer join ' + @tmptbl + ' as tmp2 on tmp2.memberid = tmp.memberid 
			and tmp2.subID = tmp.parentSubID
			and tmp2.TreeCode = tmp.TreeCode
		where tmp.parentSubID is not null
		and tmp2.rowID is null
		ORDER BY tmp.rowID'
	INSERT INTO #tblSubErrors (msg, fatal)
	EXEC(@dynSQL)

	-- reorder import file based on subscription path
	select @dynSQL = '
		update a
		set a.rowID = b.newRowID
		from ' + @tmptbl + ' as a
		inner join (
			select orig.rowID, row_number() OVER (ORDER by orig.membernumber, tmp2.rootSubscriptionID, tmp2.subscriptionPath, orig.rowID) as newRowID
			from ' + @tmptbl + ' as orig
			inner join (
				select tmp.subID as rootSubscriptionID, sto.subscriptionID, sto.subscriptionPath
				from (
					select distinct subID
					from ' + @tmptbl + '
					where parentSubID is null
				) as tmp
				cross apply membercentral.dbo.fn_sub_getSubscriptionTreeOrder(tmp.subID) as sto
			) as tmp2 on tmp2.subscriptionID = orig.subID
		) as b on b.rowID = a.rowID'
	EXEC(@dynSQL)

	select @dynSQL = '
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ADD PRIMARY KEY (rowID);
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSubErrors (msg, fatal)
			VALUES (''Reordering based on subscription path has failed.'', 1)

			/*
			INSERT INTO #tblSubErrors (msg, fatal)
			select ''Duplicate RowIDs after reordering: RowID '' + cast(tmp.rowID as varchar(10)) + '' ('' + (select top 1 membernumber + '' - '' + subscriptionName from ' + @tmptbl + ' where rowID = tmp.rowID) + '')'', 1
			from ' + @tmptbl + ' as tmp
			group by tmp.rowID 
			having count(*) > 1
			order by 1
			*/

			INSERT INTO #tblSubErrors (msg, fatal)
			VALUES (error_message(),0)
		END CATCH'
	EXEC(@dynSQL)
END TRY
BEGIN CATCH
	INSERT INTO #tblSubErrors (msg, fatal)
	VALUES ('Unable to validate subscription file.',1)

	INSERT INTO #tblSubErrors (msg, fatal)
	VALUES (error_message(),0)

	GOTO on_done
END CATCH


IF (select count(*) from #tblSubErrors) = 0 BEGIN
	/* ***************** */
	/* get expanded data */
	/* ***************** */
	BEGIN TRY
		IF OBJECT_ID('tempdb..##tmpSubImportFull') IS NOT NULL 
			DROP TABLE ##tmpSubImportFull
		CREATE TABLE ##tmpSubImportFull (rowID int, MemberNumber varchar(50), SubscriptionType varchar(100),
			SubscriptionName varchar(300), RateName varchar(200), LastPrice money, Frequency varchar(10),
			StoreModifiedRate varchar(3), StartDate datetime, EndDate datetime, GraceEndDate datetime, 
			[Status] varchar(1), ParentSubscriptionType varchar(100), ParentSubscriptionName varchar(300), 
			TreeCode varchar(50), memberID int, subTypeUID uniqueidentifier, subUID uniqueidentifier, 
			subID int, parentSubTypeUID uniqueidentifier, parentSubUID uniqueidentifier, parentSubID int, 
			rateUID uniqueidentifier, RFID int, subscriberID int)

		select @dynSQL = ' 
			select rowID, MemberNumber, SubscriptionType, SubscriptionName, RateName, LastPrice, Frequency,
				StoreModifiedRate, StartDate, EndDate, GraceEndDate, [Status], ParentSubscriptionType, 
				ParentSubscriptionName, TreeCode, memberID, subTypeUID, subUID, subID, parentSubTypeUID, 
				parentSubUID, parentSubID, rateUID, RFID, subscriberID 
			from ' + @tmptbl + '
			order by rowid'
		insert into ##tmpSubImportFull
		EXEC(@dynSQL)

	END TRY
	BEGIN CATCH
		INSERT INTO #tblSubErrors (msg, fatal)
		VALUES ('Unable to prepare final subscription data for final import.',1)

		INSERT INTO #tblSubErrors (msg, fatal)
		VALUES (error_message(),0)

		set @flatfile = ''

		GOTO on_done
	END CATCH


	-- ******************************** 
	-- dump flat table and format file and creation script
	-- would love to use xml format files, but it appears column names with spaces cause it to fail
	-- ******************************** 
	BEGIN TRY
		select @exportcmd = 'bcp ##tmpSubImportFull format nul -f ' + @flatfile + '.txt -n -T -S' + CAST(serverproperty('servername') as varchar(40))
		EXEC master..xp_cmdshell @exportcmd, NO_OUTPUT
		select @exportcmd = 'bcp ##tmpSubImportFull out ' + @flatfile + '.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
		EXEC master..xp_cmdshell @exportcmd, NO_OUTPUT

		declare @coltypes table (datatype varchar(16))          
		insert into @coltypes values('bit')          
		insert into @coltypes values('binary')          
		insert into @coltypes values('bigint')          
		insert into @coltypes values('int')          
		insert into @coltypes values('float')          
		insert into @coltypes values('datetime')          
		insert into @coltypes values('text')          
		insert into @coltypes values('image')          
		insert into @coltypes values('money')          
		insert into @coltypes values('uniqueidentifier')          
		insert into @coltypes values('smalldatetime')          
		insert into @coltypes values('tinyint')          
		insert into @coltypes values('smallint')          
		insert into @coltypes values('sql_variant')          
		select @dynSQL = ''
		select @dynSQL = @dynSQL +           
			case when charindex('(',@dynSQL,1)<=0 then '(' else '' end + '[' + Column_Name + '] ' +Data_Type +
			case when Data_Type in (Select datatype from @coltypes) then '' else  '(' end+
			case when data_type in ('real','decimal','numeric')  then cast(isnull(numeric_precision,'') as varchar)+','+
			case when data_type in ('real','decimal','numeric') then cast(isnull(Numeric_Scale,'') as varchar) end
			when data_type in ('nvarchar','varchar') and cast(isnull(Character_Maximum_Length,'') as varchar) = '-1' then 'max'
			when data_type in ('char','nvarchar','varchar','nchar') then cast(isnull(Character_Maximum_Length,'') as varchar) else '' end+
			case when Data_Type in (Select datatype from @coltypes)then '' else  ')' end+
			case when Is_Nullable='No' then ' Not null,' else ' null,' end
			from tempdb.Information_Schema.COLUMNS where Table_Name='##tmpSubImportFull'
			order by ordinal_position
		select @dynSQL='Create table ##xxx ' + substring(@dynSQL,1,len(@dynSQL)-1) +' )'            
		if dbo.fn_WriteFile(@flatfile + '.sql', @dynSQL, 1) = 0 BEGIN
			RAISERROR('Error raised in TRY block.', 16, 1);
		END
	END TRY
	BEGIN CATCH
		INSERT INTO #tblSubErrors (msg, fatal)
		VALUES ('Unable to save subscription data for final import.',1)

		set @flatfile = ''

		GOTO on_done
	END CATCH
END ELSE
	set @flatfile = ''


-- ************************
-- generate result xml file 
-- ************************
on_done:
	select @importResult = (
		select getdate() as "@date", @flatfile as "@flatfile", 
			isnull((select top 301 dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg", "@severity" = case fatal when 1 then 'fatal' else 'nonfatal' end
			from #tblSubErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE)
	
	IF OBJECT_ID('tempdb..#tblSubOrgCols') IS NOT NULL 
		DROP TABLE #tblSubOrgCols
	IF OBJECT_ID('tempdb..#tblSubImportCols') IS NOT NULL 
		DROP TABLE #tblSubImportCols
	IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
		EXEC('DROP TABLE ' + @var_tmpCols)
	IF OBJECT_ID('tempdb..#tblSubErrors') IS NOT NULL 
		DROP TABLE #tblSubErrors
	IF OBJECT_ID('tempdb..##tmpSubImportFull') IS NOT NULL 
		DROP TABLE ##tmpSubImportFull

RETURN 0
GO

CREATE PROC dbo.sub_importSubscriptions_toPerm
@siteID int,
@recordedByMemberID int,
@flatfile varchar(160),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

DECLARE @orgID int, @paymentStatusID int, @createSQL varchar(max), @cmd varchar(400), @minRowID int, @currMemberID int, 
	@currSubscriptionID int, @currRFID int, @currGLAccountID int, @currStartDate datetime, @currEndDate datetime, 
	@currGraceEndDate datetime, @currActivationOptionCode varchar(1), @currSubscriberID int, @subCost money, @currStatus char(1),
	@parentSubscriberID int, @StoreModifiedRate varchar(6), @rc int, @renewLinkPath varchar(200), @renewLinkUrl varchar(200),
	@renewLinkText varchar(max), @renewLinkTrash bit, @renewLinkUrltext nvarchar(max)
select @orgID = orgID from dbo.sites where siteID = @siteID
select @paymentStatusID = statusID from membercentral.dbo.sub_paymentStatuses where statusCode = 'P'

IF @@SERVERNAME = 'DEV04\PLATFORM2008' BEGIN
	set @renewLinkPath = '\\dev01\wwwRoot\membercentral\temp\'
	set @renewLinkUrl = 'http://mc.dev.membercentral.com/temp/'
END
IF @@SERVERNAME = 'DEV03\PLATFORM2008' BEGIN
	set @renewLinkPath = '\\dev03\wwwRoot\membercentral\temp\'
	set @renewLinkUrl = 'http://mc.beta.membercentral.com/temp/'
END
IF @@SERVERNAME = 'TSSQL1\MEMBERCENTRAL' BEGIN
	set @renewLinkPath = '\\tsfile1\platform\membercentral\temp\'
	set @renewLinkUrl = 'http://mc.prod.membercentral.com/temp/'
END

IF OBJECT_ID('tempdb..#tblSubImpErrors') IS NOT NULL 
	DROP TABLE #tblSubImpErrors
CREATE TABLE #tblSubImpErrors (rowid int IDENTITY(1,1), msg varchar(max), fatal bit)

-- see if files exist
if dbo.fn_fileExists(@flatfile + '.sql') = 0
	or dbo.fn_fileExists(@flatfile + '.txt') = 0
	or dbo.fn_fileExists(@flatfile + '.bcp') = 0
BEGIN
	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES ('Unable to read subscription data files.',1)

	GOTO on_done
END

-- **************
-- read in sql create script and create global temp table
-- **************
BEGIN TRY
	SET @createSQL = replace(dbo.fn_ReadFile(@flatfile + '.sql',0,1),'##xxx','##importSubsData')
	IF OBJECT_ID('tempdb..##importSubsData') IS NOT NULL
		DROP TABLE ##importSubsData
	EXEC(@createSQL)
END TRY
BEGIN CATCH
	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES ('Unable to create holding table for subscription data.',1)

	GOTO on_done
END CATCH


-- *******************
-- bcp in data
-- *******************
BEGIN TRY
	set @cmd = 'bcp ##importSubsData in ' + @flatfile + '.bcp -f ' + @flatfile + '.txt -n -T -S' + CAST(serverproperty('servername') as varchar(40))
	exec master..xp_cmdshell @cmd, NO_OUTPUT
END TRY
BEGIN CATCH
	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES ('Unable to import data into holding table.',1)

	GOTO on_done
END CATCH


-- *******************
-- immediately put into local temp table and drop global temp
-- *******************
BEGIN TRY
	IF OBJECT_ID('tempdb..#importSubsData') IS NOT NULL
		DROP TABLE #importSubsData
	select * into #importSubsData from ##importSubsData
	IF OBJECT_ID('tempdb..##importSubsData') IS NOT NULL
		DROP TABLE ##importSubsData
END TRY
BEGIN CATCH
	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES ('Unable to import data into local temporary table.',1)

	GOTO on_done
END CATCH



/* ******************* */
/* loop to create subs */
/* ******************* */
BEGIN TRY
	select @minRowID = min(rowID) from #importSubsData
	while @minRowID is not null BEGIN
		select @currMemberID=null, @currSubscriptionID=null, @currRFID=null, @currGLAccountID=null,
			@currStartDate=null, @currEndDate=null, @currGraceEndDate=null, @currActivationOptionCode=null, 
			@currSubscriberID=null, @subCost=null, @currStatus=null, @parentSubscriberID=null, @StoreModifiedRate=null

		select @currMemberID=tmp.memberID, @currSubscriptionID=tmp.subID, @currRFID=tmp.RFID,
			@currGLAccountID=case when subs.allowRateGLAccountOverride = 1 and r.GLAccountID is not null then r.GLAccountID else subs.GLAccountID end,
			@currStartDate=tmp.startDate, @currEndDate=tmp.endDate, @currGraceEndDate=tmp.graceEndDate, @currStatus=tmp.status,
			@currActivationOptionCode=ao.subActivationCode, @subCost=tmp.lastPrice,
			@parentSubscriberID=
				case 
				when tmp.parentSubID is null then null 
				else (
					select top 1 subscriberID 
					from #importSubsData 
					where subID = tmp.parentSubID 
					and memberID = tmp.memberID 
					and treeCode = tmp.treeCode
					)
				end,
			@StoreModifiedRate=tmp.StoreModifiedRate
		from #importSubsData as tmp
		inner join dbo.sub_subscriptions as subs on subs.subscriptionID = tmp.subID
		inner join dbo.sub_activationOptions as ao on ao.subActivationID = subs.subAlternateActivationID
		inner join dbo.sub_rates as r on r.uid = tmp.rateUID
		where tmp.rowID = @minRowID

		EXEC dbo.sub_addSubscriber @orgID=@orgID, @memberid=@currMemberID,
			@subscriptionID=@currSubscriptionID, @parentSubscriberID=@parentSubscriberID, @RFID=@currRFID,
			@GLAccountID=@currGLAccountID, @status=@currStatus, @subStartDate=@currStartDate,
			@subEndDate=@currEndDate, @graceEndDate=@currGraceEndDate, @pcfree=0,
			@activationOptionCode=@currActivationOptionCode, @recordedByMemberID=@recordedByMemberID,
			@bypassQueue=1, @subscriberID=@currSubscriberID OUTPUT

		if @currSubscriberID > 0 BEGIN
			UPDATE #importSubsData
			set subscriberID = @currSubscriberID
			where rowID = @minRowID

			update dbo.sub_subscribers
			set lastPrice=@subCost, 
				paymentStatusID = @paymentStatusID
			where subscriberID = @currSubscriberID

			IF @StoreModifiedRate IN ('YES','Y')
				update dbo.sub_subscribers
				set modifiedRate=@subCost
				where subscriberID = @currSubscriberID

			EXEC dbo.sub_createStatusBackfill @siteID=@siteID, @subscriberID=@currSubscriberID, @isRenewal=0, @enteredByMemberID=@recordedByMemberID, @bypassQueue=1
		END

		select @minRowID = min(rowID) from #importSubsData where rowID > @minRowID
	END

	INSERT INTO #tblSubImpErrors (msg, fatal)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' (' + isnull(membernumber,'') + ') subscription could not be created.' as msg, 1 as fatal 
	from #importSubsData 
	where subscriberID is null
	ORDER BY rowID
END TRY
BEGIN CATCH
	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES ('Unable to add all subscriptions',1)

	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES (error_message(),0)

	INSERT INTO #tblSubImpErrors (msg, fatal)
	SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' (' + isnull(membernumber,'') + ') subscription could not be created.' as msg, 1 as fatal 
	from #importSubsData 
	where subscriberID is null
	ORDER BY rowID

	GOTO on_done
END CATCH


/* ************************ */
/* sub_paymentStatusHistory */
/* ************************ */
BEGIN TRY
	INSERT INTO dbo.sub_paymentStatusHistory (subscriberID, updateDate, paymentStatusID, enteredByMemberID)
	select subscriberID, startDate, 1, @recordedByMemberID
	from #importSubsData
	where subscriberID is not null
END TRY
BEGIN CATCH
	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES ('Unable to insert payment status history.',1)

	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES (error_message(),0)

	GOTO on_done
END CATCH


/* ********** */
/* fix groups */
/* ********** */
BEGIN TRY
	EXEC dbo.sub_fixGroups @siteID=@siteID, @bypassQueue=1
END TRY
BEGIN CATCH
	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES ('Unable to fix groups.',1)

	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES (error_message(),0)

	GOTO on_done
END CATCH


/* ***************** */
/* check activations */
/* ***************** */
BEGIN TRY
	EXEC dbo.sub_checkActivations @memberid=null, @bypassQueue=1
END TRY
BEGIN CATCH
	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES ('Unable to check activations.',1)

	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES (error_message(),0)

	GOTO on_done
END CATCH


/* ************************************************ */
/* generate renew links for R O and P subscriptions */
/* ************************************************ */
BEGIN TRY
	if exists (select top 1 rowID from #importSubsData where [status] in ('R','O','P')) BEGIN
		set	@renewLinkPath = @renewLinkPath + 'renewlinks.cfm'
		set @renewLinkUrl = @renewLinkUrl + 'renewlinks.cfm'
		set @renewLinkText = '
			<cfset CreateObject(''component'',''model.admin.subscriptions.subscriptions'').addMissingRenewLinks()>
			<cfoutput>done adding renewLinks.</cfoutput>
		'
		select @renewLinkTrash = dbo.fn_WriteFile(@renewLinkPath,@renewLinkText,1)
		select @renewLinkUrltext = search.dbo.fn_ParseURL(@renewLinkUrl,3000,1)
	END
END TRY
BEGIN CATCH
	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES ('Unable to generate necessary renew links.',1)

	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES (error_message(),0)

	GOTO on_done
END CATCH


-- queue member groups (@runSchedule=1 indicates immediate processing) 
BEGIN TRY
	declare @itemGroupUID uniqueidentifier, @memberIDList varchar(max)
	SELECT @memberIDList = COALESCE(@memberIDList + ',', '') + cast(memberID as varchar(10)) 
		from #importSubsData 
		group by memberID
	EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberIDList, @conditionIDList='', @runSchedule=1, @itemGroupUID=@itemGroupUID OUTPUT
END TRY
BEGIN CATCH
	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES ('Unable to recalculate group assignments.',1)

	INSERT INTO #tblSubImpErrors (msg, fatal)
	VALUES (error_message(),0)

	GOTO on_done
END CATCH


on_done:
	select @importResult = (
		select getdate() as "@date", @flatfile as "@flatfile", 
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg", "@severity" = case fatal when 1 then 'fatal' else 'nonfatal' end
			from #tblSubImpErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE)

	IF OBJECT_ID('tempdb..#tblSubImpErrors') IS NOT NULL 
		DROP TABLE #tblSubImpErrors
	IF OBJECT_ID('tempdb..##importSubsData') IS NOT NULL
		DROP TABLE ##importSubsData
	IF OBJECT_ID('tempdb..#importSubsData') IS NOT NULL
		DROP TABLE #importSubsData
GO

USE [customApps]
GO

ALTER PROC [dbo].[TAGD_importNationalData]
@batchDate datetime,
@recordedByMemberID int,
@statsSessionID int,
@pathToImport varchar(200), 
@pathToExport varchar(200),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

DECLARE @orgID int, @siteID int, @cmd varchar(600), @IDList VARCHAR(max), @finalMSG varchar(max), @emailtouse varchar(300),
	@flatfile varchar(200)
SELECT @orgID = membercentral.dbo.fn_getOrgIDFromOrgCode('TAGD')
SELECT @siteID = membercentral.dbo.fn_getSiteIDFromSiteCode('TAGD')

-- create temp tables
IF OBJECT_ID('tempdb..##tagd_nationals_alpha') IS NOT NULL
	DROP TABLE ##tagd_nationals_alpha
CREATE TABLE ##tagd_nationals_alpha (
	[rowid] [int] NOT NULL,
	[ID] [varchar](50) NOT NULL,
	[MEMBER TYPE] [varchar](10) NOT NULL DEFAULT (''),
	[MEMBER TYPE DESCRIPTION] [varchar](100) NOT NULL DEFAULT (''),
	[CATEGORY DESCRIPTION] [varchar](200) NOT NULL DEFAULT (''),
	[FULL NAME] [varchar](200) NOT NULL DEFAULT (''),
	[LAST FIRST] [varchar](200) NOT NULL DEFAULT (''),
	[FIRST NAME] [varchar](75) NOT NULL DEFAULT (''),
	[MIDDLE NAME] [varchar](25) NOT NULL DEFAULT (''),
	[LAST NAME] [varchar](75) NOT NULL DEFAULT (''),
	[SUFFIX] [varchar](50) NOT NULL DEFAULT (''),
	[DESIGNATION] [varchar](max) NULL,
	[WORK PHONE] [varchar](40) NULL,
	[HOME PHONE] [varchar](40) NULL,
	[FAX] [varchar](40) NULL,
	[FULL ADDRESS] [varchar](200) NOT NULL DEFAULT (''),
	[ADDRESS 1] [varchar](100) NULL,
	[ADDRESS 2] [varchar](100) NULL,
	[CITY] [varchar](35) NULL,
	[STATE PROVINCE] [varchar](25) NULL,
	[ZIP] [varchar](25) NULL,
	[GRAD DATE] datetime NULL,
	[DENTAL SCHOOL] [varchar](300) NULL,
	[JOIN DATE] datetime NULL,
	[PAID THRU] datetime NULL,
	[DO NOT MAIL] [varchar](300) NOT NULL DEFAULT (''),
	[EMAIL] [varchar](255) NULL,
	[COMPONENT TITLE] [varchar](300) NOT NULL DEFAULT (''),
	[BIRTH DATE] datetime NULL,
	[BAD ADDRESS] [varchar](300) NOT NULL DEFAULT ('')
)

IF OBJECT_ID('tempdb..##tagd_nationals_new') IS NOT NULL
	DROP TABLE ##tagd_nationals_new
CREATE TABLE ##tagd_nationals_new (
	[rowid] [int] NOT NULL,
	[ID] [varchar](50) NOT NULL,
	[FIRST NAME] [varchar](75) NOT NULL DEFAULT (''), 
	[MIDDLE NAME] [varchar](25) NOT NULL DEFAULT (''), 
	[LAST NAME] [varchar](75) NOT NULL DEFAULT (''), 
	[WORK ADDRESS 1] [varchar](100) NULL, 
	[WORK ADDRESS 2] [varchar](100) NULL, 
	[WORK CITY] [varchar](35) NULL, 
	[WORK STATE] [varchar](25) NULL, 
	[WORK ZIP] [varchar](25) NULL, 
	[WORK PHONE] [varchar](40) NULL, 
	[WORK FAX] [varchar](40) NULL, 
	[WORK EMAIL] [varchar](200) NULL, 
	[HOME ADDRESS 1] [varchar](100) NULL, 
	[HOME ADDRESS 2] [varchar](100) NULL, 
	[HOME CITY] [varchar](35) NULL, 
	[HOME STATE] [varchar](25) NULL, 
	[HOME ZIP] [varchar](25) NULL, 
	[HOME PHONE] [varchar](40) NULL, 
	[HOME FAX] [varchar](40) NULL, 
	[HOME EMAIL] [varchar](255) NULL, 
	[BIRTH DATE] datetime NULL,
	[WEBSITE] [varchar](200) NULL, 
	[PREFERRED ADDRESS] [varchar](200) NOT NULL DEFAULT (''), 
	[MEMBER TYPE] [varchar](200) NOT NULL DEFAULT (''), 
	[MEMBER TYPE CATEGORY] [varchar](200) NOT NULL DEFAULT (''), 
	[GENDER] [varchar](200) NULL , 
	[ETHNICITY] [varchar](200) NULL, 
	[MEMBER OF OTHER DENTAL ORGANIZATION] [varchar](200) NOT NULL DEFAULT (''), 
	[PREVIOUS MEMBER] [varchar](200) NOT NULL DEFAULT (''), 
	[JOIN DATE] datetime NULL,
	[PAID THRU] datetime NULL,
	[COMPONENT] [varchar](200) NOT NULL DEFAULT (''), 
	[DENTAL DEGREE] [varchar](200) NOT NULL DEFAULT (''), 
	[DENTAL SCHOOL] [varchar](200) NOT NULL DEFAULT (''), 
	[GRAD_DATE] datetime NULL,
	[GRADE] [varchar](200) NOT NULL DEFAULT (''), 
	[US RESIDENT] [varchar](200) NOT NULL DEFAULT (''), 
	[POSTDOCTORAL INSTITUTION] [varchar](200) NOT NULL DEFAULT (''), 
	[USA LICENSE] [varchar](200) NOT NULL DEFAULT (''), 
	[LICENSENO] [varchar](200) NULL, 
	[FEDERAL SERVICES] [varchar](200) NOT NULL DEFAULT (''), 
	[BRANCH] [varchar](200) NOT NULL DEFAULT (''), 
	[PRACTICE ENVIRONMENT] [varchar](200) NOT NULL DEFAULT (''), 
	[SPECIALTY] [varchar](200) NOT NULL DEFAULT ('')
)


IF OBJECT_ID('tempdb..##tagd_nationals_dues') IS NOT NULL
	DROP TABLE ##tagd_nationals_dues
CREATE TABLE ##tagd_nationals_dues (
	[rowid] [int] NOT NULL,
	[ID] [varchar](50) NOT NULL,
	[CHAPTER] [varchar](200) NOT NULL DEFAULT (''), 
	[FIRST NAME] [varchar](75) NOT NULL DEFAULT (''), 
	[MIDDLE NAME] [varchar](25) NOT NULL DEFAULT (''), 
	[LAST NAME] [varchar](75) NOT NULL DEFAULT (''), 
	[SUFFIX] [varchar](50) NOT NULL DEFAULT (''), 
	[DESIGNATION] [varchar](max) NULL, 
	[ADDRESS 1] [varchar](100) NOT NULL DEFAULT (''), 
	[ADDRESS 2] [varchar](100) NOT NULL DEFAULT (''), 
	[CITY] [varchar](35) NOT NULL DEFAULT (''), 
	[STATE] [varchar](25) NOT NULL DEFAULT (''), 
	[ZIP] [varchar](25) NOT NULL DEFAULT (''), 
	[WORK PHONE] [varchar](40) NOT NULL DEFAULT (''), 
	[FAX] [varchar](40) NOT NULL DEFAULT (''), 
	[EMAIL] [varchar](255) NULL, 
	[MEMBER TYPE] [varchar](200) NOT NULL DEFAULT (''), 
	[MEMBER TYPE DESCRIPTION] [varchar](200) NOT NULL DEFAULT (''), 
	[CATEGORY] [varchar](200) NOT NULL DEFAULT (''), 
	[CATEGORY DESCRIPTION] [varchar](200) NOT NULL DEFAULT (''), 
	[MEMBER TYPE BREAKDOWN] [varchar](200) NOT NULL DEFAULT (''), 
	[GRAD DATE] datetime NULL,
	[JOIN DATE] datetime NULL,
	[PAID THRU] datetime NULL,
	[CONSTITUENT] [varchar](200) NOT NULL DEFAULT (''), 
	[CONSTITUENT DUES] [decimal](9,2) NOT NULL, 
	[CONSTITUENT PAID] [decimal](9,2) NOT NULL, 
	[CONSTITUENT ABBREVIATION] [varchar](200) NOT NULL DEFAULT ('')
)


-- import csv files into temp tables.
BEGIN TRY
	SELECT @cmd = 'BULK INSERT ##tagd_nationals_alpha FROM ''' + @pathToImport + '1.csv'' WITH (DATAFILETYPE = ''widechar'', FIELDTERMINATOR = '''+ char(7) + ''', FIRSTROW = 2);'
	exec(@cmd)
	SELECT @cmd = 'BULK INSERT ##tagd_nationals_new FROM ''' + @pathToImport + '2.csv'' WITH (DATAFILETYPE = ''widechar'', FIELDTERMINATOR = '''+ char(7) + ''', FIRSTROW = 2);'
	exec(@cmd)
	SELECT @cmd = 'BULK INSERT ##tagd_nationals_dues FROM ''' + @pathToImport + '3.csv'' WITH (DATAFILETYPE = ''widechar'', FIELDTERMINATOR = '''+ char(7) + ''', FIRSTROW = 2);'
	exec(@cmd)
END TRY
BEGIN CATCH
	SET @importResult = '<import date="" flatfile=""><errors><error msg="Unable to import data. Ensure the columns are in the correct order and match previous imports." severity="fatal" /></errors></import>'
	GOTO on_cleanup
END CATCH


/* *********** */
/* DATA CHECKS */
/* *********** */
-- ensure IDs appear only once and set as primary key
BEGIN TRY
	ALTER TABLE ##tagd_nationals_alpha ADD CONSTRAINT PK__tagd_nationals_alpha PRIMARY KEY CLUSTERED (ID) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
END TRY
BEGIN CATCH
	SET @IDList = null
	SELECT @IDList = COALESCE(@IDList + ', ', '') + ID 
		from ##tagd_nationals_alpha
		group by ID
		having count(*) > 1
	SET @importResult = '<import date="" flatfile=""><errors><error msg="The file uploaded for Constituent Alpha Roster contains multiple rows for the following IDs: ' + @IDList + '. Ensure each ID appears only once in the file." severity="fatal" /></errors></import>'
	GOTO on_cleanup
END CATCH

-- ensure IDs appear only once and set as primary key
BEGIN TRY
	ALTER TABLE ##tagd_nationals_new ADD CONSTRAINT PK__tagd_nationals_new PRIMARY KEY CLUSTERED (ID) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
END TRY
BEGIN CATCH
	SET @IDList = null
	SELECT @IDList = COALESCE(@IDList + ', ', '') + ID 
		from ##tagd_nationals_new
		group by ID
		having count(*) > 1
	SET @importResult = '<import date="" flatfile=""><errors><error msg="The file uploaded for Constituent New Members contains multiple rows for the following IDs: ' + @IDList + '. Ensure each ID appears only once in the file." severity="fatal" /></errors></import>'
	GOTO on_cleanup
END CATCH

-- no missing PAID THRU dates for accounting
IF EXISTS (select top 1 ID from ##tagd_nationals_dues where [PAID THRU] is null) BEGIN
	SET @IDList = null
	SELECT @IDList = COALESCE(@IDList + ', ', '') + ID 
		from ##tagd_nationals_dues
		where [PAID THRU] is null
		group by ID
	SET @importResult = '<import date="" flatfile=""><errors><error msg="The file uploaded for Constituent Dues contains missing Paid Thru Dates for the following IDs: ' + @IDList + '. Ensure each row has a Paid Thru Date in the file." severity="fatal" /></errors></import>'
	GOTO on_cleanup
END

-- no negative amounts for accounting
IF EXISTS (select top 1 ID from ##tagd_nationals_dues where [Constituent PAID] < 0) BEGIN
	SET @IDList = null
	SELECT @IDList = COALESCE(@IDList + ', ', '') + ID 
		from ##tagd_nationals_dues
		where [Constituent PAID] < 0
		group by ID
	SET @importResult = '<import date="" flatfile=""><errors><error msg="The file uploaded for Constituent Dues contains negative CONSTITUENT PAID amounts for the following IDs: ' + @IDList + '. Negative amounts are not permitted." severity="fatal" /></errors></import>'
	GOTO on_cleanup
END



/* ****************** */
/* MEMBER DATA IMPORT */
/* ****************** */
-- flatten alpha table
IF OBJECT_ID('tempdb..##tagd_nationals_Members') IS NOT NULL
	DROP TABLE ##tagd_nationals_Members
select 
	m.prefix, 
	alpha.[FIRST NAME] as firstname, alpha.[MIDDLE NAME] as middlename, alpha.[LAST NAME] as lastname, alpha.SUFFIX as suffix, 
	cast(alpha.[DESIGNATION] as varchar(100)) as professionalSuffix, alpha.[ID] as memberNumber,
	m.company,
	
	vw.[Mailing Address_attn], 
	alpha.[ADDRESS 1] as [Mailing Address_address1], alpha.[ADDRESS 2] as [Mailing Address_address2], alpha.[CITY] as [Mailing Address_city],
	alpha.[STATE PROVINCE] as [Mailing Address_stateprov], alpha.[ZIP] as [Mailing Address_postalCode], cast('' as varchar(50)) as [Mailing Address_county],
	cast('United States' as varchar(100)) as [Mailing Address_country], alpha.[WORK PHONE] as [Mailing Address_Phone], alpha.[FAX] as [Mailing Address_Fax],
	vw.[Mailing Address_Cell], vw.[Mailing Address_2nd Phone],
 
	vw.[Office Address_address1], vw.[Office Address_address2], vw.[Office Address_city], vw.[Office Address_stateprov],
	vw.[Office Address_postalCode], vw.[Office Address_county], vw.[Office Address_country], vw.[Office Address_Phone], 
	vw.[Office Address_Fax], vw.[Office Address_Cell], vw.[Office Address_2nd Phone],

	vw.[Other Address_address1], vw.[Other Address_address2], vw.[Other Address_city], vw.[Other Address_stateprov], vw.[Other Address_postalCode],
	vw.[Other Address_county], vw.[Other Address_country], vw.[Other Address_Phone], vw.[Other Address_Fax], vw.[Other Address_Cell],
	vw.[Other Address_2nd Phone],

	coalesce(new.[HOME ADDRESS 1],vw.[Home Address_address1]) as [Home Address_address1],
	coalesce(new.[HOME ADDRESS 2],vw.[Home Address_address2]) as [Home Address_address2],	
	coalesce(new.[HOME CITY],vw.[Home Address_city]) as [Home Address_city],	
	coalesce(new.[HOME STATE],vw.[Home Address_stateprov]) as [Home Address_stateprov], 
	coalesce(new.[HOME ZIP],vw.[Home Address_postalCode]) as [Home Address_postalCode],
	vw.[Home Address_county], 
	case when new.[HOME STATE] is not null then 'United States' else vw.[Home Address_country] end as [Home Address_country], 
	alpha.[HOME PHONE] as [Home Address_Phone], 
	coalesce(new.[HOME FAX],vw.[Home Address_Fax]) as [Home Address_Fax],
	vw.[Home Address_Cell], vw.[Home Address_2nd Phone],

	coalesce(new.WEBSITE,vw.[Business Website]) as [Business Website],
	alpha.Email as [Email],
	coalesce(new.[HOME EMAIL],vw.[Personal Email]) as [Personal Email],
	vw.[Other Email],

	alpha.[JOIN DATE] as [AGD Join Date],
	cast(alpha.[ID] as varchar(255)) as [AGD Member Number], 
	alpha.[PAID THRU] as [AGD Paid Thru Date],
	cast(alpha.[FIRST NAME] as varchar(255)) as [Badge Name], 

	cast(case
	when alpha.[MEMBER TYPE] = 'AC' and alpha.[CATEGORY DESCRIPTION] = 'Disability Waiver' then 'General Dentist|Dues Waived'
	when alpha.[MEMBER TYPE] IN ('AC','EM','HM') then 'General Dentist'
	when alpha.[MEMBER TYPE] = 'AF' then 'Affiliate'
	when alpha.[MEMBER TYPE] = 'AS' then 'General Dentist'
	when alpha.[MEMBER TYPE] = 'RE' then 'Retired'
	when alpha.[MEMBER TYPE] = 'ST' then 'Student'
	else '' end as varchar(max)) as [Contact Type],
	alpha.[BIRTH DATE] as [Date of Birth],
	replace(replace(alpha.[DESIGNATION],',','|'),' ','') as [Designation],
	case 
	when new.ETHNICITY is not null and new.ETHNICITY = 'African American' then 'African-American'
	when new.ETHNICITY is not null and new.ETHNICITY = 'Hispanic or Latino' then 'Hispanic'
	when new.ETHNICITY is not null and new.ETHNICITY = 'White or Caucasian' then 'Caucasian'
	when new.ETHNICITY is not null and new.ETHNICITY = 'Asian or Pacific IslANDer' then 'Asian'
	else coalesce(new.ETHNICITY,vw.[Ethnicity])	end as [Ethnicity],
	coalesce(new.GENDER,vw.[Gender]) as [Gender],
	alpha.[GRAD DATE] as [Graduation Date],
	coalesce(new.LICENSENO,vw.[License Number]) as [License Number],
	cast(case
	when alpha.[MEMBER TYPE] = 'AC' and alpha.[CATEGORY DESCRIPTION] = 'Disability Waiver' then 'Disability Waiver'
	when alpha.[MEMBER TYPE] = 'AC' and alpha.[CATEGORY DESCRIPTION] = 'Residency' then 'Resident'
	when alpha.[MEMBER TYPE] = 'AC' then 'Active General Dentist'
	when alpha.[MEMBER TYPE] = 'AF' then 'Affiliate'
	when alpha.[MEMBER TYPE] = 'AS' then 'Associate'
	when alpha.[MEMBER TYPE] = 'EM' then 'Emeritus'
	when alpha.[MEMBER TYPE] = 'HM' then 'Honorary Member'
	when alpha.[MEMBER TYPE] = 'RE' then 'Retired'
	when alpha.[MEMBER TYPE] = 'ST' then 'Student Member'
	else '' end as varchar(255)) as [MemberType],
	cast(case 
	when alpha.[DENTAL SCHOOL] = 'Texas A&M Health Science Center Baylor College of Dentistry' then null
	when alpha.[DENTAL SCHOOL] = 'University of Texas Dental Branch at Houston' then null
	when alpha.[DENTAL SCHOOL] = 'University of Texas Health Science Center at San Antonio Dental School' then null
	else alpha.[DENTAL SCHOOL] end as varchar(255)) as [Other Dental School],
	cast(case alpha.[DENTAL SCHOOL]
	when 'Texas A&M Health Science Center Baylor College of Dentistry' then 'Baylor College of Dentistry'
	when 'University of Texas Dental Branch at Houston' then 'UTHSCS Houston'
	when 'University of Texas Health Science Center at San Antonio Dental School' then 'UTHSC San Antonio'
	else null end as varchar(255)) as [Texas Dental School],
	coalesce(new.[PRACTICE ENVIRONMENT],vw.[Type of Practice]) as [Type of Practice]
into ##tagd_nationals_Members
from ##tagd_nationals_alpha as alpha
left outer join membercentral.dbo.ams_members as m
	inner join membercentral.dbo.vw_memberdata_TAGD as vw on vw.memberID = m.memberID
	on m.orgID = @orgID and m.membernumber = alpha.[ID] and m.status <> 'D' and m.memberid = m.activeMemberID
left outer join ##tagd_nationals_new as new on new.ID = alpha.ID

-- get active members in database now so we dont inactivate them
insert into ##tagd_nationals_Members (
	[prefix],[firstname],[middlename],[lastname],[suffix],[professionalSuffix],[memberNumber],[company],[Mailing Address_address1],[Mailing Address_address2],
	[Mailing Address_city],[Mailing Address_stateprov],[Mailing Address_postalCode],[Mailing Address_county],[Mailing Address_country],[Mailing Address_Phone],
	[Mailing Address_Fax],[Mailing Address_Cell],[Mailing Address_2nd Phone],[Office Address_address1],[Office Address_address2],[Office Address_city],
	[Office Address_stateprov],[Office Address_postalCode],[Office Address_county],[Office Address_country],[Office Address_Phone],[Office Address_Fax],
	[Office Address_Cell],[Office Address_2nd Phone],[Other Address_address1],[Other Address_address2],[Other Address_city],[Other Address_stateprov],
	[Other Address_postalCode],[Other Address_county],[Other Address_country],[Other Address_Phone],[Other Address_Fax],[Other Address_Cell],
	[Other Address_2nd Phone],[Home Address_address1],[Home Address_address2],[Home Address_city],[Home Address_stateprov],[Home Address_postalCode],
	[Home Address_county],[Home Address_country],[Home Address_Phone],[Home Address_Fax],[Home Address_Cell],[Home Address_2nd Phone],[Business Website],
	[Email],[Personal Email],[Other Email],[AGD Join Date],[AGD Member Number],[AGD Paid Thru Date],[Badge Name],[Contact Type],[Date of Birth],[Designation],
	[Ethnicity],[Gender],[Graduation Date],[License Number],[MemberType],[Other Dental School],[Texas Dental School],[Type of Practice]
)
select m.prefix, m.firstname, m.middlename, m.lastname, m.suffix, m.professionalSuffix, m.membernumber, m.company, vw.[Mailing Address_address1], vw.[Mailing Address_address2],
	vw.[Mailing Address_city],vw.[Mailing Address_stateprov],vw.[Mailing Address_postalCode],vw.[Mailing Address_county],vw.[Mailing Address_country],vw.[Mailing Address_Phone],
	vw.[Mailing Address_Fax],vw.[Mailing Address_Cell],vw.[Mailing Address_2nd Phone],vw.[Office Address_address1],vw.[Office Address_address2],vw.[Office Address_city],
	vw.[Office Address_stateprov],vw.[Office Address_postalCode],vw.[Office Address_county],vw.[Office Address_country],vw.[Office Address_Phone],vw.[Office Address_Fax],
	vw.[Office Address_Cell],vw.[Office Address_2nd Phone],vw.[Other Address_address1],vw.[Other Address_address2],vw.[Other Address_city],vw.[Other Address_stateprov],
	vw.[Other Address_postalCode],vw.[Other Address_county],vw.[Other Address_country],vw.[Other Address_Phone],vw.[Other Address_Fax],vw.[Other Address_Cell],
	vw.[Other Address_2nd Phone],vw.[Home Address_address1],vw.[Home Address_address2],vw.[Home Address_city],vw.[Home Address_stateprov],vw.[Home Address_postalCode],
	vw.[Home Address_county],vw.[Home Address_country],vw.[Home Address_Phone],vw.[Home Address_Fax],vw.[Home Address_Cell],vw.[Home Address_2nd Phone],vw.[Business Website],
	vw.[Email],vw.[Personal Email],vw.[Other Email],vw.[AGD Join Date],vw.[AGD Member Number],vw.[AGD Paid Thru Date],vw.[Badge Name],vw.[Contact Type],vw.[Date of Birth],vw.[Designation],
	vw.[Ethnicity],vw.[Gender],vw.[Graduation Date],vw.[License Number],vw.[MemberType],vw.[Other Dental School],vw.[Texas Dental School],vw.[Type of Practice]
from membercentral.dbo.ams_members as m
inner join membercentral.dbo.vw_memberdata_TAGD as vw on vw.memberID = m.memberID
where m.orgID = @orgID
and m.status = 'A'
and m.memberid = m.activeMemberID
and m.memberTypeID = 2
and not exists (select membernumber from ##tagd_nationals_Members where membernumber = m.membernumber)

-- add rowID
ALTER TABLE ##tagd_nationals_Members ADD rowIDtemp int IDENTITY(1,1), rowID int NULL;
update ##tagd_nationals_Members set rowID = rowIDtemp;
ALTER TABLE ##tagd_nationals_Members DROP COLUMN rowIDtemp;

-- move data to holding tables
EXEC memberCentral.dbo.ams_importMemberData_tempToHolding @orgid=@orgID, @tmptbl='##tagd_nationals_Members', @pathToExport=@pathToExport, @importResult=@importResult OUTPUT

-- if no errors, run member import
IF @importResult.value('count(/import/errors/error)','int') = 0
BEGIN
	SELECT @flatfile = @importResult.value('(/import/@flatfile)[1]','varchar(160)')
	EXEC membercentral.dbo.ams_importMemberData_holdingToPerm @orgID=@orgID, @flatfile=@flatfile

	BEGIN TRY
		DECLARE @smtpserver varchar(20)
		IF @@SERVERNAME IN ('DEV04\PLATFORM2008','DEV03\PLATFORM2008','STAGING01\PLATFORM2008')
			SELECT @smtpserver = 'mail.trialsmith.com'
		ELSE
			SELECT @smtpserver = '***********'
		EXEC membercentral.dbo.ams_importMemberDataHoldingResultReport @importResult=@importResult, @finalMSG=@finalMSG OUTPUT
		EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject='TAGD Membership Import Results',
			@message=@finalMSG, @priority='normal', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
		SELECT @emailtouse = emailImportResults from membercentral.dbo.organizations where orgID = @orgID	
		IF len(@emailtouse) > 0 BEGIN
			EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to=@emailtouse,
				@cc=null, @bcc='<EMAIL>', @subject='TAGD Membership Import Results',
				@message=@finalMSG, @priority='normal', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
		END
	END TRY
	BEGIN CATCH      
		DECLARE @ErrorMessage VARCHAR(max)      
		DECLARE @ErrorSeverity INT      
		DECLARE @ErrorState INT        
		SELECT @ErrorMessage = 'Error running TAGD Membership Import' + char(13) + char(10) + ERROR_MESSAGE(),   
			@ErrorSeverity = ERROR_SEVERITY(),   
			@ErrorState = ERROR_STATE()        
		EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject='Error Importing TAGD Data',
			@message=@ErrorMessage, @priority='high', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END CATCH


	/* ***************** */
	/* ACCOUNTING IMPORT */
	/* ***************** */
	-- prep acct table (ignore $0)
	IF OBJECT_ID('tempdb..##tagd_nationals_acct') IS NOT NULL
		DROP TABLE ##tagd_nationals_acct
	select 'TAGD Dues ' + cast(year([PAID THRU]) as varchar(4)) as SaleDescription,
		@batchDate as saleDate,
		[ID] as saleMemberID,
		[Constituent PAID] as saleAmount,
		'4010' as saleRevenueGL,
		rowID as saleID,
		rowID as rowID
	into ##tagd_nationals_acct
	from ##tagd_nationals_dues
	where [Constituent PAID] <> 0

	set @importResult = null
	EXEC membercentral.dbo.tr_importTransactions_toTemp @orgid=@orgID, @payProfileCode='AGDimport', @tmptbl='##tagd_nationals_acct', @pathToExport=@pathToExport, @importResult=@importResult OUTPUT

	-- if no errors, run accounting import
	IF @importResult.value('count(/import/errors/error)','int') = 0
	BEGIN
		SELECT @flatfile = @importResult.value('(/import/@flatfile)[1]','varchar(160)')
		set @importResult = null
		EXEC membercentral.dbo.tr_importTransactions_toPerm @orgID=@orgID, @siteID=@siteID, @recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, @flatfile=@flatfile, @importResult=@importResult OUTPUT


		IF @importResult.value('count(/import/errors/error)','int') = 0
		BEGIN
			/* ******************* */
			/* SUBSCRIPTION IMPORT */
			/* ******************* */
			-- Start prep work for importing subs
			IF OBJECT_ID('tempdb..#tagd_sub_renewals') IS NOT NULL
				DROP TABLE #tagd_sub_renewals
			IF OBJECT_ID('tempdb..#tagd_sub_newmembers') IS NOT NULL
				DROP TABLE #tagd_sub_newmembers
			IF OBJECT_ID('tempdb..#tagd_sub_newComponent') IS NOT NULL
				DROP TABLE #tagd_sub_newComponent
			IF OBJECT_ID('tempdb..#tagd_sub_switchedComponent') IS NOT NULL
				DROP TABLE #tagd_sub_switchedComponent
			IF OBJECT_ID('tempdb..#tagd_memberType') IS NOT NULL
				DROP TABLE #tagd_memberType
			IF OBJECT_ID('tempdb..##tagd_import_subscriptions') IS NOT NULL
				DROP TABLE ##tagd_import_subscriptions

			declare @subscriberIDList varchar(8000), @membershipSubscriptionType varchar(100), 
				@componentSubscriptionType varchar(100), @membershipSubscriptionName varchar(100),
				@thismemberid int, @thissubscriptionID int, @thisparentSubscriberID int, 
				@thisRFID int, @thisGLAccountID int, @thisstatus varchar(1), @thissubStartDate datetime,
				@thissubEndDate datetime, @thisgraceEndDate datetime, @thispcfree bit, 
				@thisactivationOptionCode char(1), @thisrecordedByMemberID int,  @thisbypassQueue bit,
				@thissubscriberID int, @thisexistingComponentSubscriberID int, @trashID int,
				@autoID int, @currentTimeStamp datetime
			set @currentTimeStamp = getdate()
			set @membershipSubscriptionType = 'Membership Dues'
			set @componentSubscriptionType = 'Components'
			set @membershipSubscriptionName = 'TAGD Member'

			CREATE TABLE #tagd_sub_renewals (memberID int, memberNumber varchar(100), subStartDate datetime,
				subEndDate datetime, rootSubscriptionID int, componentSubscriptionID int, treecode uniqueIdentifier)
			CREATE TABLE #tagd_sub_newmembers (memberID int, memberNumber varchar(100), subStartDate datetime,
				subEndDate datetime, rootSubscriptionID int, componentSubscriptionID int, treecode uniqueIdentifier)
			CREATE TABLE #tagd_sub_newComponent (autoID int IDENTITY(1,1) PRIMARY KEY, memberID int,
				memberNumber varchar(100), existingRootSubscriberID int, existingComponentSubscriberID int,
				subStartDate datetime, subEndDate datetime, graceEndDate datetime, rootSubscriptionID int,
				componentSubscriptionID int, rfid int, GLAccountID int, status char(1), pcfree bit, 
				activationOptionCode varchar(1), existingComponentSubscriptionID int)
			CREATE TABLE #tagd_sub_switchedComponent (autoID int IDENTITY(1,1) PRIMARY KEY, memberID int,
				memberNumber varchar(100), existingRootSubscriberID int, existingComponentSubscriberID int,
				subStartDate datetime, subEndDate datetime, graceEndDate datetime, rootSubscriptionID int,
				componentSubscriptionID int, rfid int, GLAccountID int, status char(1), pcfree bit, 
				activationOptionCode varchar(1), existingComponentSubscriptionID int)
			CREATE TABLE #tagd_memberType (tempMemberTypeId int NOT NULL identity(1,1),
				ValueForMemberTypeCustomField varchar(255) NOT NULL DEFAULT (''), 
				MemberTypeValueFromAGDAlphaRoster varchar(10) NOT NULL DEFAULT (''), 
				CategoryDescriptionValueFromAGDAlphaRoster varchar(255) NULL DEFAULT (''), 
				MembershipDuesTAGDMemberSubRate varchar(255) NOT NULL DEFAULT ('')
			)

			insert into #tagd_sub_renewals (memberID, memberNumber, subStartDate, subEndDate, rootSubscriptionID, 
				componentSubscriptionID, treecode)
			select activeMember.memberID, na.id, 
				newStartDate = case 
					when (isnull(na.[JOIN DATE],'1/1/1900') > cast (cast(YEAR(na.[PAID THRU]) as nvarchar)+'0101' as datetime)) and (na.[JOIN DATE] > DATEADD(dd, DATEDIFF(dd, 0,dateadd(d,1,max(ss.subenddate))), 0))
						then isnull(na.[JOIN DATE],'1/1/1900')
					when (max(ss.subenddate) > cast (cast(YEAR(na.[PAID THRU]) as nvarchar)+'0101' as datetime)) and (DATEADD(dd, DATEDIFF(dd, 0,dateadd(d,1,max(ss.subenddate))), 0) > isnull(na.[JOIN DATE],'1/1/1900'))
						then DATEADD(dd, DATEDIFF(dd, 0,dateadd(d,1,max(ss.subenddate))), 0)
					else
						cast (cast(YEAR(na.[PAID THRU]) as nvarchar)+'0101' as datetime) 
					end,
				newSubEndDate = dateadd(ms,-3,dateadd(day,1,[Paid Thru])),
				subs.subscriptionID as rootSubscriptionID, 
				csubs.subscriptionID as componentSubscripionID,
				treecode = newid()
			from membercentral.dbo.sub_types t
			inner join membercentral.dbo.sub_subscriptions subs on subs.typeID = t.typeID
				and t.typeName = @membershipSubscriptionType
				and subs.subscriptionName = @membershipSubscriptionName
				and t.siteID = @siteID
			inner join membercentral.dbo.sub_subscribers ss on ss.subscriptionID = subs.subscriptionID
				and ss.parentSubscriberID is null
			inner join membercentral.dbo.sub_statuses st on st.statusID = ss.statusID
				and st.statuscode in ('A','I','P')
			inner join membercentral.dbo.ams_members m on m.memberID = ss.memberID
			inner join membercentral.dbo.ams_members activeMember on activeMember.memberID = m.activeMemberID
				and activeMember.status in ('A','I')
			--make sure we have subscriberID with the latest end date
			left outer join membercentral.dbo.sub_subscribers otherterms_ss
				inner join membercentral.dbo.sub_statuses otherterms_st on otherterms_st.statusID = otherterms_ss.statusID
					and otherterms_st.statuscode in ('A','I','P')
				inner join membercentral.dbo.ams_members otherterms_m on otherterms_m.memberID = otherterms_ss.memberID
				on ss.subscriptionID = otherterms_ss.subscriptionID
					and ss.subenddate < otherterms_ss.subenddate
					and otherterms_m.activeMemberID = activeMember.memberID
			inner join ##tagd_nationals_alpha na on na.id = activeMember.membernumber
				and na.[Paid Thru] is not null
				and ss.subenddate < na.[paid thru]
				and na.[paid thru] > @currentTimeStamp
			inner join membercentral.dbo.sub_types ct on ct.siteID = t.siteID
				and ct.typeName = @componentSubscriptionType
			inner join membercentral.dbo.sub_subscriptions csubs on csubs.typeID = ct.typeID
				and csubs.subscriptionName = na.[COMPONENT TITLE]
			where otherterms_ss.subscriberID is null
			group by activeMember.memberID,na.id,  na.[paid thru], na.[JOIN DATE], subs.subscriptionID, csubs.subscriptionID
			order by na.[JOIN DATE] desc

			insert into #tagd_sub_newmembers (memberID, memberNumber,subStartDate,subEndDate, rootSubscriptionID, componentSubscriptionID,treecode)
			select 
				activeMember.memberID,
				na.id,
				newStartDate = case 
					when (isnull(na.[JOIN DATE],'1/1/1900') > cast (cast(YEAR(na.[PAID THRU]) as nvarchar)+'0101' as datetime))
						then isnull(na.[JOIN DATE],'1/1/1900')
					else
						cast (cast(YEAR(na.[PAID THRU]) as nvarchar)+'0101' as datetime) 
					end,
				newSubEndDate = dateadd(ms,-3,dateadd(day,1,[Paid Thru])),
				subs.subscriptionID as rootSubscriptionID, 
				csubs.subscriptionID as componentSubscripionID,
				treecode = newid()
			from 
				##tagd_nationals_alpha na
				inner join membercentral.dbo.ams_members activeMember
					on activeMember.memberNumber = na.id
					and activeMember.orgID = @orgID
					and activeMember.status in ('A','I')
					and activeMember.memberID = activeMember.activeMemberID
					and na.[paid thru] > @currentTimeStamp
				inner join membercentral.dbo.ams_members m
					on activeMember.memberID = m.activeMemberID
				inner join membercentral.dbo.sub_types t
					on t.typeName = @membershipSubscriptionType
					and t.siteID = @siteID
				inner join membercentral.dbo.sub_subscriptions subs
					on subs.typeID = t.typeID
					and subs.subscriptionName = @membershipSubscriptionName
				left outer join membercentral.dbo.sub_subscribers ss
					inner join membercentral.dbo.sub_statuses st
						on st.statusID = ss.statusID
						and st.statuscode in ('A','I','P')
						and ss.parentSubscriberID is null
				on m.memberID = ss.memberID
					and ss.subscriptionID = subs.subscriptionID
				inner join membercentral.dbo.sub_types ct
					on ct.siteID = @siteID
					and ct.typeName = @componentSubscriptionType
				inner join membercentral.dbo.sub_subscriptions csubs
					on csubs.typeID = ct.typeID
					and csubs.subscriptionName = na.[COMPONENT TITLE]
			group by 
				activeMember.memberID, na.id,  na.[paid thru], na.[JOIN DATE], subs.subscriptionID, csubs.subscriptionID
			having 
				max(isnull(ss.subscriberID,0)) = 0
			order by 
				na.[JOIN DATE] desc

			insert into #tagd_sub_newComponent (
				memberID, 
				memberNumber,
				subStartDate,
				subEndDate, 
				graceEndDate, 
				rootSubscriptionID, 
				componentSubscriptionID, 
				existingRootSubscriberID, 
				existingComponentSubscriberID,
				existingComponentSubscriptionID
			)
			select 
				activeMember.memberID,
				na.id,
				-- case statement to handle changing future Accepted subscriptions
				newStartDate = case 
					when (@currentTimeStamp > ss.substartDate)
						then @currentTimeStamp
					else
						ss.substartDate
					end,
				newSubEndDate = ss.subEndDate,
				graceEndDate = cast (cast(YEAR(ss.subEndDate) + 1 as nvarchar)+'0331' as datetime),
				subs.subscriptionID as rootSubscriptionID, 
				correct_csubs.subscriptionID as componentSubscripionID,
				ss.rootSubscriberID as existingRootSubscriberID, 
				current_css.subscriberID as existingComponentSubscriberID,
				current_css.subscriptionID as existingComponentSubscriptionID
			from 
				membercentral.dbo.sub_types t
				inner join membercentral.dbo.sub_subscriptions subs
					on subs.typeID = t.typeID
					and t.typeName = @membershipSubscriptionType
					and subs.subscriptionName = @membershipSubscriptionName
					and t.siteID = @siteID
				inner join membercentral.dbo.sub_subscribers ss
					on ss.subscriptionID = subs.subscriptionID
					and ss.parentSubscriberID is null
				inner join membercentral.dbo.sub_statuses st
					on st.statusID = ss.statusID
					and st.statuscode in ('A','I','P')
				inner join membercentral.dbo.ams_members m
					on m.memberID = ss.memberID
				inner join membercentral.dbo.ams_members activeMember
					on activeMember.memberID = m.activeMemberID
					and activeMember.status in ('A','I')
				inner join ##tagd_nationals_alpha na
					on na.id = activeMember.membernumber
					and na.[Paid Thru] is not null
				inner join membercentral.dbo.sub_types ct
					on ct.siteID = t.siteID
					and ct.typeName = @componentSubscriptionType
				inner join membercentral.dbo.sub_subscriptions correct_csubs
					on correct_csubs.typeID = ct.typeID
					and correct_csubs.subscriptionName = na.[COMPONENT TITLE]
				left outer join membercentral.dbo.sub_subscribers current_css
					inner join membercentral.dbo.sub_statuses current_cst
						on current_cst.statusID = current_css.statusID
						and current_cst.statuscode in ('A','I','P')
					on current_css.parentSubscriberID = ss.subscriberID
				and correct_csubs.subscriptionID = current_css.subscriptionID
			where
				current_css.subscriptionID is null
			group by 
				current_cst.statuscode, activeMember.memberID,na.id,  na.[paid thru], na.[JOIN DATE], subs.subscriptionID, 
				correct_csubs.subscriptionID, ss.rootSubscriberID, current_css.subscriberID,
				current_css.subscriptionID, ss.substartDate, ss.subEndDate
			order by na.[JOIN DATE] desc

			insert into #tagd_sub_switchedComponent (
				memberID, 
				memberNumber,
				subStartDate,
				subEndDate, 
				graceEndDate, 
				rootSubscriptionID, 
				componentSubscriptionID, 
				existingRootSubscriberID, 
				existingComponentSubscriberID,
				existingComponentSubscriptionID
			)
			select 
				activeMember.memberID,
				na.id,
				-- case statement to handle changing future Accepted subscriptions
				newStartDate = case 
					when (@currentTimeStamp > ss.substartDate)
						then @currentTimeStamp
					else
						ss.substartDate
					end,
				newSubEndDate = ss.subEndDate,
				graceEndDate = cast (cast(YEAR(ss.subEndDate) + 1 as nvarchar)+'0331' as datetime),
				subs.subscriptionID as rootSubscriptionID, 
				correct_csubs.subscriptionID as componentSubscripionID,
				ss.rootSubscriberID as existingRootSubscriberID, 
				current_css.subscriberID as existingComponentSubscriberID,
				current_css.subscriptionID as existingComponentSubscriptionID
			from 
				membercentral.dbo.sub_types t
				inner join membercentral.dbo.sub_subscriptions subs
					on subs.typeID = t.typeID
					and t.typeName = @membershipSubscriptionType
					and subs.subscriptionName = @membershipSubscriptionName
					and t.siteID = @siteID
				inner join membercentral.dbo.sub_subscribers ss
					on ss.subscriptionID = subs.subscriptionID
					and ss.parentSubscriberID is null
				inner join membercentral.dbo.sub_statuses st
					on st.statusID = ss.statusID
					and st.statuscode in ('A','I','P')
				inner join membercentral.dbo.ams_members m
					on m.memberID = ss.memberID
				inner join membercentral.dbo.ams_members activeMember
					on activeMember.memberID = m.activeMemberID
					and activeMember.status in ('A','I')
				inner join ##tagd_nationals_alpha na
					on na.id = activeMember.membernumber
					and na.[Paid Thru] is not null
				inner join membercentral.dbo.sub_types ct
					on ct.siteID = t.siteID
					and ct.typeName = @componentSubscriptionType
				inner join membercentral.dbo.sub_subscriptions correct_csubs
					on correct_csubs.typeID = ct.typeID
					and correct_csubs.subscriptionName = na.[COMPONENT TITLE]
				inner join membercentral.dbo.sub_subscribers current_css
					on current_css.parentSubscriberID = ss.subscriberID
					and correct_csubs.subscriptionID <> current_css.subscriptionID
				inner join membercentral.dbo.sub_statuses current_cst
					on current_cst.statusID = current_css.statusID
					and current_cst.statuscode in ('A','I','P')
			group by 
				activeMember.memberID,na.id,  na.[paid thru], na.[JOIN DATE], subs.subscriptionID, 
				correct_csubs.subscriptionID, ss.rootSubscriberID, current_css.subscriberID,
				current_css.subscriptionID, ss.substartDate, ss.subEndDate
			order by na.[JOIN DATE] desc

			update sc set
				GLAccountID = case
					when subs.allowRateGLAccountOverride = 1 then isnull(r.GLAccountID,subs.GLAccountID) 
					else subs.GLAccountID
				end, 
				rfid=rf.rfID,
				pcfree=0, 
				activationOptionCode = sao.subActivationCode,
				Status = case 
					when subStartDate > @currentTimeStamp then 'P'
					else 'A'
				end
			from 
				#tagd_sub_switchedComponent sc
				inner join membercentral.dbo.sub_subscriptions subs
					on subs.subscriptionID = sc.componentSubscriptionID
				inner join membercentral.dbo.sub_rateSchedules as rs 
					on subs.scheduleID = rs.scheduleID 
					and rs.status = 'A'
				inner join membercentral.dbo.sub_rates r
					on r.scheduleID = rs.scheduleID
					and r.status = 'A'
					and r.isRenewalRate = 0 
					and r.rateName = 'Annual Term'
				inner join membercentral.dbo.sub_rateFrequencies rf on
					rf.rateID = r.rateID
					and rf.status = 'A'
				inner join membercentral.dbo.sub_frequencies f on
					f.frequencyID = rf.frequencyID
					and f.status = 'A'
				inner join membercentral.dbo.sub_rateFrequenciesMerchantProfiles as rfmp on 
					rfmp.rfid = rf.rfID 
					and rfmp.status = 'A' 
				inner join membercentral.dbo.mp_profiles as mp on 
					mp.profileID = rfmp.profileID 
					and mp.status = 'A' 
				inner join membercentral.dbo.sub_activationOptions sao
					on sao.subActivationID = subs.subAlternateActivationID

			update nc set
				GLAccountID = case
					when subs.allowRateGLAccountOverride = 1 then isnull(r.GLAccountID,subs.GLAccountID) 
					else subs.GLAccountID
				end, 
				rfid=rf.rfID,
				pcfree=0, 
				activationOptionCode = sao.subActivationCode,
				Status = case 
					when subStartDate > @currentTimeStamp then 'P'
					else 'A'
				end
			from 
				#tagd_sub_newComponent nc
				inner join membercentral.dbo.sub_subscriptions subs
					on subs.subscriptionID = nc.componentSubscriptionID
				inner join membercentral.dbo.sub_rateSchedules as rs 
					on subs.scheduleID = rs.scheduleID 
					and rs.status = 'A'
				inner join membercentral.dbo.sub_rates r
					on r.scheduleID = rs.scheduleID
					and r.status = 'A'
					and r.isRenewalRate = 0 
					and r.rateName = 'Annual Term'
				inner join membercentral.dbo.sub_rateFrequencies rf on
					rf.rateID = r.rateID
					and rf.status = 'A'
				inner join membercentral.dbo.sub_frequencies f on
					f.frequencyID = rf.frequencyID
					and f.status = 'A'
				inner join membercentral.dbo.sub_rateFrequenciesMerchantProfiles as rfmp on 
					rfmp.rfid = rf.rfID 
					and rfmp.status = 'A' 
				inner join membercentral.dbo.mp_profiles as mp on 
					mp.profileID = rfmp.profileID 
					and mp.status = 'A' 
				inner join membercentral.dbo.sub_activationOptions sao
					on sao.subActivationID = subs.subAlternateActivationID

			insert into #tagd_memberType (
				ValueForMemberTypeCustomField,
				MemberTypeValueFromAGDAlphaRoster,
				CategoryDescriptionValueFromAGDAlphaRoster,
				MembershipDuesTAGDMemberSubRate
			)
			select 'Active Member', 'AC', '',	'Active General Dentist'
			union
			select 'Affiliate', 'AF', '',	'Affiliate'
			union
			select 'Associate', 'AS', '',	'Associate'
			union
			select 'Disability Waiver', 'AC', 'Disability Waiver',	'Dues Waiver'
			union
			select 'Emeritus Member', 'EM', '',	'Emeritus'
			union
			select 'Honorary Member', 'HM', '',	'Honorary Member'
			union
			select 'Retired', 'RE', '',	'Retired'
			union
			select 'Resident', 'AC', 'Residency',	'Resident'
			union
			select 'Student Member', 'ST', '',	'Student'
			union
			select 'Financial Waiver', 'AC', 'Financial Waiver','Dues Waiver'

			IF OBJECT_ID('tempdb..#temp_tagd_nationals_alpha') IS NOT NULL
				DROP TABLE #temp_tagd_nationals_alpha

			-- Inner join alpha with memberType so we can get the right rate
			select * 
			into #temp_tagd_nationals_alpha
			from ##tagd_nationals_alpha na
			left outer join #tagd_memberType mt on ltrim(rtrim(mt.MemberTypeValueFromAGDAlphaRoster)) = ltrim(rtrim(na.[member type]))
				and ltrim(rtrim(mt.CategoryDescriptionValueFromAGDAlphaRoster )) = ltrim(rtrim(na.[category description]))
			order by na.[category description] desc

			-- Get the query to be passed to import subscriptions stored proc
			select *  
			into ##tagd_import_subscriptions 
			from (
				select 
					distinct renewals.memberNumber
					,@membershipSubscriptionType as SubscriptionType
					, subs.SubscriptionName
					,r.RateName
					, rf.rateAmt as LastPrice
					, f.frequencyShortName as Frequency
					, 'NO' as StoreModifiedRate
					, renewals.subStartDate as StartDate
					, renewals.subEndDate as EndDate
					, cast (cast(YEAR(renewals.subEndDate) + 1 as nvarchar)+'0331' as datetime) as graceEndDate
					, Status = case 
						when renewals.subStartDate > @currentTimeStamp then 'P'
						else 'A'
					end
					, NULL as ParentSubscriptionType
					, NULL  as ParentSubscriptionName
					, renewals.treecode as TreeCode
				from (
					select memberID, memberNumber,subStartDate,subEndDate, rootSubscriptionID, componentSubscriptionID, treecode from #tagd_sub_renewals
					union
					select memberID, memberNumber,subStartDate,subEndDate, rootSubscriptionID, componentSubscriptionID, treecode from #tagd_sub_newmembers 
				) as renewals
				inner join membercentral.dbo.sub_subscriptions as subs
					on renewals.rootSubscriptionID = subs.subscriptionID
					and subs.status = 'A' 
				inner join membercentral.dbo.sub_rateSchedules as rs 
					on subs.scheduleID = rs.scheduleID 
					and rs.status = 'A'
				inner join membercentral.dbo.sub_rates r
					on r.scheduleID = rs.scheduleID
					and r.status = 'A'
					and r.isRenewalRate = 0 
				inner join #tagd_memberType mt on 
					ltrim(rtrim(mt.MembershipDuesTAGDMemberSubRate)) = ltrim(rtrim(r.rateName))
				inner join #temp_tagd_nationals_alpha tmt on
					tmt.tempMemberTypeId = mt.tempMemberTypeId	
					and tmt.id = renewals.memberNumber	
				inner join membercentral.dbo.sub_rateFrequencies rf on
					rf.rateID = r.rateID
					and rf.status = 'A'
				inner join membercentral.dbo.sub_frequencies f on
					f.frequencyID = rf.frequencyID
					and f.status = 'A'
				inner join membercentral.dbo.sub_rateFrequenciesMerchantProfiles as rfmp on 
					rfmp.rfid = rf.rfID 
					and rfmp.status = 'A' 
				inner join membercentral.dbo.mp_profiles as mp on 
					mp.profileID = rfmp.profileID 
					and mp.status = 'A' 
				union
				select 
					distinct renewals.memberNumber
					,@componentSubscriptionType as SubscriptionType
					, subs.SubscriptionName
					,r.RateName
					, rf.rateAmt as LastPrice
					, f.frequencyShortName as Frequency
					, 'NO' as StoreModifiedRate
					, renewals.subStartDate as StartDate
					, renewals.subEndDate as EndDate
					, cast (cast(YEAR(renewals.subEndDate) + 1 as nvarchar)+'0331' as datetime) as graceEndDate
					, Status = case 
						when renewals.subStartDate > @currentTimeStamp then 'P'
						else 'A'
					end
					, @membershipSubscriptionType as ParentSubscriptionType
					, @membershipSubscriptionName  as ParentSubscriptionName
					, renewals.treecode as TreeCode
				from (
					select memberID, memberNumber,subStartDate,subEndDate, rootSubscriptionID, componentSubscriptionID, treecode from #tagd_sub_renewals
					union
					select memberID, memberNumber,subStartDate,subEndDate, rootSubscriptionID, componentSubscriptionID, treecode from #tagd_sub_newmembers 
				) as renewals
				inner join membercentral.dbo.sub_subscriptions as subs
					on renewals.componentSubscriptionID = subs.subscriptionID
					and subs.status = 'A' 
				inner join membercentral.dbo.sub_rateSchedules as rs 
					on subs.scheduleID = rs.scheduleID 
					and rs.status = 'A'
				inner join membercentral.dbo.sub_rates r
					on r.scheduleID = rs.scheduleID
					and r.status = 'A'
					and r.isRenewalRate = 0 
					and r.rateName = 'Annual Term'
				inner join membercentral.dbo.sub_rateFrequencies rf on
					rf.rateID = r.rateID
					and rf.status = 'A'
				inner join membercentral.dbo.sub_frequencies f on
					f.frequencyID = rf.frequencyID
					and f.status = 'A'
				inner join membercentral.dbo.sub_rateFrequenciesMerchantProfiles as rfmp on 
					rfmp.rfid = rf.rfID 
					and rfmp.status = 'A' 
				inner join membercentral.dbo.mp_profiles as mp on 
					mp.profileID = rfmp.profileID 
					and mp.status = 'A' 
			) tmp
			order by tmp.MemberNumber, tmp.ParentSubscriptionType

			DECLARE @qry varchar(400)
			SELECT @qry = 'ALTER TABLE ##tagd_import_subscriptions ADD rowID int NULL, rowID2 int identity(1,1);'
			EXEC(@qry)
			SELECT @qry = 'update ##tagd_import_subscriptions set rowID = rowID2;'
			EXEC(@qry)
			SELECT @qry = 'ALTER TABLE ##tagd_import_subscriptions DROP COLUMN rowID2;'
			EXEC(@qry)
			SELECT @qry = 'ALTER TABLE ##tagd_import_subscriptions ALTER COLUMN rowID int NOT NULL;'
			EXEC(@qry)

			-- Change components 
			select @autoID = min(autoID) from #tagd_sub_switchedComponent
			while @autoID is not null
			begin
				select 
					@thismemberid=NULL, 
					@thissubscriptionID=NULL, 
					@thisparentSubscriberID=NULL, 
					@thisRFID=NULL, 
					@thisGLAccountID=NULL, 
					@thisstatus=NULL, 
					@thissubStartDate=NULL, 
					@thissubEndDate=NULL, 
					@thisgraceEndDate = NULL, 
					@thispcfree=NULL, 
					@thisactivationOptionCode=NULL, 
					@thisbypassQueue=NULL, 
					@thisexistingComponentSubscriberID = NULL

				select 
					@thismemberid=memberID, 
					@thissubscriptionID=componentSubscriptionID, 
					@thisparentSubscriberID=existingRootSubscriberID, 
					@thisRFID=rfID, 
					@thisGLAccountID=GLAccountID, 
					@thisstatus=status, 
					@thissubStartDate=subStartDate, 
					@thissubEndDate=subEndDate, 
					@thisgraceEndDate = graceEndDate , 
					@thispcfree=pcfree, 
					@thisactivationOptionCode=activationOptionCode, 
					@thisbypassQueue=0, 
					@thisexistingComponentSubscriberID = existingComponentSubscriberID
				from #tagd_sub_switchedComponent
				where autoID = @autoID


				exec membercentral.dbo.sub_expireSubscriber
					@subscriberID=@thisexistingComponentSubscriberID, 
					@memberID=@thismemberid, 
					@siteID=@siteID, 
					@enteredByMemberID=@recordedByMemberID, 
					@statsSessionID=0, 
					@AROption='C', 
					@fReturnQuery=0

				exec membercentral.dbo.sub_addSubscriber
					@orgID=@orgID, 
					@memberid=@thismemberid, 
					@subscriptionID=@thissubscriptionID, 
					@parentSubscriberID=@thisparentSubscriberID, 
					@RFID=@thisRFID, 
					@GLAccountID=@thisGLAccountID, 
					@status=@thisstatus, 
					@subStartDate=@thissubStartDate, 
					@subEndDate=@thissubEndDate, 
					@graceEndDate=@thisgraceEndDate, 
					@pcfree=@thispcfree, 
					@activationOptionCode=@thisactivationOptionCode,
					@recordedByMemberID=@recordedByMemberID, 
					@bypassQueue=@thisbypassQueue, 
					@subscriberID=@trashID OUTPUT

				select @autoID = min(autoID) from #tagd_sub_switchedComponent where autoID > @autoID
			end

			-- Add individual components 
			set @autoID = null
			select @autoID = min(autoID) from #tagd_sub_newComponent
			while @autoID is not null
			begin

				select 
					@thismemberid=NULL, 
					@thissubscriptionID=NULL, 
					@thisparentSubscriberID=NULL, 
					@thisRFID=NULL, 
					@thisGLAccountID=NULL, 
					@thisstatus=NULL, 
					@thissubStartDate=NULL, 
					@thissubEndDate=NULL, 
					@thisgraceEndDate = NULL, 
					@thispcfree=NULL, 
					@thisactivationOptionCode=NULL, 
					@thisbypassQueue=NULL, 
					@thisexistingComponentSubscriberID = NULL

				select 
					@thismemberid=memberID, 
					@thissubscriptionID=componentSubscriptionID, 
					@thisparentSubscriberID=existingRootSubscriberID, 
					@thisRFID=rfID, 
					@thisGLAccountID=GLAccountID, 
					@thisstatus=status, 
					@thissubStartDate=subStartDate, 
					@thissubEndDate=subEndDate, 
					@thisgraceEndDate = graceEndDate , 
					@thispcfree=pcfree, 
					@thisactivationOptionCode=activationOptionCode, 
					@thisbypassQueue=0, 
					@thisexistingComponentSubscriberID = existingComponentSubscriberID
				from #tagd_sub_newComponent
				where autoID = @autoID

				exec membercentral.dbo.sub_addSubscriber
					@orgID=@orgID, 
					@memberid=@thismemberid, 
					@subscriptionID=@thissubscriptionID, 
					@parentSubscriberID=@thisparentSubscriberID, 
					@RFID=@thisRFID, 
					@GLAccountID=@thisGLAccountID, 
					@status=@thisstatus, 
					@subStartDate=@thissubStartDate, 
					@subEndDate=@thissubEndDate, 
					@graceEndDate=@thisgraceEndDate, 
					@pcfree=@thispcfree, 
					@activationOptionCode=@thisactivationOptionCode,
					@recordedByMemberID=@recordedByMemberID, 
					@bypassQueue=@thisbypassQueue, 
					@subscriberID=@trashID OUTPUT

				select @autoID = min(autoID) from #tagd_sub_newComponent where autoID > @autoID
			end

			set @importResult = null
			EXEC membercentral.dbo.sub_importSubscriptions_toTemp @siteID=@siteID, @tmptbl='##tagd_import_subscriptions', @pathToExport=@pathToExport, @importResult=@importResult OUTPUT
		
			-- if no errors, run subscription import
			IF @importResult.value('count(/import/errors/error)','int') = 0
			BEGIN
				SELECT @flatfile = @importResult.value('(/import/@flatfile)[1]','varchar(160)')
				set @importResult = null
				EXEC membercentral.dbo.sub_importSubscriptions_toPerm @siteID=@siteID, @recordedByMemberID=@recordedByMemberID, @flatfile=@flatfile, @importResult=@importResult OUTPUT
			END
		END
	END
END

-- cleanup
on_cleanup:
	IF OBJECT_ID('tempdb..##tagd_nationals_alpha') IS NOT NULL
		DROP TABLE ##tagd_nationals_alpha
	IF OBJECT_ID('tempdb..##tagd_nationals_new') IS NOT NULL
		DROP TABLE ##tagd_nationals_new
	IF OBJECT_ID('tempdb..##tagd_nationals_dues') IS NOT NULL
		DROP TABLE ##tagd_nationals_dues
	IF OBJECT_ID('tempdb..##tagd_nationals_Members') IS NOT NULL
		DROP TABLE ##tagd_nationals_Members
	IF OBJECT_ID('tempdb..##tagd_nationals_acct') IS NOT NULL
		DROP TABLE ##tagd_nationals_acct
	IF OBJECT_ID('tempdb..#tagd_memberType') IS NOT NULL
		DROP TABLE #tagd_memberType
	IF OBJECT_ID('tempdb..#temp_tagd_nationals_alpha') IS NOT NULL
		DROP TABLE #temp_tagd_nationals_alpha
	IF OBJECT_ID('tempdb..##tagd_import_subscriptions') IS NOT NULL
		DROP TABLE ##tagd_import_subscriptions
	IF OBJECT_ID('tempdb..#tempTreeCodeTbl') IS NOT NULL
		DROP TABLE #tempTreeCodeTbl
	IF OBJECT_ID('tempdb..#tagd_sub_renewals') IS NOT NULL
		DROP TABLE #tagd_sub_renewals
	IF OBJECT_ID('tempdb..#tagd_sub_newmembers') IS NOT NULL
		DROP TABLE #tagd_sub_newmembers
	IF OBJECT_ID('tempdb..#tagd_sub_switchedComponent') IS NOT NULL
		DROP TABLE #tagd_sub_switchedComponent
	IF OBJECT_ID('tempdb..#tagd_sub_newComponent') IS NOT NULL
		DROP TABLE #tagd_sub_newComponent
GO

