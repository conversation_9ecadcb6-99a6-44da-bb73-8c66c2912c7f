use search
GO

declare @bucketID int
select @bucketID = bucketID from dbo.tblSearchBuckets where siteID = 46 and bucketName = 'Document Bank'

update dbo.tblSearchBuckets
set bucketName = 'Additional Resources'
where bucketID = @bucketID
GO

insert into dbo.tblSearchBuckets (siteid, bucketName, bucketGroupID, bucketTypeID, bucketOrder, bucketSettings, hideUntilSearched, isActive, restrictToGroupID, hideIfRestricted)
values (46, 'FileShare', 1, 34, 2, '<settings fileShareID="799" />', 0, 1, null, 0)
GO

update dbo.tblSearchBuckets set bucketOrder = 3 where bucketID = 313
update dbo.tblSearchBuckets set bucketOrder = 4 where bucketID = 1330
update dbo.tblSearchBuckets set bucketOrder = 5 where bucketID = 312
update dbo.tblSearchBuckets set bucketOrder = 6 where bucketID = 1246
update dbo.tblSearchBuckets set bucketOrder = 7 where bucketID = 311
GO
