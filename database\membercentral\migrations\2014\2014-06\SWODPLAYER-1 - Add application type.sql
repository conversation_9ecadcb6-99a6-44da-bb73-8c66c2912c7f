-- add swOnDemandPlayer app
declare @rc int, @classID int, @resourceTypeID int, @functionID int, @applicationTypeID int, @siteID int,
	@sectionID int, @publicGID int, @mainZoneID int, @directModeID int, @appPgResourceTypeID int,
	@applicationInstanceID int, @siteresourceID int, @appPageID int, @viewFID int, @trashID int,
	@resourceTypeFunctionID int
declare @applicationTypeName varchar(50), @applicationTypeDesc varchar(50), @suggestedPageName varchar(50)

select @classID = dbo.fn_getResourceTypeClassID('application');
select @applicationTypeName = 'swOnDemandPlayer';
select @applicationTypeDesc = 'SeminarWeb OnDemand Player';
select @suggestedPageName = 'swOnDemandPlayer';
select @mainZoneID = dbo.fn_getZoneID('Main')
select @directModeID = dbo.fn_getModeID('Direct')
select @appPgResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedPage')

-- create application type
EXEC dbo.cms_createSiteResourceType @resourceTypeClassID=@classID, @resourceType=@applicationTypeName, @resourceTypeID=@resourceTypeID OUTPUT
EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='View', @displayName='View', @functionID=@functionID OUTPUT
EXEC dbo.cms_createApplicationType @resourceTypeID=@resourceTypeID, @maxInstancesPerSite=1, @allowPageNameChange=0, @isHidden=1, 
	@applicationTypeName=@applicationTypeName, @applicationTypeDesc=@applicationTypeDesc, @suggestedPageName=@suggestedPageName, 
	@settingsXML = '<settings><setting name="isCommunityReady" value="0" /><setting name="isMultiInstanceReady" value="0" /></settings>', 
	@applicationTypeID=@applicationTypeID OUTPUT

-- give to superadmins
select @resourceTypeFunctionID = resourceTypeFunctionID from dbo.cms_siteResourceTypeFunctions where resourceTypeID = @resourceTypeID
EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID

-- create template
declare @templateID int
EXEC cms_createPageTemplate null, 1, 'SeminarWeb OnDemand Player', 'SeminarWeb OnDemand Player', 'SwOnDemandPlayerTemplate', 0, @templateID OUTPUT

-- give instance to sites that have semwebcatalog
SELECT @viewFID = dbo.fn_getResourceFunctionID('view',@resourceTypeID)
select @siteID = min(ai.siteID) 
	from dbo.cms_applicationInstances as ai
	inner join dbo.cms_applicationTypes as app on app.applicationTypeID = ai.applicationTypeID
	where app.applicationTypeName = 'SemWebCatalog'
while @siteID is not null BEGIN
	set @sectionID = null
	set @applicationInstanceID = null
	set @siteresourceID = null
	set @appPageID = null

	select @sectionID = sectionID from dbo.cms_pageSections where sectionName = 'Root' AND sectionCode = 'Root' AND siteID = @siteID

	EXEC dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=1, @sectionID=@sectionID, 
		@applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, 
		@pageTitle=@applicationTypeDesc, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=@templateID, 
		@pageModeID=@directModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, 
		@allowReturnAfterLogin=0, @applicationInstanceName=@applicationTypeDesc, @applicationInstanceDesc=null, 
		@applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, 
		@pageID = @appPageID OUTPUT

	select @siteID = min(ai.siteID) 
		from dbo.cms_applicationInstances as ai
		inner join dbo.cms_applicationTypes as app on app.applicationTypeID = ai.applicationTypeID
		where app.applicationTypeName = 'SemWebCatalog'
		and ai.siteID > @siteID
END
GO
update dbo.cms_pages set inheritPlacements = 0 where pagename = 'swOnDemandPlayer'
GO

