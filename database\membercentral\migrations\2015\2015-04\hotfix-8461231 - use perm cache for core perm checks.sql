use membercentral;
GO
CREATE FUNCTION [dbo].[fn_cache_perms_checkResourceRightsForGroupPrint] (
	@resourceid int,
	@functionID int,
	@groupPrintID int,
	@siteID int
)
RETURNS bit
AS
BEGIN
	DECLARE @allowAccess bit
	SELECT @allowAccess = 0

	SELECT @allowAccess =
		CASE 
			WHEN EXISTS (
				select gprp.groupRightPrintID
				from dbo.cache_perms_siteResourceFunctionRightPrints srfrp
				inner join dbo.cache_perms_groupPrintsRightPrints gprp
					on srfrp.rightPrintID = gprp.rightPrintID
					and srfrp.siteresourceID = @resourceID
					and srfrp.functionID = @functionID
					and gprp.groupPrintID = @groupPrintID
				)
				THEN 1
		ELSE 0
		END
	RETURN @allowAccess
END
GO
ALTER FUNCTION [dbo].[fn_cache_perms_checkResourceRights] (
	@resourceid int,
	@functionID int,
	@memberID int,
	@siteID int
)
RETURNS bit
AS
BEGIN

	DECLARE @allowAccess bit, @groupPrintID int
	SELECT @allowAccess = 0
    set @groupPrintID = null
    
    if @memberID > 0
        select @groupPrintID = groupPrintID 
	   from ams_members m
	   where memberID = @memberID

    if @groupPrintID is null
	   select @groupPrintID = publicGroupPrintID
	   from organizations o
	   inner join sites s
		  on s.orgID = o.orgID
		  and s.siteID = @siteID

	SELECT @allowAccess = [dbo].[fn_cache_perms_checkResourceRightsForGroupPrint] (
	   @resourceid,
	   @functionID,
	   @groupPrintID,
	   @siteID)

	RETURN @allowAccess
END
GO
ALTER FUNCTION [dbo].[fn_cache_perms_getResourceRightsXMLForGroupPrint] (@siteResourceID int, @groupPrintID int, @siteID int)
RETURNS xml
AS
BEGIN

	DECLARE @rightsXML xml

	select @rightsXML = isnull((
			SELECT [right].functionName, allowed = case when gprp.groupRightPrintID is not null then 1 else 0 end
			FROM cms_siteResources sr
			inner join cms_siteResourceTypeFunctions srtf 
				on sr.siteResourceID = @siteResourceID 
				and sr.resourceTypeID = srtf.resourceTypeID
			inner join dbo.cms_siteResourceFunctions as [right]
				on [right].functionID = srtf.functionID
			left outer join cache_perms_siteResourceFunctionRightPrints srfrp
				inner join cache_perms_groupPrintsRightPrints gprp
					on gprp.rightPrintID = srfrp.rightPrintID
					and gprp.groupPrintID = @groupPrintID
			on srfrp.siteResourceID = sr.siteResourceID
			and srfrp.functionID = [right].functionID
			for XML AUTO, ROOT('rights'), TYPE

		),'<rights/>')

	-- Return the result of the function
	RETURN @rightsXML

END
GO
ALTER PROCEDURE [dbo].[cms_getPageStructureByID] 
@siteID int,
@pageID int,
@languageID int,
@ovrMode varchar(30),
@processMobileOverrides bit,
@templateTypeName varchar(100)

AS

DECLARE @templateID int, @sectionID int, @pgResourceTypeID int, @modeID int, @ovrModeID int, @defaultTemplateID int, @defaultModeID int, @rootSectionID int, @pageIncludePlacements int, @siteResourceStatusID int
DECLARE @viewFunctionID int, @editFunctionID int
DECLARE @layoutInfo TABLE (templateID int, templateTypeName varchar(100), modeID int, modeName varchar(100), templateFilename varchar(100), sitecode varchar(10), orgcode varchar(10))
DECLARE @pageSectionPath TABLE (templateID int, modeID int, sectionID int, parentSectionID int, dataLevel int PRIMARY KEY,includePlacements bit)
DECLARE @zoneAssignments TABLE (autoid int IDENTITY(1,1), zoneID int, assignmentLevel varchar(10), sectionID int, siteResourceID int PRIMARY KEY, sortOrder int, dataLevel int, isSSL bit, appInstanceID int, appInstanceName varchar(100), appTypeName varchar(100), appWidgetInstanceID int, appInstanceResourceID int, appWidgetTypeName varchar(100), appWidgetInstanceName varchar(100),contentID int,enableSocialMediaSharing bit, resourceTypeID int, resourceType varchar(100), resourceTypeClassName varchar(100), objectType char(1), viewRightPrintID int, editContentRightPrintID int)

set @defaultTemplateID = 1
set @defaultModeID = 1

select @ovrModeID = modeID from cms_pageModes where modeName = @ovrMode
select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

select @viewFunctionID = functionID from cms_siteResourceFunctions where functionName = 'view' 
select @editFunctionID = functionID from cms_siteResourceFunctions where functionName = 'editContent' 

select 
	@pgResourceTypeID = sr.resourceTypeID, 
	@sectionID = p.sectionID, 
	@modeID = pm.modeID, 
	@templateID = isnull(mobilept.templateID,pt.templateid),
	@pageIncludePlacements = p.inheritPlacements
from cms_pages p (nolock)
inner join sites s
	on s.siteID = p.siteID
inner join cms_siteResources sr (nolock)
	on p.siteResourceID = sr.siteResourceID
	and pageID = @pageID
     and sr.siteResourceStatusID = @siteResourceStatusID
inner join dbo.cache_cms_derivedPageSectionSettings dpss (nolock)
	on dpss.sectionID = p.sectionID
inner join dbo.cms_pageTemplates pt (nolock)
	on pt.templateid = coalesce(p.ovTemplateID,dpss.ovTemplateID,@defaultTemplateID)
inner join dbo.cms_pageModes pm (nolock)
	on pm.modeID = coalesce(@ovrModeID,p.ovModeID,dpss.ovModeID,@defaultModeID)
left outer join dbo.cms_pageTemplates mobilept (nolock)
	on mobilept.templateid = coalesce(p.ovTemplateIDMobile,dpss.ovTemplateIDMobile)
	and mobilept.templateTypeID = pt.templateTypeID
	and @processMobileOverrides = 1


select @rootSectionID = sectionID from cms_pageSections where siteID = @siteID and parentSectionID is null

if (nullif(@ovrMode,'') is not null)
	select @ovrModeID = modeID from cms_pageModes where modeName = @ovrMode

-- find all zone assignments for page, not considering pageMode
insert into @zoneAssignments (zoneid, siteResourceID, assignmentLevel, sectionID, sortOrder, dataLevel,resourceTypeID,	resourceType, resourceTypeClassName,isSSL,contentID,enableSocialMediaSharing,objecttype)
select pzr.zoneid, pzr.siteResourceID, 'page' as assignmentLevel, 0 as sectionID, pzr.sortOrder, 0 as dataLevel, srt.resourceTypeID,srt.resourceType,srtc.resourceTypeClassName, isnull(c.isSSL,1),c.contentID, c.enableSocialMediaSharing,
objecttype = case
	when c.siteresourceID is not null then 'C'
	when ai.siteresourceID is not null then 'A'
	when awi.siteresourceID is not null then 'W'
end

from dbo.cms_pageZonesResources as pzr
inner join dbo.cms_siteResources sr
	on pzr.siteResourceID = sr.siteResourceID
	and pzr.pageID = @pageID
     and sr.siteResourceStatusID = @siteResourceStatusID
inner join dbo.cms_siteResourceTypes srt ON sr.resourceTypeID = srt.resourceTypeID
inner join dbo.cms_siteResourceTypeClasses srtc ON srt.resourceTypeClassID = srtc.resourceTypeClassID
left outer join dbo.cms_content c on sr.siteResourceID = c.siteResourceID 
left outer join dbo.cms_applicationInstances ai on sr.siteResourceID = ai.siteResourceID
left outer join dbo.cms_applicationWidgetInstances awi on sr.siteResourceID = awi.siteResourceID


if @pageIncludePlacements = 1
BEGIN
	insert into @zoneAssignments (zoneid, siteResourceID, assignmentLevel, sectionID, sortOrder, dataLevel,resourceTypeID,	resourceType, resourceTypeClassName,isSSL,contentID,enableSocialMediaSharing,objecttype)
	select pszr.zoneid, pszr.siteResourceID, 
		assignmentLevel = case when rps.sectionID = @rootSectionID then 'site' else 'section' end,
		rps.sectionID, pszr.sortOrder, (rps.depth * -1) as dataLevel, srt.resourceTypeID,	srt.resourceType, srtc.resourceTypeClassName, isnull(c.isSSL,1),c.contentID, c.enableSocialMediaSharing,
		objecttype = case
			when c.siteresourceID is not null then 'C'
			when ai.siteresourceID is not null then 'A'
			when awi.siteresourceID is not null then 'W'
		end
			
	from dbo.cms_pageSectionsZonesResources pszr
	inner join dbo.cache_cms_recursivePageSections rps
		on rps.startSectionID = @sectionID
		and rps.sectionid = pszr.sectionid
		and rps.includePlacements = 1
	inner join dbo.cms_siteResources sr
		on pszr.siteResourceID = sr.siteResourceID
		  and sr.siteResourceStatusID = @siteResourceStatusID
	inner join dbo.cms_siteResourceTypes srt ON sr.resourceTypeID = srt.resourceTypeID
	inner join dbo.cms_siteResourceTypeClasses srtc ON srt.resourceTypeClassID = srtc.resourceTypeClassID
	left outer join dbo.cms_content c on sr.siteResourceID = c.siteResourceID 
	left outer join dbo.cms_applicationInstances ai on sr.siteResourceID = ai.siteResourceID
	left outer join dbo.cms_applicationWidgetInstances awi on sr.siteResourceID = awi.siteResourceID
END

-- update view and editContentPerms
update za set
    viewRightPrintID = srfrp_view.rightPrintID, 
    editContentRightPrintID = srfrp_editContent.rightPrintID
from @zoneAssignments za
left outer join cache_perms_siteResourceFunctionRightPrints srfrp_view
    on srfrp_view.siteResourceID = za.siteResourceID and srfrp_view.functionID= @viewFunctionID
left outer join cache_perms_siteResourceFunctionRightPrints srfrp_editContent
    on srfrp_editContent.siteResourceID = za.siteResourceID and srfrp_editContent.functionID= @editFunctionID


-- update Application related columns
if exists (select top 1 siteresourceID from @zoneAssignments where objecttype = 'A')
	update za
	set
		appInstanceID = ai.applicationInstanceID,
		appInstanceName = ai.applicationInstanceName, 
		appTypeName = at.applicationTypeName, 
		appInstanceResourceID = ai.siteResourceID,
		isSSL = 1
	from @zoneAssignments za
	inner join dbo.cms_applicationInstances ai
		on za.siteResourceID = ai.siteResourceID
		and za.objecttype = 'A'
	inner join dbo.cms_applicationTypes at ON ai.applicationTypeID = at.applicationTypeID

--update Application Widget related columns
if exists (select top 1 siteresourceID from @zoneAssignments where objecttype = 'W')
	update za
	set
		appInstanceID = ai.applicationInstanceID,
		appInstanceName = ai.applicationInstanceName, 
		appTypeName = at.applicationTypeName, 
		appInstanceResourceID = ai.siteResourceID, 
		appWidgetInstanceID = awi.applicationWidgetInstanceID , 
		appWidgetTypeName = awt.applicationWidgetTypeName, 
		appWidgetInstanceName = awi.applicationWidgetInstanceName,
		isSSL = 1
	from @zoneAssignments za
	inner join dbo.cms_applicationWidgetInstances awi
		on za.siteResourceID = awi.siteResourceID
		and za.objecttype = 'W'
	inner join dbo.cms_applicationInstances ai ON awi.applicationInstanceID = ai.applicationInstanceID
	inner join dbo.cms_applicationWidgetTypes awt ON awi.applicationWidgetTypeID = awt.applicationWidgetTypeID
	inner join dbo.cms_applicationTypes at ON ai.applicationTypeID = at.applicationTypeID



-- is page offered in requested page language? if not, use site default language
IF NOT EXISTS (select pageLanguageID from dbo.cms_pageLanguages where pageID = @pageID and languageID = @languageID)
	SELECT @languageID = defaultLanguageID from sites where siteID = @siteID
select cast(isNull((
select page.pageID, page.pageName, page.allowReturnAfterLogin, page.siteResourceID as pageSiteResourceID,
	isnull(sites.siteID,0) as siteID,
	isnull(sites.siteCode,0) as siteCode,
	isnull(organizations.orgID,0) as orgID,
	isnull(organizations.orgCode,0) as orgCode,
	ISNULL(ps.sectionID,'') as sectionID, 
	ISNULL(ps.sectionName,'') as sectionName, 
	ISNULL(ps.sectionCode,'') as sectionCode,
	@languageID as pageLanguageID,
	ISNULL(pl.pageTitle,'') as pageTitle, 
	ISNULL(pl.pageDesc,'') as pageDesc, 
	ISNULL(pl.keywords,'') as keywords, 
	ISNULL(pt.templateFileName,'DefaultTemplate') as layoutFileName,
	ISNULL(pt.templateID,'') as templateID,
	ISNULL(pt.siteResourceID,'') as templateSiteResourceID,
	ISNULL(ptt.templateTypeName,'') as templateTypeName,
	ISNULL(pm.modeName,'Normal') as layoutMode, 
	ISNULL(templateSite.sitecode,'') as layoutSiteCode,
	ISNULL(templateOrg.orgcode,'') as layoutOrgCode, 
	pageZone.zoneName, 
	siteResource.siteResourceID,
	ISNULL(sites.siteID,0) as siteID,
	ISNULL(sites.siteCode,'') as siteCode,
	ISNULL(organizations.orgID,0) as orgID,
	ISNULL(organizations.orgCode,'') as orgCode,
	ISNULL(siteResource.resourceType,'') as resourceType,
	ISNULL(siteResource.resourceTypeClassName,'') as resourceClass,
	ISNULL(siteResource.assignmentLevel,'') as assignmentLevel,
	ISNULL(siteResource.sectionID,'') as assignmentSectionID,
	ISNULL(siteResource.isSSL,'') as isSSL, 
	ISNULL(siteResource.appInstanceID,'') as appInstanceID, 
	ISNULL(siteResource.appInstanceName,'') as appInstanceName, 
	ISNULL(siteResource.appTypeName,'') as appTypeName, 
	ISNULL(siteResource.appWidgetInstanceID,'') as appWidgetInstanceID, 
	ISNULL(siteResource.appInstanceResourceID,'') as appInstanceResourceID, 
	ISNULL(siteResource.appWidgetTypeName,'') as appWidgetTypeName, 
	ISNULL(siteResource.appWidgetInstanceName,'') as appWidgetInstanceName,
	ISNULL(siteResource.contentID,'') as contentID,
	ISNULL(siteResource.enableSocialMediaSharing,'') as enableSocialMediaSharing,
    ISNULL(siteResource.viewRightPrintID,'') as viewRightPrintID,
    ISNULL(siteResource.editContentRightPrintID,'') as editContentRightPrintID
from dbo.cms_pages as page (nolock)
inner join dbo.sites (nolock)
	on sites.siteID = page.siteID
	and page.pageID = @pageID
inner join dbo.organizations (nolock)
	on sites.orgID = organizations.orgID
inner join dbo.cms_pageSections (nolock) as ps
	on ps.sectionID = page.sectionID
inner join dbo.cms_pageTemplates pt (nolock)
	on pt.templateid = @templateID
inner join dbo.cms_pageTemplateTypes ptt (nolock)
	on pt.templateTypeID = ptt.templateTypeID
	and ptt.templateTypeName = @templateTypeName
inner join dbo.cms_pageModes pm (nolock)
	on pm.modeID = @modeID
left outer join dbo.sites templateSite (nolock)
	inner join dbo.organizations templateOrg (nolock)
		on templateSite.orgid = templateOrg.orgid
on templateSite.siteid = pt.siteid
inner join dbo.cms_pageLanguages as pl (nolock) 
	on pl.pageID = page.pageid and pl.languageid = @languageID
inner join dbo.cms_pageTemplatesModesZones as ptmz (nolock)
	on pm.modeid = ptmz.modeid and ptmz.templateID = pt.templateID
inner join dbo.cms_pageZones as pageZone (nolock)
	on pageZone.zoneid = ptmz.zoneid
left outer join @zoneAssignments as siteResource
	on ptmz.zoneID = siteResource.zoneid
order by siteResource.zoneid, siteResource.dataLevel desc, siteResource.sortOrder
for xml auto, root('pageStructure')
),'<pageStructure />') as xml) as pageStructureXML
OPTION(RECOMPILE)


RETURN 0
GO
ALTER PROCEDURE [dbo].[cms_getCMSResourceByID]
@siteID int,
@siteResourceID int,
@languageID int,
@memberID int,
@ovrMode varchar(30)

AS


DECLARE @activeMemberID int
select @activeMemberID = dbo.fn_getActiveMemberID(@memberID)

DECLARE @viewFunctionID int, @editFunctionID int
DECLARE @templateID int, @modeID int, @ovrModeID int, @siteCode varchar(10), @mainZoneID int, @siteResourceStatusID int
DECLARE @layoutInfo TABLE (templateID int, templateTypeName varchar(100), modeID int, modeName varchar(100), templateFilename varchar(100), sitecode varchar(10), orgcode varchar(10), siteResourceID int)
DECLARE @zoneAssignments TABLE (autoid int IDENTITY(1,1), zoneID int, siteResourceID int, sortOrder int, dataLevel int, viewRightPrintID int, editContentRightPrintID int)

select @siteCode = dbo.fn_getSiteCodeFromSiteID(@siteID)

declare @usetemplateID int, @usemodeID int
select TOP 1 @usetemplateID = ovtemplateID from cms_pageSections ps where siteID = @siteID and parentSectionID is null


select @usemodeID = modeID from cms_pageModes where modeName = @ovrMode
select @mainZoneID = zoneid from cms_pageZones where zoneName = 'Main'

select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

select @viewFunctionID = functionID from cms_siteResourceFunctions where functionName = 'view' 
select @editFunctionID = functionID from cms_siteResourceFunctions where functionName = 'editContent' 


-- if usetemplateid is null then use template 1 - platform default
IF @usetemplateID is null 
	select @usetemplateID = 1

insert into @layoutInfo (templateID, templateTypeName, modeID, modeName, templateFilename, sitecode, orgcode, siteResourceID)
select pSp.templateID, ptt.templateTypeName, pSp.modeID, pm.modeName, pt.templateFilename, s2.sitecode, o.orgcode, pt.siteResourceID
from (select @usetemplateID as templateID, @usemodeID as modeID) as pSp
inner join dbo.cms_pageTemplates as pt on pSp.templateID = pt.templateid
inner join dbo.cms_pageTemplateTypes ptt on pt.templateTypeID = ptt.templateTypeID
inner join dbo.cms_pageModes as pm on pSp.modeID = pm.modeID
left outer join dbo.sites as s2 on s2.siteid = pt.siteid
left outer join dbo.organizations as o on s2.orgid = o.orgid

-- find all zone assignments for page, not considering pageMode
insert into @zoneAssignments (zoneid, siteResourceID, sortOrder, dataLevel)
select @mainZoneID, @siteResourceID, 1 as sortOrder, 0 as dataLevel


-- update view and editContentPerms
update za set
    viewRightPrintID = srfrp_view.rightPrintID, 
    editContentRightPrintID = srfrp_editContent.rightPrintID
from @zoneAssignments za
left outer join cache_perms_siteResourceFunctionRightPrints srfrp_view
    on srfrp_view.siteResourceID = za.siteResourceID and srfrp_view.functionID= @viewFunctionID
left outer join cache_perms_siteResourceFunctionRightPrints srfrp_editContent
    on srfrp_editContent.siteResourceID = za.siteResourceID and srfrp_editContent.functionID= @editFunctionID

-- use site default language
SELECT @languageID = defaultLanguageID from sites where siteCode = @sitecode

-- Now that we know template and mode, figure out page pod assignments 
select cast(isNull((
select page.pageID, page.pageName, page.allowReturnAfterLogin, page.siteResourceID as pageSiteResourceID, 
	isnull(siteResource.siteID,0) as siteID,
	isnull(siteResource.siteCode,0) as siteCode,
	isnull(siteResource.orgID,0) as orgID,
	isnull(siteResource.orgCode,0) as orgCode,
	page.sectionname, page.sectionCode, @languageID as pageLanguageID,
	coalesce(siteResourceDetails.applicationWidgetInstanceName,siteResourceDetails.applicationInstanceName,'') as pageTitle, 
	'' as pageDesc, 
	'' as keywords, 
	ISNULL(layout.templateFileName,'DefaultTemplate') as layoutFileName,
	ISNULL(layout.templateID,'') as templateID,
	ISNULL(layout.siteResourceID,'') as templateSiteResourceID,
	ISNULL(layout.templateTypeName,'') as templateTypeName,
	ISNULL(layout.modeName,'Normal') as layoutMode, 
	ISNULL(layout.sitecode,'') as layoutSiteCode, ISNULL(layout.orgcode,'') as layoutOrgCode, 
	pageZone.zoneName, 
	siteResource.siteResourceID,
	dbo.fn_checkResourceRights(siteResource.siteResourceID,dbo.fn_getResourceFunctionID('view',siteResource.resourceTypeID),@activeMemberID,@siteID) as allowed,
	ISNULL(siteResource.resourceType,'') as resourceType,
	ISNULL(siteResource.resourceTypeClassName,'') as resourceClass,
	ISNULL(siteResourceDetails.applicationInstanceID,'') as appInstanceID, 
	ISNULL(siteResourceDetails.applicationInstanceName,'') as appInstanceName, 
	ISNULL(siteResourceDetails.applicationTypeName,'') as appTypeName, 
	ISNULL(siteResourceDetails.applicationWidgetInstanceID,'') as appWidgetInstanceID, 
	ISNULL(siteResourceDetails.applicationInstanceResourceID,'') as appInstanceResourceID, 
	ISNULL(siteResourceDetails.applicationWidgetTypeName,'') as appWidgetTypeName, 
	ISNULL(siteResourceDetails.applicationWidgetInstanceName,'') as appWidgetInstanceName,
	ISNULL(siteResourceDetails.contentID,'') as contentID,
	ISNULL(siteResourceDetails.enableSocialMediaSharing,'') as enableSocialMediaSharing,
   ISNULL(zr.viewRightPrintID,'') as viewRightPrintID,
   ISNULL(zr.editContentRightPrintID,'') as editContentRightPrintID
from (select '' as pageID, '' as pageName, 0 as siteResourceID, 0 as allowReturnAfterLogin, '' as sectionName, '' as sectionCode, '' as sectionID) as page
inner join @layoutInfo as layout on 1=1
inner join cms_pageTemplatesModesZones as ptmz on layout.modeid = ptmz.modeid and ptmz.templateID = layout.templateID
inner join cms_pageZones as pageZone on pageZone.zoneid = ptmz.zoneid
left outer join @zoneAssignments as zr
	inner join (
		select sr.siteResourceID, srt.resourceTypeID, srt.resourceType, srtc.resourceTypeClassName, s.siteID, o.orgID, s.siteCode, o.orgCode
		from dbo.cms_siteResources sr
		inner join dbo.cms_siteResourceTypes as srt 
			on sr.resourceTypeID = srt.resourceTypeID
			and sr.siteResourceStatusID = @siteResourceStatusID
		inner join dbo.cms_siteResourceTypeClasses as srtc on srt.resourceTypeClassID = srtc.resourceTypeClassID
		inner join dbo.sites s on sr.siteID = s.siteID
		inner join dbo.organizations o on s.orgid = o.orgid
		) as siteResource on siteResource.siteResourceID = zr.siteResourceID
	inner join dbo.fn_getCMSResourcesForSite(@siteid) as siteResourceDetails 
		on siteResource.siteResourceID = siteResourceDetails.siteResourceID and siteResourceDetails.siteResourceID = @siteResourceID
	on ptmz.zoneID = zr.zoneid
order by zr.zoneid, zr.dataLevel desc, zr.sortOrder
for xml auto, root('pageStructure')
),'<pageStructure />') as xml) as pageStructureXML

RETURN 0
GO
CREATE FUNCTION [dbo].[fn_cache_perms_checkGroupPrintRightPrint] (
	@groupPrintID int,
	@rightPrintID int
)
RETURNS bit
AS
BEGIN
	DECLARE @allowAccess bit
	SELECT @allowAccess = 0

	SELECT @allowAccess =
		CASE 
			WHEN EXISTS (
				select gprp.groupRightPrintID
				from dbo.cache_perms_groupPrintsRightPrints gprp
				where gprp.groupPrintID = @groupPrintID
				    and gprp.rightPrintID = @rightPrintID
				)
				THEN 1
		ELSE 0
		END
	RETURN @allowAccess
END
GO