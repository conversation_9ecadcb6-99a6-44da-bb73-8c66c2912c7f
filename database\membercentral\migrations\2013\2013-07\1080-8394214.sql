declare 
	@siteID int, 
	@missingDocsID int,
	@documentIDToUpdate int, 
	@documentSiteResourceID int, 
	@newSiteResourceIDToInheritFrom int, 
	@resourceRightsID int,
	@childSectionID int,
	@sectionIDToUpdate int,
	@newParentSectionID int,
	@siteResourceStatusID int,
	@sectionName varchar(255),
	@sectionCode varchar(255),
	@inheritPlacements bit,
	@trashID int,
	@autoID int, 
	@sectionID int

declare 
	@resourceID int, 
	@include bit, 
	@functionID int, 
	@roleID int, 
	@groupID int, 
	@inheritedRightsResourceID int, 
	@inheritedRightsFunctionID int

set @siteID = dbo.fn_getSiteIDFromSiteCode('NC')

/*
select *
from cms_applicationInstances ai
inner join fs_fileshare fs
	on ai.applicationInstanceID = fs.applicationInstanceID
	and ai.siteID = @siteID
	and fs.fileShareID = 612
*/

declare @fsMap TABLE (id int IDENTITY(1,1), fileShareID int, sectionID int)
insert into @fsMap (fileShareID, sectionID) values (614,4266)
insert into @fsMap (fileShareID, sectionID) values (615,4267)
insert into @fsMap (fileShareID, sectionID) values (616,4198)
insert into @fsMap (fileShareID, sectionID) values (617,4268)
insert into @fsMap (fileShareID, sectionID) values (611,4271)
insert into @fsMap (fileShareID, sectionID) values (623,4272)
insert into @fsMap (fileShareID, sectionID) values (619,4273)
insert into @fsMap (fileShareID, sectionID) values (621,4274)
insert into @fsMap (fileShareID, sectionID) values (609,4276)
insert into @fsMap (fileShareID, sectionID) values (622,4278)
insert into @fsMap (fileShareID, sectionID) values (608,4283)
insert into @fsMap (fileShareID, sectionID) values (610,4285)
insert into @fsMap (fileShareID, sectionID) values (625,4287)
insert into @fsMap (fileShareID, sectionID) values (626,4288)
insert into @fsMap (fileShareID, sectionID) values (627,4289)
insert into @fsMap (fileShareID, sectionID) values (629,4290)
insert into @fsMap (fileShareID, sectionID) values (633,4292)

--select * from @fsMap 

declare @missingDocs TABLE (missingDocsID int IDENTITY(1,1), documentIDToUpdate int, documentSiteResourceID int, newSiteResourceIDToInheritFrom int, resourceRightsID int)

-- BEGIN TRAN

begin try

	--look for all misassigned documents (need permissions updated to inherit from correct fileshare instance)
	insert into @missingDocs
	select distinct
		d.documentID as documentIDToUpdate, 
		d.siteResourceID as documentSiteResourceID, 
		ai.siteResourceID as newSiteResourceIDToInheritFrom, 
		srr.resourceRightsID as resourceRightsID
	from 
		@fsmap fsmap
	inner join cms_pageSections ps
		on ps.sectionID = fsmap.sectionID
	inner join dbo.cache_cms_recursivePageSections rps
		on ps.sectionID = rps.sectionID
	inner join cms_documents d
		on d.sectionID = rps.sectionID
	inner join cms_siteResourceRights srr
		on srr.resourceID = d.siteResourceID
	inner join fs_fileshare fs
		on fs.fileShareID = fsmap.fileShareID
	inner join cms_applicationInstances ai
		on ai.applicationInstanceID = fs.applicationInstanceID


	select @missingDocsID = min(missingDocsID) from @missingDocs

	while @missingDocsID is not null
	begin
		
		print '@missingDocsID = ' + cast(@missingDocsID as varchar)

		select 
			@documentIDToUpdate = documentIDToUpdate, 
			@documentSiteResourceID = documentSiteResourceID, 
			@newSiteResourceIDToInheritFrom = newSiteResourceIDToInheritFrom, 
			@resourceRightsID = resourceRightsID 
		from 
			@missingDocs 
		where 
			missingDocsID = @missingDocsID

		print '@documentIDToUpdate = ' + isNull(cast(@documentIDToUpdate as varchar),'NULL')
		print '@documentSiteResourceID = ' + isNull(cast(@documentSiteResourceID as varchar),'NULL')
		print '@newSiteResourceIDToInheritFrom = ' + isNull(cast(@newSiteResourceIDToInheritFrom as varchar),'NULL')
		print '@resourceRightsID = ' + isNull(cast(@resourceRightsID as varchar),'NULL')

		select 
			@resourceID=srr.resourceID, 
			@include=include, 
			@functionID=functionID, 
			@roleID=roleID, 
			@groupID=groupID, 
			@inheritedRightsFunctionID=inheritedRightsFunctionID
		from 
			cms_siteResourceRights srr
		inner join cms_siteResources sr
			on sr.siteResourceID =srr.resourceID
			and sr.siteID = @siteID
			and srr.resourceRightsID = @resourceRightsID

		if @resourceID is not null
		BEGIN

			print '@resourceID = ' + cast(@resourceID as varchar)

			print 'exec dbo.cms_deleteSiteResourceRight @siteID= ' + isNull(cast(@siteID as varchar),'NULL') 
				+ ', @siteResourceID = ' + isNull(cast(@documentSiteResourceID as varchar),'NULL') 
				+ ', @siteResourceRightID = ' + isNull(cast(@resourceRightsID as varchar),'NULL')
			
			exec dbo.cms_deleteSiteResourceRight
				@siteID=@siteID, 
				@siteResourceID=@documentSiteResourceID, 
				@siteResourceRightID=@resourceRightsID
			IF @@ERROR <> 0 GOTO on_error

			print 'exec dbo.cms_createSiteResourceRight @siteID= ' + isNull(cast(@siteID as varchar),'NULL') 
				+ ', @siteResourceID = ' + isNull(cast(@documentSiteResourceID as varchar),'NULL') 
				+ ', @include = ' + isNull(cast(@include as varchar),'NULL')
				+ ', @functionID = ' + isNull(cast(@functionID as varchar),'NULL')
				+ ', @roleID = ' + isNull(cast(@roleID as varchar),'NULL')
				+ ', @groupID = ' + isNull(cast(@groupID as varchar),'NULL')
				+ ', @memberID = NULL' 
				+ ', @inheritedRightsResourceID = ' + isNull(cast(@newSiteResourceIDToInheritFrom as varchar),'NULL')
				+ ', @inheritedRightsFunctionID = ' + isNull(cast(@inheritedRightsFunctionID as varchar),'NULL')
				+ ', @resourceRightID = NULL' 

			exec dbo.cms_createSiteResourceRight
				@siteID=@siteID, 
				@siteResourceID=@documentSiteResourceID, 
				@include=@include, 
				@functionID=@functionID, 
				@roleID=@roleID, 
				@groupID=@groupID, 
				@memberID=null, 
				@inheritedRightsResourceID=@newSiteResourceIDToInheritFrom,  
				@inheritedRightsFunctionID=@inheritedRightsFunctionID, 
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 GOTO on_error

		END

		print '-------------------------------------------------------------------------------------------'

		select @missingDocsID = min(missingDocsID) from @missingDocs where missingDocsID > @missingDocsID
		
	end

	declare @childSectionsTbl table (
		childSectionID int identity(1,1), 
		sectionIDToUpdate int, 
		newParentSectionID int, 
		siteResourceStatusID int, 
		sectionName varchar(255), 
		sectionCode varchar(255), 
		inheritPlacements bit
	)

	--look for childSections (need to set parentSectionID = rootSection of correct fileshare instance
	insert into @childSectionsTbl
	select 
		childSections.sectionID as sectionIDToUpdate, 
		fs.rootSectionID as newParentSectionID,
		sr.siteResourceStatusID,
		childSections.sectionName,
		childSections.sectionCode,
		childSections.inheritPlacements
	from 
		@fsmap fsmap
	inner join cms_pageSections ps
		on ps.sectionID = fsmap.sectionID
	inner join cms_pageSections childSections
		on childSections.parentSectionID = ps.sectionID
	inner join fs_fileshare fs
		on fs.fileShareID = fsmap.fileShareID
	inner join cms_siteResources sr on
		sr.siteResourceID = childSections.siteResourceID

	select @childSectionID =  min(childSectionID) from @childSectionsTbl

	while @childSectionID is not null
	begin
		
		select 
			@sectionIDToUpdate =  sectionIDToUpdate,
			@newParentSectionID = newParentSectionID ,
			@siteResourceStatusID = siteResourceStatusID,
			@sectionName = sectionName,
			@sectionCode = sectionCode,
			@inheritPlacements = inheritPlacements
		from 
			@childSectionsTbl 
		where 
			childSectionID = @childSectionID

		print 'exec dbo.cms_updatePageSection @sectionID= ' + isNull(cast(@sectionIDToUpdate as varchar),'NULL') 
			+ ', @siteid = ' + isNull(cast(@siteid as varchar),'NULL') 
			+ ', @siteResourceStatusID = ' + isNull(cast(@siteResourceStatusID as varchar),'NULL')
			+ ', @parentSectionID = ' + isNull(cast(@newParentSectionID as varchar),'NULL')
			+ ', @sectionName = ' + isNull(@sectionName,'NULL')
			+ ', @sectionCode = ' + isNull(@sectionCode,'NULL')
			+ ', @ovTemplateID = NULL' 
			+ ', @ovTemplateIDMobile = NULL'
			+ ', @ovModeID = NULL'
			+ ', @inheritPlacements = ' + isNull(cast(@inheritPlacements as varchar),'NULL')

		exec dbo.cms_updatePageSection
			@sectionID=@sectionIDToUpdate, 
			@siteid=@siteID, 
			@siteResourceStatusID=@siteResourceStatusID, 
			@parentSectionID=@newParentSectionID, 
			@sectionName=@sectionName, 
			@sectionCode=@sectionCode, 
			@ovTemplateID=NULL, 
			@ovTemplateIDMobile=NULL, 
			@ovModeID=NULL, 
			@inheritPlacements=@inheritPlacements
		
		IF @@ERROR <> 0 GOTO on_error

		print '***************************************************************************************************'

		select @childSectionID =  min(childSectionID) from @childSectionsTbl where childSectionID > @childSectionID
	end

	--look for documents directly in badsection (need to set parentSectionID = rootSection of correct fileshare instance

	update
		d
	set
		d.sectionID = fs.rootSectionID 
	from 
		@fsmap fsmap
		inner join cms_documents d
			on d.sectionID = fsmap.sectionID
		inner join fs_fileshare fs
			on fs.fileShareID = fsmap.fileShareID
	
	IF @@ERROR <> 0 GOTO on_error
	
	declare @deleteSectionsTbl TABLE (autoID int IDENTITY(1,1), sectionID int, docCount int)

	insert into @deleteSectionsTbl (sectionID, docCount)
	select 
		ps.sectionID, count(d.documentid)
	from

		@fsmap fsmap
		inner join cms_pageSections ps on
			ps.sectionID = fsmap.sectionID
		left outer join cms_documents d on 
			d.sectionID = ps.sectionID
	group by 
		ps.sectionID
	having 
		count(d.documentid) = 0
		
	select @autoID = min(autoID) from @deleteSectionsTbl
		
	while @autoID is not null
	begin
		
		select @sectionID = sectionID from @deleteSectionsTbl where autoID = @autoID
		
		print @autoID
		
		print 'exec dbo.cms_deletePageSection @siteID= ' + isNull(cast(@siteID as varchar),'NULL') 
			+ ', @sectionID = ' + isNull(cast(@sectionID as varchar),'NULL') 	
		
		exec cms_deletePageSection @siteID = @siteID , @sectionID = @sectionID
		
		IF @@ERROR <> 0 GOTO on_error

		select @autoID = min(autoID) from @deleteSectionsTbl where 	autoID > @autoID
	end	

	-- IF @@TRANCOUNT > 0 COMMIT TRAN

end try

begin catch
    --returns the complete original error message as a result set
    SELECT 
        ERROR_NUMBER() AS ErrorNumber
        ,ERROR_SEVERITY() AS ErrorSeverity
        ,ERROR_STATE() AS ErrorState
        ,ERROR_PROCEDURE() AS ErrorProcedure
        ,ERROR_LINE() AS ErrorLine
        ,ERROR_MESSAGE() AS ErrorMessage

    --will return the complete original error message as an error message
    DECLARE @ErrorMessage nvarchar(400), @ErrorNumber int, @ErrorSeverity int, @ErrorState int, @ErrorLine int
    SELECT @ErrorMessage = N'Error %d, Line %d, Message: '+ERROR_MESSAGE(),@ErrorNumber = ERROR_NUMBER(),@ErrorSeverity = ERROR_SEVERITY(),@ErrorState = ERROR_STATE(),@ErrorLine = ERROR_LINE()
    RAISERROR (@ErrorMessage, @ErrorSeverity, @ErrorState, @ErrorNumber,@ErrorLine)

end catch

goto on_done

-- error exit
on_error:
	/*
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	*/
on_done:

GO