USE Membercentral
GO
ALTER TABLE dbo.ams_groups ADD alertIfPopulated bit NOT NULL CONSTRAINT DF_ams_groups_alertIfPopulated DEFAULT 0
GO

ALTER FUNCTION [dbo].[fn_getGroupsStructureXML] (@orgID int)
RETURNS xml
AS
BEGIN
	DECLARE @xmlStructure xml
	
	SELECT @xmlStructure = (
		select (
			select [group].groupid, isnull([group].groupcode,'') as groupcode, [group].groupname, 
				isnull([group].groupdesc,'') as groupdesc, 
				[group].allowmanualassignment, [group].isprotected, [group].alertifpopulated, 
				isnull([group].parentgroupid,0) as parentgroupid, 
				[group].hideongrouplists, isnull([group].imageext,'') as imageext, [group].uid, 
				isnull(rg.thepathexpanded,'') as thepathexpanded
			from dbo.fn_getRecursiveGroups(@orgID,DEFAULT,DEFAULT,DEFAULT) as rg
			inner join dbo.ams_groups as [group] on [group].groupid = rg.groupid
			where [group].issystemgroup = 0
			order by rg.row
			FOR XML AUTO, root('groups'), TYPE
			)
		FOR XML RAW('GroupsStructure'), TYPE
		)

	RETURN @xmlStructure
END
GO

