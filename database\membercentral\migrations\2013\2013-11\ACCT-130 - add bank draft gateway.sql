use membercentral
GO
insert into dbo.mp_gateways (gatewayType, gatewayTypeDesc, gatewayClass, isActive)
values ('BankDraft', 'Bank Draft Gateway', 'bankdraft', 1)
GO

update dbo.mp_fieldTypes
set fieldType = 'Bank Routing Number', fieldTypeClass = 'bankdraft'
where fieldTypeID = 6

update dbo.mp_fieldTypes
set fieldType = 'Bank Account Number', fieldTypeClass = 'bankdraft'
where fieldTypeID = 7

update dbo.mp_fieldTypes
set fieldType = 'Bank Account Type', fieldTypeClass = 'bankdraft'
where fieldTypeID = 12
GO

update dbo.mp_fields
set fieldName = 'Account Type'
where fieldName = 'Bank Account Type'
GO

CREATE TABLE [dbo].[mp_secCodes](
	[secCodeID] [int] IDENTITY(1,1) NOT NULL,
	[secCode] [varchar](3) NOT NULL,
	[notes] [varchar](MAX) NOT NULL,
 CONSTRAINT [PK_mp_secCodes] PRIMARY KEY CLUSTERED 
(
	[secCodeID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

insert into mp_secCodes (secCode, notes) values ('PPD', 'You must have an agreement with your bank to accept PPD transactions. PPD are limited to personal bank accounts and allows the consumer 60 days to deny a charge. Under NACHA guidelines, you are required to have signed authorization from the consumer to draft their personal bank account.')
insert into mp_secCodes (secCode, notes) values ('CCD', 'You must have an agreement with your bank to accept CCD transactions. CCD are limited to corporate bank accounts and allows the business 24 hours to deny a charge. If you do not accept CCD bank drafts, only personal bank accounts can be accepted.')
insert into mp_secCodes (secCode, notes) values ('WEB', 'You must have an agreement with your bank to accept WEB transactions. If not offered, members will be unable to pay online using personal checking accounts.')
GO

CREATE TABLE [dbo].[mp_profileSecCodes](
	[profileSecCodeID] [int] IDENTITY(1,1) NOT NULL,
	[profileID] [int] NOT NULL,
	[secCodeID] [int] NOT NULL,
 CONSTRAINT [PK_mp_profileSecCodes] PRIMARY KEY CLUSTERED 
(
	[profileSecCodeID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
ALTER TABLE [dbo].[mp_profileSecCodes]  WITH CHECK ADD  CONSTRAINT [FK_mp_profileSecCodes_mp_profiles] FOREIGN KEY([profileID])
REFERENCES [dbo].[mp_profiles] ([profileID])
GO
ALTER TABLE [dbo].[mp_profileSecCodes] CHECK CONSTRAINT [FK_mp_profileSecCodes_mp_profiles]
GO
ALTER TABLE [dbo].[mp_profileSecCodes]  WITH CHECK ADD  CONSTRAINT [FK_mp_profileSecCodes_mp_secCodes] FOREIGN KEY([secCodeID])
REFERENCES [dbo].[mp_secCodes] ([secCodeID])
GO
ALTER TABLE [dbo].[mp_profileSecCodes] CHECK CONSTRAINT [FK_mp_profileSecCodes_mp_secCodes]
GO

CREATE PROCEDURE [dbo].[mp_insertProfileSecCodes]
@profileID int,
@secCodeID int

AS

IF EXISTS (select profileSecCodeID from dbo.mp_profileSecCodes where profileID = @profileID and secCodeID = @secCodeID)
	GOTO on_error

INSERT INTO	dbo.mp_profileSecCodes (profileID, secCodeID)
VALUES (@profileID, @secCodeID)
	IF @@ERROR <> 0 GOTO on_error

RETURN 0

on_error:
	RETURN -1
GO

insert into dbo.mp_gatewayFields (gatewayID, fieldID, isRequired)
values (16, 27, 1)
insert into dbo.mp_gatewayFields (gatewayID, fieldID, isRequired)
values (16, 9, 1)
insert into dbo.mp_gatewayFields (gatewayID, fieldID, isRequired)
values (16, 10, 1)
GO



