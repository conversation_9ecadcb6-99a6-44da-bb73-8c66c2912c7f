use membercentral
GO

DECLARE	@toolTypeID int, @toolResourceTypeID int, @navigationID int, @TopTabNavigationID int, @level2EmailMembersNavigationID int, @viewResourceTypeFunctionID int

select @TopTabNavigationID = navigationID 
from dbo.admin_navigation 
where navName = 'Members' 
and parentNavigation<PERSON> is null

select @level2EmailMembersNavigationID = navigationID 
from dbo.admin_navigation 
where navName = 'Email Members' 
and parentNavigationID = @TopTabNavigationID

EXEC dbo.createAdminNavigation @navName='Email Activity', @navDesc='View Email Activity', @parentNavigationID=@level2EmailMembersNavigationID,
	@navAreaID=3, @cfcMethod='searchEmailActivity', @isHeader=0, @showInNav=1, @navigationID=@navigationID OUTPUT

select @toolResourceTypeID = resourceTypeID, @toolTypeID = toolTypeID from dbo.admin_toolTypes where toolType = 'EmailBlast'
select @viewResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@toolResourceTypeID,4)

EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID=@viewResourceTypeFunctionID, @toolTypeID=@toolTypeID, @navigationID=@navigationID
GO



use platformMail
GO

CREATE PROC dbo.email_reviewMessage
@siteID int,
@messageID int,
@recipientID int

AS

declare @messageToParse varchar(max)

select @messageToParse = replace(m.messageWrapper,'@@rawcontent@@',cv.rawContent)
from dbo.email_messages as m WITH(NOLOCK)
inner join membercentral.dbo.cms_contentVersions as cv WITH(NOLOCK) on cv.contentVersionID = m.contentVersionID
where m.siteID = @siteID
and m.messageID = @messageID

-- all merge codes in message
select distinct [Text] as fieldName
from membercentral.dbo.fn_RegexMatches(@messageToParse,'(?<=\[\[)([^,\]]+)(?=,?([^\]]+)?\]\])')
order by 1

-- recipient
select mrh.recipientID, mrh.toEmail + case when len(mrh.toName) > 0 then ' (' + mrh.toName + ')' else '' end as emailTo, @messageToParse as messageContent
from dbo.email_messageRecipientHistory as mrh WITH(NOLOCK)
inner join dbo.email_messages as m WITH(NOLOCK) on m.messageID = mrh.messageID
where mrh.recipientID = @recipientID
and m.siteID = @siteID
and m.messageID = @messageID

-- recipient merge code data
select mrh.recipientID, f.fieldID, f.fieldName, mf.messageid, mf.memberid, mf.fieldValue
from dbo.email_metadataFields as f WITH(NOLOCK)
inner join dbo.email_messageMetadataFields as mf WITH(NOLOCK) on mf.fieldID = f.fieldID
inner join dbo.email_messageRecipientHistory as mrh WITH(NOLOCK) on mrh.messageID = mf.messageID
inner join dbo.email_messages as m WITH(NOLOCK) on m.messageID = mrh.messageID
where mrh.recipientID = @recipientID
and m.siteID = @siteID
and m.messageID = @messageID
and f.isMergeField = 1

RETURN 0
GO

