USE [customApps]
GO

ALTER PROC [dbo].[ca_importMemberData]
@pathToImport varchar(200), 
@pathToExport varchar(200),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

DECLARE @orgID int
SELECT @orgID = memberCentral.dbo.fn_getOrgIDFromOrgCode('CA')

EXEC memberCentral.dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Starting ca_importMemberData'

-- create temp tables
IF OBJECT_ID('tempdb..##ca_members') IS NOT NULL
	DROP TABLE ##ca_members
CREATE TABLE ##ca_members (
	[rowid] [int] NOT NULL,
	[firstname] [varchar](500) NOT NULL DEFAULT (''),
	[middlename] [varchar](500) NOT NULL DEFAULT (''),
	[lastname] [varchar](500) NOT NULL DEFAULT (''),
	[suffix] [varchar](500) NOT NULL DEFAULT (''),
	[professionalSuffix] [varchar](500) NOT NULL DEFAULT (''),
	[company] [varchar](500) NOT NULL DEFAULT (''),
	[Address_address1] [varchar](500) NOT NULL DEFAULT (''),
	[Address_address2] [varchar](500) NOT NULL DEFAULT (''),
	[Address_city] [varchar](500) NOT NULL DEFAULT (''),
	[Address_stateprov] [varchar](500) NOT NULL DEFAULT (''),
	[Address_postalCode] [varchar](500) NOT NULL DEFAULT (''),
	[Address_county] [varchar](500) NOT NULL DEFAULT (''),
	[Address_country] [varchar](500) NOT NULL DEFAULT (''),
	[Address_Phone] [varchar](500) NOT NULL DEFAULT (''),
	[Address_Fax] [varchar](500) NOT NULL DEFAULT (''),
	[Website] [varchar](500) NOT NULL DEFAULT (''),
	[Twitter] [varchar](500) NOT NULL DEFAULT (''),
	[LinkedIn] [varchar](500) NOT NULL DEFAULT (''),
	[Facebook] [varchar](500) NOT NULL DEFAULT (''),
	[Email] [varchar](500) NOT NULL DEFAULT (''),
	[ALT_EMAIL_1] [varchar](500) NOT NULL DEFAULT (''),
	[Email Opt In] [varchar](500) NOT NULL DEFAULT (''),
	[Fax Opt In] [varchar](500) NOT NULL DEFAULT (''),
	[memberNumber] [varchar](500) NOT NULL DEFAULT (''),
	[mbrtype] [varchar](500) NOT NULL DEFAULT (''),
	[MemberDate] [varchar](500) NOT NULL DEFAULT (''),
	[BOARD] [varchar](500) NOT NULL DEFAULT (''),
	[BOD] [varchar](500) NOT NULL DEFAULT (''),
	[TLA] [varchar](500) NOT NULL DEFAULT (''),
	[Incoming] [varchar](500) NOT NULL DEFAULT (''),
	[Defense] [varchar](500) NOT NULL DEFAULT (''),
	[directory] [varchar](500) NOT NULL DEFAULT (''),
	[Dist] [varchar](500) NOT NULL DEFAULT (''),
	[AdvClub] [varchar](500) NOT NULL DEFAULT (''),
	[BusFriend] [varchar](500) NOT NULL DEFAULT (''),
	[PresClub] [varchar](500) NOT NULL DEFAULT (''),
	[firmID] [varchar](500) NOT NULL DEFAULT (''),
	[PCFirm] [varchar](500) NOT NULL DEFAULT (''),
	[AOP1] [varchar](500) NOT NULL DEFAULT (''),
	[AOP2] [varchar](500) NOT NULL DEFAULT (''),
	[AOP3] [varchar](500) NOT NULL DEFAULT (''),
	[AOP4] [varchar](500) NOT NULL DEFAULT (''),
	[VM_1] [varchar](500) NOT NULL DEFAULT (''),
	[VM_2] [varchar](500) NOT NULL DEFAULT (''),
	[VM_3] [varchar](500) NOT NULL DEFAULT (''),
	[VM_4] [varchar](500) NOT NULL DEFAULT (''),
	[Gender] [varchar](500) NOT NULL DEFAULT (''),
	[Date of Birth] [varchar](500) NULL,
	[Race] [varchar](500) NOT NULL DEFAULT (''),
	[Political Affiliation] [varchar](500) NOT NULL DEFAULT (''),
	[Bar Number] [varchar](500) NOT NULL DEFAULT (''),
	[Online Directory] [varchar](500) NOT NULL DEFAULT ('')
)

IF OBJECT_ID('tempdb..##ca_listserv') IS NOT NULL
	DROP TABLE ##ca_listserv
CREATE TABLE ##ca_listserv (
	[rowid] [int] NOT NULL,
	[ID] [varchar](25) NOT NULL DEFAULT (''),
	[GEN] [varchar](40) NOT NULL DEFAULT (''),
	[POL] [varchar](40) NOT NULL DEFAULT (''),
	[MED] [varchar](40) NOT NULL DEFAULT (''),
	[WOMEN] [varchar](40) NOT NULL DEFAULT (''),
	[ASB] [varchar](40) NOT NULL DEFAULT (''),
	[HITWG] [varchar](40) NOT NULL DEFAULT (''),
	[CONCEP] [varchar](40) NOT NULL DEFAULT ('')
)

IF OBJECT_ID('tempdb..##ca_bio') IS NOT NULL
	DROP TABLE ##ca_bio
CREATE TABLE ##ca_bio (
	[rowid] [int] NOT NULL,
	[ID] [varchar](25) NOT NULL DEFAULT (''),
	[Note] [varchar](max) NOT NULL DEFAULT (''),
)


-- import csv files into temp tables. Use @tmpBCP to suppress cmdshell output from calling code
declare @cmd varchar(600)
declare @tmpBCP TABLE (theoutput varchar(max))

EXEC memberCentral.dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Importing Member Info Data'
SELECT @cmd = 'BULK INSERT ##ca_members FROM ''' + @pathToImport + 'web_mbr_list-f-migration.csv'' WITH (FIELDTERMINATOR = '''+ char(7) + ''', FIRSTROW = 2);'
insert into @tmpBCP (theoutput)
exec(@cmd)

EXEC memberCentral.dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Importing Listserv Data'
SELECT @cmd = 'BULK INSERT ##ca_listserv FROM ''' + @pathToImport + 'listserveexport.csv'' WITH (FIELDTERMINATOR = '''+ char(7) + ''', FIRSTROW = 2);'
insert into @tmpBCP (theoutput)
exec(@cmd)

EXEC memberCentral.dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Importing Bio Data'
SELECT @cmd = 'BULK INSERT ##ca_bio FROM ''' + @pathToImport + 'pcbio.csv'' WITH (FIELDTERMINATOR = '''+ char(7) + ''', FIRSTROW = 2);'
insert into @tmpBCP (theoutput)
exec(@cmd)

EXEC memberCentral.dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Flattening Data'

-- Clean up for case where members don't exist.
delete from ##ca_members where nullif(firstname,'') is null or nullif(lastname,'') is null
delete from ##ca_bio where nullif([Note],'') is null

-- put data into temp flat table
IF OBJECT_ID('tempdb..##tmpCA') IS NOT NULL
	DROP TABLE ##tmpCA
SELECT indiv.rowID, 
	indiv.firstname,
	indiv.middlename,
	indiv.lastname,
	indiv.suffix,
	indiv.professionalSuffix,
	indiv.company,
	indiv.Address_address1,
	indiv.Address_address2,
	indiv.Address_city,
	indiv.Address_stateprov,
	indiv.Address_postalCode,
	indiv.Address_county,
	indiv.Address_country,
	indiv.Address_Phone,
	indiv.Address_Fax,
	indiv.Website,
	indiv.Twitter,
	indiv.LinkedIn,
	indiv.Facebook,
	indiv.Email,
	indiv.ALT_EMAIL_1,
	indiv.[Email Opt In],
	indiv.[Fax Opt In],
	indiv.memberNumber,
	indiv.mbrtype,
	indiv.MemberDate,
	indiv.BOARD,
	indiv.BOD,
	indiv.TLA,
	indiv.Incoming,
	indiv.Defense,
	indiv.directory,
	indiv.Dist,
	indiv.AdvClub,
	indiv.BusFriend,
	indiv.PresClub,
	indiv.firmID,
	indiv.PCFirm,
	indiv.AOP1,
	indiv.AOP2,
	indiv.AOP3,
	indiv.AOP4,
	indiv.VM_1,
	indiv.VM_2,
	indiv.VM_3,
	indiv.VM_4,
	indiv.Gender,
	indiv.[Date of Birth],
	indiv.Race,
	indiv.[Political Affiliation],
	indiv.[Bar Number],
	indiv.[Online Directory],
	'' as [Address_attn],
	'' as [BillingAddressType],
	ls.GEN,
	ls.POL,
	ls.MED,
	ls.ASB,
	ls.HITWG,
	ls.CONCEP,
	ls.WOMEN,
	bio.NOTE as [Pres Club Bio]
INTO ##tmpCA
FROM ##ca_members as indiv
LEFT OUTER JOIN ##ca_listserv as ls on ls.id = indiv.memberNumber
LEFT OUTER JOIN ##ca_bio as bio on bio.id = indiv.memberNumber

-- cleanup
IF OBJECT_ID('tempdb..##ca_members') IS NOT NULL
	DROP TABLE ##ca_members
IF OBJECT_ID('tempdb..##ca_listserv') IS NOT NULL
	DROP TABLE ##ca_listserv
IF OBJECT_ID('tempdb..##ca_bio') IS NOT NULL
	DROP TABLE ##ca_bio

-- move data to holding tables
EXEC memberCentral.dbo.ams_importMemberData_tempToHolding @orgid=@orgID, @tmptbl='##tmpCA', @pathToExport=@pathToExport, @importResult=@importResult OUTPUT

RETURN 0
