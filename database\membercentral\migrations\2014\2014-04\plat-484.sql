declare @pageLanguagesTbl table(pageLanguageID int NOT NULL, dateModified datetime)

insert into @pageLanguagesTbl (
	pageLanguageID,
	dateModified
)
select 
	langTbl.pageLanguageID ,
	max(cv.dateCreated) as maxDateCreated
from
	cms_ContentVersions cv WITH(NOLOCK) 
	inner join dbo.cms_contentLanguages cl WITH(NOLOCK) on
		cl.contentLanguageID = cv.contentLanguageID	
		and cv.isActive =1	
	inner join cms_content c WITH(NOLOCK) on
		c.contentID = cl.contentID
	inner join cms_siteResources sr WITH(NOLOCK) on 
		sr.siteResourceID = c.siteResourceID
		and sr.siteResourceStatusID < 3
	inner join cms_siteResourceTypes srt WITH(NOLOCK)  on 
		srt.resourceTypeID = sr.resourceTypeID
		and srt.resourceType = 'UserCreatedContent'
	inner join dbo.cms_pageZonesResources pzr WITH(NOLOCK) on 
		pzr.siteResourceID = sr.siteResourceID
	inner join dbo.cms_pages p WITH(NOLOCK) on 
		p.pageID = pzr.pageID
	inner join sites s WITH(NOLOCK) on
		s.siteID = p.siteID
	left outer join dbo.cms_pageLanguages pl WITH(NOLOCK) on
		pl.pageID = p.pageID
		and pl.languageID = cl.languageID
	left outer join dbo.cms_pageLanguages pldefault WITH(NOLOCK) on
		pldefault.pageID = p.pageID
		and pldefault.languageID = s.defaultLanguageID
	inner join dbo.cms_pageLanguages langTbl WITH(NOLOCK) on
		langTbl.pageLanguageID = isnull(pl.pageLanguageID,pldefault.pageLanguageID)
group by langTbl.pageLanguageID,langTbl.dateModified
having max(cv.dateCreated) > langTbl.dateModified

select * from @pageLanguagesTbl

update
	pl
set
	pl.dateModified = tmp.dateModified
from
	dbo.cms_pageLanguages pl
	inner join @pageLanguagesTbl tmp on
		tmp.pageLanguageID = pl.pageLanguageID
GO	


