USE [memberCentral]
GO
IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[tr_report_baseAR]') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].[tr_report_baseAR]
GO
CREATE PROC [dbo].[tr_report_baseAR]
@orgid int,
@asOfDate datetime,
@startdate datetime = null,
@sumBy varchar(5) = null

AS

/*
The intent of this procedure is to generate the transactions with AR based on a given date.
It is meant to be used with both the AR report and the Invoice Aging report.
It is not meant to be used outside of these reports but that may change.

The report identifies the Gross AR (all the transactions up to the selected date) 
and then all offsetting AR entries, as in this:
+ sales with sale dateBilled earlier than startDate
+ tax with tax dateBilled earlier than startDate
+ postive adjustments with adjustment dateBilled earlier than startDate
- negative adjustments with adjustment dateBilled earlier than startDate
- voidoffsets of sales with void dateBilled earlier than startDate
- voidoffsets of postive adjustments with void dateBilled earlier than startDate
+ voidoffsets of negative adjustments with void dateBilled earlier than startDate
- allocations with allocation batch date earlier than startDate
+ deallocations with deallocation batch date earlier than startDate
- write offs with write off transactionDate earlier than startDate
+ voidoffsets of allocations with void batch date earlier than startDate
- voidoffsets of deallocations with void batch date earlier than startDate
+ voidoffsets of write offs with void transactionDate earlier than startDate
*/

-- sum by must be valid
IF @sumBy not in ('none','gl','inv','invgl')
	select @sumBy = 'none'

-- set date to 11:59:59 of asOfDate
select @asOfDate = dateadd(ms,-3,dateadd(day,1,DATEADD(dd,DATEDIFF(dd,0,@asOfDate),0)))


-- Get Pool of invoices to consider: 
-- 1) BILLED DATE on or before asOfDate
-- 2) invoice is not currently OPEN.
IF OBJECT_ID('tempdb..#tmpINV') IS NOT NULL 
	DROP TABLE #tmpINV
CREATE TABLE #tmpINV (invoiceID int PRIMARY KEY, memberID int, dateBilled datetime);
insert into #tmpINV
select i.invoiceID, m2.memberID, i.dateBilled
from dbo.tr_invoices as i
inner join dbo.ams_members as m on m.memberID = i.assignedToMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
where m.orgID = @orgID
and i.dateBilled <= @asOfDate
and i.statusID <> 1


IF OBJECT_ID('tempdb..#allRevTrans') IS NOT NULL 
	DROP TABLE #allRevTrans
CREATE TABLE #allRevTrans (invoiceID int, revenueGLAccountID int, startAmount money, endAmount money)

-- all sales/tax 
insert into #allRevTrans
select i.invoiceID, t.creditGLAccountID, case when @startdate is not null and i.dateBilled < @startdate then t.amount else 0 end, t.amount
from dbo.tr_transactions as t
inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
inner join #tmpINV as i on i.invoiceID = it.invoiceID
where t.typeID in (1,7)

-- all postive adj 
insert into #allRevTrans
select i.invoiceID, t.creditGLAccountID, case when @startdate is not null and i.dateBilled < @startdate then t.amount else 0 end, t.amount
from dbo.tr_transactions as t
inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
inner join #tmpINV as i on i.invoiceID = it.invoiceID
inner join dbo.tr_glaccounts as gl on gl.glaccountID = t.debitGLAccountID
where t.typeID = 3
and gl.GLCode = 'ACCOUNTSRECEIVABLE'

-- all negative adj offsets revenue
insert into #allRevTrans
select itt.invoiceID, t.debitGLAccountID, case when @startdate is not null and i.dateBilled < @startdate then r.amount*-1 else 0 end, r.amount*-1
from dbo.tr_transactions as t
inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
inner join #tmpINV as i on i.invoiceID = it.invoiceID
inner join dbo.tr_glaccounts as gl on gl.glaccountID = t.creditGLAccountID
inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustInvTrans'
inner join dbo.tr_transactions as tSalesTax on tSalesTax.transactionID = r.appliedToTransactionID and tSalesTax.typeID in (1,3,7)
inner join dbo.tr_invoiceTransactions as itt on itt.transactionID = tSalesTax.transactionID
where t.typeID = 3
and gl.GLCode = 'ACCOUNTSRECEIVABLE'

-- voids of sales/tax offsets revenue
insert into #allRevTrans
select itt.invoiceID, tV.debitGLAccountID, case when @startdate is not null and i.dateBilled < @startdate then tV.amount*-1 else 0 end, tV.amount*-1
from dbo.tr_transactions as tV 
inner join dbo.tr_invoiceTransactions as it on it.transactionID = tV.transactionID
inner join #tmpINV as i on i.invoiceID = it.invoiceID
inner join dbo.tr_relationships as r on r.transactionID = tV.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'OffsetTrans'
inner join dbo.tr_transactions as t on t.transactionID = r.appliedToTransactionID and t.typeID in (1,7)
inner join dbo.tr_invoiceTransactions as itt on itt.transactionID = t.transactionID
where tV.typeID = 8

-- voids of pos adj offsets revenue
insert into #allRevTrans
select itt.invoiceID, tV.debitGLAccountID, case when @startdate is not null and i.dateBilled < @startdate then tV.amount*-1 else 0 end, tV.amount*-1
from dbo.tr_transactions as tV 
inner join dbo.tr_invoiceTransactions as it on it.transactionID = tV.transactionID
inner join #tmpINV as i on i.invoiceID = it.invoiceID
inner join dbo.tr_relationships as r on r.transactionID = tV.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'OffsetTrans'
inner join dbo.tr_transactions as t on t.transactionID = r.appliedToTransactionID and t.typeID = 3
inner join dbo.tr_glaccounts as gl on gl.glaccountID = t.debitGLAccountID
inner join dbo.tr_invoiceTransactions as itt on itt.transactionID = t.transactionID
where tV.typeID = 8
and gl.GLCode = 'ACCOUNTSRECEIVABLE'

-- voids of neg adj 
insert into #allRevTrans
select itt.invoiceID, tV.creditGLAccountID, case when @startdate is not null and i.dateBilled < @startdate then tV.amount else 0 end, tV.amount
from dbo.tr_transactions as tV 
inner join dbo.tr_invoiceTransactions as it on it.transactionID = tV.transactionID
inner join #tmpINV as i on i.invoiceID = it.invoiceID
inner join dbo.tr_relationships as r on r.transactionID = tV.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'OffsetTrans'
inner join dbo.tr_transactions as t on t.transactionID = r.appliedToTransactionID and t.typeID = 3
inner join dbo.tr_glaccounts as gl on gl.glaccountID = t.creditGLAccountID
inner join dbo.tr_relationships as rAdj on rAdj.transactionID = t.transactionID
inner join dbo.tr_relationshipTypes as rtAdj on rtAdj.typeID = rAdj.typeID and rtAdj.type = 'AdjustTrans'
inner join dbo.tr_transactions as tSalesTax on tSalesTax.transactionID = rAdj.appliedToTransactionID and tSalesTax.typeID in (1,7)
inner join dbo.tr_invoiceTransactions as itt on itt.transactionID = tSalesTax.transactionID
where tV.typeID = 8
and gl.GLCode = 'ACCOUNTSRECEIVABLE'

-- allocation
insert into #allRevTrans
select it.invoiceID, tSalesTaxAdj.creditGLAccountID, case when @startdate is not null and b.depositDate < @startdate then tAlloc.amount else 0 end *-1, tAlloc.amount*-1
from dbo.tr_transactions as tAlloc 
inner join dbo.tr_batchTransactions as bt on bt.transactionID = tAlloc.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID
inner join dbo.tr_glaccounts as gl on gl.glaccountID = tAlloc.creditGLAccountID
inner join dbo.tr_relationships as rAlloc on rAlloc.transactionID = tAlloc.transactionID
inner join dbo.tr_relationshipTypes as rtAlloc on rtAlloc.typeID = rAlloc.typeID and rtAlloc.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as tSalesTaxAdj on tSalesTaxAdj.transactionID = rAlloc.appliedToTransactionID
inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSalesTaxAdj.transactionID
where tAlloc.ownedByOrgID = @orgID
and tAlloc.typeID = 5
and b.depositDate <= @asOfDate
and gl.glCode = 'ACCOUNTSRECEIVABLE'

-- deallocation offsets alloc
insert into #allRevTrans
select it.invoiceID, tSalesTaxAdj.creditGLAccountID, case when @startdate is not null and b.depositDate < @startdate then tAlloc.amount*-1 else 0 end *-1, tAlloc.amount
from dbo.tr_transactions as tAlloc 
inner join dbo.tr_batchTransactions as bt on bt.transactionID = tAlloc.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID
inner join dbo.tr_glaccounts as gl on gl.glaccountID = tAlloc.debitGLAccountID
inner join dbo.tr_relationships as rAlloc on rAlloc.transactionID = tAlloc.transactionID
inner join dbo.tr_relationshipTypes as rtAlloc on rtAlloc.typeID = rAlloc.typeID and rtAlloc.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as tSalesTaxAdj on tSalesTaxAdj.transactionID = rAlloc.appliedToTransactionID
inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSalesTaxAdj.transactionID
where tAlloc.ownedByOrgID = @orgID
and tAlloc.typeID = 5
and b.depositDate <= @asOfDate
and gl.glCode = 'ACCOUNTSRECEIVABLE'

-- write off
insert into #allRevTrans
select it.invoiceID, tSalesTaxAdj.creditGLAccountID, case when @startdate is not null and tWO.transactionDate < @startdate then tWO.amount else 0 end *-1, tWO.amount*-1
from dbo.tr_transactions as tWO 
inner join dbo.tr_glaccounts as gl on gl.glaccountID = tWO.creditGLAccountID
inner join dbo.tr_relationships as rWO on rWO.transactionID = tWO.transactionID
inner join dbo.tr_relationshipTypes as rtWO on rtWO.typeID = rWO.typeID and rtWO.type = 'WriteOffSaleTrans'
inner join dbo.tr_transactions as tSalesTaxAdj on tSalesTaxAdj.transactionID = rWO.appliedToTransactionID
inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSalesTaxAdj.transactionID
where tWO.ownedByOrgID = @orgID
and tWO.typeID = 6
and tWO.transactionDate <= @asOfDate
and gl.glCode = 'ACCOUNTSRECEIVABLE'

-- void of allocation offsets alloc
insert into #allRevTrans
select it.invoiceID, tSalesTaxAdj.creditGLAccountID, case when @startdate is not null and b.depositDate < @startdate then tV.amount*-1 else 0 end *-1, tV.amount
from dbo.tr_transactions as tV 
inner join dbo.tr_batchTransactions as bt on bt.transactionID = tV.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID
inner join dbo.tr_relationships as rV on rV.transactionID = tV.transactionID
inner join dbo.tr_relationshipTypes as rtV on rtV.typeID = rV.typeID and rtV.type = 'OffsetTrans'
inner join dbo.tr_transactions as tAlloc on tAlloc.transactionID = rV.appliedToTransactionID and tAlloc.typeID = 5
inner join dbo.tr_glaccounts as gl on gl.glaccountID = tAlloc.creditGLAccountID
inner join dbo.tr_relationships as rAlloc on rAlloc.transactionID = tAlloc.transactionID
inner join dbo.tr_relationshipTypes as rtAlloc on rtAlloc.typeID = rAlloc.typeID and rtAlloc.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as tSalesTaxAdj on tSalesTaxAdj.transactionID = rAlloc.appliedToTransactionID
inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSalesTaxAdj.transactionID
where tV.ownedByOrgID = @orgID
and tV.typeID = 8
and b.depositDate <= @asOfDate
and gl.GLCode = 'ACCOUNTSRECEIVABLE'

-- void of deallocation 
insert into #allRevTrans
select it.invoiceID, tSalesTaxAdj.creditGLAccountID, case when @startdate is not null and b.depositDate < @startdate then tV.amount else 0 end *-1, tV.amount*-1
from dbo.tr_transactions as tV 
inner join dbo.tr_batchTransactions as bt on bt.transactionID = tV.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID
inner join dbo.tr_relationships as rV on rV.transactionID = tV.transactionID
inner join dbo.tr_relationshipTypes as rtV on rtV.typeID = rV.typeID and rtV.type = 'OffsetTrans'
inner join dbo.tr_transactions as tAlloc on tAlloc.transactionID = rV.appliedToTransactionID and tAlloc.typeID = 5
inner join dbo.tr_glaccounts as gl on gl.glaccountID = tAlloc.debitGLAccountID
inner join dbo.tr_relationships as rAlloc on rAlloc.transactionID = tAlloc.transactionID
inner join dbo.tr_relationshipTypes as rtAlloc on rtAlloc.typeID = rAlloc.typeID and rtAlloc.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as tSalesTaxAdj on tSalesTaxAdj.transactionID = rAlloc.appliedToTransactionID
inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSalesTaxAdj.transactionID
where tV.ownedByOrgID = @orgID
and tV.typeID = 8
and b.depositDate <= @asOfDate
and gl.GLCode = 'ACCOUNTSRECEIVABLE'

-- void of write off offets alloc
insert into #allRevTrans
select it.invoiceID, tSalesTaxAdj.creditGLAccountID, case when tV.transactionDate < @startdate then tV.amount*-1 else 0 end *-1, tV.amount
from dbo.tr_transactions as tV 
inner join dbo.tr_relationships as rV on rV.transactionID = tV.transactionID
inner join dbo.tr_relationshipTypes as rtV on rtV.typeID = rV.typeID and rtV.type = 'OffsetTrans'
inner join dbo.tr_transactions as tWO on tWO.transactionID = rV.appliedToTransactionID and tWO.typeID = 6
inner join dbo.tr_glaccounts as gl on gl.glaccountID = tWO.creditGLAccountID
inner join dbo.tr_relationships as rWO on rWO.transactionID = tWO.transactionID
inner join dbo.tr_relationshipTypes as rtWO on rtWO.typeID = rWO.typeID and rtWO.type = 'WriteOffSaleTrans'
inner join dbo.tr_transactions as tSalesTaxAdj on tSalesTaxAdj.transactionID = rWO.appliedToTransactionID
inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSalesTaxAdj.transactionID
where tV.ownedByOrgID = @orgID
and tV.typeID = 8
and tV.transactionDate <= @asOfDate
and gl.GLCode = 'ACCOUNTSRECEIVABLE'


-- include data for warning messages (open invoices in date range)
declare @openInvoices int
select @openInvoices = count(distinct i.invoiceID)
	from dbo.tr_invoices as i
	inner join dbo.ams_members as m on m.memberID = i.assignedToMemberID
	inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
	where m.orgID = @orgID
	and i.dateBilled <= @asOfDate
	and i.statusID = 1



-- return data
IF @sumBy = 'none'
	select invoiceID, revenueGLAccountID, startAmount, endAmount, @openInvoices as openInvoices
	from #allRevTrans
	where NOT (startAmount = 0 AND endAmount = 0)
IF @sumBy = 'gl'
	select revenueGLAccountID, sum(StartAmount) as StartAmount, sum(EndAmount) as EndAmount, @openInvoices as openInvoices
	from #allRevTrans
	group by revenueGLAccountID
IF @sumBy = 'inv'
	select invoiceID, sum(EndAmount) as EndAmount, @openInvoices as openInvoices
	from #allRevTrans
	group by invoiceID
	having sum(EndAmount) > 0
IF @sumBy = 'invgl'
	select invoiceID, revenueGLAccountID, sum(EndAmount) as EndAmount, @openInvoices as openInvoices
	from #allRevTrans
	group by invoiceID, revenueGLAccountID
	having sum(EndAmount) > 0


IF OBJECT_ID('tempdb..#allRevTrans') IS NOT NULL 
	DROP TABLE #allRevTrans
IF OBJECT_ID('tempdb..#tmpINV') IS NOT NULL 
	DROP TABLE #tmpINV

RETURN 0
GO

ALTER PROC [dbo].[tr_report_ar]
@orgid int,
@startdate datetime,
@enddate datetime,
@diffOnly bit,
@filename varchar(400) = null

AS

IF OBJECT_ID('tempdb..#tmpARReport') IS NOT NULL 
	DROP TABLE #tmpARReport
CREATE TABLE #tmpARReport (revenueGLAccountID int, ARStart money, AREnd money, openInvoices int)

INSERT INTO #tmpARReport (revenueGLAccountID, ARStart, AREnd, openInvoices)
EXEC dbo.tr_report_baseAR @orgid, @enddate, @startdate, 'gl'

CREATE CLUSTERED INDEX IX_tmpARReport_GLAID ON #tmpARReport (revenueGLAccountID asc);

IF @filename is not null BEGIN
	DECLARE @fullsql varchar(max)
	SELECT @fullsql = '
		select rgl.thePathExpanded as [Revenue Account], rgl.accountCode as [Revenue Account Code], 
			tmp.ARStart as [AR Start], tmp.AREnd as [AR End], tmp.AREnd-tmp.ARStart as [AR Difference]
		from #tmpARReport as tmp
		INNER JOIN dbo.fn_getRecursiveGLAccounts(' + cast(@orgID as varchar(10)) + ') as rgl on rgl.GLAccountID = tmp.revenueGLAccountID
		where NOT (tmp.ARStart = 0 AND tmp.AREnd = 0) '
	IF @diffOnly=1
		SELECT @fullsql = @fullsql + 'AND tmp.AREnd-tmp.ARStart <> 0 '
	SELECT @fullsql = @fullsql + 'order by rgl.thePath'

	EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql
END ELSE BEGIN
	select rgl.GLAccountID, rgl.accountCode, rgl.thePathExpanded, rgl.thePath, tmp.ARStart, tmp.AREnd, tmp.AREnd-tmp.ARStart as ARDifference, tmp.openInvoices
	from #tmpARReport as tmp
	INNER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) as rgl on rgl.GLAccountID = tmp.revenueGLAccountID
	where NOT (tmp.ARStart = 0 AND tmp.AREnd = 0)
	and 1=case when @diffOnly=1 and tmp.AREnd-tmp.ARStart <> 0 then 1 when @diffOnly=0 then 1 else 0 end
		union all
	select 0, '', 'Report Total', '*********9', sum(tmp.ARStart), sum(tmp.AREnd), sum(tmp.AREnd-tmp.ARStart), null
	from #tmpARReport as tmp
	where NOT (tmp.ARStart = 0 AND tmp.AREnd = 0)
	and 1=case when @diffOnly=1 and tmp.AREnd-tmp.ARStart <> 0 then 1 when @diffOnly=0 then 1 else 0 end
	order by thePath
END

IF OBJECT_ID('tempdb..#tmpARReport') IS NOT NULL 
	DROP TABLE #tmpARReport

RETURN 0
GO

DROP FUNCTION dbo.fn_tr_getAdjustedAmountofSaleOrAdj
GO

ALTER PROC [dbo].[tr_report_invoiceaging]
@orgid int,
@endDate datetime,
@groupBy varchar(7),
@display varchar(7),
@filename varchar(400) = null

AS

SET NOCOUNT ON

-- group by must be valid
IF @groupBy not in ('member','invoice')
	select @groupBy = 'member'

-- display must be valid
IF @display not in ('summary','detail')
	select @display = 'summary'


-- setup holding tables
DECLARE @orgCode varchar(10), @fullsql varchar(max)
SELECT @orgcode = orgcode from dbo.organizations where orgID = @orgID

IF OBJECT_ID('tempdb..#tmpARReport') IS NOT NULL 
	DROP TABLE #tmpARReport
CREATE TABLE #tmpARReport (invoiceID int, revenueGLAccountID int, AREnd money, openInvoices int)

IF OBJECT_ID('tempdb..#tmpINVAGEReport') IS NOT NULL 
	DROP TABLE #tmpINVAGEReport
CREATE TABLE #tmpINVAGEReport (invoiceid int, invoiceNumber varchar(30), dateCreated datetime, dateBilled datetime, 
	dateDue datetime, revenueGLAID int,	revenueGL varchar(max),	revenueGLCode varchar(max), assignedToMemberId int, 
	memberName varchar(300), membernumber varchar(50), company varchar(200),
	AREnd money, ageFut bit, age030 bit, age3160 bit, age6190 bit, age91120 bit, age120over bit, openInvoices int)


-- get data
IF @display = 'detail' 
BEGIN
	INSERT INTO #tmpARReport (invoiceID, revenueGLAccountID, AREnd, openInvoices)
	EXEC dbo.tr_report_baseAR @orgid, @enddate, null, 'invgl'

	CREATE CLUSTERED INDEX IX_tmpARReport_invGLAID ON #tmpARReport (invoiceID asc, revenueGLAccountID asc);

	IF OBJECT_ID('tempdb..#tblGL') IS NOT NULL 
		DROP TABLE #tblGL
	CREATE TABLE #tblGL (GLAccountID int PRIMARY KEY, AccountCode varchar(200), thePathExpanded varchar(max))
	insert into #tblGL
	select GLAccountID, AccountCode, thePathExpanded
	from dbo.fn_getRecursiveGLAccounts(@orgID)
	
	insert into #tmpINVAGEReport (invoiceid, invoiceNumber, dateCreated, dateBilled, dateDue, revenueGLAID, revenueGL, revenueGLCode, 
		assignedToMemberId, memberName, memberNumber, company, AREnd, ageFut, age030, age3160, age6190, age91120, age120over, openInvoices)
	select i.invoiceID, @orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, 
		i.dateCreated, i.dateBilled, i.dateDue, 
		gl.glAccountID, gl.thePathExpanded, gl.accountCode,
		m2.memberID as assignedToMemberID, 
		m2.lastname + ', ' + m2.firstname + isnull(' ' + nullif(m2.middlename,''),''), m2.membernumber,
		m2.company as company, tmp.AREnd,
		ageFut = case when datediff(dd,i.dateDue,@enddate) < 0 then 1 else 0 end,
		age030 = case when datediff(dd,i.dateDue,@enddate) between 0 and 30 then 1 else 0 end,
		age3160 = case when datediff(dd,i.dateDue,@enddate) between 31 and 60 then 1 else 0 end,
		age6190 = case when datediff(dd,i.dateDue,@enddate) between 61 and 90 then 1 else 0 end,
		age91120 = case when datediff(dd,i.dateDue,@enddate) between 91 and 120 then 1 else 0 end,
		age120over = case when datediff(dd,i.dateDue,@enddate) > 120 then 1 else 0 end,
		tmp.openInvoices
	from #tmpARReport as tmp
	inner join dbo.tr_invoices AS i on i.invoiceID = tmp.invoiceID
	inner join dbo.ams_members as m on m.memberID = i.assignedToMemberID
	inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
	inner join #tblGL as gl on gl.glAccountID = tmp.revenueGLAccountID

	IF OBJECT_ID('tempdb..#tblGL') IS NOT NULL 
		DROP TABLE #tblGL
END
IF @display = 'summary'
BEGIN
	INSERT INTO #tmpARReport (invoiceID, AREnd, openInvoices)
	EXEC dbo.tr_report_baseAR @orgid, @enddate, null, 'inv'

	CREATE CLUSTERED INDEX IX_tmpARReport_invID ON #tmpARReport (invoiceID asc);

	insert into #tmpINVAGEReport (invoiceid, invoiceNumber, dateCreated, dateBilled, dateDue, assignedToMemberId, memberName, memberNumber, 
		company, AREnd, ageFut, age030, age3160, age6190, age91120, age120over, openInvoices)
	select i.invoiceID, @orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, 
		i.dateCreated, i.dateBilled, i.dateDue, m2.memberID as assignedToMemberID, 
		m2.lastname + ', ' + m2.firstname + isnull(' ' + nullif(m2.middlename,''),''), m2.membernumber,
		m2.company as company, tmp.AREnd,
		ageFut = case when datediff(dd,i.dateDue,@enddate) < 0 then 1 else 0 end,
		age030 = case when datediff(dd,i.dateDue,@enddate) between 0 and 30 then 1 else 0 end,
		age3160 = case when datediff(dd,i.dateDue,@enddate) between 31 and 60 then 1 else 0 end,
		age6190 = case when datediff(dd,i.dateDue,@enddate) between 61 and 90 then 1 else 0 end,
		age91120 = case when datediff(dd,i.dateDue,@enddate) between 91 and 120 then 1 else 0 end,
		age120over = case when datediff(dd,i.dateDue,@enddate) > 120 then 1 else 0 end,
		tmp.openInvoices
	from #tmpARReport as tmp
	inner join dbo.tr_invoices AS i on i.invoiceID = tmp.invoiceID
	inner join dbo.ams_members as m on m.memberID = i.assignedToMemberID
	inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
END


-- final data 
IF @groupBy = 'invoice' and @display = 'summary' and @filename is not null BEGIN
	SELECT @fullsql = '
		select invoiceNumber as [Invoice], 
			convert(varchar(10),dateCreated,101) as [Date Created], convert(varchar(10),dateBilled,101) as [Date Billed], 
			convert(varchar(10),dateDue,101) as [Date Due], memberName as [Member], 
			memberNumber as [Member Number], company as [Company], 
			case when ageFut = 1 then AREnd else null end as [Due Future],
			case when age030 = 1 then AREnd else null end as [0-30 Days],
			case when age3160 = 1 then AREnd else null end as [31-60 Days],
			case when age6190 = 1 then AREnd else null end as [61-90 Days],
			case when age91120 = 1 then AREnd else null end as [91-120 Days],
			case when age120over = 1 then AREnd else null end as [120+ Days]
		from #tmpINVAGEReport
		order by dateDue, invoiceNumber'
	EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql
END 
IF @groupBy = 'invoice' and @display = 'detail' and @filename is not null BEGIN
	SELECT @fullsql = '
		select invoiceNumber as [Invoice], 
			convert(varchar(10),dateCreated,101) as [Date Created], convert(varchar(10),dateBilled,101) as [Date Billed], 
			convert(varchar(10),dateDue,101) as [Date Due], memberName as [Member], 
			memberNumber as [Member Number], company as [Company], 
			revenueGL as [Revenue Account], revenueGLCode as [Revenue Account Code], 
			case when ageFut = 1 then AREnd else null end as [Due Future],
			case when age030 = 1 then AREnd else null end as [0-30 Days],
			case when age3160 = 1 then AREnd else null end as [31-60 Days],
			case when age6190 = 1 then AREnd else null end as [61-90 Days],
			case when age91120 = 1 then AREnd else null end as [91-120 Days],
			case when age120over = 1 then AREnd else null end as [120+ Days]
		from #tmpINVAGEReport
		order by dateDue, invoiceNumber, revenueGL'
	EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql
END 
IF @groupBy = 'invoice' and @filename is null BEGIN
	select *
	from (
		select invoiceID, invoiceNumber, dateBilled, dateDue, assignedToMemberId, memberName, membernumber, company, revenueGLAID, revenueGL, revenueGLCode, 
			ageFut, age030, age3160, age6190, age91120, age120over, 
			case when ageFut = 1 then AREnd else null end as invFut,
			case when age030 = 1 then AREnd else null end as inv030,
			case when age3160 = 1 then AREnd else null end as inv3160,
			case when age6190 = 1 then AREnd else null end as inv6190,
			case when age91120 = 1 then AREnd else null end as inv91120,
			case when age120over = 1 then AREnd else null end as inv120over, openInvoices
		from #tmpINVAGEReport
			union all
		select null as invoiceid, null as invoiceNumber, null, null, ********* as assignedToMemberId, 'Report Total' as memberName, null, null, revenueGLAID, revenueGL, revenueGLCode, 
			1, 1, 1, 1, 1, 1, 
			sum(case when ageFut = 1 then AREnd else 0 end) as invFut,
			sum(case when age030 = 1 then AREnd else 0 end) as inv030,
			sum(case when age3160 = 1 then AREnd else 0 end) as inv3160,
			sum(case when age6190 = 1 then AREnd else 0 end) as inv6190,
			sum(case when age91120 = 1 then AREnd else 0 end) as inv91120,
			sum(case when age120over = 1 then AREnd else 0 end) as inv120over, null
		from #tmpINVAGEReport
		group by revenueGLAID, revenueGL, revenueGLCode
	) as tmp
	order by case when invoiceid is null then 1 else 0 end, dateDue, invoiceNumber
END
IF @groupBy = 'member' and @display = 'summary' and @filename is not null BEGIN
	SELECT @fullsql = '
		select memberName as [Member], memberNumber as [Member Number], company as [Company], invoiceNumber as [Invoice], 
			convert(varchar(10),dateCreated,101) as [Date Created], convert(varchar(10),dateBilled,101) as [Date Billed], 
			convert(varchar(10),dateDue,101) as [Date Due], 
			case when ageFut = 1 then AREnd else null end as [Due Future],
			case when age030 = 1 then AREnd else null end as [0-30 Days],
			case when age3160 = 1 then AREnd else null end as [31-60 Days],
			case when age6190 = 1 then AREnd else null end as [61-90 Days],
			case when age91120 = 1 then AREnd else null end as [91-120 Days],
			case when age120over = 1 then AREnd else null end as [120+ Days]
		from #tmpINVAGEReport
		order by memberName, membernumber, assignedToMemberId, invoiceNumber'
	EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql
END 
IF @groupBy = 'member' and @display = 'detail' and @filename is not null BEGIN
	SELECT @fullsql = '
		select memberName as [Member], memberNumber as [Member Number], company as [Company], invoiceNumber as [Invoice], 
			convert(varchar(10),dateCreated,101) as [Date Created], convert(varchar(10),dateBilled,101) as [Date Billed], 
			convert(varchar(10),dateDue,101) as [Date Due], 
			revenueGL as [Revenue Accounr], revenueGLCode as [Revenue Account Code], 
			case when ageFut = 1 then AREnd else null end as [Due Future],
			case when age030 = 1 then AREnd else null end as [0-30 Days],
			case when age3160 = 1 then AREnd else null end as [31-60 Days],
			case when age6190 = 1 then AREnd else null end as [61-90 Days],
			case when age91120 = 1 then AREnd else null end as [91-120 Days],
			case when age120over = 1 then AREnd else null end as [120+ Days]
		from #tmpINVAGEReport
		order by memberName, membernumber, assignedToMemberId, invoiceNumber, revenueGL'
	EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql
END 
IF @groupBy = 'member' and @filename is null BEGIN
	select *
	from (
		select invoiceID, invoiceNumber, dateBilled, dateDue, assignedToMemberId, memberName, membernumber, company, revenueGLAID, revenueGL, revenueGLCode, 
			ageFut, age030, age3160, age6190, age91120, age120over, 
			case when ageFut = 1 then AREnd else null end as invFut,
			case when age030 = 1 then AREnd else null end as inv030,
			case when age3160 = 1 then AREnd else null end as inv3160,
			case when age6190 = 1 then AREnd else null end as inv6190,
			case when age91120 = 1 then AREnd else null end as inv91120,
			case when age120over = 1 then AREnd else null end as inv120over, openInvoices
		from #tmpINVAGEReport
			union all
		select null as invoiceid, null as invoiceNumber, null, null, ********* as assignedToMemberId, 'Report Total' as memberName, null, null, revenueGLAID, revenueGL, revenueGLCode, 
			1, 1, 1, 1, 1, 1, 
			sum(case when ageFut = 1 then AREnd else 0 end) as invFut,
			sum(case when age030 = 1 then AREnd else 0 end) as inv030,
			sum(case when age3160 = 1 then AREnd else 0 end) as inv3160,
			sum(case when age6190 = 1 then AREnd else 0 end) as inv6190,
			sum(case when age91120 = 1 then AREnd else 0 end) as inv91120,
			sum(case when age120over = 1 then AREnd else 0 end) as inv120over, null
		from #tmpINVAGEReport
		group by revenueGLAID, revenueGL, revenueGLCode
	) as tmp
	order by case when invoiceid is null then 1 else 0 end, memberName, membernumber, assignedToMemberId, invoiceNumber, revenueGL
END


-- drop the final temp table
IF OBJECT_ID('tempdb..#tmpINVAGEReport') IS NOT NULL 
	DROP TABLE #tmpINVAGEReport
IF OBJECT_ID('tempdb..#tmpARReport') IS NOT NULL 
	DROP TABLE #tmpARReport

RETURN 0
GO

