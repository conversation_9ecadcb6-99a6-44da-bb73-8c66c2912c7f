use membercentral
GO

ALTER PROC [dbo].[tr_createTransaction_allocation]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@status varchar(20),
@amount money,
@transactionDate datetime,
@paymentTransactionID int,
@saleTransactionID int,	-- this can be a sale, tax, adjustment, or DIT
@transactionID int OUTPUT

AS

set nocount on

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- reset output param
	select @transactionID = 0

	-- no zero dollar allocations
	if @amount = 0
		RAISERROR('amount is 0', 16, 1);

	-- ensure amount is 2 decimals
	select @amount = cast(@amount as decimal(10,2))

	-- ensure @paymentTransactionID is a payment
	IF NOT EXISTS (select transactionID from dbo.tr_transactions where transactionID = @paymentTransactionID and typeID = 2)
		RAISERROR('paymentTransactionID is not a payment', 16, 1);

	-- ensure @saleTransactionID is either a sale, sales tax, positive adjustment, or pos DIT
	IF NOT EXISTS (
		select t.transactionID
		from dbo.tr_transactions as t
		inner join dbo.tr_glAccounts as glDeb on glDeb.GLAccountID = t.debitGLAccountID
		where t.transactionID = @saleTransactionID
		and t.typeID in (1,3,7)
		and glDeb.GLCode = 'ACCOUNTSRECEIVABLE' 
		and glDeb.isSystemAccount = 1
		) AND NOT EXISTS (
			select t.transactionID
			from dbo.tr_transactions as t
			inner join dbo.tr_glAccounts as glDeb on glDeb.GLAccountID = t.debitGLAccountID
			inner join dbo.tr_glAccountTypes as glt on glt.accountTypeID = glDeb.accountTypeID
			where t.transactionID = @saleTransactionID
			and t.typeID = 10
			and glt.accountType = 'Liability' 
			and glDeb.isSystemAccount = 1
		)
	RAISERROR('saleTransactionID is not a sale, sales tax, positive adjustment, or deferred transfer', 16, 1);

	-- get info from payment transaction
	declare @assignedToMemberID int, @ownedByOrgID int, @payProfileID int, @payProfileCode varchar(20),
		@payCashGLID int, @payCashGLName varchar(200)
	select @assignedToMemberID = t.assignedToMemberID, 
		@ownedByOrgID = t.ownedByOrgID,
		@payProfileID = mp.profileID,
		@payProfileCode = mp.profileCode,
		@payCashGLID = glDeb.GLAccountID,
		@payCashGLName = glDeb.AccountName
	from dbo.tr_transactions as t
	inner join dbo.tr_transactionPayments as tp on tp.transactionID = t.transactionID
	inner join dbo.mp_profiles as mp on mp.profileID = tp.profileID
	inner join dbo.tr_GLAccounts as glDeb on glDeb.GLAccountID = t.debitGLAccountID
	where t.transactionID = @paymentTransactionID

	-- dont assume memberid is the active one. get the active one.
	select @assignedToMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @assignedToMemberID 
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	-- if amount is positive, allocating. Debit is DEP, Credit is AR.
	-- if amount is negative, deallocating. Debit is AR, Credit is DEP.
	declare @debitGLAccountID int, @creditGLAccountID int, @ARGLAID int, @DEPGLAID int
	select @ARGLAID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and isSystemAccount = 1
		and GLCode = 'ACCOUNTSRECEIVABLE'
		and [status] = 'A'
	select @DEPGLAID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and isSystemAccount = 1
		and GLCode = 'DEPOSITS'
		and [status] = 'A'
	if @amount > 0 BEGIN
		select @debitGLAccountID = @DEPGLAID
		select @creditGLAccountID = @ARGLAID
	END
	ELSE BEGIN
		select @debitGLAccountID = @ARGLAID
		select @creditGLAccountID = @DEPGLAID
	END

	-- ensure we have active debit/credit accts
	IF @debitGLAccountID is null or @creditGLAccountID is null
		RAISERROR('debitGLAccountID or creditGLAccountID is null', 16, 1);

	-- if allocating to a sale on an open/pending invoice, close it. Only closed inv can accept payment.
	-- no invoice if allocating to a DIT
	declare @invoiceID int, @invstatus varchar(50)
	select @invoiceID=i.invoiceID, @invstatus=ins.status
		from dbo.tr_invoices as i
		inner join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
		inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
		where it.transactionID = @saleTransactionID
	IF @invoiceID is not null and @amount > 0 AND @invstatus <> 'Closed'
		EXEC dbo.tr_closeInvoice @enteredByMemberID=@recordedByMemberID, @invoiceIDList=@invoiceID

	-- insert into transactions
	-- ensure amount is abs
	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
		amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
		typeID, accrualDate, debitGLAccountID, creditGLAccountID)
	VALUES (@ownedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID(@status), null, null, 
		abs(@amount), getdate(), @transactionDate, @assignedToMemberID, @recordedByMemberID, @statsSessionID, 
		5, @transactionDate, @debitGLAccountID, @creditGLAccountID)
		select @transactionID = SCOPE_IDENTITY()

	-- insert into relationships
	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
	VALUES (dbo.fn_tr_getRelationshipTypeID('AllocPayTrans'), @transactionID, @paymentTransactionID)

	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
	VALUES (dbo.fn_tr_getRelationshipTypeID('AllocSaleTrans'), @transactionID, @saleTransactionID)

	-- add allocation to batch
	-- put on same batch as payment if that batch is open.
	-- otherwise, put on daily exception batch
	declare @allocBatchID int, @batchCode varchar(40), @batchName varchar(400)
	select @allocBatchID = b.batchID
		from dbo.tr_batchTransactions as bt
		inner join dbo.tr_batches as b on b.batchID = bt.batchID
		where bt.transactionID = @paymentTransactionID
		and b.statusID = 1
	IF @allocBatchID is null BEGIN
		select @batchCode = CONVERT(CHAR(8),@transactionDate,112) + '_' + cast(@payProfileID as varchar(10)) + '_' + cast(@payCashGLID as varchar(10)) + '_EX'
		select @batchName = CONVERT(CHAR(8),@transactionDate,112) + ' ' + @payProfileCode + ' ' + @payCashGLName + ' Exceptions'
		select @allocBatchID = b.batchID
			from dbo.tr_batches as b
			where b.orgID = @ownedByOrgID
			and b.batchCode = @batchCode
			and b.statusID = 1
			and b.isSystemCreated = 1
		IF @allocBatchID is null 
			EXEC dbo.tr_createBatch @orgID=@ownedByOrgID, @payProfileID=@payProfileID, @batchCode=@batchCode, 
				@batchName=@batchName, @controlAmt=0, @controlCount=0, @depositDate=@transactionDate, 
				@isSystemCreated=1, @createdByMemberID=null, @batchID=@allocBatchID OUTPUT 
	END
	INSERT INTO dbo.tr_batchTransactions (batchID, transactionID)
	VALUES (@allocBatchID, @transactionID)

	-- update payment cache	
	declare @refundableAmount money, @allocatedAmount money
	select @refundableAmount = refundableAmount from dbo.fn_tr_getRefundableAmountofPayment(@paymentTransactionID,null)
	select @allocatedAmount = allocatedAmount from dbo.fn_tr_getAllocatedAmountofPayment(@paymentTransactionID,null)

	update dbo.tr_transactionPayments
	set cache_allocatedAmountOfPayment = @allocatedAmount,
		cache_refundableAmountOfPayment = @refundableAmount
	where transactionID = @paymentTransactionID

	-- update sales cache
	-- if allocating to an adjustment, update cache of sale or tax it is adjusting
	declare @stTypeID int, @stID int, @activePaymentAllocatedAmount money, @pendingPaymentAllocatedAmount money
	select @stTypeID = typeID from dbo.tr_transactions where transactionID = @saleTransactionID 
	IF @stTypeID in (1,7,10)
		select @stID = @saleTransactionID
	ELSE
		select @stID = tSale.transactionID
			from dbo.tr_transactions as tSale
			inner join dbo.tr_relationships as tR on tR.appliedToTransactionID = tSale.transactionID and tR.transactionID = @saleTransactionID
			inner join dbo.tr_relationshipTypes as trt on trt.typeID = tR.typeID and trt.type = 'AdjustTrans'

	select @activePaymentAllocatedAmount = activePaymentAllocatedAmount,
		@pendingPaymentAllocatedAmount = pendingPaymentAllocatedAmount
	from dbo.fn_tr_getAllocatedAmountofSale(@stID)

	UPDATE dbo.tr_transactionSales
	SET cache_activePaymentAllocatedAmount = @activePaymentAllocatedAmount,
		cache_pendingPaymentAllocatedAmount = @pendingPaymentAllocatedAmount
	WHERE transactionID = @stID

	-- update invoiceTransactions cache
	select @activePaymentAllocatedAmount = null, @pendingPaymentAllocatedAmount = null
	select @activePaymentAllocatedAmount = activePaymentAllocatedAmount,
		@pendingPaymentAllocatedAmount = pendingPaymentAllocatedAmount
	from dbo.fn_tr_getAllocatedAmountofSaleOrAdj(@saleTransactionID,null)
	
	UPDATE dbo.tr_invoiceTransactions
	SET cache_activePaymentAllocatedAmount = @activePaymentAllocatedAmount,
		cache_pendingPaymentAllocatedAmount = @pendingPaymentAllocatedAmount
	WHERE transactionID = @saleTransactionID

	-- check the in-bound rules.
	-- sale - new cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount must be between 0 and cache_amountAfterAdjustment
	-- payment - new cache_allocatedAmountOfPayment must be between 0 and cache_refundableAmountOfPayment
	IF NOT EXISTS (select saleID from dbo.tr_transactionSales where transactionID = @stID and cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount between 0 and cache_amountAfterAdjustment)
		OR NOT EXISTS (select itID from dbo.tr_invoiceTransactions where transactionID = @saleTransactionID and cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount between 0 and cache_invoiceAmountAfterAdjustment)
		OR NOT EXISTS (select paymentID from dbo.tr_transactionPayments where transactionID = @paymentTransactionID and cache_allocatedAmountOfPayment between 0 and cache_refundableAmountOfPayment)
		RAISERROR('in-bounds checking failed', 16, 1);

	-- cleanup invoice
	-- if invoice is closed and is now fully paid with active payments, mark it as paid
	-- if invoice is paid and is now not fully paid with active payments, mark it as closed
	declare @amtDueNoPendingOnInvoice money
	select @invoiceID=i.invoiceID, @invstatus=ins.status
		from dbo.tr_invoices as i
		inner join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
		inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
		where it.transactionID = @saleTransactionID
	select @amtDueNoPendingOnInvoice = sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount)
		from dbo.tr_invoiceTransactions as it
		inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
		where it.invoiceID = @invoiceID
		and t.statusID <> 2
	IF @invoiceID is not null and @invstatus = 'closed' and @amtDueNoPendingOnInvoice = 0 BEGIN
		update dbo.tr_invoices
		set statusID = 4, payProfileID = null
		where invoiceID = @invoiceID
		
		insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
		values (@invoiceID, getdate(), 4, 3, @recordedByMemberID)
	END
	IF @invoiceID is not null and @invstatus = 'paid' and @amtDueNoPendingOnInvoice > 0 BEGIN
		update dbo.tr_invoices
		set statusID = 3
		where invoiceID = @invoiceID

		insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
		values (@invoiceID, getdate(), 3, 4, @recordedByMemberID)
	END

	-- update credit balances
	EXEC dbo.tr_updateCreditBalanceByMember @memberID=@assignedToMemberID

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @transactionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

