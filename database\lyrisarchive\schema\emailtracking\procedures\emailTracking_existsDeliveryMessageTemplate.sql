ALTER PROC dbo.emailTracking_existsDeliveryMessageTemplate
@statusMessageTemplate VARCHAR(1000),
@messageTemplateSHA1 varchar(42),
@exists bit OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF EXISTS (SELECT 1 FROM dbo.deliveryMessageTemplates WHERE messageTemplateSHA1 = @MessageTemplateSHA1)
		set @exists=1;
	ELSE 
		set @exists=0;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO
