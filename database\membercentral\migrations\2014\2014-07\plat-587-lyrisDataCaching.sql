USE [dataTransfer]
GO
CREATE TABLE [dbo].[cache_lyris_IntegratedMembers](
	[memberID_] [int] NOT NULL,
	[orgcode] [varchar](5) NOT NULL,
	[externalMemberID] [varchar](100) NULL,
	[dateJoined_] [smalldatetime] NULL,
	[expireDate_] [smalldatetime] NULL,
	[list_] [varchar](60) NULL,
	[membertype_] [varchar](20) NULL,
	[subtype_] [varchar](20) NULL,
	[mcoption_lockAddress] [bit] NULL,
	[mcoption_keepactive] [bit] NULL,
 CONSTRAINT [PK_cache_lyris_IntegratedMembers] PRIMARY KEY CLUSTERED 
(
	[memberID_] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO


CREATE PROC [dbo].cache_lyris_updateIntegratedMembers
	@restrictToOrgcode varchar(5) = null
AS

IF OBJECT_ID('tempdb..#IntegratedMembers') IS NOT NULL
	DROP TABLE #IntegratedMembers

IF OBJECT_ID('tempdb..#IntegratedMembers_updated') IS NOT NULL
	DROP TABLE #IntegratedMembers_updated

IF OBJECT_ID('tempdb..#IntegratedMembers_removed') IS NOT NULL
	DROP TABLE #IntegratedMembers_removed

IF OBJECT_ID('tempdb..#orgcodes') IS NOT NULL
	DROP TABLE #orgcodes

IF OBJECT_ID('tempdb..#affectedMembers') IS NOT NULL
	DROP TABLE #affectedMembers


CREATE TABLE #IntegratedMembers(
	memberID_ int PRIMARY KEY, 
	orgcode varchar(5), 
	externalMemberID varchar(100), 
	dateJoined_ smalldatetime, 
	expireDate_ smalldatetime, 
	list_ varchar(60), 
	membertype_ varchar(20), 
	subtype_ varchar(20), 
	mcoption_lockAddress bit, 
	mcoption_keepactive bit
)

CREATE TABLE #IntegratedMembers_updated(
	memberID_ int PRIMARY KEY,
	orgcode varchar(5), 
	externalMemberID varchar(100), 
	dateJoined_ smalldatetime, 
	expireDate_ smalldatetime, 
	list_ varchar(60), 
	membertype_ varchar(20), 
	subtype_ varchar(20), 
	mcoption_lockAddress bit, 
	mcoption_keepactive bit
)

CREATE TABLE #IntegratedMembers_removed(
	memberID_ int PRIMARY KEY, 
	orgcode varchar(5), 
	externalMemberID varchar(100), 
	dateJoined_ smalldatetime, 
	expireDate_ smalldatetime, 
	list_ varchar(60), 
	membertype_ varchar(20), 
	subtype_ varchar(20), 
	mcoption_lockAddress bit, 
	mcoption_keepactive bit
)

CREATE TABLE #orgcodes(
	orgcode varchar(5)
)

CREATE TABLE #affectedMembers(
	orgcode varchar(5), 
	externalMemberID varchar(100), 
	list_ varchar(60)
)

insert into #orgcodes (orgcode)
select state
from tlasites.trialsmith.dbo.depotla tla
where tla.state = isnull(@restrictToOrgcode,tla.state) collate database_default 


insert into #IntegratedMembers (memberID_, orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive)
select memberID_, lf.orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive
from lyris.trialslyris1.dbo.members_ m
inner join lyris.trialslyris1.dbo.lists_format lf
	on m.list_ = lf.name
	and m.externalMemberID is not null
inner join #orgcodes o
	on o.orgcode = lf.orgcode collate database_default

insert into #IntegratedMembers_updated (memberID_, orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive)
select memberID_, orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive
from #IntegratedMembers
except
select memberID_, cache.orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive
from dbo.[cache_lyris_IntegratedMembers] cache
inner join #orgcodes o
	on o.orgcode = cache.orgcode



insert into #IntegratedMembers_removed (memberID_, orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive)
select memberID_, cache.orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive
from dbo.[cache_lyris_IntegratedMembers] cache
inner join #orgcodes o
	on o.orgcode = cache.orgcode
except
select memberID_, orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive
from #IntegratedMembers


insert into #affectedMembers (orgcode, externalMemberID, list_)
select distinct orgcode, externalMemberID, list_
from #IntegratedMembers_removed
union
select distinct orgcode, externalMemberID, list_
from #IntegratedMembers_updated



delete cache
from dbo.[cache_lyris_IntegratedMembers] cache
inner join #IntegratedMembers_removed removed
	on removed.memberID_ = cache.memberID_

insert into dbo.[cache_lyris_IntegratedMembers] (memberID_, orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive)
select memberID_, orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive
from #IntegratedMembers_updated


IF OBJECT_ID('tempdb..#IntegratedMembers') IS NOT NULL
	DROP TABLE #IntegratedMembers

IF OBJECT_ID('tempdb..#IntegratedMembers_updated') IS NOT NULL
	DROP TABLE #IntegratedMembers_updated

IF OBJECT_ID('tempdb..#IntegratedMembers_removed') IS NOT NULL
	DROP TABLE #IntegratedMembers_removed

IF OBJECT_ID('tempdb..#orgcodes') IS NOT NULL
	DROP TABLE #orgcodes

IF OBJECT_ID('tempdb..#affectedMembers') IS NOT NULL
	DROP TABLE #affectedMembers


GO


use membercentral
go
insert into dbo.ams_virtualGroupConditionKeys (conditionKey) values ('listList')
insert into dbo.ams_virtualGroupConditionKeys (conditionKey) values ('listMemberType')
insert into dbo.ams_virtualGroupConditionKeys (conditionKey) values ('listSubType')
insert into dbo.ams_virtualGroupConditionKeys (conditionKey) values ('listLockAddress')
insert into dbo.ams_virtualGroupConditionKeys (conditionKey) values ('listKeepActive')
insert into dbo.ams_virtualGroupConditionKeys (conditionKey) values ('listJoinDateLower')
insert into dbo.ams_virtualGroupConditionKeys (conditionKey) values ('listJoinDateUpper')
insert into dbo.ams_virtualGroupConditionKeys (conditionKey) values ('listExpireDateLower')
insert into dbo.ams_virtualGroupConditionKeys (conditionKey) values ('listExpireDateUpper')
GO

ALTER FUNCTION [dbo].[ams_getVirtualGroupConditionVerbose] (@conditionID int)
RETURNS varchar(max)
AS
BEGIN

	DECLARE @fieldCode varchar(40), @orgid int, @verbose varchar(max), @hasVerbose bit, 
		@expression varchar(20), @fieldLabel varchar(max), @displayTypeCode varchar(20),
		@dataTypeCode varchar(20)
	set @hasVerbose = 0

	select @fieldCode=c.fieldCode, @orgID=c.orgID, @expression=e.expression, 
		@fieldLabel=fields.fieldLabel, @displayTypeCode=fields.displayTypeCode,
		@dataTypeCode=fields.dataTypeCode
		from dbo.ams_virtualGroupConditions as c
		inner join dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		cross apply dbo.fn_ams_getVirtualGroupConditionVerboseFields(c.orgID,c.fieldCode) as fields
		where c.conditionID = @conditionID

	-- events
	IF @hasVerbose = 0 and left(@fieldCode,2) = 'e_' BEGIN
		select top 1 @verbose = e.expressionVerbose + ' ' + @fieldLabel
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		where c.conditionID = @conditionID
		
		set @hasVerbose = 1
	END

	-- accounting
	IF @hasVerbose = 0 and @fieldcode = 'acct_allocsum' BEGIN
		select top 1 @verbose = case
			when @expression in ('exists','not_exists') then
				case when batchDateLower.val = batchDateUpper.val then 'Allocations ' + case when @expression = 'exists' then 'exist' else 'do not exist' end + ' on ' + batchDateLower.val + ' with members linked to ' + linkAllocType.val + ' for ' + LEFT(gllist.list, LEN(gllist.list)-1)
				else 'Allocations ' + case when @expression = 'exists' then 'exist' else 'do not exist' end + ' between ' + batchDateLower.val + ' and ' + batchDateUpper.val + ' with members linked to ' + linkAllocType.val + ' for ' + LEFT(gllist.list, LEN(gllist.list)-1)
				end
			when @expression = 'between' then
				case when batchDateLower.val = batchDateUpper.val then 'Allocations on ' + batchDateLower.val + ' with members linked to ' + linkAllocType.val + ' ' + e.expressionVerbose + ' ' + valueLower.val + ' and ' + valueUpper.val + ' for ' + LEFT(gllist.list, LEN(gllist.list)-1)
				else 'Allocations between ' + batchDateLower.val + ' and ' + batchDateUpper.val + ' with members linked to ' + linkAllocType.val + ' ' + e.expressionVerbose + ' ' + valueLower.val + ' and ' + valueUpper.val + ' for ' + LEFT(gllist.list, LEN(gllist.list)-1)
				end
			else 
				case when batchDateLower.val = batchDateUpper.val then 'Allocations on ' + batchDateLower.val + ' with members linked to ' + linkAllocType.val + ' ' + e.expressionVerbose + ' ' + value.val + ' for ' + LEFT(gllist.list, LEN(gllist.list)-1)
				else 'Allocations between ' + batchDateLower.val + ' and ' + batchDateUpper.val + ' with members linked to ' + linkAllocType.val + ' ' + e.expressionVerbose + ' ' + value.val + ' for ' + LEFT(gllist.list, LEN(gllist.list)-1)
				end
			end
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		CROSS APPLY ( 
			SELECT gl.thePathExpanded + ', ' AS [text()] 
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'revenueGL'
			inner join dbo.fn_getRecursiveGLAccounts(@orgID) as gl on cast(gl.GLAccountID as varchar(10)) = cv.conditionValue
			WHERE cv.conditionID = c.conditionID
			ORDER BY gl.thePath
			FOR XML PATH('')
		) as gllist(list) 
		CROSS APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'batchDateLower'
			WHERE cv.conditionID = c.conditionID
		) as batchDateLower(val)
		CROSS APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'batchDateUpper'
			WHERE cv.conditionID = c.conditionID
		) as batchDateUpper(val)
		CROSS APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'linkAllocType'
			WHERE cv.conditionID = c.conditionID
		) as linkAllocType(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'value'
			WHERE cv.conditionID = c.conditionID
		) as value(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'valueLower'
			WHERE cv.conditionID = c.conditionID
		) as valueLower(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'valueUpper'
			WHERE cv.conditionID = c.conditionID
		) as valueUpper(val)
		where c.conditionID = @conditionID

		set @hasVerbose = 1
	END

	-- record types
	IF @hasVerbose = 0 and @fieldcode = 'rt_role' BEGIN
		select top 1 @verbose = 'Record Type of ' + rt.recordTypeName + 
			case 
			when len(isnull(rolelist.list,'')) = 0 then ''
			else ' with linked role of ' + LEFT(rolelist.list, LEN(rolelist.list)-1)
			end
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		CROSS APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'recordType'
			WHERE cv.conditionID = c.conditionID
		) as recordType(val)
		INNER JOIN dbo.ams_recordTypes as rt on rt.recordTypeID = cast(recordType.val as int)
		OUTER APPLY ( 
			select rrt.relationshipTypeName + ' to ' + coalesce(masterLinkingRT.recordTypeName,childLinkingRT.recordTypeName) + ', ' AS [text()] 
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'role'
			inner join dbo.ams_recordTypesRelationshipTypes as rtrt on cast(rtrt.recordTypeRelationshipTypeID as varchar(10)) = cv.conditionValue
			inner join dbo.ams_recordRelationshipTypes as rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID
			inner join dbo.ams_recordTypes as rt1 on rt1.recordTypeID = rt.recordTypeID
			left outer join dbo.ams_recordTypes as masterLinkingRT on rtrt.masterRecordTypeID = masterLinkingRT.recordTypeID and rt1.isPerson = 1
			left outer join dbo.ams_recordTypes as childLinkingRT on rtrt.childRecordTypeID = childLinkingRT.recordTypeID and rt1.isPerson = 0
			WHERE cv.conditionID = c.conditionID
			ORDER BY rrt.relationshipTypeName
			FOR XML PATH('')
		) as rolelist(list) 
		where c.conditionID = @conditionID

		set @hasVerbose = 1
	END

	-- group membership
	IF @hasVerbose = 0 and left(@fieldcode,4) = 'grp_' BEGIN
		set @verbose = 'Member of ' + @fieldLabel
		set @hasVerbose = 1
	END

	-- subscriptions
	IF @hasVerbose = 0 and left(@fieldcode,4) = 'sub_' BEGIN
		select top 1 @verbose = 'Has Subscription ' + @fieldLabel + 
			case when len(isnull(subStartDateLower.val,'')) > 0 or len(isnull(subStartDateUpper.val,'')) > 0 or len(isnull(subEndDateLower.val,'')) > 0 or len(isnull(subEndDateUpper.val,'')) > 0 or len(isnull(subGraceDateLower.val,'')) > 0 or len(isnull(subGraceDateUpper.val,'')) > 0 then ' having ' else '' end + 
			case 
			when len(isnull(subStartDateLower.val,'')) > 0 and len(isnull(subStartDateUpper.val,'')) > 0 then 'subscription start date between ' + subStartDateLower.val + ' and ' + subStartDateUpper.val
			when len(isnull(subStartDateLower.val,'')) > 0 then 'subscription start date after ' + subStartDateLower.val
			when len(isnull(subStartDateUpper.val,'')) > 0 then 'subscription start date before ' + subStartDateUpper.val
			else ''
			end +
			case when (len(isnull(subStartDateLower.val,'')) > 0 or len(isnull(subStartDateUpper.val,'')) > 0) and (len(isnull(subEndDateLower.val,'')) > 0 or len(isnull(subEndDateUpper.val,'')) > 0) then ' and ' else '' end +
			case 
			when len(isnull(subEndDateLower.val,'')) > 0 and len(isnull(subEndDateUpper.val,'')) > 0 then 'subscription end date between ' + subEndDateLower.val + ' and ' + subEndDateUpper.val
			when len(isnull(subEndDateLower.val,'')) > 0 then 'subscription end date after ' + subEndDateLower.val
			when len(isnull(subEndDateUpper.val,'')) > 0 then 'subscription end date before ' + subEndDateUpper.val
			else ''
			end +
			case when (len(isnull(subStartDateLower.val,'')) > 0 or len(isnull(subStartDateUpper.val,'')) > 0 or len(isnull(subEndDateLower.val,'')) > 0 or len(isnull(subEndDateUpper.val,'')) > 0) and (len(isnull(subGraceDateLower.val,'')) > 0 or len(isnull(subGraceDateUpper.val,'')) > 0) then ' and ' else '' end +
			case 
			when len(isnull(subGraceDateLower.val,'')) > 0 and len(isnull(subGraceDateUpper.val,'')) > 0 then 'subscription grace date between ' + subGraceDateLower.val + ' and ' + subGraceDateUpper.val
			when len(isnull(subGraceDateLower.val,'')) > 0 then 'subscription grace date after ' + subGraceDateLower.val
			when len(isnull(subGraceDateUpper.val,'')) > 0 then 'subscription grace date before ' + subGraceDateUpper.val
			else ''
			end
		from dbo.ams_virtualGroupConditions as c
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subStartDateLower'
			WHERE cv.conditionID = c.conditionID
		) as subStartDateLower(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subStartDateUpper'
			WHERE cv.conditionID = c.conditionID
		) as subStartDateUpper(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subEndDateLower'
			WHERE cv.conditionID = c.conditionID
		) as subEndDateLower(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subEndDateUpper'
			WHERE cv.conditionID = c.conditionID
		) as subEndDateUpper(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subGraceDateLower'
			WHERE cv.conditionID = c.conditionID
		) as subGraceDateLower(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subGraceDateUpper'
			WHERE cv.conditionID = c.conditionID
		) as subGraceDateUpper(val)
		where c.conditionID = @conditionID

		set @hasVerbose = 1
	END

	-- member history
	IF @hasVerbose = 0 and left(@fieldCode,3) = 'mh_'  BEGIN
		select top 1 @verbose = 'Has Member History Entry in ' + historyCategory.val +
			case when len(isnull(historySubCategory.val,'')) > 0 then ' and ' + historySubCategory.val else '' end +
			case 
				when len(isnull(historyDateLower.val,'')) > 0 and len(isnull(historyDateUpper.val,'')) > 0 then '; dated between ' + historyDateLower.val + ' and ' + historyDateUpper.val
				when len(isnull(historyDateLower.val,'')) > 0 then '; dated after ' + historyDateLower.val
				when len(isnull(historyDateUpper.val,'')) > 0 then '; dated before ' + historyDateUpper.val
			else ''
			end +
			case 
				when len(isnull(historyEnteredDateLower.val,'')) > 0 and len(isnull(historyEnteredDateUpper.val,'')) > 0 then '; entered between ' + historyEnteredDateLower.val + ' and ' + historyEnteredDateUpper.val
				when len(isnull(historyEnteredDateLower.val,'')) > 0 then '; entered after ' + historyEnteredDateLower.val
				when len(isnull(historyEnteredDateUpper.val,'')) > 0 then '; entered before ' + historyEnteredDateUpper.val
			else ''
			end +
			case 
			when len(isnull(historyQuantityLower.val,'')) > 0 and len(isnull(historyQuantityUpper.val,'')) > 0 then '; quantity between ' + historyQuantityLower.val + ' and ' + historyQuantityUpper.val
			when len(isnull(historyQuantityLower.val,'')) > 0 then '; quantity greater than or equal to ' + historyQuantityLower.val
			when len(isnull(historyQuantityUpper.val,'')) > 0 then '; quantity less than or equal to ' + historyQuantityUpper.val
			else ''
			end +
			case 
			when len(isnull(historyAmountLower.val,'')) > 0 and len(isnull(historyAmountUpper.val,'')) > 0 then '; amount between $' + historyAmountLower.val + ' and $' + historyAmountUpper.val
			when len(isnull(historyAmountLower.val,'')) > 0 then '; amount greater than or equal to $' + historyAmountLower.val
			when len(isnull(historyAmountUpper.val,'')) > 0 then '; amount less than or equal to $' + historyAmountUpper.val
			else ''
			end +
			case when len(isnull(historyDescriptionContains.val,'')) > 0 then '; description contains ' + historyDescriptionContains.val else '' end			
		from dbo.ams_virtualGroupConditions as c
		OUTER APPLY (
			SELECT cat.categoryName as conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyCategory'
			inner join cms_categories cat
				on cat.categoryID = cast(cv.conditionValue as int)
			WHERE cv.conditionID = c.conditionID
		) as historyCategory(val)
		OUTER APPLY (
			SELECT replace(dbo.pipelist(cat.categoryName),'|',' or ') as conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historySubCategory'
			inner join cms_categories cat
				on cat.categoryID = cast(cv.conditionValue as int)
			WHERE cv.conditionID = c.conditionID
			group by k.conditionKey
		) as historySubCategory(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyDateLower'
			WHERE cv.conditionID = c.conditionID
		) as historyDateLower(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyDateUpper'
			WHERE cv.conditionID = c.conditionID
		) as historyDateUpper(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyEnteredDateLower'
			WHERE cv.conditionID = c.conditionID
		) as historyEnteredDateLower(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyEnteredDateUpper'
			WHERE cv.conditionID = c.conditionID
		) as historyEnteredDateUpper(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyQuantityLower'
			WHERE cv.conditionID = c.conditionID
		) as historyQuantityLower(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyQuantityUpper'
			WHERE cv.conditionID = c.conditionID
		) as historyQuantityUpper(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyAmountLower'
			WHERE cv.conditionID = c.conditionID
		) as historyAmountLower(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyAmountUpper'
			WHERE cv.conditionID = c.conditionID
		) as historyAmountUpper(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyDescriptionContains'
			WHERE cv.conditionID = c.conditionID
		) as historyDescriptionContains(val)
		where c.conditionID = @conditionID

		set @hasVerbose = 1
	END

	-- exists/not exists
	IF @hasVerbose = 0 and @expression in ('exists','not_exists') BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + e.expressionVerbose
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		where c.conditionID = @conditionID

		set @hasVerbose = 1
	END
	
	-- membertypeID (multi-select not allowed)
	IF @hasVerbose = 0 and @fieldCode = 'm_membertypeid' BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + e.expressionVerbose + ' ' + mt.memberType
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
		INNER JOIN dbo.ams_memberTypes as mt on mt.memberTypeID = cv.conditionValue
		where c.conditionID = @conditionID
		and cvk.conditionKey = 'value'

		set @hasVerbose = 1
	END

	-- stateprov (multi-select allowed)
	IF @hasVerbose = 0 and left(@fieldCode,3) = 'ma_' and right(@fieldCode,10) = '_stateprov' BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + e.expressionVerbose + ' ' + replace(replace(dbo.PipeList(replace(s.Name,'|',char(7))),'|',' OR '),char(7),'|')
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
		INNER JOIN dbo.ams_states as s on cast(s.stateID as varchar(10)) = cv.conditionValue
		where c.conditionID = @conditionID
		and cvk.conditionKey = 'value'
		group by e.expressionVerbose

		set @hasVerbose = 1
	END

	-- country (multi-select allowed)
	IF @hasVerbose = 0 and left(@fieldCode,3) = 'ma_' and right(@fieldCode,8) = '_country' BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + e.expressionVerbose + ' ' + replace(replace(dbo.PipeList(replace(country.country,'|',char(7))),'|',' OR '),char(7),'|')
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
		INNER JOIN dbo.ams_countries as country on cast(country.countryID as varchar(10)) = cv.conditionValue
		where c.conditionID = @conditionID
		and cvk.conditionKey = 'value'
		group by e.expressionVerbose

		set @hasVerbose = 1
	END

	-- districting data (multi-select allowed)
	IF @hasVerbose = 0 and left(@fieldCode,4) = 'mad_' BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + e.expressionVerbose + ' ' + replace(replace(dbo.PipeList(replace(dv.vendorvalue,'|',char(7))),'|',' OR '),char(7),'|')
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
		INNER JOIN dbo.ams_memberDistrictValues as dv on cast(dv.valueid as varchar(10)) = cv.ConditionValue
		where c.conditionID = @conditionID
		and cvk.conditionKey = 'value'
		group by e.expressionVerbose

		set @hasVerbose = 1
	END

	-- prof license status (multi-select allowed)
	IF @hasVerbose = 0 and left(@fieldCode,4) = 'mpl_' and right(@fieldCode,7) = '_status' BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + e.expressionVerbose + ' ' + replace(replace(dbo.PipeList(replace(cv.conditionValue,'|',char(7))),'|',' OR '),char(7),'|')
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
		where c.conditionID = @conditionID
		and cvk.conditionKey = 'value'
		group by e.expressionVerbose

		set @hasVerbose = 1
	END

	-- datediff (multi-select not allowed)
	IF @hasVerbose = 0 and @expression = 'datediff' BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + de.expressionVerbose + ' ' + cast(abs(cv.conditionValue) as varchar(10)) + ' ' + 
			case c.[datepart]
			when 'yy' then 'year'
			when 'q' then 'quarter'
			when 'm' then 'month'
			when 'w' then 'week'
			when 'dw' then 'weekday'
			when 'd' then 'day'
			when 'dy' then 'day of year'
			else 'unknown'
			end + 
			case when cv.conditionValue not in (1,-1) then 's' else '' end + 
			case when cv.conditionValue > 0 then ' in the past' else ' in the future' end
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS de ON c.dateexpressionID = de.expressionID
		INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
		where c.conditionID = @conditionID
		and cvk.conditionKey = 'value'

		set @hasVerbose = 1
	END

	-- datepart (multi-select allowed)
	IF @hasVerbose = 0 and @expression = 'datepart' BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + 
			case c.[datepart]
			when 'yy' then 'year'
			when 'q' then 'quarter'
			when 'm' then 'month'
			when 'w' then 'week'
			when 'dw' then 'weekday'
			when 'd' then 'day'
			when 'dy' then 'day of year'
			else 'unknown'
			end + ' ' + de.expressionVerbose + ' ' + replace(replace(dbo.PipeList(replace(cv.conditionValue,'|',char(7))),'|',' OR '),char(7),'|')
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS de ON c.dateexpressionID = de.expressionID
		INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
		where c.conditionID = @conditionID
		and cvk.conditionKey = 'value'
		group by c.[datepart], de.expressionVerbose

		set @hasVerbose = 1
	END

	-- memberdata SELECT/RADIO - BIT (multi-select not allowed)
	IF @hasVerbose = 0 and left(@fieldCode,3) = 'md_' and @displayTypeCode in ('SELECT','RADIO') and @dataTypeCode = 'BIT' BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + e.expressionVerbose + ' ' + case when cv.ConditionValue = 1 then 'YES' else 'NO' end
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
		where c.conditionID = @conditionID
		and cvk.conditionKey = 'value'

		set @hasVerbose = 1
	END

	-- memberdata SELECT/RADIO (multi-select allowed)
	IF @hasVerbose = 0 and left(@fieldCode,3) = 'md_' and @displayTypeCode in ('SELECT','RADIO') BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + tmp.expressionVerbose + ' ' + replace(replace(dbo.PipeList(replace(tmp.valueVerbose,'|',char(7))),'|',' OR '),char(7),'|')
		from (
			select e.expressionVerbose, 
				valueVerbose = case 
					when @dataTypeCode = 'STRING' then mdcv.columnValueString
					when @dataTypeCode = 'DECIMAL2' then cast(mdcv.columnValueDecimal2 as varchar(max))
					when @dataTypeCode = 'INTEGER' then cast(mdcv.columnValueInteger as varchar(max))
					when @dataTypeCode = 'DATE' then convert(varchar(10),mdcv.columnValueDate,101)
					else cv.conditionValue
				end
			from dbo.ams_virtualGroupConditions as c
			INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
			INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
			INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
			INNER JOIN dbo.ams_memberDataColumnValues as mdcv on cast(mdcv.valueid as varchar(10)) = cv.ConditionValue
			where c.conditionID = @conditionID
			and cvk.conditionKey = 'value'
		) as tmp
		group by tmp.expressionVerbose

		set @hasVerbose = 1
	END

	-- lists
	IF @hasVerbose = 0 and left(@fieldCode,2) = 'l_'  BEGIN
		select top 1 @verbose = 'Member of ' + LEFT(listlist.list, LEN(listlist.list)-1) + ' with member type ' + LEFT(mtlist.list, LEN(mtlist.list)-1) + 
			+ ' with sub type ' + LEFT(stlist.list, LEN(stlist.list)-1) + 
			case 
			when isnull(listLockAddress.val,'-1') = 0 then  '; locked address equals no'
			when isnull(listLockAddress.val,'-1') = 1 then  '; locked address equals yes'
			else ''
			end +
			case 
			when isnull(listKeepActive.val,'-1') = 0 then  '; keep active equals no'
			when isnull(listKeepActive.val,'-1') = 1 then  '; keep active equals yes'
			else ''
			end +
			case 
				when len(isnull(listJoinDateLower.val,'')) > 0 and len(isnull(listJoinDateUpper.val,'')) > 0 then '; joined between ' + listJoinDateLower.val + ' and ' + listJoinDateUpper.val
				when len(isnull(listJoinDateLower.val,'')) > 0 then '; joined after ' + listJoinDateLower.val
				when len(isnull(listJoinDateUpper.val,'')) > 0 then '; joined before ' + listJoinDateUpper.val
			else ''
			end +
			case 
				when len(isnull(listExpireDateLower.val,'')) > 0 and len(isnull(listExpireDateUpper.val,'')) > 0 then '; expired between ' + listExpireDateLower.val + ' and ' + listExpireDateUpper.val
				when len(isnull(listExpireDateLower.val,'')) > 0 then '; expired after ' + listExpireDateLower.val
				when len(isnull(listExpireDateUpper.val,'')) > 0 then '; expired before ' + listExpireDateUpper.val
			else ''
			end			
		from dbo.ams_virtualGroupConditions as c
		CROSS APPLY ( 
			SELECT cv.conditionValue + ', ' AS [text()] 
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listList'
			WHERE cv.conditionID = c.conditionID
			ORDER BY cv.conditionValue
			FOR XML PATH('')
		) as listlist(list) 
		CROSS APPLY ( 
			SELECT cv.conditionValue + ', ' AS [text()] 
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listMemberType'
			WHERE cv.conditionID = c.conditionID
			ORDER BY cv.conditionValue
			FOR XML PATH('')
		) as mtlist(list) 
		CROSS APPLY ( 
			SELECT cv.conditionValue + ', ' AS [text()] 
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listSubType'
			WHERE cv.conditionID = c.conditionID
			ORDER BY cv.conditionValue
			FOR XML PATH('')
		) as stlist(list) 
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listLockAddress'
			WHERE cv.conditionID = c.conditionID
		) as listLockAddress(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listKeepActive'
			WHERE cv.conditionID = c.conditionID
		) as listKeepActive(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listJoinDateLower'
			WHERE cv.conditionID = c.conditionID
		) as listJoinDateLower(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listJoinDateUpper'
			WHERE cv.conditionID = c.conditionID
		) as listJoinDateUpper(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listExpireDateLower'
			WHERE cv.conditionID = c.conditionID
		) as listExpireDateLower(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listExpireDateUpper'
			WHERE cv.conditionID = c.conditionID
		) as listExpireDateUpper(val)
		where c.conditionID = @conditionID

		set @hasVerbose = 1
	END

	-- All others
	IF @hasVerbose = 0 BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + e.expressionVerbose + ' ' + replace(replace(dbo.PipeList(replace(cv.conditionValue,'|',char(7))),'|',' OR '),char(7),'|')
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
		where c.conditionID = @conditionID
		and cvk.conditionKey = 'value'
		group by e.expressionVerbose

		set @hasVerbose = 1
	END

	RETURN @verbose
END
GO

ALTER FUNCTION [dbo].[fn_ams_getVirtualGroupConditionVerboseFields] (
	@orgID int,
	@fieldcode varchar(40)
)
RETURNS @fields TABLE (
	fieldCode varchar(40), 
	fieldCodeArea varchar(40), 
	fieldLabel varchar(max), 
	displayTypeCode varchar(20), 
	dataTypeCode varchar(20)
)
AS
BEGIN

	DECLARE @subT varchar(100), @subS varchar(300), @subR varchar(403), @subSS varchar(50), @subPS varchar(50)

	IF left(@fieldCode,2) = 'm_'
		insert into @fields (fieldCode, fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode)
		select fieldCode, 'Member Data' as fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode
		from dbo.fn_ams_getConditionFields_member(@orgID)
		where fieldcode = @fieldCode

	IF left(@fieldCode,3) = 'md_'
		insert into @fields (fieldCode, fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode)
		select 'md_' + cast(mdc.columnID as varchar(10)) as fieldCode, 'Custom Fields' as fieldCodeArea, mdc.columnName as fieldLabel, dt.displayTypeCode, ddt.dataTypeCode
		from dbo.ams_memberdatacolumns as mdc
		inner join dbo.ams_memberDataColumnDisplayTypes as dt on dt.displayTypeID = mdc.displayTypeID
		inner join dbo.ams_memberDataColumnDataTypes as ddt on ddt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.columnID = parsename(replace(@fieldcode,'_','.'),1)

	IF left(@fieldCode,3) = 'ma_' or left(@fieldCode,3) = 'mp_' or left(@fieldCode,4) = 'mad_'
		insert into @fields (fieldCode, fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode)
		select af.fieldCode, 'Addresses' as fieldCodeArea, at.addressType + ' ' + af.fieldLabel as fieldLabel, af.displayTypeCode, af.dataTypeCode
		from dbo.ams_memberAddressTypes as at
		cross apply dbo.fn_ams_getConditionFields_addrfields(at.orgid,at.addressTypeID) as af
		where at.orgID = @orgID
		and af.fieldcode = @fieldCode

	IF left(@fieldCode,3) = 'me_'
		insert into @fields (fieldCode, fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode)
		select fieldCode, 'Emails' as fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode
		from dbo.fn_ams_getConditionFields_emailtypes(@orgID)
		where fieldcode = @fieldCode

	IF left(@fieldCode,3) = 'mw_'
		insert into @fields (fieldCode, fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode)
		select fieldCode, 'Websites' as fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode
		from dbo.fn_ams_getConditionFields_websitetypes(@orgID)
		where fieldcode = @fieldCode

	IF left(@fieldCode,2) = 'e_'
		insert into @fields (fieldCode, fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode)
		select 'e_' + cast(e.eventID as varchar(10)) as fieldCode, 'Events' as fieldCodeArea, isnull(eventContent.contentTitle,'<no title>') as fieldLabel, 'SELECT', 'STRING'
		from dbo.ev_events as e
		cross apply dbo.fn_getContent(e.eventContentID,1) as eventContent
		where e.eventID = parsename(replace(@fieldcode,'_','.'),1)

	IF @fieldCode = 'acct_allocsum'
		insert into @fields (fieldCode, fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode)
		VALUES ('acct_allocsum','Accounting','Allocation Summary','TEXTBOX','DECIMAL2')

	IF @fieldCode = 'rt_role'
		insert into @fields (fieldCode, fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode)
		VALUES ('rt_role','Record Types','Linked Roles','SELECT','STRING')

	IF left(@fieldcode,4) = 'mpl_'
		insert into @fields (fieldCode, fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode)
		select 'mpl_' + cast(PLTypeID as varchar(10)) + '_licenseNumber' as fieldCode, 'Professional Licenses' as fieldCodeArea, PLName + ' License Number' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from dbo.ams_memberProfessionalLicenseTypes
		where orgID = @orgID
		and PLTypeID = parsename(replace(@fieldcode,'_','.'),2)
		and parsename(replace(@fieldcode,'_','.'),1) = 'licenseNumber'
			union all
		select 'mpl_' + cast(PLTypeID as varchar(10)) + '_activeDate' as fieldCode, 'Professional Licenses' as fieldCodeArea, PLName + ' Active Date' as fieldLabel, 'DATE' as displayTypeCode, 'DATE' as dataTypeCode
		from dbo.ams_memberProfessionalLicenseTypes
		where orgID = @orgID
		and PLTypeID = parsename(replace(@fieldcode,'_','.'),2)
		and parsename(replace(@fieldcode,'_','.'),1) = 'activeDate'
			union all
		select 'mpl_' + cast(PLTypeID as varchar(10)) + '_status' as fieldCode, 'Professional Licenses' as fieldCodeArea, PLName + ' Status' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		from dbo.ams_memberProfessionalLicenseTypes
		where orgID = @orgID
		and PLTypeID = parsename(replace(@fieldcode,'_','.'),2)
		and parsename(replace(@fieldcode,'_','.'),1) = 'status'

	IF left(@fieldcode,4) = 'grp_'
		insert into @fields (fieldCode, fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode)
		select 'grp_' + cast(groupID as varchar(10)) as fieldCode, 'Groups' as fieldCodeArea, dbo.fn_getGroupPathExpanded(groupid) as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		from dbo.ams_groups
		where orgID = @orgID
		and groupID = parsename(replace(@fieldcode,'_','.'),1)

	IF left(@fieldcode,4) = 'sub_' BEGIN
		select top 1 @subT = subtype.typeName
			from dbo.fn_varCharListToTable(@fieldcode,'_') as items
			inner join dbo.sub_types as subtype on cast(subtype.typeID as varchar(10)) = items.listitem 
			where items.autoid = 2
		select top 1 @subS = coalesce(subs.subscriptionName,'All Subscriptions')
			from dbo.fn_varCharListToTable(@fieldcode,'_') as items
			left outer join dbo.sub_subscriptions as subs on cast(subs.subscriptionID as varchar(10)) = items.listitem 
			where items.autoid = 3
		select top 1 @subR = coalesce(rs.scheduleName + ' \ ' + r.rateName,'All Rates')
			from dbo.fn_varCharListToTable(@fieldcode,'_') as items
			left outer join dbo.sub_rates as r 
				inner join dbo.sub_rateSchedules as rs on rs.scheduleID = r.scheduleID 	
				on cast(r.rateID as varchar(10)) = items.listitem 
			where items.autoid = 4
		select top 1 @subSS = s.statusName
			from dbo.fn_varCharListToTable(@fieldcode,'_') as items
			inner join dbo.sub_statuses as s on cast(s.statusid as varchar(10)) = items.listitem 
			where items.autoid = 5
		select top 1 @subPS = coalesce(ps.statusName,'All Payment Statuses')
			from dbo.fn_varCharListToTable(@fieldcode,'_') as items
			left outer join dbo.sub_paymentStatuses as ps on cast(ps.statusID as varchar(10)) = items.listitem 
			where items.autoid = 6
		insert into @fields (fieldCode, fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode)
		VALUES (@fieldcode, 'Subscriptions', @subT + ' / ' + @subS + ' / ' + @subR + ' / ' + @subSS + ' / ' + @subPS, 'SELECT', 'STRING')
	END

	IF @fieldCode = 'mh_entry'
		insert into @fields (fieldCode, fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode)
		VALUES ('mh_entry','Member History','History Entry','SELECT','STRING')

	IF @fieldCode = 'l_entry'
		insert into @fields (fieldCode, fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode)
		VALUES ('l_entry','Listserver Memberships','List Membership','SELECT','STRING')
	
	RETURN 
END
GO



USE [memberCentral]
GO
ALTER PROC [dbo].[cache_members_populateMemberConditionCache]
@orgID int,
@conditionIDList varchar(max) = null,
@memberIDList varchar(max) = null,
@processImmediateOnly bit = 1,
@itemGroupUID uniqueidentifier,
@logTreeID uniqueidentifier

as

set nocount on

IF @itemGroupUID is null
	RETURN -1

declare @starttime datetime, @starttime2 datetime, @sql varchar(max), @totalMS int, 
	@totalID int, @totalRemoved int, @totalAdded int
select @starttime = getdate()

set @conditionIDList = isNull(@conditionIDList,'')
set @memberIDList = isNull(@memberIDList,'')

-- Log
INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage)
VALUES (@logTreeID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'Start Process of orgID=' + cast(@orgID as varchar(10)) + ' conditionID=' + left(@conditionIDList,100) + ' memberID=' + left(@memberIDList,100));

IF OBJECT_ID('tempdb..#cache_members_conditions_shouldbe') IS NOT NULL
	DROP TABLE #cache_members_conditions_shouldbe
IF OBJECT_ID('tempdb..#tblCond') IS NOT NULL
	DROP TABLE #tblCond
IF OBJECT_ID('tempdb..#tblCondALL') IS NOT NULL
	DROP TABLE #tblCondALL
IF OBJECT_ID('tempdb..#tblCondALLFinal') IS NOT NULL
	DROP TABLE #tblCondALLFinal
IF OBJECT_ID('tempdb..#tblFCSplit') IS NOT NULL
	DROP TABLE #tblFCSplit
IF OBJECT_ID('tempdb..#tblSubSplit') IS NOT NULL
	DROP TABLE #tblSubSplit
IF OBJECT_ID('tempdb..#tblAccSplit') IS NOT NULL
	DROP TABLE #tblAccSplit
IF OBJECT_ID('tempdb..#tblAccSplitGL') IS NOT NULL
	DROP TABLE #tblAccSplitGL
IF OBJECT_ID('tempdb..#tblRecSplit') IS NOT NULL
	DROP TABLE #tblRecSplit
IF OBJECT_ID('tempdb..#tblRecSplitRoles') IS NOT NULL
	DROP TABLE #tblRecSplitRoles
IF OBJECT_ID('tempdb..#tblCondAccNotExist') IS NOT NULL
	DROP TABLE #tblCondAccNotExist
IF OBJECT_ID('tempdb..#tblCondValues') IS NOT NULL
	DROP TABLE #tblCondValues
IF OBJECT_ID('tempdb..#tblMembers') IS NOT NULL
	DROP TABLE #tblMembers
IF OBJECT_ID('tempdb..#tblMHSplit') IS NOT NULL
	DROP TABLE #tblMHSplit
IF OBJECT_ID('tempdb..#tblMHSubCategoriesSplit') IS NOT NULL
	DROP TABLE #tblMHSubCategoriesSplit

IF OBJECT_ID('tempdb..#tblLyrisListSplit') IS NOT NULL
	DROP TABLE #tblLyrisListSplit
IF OBJECT_ID('tempdb..#tblLyrisMemTypeSplit') IS NOT NULL
	DROP TABLE #tblLyrisMemTypeSplit
IF OBJECT_ID('tempdb..#tblLyrisSubTypeSplit') IS NOT NULL
	DROP TABLE #tblLyrisSubTypeSplit
IF OBJECT_ID('tempdb..#tblLyrisOptionalSplit') IS NOT NULL
	DROP TABLE #tblLyrisOptionalSplit


CREATE TABLE #cache_members_conditions_shouldbe (memberid int, conditionID int);
CREATE TABLE #tblCond (conditionID int PRIMARY KEY);
CREATE TABLE #tblFCSplit (fieldcode varchar(50), v1 varchar(30), v2 varchar(30), v3 varchar(30), v4 varchar(30), v5 varchar(30), v6 varchar(30));
CREATE TABLE #tblSubSplit (conditionID int, subStartDateLower datetime, subStartDateUpper datetime, subEndDateLower datetime, subEndDateUpper datetime, subGraceDateLower datetime, subGraceDateUpper datetime);
CREATE TABLE #tblAccSplit (conditionID int, revenueGLs varchar(max), batchDateLower datetime, batchDateUpper datetime, revOrCash varchar(7), conditionValue money, conditionValueLower money, conditionValueUpper money);
CREATE TABLE #tblAccSplitGL (conditionID int, revenueGL int);
CREATE TABLE #tblRecSplit (conditionID int, recordTypeID int, roles varchar(max));
CREATE TABLE #tblRecSplitRoles (conditionID int, [role] int);
CREATE TABLE #tblCondALLFinal (conditionID int PRIMARY KEY);
CREATE TABLE #tblCondValues (conditionID int, conditionKeyID int, conditionValueString varchar(max), conditionValueInteger int, conditionValueBit bit, conditionValueDecimal2 decimal(9,2), conditionValueDate datetime);
CREATE TABLE #tblMembers (memberID int PRIMARY KEY);

CREATE TABLE #tblMHSplit (
	conditionID int,
	historyCategory int,
	historyDateLower datetime,
	historyDateUpper datetime,
	historyEnteredDateLower datetime,
	historyEnteredDateUpper datetime,
	historyQuantityLower int,
	historyQuantityUpper int,
	historyAmountLower money,
	historyAmountUpper money,
	historyDescriptionContains varchar(max));

CREATE TABLE #tblMHSubCategoriesSplit (
	conditionID int,
	historyCategory int,
	historySubCategory int
)

CREATE TABLE #tblLyrisListSplit (
	conditionID int,
	list_ varchar(60)
)
CREATE TABLE #tblLyrisMemTypeSplit (
	conditionID int,
	membertype_ varchar(60)
)
CREATE TABLE #tblLyrisSubTypeSplit (
	conditionID int,
	subtype_ varchar(60)
)

CREATE TABLE #tblLyrisOptionalSplit (
	conditionID int,
	listLockAddress bit,
	listKeepActive bit,
	listJoinDateLower datetime,
	listJoinDateUpper datetime,
	listExpireDateLower datetime,
	listExpireDateUpper datetime
);

-- split conditions to calculate
-- HONOR THE @processImmediateOnly parameter
IF len(@conditionIDList) > 0
	INSERT INTO #tblCond (conditionID)
	select distinct c.conditionID
	from dbo.fn_intListToTable(@conditionIDList,',') as tmp
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tmp.listitem
	inner join dbo.ams_virtualGroupConditionTypes as ct on ct.conditionTypeID = c.conditionTypeID
	where c.orgID = @orgID
	and 1 = case 
		when @processImmediateOnly = 1 and ct.processImmediately = 0 then 0
		else 1 end
ELSE
	INSERT INTO #tblCond (conditionID)
	select c.conditionID
	from dbo.ams_virtualGroupConditions as c
	inner join dbo.ams_virtualGroupConditionTypes as ct on ct.conditionTypeID = c.conditionTypeID
	where c.orgID = @orgID
	and 1 = case 
		when @processImmediateOnly = 1 and ct.processImmediately = 0 then 0
		else 1 end

-- get all conditions to calculate
select c.conditionID, e.expression, c.fieldCode, fieldCodeArea = case
	when left(c.fieldCode,2) = 'm_' then 'Member Data'	
	when left(c.fieldCode,3) = 'md_' then 'Custom Fields'	
	when left(c.fieldCode,3) = 'ma_' then 'Addresses'	
	when left(c.fieldCode,3) = 'mp_' then 'Phones'	
	when left(c.fieldCode,4) = 'mad_' then 'Districting'	
	when left(c.fieldCode,3) = 'me_' then 'Emails'	
	when left(c.fieldCode,3) = 'mw_' then 'Websites'	
	when left(c.fieldCode,2) = 'e_' then 'Events'	
	when left(c.fieldcode,4) = 'mpl_' then 'Professional Licenses'	
	when left(c.fieldcode,4) = 'grp_' then 'Groups'	-- excluded in where clause
	when left(c.fieldcode,4) = 'sub_' then 'Subscriptions'	
	when left(c.fieldcode,3) = 'rt_' then 'Record Types'	
	when left(c.fieldcode,5) = 'acct_' then 'Accounting'	
	when left(c.fieldcode,3) = 'mh_' then 'Member History'	
	when left(c.fieldcode,2) = 'l_' then 'Listserver Memberships'
	end, dit.displayTypeCode, dat.dataTypeCode,
	fieldCodeAreaID = case 
	when left(c.fieldCode,3) = 'md_' then cast(replace(c.fieldcode,'md_','') as int)
	when left(c.fieldcode,4) = 'mpl_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,3) = 'ma_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,2) = 'e_' then cast(replace(c.fieldcode,'e_','') as int)
	when left(c.fieldCode,3) = 'me_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,3) = 'mw_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,3) = 'mp_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as int)
	when left(c.fieldCode,4) = 'mad_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as int)
	else null end,
	fieldCodeAreaPartA = case
	when left(c.fieldcode,4) = 'mpl_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as varchar(20))
	when left(c.fieldCode,3) = 'ma_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as varchar(20))
	when left(c.fieldCode,3) = 'mp_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as varchar(10))
	when left(c.fieldCode,4) = 'mad_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as varchar(10))
	else null end
into #tblCondALL
from dbo.ams_virtualGroupConditions as c
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.expressionID
inner join dbo.ams_memberDataColumnDataTypes as dat on dat.dataTypeID = c.dataTypeID
inner join dbo.ams_memberDataColumnDisplayTypes as dit on dit.displayTypeID = c.displayTypeID
inner join #tblCond as tblC on tblc.conditionID = c.conditionID
where c.orgID = @orgID
and c.isDefined = 1
and left(c.fieldCode,4) <> 'grp_'

-- add indexes for speed
ALTER TABLE #tblCondALL ADD PRIMARY KEY(conditionID);
CREATE INDEX IX_tblCondALL_areaid ON #tblCondALL (fieldCodeAreaID asc);
CREATE INDEX IX_tblCondALL_areaparta ON #tblCondALL (fieldCodeAreaPartA asc);
CREATE NONCLUSTERED INDEX IX_tblCondALL_DTA2 ON #tblCondALL (fieldCodeArea ASC, dataTypeCode ASC, expression ASC);

-- for the final processing at the end
insert into #tblCondALLFinal (conditionid)
select conditionID from #tblCondALL

-- this is used in subscriptions calculations
insert into #tblFCSplit (fieldcode, v1, v2, v3, v4, v5, v6)
select distinct fieldCode, [1], [2], [3], [4], [5], [6]
from #tblCondALL as tblc  
cross apply dbo.fn_varcharListToTable(tblc.fieldCode,'_') as vclist
PIVOT (min(listItem) FOR autoID in ([1],[2],[3],[4],[5],[6])) as pvt
where fieldCodeArea = 'Subscriptions'

CREATE INDEX IX_tblFCSplit_fc ON #tblFCSplit (fieldcode asc);
CREATE INDEX IX_tblFCSplit_v1 ON #tblFCSplit (v1 asc);
CREATE INDEX IX_tblFCSplit_v2 ON #tblFCSplit (v2 asc);
CREATE INDEX IX_tblFCSplit_v3 ON #tblFCSplit (v3 asc);
CREATE INDEX IX_tblFCSplit_v4 ON #tblFCSplit (v4 asc);
CREATE INDEX IX_tblFCSplit_v5 ON #tblFCSplit (v5 asc);
CREATE INDEX IX_tblFCSplit_v6 ON #tblFCSplit (v6 asc);

insert into #tblSubSplit
select distinct tblc.conditionID, 
	cast(nullif(subStartDateLower.val,'') as datetime) as subStartDateLower, 
	DATEADD(d,1,DATEADD(ms,-3,cast(nullif(subStartDateUpper.val,'') as datetime))) as subStartDateUpper,
	cast(nullif(subEndDateLower.val,'') as datetime) as subEndDateLower, 
	DATEADD(d,1,DATEADD(ms,-3,cast(nullif(subEndDateUpper.val,'') as datetime))) as subEndDateUpper,
	cast(nullif(subGraceDateLower.val,'') as datetime) as subGraceDateLower, 
	DATEADD(d,1,DATEADD(ms,-3,cast(nullif(subGraceDateUpper.val,'') as datetime))) as subGraceDateUpper
from #tblCondALL as tblc
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subStartDateLower'
	WHERE cv.conditionID = tblc.conditionID
) as subStartDateLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subStartDateUpper'
	WHERE cv.conditionID = tblc.conditionID
) as subStartDateUpper(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subEndDateLower'
	WHERE cv.conditionID = tblc.conditionID
) as subEndDateLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subEndDateUpper'
	WHERE cv.conditionID = tblc.conditionID
) as subEndDateUpper(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subGraceDateLower'
	WHERE cv.conditionID = tblc.conditionID
) as subGraceDateLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subGraceDateUpper'
	WHERE cv.conditionID = tblc.conditionID
) as subGraceDateUpper(val)
where fieldCodeArea = 'Subscriptions'


-- this is used in accounting calculations
insert into #tblAccSplit
select distinct tblc.conditionID, LEFT(gllist.list, LEN(gllist.list)-1) as revenueGLs, 
	cast(batchDateLower.val as datetime) as batchDateLower, 
	DATEADD(d,1,DATEADD(ms,-3,cast(batchDateUpper.val as datetime))) as batchDateUpper,
	linkAllocType.val as revOrCash, 
	cast(value.val as money) as conditionValue, 
	cast(valueLower.val as money) as conditionValueLower, 
	cast(valueUpper.val as money) as conditionValueUpper 
from #tblCondALL as tblc
CROSS APPLY ( 
	SELECT cv.conditionValue + ',' AS [text()] 
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'revenueGL'
	WHERE cv.conditionID = tblc.conditionID
	FOR XML PATH('')
) as gllist(list) 
CROSS APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'batchDateLower'
	WHERE cv.conditionID = tblc.conditionID
) as batchDateLower(val)
CROSS APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'batchDateUpper'
	WHERE cv.conditionID = tblc.conditionID
) as batchDateUpper(val)
CROSS APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'linkAllocType'
	WHERE cv.conditionID = tblc.conditionID
) as linkAllocType(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'value'
	WHERE cv.conditionID = tblc.conditionID
) as value(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'valueLower'
	WHERE cv.conditionID = tblc.conditionID
) as valueLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'valueUpper'
	WHERE cv.conditionID = tblc.conditionID
) as valueUpper(val)
where fieldCodeArea = 'Accounting'

CREATE INDEX IX_tblACCSplit_conditionID ON #tblAccSplit (conditionID asc);

insert into #tblAccSplitGL
select conditionID, revGL.listItem as revenueGL
from #tblAccSplit
cross apply dbo.fn_intListToTable(revenueGLs,',') as revGL


-- this is used in recordtype calculations
insert into #tblRecSplit
select distinct tblc.conditionID, cast(recordType.val as int) as recordTypeID, LEFT(rolelist.list, LEN(rolelist.list)-1) as roles 
from #tblCondALL as tblc
CROSS APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'recordType'
	WHERE cv.conditionID = tblc.conditionID
) as recordType(val)
INNER JOIN dbo.ams_recordTypes as rt on rt.recordTypeID = cast(recordType.val as int)
OUTER APPLY ( 
	SELECT cv.conditionValue + ', ' AS [text()] 
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'role'
	WHERE cv.conditionID = tblc.conditionID
	FOR XML PATH('')
) as rolelist(list) 
where fieldCodeArea = 'Record Types'

CREATE INDEX IX_tblRecSplit_conditionID ON #tblRecSplit (conditionID asc);

insert into #tblRecSplitRoles
select conditionID, recRole.listItem as [role]
from #tblRecSplit
cross apply dbo.fn_intListToTable(roles,',') as recRole

-- this is used in Member history calculations

insert into #tblMHSubCategoriesSplit (conditionID,historyCategory,historySubCategory)
select 
	tblc.conditionID,
	cast(historyCategory.val as int),
	cast(historySubCategory.val as int)
from #tblCondALL as tblc
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyCategory'
	WHERE cv.conditionID = tblc.conditionID
) as historyCategory(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historySubCategory'
	WHERE cv.conditionID = tblc.conditionID
) as historySubCategory(val)
where fieldCodeArea = 'Member History'
and nullif(historySubCategory.val,'') is not null


CREATE INDEX IX_tblMHSubCategoriesSplit_conditionID ON #tblMHSubCategoriesSplit (conditionID asc);
CREATE INDEX IX_tblMHSubCategoriesSplit_historyCategory ON #tblMHSubCategoriesSplit (historyCategory asc);
CREATE INDEX IX_tblMHSubCategoriesSplit_historySubCategory ON #tblMHSubCategoriesSplit (historySubCategory asc);

insert into #tblMHSplit (conditionID ,historyCategory ,historyDateLower ,historyDateUpper ,historyEnteredDateLower ,historyEnteredDateUpper ,historyQuantityLower ,historyQuantityUpper ,historyAmountLower ,historyAmountUpper ,historyDescriptionContains)
select 
	tblc.conditionID,
	historyCategory.val,
	cast(nullif(historyDateLower.val,'') as datetime),
	cast(nullif(historyDateUpper.val,'') as datetime),
	cast(nullif(historyEnteredDateLower.val,'') as datetime),
	cast(nullif(historyEnteredDateUpper.val,'') as datetime),
	cast(nullif(historyQuantityLower.val,'') as int),
	cast(nullif(historyQuantityUpper.val,'') as int),
	cast(nullif(historyAmountLower.val,'') as money),
	cast(nullif(historyAmountUpper.val,'') as money),
	nullif(historyDescriptionContains.val,'')
from #tblCondALL as tblc
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyCategory'
	WHERE cv.conditionID = tblc.conditionID
) as historyCategory(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyDateLower'
	WHERE cv.conditionID = tblc.conditionID
) as historyDateLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyDateUpper'
	WHERE cv.conditionID = tblc.conditionID
) as historyDateUpper(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyEnteredDateLower'
	WHERE cv.conditionID = tblc.conditionID
) as historyEnteredDateLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyEnteredDateUpper'
	WHERE cv.conditionID = tblc.conditionID
) as historyEnteredDateUpper(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyQuantityLower'
	WHERE cv.conditionID = tblc.conditionID
) as historyQuantityLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyQuantityUpper'
	WHERE cv.conditionID = tblc.conditionID
) as historyQuantityUpper(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyAmountLower'
	WHERE cv.conditionID = tblc.conditionID
) as historyAmountLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyAmountUpper'
	WHERE cv.conditionID = tblc.conditionID
) as historyAmountUpper(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyDescriptionContains'
	WHERE cv.conditionID = tblc.conditionID
) as historyDescriptionContains(val)
where fieldCodeArea = 'Member History'


CREATE INDEX IX_tblMHSplit_conditionID ON #tblMHSubCategoriesSplit (conditionID asc);
CREATE INDEX IX_tblMHSplit_historyCategory ON #tblMHSubCategoriesSplit (historyCategory asc);


insert into #tblLyrisListSplit (conditionID, list_)
select 
	tblc.conditionID,
	cast(listName.val as varchar(60))
from #tblCondALL as tblc
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listList'
	WHERE cv.conditionID = tblc.conditionID
) as listName(val)
where fieldCodeArea = 'Listserver Memberships'

CREATE INDEX IX_tblLyrisListSplit_conditionID ON #tblLyrisListSplit (conditionID asc);
CREATE INDEX IX_tblLyrisListSplit_listname ON #tblLyrisListSplit (list_ asc);



insert into #tblLyrisMemTypeSplit (conditionID, membertype_)
select 
	tblc.conditionID,
	cast(memberType.val as varchar(20))
from #tblCondALL as tblc
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listMemberType'
	WHERE cv.conditionID = tblc.conditionID
) as memberType(val)
where fieldCodeArea = 'Listserver Memberships'


CREATE INDEX IX_tblLyrisMemTypeSplit_conditionID ON #tblLyrisMemTypeSplit (conditionID asc);
CREATE INDEX IX_tblLyrisMemTypeSplit_memberType ON #tblLyrisMemTypeSplit (membertype_ asc);

insert into #tblLyrisSubTypeSplit (conditionID, subtype_)
select 
	tblc.conditionID,
	cast(subType.val as varchar(20))
from #tblCondALL as tblc
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listSubType'
	WHERE cv.conditionID = tblc.conditionID
) as subType(val)
where fieldCodeArea = 'Listserver Memberships'


CREATE INDEX IX_tblLyrisSubTypeSplit_conditionID ON #tblLyrisSubTypeSplit (conditionID asc);
CREATE INDEX IX_tblLyrisSubTypeSplit_memberType ON #tblLyrisSubTypeSplit (subtype_ asc);


insert into #tblLyrisOptionalSplit (conditionID ,listLockAddress ,listKeepActive ,listJoinDateLower ,listJoinDateUpper ,listExpireDateLower ,listExpireDateUpper)
select 
	tblc.conditionID,
	cast(nullif(listLockAddress.val,'-1') as bit),
	cast(nullif(listKeepActive.val,'-1') as bit),
	cast(nullif(listJoinDateLower.val,'') as datetime),
	cast(nullif(listJoinDateUpper.val,'') as datetime),
	cast(nullif(listExpireDateLower.val,'') as datetime),
	cast(nullif(listExpireDateUpper.val,'') as datetime)
from #tblCondALL as tblc
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listLockAddress'
	WHERE cv.conditionID = tblc.conditionID
) as listLockAddress(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listKeepActive'
	WHERE cv.conditionID = tblc.conditionID
) as listKeepActive(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listJoinDateLower'
	WHERE cv.conditionID = tblc.conditionID
) as listJoinDateLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listJoinDateUpper'
	WHERE cv.conditionID = tblc.conditionID
) as listJoinDateUpper(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listExpireDateLower'
	WHERE cv.conditionID = tblc.conditionID
) as listExpireDateLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listExpireDateUpper'
	WHERE cv.conditionID = tblc.conditionID
) as listExpireDateUpper(val)

where fieldCodeArea = 'Listserver Memberships'

CREATE INDEX IX_tblLyrisOptionalSplit_conditionID ON #tblLyrisOptionalSplit (conditionID asc);


-- put condition values into temp table by datatype to remove all casting in below queries
insert into #tblCondValues (conditionID, conditionKeyID, conditionValueString, conditionValueInteger, conditionValueBit, conditionValueDecimal2, conditionValueDate)
select cv.conditionID, cv.conditionKeyID, null, cv.conditionValue, null, null, null
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where 
(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'STRING' and tblc.displayTypeCode in ('RADIO','SELECT')) or
(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'DECIMAL2' and tblc.displayTypeCode in ('RADIO','SELECT')) or 
(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'DATE' and tblc.displayTypeCode in ('RADIO','SELECT')) or 
(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq','lt','lte','gt','gte') and tblc.dataTypeCode = 'INTEGER') or
(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('datepart','datediff')) or
(tblc.fieldCodeArea = 'Addresses' and tblc.expression in ('eq','neq') and tblc.displayTypeCode in ('RADIO','SELECT')) or
(tblc.fieldCodeArea = 'Professional Licenses' and tblc.expression in ('datepart','datediff')) or
(tblc.fieldCodeArea = 'Member Data' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'INTEGER') or
(tblc.fieldCodeArea = 'Districting' and tblc.expression in ('eq','neq'))
	union all
select cv.conditionID, cv.conditionKeyID, cv.conditionValue, null, null, null, null
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where
(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'STRING' and tblc.displayTypeCode not in ('RADIO','SELECT')) or
(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('lt','lte','gt','gte','contains','contains_regex') and tblc.dataTypeCode = 'STRING') or
(tblc.fieldCodeArea = 'Addresses' and tblc.expression in ('eq','neq') and tblc.displayTypeCode not in ('RADIO','SELECT')) or
(tblc.fieldCodeArea = 'Addresses' and tblc.expression in ('lt','lte','gt','gte','contains','contains_regex')) or
(tblc.fieldCodeArea = 'Professional Licenses' and tblc.expression in ('eq','neq','lt','lte','gt','gte','contains','contains_regex') and tblc.dataTypeCode = 'STRING') or
(tblc.fieldCodeArea = 'Member Data' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'STRING') or
(tblc.fieldCodeArea = 'Member Data' and tblc.expression in ('lt','lte','gt','gte','contains','contains_regex')) or
(tblc.fieldCodeArea = 'Websites') or
(tblc.fieldCodeArea = 'Emails') or
(tblc.fieldCodeArea = 'Phones')
	union all
select cv.conditionID, cv.conditionKeyID, null, null, cv.conditionValue, null, null
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where (tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'BIT')
	union all
select cv.conditionID, cv.conditionKeyID, null, null, null, cv.conditionValue, null
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where
(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'DECIMAL2' and tblc.displayTypeCode not in ('RADIO','SELECT')) or
(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('lt','lte','gt','gte') and tblc.dataTypeCode = 'DECIMAL2')
	union all
select cv.conditionID, cv.conditionKeyID, null, null, null, null, cv.conditionValue
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where
(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'DATE' and tblc.displayTypeCode not in ('RADIO','SELECT')) or
(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('lt','lte','gt','gte') and tblc.dataTypeCode = 'DATE') or
(tblc.fieldCodeArea = 'Professional Licenses' and tblc.expression in ('eq','neq','lt','lte','gt','gte') and tblc.dataTypeCode = 'DATE')

CREATE INDEX IX_tblCondValues_conditionID ON #tblCondValues (conditionID asc);


-- split members to calculate
IF len(@memberIDList) > 0
	INSERT INTO #tblMembers (memberID)
	select distinct m.memberID
	from dbo.fn_intListToTable(@memberIDList,',') as tmp
	inner join dbo.ams_members as m on m.memberID = tmp.listitem
	where m.orgID = @orgID
	and m.status <> 'D'
ELSE
	INSERT INTO #tblMembers (memberID)
	select m.memberID
	from dbo.ams_members as m
	where m.orgID = @orgID
	and m.status <> 'D'

select @totalID = count(*) from #tblMembers 
INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage)
VALUES (@logTreeID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'Found ' + cast(@totalID as varchar(10)) + ' members to process');




/* These have been arranged in order of their popularity for faster processing. */

/* ******************** */
/* md_xxxx, eq, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.valueID = cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'STRING'
	and tblc.displayTypeCode in ('RADIO','SELECT')

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue = cv.conditionValueString
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'STRING'
	and tblc.displayTypeCode not in ('RADIO','SELECT')

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'eq' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* sub_xxxx, subscribed  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Subscriptions' and expression='subscribed') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblFCSplit as fcsplit on fcsplit.fieldCode = tblc.fieldCode
	inner join #tblSubSplit as subsplit on subsplit.conditionID = tblc.conditionID
	inner join dbo.sub_subscriptions as s on fcsplit.v2 in (0,s.typeID)
		and fcsplit.v3 in (0,s.subscriptionID)
	inner join dbo.sub_rateFrequencies as rf on fcsplit.v4 in (0,rf.rateID)
	inner join dbo.sub_subscribers as sub on rf.rfid = sub.RFID
		and s.subscriptionID = sub.subscriptionID
		and fcsplit.v5 in (0,sub.statusID)
		and fcsplit.v6 in (0,sub.paymentStatusID)
		and 1 = 
			case 
			when subsplit.subStartDateLower is null and subsplit.subStartDateUpper is null then 1
			when subsplit.subStartDateLower is not null and subsplit.subStartDateUpper is not null and sub.subStartDate between subsplit.subStartDateLower and subsplit.subStartDateUpper then 1 
			when subsplit.subStartDateLower is not null and subsplit.subStartDateUpper is null and sub.subStartDate >= subsplit.subStartDateLower then 1
			when subsplit.subStartDateUpper is not null and subsplit.subStartDateLower is null and sub.subStartDate <= subsplit.subStartDateUpper then 1
			else 0 end
		and 1 = 
			case 
			when subsplit.subEndDateLower is null and subsplit.subEndDateUpper is null then 1
			when subsplit.subEndDateLower is not null and subsplit.subEndDateUpper is not null and sub.subEndDate between subsplit.subEndDateLower and subsplit.subEndDateUpper then 1 
			when subsplit.subEndDateLower is not null and subsplit.subEndDateUpper is null and sub.subEndDate >= subsplit.subEndDateLower then 1
			when subsplit.subEndDateUpper is not null and subsplit.subEndDateLower is null and sub.subEndDate <= subsplit.subEndDateUpper then 1
			else 0 end
		and 1 = 
			case 
			when subsplit.subGraceDateLower is null and subsplit.subGraceDateUpper is null then 1
			when subsplit.subGraceDateLower is not null and subsplit.subGraceDateUpper is not null and sub.graceEndDate between subsplit.subGraceDateLower and subsplit.subGraceDateUpper then 1 
			when subsplit.subGraceDateLower is not null and subsplit.subGraceDateUpper is null and sub.graceEndDate >= subsplit.subGraceDateLower then 1
			when subsplit.subGraceDateUpper is not null and subsplit.subGraceDateLower is null and sub.graceEndDate <= subsplit.subGraceDateUpper then 1
			else 0 end
	inner join dbo.ams_members as m2 on m2.memberid = sub.memberid
	inner join #tblMembers as m on m.memberID = m2.activeMemberID
	where tblc.fieldCodeArea = 'Subscriptions' 
	and tblc.expression = 'subscribed' 

	delete from #tblCondALL 
	where fieldCodeArea = 'Subscriptions' and expression = 'subscribed'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, eq, bit  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='BIT') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__bit as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue = cv.conditionValueBit
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'BIT'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'eq' and dataTypeCode = 'BIT'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************ */
/* ma_xxxx, eq  */
/* ************ */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='eq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'eq' 
	and tblc.displayTypeCode not in ('RADIO','SELECT')
	and case tblc.fieldCodeAreaPartA
		when 'address1' then isnull(ma.address1,'') 
		when 'address2' then isnull(ma.address2,'') 
		when 'address3' then isnull(ma.address3,'') 
		when 'city' then isnull(ma.city,'') 
		when 'postalcode' then isnull(ma.postalcode,'') 
		when 'county' then isnull(ma.county,'') 
		else isnull(ma.attn,'') end = cv.conditionValueString

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'eq' 
	and tblc.displayTypeCode in ('RADIO','SELECT')
	and case tblc.fieldCodeAreaPartA
		when 'stateprov' then isnull(ma.stateid,0) 
		else isnull(ma.countryid,0) end = cv.conditionValueInteger

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'eq'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, eq, int  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.valueID = cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'INTEGER'
	and tblc.displayTypeCode in ('RADIO','SELECT')

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue = cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'INTEGER'
	and tblc.displayTypeCode not in ('RADIO','SELECT')

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'eq' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, eq, string  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='eq' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'STRING'
	and case tblc.fieldCodeAreaPartA
		when 'status' then isnull(mpls.statusName,'') 
		else isnull(mpl.licensenumber,'') end = cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'eq' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, datediff  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='datediff') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'eq'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,vw.columnValue,getdate())
			when 'm' then datediff(m,vw.columnValue,getdate())
			when 'wk' then datediff(wk,vw.columnValue,getdate())
			when 'dw' then datediff(dw,vw.columnValue,getdate())
			when 'd' then datediff(d,vw.columnValue,getdate())
			when 'dy' then datediff(dy,vw.columnValue,getdate())
			else datediff(yy,vw.columnValue,getdate()) end = abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'eq'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),vw.columnValue)
			when 'm' then datediff(m,getdate(),vw.columnValue)
			when 'wk' then datediff(wk,getdate(),vw.columnValue)
			when 'dw' then datediff(dw,getdate(),vw.columnValue)
			when 'd' then datediff(d,getdate(),vw.columnValue)
			when 'dy' then datediff(dy,getdate(),vw.columnValue)
			else datediff(yy,getdate(),vw.columnValue) end = abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger < 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'neq'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,vw.columnValue,getdate())
			when 'm' then datediff(m,vw.columnValue,getdate())
			when 'wk' then datediff(wk,vw.columnValue,getdate())
			when 'dw' then datediff(dw,vw.columnValue,getdate())
			when 'd' then datediff(d,vw.columnValue,getdate())
			when 'dy' then datediff(dy,vw.columnValue,getdate())
			else datediff(yy,vw.columnValue,getdate()) end <> abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'neq'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),vw.columnValue)
			when 'm' then datediff(m,getdate(),vw.columnValue)
			when 'wk' then datediff(wk,getdate(),vw.columnValue)
			when 'dw' then datediff(dw,getdate(),vw.columnValue)
			when 'd' then datediff(d,getdate(),vw.columnValue)
			when 'dy' then datediff(dy,getdate(),vw.columnValue)
			else datediff(yy,getdate(),vw.columnValue) end <> abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger < 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lt'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,vw.columnValue,getdate())
			when 'm' then datediff(m,vw.columnValue,getdate())
			when 'wk' then datediff(wk,vw.columnValue,getdate())
			when 'dw' then datediff(dw,vw.columnValue,getdate())
			when 'd' then datediff(d,vw.columnValue,getdate())
			when 'dy' then datediff(dy,vw.columnValue,getdate())
			else datediff(yy,vw.columnValue,getdate()) end < abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lt'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),vw.columnValue)
			when 'm' then datediff(m,getdate(),vw.columnValue)
			when 'wk' then datediff(wk,getdate(),vw.columnValue)
			when 'dw' then datediff(dw,getdate(),vw.columnValue)
			when 'd' then datediff(d,getdate(),vw.columnValue)
			when 'dy' then datediff(dy,getdate(),vw.columnValue)
			else datediff(yy,getdate(),vw.columnValue) end < abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger < 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lte'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,vw.columnValue,getdate())
			when 'm' then datediff(m,vw.columnValue,getdate())
			when 'wk' then datediff(wk,vw.columnValue,getdate())
			when 'dw' then datediff(dw,vw.columnValue,getdate())
			when 'd' then datediff(d,vw.columnValue,getdate())
			when 'dy' then datediff(dy,vw.columnValue,getdate())
			else datediff(yy,vw.columnValue,getdate()) end <= abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lte'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),vw.columnValue)
			when 'm' then datediff(m,getdate(),vw.columnValue)
			when 'wk' then datediff(wk,getdate(),vw.columnValue)
			when 'dw' then datediff(dw,getdate(),vw.columnValue)
			when 'd' then datediff(d,getdate(),vw.columnValue)
			when 'dy' then datediff(dy,getdate(),vw.columnValue)
			else datediff(yy,getdate(),vw.columnValue) end <= abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger < 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gt'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,vw.columnValue,getdate())
			when 'm' then datediff(m,vw.columnValue,getdate())
			when 'wk' then datediff(wk,vw.columnValue,getdate())
			when 'dw' then datediff(dw,vw.columnValue,getdate())
			when 'd' then datediff(d,vw.columnValue,getdate())
			when 'dy' then datediff(dy,vw.columnValue,getdate())
			else datediff(yy,vw.columnValue,getdate()) end > abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gt'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),vw.columnValue)
			when 'm' then datediff(m,getdate(),vw.columnValue)
			when 'wk' then datediff(wk,getdate(),vw.columnValue)
			when 'dw' then datediff(dw,getdate(),vw.columnValue)
			when 'd' then datediff(d,getdate(),vw.columnValue)
			when 'dy' then datediff(dy,getdate(),vw.columnValue)
			else datediff(yy,getdate(),vw.columnValue) end > abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger < 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gte'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,vw.columnValue,getdate())
			when 'm' then datediff(m,vw.columnValue,getdate())
			when 'wk' then datediff(wk,vw.columnValue,getdate())
			when 'dw' then datediff(dw,vw.columnValue,getdate())
			when 'd' then datediff(d,vw.columnValue,getdate())
			when 'dy' then datediff(dy,vw.columnValue,getdate())
			else datediff(yy,vw.columnValue,getdate()) end >= abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gte'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),vw.columnValue)
			when 'm' then datediff(m,getdate(),vw.columnValue)
			when 'wk' then datediff(wk,getdate(),vw.columnValue)
			when 'dw' then datediff(dw,getdate(),vw.columnValue)
			when 'd' then datediff(d,getdate(),vw.columnValue)
			when 'dy' then datediff(dy,getdate(),vw.columnValue)
			else datediff(yy,getdate(),vw.columnValue) end >= abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger < 0

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'datediff'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, exists, string  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='exists' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'exists' 
	and tblc.dataTypeCode = 'STRING'
	and case tblc.fieldCodeAreaPartA
		when 'status' then nullif(mpls.StatusName,'')
		else nullif(mpl.licensenumber,'') end is not null

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'exists' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, exists, bit  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='BIT') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.vw_memberData__bit as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue is not null
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'exists' 
	and tblc.dataTypeCode = 'BIT'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'exists' and dataTypeCode = 'BIT'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* e_xxxx, registered  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Events' and expression='registered') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ev_events as e on e.eventID = tblc.fieldCodeAreaID
	inner join dbo.ev_registration as er on er.eventID = e.eventID and er.status = 'A'
	inner join dbo.ev_registrants as reg on reg.registrationID = er.registrationID and reg.status = 'A'
	inner join dbo.ams_members as m2 on m2.memberid = reg.memberid
	inner join #tblMembers as m on m.memberID = m2.activeMemberID
	where tblc.fieldCodeArea = 'Events' 
	and tblc.expression = 'registered' 

	delete from #tblCondALL 
	where fieldCodeArea = 'Events' and expression = 'registered'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* md_xxxx, exists, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue is not null
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'exists' 
	and tblc.dataTypeCode = 'STRING'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'exists' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc


/* THE FOLLOWING ARE NOT ORDERED BY POPULARITY DUE TO LOW INSTANCES (UNDER 50) */


/* ****************** */
/* mpl_xxxx, datediff  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='datediff') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'eq'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,mpl.activeDate,getdate())
			when 'm' then datediff(m,mpl.activeDate,getdate())
			when 'wk' then datediff(wk,mpl.activeDate,getdate())
			when 'dw' then datediff(dw,mpl.activeDate,getdate())
			when 'd' then datediff(d,mpl.activeDate,getdate())
			when 'dy' then datediff(dy,mpl.activeDate,getdate())
			else datediff(yy,mpl.activeDate,getdate()) end = abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff'
	and cv.conditionValueInteger >= 0 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'eq'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),mpl.activeDate)
			when 'm' then datediff(m,getdate(),mpl.activeDate)
			when 'wk' then datediff(wk,getdate(),mpl.activeDate)
			when 'dw' then datediff(dw,getdate(),mpl.activeDate)
			when 'd' then datediff(d,getdate(),mpl.activeDate)
			when 'dy' then datediff(dy,getdate(),mpl.activeDate)
			else datediff(yy,getdate(),mpl.activeDate) end = abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger < 0 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'neq'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,mpl.activeDate,getdate())
			when 'm' then datediff(m,mpl.activeDate,getdate())
			when 'wk' then datediff(wk,mpl.activeDate,getdate())
			when 'dw' then datediff(dw,mpl.activeDate,getdate())
			when 'd' then datediff(d,mpl.activeDate,getdate())
			when 'dy' then datediff(dy,mpl.activeDate,getdate())
			else datediff(yy,mpl.activeDate,getdate()) end <> abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'neq'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),mpl.activeDate)
			when 'm' then datediff(m,getdate(),mpl.activeDate)
			when 'wk' then datediff(wk,getdate(),mpl.activeDate)
			when 'dw' then datediff(dw,getdate(),mpl.activeDate)
			when 'd' then datediff(d,getdate(),mpl.activeDate)
			when 'dy' then datediff(dy,getdate(),mpl.activeDate)
			else datediff(yy,getdate(),mpl.activeDate) end <> abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger < 0 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lt'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,mpl.activeDate,getdate())
			when 'm' then datediff(m,mpl.activeDate,getdate())
			when 'wk' then datediff(wk,mpl.activeDate,getdate())
			when 'dw' then datediff(dw,mpl.activeDate,getdate())
			when 'd' then datediff(d,mpl.activeDate,getdate())
			when 'dy' then datediff(dy,mpl.activeDate,getdate())
			else datediff(yy,mpl.activeDate,getdate()) end < abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses'
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lt'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),mpl.activeDate)
			when 'm' then datediff(m,getdate(),mpl.activeDate)
			when 'wk' then datediff(wk,getdate(),mpl.activeDate)
			when 'dw' then datediff(dw,getdate(),mpl.activeDate)
			when 'd' then datediff(d,getdate(),mpl.activeDate)
			when 'dy' then datediff(dy,getdate(),mpl.activeDate)
			else datediff(yy,getdate(),mpl.activeDate) end < abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses'
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger < 0 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lte'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,mpl.activeDate,getdate())
			when 'm' then datediff(m,mpl.activeDate,getdate())
			when 'wk' then datediff(wk,mpl.activeDate,getdate())
			when 'dw' then datediff(dw,mpl.activeDate,getdate())
			when 'd' then datediff(d,mpl.activeDate,getdate())
			when 'dy' then datediff(dy,mpl.activeDate,getdate())
			else datediff(yy,mpl.activeDate,getdate()) end <= abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lte'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),mpl.activeDate)
			when 'm' then datediff(m,getdate(),mpl.activeDate)
			when 'wk' then datediff(wk,getdate(),mpl.activeDate)
			when 'dw' then datediff(dw,getdate(),mpl.activeDate)
			when 'd' then datediff(d,getdate(),mpl.activeDate)
			when 'dy' then datediff(dy,getdate(),mpl.activeDate)
			else datediff(yy,getdate(),mpl.activeDate) end <= abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger < 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gt'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,mpl.activeDate,getdate())
			when 'm' then datediff(m,mpl.activeDate,getdate())
			when 'wk' then datediff(wk,mpl.activeDate,getdate())
			when 'dw' then datediff(dw,mpl.activeDate,getdate())
			when 'd' then datediff(d,mpl.activeDate,getdate())
			when 'dy' then datediff(dy,mpl.activeDate,getdate())
			else datediff(yy,mpl.activeDate,getdate()) end > abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gt'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),mpl.activeDate)
			when 'm' then datediff(m,getdate(),mpl.activeDate)
			when 'wk' then datediff(wk,getdate(),mpl.activeDate)
			when 'dw' then datediff(dw,getdate(),mpl.activeDate)
			when 'd' then datediff(d,getdate(),mpl.activeDate)
			when 'dy' then datediff(dy,getdate(),mpl.activeDate)
			else datediff(yy,getdate(),mpl.activeDate) end > abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger < 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gte'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,mpl.activeDate,getdate())
			when 'm' then datediff(m,mpl.activeDate,getdate())
			when 'wk' then datediff(wk,mpl.activeDate,getdate())
			when 'dw' then datediff(dw,mpl.activeDate,getdate())
			when 'd' then datediff(d,mpl.activeDate,getdate())
			when 'dy' then datediff(dy,mpl.activeDate,getdate())
			else datediff(yy,mpl.activeDate,getdate()) end >= abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger >= 0

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gte'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datediff(q,getdate(),mpl.activeDate)
			when 'm' then datediff(m,getdate(),mpl.activeDate)
			when 'wk' then datediff(wk,getdate(),mpl.activeDate)
			when 'dw' then datediff(dw,getdate(),mpl.activeDate)
			when 'd' then datediff(d,getdate(),mpl.activeDate)
			when 'dy' then datediff(dy,getdate(),mpl.activeDate)
			else datediff(yy,getdate(),mpl.activeDate) end >= abs(cv.conditionValueInteger)
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datediff' 
	and cv.conditionValueInteger < 0

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'datediff'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************* */
/* md_xxxx, neq, string  */
/* ********************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'STRING'
	and tblc.displayTypeCode in ('RADIO','SELECT')
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__string as vw WITH(NOEXPAND)
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
			and vw.valueID = cv.conditionValueInteger
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
	)	

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'STRING'
	and tblc.displayTypeCode not in ('RADIO','SELECT')
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__string as vw WITH(NOEXPAND)
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
			and vw.columnValue = cv.conditionValueString
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'neq' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* md_xxxx, not_exists, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'not_exists' 
	and tblc.dataTypeCode = 'STRING'
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__string as vw WITH(NOEXPAND)
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
		and vw.columnValue is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'not_exists' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************** */
/* md_xxxx, eq, decimal2  */
/* ********************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='DECIMAL2') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.valueID = cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'DECIMAL2'
	and tblc.displayTypeCode in ('RADIO','SELECT')

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue = cv.conditionValueDecimal2
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'DECIMAL2'
	and tblc.displayTypeCode not in ('RADIO','SELECT')

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'eq' and dataTypeCode = 'DECIMAL2'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, eq, date  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.valueID = cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'DATE'
	and tblc.displayTypeCode in ('RADIO','SELECT')

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue = cv.conditionValueDate
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'DATE'
	and tblc.displayTypeCode not in ('RADIO','SELECT')

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'eq' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, neq, bit  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='BIT') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'BIT'
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__bit as vw WITH(NOEXPAND)
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
			and vw.columnValue = cv.conditionValueBit
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'neq' and dataTypeCode = 'BIT'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, neq, int  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'INTEGER'
	and tblc.displayTypeCode in ('RADIO','SELECT')
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__integer as vw WITH(NOEXPAND)
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
			and vw.valueID = cv.conditionValueInteger
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
	)	

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberID = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'INTEGER'
	and tblc.displayTypeCode not in ('RADIO','SELECT')
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__integer as vw WITH(NOEXPAND)
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
			and vw.columnValue = cv.conditionValueInteger
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'neq' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********************** */
/* md_xxxx, neq, decimal2  */
/* *********************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='DECIMAL2') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'DECIMAL2'
	and tblc.displayTypeCode in ('RADIO','SELECT')
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND)
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
			and vw.valueID = cv.conditionValueInteger
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
	)	

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'DECIMAL2'
	and tblc.displayTypeCode not in ('RADIO','SELECT')
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND)
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
			and vw.columnValue = cv.conditionValueDecimal2
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'neq' and dataTypeCode = 'DECIMAL2'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* md_xxxx, neq, date  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'DATE'
	and tblc.displayTypeCode in ('RADIO','SELECT')
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__date as vw WITH(NOEXPAND)
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
			and vw.valueID = cv.conditionValueInteger
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
	)	

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'DATE'
	and tblc.displayTypeCode not in ('RADIO','SELECT')
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__date as vw WITH(NOEXPAND)
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
			and vw.columnValue = cv.conditionValueDate
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'neq' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* md_xxxx, lt, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue < cv.conditionValueString
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'lt' 
	and tblc.dataTypeCode = 'STRING'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'lt' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, lt, int  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue < cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'lt' 
	and tblc.dataTypeCode = 'INTEGER'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'lt' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************** */
/* md_xxxx, lt, decimal2  */
/* ********************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='DECIMAL2') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue < cv.conditionValueDecimal2
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'lt' 
	and tblc.dataTypeCode = 'DECIMAL2'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'lt' and dataTypeCode = 'DECIMAL2'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, lt, date  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue < cv.conditionValueDate
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'lt' 
	and tblc.dataTypeCode = 'DATE'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'lt' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* md_xxxx, lte, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue <= cv.conditionValueString
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'lte' 
	and tblc.dataTypeCode = 'STRING'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'lte' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, lte, int  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue <= cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'lte' 
	and tblc.dataTypeCode = 'INTEGER'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'lte' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************** */
/* md_xxxx, lte, decimal2  */
/* ********************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='DECIMAL2') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue <= cv.conditionValueDecimal2
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'lte' 
	and tblc.dataTypeCode = 'DECIMAL2'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'lte' and dataTypeCode = 'DECIMAL2'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, lte, date  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue <= cv.conditionValueDate
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'lte' 
	and tblc.dataTypeCode = 'DATE'                                                         

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'lte' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* md_xxxx, gt, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue > cv.conditionValueString
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'gt' 
	and tblc.dataTypeCode = 'STRING'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'gt' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, gt, int  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue > cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'gt' 
	and tblc.dataTypeCode = 'INTEGER'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'gt' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************** */
/* md_xxxx, gt, decimal2  */
/* ********************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='DECIMAL2') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue > cv.conditionValueDecimal2
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'gt' 
	and tblc.dataTypeCode = 'DECIMAL2'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'gt' and dataTypeCode = 'DECIMAL2'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, gt, date  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue > cv.conditionValueDate
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'gt' 
	and tblc.dataTypeCode = 'DATE'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'gt' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* md_xxxx, gte, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue >= cv.conditionValueString
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'gte' 
	and tblc.dataTypeCode = 'STRING'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'gte' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, gte, int  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue >= cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'gte' 
	and tblc.dataTypeCode = 'INTEGER'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'gte' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************** */
/* md_xxxx, gte, decimal2  */
/* ********************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='DECIMAL2') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue >= cv.conditionValueDecimal2
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'gte' 
	and tblc.dataTypeCode = 'DECIMAL2'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'gte' and dataTypeCode = 'DECIMAL2'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, gte, date  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue >= cv.conditionValueDate
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'gte' 
	and tblc.dataTypeCode = 'DATE'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'gte' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, exists, int  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.vw_memberData__integer as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue is not null
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'exists' 
	and tblc.dataTypeCode = 'INTEGER'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'exists' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************** */
/* md_xxxx, exists, decimal2  */
/* ********************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='DECIMAL2') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue is not null
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'exists' 
	and tblc.dataTypeCode = 'DECIMAL2'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'exists' and dataTypeCode = 'DECIMAL2'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, exists, date  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue is not null
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'exists' 
	and tblc.dataTypeCode = 'DATE'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'exists' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, exists, CONTENTOBJ  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='CONTENTOBJ') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct md.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = tblc.fieldCodeAreaID
	inner join dbo.ams_memberdataColumnValues as mdcv on mdcv.columnID = mdc.columnID
	inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID and mdcdt.dataTypeCode = 'CONTENTOBJ'
	inner join dbo.ams_memberData as md on md.valueID = mdcv.valueID
	inner join #tblMembers as m on m.memberID = md.memberID
	inner join dbo.cms_content as c on c.siteResourceID = mdcv.columnValueSiteResourceID
	inner join dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID and cl.languageID = 1
	inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'exists' 
	and tblc.dataTypeCode = 'CONTENTOBJ'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'exists' and dataTypeCode = 'CONTENTOBJ'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, exists, DOCUMENTOBJ  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='DOCUMENTOBJ') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct md.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = tblc.fieldCodeAreaID
	inner join dbo.ams_memberdataColumnValues as mdcv on mdcv.columnID = mdc.columnID
	inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID and mdcdt.dataTypeCode = 'DOCUMENTOBJ'
	inner join dbo.ams_memberData as md on md.valueID = mdcv.valueID
	inner join #tblMembers as m on m.memberID = md.memberID
	inner join dbo.cms_documents as d on d.siteResourceID = mdcv.columnValueSiteResourceID
	inner join dbo.cms_documentLanguages as dl on dl.documentID = d.documentID and dl.languageID = 1
	inner join dbo.cms_documentVersions as dv on dv.documentLanguageID = dl.documentLanguageID and dv.isActive = 1
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'exists' 
	and tblc.dataTypeCode = 'DOCUMENTOBJ'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'exists' and dataTypeCode = 'DOCUMENTOBJ'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, not_exists, int  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'not_exists' 
	and tblc.dataTypeCode = 'INTEGER'
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__integer as vw WITH(NOEXPAND)
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
		and vw.columnValue is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'not_exists' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************** */
/* md_xxxx, not_exists, decimal2  */
/* ********************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='DECIMAL2') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'not_exists' 
	and tblc.dataTypeCode = 'DECIMAL2'
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__decimal2 as vw WITH(NOEXPAND)
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
		and vw.columnValue is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'not_exists' and dataTypeCode = 'DECIMAL2'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, not_exists, date  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'not_exists' 
	and tblc.dataTypeCode = 'DATE'
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__date as vw WITH(NOEXPAND)
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
		and vw.columnValue is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'not_exists' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, not_exists, bit  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='BIT') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'not_exists' 
	and tblc.dataTypeCode = 'BIT'
	and not exists (
		select vw.memberID
		from dbo.vw_memberData__bit as vw WITH(NOEXPAND)
		where vw.columnID = tblc.fieldCodeAreaID
		and vw.memberID = m.memberid
		and vw.columnValue is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'not_exists' and dataTypeCode = 'BIT'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, not_exists, CONTENTOBJ  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='CONTENTOBJ') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'not_exists' 
	and tblc.dataTypeCode = 'CONTENTOBJ'
	and not exists (
		select VWmd.memberID
		from dbo.ams_memberData as VWmd
		inner join dbo.ams_members as VWm on VWm.memberID = VWmd.memberID and VWm.memberID = m.memberid
		inner join dbo.ams_memberdataColumnValues as VWmdcv on VWmdcv.valueID = VWmd.valueID
		inner join dbo.ams_memberDataColumns as VWmdc on VWmdc.columnID = VWmdcv.columnID and VWmdc.columnID = tblc.fieldCodeAreaID
		where VWmdcv.columnValueSiteResourceID is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'not_exists' and dataTypeCode = 'CONTENTOBJ'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* md_xxxx, not_exists, DOCUMENTOBJ  */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='DOCUMENTOBJ') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'not_exists' 
	and tblc.dataTypeCode = 'DOCUMENTOBJ'
	and not exists (
		select VWmd.memberID
		from dbo.ams_memberData as VWmd
		inner join dbo.ams_members as VWm on VWm.memberID = VWmd.memberID and VWm.memberID = m.memberid
		inner join dbo.ams_memberdataColumnValues as VWmdcv on VWmdcv.valueID = VWmd.valueID
		inner join dbo.ams_memberDataColumns as VWmdc on VWmdc.columnID = VWmdcv.columnID and VWmdc.columnID = tblc.fieldCodeAreaID
		where VWmdcv.columnValueSiteResourceID is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'not_exists' and dataTypeCode = 'DOCUMENTOBJ'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* md_xxxx, contains, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='contains' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and vw.columnValue like '%' + cv.conditionValueString + '%'
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'contains' 
	and tblc.dataTypeCode = 'STRING'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'contains' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* md_xxxx, contains_regex, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='contains_regex' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.vw_memberData__string as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		-- add back when we have a regexfind fn
		-- and dbo.fn_RegExFind(vw.columnValue,tblc.value) = 1
		and vw.columnValue like '%' + cv.conditionValueString + '%'
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'contains_regex' 
	and tblc.dataTypeCode = 'STRING'

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'contains_regex' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* md_xxxx, datepart  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Custom Fields' and expression='datepart') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'eq'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,vw.columnValue) 
			when 'm' then datepart(m,vw.columnValue) 
			when 'wk' then datepart(wk,vw.columnValue)
			when 'dw' then datepart(dw,vw.columnValue)
			when 'd' then datepart(d,vw.columnValue) 
			when 'dy' then datepart(dy,vw.columnValue)
			else datepart(yy,vw.columnValue) end = cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'neq'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,vw.columnValue) 
			when 'm' then datepart(m,vw.columnValue) 
			when 'wk' then datepart(wk,vw.columnValue)
			when 'dw' then datepart(dw,vw.columnValue)
			when 'd' then datepart(d,vw.columnValue) 
			when 'dy' then datepart(dy,vw.columnValue)
			else datepart(yy,vw.columnValue) end <> cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lt'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,vw.columnValue) 
			when 'm' then datepart(m,vw.columnValue) 
			when 'wk' then datepart(wk,vw.columnValue)
			when 'dw' then datepart(dw,vw.columnValue)
			when 'd' then datepart(d,vw.columnValue) 
			when 'dy' then datepart(dy,vw.columnValue)
			else datepart(yy,vw.columnValue) end < cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lte'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,vw.columnValue) 
			when 'm' then datepart(m,vw.columnValue) 
			when 'wk' then datepart(wk,vw.columnValue)
			when 'dw' then datepart(dw,vw.columnValue)
			when 'd' then datepart(d,vw.columnValue) 
			when 'dy' then datepart(dy,vw.columnValue)
			else datepart(yy,vw.columnValue) end <= cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gt'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,vw.columnValue) 
			when 'm' then datepart(m,vw.columnValue) 
			when 'wk' then datepart(wk,vw.columnValue)
			when 'dw' then datepart(dw,vw.columnValue)
			when 'd' then datepart(d,vw.columnValue) 
			when 'dy' then datepart(dy,vw.columnValue)
			else datepart(yy,vw.columnValue) end > cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct vw.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gte'
	inner join dbo.vw_memberData__date as vw WITH(NOEXPAND) on vw.columnID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,vw.columnValue) 
			when 'm' then datepart(m,vw.columnValue) 
			when 'wk' then datepart(wk,vw.columnValue)
			when 'dw' then datepart(dw,vw.columnValue)
			when 'd' then datepart(d,vw.columnValue) 
			when 'dy' then datepart(dy,vw.columnValue)
			else datepart(yy,vw.columnValue) end >= cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = vw.memberid
	where tblc.fieldCodeArea = 'Custom Fields' 
	and tblc.expression = 'datepart' 

	delete from #tblCondALL 
	where fieldCodeArea = 'Custom Fields' and expression = 'datepart'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* m_xxxx, eq, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='eq' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
	inner join dbo.ams_members as m on m.memberID = tmpM.memberID
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'STRING'
	and case tblc.fieldCode
		when 'm_firstname' then isnull(m.firstname,'')
		when 'm_middlename' then isnull(m.middlename,'')
		when 'm_lastname' then isnull(m.lastname,'')
		when 'm_suffix' then isnull(m.suffix,'')
		when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
		when 'm_company' then isnull(m.company,'')
		when 'm_membernumber' then isnull(m.membernumber,'')
		else isnull(m.prefix,'') end = cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'eq' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* m_xxxx, eq, integer  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='eq' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
	inner join dbo.ams_members as m on m.memberID = tmpM.memberID
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'INTEGER'
	and isnull(m.memberTypeID,0) = cv.conditionValueInteger

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'eq' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* m_xxxx, neq, integer */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='neq' and dataTypeCode='INTEGER') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
	inner join dbo.ams_members as m on m.memberID = tmpM.memberID
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'INTEGER'
	and isnull(m.memberTypeID,0) <> cv.conditionValueInteger

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'neq' and dataTypeCode = 'INTEGER'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************** */
/* m_xxxx, neq, string  */
/* ******************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='neq' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
	inner join dbo.ams_members as m on m.memberID = tmpM.memberID
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'STRING'
	and case tblc.fieldCode
		when 'm_firstname' then isnull(m.firstname,'')
		when 'm_middlename' then isnull(m.middlename,'')
		when 'm_lastname' then isnull(m.lastname,'')
		when 'm_suffix' then isnull(m.suffix,'')
		when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
		when 'm_company' then isnull(m.company,'')
		when 'm_membernumber' then isnull(m.membernumber,'')
		else isnull(m.prefix,'') end <> cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'neq' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* m_xxxx, lt  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='lt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
	inner join dbo.ams_members as m on m.memberID = tmpM.memberID
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'lt' 
	and case tblc.fieldCode
		when 'm_firstname' then isnull(m.firstname,'')
		when 'm_middlename' then isnull(m.middlename,'')
		when 'm_lastname' then isnull(m.lastname,'')
		when 'm_suffix' then isnull(m.suffix,'')
		when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
		when 'm_company' then isnull(m.company,'')
		when 'm_membernumber' then isnull(m.membernumber,'')
		else isnull(m.prefix,'') end < cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'lt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* m_xxxx, lte  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='lte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
	inner join dbo.ams_members as m on m.memberID = tmpM.memberID
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'lte' 
	and case tblc.fieldCode
		when 'm_firstname' then isnull(m.firstname,'')
		when 'm_middlename' then isnull(m.middlename,'')
		when 'm_lastname' then isnull(m.lastname,'')
		when 'm_suffix' then isnull(m.suffix,'')
		when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
		when 'm_company' then isnull(m.company,'')
		when 'm_membernumber' then isnull(m.membernumber,'')
		else isnull(m.prefix,'') end <= cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'lte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* m_xxxx, gt  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='gt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
	inner join dbo.ams_members as m on m.memberID = tmpM.memberID
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'gt' 
	and case tblc.fieldCode
		when 'm_firstname' then isnull(m.firstname,'')
		when 'm_middlename' then isnull(m.middlename,'')
		when 'm_lastname' then isnull(m.lastname,'')
		when 'm_suffix' then isnull(m.suffix,'')
		when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
		when 'm_company' then isnull(m.company,'')
		when 'm_membernumber' then isnull(m.membernumber,'')
		else isnull(m.prefix,'') end > cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'gt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* m_xxxx, gte  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='gte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
	inner join dbo.ams_members as m on m.memberID = tmpM.memberID
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'gte' 
	and case tblc.fieldCode
		when 'm_firstname' then isnull(m.firstname,'')
		when 'm_middlename' then isnull(m.middlename,'')
		when 'm_lastname' then isnull(m.lastname,'')
		when 'm_suffix' then isnull(m.suffix,'')
		when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
		when 'm_company' then isnull(m.company,'')
		when 'm_membernumber' then isnull(m.membernumber,'')
		else isnull(m.prefix,'') end >= cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'gte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* m_xxxx, exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
	inner join dbo.ams_members as m on m.memberID = tmpM.memberID
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'exists' 
	and case tblc.fieldCode
		when 'm_firstname' then nullif(m.firstname,'')
		when 'm_middlename' then nullif(m.middlename,'')
		when 'm_lastname' then nullif(m.lastname,'')
		when 'm_suffix' then nullif(m.suffix,'')
		when 'm_professionalsuffix' then nullif(m.professionalsuffix,'')
		when 'm_company' then nullif(m.company,'')
		when 'm_membernumber' then nullif(m.membernumber,'')
		else nullif(m.prefix,'') end is not null

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* m_xxxx, not_exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='not_exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
	inner join dbo.ams_members as m on m.memberID = tmpM.memberID
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'not_exists' 
	and case tblc.fieldCode
		when 'm_firstname' then nullif(m.firstname,'')
		when 'm_middlename' then nullif(m.middlename,'')
		when 'm_lastname' then nullif(m.lastname,'')
		when 'm_suffix' then nullif(m.suffix,'')
		when 'm_professionalsuffix' then nullif(m.professionalsuffix,'')
		when 'm_company' then nullif(m.company,'')
		when 'm_membernumber' then nullif(m.membernumber,'')
		else nullif(m.prefix,'') end is null

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'not_exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* m_xxxx, contains  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='contains') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
	inner join dbo.ams_members as m on m.memberID = tmpM.memberID
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'contains' 
	and case tblc.fieldCode
		when 'm_firstname' then isnull(m.firstname,'')
		when 'm_middlename' then isnull(m.middlename,'')
		when 'm_lastname' then isnull(m.lastname,'')
		when 'm_suffix' then isnull(m.suffix,'')
		when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
		when 'm_company' then isnull(m.company,'')
		when 'm_membernumber' then isnull(m.membernumber,'')
		else isnull(m.prefix,'') end like '%' + cv.conditionValueString + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'contains'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* m_xxxx, contains_regex  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member Data' and expression='contains_regex') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
	inner join dbo.ams_members as m on m.memberID = tmpM.memberID
	where tblc.fieldCodeArea = 'Member Data' 
	and tblc.expression = 'contains_regex' 
	-- add back when we have a regexfind fn
	-- dbo.fn_RegExFind(vw.columnValue,tblc.value) = 1
	and case tblc.fieldCode
		when 'm_firstname' then isnull(m.firstname,'')
		when 'm_middlename' then isnull(m.middlename,'')
		when 'm_lastname' then isnull(m.lastname,'')
		when 'm_suffix' then isnull(m.suffix,'')
		when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
		when 'm_company' then isnull(m.company,'')
		when 'm_membernumber' then isnull(m.membernumber,'')
		else isnull(m.prefix,'') end like '%' + cv.conditionValueString + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Member Data' and expression = 'contains_regex'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, eq  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='eq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberWebsites as mw on mw.websiteTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = mw.memberID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'eq' 
	and isnull(mw.website,'') = cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'eq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, neq  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='neq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
	inner join dbo.ams_members as m on m.memberID = tmpM.memberID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'neq' 
	and not exists (
		select mw.memberID
		from dbo.ams_memberWebsites as mw
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
			and isnull(mw.website,'') = cv.conditionValueString
		where mw.websiteTypeID = tblc.fieldCodeAreaID
		and mw.memberID = m.memberid
	)	

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'neq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, lt  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='lt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberWebsites as mw on mw.websiteTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = mw.memberID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'lt' 
	and isnull(mw.website,'') < cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'lt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, lte  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='lte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberWebsites as mw on mw.websiteTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = mw.memberID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'lte' 
	and isnull(mw.website,'') <= cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'lte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, gt  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='gt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberWebsites as mw on mw.websiteTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = mw.memberID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'gt' 
	and isnull(mw.website,'') > cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'gt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, gte  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='gte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberWebsites as mw on mw.websiteTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = mw.memberID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'gte' 
	and isnull(mw.website,'') >= cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'gte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_memberWebsites as mw on mw.websiteTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = mw.memberID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'exists' 
	and nullif(mw.website,'') is not null

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, not_exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='not_exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberID = m.memberID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'not_exists' 
	and not exists (
		select mw.memberID
		from dbo.ams_memberWebsites as mw
		where mw.websiteTypeID = tblc.fieldCodeAreaID
		and mw.memberID = m.memberid
		and nullif(mw.website,'') is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'not_exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, contains  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='contains') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberWebsites as mw on mw.websiteTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = mw.memberID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'contains' 
	and isnull(mw.website,'') like '%' + cv.conditionValueString + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'contains'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mw_xxxx, contains_regex  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Websites' and expression='contains_regex') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberWebsites as mw on mw.websiteTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = mw.memberID
	where tblc.fieldCodeArea = 'Websites' 
	and tblc.expression = 'contains_regex' 
	-- add back when we have a regexfind fn
	-- dbo.fn_RegExFind(mw.website,c.value) = 1
	and isnull(mw.website,'') like '%' + cv.conditionValueString + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Websites' and expression = 'contains_regex'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, eq  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='eq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberEmails as me on me.emailTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = me.memberID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'eq' 
	and isnull(me.email,'') = cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'eq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, neq  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='neq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberID = m.memberID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'neq' 
	and not exists (
		select me.memberID
		from dbo.ams_memberEmails as me
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
			and isnull(me.email,'') = cv.conditionValueString
		where me.emailTypeID = tblc.fieldCodeAreaID
		and me.memberID = m.memberid
	)	

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'neq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, lt  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='lt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberEmails as me on me.emailTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = me.memberID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'lt' 
	and isnull(me.email,'') < cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'lt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, lte  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='lte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberEmails as me on me.emailTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = me.memberID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'lte' 
	and isnull(me.email,'') <= cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'lte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, gt  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='gt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberEmails as me on me.emailTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = me.memberID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'gt' 
	and isnull(me.email,'') > cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'gt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, gte  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='gte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberEmails as me on me.emailTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = me.memberID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'gte' 
	and isnull(me.email,'') >= cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'gte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_memberEmails as me on me.emailTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = me.memberID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'exists' 
	and nullif(me.email,'') is not null

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, not_exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='not_exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberID = m.memberID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'not_exists' 
	and not exists (
		select me.memberID
		from dbo.ams_memberEmails as me
		where me.emailTypeID = tblc.fieldCodeAreaID
		and me.memberID = m.memberid
		and nullif(me.email,'') is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'not_exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, contains  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='contains') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberEmails as me on me.emailTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = me.memberID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'contains' 
	and isnull(me.email,'') like '%' + cv.conditionValueString + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'contains'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* me_xxxx, contains_regex  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Emails' and expression='contains_regex') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberEmails as me on me.emailTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberID = me.memberID
	where tblc.fieldCodeArea = 'Emails' 
	and tblc.expression = 'contains_regex' 
	-- add back when we have a regexfind fn
	-- dbo.fn_RegExFind(me.email,tblc.value) = 1
	and isnull(me.email,'') like '%' + cv.conditionValueString + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Emails' and expression = 'contains_regex'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, eq  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='eq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID and mp.phoneTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'eq' 
	and isnull(mp.phone,'') = cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'eq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, neq  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='neq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberID = m.memberID
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'neq' 
	and not exists (
		select ma.memberid
		from dbo.ams_memberAddresses as ma 
		inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID
			and mp.phoneTypeID = tblc.fieldCodeAreaID
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
			and isnull(mp.phone,'') = cv.conditionValueString
		where ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaPartA
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'neq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, lt  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='lt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID and mp.phoneTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'lt' 
	and isnull(mp.phone,'') < cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'lt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, lte  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='lte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID and mp.phoneTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'lte' 
	and isnull(mp.phone,'') <= cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'lte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, gt  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='gt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID and mp.phoneTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'gt' 
	and isnull(mp.phone,'') > cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'gt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, gte  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='gte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID and mp.phoneTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'gte' 
	and isnull(mp.phone,'') >= cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'gte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID and mp.phoneTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'exists' 
	and nullif(mp.phone,'') is not null

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, not_exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='not_exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberID = m.memberID
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'not_exists' 
	and not exists (
		select ma.memberid
		from dbo.ams_memberAddresses as ma 
		inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID
			and mp.phoneTypeID = tblc.fieldCodeAreaID
		where ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaPartA
		and nullif(mp.phone,'') is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'not_exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, contains  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='contains') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID and mp.phoneTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'contains' 
	and isnull(mp.phone,'') like '%' + cv.conditionValueString + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'contains'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mp_xxxx, contains_regex  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Phones' and expression='contains_regex') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join dbo.ams_memberPhones as mp on mp.addressid = ma.addressID and mp.phoneTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Phones' 
	and tblc.expression = 'contains_regex' 
	-- add back when we have a regexfind fn
	-- dbo.fn_RegExFind(mp.phone,tblc.value) = 1
	and isnull(mp.phone,'') like '%' + cv.conditionValueString + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Phones' and expression = 'contains_regex'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************ */
/* ma_xxxx, neq  */
/* ************ */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='neq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberID = m.memberID
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'neq' 
	and tblc.displayTypeCode not in ('RADIO','SELECT')
	and not exists (
		select ma.memberid
		from dbo.ams_memberAddresses as ma
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
		where ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaID
		and tblc.displayTypeCode not in ('RADIO','SELECT')
		and case tblc.fieldCodeAreaPartA
			when 'address1' then isnull(ma.address1,'') 
			when 'address2' then isnull(ma.address2,'') 
			when 'address3' then isnull(ma.address3,'') 
			when 'city' then isnull(ma.city,'') 
			when 'postalcode' then isnull(ma.postalcode,'') 
			when 'county' then isnull(ma.county,'') 
			else isnull(ma.attn,'') end = cv.conditionValueString
	)

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberID = m.memberID
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'neq' 
	and tblc.displayTypeCode in ('RADIO','SELECT')
	and not exists (
		select ma.memberid
		from dbo.ams_memberAddresses as ma
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
		where ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaID
		and tblc.displayTypeCode in ('RADIO','SELECT')
		and case tblc.fieldCodeAreaPartA
			when 'stateprov' then isnull(ma.stateid,0) 
			else isnull(ma.countryid,0) end = cv.conditionValueInteger
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'neq'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************ */
/* ma_xxxx, lt  */
/* ************ */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='lt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'lt' 
	and case tblc.fieldCodeAreaPartA
		when 'address1' then isnull(ma.address1,'') 
		when 'address2' then isnull(ma.address2,'') 
		when 'address3' then isnull(ma.address3,'') 
		when 'city' then isnull(ma.city,'') 
		when 'postalcode' then isnull(ma.postalcode,'') 
		when 'county' then isnull(ma.county,'') 
		else isnull(ma.attn,'') end < cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'lt'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************ */
/* ma_xxxx, lte  */
/* ************ */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='lte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'lte' 
	and case tblc.fieldCodeAreaPartA
		when 'address1' then isnull(ma.address1,'') 
		when 'address2' then isnull(ma.address2,'') 
		when 'address3' then isnull(ma.address3,'') 
		when 'city' then isnull(ma.city,'') 
		when 'postalcode' then isnull(ma.postalcode,'') 
		when 'county' then isnull(ma.county,'') 
		else isnull(ma.attn,'') end <= cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'lte'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************ */
/* ma_xxxx, gt  */
/* ************ */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='gt') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'gt' 
	and case tblc.fieldCodeAreaPartA
		when 'address1' then isnull(ma.address1,'') 
		when 'address2' then isnull(ma.address2,'') 
		when 'address3' then isnull(ma.address3,'') 
		when 'city' then isnull(ma.city,'') 
		when 'postalcode' then isnull(ma.postalcode,'') 
		when 'county' then isnull(ma.county,'') 
		else isnull(ma.attn,'') end > cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'gt'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************ */
/* ma_xxxx, gte  */
/* ************ */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='gte') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'gte' 
	and case tblc.fieldCodeAreaPartA
		when 'address1' then isnull(ma.address1,'') 
		when 'address2' then isnull(ma.address2,'') 
		when 'address3' then isnull(ma.address3,'') 
		when 'city' then isnull(ma.city,'') 
		when 'postalcode' then isnull(ma.postalcode,'') 
		when 'county' then isnull(ma.county,'') 
		else isnull(ma.attn,'') end >= cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'gte'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************ */
/* ma_xxxx, exists  */
/* ************ */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'exists' 
	and tblc.displayTypeCode not in ('RADIO','SELECT')
	and case tblc.fieldCodeAreaPartA
		when 'address1' then nullif(ma.address1,'') 
		when 'address2' then nullif(ma.address2,'') 
		when 'address3' then nullif(ma.address3,'') 
		when 'city' then nullif(ma.city,'') 
		when 'postalcode' then nullif(ma.postalcode,'') 
		when 'county' then nullif(ma.county,'') 
		else nullif(ma.attn,'') end is not null

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'exists' 
	and tblc.displayTypeCode in ('RADIO','SELECT')
	and case tblc.fieldCodeAreaPartA
		when 'stateprov' then ma.stateid
		else ma.countryid end is not null

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'exists'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* ma_xxxx, not_exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='not_exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberID = m.memberID
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'not_exists' 
	and tblc.displayTypeCode not in ('RADIO','SELECT')
	and not exists (
		select ma.memberid
		from dbo.ams_memberAddresses as ma
		where ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaID
		and tblc.displayTypeCode not in ('RADIO','SELECT')
		and case tblc.fieldCodeAreaPartA
			when 'address1' then nullif(ma.address1,'') 
			when 'address2' then nullif(ma.address2,'') 
			when 'address3' then nullif(ma.address3,'') 
			when 'city' then nullif(ma.city,'') 
			when 'postalcode' then nullif(ma.postalcode,'') 
			when 'county' then nullif(ma.county,'') 
			else nullif(ma.attn,'') end is not null
	)

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberID = m.memberID
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'not_exists' 
	and tblc.displayTypeCode in ('RADIO','SELECT')
	and not exists (
		select ma.memberid
		from dbo.ams_memberAddresses as ma
		where ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaID
		and tblc.displayTypeCode in ('RADIO','SELECT')
		and case tblc.fieldCodeAreaPartA
			when 'stateprov' then ma.stateid
			else ma.countryid end is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'not_exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************ */
/* ma_xxxx, contains  */
/* ************ */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='contains') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'contains' 
	and case tblc.fieldCodeAreaPartA
		when 'address1' then isnull(ma.address1,'') 
		when 'address2' then isnull(ma.address2,'') 
		when 'address3' then isnull(ma.address3,'') 
		when 'city' then isnull(ma.city,'') 
		when 'postalcode' then isnull(ma.postalcode,'') 
		when 'county' then isnull(ma.county,'') 
		else isnull(ma.attn,'') end like '%' + cv.conditionValueString + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'contains'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************ */
/* ma_xxxx, contains_regex  */
/* ************ */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Addresses' and expression='contains_regex') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = ma.memberid
	where tblc.fieldCodeArea = 'Addresses' 
	and tblc.expression = 'contains_regex' 
	and case tblc.fieldCodeAreaPartA
		when 'address1' then isnull(ma.address1,'') 
		when 'address2' then isnull(ma.address2,'') 
		when 'address3' then isnull(ma.address3,'') 
		when 'city' then isnull(ma.city,'') 
		when 'postalcode' then isnull(ma.postalcode,'') 
		when 'county' then isnull(ma.county,'') 
		else isnull(ma.attn,'') end like '%' + cv.conditionValueString + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Addresses' and expression = 'contains_regex'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, neq, string  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='neq' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberID = m.memberID
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'STRING'
	and not exists (
		select mpl.memberid
		from dbo.ams_memberProfessionalLicenses as mpl
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
		left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
		where mpl.PLTypeID = tblc.fieldCodeAreaID
		and mpl.memberid = m.memberid
		and case tblc.fieldCodeAreaPartA
			when 'status' then isnull(mpls.statusName,'') 
			else isnull(mpl.licensenumber,'') end = cv.conditionValueString
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'neq' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, lt, string  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='lt' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'lt' 
	and tblc.dataTypeCode = 'STRING'
	and case tblc.fieldCodeAreaPartA
		when 'status' then isnull(mpls.statusName,'') 
		else isnull(mpl.licensenumber,'') end < cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'lt' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, lte, string  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='lte' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'lte' 
	and tblc.dataTypeCode = 'STRING'
	and case tblc.fieldCodeAreaPartA
		when 'status' then isnull(mpls.statusName,'') 
		else isnull(mpl.licensenumber,'') end <= cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'lte' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, gt, string  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='gt' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'gt' 
	and tblc.dataTypeCode = 'STRING'
	and case tblc.fieldCodeAreaPartA
		when 'status' then isnull(mpls.statusName,'') 
		else isnull(mpl.licensenumber,'') end > cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'gt' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, gte, string  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='gte' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'gte' 
	and tblc.dataTypeCode = 'STRING'
	and case tblc.fieldCodeAreaPartA
		when 'status' then isnull(mpls.statusName,'') 
		else isnull(mpl.licensenumber,'') end >= cv.conditionValueString

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'gte' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, not_exists, string  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='not_exists' and dataTypeCode='STRING') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberID = m.memberID
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'not_exists' 
	and tblc.dataTypeCode = 'STRING'
	and not exists (
		select mpl.memberid
		from dbo.ams_memberProfessionalLicenses as mpl 
		where mpl.PLTypeID = tblc.fieldCodeAreaID
		and mpl.memberid = m.memberid
		and case tblc.fieldCodeAreaPartA
			when 'status' then mpl.PLStatusID
			else nullif(mpl.licensenumber,'') end is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'not_exists' and dataTypeCode = 'STRING'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, contains  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='contains') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'contains' 
	and case tblc.fieldCodeAreaPartA
		when 'status' then isnull(mpls.statusName,'') 
		else isnull(mpl.licensenumber,'') end like '%' + cv.conditionValueString + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'contains'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, contains_regex  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='contains_regex') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls on mpls.PLStatusID = mpl.PLStatusID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'contains_regex' 
	and case tblc.fieldCodeAreaPartA
		when 'status' then isnull(mpls.statusName,'') 
		else isnull(mpl.licensenumber,'') end like '%' + cv.conditionValueString + '%'

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'contains_regex'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, eq, date  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='eq' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'eq' 
	and tblc.dataTypeCode = 'DATE'
	and mpl.activeDate = cv.conditionValueDate

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'eq' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, neq, date  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='neq' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'neq' 
	and tblc.dataTypeCode = 'DATE'
	and not exists (
		select mpl.memberid
		from dbo.ams_memberProfessionalLicenses as mpl 
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
			and mpl.activeDate = cv.conditionValueDate
		where mpl.PLTypeID = tblc.fieldCodeAreaID
		and mpl.memberid = m.memberid
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'neq' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, lt, date  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='lt' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'lt' 
	and tblc.dataTypeCode = 'DATE'
	and mpl.activeDate < cv.conditionValueDate

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'lt' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, lte, date  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='lte' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'lte' 
	and tblc.dataTypeCode = 'DATE'
	and mpl.activeDate <= cv.conditionValueDate

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'lte' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, gt, date  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='gt' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'gt' 
	and tblc.dataTypeCode = 'DATE'
	and mpl.activeDate > cv.conditionValueDate

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'gt' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, gte, date  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='gte' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'gte' 
	and tblc.dataTypeCode = 'DATE'
	and mpl.activeDate >= cv.conditionValueDate

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'gte' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, exists, date  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='exists' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'exists' 
	and tblc.dataTypeCode = 'DATE'
	and mpl.activeDate is not null

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'exists' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* mpl_xxxx, not_exists, date  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='not_exists' and dataTypeCode='DATE') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'not_exists' 
	and tblc.dataTypeCode = 'DATE'
	and not exists (
		select mpl.memberid
		from dbo.ams_memberProfessionalLicenses as mpl 
		where mpl.PLTypeID = tblc.fieldCodeAreaID
		and mpl.memberid = m.memberid
		and mpl.activeDate is not null
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'not_exists' and dataTypeCode = 'DATE'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* mpl_xxxx, datepart  */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Professional Licenses' and expression='datepart') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'eq'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,mpl.activeDate) 
			when 'm' then datepart(m,mpl.activeDate) 
			when 'wk' then datepart(wk,mpl.activeDate)
			when 'dw' then datepart(dw,mpl.activeDate)
			when 'd' then datepart(d,mpl.activeDate) 
			when 'dy' then datepart(dy,mpl.activeDate)
			else datepart(yy,mpl.activeDate) end = cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'neq'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,mpl.activeDate) 
			when 'm' then datepart(m,mpl.activeDate) 
			when 'wk' then datepart(wk,mpl.activeDate)
			when 'dw' then datepart(dw,mpl.activeDate)
			when 'd' then datepart(d,mpl.activeDate) 
			when 'dy' then datepart(dy,mpl.activeDate)
			else datepart(yy,mpl.activeDate) end <> cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lt'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,mpl.activeDate) 
			when 'm' then datepart(m,mpl.activeDate) 
			when 'wk' then datepart(wk,mpl.activeDate)
			when 'dw' then datepart(dw,mpl.activeDate)
			when 'd' then datepart(d,mpl.activeDate) 
			when 'dy' then datepart(dy,mpl.activeDate)
			else datepart(yy,mpl.activeDate) end < cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'lte'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,mpl.activeDate) 
			when 'm' then datepart(m,mpl.activeDate) 
			when 'wk' then datepart(wk,mpl.activeDate)
			when 'dw' then datepart(dw,mpl.activeDate)
			when 'd' then datepart(d,mpl.activeDate) 
			when 'dy' then datepart(dy,mpl.activeDate)
			else datepart(yy,mpl.activeDate) end <= cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gt'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,mpl.activeDate) 
			when 'm' then datepart(m,mpl.activeDate) 
			when 'wk' then datepart(wk,mpl.activeDate)
			when 'dw' then datepart(dw,mpl.activeDate)
			when 'd' then datepart(d,mpl.activeDate) 
			when 'dy' then datepart(dy,mpl.activeDate)
			else datepart(yy,mpl.activeDate) end > cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datepart' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tblc.conditionID
	inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.dateexpressionID and e.expression = 'gte'
	inner join dbo.ams_memberProfessionalLicenses as mpl on mpl.PLTypeID = tblc.fieldCodeAreaID
		and case c.[datePart]
			when 'q' then datepart(q,mpl.activeDate) 
			when 'm' then datepart(m,mpl.activeDate) 
			when 'wk' then datepart(wk,mpl.activeDate)
			when 'dw' then datepart(dw,mpl.activeDate)
			when 'd' then datepart(d,mpl.activeDate) 
			when 'dy' then datepart(dy,mpl.activeDate)
			else datepart(yy,mpl.activeDate) end >= cv.conditionValueInteger
	inner join #tblMembers as m on m.memberid = mpl.memberid
	where tblc.fieldCodeArea = 'Professional Licenses' 
	and tblc.expression = 'datepart' 

	delete from #tblCondALL 
	where fieldCodeArea = 'Professional Licenses' and expression = 'datepart'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* e_xxxx, attended  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Events' and expression='attended') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ev_events as e on e.eventID = tblc.fieldCodeAreaID
	inner join dbo.ev_registration as er on er.eventID = e.eventID and er.status = 'A'
	inner join dbo.ev_registrants as reg on reg.registrationID = er.registrationID and reg.status = 'A'
		and reg.attended = 1
	inner join dbo.ams_members as m2 on m2.memberid = reg.memberid
	inner join #tblMembers as m on m.memberID = m2.activeMemberID
	where tblc.fieldCodeArea = 'Events' 
	and tblc.expression = 'attended' 

	delete from #tblCondALL 
	where fieldCodeArea = 'Events' and expression = 'attended'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ******************* */
/* e_xxxx, awarded  */
/* ******************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Events' and expression='awarded') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ev_events as e on e.eventID = tblc.fieldCodeAreaID
	inner join dbo.ev_registration as er on er.eventID = e.eventID and er.status = 'A'
	inner join dbo.ev_registrants as reg on reg.registrationID = er.registrationID and reg.status = 'A'
		and reg.attended = 1
	inner join dbo.crd_requests as rc on rc.registrantID = reg.registrantID
		and rc.creditAwarded = 1
	inner join dbo.ams_members as m2 on m2.memberid = reg.memberid
	inner join #tblMembers as m on m.memberID = m2.activeMemberID
	where tblc.fieldCodeArea = 'Events' 
	and tblc.expression = 'awarded' 

	delete from #tblCondALL 
	where fieldCodeArea = 'Events' and expression = 'awarded'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************* */
/* mad_xxxx, eq  */
/* ************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Districting' and expression='eq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join #tblMembers as m on m.memberid = ma.memberid
	inner join dbo.ams_memberAddressData as mad on mad.addressid = ma.addressID
	inner join dbo.ams_memberDistrictValues as mdv on mdv.valueID = mad.valueID
		and mdv.districtTypeID = tblc.fieldCodeAreaID
		and mdv.valueID = cv.conditionValueInteger
	where tblc.fieldCodeArea = 'Districting' 
	and tblc.expression = 'eq' 

	delete from #tblCondALL 
	where fieldCodeArea = 'Districting' and expression = 'eq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************** */
/* mad_xxxx, neq  */
/* ************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Districting' and expression='neq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberID
	where tblc.fieldCodeArea = 'Districting' 
	and tblc.expression = 'neq' 
	and not exists (
		select ma.memberid
		from dbo.ams_memberAddresses as ma
		inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
		inner join dbo.ams_memberAddressData as mad on mad.addressid = ma.addressID
		inner join dbo.ams_memberDistrictValues as mdv on mdv.valueID = mad.valueID
			and mdv.districtTypeID = tblc.fieldCodeAreaID
			and mdv.valueID = cv.conditionValueInteger
		where ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaPartA
	)
	
	delete from #tblCondALL 
	where fieldCodeArea = 'Districting' and expression = 'neq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mad_xxxx, exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Districting' and expression='exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaPartA
	inner join #tblMembers as m on m.memberid = ma.memberid
	inner join dbo.ams_memberAddressData as mad on mad.addressid = ma.addressID
	inner join dbo.ams_memberDistrictValues as mdv on mdv.valueID = mad.valueID
		and mdv.districtTypeID = tblc.fieldCodeAreaID
	where tblc.fieldCodeArea = 'Districting' 
	and tblc.expression = 'exists' 

	delete from #tblCondALL 
	where fieldCodeArea = 'Districting' and expression = 'exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *********** */
/* mad_xxxx, not_exists  */
/* *********** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Districting' and expression='not_exists') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMembers as m on m.memberid = m.memberid
	where tblc.fieldCodeArea = 'Districting' 
	and tblc.expression = 'not_exists' 
	and not exists (
		select ma.memberid
		from dbo.ams_memberAddresses as ma
		inner join dbo.ams_memberAddressData as mad on mad.addressid = ma.addressID
		inner join dbo.ams_memberDistrictValues as mdv on mdv.valueID = mad.valueID
			and mdv.districtTypeID = tblc.fieldCodeAreaID
		where ma.memberid = m.memberid
		and ma.addressTypeID = tblc.fieldCodeAreaPartA
	)

	delete from #tblCondALL 
	where fieldCodeArea = 'Districting' and expression = 'not_exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* acct_allocSum, eq */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='eq') BEGIN
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct memberid, conditionID
	from (
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = @orgID and allocT.typeID = 5
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'eq' 
			union all
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = @orgID and VOT.typeID = 8
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
		inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'eq' 
	) as tmp
	group by memberid, conditionID, conditionValue
	having sum(allocAmt) = conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Accounting' and fieldCode='acct_allocsum' and expression='eq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ****************** */
/* acct_allocSum, neq */
/* ****************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='neq') BEGIN

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct memberid, conditionID
	from (
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = @orgID and allocT.typeID = 5
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'neq' 
			union all
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = @orgID and VOT.typeID = 8
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
		inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'neq' 
	) as tmp
	group by memberid, conditionID, conditionValue
	having sum(allocAmt) <> conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Accounting' and fieldCode='acct_allocsum' and expression='neq'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* acct_allocSum, lt */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='lt') BEGIN

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct memberid, conditionID
	from (
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = @orgID and allocT.typeID = 5
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'lt' 
			union all
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = @orgID and VOT.typeID = 8
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
		inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'lt' 
	) as tmp
	group by memberid, conditionID, conditionValue
	having sum(allocAmt) < conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Accounting' and fieldCode='acct_allocsum' and expression='lt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* acct_allocSum, lte */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='lte') BEGIN

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct memberid, conditionID
	from (
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = @orgID and allocT.typeID = 5
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'lte' 
			union all
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = @orgID and VOT.typeID = 8
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
		inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'lte' 
	) as tmp
	group by memberid, conditionID, conditionValue
	having sum(allocAmt) <= conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Accounting' and fieldCode='acct_allocsum' and expression='lte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* acct_allocSum, gt */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='gt') BEGIN

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct memberid, conditionID
	from (
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = @orgID and allocT.typeID = 5
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'gt' 
			union all
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = @orgID and VOT.typeID = 8
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
		inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'gt' 
	) as tmp
	group by memberid, conditionID, conditionValue
	having sum(allocAmt) > conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Accounting' and fieldCode='acct_allocsum' and expression='gt'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ***************** */
/* acct_allocSum, gte */
/* ***************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='gte') BEGIN

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct memberid, conditionID
	from (
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = @orgID and allocT.typeID = 5
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'gte' 
			union all
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = @orgID and VOT.typeID = 8
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
		inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'gte' 
	) as tmp
	group by memberid, conditionID, conditionValue
	having sum(allocAmt) >= conditionValue

	delete from #tblCondALL 
	where fieldCodeArea = 'Accounting' and fieldCode='acct_allocsum' and expression='gte'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************** */
/* acct_allocSum, between */
/* ********************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='between') BEGIN

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct memberid, conditionID
	from (
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValueLower, accsplit.conditionValueUpper
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = @orgID and allocT.typeID = 5
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'between' 
			union all
		select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValueLower, accsplit.conditionValueUpper
		from #tblCondALL as tblc
		inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
		inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = @orgID and VOT.typeID = 8
		inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
		inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
		inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
		inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
		inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
		inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
		inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
		inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
		where tblc.expression = 'between' 
	) as tmp
	group by memberid, conditionID, conditionValueLower, conditionValueUpper
	having sum(allocAmt) between conditionValueLower and conditionValueUpper

	delete from #tblCondALL 
	where fieldCodeArea = 'Accounting' and fieldCode='acct_allocsum' and expression='between'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ********************* */
/* acct_allocSum, exists */
/* ********************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='exists') BEGIN

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = @orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.expression = 'exists' 
		union
	select m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = @orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.expression = 'exists' 

	delete from #tblCondALL 
	where fieldCodeArea = 'Accounting' and fieldCode='acct_allocsum' and expression='exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* ************************* */
/* acct_allocSum, not_exists */
/* ************************* */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='not_exists') BEGIN

	select m.memberid, tblc.conditionID
	into #tblCondAccNotExist
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = @orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.expression = 'not_exists' 
		union
	select m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = @orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID and allocR.typeID = 3
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.expression = 'not_exists' 

	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct mOuter.memberid, tblcOuter.conditionID
	from #tblCondALL as tblcOuter
	inner join #tblMembers as mOuter on mOuter.memberid = mOuter.memberID
	where tblcOuter.expression = 'not_exists' 
	except
	select memberid, conditionID
	from #tblCondAccNotExist

	delete from #tblCondALL 
	where fieldCodeArea = 'Accounting' and fieldCode='acct_allocsum' and expression='not_exists'
END 
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* *************** */
/* rt_role, linked */
/* *************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Record Types' and fieldCode='rt_role' and expression='linked') BEGIN

	-- no roles defined just a record type	
	IF (select count(*) from #tblRecSplitRoles) = 0 BEGIN
		insert into #cache_members_conditions_shouldbe (memberid, conditionID)
		select distinct m.memberid, tblc.conditionID	
		from #tblCondALL as tblc
		inner join #tblRecSplit as recsplit on recsplit.conditionID = tblc.conditionID
		inner join dbo.ams_members as m2 on m2.recordtypeID = recsplit.recordTypeID
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		where tblc.expression = 'linked' 
	END ELSE BEGIN
		-- roles defined
		insert into #cache_members_conditions_shouldbe (memberid, conditionID)
		select distinct m.memberid, tblc.conditionID	
		from #tblCondALL as tblc
		inner join #tblRecSplit as recsplit on recsplit.conditionID = tblc.conditionID
		inner join dbo.ams_members as m2 on m2.recordtypeID = recsplit.recordTypeID
		inner join #tblMembers as m on m.memberid = m2.activeMemberID
		inner join dbo.ams_recordRelationships as rr on rr.masterMemberID = m.memberid or rr.childMemberID = m.memberid
		inner join dbo.ams_recordTypesRelationshipTypes as rtrt on rtrt.recordTypeRelationshipTypeID = rr.recordTypeRelationshipTypeID 
		inner join #tblRecSplitRoles as roles on roles.role = rtrt.recordTypeRelationshipTypeID
		where rr.isactive = 1
		and rtrt.isActive = 1
		and tblc.expression = 'linked' 
	END

	delete from #tblCondALL 
	where fieldCodeArea = 'Record Types' and fieldCode='rt_role' and expression='linked'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc


/* **************** */
/* mh_entry, exists */
/* **************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Member History' and fieldCode='mh_entry' and expression='exists') BEGIN

	-- when no subcategories defined	
	insert into #cache_members_conditions_shouldbe  (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblMHSplit as mhsplit on mhsplit.conditionID = tblc.conditionID
	inner join dbo.ams_memberHistory as mh on mh.categoryID = mhsplit.historyCategory
		and mh.typeID = 1
		and mh.dateEntered >= isnull(mhsplit.historyEnteredDateLower,mh.dateEntered)
		and mh.dateEntered <= isnull(mhsplit.historyEnteredDateUpper,mh.dateEntered)
		and (
			(mh.userDate is null and mhsplit.historyDateLower is null and mhsplit.historyDateUpper is null)
			or 
			(mh.userDate >= isnull(mhsplit.historyDateLower,mh.userDate) and mh.userDate <= isnull(mhsplit.historyDateUpper,mh.userDate))
		)
		and (
			(mh.quantity is null and mhsplit.historyQuantityLower is null and mhsplit.historyQuantityUpper is null)
			or 
			(mh.quantity >= isnull(mhsplit.historyQuantityLower,mh.quantity) and mh.quantity <= isnull(mhsplit.historyQuantityUpper,mh.quantity))
		)
		and (
			(mh.dollarAmt is null and mhsplit.historyAmountLower is null and mhsplit.historyAmountUpper is null)
			or 
			(mh.dollarAmt >= isnull(mhsplit.historyAmountLower,mh.dollarAmt) and mh.dollarAmt <= isnull(mhsplit.historyAmountUpper,mh.dollarAmt))
		)
		and (
			(mh.description is null and mhsplit.historyDescriptionContains is null)
			or 
			(mh.description like '%' + isnull(mhsplit.historyDescriptionContains,mh.description) + '%')
		)
	inner join dbo.cms_categories as c on c.categoryID = mh.categoryID
	inner join dbo.cms_categoryTrees as ct on ct.categoryTreeID = c.categoryTreeID
	inner join dbo.sites as s on s.siteID = ct.siteID and s.orgID = @orgID
	inner join dbo.ams_members as m2 on m2.memberID = mh.memberID
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	left outer join #tblMHSubCategoriesSplit as mhsubcats on mhsubcats.historyCategory = mhsplit.historyCategory
		and mhsubcats.conditionID = mhsplit.conditionID
	where tblc.expression = 'exists'
	and mhsubcats.conditionID is null

	-- when subcategories defined	
	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID	
	from #tblCondALL as tblc
	inner join #tblMHSplit as mhsplit on mhsplit.conditionID = tblc.conditionID
	inner join #tblMHSubCategoriesSplit as mhsubcats on mhsubcats.historyCategory = mhsplit.historyCategory
		and mhsubcats.conditionID = mhsplit.conditionID
	inner join dbo.ams_memberHistory as mh on mh.categoryID = mhsplit.historyCategory
		and mh.subcategoryID = mhsubcats.historySubCategory
		and mh.typeID = 1
		and mh.dateEntered >= isnull(mhsplit.historyEnteredDateLower,mh.dateEntered)
		and mh.dateEntered <= isnull(mhsplit.historyEnteredDateUpper,mh.dateEntered)
		and (
			(mh.userDate is null and mhsplit.historyDateLower is null and mhsplit.historyDateUpper is null)
			or 
			(mh.userDate >= isnull(mhsplit.historyDateLower,mh.userDate) and mh.userDate <= isnull(mhsplit.historyDateUpper,mh.userDate))
		)
		and (
			(mh.quantity is null and mhsplit.historyQuantityLower is null and mhsplit.historyQuantityUpper is null)
			or 
			(mh.quantity >= isnull(mhsplit.historyQuantityLower,mh.quantity) and mh.quantity <= isnull(mhsplit.historyQuantityUpper,mh.quantity))
		)
		and (
			(mh.dollarAmt is null and mhsplit.historyAmountLower is null and mhsplit.historyAmountUpper is null)
			or 
			(mh.dollarAmt >= isnull(mhsplit.historyAmountLower,mh.dollarAmt) and mh.dollarAmt <= isnull(mhsplit.historyAmountUpper,mh.dollarAmt))
		)
		and (
			(mh.description is null and mhsplit.historyDescriptionContains is null)
			or 
			(mh.description like '%' + isnull(mhsplit.historyDescriptionContains,mh.description) + '%')
		)
	inner join dbo.cms_categories as c on c.categoryID = mh.categoryID
	inner join dbo.cms_categoryTrees as ct on ct.categoryTreeID = c.categoryTreeID
	inner join dbo.sites as s on s.siteID = ct.siteID and s.orgID = @orgID
	inner join dbo.ams_members as m2 on m2.memberID = mh.memberID
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	where tblc.expression = 'exists'

	delete from #tblCondALL 
	where fieldCodeArea = 'Member History' and fieldCode='mh_entry' and expression='exists'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc

/* **************** */
/* l_entry, exists */
/* **************** */
IF EXISTS (select top 1 conditionID from #tblCondALL where fieldCodeArea='Listserver Memberships' and fieldCode='l_entry' and expression='onlist') BEGIN


	insert into #cache_members_conditions_shouldbe (memberid, conditionID)
	select distinct m.memberid, tblc.conditionID	
	from #tblCondALL as tblc
	inner join #tblLyrisListSplit as listsplit
		on listsplit.conditionID = tblc.conditionID
	inner join #tblLyrisMemTypeSplit as msplit
		on msplit.conditionID = tblc.conditionID
	inner join #tblLyrisSubTypeSplit as ssplit
		on ssplit.conditionID = tblc.conditionID
	inner join #tblLyrisOptionalSplit as osplit
		on osplit.conditionID = tblc.conditionID
	inner join organizations o
		on o.orgID = @orgID
	inner join datatransfer.dbo.cache_lyris_IntegratedMembers im
		on im.list_ = listsplit.list_
		and im.orgcode = o.orgcode
		and im.membertype_ = msplit.membertype_
		and im.subtype_ = ssplit.subtype_
		and im.mcoption_lockAddress = isnull(osplit.listLockAddress,im.mcoption_lockAddress)
		and im.mcoption_keepactive = isnull(osplit.listKeepActive,im.mcoption_keepactive)
		and im.dateJoined_ >= isnull(osplit.listJoinDateLower,im.dateJoined_)
		and im.dateJoined_ <= isnull(osplit.listJoinDateUpper,im.dateJoined_)
		and (
			(osplit.listExpireDateLower is null and osplit.listExpireDateUpper is null)
			or
			(im.expireDate_ >= isnull(osplit.listExpireDateLower,im.expireDate_) and im.expireDate_ <= isnull(osplit.listExpireDateUpper,im.expireDate_))
		)
	inner join dbo.ams_members as m2 
		on m2.memberNumber = im.externalMemberID
		and m2.orgID = o.orgID
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	where tblc.expression = 'onlist'

	delete from #tblCondALL 
	where fieldCodeArea='Listserver Memberships' and fieldCode='l_entry' and expression='onlist'
END
ELSE IF NOT EXISTS (select top 1 conditionID from #tblCondALL) GOTO donecalc



donecalc:
	select @totalID = Count(*) from #cache_members_conditions_shouldbe
	
	-- Log
	set @starttime2 = getdate()
	INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage)
	VALUES (@logTreeID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'Start changes to real tables');

	-- delete member/conditions that should not be there
	delete cmc
	from dbo.cache_members_conditions as cmc
	inner join #tblCondALLFinal as caf on caf.conditionID = cmc.conditionID
	inner join #tblMembers as m on m.memberid = cmc.memberid
	and not exists (
		select *
		from #cache_members_conditions_shouldbe
		where conditionID = cmc.conditionID
		and memberid = cmc.memberid
	)	
		select @totalRemoved = @@rowcount
		
	-- insert member/conditions that should be but arent already there
	insert into dbo.cache_members_conditions (memberID, conditionID)
	select distinct cache.memberID, cache.conditionID
	from #cache_members_conditions_shouldbe as cache
	where not exists (
		select memberid, conditionid
		from dbo.cache_members_conditions
		where memberid = cache.memberid
		and conditionID = cache.conditionID
	)
		select @totalAdded = @@rowcount

	-- log
	INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage, totalMS)
	VALUES (@logTreeID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'End changes to real tables', Datediff(ms,@starttime2,getdate()));

	-- return query of members affected
	select distinct memberID
	from #tblMembers
	
	-- cleanup temp tables
	IF OBJECT_ID('tempdb..#cache_members_conditions_shouldbe') IS NOT NULL
		DROP TABLE #cache_members_conditions_shouldbe
	IF OBJECT_ID('tempdb..#tblCond') IS NOT NULL
		DROP TABLE #tblCond
	IF OBJECT_ID('tempdb..#tblCondALL') IS NOT NULL
		DROP TABLE #tblCondALL
	IF OBJECT_ID('tempdb..#tblCondALLFinal') IS NOT NULL
		DROP TABLE #tblCondALLFinal
	IF OBJECT_ID('tempdb..#tblFCSplit') IS NOT NULL
		DROP TABLE #tblFCSplit
	IF OBJECT_ID('tempdb..#tblSubSplit') IS NOT NULL
		DROP TABLE #tblSubSplit
	IF OBJECT_ID('tempdb..#tblAccSplit') IS NOT NULL
		DROP TABLE #tblAccSplit
	IF OBJECT_ID('tempdb..#tblAccSplitGL') IS NOT NULL
		DROP TABLE #tblAccSplitGL
	IF OBJECT_ID('tempdb..#tblRecSplit') IS NOT NULL
		DROP TABLE #tblRecSplit
	IF OBJECT_ID('tempdb..#tblRecSplitRoles') IS NOT NULL
		DROP TABLE #tblRecSplitRoles
	IF OBJECT_ID('tempdb..#tblCondAccNotExist') IS NOT NULL
		DROP TABLE #tblCondAccNotExist
	IF OBJECT_ID('tempdb..#tblCondValues') IS NOT NULL
		DROP TABLE #tblCondValues
	IF OBJECT_ID('tempdb..#tblMembers') IS NOT NULL
		DROP TABLE #tblMembers
	IF OBJECT_ID('tempdb..#tblMHSplit') IS NOT NULL
		DROP TABLE #tblMHSplit
	IF OBJECT_ID('tempdb..#tblMHSubCategoriesSplit') IS NOT NULL
		DROP TABLE #tblMHSubCategoriesSplit

	-- Log
	set @totalMS = Datediff(ms,@starttime,getdate())
	INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage, totalMS)
	VALUES (@logTreeID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'End Process of orgID=' + cast(@orgID as varchar(10)) + ' conditionID=' + left(@conditionIDList,100) + ' memberID=' + left(@memberIDList,100), @totalMS);

	set nocount off
GO

CREATE INDEX IX_cache_lyris_IntegratedMembers_list_externalMemberID 
	ON datatransfer.dbo.cache_lyris_IntegratedMembers (list_ asc, externalMemberID asc)
	include (orgcode, dateJoined_, expireDate_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive);

GO
USE [dataTransfer]
GO
ALTER PROC [dbo].[cache_lyris_updateIntegratedMembers]
	@restrictToOrgcode varchar(5) = null
AS

IF OBJECT_ID('tempdb..#IntegratedMembers') IS NOT NULL
	DROP TABLE #IntegratedMembers

IF OBJECT_ID('tempdb..#IntegratedMembers_updated') IS NOT NULL
	DROP TABLE #IntegratedMembers_updated

IF OBJECT_ID('tempdb..#IntegratedMembers_removed') IS NOT NULL
	DROP TABLE #IntegratedMembers_removed

IF OBJECT_ID('tempdb..#orgcodes') IS NOT NULL
	DROP TABLE #orgcodes

IF OBJECT_ID('tempdb..#affectedMembers') IS NOT NULL
	DROP TABLE #affectedMembers

IF OBJECT_ID('tempdb..#affectedLists') IS NOT NULL
	DROP TABLE #affectedLists

CREATE TABLE #IntegratedMembers(
	memberID_ int PRIMARY KEY, 
	orgcode varchar(5), 
	externalMemberID varchar(100), 
	dateJoined_ smalldatetime, 
	expireDate_ smalldatetime, 
	list_ varchar(60), 
	membertype_ varchar(20), 
	subtype_ varchar(20), 
	mcoption_lockAddress bit, 
	mcoption_keepactive bit
)

CREATE TABLE #IntegratedMembers_updated(
	memberID_ int PRIMARY KEY,
	orgcode varchar(5), 
	externalMemberID varchar(100), 
	dateJoined_ smalldatetime, 
	expireDate_ smalldatetime, 
	list_ varchar(60), 
	membertype_ varchar(20), 
	subtype_ varchar(20), 
	mcoption_lockAddress bit, 
	mcoption_keepactive bit
)

CREATE TABLE #IntegratedMembers_removed(
	memberID_ int PRIMARY KEY, 
	orgcode varchar(5), 
	externalMemberID varchar(100), 
	dateJoined_ smalldatetime, 
	expireDate_ smalldatetime, 
	list_ varchar(60), 
	membertype_ varchar(20), 
	subtype_ varchar(20), 
	mcoption_lockAddress bit, 
	mcoption_keepactive bit
)

CREATE TABLE #orgcodes(
	orgcode varchar(5)
)

CREATE TABLE #affectedMembers(
	orgcode varchar(5), 
	externalMemberID varchar(100), 
	list_ varchar(60)
)

CREATE TABLE #affectedLists(
	list_ varchar(60) PRIMARY KEY,
	modfactor int
)


insert into #orgcodes (orgcode)
select state
from tlasites.trialsmith.dbo.depotla tla
where tla.state = isnull(@restrictToOrgcode,tla.state) collate database_default 


insert into #IntegratedMembers (memberID_, orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive)
select memberID_, lf.orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive
from lyris.trialslyris1.dbo.members_ m
inner join lyris.trialslyris1.dbo.lists_format lf
	on m.list_ = lf.name
	and m.externalMemberID is not null
inner join #orgcodes o
	on o.orgcode = lf.orgcode collate database_default

insert into #IntegratedMembers_updated (memberID_, orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive)
select memberID_, orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive
from #IntegratedMembers
except
select memberID_, cache.orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive
from dbo.[cache_lyris_IntegratedMembers] cache
inner join #orgcodes o
	on o.orgcode = cache.orgcode



insert into #IntegratedMembers_removed (memberID_, orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive)
select memberID_, cache.orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive
from dbo.[cache_lyris_IntegratedMembers] cache
inner join #orgcodes o
	on o.orgcode = cache.orgcode
except
select memberID_, orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive
from #IntegratedMembers


insert into #affectedMembers (orgcode, externalMemberID, list_)
select distinct orgcode, externalMemberID, list_
from #IntegratedMembers_removed
union
select distinct orgcode, externalMemberID, list_
from #IntegratedMembers_updated


delete cache
from dbo.[cache_lyris_IntegratedMembers] cache
inner join #IntegratedMembers_removed removed
	on removed.memberID_ = cache.memberID_

insert into dbo.[cache_lyris_IntegratedMembers] (memberID_, orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive)
select memberID_, orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive
from #IntegratedMembers_updated


-- update affected conditions
declare @conditionsToReprocess TABLE (autoID int IDENTITY(1,1) PRIMARY KEY, orgID int, conditionID int, memberlist varchar(max))
declare 
	@maxStringLength int,
	@maxListIntElements int, 
	@thisAutoID int, 
	@thisOrgID int, 
	@thisConditionID varchar(10), 
	@thisMemberList varchar(max),
	@thisItemGroupUID uniqueidentifier

set @maxStringLength = 8000
set @maxListIntElements = @maxStringLength / 12 -- sql int max int is 10 digits (2,147,483,647) plus a couple more to account for commas between list elements


insert into #affectedLists (list_, modfactor)
select list_, (count(*) / @maxListIntElements) + 1
from #affectedMembers
group by list_


insert into @conditionsToReprocess (orgID, conditionID , memberlist)
SELECT temp.orgID, temp.conditionID, membercentral.dbo.sortedIntList(temp.memberID)
from (
	SELECT vgc.orgID, vgc.conditionID, m.memberID, (row_number() over (partition by vgc.conditionID order by m.memberid) % affl.modFactor) as dataset
	FROM membercentral.dbo.ams_virtualGroupConditionValues as cv
	inner join membercentral.dbo.ams_virtualGroupConditionKeys as k
		on k.conditionKeyID = cv.conditionKeyID
		and k.conditionKey = 'listList'
	inner join membercentral.dbo.ams_virtualGroupConditions vgc
		on vgc.conditionID = cv.conditionID
	inner join #affectedMembers affm
		on affm.list_ = cv.conditionValue
	inner join #affectedLists affl
		on affl.list_ = affm.list_
	inner join membercentral.dbo.ams_members m
		on m.orgID = vgc.orgID
		and m.membernumber = affm.externalMemberID
		and m.status in ('a','i')	
		and m.memberID = m.activeMemberID
) as temp
group by temp.orgID, temp.conditionID, temp.dataset

select @thisAutoID = min(autoID) from @conditionsToReprocess
while @thisAutoID is not null
begin
	select 
		@thisOrgID = orgID, 
		@thisConditionID = cast(conditionID as varchar(10)), 
		@thisMemberList = memberList,
		@thisItemGroupUID = null
	from @conditionsToReprocess
	where autoID = @thisAutoID

	
	EXEC platformQueue.dbo.queue_processMemberGroups_insert @thisOrgID, @thisMemberList, @thisConditionID, 2, @thisItemGroupUID OUTPUT
	select @thisAutoID = min(autoID) from @conditionsToReprocess where autoID > @thisAutoID
end




IF OBJECT_ID('tempdb..#IntegratedMembers') IS NOT NULL
	DROP TABLE #IntegratedMembers

IF OBJECT_ID('tempdb..#IntegratedMembers_updated') IS NOT NULL
	DROP TABLE #IntegratedMembers_updated

IF OBJECT_ID('tempdb..#IntegratedMembers_removed') IS NOT NULL
	DROP TABLE #IntegratedMembers_removed

IF OBJECT_ID('tempdb..#orgcodes') IS NOT NULL
	DROP TABLE #orgcodes

IF OBJECT_ID('tempdb..#affectedMembers') IS NOT NULL
	DROP TABLE #affectedMembers

IF OBJECT_ID('tempdb..#affectedLists') IS NOT NULL
	DROP TABLE #affectedLists

GO

USE [customApps]
GO
ALTER PROC [dbo].[job_runHourlyCustomJobs]
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '10.36.18.90'
IF @@SERVERNAME = 'DEV04\PLATFORM2008' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'DEV03\PLATFORM2008' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'STAGING01\PLATFORM2008' BEGIN
	SET @tier = 'STAGING'
	SET @smtpserver = 'mail.trialsmith.com'
END

/* ********************** */
/* va_updateBarAndBirthDates */
/* ********************** */
BEGIN TRY
	EXEC customApps.dbo.va_updateBarAndBirthDates
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Bar and Birth Date - VA'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* va_updateVTLADistrictAndTaskforce */
/* ********************** */
BEGIN TRY
	EXEC customApps.dbo.va_updateVTLADistrictAndTaskforce
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating VTLA District and Taskforcing Areas - VA'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* *************** */
/* ny_updateRegion */
/* *************** */
BEGIN TRY
	EXEC customApps.dbo.ny_updateRegion
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Region - NY'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* *************** */
/* cache_lyris_updateIntegratedMembers */
/* *************** */
BEGIN TRY
	EXEC datatransfer.dbo.cache_lyris_updateIntegratedMembers @restrictToOrgcode=null
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': datatransfer.dbo.cache_lyris_updateIntegratedMembers'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH




RETURN 0
GO

USE [dataTransfer]
GO
ALTER PROC [dbo].[cache_lyris_updateIntegratedMembers]
	@restrictToOrgcode varchar(5) = null
AS

IF OBJECT_ID('tempdb..#IntegratedMembers') IS NOT NULL
	DROP TABLE #IntegratedMembers

IF OBJECT_ID('tempdb..#IntegratedMembers_updated') IS NOT NULL
	DROP TABLE #IntegratedMembers_updated

IF OBJECT_ID('tempdb..#IntegratedMembers_removed') IS NOT NULL
	DROP TABLE #IntegratedMembers_removed

IF OBJECT_ID('tempdb..#orgcodes') IS NOT NULL
	DROP TABLE #orgcodes

IF OBJECT_ID('tempdb..#affectedMembers') IS NOT NULL
	DROP TABLE #affectedMembers

IF OBJECT_ID('tempdb..#affectedLists') IS NOT NULL
	DROP TABLE #affectedLists

CREATE TABLE #IntegratedMembers(
	memberID_ int PRIMARY KEY, 
	orgcode varchar(5), 
	externalMemberID varchar(100), 
	dateJoined_ smalldatetime, 
	expireDate_ smalldatetime, 
	list_ varchar(60), 
	membertype_ varchar(20), 
	subtype_ varchar(20), 
	mcoption_lockAddress bit, 
	mcoption_keepactive bit
)

CREATE TABLE #IntegratedMembers_updated(
	memberID_ int PRIMARY KEY,
	orgcode varchar(5), 
	externalMemberID varchar(100), 
	dateJoined_ smalldatetime, 
	expireDate_ smalldatetime, 
	list_ varchar(60), 
	membertype_ varchar(20), 
	subtype_ varchar(20), 
	mcoption_lockAddress bit, 
	mcoption_keepactive bit
)

CREATE TABLE #IntegratedMembers_removed(
	memberID_ int PRIMARY KEY, 
	orgcode varchar(5), 
	externalMemberID varchar(100), 
	dateJoined_ smalldatetime, 
	expireDate_ smalldatetime, 
	list_ varchar(60), 
	membertype_ varchar(20), 
	subtype_ varchar(20), 
	mcoption_lockAddress bit, 
	mcoption_keepactive bit
)

CREATE TABLE #orgcodes(
	orgcode varchar(5)
)

CREATE TABLE #affectedMembers(
	orgcode varchar(5), 
	externalMemberID varchar(100), 
	list_ varchar(60)
)

CREATE TABLE #affectedLists(
	list_ varchar(60) PRIMARY KEY,
	modfactor int
)


insert into #orgcodes (orgcode)
select state
from tlasites.trialsmith.dbo.depotla tla
where tla.state = isnull(@restrictToOrgcode,tla.state) collate database_default 


insert into #IntegratedMembers (memberID_, orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive)
select memberID_, lf.orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, isnull(mcoption_lockAddress,0), isnull(mcoption_keepactive,0)
from lyris.trialslyris1.dbo.members_ m
inner join lyris.trialslyris1.dbo.lists_format lf
	on m.list_ = lf.name
	and m.externalMemberID is not null
inner join #orgcodes o
	on o.orgcode = lf.orgcode collate database_default

insert into #IntegratedMembers_updated (memberID_, orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive)
select memberID_, orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive
from #IntegratedMembers
except
select memberID_, cache.orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive
from dbo.[cache_lyris_IntegratedMembers] cache
inner join #orgcodes o
	on o.orgcode = cache.orgcode



insert into #IntegratedMembers_removed (memberID_, orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive)
select memberID_, cache.orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive
from dbo.[cache_lyris_IntegratedMembers] cache
inner join #orgcodes o
	on o.orgcode = cache.orgcode
except
select memberID_, orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive
from #IntegratedMembers


insert into #affectedMembers (orgcode, externalMemberID, list_)
select distinct orgcode, externalMemberID, list_
from #IntegratedMembers_removed
union
select distinct orgcode, externalMemberID, list_
from #IntegratedMembers_updated


delete cache
from dbo.[cache_lyris_IntegratedMembers] cache
inner join #IntegratedMembers_removed removed
	on removed.memberID_ = cache.memberID_

insert into dbo.[cache_lyris_IntegratedMembers] (memberID_, orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive)
select memberID_, orgcode, externalMemberID, dateJoined_, expireDate_, list_, membertype_, subtype_, mcoption_lockAddress, mcoption_keepactive
from #IntegratedMembers_updated


-- update affected conditions
declare @conditionsToReprocess TABLE (autoID int IDENTITY(1,1) PRIMARY KEY, orgID int, conditionlist varchar(max), memberlist varchar(max))
declare 
	@maxStringLength int,
	@maxListIntElements int, 
	@thisAutoID int, 
	@thisOrgID int, 
	@thisConditionList varchar(max), 
	@thisMemberList varchar(max),
	@thisItemGroupUID uniqueidentifier

set @maxStringLength = 8000
set @maxListIntElements = @maxStringLength / 12 -- sql int max int is 10 digits (2,147,483,647) plus a couple more to account for commas between list elements


insert into #affectedLists (list_, modfactor)
select list_, (count(*) / @maxListIntElements) + 1
from #affectedMembers
group by list_



insert into @conditionsToReprocess (orgID, conditionlist , memberlist)
select multiMemberConditions.orgID, membercentral.dbo.sortedIntList(multiMemberConditions.conditionID) as conditionList, multiMemberConditions.memberlist
from (
	SELECT singleMemberConditions.orgID, singleMemberConditions.conditionID, membercentral.dbo.sortedIntList(singleMemberConditions.memberID) as memberlist
	from (
		SELECT vgc.orgID, vgc.conditionID, m.memberID, (row_number() over (partition by vgc.conditionID order by m.memberid) % affl.modFactor) as dataset
		FROM membercentral.dbo.ams_virtualGroupConditionValues as cv
		inner join membercentral.dbo.ams_virtualGroupConditionKeys as k
			on k.conditionKeyID = cv.conditionKeyID
			and k.conditionKey = 'listList'
		inner join membercentral.dbo.ams_virtualGroupConditions vgc
			on vgc.conditionID = cv.conditionID
		inner join #affectedMembers affm
			on affm.list_ = cv.conditionValue
		inner join #affectedLists affl
			on affl.list_ = affm.list_
		inner join membercentral.dbo.ams_members m
			on m.orgID = vgc.orgID
			and m.membernumber = affm.externalMemberID
			and m.status in ('a','i')	
			and m.memberID = m.activeMemberID
	) as singleMemberConditions
	group by singleMemberConditions.orgID, singleMemberConditions.conditionID, singleMemberConditions.dataset
) as multiMemberConditions
group by multiMemberConditions.orgID, multiMemberConditions.memberlist

select @thisAutoID = min(autoID) from @conditionsToReprocess
while @thisAutoID is not null
begin
	select 
		@thisOrgID = orgID, 
		@thisConditionList = conditionList, 
		@thisMemberList = memberList,
		@thisItemGroupUID = null
	from @conditionsToReprocess
	where autoID = @thisAutoID

	
	EXEC platformQueue.dbo.queue_processMemberGroups_insert @thisOrgID, @thisMemberList, @thisConditionList, 2, @thisItemGroupUID OUTPUT



	select @thisAutoID = min(autoID) from @conditionsToReprocess where autoID > @thisAutoID
end




IF OBJECT_ID('tempdb..#IntegratedMembers') IS NOT NULL
	DROP TABLE #IntegratedMembers

IF OBJECT_ID('tempdb..#IntegratedMembers_updated') IS NOT NULL
	DROP TABLE #IntegratedMembers_updated

IF OBJECT_ID('tempdb..#IntegratedMembers_removed') IS NOT NULL
	DROP TABLE #IntegratedMembers_removed

IF OBJECT_ID('tempdb..#orgcodes') IS NOT NULL
	DROP TABLE #orgcodes

IF OBJECT_ID('tempdb..#affectedMembers') IS NOT NULL
	DROP TABLE #affectedMembers

IF OBJECT_ID('tempdb..#affectedLists') IS NOT NULL
	DROP TABLE #affectedLists
GO