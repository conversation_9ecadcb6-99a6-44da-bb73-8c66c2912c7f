-- NY-24 Create general_SWODEnrollmentReportWeekly

CREATE PROC [dbo].[general_SWODEnrollmentReportWeekly]
@participantID int,
@startdate datetime,
@enddate datetime,
@filename varchar(400)

AS

SET NOCOUNT ON

-- set startdate to 00:00:00 of startdate, 00:00:00 of enddate
SELECT @startdate = DATEADD(dd, DATEDIFF(dd,0,@startdate), 0)
SELECT @enddate = DATEADD(dd, DATEDIFF(dd,0,dateadd(dd,1,@enddate)), 0)

-- drop if exists
IF OBJECT_ID('tempdb..##tmpPreSWODSales') IS NOT NULL 
	DROP TABLE ##tmpPreSWODSales
IF OBJECT_ID('tempdb..##tmpSWODSales') IS NOT NULL 
	DROP TABLE ##tmpSWODSales

SELECT p.orgcode, 
	d.LastName,
	d.<PERSON>ame,
	d.<PERSON>,
	t.description,
	t.datePurchased,
	isnull(t.amountBilled,0) as [amountBilled],
	d.depomemberdataid,
	'' as MemberN<PERSON>ber,
	'' as Company,
	'' as Address1,
	'' as Address2,
	'' as Address3,
	'' as City,
	'' as State,
	'' as Zip,
	'' as Telephone
INTO ##tmpPreSWODSales
FROM dbo.tblEnrollments AS e 
INNER JOIN dbo.tblEnrollmentsSWOD AS eswod ON e.enrollmentID = eswod.enrollmentID 
INNER JOIN dbo.tblSeminars as s on s.seminarID = e.seminarID
INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
LEFT OUTER JOIN trialsmith.dbo.depotransactions t ON t.depomemberdataID = d.depomemberdataID
	AND t.accountcode BETWEEN '7000' AND '7005'
	AND t.purchasedItemTableName = 'SeminarWebOnDemand'
	AND t.purchasedItemID = e.seminarID
WHERE p.participantID = @participantID
	AND e.isActive = 1
	AND e.dateEnrolled BETWEEN @startdate AND @enddate
ORDER BY e.dateEnrolled

alter table ##tmpPreSWODSales alter column MemberNumber varchar(50)
alter table ##tmpPreSWODSales alter column Company varchar(200)
alter table ##tmpPreSWODSales alter column Address1 varchar(100)
alter table ##tmpPreSWODSales alter column Address2 varchar(100)
alter table ##tmpPreSWODSales alter column Address3 varchar(100)
alter table ##tmpPreSWODSales alter column City varchar(35)
alter table ##tmpPreSWODSales alter column State varchar(4)
alter table ##tmpPreSWODSales alter column Zip varchar(25)
alter table ##tmpPreSWODSales alter column Telephone varchar(40)

update sales set
	MemberNumber = m2.MemberNumber,
	Company = m2.Company,
	Address1 = ma.Address1,
	Address2 = ma.Address2,
	Address3 = ma.Address3,
	City = ma.City,
	State = st.Code,
	Zip = ma.postalCode,
	Telephone = mp.phone,
	Email = me.email
from ##tmpPreSWODSales sales
INNER JOIN membercentral.membercentral.dbo.ams_networkprofiles np on np.depomemberDataID = sales.depomemberDataID
INNER JOIN membercentral.membercentral.dbo.ams_membernetworkprofiles mnp on mnp.profileid = np.profileid and mnp.status in ('I', 'A')
INNER JOIN membercentral.membercentral.dbo.ams_members as m ON m.memberID = mnp.memberid
INNER JOIN membercentral.membercentral.dbo.ams_members as m2 ON m2.memberID = m.activeMemberID
INNER JOIN membercentral.membercentral.dbo.ams_memberAddressTypes at on at.orgID = m.orgID and at.addressTypeOrder=1
INNER JOIN membercentral.membercentral.dbo.ams_memberAddresses ma on ma.addressTypeID = at.addressTypeID and ma.memberID = m2.memberID
INNER JOIN membercentral.membercentral.dbo.ams_states st on st.stateID = ma.stateID 
INNER JOIN membercentral.membercentral.dbo.ams_memberPhoneTypes mpt on mpt.orgID = m.orgID and mpt.phoneTypeOrder=1
INNER JOIN membercentral.membercentral.dbo.ams_memberPhones mp on mp.addressID = ma.addressID and mp.phoneTypeID = mpt.phoneTypeID
INNER JOIN membercentral.membercentral.dbo.ams_memberEmailTypes met on met.orgID = m.orgID and met.emailTypeOrder=1
left outer JOIN membercentral.membercentral.dbo.ams_memberEmails me on me.memberID = m.activeMemberID and me.emailTypeID = met.emailTypeID


SELECT TOP 100 PERCENT orgcode, 
	dbo.fn_csvSafeString(LastName) as [Last Name],
	dbo.fn_csvSafeString(FirstName) as [First Name],
	dbo.fn_csvSafeString(Email) as [Email],
	dbo.fn_csvSafeString(description) as [Seminar Description],
	convert(varchar(10),datePurchased,101) as 'Date Purchased',
	dbo.fn_csvSafeString(isnull(amountBilled,0)) as [Amount Billed],
	dbo.fn_csvSafeString(MemberNumber) as [Member Number],
	dbo.fn_csvSafeString(Company) as [Firm Name],
	dbo.fn_csvSafeString(Address1) as [Address1],
	dbo.fn_csvSafeString(Address2) as [Address2],
	dbo.fn_csvSafeString(Address3) as [Address3],
	dbo.fn_csvSafeString(City) as [City],
	dbo.fn_csvSafeString(State) as [State],
	dbo.fn_csvSafeString(Zip) as [Zip],
	dbo.fn_csvSafeString(Telephone) as [Telephone]
INTO ##tmpSWODSales
FROM ##tmpPreSWODSales

-- export data
DECLARE @cmd varchar(6000)
declare @tmpBCP TABLE (theoutput varchar(max))
set @cmd = 'bcp ##tmpSWODSales out ' + @filename + ' -c -t, -T -S' + CAST(serverproperty('servername') as varchar(20))
insert into @tmpBCP (theoutput)
exec master..xp_cmdshell @cmd	

-- return count of records
SELECT count(*) AS returnCount
FROM ##tmpSWODSales

-- get fields returned
EXEC tempdb.dbo.SP_COLUMNS ##tmpSWODSales

-- drop temp table
IF OBJECT_ID('tempdb..##tmpSWODSales') IS NOT NULL 
	DROP TABLE ##tmpSWODSales

IF OBJECT_ID('tempdb..##tmpPreSWODSales') IS NOT NULL 
	DROP TABLE ##tmpPreSWODSales
GO