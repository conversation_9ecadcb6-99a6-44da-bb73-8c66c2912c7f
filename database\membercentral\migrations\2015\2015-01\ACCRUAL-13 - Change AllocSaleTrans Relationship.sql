use membercentral
GO

ALTER FUNCTION [dbo].[fn_ev_registrantTransactions] (
	@registrantID int
)
RETURNS TABLE
AS
RETURN (

	-- this should include all transactions (ignoring status)

	WITH regTrans as (
		select transactionID, typeID 
		from (
			-- rate sales
			select t.transactionID, t.typeID
			from dbo.ev_registrants as r
			inner join dbo.tr_applications as a on a.itemType = 'Rate' and a.ItemID = r.registrantID
			inner join dbo.tr_transactions as t on t.transactionID = a.transactionID 
			inner join dbo.tr_types as tt on tt.typeID = t.typeID and tt.type = 'Sale'
			where r.registrantID = @registrantID
				union
			-- custom q sales
			select t.transactionID, t.typeID
			from dbo.ev_registrants as r
			inner join dbo.ev_registrantDetails as rd on rd.registrantID = r.registrantID
			inner join dbo.tr_applications as a on a.itemType = 'Custom' and a.ItemID = rd.detailID
			inner join dbo.tr_transactions as t on t.transactionID = a.transactionID 
			inner join dbo.tr_types as tt on tt.typeID = t.typeID and tt.type = 'Sale'
			where r.registrantID = @registrantID
		) as salesTrans
			union all
		-- sales tax
		select t.transactionID, t.typeid
		from dbo.tr_transactions as t
		inner join dbo.tr_types as tt on tt.typeID = t.typeID and tt.type = 'Sales Tax'
		inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'SalesTaxTrans'
		inner join regTrans as rt on rt.transactionID = tr.appliedToTransactionID 
		inner join dbo.tr_types as rtt on rtt.typeID = rt.typeID and rtt.type = 'Sale'
			union all
		-- adjustments
		select t.transactionID, t.typeid
		from dbo.tr_transactions as t
		inner join dbo.tr_types as tt on tt.typeID = t.typeID and tt.type = 'Adjustment'
		inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'AdjustTrans'
		inner join regTrans as rt on rt.transactionID = tr.appliedToTransactionID 
		inner join dbo.tr_types as rtt on rtt.typeID = rt.typeID and rtt.type in ('Sale','Sales Tax')
			union all
		-- allocations (allocSaleTrans)
		select t.transactionID, t.typeID
		from dbo.tr_transactions as t
		inner join dbo.tr_types as tt on tt.typeID = t.typeID and tt.type = 'Allocation'
		inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'AllocSaleTrans'
		inner join regTrans as rt on rt.transactionID = tr.appliedToTransactionID 
			union all
		-- writeoffs (writeOffSaleTrans)
		select t.transactionID, t.typeID
		from dbo.tr_transactions as t
		inner join dbo.tr_types as tt on tt.typeID = t.typeID and tt.type = 'Write Off'
		inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'WriteOffSaleTrans'
		inner join regTrans as rt on rt.transactionID = tr.appliedToTransactionID 
		inner join dbo.tr_types as rtt on rtt.typeID = rt.typeID and rtt.type in ('Sale','Sales Tax','Adjustment')
			union all
		-- payments (tied to allocations - AllocPayTrans)
		select t.transactionID, t.typeID
		from dbo.tr_transactions as t
		inner join dbo.tr_types as tt on tt.typeID = t.typeID and tt.type = 'Payment'
		inner join dbo.tr_relationships as tr on tr.appliedToTransactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'AllocPayTrans'
		inner join regTrans as rt on rt.transactionID = tr.transactionID 
		inner join dbo.tr_types as rtt on rtt.typeID = rt.typeID and rtt.type = 'Allocation'
			union all
		-- refunds tied to included payments
		select t.transactionID, t.typeID
		from dbo.tr_transactions as t
		inner join dbo.tr_types as tt on tt.typeID = t.typeID and tt.type = 'Refund'
		inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'RefundTrans'
		inner join regTrans as rt on rt.transactionID = tr.appliedToTransactionID 
		inner join dbo.tr_types as rtt on rtt.typeID = rt.typeID and rtt.type = 'Payment'
	), regTransDist AS (
		select distinct transactionID
		from regTrans
	)
	select t.transactionID, t.ownedByOrgID, t.recordedOnSiteID, t.statusID, t.detail, t.parentTransactionID,
		t.amount, t.dateRecorded, t.transactionDate, t.assignedToMemberID, t.recordedByMemberID,
		t.statsSessionID, t.typeID, t.accrualDate, t.debitGLAccountID, t.creditGLAccountID
	from regTransDist as rt
	inner join dbo.tr_transactions as t on t.transactionid = rt.transactionid
)
GO

ALTER FUNCTION [dbo].[fn_store_orderTransactions] (
	@orderID int
)
RETURNS TABLE
AS
RETURN (

	-- this should include all transactions (ignoring status)

	WITH ordTrans as (
		-- new style
		select t.transactionID, t.typeID
		from dbo.store_orderDetails as sod
		inner join dbo.tr_applications as a on a.applicationTypeID = 14 and a.itemType IN ('ProductPerShipment','ProductPerItem','Product') and a.ItemID = sod.orderDetailID
		inner join dbo.tr_transactions as t on t.transactionID = a.transactionID 
		inner join dbo.tr_types as tt on tt.typeID = t.typeID and tt.type = 'Sale'
		where sod.orderID = @orderID
			union all
		-- old style
		select t.transactionID, t.typeID
		from dbo.store_orders as so
		inner join dbo.tr_applications as a on a.applicationTypeID = 14 and a.itemType = 'Order' and a.ItemID = so.orderID
		inner join dbo.tr_transactions as t on t.transactionID = a.transactionID 
		inner join dbo.tr_types as tt on tt.typeID = t.typeID and tt.type = 'Sale'
		where so.orderID = @orderID
			union all
		-- sales tax
		select t.transactionID, t.typeid
		from dbo.tr_transactions as t
		inner join dbo.tr_types as tt on tt.typeID = t.typeID and tt.type = 'Sales Tax'
		inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'SalesTaxTrans'
		inner join ordTrans as rt on rt.transactionID = tr.appliedToTransactionID 
		inner join dbo.tr_types as rtt on rtt.typeID = rt.typeID and rtt.type = 'Sale'
			union all
		-- adjustments
		select t.transactionID, t.typeid
		from dbo.tr_transactions as t
		inner join dbo.tr_types as tt on tt.typeID = t.typeID and tt.type = 'Adjustment'
		inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'AdjustTrans'
		inner join ordTrans as rt on rt.transactionID = tr.appliedToTransactionID 
		inner join dbo.tr_types as rtt on rtt.typeID = rt.typeID and rtt.type in ('Sale','Sales Tax')
			union all
		-- allocations (allocSaleTrans)
		select t.transactionID, t.typeID
		from dbo.tr_transactions as t
		inner join dbo.tr_types as tt on tt.typeID = t.typeID and tt.type = 'Allocation'
		inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'AllocSaleTrans'
		inner join ordTrans as rt on rt.transactionID = tr.appliedToTransactionID 
			union all
		-- writeoffs (writeOffSaleTrans)
		select t.transactionID, t.typeID
		from dbo.tr_transactions as t
		inner join dbo.tr_types as tt on tt.typeID = t.typeID and tt.type = 'Write Off'
		inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'WriteOffSaleTrans'
		inner join ordTrans as rt on rt.transactionID = tr.appliedToTransactionID 
		inner join dbo.tr_types as rtt on rtt.typeID = rt.typeID and rtt.type in ('Sale','Sales Tax','Adjustment')
			union all
		-- payments (tied to allocations - AllocPayTrans)
		select t.transactionID, t.typeID
		from dbo.tr_transactions as t
		inner join dbo.tr_types as tt on tt.typeID = t.typeID and tt.type = 'Payment'
		inner join dbo.tr_relationships as tr on tr.appliedToTransactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'AllocPayTrans'
		inner join ordTrans as rt on rt.transactionID = tr.transactionID 
		inner join dbo.tr_types as rtt on rtt.typeID = rt.typeID and rtt.type = 'Allocation'
			union all
		-- refunds tied to included payments
		select t.transactionID, t.typeID
		from dbo.tr_transactions as t
		inner join dbo.tr_types as tt on tt.typeID = t.typeID and tt.type = 'Refund'
		inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'RefundTrans'
		inner join ordTrans as rt on rt.transactionID = tr.appliedToTransactionID 
		inner join dbo.tr_types as rtt on rtt.typeID = rt.typeID and rtt.type = 'Payment'
	), ordTransDist AS (
		select distinct transactionID
		from ordTrans
	)
	select t.transactionID, t.ownedByOrgID, t.recordedOnSiteID, t.statusID, t.detail, t.parentTransactionID,
		t.amount, t.dateRecorded, t.transactionDate, t.assignedToMemberID, t.recordedByMemberID,
		t.statsSessionID, t.typeID, t.accrualDate, t.debitGLAccountID, t.creditGLAccountID
	from ordTransDist as ot
	inner join dbo.tr_transactions as t on t.transactionid = ot.transactionid
)
GO

ALTER FUNCTION [dbo].[fn_tr_getRelatedTransactionsToVoid_adjustment] (@transactionID int)
RETURNS TABLE 
AS
RETURN 
(
	WITH adjAndTax AS (
		-- orig adj (only used in queries below. not included in result)
		select transactionID, dateRecorded
		from dbo.tr_transactions 
		where transactionID = @transactionID
		and statusID <> 2
			union all
		-- pit tax
		SELECT t.transactionID, t.dateRecorded
		FROM dbo.tr_transactions as t
		INNER JOIN dbo.tr_relationships AS r ON r.transactionID = t.transactionID and t.statusID <> 2
		INNER JOIN dbo.tr_relationshipTypes AS rt ON rt.typeID = r.typeID AND rt.type = 'PITTaxTrans'
		WHERE r.appliedToTransactionID = @transactionID
	)
	select transactionID, dateRecorded
	from adjAndTax
	where transactionID <> @transactionID
		union
	-- allocations to adjustments
	SELECT tAlloc.transactionID, tAlloc.dateRecorded
	FROM dbo.tr_transactions as tAlloc
	INNER JOIN dbo.tr_relationships AS r ON r.transactionID = tAlloc.transactionID
	INNER JOIN dbo.tr_relationshipTypes AS rt ON rt.typeID = r.typeID AND rt.type = 'AllocSaleTrans'
	INNER JOIN adjAndTax on adjAndTax.transactionID = r.appliedToTransactionID
	WHERE tAlloc.statusID in (1,3)
		union
	-- write offs of adjustments
	SELECT tWO.transactionID, tWO.dateRecorded
	FROM dbo.tr_transactions as tWO
	INNER JOIN dbo.tr_relationships AS r ON r.transactionID = tWO.transactionID
	INNER JOIN dbo.tr_relationshipTypes AS rt ON rt.typeID = r.typeID AND rt.type = 'WriteOffSaleTrans'
	INNER JOIN adjAndTax on adjAndTax.transactionID = r.appliedToTransactionID
	WHERE tWO.statusID in (1,3)
)
GO

ALTER FUNCTION [dbo].[fn_tr_getRelatedTransactionsToVoid_sale] (@transactionID int)
RETURNS TABLE 
AS
RETURN 
(
	WITH ST AS (
		-- orig sale (only used in queries below. not included in result)
		select transactionID, dateRecorded
		from dbo.tr_transactions 
		where transactionID = @transactionID
			union all
		-- children sales
		select transactionID, dateRecorded
		from dbo.tr_transactions 
		where parentTransactionID = @transactionID 
		and statusID IN (1,3)
			union all
		-- sales tax
		SELECT tSalesTax.transactionID, tSalesTax.daterecorded
		FROM dbo.tr_transactions as tSalesTax
		INNER JOIN dbo.tr_relationships AS r ON r.transactionID = tSalesTax.transactionID
		INNER JOIN dbo.tr_relationshipTypes AS rt ON rt.typeID = r.typeID AND rt.type = 'SalesTaxTrans'
		INNER JOIN ST on ST.transactioniD = r.appliedToTransactionID
		WHERE tSalesTax.statusID in (1,3)
	), STA AS (
		select transactionID, dateRecorded
		from ST
			union
		SELECT tAdj.transactionID, tAdj.dateRecorded
		FROM dbo.tr_transactions as tAdj
		INNER JOIN dbo.tr_relationships AS r ON r.transactionID = tAdj.transactionID
		INNER JOIN dbo.tr_relationshipTypes AS rt ON rt.typeID = r.typeID AND rt.type = 'AdjustTrans'
		INNER JOIN ST on ST.transactionID = r.appliedToTransactionID
		WHERE tAdj.statusID in (1,3)
	)
	select transactionID, dateRecorded
	from STA
	where transactionID <> @transactionID
		union
	-- allocations
	SELECT tAlloc.transactionID, tAlloc.dateRecorded
	FROM dbo.tr_transactions as tAlloc
	INNER JOIN dbo.tr_relationships AS r ON r.transactionID = tAlloc.transactionID
	INNER JOIN dbo.tr_relationshipTypes AS rt ON rt.typeID = r.typeID AND rt.type = 'AllocSaleTrans'
	INNER JOIN STA on STA.transactionID = r.appliedToTransactionID
	WHERE tAlloc.statusID IN (1,3)
		union
	-- write offs of sales
	SELECT tWO.transactionID, tWO.dateRecorded
	FROM dbo.tr_transactions as tWO
	INNER JOIN dbo.tr_relationships AS r ON r.transactionID = tWO.transactionID
	INNER JOIN dbo.tr_relationshipTypes AS rt ON rt.typeID = r.typeID AND rt.type = 'WriteOffSaleTrans'
	INNER JOIN STA on STA.transactionID = r.appliedToTransactionID
	WHERE tWO.statusID IN (1,3)
)
GO

ALTER FUNCTION [dbo].[fn_tr_getRelatedTransactionsToVoid_salesTax] (@transactionID int)
RETURNS TABLE 
AS
RETURN 
(
	SELECT tAdj.transactionID, tAdj.dateRecorded
	FROM dbo.tr_transactions as tAdj
	INNER JOIN dbo.tr_relationships AS r ON r.transactionID = tAdj.transactionID
	INNER JOIN dbo.tr_relationshipTypes AS rt ON rt.typeID = r.typeID AND rt.type = 'AdjustTrans'
	INNER JOIN dbo.tr_transactions AS tSale ON r.appliedToTransactionID = tSale.transactionID 
	WHERE tAdj.statusID IN (1,3)
	AND tSale.transactionID = @transactionID
		union all
	SELECT tAlloc.transactionID, tAlloc.dateRecorded
	FROM dbo.tr_transactions as tAlloc
	INNER JOIN dbo.tr_relationships AS r ON r.transactionID = tAlloc.transactionID
	INNER JOIN dbo.tr_relationshipTypes AS rt ON rt.typeID = r.typeID AND rt.type = 'AllocSaleTrans'
	INNER JOIN dbo.tr_transactions AS tSale ON r.appliedToTransactionID = tSale.transactionID 
	WHERE tAlloc.statusID IN (1,3)
	AND tSale.transactionID = @transactionID
		union all
	SELECT tWO.transactionID, tWO.dateRecorded
	FROM dbo.tr_transactions as tWO
	INNER JOIN dbo.tr_relationships AS r ON r.transactionID = tWO.transactionID
	INNER JOIN dbo.tr_relationshipTypes AS rt ON rt.typeID = r.typeID AND rt.type = 'WriteOffSaleTrans'
	INNER JOIN dbo.tr_transactions AS tSale ON r.appliedToTransactionID = tSale.transactionID 
	WHERE tWO.statusID in (1,3)
	AND tSale.transactionID = @transactionID
)
GO

ALTER FUNCTION [dbo].[fn_tr_invoicesAssociatedToMember] (@memberID int, @orgID int)
RETURNS @invoiceTable TABLE (
 invoiceID int PRIMARY KEY
)
AS
BEGIN
	declare @memberIDs TABLE (memberID int PRIMARY KEY)

	insert into @memberIDs
	select @memberID as memberID
		union
	select allchildMember.memberID
	from dbo.ams_members as m WITH(NOLOCK)
	inner join dbo.ams_recordRelationships as rr WITH(NOLOCK) on rr.masterMemberID = m.memberID
	inner join dbo.ams_members as childMember WITH(NOLOCK) on rr.childMemberID = childMember.memberID
	inner join dbo.ams_members as allchildMember WITH(NOLOCK) on allchildMember.activeMemberID = childMember.memberID
	where m.memberID = @memberID
	and childMember.status <> 'D'

	insert into @invoiceTable (invoiceID)
		-- inv assigned to member
		select i.invoiceID
		from dbo.tr_invoices as i WITH(NOLOCK)
		INNER JOIN dbo.ams_members as m WITH(NOLOCK) on m.memberid = i.assignedToMemberID 
		INNER JOIN @memberIDs as mct on mct.memberID = m.activeMemberID	
		WHERE m.orgID = @orgID
			union
		-- trans on inv assigned to member
		select it.invoiceID
		from dbo.tr_invoiceTransactions as it WITH(NOLOCK)
		inner join dbo.tr_transactions as t WITH(NOLOCK) on t.transactionID = it.transactionID 
			and t.statusID not in (2,4)
			and t.ownedByOrgID = @orgID
		INNER JOIN dbo.ams_members as m WITH(NOLOCK) on m.memberid = t.assignedToMemberID 
		INNER JOIN @memberIDs as mct on mct.memberID = m.activeMemberID	
		WHERE m.orgID = @orgID
			union
		-- payments assigned to member allocated to trans on inv
		select it.invoiceID
		from dbo.tr_transactions as tPay WITH(NOLOCK)
		inner join dbo.tr_relationships as rAlloc WITH(NOLOCK) on rAlloc.appliedToTransactionID = tPay.transactionID
		inner join dbo.tr_relationshipTypes as rt WITH(NOLOCK) on rt.typeID = rAlloc.typeID and rt.type = 'AllocPayTrans'
		inner join dbo.tr_transactions as tAlloc WITH(NOLOCK) on tAlloc.transactionID = rAlloc.transactionID and tAlloc.statusID in (1,3)
		inner join dbo.tr_relationships as rAlloc2 WITH(NOLOCK) on rAlloc2.transactionID = tAlloc.transactionID
		inner join dbo.tr_relationshipTypes as rt2 WITH(NOLOCK) on rt2.typeID = rAlloc2.typeID and rt2.type = 'AllocSaleTrans'
		inner join dbo.tr_transactions as tSale WITH(NOLOCK) on tSale.transactionID = rAlloc2.appliedToTransactionID and tSale.statusID in (1,3)
		inner join dbo.tr_invoiceTransactions as it WITH(NOLOCK) on it.transactionID = tSale.transactionID
		INNER JOIN dbo.ams_members as m WITH(NOLOCK) on m.memberid = tPay.assignedToMemberID
		INNER JOIN @memberIDs as mct on mct.memberID = m.activeMemberID	
		where tPay.ownedByOrgID = @orgID
		and tPay.typeID = 2

	RETURN
END
GO

ALTER PROC [dbo].[store_getOrderDetails]
@storeID int, 
@orderid int

AS

declare @detailTbl TABLE (autoid int IDENTITY(1,1), formatID int, itemID int,  rateID int, quantity int, 
	formatName varchar(250), contentTitle varchar(max), itemTotal money, 
	shipTotal money, numAffirmationsIncluded int)

insert into @detailTbl (formatID, itemID, rateID, quantity, formatName,  contentTitle, itemTotal, shipTotal, numAffirmationsIncluded)
select spf.formatID, spf.itemID, sod.rateID, sod.quantity, spf.name, prodcontent.contentTitle,
	coalesce(sum(case when tra.itemType = 'Product' then ts.cache_amountAfterAdjustment else null end),sod.ratePaid*sod.quantity,sod.pricePaid*sod.quantity) as itemTotal,
	sum(case when tra.itemType <> 'Product' then ts.cache_amountAfterAdjustment else null end) as shipTotal,
	case
		when (select count(*) from dbo.crd_affirmations where orderID = @orderid and productFormatID = spf.FormatID and status = 'A') = 0 then 0
		else spf.quantity * sod.quantity
	end as numAffirmationsIncluded
FROM dbo.store_orders as so
INNER JOIN dbo.store on store.storeID = so.storeID
INNER JOIN dbo.store_orderDetails AS sod on sod.orderID = so.orderID
INNER JOIN dbo.store_ProductFormats AS spf ON spf.formatID = sod.formatID
INNER JOIN dbo.store_Products AS p ON sod.productItemID = p.ItemID
CROSS APPLY dbo.fn_getContent(p.productContentID, 1) as prodcontent
LEFT OUTER JOIN dbo.tr_applications as tra
	inner join dbo.tr_transactionSales as ts on ts.transactionID = tra.transactionID
	on tra.applicationTypeID = 14
	and tra.itemType in ('Product','ProductPerItem','ProductPerShipment')
	and tra.itemID = sod.orderDetailID
	and tra.status = 'A'
WHERE so.orderID = @orderid
and store.storeID = @storeID
group by spf.formatID, spf.itemID, sod.rateID, sod.quantity, spf.name,  prodcontent.contentTitle, sod.ratePaid, sod.pricePaid,
	store.offerAffirmations, spf.offerAffirmations, spf.quantity, sod.orderDetailID, so.totalShipping
order by sod.orderDetailID

-- return items in order
select formatID, itemID, rateID, quantity, formatName, contentTitle, itemTotal, numAffirmationsIncluded
from @detailTbl
order by autoid

-- return payments applied to order
select tPay.transactionID, tPay.statusID, tPay.detail, tPay.transactionDate, 
	isnull(m2.firstname + ' ','') + case when o.hasMiddleName = 1 then isnull(m2.middlename + ' ','') else '' end + isnull(m2.lastname,'') + ' (' + m2.membernumber + ')' as AssignedTo,
	sum(case when glAllocDeb.GLCode = 'ACCOUNTSRECEIVABLE' and glAllocDeb.AccountTypeID = 2 then tAlloc.amount*-1 else tAlloc.amount end) as AllocSum
from dbo.tr_transactions as tSaleAdj
inner join dbo.fn_store_orderTransactions(@orderID) as rt on rt.transactionID = tSaleAdj.transactionID
inner join dbo.tr_relationships as rS on rS.appliedToTransactionID = tSaleAdj.transactionID and tSaleAdj.statusID = 1
inner join dbo.tr_relationshipTypes as rtS on rtS.typeID = rS.typeID and rtS.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as tAlloc on tAlloc.transactionID = rS.transactionID and tAlloc.statusID in (1,3)
inner join dbo.tr_GLAccounts as glAllocDeb on glAllocDeb.GLAccountID = tAlloc.debitGLAccountID
inner join dbo.tr_relationships as rP on rP.transactionID = tAlloc.transactionID
inner join dbo.tr_relationshipTypes as rtP on rtP.typeID = rP.typeID and rtP.type = 'AllocPayTrans'
inner join dbo.tr_transactions as tPay on tPay.transactionID = rP.appliedToTransactionID and tPay.statusID in (1,3)
inner join dbo.ams_members as m on m.memberid = tPay.assignedToMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
inner join dbo.organizations as o on o.orgID = m2.orgID
inner join dbo.tr_transactionPayments as tp on tp.transactionID = tPay.transactionID
group by tPay.transactionID, tPay.statusID, tPay.detail, tPay.transactionDate, isnull(m2.firstname + ' ','') + case when o.hasMiddleName = 1 then isnull(m2.middlename + ' ','') else '' end + isnull(m2.lastname,'') + ' (' + m2.membernumber + ')'
having sum(case when glAllocDeb.GLCode = 'ACCOUNTSRECEIVABLE' and glAllocDeb.AccountTypeID = 2 then tAlloc.amount*-1 else tAlloc.amount end) > 0
order by tPay.transactionDate

-- return summary totals
; with allOT as (
	select ot.typeID, ts.cache_amountAfterAdjustment, ts.cache_activePaymentAllocatedAmount
	from dbo.fn_store_orderTransactions(@orderid) as ot
	inner join dbo.tr_transactionSales as ts on ts.transactionID = ot.transactionID
)
select sum(itemTotal) as totalProducts, sum(shipTotal) as totalShipping, 
	(select sum(cache_amountAfterAdjustment) from allOT where typeID = 7) as totalTax,
	(select sum(cache_activePaymentAllocatedAmount) from allOT) as totalPaid,
	sum(numAffirmationsIncluded)as totalAffirmationsIncluded
from @detailTbl

-- return order details with sale and transaction info
select 
	sod.orderDetailID,	
	so.orderNumber,
	spf.itemID,
	spf.formatID, 
	spf.name,  
	sod.rateID, 
	sod.quantity, 	
	prodcontent.contentTitle,
	sod.ratePaid,
	case when store.offerAffirmations = 1 and spf.offerAffirmations = 1 then spf.quantity else 0 end as numAffirmationsIncluded,
	ts.saleID,
	ts.transactionID,
	ts.cache_amountAfterAdjustment,
	ts.cache_activePaymentAllocatedAmount,
	ts.cache_pendingPaymentAllocatedAmount,
	ts.stateIDForTax,
	tra.taID,
	tra.status as trAppStatus,
	case when ts.cache_amountAfterAdjustment <> sod.ratePaid then 1 else 0 end as saleAmountChanged
FROM 
	dbo.store_orders as so
	INNER JOIN dbo.store on store.storeID = so.storeID
	INNER JOIN dbo.store_orderDetails AS sod on sod.orderID = so.orderID
	INNER JOIN dbo.store_ProductFormats AS spf ON spf.formatID = sod.formatID
	INNER JOIN dbo.store_Products AS p ON sod.productItemID = p.ItemID
	CROSS APPLY dbo.fn_getContent(p.productContentID, 1) as prodcontent
	INNER JOIN dbo.tr_applications as tra
		inner join dbo.tr_transactionSales as ts on ts.transactionID = tra.transactionID on 
			tra.applicationTypeID = 14
			and tra.itemType in ('Product')
			and tra.itemID = sod.orderDetailID
			and tra.status = 'A'
WHERE 
	so.orderID = @orderid
	and store.storeID = @storeID

-- return shipping details with sale and transaction info

select 
	tra.itemType,
	sod.orderDetailID,	
	so.orderNumber,
	spf.itemID,
	spf.formatID, 
	spf.name,  
	sod.rateID, 
	sod.quantity, 	
	prodcontent.contentTitle,
	sod.ratePaid,
	case when store.offerAffirmations = 1 and spf.offerAffirmations = 1 then spf.quantity else 0 end as numAffirmationsIncluded,
	ts.saleID,
	ts.transactionID,
	ts.cache_amountAfterAdjustment as amountAfterAdjustment,
	ts.cache_activePaymentAllocatedAmount as activePaymentAllocatedAmount,
	ts.cache_pendingPaymentAllocatedAmount as pendingPaymentAllocatedAmount,
	ts.stateIDForTax,
	tra.taID,
	tra.status as trAppStatus,
	case when ts.cache_amountAfterAdjustment <> sod.ratePaid then 1 else 0 end as saleAmountChanged
FROM 
	dbo.store_orders as so
	INNER JOIN dbo.store on store.storeID = so.storeID
	INNER JOIN dbo.store_orderDetails AS sod on sod.orderID = so.orderID
	INNER JOIN dbo.store_ProductFormats AS spf ON spf.formatID = sod.formatID
	INNER JOIN dbo.store_Products AS p ON sod.productItemID = p.ItemID
	CROSS APPLY dbo.fn_getContent(p.productContentID, 1) as prodcontent
	inner join dbo.tr_applications as tra
		inner join dbo.tr_transactionSales as ts on ts.transactionID = tra.transactionID  on 
			tra.applicationTypeID = 14
			and tra.itemType in ('ProductPerItem', 'ProductPerShipment')
			and tra.itemID = sod.orderDetailID
			and tra.status = 'A'
WHERE 
	so.orderID = @orderid
	and store.storeID = @storeID

RETURN 0
GO

ALTER PROC [dbo].[sub_report_SubscriptionBilling]
@tblName varchar(100),
@asOfDate datetime, 
@invoiceDueLower datetime

AS

declare @dynSQL varchar(4000)

-- "POOL OF TRANSACTIONS TO CONSIDER"
-- get all subscriber related transactions. this should include all transactions (ignoring status)
IF OBJECT_ID('tempdb..#tmpSubTrans') IS NOT NULL 
	DROP TABLE #tmpSubTrans
CREATE TABLE #tmpSubTrans (subscriberID int, transactionID int, typeID int)
	-- sales
	SET @dynSQL = '
		INSERT INTO #tmpSubTrans
		select s.subscriberID, t.transactionID, t.typeID
		from ' + @tblName + ' as s
		inner join dbo.tr_applications as a on a.applicationTypeID = 17 and a.itemType = ''Dues'' and a.ItemID = s.subscriberID
		inner join dbo.tr_transactions as t on t.transactionID = a.transactionID and t.typeID = 1'
	EXEC(@dynSQL)

	-- sales tax
	INSERT INTO #tmpSubTrans
	select rt.subscriberID, t.transactionID, t.typeid
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'SalesTaxTrans'
	inner join #tmpSubTrans as rt on rt.transactionID = tr.appliedToTransactionID

	-- adjustments
	INSERT INTO #tmpSubTrans
	select rt.subscriberID, t.transactionID, t.typeid
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'AdjustTrans'
	inner join #tmpSubTrans as rt on rt.transactionID = tr.appliedToTransactionID

	-- allocations (allocSaleTrans)
	INSERT INTO #tmpSubTrans
	select rt.subscriberID, t.transactionID, t.typeID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'AllocSaleTrans'
	inner join #tmpSubTrans as rt on rt.transactionID = tr.appliedToTransactionID

	-- writeoffs (writeOffSaleTrans)
	INSERT INTO #tmpSubTrans
	select rt.subscriberID, t.transactionID, t.typeID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'WriteOffSaleTrans'
	inner join #tmpSubTrans as rt on rt.transactionID = tr.appliedToTransactionID

	-- payments (tied to allocations - AllocPayTrans)
	INSERT INTO #tmpSubTrans
	select rt.subscriberID, t.transactionID, t.typeID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.appliedToTransactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'AllocPayTrans'
	inner join #tmpSubTrans as rt on rt.transactionID = tr.transactionID

	-- writeoffs (writeOffPayTrans)
	INSERT INTO #tmpSubTrans
	select rt.subscriberID, t.transactionID, t.typeID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'WriteOffPayTrans'
	inner join #tmpSubTrans as rt on rt.transactionID = tr.appliedToTransactionID

	-- refunds tied to included payments
	INSERT INTO #tmpSubTrans
	select rt.subscriberID, t.transactionID, t.typeID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'RefundTrans'
	inner join #tmpSubTrans as rt on rt.transactionID = tr.appliedToTransactionID

	-- nsf tied to included payments
	INSERT INTO #tmpSubTrans
	select rt.subscriberID, t.transactionID, t.typeID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'NSFTrans'
	inner join #tmpSubTrans as rt on rt.transactionID = tr.appliedToTransactionID
	
	-- void offsets
	INSERT INTO #tmpSubTrans
	select rt.subscriberID, t.transactionID, t.typeID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'OffsetTrans'
	inner join #tmpSubTrans as rt on rt.transactionID = tr.appliedToTransactionID
	
-- consider amounts
IF OBJECT_ID('tempdb..#allRevTrans') IS NOT NULL 
	DROP TABLE #allRevTrans
CREATE TABLE #allRevTrans (subscriberID int, invoiceID int, subInvAmount money)
	-- all sales/tax 
	insert into #allRevTrans
	select tmp.subscriberID, i.invoiceID, t.amount
	from #tmpSubTrans as tmp
	inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	where t.typeID in (1,7)
	and i.statusID <> 1

	-- all postive adj 
	insert into #allRevTrans
	select tmp.subscriberID, i.invoiceID, t.amount
	from #tmpSubTrans as tmp
	inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
	inner join dbo.tr_glaccounts as gl on gl.glaccountID = t.debitGLAccountID
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	where t.typeID = 3
	and gl.GLCode = 'ACCOUNTSRECEIVABLE'
	and i.statusID <> 1

	-- all negative adj offsets revenue
	insert into #allRevTrans
	select tmp.subscriberID, itt.invoiceID, r.amount*-1
	from #tmpSubTrans as tmp
	inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
	inner join dbo.tr_glaccounts as gl on gl.glaccountID = t.creditGLAccountID
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustInvTrans'
	inner join dbo.tr_transactions as tSalesTax on tSalesTax.transactionID = r.appliedToTransactionID and tSalesTax.typeID in (1,3,7)
	inner join dbo.tr_invoiceTransactions as itt on itt.transactionID = tSalesTax.transactionID
	where t.typeID = 3
	and gl.GLCode = 'ACCOUNTSRECEIVABLE'
	and i.statusID <> 1

	-- voids of sales/tax offsets revenue
	insert into #allRevTrans
	select tmp.subscriberID, itt.invoiceID, t.amount*-1
	from #tmpSubTrans as tmp
	inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'OffsetTrans'
	inner join dbo.tr_transactions as tVoidee on tVoidee.transactionID = r.appliedToTransactionID and t.typeID in (1,7)
	inner join dbo.tr_invoiceTransactions as itt on itt.transactionID = tVoidee.transactionID
	where t.typeID = 8
	and i.statusID <> 1

	-- voids of pos adj offsets revenue
	insert into #allRevTrans
	select tmp.subscriberID, itt.invoiceID, t.amount*-1
	from #tmpSubTrans as tmp
	inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'OffsetTrans'
	inner join dbo.tr_transactions as tVoidee on tVoidee.transactionID = r.appliedToTransactionID and t.typeID = 3
	inner join dbo.tr_glaccounts as gl on gl.glaccountID = tVoidee.debitGLAccountID
	inner join dbo.tr_invoiceTransactions as itt on itt.transactionID = tVoidee.transactionID
	where t.typeID = 8
	and gl.GLCode = 'ACCOUNTSRECEIVABLE'
	and i.statusID <> 1

	-- voids of neg adj 
	insert into #allRevTrans
	select tmp.subscriberID, itt.invoiceID, t.amount
	from #tmpSubTrans as tmp
	inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'OffsetTrans'
	inner join dbo.tr_transactions as tVoidee on tVoidee.transactionID = r.appliedToTransactionID and t.typeID = 3
	inner join dbo.tr_glaccounts as gl on gl.glaccountID = tVoidee.creditGLAccountID
	inner join dbo.tr_relationships as rAdj on rAdj.transactionID = tVoidee.transactionID
	inner join dbo.tr_relationshipTypes as rtAdj on rtAdj.typeID = rAdj.typeID and rtAdj.type = 'AdjustTrans'
	inner join dbo.tr_transactions as tSalesTax on tSalesTax.transactionID = rAdj.appliedToTransactionID and tSalesTax.typeID in (1,7)
	inner join dbo.tr_invoiceTransactions as itt on itt.transactionID = tSalesTax.transactionID
	where t.typeID = 8
	and gl.GLCode = 'ACCOUNTSRECEIVABLE'
	and i.statusID <> 1

	-- allocation
	insert into #allRevTrans
	select tmp.subscriberID, it.invoiceID, t.amount*-1
	from #tmpSubTrans as tmp
	inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = t.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID
	inner join dbo.tr_glaccounts as gl on gl.glaccountID = t.creditGLAccountID
	inner join dbo.tr_relationships as rAlloc on rAlloc.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rtAlloc on rtAlloc.typeID = rAlloc.typeID and rtAlloc.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as tSalesTaxAdj on tSalesTaxAdj.transactionID = rAlloc.appliedToTransactionID
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSalesTaxAdj.transactionID
	where t.typeID = 5
	and b.depositDate <= @asOfDate
	and gl.glCode = 'ACCOUNTSRECEIVABLE'

	-- deallocation offsets alloc
	insert into #allRevTrans
	select tmp.subscriberID, it.invoiceID, t.amount
	from #tmpSubTrans as tmp
	inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = t.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID
	inner join dbo.tr_glaccounts as gl on gl.glaccountID = t.debitGLAccountID
	inner join dbo.tr_relationships as rAlloc on rAlloc.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rtAlloc on rtAlloc.typeID = rAlloc.typeID and rtAlloc.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as tSalesTaxAdj on tSalesTaxAdj.transactionID = rAlloc.appliedToTransactionID
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSalesTaxAdj.transactionID
	where t.typeID = 5
	and b.depositDate <= @asOfDate
	and gl.glCode = 'ACCOUNTSRECEIVABLE'

	-- write off
	insert into #allRevTrans
	select tmp.subscriberID, it.invoiceID, t.amount*-1
	from #tmpSubTrans as tmp
	inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
	inner join dbo.tr_glaccounts as gl on gl.glaccountID = t.creditGLAccountID
	inner join dbo.tr_relationships as rWO on rWO.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rtWO on rtWO.typeID = rWO.typeID and rtWO.type = 'WriteOffSaleTrans'
	inner join dbo.tr_transactions as tSalesTaxAdj on tSalesTaxAdj.transactionID = rWO.appliedToTransactionID
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSalesTaxAdj.transactionID
	where t.typeID = 6
	and t.transactionDate <= @asOfDate
	and gl.glCode = 'ACCOUNTSRECEIVABLE'

	-- void of allocation offsets alloc
	insert into #allRevTrans
	select tmp.subscriberID, it.invoiceID, t.amount
	from #tmpSubTrans as tmp
	inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = t.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID
	inner join dbo.tr_relationships as rV on rV.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rtV on rtV.typeID = rV.typeID and rtV.type = 'OffsetTrans'
	inner join dbo.tr_transactions as tAlloc on tAlloc.transactionID = rV.appliedToTransactionID and tAlloc.typeID = 5
	inner join dbo.tr_glaccounts as gl on gl.glaccountID = tAlloc.creditGLAccountID
	inner join dbo.tr_relationships as rAlloc on rAlloc.transactionID = tAlloc.transactionID
	inner join dbo.tr_relationshipTypes as rtAlloc on rtAlloc.typeID = rAlloc.typeID and rtAlloc.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as tSalesTaxAdj on tSalesTaxAdj.transactionID = rAlloc.appliedToTransactionID
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSalesTaxAdj.transactionID
	where t.typeID = 8
	and b.depositDate <= @asOfDate
	and gl.GLCode = 'ACCOUNTSRECEIVABLE'	

	-- void of deallocation 
	insert into #allRevTrans
	select tmp.subscriberID, it.invoiceID, t.amount*-1
	from #tmpSubTrans as tmp
	inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = t.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID
	inner join dbo.tr_relationships as rV on rV.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rtV on rtV.typeID = rV.typeID and rtV.type = 'OffsetTrans'
	inner join dbo.tr_transactions as tAlloc on tAlloc.transactionID = rV.appliedToTransactionID and tAlloc.typeID = 5
	inner join dbo.tr_glaccounts as gl on gl.glaccountID = tAlloc.debitGLAccountID
	inner join dbo.tr_relationships as rAlloc on rAlloc.transactionID = tAlloc.transactionID
	inner join dbo.tr_relationshipTypes as rtAlloc on rtAlloc.typeID = rAlloc.typeID and rtAlloc.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as tSalesTaxAdj on tSalesTaxAdj.transactionID = rAlloc.appliedToTransactionID
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSalesTaxAdj.transactionID
	where t.typeID = 8
	and b.depositDate <= @asOfDate
	and gl.GLCode = 'ACCOUNTSRECEIVABLE'	

	-- void of write off offets alloc
	insert into #allRevTrans
	select tmp.subscriberID, it.invoiceID, t.amount
	from #tmpSubTrans as tmp
	inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
	inner join dbo.tr_relationships as rV on rV.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rtV on rtV.typeID = rV.typeID and rtV.type = 'OffsetTrans'
	inner join dbo.tr_transactions as tWO on tWO.transactionID = rV.appliedToTransactionID and tWO.typeID = 6
	inner join dbo.tr_glaccounts as gl on gl.glaccountID = tWO.creditGLAccountID
	inner join dbo.tr_relationships as rWO on rWO.transactionID = tWO.transactionID
	inner join dbo.tr_relationshipTypes as rtWO on rtWO.typeID = rWO.typeID and rtWO.type = 'WriteOffSaleTrans'
	inner join dbo.tr_transactions as tSalesTaxAdj on tSalesTaxAdj.transactionID = rWO.appliedToTransactionID
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSalesTaxAdj.transactionID
	where t.typeID = 8
	and t.transactionDate <= @asOfDate
	and gl.GLCode = 'ACCOUNTSRECEIVABLE'

select subscriberID, invoiceID, dateDue, subInvAmount, 
	case when dateDue < @invoiceDueLower then DATEDIFF(dd,dateDue,@invoiceDueLower) else 0 end as daysPastDue
from (
	select tmp.subscriberID, tmp.invoiceID, i.dateDue, sum(tmp.subInvAmount) as subInvAmount
	from #allRevTrans as tmp
	inner join dbo.tr_invoices as i on i.invoiceID = tmp.invoiceID
	group by tmp.subscriberID, tmp.invoiceID, i.dateDue
	having sum(tmp.subInvAmount) > 0
) as tmp2


IF OBJECT_ID('tempdb..#tmpSubTrans') IS NOT NULL 
	DROP TABLE #tmpSubTrans
IF OBJECT_ID('tempdb..#allRevTrans') IS NOT NULL 
	DROP TABLE #allRevTrans
GO

ALTER PROC [dbo].[tr_createTransaction_allocation]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@status varchar(20),
@amount money,
@transactionDate datetime,
@paymentTransactionID int,
@saleTransactionID int,	-- this can be a sale, tax, adjustment, or DIT
@transactionID int OUTPUT

AS

set nocount on

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- reset output param
	select @transactionID = 0

	-- no zero dollar allocations
	if @amount = 0
		RAISERROR('amount is 0', 16, 1);

	-- ensure amount is 2 decimals
	select @amount = cast(@amount as decimal(10,2))

	-- ensure @paymentTransactionID is a payment
	IF NOT EXISTS (select transactionID from dbo.tr_transactions where transactionID = @paymentTransactionID and typeID = 2)
		RAISERROR('paymentTransactionID is not a payment', 16, 1);

	-- ensure @saleTransactionID is either a sale, sales tax, positive adjustment, or pos DIT
	IF NOT EXISTS (
		select t.transactionID
		from dbo.tr_transactions as t
		inner join dbo.tr_glAccounts as glDeb on glDeb.GLAccountID = t.debitGLAccountID
		where t.transactionID = @saleTransactionID
		and t.typeID in (1,3,7)
		and glDeb.GLCode = 'ACCOUNTSRECEIVABLE' 
		and glDeb.isSystemAccount = 1
		) AND NOT EXISTS (
			select t.transactionID
			from dbo.tr_transactions as t
			inner join dbo.tr_glAccounts as glDeb on glDeb.GLAccountID = t.debitGLAccountID
			inner join dbo.tr_glAccountTypes as glt on glt.accountTypeID = glDeb.accountTypeID
			where t.transactionID = @saleTransactionID
			and t.typeID = 10
			and glt.accountType = 'Liability' 
			and glDeb.isSystemAccount = 1
		)
	RAISERROR('saleTransactionID is not a sale, sales tax, positive adjustment, or deferred transfer', 16, 1);

	-- get info from payment transaction
	declare @assignedToMemberID int, @ownedByOrgID int, @payProfileID int, @payProfileCode varchar(20),
		@payCashGLID int, @payCashGLName varchar(200)
	select @assignedToMemberID = t.assignedToMemberID, 
		@ownedByOrgID = t.ownedByOrgID,
		@payProfileID = mp.profileID,
		@payProfileCode = mp.profileCode,
		@payCashGLID = glDeb.GLAccountID,
		@payCashGLName = glDeb.AccountName
	from dbo.tr_transactions as t
	inner join dbo.tr_transactionPayments as tp on tp.transactionID = t.transactionID
	inner join dbo.mp_profiles as mp on mp.profileID = tp.profileID
	inner join dbo.tr_GLAccounts as glDeb on glDeb.GLAccountID = t.debitGLAccountID
	where t.transactionID = @paymentTransactionID

	-- dont assume memberid is the active one. get the active one.
	select @assignedToMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @assignedToMemberID 
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members
		where memberID = @recordedByMemberID 

	-- if amount is positive, allocating. Debit is DEP, Credit is AR.
	-- if amount is negative, deallocating. Debit is AR, Credit is DEP.
	declare @debitGLAccountID int, @creditGLAccountID int, @ARGLAID int, @DEPGLAID int
	select @ARGLAID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and isSystemAccount = 1
		and GLCode = 'ACCOUNTSRECEIVABLE'
		and [status] = 'A'
	select @DEPGLAID = glaccountid 
		from dbo.tr_GLAccounts 
		where orgID = @ownedByOrgID
		and isSystemAccount = 1
		and GLCode = 'DEPOSITS'
		and [status] = 'A'
	if @amount > 0 BEGIN
		select @debitGLAccountID = @DEPGLAID
		select @creditGLAccountID = @ARGLAID
	END
	ELSE BEGIN
		select @debitGLAccountID = @ARGLAID
		select @creditGLAccountID = @DEPGLAID
	END

	-- ensure we have active debit/credit accts
	IF @debitGLAccountID is null or @creditGLAccountID is null
		RAISERROR('debitGLAccountID or creditGLAccountID is null', 16, 1);

	-- if allocating to a sale on an open/pending invoice, close it. Only closed inv can accept payment.
	declare @invoiceID int, @invstatus varchar(50)
	select @invoiceID=i.invoiceID, @invstatus=ins.status
		from dbo.tr_invoices as i
		inner join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
		inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
		where it.transactionID = @saleTransactionID
	IF @amount > 0 AND @invstatus <> 'Closed'
		EXEC dbo.tr_closeInvoice @enteredByMemberID=@recordedByMemberID, @invoiceIDList=@invoiceID

	-- insert into transactions
	-- ensure amount is abs
	INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
		amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
		typeID, accrualDate, debitGLAccountID, creditGLAccountID)
	VALUES (@ownedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID(@status), null, null, 
		abs(@amount), getdate(), @transactionDate, @assignedToMemberID, @recordedByMemberID, @statsSessionID, 
		5, @transactionDate, @debitGLAccountID, @creditGLAccountID)
		select @transactionID = SCOPE_IDENTITY()

	-- insert into relationships
	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
	VALUES (dbo.fn_tr_getRelationshipTypeID('AllocPayTrans'), @transactionID, @paymentTransactionID)

	INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
	VALUES (dbo.fn_tr_getRelationshipTypeID('AllocSaleTrans'), @transactionID, @saleTransactionID)

	-- add allocation to batch
	-- put on same batch as payment if that batch is open.
	-- otherwise, put on daily exception batch
	declare @allocBatchID int, @batchCode varchar(40), @batchName varchar(400)
	select @allocBatchID = b.batchID
		from dbo.tr_batchTransactions as bt
		inner join dbo.tr_batches as b on b.batchID = bt.batchID
		where bt.transactionID = @paymentTransactionID
		and b.statusID = 1
	IF @allocBatchID is null BEGIN
		select @batchCode = CONVERT(CHAR(8),@transactionDate,112) + '_' + cast(@payProfileID as varchar(10)) + '_' + cast(@payCashGLID as varchar(10)) + '_EX'
		select @batchName = CONVERT(CHAR(8),@transactionDate,112) + ' ' + @payProfileCode + ' ' + @payCashGLName + ' Exceptions'
		select @allocBatchID = b.batchID
			from dbo.tr_batches as b
			where b.orgID = @ownedByOrgID
			and b.batchCode = @batchCode
			and b.statusID = 1
			and b.isSystemCreated = 1
		IF @allocBatchID is null 
			EXEC dbo.tr_createBatch @orgID=@ownedByOrgID, @payProfileID=@payProfileID, @batchCode=@batchCode, 
				@batchName=@batchName, @controlAmt=0, @controlCount=0, @depositDate=@transactionDate, 
				@isSystemCreated=1, @createdByMemberID=null, @batchID=@allocBatchID OUTPUT 
	END
	INSERT INTO dbo.tr_batchTransactions (batchID, transactionID)
	VALUES (@allocBatchID, @transactionID)

	-- update payment cache	
	declare @refundableAmount money, @allocatedAmount money
	select @refundableAmount = refundableAmount from dbo.fn_tr_getRefundableAmountofPayment(@paymentTransactionID,null)
	select @allocatedAmount = allocatedAmount from dbo.fn_tr_getAllocatedAmountofPayment(@paymentTransactionID,null)

	update dbo.tr_transactionPayments
	set cache_allocatedAmountOfPayment = @allocatedAmount,
		cache_refundableAmountOfPayment = @refundableAmount
	where transactionID = @paymentTransactionID

	-- update sales cache
	-- if allocating to an adjustment, update cache of sale or tax it is adjusting
	declare @stTypeID int, @stID int, @activePaymentAllocatedAmount money, @pendingPaymentAllocatedAmount money
	select @stTypeID = typeID from dbo.tr_transactions where transactionID = @saleTransactionID 
	IF @stTypeID in (1,7)
		select @stID = @saleTransactionID
	ELSE
		select @stID = tSale.transactionID
			from dbo.tr_transactions as tSale
			inner join dbo.tr_relationships as tR on tR.appliedToTransactionID = tSale.transactionID and tR.transactionID = @saleTransactionID
			inner join dbo.tr_relationshipTypes as trt on trt.typeID = tR.typeID and trt.type = 'AdjustTrans'

	select @activePaymentAllocatedAmount = activePaymentAllocatedAmount,
		@pendingPaymentAllocatedAmount = pendingPaymentAllocatedAmount
	from dbo.fn_tr_getAllocatedAmountofSale(@stID)

	UPDATE dbo.tr_transactionSales
	SET cache_activePaymentAllocatedAmount = @activePaymentAllocatedAmount,
		cache_pendingPaymentAllocatedAmount = @pendingPaymentAllocatedAmount
	WHERE transactionID = @stID

	-- update invoiceTransactions cache
	select @activePaymentAllocatedAmount = null, @pendingPaymentAllocatedAmount = null
	select @activePaymentAllocatedAmount = activePaymentAllocatedAmount,
		@pendingPaymentAllocatedAmount = pendingPaymentAllocatedAmount
	from dbo.fn_tr_getAllocatedAmountofSaleOrAdj(@saleTransactionID,null)
	
	UPDATE dbo.tr_invoiceTransactions
	SET cache_activePaymentAllocatedAmount = @activePaymentAllocatedAmount,
		cache_pendingPaymentAllocatedAmount = @pendingPaymentAllocatedAmount
	WHERE transactionID = @saleTransactionID

	-- check the in-bound rules.
	-- sale - new cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount must be between 0 and cache_amountAfterAdjustment
	-- payment - new cache_allocatedAmountOfPayment must be between 0 and cache_refundableAmountOfPayment
	IF NOT EXISTS (select saleID from dbo.tr_transactionSales where transactionID = @stID and cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount between 0 and cache_amountAfterAdjustment)
		OR NOT EXISTS (select itID from dbo.tr_invoiceTransactions where transactionID = @saleTransactionID and cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount between 0 and cache_invoiceAmountAfterAdjustment)
		OR NOT EXISTS (select paymentID from dbo.tr_transactionPayments where transactionID = @paymentTransactionID and cache_allocatedAmountOfPayment between 0 and cache_refundableAmountOfPayment)
		RAISERROR('in-bounds checking failed', 16, 1);

	-- cleanup invoice
	-- if invoice is closed and is now fully paid with active payments, mark it as paid
	-- if invoice is paid and is now not fully paid with active payments, mark it as closed
	declare @amtDueNoPendingOnInvoice money
	select @invoiceID=i.invoiceID, @invstatus=ins.status
		from dbo.tr_invoices as i
		inner join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
		inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
		where it.transactionID = @saleTransactionID
	select @amtDueNoPendingOnInvoice = sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount)
		from dbo.tr_invoiceTransactions as it
		inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
		where it.invoiceID = @invoiceID
		and t.statusID <> 2
	IF @invstatus = 'closed' and @amtDueNoPendingOnInvoice = 0 BEGIN
		update dbo.tr_invoices
		set statusID = 4, payProfileID = null
		where invoiceID = @invoiceID
		
		insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
		values (@invoiceID, getdate(), 4, 3, @recordedByMemberID)
	END
	IF @invstatus = 'paid' and @amtDueNoPendingOnInvoice > 0 BEGIN
		update dbo.tr_invoices
		set statusID = 3
		where invoiceID = @invoiceID

		insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
		values (@invoiceID, getdate(), 3, 4, @recordedByMemberID)
	END

	-- update credit balances
	EXEC dbo.tr_updateCreditBalanceByMember @memberID=@assignedToMemberID

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @transactionID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER FUNCTION [dbo].[tr_getBatchTransactions] (@orgID int, @batchID int)	
RETURNS TABLE 
AS
RETURN 
(
	WITH allBT as (
		select t.transactionID, t.typeID, t.assignedToMemberID, t.recordedByMemberID, 
			t.debitGLAccountID, t.creditGLAccountID, t.transactionDate, t.amount, t.detail
		from dbo.tr_batches as b
		inner join dbo.tr_batchTransactions as bt on bt.batchID = b.batchID
		inner join dbo.tr_transactions as t on t.transactionID = bt.transactionID
		where b.batchID = @batchID
		and b.orgID = @orgID
	)
	select tmp.transactionID, tmp.payTID, tmp.debitGLAccountID, tmp.creditGLAccountID, 
		tmp.CashGLAccountID, tmp.RevenueGLAccountID, tmp.CashTransactionID, tmp.RevenueTransactionID,
		tmp.transactionDate, tmp.amount, tmp.detail, tmp.gatewayTransactionID, tmp.gatewayApprovalCode, 
		case when tmp.typeID = 5 and tmp.amount < 0 then 'Deallocation' else tt.[type] end as [type],
		m2.memberid, m2.memberNumber, m2.company as memberCompany,
		m2.lastname 
			+ case when o.hasSuffix = 1 then isnull(' ' + nullif(m2.suffix,''),'') else '' end
			+ ', ' + m2.firstname 
			+ case when o.hasMiddleName = 1 then isnull(' ' + nullif(m2.middlename,''),'') else '' end 
			as memberName,
		recordedByM2.memberid as recordedByMemberID, recordedByM2.membernumber as recordedByMemberNumber, 
		recordedByM2.lastname 
			+ case when o.hasSuffix = 1 then isnull(' ' + nullif(recordedByM2.suffix,''),'') else '' end
			+ ', ' + recordedByM2.firstname 
			+ case when o.hasMiddleName = 1 then isnull(' ' + nullif(recordedByM2.middlename,''),'') else '' end 
		as recordedByMemberName
	from (
		select allBT.transactionID, allBT.transactionID as payTID, allBT.typeID, allBT.assignedToMemberID, 
			allBT.recordedByMemberID, allBT.debitGLAccountID, allBT.creditGLAccountID,
			allBT.debitGLAccountID as CashGLAccountID, cast(null as int) as RevenueGLAccountID, 
			allBT.transactionID as cashTransactionID, cast(null as int) as RevenueTransactionID,
			allBT.transactionDate, allBT.amount, allBT.detail, ph.gatewayTransactionID, 
			isnull(ph.gatewayApprovalCode,'') as gatewayApprovalCode
		from allBT
		inner join dbo.tr_transactionPayments as tp on tp.transactionID = allBT.transactionID
		inner join dbo.tr_paymentHistory as ph on ph.historyID = tp.historyID
		where allBT.typeID = 2
			union all
		select allBT.transactionID, allBT.transactionID, allBT.typeID, allBT.assignedToMemberID, 
			allBT.recordedByMemberID, allBT.debitGLAccountID, allBT.creditGLAccountID,
			allBT.creditGLAccountID, cast(null as int), 
			allBT.transactionID as cashTransactionID, cast(null as int) as RevenueTransactionID,
			allBT.transactionDate, allBT.amount*-1, allBT.detail, ph.gatewayTransactionID, 
			isnull(ph.gatewayApprovalCode,'') as gatewayApprovalCode
		from allBT
		inner join dbo.tr_transactionPayments as tp on tp.transactionID = allBT.transactionID
		inner join dbo.tr_paymentHistory as ph on ph.historyID = tp.historyID
		where allBT.typeID = 4
			union all
		select allBT.transactionID, rNSF.appliedToTransactionID, allBT.typeID, 
			allBT.assignedToMemberID, allBT.recordedByMemberID, allBT.debitGLAccountID, allBT.creditGLAccountID,
			allBT.creditGLAccountID, null,
			rNSF.appliedToTransactionID as cashTransactionID, cast(null as int) as RevenueTransactionID,
			allBT.transactionDate, allBT.amount*-1, 'NSF of ' + allBT.detail, null, null
		from allBT
		inner join dbo.tr_relationships as rNSF on rNSF.transactionID = allBT.transactionID and rNSF.typeID = 13
		where allBT.typeID = 9
			union all
		select allBT.transactionID, tPay.transactionID, allBT.typeID, 
			allBT.assignedToMemberID, allBT.recordedByMemberID, allBT.debitGLAccountID, allBT.creditGLAccountID,
			tPay.debitGLAccountID, allBT.creditGLAccountID,  
			tPay.transactionID as cashTransactionID, allBT.transactionID as RevenueTransactionID,
			allBT.transactionDate, allBT.amount, 'Write-off of ' + allBT.detail, null, null
		from allBT
		inner join dbo.tr_relationships as rWO on rWO.transactionID = allBT.transactionID and rWO.typeID = 7
		inner join dbo.tr_transactions as tPay on tPay.transactionID = rWO.appliedToTransactionID
		where allBT.typeID = 6
			union all
		select allBT.transactionID, tPay.transactionID, allBT.typeID, tSaleTaxAdj.assignedToMemberID, 
			allBT.recordedByMemberID, allBT.debitGLAccountID, allBT.creditGLAccountID, 
			tPay.debitGLAccountID, tSaleTaxAdj.creditGLAccountID, 
			tPay.transactionID as cashTransactionID, tSaleTaxAdj.transactionID as RevenueTransactionID,
			allBT.transactionDate, case when glDeb.GLCode = 'ACCOUNTSRECEIVABLE' then allBT.amount*-1 else allBT.amount end, 
			case when glDeb.GLCode = 'ACCOUNTSRECEIVABLE' then 'Deallocation from ' + tSaleTaxAdj.detail else 'Allocation to ' + tSaleTaxAdj.detail end,
			null, null
		from allBT
		inner join dbo.tr_glAccounts as glDeb on glDeb.glaccountID = allBT.debitGLAccountID
		inner join dbo.tr_relationships as rAllocPay on rAllocPay.transactionID = allBT.transactionID and rAllocPay.typeID = 2
		inner join dbo.tr_relationships as rAllocSale on rAllocSale.transactionID = allBT.transactionID
		inner join dbo.tr_relationshipTypes as rtAllocSale on rtAllocSale.typeID = rAllocSale.typeID and rtAllocSale.type = 'AllocSaleTrans'
		inner join dbo.tr_transactions as tSaleTaxAdj on tSaleTaxAdj.transactionID = rAllocSale.appliedToTransactionID
		inner join dbo.tr_transactions as tPay on tPay.transactionID = rAllocPay.appliedToTransactionID
		where allBT.typeID = 5
			union all
		select allBT.transactionID, tVoided.transactionID, allBT.typeID, 
			allBT.assignedToMemberID, allBT.recordedByMemberID, allBT.debitGLAccountID, allBT.creditGLAccountID,
			tVoided.debitGLAccountID, null,  
			tVoided.transactionID as cashTransactionID, null as RevenueTransactionID,
			allBT.transactionDate, allBT.amount*-1, 'VOID of ' + allBT.detail, null, null
		from allBT
		inner join dbo.tr_relationships as rVoid on rVoid.transactionID = allBT.transactionID and rVoid.typeID = 8
		inner join dbo.tr_transactions as tVoided on tVoided.transactionID = rVoid.appliedToTransactionID
		where allBT.typeID = 8
		and tVoided.typeID = 2
			union all
		select allBT.transactionID, tVoided.transactionID, allBT.typeID, 
			allBT.assignedToMemberID, allBT.recordedByMemberID, allBT.debitGLAccountID, allBT.creditGLAccountID,
			tVoided.creditGLAccountID, null,  
			tVoided.transactionID as cashTransactionID, null as RevenueTransactionID,
			allBT.transactionDate, allBT.amount, 'VOID of ' + allBT.detail, null, null
		from allBT
		inner join dbo.tr_relationships as rVoid on rVoid.transactionID = allBT.transactionID and rVoid.typeID = 8
		inner join dbo.tr_transactions as tVoided on tVoided.transactionID = rVoid.appliedToTransactionID
		where allBT.typeID = 8
		and tVoided.typeID = 4
			union all
		select allBT.transactionID, rNSF.appliedToTransactionID, allBT.typeID, 
			allBT.assignedToMemberID, allBT.recordedByMemberID, allBT.debitGLAccountID, allBT.creditGLAccountID,
			tVoided.creditGLAccountID, null,  
			rNSF.appliedToTransactionID as cashTransactionID, null as RevenueTransactionID,
			allBT.transactionDate, allBT.amount, 'VOID of NSF of ' + allBT.detail, null, null
		from allBT
		inner join dbo.tr_relationships as rVoid on rVoid.transactionID = allBT.transactionID and rVoid.typeID = 8
		inner join dbo.tr_transactions as tVoided on tVoided.transactionID = rVoid.appliedToTransactionID
		inner join dbo.tr_relationships as rNSF on rNSF.transactionID = tVoided.transactionID and rNSF.typeID = 13
		where allBT.typeID = 8
		and tVoided.typeID = 9
			union all
		select allBT.transactionID, tPay.transactionID, allBT.typeID, 
			allBT.assignedToMemberID, allBT.recordedByMemberID, allBT.debitGLAccountID, allBT.creditGLAccountID,
			tPay.debitGLAccountID, tVoided.creditGLAccountID,
			tPay.transactionID as cashTransactionID, tVoided.transactionID as RevenueTransactionID,
			allBT.transactionDate, allBT.amount*-1, 'VOID of Write-off of ' + allBT.detail, null, null
		from allBT
		inner join dbo.tr_relationships as rVoid on rVoid.transactionID = allBT.transactionID and rVoid.typeID = 8
		inner join dbo.tr_transactions as tVoided on tVoided.transactionID = rVoid.appliedToTransactionID
		inner join dbo.tr_relationships as rWO on rWO.transactionID = tVoided.transactionID and rWO.typeID = 7
		inner join dbo.tr_transactions as tPay on tPay.transactionID = rWO.appliedToTransactionID
		where allBT.typeID = 8
		and tVoided.typeID = 6
			union all
		select allBT.transactionID, rAllocPay.appliedToTransactionID, allBT.typeID, tSaleTaxAdj.assignedToMemberID, 
			allBT.recordedByMemberID, allBT.debitGLAccountID, allBT.creditGLAccountID, 
			tPay.debitGLAccountID, tSaleTaxAdj.creditGLAccountID, 
			tPay.transactionID as cashTransactionID, tSaleTaxAdj.transactionID as RevenueTransactionID,
			allBT.transactionDate, case when glDeb.GLCode = 'ACCOUNTSRECEIVABLE' then allBT.amount else allBT.amount*-1 end, 
			case when glDeb.GLCode = 'ACCOUNTSRECEIVABLE' then 'VOID of Deallocation from ' + tSaleTaxAdj.detail else 'VOID of Allocation to ' + tSaleTaxAdj.detail end,
			null, null
		from allBT
		inner join dbo.tr_relationships as rVoid on rVoid.transactionID = allBT.transactionID and rVoid.typeID = 8
		inner join dbo.tr_transactions as tVoided on tVoided.transactionID = rVoid.appliedToTransactionID
		inner join dbo.tr_glAccounts as glDeb on glDeb.glaccountID = tVoided.debitGLAccountID
		inner join dbo.tr_relationships as rAllocPay on rAllocPay.transactionID = tVoided.transactionID and rAllocPay.typeID = 2
		inner join dbo.tr_relationships as rAllocSale on rAllocSale.transactionID = tVoided.transactionID
		inner join dbo.tr_relationshipTypes as rtAllocSale on rtAllocSale.typeID = rAllocSale.typeID and rtAllocSale.type = 'AllocSaleTrans'
		inner join dbo.tr_transactions as tSaleTaxAdj on tSaleTaxAdj.transactionID = rAllocSale.appliedToTransactionID
		inner join dbo.tr_transactions as tPay on tPay.transactionID = rAllocPay.appliedToTransactionID
		where allBT.typeID = 8
		and tVoided.typeID = 5
	) as tmp
	inner join dbo.tr_types as tt on tt.typeID = tmp.typeID
	inner join dbo.ams_members as m on m.memberID = tmp.assignedToMemberID
	inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
	inner join dbo.ams_members as recordedByM on recordedBym.memberID = tmp.recordedByMemberID
	inner join dbo.ams_members as recordedByM2 on recordedByM2.memberID = recordedByM.activeMemberID
	inner join dbo.organizations as o on o.orgID = m2.orgID
)
GO

ALTER PROC dbo.cache_members_populateMemberConditionCache_ACCT_EXISTS_ALLOCSUM
AS

insert into #cache_members_conditions_shouldbe
select m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
inner join #tblMembers as m on m.memberid = m2.activeMemberID
inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
where tblc.subProc = 'ACCT_EXISTS_ALLOCSUM'
	union
select m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
inner join #tblMembers as m on m.memberid = m2.activeMemberID
inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
where tblc.subProc = 'ACCT_EXISTS_ALLOCSUM' 

GO

ALTER PROC dbo.cache_members_populateMemberConditionCache_ACCT_NOTEXISTS_ALLOCSUM
AS

IF OBJECT_ID('tempdb..#tblCondAccNotExist') IS NOT NULL
	DROP TABLE #tblCondAccNotExist

select m.memberid, tblc.conditionID
into #tblCondAccNotExist
from #tblCondALL as tblc
inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
inner join #tblMembers as m on m.memberid = m2.activeMemberID
inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
where tblc.subProc = 'ACCT_NOTEXISTS_ALLOCSUM' 
	union
select m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
inner join #tblMembers as m on m.memberid = m2.activeMemberID
inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
where tblc.subProc = 'ACCT_NOTEXISTS_ALLOCSUM' 

insert into #cache_members_conditions_shouldbe
select distinct mOuter.memberid, tblcOuter.conditionID
from #tblCondALL as tblcOuter
inner join #tblMembers as mOuter on mOuter.memberid = mOuter.memberID
where tblcOuter.subProc = 'ACCT_NOTEXISTS_ALLOCSUM' 
	except
select memberid, conditionID
from #tblCondAccNotExist

IF OBJECT_ID('tempdb..#tblCondAccNotExist') IS NOT NULL
	DROP TABLE #tblCondAccNotExist

GO

ALTER PROC dbo.cache_members_populateMemberConditionCache_ACCT_NEQ_ALLOCSUM
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_NEQ_ALLOCSUM'
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_NEQ_ALLOCSUM'	
) as tmp
group by memberid, conditionID, conditionValue
having sum(allocAmt) <> conditionValue

GO

ALTER PROC dbo.cache_members_populateMemberConditionCache_ACCT_LTE_ALLOCSUM
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_LTE_ALLOCSUM'	
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_LTE_ALLOCSUM'	
) as tmp
group by memberid, conditionID, conditionValue
having sum(allocAmt) <= conditionValue

GO


ALTER PROC dbo.cache_members_populateMemberConditionCache_ACCT_LT_ALLOCSUM
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_LT_ALLOCSUM'	
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_LT_ALLOCSUM'	
) as tmp
group by memberid, conditionID, conditionValue
having sum(allocAmt) < conditionValue

GO

ALTER PROC dbo.cache_members_populateMemberConditionCache_ACCT_GTE_ALLOCSUM
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_GTE_ALLOCSUM'	
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_GTE_ALLOCSUM'	
) as tmp
group by memberid, conditionID, conditionValue
having sum(allocAmt) >= conditionValue

GO

ALTER PROC dbo.cache_members_populateMemberConditionCache_ACCT_GT_ALLOCSUM
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_GT_ALLOCSUM'	
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_GT_ALLOCSUM'	
) as tmp
group by memberid, conditionID, conditionValue
having sum(allocAmt) > conditionValue

GO

ALTER PROC dbo.cache_members_populateMemberConditionCache_ACCT_EQ_ALLOCSUM
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_EQ_ALLOCSUM'	
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_EQ_ALLOCSUM'	
) as tmp
group by memberid, conditionID, conditionValue
having sum(allocAmt) = conditionValue

GO

ALTER PROC dbo.cache_members_populateMemberConditionCache_ACCT_BETWEEN_ALLOCSUM
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValueLower, accsplit.conditionValueUpper
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_BETWEEN_ALLOCSUM'
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValueLower, accsplit.conditionValueUpper
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_BETWEEN_ALLOCSUM'
) as tmp
group by memberid, conditionID, conditionValueLower, conditionValueUpper
having sum(allocAmt) between conditionValueLower and conditionValueUpper

GO

