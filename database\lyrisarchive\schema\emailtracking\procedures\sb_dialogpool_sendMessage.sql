ALTER PROC dbo.sb_dialogpool_sendMessage
@fromService SYSNAME,
@toService SYSNAME,
@onContract SYSNAME,
@messageType SYSNAME,
@messageBody XML

AS

SET NOCOUNT ON;
BEGIN TRY

	DECLARE @dialogHandle UNIQUEIDENTIFIER, @sendCount BIGINT, @counter INT = 1, @error INT;

	BEGIN TRAN;
		-- Will need a loop to retry in case the dialog is in a state that does not allow transmission
		WHILE (1=1) BEGIN
			-- Claim a dialog from the dialog pool.
			-- A new one will be created if none are available.
			EXEC dbo.sb_getDialogFromPool @fromService=@fromService, @toService=@toService, @onContract=@onContract, 
				@dialogHandle=@dialogHandle OUTPUT, @sendCount=@sendCount OUTPUT;

			BEGIN TRY
				-- Attempt to SEND on the dialog
				-- If the @messageBody is not null it must be sent explicitly
				IF (@messageBody IS NOT NULL)
					SEND ON CONVERSATION @dialogHandle MESSAGE TYPE @messageType (@messageBody);

				-- Messages with no body must *not* specify the body, cannot send a NULL value argument
				ELSE
					SEND ON CONVERSATION @dialogHandle MESSAGE TYPE @messageType;
			
				SELECT @error = @@ERROR;
			END TRY
			BEGIN CATCH
				SET @error = 1;
				SET @counter = 0;
			END CATCH

			IF @error = 0 BEGIN
				-- Successful send, increment count and exit the loop
				SET @sendCount = @sendCount + 1;
				BREAK;
			END

			SELECT @counter = @counter+1;
			IF @counter > 10 BEGIN
				-- We failed 10 times in a row, something must be broken
				RAISERROR('Failed to SEND on a conversation for more than 10 times. Error %i.', 16, 1, @error) WITH LOG;
				BREAK;
			END

			-- Delete the associated dialog from the table and try again
			EXEC dbo.sb_deleteDialogFromPool @dialogHandle=@dialogHandle;
			SELECT @dialogHandle = NULL;
		END

		-- "Criterion" for dialog pool removal is send count > 1000.
		-- Modify to suit application.
		-- When deleting also inform the target to end the dialog.
		IF @sendCount > 1000 BEGIN
			EXEC dbo.sb_deleteDialogFromPool @dialogHandle=@dialogHandle;

			SEND ON CONVERSATION @dialogHandle MESSAGE TYPE [EmailTracking/EndOfStream];
		END
		ELSE BEGIN
			-- Free the dialog.
			EXEC dbo.sb_freeDialogFromPool @dialogHandle=@dialogHandle, @sendCount=@sendCount;
		END
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO
