ALTER PROCEDURE emailTracking_recordIncomingMessageDeliveredStatus
	@sesid varchar(255)
AS
SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	-- Update the emailtracking.dbo.incomingMessages table
	UPDATE emailtracking.dbo.incomingMessages
	SET
		dateCreated = GETDATE(),
		[status] = 'DELIVERED',
		[reason] = 'Message added to Lyr<PERSON> for delivery'
		WHERE sesid = @sesid;

	RETURN 0;    
END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO
