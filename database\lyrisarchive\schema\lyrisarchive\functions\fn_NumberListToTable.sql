ALTER FUNCTION [dbo].[fn_NumberListToTable] (@list varchar(max)) 
returns @VarcharTable table (ListItem int)
AS
begin
	declare @separator_position int 
	declare @list_value varchar(max) 
	
	select @list = @list + ','
	
	while patindex('%,%', @list) <> 0 
	begin
		select @separator_position = patindex('%,%', @list)
		select @list_value = left(@list, @separator_position - 1)

		Insert @VarcharTable
		Values (Cast(@list_value as int))

		select @list = stuff(@list, 1, @separator_position, '')
	end
	return
end
GO
