-- 18:05 on dev
-- 4:11 on beta
-- 3:13 on prod

use datatransfer
GO


/* START SET VARIABLES HERE */
DECLARE @sitecode varchar(10), @tmpFile varchar(400), @timeZoneID int, @replyToEmail varchar(100),
	@deleteMissingCalendars bit, @deleteMissingMembers bit,	@deleteMissingCategories bit, @stopBeforeImport bit
set @sitecode = 'NM'
set @tmpFile = 'e:\temp\'
set @timeZoneID = 4
set @replyToEmail = '<EMAIL>'
set @deleteMissingCalendars = 0
set @deleteMissingMembers = 1
set @deleteMissingCategories = 0
set @stopBeforeImport = 0
/* END SET VARIABLES HERE */



SET NOCOUNT ON

declare @orgID int, @siteID int, @errSection varchar(30), @qry varchar(2000), @minID int, @MCEventID int, @MCCalendarID int,
	@MCsystemMemberID int, @MCAllDayEvent bit, @eventCode varchar(15), @starttime datetime, @endtime datetime, @eventContentID int,
	@eventName varchar(400), @expirationContentID int, @maxEventID int, @MCRegistrationID int, @customQID int, @maxGeneral decimal(9,2),
	@maxEthics decimal(9,2), @offeringID int
declare @MCTypeCID int, @MCFeeCID int, @MCDueCID int, @MCCLEStateCID int, @MCCreditOTGeneral int, @MCCreditOTEthics int
select @orgID = orgID, @siteID = siteID from membercentral.dbo.sites where sitecode = @siteCode
select @MCsystemMemberID = memberID from membercentral.dbo.ams_members where memberNumber = 'SYSTEM' and orgID = 1


/* ************** */
/* initial checks */
/* ************** */
IF @siteID is null BEGIN
	set @errSection = 'initialchecks'
	GOTO on_error	
END


/* ********************* */
/* import data from file */
/* ********************* */
IF OBJECT_ID('tempdb..##tmpSeminars') IS NOT NULL 
	DROP TABLE ##tmpSeminars
IF OBJECT_ID('tempdb..##tmpRegistrants') IS NOT NULL 
	DROP TABLE ##tmpRegistrants
IF OBJECT_ID('tempdb..#tmpEvImport') IS NOT NULL 
	DROP TABLE #tmpEvImport
IF OBJECT_ID('tempdb..#tmpEvImportEV') IS NOT NULL 
	DROP TABLE #tmpEvImportEV

CREATE TABLE ##tmpSeminars (
	[eventCode] [varchar](15) NULL,
	[eventName] [varchar](400) NULL,
	[eventStartDate] [datetime] NULL,
	[eventEndDate] [datetime] NULL,
	[eventLocationDesc] [varchar](400) NULL,
	[eventLocationTitle] [varchar](400) NULL,
	[CLE_MIN] [varchar](200) NULL,
	[CAPACITY] [varchar](200) NULL,
	[RATE_M] [varchar](200) NULL,
	[RATE_NM] [varchar](200) NULL,
	[RATE_OT] [varchar](200) NULL,
	[TOT_CREDIT] [varchar](200) NULL,
	[ETH_CREDIT] [varchar](200) NULL
)
CREATE TABLE ##tmpRegistrants (
	SEMINAR	[varchar](15) NULL,
	JOURNAL	[varchar](15) NULL,
	TRN_ID [varchar](15) NULL,
	DATE [datetime] NULL,
	MEM_ID [varchar](30) NULL,
	TYPE [varchar](15) NULL,	
	LNAME [varchar](50) NULL,
	FNAME [varchar](50) NULL,
	ADDRESS1 [varchar](80) NULL,
	ADDRESS2 [varchar](80) NULL,
	CITY [varchar](80) NULL,
	STATE [varchar](80) NULL,
	ZIP [varchar](30) NULL,
	FEE [varchar](15) NULL,
	PAID [varchar](15) NULL,
	DUE [varchar](15) NULL,
	CLE_STATE [varchar](80) NULL,
	ATTENDED [varchar](15) NULL,
	FLAG [varchar](15) NULL,
	CLAIMED [varchar](15) NULL,
	ETHICS [varchar](15) NULL
)

BEGIN TRY
	SELECT @qry = 'BULK INSERT ##tmpSeminars FROM ''' + @tmpFile + 'SEMINAR.txt'' WITH (FIELDTERMINATOR = ''' + char(9) + ''', FIRSTROW = 2);'
	EXEC(@qry)
END TRY
BEGIN CATCH
	set @errSection = 'importfromfile ##tmpSeminars'
	GOTO on_error	
END CATCH

BEGIN TRY
	SELECT @qry = 'BULK INSERT ##tmpRegistrants FROM ''' + @tmpFile + 'ZZSEM.txt'' WITH (FIELDTERMINATOR = ''' + char(9) + ''', FIRSTROW = 2);'
	EXEC(@qry)
END TRY
BEGIN CATCH
	set @errSection = 'importfromfile ##tmpRegistrants'
    SELECT 
        ERROR_NUMBER() AS ErrorNumber
        ,ERROR_SEVERITY() AS ErrorSeverity
        ,ERROR_STATE() AS ErrorState
        ,ERROR_PROCEDURE() AS ErrorProcedure
        ,ERROR_LINE() AS ErrorLine
        ,ERROR_MESSAGE() AS ErrorMessage

    --will return the complete original error message as an error message
    DECLARE @ErrorMessage nvarchar(400), @ErrorNumber int, @ErrorSeverity int, @ErrorState int, @ErrorLine int
    SELECT @ErrorMessage = N'Error %d, Line %d, Message: '+ERROR_MESSAGE(),@ErrorNumber = ERROR_NUMBER(),@ErrorSeverity = ERROR_SEVERITY(),@ErrorState = ERROR_STATE(),@ErrorLine = ERROR_LINE()
    RAISERROR (@ErrorMessage, @ErrorSeverity, @ErrorState, @ErrorNumber,@ErrorLine)
	GOTO on_error	
END CATCH

-- For the records that result in negative general credits, please Zero out the Ethics credit and leave the General credits as they are
select sem.eventCode, sem.eventName, sem.eventStartDate, sem.eventEndDate, sem.eventLocationDesc, sem.eventLocationTitle, 
	'Calendar of Events' as calendarName, 'Imported Event' as eventCategory, 
	reg.DATE as registrantDate, reg.MEM_ID as membernumber, reg.type, reg.fee, reg.due, reg.cle_state, 
	ethics = 
		case
		when cast(isnull(replace(reg.claimed,'*',0),0) as decimal(9,2))-cast(isnull(reg.ethics,0) as decimal(9,2)) < 0 then 0
		else cast(isnull(reg.ethics,0) as decimal(9,2))
		end,
	general = 
		case
		when cast(isnull(replace(reg.claimed,'*',0),0) as decimal(9,2))-cast(isnull(reg.ethics,0) as decimal(9,2)) < 0 then cast(isnull(replace(reg.claimed,'*',0),0) as decimal(9,2))
		else cast(isnull(replace(reg.claimed,'*',0),0) as decimal(9,2))-cast(isnull(reg.ethics,0) as decimal(9,2)) 
		end,
	case when cast(isnull(replace(reg.claimed,'*',0),0) as decimal(9,2))-cast(isnull(reg.ethics,0) as decimal(9,2)) < 0 then 1 else 0 end as negativeGeneral,
	reg.attended as registrantAttended,
	cast(null as int) as MCCalendarID,
	cast(null as int) as MCMemberID,
	cast(null as int) as MCEventID,
	cast(null as int) as MCCategoryID,
	cast(null as int) as MCRegistrationID,
	cast(0 as bit) as MCAllDayEvent,
	cast(null as int) as MCTypeCID,
	cast(null as int) as MCFeeCID,
	cast(null as int) as MCDueCID,
	cast(null as int) as MCCLEStateCID,
	cast(null as int) as MCCreditOTGeneral,
	cast(null as int) as MCCreditOTEthics,
	ROW_NUMBER() OVER(order by sem.eventCode, reg.MEM_ID) as autoID
into #tmpEvImport 
from ##tmpSeminars as sem
inner join ##tmpRegistrants as reg on reg.SEMINAR = sem.eventCode


IF OBJECT_ID('tempdb..##tmpSeminars') IS NOT NULL 
	DROP TABLE ##tmpSeminars
IF OBJECT_ID('tempdb..##tmpRegistrants') IS NOT NULL 
	DROP TABLE ##tmpRegistrants

/* ******************* */
/* remove extra quotes */
/* ******************* */
update #tmpEvImport
set calendarName = replace(calendarName,char(34),''),
	eventName = replace(eventName,char(34),''),
	eventLocationDesc = replace(eventLocationDesc,char(34),''),
	eventLocationTitle = replace(eventLocationTitle,char(34),'')

/* **************************** */
/* try to match by calendarname */
/* **************************** */
BEGIN TRY
	update tmp
	set tmp.MCCalendarID = c.calendarID
	from #tmpEvImport as tmp
	inner join membercentral.dbo.cms_applicationInstances as ai on ai.applicationInstanceName = tmp.calendarName
		and ai.siteID = @siteID
	inner join membercentral.dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID
		and sr.siteResourceStatusID = 1
	inner join membercentral.dbo.ev_calendars as c on c.applicationInstanceID = ai.applicationInstanceID

	IF @deleteMissingCalendars = 1
		delete from #tmpEvImport where MCCalendarID is null

	IF EXISTS (select * from #tmpEvImport where MCCalendarID is null) BEGIN
		select distinct calendarName as CalendarNotFound
		from #tmpEvImport
		where MCCalendarID is null
		order by 1

		RAISERROR('Error raised in TRY block.', 16, 1);
	END
END TRY
BEGIN CATCH
	set @errSection = 'calendarnotfound'
	GOTO on_error
END CATCH

/* **************************** */
/* try to match by membernumber */
/* **************************** */
BEGIN TRY
	update tmp
	set tmp.MCMemberID = m.memberid
	from #tmpEvImport as tmp
	inner join membercentral.dbo.ams_members as m on m.memberNumber = tmp.memberNumber
		and m.orgID = @orgID
		and m.memberID = m.activeMemberID

	IF @deleteMissingMembers = 1
		delete from #tmpEvImport where MCMemberID is null

	IF EXISTS (select * from #tmpEvImport where MCMemberID is null) BEGIN
		select distinct e.memberNumber as MemberNumberNotFound
			-- , r.fname, r.lname
		from #tmpEvImport e
--			inner join ##tmpRegistrants r on 
--				r.MEM_ID = e.memberNumber
		where e.MCMemberID is null
		order by 1

		RAISERROR('Error raised in TRY block.', 16, 1);
	END
END TRY
BEGIN CATCH
	set @errSection = 'membernotfound'
	GOTO on_error
END CATCH

/* ************************* */
/* try to match by eventcode */
/* ************************* */
BEGIN TRY
	update tmp
	set tmp.MCEventID = e.eventID
	from #tmpEvImport as tmp
	inner join membercentral.dbo.ev_events as e on e.reportCode = tmp.eventCode
		and e.status = 'A'
	inner join membercentral.dbo.ev_calendarEvents as ce on ce.sourceEventID = e.eventID
		and ce.calendarID = tmp.MCCalendarID
		and ce.calendarID = ce.sourceCalendarID

	update tmp
	set tmp.MCRegistrationID = r.registrationID
	from #tmpEvImport as tmp
	inner join membercentral.dbo.ev_registration as r on r.eventID = tmp.MCEventID and r.status = 'A'
	where tmp.MCEventID is not null
END TRY
BEGIN CATCH
	set @errSection = 'matchingeventcode'
	GOTO on_error
END CATCH

/* ************************ */
/* try to match by category */
/* ************************ */
BEGIN TRY
	update tmp
	set tmp.MCCategoryID = cat.categoryID
	from #tmpEvImport as tmp
	inner join membercentral.dbo.cms_applicationInstances as ai on ai.applicationInstanceName = tmp.calendarName
		and ai.siteID = @siteID
	inner join membercentral.dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID
		and sr.siteResourceStatusID = 1
	inner join membercentral.dbo.ev_calendars as c on c.applicationInstanceID = ai.applicationInstanceID
	inner join membercentral.dbo.ev_categories as cat on cat.calendarID = c.calendarID
		and cat.category = tmp.eventCategory

	IF @deleteMissingCategories = 1
		delete from #tmpEvImport where MCCategoryID is null

	IF EXISTS (select * from #tmpEvImport where MCCategoryID is null) BEGIN
		select distinct eventCategory as EventCategoryNotFound
		from #tmpEvImport
		where MCCategoryID is null
		order by 1

		RAISERROR('Error raised in TRY block.', 16, 1);
	END
END TRY
BEGIN CATCH
	set @errSection = 'categorynotfound'
	select *
	from #tmpEvImport

	GOTO on_error
END CATCH

/* *********** */
/* check dates */
/* *********** */
IF EXISTS (select * from #tmpEvImport where eventStartDate is null) BEGIN
	select 'Start Date is empty' as errReason, * 
	from #tmpEvImport
	where eventStartDate is null

	set @errSection = 'startempty'
	GOTO on_error
END
IF EXISTS (select * from #tmpEvImport where eventEndDate is null) BEGIN
	select 'End Date is empty' as errReason, * 
	from #tmpEvImport
	where eventEndDate is null

	set @errSection = 'endempty'
	GOTO on_error
END
IF EXISTS (select * from #tmpEvImport where eventStartDate > eventEndDate) BEGIN
	select 'Start Date is after End Date' as errReason, * 
	from #tmpEvImport
	where eventStartDate > eventEndDate

	set @errSection = 'startafterend'
	GOTO on_error
END
IF EXISTS (select * from #tmpEvImport where registrantDate is null) BEGIN
	update #tmpEvImport
	set registrantDate = eventStartDate
	where registrantDate is null
END


/* ***************** */
/* set all day event */
/* ***************** */
update #tmpEvImport
set MCAllDayEvent = 1
where eventStartDate = eventEndDate



/* ******************* */
/* check credit values */
/* ******************* */
IF EXISTS (select * from #tmpEvImport where ethics < 0 or general < 0) BEGIN
	select 'Negative credit values' as errReason, * 
	from #tmpEvImport
	where ethics < 0 or general < 0

	set @errSection = 'negativecredit'
	GOTO on_error
END

/* ************** */
/* check attended */
/* ************** */
update #tmpEvImport
set registrantAttended = 'No'
where registrantAttended is null or registrantAttended = 'N'

update #tmpEvImport
set registrantAttended = 'Yes'
where registrantAttended = 'Y'

IF EXISTS (select * from #tmpEvImport where registrantAttended not in ('Yes','No')) BEGIN
	select 'Bad values for Attended' as errReason, * 
	from #tmpEvImport
	where registrantAttended not in ('Yes','No')

	set @errSection = 'badattended'
	GOTO on_error
END


/* ******************* */
/* Get Distinct Events */
/* ******************* */
select calendarName, eventCode, eventName, eventCategory, eventStartDate, eventEndDate, MCCalendarID, MCEventID, MCCategoryID, 
	MCAllDayEvent, MCRegistrationID, MCTypeCID, MCFeeCID, MCDueCID, MCCLEStateCID, MCCreditOTGeneral, MCCreditOTEthics, 
	max(ethics) as maxEthics, max(general) as maxGeneral
into #tmpEvImportEV
from #tmpEvImport
group by calendarName, eventCode, eventName, eventCategory, eventStartDate, eventEndDate, MCCalendarID, MCEventID, MCCategoryID, 
	MCAllDayEvent, MCRegistrationID, MCTypeCID, MCFeeCID, MCDueCID, MCCLEStateCID, MCCreditOTGeneral, MCCreditOTEthics

ALTER TABLE #tmpEvImportEV ADD autoID int IDENTITY(1,1);


/* ********** */
/* IF TESTING */
/* ********** */
IF @stopBeforeImport = 1 BEGIN
	set @errSection = 'testing'
	GOTO on_error
END

/* ********** */
/* Add Events */
/* ********** */
EXEC membercentral.dbo.cache_perms_setStatus @orgID, 'disabled'
select @maxEventID = max(eventID) from membercentral.dbo.ev_events

BEGIN TRY
	SELECT @minID = min(autoID) FROM #tmpEvImportEV
	WHILE @minID is not null BEGIN
		select @MCEventID = null, @MCCalendarID = null, @MCAllDayEvent = null, @eventCode = null, @starttime = null, 
			@endtime = null, @eventName = null, @MCRegistrationID = null, @maxGeneral = null, @maxEthics = null, @customQID = null

		select @MCEventID = MCEventID, @MCCalendarID = MCCalendarID, @MCAllDayEvent = MCAllDayEvent, @eventCode = eventCode,
			@starttime = eventStartDate, @endtime = eventEndDate, @eventName = eventName, @MCRegistrationID = MCRegistrationID,
			@maxGeneral = maxGeneral, @maxEthics = maxEthics
			from #tmpEvImportEV
			where autoID = @minID

		print '@minID=' + cast(@minID as varchar(10)) + ' @eventCode=' + cast(@eventCode as varchar(20)) + ' @MCEventID=' + isnull(cast(@MCEventID as varchar(10)),'')

		IF @MCEventID is null BEGIN
			EXEC membercentral.dbo.ev_createEvent 
				@siteID=@siteID, 
				@calendarid=@MCCalendarID, 
				@eventTypeID=1, 
				@enteredByMemberID=@MCsystemMemberID, 
				@lockTimeZoneID=null, 
				@isAllDayEvent=@MCAllDayEvent, 
				@altRegistrationURL=null, 
				@status='A', 
				@reportCode=@eventCode, 
				@emailContactContent = 0,
				@emailLocationContent = 0,
				@emailCancelContent	= 0,
				@emailTravelContent	= 0,
				@eventID=@MCEventID OUTPUT
			print 'Created @MCEventID ' + cast(@MCEventID as varchar(10))
			EXEC membercentral.dbo.ev_createTime @eventID=@MCEventID, @timeZoneID=@timeZoneID, @startTime=@starttime, @endTime=@endtime
			select @eventContentID = eventContentID FROM membercentral.dbo.ev_events WHERE eventID = @MCEventID
			EXEC membercentral.dbo.cms_updateContent @contentID=@eventContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@eventName, @contentDesc='', @rawcontent=@eventName
		END

		IF @MCRegistrationID is null BEGIN 
			EXEC membercentral.dbo.ev_createRegistration @eventID=@MCEventID, @registrationTypeID=1, @startDate=@starttime, @endDate=@endtime, @registrantCap=null, @replyToEmail=@replyToEmail, @notifyEmail='', @isPriceBasedOnActual=1, @bulkCountByRate=0, @registrationID=@MCRegistrationID OUTPUT
			print 'Created @MCRegistrationID ' + cast(@MCRegistrationID as varchar(10))
			select @expirationContentID = expirationContentID FROM membercentral.dbo.ev_registration WHERE registrationID = @MCRegistrationID
			EXEC membercentral.dbo.cms_updateContent @contentID=@expirationContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle='Expiration Message', @contentDesc='', @rawcontent='Registration for this event has closed.'
		END

		UPDATE #tmpEvImportEV 
		SET MCEventID = @MCEventID, MCregistrationID = @MCRegistrationID 
		WHERE autoID = @minID

		UPDATE #tmpEvImport
		SET MCEventID = @MCEventID, MCregistrationID = @MCRegistrationID 
		WHERE eventCode = @eventCode

		select @MCTypeCID = null
		select @MCTypeCID = customID from membercentral.dbo.ev_registrationCustom where registrationID=@MCRegistrationID and titleOnInvoice='Type'
		IF @MCTypeCID is null BEGIN
			insert into membercentral.dbo.ev_registrationCustom (registrationID, areaID, fieldDesc, titleOnInvoice, customTypeID, isRequired, requiredMSG, fieldOrder, status, offerQty, amount, GLAccountID)
			VALUES (@MCRegistrationID, 2, 'Type', 'Type', 1, 0, null, 1, 'A', 0, null, null)
				select @MCTypeCID = SCOPE_IDENTITY()
		END
		UPDATE #tmpEvImportEV SET MCTypeCID = @MCTypeCID where autoID = @minID
		UPDATE #tmpEvImport SET MCTypeCID = @MCTypeCID where eventCode = @eventCode

		select @MCFeeCID = null
		select @MCFeeCID = customID from membercentral.dbo.ev_registrationCustom where registrationID=@MCRegistrationID and titleOnInvoice='Fee'
		IF @MCFeeCID is null BEGIN
			insert into membercentral.dbo.ev_registrationCustom (registrationID, areaID, fieldDesc, titleOnInvoice, customTypeID, isRequired, requiredMSG, fieldOrder, status, offerQty, amount, GLAccountID)
			VALUES (@MCRegistrationID, 2, 'Fee', 'Fee', 1, 0, null, 2, 'A', 0, null, null)
				select @MCFeeCID = SCOPE_IDENTITY()
		END
		UPDATE #tmpEvImportEV SET MCFeeCID = @MCFeeCID where autoID = @minID
		UPDATE #tmpEvImport SET MCFeeCID = @MCFeeCID where eventCode = @eventCode

		select @MCDueCID = null
		select @MCDueCID = customID from membercentral.dbo.ev_registrationCustom where registrationID=@MCRegistrationID and titleOnInvoice='Due'
		IF @MCDueCID is null BEGIN
			insert into membercentral.dbo.ev_registrationCustom (registrationID, areaID, fieldDesc, titleOnInvoice, customTypeID, isRequired, requiredMSG, fieldOrder, status, offerQty, amount, GLAccountID)
			VALUES (@MCRegistrationID, 2, 'Due', 'Due', 1, 0, null, 3, 'A', 0, null, null)
				select @MCDueCID = SCOPE_IDENTITY()
		END
		UPDATE #tmpEvImportEV SET MCDueCID = @MCDueCID where autoID = @minID
		UPDATE #tmpEvImport SET MCDueCID = @MCDueCID where eventCode = @eventCode

		select @MCCLEStateCID = null
		select @MCCLEStateCID = customID from membercentral.dbo.ev_registrationCustom where registrationID=@MCRegistrationID and titleOnInvoice='CLE State'
		IF @MCCLEStateCID is null BEGIN
			insert into membercentral.dbo.ev_registrationCustom (registrationID, areaID, fieldDesc, titleOnInvoice, customTypeID, isRequired, requiredMSG, fieldOrder, status, offerQty, amount, GLAccountID)
			VALUES (@MCRegistrationID, 2, 'CLE State', 'CLE State', 1, 0, null, 4, 'A', 0, null, null)
				select @MCCLEStateCID = SCOPE_IDENTITY()
		END
		UPDATE #tmpEvImportEV SET MCCLEStateCID = @MCCLEStateCID where autoID = @minID
		UPDATE #tmpEvImport SET MCCLEStateCID = @MCCLEStateCID where eventCode = @eventCode

		IF @maxGeneral > 0 or @maxEthics > 0 BEGIN
			select @offeringID = null
			select @offeringID = offeringID from membercentral.dbo.crd_offerings where eventID = @MCEventID and ASID = 47
			if @offeringID is null BEGIN
				EXEC membercentral.dbo.crd_addOffering 'Events', @MCEventID, 47, @offeringID OUTPUT
			END

			UPDATE membercentral.dbo.crd_offerings 
			SET statusID = 4 
			WHERE offeringID = @offeringID

			select @MCCreditOTGeneral = null			
			select @MCCreditOTGeneral = offeringTypeID from membercentral.dbo.crd_offeringTypes where offeringID = @offeringID and ASTID = 143
			IF @maxGeneral > 0 and @MCCreditOTGeneral is null BEGIN
				INSERT INTO membercentral.dbo.crd_offeringTypes (offeringID, ASTID, creditValue) 
				values (@offeringID, 143, @maxGeneral)
					select @MCCreditOTGeneral = SCOPE_IDENTITY()
			END
			update #tmpEvImportEV SET MCCreditOTGeneral = @MCCreditOTGeneral where autoID = @minID
			update #tmpEvImport SET MCCreditOTGeneral = @MCCreditOTGeneral where eventCode = @eventCode

			select @MCCreditOTEthics = null			
			select @MCCreditOTEthics = offeringTypeID from membercentral.dbo.crd_offeringTypes where offeringID = @offeringID and ASTID = 144
			IF @maxEthics > 0 and @MCCreditOTEthics is null BEGIN
				INSERT INTO membercentral.dbo.crd_offeringTypes (offeringID, ASTID, creditValue) 
				values (@offeringID, 144, @maxEthics)
					select @MCCreditOTEthics = SCOPE_IDENTITY()
			END
			update #tmpEvImportEV SET MCCreditOTEthics = @MCCreditOTEthics where autoID = @minID
			update #tmpEvImport SET MCCreditOTEthics = @MCCreditOTEthics where eventCode = @eventCode
		END

		SELECT @minID = min(autoID) FROM #tmpEvImportEV where autoID > @minID
	END

END TRY
BEGIN CATCH
	set @errSection = 'eventcreation'
	GOTO on_error
END CATCH

PRINT '******** done creating events ************'



/* ******************** */
/* Add Event Categories */
/* ******************** */
INSERT INTO membercentral.dbo.ev_eventCategories (eventID, categoryID)
SELECT distinct MCEventID, MCCategoryID
FROM #tmpEvImportEV
where MCEventID > @maxEventID
except (
	SELECT eventID, categoryID
	FROM membercentral.dbo.ev_eventCategories
)

PRINT '******** done creating event categories ************'


/* *********** */
/* Final Check */
/* *********** */
IF EXISTS (select * from #tmpEvImport where MCRegistrationID is null) BEGIN
	select 'Registration is missing' as errReason, * 
	from #tmpEvImport
	where MCRegistrationID is null

	set @errSection = 'registrationmissing'
	GOTO on_error
END


/* *************** */
/* Add Registrants */
/* *************** */
insert into membercentral.dbo.ev_registrants (registrationID, memberID, recordedOnSiteID, rateID, dateRegistered, status, attended)
select distinct MCRegistrationID, MCMemberID, @siteID, null, registrantDate, 'A', 0
from #tmpEvImport as e
where not exists (
	select registrantID
	from membercentral.dbo.ev_registrants
	where registrationID = e.MCRegistrationID
	and memberID = e.MCMemberID
)

PRINT '******** done creating registrants ************'


/* ************************ */
/* CUSTOM Q for Registrants */
/* ************************ */
INSERT INTO membercentral.dbo.ev_registrantDetails (registrantID, customID, customOptionID, customText, status)
select distinct r.registrantID, tmp.MCTypeCID, null, tmp.type, 'A'
from membercentral.dbo.ev_registrants as r
inner join #tmpEvImport as tmp on tmp.MCRegistrationID = r.registrationID and tmp.MCMemberID = r.memberID
where len(tmp.type) > 0
	union
select distinct r.registrantID, tmp.MCFeeCID, null, tmp.fee, 'A'
from membercentral.dbo.ev_registrants as r
inner join #tmpEvImport as tmp on tmp.MCRegistrationID = r.registrationID and tmp.MCMemberID = r.memberID
where len(tmp.fee) > 0
	union
select distinct r.registrantID, tmp.MCDueCID, null, tmp.due, 'A'
from membercentral.dbo.ev_registrants as r
inner join #tmpEvImport as tmp on tmp.MCRegistrationID = r.registrationID and tmp.MCMemberID = r.memberID
where len(tmp.due) > 0
	union
select distinct r.registrantID, tmp.MCCLEStateCID, null, tmp.cle_state, 'A'
from membercentral.dbo.ev_registrants as r
inner join #tmpEvImport as tmp on tmp.MCRegistrationID = r.registrationID and tmp.MCMemberID = r.memberID
where len(tmp.cle_state) > 0

PRINT '******** done creating registrant details ************'



/* ************************** */
/* ATTENDANCE for Registrants */
/* ************************** */
update evr
set evr.attended = case tmp.registrantAttended when 'Yes' then 1 else 0 end
from membercentral.dbo.ev_registrants as evr
inner join #tmpEvImport as tmp on tmp.MCMemberID = evr.memberID and tmp.MCRegistrationID = evr.registrationID
where evr.status = 'A'

PRINT '******** done creating registrant attendance ************'

/* ********************** */
/* CREDIT for Registrants */
/* ********************** */
INSERT INTO membercentral.dbo.crd_requests (offeringTypeID, lastDateToComplete, creditAwarded, creditValueAwarded, addedViaAward, registrantID)
select distinct tmp.MCCreditOTGeneral, tmp.eventStartDate, 1, tmp.general, 1, r.registrantID
from membercentral.dbo.ev_registrants as r
inner join #tmpEvImport as tmp on tmp.MCRegistrationID = r.registrationID and tmp.MCMemberID = r.memberID
where tmp.general > 0

INSERT INTO membercentral.dbo.crd_requests (offeringTypeID, lastDateToComplete, creditAwarded, creditValueAwarded, addedViaAward, registrantID)
select distinct tmp.MCCreditOTEthics, tmp.eventStartDate, 1, tmp.ethics, 1, r.registrantID
from membercentral.dbo.ev_registrants as r
inner join #tmpEvImport as tmp on tmp.MCRegistrationID = r.registrationID and tmp.MCMemberID = r.memberID
where tmp.ethics > 0


GOTO on_good

on_error:
	print 'import stopped at ' + @errSection
	goto on_done

on_good:
	print 'import success.'

on_done:
	print 'end of import.'

	IF OBJECT_ID('tempdb..##tmpSeminars') IS NOT NULL 
		DROP TABLE ##tmpSeminars
	IF OBJECT_ID('tempdb..##tmpRegistrants') IS NOT NULL 
		DROP TABLE ##tmpRegistrants
	IF OBJECT_ID('tempdb..#tmpEvImport') IS NOT NULL 
		DROP TABLE #tmpEvImport
	IF OBJECT_ID('tempdb..#tmpEvImportEV') IS NOT NULL 
		DROP TABLE #tmpEvImportEV

	EXEC membercentral.dbo.cache_perms_setStatus @orgID, 'enabled'

SET NOCOUNT OFF

