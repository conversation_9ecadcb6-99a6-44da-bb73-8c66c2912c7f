ALTER TABLE dbo.cms_pageTemplates ADD
	siteResourceID int NULL
GO
ALTER TABLE dbo.cms_pageTemplates ADD CONSTRAINT
	FK_cms_pageTemplates_cms_siteResources FOREIGN KEY
	(
	siteResourceID
	) REFERENCES dbo.cms_siteResources
	(
	siteResourceID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO


declare 
	@ResourceTypeClassID int, 
	@resourceTypeID int, 
	@trashID int, 
	@siteResourceStatusID int, 
	@siteResourceID int,
	@thisTemplateID int,
	@thisSiteID int


declare @templates TABLE (templateID int, siteID int)

select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

exec dbo.cms_createSiteResourceTypeClass
	@ResourceTypeClassName='pageTemplate', @ResourceTypeClassID=@ResourceTypeClassID OUTPUT

exec dbo.cms_createSiteResourceType
	@resourceTypeClassID=@ResourceTypeClassID, @resourceType='pageTemplate', @resourceTypeID=@resourceTypeID OUTPUT


insert into @templates (templateID, siteID)
select templateID, isnull(siteID,1) as siteID
from cms_pageTemplates
where siteResourceID is null
order by templateID


SELECT @thisTemplateID = min(templateID) from @templates
WHILE @thisTemplateID IS NOT NULL BEGIN
	select
		@thissiteID = siteID
	from @templates where templateID = @thisTemplateID


	exec dbo.cms_createSiteResource
		@resourceTypeID=@resourceTypeID, 
		@siteResourceStatusID=@siteResourceStatusID, 
		@siteID=@thisSiteID, 
		@isVisible=1, 
		@parentSiteResourceID=null, 
		@siteResourceID=@siteResourceID OUTPUT


	update cms_pageTemplates set
		siteResourceID = @siteResourceID
	where templateID = @thisTemplateID


	SELECT @thisTemplateID = min(templateID) from @templates where templateID > @thisTemplateID
END

GO

ALTER PROC [dbo].[cms_createPageTemplate]
@siteID int,
@templateTypeID int,
@templateName varchar(100),
@templateDesc varchar(300),
@templateFileName varchar(500),
@isMobile bit = 0,
@templateID int OUTPUT

AS

-- ensure @templateID is null (can be passed in)
SELECT @templateID = null

DECLARE 
	@rc int,
	@siteResourceStatusID int, 
	@siteResourceID int,
	@thisSiteID int,
	@resourceTypeID int

BEGIN TRAN

	-- check to see if template already exists
	select @templateID = templateID FROM dbo.cms_pageTemplates where (siteID = @siteID) and ((templateName = @templateName) or (templateFilename = @templateFilename)) and (status = 'A')

	-- if not, add template
	IF @templateID is null BEGIN

		select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')
		select @thisSiteID = isnull(@siteID,dbo.fn_getSiteIDFromSiteCode('MC'))
		select @resourceTypeID = dbo.fn_getResourceTypeID('pageTemplate')


		exec dbo.cms_createSiteResource
			@resourceTypeID=@resourceTypeID, 
			@siteResourceStatusID=@siteResourceStatusID, 
			@siteID=@thisSiteID, 
			@isVisible=1, 
			@parentSiteResourceID=null, 
			@siteResourceID=@siteResourceID OUTPUT


		INSERT INTO dbo.cms_pageTemplates (siteID, templateName, templateDesc, templateFileName, templateTypeID, status, isMobile, siteResourceID)
		VALUES (@siteID, @templateName, @templateDesc, @templateFileName, @templateTypeID, 'A', @isMobile, @siteResourceID)
			IF @@ERROR <> 0 GOTO on_error
			select @templateID = SCOPE_IDENTITY()
	
	
		-- add main zone (A) to template in all modes
		declare @zoneID int, @modeID int, @placementDesc varchar(50)
		select @zoneID = zoneID from dbo.cms_pageZones where zoneName = 'Main'
		select @modeID = min(modeID) from dbo.cms_pageModes
		select @placementDesc = 'Main Zone'
		while @modeID is not null
		BEGIN
			EXEC @rc = dbo.cms_createPageTemplatesModesZones @templateID, @modeID, @zoneID, @placementDesc
				IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
				select @modeID = min(modeID) from dbo.cms_pageModes where modeID > @modeID
		END
	END
	ELSE 
		SELECT @templateID = NULL
-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @templateID = 0
	RETURN -1

on_error2:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO


DECLARE 
	@rc int,
	@resourceTypeID int,
	@settingKeyTypeID int,
	@settingKeyValueID int

select @resourceTypeID = dbo.fn_getResourceTypeID('pageTemplate')
exec dbo.cms_createSiteResourceSettingKeyType
	@resourceTypeID=@resourceTypeID, @keyName='supportsBootstrap', @settingKeyTypeID=@settingKeyTypeID OUTPUT

exec dbo.cms_createSiteResourceSettingKeyValue
	@settingKeyTypeID=@settingKeyTypeID, @value='false', @settingKeyValueID=@settingKeyValueID OUTPUT

exec dbo.cms_createSiteResourceSettingKeyValue
	@settingKeyTypeID=@settingKeyTypeID, @value='true', @settingKeyValueID=@settingKeyValueID OUTPUT

GO

update dbo.cms_siteResourceSettingKeyTypes set
keyName = 'Supports Bootstrap'
where keyName = 'supportsBootstrap'

GO

CREATE PROC [dbo].[cms_deleteSiteResourceSettingsAll]
@siteResourceID int

AS

BEGIN TRAN

	-- delete any preexisting value for this setting
	delete
	from dbo.cms_siteResourceSettings
	where siteResourceID = @siteResourceID
		IF @@ERROR <> 0 GOTO on_error

-- normal exit
on_ok:
	IF @@TRANCOUNT > 0 COMMIT TRAN
	RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

ALTER PROCEDURE [dbo].[cms_getPageStructureByID] 
@siteID int,
@pageID int,
@languageID int,
@ovrMode varchar(30),
@processMobileOverrides bit,
@templateTypeName varchar(100)

AS

DECLARE @templateID int, @sectionID int, @pgResourceTypeID int, @modeID int, @ovrModeID int, @defaultTemplateID int, @defaultModeID int, @rootSectionID int, @pageIncludePlacements int
DECLARE @viewFunctionID int, @editFunctionID int
DECLARE @layoutInfo TABLE (templateID int, templateTypeName varchar(100), modeID int, modeName varchar(100), templateFilename varchar(100), sitecode varchar(10), orgcode varchar(10))
DECLARE @pageSectionPath TABLE (templateID int, modeID int, sectionID int, parentSectionID int, dataLevel int PRIMARY KEY,includePlacements bit)
DECLARE @zoneAssignments TABLE (autoid int IDENTITY(1,1), zoneID int, assignmentLevel varchar(10), sectionID int, siteResourceID int, sortOrder int, dataLevel int, isSSL bit, appInstanceID int, appInstanceName varchar(100), appTypeName varchar(100), appWidgetInstanceID int, appInstanceResourceID int, appWidgetTypeName varchar(100), appWidgetInstanceName varchar(100),contentID int,enableSocialMediaSharing bit, resourceTypeID int, resourceType varchar(100), resourceTypeClassName varchar(100), objectType char(1))

set @defaultTemplateID = 1
set @defaultModeID = 1

select @ovrModeID = modeID from cms_pageModes where modeName = @ovrMode

select 
	@pgResourceTypeID = sr.resourceTypeID, 
	@sectionID = p.sectionID, 
	@modeID = pm.modeID, 
	@templateID = isnull(mobilept.templateID,pt.templateid),
	@pageIncludePlacements = p.inheritPlacements
from cms_pages p (nolock)
inner join sites s
	on s.siteID = p.siteID
inner join cms_siteResources sr (nolock)
	on p.siteResourceID = sr.siteResourceID
	and pageID = @pageID
inner join dbo.cache_cms_derivedPageSectionSettings dpss (nolock)
	on dpss.sectionID = p.sectionID
inner join dbo.cms_pageTemplates pt (nolock)
	on pt.templateid = coalesce(p.ovTemplateID,dpss.ovTemplateID,@defaultTemplateID)
inner join dbo.cms_pageModes pm (nolock)
	on pm.modeID = coalesce(@ovrModeID,p.ovModeID,dpss.ovModeID,@defaultModeID)
left outer join dbo.cms_pageTemplates mobilept (nolock)
	on mobilept.templateid = coalesce(p.ovTemplateIDMobile,dpss.ovTemplateIDMobile)
	--and s.enableMobile = 1
	and @processMobileOverrides = 1


select @rootSectionID = sectionID from cms_pageSections where siteID = @siteID and parentSectionID is null

if (nullif(@ovrMode,'') is not null)
	select @ovrModeID = modeID from cms_pageModes where modeName = @ovrMode

-- find all zone assignments for page, not considering pageMode
insert into @zoneAssignments (zoneid, siteResourceID, assignmentLevel, sectionID, sortOrder, dataLevel,resourceTypeID,	resourceType, resourceTypeClassName,isSSL,contentID,enableSocialMediaSharing,objecttype)
select pzr.zoneid, pzr.siteResourceID, 'page' as assignmentLevel, 0 as sectionID, pzr.sortOrder, 0 as dataLevel, srt.resourceTypeID,srt.resourceType,srtc.resourceTypeClassName, isnull(c.isSSL,1),c.contentID, c.enableSocialMediaSharing,
objecttype = case
	when c.siteresourceID is not null then 'C'
	when ai.siteresourceID is not null then 'A'
	when awi.siteresourceID is not null then 'W'
end

from dbo.cms_pageZonesResources as pzr
inner join dbo.cms_siteResources sr
	on pzr.siteResourceID = sr.siteResourceID
	and pzr.pageID = @pageID
inner join dbo.cms_siteResourceStatuses srs
	ON sr.siteResourceStatusID = srs.siteResourceStatusID
	AND srs.siteResourceStatusDesc = 'Active'
inner join dbo.cms_siteResourceTypes srt ON sr.resourceTypeID = srt.resourceTypeID
inner join dbo.cms_siteResourceTypeClasses srtc ON srt.resourceTypeClassID = srtc.resourceTypeClassID
left outer join dbo.cms_content c on sr.siteResourceID = c.siteResourceID 
left outer join dbo.cms_applicationInstances ai on sr.siteResourceID = ai.siteResourceID
left outer join dbo.cms_applicationWidgetInstances awi on sr.siteResourceID = awi.siteResourceID


if @pageIncludePlacements = 1
BEGIN
	insert into @zoneAssignments (zoneid, siteResourceID, assignmentLevel, sectionID, sortOrder, dataLevel,resourceTypeID,	resourceType, resourceTypeClassName,isSSL,contentID,enableSocialMediaSharing,objecttype)
	select pszr.zoneid, pszr.siteResourceID, 
		assignmentLevel = case when rps.sectionID = @rootSectionID then 'site' else 'section' end,
		rps.sectionID, pszr.sortOrder, (rps.depth * -1) as dataLevel, srt.resourceTypeID,	srt.resourceType, srtc.resourceTypeClassName, isnull(c.isSSL,1),c.contentID, c.enableSocialMediaSharing,
		objecttype = case
			when c.siteresourceID is not null then 'C'
			when ai.siteresourceID is not null then 'A'
			when awi.siteresourceID is not null then 'W'
		end
			
	from dbo.cms_pageSectionsZonesResources pszr
	inner join dbo.cache_cms_recursivePageSections rps
		on rps.startSectionID = @sectionID
		and rps.sectionid = pszr.sectionid
		and rps.includePlacements = 1
	inner join dbo.cms_siteResources sr
		on pszr.siteResourceID = sr.siteResourceID
	inner join dbo.cms_siteResourceStatuses srs
		ON sr.siteResourceStatusID = srs.siteResourceStatusID
		AND srs.siteResourceStatusDesc = 'Active'
	inner join dbo.cms_siteResourceTypes srt ON sr.resourceTypeID = srt.resourceTypeID
	inner join dbo.cms_siteResourceTypeClasses srtc ON srt.resourceTypeClassID = srtc.resourceTypeClassID
	left outer join dbo.cms_content c on sr.siteResourceID = c.siteResourceID 
	left outer join dbo.cms_applicationInstances ai on sr.siteResourceID = ai.siteResourceID
	left outer join dbo.cms_applicationWidgetInstances awi on sr.siteResourceID = awi.siteResourceID
END

-- update Application related columns
if exists (select top 1 siteresourceID from @zoneAssignments where objecttype = 'A')
	update za
	set
		appInstanceID = ai.applicationInstanceID,
		appInstanceName = ai.applicationInstanceName, 
		appTypeName = at.applicationTypeName, 
		appInstanceResourceID = ai.siteResourceID,
		isSSL = 1
	from @zoneAssignments za
	inner join dbo.cms_applicationInstances ai
		on za.siteResourceID = ai.siteResourceID
		and za.objecttype = 'A'
	inner join dbo.cms_applicationTypes at ON ai.applicationTypeID = at.applicationTypeID

--update Application Widget related columns
if exists (select top 1 siteresourceID from @zoneAssignments where objecttype = 'W')
	update za
	set
		appInstanceID = ai.applicationInstanceID,
		appInstanceName = ai.applicationInstanceName, 
		appTypeName = at.applicationTypeName, 
		appInstanceResourceID = ai.siteResourceID, 
		appWidgetInstanceID = awi.applicationWidgetInstanceID , 
		appWidgetTypeName = awt.applicationWidgetTypeName, 
		appWidgetInstanceName = awi.applicationWidgetInstanceName,
		isSSL = 1
	from @zoneAssignments za
	inner join dbo.cms_applicationWidgetInstances awi
		on za.siteResourceID = awi.siteResourceID
		and za.objecttype = 'W'
	inner join dbo.cms_applicationInstances ai ON awi.applicationInstanceID = ai.applicationInstanceID
	inner join dbo.cms_applicationWidgetTypes awt ON awi.applicationWidgetTypeID = awt.applicationWidgetTypeID
	inner join dbo.cms_applicationTypes at ON ai.applicationTypeID = at.applicationTypeID



-- is page offered in requested page language? if not, use site default language
IF NOT EXISTS (select pageLanguageID from dbo.cms_pageLanguages where pageID = @pageID and languageID = @languageID)
	SELECT @languageID = defaultLanguageID from sites where siteID = @siteID
select cast(isNull((
select page.pageID, page.pageName, page.allowReturnAfterLogin, page.siteResourceID as pageSiteResourceID,
	isnull(sites.siteID,0) as siteID,
	isnull(sites.siteCode,0) as siteCode,
	isnull(organizations.orgID,0) as orgID,
	isnull(organizations.orgCode,0) as orgCode,
	ISNULL(ps.sectionID,'') as sectionID, 
	ISNULL(ps.sectionName,'') as sectionName, 
	ISNULL(ps.sectionCode,'') as sectionCode,
	@languageID as pageLanguageID,
	ISNULL(pl.pageTitle,'') as pageTitle, 
	ISNULL(pl.pageDesc,'') as pageDesc, 
	ISNULL(pl.keywords,'') as keywords, 
	ISNULL(pt.templateFileName,'DefaultTemplate') as layoutFileName,
	ISNULL(pt.templateID,'') as templateID,
	ISNULL(pt.siteResourceID,'') as templateSiteResourceID,
	ISNULL(ptt.templateTypeName,'') as templateTypeName,
	ISNULL(pm.modeName,'Normal') as layoutMode, 
	ISNULL(templateSite.sitecode,'') as layoutSiteCode,
	ISNULL(templateOrg.orgcode,'') as layoutOrgCode, 
	pageZone.zoneName, 
	siteResource.siteResourceID,
	ISNULL(sites.siteID,0) as siteID,
	ISNULL(sites.siteCode,'') as siteCode,
	ISNULL(organizations.orgID,0) as orgID,
	ISNULL(organizations.orgCode,'') as orgCode,
	ISNULL(siteResource.resourceType,'') as resourceType,
	ISNULL(siteResource.resourceTypeClassName,'') as resourceClass,
	ISNULL(siteResource.assignmentLevel,'') as assignmentLevel,
	ISNULL(siteResource.sectionID,'') as assignmentSectionID,
	ISNULL(siteResource.isSSL,'') as isSSL, 
	ISNULL(siteResource.appInstanceID,'') as appInstanceID, 
	ISNULL(siteResource.appInstanceName,'') as appInstanceName, 
	ISNULL(siteResource.appTypeName,'') as appTypeName, 
	ISNULL(siteResource.appWidgetInstanceID,'') as appWidgetInstanceID, 
	ISNULL(siteResource.appInstanceResourceID,'') as appInstanceResourceID, 
	ISNULL(siteResource.appWidgetTypeName,'') as appWidgetTypeName, 
	ISNULL(siteResource.appWidgetInstanceName,'') as appWidgetInstanceName,
	ISNULL(siteResource.contentID,'') as contentID,
	ISNULL(siteResource.enableSocialMediaSharing,'') as enableSocialMediaSharing
from dbo.cms_pages as page (nolock)
inner join dbo.sites (nolock)
	on sites.siteID = page.siteID
	and page.pageID = @pageID
inner join dbo.organizations (nolock)
	on sites.orgID = organizations.orgID
inner join dbo.cms_pageSections (nolock) as ps
	on ps.sectionID = page.sectionID
inner join dbo.cms_pageTemplates pt (nolock)
	on pt.templateid = @templateID
inner join dbo.cms_pageTemplateTypes ptt (nolock)
	on pt.templateTypeID = ptt.templateTypeID
	and ptt.templateTypeName = @templateTypeName
inner join dbo.cms_pageModes pm (nolock)
	on pm.modeID = @modeID
left outer join dbo.sites templateSite (nolock)
	inner join dbo.organizations templateOrg (nolock)
		on templateSite.orgid = templateOrg.orgid
on templateSite.siteid = pt.siteid
inner join dbo.cms_pageLanguages as pl (nolock) 
	on pl.pageID = page.pageid and pl.languageid = @languageID
inner join dbo.cms_pageTemplatesModesZones as ptmz (nolock)
	on pm.modeid = ptmz.modeid and ptmz.templateID = pt.templateID
inner join dbo.cms_pageZones as pageZone (nolock)
	on pageZone.zoneid = ptmz.zoneid
left outer join @zoneAssignments as siteResource
	on ptmz.zoneID = siteResource.zoneid
order by siteResource.zoneid, siteResource.dataLevel desc, siteResource.sortOrder
for xml auto, root('pageStructure')
),'<pageStructure />') as xml) as pageStructureXML
OPTION(RECOMPILE)


RETURN 0
GO

ALTER PROCEDURE [dbo].[cms_getCMSResourceByID]
@siteID int,
@siteResourceID int,
@languageID int,
@memberID int,
@ovrMode varchar(30)
AS

DECLARE @activeMemberID int
select @activeMemberID = dbo.fn_getActiveMemberID(@memberID)

DECLARE @templateID int, @modeID int, @ovrModeID int, @siteCode varchar(10), @mainZoneID int
DECLARE @layoutInfo TABLE (templateID int, templateTypeName varchar(100), modeID int, modeName varchar(100), templateFilename varchar(100), sitecode varchar(10), orgcode varchar(10), siteResourceID int)
DECLARE @zoneAssignments TABLE (autoid int IDENTITY(1,1), zoneID int, siteResourceID int, sortOrder int, dataLevel int)

select @siteCode = dbo.fn_getSiteCodeFromSiteID(@siteID)

declare @usetemplateID int, @usemodeID int
select TOP 1 @usetemplateID = ovtemplateID from cms_pageSections ps where siteID = @siteID and parentSectionID is null

select @usemodeID = modeID from cms_pageModes where modeName = @ovrMode
select @mainZoneID = zoneid from cms_pageZones where zoneName = 'Main'

-- if usetemplateid is null then use template 1 - platform default
IF @usetemplateID is null 
	select @usetemplateID = 1

insert into @layoutInfo (templateID, templateTypeName, modeID, modeName, templateFilename, sitecode, orgcode, siteResourceID)
select pSp.templateID, ptt.templateTypeName, pSp.modeID, pm.modeName, pt.templateFilename, s2.sitecode, o.orgcode, pt.siteResourceID
from (select @usetemplateID as templateID, @usemodeID as modeID) as pSp
inner join dbo.cms_pageTemplates as pt on pSp.templateID = pt.templateid
inner join dbo.cms_pageTemplateTypes ptt on pt.templateTypeID = ptt.templateTypeID
inner join dbo.cms_pageModes as pm on pSp.modeID = pm.modeID
left outer join dbo.sites as s2 on s2.siteid = pt.siteid
left outer join dbo.organizations as o on s2.orgid = o.orgid

-- find all zone assignments for page, not considering pageMode
insert into @zoneAssignments (zoneid, siteResourceID, sortOrder, dataLevel)
select @mainZoneID, @siteResourceID, 1 as sortOrder, 0 as dataLevel

-- use site default language
SELECT @languageID = defaultLanguageID from sites where siteCode = @sitecode

-- Now that we know template and mode, figure out page pod assignments 
select cast(isNull((
select page.pageID, page.pageName, page.allowReturnAfterLogin, page.siteResourceID as pageSiteResourceID, 
	isnull(siteResource.siteID,0) as siteID,
	isnull(siteResource.siteCode,0) as siteCode,
	isnull(siteResource.orgID,0) as orgID,
	isnull(siteResource.orgCode,0) as orgCode,
	page.sectionname, page.sectionCode, @languageID as pageLanguageID,
	coalesce(siteResourceDetails.applicationWidgetInstanceName,siteResourceDetails.applicationInstanceName,'') as pageTitle, 
	'' as pageDesc, 
	'' as keywords, 
	ISNULL(layout.templateFileName,'DefaultTemplate') as layoutFileName,
	ISNULL(layout.templateID,'') as templateID,
	ISNULL(layout.siteResourceID,'') as templateSiteResourceID,
	ISNULL(layout.templateTypeName,'') as templateTypeName,
	ISNULL(layout.modeName,'Normal') as layoutMode, 
	ISNULL(layout.sitecode,'') as layoutSiteCode, ISNULL(layout.orgcode,'') as layoutOrgCode, 
	pageZone.zoneName, 
	siteResource.siteResourceID,
	dbo.fn_checkResourceRights(siteResource.siteResourceID,dbo.fn_getResourceFunctionID('view',siteResource.resourceTypeID),@activeMemberID,@siteID) as allowed,
	ISNULL(siteResource.resourceType,'') as resourceType,
	ISNULL(siteResource.resourceTypeClassName,'') as resourceClass,
	ISNULL(siteResourceDetails.applicationInstanceID,'') as appInstanceID, 
	ISNULL(siteResourceDetails.applicationInstanceName,'') as appInstanceName, 
	ISNULL(siteResourceDetails.applicationTypeName,'') as appTypeName, 
	ISNULL(siteResourceDetails.applicationWidgetInstanceID,'') as appWidgetInstanceID, 
	ISNULL(siteResourceDetails.applicationInstanceResourceID,'') as appInstanceResourceID, 
	ISNULL(siteResourceDetails.applicationWidgetTypeName,'') as appWidgetTypeName, 
	ISNULL(siteResourceDetails.applicationWidgetInstanceName,'') as appWidgetInstanceName,
	ISNULL(siteResourceDetails.contentID,'') as contentID,
	ISNULL(siteResourceDetails.enableSocialMediaSharing,'') as enableSocialMediaSharing
from (select '' as pageID, '' as pageName, 0 as siteResourceID, 0 as allowReturnAfterLogin, '' as sectionName, '' as sectionCode, '' as sectionID) as page
inner join @layoutInfo as layout on 1=1
inner join cms_pageTemplatesModesZones as ptmz on layout.modeid = ptmz.modeid and ptmz.templateID = layout.templateID
inner join cms_pageZones as pageZone on pageZone.zoneid = ptmz.zoneid
left outer join @zoneAssignments as zr
	inner join (
		select sr.siteResourceID, srt.resourceTypeID, srt.resourceType, srtc.resourceTypeClassName, s.siteID, o.orgID, s.siteCode, o.orgCode
		from dbo.cms_siteResources sr
		inner join dbo.cms_siteResourceTypes as srt on sr.resourceTypeID = srt.resourceTypeID
		inner join dbo.cms_siteResourceTypeClasses as srtc on srt.resourceTypeClassID = srtc.resourceTypeClassID
		inner join dbo.cms_siteResourceStatuses as srStatus on sr.siteResourceStatusID = srStatus.siteResourceStatusID and srStatus.siteResourceStatusDesc = 'Active'
		inner join dbo.sites s on sr.siteID = s.siteID
		inner join dbo.organizations o on s.orgid = o.orgid
		) as siteResource on siteResource.siteResourceID = zr.siteResourceID
	inner join dbo.fn_getCMSResourcesForSite(@siteid) as siteResourceDetails 
		on siteResource.siteResourceID = siteResourceDetails.siteResourceID and siteResourceDetails.siteResourceID = @siteResourceID
	on ptmz.zoneID = zr.zoneid
order by zr.zoneid, zr.dataLevel desc, zr.sortOrder
for xml auto, root('pageStructure')
),'<pageStructure />') as xml) as pageStructureXML

RETURN 0

GO
ALTER TABLE dbo.cms_siteResourceSettingKeyTypes ADD
	keyCode varchar(50) NULL
GO
update dbo.cms_siteResourceSettingKeyTypes set
keycode = 'supportsBootstrap'
where keyName = 'Supports Bootstrap'
GO

insert into dbo.cms_siteResourceSettings (siteResourceID, settingKeyValueID)
select sr.siteResourceID, resourceKeyValue.settingKeyValueID
from cms_pageTemplates pt
inner join cms_siteResources sr
	on sr.siteResourceID = pt.siteResourceID
	and pt.isMobile = 1
inner join cms_siteResourceTypes resourceType
	on resourceType.resourceTypeID = sr.resourceTypeID
inner join dbo.cms_siteResourceSettingKeyTypes resourceKeyType
	on resourceKeyType.resourceTypeID = resourceType.resourceTypeID
inner join dbo.cms_siteResourceSettingKeyValues resourceKeyValue
	on resourceKeyType.settingKeyTypeID = resourceKeyValue.settingKeyTypeID
	and resourceKeyValue.value = 'true'
GO

ALTER PROCEDURE [dbo].[cms_getPageStructureByID] 
@siteID int,
@pageID int,
@languageID int,
@ovrMode varchar(30),
@processMobileOverrides bit,
@templateTypeName varchar(100)

AS

DECLARE @templateID int, @sectionID int, @pgResourceTypeID int, @modeID int, @ovrModeID int, @defaultTemplateID int, @defaultModeID int, @rootSectionID int, @pageIncludePlacements int
DECLARE @viewFunctionID int, @editFunctionID int
DECLARE @layoutInfo TABLE (templateID int, templateTypeName varchar(100), modeID int, modeName varchar(100), templateFilename varchar(100), sitecode varchar(10), orgcode varchar(10))
DECLARE @pageSectionPath TABLE (templateID int, modeID int, sectionID int, parentSectionID int, dataLevel int PRIMARY KEY,includePlacements bit)
DECLARE @zoneAssignments TABLE (autoid int IDENTITY(1,1), zoneID int, assignmentLevel varchar(10), sectionID int, siteResourceID int, sortOrder int, dataLevel int, isSSL bit, appInstanceID int, appInstanceName varchar(100), appTypeName varchar(100), appWidgetInstanceID int, appInstanceResourceID int, appWidgetTypeName varchar(100), appWidgetInstanceName varchar(100),contentID int,enableSocialMediaSharing bit, resourceTypeID int, resourceType varchar(100), resourceTypeClassName varchar(100), objectType char(1))

set @defaultTemplateID = 1
set @defaultModeID = 1

select @ovrModeID = modeID from cms_pageModes where modeName = @ovrMode

select 
	@pgResourceTypeID = sr.resourceTypeID, 
	@sectionID = p.sectionID, 
	@modeID = pm.modeID, 
	@templateID = isnull(mobilept.templateID,pt.templateid),
	@pageIncludePlacements = p.inheritPlacements
from cms_pages p (nolock)
inner join sites s
	on s.siteID = p.siteID
inner join cms_siteResources sr (nolock)
	on p.siteResourceID = sr.siteResourceID
	and pageID = @pageID
inner join cms_siteResourceStatuses srs
	on srs.siteResourceStatusID = sr.siteResourceStatusID
and srs.siteResourceStatusDesc = 'Active'
inner join dbo.cache_cms_derivedPageSectionSettings dpss (nolock)
	on dpss.sectionID = p.sectionID
inner join dbo.cms_pageTemplates pt (nolock)
	on pt.templateid = coalesce(p.ovTemplateID,dpss.ovTemplateID,@defaultTemplateID)
inner join dbo.cms_pageModes pm (nolock)
	on pm.modeID = coalesce(@ovrModeID,p.ovModeID,dpss.ovModeID,@defaultModeID)
left outer join dbo.cms_pageTemplates mobilept (nolock)
	on mobilept.templateid = coalesce(p.ovTemplateIDMobile,dpss.ovTemplateIDMobile)
	and mobilept.templateTypeID = pt.templateTypeID
	and @processMobileOverrides = 1


select @rootSectionID = sectionID from cms_pageSections where siteID = @siteID and parentSectionID is null

if (nullif(@ovrMode,'') is not null)
	select @ovrModeID = modeID from cms_pageModes where modeName = @ovrMode

-- find all zone assignments for page, not considering pageMode
insert into @zoneAssignments (zoneid, siteResourceID, assignmentLevel, sectionID, sortOrder, dataLevel,resourceTypeID,	resourceType, resourceTypeClassName,isSSL,contentID,enableSocialMediaSharing,objecttype)
select pzr.zoneid, pzr.siteResourceID, 'page' as assignmentLevel, 0 as sectionID, pzr.sortOrder, 0 as dataLevel, srt.resourceTypeID,srt.resourceType,srtc.resourceTypeClassName, isnull(c.isSSL,1),c.contentID, c.enableSocialMediaSharing,
objecttype = case
	when c.siteresourceID is not null then 'C'
	when ai.siteresourceID is not null then 'A'
	when awi.siteresourceID is not null then 'W'
end

from dbo.cms_pageZonesResources as pzr
inner join dbo.cms_siteResources sr
	on pzr.siteResourceID = sr.siteResourceID
	and pzr.pageID = @pageID
inner join dbo.cms_siteResourceStatuses srs
	ON sr.siteResourceStatusID = srs.siteResourceStatusID
	AND srs.siteResourceStatusDesc = 'Active'
inner join dbo.cms_siteResourceTypes srt ON sr.resourceTypeID = srt.resourceTypeID
inner join dbo.cms_siteResourceTypeClasses srtc ON srt.resourceTypeClassID = srtc.resourceTypeClassID
left outer join dbo.cms_content c on sr.siteResourceID = c.siteResourceID 
left outer join dbo.cms_applicationInstances ai on sr.siteResourceID = ai.siteResourceID
left outer join dbo.cms_applicationWidgetInstances awi on sr.siteResourceID = awi.siteResourceID


if @pageIncludePlacements = 1
BEGIN
	insert into @zoneAssignments (zoneid, siteResourceID, assignmentLevel, sectionID, sortOrder, dataLevel,resourceTypeID,	resourceType, resourceTypeClassName,isSSL,contentID,enableSocialMediaSharing,objecttype)
	select pszr.zoneid, pszr.siteResourceID, 
		assignmentLevel = case when rps.sectionID = @rootSectionID then 'site' else 'section' end,
		rps.sectionID, pszr.sortOrder, (rps.depth * -1) as dataLevel, srt.resourceTypeID,	srt.resourceType, srtc.resourceTypeClassName, isnull(c.isSSL,1),c.contentID, c.enableSocialMediaSharing,
		objecttype = case
			when c.siteresourceID is not null then 'C'
			when ai.siteresourceID is not null then 'A'
			when awi.siteresourceID is not null then 'W'
		end
			
	from dbo.cms_pageSectionsZonesResources pszr
	inner join dbo.cache_cms_recursivePageSections rps
		on rps.startSectionID = @sectionID
		and rps.sectionid = pszr.sectionid
		and rps.includePlacements = 1
	inner join dbo.cms_siteResources sr
		on pszr.siteResourceID = sr.siteResourceID
	inner join dbo.cms_siteResourceStatuses srs
		ON sr.siteResourceStatusID = srs.siteResourceStatusID
		AND srs.siteResourceStatusDesc = 'Active'
	inner join dbo.cms_siteResourceTypes srt ON sr.resourceTypeID = srt.resourceTypeID
	inner join dbo.cms_siteResourceTypeClasses srtc ON srt.resourceTypeClassID = srtc.resourceTypeClassID
	left outer join dbo.cms_content c on sr.siteResourceID = c.siteResourceID 
	left outer join dbo.cms_applicationInstances ai on sr.siteResourceID = ai.siteResourceID
	left outer join dbo.cms_applicationWidgetInstances awi on sr.siteResourceID = awi.siteResourceID
END

-- update Application related columns
if exists (select top 1 siteresourceID from @zoneAssignments where objecttype = 'A')
	update za
	set
		appInstanceID = ai.applicationInstanceID,
		appInstanceName = ai.applicationInstanceName, 
		appTypeName = at.applicationTypeName, 
		appInstanceResourceID = ai.siteResourceID,
		isSSL = 1
	from @zoneAssignments za
	inner join dbo.cms_applicationInstances ai
		on za.siteResourceID = ai.siteResourceID
		and za.objecttype = 'A'
	inner join dbo.cms_applicationTypes at ON ai.applicationTypeID = at.applicationTypeID

--update Application Widget related columns
if exists (select top 1 siteresourceID from @zoneAssignments where objecttype = 'W')
	update za
	set
		appInstanceID = ai.applicationInstanceID,
		appInstanceName = ai.applicationInstanceName, 
		appTypeName = at.applicationTypeName, 
		appInstanceResourceID = ai.siteResourceID, 
		appWidgetInstanceID = awi.applicationWidgetInstanceID , 
		appWidgetTypeName = awt.applicationWidgetTypeName, 
		appWidgetInstanceName = awi.applicationWidgetInstanceName,
		isSSL = 1
	from @zoneAssignments za
	inner join dbo.cms_applicationWidgetInstances awi
		on za.siteResourceID = awi.siteResourceID
		and za.objecttype = 'W'
	inner join dbo.cms_applicationInstances ai ON awi.applicationInstanceID = ai.applicationInstanceID
	inner join dbo.cms_applicationWidgetTypes awt ON awi.applicationWidgetTypeID = awt.applicationWidgetTypeID
	inner join dbo.cms_applicationTypes at ON ai.applicationTypeID = at.applicationTypeID



-- is page offered in requested page language? if not, use site default language
IF NOT EXISTS (select pageLanguageID from dbo.cms_pageLanguages where pageID = @pageID and languageID = @languageID)
	SELECT @languageID = defaultLanguageID from sites where siteID = @siteID
select cast(isNull((
select page.pageID, page.pageName, page.allowReturnAfterLogin, page.siteResourceID as pageSiteResourceID,
	isnull(sites.siteID,0) as siteID,
	isnull(sites.siteCode,0) as siteCode,
	isnull(organizations.orgID,0) as orgID,
	isnull(organizations.orgCode,0) as orgCode,
	ISNULL(ps.sectionID,'') as sectionID, 
	ISNULL(ps.sectionName,'') as sectionName, 
	ISNULL(ps.sectionCode,'') as sectionCode,
	@languageID as pageLanguageID,
	ISNULL(pl.pageTitle,'') as pageTitle, 
	ISNULL(pl.pageDesc,'') as pageDesc, 
	ISNULL(pl.keywords,'') as keywords, 
	ISNULL(pt.templateFileName,'DefaultTemplate') as layoutFileName,
	ISNULL(pt.templateID,'') as templateID,
	ISNULL(pt.siteResourceID,'') as templateSiteResourceID,
	ISNULL(ptt.templateTypeName,'') as templateTypeName,
	ISNULL(pm.modeName,'Normal') as layoutMode, 
	ISNULL(templateSite.sitecode,'') as layoutSiteCode,
	ISNULL(templateOrg.orgcode,'') as layoutOrgCode, 
	pageZone.zoneName, 
	siteResource.siteResourceID,
	ISNULL(sites.siteID,0) as siteID,
	ISNULL(sites.siteCode,'') as siteCode,
	ISNULL(organizations.orgID,0) as orgID,
	ISNULL(organizations.orgCode,'') as orgCode,
	ISNULL(siteResource.resourceType,'') as resourceType,
	ISNULL(siteResource.resourceTypeClassName,'') as resourceClass,
	ISNULL(siteResource.assignmentLevel,'') as assignmentLevel,
	ISNULL(siteResource.sectionID,'') as assignmentSectionID,
	ISNULL(siteResource.isSSL,'') as isSSL, 
	ISNULL(siteResource.appInstanceID,'') as appInstanceID, 
	ISNULL(siteResource.appInstanceName,'') as appInstanceName, 
	ISNULL(siteResource.appTypeName,'') as appTypeName, 
	ISNULL(siteResource.appWidgetInstanceID,'') as appWidgetInstanceID, 
	ISNULL(siteResource.appInstanceResourceID,'') as appInstanceResourceID, 
	ISNULL(siteResource.appWidgetTypeName,'') as appWidgetTypeName, 
	ISNULL(siteResource.appWidgetInstanceName,'') as appWidgetInstanceName,
	ISNULL(siteResource.contentID,'') as contentID,
	ISNULL(siteResource.enableSocialMediaSharing,'') as enableSocialMediaSharing
from dbo.cms_pages as page (nolock)
inner join dbo.sites (nolock)
	on sites.siteID = page.siteID
	and page.pageID = @pageID
inner join dbo.organizations (nolock)
	on sites.orgID = organizations.orgID
inner join dbo.cms_pageSections (nolock) as ps
	on ps.sectionID = page.sectionID
inner join dbo.cms_pageTemplates pt (nolock)
	on pt.templateid = @templateID
inner join dbo.cms_pageTemplateTypes ptt (nolock)
	on pt.templateTypeID = ptt.templateTypeID
	and ptt.templateTypeName = @templateTypeName
inner join dbo.cms_pageModes pm (nolock)
	on pm.modeID = @modeID
left outer join dbo.sites templateSite (nolock)
	inner join dbo.organizations templateOrg (nolock)
		on templateSite.orgid = templateOrg.orgid
on templateSite.siteid = pt.siteid
inner join dbo.cms_pageLanguages as pl (nolock) 
	on pl.pageID = page.pageid and pl.languageid = @languageID
inner join dbo.cms_pageTemplatesModesZones as ptmz (nolock)
	on pm.modeid = ptmz.modeid and ptmz.templateID = pt.templateID
inner join dbo.cms_pageZones as pageZone (nolock)
	on pageZone.zoneid = ptmz.zoneid
left outer join @zoneAssignments as siteResource
	on ptmz.zoneID = siteResource.zoneid
order by siteResource.zoneid, siteResource.dataLevel desc, siteResource.sortOrder
for xml auto, root('pageStructure')
),'<pageStructure />') as xml) as pageStructureXML
OPTION(RECOMPILE)


RETURN 0
GO