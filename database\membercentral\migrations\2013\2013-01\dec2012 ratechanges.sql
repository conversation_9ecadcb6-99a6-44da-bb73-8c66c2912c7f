update sub_rates set
	frontEndAllow<PERSON>hangePrice = 0
where frontEndAllow<PERSON>hang<PERSON><PERSON><PERSON> is null

update sub_rates set
	keepChangedPriceOnRenewal = 0
where keepChangedPriceOnR<PERSON>wal is null



/* To prevent any potential data loss issues, you should review this script in detail before running it outside the context of the database designer.*/
BEGIN TRANSACTION
SET QUOTED_IDENTIFIER ON
SET ARITHABORT ON
SET NUMERIC_ROUNDABORT OFF
SET CONCAT_NULL_YIELDS_NULL ON
SET ANSI_NULLS ON
SET ANSI_PADDING ON
SET ANSI_WARNINGS ON
COMMIT
BEGIN TRANSACTION
GO
ALTER TABLE dbo.sub_rates
	DROP CONSTRAINT FK_sub_rates_sub_rateSchedules
GO
COMMIT
BEGIN TRANSACTION
GO
ALTER TABLE dbo.sub_rates
	DROP CONSTRAINT FK_sub_rates_cms_siteResources
GO
COMMIT
BEGIN TRANSACTION
GO
ALTER TABLE dbo.sub_rates
	DROP CONSTRAINT FK_sub_rates_sub_advanceFormulas4
GO
ALTER TABLE dbo.sub_rates
	DROP CONSTRAINT FK_sub_rates_sub_advanceFormulas
GO
ALTER TABLE dbo.sub_rates
	DROP CONSTRAINT FK_sub_rates_sub_advanceFormulas1
GO
ALTER TABLE dbo.sub_rates
	DROP CONSTRAINT FK_sub_rates_sub_advanceFormulas2
GO
ALTER TABLE dbo.sub_rates
	DROP CONSTRAINT FK_sub_rates_sub_advanceFormulas3
GO
COMMIT
BEGIN TRANSACTION
GO
ALTER TABLE dbo.sub_rates
	DROP CONSTRAINT FK_sub_rates_tr_GLAccounts
GO
COMMIT
BEGIN TRANSACTION
GO
ALTER TABLE dbo.sub_rates
	DROP CONSTRAINT DF_sub_rates_status
GO
ALTER TABLE dbo.sub_rates
	DROP CONSTRAINT DF_sub_rates_rateStartDate
GO
ALTER TABLE dbo.sub_rates
	DROP CONSTRAINT DF_sub_rates_rateEndDate
GO
ALTER TABLE dbo.sub_rates
	DROP CONSTRAINT DF_sub_rates_rateAFStartDate
GO
ALTER TABLE dbo.sub_rates
	DROP CONSTRAINT DF_sub_rates_rateADEndDate
GO
ALTER TABLE dbo.sub_rates
	DROP CONSTRAINT DF_sub_rates_TermStartDate
GO
ALTER TABLE dbo.sub_rates
	DROP CONSTRAINT DF_sub_rates_termEndDate
GO
ALTER TABLE dbo.sub_rates
	DROP CONSTRAINT DF_sub_rates_termAFStartDate
GO
ALTER TABLE dbo.sub_rates
	DROP CONSTRAINT DF_sub_rates_termAFEndDate
GO
ALTER TABLE dbo.sub_rates
	DROP CONSTRAINT DF__sub_rates__rateN__19B0F400
GO
ALTER TABLE dbo.sub_rates
	DROP CONSTRAINT DF__sub_rates__rateA__1D8184E4
GO
ALTER TABLE dbo.sub_rates
	DROP CONSTRAINT DF__sub_rates__uid__3CC52613
GO
ALTER TABLE dbo.sub_rates
	DROP CONSTRAINT DF__sub_rates__isRen__518B38CF
GO
ALTER TABLE dbo.sub_rates
	DROP CONSTRAINT DF__sub_rates__force__527F5D08
GO
CREATE TABLE dbo.Tmp_sub_rates
	(
	rateID int NOT NULL IDENTITY (1, 1),
	scheduleID int NOT NULL,
	siteResourceID int NOT NULL,
	status char(1) NOT NULL,
	rateStartDate datetime NOT NULL,
	rateEndDate datetime NOT NULL,
	rateStartDateAFID int NULL,
	rateAFStartDate datetime NOT NULL,
	rateAFEndDate datetime NOT NULL,
	termStartDate datetime NOT NULL,
	termEndDate datetime NOT NULL,
	termStartDateAFID int NULL,
	termEndDateAFID int NULL,
	termAFStartDate datetime NOT NULL,
	termAFEndDate datetime NOT NULL,
	graceEndDate datetime NULL,
	graceAFID int NULL,
	rateName varchar(200) NOT NULL,
	rateEndDateAFID int NULL,
	rateAdvanceOnTermEnd int NOT NULL,
	uid uniqueidentifier NOT NULL,
	isRenewalRate bit NOT NULL,
	forceUpfront bit NOT NULL,
	reportCode varchar(15) NULL,
	GLAccountID int NULL,
	frontEndAllowChangePrice bit NOT NULL,
	linkedNonRenewalRateID int NULL,
	fallbackRenewalRateID int NULL,
	keepChangedPriceOnRenewal bit NOT NULL,
	frontEndChangePriceMin money NULL,
	frontEndChangePriceMax money NULL
	)  ON [PRIMARY]
GO
ALTER TABLE dbo.Tmp_sub_rates ADD CONSTRAINT
	DF_sub_rates_status DEFAULT ('A') FOR status
GO
ALTER TABLE dbo.Tmp_sub_rates ADD CONSTRAINT
	DF_sub_rates_rateStartDate DEFAULT (getdate()) FOR rateStartDate
GO
ALTER TABLE dbo.Tmp_sub_rates ADD CONSTRAINT
	DF_sub_rates_rateEndDate DEFAULT (getdate()) FOR rateEndDate
GO
ALTER TABLE dbo.Tmp_sub_rates ADD CONSTRAINT
	DF_sub_rates_rateAFStartDate DEFAULT (getdate()) FOR rateAFStartDate
GO
ALTER TABLE dbo.Tmp_sub_rates ADD CONSTRAINT
	DF_sub_rates_rateADEndDate DEFAULT (getdate()) FOR rateAFEndDate
GO
ALTER TABLE dbo.Tmp_sub_rates ADD CONSTRAINT
	DF_sub_rates_TermStartDate DEFAULT (getdate()) FOR termStartDate
GO
ALTER TABLE dbo.Tmp_sub_rates ADD CONSTRAINT
	DF_sub_rates_termEndDate DEFAULT (getdate()) FOR termEndDate
GO
ALTER TABLE dbo.Tmp_sub_rates ADD CONSTRAINT
	DF_sub_rates_termAFStartDate DEFAULT (getdate()) FOR termAFStartDate
GO
ALTER TABLE dbo.Tmp_sub_rates ADD CONSTRAINT
	DF_sub_rates_termAFEndDate DEFAULT (getdate()) FOR termAFEndDate
GO
ALTER TABLE dbo.Tmp_sub_rates ADD CONSTRAINT
	DF__sub_rates__rateN__19B0F400 DEFAULT ('') FOR rateName
GO
ALTER TABLE dbo.Tmp_sub_rates ADD CONSTRAINT
	DF__sub_rates__rateA__1D8184E4 DEFAULT ((1)) FOR rateAdvanceOnTermEnd
GO
ALTER TABLE dbo.Tmp_sub_rates ADD CONSTRAINT
	DF__sub_rates__uid__3CC52613 DEFAULT (newid()) FOR uid
GO
ALTER TABLE dbo.Tmp_sub_rates ADD CONSTRAINT
	DF__sub_rates__isRen__518B38CF DEFAULT ((0)) FOR isRenewalRate
GO
ALTER TABLE dbo.Tmp_sub_rates ADD CONSTRAINT
	DF__sub_rates__force__527F5D08 DEFAULT ((0)) FOR forceUpfront
GO
ALTER TABLE dbo.Tmp_sub_rates ADD CONSTRAINT
	DF_sub_rates_frontEndAllowChangePrice DEFAULT 0 FOR frontEndAllowChangePrice
GO
ALTER TABLE dbo.Tmp_sub_rates ADD CONSTRAINT
	DF_sub_rates_keepChangedPriceOnRenewal DEFAULT 0 FOR keepChangedPriceOnRenewal
GO
SET IDENTITY_INSERT dbo.Tmp_sub_rates ON
GO
IF EXISTS(SELECT * FROM dbo.sub_rates)
	 EXEC('INSERT INTO dbo.Tmp_sub_rates (rateID, scheduleID, siteResourceID, status, rateStartDate, rateEndDate, rateStartDateAFID, rateAFStartDate, rateAFEndDate, termStartDate, termEndDate, termStartDateAFID, termEndDateAFID, termAFStartDate, termAFEndDate, graceEndDate, graceAFID, rateName, rateEndDateAFID, rateAdvanceOnTermEnd, uid, isRenewalRate, forceUpfront, reportCode, GLAccountID, frontEndAllowChangePrice, linkedNonRenewalRateID, fallbackRenewalRateID, keepChangedPriceOnRenewal, frontEndChangePriceMin, frontEndChangePriceMax)
		SELECT rateID, scheduleID, siteResourceID, status, rateStartDate, rateEndDate, rateStartDateAFID, rateAFStartDate, rateAFEndDate, termStartDate, termEndDate, termStartDateAFID, termEndDateAFID, termAFStartDate, termAFEndDate, graceEndDate, graceAFID, rateName, rateEndDateAFID, rateAdvanceOnTermEnd, uid, isRenewalRate, forceUpfront, reportCode, GLAccountID, frontEndAllowChangePrice, linkedNonRenewalRateID, fallbackRenewalRateID, keepChangedPriceOnRenewal, frontEndChangePriceMin, frontEndChangePriceMax FROM dbo.sub_rates WITH (HOLDLOCK TABLOCKX)')
GO
SET IDENTITY_INSERT dbo.Tmp_sub_rates OFF
GO
ALTER TABLE dbo.sub_rateFrequencies
	DROP CONSTRAINT FK_sub_rateFrequencies_sub_rates
GO
ALTER TABLE dbo.sub_rates
	DROP CONSTRAINT FK_sub_rates_sub_rates
GO
ALTER TABLE dbo.sub_rates
	DROP CONSTRAINT FK_sub_rates_sub_rates1
GO
DROP TABLE dbo.sub_rates
GO
EXECUTE sp_rename N'dbo.Tmp_sub_rates', N'sub_rates', 'OBJECT' 
GO
ALTER TABLE dbo.sub_rates ADD CONSTRAINT
	PK_sub_rates PRIMARY KEY CLUSTERED 
	(
	rateID
	) WITH( PAD_INDEX = OFF, FILLFACTOR = 90, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

GO
CREATE NONCLUSTERED INDEX _dta_index_sub_rates_5_812932551__K1_2_23 ON dbo.sub_rates
	(
	rateID
	) INCLUDE (scheduleID, isRenewalRate) 
 WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
CREATE NONCLUSTERED INDEX _dta_index_sub_rates_5_812932551__K1_15 ON dbo.sub_rates
	(
	rateID
	) INCLUDE (termAFStartDate) 
 WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
CREATE NONCLUSTERED INDEX _dta_index_sub_rates_7_812932551__K2_K1_K19_K25 ON dbo.sub_rates
	(
	scheduleID,
	rateID,
	rateName,
	reportCode
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
ALTER TABLE dbo.sub_rates ADD CONSTRAINT
	FK_sub_rates_tr_GLAccounts FOREIGN KEY
	(
	GLAccountID
	) REFERENCES dbo.tr_GLAccounts
	(
	GLAccountID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.sub_rates ADD CONSTRAINT
	FK_sub_rates_sub_advanceFormulas4 FOREIGN KEY
	(
	rateEndDateAFID
	) REFERENCES dbo.sub_advanceFormulas
	(
	AFID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.sub_rates ADD CONSTRAINT
	FK_sub_rates_cms_siteResources FOREIGN KEY
	(
	siteResourceID
	) REFERENCES dbo.cms_siteResources
	(
	siteResourceID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.sub_rates ADD CONSTRAINT
	FK_sub_rates_sub_advanceFormulas FOREIGN KEY
	(
	graceAFID
	) REFERENCES dbo.sub_advanceFormulas
	(
	AFID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.sub_rates ADD CONSTRAINT
	FK_sub_rates_sub_advanceFormulas1 FOREIGN KEY
	(
	rateStartDateAFID
	) REFERENCES dbo.sub_advanceFormulas
	(
	AFID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.sub_rates ADD CONSTRAINT
	FK_sub_rates_sub_advanceFormulas2 FOREIGN KEY
	(
	termEndDateAFID
	) REFERENCES dbo.sub_advanceFormulas
	(
	AFID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.sub_rates ADD CONSTRAINT
	FK_sub_rates_sub_advanceFormulas3 FOREIGN KEY
	(
	termStartDateAFID
	) REFERENCES dbo.sub_advanceFormulas
	(
	AFID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.sub_rates ADD CONSTRAINT
	FK_sub_rates_sub_rateSchedules FOREIGN KEY
	(
	scheduleID
	) REFERENCES dbo.sub_rateSchedules
	(
	scheduleID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.sub_rates ADD CONSTRAINT
	FK_sub_rates_sub_rates FOREIGN KEY
	(
	fallbackRenewalRateID
	) REFERENCES dbo.sub_rates
	(
	rateID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.sub_rates ADD CONSTRAINT
	FK_sub_rates_sub_rates1 FOREIGN KEY
	(
	linkedNonRenewalRateID
	) REFERENCES dbo.sub_rates
	(
	rateID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
COMMIT
BEGIN TRANSACTION
GO
ALTER TABLE dbo.sub_rateFrequencies ADD CONSTRAINT
	FK_sub_rateFrequencies_sub_rates FOREIGN KEY
	(
	rateID
	) REFERENCES dbo.sub_rates
	(
	rateID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
COMMIT

GO

USE [memberCentral]
GO
/****** Object:  UserDefinedFunction [dbo].[fn_getRecursiveMemberSubscriptions]    Script Date: 12/21/2012 17:44:14 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER FUNCTION [dbo].[fn_getRecursiveMemberSubscriptions]
( @memberID int,
 @siteID int,
 @subscriberID int = NULL
)
RETURNS @subscriptionTable TABLE (
 subscriberID int NULL,
 subscriptionID int NULL,
 typeName varchar(100) NULL,
 subscriptionName varchar(300) NULL,
 status varchar(1) NULL,
 statusName varchar(50),
 paymentStatus varchar(1) NULL,
 paymentStatusName varchar(50) NULL,
 RFID int NULL,
 rateTermDateFlag varchar(1) NULL,
 GLAccountID int NULL,
 subStartDate datetime,
 subEndDate datetime,
 graceEndDate datetime,
 parentSubscriberID int,
 rootSubscriberID int,
 PCFree bit,
 modifiedRate money NULL,
 paymentOrder int,
 subActivationCode varchar(1),
 allowRateGLAccountOverride bit,
 topRow varchar(max),
 thePath varchar(max),
 thePathExpanded varchar(max),
 linkedNonRenewalRateID int,
 fallbackRenewalRateID int,
 keepChangedPriceOnRenewal bit,
 frontEndAllowChangePrice int,
 frontEndChangePriceMin money,
 frontEndChangePriceMax money
)
AS
BEGIN
IF @subscriberID is NULL
BEGIN
WITH subscribers AS (
 select subscriberID, subscriptionID, typeName, subscriptionName, [status], statusName, paymentStatus, paymentStatusName, 
	RFID, rateTermDateFlag, GLAccountID, subStartDate, subEndDate, graceEndDate, parentSubscriberID, rootSubscriberID, PCFree, modifiedRate, paymentOrder, 
	subActivationCode, allowRateGLAccountOverride,
 linkedNonRenewalRateID,
 fallbackRenewalRateID,
 keepChangedPriceOnRenewal,
 frontEndAllowChangePrice,
 frontEndChangePriceMin,
 frontEndChangePriceMax,
  CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS topRow,
  CONVERT(varchar, memberID) + '.' + CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
  CAST(subscriptionName as varchar(max)) as thePathExpanded
 FROM (
  select subscriberID, memberID, subscriptionID, typeName, subscriptionName, [status], statusName, paymentStatus, paymentStatusName, 
	RFID, rateTermDateFlag, GLAccountID, subStartDate, subEndDate, graceEndDate, parentSubscriberID, rootSubscriberID, PCFree, modifiedRate, paymentOrder, 
	subActivationCode, allowRateGLAccountOverride,linkedNonRenewalRateID, fallbackRenewalRateID, keepChangedPriceOnRenewal, frontEndAllowChangePrice, frontEndChangePriceMin, frontEndChangePriceMax,
  ROW_NUMBER() OVER (ORDER BY statusSort, typeName, subscriberID) AS theRow
  FROM (
   select s.subscriberID, m.activeMemberID as memberID, s.subscriptionID, t.typeName, sc.subscriptionName, st.statusCode as status, 
	st.statusName as statusName, pst.statusCode as paymentStatus, pst.statusName as paymentStatusName,
	s.RFID, sc.rateTermDateFlag, s.GLAccountID, s.subStartDate, s.subEndDate, s.graceEndDate, s.parentSubscriberID, s.rootSubscriberID, s.PCFree, s.modifiedRate, 
	sc.paymentOrder, o.subActivationCode, sc.allowRateGLAccountOverride,	 r.linkedNonRenewalRateID,	 r.fallbackRenewalRateID,	 r.keepChangedPriceOnRenewal,	 r.frontEndAllowChangePrice,	 r.frontEndChangePriceMin,	 r.frontEndChangePriceMax,
 	case when st.statusCode = 'A' then 1 else
  	case when st.statusCode = 'E' then 2 else
   	case when st.statusCode = 'I' then 3 else
    	case when st.statusCode = 'D' then 4 else 5 end
   	end
  	end
 	end as statusSort             	
   from dbo.sub_subscribers s
   inner join dbo.sub_statuses st on st.statusID = s.statusID
   inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID
   inner join dbo.sub_activationOptions o on o.subActivationID = s.subActivationID
   inner join dbo.sub_subscriptions sc on sc.subscriptionID = s.subscriptionID
   inner join dbo.sub_types t on t.typeID = sc.typeID and t.siteID = @siteID
   inner join dbo.ams_members m on m.memberID = s.memberID
   inner join dbo.sub_rateFrequencies rf on s.rfid = rf.rfid
   inner join dbo.sub_rates r on r.rateID = rf.rateID
   where s.memberID in (select mAll.memberID
      from ams_members m
      inner join ams_members mAll on mAll.activeMemberID = m.activeMemberID
      where m.memberID = @memberID)
   and parentSubscriberID is null
  ) as x1
 ) as x
 UNION ALL
 select subscriberID, subscriptionID, typeName, subscriptionName, [status], statusName, paymentStatus, paymentStatusName, 
	RFID, rateTermDateFlag, GLAccountID, subStartDate, subEndDate, graceEndDate, parentSubscriberID, rootSubscriberID, PCFree, modifiedRate,
  paymentOrder, subActivationCode, allowRateGLAccountOverride,
 linkedNonRenewalRateID,
 fallbackRenewalRateID,
 keepChangedPriceOnRenewal,
 frontEndAllowChangePrice,
 frontEndChangePriceMin,
 frontEndChangePriceMax,
topRow,
  thePath + '.' + CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
  thePathExpanded = case
   	when isnull(parentSubscriberID,0) = 0 then subscriptionName
   	else thePathExpanded + ' \ ' + subscriptionName
  	end
 FROM (
 select s.subscriberID, m.activeMemberID as memberID, s.subscriptionID, t.typeName, sc.subscriptionName, st.statusCode as status, st.statusName as statusName, pst.statusCode as paymentStatus, pst.statusName as paymentStatusName, s.RFID, sc.rateTermDateFlag, s.GLAccountID, s.subStartDate, s.subEndDate, s.graceEndDate, s.parentSubscriberID, s.rootSubscriberID, s.PCFree, s.modifiedRate,
  sc.paymentOrder, o.subActivationCode, sc.allowRateGLAccountOverride, scte.topRow, scte.thePath, scte.thePathExpanded,
	 r.linkedNonRenewalRateID,
	 r.fallbackRenewalRateID,
	 r.keepChangedPriceOnRenewal,
	 r.frontEndAllowChangePrice,
	 r.frontEndChangePriceMin,
	 r.frontEndChangePriceMax,
  ROW_NUMBER() OVER (ORDER BY t.typeName, s.subscriberID) AS theRow
 from dbo.sub_subscribers s
 inner join dbo.sub_statuses st on st.statusID = s.statusID
 inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID
 inner join dbo.sub_activationOptions o on o.subActivationID = s.subActivationID
 inner join dbo.sub_subscriptions sc on sc.subscriptionID = s.subscriptionID
 inner join dbo.sub_types t on t.typeID = sc.typeID and t.siteID = @siteID
 INNER JOIN subscribers scte on s.parentSubscriberID = scte.subscriberID
 INNER JOIN dbo.ams_members m on m.memberID = s.memberID
 inner join dbo.sub_rateFrequencies rf on s.rfid = rf.rfid
 inner join dbo.sub_rates r on r.rateID = rf.rateID
 where s.memberID in (select mAll.memberID
      from ams_members m
      inner join ams_members mAll on mAll.activeMemberID = m.activeMemberID
      where m.memberID = @memberID)
 ) as y
)
INSERT INTO @subscriptionTable(subscriberID, subscriptionID, typeName, subscriptionName, [status], statusName, paymentStatus, paymentStatusName, RFID, rateTermDateFlag, GLAccountID,
    	subStartDate, subEndDate, graceEndDate, parentSubscriberID, rootSubscriberID, PCFree, modifiedRate, paymentOrder, subActivationCode, allowRateGLAccountOverride, topRow, thePath, thePathExpanded, linkedNonRenewalRateID,fallbackRenewalRateID, keepChangedPriceOnRenewal, frontEndAllowChangePrice, frontEndChangePriceMin, frontEndChangePriceMax)
SELECT subscriberID, subscriptionID, typeName, subscriptionName, [status], statusName, paymentStatus, paymentStatusName, RFID, rateTermDateFlag, GLAccountID,
  subStartDate, subEndDate, graceEndDate, parentSubscriberID, rootSubscriberID, PCFree, modifiedRate, paymentOrder, subActivationCode, allowRateGLAccountOverride, topRow, thePath, thePathExpanded,  linkedNonRenewalRateID, fallbackRenewalRateID, keepChangedPriceOnRenewal, frontEndAllowChangePrice, frontEndChangePriceMin, frontEndChangePriceMax
from subscribers
END
ELSE
BEGIN
WITH subscribers AS (
 select subscriberID, subscriptionID, typeName, subscriptionName, [status], statusName, paymentStatus, paymentStatusName, RFID, rateTermDateFlag,
	GLAccountID, subStartDate, subEndDate, graceEndDate, parentSubscriberID, rootSubscriberID, PCFree, modifiedRate, paymentOrder, subActivationCode, 
	allowRateGLAccountOverride, linkedNonRenewalRateID,fallbackRenewalRateID, keepChangedPriceOnRenewal, frontEndAllowChangePrice, frontEndChangePriceMin, frontEndChangePriceMax,
  CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS topRow,
  CONVERT(varchar, memberID) + '.' + CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
  CAST(subscriptionName as varchar(max)) as thePathExpanded
 FROM (
 select s.subscriberID, m.activeMemberID as memberID, s.subscriptionID, t.typeName, sc.subscriptionName, st.statusCode as status, 
	st.statusName as statusName, pst.statusCode as paymentStatus, pst.statusName as paymentStatusName, s.RFID, sc.rateTermDateFlag, s.GLAccountID, 
	s.subStartDate, s.subEndDate, s.graceEndDate, s.parentSubscriberID, s.rootSubscriberID, s.PCFree, s.modifiedRate, sc.paymentOrder, o.subActivationCode,
	sc.allowRateGLAccountOverride,
	 r.linkedNonRenewalRateID,
	 r.fallbackRenewalRateID,
	 r.keepChangedPriceOnRenewal,
	 r.frontEndAllowChangePrice,
	 r.frontEndChangePriceMin,
	 r.frontEndChangePriceMax,
  ROW_NUMBER() OVER (ORDER BY t.typeName, s.subscriberID) AS theRow
 from dbo.sub_subscribers s
 inner join dbo.sub_statuses st on st.statusID = s.statusID
 inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID
 inner join dbo.sub_activationOptions o on o.subActivationID = s.subActivationID
 inner join dbo.sub_subscriptions sc on sc.subscriptionID = s.subscriptionID
 inner join dbo.sub_types t on t.typeID = sc.typeID and t.siteID = @siteID
 inner join dbo.ams_members m on m.memberID = s.memberID
 inner join dbo.sub_rateFrequencies rf on s.rfid = rf.rfid
 inner join dbo.sub_rates r on r.rateID = rf.rateID
 where s.memberID in (select mAll.memberID
      from ams_members m
      inner join ams_members mAll on mAll.activeMemberID = m.activeMemberID
      where m.memberID = @memberID)
 and subscriberID = @subscriberID
 --and parentSubscriberID is null
 ) as x
 UNION ALL
 select subscriberID, subscriptionID, typeName, subscriptionName, [status], statusName, paymentStatus, paymentStatusName, RFID, rateTermDateFlag,
	GLAccountID, subStartDate, subEndDate, graceEndDate, parentSubscriberID, rootSubscriberID, PCFree, modifiedRate, paymentOrder, subActivationCode, 
	allowRateGLAccountOverride, linkedNonRenewalRateID,fallbackRenewalRateID, keepChangedPriceOnRenewal, frontEndAllowChangePrice, frontEndChangePriceMin, frontEndChangePriceMax, topRow,
  thePath + '.' + CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
  thePathExpanded = case
   	when isnull(parentSubscriberID,0) = 0 then subscriptionName
   	else thePathExpanded + ' \ ' + subscriptionName
  	end
 FROM (
 select s.subscriberID, m.activeMemberID as memberID, s.subscriptionID, t.typeName, sc.subscriptionName, st.statusCode as status, 
	st.statusName as statusName, pst.statusCode as paymentStatus, pst.statusName as paymentStatusName, s.RFID, sc.rateTermDateFlag, s.GLAccountID, 
	s.subStartDate, s.subEndDate, s.graceEndDate, s.parentSubscriberID, s.rootSubscriberID, s.PCFree, s.modifiedRate,
	sc.paymentOrder, o.subActivationCode, sc.allowRateGLAccountOverride,
	 r.linkedNonRenewalRateID,
	 r.fallbackRenewalRateID,
	 r.keepChangedPriceOnRenewal,
	 r.frontEndAllowChangePrice,
	 r.frontEndChangePriceMin,
	 r.frontEndChangePriceMax,
scte.topRow, scte.thePath, scte.thePathExpanded,
  ROW_NUMBER() OVER (ORDER BY t.typeName, s.subscriberID) AS theRow
 from dbo.sub_subscribers s
 inner join dbo.sub_statuses st on st.statusID = s.statusID
 inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID
 inner join dbo.sub_activationOptions o on o.subActivationID = s.subActivationID
 inner join dbo.sub_subscriptions sc on sc.subscriptionID = s.subscriptionID
 inner join dbo.sub_types t on t.typeID = sc.typeID and t.siteID = @siteID
 INNER JOIN subscribers scte on s.parentSubscriberID = scte.subscriberID
 INNER JOIN dbo.ams_members m on m.memberID = s.memberID
 inner join dbo.sub_rateFrequencies rf on s.rfid = rf.rfid
 inner join dbo.sub_rates r on r.rateID = rf.rateID
 where s.memberID in (select mAll.memberID
      from ams_members m
      inner join ams_members mAll on mAll.activeMemberID = m.activeMemberID
      where m.memberID = @memberID)
 ) as y
)
INSERT INTO @subscriptionTable(subscriberID, subscriptionID, typeName, subscriptionName, [status], statusName, paymentStatus, paymentStatusName, RFID, rateTermDateFlag, GLAccountID,
    	subStartDate, subEndDate, graceEndDate, parentSubscriberID, rootSubscriberID, PCFree, modifiedRate, paymentOrder, subActivationCode, allowRateGLAccountOverride, topRow, thePath, thePathExpanded, linkedNonRenewalRateID,fallbackRenewalRateID, keepChangedPriceOnRenewal, frontEndAllowChangePrice, frontEndChangePriceMin, frontEndChangePriceMax)
SELECT subscriberID, subscriptionID, typeName, subscriptionName, [status], statusName, paymentStatus, paymentStatusName, RFID, rateTermDateFlag, GLAccountID,
  subStartDate, subEndDate, graceEndDate, parentSubscriberID, rootSubscriberID, PCFree, modifiedRate, paymentOrder, subActivationCode, allowRateGLAccountOverride, topRow, thePath, thePathExpanded, linkedNonRenewalRateID,fallbackRenewalRateID, keepChangedPriceOnRenewal, frontEndAllowChangePrice, frontEndChangePriceMin, frontEndChangePriceMax
from subscribers
END
 RETURN
END

GO


USE [memberCentral]
GO
/****** Object:  UserDefinedFunction [dbo].[fn_getRecursiveMemberSubscriptions]    Script Date: 12/21/2012 17:44:14 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER FUNCTION [dbo].[fn_getRecursiveMemberSubscriptions]
( @memberID int,
 @siteID int,
 @subscriberID int = NULL
)
RETURNS @subscriptionTable TABLE (
 subscriberID int NULL,
 subscriptionID int NULL,
 typeName varchar(100) NULL,
 subscriptionName varchar(300) NULL,
 status varchar(1) NULL,
 statusName varchar(50),
 paymentStatus varchar(1) NULL,
 paymentStatusName varchar(50) NULL,
 RFID int NULL,
 rateTermDateFlag varchar(1) NULL,
 GLAccountID int NULL,
 subStartDate datetime,
 subEndDate datetime,
 graceEndDate datetime,
 parentSubscriberID int,
 rootSubscriberID int,
 PCFree bit,
 modifiedRate money NULL,
 paymentOrder int,
 subActivationCode varchar(1),
 allowRateGLAccountOverride bit,
 topRow varchar(max),
 thePath varchar(max),
 thePathExpanded varchar(max),
 lastPrice money,
 linkedNonRenewalRateID int,
 fallbackRenewalRateID int,
 keepChangedPriceOnRenewal bit,
 frontEndAllowChangePrice int,
 frontEndChangePriceMin money,
 frontEndChangePriceMax money
)
AS
BEGIN
IF @subscriberID is NULL
BEGIN
WITH subscribers AS (
 select subscriberID, subscriptionID, typeName, subscriptionName, [status], statusName, paymentStatus, paymentStatusName, 
	RFID, rateTermDateFlag, GLAccountID, subStartDate, subEndDate, graceEndDate, parentSubscriberID, rootSubscriberID, PCFree, modifiedRate, paymentOrder, 
	subActivationCode, allowRateGLAccountOverride,
 linkedNonRenewalRateID,
 fallbackRenewalRateID,
 keepChangedPriceOnRenewal,
 frontEndAllowChangePrice,
 frontEndChangePriceMin,
 frontEndChangePriceMax,
  CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS topRow,
  CONVERT(varchar, memberID) + '.' + CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
  CAST(subscriptionName as varchar(max)) as thePathExpanded
 FROM (
  select subscriberID, memberID, subscriptionID, typeName, subscriptionName, [status], statusName, paymentStatus, paymentStatusName, 
	RFID, rateTermDateFlag, GLAccountID, subStartDate, subEndDate, graceEndDate, parentSubscriberID, rootSubscriberID, PCFree, modifiedRate, paymentOrder, 
	subActivationCode, allowRateGLAccountOverride,linkedNonRenewalRateID, fallbackRenewalRateID, keepChangedPriceOnRenewal, frontEndAllowChangePrice, frontEndChangePriceMin, frontEndChangePriceMax,
  ROW_NUMBER() OVER (ORDER BY statusSort, typeName, subscriberID) AS theRow
  FROM (
   select s.subscriberID, m.activeMemberID as memberID, s.subscriptionID, t.typeName, sc.subscriptionName, st.statusCode as status, 
	st.statusName as statusName, pst.statusCode as paymentStatus, pst.statusName as paymentStatusName,
	s.RFID, sc.rateTermDateFlag, s.GLAccountID, s.subStartDate, s.subEndDate, s.graceEndDate, s.parentSubscriberID, s.rootSubscriberID, s.PCFree, s.modifiedRate, 
	sc.paymentOrder, o.subActivationCode, sc.allowRateGLAccountOverride,	 r.linkedNonRenewalRateID,	 r.fallbackRenewalRateID,	 r.keepChangedPriceOnRenewal,	 r.frontEndAllowChangePrice,	 r.frontEndChangePriceMin,	 r.frontEndChangePriceMax,
 	case when st.statusCode = 'A' then 1 else
  	case when st.statusCode = 'E' then 2 else
   	case when st.statusCode = 'I' then 3 else
    	case when st.statusCode = 'D' then 4 else 5 end
   	end
  	end
 	end as statusSort             	
   from dbo.sub_subscribers s
   inner join dbo.sub_statuses st on st.statusID = s.statusID
   inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID
   inner join dbo.sub_activationOptions o on o.subActivationID = s.subActivationID
   inner join dbo.sub_subscriptions sc on sc.subscriptionID = s.subscriptionID
   inner join dbo.sub_types t on t.typeID = sc.typeID and t.siteID = @siteID
   inner join dbo.ams_members m on m.memberID = s.memberID
   inner join dbo.sub_rateFrequencies rf on s.rfid = rf.rfid
   inner join dbo.sub_rates r on r.rateID = rf.rateID
   where s.memberID in (select mAll.memberID
      from ams_members m
      inner join ams_members mAll on mAll.activeMemberID = m.activeMemberID
      where m.memberID = @memberID)
   and parentSubscriberID is null
  ) as x1
 ) as x
 UNION ALL
 select subscriberID, subscriptionID, typeName, subscriptionName, [status], statusName, paymentStatus, paymentStatusName, 
	RFID, rateTermDateFlag, GLAccountID, subStartDate, subEndDate, graceEndDate, parentSubscriberID, rootSubscriberID, PCFree, modifiedRate,
  paymentOrder, subActivationCode, allowRateGLAccountOverride,
 linkedNonRenewalRateID,
 fallbackRenewalRateID,
 keepChangedPriceOnRenewal,
 frontEndAllowChangePrice,
 frontEndChangePriceMin,
 frontEndChangePriceMax,
topRow,
  thePath + '.' + CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
  thePathExpanded = case
   	when isnull(parentSubscriberID,0) = 0 then subscriptionName
   	else thePathExpanded + ' \ ' + subscriptionName
  	end
 FROM (
 select s.subscriberID, m.activeMemberID as memberID, s.subscriptionID, t.typeName, sc.subscriptionName, st.statusCode as status, st.statusName as statusName, pst.statusCode as paymentStatus, pst.statusName as paymentStatusName, s.RFID, sc.rateTermDateFlag, s.GLAccountID, s.subStartDate, s.subEndDate, s.graceEndDate, s.parentSubscriberID, s.rootSubscriberID, s.PCFree, s.modifiedRate,
  sc.paymentOrder, o.subActivationCode, sc.allowRateGLAccountOverride, scte.topRow, scte.thePath, scte.thePathExpanded,
	 r.linkedNonRenewalRateID,
	 r.fallbackRenewalRateID,
	 r.keepChangedPriceOnRenewal,
	 r.frontEndAllowChangePrice,
	 r.frontEndChangePriceMin,
	 r.frontEndChangePriceMax,
  ROW_NUMBER() OVER (ORDER BY t.typeName, s.subscriberID) AS theRow
 from dbo.sub_subscribers s
 inner join dbo.sub_statuses st on st.statusID = s.statusID
 inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID
 inner join dbo.sub_activationOptions o on o.subActivationID = s.subActivationID
 inner join dbo.sub_subscriptions sc on sc.subscriptionID = s.subscriptionID
 inner join dbo.sub_types t on t.typeID = sc.typeID and t.siteID = @siteID
 INNER JOIN subscribers scte on s.parentSubscriberID = scte.subscriberID
 INNER JOIN dbo.ams_members m on m.memberID = s.memberID
 inner join dbo.sub_rateFrequencies rf on s.rfid = rf.rfid
 inner join dbo.sub_rates r on r.rateID = rf.rateID
 where s.memberID in (select mAll.memberID
      from ams_members m
      inner join ams_members mAll on mAll.activeMemberID = m.activeMemberID
      where m.memberID = @memberID)
 ) as y
)
INSERT INTO @subscriptionTable(subscriberID, subscriptionID, typeName, subscriptionName, [status], statusName, paymentStatus, paymentStatusName, RFID, rateTermDateFlag, GLAccountID,
    	subStartDate, subEndDate, graceEndDate, parentSubscriberID, rootSubscriberID, PCFree, modifiedRate, paymentOrder, subActivationCode, allowRateGLAccountOverride, topRow, thePath, thePathExpanded, linkedNonRenewalRateID,fallbackRenewalRateID, keepChangedPriceOnRenewal, frontEndAllowChangePrice, frontEndChangePriceMin, frontEndChangePriceMax)
SELECT subscriberID, subscriptionID, typeName, subscriptionName, [status], statusName, paymentStatus, paymentStatusName, RFID, rateTermDateFlag, GLAccountID,
  subStartDate, subEndDate, graceEndDate, parentSubscriberID, rootSubscriberID, PCFree, modifiedRate, paymentOrder, subActivationCode, allowRateGLAccountOverride, topRow, thePath, thePathExpanded,  linkedNonRenewalRateID, fallbackRenewalRateID, keepChangedPriceOnRenewal, frontEndAllowChangePrice, frontEndChangePriceMin, frontEndChangePriceMax
from subscribers
END
ELSE
BEGIN
WITH subscribers AS (
 select subscriberID, subscriptionID, typeName, subscriptionName, [status], statusName, paymentStatus, paymentStatusName, RFID, rateTermDateFlag,
	GLAccountID, subStartDate, subEndDate, graceEndDate, parentSubscriberID, rootSubscriberID, PCFree, modifiedRate, paymentOrder, subActivationCode, 
	allowRateGLAccountOverride, lastPrice, linkedNonRenewalRateID,fallbackRenewalRateID, keepChangedPriceOnRenewal, frontEndAllowChangePrice, frontEndChangePriceMin, frontEndChangePriceMax,
  CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS topRow,
  CONVERT(varchar, memberID) + '.' + CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
  CAST(subscriptionName as varchar(max)) as thePathExpanded
 FROM (
 select s.subscriberID, m.activeMemberID as memberID, s.subscriptionID, t.typeName, sc.subscriptionName, st.statusCode as status, 
	st.statusName as statusName, pst.statusCode as paymentStatus, pst.statusName as paymentStatusName, s.RFID, sc.rateTermDateFlag, s.GLAccountID, 
	s.subStartDate, s.subEndDate, s.graceEndDate, s.parentSubscriberID, s.rootSubscriberID, s.PCFree, s.modifiedRate, sc.paymentOrder, o.subActivationCode,
	sc.allowRateGLAccountOverride, s.lastPrice,
	 r.linkedNonRenewalRateID,
	 r.fallbackRenewalRateID,
	 r.keepChangedPriceOnRenewal,
	 r.frontEndAllowChangePrice,
	 r.frontEndChangePriceMin,
	 r.frontEndChangePriceMax,
  ROW_NUMBER() OVER (ORDER BY t.typeName, s.subscriberID) AS theRow
 from dbo.sub_subscribers s
 inner join dbo.sub_statuses st on st.statusID = s.statusID
 inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID
 inner join dbo.sub_activationOptions o on o.subActivationID = s.subActivationID
 inner join dbo.sub_subscriptions sc on sc.subscriptionID = s.subscriptionID
 inner join dbo.sub_types t on t.typeID = sc.typeID and t.siteID = @siteID
 inner join dbo.ams_members m on m.memberID = s.memberID
 inner join dbo.sub_rateFrequencies rf on s.rfid = rf.rfid
 inner join dbo.sub_rates r on r.rateID = rf.rateID
 where s.memberID in (select mAll.memberID
      from ams_members m
      inner join ams_members mAll on mAll.activeMemberID = m.activeMemberID
      where m.memberID = @memberID)
 and subscriberID = @subscriberID
 --and parentSubscriberID is null
 ) as x
 UNION ALL
 select subscriberID, subscriptionID, typeName, subscriptionName, [status], statusName, paymentStatus, paymentStatusName, RFID, rateTermDateFlag,
	GLAccountID, subStartDate, subEndDate, graceEndDate, parentSubscriberID, rootSubscriberID, PCFree, modifiedRate, paymentOrder, subActivationCode, 
	allowRateGLAccountOverride, lastPrice, linkedNonRenewalRateID,fallbackRenewalRateID, keepChangedPriceOnRenewal, frontEndAllowChangePrice, frontEndChangePriceMin, frontEndChangePriceMax, topRow,
  thePath + '.' + CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
  thePathExpanded = case
   	when isnull(parentSubscriberID,0) = 0 then subscriptionName
   	else thePathExpanded + ' \ ' + subscriptionName
  	end
 FROM (
 select s.subscriberID, m.activeMemberID as memberID, s.subscriptionID, t.typeName, sc.subscriptionName, st.statusCode as status, 
	st.statusName as statusName, pst.statusCode as paymentStatus, pst.statusName as paymentStatusName, s.RFID, sc.rateTermDateFlag, s.GLAccountID, 
	s.subStartDate, s.subEndDate, s.graceEndDate, s.parentSubscriberID, s.rootSubscriberID, s.PCFree, s.modifiedRate,
	sc.paymentOrder, o.subActivationCode, sc.allowRateGLAccountOverride, s.lastPrice,
	 r.linkedNonRenewalRateID,
	 r.fallbackRenewalRateID,
	 r.keepChangedPriceOnRenewal,
	 r.frontEndAllowChangePrice,
	 r.frontEndChangePriceMin,
	 r.frontEndChangePriceMax,
scte.topRow, scte.thePath, scte.thePathExpanded,
  ROW_NUMBER() OVER (ORDER BY t.typeName, s.subscriberID) AS theRow
 from dbo.sub_subscribers s
 inner join dbo.sub_statuses st on st.statusID = s.statusID
 inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID
 inner join dbo.sub_activationOptions o on o.subActivationID = s.subActivationID
 inner join dbo.sub_subscriptions sc on sc.subscriptionID = s.subscriptionID
 inner join dbo.sub_types t on t.typeID = sc.typeID and t.siteID = @siteID
 INNER JOIN subscribers scte on s.parentSubscriberID = scte.subscriberID
 INNER JOIN dbo.ams_members m on m.memberID = s.memberID
 inner join dbo.sub_rateFrequencies rf on s.rfid = rf.rfid
 inner join dbo.sub_rates r on r.rateID = rf.rateID
 where s.memberID in (select mAll.memberID
      from ams_members m
      inner join ams_members mAll on mAll.activeMemberID = m.activeMemberID
      where m.memberID = @memberID)
 ) as y
)
INSERT INTO @subscriptionTable(subscriberID, subscriptionID, typeName, subscriptionName, [status], statusName, paymentStatus, paymentStatusName, RFID, rateTermDateFlag, GLAccountID,
    	subStartDate, subEndDate, graceEndDate, parentSubscriberID, rootSubscriberID, PCFree, modifiedRate, paymentOrder, subActivationCode, allowRateGLAccountOverride, topRow, thePath, thePathExpanded, lastprice, linkedNonRenewalRateID,fallbackRenewalRateID, keepChangedPriceOnRenewal, frontEndAllowChangePrice, frontEndChangePriceMin, frontEndChangePriceMax)
SELECT subscriberID, subscriptionID, typeName, subscriptionName, [status], statusName, paymentStatus, paymentStatusName, RFID, rateTermDateFlag, GLAccountID,
  subStartDate, subEndDate, graceEndDate, parentSubscriberID, rootSubscriberID, PCFree, modifiedRate, paymentOrder, subActivationCode, allowRateGLAccountOverride, topRow, thePath, thePathExpanded, lastPrice,linkedNonRenewalRateID,fallbackRenewalRateID, keepChangedPriceOnRenewal, frontEndAllowChangePrice, frontEndChangePriceMin, frontEndChangePriceMax
from subscribers
END
 RETURN
END

GO

USE [memberCentral]
GO
/****** Object:  UserDefinedFunction [dbo].[fn_sub_getBestRenewalRateInfo]    Script Date: 01/02/2013 18:11:03 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
ALTER FUNCTION [dbo].[fn_sub_getBestRenewalRateInfo]
(
	-- Add the parameters for the function here
	@memberID int, @subscriptionID int, @currentSubscriberID int
)
RETURNS 
@BestRenewalRateInfo TABLE 
(

rfid int,
rateAmt money,
numInstallments int,
frequencyName varchar(50), 
frequency int, 
frequencyID int, 
rateName varchar(200),
keepChangePrice bit,
modifiedRate money
)
AS
BEGIN
	DECLARE @FID int
	select @FID = dbo.fn_getResourceFunctionID('qualify',dbo.fn_getResourceTypeID('SubscriptionRate'))

	insert into @BestRenewalRateInfo (rfid,rateAmt,numInstallments,frequencyName, frequency, frequencyID, rateName,keepChangePrice,modifiedRate)

	select top 1 rf.rfid, rf.rateAmt, rf.numInstallments, isnull(f.frequencyName,'') as frequencyName, isnull(f.frequency,0) as frequency, isnull(f.frequencyID,0) as frequencyID, isnull(r.rateName,'') as rateName,
		keepChangePrice = case when r2.keepChangedPriceOnRenewal = 1 and (r.linkedNonRenewalRateID = r2.rateID or r.rateID = r2.rateID) then cast (1 as bit) else cast (0 as bit) end,
		modifiedRate = case when r2.keepChangedPriceOnRenewal = 1 and (r.linkedNonRenewalRateID = r2.rateID or r.rateID = r2.rateID) then ss.modifiedRate else null end
	from sub_subscriptions subs
	inner join dbo.sub_rateSchedules as rs
		on rs.scheduleID = subs.scheduleID
		and rs.status = 'A'
		and subs.subscriptionID = @subscriptionID
	inner join dbo.sub_rates as r on r.scheduleID = rs.scheduleID and r.status = 'A' and r.isRenewalRate = 1
		and getdate() between r.rateAFStartDate and dateadd(day, datediff(day, 0, r.rateAFEndDate)+1, 0)
	inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID
	inner join dbo.sub_frequencies f on rf.frequencyID = f.frequencyID
	INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints srfrp ON srfrp.siteResourceID = r.siteResourceID
		AND srfrp.functionID = @FID
	INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp on srfrp.rightPrintID = gprp.rightPrintID
	inner join ams_members m
		on m.groupPrintID = gprp.groupPrintID
		and m.memberID = @memberID
	left outer join sub_subscribers ss
		inner join dbo.sub_rateFrequencies rf2 on rf2.rfid = ss.rfid
		inner join dbo.sub_rates r2 on r2.rateID = rf2.rateID
		left outer join dbo.sub_rates r3 on r2.linkedNonRenewalRateID = r3.rateID
	on ss.subscriberID = @currentSubscriberID
	order by 
		--	sameRateMatch
		case when r.rateID = r2.rateID then 1 else 0 end desc,
		--linkedRateMatch
		case when r.linkedNonRenewalRateID = r2.rateID then 1 else 0 end desc,
		-- fallbackRateMatch
		case when r.rateID = r2.fallbackRenewalRateID then 1 else 0 end desc,
		-- fallbackRateMatch of linked rate
		case when r.rateID = r3.fallbackRenewalRateID then 1 else 0 end desc,
		--sameFrequencyMatch
		case when rf.frequencyID = rf2.frequencyID then 1 else 0 end desc,
		--fullRateMatch
		case when f.frequencyName = 'Full' then 1 else 0 end desc

	
	RETURN 
END
