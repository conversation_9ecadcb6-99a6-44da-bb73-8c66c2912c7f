USE [platformQueue]
GO
CREATE TABLE [dbo].[tblQueueItems_preProcessMemberGroups](
	[itemid] [bigint] IDENTITY(1,1),
	[itemGroupUID] [uniqueidentifier] NOT NULL,
	[orgID] [int] NOT NULL,
	[memberID] [int] NULL,
	[conditionID] [int] NULL,
 CONSTRAINT [PK_tblQueueItems_preProcessMemberGroups] PRIMARY KEY CLUSTERED 
(
	[itemID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE PROC [dbo].[queue_preProcessMemberGroups_process]
@itemGroupUID uniqueidentifier

AS

IF @itemGroupUID is null
	RETURN -1

BEGIN TRY

	declare @statusReady int
	select @statusReady = qs.queueStatusID 
		from dbo.tblQueueStatuses as qs
		inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'popCondCache'
		and qs.queueStatus = 'readyToProcess'

	-- queue items
	insert into dbo.tblQueueItems_processMemberGroups (itemUID, itemGroupUID, orgID, memberID, conditionID)
		OUTPUT inserted.itemUID, inserted.dateAdded, @statusReady 
		INTO dbo.tblQueueItems(itemUID, dateAdded, queueStatusID)
	select NEWID(), itemGroupUID, orgID, memberID, conditionID
	from dbo.tblQueueItems_preProcessMemberGroups
	where itemGroupUID = @itemGroupUID

	-- send message to SB
	declare @msgXML xml
	set @msgXML = '<itemGroupUID>' + cast(@itemGroupUID as varchar(60)) + '</itemGroupUID>'
	EXEC dbo.sb_ProcessMemberGroupsSendMessage @msgXML

	-- cleanup
	delete from dbo.tblQueueItems_preProcessMemberGroups
	where itemGroupUID = @itemGroupUID

END TRY
BEGIN CATCH
	EXEC membercentral.dbo.up_errorhandler
END CATCH

RETURN 0
GO


-- create the message types for conversation
CREATE MESSAGE TYPE [PlatformQueue/PreProcessMemberGroupsRequestMessage] VALIDATION = WELL_FORMED_XML;
CREATE MESSAGE TYPE [PlatformQueue/PreProcessMemberGroupsReplyMessage] VALIDATION = WELL_FORMED_XML;
GO

-- create the contract for conversation
CREATE CONTRACT [PlatformQueue/PreProcessMemberGroupsContract] (
	[PlatformQueue/PreProcessMemberGroupsRequestMessage] SENT BY INITIATOR,
	[PlatformQueue/PreProcessMemberGroupsReplyMessage] SENT BY TARGET
	);
GO

CREATE PROC dbo.sb_PreProcessMemberGroupsQueueActivated
AS

DECLARE @conversation_handle uniqueidentifier, 
	@message_type_name nvarchar(256), 
	@message_body varbinary(max), 
	@error_message nvarchar(3000),
	@MsgXML xml,
	@itemGroupUID uniqueidentifier;

WHILE 1 = 1
BEGIN

	WAITFOR (
		RECEIVE TOP(1) 
			@conversation_handle = conversation_handle,
			@message_type_name = message_type_name,
			@message_body = message_body
		FROM dbo.PreProcessMemberGroupsQueue
		), TIMEOUT 1000;

	IF @@ROWCOUNT = 0
		BREAK;

	IF @message_type_name = N'PlatformQueue/PreProcessMemberGroupsRequestMessage'
	BEGIN
		BEGIN TRY
			SET @MsgXML = cast(@message_body as xml)
			SELECT @itemGroupUID = @MsgXML.value('(/itemGroupUID)[1]','uniqueidentifier')
			IF @itemGroupUID is not null
				EXEC platformQueue.dbo.queue_PreProcessMemberGroups_process @itemGroupUID=@itemGroupUID

			END CONVERSATION @conversation_handle;
		END TRY
		BEGIN CATCH
			SET @error_message = ERROR_MESSAGE();
			INSERT INTO dbo.sb_ServiceBrokerErrorLog VALUES(getdate(), @error_message, @message_body)
			END CONVERSATION @conversation_handle WITH ERROR = 1 DESCRIPTION = @error_message;
		END CATCH;
	END
	ELSE
		IF @message_type_name = N'http://schemas.microsoft.com/SQL/ServiceBroker/EndDialog'
			END CONVERSATION @conversation_handle;
		ELSE
		BEGIN
			--anything other than user type or EndDialog is an unexpected error
			SET @error_message = N'Unexpected message type received: ' + @message_type_name; 
			INSERT INTO dbo.sb_ServiceBrokerErrorLog VALUES(getdate(), @error_message, @message_body)
			END CONVERSATION @conversation_handle WITH ERROR = 1 DESCRIPTION = @error_message;
		END
END;
GO

CREATE QUEUE PreProcessMemberGroupsQueue
      WITH STATUS=ON,
      ACTIVATION (
          PROCEDURE_NAME = dbo.sb_PreProcessMemberGroupsQueueActivated,
          MAX_QUEUE_READERS = 1,
          EXECUTE AS OWNER
		);
GO

CREATE SERVICE [PlatformQueue/PreProcessMemberGroupsService] ON QUEUE dbo.PreProcessMemberGroupsQueue ([PlatformQueue/PreProcessMemberGroupsContract]);
GO

CREATE PROC dbo.sb_PreProcessMemberGroupsSendMessage
	@MessageText xml
AS
DECLARE @dialog_handle uniqueidentifier;

BEGIN DIALOG CONVERSATION @dialog_handle
   FROM SERVICE [PlatformQueue/PreProcessMemberGroupsService]
   TO SERVICE N'PlatformQueue/PreProcessMemberGroupsService'
   ON CONTRACT [PlatformQueue/PreProcessMemberGroupsContract]
   WITH ENCRYPTION = OFF;

SEND ON CONVERSATION @dialog_handle 
	MESSAGE TYPE [PlatformQueue/PreProcessMemberGroupsRequestMessage] (@MessageText);
GO


use membercentral
GO

CREATE TRIGGER trg_tr_invoices ON dbo.tr_invoices
AFTER UPDATE 
AS 

SET NOCOUNT ON

BEGIN TRY
	IF UPDATE(statusID) or UPDATE(dateDue) or UPDATE(assignedToMemberID) or UPDATE(payprofileID) BEGIN
		DECLARE @anyChange int

		-- did any of my tracking fields change for any of the rows updated?
		SELECT @anyChange = COUNT(*) 
		FROM Inserted as i 
		INNER JOIN Deleted as d ON i.invoiceID = d.invoiceID 
		WHERE i.statusID != d.statusID
		OR i.dateDue != d.dateDue
		OR i.assignedToMemberID != d.assignedToMemberID
		OR isnull(i.payprofileID,0) != isnull(d.payprofileID,0)

		IF @anyChange > 0 BEGIN
			
			-- handle any conditions that need updating
			declare @itemGroupUID uniqueidentifier
			set @itemGroupUID = NEWID()

			insert into platformQueue.dbo.tblQueueItems_preProcessMemberGroups (itemGroupUID, orgID, memberID, conditionID)
			SELECT distinct @itemGroupUID, m.orgID, m.activeMemberID, c.conditionID 
			from dbo.ams_virtualGroupConditions as c WITH(NOLOCK)
			inner join dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'acctInvProf'
			inner join Inserted as i on cast(i.invoiceProfileID as varchar(20)) = cv.conditionValue
			inner join dbo.ams_members as m on m.memberID = i.assignedToMemberID
			where c.fieldcode = 'acct_inv'
			and c.conditionTypeID = 1
				EXCEPT
			select @itemGroupUID, orgID, memberID, conditionID
			from platformQueue.dbo.tblQueueItems_preProcessMemberGroups

			IF @@ROWCOUNT > 0 BEGIN
				declare @msgXML xml
				set @msgXML = '<itemGroupUID>' + cast(@itemGroupUID as varchar(60)) + '</itemGroupUID>'
				EXEC platformQueue.dbo.sb_PreProcessMemberGroupsSendMessage @msgXML
			END	
		END
	END
END TRY
BEGIN CATCH
	-- do nothing
END CATCH

SET NOCOUNT OFF
GO

ALTER PROC [dbo].[tr_deleteInvoiceProfile]
@orgID int,
@profileID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	IF EXISTS (select profileID from dbo.tr_invoiceProfiles where profileID = @profileID AND orgID = @orgID and status <> 'D') 
		AND NOT EXISTS (select GLAccountID from dbo.tr_GLAccounts where invoiceProfileID = @profileID)
		AND NOT EXISTS (select invoiceID from dbo.tr_invoices where invoiceProfileID = @profileID)
		AND NOT EXISTS (
			select c.conditionID 
			from dbo.ams_virtualGroupConditions as c 
			inner join dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'acctInvProf'
			where cv.conditionValue = cast(@profileID as varchar(20)))
		DELETE FROM dbo.tr_invoiceProfiles
		WHERE profileID = @profileID

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

CREATE TRIGGER trg_ams_memberPaymentProfiles ON dbo.ams_memberPaymentProfiles
AFTER UPDATE 
AS 

SET NOCOUNT ON

BEGIN TRY
	IF UPDATE(lastUpdatedDate) or UPDATE(failedSinceDate) or UPDATE(failedLastDate) BEGIN
		DECLARE @anyChange int, @nowDate datetime
		set @nowDate = getdate()

		-- did any of my tracking fields change for any of the rows updated? card must be linked to invoice.
		SELECT @anyChange = COUNT(*) 
		FROM Inserted as i 
		INNER JOIN Deleted as d ON i.payProfileID = d.payProfileID 
		inner join dbo.tr_invoices as inv on inv.payProfileID = i.payProfileID
		WHERE isnull(i.lastUpdatedDate,@nowDate) != isnull(d.lastUpdatedDate,@nowDate)
		OR isnull(i.failedSinceDate,@nowDate) != isnull(d.failedSinceDate,@nowDate)
		OR isnull(i.failedLastDate,@nowDate) != isnull(d.failedLastDate,@nowDate)

		IF @anyChange > 0 BEGIN
			
			-- handle any conditions that need updating
			declare @itemGroupUID uniqueidentifier
			set @itemGroupUID = NEWID()

			insert into platformQueue.dbo.tblQueueItems_preProcessMemberGroups (itemGroupUID, orgID, memberID, conditionID)
			SELECT distinct @itemGroupUID, m.orgID, m.activeMemberID, c.conditionID 
			from dbo.ams_virtualGroupConditions as c WITH(NOLOCK)
			inner join Inserted as i on i.payProfileID = i.payProfileID
			inner join dbo.ams_members as m on m.memberID = i.memberID
			where c.fieldcode = 'acct_inv'
			and c.conditionTypeID = 1
				EXCEPT
			select @itemGroupUID, orgID, memberID, conditionID
			from platformQueue.dbo.tblQueueItems_preProcessMemberGroups

			IF @@ROWCOUNT > 0 BEGIN
				declare @msgXML xml
				set @msgXML = '<itemGroupUID>' + cast(@itemGroupUID as varchar(60)) + '</itemGroupUID>'
				EXEC platformQueue.dbo.sb_PreProcessMemberGroupsSendMessage @msgXML
			END	
		END
	END
END TRY
BEGIN CATCH
	-- do nothing
END CATCH

SET NOCOUNT OFF
GO

CREATE TRIGGER trg_tr_invoiceTransactionsInsert ON dbo.tr_invoiceTransactions
AFTER INSERT 
AS

SET NOCOUNT ON

BEGIN TRY

	-- handle any conditions that need updating
	declare @itemGroupUID uniqueidentifier
	set @itemGroupUID = NEWID()

	insert into platformQueue.dbo.tblQueueItems_preProcessMemberGroups (itemGroupUID, orgID, memberID, conditionID)
	SELECT distinct @itemGroupUID, m.orgID, m.activeMemberID, c.conditionID 
	from dbo.ams_virtualGroupConditions as c WITH(NOLOCK)
	inner join dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'acctInvProf'
	inner join dbo.ams_virtualGroupConditionValues as cv2 on cv2.conditionID = c.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k2 on k2.conditionKeyID = cv2.conditionKeyID and k2.conditionKey = 'acctInvStatus'
	inner join dbo.tr_invoices as inv on cast(inv.invoiceProfileID as varchar(20)) = cv.conditionValue
		and cast(inv.statusID as varchar(20)) = cv2.conditionValue	
	inner join Inserted as i on i.invoiceID = inv.invoiceID
	inner join dbo.ams_members as m on m.memberID = inv.assignedToMemberID
	where c.fieldcode = 'acct_inv'
	and c.conditionTypeID = 1
	and i.cache_invoiceAmountAfterAdjustment > 0
		EXCEPT
	select @itemGroupUID, orgID, memberID, conditionID
	from platformQueue.dbo.tblQueueItems_preProcessMemberGroups

	IF @@ROWCOUNT > 0 BEGIN
		declare @msgXML xml
		set @msgXML = '<itemGroupUID>' + cast(@itemGroupUID as varchar(60)) + '</itemGroupUID>'
		EXEC platformQueue.dbo.sb_PreProcessMemberGroupsSendMessage @msgXML
	END	

END TRY
BEGIN CATCH
	-- do nothing
END CATCH

SET NOCOUNT OFF
GO

CREATE TRIGGER trg_tr_invoiceTransactionsUpdate ON dbo.tr_invoiceTransactions
AFTER UPDATE 
AS 

SET NOCOUNT ON

BEGIN TRY
	IF UPDATE(invoiceID) or UPDATE(cache_invoiceAmountAfterAdjustment) or UPDATE(cache_activePaymentAllocatedAmount) or UPDATE(cache_pendingPaymentAllocatedAmount) BEGIN
		DECLARE @anyChange int

		-- did any of my tracking fields change for any of the rows updated? 
		SELECT @anyChange = COUNT(*) 
		FROM Inserted as i 
		INNER JOIN Deleted as d ON i.itID = d.itID 
		WHERE i.invoiceID != d.invoiceID
		OR i.cache_invoiceAmountAfterAdjustment != d.cache_invoiceAmountAfterAdjustment
		OR i.cache_activePaymentAllocatedAmount != d.cache_activePaymentAllocatedAmount
		OR i.cache_pendingPaymentAllocatedAmount != d.cache_pendingPaymentAllocatedAmount

		IF @anyChange > 0 BEGIN
			
			-- handle any conditions that need updating
			declare @itemGroupUID uniqueidentifier
			set @itemGroupUID = NEWID()

			insert into platformQueue.dbo.tblQueueItems_preProcessMemberGroups (itemGroupUID, orgID, memberID, conditionID)
			SELECT distinct @itemGroupUID, m.orgID, m.activeMemberID, c.conditionID 
			from dbo.ams_virtualGroupConditions as c WITH(NOLOCK)
			inner join dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'acctInvProf'
			inner join dbo.ams_virtualGroupConditionValues as cv2 on cv2.conditionID = c.conditionID
			inner join dbo.ams_virtualGroupConditionKeys as k2 on k2.conditionKeyID = cv2.conditionKeyID and k2.conditionKey = 'acctInvStatus'
			inner join dbo.tr_invoices as inv on cast(inv.invoiceProfileID as varchar(20)) = cv.conditionValue
				and cast(inv.statusID as varchar(20)) = cv2.conditionValue	
			inner join Inserted as i on i.invoiceID = inv.invoiceID
			inner join dbo.ams_members as m on m.memberID = inv.assignedToMemberID
			where c.fieldcode = 'acct_inv'
			and c.conditionTypeID = 1
				EXCEPT
			select @itemGroupUID, orgID, memberID, conditionID
			from platformQueue.dbo.tblQueueItems_preProcessMemberGroups

			IF @@ROWCOUNT > 0 BEGIN
				declare @msgXML xml
				set @msgXML = '<itemGroupUID>' + cast(@itemGroupUID as varchar(60)) + '</itemGroupUID>'
				EXEC platformQueue.dbo.sb_PreProcessMemberGroupsSendMessage @msgXML
			END	
		END
	END
END TRY
BEGIN CATCH
	-- do nothing
END CATCH

SET NOCOUNT OFF
GO

ALTER PROC [dbo].[ams_recalcVirtualGroupsBasedOnDateConditions]

AS

IF OBJECT_ID('tempdb..#tmpConds') IS NOT NULL
	DROP TABLE #tmpConds
CREATE TABLE #tmpConds (orgID int, conditionID int)

declare @today datetime, @yesterday datetime
declare @changedDateParts TABLE (datepart varchar(5) PRIMARY KEY)

set @today = getdate()
set @yesterday = dateadd(d,-1,@today)

-- figure out which dateparts have changed between today and yesterday
-- d,dw, and dy dateparts always change day to day, by definition
insert into @changedDateParts (datepart) values ('d')
insert into @changedDateParts (datepart) values ('dy')
insert into @changedDateParts (datepart) values ('dw')

-- should be triggered when today is the first of the week
if datediff(week,@yesterday,@today) > 0
	insert into @changedDateParts (datepart) values ('wk')

-- should be triggered when today is the first of the month
if datediff(month,@yesterday,@today) > 0
	insert into @changedDateParts (datepart) values ('m')

-- should be triggered when today is the first of the quarter
if datediff(quarter,@yesterday,@today) > 0
	insert into @changedDateParts (datepart) values ('q')

-- should be triggered when today is the first of the year
if datediff(year,@yesterday,@today) > 0
	insert into @changedDateParts (datepart) values ('yy')


-- which orgs have active rules based on date conditions and linked to groups?
INSERT INTO #tmpConds (orgID, conditionID)
select distinct r.orgID, vgc.conditionID
from dbo.ams_virtualGroupRules as r
inner join dbo.ams_virtualGroupRuleGroups as rg on rg.ruleID = r.ruleID and r.isActive = 1
cross apply r.ruleXML.nodes('//condition') as C(condition)
inner join dbo.ams_virtualGroupConditions as vgc on vgc.uid = C.condition.value('@id','uniqueidentifier')
inner join @changedDateParts as cdp on cdp.datepart = vgc.datepart
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = vgc.expressionID 
where e.expression in ('datepart','datediff')

-- acct_inv conditions are based on due date of invoice so these need to be rerun too if the due date is in range
INSERT INTO #tmpConds (orgID, conditionID)
select distinct r.orgID, vgc.conditionID
from dbo.ams_virtualGroupRules as r
inner join dbo.ams_virtualGroupRuleGroups as rg on rg.ruleID = r.ruleID and r.isActive = 1
cross apply r.ruleXML.nodes('//condition') as C(condition)
inner join dbo.ams_virtualGroupConditions as vgc on vgc.uid = C.condition.value('@id','uniqueidentifier')
inner join dbo.ams_members as m on m.orgID = vgc.orgID
inner join dbo.tr_invoices as i on i.assignedToMemberID = m.memberID
where vgc.fieldcode = 'acct_inv'
and i.dateDue between dateadd(d,-2,@today) and dateadd(d,1,@today)


-- populate member group cache (@runSchedule=1 indicates immediate processing)
declare @minOrgID int, @conditionIDList varchar(max), @itemGroupUID uniqueidentifier
select @minOrgID = min(orgID) from #tmpConds
while @minOrgID is not null BEGIN
	SET @conditionIDList = null
	SET @itemGroupUID = null

	select @conditionIDList = COALESCE(@conditionIDList + ',', '') + cast(conditionID as varchar(10)) 
		from #tmpConds 
		where orgID = @minOrgID
		group by conditionID
	EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@minOrgID, @memberIDList='', @conditionIDList=@conditionIDList, @runSchedule=1, @itemGroupUID=@itemGroupUID OUTPUT

	select @minOrgID = min(orgID) from #tmpConds where orgID > @minOrgID
END

RETURN 0
GO


