USE [platformstats]
GO
/****** Object:  StoredProcedure [dbo].[stats_pageDashboard]    Script Date: 02/19/2014 17:26:40 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROCEDURE [dbo].[stats_pageDashboard]
@siteID int,
@statsStart smalldatetime,
@statsEnd smalldatetime

WITH RECOMPILE

AS

SET NOCOUNT ON

-- declare vars
DECLARE @sessionStart smalldatetime, @sessionEnd smalldatetime
DECLARE @statSessions TABLE (sessionID int PRIMARY KEY, memberID int)
DECLARE @PageViewsLoggedIn int, @PageViewsNotLoggedIn int, @SessionsLoggedIn int, @SessionsNotLoggedIn int

/*
24hr padding with (NOLOCK) ON session start time just to make sure 
that we have the sessions for ALL of the hits in the time range
-- reset start date to 00:00 of startdate
-- reset end date to 23:59 of enddate (actually 0:00 of next day)
*/
select @sessionStart = DateAdd(dd,-1,@statsStart)
select @sessionEnd = @statsEnd
SELECT @statsStart = DATEADD(dd, DATEDIFF(dd,0,@statsStart), 0)
SELECT @statsEnd = DATEADD(dd, DATEDIFF(dd,0,@statsEnd)+1, 0)

-- stat sessions
INSERT INTO @statSessions (sessionID, memberID)
select sessionid, memberid
from dbo.statsSessions with (NOLOCK)
where siteID = @siteID
and dateentered between @sessionStart and @sessionEnd
and ignore = 0

SELECT @PageViewsLoggedIn = COUNT(sph.pageHitID)
	FROM dbo.statsPageHits as sph with (NOLOCK)
	INNER JOIN @statSessions as ss ON sph.sessionid = ss.sessionid AND ss.memberid IS NOT NULL
	WHERE sph.allowed = 1
	AND sph.dateentered BETWEEN @statsStart and @statsEnd
	option (RECOMPILE)

SELECT @PageViewsNotLoggedIn = COUNT(sph.pageHitID)
	FROM dbo.statsPageHits as sph with (NOLOCK)
	INNER JOIN @statSessions as ss ON sph.sessionid = ss.sessionid AND ss.memberid IS NULL
	WHERE sph.allowed = 1
	AND sph.dateentered BETWEEN @statsStart and @statsEnd
	option (RECOMPILE)

SELECT @SessionsLoggedIn = count(DISTINCT sph.sessionid)
	FROM dbo.statsPageHits AS sph with (NOLOCK)
	INNER JOIN @statSessions AS ss ON sph.sessionid = ss.sessionid AND ss.memberid IS NOT NULL
	WHERE sph.dateentered BETWEEN @statsStart and @statsEnd
	option (RECOMPILE)

SELECT @SessionsNotLoggedIn = count(DISTINCT sph.sessionid)
	FROM dbo.statsPageHits AS sph with (NOLOCK)
	INNER JOIN @statSessions AS ss ON sph.sessionid = ss.sessionid AND ss.memberid IS NULL
	WHERE sph.dateentered BETWEEN @statsStart and @statsEnd
	option (RECOMPILE)

-- summary counts
select @PageViewsLoggedIn as PageViewsLoggedIn, 
	@PageViewsNotLoggedIn as PageViewsNotLoggedIn,
	@SessionsLoggedIn as SessionsLoggedIn, 
	@SessionsNotLoggedIn as SessionsNotLoggedIn,
	AVGPageViewsLoggedIn = case @SessionsLoggedIn when 0 then 0 else cast(cast(@PageViewsLoggedIn as decimal(8,1)) / cast(@SessionsLoggedIn as decimal(8,1)) as decimal(8,1)) end,
	AVGPageViewsNotLoggedIn = case @SessionsNotLoggedIn when 0 then 0 else cast(cast(@PageViewsNotLoggedIn as decimal(8,1)) / cast(@SessionsNotLoggedIn as decimal(8,1)) as decimal(8,1)) end,
	AVGPageViews = case @SessionsLoggedIn+@SessionsNotLoggedIn when 0 then 0 else cast(cast(@PageViewsLoggedIn+@PageViewsNotLoggedIn as decimal(8,1)) / cast(@SessionsLoggedIn+@SessionsNotLoggedIn as decimal(8,1)) as decimal(8,1)) end

-- qryLoggedInViews
SELECT TOP 10 COUNT(sph.pageHitID) AS viewCount, p.pageName, pl.pageTitle
FROM dbo.statsPageHits AS sph with (NOLOCK)
INNER JOIN @statSessions as ss ON sph.sessionid = ss.sessionid 
	and sph.allowed = 1 
	and sph.dateentered BETWEEN @statsStart and @statsEnd 
	and ss.memberID is not null
INNER JOIN membercentral.dbo.cms_pages AS p with (NOLOCK) ON p.pageId = sph.pageid
INNER JOIN membercentral.dbo.cms_pageLanguages as pl with (NOLOCK) ON pl.pageID = p.pageID and pl.languageID = 1
where p.pageName <> 'Login'
GROUP BY p.pageName, pl.pageTitle
ORDER BY COUNT(sph.pageHitID) desc
option (RECOMPILE)

-- qryNotLoggedInViews
SELECT TOP 10 COUNT(sph.pageHitID) AS viewCount, p.pageName, pl.pageTitle
FROM dbo.statsPageHits AS sph with (NOLOCK)
INNER JOIN @statSessions as ss ON sph.sessionid = ss.sessionid 
	and sph.allowed = 1 
	and sph.dateentered BETWEEN @statsStart and @statsEnd 
	and ss.memberID is null
INNER JOIN membercentral.dbo.cms_pages AS p with (NOLOCK) ON p.pageId = sph.pageid
INNER JOIN membercentral.dbo.cms_pageLanguages as pl with (NOLOCK) ON pl.pageID = p.pageID and pl.languageID = 1
where p.pageName <> 'Login'
GROUP BY p.pageName, pl.pageTitle
ORDER BY COUNT(sph.pageHitID) desc
option (RECOMPILE)

RETURN 0

GO


USE [platformstats]
GO
/****** Object:  StoredProcedure [dbo].[up_pageStats]    Script Date: 02/19/2014 17:30:02 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROCEDURE [dbo].[up_pageStats]
	@siteID int = 0,
	@orgID int = 0,
	@pageName varchar(100) = '',
	@statsStart smalldatetime = getdate,
	@statsEnd smalldatetime = getdate


	WITH RECOMPILE
AS

SET NOCOUNT ON

-- declare vars
DECLARE @pageid int, @pagecreated datetime, @pageModified datetime, @resourceid int
DECLARE @tmpStats TABLE (
	pageID int,
	pagecreated datetime,
	pagemodified datetime,
	resourceid int,
	numRegisteredMembers int,	
	numTotalMembers int,	
	memberGrossViewsAllowed int,
	guestGrossViewsAllowed int,
	totalGrossViewsAllowed int,
	memberGrossViewsDenied int,
	guestGrossViewsDenied int,
	totalGrossViewsDenied int,
	memberSessions int,
	guestSessions int,
	totalSessions int,
	memberPageViewsPerSession int,
	guestPageViewsPerSession int,
	totalPageViewsPerSession int,
	membersVisited int,
	membersVisitedPctRegistered int,
	membersVisitedPctTotal int
)

-- reset start date to 00:00 of startdate
-- reset end date to 23:59 of enddate (actually 0:00 of next day)
SELECT @statsStart = DATEADD(dd, DATEDIFF(dd,0,@statsStart), 0)
SELECT @statsEnd = DATEADD(dd, DATEDIFF(dd,0,@statsEnd)+1, 0)

-- get pageid
select @pageid = pageid, @resourceid = siteResourceid
from membercentral.dbo.cms_pages with (NOLOCK)
where siteID = @siteID
and pagename = @pageName

INSERT INTO @tmpStats (pageID, resourceid)
VALUES (@pageid, @resourceid)


-- numRegisteredMembers
UPDATE @tmpStats
set numRegisteredMembers = (SELECT COUNT(m.memberID)
	from membercentral.dbo.ams_members m  with (NOLOCK)
		INNER JOIN membercentral.dbo.ams_memberNetworkProfiles AS mnp with (NOLOCK) ON mnp.memberID = m.memberID
	where m.orgID = @orgID
	AND m.status = 'A')

-- numTotalMembers
UPDATE @tmpStats
set numTotalMembers = (SELECT COUNT(memberid)
	FROM membercentral.dbo.ams_members with (NOLOCK)
	WHERE orgID = @orgID
	and status = 'A')



-- memberGrossViewsAllowed
UPDATE @tmpStats
set memberGrossViewsAllowed = (select COUNT(sph.pageHitID)
	FROM dbo.statsPageHits as sph with (NOLOCK) 
	INNER JOIN dbo.statsSessions as ss with (NOLOCK) ON sph.sessionid = ss.sessionid
	WHERE ss.siteID = @siteID
	AND ss.ignore = 0
	AND sph.allowed = 1
	AND sph.pageID = @pageid
	AND ss.memberid IS NOT NULL
	AND sph.dateentered BETWEEN @statsStart and @statsEnd)

-- guestGrossViewsAllowed
UPDATE @tmpStats
set guestGrossViewsAllowed = (select COUNT(sph.pageHitID)
	FROM dbo.statsPageHits as sph  with (NOLOCK)
	INNER JOIN dbo.statsSessions as ss with (NOLOCK) ON sph.sessionid = ss.sessionid
	WHERE ss.siteID = @siteID
	AND ss.ignore = 0
	AND sph.allowed = 1
	AND sph.pageID = @pageid
	AND ss.memberid IS NULL
	AND sph.dateentered BETWEEN @statsStart and @statsEnd)

-- totalGrossViewsAllowed
UPDATE @tmpStats
set totalGrossViewsAllowed = isnull(memberGrossViewsAllowed,0) + isnull(guestGrossViewsAllowed,0)

-- memberGrossViewsDenied
UPDATE @tmpStats
set memberGrossViewsDenied = (select COUNT(sph.pageHitID)
	FROM dbo.statsPageHits as sph  with (NOLOCK)
	INNER JOIN dbo.statsSessions as ss with (NOLOCK) ON sph.sessionid = ss.sessionid
	WHERE ss.siteID = @siteID
	AND ss.ignore = 0
	AND sph.allowed = 0
	AND sph.pageID = @pageid
	AND ss.memberid IS NOT NULL
	AND sph.dateentered BETWEEN @statsStart and @statsEnd)

-- guestGrossViewsDenied
UPDATE @tmpStats
set guestGrossViewsDenied = (select COUNT(sph.pageHitID)
	FROM dbo.statsPageHits as sph  with (NOLOCK)
	INNER JOIN dbo.statsSessions as ss with (NOLOCK) ON sph.sessionid = ss.sessionid
	WHERE ss.siteID = @siteID
	AND ss.ignore = 0
	AND sph.allowed = 0
	AND sph.pageID = @pageid
	AND ss.memberid IS NULL
	AND sph.dateentered BETWEEN @statsStart and @statsEnd)

-- totalGrossViewsDenied
UPDATE @tmpStats
set totalGrossViewsDenied = isnull(memberGrossViewsDenied,0) + isnull(guestGrossViewsDenied,0)

-- memberSessions
UPDATE @tmpStats
set memberSessions = (select COUNT(DISTINCT sph.sessionid)
	FROM dbo.statsPageHits AS sph  with (NOLOCK)
	INNER JOIN dbo.statsSessions AS ss with (NOLOCK) ON sph.sessionid = ss.sessionid
	WHERE ss.siteID = @siteID
	AND ss.ignore = 0
	AND sph.pageID = @pageid
	AND ss.memberid IS NOT NULL
	AND sph.dateentered BETWEEN @statsStart and @statsEnd)

-- guestSessions
UPDATE @tmpStats
set guestSessions = (select COUNT(DISTINCT sph.sessionid)
	FROM dbo.statsPageHits AS sph  with (NOLOCK)
	INNER JOIN dbo.statsSessions AS ss with (NOLOCK) ON sph.sessionid = ss.sessionid
	WHERE ss.siteID = @siteID
	AND ss.ignore = 0
	AND sph.pageID = @pageid
	AND ss.memberid IS NULL
	AND sph.dateentered BETWEEN @statsStart and @statsEnd)

-- totalSessions
UPDATE @tmpStats
set totalSessions = isnull(memberSessions,0) + isnull(guestSessions,0)

-- memberPageViewsPerSession, guestPageViewsPerSession, totalPageViewsPerSession
UPDATE @tmpStats
set memberPageViewsPerSession = CASE
	WHEN memberGrossViewsAllowed = 0 or memberSessions = 0 then 0
	ELSE cast(memberGrossViewsAllowed / memberSessions as int)
	END,
	guestPageViewsPerSession = CASE
	WHEN guestGrossViewsAllowed = 0 or guestSessions = 0 then 0
	ELSE cast(guestGrossViewsAllowed / guestSessions as int)
	END,
	totalPageViewsPerSession = CASE
	WHEN totalGrossViewsAllowed = 0 or totalSessions = 0 then 0
	ELSE cast(totalGrossViewsAllowed / totalSessions as int)
	END

-- membersVisited
UPDATE @tmpStats
set membersVisited = (select COUNT(DISTINCT ss.memberid)
	FROM dbo.statsPageHits AS sph  with (NOLOCK)
	INNER JOIN dbo.statsSessions AS ss with (NOLOCK) ON sph.sessionid = ss.sessionid
	INNER JOIN membercentral.dbo.ams_members as o with (NOLOCK) ON o.memberid = ss.memberid
	WHERE ss.siteID = @siteID
	AND o.orgID = @orgID
	AND ss.ignore = 0
	AND sph.pageID = @pageid
	AND ss.memberid IS NOT NULL
	AND sph.dateentered BETWEEN @statsStart and @statsEnd)

-- membersVisitedPctRegistered, membersVisitedPctTotal
UPDATE @tmpStats
set membersVisitedPctRegistered = CASE
	WHEN numRegisteredMembers = 0 or membersVisited = 0 then 0
	ELSE cast(membersVisited / numRegisteredMembers * 100 as int)
	END,
	membersVisitedPctTotal = CASE
	WHEN numTotalMembers = 0 or membersVisited = 0 then 0
	ELSE cast(membersVisited / numTotalMembers * 100 as int)
	END

-- return one query for the counts
SELECT * from @tmpStats

-- page security
select srr.groupid, g.GroupName
	from membercentral.dbo.cms_siteResourceRights srr
	INNER JOIN membercentral.dbo.ams_groups as g on g.groupID = srr.GroupID
where resourceid = @resourceid

-- top referers
SELECT top 10 sph.refererPageid, count(sph.pagehitid) as numhits, p.pagename
FROM dbo.statsPageHits as sph  with (NOLOCK) 
INNER JOIN dbo.statsSessions as ss with (NOLOCK) ON sph.sessionid = ss.sessionid
left outer join membercentral.dbo.cms_pages as p with (NOLOCK) ON p.pageid = sph.refererPageid
WHERE sph.pageID = @pageid
and sph.dateentered BETWEEN @statsStart and @statsEnd
and ss.ignore = 0
and ss.siteID = @siteID
group by sph.refererpageid, p.pagename
order by count(sph.pagehitid) desc

-- top destinations
SELECT top 10 sph.Pageid, count(sph.pagehitid) as numhits, p.pagename
FROM dbo.statsPageHits as sph   with (NOLOCK)
INNER JOIN dbo.statsSessions as ss with (NOLOCK) ON sph.sessionid = ss.sessionid
left outer join membercentral.dbo.cms_pages as p with (NOLOCK) ON p.pageid = sph.Pageid
WHERE sph.refererpageID = @pageid
and sph.dateentered BETWEEN @statsStart and @statsEnd
and ss.ignore = 0
and ss.siteID = @siteID
group by sph.pageid, p.pagename
order by count(sph.pagehitid) desc