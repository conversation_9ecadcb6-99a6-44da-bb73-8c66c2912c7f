
/*
This sql script performs the following changes:
*** INSERT INTO TABLE tblQueueTypes 
*** CREATE TABLE tblQueueItems_clientSurveys 
*** CREATE TABLE tblQueueItems_clientSurveysDetail 
*** CREATE SP job_clientSurveys_grabForProcessing 
*** CREATE TABLE ref_panelSurveyClientQueue
*** CREATE SP ref_addClientSurveyQueue 
*** ALTER SP job_runDailyMaintenanceJobs 
*/

use platformQueue
GO

print '*** INSERT INTO TABLE tblQueueTypes *************************************************************************'
GO

declare @queueTypeID int

insert into dbo.tblQueueTypes 
values ('clientSurveys')

select @queueTypeID = SCOPE_IDENTITY()

print '*** INSERT INTO TABLE tblQueueStatuses *************************************************************************'

insert into dbo.tblQueueStatuses (queueTypeID, queueStatus)
select 
	@queueTypeID, queueStatus
from 
	dbo.tblQueueStatuses 
where 
	queueTypeID = 1
	and queueStatus not in ('processingAllocation','processingPayment')

insert into dbo.tblQueueStatuses (queueTypeID, queueStatus)
values (@queueTypeID, 'processingItem')
GO

print '*** CREATE TABLE tblQueueItems_clientSurveys *************************************************************************'
GO

IF OBJECT_ID('tblQueueItems_clientSurveys') IS NOT NULL 
	EXEC('DROP TABLE  tblQueueItems_clientSurveys')

GO

CREATE TABLE dbo.tblQueueItems_clientSurveys(
	itemUID uniqueidentifier PRIMARY KEY ,
	itemGroupUID uniqueidentifier NOT NULL,
	referralID int NOT NULL,
	clientReferralUID varchar(50) NOT NULL
)
GO

print '*** CREATE TABLE tblQueueItems_clientSurveysDetail *************************************************************************'
GO

IF OBJECT_ID('tblQueueItems_clientSurveysDetail') IS NOT NULL 
	EXEC('DROP TABLE  tblQueueItems_clientSurveysDetail')

GO

CREATE TABLE dbo.tblQueueItems_clientSurveysDetail (
	[itemUID] uniqueidentifier not null,
	[clientReferralID] [int] NOT NULL,
	[clientID] [int] NOT NULL,
	[referralID] [int] NOT NULL,
	[firstName] [varchar](75) NOT NULL,
	[middleName] [varchar](25) NULL,
	[lastName] [varchar](75) NOT NULL,
	[clientName] [varchar](152) NOT NULL,
	[businessName] [varchar](100) NULL,
	[address1] [varchar](100) NULL,
	[address2] [varchar](100) NULL,
	[address3] [varchar](100) NULL,
	[city] [varchar](100) NULL,
	[state] [int] NULL,
	[postalCode] [varchar](25) NULL,
	[countryID] [int] NULL,
	[email] [varchar](255) NULL,
	[homePhone] [varchar](40) NULL,
	[cellPhone] [varchar](40) NULL,
	[alternatePhone] [varchar](40) NULL,
	[typeID] [int] NOT NULL,
	[statusID] [int] NULL,
	[statusName] [varchar](255) NULL,
	[isReferred] [bit] NULL,
	[isAgency] [bit] NULL,
	[isPending] [bit] NULL,
	[clientDateCreated] [datetime] NULL,
	[clientCreatedBt] [int] NULL,
	[clientDateLastUpdated] [datetime] NULL,
	[clientParentID] [int] NULL,
	[clientReferralUID] [varchar](50) NOT NULL,
	[memberID] [int] NULL,
	[enteredByMemberID] [int] NULL,
	[sourceID] [int] NULL,
	[counselorNotes] [varchar](max) NULL,
	[attorneyNotes] [varchar](max) NULL,
	[communicateLanguageID] [int] NULL,
	[issueDesc] [varchar](max) NULL,
	[sendSurvey] [bit] NOT NULL,
	[sendNewsBlog] [bit] NOT NULL,
	[clientReferralTypeID] [int] NULL,
	[clientReferralDate] [datetime] NULL,
	[dateCreated] [datetime] NULL,
	[lastUpdatedBy] [int] NULL,
	[dateLastUpdated] [datetime] NULL,
	[repID] [int] NULL,
	[repFirstName] [varchar](75) NULL,
	[repLastName] [varchar](75) NULL,
	[repName] [varchar](152) NULL,
	[repAddress1] [varchar](100) NULL,
	[repAddress2] [varchar](100) NULL,
	[repAddress3] [varchar](100) NULL,
	[repCity] [varchar](100) NULL,
	[repState] [int] NULL,
	[repPostalCode] [varchar](25) NULL,
	[repCountryID] [int] NULL,
	[repEmail] [varchar](255) NULL,
	[repHomePhone] [varchar](40) NULL,
	[repCellPhone] [varchar](40) NULL,
	[repAlternatePhone] [varchar](40) NULL,
	[repTypeID] [int] NULL,
	[relationToClient] [varchar](100) NULL,
	[repParentID] [int] NULL,
	[memFirstName] [varchar](75) NULL,
	[memMiddleName] [varchar](25) NULL,
	[memLastname] [varchar](75) NULL,
	[prefix] [varchar](50) NULL,
	[suffix] [varchar](50) NULL,
	[company] [varchar](200) NULL,
	[memberName] [varchar](153) NULL,
	[memberNumber] [varchar](50) NULL,
	[daysElapsed] [int] NULL,
	[caseID] [int] NULL,
	[caseEnteredByMemberID] [int] NULL,
	[caseNotesTxt] [varchar](max) NULL,
	[caseFees] [money] NULL,
	[dateCaseOpened] [datetime] NULL,
	[dateCaseClosed] [datetime] NULL,
	[dateCaseCreated] [datetime] NULL,
	[dateCaseLastUpdated] [datetime] NULL,
	[languageID] [int] NOT NULL,
	[languageName] [varchar](100) NOT NULL,
	[panelID] [int] NOT NULL,
	[panelName] [varchar](255) NOT NULL,
	[surveyID] [int] NOT NULL,
	[surveyName] [varchar](255) NULL,
	[surveyTypeID] [int] NOT NULL,
	[surveyTypeName] [varchar](255) NOT NULL,
	[surveyFreq] [int] NOT NULL,
	[isRetainedCase] [bit] NULL,
	[isClosedCase] [bit] NULL,
	[isModestMeans] [bit] NULL,
	[siteResourceID] [int] NOT NULL,
	[contentCreatedDt] [datetime] NOT NULL,
	[contentDesc] [varchar](400) NOT NULL,
	[languageDt] [datetime] NOT NULL,
	[languageModDt] [datetime] NOT NULL,
	[languageCode] [varchar](5) NULL,
	[contentID] [int] NOT NULL,
	[isSSL] [bit] NOT NULL,
	[isHTML] [bit] NOT NULL,
	[enableSocialMediaSharing] [bit] NOT NULL,
	[contentLanguageID] [int] NULL,
	[contentVersionID] [int] NULL,
	[emailSubject] [varchar](200) NULL,
	[scContentDesc] [varchar](400) NULL,
	[emailContent] [varchar](max) NULL,
	[emailSent] [bit] NOT NULL default(0),
	 CONSTRAINT pk_tblQueueItems_clientSurveysDetail  PRIMARY KEY (itemUID, clientReferralID) WITH (FILLFACTOR=90, IGNORE_DUP_KEY = ON))
GO

print '*** CREATE SP job_clientSurveys_grabForProcessing *************************************************************************'
GO

IF (OBJECT_ID('job_clientSurveys_grabForProcessing') IS NOT NULL)
  DROP PROCEDURE job_clientSurveys_grabForProcessing
GO

CREATE PROC dbo.job_clientSurveys_grabForProcessing
@serverID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @batchSize int
	set @batchSize = 120

	declare @statusReady int, @statusGrabbed int

	select 
		@statusReady = qs.queueStatusID 
	from 
		platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
	where 
		qt.queueType = 'clientSurveys'
		and qs.queueStatus = 'readyToProcess'

	select 
		@statusGrabbed = qs.queueStatusID 
	from 
		platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on 
			qt.queueTypeID = qs.queueTypeID
	where 
		qt.queueType = 'clientSurveys'
		and qs.queueStatus = 'grabbedForProcessing'

	IF OBJECT_ID('tempdb..#tmpTblQueueItems_clientSurveys') IS NOT NULL 
		EXEC('DROP TABLE  #tmpTblQueueItems_clientSurveys')

	CREATE TABLE #tmpTblQueueItems_clientSurveys(
		itemUID uniqueidentifier,
		jobUID uniqueidentifier,
		referralID int,
		clientReferralUID varchar(50)
	)

	declare @jobUID uniqueidentifier
	set @jobUID = NEWID()

	-- dequeue in order of dateAdded. get @batchsize payments
	update 
		qi WITH (UPDLOCK, READPAST)
	set 
		qi.queueStatusID = @statusGrabbed,
		qi.dateUpdated = getdate(),
		qi.jobUID = @jobUID,
		qi.jobDateStarted = getdate(),
		qi.jobServerID = @serverID
		OUTPUT inserted.itemUID, inserted.jobUID, qid.referralID, qid.clientReferralUID
		INTO #tmpTblQueueItems_clientSurveys
	from
		 platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueItems_clientSurveys as qid ON 
			qid.itemUID = qi.itemUID
		inner join (
			select 
				top(@BatchSize) qi2.itemUID 
			from 
				platformQueue.dbo.tblQueueItems as qi2
				inner join platformQueue.dbo.tblQueueItems_clientSurveys as qid2 ON 
					qid2.itemUID = qi2.itemUID
			where 
				qi2.queueStatusID = @statusReady
			order by 
				qi2.dateAdded, qi2.itemUID
		) as batch on batch.itemUID = qi.itemUID
	where 
		qi.queueStatusID = @statusReady

	-- final data
	select 
		qid.jobUID,
		qidd.* 
	from 
		#tmpTblQueueItems_clientSurveys as qid
		inner join platformQueue.dbo.tblQueueItems_ClientSurveysDetail as qidd on 
			qidd.itemUID = qid.itemUID
	order by 
		qid.itemUID

	IF OBJECT_ID('tempdb..#tmpTblQueueItems_clientSurveys') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_clientSurveys

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH

GO

IF (OBJECT_ID('job_clientSurveys_clearDone') IS NOT NULL)
  DROP PROCEDURE job_clientSurveys_clearDone
GO

CREATE PROC dbo.job_clientSurveys_clearDone
AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @statusDone int
	select 
		@statusDone = qs.queueStatusID 
	from 
		platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on 
			qt.queueTypeID = qs.queueTypeID
	where 
		qt.queueType = 'clientSurveys'
		and qs.queueStatus = 'done'

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select 
			distinct qid.itemGroupUID
		from 
			platformQueue.dbo.tblQueueItems as qi
			inner join platformQueue.dbo.tblQueueItems_clientSurveys as qid ON 
				qid.itemUID = qi.itemUID
		where qi.queueStatusID = @statusDone
			except
		select 
			distinct qid.itemGroupUID
		from 
			platformQueue.dbo.tblQueueItems as qi
			inner join platformQueue.dbo.tblQueueItems_clientSurveys as qid on 
				qid.itemUID = qi.itemUID
		where 
			qi.queueStatusID <> @statusDone
	)
	DELETE from platformQueue.dbo.tblQueueItems
	where itemUID in (
		select 
			qi.itemUID
		from 
			platformQueue.dbo.tblQueueItems as qi
			inner join platformQueue.dbo.tblQueueItems_clientSurveys as qid on 
				qid.itemUID = qi.itemUID
			inner join itemGroupUIDs on 
				itemGroupUIDs.itemGroupUID = qid.itemGroupUID
		WHERE 
			qi.queueStatusID = @statusDone
	)

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH
GO

use platformStats
GO

print '*** CREATE TABLE ref_panelSurveyClientQueue *************************************************************************'
GO

IF OBJECT_ID('ref_panelSurveyClientQueue') IS NOT NULL 
	EXEC('DROP TABLE  ref_panelSurveyClientQueue')

GO

CREATE TABLE dbo.ref_panelSurveyClientQueue(
	historyID int IDENTITY(1,1) NOT NULL PRIMARY KEY CLUSTERED,
	surveyID int NOT NULL,
	clientReferralUID varchar(50) NOT NULL,
	dateCreated datetime
)
GO

use membercentral
GO

print '*** CREATE SP ref_addClientSurveyQueue *************************************************************************'
GO

IF (OBJECT_ID('ref_addClientSurveyQueue') IS NOT NULL)
  DROP PROCEDURE ref_addClientSurveyQueue
GO

CREATE PROC dbo.ref_addClientSurveyQueue
	@referralID int

AS

SET NOCOUNT ON

BEGIN TRAN

BEGIN TRY

	declare 
	@surveyTypeID int,
	@surveyTypeName varchar (1000),
	@surveyFreq int,
	@isRetainedCase bit,
	@isClosedCase bit,
	@isModestMeans bit,
	@sql varchar (max),
	@surveyRecordCount int,
	@loopIndex int,
	@statusInserting int, 
	@statusReady int,
	@dateAdded datetime

	set @dateAdded = getdate()

	declare @surveyTbl as table (
		surveyTypeID int PRIMARY KEY,
		surveyTypeName varchar (1000),
		surveyFreq int,
		isRetainedCase bit,
		isClosedCase bit,
		isModestMeans bit
	)

	IF OBJECT_ID('tempdb..#tmpClientSurveysTbl') IS NOT NULL 
		EXEC('DROP TABLE  #tmpClientSurveysTbl')

	CREATE TABLE #tmpClientSurveysTbl (
		[clientReferralID] [int] NOT NULL,
		[clientID] [int] NOT NULL,
		[referralID] [int] NOT NULL,
		[firstName] [varchar](75) NOT NULL,
		[middleName] [varchar](25) NULL,
		[lastName] [varchar](75) NOT NULL,
		[clientName] [varchar](152) NOT NULL,
		[businessName] [varchar](100) NULL,
		[address1] [varchar](100) NULL,
		[address2] [varchar](100) NULL,
		[address3] [varchar](100) NULL,
		[city] [varchar](100) NULL,
		[state] [int] NULL,
		[postalCode] [varchar](25) NULL,
		[countryID] [int] NULL,
		[email] [varchar](255) NULL,
		[homePhone] [varchar](40) NULL,
		[cellPhone] [varchar](40) NULL,
		[alternatePhone] [varchar](40) NULL,
		[typeID] [int] NOT NULL,
		[statusID] [int] NULL,
		[statusName] [varchar](255) NULL,
		[isReferred] [bit] NULL,
		[isAgency] [bit] NULL,
		[isPending] [bit] NULL,
		[clientDateCreated] [datetime] NULL,
		[clientCreatedBt] [int] NULL,
		[clientDateLastUpdated] [datetime] NULL,
		[clientParentID] [int] NULL,
		[clientReferralUID] [varchar](50) NULL,
		[memberID] [int] NULL,
		[enteredByMemberID] [int] NULL,
		[sourceID] [int] NULL,
		[counselorNotes] [varchar](max) NULL,
		[attorneyNotes] [varchar](max) NULL,
		[communicateLanguageID] [int] NULL,
		[issueDesc] [varchar](max) NULL,
		[sendSurvey] [bit] NOT NULL,
		[sendNewsBlog] [bit] NOT NULL,
		[clientReferralTypeID] [int] NULL,
		[clientReferralDate] [datetime] NULL,
		[dateCreated] [datetime] NULL,
		[lastUpdatedBy] [int] NULL,
		[dateLastUpdated] [datetime] NULL,
		[repID] [int] NULL,
		[repFirstName] [varchar](75) NULL,
		[repLastName] [varchar](75) NULL,
		[repName] [varchar](152) NULL,
		[repAddress1] [varchar](100) NULL,
		[repAddress2] [varchar](100) NULL,
		[repAddress3] [varchar](100) NULL,
		[repCity] [varchar](100) NULL,
		[repState] [int] NULL,
		[repPostalCode] [varchar](25) NULL,
		[repCountryID] [int] NULL,
		[repEmail] [varchar](255) NULL,
		[repHomePhone] [varchar](40) NULL,
		[repCellPhone] [varchar](40) NULL,
		[repAlternatePhone] [varchar](40) NULL,
		[repTypeID] [int] NULL,
		[relationToClient] [varchar](100) NULL,
		[repParentID] [int] NULL,
		[memFirstName] [varchar](75) NULL,
		[memMiddleName] [varchar](25) NULL,
		[memLastname] [varchar](75) NULL,
		[prefix] [varchar](50) NULL,
		[suffix] [varchar](50) NULL,
		[company] [varchar](200) NULL,
		[memberName] [varchar](153) NULL,
		[memberNumber] [varchar](50) NULL,
		[daysElapsed] [int] NULL,
		[caseID] [int] NULL,
		[caseEnteredByMemberID] [int] NULL,
		[caseNotesTxt] [varchar](max) NULL,
		[caseFees] [money] NULL,
		[dateCaseOpened] [datetime] NULL,
		[dateCaseClosed] [datetime] NULL,
		[dateCaseCreated] [datetime] NULL,
		[dateCaseLastUpdated] [datetime] NULL,
		[languageID] [int] NOT NULL,
		[languageName] [varchar](100) NOT NULL,
		[panelID] [int] NOT NULL,
		[panelName] [varchar](255) NOT NULL,
		[surveyID] [int] NOT NULL,
		[surveyName] [varchar](255) NULL,
		[surveyTypeID] [int] NOT NULL,
		[surveyTypeName] [varchar](255) NOT NULL,
		[surveyFreq] [int] NOT NULL,
		[isRetainedCase] [bit] NULL,
		[isClosedCase] [bit] NULL,
		[isModestMeans] [bit] NULL,
		[siteResourceID] [int] NOT NULL,
		[contentCreatedDt] [datetime] NOT NULL,
		[contentDesc] [varchar](400) NOT NULL,
		[languageDt] [datetime] NOT NULL,
		[languageModDt] [datetime] NOT NULL,
		[languageCode] [varchar](5) NULL,
		[contentID] [int] NOT NULL,
		[isSSL] [bit] NOT NULL,
		[isHTML] [bit] NOT NULL,
		[enableSocialMediaSharing] [bit] NOT NULL,
		[contentLanguageID] [int] NULL,
		[contentVersionID] [int] NULL,
		[emailSubject] [varchar](200) NULL,
		[scContentDesc] [varchar](400) NULL,
		[emailContent] [varchar](max) NULL,
		[itemUID] uniqueidentifier NULL,
		[itemGroupUID] uniqueidentifier NULL,
		[emailSent] [bit] NOT NULL default(0)
	)

	-- exec  tempdb..sp_help #tmpClientSurveysTbl

	IF OBJECT_ID('tempdb..#tmpTblQueueItems_clientSurveys') IS NOT NULL 
		EXEC('DROP TABLE  #tmpTblQueueItems_clientSurveys')

	CREATE TABLE #tmpTblQueueItems_clientSurveys(
		itemUID uniqueidentifier PRIMARY KEY DEFAULT NEWID(),
		itemGroupUID uniqueidentifier NOT NULL,
		referralID int NOT NULL,
		clientReferralUID varchar(50) NOT NULL
	)

	IF OBJECT_ID('tempdb..#tmpClientSurveyReferrals') IS NOT NULL 
		EXEC('DROP TABLE  #tmpClientSurveyReferrals')

	CREATE TABLE #tmpClientSurveyReferrals (
		referralID int, 
		itemGroupUID uniqueidentifier DEFAULT NEWID()
	)

	IF OBJECT_ID('tempdb..#tmpModestMeansPanelTbl') IS NOT NULL 
		EXEC('DROP TABLE  #tmpModestMeansPanelTbl')

	create table #tmpModestMeansPanelTbl (
		panelID int PRIMARY KEY,
		panelname varchar (1000)
	)

	insert into #tmpModestMeansPanelTbl
	select
		p.panelID,
		p.name
	from
		membercentral.dbo.ref_panels p
	where
		(p.name like '%modest%' or p.shortDesc like '%FAMIMM%')
		and referralID = @referralID

	insert into @surveyTbl
	select
		pst.surveyTypeID, pst.name as surveyTypeName,
		pst.surveyFreq, pst.isRetainedCase, pst.isClosedCase, pst.isModestMeans
	from
		membercentral.dbo.ref_panelSurveys ps WITH(NOLOCK) 
		inner join membercentral.dbo.cms_content c WITH(NOLOCK) on
			ps.surveyContentID = c.contentID
		inner join membercentral.dbo.cms_contentLanguages cl WITH(NOLOCK) on
			c.contentID = cl.contentID
		inner join membercentral.dbo.cms_languages l WITH(NOLOCK) on
			l.languageID = cl.languageID
		cross apply membercentral.dbo.fn_getContent(ps.surveyContentID ,l.languageID ) as sc
		inner join membercentral.dbo.ref_panels p WITH(NOLOCK) on
			p.panelID = ps.panelID
			and p.sendMail = 1
			and p.referralID = @referralID
			and p.panelParentID is null
		inner join membercentral.dbo.ref_panelSurveyTypes pst WITH(NOLOCK) on
			pst.surveyTypeID = ps.surveyTypeID
	group by
		pst.surveyTypeID, pst.name,
		pst.surveyFreq, pst.isRetainedCase, pst.isClosedCase, pst.isModestMeans

	-- ****************************************** Populate Main survey / referrals Table **********************************************************************

	set @sql = '' 
	set @loopIndex = 1
	select @surveyRecordCount =  count(surveyTypeID) from @surveyTbl
	select @surveyTypeID =  min(surveyTypeID) from @surveyTbl

	while @surveyTypeID is not null
	begin

		select 
			@surveyTypeName = surveyTypeName,
			@surveyFreq = surveyFreq,
			@isRetainedCase = isRetainedCase,
			@isClosedCase = isClosedCase,
			@isModestMeans = isModestMeans
		from 
			@surveyTbl
		where
			surveyTypeID = @surveyTypeID

		set @sql = @sql + '
			select
					cr.clientReferralID,
					c.clientID,	c.referralID, 
					c.firstName, c.middleName, c.lastName,
					c.lastName + '', '' + c.firstName as clientName,
					c.businessName,
					c.address1, c.address2,	c.address3,
					c.city,	c.state, c.postalCode, c.countryID,
					c.email,c.homePhone, c.cellPhone, c.alternatePhone,
					c.typeID, cr.statusID, 
					crs.statusName, crs.isReferred, crs.isAgency, crs.isPending,
					c.dateCreated as clientDateCreated,	c.createdBy as clientCreatedBt, c.dateLastUpdated as clientDateLastUpdated,
					c.clientParentID,
					cr.uid as clientReferralUID, cr.memberID, cr.enteredByMemberID,
					cr.sourceID, cr.counselorNotes, cr.attorneyNotes,
					cr.communicateLanguageID, cr.issueDesc,
					isNull(cr.sendSurvey, 0) as sendSurvey, isNull(cr.sendNewsBlog,0) as sendNewsBlog, 
					cr.typeID as clientReferralTypeID, 
					cr.clientReferralDate, cr.dateCreated, cr.lastUpdatedBy, cr.dateLastUpdated,
					rep.clientID as repID,	rep.firstName as repFirstName, rep.lastName as repLastName,
					rep.lastName + '', '' + rep.firstName as repName,
					rep.address1 as repAddress1, rep.address2 as repAddress2, rep.address3 as repAddress3,
					rep.city as repCity, rep.state as repState, rep.postalCode as repPostalCode, rep.countryID as repCountryID,
					rep.email as repEmail, rep.homePhone as repHomePhone, rep.cellPhone as repCellPhone, rep.alternatePhone as repAlternatePhone,
					rep.typeID as repTypeID, rep.relationToClient, rep.clientParentID as repParentID,
					m.firstName as memFirstName, m.middleName as memMiddleName, m.lastName as memLastname, m.prefix, m.suffix, m.company,
					(m.firstName  + 
					case
						when m.middlename is not null and len(m.middlename) > 0  then
							'' '' + left(m.middleName, 1) + '' ''
						else
							'' ''
					end  + m.lastName) as memberName, m.memberNumber, 
					dateDiff(day,cr.clientReferralDate,getDate()) as daysElapsed, '
				if @isRetainedCase = 1 begin
						set @sql = @sql + '
						rc.caseID, rc.enteredByMemberID as caseEnteredByMemberID, rc.notesTxt as caseNotesTxt,
						rc.caseFees, rc.dateCaseOpened, rc.dateCaseClosed,
						rc.dateCreated as dateCaseCreated, rc.dateLastUpdated as dateCaseLastUpdated,
						' 
					end
				else
					begin
						set @sql = @sql + '
							null as caseID, null as caseEnteredByMemberID, null as caseNotesTxt,
							0 as caseFees, null as dateCaseOpened, null as dateCaseClosed,
							null  as dateCaseCreated, null as dateCaseLastUpdated,	
						'					
					end			
				set @sql = @sql + '
					l.languageID, l.languageName,
					p.panelID, p.name as panelName,
					ps.surveyID, ps.name as surveyName,
					pst.surveyTypeID, pst.name as surveyTypeName,
					pst.surveyFreq, pst.isRetainedCase, pst.isClosedCase, pst.isModestMeans,
					cc.siteResourceID,cc.dateCreated as contentCreatedDt,
					cl.contentDesc, cl.	dateCreated as languageDt, cl.dateModified as languageModDt,
					lang.languageCode,
					sc.contentID, sc.isSSL, sc.isHTML, sc.enableSocialMediaSharing,
					sc.contentLanguageID, sc.contentVersionID,
					sc.contentTitle as emailSubject, sc.contentDesc as scContentDesc, sc.rawContent as emailContent
				from
					membercentral.dbo.ref_clients c WITH(NOLOCK) 
					inner join membercentral.dbo.ref_clientTypes ct WITH(NOLOCK) on
						ct.clientTypeID = c.typeID
						and ct.clientType = ''Client''
					left outer join membercentral.dbo.ref_clientReferrals cr WITH(NOLOCK) on
						cr.clientID = c.clientID
					left outer join membercentral.dbo.ref_clientReferralStatus crs WITH(NOLOCK) on
						crs.clientReferralStatusID = cr.statusID
					left outer join membercentral.dbo.ref_clients rep WITH(NOLOCK) on
						rep.clientID = cr.representativeID
					left outer join membercentral.dbo.ams_members m WITH(NOLOCK) on
						m.memberid = cr.memberid
					inner join membercentral.dbo.ams_members m2 WITH(NOLOCK) on
						m2.memberid = m.activeMemberID
					inner join membercentral.dbo.organizations orgs  WITH(NOLOCK) on	 
						m2.orgID = orgs.orgid
						and orgs.orgCode = ''sdcba''
				'			

				if @isRetainedCase = 1 begin
						set @sql = @sql + '
						inner join membercentral.dbo.ref_cases rc WITH(NOLOCK) on
							rc.clientReferralID = cr.clientReferralID
						' 
					end		
				set @sql = @sql + '
					inner join membercentral.dbo.ref_languages l WITH(NOLOCK) on
						l.languageID = cr.communicateLanguageID
					cross apply
						(	select 
								case when  isNumeric(left(r.value(''(text())[1]'', ''varchar(max)''), charindex('','', r.value(''(text())[1]'', ''varchar(max)'') + '','')-1)) = 1 then
										left(r.value(''(text())[1]'', ''varchar(max)''), charindex('','', r.value(''(text())[1]'', ''varchar(max)'') + '','')-1)
										else 0
								end  as panelID,
								clientID
							from 
								search.dbo.tblSearchReferralHistory WITH(NOLOCK) 
								CROSS APPLY searchXML.nodes(''/search/field[@name=''''PANELID1'''']'') AS x(r)  
							where 
								clientID = coalesce(c.clientParentID, c.clientID)
						) as paramTbl	
					inner join membercentral.dbo.ref_panels p WITH(NOLOCK) on
						p.panelID = paramTbl.panelID
						and p.sendMail = 1
						and p.panelParentID is null
					'
				if @isRetainedCase = 1 begin
						set @sql = @sql + '
							inner join #tmpModestMeansPanelTbl mmp on
								mmp.panelID = p.panelID
						' 
					end
				set @sql = @sql + '
					inner join membercentral.dbo.ref_panelSurveys ps	WITH(NOLOCK) on
						p.panelID = ps.panelID
					inner join membercentral.dbo.ref_panelSurveyTypes pst WITH(NOLOCK) on
						pst.surveyTypeID = ps.surveyTypeID
						and pst.surveyTypeID = 1
						and pst.surveyFreq = ' +  cast(@surveyFreq as varchar) + '
					inner join membercentral.dbo.cms_content cc WITH(NOLOCK) on
						ps.surveyContentID = cc.contentID
					inner join membercentral.dbo.cms_contentLanguages cl WITH(NOLOCK) on
						cc.contentID = cl.contentID
					inner join membercentral.dbo.cms_languages lang WITH(NOLOCK) on
						lang.languageID = cl.languageID
						and lang.languageName = l.languageName
					cross apply (
							select top 1 c.contentID, 
								c.isSSL, c.isHTML, c.enableSocialMediaSharing, 
								cl.contentLanguageID, cl.languageID, cv.contentVersionID,
								isnull(cl.contentTitle,clDef.contentTitle) as contentTitle, 
								isnull(cl.contentDesc,clDef.contentDesc) as contentDesc, 
								isnull(cv.rawContent,cvDef.rawContent) as rawContent
							from membercentral.dbo.cms_content as c WITH(NOLOCK) 
								inner join membercentral.dbo.cms_siteResources sr	WITH(NOLOCK) on 
									sr.siteResourceID = c.siteResourceID 
									and c.contentid = ps.surveyContentID
								inner join membercentral.dbo.cms_siteResourceStatuses as srs WITH(NOLOCK) on 
									srs.siteResourceStatusID = sr.siteResourceStatusID 
									and srs.siteResourceStatusDesc = ''Active''
								left outer join membercentral.dbo.cms_contentLanguages as cl WITH(NOLOCK)
									inner join membercentral.dbo.cms_contentVersions as cv  WITH(NOLOCK) on 
										cv.contentLanguageID = cl.contentLanguageID 
										and cv.isActive = 1
									 on cl.contentID = c.contentID 
										and cl.languageID = lang.languageID
								left outer join membercentral.dbo.cms_contentLanguages as clDef WITH(NOLOCK)
									inner join membercentral.dbo.cms_contentVersions as cvDef  WITH(NOLOCK) on 
										cvDef.contentLanguageID = clDef.contentLanguageID 
										and cvDef.isActive = 1
								on clDef.contentID = c.contentID 
									and clDef.languageID = (select s.defaultLanguageID from membercentral.dbo.sites as s inner join membercentral.dbo.cms_content as c2 on c2.siteID = s.siteID where c2.contentID = c.contentID)
						) as sc	
					left outer join platformstats.dbo.ref_panelSurveyClientQueue historyTbl on
						historyTbl.clientReferralUID = cr.uid
						and historyTbl.surveyID = ps.surveyID
				where
					c.referralID = 1
					and (len(ltrim(rtrim(c.email))) > 0 or len(ltrim(rtrim(rep.email))) > 0)
					and historyTbl.historyID is NULL
				'
				if @isRetainedCase = 0 and @isClosedCase = 0 begin
					set @sql = @sql + '
						and dateDiff(day,cr.clientReferralDate,getDate()) = ' +  cast(@surveyFreq as varchar) 
						
				end
				if @isRetainedCase = 1 and @isClosedCase = 0 begin
					set @sql = @sql + '
						and dateDiff(day,rc.dateCaseOpened,getDate()) = ' +  cast(@surveyFreq as varchar) 
						
				end
				if @isRetainedCase = 1 and @isClosedCase = 1 begin
					set @sql = @sql + '
						and dateDiff(day,rc.dateCaseClosed,getDate()) = ' +  cast(@surveyFreq as varchar) 
						
				end	

				if @loopIndex < @surveyRecordCount begin
					set @sql = @sql + '
				union
					'
				end					

		select @surveyTypeID =  min(surveyTypeID) from @surveyTbl where surveyTypeID > @surveyTypeID
		set @loopIndex = @loopIndex + 1

	end

	if len(@sql) > 0  begin

			set @sql = '
				insert into #tmpClientSurveysTbl
				select 
					clientReferralID, clientID, referralID,	firstName,
					middleName, lastName, clientName, businessName,
					address1,address2, address3, city,
					state, postalCode, countryID, email,
					homePhone, cellPhone, alternatePhone, typeID,
					statusID, statusName, isReferred, isAgency,
					isPending, clientDateCreated, clientCreatedBt,
					clientDateLastUpdated, clientParentID, clientReferralUID,
					memberID, enteredByMemberID, sourceID, counselorNotes,
					attorneyNotes, communicateLanguageID, issueDesc,
					sendSurvey, sendNewsBlog, clientReferralTypeID, clientReferralDate,
					dateCreated, lastUpdatedBy, dateLastUpdated, repID,
					repFirstName, repLastName, repName, repAddress1,
					repAddress2, repAddress3, repCity, repState,
					repPostalCode, repCountryID, repEmail,
					repHomePhone, repCellPhone, repAlternatePhone,
					repTypeID, relationToClient, repParentID,
					memFirstName, memMiddleName, memLastname, prefix,
					suffix, company, memberName, memberNumber,
					daysElapsed, caseID, caseEnteredByMemberID, caseNotesTxt,
					caseFees, dateCaseOpened, dateCaseClosed, dateCaseCreated,
					dateCaseLastUpdated, languageID, languageName,
					panelID, panelName, surveyID, surveyName,
					surveyTypeID, surveyTypeName, surveyFreq, isRetainedCase,
					isClosedCase, isModestMeans, siteResourceID, contentCreatedDt,
					contentDesc, languageDt, languageModDt, languageCode,
					contentID, isSSL, isHTML, enableSocialMediaSharing,
					contentLanguageID, contentVersionID, emailSubject,
					scContentDesc, emailContent, NULL, NULL, 0			
				from (' + @sql + ') tmp
				order by
					lastName + '', '' + firstName, clientReferralID				
				option(recompile)'

	end

	-- select cast('<root><![CDATA[' + @sql + ']]></root>' AS XML) as sqlString

	exec(@sql)

	-- ****************************************** Populate Queue Tables **********************************************************************

	-- each org should have its own itemGroupUID
	insert into #tmpClientSurveyReferrals (
		referralID
	) 
	select 
		distinct referralID 
	from 
		#tmpClientSurveysTbl

	update 
		cs
	set 
		cs.itemGroupUID = csr.itemGroupUID
	from 
		#tmpClientSurveysTbl as cs
		inner join #tmpClientSurveyReferrals as csr on 
		csr.referralID = cs.referralID


	insert into #tmpTblQueueItems_clientSurveys(
		itemGroupUID ,
		referralID,
		clientReferralUID
	)
	select
		itemGroupUID ,
		referralID,
		clientReferralUID
	from
		#tmpClientSurveysTbl
	group by
		itemGroupUID, referralID, clientReferralUID

	update 
		cs
	set 
		cs.itemUID = tmpcs.itemUID
	from 
		#tmpClientSurveysTbl as cs
		inner join #tmpTblQueueItems_clientSurveys as tmpcs on 
			tmpcs.referralID = cs.referralID
			and tmpcs.clientReferralUID = cs.clientReferralUID

	select 
		@statusInserting =  qs.queueStatusID 
	from 
		platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on 
			qt.queueTypeID = qs.queueTypeID
	where 
		qt.queueType = 'clientSurveys'
		and qs.queueStatus = 'insertingItems'

	select 
		@statusReady =  qs.queueStatusID 
	from 
		platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on 
			qt.queueTypeID = qs.queueTypeID
	where qt.queueType = 'clientSurveys'
		and qs.queueStatus = 'readyToProcess'

	-- queue items
	insert into platformQueue.dbo.tblQueueItems_clientSurveys (
		itemUID, itemGroupUID, referralID, clientReferralUID
	)
		OUTPUT inserted.itemUID, @dateAdded, @dateAdded, @statusInserting 
		INTO platformQueue.dbo.tblQueueItems(itemUID, dateAdded, dateUpdated, queueStatusID)
	select 
		distinct itemUID, itemGroupUID, referralID, clientReferralUID
	from 
		#tmpTblQueueItems_clientSurveys

	insert into platformQueue.dbo.tblQueueItems_clientSurveysDetail 
	select
		itemUID, clientReferralID, clientID, referralID, firstName,
		middleName, lastName, clientName, businessName,
		address1,address2, address3, city,
		state, postalCode, countryID, email,
		homePhone, cellPhone, alternatePhone, typeID,
		statusID, statusName, isReferred, isAgency,
		isPending, clientDateCreated, clientCreatedBt,
		clientDateLastUpdated, clientParentID, clientReferralUID,
		memberID, enteredByMemberID, sourceID, counselorNotes,
		attorneyNotes, communicateLanguageID, issueDesc,
		sendSurvey, sendNewsBlog, clientReferralTypeID, clientReferralDate,
		dateCreated, lastUpdatedBy, dateLastUpdated, repID,
		repFirstName, repLastName, repName, repAddress1,
		repAddress2, repAddress3, repCity, repState,
		repPostalCode, repCountryID, repEmail,
		repHomePhone, repCellPhone, repAlternatePhone,
		repTypeID, relationToClient, repParentID,
		memFirstName, memMiddleName, memLastname, prefix,
		suffix, company, memberName, memberNumber,
		daysElapsed, caseID, caseEnteredByMemberID, caseNotesTxt,
		caseFees, dateCaseOpened, dateCaseClosed, dateCaseCreated,
		dateCaseLastUpdated, languageID, languageName,
		panelID, panelName, surveyID, surveyName,
		surveyTypeID, surveyTypeName, surveyFreq, isRetainedCase,
		isClosedCase, isModestMeans, siteResourceID, contentCreatedDt,
		contentDesc, languageDt, languageModDt, languageCode,
		contentID, isSSL, isHTML, enableSocialMediaSharing,
		contentLanguageID, contentVersionID, emailSubject,
		scContentDesc, emailContent, emailSent
	from 
		#tmpClientSurveysTbl

	-- update queue item groups to show ready to process
	update 
		qi WITH (UPDLOCK, HOLDLOCK)
	set 
		qi.queueStatusID = @statusReady,
		dateUpdated = getdate()
	from 
		platformQueue.dbo.tblQueueItems as qi
		inner join #tmpClientSurveysTbl as i on 
		i.itemUID = qi.itemUID

	-- Add survey - client history for avoiding dups in the future	
	insert into platformstats.dbo.ref_panelSurveyClientQueue (
		surveyID, clientReferralUID, dateCreated
	)
	select
		distinct surveyID, clientReferralUID,  @dateAdded
	from
		#tmpClientSurveysTbl	
	

	IF @@TRANCOUNT > 0 COMMIT TRAN

	IF OBJECT_ID('tempdb..#tmpModestMeansPanelTbl') IS NOT NULL 
		EXEC('DROP TABLE  #tmpModestMeansPanelTbl')

	IF OBJECT_ID('tempdb..#tmpClientSurveysTbl') IS NOT NULL 
		EXEC('DROP TABLE  #tmpClientSurveysTbl')

	IF OBJECT_ID('tempdb..#tmpTblQueueItems_clientSurveys') IS NOT NULL 
		EXEC('DROP TABLE  #tmpTblQueueItems_clientSurveys')

	IF OBJECT_ID('tempdb..#tmpClientSurveyReferrals') IS NOT NULL 
		EXEC('DROP TABLE  #tmpClientSurveyReferrals')

	RETURN 0
END TRY
BEGIN CATCH

	EXEC membercentral.dbo.up_errorhandler

	 IF @@TRANCOUNT > 0 ROLLBACK TRAN

	RETURN -1
END CATCH
GO

print '*** ALTER SP job_runDailyMaintenanceJobs *************************************************************************'
GO

ALTER PROC [dbo].[job_runDailyMaintenanceJobs]
AS

DECLARE @tier varchar(20), @errorSubjectRoot varchar(100), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '10.36.18.90'
IF @@SERVERNAME = 'DEV04\PLATFORM2008' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'DEV03\PLATFORM2008' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'STAGING01\PLATFORM2008' BEGIN
	SET @tier = 'STAGING'
	SET @smtpserver = 'mail.trialsmith.com'
END
SET @errorSubjectRoot = @tier + ' - Developer Needed - '


/* Recalc Date Based Virtual Group Rules */
BEGIN TRY
	EXEC dbo.ams_recalcVirtualGroupsBasedOnDateConditions
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Recalculating Date Based Conditions'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup resetPasswordRequests */
BEGIN TRY
	delete from dbo.ams_resetPasswordRequest
	where dateentered < dateadd(day,-30,getdate())
	and expire = 1

	delete from dbo.ams_resetPasswordRequest
	where dateentered < dateadd(day,-30,getdate())
	and expire = 0 
	and hasBeenUsed = 1
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of resetPasswordRequests'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Viewed Member Times */
BEGIN TRY
	delete tmp
	from dbo.ams_viewedMemberTimes as tmp
	inner join (
		select adminMemberViewedID, memRow
		from (
			select adminMemberViewedID, ROW_NUMBER() OVER (PARTITION BY adminID, viewedOrgID order by lastViewedDateTime desc) as memRow
			from dbo.ams_viewedMemberTimes
		) as innerTMP
		where memRow > 20
	) as t2 on t2.adminMemberViewedID = tmp.adminMemberViewedID
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of viewedMemberTimes'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Orphaned Member Data Content */
BEGIN TRY
	EXEC dbo.ams_cleanupOrphanedMemberDataContent
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of member data content'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Orphaned Network Profiles */
BEGIN TRY
	EXEC dbo.ams_deleteOrphanedNetworkProfiles
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of orphaned network profiles'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Orphaned Virtual Group Conditions */
BEGIN TRY
	EXEC dbo.ams_cleanupOrphanedVirtualGroupConditions
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of orphaned virtual group conditions'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Search Site Resource Cache entries */
BEGIN TRY
	delete from search.dbo.tblSearchSiteResourceCache
	where dateCreated < dateadd(day,-1,getdate())
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of Search Site Resource Cache'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup BuyNow log entries */
BEGIN TRY
	delete from dbo.buyNow_Log
	where insertDate < dateadd(day,-7,getdate())
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of BuyNow log entries'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Auto Post System Batches */
BEGIN TRY
	EXEC dbo.tr_autoPostSystemBatches
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Auto Posting System Batches'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Mark paid closed invoices as paid */
BEGIN TRY
	EXEC dbo.tr_autoMarkClosedInvoicesAsPaid
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Marking paid closed invoices as paid'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Close Pending Invoices */
BEGIN TRY
	EXEC dbo.sub_closePendingInvoices
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Closing Pending Invoices'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Close empty open invoices */
BEGIN TRY
	EXEC dbo.tr_autoCloseEmptyOpenInvoices
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Closing Empty Open Invoices'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Advance Subscription Rates */
BEGIN TRY
	EXEC dbo.sub_advanceRates
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Advancing Subscription Rates'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Expire Subscription Offers */
BEGIN TRY
	EXEC dbo.sub_expireOffers
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Expiring Subscription Offers'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Expire Subscriptions */
BEGIN TRY
	EXEC dbo.sub_expireSubscriptions
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Expiring Subscriptions'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Add autoPay Invoices to Invoice Payment Queue */
BEGIN TRY
	EXEC dbo.tr_autoPayInvoices
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Adding autoPay Invoices to Invoice Payment Queue'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* Add items to LRIS Client Surveys Queue */
BEGIN TRY
	declare @referralID int
	set @referralID = 1
	EXEC dbo.ref_addClientSurveyQueue @referralID = @referralID
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Adding items to Client Surveys Queue'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

RETURN 0

GO



