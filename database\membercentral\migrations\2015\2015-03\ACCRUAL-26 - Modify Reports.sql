use membercentral
GO

ALTER PROC [dbo].[tr_report_cashreceipt]
@orgid int,
@startdate datetime,
@enddate datetime,
@glAccountID int,
@groupBy varchar(15),
@filename varchar(400) = null

AS

-- status 3 (pending) do not appear here -- they are not accepted transactions

-- group by must be valid
IF @groupBy not in ('GLAccountID','Detail','batchDate')
	select @groupBy = 'GLAccountID'

-- set date to 11:59:59 of enddate
select @endDate = dateadd(ms,-3,dateadd(day,1,DATEADD(dd, DATEDIFF(dd,0,@endDate), 0)))

-- accounts to track
declare @tblGL TABLE (glOrder varchar(max), GLAccountID int, accountName varchar(max), accountCode varchar(200))
IF @glAccountID = 0
	insert into @tblGL (glOrder, GLAccountID, accountName, accountCode)
	SELECT gl.thePath, gl.GLAccountID, gl.thePathExpanded, gl.accountCode
	FROM dbo.fn_getRecursiveGLAccounts(@orgID) as gl
	where gl.AccountTypeID = 3
	and gl.status <> 'D'
	ORDER BY gl.thePath
ELSE
	insert into @tblGL (glOrder, GLAccountID, accountName, accountCode)
	SELECT gl.thePath, gl.GLAccountID, gl.thePathExpanded, gl.accountCode
	FROM dbo.fn_getRecursiveGLAccounts(@orgID) as gl
	where gl.AccountTypeID = 3
	and gl.status <> 'D'
	and gl.GLAccountID = @glAccountID
	ORDER BY gl.thePath

DECLARE @tblData TABLE (GLAccountID int, detail varchar(max), batchDate datetime, allocatedAmount money)
DECLARE @fullsql varchar(max)

IF @groupBy = 'GLAccountID' BEGIN
	INSERT INTO @tblData(GLAccountID, allocatedAmount)	
	select 
		case when saleDitT.typeid = 10 then saleDitT.debitGLAccountID else saleDitT.creditGLAccountID end, 
		sum(case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end) 
	from dbo.tr_transactions as allocT
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleDitT on saleDitT.transactionID = allocR.appliedToTransactionID and saleDitT.statusID = 1
	inner join dbo.tr_GLAccounts as gl on gl.GLAccountID = case when saleDitT.typeid = 10 then saleDitT.debitGLAccountID else saleDitT.creditGLAccountID end
	inner join @tblGL as tblgl on tblgl.glAccountID = gl.GLAccountID
	inner join dbo.tr_relationships as payR on payR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as payRT on payRT.typeID = payR.typeID and payRT.type = 'AllocPayTrans'
	inner join dbo.tr_transactions as payT on payT.transactionID = payR.appliedToTransactionID and payT.statusID = 1
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID and glAlloc.status <> 'D'
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4
	where allocT.ownedByOrgID = @orgid
	and allocT.typeID = 5
	and allocT.statusID = 1
	and b.depositDate between @startdate and @enddate
	group by case when saleDitT.typeid = 10 then saleDitT.debitGLAccountID else saleDitT.creditGLAccountID end

	IF @filename is not null BEGIN	
		IF OBJECT_ID('tempdb..#tmpReceiptsReportExport1') IS NOT NULL 
			DROP TABLE #tmpReceiptsReportExport1

		select gl.glOrder, gl.accountName, gl.accountCode, td.allocatedAmount
		into #tmpReceiptsReportExport1
		from @tblData as td
		inner join @tblGL as gl on gl.glaccountid = td.glaccountID

		-- sql for export
		SELECT @fullsql = '
			select accountName as [Account], accountCode as [Account Code], allocatedAmount as [Allocated Amount]
			from #tmpReceiptsReportExport1
			order by glOrder'
		EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql

		IF OBJECT_ID('tempdb..#tmpReceiptsReportExport1') IS NOT NULL 
			DROP TABLE #tmpReceiptsReportExport1
	END ELSE BEGIN
		select gl.accountName, gl.accountCode, td.allocatedAmount
		from @tblData as td
		inner join @tblGL as gl on gl.glaccountID = td.glAccountID
		order by gl.glOrder
	END
END

IF @groupBy = 'Detail' BEGIN
	INSERT INTO @tblData(detail, allocatedAmount)	
	select 
		case when saleDitT.typeID = 10 then saleAdjTaxT.detail else saleDitT.detail end,
		sum(case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end)
	from dbo.tr_transactions as allocT
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleDitT on saleDitT.transactionID = allocR.appliedToTransactionID and saleDitT.statusID = 1
	left outer join dbo.tr_relationships as ditR 
		inner join dbo.tr_relationshipTypes as ditRT on ditRT.typeID = ditR.typeID and ditRT.type = 'DITSaleTrans'
		inner join dbo.tr_transactions as saleAdjTaxT on saleAdjTaxT.transactionID = ditR.appliedToTransactionID and saleAdjTaxT.statusID = 1
		on ditR.transactionID = saleDitT.transactionID
	inner join dbo.tr_GLAccounts as gl on gl.GLAccountID = case when saleDitT.typeid = 10 then saleDitT.debitGLAccountID else saleDitT.creditGLAccountID end
	inner join @tblGL as tblgl on tblgl.glAccountID = gl.GLAccountID
	inner join dbo.tr_relationships as payR on payR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as payRT on payRT.typeID = payR.typeID and payRT.type = 'AllocPayTrans'
	inner join dbo.tr_transactions as payT on payT.transactionID = payR.appliedToTransactionID and payT.statusID = 1
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID and glAlloc.status <> 'D'
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4
	where allocT.ownedByOrgID = @orgid
	and allocT.typeID = 5
	and allocT.statusID = 1
	and b.depositDate between @startdate and @enddate
	group by case when saleDitT.typeID = 10 then saleAdjTaxT.detail else saleDitT.detail end

	IF @filename is not null BEGIN	
		IF OBJECT_ID('tempdb..#tmpReceiptsReportExport2') IS NOT NULL 
			DROP TABLE #tmpReceiptsReportExport2

		select td.detail, td.allocatedAmount
		into #tmpReceiptsReportExport2
		from @tblData as td

		-- sql for export
		SELECT @fullsql = '
			select detail as [Sale Detail], allocatedAmount as [Allocated Amount]
			from #tmpReceiptsReportExport2
			order by detail'
		EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql

		IF OBJECT_ID('tempdb..#tmpReceiptsReportExport2') IS NOT NULL 
			DROP TABLE #tmpReceiptsReportExport2
	END ELSE BEGIN
		select td.detail, td.allocatedAmount
		from @tblData as td
		order by td.detail
	END
END

IF @groupBy = 'batchDate' BEGIN
	INSERT INTO @tblData(batchDate, allocatedAmount)	
	select 
		CAST(FLOOR(CAST(b.depositDate AS FLOAT)) AS DATETIME), 
		sum(case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end)
	from dbo.tr_transactions as allocT
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleDitT on saleDitT.transactionID = allocR.appliedToTransactionID and saleDitT.statusID = 1
	inner join dbo.tr_GLAccounts as gl on gl.GLAccountID = case when saleDitT.typeid = 10 then saleDitT.debitGLAccountID else saleDitT.creditGLAccountID end
	inner join @tblGL as tblgl on tblgl.glAccountID = gl.GLAccountID
	inner join dbo.tr_relationships as payR on payR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as payRT on payRT.typeID = payR.typeID and payRT.type = 'AllocPayTrans'
	inner join dbo.tr_transactions as payT on payT.transactionID = payR.appliedToTransactionID and payT.statusID = 1
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID and glAlloc.status <> 'D'
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4
	where allocT.ownedByOrgID = @orgid
	and allocT.typeID = 5
	and allocT.statusID = 1
	and b.depositDate between @startdate and @enddate
	group by CAST(FLOOR(CAST(b.depositDate AS FLOAT)) AS DATETIME)

	IF @filename is not null BEGIN	
		IF OBJECT_ID('tempdb..#tmpReceiptsReportExport3') IS NOT NULL 
			DROP TABLE #tmpReceiptsReportExport3

		select td.batchDate, td.allocatedAmount
		into #tmpReceiptsReportExport3
		from @tblData as td

		-- sql for export
		SELECT @fullsql = '
			select batchDate as [Batch Deposit Date], allocatedAmount as [Allocated Amount]
			from #tmpReceiptsReportExport3
			order by batchDate'
		EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql

		IF OBJECT_ID('tempdb..#tmpReceiptsReportExport3') IS NOT NULL 
			DROP TABLE #tmpReceiptsReportExport3
	END ELSE BEGIN
		select td.batchDate, td.allocatedAmount
		from @tblData as td
		order by td.batchDate
	END
END

RETURN 0
GO

ALTER PROC [dbo].[tr_report_sales]
@orgid int,
@datetype varchar(15),
@startdate datetime,
@enddate datetime

AS

-- set date to 11:59:59 of enddate
select @endDate = dateadd(ms,-3,dateadd(day,1,DATEADD(dd, DATEDIFF(dd,0,@endDate), 0)))

;with allTrans AS (
	select t.creditGLAccountID as GLAccountID, t.typeid, t.amount
	from dbo.tr_transactions as t
	inner join dbo.tr_GLAccounts as gl on gl.GLAccountID = t.creditGLAccountID and gl.status <> 'D'
	inner join dbo.tr_types as tt on tt.typeID = t.typeID and tt.type in ('Sale','Sales Tax')
	where t.ownedByOrgID = @orgid
	and t.statusID = 1
	and (
		(@datetype = 'dateRecorded' and t.dateRecorded between @startdate and @endDate)
		or
		(@datetype = 'transactionDate' and t.transactionDate between @startdate and @endDate)
	)
		union all
	select t.creditGLAccountID, t.typeid, t.amount
	from dbo.tr_transactions as t
	inner join dbo.tr_GLAccounts as gl on gl.GLAccountID = t.creditGLAccountID and gl.status <> 'D'
	inner join dbo.tr_GLAccounts as gl2 on gl2.GLAccountID = t.debitGLAccountID and gl2.GLCode = 'ACCOUNTSRECEIVABLE' and gl2.status <> 'D'
	inner join dbo.tr_types as tt on tt.typeID = t.typeID and tt.type = 'Adjustment'
	where t.ownedByOrgID = @orgid
	and t.statusID = 1
	and (
		(@datetype = 'dateRecorded' and t.dateRecorded between @startdate and @endDate)
		or
		(@datetype = 'transactionDate' and t.transactionDate between @startdate and @endDate)
	)
		union all
	select t.debitGLAccountID, t.typeid, t.amount * -1
	from dbo.tr_transactions as t
	inner join dbo.tr_GLAccounts as gl on gl.GLAccountID = t.debitGLAccountID and gl.status <> 'D'
	inner join dbo.tr_GLAccounts as gl2 on gl2.GLAccountID = t.creditGLAccountID and gl2.GLCode = 'ACCOUNTSRECEIVABLE' and gl2.status <> 'D'
	inner join dbo.tr_types as tt on tt.typeID = t.typeID and tt.type = 'Adjustment'
	where t.ownedByOrgID = @orgid
	and t.statusID = 1
	and (
		(@datetype = 'dateRecorded' and t.dateRecorded between @startdate and @endDate)
		or
		(@datetype = 'transactionDate' and t.transactionDate between @startdate and @endDate)
	)
), sales AS (
	select GLAccountID, sum(amount) as saleAmount
	from allTrans
	where typeID = 1
	group by GLAccountID
), adjustments AS (
	select GLAccountID, sum(amount) as adjAmount
	from allTrans
	where typeID = 3
	group by GLAccountID
), allGLs AS (
	select GLAccountID from sales
	union
	select GLAccountID from adjustments
)
select allGLs.GLAccountID, gl.accountCode, rgl.thePathExpanded as glAccountPath, isnull(sales.saleAmount,0) as saleAmount, isnull(adjustments.adjAmount,0) as adjustmentAmount, isnull(sales.saleAmount,0) + isnull(adjustments.adjAmount,0) as total
from allGLs
left outer join sales on sales.GLAccountID = allGLs.GLAccountID
left outer join adjustments on adjustments.GLAccountID = allGLs.GLAccountID
inner join dbo.tr_GLAccounts as gl on gl.GLAccountID = allGLs.GLAccountID
inner join dbo.fn_getRecursiveGLAccounts(@orgid) as rgl on rgl.GLAccountID = gl.GLAccountID
order by rgl.thePath

RETURN 0
GO

ALTER PROC [dbo].[tr_report_sales_export]
@orgid int,
@datetype varchar(15),
@startdate datetime,
@enddate datetime,
@filename varchar(400)

AS

-- drop the temp table
IF OBJECT_ID('tempdb..#GSReport') IS NOT NULL 
	DROP TABLE #GSReport
IF OBJECT_ID('tempdb..#GSReport2') IS NOT NULL 
	DROP TABLE #GSReport2

DECLARE @fullsql varchar(max)

-- put into temp table
CREATE TABLE #GSReport (autoid int IDENTITY(1,1), GLAccountID int, AccountCode varchar(200), glAccountPath varchar(max), saleAmount money, adjustmentAmount money, total money)
INSERT INTO #GSReport (GLAccountID, AccountCode, glAccountPath, saleAmount, adjustmentAmount, total)
EXEC dbo.tr_report_sales @orgID, @datetype, @startdate, @enddate

-- sql for export
SELECT @fullsql = '
	SELECT glAccountPath as [Account], accountCode as [Account Code], saleAmount as [Sales], adjustmentAmount as [Adjustments], total as [Total]
	FROM #GSReport
	ORDER BY autoid'
EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql

-- drop the temp table
IF OBJECT_ID('tempdb..#GSReport') IS NOT NULL 
	DROP TABLE #GSReport
IF OBJECT_ID('tempdb..#GSReport2') IS NOT NULL 
	DROP TABLE #GSReport2

RETURN 0
GO

ALTER PROC [dbo].[tr_report_payment]
@orgID int,
@datetype varchar(15),
@startDate datetime,
@endDate datetime,
@profileID int,
@lowamt money,
@highamt money,
@batch varchar(400),
@pending bit,
@fld4 varchar(4),
@fld18 varchar(20),
@fld19 varchar(20)

AS

-- if pending = 0, status 3 (pending) do not appear here -- they are not accepted transactions
-- if pending = 1, status 3 appears here and not 1,2
-- status 4 (voidedpending) do not appear here -- they are meant to be completely hidden (deprecated)
-- status 2 (voided) do not appear here

if @profileID = 0
	select @profileID = null

if (@lowamt = @highamt and @highamt = 0) or @highamt = 0 begin
	set @lowamt = null
	set @highamt = null
end

declare @payStatuses TABLE (statusid int)
if @pending = 0
	insert into @payStatuses (statusid)
	select 1
else
	insert into @payStatuses (statusid)
	select 3

-- set date to 11:59:59 of enddate
if @endDate is not null
	select @endDate = dateadd(s,-1,dateadd(day,1,DATEADD(dd, DATEDIFF(dd,0,@endDate), 0)))

IF OBJECT_ID('tempdb..#tblTrans') IS NOT NULL 
	DROP TABLE #tblTrans
CREATE TABLE #tblTrans (rootTransactionID int, transactionID int, typeID int)

-- payments / refunds directly found
insert into #tblTrans (rootTransactionID, transactionID, typeid)
select t.transactionID, t.transactionID, t.typeid
from dbo.tr_transactions as t
inner join dbo.tr_transactionPayments as tp on tp.transactionID = t.transactionID
inner join dbo.tr_batchTransactions as bt on bt.transactionID = t.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID
inner join @payStatuses as ps on ps.statusID = t.statusID
inner join dbo.tr_paymentHistory as ph on ph.historyID = tp.historyID
inner join dbo.tr_GLAccounts as glPay on glPay.GLAccountID = t.debitGLAccountID and glPay.status <> 'D'
inner join dbo.tr_GLAccounts as glRef on glRef.GLAccountID = t.creditGLAccountID and glRef.status <> 'D'
where (@profileID is null or tp.profileID = @profileID)
and (@lowamt is null or t.amount between @lowamt and @highamt)
and t.ownedByOrgID = @orgID
and (
	(@datetype = 'dateRecorded' and t.dateRecorded between @startdate and @endDate)
	or
	(@datetype = 'transactionDate' and t.transactionDate between @startdate and @endDate)
)
and (@batch is null or b.batchName like '%' + @batch + '%')
and (
	@fld4 is null 
	or 
	right(ph.paymentInfo.value('(//args/fld_4_)[1]','varchar(40)'),4) = @fld4
	or
	right(t.detail,8) = '****' + @fld4
	or
	right(t.detail,8) = 'XXXX' + @fld4
	)
and (@fld18 is null or ph.paymentInfo.value('(//args/fld_18_)[1]','varchar(30)') = @fld18)
and (@fld19 is null or ph.paymentInfo.value('(//args/fld_19_)[1]','varchar(30)') = @fld19)

-- all payments tied to refunds
insert into #tblTrans (rootTransactionID, transactionID, typeid)
select tbl.rootTransactionID, tr.appliedToTransactionID, t.typeID
from #tblTrans as tbl
inner join dbo.tr_relationships as tr on tr.TransactionID = tbl.transactionID
INNER JOIN dbo.tr_relationshipTypes AS rt ON rt.typeID = tr.typeID AND rt.type = 'RefundTrans'
inner join dbo.tr_transactions as t on t.transactionID = tr.appliedToTransactionID
where tbl.typeID = 4
and t.statusID in (1,3)

-- allocations, refunds, write offs, nsf trans applied to payments
insert into #tblTrans (rootTransactionID, transactionID, typeid)
select tbl.rootTransactionID, t.transactionid, t.typeID
from #tblTrans as tbl
inner join dbo.tr_relationships as tr on tr.appliedToTransactionID = tbl.transactionID
INNER JOIN dbo.tr_relationshipTypes AS rt ON rt.typeID = tr.typeID AND rt.type in ('AllocPayTrans','RefundTrans','WriteOffPayTrans','NSFTrans')
inner join dbo.tr_transactions as t on t.transactionID = tr.transactionid
where tbl.typeID = 2
and tbl.rootTransactionID = tbl.transactionID
and t.statusID in (1,3)

declare @tblGL TABLE (GLAccountID int, accountCode varchar(200), glCode varchar(50), thePathExpanded varchar(max))
insert into @tblGL (GLAccountID, accountCode, glCode, thePathExpanded)
SELECT rgl.GLAccountID, rgl.accountCode, gl.glCode, rgl.thePathExpanded
FROM dbo.fn_getRecursiveGLAccounts(@orgID) as rgl
inner join dbo.tr_GLAccounts as gl on gl.glaccountID = rgl.glaccountID
where gl.status <> 'D'

select tmp.rootTransactionID, tt.type, t.amount, convert(varchar(10),t.transactionDate,101) as transactionDate, t.dateRecorded, 
	case when tt.type = 'Write Off' then 'Write-Off of ' + t.detail else t.detail end as Detail,
	rglDeb.thePathExpanded as DEBITACCOUNT,
	isnull(rglDeb.accountCode,'') as DEBITACCOUNTCODE,
	rglCred.thePathExpanded as CREDITACCOUNT,
	isnull(rglCred.accountCode,'') as CREDITACCOUNTCODE,
	m2.memberID as assignedToMemberID,
	m2.lastname + ', ' + m2.firstname as assignedToMember,
	m2.company as assignedToCompany,
	m2.membernumber as assignedToMemberNumber,
	'' as [Invoice Number], 
	null as [Invoice Date Billed], 
	null as [Invoice Date Due],
	case when tmp.rootTransactionID = tmp.transactionID then 1 else 0 end as isRoot
from #tblTrans as tmp
inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
inner join dbo.tr_types as tt on tt.typeID = t.typeID
inner join dbo.ams_members as m on m.memberid = t.assignedToMemberID
inner join dbo.ams_members as m2 on m2.memberid = m.activeMemberID
INNER JOIN @tblGL as rglDeb on rglDeb.GLAccountID = t.debitGLAccountID
INNER JOIN @tblGL as rglCred on rglCred.GLAccountID = t.creditGLAccountID
where tt.type <> 'Allocation'
	union all
select tmp.rootTransactionID,  
	case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then 'Deallocation' else tt.type end,
	sum(t.amount), convert(varchar(10),t.transactionDate,101), min(t.dateRecorded), 
	case 
		when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then 'Deallocation from ' + isnull(tSaleDit.detail,'')
		else 'Allocation to ' + isnull(tSaleDit.detail,'')
		end,
	case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then rglCredSale.thePathExpanded else rglDeb.thePathExpanded end,
	case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then isnull(rglCredSale.accountCode,'') else isnull(rglDeb.accountCode,'') end,
	case when rglCred.GLCode = 'ACCOUNTSRECEIVABLE' then rglCredSale.thePathExpanded else rglCred.thePathExpanded end,
	case when rglCred.GLCode = 'ACCOUNTSRECEIVABLE' then isnull(rglCredSale.accountCode,'') else isnull(rglCred.accountCode,'') end,
	mSale2.memberID, mSale2.lastname + ', ' + mSale2.firstname, mSale2.company, mSale2.membernumber,
	o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber), i.dateBilled, i.dateDue,
	0 as isRoot
from #tblTrans as tmp
inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
inner join dbo.tr_types as tt on tt.typeID = t.typeID
inner join dbo.ams_members as m on m.memberid = t.assignedToMemberID
inner join dbo.ams_members as m2 on m2.memberid = m.activeMemberID
INNER JOIN @tblGL as rglDeb on rglDeb.GLAccountID = t.debitGLAccountID
INNER JOIN @tblGL as rglCred on rglCred.GLAccountID = t.creditGLAccountID
inner join dbo.tr_relationships as rAlloc on rAlloc.transactionID = t.transactionID
inner join dbo.tr_relationshipTypes as rtAlloc on rtAlloc.typeID = rAlloc.typeID and rtAlloc.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as tSaleDit on tSaleDit.transactionID = rAlloc.appliedToTransactionID and tSaleDit.statusID = 1 and tSaleDit.typeID in (1,3,7)
inner join dbo.ams_members as mSale on mSale.memberid = tSaleDit.assignedToMemberID
inner join dbo.ams_members as mSale2 on mSale2.memberid = mSale.activeMemberID
INNER JOIN @tblGL as rglCredSale on rglCredSale.GLAccountID = tSaleDit.creditGLAccountID
inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSaleDit.transactionID
inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
inner join dbo.organizations as o on o.orgID = tSaleDit.ownedByOrgID
where tt.type = 'Allocation'
group by tmp.rootTransactionID,  
	case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then 'Deallocation' else tt.type end,
	convert(varchar(10),t.transactionDate,101), 
	case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then 'Deallocation from ' + isnull(tSaleDit.detail,'') else 'Allocation to ' + isnull(tSaleDit.detail,'') end,
	case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then rglCredSale.thePathExpanded else rglDeb.thePathExpanded end,
	case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then isnull(rglCredSale.accountCode,'') else isnull(rglDeb.accountCode,'') end,
	case when rglCred.GLCode = 'ACCOUNTSRECEIVABLE' then rglCredSale.thePathExpanded else rglCred.thePathExpanded end,
	case when rglCred.GLCode = 'ACCOUNTSRECEIVABLE' then isnull(rglCredSale.accountCode,'') else isnull(rglCred.accountCode,'') end,
	mSale2.memberID, mSale2.lastname + ', ' + mSale2.firstname, mSale2.company, mSale2.membernumber,
	o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber), i.dateBilled, i.dateDue
	union all
select tmp.rootTransactionID,  
	case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then 'Deallocation' else tt.type end,
	sum(t.amount), convert(varchar(10),t.transactionDate,101), min(t.dateRecorded), 
	case 
		when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then 'Deallocation from ' + isnull(tSaleAdj.detail,'')
		else 'Allocation to ' + isnull(tSaleAdj.detail,'')
		end,
	case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then rglCredSale.thePathExpanded else rglDeb.thePathExpanded end,
	case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then isnull(rglCredSale.accountCode,'') else isnull(rglDeb.accountCode,'') end,
	case when rglCred.GLCode = 'ACCOUNTSRECEIVABLE' then rglCredSale.thePathExpanded else rglCred.thePathExpanded end,
	case when rglCred.GLCode = 'ACCOUNTSRECEIVABLE' then isnull(rglCredSale.accountCode,'') else isnull(rglCred.accountCode,'') end,
	mSale2.memberID, mSale2.lastname + ', ' + mSale2.firstname, mSale2.company, mSale2.membernumber,
	o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber), i.dateBilled, i.dateDue,
	0 as isRoot
from #tblTrans as tmp
inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
inner join dbo.tr_types as tt on tt.typeID = t.typeID
inner join dbo.ams_members as m on m.memberid = t.assignedToMemberID
inner join dbo.ams_members as m2 on m2.memberid = m.activeMemberID
INNER JOIN @tblGL as rglDeb on rglDeb.GLAccountID = t.debitGLAccountID
INNER JOIN @tblGL as rglCred on rglCred.GLAccountID = t.creditGLAccountID
inner join dbo.tr_relationships as rAlloc on rAlloc.transactionID = t.transactionID
inner join dbo.tr_relationshipTypes as rtAlloc on rtAlloc.typeID = rAlloc.typeID and rtAlloc.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as tSaleDit on tSaleDit.transactionID = rAlloc.appliedToTransactionID and tSaleDit.statusID = 1
inner join dbo.ams_members as mSale on mSale.memberid = tSaleDit.assignedToMemberID
inner join dbo.ams_members as mSale2 on mSale2.memberid = mSale.activeMemberID
inner join dbo.tr_relationships as rDit on rDit.transactionID = tSaleDit.transactionID
inner join dbo.tr_relationshipTypes as rtDit on rtDit.typeID = rDit.typeID and rtDit.type = 'DITSaleTrans'
inner join dbo.tr_transactions as tSaleAdj on tSaleAdj.transactionID = rDit.appliedToTransactionID and tSaleAdj.statusID = 1
INNER JOIN @tblGL as rglCredSale on rglCredSale.GLAccountID = tSaleDit.creditGLAccountID
inner join dbo.tr_invoiceTransactions as it on it.transactionID = rDit.appliedToTransactionID
inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
inner join dbo.organizations as o on o.orgID = tSaleDit.ownedByOrgID
where tt.type = 'Allocation'
group by tmp.rootTransactionID, case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then 'Deallocation' else tt.type end,
	convert(varchar(10),t.transactionDate,101), 
	case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then 'Deallocation from ' + isnull(tSaleAdj.detail,'') else 'Allocation to ' + isnull(tSaleAdj.detail,'') end,
	case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then rglCredSale.thePathExpanded else rglDeb.thePathExpanded end,
	case when rglDeb.GLCode = 'ACCOUNTSRECEIVABLE' then isnull(rglCredSale.accountCode,'') else isnull(rglDeb.accountCode,'') end,
	case when rglCred.GLCode = 'ACCOUNTSRECEIVABLE' then rglCredSale.thePathExpanded else rglCred.thePathExpanded end,
	case when rglCred.GLCode = 'ACCOUNTSRECEIVABLE' then isnull(rglCredSale.accountCode,'') else isnull(rglCred.accountCode,'') end,
	mSale2.memberID, mSale2.lastname + ', ' + mSale2.firstname, mSale2.company, mSale2.membernumber,
	o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber), i.dateBilled, i.dateDue
order by 1, 18 desc, 5

IF OBJECT_ID('tempdb..#tblTrans') IS NOT NULL 
	DROP TABLE #tblTrans

RETURN 0
GO

ALTER PROC [dbo].[tr_report_payment_export]
@orgID int,
@datetype varchar(15),
@startDate datetime,
@endDate datetime,
@profileID int,
@lowamt money,
@highamt money,
@batch varchar(400),
@pending bit,
@fld4 varchar(4),
@fld18 varchar(20),
@fld19 varchar(20),
@filename varchar(400)

AS

-- drop the temp table
IF OBJECT_ID('tempdb..#PayReport') IS NOT NULL 
	DROP TABLE #PayReport

-- put into temp table
CREATE TABLE #PayReport (autoid int IDENTITY(1,1), rootTransactionID int,  
	[type] varchar(20), transactionDate datetime, dateRecorded datetime, assignedToMemberID int, assignedToMember varchar(max), 
	assignedToMemberNumber varchar(50), assignedToCompany varchar(200), detail varchar(max), debitAccount varchar(max), debitAccountCode varchar(200), 
	creditAccount varchar(max), creditAccountCode varchar(200), amount money, [Invoice Number] varchar(50), 
	[Invoice Date Billed] datetime, [Invoice Date Due] datetime, isRoot bit)
INSERT INTO #PayReport (rootTransactionID, [type], amount, transactionDate, dateRecorded, detail, debitAccount, debitAccountCode, 
	creditAccount, creditAccountCode, assignedToMemberID, assignedToMember, assignedToMemberNumber, assignedToCompany, [Invoice Number], 
	[Invoice Date Billed], [Invoice Date Due], isRoot)
EXEC dbo.tr_report_payment @orgID, @datetype, @startdate, @enddate, @profileID, @lowamt, @highamt, @batch, @pending, @fld4, @fld18, @fld19

-- sql for export
DECLARE @fullsql varchar(max)
SELECT @fullsql = '
	SELECT rootTransactionID as [GroupingTransactionID], convert(varchar(10),transactionDate,101) as [Transaction Date], [type] as [Type], 
		detail as [Detail], assignedToMember as [Assigned To], assignedToMemberNumber as [Member Number], assignedToCompany as [Company], 
		amount as [Amount], DebitAccount as [Debit Account], 
		DebitAccountCode as [Debit Account Code], CreditAccount as [Credit Account], 
		CreditAccountCode as [Credit Account Code], [Invoice Number], convert(varchar(10),[Invoice Date Billed],101) as [Invoice Date Billed], 
		convert(varchar(10),[Invoice Date Due],101) as [Invoice Date Due]
	FROM #PayReport
	ORDER BY autoid'
EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql

-- drop the temp table
IF OBJECT_ID('tempdb..#PayReport') IS NOT NULL 
	DROP TABLE #PayReport

RETURN 0
GO


ALTER PROC [dbo].[tr_report_transactions]
@orgid int,
@datetype varchar(15),
@startdate datetime,
@enddate datetime,
@typeID int,
@voided bit

AS

/*
if voided = 0, show statusID 1 only.
	-- status 2 (voided) do not appear here.
	-- status 3 (pending) do not appear here -- they are not accepted transactions
		-- do not show allocations to pending payments.
	-- status 4 (voidedpending) do not appear here -- they are meant to be completely hidden (deprecated)
if voided = 1, show statusID 2 only.
	-- status 1 (active) do not appear here.
	-- status 3 (pending) do not appear here -- they are not accepted transactions
	-- status 4 (voidedpending) do not appear here -- they are meant to be completely hidden (deprecated)
*/

-- set date to 11:59:59 of enddate
declare @realEndDate datetime
select @realEndDate = dateadd(s,-1,dateadd(day,1,DATEADD(dd, DATEDIFF(dd,0,@endDate), 0)))

IF @voided = 0
	SELECT t.transactionID, t.transactionDate, null as voidedDate, 
		case 
			when tt.type = 'Allocation' and glDeb.GLCode <> 'DEPOSITS' then 'Deallocation'
			else tt.type 
			end as [type],
		m2.memberID as assignedToMemberID, m2.membernumber as assignedToMemberNumber, 
		m2.lastname + ', ' + m2.firstname as assignedToMember, m2.company as assignedToCompany, 
		case 
			when tt.type = 'Allocation' and glDeb.GLCode <> 'DEPOSITS' then 'Deallocation of Payment'
			when tt.type = 'Allocation' then 'Allocation of Payment' 
			when tt.type = 'Deferred Transfer' then 'Deferred Transfer of Revenue' 
			when tt.type = 'Write Off' then 'Write-Off of ' + t.detail
			when tt.type = 'NSF' then 'NSF of ' + t.detail
			else t.detail 
			end as Detail,
		rglDeb.thePathExpanded as DEBITACCOUNT,
		glDeb.accountCode as DEBITACCOUNTCODE,
		rglCred.thePathExpanded as CREDITACCOUNT,
		glCred.accountCode as CREDITACCOUNTCODE,
		t.amount
	FROM dbo.tr_transactions as t
	INNER JOIN dbo.tr_types as tt on tt.typeID = t.typeID
		AND t.ownedByOrgID = @orgID
		AND t.statusID = case when @voided = 0 then 1 else 2 end
		and (
			(@datetype = 'dateRecorded' and t.dateRecorded between @startdate and @realEndDate)
			or
			(@datetype = 'transactionDate' and t.transactionDate between @startdate and @realEndDate)
		)
		and t.typeID not in (8)
		and (@typeID is null or t.typeID = @typeid)
	INNER JOIN dbo.ams_members as m on m.memberid = t.assignedToMemberID
	INNER JOIN dbo.ams_members as m2 on m2.memberid = m.activeMemberID
	INNER JOIN dbo.organizations as o on o.orgID = m2.orgID
	INNER JOIN dbo.tr_GLAccounts as glDeb on glDeb.GLAccountID = t.debitGLAccountID and glDeb.status <> 'D'
	INNER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) as rglDeb on rglDeb.GLAccountID = glDeb.GLAccountID
	INNER JOIN dbo.tr_GLAccounts as glCred on glCred.GLAccountID = t.creditGLAccountID and glCred.status <> 'D'
	INNER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) as rglCred on rglCred.GLAccountID = glCred.GLAccountID
	LEFT OUTER JOIN dbo.tr_relationships as tr 
		INNER JOIN dbo.tr_relationshipTypes AS rt ON rt.typeID = tr.typeID AND rt.type = 'AllocPayTrans'
		INNER JOIN dbo.tr_transactions as tPay on tPay.transactionID = tr.appliedToTransactionID
		on tr.transactionID = t.transactionID
	WHERE 1 = case
		when tt.type <> 'Allocation' then 1
		when tPay.statusID = 1 then 1
		else 0	
		end
	order by t.transactionDate desc, t.transactionid desc

IF @voided = 1 and @datetype = 'transactionDate'
	SELECT t.transactionID, t.transactionDate, tVO.dateRecorded as voidedDate, 
		case 
			when tt.type = 'Allocation' and glDeb.GLCode <> 'DEPOSITS' then 'Deallocation'
			else tt.type 
			end as [type],
		m2.memberID as assignedToMemberID, m2.membernumber as assignedToMemberNumber, 
		m2.lastname + ', ' + m2.firstname as assignedToMember, m2.company as assignedToCompany, 
		case 
			when tt.type = 'Allocation' and glDeb.GLCode <> 'DEPOSITS' then 'Deallocation of Payment'
			when tt.type = 'Allocation' then 'Allocation of Payment' 
			when tt.type = 'Deferred Transfer' then 'Deferred Transfer of Revenue' 
			when tt.type = 'Write Off' then 'Write-Off of ' + t.detail
			when tt.type = 'NSF' then 'NSF of ' + t.detail
			else t.detail 
			end as Detail,
		rglDeb.thePathExpanded as DEBITACCOUNT,
		glDeb.accountCode as DEBITACCOUNTCODE,
		rglCred.thePathExpanded as CREDITACCOUNT,
		glCred.accountCode as CREDITACCOUNTCODE,
		t.amount
	FROM dbo.tr_transactions as t
	INNER JOIN dbo.tr_types as tt on tt.typeID = t.typeID
		AND t.ownedByOrgID = @orgID
		AND t.statusID = 2
		and (
			(@datetype = 'dateRecorded' and t.dateRecorded between @startdate and @realEndDate)
			or
			(@datetype = 'transactionDate' and t.transactionDate between @startdate and @realEndDate)
		)
		and t.typeID not in (8)
		and (@typeID is null or t.typeID = @typeid)
	INNER JOIN dbo.ams_members as m on m.memberid = t.assignedToMemberID
	INNER JOIN dbo.ams_members as m2 on m2.memberid = m.activeMemberID
	INNER JOIN dbo.organizations as o on o.orgID = m2.orgID
	INNER JOIN dbo.tr_GLAccounts as glDeb on glDeb.GLAccountID = t.debitGLAccountID and glDeb.status <> 'D'
	INNER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) as rglDeb on rglDeb.GLAccountID = glDeb.GLAccountID
	INNER JOIN dbo.tr_GLAccounts as glCred on glCred.GLAccountID = t.creditGLAccountID and glCred.status <> 'D'
	INNER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) as rglCred on rglCred.GLAccountID = glCred.GLAccountID
	INNER JOIN dbo.tr_relationships as trVO on trVO.appliedToTransactionID =  t.transactionID
	INNER JOIN dbo.tr_relationshipTypes AS rtVO ON rtVO.typeID = trVO.typeID AND rtVO.type = 'OffsetTrans'
	INNER JOIN dbo.tr_transactions as tVO on tVO.transactionID = trVO.transactionID
	LEFT OUTER JOIN dbo.tr_relationships as tr 
		INNER JOIN dbo.tr_relationshipTypes AS rt ON rt.typeID = tr.typeID AND rt.type = 'AllocPayTrans'
		INNER JOIN dbo.tr_transactions as tPay on tPay.transactionID = tr.appliedToTransactionID
		on tr.transactionID = t.transactionID
	WHERE 1 = case
		when tt.type <> 'Allocation' then 1
		when tPay.statusID = 1 then 1
		else 0	
		end
	order by t.transactionDate desc, t.transactionid desc

IF @voided = 1 and @datetype = 'voidedDate'
	SELECT t.transactionID, t.transactionDate, tVO.dateRecorded as voidedDate, 
		case 
			when tt.type = 'Allocation' and glDeb.GLCode <> 'DEPOSITS' then 'Deallocation'
			else tt.type 
			end as [type],
		m2.memberID as assignedToMemberID, m2.membernumber as assignedToMemberNumber, 
		m2.lastname + ', ' + m2.firstname as assignedToMember, m2.company as assignedToCompany, 
		case 
			when tt.type = 'Allocation' and glDeb.GLCode <> 'DEPOSITS' then 'Deallocation of Payment'
			when tt.type = 'Allocation' then 'Allocation of Payment' 
			when tt.type = 'Deferred Transfer' then 'Deferred Transfer of Revenue' 
			when tt.type = 'Write Off' then 'Write-Off of ' + t.detail
			when tt.type = 'NSF' then 'NSF of ' + t.detail
			else t.detail 
			end as Detail,
		rglDeb.thePathExpanded as DEBITACCOUNT,
		glDeb.accountCode as DEBITACCOUNTCODE,
		rglCred.thePathExpanded as CREDITACCOUNT,
		glCred.accountCode as CREDITACCOUNTCODE,
		t.amount
	FROM dbo.tr_transactions as t
	INNER JOIN dbo.tr_types as tt on tt.typeID = t.typeID
		AND t.ownedByOrgID = @orgID
		AND t.statusID = 2
		and t.typeID not in (8)
		and (@typeID is null or t.typeID = @typeid)
	INNER JOIN dbo.ams_members as m on m.memberid = t.assignedToMemberID
	INNER JOIN dbo.ams_members as m2 on m2.memberid = m.activeMemberID
	INNER JOIN dbo.organizations as o on o.orgID = m2.orgID
	INNER JOIN dbo.tr_GLAccounts as glDeb on glDeb.GLAccountID = t.debitGLAccountID and glDeb.status <> 'D'
	INNER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) as rglDeb on rglDeb.GLAccountID = glDeb.GLAccountID
	INNER JOIN dbo.tr_GLAccounts as glCred on glCred.GLAccountID = t.creditGLAccountID and glCred.status <> 'D'
	INNER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) as rglCred on rglCred.GLAccountID = glCred.GLAccountID
	INNER JOIN dbo.tr_relationships as trVO on trVO.appliedToTransactionID =  t.transactionID
	INNER JOIN dbo.tr_relationshipTypes AS rtVO ON rtVO.typeID = trVO.typeID AND rtVO.type = 'OffsetTrans'
	INNER JOIN dbo.tr_transactions as tVO on tVO.transactionID = trVO.transactionID
		and tVO.dateRecorded between @startdate and @realEndDate
	LEFT OUTER JOIN dbo.tr_relationships as tr 
		INNER JOIN dbo.tr_relationshipTypes AS rt ON rt.typeID = tr.typeID AND rt.type = 'AllocPayTrans'
		INNER JOIN dbo.tr_transactions as tPay on tPay.transactionID = tr.appliedToTransactionID
		on tr.transactionID = t.transactionID
	WHERE 1 = case
		when tt.type <> 'Allocation' then 1
		when tPay.statusID = 1 then 1
		else 0	
		end
	order by t.transactionDate desc, t.transactionid desc

RETURN 0
GO


ALTER PROC [dbo].[tr_report_transactions_export]
@orgid int,
@datetype varchar(15),
@startdate datetime,
@enddate datetime,
@typeID int,
@voided bit,
@filename varchar(400),
@incMD bit

AS

-- drop the temp table
IF OBJECT_ID('tempdb..#TransReport') IS NOT NULL 
	DROP TABLE #TransReport
IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
	DROP TABLE ##tmpMembers

-- put into temp table
CREATE TABLE #TransReport (autoid int IDENTITY(1,1), transactionID int, transactionDate datetime, voidedDate datetime, [type] varchar(20), 
	assignedToMemberID int, assignedToMemberNumber varchar(50), assignedToMember varchar(130), assignedToCompany varchar(200), 
	detail varchar(max), DEBITACCOUNT varchar(max), DebitAccountCode varchar(200), CreditACCOUNT varchar(max), CreditAccountCode varchar(200), amount money)
INSERT INTO #TransReport (transactionID, transactionDate, voidedDate, [type], assignedToMemberID, assignedToMemberNumber, assignedToMember, assignedToCompany, detail, DebitAccount, DebitAccountCode, creditAccount, creditAccountCode, amount)
EXEC dbo.tr_report_transactions @orgID, @datetype, @startdate, @enddate, @typeID, @voided

-- put member data into temp table
IF @incMD = 1 BEGIN
	declare @memList varchar(max)
	select @memList = COALESCE(@memList + ',', '') + cast(assignedToMemberID as varchar(10)) FROM #TransReport
	declare @MDsql varchar(max)
	EXEC dbo.ams_getFlattenedMemberDataSQL @orgID=@orgID, @importDataMode=0, @memberIDList=@memList, @sql=@MDsql OUTPUT
	select @MDsql = stuff(@MDsql, charIndex('from membercentral',@MDsql), len('from membercentral'), 'into ##tmpMembers from membercentral')
	EXEC(@MDsql)
END

-- sql for export
DECLARE @fullsql varchar(max)
SELECT @fullsql = '
	SELECT r.transactionID as [Transaction ID], ' + 
		case when @voided = 1 then '''VOIDED'' as Status, ' else '' end + '
		r.transactionDate as [Date of Transaction], ' + 
		case when @voided = 1 then 'r.voidedDate as [Date Voided], ' else '' end + '
		r.type as [Transaction Type], r.detail as [Detail], 
		r.DebitAccount as [Debit Account], r.DebitAccountCode as [Debit Account Code], 
		r.CreditAccount as [Credit Account], r.CreditAccountCode as [Credit Account Code],
		r.amount as [Amount]'
IF @incMD = 1 BEGIN
	SELECT @fullsql = @fullsql + ', m.*
		FROM #TransReport as r
		INNER JOIN ##tmpMembers as m ON m.MemberCentralID = r.assignedToMemberID
		ORDER BY r.autoid'
END ELSE BEGIN
	SELECT @fullsql = @fullsql + ', r.assignedToMemberNumber, r.assignedToMember, r.assignedToCompany
		FROM #TransReport as r
		ORDER BY r.autoid'
END

-- export
EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql

-- drop the temp table
IF OBJECT_ID('tempdb..#TransReport') IS NOT NULL 
	DROP TABLE #TransReport
IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
	DROP TABLE ##tmpMembers

RETURN 0
GO

ALTER PROC [dbo].[tr_report_baseAR]
@orgid int,
@asOfDate datetime,
@startdate datetime = null,
@sumBy varchar(5) = null

AS

/*
The intent of this procedure is to generate the transactions with AR based on a given date.
It is meant to be used with both the AR report and the Invoice Aging report.
It is not meant to be used outside of these reports but that may change.

The report identifies the Gross AR (all the transactions up to the selected date) 
and then all offsetting AR entries, as in this:
+ sales with sale dateBilled earlier than startDate
+ tax with tax dateBilled earlier than startDate
+ postive adjustments with adjustment dateBilled earlier than startDate
- negative adjustments with adjustment dateBilled earlier than startDate
- voidoffsets of sales with void dateBilled earlier than startDate
- voidoffsets of postive adjustments with void dateBilled earlier than startDate
+ voidoffsets of negative adjustments with void dateBilled earlier than startDate
- allocations with allocation batch date earlier than startDate
+ deallocations with deallocation batch date earlier than startDate
- write offs with write off transactionDate earlier than startDate
+ voidoffsets of allocations with void batch date earlier than startDate
- voidoffsets of deallocations with void batch date earlier than startDate
+ voidoffsets of write offs with void transactionDate earlier than startDate
*/

-- sum by must be valid
IF @sumBy not in ('none','gl','inv','invgl')
	select @sumBy = 'none'

-- set date to 11:59:59 of asOfDate
select @asOfDate = dateadd(ms,-3,dateadd(day,1,DATEADD(dd,DATEDIFF(dd,0,@asOfDate),0)))


-- Get Pool of invoices to consider: 
-- 1) BILLED DATE on or before asOfDate
-- 2) invoice is not currently OPEN.
IF OBJECT_ID('tempdb..#tmpINV') IS NOT NULL 
	DROP TABLE #tmpINV
CREATE TABLE #tmpINV (invoiceID int PRIMARY KEY, memberID int, dateBilled datetime);
insert into #tmpINV
select i.invoiceID, m2.memberID, i.dateBilled
from dbo.tr_invoices as i
inner join dbo.ams_members as m on m.memberID = i.assignedToMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
where m.orgID = @orgID
and i.dateBilled <= @asOfDate
and i.statusID <> 1


IF OBJECT_ID('tempdb..#allRevTrans') IS NOT NULL 
	DROP TABLE #allRevTrans
CREATE TABLE #allRevTrans (invoiceID int, revenueGLAccountID int, startAmount money, endAmount money)

-- all sales/tax 
insert into #allRevTrans
select i.invoiceID, t.creditGLAccountID, case when @startdate is not null and i.dateBilled < @startdate then t.amount else 0 end, t.amount
from dbo.tr_transactions as t
inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
inner join #tmpINV as i on i.invoiceID = it.invoiceID
where t.typeID in (1,7)

-- all postive adj 
insert into #allRevTrans
select i.invoiceID, t.creditGLAccountID, case when @startdate is not null and i.dateBilled < @startdate then t.amount else 0 end, t.amount
from dbo.tr_transactions as t
inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
inner join #tmpINV as i on i.invoiceID = it.invoiceID
inner join dbo.tr_glaccounts as gl on gl.glaccountID = t.debitGLAccountID
where t.typeID = 3
and gl.GLCode = 'ACCOUNTSRECEIVABLE'

-- all negative adj offsets revenue
insert into #allRevTrans
select itt.invoiceID, t.debitGLAccountID, case when @startdate is not null and i.dateBilled < @startdate then r.amount*-1 else 0 end, r.amount*-1
from dbo.tr_transactions as t
inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
inner join #tmpINV as i on i.invoiceID = it.invoiceID
inner join dbo.tr_glaccounts as gl on gl.glaccountID = t.creditGLAccountID
inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustInvTrans'
inner join dbo.tr_transactions as tSalesTax on tSalesTax.transactionID = r.appliedToTransactionID and tSalesTax.typeID in (1,3,7)
inner join dbo.tr_invoiceTransactions as itt on itt.transactionID = tSalesTax.transactionID
where t.typeID = 3
and gl.GLCode = 'ACCOUNTSRECEIVABLE'

-- voids of sales/tax offsets revenue
insert into #allRevTrans
select itt.invoiceID, tV.debitGLAccountID, case when @startdate is not null and i.dateBilled < @startdate then tV.amount*-1 else 0 end, tV.amount*-1
from dbo.tr_transactions as tV 
inner join dbo.tr_invoiceTransactions as it on it.transactionID = tV.transactionID
inner join #tmpINV as i on i.invoiceID = it.invoiceID
inner join dbo.tr_relationships as r on r.transactionID = tV.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'OffsetTrans'
inner join dbo.tr_transactions as t on t.transactionID = r.appliedToTransactionID and t.typeID in (1,7)
inner join dbo.tr_invoiceTransactions as itt on itt.transactionID = t.transactionID
where tV.typeID = 8

-- voids of pos adj offsets revenue
insert into #allRevTrans
select itt.invoiceID, tV.debitGLAccountID, case when @startdate is not null and i.dateBilled < @startdate then tV.amount*-1 else 0 end, tV.amount*-1
from dbo.tr_transactions as tV 
inner join dbo.tr_invoiceTransactions as it on it.transactionID = tV.transactionID
inner join #tmpINV as i on i.invoiceID = it.invoiceID
inner join dbo.tr_relationships as r on r.transactionID = tV.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'OffsetTrans'
inner join dbo.tr_transactions as t on t.transactionID = r.appliedToTransactionID and t.typeID = 3
inner join dbo.tr_glaccounts as gl on gl.glaccountID = t.debitGLAccountID
inner join dbo.tr_invoiceTransactions as itt on itt.transactionID = t.transactionID
where tV.typeID = 8
and gl.GLCode = 'ACCOUNTSRECEIVABLE'

-- voids of neg adj 
insert into #allRevTrans
select itt.invoiceID, tV.creditGLAccountID, case when @startdate is not null and i.dateBilled < @startdate then rAdj.amount else 0 end, rAdj.amount
from dbo.tr_transactions as tV 
inner join dbo.tr_invoiceTransactions as it on it.transactionID = tV.transactionID
inner join #tmpINV as i on i.invoiceID = it.invoiceID
inner join dbo.tr_relationships as r on r.transactionID = tV.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'OffsetTrans'
inner join dbo.tr_transactions as tAdj on tAdj.transactionID = r.appliedToTransactionID and tAdj.typeID = 3
inner join dbo.tr_glaccounts as gl on gl.glaccountID = tAdj.creditGLAccountID
inner join dbo.tr_relationships as rAdj on rAdj.transactionID = tAdj.transactionID
inner join dbo.tr_relationshipTypes as rtAdj on rtAdj.typeID = rAdj.typeID and rtAdj.type = 'AdjustInvTrans'
inner join dbo.tr_transactions as tSalesTaxAdj on tSalesTaxAdj.transactionID = rAdj.appliedToTransactionID and tSalesTaxAdj.typeID in (1,3,7)
inner join dbo.tr_invoiceTransactions as itt on itt.transactionID = tSalesTaxAdj.transactionID
where tV.typeID = 8
and gl.GLCode = 'ACCOUNTSRECEIVABLE'

-- allocation to invoiceable items (sales/tax/adj)
insert into #allRevTrans
select it.invoiceID, tSalesTaxAdjDit.creditGLAccountID, case when @startdate is not null and b.depositDate < @startdate then tAlloc.amount else 0 end *-1, tAlloc.amount*-1
from dbo.tr_transactions as tAlloc 
inner join dbo.tr_batchTransactions as bt on bt.transactionID = tAlloc.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID
inner join dbo.tr_glaccounts as gl on gl.glaccountID = tAlloc.creditGLAccountID
inner join dbo.tr_relationships as rAlloc on rAlloc.transactionID = tAlloc.transactionID
inner join dbo.tr_relationshipTypes as rtAlloc on rtAlloc.typeID = rAlloc.typeID and rtAlloc.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as tSalesTaxAdjDit on tSalesTaxAdjDit.transactionID = rAlloc.appliedToTransactionID
inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSalesTaxAdjDit.transactionID
where tAlloc.ownedByOrgID = @orgID
and tAlloc.typeID = 5
and b.depositDate <= @asOfDate
and gl.glCode = 'ACCOUNTSRECEIVABLE'

-- allocation to non-invoiceable items (dit)
insert into #allRevTrans
select it.invoiceID, tSalesTaxAdjDit.debitGLAccountID, case when @startdate is not null and b.depositDate < @startdate then tAlloc.amount else 0 end *-1, tAlloc.amount*-1
from dbo.tr_transactions as tAlloc 
inner join dbo.tr_batchTransactions as bt on bt.transactionID = tAlloc.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID
inner join dbo.tr_glaccounts as gl on gl.glaccountID = tAlloc.creditGLAccountID
inner join dbo.tr_relationships as rAlloc on rAlloc.transactionID = tAlloc.transactionID
inner join dbo.tr_relationshipTypes as rtAlloc on rtAlloc.typeID = rAlloc.typeID and rtAlloc.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as tSalesTaxAdjDit on tSalesTaxAdjDit.transactionID = rAlloc.appliedToTransactionID
inner join dbo.tr_relationships as rDit on rDit.transactionID = tSalesTaxAdjDit.transactionID
inner join dbo.tr_relationshipTypes as rtDit on rtDit.typeID = rDit.typeID and rtDit.type = 'DITSaleTrans'
inner join dbo.tr_invoiceTransactions as it on it.transactionID = rDit.appliedToTransactionID
where tAlloc.ownedByOrgID = @orgID
and tAlloc.typeID = 5
and b.depositDate <= @asOfDate
and gl.glCode = 'ACCOUNTSRECEIVABLE'

-- deallocation to invoiceable items (sales/tax/adj) offsets alloc
insert into #allRevTrans
select it.invoiceID, tSalesTaxAdjDit.creditGLAccountID, case when @startdate is not null and b.depositDate < @startdate then tAlloc.amount*-1 else 0 end *-1, tAlloc.amount
from dbo.tr_transactions as tAlloc 
inner join dbo.tr_batchTransactions as bt on bt.transactionID = tAlloc.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID
inner join dbo.tr_glaccounts as gl on gl.glaccountID = tAlloc.debitGLAccountID
inner join dbo.tr_relationships as rAlloc on rAlloc.transactionID = tAlloc.transactionID
inner join dbo.tr_relationshipTypes as rtAlloc on rtAlloc.typeID = rAlloc.typeID and rtAlloc.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as tSalesTaxAdjDit on tSalesTaxAdjDit.transactionID = rAlloc.appliedToTransactionID
inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSalesTaxAdjDit.transactionID
where tAlloc.ownedByOrgID = @orgID
and tAlloc.typeID = 5
and b.depositDate <= @asOfDate
and gl.glCode = 'ACCOUNTSRECEIVABLE'

-- deallocation to non-invoiceable items (dit) offsets alloc
insert into #allRevTrans
select it.invoiceID, tSalesTaxAdjDit.debitGLAccountID, case when @startdate is not null and b.depositDate < @startdate then tAlloc.amount*-1 else 0 end *-1, tAlloc.amount
from dbo.tr_transactions as tAlloc 
inner join dbo.tr_batchTransactions as bt on bt.transactionID = tAlloc.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID
inner join dbo.tr_glaccounts as gl on gl.glaccountID = tAlloc.debitGLAccountID
inner join dbo.tr_relationships as rAlloc on rAlloc.transactionID = tAlloc.transactionID
inner join dbo.tr_relationshipTypes as rtAlloc on rtAlloc.typeID = rAlloc.typeID and rtAlloc.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as tSalesTaxAdjDit on tSalesTaxAdjDit.transactionID = rAlloc.appliedToTransactionID
inner join dbo.tr_relationships as rDit on rDit.transactionID = tSalesTaxAdjDit.transactionID
inner join dbo.tr_relationshipTypes as rtDit on rtDit.typeID = rDit.typeID and rtDit.type = 'DITSaleTrans'
inner join dbo.tr_invoiceTransactions as it on it.transactionID = rDit.appliedToTransactionID
where tAlloc.ownedByOrgID = @orgID
and tAlloc.typeID = 5
and b.depositDate <= @asOfDate
and gl.glCode = 'ACCOUNTSRECEIVABLE'

-- write off
insert into #allRevTrans
select it.invoiceID, tSalesTaxAdj.creditGLAccountID, case when @startdate is not null and tWO.transactionDate < @startdate then tWO.amount else 0 end *-1, tWO.amount*-1
from dbo.tr_transactions as tWO 
inner join dbo.tr_glaccounts as gl on gl.glaccountID = tWO.creditGLAccountID
inner join dbo.tr_relationships as rWO on rWO.transactionID = tWO.transactionID
inner join dbo.tr_relationshipTypes as rtWO on rtWO.typeID = rWO.typeID and rtWO.type = 'WriteOffSaleTrans'
inner join dbo.tr_transactions as tSalesTaxAdj on tSalesTaxAdj.transactionID = rWO.appliedToTransactionID
inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSalesTaxAdj.transactionID
where tWO.ownedByOrgID = @orgID
and tWO.typeID = 6
and tWO.transactionDate <= @asOfDate
and gl.glCode = 'ACCOUNTSRECEIVABLE'

-- void of allocation of invoiceable items (sales/tax/adj) offsets alloc
insert into #allRevTrans
select it.invoiceID, tSalesTaxAdjDit.creditGLAccountID, case when @startdate is not null and b.depositDate < @startdate then tV.amount*-1 else 0 end *-1, tV.amount
from dbo.tr_transactions as tV 
inner join dbo.tr_batchTransactions as bt on bt.transactionID = tV.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID
inner join dbo.tr_relationships as rV on rV.transactionID = tV.transactionID
inner join dbo.tr_relationshipTypes as rtV on rtV.typeID = rV.typeID and rtV.type = 'OffsetTrans'
inner join dbo.tr_transactions as tAlloc on tAlloc.transactionID = rV.appliedToTransactionID and tAlloc.typeID = 5
inner join dbo.tr_glaccounts as gl on gl.glaccountID = tAlloc.creditGLAccountID
inner join dbo.tr_relationships as rAlloc on rAlloc.transactionID = tAlloc.transactionID
inner join dbo.tr_relationshipTypes as rtAlloc on rtAlloc.typeID = rAlloc.typeID and rtAlloc.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as tSalesTaxAdjDit on tSalesTaxAdjDit.transactionID = rAlloc.appliedToTransactionID
inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSalesTaxAdjDit.transactionID
where tV.ownedByOrgID = @orgID
and tV.typeID = 8
and b.depositDate <= @asOfDate
and gl.GLCode = 'ACCOUNTSRECEIVABLE'

-- void of allocation of non-invoiceable items (dit) offsets alloc
insert into #allRevTrans
select it.invoiceID, tSalesTaxAdjDit.debitGLAccountID, case when @startdate is not null and b.depositDate < @startdate then tV.amount*-1 else 0 end *-1, tV.amount
from dbo.tr_transactions as tV 
inner join dbo.tr_batchTransactions as bt on bt.transactionID = tV.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID
inner join dbo.tr_relationships as rV on rV.transactionID = tV.transactionID
inner join dbo.tr_relationshipTypes as rtV on rtV.typeID = rV.typeID and rtV.type = 'OffsetTrans'
inner join dbo.tr_transactions as tAlloc on tAlloc.transactionID = rV.appliedToTransactionID and tAlloc.typeID = 5
inner join dbo.tr_glaccounts as gl on gl.glaccountID = tAlloc.creditGLAccountID
inner join dbo.tr_relationships as rAlloc on rAlloc.transactionID = tAlloc.transactionID
inner join dbo.tr_relationshipTypes as rtAlloc on rtAlloc.typeID = rAlloc.typeID and rtAlloc.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as tSalesTaxAdjDit on tSalesTaxAdjDit.transactionID = rAlloc.appliedToTransactionID
inner join dbo.tr_relationships as rDit on rDit.transactionID = tSalesTaxAdjDit.transactionID
inner join dbo.tr_relationshipTypes as rtDit on rtDit.typeID = rDit.typeID and rtDit.type = 'DITSaleTrans'
inner join dbo.tr_invoiceTransactions as it on it.transactionID = rDit.appliedToTransactionID
where tV.ownedByOrgID = @orgID
and tV.typeID = 8
and b.depositDate <= @asOfDate
and gl.GLCode = 'ACCOUNTSRECEIVABLE'

-- void of deallocation of invoiceable items (sales/tax/adj)
insert into #allRevTrans
select it.invoiceID, tSalesTaxAdjDit.creditGLAccountID, case when @startdate is not null and b.depositDate < @startdate then tV.amount else 0 end *-1, tV.amount*-1
from dbo.tr_transactions as tV 
inner join dbo.tr_batchTransactions as bt on bt.transactionID = tV.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID
inner join dbo.tr_relationships as rV on rV.transactionID = tV.transactionID
inner join dbo.tr_relationshipTypes as rtV on rtV.typeID = rV.typeID and rtV.type = 'OffsetTrans'
inner join dbo.tr_transactions as tAlloc on tAlloc.transactionID = rV.appliedToTransactionID and tAlloc.typeID = 5
inner join dbo.tr_glaccounts as gl on gl.glaccountID = tAlloc.debitGLAccountID
inner join dbo.tr_relationships as rAlloc on rAlloc.transactionID = tAlloc.transactionID
inner join dbo.tr_relationshipTypes as rtAlloc on rtAlloc.typeID = rAlloc.typeID and rtAlloc.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as tSalesTaxAdjDit on tSalesTaxAdjDit.transactionID = rAlloc.appliedToTransactionID
inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSalesTaxAdjDit.transactionID
where tV.ownedByOrgID = @orgID
and tV.typeID = 8
and b.depositDate <= @asOfDate
and gl.GLCode = 'ACCOUNTSRECEIVABLE'

-- void of deallocation of non-invoiceable items (dit)
insert into #allRevTrans
select it.invoiceID, tSalesTaxAdjDit.debitGLAccountID, case when @startdate is not null and b.depositDate < @startdate then tV.amount else 0 end *-1, tV.amount*-1
from dbo.tr_transactions as tV 
inner join dbo.tr_batchTransactions as bt on bt.transactionID = tV.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID
inner join dbo.tr_relationships as rV on rV.transactionID = tV.transactionID
inner join dbo.tr_relationshipTypes as rtV on rtV.typeID = rV.typeID and rtV.type = 'OffsetTrans'
inner join dbo.tr_transactions as tAlloc on tAlloc.transactionID = rV.appliedToTransactionID and tAlloc.typeID = 5
inner join dbo.tr_glaccounts as gl on gl.glaccountID = tAlloc.debitGLAccountID
inner join dbo.tr_relationships as rAlloc on rAlloc.transactionID = tAlloc.transactionID
inner join dbo.tr_relationshipTypes as rtAlloc on rtAlloc.typeID = rAlloc.typeID and rtAlloc.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as tSalesTaxAdjDit on tSalesTaxAdjDit.transactionID = rAlloc.appliedToTransactionID
inner join dbo.tr_relationships as rDit on rDit.transactionID = tSalesTaxAdjDit.transactionID
inner join dbo.tr_relationshipTypes as rtDit on rtDit.typeID = rDit.typeID and rtDit.type = 'DITSaleTrans'
inner join dbo.tr_invoiceTransactions as it on it.transactionID = rDit.appliedToTransactionID
where tV.ownedByOrgID = @orgID
and tV.typeID = 8
and b.depositDate <= @asOfDate
and gl.GLCode = 'ACCOUNTSRECEIVABLE'

-- void of write off offets alloc
insert into #allRevTrans
select it.invoiceID, tSalesTaxAdj.creditGLAccountID, case when tV.transactionDate < @startdate then tV.amount*-1 else 0 end *-1, tV.amount
from dbo.tr_transactions as tV 
inner join dbo.tr_relationships as rV on rV.transactionID = tV.transactionID
inner join dbo.tr_relationshipTypes as rtV on rtV.typeID = rV.typeID and rtV.type = 'OffsetTrans'
inner join dbo.tr_transactions as tWO on tWO.transactionID = rV.appliedToTransactionID and tWO.typeID = 6
inner join dbo.tr_glaccounts as gl on gl.glaccountID = tWO.creditGLAccountID
inner join dbo.tr_relationships as rWO on rWO.transactionID = tWO.transactionID
inner join dbo.tr_relationshipTypes as rtWO on rtWO.typeID = rWO.typeID and rtWO.type = 'WriteOffSaleTrans'
inner join dbo.tr_transactions as tSalesTaxAdj on tSalesTaxAdj.transactionID = rWO.appliedToTransactionID
inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSalesTaxAdj.transactionID
where tV.ownedByOrgID = @orgID
and tV.typeID = 8
and tV.transactionDate <= @asOfDate
and gl.GLCode = 'ACCOUNTSRECEIVABLE'


-- include data for warning messages (open invoices in date range)
declare @openInvoices int
select @openInvoices = count(distinct i.invoiceID)
	from dbo.tr_invoices as i
	inner join dbo.ams_members as m on m.memberID = i.assignedToMemberID
	inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
	where m.orgID = @orgID
	and i.dateBilled <= @asOfDate
	and i.statusID = 1



-- return data
IF @sumBy = 'none'
	select invoiceID, revenueGLAccountID, startAmount, endAmount, @openInvoices as openInvoices
	from #allRevTrans
	where NOT (startAmount = 0 AND endAmount = 0)
IF @sumBy = 'gl'
	select revenueGLAccountID, sum(StartAmount) as StartAmount, sum(EndAmount) as EndAmount, @openInvoices as openInvoices
	from #allRevTrans
	group by revenueGLAccountID
IF @sumBy = 'inv'
	select invoiceID, sum(EndAmount) as EndAmount, @openInvoices as openInvoices
	from #allRevTrans
	group by invoiceID
	having sum(EndAmount) > 0
IF @sumBy = 'invgl'
	select invoiceID, revenueGLAccountID, sum(EndAmount) as EndAmount, @openInvoices as openInvoices
	from #allRevTrans
	group by invoiceID, revenueGLAccountID
	having sum(EndAmount) > 0


IF OBJECT_ID('tempdb..#allRevTrans') IS NOT NULL 
	DROP TABLE #allRevTrans
IF OBJECT_ID('tempdb..#tmpINV') IS NOT NULL 
	DROP TABLE #tmpINV

RETURN 0
GO

ALTER PROC [dbo].[tr_report_MTDYTD]
@orgID int,
@endDate datetime,
@base varchar(11),
@glaccountID int,
@filename varchar(400) = null

AS

-- set end date to real end
declare @newendDate datetime
select @newendDate = dateadd(s,-1,dateadd(day,1,DATEADD(dd, DATEDIFF(dd,0,@endDate), 0)))

-- set all other dates
declare @currMTDStartDate datetime, @currMTDEndDate datetime
declare @currYTDStartDate datetime, @currYTDEndDate datetime
declare @PrevMTDStartDate datetime, @PrevMTDEndDate datetime
declare @PrevYTDStartDate datetime, @PrevYTDEndDate datetime
select @currMTDEndDate = @newendDate
select @currMTDStartDate = DATEADD(mm,DATEDIFF(mm,0,@currMTDEndDate),0)
select @currYTDEndDate = @newendDate
select @currYTDStartDate = DATEADD(yy,DATEDIFF(yy,0,@currYTDEndDate),0)
select @PrevMTDEndDate = DATEADD(yy,-1,@CurrMTDEndDate)
select @PrevMTDStartDate = DATEADD(yy,-1,@CurrMTDStartDate)
select @PrevYTDEndDate = DATEADD(yy,-1,@CurrYTDEndDate)
select @PrevYTDStartDate = DATEADD(yy,-1,@CurrYTDStartDate)

-- accounts to track
declare @tblGL TABLE (glOrder varchar(max), GLAccountID int, accountName varchar(max), accountCode varchar(200),  
	CurrMTDNetAmt money DEFAULT(0), PrevMTDNetAmt money DEFAULT(0),
	CurrYTDNetAmt money DEFAULT(0), PrevYTDNetAmt money DEFAULT(0),
	MTDDiff float DEFAULT(0), YTDDiff float DEFAULT(0))
IF @glAccountID = 0
	insert into @tblGL (glOrder, GLAccountID, accountName, accountCode)
	SELECT gl.thePath, gl.GLAccountID, gl.thePathExpanded, gl.accountCode
	FROM dbo.fn_getRecursiveGLAccounts(@orgID) as gl
	where gl.AccountTypeID = 3
	and gl.status <> 'D'
	ORDER BY gl.thePath
ELSE
	insert into @tblGL (glOrder, GLAccountID, accountName, accountCode)
	SELECT gl.thePath, gl.GLAccountID, gl.thePathExpanded, gl.accountCode
	FROM dbo.fn_getRecursiveGLAccounts(@orgID) as gl
	where gl.AccountTypeID = 3
	and gl.status <> 'D'
	and gl.GLAccountID = @glAccountID
	ORDER BY gl.thePath

/* ***** */
/* SALES */
/* ***** */
IF @base = 'sales' BEGIN

	-- net sales
	update rpt
	set rpt.CurrMTDNetAmt = sdt.CurrMTDamount,
		rpt.PrevMTDNetAmt = sdt.PrevMTDamount,
		rpt.CurrYTDNetAmt = sdt.CurrYTDamount,
		rpt.PrevYTDNetAmt = sdt.PrevYTDamount
	from @tblGL as rpt
	inner join (
		select GLAccountID, sum(currMTD) as CurrMTDamount, sum(currYTD) as CurrYTDamount,
			sum(prevMTD) as PrevMTDamount, sum(prevYTD) as PrevYTDamount
		from (
			select t.creditGLAccountID as GLAccountID,
				currMTD = case when t.transactionDate between @currMTDStartDate and @currMTDEndDate then t.amount else 0 end,
				currYTD = case when t.transactionDate between @currYTDStartDate and @currYTDEndDate then t.amount else 0 end,
				prevMTD = case when t.transactionDate between @prevMTDStartDate and @prevMTDEndDate then t.amount else 0 end,
				prevYTD = case when t.transactionDate between @prevYTDStartDate and @prevYTDEndDate then t.amount else 0 end
			from dbo.tr_transactions as t
			inner join dbo.tr_GLAccounts as gl on gl.GLAccountID = t.creditGLAccountID
			inner join @tblGL as tblgl on tblgl.glAccountID = gl.GLAccountID
			where t.ownedByOrgID = @orgid
			and t.typeID in (1,7)
			and t.statusID = 1
			and gl.status <> 'D'
			and t.transactionDate between @PrevYTDStartDate and @currYTDEndDate
				union all
			select t.creditGLAccountID, 
				currMTD = case when t.transactionDate between @currMTDStartDate and @currMTDEndDate then t.amount else 0 end,
				currYTD = case when t.transactionDate between @currYTDStartDate and @currYTDEndDate then t.amount else 0 end,
				prevMTD = case when t.transactionDate between @prevMTDStartDate and @prevMTDEndDate then t.amount else 0 end,
				prevYTD = case when t.transactionDate between @prevYTDStartDate and @prevYTDEndDate then t.amount else 0 end
			from dbo.tr_transactions as t
			inner join dbo.tr_GLAccounts as gl on gl.GLAccountID = t.creditGLAccountID and gl.status <> 'D'
			inner join @tblGL as tblgl on tblgl.glAccountID = gl.GLAccountID
			inner join dbo.tr_GLAccounts as gl2 on gl2.GLAccountID = t.debitGLAccountID and gl2.GLCode = 'ACCOUNTSRECEIVABLE' and gl2.status <> 'D'
			where t.ownedByOrgID = @orgid
			and t.typeID = 3
			and t.statusID = 1
			and t.transactionDate between @PrevYTDStartDate and @currYTDEndDate
				union all
			select t.debitGLAccountID, 
				currMTD = case when t.transactionDate between @currMTDStartDate and @currMTDEndDate then t.amount*-1 else 0 end,
				currYTD = case when t.transactionDate between @currYTDStartDate and @currYTDEndDate then t.amount*-1 else 0 end,
				prevMTD = case when t.transactionDate between @prevMTDStartDate and @prevMTDEndDate then t.amount*-1 else 0 end,
				prevYTD = case when t.transactionDate between @prevYTDStartDate and @prevYTDEndDate then t.amount*-1 else 0 end
			from dbo.tr_transactions as t
			inner join dbo.tr_GLAccounts as gl on gl.GLAccountID = t.debitGLAccountID and gl.status <> 'D'
			inner join @tblGL as tblgl on tblgl.glAccountID = gl.GLAccountID
			inner join dbo.tr_GLAccounts as gl2 on gl2.GLAccountID = t.creditGLAccountID and gl2.GLCode = 'ACCOUNTSRECEIVABLE' and gl2.status <> 'D'
			where t.ownedByOrgID = @orgid
			and t.typeID = 3
			and t.statusID = 1
			and t.transactionDate between @PrevYTDStartDate and @currYTDEndDate
		) as innerSales
		group by GLAccountID
	) as sdt on sdt.GLAccountID = rpt.GLAccountID

END 

/* *********** */
/* ALLOCATIONS */
/* *********** */
ELSE BEGIN

	-- net sales
	update rpt
	set rpt.CurrMTDNetAmt = sdt.CurrMTDamount,
		rpt.PrevMTDNetAmt = sdt.PrevMTDamount,
		rpt.CurrYTDNetAmt = sdt.CurrYTDamount,
		rpt.PrevYTDNetAmt = sdt.PrevYTDamount
	from @tblGL as rpt
	inner join (
		select creditGLAccountID as GLAccountID, sum(currMTD) as CurrMTDamount, sum(currYTD) as CurrYTDamount, 
			sum(prevMTD) as PrevMTDamount, sum(prevYTD) as PrevYTDamount
		from (
			select saleAdjTaxDitT.creditGLAccountID, 
				currMTD = case when b.depositDate between @currMTDStartDate and @currMTDEndDate then case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end else 0 end,
				currYTD = case when b.depositDate between @currYTDStartDate and @currYTDEndDate then case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end else 0 end,
				prevMTD = case when b.depositDate between @prevMTDStartDate and @prevMTDEndDate then case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end else 0 end,
				prevYTD = case when b.depositDate between @prevYTDStartDate and @prevYTDEndDate then case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end else 0 end
			from dbo.tr_transactions as allocT
			inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
			inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
			inner join dbo.tr_transactions as saleAdjTaxDitT on saleAdjTaxDitT.transactionID = allocR.appliedToTransactionID and saleAdjTaxDitT.statusID = 1
			left outer join dbo.tr_relationships as ditR 
				inner join dbo.tr_relationshipTypes as ditRT on ditRT.typeID = ditR.typeID and ditRT.type = 'DITSaleTrans'
				on ditR.transactionID = saleAdjTaxDitT.transactionID
			inner join dbo.tr_GLAccounts as gl on gl.GLAccountID = case when saleAdjTaxDitT.typeID = 10 then saleAdjTaxDitT.debitGLAccountID else saleAdjTaxDitT.creditGLAccountID end
			inner join @tblGL as tblgl on tblgl.glAccountID = gl.GLAccountID
			inner join dbo.tr_relationships as payR on payR.transactionID = allocT.transactionID
			inner join dbo.tr_relationshipTypes as payRT on payRT.typeID = payR.typeID and payRT.type = 'AllocPayTrans'
			inner join dbo.tr_transactions as payT on payT.transactionID = payR.appliedToTransactionID and payT.statusID = 1
			inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID and glAlloc.status <> 'D'
			inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
			inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4
			where allocT.ownedByOrgID = @orgid
			and allocT.typeID = 5
			and allocT.statusID = 1
			and b.depositDate between @prevYTDStartDate and @currYTDEndDate
		) as innerAlloc
		group by creditGLAccountID
	) as sdt on sdt.GLAccountID = rpt.GLAccountID

END

-- diffs
update @tblGL
set MTDDiff = ((CurrMTDNetAmt-PrevMTDNetAmt)/PrevMTDNetAmt) * 100
where PrevMTDNetAmt <> 0

update @tblGL
set MTDDiff = null
where isnull(PrevMTDNetAmt,0) = 0 and isnull(CurrMTDNetAmt,0) <> 0

update @tblGL
set YTDDiff = ((CurrYTDNetAmt-PrevYTDNetAmt)/PrevYTDNetAmt) * 100
where PrevYTDNetAmt <> 0

update @tblGL
set YTDDiff = null
where isnull(PrevYTDNetAmt,0) = 0 and isnull(CurrYTDNetAmt,0) <> 0

-- remove empty rows
delete from @tblGL
where CurrMTDNetAmt = 0 
and PrevMTDNetAmt = 0 
and CurrYTDNetAmt = 0 
and PrevYTDNetAmt = 0 

IF @filename is not null BEGIN

	IF OBJECT_ID('tempdb..##tmpPerfReportExport') IS NOT NULL 
		DROP TABLE ##tmpPerfReportExport

	select *
	into ##tmpPerfReportExport
	from @tblGL

	-- sql for export
	DECLARE @fullsql varchar(max)
	SELECT @fullsql = '
		select accountName as [Account], accountCode as [Account Code], 
			CurrMTDNetAmt as [MTD Current], PrevMTDNetAmt as [MTD Previous], MTDDiff as [MTD Change %], 
			CurrYTDNetAmt as [YTD Current], PrevYTDNetAmt as [YTD Previous], YTDDiff as [YTD Change %]
		from ##tmpPerfReportExport
		order by glOrder'

	-- export
	EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql

	IF OBJECT_ID('tempdb..##tmpPerfReportExport') IS NOT NULL 
		DROP TABLE ##tmpPerfReportExport

END ELSE BEGIN
	-- return dates
	select	@endDate as EndDate, @newEndDate as newEndDate,
			@currMTDStartDate as currMTDStartDate, @currMTDEndDate as currMTDEndDate,
			@prevMTDStartDate as prevMTDStartDate, @prevMTDEndDate as prevMTDEndDate,
			@currYTDStartDate as currYTDStartDate, @currYTDEndDate as currYTDEndDate,
			@prevYTDStartDate as prevYTDStartDate, @prevYTDEndDate as prevYTDEndDate

	-- return performance
	select * 
	from @tblGL
	order by glOrder

END

RETURN 0
GO


ALTER PROC [dbo].[tr_report_MTDYTD]
@orgID int,
@endDate datetime,
@base varchar(11),
@glaccountID int,
@filename varchar(400) = null

AS

-- set end date to real end
declare @newendDate datetime
select @newendDate = dateadd(s,-1,dateadd(day,1,DATEADD(dd, DATEDIFF(dd,0,@endDate), 0)))

-- set all other dates
declare @currMTDStartDate datetime, @currMTDEndDate datetime
declare @currYTDStartDate datetime, @currYTDEndDate datetime
declare @PrevMTDStartDate datetime, @PrevMTDEndDate datetime
declare @PrevYTDStartDate datetime, @PrevYTDEndDate datetime
select @currMTDEndDate = @newendDate
select @currMTDStartDate = DATEADD(mm,DATEDIFF(mm,0,@currMTDEndDate),0)
select @currYTDEndDate = @newendDate
select @currYTDStartDate = DATEADD(yy,DATEDIFF(yy,0,@currYTDEndDate),0)
select @PrevMTDEndDate = DATEADD(yy,-1,@CurrMTDEndDate)
select @PrevMTDStartDate = DATEADD(yy,-1,@CurrMTDStartDate)
select @PrevYTDEndDate = DATEADD(yy,-1,@CurrYTDEndDate)
select @PrevYTDStartDate = DATEADD(yy,-1,@CurrYTDStartDate)

-- accounts to track
declare @tblGL TABLE (glOrder varchar(max), GLAccountID int, accountName varchar(max), accountCode varchar(200),  
	CurrMTDNetAmt money DEFAULT(0), PrevMTDNetAmt money DEFAULT(0),
	CurrYTDNetAmt money DEFAULT(0), PrevYTDNetAmt money DEFAULT(0),
	MTDDiff float DEFAULT(0), YTDDiff float DEFAULT(0))
IF @glAccountID = 0
	insert into @tblGL (glOrder, GLAccountID, accountName, accountCode)
	SELECT gl.thePath, gl.GLAccountID, gl.thePathExpanded, gl.accountCode
	FROM dbo.fn_getRecursiveGLAccounts(@orgID) as gl
	where gl.AccountTypeID = 3
	and gl.status <> 'D'
	ORDER BY gl.thePath
ELSE
	insert into @tblGL (glOrder, GLAccountID, accountName, accountCode)
	SELECT gl.thePath, gl.GLAccountID, gl.thePathExpanded, gl.accountCode
	FROM dbo.fn_getRecursiveGLAccounts(@orgID) as gl
	where gl.AccountTypeID = 3
	and gl.status <> 'D'
	and gl.GLAccountID = @glAccountID
	ORDER BY gl.thePath

/* ***** */
/* SALES */
/* ***** */
IF @base = 'sales' BEGIN

	-- net sales
	update rpt
	set rpt.CurrMTDNetAmt = sdt.CurrMTDamount,
		rpt.PrevMTDNetAmt = sdt.PrevMTDamount,
		rpt.CurrYTDNetAmt = sdt.CurrYTDamount,
		rpt.PrevYTDNetAmt = sdt.PrevYTDamount
	from @tblGL as rpt
	inner join (
		select GLAccountID, sum(currMTD) as CurrMTDamount, sum(currYTD) as CurrYTDamount,
			sum(prevMTD) as PrevMTDamount, sum(prevYTD) as PrevYTDamount
		from (
			select t.creditGLAccountID as GLAccountID,
				currMTD = case when t.transactionDate between @currMTDStartDate and @currMTDEndDate then t.amount else 0 end,
				currYTD = case when t.transactionDate between @currYTDStartDate and @currYTDEndDate then t.amount else 0 end,
				prevMTD = case when t.transactionDate between @prevMTDStartDate and @prevMTDEndDate then t.amount else 0 end,
				prevYTD = case when t.transactionDate between @prevYTDStartDate and @prevYTDEndDate then t.amount else 0 end
			from dbo.tr_transactions as t
			inner join dbo.tr_GLAccounts as gl on gl.GLAccountID = t.creditGLAccountID
			inner join @tblGL as tblgl on tblgl.glAccountID = gl.GLAccountID
			where t.ownedByOrgID = @orgid
			and t.typeID in (1,7)
			and t.statusID = 1
			and gl.status <> 'D'
			and t.transactionDate between @PrevYTDStartDate and @currYTDEndDate
				union all
			select t.creditGLAccountID, 
				currMTD = case when t.transactionDate between @currMTDStartDate and @currMTDEndDate then t.amount else 0 end,
				currYTD = case when t.transactionDate between @currYTDStartDate and @currYTDEndDate then t.amount else 0 end,
				prevMTD = case when t.transactionDate between @prevMTDStartDate and @prevMTDEndDate then t.amount else 0 end,
				prevYTD = case when t.transactionDate between @prevYTDStartDate and @prevYTDEndDate then t.amount else 0 end
			from dbo.tr_transactions as t
			inner join dbo.tr_GLAccounts as gl on gl.GLAccountID = t.creditGLAccountID and gl.status <> 'D'
			inner join @tblGL as tblgl on tblgl.glAccountID = gl.GLAccountID
			inner join dbo.tr_GLAccounts as gl2 on gl2.GLAccountID = t.debitGLAccountID and gl2.GLCode = 'ACCOUNTSRECEIVABLE' and gl2.status <> 'D'
			where t.ownedByOrgID = @orgid
			and t.typeID = 3
			and t.statusID = 1
			and t.transactionDate between @PrevYTDStartDate and @currYTDEndDate
				union all
			select t.debitGLAccountID, 
				currMTD = case when t.transactionDate between @currMTDStartDate and @currMTDEndDate then t.amount*-1 else 0 end,
				currYTD = case when t.transactionDate between @currYTDStartDate and @currYTDEndDate then t.amount*-1 else 0 end,
				prevMTD = case when t.transactionDate between @prevMTDStartDate and @prevMTDEndDate then t.amount*-1 else 0 end,
				prevYTD = case when t.transactionDate between @prevYTDStartDate and @prevYTDEndDate then t.amount*-1 else 0 end
			from dbo.tr_transactions as t
			inner join dbo.tr_GLAccounts as gl on gl.GLAccountID = t.debitGLAccountID and gl.status <> 'D'
			inner join @tblGL as tblgl on tblgl.glAccountID = gl.GLAccountID
			inner join dbo.tr_GLAccounts as gl2 on gl2.GLAccountID = t.creditGLAccountID and gl2.GLCode = 'ACCOUNTSRECEIVABLE' and gl2.status <> 'D'
			where t.ownedByOrgID = @orgid
			and t.typeID = 3
			and t.statusID = 1
			and t.transactionDate between @PrevYTDStartDate and @currYTDEndDate
		) as innerSales
		group by GLAccountID
	) as sdt on sdt.GLAccountID = rpt.GLAccountID

END 

/* *********** */
/* ALLOCATIONS */
/* *********** */
ELSE BEGIN

	-- net sales
	update rpt
	set rpt.CurrMTDNetAmt = sdt.CurrMTDamount,
		rpt.PrevMTDNetAmt = sdt.PrevMTDamount,
		rpt.CurrYTDNetAmt = sdt.CurrYTDamount,
		rpt.PrevYTDNetAmt = sdt.PrevYTDamount
	from @tblGL as rpt
	inner join (
		select creditGLAccountID as GLAccountID, sum(currMTD) as CurrMTDamount, sum(currYTD) as CurrYTDamount, 
			sum(prevMTD) as PrevMTDamount, sum(prevYTD) as PrevYTDamount
		from (
			select case when saleAdjTaxDitT.typeID = 10 then saleAdjTaxDitT.debitGLAccountID else saleAdjTaxDitT.creditGLAccountID end as creditGLAccountID, 
				currMTD = case when b.depositDate between @currMTDStartDate and @currMTDEndDate then case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end else 0 end,
				currYTD = case when b.depositDate between @currYTDStartDate and @currYTDEndDate then case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end else 0 end,
				prevMTD = case when b.depositDate between @prevMTDStartDate and @prevMTDEndDate then case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end else 0 end,
				prevYTD = case when b.depositDate between @prevYTDStartDate and @prevYTDEndDate then case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end else 0 end
			from dbo.tr_transactions as allocT
			inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
			inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
			inner join dbo.tr_transactions as saleAdjTaxDitT on saleAdjTaxDitT.transactionID = allocR.appliedToTransactionID and saleAdjTaxDitT.statusID = 1
			left outer join dbo.tr_relationships as ditR 
				inner join dbo.tr_relationshipTypes as ditRT on ditRT.typeID = ditR.typeID and ditRT.type = 'DITSaleTrans'
				on ditR.transactionID = saleAdjTaxDitT.transactionID
			inner join dbo.tr_GLAccounts as gl on gl.GLAccountID = case when saleAdjTaxDitT.typeID = 10 then saleAdjTaxDitT.debitGLAccountID else saleAdjTaxDitT.creditGLAccountID end
			inner join @tblGL as tblgl on tblgl.glAccountID = gl.GLAccountID
			inner join dbo.tr_relationships as payR on payR.transactionID = allocT.transactionID
			inner join dbo.tr_relationshipTypes as payRT on payRT.typeID = payR.typeID and payRT.type = 'AllocPayTrans'
			inner join dbo.tr_transactions as payT on payT.transactionID = payR.appliedToTransactionID and payT.statusID = 1
			inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID and glAlloc.status <> 'D'
			inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
			inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4
			where allocT.ownedByOrgID = @orgid
			and allocT.typeID = 5
			and allocT.statusID = 1
			and b.depositDate between @prevYTDStartDate and @currYTDEndDate
		) as innerAlloc
		group by creditGLAccountID
	) as sdt on sdt.GLAccountID = rpt.GLAccountID

END

-- diffs
update @tblGL
set MTDDiff = ((CurrMTDNetAmt-PrevMTDNetAmt)/PrevMTDNetAmt) * 100
where PrevMTDNetAmt <> 0

update @tblGL
set MTDDiff = null
where isnull(PrevMTDNetAmt,0) = 0 and isnull(CurrMTDNetAmt,0) <> 0

update @tblGL
set YTDDiff = ((CurrYTDNetAmt-PrevYTDNetAmt)/PrevYTDNetAmt) * 100
where PrevYTDNetAmt <> 0

update @tblGL
set YTDDiff = null
where isnull(PrevYTDNetAmt,0) = 0 and isnull(CurrYTDNetAmt,0) <> 0

-- remove empty rows
delete from @tblGL
where CurrMTDNetAmt = 0 
and PrevMTDNetAmt = 0 
and CurrYTDNetAmt = 0 
and PrevYTDNetAmt = 0 

IF @filename is not null BEGIN

	IF OBJECT_ID('tempdb..##tmpPerfReportExport') IS NOT NULL 
		DROP TABLE ##tmpPerfReportExport

	select *
	into ##tmpPerfReportExport
	from @tblGL

	-- sql for export
	DECLARE @fullsql varchar(max)
	SELECT @fullsql = '
		select accountName as [Account], accountCode as [Account Code], 
			CurrMTDNetAmt as [MTD Current], PrevMTDNetAmt as [MTD Previous], MTDDiff as [MTD Change %], 
			CurrYTDNetAmt as [YTD Current], PrevYTDNetAmt as [YTD Previous], YTDDiff as [YTD Change %]
		from ##tmpPerfReportExport
		order by glOrder'

	-- export
	EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql

	IF OBJECT_ID('tempdb..##tmpPerfReportExport') IS NOT NULL 
		DROP TABLE ##tmpPerfReportExport

END ELSE BEGIN
	-- return dates
	select	@endDate as EndDate, @newEndDate as newEndDate,
			@currMTDStartDate as currMTDStartDate, @currMTDEndDate as currMTDEndDate,
			@prevMTDStartDate as prevMTDStartDate, @prevMTDEndDate as prevMTDEndDate,
			@currYTDStartDate as currYTDStartDate, @currYTDEndDate as currYTDEndDate,
			@prevYTDStartDate as prevYTDStartDate, @prevYTDEndDate as prevYTDEndDate

	-- return performance
	select * 
	from @tblGL
	order by glOrder

END

RETURN 0
GO

