use membercentral;
GO
ALTER PROCEDURE [dbo].[cms_getCMSResourceByID]
@siteID int,
@siteResourceID int,
@languageID int,
@memberID int,
@ovrMode varchar(30)

AS


DECLARE @activeMemberID int
select @activeMemberID = dbo.fn_getActiveMemberID(@memberID)

DECLARE @viewFunctionID int, @editFunctionID int
DECLARE @templateID int, @modeID int, @ovrModeID int, @siteCode varchar(10), @mainZoneID int, @siteResourceStatusID int
DECLARE @layoutInfo TABLE (templateID int, templateTypeName varchar(100), modeID int, modeName varchar(100), templateFilename varchar(100), sitecode varchar(10), orgcode varchar(10), siteResourceID int)
DECLARE @zoneAssignments TABLE (autoid int IDENTITY(1,1), zoneID int, siteResourceID int, sortOrder int, dataLevel int, viewRightPrintID int, editContentRightPrintID int)

select @siteCode = dbo.fn_getSiteCodeFromSiteID(@siteID)

declare @usetemplateID int, @usemodeID int
select TOP 1 @usetemplateID = ovtemplateID from cms_pageSections ps where siteID = @siteID and parentSectionID is null


select @usemodeID = modeID from cms_pageModes where modeName = @ovrMode
select @mainZoneID = zoneid from cms_pageZones where zoneName = 'Main'

select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

select @viewFunctionID = functionID from cms_siteResourceFunctions where functionName = 'view' 
select @editFunctionID = functionID from cms_siteResourceFunctions where functionName = 'editContent' 


-- if usetemplateid is null then use template 1 - platform default
IF @usetemplateID is null 
	select @usetemplateID = 1

insert into @layoutInfo (templateID, templateTypeName, modeID, modeName, templateFilename, sitecode, orgcode, siteResourceID)
select pSp.templateID, ptt.templateTypeName, pSp.modeID, pm.modeName, pt.templateFilename, s2.sitecode, o.orgcode, pt.siteResourceID
from (select @usetemplateID as templateID, @usemodeID as modeID) as pSp
inner join dbo.cms_pageTemplates as pt on pSp.templateID = pt.templateid
inner join dbo.cms_pageTemplateTypes ptt on pt.templateTypeID = ptt.templateTypeID
inner join dbo.cms_pageModes as pm on pSp.modeID = pm.modeID
left outer join dbo.sites as s2 on s2.siteid = pt.siteid
left outer join dbo.organizations as o on s2.orgid = o.orgid

-- find all zone assignments for page, not considering pageMode
insert into @zoneAssignments (zoneid, siteResourceID, sortOrder, dataLevel)
select @mainZoneID, sr.siteResourceID, 1 as sortOrder, 0 as dataLevel
from dbo.cms_siteResources sr
where sr.siteResourceID = @siteResourceID
and sr.siteID = @siteID
and sr.siteResourceStatusID = @siteResourceStatusID

IF NOT EXISTS (select * from @zoneAssignments) BEGIN
    select cast('<pageStructure />' as xml) as pageStructureXML
END
ELSE BEGIN
    -- update view and editContentPerms
    update za set
	   viewRightPrintID = srfrp_view.rightPrintID, 
	   editContentRightPrintID = srfrp_editContent.rightPrintID
    from @zoneAssignments za
    left outer join cache_perms_siteResourceFunctionRightPrints srfrp_view
	   on srfrp_view.siteResourceID = za.siteResourceID and srfrp_view.functionID= @viewFunctionID
    left outer join cache_perms_siteResourceFunctionRightPrints srfrp_editContent
	   on srfrp_editContent.siteResourceID = za.siteResourceID and srfrp_editContent.functionID= @editFunctionID

    -- use site default language
    SELECT @languageID = defaultLanguageID from sites where siteCode = @sitecode

    -- Now that we know template and mode, figure out page pod assignments 
    select cast(isNull((
    select page.pageID, page.pageName, page.allowReturnAfterLogin, page.siteResourceID as pageSiteResourceID, 
	    isnull(siteResource.siteID,0) as siteID,
	    isnull(siteResource.siteCode,0) as siteCode,
	    isnull(siteResource.orgID,0) as orgID,
	    isnull(siteResource.orgCode,0) as orgCode,
	    page.sectionname, page.sectionCode, @languageID as pageLanguageID,
	    coalesce(siteResourceDetails.applicationWidgetInstanceName,siteResourceDetails.applicationInstanceName,'') as pageTitle, 
	    '' as pageDesc, 
	    '' as keywords, 
	    ISNULL(layout.templateFileName,'DefaultTemplate') as layoutFileName,
	    ISNULL(layout.templateID,'') as templateID,
	    ISNULL(layout.siteResourceID,'') as templateSiteResourceID,
	    ISNULL(layout.templateTypeName,'') as templateTypeName,
	    ISNULL(layout.modeName,'Normal') as layoutMode, 
	    ISNULL(layout.sitecode,'') as layoutSiteCode, ISNULL(layout.orgcode,'') as layoutOrgCode, 
	    pageZone.zoneName, 
	    siteResource.siteResourceID,
	    dbo.fn_checkResourceRights(siteResource.siteResourceID,dbo.fn_getResourceFunctionID('view',siteResource.resourceTypeID),@activeMemberID,@siteID) as allowed,
	    ISNULL(siteResource.resourceType,'') as resourceType,
	    ISNULL(siteResource.resourceTypeClassName,'') as resourceClass,
	    ISNULL(siteResourceDetails.applicationInstanceID,'') as appInstanceID, 
	    ISNULL(siteResourceDetails.applicationInstanceName,'') as appInstanceName, 
	    ISNULL(siteResourceDetails.applicationTypeName,'') as appTypeName, 
	    ISNULL(siteResourceDetails.applicationWidgetInstanceID,'') as appWidgetInstanceID, 
	    ISNULL(siteResourceDetails.applicationInstanceResourceID,'') as appInstanceResourceID, 
	    ISNULL(siteResourceDetails.applicationWidgetTypeName,'') as appWidgetTypeName, 
	    ISNULL(siteResourceDetails.applicationWidgetInstanceName,'') as appWidgetInstanceName,
	    ISNULL(siteResourceDetails.contentID,'') as contentID,
	    ISNULL(siteResourceDetails.enableSocialMediaSharing,'') as enableSocialMediaSharing,
  ISNULL(zr.viewRightPrintID,'') as viewRightPrintID,
  ISNULL(zr.editContentRightPrintID,'') as editContentRightPrintID
    from (select '' as pageID, '' as pageName, 0 as siteResourceID, 0 as allowReturnAfterLogin, '' as sectionName, '' as sectionCode, '' as sectionID) as page
    inner join @layoutInfo as layout on 1=1
    inner join cms_pageTemplatesModesZones as ptmz on layout.modeid = ptmz.modeid and ptmz.templateID = layout.templateID
    inner join cms_pageZones as pageZone on pageZone.zoneid = ptmz.zoneid
    left outer join @zoneAssignments as zr
	    inner join (
		    select sr.siteResourceID, srt.resourceTypeID, srt.resourceType, srtc.resourceTypeClassName, s.siteID, o.orgID, s.siteCode, o.orgCode
		    from dbo.cms_siteResources sr
		    inner join dbo.cms_siteResourceTypes as srt 
			    on sr.resourceTypeID = srt.resourceTypeID
			    and sr.siteResourceStatusID = @siteResourceStatusID
		    inner join dbo.cms_siteResourceTypeClasses as srtc on srt.resourceTypeClassID = srtc.resourceTypeClassID
		    inner join dbo.sites s on sr.siteID = s.siteID
		    inner join dbo.organizations o on s.orgid = o.orgid
		    ) as siteResource on siteResource.siteResourceID = zr.siteResourceID
	    inner join dbo.fn_getCMSResourcesForSite(@siteid) as siteResourceDetails 
		    on siteResource.siteResourceID = siteResourceDetails.siteResourceID and siteResourceDetails.siteResourceID = @siteResourceID
	    on ptmz.zoneID = zr.zoneid
    order by zr.zoneid, zr.dataLevel desc, zr.sortOrder
    for xml auto, root('pageStructure')
    ),'<pageStructure />') as xml) as pageStructureXML
END
RETURN 0
GO