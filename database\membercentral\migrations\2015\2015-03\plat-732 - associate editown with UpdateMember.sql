use membercentral;
GO
-- change the display name of the function to something more generic since it no longer just applied to photos and videos
update cms_siteResourceFunctions set
    displayName = 'Edit Their Own Entries'
where functionname like 'editOwn'
GO
-- association editOwn with the UpdateMember resourceType
declare @updateMemberResourceTypeID int, @trashID int
select @updateMemberResourceTypeID = dbo.fn_getResourceTypeID('UpdateMember')

exec dbo.cms_createSiteResourceFunction
    @resourceTypeID=@updateMemberResourceTypeID, 
    @functionName='editOwn', 
    @displayName='Edit Their Own Entries', 
    @functionID = @trashID OUTPUT
GO

-- mass add perms to copy view function to editown for all UpdateMember instances

declare 
	@autoID int,
	@siteResourceID int,
	@include bit,
	@functionID int,
	@roleID int,
	@groupID int,
	@inheritedRightsResourceID int,
	@inheritedRightsFunctionID int,
	@siteID int,
	@trashID int

declare @permsToAdd TABLE (autoID int IDENTITY(1,1) PRIMARY KEY, siteID int, siteResourceID int, include bit, functionID int, roleID int, groupID int, inheritedRightsResourceID int, inheritedRightsFunctionID int)

-- write your query to populate the @permsToAdd table
declare @viewfunctionID int, @editOwnFunctionID int, @updateMemberResourceTypeID int

select @updateMemberResourceTypeID = dbo.fn_getResourceTypeID('UpdateMember')
select @viewfunctionID = dbo.fn_getResourceFunctionID('view',@updateMemberResourceTypeID)
select @editOwnFunctionID = dbo.fn_getResourceFunctionID('editOwn',@updateMemberResourceTypeID)

-- add all existing view perms to the table as editOwn perms 
insert into @permsToAdd (siteID , siteResourceID , include , functionID , roleID , groupID , inheritedRightsResourceID , inheritedRightsFunctionID )
select sr.siteID, sr.siteResourceID , srr.include , @editOwnFunctionID , srr.roleID , srr.groupID , srr.inheritedRightsResourceID , srr.inheritedRightsFunctionID
from cms_siteResourceRights srr
inner join cms_siteResources sr
    on sr.siteResourceID = srr.resourceID
    and srr.functionID = @viewfunctionID
    and sr.resourceTypeID = @updateMemberResourceTypeID

-- do not change below this line

select @autoID = min(autoID) from @permsToAdd
while @autoID is not null
begin

	select
		@siteID = siteID,
		@siteResourceID = siteResourceID, 
		@include = include, 
		@functionID = functionID, 
		@roleID = roleID, 
		@groupID = groupID, 
		@inheritedRightsResourceID = inheritedRightsResourceID, 
		@inheritedRightsFunctionID = inheritedRightsFunctionID
	from @permsToAdd
	where autoID = @autoID

	exec dbo.cms_createSiteResourceRight
		@siteID=@siteID,
		@siteResourceID=@siteResourceID, 
		@include=@include, 
		@functionID=@functionID, 
		@roleID=@roleID, 
		@groupID=@groupID,
		@memberID = null,
		@inheritedRightsResourceID=@inheritedRightsResourceID, 
		@inheritedRightsFunctionID=@inheritedRightsFunctionID,
		@resourceRightID=@trashID OUTPUT

	select @autoID = min(autoID) from @permsToAdd where autoID > @autoID
end

GO
ALTER PROC [dbo].[cms_createDefaultPages]
@siteid int,
@sectionid int,
@languageID int

AS

declare @appPageID int

-- get resourceType for system created pages and HTML content
declare @pgResourceTypeID int, @HTMLResourceTypeID int, @appPgResourceTypeID int, @siteResourceTypeID int
select @pgResourceTypeID = dbo.fn_getResourceTypeID('SystemCreatedPage')
select @HTMLResourceTypeID = dbo.fn_getResourceTypeID('UserCreatedContent')
select @appPgResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedPage')
select @siteResourceTypeID = dbo.fn_getResourceTypeID('site')

-- get siteresourceid
declare @websiteResourceID int
select @websiteResourceID = siteResourceID from sites where siteID = @siteID

-- get active resource status
declare @siteResourceStatusID int
select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

-- get Main Zone
declare @mainZoneID int
select @mainZoneID = dbo.fn_getZoneID('Main')

-- get page modes
declare @fullModeID int, @directModeID int, @streamModeID int
select @fullModeID = dbo.fn_getModeID('Full')
select @directModeID = dbo.fn_getModeID('Direct')
select @streamModeID = dbo.fn_getModeID('Stream')

-- get resource functions
declare @viewFID int, @loginFID int, @editOwnFID int
SELECT @viewFID = dbo.fn_getResourceFunctionID('view',@HTMLResourceTypeID)
SELECT @loginFID = dbo.fn_getResourceFunctionID('login',@siteResourceTypeID)
SELECT @editOwnFID = dbo.fn_getResourceFunctionID('editOwn',dbo.fn_getResourceTypeID('UpdateMember'))


-- get public, siteadmin group
declare @publicGID int, @siteadminGID int, @usersGID int, @guestGID int, @orgID int
select @publicGID = g.groupID, @orgID = s.orgID from dbo.ams_groups as g inner join dbo.sites as s on s.orgid = g.orgid where g.isSystemGroup = 1 and g.groupName = 'Public' AND g.status <> 'D' and s.siteid = @siteid
select @usersGID = g.groupID from dbo.ams_groups as g inner join dbo.sites as s on s.orgid = g.orgid where g.isSystemGroup = 1 and g.groupName = 'Users' AND g.status <> 'D' and s.siteid = @siteid
select @guestGID = g.groupID from dbo.ams_groups as g inner join dbo.sites as s on s.orgid = g.orgid where g.isSystemGroup = 1 and g.groupName = 'Guests' AND g.status <> 'D' and s.siteid = @siteid
select @siteadminGID = g.groupID from dbo.ams_groups as g inner join dbo.sites as s on s.orgid = g.orgid where g.isSystemGroup = 1 and g.groupName = 'Site Administrators' AND g.status <> 'D' and s.siteid = @siteid

-- Add memberID 0 record to the cache member groups table to allow public access to public content for non-authenticated users.
INSERT INTO [dbo].[cache_members_groups]
           ([orgid],[memberID],[groupID],[isManualDirect],[isManualIndirect],[isVirtualDirect],[isVirtualIndirect])
     VALUES
           (@orgID, 0, @publicGID, 0, 0, 0, 1)

-- create default pages and applications
DECLARE @rc int, @pageID int, @contentID int, @contentSiteResourceID int, @applicationTypeID int, @suggestedPageName varchar(100), @applicationInstanceID int, @siteresourceID int, @trashID int
BEGIN TRAN

	-- main page and content
	EXEC @rc = dbo.cms_createPage 
		@siteid=@siteid, 
		@languageID=@languageID, 
		@resourceTypeID=@pgResourceTypeID, 
		@siteResourceStatusID=@siteResourceStatusID, 
		@pgParentResourceID=null, 
		@isVisible=1, 
		@sectionID=@sectionID, 
		@ovTemplateID=null, 
		@ovTemplateIDMobile=null,
		@ovModeID=null, 
		@pageName='Main', 
		@pageTitle='Welcome', 
		@pageDesc=null, 
		@keywords=null,
		@inheritPlacements=1,
		@allowReturnAfterLogin=1, 
		@checkReservedNames=0, 
		@pageID=@pageID OUTPUT

		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = dbo.cms_createContent @siteID=@siteid, @pageID=@pageID, @zoneID=@mainZoneID, @resourceTypeID=@HTMLResourceTypeID, @siteResourceStatusID=@siteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle='Welcome', @contentDesc=null, @rawContent='Put your homepage text here. You can include tables, images, lists, and more! Our built-in content editor can do all of this and more.', @memberID=NULL, @contentID=@contentID, @contentSiteResourceID=@contentSiteResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- login app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'Login'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=1, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@fullModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- org doc download app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'OrgDocDownload'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- store doc download app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'StoreDocDownload'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- ts doc download app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'TSDocDownload'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- account locator
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'accountLocator'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@directModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1,
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null,
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null,
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- userinfo app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'UserInfo'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@directModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- updatemember app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'UpdateMember'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=1, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=null, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- Ticket 8352718 - default permissions when setting up a site
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@usersGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@guestGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@editOwnFID, @roleID=null, @groupID=@usersGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@editOwnFID, @roleID=null, @groupID=@guestGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- ContentEditor app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'ContentEditor'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@fullModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- ajax app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'Ajax'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	
	-- Flash Express Install app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'flashExpressInstall'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	
	-- AppProxy app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'appProxy'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	
	-- invoices app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'invoices'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@fullModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- BuyNow app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'buyNow'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@fullModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	
	-- ViewCart app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'viewCart'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@fullModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	
	-- Support app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'Support'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=null, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- admin app
	-- set template used by the admin app to the admin template
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'Admin'
	EXEC @rc = dbo.cms_createApplicationInstanceAdmin @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle='Website Control Panel', @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=2, @pageModeID=null, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@siteadminGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	--set admin page inheritPlacements = 0
	update cms_pages set inheritPlacements = 0 where pageID = @appPageID
	IF @@ERROR <> 0 GOTO on_error

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO
