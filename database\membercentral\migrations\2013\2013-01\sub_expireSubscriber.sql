ALTER PROC [dbo].[sub_expireSubscriber]
@subscriberID int,
@memberID int,
@siteID int,
@enteredByMemberID int,
@statsSessionID int,
@AROption char(1),
@fReturnQuery bit=1

AS

declare @ApplicationTypeID int, @nowdate datetime, @minSubID int, @updateResult int, @invoiceID int,
	@invoiceNumber varchar(18), @minTID int, @adjAmount money, @rc int, @adjtransactionID int
DECLARE @tblAdjust TABLE (transactionID int, amountToAdjust money)

select @ApplicationTypeID = dbo.fn_getApplicationTypeIDFromName('Admin')
select @nowdate = getdate()

declare @tblSubs TABLE (subscriberID int, thePathExpanded varchar(max))
insert into @tblSubs (subscriberID, thePathExpanded)
select subscriberID, thePathExpanded
from dbo.fn_getRecursiveMemberSubscriptions(@memberID,@siteID,@subscriberID)
where status not in ('D','E')

BEGIN TRAN
	
	select @minSubID = min(subscriberID) from @tblSubs
	while @minSubID is not null BEGIN
		EXEC dbo.sub_updateSubscriberStatus @subscriberID=@minSubID, @newStatusCode='E', @siteID=@siteID, 
											@enteredByMemberID=@enteredByMemberID, @result=@updateResult OUTPUT
			IF @@ERROR <> 0 OR @updateResult < 0 goto on_error

		IF @AROption = 'A' BEGIN
			INSERT INTO @tblAdjust (transactionID, amountToAdjust)
			select rt.mainTransactionID, ts.cache_amountAfterAdjustment
			from dbo.fn_sub_subscriberTransactions(@minSubID) as rt
			inner join dbo.tr_transactionSales as ts on ts.transactionID = rt.transactionID
				and ts.cache_amountAfterAdjustment > 0
				and rt.typeID = 1
				IF @@ERROR <> 0 GOTO on_error
		END
		IF @AROption = 'B' BEGIN
			INSERT INTO @tblAdjust (transactionID, amountToAdjust)
			select rt.mainTransactionID, ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount-ts.cache_pendingPaymentAllocatedAmount
			from dbo.fn_sub_subscriberTransactions(@minSubID) as rt
			inner join dbo.tr_transactionSales as ts on ts.transactionID = rt.transactionID
				and ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount-ts.cache_pendingPaymentAllocatedAmount > 0
				and rt.typeID = 1
				IF @@ERROR <> 0 GOTO on_error
		END

		UPDATE dbo.tr_applications
		SET [status] = 'D'
		WHERE itemID = @minSubID
		AND itemType = 'Dues'
		AND applicationTypeID = @ApplicationTypeID
		AND [status] <> 'D'
			IF @@ERROR <> 0 GOTO on_error

		select @minSubID = min(subscriberID) from @tblSubs where subscriberID > @minSubID
	END

	-- if there are adjustments to make
	IF EXISTS (select transactionID from @tblAdjust) BEGIN

		-- if any transactions are on a open or pending invoice, grab it for all adjustments. 
		-- otherwise, we need to create one to hold these adjustments
		select top 1 @invoiceID = i.invoiceID
			from dbo.tr_invoices as i
			inner join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
			inner join @tblAdjust as tbl on tbl.transactionID = it.transactionID
			and statusID in (1,2)
			order by i.invoiceID
		IF @invoiceID is null BEGIN
			EXEC @rc = dbo.tr_createInvoice @enteredByMemberID=@enteredByMemberID,
				@assignedToMemberID=@memberID, @dateBilled=@nowdate, @dateDue=@nowdate, 
				@messageContentID=null, @invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT
				IF @@ERROR <> 0 or @rc <> 0 or @invoiceID = 0 GOTO on_error
		END

		-- record adjustments
		SELECT @minTID = min(transactionID) from @tblAdjust
		WHILE @minTID IS NOT NULL BEGIN
			SELECT @adjAmount = amountToAdjust*-1 from @tblAdjust where transactionID = @minTID
			SELECT @rc = null
			
			EXEC @rc = dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteID,
				@recordedByMemberID=@enteredByMemberID, @statsSessionID=@statsSessionID,
				@status='Active', @amount=@adjAmount, @transactionDate=@nowdate,
				@saleTransactionID=@minTID, @invoiceID=@invoiceID, @transactionID=@adjtransactionID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @adjtransactionID = 0 GOTO on_error
			
			SELECT @minTID = min(transactionID) from @tblAdjust where transactionID > @minTID
		END

		EXEC @rc = dbo.tr_closeInvoice @enteredByMemberID=@enteredByMemberID, @invoiceID=@invoiceID			
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	
	END

	IF @fReturnQuery = 1
	BEGIN
		-- return query of subs
		select * from @tblSubs
	END

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
