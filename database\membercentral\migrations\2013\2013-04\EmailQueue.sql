USE platformmail
GO
CREATE TABLE [dbo].[email_messageTypes](
	[messageTypeID] [int] IDENTITY(1,1) NOT NULL,
	[messageTypeCode] [varchar](10) NOT NULL,
	[messageType] [varchar](40) NOT NULL,
 CONSTRAINT [PK_email_messageTypes] PRIMARY KEY CLUSTERED 
(
	[messageTypeID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE dbo.email_messageTypes ADD CONSTRAINT
	IX_email_messageTypes UNIQUE NONCLUSTERED 
	(
	messageTypeCode
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
CREATE TABLE [dbo].[email_messages](
	[messageID] [int] IDENTITY(1,1) NOT NULL,
	[messageTypeID] [int] NOT NULL,
	[siteID] [int] NOT NULL,
	[sendingSiteResourceID] [int] NOT NULL,
	[dateEntered] [datetime] NOT NULL CONSTRAINT [DF_email_messages_dateEntered]  DEFAULT (getdate()),
	[recordedByMemberID] [int] NOT NULL,
	[fromName] [varchar](200) NOT NULL,
	[fromEmail] [varchar](200) NOT NULL,
	[replyToEmail] [varchar](200) NOT NULL,
	[senderEmail] [varchar](200) NOT NULL,
	[subject] [varchar](400) NOT NULL,
	[contentVersionID] [int] NOT NULL,
	[messageWrapper] [varchar](max) NOT NULL,
	[uid] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_email_messages] PRIMARY KEY CLUSTERED 
(
	[messageID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE dbo.email_messages ADD CONSTRAINT
	FK_email_messages_email_messageTypes FOREIGN KEY
	(
	messageTypeID
	) REFERENCES dbo.email_messageTypes
	(
	messageTypeID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
CREATE TABLE [dbo].[email_messageRecipientHistory](
	[recipientID] [int] IDENTITY(1,1) NOT NULL,
	[messageID] [int] NOT NULL,
	[memberID] [int] NOT NULL,
	[dateLastUpdated] [datetime] NOT NULL CONSTRAINT [DF_email_messageRecipientHistory_dateLastUpdated]  DEFAULT (getdate()),
	[toName] [varchar](200) NOT NULL,
	[toEmail] [varchar](200) NOT NULL,
	[emailStatusID] [int] NOT NULL,
	[batchID] [uniqueidentifier] NULL,
	[batchStartDate] [datetime] NULL,
 CONSTRAINT [PK_email_messageRecipientHistory] PRIMARY KEY CLUSTERED 
(
	[recipientID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE dbo.email_messageRecipientHistory ADD CONSTRAINT
	FK_email_messageRecipientHistory_email_messages FOREIGN KEY
	(
	messageID
	) REFERENCES dbo.email_messages
	(
	messageID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
CREATE TABLE [dbo].[email_statuses](
	[statusID] [int] IDENTITY(1,1) NOT NULL,
	[statusCode] [varchar](10) NOT NULL,
	[status] [varchar](40) NOT NULL,
	[statusOrder] [tinyint] NOT NULL,
 CONSTRAINT [PK_email_statuses] PRIMARY KEY CLUSTERED 
(
	[statusID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE dbo.email_statuses ADD CONSTRAINT
	IX_email_statuses UNIQUE NONCLUSTERED 
	(
	statusCode
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

GO
ALTER TABLE dbo.email_messageRecipientHistory ADD CONSTRAINT
	FK_email_messageRecipientHistory_email_statuses FOREIGN KEY
	(
	emailStatusID
	) REFERENCES dbo.email_statuses
	(
	statusID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
CREATE TABLE [dbo].[email_metadataFields](
	[fieldID] [int] IDENTITY(1,1) NOT NULL,
	[fieldName] [varchar](60) NOT NULL,
	[isMergeField] [bit] NOT NULL,
 CONSTRAINT [PK_email_metadataFields] PRIMARY KEY CLUSTERED 
(
	[fieldID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE dbo.email_metadataFields ADD CONSTRAINT
	IX_email_metadataFields UNIQUE NONCLUSTERED 
	(
	fieldName
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

GO
CREATE TABLE [dbo].[email_messageMetadataFields](
	[messageFieldID] [int] IDENTITY(1,1) NOT NULL,
	[messageID] [int] NOT NULL,
	[fieldID] [int] NOT NULL,
	[memberID] [int] NOT NULL,
	[fieldValue] [varchar](max) NOT NULL,
 CONSTRAINT [PK_email_messageMetadataFields] PRIMARY KEY CLUSTERED 
(
	[messageFieldID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE dbo.email_messageMetadataFields ADD CONSTRAINT
	FK_email_messageMetadataFields_email_messages FOREIGN KEY
	(
	messageID
	) REFERENCES dbo.email_messages
	(
	messageID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.email_messageMetadataFields ADD CONSTRAINT
	FK_email_messageMetadataFields_email_metadataFields FOREIGN KEY
	(
	fieldID
	) REFERENCES dbo.email_metadataFields
	(
	fieldID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO

insert into dbo.email_statuses (statusCode, [status], statusOrder)
values ('I', 'Inserting', 1)
insert into dbo.email_statuses (statusCode, [status], statusOrder)
values ('Q', 'Queued', 2)
insert into dbo.email_statuses (statusCode, [status], statusOrder)
values ('P', 'Processing', 3)
insert into dbo.email_statuses (statusCode, [status], statusOrder)
values ('S', 'Sent', 4)
GO
insert into dbo.email_messageTypes (messageTypeCode, messageType)
values ('ANNOUNCE', 'Announcements')
insert into dbo.email_messageTypes (messageTypeCode, messageType)
values ('SITEWEL', 'Site Welcome Message')
insert into dbo.email_messageTypes (messageTypeCode, messageType)
values ('EMAILBLAST', 'Email Blast')
GO

CREATE PROC dbo.email_insertMessage
@messageTypeID int,
@siteID int,
@sendingSiteResourceID int,
@recordedByMemberID int,
@fromName varchar(200),
@fromEmail varchar(200),
@replyToEmail varchar(200),
@senderEmail varchar(200),
@subject varchar(400),
@contentVersionID int,
@messageWrapper varchar(max),
@messageID int OUTPUT

AS

select @messageID = null

insert into dbo.email_messages (messageTypeID, siteID, sendingSiteResourceID, dateEntered, 
	recordedByMemberID, fromName, fromEmail, replyToEmail, senderEmail, [subject], 
	contentVersionID, messageWrapper, [uid])
values (@messageTypeID, @siteID, @sendingSiteResourceID, getdate(), @recordedByMemberID,
	@fromName, @fromEmail, @replyToEmail, @senderEmail, @subject, @contentVersionID, 
	@messageWrapper, NEWID())
	IF @@ERROR <> 0 GOTO on_error
	SELECT @messageID = SCOPE_IDENTITY()

RETURN 0

-- error exit
on_error:
	SELECT @messageID = 0
	RETURN -1
GO

CREATE PROC dbo.email_insertMessageRecipientHistory
@messageID int, 
@memberID int, 
@toName varchar(200),
@toEmail varchar(200),
@statusCode varchar(10) = 'Q',
@recipientID int OUTPUT

AS

select @recipientID = null

declare @emailStatusID int
select @statusCode = isnull(@statusCode,'Q')
select @emailStatusID = statusID from dbo.email_statuses where statusCode = @statusCode
	IF @@ERROR <> 0 GOTO on_error

insert into dbo.email_messageRecipientHistory (messageID, memberID, dateLastUpdated, toName, toEmail, emailStatusID)
values (@messageID, @memberID, getdate(), @toName, @toEmail, @emailStatusID)
	IF @@ERROR <> 0 GOTO on_error
	SELECT @recipientID = SCOPE_IDENTITY()

RETURN 0

-- error exit
on_error:
	SELECT @recipientID = 0
	RETURN -1
GO

CREATE PROC dbo.email_insertMessageMetadataField
@messageID int, 
@fieldID int, 
@memberID int,
@fieldValue varchar(max),
@messageFieldID int OUTPUT

AS

select @messageFieldID = null

insert into dbo.email_messageMetadataFields (messageID, fieldID, memberID, fieldValue)
values (@messageID, @fieldID, @memberID, @fieldValue)
	IF @@ERROR <> 0 GOTO on_error
	SELECT @messageFieldID = SCOPE_IDENTITY()

RETURN 0

-- error exit
on_error:
	SELECT @messageFieldID = 0
	RETURN -1
GO

CREATE PROC dbo.email_insertMetadataField
@fieldName varchar(60),
@isMergeField bit,
@fieldID int OUTPUT

AS

select @fieldID = null
select @fieldID = fieldID from dbo.email_metadataFields where fieldName = @fieldName and isMergeField = @isMergeField

IF @fieldID is null BEGIN
	insert into dbo.email_metadataFields (fieldName, isMergeField)
	values (@fieldName, @isMergeField)
		IF @@ERROR <> 0 GOTO on_error
		SELECT @fieldID = SCOPE_IDENTITY()
END

RETURN 0

-- error exit
on_error:
	SELECT @fieldID = 0
	RETURN -1
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[email_setMessageRecipientHistoryStatus]') AND type in (N'P', N'PC'))
	DROP PROCEDURE [dbo].[email_setMessageRecipientHistoryStatus]
GO
CREATE PROC [dbo].[email_setMessageRecipientHistoryStatus]
@messageID int,
@recipientID int = null,
@statusCode varchar(10),
@updateDate bit

AS

declare @emailStatusID int
select @emailStatusID = statusID from dbo.email_statuses where statusCode = @statusCode
	IF @@ERROR <> 0 GOTO on_error

update dbo.email_messageRecipientHistory
set emailStatusID = @emailStatusID,
	dateLastUpdated = case when @updateDate = 1 then getdate() else dateLastUpdated end
where messageID = @messageID
and recipientID = isnull(@recipientID,recipientID)
and emailStatusID <> @emailStatusID
	IF @@ERROR <> 0 GOTO on_error

RETURN 0

-- error exit
on_error:
	RETURN -1
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[email_markRecipientBatch]') AND type in (N'P', N'PC'))
	DROP PROCEDURE [dbo].[email_markRecipientBatch]
GO
CREATE PROC dbo.email_markRecipientBatch
@batchSize int,
@workerUUID uniqueIdentifier OUTPUT

AS

declare @batchStartDate datetime, @newStatusID int, @oldStatusID int
declare @tblMessages table(
	messageID int NOT NULL, 
	replyToEmail varchar(200) NOT NULL, 
	subject varchar(400) NOT NULL, 
	siteCode varchar(10) NOT NULL, 
	siteName varchar(60) NOT NULL,
	emailFrom varchar(403) NOT NULL,
	senderEmail varchar(200) NOT NULL, 
	messageContent varchar(max) NOT NULL);
declare @tblRecipients table(
	recipientID int NOT NULL, 
	messageID int NOT NULL, 
	memberID int NOT NULL,
	toName varchar(200) NOT NULL,
	toEmail varchar(200) NOT NULL);
    
set @workerUUID = newID()
set @batchStartDate = getdate()
select @newStatusID = statusID from dbo.email_statuses where statusCode = 'P'
select @oldStatusID = statusID from dbo.email_statuses where statusCode = 'Q'

-- mark recipients
update r 
set batchID = @workerUUID,
	batchStartDate = @batchStartDate,
	emailStatusID = @newStatusID
output	inserted.recipientID, 
		inserted.messageID, 
		inserted.memberID,
		inserted.toName,
		inserted.toEmail
into @tblRecipients
from dbo.email_messageRecipientHistory as r
inner join (
	select top (@batchSize) recipientID
	from dbo.email_messageRecipientHistory
	where emailStatusID = @oldStatusID
	and batchID is null
	order by dateLastUpdated
) as temp on temp.recipientID = r.recipientID
	IF @@ERROR <> 0 GOTO on_error

-- get messages
insert into @tblMessages (messageID, replyToEmail, subject, siteCode, siteName, emailFrom, senderEmail, messageContent)
select m.messageID, m.replyToEmail, m.subject, s.siteCode, s.siteName,
	m.fromEmail + case when len(m.fromName) > 0 then ' (' + m.fromName + ')' else '' end as emailFrom,
	m.senderEmail, 
	replace(m.messagewrapper,'@@rawcontent@@',cv.rawContent) as messageContent
from (select distinct messageID from @tblRecipients) as tmpM
inner join dbo.email_messages as m on m.messageID = tmpM.messageID
inner join membercentral.dbo.cms_contentVersions as cv on cv.contentVersionID = m.contentVersionID
inner join membercentral.dbo.sites as s on s.siteID = m.siteID

select *
from @tblMessages
order by messageID

-- get message merge codes
select distinct m.messageID, reg.Text as fieldName
from @tblMessages as m
cross apply membercentral.dbo.fn_RegexMatches(m.messageContent,'(?<=\[\[)([^,\]]+)(?=,?([^\]]+)?\]\])') as reg
order by 1, 2

-- get recipients
select recipientID, messageID, memberID, 
	toEmail + case when len(toName) > 0 then ' (' + toName + ')' else '' end as emailTo
from @tblRecipients
	IF @@ERROR <> 0 GOTO on_error

-- get metadata
select r.recipientID, f.fieldID, f.fieldName, mf.messageid, mf.memberid, mf.fieldValue
from dbo.email_metadataFields as f
inner join dbo.email_messageMetadataFields as mf on mf.fieldID = f.fieldID
inner join @tblRecipients as r on r.messageID = mf.messageID and r.memberID = mf.memberID
where f.isMergeField = 1

-- normal exit
RETURN 0

-- error exit
on_error:
	RETURN -1
GO








use membercentral
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[an_emailNotices]') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].[an_emailNotices]
GO
CREATE PROC dbo.an_emailNotices
@messageWrapper varchar(max)

AS

declare @functionID int, @recordedByMemberID int, @maxEmailsAllowedPerNotice int, @messageID int, 
	@messageTypeID int, @mesageStatusID int, @rawcontent varchar(max), @messageToParse varchar(max),
	@fieldID int, @fieldName varchar(60), @noticeID int
declare @metadataFields TABLE (fieldName varchar(60), fieldID int NULL)
select @functionID = dbo.fn_getResourceFunctionID('ReceiveAnnouncementEmail',dbo.fn_getResourceTypeId('Announcement'))
select @recordedByMemberID = memberID from dbo.ams_members where memberNumber = 'SYSTEM' and orgID = 1
select @messageTypeID = messageTypeID from platformMail.dbo.email_messageTypes where messageTypeCode = 'ANNOUNCE'
select @mesageStatusID = statusID from platformMail.dbo.email_statuses where statusCode = 'Q'
select @maxEmailsAllowedPerNotice = 2000

IF OBJECT_ID('tempdb..#tmpNotices') IS NOT NULL 
	DROP TABLE #tmpNotices
IF OBJECT_ID('tempdb..#tmpNoticeRecipients') IS NOT NULL 
	DROP TABLE #tmpNoticeRecipients

SELECT n.noticeID, s.siteID, n.siteResourceID, 
	coalesce(nullif(n.emailFromName,''),nullif(c.emailFromName,''),s.sitename) as emailFromName,
	coalesce(nullif(n.emailFromAddress,''),nullif(c.emailFromAddress,''),net.supportProviderEmail) as emailFromEmail,
	coalesce(nullif(n.emailFromAddress,''),nullif(c.emailFromAddress,''),net.supportProviderEmail) as emailReplyToEmail,
	net.supportProviderEmail as emailSenderEmail,
	cl.contentTitle as emailSubject, n.noticeContentID, cv.contentVersionID, n.notifyEmail, NEWID() as messageUID, 
	replace(replace(@messageWrapper,'@@centername@@',Cai.applicationInstanceName + isnull(' (' + CcommunityInstances.applicationInstanceName + ')','')),'@@centerlink@@','<a href="http://' + sh.HostName + '">' + s.sitename + '</a>') as messageWrapper, 
	0 as numRecipients, @maxEmailsAllowedPerNotice as maxEmailsAllowedPerNotice
INTO #tmpNotices
FROM dbo.an_notices as n 
INNER JOIN dbo.cms_siteResources as sr ON n.siteResourceID = sr.siteResourceID
INNER JOIN dbo.sites as s on s.siteID = sr.siteID
INNER JOIN dbo.siteHostNames as sh on sh.siteID = s.siteID and sh.hostnameID = sh.mainHostnameID
inner join dbo.networkSites as ns on ns.siteID = s.siteID and ns.isLoginNetwork = 1
INNER JOIN dbo.networks as net on net.networkID = ns.networkID
INNER JOIN dbo.an_centerNotices as cn ON cn.sourceNoticeID = n.noticeID
INNER JOIN dbo.an_centers as c on c.centerID = cn.centerID
INNER JOIN dbo.cms_applicationInstances as Cai on Cai.applicationInstanceID = c.applicationInstanceID
INNER JOIN dbo.cms_siteResources as Csr on Cai.siteResourceID = Csr.siteResourceID and Csr.siteResourceStatusID = 1
INNER JOIN dbo.cms_siteResources as CparentResource on CparentResource.siteResourceID = Csr.parentSiteResourceID
LEFT OUTER JOIN dbo.cms_siteResources as CgrandparentResource
	INNER JOIN dbo.cms_applicationInstances as CCommunityInstances on CcommunityInstances.siteResourceID = CgrandParentResource.siteResourceID
	INNER JOIN dbo.cms_siteResourceTypes as Csrt on Csrt.resourceTypeID = CgrandparentResource.resourceTypeID and Csrt.resourceType = 'Community'
	on CgrandparentResource.siteResourceID = CparentResource.parentSiteResourceID
INNER JOIN dbo.cms_contentLanguages as cl ON cl.contentID = n.noticeContentID AND cl.languageID = 1
INNER JOIN dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID AND cv.isActive = 1
cross apply dbo.fn_getContent(n.noticeContentID,1) as noticeContent
WHERE n.emailNotice = 1
AND n.emailDateScheduled < getDate()
AND n.endDate > getDate()
AND n.emailDateSent IS NULL
and len(noticeContent.rawContent) > 0

select uniqueEmails.noticeID, uniqueEmails.memberID, chosenMember.prefix, chosenMember.firstName, 
	chosenMember.middlename, chosenMember.lastName, chosenMember.suffix, uniqueEmails.email
into #tmpNoticeRecipients
from (
	SELECT n.noticeID, min(m.memberID) as memberID, me.email
	FROM dbo.an_notices as n
	INNER JOIN #tmpNotices as tmp on tmp.noticeID = n.noticeID
	inner join dbo.cache_perms_siteResourceFunctionRightPrints as srfrp on srfrp.siteResourceID = n.siteResourceID
		and srfrp.functionID = @functionID
	inner join dbo.cache_perms_groupPrintsRightPrints as gprp on gprp.rightPrintID = srfrp.rightPrintID
	inner join dbo.cache_perms_groupPrints as gp on gp.groupPrintID = gprp.groupPrintID
	INNER JOIN dbo.ams_members as m ON m.groupPrintID = gp.groupPrintID and m.status = 'A'
	INNER JOIN dbo.ams_memberEmails as me ON me.memberID = m.memberID 
	inner join dbo.ams_memberEmailTypes as met on met.emailTypeID = me.emailTypeID and met.emailTypeOrder = 1
	WHERE me.email <> ''
	group by n.noticeID, me.email
) as uniqueEmails
inner join dbo.ams_members as chosenMember on chosenMember.memberID = uniqueEmails.memberID

-- put recipient count in notices table
update #tmpNotices set numRecipients = (select count(*) from #tmpNoticeRecipients where noticeID = #tmpNotices.noticeID)

-- cancel notices that are over the threshold of recipients or have no recipients
update n
set n.emailNotice = 0
from dbo.an_notices as n
inner join #tmpNotices as tmp on tmp.noticeID = n.noticeID
where tmp.numRecipients >= @maxEmailsAllowedPerNotice
or tmp.numRecipients = 0

-- delete notices where there are no recipients
delete from #tmpNotices where numRecipients = 0

-- each notice becomes its own email_message (as queued status)
insert into platformMail.dbo.email_messages (messageTypeID, siteID, sendingSiteResourceID, dateEntered, 
	recordedByMemberID, fromName, fromEmail, replyToEmail, senderEmail, [subject], contentVersionID, 
	messageWrapper, [uid])
select @messageTypeID, siteID, siteResourceID, getdate(), @recordedByMemberID, emailFromName,
	emailFromEmail, emailReplyToEmail, emailSenderEmail, emailSubject, contentVersionID, 
	messageWrapper, messageUID
from #tmpNotices
where numRecipients < @maxEmailsAllowedPerNotice

-- add recipients
insert into platformMail.dbo.email_messageRecipientHistory (messageID, memberID, 
	dateLastUpdated, toName, toEmail, emailStatusID, batchID, batchStartDate)
select em.messageID, tmpR.memberID, getdate(), tmpR.firstName + ' ' + tmpR.lastName, 
	tmpR.email, @mesageStatusID, null, null
from platformMail.dbo.email_messages as em
inner join #tmpNotices as tmpN on tmpN.messageUID = em.uid
inner join #tmpNoticeRecipients as tmpR on tmpR.noticeID = tmpN.noticeID

-- loop over each notice to add any necessary metadata fields
select @messageID = min(em.messageID)
	from platformMail.dbo.email_messages as em
	inner join #tmpNotices as tmpN on tmpN.messageUID = em.uid
while @messageID is not null BEGIN
	select @messageWrapper = null, @rawcontent = null
	delete from @metadataFields

	select @noticeID = tmpN.noticeID, @messageWrapper = tmpN.messageWrapper, 
		@rawcontent = msgcontent.rawcontent
		from platformMail.dbo.email_messages as em
		inner join #tmpNotices as tmpN on tmpN.messageUID = em.uid
		cross apply dbo.fn_getContent(tmpN.noticeContentID,1) as msgcontent
		where em.messageID = @messageID

	select @messageToParse = replace(@messageWrapper,'@@rawcontent@@',@rawcontent)

	insert into @metadataFields (fieldName)
	select distinct [Text]
	from dbo.fn_RegexMatches(@messageToParse,'(?<=\[\[)([^,\]]+)(?=,?([^\]]+)?\]\])')

	insert into platformMail.dbo.email_metadataFields (fieldName, isMergeField)
	select fieldName, 1
	from @metadataFields
		except
	select fieldName, isMergeField
	from platformMail.dbo.email_metadataFields

	update tmp
	set tmp.fieldID = MF.fieldID
	from @metadataFields as tmp
	inner join platformMail.dbo.email_metadataFields as MF on MF.fieldName = tmp.fieldName
	where MF.isMergeField = 1

	select @fieldID = min(fieldID) from @metadataFields
	while @fieldID is not null BEGIN
		select @fieldName = fieldName from @metadataFields where fieldID = @fieldID

		insert into platformMail.dbo.email_messageMetadataFields (messageID, fieldID, memberID, fieldValue)
		select @messageID, @fieldID, memberID, 
			fieldValue = case @fieldName
				when 'firstname' then firstname
				when 'lastname' then lastname
				when 'prefix' then prefix 
				when 'fullName' then firstname + ' ' + lastname
				when 'extendedname' then firstname + isnull(' ' + nullif(middlename,''),'') + ' ' + lastname + isnull(' ' + nullif(suffix,''),'')
				end
		from #tmpNoticeRecipients
		where noticeID = @noticeID

		select @fieldID = min(fieldID) from @metadataFields where fieldID > @fieldID
	END

	select @messageID = min(em.messageID)
		from platformMail.dbo.email_messages as em
		inner join #tmpNotices as tmpN on tmpN.messageUID = em.uid
		where em.messageID > @messageID
END

-- update notices with datesent
update n
set n.emailDateSent = getDate()
from dbo.an_notices as n
inner join #tmpNotices as tmp on tmp.noticeID = n.noticeID
where tmp.numRecipients < @maxEmailsAllowedPerNotice

-- return notices
select *
from #tmpNotices

IF OBJECT_ID('tempdb..#tmpNotices') IS NOT NULL 
	DROP TABLE #tmpNotices
IF OBJECT_ID('tempdb..#tmpNoticeRecipients') IS NOT NULL 
	DROP TABLE #tmpNoticeRecipients

RETURN 0
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ams_emailWelcomeEmail]') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].[ams_emailWelcomeEmail]
GO
CREATE PROC dbo.ams_emailWelcomeEmail
@recordedByMemberID int,
@siteID int,
@createdFrom datetime,
@createdTo datetime,
@messageStatusCode varchar(10),
@messageWrapper varchar(max)

AS

declare @loginNetworkID int, @numRecipients int, @rc int, @messageTypeID int, 
	@mesageStatusID int, @sendingSiteResourceID int, @supportProviderEmail varchar(100),
	@supportProviderName varchar(100), @emailSubject varchar(200), @contentVersionID int,
	@messageID int, @rawcontent varchar(max), @messageToParse varchar(max),
	@fieldID int, @fieldName varchar(60)
declare @metadataFields TABLE (fieldName varchar(60), fieldID int NULL)
select @loginNetworkID = dbo.fn_getloginnetworkfromsiteid(@siteID)
		
IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
	DROP TABLE #tmpRecipients

SELECT distinct m.memberID, m.prefix, m.firstName, m.middlename, m.lastName, m.suffix, me.email, 0 as messageID
into #tmpRecipients
FROM dbo.ams_members as m
inner join dbo.ams_memberEmails as me on m.memberID = me.memberID
inner join dbo.ams_memberEmailTypes as met on me.emailTypeID = met.emailTypeID and met.emailTypeOrder = 1
inner join dbo.ams_memberSiteDefaults as msd on m.memberid = msd.memberid AND msd.siteID = @siteid
WHERE m.memberID = m.activeMemberID
and m.status = 'A'
and m.memberTypeID = 2
and m.dateCreated between @createdFrom and @createdTo
and len(me.Email) > 0
AND msd.status = 'A'
AND not exists (
	select mnp.mnpID 
	from dbo.ams_membernetworkprofiles as mnp
	inner join dbo.ams_networkprofiles as np on np.profileid = mnp.profileid and np.networkid = @loginNetworkID
	where mnp.memberID = m.memberID
	and mnp.status = 'A'
	AND np.status = 'A' 
)
and exists (
	select srr.groupID
	from dbo.sites as s
	inner join dbo.cms_siteResourceRightsCache AS srr on srr.resourceID = s.siteResourceID
	INNER JOIN dbo.cms_siteResourceFunctions AS srf ON srf.functionID = srr.functionID and srf.functionname = 'Login'
	INNER JOIN dbo.cms_siteResourceTypeFunctions AS srtf ON srtf.functionID = srf.functionID
	INNER JOIN dbo.cms_siteResourceTypes AS srt ON srt.resourceTypeID = srtf.resourceTypeID and srt.resourceType = 'Site'
	INNER JOIN dbo.cache_members_groups as mg on mg.groupID = srr.groupID and mg.memberID = m.memberid
	where s.siteID = @siteid
)

select @numRecipients = count(*) from #tmpRecipients

IF @numRecipients > 0 BEGIN
	select @messageTypeID = messageTypeID from platformMail.dbo.email_messageTypes where messageTypeCode = 'SITEWEL'
	select @sendingSiteResourceID = siteResourceID from dbo.sites where siteID = @siteID
	select @mesageStatusID = statusID from platformMail.dbo.email_statuses where statusCode = @messageStatusCode
	select TOP 1 @supportProviderName = net.supportProviderName, @supportProviderEmail = net.supportProviderEmail,
		@emailSubject = 'Access Instructions for ' + s.siteName, @contentVersionID = cv.contentVersionID,
		@rawcontent = cv.rawContent
		from dbo.networks as net
		inner join dbo.networkSites as ns on net.networkID = ns.networkID
		inner join dbo.sites as s on s.siteID = ns.siteID
		INNER JOIN dbo.cms_contentLanguages as cl ON cl.contentID = s.welcomeMessageContentID AND cl.languageID = 1
		INNER JOIN dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID AND cv.isActive = 1
		where s.siteID = @siteID 
		and ns.isLoginNetwork = 1
		IF @@ERROR <> 0 goto on_error

	-- add email_message
	EXEC @rc = platformMail.dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID, 
		@sendingSiteResourceID=@sendingSiteResourceID, @recordedByMemberID=@recordedByMemberID, 
		@fromName=@supportProviderName, @fromEmail=@supportProviderEmail, 
		@replyToEmail=@supportProviderEmail, @senderEmail=@supportProviderEmail, 
		@subject=@emailSubject, @contentVersionID=@contentVersionID, 
		@messageWrapper=@messageWrapper, @messageID=@messageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @messageID = 0 goto on_error

	-- update recipients table so we can brint it back to CF
	update #tmpRecipients set messageID = @messageID
		IF @@ERROR <> 0 goto on_error

	-- add recipients as I (not ready to be queued yet)
	insert into platformMail.dbo.email_messageRecipientHistory (messageID, memberID, 
		dateLastUpdated, toName, toEmail, emailStatusID, batchID, batchStartDate)
	select @messageID, memberID, getdate(), firstName + ' ' + lastName, email, @mesageStatusID, null, null
	from #tmpRecipients
		IF @@ERROR <> 0 goto on_error

	-- add any necessary metadata fields
	select @messageToParse = replace(@messageWrapper,'@@rawcontent@@',@rawcontent)
		IF @@ERROR <> 0 goto on_error
	insert into @metadataFields (fieldName)
	select distinct [Text]
	from dbo.fn_RegexMatches(@messageToParse,'(?<=\[\[)([^,\]]+)(?=,?([^\]]+)?\]\])')
		IF @@ERROR <> 0 goto on_error
	insert into platformMail.dbo.email_metadataFields (fieldName, isMergeField)
	select fieldName, 1
	from @metadataFields
		except
	select fieldName, isMergeField
	from platformMail.dbo.email_metadataFields
		IF @@ERROR <> 0 goto on_error
	update tmp
	set tmp.fieldID = MF.fieldID
	from @metadataFields as tmp
	inner join platformMail.dbo.email_metadataFields as MF on MF.fieldName = tmp.fieldName
	where MF.isMergeField = 1
		IF @@ERROR <> 0 goto on_error
	select @fieldID = min(fieldID) from @metadataFields where fieldName in ('firstname','lastname','prefix','fullName','extendedname')
	while @fieldID is not null BEGIN
		select @fieldName = fieldName from @metadataFields where fieldID = @fieldID
			IF @@ERROR <> 0 goto on_error
		insert into platformMail.dbo.email_messageMetadataFields (messageID, fieldID, memberID, fieldValue)
		select @messageID, @fieldID, memberID, 
			fieldValue = case @fieldName
				when 'firstname' then firstname
				when 'lastname' then lastname
				when 'prefix' then prefix 
				when 'fullName' then firstname + ' ' + lastname
				when 'extendedname' then firstname + isnull(' ' + nullif(middlename,''),'') + ' ' + lastname + isnull(' ' + nullif(suffix,''),'')
				end
		from #tmpRecipients
			IF @@ERROR <> 0 goto on_error
		select @fieldID = min(fieldID) from @metadataFields where fieldName in ('firstname','lastname','prefix','fullName','extendedname') and fieldID > @fieldID
	END
END

-- return recipients
select * 
from #tmpRecipients

goto on_done

on_error:
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients
	RETURN -1	

on_done:
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients
	RETURN 0

GO


CREATE PROC dbo.cms_qualifyAllLinks
@siteID int, 
@oldContent varchar(max), 
@qualURL varchar(400),
@newContent varchar(max) OUTPUT 

AS

declare @hostNameList varchar(max)
select @hostNameList = replace(dbo.pipelist(hostname),'.','\.')
from dbo.sitehostnames
where siteid = @siteID
and hostnameID <> mainhostnameID
group by siteid

declare @regex nvarchar(4000)
select @regex = '(?<=\b(?:src|href)\s*=\s*[''"]?)(?:https?://(?:' + isnull(@hostNameList,'') + '))?/'

select @newContent = null
select @newContent = dbo.fn_regexReplace(@oldContent,@regex,@qualURL)

RETURN 0
GO

