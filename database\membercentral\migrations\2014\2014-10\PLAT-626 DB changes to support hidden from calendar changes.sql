USE [memberCentral]
GO

ALTER TABLE dbo.ev_events ADD
	hiddenFromCalendar bit NULL CONSTRAINT DF_ev_events_hiddenFromCalendar DEFAULT 0


ALTER PROCEDURE [dbo].[ev_updateEvent] 
@eventID int,
@eventTypeID int,
@enteredByMemberID int,
@lockTimeZoneID int,
@isAllDayEvent bit,
@status char(1),
@reportCode varchar(15),
@internalNotes varchar(max),
@hiddenFromCalendar bit,
@emailContactContent bit,
@emailLocationContent bit,
@emailCancelContent	bit,
@emailTravelContent	bit

AS

update dbo.ev_events
set eventTypeID = @eventTypeID,
	lockTimeZoneID = @lockTimeZoneID,
	isAllDayEvent = @isAllDayEvent,
	[status] = @status,
	reportCode = nullif(@reportCode,''),
	internalNotes = @internalNotes,
	hiddenFromCalendar = nullif(@hiddenFromCalendar,''),
	emailContactContent = nullif(@emailContactContent,''),
	emailLocationContent = nullif(@emailLocationContent,''),
	emailCancelContent	= nullif(@emailCancelContent,''),
	emailTravelContent	= nullif(@emailTravelContent,'')
	
where eventid = @eventID
	IF @@ERROR <> 0 GOTO on_error

-- Get event information for activity log
DECLARE @eventSiteResourceID int
DECLARE @calApplicationInstanceID int

select @eventSiteResourceID = e.siteResourceID, 
		@calApplicationInstanceID = c.applicationInstanceID
from dbo.ev_calendarEvents ce
inner join ev_calendars c on c.calendarID = ce.sourceCalendarID 
inner join ev_events e on e.eventID = ce.sourceEventID
	AND e.eventID = @eventID

-- get applicationTypeID
DECLARE @applicationTypeID int
select @applicationTypeID = dbo.fn_getApplicationTypeIDFromName('Events')

-- create activity log entry
EXEC platformstats.dbo.act_recordLog @memberID=@enteredByMemberID, @activityType='update', 
	@applicationTypeID=@applicationTypeID, @applicationInstanceID=@calApplicationInstanceID,
	@supportSiteResourceID=@eventSiteResourceID, @supportMemberID=null, @supportMessage=null
	IF @@ERROR <> 0 GOTO on_error

-- normal exit
RETURN 0

-- error exit
on_error:
	RETURN -1
GO



ALTER PROC [dbo].[ev_getMetaByEventID]
@eventID int,
@languageID int

AS

select ce.calendarID, ev.eventID, ev.eventTypeID, et.eventType, ev.siteID, ev.enteredByMemberID, ev.isAllDayEvent, 
	ev.lockTimeZoneID, ev.GLAccountID, ev.altRegistrationURL, ev.status, ev.reportCode, ev.internalNotes, 
	ev.emailContactContent, ev.emailLocationContent, ev.emailCancelContent, ev.emailTravelContent,
	ai.applicationInstanceID as calendarApplicationInstanceID, ai.applicationInstanceName as calendarName,
	c.showAddCalendarLinks, 
	ev.eventContentID, eventcontent.contentTitle as eventContentTitle, eventcontent.rawContent as eventContent,
	ev.locationContentID, locationcontent.contentTitle as locationContentTitle, locationcontent.rawContent as locationContent,
	ev.travelContentID, travelcontent.contentTitle as travelContentTitle, travelcontent.rawContent as travelContent,
	ev.contactContentID, contactcontent.contentTitle as contactContentTitle, contactcontent.rawContent as contactContent,
	ev.cancellationPolicyContentID as cancelContentID, cancelcontent.contentTitle as cancelContentTitle, cancelcontent.rawContent as cancelContent,
	ev.informationContentID, informationcontent.contentTitle as informationContentTitle, informationcontent.rawContent as informationContent,
	ev.hiddenFromCalendar
from dbo.ev_events as ev
INNER JOIN dbo.ev_eventTypes AS et ON ev.eventTypeID = et.eventTypeID
INNER JOIN dbo.ev_calendarEvents as ce on ce.sourceEventID = ev.eventID and ce.calendarID = ce.sourceCalendarID
INNER JOIN dbo.ev_calendars as c on c.calendarID = ce.calendarID
inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = c.applicationInstanceID
cross apply dbo.fn_getContent(ev.eventcontentID,@languageID) as eventcontent
cross apply dbo.fn_getContent(ev.locationcontentID,@languageID) as locationcontent
cross apply dbo.fn_getContent(ev.travelcontentID,@languageID) as travelcontent
cross apply dbo.fn_getContent(ev.contactcontentID,@languageID) as contactcontent
cross apply dbo.fn_getContent(ev.cancellationPolicycontentID,@languageID) as cancelcontent
cross apply dbo.fn_getContent(ev.informationContentID,@languageID) as informationcontent
WHERE ev.status <> 'D'
AND ev.eventID = @eventID

RETURN 0
GO


ALTER PROCEDURE [dbo].[ev_createEvent] 
@siteID int,
@calendarID int,
@eventTypeID int,
@enteredByMemberID int,
@lockTimeZoneID int,
@isAllDayEvent bit,
@altRegistrationURL varchar(300),
@status char(1),
@reportCode varchar(15),
@hiddenFromCalendar bit,
@emailContactContent bit,
@emailLocationContent bit,
@emailCancelContent	bit,
@emailTravelContent	bit,
@eventID int OUTPUT

AS

declare @rc int
select @eventID = null

BEGIN TRAN

declare @appCreatedContentResourceTypeID int, @activeSiteResourceStatusID int, @defaultLanguageID int
declare @eventSiteResourceID int, @eventResourceTypeID int, @eventcontentID int, @eventcontentSiteResourceID int
declare @locationContentID int, @travelcontentid int, @contactcontentid int, @cancellationPolicyContentID int, @informationContentID int
declare @defaultGLAccountID int
select @eventResourceTypeID = dbo.fn_getResourceTypeId('Event')
select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')
select @activeSiteResourceStatusID = dbo.fn_getResourceStatusId('Active')
select @defaultLanguageID = defaultLanguageID from dbo.sites where siteID = @siteID

-- create a resourceID for the event
exec dbo.cms_createSiteResource
	@resourceTypeID = @eventResourceTypeID,
	@siteResourceStatusID = @activeSiteResourceStatusID,
	@siteID = @siteid,
	@isVisible = 1,
	@parentSiteResourceID = null,
	@siteResourceID   = @eventSiteResourceID OUTPUT

EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@defaultLanguageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='',
	@memberID=@enteredByMemberID,
	@contentID=@eventcontentID OUTPUT, 
	@siteResourceID=@eventcontentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- create eventcontentid
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@defaultLanguageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', 
	@memberID=@enteredByMemberID,
	@contentID=@eventcontentID OUTPUT, 
	@siteResourceID=@eventcontentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- create locationcontentid
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@defaultLanguageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', 
	@memberID=@enteredByMemberID,
	@contentID=@locationContentID OUTPUT, 
	@siteResourceID=@eventcontentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- create travelcontentid
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@defaultLanguageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', 
	@memberID=@enteredByMemberID,
	@contentID=@travelcontentid OUTPUT, 
	@siteResourceID=@eventcontentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- create contactcontentid
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@defaultLanguageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', 
	@memberID=@enteredByMemberID,
	@contentID=@contactcontentid OUTPUT, 
	@siteResourceID=@eventcontentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- create cancelcontentid
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@defaultLanguageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', 
	@memberID=@enteredByMemberID,
	@contentID=@cancellationPolicyContentID OUTPUT, 
	@siteResourceID=@eventcontentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- create informationcontentid
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@defaultLanguageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', 
	@memberID=@enteredByMemberID,
	@contentID=@informationContentID OUTPUT, 
	@siteResourceID=@eventcontentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- get GLAccountID from home calendar
select @defaultGLAccountID = defaultGLAccountID
from dbo.ev_calendars
where calendarID = @calendarID
	IF @@ERROR <> 0 OR @defaultGLAccountID is null GOTO on_error

-- add event
insert into dbo.ev_events (eventTypeID, siteID, siteResourceID, eventContentID, locationContentID, travelContentId, contactContentID, cancellationPolicyContentID, enteredByMemberID, lockTimeZoneID, isAllDayEvent, altRegistrationURL, GLAccountID, [status], reportCode, informationContentID, emailContactContent, emailLocationContent, emailCancelContent, emailTravelContent, hiddenFromCalendar)
values (@eventTypeID, @siteID, @eventSiteResourceID, @eventContentID, @locationContentID, @travelContentId, @contactContentID, @cancellationPolicyContentID, @enteredByMemberID, @lockTimeZoneID, @isAllDayEvent, @altRegistrationURL, @defaultGLAccountID, @status, nullif(@reportCode,''), @informationContentID, @emailContactContent, @emailLocationContent, @emailCancelContent, @emailTravelContent, @hiddenFromCalendar)
	IF @@ERROR <> 0 GOTO on_error
	select @eventID = SCOPE_IDENTITY()

-- add permissions for event management
declare @resourceRightID int, @calApplicationSiteResourceID int
declare @eventfunctionid int, @inheritedRightsFunctionID int, @calApplicationInstanceID int
select @calApplicationSiteResourceID = ai.siteResourceID, @calApplicationInstanceID = c.applicationInstanceID
	FROM dbo.ev_calendars AS c 
	INNER JOIN dbo.cms_applicationInstances AS ai ON c.applicationInstanceID = ai.applicationInstanceID
	WHERE c.calendarID = @calendarID
SELECT @eventfunctionid = dbo.fn_getResourceFunctionID('EditEvent',dbo.fn_getResourceTypeID('Event'))
SELECT @inheritedRightsFunctionID = dbo.fn_getResourceFunctionID('EditEventByDefault',dbo.fn_getResourceTypeID('Events'))
EXEC @rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@eventSiteResourceID, @functionID=@eventfunctionid, @inheritedRightsResourceID=@calApplicationSiteResourceID, @inheritedRightsFunctionID=@inheritedRightsFunctionID, @resourceRightID=@resourceRightID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
SELECT @eventfunctionid = dbo.fn_getResourceFunctionID('DeleteEvent',dbo.fn_getResourceTypeID('Event'))
SELECT @inheritedRightsFunctionID = dbo.fn_getResourceFunctionID('DeleteEventByDefault',dbo.fn_getResourceTypeID('Events'))
EXEC @rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@eventSiteResourceID, @functionID=@eventfunctionid, @inheritedRightsResourceID=@calApplicationSiteResourceID, @inheritedRightsFunctionID=@inheritedRightsFunctionID, @resourceRightID=@resourceRightID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
SELECT @eventfunctionid = dbo.fn_getResourceFunctionID('ViewRegistrants',dbo.fn_getResourceTypeID('Event'))
SELECT @inheritedRightsFunctionID = dbo.fn_getResourceFunctionID('ViewRegistrantsByDefault',dbo.fn_getResourceTypeID('Events'))
EXEC @rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@eventSiteResourceID, @functionID=@eventfunctionid, @inheritedRightsResourceID=@calApplicationSiteResourceID, @inheritedRightsFunctionID=@inheritedRightsFunctionID, @resourceRightID=@resourceRightID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
SELECT @eventfunctionid = dbo.fn_getResourceFunctionID('EditRegistrants',dbo.fn_getResourceTypeID('Event'))
SELECT @inheritedRightsFunctionID = dbo.fn_getResourceFunctionID('EditRegistrantsByDefault',dbo.fn_getResourceTypeID('Events'))
EXEC @rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@eventSiteResourceID, @functionID=@eventfunctionid, @inheritedRightsResourceID=@calApplicationSiteResourceID, @inheritedRightsFunctionID=@inheritedRightsFunctionID, @resourceRightID=@resourceRightID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
SELECT @eventfunctionid = dbo.fn_getResourceFunctionID('ManageFreeRates',dbo.fn_getResourceTypeID('Event'))
SELECT @inheritedRightsFunctionID = dbo.fn_getResourceFunctionID('ManageFreeRatesByDefault',dbo.fn_getResourceTypeID('Events'))
EXEC @rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@eventSiteResourceID, @functionID=@eventfunctionid, @inheritedRightsResourceID=@calApplicationSiteResourceID, @inheritedRightsFunctionID=@inheritedRightsFunctionID, @resourceRightID=@resourceRightID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
SELECT @eventfunctionid = dbo.fn_getResourceFunctionID('ManagePaidRates',dbo.fn_getResourceTypeID('Event'))
SELECT @inheritedRightsFunctionID = dbo.fn_getResourceFunctionID('ManagePaidRatesByDefault',dbo.fn_getResourceTypeID('Events'))
EXEC @rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@eventSiteResourceID, @functionID=@eventfunctionid, @inheritedRightsResourceID=@calApplicationSiteResourceID, @inheritedRightsFunctionID=@inheritedRightsFunctionID, @resourceRightID=@resourceRightID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- add to its main calendar
EXEC @rc = dbo.ev_createCalendarEvent @calendarID=@calendarID, @sourceCalendarID=@calendarID, @sourceCategoryID=null, @sourceEventID=@eventID, @ovCategoryID=null
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error


-- get applicationTypeID
DECLARE @applicationTypeID int
select @applicationTypeID = dbo.fn_getApplicationTypeIDFromName('Events')

-- create activity log entry
EXEC platformstats.dbo.act_recordLog @memberID=@enteredByMemberID, @activityType='post', 
	@applicationTypeID=@applicationTypeID, @applicationInstanceID=@calApplicationInstanceID,
	@supportSiteResourceID=@eventSiteResourceID, @supportMemberID=null, @supportMessage=null
	IF @@ERROR <> 0 GOTO on_error


-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @eventID = 0
	RETURN -1

GO



ALTER PROC [dbo].[ev_copyEvent]
@eventid int,
@copiedByMemberID int,
@newEventID int OUTPUT

AS

DECLARE @rc int
DECLARE @siteID int, @eventTypeID int, @lockTimeZoneID int, @isAllDayEvent bit, @hiddenFromCalendar bit,
	@altRegistrationURL varchar(300), @GLAccountID int, @calendarID int, @status char(1), @reportCode varchar(15),
	@internalNotes varchar(max)
DECLARE @eventContentID int, @contactContentID int, @locationContentID int, @cancelContentID int, @travelContentID int, @informationContentID int
DECLARE @neweventContentID int, @newcontactContentID int, @newlocationContentID int, @newcancelContentID int, @newtravelContentID int, @newinformationContentID int
DECLARE @languageID int, @isSSL bit, @isHTML bit, @contentTitle varchar(200), @contentDesc varchar(400), @rawcontent varchar(max)
DECLARE @emailContactContent bit, @emailLocationContent bit, @emailCancelContent bit, @emailTravelContent	bit
DECLARE @registrationID int, @registrationTypeID int, @startDate datetime, @endDate datetime, @registrantCap int,
	@ReplyToEmail varchar(200), @notifyEmail varchar(200), @isPriceBasedOnActual bit, @bulkCountByRate bit, @newregistrationID int
DECLARE @expirationContentID int, @registrantCapContentID int
DECLARE @newexpirationContentID int, @newregistrantCapContentID int
DECLARE @minofferingID int, @newofferingID int
DECLARE @minRateId int, @rateGroupingID int, @rateGLAccountID int, @rateName varchar(100),
	@rate money, @ratestartDate datetime, @rateendDate datetime, @newRateID int, @newRatesiteResourceID int,
	@ratereportCode varchar(15), @rateQty int
DECLARE @minBulkRateID int, @bulkrate money, @bulksiteResourceID int, @bulkrateqty int, @newBulkRateID int,
	@newBulkRatesiteResourceID int, @bulkresourceRightID int
DECLARE @siteResourceID int, @newrateGroupingID int
DECLARE @srr_rightsID int, @srr_roleid int, @srr_functionID int, @srr_groupid int, @srr_memberid int, @srr_include bit, @srr_inheritedRightsResourceID int, @srr_inheritedRightsFunctionID int, @resourceRightID int
DECLARE @minCustomID int, @newCustomID int, @showCredit bit
DECLARE @isOnlineMeeting bit, @onlineEmbedCode varchar(max), @onlineEmbedOverrideLink varchar(400), @onlineEnterStartTime datetime, @onlineEnterEndTime datetime
SELECT @newEventID = null

BEGIN TRAN

-- get the event we are copying
SELECT @siteID=siteID, @eventTypeID=eventTypeID, 
	@lockTimeZoneID=lockTimeZoneID, @isAllDayEvent=isAllDayEvent, @altRegistrationURL=altRegistrationURL,
	@GLAccountID=GLAccountID, @status=status, @reportCode=reportcode, @internalNotes=internalNotes,
	@emailContactContent = emailContactContent, @emailLocationContent = emailLocationContent,
	@emailCancelContent = emailCancelContent, @emailTravelContent = emailTravelContent,
	@hiddenFromCalendar = hiddenFromCalendar
	FROM dbo.ev_events
	WHERE eventID = @eventID
	IF @@ERROR <> 0 GOTO on_error
SELECT TOP 1 @calendarID = calendarID
	FROM dbo.ev_calendarEvents
	WHERE calendarID = sourceCalendarID
	AND sourceEventID = @eventID
	IF @@ERROR <> 0 GOTO on_error

-- create event
EXEC @rc = dbo.ev_createEvent @siteID=@siteID, @calendarID=@calendarID, @eventTypeID=@eventTypeID, 
	@enteredByMemberID=@copiedByMemberID, @lockTimeZoneID=@lockTimeZoneID, @isAllDayEvent=@isAllDayEvent, 
	@altRegistrationURL=@altRegistrationURL, @status=@status, @reportCode=@reportCode, @hiddenFromCalendar=@hiddenFromCalendar,
	@emailContactContent=@emailContactContent, @emailLocationContent=@emailLocationContent,
	@emailCancelContent=@emailCancelContent, @emailTravelContent=@emailTravelContent,
	@eventID=@newEventID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

update dbo.ev_events
set GLAccountID = @GLAccountID,
	internalNotes = @internalNotes
where eventID = @newEventID
	IF @@ERROR <> 0 GOTO on_error

select @neweventContentID=eventContentID, @newcontactContentID=contactContentID, @newlocationContentID=locationContentID,
	@newcancelContentID=cancellationPolicyContentID, @newtravelContentID=travelContentID, @newinformationContentID=informationContentID
FROM dbo.ev_events
where eventID = @newEventID
	IF @@ERROR <> 0 GOTO on_error

select @eventContentID=eventContentID, @contactContentID=contactContentID, @locationContentID=locationContentID,
	@cancelContentID=cancellationPolicyContentID, @travelContentID=travelContentID, @informationContentID=informationContentID
FROM dbo.ev_events
where eventID = @EventID
	IF @@ERROR <> 0 GOTO on_error

-- make event inactive
UPDATE dbo.ev_events
SET [status] = 'I'
WHERE eventID = @newEventID
	IF @@ERROR <> 0 GOTO on_error

-- copy event category
INSERT INTO dbo.ev_eventcategories (eventID, categoryID)
select @newEventID, categoryID
from dbo.ev_eventcategories
where eventID = @eventID
	IF @@ERROR <> 0 GOTO on_error

-- copy event times
INSERT INTO dbo.ev_Times (eventid, timeZoneID, startTime, endTime)
select @newEventID, timeZoneID, startTime, endTime
from dbo.ev_times
where eventid = @eventID
	IF @@ERROR <> 0 GOTO on_error

-- copy content objects
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle='Copy of ' + contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@eventContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@neweventContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@contactContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newcontactContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@locationContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newlocationContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@cancelContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newcancelContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@travelContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newtravelContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@informationContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newinformationContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- copy sponsors
insert into dbo.ev_sponsors (eventID, sponsorContentID, sponsorOrder)
select @newEventID, sponsorContentID, sponsorOrder
from dbo.ev_sponsors
where eventID = @eventID
	IF @@ERROR <> 0 GOTO on_error

-- does orig event have registration?
SELECT @registrationID=registrationid, @registrationTypeID=registrationTypeID, @startDate=startdate, 
	@endDate=endDate, @registrantCap=registrantCap, @ReplyToEmail=ReplyToEmail, @notifyEmail=notifyEmail, 
	@isPriceBasedOnActual=isPriceBasedOnActual, @bulkCountByRate=bulkCountByRate, @expirationContentID=expirationContentID, 
	@registrantCapContentID=registrantCapContentID, @isOnlineMeeting=isOnlineMeeting, @onlineEmbedCode=onlineEmbedCode,
	@onlineEmbedOverrideLink=onlineEmbedOverrideLink, @onlineEnterStartTime=onlineEnterStartTime, @onlineEnterEndTime=onlineEnterEndTime,
	@showCredit=showCredit
	from dbo.ev_registration
	where eventID = @eventID
	and [status] <> 'D'
IF @registrationID is not null BEGIN

	-- insert registration
	EXEC @rc = dbo.ev_createRegistration @eventID=@newEventID, @registrationTypeID=@registrationTypeID, 
			@startDate=@startDate, @endDate=@endDate, @registrantCap=@registrantCap, @ReplyToEmail=@ReplyToEmail,
			@notifyEmail=@notifyEmail, @isPriceBasedOnActual=@isPriceBasedOnActual, @bulkCountByRate=@bulkCountByRate,
			@registrationID=@newregistrationID OUTPUT
		IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

	select @newexpirationContentID=expirationContentID, @newregistrantCapContentID=registrantCapContentID
	FROM dbo.ev_registration
	where registrationID = @newregistrationID
		IF @@ERROR <> 0 GOTO on_error

	-- copy content objects
	select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@expirationContentID,1)
	EXEC @rc = dbo.cms_updateContent @contentID=@newexpirationContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
		IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
	select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@registrantCapContentID,1)
	EXEC @rc = dbo.cms_updateContent @contentID=@newregistrantCapContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
		IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

	-- if reg type
	IF @registrationTypeID = 1 BEGIN

		-- other registration fields
		update dbo.ev_registration
		set isOnlineMeeting = @isOnlineMeeting,
			onlineEmbedCode = @onlineEmbedCode,
			onlineEmbedOverrideLink = @onlineEmbedOverrideLink,
			onlineEnterStartTime = @onlineEnterStartTime,
			onlineEnterEndTime = @onlineEnterEndTime,
			showCredit = @showCredit
		where registrationID = @newregistrationID
			IF @@ERROR <> 0 GOTO on_error

		-- merchant profiles
		insert into dbo.ev_registrationMerchantProfiles (registrationID, profileID)
		select @newregistrationID, rmp.profileID
		from dbo.ev_registrationMerchantProfiles as rmp
		inner join dbo.mp_profiles as mp on mp.profileID = rmp.profileID
		inner join dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
		where rmp.registrationID = @registrationID
		and mp.status = 'A'
		and mp.allowPayments = 1
		and g.isActive = 1
			IF @@ERROR <> 0 GOTO on_error

		-- credit offered
		select @minofferingID = min(offeringID) from dbo.crd_offerings where eventID = @eventID
		while @minofferingID is not null BEGIN
			INSERT INTO dbo.crd_offerings (ASID, statusID, ApprovalNum, offeredStartDate, offeredEndDate, completeByDate, isCreditRequired, isIDRequired, isCreditDefaulted, eventID)
			SELECT ASID, statusID, ApprovalNum, offeredStartDate, offeredEndDate, completeByDate, isCreditRequired, isIDRequired, isCreditDefaulted, @newEventID
			FROM dbo.crd_offerings
			WHERE offeringID = @minofferingID
				IF @@ERROR <> 0 GOTO on_error
				SELECT @newofferingID = SCOPE_IDENTITY()

			INSERT INTO dbo.crd_offeringTypes (offeringID, ASTID, creditValue)
			SELECT @newofferingID, ASTID, creditValue
			FROM dbo.crd_offeringTypes
			WHERE offeringID = @minofferingID
				IF @@ERROR <> 0 GOTO on_error

			select @minofferingID = min(offeringID) from dbo.crd_offerings where eventID = @eventID and offeringID > @minofferingID
		END

		-- rate groupings
		insert into dbo.ev_rateGrouping (rateGrouping, registrationID, rateGroupingOrder)
		select rateGrouping, @newregistrationID, rateGroupingOrder
		from dbo.ev_rateGrouping
		where registrationID = @registrationID
			IF @@ERROR <> 0 GOTO on_error

		-- active rates and permissions
		select @minRateId = min(r.rateID) 
			from dbo.ev_rates as r
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			where r.registrationID = @registrationID
			and r.parentRateID is null
		while @minRateID is not null BEGIN
			select @rateGroupingID = null, @rateGLAccountID = null, @rateName = null, 
				@ratereportCode = null, @rate = null, @ratestartDate = null, @rateendDate = null, 
				@siteResourceID = null, @rateqty = null

			select @rateGroupingID=rateGroupingID, @rateGLAccountID=GLAccountID, @rateName=rateName, 
				@ratereportCode=reportCode, @rate=rate, @ratestartDate=startDate, @rateendDate=endDate, 
				@siteResourceID=siteResourceID, @rateqty=bulkQty
			from dbo.ev_rates 
			where rateID = @minRateID

			select @newrateGroupingID = rg1.rateGroupingID
			from dbo.ev_rateGrouping as rg1
			inner join dbo.ev_rateGrouping as rg2 
				on rg2.rateGrouping = rg1.rateGrouping
				and rg1.registrationID = @newregistrationID
				and rg2.registrationID = @registrationID
				and isnull(rg2.rateGroupingID,0) = isnull(@rateGroupingID,0)
			
			IF @rateName is not null and @rate is not null BEGIN
				EXEC @rc = dbo.ev_createRate @registrationID=@newregistrationID,  
					@rateGroupingID=@newrateGroupingID, @GLAccountID=@rateGLAccountID, @rateName=@rateName, 
					@reportCode=@ratereportCode, @rate=@rate, @startDate=@ratestartDate, @endDate=@rateendDate,
					@parentRateID=null, @qty=@rateqty, 
					@rateID=@newRateID OUTPUT, @siteResourceID=@newRatesiteResourceID OUTPUT
					IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

				select @minBulkRateID = min(rateID)
				from dbo.ev_rates
				where parentRateID = @minRateID

				while @minBulkRateID is not null begin
					select @bulkrate=rate, @bulksiteResourceID=siteResourceID, @bulkrateqty=bulkQty
					from dbo.ev_rates 
					where rateID = @minBulkRateID

					EXEC @rc = dbo.ev_createRate @registrationID=@newregistrationID,  
						@rateGroupingID=@newrateGroupingID, @GLAccountID=@rateGLAccountID, @rateName=@rateName, 
						@reportCode=@ratereportCode, @rate=@rate, @startDate=@ratestartDate, @endDate=@rateendDate,
						@parentRateID=@newRateID, @qty=@bulkrateqty, 
						@rateID=@newBulkRateID OUTPUT, @siteResourceID=@newBulkRatesiteResourceID OUTPUT
						IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

					select @minBulkRateID = min(rateID)
					from dbo.ev_rates
					where parentRateID = @minRateID
					and rateID > @minBulkRateID
				end

				-- copy resource rights for this resource		
				SELECT @srr_rightsID = null	
				SELECT @srr_rightsID = min(resourceRightsID) from dbo.cms_siteResourceRights where resourceID = @siteResourceID
				WHILE @srr_rightsID IS NOT NULL BEGIN
					SELECT @srr_roleid=roleID, @srr_functionID=functionID, @srr_groupid=groupID, 
						@srr_memberid=memberID, @srr_include=[include], @srr_inheritedRightsResourceID=inheritedRightsResourceID, 
						@srr_inheritedRightsFunctionID=inheritedRightsFunctionID
					FROM dbo.cms_siteResourceRights
					WHERE resourceRightsID = @srr_rightsID

					EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@newRatesiteResourceID, @include=@srr_include, 
						@functionID=@srr_functionID, @roleID=@srr_roleid, @groupID=@srr_groupid, @memberID=@srr_memberid, 
						@inheritedRightsResourceID=@srr_inheritedRightsResourceID, @inheritedRightsFunctionID=@srr_inheritedRightsFunctionID, 
						@resourceRightID=@resourceRightID OUTPUT
					IF @@ERROR <> 0 GOTO on_error

					select @minBulkRateID = min(rateID)
					from dbo.ev_rates
					where parentRateID = @newRateID

					while @minBulkRateID is not null begin
						select @bulksiteResourceID=siteResourceID
						from dbo.ev_rates 
						where rateID = @minBulkRateID

						EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@bulksiteResourceID, @include=@srr_include, 
							@functionID=@srr_functionID, @roleID=@srr_roleid, @groupID=@srr_groupid, @memberID=@srr_memberid, 
							@inheritedRightsResourceID=@srr_inheritedRightsResourceID, @inheritedRightsFunctionID=@srr_inheritedRightsFunctionID, 
							@resourceRightID=@bulkresourceRightID OUTPUT
						IF @@ERROR <> 0 GOTO on_error

						select @minBulkRateID = min(rateID)
						from dbo.ev_rates
						where parentRateID = @minRateID
						and rateID > @newRateID
					end
		
					SELECT @srr_rightsID = min(resourceRightsID) from dbo.cms_siteResourceRights where resourceID = @siteResourceID and resourceRightsID > @srr_rightsID
				END

			END
			
			select @minRateId = min(rateID) 
				from dbo.ev_rates as r
				inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
				inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
					and srs.siteResourceStatusDesc = 'Active'
				where r.registrationID = @registrationID
				and r.parentRateID is null
				and rateID > @minRateID
		END

		-- custom questions
		SELECT @minCustomID = min(CustomID) FROM dbo.ev_registrationCustom WHERE registrationID = @registrationID
		WHILE @minCustomID is not null BEGIN
			INSERT INTO dbo.ev_registrationCustom (registrationID, areaID, FieldDesc, titleOnInvoice, customTypeID, IsRequired, RequiredMsg, fieldOrder, Status, amount, offerQTY, GLAccountID)
			SELECT	@newregistrationID, areaID, FieldDesc, titleOnInvoice, customTypeID, IsRequired, RequiredMsg, fieldOrder, Status, amount, offerQTY, GLAccountID
			FROM	dbo.ev_registrationCustom	
			WHERE	customID = @minCustomID
				IF @@ERROR <> 0 GOTO on_error
				SELECT @newCustomID = SCOPE_IDENTITY()
				
			INSERT INTO dbo.ev_customOptions(customID, optionDesc, optionOrder, status, amount)
			SELECT	@newCustomID, optionDesc, optionOrder, status, amount
			FROM	dbo.ev_customOptions
			WHERE	customID = @minCustomID
				IF @@ERROR <> 0 GOTO on_error			
	
			SELECT @minCustomID = min(CustomID) FROM dbo.ev_registrationCustom WHERE registrationID = @registrationID and CustomID > @minCustomID	
		END

	END

END

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @newEventID = 0
	RETURN -1
GO




ALTER FUNCTION [dbo].[fn_ev_getEventsOnCalendar] (
	@calendarID int,
	@languageID int,
	@siteID varchar(20),
	@startDate datetime = '1/1/2000',
	@endDate datetime = '12/31/2025'
)
RETURNS TABLE
AS
RETURN (

	-- do not exclude deleted events. We show them in the admin event grids.

	select events.eventID, events.status, 
		isPastEvent = case 
			WHEN Datediff(DAY,times.endTime,getdate()) > 0 THEN cast(1 AS bit)
			ELSE cast(0 AS bit)
			END,
		eventsonCalendar.hereBecause,
		eventsonCalendar.sourceCalendarID,
		times.startTime, times.endTime, times.timeZoneID,
		timezones.timeZoneCode, timezones.timeZoneAbbr, isnull(lockTimes.startTime,times.startTime) as displayStartTime,
		isnull(lockTimes.endTime,times.endTime) as displayEndTime,
		isnull(lockTimes.timeZoneID,times.timeZoneID) as displayTimeZoneID,
		isnull(lockTimeZones.timeZoneCode,timezones.timeZoneCode) as displayTimeZoneCode,
		isnull(lockTimeZones.timeZoneAbbr,timezones.timeZoneAbbr) as displayTimeZoneAbbr,
		events.siteResourceID,events.isAllDayEvent, categories.categoryID, isnull(categories.category,'') as categoryName,
		isnull(categories.calColor,'') as categoryColor, 
		isnull(events.altRegistrationURL,'') as altRegistrationURL,
		events.eventContentID, events.locationContentID, events.travelContentID, events.contactContentID,
		eventsOnCalendar.hiddenFromCalendar
	from dbo.ev_categories as categories
	inner join dbo.ev_calendars as cal on cal.calendarID = @calendarID
	inner join dbo.sites as s on s.siteID = cal.siteID
	inner join (
		select priEventOptIns.eventID, priEventOptIns.categoryID, priEventOptIns.sourceCalendarID, e.hiddenFromCalendar,
		hereBecause = case
			when minPriority = 1 then 'homeCalendar'
			when minPriority = 2 then 'optInEvent'
			when minPriority = 3 then 'optInCategory'
			when minPriority = 4 then 'optInCalendar'
		end
		from (
			select eventID, sourceCalendarID, categoryID, priority, min(priority) over (Partition by eventID) as minPriority
			from (
				select calEvents.sourceEventID as eventID, cats.categoryID as categoryID, 
					calEvents.sourceCalendarID, 1 as priority
				from dbo.ev_calendarEvents as calEvents
				inner join dbo.ev_eventCategories as cats ON cats.eventID = calEvents.sourceEventID
				where calEvents.calendarID = @calendarID AND calEvents.calendarID = calEvents.sourceCalendarID
				
				UNION

				-- Event Level Opt-in
				select calEvents.sourceEventID as eventID, isnull(calEvents.ovCategoryID,cats.categoryID) as categoryID, 
					calEvents.sourceCalendarID, 2 as priority
				from dbo.ev_calendarEvents as calEvents
				INNER join dbo.ev_eventCategories as cats on cats.eventID = calEvents.sourceEventID
					and calEvents.calendarID = @calendarID
					and calEvents.sourceCalendarID <> @calendarID
					and calEvents.sourceEventID is not null 
				
				UNION

				-- Category Level Opt-in
				select calEvents1.sourceEventID as eventID, isnull(calEvents.ovCategoryID,cats.categoryID) as categoryID, 
					calEvents.sourceCalendarID, 3 as priority
				from dbo.ev_calendarEvents as calEvents
				INNER join dbo.ev_calendarEvents as calEvents1 on calEvents.calendarID = @calendarID
					and calEvents.sourceCalendarID = calEvents1.calendarID
					and calEvents1.calendarID = calEvents1.sourceCalendarID
					and calEvents.sourceEventID is null
				INNER join dbo.ev_eventCategories as cats
					on cats.eventID = calEvents1.sourceEventID
					and cats.categoryID = calEvents.sourceCategoryID

				UNION
	
				-- Calendar Level Opt-in
				select calEvents1.sourceEventID as eventID, isnull(calEvents.ovCategoryID,cats.categoryID) as categoryID, 
					calEvents.sourceCalendarID, 4 as priority
				from dbo.ev_calendarEvents as calEvents
				INNER join dbo.ev_calendarEvents as calEvents1 on calEvents.calendarID = @calendarID 
					and calEvents.sourceCalendarID = calEvents1.calendarID 
					and calEvents1.calendarID = calEvents1.sourceCalendarID
					and calEvents.sourceCategoryID is null
					and calEvents.sourceEventID is null
				INNER join dbo.ev_eventCategories as cats on cats.eventID = calEvents1.sourceEventID
			) as eventOptIns
		) as priEventOptIns
		inner join dbo.ev_events as e on e.eventid = priEventOptIns.eventid
		inner join dbo.cms_siteResources as sr on e.siteResourceID = sr.siteResourceID 
		INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
		where priority = minpriority
	) as eventsOnCalendar on eventsOnCalendar.categoryID = categories.categoryID
	inner join dbo.ev_events as events on eventsOnCalendar.eventID = events.eventID
	inner join dbo.ev_times as times on times.eventID = events.eventID and times.timeZoneID = s.defaultTimeZoneID
	inner join dbo.timeZones on timeZones.timeZoneID = times.timeZoneID
	left outer join dbo.ev_times as lockTimes
		inner join dbo.timeZones as lockTimeZones on lockTimeZones.timeZoneID = lockTimes.timeZoneID
		on lockTimes.eventID = events.eventID and lockTimes.timeZoneID = events.lockTimeZoneID
	where isnull(lockTimes.startTime,times.startTime) between @startDate and @endDate

)
GO

ALTER FUNCTION [dbo].[fn_ev_getEventsOnCalendar] (
	@calendarID int,
	@languageID int,
	@siteID varchar(20),
	@startDate datetime = '1/1/2000',
	@endDate datetime = '12/31/2025'
)
RETURNS TABLE
AS
RETURN (

	-- do not exclude deleted events. We show them in the admin event grids.

	select events.eventID, events.status, 
		isPastEvent = case 
			WHEN Datediff(DAY,times.endTime,getdate()) > 0 THEN cast(1 AS bit)
			ELSE cast(0 AS bit)
			END,
		eventsonCalendar.hereBecause,
		eventsonCalendar.sourceCalendarID,
		times.startTime, times.endTime, times.timeZoneID,
		timezones.timeZoneCode, timezones.timeZoneAbbr, isnull(lockTimes.startTime,times.startTime) as displayStartTime,
		isnull(lockTimes.endTime,times.endTime) as displayEndTime,
		isnull(lockTimes.timeZoneID,times.timeZoneID) as displayTimeZoneID,
		isnull(lockTimeZones.timeZoneCode,timezones.timeZoneCode) as displayTimeZoneCode,
		isnull(lockTimeZones.timeZoneAbbr,timezones.timeZoneAbbr) as displayTimeZoneAbbr,
		events.siteResourceID,events.isAllDayEvent, categories.categoryID, isnull(categories.category,'') as categoryName,
		isnull(categories.calColor,'') as categoryColor, 
		isnull(events.altRegistrationURL,'') as altRegistrationURL,
		events.eventContentID, events.locationContentID, events.travelContentID, events.contactContentID,
		eventsOnCalendar.hiddenFromCalendar
	from dbo.ev_categories as categories
	inner join dbo.ev_calendars as cal on cal.calendarID = @calendarID
	inner join dbo.sites as s on s.siteID = cal.siteID
	inner join (
		select priEventOptIns.eventID, priEventOptIns.categoryID, priEventOptIns.sourceCalendarID, e.hiddenFromCalendar,
		hereBecause = case
			when minPriority = 1 then 'homeCalendar'
			when minPriority = 2 then 'optInEvent'
			when minPriority = 3 then 'optInCategory'
			when minPriority = 4 then 'optInCalendar'
		end
		from (
			select eventID, sourceCalendarID, categoryID, priority, min(priority) over (Partition by eventID) as minPriority
			from (
				select calEvents.sourceEventID as eventID, cats.categoryID as categoryID, 
					calEvents.sourceCalendarID, 1 as priority
				from dbo.ev_calendarEvents as calEvents
				inner join dbo.ev_eventCategories as cats ON cats.eventID = calEvents.sourceEventID
				where calEvents.calendarID = @calendarID AND calEvents.calendarID = calEvents.sourceCalendarID
				
				UNION

				-- Event Level Opt-in
				select calEvents.sourceEventID as eventID, isnull(calEvents.ovCategoryID,cats.categoryID) as categoryID, 
					calEvents.sourceCalendarID, 2 as priority
				from dbo.ev_calendarEvents as calEvents
				INNER join dbo.ev_eventCategories as cats on cats.eventID = calEvents.sourceEventID
					and calEvents.calendarID = @calendarID
					and calEvents.sourceCalendarID <> @calendarID
					and calEvents.sourceEventID is not null 
				
				UNION

				-- Category Level Opt-in
				select calEvents1.sourceEventID as eventID, isnull(calEvents.ovCategoryID,cats.categoryID) as categoryID, 
					calEvents.sourceCalendarID, 3 as priority
				from dbo.ev_calendarEvents as calEvents
				INNER join dbo.ev_calendarEvents as calEvents1 on calEvents.calendarID = @calendarID
					and calEvents.sourceCalendarID = calEvents1.calendarID
					and calEvents1.calendarID = calEvents1.sourceCalendarID
					and calEvents.sourceEventID is null
				INNER join dbo.ev_eventCategories as cats
					on cats.eventID = calEvents1.sourceEventID
					and cats.categoryID = calEvents.sourceCategoryID

				UNION
	
				-- Calendar Level Opt-in
				select calEvents1.sourceEventID as eventID, isnull(calEvents.ovCategoryID,cats.categoryID) as categoryID, 
					calEvents.sourceCalendarID, 4 as priority
				from dbo.ev_calendarEvents as calEvents
				INNER join dbo.ev_calendarEvents as calEvents1 on calEvents.calendarID = @calendarID 
					and calEvents.sourceCalendarID = calEvents1.calendarID 
					and calEvents1.calendarID = calEvents1.sourceCalendarID
					and calEvents.sourceCategoryID is null
					and calEvents.sourceEventID is null
				INNER join dbo.ev_eventCategories as cats on cats.eventID = calEvents1.sourceEventID
			) as eventOptIns
		) as priEventOptIns
		inner join dbo.ev_events as e on e.eventid = priEventOptIns.eventid
		inner join dbo.cms_siteResources as sr on e.siteResourceID = sr.siteResourceID 
		INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
		where priority = minpriority
	) as eventsOnCalendar on eventsOnCalendar.categoryID = categories.categoryID
	inner join dbo.ev_events as events on eventsOnCalendar.eventID = events.eventID
	inner join dbo.ev_times as times on times.eventID = events.eventID and times.timeZoneID = s.defaultTimeZoneID
	inner join dbo.timeZones on timeZones.timeZoneID = times.timeZoneID
	left outer join dbo.ev_times as lockTimes
		inner join dbo.timeZones as lockTimeZones on lockTimeZones.timeZoneID = lockTimes.timeZoneID
		on lockTimes.eventID = events.eventID and lockTimes.timeZoneID = events.lockTimeZoneID
	where isnull(lockTimes.startTime,times.startTime) between @startDate and @endDate

)
GO



ALTER PROCEDURE [dbo].[ev_eventListXMLByDateRange]
	@begindate datetime,
	@enddate datetime,
	@calendarID int,
	@languageID int,
	@siteID int,
	@groupPrintID int = null,
	@includeHiddenEvents int = null
AS

set nocount on

DECLARE @returnXML xml, @returnCategoryXML xml, @isSWL bit

select @isSWL = isSWL
	from ev_calendars
	where calendarID = @calendarID

IF OBJECT_ID('tempdb..#tempevents') IS NOT NULL 
	DROP TABLE #tempevents

CREATE TABLE #tempevents (
		autoID int IDENTITY(1,1),
		isSWL bit,
		eventID int,
		startTime datetime, 
		endTime datetime, 
		timezoneID int, 
		timeZoneCode varchar(50), 
		timeZoneAbbr varchar(10),
		displaystartTime datetime,
		displayendTime datetime, 
		displaytimezoneID int, 
		displaytimeZoneCode varchar(50), 
		displaytimeZoneAbbr varchar(10),
		altregistrationURL  varchar(500),
		isAllDayEvent bit,
		categoryID int, 
		categoryName varchar(100), 
		categoryColor varchar(100),
		eventTitle varchar(250),
		locationTitle varchar(200)
		)


declare @viewableCalendars TABLE (calendarID int PRIMARY KEY, siteResourceID int, calendarName varchar(max))

insert into @viewableCalendars (calendarID, siteResourceID, calendarName)
exec dbo.[ev_getCalendarsViewableByGroupPrintID]
	@siteID = @siteID ,
	@groupPrintID = @groupPrintID


insert into #tempevents (eventID, isSWL, startTime, endTime, timezoneID, timeZoneCode, 
	timeZoneAbbr, displaystartTime, displayendTime, displaytimezoneID, displaytimeZoneCode, 
	displaytimeZoneAbbr, altregistrationURL, isAllDayEvent, categoryID, categoryName, 
	categoryColor, eventTitle, locationTitle)
select events.eventID,
		0 as isSWL,
		CONVERT(varchar(30),
		events.startTime,121) as startTime, 
		CONVERT(varchar(30),events.endTime,121) as endTime, 
		events.timezoneID, 
		events.timeZoneCode, 
		events.timeZoneAbbr,
		CONVERT(varchar(30),
		events.displaystartTime,121) as displaystartTime,
		CONVERT(varchar(30),events.displayendTime,121) as displayendTime, 
		events.displaytimezoneID, 
		events.displaytimeZoneCode, 
		events.displaytimeZoneAbbr,
		events.altregistrationURL,
		events.isAllDayEvent,
		events.categoryID , 
		events.categoryName , 
		events.categoryColor,
		isnull(eventContent.contentTitle,'') as eventTitle,
		isnull(locationContent.contentTitle,'') as locationTitle 
	from dbo.fn_ev_getEventsOnCalendar(@calendarID,@languageID,@siteID,DEFAULT,DEFAULT) as events 
	inner join @viewableCalendars vc
		on vc.calendarID = events.sourceCalendarID
	cross apply dbo.fn_getContent(events.eventContentID,1) as eventContent
	cross apply dbo.fn_getContent(events.locationContentID,1) as locationContent
	where ((startTime between @begindate and @enddate) or (endTime between @begindate and @enddate) or (startTime < @begindate and endTime > @enddate))
		AND events.status = 'A'
	and (events.hiddenFromCalendar is null OR events.hiddenFromCalendar in (0, @includeHiddenEvents))
	order by events.startTime, events.isAllDayEvent

if @isSWL = 1 
BEGIN
	insert into #tempevents (eventID, isSWL, startTime, endTime, timezoneID, timeZoneCode, 
		timeZoneAbbr, displaystartTime, displayendTime, displaytimezoneID, displaytimeZoneCode, 
		displaytimeZoneAbbr, altregistrationURL, isAllDayEvent, categoryID, categoryName, 
		categoryColor, eventTitle, locationTitle)

	select matchingSeminars.seminarID as eventID,
		isSWL = 1,
		startTime = matchingSeminars.dateStart,
		endTime = matchingSeminars.dateEnd,
		matchingSeminars.timezoneID,
		matchingSeminars.timezoneCode,
		matchingSeminars.timezoneAbbr,
		displayStartTime = cast(replace(replace(startdate.value('(./node())[1]','varchar(100)'),'T',' '),'-6:0','') as datetime),
		displayEndTime = cast(replace(replace(enddate.value('(./node())[1]','varchar(100)'),'T',' '),'-6:0','') as datetime),
		displaytimezoneID = matchingSeminars.timezoneID,
		displaytimezoneCode = matchingSeminars.timezoneCode,
		displaytimezoneAbbr = matchingSeminars.timezoneAbbr,
		altregistrationURL = '',
		isAllDayEvent = cast(0 as bit),
		matchingSeminars.categoryID,
		isnull(matchingSeminars.category,'') as categoryName,
		isnull(matchingSeminars.calColor,'') as categoryColor, 
		matchingSeminars.seminarName as eventTitle ,
		'' as locationTitle
	from (
		SELECT 
			s.seminarID,
			s.seminarName,
			swl.dateStart,
			swl.dateEnd,
			cast(swl.wddxTimeZones as xml) as wddxTimeZones,
			swl.offerCredit,
			s.offerCertificate,
			stz.code,
			tz.*,
			cat.*
		FROM dbo.ev_calendars as cal
		inner join membercentral.dbo.sites sites
			on sites.siteID = @siteID
			and cal.calendarID = @calendarID
			and cal.isSWL = 1
		inner join ev_categories cat
			on cat.categoryID = cal.categoryID
		inner join dbo.organizations o
			on o.orgID = sites.orgID
		inner join membercentral.dbo.timezones tz
			on sites.defaultTimeZoneID = tz.timeZoneID
		INNER JOIN tlasites.seminarweb.dbo.tblParticipants p
			on p.orgcode = o.orgcode
		INNER JOIN tlasites.seminarweb.dbo.tblSeminarsOptIn soi
			on p.participantID = soi.participantID
		inner join tlasites.seminarweb.dbo.tblSeminars as s
			on soi.seminarID = s.seminarID
			and s.isPublished = 1
			AND s.isDeleted = 0
		INNER JOIN tlasites.seminarweb.dbo.vw_tblSeminarsSWLive swl
			on swl.seminarID = s.seminarID
			AND swl.dateStart between @begindate and @enddate
		inner join tlasites.seminarweb.dbo.tblTimeZones stz
			on tz.timeZoneCode = stz.id
		) as matchingSeminars
	CROSS APPLY matchingSeminars.wddxTimeZones.nodes(N'/wddxPacket/data/struct/var[@name=''START'']//var[@name=sql:column("matchingSeminars.code")]/dateTime') startt(startdate)
	CROSS APPLY matchingSeminars.wddxTimeZones.nodes(N'/wddxPacket/data/struct/var[@name=''END'']//var[@name=sql:column("matchingSeminars.code")]/dateTime') endt(enddate)
END

select @returnXML = (
	select 
		dateRange.calendarID, 
		CONVERT(varchar(30),dateRange.startDate,121) as startDate, 
		CONVERT(varchar(30),dateRange.enddate,121) as endDate, 
		events.isSWL,
		events.eventID ,
		CONVERT(varchar(30),events.startTime,121) as startTime, 
		CONVERT(varchar(30),events.endTime,121) as endTime , 
		events.timezoneID , 
		events.timeZoneCode , 
		events.timeZoneAbbr ,
		CONVERT(varchar(30),events.displaystartTime,121) as displaystartTime ,
		CONVERT(varchar(30),events.displayendTime,121) as displayendTime , 
		events.displaytimezoneID , 
		events.displaytimeZoneCode , 
		events.displaytimeZoneAbbr ,
		events.altregistrationURL  ,
		events.isAllDayEvent ,
		events.categoryID , 
		events.categoryName , 
		events.categoryColor ,
		events.eventTitle ,
		events.locationTitle
	from #tempevents events
	inner join ( select @calendarID as calendarID, @begindate as startDate, @enddate as endDate) dateRange
		on 1=1 
	order by startTime, isAllDayEvent
	for xml auto, root('calendar')
)
select isnull(@returnXML,cast('<calendar/>' as xml)) as dateXML

select @returnCategoryXML = (
	select distinct categoryID, categoryName, categoryColor
	from #tempevents as category
	order by categoryName
	for xml auto, root('categories')
)
select isnull(@returnCategoryXML,cast('<categories/>' as xml)) as categoryXML

IF OBJECT_ID('tempdb..#tempevents') IS NOT NULL 
	DROP TABLE #tempevents

set nocount off

RETURN 0
GO


ALTER PROCEDURE [dbo].[ev_eventListXML]
	@month int,
	@year int,
	@calendarID int,
	@languageID int,
	@siteID int,
	@groupPrintID int = null,
	@includeHiddenEvents int = null
AS
BEGIN
	SET NOCOUNT ON;

	declare @begindate datetime
	declare @enddate datetime

	select @begindate = cast(cast(@month as varchar(2)) + '/01/' + cast(@year as varchar(4)) as datetime)
	select @enddate = dateadd(second,-1,dateadd(year,5,@begindate))

	exec ev_eventListXMLByDateRange
		@begindate,
		@enddate,
		@calendarID,
		@languageID,
		@siteID,
		@groupPrintID,
		@includeHiddenEvents
END
GO



ALTER PROCEDURE [dbo].[ev_eventListXMLByMonth]
	@month int,
	@year int,
	@calendarID int,
	@languageID int,
	@siteID int,
	@groupPrintID int = null,
	@includeHiddenEvents int = null
AS
BEGIN
	SET NOCOUNT ON;

	declare @begindate datetime
	declare @enddate datetime

	select @begindate = cast(cast(@month as varchar(2)) + '/01/' + cast(@year as varchar(4)) as datetime)
	select @enddate = dateadd(second,-1,dateadd(month,1,@begindate))


	exec ev_eventListXMLByDateRange
		@begindate,
		@enddate,
		@calendarID,
		@languageID,
		@siteID,
		@groupPrintID,
		@includeHiddenEvents
END
GO





ALTER PROCEDURE [dbo].[ev_calendarGridXMLByDateRange]
	@begindate datetime,
	@enddate datetime,
	@calendarID int,
	@languageID int,
	@siteID varchar(20),
	@groupPrintID int = null,
	@includeHiddenEvents int = null
AS

SET NOCOUNT ON

DECLARE @today datetime
DECLARE @minDOW int
DECLARE @maxDOW int
DECLARE @returnXML xml, @returnCategoryXML xml, @isSWL bit

DECLARE @infoTable TABLE (minDOW int, maxDOW int, startDate datetime, endDate datetime)
DECLARE @daysTable TABLE (id int IDENTITY(1,1), date datetime, dayOfMonth int, dayOfWeek int, weeksSinceStart int, monthofYear int, year int, isToday bit)
	
select @isSWL = isSWL
	from ev_calendars
	where calendarID = @calendarID

IF OBJECT_ID('tempdb..#tempevents') IS NOT NULL 
	DROP TABLE #tempevents

CREATE TABLE #tempevents (
	autoID int IDENTITY(1,1),
	isSWL bit,
	eventID int,
	startTime datetime, 
	endTime datetime, 
	timezoneID int, 
	timeZoneCode varchar(50), 
	timeZoneAbbr varchar(10),
	displaystartTime datetime,
	displayendTime datetime, 
	displaytimezoneID int, 
	displaytimeZoneCode varchar(50), 
	displaytimeZoneAbbr varchar(10),
	altregistrationURL  varchar(500),
	isAllDayEvent bit,
	categoryID int, 
	categoryName varchar(100), 
	categoryColor varchar(100),
	eventTitle varchar(250),
	locationTitle varchar(200))

set @today = getdate()
;WITH cte AS (
	SELECT 0 i, @begindate AS date, datepart(d,@begindate) as dayOfMonth, datepart(dw,@begindate) as dayofWeek, 0 as weeksSinceStart, datepart(month,@begindate) as monthOfYear, datepart(year,@begindate) as Year, isToday = case when datediff(day,@begindate,@today) = 0 then 1 else 0 end
	UNION ALL
	SELECT i + 1, DATEADD(day,i+1,@begindate) as date, datepart(d,DATEADD(day,i+1,@begindate)) as dayOfMonth, datepart(dw,DATEADD(day,i+1,@begindate)) as dayofWeek, datediff(week,@begindate,DATEADD(day,i+1,@begindate)) as weeksSinceStart, datepart(month,DATEADD(day,i+1,@begindate)) as monthOfYear, datepart(year,DATEADD(day,i+1,@begindate)) as Year, isToday = case when datediff(day,DATEADD(day,i+1,@begindate),@today) = 0 then 1 else 0 end
	FROM cte
	WHERE datediff(day,DATEADD( day, i, @begindate ),@enddate) > 0
)
INSERT INTO @daystable (date, dayOfMonth, dayOfWeek, weeksSinceStart, monthOfYear, year,isToday)
SELECT date, dayOfMonth, dayOfWeek, weeksSinceStart, monthOfYear, year, isToday
FROM cte
option (MAXRECURSION 366)

select @minDOW = min(dayOfWeek) from @daystable
select @maxDOW = max(dayOfWeek) from @daystable

insert into @infoTable (minDOW, maxDOW, startDate, endDate)
values (@minDOW,@maxDOW,@begindate,@enddate)


declare @viewableCalendars TABLE (calendarID int PRIMARY KEY, siteResourceID int, calendarName varchar(max))

insert into @viewableCalendars (calendarID, siteResourceID, calendarName)
exec dbo.[ev_getCalendarsViewableByGroupPrintID]
	@siteID = @siteID ,
	@groupPrintID = @groupPrintID



insert into #tempevents (eventID, isSWL, startTime, endTime, timezoneID, timeZoneCode, 
	timeZoneAbbr, displaystartTime, displayendTime, displaytimezoneID, displaytimeZoneCode, 
	displaytimeZoneAbbr, altregistrationURL, isAllDayEvent, categoryID, categoryName, 
	categoryColor, eventTitle, locationTitle)
select events.eventID,
	0 as isSWL,
	CONVERT(varchar(30),
	events.startTime,121) as startTime, 
	CONVERT(varchar(30),events.endTime,121) as endTime, 
	events.timezoneID, 
	events.timeZoneCode, 
	events.timeZoneAbbr,
	CONVERT(varchar(30),
	events.displaystartTime,121) as displaystartTime,
	CONVERT(varchar(30),events.displayendTime,121) as displayendTime, 
	events.displaytimezoneID, 
	events.displaytimeZoneCode, 
	events.displaytimeZoneAbbr,
	events.altregistrationURL,
	events.isAllDayEvent,
	events.categoryID , 
	events.categoryName , 
	events.categoryColor,
	isnull(eventContent.contentTitle,'') as eventTitle,
	isnull(locationContent.contentTitle,'') as locationTitle
from dbo.fn_ev_getEventsOnCalendar(@calendarID,@languageID,@siteID,DEFAULT,DEFAULT) as events 
inner join @viewableCalendars vc
	on vc.calendarID = events.sourceCalendarID
cross apply dbo.fn_getContent(events.eventContentID,1) as eventContent
cross apply dbo.fn_getContent(events.locationContentID,1) as locationContent
where ((startTime between @begindate and @enddate) or (endTime between @begindate and @enddate))
	AND events.status = 'A'
	and (events.hiddenFromCalendar is null OR events.hiddenFromCalendar in (0, @includeHiddenEvents))
order by events.startTime, events.isAllDayEvent

if @isSWL = 1 
BEGIN
	insert into #tempevents (eventID, isSWL, startTime, endTime, timezoneID, timeZoneCode, 
		timeZoneAbbr, displaystartTime, displayendTime, displaytimezoneID, displaytimeZoneCode, 
		displaytimeZoneAbbr, altregistrationURL, isAllDayEvent, categoryID, categoryName, 
		categoryColor, eventTitle, locationTitle)
	select matchingSeminars.seminarID as eventID,
		isSWL = 1,
		startTime = matchingSeminars.dateStart,
		endTime = matchingSeminars.dateEnd,
		matchingSeminars.timezoneID,
		matchingSeminars.timezoneCode,
		matchingSeminars.timezoneAbbr,
		displayStartTime = cast(replace(replace(startdate.value('(./node())[1]','varchar(100)'),'T',' '),'-6:0','') as datetime),
		displayEndTime = cast(replace(replace(enddate.value('(./node())[1]','varchar(100)'),'T',' '),'-6:0','') as datetime),
		displaytimezoneID = matchingSeminars.timezoneID,
		displaytimezoneCode = matchingSeminars.timezoneCode,
		displaytimezoneAbbr = matchingSeminars.timezoneAbbr,
		altregistrationURL = '',
		isAllDayEvent = cast(0 as bit),
		matchingSeminars.categoryID,
		isnull(matchingSeminars.category,'') as categoryName,
		isnull(matchingSeminars.calColor,'') as categoryColor, 
		matchingSeminars.seminarName as eventTitle ,
		'' as locationTitle
	from (
		SELECT 
			s.seminarID,
			s.seminarName,
			swl.dateStart,
			swl.dateEnd,
			cast(swl.wddxTimeZones as xml) as wddxTimeZones,
			swl.offerCredit,
			s.offerCertificate,
			stz.code,
			tz.*,
			cat.*
		FROM dbo.ev_calendars as cal
		inner join membercentral.dbo.sites sites
			on sites.siteID = @siteID
			and cal.calendarID = @calendarID
			and cal.isSWL = 1
		inner join ev_categories cat
			on cat.categoryID = cal.categoryID
		inner join dbo.organizations o
			on o.orgID = sites.orgID
		inner join membercentral.dbo.timezones tz
			on sites.defaultTimeZoneID = tz.timeZoneID
		INNER JOIN tlasites.seminarweb.dbo.tblParticipants p
			on p.orgcode = o.orgcode
		INNER JOIN tlasites.seminarweb.dbo.tblSeminarsOptIn soi
			on p.participantID = soi.participantID
		inner join tlasites.seminarweb.dbo.tblSeminars as s
			on soi.seminarID = s.seminarID
			and s.isPublished = 1
			AND s.isDeleted = 0
		INNER JOIN tlasites.seminarweb.dbo.vw_tblSeminarsSWLive swl
			on swl.seminarID = s.seminarID
			AND swl.dateStart between @begindate and @enddate
		inner join tlasites.seminarweb.dbo.tblTimeZones stz
			on tz.timeZoneCode = stz.id
		) as matchingSeminars
	CROSS APPLY matchingSeminars.wddxTimeZones.nodes(N'/wddxPacket/data/struct/var[@name=''START'']//var[@name=sql:column("matchingSeminars.code")]/dateTime') startt(startdate)
	CROSS APPLY matchingSeminars.wddxTimeZones.nodes(N'/wddxPacket/data/struct/var[@name=''END'']//var[@name=sql:column("matchingSeminars.code")]/dateTime') endt(enddate)
END

set @returnXML = (
	select 
		dateRange.minDOW, 
			dateRange.maxDOW, 
			CONVERT(nvarchar(30), dateRange.startDate, 121) as startDate, 
			CONVERT(varchar(30), dateRange.endDate, 121) as endDate, 
		weeks.weeksSinceStart,
		days.dayOfMonth,
			days.dayofWeek, 
			days.monthOfYear, 
			days.year, 
			days.isToday, 
			CONVERT(nvarchar(30), days.date, 121) as date, 
		events.isSWL,
			events.eventID ,
			CONVERT(varchar(30),events.startTime,121) as startTime, 
			CONVERT(varchar(30),events.endTime,121) as endTime , 
			events.timezoneID , 
			events.timeZoneCode , 
			events.timeZoneAbbr ,
			CONVERT(varchar(30),events.displaystartTime,121) as displaystartTime ,
			CONVERT(varchar(30),events.displayendTime,121) as displayendTime , 
			events.displaytimezoneID , 
			events.displaytimeZoneCode , 
			events.displaytimeZoneAbbr ,
			events.altregistrationURL  ,
			events.isAllDayEvent ,
			events.categoryID , 
			events.categoryName , 
			events.categoryColor ,
			events.eventTitle ,
			events.locationTitle
	from @infoTable as dateRange
	inner join (select distinct weeksSinceStart from @daystable) as weeks on 1=1
	inner join @daystable as days on weeks.weeksSinceStart = days.weeksSinceStart
	left outer join #tempevents events 
		on (datediff(day,events.startTime,days.date) >= 0 and datediff(day,days.date,events.endTime) >= 0)
	order by weeks.weeksSinceStart, days.date, events.isAllDayEvent, events.startTime
	for xml auto, root('calendar')
)
select @returnXML as dateXML


set @returnCategoryXML = (
	select distinct categoryID, categoryName, categoryColor
	from #tempevents as category
	order by categoryName
	for xml auto, root('categories')
)

select isnull(@returnCategoryXML,cast('<categories/>' as xml)) as categoryXML

IF OBJECT_ID('tempdb..#tempevents') IS NOT NULL 
	DROP TABLE #tempevents

set nocount off

RETURN 0
GO



ALTER PROCEDURE [dbo].[ev_calendarGridXMLByMonth]
	@month int,
	@year int,
	@calendarID int,
	@languageID int,
	@siteID int,
	@groupPrintID int = null,
	@includeHiddenEvents int = null
AS

declare @begindate datetime
declare @enddate datetime

select @begindate = cast(cast(@month as varchar(2)) + '/01/' + cast(@year as varchar(4)) as datetime)
select @enddate = dateadd(second,-1,dateadd(month,1,@begindate))

exec dbo.ev_calendarGridXMLByDateRange @begindate, @enddate, @calendarID, @languageID, @siteID, @groupPrintID, @includeHiddenEvents
GO




ALTER PROCEDURE [dbo].[ev_eventListByDateRange]
	@begindate datetime,
	@enddate datetime,
	@calendarID int,
	@languageID int,
	@siteID int,
	@groupPrintID int = null,
	@includeHiddenEvents int = null
AS

set nocount on

DECLARE @returnXML xml, @returnCategoryXML xml, @isSWL bit

select @isSWL = isSWL
	from ev_calendars
	where calendarID = @calendarID

IF OBJECT_ID('tempdb..#tempevents') IS NOT NULL 
	DROP TABLE #tempevents

CREATE TABLE #tempevents (
		autoID int IDENTITY(1,1),
		isSWL bit,
		eventID int,
		startTime datetime, 
		endTime datetime, 
		timezoneID int, 
		timeZoneCode varchar(50), 
		timeZoneAbbr varchar(10),
		displaystartTime datetime,
		displayendTime datetime, 
		displaytimezoneID int, 
		displaytimeZoneCode varchar(50), 
		displaytimeZoneAbbr varchar(10),
		altregistrationURL  varchar(500),
		isAllDayEvent bit,
		categoryID int, 
		categoryName varchar(100), 
		categoryColor varchar(100),
		eventTitle varchar(200),
		eventContent varchar(max),
		locationTitle varchar(200),
		locationContent varchar(max)
		)


declare @viewableCalendars TABLE (calendarID int PRIMARY KEY, siteResourceID int, calendarName varchar(max))

insert into @viewableCalendars (calendarID, siteResourceID, calendarName)
exec dbo.[ev_getCalendarsViewableByGroupPrintID]
	@siteID = @siteID ,
	@groupPrintID = @groupPrintID


insert into #tempevents (eventID, isSWL, startTime, endTime, timezoneID, timeZoneCode, 
	timeZoneAbbr, displaystartTime, displayendTime, displaytimezoneID, displaytimeZoneCode, 
	displaytimeZoneAbbr, altregistrationURL, isAllDayEvent, categoryID, categoryName, 
	categoryColor, eventTitle, eventContent, locationTitle, locationContent)
select events.eventID,
		0 as isSWL,
		CONVERT(varchar(30),
		events.startTime,121) as startTime, 
		CONVERT(varchar(30),events.endTime,121) as endTime, 
		events.timezoneID, 
		events.timeZoneCode, 
		events.timeZoneAbbr,
		CONVERT(varchar(30),
		events.displaystartTime,121) as displaystartTime,
		CONVERT(varchar(30),events.displayendTime,121) as displayendTime, 
		events.displaytimezoneID, 
		events.displaytimeZoneCode, 
		events.displaytimeZoneAbbr,
		events.altregistrationURL,
		events.isAllDayEvent,
		events.categoryID , 
		events.categoryName , 
		events.categoryColor,
		isnull(eventContent.contentTitle,'') as eventTitle,
		isnull(eventContent.rawContent, '') as eventContent,
		isnull(locationContent.contentTitle,'') as locationTitle,
		isnull(locationContent.rawContent,'') as locationContent
	from dbo.fn_ev_getEventsOnCalendar(@calendarID,@languageID,@siteID,DEFAULT,DEFAULT) as events 
	inner join @viewableCalendars vc
		on vc.calendarID = events.sourceCalendarID
	cross apply dbo.fn_getContent(events.eventContentID,1) as eventContent
	cross apply dbo.fn_getContent(events.locationContentID,1) as locationContent
	where ((startTime between @begindate and @enddate) or (endTime between @begindate and @enddate) or (startTime < @begindate and endTime > @enddate))
		AND events.status = 'A'
	and (events.hiddenFromCalendar is null OR events.hiddenFromCalendar in (0, @includeHiddenEvents))
	order by events.startTime, events.isAllDayEvent

if @isSWL = 1 
BEGIN
	insert into #tempevents (eventID, isSWL, startTime, endTime, timezoneID, timeZoneCode, 
		timeZoneAbbr, displaystartTime, displayendTime, displaytimezoneID, displaytimeZoneCode, 
		displaytimeZoneAbbr, altregistrationURL, isAllDayEvent, categoryID, categoryName, 
		categoryColor, eventTitle, locationTitle)

	select matchingSeminars.seminarID as eventID,
		isSWL = 1,
		startTime = matchingSeminars.dateStart,
		endTime = matchingSeminars.dateEnd,
		matchingSeminars.timezoneID,
		matchingSeminars.timezoneCode,
		matchingSeminars.timezoneAbbr,
		displayStartTime = cast(replace(replace(startdate.value('(./node())[1]','varchar(100)'),'T',' '),'-6:0','') as datetime),
		displayEndTime = cast(replace(replace(enddate.value('(./node())[1]','varchar(100)'),'T',' '),'-6:0','') as datetime),
		displaytimezoneID = matchingSeminars.timezoneID,
		displaytimezoneCode = matchingSeminars.timezoneCode,
		displaytimezoneAbbr = matchingSeminars.timezoneAbbr,
		altregistrationURL = '',
		isAllDayEvent = cast(0 as bit),
		matchingSeminars.categoryID,
		isnull(matchingSeminars.category,'') as categoryName,
		isnull(matchingSeminars.calColor,'') as categoryColor, 
		matchingSeminars.seminarName as eventTitle ,
		'' as locationTitle
	from (
		SELECT 
			s.seminarID,
			s.seminarName,
			swl.dateStart,
			swl.dateEnd,
			cast(swl.wddxTimeZones as xml) as wddxTimeZones,
			swl.offerCredit,
			s.offerCertificate,
			stz.code,
			tz.*,
			cat.*
		FROM dbo.ev_calendars as cal
		inner join membercentral.dbo.sites sites
			on sites.siteID = @siteID
			and cal.calendarID = @calendarID
			and cal.isSWL = 1
		inner join ev_categories cat
			on cat.categoryID = cal.categoryID
		inner join dbo.organizations o
			on o.orgID = sites.orgID
		inner join membercentral.dbo.timezones tz
			on sites.defaultTimeZoneID = tz.timeZoneID
		INNER JOIN tlasites.seminarweb.dbo.tblParticipants p
			on p.orgcode = o.orgcode
		INNER JOIN tlasites.seminarweb.dbo.tblSeminarsOptIn soi
			on p.participantID = soi.participantID
		inner join tlasites.seminarweb.dbo.tblSeminars as s
			on soi.seminarID = s.seminarID
			and s.isPublished = 1
			AND s.isDeleted = 0
		INNER JOIN tlasites.seminarweb.dbo.vw_tblSeminarsSWLive swl
			on swl.seminarID = s.seminarID
			AND swl.dateStart between @begindate and @enddate
		inner join tlasites.seminarweb.dbo.tblTimeZones stz
			on tz.timeZoneCode = stz.id
		) as matchingSeminars
	CROSS APPLY matchingSeminars.wddxTimeZones.nodes(N'/wddxPacket/data/struct/var[@name=''START'']//var[@name=sql:column("matchingSeminars.code")]/dateTime') startt(startdate)
	CROSS APPLY matchingSeminars.wddxTimeZones.nodes(N'/wddxPacket/data/struct/var[@name=''END'']//var[@name=sql:column("matchingSeminars.code")]/dateTime') endt(enddate)
END

select 
	dateRange.calendarID, 
	CONVERT(varchar(30),dateRange.startDate,121) as startDate, 
	CONVERT(varchar(30),dateRange.enddate,121) as endDate, 
	events.isSWL,
	events.eventID ,
	CONVERT(varchar(30),events.startTime,121) as startTime, 
	CONVERT(varchar(30),events.endTime,121) as endTime , 
	events.timezoneID , 
	events.timeZoneCode , 
	events.timeZoneAbbr ,
	CONVERT(varchar(30),events.displaystartTime,121) as displaystartTime ,
	CONVERT(varchar(30),events.displayendTime,121) as displayendTime , 
	events.displaytimezoneID , 
	events.displaytimeZoneCode , 
	events.displaytimeZoneAbbr ,
	events.altregistrationURL  ,
	events.isAllDayEvent ,
	events.categoryID , 
	events.categoryName , 
	events.categoryColor ,
	events.eventTitle ,
	events.eventContent ,
	events.locationTitle,
	events.locationContent
from #tempevents events
inner join ( select @calendarID as calendarID, @begindate as startDate, @enddate as endDate) dateRange
	on 1=1 
order by startTime, isAllDayEvent

select distinct categoryID, categoryName, categoryColor
from #tempevents as category
order by categoryName

IF OBJECT_ID('tempdb..#tempevents') IS NOT NULL 
	DROP TABLE #tempevents

set nocount off

RETURN 0
GO