use customApps
GO

declare @udid int 

insert into dbo.schedTask_memberJoinDates (
	siteCode, 
	joinDateFieldName, 
	rejoinDateFieldName, 
	droppedDateFieldName, 
	lastSuccessDate, 
	lastErrorCode, 
	isActive, 
	paidThruDateFieldName
)
values (
	'CA',
	'Join Date',
	'Rejoin Date',
	'Dropped Date',
	NULL,
	0,
	1,
	'Paid Through Date'
)

select @udid =  scope_identity()

insert into dbo.schedTask_memberJoinDateSubTypes (
	memberJoinDateUDID, 
	subscriptionTypeUID 
)
values (
	@udid,
	'8116514A-8505-4A37-80FC-654610653F3E'
)