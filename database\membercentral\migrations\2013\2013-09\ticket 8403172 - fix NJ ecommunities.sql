declare @resourcesToDelete TABLE (siteResourceID int)
declare @thisResourceID int

insert into @resourcesToDelete (siteResourceID)
select p.siteResourceID
from sites s
inner join cms_applicationInstances ai
	on ai.siteID = s.siteID
	and s.sitecode = 'nj'
inner join comm_communities comm
	on comm.applicationInstanceID = ai.applicationInstanceID
inner join cms_siteResources sr
	on sr.siteResourceID = ai.siteResourceID
inner join cms_siteResourceStatuses srs
	on srs.siteResourceStatusID = sr.siteResourceStatusID
	and srs.siteResourceStatusDesc = 'active'
inner join cms_siteResources pageResource
	on pageResource.siteResourceID = sr.parentSiteResourceID
inner join cms_pages p
	on p.siteResourceID = pageResource.siteResourceID
	


select @thisResourceID = min(siteResourceID) from @resourcesToDelete

while @thisResourceID is not null BEGIN

	exec dbo.cms_deleteSiteResourceAndChildren @siteResourceID=@thisResourceID
	
	select @thisResourceID = min(siteResourceID) from @resourcesToDelete where siteResourceID > @thisResourceID
END

GO



	
	
	
USE memberCentral

/*
HOW TO USE THIS SCRIPT:

1) keep this handy if you need to 'delete' any mistakes you make

	-- run to find out which parentSiteResourceID (@siteResourceID) to pass to cms_deleteSiteResourceAndChildren below
	select sr.parentSiteResourceID, c.*
	from sites s
	inner join cms_applicationInstances ai
		on ai.siteID = s.siteID
		and s.sitecode = 'NJ'
	inner join comm_communities c
		on c.applicationInstanceID = ai.applicationInstanceID
	inner join cms_siteREsources sr
		on sr.siteResourceID = ai.siteResourceID
	inner join cms_siteResourceStatuses srs
		on srs.siteResourceStatusID = sr.siteResourceStatusID
		and srs.siteResourceStatusDesc = 'Active'
	order by ai.applicationInstanceID desc

	-- run once per community you want to reset (delete) - USE the parentSiteResourceID's from the query above
	exec dbo.cms_deleteSiteResourceAndChildren @siteResourceID=1004558


2) note there's a WARNING section below where you should not have to edit below it
3) the request for ecommunities should come with spreadsheet that tells you which
	variables to use for each community
4) substitute variables for @sitecode, @fieldsetName_search, etc. below...
5) run temp script to delete current new sections
6) re-run/test this script
7) go in and manually add View and Participate permissions for each (under Control Panel >  Website > Communities...)
*/

DECLARE	@sitecode varchar(10), 
		@fieldsetName_search varchar (200), 
		@fieldsetName_results varchar (200), 
		@fieldsetName_details varchar (200),
		@GLAccountName varchar(30),
		@communitiesSectionCode varchar(100);

set @sitecode = 'NJ';
-- for these next 3, look in Members > Member Setup > Member Field Sets for which are used for which
set @fieldsetName_search = 'Member Directory Search Form';
set @fieldsetName_results = 'Directory Search Results & Details';
set @fieldsetName_details = 'Directory Search Results & Details';
-- for this one, go to Events > Calendars > choose to Edit a Calendar for an existing community > view
-- "Revenue Account for New Events", and use that same name here
set @GLAccountName = 'Meetings';
-- go to Website > Content > Pages and click on the Root directory. If current Communities are directly
-- under Root, then Root is the code you want below; otherwise, find the parent directory needed
set @communitiesSectionCode = 'Root'

-- you shouldn't need to change this table declaration
declare @commList TABLE (
	autoID int IDENTITY(1,1),
	pageName varchar(100),
	pageTitle varchar(200),
	applicationInstanceName varchar(100),
	applicationInstanceDesc  varchar(200),
	pagedesc varchar(400),
	needsMemberManager bit,
	needsLists bit,
	needsFiles bit,
	needsCalendar bit,
	needsAnnouncements bit,
	needsPhotos bit,
	needsVideos bit,
	needsHelp bit,
	needsGuidelines bit
);

-- make an INSERT statement below for each new Community you want.
-- the client should provide a spreadsheet with the values for each field for each Community
insert into @commlist (
		pageName,
		pageTitle,
		applicationInstanceName,
		applicationInstanceDesc,
		pagedesc,
		needsMemberManager,
		needsLists,
		needsFiles,
		needsCalendar,
		needsAnnouncements,
		needsPhotos,
		needsVideos,
		needsHelp,
		needsGuidelines
	) 

select 
	p.pageName,
	pl.pageTitle,
	ai.applicationInstanceName,
	ai.applicationInstanceDesc,
	pl.pagedesc,
	cast(1 as bit) as needsMemberManager,
	cast(1 as bit) as needsLists,
	cast(1 as bit) as needsFiles,
	cast(1 as bit) as needsCalendar,
	cast(1 as bit) as needsAnnouncements,
	cast(1 as bit) as needsPhotos,
	cast(1 as bit) as needsVideos,
	cast(0 as bit) as needsHelp,
	cast(0 as bit) as needsGuidelines
from sites s
inner join cms_applicationInstances ai
	on ai.siteID = s.siteID
	and s.sitecode = 'nj'
inner join comm_communities comm
	on comm.applicationInstanceID = ai.applicationInstanceID
inner join cms_siteResources sr
	on sr.siteResourceID = ai.siteResourceID
inner join cms_siteResourceStatuses srs
	on srs.siteResourceStatusID = sr.siteResourceStatusID
	and srs.siteResourceStatusDesc = 'deleted'
inner join cms_siteResources pageResource
	on pageResource.siteResourceID = sr.parentSiteResourceID
inner join cms_pages p
	on p.siteResourceID = pageResource.siteResourceID
inner join cms_pageLanguages pl
	on pl.pageID = p.pageID
	and pl.languageID = s.defaultLanguageID



/*
	WARNING: you shouldn't need to edit anything below unless you're editing the way the script
		actually works. The variables and INSERT statements above are typically the only variables
*/

DECLARE	@siteid int;

select @siteID = dbo.fn_getSiteIDFromSiteCode(@siteCode)

update @commList set
	pageName = replace(pageName,' ','')

if exists (select cl.pageName from @commlist cl group by cl.pageName having count(*) > 1)
	GOTO on_duplicatePageNames

DECLARE
	@languageID int,
	@sectionID int,
	@zoneID int,
	@pageTemplateID int,
	@subpageTemplateID int,
	@pageModeID int,
	@pgResourceTypeID int,
	@pgParentResourceTypeID int,
	@allowReturnAfterLogin bit,
	@isVisible bit,
	@viewFunction int,
	@applicationWidgetInstanceID int,
	@applicationWidgetInstanceSiteResourceID int,
	@commSiteResourceTypeID int,
	@zoneIDB int,
	@trashID int,
	@poolTypeID int


select
	@isVisible = 1,
	@languageID =  dbo.fn_getLanguageID('en'),
	@zoneID = dbo.fn_getZoneID('Main'),
	@zoneIDB = dbo.fn_getZoneID('B'),
	@pageTemplateID=null,
	@subpageTemplateID=dbo.fn_getTemplateID(null,'communityThreeColumn'),
	@pageModeID=dbo.fn_getModeId('Full'),
	@pgParentResourceTypeID= dbo.fn_getResourceTypeID('ApplicationCreatedPage'),
	@pgResourceTypeID= dbo.fn_getResourceTypeID('ApplicationSubPage'),
	@allowReturnAfterLogin = 1


declare
	@thisAutoID int,
	@thispageName varchar(100),
	@thispageTitle varchar(200),
	@thispagedesc varchar(400),
	@thisapplicationInstanceName varchar(100),
	@thisapplicationInstanceDesc  varchar(100),
	@thisNeedsMemberManager bit,
	@thisNeedsLists bit,
	@thisNeedsFiles bit,
	@thisNeedsCalendar bit,
	@thisNeedsAnnouncements bit,
	@thisNeedsPhotos bit,
	@thisNeedsVideos bit,
	@thisNeedsHelp bit,
	@thisNeedsGuidelines bit,
	@thisapplicationInstanceID int,
	@thissiteResourceID int,
	@thispageID int,
	@thisRootSectionID int,
	@subAppPageName varchar(100),
	@subAppPageTitle varchar(100),
	@subAppPageDesc varchar(100),
	@subAppResourceID int,
	@subAppApplicationInstanceID int,
	@subAppPageID int,
	@commHomePageID int,
	@recentFilesWidgetTypeID int,
	@recentPhotosWidgetTypeID int,
	@recentVideosWidgetTypeID int,
	@defaultGLAccountID int,
	@searchFieldSetID int,
	@resultsFieldSetID int,
	@detailsFieldSetID int,
	@searchUseID int,
	@resultsUseID int,
	@detailsUseID int,
	@resultsUseSiteResourceID int,
	@detailsUseSiteResourceID int


DECLARE
	@viewFunctionID int,
	@participateFunctionID int,
	@MemberManagerResourceTypeID int,
	@ListViewerResourceTypeID int,
	@EventsResourceTypeID int,
	@FileShareResourceTypeID int,
	@AnnouncementsResourceTypeID int,
	@VideoGalleryResourceTypeID int,
	@PhotoGalleryResourceTypeID int;

declare
	@fsAddDocumentsFunctionID int,
	@fsEditOwnMetadataFunctionID int,
	@fsReuploadOwnFunctionID int,
	@fsDeleteOwnFunctionID int,
	@fsAddSubFolderFunctionID int,
	@editOwnFunctionID int,
	@AddPhotosFunctionID int,
	@deleteOwnFunctionID int,
	@AddVideosFunctionID int,
	@appearInDirectoryID int,
	@memberFieldsetFunctionID int,
	@rc int;


select @MemberManagerResourceTypeID = srt.resourceTypeID  from cms_applicationTypes at inner join cms_siteResourceTypes srt on srt.resourceTypeID = at.resourceTypeID where applicationTypename = 'Members'
select @ListViewerResourceTypeID = srt.resourceTypeID  from cms_applicationTypes at inner join cms_siteResourceTypes srt on srt.resourceTypeID = at.resourceTypeID where applicationTypename = 'ListViewer'
select @EventsResourceTypeID = srt.resourceTypeID  from cms_applicationTypes at inner join cms_siteResourceTypes srt on srt.resourceTypeID = at.resourceTypeID where applicationTypename = 'Events'
select @FileShareResourceTypeID = srt.resourceTypeID  from cms_applicationTypes at inner join cms_siteResourceTypes srt on srt.resourceTypeID = at.resourceTypeID where applicationTypename = 'FileShare'
select @AnnouncementsResourceTypeID = srt.resourceTypeID  from cms_applicationTypes at inner join cms_siteResourceTypes srt on srt.resourceTypeID = at.resourceTypeID where applicationTypename = 'Announcements'
select @VideoGalleryResourceTypeID = srt.resourceTypeID  from cms_applicationTypes at inner join cms_siteResourceTypes srt on srt.resourceTypeID = at.resourceTypeID where applicationTypename = 'VideoGallery'
select @PhotoGalleryResourceTypeID = srt.resourceTypeID  from cms_applicationTypes at inner join cms_siteResourceTypes srt on srt.resourceTypeID = at.resourceTypeID where applicationTypename = 'PhotoGallery'

select @recentPhotosWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationWidgetTypeName = 'recentPhotos'
select @recentVideosWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationWidgetTypeName = 'recentVideos'
select @recentFilesWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationWidgetTypeName = 'recentFiles'
select @commSiteResourceTypeID = dbo.fn_getResourceTypeID('Community')


select @searchFieldSetID = mfs.fieldSetID 
							FROM	dbo.ams_memberFieldSets mfs
								INNER JOIN sites s ON mfs.siteID = s.siteID 
								AND siteCode = @sitecode
							WHERE fieldsetName = @fieldsetName_search

select @resultsFieldSetID = mfs.fieldSetID 
							FROM	dbo.ams_memberFieldSets mfs
								INNER JOIN sites s ON mfs.siteID = s.siteID 
								AND siteCode = @sitecode
							WHERE fieldsetName = @fieldsetName_results

select @detailsFieldSetID = mfs.fieldSetID 
							FROM	dbo.ams_memberFieldSets mfs
								INNER JOIN sites s ON mfs.siteID = s.siteID 
								AND siteCode = @sitecode
							WHERE fieldsetName = @fieldsetName_details 


select @appearInDirectoryID = dbo.fn_getResourceFunctionID('AppearInDirectory', @MemberManagerResourceTypeID)
select @participateFunctioniD = dbo.fn_getResourceFunctionID('Participate', @commSiteResourceTypeID)
select @viewFunctionID = dbo.fn_getResourceFunctionID('View', @commSiteResourceTypeID)
select @fsAddDocumentsFunctionID = dbo.fn_getResourceFunctionID('fsAddDocuments', @FileShareResourceTypeID)
select @fsEditOwnMetadataFunctionID = dbo.fn_getResourceFunctionID('fsEditOwnMetadata', @FileShareResourceTypeID)
select @fsReuploadOwnFunctionID = dbo.fn_getResourceFunctionID('fsReuploadOwn', @FileShareResourceTypeID)
select @fsDeleteOwnFunctionID = dbo.fn_getResourceFunctionID('fsDeleteOwn', @FileShareResourceTypeID)
select @fsAddSubFolderFunctionID = dbo.fn_getResourceFunctionID('fsAddSubFolder', @FileShareResourceTypeID)
select @editOwnFunctionID = dbo.fn_getResourceFunctionID('editOwn', @PhotoGalleryResourceTypeID)
select @AddPhotosFunctionID = dbo.fn_getResourceFunctionID('AddPhotosByDefault', @PhotoGalleryResourceTypeID)
select @deleteOwnFunctionID = dbo.fn_getResourceFunctionID('deleteOwn', @PhotoGalleryResourceTypeID)
select @AddVideosFunctionID = dbo.fn_getResourceFunctionID('AddVideos', @VideoGalleryResourceTypeID)
select @memberFieldsetFunctionID =	srf.functionID
									from ams_memberFieldusage mfu
									inner join cms_siteResources sr
										on sr.siteResourceID = mfu.useSiteResourceID
									inner join cms_siteResourceTypes srt
										on sr.resourceTypeID = srt.resourceTypeID
									inner join cms_siteResourceTypeFunctions srtf
										on srtf.resourceTypeID = srt.resourceTypeID
									inner join cms_siteResourceFunctions srf
										on srf.functionID = srtf.functionID


select 
	@defaultGLAccountID = GLAccountID
	FROM tr_glAccounts
	WHERE orgID = dbo.fn_getOrgIDFromSiteID(@siteID)
	AND AccountName = @GLAccountName 

IF @defaultGLAccountID is null
GOTO on_noGL

	select top 1 @thisAutoID = autoID from @commList order by autoID 
	while @thisAutoID is not null
	begin
		BEGIN TRAN

		select
			@thisPageName = pageName,
			@thisPageTitle = pageTitle,
			@thisPagedesc = pagedesc,
			@thisApplicationInstanceName = applicationInstanceName,
			@thisApplicationInstanceDesc = applicationInstanceDesc,
			@thisNeedsMemberManager = NeedsMemberManager,
			@thisNeedsLists = NeedsLists,
			@thisNeedsFiles = NeedsFiles,
			@thisNeedsCalendar = NeedsCalendar,
			@thisNeedsPhotos = NeedsPhotos,
			@thisNeedsVideos = NeedsVideos,
			@thisNeedsAnnouncements = NeedsAnnouncements,
			@thisNeedsHelp = needsHelp,
			@thisNeedsGuidelines = needsGuidelines,
			@thisApplicationInstanceID = null,
			@thisSiteResourceID = null,
			@thisPageID = null
		from @commlist where autoID = @thisAutoID

		select @sectionID = sectionID
			from dbo.cms_pageSections
			where siteid = @siteID
			and sectionCode = @communitiesSectionCode

		exec @rc = dbo.cms_createApplicationInstanceCommunity

			@siteid=@siteID,
			@languageID=@languageID,
			@sectionID=@sectionID,
			@isVisible=@isVisible,
			@pageName=@thispageName,
			@pageTitle=@thispageTitle,
			@pagedesc=@thispagedesc,
			@zoneID=@zoneID,
			@pageTemplateID=@pageTemplateID,
			@subpageTemplateID=@subpageTemplateID,
			@pageModeID=@pageModeID,
			@pgResourceTypeID=@pgParentResourceTypeID,
			@pgParentResourceID=null,
			@allowReturnAfterLogin=@allowReturnAfterLogin,
			@applicationInstanceName=@thisapplicationInstanceName,
			@applicationInstanceDesc=@thisapplicationInstanceDesc,
			@applicationInstanceID=@thisapplicationInstanceID OUTPUT,
			@siteResourceID=@thissiteResourceID OUTPUT,
			@pageID=@thispageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


		select @thisRootSectionID = rootSectionID
		from comm_communities where applicationInstanceID = @thisapplicationInstanceID

		select @commHomePageID = p.pageID
		from comm_communities comm
			inner join cms_pages p
				on p.pageName = comm.defaultCommunityPageName
				and comm.rootSectionID = p.sectionID
				and comm.rootSectionID = @thisRootSectionID
			inner join cms_siteResources sr
				on sr.siteResourceID = p.siteResourceID
			inner join cms_siteResourceStatuses srs
				on sr.siteResourceStatusID = srs.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'

		if @thisNeedsMemberManager = 1
		BEGIN
			set @subAppPageName = @thispageName + 'MemberDirectory';
			set @subAppPageTitle = 'Directory';

			EXEC @rc = dbo.cms_createApplicationInstanceMemberDirectory @siteid=@siteid, @languageID=@languageID, @sectionID=@thisRootSectionID, 
					@isVisible=@isVisible, @pageName=@subAppPageName, 
					@pageTitle=@subAppPageTitle, @pageDesc='', @zoneID=@zoneID, @pagetemplateid=null,
					@pageModeID=null, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID = @thissiteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
					@applicationInstanceName=@subAppPageTitle, @applicationInstanceDesc='', 
					@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
					@siteresourceID=@subAppResourceID OUTPUT, 
					@pageID=@subAppPageID OUTPUT
				IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			
			-- set the search fieldset for the member directory
			EXEC @rc = dbo.ams_createMemberFieldUsage @siteResourceID=@subAppResourceID, @fieldsetID=@searchFieldSetID, @area='search', 
			@createSiteResourceID=0, @useID=@searchUseID OUTPUT

			-- set the results fieldset for the member directory
			EXEC @rc = dbo.ams_createMemberFieldUsage @siteResourceID=@subAppResourceID, @fieldsetID=@resultsFieldSetID, @area='results', 
			@createSiteResourceID=1, @useID=@resultsUseID OUTPUT

			-- lookup usesiteResourceID from useID returned from previous step
			SELECT @resultsUseSiteResourceID = mfu.useSiteResourceID
										FROM ams_memberFieldusage mfu
										INNER JOIN cms_siteResources sr
											ON sr.siteResourceID = mfu.useSiteResourceID
											AND mfu.useID = @resultsUseID
			
			-- call cms_createSiteResourceRight to inherit qualify perm for results fieldset usage from participate
			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid,
				@siteResourceID=@resultsUseSiteResourceID,
				@include=1, @functionID=@memberFieldsetFunctionID,
				@roleID=null, @groupID=null, @memberID=null,
				@inheritedRightsResourceID=@thisSiteResourceID,
				@inheritedRightsFunctionID=@participateFunctioniD,
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			-- set the details fieldset for the member directory
			EXEC @rc = ams_createMemberFieldUsage @siteResourceID=@subAppResourceID, @fieldsetID=@detailsFieldSetID, @area='details', 
			@createSiteResourceID=1, @useID=@detailsUseID OUTPUT

			-- lookup usesiteResourceID from useID returned from previous step
			SELECT @detailsUseSiteResourceID = mfu.useSiteResourceID
										FROM ams_memberFieldusage mfu
										INNER JOIN cms_siteResources sr
											ON sr.siteResourceID = mfu.useSiteResourceID
											AND mfu.useID = @detailsUseID

			-- call cms_createSiteResourceRight to inherit qualify perm for results fieldset usage from participate
			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid,
				@siteResourceID=@detailsUseSiteResourceID,
				@include=1, @functionID=@memberFieldsetFunctionID,
				@roleID=null, @groupID=null, @memberID=null,
				@inheritedRightsResourceID=@thisSiteResourceID,
				@inheritedRightsFunctionID=@participateFunctioniD,
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			-- set the default action per MF; Set's the default view on membermanager.
			update dbo.cms_applicationInstances
			  set settingsXML = '<settings><setting name="defaultAction" value="SearchResults" /></settings>'
			where applicationInstanceID = @subAppApplicationInstanceID
				IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid,
				@siteResourceID=@subAppResourceID,
				@include=1, @functionID=@appearInDirectoryID,
				@roleID=null, @groupID=null, @memberID=null,
				@inheritedRightsResourceID=@thissiteResourceID,
				@inheritedRightsFunctionID=@participateFunctioniD,
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, 
				@memberID=null, @inheritedRightsResourceID=@thissiteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
		END


		if @thisNeedsLists = 1
		BEGIN
			set @subAppPageName = @thispageName + 'Lists';
			set @subAppPageTitle = 'Lists';

			EXEC @rc = dbo.cms_createApplicationInstanceListViewer @siteid=@siteid, @languageID=@languageID, @sectionID=@thisRootSectionID, 
					@isVisible=@isVisible,@pageName=@subAppPageName, 
					@pageTitle=@subAppPageTitle, @pageDesc='', @zoneID=@zoneID, @pagetemplateid=null,
					@pageModeID=null, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID = @thissiteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
					@applicationInstanceName=@subAppPageTitle, @applicationInstanceDesc='', 
					@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
					@siteresourceID=@subAppResourceID OUTPUT, 
					@pageID=@subAppPageID OUTPUT
				IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, 
				@memberID=null, @inheritedRightsResourceID=@thissiteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
		END


		if @thisNeedsFiles = 1
		BEGIN
			set @subAppPageName = @thispageName + 'Files';
			set @subAppPageTitle = 'Files';

			EXEC @rc = dbo.cms_createApplicationInstanceFileShare @siteid=@siteid, @languageID=@languageID, @sectionID=@thisRootSectionID, 
					@isVisible=@isVisible,@pageName=@subAppPageName, 
					@pageTitle=@subAppPageTitle, @pageDesc='', @zoneID=@zoneID, @pagetemplateid=null,
					@pageModeID=null, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID = @thissiteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
					@applicationInstanceName=@subAppPageTitle, @applicationInstanceDesc='', 
					@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
					@siteresourceID=@subAppResourceID OUTPUT, 
					@pageID=@subAppPageID OUTPUT
				IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, 
				@memberID=null, @inheritedRightsResourceID=@thissiteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid,
				@siteResourceID=@subAppResourceID,
				@include=1, @functionID=@fsAddDocumentsFunctionID,
				@roleID=null, @groupID=null, @memberID=null,
				@inheritedRightsResourceID=@thissiteResourceID,
				@inheritedRightsFunctionID=@participateFunctioniD,
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid,
				@siteResourceID=@subAppResourceID,
				@include=1, @functionID=@fsEditOwnMetadataFunctionID,
				@roleID=null, @groupID=null, @memberID=null,
				@inheritedRightsResourceID=@thisSiteResourceID,
				@inheritedRightsFunctionID=@participateFunctioniD,
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid,
				@siteResourceID=@subAppResourceID,
				@include=1, @functionID=@fsReuploadOwnFunctionID,
				@roleID=null, @groupID=null, @memberID=null,
				@inheritedRightsResourceID=@thisSiteResourceID,
				@inheritedRightsFunctionID=@participateFunctioniD,
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid,
				@siteResourceID=@subAppResourceID,
				@include=1, @functionID=@fsAddSubFolderFunctionID,
				@roleID=null, @groupID=null, @memberID=null,
				@inheritedRightsResourceID=@thisSiteResourceID,
				@inheritedRightsFunctionID=@participateFunctioniD,
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid,
				@siteResourceID=@subAppResourceID,
				@include=1, @functionID=@fsDeleteOwnFunctionID,
				@roleID=null, @groupID=null, @memberID=null,
				@inheritedRightsResourceID=@thisSiteResourceID,
				@inheritedRightsFunctionID=@participateFunctioniD,
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			EXEC @rc = dbo.cms_createApplicationWidgetInstance
				@siteid=@siteID,
				@applicationInstanceID=@subAppApplicationInstanceID,
				@applicationWidgetTypeID=@recentFilesWidgetTypeID,
				@applicationWidgetInstanceName='Recent Files',
				@applicationWidgetInstanceDesc='Recent Files',
				@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
				@siteResourceID=@applicationWidgetInstanceSiteResourceID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createPageZoneResource
				@pageID=@commHomePageID, @zoneID=@zoneIDB, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
		END


		if @thisNeedsCalendar = 1
		BEGIN

			set @subAppPageName = @thispageName + 'Calendar';
			set @subAppPageTitle = 'Calendar';
			EXEC @rc = dbo.cms_createApplicationInstanceEvents @siteid=@siteid, @languageID=@languageID, @sectionID=@thisRootSectionID, 
					@pageName=@subAppPageName, 
					@pageTitle=@subAppPageTitle, @pageDesc='', @zoneID=@zoneID, @pagetemplateid=null,
					@pageModeID=null, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID = @thissiteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
					@defaultGLAccountID=@defaultGLAccountID,
					@applicationInstanceName=@subAppPageTitle, @applicationInstanceDesc='', 
					@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
					@siteresourceID=@subAppResourceID OUTPUT, 
					@pageID=@subAppPageID OUTPUT
				IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			-- Copy the default categories from the first calendar.
			declare @sourceCalendarID int
			declare @destinationCalendarID int

			select top 1 @sourceCalendarID=calendarID
			from dbo.ev_calendars c
			inner join dbo.cms_applicationInstances ai on ai.applicationInstanceID = c.applicationInstanceID and c.siteid = @siteid


			select @destinationCalendarID=calendarID
			from dbo.ev_calendars c
			inner join dbo.cms_applicationInstances ai on ai.applicationInstanceID = c.applicationInstanceID and ai.applicationInstanceID = @subAppApplicationInstanceID				

			if (@sourceCalendarID is not null and @destinationCalendarID is not null) begin
				exec @rc = dbo.ev_copyMissingCategoriesToCalendar @sourceCalendarID = @sourceCalendarID, @destCalendarID = @destinationCalendarID
			end

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, 
				@memberID=null, @inheritedRightsResourceID=@thissiteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
		END



		if @thisNeedsAnnouncements = 1
		BEGIN

			set @subAppPageName = @thispageName + 'Announcements';
			set @subAppPageTitle = 'Announcements';
			EXEC @rc = dbo.cms_createApplicationInstanceAnnouncements @siteid=@siteid, @languageID=@languageID, @sectionID=@thisRootSectionID, 
					@isVisible=@isVisible, @pageName=@subAppPageName, 
					@pageTitle=@subAppPageTitle, @pageDesc='', @zoneID=@zoneID, @pagetemplateid=null,
					@pageModeID=null, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID = @thissiteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
					@applicationInstanceName=@subAppPageTitle, @applicationInstanceDesc='', 
					@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
					@siteresourceID=@subAppResourceID OUTPUT, 
					@pageID=@subAppPageID OUTPUT
				IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, 
				@memberID=null, @inheritedRightsResourceID=@thissiteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
		END

		if @thisNeedsPhotos = 1
		BEGIN
			set @subAppPageName = @thispageName + 'Photos';
			set @subAppPageTitle = 'Photos';

			EXEC @rc = dbo.cms_createApplicationInstancePhotoGallery @siteid=@siteid, @languageID=@languageID, 
					@sectionID=@thisRootSectionID, 
					@isVisible=@isVisible,@pageName=@subAppPageName, 
					@pageTitle=@subAppPageTitle, @pageDesc='', @zoneID=@zoneID, @pagetemplateid=null,
					@pageModeID=null, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID = @thissiteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
					@allowSubGalleries = 1,
					@creatorMemberID = '15',
					@applicationInstanceName=@subAppPageTitle, @applicationInstanceDesc='', 
					@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
					@siteresourceID=@subAppResourceID OUTPUT, 
					@pageID=@subAppPageID OUTPUT
				IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, 
				@memberID=null, @inheritedRightsResourceID=@thissiteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid,
				@siteResourceID=@subAppResourceID,
				@include=1, @functionID=@editOwnFunctionID,
				@roleID=null, @groupID=null, @memberID=null,
				@inheritedRightsResourceID=@thisSiteResourceID,
				@inheritedRightsFunctionID=@participateFunctioniD,
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid,
				@siteResourceID=@subAppResourceID,
				@include=1, @functionID=@AddPhotosFunctionID,
				@roleID=null, @groupID=null, @memberID=null,
				@inheritedRightsResourceID=@thisSiteResourceID,
				@inheritedRightsFunctionID=@participateFunctioniD,
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid,
				@siteResourceID=@subAppResourceID,
				@include=1, @functionID=@deleteOwnFunctionID,
				@roleID=null, @groupID=null, @memberID=null,
				@inheritedRightsResourceID=@thisSiteResourceID,
				@inheritedRightsFunctionID=@participateFunctioniD,
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


			EXEC @rc = dbo.cms_createApplicationWidgetInstance
				@siteid=@siteID,
				@applicationInstanceID=@subAppApplicationInstanceID,
				@applicationWidgetTypeID=@recentPhotosWidgetTypeID,
				@applicationWidgetInstanceName='Recent Photos',
				@applicationWidgetInstanceDesc='Recent Photos',
				@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
				@siteResourceID=@applicationWidgetInstanceSiteResourceID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createPageZoneResource
				@pageID=@commHomePageID, @zoneID=@zoneIDB, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
		END

		if @thisNeedsVideos = 1
		BEGIN
			set @subAppPageName = @thispageName + 'Videos';
			set @subAppPageTitle = 'Videos';

			EXEC @rc = dbo.cms_createApplicationInstanceVideoGallery @siteid=@siteid, @languageID=@languageID, @sectionID=@thisRootSectionID, 
					@isVisible=@isVisible,@pageName=@subAppPageName, 
					@pageTitle=@subAppPageTitle, @pageDesc='', @zoneID=@zoneID, @pagetemplateid=null,
					@pageModeID=null, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID = @thissiteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
					@allowSubGalleries = 1,
					@creatorMemberID = '15',
					@applicationInstanceName=@subAppPageTitle, @applicationInstanceDesc='', 
					@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
					@siteresourceID=@subAppResourceID OUTPUT, 
					@pageID=@subAppPageID OUTPUT
				IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid, 
				@siteResourceID=@subAppResourceID, 
				@include=1, @functionID=@viewFunctionID, 
				@roleID=null, @groupID=null, @memberID=null, 
				@inheritedRightsResourceID=@thissiteResourceID, 
				@inheritedRightsFunctionID=@participateFunctionID, 
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid,
				@siteResourceID=@subAppResourceID,
				@include=1, @functionID=@editOwnFunctionID,
				@roleID=null, @groupID=null, @memberID=null,
				@inheritedRightsResourceID=@thisSiteResourceID,
				@inheritedRightsFunctionID=@viewFunctionID,
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid,
				@siteResourceID=@subAppResourceID,
				@include=1, @functionID=@deleteOwnFunctionID,
				@roleID=null, @groupID=null, @memberID=null,
				@inheritedRightsResourceID=@thisSiteResourceID,
				@inheritedRightsFunctionID=@participateFunctioniD,
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createSiteResourceRight
				@siteID=@siteid,
				@siteResourceID=@subAppResourceID,
				@include=1, @functionID=@AddVideosFunctionID,
				@roleID=null, @groupID=null, @memberID=null,
				@inheritedRightsResourceID=@thisSiteResourceID,
				@inheritedRightsFunctionID=@participateFunctioniD,
				@resourceRightID = @trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			EXEC @rc = dbo.cms_createApplicationWidgetInstance
				@siteid=@siteID,
				@applicationInstanceID=@subAppApplicationInstanceID,
				@applicationWidgetTypeID=@recentVideosWidgetTypeID,
				@applicationWidgetInstanceName='Recent Videos',
				@applicationWidgetInstanceDesc='Recent Videos',
				@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
				@siteResourceID=@applicationWidgetInstanceSiteResourceID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

			exec @rc = dbo.cms_createPageZoneResource
				@pageID=@commHomePageID, @zoneID=@zoneIDB, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
		END

		-- declare vars for Help or Guidelines
		DECLARE @AppSubPageTypeID int, @siteResourceStatusID int

		IF @thisNeedsHelp = 1
		BEGIN
			-- create Help for community			
			DECLARE @commpgPageID int, @communityPageID int
			DECLARE @userCreatedContentResourceTypeID int, @contentID int, @contentSiteResourceID int
			SELECT @AppSubPageTypeID = dbo.fn_getResourceTypeId('ApplicationSubPage')
			select @siteResourceStatusID = dbo.fn_getResourceStatusId('Active')
			set @subAppPageName = @thispageName + 'Help';		
			select @userCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('UserCreatedContent')
			EXEC @rc = dbo.cms_createPage @siteid=@siteid, @languageID=@languageID, @resourceTypeID=@AppSubPageTypeID, 
				@siteResourceStatusID=@siteResourceStatusID, @pgParentResourceID = @thissiteresourceID, @isVisible=@isVisible, @sectionID=@thisRootSectionID, 
				@ovTemplateID=NULL, @ovTemplateIDMobile=NULL, @ovTemplateIDMobile=NULL, @ovModeID=NULL, @pageName=@subAppPageName, @pageTitle='Help & FAQ',
				@pageDesc=null, @keywords=null, @allowReturnAfterLogin=0, @inheritPlacements=1, @checkReservedNames=1, @pageID=@commpgPageID OUTPUT
				IF @@ERROR <> 0 OR @rc <> 0 OR @commpgPageID = 0 GOTO on_error

			EXEC @rc = dbo.cms_createContent @siteID=@siteID, @pageID=@commpgPageID, @zoneID=@zoneID, 
				@resourceTypeID=@userCreatedContentResourceTypeID, @siteResourceStatusID=@siteResourceStatusID,
				@isSSL=0, @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle='Help & FAQ', 
				@contentDesc='', 
				@rawContent='Welcome to this community. Click around and see what''s here.',
				@contentID=@contentID OUTPUT, @contentSiteResourceID=@contentSiteResourceID OUTPUT
				IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
		END
		
		IF @thisNeedsGuidelines = 1
		BEGIN
			-- create Guidelines for community
			set @subAppPageName = @thispageName + 'Guidelines';		
			select @userCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('UserCreatedContent')
			EXEC @rc = dbo.cms_createPage @siteid=@siteid, @languageID=@languageID, @resourceTypeID=@AppSubPageTypeID, 
				@siteResourceStatusID=@siteResourceStatusID, @pgParentResourceID = @thissiteresourceID, @isVisible=@isVisible, @sectionid=@thisRootSectionID, 
				@ovTemplateID=NULL, @ovTemplateIDMobile=NULL, @ovTemplateIDMobile=NULL, @ovModeID=NULL, @pageName=@subAppPageName, @pageTitle='E-Community Guidelines',
				@pageDesc=null, @keywords=null, @allowReturnAfterLogin=0, @inheritPlacements=1, @checkReservedNames=1, @pageID=@commpgPageID OUTPUT
				IF @@ERROR <> 0 OR @rc <> 0 OR @commpgPageID = 0 GOTO on_error

			EXEC @rc = dbo.cms_createContent @siteID=@siteID, @pageID=@commpgPageID, @zoneID=@zoneID, 
				@resourceTypeID=@userCreatedContentResourceTypeID, @siteResourceStatusID=@siteResourceStatusID,
				@isSSL=0, @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle='Community Guidelines', 
				@contentDesc='', 
				@rawContent='Welcome to this community. Click around and see what''s here.',
				@contentID=@contentID OUTPUT, @contentSiteResourceID=@contentSiteResourceID OUTPUT
				IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
		END

		if exists (select autoID from @commList where autoid > @thisAutoID)
			select top 1 @thisAutoID = autoID from @commList where autoid > @thisAutoID order by autoid
		else
			select @thisAutoID = null

		-- normal exit
		IF @@TRANCOUNT > 0 COMMIT TRAN
	END


GOTO on_done

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN

	GOTO on_done

on_duplicatePageNames:

	print 'Some pageNames names are used more than once in your commlist';
	select cl.pageName, 'used ' + cast(count(*) as varchar(100)) + ' time(s) in your commlist'
	from @commlist cl
	group by cl.pageName
	having count(*) > 1

	GOTO on_done

on_takenPages:

	print 'Some pagename you requested are already in use within this site';
	select distinct cl.pagename as pagesAlreadyTaken, 'already in use within this site'
	from @commlist cl
		inner join cms_pages p
			on cl.pagename = p.pagename
			and p.siteID = @siteID

	GOTO on_done

on_noGL:
	print 'No default GL Account found';
	select 'No default GL Account found'

	GOTO on_done

on_done:
	print 'Script is done!';
	
	
GO



declare @permissionsToAdd TABLE (autoID int IDENTITY(1,1), siteID int, siteResourceID int, groupID int, include bit, functionID int, inheritedRightsResourceID int, inheritedRightsFunctionID int)
declare 
	@thisAutoID int,
	@thissiteID int,
	@thissiteResourceID int,
	@thisgroupID int,
	@thisinclude bit, 
	@thisfunctionID int, 
	@thisinheritedRightsResourceID int, 
	@thisinheritedRightsFunctionID int,
	
	@trashID int



insert into @permissionsToAdd (siteID, siteResourceID, groupID,include , functionID , inheritedRightsResourceID , inheritedRightsFunctionID )
select s.siteID, newCommunities.siteResourceID, srr.groupID, srr.include , srr.functionID , srr.inheritedRightsResourceID , srr.inheritedRightsFunctionID 
from sites s
inner join cms_applicationInstances ai
	on ai.siteID = s.siteID
	and s.sitecode = 'nj'
inner join comm_communities comm
	on comm.applicationInstanceID = ai.applicationInstanceID
inner join cms_siteResources sr
	on sr.siteResourceID = ai.siteResourceID
inner join cms_siteResourceStatuses srs
	on srs.siteResourceStatusID = sr.siteResourceStatusID
	and srs.siteResourceStatusDesc = 'deleted'
inner join cms_siteResources pageResource
	on pageResource.siteResourceID = sr.parentSiteResourceID
inner join cms_pages p
	on p.siteResourceID = pageResource.siteResourceID
inner join cms_siteResourceRights srr
	on srr.resourceID = sr.siteResourceID

inner join (
	select 	p.pageName, ai.applicationInstanceName, ai.siteResourceID
	from sites s
	inner join cms_applicationInstances ai
		on ai.siteID = s.siteID
		and s.sitecode = 'nj'
	inner join comm_communities comm
		on comm.applicationInstanceID = ai.applicationInstanceID
	inner join cms_siteResources sr
		on sr.siteResourceID = ai.siteResourceID
	inner join cms_siteResourceStatuses srs
		on srs.siteResourceStatusID = sr.siteResourceStatusID
		and srs.siteResourceStatusDesc = 'Active'
	inner join cms_siteResources pageResource
		on pageResource.siteResourceID = sr.parentSiteResourceID
	inner join cms_pages p
		on p.siteResourceID = pageResource.siteResourceID
) as newCommunities
on newCommunities.pageName = p.pagename
and newCommunities.applicationInstanceName = ai.applicationInstanceName





SELECT @thisAutoID = min(autoid) from @permissionsToAdd
WHILE @thisAutoID IS NOT NULL BEGIN
	select
		@thissiteID = siteID,
		@thissiteResourceID = siteResourceID,
		@thisgroupID = groupID,
		@thisinclude = include, 
		@thisfunctionID =functionID, 
		@thisinheritedRightsResourceID = inheritedRightsResourceID, 
		@thisinheritedRightsFunctionID = inheritedRightsFunctionID
	from @permissionsToAdd where autoID = @thisAutoID


	EXEC dbo.cms_createSiteResourceRight
		@siteID=@thissiteID,
		@siteResourceID=@thissiteResourceID,
		@include=@thisinclude, 
		@functionID=@thisfunctionID,
		@roleID=null,
		@groupID=@thisgroupID,
		@memberID=null, 
		@inheritedRightsResourceID=@thisinheritedRightsResourceID,
		@inheritedRightsFunctionID=@thisinheritedRightsFunctionID, 
		@resourceRightID=@trashID OUTPUT

	SELECT @thisAutoID = min(autoid) from @permissionsToAdd where autoid > @thisAutoID
END

GO