
USE [customApps]
GO

CREATE PROC [dbo].[sdcba_updateSpecialDesignations]
AS

declare 
	@orgID int, 
	@recordTypeID int,
	@memberID int,
	@sqlStr varchar (max),
	@customFliedName varchar(255),
	@designationName varchar(255),
	@childMemberID int,
	@rc int,
	@specDesigColumnID int,
	@specDesigAllowMultiple int,
	@sqlStr2 varchar (max),
	@thisColumnValue varchar(max),
	@thisChildColumnValue varchar(max),
	@thisValue varchar(255),
	@childRecordTypeID int

declare @addDesignationTbl table (memberID int, special_designations varchar(max) )
declare @removeDesignationTbl table (memberID int, special_designations varchar(max) )
declare @thisColumnValueTbl table (thisColumnValue varchar(max) )

select @orgID = membercentral.dbo.fn_getOrgIDFromOrgCode('SDCBA')
set @customFliedName = 'Special Designations'
set @designationName  = '100 Percent Club'

select 
	@specDesigColumnID = columnID, 
	@specDesigAllowMultiple = allowMultiple
FROM 
	membercentral.dbo.ams_memberDataColumns
where 
	orgID = @orgID
	and columnName = @customFliedName

select @recordTypeID = recordTypeID from membercentral.dbo.ams_recordTypes where orgID = @orgID and recordTypeName = 'Law Firm'
select @childRecordTypeID = recordTypeID from membercentral.dbo.ams_recordTypes where orgID = @orgID and recordTypeName = 'Individual'

/* Find those without Special designation */

insert into @addDesignationTbl (memberID, special_designations)
select
	ActiveChildMember.memberID, child_mdcv.columnValueString
from membercentral.dbo.ams_members as m
	inner join membercentral.dbo.ams_memberData md
		on md.memberID = m.memberID
		and m.memberID = m.activeMemberID
		and m.recordTypeID = @recordTypeID
	inner join membercentral.dbo.ams_memberDataColumnValues mdcv
		on mdcv.valueID =md.valueID
		and mdcv.columnValueString = @designationName
	inner join membercentral.dbo.ams_memberDataColumns mdc
		on mdc.columnID = mdcv.columnID
		and mdc.columnName = @customFliedName
	inner join membercentral.dbo.ams_recordRelationships as rr on 
		rr.masterMemberID = m.memberID
	inner join membercentral.dbo.ams_recordTypesRelationshipTypes as rtrt on 
		rtrt.recordTypeRelationshipTypeID = rr.recordTypeRelationshipTypeID
	inner join membercentral.dbo.ams_recordRelationshipTypes as rrt on 
		rrt.relationshipTypeID = rtrt.relationshipTypeID
		--and rrt.relationshipTypeName = 'Firm Member'
	inner join membercentral.dbo.ams_members as childMember on 
		rr.childMemberID = childMember.memberID 
	inner join membercentral.dbo.ams_members as activeChildMember on 
		activeChildMember.memberID = childMember.activeMemberID 
		and activeChildMember.status <> 'D'
	left outer join membercentral.dbo.ams_memberData child_md
		inner join membercentral.dbo.ams_memberDataColumnValues child_mdcv
			on child_mdcv.valueID =child_md.valueID
			and child_mdcv.columnValueString = @designationName
		inner join membercentral.dbo.ams_memberDataColumns child_mdc
			on child_mdc.columnID = child_mdcv.columnID
			and child_mdc.columnName = @customFliedName
	on child_md.memberID = activeChildMember.memberID
	and activeChildMember.recordTypeID = @childrecordTypeID
where 
	child_mdcv.valueID is null

/* Find those with Special designation that should not have one */

insert into @removeDesignationTbl (memberID, special_designations)
select
	m.memberID, mdcv.columnValueString
from 
	membercentral.dbo.ams_members as m
	inner join membercentral.dbo.ams_memberData md
		on md.memberID = m.memberID
		and m.memberID = m.activeMemberID
		and m.recordTypeID <> @recordTypeID
	inner join membercentral.dbo.ams_memberDataColumnValues mdcv
		on mdcv.valueID =md.valueID
		and mdcv.columnValueString = @designationName
	inner join membercentral.dbo.ams_memberDataColumns mdc
		on mdc.columnID = mdcv.columnID
		and mdc.columnName = @customFliedName
	left outer join membercentral.dbo.ams_recordRelationships as rr 
		inner join membercentral.dbo.ams_recordTypesRelationshipTypes as rtrt on 
			rtrt.recordTypeRelationshipTypeID = rr.recordTypeRelationshipTypeID
		inner join membercentral.dbo.ams_recordRelationshipTypes as rrt on 
			rrt.relationshipTypeID = rtrt.relationshipTypeID
			--and rrt.relationshipTypeName = 'Firm Member'
		inner join membercentral.dbo.ams_members as masterMember on 
			rr.masterMemberID = masterMember.memberID 
		inner join membercentral.dbo.ams_members as activeMasterMember on 
			activeMasterMember.memberID = masterMember.activeMemberID 
			and activeMasterMember.status <> 'D'
		left outer join membercentral.dbo.ams_memberData master_md
			inner join membercentral.dbo.ams_memberDataColumnValues master_mdcv
				on master_mdcv.valueID =master_md.valueID
				and master_mdcv.columnValueString = @designationName
			inner join membercentral.dbo.ams_memberDataColumns master_mdc
				on master_mdc.columnID = master_mdcv.columnID
				and master_mdc.columnName = @customFliedName
		on master_md.memberID = activeMasterMember.memberID
		and activeMasterMember.recordTypeID = @recordTypeID
	on rr.childMemberID = m.memberID
where 
	master_mdcv.valueID is null

/* Add the ones that need to be added */

select @memberID = min(memberID) from @addDesignationTbl

while @memberID is not null
begin
			
	print 'Adding'
	print @memberID

	EXEC @rc = membercentral.dbo.ams_saveMemberData 
			@memberID=@memberID, 
			@columnID=@specDesigColumnID, 
			@columnvalueID=null, 
			@columnValue=@designationName
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error	

	select @memberID =  min(memberID) from @addDesignationTbl where memberID > @memberID

end

/* Remove the ones that don;t have a parent with Special Designation */

select @memberID = min(memberID) from @removeDesignationTbl

while @memberID is not null
begin

	set @sqlStr = '
	select 
		md.[' + @customFliedName + '] as ''thisColumnValue''
	from 
		membercentral.dbo.ams_members m
		inner join membercentral.dbo.vw_memberData_SDCBA  md on
			md.memberid = m.memberid
			and m.memberID = ' + cast (@memberID as varchar(10))

	insert into  @thisColumnValueTbl
	exec(@sqlStr) 

	select @thisColumnValue = thisColumnValue  from @thisColumnValueTbl

	print  @thisColumnValue 
			
	print 'Removing' 
	print @memberID

	EXEC @rc = membercentral.dbo.ams_deleteMemberData @memberID=@memberID, @columnID=@specDesigColumnID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	print '***Deleted!***'

	select @thisValue = ltrim(rtrim(min(listitem))) from membercentral.dbo.fn_varCharListToTable(@thisColumnValue,'|')
		IF @@ERROR <> 0 GOTO on_error

	while @thisValue is not null 
	begin
		if @thisValue <> @designationName
		begin
			EXEC @rc = membercentral.dbo.ams_saveMemberData 
					@memberID=@memberID, 
					@columnID=@specDesigColumnID, 
					@columnvalueID=null, 
					@columnValue=@thisValue
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
			print 'inserted!!'
		end

		select @thisValue = ltrim(rtrim(min(listitem))) from membercentral.dbo.fn_varCharListToTable(@thisColumnValue,'|') where listitem > @thisValue
		IF @@ERROR <> 0 GOTO on_error
	end

	select @memberID =  min(memberID) from @removeDesignationTbl where memberID > @memberID

end

GOTO on_ok

on_error:
SELECT 0 as success
GOTO on_done

on_ok:
SELECT 1 as success

on_done:

RETURN 0

GO