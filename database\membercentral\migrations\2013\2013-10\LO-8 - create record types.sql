declare
@orgcode varchar(10), 
@orgID int,
@siteID int,
@orgRecordTypeID int,
@firmRecordTypeID int,
@personRecordTypeID int,
@primaryContactRelationshipTypeID int,
@billingContactRelationshipTypeID int,
@marketingContactRelationshipTypeID int,
@representativeRelationshipTypeID int,
@firmBillingContactRelationshipTypeID int,
@firmPrimaryContactRelationshipTypeID int,
@firmMemberRelationshipTypeID int,
@firmPartnerRelationshipTypeID int,
@trashID int

set @orgcode = 'LO'
select @siteID = siteID, @orgID = orgID from sites where sitecode = @orgcode

-- Create Record Types
exec dbo.ams_createRecordType
@orgID=@orgID,
@recordTypeCode = 'Org',
@recordTypeName = 'Organization', 
@recordTypeDesc = 'Organization', 
@isPerson = 0,
@isOrganization = 1,
@recordTypeID = @orgRecordTypeID OUTPUT

exec dbo.ams_createRecordType
@orgID=@orgID,
@recordTypeCode = 'LawFirm',
@recordTypeName = 'Law Firm', 
@recordTypeDesc = 'Law Firm',
@isPerson = 0,
@isOrganization = 1,
@recordTypeID = @firmRecordTypeID OUTPUT

exec dbo.ams_createRecordType
@orgID=@orgID,
@recordTypeCode = 'Individual',
@recordTypeName = 'Individual', 
@recordTypeDesc = 'Individual',
@isPerson = 1,
@isOrganization = 0,
@recordTypeID = @personRecordTypeID OUTPUT

-- Create Record Relationship Type - Organization
exec dbo.ams_createRecordRelationShipType
@orgID=@orgID,
@relationshipTypeCode='OrgPrimaryContact',
@relationshipTypeName='Primary Contact',
@relationshipTypeID =@primaryContactRelationshipTypeID OUTPUT

exec dbo.ams_createRecordRelationShipType
@orgID=@orgID,
@relationshipTypeCode='OrgBillingContact',
@relationshipTypeName='Billing Contact',
@relationshipTypeID =@billingContactRelationshipTypeID OUTPUT

exec dbo.ams_createRecordRelationShipType
@orgID=@orgID,
@relationshipTypeCode='OrgMarketingContact',
@relationshipTypeName='Marketing Contact',
@relationshipTypeID =@marketingContactRelationshipTypeID OUTPUT

exec dbo.ams_createRecordRelationShipType
@orgID=@orgID,
@relationshipTypeCode='OrgRep',
@relationshipTypeName='Representative',
@relationshipTypeID =@representativeRelationshipTypeID OUTPUT

-- Create Record Relationship Type - Law Firm
exec dbo.ams_createRecordRelationShipType
@orgID=@orgID,
@relationshipTypeCode='LawFirmBillingContact',
@relationshipTypeName='Billing Contact',
@relationshipTypeID =@firmBillingContactRelationshipTypeID OUTPUT

exec dbo.ams_createRecordRelationShipType
@orgID=@orgID,
@relationshipTypeCode='LawFirmPrimaryContact',
@relationshipTypeName='Primary Contact',
@relationshipTypeID =@firmPrimaryContactRelationshipTypeID OUTPUT

exec dbo.ams_createRecordRelationShipType
@orgID=@orgID,
@relationshipTypeCode='LawFirmMember',
@relationshipTypeName='Firm Member',
@relationshipTypeID =@firmMemberRelationshipTypeID OUTPUT

exec dbo.ams_createRecordRelationShipType
@orgID=@orgID,
@relationshipTypeCode='LawFirmPartner',
@relationshipTypeName='Firm Partner',
@relationshipTypeID =@firmPartnerRelationshipTypeID OUTPUT

-- Create Record Types Relationship Type for Organizations  Primary Contact, Billing Contact, Marketing Contact, and Representative
exec dbo.ams_createRecordTypesRelationshipTypes
@relationshipTypeID=@primaryContactRelationshipTypeID,
@masterRecordTypeID=@orgRecordTypeID,
@childRecordTypeID=@personRecordTypeID,
@isActive=1,
@recordTypeRelationshipTypeID = @trashID OUTPUT

exec dbo.ams_createRecordTypesRelationshipTypes
@relationshipTypeID=@billingContactRelationshipTypeID,
@masterRecordTypeID=@orgRecordTypeID,
@childRecordTypeID=@personRecordTypeID,
@isActive=1,
@recordTypeRelationshipTypeID = @trashID OUTPUT

exec dbo.ams_createRecordTypesRelationshipTypes
@relationshipTypeID=@marketingContactRelationshipTypeID,
@masterRecordTypeID=@orgRecordTypeID,
@childRecordTypeID=@personRecordTypeID,
@isActive=1,
@recordTypeRelationshipTypeID = @trashID OUTPUT

exec dbo.ams_createRecordTypesRelationshipTypes
@relationshipTypeID=@representativeRelationshipTypeID,
@masterRecordTypeID=@orgRecordTypeID,
@childRecordTypeID=@personRecordTypeID,
@isActive=1,
@recordTypeRelationshipTypeID = @trashID OUTPUT

-- Create Record Types Relationship Type for Law Firms  Billing Contact, Primary Contact, Firm Member, and Firm Partner
exec dbo.ams_createRecordTypesRelationshipTypes
@relationshipTypeID=@firmBillingContactRelationshipTypeID,
@masterRecordTypeID=@firmRecordTypeID,
@childRecordTypeID=@personRecordTypeID,
@isActive=1,
@recordTypeRelationshipTypeID = @trashID OUTPUT

exec dbo.ams_createRecordTypesRelationshipTypes
@relationshipTypeID=@firmPrimaryContactRelationshipTypeID,
@masterRecordTypeID=@firmRecordTypeID,
@childRecordTypeID=@personRecordTypeID,
@isActive=1,
@recordTypeRelationshipTypeID = @trashID OUTPUT

exec dbo.ams_createRecordTypesRelationshipTypes
@relationshipTypeID=@firmMemberRelationshipTypeID,
@masterRecordTypeID=@firmRecordTypeID,
@childRecordTypeID=@personRecordTypeID,
@isActive=1,
@recordTypeRelationshipTypeID = @trashID OUTPUT

exec dbo.ams_createRecordTypesRelationshipTypes
@relationshipTypeID=@firmPartnerRelationshipTypeID,
@masterRecordTypeID=@firmRecordTypeID,
@childRecordTypeID=@personRecordTypeID,
@isActive=1,
@recordTypeRelationshipTypeID = @trashID OUTPUT
GO