use membercentral
GO
ALTER PROC [dbo].[tr_viewTransaction_related_recursive]
@transactionID int

AS

declare @relatedTrans TABLE (
	transactionID int,
	type varchar(30),
	transactionDate datetime,
	amount money,
	detail varchar(max),
	assignedToMember varchar(max),
	assignedToMemberCompany varchar(400),
	parentTransactionID int
)
declare @tmpRelatedTrans TABLE (
	transactionID int PRIMARY KEY,
	type varchar(30),
	transactionDate datetime,
	amount money,
	detail varchar(max),
	assignedToMember varchar(max),
	assignedToMemberCompany varchar(400),
	allocDetail varchar(max),
	allocDetailTransactionID int,
	allocDetailAssignedToMember varchar(max),
	allocDetailAssignedToMemberCompany varchar(400)
)
declare @tmpTIDs TABLE (transactionID int)
declare @tmpRelatedTIDs TABLE (transactionID int PRIMARY KEY)
declare @tmpProcessedTIDs TABLE (transactionID int PRIMARY KEY)
declare @rootTransactionID int
set @rootTransactionID = @transactionID

insert into @tmpTIDs values (@rootTransactionID)

select @transactionID = min(transactionID) from @tmpTIDs
while @transactionID is not null BEGIN
	insert into @tmpRelatedTrans
	EXEC dbo.tr_viewTransaction_related @transactionID

	insert into @relatedTrans (transactionID, type, transactionDate, amount, detail, assignedToMember, assignedToMemberCompany, parentTransactionID)
		output inserted.transactionid 
		into @tmpRelatedTIDs
	select transactionID, type, transactionDate, amount, detail, assignedToMember, assignedToMemberCompany, @transactionID
	from @tmpRelatedTrans
	where transactionID <> @rootTransactionID
	and transactionID not in (select transactionID from @relatedTrans)

	insert into @tmpTIDs
	select transactionID from @tmpRelatedTIDs
		except
	select transactionID from @tmpProcessedTIDs

	delete from @tmpRelatedTrans
	delete from @tmpTIDs where transactionID = @transactionID

	insert into @tmpProcessedTIDs values (@transactionID)

	select @transactionID = min(transactionID) from @tmpTIDs
END

;WITH tree AS (
	select transactionID, type, transactionDate, amount, detail, assignedToMember, assignedToMemberCompany, parentTransactionID, CAST(RIGHT(1000+ROW_NUMBER() OVER (ORDER BY transactionDate, transactionID),3) AS varchar(max)) AS bpath
	from @relatedTrans
	where parentTransactionID = @rootTransactionID
		union all
	select tmp.transactionID, tmp.type, tmp.transactionDate, tmp.amount, tmp.detail, tmp.assignedToMember, tmp.assignedToMemberCompany, tmp.parentTransactionID, tree.bpath + '.' + CAST(RIGHT(1000+ROW_NUMBER() OVER (ORDER BY tmp.transactionDate, tmp.transactionID),3) AS varchar(max))
	from @relatedTrans as tmp
	inner join tree on tmp.parentTransactionID = tree.transactionID
)
select * from tree
order by bPath

GO