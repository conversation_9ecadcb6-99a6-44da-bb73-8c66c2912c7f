use platformMail;
GO

update dbo.email_statuses set status = 'Processing (queued)' where statusCode='Q'
update dbo.email_statuses set status = 'Processing (working)' where statusCode='G'
update dbo.email_statuses set status = 'Processing (merging)' where statusCode='P'
update dbo.email_statuses set status = 'Processing (requeued)', statusOrder=5 where statusCode='R'
update dbo.email_statuses set statusOrder=6 where statusCode='S'


insert into dbo.email_statuses (statusCode, status, statusOrder) values ('sg_process','Processing (validated)',7)
insert into dbo.email_statuses (statusCode, status, statusOrder) values ('sg_defer','Processing (deferred)',8)
insert into dbo.email_statuses (statusCode, status, statusOrder) values ('sg_drop','Dropped',9)
insert into dbo.email_statuses (statusCode, status, statusOrder) values ('sg_deliver','Delivered',10)
insert into dbo.email_statuses (statusCode, status, statusOrder) values ('sg_open','Opened',11)
insert into dbo.email_statuses (statusCode, status, statusOrder) values ('sg_bounce','Bounce',12)
insert into dbo.email_statuses (statusCode, status, statusOrder) values ('sg_spam','Spam Report',13)
GO