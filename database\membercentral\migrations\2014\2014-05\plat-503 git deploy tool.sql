
DECLARE
	@toolTypeID int,
	@toolResourceTypeID int,
	@roleID int,
	@navigationID int,
	@resourceTypeFunctionID int,
	@orgcode varchar(5),
	@orgID int,
	@sitecode varchar(5),
	@siteID int,
	@TopTabNavigationID int,
	@level2NavigationID int,
	@level3NavigationID int,
	@rc int,
	@viewfunctionID int,
	@superAdminRoleID int,
	@viewResourceTypeFunctionID int

select @siteID = dbo.fn_getSiteIDfromSiteCode('MC')
select @orgID = dbo.fn_getOrgIDfromOrgCode('MC')

select @toolTypeID = null, @toolResourceTypeID = null

-- get Admin tool type
select 
	@toolTypeID = toolTypeID,
	@toolResourceTypeID = resourceTypeID
from 
	admin_toolTypes
where 
	toolTypeID = dbo.fn_getAdminToolTypeID('WebsiteAdmin')

EXEC @rc = cms_createSiteResourceFunction @resourceTypeID=@toolResourceTypeID, @functionName='DeployCode', @displayName='Deploy Code', @functionID=@viewfunctionID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
select @viewResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@toolResourceTypeID,@viewfunctionID);
	IF @@ERROR <> 0 GOTO on_error

select 
	@level2NavigationID = n1.navigationID,
	@TopTabNavigationID = n2.navigationID
from 
	dbo.admin_navigation n1
	inner join admin_navigation n2 on
		n2.navigationID = n1.parentNavigationID
		and n2.navName = 'Website'
		and n2.navAreaID = 1
where 
	n1.navName = 'settings'
	and n1.navAreaID = 2

select @resourceTypeFunctionID = resourceTypeFunctionID
from admin_toolTypes tt
inner join cms_siteResourceTypes srt
	on tt.resourceTypeID = srt.resourceTypeID
	and tt.toolType ='WebsiteAdmin'
inner join cms_siteResourceTypeFunctions srtf
	on srtf.resourceTypeID = srt.resourceTypeID
inner join cms_siteResourceFunctions srf
	on srf.functionID = srtf.functionID
	and srf.functionName = 'DeployCode'

select @resourceTypeFunctionID 

BEGIN TRAN
	-- create level3 items
	select @level3NavigationID = null

	EXEC @rc = dbo.createAdminNavigation
		@navName='Code Deploy',
		@navDesc='Code Deploy',
		@parentNavigationID=@level2NavigationID,
		@navAreaID=3,
		@cfcMethod='codeDeploy',
		@isHeader=0,
		@showInNav=1,
		@navigationID=@level3NavigationID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = dbo.createAdminFunctionsDeterminingNav
		@resourceTypeFunctionID=@viewResourceTypeFunctionID,
		@toolTypeID=@toolTypeID,
		@navigationID=@level3NavigationID
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

IF @@TRANCOUNT > 0 COMMIT TRAN

EXEC dbo.createAdminSuite @siteid=@siteID
GOTO on_done

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN

on_done:
