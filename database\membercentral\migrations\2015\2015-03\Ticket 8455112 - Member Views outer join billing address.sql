use membercentral
GO
ALTER PROC [dbo].[ams_createVWMemberData]
@orgID int

-- tl 7/2010 - ensured this causes views to only contain activememberid data

AS

declare @PVTFull varchar(max)
declare @PVTbit varchar(max), @PVTcontent varchar(max), @PVTdate varchar(max), 
		@PVTdecimal2 varchar(max), @PVTdecimal2Multi varchar(max), @PVTdocument varchar(max), 
		@PVTinteger varchar(max), @PVTintegerMulti varchar(max), @PVTstring varchar(max), 
		@PVTstringMulti varchar(max), @PVTxml varchar(max)
declare @sql varchar(max), @cmd varchar(6)
declare @orgcode varchar(10)

-- get columns for pivots
select @PVTbit = COALESCE(@PVTbit + ',', '') + quoteName(mdc.columnName)
	from dbo.ams_memberdatacolumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
		and mdcdt.dataTypeCode = 'BIT'
	where mdc.orgid = @orgid
	order by mdc.columnName
select @PVTcontent = COALESCE(@PVTcontent + ',', '') + quoteName(mdc.columnName)
	from dbo.ams_memberdatacolumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
		and mdcdt.dataTypeCode = 'CONTENTOBJ'
	where mdc.orgid = @orgid
	order by mdc.columnName
select @PVTdate = COALESCE(@PVTdate + ',', '') + quoteName(mdc.columnName)
	from dbo.ams_memberdatacolumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
		and mdcdt.dataTypeCode = 'DATE'
	where mdc.orgid = @orgid
	order by mdc.columnName
select @PVTdecimal2 = COALESCE(@PVTdecimal2 + ',', '') + quoteName(mdc.columnName)
	from dbo.ams_memberdatacolumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
		and mdcdt.dataTypeCode = 'DECIMAL2'
	where mdc.orgid = @orgid
	and mdc.allowMultiple = 0
	order by mdc.columnName
select @PVTdecimal2Multi = COALESCE(@PVTdecimal2Multi + ',', '') + quoteName(mdc.columnName)
	from dbo.ams_memberdatacolumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
		and mdcdt.dataTypeCode = 'DECIMAL2'
	where mdc.orgid = @orgid
	and mdc.allowMultiple = 1
	order by mdc.columnName
select @PVTdocument = COALESCE(@PVTdocument + ',', '') + quoteName(mdc.columnName)
	from dbo.ams_memberdatacolumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
		and mdcdt.dataTypeCode = 'DOCUMENTOBJ'
	where mdc.orgid = @orgid
	order by mdc.columnName
select @PVTinteger = COALESCE(@PVTinteger + ',', '') + quoteName(mdc.columnName)
	from dbo.ams_memberdatacolumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
		and mdcdt.dataTypeCode = 'INTEGER'
	where mdc.orgid = @orgid
	and mdc.allowMultiple = 0
	order by mdc.columnName
select @PVTintegerMulti = COALESCE(@PVTintegerMulti + ',', '') + quoteName(mdc.columnName)
	from dbo.ams_memberdatacolumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
		and mdcdt.dataTypeCode = 'INTEGER'
	where mdc.orgid = @orgid
	and mdc.allowMultiple = 1
	order by mdc.columnName
select @PVTstring = COALESCE(@PVTstring + ',', '') + quoteName(mdc.columnName)
	from dbo.ams_memberdatacolumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
		and mdcdt.dataTypeCode = 'STRING'
	where mdc.orgid = @orgid
	and mdc.allowMultiple = 0
	order by mdc.columnName
select @PVTstringMulti = COALESCE(@PVTstringMulti + ',', '') + quoteName(mdc.columnName)
	from dbo.ams_memberdatacolumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
		and mdcdt.dataTypeCode = 'STRING'
	where mdc.orgid = @orgid
	and mdc.allowMultiple = 1
	order by mdc.columnName
select @PVTxml = COALESCE(@PVTxml + ',', '') + quoteName(mdc.columnName)
	from dbo.ams_memberdatacolumns as mdc
	inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
		and mdcdt.dataTypeCode = 'XML'
	where mdc.orgid = @orgid
	order by mdc.columnName


-- construct pvtfull
select @PVTFull = ''
if LEN(@PVTbit) > 0
	select @PVTFull = case when len(@PVTFull) > 0 then @PVTFull + ',' else '' end + replace(@PVTbit,'[','vwbit.[')
if LEN(@PVTcontent) > 0
	select @PVTFull = case when len(@PVTFull) > 0 then @PVTFull + ',' else '' end + replace(@PVTcontent,'[','vwcontent.[')
if LEN(@PVTdate) > 0
	select @PVTFull = case when len(@PVTFull) > 0 then @PVTFull + ',' else '' end + replace(@PVTdate,'[','vwdate.[')
if LEN(@PVTdecimal2) > 0
	select @PVTFull = case when len(@PVTFull) > 0 then @PVTFull + ',' else '' end + replace(@PVTdecimal2,'[','vwdecimal2.[')
if LEN(@PVTdecimal2Multi) > 0
	select @PVTFull = case when len(@PVTFull) > 0 then @PVTFull + ',' else '' end + replace(@PVTdecimal2Multi,'[','vwdecimal2M.[')
if LEN(@PVTdocument) > 0
	select @PVTFull = case when len(@PVTFull) > 0 then @PVTFull + ',' else '' end + replace(@PVTdocument,'[','vwdocument.[')
if LEN(@PVTinteger) > 0
	select @PVTFull = case when len(@PVTFull) > 0 then @PVTFull + ',' else '' end + replace(@PVTinteger,'[','vwinteger.[')
if LEN(@PVTintegerMulti) > 0
	select @PVTFull = case when len(@PVTFull) > 0 then @PVTFull + ',' else '' end + replace(@PVTintegerMulti,'[','vwintegerM.[')
if LEN(@PVTstring) > 0
	select @PVTFull = case when len(@PVTFull) > 0 then @PVTFull + ',' else '' end + replace(@PVTstring,'[','vwstring.[')
if LEN(@PVTstringMulti) > 0
	select @PVTFull = case when len(@PVTFull) > 0 then @PVTFull + ',' else '' end + replace(@PVTstringMulti,'[','vwstringM.[')
if LEN(@PVTxml) > 0
	select @PVTFull = case when len(@PVTFull) > 0 then @PVTFull + ',' else '' end + replace(@PVTxml,'[','vwxml.[')


-- get orgcode
select @orgcode = orgcode from organizations where orgID = @orgid

-- loop over websitetypes to generate sql joins and selects
declare @minWTID int, @minWT varchar(20), @WTsqlJoin varchar(max), @WTsqlSelect varchar(max)
select @WTsqlJoin = '', @WTsqlSelect = ''
select @minWTID = min(websiteTypeID) from dbo.ams_memberWebsiteTypes where orgID = @orgID
while @minWTID is not null BEGIN
	select @minWT = websiteType from dbo.ams_memberWebsiteTypes where websiteTypeID = @minWTID
	select @WTsqlSelect = @WTsqlSelect + char(10) + ', mw' + cast(@minWTID as varchar(10)) + '.website as ' + quoteName(@minWT)
	select @WTsqlJoin = @WTsqlJoin + char(10) + 'left outer join dbo.ams_memberWebsites as mw' + cast(@minWTID as varchar(10)) + ' on mw' + cast(@minWTID as varchar(10)) + '.memberid = m.memberid and mw' + cast(@minWTID as varchar(10)) + '.websiteTypeID = ' + cast(@minWTID as varchar(10)) + ' '
	select @minWTID = min(websiteTypeID) from dbo.ams_memberWebsiteTypes where orgID = @orgID and websiteTypeID > @minWTID
END

-- loop over emailtypes to generate sql joins and selects
declare @minETID int, @minET varchar(20), @ETsqlJoin varchar(max), @ETsqlSelect varchar(max)
select @ETsqlJoin = '', @ETsqlSelect = ''
select @minETID = min(emailTypeID) from dbo.ams_memberEmailTypes where orgID = @orgID
while @minETID is not null BEGIN
	select @minET = emailType from dbo.ams_memberEmailTypes where emailTypeID = @minETID
	select @ETsqlSelect = @ETsqlSelect + char(10) + ', me' + cast(@minETID as varchar(10)) + '.email as ' + quoteName(@minET)
	select @ETsqlJoin = @ETsqlJoin + char(10) + 'left outer join dbo.ams_memberEmails as me' + cast(@minETID as varchar(10)) + ' on me' + cast(@minETID as varchar(10)) + '.memberid = m.memberid and me' + cast(@minETID as varchar(10)) + '.emailTypeID = ' + cast(@minETID as varchar(10)) + ' '
	select @minETID = min(emailTypeID) from dbo.ams_memberEmailTypes where orgID = @orgID and emailTypeID > @minETID
END

-- loop over addresstypes and phone types and district types to generate sql joins and selects
declare @minATID int, @minAT varchar(20), @minPTID int, @minPT varchar(20), @minDTID int, @minDT varchar(20)
declare @hasAttn bit, @hasAddress2 bit, @hasAddress3 bit, @hasCounty bit, @districtMatching bit
declare @ATsqlJoin varchar(max), @ATsqlSelect varchar(max)

declare @tblAddrTypes table (addressTypeID int)
insert into @tblAddrTypes (addressTypeID)
select addressTypeID 
from dbo.ams_memberAddressTypes 
where orgID = @orgID
	union
select 0

select @ATsqlJoin = '', @ATsqlSelect = ''
select @minATID = min(addressTypeID) from @tblAddrTypes
while @minATID is not null BEGIN
	IF @minATID = 0 BEGIN
		SET @minAT = 'Designated Billing'
		select @hasAttn=max(cast(hasAttn as tinyint)), @hasAddress2=max(cast(hasAddress2 as tinyint)), 
			@hasAddress3=max(cast(hasAddress3 as tinyint)), @hasCounty=max(cast(hasCounty as tinyint)), 
			@districtMatching=max(cast(districtMatching as tinyint))
		from dbo.ams_memberAddressTypes 
		where orgID = @orgID
	END ELSE BEGIN
		select @minAT=addressType, @hasAttn=hasAttn, @hasAddress2=hasAddress2, @hasAddress3=hasAddress3, 
			@hasCounty=hasCounty, @districtMatching=districtMatching
		from dbo.ams_memberAddressTypes 
		where addressTypeID = @minATID
	END

	select @ATsqlSelect = @ATsqlSelect + char(10) + 
		case when @hasAttn = 1 then ', ma' + cast(@minATID as varchar(5)) + '.attn as [' + @minAT + '_attn]' else '' end +
		', ma' + cast(@minATID as varchar(5)) + '.address1 as [' + @minAT + '_address1]' +
		case when @hasAddress2 = 1 then ', ma' + cast(@minATID as varchar(5)) + '.address2 as [' + @minAT + '_address2]' else '' end +
		case when @hasAddress3 = 1 then ', ma' + cast(@minATID as varchar(5)) + '.address3 as [' + @minAT + '_address3]' else '' end +
		', ma' + cast(@minATID as varchar(5)) + '.city as [' + @minAT + '_city]' +
		', s' + cast(@minATID as varchar(5)) + '.code as [' + @minAT + '_stateprov]' +
		', ma' + cast(@minATID as varchar(5)) + '.postalCode as [' + @minAT + '_postalCode]' +
		case when @hasCounty = 1 then ', ma' + cast(@minATID as varchar(5)) + '.county as [' + @minAT + '_county]' else '' end +
		', c' + cast(@minATID as varchar(5)) + '.country as [' + @minAT + '_country]'
	IF @minATID = 0	
		select @ATsqlSelect = @ATsqlSelect + ', mat' + cast(@minATID as varchar(5)) + '.addressType as [' + @minAT + '_addressType]'
		
	IF @minATID = 0	BEGIN
		select @ATsqlJoin = @ATsqlJoin + char(10) + '
			left outer join dbo.ams_memberAddresses as ma' + cast(@minATID as varchar(10)) + ' 
				inner join dbo.ams_memberAddressTypes as mat' + cast(@minATID as varchar(10)) + ' on mat' + cast(@minATID as varchar(10)) + '.addressTypeID = ma' + cast(@minATID as varchar(10)) + '.addressTypeID 
			on ma' + cast(@minATID as varchar(10)) + '.memberid = m.memberid and ma' + cast(@minATID as varchar(10)) + '.addressTypeID = m.billingAddressTypeID '
	END ELSE
		select @ATsqlJoin = @ATsqlJoin + char(10) + 'left outer join dbo.ams_memberAddresses as ma' + cast(@minATID as varchar(10)) + ' on ma' + cast(@minATID as varchar(10)) + '.memberid = m.memberid and ma' + cast(@minATID as varchar(10)) + '.addressTypeID = ' + cast(@minATID as varchar(10))

	select @ATsqlJoin = @ATsqlJoin + char(10) + 'left outer join dbo.ams_states as s' + cast(@minATID as varchar(10)) + ' on s' + cast(@minATID as varchar(10)) + '.stateid = ma' + cast(@minATID as varchar(10)) + '.stateid '
	select @ATsqlJoin = @ATsqlJoin + char(10) + 'left outer join dbo.ams_countries as c' + cast(@minATID as varchar(10)) + ' on c' + cast(@minATID as varchar(10)) + '.countryid = ma' + cast(@minATID as varchar(10)) + '.countryid '

	select @minPTID = min(phoneTypeID) from dbo.ams_memberPhoneTypes where orgID = @orgID
	while @minPTID is not null BEGIN
		select @minPT = phoneType from dbo.ams_memberPhoneTypes where phoneTypeID = @minPTID

		select @ATsqlSelect = @ATsqlSelect + ', mp' + cast(@minATID as varchar(10)) + cast(@minPTID as varchar(10)) + '.phone as [' + @minAT + '_' + @minPT + ']'
		select @ATsqlJoin = @ATsqlJoin + char(10) + 'left outer join dbo.ams_memberPhones as mp' + cast(@minATID as varchar(10)) + cast(@minPTID as varchar(10)) + ' on mp' + cast(@minATID as varchar(10)) + cast(@minPTID as varchar(10)) + '.addressID = ma' + cast(@minATID as varchar(10)) + '.addressID and mp' + cast(@minATID as varchar(10)) + cast(@minPTID as varchar(10)) + '.phoneTypeID = ' + cast(@minPTID as varchar(10)) + ' '

		select @minPTID = min(phoneTypeID) from dbo.ams_memberPhoneTypes where orgID = @orgID and phoneTypeID > @minPTID
	END

	IF @districtMatching = 1 BEGIN
		select @minDTID = min(districtTypeID) from dbo.ams_memberDistrictTypes where orgID = @orgID
		while @minDTID is not null BEGIN
			select @minDT = districtType from dbo.ams_memberDistrictTypes where districtTypeID = @minDTID

			select @ATsqlSelect = @ATsqlSelect + ',
				madd' + cast(@minATID as varchar(10)) + cast(@minDTID as varchar(10)) + '.vendorValue as [' + @minAT + '_' + @minDT + ']'
			select @ATsqlJoin = @ATsqlJoin + char(10) + '
				left outer join (
					select mad' + cast(@minATID as varchar(10)) + cast(@minDTID as varchar(10)) + '.addressID, dbo.PipeList(mdv' + cast(@minATID as varchar(10)) + cast(@minDTID as varchar(10)) + '.vendorValue) as vendorValue
					from dbo.ams_memberAddressData as mad' + cast(@minATID as varchar(10)) + cast(@minDTID as varchar(10)) + '
					inner join dbo.ams_memberDistrictValues as mdv' + cast(@minATID as varchar(10)) + cast(@minDTID as varchar(10)) + '
						on mdv' + cast(@minATID as varchar(10)) + cast(@minDTID as varchar(10)) + '.valueID = mad' + cast(@minATID as varchar(10)) + cast(@minDTID as varchar(10)) + '.valueid 
						and mdv' + cast(@minATID as varchar(10)) + cast(@minDTID as varchar(10)) + '.districtTypeID = ' + cast(@minDTID as varchar(10)) + '
					group by mad' + cast(@minATID as varchar(10)) + cast(@minDTID as varchar(10)) + '.addressID
				) as madd' + cast(@minATID as varchar(10)) + cast(@minDTID as varchar(10)) + ' on madd' + cast(@minATID as varchar(10)) + cast(@minDTID as varchar(10)) + '.addressID = ma' + cast(@minATID as varchar(10)) + '.addressID'

			select @minDTID = min(districtTypeID) from dbo.ams_memberDistrictTypes where orgID = @orgID and districtTypeID > @minDTID
		END
	END

	select @minATID = min(addressTypeID) from @tblAddrTypes where addressTypeID > @minATID
END

-- loop over professional license types to generate sql joins and selects
declare @minPLTID int, @minPL varchar(200)
declare @PLTsqlJoin varchar(max), @PLTsqlSelect varchar(max)
select @PLTsqlJoin = '', @PLTsqlSelect = ''
select @minPLTID = min(PLTypeID) from dbo.ams_memberProfessionalLicenseTypes where orgID = @orgID
while @minPLTID is not null BEGIN
	select @minPL=PLName
		from dbo.ams_memberProfessionalLicenseTypes 
		where PLTypeID = @minPLTID

	select @PLTsqlSelect = @PLTsqlSelect + char(10) + 
		', mpl' + cast(@minPLTID as varchar(10)) + '.licensenumber as [' + @minPL + '_licenseNumber]' +
		', mpl' + cast(@minPLTID as varchar(10)) + '.activeDate as [' + @minPL + '_activeDate]' +
		', mpls' + cast(@minPLTID as varchar(10)) + '.statusName as [' + @minPL + '_status]'
		
	select @PLTsqlJoin = @PLTsqlJoin + char(10) + 'left outer join dbo.ams_memberProfessionalLicenses as mpl' + cast(@minPLTID as varchar(10)) + ' on mpl' + cast(@minPLTID as varchar(10)) + '.memberid = m.memberid and mpl' + cast(@minPLTID as varchar(10)) + '.PLTypeID = ' + cast(@minPLTID as varchar(10))
	select @PLTsqlJoin = @PLTsqlJoin + char(10) + 'left outer join dbo.ams_memberProfessionalLicenseStatuses as mpls' + cast(@minPLTID as varchar(10)) + ' on mpls' + cast(@minPLTID as varchar(10)) + '.PLStatusID = mpl' + cast(@minPLTID as varchar(10)) + '.PLStatusID and mpls' + cast(@minPLTID as varchar(10)) + '.orgID = ' + cast(@orgID as varchar(10))

	select @minPLTID = min(PLTypeID) from dbo.ams_memberProfessionalLicenseTypes where orgID = @orgID and PLTypeID > @minPLTID
END

IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[vw_memberData_' + @orgcode + ']'))
	SELECT @cmd = 'ALTER'
ELSE
	SELECT @cmd = 'CREATE'

select @sql = @cmd + ' VIEW dbo.vw_memberData_' + @orgcode + ' WITH SCHEMABINDING 
AS '

IF len(@PVTFull) > 0 BEGIN
	select @sql = @sql + '
	select m.memberid' + @WTsqlSelect + @ETsqlSelect + @ATsqlSelect + @PLTsqlSelect + ', 
	' + @PVTFull + '
	from dbo.ams_members as m' + @WTsqlJoin + char(10) + @ETsqlJoin + char(10) + @ATsqlJoin + char(10) + @PLTsqlJoin

	IF len(@PVTbit) > 0
		select @sql = @sql + '
		-- bit
		left outer join (
			select memberid, ' + @PVTbit + '
			from (
				select vw.memberid, mdc.columnName, cast(vw.columnValue as tinyint) as columnValue
				from dbo.vw_memberData__bit as vw WITH (NOEXPAND)
				inner join dbo.ams_memberdatacolumns as mdc on mdc.columnID = vw.columnID
					and mdc.orgid = ' + cast(@orgid as varchar(10)) + '
			) as orgvw
			PIVOT (max(columnValue) FOR columnName in (' + @PVTbit + ')) as pvt
		) as vwbit on vwbit.memberid = m.memberid
		'

	IF len(@PVTcontent) > 0
		select @sql = @sql + '
		-- content
		left outer join (
			select memberid, ' + @PVTcontent + '
			from (
				select md.memberid, mdc.columnName, cv.rawContent
				from dbo.ams_memberData as md
				inner join dbo.ams_members as m on m.memberID = md.memberID and m.memberID = m.activeMemberID
				inner join dbo.ams_memberdataColumnValues as mdcv on mdcv.valueID = md.valueID
				inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID and mdc.orgID = ' + cast(@orgid as varchar(10)) + '
				inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID and mdcdt.dataTypeCode = ''CONTENTOBJ''
				inner join dbo.cms_content as c on c.siteResourceID = mdcv.columnValueSiteResourceID
				inner join dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID and cl.languageID = 1
				inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
			) as orgvw
			PIVOT (min(rawContent) FOR columnName in (' + @PVTcontent + ')) as pvt
		) as vwcontent on vwcontent.memberid = m.memberid
		'

	IF len(@PVTdate) > 0
		select @sql = @sql + '
		-- date
		left outer join (
			select memberid, ' + @PVTdate + '
			from (
				select vw.memberid, mdc.columnName, vw.columnValue
				from dbo.vw_memberData__date as vw WITH (NOEXPAND)
				inner join dbo.ams_memberdatacolumns as mdc on mdc.columnID = vw.columnID
					and mdc.orgid = ' + cast(@orgid as varchar(10)) + '
			) as orgvw
			PIVOT (min(columnValue) FOR columnName in (' + @PVTdate + ')) as pvt
		) as vwdate on vwdate.memberid = m.memberid
		'

	IF len(@PVTdecimal2) > 0
		select @sql = @sql + '
		-- decimal2
		left outer join (
			select memberid, ' + @PVTdecimal2 + '
			from (
				select vw.memberid, mdc.columnName, vw.columnValue
				from dbo.vw_memberData__decimal2 as vw WITH (NOEXPAND)
				inner join dbo.ams_memberdatacolumns as mdc on mdc.columnID = vw.columnID
					and mdc.orgid = ' + cast(@orgid as varchar(10)) + ' 
				where mdc.allowMultiple = 0
			) as orgvw
			PIVOT (min(columnValue) FOR columnName in (' + @PVTdecimal2 + ')) as pvt
		) as vwdecimal2 on vwdecimal2.memberid = m.memberid
		'

	IF len(@PVTdecimal2Multi) > 0
		select @sql = @sql + '
		-- decimal2
		left outer join (
			select memberid, ' + @PVTdecimal2Multi + '
			from (
				select vw.memberid, mdc.columnName, dbo.PipeList(vw.columnValue) as columnValue
				from dbo.vw_memberData__decimal2 as vw WITH (NOEXPAND)
				inner join dbo.ams_memberdatacolumns as mdc on mdc.columnID = vw.columnID
					and mdc.orgid = ' + cast(@orgid as varchar(10)) + ' 
				group by vw.memberid, mdc.columnName
			) as orgvw
			PIVOT (min(columnValue) FOR columnName in (' + @PVTdecimal2Multi + ')) as pvt
		) as vwdecimal2M on vwdecimal2M.memberid = m.memberid
		'

	IF len(@PVTdocument) > 0
		select @sql = @sql + '
		-- document
		left outer join (
			select memberid, ' + @PVTdocument + '
			from (
				select md.memberid, mdc.columnName, dv.filename as columnValue
				from dbo.ams_memberData as md
				inner join dbo.ams_members as m on m.memberID = md.memberID and m.memberID = m.activeMemberID
				inner join dbo.ams_memberdataColumnValues as mdcv on mdcv.valueID = md.valueID
				inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID and mdc.orgID = ' + cast(@orgid as varchar(10)) + '
				inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID and mdcdt.dataTypeCode = ''DOCUMENTOBJ''
				inner join dbo.cms_documents as d on d.siteResourceID = mdcv.columnValueSiteResourceID
				inner join dbo.cms_documentLanguages as dl on dl.documentID = d.documentID and dl.languageID = 1
				inner join dbo.cms_documentVersions as dv on dv.documentLanguageID = dl.documentLanguageID and dv.isActive = 1
			) as orgvw
			PIVOT (min(columnValue) FOR columnName in (' + @PVTdocument + ')) as pvt
		) as vwdocument on vwdocument.memberid = m.memberid
		'

	IF len(@PVTinteger) > 0
		select @sql = @sql + '
		-- integer
		left outer join (
			select memberid, ' + @PVTinteger + '
			from (
				select vw.memberid, mdc.columnName, vw.columnValue
				from dbo.vw_memberData__integer as vw WITH (NOEXPAND)
				inner join dbo.ams_memberdatacolumns as mdc on mdc.columnID = vw.columnID
					and mdc.orgid = ' + cast(@orgid as varchar(10)) + ' 
				where mdc.allowMultiple = 0 
			) as orgvw
			PIVOT (min(columnValue) FOR columnName in (' + @PVTinteger + ')) as pvt
		) as vwinteger on vwinteger.memberid = m.memberid
		'

	IF len(@PVTintegerMulti) > 0
		select @sql = @sql + '
		-- integer
		left outer join (
			select memberid, ' + @PVTintegerMulti + '
			from (
				select vw.memberid, mdc.columnName, dbo.PipeList(vw.columnValue) as columnValue
				from dbo.vw_memberData__integer as vw WITH (NOEXPAND)
				inner join dbo.ams_memberdatacolumns as mdc on mdc.columnID = vw.columnID
					and mdc.orgid = ' + cast(@orgid as varchar(10)) + ' 
				where mdc.allowMultiple = 1 
				group by vw.memberid, mdc.columnName
			) as orgvw
			PIVOT (min(columnValue) FOR columnName in (' + @PVTintegerMulti + ')) as pvt
		) as vwintegerM on vwintegerM.memberid = m.memberid
		'

	IF len(@PVTstring) > 0
		select @sql = @sql + '
		-- string
		left outer join (
			select memberid, ' + @PVTstring + '
			from (
				select vw.memberid, mdc.columnName, vw.columnValue
				from dbo.vw_memberData__string as vw WITH (NOEXPAND)
				inner join dbo.ams_memberdatacolumns as mdc on mdc.columnID = vw.columnID
					and mdc.orgid = ' + cast(@orgid as varchar(10)) + ' 
				where mdc.allowMultiple = 0 
			) as orgvw
			PIVOT (min(columnValue) FOR columnName in (' + @PVTstring + ')) as pvt
		) as vwstring on vwstring.memberid = m.memberid
		'

	IF len(@PVTstringMulti) > 0
		select @sql = @sql + '
		-- string
		left outer join (
			select memberid, ' + @PVTstringMulti + '
			from (
				select vw.memberid, mdc.columnName, dbo.PipeList(vw.columnValue) as columnValue
				from dbo.vw_memberData__string as vw WITH (NOEXPAND)
				inner join dbo.ams_memberdatacolumns as mdc on mdc.columnID = vw.columnID
					and mdc.orgid = ' + cast(@orgid as varchar(10)) + ' 
				where mdc.allowMultiple = 1 
				group by vw.memberid, mdc.columnName
			) as orgvw
			PIVOT (min(columnValue) FOR columnName in (' + @PVTstringMulti + ')) as pvt
		) as vwstringM on vwstringM.memberid = m.memberid
		'

	IF len(@PVTxml) > 0
		select @sql = @sql + '
		-- xml
		left outer join (
			select memberid, ' + @PVTxml + '
			from (
				select vw.memberid, mdc.columnName, cast(vw.columnValue as varchar(max)) as columnValue
				from dbo.vw_memberData__xml as vw WITH (NOEXPAND)
				inner join dbo.ams_memberdatacolumns as mdc on mdc.columnID = vw.columnID
					and mdc.orgid = ' + cast(@orgid as varchar(10)) + '
			) as orgvw
			PIVOT (min(columnValue) FOR columnName in (' + @PVTxml + ')) as pvt
		) as vwxml on vwxml.memberid = m.memberid

		'
	select @sql = @sql + char(10) + '
		where m.orgID = ' + cast(@orgid as varchar(10)) + ' 
		and m.memberid = m.activeMemberID 
		and m.status <> ''D'''

END
ELSE
	select @sql = @sql + '
	select m.memberid' + @WTsqlSelect + @ETsqlSelect + @ATsqlSelect + @PLTsqlSelect + ' 
	from dbo.ams_members as m' + @WTsqlJoin + char(10) + @ETsqlJoin + char(10) + @ATsqlJoin + char(10) + @PLTsqlJoin + ' 
	where m.orgid = ' + cast(@orgid as varchar(10)) + ' 
	and m.memberid = m.activeMemberID 
	and m.status <> ''D''
	'


EXEC(@sql)

RETURN 0
GO

declare @orgID int
select @orgID = min(orgID) from organizations
while @orgID is not null BEGIN
	EXEC dbo.ams_createVWMemberData @orgID
	select @orgID = min(orgID) from organizations where orgID > @orgID
END
GO



