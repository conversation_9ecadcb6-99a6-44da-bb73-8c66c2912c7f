ALTER PROC [dbo].[up_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]
AS

DECLARE @errmsg nvarchar(2048), @severity tinyint, @state tinyint, @errno int, @proc sysname, @lineno int;

SELECT	@errmsg = error_message(), @severity = error_severity(), @state = error_state(), 
		@errno = error_number(), @proc = error_procedure(), @lineno = error_line();
   
IF @errmsg NOT LIKE '***%'
	SELECT @errmsg = '*** ' + coalesce(quotename(@proc), '<dynamic SQL>') + ', Line ' + ltrim(str(@lineno)) + '. Errno ' + ltrim(str(@errno)) + ': ' + @errmsg;

RAISERROR('%s', @severity, @state, @errmsg); -- use this approach in case errmsg has a percent character

on_done:
RETURN 0;
GO
