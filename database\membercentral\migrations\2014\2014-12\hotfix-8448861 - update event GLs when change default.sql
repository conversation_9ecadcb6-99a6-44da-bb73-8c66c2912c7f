use membercentral
GO

CREATE PROC dbo.ev_updateEventGLNoReg
AS

IF OBJECT_ID('tempdb..#tmpEvents') IS NOT NULL
	DROP TABLE #tmpEvents

-- all events with gl not the default and no full reg
select distinct e.eventID, c.defaultGLAccountID
into #tmpEvents
from dbo.ev_events as e
inner join dbo.ev_calendarEvents as ce on ce.sourceEventID = e.eventID and ce.calendarID = ce.sourceCalendarID
inner join dbo.ev_calendars as c on c.calendarID = ce.sourceCalendarID
where c.defaultGLAccountID <> e.GLAccountID
and not exists (
	select e2.eventID
	from ev_events as e2
	inner join ev_calendarEvents as ce2 on ce2.sourceEventID = e2.eventID and ce2.calendarID = ce2.sourceCalendarID
	inner join ev_calendars as c2 on c2.calendarID = ce2.sourceCalendarID
	inner join ev_registration as r2 on r2.eventID = e2.eventID and r2.status = 'A' and r2.registrationTypeID = 1
	where nullIf(e2.altRegistrationURL,'') is null
	and e2.eventID = e.eventID
)

-- update GLs
update e
set e.GLAccountID = tmp.defaultGLAccountID
from dbo.ev_events as e
inner join #tmpEvents as tmp on tmp.eventID = e.eventID

IF OBJECT_ID('tempdb..#tmpEvents') IS NOT NULL
	DROP TABLE #tmpEvents

GO