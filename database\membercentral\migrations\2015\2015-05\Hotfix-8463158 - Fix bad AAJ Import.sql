declare @subsToDelete TABLE (rootSubscriberID int)
declare @batchIDToRemove TABLE (batchID int)

declare @performDelete int, @sitecode varchar(5), @orgID int, @siteID int, @subscriptionTypeName varchar(100), @subscriptionName varchar(100)

set @sitecode = 'SC'
insert into @batchIDToRemove (batchID) values (105789)



-- set to 0 to view rows that will be deleted
-- set to 1 to actually delete the data

set @performDelete = 0



--- DO NOT CHANGE BELOW

select @siteID = dbo.fn_getSiteIDFromSiteCode(@sitecode)
select @orgID = dbo.fn_getOrgIDFromSiteID(@siteID)



insert into @subsToDelete (rootSubscriberID)
select ss.rootsubscriberID
from tr_batches b
inner join @batchIDToRemove BR
	on br.batchID = b.batchID
inner join dbo.tr_batchTransactions bt
	on bt.batchID = b.batchID
inner join tr_transactions t
	on t.transactionID = bt.transactionID
inner join dbo.tr_types tt
	on tt.typeID = t.typeID
	and tt.type in ('Allocation')
inner join dbo.tr_relationships tr
	on tr.transactionID = t.transactionID
inner join tr_transactions at
	on at.transactionID = tr.appliedToTransactionID
inner join dbo.tr_types att
	on att.typeID = at.typeID
	and att.type in ('Sale')
inner join dbo.tr_applications ta
	on ta.transactionID = at.transactionID
	and ta.itemType = 'Dues'
inner join dbo.sub_subscribers ss
	on ss.subscriberID = ta.itemID

if @performDelete <> 1
BEGIN
	select *
	from @subsToDelete sd
	inner join sub_subscribers ss
		on ss.rootSubscriberID = sd.rootSubscriberID
END

if @performDelete = 1
BEGIN
	
	update ta set
		status = 'D'
	from @subsToDelete sd
	inner join sub_subscribers ss
		on ss.rootSubscriberID = sd.rootSubscriberID
	inner join dbo.tr_applications ta
		on ta.itemID = ss.subscriberID
		and ta.itemType = 'Dues'

	-- wipe statusHistory table for subtype
	delete sh
	from @subsToDelete sd
	inner join sub_subscribers ss
		on ss.rootSubscriberID = sd.rootSubscriberID
	inner join sub_statusHistory sh
		on sh.subscriberID = ss.subscriberID

	-- wipe paymentStatusHistory table for subtype
	delete psh
	from @subsToDelete sd
	inner join sub_subscribers ss
		on ss.rootSubscriberID = sd.rootSubscriberID
	inner join sub_paymentStatusHistory psh
		on psh.subscriberID = ss.subscriberID

	-- wipe actual subscribers
	delete ss
	from @subsToDelete sd
	inner join sub_subscribers ss
		on ss.rootSubscriberID = sd.rootSubscriberID

	exec dbo.sub_fixGroups @siteID=@siteID, @bypassQueue=0

END



-- VOID ALL TRANSACTIONS TIED TO A BATCH

declare @enteredByMemberID int, @transactionID int, @vidPool xml, @tids xml
declare @transToVoid TABLE (transactionID int, siteID int)


PRINT 'insert into @transToVoid(transactionID, siteID)'
insert into @transToVoid(transactionID, siteID)

-- insert whatever transaction IDs need to be voided 
-- keep in mind that recursive voids might be called
-- (i.e. voiding payments or sales will auto-void associated allocations)

select t.transactionID, t.recordedOnSiteID 
from tr_batches b
inner join @batchIDToRemove BR
	on br.batchID = b.batchID
	and b.orgID = @orgID
inner join dbo.tr_batchTransactions bt
	on bt.batchID = b.batchID
inner join tr_transactions t
	on t.transactionID = bt.transactionID
inner join dbo.tr_types tt
	on tt.typeID = t.typeID
	and tt.type in ('Payment')

union

select at.transactionID, at.recordedOnSiteID 
from tr_batches b
inner join @batchIDToRemove BR
	on br.batchID = b.batchID
	and b.orgID = @orgID
inner join dbo.tr_batchTransactions bt
	on bt.batchID = b.batchID
inner join tr_transactions t
	on t.transactionID = bt.transactionID
inner join dbo.tr_types tt
	on tt.typeID = t.typeID
	and tt.type in ('Allocation')
inner join dbo.tr_relationships tr
	on tr.transactionID = t.transactionID
inner join tr_transactions at
	on at.transactionID = tr.appliedToTransactionID
inner join dbo.tr_types att
	on att.typeID = at.typeID
	and att.type in ('Sale')


if @performDelete <> 1
BEGIN
	select *
	from @transToVoid 
END

if @performDelete = 1
BEGIN
		


	select @enteredByMemberID = memberID from membercentral.dbo.ams_members where memberNumber = 'SYSTEM' and orgID = 1
	select @transactionID = min(transactionID) from @transToVoid
	while @transactionID is not null BEGIN
		set @vidPool = null
		set @tids = null

		select @siteID = siteID from @transToVoid where transactionID = @transactionID
		EXEC dbo.tr_voidTransaction @siteID, @enteredByMemberID, null, @transactionID, 1, @vidPool OUTPUT, @tids OUTPUT
		select @transactionID = min(transactionID) from @transToVoid where transactionID > @transactionID
	END
END

