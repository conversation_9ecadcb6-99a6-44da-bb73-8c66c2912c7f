ALTER TABLE dbo.ev_registration ADD freeRateDisplay varchar(10) NOT NULL CONSTRAINT DF_ev_registration_freeRateDisplay DEFAULT 'FREE'
GO

ALTER PROC [dbo].[ev_getRegistrationMetaByEventID]
@eventID int,
@languageID int

AS

select r.registrationid, r.registrationtypeID, rt.registrationtype, r.startdate, r.enddate, 
	r.registrantCap, r.notifyEmail, r.replyToEmail, r.status, r.isPriceBasedOnActual, r.bulkCountByRate, 
	r.expirationcontentID as expireContentID, expireContent.contentTitle as expireContentTitle, 
	expireContent.rawContent as expireContent,
	r.registrantCapcontentID as registrantCapContentID, 
	registrantCapContent.contentTitle as registrantCapContentTitle, 
	registrantCapContent.rawContent as registrantCapContent,
	dbo.fn_getRegCapReached(r.eventID) as regCapReached,
	r.freeRateDisplay
from dbo.ev_registration as r
inner join dbo.ev_registrationTypes as rt on rt.registrationTypeID = r.registrationTypeID
inner join dbo.ev_events as e on e.eventid = r.eventid and e.status <> 'D'
cross apply dbo.fn_getContent(r.expirationcontentID,@languageID) as expireContent
cross apply dbo.fn_getContent(r.registrantCapcontentID,@languageID) as registrantCapContent
where r.eventID = @eventID
and r.status <> 'D'

RETURN 0
GO
ALTER PROC [dbo].[ev_getRegistrationMetaByRegistrationID]
@registrationID int

AS

select r.registrationid, r.registrationtypeID, rt.registrationtype, r.startdate, r.enddate, 
	r.registrantCap, r.notifyEmail, r.replyToEmail, r.status, r.isPriceBasedOnActual, r.bulkCountByRate,
	r.expirationcontentID as expireContentID, expireContent.contentTitle as expireContentTitle, 
	expireContent.rawContent as expireContent,
	r.registrantCapcontentID as registrantCapContentID, 
	registrantCapContent.contentTitle as registrantCapContentTitle, 
	registrantCapContent.rawContent as registrantCapContent,
	dbo.fn_getRegCapReached(r.eventID) as regCapReached,
	r.freeRateDisplay
from dbo.ev_registration as r
inner join dbo.ev_registrationTypes as rt on rt.registrationTypeID = r.registrationTypeID
cross apply dbo.fn_getContent(r.expirationcontentID,1) as expireContent
cross apply dbo.fn_getContent(r.registrantCapcontentID,1) as registrantCapContent
where r.registrationid = @registrationID
and r.status <> 'D'

RETURN 0
GO

ALTER PROC [dbo].[ev_getRatesByRegistrationIDForAdmin]
@registrationID int,
@memberid int,
@showall bit

AS

DECLARE @isPriceBasedOnActual bit
SELECT @isPriceBasedOnActual = isPriceBasedOnActual from dbo.ev_registration where registrationID = @registrationID

declare @siteID int, @freeRateDisplay varchar(10)
select @siteID = e.siteid, @freeRateDisplay = r.freeRateDisplay
	from dbo.ev_events as e
	inner join dbo.ev_registration as r on r.eventid = e.eventid
	where registrationID = @registrationID

declare @FID int
select @FID = dbo.fn_getResourceFunctionID('qualify',dbo.fn_getResourceTypeID('EventRate'))

declare @tblRates TABLE (rateid int, rateGroupingOrder int, 
	rateGrouping varchar(200), GLAccountID int, 
	rateName varchar(100), rate money, startDate datetime, endDate datetime, rateOrder int,
	bulkRateID int, bulkQty int, bulkRate money, freeRateDisplay varchar(10))

-- if basing rate on actual groups, look up rates tied to member's groups
IF @isPriceBasedOnActual = 1 BEGIN
	insert into @tblRates (rateid, rateGroupingOrder, rateGrouping, 
		GLAccountID, rateName, rate, startDate, endDate, rateOrder,
		bulkRateID, bulkQty, bulkRate, freeRateDisplay)
	select r.rateid, isnull(rging.rateGroupingOrder,0) as rateGroupingOrder, rging.rateGrouping, 
		r.GLAccountID, r.rateName, r.rate, r.startDate, r.endDate, r.rateOrder, 
		rK.rateID as bulkRateID, rk.bulkQty as bulkQty, rk.rate as bulkRate, @freeRateDisplay
	from dbo.ev_rates as r
	inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
	inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
		and srs.siteResourceStatusDesc = 'Active'
	left outer join dbo.ev_rateGrouping as rging on rging.rateGroupingID = r.rateGroupingID
	left outer join dbo.ev_rates as rK
		on rK.parentRateID = r.rateID
	where r.registrationID = @registrationID
	and r.rate >= 0
	and r.parentRateID is null
	and dbo.fn_checkResourceRights(r.siteResourceID,@FID,@memberid,@siteid) = 1
	order by rging.rateGroupingOrder, r.rateOrder

	-- active rates
	select *
	FROM @tblRates
	where getdate() between startDate and endDate
	order by rateGroupingOrder, rateOrder
	
	-- inactive rates
	select *
	FROM @tblRates
	where getdate() not between startDate and endDate
	order by rateGroupingOrder, rateOrder

	-- all other rates
	select r.rateid, isnull(rging.rateGroupingOrder,0) as rateGroupingOrder, rging.rateGrouping, 
		r.GLAccountID, r.rateName, r.rate, r.startDate, r.endDate, r.rateOrder, 
		rK.rateID as bulkRateID, rk.bulkQty as bulkQty, rk.rate as bulkRate, @freeRateDisplay as freeRateDisplay
	from dbo.ev_rates as r
	inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
	inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
		and srs.siteResourceStatusDesc = 'Active'
	left outer join dbo.ev_rateGrouping as rging on rging.rateGroupingID = r.rateGroupingID
	left outer join dbo.ev_rates as rK on rK.parentRateID = r.rateID
	left outer join @tblRates as tbl on tbl.rateID = r.rateID
	where r.registrationID = @registrationID
	and r.rate >= 0
	and r.parentRateID is null
	and tbl.rate is null
	order by rging.rateGroupingOrder, r.rateOrder

	END
-- else get all rates
ELSE BEGIN
	insert into @tblRates (rateid, rateGroupingOrder, rateGrouping, 
		GLAccountID, rateName, rate, startDate, endDate, rateOrder,
		bulkRateID, bulkQty, bulkRate, freeRateDisplay)
	select r.rateid, isnull(rging.rateGroupingOrder,0) as rateGroupingOrder, rging.rateGrouping, 
		r.GLAccountID, r.rateName, r.rate, r.startDate, r.endDate, r.rateOrder, 
		rK.rateID as bulkRateID, rk.bulkQty as bulkQty, rk.rate as bulkRate, @freeRateDisplay
	from dbo.ev_rates as r
	inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
	inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
		and srs.siteResourceStatusDesc = 'Active'
	left outer join dbo.ev_rateGrouping as rging on rging.rateGroupingID = r.rateGroupingID
	left outer join dbo.ev_rates as rK
		on rK.parentRateID = r.rateID
	where r.registrationID = @registrationID
	and r.rate >= 0
	and r.parentRateID is null
	order by rging.rateGroupingOrder, r.rateOrder

	-- active rates
	select *
	FROM @tblRates
	where getdate() between startDate and endDate
	order by rateGroupingOrder, rateOrder
	
	-- inactive rates
	select *
	FROM @tblRates
	where getdate() not between startDate and endDate
	order by rateGroupingOrder, rateOrder

	-- all other rates (none in this case)
	select *
	FROM @tblRates
	where 1 = 0

	END

RETURN 0
GO

ALTER PROC [dbo].[ev_getRatesByRegistrationID]
@registrationID int,
@memberid int,
@showall bit

AS

DECLARE @isPriceBasedOnActual bit
SELECT @isPriceBasedOnActual = isPriceBasedOnActual from dbo.ev_registration where registrationID = @registrationID

-- if showall override, set basedonActual to 0
if @showall = 1
	select @isPriceBasedOnActual = 0

declare @siteID int, @freeRateDisplay varchar(10)
select @siteID = e.siteid, @freeRateDisplay = r.freeRateDisplay
	from dbo.ev_events as e
	inner join dbo.ev_registration as r on r.eventid = e.eventid
	where registrationID = @registrationID

declare @FID int
select @FID = dbo.fn_getResourceFunctionID('qualify',dbo.fn_getResourceTypeID('EventRate'))

IF @isPriceBasedOnActual = 1
	select r.rateid, isnull(rging.rateGroupingOrder,0) as rateGroupingOrder, rging.rateGrouping, 
		r.GLAccountID, r.rateName, r.rate, r.startDate, r.endDate, r.rateOrder, 
		rK.rateID as bulkRateID, rk.bulkQty as bulkQty, rk.rate as bulkRate, @freeRateDisplay as freeRateDisplay
	from dbo.ev_rates as r
	inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
	inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
		and srs.siteResourceStatusDesc = 'Active'
	left outer join dbo.ev_rateGrouping as rging on rging.rateGroupingID = r.rateGroupingID
	left outer join dbo.ev_rates as rK on rK.parentRateID = r.rateID
	where r.registrationID = @registrationID
	and getdate() between r.startDate and r.endDate
	and r.rate >= 0
	and r.parentRateID is null
	and dbo.fn_checkResourceRights(r.siteResourceID,@FID,@memberid,@siteid) = 1
	order by rging.rateGroupingOrder, r.rateOrder

-- else get all rates
ELSE
	select r.rateid, isnull(rging.rateGroupingOrder,0) as rateGroupingOrder, rging.rateGrouping, 
		r.GLAccountID, r.rateName, r.rate, r.startDate, r.endDate, r.rateOrder, 
		rK.rateID as bulkRateID, rk.bulkQty as bulkQty, rk.rate as bulkRate, @freeRateDisplay as freeRateDisplay
	from dbo.ev_rates as r
	inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
	inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
		and srs.siteResourceStatusDesc = 'Active'
	left outer join dbo.ev_rateGrouping as rging on rging.rateGroupingID = r.rateGroupingID
	left outer join dbo.ev_rates as rK on rK.parentRateID = r.rateID
	where r.registrationID = @registrationID
	and getdate() between r.startDate and r.endDate
	and r.rate >= 0
	and r.parentRateID is null
	order by rging.rateGroupingOrder, r.rateOrder

RETURN 0
GO

