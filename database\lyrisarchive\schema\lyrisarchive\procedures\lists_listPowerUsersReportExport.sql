ALTER PROC dbo.lists_listPowerUsersReportExport
@orgcode varchar(10),
@startYear varchar(4),
@includeLists varchar(max),
@filename varchar(800)

AS

IF OBJECT_ID('tempdb..##tmpListPowerUsersExport') IS NOT NULL 
	DROP TABLE ##tmpListPowerUsersExport

declare @optionalIncludeListFilter TABLE ( listname varchar(100));
insert into @optionalIncludeListFilter (listname)
select listitem from dbo.fn_varcharListToTable(@includeLists,',') where listitem <> ''

declare @listnames varchar(max)
SELECT @listnames = COALESCE(@listnames + ',','') + '''' + listname + '''' FROM @optionalIncludeListFilter

declare @yearlist varchar(500)
; with yearsCTE as (
	select cast(@startYear as int) as theYear
		union all
	select yearsCTE.theYear + 1 as theYear
	from yearsCTE
	where yearsCTE.theYear < year(getdate())
)
SELECT @yearlist = COALESCE(@yearlist + ',','') + quoteName(CAST(theYear AS varchar(4)))
FROM yearsCTE

declare @sql varchar(8000)
set @sql = '
	select list, hdrfromspc_, fullname_, ' + @yearlist + '
	into ##tmpListPowerUsersExport
	from 
		(
			select ml.list, m.hdrfromspc_,mem.fullname_, year(creatStamp_) as year, count(*) as messageCount
			from dbo.messages_ m 
			inner join dbo.messageLists ml on m.listID = ml.listID  and m.isVisible=1'
if @listnames is not null
	set @sql = @sql + 'and ml.list in (' + @listnames + ') '
set @sql = @sql + '
			inner join lyris.trialslyris1.dbo.lists_format lf on ml.list = lf.name COLLATE Latin1_General_CI_AI 
				and lf.orgcode = ''' + @orgcode + '''
			left outer join lyris.trialslyris1.dbo.members_ mem
				on mem.emailaddr_ = m.hdrfromspc_ COLLATE Latin1_General_CI_AI
				and mem.list_ = lf.name COLLATE Latin1_General_CI_AI
			group by ml.list, m.hdrfromspc_, mem.fullname_, year(creatStamp_)
		) as rawdata
	PIVOT (sum(messageCount) for year in (' + @yearlist + ')) as pivottable'
exec(@sql)

EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql='select * from ##tmpListPowerUsersExport order by 1'

IF OBJECT_ID('tempdb..##tmpListPowerUsersExport') IS NOT NULL 
	DROP TABLE ##tmpListPowerUsersExport
GO
