USE membercentral
GO
CREATE FUNCTION dbo.fn_sub_getDuplicateSubHistoryDates ()
RETURNS TABLE 
AS
RETURN 
(
	select subscriberID, updateDate
	from sub_statusHistory
	group by subscriberID, updateDate
	having count(*) > 1
)
GO


CREATE PROC dbo.sub_cleanupDuplicateSubHistoryDates
AS

IF OBJECT_ID('tempdb..#tmpDupeSubs') IS NOT NULL
	DROP TABLE #tmpDupeSubs
IF OBJECT_ID('tempdb..#tmpDupeSubs2') IS NOT NULL
	DROP TABLE #tmpDupeSubs2

-- FIX SUB_STATUSHISTORY ENTRIES THAT MATCH ON DATETIME
CREATE TABLE #tmpDupeSubs (subscriberID int, updateDate datetime)
insert into #tmpDupeSubs
select subscriberID, updateDate
from dbo.fn_sub_getDuplicateSubHistoryDates()

CREATE TABLE #tmpDupeSubs2 (subscriberID int, updateDate datetime, statusHistoryID int, row int)
insert into #tmpDupeSubs2
select tmp.subscriberID, tmp.updateDate, sh.statusHistoryID, ROW_NUMBER() OVER (PARTITION BY tmp.subscriberID ORDER BY sh.statusHistoryID desc) as row
from #tmpDupeSubs as tmp
inner join sub_statusHistory as sh on sh.subscriberID = tmp.subscriberID and sh.updateDate = tmp.updateDate

update sh
set sh.updateDate = dateAdd(ss, tmp.row*-1, sh.updateDate)
from sub_statusHistory as sh
inner join #tmpDupeSubs2 as tmp on tmp.statusHistoryID = sh.statusHistoryID
where tmp.row > 1

IF OBJECT_ID('tempdb..#tmpDupeSubs') IS NOT NULL
	DROP TABLE #tmpDupeSubs
IF OBJECT_ID('tempdb..#tmpDupeSubs2') IS NOT NULL
	DROP TABLE #tmpDupeSubs2
GO

ALTER PROC [dbo].[job_runDailyMaintenanceJobs]
AS

DECLARE @tier varchar(20), @errorSubjectRoot varchar(100), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '10.36.18.90'
IF @@SERVERNAME = 'DEV04\PLATFORM2008' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'DEV03\PLATFORM2008' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'STAGING01\PLATFORM2008' BEGIN
	SET @tier = 'STAGING'
	SET @smtpserver = 'mail.trialsmith.com'
END
SET @errorSubjectRoot = @tier + ' - Developer Needed - '


/* Recalc Date Based Virtual Group Rules */
BEGIN TRY
	EXEC dbo.ams_recalcVirtualGroupsBasedOnDateConditions
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Recalculating Date Based Conditions'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup resetPasswordRequests */
BEGIN TRY
	delete from dbo.ams_resetPasswordRequest
	where dateentered < dateadd(day,-30,getdate())
	and expire = 1

	delete from dbo.ams_resetPasswordRequest
	where dateentered < dateadd(day,-30,getdate())
	and expire = 0 
	and hasBeenUsed = 1
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of resetPasswordRequests'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Viewed Member Times */
BEGIN TRY
	delete tmp
	from dbo.ams_viewedMemberTimes as tmp
	inner join (
		select adminMemberViewedID, memRow
		from (
			select adminMemberViewedID, ROW_NUMBER() OVER (PARTITION BY adminID, viewedOrgID order by lastViewedDateTime desc) as memRow
			from dbo.ams_viewedMemberTimes
		) as innerTMP
		where memRow > 20
	) as t2 on t2.adminMemberViewedID = tmp.adminMemberViewedID
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of viewedMemberTimes'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Orphaned Member Data Content */
BEGIN TRY
	EXEC dbo.ams_cleanupOrphanedMemberDataContent
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of member data content'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Orphaned Network Profiles */
BEGIN TRY
	EXEC dbo.ams_deleteOrphanedNetworkProfiles
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of orphaned network profiles'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Orphaned Virtual Group Conditions */
BEGIN TRY
	EXEC dbo.ams_cleanupOrphanedVirtualGroupConditions
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of orphaned virtual group conditions'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Search Site Resource Cache entries */
BEGIN TRY
	delete from search.dbo.tblSearchSiteResourceCache
	where dateCreated < dateadd(day,-1,getdate())
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of Search Site Resource Cache'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup BuyNow log entries */
BEGIN TRY
	delete from dbo.buyNow_Log
	where insertDate < dateadd(day,-7,getdate())
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of BuyNow log entries'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Auto Post System Batches */
BEGIN TRY
	EXEC dbo.tr_autoPostSystemBatches
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Auto Posting System Batches'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Mark paid closed invoices as paid */
BEGIN TRY
	EXEC dbo.tr_autoMarkClosedInvoicesAsPaid
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Marking paid closed invoices as paid'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Close Pending Invoices */
BEGIN TRY
	EXEC dbo.sub_closePendingInvoices
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Closing Pending Invoices'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Close empty open invoices */
BEGIN TRY
	EXEC dbo.tr_autoCloseEmptyOpenInvoices
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Closing Empty Open Invoices'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Advance Subscription Rates */
BEGIN TRY
	EXEC dbo.sub_advanceRates
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Advancing Subscription Rates'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Expire Subscription Offers */
BEGIN TRY
	EXEC dbo.sub_expireOffers
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Expiring Subscription Offers'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Expire Subscriptions */
BEGIN TRY
	EXEC dbo.sub_expireSubscriptions
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Expiring Subscriptions'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Dupe Sub History Dates */
BEGIN TRY
	EXEC dbo.sub_cleanupDuplicateSubHistoryDates
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleaning Up Duplicate Sub History Dates'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Add autoPay Invoices to Invoice Payment Queue */
BEGIN TRY
	EXEC dbo.tr_autoPayInvoices
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Adding autoPay Invoices to Invoice Payment Queue'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Add items to LRIS Client Surveys Queue */
IF @tier = 'PRODUCTION' BEGIN
	BEGIN TRY
		declare @referralID int
		set @referralID = 1
		EXEC dbo.ref_addClientSurveyQueue @referralID = @referralID
	END TRY
	BEGIN CATCH
		SET @errorSubject = @errorSubjectRoot + 'Error Adding items to Client Surveys Queue'

		SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
		SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END CATCH
END

/* Cleanup tblQueueItems_processMemberGroups */
BEGIN TRY
	delete from platformQueue.dbo.tblQueueItems_processMemberGroups
	where dateadded < dateadd(day,-1,getdate())
	and itemUID not in (select itemUID from platformQueue.dbo.tblQueueItems)
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of tblQueueItems_processMemberGroups'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* Add missing ad zones or update ad zone names */
BEGIN TRY
	EXEC dbo.ad_populateAdZones
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Populating Ad Zones'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* Admin Count checks - Run on first of month only */
IF datepart(d,getdate()) = 1 BEGIN
	BEGIN TRY
		EXEC dbo.ams_adminCountReport
	END TRY
	BEGIN CATCH
		SET @errorSubject = @errorSubjectRoot + 'Error Running Admin Count Report'

		SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
		SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END CATCH
END

RETURN 0
GO

