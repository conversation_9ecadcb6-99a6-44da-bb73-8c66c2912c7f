-- add custom page
declare @siteID int, @sectionID int, @zoneID int, @pgResourceTypeID int, @applicationInstanceID int, @siteResourceID int, 
	@pageID int, @resourceRightID int
		      
select @siteID = siteID from sites where siteCode = 'WA'
select @sectionID = sectionID from cms_pageSections where siteID = @siteID and sectionName = 'Root' and sectionCode = 'Root'
SELECT @zoneID = dbo.fn_getZoneID('Main')
SELECT @pgResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedPage')

EXEC dbo.cms_createApplicationInstance @siteid=@siteID, @languageID=1, @sectionID=@sectionID, @applicationTypeID=18,
	@isVisible=1, @pageName='myWSAJ', @pageTitle='My WSAJ', @pagedesc='My WSAJ', @zoneID=@zoneID, @pageTemplateID=null,
	@pageModeID=null, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1,
	@applicationInstanceName='myWSAJ', @applicationInstanceDesc='myWSAJ', @applicationInstanceID=@applicationInstanceID OUTPUT,
	@siteResourceID=@siteResourceID OUTPUT, @pageID=@pageID OUTPUT
								
INSERT INTO cms_customPages(appInstanceID, customFileName)
VALUES(@applicationInstanceID,'myWSAJ')

EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1, @functionID=4, @roleID=null, 
	@groupID=18208, @memberID=null, @inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, @resourceRightID=@resourceRightID OUTPUT
GO

