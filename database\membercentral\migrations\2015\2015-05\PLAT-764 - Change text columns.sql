/* TLASITES INSTANCE */

use seminarWeb
GO

ALTER TABLE dbo.tblSeminarsAndForms ALTER COLUMN certifiedStatement varchar(max) NULL;
GO


use trialsmith
GO

ALTER TABLE dbo.CaseRetriever_cases ALTER COLUMN htmlbody varchar(max) NULL;
ALTER TABLE dbo.CustomerNotes ALTER COLUMN Note varchar(max) NOT NULL;
ALTER TABLE dbo.Defaults ALTER COLUMN DefaultDays varchar(max) NULL;
ALTER TABLE dbo.Defaults ALTER COLUMN DefaultMonths varchar(max) NULL;
ALTER TABLE dbo.depoDocuments ALTER COLUMN reviewComments varchar(max) NULL;
ALTER TABLE dbo.depoTLA ALTER COLUMN importMessage varchar(max) NULL;
ALTER TABLE dbo.eClipsPublicationHeadlines ALTER COLUMN abstract varchar(max) NULL;
ALTER TABLE dbo.eclipsSources ALTER COLUMN LicenseLanguage varchar(max) NULL;
ALTER TABLE dbo.eclipsSources ALTER COLUMN notes varchar(max) NULL;
ALTER TABLE dbo.eClipsSponsors ALTER COLUMN newsfooter varchar(max) NULL;
ALTER TABLE dbo.EventRegistration ALTER COLUMN CLEInfo varchar(max) NULL;
ALTER TABLE dbo.EventRegistration ALTER COLUMN FacilityDetails varchar(max) NULL;
ALTER TABLE dbo.EventRegistrationProcess ALTER COLUMN joinWDDX varchar(max) NOT NULL;
ALTER TABLE dbo.Events ALTER COLUMN Details varchar(max) NULL;
ALTER TABLE dbo.Events ALTER COLUMN LocationNotes varchar(max) NULL;
ALTER TABLE dbo.forumThreads ALTER COLUMN threadBody varchar(max) NULL;
ALTER TABLE dbo.joinProcess ALTER COLUMN joinWDDX varchar(max) NOT NULL;
ALTER TABLE dbo.orgMemberConfig ALTER COLUMN importMessage varchar(max) NULL;
ALTER TABLE dbo.StoreCheckoutHeader ALTER COLUMN CheckoutHeader varchar(max) NULL;
ALTER TABLE dbo.StoreCompanyInfo ALTER COLUMN AboutPage varchar(max) NULL;
ALTER TABLE dbo.StoreCompanyInfo ALTER COLUMN CatalogPath varchar(max) NULL;
ALTER TABLE dbo.StoreCompanyInfo ALTER COLUMN EmailText varchar(max) NULL;
ALTER TABLE dbo.StoreCompanyInfo ALTER COLUMN EndOrderMessage varchar(max) NULL;
ALTER TABLE dbo.StoreCompanyInfo ALTER COLUMN HomeURL varchar(max) NULL;
ALTER TABLE dbo.StoreCompanyInfo ALTER COLUMN MailServer varchar(max) NULL;
ALTER TABLE dbo.StoreCompanyInfo ALTER COLUMN PhotoDirectory varchar(max) NULL;
ALTER TABLE dbo.StoreCompanyInfo ALTER COLUMN PhotoPath varchar(max) NULL;
ALTER TABLE dbo.StoreCompanyInfo ALTER COLUMN SecurePath varchar(max) NULL;
ALTER TABLE dbo.StoreCompanyInfo ALTER COLUMN SecureURL varchar(max) NULL;
ALTER TABLE dbo.StoreCustomerHistory ALTER COLUMN Address varchar(max) NULL;
ALTER TABLE dbo.StoreCustomerHistory ALTER COLUMN Address2 varchar(max) NULL;
ALTER TABLE dbo.StoreCustomerHistory ALTER COLUMN Country varchar(max) NULL;
ALTER TABLE dbo.StoreCustomerHistory ALTER COLUMN Memo varchar(max) NULL;
ALTER TABLE dbo.StoreCustomerHistory ALTER COLUMN ShipAddress varchar(max) NULL;
ALTER TABLE dbo.StoreCustomerHistory ALTER COLUMN ShipAddress2 varchar(max) NULL;
ALTER TABLE dbo.StoreHeader ALTER COLUMN StoreHeader varchar(max) NULL;
ALTER TABLE dbo.StoreHomePage ALTER COLUMN HomePage varchar(max) NULL;
ALTER TABLE dbo.StoreOrders ALTER COLUMN ccresponse varchar(max) NULL;
ALTER TABLE dbo.StoreOrders ALTER COLUMN CrtItemID varchar(max) NULL;
ALTER TABLE dbo.StoreOrders ALTER COLUMN CrtPrice varchar(max) NULL;
ALTER TABLE dbo.StoreOrders ALTER COLUMN CrtProductID varchar(max) NULL;
ALTER TABLE dbo.StoreOrders ALTER COLUMN CrtProductName varchar(max) NULL;
ALTER TABLE dbo.StoreOrders ALTER COLUMN CrtQuantity varchar(max) NULL;
ALTER TABLE dbo.StoreOrders ALTER COLUMN Memo varchar(max) NULL;
ALTER TABLE dbo.StoreOrders ALTER COLUMN Notes varchar(max) NULL;
ALTER TABLE dbo.StoreOrders ALTER COLUMN ShippedTo varchar(max) NULL;
ALTER TABLE dbo.StoreOrders ALTER COLUMN TrackingNumber varchar(max) NULL;
ALTER TABLE dbo.StoreProducts ALTER COLUMN ImageURL varchar(max) NULL;
ALTER TABLE dbo.StoreProducts ALTER COLUMN Thumbnail varchar(max) NULL;
ALTER TABLE dbo.StoreSellingAreas ALTER COLUMN Countries varchar(max) NULL;
ALTER TABLE dbo.StoreSellingAreas ALTER COLUMN CountryCodes varchar(max) NULL;
ALTER TABLE dbo.StoreSellingAreas ALTER COLUMN SelectedCCodes varchar(max) NULL;
ALTER TABLE dbo.StoreSellingAreas ALTER COLUMN SelectedCountries varchar(max) NULL;
ALTER TABLE dbo.StoreSellingAreas ALTER COLUMN SelectedSCodes varchar(max) NULL;
ALTER TABLE dbo.StoreSellingAreas ALTER COLUMN SelectedStates varchar(max) NULL;
ALTER TABLE dbo.StoreSellingAreas ALTER COLUMN StateCodes varchar(max) NULL;
ALTER TABLE dbo.StoreSellingAreas ALTER COLUMN States varchar(max) NULL;
ALTER TABLE dbo.TLATransactions ALTER COLUMN ccResponseText varchar(max) NULL;
DROP TABLE dbo.txdirectory_bios;
DROP TABLE dbo.txdirectory_boardadditional;
DROP TABLE dbo.txdirectory_members;
DROP TABLE dbo.txdirectory_photoyear;
ALTER TABLE dbo.usergroupmembers ALTER COLUMN notes varchar(max) NULL;
GO

ALTER FULLTEXT INDEX ON [dbo].[StoreProducts] DROP ([BriefDescription]);
ALTER FULLTEXT INDEX ON [dbo].[StoreProducts] DROP ([Details]);
GO
ALTER TABLE dbo.StoreProducts ALTER COLUMN BriefDescription varchar(max) NULL;
ALTER TABLE dbo.StoreProducts ALTER COLUMN Details varchar(max) NULL;
GO
ALTER FULLTEXT INDEX ON [dbo].[StoreProducts] ADD ([BriefDescription]);
ALTER FULLTEXT INDEX ON [dbo].[StoreProducts] ADD ([Details]);
GO

ALTER FULLTEXT INDEX ON [dbo].[experts] DROP ([Summary]);
ALTER FULLTEXT INDEX ON [dbo].[experts] DROP ([Terms]);
GO
ALTER TABLE dbo.experts ALTER COLUMN Summary varchar(max) NULL;
ALTER TABLE dbo.experts ALTER COLUMN Terms varchar(max) NULL;
GO
ALTER FULLTEXT INDEX ON [dbo].[experts] ADD ([Summary]);
ALTER FULLTEXT INDEX ON [dbo].[experts] ADD ([Terms]);
GO

ALTER FULLTEXT INDEX ON [dbo].[EventRegistration] DROP ([Description]);
GO
ALTER TABLE dbo.EventRegistration ALTER COLUMN Description varchar(max) NULL;
GO
ALTER FULLTEXT INDEX ON [dbo].[EventRegistration] ADD ([Description]);
GO

ALTER FULLTEXT INDEX ON [dbo].[depoDocuments] DROP ([Notes]);
GO
ALTER TABLE dbo.depoDocuments ALTER COLUMN Notes varchar(max) NULL;
GO
ALTER FULLTEXT INDEX ON [dbo].[depoDocuments] ADD ([Notes]);
GO

