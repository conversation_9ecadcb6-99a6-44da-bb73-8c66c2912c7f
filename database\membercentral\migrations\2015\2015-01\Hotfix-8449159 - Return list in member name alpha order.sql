-- Hotfix-8449159 - Return list in alpha order
use membercentral
GO 

ALTER PROC [dbo].[email_previewEmailBlastSummary]
@blastID int,
@mode tinyint

AS

declare @ruleID int, @xmlMembers xml
declare @tblM TABLE (memberid int PRIMARY KEY, hasEmail bit)

select @ruleID = ruleID from dbo.email_EmailBlasts where blastID = @blastID

EXEC dbo.ams_RunVirtualGroupRule @ruleID=@ruleID, @xmlVirtual=@xmlMembers OUTPUT

insert into @tblM (memberid, hasEmail)
SELECT m2.memberid, case when len(me.email) > 0 then 1 else 0 end
FROM @xmlMembers.nodes('//m') AS M(item)
INNER JOIN dbo.ams_members as m2 WITH(NOLOCK) on m2.memberid = M.item.value('@id', 'int')
LEFT OUTER JOIN dbo.ams_memberEmails as me WITH(NOLOCK) 
	INNER JOIN dbo.ams_memberEmailTypes as met WITH(NOLOCK) on met.emailTypeID = me.emailTypeID and met.emailTypeOrder = 1
	on me.memberid = m2.memberid 

-- summary counts
IF @mode = 0 BEGIN
	select 1 as row, 'Members matching filter criteria' as Members, count(distinct memberid) as MemberCount
	from @tblM
		union all
	select 2 as row, 'Members with no e-mail address' as Members, count(distinct memberid) as MemberCount
	from @tblM
	where hasEmail = 0
		union all
	select 3 as row, 'Total messages to be sent' as Members, count(distinct memberid) as MemberCount
	from @tblM
	where hasEmail = 1
	order by row
END

-- row 1 detail
IF @mode = 1 BEGIN
	select m.lastname + ', ' + m.firstname + ' ' + m.middlename + ' (' + m.membernumber + ')' as memberName, 
		m.company as memberCompany,
		memberEmail = case when len(me.email) > 0 then me.email else '<i>(no e-mail address)</i>' end
	from @tblM as tblM
	inner join dbo.ams_members as m on m.memberid = tblM.memberid
	LEFT OUTER JOIN dbo.ams_memberEmails as me WITH(NOLOCK) 
		INNER JOIN dbo.ams_memberEmailTypes as met WITH(NOLOCK) on met.emailTypeID = me.emailTypeID and met.emailTypeOrder = 1
		on me.memberid = m.memberid 
	order by 1
END

-- row 2 detail
IF @mode = 2 BEGIN
	select m.lastname + ', ' + m.firstname + ' ' + m.middlename + ' (' + m.membernumber + ')' as memberName, 
		m.company as memberCompany,
		memberEmail = '<i>(no e-mail address)</i>'
	from @tblM as tblM
	inner join dbo.ams_members as m on m.memberid = tblM.memberid
	where tblM.hasEmail = 0
	order by 1
END

-- row 3 detail 
IF @mode = 3 BEGIN
	select m.memberid, 
		m.lastname + ', ' + m.firstname + ' ' + m.middlename + ' (' + m.membernumber + ')' as memberName, 
		m.company as memberCompany,
		memberEmail = me.email
	from @tblM as tblM
	inner join dbo.ams_members as m on m.memberid = tblM.memberid
	INNER JOIN dbo.ams_memberEmails as me WITH(NOLOCK) on me.memberid = m.memberid 
	INNER JOIN dbo.ams_memberEmailTypes as met WITH(NOLOCK) on met.emailTypeID = me.emailTypeID and met.emailTypeOrder = 1
	where tblM.hasEmail = 1
	order by 2
END

RETURN 0
GO
