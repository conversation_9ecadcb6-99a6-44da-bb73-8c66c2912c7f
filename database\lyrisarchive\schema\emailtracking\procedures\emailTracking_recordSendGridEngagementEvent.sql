ALTER PROC dbo.emailTracking_recordSendGridEngagementEvent
@messagetype varchar(50),
@applicationtype varchar(20),
@applicationname varchar(100),
@periodCode int,
@event varchar(50),
@sgmessageid varchar(100),
@sgeventid varchar(100),
@timestamp datetime,
@membernumber varchar(100),
@sendingapplicationid int,
@sendingapplicationmessageid int,
@sendingapplicationrecipientid int,
@siteid int,
@emailusername varchar(100),
@emailDomainID int,
@emaildomain varchar(250),
@useragent varchar(500),
@useragentid int,
@useragentSHA1 varchar(40),
@ipaddress varchar(50),
@ismachineopen bit,
@url varchar(1000),
@urlDomain varchar(500)

AS

SET XACT_ABORT, NOCOUNT ON;

declare @eventadded bit = 0;


IF NULLIF(@sendingapplicationid,0) IS NULL
	EXEC dbo.emailTracking_createApplicationID @applicationName=@applicationName, @applicationType=@applicationType, @sendingApplicationID=@sendingapplicationid OUTPUT;

IF NULLIF(@emailDomainID,0) IS NULL
	EXEC dbo.emailTracking_createDomainID @domain=@emaildomain, @domainID=@emailDomainID OUTPUT;

IF NULLIF(@useragentid,0) IS NULL
	EXEC dbo.emailTracking_createUseragentID @userAgentString=@useragent, @useragentSHA1=@useragentSHA1, @useragentid=@useragentid OUTPUT;

IF @event = 'open' BEGIN
	IF NOT EXISTS (
		select 1
		from dbo.recipientOpens
		where siteID = @siteID 
		and sendingApplicationID = @sendingapplicationid 
		and sendingApplicationMessageID = @sendingapplicationmessageid 
		and sg_event_id = @sgeventid
	) BEGIN
		SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

		insert into dbo.recipientOpens (sg_event_id, sg_message_id, memberNumber, sendingApplicationID, sendingApplicationMessageID,
			siteID, username, domainID, periodCode, dateReceived, useragentID, ipaddress, isMachineOpen, hasBeenAggregated)
		select @sgeventid, @sgmessageid, @membernumber, @sendingApplicationID, @sendingApplicationMessageID, @siteID, @emailusername,
			@emailDomainID, @periodCode, @timestamp, @useragentID, @ipaddress, @isMachineOpen, 0
		WHERE NOT EXISTS (
			select 1
			from dbo.recipientOpens
			where siteID = @siteID 
			and sendingApplicationID = @sendingapplicationid 
			and sendingApplicationMessageID = @sendingapplicationmessageid 
			and sg_event_id = @sgeventid
		);

		IF @@ROWCOUNT > 0
			set @eventadded = 1;

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		IF @eventadded = 1
			update dbo.recipients 
			set firstOpened = @timestamp
			where siteID = @siteID 
			and sendingApplicationID = @sendingapplicationid 
			and sendingApplicationMessageID = @sendingapplicationmessageid 
			and sg_message_id = @sgmessageid
			and (firstOpened IS NULL or firstOpened > @timestamp);
	END
END

IF @event = 'click' BEGIN
	IF NOT EXISTS (
		select 1
		from dbo.recipientClicks
		where siteID = @siteID 
		and sendingApplicationID = @sendingapplicationid 
		and sendingApplicationMessageID = @sendingapplicationmessageid 
		and sg_event_id = @sgeventid
	) BEGIN
		SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

		insert into dbo.recipientClicks (sg_event_id, sg_message_id, memberNumber, sendingApplicationID, sendingApplicationMessageID,
			siteID, username, domainID, periodCode, dateReceived, useragentID, ipaddress, clickDomain, clickURL, hasBeenAggregated)
		select @sgeventid, @sgmessageid, @membernumber, @sendingApplicationID, @sendingApplicationMessageID, @siteID, @emailusername,
			@emailDomainID, @periodCode, @timestamp, @useragentID, @ipaddress, @urlDomain, @url, 0
		WHERE NOT EXISTS (
			select 1
			from dbo.recipientClicks
			where siteID = @siteID 
			and sendingApplicationID = @sendingapplicationid 
			and sendingApplicationMessageID = @sendingapplicationmessageid 
			and sg_event_id = @sgeventid
		);

		IF @@ROWCOUNT > 0
			set @eventadded = 1;

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		IF @eventadded = 1
			update dbo.recipients 
			set firstClicked = @timestamp
			where siteID = @siteID 
			and sendingApplicationID = @sendingapplicationid 
			and sendingApplicationMessageID = @sendingapplicationmessageid 
			and sg_message_id = @sgmessageid
			and (firstClicked IS NULL or firstClicked > @timestamp);
	END
END

IF @event = 'spamreport'
	update dbo.recipients 
	set firstReportedSpam = @timestamp
	where siteID = @siteID 
	and sendingApplicationID = @sendingapplicationid 
	and sendingApplicationMessageID = @sendingapplicationmessageid 
	and sg_message_id = @sgmessageid
	and (firstReportedSpam IS NULL or firstReportedSpam > @timestamp);

RETURN 0;
GO
