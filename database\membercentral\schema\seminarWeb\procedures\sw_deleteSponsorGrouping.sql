ALTER PROC dbo.sw_deleteSponsorGrouping
@sponsorGroupingID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF NOT EXISTS (SELECT sponsorGroupingID FROM dbo.sw_sponsorGrouping WHERE sponsorGroupingID=@sponsorGroupingID)
		RAISERROR('Sponsor Grouping does not exist.',16,1);

	-- Check if any sponsors are using this grouping
	IF EXISTS (SELECT sponsorUsageID FROM memberCentral.dbo.sponsorsUsage WHERE sponsorGroupingID=@sponsorGroupingID)
		RAISERROR('Cannot delete sponsor grouping that has sponsors assigned to it.',16,1);

	DECLARE @seminarID INT, @participantID INT;
	SELECT @seminarID = seminarID, @participantID = participantID FROM dbo.sw_sponsorGrouping WHERE sponsorGroupingID=@sponsorGroupingID;

	DELETE FROM dbo.sw_sponsorGrouping WHERE sponsorGroupingID = @sponsorGroupingID;

	-- Reorder remaining groupings
	EXEC dbo.sw_reorderSponsorGroupings @seminarID=@seminarID, @participantID=@participantID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLL<PERSON>CK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
