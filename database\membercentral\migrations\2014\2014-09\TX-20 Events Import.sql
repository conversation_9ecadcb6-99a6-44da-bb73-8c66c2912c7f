use datatransfer
GO


/* START SET VARIABLES HERE */
DECLARE @sitecode varchar(10), @tmpFile varchar(400), @timeZoneID int, @replyToEmail varchar(100),
	@deleteMissingCalendars bit, @deleteMissingMembers bit,	@deleteMissingCategories bit, @stopBeforeImport bit
set @sitecode = 'TX'
set @tmpFile = 'e:\temp\EventImportAll.txt'
set @timeZoneID = 6
set @replyToEmail = '<EMAIL>'
set @deleteMissingCalendars = 0
set @deleteMissingMembers = 1
set @deleteMissingCategories = 0
set @stopBeforeImport = 1
/* END SET VARIABLES HERE */



SET NOCOUNT ON

declare @orgID int, @siteID int, @errSection varchar(30), @qry varchar(2000), @minID int, @MCEventID int, @MCCalendarID int,
	@MCsystemMemberID int, @MCAllDayEvent bit, @eventCode varchar(15), @starttime datetime, @endtime datetime, @eventContentID int,
	@eventName varchar(400), @expirationContentID int, @maxEventID int, @MCRegistrationID int, @customQID int, @custom1QID int
select @orgID = orgID, @siteID = siteID from membercentral.dbo.sites where sitecode = @siteCode
select @MCsystemMemberID = memberID from membercentral.dbo.ams_members where memberNumber = 'SYSTEM' and orgID = 1


/* ************** */
/* initial checks */
/* ************** */
IF @siteID is null BEGIN
	set @errSection = 'initialchecks'
	GOTO on_error	
END

/* ********************* */
/* import data from file */
/* ********************* */
IF OBJECT_ID('tempdb..##tmpEvImport') IS NOT NULL 
	DROP TABLE ##tmpEvImport
IF OBJECT_ID('tempdb..#tmpEvImport') IS NOT NULL 
	DROP TABLE #tmpEvImport
IF OBJECT_ID('tempdb..#tmpEvImportEV') IS NOT NULL 
	DROP TABLE #tmpEvImportEV

CREATE TABLE ##tmpEvImport (
	[calendarName] [varchar](200) NULL,
	[memberNumber] [varchar](200) NULL,
	[eventCode] [varchar](15) NULL,
	[eventName] [varchar](400) NULL,
	[eventCategory] [varchar](50) NULL,
	[eventStartDate] [datetime] NULL,
	[eventEndDate] [datetime] NULL,
	[eventCreditAuthority] [varchar](200) NULL,
	[eventCreditType] [varchar](100) NULL,
	[eventCreditValue] [decimal](6,2) NULL,
	[registrantDate] [datetime] NULL,
	[registrantAttended] [varchar](3) NULL,
	[registrantCreditValue] [decimal](6,2) NULL,
	[custom1] [varchar](100) NULL
)

BEGIN TRY
	SELECT @qry = 'BULK INSERT ##tmpEvImport FROM ''' + @tmpFile + ''' WITH (FIELDTERMINATOR = ''' + char(9) + ''', FIRSTROW = 2);'
	EXEC(@qry)
END TRY
BEGIN CATCH
	EXEC membercentral.dbo.up_errorhandler
	set @errSection = 'importfromfile'
	GOTO on_error	
END CATCH

select *,
	cast(null as int) as MCCalendarID,
	cast(null as int) as MCMemberID,
	cast(null as int) as MCEventID,
	cast(null as int) as MCCategoryID,
	cast(null as int) as MCRegistrationID,
	cast(0 as bit) as MCAllDayEvent,
	cast(null as int) as custom1QID,
	ROW_NUMBER() OVER(order by calendarName, eventCode, memberNumber) as autoID
into #tmpEvImport 
from ##tmpEvImport

IF OBJECT_ID('tempdb..##tmpEvImport') IS NOT NULL 
	DROP TABLE ##tmpEvImport


/* ******************* */
/* remove extra quotes */
/* ******************* */
update #tmpEvImport
set calendarName = replace(calendarName,char(34),''),
	eventName = replace(eventName,char(34),'')


/* **************************** */
/* try to match by calendarname */
/* **************************** */
BEGIN TRY
	update tmp
	set tmp.MCCalendarID = c.calendarID
	from #tmpEvImport as tmp
	inner join membercentral.dbo.cms_applicationInstances as ai on ai.applicationInstanceName = tmp.calendarName
		and ai.siteID = @siteID
	inner join membercentral.dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID
		and sr.siteResourceStatusID = 1
	inner join membercentral.dbo.ev_calendars as c on c.applicationInstanceID = ai.applicationInstanceID

	IF @deleteMissingCalendars = 1
		delete from #tmpEvImport where MCCalendarID is null

	IF EXISTS (select * from #tmpEvImport where MCCalendarID is null) BEGIN
		select distinct calendarName as CalendarNotFound
		from #tmpEvImport
		where MCCalendarID is null
		order by 1

		RAISERROR('Error raised in TRY block.', 16, 1);
	END
END TRY
BEGIN CATCH
	set @errSection = 'calendarnotfound'
	GOTO on_error
END CATCH


/* **************************** */
/* try to match by membernumber */
/* **************************** */
BEGIN TRY
	update tmp
	set tmp.MCMemberID = m.memberid
	from #tmpEvImport as tmp
	inner join membercentral.dbo.ams_members as m on m.memberNumber = tmp.memberNumber
		and m.orgID = @orgID
		and m.memberID = m.activeMemberID

	IF @deleteMissingMembers = 1
		delete from #tmpEvImport where MCMemberID is null

	IF EXISTS (select * from #tmpEvImport where MCMemberID is null) BEGIN
		select distinct memberNumber as MemberNumberNotFound
		from #tmpEvImport
		where MCMemberID is null
		order by 1

		RAISERROR('Error raised in TRY block.', 16, 1);
	END
END TRY
BEGIN CATCH
	set @errSection = 'membernotfound'
	GOTO on_error
END CATCH


/* ************************* */
/* try to match by eventcode */
/* ************************* */
BEGIN TRY
	update tmp
	set tmp.MCEventID = e.eventID
	from #tmpEvImport as tmp
	inner join membercentral.dbo.ev_events as e on e.reportCode = tmp.eventCode
		and e.status = 'A'
	inner join membercentral.dbo.ev_calendarEvents as ce on ce.sourceEventID = e.eventID
		and ce.calendarID = tmp.MCCalendarID
		and ce.calendarID = ce.sourceCalendarID

	update tmp
	set tmp.MCRegistrationID = r.registrationID
	from #tmpEvImport as tmp
	inner join membercentral.dbo.ev_registration as r on r.eventID = tmp.MCEventID and r.status = 'A'
	where tmp.MCEventID is not null
END TRY
BEGIN CATCH
	set @errSection = 'matchingeventcode'
	GOTO on_error
END CATCH


/* ************************ */
/* try to match by category */
/* ************************ */
BEGIN TRY
	update tmp
	set tmp.MCCategoryID = cat.categoryID
	from #tmpEvImport as tmp
	inner join membercentral.dbo.cms_applicationInstances as ai on ai.applicationInstanceName = tmp.calendarName
		and ai.siteID = @siteID
	inner join membercentral.dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID
		and sr.siteResourceStatusID = 1
	inner join membercentral.dbo.ev_calendars as c on c.applicationInstanceID = ai.applicationInstanceID
	inner join membercentral.dbo.ev_categories as cat on cat.calendarID = c.calendarID
		and cat.category = tmp.eventCategory

	IF @deleteMissingCategories = 1
		delete from #tmpEvImport where MCCategoryID is null

	IF EXISTS (select * from #tmpEvImport where MCCategoryID is null) BEGIN
		select distinct eventCategory as EventCategoryNotFound
		from #tmpEvImport
		where MCCategoryID is null
		order by 1

		RAISERROR('Error raised in TRY block.', 16, 1);
	END
END TRY
BEGIN CATCH
	set @errSection = 'categorynotfound'
	GOTO on_error
END CATCH


/* *********** */
/* check dates */
/* *********** */
IF EXISTS (select * from #tmpEvImport where eventStartDate is null) BEGIN
	select 'Start Date is empty' as errReason, * 
	from #tmpEvImport
	where eventStartDate is null

	set @errSection = 'startempty'
	GOTO on_error
END
IF EXISTS (select * from #tmpEvImport where eventEndDate is null) BEGIN
	select 'End Date is empty' as errReason, * 
	from #tmpEvImport
	where eventEndDate is null

	set @errSection = 'endempty'
	GOTO on_error
END
IF EXISTS (select * from #tmpEvImport where eventStartDate > eventEndDate) BEGIN
	select 'Start Date is after End Date' as errReason, * 
	from #tmpEvImport
	where eventStartDate > eventEndDate

	set @errSection = 'startafterend'
	GOTO on_error
END
IF EXISTS (select * from #tmpEvImport where registrantDate is null) BEGIN
	update #tmpEvImport
	set registrantDate = eventStartDate
	where registrantDate is null
END


/* ***************** */
/* set all day event */
/* ***************** */
update #tmpEvImport
set MCAllDayEvent = 1
where eventStartDate = eventEndDate



/* NEED CHECKS HERE FOR CREDIT AUTHORITIES, TYPES, VALUES */


/* ************** */
/* check attended */
/* ************** */
update #tmpEvImport
set registrantAttended = 'No'
where nullIf(registrantAttended,'') is null

IF EXISTS (select * from #tmpEvImport where registrantAttended not in ('Yes','No')) BEGIN
	select 'Bad valued for Attended' as errReason, * 
	from #tmpEvImport
	where registrantAttended not in ('Yes','No')

	set @errSection = 'badattended'
	GOTO on_error
END


/* ******************* */
/* Get Distinct Events */
/* ******************* */
select distinct calendarName, eventCode, eventName, eventCategory, eventStartDate, eventEndDate, MCCalendarID, MCEventID, MCCategoryID, 
	MCAllDayEvent, MCRegistrationID, custom1QID
into #tmpEvImportEV
from #tmpEvImport

ALTER TABLE #tmpEvImportEV ADD autoID int IDENTITY(1,1);


/* ********** */
/* IF TESTING */
/* ********** */
IF @stopBeforeImport = 1 BEGIN
	select * from #tmpEvImport
	set @errSection = 'testing'
	GOTO on_error
END


/* ************************** */
/* delete all existing events */
/* ************************** */



/* ********** */
/* Add Events */
/* ********** */
EXEC membercentral.dbo.cache_perms_setStatus @orgID, 'disabled'
select @maxEventID = max(eventID) from membercentral.dbo.ev_events

SELECT @minID = min(autoID) FROM #tmpEvImportEV
WHILE @minID is not null BEGIN
	select @MCEventID = null, @MCCalendarID = null, @MCAllDayEvent = null, @eventCode = null, @starttime = null, 
		@endtime = null, @eventName = null, @MCRegistrationID = null, @custom1QID = null

	select @MCEventID = MCEventID, @MCCalendarID = MCCalendarID, @MCAllDayEvent = MCAllDayEvent, @eventCode = eventCode,
		@starttime = eventStartDate, @endtime = eventEndDate, @eventName = eventName, @MCRegistrationID = MCRegistrationID, @custom1QID = custom1QID
		from #tmpEvImportEV
		where autoID = @minID

	IF @MCEventID is null BEGIN
		EXEC membercentral.dbo.ev_createEvent @siteID=@siteID, @calendarid=@MCCalendarID, @eventTypeID=1, @enteredByMemberID=@MCsystemMemberID, @lockTimeZoneID=null, @isAllDayEvent=@MCAllDayEvent, @altRegistrationURL=null, @status='A', @reportCode=@eventCode, @emailContactContent = 0, @emailLocationContent = 0, @emailCancelContent = 0, @emailTravelContent = 0, @eventID=@MCEventID OUTPUT
		EXEC membercentral.dbo.ev_createTime @eventID=@MCEventID, @timeZoneID=@timeZoneID, @startTime=@starttime, @endTime=@endtime
		select @eventContentID = eventContentID FROM membercentral.dbo.ev_events WHERE eventID = @MCEventID
		EXEC membercentral.dbo.cms_updateContent @contentID=@eventContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@eventName, @contentDesc='', @rawcontent=@eventName
	END

	IF @MCRegistrationID is null BEGIN 
		EXEC membercentral.dbo.ev_createRegistration @eventID=@MCEventID, @registrationTypeID=1, @startDate=@starttime, @endDate=@endtime, @registrantCap=null, @replyToEmail=@replyToEmail, @notifyEmail='', @isPriceBasedOnActual=1, @bulkCountByRate=0, @registrationID=@MCRegistrationID OUTPUT
		select @expirationContentID = expirationContentID FROM membercentral.dbo.ev_registration WHERE registrationID = @MCRegistrationID
		EXEC membercentral.dbo.cms_updateContent @contentID=@expirationContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle='Expiration Message', @contentDesc='', @rawcontent='Registration for this event has closed.'

		-- add custom q
		insert into membercentral.dbo.ev_registrationCustom (registrationID, areaID, fieldDesc, titleOnInvoice, customTypeID, isRequired, requiredMSG, fieldOrder, status, offerQty, amount, GLAccountID)
		VALUES (@MCRegistrationID, 2, 'Reg Rate', 'Reg Rate', 1, 0, null, 1, 'A', 0, null, null)
			select @customQID = SCOPE_IDENTITY()
			UPDATE #tmpEvImportEV SET custom1QID = @customQID where autoID = @minID
			UPDATE #tmpEvImport SET custom1QID = @customQID where eventCode = @eventCode

	END

	UPDATE #tmpEvImportEV 
	SET MCEventID = @MCEventID, MCregistrationID = @MCRegistrationID 
	WHERE autoID = @minID

	UPDATE #tmpEvImport
	SET MCEventID = @MCEventID, MCregistrationID = @MCRegistrationID 
	WHERE eventCode = @eventCode

	SELECT @minID = min(autoID) FROM #tmpEvImportEV where autoID > @minID
END


/* ******************** */
/* Add Event Categories */
/* ******************** */
INSERT INTO membercentral.dbo.ev_eventCategories (eventID, categoryID)
SELECT distinct MCEventID, MCCategoryID
FROM #tmpEvImportEV
where MCEventID > @maxEventID
except (
	SELECT eventID, categoryID
	FROM membercentral.dbo.ev_eventCategories
)


/* *********** */
/* Final Check */
/* *********** */
IF EXISTS (select * from #tmpEvImport where MCRegistrationID is null) BEGIN
	select 'Registration is missing' as errReason, * 
	from #tmpEvImport
	where MCRegistrationID is null

	set @errSection = 'registrationmissing'
	GOTO on_error
END


/* *************** */
/* Add Registrants */
/* *************** */
insert into membercentral.dbo.ev_registrants (registrationID, memberID, recordedOnSiteID, rateID, dateRegistered, status, attended)
select distinct MCRegistrationID, MCMemberID, @siteID, null, registrantDate, 'A', 0
from #tmpEvImport as e
where not exists (
	select registrantID
	from membercentral.dbo.ev_registrants r
	inner join membercentral.dbo.ams_members as m on m.orgID = @orgID
		and r.memberid = m.memberID
		and m.activeMemberID = e.MCMemberID
	where r.registrationID = e.MCRegistrationID
)

/* ************************** */
/* ATTENDANCE for Registrants */
/* ************************** */
update evr
set evr.attended = case tmp.registrantAttended when 'Yes' then 1 else 0 end
from membercentral.dbo.ev_registrants as evr
inner join #tmpEvImport as tmp on tmp.MCRegistrationID = evr.registrationID
inner join membercentral.dbo.ams_members as m on m.orgID = @orgid
	and m.memberID = evr.memberID
	and m.activeMemberID = tmp.MCMemberID
where evr.status = 'A'


/* ************************ */
/* CUSTOM Q for Registrants */
/* ************************ */
INSERT INTO membercentral.dbo.ev_registrantDetails (registrantID, customID, customOptionID, customText, status)
select distinct r.registrantID, tmp.custom1QID, null, tmp.custom1, 'A'
from membercentral.dbo.ev_registrants as r
inner join #tmpEvImport as tmp on tmp.MCRegistrationID = r.registrationID 
inner join membercentral.dbo.ams_members as m on m.orgID = @orgid
	and m.memberID = r.memberID
	and m.activeMemberID = tmp.MCMemberID
where len(tmp.custom1) > 0
and tmp.custom1QID is not null


GOTO on_good

on_error:
	print 'import stopped at ' + @errSection
	goto on_done

on_good:
	print 'import success.'

on_done:
	print 'end of import.'

	IF OBJECT_ID('tempdb..##tmpEvImport') IS NOT NULL 
		DROP TABLE ##tmpEvImport
	IF OBJECT_ID('tempdb..#tmpEvImport') IS NOT NULL 
		DROP TABLE #tmpEvImport
	IF OBJECT_ID('tempdb..#tmpEvImportEV') IS NOT NULL 
		DROP TABLE #tmpEvImportEV

	EXEC membercentral.dbo.cache_perms_setStatus @orgID, 'enabled'

SET NOCOUNT OFF

