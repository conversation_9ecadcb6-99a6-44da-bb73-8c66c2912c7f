use membercentral
GO
declare @toolTypeID int, @toolResourceTypeID int, @level3NavID int, @navigationID int, @resourceTypeFunctionID int, @siteID int
set @level3NavID = 126
SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('Admin'),dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('Admin')))

EXEC dbo.createAdminToolType @toolType='MemberCallSheet', @toolCFC='Reports.members.MemberCallSheet', @toolDesc='Call Sheet', @toolTypeID=@toolTypeID OUTPUT, @resourceTypeID=@toolResourceTypeID OUTPUT
	EXEC dbo.createAdminNavigation @navName='Call Sheet', @navDesc='Used to generate a call sheet for membership recruiting', @parentNavigationID=@level3NavID, @navAreaID=4, @cfcMethod='showReport', @isHeader=0, @showInNav=1, @navigationID=@navigationID OUTPUT
		EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID=@resourceTypeFunctionID, @toolTypeID=@toolTypeID, @navigationID=@navigationID

insert into dbo.admin_siteToolRestrictions (toolTypeID, siteID)
select @toolTypeID, siteID
from dbo.admin_siteToolRestrictions as stre
inner join dbo.admin_toolTypes as tt on tt.toolTypeID = stre.toolTypeID
where tt.tooltype = 'MMRMembershipAlphaRoster'

select @siteID = min(siteID) from dbo.sites
while @siteID is not null BEGIN
	exec dbo.createAdminSuite @siteID
	select @siteID = min(siteID) from dbo.sites where siteID > @siteID
END
GO



CREATE FUNCTION dbo.fn_getRecursiveCategoriesByResourceType (@siteID int, @resourceType varchar(60))
RETURNS @tblCategories TABLE (
	categoryID int, 
	categoryName varchar(200), 
	parentCategoryID int, 
	catlevel int, 
	sortExpanded varchar(max)
)
AS
BEGIN

	declare @siteResourceID int, @categoryTreeID int

	select @siteResourceID = dbo.fn_getSiteResourceIDForResourceType(@resourceType, @siteID)
	select @categoryTreeID = dbo.fn_getCategoryTreeIDForSiteResourceID(@siteResourceID)

	; WITH categories( categoryID, categoryName, parentCategoryID, catlevel, sortExpanded)
	AS (
		SELECT categoryID, categoryName, parentCategoryID, 0 AS catlevel, cast(categoryName as varchar(max))
		FROM dbo.cms_categories
		WHERE parentCategoryID is NULL
		and categoryTreeId = @categoryTreeID
		and isActive = 1
			UNION ALL
		SELECT c2.categoryID, c2.categoryName, c2.parentCategoryID, catlevel + 1, cast(sortExpanded + '|' + c2.categoryName as varchar(max))
		FROM dbo.cms_categories c2
		INNER JOIN categories c
		ON c2.parentCategoryID = c.categoryID
		WHERE categoryTreeId = @categoryTreeID
		AND isActive = 1
	)
	insert into @tblCategories (categoryID, categoryName, parentCategoryID, catlevel, sortExpanded)
	SELECT categoryID, categoryName, parentCategoryID, catlevel, sortExpanded
	FROM categories 
	order by sortExpanded 	
	
	RETURN 
END
GO


ALTER FUNCTION [dbo].[fn_getRecursiveCategoriesByResourceType] (@siteID int, @resourceType varchar(60))
RETURNS @tblCategories TABLE (
	categoryID int, 
	categoryName varchar(200), 
	parentCategoryID int, 
	catlevel int, 
	thePathExpanded varchar(MAX),
	thePath [varchar](40)
)
AS
BEGIN

	declare @siteResourceID int, @categoryTreeID int

	select @siteResourceID = dbo.fn_getSiteResourceIDForResourceType(@resourceType, @siteID)
	select @categoryTreeID = dbo.fn_getCategoryTreeIDForSiteResourceID(@siteResourceID)

	; WITH categories( categoryID, categoryName, parentCategoryID, catlevel, thePathExpanded, thePath)
	AS (
		
		SELECT sub1.categoryID, sub1.categoryName, sub1.parentCategoryID, sub1.catlevel, sub1.thePathExpanded,
				CAST(RIGHT('100000'+sub1.theRow,4) as varchar(max)) AS thePath
		FROM 
		(
			SELECT categoryID, categoryName, parentCategoryID, 0 AS catlevel, cast(categoryName as varchar(MAX)) AS thePathExpanded, 
				ROW_NUMBER() OVER (ORDER BY categoryName ASC) AS theRow
			FROM dbo.cms_categories
			WHERE parentCategoryID is NULL
			and categoryTreeId = @categoryTreeID
			AND	 isActive = 1
		) AS sub1
			UNION ALL
		SELECT sub2.categoryID,  sub2.categoryName, sub2.parentCategoryID, sub2.catlevel, sub2.thePathExpanded,
				thePath + '.' + CAST(RIGHT('100000'+sub2.theRow,4) as varchar(max)) AS thePath
		FROM 
		(	
			SELECT c2.categoryID, c2.categoryName, c2.parentCategoryID, catlevel + 1 AS catlevel, cast(thePathExpanded + ' \ ' + c2.categoryName as varchar(MAX)) AS thePathExpanded,
				ROW_NUMBER() OVER (ORDER BY c2.categoryName ASC) AS theRow, [ccte].[thePath]
			FROM dbo.cms_categories c2
			INNER JOIN categories as ccte ON c2.parentCategoryID = ccte.categoryID
			WHERE categoryTreeId = @categoryTreeID
			AND isActive = 1
		) AS sub2
		
	)
	insert INTO @tblCategories (categoryID, categoryName, parentCategoryID, catlevel, thePathExpanded, [thePath])
	SELECT categoryID, categoryName, parentCategoryID, catlevel, thePathExpanded, thePath
	FROM categories 
	order by thePathExpanded 	
	
	RETURN 
END
GO

