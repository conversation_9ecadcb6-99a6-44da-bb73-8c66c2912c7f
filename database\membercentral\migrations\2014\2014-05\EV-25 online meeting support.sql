use membercentral
GO
ALTER TABLE dbo.ev_registration ADD isOnlineMeeting bit not null DEFAULT(0);
GO
ALTER TABLE dbo.ev_registration ADD onlineEmbedCode varchar(max) null;
GO
ALTER TABLE dbo.ev_registration ADD onlineEmbedOverrideLink varchar(400) null;
GO
ALTER TABLE dbo.ev_registration ADD onlineEnterStartTime datetime null, onlineEnterEndTime datetime null;
GO
ALTER PROC [dbo].[ev_getRegistrationMetaByEventID]
@eventID int,
@languageID int

AS

select r.registrationid, r.registrationtypeID, rt.registrationtype, r.startdate, r.enddate, 
	r.registrantCap, r.notifyEmail, r.replyToEmail, r.status, r.isPriceBasedOnActual, r.bulkCountByRate, 
	r.expirationcontentID as expireContentID, expireContent.contentTitle as expireContentTitle, 
	expireContent.rawContent as expireContent,
	r.registrantCapcontentID as registrantCapContentID, 
	registrantCapContent.contentTitle as registrantCapContentTitle, 
	registrantCapContent.rawContent as registrantCapContent,
	dbo.fn_getRegCapReached(r.eventID) as regCapReached,
	r.freeRateDisplay, r.isOnlineMeeting, r.onlineEnterStartTime, r.onlineEnterEndTime, 
	r.onlineEmbedOverrideLink, r.onlineEmbedCode
from dbo.ev_registration as r
inner join dbo.ev_registrationTypes as rt on rt.registrationTypeID = r.registrationTypeID
inner join dbo.ev_events as e on e.eventid = r.eventid and e.status <> 'D'
cross apply dbo.fn_getContent(r.expirationcontentID,@languageID) as expireContent
cross apply dbo.fn_getContent(r.registrantCapcontentID,@languageID) as registrantCapContent
where r.eventID = @eventID
and r.status <> 'D'

RETURN 0
GO

ALTER PROC [dbo].[ev_copyEvent]
@eventid int,
@copiedByMemberID int,
@newEventID int OUTPUT

AS

DECLARE @rc int
DECLARE @siteID int, @eventTypeID int, @lockTimeZoneID int, @isAllDayEvent bit,
	@altRegistrationURL varchar(300), @GLAccountID int, @calendarID int, @status char(1), @reportCode varchar(15),
	@internalNotes varchar(max)
DECLARE @eventContentID int, @contactContentID int, @locationContentID int, @cancelContentID int, @travelContentID int, @informationContentID int
DECLARE @neweventContentID int, @newcontactContentID int, @newlocationContentID int, @newcancelContentID int, @newtravelContentID int, @newinformationContentID int
DECLARE @languageID int, @isSSL bit, @isHTML bit, @contentTitle varchar(200), @contentDesc varchar(400), @rawcontent varchar(max)
DECLARE @emailContactContent bit, @emailLocationContent bit, @emailCancelContent bit, @emailTravelContent	bit
DECLARE @registrationID int, @registrationTypeID int, @startDate datetime, @endDate datetime, @registrantCap int,
	@ReplyToEmail varchar(200), @notifyEmail varchar(200), @isPriceBasedOnActual bit, @bulkCountByRate bit, @newregistrationID int
DECLARE @expirationContentID int, @registrantCapContentID int
DECLARE @newexpirationContentID int, @newregistrantCapContentID int
DECLARE @minofferingID int, @newofferingID int
DECLARE @minRateId int, @rateGroupingID int, @rateGLAccountID int, @rateName varchar(100),
	@rate money, @ratestartDate datetime, @rateendDate datetime, @newRateID int, @newRatesiteResourceID int,
	@ratereportCode varchar(15), @rateQty int
DECLARE @minBulkRateID int, @bulkrate money, @bulksiteResourceID int, @bulkrateqty int, @newBulkRateID int,
	@newBulkRatesiteResourceID int, @bulkresourceRightID int
DECLARE @siteResourceID int, @newrateGroupingID int
DECLARE @srr_rightsID int, @srr_roleid int, @srr_functionID int, @srr_groupid int, @srr_memberid int, @srr_include bit, @srr_inheritedRightsResourceID int, @srr_inheritedRightsFunctionID int, @resourceRightID int
DECLARE @minCustomID int, @newCustomID int
DECLARE @isOnlineMeeting bit, @onlineEmbedCode varchar(max), @onlineEmbedOverrideLink varchar(400), @onlineEnterStartTime datetime, @onlineEnterEndTime datetime
SELECT @newEventID = null

BEGIN TRAN

-- get the event we are copying
SELECT @siteID=siteID, @eventTypeID=eventTypeID, 
	@lockTimeZoneID=lockTimeZoneID, @isAllDayEvent=isAllDayEvent, @altRegistrationURL=altRegistrationURL,
	@GLAccountID=GLAccountID, @status=status, @reportCode=reportcode, @internalNotes=internalNotes,
	@emailContactContent = emailContactContent, @emailLocationContent = emailLocationContent,
	@emailCancelContent = emailCancelContent, @emailTravelContent = emailTravelContent
	FROM dbo.ev_events
	WHERE eventID = @eventID
	IF @@ERROR <> 0 GOTO on_error
SELECT TOP 1 @calendarID = calendarID
	FROM dbo.ev_calendarEvents
	WHERE calendarID = sourceCalendarID
	AND sourceEventID = @eventID
	IF @@ERROR <> 0 GOTO on_error

-- create event
EXEC @rc = dbo.ev_createEvent @siteID=@siteID, @calendarID=@calendarID, @eventTypeID=@eventTypeID, 
	@enteredByMemberID=@copiedByMemberID, @lockTimeZoneID=@lockTimeZoneID, @isAllDayEvent=@isAllDayEvent, 
	@altRegistrationURL=@altRegistrationURL, @status=@status, @reportCode=@reportCode, 
	@emailContactContent=@emailContactContent, @emailLocationContent=@emailLocationContent,
	@emailCancelContent=@emailCancelContent, @emailTravelContent=@emailTravelContent,
	@eventID=@newEventID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

update dbo.ev_events
set GLAccountID = @GLAccountID,
	internalNotes = @internalNotes
where eventID = @newEventID
	IF @@ERROR <> 0 GOTO on_error

select @neweventContentID=eventContentID, @newcontactContentID=contactContentID, @newlocationContentID=locationContentID,
	@newcancelContentID=cancellationPolicyContentID, @newtravelContentID=travelContentID, @newinformationContentID=informationContentID
FROM dbo.ev_events
where eventID = @newEventID
	IF @@ERROR <> 0 GOTO on_error

select @eventContentID=eventContentID, @contactContentID=contactContentID, @locationContentID=locationContentID,
	@cancelContentID=cancellationPolicyContentID, @travelContentID=travelContentID, @informationContentID=informationContentID
FROM dbo.ev_events
where eventID = @EventID
	IF @@ERROR <> 0 GOTO on_error

-- make event inactive
UPDATE dbo.ev_events
SET [status] = 'I'
WHERE eventID = @newEventID
	IF @@ERROR <> 0 GOTO on_error

-- copy event category
INSERT INTO dbo.ev_eventcategories (eventID, categoryID)
select @newEventID, categoryID
from dbo.ev_eventcategories
where eventID = @eventID
	IF @@ERROR <> 0 GOTO on_error

-- copy event times
INSERT INTO dbo.ev_Times (eventid, timeZoneID, startTime, endTime)
select @newEventID, timeZoneID, startTime, endTime
from dbo.ev_times
where eventid = @eventID
	IF @@ERROR <> 0 GOTO on_error

-- copy content objects
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle='Copy of ' + contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@eventContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@neweventContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@contactContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newcontactContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@locationContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newlocationContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@cancelContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newcancelContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@travelContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newtravelContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@informationContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newinformationContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- copy sponsors
insert into dbo.ev_sponsors (eventID, sponsorContentID, sponsorOrder)
select @newEventID, sponsorContentID, sponsorOrder
from dbo.ev_sponsors
where eventID = @eventID
	IF @@ERROR <> 0 GOTO on_error

-- does orig event have registration?
SELECT @registrationID=registrationid, @registrationTypeID=registrationTypeID, @startDate=startdate, 
	@endDate=endDate, @registrantCap=registrantCap, @ReplyToEmail=ReplyToEmail, @notifyEmail=notifyEmail, 
	@isPriceBasedOnActual=isPriceBasedOnActual, @bulkCountByRate=bulkCountByRate, @expirationContentID=expirationContentID, 
	@registrantCapContentID=registrantCapContentID, @isOnlineMeeting=isOnlineMeeting, @onlineEmbedCode=onlineEmbedCode,
	@onlineEmbedOverrideLink=onlineEmbedOverrideLink, @onlineEnterStartTime=onlineEnterStartTime, @onlineEnterEndTime=onlineEnterEndTime
	from dbo.ev_registration
	where eventID = @eventID
	and [status] <> 'D'
IF @registrationID is not null BEGIN

	-- insert registration
	EXEC @rc = dbo.ev_createRegistration @eventID=@newEventID, @registrationTypeID=@registrationTypeID, 
			@startDate=@startDate, @endDate=@endDate, @registrantCap=@registrantCap, @ReplyToEmail=@ReplyToEmail,
			@notifyEmail=@notifyEmail, @isPriceBasedOnActual=@isPriceBasedOnActual, @bulkCountByRate=@bulkCountByRate,
			@registrationID=@newregistrationID OUTPUT
		IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

	select @newexpirationContentID=expirationContentID, @newregistrantCapContentID=registrantCapContentID
	FROM dbo.ev_registration
	where registrationID = @newregistrationID
		IF @@ERROR <> 0 GOTO on_error

	-- copy content objects
	select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@expirationContentID,1)
	EXEC @rc = dbo.cms_updateContent @contentID=@newexpirationContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
		IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
	select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@registrantCapContentID,1)
	EXEC @rc = dbo.cms_updateContent @contentID=@newregistrantCapContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
		IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

	-- if reg type
	IF @registrationTypeID = 1 BEGIN

		-- other registration fields
		update dbo.ev_registration
		set isOnlineMeeting = @isOnlineMeeting,
			onlineEmbedCode = @onlineEmbedCode,
			onlineEmbedOverrideLink = @onlineEmbedOverrideLink,
			onlineEnterStartTime = @onlineEnterStartTime,
			onlineEnterEndTime = @onlineEnterEndTime
		where registrationID = @newregistrationID
			IF @@ERROR <> 0 GOTO on_error

		-- merchant profiles
		insert into dbo.ev_registrationMerchantProfiles (registrationID, profileID)
		select @newregistrationID, rmp.profileID
		from dbo.ev_registrationMerchantProfiles as rmp
		inner join dbo.mp_profiles as mp on mp.profileID = rmp.profileID
		inner join dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
		where rmp.registrationID = @registrationID
		and mp.status = 'A'
		and mp.allowPayments = 1
		and g.isActive = 1
			IF @@ERROR <> 0 GOTO on_error

		-- credit offered
		select @minofferingID = min(offeringID) from dbo.crd_offerings where eventID = @eventID
		while @minofferingID is not null BEGIN
			INSERT INTO dbo.crd_offerings (ASID, statusID, ApprovalNum, offeredStartDate, offeredEndDate, completeByDate, isCreditRequired, isIDRequired, isCreditDefaulted, showCredit, eventID)
			SELECT ASID, statusID, ApprovalNum, offeredStartDate, offeredEndDate, completeByDate, isCreditRequired, isIDRequired, isCreditDefaulted, showCredit, @newEventID
			FROM dbo.crd_offerings
			WHERE offeringID = @minofferingID
				IF @@ERROR <> 0 GOTO on_error
				SELECT @newofferingID = SCOPE_IDENTITY()

			INSERT INTO dbo.crd_offeringTypes (offeringID, ASTID, creditValue)
			SELECT @newofferingID, ASTID, creditValue
			FROM dbo.crd_offeringTypes
			WHERE offeringID = @minofferingID
				IF @@ERROR <> 0 GOTO on_error

			select @minofferingID = min(offeringID) from dbo.crd_offerings where eventID = @eventID and offeringID > @minofferingID
		END

		-- rate groupings
		insert into dbo.ev_rateGrouping (rateGrouping, registrationID, rateGroupingOrder)
		select rateGrouping, @newregistrationID, rateGroupingOrder
		from dbo.ev_rateGrouping
		where registrationID = @registrationID
			IF @@ERROR <> 0 GOTO on_error

		-- active rates and permissions
		select @minRateId = min(r.rateID) 
			from dbo.ev_rates as r
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			where r.registrationID = @registrationID
			and r.parentRateID is null
		while @minRateID is not null BEGIN
			select @rateGroupingID = null, @rateGLAccountID = null, @rateName = null, 
				@ratereportCode = null, @rate = null, @ratestartDate = null, @rateendDate = null, 
				@siteResourceID = null, @rateqty = null

			select @rateGroupingID=rateGroupingID, @rateGLAccountID=GLAccountID, @rateName=rateName, 
				@ratereportCode=reportCode, @rate=rate, @ratestartDate=startDate, @rateendDate=endDate, 
				@siteResourceID=siteResourceID, @rateqty=bulkQty
			from dbo.ev_rates 
			where rateID = @minRateID

			select @newrateGroupingID = rg1.rateGroupingID
			from dbo.ev_rateGrouping as rg1
			inner join dbo.ev_rateGrouping as rg2 
				on rg2.rateGrouping = rg1.rateGrouping
				and rg1.registrationID = @newregistrationID
				and rg2.registrationID = @registrationID
				and isnull(rg2.rateGroupingID,0) = isnull(@rateGroupingID,0)
			
			IF @rateName is not null and @rate is not null BEGIN
				EXEC @rc = dbo.ev_createRate @registrationID=@newregistrationID,  
					@rateGroupingID=@newrateGroupingID, @GLAccountID=@rateGLAccountID, @rateName=@rateName, 
					@reportCode=@ratereportCode, @rate=@rate, @startDate=@ratestartDate, @endDate=@rateendDate,
					@parentRateID=null, @qty=@rateqty, 
					@rateID=@newRateID OUTPUT, @siteResourceID=@newRatesiteResourceID OUTPUT
					IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

				select @minBulkRateID = min(rateID)
				from dbo.ev_rates
				where parentRateID = @minRateID

				while @minBulkRateID is not null begin
					select @bulkrate=rate, @bulksiteResourceID=siteResourceID, @bulkrateqty=bulkQty
					from dbo.ev_rates 
					where rateID = @minBulkRateID

					EXEC @rc = dbo.ev_createRate @registrationID=@newregistrationID,  
						@rateGroupingID=@newrateGroupingID, @GLAccountID=@rateGLAccountID, @rateName=@rateName, 
						@reportCode=@ratereportCode, @rate=@rate, @startDate=@ratestartDate, @endDate=@rateendDate,
						@parentRateID=@newRateID, @qty=@bulkrateqty, 
						@rateID=@newBulkRateID OUTPUT, @siteResourceID=@newBulkRatesiteResourceID OUTPUT
						IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

					select @minBulkRateID = min(rateID)
					from dbo.ev_rates
					where parentRateID = @minRateID
					and rateID > @minBulkRateID
				end

				-- copy resource rights for this resource		
				SELECT @srr_rightsID = null	
				SELECT @srr_rightsID = min(resourceRightsID) from dbo.cms_siteResourceRights where resourceID = @siteResourceID
				WHILE @srr_rightsID IS NOT NULL BEGIN
					SELECT @srr_roleid=roleID, @srr_functionID=functionID, @srr_groupid=groupID, 
						@srr_memberid=memberID, @srr_include=[include], @srr_inheritedRightsResourceID=inheritedRightsResourceID, 
						@srr_inheritedRightsFunctionID=inheritedRightsFunctionID
					FROM dbo.cms_siteResourceRights
					WHERE resourceRightsID = @srr_rightsID

					EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@newRatesiteResourceID, @include=@srr_include, 
						@functionID=@srr_functionID, @roleID=@srr_roleid, @groupID=@srr_groupid, @memberID=@srr_memberid, 
						@inheritedRightsResourceID=@srr_inheritedRightsResourceID, @inheritedRightsFunctionID=@srr_inheritedRightsFunctionID, 
						@resourceRightID=@resourceRightID OUTPUT
					IF @@ERROR <> 0 GOTO on_error

					select @minBulkRateID = min(rateID)
					from dbo.ev_rates
					where parentRateID = @newRateID

					while @minBulkRateID is not null begin
						select @bulksiteResourceID=siteResourceID
						from dbo.ev_rates 
						where rateID = @minBulkRateID

						EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@bulksiteResourceID, @include=@srr_include, 
							@functionID=@srr_functionID, @roleID=@srr_roleid, @groupID=@srr_groupid, @memberID=@srr_memberid, 
							@inheritedRightsResourceID=@srr_inheritedRightsResourceID, @inheritedRightsFunctionID=@srr_inheritedRightsFunctionID, 
							@resourceRightID=@bulkresourceRightID OUTPUT
						IF @@ERROR <> 0 GOTO on_error

						select @minBulkRateID = min(rateID)
						from dbo.ev_rates
						where parentRateID = @minRateID
						and rateID > @newRateID
					end
		
					SELECT @srr_rightsID = min(resourceRightsID) from dbo.cms_siteResourceRights where resourceID = @siteResourceID and resourceRightsID > @srr_rightsID
				END

			END
			
			select @minRateId = min(rateID) 
				from dbo.ev_rates as r
				inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
				inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
					and srs.siteResourceStatusDesc = 'Active'
				where r.registrationID = @registrationID
				and r.parentRateID is null
				and rateID > @minRateID
		END

		-- custom questions
		SELECT @minCustomID = min(CustomID) FROM dbo.ev_registrationCustom WHERE registrationID = @registrationID
		WHILE @minCustomID is not null BEGIN
			INSERT INTO dbo.ev_registrationCustom (registrationID, areaID, FieldDesc, titleOnInvoice, customTypeID, IsRequired, RequiredMsg, fieldOrder, Status, amount, offerQTY, GLAccountID)
			SELECT	@newregistrationID, areaID, FieldDesc, titleOnInvoice, customTypeID, IsRequired, RequiredMsg, fieldOrder, Status, amount, offerQTY, GLAccountID
			FROM	dbo.ev_registrationCustom	
			WHERE	customID = @minCustomID
				IF @@ERROR <> 0 GOTO on_error
				SELECT @newCustomID = SCOPE_IDENTITY()
				
			INSERT INTO dbo.ev_customOptions(customID, optionDesc, optionOrder, status, amount)
			SELECT	@newCustomID, optionDesc, optionOrder, status, amount
			FROM	dbo.ev_customOptions
			WHERE	customID = @minCustomID
				IF @@ERROR <> 0 GOTO on_error			
	
			SELECT @minCustomID = min(CustomID) FROM dbo.ev_registrationCustom WHERE registrationID = @registrationID and CustomID > @minCustomID	
		END

	END

END

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @newEventID = 0
	RETURN -1
GO


USE [platformstats]
GO
CREATE TABLE [dbo].[ev_onlineMeetingLog](
	[pageloadUID] [uniqueidentifier] NOT NULL CONSTRAINT [DF_ev_onlineMeetingLog_pageloadUID]  DEFAULT (newid()),
	[eventID] [int] NOT NULL,
	[registrantID] [int] NOT NULL,
	[statsSessionID] [bigint] NOT NULL,
	[dateCreated] [datetime] NOT NULL CONSTRAINT [DF_ev_onlineMeetingLog_dateCreated]  DEFAULT (getdate()),
	[dateUpdated] [datetime] NOT NULL CONSTRAINT [DF_ev_onlineMeetingLog_dateUpdated]  DEFAULT (getdate()),
	[numSeconds] [int] NOT NULL CONSTRAINT [DF_ev_onlineMeetingLog_numSeconds]  DEFAULT ((0)),
 CONSTRAINT [PK_ev_onlineMeetingLog] PRIMARY KEY CLUSTERED 
(
	[pageloadUID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO


CREATE PROC dbo.ev_UpdateOnlineMeetingLog
@pageLoadUID uniqueidentifier,
@numSeconds int

AS

BEGIN TRY

	UPDATE dbo.ev_onlineMeetingLog
	SET dateUpdated = getdate(), 
		numSeconds = numSeconds + @numSeconds
	WHERE pageLoadUID = @pageLoadUID
		IF @@ROWCOUNT = 0 RAISERROR('pageLoadUID not found',16,1)

END TRY
BEGIN CATCH
	EXEC membercentral.dbo.dbo.up_errorhandler
END CATCH

GO

CREATE PROC dbo.ev_InsertOnlineMeetingLog
@eventID int,
@memberID int,
@statsSessionID bigint,
@pageloadUID uniqueidentifier OUTPUT

AS

declare @registrantID int

-- init output variable
set @pageloadUID = NEWID()

BEGIN TRY

	select top 1 @registrantID = evr.registrantID
		from membercentral.dbo.ev_registrants as evr
		inner join membercentral.dbo.ev_registration as r on r.registrationID = evr.registrationID 
			and evr.status = 'A' 
			and r.eventID = @eventID
		inner join membercentral.dbo.ams_members as m on m.memberid = evr.memberid
		inner join membercentral.dbo.ams_members as m2 on m2.memberid = @memberID
			and m2.activeMemberID = m.activeMemberID
	
	IF @registrantID is not null 
		INSERT INTO dbo.ev_onlineMeetingLog (pageloadUID, eventID, registrantID, statsSessionID)
		VALUES (@pageloadUID, @eventID, @registrantID, @statsSessionID)
	ELSE
		RAISERROR('registrantID not found',16,1)

END TRY
BEGIN CATCH
	set @pageloadUID = null
	EXEC membercentral.dbo.dbo.up_errorhandler
END CATCH

GO


USE [memberCentral]
GO
CREATE PROC [dbo].[ev_exportOnlineMeetingLog]
	@registrationID int,
	@filename varchar(400)
AS

declare @eventID int
select @eventID = eventID from dbo.ev_registration where registrationID = @registrationID

IF OBJECT_ID('tempdb..#tmpAuditLog') IS NOT NULL 
	DROP TABLE #tmpAuditLog
select pageloadUID, registrantID, statsSessionID, dateCreated, dateUpdated, numSeconds
into #tmpAuditLog
from platformStats.dbo.ev_onlineMeetingLog
where eventID = @eventID

declare @fullsql varchar(max)
select @fullsql = '
	select mr2.LastName, mr2.FirstName, mr2.MemberNumber, 
		case when r.status = ''D'' then ''Removed'' else '''' end as RegistrantStatus,
		auditlog.pageloadUID as [PageLoadUID], auditlog.StatsSessionID, 
		auditlog.dateCreated as [Date Joined], auditlog.dateUpdated as [Date Left], auditlog.numSeconds [Number of Seconds]
	from dbo.ev_registrants as r
	inner join dbo.ams_members as mr on mr.memberID = r.memberid
	inner join dbo.ams_members as mr2 on mr2.memberID = mr.activeMemberID
	inner join #tmpAuditLog as auditlog on auditlog.registrantID = r.registrantID
	where r.registrationID = ' + cast(@registrationID as varchar(20)) + ' 
	order by r.status, mr2.LastName, mr2.FirstName, mr2.MemberNumber, auditlog.dateCreated
'

-- export
EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql

IF OBJECT_ID('tempdb..#tmpAuditLog') IS NOT NULL 
	DROP TABLE #tmpAuditLog

RETURN 0
GO

