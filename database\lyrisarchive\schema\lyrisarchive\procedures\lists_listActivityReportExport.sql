ALTER PROC dbo.lists_listActivityReportExport
@orgcode varchar(10),
@orgEmailDomain varchar(100),
@filename varchar(800)

AS

IF OBJECT_ID('tempdb..##tmpListActvityExport') IS NOT NULL 
	DROP TABLE ##tmpListActvityExport

select 
	l.name_,
	l.creatStamp_,
	l.descShort_,
	(select count(*) from lyris.trialslyris1.dbo.members_ where list_ = l.name_ COLLATE Latin1_General_CI_AI) as numTotalMembers,
	(select count(*) from lyris.trialslyris1.dbo.members_ where list_ = l.name_ COLLATE Latin1_General_CI_AI and domain_ in (@orgEmailDomain,'trialsmith.com','membercentral.com','seminarweb.com')) as numStaffMembers,
	(select count(*) from lyris.trialslyris1.dbo.members_ where list_ = l.name_ COLLATE Latin1_General_CI_AI and domain_ not in (@orgEmailDomain,'trialsmith.com','membercentral.com','seminarweb.com')) as numNonStaffMembers,
	(
		select max(creatStamp_)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID
			and ml.list = l.name_ COLLATE Latin1_General_CI_AI
			and m.isVisible=1
		group by ml.list
	) as mostRecentMessage,
	(
		select count(creatStamp_)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID
		and ml.list = l.name_  COLLATE Latin1_General_CI_AI
		and right(hdrFromSpc_,len(hdrFromSpc_) - charindex('@',hdrFromSpc_)) not in (@orgEmailDomain,'trialsmith.com','membercentral.com','seminarweb.com')
		and m.isVisible=1
		group by ml.list
	) as numMessagesInArchive
into ##tmpListActvityExport
from lyris.trialslyris1.dbo.lists_ l
inner join lyris.trialslyris1.dbo.lists_format lf on l.name_ = lf.name COLLATE Latin1_General_CI_AI
	and lf.orgcode = @orgcode

EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql='select name_, creatStamp_, descShort_, numTotalMembers, numStaffMembers, numNonStaffMembers, mostRecentMessage, numMessagesInArchive from ##tmpListActvityExport order by name_'

IF OBJECT_ID('tempdb..##tmpListActvityExport') IS NOT NULL 
	DROP TABLE ##tmpListActvityExport
GO
