DELETE FROM dbo.ams_zipCodes where latitude is null
GO
DELETE FROM dbo.ams_zipCodes where len(zipcode) = 5
GO

ALTER TABLE dbo.ams_zipCodes
	DROP CONSTRAINT FK_ams_zipCodes_ams_countries
GO
ALTER TABLE dbo.ams_zipCodes
	DROP CONSTRAINT DF_ams_zipCodes_countryID
GO
CREATE TABLE dbo.Tmp_ams_zipCodes
	(
	zipcodeid int NOT NULL IDENTITY (1, 1),
	zipcode varchar(7) NOT NULL,
	ZIPType char(1) NULL,
	cityName varchar(64) NULL,
	cityType char(1) NULL,
	countyName varchar(64) NULL,
	countyFIPS char(5) NULL,
	stateName varchar(64) NULL,
	stateAbbr char(2) NULL,
	stateFIPS char(2) NULL,
	countryID int NOT NULL,
	MSACode char(4) NULL,
	areaCode varchar(16) NULL,
	timeZone varchar(16) NULL,
	UTC decimal(3, 1) NULL,
	DST char(1) NULL,
	latitude decimal(9, 6) NOT NULL,
	longitude decimal(9, 6) NOT NULL
	)  ON [PRIMARY]
GO
SET IDENTITY_INSERT dbo.Tmp_ams_zipCodes ON
GO
IF EXISTS(SELECT * FROM dbo.ams_zipCodes)
	 EXEC('INSERT INTO dbo.Tmp_ams_zipCodes (zipcodeid, zipcode, latitude, longitude, cityName, stateName, stateAbbr, countryID)
		SELECT zipcodeid, CONVERT(varchar(7), zipcode), CONVERT(decimal(9, 6), latitude), CONVERT(decimal(9, 6), longitude), City, State, Abbr, countryID FROM dbo.ams_zipCodes WITH (HOLDLOCK TABLOCKX)')
GO
SET IDENTITY_INSERT dbo.Tmp_ams_zipCodes OFF
GO
DROP TABLE dbo.ams_zipCodes
GO
EXECUTE sp_rename N'dbo.Tmp_ams_zipCodes', N'ams_zipCodes', 'OBJECT' 
GO
ALTER TABLE dbo.ams_zipCodes ADD CONSTRAINT
	PK_ams_zipCodes PRIMARY KEY CLUSTERED 
	(
	zipcodeid
	) WITH( PAD_INDEX = OFF, FILLFACTOR = 90, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

GO
ALTER TABLE dbo.ams_zipCodes ADD CONSTRAINT
	FK_ams_zipCodes_ams_countries FOREIGN KEY
	(
	countryID
	) REFERENCES dbo.ams_countries
	(
	countryID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO

ALTER TABLE dbo.ams_zipCodes
	DROP CONSTRAINT PK_ams_zipCodes
GO
ALTER TABLE dbo.ams_zipCodes
	DROP COLUMN zipcodeid
GO
ALTER TABLE dbo.ams_zipCodes
	DROP CONSTRAINT FK_ams_zipCodes_ams_countries
GO
CREATE TABLE dbo.Tmp_ams_zipCodes
	(
	zipcodeID int NOT NULL IDENTITY (1, 1),
	zipcode varchar(7) NOT NULL,
	ZIPType char(1) NULL,
	cityName varchar(64) NULL,
	cityType char(1) NULL,
	countyName varchar(64) NULL,
	countyFIPS char(5) NULL,
	stateName varchar(64) NULL,
	stateAbbr char(2) NULL,
	stateFIPS char(2) NULL,
	countryID int NOT NULL,
	MSACode char(4) NULL,
	areaCode varchar(16) NULL,
	timeZone varchar(16) NULL,
	UTC decimal(3, 1) NULL,
	DST char(1) NULL,
	latitude decimal(9, 6) NOT NULL,
	longitude decimal(9, 6) NOT NULL
	)  ON [PRIMARY]
GO
SET IDENTITY_INSERT dbo.Tmp_ams_zipCodes OFF
GO
IF EXISTS(SELECT * FROM dbo.ams_zipCodes)
	 EXEC('INSERT INTO dbo.Tmp_ams_zipCodes (zipcode, ZIPType, cityName, cityType, countyName, countyFIPS, stateName, stateAbbr, stateFIPS, countryID, MSACode, areaCode, timeZone, UTC, DST, latitude, longitude)
		SELECT zipcode, ZIPType, cityName, cityType, countyName, countyFIPS, stateName, stateAbbr, stateFIPS, countryID, MSACode, areaCode, timeZone, UTC, DST, latitude, longitude FROM dbo.ams_zipCodes WITH (HOLDLOCK TABLOCKX)')
GO
DROP TABLE dbo.ams_zipCodes
GO
EXECUTE sp_rename N'dbo.Tmp_ams_zipCodes', N'ams_zipCodes', 'OBJECT' 
GO
ALTER TABLE dbo.ams_zipCodes ADD CONSTRAINT
	PK_ams_zipCodes PRIMARY KEY CLUSTERED 
	(
	zipcodeID
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

GO
ALTER TABLE dbo.ams_zipCodes ADD CONSTRAINT
	FK_ams_zipCodes_ams_countries FOREIGN KEY
	(
	countryID
	) REFERENCES dbo.ams_countries
	(
	countryID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO

ALTER FUNCTION [dbo].[fn_PythagoreanZipCodeResults] (@zip varchar(7), @radius int, @countryID int)
RETURNS @tmpResults TABLE (ZipCode varchar(7))
AS
BEGIN

	DECLARE @PiDivRad REAL, @GlobeLatitudeMiles FLOAT, @CurrentLatitude FLOAT, @CurrentLongitude FLOAT, @CurrentLongitudeMiles FLOAT

	/* Set varibale Constants */
	SET @PIDivRad = 0.017453292519943295769236907684886      /* Pi divided by 180. */
	SET @GlobeLatitudeMiles = 69.1                           /* Number of miles per degree of latitude. */

	/* If USA, consider 5 digits only */
	SET @zip = replace(@zip,' ','')
	if @countryID = 1
		SET @zip = left(@zip,5)

	/* Get Current zip code's latitude and longitude */
	SELECT @CurrentLatitude = LATITUDE, @CurrentLongitude = LONGITUDE 
	FROM dbo.ams_zipCodes WITH(NOLOCK)
	WHERE ZIPCODE = @zip

	/* Calculate longitude miles */
	SELECT @CurrentLongitudeMiles = ABS(@GlobeLatitudeMiles * COS(@CurrentLatitude * @PiDivRad)) 

	/* Get zips in proximity */
	INSERT INTO @tmpResults (ZipCode) 
	SELECT distinct ZipCode
	FROM dbo.ams_zipCodes
	WHERE SQRT(SQUARE(@GlobeLatitudeMiles * (LATITUDE - (@CurrentLatitude))) + SQUARE(@CurrentLongitudeMiles * (LONGITUDE - (@CurrentLongitude)))) < @radius

	RETURN
END
GO

update ams_zipCodes
set StateAbbr = null
GO

ALTER TABLE ams_zipcodes ALTER COLUMN stateAbbr int null
GO
sp_RENAME 'ams_zipcodes.stateAbbr', 'stateID' , 'COLUMN'
GO
ALTER TABLE dbo.ams_zipCodes ADD CONSTRAINT
	FK_ams_zipCodes_ams_states FOREIGN KEY
	(
	stateID
	) REFERENCES dbo.ams_states
	(
	stateID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO

update zip
set zip.stateID = s.stateID
from dbo.ams_zipCodes as zip
inner join ams_states as s on s.name = zip.stateName
	and s.countryID = zip.countryID
GO

update ams_zipCodes
set stateID = 57
where stateName = 'Newfoundland'
and countryID = 2
GO

ALTER TABLE ams_zipCodes DROP column stateName
GO

