use membercentral
GO

/*
This script will re-assign documents to the main root since their parent section got deleted. This is to avoid orphancy. 
*/

update d
set
	d.sectionID = psRoot.sectionID
from dbo.cms_documents d
	inner join dbo.cms_siteResources srDoc on 
		d.siteResourceID = srDoc.siteResourceID
	inner join dbo.cms_siteResourceStatuses srsDoc on 
		srDoc.siteResourceStatusID = srsDoc.siteResourceStatusID 
		and srsDoc.siteResourceStatusDesc <> 'Deleted'
	inner join dbo.cms_pageSections ps on 
		ps.sectionID = d.sectionID
	inner join dbo.cms_siteResources srSection on 
		ps.siteResourceID = srSection.siteResourceID
	inner join dbo.cms_siteResourceStatuses srsSection on 
		srSection.siteResourceStatusID = srsSection.siteResourceStatusID 
		and srsSection.siteResourceStatusDesc = 'Deleted'
	cross apply (
				select sectionID, sectionName, siteResourceID
				from dbo.cms_pageSections 
				where sectionID = dbo.fn_getRootSectionID(d.siteID)
					AND siteID =  d.siteID
	) as psRoot

GO