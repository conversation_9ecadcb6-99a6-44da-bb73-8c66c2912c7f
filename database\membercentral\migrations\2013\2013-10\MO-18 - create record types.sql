declare
@orgcode varchar(10), 
@orgID int,
@siteID int,
@firmRecordTypeID int,
@personRecordTypeID int,
@firmAttorneyRelationshipTypeID int,
@trashID int

set @orgcode = 'MO'
select @siteID = siteID, @orgID = orgID from sites where sitecode = @orgcode

-- Create Record Types
exec dbo.ams_createRecordType
@orgID=@orgID,
@recordTypeCode = 'Firm',
@recordTypeName = 'Firm', 
@recordTypeDesc = 'Firm',
@isPerson = 0,
@isOrganization = 1,
@recordTypeID = @firmRecordTypeID OUTPUT

exec dbo.ams_createRecordType
@orgID=@orgID,
@recordTypeCode = 'Individual',
@recordTypeName = 'Individual', 
@recordTypeDesc = 'Individual',
@isPerson = 1,
@isOrganization = 0,
@recordTypeID = @personRecordTypeID OUTPUT

-- Create Record Relationship Type - Firm
exec dbo.ams_createRecordRelationShipType
@orgID=@orgID,
@relationshipTypeCode='Attorney',
@relationshipTypeName='Attorney',
@relationshipTypeID =@firmAttorneyRelationshipTypeID OUTPUT

-- Create Record Types Relationship Type for Firms  Attorney
exec dbo.ams_createRecordTypesRelationshipTypes
@relationshipTypeID=@firmAttorneyRelationshipTypeID,
@masterRecordTypeID=@firmRecordTypeID,
@childRecordTypeID=@personRecordTypeID,
@isActive=1,
@recordTypeRelationshipTypeID = @trashID OUTPUT
GO