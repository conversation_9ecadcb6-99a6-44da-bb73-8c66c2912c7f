use seminarWeb
GO

ALTER TABLE dbo.tblParticipants ADD
	brandSWLMarketingEmail varchar(50) NULL
GO


ALTER PROC [dbo].[sw_addParticipant]
@orgcode varchar(5)

AS

-- lookup participantID
DECLARE @participantID int
SELECT @participantID = dbo.fn_getParticipantIDFromOrgcode(@orgcode)

-- add to participants if not there
IF @participantID = 0 BEGIN

	INSERT INTO dbo.tblParticipants (orgcode, showUSD, supportPhone, supportEmail, 
		isConf, isSWOD, isSWTL, isSWL, isSearch, isMyCLE, catalogURL, wddxTimeZones, 
		emailFrom, brandHome, brandHomeTab, brandConf, brandConfTab, brandSWL, 
		brandSWLTab, brandSWOD, brandSWODTab, brandSWTL, brandSWTLTab, brandMyCle, 
		brandMyCleTab, brandSWLMarketingEmail)
	SELECT @orgcode, 0, '************', '<EMAIL>', 0, 0, 0, 0, 0, 0, 
		(select top 1 url + '/seminarweb' from trialsmith.dbo.depoTLA where state = @orgcode), 
		'<wddxPacket version=''1.0''><header/><data><struct><var name=''supporting''><array length=''3''><string>P</string><string>M</string><string>E</string></array></var><var name=''default''><string>C</string></var></struct></data></wddxPacket>', 
		'SeminarWeb', 'CLE Catalog', 'CLE Catalog', 'Live Conferences & Events',
		'Conferences & Events', 'Live Webinars & Teleconferences', 'Live Webinars', 
		'Self-Paced Online Seminars', 'Self-Paced Online', 'Library of Titles, Papers, and Multimedia', 
		'Title Library', 'My CLE', 'My CLE', 'support'

	insert into lyris.trialslyris1.dbo.sw_marketing (orgcode,list,email)
	values (@orgcode,'seminarweblive','<EMAIL>')

END
GO


ALTER PROC [dbo].[sw_getAssociationDetails]
@orgcode varchar(5)

AS

SELECT top 1 p.participantID, p.orgcode, p.showUSD, p.supportPhone, p.supportEmail, p.isConf, p.isSWOD, p.isSWTL, p.isSWL, 
	p.isSearch, p.isMyCLE, p.catalogURL, p.wddxTimeZones, p.emailFrom, p.brandHome, p.brandHomeTab, p.brandConf, 
	p.brandConfTab, p.brandSWL, p.brandSWLTab, p.brandSWOD, p.brandSWODTab, p.brandSWTL, p.brandSWTLTab, p.brandMyCLE, 
	p.brandMyCLETab, p.brandHomeText, p.brandHomeRegister, p.isSelfReportCredit, p.brandFAQ, p.brandFAQTab, p.brandSWODNameLink, 
	p.brandSWODCategoryLink, p.brandSWODPresenterLink, p.brandSWODCreditLink, p.brandSWODPublisherLink, 
	p.brandSWODDatePublishedLink, p.brandSWTLCategoryLink, p.brandSWTLPresenterLink, p.brandSWTLPublisherLink, 
	p.brandSWTLDatePublishedLink, p.brandSWLDateLink, p.brandSWLSpeakerLink, p.brandSWLCreditLink, p.showSWBranding,
	tla.shortname, tla.description, tla.url, sr.storageRate, p.logoFileExtension, p.usePublisherLogo, p.brandSWLMarketingEmail
FROM dbo.tblParticipants AS p 
INNER JOIN trialsmith.dbo.depoTLA AS tla ON tla.State = p.orgcode 
LEFT OUTER JOIN dbo.tblStorageRates as sr on sr.participantID = p.participantID
WHERE p.orgcode = @orgcode

RETURN
GO

-- Populate the brandSWLMarketingEmail field with current data.
update p 
	set brandSWLMarketingEmail = left(email, charIndex('@', email) - 1)
from  lyris.trialslyris1.dbo.sw_marketing m
inner join tlasites.seminarweb.dbo.tblParticipants p
	on p.orgcode = m.orgcode
GO

-- Update the sw_marketing table with the address format.
update m 
	set email = '"' + emailFrom + '" <' + brandSWLMarketingEmail + '@seminarweb.com>'
from  lyris.trialslyris1.dbo.sw_marketing m
inner join tlasites.seminarweb.dbo.tblParticipants p
	on p.orgcode = m.orgcode
GO