CREATE PROC dbo.ev_getSyndicationRules
@calendarID int

AS

select ce.calendarEventID, 
	ai.applicationInstanceName + case 
		WHEN communityInstances.applicationInstanceName is not null THEN ' (' + communityInstances.applicationInstanceName + ')'
		ELSE ''
		END as sourceCalendar,
	cat.category as sourceCategory,
	cat2.category as syndCategory,
	convert(varchar(10),t.startTime,101) + ' - ' + eventContent.contentTitle as sourceEventName
from dbo.ev_calendarEvents as ce
inner join dbo.ev_calendars as c on c.calendarID = ce.sourcecalendarID
inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = c.applicationInstanceID
inner join dbo.cms_siteResources as sr on ai.siteResourceID = sr.siteResourceID
inner join dbo.cms_siteResources as parentResource on parentResource.siteResourceID = sr.parentSiteResourceID
left outer join dbo.cms_siteResources as grandparentResource
	inner join dbo.cms_applicationInstances as CommunityInstances on communityInstances.siteResourceID = grandParentResource.siteResourceID
	inner join dbo.cms_siteResourceTypes as srt on srt.resourceTypeID = grandparentResource.resourceTypeID and srt.resourceType = 'Community'
		on grandparentResource.siteResourceID = parentResource.parentSiteResourceID
left outer join dbo.ev_categories cat on cat.categoryID = ce.sourceCategoryID
left outer join dbo.ev_categories cat2 on cat2.categoryID = ce.ovCategoryID
left outer join dbo.ev_events as e
	cross apply dbo.fn_getContent(e.eventContentID,1) as eventContent
	inner join dbo.ev_times as t on t.eventID = e.eventID and t.timeZoneID = 3
	on e.eventID = ce.sourceEventID
where ce.calendarID = @calendarID
and ce.calendarID <> ce.sourceCalendarID
order by sourceCalendar, sourceCategory, sourceEventName, calendarEventID

GO