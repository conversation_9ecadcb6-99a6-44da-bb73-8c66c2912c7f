use membercentral
GO

declare @authorityID int, @typeID1 int, @typeID2 int, @typeID3 int

insert into dbo.crd_authorities (authorityName, contact, address, city, state, ZIP, phone, fax, email, website, creditIDText)
values ('State Bar Of Montana', '', 'P.O. Box 577', 'Helena', 'MT', '59624', '************', '************', '', '', 'Bar Number')
	select @authorityID = SCOPE_IDENTITY()

insert into dbo.crd_authorityTypes (authorityID, typeName)
values (@authorityID, 'General')
insert into dbo.crd_authorityTypes (authorityID, typeName)
values (@authorityID, 'Self-Study')
insert into dbo.crd_authorityTypes (authorityID, typeName)
values (@authorityID, 'Ethics')
GO
