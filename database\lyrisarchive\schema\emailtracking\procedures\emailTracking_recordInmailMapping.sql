ALTER PROCEDURE emailTracking_recordInmailMapping
	@sesid varchar(150),
	@messageID int,
	@toEmail varchar(320),
	@fromEmail varchar(320)
AS
SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	--  Insert data into emailtracking.dbo.incomingMessageInmailMappings if it doesn't exist already
	IF NOT EXISTS (
		select 1
		from dbo.incomingMessageInmailMappings
		where sesid = @sesid
		and toAddress = @toEmail
	) BEGIN
		SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

        INSERT INTO emailtracking.dbo.incomingMessageInmailMappings
            (sesID, messageID_, toAddress, fromAddress)
        select @sesid, @messageID, @toEmail, @fromEmail
        WHERE NOT EXISTS(
       		select 1
            from dbo.incomingMessageInmailMappings
            where sesid = @sesid
            and toAddress = @toEmail
        );

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	END
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLL<PERSON>CK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO
