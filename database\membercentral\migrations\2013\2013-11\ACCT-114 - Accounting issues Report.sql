CREATE PROC dbo.tr_reportIssues

AS

-- open invoices due > 7 days ago
select o.orgcode, o.orgname, o.accountingEmail, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, 
	i.invoiceID, i.dateBilled, i.dateDue,
	m2.lastname 
	+ case when o.hasSuffix = 1 then isnull(' ' + nullif(m2.suffix,''),'') else '' end
	+ ', ' + m2.firstname 
	+ case when o.hasMiddleName = 1 then isnull(' ' + nullif(m2.middlename,''),'') else '' end 
	+ ' (' + m2.membernumber + ')' as memberName,
	case when i.payProfileID is null then 0 else 1 end as hasCard,
	isnull(sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount),0) as InvDue
from dbo.tr_invoices as i
inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
inner join dbo.ams_members as m on m.memberid = i.assignedToMemberID
inner join dbo.ams_members as m2 on m2.memberid = m.activeMemberID
inner join dbo.organizations as o on o.orgID = m.orgID
left outer join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
where ins.status = 'Open'
and i.dateDue < dateadd(d,-7,getdate())
and isnull(o.accountingEmail,'') <> ''
group by o.orgcode, o.orgname, o.accountingEmail, i.invoiceNumber, i.invoiceID, i.dateBilled, i.dateDue, 
	m2.lastname, o.hasSuffix, m2.suffix, m2.firstname, o.hasMiddleName, m2.middlename, m2.membernumber, 
	i.payProfileID
order by o.orgcode, membername, i.dateDue

-- non-posted batches with depositdate > 7 days ago
select o.orgcode, o.orgname, o.accountingEmail, b.batchID, bs.status, b.batchName, 
	isnull(actual.actualAmount,0) as actualAmount, b.depositDate, mp.profileName
from dbo.tr_batches as b
inner join dbo.tr_batchStatuses as bs on bs.statusID = b.statusID
inner join dbo.organizations as o on o.orgID = b.orgID
left outer join dbo.mp_profiles as mp on mp.profileID = b.payProfileID
outer apply dbo.fn_tr_getBatchActual(b.batchID) as actual
where bs.status <> 'Posted'
and isnull(b.batchCode,'') <> 'PENDINGPAYMENTS'
and b.depositDate < dateadd(d,-7,getdate())
and isnull(o.accountingEmail,'') <> ''
order by o.orgcode, b.batchName

GO

INSERT INTO dbo.scheduledTasks (name, nextRunDate, interval, intervalTypeID, taskCFC, timeoutMinutes, disabled, siteid)
values ('Accounting Issues Report', '11/24/2013 4:15AM', 7, 1, 'model.scheduledTasks.tasks.AccountingIssuesReport', 10, 1, 1)
GO
