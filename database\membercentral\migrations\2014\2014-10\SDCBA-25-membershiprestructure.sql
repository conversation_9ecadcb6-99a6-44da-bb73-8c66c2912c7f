use membercentral
GO

declare @siteID int, @orgID int, @sustainingRateID int, @doConverstion bit, @basicSubscriptionID int

set @doConverstion = 1
set @siteID = dbo.fn_getSiteIDFromSiteCode('SDCBA')
set @orgID = dbo.fn_getOrgIDFromOrgCode('SDCBA')

select top 1 @sustainingRateID = subs.GLAccountID
from sub_types t
inner join sub_subscriptions subs
	on subs.typeID = t.typeID
	and t.siteID = @siteID
	and t.typeName = 'sustaining membership'


-- people with (all) sub, but no basic or sustaining
select activemember.membernumber, ss.*
from sub_types t
inner join sub_subscriptions subs
	on subs.typeID = t.typeID
	and t.siteID = @siteID
	and t.typeName = 'membership'
	and subs.subscriptionName like '% (all)'
inner join sub_subscribers ss
	on ss.subscriptionID = subs.subscriptionID
	and ss.statusID <> 8
inner join ams_members m
	on m.memberID = ss.memberID
inner join ams_members activeMember
	on activeMember.memberID = m.activeMemberID
inner join sub_subscribers rootss
	on rootss.subscriberID = ss.rootsubscriberID
left outer join sub_subscribers rootAOss
	inner join sub_subscriptions addonSubs
		on addonSubs.subscriptionID = rootAoss.subscriptionID
		and ( addonSubs.subscriptionname like '% basic' or addonSubs.subscriptionname like '% (sustaining)')
on rootAOss.rootSubscriberID = rootss.subscriberID
and rootAOss.statusID = rootss.statusID
where rootAOss.subscriberID is null

-- subscription groups used by permissions
select * 
from sub_types t
inner join sub_subscriptions subs
	on subs.typeID = t.typeID
	and t.siteID = @siteID
	and t.typeName = 'membership'
	and  ( subs.subscriptionname like '%(Sustaining)' or subs.subscriptionname like '%Basic')
inner join ams_groups g
	on g.orgID = @orgID
	and g.groupcode like 'Sub%[_]' + cast(subs.subscriptionID as varchar(10)) + '[_]Tracking'
inner join cms_siteResourceRights srr
	on srr.groupID = g.groupID

-- subscription groups used by group sets
select * 
from sub_types t
inner join sub_subscriptions subs
	on subs.typeID = t.typeID
	and t.siteID = @siteID
	and t.typeName = 'membership'
	and  ( subs.subscriptionname like '%(Sustaining)' or subs.subscriptionname like '%Basic')
inner join ams_groups g
	on g.orgID = @orgID
	and g.groupcode like 'Sub%[_]' + cast(subs.subscriptionID as varchar(10)) + '[_]Tracking'
inner join dbo.ams_memberGroupSetGroups gsg
	on gsg.groupID = g.groupID

-- subscription groups used by fieldsets
select * 
from sub_types t
inner join sub_subscriptions subs
	on subs.typeID = t.typeID
	and t.siteID = @siteID
	and t.typeName = 'membership'
	and  ( subs.subscriptionname like '%(Sustaining)' or subs.subscriptionname like '%Basic')
inner join ams_groups g
	on g.orgID = @orgID
	and g.groupcode like 'Sub%[_]' + cast(subs.subscriptionID as varchar(10)) + '[_]Tracking'
inner join dbo.ams_memberFields mf
	on mf.dbfield = 'grp_' + cast(g.groupID as varchar(10))

declare @subConditions TABLE (autoID int IDENTITY(1,1) PRIMARY KEY, subscriptionID int, conditionID int)

insert into @subConditions (subscriptionID, conditionID)
select subs.subscriptionID, vgc.conditionID
from sub_types t
inner join sub_subscriptions subs
	on subs.typeID = t.typeID
	and t.siteID = @siteID
	and t.typeName = 'membership'
	and  ( subs.subscriptionname like '%(Sustaining)' or subs.subscriptionname like '%Basic')
inner join ams_groups g
	on g.orgID = @orgID
	and g.groupcode like 'Sub%[_]' + cast(subs.subscriptionID as varchar(10)) + '[_]Tracking'
inner join dbo.ams_virtualGroupConditions vgc
	on vgc.orgID = g.orgID
	and vgc.fieldcode like 'Sub%[_]' + cast(t.typeID as varchar(10)) + '[_]' + cast(subs.subscriptionID as varchar(10)) + '[_]%'



IF OBJECT_ID('tempdb..#cache_ams_virtualGroupRuleConditions') IS NOT NULL
	DROP TABLE #cache_ams_virtualGroupRuleConditions
CREATE TABLE #cache_ams_virtualGroupRuleConditions (ruleID int, conditionID int, uid uniqueidentifier)

IF OBJECT_ID('tempdb..#cache_ams_virtualGroupRuleInfo') IS NOT NULL
	DROP TABLE #cache_ams_virtualGroupRuleInfo
CREATE TABLE #cache_ams_virtualGroupRuleInfo (ruleID int PRIMARY KEY, conditionCount int, usesOR bit, usesAND bit, usesExclude bit)


insert into #cache_ams_virtualGroupRuleInfo (ruleID, conditionCount, usesOR,usesAND,usesExclude)
select 
	r.ruleID, 
	min(r.ruleXML.value('count(//condition)','int')) as conditionCount,
	max(cast (r.ruleXML.exist('//conditionset[@op = "OR"]') as integer)) as usesOR,
	max(cast (r.ruleXML.exist('//conditionset[@op = "AND"]') as integer)) as usesAND,
	max(cast (r.ruleXML.exist('//conditionset[@act = "exclude"]') as integer)) as usesExclude
from dbo.ams_virtualGroupRules as r
cross apply r.ruleXML.nodes('//condition') as T(C)

where r.orgID = @orgID
	and r.isActive = 1

group by r.orgID, r.ruleID
order by r.orgID, r.ruleID



insert into #cache_ams_virtualGroupRuleConditions (ruleID, conditionID, uid)
select r.ruleID, vc.conditionID, vc.uid
from dbo.ams_virtualGroupRules as r
inner join #cache_ams_virtualGroupRuleInfo vgri
	on vgri.ruleID = r.ruleID
cross apply r.ruleXML.nodes('//condition') as T(C)
inner join dbo.ams_virtualGroupConditions vc
	on vc.uid = C.value('@id', 'uniqueidentifier')
inner join @subConditions sc
	on sc.conditionID = vc.conditionID
order by r.orgID, r.ruleID, vc.conditionID

delete ri 
from #cache_ams_virtualGroupRuleInfo ri
left outer join #cache_ams_virtualGroupRuleConditions rc
	on rc.ruleID = ri.ruleID
where rc.ruleID is null

-- group rules that depend on membership subscriptions
select *
from dbo.ams_virtualGroupRules as r
inner join #cache_ams_virtualGroupRuleInfo vgri
	on vgri.ruleID = r.ruleID
	and r.ruleTypeID = 1

-- saved reports that depend on membership subscriptions
select *
from dbo.ams_virtualGroupRules as r
inner join #cache_ams_virtualGroupRuleInfo vgri
	on vgri.ruleID = r.ruleID
inner join dbo.rpt_SavedReports srep
	on srep.ruleID = r.ruleID

-- email blasts that depend on membership subscriptions
select *
from dbo.ams_virtualGroupRules as r
inner join #cache_ams_virtualGroupRuleInfo vgri
	on vgri.ruleID = r.ruleID
inner join dbo.email_emailBlasts eb
	on eb.ruleID = r.ruleID

IF OBJECT_ID('tempdb..#cache_ams_virtualGroupRuleConditions') IS NOT NULL
	DROP TABLE #cache_ams_virtualGroupRuleConditions

IF OBJECT_ID('tempdb..#cache_ams_virtualGroupRuleInfo') IS NOT NULL
	DROP TABLE #cache_ams_virtualGroupRuleInfo




declare @rateToScheduleMapping TABLE (ratename varchar(200), newScheduleName varchar(200))
insert into @rateToScheduleMapping (ratename, newschedulename) values ('Affiliate Member','Affiliate Member Rate')
insert into @rateToScheduleMapping (ratename, newschedulename) values ('CA Attorney','CA Membership Rate')
insert into @rateToScheduleMapping (ratename, newschedulename) values ('Legal Community','Legal Community Membership Rate')
insert into @rateToScheduleMapping (ratename, newschedulename) values ('Renewal Affiliate Member','Affiliate Member Rate')
insert into @rateToScheduleMapping (ratename, newschedulename) values ('Renewal CA Attorney','CA Membership Rate')
insert into @rateToScheduleMapping (ratename, newschedulename) values ('Renewal Legal Community','Legal Community Membership Rate')


select rs.schedulename, r.ratename, newrs.schedulename, 
newRatename = case rs.scheduleName
	when 'Sustaining Benefactor Schedule' then r.ratename + ' Sustaining Benefactor'
	when 'Sustaining Friend Schedule' then r.ratename + ' Sustaining Friend'
	when 'Sustaining Patron Member' then r.ratename + ' Sustaining Patron'
end
from sub_rateSchedules rs
inner join sub_rates r
	on r.scheduleID = rs.scheduleID
	and rs.siteID = @siteID
	and rs.scheduleName in (
		'Sustaining Benefactor Schedule',
		'Sustaining Friend Schedule',
		'Sustaining Patron Member')
inner join @rateToScheduleMapping rsm
	on rsm.ratename = r.ratename
inner join sub_rateSchedules newrs
	on newrs.scheduleName = rsm.newScheduleName
	and newrs.siteID = @siteID
order by rs.schedulename, newrs.schedulename, r.ratename


if @doConverstion = 1
BEGIN

	
	select ss.*
	into datatransfer.dbo.sdcbaSubscribers
	from sub_types t
	inner join sub_subscriptions subs
		on subs.typeID = t.typeID
		and t.siteID = @siteID
	inner join sub_subscribers ss
		on ss.subscriptionID = subs.subscriptionID

	BEGIN TRAN


-- deal with subs that have both basic and sustaining, or two of the same one .. move children of lower subscriberID to use parent as root
update dss set subscriberPath = '0001.0001', rootsubscriberID = ss2.subscriberID, parentSubscriberID = ss2.subscriberID
from sub_subscribers ss2
inner join (
	select ss.subscriberID, ss.memberID,min(childss.subscriberID) as minsubscriberID --,dbo.sortedIntList(childss.subscriberID), dbo.pipelist(childsubs.subscriptionname),  count(*)
	from sub_types t
	inner join sub_subscriptions subs
		on subs.typeID = t.typeID
		and t.siteID = 2
		and t.typeName = 'membership'
	inner join sub_subscribers ss
		on ss.subscriptionID = subs.subscriptionID
		and ss.parentSubscriberID is null
	inner join sub_subscribers childss
		on childss.parentSubscriberID = ss.subscriberID
		and childss.rootSubscriberID = ss.rootSubscriberID
	inner join sub_subscriptions childsubs
		on childsubs.subscriptionID = childss.subscriptionID
		and childsubs.typeID = t.typeID
	group by ss.subscriberID, ss.memberID
	having count(*) >1
) temp on temp.minsubscriberID = ss2.subscriberID
inner join sub_subscribers dss
	on dss.parentSubscriberID = ss2.subscriberID


-- deal with subs that have both basic and sustaining, or two of the same one .. move lower subscriberID to separate root
update ss2 set subscriberPath = '0001', rootsubscriberID = ss2.subscriberID, parentSubscriberID =null
from sub_subscribers ss2
inner join (
	select ss.subscriberID, ss.memberID,min(childss.subscriberID) as minsubscriberID --,dbo.sortedIntList(childss.subscriberID), dbo.pipelist(childsubs.subscriptionname),  count(*)
	from sub_types t
	inner join sub_subscriptions subs
		on subs.typeID = t.typeID
		and t.siteID = 2
		and t.typeName = 'membership'
	inner join sub_subscribers ss
		on ss.subscriptionID = subs.subscriptionID
		and ss.parentSubscriberID is null
	inner join sub_subscribers childss
		on childss.parentSubscriberID = ss.subscriberID
		and childss.rootSubscriberID = ss.rootSubscriberID
	inner join sub_subscriptions childsubs
		on childsubs.subscriptionID = childss.subscriptionID
		and childsubs.typeID = t.typeID
	group by ss.subscriberID, ss.memberID
	having count(*) >1
) temp on temp.minsubscriberID = ss2.subscriberID


-- Update sustaining rate GLs to Sustaining GL Account
-- Move sustaining rates to basic rate structures

update r set 
	ratename = case rs.scheduleName
		when 'Sustaining Benefactor Schedule' then r.ratename + ' Sustaining Benefactor'
		when 'Sustaining Friend Schedule' then r.ratename + ' Sustaining Friend'
		when 'Sustaining Patron Member' then r.ratename + ' Sustaining Benefactor'
	end, 
	GLAccountID = @sustainingRateID,
	scheduleID = newrs.scheduleID
from sub_rateSchedules rs
inner join sub_rates r
	on r.scheduleID = rs.scheduleID
	and rs.siteID = @siteID
	and rs.scheduleName in (
		'Sustaining Benefactor Schedule',
		'Sustaining Friend Schedule',
		'Sustaining Patron Member')
inner join @rateToScheduleMapping rsm
	on rsm.ratename = r.ratename
inner join sub_rateSchedules newrs
	on newrs.scheduleName = rsm.newScheduleName
	and newrs.siteID = @siteID
IF @@ERROR <> 0 GOTO on_error

-- Change sustaining component subscribers to basic 
update ss set
	subscriptionID = aosubs.subscriptionID,
	parentSubscriberID = ss.rootSubscriberID
from sub_types t
inner join sub_subscriptions subs
	on subs.typeID = t.typeID
	and t.siteID = @siteID
	and t.typeName = 'sustaining membership'
inner join sub_subscribers ss
	on ss.subscriptionID = subs.subscriptionID
inner join sub_subscribers rootss
	on rootss.subscriberID = ss.rootsubscriberID
inner join sub_subscriptions rootsubs
	on rootsubs.subscriptionID = rootss.subscriptionID
inner join sub_addons ao
	on ao.subscriptionID = rootsubs.subscriptionID
inner join sub_sets aoset
	on aoset.setID = ao.childSetID
inner join sub_subscriptionSets ssets
	on ssets.setID = aoset.setID
inner join sub_subscriptions aosubs
	on aosubs.subscriptionID = ssets.subscriptionID
	and aosubs.subscriptionName like '% Basic'
inner join sub_types aotypes
	on aotypes.typeID = aosubs.typeID
	and aotypes.siteID = @siteID
	and aotypes.typeName = 'membership'
IF @@ERROR <> 0 GOTO on_error

-- Move Root addons to use basic subscriberID as parentSubscriptionID
update rootaoss set
	parentSubscriberID = ss.subscriberID
from sub_types t
inner join sub_subscriptions subs
	on subs.typeID = t.typeID
	and t.siteID = @siteID
	and t.typeName = 'membership'
	and subs.subscriptionName like '% basic'
inner join sub_subscribers ss
	on ss.subscriptionID = subs.subscriptionID
inner join sub_subscribers rootss
	on rootss.subscriberID = ss.rootsubscriberID
inner join sub_subscribers rootAOss
	on rootAOss.parentSubscriberID = rootss.subscriberID
	and rootAOss.subscriberID <> ss.subscriberID
inner join sub_subscriptions addonSubs
	on addonSubs.subscriptionID = rootAoss.subscriptionID
	and addonSubs.subscriptionname not like '%(Sustaining)'
IF @@ERROR <> 0 GOTO on_error

-- make sure root sub start date is same or earlier than any addon
update ss2 set
	substartdate = temp.substartdate
from sub_subscribers ss2
inner join (
	select ss.subscriberID, substartdate = min(sstree.substartdate)
	from sub_subscribers ss
	inner join sub_subscribers sstree
		on ss.subscriberID = sstree.rootSubscriberID
		and ss.subscriberID <> sstree.subscriberID
		and sstree.substartdate < ss.substartdate
	inner join sub_subscriptions subs
		on sstree.subscriptionID = subs.subscriptionID
	inner join sub_types t
		on t.typeID = subs.typeID
		and t.siteID = @siteID
	group by ss.subscriberID
) as temp
	on temp.subscriberID = ss2.subscriberID
IF @@ERROR <> 0 GOTO on_error

-- Move entire tree (excluding (all) and (sustaining)) to use basic subscriberID as rootsubscriberID
update rootAOss set
	rootSubscriberID = ss.subscriberID
from sub_types t
inner join sub_subscriptions subs
	on subs.typeID = t.typeID
	and t.siteID = @siteID
	and t.typeName = 'membership'
	and subs.subscriptionName like '% basic'
inner join sub_subscribers ss
	on ss.subscriptionID = subs.subscriptionID
inner join sub_subscribers rootss
	on rootss.subscriberID = ss.rootsubscriberID
inner join sub_subscribers rootAOss
	on rootAOss.rootSubscriberID = rootss.subscriberID
	and rootAOss.subscriberID <> rootss.subscriberID
	and rootAOss.subscriberID <> ss.subscriberID
inner join sub_subscriptions addonSubs
	on addonSubs.subscriptionID = rootAoss.subscriptionID
	and addonSubs.subscriptionname not like '%(Sustaining)'
IF @@ERROR <> 0 GOTO on_error


-- update ams_emailLog
update el set
	subscriberID = ss.subscriberID
from sub_types t
inner join sub_subscriptions subs
	on subs.typeID = t.typeID
	and t.siteID = @siteID
	and t.typeName = 'membership'
	and subs.subscriptionName like '% basic'
inner join sub_subscribers ss
	on ss.subscriptionID = subs.subscriptionID
inner join sub_subscribers rootss
	on rootss.subscriberID = ss.rootsubscriberID
inner join ams_emailLog el
	on el.subscriberID = rootss.subscriberID

-- update basic subscriberID with start,end,grace, and paymentMethod
-- Promote Basic subscribers to root by changing subPath, rootSubscriberID, parentSubscriberID = null
update ss set
	substartdate = rootss.substartdate, subenddate = rootss.subenddate, graceenddate = rootss.graceenddate, payProfileID = rootss.payProfileID,
	subscriberPath = rootss.subscriberPath, rootsubscriberID = ss.subscriberID, parentSubscriberID = null,
	directLink = rootss.directLink, directLinkCode = rootss.directLinkCode

from sub_types t
inner join sub_subscriptions subs
	on subs.typeID = t.typeID
	and t.siteID = @siteID
	and t.typeName = 'membership'
	and subs.subscriptionName like '% basic'
inner join sub_subscribers ss
	on ss.subscriptionID = subs.subscriptionID
inner join sub_subscribers rootss
	on rootss.subscriberID = ss.rootsubscriberID
IF @@ERROR <> 0 GOTO on_error

-- make (all) and (sustaining) subsscribers standalone
update ss set
	parentSubscriberID = null, rootsubscriberID = ss.subscriberID, subscriberPath = '9999'
from sub_types t
inner join sub_subscriptions subs
	on subs.typeID = t.typeID
	and t.siteID = @siteID
	and t.typeName = 'membership'
	and  ( subs.subscriptionname like '%(Sustaining)' or subs.subscriptionname like '%(All)')
inner join sub_subscribers ss
	on ss.subscriptionID = subs.subscriptionID
left outer join (
	select ss.subscriberID
	from sub_types t
	inner join sub_subscriptions subs
		on subs.typeID = t.typeID
		and t.siteID = @siteID
		and t.typeName = 'membership'
		and subs.subscriptionName like '% (all)'
	inner join sub_subscribers ss
		on ss.subscriptionID = subs.subscriptionID
		--and ss.statusID <> 8
	inner join sub_subscribers rootss
		on rootss.subscriberID = ss.rootsubscriberID
	left outer join sub_subscribers rootAOss
		inner join sub_subscriptions addonSubs
			on addonSubs.subscriptionID = rootAoss.subscriptionID
			and ( addonSubs.subscriptionname like '% basic' or addonSubs.subscriptionname like '% (sustaining)')
	on rootAOss.rootSubscriberID = rootss.subscriberID
	and rootAOss.statusID = rootss.statusID
	where rootAOss.subscriberID is null
) slickSubs
	on slickSubs.subscriberID = ss.subscriberID
where slicksubs.subscriberID is null
IF @@ERROR <> 0 GOTO on_error

-- change subscriptionID of basic to (all)
declare @subNameMapping TABLE (basicName varchar(200), allName varchar(200))

insert into @subNameMapping (basicName, allName) values ('40 Year Bar Membership Basic','40 Year Bar Membership (All)')
insert into @subNameMapping (basicName, allName) values ('Affiliate Membership Basic','Affiliate Membership (All)')
insert into @subNameMapping (basicName, allName) values ('California Attorney Membership Basic','California Attorney Membership (All)')
insert into @subNameMapping (basicName, allName) values ('Honorary Membership Basic','Honorary Membership (All)')
insert into @subNameMapping (basicName, allName) values ('Legal Community Membership Basic','Legal Community Membership (All)')


--select ss.subscriberID, snm. *
update ss set
	subscriptionID = allsubs.subscriptionID
from sub_types t
inner join sub_subscriptions basicsubs
	on basicsubs.typeID = t.typeID
	and t.siteID = @siteID
	and t.typeName = 'membership'
inner join @subNameMapping snm
	on snm.basicName = basicsubs.subscriptionName
inner join sub_subscriptions allsubs
	on allsubs.typeID = t.typeID
	and t.siteID = @siteID
	and allsubs.subscriptionName = snm.allName
inner join sub_subscribers ss
	on ss.subscriptionID = basicsubs.subscriptionID
IF @@ERROR <> 0 GOTO on_error

IF @@TRANCOUNT > 0 COMMIT TRAN


END

-- normal exit

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
GO