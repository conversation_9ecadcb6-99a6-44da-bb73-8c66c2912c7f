CREATE PROC dbo.HalonProtectMsgStatusRecorder_sendMessage
@messageBody XML

AS

SET NOCOUNT ON;
BEGIN TRY

	EXEC dbo.sb_dialogpool_sendMessage N'HalonProtectMsgStatusRecorderInitiatorService', N'HalonProtectMsgStatusRecorderTargetService',
		N'EmailTracking/GeneralXMLEOSContract', N'EmailTracking/GeneralXMLRequest', @messageBody;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO