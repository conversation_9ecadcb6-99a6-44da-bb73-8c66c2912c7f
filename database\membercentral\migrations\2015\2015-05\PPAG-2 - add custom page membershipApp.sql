-- add custom page
declare @siteID int, @sectionID int, @zoneID int, @pgResourceTypeID int, @applicationInstanceID int, @siteResourceID int, 
	@pageID int, @resourceRightID int, @orgID int, @publicGroupID int
		      
select @siteID = siteID from sites where siteCode = 'PPAG'
select @publicGroupID=groupID from ams_groups where orgid=@orgid and groupCode = 'Public'
select @sectionID = sectionID from cms_pageSections where siteID = @siteID and sectionName = 'Root' and sectionCode = 'Root'
SELECT @zoneID = dbo.fn_getZoneID('Main')
SELECT @pgResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedPage')

EXEC dbo.cms_createApplicationInstance @siteid=@siteID, @languageID=1, @sectionID=@sectionID, @applicationTypeID=18,
	@isVisible=1, @pageName='membershipApp', @pageTitle='Membership Application', @pagedesc='Membership Application', @zoneID=@zoneID, @pageTemplateID=null,
	@pageModeID=null, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1,
	@applicationInstanceName='membershipApp', @applicationInstanceDesc='membershipApp', @applicationInstanceID=@applicationInstanceID OUTPUT,
	@siteResourceID=@siteResourceID OUTPUT, @pageID=@pageID OUTPUT
								
INSERT INTO cms_customPages(appInstanceID, customFileName)
VALUES(@applicationInstanceID,'membershipApp')

-- Give rights to the public group
EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1, @functionID=4, @roleID=null, 
	@groupID=@publicGroupID, @memberID=null, @inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, @resourceRightID=@resourceRightID OUTPUT
GO



