USE [seminarWeb]
GO
ALTER PROC [dbo].[general_exportCreditReport]
@sponsorID int,
@startdate datetime,
@enddate datetime,
@filename varchar(400)

AS

SET NOCOUNT ON

-- set startdate to 00:00:00 of startdate, 00:00:00 of enddate
SELECT @startdate = DATEADD(dd, DATEDIFF(dd,0,@startdate), 0)
SELECT @enddate = DATEADD(dd, DATEDIFF(dd,0,dateadd(dd,1,@enddate)), 0)

-- drop if exists
IF OBJECT_ID('tempdb..#tmpSWEvalResults') IS NOT NULL 
	DROP TABLE #tmpSWEvalResults
IF OBJECT_ID('tempdb..##tmpSWCredit') IS NOT NULL 
	DROP TABLE ##tmpSWCredit

-- credit report for anyone who picked passed in credit
-- SWOD only
-- hardcoded to the generic semweb eval -- formid 117
-- Removing hardcoded questions and mapping values.

select distinct safr.enrollmentID, rd.questionID, rd.optionID, 
	case 
	when optionXID=33 then 1
	when optionXID=34 then 2
	when optionXID=35 then 3
	when optionXID=36 then 4
	when optionXID=37 then 5
	when optionXID=39 then 1
	when optionXID=40 then 2
	when optionXID=41 then 3
	when optionXID=42 then 4
	when optionXID=43 then 5
	else 0
	end as matrixVal,
	rd.responseText
into #tmpSWEvalResults
from seminarweb.dbo.tblSeminarsAndFormResponses as safr
inner join seminarweb.dbo.tblenrollments as e on e.enrollmentid = safr.enrollmentid
	and e.passed = 1 
	and e.isactive = 1
	and e.datecompleted between @startdate and @enddate
inner join seminarweb.dbo.tblSeminarsSWOD as sswod on sswod.seminarID = e.seminarID
inner join seminarweb.dbo.tblenrollmentsandcredit as eac on eac.enrollmentid = e.enrollmentid
	and eac.earnedCertificate = 1
inner join seminarweb.dbo.tblSeminarsAndCredit as sac on sac.seminarCreditID = eac.seminarCreditID
inner join seminarweb.dbo.tblCreditSponsorsAndAuthorities as csa on csa.csalinkid = sac.csalinkid
	and csa.sponsorID = @sponsorID
inner join formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID
	and r.formID = 117
	and r.isActive = 1
inner join formbuilder.dbo.tblResponseDetails as rd on rd.responseID = safr.responseID

-- map to root optionid
-- The options may have changed causing a new option id.
;WITH CTE 
AS	
(
		SELECT x.optionID, x.formerOptionID, x.optionID as topID
		FROM formBuilder.dbo.tblOptions x
		INNER JOIN #tmpSWEvalResults t on t.optionID = x.optionID
	UNION ALL
		SELECT s.optionID, s.formerOptionID, CTE.topID
		FROM formBuilder.dbo.tblOptions as s
		JOIN CTE ON s.optionID = CTE.formerOptionID
)
update t
	set t.optionID = r.optionid
from CTE r
inner join #tmpSWEvalResults t on t.optionID = r.topID
where r.formerOptionID is null

-- map to root questionid
-- The questions may have changed causing a new question id.
;WITH CTE 
AS	
(
		SELECT x.questionID, x.formerQuestionID, x.questionID as topID
		FROM formBuilder.dbo.tblQuestions x
		INNER JOIN #tmpSWEvalResults t on t.questionID = x.questionID
	UNION ALL
		SELECT s.questionID, s.formerQuestionID, CTE.topID
		FROM formBuilder.dbo.tblQuestions as s
		JOIN CTE ON s.questionID = CTE.formerQuestionID
)
update t
	set t.questionID = r.questionID
from CTE r
inner join #tmpSWEvalResults t on t.questionID = r.topID
where r.formerQuestionID is null and r.questionID <> r.topID


select sem.seminarID as [Seminar ID],
	dbo.fn_csvSafeString(sem.seminarname) as [Seminar Name], 
	dbo.fn_csvSafeString(sac.courseApproval) as [Course Approval Num], 
	dbo.fn_csvSafeString(eac.idnumber) as [ID Number],
	dbo.fn_csvSafeString(d.lastname) as [Last Name],
	dbo.fn_csvSafeString(d.firstname) as [First Name],
	dbo.fn_csvSafeString(isnull(o.address1,d.billingAddress)) as [Address Line 1],
	dbo.fn_csvSafeString(isnull(o.address2,d.billingAddress2)) as [Address Line 2],
	dbo.fn_csvSafeString(isnull(o.address3,d.billingAddress3)) as [Address Line 3],
	dbo.fn_csvSafeString(isnull(o.city,d.billingCity)) as City,
	dbo.fn_csvSafeString(isnull(o.state,d.billingState)) as State,
	dbo.fn_csvSafeString(isnull(o.zipcode,d.billingZIP)) as ZIP,
	dbo.fn_csvSafeString(d.phone) as Phone,
	dbo.fn_csvSafeString(d.fax) as Fax,
	dbo.fn_csvSafeString(d.email) as [E-Mail],
	convert(varchar(10),e.dateenrolled,101) as [Date Enrolled],
	convert(varchar(10),e.datecompleted,101) as [Date Completed],
	eac.finaltimespent as [Minutes Spent in Course],
	q1r1.matrixVal as Agenda,
	q1r2.matrixVal as [Overall Quality Rating],
	q1r3.matrixVal as [Delivery Rating],
	q1r4.matrixVal as [Written Materials],
	q1r5.matrixVal as [Instructors],
	q2r1.matrixVal as [Ease of Use], 
	q2r2.matrixVal as [Format and Function],
	q2r3.matrixVal as [Ability to Ask Questions],
	q2r4.matrixVal as [Technical Difficulties],
	dbo.fn_csvSafeString(q3.responseText) as Comments
INTO ##tmpSWCredit
from dbo.tblenrollments as e
inner join dbo.tblParticipants p on e.participantID = p.participantID
inner join dbo.tblSeminars as sem on sem.seminarID = e.seminarID
inner join dbo.tblSeminarsSWOD as sswod on sswod.seminarID = sem.seminarID
inner join dbo.tblenrollmentsandcredit as eac on eac.enrollmentid = e.enrollmentid
inner join dbo.tblSeminarsAndCredit as sac on eac.seminarCreditID = sac.seminarCreditID
inner join dbo.tblCreditSponsorsAndAuthorities as csa on csa.csalinkid = sac.csalinkid
inner join dbo.tblCreditSponsors as cs on cs.sponsorid = csa.sponsorid and cs.sponsorID = @sponsorID
inner join dbo.tblCreditAuthorities as ca on ca.authorityid = csa.authorityid
inner join dbo.tblUsers as u on u.userid = e.userid
inner join trialsmith.dbo.depomemberdata as d on d.depomemberdataid = u.depomemberdataid
left outer join trialsmith.dbo.orgmemberdata as o on o.depomemberdataid = d.depomemberdataid and o.orgcode = cs.orgcode
left outer join #tmpSWEvalResults as q1r1 on q1r1.enrollmentid = e.enrollmentid and q1r1.questionID=2035 and q1r1.optionid=6800
left outer join #tmpSWEvalResults as q1r2 on q1r2.enrollmentid = e.enrollmentid and q1r2.questionID=2035 and q1r2.optionid=6801
left outer join #tmpSWEvalResults as q1r3 on q1r3.enrollmentid = e.enrollmentid and q1r3.questionID=2035 and q1r3.optionid=6802
left outer join #tmpSWEvalResults as q1r4 on q1r4.enrollmentid = e.enrollmentid and q1r4.questionID=2035 and q1r4.optionid=6803
left outer join #tmpSWEvalResults as q1r5 on q1r5.enrollmentid = e.enrollmentid and q1r5.questionID=2035 and q1r5.optionid=6804
left outer join #tmpSWEvalResults as q2r1 on q2r1.enrollmentid = e.enrollmentid and q2r1.questionID=2036 and q2r1.optionid=6805
left outer join #tmpSWEvalResults as q2r2 on q2r2.enrollmentid = e.enrollmentid and q2r2.questionID=2036 and q2r2.optionid=6806
left outer join #tmpSWEvalResults as q2r3 on q2r3.enrollmentid = e.enrollmentid and q2r3.questionID=2036 and q2r3.optionid=6807
left outer join #tmpSWEvalResults as q2r4 on q2r4.enrollmentid = e.enrollmentid and q2r4.questionID=2036 and q2r4.optionid=6808
left outer join #tmpSWEvalResults as q3 on q3.enrollmentid = e.enrollmentid and q3.questionID=2037
where e.passed = 1 and eac.earnedCertificate = 1
and e.datecompleted between @startdate and @enddate
order by e.datecompleted

-- export data
DECLARE @cmd varchar(6000)
declare @tmpBCP TABLE (theoutput varchar(max))
set @cmd = 'bcp ##tmpSWCredit out ' + @filename + ' -c -t, -T -S' + CAST(serverproperty('servername') as varchar(20))
insert into @tmpBCP (theoutput)
exec master..xp_cmdshell @cmd	

-- return count of records
SELECT count(*) AS returnCount
FROM ##tmpSWCredit

-- get fields returned
EXEC tempdb.dbo.SP_COLUMNS ##tmpSWCredit

-- drop temp table
IF OBJECT_ID('tempdb..#tmpSWEvalResults') IS NOT NULL 
	DROP TABLE #tmpSWEvalResults
IF OBJECT_ID('tempdb..##tmpSWCredit') IS NOT NULL 
	DROP TABLE ##tmpSWCredit

GO
