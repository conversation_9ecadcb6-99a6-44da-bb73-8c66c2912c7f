use customApps
GO
declare @udid int

insert into dbo.schedTask_memberJoinDates (siteCode, joinDateFieldName, rejoinDateFieldName, droppedDateFieldName, paidThruDateFieldName, lastSuccessDate, lastErrorCode, isActive)
values ('AL', 'Join Date', 'Rejoin Date', 'Drop Date', 'Paid Thru Date', '1/1/1980', 0, 1)
	select @udid = SCOPE_IDENTITY()

insert into dbo.schedTask_memberJoinDateSubTypes (memberJoinDateUDID, subscriptionTypeUID)
select @udid, [uid]
from membercentral.dbo.sub_types 
where siteID = 114
and status = 'A'
and typeName = 'Membership'

DECLARE @errorcode int
EXEC dbo.job_memberJoinDates @udid, @errorcode OUTPUT
GO




