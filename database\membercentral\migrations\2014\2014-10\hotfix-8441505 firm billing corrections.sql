use platformQueue;
GO

ALTER PROC [dbo].[job_firmSubStatements_grabForProcessing]
@serverID int,
@batchSize int,
@filePath varchar(400)

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @subTypeOrdering TABLE (itemUID uniqueidentifier, typeID int, typeSortOrder int)

	declare @statusReady int, @statusGrabbed int
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'FirmSubStatements'
		and qs.queueStatus = 'readyToProcess'
	select @statusGrabbed = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'FirmSubStatements'
		and qs.queueStatus = 'grabbedForProcessing'

	declare @jobUID uniqueidentifier
	set @jobUID = NEWID()


	-- dequeue in order of dateAdded. get @batchsize firms
	IF OBJECT_ID('tempdb..#tmpTblQueueItems_firmbilling') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_firmbilling
	CREATE TABLE #tmpTblQueueItems_firmbilling (itemUID uniqueidentifier, itemGroupUID uniqueidentifier, 
		jobUID uniqueidentifier, recordedByMemberID int, siteID int, memberID int, memberNumber varchar(50), 
		firstname varchar(75), lastname varchar(75), company varchar(200), address1 varchar(100), 
		address2 varchar(100), address3 varchar(100), city varchar(35), stateProv varchar(4),
		postalCode varchar(25), country varchar(100), xmlConfigParam varchar(max), xmlFirms varchar(max))

	update qi WITH (UPDLOCK, READPAST)
	set qi.queueStatusID = @statusGrabbed,
		qi.dateUpdated = getdate(),
		qi.jobUID = @jobUID,
		qi.jobDateStarted = getdate(),
		qi.jobServerID = @serverID
		OUTPUT inserted.itemUID, null, inserted.jobUID, null, null, null, null, null, null, null, 
			null, null, null, null, null, null, null, null, null
		INTO #tmpTblQueueItems_firmbilling
	from platformQueue.dbo.tblQueueItems as qi
	inner join (
		select top(@BatchSize) qi2.itemUID 
		from platformQueue.dbo.tblQueueItems as qi2
		where qi2.queueStatusID = @statusReady
		order by qi2.dateAdded, qi2.itemUID
		) as batch on batch.itemUID = qi.itemUID
	where qi.queueStatusID = @statusReady


	-- get memberID, itemGroupUID, siteID, recordedByMemberID from item data
	-- outer apply is required in cases where there is no match so the itemGroupUID gets updated
	update tmp
	set tmp.memberID = idata.memberID,
		tmp.itemGroupUID = idata.itemGroupUID,
		tmp.recordedByMemberID = idata.recordedByMemberID,
		tmp.siteID = idata.siteID
	from #tmpTblQueueItems_firmbilling as tmp
	outer apply (
		select min(cast(dataKey as int)) as memberID, min(cast(itemGroupUID as varchar(60))) as itemGroupUID, 
			min(recordedByMemberID) as recordedByMemberID, min(siteID) as siteID
		from platformQueue.dbo.tblQueueItemData as qid
		inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
			and dc.columnname in ('FirmChildSub','FirmChildNoSub')
		where qid.itemUID = tmp.itemUID
	) as idata


	-- get address info for firm accounts
	update tmp 
	set	tmp.memberNumber = m.memberNumber, 
		tmp.firstname = m.firstname, 
		tmp.lastname = m.lastname, 
		tmp.company = m.company, 
		tmp.address1 = ma.address1, 
		tmp.address2 = ma.address2, 
		tmp.address3 = ma.address3, 
		tmp.city = ma.city, 
		tmp.stateProv = s.code, 
		tmp.postalCode = ma.postalCode,
		tmp.country = c.country
	from #tmpTblQueueItems_firmbilling as tmp
	inner join membercentral.dbo.ams_members as m on m.memberID = tmp.memberID
	left outer join membercentral.dbo.ams_memberAddresses as ma on ma.memberID = m.memberID 
		and ma.addressTypeID = m.billingAddressTypeID
	left outer join membercentral.dbo.ams_states as s on s.stateID = ma.stateID
	left outer join membercentral.dbo.ams_countries as c on c.countryID = ma.countryID


	-- get config params for each item. casting to varchar(max) because it speed up final data query return
	update tmp
	set tmp.xmlConfigParam = config.configXML
	from #tmpTblQueueItems_firmbilling as tmp
	cross apply (
		select cast(isnull((
			select reportParam, paramvalue	
			from (
				select datakey as reportParam, cast(columnValueString as varchar(max)) as paramvalue
				from platformQueue.dbo.tblQueueItemData qid
				inner join platformQueue.dbo.tblQueueTypeDataColumns dc on dc.columnID = qid.columnID
					and dc.columnname = 'ConfigParam'
				where qid.itemUID = tmp.itemUID
					union all
				select datakey as reportParam, columnValuetext as paramvalue
				from platformQueue.dbo.tblQueueItemData qid
				inner join platformQueue.dbo.tblQueueTypeDataColumns dc on dc.columnID = qid.columnID
					and dc.columnname = 'ConfigText'
				where qid.itemUID = tmp.itemUID
			) as tmp
			for xml path('param'), root('params'), type
		),'<params/>') as varchar(max)) as configXML
	) as config


	-- get all firm members
	IF OBJECT_ID('tempdb..#qryAllFirmMembers') IS NOT NULL 
		DROP TABLE #qryAllFirmMembers
	CREATE TABLE #qryAllFirmMembers (itemUID uniqueidentifier, childMemberID int, childMemberNumber varchar(50), 
		prefix varchar(50), firstname varchar(75), lastname varchar(75), company varchar(200), suffix varchar(50),
		address1 varchar(100), address2 varchar(100), address3 varchar(100), city varchar(35), stateProv varchar(4),
				postalCode varchar(25), country varchar(100)
	)
	insert into #qryAllFirmMembers (itemUID, childMemberID)
	select distinct tmp.itemUID, qid.columnValueInteger
	from #tmpTblQueueItems_firmbilling as tmp
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = tmp.itemUID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
		and dc.columnname = 'FirmChildNoSub'
		union
	select distinct tmp.itemUID, m.activeMemberID
	from #tmpTblQueueItems_firmbilling as tmp
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = tmp.itemUID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
		and dc.columnname = 'FirmChildSub'
	inner join membercentral.dbo.sub_subscribers as ss on ss.subscriberID = qid.columnValueInteger
	inner join membercentral.dbo.ams_members as m on m.memberID = ss.memberID

	update fm 
	set childMemberNumber = m.MemberNumber, 
		prefix = m.prefix,
		firstname = m.firstname, 
		lastname = m.lastname, 
		company = m.company,
		suffix = m.suffix,
		address1 = ma.address1, 
		address2 = ma.address2, 
		address3 = ma.address3, 
		city = ma.city, 
		stateProv = s.code, 
		postalCode = ma.postalCode,
		country = c.country
	from #qryAllFirmMembers as fm
	inner join membercentral.dbo.ams_members as m on m.memberID = fm.childMemberID
	left outer join membercentral.dbo.ams_memberAddresses as ma on ma.memberID = m.memberID 
		and ma.addressTypeID = m.billingAddressTypeID
	left outer join membercentral.dbo.ams_states as s on s.stateID = ma.stateID
	left outer join membercentral.dbo.ams_countries as c on c.countryID = ma.countryID


	-- get all subs
	IF OBJECT_ID('tempdb..#qrySubs') IS NOT NULL 
		DROP TABLE #qrySubs
	CREATE TABLE #qrySubs (itemUID uniqueidentifier, subscriberID int, memberID int, subscriptionID int, typeID int,
		typeName varchar(100), subscriptionName varchar(300), rateName varchar(200), frequencyName varchar(50),
		[status] varchar(1), statusName varchar(50), subStartDate datetime, subEndDate datetime, graceEndDate datetime,
		parentSubscriberID int, rootSubscriberID int, lastPrice money, rfid int, thePath varchar(max), 
		subAmount money, subAmountDue money, subAmountPaid money, subscriberPath varchar(200), saleTransactionID int, 
		glaccountID int, invoiceContentVersionID int, invoiceProfileID int, invoiceProfileImageExt varchar(5))

	insert into #qrySubs (itemUID, subscriberID, memberID, subscriptionID, typeID, typeName, subscriptionName, rateName, 
		frequencyName, [status], statusName, subStartDate, subEndDate, graceEndDate, parentSubscriberID, rfid, 
		rootSubscriberID, lastPrice, subscriberPath, glaccountID)
	select tmp.itemUID, ss.subscriberID, m.activeMemberID, ss.subscriptionID, t.typeID, t.typeName, sub.subscriptionName, 
		r.rateName, f.frequencyName, st.statusCode, st.statusName, ss.subStartDate, ss.subEndDate, ss.graceEndDate, 
		ss.parentSubscriberID, ss.rfid, ss.rootSubscriberID, ss.lastprice, ss.subscriberPath, ss.glaccountID
	from #tmpTblQueueItems_firmbilling as tmp
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = tmp.itemUID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
		and dc.columnname = 'FirmChildSub'
	inner join membercentral.dbo.sub_subscribers as rootss on rootss.subscriberID = qid.columnValueInteger
	inner join membercentral.dbo.sub_subscribers as ss on rootss.subscriberID = ss.rootSubscriberID
	inner join membercentral.dbo.sub_subscriptions as sub on sub.subscriptionID = ss.subscriptionID
	inner join membercentral.dbo.sub_types as t on sub.typeiD = t.typeID
	inner join membercentral.dbo.ams_members as m on ss.memberID = m.memberID
	inner join membercentral.dbo.sub_statuses as st on st.statusID = ss.statusID 
		and st.statuscode in ('A','I','O','P')
	inner join membercentral.dbo.sub_rateFrequencies as rf on rf.rfid = ss.rfid
	inner join membercentral.dbo.sub_rates as r on r.rateID = rf.rateID 
	inner join membercentral.dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID



	update qs 
	set saleTransactionID = t.transactionID,
		subAmount = ts.cache_amountAfterAdjustment,
		subAmountDue = ts.cache_amountAfterAdjustment - ts.cache_activePaymentAllocatedAmount,
		subAmountPaid = ts.cache_activePaymentAllocatedAmount,
		invoiceContentVersionID = it.messageContentVersionID
	from #qrySubs as qs
	inner join membercentral.dbo.tr_applications as app on app.ItemID = qs.subscriberID 
		and app.applicationTypeID = 17 
		and app.itemType = 'Dues' 
		and app.status = 'A'
	inner join membercentral.dbo.tr_transactions as t on t.transactionID = app.transactionID
	inner join membercentral.dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID
	inner join membercentral.dbo.tr_invoiceTransactions it on it.transactionID = t.transactionID

	-- update amount and amountdue columns for billed subscriptions
	update qs set 
		subAmount = qs.lastprice,
		subAmountDue = qs.lastprice,
		subAmountPaid = 0
	from #qrySubs as qs
	where status = 'O'

	-- set typeSortOrder based on billed total by subscription type
	insert into @subTypeOrdering (itemUID, typeID , typeSortOrder )
	select tmp.itemUID, tmp.typeID, row_number() over (partition by tmp.itemUID order by tmp.subAmountTotal desc, typename desc)
	from (
		select qs.itemUID, qs.typeID, sum(subAmount) as subAmountTotal
		from #qrySubs as qs
		group by qs.itemUID, qs.typeID
	) as tmp


	-- update invoiceProfileID and invoice image (billed subs have no transactions to join against)
	update qs 
	set invoiceProfileID = ip.profileID, 
		invoiceProfileImageExt = ip.imageExt
	from #qrySubs as qs
	inner join membercentral.dbo.tr_glaccounts as gl on gl.glaccountID = qs.glaccountID
	inner join membercentral.dbo.tr_invoiceProfiles ip on ip.profileID = gl.invoiceProfileID


	-- update invoiceContentVersionID for subs with no transactions
	update qs 
	set invoiceContentVersionID = cv.contentVersionID
	from #qrySubs as qs
	inner join membercentral.dbo.sub_types t
		on t.typeID = qs.typeID
	inner join membercentral.dbo.sites s
		on s.siteID = t.siteID
	inner join membercentral.dbo.tr_glaccounts as gl 
		on gl.glaccountID = qs.glaccountID
		and qs.saleTransactionID is null
	inner join membercentral.dbo.cms_content as c 
		on c.contentID = gl.invoiceContentID
	inner join membercentral.dbo.cms_contentLanguages as cl 
		on cl.contentID = c.contentID
		and cl.languageID = s.defaultLanguageID
	inner join membercentral.dbo.cms_contentVersions as cv 
		on cv.contentLanguageID = cl.contentLanguageID
		and cv.isActive = 1

	
	-- get sub tree totals
	IF OBJECT_ID('tempdb..#tblSubTreeTotals') IS NOT NULL 
		DROP TABLE #tblSubTreeTotals
	CREATE TABLE #tblSubTreeTotals (itemUID uniqueidentifier, rootsubscriberID int, tree_subAmount money, 
		tree_subAmountDue money, tree_subAmountPaid money)

	insert into #tblSubTreeTotals (itemUID, rootsubscriberID, tree_subAmount, tree_subAmountDue, tree_subAmountPaid)
	select itemUID, rootSubscriberID, sum(subAmount) as tree_subAmount, sum(subAmountDue) as tree_subAmountDue, sum(subAmountPaid) as tree_subAmountPaid
	from #qrySubs 
	group by itemUID, rootSubscriberID


	-- get sub payments
	IF OBJECT_ID('tempdb..#tblSubPayments') IS NOT NULL 
		DROP TABLE #tblSubPayments
	CREATE TABLE #tblSubPayments (itemUID uniqueidentifier, subscriberID int, rootsubscriberID int, transactionID int, 
		allocatedAmount money, detail varchar(max), depositdate datetime)

	insert into #tblSubPayments (itemUID, subscriberID, rootsubscriberID, transactionID, allocatedAmount, detail, depositdate)
	select s.itemUID, s.subscriberID, s.rootsubscriberID, t.transactionID, st.allocatedAmount, t.detail, b.depositDate
	from #qrySubs as s
	inner join membercentral.dbo.tr_applications as app on app.ItemID = s.subscriberID 
		and app.applicationTypeID = 17 
		and app.itemType = 'Dues' 
		and app.status = 'A'
	CROSS APPLY membercentral.dbo.fn_tr_getAllocatedPaymentsofSale(app.transactionID) AS st
	inner join membercentral.dbo.tr_transactions as t on t.transactionID = st.paymentTransactionID
	inner join membercentral.dbo.tr_batchTransactions as bt on bt.transactionID = t.transactionID
	inner join membercentral.dbo.tr_batches as b on b.batchID = bt.batchID


	-- get sub invoices
	IF OBJECT_ID('tempdb..#tblSubInvoices') IS NOT NULL 
		DROP TABLE #tblSubInvoices
	CREATE TABLE #tblSubInvoices (itemUID uniqueidentifier, subscriberID int, rootsubscriberID int, invoiceID int,
		invoiceCode char(8), datebilled datetime, datedue datetime, statusID int, [status] varchar(50), amount money,
		amountDue money, payProfileDesc varchar(100), invoiceNumber varchar(20))

	insert into #tblSubInvoices (itemUID, subscriberID, rootsubscriberID, invoiceID, invoiceCode, datebilled, datedue,
		statusID, [status], amount, amountDue,payProfileDesc, invoiceNumber)
	select s.itemUID, s.subscriberID, s.rootsubscriberID, i.invoiceID, i.invoiceCode, i.datebilled, i.datedue, ist.statusID, 
		ist.status, sum(st.amount) as amount, sum(st.amountDue) as amountDue,
		payProfileDesc = g.gatewayclass + ' - (' + mpp.detail + ')',
		invoiceNumber = o.orgcode + right(replicate('0',8) + cast(i.invoiceID as varchar(10)),8)
	from #qrySubs as s
	CROSS APPLY membercentral.dbo.fn_sub_subscriberTransactions(s.subscriberID) AS st 
	inner join membercentral.dbo.tr_invoices as i on i.invoiceID = st.invoiceID
	inner join membercentral.dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
	inner join membercentral.dbo.organizations as o on o.orgID = ip.orgID
	inner join membercentral.dbo.tr_invoiceStatuses as ist on ist.statusID = i.statusID and i.statusID <> 1
	left outer join membercentral.dbo.ams_memberPaymentProfiles as mpp
		inner join membercentral.dbo.mp_profiles as mp on mp.profileID = mpp.profileID
		inner join membercentral.dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
		on mpp.payProfileID = i.payProfileID
	group by s.itemUID, s.subscriberID, s.rootsubscriberID, i.invoiceID, i.invoiceCode, i.datebilled, i.datedue, 
		ist.statusID, ist.status, g.gatewayclass, mpp.detail, o.orgcode


	-- populate table var of contentVersions referenced by qrySubs
	declare @invoiceContent table (contentVersionID int PRIMARY KEY, rawcontent varchar(max))
	declare @invoiceContentandSubtrees table (contentVersionID int, rootSubscriberID int, messageNumber int)

	insert into @invoiceContent (contentVersionID, rawcontent)
	select distinct cv.contentVersionID, cv.rawcontent
	from #qrySubs as qs
	inner join membercentral.dbo.cms_contentVersions as cv on cv.contentVersionID = qs.invoiceContentVersionID

	insert into @invoiceContentandSubtrees (contentVersionID, rootSubscriberID, messageNumber)
	select ic.contentVersionID, qs.rootsubscriberID, row_number() over (partition by qs.rootsubscriberID order by min(qs.subscriberPath))
	from #qrySubs as qs
	inner join @invoiceContent as ic on ic.contentVersionID = qs.invoiceContentVersionID
	group by ic.contentVersionID, qs.rootsubscriberID


	-- firms xml. casting to varchar(max) because it speed up final data query return
	update tmp
	set tmp.xmlFirms = firms.firmXML
	from #tmpTblQueueItems_firmbilling as tmp
	cross apply (
		select cast(isnull((		
			select company.memberID as '@firmmemberid',
				company.memberNumber as '@firmmembernumber' ,
				company.firstname as '@firmfirstname' ,
				company.lastname as '@firmlastname' ,
				company.company as '@firmcompany' ,
				company.address1 as '@firmaddress1' ,
				company.address2 as '@firmaddress2' ,
				company.address3 as '@firmaddress3' ,
				company.city as '@firmcity' ,
				company.stateProv as '@firmstate' ,
				company.postalCode as '@firmpostalcode',
				company.country as '@firmcountry',
				( select
					account.childmemberid as '@childmemberID',
					account.childMemberNumber as '@childmembernumber',
					isnull(company.memberID,0) as '@firmmemberid',
					case when exists (select * from #qrySubs as st2 where st2.memberID = account.childmemberid and st2.itemUID = tmp.itemUID) then 1 else 0 end as '@hassubscription',
					account.firstname as '@firstname', 
					account.lastname as '@lastname',
					account.prefix as '@prefix',
					account.suffix as '@suffix',
					account.lastname + ', ' + account.firstname + ' (' + account.childMemberNumber + ')' as '@namestring',
					account.company as '@company',
					account.address1 as '@address1' ,
					account.address2 as '@address2' ,
					account.address3 as '@address3' ,
					account.city as '@city' ,
					account.stateProv as '@state' ,
					account.postalCode as '@postalcode',
					account.country as '@country',

					( select
						subscriptionTree.rootsubscriberid as '@rootsubscriberid',
						case when subscriptionTree.rootsubscriberid is null then null else account.childmemberid end as '@activeMemberID',
						case when subscriptionTree.rootsubscriberid is null then null else account.lastname + ', ' + account.firstname + ' (' + account.childMemberNumber + ')' end as '@namestring',
						subscriptionTree.subscriptionname as '@subscriptionname',
						qst.tree_subAmount as '@subamount_total',
						qst.tree_subAmountPaid as '@subamountpaid_total',
						qst.tree_subAmountDue as '@subamountdue_total',
						CONVERT(VARCHAR(8),subscriptionTree.substartdate, 1) + ' - ' + CONVERT(VARCHAR(8),subscriptionTree.subenddate, 1) as '@subtreedaterange',
						cast(subscriptionTree.invoiceProfileID as varchar(10)) + '.' + subscriptionTree.invoiceProfileImageExt as '@subtreeinvoiceprofileimage',
						( select
							subscriber.subscriberid as '@subscriberID',
							account.childmemberid as '@activeMemberID',
							subscriber.subscriptionid as '@subscriptionid',
							subscriber.typeid as '@typeid',
							subscriber.typename as '@typename',
							subscriber.subscriptionname as '@subscriptionname',
							subscriber.ratename as '@ratename',
							subscriber.frequencyname as '@frequencyname',
							subscriber.status as '@status',
							subscriber.statusname as '@statusname',
							subscriber.substartdate as '@substartdate',
							subscriber.subenddate as '@subenddate',
							CONVERT(VARCHAR(8),subscriber.substartdate, 1) + ' - ' + CONVERT(VARCHAR(8),subscriber.subenddate, 1) as '@subdaterange',
							subscriber.graceenddate as '@graceenddate',
							subscriber.parentsubscriberid as '@parentsubscriberid',
							subscriber.rootsubscriberid as '@rootsubscriberid',
							subscriber.rfid as '@rfid',
							subscriber.thepath as '@thepath',
							subscriber.lastprice as '@lastprice',
							subscriber.subAmount as '@subamount',
							subscriber.subAmountPaid as '@subamountpaid',
							subscriber.subAmountDue as '@subamountdue',
							subscriber.subscriberPath as '@subscriberpath',
							subscriber.invoiceContentVersionID as '@invoicecontentversionid',
							icst.messageNumber as '@invoicecontentfootnotenumber',
							sto.typeSortOrder as '@coversheettypesortorder'
						from #qrySubs as subscriber
						inner join @subTypeOrdering sto
							on sto.itemUID = subscriber.itemUID
							and sto.typeID = subscriber.typeID
						left outer join @invoiceContentandSubtrees as icst on icst.rootsubscriberID = subscriber.rootsubscriberID
							and subscriber.invoiceContentVersionID = icst.contentVersionID
						where subscriber.itemUID = tmp.itemUID
						and subscriber.rootSubscriberID = subscriptionTree.subscriberID
						order by subscriber.subscriberPath
						for xml path('subscriber'), type
						),
						( select
							rootsubscriberID as '@rootsubscriberID',
							invoiceID as '@invoiceid',
							invoiceNumber as '@invoicenumber',
							invoiceCode as '@invoicecode',
							datebilled as '@datebilled',
							datedue as '@datedue',
							statusID as '@statusid',
							[status] as '@status',
							sum(amount) as '@amount',
							sum(amountDue) as '@amountDue',
							isnull(payProfileDesc,'') as '@payprofiledesc'
						from #tblSubInvoices as si
						where si.itemUID = tmp.itemUID
						and si.rootSubscriberID = subscriptionTree.rootsubscriberID
						group by rootsubscriberID, invoiceID, invoiceCode, datebilled, datedue, statusID, [status], payProfileDesc, invoiceNumber
						order by datedue
						for xml path ('invoice'),root('invoices'), type
						),
						( select
							rootsubscriberID as '@rootsubscriberID',
							transactionID as '@transactionID',
							depositdate as '@datebilled',
							detail as '@detail',
							sum(allocatedAmount) as '@allocatedAmount'
						from #tblSubPayments sp
						where sp.itemUID = tmp.itemUID
						and sp.rootSubscriberID = subscriptionTree.rootsubscriberID
						group by rootsubscriberID ,transactionID ,detail,depositdate
						order by depositdate
						for xml path('payment'), root('payments'), type
						),
						( select
							subscriber.rootSubscriberID as '@rootsubscriberID',
							icst.messageNumber as '@invoicecontentfootnotenumber',
							ic.contentversionid as '@contentversionid',
							ic.rawcontent as '@rawcontent'
						from #qrySubs as subscriber
						inner join @invoiceContent as ic on ic.contentVersionID = subscriber.invoiceContentVersionID
							and subscriber.itemUID = tmp.itemUID
							and subscriber.rootSubscriberID = subscriptionTree.subscriberID
						inner join @invoiceContentandSubtrees as icst on icst.rootsubscriberID = subscriber.rootsubscriberID
							and icst.contentVersionID = ic.contentVersionID
						group by subscriber.rootSubscriberID, icst.messageNumber, ic.contentversionid, ic.rawcontent
						order by icst.messageNumber
						for xml path('invoicemessage'), root('invoicemessages'), type
						)
						from #qrySubs as subscriptionTree
						inner join #tblSubTreeTotals as qst 
							on qst.rootSubscriberID = subscriptionTree.rootSubscriberID
							and qst.itemUID = subscriptionTree.itemUID
						where subscriptionTree.itemUID = tmp.itemUID
						and subscriptionTree.memberID = account.childMemberID
						and subscriptionTree.subscriberID = subscriptionTree.rootsubscriberID
						order by subscriptionTree.rootsubscriberid
						for xml path('subscriptiontree'), type
					)
					from #qryAllFirmMembers as account
					where account.itemUID = company.itemUID
					order by account.lastname, account.firstname, account.childMemberID
					for xml path('account'), type
				)
			from #tmpTblQueueItems_firmbilling as company
			where company.itemUID = tmp.itemUID
			order by company.company, company.memberID
			for xml path('company'), root('companies'), type
		),'<companies/>') as varchar(max)) as firmXML
	) as firms


	-- create directories to store xmls
	IF OBJECT_ID('tempdb..#tblDirs') IS NOT NULL 
		DROP TABLE #tblDirs
	select distinct itemGroupUID, membercentral.dbo.fn_createDirectory(@filePath + cast(itemGroupUID as varchar(50))) as cdResult
	into #tblDirs
	from #tmpTblQueueItems_firmbilling
	where membercentral.dbo.fn_DirectoryExists(@filePath + cast(itemGroupUID as varchar(50))) = 0


	-- final data
	select itemUID, itemGroupUID, jobUID, memberNumber, company, xmlConfigParam, 
		membercentral.dbo.fn_writefile(@filePath + cast(itemGroupUID as varchar(50)) + '\' + cast(itemUID as varchar(50)) + '.xml',xmlFirms,1) as writeFileResult
	from #tmpTblQueueItems_firmbilling
	order by itemUID


	IF OBJECT_ID('tempdb..#tblDirs') IS NOT NULL 
		DROP TABLE #tblDirs
	IF OBJECT_ID('tempdb..#tblSubInvoices') IS NOT NULL 
		DROP TABLE #tblSubInvoices
	IF OBJECT_ID('tempdb..#tblSubPayments') IS NOT NULL 
		DROP TABLE #tblSubPayments
	IF OBJECT_ID('tempdb..#tblSubTreeTotals') IS NOT NULL 
		DROP TABLE #tblSubTreeTotals
	IF OBJECT_ID('tempdb..#qrySubs') IS NOT NULL 
		DROP TABLE #qrySubs
	IF OBJECT_ID('tempdb..#qryAllFirmMembers') IS NOT NULL 
		DROP TABLE #qryAllFirmMembers
	IF OBJECT_ID('tempdb..#tmpTblQueueItems_firmbilling') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_firmbilling

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH
GO