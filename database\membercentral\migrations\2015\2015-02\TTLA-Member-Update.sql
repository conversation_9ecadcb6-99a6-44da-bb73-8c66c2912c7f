/*
 In this SQL file the following tasks are performed:

(1) ALTER FUNCTION fn_ams_getConditionFields_member
(2) ALTER FUNCTION fn_ams_getPossibleMemberFields
(3) CREATE PROC cache_members_populateMemberConditionCache_M_EQ_DATE
(4) CREATE PROC cache_members_populateMemberConditionCache_M_EXISTS_DATE
(5) CREATE PROC cache_members_populateMemberConditionCache_M_GT_DATE
(6) CREATE PROC cache_members_populateMemberConditionCache_M_GTE_DATE
(7) CREATE PROC cache_members_populateMemberConditionCache_M_LT_DATE
(8) CREATE PROC cache_members_populateMemberConditionCache_M_LTE_DATE
(9) CREATE PROC cache_members_populateMemberConditionCache_M_NEQ_DATE
(10) CREATE PROC cache_members_populateMemberConditionCache_M_NOTEXISTS_DATE
(11) DROP PROCEDURE cache_members_populateMemberConditionCache_M_GT -- Replaced with PROCEDURE cache_members_populateMemberConditionCache_M_GT_STRING
(12) CREATE PROC cache_members_populateMemberConditionCache_M_GT_STRING
(13) DROP PROCEDURE cache_members_populateMemberConditionCache_M_GTE -- Replaced with PROCEDURE cache_members_populateMemberConditionCache_M_GTE_STRING
(14) CREATE PROC cache_members_populateMemberConditionCache_M_GTE_STRING
(15) DROP PROCEDURE cache_members_populateMemberConditionCache_M_LT -- Replaced with PROCEDURE cache_members_populateMemberConditionCache_M_LT_STRING
(16) CREATE PROC cache_members_populateMemberConditionCache_M_LT_STRING
(17) DROP PROCEDURE cache_members_populateMemberConditionCache_M_LTE -- Replaced with PROCEDURE cache_members_populateMemberConditionCache_M_LTE_STRING
(18) CREATE PROC cache_members_populateMemberConditionCache_M_LTE_STRING
(19) DROP PROCEDURE cache_members_populateMemberConditionCache_M_NOTEXISTS -- Replaced with PROCEDURE cache_members_populateMemberConditionCache_M_NOTEXISTS_STRING
(20) CREATE PROC cache_members_populateMemberConditionCache_M_NOTEXISTS_STRING
(21) ALTER PROC cache_members_populateMemberConditionCache
(22) ALTER table ams_memberDataColumns add maxNumChar
(23) ALTER PROC ams_createMemberDataColumn
(24) ALTER PROC ams_updateMemberDataColumn
(25) ALTER PROC ams_getMemberFields
(26) ALTER table ams_memberFieldSets add descriptionContentID
(27) ALTER table ams_memberFieldSets add descriptionPosition 
(28) Script for creating content obhects for field descriptionContentID in table ams_memberFieldSets
(29) ALTER PROC ams_createMemberFieldset
(30) ALTER table sites add showFieldSetDescription -- Populate this column as well
(31) Script for modifying permissions for updateMember page
(32) ALTER PROC cms_createDefaultPages
(33) ALTER PROC email_sendTestBlast
(34) CREATE PROC email_getBlastRecipients
*/


USE membercentral
GO

/* (1) --------------------------------------------------------------------------------------------------------------------- */

ALTER FUNCTION [dbo].[fn_ams_getConditionFields_member] (@orgID int)
RETURNS TABLE 
AS
RETURN 
(
	select 1 as sorting, 'm_prefix' as fieldCode, 'Prefix' as fieldLabel, case when usePrefixList = 0 then 'TEXTBOX' else 'SELECT' end as displayTypeCode, 'STRING' as dataTypeCode from dbo.organizations where orgID = @orgID and hasPrefix = 1
		union
	select 2 as sorting, 'm_firstname' as fieldCode, 'First Name' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		union
	select 3 as sorting, 'm_middlename' as fieldCode, 'Middle Name' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode from dbo.organizations where orgID = @orgID and hasMiddleName = 1
		union
	select 4 as sorting, 'm_lastname' as fieldCode, 'Last Name' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		union
	select 5 as sorting, 'm_suffix' as fieldCode, 'Suffix' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode from dbo.organizations where orgID = @orgID and hasSuffix = 1
		union
	select 6 as sorting, 'm_professionalsuffix' as fieldCode, 'Professional Suffix' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode from dbo.organizations where orgID = @orgID and hasProfessionalSuffix = 1
		union
	select 7 as sorting, 'm_company' as fieldCode, 'Company' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode from dbo.organizations where orgID = @orgID and hasCompany = 1
		union
	select 8 as sorting, 'm_membernumber' as fieldCode, 'Member Number' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		union
	select 9 as sorting, 'm_membertypeid' as fieldCode, 'Account Type' as fieldLabel, 'SELECT' as displayTypeCode, 'INTEGER' as dataTypeCode
		union
	select 10 as sorting, 'm_datelastupdated' as fieldCode, 'Last Updated Date' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'DATE' as dataTypeCode
)
GO

/*  (2) --------------------------------------------------------------------------------------------------------------------- */

ALTER FUNCTION [dbo].[fn_ams_getPossibleMemberFields] (
	@orgID int
)
RETURNS @possibleFields TABLE (
	autoID int IDENTITY(1,1),
	sortGroup1 varchar(21), 
	sortGroup2 varchar(200), 
	sortGroup3 varchar(300), 
	sorting varchar(40), 
	dbObject varchar(40), 
	dbObjectalias varchar(5),
	dbField varchar(300) PRIMARY KEY, 
	fieldcode varchar(40), 
	fieldLabel varchar(420), 
	displayTypeCode varchar(20), 
	dataTypeCode varchar(20)
)
AS
BEGIN
	DECLARE @orgCode varchar(10), @hasPrefix bit, @usePrefixList bit, @hasMiddleName bit, @hasSuffix bit, @hasProfessionalSuffix bit, @hasCompany bit
	select @orgcode=orgcode, @hasPrefix=hasPrefix, @usePrefixList=usePrefixList, @hasMiddleName=hasMiddleName,
		@hasSuffix=hasSuffix, @hasProfessionalSuffix=hasProfessionalSuffix, @hasCompany=hasCompany
		from dbo.organizations 
		where orgID = @orgid

	-- ams_members
	INSERT INTO @possibleFields
	select 'Member Data' as sortGroup1, null as sortGroup2, null as sortGroup3, '001.001' as sorting, 'ams_members' as dbObject, 'm' as dbObjectAlias, 'prefix' as dbField, 'm_prefix' as fieldCode, 'Prefix' as fieldLabel, case when @usePrefixList = 0 then 'TEXTBOX' else 'SELECT' end as displayTypeCode, 'STRING' as dataTypeCode where @hasPrefix = 1
		union all
	select 'Member Data' as sortGroup1, null as sortGroup2, null as sortGroup3, '001.002' as sorting, 'ams_members' as dbObject, 'm' as dbObjectAlias, 'firstname' as dbField, 'm_firstname' as fieldCode, 'First Name' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		union all
	select 'Member Data' as sortGroup1, null as sortGroup2, null as sortGroup3, '001.003' as sorting, 'ams_members' as dbObject, 'm' as dbObjectAlias, 'middlename' as dbField, 'm_middlename' as fieldCode, 'Middle Name' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode where @hasMiddleName = 1
		union all
	select 'Member Data' as sortGroup1, null as sortGroup2, null as sortGroup3, '001.004' as sorting, 'ams_members' as dbObject, 'm' as dbObjectAlias, 'lastname' as dbField, 'm_lastname' as fieldCode, 'Last Name' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		union all
	select 'Member Data' as sortGroup1, null as sortGroup2, null as sortGroup3, '001.005' as sorting, 'ams_members' as dbObject, 'm' as dbObjectAlias, 'suffix' as dbField, 'm_suffix' as fieldCode, 'Suffix' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode where @hasSuffix = 1
		union all
	select 'Member Data' as sortGroup1, null as sortGroup2, null as sortGroup3, '001.006' as sorting, 'ams_members' as dbObject, 'm' as dbObjectAlias, 'professionalsuffix' as dbField, 'm_professionalsuffix' as fieldCode, 'Professional Suffix' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode where @hasProfessionalSuffix = 1
		union all
	select 'Member Data' as sortGroup1, null as sortGroup2, null as sortGroup3, '001.007' as sorting, 'ams_members' as dbObject, 'm' as dbObjectAlias, 'company' as dbField, 'm_company' as fieldCode, 'Company' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode where @hasCompany = 1
		union all
	select 'Member Data' as sortGroup1, null as sortGroup2, null as sortGroup3, '001.008' as sorting, 'ams_members' as dbObject, 'm' as dbObjectAlias, 'membernumber' as dbField, 'm_membernumber' as fieldCode, 'Member Number' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		union all
	select 'Member Data' as sortGroup1, null as sortGroup2, null as sortGroup3, '001.009' as sorting, 'ams_members' as dbObject, 'm' as dbObjectAlias, 'recordtypeid' as dbField, 'm_recordtypeid' as fieldCode, 'Record Type' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		union all
	select 'Member Data' as sortGroup1, null as sortGroup2, null as sortGroup3, '001.010' as sorting, 'ams_members' as dbObject, 'm' as dbObjectAlias, 'status' as dbField, 'm_status' as fieldCode, 'Account Status' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		union all
	select 'Member Data' as sortGroup1, null as sortGroup2, null as sortGroup3, '001.011' as sorting, 'ams_members' as dbObject, 'm' as dbObjectAlias, 'membertypeid' as dbField, 'm_membertypeid' as fieldCode, 'Account Type' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		union all
	select 'Member Data' as sortGroup1, null as sortGroup2, null as sortGroup3, '001.012' as sorting, 'ams_members' as dbObject, 'm' as dbObjectAlias, 'datelastupdated' as dbField, 'm_datelastupdated' as fieldCode, 'Last Updated Date' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'DATE' as dataTypeCode

	-- accounting
	INSERT INTO @possibleFields
	select 'Accounting' as sortGroup1, null as sortGroup2, null as sortGroup3, '002.001' as sorting, 'acct' as dbObject, 'acct' as dbObjectAlias, 'acct_balance_0' as dbField, 'acct_balance_0' as fieldCode, 'Total Credit Balance' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		union all
	select 'Accounting' as sortGroup1, null as sortGroup2, null as sortGroup3, '002.' + right('000'+cast(ROW_NUMBER() OVER(order by mp.profileName)+1 as varchar(10)),3) as sorting, 'acct' as dbObject, 'acct' as dbObjectAlias, 'acct_balance_' + cast(mp.profileID as varchar(10)) as dbField, 'acct_balance_' + cast(mp.profileID as varchar(10)) as fieldCode, mp.profileName + ' Credit Balance' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
	from dbo.mp_profiles as mp
	inner join dbo.sites as s on s.siteID = mp.siteID
	inner join dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
	where s.orgID = @orgID
	and mp.status = 'A'
	and g.isActive = 1
	and g.gatewayType not in ('PayLater')

	-- addresses
	declare @tblAddrTypes table (addressTypeID int, addressType varchar(20), addressTypeOrder int, hasAttn bit, hasAddress2 bit, hasAddress3 bit, hasCounty bit, districtMatching bit)
	insert into @tblAddrTypes
	select addressTypeID, addressType, addressTypeOrder, hasAttn, hasAddress2, hasAddress3, hasCounty, districtMatching
	from dbo.ams_memberAddressTypes 
	where orgID = @orgID
		union
	select 0, 'Designated Billing', 99, max(cast(hasAttn as tinyint)), max(cast(hasAddress2 as tinyint)), 
		max(cast(hasAddress3 as tinyint)), max(cast(hasCounty as tinyint)), max(cast(districtMatching as tinyint))
	from dbo.ams_memberAddressTypes 
	where orgID = @orgID

	INSERT INTO @possibleFields
	select 'Addresses' as sortGroup1, mat.addressType as sortGroup2, null as sortGroup3, '003.' + right('000'+cast(mat.addressTypeOrder as varchar(10)),3) + '.001' as sorting, 'vw_memberData_' + @orgcode as dbObject, 'md' as dbObjectAlias, mat.addressType + '_attn' as dbField, 'ma_' + cast(mat.addressTypeID as varchar(10)) + '_attn' as fieldCode, mat.addressType + ' Attn' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from @tblAddrTypes as mat
		where mat.hasAttn = 1
		union all
	select 'Addresses' as sortGroup1, mat.addressType as sortGroup2, null as sortGroup3, '003.' + right('000'+cast(mat.addressTypeOrder as varchar(10)),3) + '.002' as sorting, 'vw_memberData_' + @orgcode as dbObject, 'md' as dbObjectAlias, mat.addressType + '_address1' as dbField, 'ma_' + cast(mat.addressTypeID as varchar(10)) + '_address1' as fieldCode, mat.addressType + ' Address' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from @tblAddrTypes as mat
		union all
	select 'Addresses' as sortGroup1, mat.addressType as sortGroup2, null as sortGroup3, '003.' + right('000'+cast(mat.addressTypeOrder as varchar(10)),3) + '.003' as sorting, 'vw_memberData_' + @orgcode as dbObject, 'md' as dbObjectAlias, mat.addressType + '_address2' as dbField, 'ma_' + cast(mat.addressTypeID as varchar(10)) + '_address2' as fieldCode, mat.addressType + ' Address 2' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from @tblAddrTypes as mat
		where mat.hasAddress2 = 1
		union all
	select 'Addresses' as sortGroup1, mat.addressType as sortGroup2, null as sortGroup3, '003.' + right('000'+cast(mat.addressTypeOrder as varchar(10)),3) + '.004' as sorting, 'vw_memberData_' + @orgcode as dbObject, 'md' as dbObjectAlias, mat.addressType + '_address3' as dbField, 'ma_' + cast(mat.addressTypeID as varchar(10)) + '_address3' as fieldCode, mat.addressType + ' Address 3' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from @tblAddrTypes as mat
		where mat.hasAddress3 = 1
		union all
	select 'Addresses' as sortGroup1, mat.addressType as sortGroup2, null as sortGroup3, '003.' + right('000'+cast(mat.addressTypeOrder as varchar(10)),3) + '.005' as sorting, 'vw_memberData_' + @orgcode as dbObject, 'md' as dbObjectAlias, mat.addressType + '_city' as dbField, 'ma_' + cast(mat.addressTypeID as varchar(10)) + '_city' as fieldCode, mat.addressType + ' City' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from @tblAddrTypes as mat
		union all
	select 'Addresses' as sortGroup1, mat.addressType as sortGroup2, null as sortGroup3, '003.' + right('000'+cast(mat.addressTypeOrder as varchar(10)),3) + '.006' as sorting, 'vw_memberData_' + @orgcode as dbObject, 'md' as dbObjectAlias, mat.addressType + '_stateprov' as dbField, 'ma_' + cast(mat.addressTypeID as varchar(10)) + '_stateprov' as fieldCode, mat.addressType + ' State' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		from @tblAddrTypes as mat
		union all
	select 'Addresses' as sortGroup1, mat.addressType as sortGroup2, null as sortGroup3, '003.' + right('000'+cast(mat.addressTypeOrder as varchar(10)),3) + '.007' as sorting, 'vw_memberData_' + @orgcode as dbObject, 'md' as dbObjectAlias, mat.addressType + '_postalcode' as dbField, 'ma_' + cast(mat.addressTypeID as varchar(10)) + '_postalcode' as fieldCode, mat.addressType + ' Postal Code' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from @tblAddrTypes as mat
		union all
	select 'Addresses' as sortGroup1, mat.addressType as sortGroup2, null as sortGroup3, '003.' + right('000'+cast(mat.addressTypeOrder as varchar(10)),3) + '.008' as sorting, 'vw_memberData_' + @orgcode as dbObject, 'md' as dbObjectAlias, mat.addressType + '_county' as dbField, 'ma_' + cast(mat.addressTypeID as varchar(10)) + '_county' as fieldCode, mat.addressType + ' County' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from @tblAddrTypes as mat
		where mat.hasCounty = 1
		union all
	select 'Addresses' as sortGroup1, mat.addressType as sortGroup2, null as sortGroup3, '003.' + right('000'+cast(mat.addressTypeOrder as varchar(10)),3) + '.009' as sorting, 'vw_memberData_' + @orgcode as dbObject, 'md' as dbObjectAlias, mat.addressType + '_country' as dbField, 'ma_' + cast(mat.addressTypeID as varchar(10)) + '_country' as fieldCode, mat.addressType + ' Country' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		from @tblAddrTypes as mat

	-- phones (under addresses)
	INSERT INTO @possibleFields
	select 'Addresses' as sortGroup1, addressType as sortGroup2, null as sortGroup3, '003.' + right('000'+cast(addressTypeOrder as varchar(10)),3) + '.' + right('000'+cast(9+row as varchar(10)),3) as sorting, 'vw_memberData_' + @orgcode as dbObject, 'md' as dbObjectAlias, addressType + '_' + phoneType as dbField, 'mp_' + cast(addressTypeID as varchar(10)) + '_' + cast(phoneTypeID as varchar(10)) as fieldCode, addressType + ' ' + phoneType as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
	from (
		select mat.addressTypeID, mpt.phoneTypeID, mat.addressType, mpt.phoneType, mat.addressTypeOrder, 
			ROW_NUMBER() OVER (ORDER BY mat.addressTypeOrder, mpt.phoneTypeOrder) as row
		from dbo.ams_memberPhoneTypes as mpt
		cross join @tblAddrTypes as mat
		where mpt.orgID = @orgID
	) as tmpPhones

	-- district matching (under addresses)
	INSERT INTO @possibleFields
	select 'Addresses' as sortGroup1, addressType as sortGroup2, null as sortGroup3, '003.' + right('000'+cast(addressTypeOrder as varchar(10)),3) + '.' + right('000'+cast(29+row as varchar(10)),3) as sorting, 'vw_memberData_' + @orgcode as dbObject, 'md' as dbObjectAlias, addressType + '_' + districtType as dbField, 'mad_' + cast(addressTypeID as varchar(10)) + '_' + cast(districtTypeID as varchar(10)) as fieldCode, addressType + ' ' + districtType as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
	from (
		select mat.addressTypeID, mdt.districtTypeID, mat.addressType, mdt.districtType, mat.addressTypeOrder, 
			ROW_NUMBER() OVER (ORDER BY mat.addressTypeOrder, mdt.districtTypeOrder) as row
		from dbo.ams_memberDistrictTypes as mdt
		cross join @tblAddrTypes as mat
		where mdt.orgID = @orgID
		and mat.districtMatching = 1
	) as tmpDistricts

	-- ams_memberdatacolumns
	INSERT INTO @possibleFields
	select 'Custom Fields' as sortGroup1, null as sortGroup2, null as sortGroup3, '004.' + right('000'+cast(ROW_NUMBER() OVER(order by mdc.columnName) as varchar(10)),3) as sorting, 'vw_memberData_' + @orgcode as dbObject, 'md' as dbObjectAlias, mdc.columnName as dbField, 'md_' + cast(mdc.columnID as varchar(10)) as fieldCode, mdc.columnName as fieldLabel, dt.displayTypeCode, ddt.dataTypeCode
		from dbo.ams_memberdatacolumns as mdc
		inner join dbo.ams_memberDataColumnDisplayTypes as dt on dt.displayTypeID = mdc.displayTypeID
		inner join dbo.ams_memberDataColumnDataTypes as ddt on ddt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		
	-- emails
	INSERT INTO @possibleFields
	select 'E-mails' as sortGroup1, null as sortGroup2, null as sortGroup3, '005.' + right('000'+cast(met.emailTypeOrder as varchar(10)),3) as sorting, 'vw_memberData_' + @orgcode as dbObject, 'md' as dbObjectAlias, met.emailType as dbField, 'me_' + cast(met.emailTypeID as varchar(10)) + '_email' as fieldCode, met.emailType as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from dbo.ams_memberEmailTypes as met
		where met.orgID = @orgID

	-- groups
	INSERT INTO @possibleFields
	select 'Group Memberships' as sortGroup1, null as sortGroup2, null as sortGroup3, '006.' + right('0000'+cast(rg.row as varchar(20)),4) as sorting, 'grps' as dbObject, 'grps' as dbObjectAlias, 'grp_' + cast(rg.groupID as varchar(10)) as dbField, 'grp_' + cast(rg.groupID as varchar(10)) as fieldCode, left(rg.thePathExpanded,420) as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode 
		from dbo.fn_getRecursiveGroups(@orgID, default, default, 0) as rg

	-- professional licenses
	INSERT INTO @possibleFields
	select 'Professional Licenses' as sortGroup1, mplt.PLName as sortGroup2, null as sortGroup3, '007.' + right('000'+cast(mplt.orderNum as varchar(10)),3) + '.001' as sorting, 'vw_memberData_' + @orgcode as dbObject, 'md' as dbObjectAlias, mplt.PLName + '_licenseNumber' as dbField, 'mpl_' + cast(mplt.PLTypeID as varchar(10)) + '_licenseNumber' as fieldCode, mplt.PLName + ' License Number' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from dbo.ams_memberProfessionalLicenseTypes as mplt
		where mplt.orgID = @orgID
		union all
	select 'Professional Licenses' as sortGroup1, mplt.PLName as sortGroup2, null as sortGroup3, '007.' + right('000'+cast(mplt.orderNum as varchar(10)),3) + '.002' as sorting, 'vw_memberData_' + @orgcode as dbObject, 'md' as dbObjectAlias, mplt.PLName + '_activeDate' as dbField, 'mpl_' + cast(mplt.PLTypeID as varchar(10)) + '_activeDate' as fieldCode, mplt.PLName + ' Active Date' as fieldLabel, 'DATE' as displayTypeCode, 'DATE' as dataTypeCode
		from dbo.ams_memberProfessionalLicenseTypes as mplt
		where mplt.orgID = @orgID
		union all
	select 'Professional Licenses' as sortGroup1, mplt.PLName as sortGroup2, null as sortGroup3, '007.' + right('000'+cast(mplt.orderNum as varchar(10)),3) + '.003' as sorting, 'vw_memberData_' + @orgcode as dbObject, 'md' as dbObjectAlias, mplt.PLName + '_status' as dbField, 'mpl_' + cast(mplt.PLTypeID as varchar(10)) + '_status' as fieldCode, mplt.PLName + ' Status' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from dbo.ams_memberProfessionalLicenseTypes as mplt
		where mplt.orgID = @orgID

	-- subscriptions
	; WITH subTypes AS (
		select st.typeID, st.typeName, ROW_NUMBER() OVER (order by st.typeName) as row
		from dbo.sub_types as st
		inner join dbo.sites as s on s.siteID = st.siteID
		where s.orgID = @orgID
		and st.status <> 'D'
	), subs AS (
		select subs.subscriptionID, subs.subscriptionName, st.typeName, ROW_NUMBER() OVER (order by st.typeName, subs.subscriptionName) as row
		from dbo.sub_subscriptions as subs
		inner join dbo.sub_types as st on st.typeID = subs.typeID
		inner join dbo.sites as s on s.siteID = st.siteID
		where s.orgID = @orgID
		and subs.status <> 'D'
		and st.status <> 'D'
	)
	INSERT INTO @possibleFields
	select 'Subscriptions' as sortGroup1, subTypes.typeName as sortGroup2, 'All subscriptions' as sortGroup3, '008.' + right('0000'+cast(subTypes.row as varchar(10)),4) + '.001' as sorting, 'subs' as dbObject, 'subs' as dbObjectAlias, g.groupCode as dbField, 'grp_' + cast(g.groupID as varchar(10)) as fieldCode, 'Active Subscriber' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		from subTypes
		inner join dbo.ams_groups as g on g.groupCode = 'Active_' + cast(subTypes.typeID as varchar(10)) + '_tracking' and g.status <> 'D'
		union all
	select 'Subscriptions' as sortGroup1, subTypes.typeName as sortGroup2, 'All subscriptions' as sortGroup3, '008.' + right('0000'+cast(subTypes.row as varchar(10)),4) + '.002' as sorting, 'subs' as dbObject, 'subs' as dbObjectAlias, g.groupCode as dbField, 'grp_' + cast(g.groupID as varchar(10)) as fieldCode, 'Awaiting Payment Subscriber' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		from subTypes
		inner join dbo.ams_groups as g on g.groupCode = 'WaitingPay_' + cast(subTypes.typeID as varchar(10)) + '_tracking' and g.status <> 'D'
		union all
	select 'Subscriptions' as sortGroup1, subTypes.typeName as sortGroup2, 'All subscriptions' as sortGroup3, '008.' + right('0000'+cast(subTypes.row as varchar(10)),4) + '.003' as sorting, 'subs' as dbObject, 'subs' as dbObjectAlias, g.groupCode as dbField, 'grp_' + cast(g.groupID as varchar(10)) as fieldCode, 'Inactive Subscriber' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		from subTypes
		inner join dbo.ams_groups as g on g.groupCode = 'Inactive_' + cast(subTypes.typeID as varchar(10)) + '_tracking' and g.status <> 'D'
		union all
	select 'Subscriptions' as sortGroup1, subTypes.typeName as sortGroup2, 'All subscriptions' as sortGroup3, '008.' + right('0000'+cast(subTypes.row as varchar(10)),4) + '.004' as sorting, 'subs' as dbObject, 'subs' as dbObjectAlias, g.groupCode as dbField, 'grp_' + cast(g.groupID as varchar(10)) as fieldCode, 'Pending Subscriber' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		from subTypes
		inner join dbo.ams_groups as g on g.groupCode = 'Pending_' + cast(subTypes.typeID as varchar(10)) + '_tracking' and g.status <> 'D'
		union all
	select 'Subscriptions' as sortGroup1, subTypes.typeName as sortGroup2, 'All subscriptions' as sortGroup3, '008.' + right('0000'+cast(subTypes.row as varchar(10)),4) + '.005' as sorting, 'subs' as dbObject, 'subs' as dbObjectAlias, g.groupCode as dbField, 'grp_' + cast(g.groupID as varchar(10)) as fieldCode, 'Renewable Subscriber' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		from subTypes
		inner join dbo.ams_groups as g on g.groupCode = 'Renewable_' + cast(subTypes.typeID as varchar(10)) + '_tracking' and g.status <> 'D'
		union all
	select 'Subscriptions' as sortGroup1, subs.typeName as sortGroup2, subs.subscriptionName as sortGroup3, '008.' + right('0000'+cast(row+200 as varchar(10)),4) + '.001' as sorting, 'subs' as dbObject, 'subs' as dbObjectAlias, g.groupCode as dbField, 'grp_' + cast(g.groupID as varchar(10)) as fieldCode, 'Active Subscriber' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		from subs
		inner join dbo.ams_groups as g on g.groupCode = 'SubActive_' + cast(subs.subscriptionID as varchar(10)) + '_tracking' and g.status <> 'D'
		union all
	select 'Subscriptions' as sortGroup1, subs.typeName as sortGroup2, subs.subscriptionName as sortGroup3, '008.' + right('0000'+cast(row+200 as varchar(10)),4) + '.002' as sorting, 'subs' as dbObject, 'subs' as dbObjectAlias, g.groupCode as dbField, 'grp_' + cast(g.groupID as varchar(10)) as fieldCode, 'Awaiting Payment Subscriber' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		from subs
		inner join dbo.ams_groups as g on g.groupCode = 'SubWaitingPay_' + cast(subs.subscriptionID as varchar(10)) + '_tracking' and g.status <> 'D'
		union all
	select 'Subscriptions' as sortGroup1, subs.typeName as sortGroup2, subs.subscriptionName as sortGroup3, '008.' + right('0000'+cast(row+200 as varchar(10)),4) + '.003' as sorting, 'subs' as dbObject, 'subs' as dbObjectAlias, g.groupCode as dbField, 'grp_' + cast(g.groupID as varchar(10)) as fieldCode, 'Inactive Subscriber' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		from subs
		inner join dbo.ams_groups as g on g.groupCode = 'SubInactive_' + cast(subs.subscriptionID as varchar(10)) + '_tracking' and g.status <> 'D'
		union all
	select 'Subscriptions' as sortGroup1, subs.typeName as sortGroup2, subs.subscriptionName as sortGroup3, '008.' + right('0000'+cast(row+200 as varchar(10)),4) + '.004' as sorting, 'subs' as dbObject, 'subs' as dbObjectAlias, g.groupCode as dbField, 'grp_' + cast(g.groupID as varchar(10)) as fieldCode, 'Pending Subscriber' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		from subs
		inner join dbo.ams_groups as g on g.groupCode = 'SubPending_' + cast(subs.subscriptionID as varchar(10)) + '_tracking' and g.status <> 'D'
		union all
	select 'Subscriptions' as sortGroup1, subs.typeName as sortGroup2, subs.subscriptionName as sortGroup3, '008.' + right('0000'+cast(row+200 as varchar(10)),4) + '.005' as sorting, 'subs' as dbObject, 'subs' as dbObjectAlias, g.groupCode as dbField, 'grp_' + cast(g.groupID as varchar(10)) as fieldCode, 'Renewable Subscriber' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		from subs
		inner join dbo.ams_groups as g on g.groupCode = 'SubRenew_' + cast(subs.subscriptionID as varchar(10)) + '_tracking' and g.status <> 'D'

	-- websites
	INSERT INTO @possibleFields
	select 'Websites' as sortGroup1, null as sortGroup2, null as sortGroup3, '009.' + right('000'+cast(mwt.websiteTypeOrder as varchar(10)),3) as sorting, 'vw_memberData_' + @orgcode as dbObject, 'md' as dbObjectAlias, mwt.websiteType as dbField, 'mw_' + cast(mwt.websiteTypeID as varchar(10)) + '_website' as fieldCode, mwt.websiteType as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from dbo.ams_memberWebsiteTypes as mwt
		where mwt.orgID = @orgID

	RETURN 
END
GO

/* (3) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_M_EQ_DATE') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_M_EQ_DATE
GO


CREATE PROC [dbo].[cache_members_populateMemberConditionCache_M_EQ_DATE]
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
inner join dbo.ams_members as m on m.memberID = tmpM.memberID
where tblc.subProc = 'M_EQ_DATE'
and dateDiff(dd, m.datelastupdated, cv.conditionValueDate) = 0
GO

/*  (4) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_M_EXISTS_DATE') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_M_EXISTS_DATE
GO

CREATE PROC [dbo].[cache_members_populateMemberConditionCache_M_EXISTS_DATE]
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
inner join dbo.ams_members as m on m.memberID = tmpM.memberID
where tblc.subProc = 'M_EXISTS_DATE'
and m.datelastupdated  is not null
GO

/*  (5) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_M_GT_DATE') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_M_GT_DATE
GO

CREATE PROC [dbo].[cache_members_populateMemberConditionCache_M_GT_DATE]
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
inner join dbo.ams_members as m on m.memberID = tmpM.memberID
where tblc.subProc = 'M_GT_DATE'
and m.datelastupdated > cv.conditionValueDate
GO

/*  (6) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_M_GTE_DATE') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_M_GTE_DATE
GO

CREATE PROC [dbo].[cache_members_populateMemberConditionCache_M_GTE_DATE]
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
inner join dbo.ams_members as m on m.memberID = tmpM.memberID
where tblc.subProc = 'M_GTE_DATE'
and m.datelastupdated >= cv.conditionValueDate
GO

/*  (7) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_M_LT_DATE') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_M_LT_DATE
GO

CREATE PROC [dbo].[cache_members_populateMemberConditionCache_M_LT_DATE]
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
inner join dbo.ams_members as m on m.memberID = tmpM.memberID
where tblc.subProc = 'M_LT_DATE'
and m.datelastupdated < cv.conditionValueDate
GO

/*  (8) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_M_LTE_DATE') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_M_LTE_DATE
GO

CREATE PROC [dbo].[cache_members_populateMemberConditionCache_M_LTE_DATE]
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
inner join dbo.ams_members as m on m.memberID = tmpM.memberID
where tblc.subProc = 'M_LTE_DATE'
and (m.datelastupdated < cv.conditionValueDate
	OR dateDiff(dd, m.datelastupdated, cv.conditionValueDate) = 0)
GO

/*  (9) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_M_NEQ_DATE') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_M_NEQ_DATE
GO

CREATE PROC [dbo].[cache_members_populateMemberConditionCache_M_NEQ_DATE]
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
inner join dbo.ams_members as m on m.memberID = tmpM.memberID
where tblc.subProc = 'M_NEQ_DATE'
and m.datelastupdated <> cv.conditionValueDate
GO

/*  (10) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_M_NOTEXISTS_DATE') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_M_NOTEXISTS_DATE
GO

CREATE PROC [dbo].[cache_members_populateMemberConditionCache_M_NOTEXISTS_DATE]
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
inner join dbo.ams_members as m on m.memberID = tmpM.memberID
where tblc.subProc = 'M_NOTEXISTS_DATE'
and m.datelastupdated  is null
GO

/*  (11) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_M_GT') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_M_GT
GO

/*  (12) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_M_GT_STRING') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_M_GT_STRING
GO

CREATE PROC [dbo].[cache_members_populateMemberConditionCache_M_GT_STRING]
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
inner join dbo.ams_members as m on m.memberID = tmpM.memberID
where tblc.subProc = 'M_GT_STRING'
and case tblc.fieldCode
	when 'm_firstname' then isnull(m.firstname,'')
	when 'm_middlename' then isnull(m.middlename,'')
	when 'm_lastname' then isnull(m.lastname,'')
	when 'm_suffix' then isnull(m.suffix,'')
	when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
	when 'm_company' then isnull(m.company,'')
	when 'm_membernumber' then isnull(m.membernumber,'')
	else isnull(m.prefix,'') end > cv.conditionValueString
GO

/*  (13) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_M_GTE') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_M_GTE
GO

/*  (14) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_M_GTE_STRING') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_M_GTE_STRING
GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_M_GTE_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
inner join dbo.ams_members as m on m.memberID = tmpM.memberID
where tblc.subProc = 'M_GTE_STRING'
and case tblc.fieldCode
	when 'm_firstname' then isnull(m.firstname,'')
	when 'm_middlename' then isnull(m.middlename,'')
	when 'm_lastname' then isnull(m.lastname,'')
	when 'm_suffix' then isnull(m.suffix,'')
	when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
	when 'm_company' then isnull(m.company,'')
	when 'm_membernumber' then isnull(m.membernumber,'')
	else isnull(m.prefix,'') end >= cv.conditionValueString
GO

/*  (15) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_M_LT') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_M_LT
GO

/*  (16) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_M_LT_STRING') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_M_LT_STRING
GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_M_LT_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
inner join dbo.ams_members as m on m.memberID = tmpM.memberID
where tblc.subProc = 'M_LT_STRING'
and case tblc.fieldCode
	when 'm_firstname' then isnull(m.firstname,'')
	when 'm_middlename' then isnull(m.middlename,'')
	when 'm_lastname' then isnull(m.lastname,'')
	when 'm_suffix' then isnull(m.suffix,'')
	when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
	when 'm_company' then isnull(m.company,'')
	when 'm_membernumber' then isnull(m.membernumber,'')
	else isnull(m.prefix,'') end < cv.conditionValueString

GO

/*  (17) --------------------------------------------------------------------------------------------------------------------- */


IF OBJECT_ID('cache_members_populateMemberConditionCache_M_LTE') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_M_LTE
GO

/*  (18) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_M_LTE_STRING') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_M_LTE_STRING
GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_M_LTE_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
inner join dbo.ams_members as m on m.memberID = tmpM.memberID
where tblc.subProc = 'M_LTE_STRING'
and case tblc.fieldCode
	when 'm_firstname' then isnull(m.firstname,'')
	when 'm_middlename' then isnull(m.middlename,'')
	when 'm_lastname' then isnull(m.lastname,'')
	when 'm_suffix' then isnull(m.suffix,'')
	when 'm_professionalsuffix' then isnull(m.professionalsuffix,'')
	when 'm_company' then isnull(m.company,'')
	when 'm_membernumber' then isnull(m.membernumber,'')
	else isnull(m.prefix,'') end <= cv.conditionValueString

GO

/*  (19) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_M_NOTEXISTS') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_M_NOTEXISTS
GO

/*  (20) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_M_NOTEXISTS_STRING') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_M_NOTEXISTS_STRING
GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_M_NOTEXISTS_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as tmpM on tmpM.memberID = tmpM.memberID
inner join dbo.ams_members as m on m.memberID = tmpM.memberID
where tblc.subProc = 'M_NOTEXISTS_STRING'
and case tblc.fieldCode
	when 'm_firstname' then nullif(m.firstname,'')
	when 'm_middlename' then nullif(m.middlename,'')
	when 'm_lastname' then nullif(m.lastname,'')
	when 'm_suffix' then nullif(m.suffix,'')
	when 'm_professionalsuffix' then nullif(m.professionalsuffix,'')
	when 'm_company' then nullif(m.company,'')
	when 'm_membernumber' then nullif(m.membernumber,'')
	else nullif(m.prefix,'') end is null

GO

/*  (21) --------------------------------------------------------------------------------------------------------------------- */

ALTER PROC [dbo].[cache_members_populateMemberConditionCache]
@orgID int,
@conditionIDList varchar(max) = null,
@memberIDList varchar(max) = null,
@processImmediateOnly bit = 1,
@itemGroupUID uniqueidentifier,
@logTreeID uniqueidentifier

as

set nocount on

IF @itemGroupUID is null
	RETURN -1

declare @starttime datetime, @starttime2 datetime, @sql varchar(max), @totalMS int, @totalID int
select @starttime = getdate()
set @conditionIDList = isNull(@conditionIDList,'')
set @memberIDList = isNull(@memberIDList,'')


-- Log
INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage)
VALUES (@logTreeID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'Start Process of orgID=' + cast(@orgID as varchar(10)) + ' conditionID=' + left(@conditionIDList,100) + ' memberID=' + left(@memberIDList,100))


-- split members to calculate
IF OBJECT_ID('tempdb..#tblMembers') IS NOT NULL
	DROP TABLE #tblMembers
CREATE TABLE #tblMembers (memberID int PRIMARY KEY);

IF len(@memberIDList) > 0
	INSERT INTO #tblMembers
	select distinct m.memberID
	from dbo.fn_intListToTable(@memberIDList,',') as tmp
	inner join dbo.ams_members as m on m.memberID = tmp.listitem
	where m.orgID = @orgID
	and m.status <> 'D'
ELSE
	INSERT INTO #tblMembers
	select m.memberID
	from dbo.ams_members as m
	where m.orgID = @orgID
	and m.status <> 'D'

select @totalID = count(*) from #tblMembers 
INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage)
VALUES (@logTreeID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'Found ' + cast(@totalID as varchar(10)) + ' members to process');


-- split conditions to calculate. HONOR THE @processImmediateOnly parameter
IF OBJECT_ID('tempdb..#tblCond') IS NOT NULL
	DROP TABLE #tblCond
CREATE TABLE #tblCond (conditionID int PRIMARY KEY);

IF len(@conditionIDList) > 0
	INSERT INTO #tblCond
	select distinct c.conditionID
	from dbo.fn_intListToTable(@conditionIDList,',') as tmp
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tmp.listitem
	inner join dbo.ams_virtualGroupConditionTypes as ct on ct.conditionTypeID = c.conditionTypeID
	where c.orgID = @orgID
	and c.isDefined = 1
	and 1 = case 
		when @processImmediateOnly = 1 and ct.processImmediately = 0 then 0
		else 1 end
ELSE
	INSERT INTO #tblCond
	select c.conditionID
	from dbo.ams_virtualGroupConditions as c
	inner join dbo.ams_virtualGroupConditionTypes as ct on ct.conditionTypeID = c.conditionTypeID
	where c.orgID = @orgID
	and c.isDefined = 1	
	and 1 = case 
		when @processImmediateOnly = 1 and ct.processImmediately = 0 then 0
		else 1 end


-- get all conditions to calculate
IF OBJECT_ID('tempdb..#tblCondALL') IS NOT NULL
	DROP TABLE #tblCondALL
CREATE TABLE #tblCondALL (conditionID int PRIMARY KEY, orgID int, expression varchar(20), fieldCode varchar(40), fieldCodeArea varchar(25), 
	displayTypeCode varchar(20), dataTypeCode varchar(20), fieldCodeAreaID int, fieldCodeAreaPartA varchar(20), subProc varchar(30));

INSERT INTO #tblCondALL
select c.conditionID, c.orgID, e.expression, c.fieldCode, fieldCodeArea = case
	when left(c.fieldCode,2) = 'm_' then 'Member Data'	
	when left(c.fieldCode,3) = 'md_' then 'Custom Fields'	
	when left(c.fieldCode,3) = 'ma_' then 'Addresses'	
	when left(c.fieldCode,3) = 'mp_' then 'Phones'	
	when left(c.fieldCode,4) = 'mad_' then 'Districting'	
	when left(c.fieldCode,3) = 'me_' then 'Emails'	
	when left(c.fieldCode,3) = 'mw_' then 'Websites'	
	when left(c.fieldCode,2) = 'e_' then 'Events'	
	when left(c.fieldcode,4) = 'mpl_' then 'Professional Licenses'	
	when left(c.fieldcode,4) = 'grp_' then 'Groups'	-- excluded in where clause
	when left(c.fieldcode,4) = 'sub_' then 'Subscriptions'	
	when left(c.fieldcode,3) = 'rt_' then 'Record Types'	
	when left(c.fieldcode,5) = 'acct_' then 'Accounting'	
	when left(c.fieldcode,3) = 'mh_' then 'Member History'	
	when left(c.fieldcode,2) = 'l_' then 'Listserver Memberships'
	end, dit.displayTypeCode, dat.dataTypeCode,
	fieldCodeAreaID = case 
	when left(c.fieldCode,3) = 'md_' then cast(replace(c.fieldcode,'md_','') as int)
	when left(c.fieldcode,4) = 'mpl_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,3) = 'ma_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,2) = 'e_' then cast(replace(c.fieldcode,'e_','') as int)
	when left(c.fieldCode,3) = 'me_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,3) = 'mw_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,3) = 'mp_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as int)
	when left(c.fieldCode,4) = 'mad_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as int)
	else null end,
	fieldCodeAreaPartA = case
	when left(c.fieldcode,4) = 'mpl_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as varchar(20))
	when left(c.fieldCode,3) = 'ma_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as varchar(20))
	when left(c.fieldCode,3) = 'mp_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as varchar(10))
	when left(c.fieldCode,4) = 'mad_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as varchar(10))
	else null end,
	subProc = ''
from dbo.ams_virtualGroupConditions as c
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.expressionID
inner join dbo.ams_memberDataColumnDataTypes as dat on dat.dataTypeID = c.dataTypeID
inner join dbo.ams_memberDataColumnDisplayTypes as dit on dit.displayTypeID = c.displayTypeID
inner join #tblCond as tblC on tblc.conditionID = c.conditionID
where left(c.fieldCode,4) <> 'grp_'

-- add indexes for speed
CREATE INDEX IX_tblCondALL_areaid ON #tblCondALL (fieldCodeAreaID asc);
CREATE INDEX IX_tblCondALL_areaparta ON #tblCondALL (fieldCodeAreaPartA asc);
CREATE NONCLUSTERED INDEX IX_tblCondALL_DTA2 ON #tblCondALL (fieldCodeArea ASC, expression ASC, dataTypeCode ASC);

-- update subProcs to run
update #tblCondALL
set subProc = case 
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='between' then 'ACCT_BETWEEN_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='eq' then 'ACCT_EQ_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='exists' then 'ACCT_EXISTS_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='gt' then 'ACCT_GT_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='gte' then 'ACCT_GTE_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='lt' then 'ACCT_LT_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='lte' then 'ACCT_LTE_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='neq' then 'ACCT_NEQ_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='not_exists' then 'ACCT_NOTEXISTS_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsumrecog' and expression='between' then 'ACCT_BETWEEN_ALLOCSUMRECOG'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsumrecog' and expression='eq' then 'ACCT_EQ_ALLOCSUMRECOG'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsumrecog' and expression='exists' then 'ACCT_EXISTS_ALLOCSUMRECOG'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsumrecog' and expression='gt' then 'ACCT_GT_ALLOCSUMRECOG'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsumrecog' and expression='gte' then 'ACCT_GTE_ALLOCSUMRECOG'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsumrecog' and expression='lt' then 'ACCT_LT_ALLOCSUMRECOG'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsumrecog' and expression='lte' then 'ACCT_LTE_ALLOCSUMRECOG'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsumrecog' and expression='neq' then 'ACCT_NEQ_ALLOCSUMRECOG'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsumrecog' and expression='not_exists' then 'ACCT_NOTEXISTS_ALLOCSUMRECOG'
	when fieldCodeArea='Addresses' and expression='contains' then 'MA_CONTAINS'
	when fieldCodeArea='Addresses' and expression='contains_regex' then 'MA_CONTAINSREGEX'
	when fieldCodeArea='Addresses' and expression='eq' then 'MA_EQ'
	when fieldCodeArea='Addresses' and expression='exists' then 'MA_EXISTS'
	when fieldCodeArea='Addresses' and expression='gt' then 'MA_GT'
	when fieldCodeArea='Addresses' and expression='gte' then 'MA_GTE'
	when fieldCodeArea='Addresses' and expression='lt' then 'MA_LT'
	when fieldCodeArea='Addresses' and expression='lte' then 'MA_LTE'
	when fieldCodeArea='Addresses' and expression='neq' then 'MA_NEQ'
	when fieldCodeArea='Addresses' and expression='not_exists' then 'MA_NOTEXISTS'
	when fieldCodeArea='Custom Fields' and expression='datediff' then 'MD_DATEDIFF'
	when fieldCodeArea='Custom Fields' and expression='datepart' then 'MD_DATEPART'
	when fieldCodeArea='Custom Fields' and expression='contains' and dataTypeCode='STRING' then 'MD_CONTAINS_STRING'
	when fieldCodeArea='Custom Fields' and expression='contains_regex' and dataTypeCode='STRING' then 'MD_CONTAINSREGEX_STRING'
	when fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='STRING' then 'MD_EQ_STRING'
	when fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='BIT' then 'MD_EQ_BIT'
	when fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='INTEGER' then 'MD_EQ_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='DECIMAL2' then 'MD_EQ_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='DATE' then 'MD_EQ_DATE'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='STRING' then 'MD_EXISTS_STRING'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='BIT' then 'MD_EXISTS_BIT'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='INTEGER' then 'MD_EXISTS_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='DECIMAL2' then 'MD_EXISTS_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='DATE' then 'MD_EXISTS_DATE'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='CONTENTOBJ' then 'MD_EXISTS_CONTENTOBJ'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='DOCUMENTOBJ' then 'MD_EXISTS_DOCUMENTOBJ'
	when fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='STRING' then 'MD_GT_STRING'
	when fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='INTEGER' then 'MD_GT_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='DECIMAL2' then 'MD_GT_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='DATE' then 'MD_GT_DATE'
	when fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='STRING' then 'MD_GTE_STRING'
	when fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='INTEGER' then 'MD_GTE_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='DECIMAL2' then 'MD_GTE_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='DATE' then 'MD_GTE_DATE'
	when fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='STRING' then 'MD_LT_STRING'
	when fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='INTEGER' then 'MD_LT_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='DECIMAL2' then 'MD_LT_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='DATE' then 'MD_LT_DATE'
	when fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='STRING' then 'MD_LTE_STRING'
	when fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='INTEGER' then 'MD_LTE_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='DECIMAL2' then 'MD_LTE_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='DATE' then 'MD_LTE_DATE'
	when fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='STRING' then 'MD_NEQ_STRING'
	when fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='BIT' then 'MD_NEQ_BIT'
	when fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='INTEGER' then 'MD_NEQ_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='DECIMAL2' then 'MD_NEQ_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='DATE' then 'MD_NEQ_DATE'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='STRING' then 'MD_NOTEXISTS_STRING'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='INTEGER' then 'MD_NOTEXISTS_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='DECIMAL2' then 'MD_NOTEXISTS_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='DATE' then 'MD_NOTEXISTS_DATE'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='BIT' then 'MD_NOTEXISTS_BIT'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='CONTENTOBJ' then 'MD_NOTEXISTS_CONTENTOBJ'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='DOCUMENTOBJ' then 'MD_NOTEXISTS_DOCUMENTOBJ'
	when fieldCodeArea='Districting' and expression='eq' then 'MAD_EQ'
	when fieldCodeArea='Districting' and expression='exists' then 'MAD_EXISTS'
	when fieldCodeArea='Districting' and expression='neq' then 'MAD_NEQ'
	when fieldCodeArea='Districting' and expression='not_exists' then 'MAD_NOTEXISTS'
	when fieldCodeArea='Emails' and expression='contains' then 'ME_CONTAINS'
	when fieldCodeArea='Emails' and expression='contains_regex' then 'ME_CONTAINSREGEX'
	when fieldCodeArea='Emails' and expression='eq' then 'ME_EQ'
	when fieldCodeArea='Emails' and expression='exists' then 'ME_EXISTS'
	when fieldCodeArea='Emails' and expression='gt' then 'ME_GT'
	when fieldCodeArea='Emails' and expression='gte' then 'ME_GTE'
	when fieldCodeArea='Emails' and expression='lt' then 'ME_LT'
	when fieldCodeArea='Emails' and expression='lte' then 'ME_LTE'
	when fieldCodeArea='Emails' and expression='neq' then 'ME_NEQ'
	when fieldCodeArea='Emails' and expression='not_exists' then 'ME_NOTEXISTS'
	when fieldCodeArea='Events' and expression='registered' then 'E_REGISTERED'
	when fieldCodeArea='Events' and expression='attended' then 'E_ATTENDED'
	when fieldCodeArea='Events' and expression='awarded' then 'E_AWARDED'
	when fieldCodeArea='Listserver Memberships' and fieldCode='l_entry' and expression='onlist' then 'L_ONLIST'
	when fieldCodeArea='Member Data' and expression='contains' then 'M_CONTAINS'
	when fieldCodeArea='Member Data' and expression='contains_regex' then 'M_CONTAINSREGEX'
	when fieldCodeArea='Member Data' and expression='eq' and dataTypeCode='DATE' then 'M_EQ_DATE'
	when fieldCodeArea='Member Data' and expression='eq' and dataTypeCode='STRING' then 'M_EQ_STRING'
	when fieldCodeArea='Member Data' and expression='eq' and dataTypeCode='INTEGER' then 'M_EQ_INTEGER'
	when fieldCodeArea='Member Data' and expression='exists' and dataTypeCode='DATE' then 'M_EXISTS_DATE'
	when fieldCodeArea='Member Data' and expression='exists' then 'M_EXISTS'
	when fieldCodeArea='Member Data' and expression='gt' and dataTypeCode='DATE' then 'M_GT_DATE'
	when fieldCodeArea='Member Data' and expression='gt' and dataTypeCode='STRING' then 'M_GT_STRING'
	when fieldCodeArea='Member Data' and expression='gte' and dataTypeCode='DATE' then 'M_GTE_DATE'
	when fieldCodeArea='Member Data' and expression='gte' and dataTypeCode='STRING' then 'M_GTE_STRING'
	when fieldCodeArea='Member Data' and expression='lt' and dataTypeCode='DATE' then 'M_LT_DATE'
	when fieldCodeArea='Member Data' and expression='lt' and dataTypeCode='STRING' then 'M_LT_STRING'
	when fieldCodeArea='Member Data' and expression='lte' and dataTypeCode='DATE' then 'M_LTE_DATE'
	when fieldCodeArea='Member Data' and expression='lte' and dataTypeCode='STRING' then 'M_LTE_STRING'
	when fieldCodeArea='Member Data' and expression='neq' and dataTypeCode='DATE' then 'M_NEQ_DATE'
	when fieldCodeArea='Member Data' and expression='neq' and dataTypeCode='STRING' then 'M_NEQ_STRING'
	when fieldCodeArea='Member Data' and expression='neq' and dataTypeCode='INTEGER' then 'M_NEQ_INTEGER'
	when fieldCodeArea='Member Data' and expression='not_exists' and dataTypeCode='DATE' then 'M_NOTEXISTS_DATE'
	when fieldCodeArea='Member Data' and expression='not_exists' then 'M_NOTEXISTS'
	when fieldCodeArea='Member History' and fieldCode='mh_entry' and expression='exists' then 'MH_EXISTS'
	when fieldCodeArea='Phones' and expression='contains' then 'MP_CONTAINS'
	when fieldCodeArea='Phones' and expression='contains_regex' then 'MP_CONTAINSREGEX'
	when fieldCodeArea='Phones' and expression='eq' then 'MP_EQ'
	when fieldCodeArea='Phones' and expression='exists' then 'MP_EXISTS'
	when fieldCodeArea='Phones' and expression='gt' then 'MP_GT'
	when fieldCodeArea='Phones' and expression='gte' then 'MP_GTE'
	when fieldCodeArea='Phones' and expression='lt' then 'MP_LT'
	when fieldCodeArea='Phones' and expression='lte' then 'MP_LTE'
	when fieldCodeArea='Phones' and expression='neq' then 'MP_NEQ'
	when fieldCodeArea='Phones' and expression='not_exists' then 'MP_NOTEXISTS'
	when fieldCodeArea='Professional Licenses' and expression='contains' then 'MPL_CONTAINS'
	when fieldCodeArea='Professional Licenses' and expression='contains_regex' then 'MPL_CONTAINSREGEX'
	when fieldCodeArea='Professional Licenses' and expression='datepart' then 'MPL_DATEPART'
	when fieldCodeArea='Professional Licenses' and expression='datediff' then 'MPL_DATEDIFF'
	when fieldCodeArea='Professional Licenses' and expression='eq' and dataTypeCode='DATE' then 'MPL_EQ_DATE'
	when fieldCodeArea='Professional Licenses' and expression='eq' and dataTypeCode='STRING' then 'MPL_EQ_STRING'
	when fieldCodeArea='Professional Licenses' and expression='exists' and dataTypeCode='DATE' then 'MPL_EXISTS_DATE'
	when fieldCodeArea='Professional Licenses' and expression='exists' and dataTypeCode='STRING' then 'MPL_EXISTS_STRING'
	when fieldCodeArea='Professional Licenses' and expression='gt' and dataTypeCode='DATE' then 'MPL_GT_DATE'
	when fieldCodeArea='Professional Licenses' and expression='gt' and dataTypeCode='STRING' then 'MPL_GT_STRING'
	when fieldCodeArea='Professional Licenses' and expression='gte' and dataTypeCode='DATE' then 'MPL_GTE_DATE'
	when fieldCodeArea='Professional Licenses' and expression='gte' and dataTypeCode='STRING' then 'MPL_GTE_STRING'
	when fieldCodeArea='Professional Licenses' and expression='lt' and dataTypeCode='DATE' then 'MPL_LT_DATE'
	when fieldCodeArea='Professional Licenses' and expression='lt' and dataTypeCode='STRING' then 'MPL_LT_STRING'
	when fieldCodeArea='Professional Licenses' and expression='lte' and dataTypeCode='DATE' then 'MPL_LTE_DATE'
	when fieldCodeArea='Professional Licenses' and expression='lte' and dataTypeCode='STRING' then 'MPL_LTE_STRING'
	when fieldCodeArea='Professional Licenses' and expression='neq' and dataTypeCode='DATE' then 'MPL_NEQ_DATE'
	when fieldCodeArea='Professional Licenses' and expression='neq' and dataTypeCode='STRING' then 'MPL_NEQ_STRING'
	when fieldCodeArea='Professional Licenses' and expression='not_exists' and dataTypeCode='DATE' then 'MPL_NOTEXISTS_DATE'
	when fieldCodeArea='Professional Licenses' and expression='not_exists' and dataTypeCode='STRING' then 'MPL_NOTEXISTS_STRING'
	when fieldCodeArea='Record Types' and fieldCode='rt_role' and expression='linked' then 'RT_LINKED'
	when fieldCodeArea='Subscriptions' and expression='subscribed' then 'SUB_SUBSCRIBED'
	when fieldCodeArea='Websites' and expression='contains' then 'MW_CONTAINS'
	when fieldCodeArea='Websites' and expression='contains_regex' then 'MW_CONTAINSREGEX'
	when fieldCodeArea='Websites' and expression='eq' then 'MW_EQ'
	when fieldCodeArea='Websites' and expression='exists' then 'MW_EXISTS'
	when fieldCodeArea='Websites' and expression='gt' then 'MW_GT'
	when fieldCodeArea='Websites' and expression='gte' then 'MW_GTE'
	when fieldCodeArea='Websites' and expression='lt' then 'MW_LT'
	when fieldCodeArea='Websites' and expression='lte' then 'MW_LTE'
	when fieldCodeArea='Websites' and expression='neq' then 'MW_NEQ'
	when fieldCodeArea='Websites' and expression='not_exists' then 'MW_NOTEXISTS'
	else ''
	end

-- for the final processing at the end
IF OBJECT_ID('tempdb..#tblCondALLFinal') IS NOT NULL
	DROP TABLE #tblCondALLFinal
CREATE TABLE #tblCondALLFinal (conditionID int PRIMARY KEY);

insert into #tblCondALLFinal
select conditionID from #tblCondALL


-- put condition values into temp table by datatype to remove all casting in individual processing queries
IF OBJECT_ID('tempdb..#tblCondValues') IS NOT NULL
	DROP TABLE #tblCondValues
CREATE TABLE #tblCondValues (conditionID int, conditionKeyID int, conditionValueString varchar(max), conditionValueInteger int, conditionValueBit bit, conditionValueDecimal2 decimal(9,2), conditionValueDate datetime);

insert into #tblCondValues
select cv.conditionID, cv.conditionKeyID, null, cv.conditionValue, null, null, null
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where 
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'STRING' and tblc.displayTypeCode in ('RADIO','SELECT')) or
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'DECIMAL2' and tblc.displayTypeCode in ('RADIO','SELECT')) or 
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'DATE' and tblc.displayTypeCode in ('RADIO','SELECT')) or 
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq','lt','lte','gt','gte') and tblc.dataTypeCode = 'INTEGER') or
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('datepart','datediff')) or
	(tblc.fieldCodeArea = 'Addresses' and tblc.expression in ('eq','neq') and tblc.displayTypeCode in ('RADIO','SELECT')) or
	(tblc.fieldCodeArea = 'Professional Licenses' and tblc.expression in ('datepart','datediff')) or
	(tblc.fieldCodeArea = 'Member Data' and tblc.expression in ('datepart','datediff')) or
	(tblc.fieldCodeArea = 'Member Data' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'INTEGER') or
	(tblc.fieldCodeArea = 'Districting' and tblc.expression in ('eq','neq'))
	union all
select cv.conditionID, cv.conditionKeyID, cv.conditionValue, null, null, null, null
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'STRING' and tblc.displayTypeCode not in ('RADIO','SELECT')) or
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('lt','lte','gt','gte','contains','contains_regex') and tblc.dataTypeCode = 'STRING') or
	(tblc.fieldCodeArea = 'Addresses' and tblc.expression in ('eq','neq') and tblc.displayTypeCode not in ('RADIO','SELECT')) or
	(tblc.fieldCodeArea = 'Addresses' and tblc.expression in ('lt','lte','gt','gte','contains','contains_regex')) or
	(tblc.fieldCodeArea = 'Professional Licenses' and tblc.expression in ('eq','neq','lt','lte','gt','gte','contains','contains_regex') and tblc.dataTypeCode = 'STRING') or
	(tblc.fieldCodeArea = 'Member Data' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'STRING') or
	(tblc.fieldCodeArea = 'Member Data' and tblc.expression in ('lt','lte','gt','gte','contains','contains_regex') and tblc.dataTypeCode = 'STRING') or
	(tblc.fieldCodeArea = 'Websites') or
	(tblc.fieldCodeArea = 'Emails') or
	(tblc.fieldCodeArea = 'Phones')
	union all
select cv.conditionID, cv.conditionKeyID, null, null, cv.conditionValue, null, null
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where (tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'BIT')
	union all
select cv.conditionID, cv.conditionKeyID, null, null, null, cv.conditionValue, null
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'DECIMAL2' and tblc.displayTypeCode not in ('RADIO','SELECT')) or
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('lt','lte','gt','gte') and tblc.dataTypeCode = 'DECIMAL2')
	union all
select cv.conditionID, cv.conditionKeyID, null, null, null, null, cv.conditionValue
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'DATE' and tblc.displayTypeCode not in ('RADIO','SELECT')) or
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('lt','lte','gt','gte') and tblc.dataTypeCode = 'DATE') or
	(tblc.fieldCodeArea = 'Member Data' and tblc.expression in ('eq','neq','lt','lte','gt','gte') and tblc.dataTypeCode = 'DATE') or
	(tblc.fieldCodeArea = 'Professional Licenses' and tblc.expression in ('eq','neq','lt','lte','gt','gte') and tblc.dataTypeCode = 'DATE')

CREATE INDEX IX_tblCondValues_conditionID ON #tblCondValues (conditionID asc);


-- split ACCT data if necessary. This is here because several subProcs can use this data.
declare @acctCount int
select @acctCount = count(*) from #tblCondALL where fieldCodeArea = 'Accounting'
IF @acctCount > 0 BEGIN
	IF OBJECT_ID('tempdb..#tblAccSplit') IS NOT NULL
		DROP TABLE #tblAccSplit
	CREATE TABLE #tblAccSplit (conditionID int, revenueGLs varchar(max), batchDateLower datetime, batchDateUpper datetime, 
		revOrCash varchar(7), conditionValue money, conditionValueLower money, conditionValueUpper money);

	IF OBJECT_ID('tempdb..#tblAccSplitGL') IS NOT NULL
		DROP TABLE #tblAccSplitGL
	CREATE TABLE #tblAccSplitGL (conditionID int, revenueGL int);

	EXEC dbo.cache_members_populateMemberConditionCache_splitAcct
END


-- loop over subProcs to run
IF OBJECT_ID('tempdb..#cache_members_conditions_shouldbe') IS NOT NULL
	DROP TABLE #cache_members_conditions_shouldbe
CREATE TABLE #cache_members_conditions_shouldbe (memberid int, conditionID int);

declare @subProc varchar(30), @dynsql nvarchar(100)
select @subProc = min(subProc) from #tblCondALL where subProc <> ''
while @subProc is not null BEGIN
	SET @dynsql = 'EXEC dbo.cache_members_populateMemberConditionCache_' + @subProc
	EXEC sp_executesql @dynsql

	delete from #tblCondALL where subProc = @subProc

	select @subProc = min(subProc) from #tblCondALL where subProc <> '' and subProc > @subProc
END


-- Log
set @starttime2 = getdate()
INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage)
VALUES (@logTreeID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'Start changes to real tables');

-- delete member/conditions that should not be there
delete cmc
from dbo.cache_members_conditions as cmc
inner join #tblCondALLFinal as caf on caf.conditionID = cmc.conditionID
inner join #tblMembers as m on m.memberid = cmc.memberid
and not exists (
	select *
	from #cache_members_conditions_shouldbe
	where conditionID = cmc.conditionID
	and memberid = cmc.memberid
)	
		
-- insert member/conditions that should be but arent already there
insert into dbo.cache_members_conditions (memberID, conditionID)
select distinct cache.memberID, cache.conditionID
from #cache_members_conditions_shouldbe as cache
where not exists (
	select memberid, conditionid
	from dbo.cache_members_conditions
	where memberid = cache.memberid
	and conditionID = cache.conditionID
)

-- log
INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'End changes to real tables', Datediff(ms,@starttime2,getdate()));

-- return query of members affected
select distinct memberID
from #tblMembers
	
-- cleanup temp tables
IF OBJECT_ID('tempdb..#cache_members_conditions_shouldbe') IS NOT NULL
	DROP TABLE #cache_members_conditions_shouldbe
IF OBJECT_ID('tempdb..#tblCond') IS NOT NULL
	DROP TABLE #tblCond
IF OBJECT_ID('tempdb..#tblCondALL') IS NOT NULL
	DROP TABLE #tblCondALL
IF OBJECT_ID('tempdb..#tblCondALLFinal') IS NOT NULL
	DROP TABLE #tblCondALLFinal
IF OBJECT_ID('tempdb..#tblAccSplit') IS NOT NULL
	DROP TABLE #tblAccSplit
IF OBJECT_ID('tempdb..#tblAccSplitGL') IS NOT NULL
	DROP TABLE #tblAccSplitGL
IF OBJECT_ID('tempdb..#tblCondValues') IS NOT NULL
	DROP TABLE #tblCondValues
IF OBJECT_ID('tempdb..#tblMembers') IS NOT NULL
	DROP TABLE #tblMembers

-- Log
set @totalMS = Datediff(ms,@starttime,getdate())
INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'End Process of orgID=' + cast(@orgID as varchar(10)) + ' conditionID=' + left(@conditionIDList,100) + ' memberID=' + left(@memberIDList,100), @totalMS);

set nocount off

GO

/*  (22) --------------------------------------------------------------------------------------------------------------------- */

if not exists(select * from sys.columns 
            where Name = N'maxNumChar' and Object_ID = Object_ID(N'ams_memberDataColumns'))
begin
	alter table ams_memberDataColumns
		add maxNumChar int null
end
GO

/*  (23) --------------------------------------------------------------------------------------------------------------------- */

ALTER PROC [dbo].[ams_createMemberDataColumn]
@orgID int,
@columnName varchar(255),
@columnDesc varchar(255),
@allowMultiple bit,
@skipImport bit,
@allowNull bit,
@defaultValue varchar(max),
@allowNewValuesOnImport bit,
@dataTypeCode varchar(20),
@displayTypeCode varchar(20),
@isReadOnly bit = 0,
@maxNumChar int,
@columnID int OUTPUT

AS

-- check for existing column with same name
SELECT @columnID = null

-- if there, return 0
IF EXISTS(select emailTypeID from dbo.ams_memberEmailTypes where orgID = @orgID and emailType = @columnName)
OR EXISTS(select websiteTypeID from dbo.ams_memberWebsiteTypes where orgID = @orgID and websiteType = @columnName)
OR EXISTS(select columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @columnName)
OR EXISTS(select C.NAME FROM SYSCOLUMNS C INNER JOIN SYSOBJECTS O ON C.ID = O.ID WHERE O.NAME = 'ams_members' and  C.NAME = @columnName)
	SELECT @columnID = 0

-- if not there, add it
ELSE BEGIN
	DECLARE @rc int, @valueID int
	
	DECLARE @dataTypeID int, @displayTypeID int
	SELECT @dataTypeID = dataTypeID from dbo.ams_memberDataColumnDataTypes where dataTypeCode = @dataTypeCode
		IF @@ERROR <> 0 OR @dataTypeID is null GOTO on_error
	SELECT @displayTypeID = displayTypeID from dbo.ams_memberDataColumnDisplayTypes where displayTypeCode = @displayTypeCode
		IF @@ERROR <> 0 OR @displayTypeID is null GOTO on_error

	-- validate display type when multiple
	IF @allowMultiple = 1 and @displayTypeCode = 'RADIO' BEGIN
		SELECT @displayTypeCode = 'CHECKBOX'
		SELECT @displayTypeID = displayTypeID from dbo.ams_memberDataColumnDisplayTypes where displayTypeCode = @displayTypeCode
			IF @@ERROR <> 0 OR @displayTypeID is null GOTO on_error
	END

	-- validations
	IF @displayTypeCode IN ('DOCUMENT','TEXTAREA','HTMLCONTENT') OR @dataTypeCode in ('XML','CONTENTOBJ','DOCUMENTOBJ') BEGIN
		SELECT @allowNull = 1
	END
	IF @displayTypeCode IN ('DOCUMENT') OR @dataTypeCode in ('DOCUMENTOBJ') BEGIN
		SELECT @skipImport = 1
	END
	IF @allowNull = 0 and len(isnull(@defaultValue,'')) = 0
		SELECT @allowNull = 1
	IF @skipImport = 1
		SELECT @allowNewValuesOnImport = 1
	IF @allowNull = 1
		SELECT @defaultValue = ''

	-- add column
	INSERT INTO dbo.ams_memberDataColumns (orgID, columnName, columnDesc, skipImport, 
		dataTypeID, displayTypeID, allowNull, allowNewValuesOnImport, defaultValueID, isReadOnly, allowMultiple, maxNumChar)
	VALUES (@orgID, @columnName, @columnDesc, @skipImport, @dataTypeID, @displayTypeID,
		@allowNull, @allowNewValuesOnImport, null, @isReadOnly, @allowMultiple, @maxNumChar)
		IF @@ERROR <> 0 GOTO on_error
		SELECT @columnID = SCOPE_IDENTITY()
	
	-- if adding a bit column, add the two values automatically
	IF @dataTypeCode = 'BIT' BEGIN
		EXEC @rc = dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=0, @valueID=@valueID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @valueID = 0 GOTO on_error
		EXEC @rc = dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=1, @valueID=@valueID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 OR @valueID = 0 GOTO on_error
	END

	-- set default valueID if necessary
	IF len(isnull(@defaultValue,'')) > 0 BEGIN
		EXEC @rc = dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=@defaultValue, @valueID=@valueID OUTPUT
			IF @@ERROR <> 0 or @rc <> 0 or @valueID = 0 GOTO on_error
		UPDATE dbo.ams_memberDataColumns 
		SET defaultValueID = @valueID
		WHERE columnID = @columnID
			IF @@ERROR <> 0 GOTO on_error
	
		-- Anyone who doesnt have a value for this column needs this value.
		declare @tblMDDEF TABLE (memberid int PRIMARY KEY)
		insert into @tblMDDEF (memberid)
		select distinct m.memberid
		from dbo.ams_members as m
		left outer join dbo.ams_memberData as md 
			inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID and mdcv.columnID = @columnID
			on md.memberid = m.memberID
		where m.orgID = @orgID
		and m.memberid = m.activememberid
		and m.status <> 'D'
		and md.dataid is null
			IF @@ERROR <> 0 GOTO on_error

		INSERT INTO dbo.ams_memberData (memberid, valueID)
		select memberid, @valueID
		from @tblMDDEF
			IF @@ERROR <> 0 GOTO on_error

		UPDATE dbo.ams_members
		SET dateLastUpdated = getdate()
		WHERE memberID in (select memberID from @tblMDDEF)
			IF @@ERROR <> 0 GOTO on_error

		-- no need to put anything into queue processing of member groups
		-- since this is a new field so no conditions could refer to it yet
	END 

	-- recreate view for org
	EXEC dbo.ams_createVWMemberData	@orgID=@orgID
		IF @@ERROR <> 0 GOTO on_error
END

-- normal return
RETURN 0

-- error exit
on_error:
	SELECT @columnID = 0
	RETURN -1
GO

/*  (24) --------------------------------------------------------------------------------------------------------------------- */

ALTER PROC [dbo].[ams_updateMemberDataColumn]
@columnID int,
@columnName varchar(255),
@columnDesc varchar(255),
@allowMultiple bit,
@skipImport bit,
@allowNull bit,
@defaultValue varchar(max),
@allowNewValuesOnImport bit,
@dataTypeCode varchar(20),
@displayTypeCode varchar(20),
@isReadOnly bit = 0,
@maxNumChar int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	DECLARE @valueID int, @rc int, @orgID int, @displayTypeID int, @dataTypeID int, @newbitvalueID int
	DECLARE @oldColumnName varchar(255), @olddataTypeCode varchar(20), @olddisplayTypeCode varchar(20)
	DECLARE @memberIDList varchar(max), @conditionIDList varchar(max)

	IF @columnID is not null AND @columnName is not null 
	BEGIN

		SELECT @orgID = orgID from dbo.ams_memberdataColumns where columnID = @columnID

		IF EXISTS(select emailTypeID from dbo.ams_memberEmailTypes where orgID = @orgID and emailType = @columnName)
		OR EXISTS(select websiteTypeID from dbo.ams_memberWebsiteTypes where orgID = @orgID and websiteType = @columnName)
		OR EXISTS(select columnID from dbo.ams_memberDataColumns where orgID = @orgID and columnName = @columnName and columnID <> @columnID)
		OR EXISTS(select C.NAME FROM SYSCOLUMNS C INNER JOIN SYSOBJECTS O ON C.ID = O.ID WHERE O.NAME = 'ams_members' and  C.NAME = @columnName)
			RAISERROR('That column name is reserved or already in use.', 16, 1) 
		ELSE BEGIN
			SELECT @oldColumnName = columnName from dbo.ams_memberDataColumns where columnID = @columnID

			-- validate display type when multiple
			IF @allowMultiple = 1 and @displayTypeCode = 'RADIO'
				SELECT @displayTypeCode = 'CHECKBOX'
			IF @allowMultiple = 0 and @displayTypeCode = 'CHECKBOX'
				SELECT @displayTypeCode = 'RADIO'

			-- validations
			SELECT @olddataTypeCode = dt.dataTypeCode
				from dbo.ams_memberDataColumns as c
				inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = c.dataTypeID
				and c.columnID = @columnID
			SELECT @olddisplayTypeCode = dt.displayTypeCode
				from dbo.ams_memberDataColumns as c
				inner join dbo.ams_memberDataColumnDisplayTypes as dt on dt.displayTypeID = c.displayTypeID
				and c.columnID = @columnID
			SELECT @displayTypeID = displayTypeID
				from dbo.ams_memberDataColumnDisplayTypes
				where displayTypeCode = @displayTypeCode
			SELECT @dataTypeID = dataTypeID
				from dbo.ams_memberDataColumnDataTypes
				where dataTypeCode = @dataTypeCode
			IF @displayTypeCode IN ('DOCUMENT','TEXTAREA','HTMLCONTENT') OR @dataTypeCode in ('XML','CONTENTOBJ','DOCUMENTOBJ')
				SELECT @allowNull = 1
			IF @displayTypeCode IN ('DOCUMENT') OR @dataTypeCode in ('DOCUMENTOBJ')
				SELECT @skipImport = 1
			IF @allowNull = 0 and len(isnull(@defaultValue,'')) = 0
				SELECT @allowNull = 1
			IF @skipImport = 1
				SELECT @allowNewValuesOnImport = 1
			IF @allowNull = 1
				SELECT @defaultValue = ''

			-- set default valueID if necessary
			IF len(isnull(@defaultValue,'')) > 0 BEGIN
				EXEC @rc = dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=@defaultValue, @valueID=@valueID OUTPUT
					IF @rc <> 0 or @valueID = 0 SELECT @allowNull = 1
			END 

			-- update column info
			UPDATE dbo.ams_memberDataColumns 
			SET columnName = @columnName,
				columnDesc = @columnDesc,
				skipImport = @skipImport,
				allowNull = @allowNull,
				defaultValueID = nullif(@valueID,0),
				allowNewValuesOnImport = @allowNewValuesOnImport,
				isReadOnly = @isReadOnly,
				allowMultiple = @allowMultiple,
				maxNumChar = @maxNumChar 
			WHERE columnID = @columnID

			-- if changing the display type
			IF @displayTypeCode <> @olddisplayTypeCode BEGIN
				UPDATE dbo.ams_memberDataColumns
				SET displayTypeID = @displayTypeID
				WHERE columnID = @columnID

				UPDATE dbo.ams_memberFields
				SET displayTypeID = @displayTypeID
				WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))

				-- if was a radio/select/checkbox (not bit) and is no longer that, we need to convert valueID to value
				IF @olddataTypeCode <> 'BIT' and @dataTypeCode <> 'BIT' and @olddisplayTypeCode in ('RADIO','SELECT','CHECKBOX') and @displayTypeCode not in ('RADIO','SELECT','CHECKBOX') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = coalesce(mdcv.columnValueString, cast(mdcv.columnValueDecimal2 as varchar(15)), cast(mdcv.columnValueInteger as varchar(15)), convert(varchar(10),mdcv.columnvalueDate,101))
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
				END

				-- if was NOT a radio/select/checkbox (not bit) and is now that, we need to convert value to valueID
				IF @olddataTypeCode <> 'BIT' and @dataTypeCode <> 'BIT' and @olddisplayTypeCode not in ('RADIO','SELECT','CHECKBOX') and @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
					
					-- err if any expressions are not 1,2,7,8 now that it will be a select
					IF EXISTS (select conditionID from dbo.ams_virtualGroupConditions where fieldCode = 'md_' + cast(@columnID as varchar(10)) and expressionID not in (1,2,7,8))
						RAISERROR('There are group assignment conditions that are not compatible with the selected display type.', 16, 1) 

					-- create column values for those condition values that dont yet exist as column values
					declare @tblVals TABLE (newVal varchar(max))
					insert into @tblVals (newVal)
					select vgcv.conditionValue
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.dataTypeID = 1
					and vgc.expressionID in (1,2)
					and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueString = vgcv.conditionValue)
						union
					select cast(vgcv.conditionValue as varchar(15))
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.dataTypeID = 2
					and vgc.expressionID in (1,2)
					and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueDecimal2 = cast(vgcv.conditionValue as decimal(9,2)))
						union
					select cast(vgcv.conditionValue as varchar(15))
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.dataTypeID = 3
					and vgc.expressionID in (1,2)
					and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueInteger = cast(vgcv.conditionValue as int))
						union
					select convert(varchar(10),vgcv.conditionValue,101)
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.dataTypeID = 4
					and vgc.expressionID in (1,2)
					and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueDate = cast(vgcv.conditionValue as datetime))

					DECLARE @newvalueID int, @minValue varchar(max)
					select @minValue = min(newVal) from @tblVals
					while @minValue is not null BEGIN
						EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=@minValue, @valueID=@newvalueID OUTPUT
						select @minValue = min(newVal) from @tblVals where newVal > @minValue
					END

					-- get the valueID
					UPDATE vgcv
					SET vgcv.conditionValue = tmp.valueID
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					INNER JOIN (
						select vgc.conditionID, mdcv.valueID
						from dbo.ams_virtualGroupConditions as vgc
						INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
						and vgc.expressionID in (1,2)
						and vgc.dataTypeID = 1 
						and vgcv.conditionValue = mdcv.columnvalueString
							union
						select vgc.conditionID, mdcv.valueID
						from dbo.ams_virtualGroupConditions as vgc
						INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
						and vgc.expressionID in (1,2)
						and vgc.dataTypeID = 2 
						and cast(vgcv.conditionValue as decimal(9,2)) = mdcv.columnvalueDecimal2
							union
						select vgc.conditionID, mdcv.valueID
						from dbo.ams_virtualGroupConditions as vgc
						INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
						and vgc.expressionID in (1,2)
						and vgc.dataTypeID = 3 
						and cast(vgcv.conditionValue as int) = mdcv.columnvalueInteger
							union
						select vgc.conditionID, mdcv.valueID
						from dbo.ams_virtualGroupConditions as vgc
						INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
						and vgc.expressionID in (1,2)
						and vgc.dataTypeID = 4 
						and cast(vgcv.conditionValue as datetime) = mdcv.columnvalueDate
					) as tmp on tmp.conditionID = vgc.conditionID
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
				END

				UPDATE dbo.ams_virtualGroupConditions
				SET displayTypeID = @displayTypeID
				WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))

				UPDATE dbo.ams_virtualGroupConditions
				set [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
				WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))
			END

			-- if changing the data type
			IF @dataTypeCode <> @olddataTypeCode BEGIN
				UPDATE dbo.ams_memberDataColumns
				SET dataTypeID = @dataTypeID
				WHERE columnID = @columnID

				UPDATE dbo.ams_memberFields
				SET dataTypeID = @dataTypeID
				WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))

				-- check ams_virtualGroupConditions for expression conflicts
				IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'DECIMAL2' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueDecimal2 = cast(columnValueString as decimal(9,2))
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are string values not compatible with the Decimal Number (2) data type.', 16, 1) 
					END CATCH

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = null
					where columnID = @columnID

					IF EXISTS (
						select conditionID
						from dbo.ams_virtualGroupConditions
						where fieldCode = 'md_' + cast(@columnID as varchar(10))
						and expressionID in (9,10)
					) RAISERROR('There are group assignment conditions that are not compatible with the Decimal Number (2) data type.', 16, 1) 
				END
				IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'INTEGER' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueInteger = cast(columnValueString as int)
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are string values not compatible with the Whole Number data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = null
					where columnID = @columnID

					IF EXISTS (
						select conditionID
						from dbo.ams_virtualGroupConditions
						where fieldCode = 'md_' + cast(@columnID as varchar(10))
						and expressionID in (9,10)
					) RAISERROR('There are group assignment conditions that are not compatible with the Whole Number data type.', 16, 1) 
				END
				IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'DATE' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueDate = cast(columnValueString as datetime)
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are string values not compatible with the Date data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = null
					where columnID = @columnID

					IF EXISTS (
						select conditionID
						from dbo.ams_virtualGroupConditions
						where fieldCode = 'md_' + cast(@columnID as varchar(10))
						and expressionID in (9,10)
					) RAISERROR('There are group assignment conditions that are not compatible with the Date data type.', 16, 1) 
				END
				IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'BIT' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueBit = cast(columnValueString as bit)
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are string values not compatible with the Boolean data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = null
					where columnID = @columnID

					-- ensure both bit values are there					
					EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='1', @valueID=@newbitvalueID OUTPUT
					EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='0', @valueID=@newbitvalueID OUTPUT

					IF EXISTS (
						select conditionID
						from dbo.ams_virtualGroupConditions
						where fieldCode = 'md_' + cast(@columnID as varchar(10))
						and expressionID in (3,4,5,6,9,10)
					) RAISERROR('There are group assignment conditions that are not compatible with the Boolean data type.', 16, 1) 

					-- if was a radio/select/checkbox, we need to convert valueID to value because BIT doesnt store valueID
					IF @olddisplayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
						UPDATE vgcv
						SET vgcv.conditionValue = mdcv.columnValueBit
						FROM dbo.ams_virtualGroupConditions as vgc
						INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
							and cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue
						WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
						and vgc.expressionID in (1,2)
					END
				END
				IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'XML' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueXML = cast(columnValueString as xml)
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are string values not compatible with the XML data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = null
					where columnID = @columnID

					IF EXISTS (
						select conditionID
						from dbo.ams_virtualGroupConditions
						where fieldCode = 'md_' + cast(@columnID as varchar(10))
						and expressionID in (1,2,3,4,5,6,9,10)
					) RAISERROR('There are group assignment conditions that are not compatible with the XML data type.', 16, 1) 
				END
				IF @olddataTypeCode = 'DECIMAL2' AND @dataTypeCode = 'STRING' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueString = cast(columnValueDecimal2 as varchar(255))
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are decimal values not compatible with the Text String data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueDecimal2 = null
					where columnID = @columnID
				END
				IF @olddataTypeCode = 'INTEGER' AND @dataTypeCode = 'STRING' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueString = cast(columnValueInteger as varchar(255))
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are whole number values not compatible with the Text String data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueInteger = null
					where columnID = @columnID
				END
				IF @olddataTypeCode = 'INTEGER' AND @dataTypeCode = 'DECIMAL2' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueDecimal2 = cast(columnValueInteger as decimal(9,2))
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are whole number values not compatible with the Decimal Number (2) data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueInteger = null
					where columnID = @columnID
				END
				IF @olddataTypeCode = 'INTEGER' AND @dataTypeCode = 'BIT' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueBit = cast(columnValueInteger as bit)
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are whole number values not compatible with the Boolean data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueInteger = null
					where columnID = @columnID

					-- ensure both bit values are there					
					EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='1', @valueID=@newbitvalueID OUTPUT
					EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='0', @valueID=@newbitvalueID OUTPUT

					IF EXISTS (
						select conditionID
						from dbo.ams_virtualGroupConditions
						where fieldCode = 'md_' + cast(@columnID as varchar(10))
						and expressionID in (3,4,5,6)
					) RAISERROR('There are group assignment conditions that are not compatible with the Boolean data type.', 16, 1) 

					-- if was a radio/select/checkbox, we need to convert valueID to value because BIT doesnt store valueID
					IF @olddisplayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
						UPDATE vgcv
						SET vgcv.conditionValue = mdcv.columnValueBit
						FROM dbo.ams_virtualGroupConditions as vgc
						INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
							and cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue
						WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
						and vgc.expressionID in (1,2)
					END
				END
				IF @olddataTypeCode = 'DATE' AND @dataTypeCode = 'STRING' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueString = convert(varchar(10),columnValueDate,101)
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are date values not compatible with the Text String data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueDate = null
					where columnID = @columnID

					IF EXISTS (
						select conditionID
						from dbo.ams_virtualGroupConditions
						where fieldCode = 'md_' + cast(@columnID as varchar(10))
						and expressionID in (11,12)
					) RAISERROR('There are group assignment conditions that are not compatible with the Text String data type.', 16, 1) 
				END
				IF @olddataTypeCode = 'BIT' AND @dataTypeCode = 'STRING' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueString = cast(columnValueBit as varchar(255))
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are boolean values not compatible with the Text String data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueBit = null
					where columnID = @columnID

					-- if going to be radio/select/checkbox, we need to convert value to valueID because BIT doesnt store valueID
					IF @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
						UPDATE vgcv
						SET vgcv.conditionValue = mdcv.valueID
						FROM dbo.ams_virtualGroupConditions as vgc
						INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
							and mdcv.columnvalueString = vgcv.conditionValue
						WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
						and vgc.expressionID in (1,2)
					END
				END
				IF @olddataTypeCode = 'BIT' AND @dataTypeCode = 'DECIMAL2' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueDecimal2 = cast(columnValueBit as decimal(9,2))
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are boolean values not compatible with the Decimal Number (2) data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueBit = null
					where columnID = @columnID

					-- if going to be radio/select/checkbox, we need to convert value to valueID because BIT doesnt store valueID
					IF @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
						UPDATE vgcv
						SET vgcv.conditionValue = mdcv.valueID
						FROM dbo.ams_virtualGroupConditions as vgc
						INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
							and mdcv.columnvalueDecimal2 = vgcv.conditionValue
						WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
						and vgc.expressionID in (1,2)
					END
				END
				IF @olddataTypeCode = 'BIT' AND @dataTypeCode = 'INTEGER' BEGIN
					BEGIN TRY
						UPDATE dbo.ams_memberDataColumnValues
						SET columnValueInteger = cast(columnValueBit as int)
						where columnID = @columnID
					END TRY
					BEGIN CATCH
						RAISERROR('There are boolean values not compatible with the Whole Number data type.', 16, 1) 
					END CATCH					

					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueBit = null
					where columnID = @columnID

					-- if going to be radio/select/checkbox, we need to convert value to valueID because BIT doesnt store valueID
					IF @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
						UPDATE vgcv
						SET vgcv.conditionValue = mdcv.valueID
						FROM dbo.ams_virtualGroupConditions as vgc
						INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
						inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
							and cast(mdcv.columnvalueInteger as varchar(15)) = vgcv.conditionValue
						WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
						and vgc.expressionID in (1,2)
					END
				END

				UPDATE dbo.ams_virtualGroupConditions
				set dataTypeID = @dataTypeID
				WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))
			
				UPDATE dbo.ams_virtualGroupConditions
				set [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
				WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))
			END

			-- if valueID is not null, there is a def value. 
			-- Anyone who doesnt have a value for this column needs this value.
			IF nullif(@valueID,0) is not null BEGIN
				IF OBJECT_ID('tempdb..#tblMDDEF') IS NOT NULL 
					DROP TABLE #tblMDDEF
				CREATE TABLE #tblMDDEF (memberID int PRIMARY KEY)

				insert into #tblMDDEF (memberID)
				select distinct m.memberid
				from dbo.ams_members as m
				where m.orgID = @orgID
				and m.memberid = m.activememberid
				and m.status <> 'D'
					except
				select distinct md.memberID
				from dbo.ams_memberData as md 
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
				where mdcv.columnID = @columnID

				INSERT INTO dbo.ams_memberData (memberid, valueID)
				select memberid, @valueID
				from #tblMDDEF

				UPDATE m
				SET m.dateLastUpdated = getdate()
				FROM dbo.ams_members as m
				INNER JOIN #tblMDDEF as tmp on tmp.memberID = m.memberID

				-- queue processing of member groups (@runSchedule=2 indicates delayed processing)
				-- run this at end, outside of transaction, to speed it up 
				SELECT @conditionIDList = COALESCE(@conditionIDList + ',', '') + cast(c.conditionID as varchar(10)) 
					from dbo.ams_virtualGroupConditions as C
					where c.orgID = @orgID
					and C.fieldcode = 'md_' + Cast(@columnID as varchar(10))
					group by c.conditionID
				IF @conditionIDList is not NULL BEGIN				
					SELECT @memberIDList = COALESCE(@memberIDList + ',', '') + cast(m.memberID as varchar(10)) 
						from #tblMDDEF as m 
						group by m.memberID
				END

				IF OBJECT_ID('tempdb..#tblMDDEF') IS NOT NULL 
					DROP TABLE #tblMDDEF
			END

			-- if there was a change in columnname
			IF @oldColumnName <> @columnName COLLATE Latin1_General_CS_AI BEGIN
				-- update member fields
				UPDATE dbo.ams_memberFields
				SET dbField = @columnName
				WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))
			
				-- update virtual group conditions
				UPDATE dbo.ams_virtualGroupConditions
				SET [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
				WHERE fieldCode = 'md_' + cast(@columnID as varchar(10))
			END

			IF @oldColumnName <> @columnName OR @dataTypeCode <> @olddataTypeCode BEGIN
				EXEC dbo.ams_createVWMemberData	@orgID=@orgID
			END

		END

	END

	IF @TranCounter = 0
		COMMIT TRAN;

	-- if we need to call processMemberGroups
	IF @conditionIDList is not null BEGIN
		declare @itemGroupUID uniqueidentifier
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberIDList, @conditionIDList=@conditionIDList, @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT
	END

	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

/*  (25) --------------------------------------------------------------------------------------------------------------------- */

ALTER PROC [dbo].[ams_getMemberFields]
@fieldsetID int

AS

select isnull(cast((
	select mfs.fieldsetName as '@fieldsetName', mfs.nameformat as '@nameformat', mfs.fieldSetID as '@fieldsetID', mfs.showHelp as '@showHelp',
		(

		select mf.fieldID, mf.dbObject, mf.dbObjectAlias, mf.dbField, mf.fieldCode, mdColumnID,
			mf.fieldLabel, mf.fieldDescription, mf.isRequired, mf.isGrouped, mf.displayTypeCode, mf.dataTypeCode, mf.isReadOnly, mf.maxNumChar,
			mf.allowMultiple, opt.columnValueDecimal2, opt.columnValueInteger, opt.columnvalueDate, opt.columnValueBit, 
			opt.columnValueXML, opt.columnValueSiteResourceID, 
			case when left(mf.fieldCode,4) = 'grp_' or left(mf.fieldCode,5) = 'acct_' then 0 else opt.valueID end as valueID, 
			case 
				when mf.dbObjectAlias = 'subs' and left(mf.dbField,3) = 'sub' then (select coalesce(reportCode,subscriptionName) from dbo.sub_subscriptions where subscriptionID = parseName(replace(mf.dbField,'_','.'),2)) 
				when mf.dbObjectAlias = 'subs' then (select typeName from dbo.sub_Types where typeID = parseName(replace(mf.dbField,'_','.'),2)) 
				when mf.dbObjectAlias = 'grps' then (select coalesce(groupcode,groupName) from dbo.ams_groups where groupID = parseName(replace(mf.fieldCode,'_','.'),1)) 
				when mf.dbObjectAlias = 'acct' and left(mf.dbField,13) = 'acct_balance_' then isnull((select profileName from dbo.mp_profiles where profileID = parseName(replace(mf.dbField,'_','.'),1)),'Total') 
				else opt.columnValueString 
				end as columnValueString
		from (
			select amf.fieldID, amf.dbObject, amf.dbObjectAlias, amf.dbField, amf.fieldCode, amf.fieldLabel, amf.fieldDescription, 
				amf.isRequired, amf.isGrouped, dt.displayTypeCode, ddt.dataTypeCode, amf.fieldOrder,
				case 
				when left(amf.fieldcode,4) = 'mad_' then 1
				when left(amf.fieldCode,3) = 'md_' then (select isReadOnly from ams_memberDataColumns where columnID = cast(replace(amf.fieldCode,'md_','') as int)) 
				else 0 
				end as isReadOnly,
				case when left(amf.fieldCode,3) = 'md_' then (select maxNumChar from ams_memberDataColumns where columnID = cast(replace(amf.fieldCode,'md_','') as int)) else 0 end as maxNumChar,
				case when left(amf.fieldCode,3) = 'md_' then (select allowMultiple from ams_memberDataColumns where columnID = cast(replace(amf.fieldCode,'md_','') as int)) else 0 end as allowMultiple,
				case when dt.displayTypeCode in ('SELECT','RADIO','CHECKBOX') and left(amf.fieldCode,3) = 'md_' then replace(amf.fieldCode,'md_','') else 0 end as mdColumnID
			from dbo.ams_memberFields as amf
			inner join dbo.ams_memberDataColumnDisplayTypes as dt on dt.displayTypeID = amf.displayTypeID
			inner join dbo.ams_memberDataColumnDataTypes as ddt on ddt.dataTypeID = amf.dataTypeID
			where amf.fieldsetID = mfs.fieldsetID		
		) as mf
		left outer join dbo.ams_memberDataColumnValues as opt on opt.columnID = mf.mdColumnID and mf.mdColumnID > 0	
		
		order by mf.isGrouped desc, mf.fieldOrder, opt.columnValueString, opt.columnValueDecimal2,
			opt.columnValueInteger, opt.columnvalueDate, opt.columnValueBit,
			cast(opt.columnValueXML as varchar(max)), opt.columnValueSiteResourceID
		FOR XML AUTO, TYPE
		)
	from dbo.ams_memberFieldSets as mfs
	where mfs.fieldsetID = @fieldsetid
	FOR XML PATH('fields')
) as XML),'<fields/>') as fieldsXML

RETURN 0
GO

/*  (26) --------------------------------------------------------------------------------------------------------------------- */

if not exists(select * from sys.columns 
            where Name = N'descriptionContentID' and Object_ID = Object_ID(N'ams_memberFieldSets'))
begin
	alter table ams_memberFieldSets
		add descriptionContentID int null
end
GO

/*  (27) --------------------------------------------------------------------------------------------------------------------- */

if not exists(select * from sys.columns 
            where Name = N'descriptionPosition' and Object_ID = Object_ID(N'ams_memberFieldSets'))
begin
	alter table ams_memberFieldSets
		add descriptionPosition varchar(25) null
end
GO

/*  (28) --------------------------------------------------------------------------------------------------------------------- */

-- Create and assign content objects to fieldsets
-- Took 2:58 to run on DEV
declare 
	@rc int, 
	@appCreatedContentResourceTypeID int, 
	@activeSiteResourceStatusID int,
	@descriptionContentID int, 
	@descriptionContentSiteResourceID int,
	@siteID int,
	@fieldsetID int,
	@fieldSetName varchar(500),
	@thisRow int
declare @memberFieldSetsTbl as table (row int IDENTITY(1,1), fieldsetID int,  fieldsetName  varchar	(200), siteID int)

select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')
select @activeSiteResourceStatusID = dbo.fn_getResourceStatusId('Active')

insert into @memberFieldSetsTbl
select 
	fieldsetID,
	fieldsetName,
	siteID
from
	ams_memberFieldSets
where
	descriptionContentID is null

select @thisRow = min(row) from @memberFieldSetsTbl

while @thisRow is not null begin 
	-- print @thisRow

	select 
		@fieldSetName = fieldsetName,
		@fieldsetID = fieldsetID,
		@siteID = siteID
	from 
		@memberFieldSetsTbl
	where
		row =  @thisRow

	 
		print @fieldSetName
		print @fieldsetID
		print @siteID
		print '-------'

	-- create descriptionContent
	EXEC @rc = dbo.cms_createContentObject 
		@siteID=@siteID, 
		@resourceTypeID=@appCreatedContentResourceTypeID, 
		@siteResourceStatusID=@activeSiteResourceStatusID, 
		@isSSL=0, 
		@isHTML=1, 
		@languageID=1, 
		@isActive=1, 
		@contentTitle=@fieldSetName, 
		@contentDesc=null, 
		@rawContent='', 
		@contentID=@descriptionContentID OUTPUT, 
		@siteResourceID=@descriptionContentSiteResourceID OUTPUT

	update
		ams_memberFieldSets
	set
		descriptionContentID = @descriptionContentID,
		descriptionPosition = 'above'
	where
		fieldsetID = @fieldsetID

	select @thisRow = min(row) from @memberFieldSetsTbl where row >  @thisRow
end
GO

/*  (29) --------------------------------------------------------------------------------------------------------------------- */

ALTER PROC [dbo].[ams_createMemberFieldset]
@siteID int,
@fieldsetName varchar(200),
@nameformat varchar(10),
@showHelp int,
@enteredByMemberID int,
@defaultLanguageID int,
@fieldsetID int OUTPUT

AS

SELECT @fieldsetID = null

declare 
	@appCreatedContentResourceTypeID int, 
	@activeSiteResourceStatusID int, 
	@descContentSiteResourceID int,
	@descriptionContentID int,
	@rc int

select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')
select @activeSiteResourceStatusID = dbo.fn_getResourceStatusId('Active')

-- create descriptionContentID
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
		@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@defaultLanguageID, 
		@isActive=1, @contentTitle=@fieldsetName, @contentDesc=null, @rawContent='', 
		@memberID=@enteredByMemberID,
		@contentID=@descriptionContentID OUTPUT, 
		@siteResourceID=@descContentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

INSERT INTO dbo.ams_memberFieldSets (siteID, fieldsetName, nameformat, showHelp, [uid], descriptionContentID)
VALUES (@siteID, @fieldsetName, @nameformat, @showHelp, newid(), @descriptionContentID)
	IF @@ERROR <> 0 GOTO on_error
	SELECT @fieldsetID = SCOPE_IDENTITY()

RETURN 0

-- error exit
on_error:
	SELECT @fieldsetID = 0
	RETURN -1
GO

/*  (30) --------------------------------------------------------------------------------------------------------------------- */

if not exists(select * from sys.columns 
            where Name = N'showFieldSetDescription' and Object_ID = Object_ID(N'sites'))
begin
	alter table sites
		add showFieldSetDescription bit
end
GO

update
	sites
set
	showFieldSetDescription = 0
GO

/*  (31) --------------------------------------------------------------------------------------------------------------------- */

declare 
	@autoID int,
	@siteResourceID int,
	@include bit,
	@functionID int,
	@roleID int,
	@groupID int,
	@inheritedRightsResourceID int,
	@inheritedRightsFunctionID int,
	@siteID int,
	@trashID int

declare @permsToAdd TABLE (
	autoID int IDENTITY(1,1) 
	PRIMARY KEY, siteID int, 
	siteResourceID int, 
	include bit, 
	functionID int, 
	roleID int, 
	groupID int,
	groupName varchar (255), 
	inheritedRightsResourceID int, 
	inheritedRightsFunctionID int
)

-- write your query to populate the @permsToAdd table
declare 
	@viewfunctionID int,
	@updateMemberResourceTypeID int

select @updateMemberResourceTypeID = dbo.fn_getResourceTypeID('UpdateMember')
select @viewfunctionID = dbo.fn_getResourceFunctionID('view',@updateMemberResourceTypeID)

-- add all existing view perms to the table as editOwn perms 
insert into @permsToAdd (siteID , siteResourceID , include , functionID , roleID , groupID ,groupName, inheritedRightsResourceID , inheritedRightsFunctionID )
select sr.siteID, sr.siteResourceID , 1 , @viewfunctionID , null , g.groupID ,g.groupName,  null , null
from cms_siteResources sr
inner join sites s
	on s.siteID = sr.siteID
inner join ams_groups g on
	g.orgID = s.orgID
	and g.isSystemGroup=1
	and g.groupCode='public'
	and sr.resourceTypeID = @updateMemberResourceTypeID

-- select * from @permsToAdd 

-- do not change below this line
select @autoID = min(autoID) from @permsToAdd
while @autoID is not null
begin

	select
		@siteID = siteID,
		@siteResourceID = siteResourceID, 
		@include = include, 
		@functionID = functionID, 
		@roleID = roleID, 
		@groupID = groupID, 
		@inheritedRightsResourceID = inheritedRightsResourceID, 
		@inheritedRightsFunctionID = inheritedRightsFunctionID
	from @permsToAdd
	where autoID = @autoID

	exec dbo.cms_createSiteResourceRight
		@siteID=@siteID,
		@siteResourceID=@siteResourceID, 
		@include=@include, 
		@functionID=@functionID, 
		@roleID=@roleID, 
		@groupID=@groupID,
		@memberID = null,
		@inheritedRightsResourceID=@inheritedRightsResourceID, 
		@inheritedRightsFunctionID=@inheritedRightsFunctionID,
		@resourceRightID=@trashID OUTPUT
	IF @@ERROR <> 0 GOTO on_error

	select @autoID = min(autoID) from @permsToAdd where autoID > @autoID
end

on_error:
	print 'ERROR @viewfunctionID'

GO

/*  (32) --------------------------------------------------------------------------------------------------------------------- */

ALTER PROC [dbo].[cms_createDefaultPages]
@siteid int,
@sectionid int,
@languageID int

AS

declare @appPageID int

-- get resourceType for system created pages and HTML content
declare @pgResourceTypeID int, @HTMLResourceTypeID int, @appPgResourceTypeID int, @siteResourceTypeID int
select @pgResourceTypeID = dbo.fn_getResourceTypeID('SystemCreatedPage')
select @HTMLResourceTypeID = dbo.fn_getResourceTypeID('UserCreatedContent')
select @appPgResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedPage')
select @siteResourceTypeID = dbo.fn_getResourceTypeID('site')

-- get siteresourceid
declare @websiteResourceID int
select @websiteResourceID = siteResourceID from sites where siteID = @siteID

-- get active resource status
declare @siteResourceStatusID int
select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

-- get Main Zone
declare @mainZoneID int
select @mainZoneID = dbo.fn_getZoneID('Main')

-- get page modes
declare @fullModeID int, @directModeID int, @streamModeID int
select @fullModeID = dbo.fn_getModeID('Full')
select @directModeID = dbo.fn_getModeID('Direct')
select @streamModeID = dbo.fn_getModeID('Stream')

-- get resource functions
declare @viewFID int, @loginFID int, @editOwnFID int
SELECT @viewFID = dbo.fn_getResourceFunctionID('view',@HTMLResourceTypeID)
SELECT @loginFID = dbo.fn_getResourceFunctionID('login',@siteResourceTypeID)
SELECT @editOwnFID = dbo.fn_getResourceFunctionID('editOwn',dbo.fn_getResourceTypeID('UpdateMember'))


-- get public, siteadmin group
declare @publicGID int, @siteadminGID int, @usersGID int, @guestGID int, @orgID int
select @publicGID = g.groupID, @orgID = s.orgID from dbo.ams_groups as g inner join dbo.sites as s on s.orgid = g.orgid where g.isSystemGroup = 1 and g.groupName = 'Public' AND g.status <> 'D' and s.siteid = @siteid
select @usersGID = g.groupID from dbo.ams_groups as g inner join dbo.sites as s on s.orgid = g.orgid where g.isSystemGroup = 1 and g.groupName = 'Users' AND g.status <> 'D' and s.siteid = @siteid
select @guestGID = g.groupID from dbo.ams_groups as g inner join dbo.sites as s on s.orgid = g.orgid where g.isSystemGroup = 1 and g.groupName = 'Guests' AND g.status <> 'D' and s.siteid = @siteid
select @siteadminGID = g.groupID from dbo.ams_groups as g inner join dbo.sites as s on s.orgid = g.orgid where g.isSystemGroup = 1 and g.groupName = 'Site Administrators' AND g.status <> 'D' and s.siteid = @siteid

-- Add memberID 0 record to the cache member groups table to allow public access to public content for non-authenticated users.
INSERT INTO [dbo].[cache_members_groups]
           ([orgid],[memberID],[groupID],[isManualDirect],[isManualIndirect],[isVirtualDirect],[isVirtualIndirect])
     VALUES
           (@orgID, 0, @publicGID, 0, 0, 0, 1)

-- create default pages and applications
DECLARE @rc int, @pageID int, @contentID int, @contentSiteResourceID int, @applicationTypeID int, @suggestedPageName varchar(100), @applicationInstanceID int, @siteresourceID int, @trashID int
BEGIN TRAN

	-- main page and content
	EXEC @rc = dbo.cms_createPage 
		@siteid=@siteid, 
		@languageID=@languageID, 
		@resourceTypeID=@pgResourceTypeID, 
		@siteResourceStatusID=@siteResourceStatusID, 
		@pgParentResourceID=null, 
		@isVisible=1, 
		@sectionID=@sectionID, 
		@ovTemplateID=null, 
		@ovTemplateIDMobile=null,
		@ovModeID=null, 
		@pageName='Main', 
		@pageTitle='Welcome', 
		@pageDesc=null, 
		@keywords=null,
		@inheritPlacements=1,
		@allowReturnAfterLogin=1, 
		@checkReservedNames=0, 
		@pageID=@pageID OUTPUT

		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = dbo.cms_createContent @siteID=@siteid, @pageID=@pageID, @zoneID=@mainZoneID, @resourceTypeID=@HTMLResourceTypeID, @siteResourceStatusID=@siteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle='Welcome', @contentDesc=null, @rawContent='Put your homepage text here. You can include tables, images, lists, and more! Our built-in content editor can do all of this and more.', @memberID=NULL, @contentID=@contentID, @contentSiteResourceID=@contentSiteResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- login app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'Login'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=1, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@fullModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- org doc download app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'OrgDocDownload'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- store doc download app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'StoreDocDownload'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- ts doc download app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'TSDocDownload'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- account locator
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'accountLocator'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@directModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1,
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null,
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null,
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- userinfo app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'UserInfo'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@directModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- updatemember app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'UpdateMember'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=1, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=null, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- Ticket 8352718 - default permissions when setting up a site
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@editOwnFID, @roleID=null, @groupID=@usersGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@editOwnFID, @roleID=null, @groupID=@guestGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- ContentEditor app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'ContentEditor'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@fullModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- ajax app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'Ajax'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	
	-- Flash Express Install app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'flashExpressInstall'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	
	-- AppProxy app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'appProxy'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	
	-- invoices app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'invoices'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@fullModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- BuyNow app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'buyNow'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@fullModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	
	-- ViewCart app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'viewCart'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@fullModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	
	-- Support app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'Support'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=null, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- admin app
	-- set template used by the admin app to the admin template
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'Admin'
	EXEC @rc = dbo.cms_createApplicationInstanceAdmin @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle='Website Control Panel', @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=2, @pageModeID=null, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@siteadminGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	--set admin page inheritPlacements = 0
	update cms_pages set inheritPlacements = 0 where pageID = @appPageID
	IF @@ERROR <> 0 GOTO on_error

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

/* (33) --------------------------------------------------------------------------------------------------------------------- */

ALTER PROC [dbo].[email_sendTestBlast]
@recordedByMemberID int,
@siteID int,
@blastID int,
@memberID int,
@email varchar(200),
@messageWrapper varchar(max),
@markRecipientAsReady bit,
@messageID int OUTPUT,
@recipientID int OUTPUT

AS

declare @numRecipients int, @rc int, @messageTypeID int, 
	@messageStatusIDInserting int, @messageStatusIDQueued int, @sendingSiteResourceID int, 
	@rawcontent varchar(max), @messageToParse varchar(max), @footercontent varchar(max),
	@fieldID int, @fieldName varchar(60), @contentID int, @footerContentID int, @languageID int
declare @metadataFields TABLE (fieldName varchar(60), fieldID int NULL)
declare @fromName varchar(200), @fromEmail varchar(200), @replyToEmail varchar(200), @senderEmail varchar(200), @subject varchar(400), @contentVersionID int

IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
	DROP TABLE #tmpRecipients

select @messageID = null
select @messageTypeID = messageTypeID from platformMail.dbo.email_messageTypes where messageTypeCode = 'EMAILBLAST'
select @messageStatusIDInserting = statusID from platformMail.dbo.email_statuses where statusCode = 'I'
select @messageStatusIDQueued = statusID from platformMail.dbo.email_statuses where statusCode = 'Q'
select @languageID = dbo.fn_getLanguageID('en')
select @senderEmail = '<EMAIL>'

select @sendingSiteResourceID = st.siteResourceID
from dbo.sites as s WITH(NOLOCK)
inner join dbo.admin_siteTools as st WITH(NOLOCK) on st.siteID = s.siteID and st.siteID = @siteID
inner join dbo.admin_toolTypes as tt WITH(NOLOCK) on tt.toolTypeID = st.toolTypeID and tt.toolType = 'EmailBlast'
	IF @@ERROR <> 0 RETURN -1

select @fromName = fromName, @fromEmail = fromEmail, @replyToEmail = replyTo, @contentID = contentID, @footerContentID = footerContentID
from dbo.email_EmailBlasts WITH(NOLOCK)
where blastID = @blastID
	IF @@ERROR <> 0 RETURN -1

select @subject='**TEST** ' + contentTitle, @contentVersionID=contentVersionID, @rawContent = rawContent
from dbo.fn_getContent(@contentID,@languageID) as messageContent
	IF @@ERROR <> 0 RETURN -1
SELECT @footercontent = rawcontent
FROM dbo.fn_getContent(@footerContentID,@languageID) AS footerContent
	IF @@ERROR <> 0 RETURN -1

select m.memberID, m.prefix, m.firstName, m.middlename, m.lastName, m.company, m.suffix, @email as email
into #tmpRecipients
FROM dbo.ams_members as m WITH(NOLOCK)
WHERE m.memberID = @memberID
	IF @@ERROR <> 0 RETURN -1
	select @numRecipients = @@rowcount	

-- add any necessary metadata fields
select @messageToParse = replace(@messageWrapper,'@@rawcontent@@',@rawcontent)
	IF @@ERROR <> 0 RETURN -1
select @messageToParse = replace(@messageToParse,'@@footerContent@@',@footercontent)
	IF @@ERROR <> 0 RETURN -1
insert into @metadataFields (fieldName)
select distinct [Text]
from dbo.fn_RegexMatches(@messageToParse,'(?<=\[\[)([^,\]]+)(?=,?([^\]]+)?\]\])')
	IF @@ERROR <> 0 RETURN -1

BEGIN TRAN
	-- add email_message
	EXEC @rc = platformMail.dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID, 
		@sendingSiteResourceID=@sendingSiteResourceID, @recordedByMemberID=@recordedByMemberID, 
		@fromName=@fromName, @fromEmail=@fromEmail, 
		@replyToEmail=@replyToEmail, @senderEmail=@senderEmail, 
		@subject=@subject, @contentVersionID=@contentVersionID, 
		@messageWrapper=@messageWrapper, @messageID=@messageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @messageID = 0 goto on_error

	-- add recipients as I (not ready to be queued yet)
	insert into platformMail.dbo.email_messageRecipientHistory (messageID, memberID, 
		dateLastUpdated, toName, toEmail, emailStatusID, batchID, batchStartDate)
	select @messageID, memberID, getdate(), firstName + ' ' + lastName, email, @messageStatusIDInserting, null, null
	from #tmpRecipients
		IF @@ERROR <> 0 goto on_error

	select @recipientID = SCOPE_IDENTITY()

	-- add message metadata fields
	insert into platformMail.dbo.email_metadataFields (fieldName, isMergeField)
	select fieldName, 1
	from @metadataFields
		except
	select fieldName, isMergeField
	from platformMail.dbo.email_metadataFields
		IF @@ERROR <> 0 goto on_error
	update tmp
	set tmp.fieldID = MF.fieldID
	from @metadataFields as tmp
	inner join platformMail.dbo.email_metadataFields as MF on MF.fieldName = tmp.fieldName
	where MF.isMergeField = 1
		IF @@ERROR <> 0 goto on_error

	-- add recipient metadata
	select @fieldID = min(fieldID) from @metadataFields where fieldName in ('firstname','lastname','company','prefix','fullName','extendedname')
	while @fieldID is not null BEGIN
		select @fieldName = fieldName from @metadataFields where fieldID = @fieldID
			IF @@ERROR <> 0 goto on_error
		insert into platformMail.dbo.email_messageMetadataFields (messageID, fieldID, memberID, fieldValue)
		select @messageID, @fieldID, memberID, 
			fieldValue = case @fieldName
				when 'firstname' then firstname
				when 'lastname' then lastname
				when 'company' then company
				when 'prefix' then prefix 
				when 'fullName' then firstname + ' ' + lastname
				when 'extendedname' then firstname + isnull(' ' + nullif(middlename,''),'') + ' ' + lastname + isnull(' ' + nullif(suffix,''),'')
				end
		from #tmpRecipients
			IF @@ERROR <> 0 goto on_error
		select @fieldID = min(fieldID) from @metadataFields where fieldName in ('firstname','lastname','company','prefix','fullName','extendedname') and fieldID > @fieldID
	END

	-- mark recipients as queued
	if @markRecipientAsReady = 1 begin
		update mrh set
			emailStatusID = @messageStatusIDQueued
		from platformMail.dbo.email_messages m
		inner join platformMail.dbo.email_messageRecipientHistory mrh
			on m.messageID = mrh.messageID
			and m.messageID = @messageID
		IF @@ERROR <> 0 goto on_error
	end


IF @@TRANCOUNT > 0 COMMIT TRAN

goto on_done

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN

	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients
	select @messageID = 0
	RETURN -1


on_done:
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients
	RETURN 0
GO

/* (34) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('email_getBlastRecipients') IS NOT NULL
	DROP PROCEDURE email_getBlastRecipients
GO


CREATE PROC [dbo].[email_getBlastRecipients]
@siteID int,
@blastID int

AS

declare 
	@numRecipients int, @rc int, @messageTypeID int, 
	@messageStatusIDInserting int, @messageStatusIDQueued int, @sendingSiteResourceID int, 
	@messageID int, @rawcontent varchar(max), @footercontent varchar(max), @messageToParse varchar(MAX),
	@fieldID int, @fieldName varchar(60), @contentID int, @footerContentID [int], @languageID int,
	@ruleID int, @xmlMembers xml, @orgID int,
	@fromName varchar(200), @fromEmail varchar(200), @replyToEmail varchar(200), 
	@senderEmail varchar(200), @subject varchar(400), @contentVersionID int
declare @metadataFields TABLE (fieldName varchar(60), fieldID int NULL)

	

IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
	DROP TABLE #tmpRecipients

select @messageTypeID = messageTypeID from platformMail.dbo.email_messageTypes where messageTypeCode = 'EMAILBLAST'
select @messageStatusIDInserting = statusID from platformMail.dbo.email_statuses where statusCode = 'I'
select @messageStatusIDQueued = statusID from platformMail.dbo.email_statuses where statusCode = 'Q'
select @languageID = dbo.fn_getLanguageID('en')
select @senderEmail = '<EMAIL>'

select 
	@sendingSiteResourceID = st.siteResourceID,
	@orgID = orgID
from dbo.sites as s
inner join dbo.admin_siteTools as st on st.siteID = s.siteID and st.siteID = @siteID
inner join dbo.admin_toolTypes as tt on tt.toolTypeID = st.toolTypeID and tt.toolType = 'EmailBlast'
	

select @ruleID = ruleID, @fromName = fromName, @fromEmail = fromEmail, @replyToEmail = replyTo, @contentID = contentID, @footerContentID = footerContentID
from dbo.email_EmailBlasts
where blastID = @blastID
	--IF @@ERROR <> 0 RETURN -1

select @subject=contentTitle, @contentVersionID=contentVersionID, @rawContent = rawContent
from dbo.fn_getContent(@contentID,@languageID) as messageContent
	--IF @@ERROR <> 0 RETURN -1
	
SELECT @footercontent = rawcontent
FROM dbo.fn_getContent(@footerContentID,@languageID) AS footerContent
	--IF @@ERROR <> 0 RETURN -1

EXEC dbo.ams_RunVirtualGroupRule @ruleID=@ruleID, @xmlVirtual=@xmlMembers OUTPUT
	--IF @@ERROR <> 0 RETURN -1

select m.memberID, m.prefix, m.firstName, m.middlename, m.lastName, m.company, m.suffix, me.email
into #tmpRecipients
FROM @xmlMembers.nodes('//m') AS XM(item)
INNER JOIN dbo.ams_members as m WITH(NOLOCK) on m.memberid = XM.item.value('@id', 'int')
	and m.status = 'A'
inner join dbo.ams_memberEmails as me WITH(NOLOCK) on me.memberid = m.memberid
	and len(me.email) > 0
INNER JOIN dbo.ams_memberEmailTypes as met WITH(NOLOCK) on met.emailTypeID = me.emailTypeID
	and met.emailTypeOrder = 1
	--IF @@ERROR <> 0 RETURN -1

select
	mrh.recipientID,
	mrh.messageID, 
	mrh.memberID
from 
	platformMail.dbo.email_messageRecipientHistory mrh
	inner join platformMail.dbo.email_messages msg on
		msg.messageID = mrh.messageID
	inner join platformMail.dbo.email_messageTypes mt on
		mt.messageTypeID = msg.messageTypeID
		and mt.messageTypeCode = 'EMAILBLAST'
	inner join membercentral.dbo.ams_members m on
		m.memberID = mrh.memberID
		and m.memberID = m.activememberID
		and m.orgID = @orgID
	inner join #tmpRecipients r on
		r.memberID = m.memberID
	inner join platformMail.dbo.email_statuses s on
		s.statusID = mrh.emailStatusID
		and s.statusCode = 'I'


IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
	DROP TABLE #tmpRecipients

goto on_done

on_error:
	RETURN -1
on_done:
	RETURN 0
GO