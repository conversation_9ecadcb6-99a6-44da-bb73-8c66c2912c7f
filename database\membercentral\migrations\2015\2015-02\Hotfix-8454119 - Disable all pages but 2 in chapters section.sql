use membercentral;

declare @siteID int, @sectionID int, @siteResourceStatusID int

select @siteID=siteID from sites where siteCode='ABOTA'
select @siteResourceStatusID=siteResourceStatusID from  cms_siteResourceStatuses where siteResourceStatusDesc = 'Inactive'


select @sectionID = sectionID
from cms_pageSections ps
where siteID = @siteID
and sectionCode = 'Chapters'

update sr
	set sr.siteResourceStatusID = @siteResourceStatusID
from cms_pages p
inner join cms_siteResources sr on sr.siteResourceID = p.siteResourceID
where p.sectionID = @sectionID
and p.pageName not in ('ChaptersMap', 'ChapterSpotlight')