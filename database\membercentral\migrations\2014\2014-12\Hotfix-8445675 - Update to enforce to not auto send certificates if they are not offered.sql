USE [seminarWeb]
GO
/****** Object:  StoredProcedure [dbo].[swl_completeWebinar]    Script Date: 12/09/2014 16:50:51 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROC [dbo].[swl_completeWebinar]
@enrollmentID int
AS

-- get Seminar Progress
DECLARE @allPostTestCompleted bit, @allEvaluationCompleted bit, @hasCertificate bit, @allowAutoSendOfCertificate bit
DECLARE @SWLdaysToCompleteExam int, @SWLdaysToCompleteEvaluation int, @CreditCount int, @CreditFormNotRequired int

SELECT @allPostTestCompleted = 
	CASE 
	WHEN (select count(saf.formID)
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblSeminarsAndForms AS saf ON e.seminarID = saf.seminarID
			LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
				INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
				INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
					and f.isPublished = 1
					and getdate() between f.dateStartPublish and f.dateEndPublish	
					and r.passingPct >= f.passingPct				
				on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e.enrollmentID
			WHERE e.enrollmentID = @enrollmentID
			AND saf.loadPoint = 'postTest'
			AND saf.isRequired = 1
			AND safr.responseID is null) > 0 THEN 0
	ELSE 1
	END

SELECT @allEvaluationCompleted = 
	CASE 
	WHEN (select count(saf.formID)
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblSeminarsAndForms AS saf ON e.seminarID = saf.seminarID
			LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
				INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
				INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
					and f.isPublished = 1
					and getdate() between f.dateStartPublish and f.dateEndPublish	
				on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e.enrollmentID
			WHERE e.enrollmentID = @enrollmentID
			AND saf.loadPoint = 'evaluation'
			AND (safr.responseID is null or r.dateCompleted is null)) > 0 THEN 0
	ELSE 1
	END

SELECT @CreditCount = COUNT(*) FROM dbo.tblEnrollmentsAndCredit WHERE enrollmentID = @enrollmentID

-- Credit requested but Authority does not require survey
SELECT @CreditFormNotRequired = COUNT(sac.seminarCreditID)
				FROM dbo.tblEnrollmentsAndCredit AS eac 
				INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
				INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID 
				INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
				INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID 
				INNER JOIN dbo.tblCreditStatuses AS cstat ON sac.statusID = cstat.statusID
				INNER JOIN dbo.tblCreditAuthoritiesSWLive as caswl on caswl.authorityID = ca.authorityID
				WHERE eac.enrollmentID = @enrollmentID
				and evaluationRequired = 0

if (@allPostTestCompleted = 1 and @allEvaluationCompleted = 1) OR @CreditCount = 0 OR @CreditFormNotRequired > 0 begin
	-- if enrollment not marked passed then we need to update the flags and dates
	declare @testEnrollment int
	select @testEnrollment = e.enrollmentID
	FROM dbo.tblEnrollments AS e 
	INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
	WHERE e.enrollmentID = @enrollmentID
		AND e.passed = 0
		AND eswl.attended = 1

	if @testEnrollment is not null begin
		update dbo.tblEnrollments set passed = 1, dateCompleted = getDate()
		WHERE enrollmentID = @enrollmentID 
	end


	-- check date requirements for exams and 
	-- if failed then mark they did not pass
	SELECT	
		@SWLdaysToCompleteExam = caswl.daysToCompleteExam,
		@SWLdaysToCompleteEvaluation = caswl.daysToCompleteEvaluation
	FROM dbo.tblEnrollmentsAndCredit AS eac 
	INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
	INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID 
	INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
	INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID 
	INNER JOIN dbo.tblCreditStatuses AS cstat ON sac.statusID = cstat.statusID
	LEFT OUTER JOIN dbo.tblCreditAuthoritiesSWLive AS caswl ON ca.authorityID = caswl.authorityID 
	WHERE eac.enrollmentID = @enrollmentID


	-- create temp table to store all forms and exams
	DECLARE @tmpTable TABLE (
		loadPoint varchar(255),
		enrollmentid int,
		dateCompleted datetime,
		formCompleteDate datetime,
		completeByDate datetime
	)

	if @SWLdaysToCompleteExam > 0 BEGIN
		insert into @tmpTable
		select saf.loadPoint, e.enrollmentid, e.dateCompleted, r.datecompleted as formCompleteDate, s.dateEnd + @SWLdaysToCompleteExam as completeByDate
		FROM dbo.tblEnrollments AS e 
		INNER JOIN dbo.tblSeminarsAndForms AS saf ON e.seminarID = saf.seminarID
		INNER JOIN dbo.tblSeminarsSWLive s on s.seminarID = e.seminarID
		LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
			INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
			INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
				and f.isPublished = 1
				and getdate() between f.dateStartPublish and f.dateEndPublish	
				and r.passingPct >= f.passingPct				
			on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e.enrollmentID
		WHERE e.enrollmentID = @enrollmentID
		AND saf.loadPoint = 'postTest'
		AND saf.isRequired = 1
	END

	if @SWLdaysToCompleteEvaluation > 0 BEGIN
		insert into @tmpTable
		select saf.loadPoint, e.enrollmentid, e.dateCompleted, r.datecompleted as formCompleteDate, s.dateEnd + @SWLdaysToCompleteEvaluation as completeByDate
		FROM dbo.tblEnrollments AS e 
		INNER JOIN dbo.tblSeminarsAndForms AS saf ON e.seminarID = saf.seminarID
		INNER JOIN dbo.tblSeminarsSWLive s on s.seminarID = e.seminarID
		LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
			INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
			INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
				and f.isPublished = 1
				and getdate() between f.dateStartPublish and f.dateEndPublish	
			on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e.enrollmentID
		WHERE e.enrollmentID = @enrollmentID
		AND saf.loadPoint = 'evaluation'
		AND saf.isRequired = 1
	END

	-- If any forms completed passed due date then flag as NOT passed
	IF EXISTS(select * from @tmpTable where formCompleteDate > completeByDate) BEGIN
		print 'Failed Dates'

		update dbo.tblEnrollments set passed = 0
		WHERE enrollmentID = @enrollmentID 
	END
	ELSE BEGIN 
		print 'Success Dates'
		-- If dates are good then make sure earned certificate flag is set. 
		UPDATE dbo.tblEnrollmentsAndCredit
		SET earnedCertificate = 1
		WHERE enrollmentID = @enrollmentID
	END


	-- If polling required then the earnedCert is already set
	UPDATE dbo.tblEnrollmentsAndCredit
	SET earnedCertificate = 1
	WHERE enrollCreditID IN (
		SELECT enrollCreditID
		FROM dbo.tblEnrollmentsAndCredit as eac
		WHERE dbo.fn_isPollingRequiredBasedOnSCID(eac.seminarCreditID) = 0
		and enrollmentID = @enrollmentID 
	)

end


SELECT @hasCertificate = 
	CASE 
	WHEN (SELECT count(e.enrollmentID)
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
			INNER JOIN dbo.tblSeminars AS s ON e.seminarID = s.seminarID 
			INNER JOIN dbo.tblSeminarsSWLive AS sswl ON s.seminarID = sswl.seminarID
			WHERE e.enrollmentID = @enrollmentID 
			AND s.isDeleted = 0
			AND e.isActive = 1
			AND eswl.attended = 1
			AND s.offerCertificate = 1
			AND e.passed = 1
			AND LEN(e.dateCompleted) > 0) > 0 THEN 1
	ELSE 0
	END
SELECT @allowAutoSendOfCertificate = 
	CASE 
	WHEN (select count(*) from tblLogSWLive where enrollmentID = @enrollmentID and typeID = 37) > 0 THEN 0
	WHEN (SELECT count(sac.seminarCreditID)
				FROM dbo.tblEnrollmentsAndCredit AS eac 
				INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
				INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID 
				INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
				INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID 
				INNER JOIN dbo.tblCreditStatuses AS cstat ON sac.statusID = cstat.statusID
				INNER JOIN dbo.tblCreditAuthoritiesSWLive as caswl on caswl.authorityID = ca.authorityID
				WHERE eac.enrollmentID = @enrollmentID
				and evaluationRequired = 1) > 0 THEN 1
	WHEN @CreditCount = 0 THEN 1
	WHEN (SELECT count(sac.seminarCreditID)
				FROM dbo.tblEnrollmentsAndCredit AS eac 
				INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
				INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID 
				INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
				INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID 
				INNER JOIN dbo.tblCreditStatuses AS cstat ON sac.statusID = cstat.statusID
				INNER JOIN dbo.tblCreditAuthoritiesSWLive as caswl on caswl.authorityID = ca.authorityID
				WHERE eac.enrollmentID = @enrollmentID
				and evaluationRequired = 0) > 0 THEN 1
	ELSE 0
	END

SELECT e.*, @hasCertificate as hasCertificate, 
	CASE
		WHEN  (s.offerCertificate = 1) THEN @allowAutoSendOfCertificate
		ELSE 0
	END as allowAutoSendOfCertificate
FROM dbo.tblEnrollments AS e 
INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
INNER JOIN dbo.tblSeminars AS s ON e.seminarID = s.seminarID 
INNER JOIN dbo.tblSeminarsSWLive AS sswl ON s.seminarID = sswl.seminarID
WHERE e.enrollmentID = @enrollmentID 
AND s.isDeleted = 0
AND e.isActive = 1
/*
AND eswl.attended = 1
AND s.offerCertificate = 1
AND e.passed = 1
AND LEN(e.dateCompleted) > 0
*/
GO