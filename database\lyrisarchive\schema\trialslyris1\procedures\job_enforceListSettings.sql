ALTER PROC dbo.job_enforceListSettings
@listName varchar(60) = NULL

AS

DECLARE @headerLineEndings char(2) = char(13) + char(10)

DECLARE @sendgridOutboudProviderID int,  @halonOutboudProviderID int;

select @sendgridOutboudProviderID=outboundproviderID from membercentral.platformMail.dbo.email_outboundProviders where outboundProviderCode = 'sendgrid'
select @halonOutboudProviderID=outboundproviderID from membercentral.platformMail.dbo.email_outboundProviders where outboundProviderCode = 'halon'


-- turn off join by email for all lists
update dbo.lists_ 
set NoEmailSub_ = 'T'
where NoEmailSub_ <> 'T'
and name_ = isnull(@listName,name_);

-- force all lists to only allow admins to add members
update dbo.lists_ 
set security_ = 'private'
where security_ = 'open' 
and name_ not in ('eclips_js','brandigy')
and name_ = isnull(@listName,name_);

-- set all lists to invisible in Discussion Forum Interface
update dbo.lists_ 
set MriVisibility_ = 'I'
where MriVisibility_ <> 'I'
and name_ = isnull(@listName,name_);


-- turn off held notifications
update dbo.lists_ set
	ConfNotify_=0,
	CleanNotif_=0
where (ConfNotify_ <> 0 or CleanNotif_<> 0)
and name_ = isnull(@listName,name_);


-- make sure that all discussion lists with HTML conversion turned on have Pgmbefore filled in
update l set 
	pgmbefore_ = 'C:\Progra~1\groovy\groovy-2.3.0\bin\groovy.bat c:\groovyscripts\DiscussionListProcessor.groovy -m $MESSAGEID -e production -d c:\groovyscripts\'
from dbo.lists_ l
inner join dbo.lists_format lf on lf.name = l.name_
and cast(l.pgmbefore_ as varchar(500)) not like  '%DiscussionListProcessor.groovy%'
and lf.disableHTML=0
and l.AdminSend_ = 'F'
and l.name_ = isnull(@listName,l.name_);

-- update maxmesssize to 50000 for lists using offloadattachments with groovy script
update l 
set l.maxmessSiz_ = 50000
from dbo.lists_ l
inner join dbo.lists_format lf on lf.name = l.name_
where lf.offloadAttachments = 1 
and lf.disableHTML=0
and cast(l.pgmbefore_ as varchar(1000)) like '%DiscussionListProcessor.groovy%'
and l.maxmessSiz_ <> 50000
and l.name_ = isnull(@listName,l.name_);

-- update maxmesssize to 1500 for discussion lists not using offloadattachments
update l 
set l.maxmessSiz_ = 1500
from dbo.lists_ l
inner join (
	select name_
	from dbo.lists_
	where maxmessSiz_ <> 1500 and adminsend_ = 'F'
		except
	select name_
	from dbo.lists_ l2
	inner join dbo.lists_format lf on lf.name = l2.name_
	where lf.offloadAttachments = 1 
	and disableHTML=0
	and cast(pgmbefore_ as varchar(1000)) like '%DiscussionListProcessor.groovy%'
	) as temp on temp.name_ = l.name_
where l.name_ = isnull(@listName,l.name_);

-- turn on advanced scripting for all lists using HTMLConversion
update l 
set l.MergeCapabilities_ = 2
from dbo.lists_ l
inner join dbo.lists_format lf on lf.name = l.name_
and l.MergeCapabilities_ <> 2
and cast(l.pgmbefore_ as varchar(500))<> ''
and lf.disableHTML=0
and l.name_ = isnull(@listName,l.name_);

-- move digest, mimedigest, index subtypes -> mail for all TS, SW, MC hidden marketing lists
declare @hiddenMarketingLists TABLE (list_ varchar(200) primary key);
insert into @hiddenMarketingLists (list_)
select l.name_
from dbo.lists_ l
inner join dbo.lists_format lf on lf.name = l.name_
where AdminSend_= 'T' 
and lf.hidden = 1 
and lf.orgcode in ('TS','MC','SW')
and l.name_ = isnull(@listName,l.name_)
order by lf.orgcode;

IF EXISTS (
	select 1 
	from @hiddenMarketingLists l
	inner join dbo.members_ m on m.list_ = l.list_ collate Latin1_General_CI_AI
		and m.SubType_ in ('digest','mimedigest','index')
) BEGIN
	update m 
	set m.subType_ = 'mail'
	from @hiddenMarketingLists l
	inner join dbo.members_ m on m.list_ = l.list_ collate Latin1_General_CI_AI
		and m.SubType_ in ('digest','mimedigest','index');
END
-- mark all messages sent to segments as digested
declare @twohourago datetime = dateadd(hour,-2,getdate());
update dbo.messages_ 
set Digested_ = 'T'
where SubsetID_ > 0 
and Digested_ = 'F'
and CreatStamp_ > @twohourago;

-- make sure there are no NULL MC Digest Columns
IF EXISTS(select * from dbo.members_ where receiveMCThreadIndex is null)
    update dbo.members_ 
	set receiveMCThreadIndex = 0
    where receiveMCThreadIndex is null;

IF EXISTS(select * from dbo.members_ where receiveMCThreadDigest is null)
    update dbo.members_ 
	set receiveMCThreadDigest = 0
    where receiveMCThreadDigest is null;

-- make sure that all rows have correct MCEmailKey_
IF EXISTS(select 1 from dbo.members_ where UserNameLC_ <> isnull(MCEmailKey_usernameLC,'') or domain_ <> isnull(MCEmailKey_domain,''))
    update dbo.members_ set 
		MCEmailKey = convert(varchar(75),HASHBYTES('SHA2_256',list_ + '|' + usernameLC_ + '@' + domain_),2),
		MCEmailKey_usernameLC = usernameLC_,
		MCEmailKey_domain = domain_
    where UserNameLC_ <> isnull(MCEmailKey_usernameLC,'') or domain_ <> isnull(MCEmailKey_domain,'');


-- keep MCSetting_ListType updated
DECLARE @eclipsLists TABLE (listname varchar(100) PRIMARY KEY);

insert into @eclipsLists
select distinct lyrislistname
from membercentral.trialsmith.dbo.eclipsPublications
where lyrislistname <> '';

UPDATE l
SET l.MCSetting_ListType = tmp.MCSetting_ListType
FROM dbo.lists_ as l
INNER JOIN (
	select name_, MCSetting_ListType = case 
		when el.listname is not null then 'eclips' 
		when swm.list is not null then 'swmarketing' 
		when l.AdminSend_ = 'T' then 'marketing' 
		else 'discussion' end
	from dbo.lists_ l
	left outer join @eclipsLists el on el.listname = l.name_ collate Latin1_General_CI_AI
	left outer join sw_marketing swm on swm.list = l.name_ collate Latin1_General_CI_AI
) as tmp on tmp.name_ = l.name_
where l.name_ = isnull(@listName,l.name_);

-- create sendgrid subusers if needed
EXEC dbo.createSendgridSubusersAndDomains;

-- set SubUserDomainID on Lists
DECLARE @activeStatusID int, @environmentID int = 5, @platformMKTSubUserDomainID int, @platformLISTPrimarySubUserDomainID int, @platformMKTSubUserID int, @platformLISTSubUserID int, @platformMKTOutboundProviderID int, @platformLISTOutboundProviderID int;



SELECT @activeStatusID = subuserStatusID 
FROM membercentral.platformMail.dbo.sendgrid_subuserStatuses
WHERE [status] = 'Active';

SELECT @platformMKTSubUserDomainID = sud.subuserDomainID, @platformMKTSubUserID=dsu.subuserID, @platformMKTOutboundProviderID=sud.outboundProviderID
FROM membercentral.platformMail.dbo.sendgrid_defaultsubusers AS dsu
INNER JOIN membercentral.platformMail.dbo.email_mailstreams AS ms ON ms.mailStreamID = dsu.mailstreamID
	AND dsu.environmentID = @environmentID
	AND ms.mailStreamCode = 'MKT'
INNER JOIN membercentral.platformMail.dbo.sendgrid_subusers AS su ON su.subuserID = dsu.subuserID
	AND su.statusID = @activeStatusID
INNER JOIN membercentral.platformMail.dbo.sendgrid_subuserDomains AS sud ON sud.subuserID = su.subuserID
	AND sud.subuserDomainID = su.activeSubuserDomainID
WHERE sud.statusID = @activeStatusID;

SELECT @platformLISTPrimarySubUserDomainID = sud.subuserDomainID, @platformLISTSubUserID=dsu.subuserID, @platformLISTOutboundProviderID=sud.outboundProviderID
FROM membercentral.platformMail.dbo.sendgrid_defaultsubusers AS dsu
INNER JOIN membercentral.platformMail.dbo.email_mailstreams AS ms on ms.mailStreamID = dsu.mailstreamID
	AND dsu.environmentID = @environmentID
	AND ms.mailStreamCode = 'LISTS'
INNER JOIN membercentral.platformMail.dbo.sendgrid_subusers AS su ON su.subuserID = dsu.subuserID
	AND su.statusID = @activeStatusID
INNER JOIN membercentral.platformMail.dbo.sendgrid_subuserDomains sud ON sud.subuserID = su.subuserID
	AND sud.subuserDomainID = su.activeSubuserDomainID
WHERE sud.statusID = @activeStatusID;

-- marketing lists
DECLARE @tmpMarketingLists TABLE (listName varchar(60), MCSetting_subuserDomainID int);

INSERT INTO @tmpMarketingLists (listName)
SELECT name_
FROM dbo.lists_
WHERE MCSetting_ListType in ('marketing','swmarketing')
AND name_ = isnull(@listName,name_);

-- the overrideSubuserDomainID on lists_format for the list when the subuser and domain are both active status
UPDATE l
SET l.MCSetting_subuserDomainID = lf.overrideSubuserDomainID
FROM @tmpMarketingLists AS l
INNER JOIN dbo.lists_format AS lf ON lf.name = l.listName
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud ON sud.subuserDomainID = lf.overrideSubuserDomainID 
	AND sud.statusID = @activeStatusID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subusers AS su 
	ON su.subuserID = sud.subuserID
	and su.environmentID=@environmentID
	AND su.statusID = @activeStatusID
WHERE l.MCSetting_subuserDomainID IS NULL;

-- the active subuserdomain on the subuser associated with the overrideSubuserDomainID on lists_format for the list when the subuser and domain are both active status
UPDATE l
SET l.MCSetting_subuserDomainID = active_sud.subuserDomainID
FROM @tmpMarketingLists AS l
INNER JOIN dbo.lists_format AS lf ON lf.name = l.listName
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud ON sud.subuserDomainID = lf.overrideSubuserDomainID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subusers AS su 
	ON su.subuserID = sud.subuserID
	and su.environmentID=@environmentID
	AND su.statusID = @activeStatusID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS active_sud ON active_sud.subuserID = su.subuserID
	AND active_sud.subuserDomainID = su.activeSubuserDomainID
	AND active_sud.statusID = @activeStatusID
WHERE l.MCSetting_subuserDomainID IS NULL;

-- the active domain on the site-specific marketing subuser when the subuser and domain are both active status
UPDATE l
SET l.MCSetting_subuserDomainID = sud.subuserDomainID
FROM @tmpMarketingLists AS l
INNER JOIN dbo.lists_format AS lf ON lf.name = l.listName
INNER JOIN membercentral.membercentral.dbo.sites AS s ON s.sitecode = lf.orgcode COLLATE Latin1_General_CI_AI 
INNER JOIN membercentral.platformmail.dbo.sendgrid_subusers AS su 
	ON su.siteID = s.siteID
	and su.environmentID=@environmentID
    and su.subuserID <> @platformMKTSubUserID
	AND su.statusID = @activeStatusID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserMailstreams AS sums ON sums.subuserID = su.subuserID
INNER JOIN membercentral.platformmail.dbo.email_mailstreams AS ms ON ms.mailStreamID = sums.mailstreamID
	AND ms.mailStreamCode = 'MKT'
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud ON sud.subuserID = su.subuserID
	AND sud.subuserDomainID = su.activeSubuserDomainID
	AND sud.statusID = @activeStatusID
WHERE l.MCSetting_subuserDomainID IS NULL;

-- the active domain on the platformwide default marketing subuser when the subuser and domain are both active status
UPDATE @tmpMarketingLists
SET MCSetting_subuserDomainID = @platformMKTSubUserDomainID
WHERE MCSetting_subuserDomainID IS NULL;

-- discussion lists
DECLARE @tmpDiscussionLists TABLE (listName varchar(60), MCSetting_subuserDomainID int);

INSERT INTO @tmpDiscussionLists (listName)
SELECT name_
FROM dbo.lists_
WHERE MCSetting_ListType = 'discussion'
AND name_ = isnull(@listName,name_);

-- the overrideSubuserDomainID on lists_format for the list when the subuser and domain are both active status
UPDATE l
SET l.MCSetting_subuserDomainID = lf.overrideSubuserDomainID
FROM @tmpDiscussionLists AS l
INNER JOIN dbo.lists_format AS lf ON lf.name = l.listName
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud ON sud.subuserDomainID = lf.overrideSubuserDomainID 
	AND sud.statusID = @activeStatusID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subusers AS su 
	ON su.subuserID = sud.subuserID
	and su.environmentID=@environmentID
	AND su.statusID = @activeStatusID
WHERE l.MCSetting_subuserDomainID IS NULL;

-- the active subuserdomain on the subuser associated with the overrideSubuserDomainID on lists_format for the list when the subuser and domain are both active status
UPDATE l
SET l.MCSetting_subuserDomainID = active_sud.subuserDomainID
FROM @tmpDiscussionLists AS l
INNER JOIN dbo.lists_format AS lf ON lf.name = l.listName
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud ON sud.subuserDomainID = lf.overrideSubuserDomainID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subusers AS su 
	ON su.subuserID = sud.subuserID
	and su.environmentID=@environmentID
	AND su.statusID = @activeStatusID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS active_sud ON active_sud.subuserID = su.subuserID
	AND active_sud.subuserDomainID = su.activeSubuserDomainID
	AND active_sud.statusID = @activeStatusID
WHERE l.MCSetting_subuserDomainID IS NULL;

-- the active domain on the site-specific listserver subuser when the subuser and domain are both active status
UPDATE l
SET l.MCSetting_subuserDomainID = sud.subuserDomainID
FROM @tmpDiscussionLists AS l
INNER JOIN dbo.lists_format AS lf ON lf.name = l.listName
INNER JOIN membercentral.membercentral.dbo.sites AS s ON s.sitecode = lf.orgcode COLLATE Latin1_General_CI_AI 
INNER JOIN membercentral.platformmail.dbo.sendgrid_subusers AS su 
	ON su.siteID = s.siteID
	and su.environmentID=@environmentID
    and su.subuserID <> @platformLISTSubUserID
	AND su.statusID = @activeStatusID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserMailstreams AS sums ON sums.subuserID = su.subuserID
INNER JOIN membercentral.platformmail.dbo.email_mailstreams AS ms ON ms.mailStreamID = sums.mailstreamID
	AND ms.mailStreamCode = 'LISTS'
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud ON sud.subuserID = su.subuserID
	AND sud.subuserDomainID = su.activeSubuserDomainID
	AND sud.statusID = @activeStatusID
WHERE l.MCSetting_subuserDomainID IS NULL;

-- the domain that matches the lyris site sending domain on the site-specific listserver subuser when the subuser and domain are both active status
UPDATE tmp
SET tmp.MCSetting_subuserDomainID = sud.subuserDomainID
FROM @tmpDiscussionLists AS tmp
INNER JOIN dbo.lists_ AS l ON l.name_ = tmp.listName
INNER JOIN dbo.topics_ AS t ON t.title_ = l.topic_
INNER JOIN dbo.sites_ AS s ON s.name_ = t.sitename_
INNER JOIN dbo.lists_format AS lf ON lf.name = l.Name_
INNER JOIN membercentral.membercentral.dbo.sites AS mc_s ON mc_s.sitecode = lf.orgcode COLLATE Latin1_General_CI_AI 
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud ON sud.sendingHostname = s.domainName_ COLLATE Latin1_General_CI_AI 
	AND sud.statusID = @activeStatusID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subusers AS su 
	ON su.subuserID = sud.subuserID
	and su.environmentID=@environmentID
	AND su.siteID = mc_s.siteID
	AND su.statusID = @activeStatusID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserMailstreams AS sums ON sums.subuserID = su.subuserID
INNER JOIN membercentral.platformmail.dbo.email_mailstreams AS ms ON ms.mailStreamID = sums.mailstreamID
	AND ms.mailStreamCode = 'LISTS'
WHERE tmp.MCSetting_subuserDomainID IS NULL;

-- the domain that matches the lyris site sending domain on the platformwide default listserver subuser when the subuser and domain are both active status
UPDATE tmp
SET tmp.MCSetting_subuserDomainID = sud.subuserDomainID
FROM @tmpDiscussionLists AS tmp
INNER JOIN dbo.lists_ AS l ON l.name_ = tmp.listName
INNER JOIN dbo.topics_ AS t ON t.title_ = l.topic_
INNER JOIN dbo.sites_ AS s ON s.name_ = t.sitename_
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud 
	ON sud.sendingHostname = s.domainName_ COLLATE Latin1_General_CI_AI 
	AND sud.statusID = @activeStatusID
	and sud.outboundProviderID = @platformLISTOutboundProviderID
	and sud.subuserID = @platformLISTSubUserID
WHERE tmp.MCSetting_subuserDomainID IS NULL;

-- the primary domain on the platformwide default listserver subuser when the subuser and domain are both active status
UPDATE @tmpDiscussionLists
SET MCSetting_subuserDomainID = @platformLISTPrimarySubUserDomainID
WHERE MCSetting_subuserDomainID IS NULL;


-- delete matching MCSetting_subuserDomainID rows
DELETE tmp
FROM @tmpMarketingLists AS tmp
INNER JOIN dbo.lists_ AS l ON l.name_ = tmp.listName
WHERE tmp.MCSetting_subuserDomainID = l.MCSetting_subuserDomainID;

DELETE tmp
FROM @tmpDiscussionLists AS tmp
INNER JOIN dbo.lists_ AS l ON l.name_ = tmp.listName
WHERE tmp.MCSetting_subuserDomainID = l.MCSetting_subuserDomainID;

IF EXISTS (SELECT 1 FROM @tmpMarketingLists) OR EXISTS (SELECT 1 FROM @tmpDiscussionLists) BEGIN
	-- update MCSetting_subuserDomainID
	UPDATE l
	SET l.MCSetting_subuserDomainID = tmp.MCSetting_subuserDomainID,
		l.MCSetting_configLastChanged = GETDATE()
	FROM dbo.lists_ AS l
	INNER JOIN (
		SELECT listName, MCSetting_subuserDomainID FROM @tmpMarketingLists
			UNION
		SELECT listName, MCSetting_subuserDomainID FROM @tmpDiscussionLists
	) tmp ON tmp.listName = l.name_;
END


-- update discussion lists fields
UPDATE l
SET l.NoListHdr_ = 'F',
	l.AddHeadersAndFooters_ = 'N',
	l.SMTPFrom_ = '"%%merge member.fullname%% (%%list.name%% listserver)" <listsender-%%list.name%%@'+sud.sendingHostname+'>',
	l.Replyto_ = CASE WHEN l.Replyto_ = 'author' THEN l.Replyto_ COLLATE Latin1_General_CI_AI
					ELSE '"%%merge list.descshort%%" <%%merge list.name%%@'+sud.sendingHostname+'>' END,
	l.HdrRemove_ = 'X-List-Host'+ @headerLineEndings +'X-URL'+ @headerLineEndings +'List-Owner'+ @headerLineEndings +'List-Subscribe' + @headerLineEndings +'ARC-Authentication-Results' + @headerLineEndings + 'ARC-Message-Signature' + @headerLineEndings + 'ARC-Seal' + @headerLineEndings + 'X-SES-DKIM-SIGNATURE' + @headerLineEndings + 'X-SES-RECEIPT' + @headerLineEndings + 'Authentication-Results' + @headerLineEndings + 'Received-SPF' + @headerLineEndings + 'X-SES-Virus-Verdict' + @headerLineEndings + 'X-SES-Spam-Verdict' + @headerLineEndings +  'List-unsubscribe' + @headerLineEndings + 'Precedence'
FROM dbo.lists_ AS l
inner join dbo.lists_format lf on lf.name = l.name_
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud ON sud.subuserDomainID = l.MCSetting_subuserDomainID
WHERE l.MCSetting_ListType = 'discussion'
AND l.name_ = isnull(@listName,l.name_);

UPDATE l
SET l.SMTPHdrs_ = 'Precedence: list'+ @headerLineEndings +
	'X-Auto-Response-Suppress: OOF'+ @headerLineEndings +
	'X-MC-SENDGRIDSUBUSER: ' +su.username COLLATE database_default + @headerLineEndings +
	'X-WARMUP-OK: ' + case when sud.outboundproviderID = @halonOutboudProviderID then 'Y' else 'N' end + @headerLineEndings +
	'X-Relay-Volume: ' + case 
		when sud.outboundproviderID = @sendgridOutboudProviderID then '100' 
		when wps.stepRatio is not null then cast(cast(((1-wps.stepRatio)* 100) as int) as varchar(5))
		else '0' end + @headerLineEndings +
	'X-HALON-WHITELABEL: {"todomain":"' + sud.sendingHostname COLLATE database_default + '","mailfromDomain":"' + sud.returnPathHostname COLLATE database_default + '","trackingDomain":"' + sud.linkBrandHostname + '"}' + @headerLineEndings +
	case when sud.outboundproviderID <> @halonOutboudProviderID then '' else 'X-HALON-IPPOOL: ' + lower(ipp.poolName) COLLATE database_default + @headerLineEndings end +
	case when sud.dkimSelectorActive is null then '' else 'X-DKIM: ' + sud.dkimSelectorActive COLLATE database_default + ',' + lower(s.sitecode) + '.' + sud.dkimSelectorActive COLLATE database_default + ',' +  sud.sendingHostname + @headerLineEndings end +
	'X-List-unsubscribe: <mailto:%%merge email.unsub%%>, <https://' + sh.hostname + '/?pg=emailPreferences&listserverid=%%merge recip.mcemailkey%%>'+ @headerLineEndings +
	'List-Unsubscribe-Post: List-Unsubscribe=One-Click'+ @headerLineEndings +
	'X-SMTPAPI: {"filters": {"clicktrack": {"settings": {"enable": 0,"enable_text": false}}}, "ip_pool": "'+ isnull(phaseout_ippools.poolname,ipp.poolName) COLLATE database_default +'", "category": ["Discussion Listserver"], "unique_args":{"v": "2-production","mc_mailtype":"lyris_'+ l.MCSetting_ListType +'","sa_name":"%%list.name%%","sa_msgid":"%%merge outmail_.archivemessageid_%%","sa_rcp":"%%merge recip.externalMemberID%%|%%memberid%%","mc_sud":"'+sud.sendingHostname COLLATE database_default +'","mc_site":"' +s.siteCode COLLATE database_default + '-' + +su.username COLLATE database_default +'","sendgrid_pool":"'+ isnull(phaseout_ippools.poolname,ipp.poolName) COLLATE database_default +'"}}'
FROM dbo.lists_ AS l
inner join dbo.lists_format lf on lf.name = l.name_
INNER JOIN dbo.topics_ AS t ON t.title_ = l.topic_
INNER JOIN dbo.sites_ AS ls ON ls.name_ = t.sitename_
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud ON sud.subuserDomainID = l.MCSetting_subuserDomainID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subusers AS su ON su.subuserID = sud.subuserID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserMailstreams AS sums ON sums.subuserID = su.subuserID
INNER JOIN membercentral.platformmail.dbo.email_mailstreams AS ms ON ms.mailStreamID = sums.mailstreamID
	AND ms.mailStreamCode = 'LISTS'
INNER JOIN membercentral.platformmail.dbo.sendgrid_ipPools AS ipp ON ipp.ipPoolID = sums.ipPoolID
INNER JOIN membercentral.membercentral.dbo.sites AS s ON s.siteID = su.siteID
inner join membercentral.membercentral.dbo.siteEnvironments se on se.siteID = s.siteID and se.environmentID = @environmentID
inner join membercentral.membercentral.dbo.siteHostnames as sh on sh.hostnameID = se.mainHostnameID
left outer join membercentral.platformmail.dbo.sendgrid_subuserMailstreamWarmupPlans wp
    inner join membercentral.platformmail.dbo.sendgrid_subuserMailstreamWarmupPlanSteps wps
        on wp.warmupPlanID = wps.warmupPlanID
        and wps.status = 'A'
    inner join membercentral.platformmail.dbo.[sendgrid_subusers] phaseout_su
        on phaseout_su.subuserID = wp.phaseout_SubuserID
    inner join membercentral.platformmail.dbo.[sendgrid_subuserDomains] phaseout_sud 
        on phaseout_sud.subuserDomainID = wp.phaseout_SubuserDomainID
    inner join membercentral.platformmail.dbo.[sendgrid_ipPools] phaseout_ippools
        on phaseout_ippools.ippoolID = wp.phaseout_IPPoolID
    inner join membercentral.platformmail.dbo.email_outboundProviders phaseout_mp 
        on phaseout_mp.outboundProviderID = phaseout_ippools.outboundProviderID
    inner join membercentral.platformmail.dbo.email_outboundProviderEnvironments phaseout_mpe
        on phaseout_mpe.outboundProviderID = phaseout_mp.outboundProviderID
        and phaseout_mpe.environmentID = @environmentID
on wp.siteID = phaseout_su.siteID
and wp.mailStreamID = sums.mailStreamID
and wp.phasein_SubUserID = sums.subuserID
and wp.status = 'A'
WHERE l.MCSetting_ListType = 'discussion'
AND l.name_ = isnull(@listName,l.name_);



-- update hello, goodbye and other list-specific document templates for discussion lists

update d set 
	d.HdrFrom_ = '"%%merge list.descshort%%" <listsender-%%list.name%%@'+sud.sendingHostname+'>'
from messagetypes_ mt 
inner join listdocs_ ld 
    on mt.messageTypeID_ = ld.messageTypeID_
    and mt.name_ in ('list-hello','list-confirm','list-goodbye','list-held','list-private','list-delivery-report','list-unsub-confirm','list-reset-password','list-reset-password-mri','list-post-failed-duplicate','list-join-private-approved-response')
inner join docs_ d 
    on d.DocID_ = ld.DocID_
    and d.site_ like 'list:%'
inner join lists_ l 
    on l.listID_ = ld.ListID_
inner join dbo.lists_format lf on lf.name = l.name_
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud ON sud.subuserDomainID = l.MCSetting_subuserDomainID
WHERE l.MCSetting_ListType = 'discussion'
AND l.name_ = isnull(@listName,l.name_);


UPDATE dp
SET HdrAdd_ = 
	'X-Auto-Response-Suppress: OOF'+ @headerLineEndings +
	'X-MC-SENDGRIDSUBUSER: ' +su.username COLLATE database_default + @headerLineEndings +
	'X-WARMUP-OK: ' + case when sud.outboundproviderID = @halonOutboudProviderID then 'Y' else 'N' end + @headerLineEndings +
	'X-Relay-Volume: ' + case 
		when sud.outboundproviderID = @sendgridOutboudProviderID then '100' 
		when wps.stepRatio is not null then cast(cast(((1-wps.stepRatio)* 100) as int) as varchar(5))
		else '0' end + @headerLineEndings +
	'X-HALON-WHITELABEL: {"todomain":"' + sud.sendingHostname COLLATE database_default + '","mailfromDomain":"' + sud.returnPathHostname COLLATE database_default + '","trackingDomain":"' + sud.linkBrandHostname + '"}' + @headerLineEndings +
	case when sud.outboundproviderID <> @halonOutboudProviderID then '' else 'X-HALON-IPPOOL: ' + lower(ipp.poolName) COLLATE database_default + @headerLineEndings end +
	case when sud.dkimSelectorActive is null then '' else 'X-DKIM: ' + sud.dkimSelectorActive COLLATE database_default + ',' + lower(s.sitecode) + '.' + sud.dkimSelectorActive COLLATE database_default + ',' +  sud.sendingHostname + @headerLineEndings end +
	'X-SMTPAPI: {"filters": {"clicktrack": {"settings": {"enable": 0,"enable_text": false}}}, "ip_pool": "'+ isnull(phaseout_ippools.poolname,ipp.poolName) COLLATE database_default +'", "category": ["Discussion Listserver"], "unique_args":{"v": "2-production","mc_mailtype":"lyris_'+ l.MCSetting_ListType +'","sa_name":"%%list.name%%","sa_msgid":"%%merge outmail_.archivemessageid_%%","sa_rcp":"%%merge recip.externalMemberID%%|%%memberid%%","mc_sud":"'+sud.sendingHostname COLLATE database_default +'","mc_site":"' +s.siteCode COLLATE database_default + '-' + +su.username COLLATE database_default +'","sendgrid_pool":"'+ isnull(phaseout_ippools.poolname,ipp.poolName) COLLATE database_default +'"}}'
from messagetypes_ mt 
inner join listdocs_ ld 
    on mt.messageTypeID_ = ld.messageTypeID_
    and mt.name_ in ('list-hello','list-confirm','list-goodbye','list-held','list-private','list-delivery-report','list-unsub-confirm','list-reset-password','list-reset-password-mri','list-post-failed-duplicate','list-join-private-approved-response')
inner join docs_ d 
    on d.DocID_ = ld.DocID_
    and d.site_ like 'list:%'
inner join docparts_ dp 
    on dp.DocID_ = d.DocID_
inner join dbo.lists_ AS l
	on l.listID_ = ld.ListID_
inner join dbo.lists_format lf on lf.name = l.name_
INNER JOIN dbo.topics_ AS t ON t.title_ = l.topic_
INNER JOIN dbo.sites_ AS ls ON ls.name_ = t.sitename_
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud ON sud.subuserDomainID = l.MCSetting_subuserDomainID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subusers AS su ON su.subuserID = sud.subuserID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserMailstreams AS sums ON sums.subuserID = su.subuserID
INNER JOIN membercentral.platformmail.dbo.email_mailstreams AS ms ON ms.mailStreamID = sums.mailstreamID
	AND ms.mailStreamCode = 'LISTS'
INNER JOIN membercentral.platformmail.dbo.sendgrid_ipPools AS ipp ON ipp.ipPoolID = sums.ipPoolID
INNER JOIN membercentral.membercentral.dbo.sites AS s ON s.siteID = su.siteID
inner join membercentral.membercentral.dbo.siteEnvironments se on se.siteID = s.siteID and se.environmentID = @environmentID
inner join membercentral.membercentral.dbo.siteHostnames as sh on sh.hostnameID = se.mainHostnameID
left outer join membercentral.platformmail.dbo.sendgrid_subuserMailstreamWarmupPlans wp
    inner join membercentral.platformmail.dbo.sendgrid_subuserMailstreamWarmupPlanSteps wps
        on wp.warmupPlanID = wps.warmupPlanID
        and wps.status = 'A'
    inner join membercentral.platformmail.dbo.[sendgrid_subusers] phaseout_su
        on phaseout_su.subuserID = wp.phaseout_SubuserID
    inner join membercentral.platformmail.dbo.[sendgrid_subuserDomains] phaseout_sud 
        on phaseout_sud.subuserDomainID = wp.phaseout_SubuserDomainID
    inner join membercentral.platformmail.dbo.[sendgrid_ipPools] phaseout_ippools
        on phaseout_ippools.ippoolID = wp.phaseout_IPPoolID
    inner join membercentral.platformmail.dbo.email_outboundProviders phaseout_mp 
        on phaseout_mp.outboundProviderID = phaseout_ippools.outboundProviderID
    inner join membercentral.platformmail.dbo.email_outboundProviderEnvironments phaseout_mpe
        on phaseout_mpe.outboundProviderID = phaseout_mp.outboundProviderID
        and phaseout_mpe.environmentID = @environmentID
on wp.siteID = phaseout_su.siteID
and wp.mailStreamID = sums.mailStreamID
and wp.phasein_SubUserID = sums.subuserID
and wp.status = 'A'
WHERE l.MCSetting_ListType = 'discussion'
AND l.name_ = isnull(@listName,l.name_);



-- update marketing lists fields

-- don't touch SMTPFrom_ and Replyto_ on marketing lists that have used segments in the 2 years
declare @twoyearsago datetime = dateadd(YEAR,-2,getdate())
DECLARE @marketingListsThatUseSegments TABLE (listname varchar(100) PRIMARY KEY);
insert into @marketingListsThatUseSegments
select list_
from messages_ m
inner join lists_ l
    on l.name_ = m.list_
	and l.AdminSend_ = 'T'
	and m.SubsetID_ is not null 
	and m.SubsetID_ > 0 
and m.CreatStamp_ > @twoyearsago
group by list_


UPDATE l SET 
	l.SMTPFrom_ = '"%%merge member.fullname%% (%%list.name%% listserver)" <listsender-%%list.name%%@'+sud.sendingHostname+'>',
	l.Replyto_ = CASE 
		WHEN l.Replyto_ IS NOT NULL AND lyrisarchive.dbo.fn_RegExReplace(ReplyTo_,'(?:"?([^"]*)"?\s)?(?:<?(.+@[^>]+)>?)','') = '' THEN l.Replyto_
		WHEN l.Replyto_ in ('nochange','author') THEN l.Replyto_
		ELSE 'author' 
	END
FROM dbo.lists_ AS l
left outer join @marketingListsThatUseSegments mls on mls.listname = l.name_
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud ON sud.subuserDomainID = l.MCSetting_subuserDomainID
WHERE l.MCSetting_ListType in  ('marketing','swmarketing') and mls.listname is null
AND l.name_ = isnull(@listName,l.name_);


UPDATE l SET
	l.NoListHdr_ = 'F',
	l.AddHeadersAndFooters_ = 'N', 
	l.HdrRemove_ = 'X-List-Host'+ @headerLineEndings +'X-URL'+ @headerLineEndings +'List-Owner'+ @headerLineEndings +'List-Subscribe' + @headerLineEndings + 'ARC-Authentication-Results' + @headerLineEndings + 'ARC-Message-Signature' + @headerLineEndings + 'ARC-Seal' + @headerLineEndings + 'X-SES-DKIM-SIGNATURE' + @headerLineEndings + 'X-SES-RECEIPT' + @headerLineEndings + 'Authentication-Results' + @headerLineEndings + 'Received-SPF' + @headerLineEndings + 'X-SES-Virus-Verdict' + @headerLineEndings + 'X-SES-Spam-Verdict'  + @headerLineEndings +  'List-unsubscribe' + @headerLineEndings + 'Precedence',
	l.SMTPHdrs_ = 'Precedence: list'+ @headerLineEndings +
					'X-Auto-Response-Suppress: OOF'+ @headerLineEndings +
					'X-MC-SENDGRIDSUBUSER: '+su.username + @headerLineEndings +
					'X-WARMUP-OK: ' + case when sud.outboundproviderID = @halonOutboudProviderID then 'Y' else 'N' end + @headerLineEndings +
					'X-Relay-Volume: ' + case 
						when sud.outboundproviderID = @sendgridOutboudProviderID then '100' 
						when wps.stepRatio is not null then cast(cast(((1-wps.stepRatio)* 100) as int) as varchar(5))
						else '0' end + @headerLineEndings +
					'X-HALON-WHITELABEL: {"todomain":"' + sud.sendingHostname COLLATE database_default + '","mailfromDomain":"' + sud.returnPathHostname COLLATE database_default + '","trackingDomain":"' + sud.linkBrandHostname + '"}' + @headerLineEndings +
					case when sud.outboundproviderID <> @halonOutboudProviderID then '' else 'X-HALON-IPPOOL: ' + lower(ipp.poolName) COLLATE database_default + @headerLineEndings end +
					case when sud.dkimSelectorActive is null then '' else 'X-DKIM: ' + sud.dkimSelectorActive COLLATE database_default + ',' + lower(s.sitecode) + '.' + sud.dkimSelectorActive COLLATE database_default + ',' +  sud.sendingHostname + @headerLineEndings end +
					'X-List-unsubscribe: <mailto:%%merge email.unsub%%>, <https://' + sh.hostname + '/?pg=emailPreferences&listserverid=%%merge recip.mcemailkey%%>'+ @headerLineEndings +
					'List-Unsubscribe-Post: List-Unsubscribe=One-Click'+ @headerLineEndings +
					'X-SMTPAPI: {"ip_pool": "medium", "category": ["Marketing Listserver"], "unique_args":{"v": "2-production","mc_mailtype":"lyris_'+ l.MCSetting_ListType +'","sa_name":"%%list.name%%","sa_msgid":"%%merge outmail_.archivemessageid_%%","sa_rcp":"%%merge recip.externalMemberID%%|%%memberid%%","mc_sud":"'+sud.sendingHostname COLLATE database_default +'","mc_site":"' +s.siteCode COLLATE database_default + '-' + +su.username COLLATE database_default +'","sendgrid_pool":"medium"}}'
FROM dbo.lists_ AS l
INNER JOIN dbo.topics_ AS t ON t.title_ = l.topic_
INNER JOIN dbo.sites_ AS ls ON ls.name_ = t.sitename_
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud ON sud.subuserDomainID = l.MCSetting_subuserDomainID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subusers AS su ON su.subuserID = sud.subuserID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserMailstreams AS sums ON sums.subuserID = su.subuserID
INNER JOIN membercentral.platformmail.dbo.email_mailstreams AS ms ON ms.mailStreamID = sums.mailstreamID
	AND ms.mailStreamCode = 'MKT'
INNER JOIN membercentral.platformmail.dbo.sendgrid_ipPools AS ipp ON ipp.ipPoolID = sums.ipPoolID
INNER JOIN membercentral.membercentral.dbo.sites AS s ON s.siteID = su.siteID
inner join membercentral.membercentral.dbo.siteEnvironments se on se.siteID = s.siteID and se.environmentID = @environmentID
inner join membercentral.membercentral.dbo.siteHostnames as sh on sh.hostnameID = se.mainHostnameID
left outer join membercentral.platformmail.dbo.sendgrid_subuserMailstreamWarmupPlans wp
    inner join membercentral.platformmail.dbo.sendgrid_subuserMailstreamWarmupPlanSteps wps
        on wp.warmupPlanID = wps.warmupPlanID
        and wps.status = 'A'
    inner join membercentral.platformmail.dbo.[sendgrid_subusers] phaseout_su
        on phaseout_su.subuserID = wp.phaseout_SubuserID
    inner join membercentral.platformmail.dbo.[sendgrid_subuserDomains] phaseout_sud 
        on phaseout_sud.subuserDomainID = wp.phaseout_SubuserDomainID
    inner join membercentral.platformmail.dbo.[sendgrid_ipPools] phaseout_ippools
        on phaseout_ippools.ippoolID = wp.phaseout_IPPoolID
    inner join membercentral.platformmail.dbo.email_outboundProviders phaseout_mp 
        on phaseout_mp.outboundProviderID = phaseout_ippools.outboundProviderID
    inner join membercentral.platformmail.dbo.email_outboundProviderEnvironments phaseout_mpe
        on phaseout_mpe.outboundProviderID = phaseout_mp.outboundProviderID
        and phaseout_mpe.environmentID = @environmentID
on wp.siteID = phaseout_su.siteID
and wp.mailStreamID = sums.mailStreamID
and wp.phasein_SubUserID = sums.subuserID
and wp.status = 'A'
WHERE l.MCSetting_ListType in  ('marketing','swmarketing')
AND l.name_ = isnull(@listName,l.name_);



-- update hello, goodbye and other list-specific document templates for marketing lists


update d set 
	d.HdrFrom_ = '"%%merge list.descshort%%" <listsender-%%list.name%%@'+sud.sendingHostname+'>'
from messagetypes_ mt 
inner join listdocs_ ld 
    on mt.messageTypeID_ = ld.messageTypeID_
    and mt.name_ in ('list-hello','list-confirm','list-goodbye','list-held','list-private','list-delivery-report','list-unsub-confirm','list-reset-password','list-reset-password-mri','list-post-failed-duplicate','list-join-private-approved-response')
inner join docs_ d 
    on d.DocID_ = ld.DocID_
    and d.site_ like 'list:%'
inner join lists_ l 
    on l.listID_ = ld.ListID_
inner join dbo.lists_format lf on lf.name = l.name_
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud ON sud.subuserDomainID = l.MCSetting_subuserDomainID
WHERE l.MCSetting_ListType in  ('marketing','swmarketing')
AND l.name_ = isnull(@listName,l.name_);



UPDATE dp SET
	HdrAdd_ = 
		'X-Auto-Response-Suppress: OOF'+ @headerLineEndings +
		'X-MC-SENDGRIDSUBUSER: '+su.username + @headerLineEndings +
		'X-WARMUP-OK: ' + case when sud.outboundproviderID = @halonOutboudProviderID then 'Y' else 'N' end + @headerLineEndings +
		'X-Relay-Volume: ' + case 
			when sud.outboundproviderID = @sendgridOutboudProviderID then '100' 
			when wps.stepRatio is not null then cast(cast(((1-wps.stepRatio)* 100) as int) as varchar(5))
			else '0' end + @headerLineEndings +
		'X-HALON-WHITELABEL: {"todomain":"' + sud.sendingHostname COLLATE database_default + '","mailfromDomain":"' + sud.returnPathHostname COLLATE database_default + '","trackingDomain":"' + sud.linkBrandHostname + '"}' + @headerLineEndings +
		case when sud.outboundproviderID <> @halonOutboudProviderID then '' else 'X-HALON-IPPOOL: ' + lower(ipp.poolName) COLLATE database_default + @headerLineEndings end +
		case when sud.dkimSelectorActive is null then '' else 'X-DKIM: ' + sud.dkimSelectorActive COLLATE database_default + ',' + lower(s.sitecode) + '.' + sud.dkimSelectorActive COLLATE database_default + ',' +  sud.sendingHostname + @headerLineEndings end +
		'X-SMTPAPI: {"ip_pool": "medium", "category": ["Marketing Listserver"], "unique_args":{"v": "2-production","mc_mailtype":"lyris_'+ l.MCSetting_ListType +'","sa_name":"%%list.name%%","sa_msgid":"%%merge outmail_.archivemessageid_%%","sa_rcp":"%%merge recip.externalMemberID%%|%%memberid%%","mc_sud":"'+sud.sendingHostname COLLATE database_default +'","mc_site":"' +s.siteCode COLLATE database_default + '-' + +su.username COLLATE database_default +'","sendgrid_pool":"medium"}}'
from messagetypes_ mt 
inner join listdocs_ ld 
    on mt.messageTypeID_ = ld.messageTypeID_
    and mt.name_ in ('list-hello','list-confirm','list-goodbye','list-held','list-private','list-delivery-report','list-unsub-confirm','list-reset-password','list-reset-password-mri','list-post-failed-duplicate','list-join-private-approved-response')
inner join docs_ d 
    on d.DocID_ = ld.DocID_
    and d.site_ like 'list:%'
inner join docparts_ dp 
    on dp.DocID_ = d.DocID_
inner join dbo.lists_ AS l
	on l.listID_ = ld.ListID_
INNER JOIN dbo.topics_ AS t ON t.title_ = l.topic_
INNER JOIN dbo.sites_ AS ls ON ls.name_ = t.sitename_
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserDomains AS sud ON sud.subuserDomainID = l.MCSetting_subuserDomainID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subusers AS su ON su.subuserID = sud.subuserID
INNER JOIN membercentral.platformmail.dbo.sendgrid_subuserMailstreams AS sums ON sums.subuserID = su.subuserID
INNER JOIN membercentral.platformmail.dbo.email_mailstreams AS ms ON ms.mailStreamID = sums.mailstreamID
	AND ms.mailStreamCode = 'MKT'
INNER JOIN membercentral.platformmail.dbo.sendgrid_ipPools AS ipp ON ipp.ipPoolID = sums.ipPoolID
INNER JOIN membercentral.membercentral.dbo.sites AS s ON s.siteID = su.siteID
inner join membercentral.membercentral.dbo.siteEnvironments se on se.siteID = s.siteID and se.environmentID = @environmentID
inner join membercentral.membercentral.dbo.siteHostnames as sh on sh.hostnameID = se.mainHostnameID
left outer join membercentral.platformmail.dbo.sendgrid_subuserMailstreamWarmupPlans wp
    inner join membercentral.platformmail.dbo.sendgrid_subuserMailstreamWarmupPlanSteps wps
        on wp.warmupPlanID = wps.warmupPlanID
        and wps.status = 'A'
    inner join membercentral.platformmail.dbo.[sendgrid_subusers] phaseout_su
        on phaseout_su.subuserID = wp.phaseout_SubuserID
    inner join membercentral.platformmail.dbo.[sendgrid_subuserDomains] phaseout_sud 
        on phaseout_sud.subuserDomainID = wp.phaseout_SubuserDomainID
    inner join membercentral.platformmail.dbo.[sendgrid_ipPools] phaseout_ippools
        on phaseout_ippools.ippoolID = wp.phaseout_IPPoolID
    inner join membercentral.platformmail.dbo.email_outboundProviders phaseout_mp 
        on phaseout_mp.outboundProviderID = phaseout_ippools.outboundProviderID
    inner join membercentral.platformmail.dbo.email_outboundProviderEnvironments phaseout_mpe
        on phaseout_mpe.outboundProviderID = phaseout_mp.outboundProviderID
        and phaseout_mpe.environmentID = @environmentID
on wp.siteID = phaseout_su.siteID
and wp.mailStreamID = sums.mailStreamID
and wp.phasein_SubUserID = sums.subuserID
and wp.status = 'A'
WHERE l.MCSetting_ListType in  ('marketing','swmarketing')
AND l.name_ = isnull(@listName,l.name_);



RETURN 0;
GO
