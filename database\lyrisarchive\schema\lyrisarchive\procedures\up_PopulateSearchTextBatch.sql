ALTER PROCEDURE [dbo].[up_PopulateSearchTextBatch]
@batchSize int
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	DECLARE @currentMessageID int
	DECLARE @currentAutoID int

	DECLARE @lastRecordProcessed int
	DECLARE @newLastRecordProcessed int

	select @lastRecordProcessed = lastRecordProcessed from TLASITES.search.dbo.tblSearchUpdateStatus where name = 'listmessages'


	DECLARE @MessageQueue TABLE (autoid int IDENTITY(1,1), messageid_ int, searchUID int)

	INSERT INTO @MessageQueue (messageid_, searchUID)
	select top (@batchSize) messageid_, searchUID from messages_
	where searchUID > @lastRecordProcessed
	order by searchUID


	select @newLastRecordProcessed = max(searchUID) from @MessageQueue


	select top 1 @currentMessageID = messageid_, @currentAutoID = autoid from @MessageQueue order by autoid

	while @currentMessageID is not null
	begin
		
		update messages_
		set searchtext = dbo.fn_GetMessageSearchText(@currentMessageID),
			attachmentflag = dbo.fn_HasAttachment(@currentMessageID)
		where messageid_ = @currentMessageID

		--select messageid_, searchtext from messages_ where messageid_ = @currentMessageID

		if exists (select top 1 messageid_ from @MessageQueue where autoid > @currentAutoID)
			select top 1 @currentMessageID = messageid_, @currentAutoID = autoid from @MessageQueue where autoid > @currentAutoID order by autoid
		else
			select @currentMessageID = null
	end

	if @newLastRecordProcessed is not null
		update TLASITES.search.dbo.tblSearchUpdateStatus set lastRecordProcessed = @newLastRecordProcessed where name = 'listmessages'

END
GO
