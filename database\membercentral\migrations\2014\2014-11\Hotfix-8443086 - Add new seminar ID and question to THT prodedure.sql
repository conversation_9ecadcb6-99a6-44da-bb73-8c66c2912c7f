USE seminarWeb
GO

ALTER PROC [dbo].[THT_EvaluationsResponse]
@enrollmentID int

AS

SET NOCOUNT ON

-- drop if exists
IF OBJECT_ID('tempdb..##tmpSWCredit') IS NOT NULL 
	DROP TABLE ##tmpSWCredit

-- drop if exists
IF OBJECT_ID('tempdb..##tmpSWCreditAll') IS NOT NULL 
	DROP TABLE ##tmpSWCreditAll

; WITH enrollments AS (
	select e.enrollmentID
	FROM tblenrollments as e
	INNER JOIN tblParticipants p on e.participantID = p.participantID and p.orgcode = 'THT'
	INNER JOIN tblSeminars as sem on sem.seminarID = e.seminarID
	LEFT OUTER JOIN tblSeminarsSWOD as sswod on sswod.seminarID = sem.seminarID
	INNER JOIN tblUsers as u on u.userid = e.userid
	INNER JOIN trialsmith.dbo.depomemberdata as d on d.depomemberdataid = u.depomemberdataid
	WHERE e.isactive = 1 
		AND e.enrollmentID = @enrollmentID 
		AND e.seminarID in (3299, 5735)
), formresponses AS (
	SELECT rd.detailID, safr.enrollmentID, rd.questionID, rd.optionID, 
		CASE 
			WHEN rd.questionID=3452 and optionXID=555 then 1
			WHEN rd.questionID=3452 and optionXID=556 then 2
			WHEN rd.questionID=3563 and optionXID=557 then 1
			WHEN rd.questionID=3563 and optionXID=558 then 2
		ELSE 0
		END AS matrixVal
, op.optionText, rd.responseText
	FROM seminarweb.dbo.tblSeminarsAndFormResponses AS safr
	INNER JOIN enrollments as e on e.enrollmentID = safr.enrollmentID
	INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID
		AND r.formID = 344
		AND r.isActive = 1
	INNER JOIN formbuilder.dbo.tblResponseDetails as rd on rd.responseID = r.responseID
	LEFT OUTER JOIN formbuilder.dbo.tblOptions as op on rd.questionID = op.questionID and rd.optionID = op.optionID
)

SELECT 
	CASE 
		WHEN md.Hospital IS NOT NULL THEN md.Hospital
		ELSE md.[Texas Health Resource]
	END
		as Hospital,	
	q1.responseText as [BoardTitle],
	q2.responseText as [Phone],
	q3.optionText as [MoreThanYear],
	q4.optionText as [EducationHours],
	CASE 
		WHEN q5.[13333]=1 THEN 'Yes' 
		WHEN q5.[13333]=2 THEN 'No' 
	END
		as [Standards1],
	CASE 
		WHEN q5.[13334]=1 THEN 'Yes' 
		WHEN q5.[13334]=2 THEN 'No' 
	END
		as [Standards2],
	CASE 
		WHEN q5.[13335]=1 THEN 'Yes' 
		WHEN q5.[13335]=2 THEN 'No' 
	END
		as [Standards3],
	CASE 
		WHEN q5.[13336]=1 THEN 'Yes' 
		WHEN q5.[13336]=2 THEN 'No' 
	END
		as [Standards4],
	CASE 
		WHEN q5.[13337]=1 THEN 'Yes' 
		WHEN q5.[13337]=2 THEN 'No' 
	END
		as [Standards5],
	CASE 
		WHEN q5.[13338]=1 THEN 'Yes' 
		WHEN q5.[13338]=2 THEN 'No' 
	END
		as [Standards6],
	CASE 
		WHEN q5.[13339]=1 THEN 'Yes' 
		WHEN q5.[13339]=2 THEN 'No' 
	END
		as [Standards7],
	CASE 
		WHEN q5.[13340]=1 THEN 'Yes' 
		WHEN q5.[13340]=2 THEN 'No' 
	END
		as [Standards8],
	CASE 
		WHEN q5.[13341]=1 THEN 'Yes' 
		WHEN q5.[13341]=2 THEN 'No' 
	END
		as [Standards9],
	CASE 
		WHEN q6.[12867]=1 THEN 'Yes' 
		WHEN q6.[12867]=2 THEN 'No' 
	END
		as [Education1],
	CASE 
		WHEN q6.[12868]=1 THEN 'Yes' 
		WHEN q6.[12868]=2 THEN 'No' 
	END
		as [Education2],
	CASE 
		WHEN q6.[12869]=1 THEN 'Yes' 
		WHEN q6.[12869]=2 THEN 'No' 
	END
		as [Education3],
	CASE 
		WHEN q6.[12870]=1 THEN 'Yes' 
		WHEN q6.[12870]=2 THEN 'No' 
	END
		as [Education4],
	CASE 
		WHEN q6.[12871]=1 THEN 'Yes' 
		WHEN q6.[12871]=2 THEN 'No' 
	END
		as [Education5],
	CASE 
		WHEN q6.[12872]=1 THEN 'Yes' 
		WHEN q6.[12872]=2 THEN 'No' 
	END
		as [Education6],
	CASE 
		WHEN q6.[12873]=1 THEN 'Yes' 
		WHEN q6.[12873]=2 THEN 'No' 
	END
		as [Education7],
	CASE 
		WHEN q6.[12874]=1 THEN 'Yes' 
		WHEN q6.[12874]=2 THEN 'No' 
	END
		as [Education8],
	CASE 
		WHEN q6.[12875]=1 THEN 'Yes' 
		WHEN q6.[12875]=2 THEN 'No' 
	END
		as [Education9],
	CASE 
		WHEN q6.[12876]=1 THEN 'Yes' 
		WHEN q6.[12876]=2 THEN 'No' 
	END
		as [Education10],
	CASE 
		WHEN q6.[12877]=1 THEN 'Yes' 
		WHEN q6.[12877]=2 THEN 'No' 
	END
		as [Education11],
	CASE 
		WHEN q6.[12878]=1 THEN 'Yes' 
		WHEN q6.[12878]=2 THEN 'No' 
	END
		as [Education12],
	CASE 
		WHEN q6.[12879]=1 THEN 'Yes' 
		WHEN q6.[12879]=2 THEN 'No' 
	END
		as [Education13],
	CASE 
		WHEN q6.[12880]=1 THEN 'Yes' 
		WHEN q6.[12880]=2 THEN 'No' 
	END
		as [Education14],
	CASE 
		WHEN q6.[12881]=1 THEN 'Yes' 
		WHEN q6.[12881]=2 THEN 'No' 
	END
		as [Education15],
	CASE 
		WHEN q6.[12882]=1 THEN 'Yes' 
		WHEN q6.[12882]=2 THEN 'No' 
	END
		as [Education16],
	CASE 
		WHEN q6.[12883]=1 THEN 'Yes' 
		WHEN q6.[12883]=2 THEN 'No' 
	END
		as [Education17],
	CASE 
		WHEN q6.[12884]=1 THEN 'Yes' 
		WHEN q6.[12884]=2 THEN 'No' 
	END
		as [Education18],
	CASE 
		WHEN q6.[12885]=1 THEN 'Yes' 
		WHEN q6.[12885]=2 THEN 'No' 
	END
		as [Education19],
	CASE 
		WHEN q6.[12886]=1 THEN 'Yes' 
		WHEN q6.[12886]=2 THEN 'No' 
	END
		as [Education20],
	CASE 
		WHEN q6.[12887]=1 THEN 'Yes' 
		WHEN q6.[12887]=2 THEN 'No' 
	END
		as [Education21],
	CASE 
		WHEN q6.[12888]=1 THEN 'Yes' 
		WHEN q6.[12888]=2 THEN 'No' 
	END
		as [Education22],
	CASE 
		WHEN q6.[12889]=1 THEN 'Yes' 
		WHEN q6.[12889]=2 THEN 'No' 
	END
		as [Education23],
	CASE 
		WHEN q6.[12890]=1 THEN 'Yes' 
		WHEN q6.[12890]=2 THEN 'No' 
	END
		as [Education24],
	CASE 
		WHEN q6.[12891]=1 THEN 'Yes' 
		WHEN q6.[12891]=2 THEN 'No' 
	END
		as [Education25],
	CASE 
		WHEN q6.[12892]=1 THEN 'Yes' 
		WHEN q6.[12892]=2 THEN 'No' 
	END
		as [Education26],
	CASE 
		WHEN q6.[12893]=1 THEN 'Yes' 
		WHEN q6.[12893]=2 THEN 'No' 
	END
		as [Education27],
	CASE 
		WHEN q6.[12894]=1 THEN 'Yes' 
		WHEN q6.[12894]=2 THEN 'No' 
	END
		as [Education28],
	CASE 
		WHEN q6.[12895]=1 THEN 'Yes' 
		WHEN q6.[12895]=2 THEN 'No' 
	END
		as [Education29],
	q7.optionText as [Recertifying]
INTO ##tmpSWCreditAll
FROM enrollments as en
INNER JOIN tblenrollments as e on e.enrollmentID = en.enrollmentID 
	AND  e.enrollmentID = @enrollmentID 
	AND  e.seminarID in (3299, 5735)
INNER JOIN tblParticipants p on e.participantID = p.participantID and p.orgcode = 'THT'
INNER JOIN tblSeminars as sem on sem.seminarID = e.seminarID
LEFT OUTER JOIN tblSeminarsSWOD as sswod on sswod.seminarID = sem.seminarID
INNER JOIN tblUsers as u on u.userid = e.userid
INNER JOIN tblenrollmentsandcredit as eac on eac.enrollmentid = e.enrollmentid
INNER JOIN trialsmith.dbo.depomemberdata as d on d.depomemberdataid = u.depomemberdataid
INNER JOIN trialsmith.dbo.depotransactions as t on t.depomemberdataID = d.depomemberdataID

INNER JOIN membercentral.membercentral.dbo.ams_networkProfiles np ON d.depomemberdataID = np.depomemberdataID
INNER JOIN membercentral.membercentral.dbo.ams_memberNetworkProfiles mnp ON np.profileID = mnp.profileID
INNER JOIN membercentral.membercentral.dbo.ams_members m ON m.memberID = mnp.memberID
INNER JOIN membercentral.membercentral.dbo.ams_memberAddresses ma ON m.memberID = ma.memberID
INNER JOIN membercentral.membercentral.dbo.vw_memberData_THT as md on md.memberID = m.memberID

INNER JOIN tblSeminarsAndCredit as sac on eac.seminarCreditID = sac.seminarCreditID
INNER JOIN tblCreditSponsorsAndAuthorities as csa on csa.csalinkid = sac.csalinkid
INNER JOIN tblCreditSponsors as cs on cs.sponsorid = csa.sponsorid and cs.sponsorID = 67
LEFT OUTER JOIN trialsmith.dbo.orgmemberdata as o on o.depomemberdataid = d.depomemberdataid 
	AND d.tlamemberstate = 'THT'
	AND t.accountcode BETWEEN '7000' AND '7005'
	AND t.purchasedItemTableName in ('SeminarWebOnDemand')

LEFT OUTER JOIN formresponses as q1 on q1.enrollmentid = e.enrollmentid and q1.questionID=3439
LEFT OUTER JOIN formresponses as q2 on q2.enrollmentid = e.enrollmentid and q2.questionID=3441
LEFT OUTER JOIN formresponses as q3 on q3.enrollmentid = e.enrollmentid and q3.questionID=3478
LEFT OUTER JOIN formresponses as q4 on q4.enrollmentid = e.enrollmentid and q4.questionID=3444
LEFT OUTER JOIN (
	select enrollmentid, [13333],[13334],[13335],[13336],[13337],[13338],[13339],[13340],[13341]
	from (
		select enrollmentID, optionID, matrixVal
		from formresponses
		where questionID=3563
	) tmp 
	PIVOT (max(matrixVal) for optionID in ([13333],[13334],[13335],[13336],[13337],[13338],[13339],[13340],[13341])) as pvt
) as q5 on q5.enrollmentid = e.enrollmentid
LEFT OUTER JOIN (
	select enrollmentid, [12867],[12868],[12869],[12870],[12871],[12872],[12873],[12874],[12875],[12876],[12877],[12878]
		,[12879],[12880],[12881],[12882],[12883],[12884],[12885],[12886],[12887],[12888],[12889],[12890],[12891],[12892]
		,[12893],[12894],[12895]
	from (
		select enrollmentID, optionID, matrixVal
		from formresponses
		where questionID=3452
	) tmp 
	PIVOT (max(matrixVal) for optionID in ([12867],[12868],[12869],[12870],[12871],[12872],[12873],[12874],[12875],[12876],[12877],[12878]
		,[12879],[12880],[12881],[12882],[12883],[12884],[12885],[12886],[12887],[12888],[12889],[12890],[12891],[12892]
		,[12893],[12894],[12895])) as pvt
) as q6 on q6.enrollmentid = e.enrollmentid
LEFT OUTER JOIN formresponses as q7 on q7.enrollmentid = e.enrollmentid and q7.questionID=7039
ORDER BY	e.datecompleted

SELECT Distinct * 
--INTO ##tmpSWCredit
FROM ##tmpSWCreditAll

-- drop temp table
IF OBJECT_ID('tempdb..##tmpSWCredit') IS NOT NULL 
	DROP TABLE ##tmpSWCredit

-- drop temp table
IF OBJECT_ID('tempdb..##tmpSWCreditAll') IS NOT NULL 
	DROP TABLE ##tmpSWCreditAll
GO
