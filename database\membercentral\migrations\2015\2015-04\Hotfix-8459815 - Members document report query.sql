declare @orgID int, @emailTypeID int

select @orgID = orgID from sites where sitecode='CO'
select @emailTypeID = emailTypeID from dbo.ams_memberEmailTypes where orgid = @orgID and emailTypeOrder = 1


SELECT m2.firstname, m2.lastname, me.email, m2.memberNumber, m2.company, 
	ma.address1 + case when ma.address2 <> '' then ', ' + ma.address2 end + ', ' + ma.city + ', ' + s.code + '  ' + ma.postalCode as Firm<PERSON>dd<PERSON>,
	dl.docTitle, dl.docDesc, 
	'https://www.ctlanet.org/index.cfm?pg=admin&mca_s=3&mca_a=19&mca_tt=10&mca_ta=edit&memberID=' + convert(varchar(20),  m2.activememberid) + '&tab=documents' as docLink
FROM dbo.ams_memberDocuments as md
INNER JOIN dbo.cms_documents as d ON md.documentID = d.documentID
INNER JOIN dbo.cms_documentLanguages as dl ON d.documentID = dl.documentID
INNER JOIN dbo.cms_documentVersions as dv ON dl.documentLanguageID = dv.documentLanguageID and dv.isActive = 1
INNER JOIN dbo.cms_languages as l ON l.languageID = dl.languageID
INNER JOIN dbo.ams_members as m on m.memberid = md.memberID
INNER JOIN dbo.ams_members as m2 on m.activememberid = m2.activememberid 
	and m2.orgID = @orgID
	and m2.status = 'A'
left outer join dbo.ams_memberAddresses ma on ma.addressTypeID = m2.billingAddressTypeid and ma.memberid = m2.memberid
	left outer join dbo.ams_states s on s.stateid = ma.stateid
left outer join dbo.ams_memberEmails me on me.memberID = m2.memberid and me.emailTypeID = @emailTypeID 
ORDER BY m2.lastname, m2.firstname, docTitle