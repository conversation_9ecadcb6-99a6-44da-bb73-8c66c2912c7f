-- CA-20 Support for member history end date
use membercentral
GO

ALTER TABLE dbo.ams_memberHistory ADD
	userEndDate datetime NULL

-- <PERSON><PERSON><PERSON> will need to copy start date to end date for existing entries with a date. 
update dbo.ams_memberHistory set userEndDate = userDate

-- Add new condition keys
insert into dbo.ams_virtualGroupConditionKeys(conditionKey) values ('historyEndDateLower')
insert into dbo.ams_virtualGroupConditionKeys(conditionKey) values ('historyEndDateUpper')
	

ALTER PROCEDURE [dbo].[ams_addMemberHistory] 
@typeID int,
@memberID int,
@categoryID int, 
@subCategoryID int, 
@userDate datetime,
@userEndDate datetime,
@qty int,
@dollarAmt money,
@description varchar(max),
@linkMemberID int,
@enteredByMemberID int,
@historyID int OUTPUT

AS

set @historyID = 0

BEGIN TRY
	insert into dbo.ams_memberHistory (typeID, memberID, categoryID, subCategoryID, userDate, userEndDate, quantity, dollarAmt, description, dateEntered, enteredByMemberID, linkMemberID)
	values(@typeID, @memberID, @categoryID, @subCategoryID, @userDate,  @userEndDate, @qty, @dollarAmt, @description, getDate(), @enteredByMemberID, @linkMemberID)
		select @historyID = SCOPE_IDENTITY()

	-- queue member groups (@runSchedule=2 indicates delayed processing) 
	DECLARE @itemGroupUID uniqueidentifier, @orgID int, @conditionIDList varchar(max)
	select @orgID = s.orgID
		from dbo.cms_categories as c
		inner join dbo.cms_categoryTrees as ct on c.categoryTreeID = ct.categoryTreeID
		inner join dbo.sites as s on s.siteID = ct.siteID
		where c.categoryID = @categoryID
	SELECT @conditionIDList = COALESCE(@conditionIDList + ',', '') + cast(c.conditionID as varchar(10)) 
		from dbo.ams_virtualGroupConditions as c
		OUTER APPLY (
			SELECT cast(cv.conditionValue as int)
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyCategory'
			WHERE cv.conditionID = c.conditionID
		) as historyCategory(val)
		where c.orgID = @orgID
		and c.fieldCode = 'mh_entry'
		and historyCategory.val = @categoryID
		group by c.conditionID
	IF @conditionIDList is not null
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberID, @conditionIDList=@conditionIDList, @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

	RETURN 0
END TRY
BEGIN CATCH
	set @historyID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO


ALTER PROC [dbo].[ams_importMemberHistory_toHolding]
@siteid int,
@typeID int,
@csvfilename varchar(200),
@strTableColumnNames varchar(max),
@pathToExport varchar(100),
@importResult xml OUTPUT

AS

DECLARE @qry varchar(max), @sitecode varchar(10), @tmptbl varchar(17), @var_tmpCols varchar(20), @orgID int
declare @good bit, @dynSQL nvarchar(max), @prefix varchar(50), @flatfile varchar(160), @exportcmd varchar(400)
declare @tblMissingCols TABLE (colName varchar(255))
declare @tblErrors TABLE (rowid int IDENTITY(1,1), msg varchar(300), fatal bit)
declare @tblCounts TABLE (rowid int IDENTITY(1,1), countName varchar(50), countNum int)
declare @catTree_1 int, @catTree_2 int, @catTree_3 int

-- get category trees
select @catTree_1 = dbo.fn_getCategoryTreeIDForSiteResourceID(dbo.fn_getSiteResourceIDForResourceType('MemberHistoryAdmin',@siteID))
select @catTree_2 = dbo.fn_getCategoryTreeIDForSiteResourceID(dbo.fn_getSiteResourceIDForResourceType('RelationshipAdmin',@siteID))
select @catTree_3 = dbo.fn_getCategoryTreeIDForSiteResourceID(dbo.fn_getSiteResourceIDForResourceType('HistoryAdmin',@siteID))

-- get table name
select @sitecode=sitecode, @orgID=orgID from dbo.sites where siteID = @siteid
select @tmptbl = '##tmpMH' + @sitecode
select @var_tmpCols = '##tmpColsMH' + @sitecode

-- delete temp table if exists
IF OBJECT_ID('tempdb..' + @tmptbl) IS NOT NULL 
	EXEC('DROP TABLE ' + @tmptbl)

-- create temp table using columnlist
select @qry = COALESCE(@qry + ', ', '') + quotename(listitem) + ' varchar(max) '
FROM dbo.fn_varCharListToTable(@strTableColumnNames,char(7))
order by autoid
	EXEC('CREATE TABLE ' + @tmptbl + ' (' + @qry + ')')

-- rowID and membernumber is in a key column of an index and needs to not be varchar(max)
declare @trash bit
set @trash = 1
EXEC('ALTER TABLE ' + @tmptbl + ' ALTER COLUMN rowID int') 
BEGIN TRY
	EXEC('ALTER TABLE ' + @tmptbl + ' ALTER COLUMN membernumber varchar(800)') 
END TRY
BEGIN CATCH
	set @trash = 0
END CATCH

-- Execute a bulk insert into previously defined temporary table
SELECT @qry = 'BULK INSERT ' + @tmptbl + ' FROM ''' + @csvfilename + ''' WITH (FIELDTERMINATOR = '''+ char(7) + ''', FIRSTROW = 2);'
EXEC(@qry)

IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
	EXEC('DROP TABLE ' + @var_tmpCols)
IF OBJECT_ID('tempdb..#tblOrgCols') IS NOT NULL 
	DROP TABLE #tblOrgCols
IF OBJECT_ID('tempdb..#tblImportCols') IS NOT NULL 
	DROP TABLE #tblImportCols

-- ******************************** 
-- ensure all columns exist (fatal)
-- ******************************** 
-- this will get the columns that should be there
CREATE TABLE #tblOrgCols (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
	COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
	SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
	COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
	CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)

-- Need the temp table code for the export. 
IF @typeID = 1
	select @dynSQL = 'select top 1 m.MemberNumber, cType.categoryName as Category, cType.categoryName as Subcategory, 
						mh.userDate as [StartDate], mh.userEndDate as [EndDate], mh.Description, mh.Quantity, mh.dollarAmt as Amount, 
						m.MemberNumber as LinkedMemberNumber
						into ' + @var_tmpCols + '
						from dbo.ams_members as m
						inner join dbo.ams_memberHistory as mh on mh.memberID = m.memberID
						inner join dbo.cms_categories as cType on cType.categoryID = mh.categoryID
						where m.orgID = 0'
IF @typeID = 2
	select @dynSQL = 'select top 1 m.MemberNumber, cType.categoryName as Relationship, 
						mh.userDate as [StartDate], mh.userEndDate as [EndDate], mh.Description, m.MemberNumber as LinkedMemberNumber
						into ' + @var_tmpCols + '
						from dbo.ams_members as m
						inner join dbo.ams_memberHistory as mh on mh.memberID = m.memberID
						inner join dbo.cms_categories as cType on cType.categoryID = mh.categoryID
						where m.orgID = 0'
IF @typeID = 3
	select @dynSQL = 'select top 1 m.MemberNumber, cType.categoryName as Category, cType.categoryName as Subcategory, 
						mh.userDate as [StartDate], mh.userEndDate as [EndDate], mh.Description, m.MemberNumber as LinkedMemberNumber
						into ' + @var_tmpCols + '
						from dbo.ams_members as m
						inner join dbo.ams_memberHistory as mh on mh.memberID = m.memberID
						inner join dbo.cms_categories as cType on cType.categoryID = mh.categoryID
						where m.orgID = 0'
EXEC(@dynSQL)

-- get cols
INSERT INTO #tblOrgCols
EXEC tempdb.dbo.SP_COLUMNS @var_tmpCols
	
-- cleanup table no longer needed
IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
	EXEC('DROP TABLE ' + @var_tmpCols)

-- this will get the columns that are actually in the import
CREATE TABLE #tblImportCols (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
	COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
	SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
	COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
	CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)

	-- get cols
	INSERT INTO #tblImportCols
	EXEC tempdb.dbo.SP_COLUMNS @tmptbl

INSERT INTO @tblErrors (msg, fatal)
select 'The column ' + org.column_name + ' is missing from your data.', 1
from #tblOrgCols as org
left outer join #tblImportCols as imp on imp.column_name = org.column_name
where imp.table_name is null

insert into @tblMissingCols(colname)
select org.column_name
from #tblOrgCols as org
left outer join #tblImportCols as imp on imp.column_name = org.column_name
where imp.table_name is null

-- dont drop these temp tables here because we use them for skipped rows below

-- ********************************
-- data type checks (fatal)
-- ********************************
IF NOT EXISTS (select colName from @tblMissingCols where colName = 'StartDate') BEGIN
	EXEC('UPDATE ' + @tmptbl + ' SET [StartDate] = null where len([StartDate]) = 0')

	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [StartDate] datetime null
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column StartDate contains invalid dates.', 1)
END

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'EndDate') BEGIN
	EXEC('UPDATE ' + @tmptbl + ' SET [EndDate] = null where len([EndDate]) = 0')

	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [EndDate] datetime null
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column EndDate contains invalid dates.', 1)
END
				
IF @typeID = 1 and NOT EXISTS (select colName from @tblMissingCols where colName = 'quantity') BEGIN
	EXEC('UPDATE ' + @tmptbl + ' SET quantity = null where len(quantity) = 0')

	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN [quantity] int null
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column Quantity contains invalid whole number values.', 1)
END
				
IF @typeID = 1 and NOT EXISTS (select colName from @tblMissingCols where colName = 'Amount') BEGIN
	EXEC('UPDATE ' + @tmptbl + ' SET Amount = null where len(Amount) = 0')

	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			ALTER TABLE ' + @tmptbl + ' ALTER COLUMN Amount money null
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column Amount contains invalid dollar values.', 1)
END

-- ********************************
-- change Relationship to Category for easier querying below
-- ********************************
IF @typeID = 2 and NOT EXISTS (select colName from @tblMissingCols where colName = 'Relationship') BEGIN
	set @good = 1
	set @dynSQL = '
		set @good = 1
		BEGIN TRY
			EXEC tempdb..sp_rename ''' + @tmptbl + '.Relationship'', ''Category'', ''COLUMN''
		END TRY
		BEGIN CATCH
			set @good = 0
		END CATCH'
		exec sp_executesql @dynSQL, N'@good bit output', @good output
	IF @good = 0
		INSERT INTO @tblErrors (msg, fatal)
		VALUES ('The column Relationship could not be processed.', 1)
END

-- ********************************
-- no/bad member number (fatal)
-- ********************************
IF NOT EXISTS (select colName from @tblMissingCols where colName = 'memberNumber') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' is missing a Member Number. Member Numbers are required for all entries.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE (memberNumber IS NULL OR ltrim(rtrim(memberNumber)) = '''')
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' Member Number ('' + tb.membernumber + '') was not found. Member Numbers must match existing members.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' as tb
		left outer join dbo.ams_members as m on m.memberNumber = tb.membernumber
			and m.orgID = ' + cast(@orgID as varchar(10)) + '
			and m.memberID = m.activeMemberID
		WHERE tb.membernumber IS NOT NULL 
		AND ltrim(rtrim(tb.membernumber)) <> ''''
		AND m.memberNumber is null
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

IF NOT EXISTS (select colName from @tblMissingCols where colName = 'linkedmembernumber') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' Linked Member Number ('' + tb.linkedmembernumber + '') was not found. Linked Member Numbers must match existing members.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' as tb
		left outer join dbo.ams_members as m on m.memberNumber = tb.linkedmembernumber
			and m.orgID = ' + cast(@orgID as varchar(10)) + '
			and m.memberID = m.activeMemberID
		WHERE tb.linkedmembernumber IS NOT NULL 
		AND ltrim(rtrim(tb.linkedmembernumber)) <> ''''
		AND m.memberNumber is null
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

-- ********************************
-- no/bad categories (fatal)
-- ********************************
IF NOT EXISTS (select colName from @tblMissingCols where colName = 'Category') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' is missing a ' + case when @typeID = 2 then 'Relationship Type' else 'Category' end + '. ' + case when @typeID = 2 then 'Relationship Types' else 'Categories' end + ' are required for all entries.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' 
		WHERE (Category IS NULL OR ltrim(rtrim(Category)) = '''')
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)

	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ' + case when @typeID = 2 then 'Relationship Type' else 'Category' end + ' ('' + tb.Category + '') was not found. Values must match existing ' + case when @typeID = 2 then 'Relationship Types' else 'Categories' end + '.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' as tb
		left outer join dbo.cms_categories as c on c.categoryName = tb.category
			and c.categoryTreeID = ' + 
			case 
			when @typeID = 1 then cast(@catTree_1 as varchar(10))
			when @typeID = 2 then cast(@catTree_2 as varchar(10))
			when @typeID = 3 then cast(@catTree_3 as varchar(10))
			end + '
			and c.isActive = 1
			and c.parentCategoryID is NULL
		WHERE tb.Category IS NOT NULL 
		AND ltrim(rtrim(tb.Category)) <> ''''
		AND c.categoryName is null
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

IF @typeID <> 2 and NOT EXISTS (select colName from @tblMissingCols where colName = 'subcategory') BEGIN
	select @dynSQL = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' Subcategory ('' + tb.subcategory + '') was not found. Values must match existing Subcategories.'' as msg, 1 as fatal 
		FROM ' + @tmptbl + ' as tb
		left outer join dbo.cms_categories as c 
			inner join dbo.cms_categories as cP on cp.categoryID = c.parentCategoryID
				and cP.categoryTreeID = ' + 
				case 
				when @typeID = 1 then cast(@catTree_1 as varchar(10))
				when @typeID = 3 then cast(@catTree_3 as varchar(10))
				end + '
				and cP.isActive = 1 
				and cP.parentCategoryID is NULL
			on c.categoryName = tb.Subcategory
			and cP.categoryName = tb.Category
			and c.isActive = 1 
			and c.parentCategoryID is not NULL
		WHERE tb.SubCategory IS NOT NULL 
		AND ltrim(rtrim(tb.SubCategory)) <> ''''
		AND c.categoryName is null
		ORDER BY rowID'
	INSERT INTO @tblErrors (msg, fatal)
	EXEC(@dynSQL)
END

-- cleanup - these are no longer needed
IF OBJECT_ID('tempdb..#tblOrgCols') IS NOT NULL
	DROP TABLE #tblOrgCols
IF OBJECT_ID('tempdb..#tblImportCols') IS NOT NULL
	DROP TABLE #tblImportCols

-- ******************************** 
-- dump flat table and format file and creation script
-- would love to use xml format files, but it appears column names with spaces cause it to fail
-- ******************************** 
SELECT @prefix = @sitecode + '_MH_flat_' + convert(varchar(8),getdate(),112) + replace(convert(varchar(8),getdate(),108),':','')
SELECT @flatfile = @pathToExport + @prefix

select @exportcmd = 'bcp ' + @tmptbl + ' format nul -f ' + @flatfile + '.txt -n -T -S' + CAST(serverproperty('servername') as varchar(40))
EXEC master..xp_cmdshell @exportcmd, NO_OUTPUT

select @exportcmd = 'bcp ' + @tmptbl + ' out ' + @flatfile + '.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
EXEC master..xp_cmdshell @exportcmd, NO_OUTPUT

declare @createTableSQLTop varchar(100)          
declare @coltypes table (datatype varchar(16))          
insert into @coltypes values('bit')          
insert into @coltypes values('binary')          
insert into @coltypes values('bigint')          
insert into @coltypes values('int')          
insert into @coltypes values('float')          
insert into @coltypes values('datetime')          
insert into @coltypes values('text')          
insert into @coltypes values('image')          
insert into @coltypes values('money')          
insert into @coltypes values('uniqueidentifier')          
insert into @coltypes values('smalldatetime')          
insert into @coltypes values('tinyint')          
insert into @coltypes values('smallint')          
insert into @coltypes values('sql_variant')          
select @dynSQL = ''
select @dynSQL = @dynSQL +           
	case when charindex('(',@dynSQL,1)<=0 then '(' else '' end + '[' + Column_Name + '] ' +Data_Type +
	case when Data_Type in (Select datatype from @coltypes) then '' else  '(' end+
	case when data_type in ('real','decimal','numeric')  then cast(isnull(numeric_precision,'') as varchar)+','+
	case when data_type in ('real','decimal','numeric') then cast(isnull(Numeric_Scale,'') as varchar) end
	when data_type in ('nvarchar','varchar') and cast(isnull(Character_Maximum_Length,'') as varchar) = '-1' then 'max'
	when data_type in ('char','nvarchar','varchar','nchar') then cast(isnull(Character_Maximum_Length,'') as varchar) else '' end+
	case when Data_Type in (Select datatype from @coltypes)then '' else  ')' end+
	case when Is_Nullable='No' then ' Not null,' else ' null,' end
	from tempdb.Information_Schema.COLUMNS where Table_Name=@tmptbl
	order by ordinal_position
select @createTableSQLTop = 'Create table ##xxx ' 
select @dynSQL=@createTableSQLTop + substring(@dynSQL,1,len(@dynSQL)-1) +' )'            
if dbo.fn_WriteFile(@pathToExport + @prefix + '.sql', @dynSQL, 1) = 0 BEGIN
	RETURN 0
END

-- create index on temp table for numbers below
EXEC('CREATE NONCLUSTERED INDEX [idx_' + @tmptbl + '_memnum_rowid] ON ' + @tmptbl + '([membernumber] ASC,[rowID] ASC)')

-- ******************************** 
-- Counts
-- ******************************** 
IF NOT EXISTS (select rowID from @tblErrors where fatal = 1) BEGIN
	-- TotalEntriesImported
	select @dynSQL = 'SELECT ''TotalEntriesImported'', COUNT(rowID) FROM ' + @tmptbl
	INSERT INTO @tblCounts (countName, countNum)
	EXEC(@dynSQL)
END
				
-- ********************************
-- generate result xml file 
-- ********************************
select @importResult = (
	select getdate() as "@date", @flatfile as "@flatfile",
		isnull((select top 301 dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg", "@severity" = case fatal when 1 then 'fatal' else 'nonfatal' end
		from @tblErrors
		order by rowid
		FOR XML path('error'), root('errors'), type),'<errors/>'),

		isnull((select countName as "@name", countNum as "@num"
		from @tblCounts
		order by rowid
		FOR XML path('count'), root('counts'), type),'<counts/>')
	for xml path('import'), TYPE)

-- drop temp tables 
IF OBJECT_ID('tempdb..' + @tmptbl) IS NOT NULL
	EXEC('DROP TABLE ' + @tmptbl)

RETURN 0
GO


ALTER PROC [dbo].[ams_importMemberHistory]
@siteID int,
@typeID int,
@flatfile varchar(160),
@enteredByMemberID int

AS

SET NOCOUNT ON

BEGIN TRY

	declare @orgID int, @catTreeID int, @createSQL varchar(max), @cmd varchar(400)
	select @orgID = orgID from dbo.sites where siteID = @siteID
	select @catTreeID = case 
		when @typeID = 1 then dbo.fn_getCategoryTreeIDForSiteResourceID(dbo.fn_getSiteResourceIDForResourceType('MemberHistoryAdmin',@siteID))
		when @typeID = 2 then dbo.fn_getCategoryTreeIDForSiteResourceID(dbo.fn_getSiteResourceIDForResourceType('RelationshipAdmin',@siteID))
		when @typeID = 3 then dbo.fn_getCategoryTreeIDForSiteResourceID(dbo.fn_getSiteResourceIDForResourceType('HistoryAdmin',@siteID))
		end

	-- ensure files exist
	if dbo.fn_fileExists(@flatfile + '.sql') = 0 or dbo.fn_fileExists(@flatfile + '.bcp') = 0
		RAISERROR('Import files missing', 16, 1);

	-- **************
	-- read in sql create script and create global temp table
	-- **************
	select @createSQL = replace(dbo.fn_ReadFile(@flatfile + '.sql',0,1),'##xxx','##importMemberHistoryData')
	IF OBJECT_ID('tempdb..##importMemberHistoryData') IS NOT NULL
		EXEC('DROP TABLE ##importMemberHistoryData')
	EXEC(@createSQL)

	-- *******************
	-- bcp in data
	-- *******************
	select @cmd = 'bcp ##importMemberHistoryData in ' + @flatfile + '.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
	exec master..xp_cmdshell @cmd, NO_OUTPUT

	-- *******************
	-- immediately put into local temp table and drop global temp
	-- *******************
	select * into #importMemberHistoryData from ##importMemberHistoryData
	IF @typeID = 2
		ALTER TABLE #importMemberHistoryData ADD subCategory varchar(200) NULL, quantity int NULL, amount money NULL;
	IF @typeID = 3
		ALTER TABLE #importMemberHistoryData ADD quantity int NULL, amount money NULL;
	IF OBJECT_ID('tempdb..##importMemberHistoryData') IS NOT NULL
		EXEC('DROP TABLE ##importMemberHistoryData')

	-- Add index for queries below
	CREATE NONCLUSTERED INDEX [idx_importMemberHistoryData] ON #importMemberHistoryData ([membernumber] ASC);

	-- *******************
	-- process data
	-- *******************
	insert into dbo.ams_memberHistory (typeID, memberID, categoryID, subCategoryID, userDate, userEndDate, quantity, dollarAmt, description, dateEntered, enteredByMemberID, linkMemberID)
	select @typeID, m.memberID, cP.categoryID, c.categoryID, tb12.startDate, tb12.endDate, nullif(tb12.quantity,0), tb12.Amount, tb12.description, getdate(), @enteredByMemberID, mLink.memberID
	from #importMemberHistoryData as tb12 
	inner join dbo.ams_members as m on m.memberNumber = tb12.membernumber and m.orgID = @orgID and m.memberID = m.activeMemberID
	inner join dbo.cms_categories as cP on cP.categoryName = tb12.category and cP.categoryTreeID = @catTreeID and cP.isActive = 1 and cP.parentCategoryID is NULL
	left outer join dbo.cms_categories as c on cp.categoryID = c.parentCategoryID and c.categoryName = tb12.SubCategory and c.isActive = 1 and c.parentCategoryID is not NULL
	left outer join dbo.ams_members as mLink on mLink.memberNumber = tb12.linkedmembernumber and mLink.orgID = @orgID and mLink.memberID = mLink.activeMemberID

	-- add conditions to queue (@runSchedule=2 indicates delayed processing) 
	declare @itemGroupUID uniqueidentifier, @conditionIDList varchar(max)
	SELECT @conditionIDList = COALESCE(@conditionIDList + ',', '') + cast(vgc.conditionID as varchar(10)) 
		from dbo.ams_virtualGroupConditions as vgc
		where vgc.orgID = @orgID
		and vgc.fieldCode = 'mh_entry'
		group by vgc.conditionID
	IF @conditionIDList is not null 
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList='', @conditionIDList=@conditionIDList, @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT


	IF OBJECT_ID('tempdb..#importMemberHistoryData') IS NOT NULL
		EXEC('DROP TABLE #importMemberHistoryData')

	RETURN 0
END TRY
BEGIN CATCH
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO



USE [memberCentral]
GO
/****** Object:  StoredProcedure [dbo].[cache_members_populateMemberConditionCache_MH_EXISTS]    Script Date: 03/19/2015 16:18:02 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROC [dbo].[cache_members_populateMemberConditionCache_MH_EXISTS]
AS

IF OBJECT_ID('tempdb..#tblMHSubCategoriesSplit') IS NOT NULL
	DROP TABLE #tblMHSubCategoriesSplit
CREATE TABLE #tblMHSubCategoriesSplit (conditionID int, historyCategory int, historySubCategory int);

insert into #tblMHSubCategoriesSplit (conditionID,historyCategory,historySubCategory)
select tblc.conditionID, cast(historyCategory.val as int), cast(historySubCategory.val as int)
from #tblCondALL as tblc
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyCategory'
	WHERE cv.conditionID = tblc.conditionID
) as historyCategory(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historySubCategory'
	WHERE cv.conditionID = tblc.conditionID
) as historySubCategory(val)
where subProc = 'MH_EXISTS'
and nullif(historySubCategory.val,'') is not null

CREATE INDEX IX_tblMHSubCategoriesSplit_conditionID ON #tblMHSubCategoriesSplit (conditionID asc);
CREATE INDEX IX_tblMHSubCategoriesSplit_historyCategory ON #tblMHSubCategoriesSplit (historyCategory asc);
CREATE INDEX IX_tblMHSubCategoriesSplit_historySubCategory ON #tblMHSubCategoriesSplit (historySubCategory asc);


IF OBJECT_ID('tempdb..#tblMHSplit') IS NOT NULL
	DROP TABLE #tblMHSplit
CREATE TABLE #tblMHSplit (conditionID int, historyCategory int, historyDateLower datetime, historyDateUpper datetime, 
	historyEndDateLower datetime, historyEndDateUpper datetime,
	historyEnteredDateLower datetime, historyEnteredDateUpper datetime, historyQuantityLower int, historyQuantityUpper int, 
	historyAmountLower money, historyAmountUpper money, historyDescriptionContains varchar(max));

insert into #tblMHSplit (conditionID ,historyCategory ,historyDateLower ,historyDateUpper, historyEndDateLower ,historyEndDateUpper ,historyEnteredDateLower ,historyEnteredDateUpper ,historyQuantityLower ,historyQuantityUpper ,historyAmountLower ,historyAmountUpper ,historyDescriptionContains)
select tblc.conditionID, historyCategory.val,
	cast(nullif(historyDateLower.val,'') as datetime),
	cast(nullif(historyDateUpper.val,'') as datetime),
	cast(nullif(historyEndDateLower.val,'') as datetime),
	cast(nullif(historyEndDateUpper.val,'') as datetime),
	cast(nullif(historyEnteredDateLower.val,'') as datetime),
	cast(nullif(historyEnteredDateUpper.val,'') as datetime),
	cast(nullif(historyQuantityLower.val,'') as int),
	cast(nullif(historyQuantityUpper.val,'') as int),
	cast(nullif(historyAmountLower.val,'') as money),
	cast(nullif(historyAmountUpper.val,'') as money),
	nullif(historyDescriptionContains.val,'')
from #tblCondALL as tblc
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyCategory'
	WHERE cv.conditionID = tblc.conditionID
) as historyCategory(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyDateLower'
	WHERE cv.conditionID = tblc.conditionID
) as historyDateLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyDateUpper'
	WHERE cv.conditionID = tblc.conditionID
) as historyDateUpper(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyEndDateLower'
	WHERE cv.conditionID = tblc.conditionID
) as historyEndDateLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyEndDateUpper'
	WHERE cv.conditionID = tblc.conditionID
) as historyEndDateUpper(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyEnteredDateLower'
	WHERE cv.conditionID = tblc.conditionID
) as historyEnteredDateLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyEnteredDateUpper'
	WHERE cv.conditionID = tblc.conditionID
) as historyEnteredDateUpper(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyQuantityLower'
	WHERE cv.conditionID = tblc.conditionID
) as historyQuantityLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyQuantityUpper'
	WHERE cv.conditionID = tblc.conditionID
) as historyQuantityUpper(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyAmountLower'
	WHERE cv.conditionID = tblc.conditionID
) as historyAmountLower(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyAmountUpper'
	WHERE cv.conditionID = tblc.conditionID
) as historyAmountUpper(val)
OUTER APPLY (
	SELECT cv.conditionValue
	FROM dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyDescriptionContains'
	WHERE cv.conditionID = tblc.conditionID
) as historyDescriptionContains(val)
where subProc = 'MH_EXISTS'

CREATE INDEX IX_tblMHSplit_conditionID ON #tblMHSplit (conditionID asc);
CREATE INDEX IX_tblMHSplit_historyCategory ON #tblMHSplit (historyCategory asc);


-- when no subcategories defined	
insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMHSplit as mhsplit on mhsplit.conditionID = tblc.conditionID
inner join dbo.ams_memberHistory as mh on mh.categoryID = mhsplit.historyCategory
	and mh.typeID = 1
	and mh.dateEntered >= isnull(mhsplit.historyEnteredDateLower,mh.dateEntered)
	and mh.dateEntered <= isnull(mhsplit.historyEnteredDateUpper,mh.dateEntered)
	and (
		(mh.userDate is null and mhsplit.historyDateLower is null and mhsplit.historyDateUpper is null)
		or 
		(mh.userDate >= isnull(mhsplit.historyDateLower,mh.userDate) and mh.userDate <= isnull(mhsplit.historyDateUpper,mh.userDate))
	)
	and (
		(mh.userEndDate is null and mhsplit.historyEndDateLower is null and mhsplit.historyEndDateUpper is null)
		or 
		(mh.userEndDate >= isnull(mhsplit.historyEndDateLower,mh.userEndDate) and mh.userEndDate <= isnull(mhsplit.historyEndDateUpper,mh.userEndDate))
	)
	and (
		(mh.quantity is null and mhsplit.historyQuantityLower is null and mhsplit.historyQuantityUpper is null)
		or 
		(mh.quantity >= isnull(mhsplit.historyQuantityLower,mh.quantity) and mh.quantity <= isnull(mhsplit.historyQuantityUpper,mh.quantity))
	)
	and (
		(mh.dollarAmt is null and mhsplit.historyAmountLower is null and mhsplit.historyAmountUpper is null)
		or 
		(mh.dollarAmt >= isnull(mhsplit.historyAmountLower,mh.dollarAmt) and mh.dollarAmt <= isnull(mhsplit.historyAmountUpper,mh.dollarAmt))
	)
	and (
		(mh.description is null and mhsplit.historyDescriptionContains is null)
		or 
		(mh.description like '%' + isnull(mhsplit.historyDescriptionContains,mh.description) + '%')
	)
inner join dbo.cms_categories as c on c.categoryID = mh.categoryID
inner join dbo.cms_categoryTrees as ct on ct.categoryTreeID = c.categoryTreeID
inner join dbo.sites as s on s.siteID = ct.siteID and s.orgID = tblc.orgID
inner join dbo.ams_members as m2 on m2.memberID = mh.memberID
inner join #tblMembers as m on m.memberid = m2.activeMemberID
left outer join #tblMHSubCategoriesSplit as mhsubcats on mhsubcats.historyCategory = mhsplit.historyCategory
	and mhsubcats.conditionID = mhsplit.conditionID
where tblc.subProc = 'MH_EXISTS'
and mhsubcats.conditionID is null

-- when subcategories defined	
insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID	
from #tblCondALL as tblc
inner join #tblMHSplit as mhsplit on mhsplit.conditionID = tblc.conditionID
inner join #tblMHSubCategoriesSplit as mhsubcats on mhsubcats.historyCategory = mhsplit.historyCategory
	and mhsubcats.conditionID = mhsplit.conditionID
inner join dbo.ams_memberHistory as mh on mh.categoryID = mhsplit.historyCategory
	and mh.subcategoryID = mhsubcats.historySubCategory
	and mh.typeID = 1
	and mh.dateEntered >= isnull(mhsplit.historyEnteredDateLower,mh.dateEntered)
	and mh.dateEntered <= isnull(mhsplit.historyEnteredDateUpper,mh.dateEntered)
	and (
		(mh.userDate is null and mhsplit.historyDateLower is null and mhsplit.historyDateUpper is null)
		or 
		(mh.userDate >= isnull(mhsplit.historyDateLower,mh.userDate) and mh.userDate <= isnull(mhsplit.historyDateUpper,mh.userDate))
	)
	and (
		(mh.quantity is null and mhsplit.historyQuantityLower is null and mhsplit.historyQuantityUpper is null)
		or 
		(mh.quantity >= isnull(mhsplit.historyQuantityLower,mh.quantity) and mh.quantity <= isnull(mhsplit.historyQuantityUpper,mh.quantity))
	)
	and (
		(mh.dollarAmt is null and mhsplit.historyAmountLower is null and mhsplit.historyAmountUpper is null)
		or 
		(mh.dollarAmt >= isnull(mhsplit.historyAmountLower,mh.dollarAmt) and mh.dollarAmt <= isnull(mhsplit.historyAmountUpper,mh.dollarAmt))
	)
	and (
		(mh.description is null and mhsplit.historyDescriptionContains is null)
		or 
		(mh.description like '%' + isnull(mhsplit.historyDescriptionContains,mh.description) + '%')
	)
inner join dbo.cms_categories as c on c.categoryID = mh.categoryID
inner join dbo.cms_categoryTrees as ct on ct.categoryTreeID = c.categoryTreeID
inner join dbo.sites as s on s.siteID = ct.siteID and s.orgID = tblc.orgID
inner join dbo.ams_members as m2 on m2.memberID = mh.memberID
inner join #tblMembers as m on m.memberid = m2.activeMemberID
where tblc.subProc = 'MH_EXISTS'

IF OBJECT_ID('tempdb..#tblMHSubCategoriesSplit') IS NOT NULL
	DROP TABLE #tblMHSubCategoriesSplit
IF OBJECT_ID('tempdb..#tblMHSplit') IS NOT NULL
	DROP TABLE #tblMHSplit

GO