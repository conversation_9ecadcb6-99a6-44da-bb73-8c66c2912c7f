declare @resourceTypeID int, @resourceTypeName varchar(50), @functionID int, @functionName varchar(50), @functionDisplayName varchar(50), @addToSuperUserRole bit, @addToSiteAdminRole bit

DECLARE @rc int, @siteAdminRoleID int, @superAdminRoleID int, @ResourceTypeFunctionID int

set @addToSuperUserRole = 0
set @addToSiteAdminRole = 0
set @resourceTypeName = 'Site'
set @functionName = 'updateDistrictingInformation'
set @functionDisplayName = 'Update Districting Information'


--- Do not change below this line

select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)
select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')
select @siteAdminRoleID = dbo.fn_getResourceRoleID('Site Administrator')



BEGIN TRAN


    EXEC @rc = cms_createSiteResourceFunction
        @resourceTypeID=@resourceTypeID,
        @functionName=@functionName,
        @displayName=@functionDisplayName,
        @functionID=@functionID OUTPUT
        IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

    select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);
                IF @@ERROR <> 0 GOTO on_error


    IF (@addToSuperUserRole = 1)
        exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@superAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;
            IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

    IF (@addToSiteAdminRole = 1)
        exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@siteAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;
            IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error



IF @@TRANCOUNT > 0 COMMIT TRAN


-- error exit
on_error:
    IF @@TRANCOUNT > 1 COMMIT TRAN
    ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
