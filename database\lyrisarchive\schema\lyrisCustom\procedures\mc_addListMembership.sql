ALTER PROC dbo.mc_addListMembership
@siteID int,
@listName varchar(60),
@emailAddr varchar(100),
@userName varchar(100),
@domain varchar(250),
@fullName varchar(100),
@memberType varchar(20),
@subType varchar(20),
@externalMemberID varchar(100),
@lockAddress bit,
@lockName bit,
@keepActive bit,
@receiverMemberID int,
@recordedByMemberID int,
@memberID_ int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @mcEmailKey varchar(75) = convert(varchar(75),HASHBYTES('SHA2_256',@listName + '|' + @userName + '@' + @domain),2)
	SET @memberID_ = NULL;

	
	INSERT INTO trialslyris1.dbo.members_ (list_,emailaddr_, fullname_, DateJoined_, membertype_, subtype_, usernameLc_, domain_,
		externalMemberID, MCOption_lockAddress, MC<PERSON>ption_lockName, MCOption_keepActive, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>_usernameLC, MCEmail<PERSON>ey_domain)
	VALUES (@listName, @emailAddr, @fullName, getdate(), @memberType, @subType, @userName, @domain,
		@externalMemberID, @lockAddress, @lockName, @keepActive, @mcEmailKey, @userName, @domain);

	SELECT @memberID_ = SCOPE_IDENTITY();

	INSERT INTO memberCentral.platformQueue.dbo.queue_mongo (msgjson)
	SELECT '{ "c":"historyEntries_SYS_ADMIN_LISTUPDATE", "d": { "HISTORYCODE":"SYS_ADMIN_LISTUPDATE", "SITEID":' + cast(@siteID as varchar(10)) + 
		', "ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + 
		', "RECEIVERMEMBERID":' + cast(@receiverMemberID as varchar(10)) + 
		', "MAINMESSAGE":"List Membership Added", "LISTNAME":"'+ @listName +'"'+
		', "MESSAGES":[ "Email ['+ @emailAddr +'] has been added to the list." ]'+
		', "UPDATEDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '"'+
		' } }';

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
