<cfcomponent output="false">

	<cffunction name="getSponsorsSelector" access="public" output="false" returntype="struct">
		<cfargument name="resourceType" type="string" required="true">
		<cfargument name="referenceType" type="string" required="true">
		<cfargument name="referenceID" type="numeric" required="true">
		<cfargument name="selectorID" type="string" required="yes" hint="a unique id for this selector">
		<cfargument name="selectedSponsorsCardHeight" type="boolean" required="no" default="165" hint="override height of selected sponsor cards in pixels">
		<cfargument name="usageMode" type="string" required="no" default="sponsorSelectorWidget" hint="override this value to include specific logic at places">
		<cfargument name="readOnly" type="boolean" required="true">
		
		<cfset var local = structNew()>
		<cfset local.data = { "selectorID":arguments.selectorID, "html":'' }>
		
		<cfset local.baseURL = "/?pg=admin&mca_ajaxlib=sponsors&mca_ajaxfunc=">
		<cfset local.editSponsorLink = '#local.baseURL#editSponsor&_srt=#arguments.referenceType#&_srid=#arguments.referenceID#&widgetSelectorID=#arguments.selectorID#&mode=direct'>

		<!--- Sponsor Grouping URLs --->
		<cfset local.editSponsorGroupLink = '#local.baseURL#editSponsorGroupForm&selectorID=#arguments.selectorID#&mode=direct'>

		<cfsavecontent variable="local.data.html">
			<cfinclude template="dsp_sponsors.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getSponsors" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="referenceType" type="string" required="true">
		<cfargument name="referenceID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = { "arrincludedsponsors":[], "arravailablesponsors":[], "success":true }>
		
		<cfset local.qryIncludedSponsors = getSponsorsByReferenceID(siteID=arguments.mcproxy_siteID, referenceType=arguments.referenceType, referenceID=arguments.referenceID)>
		<cfif local.qryIncludedSponsors.recordCount>
			<cfquery name="local.data.arrincludedsponsors" dbtype="query" returntype="array">
				SELECT sponsorUsageID as sponsorusageid, sponsorID as sponsorid, sponsorName as sponsorname, sponsorOrder as sponsororder
				FROM local.qryIncludedSponsors
				ORDER BY sponsorOrder
			</cfquery>
		</cfif>

		<cfset local.qrySiteSponsors = getSponsorsBySiteID(siteID=arguments.mcproxy_siteID)>
		<cfif local.qrySiteSponsors.recordCount>
			<cfquery name="local.data.arravailablesponsors" dbtype="query" returntype="array">
				SELECT sponsorID as sponsorid, sponsorName as sponsorname
				FROM local.qrySiteSponsors
				<cfif local.qryIncludedSponsors.recordCount>
					WHERE sponsorID NOT IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#valueList(local.qryIncludedSponsors.sponsorID)#">)
				</cfif>
				ORDER BY sponsorName
			</cfquery>
		</cfif>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getSponsorsWithGroupings" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="referenceType" type="string" required="true">
		<cfargument name="referenceID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = { "arrincludedsponsors":[], "arravailablesponsors":[], "arrsponsorGroupings":[], "success":true }>

		<!--- Get sponsors with grouping information --->
		<cfset local.qryIncludedSponsors = getSponsorsByReferenceIDWithGroupings(siteID=arguments.mcproxy_siteID, referenceType=arguments.referenceType, referenceID=arguments.referenceID)>
		<cfif local.qryIncludedSponsors.recordCount>
			<cfquery name="local.data.arrincludedsponsors" dbtype="query" returntype="array">
				SELECT sponsorUsageID as sponsorusageid, sponsorID as sponsorid, sponsorName as sponsorname, sponsorOrder as sponsororder,
					   sponsorGroupingID as sponsorgroupingid, sponsorGrouping as sponsorgrouping, sponsorGroupingOrder as sponsorgroupingorder
				FROM local.qryIncludedSponsors
				ORDER BY ISNULL(sponsorGroupingOrder, 999), sponsorOrder
			</cfquery>
		</cfif>

		<!--- Get available sponsors (same as before) --->
		<cfset local.qrySiteSponsors = getSponsorsBySiteID(siteID=arguments.mcproxy_siteID)>
		<cfif local.qrySiteSponsors.recordCount>
			<cfquery name="local.data.arravailablesponsors" dbtype="query" returntype="array">
				SELECT sponsorID as sponsorid, sponsorName as sponsorname
				FROM local.qrySiteSponsors
				<cfif local.qryIncludedSponsors.recordCount>
					WHERE sponsorID NOT IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#valueList(local.qryIncludedSponsors.sponsorID)#">)
				</cfif>
				ORDER BY sponsorName
			</cfquery>
		</cfif>

		<!--- Get sponsor groupings using generalized method --->
		<cfset local.qryGroupings = getSponsorGroupings(referenceType=arguments.referenceType, referenceID=arguments.referenceID)>
		<cfif local.qryGroupings.recordCount>
			<cfquery name="local.data.arrsponsorGroupings" dbtype="query" returntype="array">
				SELECT sponsorGroupingID as sponsorgroupingid, sponsorGrouping as sponsorgrouping, sponsorGroupingOrder as sponsorgroupingorder
				FROM local.qryGroupings
				ORDER BY sponsorGroupingOrder
			</cfquery>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getSponsorsByReferenceIDWithGroupings" access="private" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="referenceType" type="string" required="true">
		<cfargument name="referenceID" type="numeric" required="true">

		<cfset var qrySponsors = "">

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sponsors_getSponsorsByReferenceIDFull">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.referenceType#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.referenceID#">
			<cfprocresult name="qrySponsors">
		</cfstoredproc>

		<cfreturn qrySponsors>
	</cffunction>

	<cffunction name="associateSponsor" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="sponsorID" type="numeric" required="true">
		<cfargument name="referenceType" type="string" required="true">
		<cfargument name="referenceID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sponsors_associateSponsor">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.sponsorID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.referenceType#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.referenceID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="deassociateSponsor" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="sponsorUsageID" type="numeric" required="true">
		<cfargument name="referenceType" type="string" required="true">
		<cfargument name="referenceID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sponsors_deassociateSponsor">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.sponsorUsageID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.referenceType#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.referenceID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="moveSponsor" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="sponsorUsageID" type="numeric" required="true">
		<cfargument name="referenceType" type="string" required="true">
		<cfargument name="referenceID" type="numeric" required="true">
		<cfargument name="dir" type="string" required="true">
		
		<cfset var local = structNew()>
		
		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sponsors_moveSponsor">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.sponsorUsageID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.referenceType#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.referenceID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dir#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getSponsorsBySiteID" access="private" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qrySponsors = "">

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sponsors_getSponsorsBySiteID">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocresult name="qrySponsors">
		</cfstoredproc>

		<cfreturn qrySponsors>
	</cffunction>

	<cffunction name="getSponsorsByReferenceID" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="referenceType" type="string" required="true">
		<cfargument name="referenceID" type="numeric" required="true">
		<cfargument name="includeGroupings" type="boolean" required="false" default="false">

		<cfset var qrySponsors = "">

		<!--- Use enhanced stored procedure if groupings are requested, otherwise use standard procedure --->
		<cfif arguments.includeGroupings>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sponsors_getSponsorsByReferenceIDFull">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.referenceType#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.referenceID#">
				<cfprocresult name="qrySponsors">
			</cfstoredproc>
		<cfelse>
			<!--- Use standard stored procedure for backward compatibility --->
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sponsors_getSponsorsByReferenceID">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.referenceType#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.referenceID#">
				<cfprocresult name="qrySponsors">
			</cfstoredproc>
		</cfif>

		<cfreturn qrySponsors>
	</cffunction>

	<cffunction name="getSponsorBySponsorID" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="sponsorID" type="numeric" required="true">
		
		<cfset var qrySponsor = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qrySponsor">
			SELECT s.sponsorID, s.sponsorContentID, s.sponsorName, sponsorContent.rawContent as sponsorContent, s.sponsorURL
			FROM dbo.sponsors s
			CROSS APPLY dbo.fn_getContent(s.sponsorContentID,1) AS sponsorContent
			WHERE s.sponsorID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.sponsorID#">
			AND s.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
		</cfquery>

		<cfreturn qrySponsor>
	</cffunction>

	<cffunction name="editSponsor" access="public" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="orgCode" type="string" required="true">
		<cfargument name="sponsorID" type="numeric" required="true">
		<cfargument name="referenceType" type="string" required="true">
		<cfargument name="referenceID" type="numeric" required="true">
		<cfargument name="widgetSelectorID" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.qrySponsor = getSponsorBySponsorID(siteID=arguments.siteID, sponsorID=arguments.sponsorID)>
		<cfset local.qryGroupings = queryNew("")>
		<cfset local.currentGroupingID = 0>

		<!--- Load sponsor groupings using generalized approach --->
		<cfif local.qrySponsor.recordCount>
			<cfset local.qryGroupings = getSponsorGroupings(referenceType=arguments.referenceType, referenceID=arguments.referenceID)>
			<!--- Get current group assignment --->
			<cfset local.qryCurrentAssignment = getSponsorGroupAssignment(sponsorID=arguments.sponsorID, referenceType=arguments.referenceType, referenceID=arguments.referenceID)>
			<cfif local.qryCurrentAssignment.recordCount>
				<cfset local.currentGroupingID = local.qryCurrentAssignment.sponsorGroupingID>
			</cfif>
		</cfif>

		<cfif local.qrySponsor.recordCount>
			<cfset local.objFeaturedImages = createObject("component","model.admin.common.modules.featuredImages.featuredImages")>
			<cfset local.featureImageConfigID = local.objFeaturedImages.getFeaturedImageConfigID(referenceID=arguments.siteID, referenceType="evSiteSponsor")>
			<cfif local.featureImageConfigID gt 0>
				<cfset local.arrConfigs = [ { "ftdExt":"#local.qrySponsor.sponsorID#_sponsor", "controllingReferenceID":arguments.siteID,
												"controllingReferenceType":"evSiteSponsor", "referenceID":local.qrySponsor.sponsorID, "referenceType":"EventSponsors",
												"resourceType":"Sponsors", "resourceTypeTitle":local.qrySponsor.sponsorName,
												"preEditHandler": "editSponsorImage", "onDeleteImageHandler":"",
												"onSaveImageHandler":"", "header":'<h6>Sponsor Image</h6>', "ftdImgClassList":"pl-3"
											} ]>
				<cfset local.strFeaturedImages = local.objFeaturedImages.manageFeaturedImages(orgCode=arguments.orgCode, siteCode=arguments.siteCode, arrConfigs=local.arrConfigs)>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_sponsor.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveSponsor" access="public" returntype="struct" output="no">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="sponsorID" type="numeric" required="true">
		<cfargument name="referenceType" type="string" required="true">
		<cfargument name="referenceID" type="numeric" required="true">
		<cfargument name="sponsorName" type="string" required="true">
		<cfargument name="sponsorContent" type="string" required="true">
		<cfargument name="sponsorURL" type="string" required="true">
		<cfargument name="sponsorGroupingID" type="numeric" required="false" default="0">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<cftry>
			<cfif arguments.sponsorID is 0>
				<cfset local.data.sponsorid = createSponsor(siteID=arguments.mcproxy_siteID, sponsorName=arguments.sponsorName, sponsorContent=arguments.sponsorContent,
					sponsorUrl=arguments.sponsorUrl, referenceType=arguments.referenceType, referenceID=arguments.referenceID)>
			<cfelse>
				<cfset updateSponsor(sponsorID=arguments.sponsorID, sponsorName=arguments.sponsorName, sponsorContent=arguments.sponsorContent, sponsorUrl=arguments.sponsorUrl)>

				<!--- Update group assignment for existing sponsors in all contexts --->
				<cfif structKeyExists(arguments, "sponsorGroupingID")>
					<cfset assignSponsorToGrouping(sponsorUsageID=getSponsorUsageID(sponsorID=arguments.sponsorID, referenceType=arguments.referenceType, referenceID=arguments.referenceID),
													sponsorGroupingID=arguments.sponsorGroupingID)>
				</cfif>
			</cfif>
			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getSponsorUsageID" access="private" output="false" returntype="numeric">
		<cfargument name="sponsorID" type="numeric" required="true">
		<cfargument name="referenceType" type="string" required="true">
		<cfargument name="referenceID" type="numeric" required="true">

		<cfset var qryUsage = "">

		<cfquery name="qryUsage" datasource="#application.dsn.membercentral.dsn#">
			SELECT sponsorUsageID
			FROM dbo.sponsorsUsage
			WHERE sponsorID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.sponsorID#">
			AND referenceType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.referenceType#">
			AND referenceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.referenceID#">
		</cfquery>

		<cfreturn qryUsage.recordCount ? qryUsage.sponsorUsageID : 0>
	</cffunction>

	<cffunction name="createSponsor" access="public" output="false" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="sponsorName" type="string" required="true">
		<cfargument name="sponsorContent" type="string" required="true">
		<cfargument name="sponsorURL" type="string" required="true">
		<cfargument name="referenceType" type="string" required="true">
		<cfargument name="referenceID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCreateSponsor">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @siteID INT, @sponsorName VARCHAR(200), @sponsorContent VARCHAR(MAX), @sponsorURL VARCHAR(200), 
				 @referenceType VARCHAR(20), @referenceID INT, @enteredByMemberID INT, @sponsorID INT;

				SET @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;
				SET @sponsorName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.sponsorName#">;
				SET @sponsorContent = <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.sponsorContent#">;
				SET @sponsorURL = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.sponsorURL#">;
				SET @referenceType = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.referenceType#">;
				SET @referenceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.referenceID#">;
				SET @enteredByMemberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcuser.memberdata.memberID#">;
				
				BEGIN TRAN;
					EXEC dbo.sponsors_createSponsor @siteID=@siteID, @sponsorName=@sponsorName, @sponsorContent=@sponsorContent, 
						@sponsorURL=@sponsorURL,@enteredByMemberID=@enteredByMemberID, @sponsorID=@sponsorID OUTPUT;

					EXEC dbo.sponsors_associateSponsor @siteID=@siteID, @sponsorID=@sponsorID, @referenceType=@referenceType, 
						@referenceID=@referenceID, @recordedByMemberID=@enteredByMemberID;
				COMMIT TRAN;

				SELECT @sponsorID as sponsorID;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
		
		<cfreturn local.qryCreateSponsor.sponsorID>
	</cffunction>

	<cffunction name="updateSponsor" access="public" output="false" returntype="void">
		<cfargument name="sponsorID" type="numeric" required="true">
		<cfargument name="sponsorName" type="string" required="true">
		<cfargument name="sponsorContent" type="string" required="true">
		<cfargument name="sponsorURL" type="string" required="true">

		<cfstoredproc procedure="sponsors_updateSponsor" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.sponsorID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.sponsorName#">
			<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.sponsorContent#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.sponsorURL#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
		</cfstoredproc>
	</cffunction>

	<!--- ========================================================================================================= --->
	<!--- SPONSOR GROUPING METHODS --->
	<!--- ========================================================================================================= --->

	<!---<cffunction name="getSponsorGroupingsByEventID" access="public" output="false" returntype="query">
		<cfargument name="eventID" type="numeric" required="true">

		<cfset var qryGroupings = "">

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sponsors_getSponsorGroupingsByEventID">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.eventID#">
			<cfprocresult name="qryGroupings">
		</cfstoredproc>

		<cfreturn qryGroupings>
	</cffunction>--->

	<cffunction name="assignSponsorToGrouping" access="public" output="false" returntype="struct">
		<cfargument name="sponsorUsageID" type="numeric" required="true">
		<cfargument name="sponsorGroupingID" type="numeric" required="false" default="0">

		<cfset var local = structNew()>
		<cfset local.data = { "success": false }>

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#">
				UPDATE dbo.sponsorsUsage
				SET sponsorGroupingID = <cfif arguments.sponsorGroupingID eq 0>NULL<cfelse><cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.sponsorGroupingID#"></cfif>
				WHERE sponsorUsageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.sponsorUsageID#">
			</cfquery>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="createSponsorGrouping" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="referenceType" type="string" required="true">
		<cfargument name="referenceID" type="numeric" required="true">
		<cfargument name="sponsorGrouping" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.data = { "success": false }>

		<cftry>
			<!--- Use generalized stored procedure for all contexts --->
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sponsors_createSponsorGrouping">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.referenceType#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.referenceID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.sponsorGrouping#">
			</cfstoredproc>
			<cfset local.data.success = true>
			<cfcatch>
				<cfset local.data.errmsg = cfcatch.message>
			</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="updateSponsorGrouping" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="sponsorGroupingID" type="numeric" required="true">
		<cfargument name="sponsorGrouping" type="string" required="true">
		<cfargument name="referenceType" type="string" required="true">
		<cfargument name="referenceID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = { "success": false }>

		<cftry>
			<!--- Use generalized stored procedure for all contexts --->
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sponsors_updateSponsorGrouping">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.referenceType#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.sponsorGroupingID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.sponsorGrouping#">
			</cfstoredproc>
			<cfset local.data.success = true>
			<cfcatch>
				<cfset local.data.errmsg = cfcatch.message>
			</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="moveSponsorGrouping" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="sponsorGroupingID" type="numeric" required="true">
		<cfargument name="direction" type="string" required="true">
		<cfargument name="referenceType" type="string" required="true">
		<cfargument name="referenceID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = { "success": false }>

		<cftry>
			<!--- Use generalized stored procedure for all contexts --->
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sponsors_moveSponsorGrouping">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.referenceType#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.sponsorGroupingID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.direction#">
			</cfstoredproc>
			<cfset local.data.success = true>
			<cfcatch>
				<cfset local.data.errmsg = cfcatch.message>
			</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="deleteSponsorGrouping" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="sponsorGroupingID" type="numeric" required="true">
		<cfargument name="referenceType" type="string" required="true">
		<cfargument name="referenceID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = { "success": false }>

		<cftry>
			<!--- Use generalized stored procedure for all contexts --->
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sponsors_deleteSponsorGrouping">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.referenceType#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.sponsorGroupingID#">
			</cfstoredproc>
			<cfset local.data.success = true>
			<cfcatch>
				<cfset local.data.errmsg = cfcatch.message>
			</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="moveSponsorWithinGroup" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="sponsorUsageID" type="numeric" required="true">
		<cfargument name="sponsorGroupingID" type="numeric" required="true">
		<cfargument name="direction" type="string" required="true">
		<cfargument name="referenceType" type="string" required="true">
		<cfargument name="referenceID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = { "success": false }>

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sponsors_moveSponsorWithinGroup">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.sponsorUsageID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.sponsorGroupingID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.direction#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.referenceType#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.referenceID#">
			</cfstoredproc>
			<cfset local.data.success = true>
			<cfcatch>
				<cfset local.data.errmsg = cfcatch.message>
			</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getSponsorGroupAssignment" access="private" output="false" returntype="query">
		<cfargument name="sponsorID" type="numeric" required="true">
		<cfargument name="referenceType" type="string" required="true">
		<cfargument name="referenceID" type="numeric" required="true">

		<cfset var qryAssignment = "">

		<cfquery name="qryAssignment" datasource="#application.dsn.membercentral.dsn#">
			SELECT sponsorGroupingID
			FROM dbo.sponsorsUsage
			WHERE sponsorID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.sponsorID#">
			AND referenceType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.referenceType#">
			AND referenceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.referenceID#">
		</cfquery>

		<cfreturn qryAssignment>
	</cffunction>

	<cffunction name="getSponsorGroupingByID" access="public" output="false" returntype="query">
		<cfargument name="sponsorGroupingID" type="numeric" required="true">

		<cfset var qryGrouping = "">

		<cfquery name="qryGrouping" datasource="#application.dsn.membercentral.dsn#">
			SELECT sponsorGroupingID, sponsorGrouping, sponsorGroupingOrder
			FROM dbo.sponsorGroupings
			WHERE sponsorGroupingID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.sponsorGroupingID#">
		</cfquery>

		<cfreturn qryGrouping>
	</cffunction>

	<cffunction name="editSponsorGroupForm" access="public" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="orgCode" type="string" required="true">
		<cfargument name="sponsorGroupingID" type="numeric" required="false" default="0">
		<cfargument name="referenceType" type="string" required="false" default="">
		<cfargument name="referenceID" type="numeric" required="false" default="0">
		<cfargument name="selectorID" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_editSponsorGroup.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getSponsorGroupings" access="public" output="false" returntype="query">
		<cfargument name="referenceType" type="string" required="true">
		<cfargument name="referenceID" type="numeric" required="true">

		<cfset var qryGroupings = "">

		<!--- Use generalized stored procedure for all contexts --->
		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sponsors_getSponsorGroupings">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.referenceType#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.referenceID#">
			<cfprocresult name="qryGroupings">
		</cfstoredproc>

		<cfreturn qryGroupings>
	</cffunction>

	<!---<cffunction name="getSponsorGroupingsBySeminarID" access="public" output="false" returntype="query">
		<cfargument name="seminarID" type="numeric" required="true">
		<cfargument name="participantID" type="numeric" required="false" default="0">

		<!--- Use generalized method for backward compatibility --->
		<cfreturn getSponsorGroupings(referenceType="swlProgram", referenceID=arguments.seminarID)>
	</cffunction>--->

</cfcomponent>