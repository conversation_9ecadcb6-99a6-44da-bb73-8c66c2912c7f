ALTER TABLE dbo.ams_memberDirectories ADD showPhotosDetail bit NOT NULL CONSTRAINT DF_ams_memberDirectories_showPhotosDetail DEFAULT 0
GO
update dbo.ams_memberDirectories
set showPhotosDetail = showPhotos
GO
ALTER PROCEDURE [dbo].[cms_createApplicationInstanceMemberDirectory] 
@siteid int,
@languageID int,
@sectionID int,
@isVisible bit,
@pageName varchar(50),
@pageTitle varchar(200),
@pagedesc varchar(400),
@zoneID int,
@pageTemplateID int,
@pageModeID int,
@pgResourceTypeID int,
@pgParentResourceID int = NULL,
@allowReturnAfterLogin bit,
@applicationInstanceName varchar(100),
@applicationInstanceDesc varchar(200),
@applicationInstanceID int OUTPUT,
@siteResourceID int OUTPUT,
@pageID int OUTPUT

AS

BEGIN TRAN

declare @rc int, @applicationTypeID int, @centerID int,
	@appCreatedContentResourceTypeID int, @activeSiteResourceStatusID int, @defaultLanguageID int,
	@searchContentID int, @searchContentSiteResourceID int
select @applicationInstanceID = null
select @siteResourceID = null
select @pageID = null
select @applicationTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'Members'
select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedContent')
select @activeSiteResourceStatusID = dbo.fn_getResourceStatusID('Active')
select @defaultLanguageID = defaultLanguageID from sites where siteID = @siteID

-- create instance
EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, 
		@applicationTypeID=@applicationTypeID, @isVisible=@isVisible, @pageName=@pageName, 
		@pageTitle=@pageTitle, @pageDesc=@pagedesc, @zoneID=@zoneID, @pagetemplateid=@pageTemplateID,
		@pageModeID=@pageModeID, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID = @pgParentResourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
		@applicationInstanceName=@applicationInstanceName, @applicationInstanceDesc=@applicationInstanceDesc, 
		@applicationInstanceID=@applicationInstanceID OUTPUT, 
		@siteresourceID=@siteResourceID OUTPUT, 
		@pageID=@pageID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- create searchcontentid
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@defaultLanguageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', @contentID=@searchContentID OUTPUT, 
	@siteResourceID=@searchContentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- insert into memberDirectory table
INSERT INTO dbo.ams_memberDirectories (applicationInstanceID, maxsearchresults, recordsperpage, showVCard, showPhotos, showPhotosDetail, inactivemembersVisible, searchContentID)
VALUES (@applicationInstanceID, 50, 15, 1, 1, 1, 0, @searchContentID)
	IF @@ERROR <> 0 GOTO on_error
	select @centerID = SCOPE_IDENTITY()

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

ALTER TABLE dbo.ams_memberDirectoryClassifications ADD
	showInSearchDetail bit NOT NULL CONSTRAINT DF_ams_memberDirectoryClassifications_showInSearchDetail DEFAULT 1
GO
update ams_memberDirectoryClassifications
set showInSearchDetail = showInSearchResults
GO

