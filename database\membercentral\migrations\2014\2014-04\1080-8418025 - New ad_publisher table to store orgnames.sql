USE [memberCentral]
GO
CREATE TABLE [dbo].[ad_publishers](
	[publisherID] [int] IDENTITY(1,1) NOT NULL,
	[sitecode] [varchar](5) NOT NULL,
	[publisherName] [varchar](200) NOT NULL,
 CONSTRAINT [PK_ad_publishers] PRIMARY KEY CLUSTERED 
(
	[publisherID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO


insert into ad_publishers (sitecode, publisherName)
select sitecode, orgname
from sites s
inner join organizations o
	on o.orgID = s.orgID

insert into ad_publishers (sitecode, publisherName)
select state as sitecode, description
from tlasites.trialsmith.dbo.depotla s
left outer join ad_publishers ap
	on ap.sitecode = s.state
where ap.publisherID is null

GO

USE [memberCentral]
GO

ALTER PROC [dbo].[ad_selectAdsToShow]
	@zoneTypeName varchar(255),
	@sitecode varchar(5),
	@NumberOfAdsToShow int,
	@uniqueCode varchar(50) = NULL
AS

declare @zoneID int

select @zoneID = z.zoneID
from membercentral.dbo.ad_zones z
inner join membercentral.dbo.ad_zoneTypes zt
	on zt.zoneTypeID = z.zoneTypeID
	and zt.zoneTypeName = @zoneTypeName
	and z.sitecode = @sitecode
	and z.uniqueCode = isnull(@uniqueCode, z.uniqueCode)
order by z.zoneID


update adpr2 WITH (UPDLOCK, READPAST)
set
	adpr2.lastshown = getdate()
OUTPUT
	selectedAds.adID, selectedAds.adName, selectedAds.imageUrl, selectedAds.adLink, selectedAds.adShortText, selectedAds.adContentObjectID, selectedAds.imageHeight, selectedAds.imageWidth,
	pub.publisherName as orgname, nextAds.googleUACode, nextAds.zoneTypeName, nextAds.zoneName
from platformstats.dbo.ad_adPlacementRotation adpr2
inner join (
		select top (@NumberOfAdsToShow) adp.adPlacementID, adp.adID, adv.googleUACode, azt.zoneTypeName, adz.zoneName
		from dbo.ad_zones adz
		inner join dbo.ad_zoneTypes azt
			on azt.zoneTypeID = adz.zoneTypeID
			and adz.zoneID = @zoneID
		inner join dbo.ad_zoneAdTypes azat
			on azat.zoneTypeID = azt.zoneTypeID
		inner join dbo.ad_Types adt
			on azat.adTypeID = adt.adTypeID
		inner join dbo.ad_adPlacements adp
			on adp.zoneID = adz.zoneID
		inner join dbo.ad_advertiserZones adtz
			on adp.zoneID = adtz.zoneID
		inner join dbo.ad_advertisers adv
			on adv.advertiserID = adtz.advertiserID
			and adv.status = 'A'
		inner join ad_ads ad
			on ad.adTypeID = adt.adTypeID
			and adp.adID = ad.adID
			and adtz.advertiserID = ad.advertiserID
			and ad.status = 'A'
		inner join platformstats.dbo.ad_adPlacementRotation adpr
			on adpr.adPlacementID = adp.adPlacementID
		order by adpr.lastshown
	) as nextAds on nextAds.adPlacementID = adpr2.adPlacementID
inner join membercentral.dbo.ad_ads selectedAds
	on selectedAds.adID = nextAds.adID
inner join membercentral.dbo.ad_publishers pub
	on pub.sitecode = @sitecode

GO

/* To prevent any potential data loss issues, you should review this script in detail before running it outside the context of the database designer.*/
BEGIN TRANSACTION
SET QUOTED_IDENTIFIER ON
SET ARITHABORT ON
SET NUMERIC_ROUNDABORT OFF
SET CONCAT_NULL_YIELDS_NULL ON
SET ANSI_NULLS ON
SET ANSI_PADDING ON
SET ANSI_WARNINGS ON
COMMIT
BEGIN TRANSACTION
GO
ALTER TABLE dbo.ad_zones
	DROP CONSTRAINT FK_ad_zoneTypes_ad_zones
GO
COMMIT
BEGIN TRANSACTION
GO
CREATE TABLE dbo.Tmp_ad_zones
	(
	zoneID int NOT NULL IDENTITY (1, 1),
	zoneName varchar(255) NOT NULL,
	zoneTypeID int NOT NULL,
	siteCode varchar(5) NOT NULL,
	uniqueCode varchar(50) NOT NULL
	)  ON [PRIMARY]
GO
SET IDENTITY_INSERT dbo.Tmp_ad_zones ON
GO
IF EXISTS(SELECT * FROM dbo.ad_zones)
	 EXEC('INSERT INTO dbo.Tmp_ad_zones (zoneID, zoneName, zoneTypeID, siteCode, uniqueCode)
		SELECT zoneID, zoneName, zoneTypeID, CONVERT(varchar(5), siteCode), uniqueCode FROM dbo.ad_zones WITH (HOLDLOCK TABLOCKX)')
GO
SET IDENTITY_INSERT dbo.Tmp_ad_zones OFF
GO
ALTER TABLE dbo.ad_advertiserZones
	DROP CONSTRAINT FK_ad_advertiserZones_ad_zones
GO
ALTER TABLE dbo.ad_adPlacements
	DROP CONSTRAINT FK_ad_adPlacements_ad_zones
GO
DROP TABLE dbo.ad_zones
GO
EXECUTE sp_rename N'dbo.Tmp_ad_zones', N'ad_zones', 'OBJECT' 
GO
ALTER TABLE dbo.ad_zones ADD CONSTRAINT
	PK__ad_zones__5C33AFCB PRIMARY KEY CLUSTERED 
	(
	zoneID
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

GO
ALTER TABLE dbo.ad_zones WITH NOCHECK ADD CONSTRAINT
	FK_ad_zoneTypes_ad_zones FOREIGN KEY
	(
	zoneTypeID
	) REFERENCES dbo.ad_zoneTypes
	(
	zoneTypeID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.ad_zones ADD CONSTRAINT
	FK_ad_zones_ad_zones FOREIGN KEY
	(
	zoneID
	) REFERENCES dbo.ad_zones
	(
	zoneID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
COMMIT
BEGIN TRANSACTION
GO
ALTER TABLE dbo.ad_adPlacements WITH NOCHECK ADD CONSTRAINT
	FK_ad_adPlacements_ad_zones FOREIGN KEY
	(
	zoneID
	) REFERENCES dbo.ad_zones
	(
	zoneID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
COMMIT
BEGIN TRANSACTION
GO
ALTER TABLE dbo.ad_advertiserZones WITH NOCHECK ADD CONSTRAINT
	FK_ad_advertiserZones_ad_zones FOREIGN KEY
	(
	zoneID
	) REFERENCES dbo.ad_zones
	(
	zoneID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
COMMIT

GO
/* To prevent any potential data loss issues, you should review this script in detail before running it outside the context of the database designer.*/
BEGIN TRANSACTION
SET QUOTED_IDENTIFIER ON
SET ARITHABORT ON
SET NUMERIC_ROUNDABORT OFF
SET CONCAT_NULL_YIELDS_NULL ON
SET ANSI_NULLS ON
SET ANSI_PADDING ON
SET ANSI_WARNINGS ON
COMMIT
BEGIN TRANSACTION
GO
ALTER TABLE dbo.ad_zones
	DROP CONSTRAINT FK_ad_zones_ad_zones
GO
COMMIT
GO