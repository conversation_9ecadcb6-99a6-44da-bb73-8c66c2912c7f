USE [seminarWeb]
GO
ALTER PROC [dbo].[general_ExportStorageReport]
@dtmonth datetime,	-- should be 1st day of the month for the report
@filename varchar(400)

AS

-- this shows detail for streaming/storage by orgcode, month, and file.
-- it groups streaming and storage MB into one column
-- it groups fileformats into one row

IF OBJECT_ID('tempdb..#tmpStats') IS NOT NULL 
	DROP TABLE #tmpStats
IF OBJECT_ID('tempdb..#tmpStats2') IS NOT NULL 
	DROP TABLE #tmpStats2
IF OBJECT_ID('tempdb..#tmpPrograms') IS NOT NULL 
	DROP TABLE #tmpPrograms
IF OBJECT_ID('tempdb..##tmpStorage') IS NOT NULL 
	DROP TABLE ##tmpStorage

SELECT upper(fh.orgcode) as orgcode, fh.filename, fh.filePath, 
	CONVERT(varchar(6), fhs.dateReported, 112) AS MonthRpt, 
	CASE WHEN fh.filepath like 'swod%' then ParseName(fh.fileName,2) else null end as SWFileID,
	MAX(fhs.fileSizeKB) AS totalKB
INTO #tmpStats
FROM platformStats.dbo.tblFileHosting AS fh 
INNER JOIN platformStats.dbo.tblFileHostingStorage AS fhs ON fh.fhid = fhs.fhid
WHERE fhs.dateReported BETWEEN @dtmonth and DateAdd(s,-1,DateAdd(day,0,DateAdd(month,DateDiff(month,0,@dtmonth)+1, 0)))
GROUP BY fh.originid, fh.orgcode, fh.filename, fh.filePath, CONVERT(varchar(6), fhs.dateReported, 112)
	union all
SELECT upper(fh.orgcode) as orgcode, fh.filename, fh.filePath, 
	CONVERT(varchar(6), fhs.dateReported, 112) AS MonthRpt, 
	CASE WHEN fh.filepath like 'swod%' then ParseName(fh.fileName,2) else null end as SWFileID,
	SUM(fhs.bandwidthKB) AS streamingKB
FROM platformStats.dbo.tblFileHosting AS fh 
INNER JOIN platformStats.dbo.tblFileHostingStream AS fhs ON fh.fhid = fhs.fhid
WHERE fhs.dateReported BETWEEN @dtmonth and DateAdd(s,-1,DateAdd(day,0,DateAdd(month,DateDiff(month,0,@dtmonth)+1, 0)))
GROUP BY fh.originid, fh.orgcode, fh.filename, fh.filePath, CONVERT(varchar(6), fhs.dateReported, 112)


select p.orgcode, seminarweb.dbo.pipeList(np.programName) as programList
into #tmpPrograms
from seminarweb.dbo.tblParticipants as p
inner join seminarweb.dbo.tblNationalProgramParticipants as npp on npp.participantID = p.participantID
inner join seminarweb.dbo.tblNationalPrograms as np on np.programID = npp.programID
group by p.orgcode


select tmp.orgcode, tmp.fileName, tmp.filePath, tmp.monthRpt, tmp.SWFileID, 
	f.fileTitle, count(distinct t.titleID) as NumberOfPublishedTitlesContainingFile,
	count(distinct s2.seminarID) as NumberOfPublishedSeminarsContainingFile,
	cast(sum(tmp.totalKB) as float) / 1000 as totalMB,
	CASE 
	WHEN tmp.SWFileID is null then 'Website: ' + isnull(ParseName(tmp.fileName,2),'')
	ELSE 'SW-' + cast(tmp.SWFileID as varchar(10)) + ': ' + isnull(f.fileTitle,'')
	END as ContentTitle,
	p.programList as [SeminarWeb National Programs]
into #tmpStats2
from #tmpStats as tmp
left outer join seminarweb.dbo.tblFiles as f on f.fileID = tmp.SWFileID
left outer join seminarweb.dbo.tblTitlesAndFiles as taf 
	inner join seminarweb.dbo.tblTitles as t on t.titleID = taf.titleID and t.isPublished = 1 and t.isDeleted = 0
	on taf.fileID = f.fileID
left outer join seminarweb.dbo.tblTitlesAndFiles as taf2 
	inner join seminarweb.dbo.tblTitles as t2 on t2.titleID = taf2.titleID and t2.isPublished = 1 and t2.isDeleted = 0
	inner join seminarweb.dbo.tblSeminarsAndTitles as sat2 on sat2.titleID = t2.titleID
	inner join seminarweb.dbo.tblSeminars as s2 on s2.seminarID = sat2.seminarID and s2.isPublished = 1 and s2.isDeleted = 0
	on taf2.fileID = f.fileID
left outer join #tmpPrograms as p on p.orgcode = tmp.orgcode
group by tmp.orgcode, tmp.fileName, tmp.filePath, tmp.monthRpt, tmp.SWFileID, f.fileTitle, p.programList


select OrgCode, ContentTitle, max(NumberOfPublishedTitlesContainingFile) as [Number Of Published Titles Containing File], 
	max(NumberOfPublishedSeminarsContainingFile) as [Number Of Published Seminars Containing File], 
	sum(totalMB) as totalMB, [SeminarWeb National Programs]
into ##tmpStorage
from #tmpStats2
group by OrgCode, ContentTitle, [SeminarWeb National Programs]
order by OrgCode, ContentTitle


DECLARE @fullsql varchar(max)	
SELECT @fullsql = 'select * from ##tmpStorage order by orgcode, contentTitle'
EXEC trialsmith.dbo.up_exportCSV_new @csvfilename=@filename, @sql=@fullsql

IF OBJECT_ID('tempdb..#tmpStats') IS NOT NULL 
	DROP TABLE #tmpStats
IF OBJECT_ID('tempdb..#tmpStats2') IS NOT NULL 
	DROP TABLE #tmpStats2
IF OBJECT_ID('tempdb..#tmpPrograms') IS NOT NULL 
	DROP TABLE #tmpPrograms
IF OBJECT_ID('tempdb..##tmpStorage') IS NOT NULL 
	DROP TABLE ##tmpStorage
GO