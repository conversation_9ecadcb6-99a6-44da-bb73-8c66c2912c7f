use membercentral
GO

ALTER PROC [dbo].[ams_saveMemberWebsite]
@memberID int,
@websiteTypeID int,
@website varchar(400),
@byPassQueue bit = 0

as

-- be nice and clean up their website data first (ensure http:// in front)
if len(@website) > 0 and left(@website,4) <> 'http'
	set @website = 'http://' + @website

-- ensure valid formed
declare @tldList varchar(max), @regex varchar(max)
set @tldList = 'com|edu|gov|int|mil|net|org|arpa|coop|asia|cat|academy|accountants|active|actor|aero|agency|airforce|archi|army|associates|attorney|auction|audio|autos|band|bargains|beer|best|bid|bike|bio|biz|black|blackfriday|blue|boo|boutique|build|builders|business|buzz|cab|camera|camp|cancerresearch|capital|cards|care|career|careers|cash|catering|center|ceo|channel|cheap|christmas|church|city|claims|cleaning|click|clinic|clothing|club|coach|codes|coffee|college|community|company|computer|condos|construction|consulting|contractors|cooking|cool|country|credit|creditcard|cricket|cruises|dad|dance|dating|day|deals|degree|delivery|democrat|dental|dentist|diamonds|diet|digital|direct|directory|discount|domains|eat|education|email|energy|engineer|engineering|equipment|esq|estate|events|exchange|expert|exposed|fail|farm|feedback|finance|financial|fish|fishing|fitness|flights|florist|fly|foo|forsale|foundation|fund|furniture|futbol|gallery|gift|gifts|gives|glass|global|gop|graphics|green|gripe|guide|guitars|guru|healthcare|help|here|hiphop|hiv|holdings|holiday|homes|horse|host|hosting|house|how|info|ing|ink|insure|international|investments|jobs|kim|kitchen|land|lawyer|lease|legal|lgbt|life|lighting|limited|limo|link|loans|lotto|luxe|luxury|management|market|marketing|media|meet|meme|memorial|menu|mobi|moe|money|mortgage|motorcycles|mov|museum|name|navy|network|new|ngo|ninja|ong|onl|ooo|organic|partners|parts|party|pharmacy|photo|photography|photos|physio|pics|pictures|pink|pizza|place|plumbing|poker|post|press|pro|productions|prof|properties|property|qpon|recipes|red|rehab|ren|rentals|repair|report|republican|reviews|rich|rip|rocks|rodeo|rsvp|science|services|sexy|shoes|singles|social|software|solar|solutions|space|supplies|supply|support|surf|surgery|systems|tattoo|tax|technology|tel|tips|tires|today|tools|top|town|toys|trade|training|travel|university|vacations|vet|villas|vision|vodka|vote|voting|voyage|wang|watch|webcam|website|wed|wiki|works|world|wtf|xxx|xyz|zone'
set @regex = '^(http|https|ftp)\://([a-zA-Z0-9\.\-]+(\:[a-zA-Z0-9\.&amp;%\$\-]+)*@)*((25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9])\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[0-9])|localhost|([a-zA-Z0-9\-]+\.)*[a-zA-Z0-9\-]+\.(' + @tldList + '|[a-zA-Z]{2}))(\:[0-9]+)*(/($|[a-zA-Z0-9\.\,\?\''\\\+&amp;%\!$#\=~_\-]+))*$'
if len(@website) > 0 and dbo.fn_RegExReplace(@website,@regex,'') <> ''
	set @website = ''

if len(@website) > 0 BEGIN
	if exists (select websiteID from dbo.ams_memberWebsites where memberID = @memberID and websiteTypeID = @websiteTypeID) begin
		update dbo.ams_memberwebsites
		set website = @website
		where memberID = @memberID 
		and websiteTypeID = @websiteTypeID
	end else begin
		insert into dbo.ams_memberwebsites (memberID, websiteTypeID, website)
		values (@memberID, @websiteTypeID, @website)
	end
end else begin
	delete from dbo.ams_memberWebsites
	where memberID = @memberID 
	and websiteTypeID = @websiteTypeID
end

-- set member as updated
update dbo.ams_members
set dateLastUpdated = getdate()
where memberID = @memberID

IF @byPassQueue = 0 BEGIN
	-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
	declare @itemGroupUID uniqueidentifier, @orgID int, @conditionIDList varchar(max)
	SELECT @orgID = orgID from dbo.ams_members where memberID = @memberID	
	SELECT @conditionIDList = COALESCE(@conditionIDList + ',', '') + cast(c.conditionID as varchar(10)) 
		from dbo.ams_virtualGroupConditions as c
		where c.orgID = @orgID
		and c.fieldcode = 'mw_' + cast(@websiteTypeID as varchar(10)) + '_website'
		group by c.conditionID
	IF @conditionIDList is not null
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberID, @conditionIDList=@conditionIDList, @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT
END

return 0
GO

update ams_memberWebsites
set website = 'http://' + website
where len(website) > 0 and left(website,4) <> 'http'
GO

DELETE FROM ams_memberWebsites
WHERE len(website) > 0 and dbo.fn_RegExReplace(website,'^(http|https|ftp)\://([a-zA-Z0-9\.\-]+(\:[a-zA-Z0-9\.&%\$\-]+)*@)*((25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9])\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[0-9])|localhost|([a-zA-Z0-9\-]+\.)*[a-zA-Z0-9\-]+\.(com|edu|gov|int|mil|net|org|arpa|coop|asia|cat|academy|accountants|active|actor|aero|agency|airforce|archi|army|associates|attorney|auction|audio|autos|band|bargains|beer|best|bid|bike|bio|biz|black|blackfriday|blue|boo|boutique|build|builders|business|buzz|cab|camera|camp|cancerresearch|capital|cards|care|career|careers|cash|catering|center|ceo|channel|cheap|christmas|church|city|claims|cleaning|click|clinic|clothing|club|coach|codes|coffee|college|community|company|computer|condos|construction|consulting|contractors|cooking|cool|country|credit|creditcard|cricket|cruises|dad|dance|dating|day|deals|degree|delivery|democrat|dental|dentist|diamonds|diet|digital|direct|directory|discount|domains|eat|education|email|energy|engineer|engineering|equipment|esq|estate|events|exchange|expert|exposed|fail|farm|feedback|finance|financial|fish|fishing|fitness|flights|florist|fly|foo|forsale|foundation|fund|furniture|futbol|gallery|gift|gifts|gives|glass|global|gop|graphics|green|gripe|guide|guitars|guru|healthcare|help|here|hiphop|hiv|holdings|holiday|homes|horse|host|hosting|house|how|info|ing|ink|insure|international|investments|jobs|kim|kitchen|land|lawyer|lease|legal|lgbt|life|lighting|limited|limo|link|loans|lotto|luxe|luxury|management|market|marketing|media|meet|meme|memorial|menu|mobi|moe|money|mortgage|motorcycles|mov|museum|name|navy|network|new|ngo|ninja|ong|onl|ooo|organic|partners|parts|party|pharmacy|photo|photography|photos|physio|pics|pictures|pink|pizza|place|plumbing|poker|post|press|pro|productions|prof|properties|property|qpon|recipes|red|rehab|ren|rentals|repair|report|republican|reviews|rich|rip|rocks|rodeo|rsvp|science|services|sexy|shoes|singles|social|software|solar|solutions|space|supplies|supply|support|surf|surgery|systems|tattoo|tax|technology|tel|tips|tires|today|tools|top|town|toys|trade|training|travel|university|vacations|vet|villas|vision|vodka|vote|voting|voyage|wang|watch|webcam|website|wed|wiki|works|world|wtf|xxx|xyz|zone|[a-zA-Z]{2}))(\:[0-9]+)*(/($|[a-zA-Z0-9\.\,\?\''\\\+&%\!$#\=~_\-]+))*$','') <> ''
GO

