-- MCDEV-10154 - Add Sponsor Groupings to the Sponsors Module
-- This migration adds sponsor grouping functionality similar to Event Rate Groupings
-- for both MemberCentral events and SeminarWeb programs

USE memberCentral;
GO

-- %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
-- (1) Create ev_sponsorGrouping TABLE for MemberCentral Events
-- %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

CREATE TABLE dbo.ev_sponsorGrouping(
	sponsorGroupingID INT IDENTITY(1,1) NOT NULL,
	registrationID INT NOT NULL,
	sponsorGrouping VARCHAR(200) NOT NULL,
	sponsorGroupingOrder INT NOT NULL,
 CONSTRAINT PK_ev_sponsorGrouping PRIMARY KEY CLUSTERED 
(
	sponsorGroupingID ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT IX_ev_sponsorGrouping UNIQUE NONCLUSTERED 
(
	registrationID ASC,
	sponsorGrouping ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

ALTER TABLE dbo.ev_sponsorGrouping WITH CHECK ADD CONSTRAINT FK_ev_sponsorGrouping_ev_registration FOREIGN KEY(registrationID)
REFERENCES dbo.ev_registration (registrationID)
GO

-- %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
-- (2) Modify sponsorsUsage TABLE to add sponsorGroupingID column
-- %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

ALTER TABLE dbo.sponsorsUsage ADD sponsorGroupingID INT NULL;
GO

-- Note: We don't add FK constraint yet as we need to support both ev_sponsorGrouping and sw_sponsorGrouping
-- The constraint will be handled at the application level

-- %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
-- (3) Create stored procedures for MemberCentral Events sponsor groupings
-- %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

-- Create sponsor grouping procedure
CREATE PROC dbo.ev_createSponsorGrouping
@registrationID int,
@sponsorGrouping varchar(200),
@sponsorGroupingID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	set @sponsorGroupingID = null;

	IF EXISTS (select sponsorGroupingID from dbo.ev_sponsorGrouping where registrationID=@registrationID and sponsorGrouping=@sponsorGrouping)
		RAISERROR('Sponsor Grouping exists.',16,1);

	DECLARE @sponsorGroupingOrder int;
	SELECT @sponsorGroupingOrder = isnull(max(sponsorGroupingOrder),0)+1 from dbo.ev_sponsorGrouping where registrationID=@registrationID;

	INSERT INTO dbo.ev_sponsorGrouping (registrationID, sponsorGrouping, sponsorGroupingOrder)
	VALUES (@registrationID, @sponsorGrouping, @sponsorGroupingOrder);

	SELECT @sponsorGroupingID = SCOPE_IDENTITY();

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

-- Update sponsor grouping procedure
CREATE PROC dbo.ev_updateSponsorGrouping
@sponsorGroupingID int,
@sponsorGrouping varchar(200)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF NOT EXISTS (select sponsorGroupingID from dbo.ev_sponsorGrouping where sponsorGroupingID=@sponsorGroupingID)
		RAISERROR('Sponsor Grouping does not exist.',16,1);

	DECLARE @registrationID int;
	SELECT @registrationID = registrationID from dbo.ev_sponsorGrouping where sponsorGroupingID=@sponsorGroupingID;

	IF EXISTS (select sponsorGroupingID from dbo.ev_sponsorGrouping where registrationID=@registrationID and sponsorGrouping=@sponsorGrouping and sponsorGroupingID != @sponsorGroupingID)
		RAISERROR('Sponsor Grouping name already exists.',16,1);

	UPDATE dbo.ev_sponsorGrouping 
	SET sponsorGrouping = @sponsorGrouping
	WHERE sponsorGroupingID = @sponsorGroupingID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

-- Delete sponsor grouping procedure
CREATE PROC dbo.ev_deleteSponsorGrouping
@sponsorGroupingID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF NOT EXISTS (select sponsorGroupingID from dbo.ev_sponsorGrouping where sponsorGroupingID=@sponsorGroupingID)
		RAISERROR('Sponsor Grouping does not exist.',16,1);

	-- Check if any sponsors are using this grouping
	IF EXISTS (select sponsorUsageID from dbo.sponsorsUsage where sponsorGroupingID=@sponsorGroupingID)
		RAISERROR('Cannot delete sponsor grouping that has sponsors assigned to it.',16,1);

	DECLARE @registrationID int;
	SELECT @registrationID = registrationID from dbo.ev_sponsorGrouping where sponsorGroupingID=@sponsorGroupingID;

	DELETE FROM dbo.ev_sponsorGrouping WHERE sponsorGroupingID = @sponsorGroupingID;

	-- Reorder remaining groupings
	EXEC dbo.ev_reorderSponsorGroupings @registrationID=@registrationID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

-- Reorder sponsor groupings procedure
CREATE PROC dbo.ev_reorderSponsorGroupings
@registrationID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @tmp TABLE (neworder int NOT NULL, sponsorGroupingID int NOT NULL, sponsorGroupingOrder int NOT NULL);
	
	INSERT INTO @tmp (sponsorGroupingID, sponsorGroupingOrder, newOrder)
	SELECT sponsorGroupingID, sponsorGroupingOrder, ROW_NUMBER() OVER(ORDER BY sponsorGroupingOrder) as newOrder
	FROM dbo.ev_sponsorGrouping
	WHERE registrationID = @registrationID;
	
	UPDATE sg
	SET sg.sponsorGroupingOrder = t.neworder
	FROM dbo.ev_sponsorGrouping as sg 
	INNER JOIN @tmp as t on sg.sponsorGroupingID = t.sponsorGroupingID;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

-- Move sponsor grouping procedure
CREATE PROC dbo.ev_moveSponsorGrouping
@sponsorGroupingID int,
@direction varchar(10)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF @direction NOT IN ('up','down')
		RAISERROR('Invalid direction. Must be up or down.',16,1);

	DECLARE @registrationID int, @currentOrder int, @swapOrder int, @swapGroupingID int;
	
	SELECT @registrationID = registrationID, @currentOrder = sponsorGroupingOrder 
	FROM dbo.ev_sponsorGrouping 
	WHERE sponsorGroupingID = @sponsorGroupingID;

	IF @registrationID IS NULL
		RAISERROR('Sponsor Grouping does not exist.',16,1);

	-- Find the grouping to swap with
	IF @direction = 'up'
	BEGIN
		SELECT TOP 1 @swapGroupingID = sponsorGroupingID, @swapOrder = sponsorGroupingOrder
		FROM dbo.ev_sponsorGrouping
		WHERE registrationID = @registrationID AND sponsorGroupingOrder < @currentOrder
		ORDER BY sponsorGroupingOrder DESC;
	END
	ELSE
	BEGIN
		SELECT TOP 1 @swapGroupingID = sponsorGroupingID, @swapOrder = sponsorGroupingOrder
		FROM dbo.ev_sponsorGrouping
		WHERE registrationID = @registrationID AND sponsorGroupingOrder > @currentOrder
		ORDER BY sponsorGroupingOrder ASC;
	END

	-- Perform the swap if we found a grouping to swap with
	IF @swapGroupingID IS NOT NULL
	BEGIN
		UPDATE dbo.ev_sponsorGrouping SET sponsorGroupingOrder = @swapOrder WHERE sponsorGroupingID = @sponsorGroupingID;
		UPDATE dbo.ev_sponsorGrouping SET sponsorGroupingOrder = @currentOrder WHERE sponsorGroupingID = @swapGroupingID;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

-- Get sponsor groupings by registration ID
CREATE PROC dbo.ev_getSponsorGroupingsByRegistrationID
@registrationID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SELECT sponsorGroupingID, sponsorGrouping, sponsorGroupingOrder
	FROM dbo.ev_sponsorGrouping
	WHERE registrationID = @registrationID
	ORDER BY sponsorGroupingOrder;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

-- %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
-- (4) Switch to SeminarWeb database for SeminarWeb sponsor groupings
-- %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

USE seminarWeb;
GO

-- Create sw_sponsorGrouping TABLE for SeminarWeb Programs
CREATE TABLE dbo.sw_sponsorGrouping(
	sponsorGroupingID INT IDENTITY(1,1) NOT NULL,
	seminarID INT NOT NULL,
	participantID INT NULL,
	sponsorGrouping VARCHAR(200) NOT NULL,
	sponsorGroupingOrder INT NOT NULL,
 CONSTRAINT PK_sw_sponsorGrouping PRIMARY KEY CLUSTERED
(
	sponsorGroupingID ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90) ON [PRIMARY],
 CONSTRAINT IX_sw_sponsorGrouping UNIQUE NONCLUSTERED
(
	seminarID ASC,
	participantID ASC,
	sponsorGrouping ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90) ON [PRIMARY]
) ON [PRIMARY]

GO

ALTER TABLE dbo.sw_sponsorGrouping WITH CHECK ADD CONSTRAINT FK_sw_sponsorGrouping_tblSeminars FOREIGN KEY(seminarID)
REFERENCES dbo.tblSeminars (seminarID)
GO

ALTER TABLE dbo.sw_sponsorGrouping WITH CHECK ADD CONSTRAINT FK_sw_sponsorGrouping_tblParticipants FOREIGN KEY(participantID)
REFERENCES dbo.tblParticipants (participantID)
GO

-- Create sponsor grouping procedure for SeminarWeb
CREATE PROC dbo.sw_createSponsorGrouping
@seminarID INT,
@participantID INT,
@sponsorGrouping VARCHAR(200),
@sponsorGroupingID INT OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET @sponsorGroupingID = NULL;

	IF EXISTS (SELECT sponsorGroupingID FROM dbo.sw_sponsorGrouping WHERE seminarID = @seminarID AND sponsorGrouping = @sponsorGrouping AND participantID = @participantID)
		RAISERROR('Sponsor Grouping Exists.',16,1);

	DECLARE @sponsorGroupingOrder INT;
	SELECT @sponsorGroupingOrder = ISNULL(MAX(sponsorGroupingOrder),0)+1
	FROM dbo.sw_sponsorGrouping
	WHERE seminarID = @seminarID
	AND participantID = @participantID;

	INSERT INTO dbo.sw_sponsorGrouping (participantID, seminarID, sponsorGrouping, sponsorGroupingOrder)
	VALUES (@participantID, @seminarID, @sponsorGrouping, @sponsorGroupingOrder);

	SELECT @sponsorGroupingID = SCOPE_IDENTITY();

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

-- Update sponsor grouping procedure for SeminarWeb
CREATE PROC dbo.sw_updateSponsorGrouping
@sponsorGroupingID INT,
@sponsorGrouping VARCHAR(200)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF NOT EXISTS (SELECT sponsorGroupingID FROM dbo.sw_sponsorGrouping WHERE sponsorGroupingID=@sponsorGroupingID)
		RAISERROR('Sponsor Grouping does not exist.',16,1);

	DECLARE @seminarID INT, @participantID INT;
	SELECT @seminarID = seminarID, @participantID = participantID FROM dbo.sw_sponsorGrouping WHERE sponsorGroupingID=@sponsorGroupingID;

	IF EXISTS (SELECT sponsorGroupingID FROM dbo.sw_sponsorGrouping WHERE seminarID=@seminarID AND participantID=@participantID AND sponsorGrouping=@sponsorGrouping AND sponsorGroupingID != @sponsorGroupingID)
		RAISERROR('Sponsor Grouping name already exists.',16,1);

	UPDATE dbo.sw_sponsorGrouping
	SET sponsorGrouping = @sponsorGrouping
	WHERE sponsorGroupingID = @sponsorGroupingID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

-- Delete sponsor grouping procedure for SeminarWeb
CREATE PROC dbo.sw_deleteSponsorGrouping
@sponsorGroupingID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF NOT EXISTS (SELECT sponsorGroupingID FROM dbo.sw_sponsorGrouping WHERE sponsorGroupingID=@sponsorGroupingID)
		RAISERROR('Sponsor Grouping does not exist.',16,1);

	-- Check if any sponsors are using this grouping
	IF EXISTS (SELECT sponsorUsageID FROM memberCentral.dbo.sponsorsUsage WHERE sponsorGroupingID=@sponsorGroupingID)
		RAISERROR('Cannot delete sponsor grouping that has sponsors assigned to it.',16,1);

	DECLARE @seminarID INT, @participantID INT;
	SELECT @seminarID = seminarID, @participantID = participantID FROM dbo.sw_sponsorGrouping WHERE sponsorGroupingID=@sponsorGroupingID;

	DELETE FROM dbo.sw_sponsorGrouping WHERE sponsorGroupingID = @sponsorGroupingID;

	-- Reorder remaining groupings
	EXEC dbo.sw_reorderSponsorGroupings @seminarID=@seminarID, @participantID=@participantID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

-- Reorder sponsor groupings procedure for SeminarWeb
CREATE PROC dbo.sw_reorderSponsorGroupings
@seminarID INT,
@participantID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @tmp TABLE (neworder INT NOT NULL, sponsorGroupingID INT NOT NULL, sponsorGroupingOrder INT NOT NULL);

	INSERT INTO @tmp (sponsorGroupingID, sponsorGroupingOrder, newOrder)
	SELECT sponsorGroupingID, sponsorGroupingOrder, ROW_NUMBER() OVER(ORDER BY sponsorGroupingOrder) AS newOrder
	FROM dbo.sw_sponsorGrouping
	WHERE seminarID = @seminarID
	AND participantID = @participantID;

	UPDATE sg
	SET sg.sponsorGroupingOrder = t.neworder
	FROM dbo.sw_sponsorGrouping AS sg
	INNER JOIN @tmp AS t ON sg.sponsorGroupingID = t.sponsorGroupingID
	WHERE sg.participantID = @participantID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

-- Move sponsor grouping procedure for SeminarWeb
CREATE PROC dbo.sw_moveSponsorGrouping
@sponsorGroupingID INT,
@direction VARCHAR(10)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF @direction NOT IN ('up','down')
		RAISERROR('Invalid direction. Must be up or down.',16,1);

	DECLARE @seminarID INT, @participantID INT, @currentOrder INT, @swapOrder INT, @swapGroupingID INT;

	SELECT @seminarID = seminarID, @participantID = participantID, @currentOrder = sponsorGroupingOrder
	FROM dbo.sw_sponsorGrouping
	WHERE sponsorGroupingID = @sponsorGroupingID;

	IF @seminarID IS NULL
		RAISERROR('Sponsor Grouping does not exist.',16,1);

	-- Find the grouping to swap with
	IF @direction = 'up'
	BEGIN
		SELECT TOP 1 @swapGroupingID = sponsorGroupingID, @swapOrder = sponsorGroupingOrder
		FROM dbo.sw_sponsorGrouping
		WHERE seminarID = @seminarID AND participantID = @participantID AND sponsorGroupingOrder < @currentOrder
		ORDER BY sponsorGroupingOrder DESC;
	END
	ELSE
	BEGIN
		SELECT TOP 1 @swapGroupingID = sponsorGroupingID, @swapOrder = sponsorGroupingOrder
		FROM dbo.sw_sponsorGrouping
		WHERE seminarID = @seminarID AND participantID = @participantID AND sponsorGroupingOrder > @currentOrder
		ORDER BY sponsorGroupingOrder ASC;
	END

	-- Perform the swap if we found a grouping to swap with
	IF @swapGroupingID IS NOT NULL
	BEGIN
		UPDATE dbo.sw_sponsorGrouping SET sponsorGroupingOrder = @swapOrder WHERE sponsorGroupingID = @sponsorGroupingID;
		UPDATE dbo.sw_sponsorGrouping SET sponsorGroupingOrder = @currentOrder WHERE sponsorGroupingID = @swapGroupingID;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

-- Get sponsor groupings by seminar ID for SeminarWeb
CREATE PROC dbo.sw_getSponsorGroupingsBySeminarID
@seminarID INT,
@participantID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SELECT sponsorGroupingID, sponsorGrouping, sponsorGroupingOrder
	FROM dbo.sw_sponsorGrouping
	WHERE seminarID = @seminarID
	AND participantID = @participantID
	ORDER BY sponsorGroupingOrder;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

-- %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
-- (5) Switch back to MemberCentral database for updated sponsor retrieval procedures
-- %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

USE memberCentral;
GO

-- Update the existing sponsors_getSponsorsByReferenceIDFull procedure to include grouping information
ALTER PROC dbo.sponsors_getSponsorsByReferenceIDFull
@siteID INT,
@referenceType VARCHAR(20),
@referenceID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	DECLARE @featureImageConfigReferenceType VARCHAR(20) = 'SponsorImages';
	DECLARE @featureImageReferenceType VARCHAR(20) = 'Sponsor';

	-- For Events, get sponsor groupings from ev_sponsorGrouping
	IF @referenceType = 'Events'
	BEGIN
		SELECT su.sponsorUsageID, s.sponsorID, s.sponsorName, s.sponsorURL, s.sponsorContentId,
			sponsorContent.rawContent as sponsorContent, ficu.featureImageConfigID, fiu.featureImageID,
			fics.fileExtension, ficus.featureImageSizeID, su.sponsorOrder,
			esg.sponsorGroupingID, esg.sponsorGrouping, esg.sponsorGroupingOrder
		FROM dbo.sponsorsUsage as su
		INNER JOIN dbo.sponsors as s on s.siteID = @siteID and s.sponsorID = su.sponsorID
		LEFT OUTER JOIN dbo.ev_sponsorGrouping as esg on esg.sponsorGroupingID = su.sponsorGroupingID
		LEFT OUTER JOIN dbo.cms_featuredImageConfigUsages AS ficu ON ficu.referenceID = s.siteID AND ficu.referenceType = @featureImageConfigReferenceType
		LEFT OUTER JOIN dbo.cms_featuredImageUsages AS fiu ON fiu.featureImageConfigID = ficu.featureImageConfigID AND fiu.referenceID = s.sponsorID
			AND fiu.referenceType = @featureImageReferenceType
		LEFT OUTER JOIN dbo.cms_featuredImages AS fi ON fi.featureImageID = fiu.featureImageID
		LEFT OUTER JOIN dbo.cms_featuredImageSizes AS fics ON fics.featureImageID = fi.featureImageID
		LEFT OUTER JOIN dbo.cms_featuredImageConfigSizes AS ficus ON ficus.featureImageConfigID = ficu.featureImageConfigID
			AND ficus.featureImageSizeID = fics.featureImageSizeID
		CROSS APPLY dbo.fn_getContent(s.sponsorcontentID,1) AS sponsorContent
		WHERE su.referenceType = @referenceType
		AND su.referenceID = @referenceID
		ORDER BY ISNULL(esg.sponsorGroupingOrder, 999), su.sponsorOrder;
	END
	-- For SeminarWeb, get sponsor groupings from sw_sponsorGrouping
	ELSE IF @referenceType IN ('SeminarWeb', 'SWL', 'SWOD', 'SWB')
	BEGIN
		SELECT su.sponsorUsageID, s.sponsorID, s.sponsorName, s.sponsorURL, s.sponsorContentId,
			sponsorContent.rawContent as sponsorContent, ficu.featureImageConfigID, fiu.featureImageID,
			fics.fileExtension, ficus.featureImageSizeID, su.sponsorOrder,
			swsg.sponsorGroupingID, swsg.sponsorGrouping, swsg.sponsorGroupingOrder
		FROM dbo.sponsorsUsage as su
		INNER JOIN dbo.sponsors as s on s.siteID = @siteID and s.sponsorID = su.sponsorID
		LEFT OUTER JOIN seminarWeb.dbo.sw_sponsorGrouping as swsg on swsg.sponsorGroupingID = su.sponsorGroupingID
		LEFT OUTER JOIN dbo.cms_featuredImageConfigUsages AS ficu ON ficu.referenceID = s.siteID AND ficu.referenceType = @featureImageConfigReferenceType
		LEFT OUTER JOIN dbo.cms_featuredImageUsages AS fiu ON fiu.featureImageConfigID = ficu.featureImageConfigID AND fiu.referenceID = s.sponsorID
			AND fiu.referenceType = @featureImageReferenceType
		LEFT OUTER JOIN dbo.cms_featuredImages AS fi ON fi.featureImageID = fiu.featureImageID
		LEFT OUTER JOIN dbo.cms_featuredImageSizes AS fics ON fics.featureImageID = fi.featureImageID
		LEFT OUTER JOIN dbo.cms_featuredImageConfigSizes AS ficus ON ficus.featureImageConfigID = ficu.featureImageConfigID
			AND ficus.featureImageSizeID = fics.featureImageSizeID
		CROSS APPLY dbo.fn_getContent(s.sponsorcontentID,1) AS sponsorContent
		WHERE su.referenceType = @referenceType
		AND su.referenceID = @referenceID
		ORDER BY ISNULL(swsg.sponsorGroupingOrder, 999), su.sponsorOrder;
	END
	ELSE
	BEGIN
		-- For other reference types, return without grouping information
		SELECT su.sponsorUsageID, s.sponsorID, s.sponsorName, s.sponsorURL, s.sponsorContentId,
			sponsorContent.rawContent as sponsorContent, ficu.featureImageConfigID, fiu.featureImageID,
			fics.fileExtension, ficus.featureImageSizeID, su.sponsorOrder,
			NULL as sponsorGroupingID, NULL as sponsorGrouping, NULL as sponsorGroupingOrder
		FROM dbo.sponsorsUsage as su
		INNER JOIN dbo.sponsors as s on s.siteID = @siteID and s.sponsorID = su.sponsorID
		LEFT OUTER JOIN dbo.cms_featuredImageConfigUsages AS ficu ON ficu.referenceID = s.siteID AND ficu.referenceType = @featureImageConfigReferenceType
		LEFT OUTER JOIN dbo.cms_featuredImageUsages AS fiu ON fiu.featureImageConfigID = ficu.featureImageConfigID AND fiu.referenceID = s.sponsorID
			AND fiu.referenceType = @featureImageReferenceType
		LEFT OUTER JOIN dbo.cms_featuredImages AS fi ON fi.featureImageID = fiu.featureImageID
		LEFT OUTER JOIN dbo.cms_featuredImageSizes AS fics ON fics.featureImageID = fi.featureImageID
		LEFT OUTER JOIN dbo.cms_featuredImageConfigSizes AS ficus ON ficus.featureImageConfigID = ficu.featureImageConfigID
			AND ficus.featureImageSizeID = fics.featureImageSizeID
		CROSS APPLY dbo.fn_getContent(s.sponsorcontentID,1) AS sponsorContent
		WHERE su.referenceType = @referenceType
		AND su.referenceID = @referenceID
		ORDER BY su.sponsorOrder;
	END

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
