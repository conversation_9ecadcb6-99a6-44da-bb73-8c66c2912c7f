use customApps
GO
declare @udid int, @siteID int
select @siteID = siteID from membercentral.dbo.sites where sitecode='WI'

insert into dbo.schedTask_memberJoinDates (siteCode, joinDateFieldName, rejoinDateFieldName, droppedDateFieldName, paidThruDateFieldName, lastSuccessDate, lastErrorCode, isActive)
values ('WI', 'Join Date', 'Rejoin Date', 'Dropped Date', 'Paid Thru Date', '1/1/1980', 0, 1)
	select @udid = SCOPE_IDENTITY()

insert into dbo.schedTask_memberJoinDateSubTypes (memberJoinDateUDID, subscriptionTypeUID)
select @udid, [uid]
from membercentral.dbo.sub_types 
where siteID = @siteID
and status = 'A'
and typeName = 'Membership Dues'

DECLARE @errorcode int
EXEC dbo.job_memberJoinDates @udid, @errorcode OUTPUT
GO
