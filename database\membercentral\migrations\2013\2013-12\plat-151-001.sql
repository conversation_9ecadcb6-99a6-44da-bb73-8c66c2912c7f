insert into ams_memberFieldUsage
select 
	sr.siteResourceID,
	fu.fieldSetID, 
	'linkedrecord',
	NULL,
	1
from 
	dbo.cms_siteResources as sr
	inner join dbo.cms_siteResourceTypes as srt on 
		srt.resourceTypeID = sr.resourceTypeID 
		and srt.resourceType =  'MemberSettingsAdmin'
	inner join sites s on
		s.siteID = sr.siteID
		--and s.siteID not in (44, 2)
	outer apply (
		select
			siteResources.siteResourceID
		from 
			cms_siteResources siteResources
			inner join cms_siteResourceTypes rt on 
				rt.resourceTypeID = siteResources.resourceTypeID
				 and siteResources.siteID = s.siteID 
				and rt.resourceType = 'MemberAdmin' 
		where 
			siteResources.siteResourceStatusID = dbo.fn_getResourceStatusID('Active')) as sr2
	inner join ams_memberFieldUsage fu on
		sr2.siteResourceID = fu.siteResourceID
		and fu.area = 'results' 
where 
	sr.siteResourceStatusID = dbo.fn_getResourceStatusID('Active')
GO