use membercentral
GO

declare 
	@siteID int, 
	@orgID int, 
	@practiceAreaColumnID int, 
	@thisColumnID int, 
	@thisColumnName varchar	(255), 
	@valueID int, 
	@thisItem int,
	@memberID int,
	@columnName varchar	(255) 

set @columnName = 'Practice Areas' 

declare @memberData as table (thisItem int  IDENTITY(1,1), columnID int , columnName varchar	(255), valueID int, columnValueString varchar(255), memberID int)

declare @practiceAreas as table (valueID int, columnValue varchar	(255))

select @siteID = siteID, @orgID = orgID from sites where siteCode = 'SF'

select @practiceAreaColumnID = columnID from dbo.ams_memberDataColumns  mdc where orgID = @orgID and columnName = @columnName

select @practiceAreaColumnID 

-- Get existing custom fields
insert into @memberData (columnID, columnName, valueID, columnValueString, memberID)
select 
	mdc.columnID, mdc.columnName, mdcv.valueID, mdcv.columnValueString, md.memberID
from 
	dbo.ams_memberDataColumns  mdc 
	inner join dbo.ams_memberDataColumnValues mdcv on
		mdcv.columnID = mdc.columnID
	inner join dbo.ams_memberData md on
		md.valueID = mdcv.valueID
where 
	orgID = @orgID 
	and left(columnName,3) = 'PA_' 

 select * from @memberData

-- Populate Practice Areas custom field with new options

select @thisColumnID = min(columnID) from 	dbo.ams_memberDataColumns  mdc where orgID = @orgID and left(columnName,3) = 'PA_' 

while @thisColumnID is not null begin

	select @thisColumnName =  columnName  from ams_memberDataColumns where columnID = @thisColumnID
	print @thisColumnID
	print @thisColumnName

	exec ams_createMemberDataColumnValue @columnID = @practiceAreaColumnID , @columnValue = @thisColumnName, @valueID = @valueID output

	select @thisColumnID = min(columnID) from 	dbo.ams_memberDataColumns  mdc where orgID = @orgID and left(columnName,3) = 'PA_' and columnID > @thisColumnID
end

insert into @practiceAreas (valueID, columnValue)
select 
	mdcv.valueID, mdcv.columnValueString as columnValue
from
	ams_memberDataColumns mdc
	inner join ams_memberdatacolumnvalues mdcv
		on mdcv.columnID = mdc.columnID
		and mdc.columnName = @columnName
where 
	orgID = @orgID 

 select * from @practiceAreas

print '=========================================================================='

-- Migrate member settings from legacy custom fields to new custom field.

select @thisItem = min(thisItem) from @memberData 

while @thisItem is not null begin
	
	set @thisColumnName = null
	set @memberID = null 
	set @valueID = null

	select 
		@memberID = memberid,
		@thisColumnName = columnName
	from 
		@memberData 
	where 
		thisItem = @thisItem

	select 
		@valueID = valueID
	from 
		@practiceAreas 
	where	
		columnValue = @thisColumnName
	
	print @memberID
	print @thisColumnName
	print @valueID

	exec dbo.ams_saveMemberData @memberID=@memberID, @columnID=@practiceAreaColumnID, @columnvalueID=@valueID, @columnValue=null, @byPassQueue=1

	print '*********'

	select @thisItem = min(thisItem) from @memberData where thisItem > @thisItem

end

-- Remove prefix "PA_"
update
	mdcv
set
	mdcv.columnValueString = REPLACE(SUBSTRING(mdcv.columnValueString , CHARINDEX('_', mdcv.columnValueString ), LEN(mdcv.columnValueString )), '_', '')
from
	ams_memberDataColumns mdc
	inner join ams_memberdatacolumnvalues mdcv
		on mdcv.columnID = mdc.columnID
		and mdc.columnName = @columnName
where 
	orgID = @orgID 
GO


