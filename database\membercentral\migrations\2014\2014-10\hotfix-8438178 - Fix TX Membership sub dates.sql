declare @orgcode varchar(5), @siteCode varchar(5), @siteID int, @orgID int
declare @subscriberIDs TABLE (subscriberID int PRIMARY KEY, statusHistoryID int, oldStatusID int)
declare @subscribersToBackfill TABLE (subscriberID int PRIMARY KEY)
declare @thisSubscriberID int, @enteredByMemberID int
declare @typename varchar(100), @rateName varchar(100)

set @orgcode = 'TX'
set @siteCode  ='TX'
set @orgID = dbo.fn_getOrgIDfromOrgCode(@siteCode)
set @siteID  = dbo.fn_getSiteIDfromSiteCode(@siteCode)
select @enteredByMemberID = memberID from membercentral.dbo.ams_members where memberNumber = 'SYSTEM' and orgID = 1
set @typename = 'Membership Dues'
set @rateName = 'Complimentary Renewal'


-- backup all subscriber records for site in attempts to be prudent
select ss.*
into datatransfer.dbo.txSubscribers
from sub_types t
inner join sub_subscriptions subs
	on subs.typeID = t.typeID
	and t.siteID = @siteID
inner join sub_subscribers ss
	on ss.subscriptionID = subs.subscriptionID


-- insert into temp table all subscriberIDs that need to have there most recent status change reversed
insert into @subscriberIDs (subscriberID,statusHistoryID,oldStatusID)
select stree.subscriberID, sh.statusHistoryID, sh.oldStatusID
from sites s
inner join sub_types t on t.siteID =s.siteID
	and s.siteCode = @siteCode
	and t.typename = @typename
inner join sub_subscriptions sub on sub.typeID = t.typeID
inner join sub_subscribers ss on ss.subscriptionID = sub.subscriptionID
	and ss.parentSubscriberID is null
inner join sub_rateFrequencies rf on rf.rfid = ss.rfid
inner join sub_frequencies f on f.frequencyID = rf.frequencyID
	and f.frequencyName = 'Full'
inner join sub_rates as r on r.rateID = rf.rateID
	and r.rateName <> @rateName
inner join sub_subscribers stree on stree.rootSubscriberID = ss.subscriberID
inner join sub_statusHistory sh on sh.subscriberID = stree.subscriberID
	and sh.updateDate > '9/29/2014'
inner join sub_statuses st on st.statusID = sh.oldStatusID
	and st.statuscode in ('A','P')
order by ss.rootSubscriberID, sh.statusHistoryID desc



/* ************************** */
/* REVERSE LAST STATUS CHANGE */
/* ************************** */

-- update status to reverse the last status change
update ss 
set statusID = sh.oldStatusID
from @subscriberIDs temp
inner join sub_subscribers ss on ss.subscriberID = temp.subscriberID
inner join sub_statusHistory sh on sh.statusHistoryID = temp.statusHistoryID

-- delete last status change from history
delete sh
from @subscriberIDs temp
inner join sub_statusHistory sh on sh.statusHistoryID = temp.statusHistoryID



/* **************** */
/* CHANGE SUB DATES */
/* **************** */

-- update dates
update stree 
set substartdate = dateadd(month,1,stree.substartdate),
	subenddate = dateadd(ms,-3,dateadd(month,1,dateadd(millisecond,3,stree.subenddate))),
	graceenddate = dateadd(ms,-3,dateadd(month,2,dateadd(millisecond,3,stree.subenddate)))
from sites s
inner join sub_types t on t.siteID =s.siteID
	and s.siteCode = @sitecode
	and t.typename = @typename
inner join sub_subscriptions sub on sub.typeID = t.typeID
inner join sub_subscribers ss on ss.subscriptionID = sub.subscriptionID
	and ss.parentSubscriberID is null
--	and ss.substartdate < '9/30/2014'
inner join sub_rateFrequencies rf on rf.rfid = ss.rfid
inner join sub_frequencies f on f.frequencyID = rf.frequencyID
	and f.frequencyName = 'Full'
inner join sub_rates as r on r.rateID = rf.rateID
	and r.rateName <> @rateName
inner join sub_subscribers stree on stree.rootSubscriberID = ss.subscriberID
inner join vw_memberdata_TX m on stree.memberid = m.memberid 
	and ([Join Date] < '9/30/2014' OR [ReJoin Date] < '9/30/2014')


-- call backfill on new dates
insert into @subscribersToBackfill (subscriberID)
select stree.subscriberID
from sites s
inner join sub_types t on t.siteID =s.siteID
	and s.siteCode = @sitecode
	and t.typename = @typename
inner join sub_subscriptions sub on sub.typeID = t.typeID
inner join sub_subscribers ss on ss.subscriptionID = sub.subscriptionID
	and ss.parentSubscriberID is null
--	and ss.substartdate < '9/30/2014'
inner join sub_rateFrequencies rf on rf.rfid = ss.rfid
inner join sub_frequencies f on f.frequencyID = rf.frequencyID
	and f.frequencyName = 'Full'
inner join sub_rates as r on r.rateID = rf.rateID
	and r.rateName <> @rateName
inner join sub_subscribers stree on stree.rootSubscriberID = ss.subscriberID
inner join vw_memberdata_TX m on stree.memberid = m.memberid 
	and ([Join Date] < '9/30/2014' OR [ReJoin Date] < '9/30/2014')

select @thisSubscriberID = min(subscriberID) from @subscribersToBackfill
while @thisSubscriberID is not null BEGIN
	exec dbo.sub_createStatusBackfill @siteID=@siteID, @subscriberID=@thisSubscriberID, @isRenewal=0, @enteredByMemberID=@enteredByMemberID, @bypassQueue=1
	select @thisSubscriberID = min(subscriberID) from @subscribersToBackfill where subscriberID > @thisSubscriberID
END

-- call fix groups
exec dbo.sub_fixGroups @siteID=@siteID, @bypassQueue=1

-- insert into queue
declare @itemGroupUID uniqueidentifier
EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList='', @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT
GO