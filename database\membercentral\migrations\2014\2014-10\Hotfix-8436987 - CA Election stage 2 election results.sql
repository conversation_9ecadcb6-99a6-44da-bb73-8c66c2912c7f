--  File on TSSQL2 CAOC 14 Stage 2 Election Stats.sql
use [customApps]
DECLARE @year int, @orgID int
set @year = 2014;
set @orgID = 46;


-- total records (use to confirm queries below)
select * from CA_Elections_AtLarge WHERE YEAR(dateRecorded) = @year order by district, daterecorded

-- list of all voters
select m.lastname, m.firstname, isnull(m.middlename,'') as middleName, e.boardS, m.membernumber, e.daterecorded, district
from dbo.CA_Elections_AtLarge e
INNER join membercentral.dbo.ams_members m on m.memberID = e.memberID and m.orgid = @orgID
	and YEAR(e.dateRecorded) = @year
order by m.lastname, m.firstname


-- At Large Board of Governors - South
select distinct case 
	when ltrim(rtrim(listItem)) is null then 'No vote cast'
	when ltrim(rtrim(listItem)) = '' then 'No vote cast'
	when ltrim(rtrim(listItem)) is not null then ltrim(rtrim(listItem))
	else 'unknown'
	end as [At Large Board of Governors - South], count(voteid) as numVotes
from CA_Elections_AtLarge e
cross apply dbo.fn_ListToTable(boardS, ',') tbl
WHERE electionYear = @year
group by ltrim(rtrim(listItem))
order by numVotes desc

