declare @siteID int, @orgID int, @subscriptionID int

select @siteID = siteID, @orgID = orgID from sites where sitecode ='TX'

select @subscriptionID = subscriptionID from dbo.sub_subscriptions where uid = '3A5A2E8A-25EF-4E4F-BA72-B08DB4728A84'

update 
	graceEndDate = DATEADD(s,-1,DATEADD(mm, DATEDIFF(m,0,subEndDate)+2,0))
from dbo.sub_subscribers where subscriptionid = @subscriptionID
and (graceenddate - subenddate > 31 OR graceEndDate is null)
and statusid in (1,3,4,5)
