use membercentral
GO

ALTER PROC [dbo].[ev_removeRegistrant]
@registrantID int,
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @ApplicationTypeID int, @minTID int, @adjtransactionID int, 
		@assignedToMemberID int, @invoiceProfileID int, @invoiceID int, @registrationID int,
		@GLAccountID int
	declare @invoiceNumber varchar(18)
	DECLARE @adjAmount money
	declare @nowdate datetime
	DECLARE @tblAdjust TABLE (transactionID int, amountToAdjust money, creditGLAccountID int)
	DECLARE @tblInvoices TABLE (invoiceID int, invoiceProfileID int)

	select @nowdate = getdate()
	select @ApplicationTypeID = dbo.fn_getApplicationTypeIDFromName('Events')
	select @assignedToMemberID = m.activeMemberID, @registrationID = r.registrationID
		from dbo.ev_registrants as r
		inner join dbo.ams_members as m on m.memberID = r.memberID
		where r.registrantID = @registrantID

	-- put all open invoices used for this registration into table since they were already created and can be used for adjustments
	insert into @tblInvoices (invoiceID, invoiceProfileID)
	select distinct i.invoiceID, i.invoiceProfileID
	from dbo.fn_ev_registrantTransactions(@registrantID) as rt
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = rt.transactionID
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	where i.statusID = 1

	-- update registrant status
	UPDATE dbo.ev_registrants
	SET [status] = 'D'
	WHERE registrantID = @registrantID
	
	UPDATE dbo.tr_applications
	SET [status] = 'D'
	WHERE itemID = @registrantID
	AND itemType = 'Rate'
	AND applicationTypeID = @ApplicationTypeID

	-- update registrant details status
	UPDATE dbo.ev_registrantDetails
	SET [status] = 'D'
	WHERE registrantID = @registrantID

	UPDATE dbo.tr_applications
	SET [status] = 'D'
	WHERE itemID in (select detailID from dbo.ev_registrantDetails WHERE registrantID = @registrantID)
	AND itemType = 'Custom'
	AND applicationTypeID = @ApplicationTypeID

	-- get all registrant-related sales transactions we need to adjust
	INSERT INTO @tblAdjust (transactionID, amountToAdjust, creditGLAccountID)
	select rt.transactionID, tsFull.cache_amountAfterAdjustment, t.creditGLAccountID
	from dbo.fn_ev_registrantTransactions(@registrantID) as rt
	inner join dbo.tr_transactions as t on t.transactionID = rt.transactionID
	cross apply dbo.fn_tr_transactionSalesWithDIT(t.transactionID) as tsFull
	where tsFull.cache_amountAfterAdjustment > 0
	and rt.typeID = 1
	OPTION(RECOMPILE)

	-- if there are adjustments to make
	IF EXISTS (select transactionID from @tblAdjust) BEGIN
		SELECT @minTID = min(transactionID) from @tblAdjust
		WHILE @minTID IS NOT NULL BEGIN
			select @invoiceProfileID = null, @invoiceID = null, @adjAmount = null, @GLAccountID = null

			select @adjAmount = amountToAdjust*-1, @GLAccountID = creditGLAccountID from @tblAdjust where transactionID = @minTID
			select @invoiceProfileID = invoiceProfileID from dbo.tr_glAccounts where glAccountID = @GLAccountID
			select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID
			
			-- if necessary, create invoice assigned to registrant based on invoice profile
			IF @invoiceID is null BEGIN
				EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
					@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@nowdate, 
					@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT

				insert into @tblInvoices (invoiceID, invoiceProfileID)
				values (@invoiceID, @invoiceProfileID)
			END	

			EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@recordedOnSiteID,
				@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID,
				@status='Active', @amount=@adjAmount, @transactionDate=@nowdate,
				@saleTransactionID=@minTID, @invoiceID=@invoiceID, @xmlSchedule=null, 
				@transactionID=@adjtransactionID OUTPUT
			
			SELECT @minTID = min(transactionID) from @tblAdjust where transactionID > @minTID
		END
	END

	-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
	declare @itemGroupUID uniqueidentifier, @orgID int, @eventID int, @conditionIDList varchar(max)
	SELECT @orgID = orgID from dbo.ams_members where memberID = @assignedToMemberID
	SELECT @eventID = eventID from dbo.ev_registration where registrationID = @registrationID
	SELECT @conditionIDList = COALESCE(@conditionIDList + ',', '') + cast(c.conditionID as varchar(10)) 
		from dbo.ams_virtualGroupConditions as c
		where c.orgID = @orgID
		and c.fieldcode = 'e_' + cast(@eventID as varchar(10))
		group by c.conditionID
	IF @conditionIDList is not null
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@assignedToMemberID, @conditionIDList=@conditionIDList, @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[store_exportOrderTransactions]
@storeID int,
@productKW varchar(max),
@purchaser varchar(200),
@statusList varchar(20),
@dateStart datetime,
@dateEnd datetime,
@filename varchar(400)

AS

if @dateEnd is not null
	select @dateEnd = dateadd(ms,-3,dateadd(day,1,DATEADD(dd, DATEDIFF(dd,0,@dateEnd), 0)))

-- split statuses
declare @tblStatus TABLE (orderStatusID int)
IF len(@statusList) > 0 BEGIN
	INSERT INTO @tblStatus (orderStatusID)
	select distinct c.orderStatusID
	from dbo.fn_intListToTable(@statusList,',') as tmp
	inner join dbo.store_OrderStatus as c on c.orderStatusID = tmp.listitem
END ELSE BEGIN
	INSERT INTO @tblStatus (orderStatusID)
	select c.orderStatusID
	from dbo.store_OrderStatus as c
END

IF OBJECT_ID('tempdb..#tmpStoreOrders') IS NOT NULL 
	DROP TABLE #tmpStoreOrders
IF OBJECT_ID('tempdb..#tmpStoreExport') IS NOT NULL 
	DROP TABLE #tmpStoreExport


-- orders to export
SELECT distinct o.orderID, o.orderStatusID, o.dateOfOrder, o.memberID
into #tmpStoreOrders
FROM dbo.store_orders as o
INNER JOIN dbo.ams_members as m on m.memberid = o.memberID
INNER JOIN dbo.ams_members as m2 on m2.memberid = m.activememberid
INNER JOIN @tblStatus as stat on stat.orderStatusID = o.orderStatusID
inner join dbo.store_orderDetails as od on od.orderID = o.orderID
inner join dbo.store_Products as p on p.itemID = od.productItemID
cross apply dbo.fn_getContent(p.productContentID, 1) as pc
WHERE o.storeID = @storeID
AND o.orderCompleted = 1
AND (@purchaser is null OR m2.lastname + ', ' + m2.firstname + isnull(' ' + m2.middlename,'') + ' (' + m2.membernumber + ')' like '%' + @purchaser + '%')
AND (@dateStart is null OR o.DateOfOrder >= @dateStart)
AND (@dateEnd is null OR o.DateOfOrder <= @dateEnd)
AND (@productKW is null OR p.productID + pc.contentTitle like '%' + @productKW + '%')


SELECT sites.sitecode + right('0000' + cast(o.orderID as varchar(10)),4) as [Order Number], 
	s.statusName as [Order Status], 
	o.dateOfOrder as [Order Date], 
	m2.lastname + ', ' + m2.firstname + ' ' + isnull(m2.middlename,'') + ' (' + m2.membernumber + ')' as [Purchaser],
	ttl.totalFee as [Billed], 
	ttl.totalFee-ttl.feePaid as [Due], 
	p.productID as [Product ID], 
	sod.contentTitle as [Product], 
	spf.name as [Product Format],
	sod.quantity as [Item Quantity], 
	sod.ratePaid as [Item Amount],	
	ROW_NUMBER() OVER (order by o.dateOfOrder desc, o.orderID desc, sod.contentTitle) as row
INTO #tmpStoreExport
FROM #tmpStoreOrders as o
cross apply (
	select sum(tsFull.cache_amountAfterAdjustment) as totalFee, sum(tsFull.cache_activePaymentAllocatedAmount) as feePaid
	from dbo.fn_store_orderTransactions(o.orderid) as rt
	cross apply dbo.fn_tr_transactionSalesWithDIT(rt.transactionID) as tsFull
) as ttl
INNER JOIN dbo.store_orderDetails AS sod on sod.orderID = o.orderID
INNER JOIN dbo.store_ProductFormats AS spf ON spf.formatID = sod.formatID
INNER JOIN dbo.store_Products AS p ON sod.productItemID = p.ItemID
INNER JOIN dbo.store_OrderStatus s on s.orderStatusID = o.orderStatusID
INNER JOIN dbo.store on store.storeID = p.storeID
INNER JOIN dbo.sites on sites.siteID = store.siteID
INNER JOIN dbo.ams_members as m on m.memberid = o.memberID
INNER JOIN dbo.ams_members as m2 on m2.memberid = m.activememberid
ORDER BY row
OPTION(RECOMPILE)

DECLARE @fullsql varchar(max)
SELECT @fullsql = '
	select [Order Number], [Order Status], [Order Date], [Purchaser], [Billed], [Due], [Product ID], [Product], [Product Format],
		[Item Quantity], [Item Amount]
	from #tmpStoreExport 
	order by row
'
EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql

IF OBJECT_ID('tempdb..#tmpStoreOrders') IS NOT NULL 
	DROP TABLE #tmpStoreOrders
IF OBJECT_ID('tempdb..#tmpStoreExport') IS NOT NULL 
	DROP TABLE #tmpStoreExport

RETURN 0
GO

ALTER PROCEDURE [dbo].[sub_checkActivations] 
	@memberid int,
	@bypassQueue bit = 0
WITH RECOMPILE
AS

set nocount on

IF OBJECT_ID('tempdb..#firstInvoice') IS NOT NULL 
	DROP TABLE #firstInvoice
create table #firstInvoice (subscriberID int PRIMARY KEY, orgID int, invoiceNumber int)

declare @tblSubsToCheck TABLE (subscriberID int, rootSubscriberID int, memberID int, orgID int, siteID int, subActivationCode char(1), statusCode char(1))
declare @membersToCheck TABLE (id int IDENTITY(1,1), memberID int PRIMARY KEY)
declare @tblSubsToMove TABLE (subscriberID int, statusCode char(1), reason varchar(50))
declare @activatedStatusID int, @nonActivatedStatusID int, @enteredByMemberID int

select @activatedStatusID = statusID from dbo.sub_paymentStatuses where statusCode = 'P'
select @nonActivatedStatusID = statusID from dbo.sub_paymentStatuses where statusCode = 'N'
select @enteredByMemberID = memberID from ams_members where memberNumber = 'SYSTEM' and orgID = 1	

IF @memberID is null
BEGIN
	insert into @tblSubsToCheck (subscriberID, rootSubscriberID, memberID, orgID, siteID, subActivationCode, statusCode)
	select sub.subscriberID, sub.rootSubscriberID, m.activememberID as memberID, m.orgID, t.siteid, ao.subActivationCode, s.statusCode
	from sub_subscribers as sub
	inner join dbo.sub_statuses as s on s.statusID = sub.statusID
	inner join dbo.sub_activationOptions as ao on ao.subActivationID = sub.subActivationID
	inner join dbo.ams_members as m on m.memberid = sub.memberID
	inner join dbo.sub_subscriptions as ss on ss.subscriptionID = sub.subscriptionID
	inner join dbo.sub_Types as t on t.typeID = ss.typeID
	where sub.paymentStatusID = @nonActivatedStatusID
	and s.statusCode in ('A','P','I','E')
	and ao.subActivationCode in ('C','P','I','E','N','T') 
	order by sub.subscriberID
END
ELSE BEGIN
	insert into @membersToCheck (memberID)
	select m2.memberID
	from ams_members m
	inner join ams_members m2 on m.activeMemberID = m2.activeMemberID
		and m.memberID = @memberID

	insert into @tblSubsToCheck (subscriberID, rootSubscriberID, memberID, orgID, siteID, subActivationCode, statusCode)
	select sub.subscriberID, sub.rootSubscriberID, m.activememberID as memberID, m.orgID, t.siteid, ao.subActivationCode, s.statusCode
	from @membersToCheck mtc
	inner join sub_subscribers as sub on mtc.memberID = sub.memberID
	inner join dbo.sub_statuses as s on s.statusID = sub.statusID
	inner join dbo.sub_activationOptions as ao on ao.subActivationID = sub.subActivationID
	inner join dbo.ams_members as m on m.memberid = sub.memberID
	inner join dbo.sub_subscriptions as ss on ss.subscriptionID = sub.subscriptionID
	inner join dbo.sub_Types as t on t.typeID = ss.typeID
	where sub.paymentStatusID = @nonActivatedStatusID
	and s.statusCode in ('A','P','I','E')
	and ao.subActivationCode in ('C','P','I','E','N','T') 
	order by sub.subscriberID

END


-- N: No Reliance on payment
insert into @tblSubsToMove (subscriberID, statusCode, reason)
select tbl.subscriberID, tbl.statusCode, 'No reliance'
from @tblSubsToCheck as tbl
where tbl.subActivationCode = 'N'

-- P: This sub paid in full
insert into @tblSubsToMove (subscriberID, statusCode, reason)
select tbl.subscriberID, tbl.statusCode, 'this paid in full'
from @tblSubsToCheck as tbl
inner join dbo.tr_applications as tra on tra.itemID = tbl.subscriberID
	and tra.applicationTypeID = 17
	and tra.itemType = 'Dues'
	and tra.status = 'A'
cross apply dbo.fn_tr_transactionSalesWithDIT(tra.transactionID) as tsFull
where tbl.subActivationCode = 'P'
and tsFull.cache_amountAfterAdjustment = tsFull.cache_activePaymentAllocatedAmount
OPTION(RECOMPILE)

-- C: This sub and all children subs paid in full
insert into @tblSubsToMove (subscriberID, statusCode, reason)
select tbl.subscriberID, tbl.statusCode, 'this and children paid in full'
from @tblSubsToCheck as tbl
cross apply dbo.fn_getRecursiveMemberSubscriptions(tbl.memberid, tbl.siteid, tbl.subscriberID) as rms
inner join dbo.tr_applications as tra on tra.itemID = rms.subscriberID
	and tra.applicationTypeID = 17
	and tra.itemType = 'Dues'
	and tra.status = 'A'
cross apply dbo.fn_tr_transactionSalesWithDIT(tra.transactionID) as tsFull
where tbl.subActivationCode = 'C'
group by tbl.subscriberID, tbl.statusCode
having sum(tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount) = 0
OPTION(RECOMPILE)

-- I: First invoice this sub appears on is paid
insert into @tblSubsToMove (subscriberID, statusCode, reason)
select tmp.subscriberID, tmp.statusCode, 'First invoice this sub'
from (
	select tbl.subscriberID, tbl.statusCode, i.assignedToMemberID, tbl.orgID, min(i.invoiceNumber) as firstInvNumber
	from @tblSubsToCheck as tbl
	inner join dbo.tr_applications as tra on tra.itemID = tbl.subscriberID
		and tra.applicationTypeID = 17
		and tra.itemType = 'Dues'
		and tra.status = 'A'
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = tra.transactionID
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	where tbl.subActivationCode = 'I'
	group by tbl.subscriberID, tbl.statusCode, i.assignedToMemberID, tbl.orgID
) as tmp 
inner join dbo.tr_invoices as i2 on i2.invoiceNumber = tmp.firstInvNumber and i2.statusID = 4
inner join ams_members m on m.memberID = i2.assignedToMemberID
	and m.orgID = tmp.orgID

-- E: First invoice this entire sub appears on is paid
insert into #firstInvoice (subscriberID, orgID, invoiceNumber)
select s.subscriberID, tstc.orgID, min(i.invoiceNumber) as invoiceNumber
from @tblSubsToCheck tstc
inner join dbo.sub_subscribers s on s.rootSubscriberID = tstc.rootSubscriberID
	and tstc.subActivationCode = 'E'
inner join dbo.tr_applications as tra on tra.itemID = s.subscriberID
	and tra.applicationTypeID = 17
	and tra.itemType = 'Dues'
	and tra.status = 'A'
inner join dbo.tr_invoiceTransactions as it on it.transactionID = tra.transactionID
inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
group by s.rootSubscriberID, s.subscriberID, tstc.orgID

CREATE INDEX IX_invoiceNumber on #firstInvoice (invoiceNumber asc)

insert into @tblSubsToMove (subscriberID, statusCode, reason)
select distinct tmp2.subscriberID, ts.statusCode, 'First invoice this entire sub'
from #firstInvoice as tmp2
inner join dbo.tr_invoices as i2 on i2.invoiceNumber = tmp2.invoiceNumber
	and i2.statusID = 4
inner join @tblSubsToCheck ts on ts.subscriberID = tmp2.subscriberID and ts.subActivationCode = 'E'
inner join ams_members m on m.memberID = i2.assignedToMemberID
	and m.orgID = tmp2.orgID

-- T: First Payment on This Sub
insert into @tblSubsToMove (subscriberID, statusCode, reason)
select tbl.subscriberID, tbl.statusCode, 'First payment this sub'
from @tblSubsToCheck as tbl
inner join dbo.tr_applications as tra on tra.itemID = tbl.subscriberID
	and tra.applicationTypeID = 17
	and tra.itemType = 'Dues'
	and tra.status = 'A'
cross apply dbo.fn_tr_transactionSalesWithDIT(tra.transactionID) as tsFull
where tbl.subActivationCode = 'T'
and ( tsFull.cache_activePaymentAllocatedAmount > 0 or tsFull.cache_amountAfterAdjustment = 0)
OPTION(RECOMPILE)

-- get the follow parents
insert into @tblSubsToMove (subscriberID, statusCode, reason)
select s.subscriberID, st.statusCode, 'follow parents'
from sub_subscribers s
inner join dbo.sub_statuses st
	on st.statusID = s.statusID and st.statusCode <> 'D'
	and s.paymentStatusID = @nonActivatedStatusID
inner join sub_activationOptions a on a.subActivationID = s.subActivationID and a.subActivationCode = 'F'
left outer join sub_subscribers pS on ps.subscriberID = s.parentSubscriberID and ps.paymentStatusID = @activatedStatusID
where NOT EXISTS (select subscriberID from @tblSubsToMove where subscriberID = s.subscriberID)
		AND (EXISTS (select subscriberID from @tblSubsToMove where subscriberID = s.parentSubscriberID) OR pS.subscriberID is not null)

while @@RowCount > 0
begin
	insert into @tblSubsToMove (subscriberID, statusCode, reason)
	select s.subscriberID, st.statusCode, 'follow parents'
	from sub_subscribers s
	inner join dbo.sub_statuses st
		on st.statusID = s.statusID and st.statusCode <> 'D'
		and s.paymentStatusID = @nonActivatedStatusID
	inner join sub_activationOptions a on a.subActivationID = s.subActivationID and a.subActivationCode = 'F'
	left outer join sub_subscribers pS on ps.subscriberID = s.parentSubscriberID and ps.paymentStatusID = @activatedStatusID
	where NOT EXISTS (select subscriberID from @tblSubsToMove where subscriberID = s.subscriberID)
		AND (EXISTS (select subscriberID from @tblSubsToMove where subscriberID = s.parentSubscriberID) OR pS.subscriberID is not null)
end


update subs
set subs.paymentStatusID = @activatedStatusID
from dbo.sub_subscribers subs
inner join @tblSubsToMove ts on ts.subscriberID = subs.subscriberID

insert into dbo.sub_paymentStatusHistory(subscriberID, paymentStatusID, enteredByMemberID)
select subscriberID, @activatedStatusID, @enteredByMemberID
from @tblSubsToMove

declare @minSubscriberID int, @currLoopMemberID int, @currLoopSubscriptionID int, @currLoopStatusCode char(1), @currLoopGroupID int, @tempCount int
declare @prevSubscriberID int, @currLoopSiteID int, @insideResult int

select @minSubscriberID=min(subscriberID)
from @tblSubsToMove
where statusCode in ('A','P')


while @minSubscriberID is not null
begin

	select @currLoopSubscriptionID=s.subscriptionID, @currLoopMemberID=m.activeMemberID, @currLoopStatusCode=t.statusCode, @currLoopSiteID=st.siteID
	from dbo.sub_subscribers s
	inner join @tblSubsToMove t on t.subscriberID = s.subscriberID
	inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID
	inner join dbo.sub_types st on st.typeID = subs.typeID
	inner join dbo.ams_members m on m.memberID = s.memberID
	where s.subscriberID = @minSubScriberID
	

	select @tempCount=count(s.subscriberID)
	from dbo.sub_subscribers s
	inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID AND pst.statusCode = 'N'
	inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @currLoopMemberID
	where s.subscriptionID = @currLoopSubscriptionID
	
	IF @tempCount = 0
	begin
		select @currLoopGroupID=null
		select @currLoopGroupID=groupID
		from ams_groups
		where groupCode like 'subWaitingPay_' + convert(varchar, @currLoopSubscriptionID) + '_tracking'

		IF @currLoopGroupID is not null BEGIN
			exec dbo.ams_deleteMemberGroup @currLoopMemberID, @currLoopGroupID
		END
	end

	IF @currLoopStatusCode = 'A'
	begin

		select @prevSubscriberID=null
		select @prevSubscriberID=s.subscriberID
		from dbo.sub_subscribers s
		inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode in ('A', 'I')
		inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID 
		inner join dbo.sub_types t on t.typeID = subs.typeID and t.siteID = @currLoopSiteID
		inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @currLoopMemberID											
		where s.subscriptionID = @currLoopSubscriptionID
		and s.subscriberID <> @minSubscriberID

		IF @prevSubscriberID is not null
		BEGIN

			EXEC sub_updateSubscriberStatus @prevSubscriberID, 'E', @currLoopSiteID, @enteredByMemberID, @insideResult OUTPUT

		END

		select @currLoopGroupID=null
		select @currLoopGroupID=groupID
		from ams_groups
		where groupCode like 'subActive_' + convert(varchar, @currLoopSubscriptionID) + '_tracking'

		IF @currLoopGroupID is not null
			exec dbo.ams_createMemberGroup @currLoopMemberID, @currLoopGroupID, @bypassQueue
	end
	ELSE IF @currLoopStatusCode = 'P'
	begin
		select @currLoopGroupID=null
		select @currLoopGroupID=groupID
		from ams_groups
		where groupCode like 'subPending_' + convert(varchar, @currLoopSubscriptionID) + '_tracking'

		IF @currLoopGroupID is not null
			exec dbo.ams_createMemberGroup @currLoopMemberID, @currLoopGroupID, @bypassQueue
	end

	select @minSubscriberID=min(subscriberID)
	from @tblSubsToMove
	where statusCode in ('A','P') and subscriberID > @minSubscriberID
end

IF OBJECT_ID('tempdb..#firstInvoice') IS NOT NULL 
	DROP TABLE #firstInvoice

set nocount off

GO

ALTER PROC [dbo].[sub_expireSubscriber]
@subscriberID int,
@memberID int,
@siteID int,
@enteredByMemberID int,
@statsSessionID int,
@AROption char(1),
@fReturnQuery bit=1

AS

set nocount on

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @ApplicationTypeID int, @nowdate datetime, @minSubID int, @updateResult int, @invoiceID int,
		@invoiceNumber varchar(18), @minTID int, @adjAmount money, @adjtransactionID int,
		@invoiceProfileID int
	DECLARE @tblAdjust TABLE (transactionID int, amountToAdjust money)
	select @ApplicationTypeID = dbo.fn_getApplicationTypeIDFromName('Admin')
	select @nowdate = getdate()

	declare @tblSubs TABLE (subscriberID int, thePathExpanded varchar(max))
	insert into @tblSubs (subscriberID, thePathExpanded)
	select subscriberID, thePathExpanded
	from dbo.fn_getRecursiveMemberSubscriptions(@memberID,@siteID,@subscriberID)
	where status not in ('D','E')

	select @minSubID = min(subscriberID) from @tblSubs
	while @minSubID is not null BEGIN
		EXEC dbo.sub_updateSubscriberStatus @subscriberID=@minSubID, @newStatusCode='E', @siteID=@siteID, 
			@enteredByMemberID=@enteredByMemberID, @result=@updateResult OUTPUT
			IF @updateResult < 0 RAISERROR('sub_updateSubscriberStatus returned %i', 16, 1,@updateResult);

		IF @AROption = 'A'
			INSERT INTO @tblAdjust (transactionID, amountToAdjust)
			select rt.mainTransactionID, tsFull.cache_amountAfterAdjustment
			from dbo.fn_sub_subscriberTransactions(@minSubID) as rt
			cross apply dbo.fn_tr_transactionSalesWithDIT(rt.transactionID) as tsFull
			where rt.typeID = 1
			and tsFull.cache_amountAfterAdjustment > 0
			OPTION(RECOMPILE)
		IF @AROption = 'B'
			INSERT INTO @tblAdjust (transactionID, amountToAdjust)
			select rt.mainTransactionID, tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount-tsFull.cache_pendingPaymentAllocatedAmount
			from dbo.fn_sub_subscriberTransactions(@minSubID) as rt
			cross apply dbo.fn_tr_transactionSalesWithDIT(rt.transactionID) as tsFull
			where rt.typeID = 1
			and tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount-tsFull.cache_pendingPaymentAllocatedAmount > 0
			OPTION(RECOMPILE)

		UPDATE dbo.tr_applications
		SET [status] = 'D'
		WHERE itemID = @minSubID
		AND itemType = 'Dues'
		AND applicationTypeID = @ApplicationTypeID
		AND [status] <> 'D'

		select @minSubID = min(subscriberID) from @tblSubs where subscriberID > @minSubID
	END

	-- if there are adjustments to make
	IF EXISTS (select transactionID from @tblAdjust) BEGIN

		-- if any transactions are on a open or pending invoice, grab it for all adjustments. 
		-- otherwise, we need to create one to hold these adjustments
		select top 1 @invoiceID = i.invoiceID 
			from dbo.tr_invoices as i
			inner join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
			inner join @tblAdjust as tbl on tbl.transactionID = it.transactionID
			and statusID in (1,2)
			order by i.invoiceID
		IF @invoiceID is null BEGIN
			select top 1 @invoiceProfileID=i.invoiceProfileID
				from dbo.tr_invoices as i
				inner join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
				inner join @tblAdjust as tbl on tbl.transactionID = it.transactionID
			EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@enteredByMemberID,
				@assignedToMemberID=@memberID, @dateBilled=@nowdate, @dateDue=@nowdate, 
				@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT
		END

		-- record adjustments
		SELECT @minTID = min(transactionID) from @tblAdjust
		WHILE @minTID IS NOT NULL BEGIN
			SELECT @adjAmount = amountToAdjust*-1 from @tblAdjust where transactionID = @minTID
			
			EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteID,
				@recordedByMemberID=@enteredByMemberID, @statsSessionID=@statsSessionID,
				@status='Active', @amount=@adjAmount, @transactionDate=@nowdate,
				@saleTransactionID=@minTID, @invoiceID=@invoiceID, @xmlSchedule=null,
				@transactionID=@adjtransactionID OUTPUT
			
			SELECT @minTID = min(transactionID) from @tblAdjust where transactionID > @minTID
		END

		EXEC dbo.tr_closeInvoice @enteredByMemberID=@enteredByMemberID, @invoiceIDList=@invoiceID			

	END

	IF @fReturnQuery = 1
		select * from @tblSubs


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[sub_removeSubscriber]
@subscriberID int,
@memberID int,
@siteID int,
@enteredByMemberID int,
@statsSessionID int,
@AROption char(1)

AS

set nocount on

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @ApplicationTypeID int, @nowdate datetime, @minSubID int, @updateResult int, @invoiceID int,
		@invoiceNumber varchar(18), @minTID int, @adjAmount money, @adjtransactionID int,
		@autoCloseInvoice bit, @invoiceProfileID int
	DECLARE @tblAdjust TABLE (transactionID int, amountToAdjust money)
	set @autoCloseInvoice = 0

	select @ApplicationTypeID = dbo.fn_getApplicationTypeIDFromName('Admin')
	select @nowdate = getdate()

	declare @tblSubs TABLE (subscriberID int, thePathExpanded varchar(max))
	insert into @tblSubs (subscriberID, thePathExpanded)
	select subscriberID, thePathExpanded
	from dbo.fn_getRecursiveMemberSubscriptions(@memberID,@siteID,@subscriberID)
	where status <> 'D'

	select @minSubID = min(subscriberID) from @tblSubs
	while @minSubID is not null BEGIN
		EXEC dbo.sub_updateSubscriberStatus @subscriberID=@minSubID, @newStatusCode='D', @siteID=@siteID, 
			@enteredByMemberID=@enteredByMemberID, @result=@updateResult OUTPUT
			IF @updateResult < 0 RAISERROR('sub_updateSubscriberStatus returned %i', 16, 1,@updateResult);

		IF @AROption = 'A'
			INSERT INTO @tblAdjust (transactionID, amountToAdjust)
			select rt.mainTransactionID, tsFull.cache_amountAfterAdjustment
			from dbo.fn_sub_subscriberTransactions(@minSubID) as rt
			cross apply dbo.fn_tr_transactionSalesWithDIT(rt.transactionID) as tsFull
			where rt.typeID = 1
			and tsFull.cache_amountAfterAdjustment > 0
			OPTION(RECOMPILE)
		IF @AROption = 'B'
			INSERT INTO @tblAdjust (transactionID, amountToAdjust)
			select rt.mainTransactionID, tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount-tsFull.cache_pendingPaymentAllocatedAmount
			from dbo.fn_sub_subscriberTransactions(@minSubID) as rt
			cross apply dbo.fn_tr_transactionSalesWithDIT(rt.transactionID) as tsFull
			where rt.typeID = 1
			and tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount-tsFull.cache_pendingPaymentAllocatedAmount > 0
			OPTION(RECOMPILE)

		UPDATE dbo.tr_applications
		SET [status] = 'D'
		WHERE itemID = @minSubID
		AND itemType = 'Dues'
		AND applicationTypeID = @ApplicationTypeID
		AND [status] <> 'D'

		select @minSubID = min(subscriberID) from @tblSubs where subscriberID > @minSubID
	END

	-- if there are adjustments to make
	IF EXISTS (select transactionID from @tblAdjust) BEGIN

		-- if any transactions are on a open or pending invoice, grab it for all adjustments. 
		-- otherwise, we need to create one to hold these adjustments
		select top 1 @invoiceID = i.invoiceID 
			from dbo.tr_invoices as i
			inner join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
			inner join @tblAdjust as tbl on tbl.transactionID = it.transactionID
			and statusID in (1,2)
			order by i.invoiceID
		IF @invoiceID is null BEGIN
			select top 1 @invoiceProfileID=i.invoiceProfileID
				from dbo.tr_invoices as i
				inner join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
				inner join @tblAdjust as tbl on tbl.transactionID = it.transactionID
			EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@enteredByMemberID,
				@assignedToMemberID=@memberID, @dateBilled=@nowdate, @dateDue=@nowdate, 
				@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT
			set @autoCloseInvoice = 1
		END

		-- record adjustments
		SELECT @minTID = min(transactionID) from @tblAdjust
		WHILE @minTID IS NOT NULL BEGIN
			SELECT @adjAmount = amountToAdjust*-1 from @tblAdjust where transactionID = @minTID
			
			EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteID,
				@recordedByMemberID=@enteredByMemberID, @statsSessionID=@statsSessionID,
				@status='Active', @amount=@adjAmount, @transactionDate=@nowdate,
				@saleTransactionID=@minTID, @invoiceID=@invoiceID, @xmlSchedule=null,
				@transactionID=@adjtransactionID OUTPUT
			
			SELECT @minTID = min(transactionID) from @tblAdjust where transactionID > @minTID
		END

		IF @autoCloseInvoice = 1
			EXEC dbo.tr_closeInvoice @enteredByMemberID=@enteredByMemberID, @invoiceIDList=@invoiceID			
	END

	-- return query of subs
	select * from @tblSubs


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[tr_viewTransaction_payment]
@transactionID int

AS

set nocount on

declare @orgID int
select @orgID = ownedByOrgID from dbo.tr_transactions where transactionID = @transactionID

declare @allGLs TABLE (GLAccountID int, thePathExpanded varchar(max), accountCode varchar(200), accountType varchar(30), GLCode varchar(30), thePath varchar(max))
insert into @allGLS
select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode, rgl.accountType, rgl.GLCode, rgl.thePath
from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl


-- transaction info
select TOP 1 t.transactionid, t.ownedByOrgID, t.recordedOnSiteID, tt.type, t.detail, t.amount, 
	t.transactionDate, t.dateRecorded, ts.status, mAss2.memberID as assignedTomemberID, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullif(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as recordedByMember,
	m2.company as recordedByMemberCompany
from dbo.tr_transactions as t
inner join dbo.tr_types as tt on tt.typeID = t.typeID
inner join dbo.tr_statuses as ts on ts.statusID = t.statusID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join dbo.ams_members as m on m.memberid = t.recordedByMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
where t.transactionID = @transactionID


-- payment info
select top 1 tp.paymentID, tp.profileID, mp.profileName, tp.historyID, tp.cache_allocatedAmountOfPayment as allocatedAmount, 
	tp.cache_refundableAmountOfPayment-tp.cache_allocatedAmountOfPayment as unallocatedAmount,
	b.batchID, b.batchName, b.depositDate, bs.status, ph.datePaid, ph.gatewayTransactionID, ph.gatewayApprovalCode, 
	ph.paymentInfo, ph.gatewayResponse,
	btn_canRefund = 
		case 
		when t.statusID = 1 and (
								((tp.cache_refundableAmountOfPayment >= tp.cache_refundableAmountOfPayment-tp.cache_allocatedAmountOfPayment) and (tp.cache_refundableAmountOfPayment-tp.cache_allocatedAmountOfPayment > 0))
								OR 
								((tp.cache_refundableAmountOfPayment < tp.cache_refundableAmountOfPayment-tp.cache_allocatedAmountOfPayment) and (tp.cache_refundableAmountOfPayment > 0))
								) then 1 
		else 0 
		end,
	btn_canEdit = case when t.statusID = 3 then 1 else 0 end,
	btn_canWriteOff =
		case 
		when t.statusID = 1 and (
								((tp.cache_refundableAmountOfPayment >= tp.cache_refundableAmountOfPayment-tp.cache_allocatedAmountOfPayment) and (tp.cache_refundableAmountOfPayment-tp.cache_allocatedAmountOfPayment > 0))
								or
								((tp.cache_refundableAmountOfPayment < tp.cache_refundableAmountOfPayment-tp.cache_allocatedAmountOfPayment) and (tp.cache_refundableAmountOfPayment > 0))
								) then 1
		else 0 
		end,
	btn_canNSF =
		case 
		when t.statusID = 1 and not exists(
								select trNSF.relationshipID
								from dbo.tr_relationships as trNSF
								inner join dbo.tr_relationshiptypes as trtNSF on trtNSF.typeID = trNSF.typeID and trtNSF.type = 'NSFTrans'
								inner join dbo.tr_transactions as tNSF on tNSF.transactionID = trNSF.transactionID and tNSF.statusID = 1
								where trNSF.appliedToTransactionID = t.transactionID
								) then 1
		else 0 
		end,
	btn_canVoid = case when t.statusID in (2,3) then 0 else 1 end
from dbo.tr_transactionPayments as tp
inner join dbo.tr_transactions as t on t.transactionID = tp.transactionID
inner join dbo.mp_profiles as mp on mp.profileID = tp.profileID
inner join dbo.tr_paymentHistory as ph on ph.historyID = tp.historyID
inner join dbo.tr_batchTransactions as bt on bt.transactionID = tp.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID
inner join dbo.tr_batchStatuses as bs on bs.statusID = b.statusID
where t.transactionID = @transactionID


-- current allocations (show rolled up to sales -- tax/adj/dit included)
select tmp.transactionID, sum(tmp.allocAmount) as allocAmount, isnull(tax.cache_amountAfterAdjustment,0)+tsFull.cache_amountAfterAdjustment as amount, tOuter.detail, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	glCred.thePathExpanded + isnull(' (' + nullIf(glCred.accountCode,'') + ')','') as creditGL
from (
	select 
		case 
		when t.typeID = 1 then t.transactionID 
		when t.typeID = 7 then rTax.AppliedToTransactionID 
		when t.typeID = 3 and tAdjee.typeID = 1 then tAdjee.transactionID 
		when t.typeID = 3 and tAdjee.typeID = 7 then rAdjTax.AppliedToTransactionID 
		when t.typeID = 10 and tDit.typeID = 1 then tDit.transactionID
		when t.typeID = 10 and tDit.typeID = 7 then rDitTax.AppliedToTransactionID
		when t.typeID = 10 and tDit.typeID = 3 and tDitAdjee.typeID = 1 then tDitAdjee.transactionID
		when t.typeID = 10 and tDit.typeID = 3 and tDitAdjee.typeID = 7 then rDitAdjTax.AppliedToTransactionID
		else null end as transactionID,
		atop.allocAmount, t.amount, atop.allocDate
	from dbo.fn_tr_getAllocatedTransactionsofPayment(@transactionID) as atop
	inner join dbo.tr_transactions as t on t.transactionID = atop.transactionID
	left outer join dbo.tr_relationships as rAdj 
		inner join dbo.tr_relationshipTypes as rtAdj on rtAdj.typeID = rAdj.typeID and rtAdj.type = 'AdjustTrans'
		inner join dbo.tr_transactions as tAdjee on tAdjee.transactionID = rAdj.AppliedToTransactionID
		left outer join dbo.tr_relationships as rAdjTax
			inner join dbo.tr_relationshipTypes as rtAdjTax on rtAdjTax.typeID = rAdjTax.typeID and rtAdjTax.type = 'SalesTaxTrans'
			on rAdjTax.transactionID = tAdjee.transactionID
		on rAdj.transactionID = t.transactionID
	left outer join dbo.tr_relationships as rTax
		inner join dbo.tr_relationshipTypes as rtTax on rtTax.typeID = rTax.typeID and rtTax.type = 'SalesTaxTrans'
		on rTax.transactionID = t.transactionID
	left outer join dbo.tr_relationships as rDit
		inner join dbo.tr_relationshipTypes as rtDit on rtDit.typeID = rDit.typeID and rtDit.type = 'DITSaleTrans'
		inner join dbo.tr_transactions as tDit on tDit.transactionID = rDit.AppliedToTransactionID
		left outer join dbo.tr_relationships as rDitTax
			inner join dbo.tr_relationshipTypes as rtDitTax on rtDitTax.typeID = rDitTax.typeID and rtDitTax.type = 'SalesTaxTrans'
			on rDitTax.transactionID = tDit.transactionID
		left outer join dbo.tr_relationships as rDitAdj
			inner join dbo.tr_relationshipTypes as rtDitAdj on rtDitAdj.typeID = rDitAdj.typeID and rtDitAdj.type = 'AdjustTrans'
			inner join dbo.tr_transactions as tDitAdjee on tDitAdjee.transactionID = rDitAdj.AppliedToTransactionID
			left outer join dbo.tr_relationships as rDitAdjTax
				inner join dbo.tr_relationshipTypes as rtDitAdjTax on rtDitAdjTax.typeID = rDitAdjTax.typeID and rtDitAdjTax.type = 'SalesTaxTrans'
				on rDitAdjTax.transactionID = tDitAdjee.transactionID
			on rDitAdj.transactionID = tDit.transactionID
		on rDit.transactionID = t.transactionID
) as tmp
inner join dbo.tr_transactions as tOuter on tOuter.transactionID = tmp.transactionID
cross apply dbo.fn_tr_transactionSalesWithDIT(tOuter.transactionID) as tsFull
outer apply (
	select sum(tsTaxFull.cache_amountAfterAdjustment) as cache_amountAfterAdjustment, 
		   sum(tsTaxFull.cache_activePaymentAllocatedAmount) as cache_activePaymentAllocatedAmount, 
		   sum(tsTaxFull.cache_pendingPaymentAllocatedAmount) as cache_pendingPaymentAllocatedAmount
	from dbo.tr_relationships as tr WITH(NOLOCK)
	inner join dbo.tr_relationshiptypes as trt WITH(NOLOCK) on trt.typeID = tr.typeID and trt.type = 'SalesTaxTrans'
	inner join dbo.tr_transactions as tTax WITH(NOLOCK) on tTax.transactionID = tr.transactionID and tTax.statusID = 1
	cross apply dbo.fn_tr_transactionSalesWithDIT(tTax.transactionID) as tsTaxFull
	where tr.appliedToTransactionID = tOuter.transactionID
	) as tax
inner join dbo.ams_members as mAss on mAss.memberid = tOuter.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glCred on glCred.GLAccountID = tOuter.creditGLAccountID
group by tmp.transactionID, isnull(tax.cache_amountAfterAdjustment,0)+tsFull.cache_amountAfterAdjustment, tOuter.detail, mAss2.firstname, mAss2.middlename, mAss2.lastname, mAss2.suffix, mAss2.membernumber, mAss2.company, 
	glCred.thePathExpanded, glCred.accountCode
order by max(tmp.allocDate) desc
OPTION(RECOMPILE)

-- current gl spread
declare @tblTrans TABLE (transactionID int, glAccountID int, debitAmount money, creditAmount money)

insert into @tblTrans
select transactionID, debitglAccountID, amount, 0
from dbo.tr_transactions
WHERE transactionID = @transactionID
and statusID = 1
	union all
select transactionID, creditglAccountID, 0, amount
from dbo.tr_transactions
WHERE transactionID = @transactionID
and statusID = 1

select gl.thePathExpanded + isnull(' (' + nullIf(gl.accountCode,'') + ')','') as glexpanded,
	case 
	when gl.accountType = 'Cash' and sd.debitAmount - sd.creditAmount >= 0 then sd.debitAmount - sd.creditAmount
	when gl.accountType = 'Asset' and gl.GLCode = 'ACCOUNTSRECEIVABLE' and sd.debitAmount - sd.creditAmount > 0 then sd.debitAmount - sd.creditAmount
	when gl.accountType = 'Liability' and gl.GLCode = 'DEPOSITS' and sd.creditAmount - sd.debitAmount <= 0 then abs(sd.creditAmount - sd.debitAmount)
	when gl.accountType = 'Revenue' and sd.creditAmount - sd.debitAmount < 0 then abs(sd.creditAmount - sd.debitAmount)
	when gl.accountType = 'Expense' and sd.debitAmount - sd.creditAmount >= 0 then sd.debitAmount - sd.creditAmount
	when gl.accountType = 'Liability' and sd.creditAmount - sd.debitAmount <= 0 then abs(sd.creditAmount - sd.debitAmount)
	else null
	end as debits,
	case 
	when gl.accountType = 'Cash' and sd.debitAmount - sd.creditAmount < 0 then abs(sd.debitAmount - sd.creditAmount)
	when gl.accountType = 'Asset' and gl.GLCode = 'ACCOUNTSRECEIVABLE' and sd.debitAmount - sd.creditAmount <= 0 then abs(sd.debitAmount - sd.creditAmount)
	when gl.accountType = 'Liability' and gl.GLCode = 'DEPOSITS' and sd.creditAmount - sd.debitAmount > 0 then sd.creditAmount - sd.debitAmount
	when gl.accountType = 'Revenue' and sd.creditAmount - sd.debitAmount >= 0 then sd.creditAmount - sd.debitAmount
	when gl.accountType = 'Expense' and sd.debitAmount - sd.creditAmount < 0 then abs(sd.debitAmount - sd.creditAmount)
	when gl.accountType = 'Liability' and sd.creditAmount - sd.debitAmount > 0 then sd.creditAmount - sd.debitAmount
	else null
	end as credits
from (
	select glAccountID, sum(debitAmount) as debitAmount, sum(creditAmount) as creditAmount
	from @tblTrans
	group by glAccountID
	having (sum(debitAmount) > 0 OR sum(creditAmount) > 0) and sum(debitAmount) <> sum(creditAmount)
) as sd
inner join @allGLS as gl on gl.GLAccountID = sd.GLAccountID
order by gl.thePath


RETURN 0
GO

ALTER PROC [dbo].[tr_viewTransaction_sale]
@transactionID int

AS

set nocount on

declare @orgID int
select @orgID = ownedByOrgID from dbo.tr_transactions where transactionID = @transactionID

declare @allGLs TABLE (GLAccountID int, thePathExpanded varchar(max), accountCode varchar(200), accountType varchar(30), GLCode varchar(30), thePath varchar(max))
insert into @allGLS
select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode, rgl.accountType, rgl.GLCode, rgl.thePath
from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl

-- transaction info
select TOP 1 t.transactionid, t.ownedByOrgID, t.recordedOnSiteID, tt.type, t.detail, t.amount, 
	t.transactionDate, t.dateRecorded, ts.status, mAss2.memberID as assignedTomemberID, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullif(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as recordedByMember,
	m2.company as recordedByMemberCompany
from dbo.tr_transactions as t
inner join dbo.tr_types as tt on tt.typeID = t.typeID
inner join dbo.tr_statuses as ts on ts.statusID = t.statusID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join dbo.ams_members as m on m.memberid = t.recordedByMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
where t.transactionID = @transactionID

-- sale info
select top 1 ts.saleID, 
	tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount-tsFull.cache_pendingPaymentAllocatedAmount as unAllocatedAmount,
	tsFull.cache_activePaymentAllocatedAmount+tsFull.cache_pendingPaymentAllocatedAmount as allocatedAmount,
	paymentDueAmount =
		case 
		when t.statusID = 1 then (isnull(tax.cache_amountAfterAdjustment,0)+tsFull.cache_amountAfterAdjustment) - (isnull(tax.cache_activePaymentAllocatedAmount,0)+tsFull.cache_activePaymentAllocatedAmount) - (isnull(tax.cache_pendingPaymentAllocatedAmount,0)+tsFull.cache_pendingPaymentAllocatedAmount)
		else 0
		end,
	btn_canApplyPayment = 
		case 
		when t.statusID = 1 and (isnull(tax.cache_amountAfterAdjustment,0)+tsFull.cache_amountAfterAdjustment) - (isnull(tax.cache_activePaymentAllocatedAmount,0)+tsFull.cache_activePaymentAllocatedAmount) > 0 then 1 
		else 0
		end,
	btn_canWriteOff =
		case 
		when t.statusID = 1 and tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount > 0 then 1 
		else 0 
		end,
	btn_canAdjust = case when t.statusID = 1 then 1 else 0 end,
	btn_canVoid = case when t.statusID = 1 then 1 else 0 end,
	hasAdjustments = case when exists (
		select adj.transactionID
		from dbo.tr_transactions as adj
		inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
		where adj.statusID = 1
		) then 1 else 0 end,
	hasSchedule = case when exists (
		select dit.transactionID
		from dbo.tr_transactions as dit
		inner join dbo.tr_relationships as rInner on rInner.transactionID = dit.transactionID and rInner.appliedToTransactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'DITSaleTrans'
		inner join dbo.tr_transactionDIT as tdit on tdit.transactionID = dit.transactionID
		where dit.statusID = 1
		and tdit.isActive = 1
		) then 1 else 0 end
from dbo.tr_transactionSales as ts
inner join dbo.tr_transactions as t on t.transactionID = ts.transactionID
cross apply dbo.fn_tr_transactionSalesWithDIT(t.transactionID) as tsFull
outer apply (
	select sum(tsTaxFull.cache_amountAfterAdjustment) as cache_amountAfterAdjustment, 
		   sum(tsTaxFull.cache_activePaymentAllocatedAmount) as cache_activePaymentAllocatedAmount, 
		   sum(tsTaxFull.cache_pendingPaymentAllocatedAmount) as cache_pendingPaymentAllocatedAmount
	from dbo.tr_relationships as tr WITH(NOLOCK)
	inner join dbo.tr_relationshiptypes as trt WITH(NOLOCK) on trt.typeID = tr.typeID and trt.type = 'SalesTaxTrans'
	inner join dbo.tr_transactions as tTax WITH(NOLOCK) on tTax.transactionID = tr.transactionID and tTax.statusID = 1
	cross apply dbo.fn_tr_transactionSalesWithDIT(tTax.transactionID) as tsTaxFull
	where tr.appliedToTransactionID = t.transactionID
	) as tax
where t.transactionID = @transactionID
OPTION(RECOMPILE)

-- invoices
select distinct i.invoiceID, ins.status, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, i.dateDue, i.invoiceProfileID, ip.profileName, i.invoiceCode
from dbo.tr_transactions as t
inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
inner join dbo.organizations as o on o.orgID = @orgID
where t.transactionID in (
	select @transactionID as transactionID
	union
	select adj.transactionID
	from dbo.tr_transactions as adj
	inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
	inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
)
order by i.dateDue desc, 3 desc


-- current allocations (also include writeoffs)
select t.transactionID, t.typeID, apos.allocatedAmount as allocAmount, t.amount, t.detail, t.transactionDate, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL
from dbo.fn_tr_getAllocatedPaymentsofSale(@transactionID) as apos
inner join dbo.tr_transactions as t on t.transactionID = apos.paymentTransactionID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glDeb on glDeb.GLAccountID = t.debitGLAccountID
	union all
select tWO.transactionID, tWO.typeID, tWO.amount, tWO.amount, 'WriteOff of ' + tWO.detail, tWO.transactionDate, null, null, 
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL
from dbo.tr_transactions as tWO
inner join dbo.tr_relationships as rInner on rInner.transactionID = tWO.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'WriteOffSaleTrans'
inner join dbo.ams_members as mAss on mAss.memberid = tWO.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glDeb on glDeb.GLAccountID = tWO.debitGLAccountID
where rInner.appliedToTransactionID = @transactionID
and tWO.statusID = 1
	union all
select tWO.transactionID, tWO.typeID, tWO.amount, tWO.amount, 'WriteOff of ' + tWO.detail, tWO.transactionDate, null, null, 
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL
from dbo.tr_transactions as tWO
inner join dbo.tr_relationships as rWO on rWO.transactionID = tWO.transactionID
inner join dbo.tr_relationshipTypes as rtWO on rtWO.typeID = rWO.typeID and rtWO.type = 'WriteOffSaleTrans'
inner join dbo.tr_transactions as tAdj on tAdj.transactionID = rWO.appliedToTransactionID and tAdj.typeID = 3
inner join dbo.tr_relationships as rAdj on rAdj.transactionID = tAdj.transactionID
inner join dbo.tr_relationshipTypes as rtAdj on rtAdj.typeID = rAdj.typeID and rtAdj.type = 'AdjustTrans'
inner join dbo.ams_members as mAss on mAss.memberid = tWO.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glDeb on glDeb.GLAccountID = tWO.debitGLAccountID
where rAdj.appliedToTransactionID = @transactionID
and tWO.statusID = 1
order by 6 desc


-- current gl spread
DECLARE @tblHold TABLE (transactionID int, debitglAccountID int, creditglAccountID int, amount money)
declare @tblTrans TABLE (transactionID int, glAccountID int, debitAmount money, creditAmount money)

-- sale	
INSERT INTO @tblHold
select transactionID, debitglAccountID, creditglAccountID, amount
from dbo.tr_transactions
WHERE transactionID = @transactionID
and statusID = 1

-- adj
INSERT INTO @tblHold
select distinct adj.transactionID, adj.debitglAccountID, adj.creditglAccountID, adj.amount
from dbo.tr_transactions as adj
inner join dbo.tr_relationships as r on r.transactionID = adj.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
inner join @tblHold as tbl on tbl.transactionID = r.appliedToTransactionID
where adj.statusID = 1

-- wo
INSERT INTO @tblHold
select distinct wo.transactionID, wo.debitglAccountID, wo.creditglAccountID, wo.amount
from dbo.tr_transactions as wo
inner join dbo.tr_relationships as r on r.transactionID = wo.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'WriteOffSaleTrans'
inner join @tblHold as tbl on tbl.transactionID = r.appliedToTransactionID
where wo.statusID = 1

-- dit
INSERT INTO @tblHold
select distinct dit.transactionID, dit.debitglAccountID, dit.creditglAccountID, dit.amount
from dbo.tr_transactions as dit
inner join dbo.tr_relationships as r on r.transactionID = dit.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
inner join @tblHold as tbl on tbl.transactionID = r.appliedToTransactionID
where dit.statusID = 1

insert into @tblTrans
select transactionID, debitglAccountID, amount, 0
from @tblHold
	union all
select transactionID, creditglAccountID, 0, amount
from @tblHold

select gl.thePathExpanded + isnull(' (' + nullIf(gl.accountCode,'') + ')','') as glexpanded,
	case 
	when gl.accountType = 'Cash' and sd.debitAmount - sd.creditAmount >= 0 then sd.debitAmount - sd.creditAmount
	when gl.accountType = 'Asset' and gl.GLCode = 'ACCOUNTSRECEIVABLE' and sd.debitAmount - sd.creditAmount > 0 then sd.debitAmount - sd.creditAmount
	when gl.accountType = 'Liability' and gl.GLCode = 'DEPOSITS' and sd.creditAmount - sd.debitAmount <= 0 then abs(sd.creditAmount - sd.debitAmount)
	when gl.accountType = 'Revenue' and sd.creditAmount - sd.debitAmount < 0 then abs(sd.creditAmount - sd.debitAmount)
	when gl.accountType = 'Expense' and sd.debitAmount - sd.creditAmount >= 0 then sd.debitAmount - sd.creditAmount
	when gl.accountType = 'Liability' and sd.creditAmount - sd.debitAmount <= 0 then abs(sd.creditAmount - sd.debitAmount)
	else null
	end as debits,
	case 
	when gl.accountType = 'Cash' and sd.debitAmount - sd.creditAmount < 0 then abs(sd.debitAmount - sd.creditAmount)
	when gl.accountType = 'Asset' and gl.GLCode = 'ACCOUNTSRECEIVABLE' and sd.debitAmount - sd.creditAmount <= 0 then abs(sd.debitAmount - sd.creditAmount)
	when gl.accountType = 'Liability' and gl.GLCode = 'DEPOSITS' and sd.creditAmount - sd.debitAmount > 0 then sd.creditAmount - sd.debitAmount
	when gl.accountType = 'Revenue' and sd.creditAmount - sd.debitAmount >= 0 then sd.creditAmount - sd.debitAmount
	when gl.accountType = 'Expense' and sd.debitAmount - sd.creditAmount < 0 then abs(sd.debitAmount - sd.creditAmount)
	when gl.accountType = 'Liability' and sd.creditAmount - sd.debitAmount > 0 then sd.creditAmount - sd.debitAmount
	else null
	end as credits
from (
	select glAccountID, sum(debitAmount) as debitAmount, sum(creditAmount) as creditAmount
	from @tblTrans
	group by glAccountID
	having (sum(debitAmount) > 0 OR sum(creditAmount) > 0) and sum(debitAmount) <> sum(creditAmount)
) as sd
inner join @allGLS as gl on gl.GLAccountID = sd.GLAccountID
order by gl.thePath


-- deferred schedule
select dit.recognitionDate, sum(t.amount) as recogAmt
from dbo.tr_transactions as t
inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
inner join (
	select @transactionID as transactionID
		union
	select adj.transactionID
	from dbo.tr_transactions as adj
	inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
	inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
	where adj.statusID = 1
) as tbl on tbl.transactionID = r.appliedToTransactionID
inner join dbo.tr_transactionDIT as dit on dit.transactionID = t.transactionID
where t.statusID = 1
and dit.isActive = 1
group by dit.recognitionDate
order by dit.recognitionDate

RETURN 0
GO

ALTER PROC [dbo].[tr_viewTransaction_salestax]
@transactionID int

AS

set nocount on

declare @orgID int
select @orgID = ownedByOrgID from dbo.tr_transactions where transactionID = @transactionID

declare @allGLs TABLE (GLAccountID int, thePathExpanded varchar(max), accountCode varchar(200), accountType varchar(30), GLCode varchar(30), thePath varchar(max))
insert into @allGLS
select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode, rgl.accountType, rgl.GLCode, rgl.thePath
from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl


-- transaction info
select TOP 1 t.transactionid, t.ownedByOrgID, t.recordedOnSiteID, tt.type, t.detail, t.amount, 
	t.transactionDate, t.dateRecorded, ts.status, mAss2.memberID as assignedTomemberID, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullif(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as recordedByMember,
	m2.company as recordedByMemberCompany
from dbo.tr_transactions as t
inner join dbo.tr_types as tt on tt.typeID = t.typeID
inner join dbo.tr_statuses as ts on ts.statusID = t.statusID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join dbo.ams_members as m on m.memberid = t.recordedByMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
where t.transactionID = @transactionID


-- salestax info
select top 1 ts.saleID, 
	tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount-tsFull.cache_pendingPaymentAllocatedAmount as unAllocatedAmount,
	tsFull.cache_activePaymentAllocatedAmount+tsFull.cache_pendingPaymentAllocatedAmount as allocatedAmount,
	hasAdjustments = case when exists (
		select adj.transactionID
		from dbo.tr_transactions as adj
		inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
		inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
		where adj.statusID = 1
		) then 1 else 0 end
from dbo.tr_transactionSales as ts
cross apply dbo.fn_tr_transactionSalesWithDIT(ts.transactionID) as tsFull
where ts.transactionID = @transactionID
OPTION(RECOMPILE)


-- invoices
select distinct i.invoiceID, ins.status, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, i.dateDue, i.invoiceProfileID, ip.profileName, i.invoiceCode
from dbo.tr_transactions as t
inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
inner join dbo.organizations as o on o.orgID = @orgID
where t.transactionID in (
	select @transactionID as transactionID
	union
	select adj.transactionID
	from dbo.tr_transactions as adj
	inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
	inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
)
order by i.dateDue desc, 3 desc


-- current allocations (also include writeoffs)
select t.transactionID, t.typeID, apos.allocatedAmount as allocAmount, t.amount, t.detail, t.transactionDate, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL
from dbo.fn_tr_getAllocatedPaymentsofSale(@transactionID) as apos
inner join dbo.tr_transactions as t on t.transactionID = apos.paymentTransactionID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glDeb on glDeb.GLAccountID = t.debitGLAccountID
	union all
select tWO.transactionID, tWO.typeID, tWO.amount, tWO.amount, 'WriteOff of ' + tWO.detail, tWO.transactionDate, null, null, 
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL
from dbo.tr_transactions as tWO
inner join dbo.tr_relationships as rInner on rInner.transactionID = tWO.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'WriteOffSaleTrans'
inner join dbo.ams_members as mAss on mAss.memberid = tWO.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glDeb on glDeb.GLAccountID = tWO.debitGLAccountID
where rInner.appliedToTransactionID = @transactionID
and tWO.statusID = 1
	union all
select tWO.transactionID, tWO.typeID, tWO.amount, tWO.amount, 'WriteOff of ' + tWO.detail, tWO.transactionDate, null, null, 
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL
from dbo.tr_transactions as tWO
inner join dbo.tr_relationships as rWO on rWO.transactionID = tWO.transactionID
inner join dbo.tr_relationshipTypes as rtWO on rtWO.typeID = rWO.typeID and rtWO.type = 'WriteOffSaleTrans'
inner join dbo.tr_transactions as tAdj on tAdj.transactionID = rWO.appliedToTransactionID and tAdj.typeID = 3
inner join dbo.tr_relationships as rAdj on rAdj.transactionID = tAdj.transactionID
inner join dbo.tr_relationshipTypes as rtAdj on rtAdj.typeID = rAdj.typeID and rtAdj.type = 'AdjustTrans'
inner join dbo.ams_members as mAss on mAss.memberid = tWO.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glDeb on glDeb.GLAccountID = tWO.debitGLAccountID
where rAdj.appliedToTransactionID = @transactionID
and tWO.statusID = 1
order by 6 desc


-- current gl spread
DECLARE @tblHold TABLE (transactionID int, debitglAccountID int, creditglAccountID int, amount money)
declare @tblTrans TABLE (transactionID int, glAccountID int, debitAmount money, creditAmount money)

-- tax	
INSERT INTO @tblHold
select transactionID, debitglAccountID, creditglAccountID, amount
from dbo.tr_transactions
WHERE transactionID = @transactionID
and statusID = 1

-- adj
INSERT INTO @tblHold
select distinct adj.transactionID, adj.debitglAccountID, adj.creditglAccountID, adj.amount
from dbo.tr_transactions as adj
inner join dbo.tr_relationships as r on r.transactionID = adj.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
inner join @tblHold as tbl on tbl.transactionID = r.appliedToTransactionID
where adj.statusID = 1

-- wo
INSERT INTO @tblHold
select distinct wo.transactionID, wo.debitglAccountID, wo.creditglAccountID, wo.amount
from dbo.tr_transactions as wo
inner join dbo.tr_relationships as r on r.transactionID = wo.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'WriteOffSaleTrans'
inner join @tblHold as tbl on tbl.transactionID = r.appliedToTransactionID
where wo.statusID = 1

-- dit
INSERT INTO @tblHold
select distinct dit.transactionID, dit.debitglAccountID, dit.creditglAccountID, dit.amount
from dbo.tr_transactions as dit
inner join dbo.tr_relationships as r on r.transactionID = dit.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
inner join @tblHold as tbl on tbl.transactionID = r.appliedToTransactionID
where dit.statusID = 1

insert into @tblTrans
select transactionID, debitglAccountID, amount, 0
from @tblHold
	union all
select transactionID, creditglAccountID, 0, amount
from @tblHold

select gl.thePathExpanded + isnull(' (' + nullIf(gl.accountCode,'') + ')','') as glexpanded,
	case 
	when gl.accountType = 'Cash' and sd.debitAmount - sd.creditAmount >= 0 then sd.debitAmount - sd.creditAmount
	when gl.accountType = 'Asset' and gl.GLCode = 'ACCOUNTSRECEIVABLE' and sd.debitAmount - sd.creditAmount > 0 then sd.debitAmount - sd.creditAmount
	when gl.accountType = 'Liability' and gl.GLCode = 'DEPOSITS' and sd.creditAmount - sd.debitAmount <= 0 then abs(sd.creditAmount - sd.debitAmount)
	when gl.accountType = 'Revenue' and sd.creditAmount - sd.debitAmount < 0 then abs(sd.creditAmount - sd.debitAmount)
	when gl.accountType = 'Expense' and sd.debitAmount - sd.creditAmount >= 0 then sd.debitAmount - sd.creditAmount
	when gl.accountType = 'Liability' and sd.creditAmount - sd.debitAmount <= 0 then abs(sd.creditAmount - sd.debitAmount)
	else null
	end as debits,
	case 
	when gl.accountType = 'Cash' and sd.debitAmount - sd.creditAmount < 0 then abs(sd.debitAmount - sd.creditAmount)
	when gl.accountType = 'Asset' and gl.GLCode = 'ACCOUNTSRECEIVABLE' and sd.debitAmount - sd.creditAmount <= 0 then abs(sd.debitAmount - sd.creditAmount)
	when gl.accountType = 'Liability' and gl.GLCode = 'DEPOSITS' and sd.creditAmount - sd.debitAmount > 0 then sd.creditAmount - sd.debitAmount
	when gl.accountType = 'Revenue' and sd.creditAmount - sd.debitAmount >= 0 then sd.creditAmount - sd.debitAmount
	when gl.accountType = 'Expense' and sd.debitAmount - sd.creditAmount < 0 then abs(sd.debitAmount - sd.creditAmount)
	when gl.accountType = 'Liability' and sd.creditAmount - sd.debitAmount > 0 then sd.creditAmount - sd.debitAmount
	else null
	end as credits
from (
	select glAccountID, sum(debitAmount) as debitAmount, sum(creditAmount) as creditAmount
	from @tblTrans
	group by glAccountID
	having (sum(debitAmount) > 0 OR sum(creditAmount) > 0) and sum(debitAmount) <> sum(creditAmount)
) as sd
inner join @allGLS as gl on gl.GLAccountID = sd.GLAccountID
order by gl.thePath


-- deferred schedule
select dit.recognitionDate, sum(t.amount) as recogAmt
from dbo.tr_transactions as t
inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
inner join (
	select @transactionID as transactionID
		union
	select adj.transactionID
	from dbo.tr_transactions as adj
	inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
	inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
	where adj.statusID = 1
) as tbl on tbl.transactionID = r.appliedToTransactionID
inner join dbo.tr_transactionDIT as dit on dit.transactionID = t.transactionID
where t.statusID = 1
and dit.isActive = 1
group by dit.recognitionDate
order by dit.recognitionDate


-- sale transaction
select top 1 tSale.transactionID, tt.type, tSale.amount, tSale.detail,
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	glCred.thePathExpanded + isnull(' (' + nullIf(glCred.accountCode,'') + ')','') as creditGL
from dbo.tr_transactions as tSale
inner join dbo.tr_types as tt on tt.typeID = tSale.typeID
inner join dbo.tr_relationships as rTax on rTax.appliedToTransactionID = tSale.transactionID
inner join dbo.tr_relationshipTypes as rtTax on rtTax.typeID = rTax.typeID and rtTax.type = 'SalesTaxTrans'
inner join dbo.ams_members as mAss on mAss.memberid = tSale.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glCred on glCred.GLAccountID = tSale.creditGLAccountID
where rTax.transactionID = @transactionID


RETURN 0
GO

use platformQueue
GO
ALTER PROC [dbo].[job_firmSubStatements_grabForProcessing]
@serverID int,
@batchSize int,
@filePath varchar(400)

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @subTypeOrdering TABLE (itemUID uniqueidentifier, typeID int, typeSortOrder int)

	declare @statusReady int, @statusGrabbed int
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'FirmSubStatements'
		and qs.queueStatus = 'readyToProcess'
	select @statusGrabbed = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'FirmSubStatements'
		and qs.queueStatus = 'grabbedForProcessing'

	declare @jobUID uniqueidentifier
	set @jobUID = NEWID()

	-- dequeue in order of dateAdded. get @batchsize firms
	IF OBJECT_ID('tempdb..#tmpTblQueueItems_firmbilling') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_firmbilling
	CREATE TABLE #tmpTblQueueItems_firmbilling (itemUID uniqueidentifier, itemGroupUID uniqueidentifier, 
		jobUID uniqueidentifier, recordedByMemberID int, siteID int, memberID int, memberNumber varchar(50), 
		firstname varchar(75), lastname varchar(75), company varchar(200), address1 varchar(100), 
		address2 varchar(100), address3 varchar(100), city varchar(35), stateProv varchar(4),
		postalCode varchar(25), country varchar(100), xmlFieldSets varchar(max), 
		xmlConfigParam varchar(max), xmlFirms varchar(max))

	update qi WITH (UPDLOCK, READPAST)
	set qi.queueStatusID = @statusGrabbed,
		qi.dateUpdated = getdate(),
		qi.jobUID = @jobUID,
		qi.jobDateStarted = getdate(),
		qi.jobServerID = @serverID
		OUTPUT inserted.itemUID, null, inserted.jobUID, null, null, null, null, null, null, null, 
			null, null, null, null, null, null, null, null, null, null
		INTO #tmpTblQueueItems_firmbilling
	from platformQueue.dbo.tblQueueItems as qi
	inner join (
		select top(@BatchSize) qi2.itemUID 
		from platformQueue.dbo.tblQueueItems as qi2
		where qi2.queueStatusID = @statusReady
		order by qi2.dateAdded, qi2.itemUID
		) as batch on batch.itemUID = qi.itemUID
	where qi.queueStatusID = @statusReady

	IF @TranCounter = 0
		COMMIT TRAN;

END TRY
BEGIN CATCH
	IF OBJECT_ID('tempdb..#tmpTblQueueItems_firmbilling') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_firmbilling

	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH


BEGIN TRY
	-- get memberID, itemGroupUID, siteID, recordedByMemberID from item data
	-- outer apply is required in cases where there is no match so the itemGroupUID gets updated
	update tmp
	set tmp.memberID = idata.memberID,
		tmp.itemGroupUID = idata.itemGroupUID,
		tmp.recordedByMemberID = idata.recordedByMemberID,
		tmp.siteID = idata.siteID
	from #tmpTblQueueItems_firmbilling as tmp
	outer apply (
		select min(cast(dataKey as int)) as memberID, min(cast(itemGroupUID as varchar(60))) as itemGroupUID, 
			min(recordedByMemberID) as recordedByMemberID, min(siteID) as siteID
		from platformQueue.dbo.tblQueueItemData as qid
		inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
			and dc.columnname in ('FirmChildSub','FirmChildNoSub')
		where qid.itemUID = tmp.itemUID
	) as idata

	-- get address info for firm accounts
	update tmp 
	set	tmp.memberNumber = m.memberNumber, 
		tmp.firstname = m.firstname, 
		tmp.lastname = m.lastname, 
		tmp.company = m.company, 
		tmp.address1 = ma.address1, 
		tmp.address2 = ma.address2, 
		tmp.address3 = ma.address3, 
		tmp.city = ma.city, 
		tmp.stateProv = s.code, 
		tmp.postalCode = ma.postalCode,
		tmp.country = c.country
	from #tmpTblQueueItems_firmbilling as tmp
	inner join membercentral.dbo.ams_members as m on m.memberID = tmp.memberID
	left outer join membercentral.dbo.ams_memberAddresses as ma on ma.memberID = m.memberID 
		and ma.addressTypeID = m.billingAddressTypeID
	left outer join membercentral.dbo.ams_states as s on s.stateID = ma.stateID
	left outer join membercentral.dbo.ams_countries as c on c.countryID = ma.countryID

	-- get config params for each item. casting to varchar(max) because it speed up final data query return
	update tmp
	set tmp.xmlConfigParam = config.configXML
	from #tmpTblQueueItems_firmbilling as tmp
	cross apply (
		select cast(isnull((
			select reportParam, paramvalue	
			from (
				select datakey as reportParam, cast(isnull(columnValueString,'') as varchar(max)) as paramvalue
				from platformQueue.dbo.tblQueueItemData qid
				inner join platformQueue.dbo.tblQueueTypeDataColumns dc on dc.columnID = qid.columnID
					and dc.columnname = 'ConfigParam'
				where qid.itemUID = tmp.itemUID
					union all
				select datakey as reportParam, isnull(columnValuetext,'') as paramvalue
				from platformQueue.dbo.tblQueueItemData qid
				inner join platformQueue.dbo.tblQueueTypeDataColumns dc on dc.columnID = qid.columnID
					and dc.columnname = 'ConfigText'
				where qid.itemUID = tmp.itemUID
			) as tmp
			for xml path('param'), root('params'), type
		),'<params/>') as varchar(max)) as configXML
	) as config

	-- get fieldsets for each item. casting to varchar(max) because it speed up final data query return
	update tmp
	set tmp.xmlFieldSets = fieldset.fieldsetXML
	from #tmpTblQueueItems_firmbilling as tmp
	cross apply (
		select cast(isnull((
			select reportParam, paramvalue	
			from (
				select datakey as reportParam, cast(columnValueString as varchar(max)) as paramvalue
				from platformQueue.dbo.tblQueueItemData qid
				inner join platformQueue.dbo.tblQueueTypeDataColumns dc on dc.columnID = qid.columnID
					and dc.columnname = 'fieldset'
				where qid.itemUID = tmp.itemUID
			) as tmp
			for xml path('fieldset'), root('fieldsets'), type
		),'<fieldsets/>') as varchar(max)) as fieldsetXML
	) as fieldset

	-- get all firm members
	IF OBJECT_ID('tempdb..#qryAllFirmMembers') IS NOT NULL 
		DROP TABLE #qryAllFirmMembers
	CREATE TABLE #qryAllFirmMembers (itemUID uniqueidentifier, childMemberID int, childMemberNumber varchar(50), 
		prefix varchar(50), firstname varchar(75), lastname varchar(75), company varchar(200), suffix varchar(50),
		address1 varchar(100), address2 varchar(100), address3 varchar(100), city varchar(35), stateProv varchar(4),
				postalCode varchar(25), country varchar(100)
	)
	insert into #qryAllFirmMembers (itemUID, childMemberID)
	select distinct tmp.itemUID, qid.columnValueInteger
	from #tmpTblQueueItems_firmbilling as tmp
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = tmp.itemUID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
		and dc.columnname = 'FirmChildNoSub'
		union
	select distinct tmp.itemUID, m.activeMemberID
	from #tmpTblQueueItems_firmbilling as tmp
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = tmp.itemUID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
		and dc.columnname = 'FirmChildSub'
	inner join membercentral.dbo.sub_subscribers as ss on ss.subscriberID = qid.columnValueInteger
	inner join membercentral.dbo.ams_members as m on m.memberID = ss.memberID

	update fm 
	set childMemberNumber = m.MemberNumber, 
		prefix = m.prefix,
		firstname = m.firstname, 
		lastname = m.lastname, 
		company = m.company,
		suffix = m.suffix,
		address1 = ma.address1, 
		address2 = ma.address2, 
		address3 = ma.address3, 
		city = ma.city, 
		stateProv = s.code, 
		postalCode = ma.postalCode,
		country = c.country
	from #qryAllFirmMembers as fm
	inner join membercentral.dbo.ams_members as m on m.memberID = fm.childMemberID
	left outer join membercentral.dbo.ams_memberAddresses as ma on ma.memberID = m.memberID 
		and ma.addressTypeID = m.billingAddressTypeID
	left outer join membercentral.dbo.ams_states as s on s.stateID = ma.stateID
	left outer join membercentral.dbo.ams_countries as c on c.countryID = ma.countryID

	-- get all subs
	IF OBJECT_ID('tempdb..#qrySubs') IS NOT NULL 
		DROP TABLE #qrySubs
	CREATE TABLE #qrySubs (itemUID uniqueidentifier, subscriberID int, memberID int, subscriptionID int, typeID int,
		typeName varchar(100), subscriptionName varchar(300), rateName varchar(200), frequencyName varchar(50),
		[status] varchar(1), statusName varchar(50), subStartDate datetime, subEndDate datetime, graceEndDate datetime,
		parentSubscriberID int, rootSubscriberID int, lastPrice money, rfid int, thePath varchar(max), 
		subAmount money, subAmountDue money, subAmountPaid money, subscriberPath varchar(200), saleTransactionID int, 
		glaccountID int, invoiceContentVersionID int, invoiceProfileID int, invoiceProfileImageExt varchar(5))

	insert into #qrySubs (itemUID, subscriberID, memberID, subscriptionID, typeID, typeName, subscriptionName, rateName, 
		frequencyName, [status], statusName, subStartDate, subEndDate, graceEndDate, parentSubscriberID, rfid, 
		rootSubscriberID, lastPrice, subscriberPath, glaccountID)
	select tmp.itemUID, ss.subscriberID, m.activeMemberID, ss.subscriptionID, t.typeID, t.typeName, sub.subscriptionName, 
		r.rateName, f.frequencyName, st.statusCode, st.statusName, ss.subStartDate, ss.subEndDate, ss.graceEndDate, 
		ss.parentSubscriberID, ss.rfid, ss.rootSubscriberID, ss.lastprice, ss.subscriberPath, ss.glaccountID
	from #tmpTblQueueItems_firmbilling as tmp
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = tmp.itemUID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
		and dc.columnname = 'FirmChildSub'
	inner join membercentral.dbo.sub_subscribers as rootss on rootss.subscriberID = qid.columnValueInteger
	inner join membercentral.dbo.sub_subscribers as ss on rootss.subscriberID = ss.rootSubscriberID
	inner join membercentral.dbo.sub_subscriptions as sub on sub.subscriptionID = ss.subscriptionID
	inner join membercentral.dbo.sub_types as t on sub.typeiD = t.typeID
	inner join membercentral.dbo.ams_members as m on ss.memberID = m.memberID
	inner join membercentral.dbo.sub_statuses as st on st.statusID = ss.statusID 
		and st.statuscode in ('A','I','O','P')
	inner join membercentral.dbo.sub_rateFrequencies as rf on rf.rfid = ss.rfid
	inner join membercentral.dbo.sub_rates as r on r.rateID = rf.rateID 
	inner join membercentral.dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID

	update qs 
	set saleTransactionID = t.transactionID,
		subAmount = tsFull.cache_amountAfterAdjustment,
		subAmountDue = tsFull.cache_amountAfterAdjustment - tsFull.cache_activePaymentAllocatedAmount,
		subAmountPaid = tsFull.cache_activePaymentAllocatedAmount,
		invoiceContentVersionID = it.messageContentVersionID
	from #qrySubs as qs
	inner join membercentral.dbo.tr_applications as app on app.ItemID = qs.subscriberID 
		and app.applicationTypeID = 17 
		and app.itemType = 'Dues' 
		and app.status = 'A'
	inner join membercentral.dbo.tr_transactions as t on t.transactionID = app.transactionID
	cross apply membercentral.dbo.fn_tr_transactionSalesWithDIT(t.transactionID) as tsFull
	inner join membercentral.dbo.tr_invoiceTransactions it on it.transactionID = t.transactionID
	OPTION(RECOMPILE)

	-- update amount and amountdue columns for billed subscriptions
	update qs 
	set subAmount = qs.lastprice, subAmountDue = qs.lastprice, subAmountPaid = 0
	from #qrySubs as qs
	where status = 'O'

	-- set typeSortOrder based on billed total by subscription type
	insert into @subTypeOrdering (itemUID, typeID , typeSortOrder )
	select tmp.itemUID, tmp.typeID, row_number() over (partition by tmp.itemUID order by tmp.subAmountTotal desc, typename desc)
	from (
		select qs.itemUID, qs.typeID, qs.typeName, sum(subAmount) as subAmountTotal
		from #qrySubs as qs
		group by qs.itemUID, qs.typeID, qs.typeName
	) as tmp

	-- update invoiceProfileID and invoice image (billed subs have no transactions to join against)
	update qs 
	set invoiceProfileID = ip.profileID, 
		invoiceProfileImageExt = ip.imageExt
	from #qrySubs as qs
	inner join membercentral.dbo.tr_glaccounts as gl on gl.glaccountID = qs.glaccountID
	inner join membercentral.dbo.tr_invoiceProfiles ip on ip.profileID = gl.invoiceProfileID

	-- update invoiceContentVersionID for subs with no transactions
	update qs 
	set invoiceContentVersionID = cv.contentVersionID
	from #qrySubs as qs
	inner join membercentral.dbo.sub_types t on t.typeID = qs.typeID
	inner join membercentral.dbo.sites s on s.siteID = t.siteID
	inner join membercentral.dbo.tr_glaccounts as gl on gl.glaccountID = qs.glaccountID
		and qs.saleTransactionID is null
	inner join membercentral.dbo.cms_content as c on c.contentID = gl.invoiceContentID
	inner join membercentral.dbo.cms_contentLanguages as cl on cl.contentID = c.contentID
		and cl.languageID = s.defaultLanguageID
	inner join membercentral.dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID
		and cv.isActive = 1

	-- get sub tree totals
	IF OBJECT_ID('tempdb..#tblSubTreeTotals') IS NOT NULL 
		DROP TABLE #tblSubTreeTotals
	CREATE TABLE #tblSubTreeTotals (itemUID uniqueidentifier, rootsubscriberID int, tree_subAmount money, 
		tree_subAmountDue money, tree_subAmountPaid money)

	insert into #tblSubTreeTotals (itemUID, rootsubscriberID, tree_subAmount, tree_subAmountDue, tree_subAmountPaid)
	select itemUID, rootSubscriberID, sum(subAmount) as tree_subAmount, sum(subAmountDue) as tree_subAmountDue, sum(subAmountPaid) as tree_subAmountPaid
	from #qrySubs 
	group by itemUID, rootSubscriberID

	-- get sub payments
	IF OBJECT_ID('tempdb..#tblSubPayments') IS NOT NULL 
		DROP TABLE #tblSubPayments
	CREATE TABLE #tblSubPayments (itemUID uniqueidentifier, subscriberID int, rootsubscriberID int, transactionID int, 
		allocatedAmount money, detail varchar(max), depositdate datetime)

	insert into #tblSubPayments (itemUID, subscriberID, rootsubscriberID, transactionID, allocatedAmount, detail, depositdate)
	select s.itemUID, s.subscriberID, s.rootsubscriberID, t.transactionID, st.allocatedAmount, t.detail, b.depositDate
	from #qrySubs as s
	inner join membercentral.dbo.tr_applications as app on app.ItemID = s.subscriberID 
		and app.applicationTypeID = 17 
		and app.itemType = 'Dues' 
		and app.status = 'A'
	CROSS APPLY membercentral.dbo.fn_tr_getAllocatedPaymentsofSale(app.transactionID) AS st
	inner join membercentral.dbo.tr_transactions as t on t.transactionID = st.paymentTransactionID
	inner join membercentral.dbo.tr_batchTransactions as bt on bt.transactionID = t.transactionID
	inner join membercentral.dbo.tr_batches as b on b.batchID = bt.batchID

	-- get sub invoices
	IF OBJECT_ID('tempdb..#tblSubInvoices') IS NOT NULL 
		DROP TABLE #tblSubInvoices
	CREATE TABLE #tblSubInvoices (itemUID uniqueidentifier, subscriberID int, rootsubscriberID int, invoiceID int,
		invoiceCode char(8), datebilled datetime, datedue datetime, statusID int, [status] varchar(50), amount money,
		amountDue money, payProfileDesc varchar(100), invoiceNumber varchar(20))

	insert into #tblSubInvoices (itemUID, subscriberID, rootsubscriberID, invoiceID, invoiceCode, datebilled, datedue,
		statusID, [status], amount, amountDue,payProfileDesc, invoiceNumber)
	select s.itemUID, s.subscriberID, s.rootsubscriberID, i.invoiceID, i.invoiceCode, i.datebilled, i.datedue, ist.statusID, 
		ist.status, sum(st.amount) as amount, sum(st.amountDue) as amountDue,
		payProfileDesc = g.gatewayclass + ' - (' + mpp.detail + ')',
		invoiceNumber = o.orgcode + right(replicate('0',8) + cast(i.invoiceID as varchar(10)),8)
	from #qrySubs as s
	CROSS APPLY membercentral.dbo.fn_sub_subscriberTransactions(s.subscriberID) AS st 
	inner join membercentral.dbo.tr_invoices as i on i.invoiceID = st.invoiceID
	inner join membercentral.dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
	inner join membercentral.dbo.organizations as o on o.orgID = ip.orgID
	inner join membercentral.dbo.tr_invoiceStatuses as ist on ist.statusID = i.statusID and i.statusID <> 1
	left outer join membercentral.dbo.ams_memberPaymentProfiles as mpp
		inner join membercentral.dbo.mp_profiles as mp on mp.profileID = mpp.profileID
		inner join membercentral.dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
		on mpp.payProfileID = i.payProfileID
	group by s.itemUID, s.subscriberID, s.rootsubscriberID, i.invoiceID, i.invoiceCode, i.datebilled, i.datedue, 
		ist.statusID, ist.status, g.gatewayclass, mpp.detail, o.orgcode

	-- populate table var of contentVersions referenced by qrySubs
	declare @invoiceContent table (contentVersionID int PRIMARY KEY, rawcontent varchar(max))
	declare @invoiceContentandSubtrees table (contentVersionID int, rootSubscriberID int, messageNumber int)

	insert into @invoiceContent (contentVersionID, rawcontent)
	select distinct cv.contentVersionID, cv.rawcontent
	from #qrySubs as qs
	inner join membercentral.dbo.cms_contentVersions as cv on cv.contentVersionID = qs.invoiceContentVersionID

	insert into @invoiceContentandSubtrees (contentVersionID, rootSubscriberID, messageNumber)
	select ic.contentVersionID, qs.rootsubscriberID, row_number() over (partition by qs.rootsubscriberID order by min(qs.subscriberPath))
	from #qrySubs as qs
	inner join @invoiceContent as ic on ic.contentVersionID = qs.invoiceContentVersionID
	group by ic.contentVersionID, qs.rootsubscriberID

	-- firms xml. casting to varchar(max) because it speed up final data query return
	update tmp
	set tmp.xmlFirms = firms.firmXML
	from #tmpTblQueueItems_firmbilling as tmp
	cross apply (
		select cast(isnull((		
			select company.memberID as '@firmmemberid',
				company.memberNumber as '@firmmembernumber' ,
				company.firstname as '@firmfirstname' ,
				company.lastname as '@firmlastname' ,
				company.company as '@firmcompany' ,
				company.address1 as '@firmaddress1' ,
				company.address2 as '@firmaddress2' ,
				company.address3 as '@firmaddress3' ,
				company.city as '@firmcity' ,
				company.stateProv as '@firmstate' ,
				company.postalCode as '@firmpostalcode',
				company.country as '@firmcountry',
				( select
					account.childmemberid as '@childmemberID',
					account.childMemberNumber as '@childmembernumber',
					isnull(company.memberID,0) as '@firmmemberid',
					case when exists (select * from #qrySubs as st2 where st2.memberID = account.childmemberid and st2.itemUID = tmp.itemUID) then 1 else 0 end as '@hassubscription',
					account.firstname as '@firstname', 
					account.lastname as '@lastname',
					account.prefix as '@prefix',
					account.suffix as '@suffix',
					account.lastname + ', ' + account.firstname + ' (' + account.childMemberNumber + ')' as '@namestring',
					account.company as '@company',
					account.address1 as '@address1' ,
					account.address2 as '@address2' ,
					account.address3 as '@address3' ,
					account.city as '@city' ,
					account.stateProv as '@state' ,
					account.postalCode as '@postalcode',
					account.country as '@country',

					( select
						subscriptionTree.rootsubscriberid as '@rootsubscriberid',
						case when subscriptionTree.rootsubscriberid is null then null else account.childmemberid end as '@activeMemberID',
						case when subscriptionTree.rootsubscriberid is null then null else account.lastname + ', ' + account.firstname + ' (' + account.childMemberNumber + ')' end as '@namestring',
						subscriptionTree.subscriptionname as '@subscriptionname',
						qst.tree_subAmount as '@subamount_total',
						qst.tree_subAmountPaid as '@subamountpaid_total',
						qst.tree_subAmountDue as '@subamountdue_total',
						CONVERT(VARCHAR(8),subscriptionTree.substartdate, 1) + ' - ' + CONVERT(VARCHAR(8),subscriptionTree.subenddate, 1) as '@subtreedaterange',
						cast(subscriptionTree.invoiceProfileID as varchar(10)) + '.' + subscriptionTree.invoiceProfileImageExt as '@subtreeinvoiceprofileimage',
						( select
							subscriber.subscriberid as '@subscriberID',
							account.childmemberid as '@activeMemberID',
							subscriber.subscriptionid as '@subscriptionid',
							subscriber.typeid as '@typeid',
							subscriber.typename as '@typename',
							subscriber.subscriptionname as '@subscriptionname',
							subscriber.ratename as '@ratename',
							subscriber.frequencyname as '@frequencyname',
							subscriber.status as '@status',
							subscriber.statusname as '@statusname',
							subscriber.substartdate as '@substartdate',
							subscriber.subenddate as '@subenddate',
							CONVERT(VARCHAR(8),subscriber.substartdate, 1) + ' - ' + CONVERT(VARCHAR(8),subscriber.subenddate, 1) as '@subdaterange',
							subscriber.graceenddate as '@graceenddate',
							subscriber.parentsubscriberid as '@parentsubscriberid',
							subscriber.rootsubscriberid as '@rootsubscriberid',
							subscriber.rfid as '@rfid',
							subscriber.thepath as '@thepath',
							subscriber.lastprice as '@lastprice',
							subscriber.subAmount as '@subamount',
							subscriber.subAmountPaid as '@subamountpaid',
							subscriber.subAmountDue as '@subamountdue',
							subscriber.subscriberPath as '@subscriberpath',
							subscriber.invoiceContentVersionID as '@invoicecontentversionid',
							icst.messageNumber as '@invoicecontentfootnotenumber',
							sto.typeSortOrder as '@coversheettypesortorder'
						from #qrySubs as subscriber
						inner join @subTypeOrdering sto
							on sto.itemUID = subscriber.itemUID
							and sto.typeID = subscriber.typeID
						left outer join @invoiceContentandSubtrees as icst on icst.rootsubscriberID = subscriber.rootsubscriberID
							and subscriber.invoiceContentVersionID = icst.contentVersionID
						where subscriber.itemUID = tmp.itemUID
						and subscriber.rootSubscriberID = subscriptionTree.subscriberID
						order by subscriber.subscriberPath
						for xml path('subscriber'), type
						),
						( select
							rootsubscriberID as '@rootsubscriberID',
							invoiceID as '@invoiceid',
							invoiceNumber as '@invoicenumber',
							invoiceCode as '@invoicecode',
							datebilled as '@datebilled',
							datedue as '@datedue',
							statusID as '@statusid',
							[status] as '@status',
							sum(amount) as '@amount',
							sum(amountDue) as '@amountDue',
							isnull(payProfileDesc,'') as '@payprofiledesc'
						from #tblSubInvoices as si
						where si.itemUID = tmp.itemUID
						and si.rootSubscriberID = subscriptionTree.rootsubscriberID
						group by rootsubscriberID, invoiceID, invoiceCode, datebilled, datedue, statusID, [status], payProfileDesc, invoiceNumber
						order by datedue
						for xml path ('invoice'),root('invoices'), type
						),
						( select
							rootsubscriberID as '@rootsubscriberID',
							transactionID as '@transactionID',
							depositdate as '@datebilled',
							detail as '@detail',
							sum(allocatedAmount) as '@allocatedAmount'
						from #tblSubPayments sp
						where sp.itemUID = tmp.itemUID
						and sp.rootSubscriberID = subscriptionTree.rootsubscriberID
						group by rootsubscriberID ,transactionID ,detail,depositdate
						order by depositdate
						for xml path('payment'), root('payments'), type
						),
						( select
							subscriber.rootSubscriberID as '@rootsubscriberID',
							icst.messageNumber as '@invoicecontentfootnotenumber',
							ic.contentversionid as '@contentversionid',
							ic.rawcontent as '@rawcontent'
						from #qrySubs as subscriber
						inner join @invoiceContent as ic on ic.contentVersionID = subscriber.invoiceContentVersionID
							and subscriber.itemUID = tmp.itemUID
							and subscriber.rootSubscriberID = subscriptionTree.subscriberID
						inner join @invoiceContentandSubtrees as icst on icst.rootsubscriberID = subscriber.rootsubscriberID
							and icst.contentVersionID = ic.contentVersionID
						group by subscriber.rootSubscriberID, icst.messageNumber, ic.contentversionid, ic.rawcontent
						order by icst.messageNumber
						for xml path('invoicemessage'), root('invoicemessages'), type
						)
						from #qrySubs as subscriptionTree
						inner join #tblSubTreeTotals as qst 
							on qst.rootSubscriberID = subscriptionTree.rootSubscriberID
							and qst.itemUID = subscriptionTree.itemUID
						where subscriptionTree.itemUID = tmp.itemUID
						and subscriptionTree.memberID = account.childMemberID
						and subscriptionTree.subscriberID = subscriptionTree.rootsubscriberID
						order by subscriptionTree.rootsubscriberid
						for xml path('subscriptiontree'), type
					)
					from #qryAllFirmMembers as account
					where account.itemUID = company.itemUID
					order by account.lastname, account.firstname, account.childMemberID
					for xml path('account'), type
				)
			from #tmpTblQueueItems_firmbilling as company
			where company.itemUID = tmp.itemUID
			order by company.company, company.memberID
			for xml path('company'), root('companies'), type
		),'<companies/>') as varchar(max)) as firmXML
	) as firms

	-- create directories to store xmls
	IF OBJECT_ID('tempdb..#tblDirs') IS NOT NULL 
		DROP TABLE #tblDirs
	select distinct itemGroupUID, membercentral.dbo.fn_createDirectory(@filePath + cast(itemGroupUID as varchar(50))) as cdResult
	into #tblDirs
	from #tmpTblQueueItems_firmbilling
	where membercentral.dbo.fn_DirectoryExists(@filePath + cast(itemGroupUID as varchar(50))) = 0

	-- final data
	select itemUID, itemGroupUID, jobUID, siteID, memberNumber, company, xmlFieldSets, xmlConfigParam, 
		membercentral.dbo.fn_writefile(@filePath + cast(itemGroupUID as varchar(50)) + '\' + cast(itemUID as varchar(50)) + '.xml',xmlFirms,1) as writeFileResult
	from #tmpTblQueueItems_firmbilling
	order by itemUID

	IF OBJECT_ID('tempdb..#tblDirs') IS NOT NULL 
		DROP TABLE #tblDirs
	IF OBJECT_ID('tempdb..#tblSubInvoices') IS NOT NULL 
		DROP TABLE #tblSubInvoices
	IF OBJECT_ID('tempdb..#tblSubPayments') IS NOT NULL 
		DROP TABLE #tblSubPayments
	IF OBJECT_ID('tempdb..#tblSubTreeTotals') IS NOT NULL 
		DROP TABLE #tblSubTreeTotals
	IF OBJECT_ID('tempdb..#qrySubs') IS NOT NULL 
		DROP TABLE #qrySubs
	IF OBJECT_ID('tempdb..#qryAllFirmMembers') IS NOT NULL 
		DROP TABLE #qryAllFirmMembers
	IF OBJECT_ID('tempdb..#tmpTblQueueItems_firmbilling') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_firmbilling

	RETURN 0
END TRY
BEGIN CATCH
	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH
GO

