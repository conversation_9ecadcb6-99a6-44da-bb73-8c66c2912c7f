declare @glaccountID int

select 
	@glaccountID = glaccountID 
from 
	dbo.tr_GLAccounts 
where 
	orgID = 2 
	and accountName = 'Membership Dues No Charge' 
	and accountCode = '9999|15|000'

print @glaccountID

if @glaccountID is not null
	update 
		s
	set
		s.glaccountID = @glaccountID
	from
		sub_subscriptions s 
		inner join dbo.sub_Types t on
			t.typeID = s.typeID
			and typeName in ('Committee Titles','Section Titles')
			and siteID = 2
		inner join dbo.tr_GLAccounts gla on
			gla.glaccountID = s.glaccountID