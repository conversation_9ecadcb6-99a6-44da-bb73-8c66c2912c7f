use customApps
GO

CREATE TABLE dbo.WA_Blog_Categories
(
blogCategoryID int NOT NULL,
blogCategoryDescription varchar(50) NOT NULL,
sortOrder int NULL
) ON [PRIMARY]
GO

ALTER TABLE dbo.WA_Blog_Categories ADD CONSTRAINT
PK_WA_Blog_Categories PRIMARY KEY CLUSTERED 
(
blogCategoryID
) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO

CREATE TABLE dbo.WA_Blog_Entries
(
blogEntryID int NOT NULL,
blogID int NOT NULL,
title varchar(250) NULL,
link varchar(500) null,
pubDate datetime null,
[guid] varchar(500) null,
[description] varchar(max) null,
shortDesc varchar(400) null 
) ON [PRIMARY]
GO
ALTER TABLE dbo.WA_Blog_Entries ADD CONSTRAINT
PK_WA_Blog_Entries PRIMARY KEY CLUSTERED 
(
blogEntryID
) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO

CREATE TABLE dbo.WA_Blog_Feeds
(
blogID int NOT NULL,
blogCategoryID int NULL,
blogURL varchar(350) NULL,
blogDescription varchar(max) null,
addAuth bit not null,
sortOrder int null,
isValid int not null 
) ON [PRIMARY]
GO
ALTER TABLE dbo.WA_Blog_Feeds ADD CONSTRAINT
PK_WA_Blog_Feeds PRIMARY KEY CLUSTERED 
(
blogID
) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO

use membercentral
GO
declare @toolTypeID int
declare @toolResourceTypeID int
declare @navigationID int
declare @resourceTypeFunctionID int
SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('Admin'),dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('Admin')))
exec createAdminToolType @toolType='WABlogAdmin', @toolCFC='Custom.WA.WA.BlogAdmin', @toolDesc='WA Blog Administration', @toolTypeID=@toolTypeID OUTPUT, @resourceTypeID=@toolResourceTypeID OUTPUT
exec createAdminNavigation @navName='Blogs',@navDesc=NULL,@parentNavigationID=8,@navAreaID=3,@cfcMethod='list',@isHeader=0, @showInNav=1,@navigationID=@navigationID OUTPUT
exec dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID=@resourceTypeFunctionID, @toolTypeID=@toolTypeID, @navigationID=@navigationID
exec [createAdminSuite] 21
GO

