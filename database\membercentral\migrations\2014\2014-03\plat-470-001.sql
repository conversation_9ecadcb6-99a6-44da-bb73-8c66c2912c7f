use search
GO

declare 
	@zoneTypeID int,
	@advertiserID int,
	@adTypeID int,
	@orgID int

select @orgID = orgID from membercentral.membercentral.dbo.organizations where orgCode = 'TS'

insert into membercentral.membercentral.dbo.ad_advertisers (
	name,
	orgID,
	status,
	dateCreated
)
values (
	'Trial<PERSON>mith, Inc',
	@orgID,
	'A',
	getDate()
)

select @advertiserID = advertiserID from  membercentral.membercentral.dbo.ad_advertisers where [name] = '<PERSON><PERSON><PERSON>, <PERSON>'

select @zoneTypeID = zoneTypeID from membercentral.membercentral.dbo.ad_zoneTypes where zoneTypeName = 'search'

select @adTypeID = adTypeID from membercentral.membercentral.dbo.ad_types where adTypeName = 'Search Ad'

insert into membercentral.membercentral.dbo.ad_advertiserZones (
	zoneID,
	advertiserID
)
select 
	zoneID,
	@advertiserID
from 
	membercentral.membercentral.dbo.ad_zones 
where 
	zoneTypeID = @zoneTypeID

insert into membercentral.membercentral.dbo.ad_ads(
	adName,
	advertiserID,
	adTypeID,
	imageUrl,
	adShortText,
	imageWidth,
	imageHeight,
	adContentObjectID,
	status,
	dateCreated
)
select
	adName, 
	@advertiserID,
	@adTypeID,
	cast (searchAdID as varchar),--'/assets/common/searchAds/' + cast (searchAdID as varchar) + '.jpg',
	adName as adShortText,
	180,
	150,
	adContentID, 
	case
		when sr.siteResourceStatusID = 1 then 'A'
		else 'I'
	end,
	dateCreated
from 
	dbo.tblSearchAds sa
	inner join membercentral.membercentral.dbo.cms_siteResources sr on 
		sr.siteResourceID = sa.siteResourceID 

insert into membercentral.membercentral.dbo.ad_adPlacements (
	adID,
	zoneID,
	status
)
select 
	aa.adID, 
	adz.zoneID, 
	'A'
from 
	dbo.tblSearchAdsTLA tla
	inner join dbo.tblSearchAds sads
		on sads.searchAdID = tla.searchAdID
	inner join membercentral.membercentral.dbo.ad_zones adz
		on adz.sitecode = tla.orgcode
	inner join membercentral.membercentral.dbo.ad_zoneTypes adzt
		on adzt.zoneTypeID = adz.zoneTypeID
		and adzt.zoneTypeName = 'Search'
	inner join membercentral.membercentral.dbo.ad_ads aa
		on cast (aa.imageUrl as int) = tla.searchAdID
			and aa.advertiserID = @advertiserID

insert into membercentral.platformstats.dbo.ad_adPlacementRotation (
	adPlacementID
)
select 
	adPlacementID
from
	membercentral.membercentral.dbo.ad_adPlacements ap
	inner join membercentral.membercentral.dbo.ad_ads aa on
		aa.adID = ap.adID
		and aa.advertiserID = @advertiserID

update
	membercentral.membercentral.dbo.ad_ads
set
	imageURL = '/assets/common/searchAds/' + imageURL + '.jpg'
where
	advertiserID = @advertiserID

GO

