BEGIN TRANSACTION
GO
ALTER TABLE dbo.sites ADD
	enableMobile bit NOT NULL CONSTRAINT DF_sites_enableMobile DEFAULT 0
GO
COMMIT

CREATE TABLE dbo.cms_menus
	(
	menuID int NOT NULL IDENTITY (1, 1),
	siteID int NOT NULL,
	menuName varchar(200) NOT NULL,
	menuCode varchar(50) NULL,
	uid uniqueidentifier NOT NULL,
	contentID int NOT NULL
	)  ON [PRIMARY]
GO
ALTER TABLE dbo.cms_menus ADD CONSTRAINT
	DF_cms_menus_menuUID DEFAULT newID() FOR uid
GO
ALTER TABLE dbo.cms_menus ADD CONSTRAINT
	FK_cms_menus_cms_content FOREIGN KEY
	(
	contentID
	) REFERENCES dbo.cms_content
	(
	contentID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.cms_menus ADD CONSTRAINT
	FK_cms_menus_sites FOREIG<PERSON>EY
	(
	siteID
	) REFERENCES dbo.sites
	(
	siteID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.cms_menus  ADD CONSTRAINT
	PK_cms_menus PRIMARY KEY CLUSTERED 
	(
	menuID
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

GO


BEGIN TRANSACTION
GO
ALTER TABLE dbo.cms_pages ADD
	ovTemplateIDMobile int NULL
GO
ALTER TABLE dbo.cms_pages ADD CONSTRAINT
	FK_cms_pages_cms_pageTemplates FOREIGN KEY
	(
	ovTemplateIDMobile
	) REFERENCES dbo.cms_pageTemplates
	(
	templateID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
COMMIT
GO
BEGIN TRANSACTION
GO
ALTER TABLE dbo.cms_pageSections ADD
	ovTemplateIDMobile int NULL
GO
ALTER TABLE dbo.cms_pageSections ADD CONSTRAINT
	FK_cms_pageSections_cms_pageTemplates FOREIGN KEY
	(
	ovTemplateIDMobile
	) REFERENCES dbo.cms_pageTemplates
	(
	templateID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
COMMIT
GO

BEGIN TRANSACTION
GO
CREATE TABLE dbo.cms_menuUsageTypes
	(
	usageTypeID int NOT NULL IDENTITY (1, 1),
	usageName varchar(50) NOT NULL,
	usageCode varchar(20) NOT NULL
	)  ON [PRIMARY]
GO
ALTER TABLE dbo.cms_menuUsageTypes ADD CONSTRAINT
	PK_cms_menuUsageTypes PRIMARY KEY CLUSTERED 
	(
	usageTypeID
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

GO
COMMIT
GO




BEGIN TRANSACTION
GO
CREATE TABLE dbo.cms_pageTemplateMenuUsageTypes 
	(
	templateUsageTypeID int NOT NULL IDENTITY (1, 1),
	templateID int NOT NULL ,
	usageTypeID int NOT NULL ,
	usageDescription varchar(100) NULL,
	)  ON [PRIMARY]
GO
ALTER TABLE dbo.cms_pageTemplateMenuUsageTypes  ADD CONSTRAINT
	PK_cms_pageTemplateMenuUsageTypes PRIMARY KEY CLUSTERED 
	(
	templateUsageTypeID
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

GO
ALTER TABLE dbo.cms_pageTemplateMenuUsageTypes ADD CONSTRAINT
	FK_cms_pageTemplateMenuUsageTypes_cms_pageTemplates FOREIGN KEY
	(
	templateID
	) REFERENCES dbo.cms_pageTemplates
	(
	templateID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.cms_pageTemplateMenuUsageTypes ADD CONSTRAINT
	FK_cms_pageTemplateMenuUsageTypes_cms_menuUsageTypes FOREIGN KEY
	(
	usageTypeID
	) REFERENCES dbo.cms_menuUsageTypes
	(
	usageTypeID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
COMMIT
GO






BEGIN TRANSACTION
GO
CREATE TABLE dbo.cms_menuUsages  
	(
	useID int NOT NULL IDENTITY (1, 1),
	menuID int NOT NULL ,
	templateUsageTypeID int NOT NULL ,
	usedBySiteResourceID int NULL,
	)  ON [PRIMARY]
GO
ALTER TABLE dbo.cms_menuUsages  ADD CONSTRAINT
	PK_cms_menuUsages PRIMARY KEY CLUSTERED 
	(
	useID
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

GO
ALTER TABLE dbo.cms_menuUsages ADD CONSTRAINT
	FK_cms_menuUsages_cms_menus FOREIGN KEY
	(
	menuID
	) REFERENCES dbo.cms_menus
	(
	menuID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.cms_menuUsages ADD CONSTRAINT
	FK_cms_menuUsages_cms_pageTemplateMenuUsageTypes FOREIGN KEY
	(
	templateUsageTypeID
	) REFERENCES dbo.cms_pageTemplateMenuUsageTypes
	(
	templateUsageTypeID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO

ALTER TABLE dbo.cms_menuUsages ADD CONSTRAINT
	FK_cms_menuUsages_cms_siteResources FOREIGN KEY
	(
	usedBySiteResourceID
	) REFERENCES dbo.cms_siteResources
	(
	siteResourceID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
COMMIT
GO





insert into cms_menuUsageTypes (usageName, usageCode) values ('Primary Navigation','primaryNav')
insert into cms_menuUsageTypes (usageName, usageCode) values ('Secondary Navigation','secondaryNav')
insert into cms_menuUsageTypes (usageName, usageCode) values ('Mobile Navigation','mobileNav')
GO


USE [memberCentral]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROC [dbo].[cms_updateMenu]
@menuID int,
@templateUsageTypeID int,
@siteResourceID int
AS

-- Perform lazy menu update
DELETE FROM cms_menuUsages 
WHERE 
	templateUsageTypeID = @templateUsageTypeID
	AND	usedBySiteResourceID = @siteResourceID
	
if @menuID > 0 AND @templateUsageTypeID > 0 and @siteResourceID > 0 begin
	insert cms_menuUsages(menuID, templateUsageTypeID, usedBySiteResourceID)
	values (@menuID, @templateUsageTypeID, @siteResourceID)
end

-- normal exit
RETURN 0
GO
USE [memberCentral]
GO

ALTER TABLE dbo.cms_menuUsages ADD
	isMobileUsage bit NOT NULL
GO

USE [memberCentral]
GO

ALTER PROC [dbo].[cms_updateMenu]
@menuID int,
@templateUsageTypeID int,
@siteResourceID int,
@isMobileUsage bit
AS

-- Perform lazy menu update
DELETE FROM cms_menuUsages 
WHERE 
	templateUsageTypeID = @templateUsageTypeID
	AND	usedBySiteResourceID = @siteResourceID
	and isMobileUsage = @isMobileUsage
	
if @menuID > 0 AND @templateUsageTypeID > 0 and @siteResourceID > 0 begin
	insert cms_menuUsages(menuID, templateUsageTypeID, usedBySiteResourceID, isMobileUsage)
	values (@menuID, @templateUsageTypeID, @siteResourceID, @isMobileUsage)
end

-- normal exit
RETURN 0
GO



USE [memberCentral]
GO
CREATE PROC [dbo].[cms_updatePage]
@pageID int,
@siteid int,
@siteResourceStatusID int,
@sectionID int,
@ovTemplateID int,
@ovTemplateIDMobile int,
@ovModeID int,
@inheritPlacements bit

AS

BEGIN TRAN

UPDATE cms_pages
SET 
	sectionID = @sectionID,
	ovModeID = @ovModeID,
	ovTemplateID = @ovTemplateID,
	ovTemplateIDMobile = @ovTemplateIDMobile,
	inheritPlacements = @inheritPlacements
WHERE 
	pageID = @pageID
	AND siteID = @siteID


update sr
set siteResourceStatusID = @siteResourceStatusID
from cms_pages p
	inner join cms_siteResources sr
		on p.siteresourceID = sr.siteResourceID
		and p.pageID = @pageID
		and p.siteID = @siteID


IF @@TRANCOUNT > 0 COMMIT TRAN

-- normal exit
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO



USE [memberCentral]
GO
CREATE PROC [dbo].[cms_updatePageSection]
@sectionID int,
@siteid int,
@siteResourceStatusID int,
@parentSectionID int,
@sectionName varchar(50),
@sectionCode varchar(30),
@ovTemplateID int,
@ovTemplateIDMobile int,
@ovModeID int,
@inheritPlacements bit

AS

BEGIN TRAN

UPDATE cms_pageSections
SET 
	parentSectionID = @parentSectionID,
	sectionName = @sectionName,
	sectionCode = @sectionCode,
	ovModeID = @ovModeID,
	ovTemplateID = @ovTemplateID,
	ovTemplateIDMobile = @ovTemplateIDMobile,
	inheritPlacements = @inheritPlacements
WHERE 
	sectionID = @sectionID
	AND siteID = @siteID


update sr
set siteResourceStatusID = @siteResourceStatusID
from cms_pageSections ps
	inner join cms_siteResources sr
		on ps.siteresourceID = sr.siteResourceID
		and ps.sectionID = @sectionID
		and ps.siteID = @siteID


IF @@TRANCOUNT > 0 COMMIT TRAN

-- normal exit
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO



USE [memberCentral]
GO
CREATE TABLE [dbo].[cache_cms_recursivePageSections](
	[autoID] [int] IDENTITY(1,1) NOT NULL,
	[siteID] [int] NOT NULL,
	[startSectionID] [int] NOT NULL,
	[sectionID] [int] NOT NULL,
	[includePlacements] [bit] NOT NULL,
	[depth] [int] NOT NULL,
 CONSTRAINT [PK_cache_cms_recursivePageSections] PRIMARY KEY CLUSTERED 
(
	[autoID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE [dbo].[cache_cms_recursivePageSections]  WITH CHECK ADD  CONSTRAINT [FK_cache_cms_recursivePageSections_cms_pageSections] FOREIGN KEY([startSectionID])
REFERENCES [dbo].[cms_pageSections] ([sectionID])
GO
ALTER TABLE [dbo].[cache_cms_recursivePageSections] CHECK CONSTRAINT [FK_cache_cms_recursivePageSections_cms_pageSections]
GO
ALTER TABLE [dbo].[cache_cms_recursivePageSections]  WITH CHECK ADD  CONSTRAINT [FK_cache_cms_recursivePageSections_cms_pageSections1] FOREIGN KEY([sectionID])
REFERENCES [dbo].[cms_pageSections] ([sectionID])
GO
ALTER TABLE [dbo].[cache_cms_recursivePageSections] CHECK CONSTRAINT [FK_cache_cms_recursivePageSections_cms_pageSections1]
GO
ALTER TABLE [dbo].[cache_cms_recursivePageSections]  WITH CHECK ADD  CONSTRAINT [FK_cache_cms_recursivePageSections_sites] FOREIGN KEY([siteID])
REFERENCES [dbo].[sites] ([siteID])
GO
ALTER TABLE [dbo].[cache_cms_recursivePageSections] CHECK CONSTRAINT [FK_cache_cms_recursivePageSections_sites]


GO

USE [memberCentral]
GO
CREATE PROCEDURE [dbo].[cache_cms_updateRecursivePageSections] 
@restrictToSiteID int = NULL,
@restrictToSectionID int = NULL

AS



declare @updatedCache TABLE (id int IDENTITY(1,1) PRIMARY KEY, siteID int, startSectionID int,sectionID int, includeplacements bit, Depth int)
declare @itemsToAdd TABLE (id int IDENTITY(1,1) PRIMARY KEY, siteID int, startSectionID int,sectionID int, includeplacements bit, Depth int)
declare @itemsToDelete TABLE (autoid int)


;WITH CTE (siteID, startSectionID,sectionID, parentSectionID, inheritPlacements, includeplacements, Depth)
AS	
(
	SELECT siteID, sectionID as startSectionID, sectionID, parentSectionID,inheritPlacements, cast(1 as bit) as includePlacements, 0 AS Depth
	FROM dbo.cms_pageSections 
	WHERE siteID = isnull(@restrictToSiteID,siteID)
	and sectionID =  isnull(@restrictToSectionID,sectionID)
	UNION ALL
	SELECT s.siteID, cte.startSectionID, s.sectionID, s.parentSectionID,  (cte.inheritPlacements & s.inheritPlacements) as inheritPlacements, cte.inheritPlacements as includeplacements, CTE.Depth - 1 AS Depth
	FROM dbo.cms_pageSections as s
	JOIN CTE ON s.sectionID = CTE.parentSectionID
)

insert into @updatedCache (siteID, startSectionID,sectionID, includeplacements, Depth)
select siteID, startSectionID, sectionID,includeplacements, Depth = Depth + count(*) over (partition by startsectionID)
from CTE
option(recompile)

insert into @itemsToAdd (siteID, startSectionID,sectionID, includeplacements, Depth)
select siteID, startSectionID,sectionID, includeplacements, Depth
from @updatedCache uc
except
select siteID, startSectionID,sectionID, includeplacements, Depth
from dbo.cache_cms_recursivePageSections rps

insert into @itemsToDelete (autoID)
select autoID
from dbo.cache_cms_recursivePageSections rps
inner join (
	select siteID, startSectionID,sectionID, includeplacements, Depth
	from dbo.cache_cms_recursivePageSections
	WHERE siteID = isnull(@restrictToSiteID,siteID)
	and sectionID =  isnull(@restrictToSectionID,sectionID)
	except
	select siteID, startSectionID,sectionID, includeplacements, Depth
	from @updatedCache uc
) as rowsToDelete
	on rowsToDelete.siteID = rps.siteID
	and rowsToDelete.startSectionID = rps.startSectionID
	and rowsToDelete.sectionID = rps.sectionID
	and rowsToDelete.includeplacements = rps.includeplacements
	and rowsToDelete.Depth = rps.Depth

BEGIN TRAN

if exists (select top 1 autoID from @itemsToDelete)
	delete rps
	from dbo.cache_cms_recursivePageSections rps
	inner join @itemsToDelete d
		on d.autoID = rps.autoID
	IF @@ERROR <> 0 GOTO on_error


if exists (select top 1 siteID from @itemsToAdd)
	insert into dbo.cache_cms_recursivePageSections (siteID, startSectionID,sectionID, includeplacements, Depth)
	select siteID, startSectionID,sectionID, includeplacements, Depth
	from @itemsToAdd
	IF @@ERROR <> 0 GOTO on_error

IF @@TRANCOUNT > 0 COMMIT TRAN

RETURN 0

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO
USE [memberCentral]
GO
/****** Object:  StoredProcedure [dbo].[cms_updatePageSection]    Script Date: 05/14/2013 10:52:53 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROC [dbo].[cms_updatePageSection]
@sectionID int,
@siteid int,
@siteResourceStatusID int,
@parentSectionID int,
@sectionName varchar(50),
@sectionCode varchar(30),
@ovTemplateID int,
@ovTemplateIDMobile int,
@ovModeID int,
@inheritPlacements bit

AS

DECLARE @currentParentSection int, @currentInheritPlacements bit, @rc int
select @currentParentSection=parentSectionID, @currentInheritPlacements=inheritPlacements
from cms_pageSections


BEGIN TRAN

UPDATE cms_pageSections
SET 
	parentSectionID = @parentSectionID,
	sectionName = @sectionName,
	sectionCode = @sectionCode,
	ovModeID = @ovModeID,
	ovTemplateID = @ovTemplateID,
	ovTemplateIDMobile = @ovTemplateIDMobile,
	inheritPlacements = @inheritPlacements
WHERE 
	sectionID = @sectionID
	AND siteID = @siteID
IF @@ERROR <> 0 GOTO on_error

update sr
set siteResourceStatusID = @siteResourceStatusID
from cms_pageSections ps
	inner join cms_siteResources sr
		on ps.siteresourceID = sr.siteResourceID
		and ps.sectionID = @sectionID
		and ps.siteID = @siteID
IF @@ERROR <> 0 GOTO on_error


if (@currentParentSection <> @parentSectionID or @currentInheritPlacements <> @inheritPlacements)
BEGIN
	exec @rc = [dbo].[cache_cms_updateRecursivePageSections] 
		@restrictToSiteID = @siteID,
		@restrictToSectionID = NULL
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
END

IF @@TRANCOUNT > 0 COMMIT TRAN

-- normal exit
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO
USE [memberCentral]
GO
/****** Object:  StoredProcedure [dbo].[cms_createPageSection]    Script Date: 05/14/2013 11:15:43 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

ALTER PROC [dbo].[cms_createPageSection]
@siteID int,
@sectionResourceTypeID int,
@ovTemplateID int,
@ovTemplateIDMobile int,
@ovModeID int,
@parentSectionID int,
@sectionName varchar(50),
@sectionCode varchar(30),
@inheritPlacements bit,
@sectionID int OUTPUT

AS

-- ensure @sectionID is null (can be passed in)
SELECT @sectionID = null
	
declare @sectionResourceID int, @siteResourceStatusID int, @rc int
select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

BEGIN TRAN

-- sectionCode must be A-Z 0-9 only and be unique in site
select @sectionCode = dbo.fn_regExReplace(isnull(@sectionCode,''),'[^A-Za-z0-9]','')
IF EXISTS (select sectionID from dbo.cms_pageSections ps inner join cms_siteResources sr on ps.siteResourceID = sr.siteResourceID inner join cms_siteResourceStatuses srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc <> 'Deleted' where ps.siteID = @siteID and ps.sectionCode = @sectionCode and ps.sectioncode <> '')
	GOTO on_error

exec dbo.cms_createSiteResource
	@resourceTypeID = @sectionResourceTypeID,
	@siteResourceStatusID = @siteResourceStatusID,
	@siteID = @siteid,
	@isVisible = 1,
	@parentSiteResourceID = null,
	@siteResourceID   = @sectionResourceID OUTPUT


INSERT INTO dbo.cms_pageSections (
	siteID, 
	ovTemplateID, 
	ovTemplateIDMobile, 
	ovModeID, 
	parentSectionID, 
	sectionName, 
	sectionCode, 
	inheritPlacements,
	siteResourceID)
VALUES (
	@siteID, 
	@ovTemplateID, 
	@ovTemplateIDMobile, 
	@ovModeID, 
	@parentSectionID, 
	@sectionName, 
	nullif(@sectionCode,''), 
	@inheritPlacements,
	@sectionResourceID)
	IF @@ERROR <> 0 GOTO on_error
	select @sectionID = SCOPE_IDENTITY()

	print 'section create success: ' + @sectionName


	exec @rc = [dbo].[cache_cms_updateRecursivePageSections] 
		@restrictToSiteID = @siteID,
		@restrictToSectionID = @sectionID
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

IF @@TRANCOUNT > 0 COMMIT TRAN

RETURN 0

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @sectionID = 0
	print 'section create failed: ' + @sectionName
	RETURN -1
GO
USE [memberCentral]
GO
ALTER PROC [dbo].[cms_deletePageSection]
@siteID int,
@sectionID int

AS

BEGIN TRAN

declare @siteResourceID int, @rc int
select @siteResourceID = siteResourceID
	from dbo.cms_pageSections
	WHERE siteID = @siteID
	AND sectionID = @sectionID

exec @rc = [dbo].[cache_cms_updateRecursivePageSections] 
	@restrictToSiteID = @siteID,
	@restrictToSectionID = @sectionID
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

delete from dbo.cache_cms_recursivePageSections
where startSectionID = @sectionID or sectionID = @sectionID
IF @@ERROR <> 0 GOTO on_error

DELETE FROM dbo.cms_pageSections
WHERE siteID = @siteID
AND sectionID = @sectionID
	IF @@ERROR <> 0 GOTO on_error

DELETE FROM dbo.cms_siteResources
WHERE siteID = @siteID
AND siteResourceID = @siteResourceID
	IF @@ERROR <> 0 GOTO on_error

exec @rc = [dbo].[cache_cms_updateRecursivePageSections] 
	@restrictToSiteID = @siteID,
	@restrictToSectionID = null
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


IF @@TRANCOUNT > 0 COMMIT TRAN

RETURN 0

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

USE [memberCentral]
GO
/****** Object:  Table [dbo].[cache_cms_derivedMenuUsages]    Script Date: 05/14/2013 18:36:51 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[cache_cms_derivedMenuUsages](
	[autoID] [int] IDENTITY(1,1) NOT NULL,
	[sectionID] [int] NOT NULL,
	[ovModeID] [int] NOT NULL,
	[ovTemplateID] [int] NOT NULL,
	[ovTemplateIDMobile] [int] NULL,
 CONSTRAINT [PK_cache_cms_derivedMenuUsages] PRIMARY KEY CLUSTERED 
(
	[autoID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE [dbo].[cache_cms_derivedMenuUsages]  WITH CHECK ADD  CONSTRAINT [FK_cache_cms_derivedMenuUsages_cms_pageSections] FOREIGN KEY([sectionID])
REFERENCES [dbo].[cms_pageSections] ([sectionID])
GO
ALTER TABLE [dbo].[cache_cms_derivedMenuUsages] CHECK CONSTRAINT [FK_cache_cms_derivedMenuUsages_cms_pageSections]
GO
ALTER TABLE [dbo].[cache_cms_derivedMenuUsages]  WITH CHECK ADD  CONSTRAINT [FK_cache_cms_derivedMenuUsages_cms_pageTemplates] FOREIGN KEY([ovTemplateID])
REFERENCES [dbo].[cms_pageTemplates] ([templateID])
GO
ALTER TABLE [dbo].[cache_cms_derivedMenuUsages] CHECK CONSTRAINT [FK_cache_cms_derivedMenuUsages_cms_pageTemplates]
GO
ALTER TABLE [dbo].[cache_cms_derivedMenuUsages]  WITH CHECK ADD  CONSTRAINT [FK_cache_cms_derivedMenuUsages_cms_pageTemplates1] FOREIGN KEY([ovTemplateIDMobile])
REFERENCES [dbo].[cms_pageTemplates] ([templateID])
GO
ALTER TABLE [dbo].[cache_cms_derivedMenuUsages] CHECK CONSTRAINT [FK_cache_cms_derivedMenuUsages_cms_pageTemplates1]
GO

BEGIN TRANSACTION
GO
COMMIT
BEGIN TRANSACTION
GO
ALTER TABLE dbo.cache_cms_derivedMenuUsages
	DROP CONSTRAINT FK_cache_cms_derivedMenuUsages_cms_pageTemplates
GO
ALTER TABLE dbo.cache_cms_derivedMenuUsages
	DROP CONSTRAINT FK_cache_cms_derivedMenuUsages_cms_pageTemplates1
GO
COMMIT
BEGIN TRANSACTION
GO
ALTER TABLE dbo.cache_cms_derivedMenuUsages
	DROP CONSTRAINT FK_cache_cms_derivedMenuUsages_cms_pageSections
GO
COMMIT
BEGIN TRANSACTION
GO
CREATE TABLE dbo.Tmp_cache_cms_derivedMenuUsages
	(
	autoID int NOT NULL IDENTITY (1, 1),
	siteID int NOT NULL,
	sectionID int NOT NULL,
	ovModeID int NOT NULL,
	ovTemplateID int NOT NULL,
	ovTemplateIDMobile int NULL
	)  ON [PRIMARY]
GO
SET IDENTITY_INSERT dbo.Tmp_cache_cms_derivedMenuUsages ON
GO
IF EXISTS(SELECT * FROM dbo.cache_cms_derivedMenuUsages)
	 EXEC('INSERT INTO dbo.Tmp_cache_cms_derivedMenuUsages (autoID, sectionID, ovModeID, ovTemplateID, ovTemplateIDMobile)
		SELECT autoID, sectionID, ovModeID, ovTemplateID, ovTemplateIDMobile FROM dbo.cache_cms_derivedMenuUsages WITH (HOLDLOCK TABLOCKX)')
GO
SET IDENTITY_INSERT dbo.Tmp_cache_cms_derivedMenuUsages OFF
GO
DROP TABLE dbo.cache_cms_derivedMenuUsages
GO
EXECUTE sp_rename N'dbo.Tmp_cache_cms_derivedMenuUsages', N'cache_cms_derivedMenuUsages', 'OBJECT' 
GO
ALTER TABLE dbo.cache_cms_derivedMenuUsages ADD CONSTRAINT
	PK_cache_cms_derivedMenuUsages PRIMARY KEY CLUSTERED 
	(
	autoID
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

GO
ALTER TABLE dbo.cache_cms_derivedMenuUsages ADD CONSTRAINT
	FK_cache_cms_derivedMenuUsages_cms_pageSections FOREIGN KEY
	(
	sectionID
	) REFERENCES dbo.cms_pageSections
	(
	sectionID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.cache_cms_derivedMenuUsages ADD CONSTRAINT
	FK_cache_cms_derivedMenuUsages_cms_pageTemplates FOREIGN KEY
	(
	ovTemplateID
	) REFERENCES dbo.cms_pageTemplates
	(
	templateID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.cache_cms_derivedMenuUsages ADD CONSTRAINT
	FK_cache_cms_derivedMenuUsages_cms_pageTemplates1 FOREIGN KEY
	(
	ovTemplateIDMobile
	) REFERENCES dbo.cms_pageTemplates
	(
	templateID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.cache_cms_derivedMenuUsages ADD CONSTRAINT
	FK_cache_cms_derivedMenuUsages_sites FOREIGN KEY
	(
	siteID
	) REFERENCES dbo.sites
	(
	siteID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
COMMIT
GO


BEGIN TRANSACTION
GO
ALTER TABLE dbo.cache_cms_derivedMenuUsages
	DROP CONSTRAINT FK_cache_cms_derivedMenuUsages_sites
GO
COMMIT
BEGIN TRANSACTION
GO
ALTER TABLE dbo.cache_cms_derivedMenuUsages
	DROP CONSTRAINT FK_cache_cms_derivedMenuUsages_cms_pageTemplates
GO
ALTER TABLE dbo.cache_cms_derivedMenuUsages
	DROP CONSTRAINT FK_cache_cms_derivedMenuUsages_cms_pageTemplates1
GO
COMMIT
BEGIN TRANSACTION
GO
ALTER TABLE dbo.cache_cms_derivedMenuUsages
	DROP CONSTRAINT FK_cache_cms_derivedMenuUsages_cms_pageSections
GO
COMMIT
BEGIN TRANSACTION
GO
CREATE TABLE dbo.Tmp_cache_cms_derivedMenuUsages
	(
	autoID int NOT NULL IDENTITY (1, 1),
	siteID int NOT NULL,
	sectionID int NOT NULL,
	ovModeID int NULL,
	ovTemplateID int NULL,
	ovTemplateIDMobile int NULL
	)  ON [PRIMARY]
GO
SET IDENTITY_INSERT dbo.Tmp_cache_cms_derivedMenuUsages ON
GO
IF EXISTS(SELECT * FROM dbo.cache_cms_derivedMenuUsages)
	 EXEC('INSERT INTO dbo.Tmp_cache_cms_derivedMenuUsages (autoID, siteID, sectionID, ovModeID, ovTemplateID, ovTemplateIDMobile)
		SELECT autoID, siteID, sectionID, ovModeID, ovTemplateID, ovTemplateIDMobile FROM dbo.cache_cms_derivedMenuUsages WITH (HOLDLOCK TABLOCKX)')
GO
SET IDENTITY_INSERT dbo.Tmp_cache_cms_derivedMenuUsages OFF
GO
DROP TABLE dbo.cache_cms_derivedMenuUsages
GO
EXECUTE sp_rename N'dbo.Tmp_cache_cms_derivedMenuUsages', N'cache_cms_derivedMenuUsages', 'OBJECT' 
GO
ALTER TABLE dbo.cache_cms_derivedMenuUsages ADD CONSTRAINT
	PK_cache_cms_derivedMenuUsages PRIMARY KEY CLUSTERED 
	(
	autoID
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

GO
ALTER TABLE dbo.cache_cms_derivedMenuUsages ADD CONSTRAINT
	FK_cache_cms_derivedMenuUsages_cms_pageSections FOREIGN KEY
	(
	sectionID
	) REFERENCES dbo.cms_pageSections
	(
	sectionID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.cache_cms_derivedMenuUsages ADD CONSTRAINT
	FK_cache_cms_derivedMenuUsages_cms_pageTemplates FOREIGN KEY
	(
	ovTemplateID
	) REFERENCES dbo.cms_pageTemplates
	(
	templateID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.cache_cms_derivedMenuUsages ADD CONSTRAINT
	FK_cache_cms_derivedMenuUsages_cms_pageTemplates1 FOREIGN KEY
	(
	ovTemplateIDMobile
	) REFERENCES dbo.cms_pageTemplates
	(
	templateID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.cache_cms_derivedMenuUsages ADD CONSTRAINT
	FK_cache_cms_derivedMenuUsages_sites FOREIGN KEY
	(
	siteID
	) REFERENCES dbo.sites
	(
	siteID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
COMMIT
GO
USE [memberCentral]
GO
CREATE PROCEDURE [dbo].[cache_cms_updateDerivedMenuUsages] 
@restrictToSiteID int = NULL, 
@restrictToSectionID int = NULL

AS

declare @defaultModeID int

declare @updatedCache TABLE (id int IDENTITY(1,1) PRIMARY KEY, siteID int, sectionID int, ovModeID int, ovTemplateID int, ovTemplateIDMobile int)
declare @itemsToAdd TABLE (id int IDENTITY(1,1) PRIMARY KEY, siteID int, sectionID int, ovModeID int, ovTemplateID int, ovTemplateIDMobile int)
declare @itemsToDelete TABLE (autoid int)

select @defaultModeID = modeID from cms_pageModes where modeName = 'Normal'


;WITH sections AS (
	--initialization
	SELECT ps.siteID, sectionID as originalSectionID, ovTemplateID, ovModeID, ovTemplateIDMobile, sectionID as sectionid, parentSectionID as parentSectionID
	FROM dbo.cms_pageSections ps
	where siteID = isnull(@restrictToSiteID,ps.siteID)
	and ps.sectionID = isnull(@restrictToSectionID,ps.sectionID)
		UNION ALL
	--recursive execution
	SELECT ps.siteID,sections.originalSectionID, isnull(sections.ovTemplateID,ps.ovTemplateID) as templateID, isnull(sections.ovModeID, ps.ovModeID) as ovModeID, isnull(sections.ovTemplateIDMobile,ps.ovTemplateIDMobile) as ovTemplateIDMobile, ps.sectionid, ps.parentSectionID
	FROM dbo.cms_pageSections as ps
	JOIN sections ON ps.sectionID = sections.parentSectionID 
)
insert into @updatedCache (siteID, sectionID, ovModeID, ovTemplateID, ovTemplateIDMobile)
select siteID, originalSectionID as sectionID, isnull(ovModeID,@defaultModeID), ovTemplateID, ovTemplateIDMobile
from sections
where parentSectionID is null



insert into @itemsToAdd (siteID, sectionID, ovModeID, ovTemplateID, ovTemplateIDMobile)
select siteID, sectionID, ovModeID, ovTemplateID, ovTemplateIDMobile
from @updatedCache uc
except
select siteID, sectionID, ovModeID, ovTemplateID, ovTemplateIDMobile
from dbo.cache_cms_derivedMenuUsages dmu

insert into @itemsToDelete (autoID)
select autoID
from dbo.cache_cms_derivedMenuUsages dmu
inner join (
	select siteID, sectionID, ovModeID, ovTemplateID, ovTemplateIDMobile
	from dbo.cache_cms_derivedMenuUsages
	WHERE siteID = isnull(@restrictToSiteID,siteID)
	and sectionID =  isnull(@restrictToSectionID,sectionID)
	except
	select siteID, sectionID, ovModeID, ovTemplateID, ovTemplateIDMobile
	from @updatedCache uc
) as rowsToDelete
	on rowsToDelete.siteID = dmu.siteID
	and rowsToDelete.sectionID = dmu.sectionID
	and rowsToDelete.ovModeID = dmu.ovModeID
	and isnull(rowsToDelete.ovTemplateID,0) = isnull(dmu.ovTemplateID,0)
	and isnull(rowsToDelete.ovTemplateIDMobile,0) = isnull(dmu.ovTemplateIDMobile,0)

BEGIN TRAN

if exists (select top 1 autoID from @itemsToDelete)
	delete dmu
	from dbo.cache_cms_derivedMenuUsages dmu
	inner join @itemsToDelete d
		on d.autoID = dmu.autoID
	IF @@ERROR <> 0 GOTO on_error


if exists (select top 1 siteID from @itemsToAdd)
	insert into dbo.cache_cms_derivedMenuUsages (siteID, sectionID, ovModeID, ovTemplateID, ovTemplateIDMobile)
	select siteID, sectionID, ovModeID, ovTemplateID, ovTemplateIDMobile
	from @itemsToAdd
	IF @@ERROR <> 0 GOTO on_error

IF @@TRANCOUNT > 0 COMMIT TRAN

RETURN 0

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1

GO

USE [memberCentral]
GO
ALTER PROC [dbo].[cms_createPageSection]
@siteID int,
@sectionResourceTypeID int,
@ovTemplateID int,
@ovTemplateIDMobile int,
@ovModeID int,
@parentSectionID int,
@sectionName varchar(50),
@sectionCode varchar(30),
@inheritPlacements bit,
@sectionID int OUTPUT

AS

-- ensure @sectionID is null (can be passed in)
SELECT @sectionID = null
	
declare @sectionResourceID int, @siteResourceStatusID int, @rc int
select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

BEGIN TRAN

-- sectionCode must be A-Z 0-9 only and be unique in site
select @sectionCode = dbo.fn_regExReplace(isnull(@sectionCode,''),'[^A-Za-z0-9]','')
IF EXISTS (select sectionID from dbo.cms_pageSections ps inner join cms_siteResources sr on ps.siteResourceID = sr.siteResourceID inner join cms_siteResourceStatuses srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc <> 'Deleted' where ps.siteID = @siteID and ps.sectionCode = @sectionCode and ps.sectioncode <> '')
	GOTO on_error

exec dbo.cms_createSiteResource
	@resourceTypeID = @sectionResourceTypeID,
	@siteResourceStatusID = @siteResourceStatusID,
	@siteID = @siteid,
	@isVisible = 1,
	@parentSiteResourceID = null,
	@siteResourceID   = @sectionResourceID OUTPUT


INSERT INTO dbo.cms_pageSections (
	siteID, 
	ovTemplateID, 
	ovTemplateIDMobile, 
	ovModeID, 
	parentSectionID, 
	sectionName, 
	sectionCode, 
	inheritPlacements,
	siteResourceID)
VALUES (
	@siteID, 
	@ovTemplateID, 
	@ovTemplateIDMobile, 
	@ovModeID, 
	@parentSectionID, 
	@sectionName, 
	nullif(@sectionCode,''), 
	@inheritPlacements,
	@sectionResourceID)
	IF @@ERROR <> 0 GOTO on_error
	select @sectionID = SCOPE_IDENTITY()

	print 'section create success: ' + @sectionName


	exec @rc = [dbo].[cache_cms_updateRecursivePageSections] 
		@restrictToSiteID = @siteID,
		@restrictToSectionID = @sectionID
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = [dbo].[cache_cms_updateDerivedMenuUsages] 
		@restrictToSiteID = @siteID,
		@restrictToSectionID = @sectionID
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


IF @@TRANCOUNT > 0 COMMIT TRAN

RETURN 0

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @sectionID = 0
	print 'section create failed: ' + @sectionName
	RETURN -1
GO
USE [memberCentral]
GO
ALTER PROC [dbo].[cms_updatePageSection]
@sectionID int,
@siteid int,
@siteResourceStatusID int,
@parentSectionID int,
@sectionName varchar(50),
@sectionCode varchar(30),
@ovTemplateID int,
@ovTemplateIDMobile int,
@ovModeID int,
@inheritPlacements bit

AS

DECLARE @currentParentSection int, @currentInheritPlacements bit, @rc int
select @currentParentSection=parentSectionID, @currentInheritPlacements=inheritPlacements
from cms_pageSections


BEGIN TRAN

UPDATE cms_pageSections
SET 
	parentSectionID = @parentSectionID,
	sectionName = @sectionName,
	sectionCode = @sectionCode,
	ovModeID = @ovModeID,
	ovTemplateID = @ovTemplateID,
	ovTemplateIDMobile = @ovTemplateIDMobile,
	inheritPlacements = @inheritPlacements
WHERE 
	sectionID = @sectionID
	AND siteID = @siteID
IF @@ERROR <> 0 GOTO on_error

update sr
set siteResourceStatusID = @siteResourceStatusID
from cms_pageSections ps
	inner join cms_siteResources sr
		on ps.siteresourceID = sr.siteResourceID
		and ps.sectionID = @sectionID
		and ps.siteID = @siteID
IF @@ERROR <> 0 GOTO on_error


if (@currentParentSection <> @parentSectionID or @currentInheritPlacements <> @inheritPlacements)
BEGIN
	exec @rc = [dbo].[cache_cms_updateRecursivePageSections] 
		@restrictToSiteID = @siteID,
		@restrictToSectionID = NULL
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
END

exec @rc = [dbo].[cache_cms_updateDerivedMenuUsages] 
	@restrictToSiteID = @siteID,
	@restrictToSectionID = NULL
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


IF @@TRANCOUNT > 0 COMMIT TRAN

-- normal exit
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

USE [memberCentral]
GO
ALTER PROC [dbo].[cms_deletePageSection]
@siteID int,
@sectionID int

AS

BEGIN TRAN

declare @siteResourceID int, @rc int
select @siteResourceID = siteResourceID
	from dbo.cms_pageSections
	WHERE siteID = @siteID
	AND sectionID = @sectionID

delete from dbo.cache_cms_recursivePageSections
where startSectionID = @sectionID or sectionID = @sectionID
IF @@ERROR <> 0 GOTO on_error

delete from dbo.cache_cms_derivedMenuUsages
where sectionID = @sectionID
IF @@ERROR <> 0 GOTO on_error

DELETE FROM dbo.cms_pageSections
WHERE siteID = @siteID
AND sectionID = @sectionID
	IF @@ERROR <> 0 GOTO on_error

DELETE FROM dbo.cms_siteResources
WHERE siteID = @siteID
AND siteResourceID = @siteResourceID
	IF @@ERROR <> 0 GOTO on_error

exec @rc = [dbo].[cache_cms_updateRecursivePageSections] 
	@restrictToSiteID = @siteID,
	@restrictToSectionID = null
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

exec @rc = [dbo].[cache_cms_updateDerivedMenuUsages] 
	@restrictToSiteID = @siteID,
	@restrictToSectionID = NULL
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

IF @@TRANCOUNT > 0 COMMIT TRAN

RETURN 0

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

EXECUTE sp_rename N'dbo.cache_cms_derivedMenuUsages', N'cache_cms_derivedPageSectionSettings', 'OBJECT' 
GO
EXECUTE sp_rename N'dbo.cache_cms_updateDerivedMenuUsages', N'cache_cms_updateDerivedPageSectionSettings', 'OBJECT' 
GO



USE [memberCentral]
GO
/****** Object:  StoredProcedure [dbo].[cache_cms_updateDerivedMenuUsages]    Script Date: 05/14/2013 19:14:30 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROCEDURE [dbo].[cache_cms_updateDerivedPageSectionSettings] 
@restrictToSiteID int = NULL, 
@restrictToSectionID int = NULL

AS

declare @defaultModeID int

declare @updatedCache TABLE (id int IDENTITY(1,1) PRIMARY KEY, siteID int, sectionID int, ovModeID int, ovTemplateID int, ovTemplateIDMobile int)
declare @itemsToAdd TABLE (id int IDENTITY(1,1) PRIMARY KEY, siteID int, sectionID int, ovModeID int, ovTemplateID int, ovTemplateIDMobile int)
declare @itemsToDelete TABLE (autoid int)

select @defaultModeID = modeID from cms_pageModes where modeName = 'Normal'


;WITH sections AS (
	--initialization
	SELECT ps.siteID, sectionID as originalSectionID, ovTemplateID, ovModeID, ovTemplateIDMobile, sectionID as sectionid, parentSectionID as parentSectionID
	FROM dbo.cms_pageSections ps
	where siteID = isnull(@restrictToSiteID,ps.siteID)
	and ps.sectionID = isnull(@restrictToSectionID,ps.sectionID)
		UNION ALL
	--recursive execution
	SELECT ps.siteID,sections.originalSectionID, isnull(sections.ovTemplateID,ps.ovTemplateID) as templateID, isnull(sections.ovModeID, ps.ovModeID) as ovModeID, isnull(sections.ovTemplateIDMobile,ps.ovTemplateIDMobile) as ovTemplateIDMobile, ps.sectionid, ps.parentSectionID
	FROM dbo.cms_pageSections as ps
	JOIN sections ON ps.sectionID = sections.parentSectionID 
)
insert into @updatedCache (siteID, sectionID, ovModeID, ovTemplateID, ovTemplateIDMobile)
select siteID, originalSectionID as sectionID, isnull(ovModeID,@defaultModeID), ovTemplateID, ovTemplateIDMobile
from sections
where parentSectionID is null



insert into @itemsToAdd (siteID, sectionID, ovModeID, ovTemplateID, ovTemplateIDMobile)
select siteID, sectionID, ovModeID, ovTemplateID, ovTemplateIDMobile
from @updatedCache uc
except
select siteID, sectionID, ovModeID, ovTemplateID, ovTemplateIDMobile
from dbo.cache_cms_derivedPageSectionSettings dmu

insert into @itemsToDelete (autoID)
select autoID
from dbo.cache_cms_derivedPageSectionSettings dmu
inner join (
	select siteID, sectionID, ovModeID, ovTemplateID, ovTemplateIDMobile
	from dbo.cache_cms_derivedPageSectionSettings
	WHERE siteID = isnull(@restrictToSiteID,siteID)
	and sectionID =  isnull(@restrictToSectionID,sectionID)
	except
	select siteID, sectionID, ovModeID, ovTemplateID, ovTemplateIDMobile
	from @updatedCache uc
) as rowsToDelete
	on rowsToDelete.siteID = dmu.siteID
	and rowsToDelete.sectionID = dmu.sectionID
	and rowsToDelete.ovModeID = dmu.ovModeID
	and isnull(rowsToDelete.ovTemplateID,0) = isnull(dmu.ovTemplateID,0)
	and isnull(rowsToDelete.ovTemplateIDMobile,0) = isnull(dmu.ovTemplateIDMobile,0)

BEGIN TRAN

if exists (select top 1 autoID from @itemsToDelete)
	delete dmu
	from dbo.cache_cms_derivedPageSectionSettings dmu
	inner join @itemsToDelete d
		on d.autoID = dmu.autoID
	IF @@ERROR <> 0 GOTO on_error


if exists (select top 1 siteID from @itemsToAdd)
	insert into dbo.cache_cms_derivedPageSectionSettings (siteID, sectionID, ovModeID, ovTemplateID, ovTemplateIDMobile)
	select siteID, sectionID, ovModeID, ovTemplateID, ovTemplateIDMobile
	from @itemsToAdd
	IF @@ERROR <> 0 GOTO on_error

IF @@TRANCOUNT > 0 COMMIT TRAN

RETURN 0

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1

GO


USE [memberCentral]
GO
ALTER PROC [dbo].[cms_createPageSection]
@siteID int,
@sectionResourceTypeID int,
@ovTemplateID int,
@ovTemplateIDMobile int,
@ovModeID int,
@parentSectionID int,
@sectionName varchar(50),
@sectionCode varchar(30),
@inheritPlacements bit,
@sectionID int OUTPUT

AS

-- ensure @sectionID is null (can be passed in)
SELECT @sectionID = null
	
declare @sectionResourceID int, @siteResourceStatusID int, @rc int
select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

BEGIN TRAN

-- sectionCode must be A-Z 0-9 only and be unique in site
select @sectionCode = dbo.fn_regExReplace(isnull(@sectionCode,''),'[^A-Za-z0-9]','')
IF EXISTS (select sectionID from dbo.cms_pageSections ps inner join cms_siteResources sr on ps.siteResourceID = sr.siteResourceID inner join cms_siteResourceStatuses srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc <> 'Deleted' where ps.siteID = @siteID and ps.sectionCode = @sectionCode and ps.sectioncode <> '')
	GOTO on_error

exec dbo.cms_createSiteResource
	@resourceTypeID = @sectionResourceTypeID,
	@siteResourceStatusID = @siteResourceStatusID,
	@siteID = @siteid,
	@isVisible = 1,
	@parentSiteResourceID = null,
	@siteResourceID   = @sectionResourceID OUTPUT


INSERT INTO dbo.cms_pageSections (
	siteID, 
	ovTemplateID, 
	ovTemplateIDMobile, 
	ovModeID, 
	parentSectionID, 
	sectionName, 
	sectionCode, 
	inheritPlacements,
	siteResourceID)
VALUES (
	@siteID, 
	@ovTemplateID, 
	@ovTemplateIDMobile, 
	@ovModeID, 
	@parentSectionID, 
	@sectionName, 
	nullif(@sectionCode,''), 
	@inheritPlacements,
	@sectionResourceID)
	IF @@ERROR <> 0 GOTO on_error
	select @sectionID = SCOPE_IDENTITY()

	print 'section create success: ' + @sectionName


	exec @rc = [dbo].[cache_cms_updateRecursivePageSections] 
		@restrictToSiteID = @siteID,
		@restrictToSectionID = @sectionID
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = [dbo].cache_cms_updateDerivedPageSectionSettings 
		@restrictToSiteID = @siteID,
		@restrictToSectionID = @sectionID
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


IF @@TRANCOUNT > 0 COMMIT TRAN

RETURN 0

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @sectionID = 0
	print 'section create failed: ' + @sectionName
	RETURN -1
GO
USE [memberCentral]
GO
ALTER PROC [dbo].[cms_updatePageSection]
@sectionID int,
@siteid int,
@siteResourceStatusID int,
@parentSectionID int,
@sectionName varchar(50),
@sectionCode varchar(30),
@ovTemplateID int,
@ovTemplateIDMobile int,
@ovModeID int,
@inheritPlacements bit

AS

DECLARE @currentParentSection int, @currentInheritPlacements bit, @rc int
select @currentParentSection=parentSectionID, @currentInheritPlacements=inheritPlacements
from cms_pageSections


BEGIN TRAN

UPDATE cms_pageSections
SET 
	parentSectionID = @parentSectionID,
	sectionName = @sectionName,
	sectionCode = @sectionCode,
	ovModeID = @ovModeID,
	ovTemplateID = @ovTemplateID,
	ovTemplateIDMobile = @ovTemplateIDMobile,
	inheritPlacements = @inheritPlacements
WHERE 
	sectionID = @sectionID
	AND siteID = @siteID
IF @@ERROR <> 0 GOTO on_error

update sr
set siteResourceStatusID = @siteResourceStatusID
from cms_pageSections ps
	inner join cms_siteResources sr
		on ps.siteresourceID = sr.siteResourceID
		and ps.sectionID = @sectionID
		and ps.siteID = @siteID
IF @@ERROR <> 0 GOTO on_error


if (@currentParentSection <> @parentSectionID or @currentInheritPlacements <> @inheritPlacements)
BEGIN
	exec @rc = [dbo].[cache_cms_updateRecursivePageSections] 
		@restrictToSiteID = @siteID,
		@restrictToSectionID = NULL
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
END

exec @rc = [dbo].cache_cms_updateDerivedPageSectionSettings 
	@restrictToSiteID = @siteID,
	@restrictToSectionID = NULL
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


IF @@TRANCOUNT > 0 COMMIT TRAN

-- normal exit
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

USE [memberCentral]
GO
ALTER PROC [dbo].[cms_deletePageSection]
@siteID int,
@sectionID int

AS

BEGIN TRAN

declare @siteResourceID int, @rc int
select @siteResourceID = siteResourceID
	from dbo.cms_pageSections
	WHERE siteID = @siteID
	AND sectionID = @sectionID

delete from dbo.cache_cms_recursivePageSections
where startSectionID = @sectionID or sectionID = @sectionID
IF @@ERROR <> 0 GOTO on_error

delete from dbo.cache_cms_derivedPageSectionSettings
where sectionID = @sectionID
IF @@ERROR <> 0 GOTO on_error

DELETE FROM dbo.cms_pageSections
WHERE siteID = @siteID
AND sectionID = @sectionID
	IF @@ERROR <> 0 GOTO on_error

DELETE FROM dbo.cms_siteResources
WHERE siteID = @siteID
AND siteResourceID = @siteResourceID
	IF @@ERROR <> 0 GOTO on_error

exec @rc = [dbo].[cache_cms_updateRecursivePageSections] 
	@restrictToSiteID = @siteID,
	@restrictToSectionID = null
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

exec @rc = [dbo].cache_cms_updateDerivedPageSectionSettings 
	@restrictToSiteID = @siteID,
	@restrictToSectionID = null
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


IF @@TRANCOUNT > 0 COMMIT TRAN

RETURN 0

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

USE [memberCentral]
GO
ALTER PROC [dbo].[cms_createApplicationInstance]
@siteid int,
@languageID int,
@sectionID int,
@applicationTypeID int,
@isVisible bit,
@pageName varchar(50),
@pageTitle varchar(200),
@pagedesc varchar(400),
@zoneID int,
@pageTemplateID int,
@pageModeID int,
@pgResourceTypeID int = NULL,
@pgParentResourceID int,
@allowReturnAfterLogin bit,
@applicationInstanceName varchar(100),
@applicationInstanceDesc varchar(200),
@applicationInstanceID int OUTPUT,
@siteResourceID int OUTPUT,
@pageID int OUTPUT

AS

DECLARE @rc int, @appResourceTypeID int, @siteResourceStatusID int, @pzrID int, @pageSiteResourceID int	

SELECT @applicationInstanceID = null, @siteResourceID = NULL

-- make sure we can add instance based on maxInstancesPerSite
declare @maxInstancesPerSite int, @numInstances int
select @maxInstancesPerSite = maxInstancesPerSite from dbo.cms_applicationTypes where applicationTypeID = @applicationTypeID
IF @maxInstancesPerSite is null
	select @maxInstancesPerSite = 1
select @numInstances = count(*)
from dbo.cms_applicationInstances ai
	inner join cms_siteResources sr
		on ai.siteResourceID = sr.siteResourceID
		and ai.siteID = @siteID
		and ai.applicationTypeID = @applicationTypeID
	inner join cms_siteResourceStatuses srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'

-- if we can add an instance
IF @numInstances < @maxInstancesPerSite BEGIN
	print 'passed MaxInstance check: ' + @applicationInstanceName
	-- get resourceType for app created pages and GenericApp
	select @appResourceTypeID = resourceTypeID from dbo.cms_ApplicationTypes where applicationTypeID = @applicationTypeID

	-- get active resource status
	select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

	BEGIN TRAN

		-- create page for app
		EXEC @rc = dbo.cms_createPage 
			@siteid=@siteid, 
			@languageID=@languageID, 
			@resourceTypeID=@pgResourceTypeID, 
			@siteResourceStatusID=@siteResourceStatusID, 
			@pgParentResourceID=@pgParentResourceID, 
			@isVisible=@isVisible, 
			@sectionID=@sectionID, 
			@ovTemplateID=@pageTemplateID, 
			@ovTemplateIDMobile=null, 
			@ovModeID=@pageModeID, 
			@pageName=@pageName, 
			@pageTitle=@pagetitle, 
			@pageDesc=@pagedesc, 
			@keywords=null,
			@inheritPlacements=1,
			@allowReturnAfterLogin=@allowReturnAfterLogin, 
			@checkReservedNames=0, 
			@pageID=@pageID OUTPUT
			
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

		--get Page ResourceID
		select @pageSiteResourceID = siteResourceID
		from cms_pages where pageID = @pageID

		-- create a resourceID for the app instance
		exec @rc = dbo.cms_createSiteResource
			@resourceTypeID = @appResourceTypeID,
			@siteResourceStatusID = @siteResourceStatusID,
			@siteID = @siteid,
			@isVisible = @isVisible,
			@parentSiteResourceID = @pageSiteResourceID,
			@siteResourceID   = @siteResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

		-- add instance
		INSERT INTO dbo.cms_applicationInstances (applicationTypeID, siteID, siteResourceID, applicationInstanceName, applicationInstanceDesc, dateCreated)
		VALUES (@applicationTypeID, @siteID, @siteResourceID, @applicationInstanceName, @applicationInstanceDesc, getdate())
			IF @@ERROR <> 0 GOTO on_error
			SELECT @applicationInstanceID = SCOPE_IDENTITY()

		-- add content resource to zone
		EXEC @rc = dbo.cms_createPageZoneResource @pageID=@pageID, @zoneID=@zoneID, @siteResourceID=@siteResourceID, @pzrID=@pzrID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	IF @@TRANCOUNT > 0 COMMIT TRAN
END
ELSE begin
	print 'failed max instance check: ' + @applicationInstanceName
	RETURN -1
end
print 'create Instance success: ' + @applicationInstanceName
-- normal exit
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

USE [memberCentral]
GO
ALTER PROCEDURE [dbo].[cms_getPageStructureByID] 
@siteID int,
@pageID int,
@languageID int,
@ovrMode varchar(30),
@processMobileOverrides bit,
@templateTypeName varchar(100)

AS

DECLARE @templateID int, @sectionID int, @pgResourceTypeID int, @modeID int, @ovrModeID int, @defaultTemplateID int, @defaultModeID int, @rootSectionID int
DECLARE @viewFunctionID int, @editFunctionID int
DECLARE @layoutInfo TABLE (templateID int, templateTypeName varchar(100), modeID int, modeName varchar(100), templateFilename varchar(100), sitecode varchar(10), orgcode varchar(10))
DECLARE @pageSectionPath TABLE (templateID int, modeID int, sectionID int, parentSectionID int, dataLevel int PRIMARY KEY,includePlacements bit)
DECLARE @zoneAssignments TABLE (autoid int IDENTITY(1,1), zoneID int, assignmentLevel varchar(10), sectionID int, siteResourceID int, sortOrder int, dataLevel int, isSSL bit, appInstanceID int, appInstanceName varchar(100), appTypeName varchar(100), appWidgetInstanceID int, appInstanceResourceID int, appWidgetTypeName varchar(100), appWidgetInstanceName varchar(100),contentID int,enableSocialMediaSharing bit, resourceTypeID int, resourceType varchar(100), resourceTypeClassName varchar(100), objectType char(1))

set @defaultTemplateID = 1
set @defaultModeID = 1

select @ovrModeID = modeID from cms_pageModes where modeName = @ovrMode

select @pgResourceTypeID = sr.resourceTypeID, @sectionID = p.sectionID, @modeID = pm.modeID, @templateID = isnull(mobilept.templateID,pt.templateid)
from cms_pages p (nolock)
inner join sites s
	on s.siteID = p.siteID
inner join cms_siteResources sr (nolock)
	on p.siteResourceID = sr.siteResourceID
	and pageID = @pageID
inner join dbo.cache_cms_derivedPageSectionSettings dpss (nolock)
	on dpss.sectionID = p.sectionID
inner join dbo.cms_pageTemplates pt (nolock)
	on pt.templateid = coalesce(p.ovTemplateID,dpss.ovTemplateID,@defaultTemplateID)
inner join dbo.cms_pageModes pm (nolock)
	on pm.modeID = coalesce(@ovrModeID,p.ovModeID,dpss.ovModeID,@defaultModeID)
left outer join dbo.cms_pageTemplates mobilept (nolock)
	on mobilept.templateid = coalesce(p.ovTemplateIDMobile,dpss.ovTemplateIDMobile)
	--and s.enableMobile = 1
	and @processMobileOverrides = 1


select @rootSectionID = sectionID from cms_pageSections where siteID = @siteID and parentSectionID is null

if (nullif(@ovrMode,'') is not null)
	select @ovrModeID = modeID from cms_pageModes where modeName = @ovrMode

-- find all zone assignments for page, not considering pageMode
insert into @zoneAssignments (zoneid, siteResourceID, assignmentLevel, sectionID, sortOrder, dataLevel,resourceTypeID,	resourceType, resourceTypeClassName,isSSL,contentID,enableSocialMediaSharing,objecttype)
select pzr.zoneid, pzr.siteResourceID, 'page' as assignmentLevel, 0 as sectionID, pzr.sortOrder, 0 as dataLevel, srt.resourceTypeID,srt.resourceType,srtc.resourceTypeClassName, isnull(c.isSSL,1),c.contentID, c.enableSocialMediaSharing,
objecttype = case
	when c.siteresourceID is not null then 'C'
	when ai.siteresourceID is not null then 'A'
	when awi.siteresourceID is not null then 'W'
end

from dbo.cms_pageZonesResources as pzr
inner join dbo.cms_siteResources sr
	on pzr.siteResourceID = sr.siteResourceID
	and pzr.pageID = @pageID
inner join dbo.cms_siteResourceStatuses srs
	ON sr.siteResourceStatusID = srs.siteResourceStatusID
	AND srs.siteResourceStatusDesc = 'Active'
inner join dbo.cms_siteResourceTypes srt ON sr.resourceTypeID = srt.resourceTypeID
inner join dbo.cms_siteResourceTypeClasses srtc ON srt.resourceTypeClassID = srtc.resourceTypeClassID
left outer join dbo.cms_content c on sr.siteResourceID = c.siteResourceID 
left outer join dbo.cms_applicationInstances ai on sr.siteResourceID = ai.siteResourceID
left outer join dbo.cms_applicationWidgetInstances awi on sr.siteResourceID = awi.siteResourceID

union all

select pszr.zoneid, pszr.siteResourceID, 
	assignmentLevel = case when rps.sectionID = @rootSectionID then 'site' else 'section' end,
	rps.sectionID, pszr.sortOrder, (rps.depth * -1) as dataLevel, srt.resourceTypeID,	srt.resourceType, srtc.resourceTypeClassName, isnull(c.isSSL,1),c.contentID, c.enableSocialMediaSharing,
	objecttype = case
		when c.siteresourceID is not null then 'C'
		when ai.siteresourceID is not null then 'A'
		when awi.siteresourceID is not null then 'W'
	end
		
from dbo.cms_pageSectionsZonesResources pszr
inner join dbo.cache_cms_recursivePageSections rps
	on rps.startSectionID = @sectionID
	and rps.sectionid = pszr.sectionid
	and rps.includePlacements = 1
inner join dbo.cms_siteResources sr
	on pszr.siteResourceID = sr.siteResourceID
inner join dbo.cms_siteResourceStatuses srs
	ON sr.siteResourceStatusID = srs.siteResourceStatusID
	AND srs.siteResourceStatusDesc = 'Active'
inner join dbo.cms_siteResourceTypes srt ON sr.resourceTypeID = srt.resourceTypeID
inner join dbo.cms_siteResourceTypeClasses srtc ON srt.resourceTypeClassID = srtc.resourceTypeClassID
left outer join dbo.cms_content c on sr.siteResourceID = c.siteResourceID 
left outer join dbo.cms_applicationInstances ai on sr.siteResourceID = ai.siteResourceID
left outer join dbo.cms_applicationWidgetInstances awi on sr.siteResourceID = awi.siteResourceID


-- update Application related columns
if exists (select top 1 siteresourceID from @zoneAssignments where objecttype = 'A')
	update za
	set
		appInstanceID = ai.applicationInstanceID,
		appInstanceName = ai.applicationInstanceName, 
		appTypeName = at.applicationTypeName, 
		appInstanceResourceID = ai.siteResourceID,
		isSSL = 1
	from @zoneAssignments za
	inner join dbo.cms_applicationInstances ai
		on za.siteResourceID = ai.siteResourceID
		and za.objecttype = 'A'
	inner join dbo.cms_applicationTypes at ON ai.applicationTypeID = at.applicationTypeID

--update Application Widget related columns
if exists (select top 1 siteresourceID from @zoneAssignments where objecttype = 'W')
	update za
	set
		appInstanceID = ai.applicationInstanceID,
		appInstanceName = ai.applicationInstanceName, 
		appTypeName = at.applicationTypeName, 
		appInstanceResourceID = ai.siteResourceID, 
		appWidgetInstanceID = awi.applicationWidgetInstanceID , 
		appWidgetTypeName = awt.applicationWidgetTypeName, 
		appWidgetInstanceName = awi.applicationWidgetInstanceName,
		isSSL = 1
	from @zoneAssignments za
	inner join dbo.cms_applicationWidgetInstances awi
		on za.siteResourceID = awi.siteResourceID
		and za.objecttype = 'W'
	inner join dbo.cms_applicationInstances ai ON awi.applicationInstanceID = ai.applicationInstanceID
	inner join dbo.cms_applicationWidgetTypes awt ON awi.applicationWidgetTypeID = awt.applicationWidgetTypeID
	inner join dbo.cms_applicationTypes at ON ai.applicationTypeID = at.applicationTypeID



-- is page offered in requested page language? if not, use site default language
IF NOT EXISTS (select pageLanguageID from dbo.cms_pageLanguages where pageID = @pageID and languageID = @languageID)
	SELECT @languageID = defaultLanguageID from sites where siteID = @siteID
select cast(isNull((
select page.pageID, page.pageName, page.allowReturnAfterLogin, page.siteResourceID as pageSiteResourceID,
	isnull(sites.siteID,0) as siteID,
	isnull(sites.siteCode,0) as siteCode,
	isnull(organizations.orgID,0) as orgID,
	isnull(organizations.orgCode,0) as orgCode,
	ISNULL(ps.sectionID,'') as sectionID, 
	ISNULL(ps.sectionName,'') as sectionName, 
	ISNULL(ps.sectionCode,'') as sectionCode,
	@languageID as pageLanguageID,
	ISNULL(pl.pageTitle,'') as pageTitle, 
	ISNULL(pl.pageDesc,'') as pageDesc, 
	ISNULL(pl.keywords,'') as keywords, 
	ISNULL(pt.templateFileName,'DefaultTemplate') as layoutFileName,
	ISNULL(ptt.templateTypeName,'') as templateTypeName,
	ISNULL(pm.modeName,'Normal') as layoutMode, 
	ISNULL(templateSite.sitecode,'') as layoutSiteCode,
	ISNULL(templateOrg.orgcode,'') as layoutOrgCode, 
	pageZone.zoneName, 
	siteResource.siteResourceID,
	ISNULL(sites.siteID,0) as siteID,
	ISNULL(sites.siteCode,'') as siteCode,
	ISNULL(organizations.orgID,0) as orgID,
	ISNULL(organizations.orgCode,'') as orgCode,
	ISNULL(siteResource.resourceType,'') as resourceType,
	ISNULL(siteResource.resourceTypeClassName,'') as resourceClass,
	ISNULL(siteResource.assignmentLevel,'') as assignmentLevel,
	ISNULL(siteResource.sectionID,'') as assignmentSectionID,
	ISNULL(siteResource.isSSL,'') as isSSL, 
	ISNULL(siteResource.appInstanceID,'') as appInstanceID, 
	ISNULL(siteResource.appInstanceName,'') as appInstanceName, 
	ISNULL(siteResource.appTypeName,'') as appTypeName, 
	ISNULL(siteResource.appWidgetInstanceID,'') as appWidgetInstanceID, 
	ISNULL(siteResource.appInstanceResourceID,'') as appInstanceResourceID, 
	ISNULL(siteResource.appWidgetTypeName,'') as appWidgetTypeName, 
	ISNULL(siteResource.appWidgetInstanceName,'') as appWidgetInstanceName,
	ISNULL(siteResource.contentID,'') as contentID,
	ISNULL(siteResource.enableSocialMediaSharing,'') as enableSocialMediaSharing
from dbo.cms_pages as page (nolock)
inner join dbo.sites (nolock)
	on sites.siteID = page.siteID
	and page.pageID = @pageID
inner join dbo.organizations (nolock)
	on sites.orgID = organizations.orgID
inner join dbo.cms_pageSections (nolock) as ps
	on ps.sectionID = page.sectionID
inner join dbo.cms_pageTemplates pt (nolock)
	on pt.templateid = @templateID
inner join dbo.cms_pageTemplateTypes ptt (nolock)
	on pt.templateTypeID = ptt.templateTypeID
	and ptt.templateTypeName = @templateTypeName
inner join dbo.cms_pageModes pm (nolock)
	on pm.modeID = @modeID
left outer join dbo.sites templateSite (nolock)
	inner join dbo.organizations templateOrg (nolock)
		on templateSite.orgid = templateOrg.orgid
on templateSite.siteid = pt.siteid
inner join dbo.cms_pageLanguages as pl (nolock) 
	on pl.pageID = page.pageid and pl.languageid = @languageID
inner join dbo.cms_pageTemplatesModesZones as ptmz (nolock)
	on pm.modeid = ptmz.modeid and ptmz.templateID = pt.templateID
inner join dbo.cms_pageZones as pageZone (nolock)
	on pageZone.zoneid = ptmz.zoneid
left outer join @zoneAssignments as siteResource
	on ptmz.zoneID = siteResource.zoneid
order by siteResource.zoneid, siteResource.dataLevel desc, siteResource.sortOrder
for xml auto, root('pageStructure')
),'<pageStructure />') as xml) as pageStructureXML
OPTION(RECOMPILE)


RETURN 0
GO

USE [memberCentral]
GO

ALTER PROCEDURE [dbo].[cms_getPageStructureByName] 
@siteID int,
@pageName varchar(50),
@languageID int,
@ovrMode varchar(30)=null,
@processMobileOverrides bit,
@templateTypeName varchar(100)

AS


declare @pageid int, @rc int

select top 1 @pageid = pageid
from dbo.cms_pages p
inner join cms_siteResources sr
	on p.siteResourceID = sr.siteResourceID
	and p.siteID = @siteID
	and p.pageName = @pagename
inner join cms_siteResourceStatuses srs
	on srs.siteResourceStatusID = sr.siteResourceStatusID
	and srs.siteResourceStatusDesc in ('Active','Inactive')

exec @rc = dbo.cms_getPageStructureByID 
	@siteID=@siteID, 
	@pageid=@pageid, 
	@languageID=@languageID, 
	@ovrMode=@ovrMode, 
	@processMobileOverrides=@processMobileOverrides,
	@templateTypeName=@templateTypeName
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

RETURN 0

on_error:
	RETURN -1
GO




create PROC [dbo].[cms_updateMenuUsageAutoCorrect]
	@restrictToSiteID int

as

-- page section normal templates
update mu set
	templateUsageTypeID = correct_ptmut.templateUsageTypeID
from cms_menuUsages mu 
inner join cms_pageTemplateMenuUsageTypes ptmut
	on ptmut.templateUsageTypeID = mu.templateUsageTypeID
	and mu.isMobileUsage = 0
inner join cms_menuUsageTypes mut
	on ptmut.usageTypeID = mut.usageTypeID
inner join cms_pageSections ps
	on ps.siteResourceID = mu.usedBySiteResourceID
	and ps.siteID = isnull(@restrictToSiteID,ps.siteID)
inner join dbo.cache_cms_derivedPageSectionSettings dpss
	on dpss.sectionID = ps.sectionID
	and ptmut.templateID <> dpss.ovTemplateID
inner join cms_pageTemplateMenuUsageTypes correct_ptmut
	on correct_ptmut.templateID = dpss.ovTemplateID
	and correct_ptmut.usageTypeID = ptmut.usageTypeID


-- page section mobile templates
update mu set
	templateUsageTypeID = correct_ptmut.templateUsageTypeID
from cms_menuUsages mu 
inner join cms_pageTemplateMenuUsageTypes ptmut
	on ptmut.templateUsageTypeID = mu.templateUsageTypeID
	and mu.isMobileUsage = 1
inner join cms_menuUsageTypes mut
	on ptmut.usageTypeID = mut.usageTypeID
inner join cms_pageSections ps
	on ps.siteResourceID = mu.usedBySiteResourceID
	and ps.siteID = isnull(@restrictToSiteID,ps.siteID)
inner join dbo.cache_cms_derivedPageSectionSettings dpss
	on dpss.sectionID = ps.sectionID
	and ptmut.templateID <> dpss.ovTemplateIDMobile
inner join cms_pageTemplateMenuUsageTypes correct_ptmut
	on correct_ptmut.templateID = dpss.ovTemplateIDMobile
	and correct_ptmut.usageTypeID = ptmut.usageTypeID


-- page normal templates
update mu set
	templateUsageTypeID = correct_ptmut.templateUsageTypeID
from cms_menuUsages mu 
inner join cms_pageTemplateMenuUsageTypes ptmut
	on ptmut.templateUsageTypeID = mu.templateUsageTypeID
	and mu.isMobileUsage = 0
inner join cms_menuUsageTypes mut
	on ptmut.usageTypeID = mut.usageTypeID
inner join cms_pages p
	on p.siteResourceID = mu.usedBySiteResourceID
	and p.siteID = isnull(@restrictToSiteID,p.siteID)	
inner join dbo.cache_cms_derivedPageSectionSettings dpss
	on dpss.sectionID = p.sectionID
	and ptmut.templateID <> dpss.ovTemplateID
inner join cms_pageTemplateMenuUsageTypes correct_ptmut
	on correct_ptmut.templateID = dpss.ovTemplateID
	and correct_ptmut.usageTypeID = ptmut.usageTypeID


-- page mobile templates
update mu set
	templateUsageTypeID = correct_ptmut.templateUsageTypeID
from cms_menuUsages mu 
inner join cms_pageTemplateMenuUsageTypes ptmut
	on ptmut.templateUsageTypeID = mu.templateUsageTypeID
	and mu.isMobileUsage = 1
inner join cms_menuUsageTypes mut
	on ptmut.usageTypeID = mut.usageTypeID
inner join cms_pages p
	on p.siteResourceID = mu.usedBySiteResourceID
	and p.siteID = isnull(@restrictToSiteID,p.siteID)
inner join dbo.cache_cms_derivedPageSectionSettings dpss
	on dpss.sectionID = p.sectionID	
	and ptmut.templateID <> dpss.ovTemplateIDMobile
inner join cms_pageTemplateMenuUsageTypes correct_ptmut
	on correct_ptmut.templateID = dpss.ovTemplateIDMobile
	and correct_ptmut.usageTypeID = ptmut.usageTypeID

return 0
GO

USE [memberCentral]
GO
ALTER PROC [dbo].[cms_updatePageSection]
@sectionID int,
@siteid int,
@siteResourceStatusID int,
@parentSectionID int,
@sectionName varchar(50),
@sectionCode varchar(30),
@ovTemplateID int,
@ovTemplateIDMobile int,
@ovModeID int,
@inheritPlacements bit

AS

DECLARE @currentParentSection int, @currentInheritPlacements bit, @rc int
select @currentParentSection=parentSectionID, @currentInheritPlacements=inheritPlacements
from cms_pageSections


BEGIN TRAN

UPDATE cms_pageSections
SET 
	parentSectionID = @parentSectionID,
	sectionName = @sectionName,
	sectionCode = @sectionCode,
	ovModeID = @ovModeID,
	ovTemplateID = @ovTemplateID,
	ovTemplateIDMobile = @ovTemplateIDMobile,
	inheritPlacements = @inheritPlacements
WHERE 
	sectionID = @sectionID
	AND siteID = @siteID
IF @@ERROR <> 0 GOTO on_error

update sr
set siteResourceStatusID = @siteResourceStatusID
from cms_pageSections ps
	inner join cms_siteResources sr
		on ps.siteresourceID = sr.siteResourceID
		and ps.sectionID = @sectionID
		and ps.siteID = @siteID
IF @@ERROR <> 0 GOTO on_error


if (@currentParentSection <> @parentSectionID or @currentInheritPlacements <> @inheritPlacements)
BEGIN
	exec @rc = [dbo].[cache_cms_updateRecursivePageSections] 
		@restrictToSiteID = @siteID,
		@restrictToSectionID = NULL
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
END

exec @rc = [dbo].cache_cms_updateDerivedPageSectionSettings 
	@restrictToSiteID = @siteID,
	@restrictToSectionID = NULL
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

exec @rc = [dbo].cms_updateMenuUsageAutoCorrect
	@restrictToSiteID = @siteID
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error



IF @@TRANCOUNT > 0 COMMIT TRAN

-- normal exit
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1

GO

USE [memberCentral]
GO
ALTER PROC [dbo].[cms_updatePage]
@pageID int,
@siteid int,
@siteResourceStatusID int,
@sectionID int,
@ovTemplateID int,
@ovTemplateIDMobile int,
@ovModeID int,
@inheritPlacements bit

AS

DECLARE @siteResourceID int, @rc int;

BEGIN TRAN

UPDATE cms_pages
SET 
	sectionID = @sectionID,
	ovModeID = @ovModeID,
	ovTemplateID = @ovTemplateID,
	ovTemplateIDMobile = @ovTemplateIDMobile,
	inheritPlacements = @inheritPlacements
WHERE 
	pageID = @pageID
	AND siteID = @siteID
IF @@ERROR <> 0 GOTO on_error

update sr
set siteResourceStatusID = @siteResourceStatusID
from cms_pages p
	inner join cms_siteResources sr
		on p.siteresourceID = sr.siteResourceID
		and p.pageID = @pageID
		and p.siteID = @siteID
IF @@ERROR <> 0 GOTO on_error


exec @rc = [dbo].cms_updateMenuUsageAutoCorrect
	@restrictToSiteID = @siteID
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


IF @@TRANCOUNT > 0 COMMIT TRAN

-- normal exit
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1



GO


declare @toolTypeID int
declare @resourceTypeID int

exec createAdminToolType @toolType='MenuAdmin', @toolCFC='menu.MenuAdmin', @toolDesc='Menu Administration', @toolTypeID=@toolTypeID OUTPUT, @resourceTypeID=@resourceTypeID OUTPUT

declare @siteAdminRoleID int, @superAdminRoleID int, @functionID int
declare @ResourceTypeAdminFunctionID int, @ResourceTypeTypesFunctionID int, @ResourceEditTypesFunctionID int, @ResourceDeleteTypesFunctionID int

select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')
select @siteAdminRoleID = dbo.fn_getResourceRoleID('Site Administrator')

exec dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='View', @displayName='View the Menu Administration Tool', @functionID=@functionID OUTPUT

select @ResourceTypeAdminFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID)

--exec dbo.cms_createSiteResourceRoleFunction @roleID=@siteAdminRoleID, @resourceTypeFunctionID=@ResourceTypeAdminFunctionID
exec dbo.cms_createSiteResourceRoleFunction @roleID=@superAdminRoleID, @resourceTypeFunctionID=@ResourceTypeAdminFunctionID


declare @rMgmtNavigationID int, @adminNavigationID int

select @rMgmtNavigationID = n.navigationID 
from dbo.admin_navigation n
inner join dbo.admin_navigation pn
	on pn.navigationID = n.parentNavigationID
	and pn.navName = 'Website'
where n.navName = 'Settings'

exec createAdminNavigation @navName='Menus',@navDesc=NULL,@parentNavigationID=@rMgmtNavigationID,@navAreaID=3,@cfcMethod='list',@isHeader=0,@showInNav=1,@navigationID=@adminNavigationID OUTPUT

exec createAdminFunctionsDeterminingNav @resourceTypeFunctionID=@ResourceTypeAdminFunctionID,@toolTypeID=@toolTypeID,@navigationID=@adminNavigationID
GO

declare @minSiteID int

select @minSiteID = min(siteID) from sites

while @minSiteID is not null 
BEGIN

      EXEC createAdminSuite @minSiteID

      select @minSiteID = min(siteID) from sites where siteID > @minSiteID

END
GO



CREATE PROCEDURE [dbo].[menu_createMenu]
 @orgID int,
 @siteid INT,
 @menuName varchar(200),
 @menuCode varchar(50),
 @menuRawContent varchar(max),
 @menuID INT OUTPUT
AS
BEGIN
 -- SET NOCOUNT ON added to prevent extra result sets from
 -- interfering with SELECT statements.
 SET NOCOUNT ON;
 DECLARE @rc INT, @trashID INT
 DECLARE @menuAdminResourceID INT, @siteResourceStatusID INT, @appCreatedContentResourceTypeID INT, @languageID INT, 
			@contentID INT, @contentSRID INT


 select @languageID = defaultLanguageID
 from dbo.sites
 where siteID = @siteID

 select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')
 select @siteResourceStatusID = dbo.fn_getResourceStatusId('Active')
 select @menuAdminResourceID = dbo.fn_getSiteResourceIDForResourceType('MenuAdmin',@siteid)

 BEGIN TRAN

 -- Add content object
 exec dbo.cms_createContentObject @siteid, @appCreatedContentResourceTypeID, @menuAdminResourceID, @siteResourceStatusID, 
									 0, 1, @languageID, 1, '', '', @menuRawContent, 
									 @contentID OUTPUT, @contentSRID OUTPUT
 IF @@ERROR <> 0 GOTO on_error

 insert into dbo.cms_menus(menuName, menuCode, contentID, siteID)
 values(@menuName, @menuCode, @contentID, @siteID)   
  IF @@ERROR <> 0 GOTO on_error
 
 SELECT @menuID = SCOPE_IDENTITY()


 IF @@TRANCOUNT > 0 COMMIT TRAN
 RETURN 0
 on_error:
  select @menuID = 0
  RETURN -1
END
GO
set ANSI_NULLS ON
set QUOTED_IDENTIFIER ON
go

--  
-- Script to Update dbo.cms_pageTemplates in DEV02\PLATFORM2008.membercentral 
-- Generated Tuesday, May 21, 2013, at 05:31 PM 
--  
-- Please backup DEV02\PLATFORM2008.membercentral before executing this script
--  
-- ** Script Begin **
BEGIN TRANSACTION
GO
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE
GO

PRINT 'Updating dbo.cms_pageTemplates Table'
GO

SET ANSI_NULLS, ANSI_PADDING, ANSI_WARNINGS, ARITHABORT, QUOTED_IDENTIFIER, CONCAT_NULL_YIELDS_NULL ON
GO

SET NUMERIC_ROUNDABORT OFF
GO


IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_cache_cms_derivedMenuUsages_cms_pageTemplates')
      ALTER TABLE [dbo].[cache_cms_derivedPageSectionSettings] DROP CONSTRAINT [FK_cache_cms_derivedMenuUsages_cms_pageTemplates]
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_cache_cms_derivedMenuUsages_cms_pageTemplates1')
      ALTER TABLE [dbo].[cache_cms_derivedPageSectionSettings] DROP CONSTRAINT [FK_cache_cms_derivedMenuUsages_cms_pageTemplates1]
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_cms_pages_cms_pageTemplates')
      ALTER TABLE [dbo].[cms_pages] DROP CONSTRAINT [FK_cms_pages_cms_pageTemplates]
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_pages_pageTemplates')
      ALTER TABLE [dbo].[cms_pages] DROP CONSTRAINT [FK_pages_pageTemplates]
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_cms_pageSections_cms_pageTemplates')
      ALTER TABLE [dbo].[cms_pageSections] DROP CONSTRAINT [FK_cms_pageSections_cms_pageTemplates]
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_pageSections_pageTemplates')
      ALTER TABLE [dbo].[cms_pageSections] DROP CONSTRAINT [FK_pageSections_pageTemplates]
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_cms_pageTemplateMenuUsageTypes_cms_pageTemplates')
      ALTER TABLE [dbo].[cms_pageTemplateMenuUsageTypes] DROP CONSTRAINT [FK_cms_pageTemplateMenuUsageTypes_cms_pageTemplates]
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_pageTemplatesModesZones_pageTemplates')
      ALTER TABLE [dbo].[cms_pageTemplatesModesZones] DROP CONSTRAINT [FK_pageTemplatesModesZones_pageTemplates]
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_cms_pageTemplates_cms_pageTemplateTypes')
      ALTER TABLE [dbo].[cms_pageTemplates] DROP CONSTRAINT [FK_cms_pageTemplates_cms_pageTemplateTypes]
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_pageTemplates_sites')
      ALTER TABLE [dbo].[cms_pageTemplates] DROP CONSTRAINT [FK_pageTemplates_sites]
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   CREATE TABLE [dbo].[tmp_cms_pageTemplates] (
   [templateID] [int] IDENTITY (1, 1) NOT NULL,
   [templateTypeID] [int] NOT NULL,
   [siteID] [int] NULL,
   [templateName] [varchar] (100) COLLATE Latin1_General_CI_AI NOT NULL,
   [templateDesc] [varchar] (300) COLLATE Latin1_General_CI_AI NULL,
   [templateFilename] [varchar] (50) COLLATE Latin1_General_CI_AI NULL,
   [status] [char] (1) COLLATE Latin1_General_CI_AI NULL,
   [isMobile] [bit] NOT NULL CONSTRAINT [DF_cms_pageTemplates_isMobile] DEFAULT ((0))
)
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   SET IDENTITY_INSERT [dbo].[tmp_cms_pageTemplates] ON
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   INSERT INTO [dbo].[tmp_cms_pageTemplates] ([templateID], [templateTypeID], [siteID], [templateName], [templateDesc], [templateFilename], [status])
   SELECT [templateID], [templateTypeID], [siteID], [templateName], [templateDesc], [templateFilename], [status]
   FROM [dbo].[cms_pageTemplates]
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   SET IDENTITY_INSERT [dbo].[tmp_cms_pageTemplates] OFF
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   DROP TABLE [dbo].[cms_pageTemplates]
GO

sp_rename N'[dbo].[tmp_cms_pageTemplates]', N'cms_pageTemplates'

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   ALTER TABLE [dbo].[cms_pageTemplates] ADD CONSTRAINT [PK_pageTemplates] PRIMARY KEY CLUSTERED ([templateID]) WITH (FILLFACTOR = 90)
GO

-- Adding Foreign Keys

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_cms_pageTemplates_cms_pageTemplateTypes')
      ALTER TABLE [dbo].[cms_pageTemplates] DROP CONSTRAINT [FK_cms_pageTemplates_cms_pageTemplateTypes]
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF NOT EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_cms_pageTemplates_cms_pageTemplateTypes')
      ALTER TABLE [dbo].[cms_pageTemplates] ADD CONSTRAINT [FK_cms_pageTemplates_cms_pageTemplateTypes] FOREIGN KEY ([templateTypeID]) REFERENCES [dbo].[cms_pageTemplateTypes] ([templateTypeID])
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_pageTemplates_sites')
      ALTER TABLE [dbo].[cms_pageTemplates] DROP CONSTRAINT [FK_pageTemplates_sites]
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF NOT EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_pageTemplates_sites')
      ALTER TABLE [dbo].[cms_pageTemplates] ADD CONSTRAINT [FK_pageTemplates_sites] FOREIGN KEY ([siteID]) REFERENCES [dbo].[sites] ([siteID])
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF NOT EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_cache_cms_derivedMenuUsages_cms_pageTemplates')
      ALTER TABLE [dbo].[cache_cms_derivedPageSectionSettings] ADD CONSTRAINT [FK_cache_cms_derivedMenuUsages_cms_pageTemplates] FOREIGN KEY ([ovTemplateID]) REFERENCES [dbo].[cms_pageTemplates] ([templateID])
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF NOT EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_cache_cms_derivedMenuUsages_cms_pageTemplates1')
      ALTER TABLE [dbo].[cache_cms_derivedPageSectionSettings] ADD CONSTRAINT [FK_cache_cms_derivedMenuUsages_cms_pageTemplates1] FOREIGN KEY ([ovTemplateIDMobile]) REFERENCES [dbo].[cms_pageTemplates] ([templateID])
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF NOT EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_cms_pages_cms_pageTemplates')
      ALTER TABLE [dbo].[cms_pages] ADD CONSTRAINT [FK_cms_pages_cms_pageTemplates] FOREIGN KEY ([ovTemplateIDMobile]) REFERENCES [dbo].[cms_pageTemplates] ([templateID])
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF NOT EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_pages_pageTemplates')
      ALTER TABLE [dbo].[cms_pages] ADD CONSTRAINT [FK_pages_pageTemplates] FOREIGN KEY ([ovTemplateID]) REFERENCES [dbo].[cms_pageTemplates] ([templateID])
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF NOT EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_cms_pageSections_cms_pageTemplates')
      ALTER TABLE [dbo].[cms_pageSections] ADD CONSTRAINT [FK_cms_pageSections_cms_pageTemplates] FOREIGN KEY ([ovTemplateIDMobile]) REFERENCES [dbo].[cms_pageTemplates] ([templateID])
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF NOT EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_pageSections_pageTemplates')
      ALTER TABLE [dbo].[cms_pageSections] ADD CONSTRAINT [FK_pageSections_pageTemplates] FOREIGN KEY ([ovTemplateID]) REFERENCES [dbo].[cms_pageTemplates] ([templateID])
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF NOT EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_cms_pageTemplateMenuUsageTypes_cms_pageTemplates')
      ALTER TABLE [dbo].[cms_pageTemplateMenuUsageTypes] ADD CONSTRAINT [FK_cms_pageTemplateMenuUsageTypes_cms_pageTemplates] FOREIGN KEY ([templateID]) REFERENCES [dbo].[cms_pageTemplates] ([templateID])
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF NOT EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_pageTemplatesModesZones_pageTemplates')
      ALTER TABLE [dbo].[cms_pageTemplatesModesZones] ADD CONSTRAINT [FK_pageTemplatesModesZones_pageTemplates] FOREIGN KEY ([templateID]) REFERENCES [dbo].[cms_pageTemplates] ([templateID])
GO
-- Finished Adding Foreign Keys

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
BEGIN
   PRINT 'dbo.cms_pageTemplates Table Updated Successfully'
   COMMIT TRANSACTION
END ELSE
BEGIN
   PRINT 'Failed To Update dbo.cms_pageTemplates Table'
END
GO



ALTER PROC [dbo].[cms_createPageTemplate]
@siteID int,
@templateTypeID int,
@templateName varchar(100),
@templateDesc varchar(300),
@templateFileName varchar(500),
@isMobile bit = 0,
@templateID int OUTPUT

AS

-- ensure @templateID is null (can be passed in)
SELECT @templateID = null

DECLARE @rc int

BEGIN TRAN

	-- check to see if template already exists
	select @templateID = templateID FROM dbo.cms_pageTemplates where (siteID = @siteID) and ((templateName = @templateName) or (templateFilename = @templateFilename)) and (status = 'A')

	-- if not, add template
	IF @templateID is null BEGIN
		INSERT INTO dbo.cms_pageTemplates (siteID, templateName, templateDesc, templateFileName, templateTypeID, status, isMobile)
		VALUES (@siteID, @templateName, @templateDesc, @templateFileName, @templateTypeID, 'A', @isMobile)
			IF @@ERROR <> 0 GOTO on_error
			select @templateID = SCOPE_IDENTITY()
	
	
		-- add main zone (A) to template in all modes
		declare @zoneID int, @modeID int, @placementDesc varchar(50)
		select @zoneID = zoneID from dbo.cms_pageZones where zoneName = 'Main'
		select @modeID = min(modeID) from dbo.cms_pageModes
		select @placementDesc = 'Main Zone'
		while @modeID is not null
		BEGIN
			EXEC @rc = dbo.cms_createPageTemplatesModesZones @templateID, @modeID, @zoneID, @placementDesc
				IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
				select @modeID = min(modeID) from dbo.cms_pageModes where modeID > @modeID
		END
	END
	ELSE 
		SELECT @templateID = NULL
-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @templateID = 0
	RETURN -1

on_error2:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1

GO

use [memberCentral]
go

CREATE NONCLUSTERED INDEX [_dta_index_cms_pageSections_7_1005597204__K5_K2_1] ON [dbo].[cms_pageSections] 
(
	[parentSectionID] ASC,
	[siteID] ASC
)
INCLUDE ( [sectionID]) WITH (SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF) ON [PRIMARY]
go

CREATE STATISTICS [_dta_stat_2097793114_1_7_3] ON [dbo].[cms_content]([contentID], [enableSocialMediaSharing], [siteResourceID])
go

CREATE STATISTICS [_dta_stat_270362661_3_5] ON [dbo].[cache_cms_recursivePageSections]([startSectionID], [includePlacements])
go

CREATE STATISTICS [_dta_stat_270362661_4_3_5] ON [dbo].[cache_cms_recursivePageSections]([sectionID], [startSectionID], [includePlacements])
go

CREATE STATISTICS [_dta_stat_595831718_2_4] ON [dbo].[cms_applicationWidgetInstances]([applicationWidgetTypeID], [siteResourceID])
go

CREATE STATISTICS [_dta_stat_595831718_2_3] ON [dbo].[cms_applicationWidgetInstances]([applicationWidgetTypeID], [applicationInstanceID])
go

CREATE STATISTICS [_dta_stat_595831718_3_4_2] ON [dbo].[cms_applicationWidgetInstances]([applicationInstanceID], [siteResourceID], [applicationWidgetTypeID])
go

CREATE STATISTICS [_dta_stat_1203833884_3_4] ON [dbo].[cms_pageTemplatesModesZones]([modeID], [zoneID])
go


use [memberCentral]
go

CREATE NONCLUSTERED INDEX [_dta_index_cache_cms_derivedPageSectionSett_7_558363687__K3_4_5] ON [dbo].[cache_cms_derivedPageSectionSettings] 
(
	[sectionID] ASC
)
INCLUDE ( [ovModeID],
[ovTemplateID]) WITH (SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF) ON [PRIMARY]
go



exec [dbo].cache_cms_updateDerivedPageSectionSettings
	@restrictToSiteID = null,
	@restrictToSectionID = NULL


exec dbo.cache_cms_updateRecursivePageSections
	@restrictToSiteID = null,
	@restrictToSectionID = NULL

GO


set ANSI_NULLS ON
set QUOTED_IDENTIFIER ON
go

ALTER PROC [dbo].[cms_createPage]
@siteid int,
@languageID int,
@resourceTypeID int,
@siteResourceStatusID int,
@pgParentResourceID int = NULL,
@isVisible bit,
@sectionID int,
@ovTemplateID int,
@ovTemplateIDMobile int,
@ovModeID int,
@pageName varchar(50),
@pageTitle varchar(200),
@pageDesc varchar(400),
@keywords varchar(400),
@inheritPlacements bit,
@allowReturnAfterLogin bit,
@checkReservedNames bit,
@pageID int OUTPUT

AS

DECLARE @siteResourceID int;

-- ensure @pageID is null (can be passed in)
SELECT @pageID = null

-- check if pagename is already used
if EXISTS (	SELECT p.pageID 
			FROM dbo.cms_pages p
			INNER JOIN dbo.cms_siteResources sr ON p.siteResourceID = sr.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc <> 'Deleted'
			WHERE p.siteID = @siteID AND p.pageName = @pageName
		   )
	BEGIN
		SELECT @pageID = 0
		RETURN 0
	END

-- check for reserved names if necessary
if @pageID is null 
	and @checkReservedNames = 1
	and EXISTS (select applicationTypeID from dbo.cms_applicationTypes where suggestedPageName = @pageName AND allowPageNameChange = 0)
	BEGIN
		SELECT @pageID = 0
		RETURN 0
	END

-- add page if not there
IF @pageID is null BEGIN

	BEGIN TRAN

	-- first create a resourceID for the page
	exec dbo.cms_createSiteResource
		@resourceTypeID = @ResourceTypeID,
		@siteResourceStatusID = @siteResourceStatusID,
		@siteID = @siteid,
		@isVisible = @isVisible,
		@parentSiteResourceID = @pgParentResourceID,
		@siteResourceID = @siteResourceID OUTPUT

	-- add the page
	INSERT INTO dbo.cms_pages (siteID, siteResourceID, sectionID, ovTemplateID, ovTemplateIDMobile, ovModeID, pageName, inheritPlacements,allowReturnAfterLogin, dateCreated)
	VALUES (@siteid, @siteResourceID, @sectionID, @ovTemplateID, @ovTemplateIDMobile, @ovModeID, @pageName, @inheritPlacements,@allowReturnAfterLogin, getdate())
		IF @@ERROR <> 0 GOTO on_error
		SELECT @pageID = SCOPE_IDENTITY()

	-- add page language

	declare @pageLanguageID int
	exec cms_createPageLanguage @siteid,@languageID,@pageID,@pageTitle,@pageDesc,@keywords,@pageLanguageID out
		IF @@ERROR <> 0 GOTO on_error

	IF @@TRANCOUNT > 0 COMMIT TRAN

END
ELSE
	SELECT @pageID = 0

-- normal exit
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @pageID = 0
	RETURN -1
GO
set ANSI_NULLS ON
set QUOTED_IDENTIFIER ON
go

ALTER PROC [dbo].[createSite]
	@orgID int,
	@sitecode varchar(10),
	@siteName varchar(60),
	@mainNetworkID int,
	@isLoginNetwork bit,
	@isMasterSite bit,
	@defaultLanguageID int,
	@defaultTimeZoneID int,
	@defaultCurrencyTypeID int,
	@showCurrencyType bit,
	@hasSSL bit,
	@isSSL bit,
	@enableMobile bit,
	@allowGuestAccounts bit,
	@forceLoginPage bit,
	@useRemoteLogin bit,
	@affiliationRequired bit,
	@providesFreeFastCase bit,
	@enforceSiteAgreement bit,
	@allowMemberUpdates bit,
	@immediateMemberUpdates bit,
	@emailMemberUpdates varchar(200),
	@defaultPostalState varchar(10),
	@joinURL varchar(100),
	@pdfPassword varchar(30),
	@alternateGuestAccountCreationLink varchar(400),
	@alternateGuestAccountPopup bit,
	@alternateForgotPasswordLink varchar(400),
	@mainhostname varchar(80),
	@norightsContent varchar(max),
	@norightsNotLoggedInContent varchar(max),
	@inactiveUserContent varchar(max),
	@siteagreementContent varchar(max),
	@welcomeMessageContent varchar(max),
	@firstTimeLoginContent varchar(max),
	@siteID int OUTPUT

AS

DECLARE @rc int, @templateID int, @modeID int, @sectionID int, 
	@sectionResourceTypeID int, @siteAdminRoleID int, @superAdminRoleID int, 
	@siteAdminGroupID int, @superAdminGroupID int, @trashID int

BEGIN TRAN
	-- check for existing sitecode
	SELECT @siteID = null
	SELECT @siteID = siteID FROM dbo.sites where sitecode = @sitecode

	-- if not there, add it
	IF @siteID is not null
		GOTO on_error

	-- insert sites
	INSERT INTO dbo.sites (orgID, sitecode, siteName, defaultLanguageID, defaultTimeZoneId, defaultCurrencyTypeID,
		hasSSL, allowGuestAccounts, forceLoginPage, useRemoteLogin, affiliationRequired, 
		providesFreeFastCase, allowMemberUpdates, enforceSiteAgreement, immediateMemberUpdates, emailMemberUpdates, 
		defaultPostalState, joinURL, pdfPassword, alternateGuestAccountCreationLink, alternateGuestAccountPopup, 
		alternateForgotPasswordLink, showCurrencyType,isSSL, enableMobile)
	VALUES (@orgID, @sitecode, @siteName, @defaultLanguageID, @defaultTimeZoneId, @defaultCurrencyTypeID, 
		@hasSSL, @allowGuestAccounts, @forceLoginPage, @useRemoteLogin, @affiliationRequired, 
		@providesFreeFastCase, @allowMemberUpdates, @enforceSiteAgreement, @immediateMemberUpdates, @emailMemberUpdates, 
		@defaultPostalState, @joinURL, @pdfPassword, @alternateGuestAccountCreationLink, @alternateGuestAccountPopup, 
		@alternateForgotPasswordLink, @showCurrencyType,@isSSL, @enableMobile)
		IF @@ERROR <> 0 GOTO on_error
		SELECT @siteID = SCOPE_IDENTITY()

	-- createSiteLanguage		
	EXEC @rc = dbo.createSiteLanguage @siteID=@siteID, @languageID=@defaultLanguageID		
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- get resourceType for site
	declare @siteResourceTypeID int
	select @siteResourceTypeID = dbo.fn_getResourceTypeID('Site')

	-- get active resource status
	declare @siteResourceStatusID int
	select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

	-- create a resourceID for the site
	DECLARE @siteResourceID int	
	exec dbo.cms_createSiteResource
		@resourceTypeID = @siteResourceTypeID,
		@siteResourceStatusID = @siteResourceStatusID,
		@siteID = @siteid,
		@isVisible = 1,
		@parentSiteResourceID = null,
		@siteResourceID = @siteResourceID OUTPUT
		IF @@ERROR <> 0 OR @siteResourceID = 0 GOTO on_error
	
	-- update site with new resource
	UPDATE dbo.sites
	SET siteResourceID = @siteResourceID
	WHERE siteID = @siteID
		IF @@ERROR <> 0 GOTO on_error
		
	-- roles
	select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')
	select @siteAdminRoleID = dbo.fn_getResourceRoleID('Site Administrator')
	select @siteAdminGroupID = groupID 
		from dbo.ams_groups 
		where groupCode = 'SiteAdmins' 
		and orgID = @orgID
	select @superAdminGroupID = groupID 
		from dbo.ams_groups 
		where groupCode = 'SuperAdmins' 
		and orgID = 1

	-- give siteAdmin Role to siteAdmin Group
	EXEC @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteID,
		@siteResourceID=@siteResourceID,
		@include=1,
		@functionID=null,
		@roleID=@siteAdminRoleID,
		@groupID=@siteAdminGroupID,
		@memberID=null,
		@inheritedRightsResourceID=null,
		@inheritedRightsFunctionID=null,
		@resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- give superAdmin Role to superAdmin Group
	EXEC @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteID,
		@siteResourceID=@siteResourceID,
		@include=1,
		@functionID=null,
		@roleID=@superAdminRoleID,
		@groupID=@superAdminGroupID,
		@memberID=null,
		@inheritedRightsResourceID=null,
		@inheritedRightsFunctionID=null,
		@resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- add default hostname
	DECLARE @hostnameID int	
	EXEC @rc = dbo.createSiteHostName @siteID=@siteID, @hostname=@mainhostname, @useRedirect=null, @hostnameID=@hostnameID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- add default template
	DECLARE @templateTypeID int
	SELECT @templateTypeID = dbo.fn_getTemplateTypeID('Page')
	EXEC @rc = dbo.cms_CreatePageTemplate @siteid=@siteID, @templateTypeID=@templateTypeID, @templateName='Default', @templateDesc='Default site template', @templateFileName='Default', @templateID=@templateID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @templateID = 0 GOTO on_error2
	
	-- add default page section
	SELECT @modeID = dbo.fn_getModeID('Normal')
	SELECT @sectionResourceTypeID = dbo.fn_getResourceTypeID('SystemCreatedSection')
	EXEC @rc = dbo.cms_createPageSection @siteID=@siteID, @sectionResourceTypeID=@sectionResourceTypeID, @ovTemplateID=@templateID, @ovModeID=@modeID, @parentSectionID=null, @sectionName='Root', @sectionCode='Root', @inheritPlacements=1, @sectionID=@sectionID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- default fieldsets
	EXEC @rc = dbo.cms_createDefaultFieldsets @siteid=@siteID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add default pages
	EXEC @rc = dbo.cms_createDefaultPages @siteid=@siteID, @sectionid=@sectionID, @languageID=@defaultLanguageID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add default Relationship Categories
	EXEC @rc = dbo.cms_createDefaultRelationshipCategories @siteid=@siteID, @contributingMemberID=461530
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add to network
	EXEC @rc = dbo.createNetworkSite @networkID=@mainNetworkID, @siteID=@siteID, @isLoginNetwork=@isLoginNetwork, @isMasterSite=@isMasterSite
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add default FULL Frequency
	insert into dbo.sub_frequencies(frequencyName, frequency, frequencyShortName, uid, 
		rateRequired, hasInstallments, monthlyInterval, isSystemRate, siteID, status)
	values('Full', 1, 'F', newid(), 1, 1, 1, 1, @siteID, 'A')	
		IF @@ERROR <> 0 GOTO on_error2

	-- add content objects
	DECLARE @sysCreatedContentResourceTypeID int, @activesiteResourceStatusID int, @newContentid int, @newresourceid int
	select @sysCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('SystemCreatedContent')
	select @activesiteResourceStatusID = dbo.fn_getResourceStatusId('Active')
	EXEC @rc = dbo.cms_createContentObject 
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'NoRights',
		@contentDesc = null,
		@rawContent = @norightsContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET noRightsContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject 
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'NoRightsNotLoggedIn',
		@contentDesc = null,
		@rawContent = @norightsNotLoggedInContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET noRightsNotLoggedInContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'InactiveUser',
		@contentDesc = null,
		@rawContent = @inactiveUserContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET InactiveUserContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'SiteAgreement',
		@contentDesc = null,
		@rawContent = @siteagreementContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET SiteAgreementContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'Welcome Message',
		@contentDesc = null,
		@rawContent = @welcomeMessageContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET welcomeMessageContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'First Time Login Message',
		@contentDesc = null,
		@rawContent = @firstTimeLoginContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET firstTimeLoginContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2

	-- link up superusers to all new sites
	INSERT INTO dbo.ams_memberNetworkProfiles (memberID, profileID, [status], dateCreated, siteID)
	SELECT distinct mnp.memberID, mnp.profileID, 'A', getdate(), @siteID
	FROM dbo.ams_memberNetworkProfiles AS mnp 
	INNER JOIN dbo.ams_networkProfiles AS np ON mnp.profileID = np.profileID
	WHERE mnp.status = 'A'
	AND np.networkID = dbo.fn_getNetworkID('MemberCentral Super Administrators')
	AND np.status = 'A'
	AND mnp.siteID <> @siteID
		IF @@ERROR <> 0 GOTO on_error2

	EXEC @rc = dbo.cms_populateSiteResourceRightsCache @siteID=@siteID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @siteID = 0
	RETURN -1

on_error2:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1

GO

--  
-- Script to Update dbo.cms_siteResources in DEV02\PLATFORM2008.membercentral 
-- Generated Tuesday, May 21, 2013, at 05:49 PM 
--  
-- Please backup DEV02\PLATFORM2008.membercentral before executing this script
--  
-- ** Script Begin **
BEGIN TRANSACTION
GO
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE
GO

PRINT 'Updating dbo.cms_siteResources Table'
GO

SET ANSI_NULLS, ANSI_PADDING, ANSI_WARNINGS, ARITHABORT, QUOTED_IDENTIFIER, CONCAT_NULL_YIELDS_NULL ON
GO

SET NUMERIC_ROUNDABORT OFF
GO


IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
BEGIN
   PRINT 'dbo.cms_siteResources Table Updated Successfully'
   COMMIT TRANSACTION
END ELSE
BEGIN
   PRINT 'Failed To Update dbo.cms_siteResources Table'
END
GO


declare @resourceTypeID int, @resourceTypeName varchar(50), @functionID int, @functionName varchar(50), @functionDisplayName varchar(50), @addToSuperUserRole bit, @addToSiteAdminRole bit

DECLARE @rc int, @siteAdminRoleID int, @superAdminRoleID int, @ResourceTypeFunctionID int

set @addToSuperUserRole = 1
set @addToSiteAdminRole = 1
set @resourceTypeName = 'UserCreatedContent'
set @functionName = 'EditContent'



--- Do not change below this line

select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)
select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')
select @siteAdminRoleID = dbo.fn_getResourceRoleID('Site Administrator')

select @functionID = dbo.fn_getResourceFunctionID(@functionName,@resourceTypeID)
select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);


IF (@addToSuperUserRole = 1)
	exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@superAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;

IF (@addToSiteAdminRole = 1)
	exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@siteAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;

GO
USE [memberCentral]
GO
/****** Object:  StoredProcedure [dbo].[cms_getPageStructureByID]    Script Date: 05/22/2013 15:19:26 ******/
ALTER PROCEDURE [dbo].[cms_getPageStructureByID] 
@siteID int,
@pageID int,
@languageID int,
@ovrMode varchar(30),
@processMobileOverrides bit,
@templateTypeName varchar(100)

AS

DECLARE @templateID int, @sectionID int, @pgResourceTypeID int, @modeID int, @ovrModeID int, @defaultTemplateID int, @defaultModeID int, @rootSectionID int
DECLARE @viewFunctionID int, @editFunctionID int
DECLARE @layoutInfo TABLE (templateID int, templateTypeName varchar(100), modeID int, modeName varchar(100), templateFilename varchar(100), sitecode varchar(10), orgcode varchar(10))
DECLARE @pageSectionPath TABLE (templateID int, modeID int, sectionID int, parentSectionID int, dataLevel int PRIMARY KEY,includePlacements bit)
DECLARE @zoneAssignments TABLE (autoid int IDENTITY(1,1), zoneID int, assignmentLevel varchar(10), sectionID int, siteResourceID int, sortOrder int, dataLevel int, isSSL bit, appInstanceID int, appInstanceName varchar(100), appTypeName varchar(100), appWidgetInstanceID int, appInstanceResourceID int, appWidgetTypeName varchar(100), appWidgetInstanceName varchar(100),contentID int,enableSocialMediaSharing bit, resourceTypeID int, resourceType varchar(100), resourceTypeClassName varchar(100), objectType char(1))

set @defaultTemplateID = 1
set @defaultModeID = 1

select @ovrModeID = modeID from cms_pageModes where modeName = @ovrMode

select @pgResourceTypeID = sr.resourceTypeID, @sectionID = p.sectionID, @modeID = pm.modeID, @templateID = isnull(mobilept.templateID,pt.templateid)
from cms_pages p (nolock)
inner join sites s
	on s.siteID = p.siteID
inner join cms_siteResources sr (nolock)
	on p.siteResourceID = sr.siteResourceID
	and pageID = @pageID
inner join dbo.cache_cms_derivedPageSectionSettings dpss (nolock)
	on dpss.sectionID = p.sectionID
inner join dbo.cms_pageTemplates pt (nolock)
	on pt.templateid = coalesce(p.ovTemplateID,dpss.ovTemplateID,@defaultTemplateID)
inner join dbo.cms_pageModes pm (nolock)
	on pm.modeID = coalesce(@ovrModeID,p.ovModeID,dpss.ovModeID,@defaultModeID)
left outer join dbo.cms_pageTemplates mobilept (nolock)
	on mobilept.templateid = coalesce(p.ovTemplateIDMobile,dpss.ovTemplateIDMobile)
	--and s.enableMobile = 1
	and @processMobileOverrides = 1


select @rootSectionID = sectionID from cms_pageSections where siteID = @siteID and parentSectionID is null

if (nullif(@ovrMode,'') is not null)
	select @ovrModeID = modeID from cms_pageModes where modeName = @ovrMode

-- find all zone assignments for page, not considering pageMode
insert into @zoneAssignments (zoneid, siteResourceID, assignmentLevel, sectionID, sortOrder, dataLevel,resourceTypeID,	resourceType, resourceTypeClassName,isSSL,contentID,enableSocialMediaSharing,objecttype)
select pzr.zoneid, pzr.siteResourceID, 'page' as assignmentLevel, 0 as sectionID, pzr.sortOrder, 0 as dataLevel, srt.resourceTypeID,srt.resourceType,srtc.resourceTypeClassName, isnull(c.isSSL,1),c.contentID, c.enableSocialMediaSharing,
objecttype = case
	when c.siteresourceID is not null then 'C'
	when ai.siteresourceID is not null then 'A'
	when awi.siteresourceID is not null then 'W'
end

from dbo.cms_pageZonesResources as pzr
inner join dbo.cms_siteResources sr
	on pzr.siteResourceID = sr.siteResourceID
	and pzr.pageID = @pageID
inner join dbo.cms_siteResourceStatuses srs
	ON sr.siteResourceStatusID = srs.siteResourceStatusID
	AND srs.siteResourceStatusDesc = 'Active'
inner join dbo.cms_siteResourceTypes srt ON sr.resourceTypeID = srt.resourceTypeID
inner join dbo.cms_siteResourceTypeClasses srtc ON srt.resourceTypeClassID = srtc.resourceTypeClassID
left outer join dbo.cms_content c on sr.siteResourceID = c.siteResourceID 
left outer join dbo.cms_applicationInstances ai on sr.siteResourceID = ai.siteResourceID
left outer join dbo.cms_applicationWidgetInstances awi on sr.siteResourceID = awi.siteResourceID

union all

select pszr.zoneid, pszr.siteResourceID, 
	assignmentLevel = case when rps.sectionID = @rootSectionID then 'site' else 'section' end,
	rps.sectionID, pszr.sortOrder, (rps.depth * -1) as dataLevel, srt.resourceTypeID,	srt.resourceType, srtc.resourceTypeClassName, isnull(c.isSSL,1),c.contentID, c.enableSocialMediaSharing,
	objecttype = case
		when c.siteresourceID is not null then 'C'
		when ai.siteresourceID is not null then 'A'
		when awi.siteresourceID is not null then 'W'
	end
		
from dbo.cms_pageSectionsZonesResources pszr
inner join dbo.cache_cms_recursivePageSections rps
	on rps.startSectionID = @sectionID
	and rps.sectionid = pszr.sectionid
	and rps.includePlacements = 1
inner join dbo.cms_siteResources sr
	on pszr.siteResourceID = sr.siteResourceID
inner join dbo.cms_siteResourceStatuses srs
	ON sr.siteResourceStatusID = srs.siteResourceStatusID
	AND srs.siteResourceStatusDesc = 'Active'
inner join dbo.cms_siteResourceTypes srt ON sr.resourceTypeID = srt.resourceTypeID
inner join dbo.cms_siteResourceTypeClasses srtc ON srt.resourceTypeClassID = srtc.resourceTypeClassID
left outer join dbo.cms_content c on sr.siteResourceID = c.siteResourceID 
left outer join dbo.cms_applicationInstances ai on sr.siteResourceID = ai.siteResourceID
left outer join dbo.cms_applicationWidgetInstances awi on sr.siteResourceID = awi.siteResourceID


-- update Application related columns
if exists (select top 1 siteresourceID from @zoneAssignments where objecttype = 'A')
	update za
	set
		appInstanceID = ai.applicationInstanceID,
		appInstanceName = ai.applicationInstanceName, 
		appTypeName = at.applicationTypeName, 
		appInstanceResourceID = ai.siteResourceID,
		isSSL = 1
	from @zoneAssignments za
	inner join dbo.cms_applicationInstances ai
		on za.siteResourceID = ai.siteResourceID
		and za.objecttype = 'A'
	inner join dbo.cms_applicationTypes at ON ai.applicationTypeID = at.applicationTypeID

--update Application Widget related columns
if exists (select top 1 siteresourceID from @zoneAssignments where objecttype = 'W')
	update za
	set
		appInstanceID = ai.applicationInstanceID,
		appInstanceName = ai.applicationInstanceName, 
		appTypeName = at.applicationTypeName, 
		appInstanceResourceID = ai.siteResourceID, 
		appWidgetInstanceID = awi.applicationWidgetInstanceID , 
		appWidgetTypeName = awt.applicationWidgetTypeName, 
		appWidgetInstanceName = awi.applicationWidgetInstanceName,
		isSSL = 1
	from @zoneAssignments za
	inner join dbo.cms_applicationWidgetInstances awi
		on za.siteResourceID = awi.siteResourceID
		and za.objecttype = 'W'
	inner join dbo.cms_applicationInstances ai ON awi.applicationInstanceID = ai.applicationInstanceID
	inner join dbo.cms_applicationWidgetTypes awt ON awi.applicationWidgetTypeID = awt.applicationWidgetTypeID
	inner join dbo.cms_applicationTypes at ON ai.applicationTypeID = at.applicationTypeID



-- is page offered in requested page language? if not, use site default language
IF NOT EXISTS (select pageLanguageID from dbo.cms_pageLanguages where pageID = @pageID and languageID = @languageID)
	SELECT @languageID = defaultLanguageID from sites where siteID = @siteID
select cast(isNull((
select page.pageID, page.pageName, page.allowReturnAfterLogin, page.siteResourceID as pageSiteResourceID,
	isnull(sites.siteID,0) as siteID,
	isnull(sites.siteCode,0) as siteCode,
	isnull(organizations.orgID,0) as orgID,
	isnull(organizations.orgCode,0) as orgCode,
	ISNULL(ps.sectionID,'') as sectionID, 
	ISNULL(ps.sectionName,'') as sectionName, 
	ISNULL(ps.sectionCode,'') as sectionCode,
	@languageID as pageLanguageID,
	ISNULL(pl.pageTitle,'') as pageTitle, 
	ISNULL(pl.pageDesc,'') as pageDesc, 
	ISNULL(pl.keywords,'') as keywords, 
	ISNULL(pt.templateFileName,'DefaultTemplate') as layoutFileName,
	ISNULL(pt.templateID,'') as templateID,
	ISNULL(ptt.templateTypeName,'') as templateTypeName,
	ISNULL(pm.modeName,'Normal') as layoutMode, 
	ISNULL(templateSite.sitecode,'') as layoutSiteCode,
	ISNULL(templateOrg.orgcode,'') as layoutOrgCode, 
	pageZone.zoneName, 
	siteResource.siteResourceID,
	ISNULL(sites.siteID,0) as siteID,
	ISNULL(sites.siteCode,'') as siteCode,
	ISNULL(organizations.orgID,0) as orgID,
	ISNULL(organizations.orgCode,'') as orgCode,
	ISNULL(siteResource.resourceType,'') as resourceType,
	ISNULL(siteResource.resourceTypeClassName,'') as resourceClass,
	ISNULL(siteResource.assignmentLevel,'') as assignmentLevel,
	ISNULL(siteResource.sectionID,'') as assignmentSectionID,
	ISNULL(siteResource.isSSL,'') as isSSL, 
	ISNULL(siteResource.appInstanceID,'') as appInstanceID, 
	ISNULL(siteResource.appInstanceName,'') as appInstanceName, 
	ISNULL(siteResource.appTypeName,'') as appTypeName, 
	ISNULL(siteResource.appWidgetInstanceID,'') as appWidgetInstanceID, 
	ISNULL(siteResource.appInstanceResourceID,'') as appInstanceResourceID, 
	ISNULL(siteResource.appWidgetTypeName,'') as appWidgetTypeName, 
	ISNULL(siteResource.appWidgetInstanceName,'') as appWidgetInstanceName,
	ISNULL(siteResource.contentID,'') as contentID,
	ISNULL(siteResource.enableSocialMediaSharing,'') as enableSocialMediaSharing
from dbo.cms_pages as page (nolock)
inner join dbo.sites (nolock)
	on sites.siteID = page.siteID
	and page.pageID = @pageID
inner join dbo.organizations (nolock)
	on sites.orgID = organizations.orgID
inner join dbo.cms_pageSections (nolock) as ps
	on ps.sectionID = page.sectionID
inner join dbo.cms_pageTemplates pt (nolock)
	on pt.templateid = @templateID
inner join dbo.cms_pageTemplateTypes ptt (nolock)
	on pt.templateTypeID = ptt.templateTypeID
	and ptt.templateTypeName = @templateTypeName
inner join dbo.cms_pageModes pm (nolock)
	on pm.modeID = @modeID
left outer join dbo.sites templateSite (nolock)
	inner join dbo.organizations templateOrg (nolock)
		on templateSite.orgid = templateOrg.orgid
on templateSite.siteid = pt.siteid
inner join dbo.cms_pageLanguages as pl (nolock) 
	on pl.pageID = page.pageid and pl.languageid = @languageID
inner join dbo.cms_pageTemplatesModesZones as ptmz (nolock)
	on pm.modeid = ptmz.modeid and ptmz.templateID = pt.templateID
inner join dbo.cms_pageZones as pageZone (nolock)
	on pageZone.zoneid = ptmz.zoneid
left outer join @zoneAssignments as siteResource
	on ptmz.zoneID = siteResource.zoneid
order by siteResource.zoneid, siteResource.dataLevel desc, siteResource.sortOrder
for xml auto, root('pageStructure')
),'<pageStructure />') as xml) as pageStructureXML
OPTION(RECOMPILE)


RETURN 0
GO
USE [memberCentral]
GO
ALTER PROCEDURE [dbo].[cms_getCMSResourceByID]
@siteID int,
@siteResourceID int,
@languageID int,
@memberID int,
@ovrMode varchar(30)
AS

DECLARE @activeMemberID int
select @activeMemberID = dbo.fn_getActiveMemberID(@memberID)

DECLARE @templateID int, @modeID int, @ovrModeID int, @siteCode varchar(10), @mainZoneID int
DECLARE @layoutInfo TABLE (templateID int, templateTypeName varchar(100), modeID int, modeName varchar(100), templateFilename varchar(100), sitecode varchar(10), orgcode varchar(10))
DECLARE @zoneAssignments TABLE (autoid int IDENTITY(1,1), zoneID int, siteResourceID int, sortOrder int, dataLevel int)

select @siteCode = dbo.fn_getSiteCodeFromSiteID(@siteID)

declare @usetemplateID int, @usemodeID int
select TOP 1 @usetemplateID = ovtemplateID from cms_pageSections ps where siteID = @siteID and parentSectionID is null

select @usemodeID = modeID from cms_pageModes where modeName = @ovrMode
select @mainZoneID = zoneid from cms_pageZones where zoneName = 'Main'

-- if usetemplateid is null then use template 1 - platform default
IF @usetemplateID is null 
	select @usetemplateID = 1

insert into @layoutInfo (templateID, templateTypeName, modeID, modeName, templateFilename, sitecode, orgcode)
select pSp.templateID, ptt.templateTypeName, pSp.modeID, pm.modeName, pt.templateFilename, s2.sitecode, o.orgcode
from (select @usetemplateID as templateID, @usemodeID as modeID) as pSp
inner join dbo.cms_pageTemplates as pt on pSp.templateID = pt.templateid
inner join dbo.cms_pageTemplateTypes ptt on pt.templateTypeID = ptt.templateTypeID
inner join dbo.cms_pageModes as pm on pSp.modeID = pm.modeID
left outer join dbo.sites as s2 on s2.siteid = pt.siteid
left outer join dbo.organizations as o on s2.orgid = o.orgid

-- find all zone assignments for page, not considering pageMode
insert into @zoneAssignments (zoneid, siteResourceID, sortOrder, dataLevel)
select @mainZoneID, @siteResourceID, 1 as sortOrder, 0 as dataLevel

-- use site default language
SELECT @languageID = defaultLanguageID from sites where siteCode = @sitecode

-- Now that we know template and mode, figure out page pod assignments 
select cast(isNull((
select page.pageID, page.pageName, page.allowReturnAfterLogin, page.siteResourceID as pageSiteResourceID, 
	isnull(siteResource.siteID,0) as siteID,
	isnull(siteResource.siteCode,0) as siteCode,
	isnull(siteResource.orgID,0) as orgID,
	isnull(siteResource.orgCode,0) as orgCode,
	page.sectionname, page.sectionCode, @languageID as pageLanguageID,
	coalesce(siteResourceDetails.applicationWidgetInstanceName,siteResourceDetails.applicationInstanceName,'') as pageTitle, 
	'' as pageDesc, 
	'' as keywords, 
	ISNULL(layout.templateFileName,'DefaultTemplate') as layoutFileName,
	ISNULL(layout.templateID,'') as templateID,
	ISNULL(layout.templateTypeName,'') as templateTypeName,
	ISNULL(layout.modeName,'Normal') as layoutMode, 
	ISNULL(layout.sitecode,'') as layoutSiteCode, ISNULL(layout.orgcode,'') as layoutOrgCode, 
	pageZone.zoneName, 
	siteResource.siteResourceID,
	dbo.fn_checkResourceRights(siteResource.siteResourceID,dbo.fn_getResourceFunctionID('view',siteResource.resourceTypeID),@activeMemberID,@siteID) as allowed,
	ISNULL(siteResource.resourceType,'') as resourceType,
	ISNULL(siteResource.resourceTypeClassName,'') as resourceClass,
	ISNULL(siteResourceDetails.applicationInstanceID,'') as appInstanceID, 
	ISNULL(siteResourceDetails.applicationInstanceName,'') as appInstanceName, 
	ISNULL(siteResourceDetails.applicationTypeName,'') as appTypeName, 
	ISNULL(siteResourceDetails.applicationWidgetInstanceID,'') as appWidgetInstanceID, 
	ISNULL(siteResourceDetails.applicationInstanceResourceID,'') as appInstanceResourceID, 
	ISNULL(siteResourceDetails.applicationWidgetTypeName,'') as appWidgetTypeName, 
	ISNULL(siteResourceDetails.applicationWidgetInstanceName,'') as appWidgetInstanceName,
	ISNULL(siteResourceDetails.contentID,'') as contentID,
	ISNULL(siteResourceDetails.enableSocialMediaSharing,'') as enableSocialMediaSharing
from (select '' as pageID, '' as pageName, 0 as siteResourceID, 0 as allowReturnAfterLogin, '' as sectionName, '' as sectionCode, '' as sectionID) as page
inner join @layoutInfo as layout on 1=1
inner join cms_pageTemplatesModesZones as ptmz on layout.modeid = ptmz.modeid and ptmz.templateID = layout.templateID
inner join cms_pageZones as pageZone on pageZone.zoneid = ptmz.zoneid
left outer join @zoneAssignments as zr
	inner join (
		select sr.siteResourceID, srt.resourceTypeID, srt.resourceType, srtc.resourceTypeClassName, s.siteID, o.orgID, s.siteCode, o.orgCode
		from dbo.cms_siteResources sr
		inner join dbo.cms_siteResourceTypes as srt on sr.resourceTypeID = srt.resourceTypeID
		inner join dbo.cms_siteResourceTypeClasses as srtc on srt.resourceTypeClassID = srtc.resourceTypeClassID
		inner join dbo.cms_siteResourceStatuses as srStatus on sr.siteResourceStatusID = srStatus.siteResourceStatusID and srStatus.siteResourceStatusDesc = 'Active'
		inner join dbo.sites s on sr.siteID = s.siteID
		inner join dbo.organizations o on s.orgid = o.orgid
		) as siteResource on siteResource.siteResourceID = zr.siteResourceID
	inner join dbo.fn_getCMSResourcesForSite(@siteid) as siteResourceDetails 
		on siteResource.siteResourceID = siteResourceDetails.siteResourceID and siteResourceDetails.siteResourceID = @siteResourceID
	on ptmz.zoneID = zr.zoneid
order by zr.zoneid, zr.dataLevel desc, zr.sortOrder
for xml auto, root('pageStructure')
),'<pageStructure />') as xml) as pageStructureXML

RETURN 0
GO
USE [memberCentral]
GO
/****** Object:  StoredProcedure [dbo].[cms_getPageStructureByID]    Script Date: 05/22/2013 15:19:26 ******/
ALTER PROCEDURE [dbo].[cms_getPageStructureByID] 
@siteID int,
@pageID int,
@languageID int,
@ovrMode varchar(30),
@processMobileOverrides bit,
@templateTypeName varchar(100)

AS

DECLARE @templateID int, @sectionID int, @pgResourceTypeID int, @modeID int, @ovrModeID int, @defaultTemplateID int, @defaultModeID int, @rootSectionID int, @pageIncludePlacements int
DECLARE @viewFunctionID int, @editFunctionID int
DECLARE @layoutInfo TABLE (templateID int, templateTypeName varchar(100), modeID int, modeName varchar(100), templateFilename varchar(100), sitecode varchar(10), orgcode varchar(10))
DECLARE @pageSectionPath TABLE (templateID int, modeID int, sectionID int, parentSectionID int, dataLevel int PRIMARY KEY,includePlacements bit)
DECLARE @zoneAssignments TABLE (autoid int IDENTITY(1,1), zoneID int, assignmentLevel varchar(10), sectionID int, siteResourceID int, sortOrder int, dataLevel int, isSSL bit, appInstanceID int, appInstanceName varchar(100), appTypeName varchar(100), appWidgetInstanceID int, appInstanceResourceID int, appWidgetTypeName varchar(100), appWidgetInstanceName varchar(100),contentID int,enableSocialMediaSharing bit, resourceTypeID int, resourceType varchar(100), resourceTypeClassName varchar(100), objectType char(1))

set @defaultTemplateID = 1
set @defaultModeID = 1

select @ovrModeID = modeID from cms_pageModes where modeName = @ovrMode

select 
	@pgResourceTypeID = sr.resourceTypeID, 
	@sectionID = p.sectionID, 
	@modeID = pm.modeID, 
	@templateID = isnull(mobilept.templateID,pt.templateid),
	@pageIncludePlacements = p.inheritPlacements
from cms_pages p (nolock)
inner join sites s
	on s.siteID = p.siteID
inner join cms_siteResources sr (nolock)
	on p.siteResourceID = sr.siteResourceID
	and pageID = @pageID
inner join dbo.cache_cms_derivedPageSectionSettings dpss (nolock)
	on dpss.sectionID = p.sectionID
inner join dbo.cms_pageTemplates pt (nolock)
	on pt.templateid = coalesce(p.ovTemplateID,dpss.ovTemplateID,@defaultTemplateID)
inner join dbo.cms_pageModes pm (nolock)
	on pm.modeID = coalesce(@ovrModeID,p.ovModeID,dpss.ovModeID,@defaultModeID)
left outer join dbo.cms_pageTemplates mobilept (nolock)
	on mobilept.templateid = coalesce(p.ovTemplateIDMobile,dpss.ovTemplateIDMobile)
	--and s.enableMobile = 1
	and @processMobileOverrides = 1


select @rootSectionID = sectionID from cms_pageSections where siteID = @siteID and parentSectionID is null

if (nullif(@ovrMode,'') is not null)
	select @ovrModeID = modeID from cms_pageModes where modeName = @ovrMode

-- find all zone assignments for page, not considering pageMode
insert into @zoneAssignments (zoneid, siteResourceID, assignmentLevel, sectionID, sortOrder, dataLevel,resourceTypeID,	resourceType, resourceTypeClassName,isSSL,contentID,enableSocialMediaSharing,objecttype)
select pzr.zoneid, pzr.siteResourceID, 'page' as assignmentLevel, 0 as sectionID, pzr.sortOrder, 0 as dataLevel, srt.resourceTypeID,srt.resourceType,srtc.resourceTypeClassName, isnull(c.isSSL,1),c.contentID, c.enableSocialMediaSharing,
objecttype = case
	when c.siteresourceID is not null then 'C'
	when ai.siteresourceID is not null then 'A'
	when awi.siteresourceID is not null then 'W'
end

from dbo.cms_pageZonesResources as pzr
inner join dbo.cms_siteResources sr
	on pzr.siteResourceID = sr.siteResourceID
	and pzr.pageID = @pageID
inner join dbo.cms_siteResourceStatuses srs
	ON sr.siteResourceStatusID = srs.siteResourceStatusID
	AND srs.siteResourceStatusDesc = 'Active'
inner join dbo.cms_siteResourceTypes srt ON sr.resourceTypeID = srt.resourceTypeID
inner join dbo.cms_siteResourceTypeClasses srtc ON srt.resourceTypeClassID = srtc.resourceTypeClassID
left outer join dbo.cms_content c on sr.siteResourceID = c.siteResourceID 
left outer join dbo.cms_applicationInstances ai on sr.siteResourceID = ai.siteResourceID
left outer join dbo.cms_applicationWidgetInstances awi on sr.siteResourceID = awi.siteResourceID


if @pageIncludePlacements = 1
BEGIN
	insert into @zoneAssignments (zoneid, siteResourceID, assignmentLevel, sectionID, sortOrder, dataLevel,resourceTypeID,	resourceType, resourceTypeClassName,isSSL,contentID,enableSocialMediaSharing,objecttype)
	select pszr.zoneid, pszr.siteResourceID, 
		assignmentLevel = case when rps.sectionID = @rootSectionID then 'site' else 'section' end,
		rps.sectionID, pszr.sortOrder, (rps.depth * -1) as dataLevel, srt.resourceTypeID,	srt.resourceType, srtc.resourceTypeClassName, isnull(c.isSSL,1),c.contentID, c.enableSocialMediaSharing,
		objecttype = case
			when c.siteresourceID is not null then 'C'
			when ai.siteresourceID is not null then 'A'
			when awi.siteresourceID is not null then 'W'
		end
			
	from dbo.cms_pageSectionsZonesResources pszr
	inner join dbo.cache_cms_recursivePageSections rps
		on rps.startSectionID = @sectionID
		and rps.sectionid = pszr.sectionid
		and rps.includePlacements = 1
	inner join dbo.cms_siteResources sr
		on pszr.siteResourceID = sr.siteResourceID
	inner join dbo.cms_siteResourceStatuses srs
		ON sr.siteResourceStatusID = srs.siteResourceStatusID
		AND srs.siteResourceStatusDesc = 'Active'
	inner join dbo.cms_siteResourceTypes srt ON sr.resourceTypeID = srt.resourceTypeID
	inner join dbo.cms_siteResourceTypeClasses srtc ON srt.resourceTypeClassID = srtc.resourceTypeClassID
	left outer join dbo.cms_content c on sr.siteResourceID = c.siteResourceID 
	left outer join dbo.cms_applicationInstances ai on sr.siteResourceID = ai.siteResourceID
	left outer join dbo.cms_applicationWidgetInstances awi on sr.siteResourceID = awi.siteResourceID
END

-- update Application related columns
if exists (select top 1 siteresourceID from @zoneAssignments where objecttype = 'A')
	update za
	set
		appInstanceID = ai.applicationInstanceID,
		appInstanceName = ai.applicationInstanceName, 
		appTypeName = at.applicationTypeName, 
		appInstanceResourceID = ai.siteResourceID,
		isSSL = 1
	from @zoneAssignments za
	inner join dbo.cms_applicationInstances ai
		on za.siteResourceID = ai.siteResourceID
		and za.objecttype = 'A'
	inner join dbo.cms_applicationTypes at ON ai.applicationTypeID = at.applicationTypeID

--update Application Widget related columns
if exists (select top 1 siteresourceID from @zoneAssignments where objecttype = 'W')
	update za
	set
		appInstanceID = ai.applicationInstanceID,
		appInstanceName = ai.applicationInstanceName, 
		appTypeName = at.applicationTypeName, 
		appInstanceResourceID = ai.siteResourceID, 
		appWidgetInstanceID = awi.applicationWidgetInstanceID , 
		appWidgetTypeName = awt.applicationWidgetTypeName, 
		appWidgetInstanceName = awi.applicationWidgetInstanceName,
		isSSL = 1
	from @zoneAssignments za
	inner join dbo.cms_applicationWidgetInstances awi
		on za.siteResourceID = awi.siteResourceID
		and za.objecttype = 'W'
	inner join dbo.cms_applicationInstances ai ON awi.applicationInstanceID = ai.applicationInstanceID
	inner join dbo.cms_applicationWidgetTypes awt ON awi.applicationWidgetTypeID = awt.applicationWidgetTypeID
	inner join dbo.cms_applicationTypes at ON ai.applicationTypeID = at.applicationTypeID



-- is page offered in requested page language? if not, use site default language
IF NOT EXISTS (select pageLanguageID from dbo.cms_pageLanguages where pageID = @pageID and languageID = @languageID)
	SELECT @languageID = defaultLanguageID from sites where siteID = @siteID
select cast(isNull((
select page.pageID, page.pageName, page.allowReturnAfterLogin, page.siteResourceID as pageSiteResourceID,
	isnull(sites.siteID,0) as siteID,
	isnull(sites.siteCode,0) as siteCode,
	isnull(organizations.orgID,0) as orgID,
	isnull(organizations.orgCode,0) as orgCode,
	ISNULL(ps.sectionID,'') as sectionID, 
	ISNULL(ps.sectionName,'') as sectionName, 
	ISNULL(ps.sectionCode,'') as sectionCode,
	@languageID as pageLanguageID,
	ISNULL(pl.pageTitle,'') as pageTitle, 
	ISNULL(pl.pageDesc,'') as pageDesc, 
	ISNULL(pl.keywords,'') as keywords, 
	ISNULL(pt.templateFileName,'DefaultTemplate') as layoutFileName,
	ISNULL(pt.templateID,'') as templateID,
	ISNULL(ptt.templateTypeName,'') as templateTypeName,
	ISNULL(pm.modeName,'Normal') as layoutMode, 
	ISNULL(templateSite.sitecode,'') as layoutSiteCode,
	ISNULL(templateOrg.orgcode,'') as layoutOrgCode, 
	pageZone.zoneName, 
	siteResource.siteResourceID,
	ISNULL(sites.siteID,0) as siteID,
	ISNULL(sites.siteCode,'') as siteCode,
	ISNULL(organizations.orgID,0) as orgID,
	ISNULL(organizations.orgCode,'') as orgCode,
	ISNULL(siteResource.resourceType,'') as resourceType,
	ISNULL(siteResource.resourceTypeClassName,'') as resourceClass,
	ISNULL(siteResource.assignmentLevel,'') as assignmentLevel,
	ISNULL(siteResource.sectionID,'') as assignmentSectionID,
	ISNULL(siteResource.isSSL,'') as isSSL, 
	ISNULL(siteResource.appInstanceID,'') as appInstanceID, 
	ISNULL(siteResource.appInstanceName,'') as appInstanceName, 
	ISNULL(siteResource.appTypeName,'') as appTypeName, 
	ISNULL(siteResource.appWidgetInstanceID,'') as appWidgetInstanceID, 
	ISNULL(siteResource.appInstanceResourceID,'') as appInstanceResourceID, 
	ISNULL(siteResource.appWidgetTypeName,'') as appWidgetTypeName, 
	ISNULL(siteResource.appWidgetInstanceName,'') as appWidgetInstanceName,
	ISNULL(siteResource.contentID,'') as contentID,
	ISNULL(siteResource.enableSocialMediaSharing,'') as enableSocialMediaSharing
from dbo.cms_pages as page (nolock)
inner join dbo.sites (nolock)
	on sites.siteID = page.siteID
	and page.pageID = @pageID
inner join dbo.organizations (nolock)
	on sites.orgID = organizations.orgID
inner join dbo.cms_pageSections (nolock) as ps
	on ps.sectionID = page.sectionID
inner join dbo.cms_pageTemplates pt (nolock)
	on pt.templateid = @templateID
inner join dbo.cms_pageTemplateTypes ptt (nolock)
	on pt.templateTypeID = ptt.templateTypeID
	and ptt.templateTypeName = @templateTypeName
inner join dbo.cms_pageModes pm (nolock)
	on pm.modeID = @modeID
left outer join dbo.sites templateSite (nolock)
	inner join dbo.organizations templateOrg (nolock)
		on templateSite.orgid = templateOrg.orgid
on templateSite.siteid = pt.siteid
inner join dbo.cms_pageLanguages as pl (nolock) 
	on pl.pageID = page.pageid and pl.languageid = @languageID
inner join dbo.cms_pageTemplatesModesZones as ptmz (nolock)
	on pm.modeid = ptmz.modeid and ptmz.templateID = pt.templateID
inner join dbo.cms_pageZones as pageZone (nolock)
	on pageZone.zoneid = ptmz.zoneid
left outer join @zoneAssignments as siteResource
	on ptmz.zoneID = siteResource.zoneid
order by siteResource.zoneid, siteResource.dataLevel desc, siteResource.sortOrder
for xml auto, root('pageStructure')
),'<pageStructure />') as xml) as pageStructureXML
OPTION(RECOMPILE)


RETURN 0
GO


/******************************** Dan's stuff ***********************/

ALTER PROC [dbo].[createSite]
	@orgID int,
	@sitecode varchar(10),
	@siteName varchar(60),
	@mainNetworkID int,
	@isLoginNetwork bit,
	@isMasterSite bit,
	@defaultLanguageID int,
	@defaultTimeZoneID int,
	@defaultCurrencyTypeID int,
	@showCurrencyType bit,
	@hasSSL bit,
	@isSSL bit,
	@enableMobile bit,
	@allowGuestAccounts bit,
	@forceLoginPage bit,
	@useRemoteLogin bit,
	@affiliationRequired bit,
	@providesFreeFastCase bit,
	@enforceSiteAgreement bit,
	@allowMemberUpdates bit,
	@immediateMemberUpdates bit,
	@emailMemberUpdates varchar(200),
	@defaultPostalState varchar(10),
	@joinURL varchar(100),
	@pdfPassword varchar(30),
	@alternateGuestAccountCreationLink varchar(400),
	@alternateGuestAccountPopup bit,
	@alternateForgotPasswordLink varchar(400),
	@mainhostname varchar(80),
	@norightsContent varchar(max),
	@norightsNotLoggedInContent varchar(max),
	@inactiveUserContent varchar(max),
	@siteagreementContent varchar(max),
	@welcomeMessageContent varchar(max),
	@firstTimeLoginContent varchar(max),
	@siteID int OUTPUT

AS

DECLARE @rc int, @templateID int, @modeID int, @sectionID int, 
	@sectionResourceTypeID int, @siteAdminRoleID int, @superAdminRoleID int, 
	@siteAdminGroupID int, @superAdminGroupID int, @trashID int

BEGIN TRAN
	-- check for existing sitecode
	SELECT @siteID = null
	SELECT @siteID = siteID FROM dbo.sites where sitecode = @sitecode

	-- if not there, add it
	IF @siteID is not null
		GOTO on_error

	-- insert sites
	INSERT INTO dbo.sites (orgID, sitecode, siteName, defaultLanguageID, defaultTimeZoneId, defaultCurrencyTypeID,
		hasSSL, allowGuestAccounts, forceLoginPage, useRemoteLogin, affiliationRequired, 
		providesFreeFastCase, allowMemberUpdates, enforceSiteAgreement, immediateMemberUpdates, emailMemberUpdates, 
		defaultPostalState, joinURL, pdfPassword, alternateGuestAccountCreationLink, alternateGuestAccountPopup, 
		alternateForgotPasswordLink, showCurrencyType,isSSL, enableMobile)
	VALUES (@orgID, @sitecode, @siteName, @defaultLanguageID, @defaultTimeZoneId, @defaultCurrencyTypeID, 
		@hasSSL, @allowGuestAccounts, @forceLoginPage, @useRemoteLogin, @affiliationRequired, 
		@providesFreeFastCase, @allowMemberUpdates, @enforceSiteAgreement, @immediateMemberUpdates, @emailMemberUpdates, 
		@defaultPostalState, @joinURL, @pdfPassword, @alternateGuestAccountCreationLink, @alternateGuestAccountPopup, 
		@alternateForgotPasswordLink, @showCurrencyType,@isSSL, @enableMobile)
		IF @@ERROR <> 0 GOTO on_error
		SELECT @siteID = SCOPE_IDENTITY()

	-- createSiteLanguage		
	EXEC @rc = dbo.createSiteLanguage @siteID=@siteID, @languageID=@defaultLanguageID		
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- get resourceType for site
	declare @siteResourceTypeID int
	select @siteResourceTypeID = dbo.fn_getResourceTypeID('Site')

	-- get active resource status
	declare @siteResourceStatusID int
	select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

	-- create a resourceID for the site
	DECLARE @siteResourceID int	
	exec dbo.cms_createSiteResource
		@resourceTypeID = @siteResourceTypeID,
		@siteResourceStatusID = @siteResourceStatusID,
		@siteID = @siteid,
		@isVisible = 1,
		@parentSiteResourceID = null,
		@siteResourceID = @siteResourceID OUTPUT
		IF @@ERROR <> 0 OR @siteResourceID = 0 GOTO on_error
	
	-- update site with new resource
	UPDATE dbo.sites
	SET siteResourceID = @siteResourceID
	WHERE siteID = @siteID
		IF @@ERROR <> 0 GOTO on_error
		
	-- roles
	select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')
	select @siteAdminRoleID = dbo.fn_getResourceRoleID('Site Administrator')
	select @siteAdminGroupID = groupID 
		from dbo.ams_groups 
		where groupCode = 'SiteAdmins' 
		and orgID = @orgID
	select @superAdminGroupID = groupID 
		from dbo.ams_groups 
		where groupCode = 'SuperAdmins' 
		and orgID = 1

	-- give siteAdmin Role to siteAdmin Group
	EXEC @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteID,
		@siteResourceID=@siteResourceID,
		@include=1,
		@functionID=null,
		@roleID=@siteAdminRoleID,
		@groupID=@siteAdminGroupID,
		@memberID=null,
		@inheritedRightsResourceID=null,
		@inheritedRightsFunctionID=null,
		@resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- give superAdmin Role to superAdmin Group
	EXEC @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteID,
		@siteResourceID=@siteResourceID,
		@include=1,
		@functionID=null,
		@roleID=@superAdminRoleID,
		@groupID=@superAdminGroupID,
		@memberID=null,
		@inheritedRightsResourceID=null,
		@inheritedRightsFunctionID=null,
		@resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- add default hostname
	DECLARE @hostnameID int	
	EXEC @rc = dbo.createSiteHostName @siteID=@siteID, @hostname=@mainhostname, @useRedirect=null, @hostnameID=@hostnameID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- add default template
	DECLARE @templateTypeID int
	SELECT @templateTypeID = dbo.fn_getTemplateTypeID('Page')
	EXEC @rc = dbo.cms_CreatePageTemplate @siteid=@siteID, @templateTypeID=@templateTypeID, @templateName='Default', @templateDesc='Default site template', @templateFileName='Default', @templateID=@templateID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @templateID = 0 GOTO on_error2
	
	-- add default page section
	SELECT @modeID = dbo.fn_getModeID('Normal')
	SELECT @sectionResourceTypeID = dbo.fn_getResourceTypeID('SystemCreatedSection')
	EXEC @rc = dbo.cms_createPageSection @siteID=@siteID, @sectionResourceTypeID=@sectionResourceTypeID, @ovTemplateID=@templateID, @ovTemplateIDMobile=null, @ovModeID=@modeID, @parentSectionID=null, @sectionName='Root', @sectionCode='Root', @inheritPlacements=1, @sectionID=@sectionID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- default fieldsets
	EXEC @rc = dbo.cms_createDefaultFieldsets @siteid=@siteID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add default pages
	EXEC @rc = dbo.cms_createDefaultPages @siteid=@siteID, @sectionid=@sectionID, @languageID=@defaultLanguageID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add default Relationship Categories
	EXEC @rc = dbo.cms_createDefaultRelationshipCategories @siteid=@siteID, @contributingMemberID=461530
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add to network
	EXEC @rc = dbo.createNetworkSite @networkID=@mainNetworkID, @siteID=@siteID, @isLoginNetwork=@isLoginNetwork, @isMasterSite=@isMasterSite
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add default FULL Frequency
	insert into dbo.sub_frequencies(frequencyName, frequency, frequencyShortName, uid, 
		rateRequired, hasInstallments, monthlyInterval, isSystemRate, siteID, status)
	values('Full', 1, 'F', newid(), 1, 1, 1, 1, @siteID, 'A')	
		IF @@ERROR <> 0 GOTO on_error2

	-- add content objects
	DECLARE @sysCreatedContentResourceTypeID int, @activesiteResourceStatusID int, @newContentid int, @newresourceid int
	select @sysCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('SystemCreatedContent')
	select @activesiteResourceStatusID = dbo.fn_getResourceStatusId('Active')
	EXEC @rc = dbo.cms_createContentObject 
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'NoRights',
		@contentDesc = null,
		@rawContent = @norightsContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET noRightsContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject 
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'NoRightsNotLoggedIn',
		@contentDesc = null,
		@rawContent = @norightsNotLoggedInContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET noRightsNotLoggedInContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'InactiveUser',
		@contentDesc = null,
		@rawContent = @inactiveUserContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET InactiveUserContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'SiteAgreement',
		@contentDesc = null,
		@rawContent = @siteagreementContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET SiteAgreementContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'Welcome Message',
		@contentDesc = null,
		@rawContent = @welcomeMessageContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET welcomeMessageContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'First Time Login Message',
		@contentDesc = null,
		@rawContent = @firstTimeLoginContent,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET firstTimeLoginContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2

	-- link up superusers to all new sites
	INSERT INTO dbo.ams_memberNetworkProfiles (memberID, profileID, [status], dateCreated, siteID)
	SELECT distinct mnp.memberID, mnp.profileID, 'A', getdate(), @siteID
	FROM dbo.ams_memberNetworkProfiles AS mnp 
	INNER JOIN dbo.ams_networkProfiles AS np ON mnp.profileID = np.profileID
	WHERE mnp.status = 'A'
	AND np.networkID = dbo.fn_getNetworkID('MemberCentral Super Administrators')
	AND np.status = 'A'
	AND mnp.siteID <> @siteID
		IF @@ERROR <> 0 GOTO on_error2

	EXEC @rc = dbo.cms_populateSiteResourceRightsCache @siteID=@siteID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @siteID = 0
	RETURN -1

on_error2:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1

GO
ALTER PROCEDURE [dbo].[cms_createApplicationInstanceAdmin]
	@siteid int,
	@languageID int,
	@sectionID int,
	@isVisible bit,
	@pageName varchar(50),
	@pageTitle varchar(200),
	@pagedesc varchar(400),
	@zoneID int,
	@pageTemplateID int,
	@pageModeID int,
	@pgResourceTypeID int,
	@pgParentResourceID int = null,
	@allowReturnAfterLogin bit,
	@applicationInstanceName varchar(100),
	@applicationInstanceDesc varchar(200),
	@applicationInstanceID int OUTPUT,
	@siteResourceID int OUTPUT,
	@pageID int OUTPUT
AS

-- null OUTPUT vars
SELECT @applicationInstanceID = null, @siteResourceID = null, @pageID = null

DECLARE @appCreatedSectionResourceTypeID int, @applicationTypeID int, @rootSectionID int, @rc int
DECLARE @documentSectionName varchar(50)

select @appCreatedSectionResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedSection')
select @applicationTypeID = applicationTypeID from cms_applicationTypes where applicationTypeName = 'Admin'
	
BEGIN TRAN

exec @rc = dbo.cms_createApplicationInstance
		@siteid = @siteid,
		@languageID = @languageID,
		@sectionID = @sectionID,
		@applicationTypeID = @applicationTypeID,
		@isVisible = @isVisible,
		@pageName = @pageName,
		@pageTitle = @pageTitle,
		@pagedesc = @pagedesc,
		@zoneID = @zoneID,
		@pageTemplateID = @pageTemplateID,
		@pageModeID = @pageModeID,
		@pgResourceTypeID = @pgResourceTypeID,
		@pgParentResourceID = @pgParentResourceID,
		@allowReturnAfterLogin = @allowReturnAfterLogin,
		@applicationInstanceName = @applicationInstanceName,
		@applicationInstanceDesc = @applicationInstanceDesc,
		@applicationInstanceID = @applicationInstanceID OUTPUT,
		@siteResourceID = @siteResourceID OUTPUT,
		@pageID = @pageID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

select @documentSectionName = 'MemberDocument'

exec @rc = dbo.cms_createPageSection
		@siteID = @siteID, 
		@sectionResourceTypeID = @appCreatedSectionResourceTypeID, 
		@ovTemplateID = NULL,
		@ovTemplateIDMobile=NULL,
		@ovModeID = NULL, 
		@parentSectionID = @sectionID, 
		@sectionName = @documentSectionName, 
		@sectionCode = @documentSectionName,
		@inheritPlacements = 1,
		@sectionID = @rootSectionID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- update parentSiteResourceID of section
UPDATE sr
SET sr.parentSiteResourceID = @siteResourceID
FROM cms_pageSections s
INNER JOIN cms_siteResources sr on s.siteResourceID = sr.siteResourceID
	AND s.sectionID = @rootSectionID
	IF @@ERROR <> 0 GOTO on_error

-- update settings xml
UPDATE dbo.cms_applicationInstances
SET settingsXML = '<settings><setting name="memberDocumentSectionID" value="' + cast(@rootSectionID as varchar(8)) + + '" /><setting name="showMemberDocuments" value="false" /><setting name="showNotes" value="false" /><setting name="showRelationships" value="false" /><setting name="showSubscriptions" value="false" /><setting name="showMemberHistory" value="false" /><setting name="showReferrals" value="false" /><setting name="showApptTracker" value="false" /><setting name="showMemberPhotosInSearchResults" value="true" /><setting name="numPerPageInSearchResults" value="25" /></settings>'
WHERE applicationInstanceID = @applicationInstanceID
	IF @@ERROR <> 0 GOTO on_error

-- create the suite of tools
EXEC @rc = dbo.createAdminSuite @siteID
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- create admin member lookup fieldset
declare @memberAdminSRID int, @fieldSetID int, @fieldID int, @useID int
select @memberAdminSRID = dbo.fn_getSiteResourceIDForResourceType('MemberAdmin',@siteID)
EXEC @rc = dbo.ams_createMemberFieldSet @siteID=@siteID, @fieldsetName='Member Admin Search Form', @nameformat='LSXPFM', @showHelp=0, @fieldsetID=@fieldSetID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldSetID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_firstname', @fieldLabel='First Name', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_lastname', @fieldLabel='Last Name', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_company', @fieldLabel='Company', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_membernumber', @fieldLabel='Member Number', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = ams_createMemberFieldUsage @siteResourceID=@memberAdminSRID, @fieldsetID=@fieldSetID, @area='search', @createSiteResourceID=0, @useID=@useID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @useID = 0 GOTO on_error

EXEC @rc = dbo.ams_createMemberFieldSet @siteID=@siteID, @fieldsetName='Member Admin Search Results', @nameformat='LSXPFM', @showHelp=0, @fieldsetID=@fieldSetID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldSetID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_prefix', @fieldLabel='Prefix', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_middlename', @fieldLabel='Middle Name', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_suffix', @fieldLabel='Suffix', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = ams_createMemberFieldUsage @siteResourceID=@memberAdminSRID, @fieldsetID=@fieldSetID, @area='results', @createSiteResourceID=0, @useID=@useID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @useID = 0 GOTO on_error

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1

GO

ALTER PROCEDURE [dbo].[cms_createApplicationInstanceFileShare2]
	@siteid int,
	@languageID int,
	@sectionID int,
	@isVisible bit,
	@pageName varchar(50),
	@pageTitle varchar(200),
	@pagedesc varchar(400),
	@zoneID int,
	@pageTemplateID int,
	@pageModeID int,
	@pgResourceTypeID int,
	@pgParentResourceID int = null,
	@allowReturnAfterLogin bit,
	@applicationInstanceName varchar(100),
	@applicationInstanceDesc varchar(200),
	@applicationInstanceID int OUTPUT,
	@siteResourceID int OUTPUT,
	@pageID int OUTPUT
AS

-- null OUTPUT vars
SELECT @applicationInstanceID = null, @siteResourceID = null, @pageID = null

DECLARE @appCreatedSectionResourceTypeID int, @applicationTypeID int, @rootSectionID int, @rc int, @trashID int
DECLARE @documentSectionName varchar(50)

select @appCreatedSectionResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedSection')
select @applicationTypeID = applicationTypeID from cms_applicationTypes where applicationTypeName = 'FileShare2'
	
BEGIN TRAN

exec @rc = dbo.cms_createApplicationInstance
		@siteid = @siteid,
		@languageID = @languageID,
		@sectionID = @sectionID,
		@applicationTypeID = @applicationTypeID,
		@isVisible = @isVisible,
		@pageName = @pageName,
		@pageTitle = @pageTitle,
		@pagedesc = @pagedesc,
		@zoneID = @zoneID,
		@pageTemplateID = @pageTemplateID,
		@pageModeID = @pageModeID,
		@pgResourceTypeID = @pgResourceTypeID,
		@pgParentResourceID = @pgParentResourceID,
		@allowReturnAfterLogin = @allowReturnAfterLogin,
		@applicationInstanceName = @applicationInstanceName,
		@applicationInstanceDesc = @applicationInstanceDesc,
		@applicationInstanceID = @applicationInstanceID OUTPUT,
		@siteResourceID = @siteResourceID OUTPUT,
		@pageID = @pageID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

select @documentSectionName = 'FileShare2 ' + cast(@applicationInstanceID as varchar(8))

exec @rc = dbo.cms_createPageSection
		@siteID = @siteID, 
		@sectionResourceTypeID = @appCreatedSectionResourceTypeID, 
		@ovTemplateID = NULL,
		@ovTemplateIDMobile=NULL,
		@ovModeID = NULL, 
		@parentSectionID = @sectionID, 
		@sectionName = @documentSectionName, 
		@sectionCode = @documentSectionName,
		@inheritPlacements = 1,
		@sectionID = @rootSectionID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- update parentSiteResourceID of section
update sr
set sr.parentSiteResourceID = @siteResourceID
from cms_pageSections s
	inner join cms_siteResources sr on s.siteResourceID = sr.siteResourceID
		and s.sectionID = @rootSectionID

insert into dbo.fs_fileShare (applicationInstanceID, rootSectionID,showPublicationDate)
values (@applicationInstanceID,@rootSectionID,0)
	IF @@ERROR <> 0 GOTO on_error

exec @rc = cms_createCategoryTreeForFileShare2 @siteID = @siteID,@fs2SiteResourceID = @siteResourceID,
															@categoryTreeName = 'Default', @categoryTreeID = @trashID OUTPUT
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO


ALTER PROC [dbo].[cms_createDefaultPages]
@siteid int,
@sectionid int,
@languageID int

AS

declare @appPageID int

-- get resourceType for system created pages and HTML content
declare @pgResourceTypeID int, @HTMLResourceTypeID int, @appPgResourceTypeID int, @siteResourceTypeID int
select @pgResourceTypeID = dbo.fn_getResourceTypeID('SystemCreatedPage')
select @HTMLResourceTypeID = dbo.fn_getResourceTypeID('UserCreatedContent')
select @appPgResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedPage')
select @siteResourceTypeID = dbo.fn_getResourceTypeID('site')

-- get siteresourceid
declare @websiteResourceID int
select @websiteResourceID = siteResourceID from sites where siteID = @siteID

-- get active resource status
declare @siteResourceStatusID int
select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

-- get Main Zone
declare @mainZoneID int
select @mainZoneID = dbo.fn_getZoneID('Main')

-- get page modes
declare @fullModeID int, @directModeID int, @streamModeID int
select @fullModeID = dbo.fn_getModeID('Full')
select @directModeID = dbo.fn_getModeID('Direct')
select @streamModeID = dbo.fn_getModeID('Stream')

-- get resource functions
declare @viewFID int, @loginFID int
SELECT @viewFID = dbo.fn_getResourceFunctionID('view',@HTMLResourceTypeID)
SELECT @loginFID = dbo.fn_getResourceFunctionID('login',@siteResourceTypeID)


-- get public, siteadmin group
declare @publicGID int, @siteadminGID int, @usersGID int, @guestGID int
select @publicGID = g.groupID from dbo.ams_groups as g inner join dbo.sites as s on s.orgid = g.orgid where g.isSystemGroup = 1 and g.groupName = 'Public' AND g.status <> 'D' and s.siteid = @siteid
select @usersGID = g.groupID from dbo.ams_groups as g inner join dbo.sites as s on s.orgid = g.orgid where g.isSystemGroup = 1 and g.groupName = 'Users' AND g.status <> 'D' and s.siteid = @siteid
select @guestGID = g.groupID from dbo.ams_groups as g inner join dbo.sites as s on s.orgid = g.orgid where g.isSystemGroup = 1 and g.groupName = 'Guests' AND g.status <> 'D' and s.siteid = @siteid
select @siteadminGID = g.groupID from dbo.ams_groups as g inner join dbo.sites as s on s.orgid = g.orgid where g.isSystemGroup = 1 and g.groupName = 'Site Administrators' AND g.status <> 'D' and s.siteid = @siteid

-- create default pages and applications
DECLARE @rc int, @pageID int, @contentID int, @contentSiteResourceID int, @applicationTypeID int, @suggestedPageName varchar(100), @applicationInstanceID int, @siteresourceID int, @trashID int
BEGIN TRAN

	-- main page and content
	EXEC @rc = dbo.cms_createPage 
		@siteid=@siteid, 
		@languageID=@languageID, 
		@resourceTypeID=@pgResourceTypeID, 
		@siteResourceStatusID=@siteResourceStatusID, 
		@pgParentResourceID=null, 
		@isVisible=1, 
		@sectionID=@sectionID, 
		@ovTemplateID=null, 
		@ovTemplateIDMobile=null,
		@ovModeID=null, 
		@pageName='Main', 
		@pageTitle='Welcome', 
		@pageDesc=null, 
		@keywords=null,
		@inheritPlacements=1,
		@allowReturnAfterLogin=1, 
		@checkReservedNames=0, 
		@pageID=@pageID OUTPUT

		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = dbo.cms_createContent @siteID=@siteid, @pageID=@pageID, @zoneID=@mainZoneID, @resourceTypeID=@HTMLResourceTypeID, @siteResourceStatusID=@siteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle='Welcome', @contentDesc=null, @rawContent='Put your homepage text here. You can include tables, images, lists, and more! Our built-in content editor can do all of this and more.', @contentID=@contentID, @contentSiteResourceID=@contentSiteResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- login app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'Login'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=1, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@fullModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- org doc download app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'OrgDocDownload'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- store doc download app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'StoreDocDownload'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- ts doc download app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'TSDocDownload'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- account locator
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'accountLocator'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@directModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1,
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null,
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null,
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- userinfo app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'UserInfo'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@directModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- updatemember app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'UpdateMember'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=1, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=null, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- Ticket 8352718 - default permissions when setting up a site
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@usersGID, @memberID=null, 
		@inheritedRightsResourceID=@websiteResourceID, @inheritedRightsFunctionID=@loginFID, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@guestGID, @memberID=null, 
		@inheritedRightsResourceID=@websiteResourceID, @inheritedRightsFunctionID=@loginFID, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- ContentEditor app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'ContentEditor'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@fullModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- ajax app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'Ajax'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	
	-- Flash Express Install app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'flashExpressInstall'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	
	-- AppProxy app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'appProxy'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	
	-- invoices app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'invoices'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@fullModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- BuyNow app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'buyNow'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@fullModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	
	-- ViewCart app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'viewCart'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@fullModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	
	-- Support app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'Support'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=null, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- admin app
	-- set template used by the admin app to the admin template
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'Admin'
	EXEC @rc = dbo.cms_createApplicationInstanceAdmin @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle='Website Control Panel', @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=2, @pageModeID=null, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@siteadminGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	--set admin page inheritPlacements = 0
	update cms_pages set inheritPlacements = 0 where pageID = @appPageID
	IF @@ERROR <> 0 GOTO on_error

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1

GO
ALTER PROCEDURE [dbo].[cms_createApplicationInstanceStore] 
@siteid int,
@languageID int,
@sectionID int,
@isVisible bit,
@pageName varchar(50),
@pageTitle varchar(200),
@pagedesc varchar(400),
@zoneID int,
@pageTemplateID int,
@pageModeID int,
@pgResourceTypeID int,
@defaultGLAccountID int,
@allowReturnAfterLogin bit,
@applicationInstanceName varchar(100),
@applicationInstanceDesc varchar(200),
@applicationInstanceID int OUTPUT,
@siteResourceID int OUTPUT,
@pageID int OUTPUT

AS

BEGIN TRAN

declare @rc int, @applicationTypeID int, @appCreatedContentResourceTypeID int, @activeSiteResourceStatusID int,
	@maincontentID int, @maincontentSiteResourceID int
DECLARE @documentSectionName varchar(50), @appCreatedSectionResourceTypeID int, @rootSectionID int

select @applicationInstanceID = null
select @siteResourceID = null
select @pageID = null
select @appCreatedSectionResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedSection')
select @applicationTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'Store'
select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')
select @activeSiteResourceStatusID = dbo.fn_getResourceStatusId('Active')

-- create instance
EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, 
		@applicationTypeID=@applicationTypeID, @isVisible=@isVisible, @pageName=@pageName, 
		@pageTitle=@pageTitle, @pageDesc=@pagedesc, @zoneID=@zoneID, @pagetemplateid=@pageTemplateID,
		@pageModeID=@pageModeID, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID=null, 
		@allowReturnAfterLogin=@allowReturnAfterLogin, @applicationInstanceName=@applicationInstanceName, 
		@applicationInstanceDesc=@applicationInstanceDesc, @applicationInstanceID=@applicationInstanceID OUTPUT, 
		@siteresourceID=@siteResourceID OUTPUT, 
		@pageID=@pageID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- create mainContent
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@languageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='Welcome to the store.<br/><br/>Click on the links to the left to browse the catalog.<br/><br/>Please note that applicable taxes and shipping charges will be added to purchases.', 
	@contentID=@maincontentID OUTPUT, @siteResourceID=@maincontentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- setup a section to hold downloadable content
select @documentSectionName = 'StoreDocs ' + cast(@applicationInstanceID as varchar(8))

exec @rc = dbo.cms_createPageSection
		@siteID = @siteID, 
		@sectionResourceTypeID = @appCreatedSectionResourceTypeID, 
		@ovTemplateID = NULL,
		@ovTemplateIDMobile=NULL,
		@ovModeID = NULL, 
		@parentSectionID = @sectionID, 
		@sectionName = @documentSectionName, 
		@sectionCode = @documentSectionName,
		@inheritPlacements = 1,
		@sectionID = @rootSectionID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- update parentSiteResourceID of section
update sr
set sr.parentSiteResourceID = @siteResourceID
from cms_pageSections s
	inner join cms_siteResources sr on s.siteResourceID = sr.siteResourceID
		and s.sectionID = @rootSectionID

-- add store
INSERT INTO dbo.store (siteID, applicationInstanceID, mainContentID, showProductID, MaxRecords, showCartThumbnails, rootSectionID, GLAccountID, shippingGLAccountID)
VALUES (@siteid, @applicationInstanceID, @maincontentID, 0, 25, 0, @rootSectionID, @defaultGLAccountID, @defaultGLAccountID)
	IF @@ERROR <> 0 GOTO on_error

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1

GO
ALTER PROCEDURE [dbo].[cms_createApplicationInstanceCommunity] 
@siteid int,
@languageID int,
@sectionID int,
@isVisible bit,
@pageName varchar(50),
@pageTitle varchar(200),
@pagedesc varchar(400),
@zoneID int,
@pageTemplateID int,
@subPageTemplateID int,
@pageModeID int,
@pgResourceTypeID int,
@pgParentResourceID int=null,
@allowReturnAfterLogin bit,
@applicationInstanceName varchar(100),
@applicationInstanceDesc varchar(200),
@applicationInstanceID int OUTPUT,
@siteResourceID int OUTPUT,
@pageID int OUTPUT

AS

BEGIN TRAN

declare @rc int, @commApplicationTypeID int, @communityID int, @appCreatedSectionResourceTypeID int
declare @rootSectionID int
DECLARE @templateid int, @zoneIDA int, @modeID int,
	@applicationWidgetTypeID int,
	@applicationWidgetInstanceName varchar(50),
	@applicationWidgetInstanceDesc varchar(100),
	@applicationWidgetInstanceID int,
	@widgetSiteResourceID int,
	@pageSiteResourceID int,
	@trashID int,
	@ovTemplateIDMobile int


declare @viewFunctionID int



select @applicationInstanceID = null
select @siteResourceID = null
select @pageID = null
select @ovTemplateIDMobile = null
select @commApplicationTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'Community'
select @appCreatedSectionResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedSection')
select @zoneIDA = dbo.fn_getZoneID('A')
select @modeID = dbo.fn_getModeID('Normal')

select @viewFunctionID = dbo.fn_getResourceFunctionID('View',@commApplicationTypeID)
	IF @@ERROR <> 0 GOTO on_error


-- create instance
EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, 
		@applicationTypeID=@commApplicationTypeID, @isVisible=@isVisible, @pageName=@pageName, 
		@pageTitle=@pageTitle, @pageDesc=@pagedesc, @zoneID=@zoneID, @pagetemplateid=@pageTemplateID,
		@pageModeID=@pageModeID, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID = @pgParentResourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
		@applicationInstanceName=@applicationInstanceName, @applicationInstanceDesc=@applicationInstanceDesc, 
		@applicationInstanceID=@applicationInstanceID OUTPUT, 
		@siteresourceID=@siteResourceID OUTPUT, 
		@pageID=@pageID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


select @pageSiteResourceID = siteResourceID from cms_pages where pageID = @pageID

-- create section for community
EXEC @rc = dbo.cms_createPageSection 
		@siteID=@siteID, 
		@sectionResourceTypeID=@appCreatedSectionResourceTypeID,
		@ovTemplateID=@subPageTemplateID,
		@ovTemplateIDMobile=@ovTemplateIDMobile, 
		@ovModeID=@modeID, 
		@parentSectionID=@sectionID, 
		@sectionName=@pageName,
		@sectionCode=@pageName, 
		@inheritPlacements=0,
		@sectionID=@rootSectionID OUTPUT
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- update parentSiteResourceID of section
update sr
set sr.parentSiteResourceID = @siteResourceID
from cms_pageSections s
inner join cms_siteResources sr on s.siteResourceID = sr.siteResourceID	and s.sectionID = @rootSectionID
IF @@ERROR <> 0 GOTO on_error
-- add community
INSERT INTO dbo.comm_communities (applicationInstanceID, communityName, communityDescription, rootSectionID)
VALUES (@applicationInstanceID, left(@pageTitle,100), @pagedesc, @rootSectionID)
	IF @@ERROR <> 0 GOTO on_error
	select @communityID = SCOPE_IDENTITY()

-- create homepage for community
DECLARE @AppSubPageTypeID int, @siteResourceStatusID int, @commpgTemplateID int
DECLARE @commpgName varchar(60), @commpgTitle varchar(210), @commpgPageID int, @communityPageID int
DECLARE @userCreatedContentResourceTypeID int, @contentID int, @contentSiteResourceID int
SELECT @AppSubPageTypeID = dbo.fn_getResourceTypeId('ApplicationSubPage')
select @siteResourceStatusID = dbo.fn_getResourceStatusId('Active')
select @commpgName = @pageName + 'Home'
select @userCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('UserCreatedContent')
EXEC @rc = dbo.cms_createPage @siteid=@siteid, @languageID=@languageID, @resourceTypeID=@AppSubPageTypeID, 
	@siteResourceStatusID=@siteResourceStatusID, @pgParentResourceID = @siteresourceID, @isVisible=@isVisible, @sectionid=@rootSectionID, 
	@ovTemplateID=NULL, @ovTemplateIDMobile=@ovTemplateIDMobile, @ovModeID=NULL, @pageName=@commpgName, @pageTitle='Home',
	@pageDesc=null, @keywords=null, @allowReturnAfterLogin=0, @inheritPlacements=1, @checkReservedNames=1, @pageID=@commpgPageID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @commpgPageID = 0 GOTO on_error

UPDATE dbo.comm_communities 
SET defaultCommunityPageName = @commpgName
WHERE communityID = @communityID
	IF @@ERROR <> 0 GOTO on_error
EXEC @rc = dbo.cms_createContent @siteID=@siteID, @pageID=@commpgPageID, @zoneID=@zoneID, 
	@resourceTypeID=@userCreatedContentResourceTypeID, @siteResourceStatusID=@siteResourceStatusID,
	@isSSL=0, @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle='Welcome', 
	@contentDesc='Default welcome message for community', 
	@rawContent='Welcome to this community. Click around and see what''s here.',
	@contentID=@contentID OUTPUT, @contentSiteResourceID=@contentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error



-- create navigation widget

select @applicationWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes
where applicationTypeID = @commApplicationTypeID and applicationWidgetTypeName = 'communityNavigation'



exec @rc = dbo.cms_createApplicationWidgetInstance
@siteid = @siteID,
@applicationInstanceID = @applicationInstanceID,
@applicationWidgetTypeID = @applicationWidgetTypeID,
@applicationWidgetInstanceName = 'Navigation',
@applicationWidgetInstanceDesc = 'Community Navigation',
@applicationWidgetInstanceID = @applicationWidgetInstanceID OUTPUT,
@siteResourceID = @widgetSiteResourceID OUTPUT
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

update cms_applicationWidgetInstances set
settingsXML = '<settings horizontal="True" communityHDR="True" />'
where applicationWidgetInstanceID = @applicationWidgetInstanceID 
IF @@ERROR <> 0 GOTO on_error

exec @rc = dbo.cms_createPageZoneResource
	@pageID = @pageID,
	@zoneID = @zoneID,
	@siteResourceID = @widgetSiteResourceID,
	@pzrID = @trashID OUTPUT
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

exec @rc = dbo.cms_moveZoneResourceUp
	@itemSiteResourceID=@widgetSiteResourceID, @containerSiteResourceID=@pageSiteResourceID, @zoneID=@zoneID
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

EXEC @rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@widgetSiteResourceID, @include=1, 
		@functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, 
		@inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@viewFunctionID, 
		@resourceRightID=@trashID OUTPUT
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


/* link view rights of all community page resources to community view right */


declare @SRID TABLE (autoid int IDENTITY(1,1), resourceID int, inheritedRightsResourceID int)
insert into @SRID (resourceID, inheritedRightsResourceID)
select pzr.siteResourceID as resourceID, ai.siteResourceID as inheritedRightsResourceID
from dbo.comm_communities comm 
inner join dbo.cms_applicationInstances ai on comm.applicationInstanceID = ai.applicationInstanceID
	and comm.communityID = @communityID
inner join dbo.cms_siteResources sr on parentSiteResourceID = ai.siteResourceID
inner join dbo.cms_pages p on sr.siteResourceID = p.siteResourceID
inner join dbo.cms_pageZonesResources pzr on p.pageID = pzr.pageID
	IF @@ERROR <> 0 GOTO on_error

DECLARE @minID int, @srr_resourceID int, @srr_inheritedRightsResourceID int, @resourceRightID int
SELECT @minID = min(autoid) from @SRID
WHILE @minID IS NOT NULL BEGIN
	SELECT @srr_resourceID=resourceID, @srr_inheritedRightsResourceID=inheritedRightsResourceID
	FROM @SRID
	WHERE autoid = @minID

	EXEC @rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@srr_resourceID, @include=1, 
		@functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, 
		@inheritedRightsResourceID=@srr_inheritedRightsResourceID, @inheritedRightsFunctionID=@viewFunctionID, 
		@resourceRightID=@resourceRightID OUTPUT
	IF @@ERROR <> 0 GOTO on_error

	SELECT @minID = min(autoid) from @SRID where autoid > @minID
END

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1

GO

ALTER PROCEDURE [dbo].[cms_createApplicationInstanceSocialNetwork] 
@siteid int,
@languageID int,
@sectionID int,
@isVisible bit,
@pageName varchar(50),
@pageTitle varchar(200),
@pagedesc varchar(400),
@zoneID int,
@pageTemplateID int,
@subPageTemplateID int,
@masterSiteID int,
@addressTypeID int,
@emailTypeID int,
@phoneTypeID int,
@pageModeID int,
@pgResourceTypeID int,
@pgParentResourceID int=null,
@allowReturnAfterLogin bit,
@applicationInstanceName varchar(100),
@applicationInstanceDesc varchar(200),
@creatorMemberID int,
@applicationInstanceID int OUTPUT,
@siteResourceID int OUTPUT,
@pageID int OUTPUT

AS

BEGIN TRAN

declare @rc int, @snApplicationTypeID int, @socialNetworkID int, @appCreatedSectionResourceTypeID int
declare @masterOrgID int, @masterSocialNetworkGroupID int, @masterSocialNetworkID int, @masterSocialNetworkApplicationInstanceID int
declare @rootSectionID int
declare @appWidgetTypeID int, @applicationWidgetInstanceID int, @applicationWidgetSiteResourceID int, @zoneIDA int, @zoneIDB int, @trashID int;
declare @participateFunctioniD int, @viewFunctionID int, @SNResourceTypeID int, @AddBlogFunctionID int, @editOwnFunctionID int, @deleteOwnFunctionID int, @canCommentFunctionID int


declare 
	@poolTypeID int,
	@SNMasterInstancePoolRoleTypeID int,
	@SNChildInstancePoolRoleTypeID int,
	@SNPoolID int



select @applicationInstanceID = null
select @siteResourceID = null
select @masterOrgID = null
select @pageID = null
select @snApplicationTypeID = applicationTypeID, @SNResourceTypeID = resourceTypeID from dbo.cms_applicationTypes where applicationTypeName = 'SocialNetwork'
select @appWidgetTypeID = applicationWidgetTypeID from dbo.cms_applicationWidgetTypes where applicationWidgetTypeName='socialNetworkNavigation' and applicationTypeID = @snApplicationTypeID
select @zoneIDA = dbo.fn_getZoneID('A')
select @zoneIDB = dbo.fn_getZoneID('B')

select 
	@participateFunctioniD = dbo.fn_getResourceFunctionID('Participate', @SNResourceTypeID),
	@viewFunctionID = dbo.fn_getResourceFunctionID('View', @SNResourceTypeID),
	@AddBlogFunctionID = dbo.fn_getResourceFunctionID('View', dbo.fn_getResourceTypeID('Blog')),
	@editOwnFunctionID = dbo.fn_getResourceFunctionID('editOwn', dbo.fn_getResourceTypeID('Blog')),
	@deleteOwnFunctionID = dbo.fn_getResourceFunctionID('deleteOwn', dbo.fn_getResourceTypeID('Blog')),
	@canCommentFunctionID = dbo.fn_getResourceFunctionID('canComment', dbo.fn_getResourceTypeID('Blog')),
	@appCreatedSectionResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedSection'),
	@masterOrgID =dbo.fn_getOrgIDFromSiteID(@masterSiteID),
	@poolTypeID = dbo.fn_getResourcePoolTypeID ('SNConfig'),
	@SNMasterInstancePoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'MasterInstance'),
	@SNChildInstancePoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'ChildInstance')

if @masterSiteID <> @siteID
	select 
		@masterSocialNetworkID=socialNetworkID,
		@masterSocialNetworkGroupID = masterSocialNetworkGroupID,
		@masterSocialNetworkApplicationInstanceID = ai.applicationInstanceID,
		@SNPoolID = srpm.poolID
	from sn_socialNetworks sn
	inner join cms_applicationInstances ai
		on sn.applicationinstanceID = ai.applicationInstanceID
		and ai.siteID = @masterSiteID
	inner join cms_siteResources sr on ai.siteResourceID = sr.siteResourceID
	inner join cms_siteResourceStatuses srs
		on sr.siteResourceStatusID = srs.siteResourceStatusID
		and srs.siteResourceStatusDesc = 'Active'
	inner join cms_siteResourcePoolMembers srpm
		on srpm.siteResourceID = ai.siteResourceID
		and srpm.poolRoleTypeID = @SNMasterInstancePoolRoleTypeID
	inner join cms_siteResourcePools srp
		on srpm.poolID = srp.poolID
else
	select @masterSocialNetworkID=null,
		@masterSocialNetworkGroupID = null,
		@masterSocialNetworkApplicationInstanceID = null,
		@SNPoolID = null


-- create instance
EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, 
		@applicationTypeID=@snApplicationTypeID, @isVisible=@isVisible, @pageName=@pageName, 
		@pageTitle=@pageTitle, @pageDesc=@pagedesc, @zoneID=@zoneID, @pagetemplateid=@pageTemplateID,
		@pageModeID=@pageModeID, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID = @pgParentResourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
		@applicationInstanceName=@applicationInstanceName, @applicationInstanceDesc=@applicationInstanceDesc, 
		@applicationInstanceID=@applicationInstanceID OUTPUT, 
		@siteresourceID=@siteResourceID OUTPUT, 
		@pageID=@pageID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
print 'Created SN instance'
-- create section for Social Network
EXEC @rc = dbo.cms_createPageSection 
		@siteID=@siteID, 
		@sectionResourceTypeID=@appCreatedSectionResourceTypeID,
		@ovTemplateID=@subPageTemplateID, 
		@ovTemplateIDMobile=NULL,
		@ovModeID=NULL, 
		@parentSectionID=@sectionID, 
		@sectionName=@pageName,
		@sectionCode=@pageName, 
		@inheritPlacements=0,
		@sectionID=@rootSectionID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


-- update parentSiteResourceID of section
update sr 
set sr.parentSiteResourceID = @siteResourceID
from cms_pageSections s
inner join cms_siteResources sr on s.siteResourceID = sr.siteResourceID	and s.sectionID = @rootSectionID
IF @@ERROR <> 0 GOTO on_error

-- create Navigation Widget and assign to zone A of section


EXEC @rc = dbo.cms_createApplicationWidgetInstance
	@siteid = @siteid,
	@applicationInstanceID = @applicationInstanceID,
	@applicationWidgetTypeID = @appWidgetTypeID,
	@applicationWidgetInstanceName = 'Social Network Navigation',
	@applicationWidgetInstanceDesc = 'Navigation',
	@applicationWidgetInstanceID = @applicationWidgetInstanceID OUTPUT,
	@siteResourceID = @applicationWidgetSiteResourceID  OUTPUT
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

exec @rc = dbo.cms_createPageSectionZoneResource
	@sectionID=@rootSectionID,
	@zoneID=@zoneIDA,
	@siteResourceID = @applicationWidgetSiteResourceID,
	@pszrID = @trashID OUTPUT
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- create a group for social network users if the masterSiteID and siteID are the same, this implies
-- a master site is being created.
IF @masterSiteID = @siteid BEGIN
	EXEC @rc = dbo.ams_createGroup 
			@orgID=@masterOrgID, 
			@groupCode=null, 
			@groupName='Social Network Users', 
			@groupDesc='Social Network Users', 
			@isSystemGroup=0, 
			@allowManualAssignment=0, 
			@parentGroupID=null, 
			@hideOnGroupLists=1,
			@groupID=@masterSocialNetworkGroupID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	update dbo.ams_groups
	set hideOnGroupLists = 1
	where groupID = @masterSocialNetworkGroupID
	IF @@ERROR <> 0 GOTO on_error


	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@siteResourceID, @include=1, @functionID=@participateFunctionID, @roleID=null, @groupID=@masterSocialNetworkGroupID, @memberID=null, @inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error



END

-- add Social Network
INSERT INTO dbo.sn_socialNetworks (applicationInstanceID, masterSiteID, rootSectionID, masterOrgID, masterSocialNetworkGroupID, masterSocialNetworkID,defaultPageAlias, addressTypeID, emailTypeID,phoneTypeID)
VALUES (@applicationInstanceID, @masterSiteID, @rootSectionID, @masterOrgID, @masterSocialNetworkGroupID,@masterSocialNetworkID,'home', @addressTypeID, @emailTypeID,@phoneTypeID)
	IF @@ERROR <> 0 GOTO on_error
	select @socialNetworkID = SCOPE_IDENTITY()

IF @masterSiteID <> @siteID BEGIN
	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@siteResourceID, @poolRoleTypeID=@SNChildInstancePoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
END
ELSE BEGIN

	print 'Doing Master Instance stuff'

	update sn_socialNetworks
	set masterSocialNetworkID = @socialNetworkID
	where socialNetworkID = @socialNetworkID

	set @masterSocialNetworkID = @socialNetworkID;


	declare
		@MemberManagerPoolRoleTypeID int,
		@SharedAppWallPoolRoleTypeID int,
		@SharedWidgetWallAddEntryPoolRoleTypeID int,
		@HomePoolRoleTypeID int,
		@ProfileEditorPoolRoleTypeID int,
		@ProfileViewerPoolRoleTypeID int,
		@OrgProfileViewerPoolRoleTypeID int,
		@SharedAppBlogPoolRoleTypeID int,
		@SharedAppPhotosPoolRoleTypeID int,
		@SharedAppVideosPoolRoleTypeID int,
		@SharedAppCommunityManagerPoolRoleTypeID int,
		@SharedAppWelcomePoolRoleTypeID int;


	select @MemberManagerPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'MemberManager')
	select @SharedAppWallPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'SharedAppWall')
	select @SharedWidgetWallAddEntryPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'SharedWidgetWallAddEntry')
	select @HomePoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'Home')
	select @ProfileEditorPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'ProfileEditor')
	select @ProfileViewerPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'ProfileViewer')
	select @OrgProfileViewerPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'OrgProfileViewer')
	select @SharedAppBlogPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'SharedAppBlog')
	select @SharedAppPhotosPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'SharedAppPhotos')
	select @SharedAppVideosPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'SharedAppVideos')
	select @SharedAppCommunityManagerPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'SharedAppCommunityManager')
	select @SharedAppWelcomePoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'SharedAppWelcome')

	-- create SNConfig Resource Pool
	exec dbo.cms_createSiteResourcePool
		@poolTypeID=@poolTypeID, @poolName='Social Network Config', @poolID = @SNPoolID OUTPUT

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@siteResourceID, @poolRoleTypeID=@SNMasterInstancePoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	
	declare
		@appSubPageResourceTypeID int,
		@subAppPageName varchar(100),
		@subAppPageID int,
		@snNetworkHomePageID int,
		@snNetworkHomeApplicationInstanceID int,
		@snViewProfilePageID int,
		@subAppApplicationInstanceID int,
		@subAppResourceID int,
		@snHomeAppTypeID int,
		@snmemberManagerAppTypeID int,
		@sneditProfileAppTypeID int,
		@snviewProfileAppTypeID int,
		@snviewOrgProfileAppTypeID int,
		@snblogAppTypeID int,
		@snstatusAppTypeID int,
		@snphotosAppTypeID int,
		@snPhotosApplicationInstanceID int,
		@snVideosAppTypeID int,
		@communityManagerAppTypeID int,
		@SNWelcomeApplicationTypeID int,
		@MasterInstanceUserGroupID int;

	declare
		@upcomingEventsWidgetTypeID int,
		@recentUploadsWidgetTypeID int,
		@recentPhotosWidgetTypeID int,
		@profilePictureWidgetTypeID int,
		@myStatusWidgetTypeID int,
		@myFriendsWidgetTypeID int,
		@activityFeedWidgetTypeID int,
		@applicationWidgetInstanceSiteResourceID int,
		@myFriendsWidgetInstanceSiteResourceID int,
		@profilePicWidgetInstanceSiteResourceID int,
		@myStatusWidgetInstanceSiteResourceID int,
		@activityFeedWidgetInstanceSiteResourceID int,
		@recentVideosWidgetTypeID int,
		@recentBlogsWidgetTypeID int,
		@recentCenterListMessagesWidgetTypeID int,
		@recentActiveMembersWidgetTypeID int;

	select @appSubPageResourceTypeID = dbo.fn_getResourceTypeID('ApplicationSubPage')
	select @snHomeAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'socialNetworkHome'
	select @snmemberManagerAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'MemberManager'
	select @sneditProfileAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'ProfileManager'
	select @snviewProfileAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'profileViewer'
	select @snviewOrgProfileAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'orgProfileViewer'

	select @snblogAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'Blog'
	select @snstatusAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'Blog'
	select @snphotosAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'PhotoGallery'
	select @snVideosAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'VideoGallery'
	select @communityManagerAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'CommunityCenter'
	select @SNWelcomeApplicationTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'SocialNetworkWelcome'


	select @recentPhotosWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationTypeID = @snphotosAppTypeID and applicationWidgetTypeName = 'recentPhotos'
	select @profilePictureWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationTypeID = @snApplicationTypeID and applicationWidgetTypeName = 'profilePic'
	select @myStatusWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationTypeID = @snstatusAppTypeID and applicationWidgetTypeName = 'addEntry'
	select @myFriendsWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationTypeID = @snApplicationTypeID and applicationWidgetTypeName = 'myFriends'
	select @activityFeedWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationTypeID = @snApplicationTypeID and applicationWidgetTypeName = 'socialNetworkActivity'
	select @recentVideosWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationTypeID = @snVideosAppTypeID and applicationWidgetTypeName = 'recentVideos'
	select @recentBlogsWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationTypeID = @snblogAppTypeID and applicationWidgetTypeName = 'recentBlogs'
	select @recentCenterListMessagesWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationTypeID = @communityManagerAppTypeID and applicationWidgetTypeName = 'recentCenterListMessages'
	select @recentActiveMembersWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationTypeID = @snmemberManagerAppTypeID and applicationWidgetTypeName = 'recentActiveMembers'


	select @MasterInstanceUserGroupID = groupID
	from ams_groups
	where parentGroupID is null
	and groupName = 'public'
	and orgID = @masterOrgID

	-- create shared Widgets for Master Network

	EXEC @rc = dbo.cms_createApplicationWidgetInstance
		@siteid=@siteID,
		@applicationInstanceID=@applicationInstanceID,
		@applicationWidgetTypeID=@profilePictureWidgetTypeID,
		@applicationWidgetInstanceName='Social Network Profile Picture',
		@applicationWidgetInstanceDesc='Profile Picture',
		@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
		@siteResourceID=@profilePicWidgetInstanceSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

/*
	EXEC @rc = dbo.cms_createApplicationWidgetInstance
		@siteid=@siteID,
		@applicationInstanceID=@applicationInstanceID,
		@applicationWidgetTypeID=@myFriendsWidgetTypeID,
		@applicationWidgetInstanceName='Social Network Friends',
		@applicationWidgetInstanceDesc='Friends',
		@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
		@siteResourceID=@myFriendsWidgetInstanceSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
*/
	EXEC @rc = dbo.cms_createApplicationWidgetInstance
		@siteid=@siteID,
		@applicationInstanceID=@applicationInstanceID,
		@applicationWidgetTypeID=@activityFeedWidgetTypeID ,
		@applicationWidgetInstanceName='Activity Feed',
		@applicationWidgetInstanceDesc='Social Networking Activity Feed',
		@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
		@siteResourceID=@activityFeedWidgetInstanceSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	-- create shared subApplications for Master network
	set @subAppPageName = @pageName + 'Wall';
	EXEC @rc = dbo.cms_createApplicationInstanceBlog @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@isVisible=@isVisible, @pageName=@subAppPageName, 
			@pageTitle='Social Network Wall', @pageDesc='Wall for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,@subPageTemplateID = null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@applicationInstanceName='Social Network Wall', @applicationInstanceDesc='Wall for Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@subAppPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@AddBlogFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@editOwnFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@deleteOwnFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@canCommentFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error




	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@subAppPageID,@socialNetworkID,'wall',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@SharedAppWallPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = dbo.cms_createApplicationWidgetInstance
		@siteid=@siteID,
		@applicationInstanceID=@subAppApplicationInstanceID,
		@applicationWidgetTypeID=@myStatusWidgetTypeID ,
		@applicationWidgetInstanceName='My Status',
		@applicationWidgetInstanceDesc='Social Networking Status Tool',
		@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
		@siteResourceID=@myStatusWidgetInstanceSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	
	update dbo.sn_socialNetworks
	set statusApplicationWidgetInstanceID = @applicationWidgetInstanceID
	where socialNetworkID = @socialNetworkID

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@myStatusWidgetInstanceSiteResourceID, @poolRoleTypeID=@SharedWidgetWallAddEntryPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error



	set @subAppPageName = @pageName + 'Welcome';
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@applicationTypeID=@SNWelcomeApplicationTypeID,
			@isVisible=@isVisible, @pageName=@subAppPageName, 
			@pageTitle='Social Network Welcome', @pageDesc='Welcome for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@applicationInstanceName='Social Network Welcome', @applicationInstanceDesc='Welcome for Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@subAppPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=@MasterInstanceUserGroupID, @memberID=null, @inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@subAppPageID,@socialNetworkID,'welcomePage',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@SharedAppWelcomePoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	set @subAppPageName = @pageName + 'Home';
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@applicationTypeID=@snHomeAppTypeID,
			@isVisible=@isVisible, @pageName=@subAppPageName, 
			@pageTitle='Social Network Home', @pageDesc='Homepage for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@applicationInstanceName='Social Network Home', @applicationInstanceDesc='Homepage for Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@snNetworkHomePageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@snNetworkHomePageID,@socialNetworkID,'home',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@HomePoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


/*	
	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snNetworkHomePageID, @zoneID=@zoneIDB, @siteResourceID=@myFriendsWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
*/


	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snNetworkHomePageID, @zoneID=@zoneIDB, @siteResourceID=@profilePicWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snNetworkHomePageID, @zoneID=@zoneID, @siteResourceID=@myStatusWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snNetworkHomePageID, @zoneID=@zoneID, @siteResourceID=@activityFeedWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	set @subAppPageName = @pageName + 'CommunityManager';
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@applicationTypeID=@communityManagerAppTypeID,
			@isVisible=@isVisible, @pageName=@subAppPageName, 
			@pageTitle='Communities', @pageDesc='Communities for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@applicationInstanceName='Communities', @applicationInstanceDesc='Communities for the Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@subAppPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@subAppPageID,@socialNetworkID,'communitymanager',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@SharedAppCommunityManagerPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = dbo.cms_createApplicationWidgetInstance
		@siteid=@siteID,
		@applicationInstanceID=@subAppApplicationInstanceID,
		@applicationWidgetTypeID=@recentCenterListMessagesWidgetTypeID ,
		@applicationWidgetInstanceName='Recent List Messages',
		@applicationWidgetInstanceDesc='Recent List Messages',
		@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
		@siteResourceID=@applicationWidgetInstanceSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	


	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snNetworkHomePageID, @zoneID=@zoneID, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	set @subAppPageName = @pageName + 'MemberManager';
	EXEC @rc = dbo.cms_createApplicationInstanceMemberManager @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@isVisible=@isVisible, @pageName=@subAppPageName, 
			@pageTitle='Social Network Member Manager', @pageDesc=' Member Manager for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,@subPageTemplateID = null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@applicationInstanceName='Social Network Member Manager', @applicationInstanceDesc=' Member Manager for Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@subAppPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@subAppPageID,@socialNetworkID,'membermanager',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@MemberManagerPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error




	EXEC @rc = dbo.cms_createApplicationWidgetInstance
		@siteid=@siteID,
		@applicationInstanceID=@subAppApplicationInstanceID,
		@applicationWidgetTypeID=@recentActiveMembersWidgetTypeID ,
		@applicationWidgetInstanceName='Recently Active Users',
		@applicationWidgetInstanceDesc='Recently Active Users',
		@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
		@siteResourceID=@applicationWidgetInstanceSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	


	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snNetworkHomePageID, @zoneID=@zoneID, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error



	set @subAppPageName = @pageName + 'ProfileEditor';
	EXEC @rc = dbo.cms_createApplicationInstanceProfileManager @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@isVisible=@isVisible, @pageName=@subAppPageName, 
			@pageTitle='Social Network Profile Manager', @pageDesc=' Profile Manager for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,@subPageTemplateID = null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@applicationInstanceName='Social Network Profile Manager', @applicationInstanceDesc=' Profile Manager for Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@subAppPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@subAppPageID,@socialNetworkID,'editProfile',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error


	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@ProfileEditorPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error



	set @subAppPageName = @pageName + 'ProfileViewer';
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@applicationTypeID=@snviewProfileAppTypeID,
			@isVisible=@isVisible, @pageName=@subAppPageName, 
			@pageTitle='Social Network Profile Viewer', @pageDesc='Profile Viewer for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@applicationInstanceName='Social Network Profile Viewer', @applicationInstanceDesc='Profile Viewer for Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@snViewProfilePageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@snViewProfilePageID,@socialNetworkID,'viewProfile',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snViewProfilePageID, @zoneID=@zoneIDB, @siteResourceID=@profilePicWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

/*
	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snViewProfilePageID, @zoneID=@zoneIDB, @siteResourceID=@myFriendsWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
*/

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@ProfileViewerPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error



	set @subAppPageName = @pageName + 'OrgProfileViewer';
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@applicationTypeID=@snviewOrgProfileAppTypeID,
			@isVisible=@isVisible, @pageName=@subAppPageName, 
			@pageTitle='Social Network Organization Profile Viewer', @pageDesc='Organization Profile Viewer for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@applicationInstanceName='Social Network Organization Profile Viewer', @applicationInstanceDesc='Organization Profile Viewer for Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@subAppPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@subAppPageID,@socialNetworkID,'viewOrgProfile',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@OrgProfileViewerPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error



	set @subAppPageName = @pageName + 'Photos';
	EXEC @rc = dbo.cms_createApplicationInstancePhotoGallery @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@isVisible=@isVisible,@pageName=@subAppPageName, 
			@pageTitle='Social Network Photos', @pageDesc='Photos for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@allowSubGalleries = 1,
			@applicationInstanceName='Social Network Photos', @applicationInstanceDesc='Photos for Social Network', @creatorMemberID=@creatorMemberID,
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@subAppPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@subAppPageID,@socialNetworkID,'photos',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@SharedAppPhotosPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	EXEC @rc = dbo.cms_createApplicationWidgetInstance
		@siteid=@siteID,
		@applicationInstanceID=@subAppApplicationInstanceID,
		@applicationWidgetTypeID=@recentPhotosWidgetTypeID,
		@applicationWidgetInstanceName='Social Network Recent Photos',
		@applicationWidgetInstanceDesc='Recent Photos',
		@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
		@siteResourceID=@applicationWidgetInstanceSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	
	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snNetworkHomePageID, @zoneID=@zoneIDB, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snViewProfilePageID, @zoneID=@zoneIDB, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	set @subAppPageName = @pageName + 'Videos';
	EXEC @rc = dbo.cms_createApplicationInstanceVideoGallery @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@isVisible=@isVisible,@pageName=@subAppPageName, 
			@pageTitle='Social Network Videos', @pageDesc='Videos for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@allowSubGalleries = 1, @creatorMemberID=@creatorMemberID,
			@applicationInstanceName='Social Network Videos', @applicationInstanceDesc='Videos for Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@subAppPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@subAppPageID,@socialNetworkID,'videos',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@SharedAppVideosPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = dbo.cms_createApplicationWidgetInstance
		@siteid=@siteID,
		@applicationInstanceID=@subAppApplicationInstanceID,
		@applicationWidgetTypeID=@recentVideosWidgetTypeID,
		@applicationWidgetInstanceName='Social Network Recent Videos',
		@applicationWidgetInstanceDesc='Recent Videos',
		@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
		@siteResourceID=@applicationWidgetInstanceSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	
	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snNetworkHomePageID, @zoneID=@zoneIDB, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snViewProfilePageID, @zoneID=@zoneIDB, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error



	set @subAppPageName = @pageName + 'Blog';
	EXEC @rc = dbo.cms_createApplicationInstanceBlog @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@isVisible=@isVisible, @pageName=@subAppPageName, 
			@pageTitle='Social Network Blog', @pageDesc='Blog for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,@subPageTemplateID = null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@applicationInstanceName='Social Network Blog', @applicationInstanceDesc='Blog for Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@subAppPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@AddBlogFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@editOwnFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@deleteOwnFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@canCommentFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error




	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@subAppPageID,@socialNetworkID,'blog',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error


	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@SharedAppBlogPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = dbo.cms_createApplicationWidgetInstance
		@siteid=@siteID,
		@applicationInstanceID=@subAppApplicationInstanceID,
		@applicationWidgetTypeID=@recentBlogsWidgetTypeID,
		@applicationWidgetInstanceName='Social Network Recent Blogs',
		@applicationWidgetInstanceDesc='Recent Blogs',
		@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
		@siteResourceID=@applicationWidgetInstanceSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	
	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snNetworkHomePageID, @zoneID=@zoneIDB, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snViewProfilePageID, @zoneID=@zoneIDB, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- CREATE USER GUIDE FOR THE SOCIAL NETWORK
	DECLARE @userGuidePageID int, @siteResourceStatusID int
	DECLARE @contentID int, @userGuideContentSiteResourceID int, @resourceTypeID int

	SELECT	@resourceTypeID = dbo.fn_getResourceTypeID('ApplicationSubPage') 
	SELECT	@siteResourceStatusID = dbo.fn_getResourceStatusID('Active') 

	EXEC  @rc = dbo.cms_createPage @siteid=@siteID, @languageID=@languageID, @resourceTypeID=@resourceTypeID
		, @siteResourceStatusID=@siteResourceStatusID
		, @pgParentResourceID=@siteResourceID
		, @isVisible=1, @sectionID=@rootSectionID, @ovTemplateID=null, @ovTemplateIDMobile=NULL, @ovModeID=null, @pageName='UserGuide'
		, @pageTitle='UserGuide'
		, @pageDesc='User Guide', @keywords='', @inheritPlacements=1, @allowReturnAfterLogin=1
		, @checkReservedNames=1, @pageID=@userGuidePageID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	INSERT INTO sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	VALUES (@userGuidePageID,@socialNetworkID,'userguide',1,null)
	IF @@ERROR <> 0 GOTO on_error

	SELECT	@resourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedContent')

	EXEC  @rc = cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @resourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = 1,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'UserGuide',
		@contentDesc = 'User Guide',
		@rawContent = '',
		@contentID = @contentID OUTPUT,
		@siteResourceID = @userGuideContentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	SELECT @zoneID = dbo.fn_getZoneID('Main')

	EXEC  @rc = cms_createPageZoneResource
		@pageID = @userGuidePageID,
		@zoneID = @zoneID,
		@siteResourceID = @userGuideContentSiteResourceID,
		@pzrID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	SELECT	@viewFunctionID = dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('ApplicationCreatedContent'))

	EXEC	@rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@userGuideContentSiteResourceID, @include=1
	, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID
	, @inheritedRightsFunctionID=@viewFunctionID, @resourceRightID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

END

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1


GO

ALTER PROCEDURE [dbo].[cms_createApplicationInstanceFileShare]
	@siteid int,
	@languageID int,
	@sectionID int,
	@isVisible bit,
	@pageName varchar(50),
	@pageTitle varchar(200),
	@pagedesc varchar(400),
	@zoneID int,
	@pageTemplateID int,
	@pageModeID int,
	@pgResourceTypeID int,
	@pgParentResourceID int = null,
	@allowReturnAfterLogin bit,
	@applicationInstanceName varchar(100),
	@applicationInstanceDesc varchar(200),
	@applicationInstanceID int OUTPUT,
	@siteResourceID int OUTPUT,
	@pageID int OUTPUT
AS

-- null OUTPUT vars
SELECT @applicationInstanceID = null, @siteResourceID = null, @pageID = null

DECLARE @appCreatedSectionResourceTypeID int, @applicationTypeID int, @rootSectionID int, @rc int
DECLARE @documentSectionName varchar(50)

select @appCreatedSectionResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedSection')
select @applicationTypeID = applicationTypeID from cms_applicationTypes where applicationTypeName = 'FileShare'
	
BEGIN TRAN

exec @rc = dbo.cms_createApplicationInstance
		@siteid = @siteid,
		@languageID = @languageID,
		@sectionID = @sectionID,
		@applicationTypeID = @applicationTypeID,
		@isVisible = @isVisible,
		@pageName = @pageName,
		@pageTitle = @pageTitle,
		@pagedesc = @pagedesc,
		@zoneID = @zoneID,
		@pageTemplateID = @pageTemplateID,
		@pageModeID = @pageModeID,
		@pgResourceTypeID = @pgResourceTypeID,
		@pgParentResourceID = @pgParentResourceID,
		@allowReturnAfterLogin = @allowReturnAfterLogin,
		@applicationInstanceName = @applicationInstanceName,
		@applicationInstanceDesc = @applicationInstanceDesc,
		@applicationInstanceID = @applicationInstanceID OUTPUT,
		@siteResourceID = @siteResourceID OUTPUT,
		@pageID = @pageID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

select @documentSectionName = 'FileShare ' + cast(@applicationInstanceID as varchar(8))

exec @rc = dbo.cms_createPageSection
		@siteID = @siteID, 
		@sectionResourceTypeID = @appCreatedSectionResourceTypeID, 
		@ovTemplateID = NULL,
		@ovTemplateIDMobile=NULL,
		@ovModeID = NULL, 
		@parentSectionID = @sectionID, 
		@sectionName = @documentSectionName, 
		@sectionCode = @documentSectionName,
		@inheritPlacements = 1,
		@sectionID = @rootSectionID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- update parentSiteResourceID of section
update sr
set sr.parentSiteResourceID = @siteResourceID
from cms_pageSections s
	inner join cms_siteResources sr on s.siteResourceID = sr.siteResourceID
		and s.sectionID = @rootSectionID

insert into dbo.fs_fileShare (applicationInstanceID, rootSectionID,showPublicationDate)
values (@applicationInstanceID,@rootSectionID,0)
	IF @@ERROR <> 0 GOTO on_error

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

ALTER PROCEDURE [dbo].[cms_createApplicationInstanceJobBank]
	@siteid int,
	@languageID int,
	@sectionID int,
	@isVisible bit,
	@pageName varchar(50),
	@pageTitle varchar(200),
	@pagedesc varchar(400),
	@zoneID int,
	@pageTemplateID int,
	@pageModeID int,
	@pgResourceTypeID int,
	@pgParentResourceID int = null,
	@allowReturnAfterLogin bit,
	@applicationInstanceName varchar(100),
	@applicationInstanceDesc varchar(200),
	@applicationInstanceID int OUTPUT, @siteResourceID int OUTPUT, @pageID int OUTPUT
AS

-- null OUTPUT vars
SELECT @applicationInstanceID = null, @siteResourceID = null, @pageID = null

DECLARE @appCreatedSectionResourceTypeID int, @applicationTypeID int, @rootSectionID int, @rc int, @documentSectionName varchar(50)

select @applicationTypeID = applicationTypeID from cms_applicationTypes where applicationTypeName = 'JobBank'
select @appCreatedSectionResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedSection')
select @documentSectionName = 'JobBank ' + @pageName

BEGIN TRAN

exec @rc = dbo.cms_createApplicationInstance
		@siteid = @siteid,
		@languageID = @languageID,
		@sectionID = @sectionID,
		@applicationTypeID = @applicationTypeID,
		@isVisible = @isVisible,
		@pageName = @pageName,
		@pageTitle = @pageTitle,
		@pagedesc = @pagedesc,
		@zoneID = @zoneID,
		@pageTemplateID = @pageTemplateID,
		@pageModeID = @pageModeID,
		@pgResourceTypeID = @pgResourceTypeID,
		@pgParentResourceID = @pgParentResourceID,
		@allowReturnAfterLogin = @allowReturnAfterLogin,
		@applicationInstanceName = @applicationInstanceName,
		@applicationInstanceDesc = @applicationInstanceDesc,
		@applicationInstanceID = @applicationInstanceID OUTPUT,
		@siteResourceID = @siteResourceID OUTPUT,
		@pageID = @pageID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


exec @rc = dbo.cms_createPageSection
		@siteID = @siteID, 
		@sectionResourceTypeID = @appCreatedSectionResourceTypeID, 
		@ovTemplateID = NULL,
		@ovTemplateIDMobile=NULL,
		@ovModeID = NULL, 
		@parentSectionID = @sectionID, 
		@sectionName = @documentSectionName, 
		@sectionCode = @documentSectionName,
		@inheritPlacements = 1,
		@sectionID = @rootSectionID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- update parentSiteResourceID of section
update sr
set sr.parentSiteResourceID = @siteResourceID
from cms_pageSections s
	inner join cms_siteResources sr on s.siteResourceID = sr.siteResourceID
		and s.sectionID = @rootSectionID

insert into dbo.jobBank (applicationInstanceID)
values (@applicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1

GO




--  
-- Script to Update dbo.cache_cms_derivedPageSectionSettings in DEV04\PLATFORM2008.memberCentral 
-- Generated Friday, June 7, 2013, at 12:34 PM 
--  
-- Please backup DEV04\PLATFORM2008.memberCentral before executing this script
--  
-- ** Script Begin **
BEGIN TRANSACTION
GO
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE
GO

PRINT 'Updating dbo.cache_cms_derivedPageSectionSettings Table'
GO

SET ANSI_NULLS, ANSI_PADDING, ANSI_WARNINGS, ARITHABORT, QUOTED_IDENTIFIER, CONCAT_NULL_YIELDS_NULL ON
GO

SET NUMERIC_ROUNDABORT OFF
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
BEGIN
   PRINT 'dbo.cache_cms_derivedPageSectionSettings Table Updated Successfully'
   COMMIT TRANSACTION
END ELSE
BEGIN
   PRINT 'Failed To Update dbo.cache_cms_derivedPageSectionSettings Table'
END
GO


--  
-- Script to Update dbo.cms_pageTemplateMenuUsageTypes in DEV04\PLATFORM2008.memberCentral 
-- Generated Friday, June 7, 2013, at 12:34 PM 
--  
-- Please backup DEV04\PLATFORM2008.memberCentral before executing this script
--  
-- ** Script Begin **
BEGIN TRANSACTION
GO
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE
GO

PRINT 'Updating dbo.cms_pageTemplateMenuUsageTypes Table'
GO

SET ANSI_NULLS, ANSI_PADDING, ANSI_WARNINGS, ARITHABORT, QUOTED_IDENTIFIER, CONCAT_NULL_YIELDS_NULL ON
GO

SET NUMERIC_ROUNDABORT OFF
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
BEGIN
   PRINT 'dbo.cms_pageTemplateMenuUsageTypes Table Updated Successfully'
   COMMIT TRANSACTION
END ELSE
BEGIN
   PRINT 'Failed To Update dbo.cms_pageTemplateMenuUsageTypes Table'
END
GO


--  
-- Script to Update dbo.cms_pageTemplatesModesZones in DEV04\PLATFORM2008.memberCentral 
-- Generated Friday, June 7, 2013, at 12:34 PM 
--  
-- Please backup DEV04\PLATFORM2008.memberCentral before executing this script
--  
-- ** Script Begin **
BEGIN TRANSACTION
GO
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE
GO

PRINT 'Updating dbo.cms_pageTemplatesModesZones Table'
GO

SET ANSI_NULLS, ANSI_PADDING, ANSI_WARNINGS, ARITHABORT, QUOTED_IDENTIFIER, CONCAT_NULL_YIELDS_NULL ON
GO

SET NUMERIC_ROUNDABORT OFF
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
BEGIN
   PRINT 'dbo.cms_pageTemplatesModesZones Table Updated Successfully'
   COMMIT TRANSACTION
END ELSE
BEGIN
   PRINT 'Failed To Update dbo.cms_pageTemplatesModesZones Table'
END
GO



--  
-- Script to Update dbo.cache_cms_derivedPageSectionSettings in DEV04\PLATFORM2008.memberCentral 
-- Generated Friday, June 7, 2013, at 12:34 PM 
--  
-- Please backup DEV04\PLATFORM2008.memberCentral before executing this script
--  
-- ** Script Begin **
BEGIN TRANSACTION
GO
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE
GO

PRINT 'Updating dbo.cache_cms_derivedPageSectionSettings Table'
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_cache_cms_derivedMenuUsages_cms_pageTemplates')
      ALTER TABLE [dbo].[cache_cms_derivedPageSectionSettings] DROP CONSTRAINT [FK_cache_cms_derivedMenuUsages_cms_pageTemplates]
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF NOT EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_cache_cms_derivedMenuUsages_cms_pageTemplates')
      ALTER TABLE [dbo].[cache_cms_derivedPageSectionSettings] ADD CONSTRAINT [FK_cache_cms_derivedMenuUsages_cms_pageTemplates] FOREIGN KEY ([ovTemplateID]) REFERENCES [dbo].[cms_pageTemplates] ([templateID])
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_cache_cms_derivedMenuUsages_cms_pageTemplates1')
      ALTER TABLE [dbo].[cache_cms_derivedPageSectionSettings] DROP CONSTRAINT [FK_cache_cms_derivedMenuUsages_cms_pageTemplates1]
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF NOT EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_cache_cms_derivedMenuUsages_cms_pageTemplates1')
      ALTER TABLE [dbo].[cache_cms_derivedPageSectionSettings] ADD CONSTRAINT [FK_cache_cms_derivedMenuUsages_cms_pageTemplates1] FOREIGN KEY ([ovTemplateIDMobile]) REFERENCES [dbo].[cms_pageTemplates] ([templateID])
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
BEGIN
   PRINT 'dbo.cache_cms_derivedPageSectionSettings Table Updated Successfully'
   COMMIT TRANSACTION
END ELSE
BEGIN
   PRINT 'Failed To Update dbo.cache_cms_derivedPageSectionSettings Table'
END
GO


--  
-- Script to Update dbo.cms_pageTemplateMenuUsageTypes in DEV04\PLATFORM2008.memberCentral 
-- Generated Friday, June 7, 2013, at 12:34 PM 
--  
-- Please backup DEV04\PLATFORM2008.memberCentral before executing this script
--  
-- ** Script Begin **
BEGIN TRANSACTION
GO
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE
GO

PRINT 'Updating dbo.cms_pageTemplateMenuUsageTypes Table'
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_cms_pageTemplateMenuUsageTypes_cms_pageTemplates')
      ALTER TABLE [dbo].[cms_pageTemplateMenuUsageTypes] DROP CONSTRAINT [FK_cms_pageTemplateMenuUsageTypes_cms_pageTemplates]
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF NOT EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_cms_pageTemplateMenuUsageTypes_cms_pageTemplates')
      ALTER TABLE [dbo].[cms_pageTemplateMenuUsageTypes] ADD CONSTRAINT [FK_cms_pageTemplateMenuUsageTypes_cms_pageTemplates] FOREIGN KEY ([templateID]) REFERENCES [dbo].[cms_pageTemplates] ([templateID])
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
BEGIN
   PRINT 'dbo.cms_pageTemplateMenuUsageTypes Table Updated Successfully'
   COMMIT TRANSACTION
END ELSE
BEGIN
   PRINT 'Failed To Update dbo.cms_pageTemplateMenuUsageTypes Table'
END
GO


--  
-- Script to Update dbo.cms_pageTemplatesModesZones in DEV04\PLATFORM2008.memberCentral 
-- Generated Friday, June 7, 2013, at 12:34 PM 
--  
-- Please backup DEV04\PLATFORM2008.memberCentral before executing this script
--  
-- ** Script Begin **
BEGIN TRANSACTION
GO
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE
GO

PRINT 'Updating dbo.cms_pageTemplatesModesZones Table'
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_pageTemplatesModesZones_pageTemplates')
      ALTER TABLE [dbo].[cms_pageTemplatesModesZones] DROP CONSTRAINT [FK_pageTemplatesModesZones_pageTemplates]
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF NOT EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_pageTemplatesModesZones_pageTemplates')
      ALTER TABLE [dbo].[cms_pageTemplatesModesZones] ADD CONSTRAINT [FK_pageTemplatesModesZones_pageTemplates] FOREIGN KEY ([templateID]) REFERENCES [dbo].[cms_pageTemplates] ([templateID])
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
BEGIN
   PRINT 'dbo.cms_pageTemplatesModesZones Table Updated Successfully'
   COMMIT TRANSACTION
END ELSE
BEGIN
   PRINT 'Failed To Update dbo.cms_pageTemplatesModesZones Table'
END
GO



--  
-- Script to Update dbo.cms_applicationInstances in DEV04\PLATFORM2008.memberCentral 
-- Generated Friday, June 7, 2013, at 12:48 PM 
--  
-- Please backup DEV04\PLATFORM2008.memberCentral before executing this script
--  
-- ** Script Begin **
BEGIN TRANSACTION
GO
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE
GO

PRINT 'Updating dbo.cms_applicationInstances Table'
GO

SET ANSI_NULLS, ANSI_PADDING, ANSI_WARNINGS, ARITHABORT, QUOTED_IDENTIFIER, CONCAT_NULL_YIELDS_NULL ON
GO

SET NUMERIC_ROUNDABORT OFF
GO


IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   CREATE INDEX [cms_applicationInstances_applicationInstanceID_applicationTypeID_applicationInstanceName] ON [dbo].[cms_applicationInstances] ([siteResourceID]) INCLUDE ([applicationInstanceID], [applicationTypeID], [applicationInstanceName])
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
BEGIN
   PRINT 'dbo.cms_applicationInstances Table Updated Successfully'
   COMMIT TRANSACTION
END ELSE
BEGIN
   PRINT 'Failed To Update dbo.cms_applicationInstances Table'
END
GO


--  
-- Script to Update dbo.cms_contentLanguages in DEV04\PLATFORM2008.memberCentral 
-- Generated Friday, June 7, 2013, at 12:48 PM 
--  
-- Please backup DEV04\PLATFORM2008.memberCentral before executing this script
--  
-- ** Script Begin **
BEGIN TRANSACTION
GO
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE
GO

PRINT 'Updating dbo.cms_contentLanguages Table'
GO

SET ANSI_NULLS, ANSI_PADDING, ANSI_WARNINGS, ARITHABORT, QUOTED_IDENTIFIER, CONCAT_NULL_YIELDS_NULL ON
GO

SET NUMERIC_ROUNDABORT OFF
GO


IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF EXISTS (SELECT name FROM sysindexes WHERE name = N'_dta_index_cms_contentLanguages_9_221594411__K2_K1_K3_4_8337')
      DROP INDEX [_dta_index_cms_contentLanguages_9_221594411__K2_K1_K3_4_8337] ON [dbo].[cms_contentLanguages]
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
BEGIN
   PRINT 'dbo.cms_contentLanguages Table Updated Successfully'
   COMMIT TRANSACTION
END ELSE
BEGIN
   PRINT 'Failed To Update dbo.cms_contentLanguages Table'
END
GO


--  
-- Script to Update dbo.cms_contentVersions in DEV04\PLATFORM2008.memberCentral 
-- Generated Friday, June 7, 2013, at 12:48 PM 
--  
-- Please backup DEV04\PLATFORM2008.memberCentral before executing this script
--  
-- ** Script Begin **
BEGIN TRANSACTION
GO
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE
GO

PRINT 'Updating dbo.cms_contentVersions Table'
GO

SET ANSI_NULLS, ANSI_PADDING, ANSI_WARNINGS, ARITHABORT, QUOTED_IDENTIFIER, CONCAT_NULL_YIELDS_NULL ON
GO

SET NUMERIC_ROUNDABORT OFF
GO


IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   CREATE INDEX [_dta_index_cms_contentVersions_6_348878843__K4_K2] ON [dbo].[cms_contentVersions] ([isActive], [contentLanguageID]) WITH (FILLFACTOR = 100)
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
BEGIN
   PRINT 'dbo.cms_contentVersions Table Updated Successfully'
   COMMIT TRANSACTION
END ELSE
BEGIN
   PRINT 'Failed To Update dbo.cms_contentVersions Table'
END
GO


--  
-- Script to Update dbo.cms_pageSections in DEV04\PLATFORM2008.memberCentral 
-- Generated Friday, June 7, 2013, at 12:48 PM 
--  
-- Please backup DEV04\PLATFORM2008.memberCentral before executing this script
--  
-- ** Script Begin **
BEGIN TRANSACTION
GO
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE
GO

PRINT 'Updating dbo.cms_pageSections Table'
GO

SET ANSI_NULLS, ANSI_PADDING, ANSI_WARNINGS, ARITHABORT, QUOTED_IDENTIFIER, CONCAT_NULL_YIELDS_NULL ON
GO

SET NUMERIC_ROUNDABORT OFF
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
BEGIN
   PRINT 'dbo.cms_pageSections Table Updated Successfully'
   COMMIT TRANSACTION
END ELSE
BEGIN
   PRINT 'Failed To Update dbo.cms_pageSections Table'
END
GO


--  
-- Script to Update dbo.cms_pages in DEV04\PLATFORM2008.memberCentral 
-- Generated Friday, June 7, 2013, at 12:48 PM 
--  
-- Please backup DEV04\PLATFORM2008.memberCentral before executing this script
--  
-- ** Script Begin **
BEGIN TRANSACTION
GO
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE
GO

PRINT 'Updating dbo.cms_pages Table'
GO

SET ANSI_NULLS, ANSI_PADDING, ANSI_WARNINGS, ARITHABORT, QUOTED_IDENTIFIER, CONCAT_NULL_YIELDS_NULL ON
GO

SET NUMERIC_ROUNDABORT OFF
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
BEGIN
   PRINT 'dbo.cms_pages Table Updated Successfully'
   COMMIT TRANSACTION
END ELSE
BEGIN
   PRINT 'Failed To Update dbo.cms_pages Table'
END
GO


--  
-- Script to Update dbo.cms_pageSections in DEV04\PLATFORM2008.memberCentral 
-- Generated Friday, June 7, 2013, at 12:48 PM 
--  
-- Please backup DEV04\PLATFORM2008.memberCentral before executing this script
--  
-- ** Script Begin **
BEGIN TRANSACTION
GO
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE
GO

PRINT 'Updating dbo.cms_pageSections Table'
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_cms_pageSections_cms_pageTemplates')
      ALTER TABLE [dbo].[cms_pageSections] DROP CONSTRAINT [FK_cms_pageSections_cms_pageTemplates]
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF NOT EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_cms_pageSections_cms_pageTemplates')
      ALTER TABLE [dbo].[cms_pageSections] ADD CONSTRAINT [FK_cms_pageSections_cms_pageTemplates] FOREIGN KEY ([ovTemplateIDMobile]) REFERENCES [dbo].[cms_pageTemplates] ([templateID])
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_pageSections_pageTemplates')
      ALTER TABLE [dbo].[cms_pageSections] DROP CONSTRAINT [FK_pageSections_pageTemplates]
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF NOT EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_pageSections_pageTemplates')
      ALTER TABLE [dbo].[cms_pageSections] ADD CONSTRAINT [FK_pageSections_pageTemplates] FOREIGN KEY ([ovTemplateID]) REFERENCES [dbo].[cms_pageTemplates] ([templateID])
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
BEGIN
   PRINT 'dbo.cms_pageSections Table Updated Successfully'
   COMMIT TRANSACTION
END ELSE
BEGIN
   PRINT 'Failed To Update dbo.cms_pageSections Table'
END
GO


--  
-- Script to Update dbo.cms_pages in DEV04\PLATFORM2008.memberCentral 
-- Generated Friday, June 7, 2013, at 12:48 PM 
--  
-- Please backup DEV04\PLATFORM2008.memberCentral before executing this script
--  
-- ** Script Begin **
BEGIN TRANSACTION
GO
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE
GO

PRINT 'Updating dbo.cms_pages Table'
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_cms_pages_cms_pageTemplates')
      ALTER TABLE [dbo].[cms_pages] DROP CONSTRAINT [FK_cms_pages_cms_pageTemplates]
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF NOT EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_cms_pages_cms_pageTemplates')
      ALTER TABLE [dbo].[cms_pages] ADD CONSTRAINT [FK_cms_pages_cms_pageTemplates] FOREIGN KEY ([ovTemplateIDMobile]) REFERENCES [dbo].[cms_pageTemplates] ([templateID])
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_pages_pageTemplates')
      ALTER TABLE [dbo].[cms_pages] DROP CONSTRAINT [FK_pages_pageTemplates]
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
   IF NOT EXISTS (SELECT name FROM sysobjects WHERE name = N'FK_pages_pageTemplates')
      ALTER TABLE [dbo].[cms_pages] ADD CONSTRAINT [FK_pages_pageTemplates] FOREIGN KEY ([ovTemplateID]) REFERENCES [dbo].[cms_pageTemplates] ([templateID])
GO

IF @@ERROR <> 0
   IF @@TRANCOUNT = 1 ROLLBACK TRANSACTION
GO

IF @@TRANCOUNT = 1
BEGIN
   PRINT 'dbo.cms_pages Table Updated Successfully'
   COMMIT TRANSACTION
END ELSE
BEGIN
   PRINT 'Failed To Update dbo.cms_pages Table'
END
GO

declare @resourceTypeID int, @resourceTypeName varchar(50), @functionID int, @functionName varchar(50), @functionDisplayName varchar(50), @addToSuperUserRole bit, @addToSiteAdminRole bit

DECLARE @rc int, @siteAdminRoleID int, @superAdminRoleID int, @ResourceTypeFunctionID int

set @addToSuperUserRole = 1
set @addToSiteAdminRole = 1
set @resourceTypeName = 'accountLocator'
set @functionName = 'View'

--- Do not change below this line

select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)
select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')
select @siteAdminRoleID = dbo.fn_getResourceRoleID('Site Administrator')

SELECT @functionID = dbo.fn_getResourceFunctionID(@functionName,@resourceTypeID)
select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);

IF (@addToSuperUserRole = 1)
	exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@superAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;
IF (@addToSiteAdminRole = 1)
	exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@siteAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;


GO
	
declare @resourceTypeID int, @resourceTypeName varchar(50), @functionID int, @functionName varchar(50), @functionDisplayName varchar(50), @addToSuperUserRole bit, @addToSiteAdminRole bit

DECLARE @rc int, @siteAdminRoleID int, @superAdminRoleID int, @ResourceTypeFunctionID int

set @addToSuperUserRole = 1
set @addToSiteAdminRole = 1
set @resourceTypeName = 'invoices'
set @functionName = 'View'

--- Do not change below this line

select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)
select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')
select @siteAdminRoleID = dbo.fn_getResourceRoleID('Site Administrator')

SELECT @functionID = dbo.fn_getResourceFunctionID(@functionName,@resourceTypeID)
select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);

IF (@addToSuperUserRole = 1)
	exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@superAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;
IF (@addToSiteAdminRole = 1)
	exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@siteAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;
GO
	
declare @resourceTypeID int, @resourceTypeName varchar(50), @functionID int, @functionName varchar(50), @functionDisplayName varchar(50), @addToSuperUserRole bit, @addToSiteAdminRole bit

DECLARE @rc int, @siteAdminRoleID int, @superAdminRoleID int, @ResourceTypeFunctionID int

set @addToSuperUserRole = 1
set @addToSiteAdminRole = 1
set @resourceTypeName = 'jobbank'
set @functionName = 'View'

--- Do not change below this line

select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)
select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')
select @siteAdminRoleID = dbo.fn_getResourceRoleID('Site Administrator')

SELECT @functionID = dbo.fn_getResourceFunctionID(@functionName,@resourceTypeID)
select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);

IF (@addToSuperUserRole = 1)
	exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@superAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;
IF (@addToSiteAdminRole = 1)
	exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@siteAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;
	
	GO