USE [memberCentral]
GO

CREATE PROC [dbo].[tr_manuallyProcessPayments]
@enteredByMemberID int,
@invoiceIDList varchar(max)

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY


	declare @jobUID uniqueidentifier, @isAutoCreated bit
	declare @jobStatusCreating int, @jobStatusQueuedPayment int

	select @jobStatusCreating = processStatusID 
	from customApps.dbo.schedTask_payInvoicesProcessStatuses
	where statusName = 'creating'

	select @jobStatusQueuedPayment = processStatusID 
	from customApps.dbo.schedTask_payInvoicesProcessStatuses
	where statusName = 'queuedPayment'

	select @jobUID = NewID()
	set @isAutoCreated = 0

	insert into customApps.dbo.schedTask_payInvoicesProcessJobs (jobID, dateCreated, isAutoCreated, processStatusID)
	VALUES (@jobUID,getDate(),@isAutoCreated,@jobStatusCreating)


	; WITH innerTbl as (
		select i.invoiceID, m2.memberID, o.orgID, s.siteID, mp.gatewayID, mp.profileID,
			o.useBatches, mpp.customerProfileID, mpp.paymentProfileID, mp.profileCode,
			s.siteName, i.dateCreated, i.dateBilled, i.dateDue, 
			o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber,
			isnull(m2.lastname,'') + ', ' + isnull(m2.firstname,'') + case when o.hasMiddleName = 1 then isnull(' ' + m2.middlename,'') else '' end + ' (' + m2.membernumber + ')' as memberName,
			mpp.detail, mp.profileName, o.accountingEmail
		from dbo.fn_intListToTable(@invoiceIDList,',') as tmp
		inner join dbo.tr_invoices as i on i.invoiceID = tmp.listitem
		inner join dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = i.payProfileID
		inner join dbo.ams_members as m on m.memberid = i.assignedToMemberID
		inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
		inner join dbo.organizations as o on o.orgID = m2.orgID
		inner join dbo.mp_profiles as mp on mp.profileID = mpp.profileID
		inner join dbo.sites as s on s.siteID = mp.siteID
		where i.statusID = 3
		and mpp.status = 'A'
		and mp.status = 'A'
		and mp.allowPayments = 1
	)

	insert into customApps.dbo.schedTask_autoPayInvoices (jobID, invoiceID, memberID, orgID,
		siteID, gatewayID, profileID, useBatches, customerProfileID, paymentProfileID,
		profileCode, siteName, chargeAmount, dateCreated, dateBilled, dateDue, invoiceNumber, 
		memberName, detail, profileName, accountingEmail, isAutoCreated)
	select @jobUID, tmp.invoiceID, tmp.memberID, tmp.orgID, tmp.siteID, tmp.gatewayID, tmp.profileID, 
		tmp.useBatches, tmp.customerProfileID, tmp.paymentProfileID, tmp.profileCode, tmp.siteName, 
		sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) as chargeAmount,
		tmp.dateCreated, tmp.dateBilled, tmp.dateDue, tmp.invoiceNumber, tmp.memberName, tmp.detail,
		tmp.profileName, tmp.accountingEmail, @isAutoCreated as isAutoCreated
	from innerTbl as tmp
	left outer join dbo.tr_invoiceTransactions as it on it.invoiceID = tmp.invoiceID
	where tmp.invoiceID not in (
		select invoiceID from customApps.dbo.schedTask_autoPayInvoices api where api.responsecode is  null
	)
	group by tmp.invoiceID, tmp.memberID, tmp.orgID, tmp.siteID, tmp.gatewayID, tmp.profileID, 
		tmp.useBatches, tmp.customerProfileID, tmp.paymentProfileID, tmp.profileCode, tmp.siteName, 
		tmp.dateCreated, tmp.dateBilled, tmp.dateDue, tmp.invoiceNumber, tmp.memberName, tmp.detail,
		tmp.profileName, tmp.accountingEmail
	having sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) > 0
	order by 1, tmp.customerProfileID, tmp.invoiceID, tmp.memberID


	update customApps.dbo.schedTask_payInvoicesProcessJobs set
		processStatusID = @jobStatusQueuedPayment
	where jobID = @jobUID


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

ALTER PROC [dbo].[tr_viewInvoice]
@invoiceID int

AS

set nocount on

-- invoice info
SELECT i.invoiceID, i.dateCreated, i.dateBilled, i.dateDue, istat.status, i.invoiceCode, 
	o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber,
	m2.memberid as assignedToMemberID,
	case when i.payProfileID is null then 0 else 1 end as hasCard, 
	isnull(m2.lastname,'') + ', ' + isnull(m2.firstname,'') + case when o.hasMiddleName = 1 then isnull(' ' + m2.middlename,'') else '' end + ' (' + m2.membernumber + ')' as memberName,
	m2.company as memberCompany,
	ip.profileID as invoiceProfileID, ip.profileName as invoiceProfile,
	btn_canEdit = case when istat.status = 'Paid' then 0 else 1 end,
	btn_canEmail = case when istat.status in ('Closed','Paid') then 1 else 0 end,
	btn_canPay = case when sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) > 0 then 1 else 0 end,
	inPaymentQueue = case when api.invoiceID is not null then cast(1 as bit) else cast(0 as bit) end,
	sum(it.cache_invoiceAmountAfterAdjustment) as InvAmt,
	sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) as InvDue,
	dbo.fn_tr_showInvoicePayOnlineLink(i.invoiceID) as showLink
from dbo.tr_invoices as i
inner join dbo.tr_invoiceStatuses as istat on istat.statusID = i.statusID
inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
inner join dbo.ams_members as m on m.memberid = i.assignedToMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
inner join dbo.organizations as o on o.orgID = m2.orgID
left outer join dbo.tr_invoiceTransactions as it 
	inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
	on it.invoiceID = i.invoiceID
left outer join customApps.dbo.schedTask_autoPayInvoices api
	on api.invoiceID = i.invoiceID
	and responseCode is null 

where i.invoiceID = @invoiceID
group by i.invoiceID, i.dateCreated, i.dateBilled, i.dateDue, istat.status, i.invoiceCode, o.orgcode, 
	i.invoiceNumber, m2.memberid, i.payProfileID, m2.lastname, m2.firstname, o.hasMiddleName, m2.middlename, 
	m2.membernumber, m2.company, ip.profileID, ip.profileName, api.invoiceID

-- invoice history
select ish.updateDate, istat.status, 
	isnull(m2.lastname,'') + ', ' + isnull(m2.firstname,'') + case when o.hasMiddleName = 1 then isnull(' ' + m2.middlename,'') else '' end + ' (' + m2.membernumber + ')' as memberName,
	m2.company as memberCompany
from dbo.tr_invoiceStatusHistory as ish
inner join dbo.tr_invoiceStatuses as istat on istat.statusID = ish.statusID
inner join dbo.ams_members as m on m.memberid = ish.enteredByMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
inner join dbo.organizations as o on o.orgID = m2.orgID
where ish.invoiceID = @invoiceID
order by ish.updateDate, ish.statusHistoryID

RETURN 0
GO

