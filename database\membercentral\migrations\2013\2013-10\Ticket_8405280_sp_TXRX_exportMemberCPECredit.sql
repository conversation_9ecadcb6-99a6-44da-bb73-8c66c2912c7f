USE [seminarWeb]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROC [dbo].[TXRX_exportMemberCPECredit]
@sponsorID int,
@memberID int,
@startdate datetime,
@enddate datetime,
@filename varchar(400)

AS
-- set startdate to 00:00:00 of startdate, 00:00:00 of enddate
SELECT @startdate = DATEADD(dd, DATEDIFF(dd,0,@startdate), 0)
SELECT @enddate = DATEADD(dd, DATEDIFF(dd,0,dateadd(dd,1,@enddate)), 0)


-- drop if exists
IF OBJECT_ID('tempdb..##tmpCPEOrgs') IS NOT NULL 
	DROP TABLE ##tmpCPEOrgs

-- Get the membercentral orgs for custom fields 
select distinct  m.orgid
into ##tmpCPEOrgs
FROM tblenrollments as e
	INNER JOIN tblParticipants p on e.participantID = p.participantID
	INNER JOIN tblSeminars as sem on sem.seminarID = e.seminarID
	INNER JOIN tblSeminarsSWOD as sswod on sswod.seminarID = sem.seminarID
	INNER JOIN tblenrollmentsandcredit as eac on eac.enrollmentid = e.enrollmentid
	INNER JOIN tblSeminarsAndCredit as sac on eac.seminarCreditID = sac.seminarCreditID
	INNER JOIN tblCreditSponsorsAndAuthorities as csa on csa.csalinkid = sac.csalinkid
	INNER JOIN tblCreditSponsors as cs on cs.sponsorid = csa.sponsorid and cs.sponsorID = @sponsorID
	INNER JOIN tblCreditAuthorities as ca on ca.authorityid = csa.authorityid
	INNER JOIN tblUsers as u on u.userid = e.userid
	INNER JOIN trialsmith.dbo.depomemberdata as d on d.depomemberdataid = u.depomemberdataid
	LEFT OUTER JOIN trialsmith.dbo.orgmemberdata as o on o.depomemberdataid = d.depomemberdataid 
		and o.orgcode = cs.orgcode
	INNER JOIN membercentral.membercentral.dbo.ams_networkProfiles np ON d.depomemberdataID = np.depomemberdataID
	INNER JOIN membercentral.membercentral.dbo.ams_memberNetworkProfiles mnp ON np.profileID = mnp.profileID
	INNER JOIN membercentral.membercentral.dbo.ams_members m ON m.memberID = mnp.memberID and m.memberID = @memberID
		AND m.status <> 'D'
WHERE e.passed = 1 and eac.earnedCertificate = 1
	AND e.datecompleted between @startdate and @enddate
AND e.isActive = 1
AND (d.adminflag2 is null or d.adminflag2 <> 'Y')

-- drop if exists
IF OBJECT_ID('tempdb..##tmpTXRXCPE') IS NOT NULL 
	DROP TABLE ##tmpTXRXCPE

-- put data into temp table 
;with rawdata as (SELECT TOP 100 PERCENT 
	m.memberNumber
	, p.orgcode as [Signed Up On]
	, CASE WHEN eac.idNumber IS NULL THEN replace(md2.nabpNumber, 'nabp', '')
		ELSE replace(eac.idNumber,'nabp','') END AS NABP_ePID
	, right('0' + rtrim(month(md.dateOfBirth)),2) + right('0' + rtrim(day(md.dateOfBirth)),2) AS DOB
	, sac.courseApproval AS ACPE_UAN
	, convert(varchar(10),e.datecompleted,101) AS Date_Of_Participation
	, CASE WHEN CSA.CSALinkID = 149 THEN 'P'
		WHEN CSA.CSALinkID = 204 THEN 'P'
		WHEN CSA.CSALinkID = 183 THEN 'P' 
		WHEN CSA.CSALinkID = 208 THEN 'T'
		WHEN CSA.CSALinkID = 206 THEN 'T'
		ELSE NULL
		END AS Participant_Type
   ,'On-Demand' as [Seminar Type]
FROM tblenrollments as e
	INNER JOIN tblParticipants p on e.participantID = p.participantID
	INNER JOIN tblSeminars as sem on sem.seminarID = e.seminarID
	INNER JOIN tblSeminarsSWOD as sswod on sswod.seminarID = sem.seminarID
	INNER JOIN tblenrollmentsandcredit as eac on eac.enrollmentid = e.enrollmentid
	INNER JOIN tblSeminarsAndCredit as sac on eac.seminarCreditID = sac.seminarCreditID
	INNER JOIN tblCreditSponsorsAndAuthorities as csa on csa.csalinkid = sac.csalinkid
	INNER JOIN tblCreditSponsors as cs on cs.sponsorid = csa.sponsorid and cs.sponsorID = @sponsorID
	INNER JOIN tblCreditAuthorities as ca on ca.authorityid = csa.authorityid
	INNER JOIN tblUsers as u on u.userid = e.userid
	INNER JOIN trialsmith.dbo.depomemberdata as d on d.depomemberdataid = u.depomemberdataid
	LEFT OUTER JOIN trialsmith.dbo.orgmemberdata as o on o.depomemberdataid = d.depomemberdataid 
		and o.orgcode = cs.orgcode
	INNER JOIN membercentral.membercentral.dbo.ams_networkProfiles np ON d.depomemberdataID = np.depomemberdataID
	INNER JOIN membercentral.membercentral.dbo.ams_memberNetworkProfiles mnp ON np.profileID = mnp.profileID
	INNER JOIN membercentral.membercentral.dbo.ams_members m ON m.memberID = mnp.memberID and m.memberID = @memberID
		AND m.status <> 'D'

	-- do this instead of joining against the view since we may have more than one org
	left outer join (
		select memberid, [dateofbirth]
		from (
			select vw.memberid, mdc.columnName, vw.columnValue
			from membercentral.membercentral.dbo.vw_memberData__date as vw WITH (NOEXPAND)
			inner join membercentral.membercentral.dbo.ams_memberdatacolumns as mdc on mdc.columnID = vw.columnID
			inner join ##tmpCPEOrgs orgs on mdc.orgid = orgs.orgid
		) as orgvw
		PIVOT (min(columnValue) FOR columnName in ([dateofbirth])) as pvt
	) as md on md.memberid = m.memberid
	left outer join (
		select memberid, [NABPNumber]
		from (
			select vw.memberid, mdc.columnName, vw.columnValue
			from membercentral.membercentral.dbo.vw_memberData__string as vw WITH (NOEXPAND)
			inner join membercentral.membercentral.dbo.ams_memberdatacolumns as mdc on mdc.columnID = vw.columnID
			inner join ##tmpCPEOrgs orgs on mdc.orgid = orgs.orgid
			where mdc.allowMultiple = 0 
		) as orgvw
		PIVOT (min(columnValue) FOR columnName in ([NABPNumber])) as pvt
	) as md2 on md2.memberid = m.memberid

WHERE e.passed = 1 and eac.earnedCertificate = 1
	AND e.datecompleted between @startdate and @enddate
AND e.isActive = 1
AND (d.adminflag2 is null or d.adminflag2 <> 'Y')

UNION 

SELECT TOP 100 PERCENT 
	m.memberNumber
	, p.orgcode as [Signed Up On]
	, CASE WHEN eac.idNumber IS NULL THEN replace(md2.nabpNumber, 'nabp', '')
		ELSE replace(eac.idNumber,'nabp','') END AS NABP_ePID
	, right('0' + rtrim(month(md.dateOfBirth)),2) + right('0' + rtrim(day(md.dateOfBirth)),2) AS DOB
	, sac.courseApproval AS ACPE_UAN
	, convert(varchar(10),e.datecompleted,101) AS Date_Of_Participation
	, CASE WHEN CSA.CSALinkID = 149 THEN 'P'
		WHEN CSA.CSALinkID = 204 THEN 'P'
		WHEN CSA.CSALinkID = 183 THEN 'P' 
		WHEN CSA.CSALinkID = 208 THEN 'T'
		WHEN CSA.CSALinkID = 206 THEN 'T'
		ELSE NULL
		END AS Participant_Type	
   ,'On-Demand' as [Seminar Type]

FROM tblenrollments as e
	INNER JOIN tblParticipants p on e.participantID = p.participantID
	INNER JOIN tblSeminars as sem on sem.seminarID = e.seminarID
	INNER JOIN tblSeminarsSWOD as sswod on sswod.seminarID = sem.seminarID
	INNER JOIN tblenrollmentsandcredit as eac on eac.enrollmentid = e.enrollmentid
	INNER JOIN tblSeminarsAndCredit as sac on eac.seminarCreditID = sac.seminarCreditID
	INNER JOIN tblCreditSponsorsAndAuthorities as csa on csa.csalinkid = sac.csalinkid
	INNER JOIN tblCreditSponsors as cs on cs.sponsorid = csa.sponsorid and cs.sponsorID = @sponsorID
	INNER JOIN tblCreditAuthorities as ca on ca.authorityid = csa.authorityid
	INNER JOIN tblUsers as u on u.userid = e.userid
	INNER JOIN trialsmith.dbo.depomemberdata as d on d.depomemberdataid = u.depomemberdataid
	LEFT OUTER JOIN trialsmith.dbo.orgmemberdata as o on o.depomemberdataid = d.depomemberdataid and o.orgcode = cs.orgcode
	INNER JOIN membercentral.membercentral.dbo.ams_members m ON d.MCmemberIDtemp = m.memberID and m.memberID = @memberID

	-- do this instead of joining against the view since we may have more than one org
	left outer join (
		select memberid, [dateofbirth]
		from (
			select vw.memberid, mdc.columnName, vw.columnValue
			from membercentral.membercentral.dbo.vw_memberData__date as vw WITH (NOEXPAND)
			inner join membercentral.membercentral.dbo.ams_memberdatacolumns as mdc on mdc.columnID = vw.columnID
			inner join ##tmpCPEOrgs orgs on mdc.orgid = orgs.orgid
		) as orgvw
		PIVOT (min(columnValue) FOR columnName in ([dateofbirth])) as pvt
	) as md on md.memberid = m.memberid
	left outer join (
		select memberid, [NABPNumber]
		from (
			select vw.memberid, mdc.columnName, vw.columnValue
			from membercentral.membercentral.dbo.vw_memberData__string as vw WITH (NOEXPAND)
			inner join membercentral.membercentral.dbo.ams_memberdatacolumns as mdc on mdc.columnID = vw.columnID
			inner join ##tmpCPEOrgs orgs on mdc.orgid = orgs.orgid
			where mdc.allowMultiple = 0 
		) as orgvw
		PIVOT (min(columnValue) FOR columnName in ([NABPNumber])) as pvt
	) as md2 on md2.memberid = m.memberid
WHERE e.passed = 1 and eac.earnedCertificate = 1
	AND e.datecompleted between @startdate and @enddate
AND e.isActive = 1
AND (d.adminflag2 is null or d.adminflag2 <> 'Y')

UNION 

SELECT	DISTINCT 
	m.memberNumber
	, o.orgcode as [Signed Up On]
	, replace(md.NABPNumber, 'nabp', '') AS NABP_ePID
	, right('0' + rtrim(month(md.dateOfBirth)),2) + right('0' + rtrim(day(md.dateOfBirth)),2) AS DOB
	,  ec.approvalNum AS ACPE_UAN
	,  convert(varchar(10),et.endTime,101) AS Date_Of_Participation
	, CASE WHEN ec.ASID = 5 THEN 'P' 
		WHEN ec.ASID = 38 THEN 'T'
		ELSE NULL
		END AS Participant_Type	
   ,'Live' as [Seminar Type]
	
FROM	 membercentral.membercentral.dbo.ev_events e	
	INNER JOIN  membercentral.membercentral.dbo.ev_registration r ON e.eventID = r.eventID 
		AND e.siteID = 11
	INNER JOIN  membercentral.membercentral.dbo.ev_times et ON et.eventID = e.eventID 
		AND et.timeZoneID = 6
	INNER JOIN  membercentral.membercentral.dbo.ev_registrants er ON r.registrationID = er.registrationID
	INNER JOIN  membercentral.membercentral.dbo.crd_requests erc ON erc.registrantID = er.registrantID
	INNER JOIN  membercentral.membercentral.dbo.crd_offeringTypes ect ON ect.offeringTypeID = erc.offeringTypeID 
	INNER JOIN  membercentral.membercentral.dbo.crd_offerings ec ON ec.offeringID = ect.offeringID 
	INNER JOIN  membercentral.membercentral.dbo.ams_members m ON m.memberID = er.memberID and m.memberID = @memberID
	INNER JOIN  membercentral.membercentral.dbo.organizations o ON o.orgID = m.orgID
	LEFT OUTER JOIN  membercentral.membercentral.dbo.vw_memberData_TXRX as md on md.memberID = m.memberID
	LEFT OUTER JOIN  membercentral.membercentral.dbo.cache_members_groups mg ON m.memberID = mg.memberID
	LEFT OUTER JOIN  membercentral.membercentral.dbo.ams_groups pg ON pg.groupID = mg.groupID 
		AND (pg.groupCode = 'Pharm' or pg.groupCode = 'Tech')
WHERE	et.endTime BETWEEN @startdate AND @enddate
)

SELECT DISTINCT
	dbo.fn_csvSafeString(memberNumber) as [Member Number]
	, [Signed Up On]
	, 'I' AS [Action]
	, dbo.fn_csvSafeString(NABP_ePID) as NABP_ePID
	, dbo.fn_csvSafeString(DOB) as DOB
	, dbo.fn_csvSafeString(ACPE_UAN) as ACPE_UAN
	, dbo.fn_csvSafeString(Date_Of_Participation) as Date_Of_Participation
	, Participant_Type as Participant_Type
	, '' AS Participant_Count
, [Seminar Type]
INTO ##tmpTXRXCPE
FROM rawdata
ORDER BY [Member Number], NABP_ePID, Date_Of_Participation



-- drop if exists
IF OBJECT_ID('tempdb..##tmpSWEvalResults') IS NOT NULL 
	DROP TABLE ##tmpSWEvalResults

select Min(safr.enrollmentID) AS enrollmentID, rd.questionID, rd.optionID, 
    case 
    when optionXID=33 then 1
    when optionXID=34 then 2
    when optionXID=35 then 3
    when optionXID=36 then 4
    when optionXID=37 then 5
    when optionXID=39 then 1
    when optionXID=40 then 2
    when optionXID=41 then 3
    when optionXID=42 then 4
    when optionXID=43 then 5
    else 0
    end as matrixVal,
    rd.responseText
INTO ##tmpSWEvalResults
from seminarweb.dbo.tblSeminarsAndFormResponses as safr
inner join seminarweb.dbo.tblenrollments as e on e.enrollmentid = safr.enrollmentid
    and e.passed = 1 
    and e.isactive = 1
    and e.datecompleted between @startdate and @enddate
inner join seminarweb.dbo.tblSeminarsSWOD as sswod on sswod.seminarID = e.seminarID
inner join seminarweb.dbo.tblenrollmentsandcredit as eac on eac.enrollmentid = e.enrollmentid
    and eac.earnedCertificate = 1
inner join seminarweb.dbo.tblSeminarsAndCredit as sac on sac.seminarCreditID = eac.seminarCreditID
inner join seminarweb.dbo.tblCreditSponsorsAndAuthorities as csa on csa.csalinkid = sac.csalinkid
    and csa.sponsorID = @sponsorID
inner join formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID
    and r.formID = 102
    and r.isActive = 1
inner join formbuilder.dbo.tblResponseDetails as rd on rd.responseID = safr.responseID
LEFT OUTER JOIN formbuilder.dbo.tblOptions o on rd.optionID = o.optionID
GROUP BY safr.enrollmentID, rd.questionID, rd.optionID, optionXID,
    rd.responseText, o.optionText

-- map to root questionid
-- The questions may have changed causing a new question id.
;WITH CTE 
AS	
(
		SELECT x.questionID, x.formerQuestionID, x.questionID as topID
		FROM formBuilder.dbo.tblQuestions x
		INNER JOIN ##tmpSWEvalResults t on t.questionID = x.questionID
	UNION ALL
		SELECT s.questionID, s.formerQuestionID, CTE.topID
		FROM formBuilder.dbo.tblQuestions as s
		JOIN CTE ON s.questionID = CTE.formerQuestionID
)
update t
	set t.questionID = r.questionID
from CTE r
inner join ##tmpSWEvalResults t on t.questionID = r.topID
where r.formerQuestionID is null and r.questionID <> r.topID

-- drop if exists
IF OBJECT_ID('tempdb..##tmpTXRXCredit') IS NOT NULL 
	DROP TABLE ##tmpTXRXCredit

-- credit report for anyone who picked and passed in credit
-- SWOD only
-- hardcoded to the generic semweb eval -- formid 102
select 
	d.depomemberdataid,
	sem.seminarID as [Seminar ID],
	dbo.fn_csvSafeString(sem.seminarname) as [Seminar Name], 
	dbo.fn_csvSafeString(sac.courseApproval) as [Course Approval Num], 
	dbo.fn_csvSafeString(eac.idnumber) as [ID Number],
	dbo.fn_csvSafeString(d.lastname) as [Last Name],
	dbo.fn_csvSafeString(d.firstname) as [First Name],
	dbo.fn_csvSafeString(isnull(o.address1,d.billingAddress)) as [Address Line 1],
	dbo.fn_csvSafeString(isnull(o.address2,d.billingAddress2)) as [Address Line 2],
	dbo.fn_csvSafeString(isnull(o.address3,d.billingAddress3)) as [Address Line 3],
	dbo.fn_csvSafeString(isnull(o.city,d.billingCity)) as City,
	dbo.fn_csvSafeString(isnull(o.state,d.billingState)) as State,
	dbo.fn_csvSafeString(isnull(o.zipcode,d.billingZIP)) as ZIP,
	dbo.fn_csvSafeString(m.memberNumber) as [Member Number],
	dbo.fn_csvSafeString(d.phone) as Phone,
	dbo.fn_csvSafeString(d.fax) as Fax,
	dbo.fn_csvSafeString(d.email) as [E-Mail],
	convert(varchar(10),e.dateenrolled,101) as [Date Enrolled],
	convert(varchar(10),e.datecompleted,101) as [Date Completed],
	eac.finaltimespent as [Minutes Spent in Course],
	dbo.fn_csvSafeString(q3.responseText) as [Birth Date],
	dbo.fn_csvSafeString(q4.responseText) as [Birth Month],
	dbo.fn_csvSafeString(q4.responseText+q3.responseText) as DOB
INTO ##tmpTXRXCredit
FROM tblenrollments as e
	INNER JOIN tblParticipants p on e.participantID = p.participantID
	INNER JOIN tblSeminars as sem on sem.seminarID = e.seminarID
	INNER JOIN tblSeminarsSWOD as sswod on sswod.seminarID = sem.seminarID
	INNER JOIN tblenrollmentsandcredit as eac on eac.enrollmentid = e.enrollmentid
	INNER JOIN tblSeminarsAndCredit as sac on eac.seminarCreditID = sac.seminarCreditID
	INNER JOIN tblCreditSponsorsAndAuthorities as csa on csa.csalinkid = sac.csalinkid
	INNER JOIN tblCreditSponsors as cs on cs.sponsorid = csa.sponsorid and cs.sponsorID = @sponsorID
	INNER JOIN tblCreditAuthorities as ca on ca.authorityid = csa.authorityid
	INNER JOIN tblUsers as u on u.userid = e.userid
	INNER JOIN trialsmith.dbo.depomemberdata as d on d.depomemberdataid = u.depomemberdataid
	INNER JOIN membercentral.membercentral.dbo.ams_networkProfiles np ON np.depomemberdataid = d.depomemberdataid
	INNER JOIN membercentral.membercentral.dbo.ams_memberNetworkProfiles mnp ON mnp.profileID = np.profileID and 
				mnp.siteID in (
					select siteID from membercentral.membercentral.dbo.sites where sitecode in (
					SELECT distinct [signed up on]
					FROM ##tmpTXRXCPE)
				)
	INNER JOIN membercentral.membercentral.dbo.ams_members m ON m.memberID = mnp.memberID and m.memberID = @memberID
	LEFT OUTER  JOIN trialsmith.dbo.orgmemberdata as o on o.depomemberdataid = d.depomemberdataid 
		AND o.orgcode = cs.orgcode
	LEFT OUTER JOIN ##tmpSWEvalResults as q3 on q3.enrollmentid = e.enrollmentid and q3.questionID=5028
	LEFT OUTER JOIN ##tmpSWEvalResults as q4 on q4.enrollmentid = e.enrollmentid and q4.questionID=5027
WHERE	e.passed = 1 and eac.earnedCertificate = 1
	AND e.datecompleted between @startdate and @enddate
ORDER BY	e.datecompleted

insert into ##tmpTXRXCredit (depomemberdataid, [Seminar ID], [Member Number], [First Name], [Last Name],[Seminar Name], [E-Mail], [Phone],[Date Completed],[ID Number],[Course Approval Num],[Birth Date],[Birth Month], DOB)
SELECT	DISTINCT 
	m.memberid
	, e.eventID
	, dbo.fn_csvSafeString(m.memberNumber)
	, dbo.fn_csvSafeString(m.firstName)
	, dbo.fn_csvSafeString(m.lastName)
	, dbo.fn_csvSafeString(isnull(cl.contentTitle,clDef.contentTitle))
	, dbo.fn_csvSafeString(md.Email)
	, dbo.fn_csvSafeString(md.[Address_Home Phone])
	, convert(varchar(10),et.endTime,101)
	, dbo.fn_csvSafeString(md.NABPNumber)
	, dbo.fn_csvSafeString(ec.approvalNum)
	, dbo.fn_csvSafeString(right('0' + rtrim(month(md.dateOfBirth)),2))
	, dbo.fn_csvSafeString(right('0' + rtrim(day(md.dateOfBirth)),2))
	, right('0' + rtrim(month(md.dateOfBirth)),2) + right('0' + rtrim(day(md.dateOfBirth)),2) AS DOB
FROM	 membercentral.membercentral.dbo.ev_events e	
	INNER JOIN  membercentral.membercentral.dbo.ev_registration r ON e.eventID = r.eventID 
		AND e.siteID = 11
	INNER JOIN  membercentral.membercentral.dbo.ev_times et ON et.eventID = e.eventID 
		AND et.timeZoneID = 6
	INNER JOIN  membercentral.membercentral.dbo.ev_registrants er ON r.registrationID = er.registrationID
	INNER JOIN  membercentral.membercentral.dbo.crd_requests erc ON erc.registrantID = er.registrantID
	INNER JOIN  membercentral.membercentral.dbo.crd_offeringTypes ect ON ect.offeringTypeID = erc.offeringTypeID 
	INNER JOIN  membercentral.membercentral.dbo.crd_offerings ec ON ec.offeringID = ect.offeringID 
	INNER JOIN  membercentral.membercentral.dbo.ams_members m ON m.memberID = er.memberID
	INNER JOIN  membercentral.membercentral.dbo.organizations o ON o.orgID = m.orgID

	INNER JOIN membercentral.membercentral.dbo.cms_content as c on c.contentid = e.eventcontentID
	INNER JOIN membercentral.membercentral.dbo.cms_siteResources sr	on sr.siteResourceID = c.siteResourceID 
	INNER JOIN membercentral.membercentral.dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
	LEFT OUTER JOIN membercentral.membercentral.dbo.cms_contentLanguages as cl 
		INNER JOIN membercentral.membercentral.dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID AND cv.isActive = 1
			ON cl.contentID = c.contentID AND cl.languageID = 1
	LEFT OUTER JOIN membercentral.membercentral.dbo.cms_contentLanguages as clDef 
	INNER JOIN membercentral.membercentral.dbo.cms_contentVersions as cvDef on cvDef.contentLanguageID = clDef.contentLanguageID AND cvDef.isActive = 1
		on clDef.contentID = c.contentID AND clDef.languageID = (SELECT s.defaultLanguageID FROM membercentral.membercentral.dbo.sites as s INNER JOIN membercentral.membercentral.dbo.cms_content as c2 on c2.siteID = s.siteID WHERE c2.contentID = c.contentID)

	LEFT OUTER JOIN  membercentral.membercentral.dbo.vw_memberData_TXRX as md on md.memberID = m.memberID and m.memberID = @memberID
	LEFT OUTER JOIN  membercentral.membercentral.dbo.cache_members_groups mg ON m.memberID = mg.memberID
	LEFT OUTER JOIN  membercentral.membercentral.dbo.ams_groups pg ON pg.groupID = mg.groupID 
		AND (pg.groupCode = 'Pharm' or pg.groupCode = 'Tech')
WHERE	et.endTime BETWEEN @startdate AND @enddate


-- drop temp table
IF OBJECT_ID('tempdb..##tmpTXRXCPETEMP') IS NOT NULL 
	DROP TABLE ##tmpTXRXCPETEMP

SELECT distinct
	t1.[Member Number] as [Member Number],
	[First Name],
	[Last Name],
	[Seminar Name],
	[Seminar Type],
	[Signed Up On],
	Phone,
	[E-Mail],
	[Action],	
	NABP_ePID
	, CASE WHEN len(replace(t2.DOB,'"', '')) > 0 THEN t2.DOB
		ELSE t1.DOB END AS DOB,
	ACPE_UAN,
	Date_Of_Participation,
	Participant_Type
INTO ##tmpTXRXCPETEMP	
FROM ##tmpTXRXCredit t1
	inner join ##tmpTXRXCPE t2 on 
		replace(t1.[Member Number],'"', '') = replace(t2.[Member Number],'"', '')
		and t2.ACPE_UAN = t1.[Course Approval Num]
WHERE
	len(Date_Of_Participation) > 0
	and( CONVERT (datetime, replace(Date_Of_Participation,'"', ''), 101) >= @startdate and CONVERT (datetime, replace(Date_Of_Participation,'"', ''), 101) < @enddate )	
ORDER BY [Member Number], NABP_ePID, Date_Of_Participation

-- export data
DECLARE @cmd varchar(6000)
declare @tmpBCP TABLE (theoutput varchar(max))
set @cmd = 'bcp ##tmpTXRXCPETEMP out ' + @filename + ' -c -t, -T -S' + CAST(serverproperty('servername') as varchar(20))
insert into @tmpBCP (theoutput)
exec master..xp_cmdshell @cmd	

-- return count of records
SELECT count(*) AS returnCount
FROM ##tmpTXRXCPETEMP

-- get fields returned
EXEC tempdb.dbo.SP_COLUMNS ##tmpTXRXCPETEMP

-- drop temp table
IF OBJECT_ID('tempdb..##tmpTXRXCPETEMP') IS NOT NULL 
	DROP TABLE ##tmpTXRXCPETEMP

-- drop temp table
IF OBJECT_ID('tempdb..##tmpTXRXCPE') IS NOT NULL 
	DROP TABLE ##tmpTXRXCPE

-- drop temp table
IF OBJECT_ID('tempdb..##tmpTXRXCredit') IS NOT NULL 
	DROP TABLE ##tmpTXRXCredit

-- drop if exists
IF OBJECT_ID('tempdb..##tmpCPEOrgs') IS NOT NULL 
	DROP TABLE ##tmpCPEOrgs

-- drop if exists
IF OBJECT_ID('tempdb..##tmpSWEvalResults') IS NOT NULL 
	DROP TABLE ##tmpSWEvalResults