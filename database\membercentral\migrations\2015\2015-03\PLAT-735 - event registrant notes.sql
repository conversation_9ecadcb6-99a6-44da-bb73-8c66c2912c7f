use membercentral
GO
ALTER TABLE dbo.ev_registrants ADD internalNotes varchar(max) NULL;
GO

ALTER PROC [dbo].[ev_uploadRegistrants]
@eventID int,
@markAttended bit,
@awardCredit bit,
@csvfilename varchar(200),
@strFileColumnNames varchar(max),
@strTableColumnNames varchar(max),
@pathToExport varchar(100),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

BEGIN TRY

	declare @sitecode varchar(10), @siteid int, @orgID int, @tmptbl varchar(17), @var_tmpCols varchar(20), @qry varchar(max),
		@createSQL varchar(max), @cmd varchar(400), @customID int, @customTypeID int, @titleOnInvoice varchar(max), @registrationID int
	declare @dynSQL nvarchar(max), @prefix varchar(50), @flatfile varchar(160), @exportcmd varchar(400)
	declare @tblCustFields TABLE (row int, customID int, titleOnInvoice varchar(max), customTypeID int)
	declare @tblMissingCols TABLE (colName varchar(255))
	declare @tblErrors TABLE (rowid int IDENTITY(1,1), msg varchar(300), fatal bit)
	declare @tblCounts TABLE (rowid int IDENTITY(1,1), countName varchar(50), countNum int)

	IF @awardCredit = 1 
		select @markAttended = 1

	-- get table name
	select @sitecode=s.sitecode, @siteID=s.siteID, @orgID=s.orgID
		from dbo.ev_events as e
		inner join dbo.sites as s on s.siteID = e.siteID
		where e.eventID = @eventID
	select @tmptbl = '##tmpEvents' + @sitecode
	select @var_tmpCols = '##tmpColsEvents' + @sitecode

	-- delete temp table if exists
	IF OBJECT_ID('tempdb..' + @tmptbl) IS NOT NULL 
		EXEC('DROP TABLE ' + @tmptbl)
	IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
		EXEC('DROP TABLE ' + @var_tmpCols)

	-- create temp table using columnlist
	select @qry = null
	select @qry = COALESCE(@qry + ', ', '') + quotename(listitem) + ' varchar(max) '
	FROM dbo.fn_varCharListToTable(@strFileColumnNames,char(7))
	order by autoid
		EXEC('CREATE TABLE ' + @tmptbl + ' (' + @qry + ')')

	select @qry = null
	select @qry = COALESCE(@qry + ', ', '') + quotename(listitem) + ' varchar(max) '
	FROM dbo.fn_varCharListToTable(@strTableColumnNames,char(7))
	order by autoid
		EXEC('CREATE TABLE ' + @var_tmpCols + ' (' + @qry + ')')

	-- rowID and membernumber is in a key column of an index and needs to not be varchar(max)
	declare @trash bit
	set @trash = 1
	EXEC('ALTER TABLE ' + @tmptbl + ' ALTER COLUMN rowID int') 
	BEGIN TRY
		EXEC('ALTER TABLE ' + @tmptbl + ' ALTER COLUMN membernumber varchar(800)') 
	END TRY
	BEGIN CATCH
		set @trash = 0
	END CATCH

	-- Execute a bulk insert into previously defined temporary table
	SELECT @qry = 'BULK INSERT ' + @tmptbl + ' FROM ''' + @csvfilename + ''' WITH (FIELDTERMINATOR = '''+ char(7) + ''', FIRSTROW = 2);'
	EXEC(@qry)
	
	IF OBJECT_ID('tempdb..#tblOrgCols') IS NOT NULL 
		DROP TABLE #tblOrgCols
	IF OBJECT_ID('tempdb..#tblImportCols') IS NOT NULL 
		DROP TABLE #tblImportCols

	-- **************************** 
	-- get custom fields for event
	-- **************************** 
	select @registrationID = registrationID from dbo.ev_registration where eventID = @eventID and status = 'A'
	insert into @tblCustFields (row, customID, titleOnInvoice, customTypeID)
	select ROW_NUMBER() OVER (order by rc.areaID, rc.fieldOrder) as row, rc.customID, rc.titleOnInvoice, rc.customTypeID
	from dbo.ev_registrationCustom as rc
	inner join dbo.ev_registration as r on rc.registrationID = r.registrationID
		and r.registrationID = @registrationID
		and rc.status <> 'D'
	where len(rc.titleOnInvoice) > 0

	-- ******************************** 
	-- ensure all columns exist (fatal)
	-- ******************************** 
	-- this will get the columns that should be there
	CREATE TABLE #tblOrgCols (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
		COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
		SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
		COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
		CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)

		-- get cols
		INSERT INTO #tblOrgCols
		EXEC tempdb.dbo.SP_COLUMNS @var_tmpCols
			
		-- cleanup table no longer needed
		IF OBJECT_ID('tempdb..' + @var_tmpCols) IS NOT NULL 
			EXEC('DROP TABLE ' + @var_tmpCols)

	-- this will get the columns that are actually in the import
	CREATE TABLE #tblImportCols (TABLE_QUALIFIER sysname, TABLE_OWNER sysname, TABLE_NAME sysname,
		COLUMN_NAME sysname, DATA_TYPE smallint, TYPE_NAME sysname, PRECISION int, LENGTH int,
		SCALE smallint, RADIX smallint, NULLABLE smallint, REMARKS varchar(254), 
		COLUMN_DEF nvarchar(4000), SQL_DATA_TYPE smallint, SQL_DATETIME_SUB smallint,
		CHAR_OCTET_LENGTH int, ORDINAL_POSITION int, IS_NULLABLE varchar(254), SS_DATA_TYPE tinyint)

		-- get cols
		INSERT INTO #tblImportCols
		EXEC tempdb.dbo.SP_COLUMNS @tmptbl

	INSERT INTO @tblErrors (msg, fatal)
	select 'The column ' + org.column_name + ' is missing from your data.', 1
	from #tblOrgCols as org
	left outer join #tblImportCols as imp on imp.column_name = org.column_name
	where imp.table_name is null

	insert into @tblMissingCols(colname)
	select org.column_name
	from #tblOrgCols as org
	left outer join #tblImportCols as imp on imp.column_name = org.column_name
	where imp.table_name is null

	-- ********************************
	-- no/bad member number (fatal)
	-- ********************************
	IF NOT EXISTS (select colName from @tblMissingCols where colName = 'memberNumber') BEGIN
		select @qry = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' is missing a Member Number. Member Numbers are required for all entries.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' 
			WHERE (memberNumber IS NULL OR ltrim(rtrim(memberNumber)) = '''')
			ORDER BY rowID'
		INSERT INTO @tblErrors (msg, fatal)
		EXEC(@qry)

		select @qry = 'SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' Member Number ('' + tb.membernumber + '') was not found. Member Numbers must match existing members.'' as msg, 1 as fatal 
			FROM ' + @tmptbl + ' as tb
			left outer join dbo.ams_members as m on m.memberNumber = tb.membernumber
				and m.orgID = ' + cast(@orgID as varchar(10)) + '
				and m.memberID = m.activeMemberID
				and m.status <> ''D''
			WHERE tb.membernumber IS NOT NULL 
			AND ltrim(rtrim(tb.membernumber)) <> ''''
			AND m.memberNumber is null
			ORDER BY rowID'
		INSERT INTO @tblErrors (msg, fatal)
		EXEC(@qry)
	END

	-- cleanup - these are no longer needed
	IF OBJECT_ID('tempdb..#tblOrgCols') IS NOT NULL
		DROP TABLE #tblOrgCols
	IF OBJECT_ID('tempdb..#tblImportCols') IS NOT NULL
		DROP TABLE #tblImportCols

	IF NOT EXISTS (select rowID from @tblErrors where fatal = 1) BEGIN
		-- ******************************** 
		-- dump flat table and format file and creation script
		-- would love to use xml format files, but it appears column names with spaces cause it to fail
		-- ******************************** 
		SELECT @prefix = @sitecode + '_EV_flat_' + convert(varchar(8),getdate(),112) + replace(convert(varchar(8),getdate(),108),':','')
		SELECT @flatfile = @pathToExport + @prefix

		select @exportcmd = 'bcp ' + @tmptbl + ' format nul -f ' + @flatfile + '.txt -n -T -S' + CAST(serverproperty('servername') as varchar(40))
		EXEC master..xp_cmdshell @exportcmd, NO_OUTPUT

		select @exportcmd = 'bcp ' + @tmptbl + ' out ' + @flatfile + '.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
		EXEC master..xp_cmdshell @exportcmd, NO_OUTPUT

		declare @createTableSQLTop varchar(100)          
		declare @coltypes table (datatype varchar(16))          
		insert into @coltypes values('bit')          
		insert into @coltypes values('binary')          
		insert into @coltypes values('bigint')          
		insert into @coltypes values('int')          
		insert into @coltypes values('float')          
		insert into @coltypes values('datetime')          
		insert into @coltypes values('text')          
		insert into @coltypes values('image')          
		insert into @coltypes values('money')          
		insert into @coltypes values('uniqueidentifier')          
		insert into @coltypes values('smalldatetime')          
		insert into @coltypes values('tinyint')          
		insert into @coltypes values('smallint')          
		insert into @coltypes values('sql_variant')          
		select @dynSQL = ''
		select @dynSQL = @dynSQL +           
			case when charindex('(',@dynSQL,1)<=0 then '(' else '' end + '[' + Column_Name + '] ' +Data_Type +
			case when Data_Type in (Select datatype from @coltypes) then '' else  '(' end+
			case when data_type in ('real','decimal','numeric')  then cast(isnull(numeric_precision,'') as varchar)+','+
			case when data_type in ('real','decimal','numeric') then cast(isnull(Numeric_Scale,'') as varchar) end
			when data_type in ('nvarchar','varchar') and cast(isnull(Character_Maximum_Length,'') as varchar) = '-1' then 'max'
			when data_type in ('char','nvarchar','varchar','nchar') then cast(isnull(Character_Maximum_Length,'') as varchar) else '' end+
			case when Data_Type in (Select datatype from @coltypes)then '' else  ')' end+
			case when Is_Nullable='No' then ' Not null,' else ' null,' end
			from tempdb.Information_Schema.COLUMNS where Table_Name=@tmptbl
			order by ordinal_position
		select @createTableSQLTop = 'Create table ##xxx ' 
		select @dynSQL=@createTableSQLTop + substring(@dynSQL,1,len(@dynSQL)-1) +' )'            
		if dbo.fn_WriteFile(@pathToExport + @prefix + '.sql', @dynSQL, 1) = 0 BEGIN
			RETURN 0
		END

		-- create index on temp table for numbers below
		EXEC('CREATE NONCLUSTERED INDEX [idx_' + @tmptbl + '_memnum_rowid] ON ' + @tmptbl + '([membernumber] ASC,[rowID] ASC)')

		-- ******************************** 
		-- Counts
		-- ******************************** 
		-- TotalEntriesImported
		select @dynSQL = 'SELECT ''TotalEntriesImported'', COUNT(rowID) FROM ' + @tmptbl
		INSERT INTO @tblCounts (countName, countNum)
		EXEC(@dynSQL)
	END

	-- ********************************
	-- generate result xml file 
	-- ********************************
	select @importResult = (
		select getdate() as "@date", @flatfile as "@flatfile",
			isnull((select top 301 dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg", "@severity" = case fatal when 1 then 'fatal' else 'nonfatal' end
			from @tblErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>'),

			isnull((select countName as "@name", countNum as "@num"
			from @tblCounts
			order by rowid
			FOR XML path('count'), root('counts'), type),'<counts/>')
		for xml path('import'), TYPE)

	-- drop temp tables 
	IF OBJECT_ID('tempdb..' + @tmptbl) IS NOT NULL
		EXEC('DROP TABLE ' + @tmptbl)

END TRY
BEGIN CATCH
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

-- ********************************
-- go ahead and do actual import if no fatal errors 
-- ********************************
IF @importResult.value('count(/import/errors/error[@severity="fatal"])','int') > 0
	RETURN -1

BEGIN TRY
	-- **************
	-- read in sql create script and create global temp table
	-- **************
	select @createSQL = replace(dbo.fn_ReadFile(@flatfile + '.sql',0,1),'##xxx','##importEventsData')
	IF OBJECT_ID('tempdb..##importEventsData') IS NOT NULL
		EXEC('DROP TABLE ##importEventsData')
	EXEC(@createSQL)

	-- *******************
	-- bcp in data
	-- *******************
	select @cmd = 'bcp ##importEventsData in ' + @flatfile + '.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
	exec master..xp_cmdshell @cmd, NO_OUTPUT

	-- *******************
	-- immediately put into local temp table and drop global temp
	-- *******************
	select cast(null as int) as MCMemberID, cast(null as int) as MCRegistrantID, * into #importEventsData from ##importEventsData
	IF OBJECT_ID('tempdb..##importEventsData') IS NOT NULL
		EXEC('DROP TABLE ##importEventsData')

	-- Add index for queries below
	CREATE NONCLUSTERED INDEX [idx_importEventsData] ON #importEventsData ([membernumber] ASC);

	/* ****************************** */
	/* set MCMemberID by membernumber */
	/* ****************************** */
	update tmp
	set tmp.MCMemberID = m.memberid
	from #importEventsData as tmp
	inner join dbo.ams_members as m on m.memberNumber = tmp.memberNumber
		and m.orgID = @orgID
		and m.memberID = m.activeMemberID
		and m.status <> 'D'

	/* ******************************************* */
	/* set MCRegistrantID for existing registrants */
	/* ******************************************* */
	update tmp
	set tmp.MCRegistrantID = r.registrantID
	from #importEventsData as tmp
	inner join dbo.ev_registrants as r on r.registrationID = @registrationID
		and r.status = 'A'
	inner join dbo.ams_members as m on m.memberid = r.memberID
		and m.activeMemberID = tmp.MCMemberID

	/* *********************** */
	/* Add Missing Registrants */
	/* *********************** */
	insert into dbo.ev_registrants (registrationID, memberID, recordedOnSiteID, dateRegistered, [status], attended, internalNotes)
	select distinct @registrationID, mem.MCMemberID, @siteID, getdate(), 'A', @markattended, mem.internalNotes
	from #importEventsData as mem
	where MCRegistrantID is null

	/* *********************************** */
	/* CUST FIELDS for Missing Registrants */
	/* *********************************** */
	select @customID = min(customID) from @tblCustFields
	while @customID is not null BEGIN
		select @customTypeID = null, @titleOnInvoice = null, @qry = null
		select @customTypeID = customTypeID, @titleOnInvoice = titleOnInvoice
			from @tblCustFields 
			where customID = @customID

		IF @customTypeID = 1 BEGIN
			select @qry = 'select distinct r.registrantID, ' + cast(@customID as varchar(10)) + ', null, tmp.[' + @titleOnInvoice + '], null, ''A''
				from dbo.ev_registrants as r
				inner join #importEventsData as tmp on tmp.MCMemberID = r.memberID
					and r.registrationID = ' + cast(@registrationID as varchar(10)) + '
				where tmp.MCRegistrantID is null
				and len(tmp.[' + @titleOnInvoice + ']) > 0 '
			INSERT INTO dbo.ev_registrantDetails (registrantID, customID, customOptionID, customText, customMemberID, status)
			EXEC(@qry)
		END
		IF @customTypeID IN (2,3) BEGIN
			select @qry = 'select distinct r.registrantID, ' + cast(@customID as varchar(10)) + ', co.customOptionID, null, null, ''A''
				from dbo.ev_registrants as r
				inner join #importEventsData as tmp on tmp.MCMemberID = r.memberID
					and r.registrationID = ' + cast(@registrationID as varchar(10)) + '
				inner join dbo.ev_customOptions as co on co.optionDesc = tmp.[' + @titleOnInvoice + ']
					and co.customID = ' + cast(@customID as varchar(10)) + '
				where tmp.MCRegistrantID is null '
			INSERT INTO dbo.ev_registrantDetails (registrantID, customID, customOptionID, customText, customMemberID, status)
			EXEC(@qry)
		END
		IF @customTypeID = 4 BEGIN
			select @qry = 'select distinct r.registrantID, ' + cast(@customID as varchar(10)) + ', co.customOptionID, null, null, ''A''
				from dbo.ev_registrants as r
				inner join #importEventsData as tmp on tmp.MCMemberID = r.memberID
					and r.registrationID = ' + cast(@registrationID as varchar(10)) + '
				cross apply dbo.fn_varcharListToTable(tmp.[' + @titleOnInvoice + '],''|'') as items
				inner join dbo.ev_customOptions as co on co.optionDesc = items.listitem
					and co.customID = ' + cast(@customID as varchar(10)) + '
				where tmp.MCRegistrantID is null '
			INSERT INTO dbo.ev_registrantDetails (registrantID, customID, customOptionID, customText, customMemberID, status)
			EXEC(@qry)
		END
		IF @customTypeID = 6 BEGIN
			select @qry = 'select distinct r.registrantID, ' + cast(@customID as varchar(10)) + ', null, null, m.memberid, ''A''
				from dbo.ev_registrants as r
				inner join #importEventsData as tmp on tmp.MCMemberID = r.memberID
					and r.registrationID = ' + cast(@registrationID as varchar(10)) + '
				cross apply dbo.fn_varcharListToTable(tmp.[' + @titleOnInvoice + '],''|'') as items
				inner join dbo.ams_members as m on m.membernumber = items.listitem
					and m.orgID = ' + cast(@orgID as varchar(10)) + '
					and m.memberid = m.activeMemberID
				where tmp.MCRegistrantID is null '
			INSERT INTO dbo.ev_registrantDetails (registrantID, customID, customOptionID, customText, customMemberID, status)
			EXEC(@qry)
		END

		select @customID = min(customID) from @tblCustFields where customID > @customID
	END

	/* ************************************** */
	/* ATTENDANCE for All Registrants in File */
	/* ************************************** */
	IF @markattended = 1 BEGIN	
		update evr
		set evr.attended = 1
		from dbo.ev_registrants as evr
		inner join #importEventsData as tmp on tmp.MCMemberID = evr.memberID
		where evr.registrationID = @registrationID
		and evr.status = 'A'
	END

	/* **************************************** */
	/* AWARD CREDIT for All Registrants in File */
	/* **************************************** */
	IF @awardCredit = 1 BEGIN	
		declare @lastDateToComplete datetime
		select @lastDateToComplete=max(endTime) from dbo.ev_times where eventID=@eventID
		SELECT @lastDateToComplete = DATEADD(ss, 86340, DATEADD(dd, DATEDIFF(dd,0,@lastDateToComplete), 0))

		INSERT INTO dbo.crd_requests (offeringTypeID, IDNumber, lastDateToComplete, creditAwarded, creditValueAwarded, addedViaAward, registrantID)
		select ect.offeringTypeID, '', @lastDateToComplete, 1, ect.creditValue, 1, evr.registrantID
		from dbo.crd_offerings AS ec
		inner join dbo.crd_offeringTypes as ect on ect.offeringID = ec.offeringID
		inner join dbo.crd_authoritySponsorTypes as ast on ast.ASTID = ect.ASTID
		inner join dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
		inner join #importEventsData as tmp on tmp.MCMemberID = tmp.MCMemberID
		inner join dbo.ev_registrants as evr on evr.memberID = tmp.MCMemberID
			and evr.registrationID = @registrationID
			and evr.status = 'A'
		where ec.eventID = @eventID
		and ec.statusID = 4
		and not exists (
			select requestID 
			from dbo.crd_requests
			where offeringTypeID = ect.offeringTypeID
			and registrantID = evr.registrantID
		)
	END

	IF OBJECT_ID('tempdb..#importEventsData') IS NOT NULL
		EXEC('DROP TABLE #importEventsData')

	RETURN 0
END TRY
BEGIN CATCH
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO


