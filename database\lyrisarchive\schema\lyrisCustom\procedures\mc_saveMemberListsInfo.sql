ALTER PROC dbo.mc_saveMemberListsInfo
@siteID int,
@receiverMemberID int,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @mainMessage varchar(50);

	-- ensure temp table exists
	IF OBJECT_ID('tempdb..#mc_listMembersUpdate') IS NULL BEGIN
		RAISERROR('Unable to locate the temp table for processing.',16,1);
	END

	IF OBJECT_ID('tempdb..#tblExistingMember') IS NOT NULL
		DROP TABLE #tblExistingMember;
	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;

	CREATE TABLE #tblExistingMember([MemberID_] [int],
		[SubType_] [varchar](20) collate SQL_Latin1_General_CP1_CI_AS,
		[EmailAddr_] [varchar](100) collate SQL_Latin1_General_CP1_CI_AS,
		[FullName_] [varchar](100) collate SQL_Latin1_General_CP1_CI_AS,
		[ExpireDate_] [smalldatetime],
		[MemberType_] [varchar](20) collate SQL_Latin1_General_CP1_CI_AS,
		[MCOption_lockAddress] [bit],
		[MCOption_lockName] [bit],
		[MCOption_keepActive] [bit],
		[receiveMCThreadIndex] [bit],
		[receiveMCThreadDigest] [bit]);
	CREATE TABLE #tmpLogMessages (rowID INT IDENTITY(1,1), msg VARCHAR(MAX), listName varchar(60), email varchar(100));

	INSERT INTO #tblExistingMember([MemberID_], [SubType_], [EmailAddr_], [FullName_] , [ExpireDate_], [MemberType_], [MCOption_lockAddress], [MCOption_lockName],
		[MCOption_keepActive], [receiveMCThreadIndex], [receiveMCThreadDigest])
	SELECT lm.[MemberID_], lm.[SubType_], lm.[EmailAddr_], lm.[FullName_] , lm.[ExpireDate_], lm.[MemberType_], lm.[MCOption_lockAddress], lm.[MCOption_lockName],
		lm.[MCOption_keepActive], lm.[receiveMCThreadIndex], lm.[receiveMCThreadDigest]
	FROM trialslyris1.dbo.members_ as lm
	INNER JOIN #mc_listMembersUpdate as tmp on tmp.listMemberID = lm.memberid_
	WHERE tmp.matchingListMemberID IS NULL
	AND tmp.skipUpdate = 0;
	
	UPDATE lm
	SET lm.SubType_ = tmp.subType,
		lm.EmailAddr_ = tmp.email,
		lm.FullName_ = tmp.fullname,
		lm.ExpireDate_ = CASE WHEN tmp.memberType <> 'expired' THEN NULL ELSE lm.ExpireDate_ END,
		lm.MemberType_ = tmp.memberType,
		lm.Domain_ = tmp.domain,
		lm.UserNameLC_ = tmp.username,
		lm.MCOption_lockAddress = tmp.MCOption_lockAddress,
		lm.MCOption_lockName = tmp.MCOption_lockName,
		lm.MCOption_keepActive = tmp.MCOption_keepActive,
		lm.receiveMCThreadIndex = tmp.receiveMCThreadIndex,
		lm.receiveMCThreadDigest = tmp.receiveMCThreadDigest,
		lm.MCEmailKey = convert(varchar(75),HASHBYTES('SHA2_256',lm.list_ + '|' + tmp.username collate SQL_Latin1_General_CP1_CI_AS + '@' + tmp.domain collate SQL_Latin1_General_CP1_CI_AS),2),
		lm.MCEmailKey_usernameLC = tmp.username,
		lm.MCEmailKey_domain = tmp.domain
	FROM trialslyris1.dbo.members_ as lm
	INNER JOIN #mc_listMembersUpdate as tmp on tmp.listMemberID = lm.memberid_
	WHERE tmp.matchingListMemberID IS NULL
	AND tmp.skipUpdate = 0;

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'Delivery Settings changed from [' + 
		CASE old.SubType_
			WHEN 'mail' THEN 'Send All Messages Immediately' 
			WHEN 'digest'  THEN 'Legacy Digest: one daily message with contributions for that day'
			WHEN 'mimedigest'  THEN 'Legacy Digest: one daily message with each contribution as an attachment'
			WHEN 'index'  THEN 'Legacy Index: one daily message with only subject lines'
			WHEN 'receiveMCThreadIndex'  THEN 'Send Single Daily Index of Topics'
			WHEN 'receiveMCThreadDigest'  THEN 'Send Single Daily Summary of All Messages'
			WHEN 'nomail'  THEN 'Suspend Mail - Vacation Mode'
		END + '] to [' + 
		CASE m.SubType_
			WHEN 'mail' THEN 'Send All Messages Immediately' 
			WHEN 'digest'  THEN 'Legacy Digest: one daily message with contributions for that day'
			WHEN 'mimedigest'  THEN 'Legacy Digest: one daily message with each contribution as an attachment'
			WHEN 'index'  THEN 'Legacy Index: one daily message with only subject lines'
			WHEN 'receiveMCThreadIndex'  THEN 'Send Single Daily Index of Topics'
			WHEN 'receiveMCThreadDigest'  THEN 'Send Single Daily Summary of All Messages'
			WHEN 'nomail'  THEN 'Suspend Mail - Vacation Mode'
		END
		+ '] for [' + old.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND old.SubType_ <> m.SubType_;

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'E-mail changed from [' + old.EmailAddr_ + '] to [' + m.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND old.EmailAddr_ <> m.EmailAddr_;

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'Name changed from [' + ISNULL(NULLIF(old.FullName_,''),'blank') + '] to [' + ISNULL(NULLIF(m.FullName_,''),'blank') + '] for [' + old.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND ISNULL(old.FullName_,'') <> ISNULL(m.FullName_,'');

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'Expired Date changed from [' + ISNULL(CONVERT(VARCHAR(20),old.ExpireDate_,120),'blank') + '] to [' + ISNULL(CONVERT(VARCHAR(20),m.ExpireDate_,120),'blank') + '] for [' + old.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND ISNULL(old.ExpireDate_,'') <> ISNULL(m.ExpireDate_,'');

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'Member List Status changed from [' + old.MemberType_ + '] to [' + m.MemberType_ + '] for [' + old.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND old.MemberType_ <> m.MemberType_;

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'Lock Address changed from [' + CASE WHEN old.MCOption_lockAddress = 1 THEN 'Yes' ELSE 'No' END + '] to ['+ CASE WHEN m.MCOption_lockAddress = 1 THEN 'Yes' ELSE 'No' END +'] for [' + old.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND ISNULL(old.MCOption_lockAddress, 0) <> ISNULL(m.MCOption_lockAddress, 0);

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'Lock Name changed from [' + CASE WHEN old.MCOption_lockName = 1 THEN 'Yes' ELSE 'No' END + '] to ['+ CASE WHEN m.MCOption_lockName = 1 THEN 'Yes' ELSE 'No' END +'] for [' + old.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND ISNULL(old.MCOption_lockName, 0) <> ISNULL(m.MCOption_lockName, 0);

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'Keep Active changed from [' + CASE WHEN old.MCOption_keepActive = 1 THEN 'Yes' ELSE 'No' END + '] to ['+ CASE WHEN m.MCOption_keepActive = 1 THEN 'Yes' ELSE 'No' END +'] for [' + old.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND ISNULL(old.MCOption_keepActive, 0) <> ISNULL(m.MCOption_keepActive, 0);

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'Send Single Daily Index of Topics setting changed from [' + CASE WHEN old.receiveMCThreadIndex = 1 THEN 'Yes' ELSE 'No' END + '] to ['+ CASE WHEN m.receiveMCThreadIndex = 1 THEN 'Yes' ELSE 'No' END +'] for [' + old.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND ISNULL(old.receiveMCThreadIndex, 0) <> ISNULL(m.receiveMCThreadIndex, 0);

	INSERT INTO #tmpLogMessages(listName, email, msg)
	SELECT m.List_, old.EmailAddr_, 'Send Single Daily Summary of All Messages setting changed from [' + CASE WHEN old.receiveMCThreadDigest = 1 THEN 'Yes' ELSE 'No' END + '] to ['+ CASE WHEN m.receiveMCThreadDigest = 1 THEN 'Yes' ELSE 'No' END +'] for [' + old.EmailAddr_ + '].'
	FROM #tblExistingMember AS old
	INNER JOIN trialslyris1.dbo.members_ as m on m.MemberID_ = old.MemberID_
		AND ISNULL(old.receiveMCThreadDigest, 0) <> ISNULL(m.receiveMCThreadDigest, 0);

	IF EXISTS(SELECT 1 FROM #tmpLogMessages) BEGIN
		UPDATE #tmpLogMessages
		SET msg = lyrisarchive.dbo.fn_cleanInvalidXMLChars(REPLACE(msg,'"','\"')),
			listName = lyrisarchive.dbo.fn_cleanInvalidXMLChars(REPLACE(listName,'"','\"'));

		INSERT INTO memberCentral.platformQueue.dbo.queue_mongo (msgjson)
		SELECT '{ "c":"historyEntries_SYS_ADMIN_LISTUPDATE", "d": { "HISTORYCODE":"SYS_ADMIN_LISTUPDATE", "SITEID":' + cast(@siteID as varchar(10)) + 
			', "ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + 
			', "RECEIVERMEMBERID":' + cast(@receiverMemberID as varchar(10)) + 
			', "MAINMESSAGE":"List Membership Updated"' +
			', "LISTNAME":"'+ tmp.listName +'", "MESSAGES":[ ' +
			STUFF((SELECT ', "' + msg + '"'
				FROM #tmpLogMessages
				WHERE listName = tmp.listName
				ORDER BY email, msg
				FOR XML PATH(''), TYPE).value('.','varchar(max)')
			,1,1,'') + ' ]'+
			', "UPDATEDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '"'+
			' } }'
		FROM (
			SELECT DISTINCT listName
			FROM #tmpLogMessages
		) AS tmp;
	END

	IF OBJECT_ID('tempdb..#tblExistingMember') IS NOT NULL
		DROP TABLE #tblExistingMember;
	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
