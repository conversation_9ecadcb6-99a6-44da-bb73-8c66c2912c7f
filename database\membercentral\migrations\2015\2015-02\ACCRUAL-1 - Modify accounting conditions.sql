use membercentral
GO

ALTER PROC [dbo].[tr_autoPostSystemBatches]
AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- identify which batches need to be posted
	declare @tblBatches TABLE (batchID int, depositDate datetime, controlCount int, controlAmt money, actualCount int, actualAmt money)
	insert into @tblBatches (batchID, depositDate, controlCount, controlAmt, actualCount, actualAmt)
	select b.batchID, b.depositDate, b.controlCount, b.controlAmt, isnull(actual.actualCount,0), isnull(actual.actualAmount,0)
	from dbo.tr_batches as b
	outer apply dbo.fn_tr_getBatchActual(b.batchID) as actual
	where b.isSystemCreated = 1
	and b.statusID <> 4
	and isnull(b.batchCode,'') <> 'PENDINGPAYMENTS'
	and b.depositDate < DATEADD(dd, DATEDIFF(dd,0,getdate()), 0)

	-- post batches
	update b
	set b.statusID = 4
	from dbo.tr_batches as b
	inner join @tblBatches as tbl on tbl.batchID = b.batchID

	-- update counts if necessary
	update b
	set b.controlCount = tbl.actualCount
	from dbo.tr_batches as b
	inner join @tblBatches as tbl on tbl.batchID = b.batchID
	where b.controlCount <> tbl.actualCount

	-- update amounts if necessary
	update b
	set b.controlAmt = tbl.actualAmt
	from dbo.tr_batches as b
	inner join @tblBatches as tbl on tbl.batchID = b.batchID
	where b.controlAmt <> tbl.actualAmt

	-- reprocess any applicable conditions based on these just-posted batches	
	declare @tblConds TABLE (orgID int, conditionID int, batchDateLower datetime, batchDateUpper datetime)
	insert into @tblConds (orgID, conditionID, batchDateLower, batchDateUpper)
	select distinct c.orgID, c.conditionID, cv.conditionValue, cv2.conditionValue
	from dbo.ams_virtualGroupConditions as c
	inner join dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'batchDateLower'
	inner join dbo.ams_virtualGroupConditionValues as cv2 on cv2.conditionID = c.conditionID
	inner join dbo.ams_virtualGroupConditionKeys as k2 on k2.conditionKeyID = cv2.conditionKeyID and k2.conditionKey = 'batchDateUpper'
	where c.fieldcode IN ('acct_allocsum','acct_allocsumrecog')

	-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
	declare @itemGroupUID uniqueidentifier, @orgID int, @conditionIDList varchar(max)
	SELECT top 1 @orgID = orgID from @tblConds
	SELECT @conditionIDList = COALESCE(@conditionIDList + ',', '') + cast(c.conditionID as varchar(10)) 
		from @tblConds as c
		inner join @tblBatches as b on b.batchID = b.batchID
		where b.depositDate between c.batchDateLower and c.batchDateUpper
		group by c.conditionID
	IF @conditionIDList is not null
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList='', @conditionIDList=@conditionIDList, @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[ams_tuneVirtualGroupAssignments]
@report varchar(max) OUTPUT

AS

set nocount on

-- get snapshot of rules

IF OBJECT_ID('tempdb..#tmpConditionsRpt') IS NOT NULL 
	DROP TABLE #tmpConditionsRpt

select r.ruleID, 
	min(r.ruleXML.value('count(//condition)','int')) as conditionCount,
	max(cast (r.ruleXML.exist('//conditionset[@op = "OR"]') as integer)) as usesOR,
	max(cast (r.ruleXML.exist('//conditionset[@op = "AND"]') as integer)) as usesAND,
	max(cast (r.ruleXML.exist('//conditionset[@act = "exclude"]') as integer)) as usesExclude
into #tmpConditionsRpt
from dbo.ams_virtualGroupRules as r
inner join dbo.ams_virtualGroupRuleGroups as rg on rg.ruleID = r.ruleID
	and r.isActive = 1
	and r.ruleTypeID = 1
group by r.orgID, r.ruleID

DECLARE @tblSnap TABLE (id int IDENTITY(1,1), o tinyint, rc smallint, d varchar(100))
insert into @tblSnap (o, rc, d)
select 1, count(ruleID), 'number of single-condition rules (processed in mass)'
from #tmpConditionsRpt 
where conditionCount = 1
	union all
select 2, count(ruleID), 'number of multi-condition AND rules (processed in mass)'
from #tmpConditionsRpt 
where conditionCount > 1 and usesAND = 1 and usesOR = 0 and usesExclude = 0
	union all
select 3, count(ruleID), 'number of multi-condition OR rules (processed in mass)'
from #tmpConditionsRpt 
where conditionCount > 1 and usesAND = 0 and usesOR = 1 and usesExclude = 0
	union all
select 4, count(ruleID), 'number of multi-condition rules (processed rule by rule)'
from #tmpConditionsRpt 
where conditionCount > 1 and ((usesAND = 1 and usesOR = 1) OR usesExclude = 1)
	union all
select 5, null, null
order by 1

insert into @tblSnap (o, rc, d)
select 6, count(conditionID), subProc
from (
	select c.conditionID, 
		case 
		when left(c.fieldcode,5) = 'acct_' and c.fieldCode='acct_allocsum' and e.expression='between' then 'ACCT_BETWEEN_ALLOCSUM'
		when left(c.fieldcode,5) = 'acct_' and c.fieldCode='acct_allocsum' and e.expression='eq' then 'ACCT_EQ_ALLOCSUM'
		when left(c.fieldcode,5) = 'acct_' and c.fieldCode='acct_allocsum' and e.expression='exists' then 'ACCT_EXISTS_ALLOCSUM'
		when left(c.fieldcode,5) = 'acct_' and c.fieldCode='acct_allocsum' and e.expression='gt' then 'ACCT_GT_ALLOCSUM'
		when left(c.fieldcode,5) = 'acct_' and c.fieldCode='acct_allocsum' and e.expression='gte' then 'ACCT_GTE_ALLOCSUM'
		when left(c.fieldcode,5) = 'acct_' and c.fieldCode='acct_allocsum' and e.expression='lt' then 'ACCT_LT_ALLOCSUM'
		when left(c.fieldcode,5) = 'acct_' and c.fieldCode='acct_allocsum' and e.expression='lte' then 'ACCT_LTE_ALLOCSUM'
		when left(c.fieldcode,5) = 'acct_' and c.fieldCode='acct_allocsum' and e.expression='neq' then 'ACCT_NEQ_ALLOCSUM'
		when left(c.fieldcode,5) = 'acct_' and c.fieldCode='acct_allocsum' and e.expression='not_exists' then 'ACCT_NOTEXISTS_ALLOCSUM'
		when left(c.fieldcode,5) = 'acct_' and c.fieldCode='acct_allocsumrecog' and e.expression='between' then 'ACCT_BETWEEN_ALLOCSUMRECOG'
		when left(c.fieldcode,5) = 'acct_' and c.fieldCode='acct_allocsumrecog' and e.expression='eq' then 'ACCT_EQ_ALLOCSUMRECOG'
		when left(c.fieldcode,5) = 'acct_' and c.fieldCode='acct_allocsumrecog' and e.expression='exists' then 'ACCT_EXISTS_ALLOCSUMRECOG'
		when left(c.fieldcode,5) = 'acct_' and c.fieldCode='acct_allocsumrecog' and e.expression='gt' then 'ACCT_GT_ALLOCSUMRECOG'
		when left(c.fieldcode,5) = 'acct_' and c.fieldCode='acct_allocsumrecog' and e.expression='gte' then 'ACCT_GTE_ALLOCSUMRECOG'
		when left(c.fieldcode,5) = 'acct_' and c.fieldCode='acct_allocsumrecog' and e.expression='lt' then 'ACCT_LT_ALLOCSUMRECOG'
		when left(c.fieldcode,5) = 'acct_' and c.fieldCode='acct_allocsumrecog' and e.expression='lte' then 'ACCT_LTE_ALLOCSUMRECOG'
		when left(c.fieldcode,5) = 'acct_' and c.fieldCode='acct_allocsumrecog' and e.expression='neq' then 'ACCT_NEQ_ALLOCSUMRECOG'
		when left(c.fieldcode,5) = 'acct_' and c.fieldCode='acct_allocsumrecog' and e.expression='not_exists' then 'ACCT_NOTEXISTS_ALLOCSUMRECOG'
		when left(c.fieldCode,3) = 'ma_' and e.expression='contains' then 'MA_CONTAINS'
		when left(c.fieldCode,3) = 'ma_' and e.expression='contains_regex' then 'MA_CONTAINSREGEX'
		when left(c.fieldCode,3) = 'ma_' and e.expression='eq' then 'MA_EQ'
		when left(c.fieldCode,3) = 'ma_' and e.expression='exists' then 'MA_EXISTS'
		when left(c.fieldCode,3) = 'ma_' and e.expression='gt' then 'MA_GT'
		when left(c.fieldCode,3) = 'ma_' and e.expression='gte' then 'MA_GTE'
		when left(c.fieldCode,3) = 'ma_' and e.expression='lt' then 'MA_LT'
		when left(c.fieldCode,3) = 'ma_' and e.expression='lte' then 'MA_LTE'
		when left(c.fieldCode,3) = 'ma_' and e.expression='neq' then 'MA_NEQ'
		when left(c.fieldCode,3) = 'ma_' and e.expression='not_exists' then 'MA_NOTEXISTS'
		when left(c.fieldCode,3) = 'md_' and e.expression='datediff' then 'MD_DATEDIFF'
		when left(c.fieldCode,3) = 'md_' and e.expression='datepart' then 'MD_DATEPART'
		when left(c.fieldCode,3) = 'md_' and e.expression='contains' and dt.dataTypeCode='STRING' then 'MD_CONTAINS_STRING'
		when left(c.fieldCode,3) = 'md_' and e.expression='contains_regex' and dt.dataTypeCode='STRING' then 'MD_CONTAINSREGEX_STRING'
		when left(c.fieldCode,3) = 'md_' and e.expression='eq' and dt.dataTypeCode='STRING' then 'MD_EQ_STRING'
		when left(c.fieldCode,3) = 'md_' and e.expression='eq' and dt.dataTypeCode='BIT' then 'MD_EQ_BIT'
		when left(c.fieldCode,3) = 'md_' and e.expression='eq' and dt.dataTypeCode='INTEGER' then 'MD_EQ_INTEGER'
		when left(c.fieldCode,3) = 'md_' and e.expression='eq' and dt.dataTypeCode='DECIMAL2' then 'MD_EQ_DECIMAL2'
		when left(c.fieldCode,3) = 'md_' and e.expression='eq' and dt.dataTypeCode='DATE' then 'MD_EQ_DATE'
		when left(c.fieldCode,3) = 'md_' and e.expression='exists' and dt.dataTypeCode='STRING' then 'MD_EXISTS_STRING'
		when left(c.fieldCode,3) = 'md_' and e.expression='exists' and dt.dataTypeCode='BIT' then 'MD_EXISTS_BIT'
		when left(c.fieldCode,3) = 'md_' and e.expression='exists' and dt.dataTypeCode='INTEGER' then 'MD_EXISTS_INTEGER'
		when left(c.fieldCode,3) = 'md_' and e.expression='exists' and dt.dataTypeCode='DECIMAL2' then 'MD_EXISTS_DECIMAL2'
		when left(c.fieldCode,3) = 'md_' and e.expression='exists' and dt.dataTypeCode='DATE' then 'MD_EXISTS_DATE'
		when left(c.fieldCode,3) = 'md_' and e.expression='exists' and dt.dataTypeCode='CONTENTOBJ' then 'MD_EXISTS_CONTENTOBJ'
		when left(c.fieldCode,3) = 'md_' and e.expression='exists' and dt.dataTypeCode='DOCUMENTOBJ' then 'MD_EXISTS_DOCUMENTOBJ'
		when left(c.fieldCode,3) = 'md_' and e.expression='gt' and dt.dataTypeCode='STRING' then 'MD_GT_STRING'
		when left(c.fieldCode,3) = 'md_' and e.expression='gt' and dt.dataTypeCode='INTEGER' then 'MD_GT_INTEGER'
		when left(c.fieldCode,3) = 'md_' and e.expression='gt' and dt.dataTypeCode='DECIMAL2' then 'MD_GT_DECIMAL2'
		when left(c.fieldCode,3) = 'md_' and e.expression='gt' and dt.dataTypeCode='DATE' then 'MD_GT_DATE'
		when left(c.fieldCode,3) = 'md_' and e.expression='gte' and dt.dataTypeCode='STRING' then 'MD_GTE_STRING'
		when left(c.fieldCode,3) = 'md_' and e.expression='gte' and dt.dataTypeCode='INTEGER' then 'MD_GTE_INTEGER'
		when left(c.fieldCode,3) = 'md_' and e.expression='gte' and dt.dataTypeCode='DECIMAL2' then 'MD_GTE_DECIMAL2'
		when left(c.fieldCode,3) = 'md_' and e.expression='gte' and dt.dataTypeCode='DATE' then 'MD_GTE_DATE'
		when left(c.fieldCode,3) = 'md_' and e.expression='lt' and dt.dataTypeCode='STRING' then 'MD_LT_STRING'
		when left(c.fieldCode,3) = 'md_' and e.expression='lt' and dt.dataTypeCode='INTEGER' then 'MD_LT_INTEGER'
		when left(c.fieldCode,3) = 'md_' and e.expression='lt' and dt.dataTypeCode='DECIMAL2' then 'MD_LT_DECIMAL2'
		when left(c.fieldCode,3) = 'md_' and e.expression='lt' and dt.dataTypeCode='DATE' then 'MD_LT_DATE'
		when left(c.fieldCode,3) = 'md_' and e.expression='lte' and dt.dataTypeCode='STRING' then 'MD_LTE_STRING'
		when left(c.fieldCode,3) = 'md_' and e.expression='lte' and dt.dataTypeCode='INTEGER' then 'MD_LTE_INTEGER'
		when left(c.fieldCode,3) = 'md_' and e.expression='lte' and dt.dataTypeCode='DECIMAL2' then 'MD_LTE_DECIMAL2'
		when left(c.fieldCode,3) = 'md_' and e.expression='lte' and dt.dataTypeCode='DATE' then 'MD_LTE_DATE'
		when left(c.fieldCode,3) = 'md_' and e.expression='neq' and dt.dataTypeCode='STRING' then 'MD_NEQ_STRING'
		when left(c.fieldCode,3) = 'md_' and e.expression='neq' and dt.dataTypeCode='BIT' then 'MD_NEQ_BIT'
		when left(c.fieldCode,3) = 'md_' and e.expression='neq' and dt.dataTypeCode='INTEGER' then 'MD_NEQ_INTEGER'
		when left(c.fieldCode,3) = 'md_' and e.expression='neq' and dt.dataTypeCode='DECIMAL2' then 'MD_NEQ_DECIMAL2'
		when left(c.fieldCode,3) = 'md_' and e.expression='neq' and dt.dataTypeCode='DATE' then 'MD_NEQ_DATE'
		when left(c.fieldCode,3) = 'md_' and e.expression='not_exists' and dt.dataTypeCode='STRING' then 'MD_NOTEXISTS_STRING'
		when left(c.fieldCode,3) = 'md_' and e.expression='not_exists' and dt.dataTypeCode='INTEGER' then 'MD_NOTEXISTS_INTEGER'
		when left(c.fieldCode,3) = 'md_' and e.expression='not_exists' and dt.dataTypeCode='DECIMAL2' then 'MD_NOTEXISTS_DECIMAL2'
		when left(c.fieldCode,3) = 'md_' and e.expression='not_exists' and dt.dataTypeCode='DATE' then 'MD_NOTEXISTS_DATE'
		when left(c.fieldCode,3) = 'md_' and e.expression='not_exists' and dt.dataTypeCode='BIT' then 'MD_NOTEXISTS_BIT'
		when left(c.fieldCode,3) = 'md_' and e.expression='not_exists' and dt.dataTypeCode='CONTENTOBJ' then 'MD_NOTEXISTS_CONTENTOBJ'
		when left(c.fieldCode,3) = 'md_' and e.expression='not_exists' and dt.dataTypeCode='DOCUMENTOBJ' then 'MD_NOTEXISTS_DOCUMENTOBJ'
		when left(c.fieldCode,4) = 'mad_' and e.expression='eq' then 'MAD_EQ'
		when left(c.fieldCode,4) = 'mad_' and e.expression='exists' then 'MAD_EXISTS'
		when left(c.fieldCode,4) = 'mad_' and e.expression='neq' then 'MAD_NEQ'
		when left(c.fieldCode,4) = 'mad_' and e.expression='not_exists' then 'MAD_NOTEXISTS'
		when left(c.fieldCode,3) = 'me_' and e.expression='contains' then 'ME_CONTAINS'
		when left(c.fieldCode,3) = 'me_' and e.expression='contains_regex' then 'ME_CONTAINSREGEX'
		when left(c.fieldCode,3) = 'me_' and e.expression='eq' then 'ME_EQ'
		when left(c.fieldCode,3) = 'me_' and e.expression='exists' then 'ME_EXISTS'
		when left(c.fieldCode,3) = 'me_' and e.expression='gt' then 'ME_GT'
		when left(c.fieldCode,3) = 'me_' and e.expression='gte' then 'ME_GTE'
		when left(c.fieldCode,3) = 'me_' and e.expression='lt' then 'ME_LT'
		when left(c.fieldCode,3) = 'me_' and e.expression='lte' then 'ME_LTE'
		when left(c.fieldCode,3) = 'me_' and e.expression='neq' then 'ME_NEQ'
		when left(c.fieldCode,3) = 'me_' and e.expression='not_exists' then 'ME_NOTEXISTS'
		when left(c.fieldCode,2) = 'e_' and e.expression='registered' then 'E_REGISTERED'
		when left(c.fieldCode,2) = 'e_' and e.expression='attended' then 'E_ATTENDED'
		when left(c.fieldCode,2) = 'e_' and e.expression='awarded' then 'E_AWARDED'
		when left(c.fieldcode,2) = 'l_' and c.fieldCode='l_entry' and e.expression='onlist' then 'L_ONLIST'
		when left(c.fieldCode,2) = 'm_' and e.expression='contains' then 'M_CONTAINS'
		when left(c.fieldCode,2) = 'm_' and e.expression='contains_regex' then 'M_CONTAINSREGEX'
		when left(c.fieldCode,2) = 'm_' and e.expression='eq' and dt.dataTypeCode='STRING' then 'M_EQ_STRING'
		when left(c.fieldCode,2) = 'm_' and e.expression='eq' and dt.dataTypeCode='INTEGER' then 'M_EQ_INTEGER'
		when left(c.fieldCode,2) = 'm_' and e.expression='exists' then 'M_EXISTS'
		when left(c.fieldCode,2) = 'm_' and e.expression='gt' then 'M_GT'
		when left(c.fieldCode,2) = 'm_' and e.expression='gte' then 'M_GTE'
		when left(c.fieldCode,2) = 'm_' and e.expression='lt' then 'M_LT'
		when left(c.fieldCode,2) = 'm_' and e.expression='lte' then 'M_LTE'
		when left(c.fieldCode,2) = 'm_' and e.expression='neq' and dt.dataTypeCode='STRING' then 'M_NEQ_STRING'
		when left(c.fieldCode,2) = 'm_' and e.expression='neq' and dt.dataTypeCode='INTEGER' then 'M_NEQ_INTEGER'
		when left(c.fieldCode,2) = 'm_' and e.expression='not_exists' then 'M_NOTEXISTS'
		when left(c.fieldcode,3) = 'mh_' and c.fieldCode='mh_entry' and e.expression='exists' then 'MH_EXISTS'
		when left(c.fieldCode,3) = 'mp_' and e.expression='contains' then 'MP_CONTAINS'
		when left(c.fieldCode,3) = 'mp_' and e.expression='contains_regex' then 'MP_CONTAINSREGEX'
		when left(c.fieldCode,3) = 'mp_' and e.expression='eq' then 'MP_EQ'
		when left(c.fieldCode,3) = 'mp_' and e.expression='exists' then 'MP_EXISTS'
		when left(c.fieldCode,3) = 'mp_' and e.expression='gt' then 'MP_GT'
		when left(c.fieldCode,3) = 'mp_' and e.expression='gte' then 'MP_GTE'
		when left(c.fieldCode,3) = 'mp_' and e.expression='lt' then 'MP_LT'
		when left(c.fieldCode,3) = 'mp_' and e.expression='lte' then 'MP_LTE'
		when left(c.fieldCode,3) = 'mp_' and e.expression='neq' then 'MP_NEQ'
		when left(c.fieldCode,3) = 'mp_' and e.expression='not_exists' then 'MP_NOTEXISTS'
		when left(c.fieldcode,4) = 'mpl_' and e.expression='contains' then 'MPL_CONTAINS'
		when left(c.fieldcode,4) = 'mpl_' and e.expression='contains_regex' then 'MPL_CONTAINSREGEX'
		when left(c.fieldcode,4) = 'mpl_' and e.expression='datepart' then 'MPL_DATEPART'
		when left(c.fieldcode,4) = 'mpl_' and e.expression='datediff' then 'MPL_DATEDIFF'
		when left(c.fieldcode,4) = 'mpl_' and e.expression='eq' and dt.dataTypeCode='DATE' then 'MPL_EQ_DATE'
		when left(c.fieldcode,4) = 'mpl_' and e.expression='eq' and dt.dataTypeCode='STRING' then 'MPL_EQ_STRING'
		when left(c.fieldcode,4) = 'mpl_' and e.expression='exists' and dt.dataTypeCode='DATE' then 'MPL_EXISTS_DATE'
		when left(c.fieldcode,4) = 'mpl_' and e.expression='exists' and dt.dataTypeCode='STRING' then 'MPL_EXISTS_STRING'
		when left(c.fieldcode,4) = 'mpl_' and e.expression='gt' and dt.dataTypeCode='DATE' then 'MPL_GT_DATE'
		when left(c.fieldcode,4) = 'mpl_' and e.expression='gt' and dt.dataTypeCode='STRING' then 'MPL_GT_STRING'
		when left(c.fieldcode,4) = 'mpl_' and e.expression='gte' and dt.dataTypeCode='DATE' then 'MPL_GTE_DATE'
		when left(c.fieldcode,4) = 'mpl_' and e.expression='gte' and dt.dataTypeCode='STRING' then 'MPL_GTE_STRING'
		when left(c.fieldcode,4) = 'mpl_' and e.expression='lt' and dt.dataTypeCode='DATE' then 'MPL_LT_DATE'
		when left(c.fieldcode,4) = 'mpl_' and e.expression='lt' and dt.dataTypeCode='STRING' then 'MPL_LT_STRING'
		when left(c.fieldcode,4) = 'mpl_' and e.expression='lte' and dt.dataTypeCode='DATE' then 'MPL_LTE_DATE'
		when left(c.fieldcode,4) = 'mpl_' and e.expression='lte' and dt.dataTypeCode='STRING' then 'MPL_LTE_STRING'
		when left(c.fieldcode,4) = 'mpl_' and e.expression='neq' and dt.dataTypeCode='DATE' then 'MPL_NEQ_DATE'
		when left(c.fieldcode,4) = 'mpl_' and e.expression='neq' and dt.dataTypeCode='STRING' then 'MPL_NEQ_STRING'
		when left(c.fieldcode,4) = 'mpl_' and e.expression='not_exists' and dt.dataTypeCode='DATE' then 'MPL_NOTEXISTS_DATE'
		when left(c.fieldcode,4) = 'mpl_' and e.expression='not_exists' and dt.dataTypeCode='STRING' then 'MPL_NOTEXISTS_STRING'
		when left(c.fieldcode,3) = 'rt_' and c.fieldCode='rt_role' and e.expression='linked' then 'RT_LINKED'
		when left(c.fieldcode,4) = 'sub_' and e.expression='subscribed' then 'SUB_SUBSCRIBED'
		when left(c.fieldCode,3) = 'mw_' and e.expression='contains' then 'MW_CONTAINS'
		when left(c.fieldCode,3) = 'mw_' and e.expression='contains_regex' then 'MW_CONTAINSREGEX'
		when left(c.fieldCode,3) = 'mw_' and e.expression='eq' then 'MW_EQ'
		when left(c.fieldCode,3) = 'mw_' and e.expression='exists' then 'MW_EXISTS'
		when left(c.fieldCode,3) = 'mw_' and e.expression='gt' then 'MW_GT'
		when left(c.fieldCode,3) = 'mw_' and e.expression='gte' then 'MW_GTE'
		when left(c.fieldCode,3) = 'mw_' and e.expression='lt' then 'MW_LT'
		when left(c.fieldCode,3) = 'mw_' and e.expression='lte' then 'MW_LTE'
		when left(c.fieldCode,3) = 'mw_' and e.expression='neq' then 'MW_NEQ'
		when left(c.fieldCode,3) = 'mw_' and e.expression='not_exists' then 'MW_NOTEXISTS'
		else ''
		end as subProc
	from dbo.ams_virtualGroupConditions AS c
	INNER JOIN dbo.ams_virtualGroupExpressions as e on e.expressionID = c.expressionID 
	INNER JOIN dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = c.dataTypeID 
	where left(c.fieldCode,4) <> 'grp_'
) as tmp
GROUP BY tmp.subProc
order by 2 desc, 3

select @report = COALESCE(@report + char(13) + char(10),'') + case when d is not null then cast(rc as char(7)) + ' : ' + d else '' end
from @tblSnap
order by o, id

IF OBJECT_ID('tempdb..#tmpConditionsRpt') IS NOT NULL 
	DROP TABLE #tmpConditionsRpt

set nocount off

RETURN 0
GO

ALTER FUNCTION [dbo].[fn_ams_getVirtualGroupConditionVerboseFields] (
	@orgID int,
	@fieldcode varchar(40)
)
RETURNS @fields TABLE (
	fieldCode varchar(40), 
	fieldCodeArea varchar(40), 
	fieldLabel varchar(max), 
	displayTypeCode varchar(20), 
	dataTypeCode varchar(20)
)
AS
BEGIN

	DECLARE @subT varchar(100), @subS varchar(300), @subR varchar(403), @subSS varchar(50), @subPS varchar(50)

	IF left(@fieldCode,2) = 'm_'
		insert into @fields (fieldCode, fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode)
		select fieldCode, 'Member Data' as fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode
		from dbo.fn_ams_getConditionFields_member(@orgID)
		where fieldcode = @fieldCode

	IF left(@fieldCode,3) = 'md_'
		insert into @fields (fieldCode, fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode)
		select 'md_' + cast(mdc.columnID as varchar(10)) as fieldCode, 'Custom Fields' as fieldCodeArea, mdc.columnName as fieldLabel, dt.displayTypeCode, ddt.dataTypeCode
		from dbo.ams_memberdatacolumns as mdc
		inner join dbo.ams_memberDataColumnDisplayTypes as dt on dt.displayTypeID = mdc.displayTypeID
		inner join dbo.ams_memberDataColumnDataTypes as ddt on ddt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID
		and mdc.columnID = parsename(replace(@fieldcode,'_','.'),1)

	IF left(@fieldCode,3) = 'ma_' or left(@fieldCode,3) = 'mp_' or left(@fieldCode,4) = 'mad_'
		insert into @fields (fieldCode, fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode)
		select af.fieldCode, 'Addresses' as fieldCodeArea, at.addressType + ' ' + af.fieldLabel as fieldLabel, af.displayTypeCode, af.dataTypeCode
		from dbo.ams_memberAddressTypes as at
		cross apply dbo.fn_ams_getConditionFields_addrfields(at.orgid,at.addressTypeID) as af
		where at.orgID = @orgID
		and af.fieldcode = @fieldCode

	IF left(@fieldCode,3) = 'me_'
		insert into @fields (fieldCode, fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode)
		select fieldCode, 'Emails' as fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode
		from dbo.fn_ams_getConditionFields_emailtypes(@orgID)
		where fieldcode = @fieldCode

	IF left(@fieldCode,3) = 'mw_'
		insert into @fields (fieldCode, fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode)
		select fieldCode, 'Websites' as fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode
		from dbo.fn_ams_getConditionFields_websitetypes(@orgID)
		where fieldcode = @fieldCode

	IF left(@fieldCode,2) = 'e_'
		insert into @fields (fieldCode, fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode)
		select 'e_' + cast(e.eventID as varchar(10)) as fieldCode, 'Events' as fieldCodeArea, isnull(eventContent.contentTitle,'<no title>') as fieldLabel, 'SELECT', 'STRING'
		from dbo.ev_events as e
		cross apply dbo.fn_getContent(e.eventContentID,1) as eventContent
		where e.eventID = parsename(replace(@fieldcode,'_','.'),1)

	IF @fieldCode = 'acct_allocsum'
		insert into @fields (fieldCode, fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode)
		VALUES ('acct_allocsum','Accounting','Allocation Summary of Cash','TEXTBOX','DECIMAL2')

	IF @fieldCode = 'acct_allocsumrecog'
		insert into @fields (fieldCode, fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode)
		VALUES ('acct_allocsumrecog','Accounting','Allocation Summary of Recognized Revenue','TEXTBOX','DECIMAL2')

	IF @fieldCode = 'rt_role'
		insert into @fields (fieldCode, fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode)
		VALUES ('rt_role','Record Types','Linked Roles','SELECT','STRING')

	IF left(@fieldcode,4) = 'mpl_'
		insert into @fields (fieldCode, fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode)
		select 'mpl_' + cast(PLTypeID as varchar(10)) + '_licenseNumber' as fieldCode, 'Professional Licenses' as fieldCodeArea, PLName + ' License Number' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from dbo.ams_memberProfessionalLicenseTypes
		where orgID = @orgID
		and PLTypeID = parsename(replace(@fieldcode,'_','.'),2)
		and parsename(replace(@fieldcode,'_','.'),1) = 'licenseNumber'
			union all
		select 'mpl_' + cast(PLTypeID as varchar(10)) + '_activeDate' as fieldCode, 'Professional Licenses' as fieldCodeArea, PLName + ' Active Date' as fieldLabel, 'DATE' as displayTypeCode, 'DATE' as dataTypeCode
		from dbo.ams_memberProfessionalLicenseTypes
		where orgID = @orgID
		and PLTypeID = parsename(replace(@fieldcode,'_','.'),2)
		and parsename(replace(@fieldcode,'_','.'),1) = 'activeDate'
			union all
		select 'mpl_' + cast(PLTypeID as varchar(10)) + '_status' as fieldCode, 'Professional Licenses' as fieldCodeArea, PLName + ' Status' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		from dbo.ams_memberProfessionalLicenseTypes
		where orgID = @orgID
		and PLTypeID = parsename(replace(@fieldcode,'_','.'),2)
		and parsename(replace(@fieldcode,'_','.'),1) = 'status'

	IF left(@fieldcode,4) = 'grp_'
		insert into @fields (fieldCode, fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode)
		select 'grp_' + cast(groupID as varchar(10)) as fieldCode, 'Groups' as fieldCodeArea, dbo.fn_getGroupPathExpanded(groupid) as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		from dbo.ams_groups
		where orgID = @orgID
		and groupID = parsename(replace(@fieldcode,'_','.'),1)

	IF @fieldCode = 'sub_entry'
		insert into @fields (fieldCode, fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode)
		VALUES (@fieldcode,'Subscriptions','Subscription Membership','SELECT','STRING')

	IF @fieldCode = 'mh_entry'
		insert into @fields (fieldCode, fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode)
		VALUES (@fieldCode,'Member History','History Entry','SELECT','STRING')

	IF @fieldCode = 'l_entry'
		insert into @fields (fieldCode, fieldCodeArea, fieldLabel, displayTypeCode, dataTypeCode)
		VALUES (@fieldCode,'Listserver Memberships','List Membership','SELECT','STRING')
	
	RETURN 
END

GO

ALTER FUNCTION [dbo].[ams_getVirtualGroupConditionVerbose] (@conditionID int)
RETURNS varchar(max)
AS
BEGIN

	DECLARE @fieldCode varchar(40), @orgid int, @verbose varchar(max), @hasVerbose bit, 
		@expression varchar(20), @fieldLabel varchar(max), @displayTypeCode varchar(20),
		@dataTypeCode varchar(20)
	set @hasVerbose = 0

	select @fieldCode=c.fieldCode, @orgID=c.orgID, @expression=e.expression, 
		@fieldLabel=fields.fieldLabel, @displayTypeCode=fields.displayTypeCode,
		@dataTypeCode=fields.dataTypeCode
		from dbo.ams_virtualGroupConditions as c
		inner join dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		cross apply dbo.fn_ams_getVirtualGroupConditionVerboseFields(c.orgID,c.fieldCode) as fields
		where c.conditionID = @conditionID

	-- events
	IF @hasVerbose = 0 and left(@fieldCode,2) = 'e_' BEGIN
		select top 1 @verbose = e.expressionVerbose + ' ' + @fieldLabel
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		where c.conditionID = @conditionID
		
		set @hasVerbose = 1
	END

	-- accounting
	IF @hasVerbose = 0 and @fieldcode in ('acct_allocsum','acct_allocsumrecog') BEGIN
		select top 1 @verbose = case
			when @expression in ('exists','not_exists') then
				case when batchDateLower.val = batchDateUpper.val then 'Allocations ' + case when @expression = 'exists' then 'exist' else 'do not exist' end + ' on ' + batchDateLower.val + ' with members linked to ' + linkAllocType.val + ' for ' + case when @fieldcode = 'acct_allocsumrecog' then 'recognized revenue in ' else '' end + LEFT(gllist.list, LEN(gllist.list)-1)
				else 'Allocations ' + case when @expression = 'exists' then 'exist' else 'do not exist' end + ' between ' + batchDateLower.val + ' and ' + batchDateUpper.val + ' with members linked to ' + linkAllocType.val + ' for ' + case when @fieldcode = 'acct_allocsumrecog' then 'recognized revenue in ' else '' end + LEFT(gllist.list, LEN(gllist.list)-1)
				end
			when @expression = 'between' then
				case when batchDateLower.val = batchDateUpper.val then 'Allocations on ' + batchDateLower.val + ' with members linked to ' + linkAllocType.val + ' ' + e.expressionVerbose + ' ' + valueLower.val + ' and ' + valueUpper.val + ' for ' + case when @fieldcode = 'acct_allocsumrecog' then 'recognized revenue in ' else '' end + LEFT(gllist.list, LEN(gllist.list)-1)
				else 'Allocations between ' + batchDateLower.val + ' and ' + batchDateUpper.val + ' with members linked to ' + linkAllocType.val + ' ' + e.expressionVerbose + ' ' + valueLower.val + ' and ' + valueUpper.val + ' for ' + case when @fieldcode = 'acct_allocsumrecog' then 'recognized revenue in ' else '' end + LEFT(gllist.list, LEN(gllist.list)-1)
				end
			else 
				case when batchDateLower.val = batchDateUpper.val then 'Allocations on ' + batchDateLower.val + ' with members linked to ' + linkAllocType.val + ' ' + e.expressionVerbose + ' ' + value.val + ' for ' + case when @fieldcode = 'acct_allocsumrecog' then 'recognized revenue in ' else '' end + LEFT(gllist.list, LEN(gllist.list)-1)
				else 'Allocations between ' + batchDateLower.val + ' and ' + batchDateUpper.val + ' with members linked to ' + linkAllocType.val + ' ' + e.expressionVerbose + ' ' + value.val + ' for ' + case when @fieldcode = 'acct_allocsumrecog' then 'recognized revenue in ' else '' end + LEFT(gllist.list, LEN(gllist.list)-1)
				end
			end
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		CROSS APPLY ( 
			SELECT gl.thePathExpanded + ', ' AS [text()] 
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'revenueGL'
			inner join dbo.fn_getRecursiveGLAccounts(@orgID) as gl on cast(gl.GLAccountID as varchar(10)) = cv.conditionValue
			WHERE cv.conditionID = c.conditionID
			ORDER BY gl.thePath
			FOR XML PATH('')
		) as gllist(list) 
		CROSS APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'batchDateLower'
			WHERE cv.conditionID = c.conditionID
		) as batchDateLower(val)
		CROSS APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'batchDateUpper'
			WHERE cv.conditionID = c.conditionID
		) as batchDateUpper(val)
		CROSS APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'linkAllocType'
			WHERE cv.conditionID = c.conditionID
		) as linkAllocType(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'value'
			WHERE cv.conditionID = c.conditionID
		) as value(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'valueLower'
			WHERE cv.conditionID = c.conditionID
		) as valueLower(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'valueUpper'
			WHERE cv.conditionID = c.conditionID
		) as valueUpper(val)
		where c.conditionID = @conditionID

		set @hasVerbose = 1
	END

	-- record types
	IF @hasVerbose = 0 and @fieldcode = 'rt_role' BEGIN
		select top 1 @verbose = 'Record Type of ' + rt.recordTypeName + 
			case 
			when len(isnull(rolelist.list,'')) = 0 then ''
			else ' with linked role of ' + LEFT(rolelist.list, LEN(rolelist.list)-1)
			end
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		CROSS APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'recordType'
			WHERE cv.conditionID = c.conditionID
		) as recordType(val)
		INNER JOIN dbo.ams_recordTypes as rt on rt.recordTypeID = cast(recordType.val as int)
		OUTER APPLY ( 
			select rrt.relationshipTypeName + ' to ' + coalesce(masterLinkingRT.recordTypeName,childLinkingRT.recordTypeName) + ', ' AS [text()] 
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'role'
			inner join dbo.ams_recordTypesRelationshipTypes as rtrt on cast(rtrt.recordTypeRelationshipTypeID as varchar(10)) = cv.conditionValue
			inner join dbo.ams_recordRelationshipTypes as rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID
			inner join dbo.ams_recordTypes as rt1 on rt1.recordTypeID = rt.recordTypeID
			left outer join dbo.ams_recordTypes as masterLinkingRT on rtrt.masterRecordTypeID = masterLinkingRT.recordTypeID and rt1.isPerson = 1
			left outer join dbo.ams_recordTypes as childLinkingRT on rtrt.childRecordTypeID = childLinkingRT.recordTypeID and rt1.isPerson = 0
			WHERE cv.conditionID = c.conditionID
			ORDER BY rrt.relationshipTypeName
			FOR XML PATH('')
		) as rolelist(list) 
		where c.conditionID = @conditionID

		set @hasVerbose = 1
	END

	-- group membership
	IF @hasVerbose = 0 and left(@fieldcode,4) = 'grp_' BEGIN
		set @verbose = 'Member of ' + @fieldLabel
		set @hasVerbose = 1
	END

	-- subscriptions
	IF @hasVerbose = 0 and left(@fieldcode,4) = 'sub_' BEGIN

		declare @subS varchar(max), @subR varchar(max), @subSS varchar(max), @subPS varchar(max), @subF varchar(max), @tempFieldLabel varchar(max)
		select @subS = coalesce(@subS + ', ' ,'') + subs.subscriptionName
			from dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID 
				and k.conditionKey = 'subSubscription'
			inner join dbo.sub_subscriptions as subs on subs.subscriptionID = cv.conditionValue
			where cv.conditionID = @conditionID
			order by cv.conditionValue
		if @subS is not null
			set @subS = 'Subscription of ' + @subS

		select @subR = coalesce(@subR + ', ' ,'') +  rs.scheduleName + ' \ ' + r.rateName
			from dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID 
				and k.conditionKey = 'subRate'
			inner join dbo.sub_rates as r on r.rateID = cv.conditionValue
			inner join dbo.sub_rateSchedules as rs on rs.scheduleID = r.scheduleID 	
			where cv.conditionID = @conditionID
			order by cv.conditionValue
		if @subR is not null
			set @subR = 'Rate of ' + @subR

		select @subSS = coalesce(@subSS + ', ' ,'') + s.statusName
			from dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID 
				and k.conditionKey = 'subStatus'
			inner join dbo.sub_statuses as s on s.statusid = cv.conditionValue
			where cv.conditionID = @conditionID
			order by cv.conditionValue
		if @subSS is not null
			set @subSS = 'Status of ' + @subSS

		select @subPS = coalesce(@subPS + ', ' ,'') + ps.statusName
			from dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID 
				and k.conditionKey = 'subPaymentStatus'
			inner join dbo.sub_paymentStatuses as ps on ps.statusid = cv.conditionValue
			where cv.conditionID = @conditionID
			order by cv.conditionValue
		if @subPS is not null
			set @subPS = 'Payment Status of ' + @subPS

		select @subF = coalesce(@subF + ', ' ,'') + f.frequencyName
			from dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID 
				and k.conditionKey = 'subFrequency'
			inner join dbo.sub_frequencies as f on f.frequencyID = cv.conditionValue
			where cv.conditionID = @conditionID
			order by cv.conditionValue
		if @subF is not null
			set @subF = 'Frequency of ' + @subF

		set @tempFieldLabel =  isnull('; ' + @subS,'') + isnull('; ' + @subR,'') + isnull('; ' + @subSS,'') + isnull('; ' + @subPS,'') + isnull('; ' + @subF,'')

		select top 1 @verbose = 'Has Subscription with Type of ' + st.typeName + @tempFieldLabel + 
			case when len(isnull(subStartDateLower.val,'')) > 0 or len(isnull(subStartDateUpper.val,'')) > 0 or len(isnull(subEndDateLower.val,'')) > 0 or len(isnull(subEndDateUpper.val,'')) > 0 or len(isnull(subGraceDateLower.val,'')) > 0 or len(isnull(subGraceDateUpper.val,'')) > 0 then '; having ' else '' end + 
			case 
			when len(isnull(subStartDateLower.val,'')) > 0 and len(isnull(subStartDateUpper.val,'')) > 0 then 'subscription start date between ' + subStartDateLower.val + ' and ' + subStartDateUpper.val
			when len(isnull(subStartDateLower.val,'')) > 0 then 'subscription start date after ' + subStartDateLower.val
			when len(isnull(subStartDateUpper.val,'')) > 0 then 'subscription start date before ' + subStartDateUpper.val
			else ''
			end +
			case when (len(isnull(subStartDateLower.val,'')) > 0 or len(isnull(subStartDateUpper.val,'')) > 0) and (len(isnull(subEndDateLower.val,'')) > 0 or len(isnull(subEndDateUpper.val,'')) > 0) then ' and ' else '' end +
			case 
			when len(isnull(subEndDateLower.val,'')) > 0 and len(isnull(subEndDateUpper.val,'')) > 0 then 'subscription end date between ' + subEndDateLower.val + ' and ' + subEndDateUpper.val
			when len(isnull(subEndDateLower.val,'')) > 0 then 'subscription end date after ' + subEndDateLower.val
			when len(isnull(subEndDateUpper.val,'')) > 0 then 'subscription end date before ' + subEndDateUpper.val
			else ''
			end +
			case when (len(isnull(subStartDateLower.val,'')) > 0 or len(isnull(subStartDateUpper.val,'')) > 0 or len(isnull(subEndDateLower.val,'')) > 0 or len(isnull(subEndDateUpper.val,'')) > 0) and (len(isnull(subGraceDateLower.val,'')) > 0 or len(isnull(subGraceDateUpper.val,'')) > 0) then ' and ' else '' end +
			case 
			when len(isnull(subGraceDateLower.val,'')) > 0 and len(isnull(subGraceDateUpper.val,'')) > 0 then 'subscription grace date between ' + subGraceDateLower.val + ' and ' + subGraceDateUpper.val
			when len(isnull(subGraceDateLower.val,'')) > 0 then 'subscription grace date after ' + subGraceDateLower.val
			when len(isnull(subGraceDateUpper.val,'')) > 0 then 'subscription grace date before ' + subGraceDateUpper.val
			else ''
			end
		from dbo.ams_virtualGroupConditions as c
		CROSS APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subSubType'
			WHERE cv.conditionID = c.conditionID
		) as subSubType(val)
		INNER JOIN dbo.sub_types as st on st.typeID = cast(subSubType.val as int)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subStartDateLower'
			WHERE cv.conditionID = c.conditionID
		) as subStartDateLower(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subStartDateUpper'
			WHERE cv.conditionID = c.conditionID
		) as subStartDateUpper(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subEndDateLower'
			WHERE cv.conditionID = c.conditionID
		) as subEndDateLower(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subEndDateUpper'
			WHERE cv.conditionID = c.conditionID
		) as subEndDateUpper(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subGraceDateLower'
			WHERE cv.conditionID = c.conditionID
		) as subGraceDateLower(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subGraceDateUpper'
			WHERE cv.conditionID = c.conditionID
		) as subGraceDateUpper(val)
		where c.conditionID = @conditionID

		set @hasVerbose = 1
	END

	-- member history
	IF @hasVerbose = 0 and left(@fieldCode,3) = 'mh_'  BEGIN
		select top 1 @verbose = 'Has Member History Entry in ' + historyCategory.val +
			case when len(isnull(historySubCategory.val,'')) > 0 then ' and ' + historySubCategory.val else '' end +
			case 
				when len(isnull(historyDateLower.val,'')) > 0 and len(isnull(historyDateUpper.val,'')) > 0 then '; dated between ' + historyDateLower.val + ' and ' + historyDateUpper.val
				when len(isnull(historyDateLower.val,'')) > 0 then '; dated after ' + historyDateLower.val
				when len(isnull(historyDateUpper.val,'')) > 0 then '; dated before ' + historyDateUpper.val
			else ''
			end +
			case 
				when len(isnull(historyEnteredDateLower.val,'')) > 0 and len(isnull(historyEnteredDateUpper.val,'')) > 0 then '; entered between ' + historyEnteredDateLower.val + ' and ' + historyEnteredDateUpper.val
				when len(isnull(historyEnteredDateLower.val,'')) > 0 then '; entered after ' + historyEnteredDateLower.val
				when len(isnull(historyEnteredDateUpper.val,'')) > 0 then '; entered before ' + historyEnteredDateUpper.val
			else ''
			end +
			case 
			when len(isnull(historyQuantityLower.val,'')) > 0 and len(isnull(historyQuantityUpper.val,'')) > 0 then '; quantity between ' + historyQuantityLower.val + ' and ' + historyQuantityUpper.val
			when len(isnull(historyQuantityLower.val,'')) > 0 then '; quantity greater than or equal to ' + historyQuantityLower.val
			when len(isnull(historyQuantityUpper.val,'')) > 0 then '; quantity less than or equal to ' + historyQuantityUpper.val
			else ''
			end +
			case 
			when len(isnull(historyAmountLower.val,'')) > 0 and len(isnull(historyAmountUpper.val,'')) > 0 then '; amount between $' + historyAmountLower.val + ' and $' + historyAmountUpper.val
			when len(isnull(historyAmountLower.val,'')) > 0 then '; amount greater than or equal to $' + historyAmountLower.val
			when len(isnull(historyAmountUpper.val,'')) > 0 then '; amount less than or equal to $' + historyAmountUpper.val
			else ''
			end +
			case when len(isnull(historyDescriptionContains.val,'')) > 0 then '; description contains ' + historyDescriptionContains.val else '' end			
		from dbo.ams_virtualGroupConditions as c
		OUTER APPLY (
			SELECT cat.categoryName as conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyCategory'
			inner join cms_categories cat
				on cat.categoryID = cast(cv.conditionValue as int)
			WHERE cv.conditionID = c.conditionID
		) as historyCategory(val)
		OUTER APPLY (
			SELECT replace(dbo.pipelist(cat.categoryName),'|',' or ') as conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historySubCategory'
			inner join cms_categories cat
				on cat.categoryID = cast(cv.conditionValue as int)
			WHERE cv.conditionID = c.conditionID
			group by k.conditionKey
		) as historySubCategory(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyDateLower'
			WHERE cv.conditionID = c.conditionID
		) as historyDateLower(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyDateUpper'
			WHERE cv.conditionID = c.conditionID
		) as historyDateUpper(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyEnteredDateLower'
			WHERE cv.conditionID = c.conditionID
		) as historyEnteredDateLower(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyEnteredDateUpper'
			WHERE cv.conditionID = c.conditionID
		) as historyEnteredDateUpper(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyQuantityLower'
			WHERE cv.conditionID = c.conditionID
		) as historyQuantityLower(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyQuantityUpper'
			WHERE cv.conditionID = c.conditionID
		) as historyQuantityUpper(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyAmountLower'
			WHERE cv.conditionID = c.conditionID
		) as historyAmountLower(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyAmountUpper'
			WHERE cv.conditionID = c.conditionID
		) as historyAmountUpper(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'historyDescriptionContains'
			WHERE cv.conditionID = c.conditionID
		) as historyDescriptionContains(val)
		where c.conditionID = @conditionID

		set @hasVerbose = 1
	END

	-- exists/not exists
	IF @hasVerbose = 0 and @expression in ('exists','not_exists') BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + e.expressionVerbose
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		where c.conditionID = @conditionID

		set @hasVerbose = 1
	END
	
	-- membertypeID (multi-select not allowed)
	IF @hasVerbose = 0 and @fieldCode = 'm_membertypeid' BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + e.expressionVerbose + ' ' + mt.memberType
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
		INNER JOIN dbo.ams_memberTypes as mt on mt.memberTypeID = cv.conditionValue
		where c.conditionID = @conditionID
		and cvk.conditionKey = 'value'

		set @hasVerbose = 1
	END

	-- stateprov (multi-select allowed)
	IF @hasVerbose = 0 and left(@fieldCode,3) = 'ma_' and right(@fieldCode,10) = '_stateprov' BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + e.expressionVerbose + ' ' + replace(replace(dbo.PipeList(replace(s.Name,'|',char(7))),'|',' OR '),char(7),'|')
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
		INNER JOIN dbo.ams_states as s on cast(s.stateID as varchar(10)) = cv.conditionValue
		where c.conditionID = @conditionID
		and cvk.conditionKey = 'value'
		group by e.expressionVerbose

		set @hasVerbose = 1
	END

	-- country (multi-select allowed)
	IF @hasVerbose = 0 and left(@fieldCode,3) = 'ma_' and right(@fieldCode,8) = '_country' BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + e.expressionVerbose + ' ' + replace(replace(dbo.PipeList(replace(country.country,'|',char(7))),'|',' OR '),char(7),'|')
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
		INNER JOIN dbo.ams_countries as country on cast(country.countryID as varchar(10)) = cv.conditionValue
		where c.conditionID = @conditionID
		and cvk.conditionKey = 'value'
		group by e.expressionVerbose

		set @hasVerbose = 1
	END

	-- districting data (multi-select allowed)
	IF @hasVerbose = 0 and left(@fieldCode,4) = 'mad_' BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + e.expressionVerbose + ' ' + replace(replace(dbo.PipeList(replace(dv.vendorvalue,'|',char(7))),'|',' OR '),char(7),'|')
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
		INNER JOIN dbo.ams_memberDistrictValues as dv on cast(dv.valueid as varchar(10)) = cv.ConditionValue
		where c.conditionID = @conditionID
		and cvk.conditionKey = 'value'
		group by e.expressionVerbose

		set @hasVerbose = 1
	END

	-- prof license status (multi-select allowed)
	IF @hasVerbose = 0 and left(@fieldCode,4) = 'mpl_' and right(@fieldCode,7) = '_status' BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + e.expressionVerbose + ' ' + replace(replace(dbo.PipeList(replace(cv.conditionValue,'|',char(7))),'|',' OR '),char(7),'|')
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
		where c.conditionID = @conditionID
		and cvk.conditionKey = 'value'
		group by e.expressionVerbose

		set @hasVerbose = 1
	END

	-- datediff (multi-select not allowed)
	IF @hasVerbose = 0 and @expression = 'datediff' BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + de.expressionVerbose + ' ' + cast(abs(cv.conditionValue) as varchar(10)) + ' ' + 
			case c.[datepart]
			when 'yy' then 'year'
			when 'q' then 'quarter'
			when 'm' then 'month'
			when 'w' then 'week'
			when 'dw' then 'weekday'
			when 'd' then 'day'
			when 'dy' then 'day of year'
			else 'unknown'
			end + 
			case when cv.conditionValue not in (1,-1) then 's' else '' end + 
			case when cv.conditionValue > 0 then ' in the past' else ' in the future' end
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS de ON c.dateexpressionID = de.expressionID
		INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
		where c.conditionID = @conditionID
		and cvk.conditionKey = 'value'

		set @hasVerbose = 1
	END

	-- datepart (multi-select allowed)
	IF @hasVerbose = 0 and @expression = 'datepart' BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + 
			case c.[datepart]
			when 'yy' then 'year'
			when 'q' then 'quarter'
			when 'm' then 'month'
			when 'w' then 'week'
			when 'dw' then 'weekday'
			when 'd' then 'day'
			when 'dy' then 'day of year'
			else 'unknown'
			end + ' ' + de.expressionVerbose + ' ' + replace(replace(dbo.PipeList(replace(cv.conditionValue,'|',char(7))),'|',' OR '),char(7),'|')
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS de ON c.dateexpressionID = de.expressionID
		INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
		where c.conditionID = @conditionID
		and cvk.conditionKey = 'value'
		group by c.[datepart], de.expressionVerbose

		set @hasVerbose = 1
	END

	-- memberdata SELECT/RADIO - BIT (multi-select not allowed)
	IF @hasVerbose = 0 and left(@fieldCode,3) = 'md_' and @displayTypeCode in ('SELECT','RADIO') and @dataTypeCode = 'BIT' BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + e.expressionVerbose + ' ' + case when cv.ConditionValue = 1 then 'YES' else 'NO' end
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
		where c.conditionID = @conditionID
		and cvk.conditionKey = 'value'

		set @hasVerbose = 1
	END

	-- memberdata SELECT/RADIO (multi-select allowed)
	IF @hasVerbose = 0 and left(@fieldCode,3) = 'md_' and @displayTypeCode in ('SELECT','RADIO') BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + tmp.expressionVerbose + ' ' + replace(replace(dbo.PipeList(replace(tmp.valueVerbose,'|',char(7))),'|',' OR '),char(7),'|')
		from (
			select e.expressionVerbose, 
				valueVerbose = case 
					when @dataTypeCode = 'STRING' then mdcv.columnValueString
					when @dataTypeCode = 'DECIMAL2' then cast(mdcv.columnValueDecimal2 as varchar(max))
					when @dataTypeCode = 'INTEGER' then cast(mdcv.columnValueInteger as varchar(max))
					when @dataTypeCode = 'DATE' then convert(varchar(10),mdcv.columnValueDate,101)
					else cv.conditionValue
				end
			from dbo.ams_virtualGroupConditions as c
			INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
			INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
			INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
			INNER JOIN dbo.ams_memberDataColumnValues as mdcv on cast(mdcv.valueid as varchar(10)) = cv.ConditionValue
			where c.conditionID = @conditionID
			and cvk.conditionKey = 'value'
		) as tmp
		group by tmp.expressionVerbose

		set @hasVerbose = 1
	END

	-- lists
	IF @hasVerbose = 0 and left(@fieldCode,2) = 'l_'  BEGIN
		select top 1 @verbose = 'Member of ' + LEFT(listlist.list, LEN(listlist.list)-1) + ' as ' + LEFT(mtlist.list, LEN(mtlist.list)-1) + 
			case 
			when isnull(listLockAddress.val,'-1') = 0 then  '; locked address equals no'
			when isnull(listLockAddress.val,'-1') = 1 then  '; locked address equals yes'
			else ''
			end +
			case 
			when isnull(listKeepActive.val,'-1') = 0 then  '; keep active equals no'
			when isnull(listKeepActive.val,'-1') = 1 then  '; keep active equals yes'
			else ''
			end +
			case 
				when len(isnull(listJoinDateLower.val,'')) > 0 and len(isnull(listJoinDateUpper.val,'')) > 0 then '; joined between ' + listJoinDateLower.val + ' and ' + listJoinDateUpper.val
				when len(isnull(listJoinDateLower.val,'')) > 0 then '; joined after ' + listJoinDateLower.val
				when len(isnull(listJoinDateUpper.val,'')) > 0 then '; joined before ' + listJoinDateUpper.val
			else ''
			end +
			case 
				when len(isnull(listExpireDateLower.val,'')) > 0 and len(isnull(listExpireDateUpper.val,'')) > 0 then '; expired between ' + listExpireDateLower.val + ' and ' + listExpireDateUpper.val
				when len(isnull(listExpireDateLower.val,'')) > 0 then '; expired after ' + listExpireDateLower.val
				when len(isnull(listExpireDateUpper.val,'')) > 0 then '; expired before ' + listExpireDateUpper.val
			else ''
			end			
		from dbo.ams_virtualGroupConditions as c
		CROSS APPLY ( 
			SELECT cv.conditionValue + ', ' AS [text()] 
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listList'
			WHERE cv.conditionID = c.conditionID
			ORDER BY cv.conditionValue
			FOR XML PATH('')
		) as listlist(list) 
		CROSS APPLY ( 
			SELECT cv.conditionValue + ', ' AS [text()] 
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listMemberType'
			WHERE cv.conditionID = c.conditionID
			ORDER BY cv.conditionValue
			FOR XML PATH('')
		) as mtlist(list) 
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listLockAddress'
			WHERE cv.conditionID = c.conditionID
		) as listLockAddress(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listKeepActive'
			WHERE cv.conditionID = c.conditionID
		) as listKeepActive(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listJoinDateLower'
			WHERE cv.conditionID = c.conditionID
		) as listJoinDateLower(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listJoinDateUpper'
			WHERE cv.conditionID = c.conditionID
		) as listJoinDateUpper(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listExpireDateLower'
			WHERE cv.conditionID = c.conditionID
		) as listExpireDateLower(val)
		OUTER APPLY (
			SELECT cv.conditionValue
			FROM dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'listExpireDateUpper'
			WHERE cv.conditionID = c.conditionID
		) as listExpireDateUpper(val)
		where c.conditionID = @conditionID

		set @hasVerbose = 1
	END

	-- All others
	IF @hasVerbose = 0 BEGIN
		select top 1 @verbose = @fieldLabel + ' ' + e.expressionVerbose + ' ' + replace(replace(dbo.PipeList(replace(cv.conditionValue,'|',char(7))),'|',' OR '),char(7),'|')
		from dbo.ams_virtualGroupConditions as c
		INNER JOIN dbo.ams_virtualGroupExpressions AS e ON c.expressionID = e.expressionID
		INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		INNER JOIN dbo.ams_virtualGroupConditionKeys as cvk on cvk.conditionKeyID = cv.conditionKeyID
		where c.conditionID = @conditionID
		and cvk.conditionKey = 'value'
		group by e.expressionVerbose

		set @hasVerbose = 1
	END

	RETURN @verbose
END
GO

ALTER PROC [dbo].[cache_members_populateMemberConditionCache]
@orgID int,
@conditionIDList varchar(max) = null,
@memberIDList varchar(max) = null,
@processImmediateOnly bit = 1,
@itemGroupUID uniqueidentifier,
@logTreeID uniqueidentifier

as

set nocount on

IF @itemGroupUID is null
	RETURN -1

declare @starttime datetime, @starttime2 datetime, @sql varchar(max), @totalMS int, @totalID int
select @starttime = getdate()
set @conditionIDList = isNull(@conditionIDList,'')
set @memberIDList = isNull(@memberIDList,'')


-- Log
INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage)
VALUES (@logTreeID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'Start Process of orgID=' + cast(@orgID as varchar(10)) + ' conditionID=' + left(@conditionIDList,100) + ' memberID=' + left(@memberIDList,100))


-- split members to calculate
IF OBJECT_ID('tempdb..#tblMembers') IS NOT NULL
	DROP TABLE #tblMembers
CREATE TABLE #tblMembers (memberID int PRIMARY KEY);

IF len(@memberIDList) > 0
	INSERT INTO #tblMembers
	select distinct m.memberID
	from dbo.fn_intListToTable(@memberIDList,',') as tmp
	inner join dbo.ams_members as m on m.memberID = tmp.listitem
	where m.orgID = @orgID
	and m.status <> 'D'
ELSE
	INSERT INTO #tblMembers
	select m.memberID
	from dbo.ams_members as m
	where m.orgID = @orgID
	and m.status <> 'D'

select @totalID = count(*) from #tblMembers 
INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage)
VALUES (@logTreeID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'Found ' + cast(@totalID as varchar(10)) + ' members to process');


-- split conditions to calculate. HONOR THE @processImmediateOnly parameter
IF OBJECT_ID('tempdb..#tblCond') IS NOT NULL
	DROP TABLE #tblCond
CREATE TABLE #tblCond (conditionID int PRIMARY KEY);

IF len(@conditionIDList) > 0
	INSERT INTO #tblCond
	select distinct c.conditionID
	from dbo.fn_intListToTable(@conditionIDList,',') as tmp
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tmp.listitem
	inner join dbo.ams_virtualGroupConditionTypes as ct on ct.conditionTypeID = c.conditionTypeID
	where c.orgID = @orgID
	and c.isDefined = 1
	and 1 = case 
		when @processImmediateOnly = 1 and ct.processImmediately = 0 then 0
		else 1 end
ELSE
	INSERT INTO #tblCond
	select c.conditionID
	from dbo.ams_virtualGroupConditions as c
	inner join dbo.ams_virtualGroupConditionTypes as ct on ct.conditionTypeID = c.conditionTypeID
	where c.orgID = @orgID
	and c.isDefined = 1	
	and 1 = case 
		when @processImmediateOnly = 1 and ct.processImmediately = 0 then 0
		else 1 end


-- get all conditions to calculate
IF OBJECT_ID('tempdb..#tblCondALL') IS NOT NULL
	DROP TABLE #tblCondALL
CREATE TABLE #tblCondALL (conditionID int PRIMARY KEY, orgID int, expression varchar(20), fieldCode varchar(40), fieldCodeArea varchar(25), 
	displayTypeCode varchar(20), dataTypeCode varchar(20), fieldCodeAreaID int, fieldCodeAreaPartA varchar(20), subProc varchar(30));

INSERT INTO #tblCondALL
select c.conditionID, c.orgID, e.expression, c.fieldCode, fieldCodeArea = case
	when left(c.fieldCode,2) = 'm_' then 'Member Data'	
	when left(c.fieldCode,3) = 'md_' then 'Custom Fields'	
	when left(c.fieldCode,3) = 'ma_' then 'Addresses'	
	when left(c.fieldCode,3) = 'mp_' then 'Phones'	
	when left(c.fieldCode,4) = 'mad_' then 'Districting'	
	when left(c.fieldCode,3) = 'me_' then 'Emails'	
	when left(c.fieldCode,3) = 'mw_' then 'Websites'	
	when left(c.fieldCode,2) = 'e_' then 'Events'	
	when left(c.fieldcode,4) = 'mpl_' then 'Professional Licenses'	
	when left(c.fieldcode,4) = 'grp_' then 'Groups'	-- excluded in where clause
	when left(c.fieldcode,4) = 'sub_' then 'Subscriptions'	
	when left(c.fieldcode,3) = 'rt_' then 'Record Types'	
	when left(c.fieldcode,5) = 'acct_' then 'Accounting'	
	when left(c.fieldcode,3) = 'mh_' then 'Member History'	
	when left(c.fieldcode,2) = 'l_' then 'Listserver Memberships'
	end, dit.displayTypeCode, dat.dataTypeCode,
	fieldCodeAreaID = case 
	when left(c.fieldCode,3) = 'md_' then cast(replace(c.fieldcode,'md_','') as int)
	when left(c.fieldcode,4) = 'mpl_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,3) = 'ma_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,2) = 'e_' then cast(replace(c.fieldcode,'e_','') as int)
	when left(c.fieldCode,3) = 'me_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,3) = 'mw_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,3) = 'mp_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as int)
	when left(c.fieldCode,4) = 'mad_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as int)
	else null end,
	fieldCodeAreaPartA = case
	when left(c.fieldcode,4) = 'mpl_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as varchar(20))
	when left(c.fieldCode,3) = 'ma_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as varchar(20))
	when left(c.fieldCode,3) = 'mp_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as varchar(10))
	when left(c.fieldCode,4) = 'mad_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as varchar(10))
	else null end,
	subProc = ''
from dbo.ams_virtualGroupConditions as c
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.expressionID
inner join dbo.ams_memberDataColumnDataTypes as dat on dat.dataTypeID = c.dataTypeID
inner join dbo.ams_memberDataColumnDisplayTypes as dit on dit.displayTypeID = c.displayTypeID
inner join #tblCond as tblC on tblc.conditionID = c.conditionID
where left(c.fieldCode,4) <> 'grp_'

-- add indexes for speed
CREATE INDEX IX_tblCondALL_areaid ON #tblCondALL (fieldCodeAreaID asc);
CREATE INDEX IX_tblCondALL_areaparta ON #tblCondALL (fieldCodeAreaPartA asc);
CREATE NONCLUSTERED INDEX IX_tblCondALL_DTA2 ON #tblCondALL (fieldCodeArea ASC, expression ASC, dataTypeCode ASC);

-- update subProcs to run
update #tblCondALL
set subProc = case 
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='between' then 'ACCT_BETWEEN_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='eq' then 'ACCT_EQ_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='exists' then 'ACCT_EXISTS_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='gt' then 'ACCT_GT_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='gte' then 'ACCT_GTE_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='lt' then 'ACCT_LT_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='lte' then 'ACCT_LTE_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='neq' then 'ACCT_NEQ_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='not_exists' then 'ACCT_NOTEXISTS_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsumrecog' and expression='between' then 'ACCT_BETWEEN_ALLOCSUMRECOG'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsumrecog' and expression='eq' then 'ACCT_EQ_ALLOCSUMRECOG'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsumrecog' and expression='exists' then 'ACCT_EXISTS_ALLOCSUMRECOG'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsumrecog' and expression='gt' then 'ACCT_GT_ALLOCSUMRECOG'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsumrecog' and expression='gte' then 'ACCT_GTE_ALLOCSUMRECOG'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsumrecog' and expression='lt' then 'ACCT_LT_ALLOCSUMRECOG'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsumrecog' and expression='lte' then 'ACCT_LTE_ALLOCSUMRECOG'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsumrecog' and expression='neq' then 'ACCT_NEQ_ALLOCSUMRECOG'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsumrecog' and expression='not_exists' then 'ACCT_NOTEXISTS_ALLOCSUMRECOG'
	when fieldCodeArea='Addresses' and expression='contains' then 'MA_CONTAINS'
	when fieldCodeArea='Addresses' and expression='contains_regex' then 'MA_CONTAINSREGEX'
	when fieldCodeArea='Addresses' and expression='eq' then 'MA_EQ'
	when fieldCodeArea='Addresses' and expression='exists' then 'MA_EXISTS'
	when fieldCodeArea='Addresses' and expression='gt' then 'MA_GT'
	when fieldCodeArea='Addresses' and expression='gte' then 'MA_GTE'
	when fieldCodeArea='Addresses' and expression='lt' then 'MA_LT'
	when fieldCodeArea='Addresses' and expression='lte' then 'MA_LTE'
	when fieldCodeArea='Addresses' and expression='neq' then 'MA_NEQ'
	when fieldCodeArea='Addresses' and expression='not_exists' then 'MA_NOTEXISTS'
	when fieldCodeArea='Custom Fields' and expression='datediff' then 'MD_DATEDIFF'
	when fieldCodeArea='Custom Fields' and expression='datepart' then 'MD_DATEPART'
	when fieldCodeArea='Custom Fields' and expression='contains' and dataTypeCode='STRING' then 'MD_CONTAINS_STRING'
	when fieldCodeArea='Custom Fields' and expression='contains_regex' and dataTypeCode='STRING' then 'MD_CONTAINSREGEX_STRING'
	when fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='STRING' then 'MD_EQ_STRING'
	when fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='BIT' then 'MD_EQ_BIT'
	when fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='INTEGER' then 'MD_EQ_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='DECIMAL2' then 'MD_EQ_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='DATE' then 'MD_EQ_DATE'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='STRING' then 'MD_EXISTS_STRING'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='BIT' then 'MD_EXISTS_BIT'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='INTEGER' then 'MD_EXISTS_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='DECIMAL2' then 'MD_EXISTS_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='DATE' then 'MD_EXISTS_DATE'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='CONTENTOBJ' then 'MD_EXISTS_CONTENTOBJ'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='DOCUMENTOBJ' then 'MD_EXISTS_DOCUMENTOBJ'
	when fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='STRING' then 'MD_GT_STRING'
	when fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='INTEGER' then 'MD_GT_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='DECIMAL2' then 'MD_GT_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='DATE' then 'MD_GT_DATE'
	when fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='STRING' then 'MD_GTE_STRING'
	when fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='INTEGER' then 'MD_GTE_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='DECIMAL2' then 'MD_GTE_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='DATE' then 'MD_GTE_DATE'
	when fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='STRING' then 'MD_LT_STRING'
	when fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='INTEGER' then 'MD_LT_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='DECIMAL2' then 'MD_LT_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='DATE' then 'MD_LT_DATE'
	when fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='STRING' then 'MD_LTE_STRING'
	when fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='INTEGER' then 'MD_LTE_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='DECIMAL2' then 'MD_LTE_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='DATE' then 'MD_LTE_DATE'
	when fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='STRING' then 'MD_NEQ_STRING'
	when fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='BIT' then 'MD_NEQ_BIT'
	when fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='INTEGER' then 'MD_NEQ_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='DECIMAL2' then 'MD_NEQ_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='DATE' then 'MD_NEQ_DATE'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='STRING' then 'MD_NOTEXISTS_STRING'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='INTEGER' then 'MD_NOTEXISTS_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='DECIMAL2' then 'MD_NOTEXISTS_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='DATE' then 'MD_NOTEXISTS_DATE'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='BIT' then 'MD_NOTEXISTS_BIT'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='CONTENTOBJ' then 'MD_NOTEXISTS_CONTENTOBJ'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='DOCUMENTOBJ' then 'MD_NOTEXISTS_DOCUMENTOBJ'
	when fieldCodeArea='Districting' and expression='eq' then 'MAD_EQ'
	when fieldCodeArea='Districting' and expression='exists' then 'MAD_EXISTS'
	when fieldCodeArea='Districting' and expression='neq' then 'MAD_NEQ'
	when fieldCodeArea='Districting' and expression='not_exists' then 'MAD_NOTEXISTS'
	when fieldCodeArea='Emails' and expression='contains' then 'ME_CONTAINS'
	when fieldCodeArea='Emails' and expression='contains_regex' then 'ME_CONTAINSREGEX'
	when fieldCodeArea='Emails' and expression='eq' then 'ME_EQ'
	when fieldCodeArea='Emails' and expression='exists' then 'ME_EXISTS'
	when fieldCodeArea='Emails' and expression='gt' then 'ME_GT'
	when fieldCodeArea='Emails' and expression='gte' then 'ME_GTE'
	when fieldCodeArea='Emails' and expression='lt' then 'ME_LT'
	when fieldCodeArea='Emails' and expression='lte' then 'ME_LTE'
	when fieldCodeArea='Emails' and expression='neq' then 'ME_NEQ'
	when fieldCodeArea='Emails' and expression='not_exists' then 'ME_NOTEXISTS'
	when fieldCodeArea='Events' and expression='registered' then 'E_REGISTERED'
	when fieldCodeArea='Events' and expression='attended' then 'E_ATTENDED'
	when fieldCodeArea='Events' and expression='awarded' then 'E_AWARDED'
	when fieldCodeArea='Listserver Memberships' and fieldCode='l_entry' and expression='onlist' then 'L_ONLIST'
	when fieldCodeArea='Member Data' and expression='contains' then 'M_CONTAINS'
	when fieldCodeArea='Member Data' and expression='contains_regex' then 'M_CONTAINSREGEX'
	when fieldCodeArea='Member Data' and expression='eq' and dataTypeCode='STRING' then 'M_EQ_STRING'
	when fieldCodeArea='Member Data' and expression='eq' and dataTypeCode='INTEGER' then 'M_EQ_INTEGER'
	when fieldCodeArea='Member Data' and expression='exists' then 'M_EXISTS'
	when fieldCodeArea='Member Data' and expression='gt' then 'M_GT'
	when fieldCodeArea='Member Data' and expression='gte' then 'M_GTE'
	when fieldCodeArea='Member Data' and expression='lt' then 'M_LT'
	when fieldCodeArea='Member Data' and expression='lte' then 'M_LTE'
	when fieldCodeArea='Member Data' and expression='neq' and dataTypeCode='STRING' then 'M_NEQ_STRING'
	when fieldCodeArea='Member Data' and expression='neq' and dataTypeCode='INTEGER' then 'M_NEQ_INTEGER'
	when fieldCodeArea='Member Data' and expression='not_exists' then 'M_NOTEXISTS'
	when fieldCodeArea='Member History' and fieldCode='mh_entry' and expression='exists' then 'MH_EXISTS'
	when fieldCodeArea='Phones' and expression='contains' then 'MP_CONTAINS'
	when fieldCodeArea='Phones' and expression='contains_regex' then 'MP_CONTAINSREGEX'
	when fieldCodeArea='Phones' and expression='eq' then 'MP_EQ'
	when fieldCodeArea='Phones' and expression='exists' then 'MP_EXISTS'
	when fieldCodeArea='Phones' and expression='gt' then 'MP_GT'
	when fieldCodeArea='Phones' and expression='gte' then 'MP_GTE'
	when fieldCodeArea='Phones' and expression='lt' then 'MP_LT'
	when fieldCodeArea='Phones' and expression='lte' then 'MP_LTE'
	when fieldCodeArea='Phones' and expression='neq' then 'MP_NEQ'
	when fieldCodeArea='Phones' and expression='not_exists' then 'MP_NOTEXISTS'
	when fieldCodeArea='Professional Licenses' and expression='contains' then 'MPL_CONTAINS'
	when fieldCodeArea='Professional Licenses' and expression='contains_regex' then 'MPL_CONTAINSREGEX'
	when fieldCodeArea='Professional Licenses' and expression='datepart' then 'MPL_DATEPART'
	when fieldCodeArea='Professional Licenses' and expression='datediff' then 'MPL_DATEDIFF'
	when fieldCodeArea='Professional Licenses' and expression='eq' and dataTypeCode='DATE' then 'MPL_EQ_DATE'
	when fieldCodeArea='Professional Licenses' and expression='eq' and dataTypeCode='STRING' then 'MPL_EQ_STRING'
	when fieldCodeArea='Professional Licenses' and expression='exists' and dataTypeCode='DATE' then 'MPL_EXISTS_DATE'
	when fieldCodeArea='Professional Licenses' and expression='exists' and dataTypeCode='STRING' then 'MPL_EXISTS_STRING'
	when fieldCodeArea='Professional Licenses' and expression='gt' and dataTypeCode='DATE' then 'MPL_GT_DATE'
	when fieldCodeArea='Professional Licenses' and expression='gt' and dataTypeCode='STRING' then 'MPL_GT_STRING'
	when fieldCodeArea='Professional Licenses' and expression='gte' and dataTypeCode='DATE' then 'MPL_GTE_DATE'
	when fieldCodeArea='Professional Licenses' and expression='gte' and dataTypeCode='STRING' then 'MPL_GTE_STRING'
	when fieldCodeArea='Professional Licenses' and expression='lt' and dataTypeCode='DATE' then 'MPL_LT_DATE'
	when fieldCodeArea='Professional Licenses' and expression='lt' and dataTypeCode='STRING' then 'MPL_LT_STRING'
	when fieldCodeArea='Professional Licenses' and expression='lte' and dataTypeCode='DATE' then 'MPL_LTE_DATE'
	when fieldCodeArea='Professional Licenses' and expression='lte' and dataTypeCode='STRING' then 'MPL_LTE_STRING'
	when fieldCodeArea='Professional Licenses' and expression='neq' and dataTypeCode='DATE' then 'MPL_NEQ_DATE'
	when fieldCodeArea='Professional Licenses' and expression='neq' and dataTypeCode='STRING' then 'MPL_NEQ_STRING'
	when fieldCodeArea='Professional Licenses' and expression='not_exists' and dataTypeCode='DATE' then 'MPL_NOTEXISTS_DATE'
	when fieldCodeArea='Professional Licenses' and expression='not_exists' and dataTypeCode='STRING' then 'MPL_NOTEXISTS_STRING'
	when fieldCodeArea='Record Types' and fieldCode='rt_role' and expression='linked' then 'RT_LINKED'
	when fieldCodeArea='Subscriptions' and expression='subscribed' then 'SUB_SUBSCRIBED'
	when fieldCodeArea='Websites' and expression='contains' then 'MW_CONTAINS'
	when fieldCodeArea='Websites' and expression='contains_regex' then 'MW_CONTAINSREGEX'
	when fieldCodeArea='Websites' and expression='eq' then 'MW_EQ'
	when fieldCodeArea='Websites' and expression='exists' then 'MW_EXISTS'
	when fieldCodeArea='Websites' and expression='gt' then 'MW_GT'
	when fieldCodeArea='Websites' and expression='gte' then 'MW_GTE'
	when fieldCodeArea='Websites' and expression='lt' then 'MW_LT'
	when fieldCodeArea='Websites' and expression='lte' then 'MW_LTE'
	when fieldCodeArea='Websites' and expression='neq' then 'MW_NEQ'
	when fieldCodeArea='Websites' and expression='not_exists' then 'MW_NOTEXISTS'
	else ''
	end

-- for the final processing at the end
IF OBJECT_ID('tempdb..#tblCondALLFinal') IS NOT NULL
	DROP TABLE #tblCondALLFinal
CREATE TABLE #tblCondALLFinal (conditionID int PRIMARY KEY);

insert into #tblCondALLFinal
select conditionID from #tblCondALL


-- put condition values into temp table by datatype to remove all casting in individual processing queries
IF OBJECT_ID('tempdb..#tblCondValues') IS NOT NULL
	DROP TABLE #tblCondValues
CREATE TABLE #tblCondValues (conditionID int, conditionKeyID int, conditionValueString varchar(max), conditionValueInteger int, conditionValueBit bit, conditionValueDecimal2 decimal(9,2), conditionValueDate datetime);

insert into #tblCondValues
select cv.conditionID, cv.conditionKeyID, null, cv.conditionValue, null, null, null
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where 
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'STRING' and tblc.displayTypeCode in ('RADIO','SELECT')) or
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'DECIMAL2' and tblc.displayTypeCode in ('RADIO','SELECT')) or 
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'DATE' and tblc.displayTypeCode in ('RADIO','SELECT')) or 
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq','lt','lte','gt','gte') and tblc.dataTypeCode = 'INTEGER') or
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('datepart','datediff')) or
	(tblc.fieldCodeArea = 'Addresses' and tblc.expression in ('eq','neq') and tblc.displayTypeCode in ('RADIO','SELECT')) or
	(tblc.fieldCodeArea = 'Professional Licenses' and tblc.expression in ('datepart','datediff')) or
	(tblc.fieldCodeArea = 'Member Data' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'INTEGER') or
	(tblc.fieldCodeArea = 'Districting' and tblc.expression in ('eq','neq'))
	union all
select cv.conditionID, cv.conditionKeyID, cv.conditionValue, null, null, null, null
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'STRING' and tblc.displayTypeCode not in ('RADIO','SELECT')) or
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('lt','lte','gt','gte','contains','contains_regex') and tblc.dataTypeCode = 'STRING') or
	(tblc.fieldCodeArea = 'Addresses' and tblc.expression in ('eq','neq') and tblc.displayTypeCode not in ('RADIO','SELECT')) or
	(tblc.fieldCodeArea = 'Addresses' and tblc.expression in ('lt','lte','gt','gte','contains','contains_regex')) or
	(tblc.fieldCodeArea = 'Professional Licenses' and tblc.expression in ('eq','neq','lt','lte','gt','gte','contains','contains_regex') and tblc.dataTypeCode = 'STRING') or
	(tblc.fieldCodeArea = 'Member Data' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'STRING') or
	(tblc.fieldCodeArea = 'Member Data' and tblc.expression in ('lt','lte','gt','gte','contains','contains_regex')) or
	(tblc.fieldCodeArea = 'Websites') or
	(tblc.fieldCodeArea = 'Emails') or
	(tblc.fieldCodeArea = 'Phones')
	union all
select cv.conditionID, cv.conditionKeyID, null, null, cv.conditionValue, null, null
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where (tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'BIT')
	union all
select cv.conditionID, cv.conditionKeyID, null, null, null, cv.conditionValue, null
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'DECIMAL2' and tblc.displayTypeCode not in ('RADIO','SELECT')) or
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('lt','lte','gt','gte') and tblc.dataTypeCode = 'DECIMAL2')
	union all
select cv.conditionID, cv.conditionKeyID, null, null, null, null, cv.conditionValue
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'DATE' and tblc.displayTypeCode not in ('RADIO','SELECT')) or
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('lt','lte','gt','gte') and tblc.dataTypeCode = 'DATE') or
	(tblc.fieldCodeArea = 'Professional Licenses' and tblc.expression in ('eq','neq','lt','lte','gt','gte') and tblc.dataTypeCode = 'DATE')

CREATE INDEX IX_tblCondValues_conditionID ON #tblCondValues (conditionID asc);


-- split ACCT data if necessary. This is here because several subProcs can use this data.
declare @acctCount int
select @acctCount = count(*) from #tblCondALL where fieldCodeArea = 'Accounting'
IF @acctCount > 0 BEGIN
	IF OBJECT_ID('tempdb..#tblAccSplit') IS NOT NULL
		DROP TABLE #tblAccSplit
	CREATE TABLE #tblAccSplit (conditionID int, revenueGLs varchar(max), batchDateLower datetime, batchDateUpper datetime, 
		revOrCash varchar(7), conditionValue money, conditionValueLower money, conditionValueUpper money);

	IF OBJECT_ID('tempdb..#tblAccSplitGL') IS NOT NULL
		DROP TABLE #tblAccSplitGL
	CREATE TABLE #tblAccSplitGL (conditionID int, revenueGL int);

	EXEC dbo.cache_members_populateMemberConditionCache_splitAcct
END


-- loop over subProcs to run
IF OBJECT_ID('tempdb..#cache_members_conditions_shouldbe') IS NOT NULL
	DROP TABLE #cache_members_conditions_shouldbe
CREATE TABLE #cache_members_conditions_shouldbe (memberid int, conditionID int);

declare @subProc varchar(30), @dynsql nvarchar(100)
select @subProc = min(subProc) from #tblCondALL where subProc <> ''
while @subProc is not null BEGIN
	SET @dynsql = 'EXEC dbo.cache_members_populateMemberConditionCache_' + @subProc
	EXEC sp_executesql @dynsql

	delete from #tblCondALL where subProc = @subProc

	select @subProc = min(subProc) from #tblCondALL where subProc <> '' and subProc > @subProc
END


-- Log
set @starttime2 = getdate()
INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage)
VALUES (@logTreeID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'Start changes to real tables');

-- delete member/conditions that should not be there
delete cmc
from dbo.cache_members_conditions as cmc
inner join #tblCondALLFinal as caf on caf.conditionID = cmc.conditionID
inner join #tblMembers as m on m.memberid = cmc.memberid
and not exists (
	select *
	from #cache_members_conditions_shouldbe
	where conditionID = cmc.conditionID
	and memberid = cmc.memberid
)	
		
-- insert member/conditions that should be but arent already there
insert into dbo.cache_members_conditions (memberID, conditionID)
select distinct cache.memberID, cache.conditionID
from #cache_members_conditions_shouldbe as cache
where not exists (
	select memberid, conditionid
	from dbo.cache_members_conditions
	where memberid = cache.memberid
	and conditionID = cache.conditionID
)

-- log
INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'End changes to real tables', Datediff(ms,@starttime2,getdate()));

-- return query of members affected
select distinct memberID
from #tblMembers
	
-- cleanup temp tables
IF OBJECT_ID('tempdb..#cache_members_conditions_shouldbe') IS NOT NULL
	DROP TABLE #cache_members_conditions_shouldbe
IF OBJECT_ID('tempdb..#tblCond') IS NOT NULL
	DROP TABLE #tblCond
IF OBJECT_ID('tempdb..#tblCondALL') IS NOT NULL
	DROP TABLE #tblCondALL
IF OBJECT_ID('tempdb..#tblCondALLFinal') IS NOT NULL
	DROP TABLE #tblCondALLFinal
IF OBJECT_ID('tempdb..#tblAccSplit') IS NOT NULL
	DROP TABLE #tblAccSplit
IF OBJECT_ID('tempdb..#tblAccSplitGL') IS NOT NULL
	DROP TABLE #tblAccSplitGL
IF OBJECT_ID('tempdb..#tblCondValues') IS NOT NULL
	DROP TABLE #tblCondValues
IF OBJECT_ID('tempdb..#tblMembers') IS NOT NULL
	DROP TABLE #tblMembers

-- Log
set @totalMS = Datediff(ms,@starttime,getdate())
INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'End Process of orgID=' + cast(@orgID as varchar(10)) + ' conditionID=' + left(@conditionIDList,100) + ' memberID=' + left(@memberIDList,100), @totalMS);

set nocount off
GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_ACCT_BETWEEN_ALLOCSUMRECOG
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValueLower, accsplit.conditionValueUpper
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_BETWEEN_ALLOCSUMRECOG'
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValueLower, accsplit.conditionValueUpper
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_BETWEEN_ALLOCSUMRECOG'
) as tmp
group by memberid, conditionID, conditionValueLower, conditionValueUpper
having sum(allocAmt) between conditionValueLower and conditionValueUpper

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_ACCT_EQ_ALLOCSUMRECOG
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_EQ_ALLOCSUMRECOG'	
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_EQ_ALLOCSUMRECOG'	
) as tmp
group by memberid, conditionID, conditionValue
having sum(allocAmt) = conditionValue

GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_ACCT_EXISTS_ALLOCSUMRECOG
AS

insert into #cache_members_conditions_shouldbe
select m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
inner join #tblMembers as m on m.memberid = m2.activeMemberID
inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
where tblc.subProc = 'ACCT_EXISTS_ALLOCSUMRECOG'
	union
select m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
inner join #tblMembers as m on m.memberid = m2.activeMemberID
inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
where tblc.subProc = 'ACCT_EXISTS_ALLOCSUMRECOG' 

GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_ACCT_GTE_ALLOCSUMRECOG
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_GTE_ALLOCSUMRECOG'	
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_GTE_ALLOCSUMRECOG'	
) as tmp
group by memberid, conditionID, conditionValue
having sum(allocAmt) >= conditionValue

GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_ACCT_GT_ALLOCSUMRECOG
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_GT_ALLOCSUMRECOG'	
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_GT_ALLOCSUMRECOG'	
) as tmp
group by memberid, conditionID, conditionValue
having sum(allocAmt) > conditionValue

GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_ACCT_LTE_ALLOCSUMRECOG
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_LTE_ALLOCSUMRECOG'	
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_LTE_ALLOCSUMRECOG'	
) as tmp
group by memberid, conditionID, conditionValue
having sum(allocAmt) <= conditionValue

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_ACCT_LT_ALLOCSUMRECOG
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_LT_ALLOCSUMRECOG'	
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_LT_ALLOCSUMRECOG'	
) as tmp
group by memberid, conditionID, conditionValue
having sum(allocAmt) < conditionValue

GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_ACCT_NEQ_ALLOCSUMRECOG
AS

insert into #cache_members_conditions_shouldbe
select distinct memberid, conditionID
from (
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount else allocT.amount*-1 end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_NEQ_ALLOCSUMRECOG'
		union all
	select m.memberid, tblc.conditionID, case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then allocT.amount*-1 else allocT.amount end as allocAmt, accsplit.conditionValue
	from #tblCondALL as tblc
	inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
	inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
	inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
	inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
	inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
	inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
	inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
	inner join #tblMembers as m on m.memberid = m2.activeMemberID
	inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
	where tblc.subProc = 'ACCT_NEQ_ALLOCSUMRECOG'	
) as tmp
group by memberid, conditionID, conditionValue
having sum(allocAmt) <> conditionValue

GO


CREATE PROC dbo.cache_members_populateMemberConditionCache_ACCT_NOTEXISTS_ALLOCSUMRECOG
AS

IF OBJECT_ID('tempdb..#tblCondAccNotExist') IS NOT NULL
	DROP TABLE #tblCondAccNotExist

select m.memberid, tblc.conditionID
into #tblCondAccNotExist
from #tblCondALL as tblc
inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
inner join dbo.tr_transactions as allocT on allocT.ownedByOrgID = tblc.orgID and allocT.typeID = 5
inner join dbo.tr_batchTransactions as bt on bt.transactionID = allocT.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
inner join #tblMembers as m on m.memberid = m2.activeMemberID
inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
where tblc.subProc = 'ACCT_NOTEXISTS_ALLOCSUMRECOG' 
	union
select m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblAccSplit as accsplit on accsplit.conditionID = tblc.conditionID
inner join dbo.tr_transactions as VOT on VOT.ownedByOrgID = tblc.orgID and VOT.typeID = 8
inner join dbo.tr_batchTransactions as bt on bt.transactionID = VOT.transactionID
inner join dbo.tr_batches as b on b.batchID = bt.batchID and b.statusID = 4 and b.depositDate between accsplit.batchDateLower and accsplit.batchDateUpper
inner join dbo.tr_relationships as VOR on VOR.transactionID = VOT.transactionID and VOR.typeID = 8
inner join dbo.tr_transactions as allocT on allocT.transactionID = VOR.appliedToTransactionID and allocT.typeID = 5
inner join dbo.tr_relationships as allocR on allocR.transactionID = allocT.transactionID
inner join dbo.tr_relationshipTypes as allocRT on allocRT.typeID = allocR.typeID and allocRT.type = 'AllocSaleTrans'
inner join dbo.tr_transactions as saleT on saleT.transactionID = allocR.appliedToTransactionID
inner join dbo.ams_members as m2 on m2.memberid = case when accsplit.revOrCash = 'cash' then allocT.assignedToMemberID else saleT.assignedToMemberID end
inner join #tblMembers as m on m.memberid = m2.activeMemberID
inner join #tblAccSplitGL as gl on gl.revenueGL = saleT.creditGLAccountID and gl.conditionID = tblc.conditionID
inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = allocT.creditGLAccountID
where tblc.subProc = 'ACCT_NOTEXISTS_ALLOCSUMRECOG' 

insert into #cache_members_conditions_shouldbe
select distinct mOuter.memberid, tblcOuter.conditionID
from #tblCondALL as tblcOuter
inner join #tblMembers as mOuter on mOuter.memberid = mOuter.memberID
where tblcOuter.subProc = 'ACCT_NOTEXISTS_ALLOCSUMRECOG' 
	except
select memberid, conditionID
from #tblCondAccNotExist

IF OBJECT_ID('tempdb..#tblCondAccNotExist') IS NOT NULL
	DROP TABLE #tblCondAccNotExist

GO


