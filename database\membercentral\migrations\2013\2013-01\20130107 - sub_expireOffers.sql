ALTER PROCEDURE [dbo].[sub_expireOffers] 
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @dtStart DATETIME, @dtEnd DATETIME, @dateToUse DATETIME
	select @dateToUse = dateadd(DAY, -1, getdate())
	SELECT @dtStart = dateadd(DAY, datediff(DAY, 0, @dateToUse), 0) 
	SELECT @dtEnd = dateadd(SECOND, -1, dateadd(DAY, datediff(DAY, 0, @dateToUse)+1, 0))

	IF OBJECT_ID('tempdb..##tempExpireOfferJob') IS NOT NULL 
		DROP TABLE ##tempExpireOfferJob

	select t.siteID, sc.subscriberID
	INTO ##tempExpireOfferJob
	from dbo.sub_subscribers sc
	inner join dbo.sub_subscriptions s on s.subscriptionID = sc.subscriptionID
	inner join dbo.sub_types t on t.typeID = s.typeID
	inner join dbo.sites st on st.siteID = t.siteID
	inner join dbo.sub_statuses ss on ss.statusID = sc.statusID and ss.statusCode in ('R','O')
	where (sc.offerRescindDate is not null and sc.offerRescindDate <= @dtEnd)

	declare @minSubscriberID int, @currTempResult int, @currSiteID int, @subXOStatusCode varchar(1)
	
	select @subXOStatusCode=statusCode
	from dbo.sub_statuses 
	where statusCode = 'X'

	select @minSubscriberID = min(subscriberID) from ##tempExpireOfferJob
	while @minSubscriberID is not null BEGIN
		select @currSiteID=siteID
		from ##tempExpireOfferJob
		where subscriberID = @minSubscriberID
		
		EXEC dbo.sub_updateSubscriberStatus @subscriberID=@minSubscriberID,
											@newStatusCode=@subXOStatusCode,
											@siteID=@currSiteID,
											@enteredByMemberID=21,
											@result=@currTempResult OUTPUT 

		
		select @minSubscriberID = min(subscriberID)
		from ##tempExpireOfferJob
		where subscriberID > @minSubscriberID
	END

	IF OBJECT_ID('tempdb..##tempExpireOfferJob') IS NOT NULL 
		DROP TABLE ##tempExpireOfferJob
END
