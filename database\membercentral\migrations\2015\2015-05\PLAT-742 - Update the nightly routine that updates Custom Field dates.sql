/*
In this SQL file the following DB object are being altered:

(1) SP sub_updateSubscriberStatus
(2) SP sub_expireSubscriptions
(3) SP sub_checkActivations
(4) SP sub_updateSubscriberStatusBulk

*/

use membercentral
GO

ALTER PROCEDURE [dbo].[sub_updateSubscriberStatus] 
	@subscriberID int,
	@newStatusCode varchar(1),
	@siteID int,
	@enteredByMemberID int,
	@updateDate datetime = NULL,
	@result int OUTPUT
AS

declare @itemGroupUID uniqueidentifier

select @enteredByMemberID = nullif(@enteredByMemberID,0)

/*
	Results:
	0 - nothing changed
	1 - successfully changed
	-1 - error occured
	-2 - status not found

	Statuses:
	A - Active
	I - Inactive
	R - Renewal Not Sent
	O - Billed
	P - Accepted
	E - Expired
	X - Offer Expired
	D - Deleted
*/

declare @orgID int, @currStatusID int, @currStatusCode varchar(1), @currPaymentStatusCode varchar(1), @currSubscriptionID int, @currSubStartDate DATETIME, @currSubEndDate DATETIME, @memberID int, @activeMemberID int, @prevSubscriberID int, @minSubscriberID int, @insideResult int
declare @newStatusID int, @enteredByActiveMemberID int, @tempCount int, @tempID int, @tempAcceptedStatusID int, @endDate DATETIME
declare @tempEndDate DATETIME, @tempGraceEndDate DATETIME
declare @loopCurrSubActiveGroupID int, @loopCurrSubWaitingGroupID int, @loopCurrSubInactiveGroupID int, @loopCurrSubPendingGroupID int, @loopCurrSubRenewGroupID int
declare @tempUpdateSubscriberStatus table(subscriberID int, subscriptionID int, status varchar(1), subStartDate datetime, subEndDate datetime, graceEndDate datetime, parentSubscriberID int)
declare @tempUpdatePrevSubscriberStatus table(subscriberID int, subscriptionID int, status varchar(1), subStartDate datetime, subEndDate datetime, graceEndDate datetime, parentSubscriberID int)
declare @tmpDate datetime

select @result = 0
select @endDate = getDate()
select @orgID=orgID from dbo.sites where siteID = @siteID
select @newStatusID=statusID from dbo.sub_statuses where statusCode = @newStatusCode
	
/* make sure we have the active memberID for the member changing the status */
select @enteredByActiveMemberID=dbo.fn_getActiveMemberID(@enteredByMemberID)

if @newStatusID is null
	goto on_error

if @updateDate is null
	set @updateDate =  getDate()

/* cannot return from children from a status of D or E */
select @currSubscriptionID=s.subscriptionID, @memberID=s.memberID, @activeMemberID=m.activeMemberID, @currSubStartDate=s.subStartDate,@currSubEndDate=s.subEndDate, 
	@currStatusID=s.statusID, @currStatusCode=ss.statusCode, @currPaymentStatusCode=ps.statusCode
from dbo.sub_subscribers s
inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID 
inner join dbo.sub_types t on t.typeID = subs.typeID and t.siteID = @siteID	
inner join dbo.sub_statuses ss on ss.statusID = s.statusID
inner join dbo.sub_paymentStatuses ps on ps.statusID = s.paymentStatusID
inner join dbo.ams_members m on m.memberID = s.memberID
where s.subscriberID = @subscriberID

IF @memberID is null
	goto on_error

insert into @tempUpdateSubscriberStatus
select subscriberID, subscriptionID, status, subStartDate, subEndDate, graceEndDate, parentSubscriberID
from dbo.fn_getRecursiveMemberSubscriptions(@memberID, @siteID, @subscriberID) grms
where grms.status not in ('E', 'D')
and grms.subscriberID <> @subscriberID

IF @newStatusCode = 'A'
BEGIN

	IF @currStatusCode is not null
	BEGIN

		IF @currStatusCode = 'I'
		BEGIN
			/*
				print 'Inactive to Active'
				update the subscription that was sent, 
				put in active or waiting group, based on payment status
				check if member has a subscription of same ID that is inactive or expired.  If not, remove from inactive group
				then loop over the others to call status update
			*/
			update dbo.sub_subscribers
			set statusID = @newStatusID
			where subscriberID = @subscriberID
					
			insert into dbo.sub_statusHistory (subscriberID, oldStatusID, statusID, enteredByMemberID, updateDate)
			values(@subscriberID, @currStatusID, @newStatusID, @enteredByActiveMemberID, @updateDate)

			IF @currPaymentStatusCode = 'N'
			BEGIN
				-- update groups.  Add to active, would also check on payment status
				select @loopCurrSubWaitingGroupID=null
				select @loopCurrSubWaitingGroupID=groupID
				from dbo.ams_groups
				where groupCode = 'subWaitingPay_' + convert(varchar, @currSubscriptionID) + '_tracking'

				IF @loopCurrSubWaitingGroupID is not null
					exec dbo.ams_createMemberGroup @memberID, @loopCurrSubWaitingGroupID, 1
			END
			ELSE
			BEGIN					
				-- update groups.  Add to active, would also check on payment status
				select @loopCurrSubActiveGroupID=null
				select @loopCurrSubActiveGroupID=groupID
				from dbo.ams_groups
				where groupCode = 'subActive_' + convert(varchar, @currSubscriptionID) + '_tracking'

				IF @loopCurrSubActiveGroupID is not null
					exec dbo.ams_createMemberGroup @memberID, @loopCurrSubActiveGroupID, 1
			END

			-- update groups.  Check if this subscription still exists in a I or E status, if not then remove
			select @tempCount=count(s.subscriberID)
			from dbo.sub_subscribers s
			inner join dbo.sub_statuses st on st.statusID = s.statusID AND st.statusCode in ('I','E')
			inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
			where s.subscriptionID = @currSubscriptionID

			IF @tempCount = 0
			begin
				select @loopCurrSubWaitingGroupID=null
				select @loopCurrSubWaitingGroupID=groupID
				from dbo.ams_groups
				where groupCode = 'subInactive_' + convert(varchar, @currSubscriptionID) + '_tracking'

				IF @loopCurrSubWaitingGroupID is not null
					exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubWaitingGroupID
			end

			select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus
			while @minSubscriberID is not null
			begin
				EXEC dbo.sub_updateSubscriberStatus @subscriberID=@minSubscriberID,	@newStatusCode=@newStatusCode,	@siteID=@siteID, @enteredByMemberID=@enteredByActiveMemberID, @updateDate=@updateDate, @result=@insideResult OUTPUT
				select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus where subscriberID > @minSubscriberID
			end
					
			-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
			EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberID, @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

			select @result = 1

		END -- IF @currStatusCode = 'I'

		IF @currStatusCode = 'O'
		BEGIN
			-- before we make this active, we need to see if there is another active/inactive that needs to expire
			select @prevSubscriberID=s.subscriberID
			from dbo.sub_subscribers s
			inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode in ('A', 'I')
			inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID 
			inner join dbo.sub_types t on t.typeID = subs.typeID and t.siteID = @siteID											
			where s.subscriptionID = @currSubscriptionID
			and s.subscriberID <> @subscriberID
			and s.memberID = @memberID

			IF @prevSubscriberID is not null AND @currPaymentStatusCode = 'P'
				EXEC dbo.sub_updateSubscriberStatus @subscriberID=@prevSubscriberID, @newStatusCode='E', @siteID=@siteID, @enteredByMemberID=@enteredByActiveMemberID, @updateDate=@updateDate, @result=@insideResult OUTPUT
				
			/*
				print 'Offered to Active'
				update the subscription that was sent, 
				put in active or waiting group, based on payment status
				check if member has a subscription of same ID that is of Status R or O.  If not, remove from renew group
				then loop over the others to call status update
			*/
			update dbo.sub_subscribers
			set statusID = @newStatusID
			where subscriberID = @subscriberID

			select @tempAcceptedStatusID=statusID
			from dbo.sub_statuses
			where statusCode = 'P'

			insert into dbo.sub_statusHistory (subscriberID, oldStatusID, statusID, enteredByMemberID, updateDate)
			values(@subscriberID, @currStatusID, @tempAcceptedStatusID, @enteredByActiveMemberID, @updateDate)
					
			select @tmpDate = dateadd(ms,2,getdate())
			insert into dbo.sub_statusHistory(subscriberID, oldStatusID, statusID, enteredByMemberID, updateDate)
			values(@subscriberID, @tempAcceptedStatusID, @newStatusID, @enteredByActiveMemberID, @tmpDate)
					
			IF @currPaymentStatusCode = 'N'
			BEGIN
				-- update groups.  Add to active, would also check on payment status
				select @loopCurrSubWaitingGroupID=null
				select @loopCurrSubWaitingGroupID=groupID
				from dbo.ams_groups
				where groupCode = 'subWaitingPay_' + convert(varchar, @currSubscriptionID) + '_tracking'

				IF @loopCurrSubWaitingGroupID is not null
					exec dbo.ams_createMemberGroup @memberID, @loopCurrSubWaitingGroupID, 1
			END
			ELSE
			BEGIN					
				-- update groups.  Add to active, would also check on payment status
				select @loopCurrSubActiveGroupID=null
				select @loopCurrSubActiveGroupID=groupID
				from dbo.ams_groups
				where groupCode = 'subActive_' + convert(varchar, @currSubscriptionID) + '_tracking'

				IF @loopCurrSubActiveGroupID is not null
					exec dbo.ams_createMemberGroup @memberID, @loopCurrSubActiveGroupID, 1
			END
					
			-- update groups.  Check if this subscription still exists in a I or E status, if not then remove
			select @tempCount=count(s.subscriberID)
			from dbo.sub_subscribers s
			inner join dbo.sub_statuses st on st.statusID = s.statusID AND st.statusCode in ('R','O')
			inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
			where s.subscriptionID = @currSubscriptionID
					
			IF @tempCount = 0
			begin
				select @loopCurrSubRenewGroupID=null
				select @loopCurrSubRenewGroupID=groupID
				from dbo.ams_groups
				where groupCode = 'subRenew_' + convert(varchar, @currSubscriptionID) + '_tracking'

				IF @loopCurrSubRenewGroupID is not null
					exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubRenewGroupID
			end
					
			select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus
			while @minSubscriberID is not null
			begin
				EXEC dbo.sub_updateSubscriberStatus @subscriberID=@minSubscriberID, @newStatusCode=@newStatusCode, @siteID=@siteID, @enteredByMemberID=@enteredByActiveMemberID, @updateDate=@updateDate, @result=@insideResult OUTPUT
				select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus where subscriberID > @minSubscriberID
			end
					
			-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
			EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberID, @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

			select @result = 1

		END -- IF @currStatusCode = 'O'

		IF @currStatusCode = 'P'
		BEGIN
			-- before we make this active, we need to see if there is another active/inactive that needs to expire
			select @prevSubscriberID=s.subscriberID
			from dbo.sub_subscribers s
			inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode in ('A', 'I')
			inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID 
			inner join dbo.sub_types t on t.typeID = subs.typeID and t.siteID = @siteID											
			where s.subscriptionID = @currSubscriptionID
			and s.subscriberID <> @subscriberID
			and s.memberID = @memberID

			IF @prevSubscriberID is not null AND @currPaymentStatusCode = 'P'
				EXEC dbo.sub_updateSubscriberStatus @subscriberID=@prevSubscriberID, @newStatusCode='E', @siteID=@siteID, @enteredByMemberID=@enteredByActiveMemberID, @updateDate=@updateDate, @result=@insideResult OUTPUT

			/*
				update the subscription that was sent, 
				put in active or waiting group, based on payment status
				check if member has a subscription of same ID that is of status P.  If not, remove from pending group
				then loop over the others to call status update
			*/
			update dbo.sub_subscribers
			set statusID = @newStatusID
			where subscriberID = @subscriberID

			insert into dbo.sub_statusHistory (subscriberID, oldStatusID, statusID, enteredByMemberID, updateDate)
			values(@subscriberID, @currStatusID, @newStatusID, @enteredByActiveMemberID, @updateDate)

			IF @currPaymentStatusCode = 'N'
			BEGIN
				-- update groups.  Add to active, would also check on payment status
				select @loopCurrSubWaitingGroupID=null
				select @loopCurrSubWaitingGroupID=groupID
				from dbo.ams_groups
				where groupCode = 'subWaitingPay_' + convert(varchar, @currSubscriptionID) + '_tracking'

				IF @loopCurrSubWaitingGroupID is not null
					exec dbo.ams_createMemberGroup @memberID, @loopCurrSubWaitingGroupID, 1
			END
			ELSE
			BEGIN					
				-- update groups.  Check if this subscription still exists in a N payment status, if not then remove
				select @tempCount=count(subscriberID)
				from dbo.sub_subscribers s
				inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID and pst.statusCode = 'N'
				inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
				where s.subscriptionID = @currSubscriptionID

				IF @tempCount = 0
				begin
					select @loopCurrSubWaitingGroupID=null
					select @loopCurrSubWaitingGroupID=groupID
					from dbo.ams_groups
					where groupCode = 'subWaitingPay_' + convert(varchar, @currSubscriptionID) + '_tracking'

					IF @loopCurrSubWaitingGroupID is not null
						exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubWaitingGroupID
				end

				-- update groups.  Add to active, would also check on payment status
				select @loopCurrSubActiveGroupID=null
				select @loopCurrSubActiveGroupID=groupID
				from dbo.ams_groups
				where groupCode = 'subActive_' + convert(varchar, @currSubscriptionID) + '_tracking'

				IF @loopCurrSubActiveGroupID is not null
					exec dbo.ams_createMemberGroup @memberID, @loopCurrSubActiveGroupID, 1
			END

			-- update groups.  Check if this subscription still exists in a P status, if not then remove
			select @tempCount=count(subscriberID)
			from dbo.sub_subscribers s
			inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode = 'P'
			inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
			where s.subscriptionID = @currSubscriptionID

			IF @tempCount = 0
			begin
				select @loopCurrSubPendingGroupID=null
				select @loopCurrSubPendingGroupID=groupID
				from dbo.ams_groups
				where groupCode = 'subPending_' + convert(varchar, @currSubscriptionID) + '_tracking'

				IF @loopCurrSubPendingGroupID is not null
					exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubPendingGroupID
			end

			select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus
			while @minSubscriberID is not null
			begin
				EXEC dbo.sub_updateSubscriberStatus @subscriberID=@minSubscriberID, @newStatusCode=@newStatusCode, @siteID=@siteID, @enteredByMemberID=@enteredByActiveMemberID, @updateDate=@updateDate, @result=@insideResult OUTPUT
				select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus where subscriberID > @minSubscriberID
			end
					
			-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
			EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberID, @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

			select @result = 1

		END  --IF @currStatusCode = 'P'

		IF @currStatusCode = 'D'
		BEGIN
			-- before we make this active, we need to see if there is another active/inactive that needs to expire
			select @prevSubscriberID=s.subscriberID
			from dbo.sub_subscribers s
			inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode in ('A', 'I')
			inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID 
			inner join dbo.sub_types t on t.typeID = subs.typeID and t.siteID = @siteID											
			where s.subscriptionID = @currSubscriptionID
			and s.subscriberID <> @subscriberID
			and s.memberID = @memberID

			IF @prevSubscriberID is not null
				select @result = 0
			ELSE
			BEGIN
				/* Don't undo the tree, just the current one */
				select @tempEndDate=sParent.subEndDate, @tempGraceEndDate=sParent.graceEndDate
				from dbo.fn_getRecursiveMemberSubscriptions(@memberID, @siteID, @subscriberID) rms
				left outer join dbo.sub_subscribers sParent on sParent.subscriberID = rms.rootSubscriberID
				where rms.status = 'D'
						
				update dbo.sub_subscribers
				set statusID = @newStatusID,
					subEndDate=@tempEndDate,
					graceEndDate=@tempGraceEndDate
				where subscriberID = @subscriberID

				insert into dbo.sub_statusHistory (subscriberID, oldStatusID, statusID, enteredByMemberID, updateDate)
				values(@subscriberID, @currStatusID, @newStatusID, @enteredByActiveMemberID, @updateDate)
						
				IF @currPaymentStatusCode = 'N'
				BEGIN
					-- update groups.  
					select @loopCurrSubWaitingGroupID=null
					select @loopCurrSubActiveGroupID=groupID
					from dbo.ams_groups
					where groupCode = 'subWaitingPay_' + convert(varchar, @currSubscriptionID) + '_tracking'

					IF @loopCurrSubWaitingGroupID is not null
						exec dbo.ams_createMemberGroup @memberID, @loopCurrSubWaitingGroupID, 0
				END
				ELSE
				BEGIN					
					-- update groups.  
					select @loopCurrSubActiveGroupID=null
					select @loopCurrSubActiveGroupID=groupID
					from dbo.ams_groups
					where groupCode = 'subActive_' + convert(varchar, @currSubscriptionID) + '_tracking'

					IF @loopCurrSubActiveGroupID is not null
						exec dbo.ams_createMemberGroup @memberID, @loopCurrSubActiveGroupID, 0
				END

				select @result = 0
			END

		END  --IF @currStatusCode = 'D'

	END

END

IF @newStatusCode = 'I'
BEGIN

	IF @currStatusCode is not null
	BEGIN

		IF @currStatusCode = 'A'
		BEGIN
			/*
				update the subscription that was sent, 
				put in inactive or waiting group, based on payment status
				check if member has a subscription of same ID that is active.  If not, remove from active group
				then loop over the others to call status update
			*/
			update dbo.sub_subscribers
			set statusID = @newStatusID
			where subscriberID = @subscriberID

			insert into dbo.sub_statusHistory (subscriberID, oldStatusID, statusID, enteredByMemberID, updateDate)
			values(@subscriberID, @currStatusID, @newStatusID, @enteredByActiveMemberID, @updateDate)

			-- update groups.  Add to inactive, would also check on payment status
			select @loopCurrSubInactiveGroupID=null
			select @loopCurrSubInactiveGroupID=groupID
			from dbo.ams_groups
			where groupCode = 'subInactive_' + convert(varchar, @currSubscriptionID) + '_tracking'

			IF @loopCurrSubInactiveGroupID is not null
				exec dbo.ams_createMemberGroup @memberID, @loopCurrSubInactiveGroupID, 1

			-- update groups.  Check if this subscription still exists in an A status, if not then remove
			select @tempCount=count(s.subscriberID)
			from dbo.sub_subscribers s
			inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode = 'A'
			inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
			where s.subscriptionID = @currSubscriptionID

			IF @tempCount = 0
			begin
				select @loopCurrSubActiveGroupID=null
				select @loopCurrSubActiveGroupID=groupID
				from dbo.ams_groups
				where groupCode = 'subActive_' + convert(varchar, @currSubscriptionID) + '_tracking'

				IF @loopCurrSubActiveGroupID is not null
					exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubActiveGroupID
			end

			-- update groups.  Check if this subscription still exists in an N payment status, if not then remove
			select @tempCount=count(s.subscriberID)
			from dbo.sub_subscribers s
			inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID and pst.statusCode = 'N'
			inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
			where s.subscriptionID = @currSubscriptionID

			IF @tempCount = 0
			begin
				select @loopCurrSubWaitingGroupID=null
				select @loopCurrSubWaitingGroupID=groupID
				from dbo.ams_groups
				where groupCode = 'subWaitingPay_' + convert(varchar, @currSubscriptionID) + '_tracking'

				IF @loopCurrSubWaitingGroupID is not null
					exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubWaitingGroupID
			end

			select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus
			while @minSubscriberID is not null
			begin
				EXEC dbo.sub_updateSubscriberStatus @subscriberID=@minSubscriberID, @newStatusCode=@newStatusCode, @siteID=@siteID, @enteredByMemberID=@enteredByActiveMemberID, @updateDate=@updateDate, @result=@insideResult OUTPUT
				select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus where subscriberID > @minSubscriberID
			end
					
			-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
			EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberID, @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

			select @result = 1

		END
	
	END

END

IF @newStatusCode = 'R'
BEGIN

	IF @currStatusCode is not null
	BEGIN

		IF @currStatusCode = 'D'
		BEGIN
			/* Don't undo the tree, just the current one */
			select @tempEndDate=sParent.subEndDate, @tempGraceEndDate=sParent.graceEndDate
			from dbo.fn_getRecursiveMemberSubscriptions(@memberID, @siteID, @subscriberID) rms
			left outer join dbo.sub_subscribers sParent on sParent.subscriberID = rms.parentSubscriberID
			where rms.status = 'D'
			
			update dbo.sub_subscribers
			set statusID = @newStatusID,
				subEndDate=@tempEndDate,
				graceEndDate=@tempGraceEndDate
			where subscriberID = @subscriberID

			insert into dbo.sub_statusHistory (subscriberID, oldStatusID, statusID, enteredByMemberID, updateDate)
			values(@subscriberID, @currStatusID, @newStatusID, @enteredByActiveMemberID, @updateDate)
			
			-- update groups.  Add to active, would also check on payment status
			select @loopCurrSubRenewGroupID=null
			select @loopCurrSubRenewGroupID=groupID
			from dbo.ams_groups
			where groupCode = 'subRenew_' + convert(varchar, @currSubscriptionID) + '_tracking'

			IF @loopCurrSubRenewGroupID is not null
				exec dbo.ams_createMemberGroup @memberID, @loopCurrSubRenewGroupID, 0
		END

	END

END

IF @newStatusCode = 'O'
BEGIN

	IF @currStatusCode is not null
	BEGIN

		IF @currStatusCode = 'R'
		BEGIN
			/*
				print 'Renewal Not Sent to Offered'
				update the subscription that was sent, 
				put in renew or waiting group, based on payment status
				O and R are both in renew group, so no removal
				then loop over the others to call status update
			*/

			update dbo.sub_subscribers
			set statusID = @newStatusID
			where subscriberID = @subscriberID

			insert into dbo.sub_statusHistory (subscriberID, oldStatusID, statusID, enteredByMemberID, updateDate)
			values(@subscriberID, @currStatusID, @newStatusID, @enteredByActiveMemberID, @updateDate)

			-- update groups.  Add to inactive, would also check on payment status
			-- should already be in active, but this will make sure
			select @loopCurrSubInactiveGroupID=null
			select @loopCurrSubInactiveGroupID=groupID
			from dbo.ams_groups
			where groupCode = 'subInactive_' + convert(varchar, @currSubscriptionID) + '_tracking'

			IF @loopCurrSubInactiveGroupID is not null 
				exec dbo.ams_createMemberGroup @memberID, @loopCurrSubInactiveGroupID, 1

			select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus
			while @minSubscriberID is not null
			begin
				EXEC dbo.sub_updateSubscriberStatus @subscriberID=@minSubscriberID, @newStatusCode=@newStatusCode, @siteID=@siteID, @enteredByMemberID=@enteredByActiveMemberID, @updateDate=@updateDate, @result=@insideResult OUTPUT
				select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus where subscriberID > @minSubscriberID
			end
					
			-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
			EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberID, @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

			select @result = 1
		END

		IF @currStatusCode = 'D'
		BEGIN
			/* Don't undo the tree, just the current one */
			select @tempEndDate=sParent.subEndDate, @tempGraceEndDate=sParent.graceEndDate
			from dbo.fn_getRecursiveMemberSubscriptions(@memberID, @siteID, @subscriberID) rms
			left outer join dbo.sub_subscribers sParent on sParent.subscriberID = rms.parentSubscriberID
			where rms.status = 'D'
			
			update dbo.sub_subscribers
			set statusID = @newStatusID,
				subEndDate=@tempEndDate,
				graceEndDate=@tempGraceEndDate
			where subscriberID = @subscriberID

			insert into dbo.sub_statusHistory (subscriberID, oldStatusID, statusID, enteredByMemberID, updateDate)
			values(@subscriberID, @currStatusID, @newStatusID, @enteredByActiveMemberID, @updateDate)
			
			-- update groups.  Add to active, would also check on payment status
			select @loopCurrSubRenewGroupID=null
			select @loopCurrSubRenewGroupID=groupID
			from dbo.ams_groups
			where groupCode = 'subRenew_' + convert(varchar, @currSubscriptionID) + '_tracking'

			IF @loopCurrSubRenewGroupID is not null
				exec dbo.ams_createMemberGroup @memberID, @loopCurrSubRenewGroupID, 0
		END
	
	END

END

IF @newStatusCode = 'P'
BEGIN

	IF @currStatusCode is not null
	BEGIN

		IF @currStatusCode = 'O'
		BEGIN
			/*
				print 'Offered to Accepted'
				update the subscription that was sent, 
				put in pending or waiting group, based on payment status
				check if member has a subscription of same ID that is O or R.  If not, remove from renew group
				then loop over the others to call status update
			*/
			update dbo.sub_subscribers
			set statusID = @newStatusID
			where subscriberID = @subscriberID

			insert into dbo.sub_statusHistory (subscriberID, oldStatusID, statusID, enteredByMemberID, updateDate)
			values(@subscriberID, @currStatusID, @newStatusID, @enteredByActiveMemberID, @updateDate)

			IF @currPaymentStatusCode = 'N'
			BEGIN
				-- update groups.  Add to active, would also check on payment status
				select @loopCurrSubWaitingGroupID=null
				select @loopCurrSubActiveGroupID=groupID
				from dbo.ams_groups
				where groupCode = 'subWaitingPay_' + convert(varchar, @currSubscriptionID) + '_tracking'

				IF @loopCurrSubWaitingGroupID is not null
					exec dbo.ams_createMemberGroup @memberID, @loopCurrSubWaitingGroupID, 1
			END
			ELSE
			BEGIN					
				-- update groups.  Add to active, would also check on payment status
				select @loopCurrSubPendingGroupID=null
				select @loopCurrSubPendingGroupID=groupID
				from dbo.ams_groups
				where groupCode = 'subPending_' + convert(varchar, @currSubscriptionID) + '_tracking'

				IF @loopCurrSubPendingGroupID is not null
					exec dbo.ams_createMemberGroup @memberID, @loopCurrSubPendingGroupID, 1
			END
					
			-- update groups.  Check if this subscription still exists in a R or O status, if not then remove
			select @tempCount=count(s.subscriberID)
			from dbo.sub_subscribers s
			inner join dbo.sub_statuses st on st.statusID = s.statusID AND st.statusCode in ('R','O')
			inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
			where s.subscriptionID = @currSubscriptionID
					
			IF @tempCount = 0
			begin
				select @loopCurrSubRenewGroupID=null
				select @loopCurrSubRenewGroupID=groupID
				from dbo.ams_groups
				where groupCode = 'subRenew_' + convert(varchar, @currSubscriptionID) + '_tracking'

				IF @loopCurrSubRenewGroupID is not null
					exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubRenewGroupID
			end

			select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus
			while @minSubscriberID is not null
			begin
				EXEC dbo.sub_updateSubscriberStatus @subscriberID=@minSubscriberID, @newStatusCode=@newStatusCode, @siteID=@siteID, @enteredByMemberID=@enteredByActiveMemberID, @updateDate=@updateDate, @result=@insideResult OUTPUT
				select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus where subscriberID > @minSubscriberID
			end
					
			-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
			EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberID, @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

			select @result = 1

		END

		IF @currStatusCode = 'D'
		BEGIN
			/* Don't undo the tree, just the current one */
			select @tempEndDate=sParent.subEndDate, @tempGraceEndDate=sParent.graceEndDate
			from dbo.fn_getRecursiveMemberSubscriptions(@memberID, @siteID, @subscriberID) rms
			left outer join dbo.sub_subscribers sParent on sParent.subscriberID = rms.parentSubscriberID
			where rms.status = 'D'
			
			update dbo.sub_subscribers
			set statusID = @newStatusID,
				subEndDate=@tempEndDate,
				graceEndDate=@tempGraceEndDate
			where subscriberID = @subscriberID

			insert into dbo.sub_statusHistory (subscriberID, oldStatusID, statusID, enteredByMemberID, updateDate)
			values(@subscriberID, @currStatusID, @newStatusID, @enteredByActiveMemberID, @updateDate)
					
			IF @currPaymentStatusCode = 'N'
			BEGIN
				-- update groups.  Add to active, would also check on payment status
				select @loopCurrSubWaitingGroupID=null
				select @loopCurrSubActiveGroupID=groupID
				from dbo.ams_groups
				where groupCode = 'subWaitingPay_' + convert(varchar, @currSubscriptionID) + '_tracking'

				IF @loopCurrSubWaitingGroupID is not null
					exec dbo.ams_createMemberGroup @memberID, @loopCurrSubWaitingGroupID, 0
			END
			ELSE
			BEGIN					
				-- update groups.  Add to active, would also check on payment status
				select @loopCurrSubPendingGroupID=null
				select @loopCurrSubPendingGroupID=groupID
				from dbo.ams_groups
				where groupCode = 'subPending_' + convert(varchar, @currSubscriptionID) + '_tracking'

				IF @loopCurrSubPendingGroupID is not null
					exec dbo.ams_createMemberGroup @memberID, @loopCurrSubPendingGroupID, 0
			END

		END

	END

END

IF @newStatusCode = 'E'  
BEGIN

	IF @currStatusCode is not null
	BEGIN

		IF @currStatusCode = 'A' OR @currStatusCode = 'I'
		BEGIN
			/*
				update the subscription that was sent, 
				put in expired or waiting group, based on payment status
				check if member has a subscription of same ID that is active.  If not, remove from active group
				then loop over the others to call status update
			*/

			update dbo.sub_subscribers
			set statusID = @newStatusID
			where subscriberID = @subscriberID

			if @endDate between @currSubStartDate and @currSubEndDate
			BEGIN 
				update dbo.sub_subscribers
				set subEndDate = @endDate
				where subscriberID = @subscriberID

				update dbo.sub_subscribers
				set graceEndDate = @endDate
				where subscriberID = @subscriberID
				and graceEndDate is not null
			END

			insert into dbo.sub_statusHistory (subscriberID, oldStatusID, statusID, enteredByMemberID, updateDate)
			values(@subscriberID, @currStatusID, @newStatusID, @enteredByActiveMemberID, @updateDate)

			-- update groups.  Add to active, would also check on payment status
			select @loopCurrSubInactiveGroupID=null
			select @loopCurrSubInactiveGroupID=groupID
			from dbo.ams_groups
			where groupCode = 'subInactive_' + convert(varchar, @currSubscriptionID) + '_tracking'

			IF @loopCurrSubInactiveGroupID is not null
				exec dbo.ams_createMemberGroup @memberID, @loopCurrSubInactiveGroupID, 1

			-- waiting group
			select @tempCount=count(s.subscriberID)
			from dbo.sub_subscribers s
			inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID AND pst.statusCode = 'N'
			inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
			where s.subscriptionID = @currSubscriptionID

			IF @tempCount = 0
			begin
				select @loopCurrSubWaitingGroupID=null
				select @loopCurrSubWaitingGroupID=groupID
				from dbo.ams_groups
				where groupCode = 'subWaitingPay_' + convert(varchar, @currSubscriptionID) + '_tracking'

				IF @loopCurrSubWaitingGroupID is not null
					exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubWaitingGroupID
			end

			-- update groups.  Check if this subscription still exists in a A status, if not then remove
			select @tempCount=count(s.subscriberID)
			from dbo.sub_subscribers s
			inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode = 'A'
			inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
			where s.subscriptionID = @currSubscriptionID

			IF @tempCount = 0
			begin
				select @loopCurrSubActiveGroupID=null
				select @loopCurrSubActiveGroupID=groupID
				from dbo.ams_groups
				where groupCode = 'subActive_' + convert(varchar, @currSubscriptionID) + '_tracking'

				IF @loopCurrSubActiveGroupID is not null
					exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubActiveGroupID
			end

			select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus
			while @minSubscriberID is not null
			begin
				EXEC dbo.sub_updateSubscriberStatus @subscriberID=@minSubscriberID, @newStatusCode=@newStatusCode, @siteID=@siteID, @enteredByMemberID=@enteredByActiveMemberID, @updateDate=@updateDate, @result=@insideResult OUTPUT
				select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus where subscriberID > @minSubscriberID
			end
			
			-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
			EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberID, @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

			select @result = 1

		END

		IF @currStatusCode = 'P'
		BEGIN
			/*
				update the subscription that was sent, 
				put in expired or waiting group, based on payment status
				check if member has a subscription of same ID that is pending.  If not, remove from pending group
				then loop over the others to call status update
			*/

			update dbo.sub_subscribers
			set statusID = @newStatusID
			where subscriberID = @subscriberID

			insert into dbo.sub_statusHistory (subscriberID, oldStatusID, statusID, enteredByMemberID, updateDate)
			values(@subscriberID, @currStatusID, @newStatusID, @enteredByActiveMemberID, @updateDate)

			-- update groups.  Add to active, would also check on payment status
			select @loopCurrSubInactiveGroupID=null
			select @loopCurrSubInactiveGroupID=groupID
			from dbo.ams_groups
			where groupCode = 'subInactive_' + convert(varchar, @currSubscriptionID) + '_tracking'

			IF @loopCurrSubInactiveGroupID is not null
				exec dbo.ams_createMemberGroup @memberID, @loopCurrSubInactiveGroupID, 1

			-- waiting group
			select @tempCount=count(s.subscriberID)
			from dbo.sub_subscribers s
			inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID AND pst.statusCode = 'N'
			inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
			where s.subscriptionID = @currSubscriptionID

			IF @tempCount = 0
			begin
				select @loopCurrSubWaitingGroupID=null
				select @loopCurrSubWaitingGroupID=groupID
				from dbo.ams_groups
				where groupCode = 'subWaitingPay_' + convert(varchar, @currSubscriptionID) + '_tracking'

				IF @loopCurrSubWaitingGroupID is not null
					exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubWaitingGroupID
			end

			-- update groups.  Check if this subscription still exists in a P status, if not then remove
			select @tempCount=count(s.subscriberID)
			from dbo.sub_subscribers s
			inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode = 'P'
			inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
			where s.subscriptionID = @currSubscriptionID

			IF @tempCount = 0
			begin
				select @loopCurrSubPendingGroupID=null
				select @loopCurrSubPendingGroupID=groupID
				from dbo.ams_groups
				where groupCode = 'subPending_' + convert(varchar, @currSubscriptionID) + '_tracking'

				IF @loopCurrSubPendingGroupID is not null
					exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubPendingGroupID
			end

			select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus
			while @minSubscriberID is not null
			begin
				EXEC dbo.sub_updateSubscriberStatus @subscriberID=@minSubscriberID, @newStatusCode=@newStatusCode, @siteID=@siteID, @enteredByMemberID=@enteredByActiveMemberID, @updateDate=@updateDate, @result=@insideResult OUTPUT
				select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus where subscriberID > @minSubscriberID
			end
			
			-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
			EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberID, @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

			select @result = 1

		END

	END

END

IF @newStatusCode = 'X'
BEGIN

	IF @currStatusCode is not null
	BEGIN

		IF @currStatusCode = 'R' OR @currStatusCode = 'O'
		BEGIN
			/*
				update the subscription that was sent, 
				check if member has a subscription of same ID that is R or O.  If not, remove from renew group
				then loop over the others to call status update
			*/
			update dbo.sub_subscribers
			set statusID = @newStatusID
			where subscriberID = @subscriberID

			insert into dbo.sub_statusHistory (subscriberID, oldStatusID, statusID, enteredByMemberID, updateDate)
			values(@subscriberID, @currStatusID, @newStatusID, @enteredByActiveMemberID, @updateDate)

			-- update groups.  Check if this subscription still exists in a I or E status, if not then remove
			select @tempCount=count(s.subscriberID)
			from dbo.sub_subscribers s
			inner join dbo.sub_statuses st on st.statusID = s.statusID AND st.statusCode in ('O','R')
			inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
			where s.subscriptionID = @currSubscriptionID

			IF @tempCount = 0
			begin
				select @loopCurrSubRenewGroupID=null
				select @loopCurrSubRenewGroupID=groupID
				from dbo.ams_groups
				where groupCode = 'subRenew_' + convert(varchar, @currSubscriptionID) + '_tracking'

				IF @loopCurrSubRenewGroupID is not null
					exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubRenewGroupID
			end

			select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus
			while @minSubscriberID is not null
			begin
				EXEC dbo.sub_updateSubscriberStatus @subscriberID=@minSubscriberID, @newStatusCode=@newStatusCode, @siteID=@siteID, @enteredByMemberID=@enteredByActiveMemberID, @updateDate=@updateDate, @result=@insideResult OUTPUT
				select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus where subscriberID > @minSubscriberID
			end

			-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
			EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberID, @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

			select @result = 1

		END

	END

END

IF @newStatusCode = 'D'
BEGIN

	IF (@currStatusCode is not null) AND (@currStatusCode <> 'D')
	BEGIN
		/*
			update the subscription that was sent, 
			check if member has a subscription of same ID that in any status.  remove from sub groups where applicable
			then loop over the others to call status update
		*/

		update dbo.sub_subscribers
		set statusID = @newStatusID
		where subscriberID = @subscriberID

		insert into dbo.sub_statusHistory (subscriberID, oldStatusID, statusID, enteredByMemberID, updateDate)
		values(@subscriberID, @currStatusID, @newStatusID, @enteredByActiveMemberID, @updateDate)

		-- update groups.  Check if this subscription still exists in A status, if not then remove
		select @tempCount=count(s.subscriberID)
		from dbo.sub_subscribers s
		inner join dbo.sub_statuses st on st.statusID = s.statusID AND st.statusCode = 'A'
		inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
		where s.subscriptionID = @currSubscriptionID

		IF @tempCount = 0
		begin
			select @loopCurrSubActiveGroupID=null
			select @loopCurrSubActiveGroupID=groupID
			from dbo.ams_groups
			where groupCode = 'subActive_' + convert(varchar, @currSubscriptionID) + '_tracking'

			IF @loopCurrSubActiveGroupID is not null
				exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubActiveGroupID
		end

		-- waiting group
		select @tempCount=count(s.subscriberID)
		from dbo.sub_subscribers s
		inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID AND pst.statusCode = 'N'
		inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
		where s.subscriptionID = @currSubscriptionID

		IF @tempCount = 0
		begin
			select @loopCurrSubWaitingGroupID=null
			select @loopCurrSubWaitingGroupID=groupID
			from dbo.ams_groups
			where groupCode = 'subWaitingPay_' + convert(varchar, @currSubscriptionID) + '_tracking'

			IF @loopCurrSubWaitingGroupID is not null
				exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubWaitingGroupID
		end

		-- update groups.  Check if this subscription still exists in a I or E status, if not then remove
		select @tempCount=count(s.subscriberID)
		from dbo.sub_subscribers s
		inner join dbo.sub_statuses st on st.statusID = s.statusID AND st.statusCode in ('I','E')
		inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
		where s.subscriptionID = @currSubscriptionID
		
		IF @tempCount = 0
		begin
			select @loopCurrSubInactiveGroupID=null
			select @loopCurrSubInactiveGroupID=groupID
			from dbo.ams_groups
			where groupCode = 'subInactive_' + convert(varchar, @currSubscriptionID) + '_tracking'

			IF @loopCurrSubInactiveGroupID is not null
				exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubInactiveGroupID
		end

		-- update groups.  Check if this subscription still exists in a R or O status, if not then remove
		select @tempCount=count(s.subscriberID)
		from dbo.sub_subscribers s
		inner join dbo.sub_statuses st on st.statusID = s.statusID AND st.statusCode in ('R','O')
		inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
		where s.subscriptionID = @currSubscriptionID

		IF @tempCount = 0
		begin
			select @loopCurrSubRenewGroupID=null
			select @loopCurrSubRenewGroupID=groupID
			from dbo.ams_groups
			where groupCode = 'subRenew_' + convert(varchar, @currSubscriptionID) + '_tracking'

			IF @loopCurrSubRenewGroupID is not null
				exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubRenewGroupID
		end

		-- update groups.  Check if this subscription still exists in P status, if not then remove
		select @tempCount=count(s.subscriberID)
		from dbo.sub_subscribers s
		inner join dbo.sub_statuses st on st.statusID = s.statusID AND st.statusCode = 'P'
		inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @activeMemberID
		where s.subscriptionID = @currSubscriptionID

		IF @tempCount = 0
		begin
			select @loopCurrSubPendingGroupID=null
			select @loopCurrSubPendingGroupID=groupID
			from dbo.ams_groups
			where groupCode = 'subPending_' + convert(varchar, @currSubscriptionID) + '_tracking'

			IF @loopCurrSubPendingGroupID is not null
				exec dbo.ams_deleteMemberGroup @memberID, @loopCurrSubPendingGroupID
		end
		
		select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus
		while @minSubscriberID is not null
		begin
			EXEC dbo.sub_updateSubscriberStatus @subscriberID=@minSubscriberID, @newStatusCode=@newStatusCode, @siteID=@siteID, @enteredByMemberID=@enteredByActiveMemberID, @updateDate=@updateDate, @result=@insideResult OUTPUT
			select @minSubscriberID = min(subscriberID) from @tempUpdateSubscriberStatus where subscriberID > @minSubscriberID
		end
		
		-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberID, @conditionIDList='', @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT

		select @result = 1

	END

END

goto done

on_error:
	select @result = -1

done:
GO
 
-- (2) *****************************************************************************************************************************

ALTER PROCEDURE [dbo].[sub_expireSubscriptions] 
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @dtStart DATETIME, @dtEnd DATETIME, @dateToUse DATETIME
	select @dateToUse = getdate()
	SELECT @dtStart = dateadd(DAY, datediff(DAY, 0, @dateToUse), 0) 
	SELECT @dtEnd = dateadd(millisecond, -3, @dtStart)

	declare @minSubscriberID int, @currSubscriptionID int, @currMemberID int
	declare @currActiveCount int, @currOrgID int, @currSiteID int, @currResult int

	IF OBJECT_ID('tempdb..##tempExpireSubJob') IS NOT NULL 
		DROP TABLE ##tempExpireSubJob

	IF OBJECT_ID('tempdb..##tempActivateSubJob') IS NOT NULL 
		DROP TABLE ##tempActivateSubJob

	select st.orgID, st.siteID, sc.subscriberID, sc.subscriptionID, sc.memberID
	INTO ##tempActivateSubJob
	from dbo.sub_subscribers sc
	inner join dbo.sub_subscriptions su on su.subscriptionID = sc.subscriptionID
	inner join dbo.sub_types t on t.typeID = su.typeID
	inner join dbo.sites st on st.siteID = t.siteID
	inner join dbo.sub_statuses ss on ss.statusID = sc.statusID and ss.statusCode = 'P'
	where (sc.subStartDate <= @dtStart)

	select @minSubscriberID = min(subscriberID) from ##tempActivateSubJob
	while @minSubscriberID is not null BEGIN
		select @currSubscriptionID=subscriptionID, @currMemberID=memberID, @currOrgID=orgID, @currSiteID=siteID
		from ##tempActivateSubJob
		where subscriberID = @minSubscriberID
		
		EXEC dbo.sub_updateSubscriberStatus @subscriberID=@minSubscriberID,	@newStatusCode='A',	@siteID=@currSiteID, @enteredByMemberID=461530, @result=@currResult OUTPUT

		select @minSubscriberID = min(subscriberID)
		from ##tempActivateSubJob
		where subscriberID > @minSubscriberID
	END


	select st.orgID, st.siteID, sc.subscriberID, sc.subscriptionID, sc.memberID
	INTO ##tempExpireSubJob
	from dbo.sub_subscribers sc
	inner join dbo.sub_subscriptions su on su.subscriptionID = sc.subscriptionID and su.autoExpire = 1
	inner join dbo.sub_types t on t.typeID = su.typeID
	inner join dbo.sites st on st.siteID = t.siteID
	inner join dbo.sub_statuses s on s.statusID = sc.statusID and s.statusCode not in ('D','X','O','R','E')
	where ((sc.graceEndDate is not null and sc.graceEndDate <= @dtEnd) OR
	(sc.graceEndDate is null and sc.subEndDate <= @dtEnd))

	select @minSubscriberID = min(subscriberID) from ##tempExpireSubJob
	while @minSubscriberID is not null BEGIN
		select @currSubscriptionID=subscriptionID, @currMemberID=memberID, @currOrgID=orgID, @currSiteID=siteID
		from ##tempExpireSubJob
		where subscriberID = @minSubscriberID
		
		EXEC dbo.sub_updateSubscriberStatus @subscriberID=@minSubscriberID,	@newStatusCode='E',	@siteID=@currSiteID, @enteredByMemberID=461530, @updateDate=@dtEnd, @result=@currResult OUTPUT	

		select @minSubscriberID = min(subscriberID)
		from ##tempExpireSubJob
		where subscriberID > @minSubscriberID
	END

	IF OBJECT_ID('tempdb..##tempActivateSubJob') IS NOT NULL 
		DROP TABLE ##tempActivateSubJob

	IF OBJECT_ID('tempdb..##tempExpireSubJob') IS NOT NULL 
		DROP TABLE ##tempExpireSubJob
END
GO

-- (3) *****************************************************************************************************************************

ALTER PROCEDURE [dbo].[sub_checkActivations] 
	@memberid int,
	@bypassQueue bit = 0
AS

set nocount on

IF OBJECT_ID('tempdb..#firstInvoice') IS NOT NULL 
	DROP TABLE #firstInvoice
create table #firstInvoice (subscriberID int PRIMARY KEY, orgID int, invoiceNumber int)

IF OBJECT_ID('tempdb..#tblSubsToCheck') IS NOT NULL 
	DROP TABLE #tblSubsToCheck
create table #tblSubsToCheck (subscriberID int PRIMARY KEY, rootSubscriberID int, memberID int, orgID int, siteID int, subActivationCode char(1), statusCode char(1))


declare @membersToCheck TABLE (id int IDENTITY(1,1), memberID int PRIMARY KEY)
declare @tblSubsToMove TABLE (subscriberID int, statusCode char(1), reason varchar(50))
declare @activatedStatusID int, @nonActivatedStatusID int, @enteredByMemberID int, @followParentActivationOptionID int

select @activatedStatusID = statusID from dbo.sub_paymentStatuses where statusCode = 'P'
select @nonActivatedStatusID = statusID from dbo.sub_paymentStatuses where statusCode = 'N'
select @followParentActivationOptionID = subActivationID from dbo.sub_activationOptions where subActivationCode = 'F'
select @enteredByMemberID = memberID from ams_members where memberNumber = 'SYSTEM' and orgID = 1	

IF @memberID is null
BEGIN
	insert into #tblSubsToCheck (subscriberID, rootSubscriberID, memberID, orgID, siteID, subActivationCode, statusCode)
	select sub.subscriberID, sub.rootSubscriberID, m.activememberID as memberID, m.orgID, t.siteid, ao.subActivationCode, s.statusCode
	from sub_subscribers as sub
	inner join dbo.sub_statuses as s on s.statusID = sub.statusID
	inner join dbo.sub_activationOptions as ao on ao.subActivationID = sub.subActivationID
	inner join dbo.ams_members as m on m.memberid = sub.memberID
	inner join dbo.sub_subscriptions as ss on ss.subscriptionID = sub.subscriptionID
	inner join dbo.sub_Types as t on t.typeID = ss.typeID
	where sub.paymentStatusID = @nonActivatedStatusID
	and s.statusCode in ('A','P','I','E')
	and ao.subActivationCode in ('C','P','I','E','N','T') 
	order by sub.subscriberID
END
ELSE BEGIN
	insert into @membersToCheck (memberID)
	select m2.memberID
	from ams_members m
	inner join ams_members m2 on m.activeMemberID = m2.activeMemberID
		and m.memberID = @memberID

	insert into #tblSubsToCheck (subscriberID, rootSubscriberID, memberID, orgID, siteID, subActivationCode, statusCode)
	select sub.subscriberID, sub.rootSubscriberID, m.activememberID as memberID, m.orgID, t.siteid, ao.subActivationCode, s.statusCode
	from @membersToCheck mtc
	inner join sub_subscribers as sub on mtc.memberID = sub.memberID
	inner join dbo.sub_statuses as s on s.statusID = sub.statusID
	inner join dbo.sub_activationOptions as ao on ao.subActivationID = sub.subActivationID
	inner join dbo.ams_members as m on m.memberid = sub.memberID
	inner join dbo.sub_subscriptions as ss on ss.subscriptionID = sub.subscriptionID
	inner join dbo.sub_Types as t on t.typeID = ss.typeID
	where sub.paymentStatusID = @nonActivatedStatusID
	and s.statusCode in ('A','P','I','E')
	and ao.subActivationCode in ('C','P','I','E','N','T') 
	order by sub.subscriberID

END


CREATE INDEX IX_tblSubsToCheck on #tblSubsToCheck (subscriberID asc) INCLUDE (statusCode,subActivationCode)

-- N: No Reliance on payment
insert into @tblSubsToMove (subscriberID, statusCode, reason)
select tbl.subscriberID, tbl.statusCode, 'No reliance'
from #tblSubsToCheck as tbl
where tbl.subActivationCode = 'N'

-- P: This sub paid in full
insert into @tblSubsToMove (subscriberID, statusCode, reason)
select tbl.subscriberID, tbl.statusCode, 'this paid in full'
from #tblSubsToCheck as tbl
inner join dbo.tr_applications as tra on tra.itemID = tbl.subscriberID
	and tra.applicationTypeID = 17
	and tra.itemType = 'Dues'
	and tra.status = 'A'
cross apply dbo.fn_tr_transactionSalesWithDIT(tra.transactionID) as tsFull
where tbl.subActivationCode = 'P'
and tsFull.cache_amountAfterAdjustment = tsFull.cache_activePaymentAllocatedAmount
OPTION(RECOMPILE)

-- C: This sub and all children subs paid in full

; with addons as (
    select subscriberID as topLevelSubscriberID, subscriberID
    from #tblSubsToCheck tbl
    where tbl.subActivationCode = 'C'
    union all
    select topLevelSubscriberID, ss.subscriberID
    from addons s
    inner join sub_subscribers ss
	   on ss.parentSubscriberID = s.subscriberID
)
insert into @tblSubsToMove (subscriberID, statusCode, reason)
select tbl.subscriberID, tbl.statusCode, 'this and children paid in full'
from #tblSubsToCheck as tbl
inner join addons as rms
    on tbl.subscriberID = rms.topLevelSubscriberID
inner join dbo.tr_applications as tra on tra.itemID = rms.subscriberID
	and tra.applicationTypeID = 17
	and tra.itemType = 'Dues'
	and tra.status = 'A'
cross apply dbo.fn_tr_transactionSalesWithDIT(tra.transactionID) as tsFull
where tbl.subActivationCode = 'C'
group by tbl.subscriberID, tbl.statusCode
having sum(tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount) = 0
OPTION(RECOMPILE)

-- I: First invoice this sub appears on is paid
insert into @tblSubsToMove (subscriberID, statusCode, reason)
select tmp.subscriberID, tmp.statusCode, 'First invoice this sub'
from (
	select tbl.subscriberID, tbl.statusCode, i.assignedToMemberID, tbl.orgID, min(i.invoiceNumber) as firstInvNumber
	from #tblSubsToCheck as tbl
	inner join dbo.tr_applications as tra on tra.itemID = tbl.subscriberID
		and tra.applicationTypeID = 17
		and tra.itemType = 'Dues'
		and tra.status = 'A'
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = tra.transactionID
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	where tbl.subActivationCode = 'I'
	group by tbl.subscriberID, tbl.statusCode, i.assignedToMemberID, tbl.orgID
) as tmp 
inner join dbo.tr_invoices as i2 on i2.invoiceNumber = tmp.firstInvNumber and i2.statusID = 4
inner join ams_members m on m.memberID = i2.assignedToMemberID
	and m.orgID = tmp.orgID

-- E: First invoice this entire sub appears on is paid
insert into #firstInvoice (subscriberID, orgID, invoiceNumber)
select s.subscriberID, tstc.orgID, min(i.invoiceNumber) as invoiceNumber
from #tblSubsToCheck tstc
inner join dbo.sub_subscribers s on s.rootSubscriberID = tstc.rootSubscriberID
	and tstc.subActivationCode = 'E'
inner join dbo.tr_applications as tra on tra.itemID = s.subscriberID
	and tra.applicationTypeID = 17
	and tra.itemType = 'Dues'
	and tra.status = 'A'
inner join dbo.tr_invoiceTransactions as it on it.transactionID = tra.transactionID
inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
group by s.rootSubscriberID, s.subscriberID, tstc.orgID

CREATE INDEX IX_invoiceNumber on #firstInvoice (invoiceNumber asc)

insert into @tblSubsToMove (subscriberID, statusCode, reason)
select distinct tmp2.subscriberID, ts.statusCode, 'First invoice this entire sub'
from #firstInvoice as tmp2
inner join dbo.tr_invoices as i2 on i2.invoiceNumber = tmp2.invoiceNumber
	and i2.statusID = 4
inner join #tblSubsToCheck ts on ts.subscriberID = tmp2.subscriberID and ts.subActivationCode = 'E'
inner join ams_members m on m.memberID = i2.assignedToMemberID
	and m.orgID = tmp2.orgID

-- T: First Payment on This Sub
insert into @tblSubsToMove (subscriberID, statusCode, reason)
select tbl.subscriberID, tbl.statusCode, 'First payment this sub'
from #tblSubsToCheck as tbl
inner join dbo.tr_applications as tra on tra.itemID = tbl.subscriberID
	and tra.applicationTypeID = 17
	and tra.itemType = 'Dues'
	and tra.status = 'A'
cross apply dbo.fn_tr_transactionSalesWithDIT(tra.transactionID) as tsFull
where tbl.subActivationCode = 'T'
and ( tsFull.cache_activePaymentAllocatedAmount > 0 or tsFull.cache_amountAfterAdjustment = 0)
OPTION(RECOMPILE)

-- get the follow parents of subs already marked as activated
insert into @tblSubsToMove (subscriberID, statusCode, reason)
select s.subscriberID, st.statusCode, 'follow parents'
from sub_subscribers alreadyActivated
inner join sub_subscribers s
    on alreadyActivated.subscriberID = s.parentSubscriberID
    and alreadyActivated.paymentStatusID = @activatedStatusID
    and s.paymentStatusID = @nonActivatedStatusID
    and s.subActivationID = @followParentActivationOptionID
inner join dbo.sub_statuses st
	on st.statusID = s.statusID and st.statusCode <> 'D'
left outer join @tblSubsToMove alreadyFound
    on alreadyFound.subscriberID = s.subscriberID
where alreadyFound.subscriberID is null

-- get the follow parents of subs identified by previous inserts
insert into @tblSubsToMove (subscriberID, statusCode, reason)
select s.subscriberID, st.statusCode, 'follow parents'
from @tblSubsToMove ssmove
inner join sub_subscribers s
    on ssmove.subscriberID = s.parentSubscriberID
    and s.paymentStatusID = @nonActivatedStatusID
    and s.subActivationID = @followParentActivationOptionID
inner join dbo.sub_statuses st
	on st.statusID = s.statusID and st.statusCode <> 'D'
left outer join @tblSubsToMove alreadyFound
    on alreadyFound.subscriberID = s.subscriberID
where alreadyFound.subscriberID is null

if exists (select subscriberID from @tblSubsToMove) BEGIN

    while @@RowCount > 0
    begin
	   -- get the nested follow parents of subs identified by previous inserts
	   insert into @tblSubsToMove (subscriberID, statusCode, reason)
	   select s.subscriberID, st.statusCode, 'follow parents'
	   from @tblSubsToMove ssmove
	   inner join sub_subscribers s
		  on ssmove.subscriberID = s.parentSubscriberID
		  and s.paymentStatusID = @nonActivatedStatusID
		  and s.subActivationID = @followParentActivationOptionID
	   inner join dbo.sub_statuses st
		   on st.statusID = s.statusID and st.statusCode <> 'D'
	   left outer join @tblSubsToMove alreadyFound
		  on alreadyFound.subscriberID = s.subscriberID
	   where alreadyFound.subscriberID is null
    end


    update subs
    set subs.paymentStatusID = @activatedStatusID
    from dbo.sub_subscribers subs
    inner join @tblSubsToMove ts on ts.subscriberID = subs.subscriberID

    insert into dbo.sub_paymentStatusHistory(subscriberID, paymentStatusID, enteredByMemberID)
    select subscriberID, @activatedStatusID, @enteredByMemberID
    from @tblSubsToMove

    declare @minSubscriberID int, @currLoopMemberID int, @currLoopSubscriptionID int, @currLoopStatusCode char(1), @currLoopGroupID int, @tempCount int
    declare @prevSubscriberID int, @currLoopSiteID int, @insideResult int

    select @minSubscriberID=min(subscriberID)
    from @tblSubsToMove
    where statusCode in ('A','P')


    while @minSubscriberID is not null
    begin

	    select @currLoopSubscriptionID=s.subscriptionID, @currLoopMemberID=m.activeMemberID, @currLoopStatusCode=t.statusCode, @currLoopSiteID=st.siteID
	    from dbo.sub_subscribers s
	    inner join @tblSubsToMove t on t.subscriberID = s.subscriberID
	    inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID
	    inner join dbo.sub_types st on st.typeID = subs.typeID
	    inner join dbo.ams_members m on m.memberID = s.memberID
	    where s.subscriberID = @minSubScriberID
    	

	    select @tempCount=count(s.subscriberID)
	    from dbo.sub_subscribers s
	    inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID AND pst.statusCode = 'N'
	    inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @currLoopMemberID
	    where s.subscriptionID = @currLoopSubscriptionID
    	
	    IF @tempCount = 0
	    begin
		    select @currLoopGroupID=null
		    select @currLoopGroupID=groupID
		    from ams_groups
		    where groupCode like 'subWaitingPay_' + convert(varchar, @currLoopSubscriptionID) + '_tracking'

		    IF @currLoopGroupID is not null BEGIN
			    exec dbo.ams_deleteMemberGroup @currLoopMemberID, @currLoopGroupID
		    END
	    end

	    IF @currLoopStatusCode = 'A'
	    begin

		    select @prevSubscriberID=null
		    select @prevSubscriberID=s.subscriberID
		    from dbo.sub_subscribers s
		    inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode in ('A', 'I')
		    inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID 
		    inner join dbo.sub_types t on t.typeID = subs.typeID and t.siteID = @currLoopSiteID
		    inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @currLoopMemberID											
		    where s.subscriptionID = @currLoopSubscriptionID
		    and s.subscriberID <> @minSubscriberID

		    IF @prevSubscriberID is not null
		    BEGIN

				EXEC dbo.sub_updateSubscriberStatus @subscriberID=@prevSubscriberID, @newStatusCode='E', @siteID=@currLoopSiteID, @enteredByMemberID=@enteredByMemberID, @result=@insideResult OUTPUT

		    END

		    select @currLoopGroupID=null
		    select @currLoopGroupID=groupID
		    from ams_groups
		    where groupCode like 'subActive_' + convert(varchar, @currLoopSubscriptionID) + '_tracking'

		    IF @currLoopGroupID is not null
			    exec dbo.ams_createMemberGroup @currLoopMemberID, @currLoopGroupID, @bypassQueue
	    end
	    ELSE IF @currLoopStatusCode = 'P'
	    begin
		    select @currLoopGroupID=null
		    select @currLoopGroupID=groupID
		    from ams_groups
		    where groupCode like 'subPending_' + convert(varchar, @currLoopSubscriptionID) + '_tracking'

		    IF @currLoopGroupID is not null
			    exec dbo.ams_createMemberGroup @currLoopMemberID, @currLoopGroupID, @bypassQueue
	    end

	    select @minSubscriberID=min(subscriberID)
	    from @tblSubsToMove
	    where statusCode in ('A','P') and subscriberID > @minSubscriberID
    end
END

IF OBJECT_ID('tempdb..#firstInvoice') IS NOT NULL 
	DROP TABLE #firstInvoice

IF OBJECT_ID('tempdb..#tblSubsToCheck') IS NOT NULL 
	DROP TABLE #tblSubsToCheck


set nocount off

GO

-- (4) *****************************************************************************************************************************

ALTER PROCEDURE [dbo].[sub_updateSubscriberStatusBulk] 
AS
	-- expects temp table named #subscriberStatusChangesToProcess (definition below) to already exist and be populated
	-- CREATE TABLE #subscriberStatusChangesToProcess (autoid int IDENTITY(1,1), subscriberID int, newStatusCode varchar(1), siteID int, enteredByMemberID int, updateDate datetime, result int);
BEGIN
	declare 
		@subscriberID int,
		@newStatusCode varchar(1),
		@siteID int,
		@enteredByMemberID int,
		@updateDate datetime,
		@result int

	select @subscriberID = min(subscriberID) from #subscriberStatusChangesToProcess
	while @subscriberID is not null
	begin
		select
			@subscriberID = subscriberID,
			@newStatusCode = newStatusCode,
			@siteID = siteID,
			@enteredByMemberID = enteredByMemberID,
			@updateDate = updateDate,
			@result = 0
		from #subscriberStatusChangesToProcess sq
		where subscriberID = @subscriberID
	
		EXEC dbo.sub_updateSubscriberStatus @subscriberID=@subscriberID, @newStatusCode=@newStatusCode,	@siteID=@siteID, @enteredByMemberID=@enteredByMemberID, @updateDate=@updateDate, @result=@result OUTPUT
		
		update #subscriberStatusChangesToProcess set
			result = @result
		where subscriberID = @subscriberID

		select @subscriberID = min(subscriberID) from #subscriberStatusChangesToProcess where subscriberID > @subscriberID
	end



END
GO

