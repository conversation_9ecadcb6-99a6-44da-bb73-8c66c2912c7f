use customApps
GO
ALTER PROC [dbo].[job_payInvoices_grabForNotification]
AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @statusReady int, @statusGrabbed int
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'payInvoices'
		and qs.queueStatus = 'readyToNotify'
	select @statusGrabbed = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'payInvoices'
		and qs.queueStatus = 'grabbedForNotifying'

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL 
		DROP TABLE #tmpNotify
	
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier)

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueItems_payInvoices as qid ON qid.itemUID = qi.itemUID
		where qi.queueStatusID = @statusReady
			except
		select distinct qid.itemGroupUID
		from platformQueue.dbo.tblQueueItems as qi
		inner join platformQueue.dbo.tblQueueItems_payInvoices as qid ON qid.itemUID = qi.itemUID
		where qi.queueStatusID <> @statusReady
	)
	UPDATE platformQueue.dbo.tblQueueItems WITH (UPDLOCK, READPAST)
	SET queueStatusID = @statusGrabbed,
		dateUpdated = getdate()
		OUTPUT qid.itemGroupUID
		INTO #tmpNotify
	FROM platformQueue.dbo.tblQueueItems as qi
	INNER JOIN platformQueue.dbo.tblQueueItems_payInvoices as qid ON qid.itemUID = qi.itemUID
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qid.itemGroupUID
	where qi.queueStatusID = @statusReady

	-- get system account
	declare @enteredByMemberID int
	select @enteredByMemberID = memberID from membercentral.dbo.ams_members where memberNumber = 'SYSTEM' and orgID = 1

	-- return report information
	select tmpN.itemGroupUID, qid.itemUID, m2.memberID, 
		m2.lastname + ', ' + m2.firstname + isnull(' ' + m2.middlename,'') + ' (' + m2.membernumber + ')' as memberName,
		qid.paymentAmount, qidd.invoiceDueAmount, o.orgcode + membercentral.dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber,
		mpp.detail as payMethodDetail, mp.profileName as payProfileName, qid.paymentErrorMessage, o.orgName, h.datePaid,
		case when qid.recordedByMemberID = @enteredByMemberID then o.accountingEmail else m4e.email end as accountingEmail,
		case when qid.recordedByMemberID = @enteredByMemberID then 1 else 0 end as isSystemGenerated,
		sh.hostname, ip.profileName as invoiceProfileName, i.dateDue
	from (select distinct itemGroupUID from #tmpNotify) as tmpN
	INNER JOIN platformQueue.dbo.tblQueueItems_payInvoices as qid ON qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN platformQueue.dbo.tblQueueItems_payInvoicesDetail as qidd ON qidd.itemUID = qid.itemUID
	INNER JOIN membercentral.dbo.tr_invoices as i on i.invoiceID = qidd.invoiceID
	INNER JOIN membercentral.dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = i.assignedToMemberID
	INNER JOIN membercentral.dbo.ams_members as m2 on m2.memberid = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = qid.memberPaymentProfileID
	INNER JOIN membercentral.dbo.mp_profiles as mp on mp.profileID = mpp.profileID
	INNER JOIN membercentral.dbo.sites as s on s.siteID = mp.siteID
	INNER JOIN membercentral.dbo.siteHostnames as sh on sh.siteID = s.siteID and sh.hostnameID = sh.mainHostNameID
	INNER JOIN membercentral.dbo.organizations as o on o.orgID = qid.orgID
	LEFT OUTER JOIN membercentral.dbo.tr_paymentHistory as h on h.historyID = qid.paymentHistoryID
	INNER JOIN membercentral.dbo.ams_members as m3 on m3.memberID = qid.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as m4 on m4.memberid = m3.activeMemberID
	LEFT OUTER JOIN membercentral.dbo.ams_memberEmails as m4e 
		INNER JOIN membercentral.dbo.ams_memberEmailTypes as met on met.emailTypeID = m4e.emailTypeID and met.emailTypeOrder = 1
		on m4e.memberID = m4.memberID
	order by tmpN.itemGroupUID, case when isnull(qid.paymentErrorMessage,'') <> '' then 1 else 2 end, m2.lastname, m2.firstname, m2.middlename, m2.membernumber, qid.itemUID

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL 
		DROP TABLE #tmpNotify

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH
GO
