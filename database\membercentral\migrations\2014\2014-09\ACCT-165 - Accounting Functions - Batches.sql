-- create new functions
-- link to siteadmin and superadmin roles

use membercentral
GO

declare @resourceTypeID int, @functionID int, @resourceTypeFunctionID int
select @resourceTypeID = dbo.fn_getResourceTypeID('BatchAdmin')

EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='batchCreate', @displayName='Create Batch', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('batchCreate',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID

EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='batchEdit', @displayName='Edit Batch Infomation', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('batchEdit',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID

EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='batchClose', @displayName='Close Batch', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('batchClose',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID

EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='batchPost', @displayName='Post Batch', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('batchPost',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID

EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='batchOpen', @displayName='Reopen Batch', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('batchOpen',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID

EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='batchDownload', @displayName='Download Batch Reports', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('batchDownload',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID

EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='batchMoveTransaction', @displayName='Move Batch Transaction', @functionID=@functionID OUTPUT
	set @resourceTypeFunctionID = null
	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('batchMoveTransaction',@resourceTypeID))
	EXEC dbo.cms_createSiteResourceRoleFunction 9, @resourceTypeFunctionID
	EXEC dbo.cms_createSiteResourceRoleFunction 10, @resourceTypeFunctionID
GO
