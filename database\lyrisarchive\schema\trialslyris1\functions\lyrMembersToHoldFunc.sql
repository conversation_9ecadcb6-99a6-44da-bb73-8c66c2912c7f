ALTER FUNCTION lyrMembersToHoldFunc ( @OldestRecord datetime, @LastRunTime datetime ) 
RETURNS @return_table TABLE ( MemberID int, EmailAddr varchar(100), List varchar(60) ) 
AS 
BEGIN 
	DECLARE @MemberID int 
	DECLARE @CompletionStatusID int 
	DECLARE @FinalAttemptYear int 
	DECLARE @FinalAttemptDay int 
	DECLARE @EmailAddr_ varchar(100) 
	DECLARE @List_ varchar(60) 
	DECLARE @ErrHold_ tinyint 
	 
	DECLARE @FailureCount int 
	DECLARE @SkipMember bit 
	DECLARE @FailureDay int 
	DECLARE @CurMemberID int 
	 
	DECLARE @temp_cursor CURSOR 
	 
	SET @temp_cursor = CURSOR FOR  
		SELECT  
	               c.MemberID,  
	               m.EmailAddr_ as EmailAddr,  
	               m.List_ as List, 
	               DATEPART ( year, c.FinalAttempt ) as FinalAttemptYear,  
	               DATEPART ( dayofyear ,c.FinalAttempt ) as FinalAttemptDay,  
	               c.CompletionStatusID, 
	               l.ErrHold_ 
	           FROM  
	               lyrCompletedRecips c WITH (READUNCOMMITTED, INDEX = IX_CompletedRecipsMemberID) join  
	               members_ m WITH (READUNCOMMITTED, INDEX = PK_members_) on c.MemberID = m.MemberID_ join  
	               lists_ l WITH (READUNCOMMITTED) on l.Name_ = m.List_ 
	           WHERE 
	               c.MemberID IN (select c2.MemberID from lyrCompletedRecips c2 WITH (READUNCOMMITTED, INDEX = IX_CompletedRecipsFinalAttempt) where c2.FinalAttempt > @LastRunTime and c2.CompletionStatusID > 300 and c2.CompletionStatusID < 307) and  
	               c.FinalAttempt >= @OldestRecord and 
	               c.CompletionStatusID < 307 and  
	               m.MemberType_ = 'normal' and 
	               m.CleanAuto_ = 'F' and 
	               l.CleanAuto_ = 'F' 
	           ORDER BY c.MemberID, FinalAttemptYear DESC, FinalAttemptDay DESC, c.CompletionStatusID; 
	 
	OPEN @temp_cursor 
	 
	FETCH NEXT FROM @temp_cursor INTO @MemberID, @EmailAddr_, @List_, @FinalAttemptYear, @FinalAttemptDay, @CompletionStatusID, @ErrHold_ 
	 
	SET @CurMemberID = 0 
	WHILE @@FETCH_STATUS = 0 
	BEGIN 
		IF @CurMemberID <> @MemberID 
		BEGIN  
			SET @FailureCount = 0 
			SET @SkipMember = 0 
			SET @FailureDay = -1 
			SET @CurMemberID = @MemberID  
		END 
	 
		IF @SkipMember = 0  
		BEGIN 
			IF @CompletionStatusID = 300 -- 300=DB_COMPLETION_STATUS_SUCCESS 
			BEGIN 
				SET @SkipMember = 1 
			END 
			ELSE IF @CompletionStatusID > 300 AND @FinalAttemptDay <> @FailureDay -- 300=DB_COMPLETION_STATUS_SUCCESS 
			BEGIN 
				SET @FailureCount = @FailureCount + 1 
				SET @FailureDay = @FinalAttemptDay 
				IF @FailureCount >= @ErrHold_  
				BEGIN 
					-- Hold the member 
					INSERT INTO @return_table (MemberID, EmailAddr, List) VALUES (@MemberID, @EmailAddr_, @List_) 
					SET @SkipMember = 1 
				END 
			END 
		END 
	 
		FETCH NEXT FROM @temp_cursor INTO @MemberID, @EmailAddr_, @List_, @FinalAttemptYear, @FinalAttemptDay, @CompletionStatusID, @ErrHold_ 
	END			 
	RETURN 
END
GO
