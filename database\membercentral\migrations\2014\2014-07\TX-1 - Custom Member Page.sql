
-- delete existing pg=join
EXEC dbo.cms_deleteSiteResourceAndChildren 27098
GO

-- add custom page
declare @siteID int, @sectionID int, @zoneID int, @pgResourceTypeID int, @applicationInstanceID int, @siteResourceID int, 
	@pageID int, @resourceRightID int
		      
select @siteID = siteID from sites where siteCode = 'TX'
select @sectionID = sectionID from cms_pageSections where siteID = @siteID and sectionName = 'ABOUT' and sectionCode = 'AboutTTLA'
SELECT @zoneID = dbo.fn_getZoneID('Main')
SELECT @pgResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedPage')

EXEC dbo.cms_createApplicationInstance @siteid=@siteID, @languageID=1, @sectionID=@sectionID, @applicationTypeID=18,
	@isVisible=1, @pageName='MemberForm', @pageTitle='MemberForm', @pagedesc='TTLA Membership Application', @zoneID=@zoneID, @pageTemplateID=null,
	@pageModeID=null, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1,
	@applicationInstanceName='MemberForm', @applicationInstanceDesc='MemberForm', @applicationInstanceID=@applicationInstanceID OUTPUT,
	@siteResourceID=@siteResourceID OUTPUT, @pageID=@pageID OUTPUT
								
INSERT INTO cms_customPages(appInstanceID, customFileName)
VALUES(@applicationInstanceID,'MemberForm')

EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1, @functionID=4, @roleID=null, 
	@groupID=489, @memberID=null, @inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, @resourceRightID=@resourceRightID OUTPUT
GO



-- delete existing pg=join
--EXEC dbo.cms_deleteSiteResourceAndChildren 1161090
--GO
--
---- add custom page
--declare @siteID int, @sectionID int, @zoneID int, @pgResourceTypeID int, @applicationInstanceID int, @siteResourceID int, 
--	@pageID int, @resourceRightID int
--		      
--select @siteID = siteID from sites where siteCode = 'SC'
--select @sectionID = sectionID from cms_pageSections where siteID = @siteID and sectionName = 'Join SCAJ'
--SELECT @zoneID = dbo.fn_getZoneID('Main')
--SELECT @pgResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedPage')
--
--EXEC dbo.cms_createApplicationInstance @siteid=@siteID, @languageID=1, @sectionID=@sectionID, @applicationTypeID=18,
--	@isVisible=1, @pageName='Join', @pageTitle='Join SCAJ', @pagedesc='Join SCAJ', @zoneID=@zoneID, @pageTemplateID=null,
--	@pageModeID=null, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1,
--	@applicationInstanceName='Join SCAJ', @applicationInstanceDesc='Join SCAJ', @applicationInstanceID=@applicationInstanceID OUTPUT,
--	@siteResourceID=@siteResourceID OUTPUT, @pageID=@pageID OUTPUT
--								
--INSERT INTO cms_customPages(appInstanceID, customFileName)
--VALUES(@applicationInstanceID,'Join')
--
--EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1, @functionID=4, @roleID=null, 
--	@groupID=8810, @memberID=null, @inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, @resourceRightID=@resourceRightID OUTPUT
--GO


