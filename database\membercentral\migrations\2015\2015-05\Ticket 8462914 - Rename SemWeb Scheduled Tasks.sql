use membercentral
GO

update dbo.scheduledTasks
set [name] = 'Daily Complete Seminar Reminder'
where taskCFC = 'model.scheduledTasks.tasks.completeSeminarReminder'
GO

update dbo.scheduledTasks
set [name] = 'Bi-Monthly Credit Report'
where taskCFC = 'model.scheduledTasks.tasks.creditReport'
GO

update dbo.scheduledTasks
set [name] = 'Daily SWOD Credit Report'
where taskCFC = 'model.scheduledTasks.tasks.dailyCreditreport'
GO

update dbo.scheduledTasks
set [name] = 'Pharmacy Bi-Monthly CPE Monitor Report'
where taskCFC = 'model.scheduledTasks.tasks.CPEReport'
GO

update dbo.scheduledTasks
set [name] = 'TAGD Bi-Monthly Completions Report'
where taskCFC = 'model.scheduledTasks.tasks.creditCompletionsReport'
GO

update dbo.scheduledTasks
set [name] = 'TAGD Bi-Monthly SWOD Registrant Report'
where taskCFC = 'model.scheduledTasks.tasks.creditRegistrationsReport'
GO

update dbo.scheduledTasks
set [name] = 'Monthly Expired SWOD Credit Report'
where taskCFC = 'model.scheduledTasks.tasks.ExpiredSWODCredit'
GO

update dbo.scheduledTasks
set [name] = 'Pharmacy Bi-Monthly Credit Eval Report'
where taskCFC = 'model.scheduledTasks.tasks.bimonthlyCreditReport'
GO

update dbo.scheduledTasks
set [name] = 'Pharmacy Monthly SWL Enrollments Report'
where taskCFC = 'model.scheduledTasks.tasks.pharmacySWLEnrollments'
GO

update dbo.scheduledTasks
set [name] = 'Pharmacy Monthly SWOD Enrollments Report'
where taskCFC = 'model.scheduledTasks.tasks.pharmacySWODEnrollments'
GO

