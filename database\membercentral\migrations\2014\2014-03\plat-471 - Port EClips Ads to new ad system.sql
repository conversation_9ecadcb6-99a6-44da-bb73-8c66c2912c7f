use membercentral
GO

declare 
	@zoneTypeID int,
	@adTypeID int

-- Assumes Trialsmith Advertiser already created by plat-470
insert into dbo.ad_advertisers (
	name,
	orgID,
	status,
	dateCreated
)
select distinct s.sponsorname, 1, 'A', getDate()
from tlasites.trialsmith.dbo.eClipsPublications p
inner join tlasites.trialsmith.dbo.depoTLA t 
	on t.state = p.state
inner join tlasites.trialsmith.dbo.eClipsSponsorlinks l
	on p.publicationid = l.publicationid
inner join tlasites.trialsmith.dbo.eClipsSponsors s 
	on s.sponsorid = l.sponsorid
	and sponsorName != 'Trialsmith, Inc'
order by 1


select @zoneTypeID = zoneTypeID from dbo.ad_zoneTypes where zoneTypeName = 'EClips'

select @adTypeID = adTypeID from dbo.ad_types where adTypeName = 'EClips Image Ad'


insert into dbo.ad_advertiserZones (
	zoneID,
	advertiserID
)
select az.zoneID, a.advertiserID
from tlasites.trialsmith.dbo.eClipsPublications p
inner join tlasites.trialsmith.dbo.depoTLA t 
	on t.state = p.state
inner join tlasites.trialsmith.dbo.eClipsSponsorlinks l
	on p.publicationid = l.publicationid
inner join tlasites.trialsmith.dbo.eClipsSponsors s 
	on s.sponsorid = l.sponsorid
inner join dbo.ad_zones az
	on az.uniqueCode = p.publicationID
	and zoneTypeID = @zoneTypeID
inner join dbo.ad_advertisers a
	on a.name = s.sponsorName

insert into dbo.ad_ads(
	adName,
	advertiserID,
	adTypeID,
	imageUrl, 
	adShortText, 
	adLink,
	imageWidth, 
	imageHeight,
	status,
	dateCreated
)
select
	p.name, 
	a.advertiserID,
	@adTypeID,
	s.bannerImage,
	dbo.fn_RegExReplace(s.tagline1, '<[^>]*>', ''),
	s.bannerUrl,
	120,
	360,
	'A',
	getDate()
from tlasites.trialsmith.dbo.eClipsPublications p
inner join tlasites.trialsmith.dbo.depoTLA t 
	on t.state = p.state
inner join tlasites.trialsmith.dbo.eClipsSponsorlinks l
	on p.publicationid = l.publicationid
inner join tlasites.trialsmith.dbo.eClipsSponsors s 
	on s.sponsorid = l.sponsorid
inner join dbo.ad_zones az
	on az.uniqueCode = p.publicationID
	and zoneTypeID = @zoneTypeID
inner join dbo.ad_advertisers a
	on a.name = s.sponsorName




insert into dbo.ad_adPlacements (
	adID,
	zoneID,
	status
)
select 
	aa.adID, 
	az.zoneID, 
	'A'
from tlasites.trialsmith.dbo.eClipsPublications p
inner join tlasites.trialsmith.dbo.depoTLA t 
	on t.state = p.state
inner join tlasites.trialsmith.dbo.eClipsSponsorlinks l
	on p.publicationid = l.publicationid
inner join tlasites.trialsmith.dbo.eClipsSponsors s 
	on s.sponsorid = l.sponsorid
inner join dbo.ad_zones az
	on az.uniqueCode = p.publicationID
	and zoneTypeID = @zoneTypeID
inner join dbo.ad_advertisers a
	on a.name = s.sponsorName
inner join dbo.ad_ads aa
	on aa.advertiserID = a.advertiserID
	and aa.adName = p.name


insert into dbo.ad_adPlacementRotation (
	adPlacementID
)
select 
	adPlacementID
from
	ad_adPlacements ap
	inner join dbo.ad_ads aa on
		aa.adID = ap.adID
		and aa.advertiserID in (
				select a.advertiserID
				from tlasites.trialsmith.dbo.eClipsPublications p
				inner join tlasites.trialsmith.dbo.depoTLA t 
					on t.state = p.state
				inner join tlasites.trialsmith.dbo.eClipsSponsorlinks l
					on p.publicationid = l.publicationid
				inner join tlasites.trialsmith.dbo.eClipsSponsors s 
					on s.sponsorid = l.sponsorid
				inner join dbo.ad_zones az
					on az.uniqueCode = p.publicationID
					and zoneTypeID = @zoneTypeID
				inner join dbo.ad_advertisers a
					on a.name = s.sponsorName
				)


GO

