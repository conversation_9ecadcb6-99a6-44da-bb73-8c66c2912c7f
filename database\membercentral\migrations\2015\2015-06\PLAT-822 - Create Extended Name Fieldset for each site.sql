use membercentral
go

select distinct sites.site<PERSON>, o.hasPrefix, o.hasMiddleName, o.hasSuffix, o.hasProfessionalSuffix
into #tmpSites
from dbo.admin_siteToolRestrictions tr
inner join dbo.admin_toolTypes tt on tt.tooltypeID = tr.tooltypeID
inner join dbo.sites on sites.siteID = tr.siteID
inner join dbo.organizations as o on o.orgID = sites.orgID
where (tt.toolCFC like 'Reports.custom.' + sites.sitecode + '.%' or (tt.toolCFC like 'Reports.%' and left(tt.toolCFC,15) <> 'Reports.custom.'))

declare @siteID int, @fieldsetID int, @fieldID int, @hasPrefix bit, @hasMiddleName bit, @hasSuffix bit, @hasProfessionalSuffix bit, @useID int, @siteResourceID int
select @siteID = min(siteID) from #tmpSites
while @siteID is not null begin
	SET @fieldsetID = null

	select @hasPrefix=hasPrefix, @hasMiddleName=hasMiddleName, @hasSuffix=hasSuffix, @hasProfessionalSuffix=hasProfessionalSuffix
	from #tmpSites
	where siteID = @siteID

	EXEC dbo.ams_createMemberFieldset @siteID=@siteID, @fieldsetName='Reports - Extended Name', @nameformat='LSXPFM', @showHelp=0, 
		@enteredByMemberID=461530, @defaultLanguageID=1, @fieldsetID=@fieldsetID OUTPUT
	
	IF @hasPrefix = 1
		EXEC dbo.ams_createMemberField @fieldsetID, 'm_prefix', 'Prefix', '', 0, 0, @fieldID OUTPUT
	EXEC dbo.ams_createMemberField @fieldsetID, 'm_firstname', 'First Name', '', 0, 0, @fieldID OUTPUT
	IF @hasMiddleName = 1
		EXEC dbo.ams_createMemberField @fieldsetID, 'm_middlename', 'Middle Name', '', 0, 0, @fieldID OUTPUT
	EXEC dbo.ams_createMemberField @fieldsetID, 'm_lastname', 'Last Name', '', 0, 0, @fieldID OUTPUT
	IF @hasSuffix = 1
		EXEC dbo.ams_createMemberField @fieldsetID, 'm_suffix', 'Suffix', '', 0, 0, @fieldID OUTPUT
	IF @hasProfessionalSuffix = 1
		EXEC dbo.ams_createMemberField @fieldsetID, 'm_professionalsuffix', 'Professional Suffix', '', 0, 0, @fieldID OUTPUT
	
	select top 1 @siteResourceID = sr.siteResourceID 
	from dbo.cms_siteResources as sr
	inner join dbo.cms_siteResourceTypes as srt on srt.resourceTypeID = sr.resourceTypeID and srt.resourceType = 'rpt_ReportSettings'
	where sr.siteID = @siteID
	and sr.siteResourceStatusID = 1

	EXEC dbo.ams_createMemberFieldUsage @siteResourceID=@siteResourceID, @fieldsetID=@fieldsetID, @area='custom', @createSiteResourceID=1, @useID=@useID OUTPUT

	select @siteID = min(siteID) from #tmpSites where siteID > @siteID
end

DROP TABLE #tmpSites
GO


CREATE PROC [dbo].[ams_createDefaultExtendedNameFieldset]
@siteID int

AS

set nocount on

DECLARE @fsid int, @fieldID int, @enteredByMemberID int
DECLARE @hasPrefix bit, @hasMiddleName bit, @hasSuffix bit, @hasProfessionalSuffix bit, @siteResourceID int, @useID int

select @enteredByMemberID=activeMemberID from dbo.ams_members where orgid = 1 and lastname like 'System'

select @hasPrefix=o.hasPrefix, @hasMiddleName=o.hasMiddleName, @hasSuffix=o.hasSuffix, @hasProfessionalSuffix=o.hasProfessionalSuffix
from dbo.sites 
inner join dbo.organizations as o on o.orgID = sites.orgID
where sites.siteID = @siteID

select top 1 @siteResourceID = sr.siteResourceID 
from dbo.cms_siteResources as sr
inner join dbo.cms_siteResourceTypes as srt on srt.resourceTypeID = sr.resourceTypeID and srt.resourceType = 'rpt_ReportSettings'
where sr.siteID = @siteID
and sr.siteResourceStatusID = 1


DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	EXEC dbo.ams_createMemberFieldset @siteID, 'Reports - Extended Name', 'LSXPFM', 0, @enteredByMemberID, 1, @fsid OUTPUT

	IF @hasPrefix = 1
		EXEC dbo.ams_createMemberField @fsid, 'm_prefix', 'Prefix', '', 0, 0, @fieldID OUTPUT
	EXEC dbo.ams_createMemberField @fsid, 'm_firstname', 'First Name', '', 0, 0, @fieldID OUTPUT
	IF @hasMiddleName = 1
		EXEC dbo.ams_createMemberField @fsid, 'm_middlename', 'Middle Name', '', 0, 0, @fieldID OUTPUT
	EXEC dbo.ams_createMemberField @fsid, 'm_lastname', 'Last Name', '', 0, 0, @fieldID OUTPUT
	IF @hasSuffix = 1
		EXEC dbo.ams_createMemberField @fsid, 'm_suffix', 'Suffix', '', 0, 0, @fieldID OUTPUT
	IF @hasProfessionalSuffix = 1
		EXEC dbo.ams_createMemberField @fsid, 'm_professionalsuffix', 'Professional Suffix', '', 0, 0, @fieldID OUTPUT
	
	EXEC dbo.ams_createMemberFieldUsage @siteResourceID=@siteResourceID, @fieldsetID=@fsid, @area='custom', @createSiteResourceID=1, @useID=@useID OUTPUT


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[enableSiteFeature]
	@siteID int,
	@toolTypeList varchar(1000)
AS


declare @tblTools TABLE (toolType varchar(100))
insert into @tblTools (toolType)
select listItem from dbo.fn_varCharListToTable(@toolTypeList,',')


declare @applicationInstanceID int
select @applicationInstanceID = applicationInstanceID
	from dbo.cms_applicationInstances
	where siteID = @siteID
	and applicationInstanceName = 'admin'


declare @toolType varchar(100)
SELECT @toolType = min(toolType) from @tblTools
WHILE @toolType is not null BEGIN

	-- Accrual Accounting
	if @toolType = 'AccrualAccounting' begin
		declare @orgID int, @GLAccountID int
		select @orgID = orgID from dbo.sites where siteID = @siteID

		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''useAccrualAccounting'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=5, @accountName='Deferred Revenue Accounts', @accountCode='', @GLCode='DEFERREDREVENUE', @parentGLAccountID=null, @invoiceProfileID=null, @isSystemAccount=1, @isTaxable=0, @invoiceContentID=null, @deferredGLAccountID=null, @GLAccountID=@GLAccountID output
		EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=5, @accountName='Deferred Sales Tax Accounts', @accountCode='', @GLCode='DEFERREDTAX', @parentGLAccountID=null, @invoiceProfileID=null, @isSystemAccount=1, @isTaxable=0, @invoiceContentID=null, @deferredGLAccountID=null, @GLAccountID=@GLAccountID output

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType in ('AccrualScheduleReport','DeferredIncomeAnalysisReport')
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- appt tracker
	if @toolType = 'AppointmentTrackerAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showApptTracker'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- email blast
	if @toolType = 'EmailBlast' begin
		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- member documents
	if @toolType = 'MemberDocs' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showMemberDocuments'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		declare @resourceTypeID int, @sectionID int, @parentSectionID int
		select @resourceTypeID = resourceTypeID from dbo.cms_siteResourceTypes where resourceType = 'ApplicationCreatedSection'
		select @sectionID = sectionID from dbo.cms_pageSections where siteID = @siteID and sectionName = 'MCAMSMemberDocuments'
		IF @sectionID is null begin
			select @parentSectionID = sectionID from dbo.cms_pageSections where sectionName = 'root' and parentSectionID is null and siteID = @siteID
			exec dbo.cms_createPageSection @siteID, @resourceTypeID, null, null, null, @parentSectionID, 'MCAMSMemberDocuments', 'MCAMSMemberDocuments', 0, @sectionID output
		end
	end	

	-- member history
	if @toolType = 'MemberHistoryAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showMemberHistory'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID
	end

	-- referrals
	if @toolType = 'ReferralsAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showReferrals'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- relationships
	if @toolType = 'RelationshipAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showRelationships'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- reports
	if @toolType = 'Reports' begin
		declare @siteCode varchar(10)
		select @sitecode = sitecode from dbo.sites where siteID = @siteID

		insert into dbo.admin_siteToolRestrictions (toolTypeID, siteID)
		select tooltypeid, @siteID 
		from dbo.admin_toolTypes 
		where toolCFC like 'Reports.custom.' + @sitecode + '.%'
		or (toolCFC like 'Reports.%' and left(toolCFC,15) <> 'Reports.custom.')
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID

		EXEC ams_createDefaultExtendedNameFieldset @siteID=@siteID
	end

	-- subscriptions
	if @toolType = 'SubscriptionAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showSubscriptions'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = 'SubRenewalAdmin'
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID

		-- subsriber download fieldset
		EXEC cms_createDefaultSubscriberDownloadFieldset @siteID=@siteID
	end

	-- tasks
	if @toolType = 'NotesAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showNotes'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	SELECT @toolType = min(toolType) from @tblTools where toolType > @toolType
END


-- refresh and assign resources
exec dbo.createadminsuite @siteid


-- these need to be added after resources are there (createadminsuite)
IF EXISTS (select toolType from @tblTools where toolType = 'AppointmentTrackerAdmin')
	exec dbo.cms_createDefaultAppointmentCategories @siteID=@siteid, @contributingMemberID=461530

IF EXISTS (select toolType from @tblTools where toolType = 'EmailBlast')
	exec dbo.cms_createDefaultEmailBlastCategories @siteID=@siteID

IF EXISTS (select toolType from @tblTools where toolType = 'MemberHistoryAdmin')
	exec dbo.cms_createDefaultHistoryAdminCategories @siteID=@siteID, @contributingMemberID=461530

IF EXISTS (select toolType from @tblTools where toolType = 'RelationshipAdmin')
	exec dbo.cms_createDefaultRelationshipCategories @siteID=@siteID, @contributingMemberID=461530

IF EXISTS (select toolType from @tblTools where toolType = 'NotesAdmin')
	exec dbo.cms_createDefaultNotesCategories @siteID=@siteid, @contributingMemberID=461530

RETURN 0
GO

