ALTER PROC dbo.mc_notifyListMemberIssues

AS

DECLARE @emailSubject varchar(200);

-- send email if there are members with no email address
IF EXISTS (SELECT 1 FROM dbo.members_ WHERE EmailAddr_ = '') BEGIN
	SET @emailSubject = 'Lyris Members with blank email address';

	EXEC membercentral.platformMail.dbo.email_sendMessageSupport
		@errorSubject=@emailSubject,
		@errorTitle=@emailSubject,
		@messageContent='There are records in dbo.members_ WHERE EmailAddr_ is an empty string.',
		@forDev=1;
END

RETURN 0;
GO
