use seminarWeb
GO
CREATE FUNCTION dbo.fn_swl_getEnrollmentsForAdmin (@depoMemberDataID int)
RETURNS TABLE 
AS
RETURN 
(
	SELECT e.enrollmentID, e.userid, s.seminarID, eswl.goToMeetingUID, ps.pin, eswl.swlcode, p.orgcode as Sign<PERSON>p<PERSON>rgCode, 
		p.catalogURL, p.orgcode as publisher<PERSON>rg<PERSON>ode, s.seminarName, sswl.dateStart, sswl.dateEnd, sswl.swltypeID, 
		d.depomemberdataID, d.FirstName, d.LastName, d.Fax, d.Email, e.dateEnrolled, eswl.attended, eswl.attendedPhone, 
		eswl.joinTime, eswl.exitTime, eswl.duration, eswl.durationPhone, eswl.completedPolling, e.dateCompleted, e.passed, 
		(SELECT COUNT(*) FROM dbo.tblLogSWLive AS log1 WHERE enrollmentID = e.enrollmentID AND seminarID = e.seminarID AND contact LIKE '%@%') AS EmailCount,
		(SELECT COUNT(*) FROM dbo.tblLogSWLive AS log2 WHERE enrollmentID = e.enrollmentID AND seminarID = e.seminarID AND contact NOT LIKE '%@%') AS FaxCount,
		(SELECT COUNT(*) FROM dbo.tblEnrollmentsAndCredit WHERE enrollmentID = e.enrollmentID) AS CreditCount,
		pgtm.parkedID, sswl.phoneAttendee, sswl.codeAttendee, s.isDeleted, e.isActive, sswl.premiereUsePIN, sswl.goToMeetingID, sswl.isOpen, 
		sswl.materialsLink, s.offerCertificate, 
		CASE 
		WHEN (select count(saf.formID)
				FROM dbo.tblEnrollments AS e2
				INNER JOIN dbo.tblSeminarsAndForms AS saf ON e2.seminarID = saf.seminarID
				LEFT OUTER JOIN dbo.tblSeminarsAndFormResponses as safr 
					INNER JOIN formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID and r.isactive = 1
					INNER JOIN formbuilder.dbo.tblForms as f on f.formID = r.formID
						and f.isPublished = 1
						and getdate() between f.dateStartPublish and f.dateEndPublish	
					on safr.seminarFormID = saf.seminarFormID AND safr.enrollmentID = e2.enrollmentID
				WHERE e2.enrollmentID = e.enrollmentID
				AND saf.loadPoint = 'evaluation'
				AND exists(
					SELECT sac.seminarCreditID, eac.idNumber, eac.earnedCertificate, caswl.evaluationRequired
					FROM dbo.tblEnrollmentsAndCredit AS eac 
					INNER JOIN dbo.tblSeminarsAndCredit AS sac ON eac.seminarCreditID = sac.seminarCreditID 
					INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON sac.CSALinkID = csa.CSALinkID 
					INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
					INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID 
					INNER JOIN dbo.tblCreditStatuses AS cstat ON sac.statusID = cstat.statusID
					INNER JOIN dbo.tblCreditAuthoritiesSWLive as caswl on caswl.authorityID = ca.authorityID
					WHERE eac.enrollmentID = e.enrollmentID
					and evaluationRequired = 1)
				AND (safr.responseID is null or r.dateCompleted is null)) > 0 THEN 0
		ELSE 1
		END as allEvaluationCompleted
	FROM dbo.tblEnrollments AS e 
	INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
	INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
	INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
	INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
	INNER JOIN dbo.tblSeminarsSWLive AS sswl ON e.seminarID = sswl.seminarID
	INNER JOIN dbo.tblSeminars as s on s.seminarID = sswl.seminarID
	INNER JOIN dbo.tblParticipants AS pub ON pub.participantID = s.participantID 
	LEFT OUTER JOIN dbo.tblParkedGTMSeats AS pgtm ON e.enrollmentID = pgtm.enrollmentID
	LEFT OUTER JOIN dbo.tblParkedPhoneSeats AS ps ON e.enrollmentID = ps.enrollmentID
	WHERE u.depomemberdataID = @depoMemberDataID
)
GO


CREATE FUNCTION dbo.fn_swod_getEnrollmentsForAdmin (@depoMemberDataID int)
RETURNS TABLE 
AS
RETURN 
(
	SELECT e.enrollmentID, p.orgcode as signUpOrgCode, e.dateEnrolled, s.offerCertificate, 
		(SELECT COUNT(*) FROM dbo.tblLogSWOD AS log1 WHERE enrollmentID = e.enrollmentID AND seminarID = e.seminarID AND contact LIKE '%@%') AS EmailCount,
		(SELECT COUNT(*) FROM dbo.tblEnrollmentsAndCredit WHERE enrollmentID = e.enrollmentID) AS CreditCount,
		CASE 
		WHEN e.passed = 1 and len(e.datecompleted) > 0 THEN 1
		WHEN e.passed = 0 and len(e.datecompleted) > 0 THEN 2
		ELSE 3
		END as Progress, s.seminarID, s.seminarName, s.isDeleted, pub.orgcode as publisherOrgCode, e.isActive, d.Email,
		d.depomemberdataID, d.FirstName, d.LastName,
		CASE
		WHEN len(e.dateCompleted) > 0 THEN 1
		ELSE 0
		END as showCertButton
	FROM dbo.tblEnrollments AS e 
	INNER JOIN dbo.tblEnrollmentsSWOD AS eswod ON e.enrollmentID = eswod.enrollmentID 
	INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
	INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
	INNER JOIN dbo.tblSeminars as s on s.seminarID = e.seminarID
	INNER JOIN dbo.tblParticipants AS pub ON pub.participantID = s.participantID 
	INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
	WHERE u.depomemberdataID = @depoMemberDataID
)
GO

CREATE FUNCTION dbo.fn_swtl_getEnrollmentsForAdmin (@depoMemberDataID int)
RETURNS TABLE 
AS
RETURN 
(
	SELECT e.enrollmentID, t.titleID, t.isDeleted, t.titleName, pub.orgcode as publisherOrgCode, e.dateEnrolled,
		e.isActive, p.orgcode as signUpOrgCode,  
		(SELECT COUNT(*) FROM dbo.tblLogSWTL AS log1 WHERE enrollmentID = e.enrollmentID AND titleID = e.titleID AND contact LIKE '%@%') AS EmailCount
	FROM dbo.tblEnrollments AS e 
	INNER JOIN dbo.tblEnrollmentsSWTL AS eswtl ON e.enrollmentID = eswtl.enrollmentID 
	INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
	INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
	INNER JOIN dbo.tblTitles as t on t.titleID = e.titleID
	INNER JOIN dbo.tblParticipants AS pub ON pub.participantID = t.participantID 
	WHERE u.depomemberdataID = @depoMemberDataID
)
GO

CREATE FUNCTION dbo.fn_swcp_getEnrollmentsForAdmin (@depoMemberDataID int)
RETURNS TABLE 
AS
RETURN 
(
	SELECT DISTINCT cp.programID, cp.programName, p.orgcode as publisherOrgCode, sss.semstatusstring, u.userID,
		d.FirstName + ' ' + d.LastName as member
	FROM dbo.tblCertificatePrograms AS cp 
	INNER JOIN dbo.tblParticipants as p on p.participantID = cp.participantID
	INNER JOIN dbo.tblCertificateProgramItems AS cpi ON cp.programID = cpi.programID 
	INNER JOIN dbo.tblSeminars AS s ON cpi.seminarID = s.seminarID 
	INNER JOIN (
		select distinct xcp.programid, xu.userID
		from dbo.tblCertificatePrograms AS xcp 
		INNER JOIN dbo.tblCertificateProgramItems AS xcpi ON xcp.programID = xcpi.programID 
		INNER JOIN dbo.tblEnrollments AS xe ON xcpi.seminarID = xe.seminarID 
		INNER JOIN dbo.tblUsers as xu on xu.userID = xe.userID
		where xu.depoMemberDataID = @depoMemberDataID
		and xe.isactive = 1
	) as tmp on tmp.programid = cp.programid
	INNER JOIN dbo.tblUsers AS u ON u.userID = tmp.userID 
	INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
	cross apply dbo.swcp_getSeminarStatusString(cp.programID,u.userID) as sss
)
GO

DROP PROC dbo.sw_getEnrollmentHistoryForAdminByUserID
GO

DROP PROC dbo.sw_getEnrollmentHistoryForAdminByDepoMemberDataID
GO
