use membercentral
GO

/* Add new columns to rates table */
ALTER TABLE dbo.sub_rates ADD
	recogStartDate datetime NULL,
	recogEndDate datetime NULL,
	recogStartDateAFID int NULL,
	recogEndDateAFID int NULL,
	recogAFStartDate datetime NULL,
	recogAFEndDate datetime NULL
GO

ALTER TABLE dbo.sub_rates ADD CONSTRAINT
	FK_sub_rates_sub_advanceFormulas5 FOREIGN KEY
	(
	recogStartDateAFID
	) REFERENCES dbo.sub_advanceFormulas
	(
	AFID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO
ALTER TABLE dbo.sub_rates ADD CONSTRAINT
	FK_sub_rates_sub_advanceFormulas6 FOREIGN KEY
	(
	recogEndDateAFID
	) REFERENCES dbo.sub_advanceFormulas
	(
	AFID
	) ON UPDATE  NO ACTION 
	 ON DELETE  NO ACTION 
	
GO

-- prefill data. some rates dont end at .997 so we need to make these dates do that.
update dbo.sub_rates
set recogStartDate = DATEADD(dd,DATEDIFF(dd,0,termStartDate),0),
	recogEndDate = dateadd(ms,-3,dateadd(dd,1,DATEADD(dd,DATEDIFF(dd,0,termEndDate),0))),
	recogStartDateAFID = termStartDateAFID,
	recogEndDateAFID = termEndDateAFID,
	recogAFStartDate = DATEADD(dd,DATEDIFF(dd,0,termAFStartDate),0),
	recogAFEndDate = dateadd(ms,-3,dateadd(dd,1,DATEADD(dd,DATEDIFF(dd,0,termAFEndDate),0)))
GO

ALTER TABLE sub_rates ALTER COLUMN recogStartDate datetime NOT NULL
GO
ALTER TABLE sub_rates ALTER COLUMN recogEndDate datetime NOT NULL
GO
ALTER TABLE sub_rates ALTER COLUMN recogAFStartDate datetime NOT NULL
GO
ALTER TABLE sub_rates ALTER COLUMN recogAFEndDate datetime NOT NULL
GO



ALTER PROCEDURE [dbo].[sub_createRate] 
@scheduleID int,
@rateName varchar(100),
@reportCode varchar(15),
@status char(1),
@rateStartDate datetime,
@rateEndDate datetime,
@rateStartAFID INT,
@rateEndAFID INT,
@termStartDate datetime,
@termEndDate datetime,
@termStartAFID INT,
@termEndAFID INT,
@graceEndDate datetime,
@graceEndAFID INT,
@recogStartDate datetime,
@recogEndDate datetime,
@recogStartAFID INT,
@recogEndAFID INT,
@rateAdvanceOnTermEnd INT,
@isRenewalRate INT,
@forceUpfront INT,
@accountID INT,
@frontEndAllowChangePrice INT,
@linkedNonRenewalRateID INT,
@fallbackRenewalRateID INT,
@keepChangedPriceOnRenewal INT,
@frontEndChangePriceMin money,
@frontEndChangePriceMax money,
@rateID int OUTPUT,
@siteResourceID int OUTPUT

AS

SET NOCOUNT ON

-- null output variables
select @rateID = null, @siteResourceID = null

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @siteID int, @siteResourceTypeID int, @siteResourceStatusID int

	select @siteID = siteid from dbo.sub_rateSchedules where scheduleID = @scheduleID
	select @siteResourceTypeID = dbo.fn_getResourceTypeID('SubscriptionRate')
	select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

	if (@frontEndChangePriceMin is not null) and (@frontEndChangePriceMax is not null) and (@frontEndChangePriceMin > @frontEndChangePriceMax) BEGIN
		set @frontEndChangePriceMin = null;
		set @frontEndChangePriceMax = null;
	END


	-- create a resourceID for the rate
	exec dbo.cms_createSiteResource @resourceTypeID=@siteResourceTypeID, @siteResourceStatusID=@siteResourceStatusID,
		@siteID=@siteid, @isVisible=1, @parentSiteResourceID=null, @siteResourceID=@siteResourceID OUTPUT

	-- add rate
	INSERT INTO dbo.sub_rates (scheduleID, siteResourceID, [status], 
		rateStartDate, rateEndDate, rateStartDateAFID, rateEndDateAFID, rateAFStartDate, rateAFEndDate, 
		termStartDate, termEndDate, termStartDateAFID, termEndDateAFID, termAFStartDate, termAFEndDate, 
		graceEndDate, graceAFID, 
		recogStartDate, recogEndDate, recogStartDateAFID, recogEndDateAFID, recogAFStartDate, recogAFEndDate, 
		rateName, rateAdvanceOnTermEnd, isRenewalRate, forceUpfront, reportCode, GLAccountID, 
		frontEndAllowChangePrice, linkedNonRenewalRateID, fallbackRenewalRateID, keepChangedPriceOnRenewal, 
		frontEndChangePriceMin, frontEndChangePriceMax)
	VALUES (@scheduleID, @siteResourceID, @status, 
		@rateStartDate, @rateEndDate, @rateStartAFID, @rateEndAFID, @rateStartDate, @rateEndDate, 
		@termStartDate, @termEndDate, @termStartAFID, @termEndAFID, @termStartDate, @termEndDate, 
		@graceEndDate, @graceEndAFID, 
		@recogStartDate, @recogEndDate, @recogStartAFID, @recogEndAFID, @recogStartDate, @recogEndDate, 
		@rateName, @rateAdvanceOnTermEnd, @isRenewalRate, @forceUpfront, @reportCode, @accountID,
		@frontEndAllowChangePrice, @linkedNonRenewalRateID, @fallbackRenewalRateID, @keepChangedPriceOnRenewal,
		@frontEndChangePriceMin, @frontEndChangePriceMax)

	select @rateID = SCOPE_IDENTITY()


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	SELECT @rateID = 0
	SELECT @siteResourceID = 0

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROCEDURE [dbo].[sub_getRatesForAdminByScheduleID] 
	@scheduleID int
AS

declare @rtid int, @rfid int
select @rtid = dbo.fn_getResourceTypeID('SubscriptionRate')
select @rfid = dbo.fn_getResourceFunctionID('Qualify',@rtid) 

select case when LEN(r.rateName) = 0 then '(name not set)' else r.rateName end as rateName, r.rateID, r.status, 
	r.rateStartDate, r.rateEndDate, r.rateAFStartDate, r.rateAFEndDate,
	r.termStartDate, r.termEndDate, r.termAFStartDate, r.termAFEndDate,
	r.recogStartDate, r.recogEndDate, r.recogAFStartDate, r.recogAFEndDate,
	r.siteResourceID, r.isRenewalRate, r.forceUpfront, g.groupID, g.groupName, srrc.include
from dbo.sub_rates r
inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc in ('Active', 'Deleted')
left outer join dbo.cms_siteResourceRightsCache as srrc
	INNER JOIN dbo.ams_groups as g on g.groupID = srrc.groupID 
	on srrc.resourceid = r.siteresourceID AND srrc.functionID = @rfid and g.status = 'A'
where r.scheduleID = @scheduleID
ORDER BY r.rateID

GO

ALTER PROC [dbo].[sub_exportSetup]
@siteID int,
@fileName varchar(400)

AS

declare @orgID int
select @orgID = orgID from dbo.sites where siteID = @siteID

IF OBJECT_ID('tempdb..#SubSetup') IS NOT NULL 
	DROP TABLE #SubSetup

; with allGLS as (
	select gl.GlAccountID, gl.uid, gl.thePathExpanded, gl.accountCode, gl.status, ip.profileName, ip.enableAutoPay
	from dbo.fn_getRecursiveGLAccounts(@orgID) as gl
	inner join dbo.tr_invoiceProfiles as ip on ip.profileID = gl.invoiceProfileID
	where gl.AccountType = 'Revenue'
)
select t.uid as [Subscription Type UID], t.typeName as [Subscription Type], t.feEmailNotification as feEmailNotification,
	fec.rawcontent as [Subscription Type Front End Content], fecc.rawcontent as [Subscription Type Front End Completed Content],

	s.uid as [Subscription UID], s.subscriptionName as [Subscription Name], 
	case s.status when 'A' then 'Active' when 'I' then 'Inactive' when 'D' then 'Deleted' end as [Subscription Status],
	case s.autoExpire when 1 then 'Yes' else 'No' end as [Subscription Auto Expire],
	case s.soldSeparately when 1 then 'Yes' else 'No' end as [Subscription Sold Separately],
	case s.rateTermDateFlag 
		when 'A' then 'Adhere to term dates in rate' 
		when 'S' then 'Use current day as term start date (adhere to term end date in rate)'
		when 'C' then 'Calculate term end date for term length (term start date is current day)'
		end as [Subscription Term Dates],
	s.comments as [Subscription Comments],
	case s.isFeatured when 1 then 'Yes' else 'No' end as [Subscription Featured],
	s.paymentOrder as [Subscription Payment Order],
	s.reportCode as [Subscription Report Code],
	ao.subActivationName as [Subscription Activation Option],
	aoAlt.subActivationName as [Subscription Alternate Activation Option],
	case s.allowRateGLAccountOverride when 1 then 'Yes' else 'No' end as [Subscription Allow Rate GL Account Override],
	et.templateName as [Subscription Welcome Email Template],
	etRenew.templateName as [Subscription Renew Email Template],
	sfec.rawcontent as [Subscription Front End Content], sfecc.rawcontent as [Subscription Front End Completed Content],

	glSub.uid as [Subscription GL Account UID], 
	glSub.thePathExpanded + isnull(' (' + nullIf(glSub.AccountCode,'') + ')','') as [Subscription GL Account],
	case glSub.status when 'A' then 'Active' when 'I' then 'Inactive' when 'D' then 'Deleted' end as [Subscription GL Account Status],
	glSub.profileName as [Subscription GL Account Invoice Profile],
	case glSub.enableAutoPay when 1 then 'Yes' else 'No' end as [Subscription GL Account Invoice Profile AutoPay Enabled],

	rs.uid as [Subscription Rate Schedule UID],
	rs.scheduleName as [Subscription Rate Schedule Name],
	case rs.status when 'A' then 'Active' when 'I' then 'Inactive' when 'D' then 'Deleted' end as [Subscription Rate Schedule Status],

	r.uid as [Rate UID], r.rateName as [Rate Name],
	case r.isRenewalRate when 0 then 'Join Rate' else 'Renewal Rate' end as [Rate Type], 
	case r.status when 'A' then 'Active' when 'I' then 'Inactive' when 'D' then 'Deleted' end as [Rate Status],
	r.reportCode as [Rate Report Code],
	r.rateStartDate as [Rate Available Start Date], r.rateEndDate as [Rate Available End Date], 
	r.rateAFStartDate as [Rate Available AF Start Date], r.rateAFEndDate as [Rate Available AF End Date], 
	r.termStartDate as [Rate Term Start Date], r.termEndDate as [Rate Term End Date], 
	r.termAFStartDate as [Rate Term AF Start Date], r.termAFEndDate as [Rate Term AF End Date], 
	r.graceEndDate as [Rate Grace End Date], 
	r.recogStartDate as [Rate Recognized Start Date], r.recogEndDate as [Rate Recognized End Date], 
	r.recogAFStartDate as [Rate Recognized AF Start Date], r.recogAFEndDate as [Rate Recognized AF End Date], 
	r.rateAdvanceOnTermEnd as [Rate Advance On Term End],
	case r.forceUpfront when 1 then 'Yes' else 'No' end as [Rate Must Pay on 1st invoice],
	case r.frontEndAllowChangePrice when 1 then 'Yes' else 'No' end as [Rate User can change price],
	case r.keepChangedPriceOnRenewal when 1 then 'Yes' else 'No' end as [Rate Renewal Keeps changed price],
	r.frontEndChangePriceMin as [Rate Change Price Min], r.frontEndChangePriceMax as [Rate Change Price Max], 
	rFall.uid as [Rate Preferred Fallback Renewal Rate UID],
	rFall.rateName as [Rate Preferred Fallback Renewal Rate Name],
	rJoin.uid as [Rate Associated Join Rate UID],
	rJoin.rateName as [Rate Associated Join Rate Name],

	glRate.uid as [Rate GL Account UID], 
	glRate.thePathExpanded + isnull(' (' + nullIf(glRate.AccountCode,'') + ')','') as [Rate GL Account],
	case glRate.status when 'A' then 'Active' when 'I' then 'Inactive' when 'D' then 'Deleted' end as [Rate GL Account Status],
	glRate.profileName as [Rate GL Account Invoice Profile],
	case glRate.enableAutoPay when 1 then 'Yes' else 'No' end as [Rate GL Account Invoice Profile AutoPay Enabled],

	f.uid as [Rate Frequency UID], f.frequencyName as [Rate Frequency Name], 
	rf.rateAmt as [Rate Frequency Amount], 
	case rf.status when 'A' then 'Active' when 'I' then 'Inactive' when 'D' then 'Deleted' end as [Rate Frequency Status],
	case rf.allowFrontEnd when 1 then 'Yes' else 'No' end as [Rate Frequency Allow on Front End],
	mp.profileName as [Rate Frequency Payment Profile Name]
into #SubSetup
from dbo.sub_types as t
outer apply dbo.fn_getContent(t.frontEndContentID,1) as fec
outer apply dbo.fn_getContent(t.frontEndCompletedContentID,1) as fecc
inner join dbo.sub_subscriptions as s on s.typeID = t.typeID
left outer join allGLS as glSub on glSub.glAccountID = s.GLAccountID
inner join dbo.sub_rateSchedules as rs on rs.scheduleID = s.scheduleID
inner join dbo.sub_activationOptions as ao on ao.subActivationID = s.subActivationID
inner join dbo.sub_activationOptions as aoAlt on aoAlt.subActivationID = s.subAlternateActivationID
left outer join dbo.et_emailTemplates as et on et.templateID = s.emailTemplateID
left outer join dbo.et_emailTemplates as etRenew on etRenew.templateID = s.renewEmailTemplateID
outer apply dbo.fn_getContent(s.frontEndContentID,1) as sfec
outer apply dbo.fn_getContent(s.frontEndCompletedContentID,1) as sfecc
left outer join dbo.sub_rates as r on r.scheduleID = rs.scheduleID
left outer join allGLS as glRate on glRate.glAccountID = r.GLAccountID
left outer join dbo.sub_rates as rFall on rFall.rateID = r.fallbackRenewalRateID
left outer join dbo.sub_rates as rJoin on rJoin.rateID = r.linkedNonRenewalRateID
left outer join dbo.sub_rateFrequencies as rf 
	inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID and rf.status = 'A'
	on rf.rateID = r.rateID
left outer join dbo.sub_rateFrequenciesMerchantProfiles as rfmp 
	inner join dbo.mp_profiles as mp on mp.profileID = rfmp.profileID and mp.status = 'A'
	on rfmp.rfid = rf.rfid and rfmp.status = 'A'
where t.siteID = @siteID
and t.status = 'A'
and s.status <> 'D'

-- sql for export
DECLARE @fullsql varchar(max)
SELECT @fullsql = '
	select *
	from #SubSetup
	order by [Subscription Type], [Subscription Name], [Rate Name], [Rate Frequency Name], [Rate Frequency Payment Profile Name]'

-- export
EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql=@fullsql

IF OBJECT_ID('tempdb..#SubSetup') IS NOT NULL 
	DROP TABLE #SubSetup
GO

ALTER PROCEDURE [dbo].[sub_advanceRates]
AS

SET NOCOUNT ON

declare @dtStart DATETIME, @dtEnd DATETIME, @dateToUse DATETIME
declare @datePart varchar(20), @dateNum INT, @startOfPeriod INT, @endOfPeriod INT, @nextWeekday INT, @retDate DATETIME
declare @rateStartDate DATETIME, @rateEndDate DATETIME, @termStartDate DATETIME, @termEndDate DATETIME, @graceEndDate DATETIME, @recogStartDate DATETIME, @recogEndDate DATETIME
declare @rateStartDateAFID INT, @rateEndDateAFID INT, @termStartDateAFID INT, @termEndDateAFID INT, @graceAFID INT, @recogStartDateAFID INT, @recogEndDateAFID INT
declare @minRateID int

select @dateToUse = dateadd(DAY, -1, getdate())
select @dtStart = dateadd(DAY, datediff(DAY, 0, @dateToUse), 0) 
select @dtEnd = dateadd(ms, -3, dateadd(DAY, datediff(DAY, 0, @dateToUse)+1, 0))

IF OBJECT_ID('tempdb..#tempRatesAdvance') IS NOT NULL 
	DROP TABLE #tempRatesAdvance

SELECT rateID, rateAFStartDate, rateAFEndDate, rateStartDateAFID, rateEndDateAFID, 
	termAFStartDate, termAFEndDate, termStartDateAFID, termEndDateAFID, 
	graceEndDate, graceAFID, 
	recogAFStartDate, recogAFEndDate, recogStartDateAFID, recogEndDateAFID 
INTO #tempRatesAdvance
FROM dbo.sub_rates
WHERE 
	(
		(
			rateAdvanceOnTermEnd = 0 AND 
			(
				rateAFEndDate <= @dtEnd AND 
				(rateStartDateAFID is not null OR rateEndDateAFID is not null OR termStartDateAFID is not null OR termEndDateAFID is not null OR recogStartDateAFID is not null OR recogEndDateAFID is not null)
			)
		)
		OR 
		(
			rateAdvanceOnTermEnd = 1 AND 
			(
				termAFEndDate <= @dtEnd AND 
				(rateStartDateAFID is not null OR rateEndDateAFID is not null OR termStartDateAFID is not null OR termEndDateAFID is not null OR recogStartDateAFID is not null OR recogEndDateAFID is not null)
			)
		)
	)
AND [status] <> 'D'


select @minRateID = min(rateID) from #tempRatesAdvance
while @minRateID is not null BEGIN
	select @rateStartDate=rateAFStartDate, @rateEndDate=rateAFEndDate, @rateStartDateAFID=rateStartDateAFID, @rateEndDateAFID=rateEndDateAFID,
		@termStartDate=termAFStartDate, @termEndDate=termAFEndDate, @termStartDateAFID=termStartDateAFID, @termEndDateAFID=termEndDateAFID, 
		@graceEndDate=graceEndDate, @graceAFID=graceAFID, 
		@recogStartDate=recogAFStartDate, @recogEndDate=recogAFEndDate, @recogStartDateAFID=recogStartDateAFID, @recogEndDateAFID=recogEndDateAFID 
	from #tempRatesAdvance
	where rateID = @minRateID

	IF @rateStartDateAFID is not null BEGIN
		select @retDate = NULL

		select @datePart=[datePart], @dateNum=dateNum, @startOfPeriod=startOfPeriod, @endOfPeriod=endOfPeriod, @nextWeekday=nextWeekday
		from dbo.sub_advanceFormulas
		where AFID = @rateStartDateAFID

		EXEC dbo.sub_getAFDate @baseDate=@rateStartDate, @datePart=@datePart, @dateNum=@dateNum, @startOfPeriod=@startOfPeriod, 
								@endOfPeriod=@endOfPeriod, @nextWeekday=@nextWeekday, @retDate=@retDate OUTPUT

		update dbo.sub_rates
		set rateAFStartDate = @retDate
		where rateID = @minRateID
	END

	IF @rateEndDateAFID is not null BEGIN
		select @retDate = NULL

		select @datePart=[datePart], @dateNum=dateNum, @startOfPeriod=startOfPeriod, @endOfPeriod=endOfPeriod, @nextWeekday=nextWeekday
		from dbo.sub_advanceFormulas
		where AFID = @rateEndDateAFID

		EXEC dbo.sub_getAFDate @baseDate=@rateEndDate, @datePart=@datePart, @dateNum=@dateNum, @startOfPeriod=@startOfPeriod, 
								@endOfPeriod=@endOfPeriod, @nextWeekday=@nextWeekday, @retDate=@retDate OUTPUT

		update dbo.sub_rates
		set rateAFEndDate = @retDate
		where rateID = @minRateID
	END

	IF @termStartDateAFID is not null BEGIN
		select @retDate = NULL

		select @datePart=[datePart], @dateNum=dateNum, @startOfPeriod=startOfPeriod, @endOfPeriod=endOfPeriod, @nextWeekday=nextWeekday
		from dbo.sub_advanceFormulas
		where AFID = @termStartDateAFID

		EXEC dbo.sub_getAFDate @baseDate=@termStartDate, @datePart=@datePart, @dateNum=@dateNum, @startOfPeriod=@startOfPeriod, 
								@endOfPeriod=@endOfPeriod, @nextWeekday=@nextWeekday, @retDate=@retDate OUTPUT

		update dbo.sub_rates
		set termAFStartDate = @retDate
		where rateID = @minRateID
	END

	IF @termEndDateAFID is not null BEGIN
		select @retDate = NULL

		select @datePart=[datePart], @dateNum=dateNum, @startOfPeriod=startOfPeriod, @endOfPeriod=endOfPeriod, @nextWeekday=nextWeekday
		from dbo.sub_advanceFormulas
		where AFID = @termEndDateAFID

		EXEC dbo.sub_getAFDate @baseDate=@termEndDate, @datePart=@datePart, @dateNum=@dateNum, @startOfPeriod=@startOfPeriod, 
								@endOfPeriod=@endOfPeriod, @nextWeekday=@nextWeekday, @retDate=@retDate OUTPUT

		update dbo.sub_rates
		set termAFEndDate = @retDate
		where rateID = @minRateID
	END

	IF @graceAFID is not null BEGIN
		select @retDate = NULL

		select @datePart=[datePart], @dateNum=dateNum, @startOfPeriod=startOfPeriod, @endOfPeriod=endOfPeriod, @nextWeekday=nextWeekday
		from dbo.sub_advanceFormulas
		where AFID = @graceAFID

		EXEC dbo.sub_getAFDate @baseDate=@graceEndDate, @datePart=@datePart, @dateNum=@dateNum, @startOfPeriod=@startOfPeriod, 
								@endOfPeriod=@endOfPeriod, @nextWeekday=@nextWeekday, @retDate=@retDate OUTPUT

		update dbo.sub_rates
		set graceEndDate = @retDate
		where rateID = @minRateID
	END

	IF @recogStartDateAFID is not null BEGIN
		select @retDate = NULL

		select @datePart=[datePart], @dateNum=dateNum, @startOfPeriod=startOfPeriod, @endOfPeriod=endOfPeriod, @nextWeekday=nextWeekday
		from dbo.sub_advanceFormulas
		where AFID = @recogStartDateAFID

		EXEC dbo.sub_getAFDate @baseDate=@termStartDate, @datePart=@datePart, @dateNum=@dateNum, @startOfPeriod=@startOfPeriod, 
								@endOfPeriod=@endOfPeriod, @nextWeekday=@nextWeekday, @retDate=@retDate OUTPUT

		update dbo.sub_rates
		set recogAFStartDate = @retDate
		where rateID = @minRateID
	END

	IF @recogEndDateAFID is not null BEGIN
		select @retDate = NULL

		select @datePart=[datePart], @dateNum=dateNum, @startOfPeriod=startOfPeriod, @endOfPeriod=endOfPeriod, @nextWeekday=nextWeekday
		from dbo.sub_advanceFormulas
		where AFID = @recogEndDateAFID

		EXEC dbo.sub_getAFDate @baseDate=@termEndDate, @datePart=@datePart, @dateNum=@dateNum, @startOfPeriod=@startOfPeriod, 
								@endOfPeriod=@endOfPeriod, @nextWeekday=@nextWeekday, @retDate=@retDate OUTPUT

		update dbo.sub_rates
		set recogAFEndDate = @retDate
		where rateID = @minRateID
	END

	select @minRateID = min(rateID) from #tempRatesAdvance where rateID > @minRateID
END

IF OBJECT_ID('tempdb..#tempRatesAdvance') IS NOT NULL 
	DROP TABLE #tempRatesAdvance

GO

ALTER FUNCTION [dbo].[fn_sub_getSubscriptionRatesMissingAdvanceFormulas] ()
RETURNS TABLE 
AS
RETURN 
(
	select distinct s.sitecode, rs.schedulename, r.ratename
	from dbo.sites as s
	inner join dbo.sub_types as t on s.siteID = t.siteID and t.status = 'A'
	inner join dbo.sub_subscriptions as subs on subs.typeID = t.typeID and subs.status = 'A'
	inner join dbo.sub_rateSchedules as rs on rs.scheduleID = subs.scheduleID and rs.status = 'A'
	inner join dbo.sub_rates as r on r.scheduleID = rs.scheduleID and r.status = 'A' and r.ratename not like ('%Import%')
	and (
		r.rateStartDateAFID is null or
		r.rateEndDateAFID is null or
		r.termStartDateAFID is null or
		r.termEndDateAFID is null or
		(r.graceAFID is null and r.graceEndDate is not null) or
		r.recogStartDateAFID is null or
		r.recogEndDateAFID is null
	)
)
GO


ALTER FUNCTION [dbo].[fn_sub_getQualifiedRatesXML] (
    @scheduleID INT,
	@memberID INT,
	@isRenewalRate bit
)
RETURNS XML
AS
BEGIN

    RETURN
    (

	select 
		r.rateID as '@rateID',
		r.rateName as '@rateName',
		r.uid as '@uid',
		r.isRenewalRate as '@isRenewalRate',
		r.siteResourceID as '@siteResourceID',
		r.status as '@status',
		r.rateAFStartDate as '@rateAFStartDate',
		r.rateAFEndDate as '@rateAFEndDate',
		r.termAFStartDate as '@termAFStartDate',
		r.termAFEndDate as '@termAFEndDate',
		r.graceEndDate as '@graceEndDate',
		r.recogAFStartDate as '@recogAFStartDate',
		r.recogAFEndDate as '@recogAFEndDate'
	from sub_rates r
	inner join cache_perms_siteResourceFunctionRightPrints srfrp on r.siteResourceID = srfrp.siteResourceID
		and r.scheduleID = @scheduleID
		and r.isRenewalRate = @isRenewalRate
	inner join cms_siteResourceFunctions srf on srf.functionID = srfrp.functionID
		and srf.functionName = 'Qualify'
	inner join cache_perms_groupPrintsRightPrints gprp on gprp.rightPrintID = srfrp.rightPrintID
	inner join cache_perms_groupPrints gp on gp.groupPrintID = gprp.groupPrintID
	inner join ams_members m on m.groupPrintID = gp.groupPrintID
		and m.memberID = @memberID
	for xml path('rate'),root('rates'), type

	)
END
GO

ALTER FUNCTION [dbo].[fn_sub_getSubscriptionDatesOutOfOrder] ()
RETURNS TABLE 
AS
RETURN 
(
	select s.sitecode, rs.schedulename, r.ratename, count(distinct subs.subscriptionname) as affectedSubscriptions
	from dbo.sites as s
	inner join dbo.sub_types as t on s.siteID = t.siteID and t.status = 'A'
	inner join dbo.sub_subscriptions as subs on subs.typeID = t.typeID and subs.status = 'A'
	inner join dbo.sub_rateSchedules as rs on rs.scheduleID = subs.scheduleID and rs.status = 'A'
	inner join dbo.sub_rates as r on r.scheduleID = rs.scheduleID and r.status = 'A' 
	where 
		-- Grace EndDate Before Term EndDate	
		r.graceEndDate < r.termAFEndDate
			or
		-- Term EndDate Before Term start	
		r.termAFEndDate < r.termAFStartDate
			or
		-- Recognized EndDate Before Recognized start	
		r.recogAFEndDate < r.recogAFStartDate
			or
		-- available end Before availale start
		r.rateAFEndDate < r.rateAFStartDate
	group by s.sitecode, rs.schedulename, r.ratename
)
GO

ALTER FUNCTION [dbo].[fn_sub_getSubscriptionStructureXML]
(
    @subscriptionID INT,
	@frontEndAllowSelect bit = NULL
)
RETURNS XML
AS
BEGIN
    RETURN
    (
		select
		sub.subscriptionID as '@subscriptionID',
		sub.uid as '@uid',
		sub.subscriptionName as '@subscriptionName',
		sub.rateTermDateFlag as '@rateTermDateFlag',
		sub.GLAccountID as '@GLAccountID',
		sub.allowRateGLAccountOverride as '@allowRateGLAccountOverride',
		sub.paymentOrder as '@paymentOrder',
		acto.subActivationCode as '@subActivationCode',
		actoatl.subActivationCode as '@subAlternateActivationCode',
		rs.scheduleID as '@rateScheduleID',
		rs.scheduleName as '@rateScheduleName',
		rs.status as '@rateScheduleStatus',
		rs.uid as '@rateScheduleUid',
		(
			select 
				sets.setid as '@setID',
				sets.uid as '@setUID',
				ao.addOnID as '@addOnID',
				sets.setName as '@setName',
				ao.useRatesInSet as '@useRatesInSet',
				ao.useAcctCodeInSet as '@useAcctCodeInSet',
				ao.useAddonsInSet as '@useAddonsInSet',
				ao.PCnum as '@PCnum',
				ao.PCAmtEach as '@PCAmtEach',
				ao.PCAmtOffEach as '@PCAmtOffEach',
				ao.PCPctOffEach as '@PCPctOffEach',
				ao.frontEndAllowSelect as '@frontEndAllowSelect',
				ao.frontEndAllowChangePrice as '@frontEndAllowChangePrice',
				ao.paymentOrder as '@setPayOrder',
				dbo.fn_sub_getSubscriptionAddonStructureXML(ao.addonID,ao.frontEndAllowSelect)
			from sub_addons ao
			inner join sub_sets as sets
				on sets.setID = ao.childSetID
				and sub.subscriptionID = ao.subscriptionID
				and ao.frontEndAllowSelect = isnull(@frontEndAllowSelect, ao.frontEndAllowSelect)
			for xml path('addon'), type
		) as 'addons'
		from sub_subscriptions sub
		inner join sub_rateSchedules rs
			on rs.scheduleID = sub.scheduleID
			and sub.subscriptionID = @subscriptionID
			and sub.status = 'A'
		inner join dbo.sub_activationOptions acto
			on acto.subActivationID = sub.subActivationID
		inner join dbo.sub_activationOptions actoatl
			on actoatl.subActivationID = sub.subAlternateActivationID
		for xml path('subscription')
	)
END
GO

ALTER TABLE dbo.sub_subscribers ADD recogStartDate datetime NULL, recogEndDate datetime NULL
GO


-- prefill data. some rates dont end at .997 so we need to make these dates do that.
UPDATE dbo.sub_subscribers
set recogStartDate = DATEADD(dd,DATEDIFF(dd,0,subStartDate),0),
	recogEndDate = dateadd(ms,-3,dateadd(dd,1,DATEADD(dd,DATEDIFF(dd,0,subEndDate),0)))
GO


ALTER TABLE dbo.sub_subscribers ALTER COLUMN recogStartDate datetime NOT NULL
GO
ALTER TABLE dbo.sub_subscribers ALTER COLUMN recogEndDate datetime NOT NULL
GO

ALTER PROC [dbo].[sub_addSubscriber]
@orgID int,
@memberid int,
@subscriptionID int,
@parentSubscriberID int,
@RFID INT,
@GLAccountID INT,
@status char(1),
@subStartDate datetime,
@subEndDate datetime,
@graceEndDate datetime,
@pcfree bit,
@activationOptionCode varchar(1),
@recordedByMemberID int,
@bypassQueue bit,
@subscriberID int OUTPUT

AS

-- set subEndDate and graceEndDate to 23:59:59.997
select @subEndDate = dateadd(ms,-3,dateadd(day,1,DATEADD(dd, DATEDIFF(dd,0,@subEndDate),0)))
IF @graceEndDate is not null
	select @graceEndDate = dateadd(ms,-3,dateadd(day,1,DATEADD(dd, DATEDIFF(dd,0,@graceEndDate),0)))

declare @statusID int, @paymentStatusID int, @activationOptionID int, @rootSubscriberID int, @subscriberPath varchar(100), @rootSubscriptionID int

select @statusID=statusID
	from dbo.sub_statuses
	where statusCode = @status

select @activationOptionID=subActivationID
	from dbo.sub_activationOptions
	where subActivationCode = @activationOptionCode

select @paymentStatusID=statusID
	from dbo.sub_paymentStatuses
	where statusCode = 'N'

insert into dbo.sub_subscribers (memberID, subscriptionID, RFID, GLAccountID, statusID, 
	subStartDate, subEndDate, graceEndDate, dateRecorded, recordedByMemberID, parentSubscriberID, 
	PCFree, subActivationID, paymentStatusID, recogStartDate, recogEndDate) 
values (@memberid, @subscriptionID, nullIf(@RFID,0), nullIf(@GLAccountID,0), @statusID, 
	@subStartDate, @subEndDate, @graceEndDate, getdate(), @recordedByMemberID, @parentSubscriberID, 
	@pcfree, @activationOptionID, @paymentStatusID, @subStartDate, @subEndDate)
IF @@ERROR <> 0 goto on_error
select @subscriberID = SCOPE_IDENTITY()

if @parentSubscriberID is NULL begin
	select @rootSubscriberID = @subscriberID
	select @subscriberPath = CAST(RIGHT('100001',4) as varchar(max))
end else begin

	select @rootSubscriptionID = rootss.subscriptionID, @rootSubscriberID = rootss.subscriberID
	from sub_subscribers ss
	inner join sub_subscribers parentss
		on parentss.subscriberID = ss.parentSubscriberID
		and ss.subscriberID = @subscriberID
	inner join sub_subscribers rootss
		on parentss.rootSubscriberID = rootss.subscriberID

	select @subscriberPath = sto.subscriptionPath
	from dbo.fn_sub_getSubscriptionTreeOrder(@rootSubscriptionID) as sto
	where subscriptionID = @subscriptionID
end

update dbo.sub_subscribers
set rootSubscriberID = @rootSubscriberID,
	subscriberPath = @subscriberPath
where subscriberID = @subscriberID

/* Add to group */
declare @waitingPayGroupID int, @expiredGroupID int, @renewableGroupID int, @pendingGroupID int
select @waitingPayGroupID=groupID
	from dbo.ams_groups
	where groupCode = 'subWaitingPay_' + convert(varchar, @subscriptionID) + '_tracking'
select @expiredGroupID=groupID
	from dbo.ams_groups
	where groupCode = 'SubInactive_' + convert(varchar, @subscriptionID) + '_tracking'
select @renewableGroupID=groupID
	from dbo.ams_groups
	where groupCode = 'SubRenew_' + convert(varchar, @subscriptionID) + '_tracking'
select @pendingGroupID=groupID
	from dbo.ams_groups
	where groupCode = 'SubPending_' + convert(varchar, @subscriptionID) + '_tracking'
IF @status = 'A' and @waitingPayGroupID is not null
	exec ams_createMemberGroup @memberID=@memberid, @groupID=@waitingPayGroupID, @bypassQueue=@bypassQueue
IF @status IN ('R','O') and @renewableGroupID is not null
	exec ams_createMemberGroup @memberID=@memberid, @groupID=@renewableGroupID, @bypassQueue=@bypassQueue
IF @status = 'E' and @expiredGroupID is not null
	exec ams_createMemberGroup @memberID=@memberid, @groupID=@expiredGroupID, @bypassQueue=@bypassQueue
IF @status = 'P' and @pendingGroupID is not null
	exec ams_createMemberGroup @memberID=@memberid, @groupID=@pendingGroupID, @bypassQueue=@bypassQueue

insert into dbo.sub_statusHistory(subscriberID, oldStatusID, statusID, enteredByMemberID)
values(@subscriberID, NULL, @statusID, nullif(@recordedByMemberID,0))
 
RETURN 0

on_error:
 select @subscriberID = 0
 RETURN -1
GO

ALTER FUNCTION [dbo].[fn_getRecursiveMemberSubscriptions] ( 
	@memberID int,
	@siteID int,
	@subscriberID int = NULL
)
RETURNS @subscriptionTable TABLE (
	subscriberID int NULL,
	subscriptionID int NULL,
	typeName varchar(100) NULL,
	subscriptionName varchar(300) NULL,
	status varchar(1) NULL,
	statusName varchar(50),
	paymentStatus varchar(1) NULL,
	paymentStatusName varchar(50) NULL,
	RFID int NULL,
	rateTermDateFlag varchar(1) NULL,
	GLAccountID int NULL,
	subStartDate datetime,
	subEndDate datetime,
	graceEndDate datetime,
	parentSubscriberID int,
	rootSubscriberID int,
	PCFree bit,
	modifiedRate money NULL,
	paymentOrder int,
	subActivationCode varchar(1),
	allowRateGLAccountOverride bit,
	topRow varchar(max),
	thePath varchar(max),
	thePathExpanded varchar(max),
	lastPrice money,
	linkedNonRenewalRateID int,
	fallbackRenewalRateID int,
	keepChangedPriceOnRenewal bit,
	frontEndAllowChangePrice int,
	frontEndChangePriceMin money,
	frontEndChangePriceMax money,
	recogStartDate datetime,
	recogEndDate datetime
)
AS
BEGIN

	IF @subscriberID is NULL BEGIN
		WITH subscribers AS (
			select subscriberID, subscriptionID, typeName, subscriptionName, [status], statusName, paymentStatus, paymentStatusName, 
				RFID, rateTermDateFlag, GLAccountID, subStartDate, subEndDate, graceEndDate, parentSubscriberID, rootSubscriberID, 
				PCFree, modifiedRate, paymentOrder, subActivationCode, allowRateGLAccountOverride, linkedNonRenewalRateID, 
				fallbackRenewalRateID, keepChangedPriceOnRenewal, frontEndAllowChangePrice, frontEndChangePriceMin, frontEndChangePriceMax,
				recogStartDate, recogEndDate, 
				CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS topRow,
				CONVERT(varchar, memberID) + '.' + CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
				CAST(subscriptionName as varchar(max)) as thePathExpanded
			FROM (
				select subscriberID, memberID, subscriptionID, typeName, subscriptionName, [status], statusName, paymentStatus, 
					paymentStatusName, RFID, rateTermDateFlag, GLAccountID, subStartDate, subEndDate, graceEndDate, parentSubscriberID, 
					rootSubscriberID, PCFree, modifiedRate, paymentOrder, subActivationCode, allowRateGLAccountOverride,linkedNonRenewalRateID, 
					fallbackRenewalRateID, keepChangedPriceOnRenewal, frontEndAllowChangePrice, frontEndChangePriceMin, frontEndChangePriceMax,
					recogStartDate, recogEndDate,
					ROW_NUMBER() OVER (ORDER BY statusSort, typeName, subscriberID) AS theRow
				FROM (
					select s.subscriberID, m.activeMemberID as memberID, s.subscriptionID, t.typeName, sc.subscriptionName, 
						st.statusCode as [status], st.statusName as statusName, pst.statusCode as paymentStatus, pst.statusName as paymentStatusName,
						s.RFID, sc.rateTermDateFlag, s.GLAccountID, s.subStartDate, s.subEndDate, s.graceEndDate, s.parentSubscriberID, 
						s.rootSubscriberID, s.PCFree, s.modifiedRate, sc.paymentOrder, o.subActivationCode, sc.allowRateGLAccountOverride, 
						r.linkedNonRenewalRateID, r.fallbackRenewalRateID, r.keepChangedPriceOnRenewal, r.frontEndAllowChangePrice, 
						r.frontEndChangePriceMin, r.frontEndChangePriceMax, s.recogStartDate, s.recogEndDate, 
 						case 
						when st.statusCode = 'A' then 1 
						when st.statusCode = 'E' then 2 
   						when st.statusCode = 'I' then 3
						when st.statusCode = 'D' then 4
						else 5
						end as statusSort             	
					from dbo.sub_subscribers s
					inner join dbo.sub_statuses st on st.statusID = s.statusID
					inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID
					inner join dbo.sub_activationOptions o on o.subActivationID = s.subActivationID
					inner join dbo.sub_subscriptions sc on sc.subscriptionID = s.subscriptionID
					inner join dbo.sub_types t on t.typeID = sc.typeID and t.siteID = @siteID
					inner join dbo.ams_members m on m.memberID = s.memberID
					inner join dbo.sub_rateFrequencies rf on s.rfid = rf.rfid
					inner join dbo.sub_rates r on r.rateID = rf.rateID
					where s.memberID in (
						select mAll.memberID
						from ams_members m
						inner join ams_members mAll on mAll.activeMemberID = m.activeMemberID
						where m.memberID = @memberID
					)
					and parentSubscriberID is null
				) as x1
			) as x
				UNION ALL
			select subscriberID, subscriptionID, typeName, subscriptionName, [status], statusName, paymentStatus, paymentStatusName, 
				RFID, rateTermDateFlag, GLAccountID, subStartDate, subEndDate, graceEndDate, parentSubscriberID, rootSubscriberID, PCFree, 
				modifiedRate, paymentOrder, subActivationCode, allowRateGLAccountOverride, linkedNonRenewalRateID, fallbackRenewalRateID,
				keepChangedPriceOnRenewal, frontEndAllowChangePrice, frontEndChangePriceMin, frontEndChangePriceMax,
				recogStartDate, recogEndDate, topRow, thePath + '.' + CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
				thePathExpanded = case when isnull(parentSubscriberID,0) = 0 then subscriptionName else thePathExpanded + ' \ ' + subscriptionName end
			FROM (
				select s.subscriberID, m.activeMemberID as memberID, s.subscriptionID, t.typeName, sc.subscriptionName, st.statusCode as [status], 
					st.statusName as statusName, pst.statusCode as paymentStatus, pst.statusName as paymentStatusName, s.RFID, sc.rateTermDateFlag, 
					s.GLAccountID, s.subStartDate, s.subEndDate, s.graceEndDate, s.parentSubscriberID, s.rootSubscriberID, s.PCFree, s.modifiedRate,
					sc.paymentOrder, o.subActivationCode, sc.allowRateGLAccountOverride, scte.topRow, scte.thePath, scte.thePathExpanded,
					r.linkedNonRenewalRateID, r.fallbackRenewalRateID, r.keepChangedPriceOnRenewal, r.frontEndAllowChangePrice, r.frontEndChangePriceMin,
					r.frontEndChangePriceMax, s.recogStartDate, s.recogEndDate, ROW_NUMBER() OVER (ORDER BY t.typeName, s.subscriberID) AS theRow
				from dbo.sub_subscribers s
				inner join dbo.sub_statuses st on st.statusID = s.statusID
				inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID
				inner join dbo.sub_activationOptions o on o.subActivationID = s.subActivationID
				inner join dbo.sub_subscriptions sc on sc.subscriptionID = s.subscriptionID
				inner join dbo.sub_types t on t.typeID = sc.typeID and t.siteID = @siteID
				INNER JOIN subscribers scte on s.parentSubscriberID = scte.subscriberID
				INNER JOIN dbo.ams_members m on m.memberID = s.memberID
				inner join dbo.sub_rateFrequencies rf on s.rfid = rf.rfid
				inner join dbo.sub_rates r on r.rateID = rf.rateID
				where s.memberID in (
					select mAll.memberID
					from ams_members m
					inner join ams_members mAll on mAll.activeMemberID = m.activeMemberID
					where m.memberID = @memberID
				)
			) as y
		)
		INSERT INTO @subscriptionTable (subscriberID, subscriptionID, typeName, subscriptionName, [status], statusName, 
			paymentStatus, paymentStatusName, RFID, rateTermDateFlag, GLAccountID, subStartDate, subEndDate, graceEndDate, 
			parentSubscriberID, rootSubscriberID, PCFree, modifiedRate, paymentOrder, subActivationCode, allowRateGLAccountOverride, 
			topRow, thePath, thePathExpanded, linkedNonRenewalRateID,fallbackRenewalRateID, keepChangedPriceOnRenewal, 
			frontEndAllowChangePrice, frontEndChangePriceMin, frontEndChangePriceMax, recogStartDate, recogEndDate)
		SELECT subscriberID, subscriptionID, typeName, subscriptionName, [status], statusName, paymentStatus, paymentStatusName, 
			RFID, rateTermDateFlag, GLAccountID, subStartDate, subEndDate, graceEndDate, parentSubscriberID, rootSubscriberID, 
			PCFree, modifiedRate, paymentOrder, subActivationCode, allowRateGLAccountOverride, topRow, thePath, thePathExpanded, 
			linkedNonRenewalRateID, fallbackRenewalRateID, keepChangedPriceOnRenewal, frontEndAllowChangePrice, frontEndChangePriceMin, 
			frontEndChangePriceMax, recogStartDate, recogEndDate
		from subscribers

	END
	ELSE BEGIN
		WITH subscribers AS (
			select subscriberID, subscriptionID, typeName, subscriptionName, [status], statusName, paymentStatus, paymentStatusName, 
				RFID, rateTermDateFlag, GLAccountID, subStartDate, subEndDate, graceEndDate, parentSubscriberID, rootSubscriberID, PCFree, 
				modifiedRate, paymentOrder, subActivationCode, allowRateGLAccountOverride, lastPrice, linkedNonRenewalRateID, 
				fallbackRenewalRateID, keepChangedPriceOnRenewal, frontEndAllowChangePrice, frontEndChangePriceMin, frontEndChangePriceMax,
				recogStartDate, recogEndDate, 
				CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS topRow,
				CONVERT(varchar, memberID) + '.' + CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
				CAST(subscriptionName as varchar(max)) as thePathExpanded
			FROM (
				select s.subscriberID, m.activeMemberID as memberID, s.subscriptionID, t.typeName, sc.subscriptionName, st.statusCode as [status], 
					st.statusName as statusName, pst.statusCode as paymentStatus, pst.statusName as paymentStatusName, s.RFID, sc.rateTermDateFlag, 
					s.GLAccountID, s.subStartDate, s.subEndDate, s.graceEndDate, s.parentSubscriberID, s.rootSubscriberID, s.PCFree, s.modifiedRate, 
					sc.paymentOrder, o.subActivationCode, sc.allowRateGLAccountOverride, s.lastPrice, r.linkedNonRenewalRateID,
					r.fallbackRenewalRateID, r.keepChangedPriceOnRenewal, r.frontEndAllowChangePrice, r.frontEndChangePriceMin,
					r.frontEndChangePriceMax, s.recogStartDate, s.recogEndDate, ROW_NUMBER() OVER (ORDER BY t.typeName, s.subscriberID) AS theRow
				from dbo.sub_subscribers s
				inner join dbo.sub_statuses st on st.statusID = s.statusID
				inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID
				inner join dbo.sub_activationOptions o on o.subActivationID = s.subActivationID
				inner join dbo.sub_subscriptions sc on sc.subscriptionID = s.subscriptionID
				inner join dbo.sub_types t on t.typeID = sc.typeID and t.siteID = @siteID
				inner join dbo.ams_members m on m.memberID = s.memberID
				inner join dbo.sub_rateFrequencies rf on s.rfid = rf.rfid
				inner join dbo.sub_rates r on r.rateID = rf.rateID
				where s.memberID in (
					select mAll.memberID
					from ams_members m
					inner join ams_members mAll on mAll.activeMemberID = m.activeMemberID
					where m.memberID = @memberID
				)
				and subscriberID = @subscriberID
			) as x
				UNION ALL
			select subscriberID, subscriptionID, typeName, subscriptionName, [status], statusName, paymentStatus, paymentStatusName, 
				RFID, rateTermDateFlag, GLAccountID, subStartDate, subEndDate, graceEndDate, parentSubscriberID, rootSubscriberID, 
				PCFree, modifiedRate, paymentOrder, subActivationCode, allowRateGLAccountOverride, lastPrice, linkedNonRenewalRateID,
				fallbackRenewalRateID, keepChangedPriceOnRenewal, frontEndAllowChangePrice, frontEndChangePriceMin, frontEndChangePriceMax, 
				recogStartDate, recogEndDate, topRow, thePath + '.' + CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
				thePathExpanded = case when isnull(parentSubscriberID,0) = 0 then subscriptionName else thePathExpanded + ' \ ' + subscriptionName end
			FROM (
				select s.subscriberID, m.activeMemberID as memberID, s.subscriptionID, t.typeName, sc.subscriptionName, st.statusCode as [status], 
					st.statusName as statusName, pst.statusCode as paymentStatus, pst.statusName as paymentStatusName, s.RFID, sc.rateTermDateFlag, 
					s.GLAccountID, s.subStartDate, s.subEndDate, s.graceEndDate, s.parentSubscriberID, s.rootSubscriberID, s.PCFree, s.modifiedRate,
					sc.paymentOrder, o.subActivationCode, sc.allowRateGLAccountOverride, s.lastPrice, r.linkedNonRenewalRateID, r.fallbackRenewalRateID,
					r.keepChangedPriceOnRenewal, r.frontEndAllowChangePrice, r.frontEndChangePriceMin, r.frontEndChangePriceMax, s.recogStartDate, 
					s.recogEndDate, scte.topRow, scte.thePath, scte.thePathExpanded,  ROW_NUMBER() OVER (ORDER BY t.typeName, s.subscriberID) AS theRow
				from dbo.sub_subscribers s
				inner join dbo.sub_statuses st on st.statusID = s.statusID
				inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID
				inner join dbo.sub_activationOptions o on o.subActivationID = s.subActivationID
				inner join dbo.sub_subscriptions sc on sc.subscriptionID = s.subscriptionID
				inner join dbo.sub_types t on t.typeID = sc.typeID and t.siteID = @siteID
				INNER JOIN subscribers scte on s.parentSubscriberID = scte.subscriberID
				INNER JOIN dbo.ams_members m on m.memberID = s.memberID
				inner join dbo.sub_rateFrequencies rf on s.rfid = rf.rfid
				inner join dbo.sub_rates r on r.rateID = rf.rateID
				where s.memberID in (
					select mAll.memberID
					from ams_members m
					inner join ams_members mAll on mAll.activeMemberID = m.activeMemberID
					where m.memberID = @memberID
				)
			) as y
		)
		INSERT INTO @subscriptionTable (subscriberID, subscriptionID, typeName, subscriptionName, [status], statusName, paymentStatus, 
			paymentStatusName, RFID, rateTermDateFlag, GLAccountID, subStartDate, subEndDate, graceEndDate, parentSubscriberID, rootSubscriberID, 
			PCFree, modifiedRate, paymentOrder, subActivationCode, allowRateGLAccountOverride, topRow, thePath, thePathExpanded, lastprice, 
			linkedNonRenewalRateID, fallbackRenewalRateID, keepChangedPriceOnRenewal, frontEndAllowChangePrice, frontEndChangePriceMin, 
			frontEndChangePriceMax, recogStartDate, recogEndDate)
		SELECT subscriberID, subscriptionID, typeName, subscriptionName, [status], statusName, paymentStatus, paymentStatusName, RFID, 
			rateTermDateFlag, GLAccountID, subStartDate, subEndDate, graceEndDate, parentSubscriberID, rootSubscriberID, PCFree, modifiedRate, 
			paymentOrder, subActivationCode, allowRateGLAccountOverride, topRow, thePath, thePathExpanded, lastPrice, linkedNonRenewalRateID,
			fallbackRenewalRateID, keepChangedPriceOnRenewal, frontEndAllowChangePrice, frontEndChangePriceMin, frontEndChangePriceMax,
			recogStartDate, recogEndDate
		from subscribers
	END

	RETURN
END
GO

ALTER PROC [dbo].[sub_addSubscriber]
@orgID int,
@memberid int,
@subscriptionID int,
@parentSubscriberID int,
@RFID INT,
@GLAccountID INT,
@status char(1),
@subStartDate datetime,
@subEndDate datetime,
@graceEndDate datetime,
@recogStartDate datetime,
@recogEndDate datetime,
@pcfree bit,
@activationOptionCode varchar(1),
@recordedByMemberID int,
@bypassQueue bit,
@subscriberID int OUTPUT

AS

-- set subEndDate, graceEndDate, recogEndDate to 23:59:59.997
select @subEndDate = dateadd(ms,-3,dateadd(day,1,DATEADD(dd, DATEDIFF(dd,0,@subEndDate),0)))
select @recogEndDate = dateadd(ms,-3,dateadd(day,1,DATEADD(dd, DATEDIFF(dd,0,@recogEndDate),0)))
IF @graceEndDate is not null
	select @graceEndDate = dateadd(ms,-3,dateadd(day,1,DATEADD(dd, DATEDIFF(dd,0,@graceEndDate),0)))

declare @statusID int, @paymentStatusID int, @activationOptionID int, @rootSubscriberID int, @subscriberPath varchar(100), @rootSubscriptionID int

select @statusID=statusID
	from dbo.sub_statuses
	where statusCode = @status

select @activationOptionID=subActivationID
	from dbo.sub_activationOptions
	where subActivationCode = @activationOptionCode

select @paymentStatusID=statusID
	from dbo.sub_paymentStatuses
	where statusCode = 'N'

insert into dbo.sub_subscribers (memberID, subscriptionID, RFID, GLAccountID, statusID, 
	subStartDate, subEndDate, graceEndDate, dateRecorded, recordedByMemberID, parentSubscriberID, 
	PCFree, subActivationID, paymentStatusID, recogStartDate, recogEndDate) 
values (@memberid, @subscriptionID, nullIf(@RFID,0), nullIf(@GLAccountID,0), @statusID, 
	@subStartDate, @subEndDate, @graceEndDate, getdate(), @recordedByMemberID, @parentSubscriberID, 
	@pcfree, @activationOptionID, @paymentStatusID, @recogStartDate, @recogEndDate)
IF @@ERROR <> 0 goto on_error
select @subscriberID = SCOPE_IDENTITY()

if @parentSubscriberID is NULL begin
	select @rootSubscriberID = @subscriberID
	select @subscriberPath = CAST(RIGHT('100001',4) as varchar(max))
end else begin

	select @rootSubscriptionID = rootss.subscriptionID, @rootSubscriberID = rootss.subscriberID
	from sub_subscribers ss
	inner join sub_subscribers parentss
		on parentss.subscriberID = ss.parentSubscriberID
		and ss.subscriberID = @subscriberID
	inner join sub_subscribers rootss
		on parentss.rootSubscriberID = rootss.subscriberID

	select @subscriberPath = sto.subscriptionPath
	from dbo.fn_sub_getSubscriptionTreeOrder(@rootSubscriptionID) as sto
	where subscriptionID = @subscriptionID
end

update dbo.sub_subscribers
set rootSubscriberID = @rootSubscriberID,
	subscriberPath = @subscriberPath
where subscriberID = @subscriberID

/* Add to group */
declare @waitingPayGroupID int, @expiredGroupID int, @renewableGroupID int, @pendingGroupID int
select @waitingPayGroupID=groupID
	from dbo.ams_groups
	where groupCode = 'subWaitingPay_' + convert(varchar, @subscriptionID) + '_tracking'
select @expiredGroupID=groupID
	from dbo.ams_groups
	where groupCode = 'SubInactive_' + convert(varchar, @subscriptionID) + '_tracking'
select @renewableGroupID=groupID
	from dbo.ams_groups
	where groupCode = 'SubRenew_' + convert(varchar, @subscriptionID) + '_tracking'
select @pendingGroupID=groupID
	from dbo.ams_groups
	where groupCode = 'SubPending_' + convert(varchar, @subscriptionID) + '_tracking'
IF @status = 'A' and @waitingPayGroupID is not null
	exec ams_createMemberGroup @memberID=@memberid, @groupID=@waitingPayGroupID, @bypassQueue=@bypassQueue
IF @status IN ('R','O') and @renewableGroupID is not null
	exec ams_createMemberGroup @memberID=@memberid, @groupID=@renewableGroupID, @bypassQueue=@bypassQueue
IF @status = 'E' and @expiredGroupID is not null
	exec ams_createMemberGroup @memberID=@memberid, @groupID=@expiredGroupID, @bypassQueue=@bypassQueue
IF @status = 'P' and @pendingGroupID is not null
	exec ams_createMemberGroup @memberID=@memberid, @groupID=@pendingGroupID, @bypassQueue=@bypassQueue

insert into dbo.sub_statusHistory(subscriberID, oldStatusID, statusID, enteredByMemberID)
values(@subscriberID, NULL, @statusID, nullif(@recordedByMemberID,0))
 
RETURN 0

on_error:
	select @subscriberID = 0
	RETURN -1

GO

use customApps
GO
ALTER PROC [dbo].[TAGD_importNationalData]
@batchDate datetime,
@recordedByMemberID int,
@statsSessionID int,
@pathToImport varchar(200), 
@pathToExport varchar(200),
@importResult xml OUTPUT

AS

SET NOCOUNT ON

DECLARE @orgID int, @siteID int, @cmd varchar(600), @IDList VARCHAR(max), @finalMSG varchar(max), @emailtouse varchar(300),
	@flatfile varchar(200)
SELECT @orgID = membercentral.dbo.fn_getOrgIDFromOrgCode('TAGD')
SELECT @siteID = membercentral.dbo.fn_getSiteIDFromSiteCode('TAGD')

-- create temp tables
IF OBJECT_ID('tempdb..##tagd_nationals_alpha') IS NOT NULL
	DROP TABLE ##tagd_nationals_alpha
CREATE TABLE ##tagd_nationals_alpha (
	[rowid] [int] NOT NULL,
	[ID] [varchar](50) NOT NULL,
	[MEMBER TYPE] [varchar](10) NOT NULL DEFAULT (''),
	[MEMBER TYPE DESCRIPTION] [varchar](100) NOT NULL DEFAULT (''),
	[CATEGORY DESCRIPTION] [varchar](200) NOT NULL DEFAULT (''),
	[FULL NAME] [varchar](200) NOT NULL DEFAULT (''),
	[LAST FIRST] [varchar](200) NOT NULL DEFAULT (''),
	[FIRST NAME] [varchar](75) NOT NULL DEFAULT (''),
	[MIDDLE NAME] [varchar](25) NOT NULL DEFAULT (''),
	[LAST NAME] [varchar](75) NOT NULL DEFAULT (''),
	[SUFFIX] [varchar](50) NOT NULL DEFAULT (''),
	[DESIGNATION] [varchar](max) NULL,
	[WORK PHONE] [varchar](40) NULL,
	[HOME PHONE] [varchar](40) NULL,
	[FAX] [varchar](40) NULL,
	[FULL ADDRESS] [varchar](200) NOT NULL DEFAULT (''),
	[ADDRESS 1] [varchar](100) NULL,
	[ADDRESS 2] [varchar](100) NULL,
	[CITY] [varchar](35) NULL,
	[STATE PROVINCE] [varchar](25) NULL,
	[ZIP] [varchar](25) NULL,
	[GRAD DATE] datetime NULL,
	[DENTAL SCHOOL] [varchar](300) NULL,
	[JOIN DATE] datetime NULL,
	[PAID THRU] datetime NULL,
	[DO NOT MAIL] [varchar](300) NOT NULL DEFAULT (''),
	[EMAIL] [varchar](255) NULL,
	[COMPONENT TITLE] [varchar](300) NOT NULL DEFAULT (''),
	[BIRTH DATE] datetime NULL,
	[BAD ADDRESS] [varchar](300) NOT NULL DEFAULT ('')
)

IF OBJECT_ID('tempdb..##tagd_nationals_new') IS NOT NULL
	DROP TABLE ##tagd_nationals_new
CREATE TABLE ##tagd_nationals_new (
	[rowid] [int] NOT NULL,
	[ID] [varchar](50) NOT NULL,
	[FIRST NAME] [varchar](75) NOT NULL DEFAULT (''), 
	[MIDDLE NAME] [varchar](25) NOT NULL DEFAULT (''), 
	[LAST NAME] [varchar](75) NOT NULL DEFAULT (''), 
	[WORK ADDRESS 1] [varchar](100) NULL, 
	[WORK ADDRESS 2] [varchar](100) NULL, 
	[WORK CITY] [varchar](35) NULL, 
	[WORK STATE] [varchar](25) NULL, 
	[WORK ZIP] [varchar](25) NULL, 
	[WORK PHONE] [varchar](40) NULL, 
	[WORK FAX] [varchar](40) NULL, 
	[WORK EMAIL] [varchar](200) NULL, 
	[HOME ADDRESS 1] [varchar](100) NULL, 
	[HOME ADDRESS 2] [varchar](100) NULL, 
	[HOME CITY] [varchar](35) NULL, 
	[HOME STATE] [varchar](25) NULL, 
	[HOME ZIP] [varchar](25) NULL, 
	[HOME PHONE] [varchar](40) NULL, 
	[HOME FAX] [varchar](40) NULL, 
	[HOME EMAIL] [varchar](255) NULL, 
	[BIRTH DATE] datetime NULL,
	[WEBSITE] [varchar](200) NULL, 
	[PREFERRED ADDRESS] [varchar](200) NOT NULL DEFAULT (''), 
	[MEMBER TYPE] [varchar](200) NOT NULL DEFAULT (''), 
	[MEMBER TYPE CATEGORY] [varchar](200) NOT NULL DEFAULT (''), 
	[GENDER] [varchar](200) NULL , 
	[ETHNICITY] [varchar](200) NULL, 
	[MEMBER OF OTHER DENTAL ORGANIZATION] [varchar](200) NOT NULL DEFAULT (''), 
	[PREVIOUS MEMBER] [varchar](200) NOT NULL DEFAULT (''), 
	[JOIN DATE] datetime NULL,
	[PAID THRU] datetime NULL,
	[COMPONENT] [varchar](200) NOT NULL DEFAULT (''), 
	[DENTAL DEGREE] [varchar](200) NOT NULL DEFAULT (''), 
	[DENTAL SCHOOL] [varchar](200) NOT NULL DEFAULT (''), 
	[GRAD_DATE] datetime NULL,
	[GRADE] [varchar](200) NOT NULL DEFAULT (''), 
	[US RESIDENT] [varchar](200) NOT NULL DEFAULT (''), 
	[POSTDOCTORAL INSTITUTION] [varchar](200) NOT NULL DEFAULT (''), 
	[USA LICENSE] [varchar](200) NOT NULL DEFAULT (''), 
	[LICENSENO] [varchar](200) NULL, 
	[FEDERAL SERVICES] [varchar](200) NOT NULL DEFAULT (''), 
	[BRANCH] [varchar](200) NOT NULL DEFAULT (''), 
	[PRACTICE ENVIRONMENT] [varchar](200) NOT NULL DEFAULT (''), 
	[SPECIALTY] [varchar](200) NOT NULL DEFAULT ('')
)


IF OBJECT_ID('tempdb..##tagd_nationals_dues') IS NOT NULL
	DROP TABLE ##tagd_nationals_dues
CREATE TABLE ##tagd_nationals_dues (
	[rowid] [int] NOT NULL,
	[ID] [varchar](50) NOT NULL,
	[CHAPTER] [varchar](200) NOT NULL DEFAULT (''), 
	[FIRST NAME] [varchar](75) NOT NULL DEFAULT (''), 
	[MIDDLE NAME] [varchar](25) NOT NULL DEFAULT (''), 
	[LAST NAME] [varchar](75) NOT NULL DEFAULT (''), 
	[SUFFIX] [varchar](50) NOT NULL DEFAULT (''), 
	[DESIGNATION] [varchar](max) NULL, 
	[ADDRESS 1] [varchar](100) NOT NULL DEFAULT (''), 
	[ADDRESS 2] [varchar](100) NOT NULL DEFAULT (''), 
	[CITY] [varchar](35) NOT NULL DEFAULT (''), 
	[STATE] [varchar](25) NOT NULL DEFAULT (''), 
	[ZIP] [varchar](25) NOT NULL DEFAULT (''), 
	[WORK PHONE] [varchar](40) NOT NULL DEFAULT (''), 
	[FAX] [varchar](40) NOT NULL DEFAULT (''), 
	[EMAIL] [varchar](255) NULL, 
	[MEMBER TYPE] [varchar](200) NOT NULL DEFAULT (''), 
	[MEMBER TYPE DESCRIPTION] [varchar](200) NOT NULL DEFAULT (''), 
	[CATEGORY] [varchar](200) NOT NULL DEFAULT (''), 
	[CATEGORY DESCRIPTION] [varchar](200) NOT NULL DEFAULT (''), 
	[MEMBER TYPE BREAKDOWN] [varchar](200) NOT NULL DEFAULT (''), 
	[GRAD DATE] datetime NULL,
	[JOIN DATE] datetime NULL,
	[PAID THRU] datetime NULL,
	[CONSTITUENT] [varchar](200) NOT NULL DEFAULT (''), 
	[CONSTITUENT DUES] [decimal](9,2) NOT NULL, 
	[CONSTITUENT PAID] [decimal](9,2) NOT NULL, 
	[CONSTITUENT ABBREVIATION] [varchar](200) NOT NULL DEFAULT ('')
)


-- import csv files into temp tables.
BEGIN TRY
	SELECT @cmd = 'BULK INSERT ##tagd_nationals_alpha FROM ''' + @pathToImport + '1.csv'' WITH (DATAFILETYPE = ''widechar'', FIELDTERMINATOR = '''+ char(7) + ''', FIRSTROW = 2);'
	exec(@cmd)
	SELECT @cmd = 'BULK INSERT ##tagd_nationals_new FROM ''' + @pathToImport + '2.csv'' WITH (DATAFILETYPE = ''widechar'', FIELDTERMINATOR = '''+ char(7) + ''', FIRSTROW = 2);'
	exec(@cmd)
	SELECT @cmd = 'BULK INSERT ##tagd_nationals_dues FROM ''' + @pathToImport + '3.csv'' WITH (DATAFILETYPE = ''widechar'', FIELDTERMINATOR = '''+ char(7) + ''', FIRSTROW = 2);'
	exec(@cmd)
END TRY
BEGIN CATCH
	SET @importResult = '<import date="" flatfile=""><errors><error msg="Unable to import data. Ensure the columns are in the correct order and match previous imports." severity="fatal" /></errors></import>'
	GOTO on_cleanup
END CATCH


/* *********** */
/* DATA CHECKS */
/* *********** */
-- ensure IDs appear only once and set as primary key
BEGIN TRY
	ALTER TABLE ##tagd_nationals_alpha ADD CONSTRAINT PK__tagd_nationals_alpha PRIMARY KEY CLUSTERED (ID) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
END TRY
BEGIN CATCH
	SET @IDList = null
	SELECT @IDList = COALESCE(@IDList + ', ', '') + ID 
		from ##tagd_nationals_alpha
		group by ID
		having count(*) > 1
	SET @importResult = '<import date="" flatfile=""><errors><error msg="The file uploaded for Constituent Alpha Roster contains multiple rows for the following IDs: ' + @IDList + '. Ensure each ID appears only once in the file." severity="fatal" /></errors></import>'
	GOTO on_cleanup
END CATCH

-- ensure IDs appear only once and set as primary key
BEGIN TRY
	ALTER TABLE ##tagd_nationals_new ADD CONSTRAINT PK__tagd_nationals_new PRIMARY KEY CLUSTERED (ID) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
END TRY
BEGIN CATCH
	SET @IDList = null
	SELECT @IDList = COALESCE(@IDList + ', ', '') + ID 
		from ##tagd_nationals_new
		group by ID
		having count(*) > 1
	SET @importResult = '<import date="" flatfile=""><errors><error msg="The file uploaded for Constituent New Members contains multiple rows for the following IDs: ' + @IDList + '. Ensure each ID appears only once in the file." severity="fatal" /></errors></import>'
	GOTO on_cleanup
END CATCH

-- no missing PAID THRU dates for accounting
IF EXISTS (select top 1 ID from ##tagd_nationals_dues where [PAID THRU] is null) BEGIN
	SET @IDList = null
	SELECT @IDList = COALESCE(@IDList + ', ', '') + ID 
		from ##tagd_nationals_dues
		where [PAID THRU] is null
		group by ID
	SET @importResult = '<import date="" flatfile=""><errors><error msg="The file uploaded for Constituent Dues contains missing Paid Thru Dates for the following IDs: ' + @IDList + '. Ensure each row has a Paid Thru Date in the file." severity="fatal" /></errors></import>'
	GOTO on_cleanup
END

-- no negative amounts for accounting
IF EXISTS (select top 1 ID from ##tagd_nationals_dues where [Constituent PAID] < 0) BEGIN
	SET @IDList = null
	SELECT @IDList = COALESCE(@IDList + ', ', '') + ID 
		from ##tagd_nationals_dues
		where [Constituent PAID] < 0
		group by ID
	SET @importResult = '<import date="" flatfile=""><errors><error msg="The file uploaded for Constituent Dues contains negative CONSTITUENT PAID amounts for the following IDs: ' + @IDList + '. Negative amounts are not permitted." severity="fatal" /></errors></import>'
	GOTO on_cleanup
END



/* ****************** */
/* MEMBER DATA IMPORT */
/* ****************** */
-- flatten alpha table
IF OBJECT_ID('tempdb..##tagd_nationals_Members') IS NOT NULL
	DROP TABLE ##tagd_nationals_Members
select 
	m.prefix, 
	alpha.[FIRST NAME] as firstname, alpha.[MIDDLE NAME] as middlename, alpha.[LAST NAME] as lastname, alpha.SUFFIX as suffix, 
	cast(alpha.[DESIGNATION] as varchar(100)) as professionalSuffix, alpha.[ID] as memberNumber,
	m.company, '' as BillingAddressType, 
	
	vw.[Mailing Address_attn], 
	alpha.[ADDRESS 1] as [Mailing Address_address1], alpha.[ADDRESS 2] as [Mailing Address_address2], alpha.[CITY] as [Mailing Address_city],
	alpha.[STATE PROVINCE] as [Mailing Address_stateprov], alpha.[ZIP] as [Mailing Address_postalCode], cast('' as varchar(50)) as [Mailing Address_county],
	cast('United States' as varchar(100)) as [Mailing Address_country], alpha.[WORK PHONE] as [Mailing Address_Phone], alpha.[FAX] as [Mailing Address_Fax],
	vw.[Mailing Address_Cell], vw.[Mailing Address_2nd Phone],
 
	vw.[Office Address_address1], vw.[Office Address_address2], vw.[Office Address_city], vw.[Office Address_stateprov],
	vw.[Office Address_postalCode], vw.[Office Address_county], vw.[Office Address_country], vw.[Office Address_Phone], 
	vw.[Office Address_Fax], vw.[Office Address_Cell], vw.[Office Address_2nd Phone],

	vw.[Other Address_address1], vw.[Other Address_address2], vw.[Other Address_city], vw.[Other Address_stateprov], vw.[Other Address_postalCode],
	vw.[Other Address_county], vw.[Other Address_country], vw.[Other Address_Phone], vw.[Other Address_Fax], vw.[Other Address_Cell],
	vw.[Other Address_2nd Phone],

	coalesce(new.[HOME ADDRESS 1],vw.[Home Address_address1]) as [Home Address_address1],
	coalesce(new.[HOME ADDRESS 2],vw.[Home Address_address2]) as [Home Address_address2],	
	coalesce(new.[HOME CITY],vw.[Home Address_city]) as [Home Address_city],	
	coalesce(new.[HOME STATE],vw.[Home Address_stateprov]) as [Home Address_stateprov], 
	coalesce(new.[HOME ZIP],vw.[Home Address_postalCode]) as [Home Address_postalCode],
	vw.[Home Address_county], 
	case when new.[HOME STATE] is not null then 'United States' else vw.[Home Address_country] end as [Home Address_country], 
	alpha.[HOME PHONE] as [Home Address_Phone], 
	coalesce(new.[HOME FAX],vw.[Home Address_Fax]) as [Home Address_Fax],
	vw.[Home Address_Cell], vw.[Home Address_2nd Phone],

	coalesce(new.WEBSITE,vw.[Business Website]) as [Business Website],
	alpha.Email as [Email],
	coalesce(new.[HOME EMAIL],vw.[Personal Email]) as [Personal Email],
	vw.[Other Email],

	alpha.[JOIN DATE] as [AGD Join Date],
	cast(alpha.[ID] as varchar(255)) as [AGD Member Number], 
	alpha.[PAID THRU] as [AGD Paid Thru Date],
	cast(alpha.[FIRST NAME] as varchar(255)) as [Badge Name], 

	cast(case
	when alpha.[MEMBER TYPE] = 'AC' and alpha.[CATEGORY DESCRIPTION] = 'Disability Waiver' then 'General Dentist|Dues Waived'
	when alpha.[MEMBER TYPE] IN ('AC','EM','HM') then 'General Dentist'
	when alpha.[MEMBER TYPE] = 'AF' then 'Affiliate'
	when alpha.[MEMBER TYPE] = 'AS' then 'General Dentist'
	when alpha.[MEMBER TYPE] = 'RE' then 'Retired'
	when alpha.[MEMBER TYPE] = 'ST' then 'Student'
	else '' end as varchar(max)) as [Contact Type],
	alpha.[BIRTH DATE] as [Date of Birth],
	replace(replace(alpha.[DESIGNATION],',','|'),' ','') as [Designation],
	case 
	when new.ETHNICITY is not null and new.ETHNICITY = 'African American' then 'African-American'
	when new.ETHNICITY is not null and new.ETHNICITY = 'Hispanic or Latino' then 'Hispanic'
	when new.ETHNICITY is not null and new.ETHNICITY = 'White or Caucasian' then 'Caucasian'
	when new.ETHNICITY is not null and new.ETHNICITY = 'Asian or Pacific IslANDer' then 'Asian'
	else coalesce(new.ETHNICITY,vw.[Ethnicity])	end as [Ethnicity],
	coalesce(new.GENDER,vw.[Gender]) as [Gender],
	alpha.[GRAD DATE] as [Graduation Date],
	coalesce(new.LICENSENO,vw.[License Number]) as [License Number],
	cast(case
	when alpha.[MEMBER TYPE] = 'AC' and alpha.[CATEGORY DESCRIPTION] = 'Disability Waiver' then 'Disability Waiver'
	when alpha.[MEMBER TYPE] = 'AC' and alpha.[CATEGORY DESCRIPTION] = 'Residency' then 'Resident'
	when alpha.[MEMBER TYPE] = 'AC' then 'Active General Dentist'
	when alpha.[MEMBER TYPE] = 'AF' then 'Affiliate'
	when alpha.[MEMBER TYPE] = 'AS' then 'Associate'
	when alpha.[MEMBER TYPE] = 'EM' then 'Emeritus'
	when alpha.[MEMBER TYPE] = 'HM' then 'Honorary Member'
	when alpha.[MEMBER TYPE] = 'RE' then 'Retired'
	when alpha.[MEMBER TYPE] = 'ST' then 'Student Member'
	else '' end as varchar(255)) as [MemberType],
	cast(case 
	when alpha.[DENTAL SCHOOL] = 'Texas A&M Health Science Center Baylor College of Dentistry' then null
	when alpha.[DENTAL SCHOOL] = 'University of Texas Dental Branch at Houston' then null
	when alpha.[DENTAL SCHOOL] = 'University of Texas Health Science Center at San Antonio Dental School' then null
	else alpha.[DENTAL SCHOOL] end as varchar(255)) as [Other Dental School],
	cast(case alpha.[DENTAL SCHOOL]
	when 'Texas A&M Health Science Center Baylor College of Dentistry' then 'Baylor College of Dentistry'
	when 'University of Texas Dental Branch at Houston' then 'UTHSCS Houston'
	when 'University of Texas Health Science Center at San Antonio Dental School' then 'UTHSC San Antonio'
	else null end as varchar(255)) as [Texas Dental School],
	coalesce(new.[PRACTICE ENVIRONMENT],vw.[Type of Practice]) as [Type of Practice]
into ##tagd_nationals_Members
from ##tagd_nationals_alpha as alpha
left outer join membercentral.dbo.ams_members as m
	inner join membercentral.dbo.vw_memberdata_TAGD as vw on vw.memberID = m.memberID
	on m.orgID = @orgID and m.membernumber = alpha.[ID] and m.status <> 'D' and m.memberid = m.activeMemberID
left outer join ##tagd_nationals_new as new on new.ID = alpha.ID

-- get active members in database now so we dont inactivate them
insert into ##tagd_nationals_Members (
	[prefix],[firstname],[middlename],[lastname],[suffix],[professionalSuffix],[memberNumber],[company],
	[Mailing Address_attn],[Mailing Address_address1],[Mailing Address_address2],
	[Mailing Address_city],[Mailing Address_stateprov],[Mailing Address_postalCode],[Mailing Address_county],[Mailing Address_country],[Mailing Address_Phone],
	[Mailing Address_Fax],[Mailing Address_Cell],[Mailing Address_2nd Phone],[Office Address_address1],[Office Address_address2],[Office Address_city],
	[Office Address_stateprov],[Office Address_postalCode],[Office Address_county],[Office Address_country],[Office Address_Phone],[Office Address_Fax],
	[Office Address_Cell],[Office Address_2nd Phone],[Other Address_address1],[Other Address_address2],[Other Address_city],[Other Address_stateprov],
	[Other Address_postalCode],[Other Address_county],[Other Address_country],[Other Address_Phone],[Other Address_Fax],[Other Address_Cell],
	[Other Address_2nd Phone],[Home Address_address1],[Home Address_address2],[Home Address_city],[Home Address_stateprov],[Home Address_postalCode],
	[Home Address_county],[Home Address_country],[Home Address_Phone],[Home Address_Fax],[Home Address_Cell],[Home Address_2nd Phone],[Business Website],
	[Email],[Personal Email],[Other Email],[AGD Join Date],[AGD Member Number],[AGD Paid Thru Date],[Badge Name],[Contact Type],[Date of Birth],[Designation],
	[Ethnicity],[Gender],[Graduation Date],[License Number],[MemberType],[Other Dental School],[Texas Dental School],[Type of Practice]
)
select m.prefix, m.firstname, m.middlename, m.lastname, m.suffix, m.professionalSuffix, m.membernumber, m.company, 
	vw.[Mailing Address_attn], vw.[Mailing Address_address1], vw.[Mailing Address_address2],
	vw.[Mailing Address_city],vw.[Mailing Address_stateprov],vw.[Mailing Address_postalCode],vw.[Mailing Address_county],vw.[Mailing Address_country],vw.[Mailing Address_Phone],
	vw.[Mailing Address_Fax],vw.[Mailing Address_Cell],vw.[Mailing Address_2nd Phone],vw.[Office Address_address1],vw.[Office Address_address2],vw.[Office Address_city],
	vw.[Office Address_stateprov],vw.[Office Address_postalCode],vw.[Office Address_county],vw.[Office Address_country],vw.[Office Address_Phone],vw.[Office Address_Fax],
	vw.[Office Address_Cell],vw.[Office Address_2nd Phone],vw.[Other Address_address1],vw.[Other Address_address2],vw.[Other Address_city],vw.[Other Address_stateprov],
	vw.[Other Address_postalCode],vw.[Other Address_county],vw.[Other Address_country],vw.[Other Address_Phone],vw.[Other Address_Fax],vw.[Other Address_Cell],
	vw.[Other Address_2nd Phone],vw.[Home Address_address1],vw.[Home Address_address2],vw.[Home Address_city],vw.[Home Address_stateprov],vw.[Home Address_postalCode],
	vw.[Home Address_county],vw.[Home Address_country],vw.[Home Address_Phone],vw.[Home Address_Fax],vw.[Home Address_Cell],vw.[Home Address_2nd Phone],vw.[Business Website],
	vw.[Email],vw.[Personal Email],vw.[Other Email],vw.[AGD Join Date],vw.[AGD Member Number],vw.[AGD Paid Thru Date],vw.[Badge Name],vw.[Contact Type],vw.[Date of Birth],vw.[Designation],
	vw.[Ethnicity],vw.[Gender],vw.[Graduation Date],vw.[License Number],vw.[MemberType],vw.[Other Dental School],vw.[Texas Dental School],vw.[Type of Practice]
from membercentral.dbo.ams_members as m
inner join membercentral.dbo.vw_memberdata_TAGD as vw on vw.memberID = m.memberID
where m.orgID = @orgID
and m.status = 'A'
and m.memberid = m.activeMemberID
and m.memberTypeID = 2
and not exists (select membernumber from ##tagd_nationals_Members where membernumber = m.membernumber)

-- add rowID
ALTER TABLE ##tagd_nationals_Members ADD rowIDtemp int IDENTITY(1,1), rowID int NULL;
update ##tagd_nationals_Members set rowID = rowIDtemp;
ALTER TABLE ##tagd_nationals_Members DROP COLUMN rowIDtemp;

-- move data to holding tables
EXEC memberCentral.dbo.ams_importMemberData_tempToHolding @orgid=@orgID, @tmptbl='##tagd_nationals_Members', @pathToExport=@pathToExport, @importResult=@importResult OUTPUT

-- if no errors, run member import
IF @importResult.value('count(/import/errors/error)','int') = 0
BEGIN
	SELECT @flatfile = @importResult.value('(/import/@flatfile)[1]','varchar(160)')
	EXEC membercentral.dbo.ams_importMemberData_holdingToPerm @orgID=@orgID, @flatfile=@flatfile

	DECLARE @smtpserver varchar(20)
	SELECT @smtpserver = smtpserver from membercentral.dbo.fn_getServerSettings()		

	BEGIN TRY
		EXEC membercentral.dbo.ams_importMemberDataHoldingResultReport @importResult=@importResult, @finalMSG=@finalMSG OUTPUT
		EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject='TAGD Membership Import Results',
			@message=@finalMSG, @priority='normal', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
		SELECT @emailtouse = emailImportResults from membercentral.dbo.organizations where orgID = @orgID	
		IF len(@emailtouse) > 0 BEGIN
			EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to=@emailtouse,
				@cc=null, @bcc='<EMAIL>', @subject='TAGD Membership Import Results',
				@message=@finalMSG, @priority='normal', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
		END
	END TRY
	BEGIN CATCH      
		DECLARE @ErrorMessage VARCHAR(max)      
		DECLARE @ErrorSeverity INT      
		DECLARE @ErrorState INT        
		SELECT @ErrorMessage = 'Error running TAGD Membership Import' + char(13) + char(10) + ERROR_MESSAGE(),   
			@ErrorSeverity = ERROR_SEVERITY(),   
			@ErrorState = ERROR_STATE()        
		EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject='Error Importing TAGD Data',
			@message=@ErrorMessage, @priority='high', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END CATCH


	/* ***************** */
	/* ACCOUNTING IMPORT */
	/* ***************** */
	-- prep acct table (ignore $0)
	IF OBJECT_ID('tempdb..##tagd_nationals_acct') IS NOT NULL
		DROP TABLE ##tagd_nationals_acct
	select 'TAGD Dues ' + cast(year([PAID THRU]) as varchar(4)) as SaleDescription,
		@batchDate as saleDate,
		[ID] as saleMemberID,
		[Constituent PAID] as saleAmount,
		'4010' as saleRevenueGL,
		rowID as saleID,
		rowID as rowID
	into ##tagd_nationals_acct
	from ##tagd_nationals_dues
	where [Constituent PAID] <> 0

	set @importResult = null
	EXEC membercentral.dbo.tr_importTransactions_toTemp @orgid=@orgID, @payProfileCode='AGDimport', @tmptbl='##tagd_nationals_acct', @pathToExport=@pathToExport, @importResult=@importResult OUTPUT

	-- if no errors, run accounting import
	IF @importResult.value('count(/import/errors/error)','int') = 0
	BEGIN
		SELECT @flatfile = @importResult.value('(/import/@flatfile)[1]','varchar(160)')
		set @importResult = null
		EXEC membercentral.dbo.tr_importTransactions_toPerm @orgID=@orgID, @siteID=@siteID, @recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, @flatfile=@flatfile, @importResult=@importResult OUTPUT


		IF @importResult.value('count(/import/errors/error)','int') = 0
		BEGIN
			/* ******************* */
			/* SUBSCRIPTION IMPORT */
			/* ******************* */
			-- Start prep work for importing subs
			IF OBJECT_ID('tempdb..#tagd_sub_renewals') IS NOT NULL
				DROP TABLE #tagd_sub_renewals
			IF OBJECT_ID('tempdb..#tagd_sub_newmembers') IS NOT NULL
				DROP TABLE #tagd_sub_newmembers
			IF OBJECT_ID('tempdb..#tagd_sub_newComponent') IS NOT NULL
				DROP TABLE #tagd_sub_newComponent
			IF OBJECT_ID('tempdb..#tagd_sub_switchedComponent') IS NOT NULL
				DROP TABLE #tagd_sub_switchedComponent
			IF OBJECT_ID('tempdb..#tagd_memberType') IS NOT NULL
				DROP TABLE #tagd_memberType
			IF OBJECT_ID('tempdb..##tagd_import_subscriptions') IS NOT NULL
				DROP TABLE ##tagd_import_subscriptions
			IF OBJECT_ID('tempdb..#tagd_sub_expireComponent') IS NOT NULL
				DROP TABLE #tagd_sub_expireComponent

			declare @subscriberIDList varchar(8000), @membershipSubscriptionType varchar(100), 
				@componentSubscriptionType varchar(100), @membershipSubscriptionName varchar(100),
				@thismemberid int, @thissubscriptionID int, @thisparentSubscriberID int, 
				@thisRFID int, @thisGLAccountID int, @thisstatus varchar(1), @thissubStartDate datetime,
				@thissubEndDate datetime, @thisgraceEndDate datetime, @thispcfree bit, 
				@thisactivationOptionCode char(1), @thisrecordedByMemberID int,  @thisbypassQueue bit,
				@thissubscriberID int, @thisexistingComponentSubscriberID int, @trashID int,
				@autoID int, @currentTimeStamp datetime
			set @currentTimeStamp = getdate()
			set @membershipSubscriptionType = 'Membership Dues'
			set @componentSubscriptionType = 'Components'
			set @membershipSubscriptionName = 'TAGD Member'

			CREATE TABLE #tagd_sub_renewals (memberID int, memberNumber varchar(100), subStartDate datetime,
				subEndDate datetime, rootSubscriptionID int, componentSubscriptionID int, treecode uniqueIdentifier)
			CREATE TABLE #tagd_sub_newmembers (memberID int, memberNumber varchar(100), subStartDate datetime,
				subEndDate datetime, rootSubscriptionID int, componentSubscriptionID int, treecode uniqueIdentifier)
			CREATE TABLE #tagd_sub_newComponent (autoID int IDENTITY(1,1) PRIMARY KEY, memberID int,
				memberNumber varchar(100), existingRootSubscriberID int, existingComponentSubscriberID int,
				subStartDate datetime, subEndDate datetime, graceEndDate datetime, rootSubscriptionID int,
				componentSubscriptionID int, rfid int, GLAccountID int, status char(1), pcfree bit, 
				activationOptionCode varchar(1), existingComponentSubscriptionID int)
			CREATE TABLE #tagd_sub_switchedComponent (autoID int IDENTITY(1,1) PRIMARY KEY, memberID int,
				memberNumber varchar(100), existingRootSubscriberID int, existingComponentSubscriberID int,
				subStartDate datetime, subEndDate datetime, graceEndDate datetime, rootSubscriptionID int,
				componentSubscriptionID int, rfid int, GLAccountID int, status char(1), pcfree bit, 
				activationOptionCode varchar(1), existingComponentSubscriptionID int)
			CREATE TABLE #tagd_memberType (tempMemberTypeId int NOT NULL identity(1,1),
				ValueForMemberTypeCustomField varchar(255) NOT NULL DEFAULT (''), 
				MemberTypeValueFromAGDAlphaRoster varchar(10) NOT NULL DEFAULT (''), 
				CategoryDescriptionValueFromAGDAlphaRoster varchar(255) NULL DEFAULT (''), 
				MembershipDuesTAGDMemberSubRate varchar(255) NOT NULL DEFAULT ('')
			)
			CREATE TABLE #tagd_sub_expireComponent (
				autoID int IDENTITY(1,1) 
				PRIMARY KEY, memberID int,
				memberNumber varchar(100), 
				existingRootSubscriberID int, 
				existingComponentSubscriberID int,
				existingComponentSubscriptionID int,
				rootSubscriptionID int,
				rfid int, GLAccountID int, status char(1), pcfree bit, 
				activationOptionCode varchar(1)
			)

			insert into #tagd_sub_renewals (memberID, memberNumber, subStartDate, subEndDate, rootSubscriptionID, 
				componentSubscriptionID, treecode)
			select activeMember.memberID, na.id, 
				newStartDate = case 
					when (isnull(na.[JOIN DATE],'1/1/1900') > cast (cast(YEAR(na.[PAID THRU]) as nvarchar)+'0101' as datetime)) and (na.[JOIN DATE] > DATEADD(dd, DATEDIFF(dd, 0,dateadd(d,1,max(ss.subenddate))), 0))
						then isnull(na.[JOIN DATE],'1/1/1900')
					when (max(ss.subenddate) > cast (cast(YEAR(na.[PAID THRU]) as nvarchar)+'0101' as datetime)) and (DATEADD(dd, DATEDIFF(dd, 0,dateadd(d,1,max(ss.subenddate))), 0) > isnull(na.[JOIN DATE],'1/1/1900'))
						then DATEADD(dd, DATEDIFF(dd, 0,dateadd(d,1,max(ss.subenddate))), 0)
					else
						cast (cast(YEAR(na.[PAID THRU]) as nvarchar)+'0101' as datetime) 
					end,
				newSubEndDate = dateadd(ms,-3,dateadd(day,1,[Paid Thru])),
				subs.subscriptionID as rootSubscriptionID, 
				csubs.subscriptionID as componentSubscripionID,
				treecode = newid()
			from membercentral.dbo.sub_types t
			inner join membercentral.dbo.sub_subscriptions subs on subs.typeID = t.typeID
				and t.typeName = @membershipSubscriptionType
				and subs.subscriptionName = @membershipSubscriptionName
				and t.siteID = @siteID
			inner join membercentral.dbo.sub_subscribers ss on ss.subscriptionID = subs.subscriptionID
				and ss.parentSubscriberID is null
			inner join membercentral.dbo.sub_statuses st on st.statusID = ss.statusID
				and st.statuscode in ('A','I','P')
			inner join membercentral.dbo.ams_members m on m.memberID = ss.memberID
			inner join membercentral.dbo.ams_members activeMember on activeMember.memberID = m.activeMemberID
				and activeMember.status in ('A','I')
			--make sure we have subscriberID with the latest end date
			left outer join membercentral.dbo.sub_subscribers otherterms_ss
				inner join membercentral.dbo.sub_statuses otherterms_st on otherterms_st.statusID = otherterms_ss.statusID
					and otherterms_st.statuscode in ('A','I','P')
				inner join membercentral.dbo.ams_members otherterms_m on otherterms_m.memberID = otherterms_ss.memberID
				on ss.subscriptionID = otherterms_ss.subscriptionID
					and ss.subenddate < otherterms_ss.subenddate
					and otherterms_m.activeMemberID = activeMember.memberID
			inner join ##tagd_nationals_alpha na on na.id = activeMember.membernumber
				and na.[Paid Thru] is not null
				and ss.subenddate < na.[paid thru]
				and na.[paid thru] > @currentTimeStamp
			inner join membercentral.dbo.sub_types ct on ct.siteID = t.siteID
				and ct.typeName = @componentSubscriptionType
			left outer join membercentral.dbo.sub_subscriptions csubs on csubs.typeID = ct.typeID
				and csubs.subscriptionName = na.[COMPONENT TITLE]
			where otherterms_ss.subscriberID is null
			group by activeMember.memberID,na.id,  na.[paid thru], na.[JOIN DATE], subs.subscriptionID, csubs.subscriptionID
			order by na.[JOIN DATE] desc

			insert into #tagd_sub_newmembers (memberID, memberNumber,subStartDate,subEndDate, rootSubscriptionID, componentSubscriptionID,treecode)
			select 
				activeMember.memberID,
				na.id,
				newStartDate = case 
					when (isnull(na.[JOIN DATE],'1/1/1900') > cast (cast(YEAR(na.[PAID THRU]) as nvarchar)+'0101' as datetime))
						then isnull(na.[JOIN DATE],'1/1/1900')
					else
						cast (cast(YEAR(na.[PAID THRU]) as nvarchar)+'0101' as datetime) 
					end,
				newSubEndDate = dateadd(ms,-3,dateadd(day,1,[Paid Thru])),
				subs.subscriptionID as rootSubscriptionID, 
				csubs.subscriptionID as componentSubscripionID,
				treecode = newid()
			from 
				##tagd_nationals_alpha na
				inner join membercentral.dbo.ams_members activeMember
					on activeMember.memberNumber = na.id
					and activeMember.orgID = @orgID
					and activeMember.status in ('A','I')
					and activeMember.memberID = activeMember.activeMemberID
					and na.[paid thru] > @currentTimeStamp
				inner join membercentral.dbo.ams_members m
					on activeMember.memberID = m.activeMemberID
				inner join membercentral.dbo.sub_types t
					on t.typeName = @membershipSubscriptionType
					and t.siteID = @siteID
				inner join membercentral.dbo.sub_subscriptions subs
					on subs.typeID = t.typeID
					and subs.subscriptionName = @membershipSubscriptionName
				left outer join membercentral.dbo.sub_subscribers ss
					inner join membercentral.dbo.sub_statuses st
						on st.statusID = ss.statusID
						and st.statuscode in ('A','I','P')
						and ss.parentSubscriberID is null
				on m.memberID = ss.memberID
					and ss.subscriptionID = subs.subscriptionID
				inner join membercentral.dbo.sub_types ct
					on ct.siteID = @siteID
					and ct.typeName = @componentSubscriptionType
				left outer join membercentral.dbo.sub_subscriptions csubs
					on csubs.typeID = ct.typeID
					and csubs.subscriptionName = na.[COMPONENT TITLE]
			group by 
				activeMember.memberID, na.id,  na.[paid thru], na.[JOIN DATE], subs.subscriptionID, csubs.subscriptionID
			having 
				max(isnull(ss.subscriberID,0)) = 0
			order by 
				na.[JOIN DATE] desc

			insert into #tagd_sub_newComponent (
				memberID, 
				memberNumber,
				subStartDate,
				subEndDate, 
				graceEndDate, 
				rootSubscriptionID, 
				componentSubscriptionID, 
				existingRootSubscriberID, 
				existingComponentSubscriberID,
				existingComponentSubscriptionID
			)
			select 
				activeMember.memberID,
				na.id,
				-- case statement to handle changing future Accepted subscriptions
				newStartDate = case 
					when (@currentTimeStamp > ss.substartDate)
						then @currentTimeStamp
					else
						ss.substartDate
					end,
				newSubEndDate = case 
					when (@currentTimeStamp > ss.subEndDate)
						then cast (cast(YEAR(@currentTimeStamp) as nvarchar)+'1231' as datetime)
					else
						ss.subEndDate
					end,
				graceEndDate = cast (cast(YEAR(ss.subEndDate) + 1 as nvarchar)+'0331' as datetime),
				subs.subscriptionID as rootSubscriptionID, 
				correct_csubs.subscriptionID as componentSubscripionID,
				ss.rootSubscriberID as existingRootSubscriberID, 
				current_css.subscriberID as existingComponentSubscriberID,
				current_css.subscriptionID as existingComponentSubscriptionID
			from 
				membercentral.dbo.sub_types t
				inner join membercentral.dbo.sub_subscriptions subs
					on subs.typeID = t.typeID
					and t.typeName = @membershipSubscriptionType
					and subs.subscriptionName = @membershipSubscriptionName
					and t.siteID = @siteID
				inner join membercentral.dbo.sub_subscribers ss
					on ss.subscriptionID = subs.subscriptionID
					and ss.parentSubscriberID is null
				inner join membercentral.dbo.sub_statuses st
					on st.statusID = ss.statusID
					and st.statuscode in ('A','I','P')
				inner join membercentral.dbo.ams_members m
					on m.memberID = ss.memberID
				inner join membercentral.dbo.ams_members activeMember
					on activeMember.memberID = m.activeMemberID
					and activeMember.status in ('A','I')
				inner join ##tagd_nationals_alpha na
					on na.id = activeMember.membernumber
					and na.[Paid Thru] is not null
				inner join membercentral.dbo.sub_types ct
					on ct.siteID = t.siteID
					and ct.typeName = @componentSubscriptionType
				inner join membercentral.dbo.sub_subscriptions correct_csubs
					on correct_csubs.typeID = ct.typeID
					and correct_csubs.subscriptionName = na.[COMPONENT TITLE]
				left outer join membercentral.dbo.sub_subscribers current_css
					inner join membercentral.dbo.sub_statuses current_cst
						on current_cst.statusID = current_css.statusID
						and current_cst.statuscode in ('A','I','P')
					on current_css.parentSubscriberID = ss.subscriberID
				and correct_csubs.subscriptionID = current_css.subscriptionID
			where
				current_css.subscriptionID is null
			group by 
				current_cst.statuscode, activeMember.memberID,na.id,  na.[paid thru], na.[JOIN DATE], subs.subscriptionID, 
				correct_csubs.subscriptionID, ss.rootSubscriberID, current_css.subscriberID,
				current_css.subscriptionID, ss.substartDate, ss.subEndDate
			order by na.[JOIN DATE] desc

			insert into #tagd_sub_switchedComponent (
				memberID, 
				memberNumber,
				subStartDate,
				subEndDate, 
				graceEndDate, 
				rootSubscriptionID, 
				componentSubscriptionID, 
				existingRootSubscriberID, 
				existingComponentSubscriberID,
				existingComponentSubscriptionID
			)
			select 
				activeMember.memberID,
				na.id,
				-- case statement to handle changing future Accepted subscriptions
				newStartDate = case 
					when (@currentTimeStamp > ss.substartDate)
						then @currentTimeStamp
					else
						ss.substartDate
					end,
				newSubEndDate = case 
					when (@currentTimeStamp > ss.subEndDate)
						then cast (cast(YEAR(@currentTimeStamp) as nvarchar)+'1231' as datetime)
					else
						ss.subEndDate
					end,
				graceEndDate = cast (cast(YEAR(ss.subEndDate) + 1 as nvarchar)+'0331' as datetime),
				subs.subscriptionID as rootSubscriptionID, 
				correct_csubs.subscriptionID as componentSubscripionID,
				ss.rootSubscriberID as existingRootSubscriberID, 
				current_css.subscriberID as existingComponentSubscriberID,
				current_css.subscriptionID as existingComponentSubscriptionID
			from 
				membercentral.dbo.sub_types t
				inner join membercentral.dbo.sub_subscriptions subs
					on subs.typeID = t.typeID
					and t.typeName = @membershipSubscriptionType
					and subs.subscriptionName = @membershipSubscriptionName
					and t.siteID = @siteID
				inner join membercentral.dbo.sub_subscribers ss
					on ss.subscriptionID = subs.subscriptionID
					and ss.parentSubscriberID is null
				inner join membercentral.dbo.sub_statuses st
					on st.statusID = ss.statusID
					and st.statuscode in ('A','I','P')
				inner join membercentral.dbo.ams_members m
					on m.memberID = ss.memberID
				inner join membercentral.dbo.ams_members activeMember
					on activeMember.memberID = m.activeMemberID
					and activeMember.status in ('A','I')
				inner join ##tagd_nationals_alpha na
					on na.id = activeMember.membernumber
					and na.[Paid Thru] is not null
				inner join membercentral.dbo.sub_types ct
					on ct.siteID = t.siteID
					and ct.typeName = @componentSubscriptionType
				inner join membercentral.dbo.sub_subscriptions correct_csubs
					on correct_csubs.typeID = ct.typeID
					and correct_csubs.subscriptionName = na.[COMPONENT TITLE]
				inner join membercentral.dbo.sub_subscribers current_css
					on current_css.parentSubscriberID = ss.subscriberID
					and correct_csubs.subscriptionID <> current_css.subscriptionID
				inner join membercentral.dbo.sub_statuses current_cst
					on current_cst.statusID = current_css.statusID
					and current_cst.statuscode in ('A','I','P')
			group by 
				activeMember.memberID,na.id,  na.[paid thru], na.[JOIN DATE], subs.subscriptionID, 
				correct_csubs.subscriptionID, ss.rootSubscriberID, current_css.subscriberID,
				current_css.subscriptionID, ss.substartDate, ss.subEndDate
			order by na.[JOIN DATE] desc

			insert into #tagd_sub_expireComponent (
				memberID, 
				memberNumber,
				rootSubscriptionID, 
				existingComponentSubscriptionID, 
				existingRootSubscriberID, 
				existingComponentSubscriberID
			)			
			select 
				activeMember.memberID,
				na.id,
				subs.subscriptionID as rootSubscriptionID, 
				current_css.subscriptionID as existingComponentSubscriptionID,
				ss.rootSubscriberID as existingRootSubscriberID, 
				current_css.subscriberID as existingComponentSubscriberID
			from 
				membercentral.dbo.sub_types t
				inner join membercentral.dbo.sub_subscriptions subs
					on subs.typeID = t.typeID
					and t.typeName = @membershipSubscriptionType
					and subs.subscriptionName = @membershipSubscriptionName
					and t.siteID = @siteID
				inner join membercentral.dbo.sub_subscribers ss
					on ss.subscriptionID = subs.subscriptionID
					and ss.parentSubscriberID is null
				inner join membercentral.dbo.sub_statuses st
					on st.statusID = ss.statusID
					and st.statuscode in ('A','I','P')
				inner join membercentral.dbo.ams_members m
					on m.memberID = ss.memberID
				inner join membercentral.dbo.ams_members activeMember
					on activeMember.memberID = m.activeMemberID
					and activeMember.status in ('A','I')
				inner join ##tagd_nationals_alpha na
					on na.id = activeMember.membernumber
					and na.[Paid Thru] is not null
				inner join membercentral.dbo.sub_types ct
					on ct.siteID = t.siteID
					and ct.typeName = @componentSubscriptionType
				inner join membercentral.dbo.sub_subscriptions correct_csubs
					on correct_csubs.typeID = ct.typeID
					and correct_csubs.subscriptionName <> na.[COMPONENT TITLE]
				inner join membercentral.dbo.sub_subscribers current_css
					on current_css.parentSubscriberID = ss.subscriberID
					and correct_csubs.subscriptionID = current_css.subscriptionID
				inner join membercentral.dbo.sub_statuses current_cst
					on current_cst.statusID = current_css.statusID
					and current_cst.statuscode in ('A','I','P')
				left outer join #tagd_sub_switchedComponent sc on
					current_css.subscriberID = sc.existingComponentSubscriberID
			where
				sc.existingComponentSubscriberID is null
			group by 
				activeMember.memberID,na.id, subs.subscriptionID, 
				current_css.subscriptionID, ss.rootSubscriberID, current_css.subscriberID,
				 ss.substartDate, ss.subEndDate
			union
				select 
					renewals.memberID,
					renewals.memberNumber,
					renewals.rootSubscriptionID, 
					sc.existingComponentSubscriptionID,
					sc.existingRootSubscriberID, 
					sc.existingComponentSubscriberID
				from 
					#tagd_sub_renewals  renewals
					inner join #tagd_sub_switchedComponent sc on 
						sc.rootSubscriptionID = renewals.rootSubscriptionID
						and sc.memberNumber = renewals.memberNumber
			order by 
				activeMember.memberID

			update sc set
				GLAccountID = case
					when subs.allowRateGLAccountOverride = 1 then isnull(r.GLAccountID,subs.GLAccountID) 
					else subs.GLAccountID
				end, 
				rfid=rf.rfID,
				pcfree=0, 
				activationOptionCode = sao.subActivationCode,
				Status = case 
					when subStartDate > @currentTimeStamp then 'P'
					else 'A'
				end
			from 
				#tagd_sub_switchedComponent sc
				inner join membercentral.dbo.sub_subscriptions subs
					on subs.subscriptionID = sc.componentSubscriptionID
				inner join membercentral.dbo.sub_rateSchedules as rs 
					on subs.scheduleID = rs.scheduleID 
					and rs.status = 'A'
				inner join membercentral.dbo.sub_rates r
					on r.scheduleID = rs.scheduleID
					and r.status = 'A'
					and r.isRenewalRate = 0 
					and r.rateName = 'Annual Term'
				inner join membercentral.dbo.sub_rateFrequencies rf on
					rf.rateID = r.rateID
					and rf.status = 'A'
				inner join membercentral.dbo.sub_frequencies f on
					f.frequencyID = rf.frequencyID
					and f.status = 'A'
				inner join membercentral.dbo.sub_rateFrequenciesMerchantProfiles as rfmp on 
					rfmp.rfid = rf.rfID 
					and rfmp.status = 'A' 
				inner join membercentral.dbo.mp_profiles as mp on 
					mp.profileID = rfmp.profileID 
					and mp.status = 'A' 
				inner join membercentral.dbo.sub_activationOptions sao
					on sao.subActivationID = subs.subAlternateActivationID

			update nc set
				GLAccountID = case
					when subs.allowRateGLAccountOverride = 1 then isnull(r.GLAccountID,subs.GLAccountID) 
					else subs.GLAccountID
				end, 
				rfid=rf.rfID,
				pcfree=0, 
				activationOptionCode = sao.subActivationCode,
				Status = case 
					when subStartDate > @currentTimeStamp then 'P'
					else 'A'
				end
			from 
				#tagd_sub_newComponent nc
				inner join membercentral.dbo.sub_subscriptions subs
					on subs.subscriptionID = nc.componentSubscriptionID
				inner join membercentral.dbo.sub_rateSchedules as rs 
					on subs.scheduleID = rs.scheduleID 
					and rs.status = 'A'
				inner join membercentral.dbo.sub_rates r
					on r.scheduleID = rs.scheduleID
					and r.status = 'A'
					and r.isRenewalRate = 0 
					and r.rateName = 'Annual Term'
				inner join membercentral.dbo.sub_rateFrequencies rf on
					rf.rateID = r.rateID
					and rf.status = 'A'
				inner join membercentral.dbo.sub_frequencies f on
					f.frequencyID = rf.frequencyID
					and f.status = 'A'
				inner join membercentral.dbo.sub_rateFrequenciesMerchantProfiles as rfmp on 
					rfmp.rfid = rf.rfID 
					and rfmp.status = 'A' 
				inner join membercentral.dbo.mp_profiles as mp on 
					mp.profileID = rfmp.profileID 
					and mp.status = 'A' 
				inner join membercentral.dbo.sub_activationOptions sao
					on sao.subActivationID = subs.subAlternateActivationID

			insert into #tagd_memberType (
				ValueForMemberTypeCustomField,
				MemberTypeValueFromAGDAlphaRoster,
				CategoryDescriptionValueFromAGDAlphaRoster,
				MembershipDuesTAGDMemberSubRate
			)
			select 'Active Member', 'AC', '',	'Active General Dentist'
			union
			select 'Affiliate', 'AF', '',	'Affiliate'
			union
			select 'Associate', 'AS', '',	'Associate'
			union
			select 'Disability Waiver', 'AC', 'Disability Waiver',	'Dues Waiver'
			union
			select 'Emeritus Member', 'EM', '',	'Emeritus'
			union
			select 'Honorary Member', 'HM', '',	'Honorary Member'
			union
			select 'Retired', 'RE', '',	'Retired'
			union
			select 'Resident', 'AC', 'Residency',	'Resident'
			union
			select 'Student Member', 'ST', '',	'Student'
			union
			select 'Financial Waiver', 'AC', 'Financial Waiver','Dues Waiver'

			IF OBJECT_ID('tempdb..#temp_tagd_nationals_alpha') IS NOT NULL
				DROP TABLE #temp_tagd_nationals_alpha

			-- Inner join alpha with memberType so we can get the right rate
			select * 
			into #temp_tagd_nationals_alpha
			from ##tagd_nationals_alpha na
			left outer join #tagd_memberType mt on ltrim(rtrim(mt.MemberTypeValueFromAGDAlphaRoster)) = ltrim(rtrim(na.[member type]))
				and ltrim(rtrim(mt.CategoryDescriptionValueFromAGDAlphaRoster )) = ltrim(rtrim(na.[category description]))
			order by na.[category description] desc

			-- Get the query to be passed to import subscriptions stored proc
			select *  
			into ##tagd_import_subscriptions 
			from (
				select 
					distinct renewals.memberNumber
					,@membershipSubscriptionType as SubscriptionType
					, subs.SubscriptionName
					,r.RateName
					, rf.rateAmt as LastPrice
					, f.frequencyShortName as Frequency
					, 'NO' as StoreModifiedRate
					, renewals.subStartDate as StartDate
					, renewals.subEndDate as EndDate
					, cast (cast(YEAR(renewals.subEndDate) + 1 as nvarchar)+'0331' as datetime) as graceEndDate
					, Status = case 
						when renewals.subStartDate > @currentTimeStamp then 'P'
						else 'A'
					end
					, NULL as ParentSubscriptionType
					, NULL  as ParentSubscriptionName
					, renewals.treecode as TreeCode
				from (
					select memberID, memberNumber,subStartDate,subEndDate, rootSubscriptionID, componentSubscriptionID, treecode from #tagd_sub_renewals
					union
					select memberID, memberNumber,subStartDate,subEndDate, rootSubscriptionID, componentSubscriptionID, treecode from #tagd_sub_newmembers 
				) as renewals
				inner join membercentral.dbo.sub_subscriptions as subs
					on renewals.rootSubscriptionID = subs.subscriptionID
					and subs.status = 'A' 
				inner join membercentral.dbo.sub_rateSchedules as rs 
					on subs.scheduleID = rs.scheduleID 
					and rs.status = 'A'
				inner join membercentral.dbo.sub_rates r
					on r.scheduleID = rs.scheduleID
					and r.status = 'A'
					and r.isRenewalRate = 0 
				inner join #tagd_memberType mt on 
					ltrim(rtrim(mt.MembershipDuesTAGDMemberSubRate)) = ltrim(rtrim(r.rateName))
				inner join #temp_tagd_nationals_alpha tmt on
					tmt.tempMemberTypeId = mt.tempMemberTypeId	
					and tmt.id = renewals.memberNumber	
				inner join membercentral.dbo.sub_rateFrequencies rf on
					rf.rateID = r.rateID
					and rf.status = 'A'
				inner join membercentral.dbo.sub_frequencies f on
					f.frequencyID = rf.frequencyID
					and f.status = 'A'
				inner join membercentral.dbo.sub_rateFrequenciesMerchantProfiles as rfmp on 
					rfmp.rfid = rf.rfID 
					and rfmp.status = 'A' 
				inner join membercentral.dbo.mp_profiles as mp on 
					mp.profileID = rfmp.profileID 
					and mp.status = 'A' 
				union
				select 
					distinct renewals.memberNumber
					,@componentSubscriptionType as SubscriptionType
					, subs.SubscriptionName
					,r.RateName
					, rf.rateAmt as LastPrice
					, f.frequencyShortName as Frequency
					, 'NO' as StoreModifiedRate
					, renewals.subStartDate as StartDate
					, renewals.subEndDate as EndDate
					, cast (cast(YEAR(renewals.subEndDate) + 1 as nvarchar)+'0331' as datetime) as graceEndDate
					, Status = case 
						when renewals.subStartDate > @currentTimeStamp then 'P'
						else 'A'
					end
					, @membershipSubscriptionType as ParentSubscriptionType
					, @membershipSubscriptionName  as ParentSubscriptionName
					, renewals.treecode as TreeCode
				from (
					select memberID, memberNumber,subStartDate,subEndDate, rootSubscriptionID, componentSubscriptionID, treecode from #tagd_sub_renewals
					union
					select memberID, memberNumber,subStartDate,subEndDate, rootSubscriptionID, componentSubscriptionID, treecode from #tagd_sub_newmembers 
				) as renewals
				inner join membercentral.dbo.sub_subscriptions as subs
					on renewals.componentSubscriptionID = subs.subscriptionID
					and subs.status = 'A' 
				inner join membercentral.dbo.sub_rateSchedules as rs 
					on subs.scheduleID = rs.scheduleID 
					and rs.status = 'A'
				inner join membercentral.dbo.sub_rates r
					on r.scheduleID = rs.scheduleID
					and r.status = 'A'
					and r.isRenewalRate = 0 
					and r.rateName = 'Annual Term'
				inner join membercentral.dbo.sub_rateFrequencies rf on
					rf.rateID = r.rateID
					and rf.status = 'A'
				inner join membercentral.dbo.sub_frequencies f on
					f.frequencyID = rf.frequencyID
					and f.status = 'A'
				inner join membercentral.dbo.sub_rateFrequenciesMerchantProfiles as rfmp on 
					rfmp.rfid = rf.rfID 
					and rfmp.status = 'A' 
				inner join membercentral.dbo.mp_profiles as mp on 
					mp.profileID = rfmp.profileID 
					and mp.status = 'A' 
			) tmp
			order by tmp.MemberNumber, tmp.ParentSubscriptionType

			DECLARE @qry varchar(400)
			SELECT @qry = 'ALTER TABLE ##tagd_import_subscriptions ADD rowID int NULL, rowID2 int identity(1,1);'
			EXEC(@qry)
			SELECT @qry = 'update ##tagd_import_subscriptions set rowID = rowID2;'
			EXEC(@qry)
			SELECT @qry = 'ALTER TABLE ##tagd_import_subscriptions DROP COLUMN rowID2;'
			EXEC(@qry)
			SELECT @qry = 'ALTER TABLE ##tagd_import_subscriptions ALTER COLUMN rowID int NOT NULL;'
			EXEC(@qry)

			-- Change components 
			select 
				@autoID = min(sc.autoID) 
			from 
				#tagd_sub_switchedComponent sc
				left outer join #tagd_sub_renewals sr on
					sr.componentSubscriptionID = sc.componentSubscriptionID
					and sc.memberNumber = sr.memberNumber
			where
				sr.componentSubscriptionID is null 

			while @autoID is not null
			begin
				select 
					@thismemberid=NULL, 
					@thissubscriptionID=NULL, 
					@thisparentSubscriberID=NULL, 
					@thisRFID=NULL, 
					@thisGLAccountID=NULL, 
					@thisstatus=NULL, 
					@thissubStartDate=NULL, 
					@thissubEndDate=NULL, 
					@thisgraceEndDate = NULL, 
					@thispcfree=NULL, 
					@thisactivationOptionCode=NULL, 
					@thisbypassQueue=NULL, 
					@thisexistingComponentSubscriberID = NULL

				select 
					@thismemberid=memberID, 
					@thissubscriptionID=componentSubscriptionID, 
					@thisparentSubscriberID=existingRootSubscriberID, 
					@thisRFID=rfID, 
					@thisGLAccountID=GLAccountID, 
					@thisstatus=status, 
					@thissubStartDate=subStartDate, 
					@thissubEndDate=subEndDate, 
					@thisgraceEndDate = graceEndDate , 
					@thispcfree=pcfree, 
					@thisactivationOptionCode=activationOptionCode, 
					@thisbypassQueue=0, 
					@thisexistingComponentSubscriberID = existingComponentSubscriberID
				from #tagd_sub_switchedComponent
				where autoID = @autoID


				exec membercentral.dbo.sub_expireSubscriber @subscriberID=@thisexistingComponentSubscriberID, @memberID=@thismemberid, 
					@siteID=@siteID, @enteredByMemberID=@recordedByMemberID, @statsSessionID=0, @AROption='C', @fReturnQuery=0

				exec membercentral.dbo.sub_addSubscriber @orgID=@orgID, @memberid=@thismemberid, @subscriptionID=@thissubscriptionID, 
					@parentSubscriberID=@thisparentSubscriberID, @RFID=@thisRFID, @GLAccountID=@thisGLAccountID, @status=@thisstatus, 
					@subStartDate=@thissubStartDate, @subEndDate=@thissubEndDate, @graceEndDate=@thisgraceEndDate, 
					@recogStartDate=@thissubStartDate, @recogEndDate=@thissubEndDate, @pcfree=@thispcfree, 
					@activationOptionCode=@thisactivationOptionCode, @recordedByMemberID=@recordedByMemberID, @bypassQueue=@thisbypassQueue, 
					@subscriberID=@trashID OUTPUT

				select 
					@autoID = min(sc.autoID) 
				from 
					#tagd_sub_switchedComponent sc
					left outer join #tagd_sub_renewals sr on
						sr.componentSubscriptionID = sc.componentSubscriptionID
						and sc.memberNumber = sr.memberNumber
				where
					sr.componentSubscriptionID is null 
					and sc.autoID > @autoID
			end

			-- Add individual components 
			set @autoID = null
			select 
				@autoID = min(nc.autoID) 
			from 
				#tagd_sub_newComponent nc
				left outer join #tagd_sub_switchedComponent sc on
					nc.componentSubscriptionID = sc.componentSubscriptionID
					and nc.memberNumber = sc.memberNumber
			where
				sc.componentSubscriptionID is null 

			while @autoID is not null
			begin

				select 
					@thismemberid=NULL, 
					@thissubscriptionID=NULL, 
					@thisparentSubscriberID=NULL, 
					@thisRFID=NULL, 
					@thisGLAccountID=NULL, 
					@thisstatus=NULL, 
					@thissubStartDate=NULL, 
					@thissubEndDate=NULL, 
					@thisgraceEndDate = NULL, 
					@thispcfree=NULL, 
					@thisactivationOptionCode=NULL, 
					@thisbypassQueue=NULL, 
					@thisexistingComponentSubscriberID = NULL

				select 
					@thismemberid=memberID, 
					@thissubscriptionID=componentSubscriptionID, 
					@thisparentSubscriberID=existingRootSubscriberID, 
					@thisRFID=rfID, 
					@thisGLAccountID=GLAccountID, 
					@thisstatus=status, 
					@thissubStartDate=subStartDate, 
					@thissubEndDate=subEndDate, 
					@thisgraceEndDate = graceEndDate , 
					@thispcfree=pcfree, 
					@thisactivationOptionCode=activationOptionCode, 
					@thisbypassQueue=0, 
					@thisexistingComponentSubscriberID = existingComponentSubscriberID
				from #tagd_sub_newComponent
				where autoID = @autoID

				exec membercentral.dbo.sub_addSubscriber @orgID=@orgID, @memberid=@thismemberid, @subscriptionID=@thissubscriptionID, 
					@parentSubscriberID=@thisparentSubscriberID, @RFID=@thisRFID, @GLAccountID=@thisGLAccountID, @status=@thisstatus, 
					@subStartDate=@thissubStartDate, @subEndDate=@thissubEndDate, @graceEndDate=@thisgraceEndDate, 
					@recogStartDate=@thissubStartDate, @recogEndDate=@thissubEndDate, @pcfree=@thispcfree, 
					@activationOptionCode=@thisactivationOptionCode, @recordedByMemberID=@recordedByMemberID, @bypassQueue=@thisbypassQueue, 
					@subscriberID=@trashID OUTPUT

				select 
					@autoID = min(nc.autoID) 
				from 
					#tagd_sub_newComponent nc
					left outer join #tagd_sub_switchedComponent sc on
						nc.componentSubscriptionID = sc.componentSubscriptionID		
						and nc.memberNumber = sc.memberNumber		
				where 
					nc.autoID > @autoID
					and sc.componentSubscriptionID is null
			end

			-- Exprire components 
			select @autoID = min(autoID) from #tagd_sub_expireComponent
			while @autoID is not null
			begin
				select 
					@thismemberid=memberID, 
					@thisparentSubscriberID=existingRootSubscriberID, 
					@thisbypassQueue=0, 
					@thisexistingComponentSubscriberID = existingComponentSubscriberID
				from #tagd_sub_expireComponent
				where autoID = @autoID

				exec membercentral.dbo.sub_expireSubscriber
					@subscriberID=@thisexistingComponentSubscriberID, 
					@memberID=@thismemberid, 
					@siteID=@siteID, 
					@enteredByMemberID=@recordedByMemberID, 
					@statsSessionID=0, 
					@AROption='C', 
					@fReturnQuery=0

				select @autoID = min(autoID) from #tagd_sub_expireComponent where autoID > @autoID
			end

			-- E-mail component results
			declare @subImportResult xml

			select @subImportResult = (
				select getdate() as "@date",
					isnull((select top 301  id as "@membernumber", [FULL NAME] as "@fullname"
					from ##tagd_nationals_alpha
					where len([COMPONENT TITLE]) = 0
					order by id
					FOR XML path('member'), root('nocomponent'), type),'<nocomponent/>'),
					isnull((select top 301  id as "@membernumber", [FULL NAME] as "@fullname"
					from ##tagd_nationals_alpha na
						inner join #tagd_sub_renewals sr on
							na.id = sr.memberNumber
							and len(na.[COMPONENT TITLE]) > 0
							and sr.componentSubscriptionID is null
					order by id
					FOR XML path('member'), root('invalidcomponent'), type),'<invalidcomponent/>'),
					isnull((select top 301  id as "@membernumber", [FULL NAME] as "@fullname"
					from ##tagd_nationals_alpha na
						inner join #tagd_sub_expireComponent ec on
							na.id = ec.memberNumber
					order by id
					FOR XML path('member'), root('expiredcomponent'), type),'<expiredcomponent/>')
				for xml path('subimport'), TYPE)

			declare @qrynomcomponent varchar(max)
			select @qrynomcomponent = COALESCE(@qrynomcomponent + char(10), '') + '- ' + nocomponent.theMember.value('@fullname','varchar(75)') + ' (' + nocomponent.theMember.value('@membernumber','varchar(50)') + ')'
			from @subImportResult.nodes('/subimport/nocomponent/member') as nocomponent(theMember)

			declare @qryinvalidcomponent varchar(max)
			select @qryinvalidcomponent = COALESCE(@qryinvalidcomponent + char(10), '') + '- ' + invalidcomponent.theMember.value('@fullname','varchar(75)') + ' (' + invalidcomponent.theMember.value('@membernumber','varchar(50)') + ')'
			from @subImportResult.nodes('/subimport/invalidcomponent/member') as invalidcomponent(theMember)

			declare @qryexpiredcomponent varchar(max)
			select @qryexpiredcomponent = COALESCE(@qryexpiredcomponent + char(10), '') + '- ' + expiredcomponent.theMember.value('@fullname','varchar(75)') + ' (' + expiredcomponent.theMember.value('@membernumber','varchar(50)') + ')'
			from @subImportResult.nodes('/subimport/expiredcomponent/member') as expiredcomponent(theMember)

			set @finalMSG = ''
			
			if @qrynomcomponent is not null BEGIN
				select @finalMSG = @finalMSG + 'The following records contain blank Components' + case 
					when @subImportResult.value('count(/subimport/nocomponent/member)','int') > 300 then ' (only 300 are shown)'
					else '' end + ':' + char(10) + @qrynomcomponent + char(10) + char(10)
			END

			if @qryinvalidcomponent is not null BEGIN
				select @finalMSG = @finalMSG + 'The following records contain invalid Components' + case 
					when @subImportResult.value('count(/subimport/invalidcomponent/member)','int') > 300 then ' (only 300 are shown)'
					else '' end + ':' + char(10) + @qryinvalidcomponent + char(10) + char(10)
			END
			
			if @qryexpiredcomponent is not null BEGIN
				select @finalMSG = @finalMSG + 'The following are records in which existing Components have been expired due to blank Components or changed Components in the import file:' + case 
					when @subImportResult.value('count(/subimport/expiredcomponent/member)','int') > 300 then ' (only 300 are shown)'
					else '' end + ':' + char(10) + @qryexpiredcomponent + char(10) + char(10)
			END

			IF len(@finalMSG) = 0 BEGIN
				set @finalMSG = 'The Components have successfully been added. No invalid Components found.'
			END

			set @emailtouse = NULL

			select 
				@emailtouse = me.email
			from 
				membercentral.dbo.ams_members as m
				inner join membercentral.dbo.ams_members as m2 on 
					m2.memberid = m.activeMemberID
				left outer join membercentral.dbo.ams_memberEmails as me 
					inner join membercentral.dbo.ams_memberEmailTypes as met on 
						met.emailTypeID = me.emailTypeID 
						and met.emailTypeOrder = 1
					on me.memberID = m2.memberID
			where
				 m.memberID = @recordedByMemberID

			IF len(@emailtouse) > 0 BEGIN
				EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to=@emailtouse,
					@cc=null, @bcc='<EMAIL>', @subject='TAGD Component Import Results',
					@message=@finalMSG, @priority='normal', @smtpserver=@smtpserver, @authUsername=null, @authPassword=null
			END

			set @importResult = null
			EXEC membercentral.dbo.sub_importSubscriptions_toTemp @siteID=@siteID, @tmptbl='##tagd_import_subscriptions', @pathToExport=@pathToExport, @importResult=@importResult OUTPUT
		
			-- if no errors, run subscription import
			IF @importResult.value('count(/import/errors/error)','int') = 0
			BEGIN
				SELECT @flatfile = @importResult.value('(/import/@flatfile)[1]','varchar(160)')
				set @importResult = null
				EXEC membercentral.dbo.sub_importSubscriptions_toPerm @siteID=@siteID, @recordedByMemberID=@recordedByMemberID, @flatfile=@flatfile, @importResult=@importResult OUTPUT
			END
		END
	END
END

-- cleanup
on_cleanup:
	IF OBJECT_ID('tempdb..##tagd_nationals_alpha') IS NOT NULL
		DROP TABLE ##tagd_nationals_alpha
	IF OBJECT_ID('tempdb..##tagd_nationals_new') IS NOT NULL
		DROP TABLE ##tagd_nationals_new
	IF OBJECT_ID('tempdb..##tagd_nationals_dues') IS NOT NULL
		DROP TABLE ##tagd_nationals_dues
	IF OBJECT_ID('tempdb..##tagd_nationals_Members') IS NOT NULL
		DROP TABLE ##tagd_nationals_Members
	IF OBJECT_ID('tempdb..##tagd_nationals_acct') IS NOT NULL
		DROP TABLE ##tagd_nationals_acct
	IF OBJECT_ID('tempdb..#tagd_memberType') IS NOT NULL
		DROP TABLE #tagd_memberType
	IF OBJECT_ID('tempdb..#temp_tagd_nationals_alpha') IS NOT NULL
		DROP TABLE #temp_tagd_nationals_alpha
	IF OBJECT_ID('tempdb..##tagd_import_subscriptions') IS NOT NULL
		DROP TABLE ##tagd_import_subscriptions
	IF OBJECT_ID('tempdb..#tempTreeCodeTbl') IS NOT NULL
		DROP TABLE #tempTreeCodeTbl
	IF OBJECT_ID('tempdb..#tagd_sub_renewals') IS NOT NULL
		DROP TABLE #tagd_sub_renewals
	IF OBJECT_ID('tempdb..#tagd_sub_newmembers') IS NOT NULL
		DROP TABLE #tagd_sub_newmembers
	IF OBJECT_ID('tempdb..#tagd_sub_switchedComponent') IS NOT NULL
		DROP TABLE #tagd_sub_switchedComponent
	IF OBJECT_ID('tempdb..#tagd_sub_newComponent') IS NOT NULL
		DROP TABLE #tagd_sub_newComponent
	IF OBJECT_ID('tempdb..#tagd_sub_expireComponent') IS NOT NULL
		DROP TABLE #tagd_sub_expireComponent
GO


use membercentral
GO

ALTER PROC [dbo].[sub_addSubscriber]
@orgID int,
@memberid int,
@subscriptionID int,
@parentSubscriberID int,
@RFID INT,
@GLAccountID INT,
@status char(1),
@subStartDate datetime,
@subEndDate datetime,
@graceEndDate datetime,
@recogStartDate datetime = NULL,
@recogEndDate datetime = NULL,
@pcfree bit,
@activationOptionCode varchar(1),
@recordedByMemberID int,
@bypassQueue bit,
@subscriberID int OUTPUT

AS

IF @recogStartDate is null
	set @recogStartDate = @subStartDate
IF @recogEndDate is null
	set @recogEndDate = @subEndDate

-- set subEndDate, graceEndDate, recogEndDate to 23:59:59.997
select @subEndDate = dateadd(ms,-3,dateadd(day,1,DATEADD(dd, DATEDIFF(dd,0,@subEndDate),0)))
select @recogEndDate = dateadd(ms,-3,dateadd(day,1,DATEADD(dd, DATEDIFF(dd,0,@recogEndDate),0)))
IF @graceEndDate is not null
	select @graceEndDate = dateadd(ms,-3,dateadd(day,1,DATEADD(dd, DATEDIFF(dd,0,@graceEndDate),0)))

declare @statusID int, @paymentStatusID int, @activationOptionID int, @rootSubscriberID int, @subscriberPath varchar(100), @rootSubscriptionID int

select @statusID=statusID
	from dbo.sub_statuses
	where statusCode = @status

select @activationOptionID=subActivationID
	from dbo.sub_activationOptions
	where subActivationCode = @activationOptionCode

select @paymentStatusID=statusID
	from dbo.sub_paymentStatuses
	where statusCode = 'N'

insert into dbo.sub_subscribers (memberID, subscriptionID, RFID, GLAccountID, statusID, 
	subStartDate, subEndDate, graceEndDate, dateRecorded, recordedByMemberID, parentSubscriberID, 
	PCFree, subActivationID, paymentStatusID, recogStartDate, recogEndDate) 
values (@memberid, @subscriptionID, nullIf(@RFID,0), nullIf(@GLAccountID,0), @statusID, 
	@subStartDate, @subEndDate, @graceEndDate, getdate(), @recordedByMemberID, @parentSubscriberID, 
	@pcfree, @activationOptionID, @paymentStatusID, @recogStartDate, @recogEndDate)
IF @@ERROR <> 0 goto on_error
select @subscriberID = SCOPE_IDENTITY()

if @parentSubscriberID is NULL begin
	select @rootSubscriberID = @subscriberID
	select @subscriberPath = CAST(RIGHT('100001',4) as varchar(max))
end else begin

	select @rootSubscriptionID = rootss.subscriptionID, @rootSubscriberID = rootss.subscriberID
	from sub_subscribers ss
	inner join sub_subscribers parentss
		on parentss.subscriberID = ss.parentSubscriberID
		and ss.subscriberID = @subscriberID
	inner join sub_subscribers rootss
		on parentss.rootSubscriberID = rootss.subscriberID

	select @subscriberPath = sto.subscriptionPath
	from dbo.fn_sub_getSubscriptionTreeOrder(@rootSubscriptionID) as sto
	where subscriptionID = @subscriptionID
end

update dbo.sub_subscribers
set rootSubscriberID = @rootSubscriberID,
	subscriberPath = @subscriberPath
where subscriberID = @subscriberID

/* Add to group */
declare @waitingPayGroupID int, @expiredGroupID int, @renewableGroupID int, @pendingGroupID int
select @waitingPayGroupID=groupID
	from dbo.ams_groups
	where groupCode = 'subWaitingPay_' + convert(varchar, @subscriptionID) + '_tracking'
select @expiredGroupID=groupID
	from dbo.ams_groups
	where groupCode = 'SubInactive_' + convert(varchar, @subscriptionID) + '_tracking'
select @renewableGroupID=groupID
	from dbo.ams_groups
	where groupCode = 'SubRenew_' + convert(varchar, @subscriptionID) + '_tracking'
select @pendingGroupID=groupID
	from dbo.ams_groups
	where groupCode = 'SubPending_' + convert(varchar, @subscriptionID) + '_tracking'
IF @status = 'A' and @waitingPayGroupID is not null
	exec ams_createMemberGroup @memberID=@memberid, @groupID=@waitingPayGroupID, @bypassQueue=@bypassQueue
IF @status IN ('R','O') and @renewableGroupID is not null
	exec ams_createMemberGroup @memberID=@memberid, @groupID=@renewableGroupID, @bypassQueue=@bypassQueue
IF @status = 'E' and @expiredGroupID is not null
	exec ams_createMemberGroup @memberID=@memberid, @groupID=@expiredGroupID, @bypassQueue=@bypassQueue
IF @status = 'P' and @pendingGroupID is not null
	exec ams_createMemberGroup @memberID=@memberid, @groupID=@pendingGroupID, @bypassQueue=@bypassQueue

insert into dbo.sub_statusHistory(subscriberID, oldStatusID, statusID, enteredByMemberID)
values(@subscriberID, NULL, @statusID, nullif(@recordedByMemberID,0))
 
RETURN 0

on_error:
	select @subscriberID = 0
	RETURN -1

GO

use platformQueue
GO
ALTER PROC [dbo].[job_firmSubStatements_grabForProcessing]
@serverID int,
@batchSize int,
@filePath varchar(400)

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @subTypeOrdering TABLE (itemUID uniqueidentifier, typeID int, typeSortOrder int)

	declare @statusReady int, @statusGrabbed int
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'FirmSubStatements'
		and qs.queueStatus = 'readyToProcess'
	select @statusGrabbed = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'FirmSubStatements'
		and qs.queueStatus = 'grabbedForProcessing'

	declare @jobUID uniqueidentifier
	set @jobUID = NEWID()

	-- dequeue in order of dateAdded. get @batchsize firms
	IF OBJECT_ID('tempdb..#tmpTblQueueItems_firmbilling') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_firmbilling
	CREATE TABLE #tmpTblQueueItems_firmbilling (itemUID uniqueidentifier, itemGroupUID uniqueidentifier, 
		jobUID uniqueidentifier, recordedByMemberID int, siteID int, memberID int, memberNumber varchar(50), 
		firstname varchar(75), lastname varchar(75), company varchar(200), address1 varchar(100), 
		address2 varchar(100), address3 varchar(100), city varchar(35), stateProv varchar(4),
		postalCode varchar(25), country varchar(100), xmlFieldSets varchar(max), 
		xmlConfigParam varchar(max), xmlFirms varchar(max))

	update qi WITH (UPDLOCK, READPAST)
	set qi.queueStatusID = @statusGrabbed,
		qi.dateUpdated = getdate(),
		qi.jobUID = @jobUID,
		qi.jobDateStarted = getdate(),
		qi.jobServerID = @serverID
		OUTPUT inserted.itemUID, null, inserted.jobUID, null, null, null, null, null, null, null, 
			null, null, null, null, null, null, null, null, null, null
		INTO #tmpTblQueueItems_firmbilling
	from platformQueue.dbo.tblQueueItems as qi
	inner join (
		select top(@BatchSize) qi2.itemUID 
		from platformQueue.dbo.tblQueueItems as qi2
		where qi2.queueStatusID = @statusReady
		order by qi2.dateAdded, qi2.itemUID
		) as batch on batch.itemUID = qi.itemUID
	where qi.queueStatusID = @statusReady

	IF @TranCounter = 0
		COMMIT TRAN;

END TRY
BEGIN CATCH
	IF OBJECT_ID('tempdb..#tmpTblQueueItems_firmbilling') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_firmbilling

	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH


BEGIN TRY
	-- get memberID, itemGroupUID, siteID, recordedByMemberID from item data
	-- outer apply is required in cases where there is no match so the itemGroupUID gets updated
	update tmp
	set tmp.memberID = idata.memberID,
		tmp.itemGroupUID = idata.itemGroupUID,
		tmp.recordedByMemberID = idata.recordedByMemberID,
		tmp.siteID = idata.siteID
	from #tmpTblQueueItems_firmbilling as tmp
	outer apply (
		select min(cast(dataKey as int)) as memberID, min(cast(itemGroupUID as varchar(60))) as itemGroupUID, 
			min(recordedByMemberID) as recordedByMemberID, min(siteID) as siteID
		from platformQueue.dbo.tblQueueItemData as qid
		inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
			and dc.columnname in ('FirmChildSub','FirmChildNoSub')
		where qid.itemUID = tmp.itemUID
	) as idata

	-- get address info for firm accounts
	update tmp 
	set	tmp.memberNumber = m.memberNumber, 
		tmp.firstname = m.firstname, 
		tmp.lastname = m.lastname, 
		tmp.company = m.company, 
		tmp.address1 = ma.address1, 
		tmp.address2 = ma.address2, 
		tmp.address3 = ma.address3, 
		tmp.city = ma.city, 
		tmp.stateProv = s.code, 
		tmp.postalCode = ma.postalCode,
		tmp.country = c.country
	from #tmpTblQueueItems_firmbilling as tmp
	inner join membercentral.dbo.ams_members as m on m.memberID = tmp.memberID
	left outer join membercentral.dbo.ams_memberAddresses as ma on ma.memberID = m.memberID 
		and ma.addressTypeID = m.billingAddressTypeID
	left outer join membercentral.dbo.ams_states as s on s.stateID = ma.stateID
	left outer join membercentral.dbo.ams_countries as c on c.countryID = ma.countryID

	-- get config params for each item. casting to varchar(max) because it speed up final data query return
	update tmp
	set tmp.xmlConfigParam = config.configXML
	from #tmpTblQueueItems_firmbilling as tmp
	cross apply (
		select cast(isnull((
			select reportParam, paramvalue	
			from (
				select datakey as reportParam, cast(columnValueString as varchar(max)) as paramvalue
				from platformQueue.dbo.tblQueueItemData qid
				inner join platformQueue.dbo.tblQueueTypeDataColumns dc on dc.columnID = qid.columnID
					and dc.columnname = 'ConfigParam'
				where qid.itemUID = tmp.itemUID
					union all
				select datakey as reportParam, columnValuetext as paramvalue
				from platformQueue.dbo.tblQueueItemData qid
				inner join platformQueue.dbo.tblQueueTypeDataColumns dc on dc.columnID = qid.columnID
					and dc.columnname = 'ConfigText'
				where qid.itemUID = tmp.itemUID
			) as tmp
			for xml path('param'), root('params'), type
		),'<params/>') as varchar(max)) as configXML
	) as config

	-- get fieldsets for each item. casting to varchar(max) because it speed up final data query return
	update tmp
	set tmp.xmlFieldSets = fieldset.fieldsetXML
	from #tmpTblQueueItems_firmbilling as tmp
	cross apply (
		select cast(isnull((
			select reportParam, paramvalue	
			from (
				select datakey as reportParam, cast(columnValueString as varchar(max)) as paramvalue
				from platformQueue.dbo.tblQueueItemData qid
				inner join platformQueue.dbo.tblQueueTypeDataColumns dc on dc.columnID = qid.columnID
					and dc.columnname = 'fieldset'
				where qid.itemUID = tmp.itemUID
			) as tmp
			for xml path('fieldset'), root('fieldsets'), type
		),'<fieldsets/>') as varchar(max)) as fieldsetXML
	) as fieldset

	-- get all firm members
	IF OBJECT_ID('tempdb..#qryAllFirmMembers') IS NOT NULL 
		DROP TABLE #qryAllFirmMembers
	CREATE TABLE #qryAllFirmMembers (itemUID uniqueidentifier, childMemberID int, childMemberNumber varchar(50), 
		prefix varchar(50), firstname varchar(75), lastname varchar(75), company varchar(200), suffix varchar(50),
		address1 varchar(100), address2 varchar(100), address3 varchar(100), city varchar(35), stateProv varchar(4),
				postalCode varchar(25), country varchar(100)
	)
	insert into #qryAllFirmMembers (itemUID, childMemberID)
	select distinct tmp.itemUID, qid.columnValueInteger
	from #tmpTblQueueItems_firmbilling as tmp
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = tmp.itemUID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
		and dc.columnname = 'FirmChildNoSub'
		union
	select distinct tmp.itemUID, m.activeMemberID
	from #tmpTblQueueItems_firmbilling as tmp
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = tmp.itemUID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
		and dc.columnname = 'FirmChildSub'
	inner join membercentral.dbo.sub_subscribers as ss on ss.subscriberID = qid.columnValueInteger
	inner join membercentral.dbo.ams_members as m on m.memberID = ss.memberID

	update fm 
	set childMemberNumber = m.MemberNumber, 
		prefix = m.prefix,
		firstname = m.firstname, 
		lastname = m.lastname, 
		company = m.company,
		suffix = m.suffix,
		address1 = ma.address1, 
		address2 = ma.address2, 
		address3 = ma.address3, 
		city = ma.city, 
		stateProv = s.code, 
		postalCode = ma.postalCode,
		country = c.country
	from #qryAllFirmMembers as fm
	inner join membercentral.dbo.ams_members as m on m.memberID = fm.childMemberID
	left outer join membercentral.dbo.ams_memberAddresses as ma on ma.memberID = m.memberID 
		and ma.addressTypeID = m.billingAddressTypeID
	left outer join membercentral.dbo.ams_states as s on s.stateID = ma.stateID
	left outer join membercentral.dbo.ams_countries as c on c.countryID = ma.countryID

	-- get all subs
	IF OBJECT_ID('tempdb..#qrySubs') IS NOT NULL 
		DROP TABLE #qrySubs
	CREATE TABLE #qrySubs (itemUID uniqueidentifier, subscriberID int, memberID int, subscriptionID int, typeID int,
		typeName varchar(100), subscriptionName varchar(300), rateName varchar(200), frequencyName varchar(50),
		[status] varchar(1), statusName varchar(50), subStartDate datetime, subEndDate datetime, graceEndDate datetime,
		parentSubscriberID int, rootSubscriberID int, lastPrice money, rfid int, thePath varchar(max), 
		subAmount money, subAmountDue money, subAmountPaid money, subscriberPath varchar(200), saleTransactionID int, 
		glaccountID int, invoiceContentVersionID int, invoiceProfileID int, invoiceProfileImageExt varchar(5))

	insert into #qrySubs (itemUID, subscriberID, memberID, subscriptionID, typeID, typeName, subscriptionName, rateName, 
		frequencyName, [status], statusName, subStartDate, subEndDate, graceEndDate, parentSubscriberID, rfid, 
		rootSubscriberID, lastPrice, subscriberPath, glaccountID)
	select tmp.itemUID, ss.subscriberID, m.activeMemberID, ss.subscriptionID, t.typeID, t.typeName, sub.subscriptionName, 
		r.rateName, f.frequencyName, st.statusCode, st.statusName, ss.subStartDate, ss.subEndDate, ss.graceEndDate, 
		ss.parentSubscriberID, ss.rfid, ss.rootSubscriberID, ss.lastprice, ss.subscriberPath, ss.glaccountID
	from #tmpTblQueueItems_firmbilling as tmp
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = tmp.itemUID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
		and dc.columnname = 'FirmChildSub'
	inner join membercentral.dbo.sub_subscribers as rootss on rootss.subscriberID = qid.columnValueInteger
	inner join membercentral.dbo.sub_subscribers as ss on rootss.subscriberID = ss.rootSubscriberID
	inner join membercentral.dbo.sub_subscriptions as sub on sub.subscriptionID = ss.subscriptionID
	inner join membercentral.dbo.sub_types as t on sub.typeiD = t.typeID
	inner join membercentral.dbo.ams_members as m on ss.memberID = m.memberID
	inner join membercentral.dbo.sub_statuses as st on st.statusID = ss.statusID 
		and st.statuscode in ('A','I','O','P')
	inner join membercentral.dbo.sub_rateFrequencies as rf on rf.rfid = ss.rfid
	inner join membercentral.dbo.sub_rates as r on r.rateID = rf.rateID 
	inner join membercentral.dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID

	update qs 
	set saleTransactionID = t.transactionID,
		subAmount = tsFull.cache_amountAfterAdjustment,
		subAmountDue = tsFull.cache_amountAfterAdjustment - tsFull.cache_activePaymentAllocatedAmount,
		subAmountPaid = tsFull.cache_activePaymentAllocatedAmount,
		invoiceContentVersionID = it.messageContentVersionID
	from #qrySubs as qs
	inner join membercentral.dbo.tr_applications as app on app.ItemID = qs.subscriberID 
		and app.applicationTypeID = 17 
		and app.itemType = 'Dues' 
		and app.status = 'A'
	inner join membercentral.dbo.tr_transactions as t on t.transactionID = app.transactionID
	cross apply membercentral.dbo.fn_tr_transactionSalesWithDIT(t.transactionID) as tsFull
	inner join membercentral.dbo.tr_invoiceTransactions it on it.transactionID = t.transactionID

	-- update amount and amountdue columns for billed subscriptions
	update qs 
	set subAmount = qs.lastprice, subAmountDue = qs.lastprice, subAmountPaid = 0
	from #qrySubs as qs
	where status = 'O'

	-- set typeSortOrder based on billed total by subscription type
	insert into @subTypeOrdering (itemUID, typeID , typeSortOrder )
	select tmp.itemUID, tmp.typeID, row_number() over (partition by tmp.itemUID order by tmp.subAmountTotal desc, typename desc)
	from (
		select qs.itemUID, qs.typeID, qs.typeName, sum(subAmount) as subAmountTotal
		from #qrySubs as qs
		group by qs.itemUID, qs.typeID, qs.typeName
	) as tmp

	-- update invoiceProfileID and invoice image (billed subs have no transactions to join against)
	update qs 
	set invoiceProfileID = ip.profileID, 
		invoiceProfileImageExt = ip.imageExt
	from #qrySubs as qs
	inner join membercentral.dbo.tr_glaccounts as gl on gl.glaccountID = qs.glaccountID
	inner join membercentral.dbo.tr_invoiceProfiles ip on ip.profileID = gl.invoiceProfileID

	-- update invoiceContentVersionID for subs with no transactions
	update qs 
	set invoiceContentVersionID = cv.contentVersionID
	from #qrySubs as qs
	inner join membercentral.dbo.sub_types t on t.typeID = qs.typeID
	inner join membercentral.dbo.sites s on s.siteID = t.siteID
	inner join membercentral.dbo.tr_glaccounts as gl on gl.glaccountID = qs.glaccountID
		and qs.saleTransactionID is null
	inner join membercentral.dbo.cms_content as c on c.contentID = gl.invoiceContentID
	inner join membercentral.dbo.cms_contentLanguages as cl on cl.contentID = c.contentID
		and cl.languageID = s.defaultLanguageID
	inner join membercentral.dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID
		and cv.isActive = 1

	-- get sub tree totals
	IF OBJECT_ID('tempdb..#tblSubTreeTotals') IS NOT NULL 
		DROP TABLE #tblSubTreeTotals
	CREATE TABLE #tblSubTreeTotals (itemUID uniqueidentifier, rootsubscriberID int, tree_subAmount money, 
		tree_subAmountDue money, tree_subAmountPaid money)

	insert into #tblSubTreeTotals (itemUID, rootsubscriberID, tree_subAmount, tree_subAmountDue, tree_subAmountPaid)
	select itemUID, rootSubscriberID, sum(subAmount) as tree_subAmount, sum(subAmountDue) as tree_subAmountDue, sum(subAmountPaid) as tree_subAmountPaid
	from #qrySubs 
	group by itemUID, rootSubscriberID

	-- get sub payments
	IF OBJECT_ID('tempdb..#tblSubPayments') IS NOT NULL 
		DROP TABLE #tblSubPayments
	CREATE TABLE #tblSubPayments (itemUID uniqueidentifier, subscriberID int, rootsubscriberID int, transactionID int, 
		allocatedAmount money, detail varchar(max), depositdate datetime)

	insert into #tblSubPayments (itemUID, subscriberID, rootsubscriberID, transactionID, allocatedAmount, detail, depositdate)
	select s.itemUID, s.subscriberID, s.rootsubscriberID, t.transactionID, st.allocatedAmount, t.detail, b.depositDate
	from #qrySubs as s
	inner join membercentral.dbo.tr_applications as app on app.ItemID = s.subscriberID 
		and app.applicationTypeID = 17 
		and app.itemType = 'Dues' 
		and app.status = 'A'
	CROSS APPLY membercentral.dbo.fn_tr_getAllocatedPaymentsofSale(app.transactionID) AS st
	inner join membercentral.dbo.tr_transactions as t on t.transactionID = st.paymentTransactionID
	inner join membercentral.dbo.tr_batchTransactions as bt on bt.transactionID = t.transactionID
	inner join membercentral.dbo.tr_batches as b on b.batchID = bt.batchID

	-- get sub invoices
	IF OBJECT_ID('tempdb..#tblSubInvoices') IS NOT NULL 
		DROP TABLE #tblSubInvoices
	CREATE TABLE #tblSubInvoices (itemUID uniqueidentifier, subscriberID int, rootsubscriberID int, invoiceID int,
		invoiceCode char(8), datebilled datetime, datedue datetime, statusID int, [status] varchar(50), amount money,
		amountDue money, payProfileDesc varchar(100), invoiceNumber varchar(20))

	insert into #tblSubInvoices (itemUID, subscriberID, rootsubscriberID, invoiceID, invoiceCode, datebilled, datedue,
		statusID, [status], amount, amountDue,payProfileDesc, invoiceNumber)
	select s.itemUID, s.subscriberID, s.rootsubscriberID, i.invoiceID, i.invoiceCode, i.datebilled, i.datedue, ist.statusID, 
		ist.status, sum(st.amount) as amount, sum(st.amountDue) as amountDue,
		payProfileDesc = g.gatewayclass + ' - (' + mpp.detail + ')',
		invoiceNumber = o.orgcode + right(replicate('0',8) + cast(i.invoiceID as varchar(10)),8)
	from #qrySubs as s
	CROSS APPLY membercentral.dbo.fn_sub_subscriberTransactions(s.subscriberID) AS st 
	inner join membercentral.dbo.tr_invoices as i on i.invoiceID = st.invoiceID
	inner join membercentral.dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
	inner join membercentral.dbo.organizations as o on o.orgID = ip.orgID
	inner join membercentral.dbo.tr_invoiceStatuses as ist on ist.statusID = i.statusID and i.statusID <> 1
	left outer join membercentral.dbo.ams_memberPaymentProfiles as mpp
		inner join membercentral.dbo.mp_profiles as mp on mp.profileID = mpp.profileID
		inner join membercentral.dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
		on mpp.payProfileID = i.payProfileID
	group by s.itemUID, s.subscriberID, s.rootsubscriberID, i.invoiceID, i.invoiceCode, i.datebilled, i.datedue, 
		ist.statusID, ist.status, g.gatewayclass, mpp.detail, o.orgcode

	-- populate table var of contentVersions referenced by qrySubs
	declare @invoiceContent table (contentVersionID int PRIMARY KEY, rawcontent varchar(max))
	declare @invoiceContentandSubtrees table (contentVersionID int, rootSubscriberID int, messageNumber int)

	insert into @invoiceContent (contentVersionID, rawcontent)
	select distinct cv.contentVersionID, cv.rawcontent
	from #qrySubs as qs
	inner join membercentral.dbo.cms_contentVersions as cv on cv.contentVersionID = qs.invoiceContentVersionID

	insert into @invoiceContentandSubtrees (contentVersionID, rootSubscriberID, messageNumber)
	select ic.contentVersionID, qs.rootsubscriberID, row_number() over (partition by qs.rootsubscriberID order by min(qs.subscriberPath))
	from #qrySubs as qs
	inner join @invoiceContent as ic on ic.contentVersionID = qs.invoiceContentVersionID
	group by ic.contentVersionID, qs.rootsubscriberID

	-- firms xml. casting to varchar(max) because it speed up final data query return
	update tmp
	set tmp.xmlFirms = firms.firmXML
	from #tmpTblQueueItems_firmbilling as tmp
	cross apply (
		select cast(isnull((		
			select company.memberID as '@firmmemberid',
				company.memberNumber as '@firmmembernumber' ,
				company.firstname as '@firmfirstname' ,
				company.lastname as '@firmlastname' ,
				company.company as '@firmcompany' ,
				company.address1 as '@firmaddress1' ,
				company.address2 as '@firmaddress2' ,
				company.address3 as '@firmaddress3' ,
				company.city as '@firmcity' ,
				company.stateProv as '@firmstate' ,
				company.postalCode as '@firmpostalcode',
				company.country as '@firmcountry',
				( select
					account.childmemberid as '@childmemberID',
					account.childMemberNumber as '@childmembernumber',
					isnull(company.memberID,0) as '@firmmemberid',
					case when exists (select * from #qrySubs as st2 where st2.memberID = account.childmemberid and st2.itemUID = tmp.itemUID) then 1 else 0 end as '@hassubscription',
					account.firstname as '@firstname', 
					account.lastname as '@lastname',
					account.prefix as '@prefix',
					account.suffix as '@suffix',
					account.lastname + ', ' + account.firstname + ' (' + account.childMemberNumber + ')' as '@namestring',
					account.company as '@company',
					account.address1 as '@address1' ,
					account.address2 as '@address2' ,
					account.address3 as '@address3' ,
					account.city as '@city' ,
					account.stateProv as '@state' ,
					account.postalCode as '@postalcode',
					account.country as '@country',

					( select
						subscriptionTree.rootsubscriberid as '@rootsubscriberid',
						case when subscriptionTree.rootsubscriberid is null then null else account.childmemberid end as '@activeMemberID',
						case when subscriptionTree.rootsubscriberid is null then null else account.lastname + ', ' + account.firstname + ' (' + account.childMemberNumber + ')' end as '@namestring',
						subscriptionTree.subscriptionname as '@subscriptionname',
						qst.tree_subAmount as '@subamount_total',
						qst.tree_subAmountPaid as '@subamountpaid_total',
						qst.tree_subAmountDue as '@subamountdue_total',
						CONVERT(VARCHAR(8),subscriptionTree.substartdate, 1) + ' - ' + CONVERT(VARCHAR(8),subscriptionTree.subenddate, 1) as '@subtreedaterange',
						cast(subscriptionTree.invoiceProfileID as varchar(10)) + '.' + subscriptionTree.invoiceProfileImageExt as '@subtreeinvoiceprofileimage',
						( select
							subscriber.subscriberid as '@subscriberID',
							account.childmemberid as '@activeMemberID',
							subscriber.subscriptionid as '@subscriptionid',
							subscriber.typeid as '@typeid',
							subscriber.typename as '@typename',
							subscriber.subscriptionname as '@subscriptionname',
							subscriber.ratename as '@ratename',
							subscriber.frequencyname as '@frequencyname',
							subscriber.status as '@status',
							subscriber.statusname as '@statusname',
							subscriber.substartdate as '@substartdate',
							subscriber.subenddate as '@subenddate',
							CONVERT(VARCHAR(8),subscriber.substartdate, 1) + ' - ' + CONVERT(VARCHAR(8),subscriber.subenddate, 1) as '@subdaterange',
							subscriber.graceenddate as '@graceenddate',
							subscriber.parentsubscriberid as '@parentsubscriberid',
							subscriber.rootsubscriberid as '@rootsubscriberid',
							subscriber.rfid as '@rfid',
							subscriber.thepath as '@thepath',
							subscriber.lastprice as '@lastprice',
							subscriber.subAmount as '@subamount',
							subscriber.subAmountPaid as '@subamountpaid',
							subscriber.subAmountDue as '@subamountdue',
							subscriber.subscriberPath as '@subscriberpath',
							subscriber.invoiceContentVersionID as '@invoicecontentversionid',
							icst.messageNumber as '@invoicecontentfootnotenumber',
							sto.typeSortOrder as '@coversheettypesortorder'
						from #qrySubs as subscriber
						inner join @subTypeOrdering sto
							on sto.itemUID = subscriber.itemUID
							and sto.typeID = subscriber.typeID
						left outer join @invoiceContentandSubtrees as icst on icst.rootsubscriberID = subscriber.rootsubscriberID
							and subscriber.invoiceContentVersionID = icst.contentVersionID
						where subscriber.itemUID = tmp.itemUID
						and subscriber.rootSubscriberID = subscriptionTree.subscriberID
						order by subscriber.subscriberPath
						for xml path('subscriber'), type
						),
						( select
							rootsubscriberID as '@rootsubscriberID',
							invoiceID as '@invoiceid',
							invoiceNumber as '@invoicenumber',
							invoiceCode as '@invoicecode',
							datebilled as '@datebilled',
							datedue as '@datedue',
							statusID as '@statusid',
							[status] as '@status',
							sum(amount) as '@amount',
							sum(amountDue) as '@amountDue',
							isnull(payProfileDesc,'') as '@payprofiledesc'
						from #tblSubInvoices as si
						where si.itemUID = tmp.itemUID
						and si.rootSubscriberID = subscriptionTree.rootsubscriberID
						group by rootsubscriberID, invoiceID, invoiceCode, datebilled, datedue, statusID, [status], payProfileDesc, invoiceNumber
						order by datedue
						for xml path ('invoice'),root('invoices'), type
						),
						( select
							rootsubscriberID as '@rootsubscriberID',
							transactionID as '@transactionID',
							depositdate as '@datebilled',
							detail as '@detail',
							sum(allocatedAmount) as '@allocatedAmount'
						from #tblSubPayments sp
						where sp.itemUID = tmp.itemUID
						and sp.rootSubscriberID = subscriptionTree.rootsubscriberID
						group by rootsubscriberID ,transactionID ,detail,depositdate
						order by depositdate
						for xml path('payment'), root('payments'), type
						),
						( select
							subscriber.rootSubscriberID as '@rootsubscriberID',
							icst.messageNumber as '@invoicecontentfootnotenumber',
							ic.contentversionid as '@contentversionid',
							ic.rawcontent as '@rawcontent'
						from #qrySubs as subscriber
						inner join @invoiceContent as ic on ic.contentVersionID = subscriber.invoiceContentVersionID
							and subscriber.itemUID = tmp.itemUID
							and subscriber.rootSubscriberID = subscriptionTree.subscriberID
						inner join @invoiceContentandSubtrees as icst on icst.rootsubscriberID = subscriber.rootsubscriberID
							and icst.contentVersionID = ic.contentVersionID
						group by subscriber.rootSubscriberID, icst.messageNumber, ic.contentversionid, ic.rawcontent
						order by icst.messageNumber
						for xml path('invoicemessage'), root('invoicemessages'), type
						)
						from #qrySubs as subscriptionTree
						inner join #tblSubTreeTotals as qst 
							on qst.rootSubscriberID = subscriptionTree.rootSubscriberID
							and qst.itemUID = subscriptionTree.itemUID
						where subscriptionTree.itemUID = tmp.itemUID
						and subscriptionTree.memberID = account.childMemberID
						and subscriptionTree.subscriberID = subscriptionTree.rootsubscriberID
						order by subscriptionTree.rootsubscriberid
						for xml path('subscriptiontree'), type
					)
					from #qryAllFirmMembers as account
					where account.itemUID = company.itemUID
					order by account.lastname, account.firstname, account.childMemberID
					for xml path('account'), type
				)
			from #tmpTblQueueItems_firmbilling as company
			where company.itemUID = tmp.itemUID
			order by company.company, company.memberID
			for xml path('company'), root('companies'), type
		),'<companies/>') as varchar(max)) as firmXML
	) as firms

	-- create directories to store xmls
	IF OBJECT_ID('tempdb..#tblDirs') IS NOT NULL 
		DROP TABLE #tblDirs
	select distinct itemGroupUID, membercentral.dbo.fn_createDirectory(@filePath + cast(itemGroupUID as varchar(50))) as cdResult
	into #tblDirs
	from #tmpTblQueueItems_firmbilling
	where membercentral.dbo.fn_DirectoryExists(@filePath + cast(itemGroupUID as varchar(50))) = 0

	-- final data
	select itemUID, itemGroupUID, jobUID, siteID, memberNumber, company, xmlFieldSets, xmlConfigParam, 
		membercentral.dbo.fn_writefile(@filePath + cast(itemGroupUID as varchar(50)) + '\' + cast(itemUID as varchar(50)) + '.xml',xmlFirms,1) as writeFileResult
	from #tmpTblQueueItems_firmbilling
	order by itemUID

	IF OBJECT_ID('tempdb..#tblDirs') IS NOT NULL 
		DROP TABLE #tblDirs
	IF OBJECT_ID('tempdb..#tblSubInvoices') IS NOT NULL 
		DROP TABLE #tblSubInvoices
	IF OBJECT_ID('tempdb..#tblSubPayments') IS NOT NULL 
		DROP TABLE #tblSubPayments
	IF OBJECT_ID('tempdb..#tblSubTreeTotals') IS NOT NULL 
		DROP TABLE #tblSubTreeTotals
	IF OBJECT_ID('tempdb..#qrySubs') IS NOT NULL 
		DROP TABLE #qrySubs
	IF OBJECT_ID('tempdb..#qryAllFirmMembers') IS NOT NULL 
		DROP TABLE #qryAllFirmMembers
	IF OBJECT_ID('tempdb..#tmpTblQueueItems_firmbilling') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_firmbilling

	RETURN 0
END TRY
BEGIN CATCH
	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH
GO


use membercentral
GO

ALTER PROC [dbo].[sub_removeSubscriber]
@subscriberID int,
@memberID int,
@siteID int,
@enteredByMemberID int,
@statsSessionID int,
@AROption char(1)

AS

set nocount on

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @ApplicationTypeID int, @nowdate datetime, @minSubID int, @updateResult int, @invoiceID int,
		@invoiceNumber varchar(18), @minTID int, @adjAmount money, @adjtransactionID int,
		@autoCloseInvoice bit, @invoiceProfileID int
	DECLARE @tblAdjust TABLE (transactionID int, amountToAdjust money)
	set @autoCloseInvoice = 0

	select @ApplicationTypeID = dbo.fn_getApplicationTypeIDFromName('Admin')
	select @nowdate = getdate()

	declare @tblSubs TABLE (subscriberID int, thePathExpanded varchar(max))
	insert into @tblSubs (subscriberID, thePathExpanded)
	select subscriberID, thePathExpanded
	from dbo.fn_getRecursiveMemberSubscriptions(@memberID,@siteID,@subscriberID)
	where status <> 'D'

	select @minSubID = min(subscriberID) from @tblSubs
	while @minSubID is not null BEGIN
		EXEC dbo.sub_updateSubscriberStatus @subscriberID=@minSubID, @newStatusCode='D', @siteID=@siteID, 
			@enteredByMemberID=@enteredByMemberID, @result=@updateResult OUTPUT
			IF @updateResult < 0 RAISERROR('sub_updateSubscriberStatus returned %i', 16, 1,@updateResult);

		IF @AROption = 'A'
			INSERT INTO @tblAdjust (transactionID, amountToAdjust)
			select rt.mainTransactionID, tsFull.cache_amountAfterAdjustment
			from dbo.fn_sub_subscriberTransactions(@minSubID) as rt
			cross apply dbo.fn_tr_transactionSalesWithDIT(rt.transactionID) as tsFull
			where rt.typeID = 1
			and tsFull.cache_amountAfterAdjustment > 0
		IF @AROption = 'B'
			INSERT INTO @tblAdjust (transactionID, amountToAdjust)
			select rt.mainTransactionID, tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount-tsFull.cache_pendingPaymentAllocatedAmount
			from dbo.fn_sub_subscriberTransactions(@minSubID) as rt
			cross apply dbo.fn_tr_transactionSalesWithDIT(rt.transactionID) as tsFull
			where rt.typeID = 1
			and tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount-tsFull.cache_pendingPaymentAllocatedAmount > 0

		UPDATE dbo.tr_applications
		SET [status] = 'D'
		WHERE itemID = @minSubID
		AND itemType = 'Dues'
		AND applicationTypeID = @ApplicationTypeID
		AND [status] <> 'D'

		select @minSubID = min(subscriberID) from @tblSubs where subscriberID > @minSubID
	END

	-- if there are adjustments to make
	IF EXISTS (select transactionID from @tblAdjust) BEGIN

		-- if any transactions are on a open or pending invoice, grab it for all adjustments. 
		-- otherwise, we need to create one to hold these adjustments
		select top 1 @invoiceID = i.invoiceID 
			from dbo.tr_invoices as i
			inner join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
			inner join @tblAdjust as tbl on tbl.transactionID = it.transactionID
			and statusID in (1,2)
			order by i.invoiceID
		IF @invoiceID is null BEGIN
			select top 1 @invoiceProfileID=i.invoiceProfileID
				from dbo.tr_invoices as i
				inner join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
				inner join @tblAdjust as tbl on tbl.transactionID = it.transactionID
			EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@enteredByMemberID,
				@assignedToMemberID=@memberID, @dateBilled=@nowdate, @dateDue=@nowdate, 
				@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT
			set @autoCloseInvoice = 1
		END

		-- record adjustments
		SELECT @minTID = min(transactionID) from @tblAdjust
		WHILE @minTID IS NOT NULL BEGIN
			SELECT @adjAmount = amountToAdjust*-1 from @tblAdjust where transactionID = @minTID
			
			EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteID,
				@recordedByMemberID=@enteredByMemberID, @statsSessionID=@statsSessionID,
				@status='Active', @amount=@adjAmount, @transactionDate=@nowdate,
				@saleTransactionID=@minTID, @invoiceID=@invoiceID, @xmlSchedule=null,
				@transactionID=@adjtransactionID OUTPUT
			
			SELECT @minTID = min(transactionID) from @tblAdjust where transactionID > @minTID
		END

		IF @autoCloseInvoice = 1
			EXEC dbo.tr_closeInvoice @enteredByMemberID=@enteredByMemberID, @invoiceIDList=@invoiceID			
	END

	-- return query of subs
	select * from @tblSubs


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[sub_expireSubscriber]
@subscriberID int,
@memberID int,
@siteID int,
@enteredByMemberID int,
@statsSessionID int,
@AROption char(1),
@fReturnQuery bit=1

AS

set nocount on

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @ApplicationTypeID int, @nowdate datetime, @minSubID int, @updateResult int, @invoiceID int,
		@invoiceNumber varchar(18), @minTID int, @adjAmount money, @adjtransactionID int,
		@invoiceProfileID int
	DECLARE @tblAdjust TABLE (transactionID int, amountToAdjust money)
	select @ApplicationTypeID = dbo.fn_getApplicationTypeIDFromName('Admin')
	select @nowdate = getdate()

	declare @tblSubs TABLE (subscriberID int, thePathExpanded varchar(max))
	insert into @tblSubs (subscriberID, thePathExpanded)
	select subscriberID, thePathExpanded
	from dbo.fn_getRecursiveMemberSubscriptions(@memberID,@siteID,@subscriberID)
	where status not in ('D','E')

	select @minSubID = min(subscriberID) from @tblSubs
	while @minSubID is not null BEGIN
		EXEC dbo.sub_updateSubscriberStatus @subscriberID=@minSubID, @newStatusCode='E', @siteID=@siteID, 
			@enteredByMemberID=@enteredByMemberID, @result=@updateResult OUTPUT
			IF @updateResult < 0 RAISERROR('sub_updateSubscriberStatus returned %i', 16, 1,@updateResult);

		IF @AROption = 'A'
			INSERT INTO @tblAdjust (transactionID, amountToAdjust)
			select rt.mainTransactionID, tsFull.cache_amountAfterAdjustment
			from dbo.fn_sub_subscriberTransactions(@minSubID) as rt
			cross apply dbo.fn_tr_transactionSalesWithDIT(rt.transactionID) as tsFull
			where rt.typeID = 1
			and tsFull.cache_amountAfterAdjustment > 0
		IF @AROption = 'B'
			INSERT INTO @tblAdjust (transactionID, amountToAdjust)
			select rt.mainTransactionID, tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount-tsFull.cache_pendingPaymentAllocatedAmount
			from dbo.fn_sub_subscriberTransactions(@minSubID) as rt
			cross apply dbo.fn_tr_transactionSalesWithDIT(rt.transactionID) as tsFull
			where rt.typeID = 1
			and tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount-tsFull.cache_pendingPaymentAllocatedAmount > 0

		UPDATE dbo.tr_applications
		SET [status] = 'D'
		WHERE itemID = @minSubID
		AND itemType = 'Dues'
		AND applicationTypeID = @ApplicationTypeID
		AND [status] <> 'D'

		select @minSubID = min(subscriberID) from @tblSubs where subscriberID > @minSubID
	END

	-- if there are adjustments to make
	IF EXISTS (select transactionID from @tblAdjust) BEGIN

		-- if any transactions are on a open or pending invoice, grab it for all adjustments. 
		-- otherwise, we need to create one to hold these adjustments
		select top 1 @invoiceID = i.invoiceID 
			from dbo.tr_invoices as i
			inner join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
			inner join @tblAdjust as tbl on tbl.transactionID = it.transactionID
			and statusID in (1,2)
			order by i.invoiceID
		IF @invoiceID is null BEGIN
			select top 1 @invoiceProfileID=i.invoiceProfileID
				from dbo.tr_invoices as i
				inner join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
				inner join @tblAdjust as tbl on tbl.transactionID = it.transactionID
			EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@enteredByMemberID,
				@assignedToMemberID=@memberID, @dateBilled=@nowdate, @dateDue=@nowdate, 
				@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT
		END

		-- record adjustments
		SELECT @minTID = min(transactionID) from @tblAdjust
		WHILE @minTID IS NOT NULL BEGIN
			SELECT @adjAmount = amountToAdjust*-1 from @tblAdjust where transactionID = @minTID
			
			EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteID,
				@recordedByMemberID=@enteredByMemberID, @statsSessionID=@statsSessionID,
				@status='Active', @amount=@adjAmount, @transactionDate=@nowdate,
				@saleTransactionID=@minTID, @invoiceID=@invoiceID, @xmlSchedule=null,
				@transactionID=@adjtransactionID OUTPUT
			
			SELECT @minTID = min(transactionID) from @tblAdjust where transactionID > @minTID
		END

		EXEC dbo.tr_closeInvoice @enteredByMemberID=@enteredByMemberID, @invoiceIDList=@invoiceID			

	END

	IF @fReturnQuery = 1
		select * from @tblSubs


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROCEDURE [dbo].[sub_checkActivations] 
	@memberid int,
	@bypassQueue bit = 0
WITH RECOMPILE
AS

set nocount on

IF OBJECT_ID('tempdb..#firstInvoice') IS NOT NULL 
	DROP TABLE #firstInvoice
create table #firstInvoice (subscriberID int PRIMARY KEY, orgID int, invoiceNumber int)

declare @tblSubsToCheck TABLE (subscriberID int, rootSubscriberID int, memberID int, orgID int, siteID int, subActivationCode char(1), statusCode char(1))
declare @membersToCheck TABLE (id int IDENTITY(1,1), memberID int PRIMARY KEY)
declare @tblSubsToMove TABLE (subscriberID int, statusCode char(1), reason varchar(50))
declare @activatedStatusID int, @nonActivatedStatusID int, @enteredByMemberID int

select @activatedStatusID = statusID from dbo.sub_paymentStatuses where statusCode = 'P'
select @nonActivatedStatusID = statusID from dbo.sub_paymentStatuses where statusCode = 'N'
select @enteredByMemberID = memberID from ams_members where memberNumber = 'SYSTEM' and orgID = 1	

IF @memberID is null
BEGIN
	insert into @tblSubsToCheck (subscriberID, rootSubscriberID, memberID, orgID, siteID, subActivationCode, statusCode)
	select sub.subscriberID, sub.rootSubscriberID, m.activememberID as memberID, m.orgID, t.siteid, ao.subActivationCode, s.statusCode
	from sub_subscribers as sub
	inner join dbo.sub_statuses as s on s.statusID = sub.statusID
	inner join dbo.sub_activationOptions as ao on ao.subActivationID = sub.subActivationID
	inner join dbo.ams_members as m on m.memberid = sub.memberID
	inner join dbo.sub_subscriptions as ss on ss.subscriptionID = sub.subscriptionID
	inner join dbo.sub_Types as t on t.typeID = ss.typeID
	where sub.paymentStatusID = @nonActivatedStatusID
	and s.statusCode in ('A','P','I','E')
	and ao.subActivationCode in ('C','P','I','E','N','T') 
	order by sub.subscriberID
END
ELSE BEGIN
	insert into @membersToCheck (memberID)
	select m2.memberID
	from ams_members m
	inner join ams_members m2 on m.activeMemberID = m2.activeMemberID
		and m.memberID = @memberID

	insert into @tblSubsToCheck (subscriberID, rootSubscriberID, memberID, orgID, siteID, subActivationCode, statusCode)
	select sub.subscriberID, sub.rootSubscriberID, m.activememberID as memberID, m.orgID, t.siteid, ao.subActivationCode, s.statusCode
	from @membersToCheck mtc
	inner join sub_subscribers as sub on mtc.memberID = sub.memberID
	inner join dbo.sub_statuses as s on s.statusID = sub.statusID
	inner join dbo.sub_activationOptions as ao on ao.subActivationID = sub.subActivationID
	inner join dbo.ams_members as m on m.memberid = sub.memberID
	inner join dbo.sub_subscriptions as ss on ss.subscriptionID = sub.subscriptionID
	inner join dbo.sub_Types as t on t.typeID = ss.typeID
	where sub.paymentStatusID = @nonActivatedStatusID
	and s.statusCode in ('A','P','I','E')
	and ao.subActivationCode in ('C','P','I','E','N','T') 
	order by sub.subscriberID

END


-- N: No Reliance on payment
insert into @tblSubsToMove (subscriberID, statusCode, reason)
select tbl.subscriberID, tbl.statusCode, 'No reliance'
from @tblSubsToCheck as tbl
where tbl.subActivationCode = 'N'

-- P: This sub paid in full
insert into @tblSubsToMove (subscriberID, statusCode, reason)
select tbl.subscriberID, tbl.statusCode, 'this paid in full'
from @tblSubsToCheck as tbl
inner join dbo.tr_applications as tra on tra.itemID = tbl.subscriberID
	and tra.applicationTypeID = 17
	and tra.itemType = 'Dues'
	and tra.status = 'A'
cross apply dbo.fn_tr_transactionSalesWithDIT(tra.transactionID) as tsFull
where tbl.subActivationCode = 'P'
and tsFull.cache_amountAfterAdjustment = tsFull.cache_activePaymentAllocatedAmount

-- C: This sub and all children subs paid in full
insert into @tblSubsToMove (subscriberID, statusCode, reason)
select tbl.subscriberID, tbl.statusCode, 'this and children paid in full'
from @tblSubsToCheck as tbl
cross apply dbo.fn_getRecursiveMemberSubscriptions(tbl.memberid, tbl.siteid, tbl.subscriberID) as rms
inner join dbo.tr_applications as tra on tra.itemID = rms.subscriberID
	and tra.applicationTypeID = 17
	and tra.itemType = 'Dues'
	and tra.status = 'A'
cross apply dbo.fn_tr_transactionSalesWithDIT(tra.transactionID) as tsFull
where tbl.subActivationCode = 'C'
group by tbl.subscriberID, tbl.statusCode
having sum(tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount) = 0

-- I: First invoice this sub appears on is paid
insert into @tblSubsToMove (subscriberID, statusCode, reason)
select tmp.subscriberID, tmp.statusCode, 'First invoice this sub'
from (
	select tbl.subscriberID, tbl.statusCode, i.assignedToMemberID, tbl.orgID, min(i.invoiceNumber) as firstInvNumber
	from @tblSubsToCheck as tbl
	inner join dbo.tr_applications as tra on tra.itemID = tbl.subscriberID
		and tra.applicationTypeID = 17
		and tra.itemType = 'Dues'
		and tra.status = 'A'
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = tra.transactionID
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	where tbl.subActivationCode = 'I'
	group by tbl.subscriberID, tbl.statusCode, i.assignedToMemberID, tbl.orgID
) as tmp 
inner join dbo.tr_invoices as i2 on i2.invoiceNumber = tmp.firstInvNumber and i2.statusID = 4
inner join ams_members m on m.memberID = i2.assignedToMemberID
	and m.orgID = tmp.orgID

-- E: First invoice this entire sub appears on is paid
insert into #firstInvoice (subscriberID, orgID, invoiceNumber)
select s.subscriberID, tstc.orgID, min(i.invoiceNumber) as invoiceNumber
from @tblSubsToCheck tstc
inner join dbo.sub_subscribers s on s.rootSubscriberID = tstc.rootSubscriberID
	and tstc.subActivationCode = 'E'
inner join dbo.tr_applications as tra on tra.itemID = s.subscriberID
	and tra.applicationTypeID = 17
	and tra.itemType = 'Dues'
	and tra.status = 'A'
inner join dbo.tr_invoiceTransactions as it on it.transactionID = tra.transactionID
inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
group by s.rootSubscriberID, s.subscriberID, tstc.orgID

CREATE INDEX IX_invoiceNumber on #firstInvoice (invoiceNumber asc)

insert into @tblSubsToMove (subscriberID, statusCode, reason)
select distinct tmp2.subscriberID, ts.statusCode, 'First invoice this entire sub'
from #firstInvoice as tmp2
inner join dbo.tr_invoices as i2 on i2.invoiceNumber = tmp2.invoiceNumber
	and i2.statusID = 4
inner join @tblSubsToCheck ts on ts.subscriberID = tmp2.subscriberID and ts.subActivationCode = 'E'
inner join ams_members m on m.memberID = i2.assignedToMemberID
	and m.orgID = tmp2.orgID

-- T: First Payment on This Sub
insert into @tblSubsToMove (subscriberID, statusCode, reason)
select tbl.subscriberID, tbl.statusCode, 'First payment this sub'
from @tblSubsToCheck as tbl
inner join dbo.tr_applications as tra on tra.itemID = tbl.subscriberID
	and tra.applicationTypeID = 17
	and tra.itemType = 'Dues'
	and tra.status = 'A'
cross apply dbo.fn_tr_transactionSalesWithDIT(tra.transactionID) as tsFull
where tbl.subActivationCode = 'T'
and ( tsFull.cache_activePaymentAllocatedAmount > 0 or tsFull.cache_amountAfterAdjustment = 0)


-- get the follow parents
insert into @tblSubsToMove (subscriberID, statusCode, reason)
select s.subscriberID, st.statusCode, 'follow parents'
from sub_subscribers s
inner join dbo.sub_statuses st
	on st.statusID = s.statusID and st.statusCode <> 'D'
	and s.paymentStatusID = @nonActivatedStatusID
inner join sub_activationOptions a on a.subActivationID = s.subActivationID and a.subActivationCode = 'F'
left outer join sub_subscribers pS on ps.subscriberID = s.parentSubscriberID and ps.paymentStatusID = @activatedStatusID
where NOT EXISTS (select subscriberID from @tblSubsToMove where subscriberID = s.subscriberID)
		AND (EXISTS (select subscriberID from @tblSubsToMove where subscriberID = s.parentSubscriberID) OR pS.subscriberID is not null)

while @@RowCount > 0
begin

	insert into @tblSubsToMove (subscriberID, statusCode, reason)
	select s.subscriberID, st.statusCode, 'follow parents'
	from sub_subscribers s
	inner join dbo.sub_statuses st
		on st.statusID = s.statusID and st.statusCode <> 'D'
		and s.paymentStatusID = @nonActivatedStatusID
	inner join sub_activationOptions a on a.subActivationID = s.subActivationID and a.subActivationCode = 'F'
	left outer join sub_subscribers pS on ps.subscriberID = s.parentSubscriberID and ps.paymentStatusID = @activatedStatusID
	where NOT EXISTS (select subscriberID from @tblSubsToMove where subscriberID = s.subscriberID)
		AND (EXISTS (select subscriberID from @tblSubsToMove where subscriberID = s.parentSubscriberID) OR pS.subscriberID is not null)
end


update subs
set subs.paymentStatusID = @activatedStatusID
from dbo.sub_subscribers subs
inner join @tblSubsToMove ts on ts.subscriberID = subs.subscriberID

insert into dbo.sub_paymentStatusHistory(subscriberID, paymentStatusID, enteredByMemberID)
select subscriberID, @activatedStatusID, @enteredByMemberID
from @tblSubsToMove

declare @minSubscriberID int, @currLoopMemberID int, @currLoopSubscriptionID int, @currLoopStatusCode char(1), @currLoopGroupID int, @tempCount int
declare @prevSubscriberID int, @currLoopSiteID int, @insideResult int

select @minSubscriberID=min(subscriberID)
from @tblSubsToMove
where statusCode in ('A','P')


while @minSubscriberID is not null
begin

	select @currLoopSubscriptionID=s.subscriptionID, @currLoopMemberID=m.activeMemberID, @currLoopStatusCode=t.statusCode, @currLoopSiteID=st.siteID
	from dbo.sub_subscribers s
	inner join @tblSubsToMove t on t.subscriberID = s.subscriberID
	inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID
	inner join dbo.sub_types st on st.typeID = subs.typeID
	inner join dbo.ams_members m on m.memberID = s.memberID
	where s.subscriberID = @minSubScriberID
	

	select @tempCount=count(s.subscriberID)
	from dbo.sub_subscribers s
	inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID AND pst.statusCode = 'N'
	inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @currLoopMemberID
	where s.subscriptionID = @currLoopSubscriptionID
	
	IF @tempCount = 0
	begin
		select @currLoopGroupID=null
		select @currLoopGroupID=groupID
		from ams_groups
		where groupCode like 'subWaitingPay_' + convert(varchar, @currLoopSubscriptionID) + '_tracking'

		IF @currLoopGroupID is not null BEGIN
			exec dbo.ams_deleteMemberGroup @currLoopMemberID, @currLoopGroupID
		END
	end

	IF @currLoopStatusCode = 'A'
	begin

		select @prevSubscriberID=null
		select @prevSubscriberID=s.subscriberID
		from dbo.sub_subscribers s
		inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode in ('A', 'I')
		inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID 
		inner join dbo.sub_types t on t.typeID = subs.typeID and t.siteID = @currLoopSiteID
		inner join dbo.ams_members m on m.memberID = s.memberID and m.activeMemberID = @currLoopMemberID											
		where s.subscriptionID = @currLoopSubscriptionID
		and s.subscriberID <> @minSubscriberID

		IF @prevSubscriberID is not null
		BEGIN

			EXEC sub_updateSubscriberStatus @prevSubscriberID, 'E', @currLoopSiteID, @enteredByMemberID, @insideResult OUTPUT

		END

		select @currLoopGroupID=null
		select @currLoopGroupID=groupID
		from ams_groups
		where groupCode like 'subActive_' + convert(varchar, @currLoopSubscriptionID) + '_tracking'

		IF @currLoopGroupID is not null
			exec dbo.ams_createMemberGroup @currLoopMemberID, @currLoopGroupID, @bypassQueue
	end
	ELSE IF @currLoopStatusCode = 'P'
	begin
		select @currLoopGroupID=null
		select @currLoopGroupID=groupID
		from ams_groups
		where groupCode like 'subPending_' + convert(varchar, @currLoopSubscriptionID) + '_tracking'

		IF @currLoopGroupID is not null
			exec dbo.ams_createMemberGroup @currLoopMemberID, @currLoopGroupID, @bypassQueue
	end

	select @minSubscriberID=min(subscriberID)
	from @tblSubsToMove
	where statusCode in ('A','P') and subscriberID > @minSubscriberID
end

IF OBJECT_ID('tempdb..#firstInvoice') IS NOT NULL 
	DROP TABLE #firstInvoice

set nocount off

GO

use platformQueue;
GO
ALTER PROC [dbo].[job_firmSubStatements_grabForProcessing]
@serverID int,
@batchSize int,
@filePath varchar(400)

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @subTypeOrdering TABLE (itemUID uniqueidentifier, typeID int, typeSortOrder int)

	declare @statusReady int, @statusGrabbed int
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'FirmSubStatements'
		and qs.queueStatus = 'readyToProcess'
	select @statusGrabbed = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'FirmSubStatements'
		and qs.queueStatus = 'grabbedForProcessing'

	declare @jobUID uniqueidentifier
	set @jobUID = NEWID()

	-- dequeue in order of dateAdded. get @batchsize firms
	IF OBJECT_ID('tempdb..#tmpTblQueueItems_firmbilling') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_firmbilling
	CREATE TABLE #tmpTblQueueItems_firmbilling (itemUID uniqueidentifier, itemGroupUID uniqueidentifier, 
		jobUID uniqueidentifier, recordedByMemberID int, siteID int, memberID int, memberNumber varchar(50), 
		firstname varchar(75), lastname varchar(75), company varchar(200), address1 varchar(100), 
		address2 varchar(100), address3 varchar(100), city varchar(35), stateProv varchar(4),
		postalCode varchar(25), country varchar(100), xmlFieldSets varchar(max), 
		xmlConfigParam varchar(max), xmlFirms varchar(max))

	update qi WITH (UPDLOCK, READPAST)
	set qi.queueStatusID = @statusGrabbed,
		qi.dateUpdated = getdate(),
		qi.jobUID = @jobUID,
		qi.jobDateStarted = getdate(),
		qi.jobServerID = @serverID
		OUTPUT inserted.itemUID, null, inserted.jobUID, null, null, null, null, null, null, null, 
			null, null, null, null, null, null, null, null, null, null
		INTO #tmpTblQueueItems_firmbilling
	from platformQueue.dbo.tblQueueItems as qi
	inner join (
		select top(@BatchSize) qi2.itemUID 
		from platformQueue.dbo.tblQueueItems as qi2
		where qi2.queueStatusID = @statusReady
		order by qi2.dateAdded, qi2.itemUID
		) as batch on batch.itemUID = qi.itemUID
	where qi.queueStatusID = @statusReady

	IF @TranCounter = 0
		COMMIT TRAN;

END TRY
BEGIN CATCH
	IF OBJECT_ID('tempdb..#tmpTblQueueItems_firmbilling') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_firmbilling

	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH


BEGIN TRY
	-- get memberID, itemGroupUID, siteID, recordedByMemberID from item data
	-- outer apply is required in cases where there is no match so the itemGroupUID gets updated
	update tmp
	set tmp.memberID = idata.memberID,
		tmp.itemGroupUID = idata.itemGroupUID,
		tmp.recordedByMemberID = idata.recordedByMemberID,
		tmp.siteID = idata.siteID
	from #tmpTblQueueItems_firmbilling as tmp
	outer apply (
		select min(cast(dataKey as int)) as memberID, min(cast(itemGroupUID as varchar(60))) as itemGroupUID, 
			min(recordedByMemberID) as recordedByMemberID, min(siteID) as siteID
		from platformQueue.dbo.tblQueueItemData as qid
		inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
			and dc.columnname in ('FirmChildSub','FirmChildNoSub')
		where qid.itemUID = tmp.itemUID
	) as idata

	-- get address info for firm accounts
	update tmp 
	set	tmp.memberNumber = m.memberNumber, 
		tmp.firstname = m.firstname, 
		tmp.lastname = m.lastname, 
		tmp.company = m.company, 
		tmp.address1 = ma.address1, 
		tmp.address2 = ma.address2, 
		tmp.address3 = ma.address3, 
		tmp.city = ma.city, 
		tmp.stateProv = s.code, 
		tmp.postalCode = ma.postalCode,
		tmp.country = c.country
	from #tmpTblQueueItems_firmbilling as tmp
	inner join membercentral.dbo.ams_members as m on m.memberID = tmp.memberID
	left outer join membercentral.dbo.ams_memberAddresses as ma on ma.memberID = m.memberID 
		and ma.addressTypeID = m.billingAddressTypeID
	left outer join membercentral.dbo.ams_states as s on s.stateID = ma.stateID
	left outer join membercentral.dbo.ams_countries as c on c.countryID = ma.countryID

	-- get config params for each item. casting to varchar(max) because it speed up final data query return
	update tmp
	set tmp.xmlConfigParam = config.configXML
	from #tmpTblQueueItems_firmbilling as tmp
	cross apply (
		select cast(isnull((
			select reportParam, paramvalue	
			from (
				select datakey as reportParam, cast(isnull(columnValueString,'') as varchar(max)) as paramvalue
				from platformQueue.dbo.tblQueueItemData qid
				inner join platformQueue.dbo.tblQueueTypeDataColumns dc on dc.columnID = qid.columnID
					and dc.columnname = 'ConfigParam'
				where qid.itemUID = tmp.itemUID
					union all
				select datakey as reportParam, isnull(columnValuetext,'') as paramvalue
				from platformQueue.dbo.tblQueueItemData qid
				inner join platformQueue.dbo.tblQueueTypeDataColumns dc on dc.columnID = qid.columnID
					and dc.columnname = 'ConfigText'
				where qid.itemUID = tmp.itemUID
			) as tmp
			for xml path('param'), root('params'), type
		),'<params/>') as varchar(max)) as configXML
	) as config

	-- get fieldsets for each item. casting to varchar(max) because it speed up final data query return
	update tmp
	set tmp.xmlFieldSets = fieldset.fieldsetXML
	from #tmpTblQueueItems_firmbilling as tmp
	cross apply (
		select cast(isnull((
			select reportParam, paramvalue	
			from (
				select datakey as reportParam, cast(columnValueString as varchar(max)) as paramvalue
				from platformQueue.dbo.tblQueueItemData qid
				inner join platformQueue.dbo.tblQueueTypeDataColumns dc on dc.columnID = qid.columnID
					and dc.columnname = 'fieldset'
				where qid.itemUID = tmp.itemUID
			) as tmp
			for xml path('fieldset'), root('fieldsets'), type
		),'<fieldsets/>') as varchar(max)) as fieldsetXML
	) as fieldset

	-- get all firm members
	IF OBJECT_ID('tempdb..#qryAllFirmMembers') IS NOT NULL 
		DROP TABLE #qryAllFirmMembers
	CREATE TABLE #qryAllFirmMembers (itemUID uniqueidentifier, childMemberID int, childMemberNumber varchar(50), 
		prefix varchar(50), firstname varchar(75), lastname varchar(75), company varchar(200), suffix varchar(50),
		address1 varchar(100), address2 varchar(100), address3 varchar(100), city varchar(35), stateProv varchar(4),
				postalCode varchar(25), country varchar(100)
	)
	insert into #qryAllFirmMembers (itemUID, childMemberID)
	select distinct tmp.itemUID, qid.columnValueInteger
	from #tmpTblQueueItems_firmbilling as tmp
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = tmp.itemUID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
		and dc.columnname = 'FirmChildNoSub'
		union
	select distinct tmp.itemUID, m.activeMemberID
	from #tmpTblQueueItems_firmbilling as tmp
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = tmp.itemUID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
		and dc.columnname = 'FirmChildSub'
	inner join membercentral.dbo.sub_subscribers as ss on ss.subscriberID = qid.columnValueInteger
	inner join membercentral.dbo.ams_members as m on m.memberID = ss.memberID

	update fm 
	set childMemberNumber = m.MemberNumber, 
		prefix = m.prefix,
		firstname = m.firstname, 
		lastname = m.lastname, 
		company = m.company,
		suffix = m.suffix,
		address1 = ma.address1, 
		address2 = ma.address2, 
		address3 = ma.address3, 
		city = ma.city, 
		stateProv = s.code, 
		postalCode = ma.postalCode,
		country = c.country
	from #qryAllFirmMembers as fm
	inner join membercentral.dbo.ams_members as m on m.memberID = fm.childMemberID
	left outer join membercentral.dbo.ams_memberAddresses as ma on ma.memberID = m.memberID 
		and ma.addressTypeID = m.billingAddressTypeID
	left outer join membercentral.dbo.ams_states as s on s.stateID = ma.stateID
	left outer join membercentral.dbo.ams_countries as c on c.countryID = ma.countryID

	-- get all subs
	IF OBJECT_ID('tempdb..#qrySubs') IS NOT NULL 
		DROP TABLE #qrySubs
	CREATE TABLE #qrySubs (itemUID uniqueidentifier, subscriberID int, memberID int, subscriptionID int, typeID int,
		typeName varchar(100), subscriptionName varchar(300), rateName varchar(200), frequencyName varchar(50),
		[status] varchar(1), statusName varchar(50), subStartDate datetime, subEndDate datetime, graceEndDate datetime,
		parentSubscriberID int, rootSubscriberID int, lastPrice money, rfid int, thePath varchar(max), 
		subAmount money, subAmountDue money, subAmountPaid money, subscriberPath varchar(200), saleTransactionID int, 
		glaccountID int, invoiceContentVersionID int, invoiceProfileID int, invoiceProfileImageExt varchar(5))

	insert into #qrySubs (itemUID, subscriberID, memberID, subscriptionID, typeID, typeName, subscriptionName, rateName, 
		frequencyName, [status], statusName, subStartDate, subEndDate, graceEndDate, parentSubscriberID, rfid, 
		rootSubscriberID, lastPrice, subscriberPath, glaccountID)
	select tmp.itemUID, ss.subscriberID, m.activeMemberID, ss.subscriptionID, t.typeID, t.typeName, sub.subscriptionName, 
		r.rateName, f.frequencyName, st.statusCode, st.statusName, ss.subStartDate, ss.subEndDate, ss.graceEndDate, 
		ss.parentSubscriberID, ss.rfid, ss.rootSubscriberID, ss.lastprice, ss.subscriberPath, ss.glaccountID
	from #tmpTblQueueItems_firmbilling as tmp
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = tmp.itemUID
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
		and dc.columnname = 'FirmChildSub'
	inner join membercentral.dbo.sub_subscribers as rootss on rootss.subscriberID = qid.columnValueInteger
	inner join membercentral.dbo.sub_subscribers as ss on rootss.subscriberID = ss.rootSubscriberID
	inner join membercentral.dbo.sub_subscriptions as sub on sub.subscriptionID = ss.subscriptionID
	inner join membercentral.dbo.sub_types as t on sub.typeiD = t.typeID
	inner join membercentral.dbo.ams_members as m on ss.memberID = m.memberID
	inner join membercentral.dbo.sub_statuses as st on st.statusID = ss.statusID 
		and st.statuscode in ('A','I','O','P')
	inner join membercentral.dbo.sub_rateFrequencies as rf on rf.rfid = ss.rfid
	inner join membercentral.dbo.sub_rates as r on r.rateID = rf.rateID 
	inner join membercentral.dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID

	update qs 
	set saleTransactionID = t.transactionID,
		subAmount = tsFull.cache_amountAfterAdjustment,
		subAmountDue = tsFull.cache_amountAfterAdjustment - tsFull.cache_activePaymentAllocatedAmount,
		subAmountPaid = tsFull.cache_activePaymentAllocatedAmount,
		invoiceContentVersionID = it.messageContentVersionID
	from #qrySubs as qs
	inner join membercentral.dbo.tr_applications as app on app.ItemID = qs.subscriberID 
		and app.applicationTypeID = 17 
		and app.itemType = 'Dues' 
		and app.status = 'A'
	inner join membercentral.dbo.tr_transactions as t on t.transactionID = app.transactionID
	cross apply membercentral.dbo.fn_tr_transactionSalesWithDIT(t.transactionID) as tsFull
	inner join membercentral.dbo.tr_invoiceTransactions it on it.transactionID = t.transactionID

	-- update amount and amountdue columns for billed subscriptions
	update qs 
	set subAmount = qs.lastprice, subAmountDue = qs.lastprice, subAmountPaid = 0
	from #qrySubs as qs
	where status = 'O'

	-- set typeSortOrder based on billed total by subscription type
	insert into @subTypeOrdering (itemUID, typeID , typeSortOrder )
	select tmp.itemUID, tmp.typeID, row_number() over (partition by tmp.itemUID order by tmp.subAmountTotal desc, typename desc)
	from (
		select qs.itemUID, qs.typeID, qs.typeName, sum(subAmount) as subAmountTotal
		from #qrySubs as qs
		group by qs.itemUID, qs.typeID, qs.typeName
	) as tmp

	-- update invoiceProfileID and invoice image (billed subs have no transactions to join against)
	update qs 
	set invoiceProfileID = ip.profileID, 
		invoiceProfileImageExt = ip.imageExt
	from #qrySubs as qs
	inner join membercentral.dbo.tr_glaccounts as gl on gl.glaccountID = qs.glaccountID
	inner join membercentral.dbo.tr_invoiceProfiles ip on ip.profileID = gl.invoiceProfileID

	-- update invoiceContentVersionID for subs with no transactions
	update qs 
	set invoiceContentVersionID = cv.contentVersionID
	from #qrySubs as qs
	inner join membercentral.dbo.sub_types t on t.typeID = qs.typeID
	inner join membercentral.dbo.sites s on s.siteID = t.siteID
	inner join membercentral.dbo.tr_glaccounts as gl on gl.glaccountID = qs.glaccountID
		and qs.saleTransactionID is null
	inner join membercentral.dbo.cms_content as c on c.contentID = gl.invoiceContentID
	inner join membercentral.dbo.cms_contentLanguages as cl on cl.contentID = c.contentID
		and cl.languageID = s.defaultLanguageID
	inner join membercentral.dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID
		and cv.isActive = 1

	-- get sub tree totals
	IF OBJECT_ID('tempdb..#tblSubTreeTotals') IS NOT NULL 
		DROP TABLE #tblSubTreeTotals
	CREATE TABLE #tblSubTreeTotals (itemUID uniqueidentifier, rootsubscriberID int, tree_subAmount money, 
		tree_subAmountDue money, tree_subAmountPaid money)

	insert into #tblSubTreeTotals (itemUID, rootsubscriberID, tree_subAmount, tree_subAmountDue, tree_subAmountPaid)
	select itemUID, rootSubscriberID, sum(subAmount) as tree_subAmount, sum(subAmountDue) as tree_subAmountDue, sum(subAmountPaid) as tree_subAmountPaid
	from #qrySubs 
	group by itemUID, rootSubscriberID

	-- get sub payments
	IF OBJECT_ID('tempdb..#tblSubPayments') IS NOT NULL 
		DROP TABLE #tblSubPayments
	CREATE TABLE #tblSubPayments (itemUID uniqueidentifier, subscriberID int, rootsubscriberID int, transactionID int, 
		allocatedAmount money, detail varchar(max), depositdate datetime)

	insert into #tblSubPayments (itemUID, subscriberID, rootsubscriberID, transactionID, allocatedAmount, detail, depositdate)
	select s.itemUID, s.subscriberID, s.rootsubscriberID, t.transactionID, st.allocatedAmount, t.detail, b.depositDate
	from #qrySubs as s
	inner join membercentral.dbo.tr_applications as app on app.ItemID = s.subscriberID 
		and app.applicationTypeID = 17 
		and app.itemType = 'Dues' 
		and app.status = 'A'
	CROSS APPLY membercentral.dbo.fn_tr_getAllocatedPaymentsofSale(app.transactionID) AS st
	inner join membercentral.dbo.tr_transactions as t on t.transactionID = st.paymentTransactionID
	inner join membercentral.dbo.tr_batchTransactions as bt on bt.transactionID = t.transactionID
	inner join membercentral.dbo.tr_batches as b on b.batchID = bt.batchID

	-- get sub invoices
	IF OBJECT_ID('tempdb..#tblSubInvoices') IS NOT NULL 
		DROP TABLE #tblSubInvoices
	CREATE TABLE #tblSubInvoices (itemUID uniqueidentifier, subscriberID int, rootsubscriberID int, invoiceID int,
		invoiceCode char(8), datebilled datetime, datedue datetime, statusID int, [status] varchar(50), amount money,
		amountDue money, payProfileDesc varchar(100), invoiceNumber varchar(20))

	insert into #tblSubInvoices (itemUID, subscriberID, rootsubscriberID, invoiceID, invoiceCode, datebilled, datedue,
		statusID, [status], amount, amountDue,payProfileDesc, invoiceNumber)
	select s.itemUID, s.subscriberID, s.rootsubscriberID, i.invoiceID, i.invoiceCode, i.datebilled, i.datedue, ist.statusID, 
		ist.status, sum(st.amount) as amount, sum(st.amountDue) as amountDue,
		payProfileDesc = g.gatewayclass + ' - (' + mpp.detail + ')',
		invoiceNumber = o.orgcode + right(replicate('0',8) + cast(i.invoiceID as varchar(10)),8)
	from #qrySubs as s
	CROSS APPLY membercentral.dbo.fn_sub_subscriberTransactions(s.subscriberID) AS st 
	inner join membercentral.dbo.tr_invoices as i on i.invoiceID = st.invoiceID
	inner join membercentral.dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
	inner join membercentral.dbo.organizations as o on o.orgID = ip.orgID
	inner join membercentral.dbo.tr_invoiceStatuses as ist on ist.statusID = i.statusID and i.statusID <> 1
	left outer join membercentral.dbo.ams_memberPaymentProfiles as mpp
		inner join membercentral.dbo.mp_profiles as mp on mp.profileID = mpp.profileID
		inner join membercentral.dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
		on mpp.payProfileID = i.payProfileID
	group by s.itemUID, s.subscriberID, s.rootsubscriberID, i.invoiceID, i.invoiceCode, i.datebilled, i.datedue, 
		ist.statusID, ist.status, g.gatewayclass, mpp.detail, o.orgcode

	-- populate table var of contentVersions referenced by qrySubs
	declare @invoiceContent table (contentVersionID int PRIMARY KEY, rawcontent varchar(max))
	declare @invoiceContentandSubtrees table (contentVersionID int, rootSubscriberID int, messageNumber int)

	insert into @invoiceContent (contentVersionID, rawcontent)
	select distinct cv.contentVersionID, cv.rawcontent
	from #qrySubs as qs
	inner join membercentral.dbo.cms_contentVersions as cv on cv.contentVersionID = qs.invoiceContentVersionID

	insert into @invoiceContentandSubtrees (contentVersionID, rootSubscriberID, messageNumber)
	select ic.contentVersionID, qs.rootsubscriberID, row_number() over (partition by qs.rootsubscriberID order by min(qs.subscriberPath))
	from #qrySubs as qs
	inner join @invoiceContent as ic on ic.contentVersionID = qs.invoiceContentVersionID
	group by ic.contentVersionID, qs.rootsubscriberID

	-- firms xml. casting to varchar(max) because it speed up final data query return
	update tmp
	set tmp.xmlFirms = firms.firmXML
	from #tmpTblQueueItems_firmbilling as tmp
	cross apply (
		select cast(isnull((		
			select company.memberID as '@firmmemberid',
				company.memberNumber as '@firmmembernumber' ,
				company.firstname as '@firmfirstname' ,
				company.lastname as '@firmlastname' ,
				company.company as '@firmcompany' ,
				company.address1 as '@firmaddress1' ,
				company.address2 as '@firmaddress2' ,
				company.address3 as '@firmaddress3' ,
				company.city as '@firmcity' ,
				company.stateProv as '@firmstate' ,
				company.postalCode as '@firmpostalcode',
				company.country as '@firmcountry',
				( select
					account.childmemberid as '@childmemberID',
					account.childMemberNumber as '@childmembernumber',
					isnull(company.memberID,0) as '@firmmemberid',
					case when exists (select * from #qrySubs as st2 where st2.memberID = account.childmemberid and st2.itemUID = tmp.itemUID) then 1 else 0 end as '@hassubscription',
					account.firstname as '@firstname', 
					account.lastname as '@lastname',
					account.prefix as '@prefix',
					account.suffix as '@suffix',
					account.lastname + ', ' + account.firstname + ' (' + account.childMemberNumber + ')' as '@namestring',
					account.company as '@company',
					account.address1 as '@address1' ,
					account.address2 as '@address2' ,
					account.address3 as '@address3' ,
					account.city as '@city' ,
					account.stateProv as '@state' ,
					account.postalCode as '@postalcode',
					account.country as '@country',

					( select
						subscriptionTree.rootsubscriberid as '@rootsubscriberid',
						case when subscriptionTree.rootsubscriberid is null then null else account.childmemberid end as '@activeMemberID',
						case when subscriptionTree.rootsubscriberid is null then null else account.lastname + ', ' + account.firstname + ' (' + account.childMemberNumber + ')' end as '@namestring',
						subscriptionTree.subscriptionname as '@subscriptionname',
						qst.tree_subAmount as '@subamount_total',
						qst.tree_subAmountPaid as '@subamountpaid_total',
						qst.tree_subAmountDue as '@subamountdue_total',
						CONVERT(VARCHAR(8),subscriptionTree.substartdate, 1) + ' - ' + CONVERT(VARCHAR(8),subscriptionTree.subenddate, 1) as '@subtreedaterange',
						cast(subscriptionTree.invoiceProfileID as varchar(10)) + '.' + subscriptionTree.invoiceProfileImageExt as '@subtreeinvoiceprofileimage',
						( select
							subscriber.subscriberid as '@subscriberID',
							account.childmemberid as '@activeMemberID',
							subscriber.subscriptionid as '@subscriptionid',
							subscriber.typeid as '@typeid',
							subscriber.typename as '@typename',
							subscriber.subscriptionname as '@subscriptionname',
							subscriber.ratename as '@ratename',
							subscriber.frequencyname as '@frequencyname',
							subscriber.status as '@status',
							subscriber.statusname as '@statusname',
							subscriber.substartdate as '@substartdate',
							subscriber.subenddate as '@subenddate',
							CONVERT(VARCHAR(8),subscriber.substartdate, 1) + ' - ' + CONVERT(VARCHAR(8),subscriber.subenddate, 1) as '@subdaterange',
							subscriber.graceenddate as '@graceenddate',
							subscriber.parentsubscriberid as '@parentsubscriberid',
							subscriber.rootsubscriberid as '@rootsubscriberid',
							subscriber.rfid as '@rfid',
							subscriber.thepath as '@thepath',
							subscriber.lastprice as '@lastprice',
							subscriber.subAmount as '@subamount',
							subscriber.subAmountPaid as '@subamountpaid',
							subscriber.subAmountDue as '@subamountdue',
							subscriber.subscriberPath as '@subscriberpath',
							subscriber.invoiceContentVersionID as '@invoicecontentversionid',
							icst.messageNumber as '@invoicecontentfootnotenumber',
							sto.typeSortOrder as '@coversheettypesortorder'
						from #qrySubs as subscriber
						inner join @subTypeOrdering sto
							on sto.itemUID = subscriber.itemUID
							and sto.typeID = subscriber.typeID
						left outer join @invoiceContentandSubtrees as icst on icst.rootsubscriberID = subscriber.rootsubscriberID
							and subscriber.invoiceContentVersionID = icst.contentVersionID
						where subscriber.itemUID = tmp.itemUID
						and subscriber.rootSubscriberID = subscriptionTree.subscriberID
						order by subscriber.subscriberPath
						for xml path('subscriber'), type
						),
						( select
							rootsubscriberID as '@rootsubscriberID',
							invoiceID as '@invoiceid',
							invoiceNumber as '@invoicenumber',
							invoiceCode as '@invoicecode',
							datebilled as '@datebilled',
							datedue as '@datedue',
							statusID as '@statusid',
							[status] as '@status',
							sum(amount) as '@amount',
							sum(amountDue) as '@amountDue',
							isnull(payProfileDesc,'') as '@payprofiledesc'
						from #tblSubInvoices as si
						where si.itemUID = tmp.itemUID
						and si.rootSubscriberID = subscriptionTree.rootsubscriberID
						group by rootsubscriberID, invoiceID, invoiceCode, datebilled, datedue, statusID, [status], payProfileDesc, invoiceNumber
						order by datedue
						for xml path ('invoice'),root('invoices'), type
						),
						( select
							rootsubscriberID as '@rootsubscriberID',
							transactionID as '@transactionID',
							depositdate as '@datebilled',
							detail as '@detail',
							sum(allocatedAmount) as '@allocatedAmount'
						from #tblSubPayments sp
						where sp.itemUID = tmp.itemUID
						and sp.rootSubscriberID = subscriptionTree.rootsubscriberID
						group by rootsubscriberID ,transactionID ,detail,depositdate
						order by depositdate
						for xml path('payment'), root('payments'), type
						),
						( select
							subscriber.rootSubscriberID as '@rootsubscriberID',
							icst.messageNumber as '@invoicecontentfootnotenumber',
							ic.contentversionid as '@contentversionid',
							ic.rawcontent as '@rawcontent'
						from #qrySubs as subscriber
						inner join @invoiceContent as ic on ic.contentVersionID = subscriber.invoiceContentVersionID
							and subscriber.itemUID = tmp.itemUID
							and subscriber.rootSubscriberID = subscriptionTree.subscriberID
						inner join @invoiceContentandSubtrees as icst on icst.rootsubscriberID = subscriber.rootsubscriberID
							and icst.contentVersionID = ic.contentVersionID
						group by subscriber.rootSubscriberID, icst.messageNumber, ic.contentversionid, ic.rawcontent
						order by icst.messageNumber
						for xml path('invoicemessage'), root('invoicemessages'), type
						)
						from #qrySubs as subscriptionTree
						inner join #tblSubTreeTotals as qst 
							on qst.rootSubscriberID = subscriptionTree.rootSubscriberID
							and qst.itemUID = subscriptionTree.itemUID
						where subscriptionTree.itemUID = tmp.itemUID
						and subscriptionTree.memberID = account.childMemberID
						and subscriptionTree.subscriberID = subscriptionTree.rootsubscriberID
						order by subscriptionTree.rootsubscriberid
						for xml path('subscriptiontree'), type
					)
					from #qryAllFirmMembers as account
					where account.itemUID = company.itemUID
					order by account.lastname, account.firstname, account.childMemberID
					for xml path('account'), type
				)
			from #tmpTblQueueItems_firmbilling as company
			where company.itemUID = tmp.itemUID
			order by company.company, company.memberID
			for xml path('company'), root('companies'), type
		),'<companies/>') as varchar(max)) as firmXML
	) as firms

	-- create directories to store xmls
	IF OBJECT_ID('tempdb..#tblDirs') IS NOT NULL 
		DROP TABLE #tblDirs
	select distinct itemGroupUID, membercentral.dbo.fn_createDirectory(@filePath + cast(itemGroupUID as varchar(50))) as cdResult
	into #tblDirs
	from #tmpTblQueueItems_firmbilling
	where membercentral.dbo.fn_DirectoryExists(@filePath + cast(itemGroupUID as varchar(50))) = 0

	-- final data
	select itemUID, itemGroupUID, jobUID, siteID, memberNumber, company, xmlFieldSets, xmlConfigParam, 
		membercentral.dbo.fn_writefile(@filePath + cast(itemGroupUID as varchar(50)) + '\' + cast(itemUID as varchar(50)) + '.xml',xmlFirms,1) as writeFileResult
	from #tmpTblQueueItems_firmbilling
	order by itemUID

	IF OBJECT_ID('tempdb..#tblDirs') IS NOT NULL 
		DROP TABLE #tblDirs
	IF OBJECT_ID('tempdb..#tblSubInvoices') IS NOT NULL 
		DROP TABLE #tblSubInvoices
	IF OBJECT_ID('tempdb..#tblSubPayments') IS NOT NULL 
		DROP TABLE #tblSubPayments
	IF OBJECT_ID('tempdb..#tblSubTreeTotals') IS NOT NULL 
		DROP TABLE #tblSubTreeTotals
	IF OBJECT_ID('tempdb..#qrySubs') IS NOT NULL 
		DROP TABLE #qrySubs
	IF OBJECT_ID('tempdb..#qryAllFirmMembers') IS NOT NULL 
		DROP TABLE #qryAllFirmMembers
	IF OBJECT_ID('tempdb..#tmpTblQueueItems_firmbilling') IS NOT NULL 
		DROP TABLE #tmpTblQueueItems_firmbilling

	RETURN 0
END TRY
BEGIN CATCH
	EXEC membercentral.dbo.up_errorhandler
	RETURN -1
END CATCH
GO
