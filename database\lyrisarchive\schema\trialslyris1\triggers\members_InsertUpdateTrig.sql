CREATE TRIGGER [dbo].[members_InsertUpdateTrig] 
ON [dbo].[members_] 
AFTER INSERT, UPDATE AS 
BEGIN 
	SET NOCOUNT ON 
	IF UPDATE(UnsubMessageID_) 
	BEGIN 		INSERT INTO lyrUnsummarizedRecips ( MailingID, MemberID, UnsubCount ) 
		SELECT i.UnsubMessageID_, i.MemberID_, 1 
			FROM inserted i JOIN deleted d ON i.MemberID_ = d.MemberID_ 
			WHERE i.UnsubMessageID_ IS NOT NULL AND d.UnsubMessageID_ IS NULL 
	END 
	IF UPDATE(MemberType_) 
	BEGIN 
		INSERT INTO lyrUnsummarizedRecips (MailingID, MemberID, ReferredCount ) 
					SELECT r.RollupID_, 
							r.ReceiverMemberID_, 
							1 
						FROM inserted i JOIN deleted d ON i.MemberID_ = d.MemberID_ JOIN referrals_ r ON i.MemberID_ = r.ReceiverMemberID_ 
						WHERE d.MemberType_ = 'referred' AND i.MemberType_ = 'normal' 
	END 
	UPDATE members_ SET members_.Domain_ = LOWER(inserted.Domain_) FROM inserted WHERE (LOWER(inserted.Domain_) NOT LIKE inserted.Domain_) AND members_.MemberID_ = inserted.MemberID_; 
	IF (UPDATE(Domain_) OR UPDATE(UserNameLC_))
	BEGIN 
        UPDATE lyrMemberSubsets SET Domain = LOWER(i.Domain_), UserNameLC = i.UserNameLC_ FROM inserted i, subsets_ s, deleted d WHERE 
               i.MemberID_ = d.MemberID_ AND s.List_ = d.List_ AND s.SubsetID_ = lyrMemberSubsets.SubsetID AND 
               lyrMemberSubsets.List = d.List_ AND lyrMemberSubsets.UserNameLC = d.UserNameLC_ AND 
               lyrMemberSubsets.Domain = d.Domain_ AND lyrMemberSubsets.MemberID = d.MemberID_ 
	END 
	SET NOCOUNT OFF 
END
ALTER TABLE [dbo].[members_] ENABLE TRIGGER [members_InsertUpdateTrig]
GO
