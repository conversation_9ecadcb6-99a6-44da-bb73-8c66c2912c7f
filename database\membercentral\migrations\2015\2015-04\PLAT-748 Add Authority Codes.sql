use membercentral
GO
ALTER TABLE dbo.crd_authorities ADD authorityCode varchar(20) NULL;
GO

update dbo.crd_authorities set authorityCode = 'CAStateBar' where authorityName = 'State Bar Of California'
update dbo.crd_authorities set authorityCode = 'TXStateBar' where authorityName = 'State Bar Of Texas Minimum Continuing Legal Education'
update dbo.crd_authorities set authorityCode = 'ACPEPharm' where authorityName = 'Accreditation Council for Pharmacy Education (Pharmacists)'
update dbo.crd_authorities set authorityCode = 'INCLE' where authorityName = 'Indiana Commission For Continuing Legal Education'
update dbo.crd_authorities set authorityCode = 'ILMCLE' where authorityName = 'MCLE Board of the Supreme Court of Illinois'
update dbo.crd_authorities set authorityCode = 'KYStateBar' where authorityName = 'Kentucky Bar Association, CLE Commission'
update dbo.crd_authorities set authorityCode = 'NFPA' where authorityName = 'National Federation of Paralegal Associations'
update dbo.crd_authorities set authorityCode = 'OHCLE' where authorityName = 'Supreme Court Of Ohio, Commission on CLE'
update dbo.crd_authorities set authorityCode = 'ORStateBar' where authorityName = 'Oregon State Bar Association'
update dbo.crd_authorities set authorityCode = 'MSCLE' where authorityName = 'Mississippi Commission on Continuing Legal Education'
update dbo.crd_authorities set authorityCode = 'LSUC' where authorityName = 'Law Society of Upper Canada'
update dbo.crd_authorities set authorityCode = 'NYCLE' where authorityName = 'New York State Unified Court System, CLE Board'
update dbo.crd_authorities set authorityCode = 'NEMCLE' where authorityName = 'Nebraska Supreme Court MCLE Commission'
update dbo.crd_authorities set authorityCode = 'ARCLE' where authorityName = 'Arkansas Continuing Legal Education Board'
update dbo.crd_authorities set authorityCode = 'FLStateBar' where authorityName = 'Florida Bar, Legal Specialization & Education Department'
update dbo.crd_authorities set authorityCode = 'NVCLE' where authorityName = 'Nevada Board of Continuing Legal Education'
update dbo.crd_authorities set authorityCode = 'TNCLE' where authorityName = 'Tennessee Commission On Continuing Legal Education'
update dbo.crd_authorities set authorityCode = 'LAMCLE' where authorityName = 'Committee on Mandatory Continuing Legal Education, Louisiana Supreme Court'
update dbo.crd_authorities set authorityCode = 'MOMCLE' where authorityName = 'Missouri Bar, MCLE Department'
update dbo.crd_authorities set authorityCode = 'NCStateBar' where authorityName = 'North Carolina State Bar, Board of Continuing Legal Education'
update dbo.crd_authorities set authorityCode = 'SCCLE' where authorityName = 'South Carolina Commission on Continuing Legal Education and Specialization'
update dbo.crd_authorities set authorityCode = 'GAStateBar' where authorityName = 'State Bar of Georgia, CLE Department'
update dbo.crd_authorities set authorityCode = 'CAWC' where authorityName = 'State of California Division of Workers'' Compensation'
update dbo.crd_authorities set authorityCode = 'CALS' where authorityName = 'State Bar of California Board of Legal Specialization'
update dbo.crd_authorities set authorityCode = 'ACPETech' where authorityName = 'Accreditation Council for Pharmacy Education (Technicians)'
update dbo.crd_authorities set authorityCode = 'TXCourtReporters' where authorityName = 'Court Reporters Certification Board of Texas'
update dbo.crd_authorities set authorityCode = 'NCStateBarParaLegal' where authorityName = 'North Carolina State Bar, Board of Paralegal Certification'
update dbo.crd_authorities set authorityCode = 'NMMCLE' where authorityName = 'New Mexico MCLE Commission'
update dbo.crd_authorities set authorityCode = 'AGDPACE' where authorityName = 'Academy of General Dentistry PACE'
update dbo.crd_authorities set authorityCode = 'KSCLE' where authorityName = 'Kansas Continuing Legal Education Commission'
update dbo.crd_authorities set authorityCode = 'WAStateBar' where authorityName = 'Washington State Bar Association'
update dbo.crd_authorities set authorityCode = 'NHMCLE' where authorityName = 'New Hampshire Minimum Continuing Legal Education Board'
update dbo.crd_authorities set authorityCode = 'MTStateBar' where authorityName = 'State Bar Of Montana'
update dbo.crd_authorities set authorityCode = 'ALStateBar' where authorityName = 'Alabama State Bar'
update dbo.crd_authorities set authorityCode = 'AZCLE' where authorityName = 'Arizona CLE (self-submission)'
update dbo.crd_authorities set authorityCode = 'COCLE' where authorityName = 'Supreme Court of Colorado, Board of Continuing Legal & Judicial Education'
update dbo.crd_authorities set authorityCode = 'MEStateBar' where authorityName = 'Maine Board of Overseers of the Bar'
update dbo.crd_authorities set authorityCode = 'RIMCLE' where authorityName = 'Rhode Island Supreme Court, Mandatory Continuing Legal Education'
update dbo.crd_authorities set authorityCode = 'VTCLE' where authorityName = 'Vermont Board Of Continuing Legal Education'
update dbo.crd_authorities set authorityCode = 'VAStateBar' where authorityName = 'Virginia MCLE Board, Virginia State Bar'
update dbo.crd_authorities set authorityCode = 'WVStateBar' where authorityName = 'West Virginia State Bar MCLE'
update dbo.crd_authorities set authorityCode = 'WIStateBar' where authorityName = 'Supreme Court Of Wisconsin, Board of Bar Examiners'
update dbo.crd_authorities set authorityCode = 'WYStateBar' where authorityName = 'Wyoming State Bar, CLE Program'
update dbo.crd_authorities set authorityCode = 'TAGT' where authorityName = 'Texas Association for the Gifted & Talented'
update dbo.crd_authorities set authorityCode = 'TXDental' where authorityName = 'Texas State Board of Dental Examiners'
update dbo.crd_authorities set authorityCode = 'DECLE' where authorityName = 'Commission on Continuing Legal Education of the Supreme Court of Delaware'
update dbo.crd_authorities set authorityCode = 'PACLE' where authorityName = 'Supreme Court of Pennsylvania Continuing Legal Education Board'
update dbo.crd_authorities set authorityCode = 'UTStateBar' where authorityName = 'Utah Bar Association'
update dbo.crd_authorities set authorityCode = 'MNCLE' where authorityName = 'Minnesota State Board of Continuing Legal Education'
update dbo.crd_authorities set authorityCode = 'IACLE' where authorityName = 'Iowa Office of Professional Regulation - CLE'
update dbo.crd_authorities set authorityCode = 'IDStateBar' where authorityName = 'Idaho State Bar'
update dbo.crd_authorities set authorityCode = 'NJStateBar' where authorityName = 'Supreme Court of New Jersey Board on Attorney Certification'
update dbo.crd_authorities set authorityCode = 'Sedona' where authorityName = 'The Sedona Conference'
update dbo.crd_authorities set authorityCode = 'Lorman' where authorityName = 'Lorman Education Services'
update dbo.crd_authorities set authorityCode = 'ASRT' where authorityName = 'American Society of Radiologic Technologists'
update dbo.crd_authorities set authorityCode = 'AKStateBar' where authorityName = 'Alaska Bar Association'
update dbo.crd_authorities set authorityCode = 'NDCle' where authorityName = 'North Dakota CLE Commission'
update dbo.crd_authorities set authorityCode = 'OKStateBar' where authorityName = 'Oklahoma Bar Association'
update dbo.crd_authorities set authorityCode = 'NALA' where authorityName = 'National Association of Legal Assistants'
update dbo.crd_authorities set authorityCode = 'HRCI' where authorityName = 'Human Resources Certification Institute'
update dbo.crd_authorities set authorityCode = 'BCLaw' where authorityName = 'Law Society of British Columbia'
update dbo.crd_authorities set authorityCode = 'TXPsych' where authorityName = 'Texas Psychological Association'
update dbo.crd_authorities set authorityCode = 'ACPE' where authorityName = 'Accreditation Council for Pharmacy Education'
update dbo.crd_authorities set authorityCode = 'TMA' where authorityName = 'Texas Medical Association'
update dbo.crd_authorities set authorityCode = 'AAPC' where authorityName = 'American Academy of Professional Coders'
update dbo.crd_authorities set authorityCode = 'NBLaw' where authorityName = 'Law Society of New Brunswick'
update dbo.crd_authorities set authorityCode = 'TEA' where authorityName = 'Texas Education Agency'
update dbo.crd_authorities set authorityCode = 'EIA' where authorityName = 'Educational Institute on Aging'
update dbo.crd_authorities set authorityCode = 'TXPTE' where authorityName = 'Texas Board of Physical Therapy Examiners'
update dbo.crd_authorities set authorityCode = 'TXDLR' where authorityName = 'Texas Department of Licensing & Regulation'
update dbo.crd_authorities set authorityCode = 'Weaver' where authorityName = 'Barry Weaver Consulting, LLC'
update dbo.crd_authorities set authorityCode = 'ACCME' where authorityName = 'Accreditation Council for Continuing Medical Education'
update dbo.crd_authorities set authorityCode = 'TXHealth' where authorityName = 'Texas Healthcare Trustees'
update dbo.crd_authorities set authorityCode = 'ASAE' where authorityName = 'American Society of Association Executives'
update dbo.crd_authorities set authorityCode = 'ADA' where authorityName = 'American Dental Association'
update dbo.crd_authorities set authorityCode = 'TXHospital' where authorityName = 'Texas Hospital Association'
update dbo.crd_authorities set authorityCode = 'CIC' where authorityName = 'Convention Industry Council'
update dbo.crd_authorities set authorityCode = 'GreenHousing' where authorityName = 'The Green Housing Credential'
update dbo.crd_authorities set authorityCode = 'TXSWE' where authorityName = 'Texas State Board of Social Worker Examiners'
update dbo.crd_authorities set authorityCode = 'TXSHS' where authorityName = 'Texas Department of State Health Services MC-1982'
update dbo.crd_authorities set authorityCode = 'TXBOEMFT' where authorityName = 'Texas State Board of Examiners of Marriage and Family Therapists'
update dbo.crd_authorities set authorityCode = 'TXCJ' where authorityName = 'Texas Department of Criminal Justice - CJAD'
update dbo.crd_authorities set authorityCode = 'AIA' where authorityName = 'The American Institute of Architects'
update dbo.crd_authorities set authorityCode = 'OHPTA' where authorityName = 'Ohio Physical Therapy Association'
update dbo.crd_authorities set authorityCode = 'INAPTA' where authorityName = 'Indiana Chapter, APTA'
update dbo.crd_authorities set authorityCode = 'GreenBuilding' where authorityName = 'Green Building Certification Institute'
update dbo.crd_authorities set authorityCode = 'TXLawEnforcement' where authorityName = 'Texas Commission on Law Enforcement Officer Standards and Education'
update dbo.crd_authorities set authorityCode = 'MNPT' where authorityName = 'Minnesota State Board of Physical Therapy'
update dbo.crd_authorities set authorityCode = 'TXPTA' where authorityName = 'Texas Parent Teacher Association'
update dbo.crd_authorities set authorityCode = 'TXMCLETLC' where authorityName = 'State Bar Of Texas Minimum Continuing Legal Education (for TLC)'
update dbo.crd_authorities set authorityCode = 'SMRP' where authorityName = 'Society of Maintenance & Reliability Professionals (SMRP)'
update dbo.crd_authorities set authorityCode = 'CCH' where authorityName = 'The Carolinas Center for Hospice and End of Life Care'
update dbo.crd_authorities set authorityCode = 'CCOther' where authorityName = 'The Carolinas Center (other disciplines)'
update dbo.crd_authorities set authorityCode = 'MNCosmetologists' where authorityName = 'Minnesota Board of Cosmetologists Examiners'
GO

ALTER TABLE dbo.crd_authorities ALTER COLUMN authorityCode varchar(20) NOT NULL;
GO

ALTER TABLE dbo.crd_authorityTypes ADD COLUMN typeCode varchar(20) NULL;
GO

update dbo.crd_authorityTypes
set typeCode = left(dbo.fn_regexReplace(typeName,'[^A-Za-z0-9]',''),20)
GO

update dbo.crd_authorityTypes set typeCode = 'WC' where typeName = 'Workers'' Compensation'
update dbo.crd_authorityTypes set typeCode = 'SpecialAppellate' where authorityID = 1 and typeName = 'Specialization in Appellate Law'
update dbo.crd_authorityTypes set typeCode = 'SpecialBankruptcy' where authorityID = 1 and typeName = 'Specialization in Bankruptcy Law'
update dbo.crd_authorityTypes set typeCode = 'SpecialEstate' where authorityID = 1 and typeName = 'Specialization in Estate Planning, Trust & Probate Law'
update dbo.crd_authorityTypes set typeCode = 'SpecialFamily' where authorityID = 1 and typeName = 'Specialization in Family Law'
update dbo.crd_authorityTypes set typeCode = 'SpecialTaxation' where authorityID = 1 and typeName = 'Specialization in Taxation Law'
update dbo.crd_authorityTypes set typeCode = 'EthicsDistanceEd' where authorityID = 6 and typeName = 'Ethics Distance Education'
update dbo.crd_authorityTypes set typeCode = 'NewClientFund' where authorityID = 13 and typeName = 'New Attorney Client Fund Hours'
update dbo.crd_authorityTypes set typeCode = 'NewLawGeneral' where authorityID = 13 and typeName = 'New Attorney Law General Hours'
update dbo.crd_authorityTypes set typeCode = 'NewLawOffice' where authorityID = 13 and typeName = 'New Attorney Law Office Hours'
update dbo.crd_authorityTypes set typeCode = 'NewLawProf' where authorityID = 13 and typeName = 'New Attorney Law Professionalism Hours'
update dbo.crd_authorityTypes set typeCode = 'ProfRespAccess' where authorityID = 14 and typeName = 'Professional Responsibility - Access to Justice'
update dbo.crd_authorityTypes set typeCode = 'ProfRespAbuse' where authorityID = 14 and typeName = 'Professional Responsibility - Child Abuse Reporting'
update dbo.crd_authorityTypes set typeCode = 'ProfRespEthics' where authorityID = 14 and typeName = 'Professional Responsibility - Ethics'
update dbo.crd_authorityTypes set typeCode = 'ProfDev' where authorityID = 16 and typeName = 'Continuing Professional Development'
update dbo.crd_authorityTypes set typeCode = 'EthicsProf' where authorityID = 16 and typeID = 39
update dbo.crd_authorityTypes set typeCode = 'EthicsProfFull' where authorityID = 16 and typeID = 93
update dbo.crd_authorityTypes set typeCode = 'EthicsProfDay1' where authorityID = 16 and typeID = 95
update dbo.crd_authorityTypes set typeCode = 'EthicsProfDay2' where authorityID = 16 and typeID = 97
update dbo.crd_authorityTypes set typeCode = 'EthicsProfLaw' where authorityID = 16 and typeID = 112
update dbo.crd_authorityTypes set typeCode = 'SubstantiveLawFull' where authorityID = 16 and typeID = 92
update dbo.crd_authorityTypes set typeCode = 'SubstantiveLawDay1' where authorityID = 16 and typeID = 94
update dbo.crd_authorityTypes set typeCode = 'SubstantiveLawDay2' where authorityID = 16 and typeID = 96
update dbo.crd_authorityTypes set typeCode = 'SubstantiveLawLaw' where authorityID = 16 and typeID = 113
update dbo.crd_authorityTypes set typeCode = 'ProfPractice' where authorityID = 17 and typeName = 'Areas of Professional Practice'
update dbo.crd_authorityTypes set typeCode = 'EthicsProf' where authorityID = 17 and typeName = 'Ethics and Professionalism'
update dbo.crd_authorityTypes set typeCode = 'FacultyProfPractice' where authorityID = 17 and typeName = 'Faculty: Areas of Professional Practice'
update dbo.crd_authorityTypes set typeCode = 'FacultyEthicsProf' where authorityID = 17 and typeName = 'Faculty: Ethics and Professionalism'
update dbo.crd_authorityTypes set typeCode = 'FacultyLawPractice' where authorityID = 17 and typeName = 'Faculty: Law Practice Management'
update dbo.crd_authorityTypes set typeCode = 'LawPractice' where authorityID = 17 and typeName = 'Law Practice Management'
update dbo.crd_authorityTypes set typeCode = 'ProfResp' where authorityID = 18 and typeName = 'Professional Responsibility'
update dbo.crd_authorityTypes set typeCode = 'MentalIll' where authorityID = 20 and typeName = 'Mental Illness Awareness'
update dbo.crd_authorityTypes set typeCode = 'EthicsProf' where authorityID = 22 and typeName = 'Ethics/Professionalism'
update dbo.crd_authorityTypes set typeCode = 'WC' where authorityID = 29 and typeName = 'Workers'' Compensation Law'
update dbo.crd_authorityTypes set typeCode = 'ElectivesParticip' where authorityID = 40 and typeName = 'Electives (Participation)'
update dbo.crd_authorityTypes set typeCode = 'EndodonticsParticip' where authorityID = 40 and typeName = 'Endodontics (Participation)'
update dbo.crd_authorityTypes set typeCode = 'EstheticsLecture' where authorityID = 40 and typeName = 'Esthetics/Cosmetic Dentistry (Lecture)'
update dbo.crd_authorityTypes set typeCode = 'EstheticsParticip' where authorityID = 40 and typeName = 'Esthetics/Cosmetic Dentistry (Participation)'
update dbo.crd_authorityTypes set typeCode = 'ImplantsParticip' where authorityID = 40 and typeName = 'Implants (Participation)'
update dbo.crd_authorityTypes set typeCode = 'MyofascialLecture' where authorityID = 40 and typeName = 'Myofascial Pain/Occlusion (Lecture)'
update dbo.crd_authorityTypes set typeCode = 'MyofascialParticip' where authorityID = 40 and typeName = 'Myofascial Pain/Occlusion (Participation)'
update dbo.crd_authorityTypes set typeCode = 'Self' where authorityID = 40 and typeName = 'Online Self Instruction'
update dbo.crd_authorityTypes set typeCode = 'OralSurgeryLecture' where authorityID = 40 and typeName = 'Oral and Maxillofacial Surgery (Lecture)'
update dbo.crd_authorityTypes set typeCode = 'OralSurgeryParticip' where authorityID = 40 and typeName = 'Oral and Maxillofacial Surgery (Participation)'
update dbo.crd_authorityTypes set typeCode = 'OralMed730Lecture' where authorityID = 40 and typeName = 'Oral Medicine (730) Lecture'
update dbo.crd_authorityTypes set typeCode = 'OralMedLecture' where authorityID = 40 and typeName = 'Oral Medicine, Diagnosis, Pathology (Lecture)'
update dbo.crd_authorityTypes set typeCode = 'OralMedParticip' where authorityID = 40 and typeName = 'Oral Medicine, Diagnosis, Pathology (Participation)'
update dbo.crd_authorityTypes set typeCode = 'PediatricLecture' where authorityID = 40 and typeName = 'Pediatric Dentistry (Lecture)'
update dbo.crd_authorityTypes set typeCode = 'PediatricParticip' where authorityID = 40 and typeName = 'Pediatric Dentistry (Participation)'
update dbo.crd_authorityTypes set typeCode = 'PracticeMgmt550' where authorityID = 40 and typeName = 'Practice Management (550)'
update dbo.crd_authorityTypes set typeCode = 'PracticeMgmtLecture' where authorityID = 40 and typeName = 'Practice Management (Lecture)'
update dbo.crd_authorityTypes set typeCode = 'PracticeMgmtParticip' where authorityID = 40 and typeName = 'Practice Management (Participation)'
update dbo.crd_authorityTypes set typeCode = 'ProsthoFixedLecture' where authorityID = 40 and typeName = 'Prosthodontics/Fixed (Lecture)'
update dbo.crd_authorityTypes set typeCode = 'ProsthoFixedParticip' where authorityID = 40 and typeName = 'Prosthodontics/Fixed (Participation)'
update dbo.crd_authorityTypes set typeCode = 'ProsthoRemLecture' where authorityID = 40 and typeName = 'Prosthodontics/Removable (Lecture)'
update dbo.crd_authorityTypes set typeCode = 'ProsthoRemParticip' where authorityID = 40 and typeName = 'Prosthodontics/Removable (Participation)'
update dbo.crd_authorityTypes set typeCode = 'RestorativeLecture' where authorityID = 40 and typeName = 'Restorative Dentistry (Lecture)'
update dbo.crd_authorityTypes set typeCode = 'RestorativeParticip' where authorityID = 40 and typeName = 'Restorative Dentistry (Participation)'
update dbo.crd_authorityTypes set typeCode = 'SelfImproveLecture' where authorityID = 40 and typeName = 'Self Improvement (Lecture)'
update dbo.crd_authorityTypes set typeCode = 'SelfImproveParticip' where authorityID = 40 and typeName = 'Self Improvement (Participation)'
update dbo.crd_authorityTypes set typeCode = 'SpecialCareLectur' where authorityID = 40 and typeName = 'Special Patient Care (Lecture)'
update dbo.crd_authorityTypes set typeCode = 'SpecialCareParticip' where authorityID = 40 and typeName = 'Special Patient Care (Participation)'
update dbo.crd_authorityTypes set typeCode = 'ProfRespSelfStudy' where authorityID = 49 and typeName = 'Prof Responsibility Self-Study'
update dbo.crd_authorityTypes set typeCode = 'ProfResp' where authorityID = 49 and typeName = 'Professional Responsibility'
update dbo.crd_authorityTypes set typeCode = 'LawPracticeMgmt' where authorityID = 53 and typeName = 'Law Practice Management'
update dbo.crd_authorityTypes set typeCode = 'AttyCert' where authorityID = 64 and typeName = 'Attorney Certification'
update dbo.crd_authorityTypes set typeCode = 'EthicsProf' where authorityID = 64 and typeName = 'Ethics and Professionalism'
update dbo.crd_authorityTypes set typeCode = 'CatA' where authorityID = 67 and typeName = 'Category A Continuing Ed'
update dbo.crd_authorityTypes set typeCode = 'ProfResp' where authorityID = 73 and typeName = 'Professional Responsibility & Ethics, client care'
update dbo.crd_authorityTypes set typeCode = 'LegalEd' where authorityID = 73 and typeName = 'Structured Legal Education'
update dbo.crd_authorityTypes set typeCode = 'ProfResp' where authorityID = 74 and typeName = 'Professional Responsibility'
update dbo.crd_authorityTypes set typeCode = 'ContEd' where authorityID = 75 and typeName = 'of continuing education'
update dbo.crd_authorityTypes set typeCode = 'Ethics' where authorityID = 76 and typeName = 'ethics and/or professional responsibility'
update dbo.crd_authorityTypes set typeCode = 'CPD' where authorityID = 80 and typeName = 'Mandatory Continuing Professional Development'
update dbo.crd_authorityTypes set typeCode = 'CE' where authorityID = 81 and typeName = 'Continuing Education (CE)'
update dbo.crd_authorityTypes set typeCode = 'Managers', typeName='Assisted Living Managers' where authorityID = 83 and typeName = 'Assisted Living Managers '
update dbo.crd_authorityTypes set typeCode = 'BusinessPractice' where authorityID = 85 and typeName = 'Business Practice (CEU credits)'
update dbo.crd_authorityTypes set typeCode = 'CatII' where authorityID = 91 and typeName = 'Pre-Approved Category II (non-ACHE)'
update dbo.crd_authorityTypes set typeCode = 'CMPHR' where authorityID = 93 and typeName = 'CMP Hour: Human Resources'
update dbo.crd_authorityTypes set typeCode = 'CMPDesign' where authorityID = 93 and typeName = 'CMP Hour: Mktg/Event Design'
update dbo.crd_authorityTypes set typeCode = 'CMPProf' where authorityID = 93 and typeName = 'CMP Hour: Professionalism'
update dbo.crd_authorityTypes set typeCode = 'CMPProjMgmt' where authorityID = 93 and typeName = 'CMP Hour: Project Management'
update dbo.crd_authorityTypes set typeCode = 'CMPStakeMgmt' where authorityID = 93 and typeName = 'CMP Hour: Stakeholder Mgmt'
update dbo.crd_authorityTypes set typeCode = 'CMPStratPlan' where authorityID = 93 and typeName = 'CMP Hour: Strategic Planning'
update dbo.crd_authorityTypes set typeCode = 'CEHour' where authorityID = 107 and typeName = 'Continuing Education Hour'
update dbo.crd_authorityTypes set typeCode = 'CEHour' where authorityID = 110 and typeName = 'Hour Continuing Education'
GO

ALTER TABLE dbo.crd_authorityTypes ALTER COLUMN typeCode varchar(20) NOT NULL;
GO

ALTER TABLE dbo.crd_authorityTypes ADD CONSTRAINT
	IX_crd_authorityTypes UNIQUE NONCLUSTERED 
	(
	authorityID,
	typeCode
	) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

GO

