use membercentral
GO
ALTER FUNCTION [dbo].[fn_ev_registrantTransactions] (@registrantID int)
RETURNS @tblTransactions TABLE (
	transactionID int, 
	ownedByOrgID int, 
	recordedOnSiteID int, 
	statusID int, 
	detail varchar(max), 
	parentTransactionID int,
	amount money, 
	dateRecorded datetime, 
	transactionDate datetime, 
	assignedToMemberID int, 
	recordedByMemberID int,
	statsSessionID int, 
	typeID int, 
	accrualDate datetime, 
	debitGLAccountID int, 
	creditGLAccountID int
)
WITH SCHEMABINDING
AS
BEGIN

	-- this should include all transactions (ignoring status)
	DECLARE @tblHold TABLE (transactionID int)

	-- rate sales
	insert into @tblHold
	select t.transactionID
	from dbo.ev_registrants as r
	inner join dbo.tr_applications as a on a.itemType = 'Rate' and a.ItemID = r.registrantID
	inner join dbo.tr_transactions as t on t.transactionID = a.transactionID 
	inner join dbo.tr_types as tt on tt.typeID = t.typeID and tt.type = 'Sale'
	where r.registrantID = @registrantID

	-- custom q sales
	insert into @tblHold
	select t.transactionID
	from dbo.ev_registrants as r
	inner join dbo.ev_registrantDetails as rd on rd.registrantID = r.registrantID
	inner join dbo.tr_applications as a on a.itemType = 'Custom' and a.ItemID = rd.detailID
	inner join dbo.tr_transactions as t on t.transactionID = a.transactionID 
	inner join dbo.tr_types as tt on tt.typeID = t.typeID and tt.type = 'Sale'
	where r.registrantID = @registrantID

	-- tax
	insert into @tblHold
	select distinct t.transactionID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'SalesTaxTrans'
	inner join @tblHold as rt on rt.transactionID = tr.appliedToTransactionID 

	-- adjustments
	insert into @tblHold
	select distinct t.transactionID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'AdjustTrans'
	inner join @tblHold as rt on rt.transactionID = tr.appliedToTransactionID 

	-- deferred
	insert into @tblHold
	select distinct t.transactionID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'DITSaleTrans'
	inner join @tblHold as rt on rt.transactionID = tr.appliedToTransactionID 

	-- allocations (allocSaleTrans)
	insert into @tblHold
	select distinct t.transactionID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'AllocSaleTrans'
	inner join @tblHold as rt on rt.transactionID = tr.appliedToTransactionID 

	-- writeoffs (writeOffSaleTrans)
	insert into @tblHold
	select distinct t.transactionID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'WriteOffSaleTrans'
	inner join @tblHold as rt on rt.transactionID = tr.appliedToTransactionID 

	-- payments (tied to allocations - AllocPayTrans)
	insert into @tblHold
	select distinct t.transactionID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.appliedToTransactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'AllocPayTrans'
	inner join @tblHold as rt on rt.transactionID = tr.transactionID 

	-- refunds tied to included payments
	insert into @tblHold
	select distinct t.transactionID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'RefundTrans'
	inner join @tblHold as rt on rt.transactionID = tr.appliedToTransactionID 


	insert into @tblTransactions
	select t.transactionID, t.ownedByOrgID, t.recordedOnSiteID, t.statusID, t.detail, t.parentTransactionID,
		t.amount, t.dateRecorded, t.transactionDate, t.assignedToMemberID, t.recordedByMemberID,
		t.statsSessionID, t.typeID, t.accrualDate, t.debitGLAccountID, t.creditGLAccountID
	from @tblHold as rt
	inner join dbo.tr_transactions as t on t.transactionid = rt.transactionid

	RETURN
END
GO

