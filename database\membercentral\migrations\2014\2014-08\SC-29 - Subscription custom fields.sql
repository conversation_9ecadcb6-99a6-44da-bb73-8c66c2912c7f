USE customApps
GO
declare @udid int, @subTypeUID uniqueidentifier, @subUID uniqueidentifier

insert into dbo.schedTask_memberJoinDates (siteCode, joinDateFieldName, rejoinDateFieldName, droppedDateFieldName, paidThruDateFieldName, lastSuccessDate, lastErrorCode, isActive)
values ('SC', 'Join Date', 'Rejoin Date', 'Dropped Date', 'Expiration Date', '1/1/1980', 0, 1)
	select @udid = SCOPE_IDENTITY()

select @subTypeUID = t.uid 
	from membercentral.dbo.sub_types as t
	inner join membercentral.dbo.sites on sites.siteID = t.siteID
	where sites.siteCode = 'SC'
	and t.typeName = 'Membership Dues'
	and t.status = 'A'
insert into dbo.schedTask_memberJoinDateSubTypes (memberJoinDateUDID, subscriptionTypeUID, subscriptionUID)
values (@udid, @subTypeUID, null)
set @subTypeUID = null

select @subTypeUID = t.uid 
	from membercentral.dbo.sub_types as t
	inner join membercentral.dbo.sites on sites.siteID = t.siteID
	where sites.siteCode = 'SC'
	and t.typeName = 'Card Program'
	and t.status = 'A'
select @subUID = s.uid
	from membercentral.dbo.sub_subscriptions as s
	inner join membercentral.dbo.sub_types as t on t.typeID = s.typeID
	where t.uid = @subTypeUID
	and s.subscriptionName = 'Dues'
	and s.status = 'A'
IF @subTypeUID is not null and @subUID is not null
	insert into dbo.schedTask_memberJoinDateSubTypes (memberJoinDateUDID, subscriptionTypeUID, subscriptionUID)
	values (@udid, @subTypeUID, @subUID)

declare @ec int
EXEC dbo.job_memberJoinDates @udid, @ec OUTPUT
select @ec as ec
GO