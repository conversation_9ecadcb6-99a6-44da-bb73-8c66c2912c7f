USE [memberCentral]
GO
ALTER PROC [dbo].[ams_updateMemberDataColumnValue]
@valueID int,
@columnValue varchar(255)

AS

DECLARE @newvalueID int, @columnID int, @recalc bit
DECLARE @tblCond TABLE (orgID int, conditionID int)
SET @recalc = 0

-- if valueID not passed do nothing.
IF @valueID is null or @columnValue is null
	GOTO on_error

-- get data type code for column
DECLARE @columnDataTypeCode varchar(20)
SELECT @columnDataTypeCode = dt.dataTypeCode, @columnID = c.columnID
	from dbo.ams_memberDataColumnValues as cv
	inner join dbo.ams_memberDataColumns as c on c.columnID = cv.columnID
	inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = c.dataTypeID
	where cv.valueID = @valueID

IF @columnDataTypeCode = 'STRING' BEGIN
	DECLARE @realColumnValue varchar(255)
	SELECT @realColumnValue = @columnValue
		IF @@ERROR <> 0 GOTO on_error
	SELECT @newvalueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueString = @realColumnValue COLLATE Latin1_General_CS_AS
	IF @newvalueID is null BEGIN
		UPDATE dbo.ams_memberDataColumnValues 
		SET columnValueString = @realColumnValue
		WHERE valueID = @valueID
			IF @@ERROR <> 0 GOTO on_error
		SET @recalc = 1
	END
END
IF @columnDataTypeCode = 'DECIMAL2' BEGIN
	DECLARE @realColumnValue2 decimal(9,2)
	SELECT @realColumnValue2 = cast(@columnValue as decimal(9,2))
		IF @@ERROR <> 0 GOTO on_error
	SELECT @newvalueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueDecimal2 = @realColumnValue2
	IF @newvalueID is null BEGIN
		UPDATE dbo.ams_memberDataColumnValues 
		SET columnValueDecimal2 = @realColumnValue2
		WHERE valueID = @valueID
			IF @@ERROR <> 0 GOTO on_error
		SET @recalc = 1
	END
END
IF @columnDataTypeCode = 'INTEGER' BEGIN
	DECLARE @realColumnValue3 int
	SELECT @realColumnValue3 = cast(@columnValue as int)
		IF @@ERROR <> 0 GOTO on_error
	SELECT @newvalueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueInteger = @realColumnValue3
	IF @newvalueID is null BEGIN
		UPDATE dbo.ams_memberDataColumnValues 
		SET columnValueInteger = @realColumnValue3
		WHERE valueID = @valueID
			IF @@ERROR <> 0 GOTO on_error
		SET @recalc = 1
	END
END
IF @columnDataTypeCode = 'DATE' BEGIN
	DECLARE @realColumnValue4 datetime
	SELECT @realColumnValue4 = cast(@columnValue as datetime)
		IF @@ERROR <> 0 GOTO on_error
	SELECT @newvalueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueDate = @realColumnValue4
	IF @newvalueID is null BEGIN
		UPDATE dbo.ams_memberDataColumnValues 
		SET columnValueDate = @realColumnValue4
		WHERE valueID = @valueID
			IF @@ERROR <> 0 GOTO on_error
		SET @recalc = 1
	END
END
IF @columnDataTypeCode = 'BIT' BEGIN
	DECLARE @realColumnValue5 bit
	SELECT @realColumnValue5 = cast(@columnValue as bit)
		IF @@ERROR <> 0 GOTO on_error
	SELECT @newvalueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueBit = @realColumnValue5
	IF @newvalueID is null BEGIN
		UPDATE dbo.ams_memberDataColumnValues 
		SET columnValueBit = @realColumnValue5
		WHERE valueID = @valueID
			IF @@ERROR <> 0 GOTO on_error
		SET @recalc = 1
	END
END
IF @columnDataTypeCode = 'XML' BEGIN
	DECLARE @realColumnValue6 xml
	SELECT @realColumnValue6 = cast(@columnValue as xml)
		IF @@ERROR <> 0 GOTO on_error
	SELECT @newvalueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and cast(columnValueXML as varchar(max)) = cast(@realColumnValue6 as varchar(max))
	IF @newvalueID is null BEGIN
		UPDATE dbo.ams_memberDataColumnValues 
		SET columnValueXML = @realColumnValue6
		WHERE valueID = @valueID
			IF @@ERROR <> 0 GOTO on_error
		SET @recalc = 1
	END
END
IF @columnDataTypeCode = 'CONTENTOBJ' OR @columnDataTypeCode = 'DOCUMENTOBJ' BEGIN
	DECLARE @realColumnValue7 int
	SELECT @realColumnValue7 = cast(@columnValue as int)
		IF @@ERROR <> 0 GOTO on_error
	SELECT @newvalueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueSiteResourceID = @realColumnValue7
	IF @newvalueID is null BEGIN
		UPDATE dbo.ams_memberDataColumnValues 
		SET columnValueSiteResourceID = @realColumnValue7
		WHERE valueID = @valueID
			IF @@ERROR <> 0 GOTO on_error
		SET @recalc = 1
	END
END

IF @recalc = 1 BEGIN
	INSERT INTO @tblCond (orgID, conditionID)
	select distinct mdc.orgID, vgc.conditionID
	from dbo.ams_memberDataColumnValues as mdcv
	inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
	inner join dbo.ams_virtualGroupConditions as vgc on vgc.fieldcode = 'md_' + cast(mdc.columnID as varchar(10))
	inner join dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
		and vgcv.conditionValue = cast(@valueID as varchar(10))
	where mdcv.valueID = @valueID
		IF @@ERROR <> 0 GOTO on_error

	UPDATE vgc
	set vgc.verbose = dbo.ams_getVirtualGroupConditionVerbose(vgc.conditionID)
	from dbo.ams_virtualGroupConditions as vgc
	inner join @tblCond as tbl on tbl.conditionID = vgc.conditionID
		IF @@ERROR <> 0 GOTO on_error

	-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
	declare @itemGroupUID uniqueidentifier, @orgID int, @conditionIDList varchar(max)
	SELECT top 1 @orgID = orgID from @tblCond
	SELECT @conditionIDList = COALESCE(@conditionIDList + ',', '') + cast(c.conditionID as varchar(10)) 
		from @tblCond as c
		group by c.conditionID
	IF @conditionIDList is not null BEGIN
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList='', @conditionIDList=@conditionIDList, @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	END
END

RETURN 0

-- error exit
on_error:
	RETURN -1
GO
	
ALTER PROC [dbo].[ams_createMemberDataColumnValue]
@columnID int,
@columnValue varchar(max),
@valueID int OUTPUT

AS

-- ensure null
SELECT @valueID = null

-- get data type code for column
DECLARE @columnDataTypeCode varchar(20)
SELECT @columnDataTypeCode = dt.dataTypeCode
	FROM dbo.ams_memberDataColumnDataTypes as dt
	INNER JOIN dbo.ams_memberDataColumns as c on c.dataTypeID = dt.dataTypeID
		AND c.columnID = @columnID

-- check for existing value. if not there, add it
IF @columnDataTypeCode = 'STRING' BEGIN
	DECLARE @realColumnValue varchar(255)
	SELECT @realColumnValue = @columnValue
		IF @@ERROR <> 0 GOTO on_error
	SELECT @valueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueString = @realColumnValue COLLATE Latin1_General_CS_AS
	IF @valueID is null BEGIN
		INSERT INTO dbo.ams_memberDataColumnValues (columnID, columnValueString)
		VALUES (@columnID, @realColumnValue)
			IF @@ERROR <> 0 GOTO on_error
			SELECT @valueID = SCOPE_IDENTITY()
	END
END
IF @columnDataTypeCode = 'DECIMAL2' BEGIN
	DECLARE @realColumnValue2 decimal(9,2)
	SELECT @realColumnValue2 = cast(@columnValue as decimal(9,2))
		IF @@ERROR <> 0 GOTO on_error
	SELECT @valueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueDecimal2 = @realColumnValue2
	IF @valueID is null BEGIN
		INSERT INTO dbo.ams_memberDataColumnValues (columnID, columnValueDecimal2)
		VALUES (@columnID, @realColumnValue2)
			IF @@ERROR <> 0 GOTO on_error
			SELECT @valueID = SCOPE_IDENTITY()
	END
END
IF @columnDataTypeCode = 'INTEGER' BEGIN
	DECLARE @realColumnValue3 int
	SELECT @realColumnValue3 = cast(@columnValue as int)
		IF @@ERROR <> 0 GOTO on_error
	SELECT @valueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueInteger = @realColumnValue3
	IF @valueID is null BEGIN
		INSERT INTO dbo.ams_memberDataColumnValues (columnID, columnValueInteger)
		VALUES (@columnID, @realColumnValue3)
			IF @@ERROR <> 0 GOTO on_error
			SELECT @valueID = SCOPE_IDENTITY()
	END
END
IF @columnDataTypeCode = 'DATE' BEGIN
	DECLARE @realColumnValue4 datetime
	SELECT @realColumnValue4 = cast(@columnValue as datetime)
		IF @@ERROR <> 0 GOTO on_error
	SELECT @valueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueDate = @realColumnValue4
	IF @valueID is null BEGIN
		INSERT INTO dbo.ams_memberDataColumnValues (columnID, columnValueDate)
		VALUES (@columnID, @realColumnValue4)
			IF @@ERROR <> 0 GOTO on_error
			SELECT @valueID = SCOPE_IDENTITY()
	END
END
IF @columnDataTypeCode = 'BIT' BEGIN
	DECLARE @realColumnValue5 bit
	SELECT @realColumnValue5 = cast(@columnValue as bit)
		IF @@ERROR <> 0 GOTO on_error
	SELECT @valueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueBit = @realColumnValue5
	IF @valueID is null BEGIN
		INSERT INTO dbo.ams_memberDataColumnValues (columnID, columnValueBit)
		VALUES (@columnID, @realColumnValue5)
			IF @@ERROR <> 0 GOTO on_error
			SELECT @valueID = SCOPE_IDENTITY()
	END
END
IF @columnDataTypeCode = 'XML' BEGIN
	DECLARE @realColumnValue6 xml
	SELECT @realColumnValue6 = cast(@columnValue as xml)
		IF @@ERROR <> 0 GOTO on_error
	SELECT @valueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and cast(columnValueXML as varchar(max)) = cast(@realColumnValue6 as varchar(max))
	IF @valueID is null BEGIN
		INSERT INTO dbo.ams_memberDataColumnValues (columnID, columnValueXML)
		VALUES (@columnID, @realColumnValue6)
			IF @@ERROR <> 0 GOTO on_error
			SELECT @valueID = SCOPE_IDENTITY()
	END
END
IF @columnDataTypeCode = 'CONTENTOBJ' OR @columnDataTypeCode = 'DOCUMENTOBJ' BEGIN
	DECLARE @realColumnValue7 int
	SELECT @realColumnValue7 = cast(@columnValue as int)
		IF @@ERROR <> 0 GOTO on_error
	SELECT @valueID = valueID FROM dbo.ams_memberDataColumnValues where columnID = @columnID and columnValueSiteResourceID = @realColumnValue7
	IF @valueID is null BEGIN
		INSERT INTO dbo.ams_memberDataColumnValues (columnID, columnValueSiteResourceID)
		VALUES (@columnID, @realColumnValue7)
			IF @@ERROR <> 0 GOTO on_error
			SELECT @valueID = SCOPE_IDENTITY()
	END
END

RETURN 0

-- error exit
on_error:
	SELECT @valueID = 0
	RETURN -1
GO	
	
	