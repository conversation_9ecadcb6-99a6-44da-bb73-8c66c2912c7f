ALTER PROC dbo.job_runHourlyJobs
AS

DECLARE @errorSubject VARCHAR(100), @errorTitle VARCHAR(100), @errmsg nvarchar(2048), @proc sysname, @lineno INT;

/* ********************** */
/* enforce list settings  */
/* ********************** */
BEGIN TRY
	EXEC dbo.job_enforceListSettings
END TRY
BEGIN CATCH
	SET @errorSubject = 'Error Running Adhoc queries to enforce list settings (NoEmailSub_,security_,MriVisibility_ )';
	SET @errorTitle = 'Error Running Adhoc queries to enforce list settings';
	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line();
	SELECT @errmsg = coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + '<br/><br/>' + @errmsg;
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errmsg, @forDev=1;	
END CATCH


RETURN 0
GO
