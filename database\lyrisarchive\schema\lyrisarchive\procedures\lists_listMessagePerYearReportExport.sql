ALTER PROC dbo.lists_listMessagePerYearReportExport
@orgcode varchar(10),
@filename varchar(800)

AS

declare @minYear int, @maxYear int, @yrList varchar(200), @yrListSelect varchar(max), @pvtQry varchar(4000);

IF OBJECT_ID('tempdb..#tmpData') IS NOT NULL 
	DROP TABLE #tmpData;
IF OBJECT_ID('tempdb..##tmpListPerYearExport') IS NOT NULL 
	DROP TABLE ##tmpListPerYearExport;

CREATE TABLE #tmpData (list varchar(60), msgYear int, msgCount int);

-- get unpivoted data
INSERT INTO #tmpData (list, msgYear, msgCount)
select ml.list, year(creatStamp_), count(*)
from dbo.messages_ m 
inner join dbo.messageLists ml on m.listID = ml.listID and m.isVisible=1
inner join trialslyris1.dbo.lists_format lf on ml.list = lf.name COLLATE Latin1_General_CI_AI
	and lf.orgcode = @orgcode
group by lf.orgcode, ml.list, year(creatStamp_);

-- get min and max years in unpivoted data
select @minYear=min(msgYear), @maxYear=max(msgYear)
from #tmpData;

-- generate all years between min and max years as a string for pivot
with yearCTE as (
	select @minYear as yr
		union all
	select yr + 1
	from yearCTE
	where yr < @maxYear
)
select @yrList = COALESCE(@yrList + ',','') + quoteName(yr), 
	@yrListSelect = COALESCE(@yrListSelect + ',','') + 'isnull(pvt.' + quoteName(yr) + ',0) as ' + quoteName(yr)
from yearCTE
order by yr;

IF (@yrListSelect IS NOT NULL AND @yrListSelect != '') AND (@yrList IS NOT NULL AND @yrList != '')
BEGIN
	-- pivot the data
	set @pvtQry = 'select pvt.list, ' + @yrListSelect + ' 
		into ##tmpListPerYearExport
		from #tmpData as rawdata 
		PIVOT (sum(msgCount) for msgYear in (' + @yrList + ')) as pvt;';
	EXEC(@pvtQry);
	-- export it
	EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql='select * from ##tmpListPerYearExport order by 1, 2'
END
ELSE
BEGIN
	select list 
	into ##tmpListPerYearExport
	from #tmpData as rawdata ;
	
	EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql='select * from ##tmpListPerYearExport'
END

IF OBJECT_ID('tempdb..#tmpData') IS NOT NULL 
	DROP TABLE #tmpData;
IF OBJECT_ID('tempdb..##tmpListPerYearExport') IS NOT NULL 
	DROP TABLE ##tmpListPerYearExport;
GO
