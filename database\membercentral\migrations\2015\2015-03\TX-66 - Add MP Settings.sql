use membercentral;
GO

ALTER TABLE dbo.mp_profiles ADD
	maxFailedAutoAttempts int NULL,
	daysBetweenAutoAttempts int NULL
GO

ALTER PROCEDURE [dbo].[mp_insertProfile]
@siteID int,
@gatewayID int,
@profileName varchar(100),
@profileCode varchar(20),
@gatewayUsername varchar(50),
@gatewayPassword varchar(50),
@allowPayments bit,
@allowRefunds bit,
@allowRefundsFromAnyProfile bit,
@GLAccountID int,
@gatewayMerchantId varchar(50),
@allowPayInvoicesOnline bit,
@bankAccountName varchar(200),
@maxFailedAutoAttempts int,
@daysBetweenAutoAttempts int,
@profileID int OUTPUT

AS

SELECT @profileID = null

BEGIN TRAN

INSERT INTO dbo.mp_profiles (siteID, gatewayID, profileName, profileCode, gatewayUsername, 
	gatewayPassword, gatewayMerchantId, allowPayments, allowRefunds, allowRefundsFromAnyProfile, 
	GLAccountID, [status], allowPayInvoicesOnline, bankAccountName,maxFailedAutoAttempts,daysBetweenAutoAttempts)
VALUES (@siteID, @gatewayID, @profileName, @profileCode, @gatewayUsername, @gatewayPassword, 
	@gatewayMerchantId, @allowPayments, @allowRefunds, @allowRefundsFromAnyProfile, 
	nullif(@GLAccountID,0), 'A', @allowPayInvoicesOnline, @bankAccountName,nullif(@maxFailedAutoAttempts,0),nullif(@daysBetweenAutoAttempts,0))
	IF @@ERROR <> 0 GOTO on_error
	SELECT @profileID = SCOPE_IDENTITY()

IF @gatewayID = 11 BEGIN
	declare @rc int, @sysCreatedContentResourceTypeID int, @activeSiteResourceStatusID int,
		@PayInstrContentID int, @PayInstrSiteResourceID int
	select @sysCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('SystemCreatedContent')
		IF @@ERROR <> 0 GOTO on_error
	select @activeSiteResourceStatusID = dbo.fn_getResourceStatusId('Active')
		IF @@ERROR <> 0 GOTO on_error

	EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@sysCreatedContentResourceTypeID, 
		@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=1, 
		@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', @memberID=NULL,
		@contentID=@PayInstrContentID OUTPUT, 
		@siteResourceID=@PayInstrSiteResourceID OUTPUT
		IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

	UPDATE dbo.mp_profiles
	SET paymentInstructionsContentID = @PayInstrContentID
	WHERE profileID = @profileID
		IF @@ERROR <> 0 GOTO on_error
END

IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN	
	SELECT @profileID = 0
	RETURN -1
GO

ALTER TABLE dbo.ams_memberPaymentProfiles ADD
    nextAllowedAutoChargeDate datetime NULL,
    failedCount int NULL
GO

ALTER PROC [dbo].[tr_autoPayInvoices]
AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpInvoices') IS NOT NULL 
		DROP TABLE #tmpInvoices
	IF OBJECT_ID('tempdb..#tmpInvoiceOrgs') IS NOT NULL 
		DROP TABLE #tmpInvoiceOrgs
	IF OBJECT_ID('tempdb..#tmpInvoicePayments') IS NOT NULL 
		DROP TABLE #tmpInvoicePayments

	CREATE TABLE #tmpInvoices (invoiceID int, orgID int, invoiceProfileID int, memberPaymentProfileID int, itemUID uniqueidentifier, itemGroupUID uniqueidentifier)
	CREATE TABLE #tmpInvoiceOrgs (orgID int, itemGroupUID uniqueidentifier DEFAULT NEWID())
	CREATE TABLE #tmpInvoicePayments (invoiceProfileID int, memberPaymentProfileID int, itemUID uniqueidentifier DEFAULT NEWID())
	
     declare @now datetime
	set @now = getdate()

	declare @statusInserting int, @statusReady int
	select @statusInserting = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'payInvoices'
		and qs.queueStatus = 'insertingItems'
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'payInvoices'
		and qs.queueStatus = 'readyToProcess'

	-- get closed invoices due in the past, tied to member payment profile, amount due, inv prof auto pay enabled, not in the queue
	insert into #tmpInvoices (invoiceID, orgID, invoiceProfileID, memberPaymentProfileID)
	select i.invoiceID, o.orgID, i.invoiceProfileID, mpp.payProfileID
	from dbo.tr_invoices as i
	inner join dbo.ams_members as m on m.memberid = i.assignedToMemberID
	inner join dbo.organizations as o on o.orgID = m.orgID
	inner join dbo.tr_invoiceProfiles ip on ip.profileID = i.invoiceProfileID and ip.enableAutoPay = 1
	inner join dbo.ams_memberPaymentProfiles as mpp
	   on mpp.payProfileID = i.payProfileID
	   and (mpp.nextAllowedAutoChargeDate is null or mpp.nextAllowedAutoChargeDate < @now)
	inner join dbo.mp_profiles as mp 
	   on mp.profileID = mpp.profileID  
	   and (mpp.failedCount is null or mp.maxFailedAutoAttempts > mpp.failedCount)
	inner join dbo.tr_invoiceTransactions as it on it.invoiceID = i.invoiceID
	where i.statusID = 3
	and i.dateDue < getdate()
	and mpp.status = 'A'
	and mp.status = 'A'
	and mp.allowPayments = 1
	group by i.invoiceID, o.orgID, i.invoiceProfileID, mpp.payProfileID
	having sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) > 0
		except
	select qpid.invoiceID, qpi.orgID, qpi.invoiceProfileID, qpi.memberPaymentProfileID
	from platformQueue.dbo.tblQueueItems_payInvoicesDetail as qpid
	inner join platformQueue.dbo.tblQueueItems_payInvoices as qpi on qpi.itemUID = qpid.itemUID
	inner join platformQueue.dbo.tblQueueItems as qi on qi.itemUID = qpi.itemUID
	inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.queueStatusID
	where qs.queueStatus not in ('readyToNotify','grabbedForNotifying','done')

	-- each org should have its own itemGroupUID
	insert into #tmpInvoiceOrgs (orgID) 
	select distinct orgID from #tmpInvoices

	update i
	set i.itemGroupUID = iorg.itemGroupUID
	from #tmpInvoices as i
	inner join #tmpInvoiceOrgs as iorg on iorg.orgID = i.orgID

	-- each payment should have its own itemUID
	insert into #tmpInvoicePayments (invoiceProfileID, memberPaymentProfileID) 
	select distinct invoiceProfileID, memberPaymentProfileID from #tmpInvoices

	update i
	set i.itemUID = ipay.itemUID
	from #tmpInvoices as i
	inner join #tmpInvoicePayments as ipay on ipay.invoiceProfileID = i.invoiceProfileID and ipay.memberPaymentProfileID = i.memberPaymentProfileID

	-- get system account
	declare @enteredByMemberID int
	select @enteredByMemberID = memberID from membercentral.dbo.ams_members where memberNumber = 'SYSTEM' and orgID = 1

	-- queue items
	insert into platformQueue.dbo.tblQueueItems_payInvoices (itemUID, itemGroupUID, recordedByMemberID, orgID, invoiceProfileID, memberPaymentProfileID)
		OUTPUT inserted.itemUID, inserted.dateAdded, @statusInserting 
		INTO platformQueue.dbo.tblQueueItems(itemUID, dateAdded, queueStatusID)
	select distinct itemUID, itemGroupUID, @enteredByMemberID, orgID, invoiceProfileID, memberPaymentProfileID
	from #tmpInvoices

	insert into platformQueue.dbo.tblQueueItems_payInvoicesDetail (itemUID, invoiceID)
	select itemUID, invoiceID
	from #tmpInvoices

	-- update queue item groups to show ready to process
	update qi WITH (UPDLOCK, HOLDLOCK)
	set qi.queueStatusID = @statusReady,
		dateUpdated = getdate()
	from platformQueue.dbo.tblQueueItems as qi
	inner join #tmpInvoices as i on i.itemUID = qi.itemUID

	IF OBJECT_ID('tempdb..#tmpInvoices') IS NOT NULL 
		DROP TABLE #tmpInvoices
	IF OBJECT_ID('tempdb..#tmpInvoiceOrgs') IS NOT NULL 
		DROP TABLE #tmpInvoiceOrgs
	IF OBJECT_ID('tempdb..#tmpInvoicePayments') IS NOT NULL 
		DROP TABLE #tmpInvoicePayments

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

-- populate failedCount
update mpp2 set
    failedCount = temp.failedCount
from ams_memberPaymentProfiles mpp2
inner join(
    select mpp.payprofileID, count(*) as failedCount
    from ams_memberPaymentProfiles mpp
    inner join tr_paymentHistory ph
	   on mpp.payProfileID = ph.memberPaymentProfileID
	   and ph.datePaid >= mpp.failedSinceDate
	   and ph.isSuccess = 0
    group by mpp.payprofileID
) as temp
on temp.payprofileID = mpp2.payprofileID