-- Hotfix=8454616 Add missing universal roles
use memberCentral
GO
ALTER PROC [dbo].[cms_createSiteResourceRoleFunctionBulk]
@roleID int,
@resourceTypeFunctionIDList varchar(8000)

AS


declare @resourceTypeFunctionIDTable TABLE (resourceTypeFunctionID int)

insert into @resourceTypeFunctionIDTable (resourceTypeFunctionID)
select listitem
from dbo.fn_intListToTable(@resourceTypeFunctionIDList,',')
except
select resourceTypeFunctionID 
from dbo.cms_siteResourceRoleFunctions 
where roleID = @roleID


IF EXISTS (select * from @resourceTypeFunctionIDTable) BEGIN
	INSERT into dbo.cms_siteResourceRoleFunctions (roleID, resourceTypeFunctionID) 
	select @roleID, resourceTypeFunctionID
     from @resourceTypeFunctionIDTable
	IF @@ERROR <> 0 GOTO on_error


	if exists (select resourceRightsID from cms_siteResourceRights where roleID = @roleID)
	BEGIN
		IF OBJECT_ID('tempdb..#resourceRightsToUpdate') IS NOT NULL
			DROP TABLE #resourceRightsToUpdate
		CREATE TABLE #resourceRightsToUpdate (autoid int IDENTITY(1,1), siteResourceID int, resourceRightsID int, siteID int)


		DECLARE @currentAutoID int, @currentResourceRightID int, @currentSiteID int, @currentSiteResourceID int;

		-- update role assignments for sites that have prexisting resource(s) of this resourceType
		insert into #resourceRightsToUpdate (siteResourceID,resourceRightsID,siteID)
		select distinct sr.siteResourceID, srr.resourceRightsID, sr.siteID
		from cms_siteResourceRights srr
		inner join cms_siteResources sr
			on srr.resourceID = sr.siteResourceID
			and srr.roleID = @roleID
		inner join cms_siteResources existingResources
			on existingResources.siteID = sr.siteID
		inner join cms_siteResourceTypes srt
			on existingResources.resourceTypeID = srt.resourceTypeID
		inner join cms_siteResourceTypeFunctions srtf
			on srtf.resourceTypeID = srt.resourceTypeID
		inner join @resourceTypeFunctionIDTable temp
			on temp.resourceTypeFunctionID = srtf.resourceTypeFunctionID
			

		select 
			@currentAutoID = min(autoID)
		from #resourceRightsToUpdate

		while @currentAutoID is not null
		begin

			select 
				@currentResourceRightID = resourceRightsID,
				@currentSiteID = siteID,
				@currentSiteResourceID = siteResourceID
			from #resourceRightsToUpdate
			where autoID = @currentAutoID



			exec dbo.cache_perms_refreshSiteResourceRight
				@siteID = @currentSiteID, 
				@siteResourceID = @currentSiteResourceID,
				@resourceRightID = @currentResourceRightID

			select 
				@currentAutoID = min(autoID)
			from #resourceRightsToUpdate
			where autoID > @currentAutoID
		end

		IF OBJECT_ID('tempdb..#resourceRightsToUpdate') IS NOT NULL
			DROP TABLE #resourceRightsToUpdate

	END

END

RETURN 0

on_error:
	RETURN -1
GO


declare @tmpRoleFunctions table (autoid int identity(1,1), resourceType varchar(200), functionname varchar(200), roleID int , resourceTypeFunctionID int)

insert into @tmpRoleFunctions(resourceType, functionname,roleID, resourceTypeFunctionID)
select srt.resourceType, srf.functionname, 
	(select roleid from cms_siteResourceRoles where roleName = 'Super Administrator') as roleID, srtf.resourceTypeFunctionID
from cms_siteResourceTypeFunctions srtf
inner join cms_siteResourceTypes srt
  on srt.resourceTypeID = srtf.resourceTypeID
	and srt.resourceType in ('HistoryAdmin', 'NotesAdmin','RelationshipAdmin','VideoAlbum')
inner join cms_siteResourceFunctions srf
  on srf.functionID =srtf.functionID
left outer join cms_siteResourceRoleFunctions srrf
   inner join cms_siteResourceRoles srroles
  on srroles.roleID = srrf.roleID
on srtf.resourceTypeFunctionID = srrf.resourceTypeFunctionID
where srroles.roleName is null 
union 
select srt.resourceType, srf.functionname, 
	(select roleid from cms_siteResourceRoles where roleName = 'Site Administrator') as roleID, srtf.resourceTypeFunctionID
from cms_siteResourceTypeFunctions srtf
inner join cms_siteResourceTypes srt
  on srt.resourceTypeID = srtf.resourceTypeID
	and srt.resourceType in ('HistoryAdmin', 'NotesAdmin','RelationshipAdmin','VideoAlbum')
inner join cms_siteResourceFunctions srf
  on srf.functionID =srtf.functionID
left outer join cms_siteResourceRoleFunctions srrf
   inner join cms_siteResourceRoles srroles
  on srroles.roleID = srrf.roleID
on srtf.resourceTypeFunctionID = srrf.resourceTypeFunctionID
where srroles.roleName is null 

declare @rc int, @roleID int, @resourceTypeFunctionIDList varchar(8000)

select @roleID = min(roleID) from @tmpRoleFunctions
while @roleID is not null BEGIN
	select @resourceTypeFunctionIDList = dbo.sortedIntList(resourceTypeFunctionID)
	from @tmpRoleFunctions 
	where roleID = @roleID
     group by roleID
	
	print 'Creating roleid: ' + convert(varchar(20), @roleID) + ' functionidlist: ' + @resourceTypeFunctionIDList
	EXEC @rc = dbo.cms_createSiteResourceRoleFunctionBulk @roleID=@roleID, @resourceTypeFunctionIDList=@resourceTypeFunctionIDList

	select @roleID = min(roleID) from @tmpRoleFunctions where roleID > @roleID
END
