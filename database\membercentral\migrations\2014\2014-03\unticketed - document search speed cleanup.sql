use membercentral
GO

declare @resourceTypeID int

select @resourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedDocument')
declare @parentUpdates TABLE (siteResourceID int PRIMARY Key, parentSiteResourceID int)


insert into @parentUpdates (siteResourceID, parentSiteResourceID)
select distinct sr.siteResourceID, ai.siteResourceID as newParentSiteResourceID
from cms_documents d (nolock)
inner join cms_siteResources sr (nolock)
	on sr.siteResourceID = d.siteResourceID
	and sr.parentSiteResourceID is null
inner join dbo.store_ProductFormatsDocuments pfd (nolock)
	on pfd.documentID = d.documentID
inner join dbo.store_ProductFormats pf (nolock)
	on pf.formatID = pfd.formatID
inner join dbo.store_Products p (nolock)
	on p.itemID = pf.itemID
inner join dbo.store s (nolock)
	on s.storeID = p.storeID
inner join dbo.cms_applicationInstances ai (nolock)
	on ai.applicationInstanceID = s.applicationInstanceID
inner join dbo.cms_siteResources appResource (nolock)
	on appResource.siteResourceID = ai.siteResourceID
	and appResource.siteResourceStatusID in (1,2)

insert into @parentUpdates (siteResourceID, parentSiteResourceID)
select distinct sr.siteResourceID, ai.siteResourceID as newParentSiteResourceID
from cms_documents d (nolock)
inner join cms_siteResources sr (nolock)
	on sr.siteResourceID = d.siteResourceID
	and sr.parentSiteResourceID is null
inner join dbo.cms_siteResourceRights srr (nolock)
	on srr.resourceID = sr.siteResourceID
inner join dbo.cms_siteResources appSR (nolock)
	on appSR.siteResourceID = srr.inheritedRightsResourceID
	and appSR.siteResourceStatusID in (1,2)
inner join dbo.cms_applicationInstances ai (nolock)
	on ai.siteResourceID = appSR.siteResourceID
inner join dbo.fs_fileshare fs (nolock)
	on ai.applicationInstanceID = fs.applicationInstanceID


insert into @parentUpdates (siteResourceID, parentSiteResourceID)
select distinct sr.siteResourceID, toolSR.siteResourceID as newParentSiteResourceID
from cms_documents d
inner join cms_siteResources sr (nolock)
	on sr.siteResourceID = d.siteResourceID
	and sr.parentSiteResourceID is null
inner join dbo.ams_memberDataColumnValues mdcv (nolock)
	on mdcv.columnValueSiteResourceID = sr.siteResourceID
inner join dbo.cms_siteResources toolSR (nolock)
	on toolSR.siteID = d.siteID
inner join dbo.cms_siteResourceTypes srt (nolock)
	on srt.resourceTypeID = toolsr.resourceTypeID
	and srt.resourceType = 'MemberAdmin'


insert into @parentUpdates (siteResourceID, parentSiteResourceID)
select distinct sr.siteResourceID, toolSR.siteResourceID as newParentSiteResourceID
from cms_documents d (nolock)
inner join cms_siteResources sr (nolock)
	on sr.siteResourceID = d.siteResourceID
	and sr.parentSiteResourceID is null
inner join dbo.ams_memberDocuments mdocs (nolock)
	on mdocs.documentID = d.documentID
inner join dbo.cms_siteResources toolSR (nolock)
	on toolSR.siteID = d.siteID
inner join dbo.cms_siteResourceTypes srt (nolock)
	on srt.resourceTypeID = toolsr.resourceTypeID
	and srt.resourceType = 'MemberAdmin'

insert into @parentUpdates (siteResourceID, parentSiteResourceID)
select distinct sr.siteResourceID, toolSR.siteResourceID as newParentSiteResourceID
from cms_documents d (nolock)
inner join cms_siteResources sr (nolock)
	on sr.siteResourceID = d.siteResourceID
	and sr.parentSiteResourceID is null
inner join dbo.ref_panelDocuments pd (nolock)
	on pd.documentID = d.documentID
inner join dbo.cms_siteResources toolSR (nolock)
	on toolSR.siteID = d.siteID
inner join dbo.cms_siteResourceTypes srt (nolock)
	on srt.resourceTypeID = toolsr.resourceTypeID
	and srt.resourceType = 'ReferralsAdmin'


insert into @parentUpdates (siteResourceID, parentSiteResourceID)
select distinct sr.siteResourceID, customPageSR.siteResourceID as newParentSiteResourceID
from cms_documents d (nolock)
inner join cms_siteResources sr (nolock)
	on sr.siteResourceID = d.siteResourceID
	and sr.parentSiteResourceID is null
inner join dbo.cms_siteResourceRights srr (nolock)
	on srr.resourceID = sr.siteResourceID
inner join dbo.cms_siteResources customPageSR (nolock)
	on customPageSR.siteResourceID = srr.inheritedRightsResourceID
inner join dbo.cms_siteResourceTypes srt (nolock)
	on srt.resourceTypeID = customPageSR.resourceTypeID
	and srt.resourceType = 'CustomPage'
inner join cms_pages p (nolock)
	on p.siteResourceID = customPageSR.parentSiteResourceID

update sr set
	parentSiteResourceID = pu.parentSiteResourceID,
	resourceTypeID = @resourceTypeID
from @parentUpdates pu
inner join cms_siteResources sr
	on sr.siteResourceID = pu.siteResourceID


GO


create PROCEDURE dbo.up_documentVersions_addSearchStrings
AS
BEGIN
	SET NOCOUNT ON;

	declare @firstTime bit, @batchsize int, @batchIncrementSize int

	set @firstTime = 1
	set @batchsize = 2000
	set @batchIncrementSize =250

	IF OBJECT_ID('tempdb..#docUpdates') IS NOT NULL
		DROP TABLE #docUpdates
	create table #docUpdates (id int PRIMARY KEY, documentLanguageID int, documentID int)

	insert into #docUpdates (id)
	select top (@batchsize) sdv.id
	from search.dbo.cms_documentVersions sdv
	where sdv.documentID is null

	update du set 
		documentLanguageID=dl.documentLanguageID, documentID=dl.documentID
	from #docUpdates du
	inner join search.dbo.cms_documentVersions sdv
		on du.id = sdv.id
	inner join membercentral.dbo.cms_documentVersions dv
		on dv.documentVersionID = sdv.documentVersionID
		and sdv.documentID is null
	inner join membercentral.dbo.cms_documentLanguages dl
		on dv.documentLanguageID = dl.documentLanguageID



	while @firstTime = 1 or @@rowcount > 0
	BEGIN
		set @firstTime = 0

		update sdv set
			documentLanguageID = sdvu.documentLanguageID,
			documentID = sdvu.documentID,
			searchtext = isnull(searchtext,'') + ' |||mcsearchcodes||| ' +
			'mcsearchDocumentID' + cast(d.documentID as varchar(15)) + 'xxx' + ' ' +
			'mcsearchSiteID' + cast(s.siteID as varchar(15)) + 'xxx' + ' ' +
			'mcsearchSiteCode' + s.sitecode + 'xxx' + ' ' +
			'mcsearchSectionID' + cast(d.sectionID as varchar(15)) + 'xxx' + ' ' +
			'mcsearchResourceType' + srt.resourceType + 'xxx' + ' ' +
			case when parentsrt.resourceType is not null then 'mcsearchParentResourceType' + parentsrt.resourceType + 'xxx' + ' ' else '' end +
			case when parentsr.SiteResourceID is not null then 'mcsearchParentSiteResourceID' + cast(parentsr.SiteResourceID as varchar(15)) + 'xxx' + ' ' else '' end

		from (
			select top (@batchIncrementSize) du.id, du.documentLanguageID, du.documentID
			from #docUpdates du
			inner join search.dbo.cms_documentVersions sdv2
				on sdv2.id = du.id
				and sdv2.documentID is null
			) as sdvu

		inner join search.dbo.cms_documentVersions sdv
			on sdv.id = sdvu.id
		inner join membercentral.dbo.cms_documents d
			on d.documentID = sdvu.documentID
		inner join membercentral.dbo.sites s
			on s.siteID = d.siteID
		inner join membercentral.dbo.cms_siteResources sr
			on sr.siteResourceID = d.siteResourceID
		inner join membercentral.dbo.cms_siteResourceTypes srt
			on srt.resourceTypeID = sr.resourceTypeID
		left outer join membercentral.dbo.cms_siteResources parentsr
			inner join membercentral.dbo.cms_siteResourceTypes parentsrt
				on parentsrt.resourceTypeID = parentsr.resourceTypeID
		on sr.parentsiteResourceID = parentsr.siteResourceID
	END
	IF OBJECT_ID('tempdb..#docUpdates') IS NOT NULL
		DROP TABLE #docUpdates

END
GO