-- Hotfix-8445178 - incorrect number of affirmations on Order NY7780

declare
@orderID int,
@orderNumber varchar(50),
@memberID int,
@profileID int,
@xmlShippingInfo xml

set @orderID = 7780
set @memberID = 521763

declare @tblAffirms TABLE (autoid int IDENTITY(1,1), formatID int, offeringID int, numAffirmations int)
declare @tblAffirmsGenerated TABLE (formatID int, affirmationID int, affirmationCode char(10))
declare @minAutoID int, @formatID int, @offeringID int, @numAffirmations int, @dateIssued datetime, 
	@affirmationID int, @affirmationCode char(10), @rc int, @counter int

-- what affirmations to generate
insert into @tblAffirms (formatID, offeringID, numAffirmations)
select 
	sod.formatID, co.offeringID, sum (sod.quantity * spf.Quantity)  - isNull(aff.numAffirmations, 0)  as numAffirmations
from 
	dbo.store_orderDetails as sod
	inner join dbo.store_orders as so on so.orderID = sod.orderID
	inner join dbo.store as s on s.storeID = so.storeID
	inner join dbo.store_ProductFormats as spf on spf.formatID = sod.formatID
	inner join dbo.crd_offerings as co on co.productFormatID = spf.formatID
	INNER JOIN dbo.crd_statuses as cstat on cstat.statusID = co.statusID
	outer apply (
		select	productFormatID as formatID, offeringID, count (affirmationID) as numAffirmations
		from	crd_affirmations a
		where	a.orderID = so.orderID
				and  status = 'A'
				and a.productFormatID = sod.formatID
				and a.offeringID = co.offeringID
				--and a.issuedByMemberID = @memberID
		group by productFormatID, offeringID
	) aff
where 
	so.orderID = @orderID
	and s.offerAffirmations = 1
	and spf.offerAffirmations = 1
	and sod.quantity * spf.Quantity > 0
	and cstat.status in ('Pending','Approved')
	and getdate() between co.offeredStartDate and co.offeredEndDate
group by
	sod.formatID, co.offeringID, aff.numAffirmations
having
	(sum (sod.quantity * spf.Quantity)  - isNull(aff.numAffirmations, 0)) > 0
	IF @@ERROR <> 0 GOTO on_error

	select * from @tblAffirms
	
	-- issue affirmations
	select @dateIssued = getdate()
	select @minAutoID = min(autoid) from @tblAffirms
	while @minAutoID is not null BEGIN
		select @formatID = formatID, @offeringID = offeringID, @numAffirmations = numAffirmations
			from @tblAffirms
			where autoid = @minAutoID
			IF @@ERROR <> 0 GOTO on_error

		select @counter = 0
		while @counter < @numAffirmations begin

			print @counter
			EXEC @rc = dbo.crd_addAffirmation @dateIssued=@dateIssued, @issuedByMemberID=@memberID, 
				@orderID=@orderID, @productFormatID=@formatID, @offeringID=@offeringID, 
				@affirmationID=@affirmationID OUTPUT, @affirmationCode=@affirmationCode OUTPUT
				IF @@ERROR <> 0 or @rc <> 0 GOTO on_error

			INSERT into @tblAffirmsGenerated (formatID, affirmationID, affirmationCode)
			VALUES (@formatID, @affirmationID, @affirmationCode)
				IF @@ERROR <> 0 GOTO on_error
				
			set @counter = @counter + 1
		end

		select @minAutoID = min(autoid) from @tblAffirms where autoid > @minAutoID
	END
	
	
GOTO on_success

-- error exit
on_error:
	print 'error!'

on_success:
	print 'success!'

GO

ALTER PROC [dbo].[store_finalizeOrder]
@orderID int,
@orderNumber varchar(50),
@memberID int,
@profileID int,
@xmlShippingInfo xml

AS

declare @tblAffirms TABLE (autoid int IDENTITY(1,1), formatID int, offeringID int, numAffirmations int)
declare @tblAffirmsGenerated TABLE (formatID int, affirmationID int, affirmationCode char(10))
declare @minAutoID int, @formatID int, @offeringID int, @numAffirmations int, @dateIssued datetime, 
	@affirmationID int, @affirmationCode char(10), @rc int, @counter int

BEGIN TRAN

	UPDATE dbo.store_orders
	SET merchantProfileID = @profileID,
		xmlShippingInfo = @xmlShippingInfo,
		memberID = @memberID,
		orderCompleted = 1,
		orderStatusID = 1,
		totalProduct = 0,
		totalTax = 0,
		totalShipping = 0
	WHERE orderID = @orderID
		IF @@ERROR <> 0 GOTO on_error

	insert into dbo.store_OrderStatusLog (orderID, memberID, previousStatusID, currentStatusID)
	values(@orderID, @memberID, null, 1)
		IF @@ERROR <> 0 GOTO on_error

	update sd
	SET sd.ratePaid = r.rate
	FROM dbo.store_CartItems AS ci
	INNER JOIN dbo.store_Products AS p ON ci.productItemID = p.ItemID
	INNER JOIN dbo.store_ProductFormats AS f ON ci.formatID = f.formatID
	INNER JOIN dbo.store_rates AS r ON ci.rateid = r.rateid 				
	INNER JOIN dbo.store_Orders AS o ON o.orderNumber = ci.orderNumber
	INNER JOIN dbo.store_orderDetails AS sd ON sd.productItemID = p.itemID 
		and sd.formatId = f.formatId 
		and sd.rateid = r.rateid 
		and o.orderId = sd.orderID
	WHERE ci.orderNumber = @orderNumber
	and sd.ratePaid is null
		IF @@ERROR <> 0 GOTO on_error

	DELETE FROM dbo.store_cartItems
	WHERE orderNumber = @orderNumber
		IF @@ERROR <> 0 GOTO on_error

	-- what affirmations to generate
	insert into @tblAffirms (formatID, offeringID, numAffirmations)
	select 
		sod.formatID, co.offeringID, sum (sod.quantity * spf.Quantity)  - isNull(aff.numAffirmations, 0)  as numAffirmations
	from 
		dbo.store_orderDetails as sod
		inner join dbo.store_orders as so on so.orderID = sod.orderID
		inner join dbo.store as s on s.storeID = so.storeID
		inner join dbo.store_ProductFormats as spf on spf.formatID = sod.formatID
		inner join dbo.crd_offerings as co on co.productFormatID = spf.formatID
		INNER JOIN dbo.crd_statuses as cstat on cstat.statusID = co.statusID
		outer apply (
			select	productFormatID as formatID, offeringID, count (affirmationID) as numAffirmations
			from	crd_affirmations a
			where	a.orderID = so.orderID
					and  status = 'A'
					and a.productFormatID = sod.formatID
					and a.offeringID = co.offeringID
					and a.issuedByMemberID = @memberID
			group by productFormatID, offeringID
		) aff
	where 
		so.orderID = @orderID
		and s.offerAffirmations = 1
		and spf.offerAffirmations = 1
		and sod.quantity * spf.Quantity > 0
		and cstat.status in ('Pending','Approved')
		and getdate() between co.offeredStartDate and co.offeredEndDate
	group by
		sod.formatID, co.offeringID, aff.numAffirmations
	having
		(sum (sod.quantity * spf.Quantity)  - isNull(aff.numAffirmations, 0)) > 0
	IF @@ERROR <> 0 GOTO on_error
	
	-- issue affirmations
	select @dateIssued = getdate()
	select @minAutoID = min(autoid) from @tblAffirms
	while @minAutoID is not null BEGIN
		select @formatID = formatID, @offeringID = offeringID, @numAffirmations = numAffirmations
			from @tblAffirms
			where autoid = @minAutoID
			IF @@ERROR <> 0 GOTO on_error

		select @counter = 0
		while @counter < @numAffirmations begin
			EXEC @rc = dbo.crd_addAffirmation @dateIssued=@dateIssued, @issuedByMemberID=@memberID, 
				@orderID=@orderID, @productFormatID=@formatID, @offeringID=@offeringID, 
				@affirmationID=@affirmationID OUTPUT, @affirmationCode=@affirmationCode OUTPUT
				IF @@ERROR <> 0 or @rc <> 0 GOTO on_error

			INSERT into @tblAffirmsGenerated (formatID, affirmationID, affirmationCode)
			VALUES (@formatID, @affirmationID, @affirmationCode)
				IF @@ERROR <> 0 GOTO on_error
				
			select @counter = @counter + 1
		end

		select @minAutoID = min(autoid) from @tblAffirms where autoid > @minAutoID
	END
	
-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO



