ALTER PROC dbo.ev_deleteSponsorGrouping
@sponsorGroupingID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF NOT EXISTS (select sponsorGroupingID from dbo.ev_sponsorGrouping where sponsorGroupingID=@sponsorGroupingID)
		RAISERROR('Sponsor Grouping does not exist.',16,1);

	-- Check if any sponsors are using this grouping
	IF EXISTS (select sponsorUsageID from dbo.sponsorsUsage where sponsorGroupingID=@sponsorGroupingID)
		RAISERROR('Cannot delete sponsor grouping that has sponsors assigned to it.',16,1);

	DECLARE @registrationID int;
	SELECT @registrationID = registrationID from dbo.ev_sponsorGrouping where sponsorGroupingID=@sponsorGroupingID;

	DELETE FROM dbo.ev_sponsorGrouping WHERE sponsorGroupingID = @sponsorGroupingID;

	-- Reorder remaining groupings
	EXEC dbo.ev_reorderSponsorGroupings @registrationID=@registrationID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROL<PERSON><PERSON><PERSON><PERSON> TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
