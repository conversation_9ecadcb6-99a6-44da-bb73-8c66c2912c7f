-- CA-7 update presidents club firm custom field

USE [customApps]
GO

CREATE PROCEDURE [dbo].[ca_updatePresidentClubRecords] 
AS

SET NOCOUNT ON


declare 
	@orgID int, 
	@siteID int, 
	@sitecode varchar(5), 
	@subscriptionUID uniqueIdentifier,
	@customFieldUID uniqueIdentifier,
	@childRecordTypeName varchar(100),
	@thisMemberID int,
	@thisValue varchar(100),
	@columnID int,
	@rc int,
	@columnName varchar(100)

declare @membersToChange TABLE (memberID int PRIMARY KEY, newValue varchar(100))


set @sitecode = 'CA'
set @subscriptionUID = '0506985B-66C4-4BCB-B6E4-0043E620B676' -- President's Club Member
set @customFieldUID = '89320CD6-3868-4451-ACB8-C009D67758D5' -- Presidents Club Firm
set @childRecordTypeName = 'Individual'


select @siteID = s.siteID, @orgID = s.orgID
from membercentral.dbo.sites s where sitecode = @sitecode

select @columnID = columnID, @columnName = columnName
from membercentral.dbo.ams_memberDataColumns
where uid = @customFieldUID

insert into @membersToChange (memberID, newValue)
select childAM.memberID, ltrim(rtrim(replace(r.rateName, 'Renewal','')))
from membercentral.dbo.ams_members childAM WITH(NOLOCK)
inner join membercentral.dbo.ams_recordTypes rt WITH(NOLOCK)
	on rt.recordTypeID = childAM.recordTypeID
	and rt.recordTypeName = @childRecordTypeName
	and childAM.status in ('A','I')
	and childAM.orgID = @orgID
inner join membercentral.dbo.ams_recordRelationships rr WITH(NOLOCK)
	on rr.childMemberID = childAM.memberID
	and rr.isActive = 1
inner join membercentral.dbo.ams_members as masterAM WITH(NOLOCK)
	on masterAM.memberID = rr.masterMemberID
	and masterAM.status in ('A','I')
inner join membercentral.dbo.ams_members as masterM WITH(NOLOCK)
	on masterM.activememberID = masterAM.memberID
inner join membercentral.dbo.sub_subscribers ss WITH(NOLOCK)
	on ss.memberID =masterM.memberID
inner join membercentral.dbo.sub_subscriptions subs WITH(NOLOCK)
	on subs.subscriptionID = ss.subscriptionID
	and subs.uid = @subscriptionUID
inner join membercentral.dbo.sub_rateFrequencies as rf 
	on rf.rfid = ss.rfid
inner join membercentral.dbo.sub_rates as r 
	on r.rateID = rf.rateID 
inner join membercentral.dbo.sub_statuses as st WITH(NOLOCK)
	on st.statusID = ss.statusID
	and st.statusCode = 'A'
left outer join membercentral.dbo.ams_memberData as md WITH(NOLOCK)
	inner join membercentral.dbo.ams_memberDataColumnValues as mdcv WITH(NOLOCK)
		on mdcv.valueID = md.valueID
		and mdcv.columnValueBit = 1
	inner join membercentral.dbo.ams_memberDataColumns as mdc WITH(NOLOCK)
		on mdc.columnID = mdcv.columnID
		and mdc.orgID = @orgID
		and mdc.uid = @customFieldUID
on md.memberID = childAM.memberID
inner join membercentral.dbo.vw_memberdata_ca md2
	on md2.memberID = childAM.memberID
where mdcv.valueID is null
group by childAM.memberID, rateName
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- does not have active sub, but child record has wrong value
insert into @membersToChange (memberID, newValue)
select childAM.memberID, ''
from membercentral.dbo.ams_members childAM WITH(NOLOCK)
inner join membercentral.dbo.ams_recordTypes rt WITH(NOLOCK)
	on rt.recordTypeID = childAM.recordTypeID
	and rt.recordTypeName = @childRecordTypeName
	and childAM.status in ('A','I')
left outer join membercentral.dbo.ams_recordRelationships rr WITH(NOLOCK)
	inner join membercentral.dbo.ams_members as masterAM WITH(NOLOCK)
		on masterAM.memberID = rr.masterMemberID
		and masterAM.status in ('A','I')
	inner join membercentral.dbo.ams_members as masterM WITH(NOLOCK)
		on masterM.activememberID = masterAM.memberID
	inner join membercentral.dbo.sub_subscribers ss WITH(NOLOCK)
		on ss.memberID =masterM.memberID
	inner join membercentral.dbo.sub_subscriptions subs WITH(NOLOCK)
		on subs.subscriptionID = ss.subscriptionID
		and subs.uid = @subscriptionUID
	inner join membercentral.dbo.sub_statuses as st WITH(NOLOCK)
		on st.statusID = ss.statusID
		and st.statusCode = 'A'
on rr.childMemberID = childAM.memberID
and rr.isActive = 1
inner join membercentral.dbo.ams_memberData as md WITH(NOLOCK)
	on md.memberID = childAM.memberID
inner join membercentral.dbo.ams_memberDataColumnValues as mdcv WITH(NOLOCK)
	on mdcv.valueID = md.valueID
	and mdcv.columnValueBit = 1
inner join membercentral.dbo.ams_memberDataColumns as mdc WITH(NOLOCK) on mdc.columnID = mdcv.columnID
	and mdc.orgID = @orgID
	and mdc.uid = @customFieldUID
where masterAM.memberID is null
group by childAM.memberID
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


IF EXISTS (select top 1 memberid from @membersToChange) BEGIN
	select @thisMemberID = min(memberid) from @membersToChange
	while @thisMemberID is not null BEGIN
		select @thisValue=newValue
		from @membersToChange 
		where memberID = @thisMemberID
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

		EXEC @rc = membercentral.dbo.ams_setMemberData @memberID=@thisMemberID, @orgID=@orgID, @columnName=@columnName, @columnValue=@thisValue, @bypassQueue=1
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

		select @thisMemberID = min(memberid) from @membersToChange where memberID > @thisMemberID
	END

	-- set member as updated
	UPDATE m
	SET m.dateLastUpdated = getdate()
	from membercentral.dbo.ams_members as m
	inner join @membersToChange as tmp on tmp.memberID = m.memberID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- queue processing of member groups (@runSchedule=2 indicates delayed processing) 
	DECLARE @itemGroupUID uniqueidentifier, @memberIDList varchar(max), @conditionIDList varchar(max)
	SELECT @memberIDList = COALESCE(@memberIDList + ',', '') + cast(MemberID as varchar(10)) 
		from @membersToChange 
		group by MemberID
	SELECT @conditionIDList = COALESCE(@conditionIDList + ',', '') + cast(c.conditionID as varchar(10)) 
		from @membersToChange as sd
		inner join membercentral.dbo.ams_virtualGroupConditions as c on c.fieldcode = 'md_' + cast(@columnID as varchar(10))
		group by c.conditionID
	IF @memberIDList is not null or @conditionIDList is not null BEGIN
		EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList=@memberIDList, @conditionIDList=@conditionIDList, @runSchedule=2, @itemGroupUID=@itemGroupUID OUTPUT
			IF @@ERROR <> 0 GOTO on_error
	END
	
END

GOTO on_done


on_error:

on_done:

RETURN 0





ALTER PROC [dbo].[job_runDailyCustomJobs]
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SELECT @smtpserver = smtpserver, @tier = tier from membercentral.dbo.fn_getServerSettings()


/* ********************** */
/* tn_updateOldestBarDate */
/* ********************** */
BEGIN TRY
	EXEC customApps.dbo.tn_updateOldestBarDate
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Oldest Bar Date - TN'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************** */
/* ky_updateOldestBarDate */
/* ********************** */
BEGIN TRY
	EXEC customApps.dbo.ky_updateOldestBarDate
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Oldest Bar Date - KY'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ***************** */
/* MEMBER JOIN DATES */
/* ***************** */
BEGIN TRY
	declare @mjd_udid int, @mjd_error_code int, @mjd_emsg varchar(70)
	select @mjd_udid = min(udid) from customApps.dbo.schedTask_memberJoinDates where isActive = 1
	while @mjd_udid is not null BEGIN
		EXEC customApps.dbo.job_memberJoinDates @udid=@mjd_udid, @error_code=@mjd_error_code OUTPUT
		IF @mjd_error_code <> 0 BEGIN
			select @mjd_emsg = 'UDID ' + cast(@mjd_udid as varchar(10)) + ' was not successful. Error ' + cast(@mjd_error_code as varchar(10)) + '.'
			RAISERROR(@mjd_emsg,16,1)
		END
		select @mjd_udid = min(udid) from customApps.dbo.schedTask_memberJoinDates where isActive = 1 and udid > @mjd_udid
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Member Join Dates'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ************************ */
/* caaa_updateHomeChapterRO */
/* ************************ */
BEGIN TRY
	EXEC customApps.dbo.caaa_updateHomeChapterRO
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Home Chapter - CAAA'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* *********************** */
/* caaa_checkInvoice90Days */
/* *********************** */
BEGIN TRY
	EXEC customApps.dbo.caaa_checkInvoice90Days
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running 90 Day Invoice Check - CAAA'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ************************************ */
/* ny_updateCLECreditsSinceLastBirthday */
/* ************************************ */
BEGIN TRY
	EXEC customApps.dbo.ny_updateCLECreditsSinceLastBirthday
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating CLE Credit Since Last Birthday - NY'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* *************************** */
/* sdcba_updateGeographicAreas */
/* *************************** */
BEGIN TRY
	exec customApps.dbo.sdcba_updateGeographicAreas
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Geographic Areas - SDCBA'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ******************************* */
/* sdcba_updateSpecialDesignations */
/* ******************************* */
BEGIN TRY
	exec customApps.dbo.sdcba_updateSpecialDesignations
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Special Designations - SDCBA'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************* */
/* ky_syncListMemberData */
/* ********************* */
BEGIN TRY
	EXEC customApps.dbo.ky_syncListMemberData
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Lyris Member Data - KY'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************************* */
/* lo_updateAffiliateIndivualRecords */
/* ********************************* */
BEGIN TRY
	EXEC customApps.dbo.lo_updateAffiliateIndivualRecords
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Active Affiliate Individual Records - LO'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* *************** */
/* tx_updateRegion */
/* *************** */
BEGIN TRY
	EXEC customApps.dbo.tx_updateRegion
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Region - TX'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ***************************** */
/* ca_updatePresidentClubRecords */
/* ***************************** */
BEGIN TRY
	EXEC customApps.dbo.ca_updatePresidentClubRecords
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating President Club Records - CA'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

RETURN 0
