declare @orgcode varchar(10), 
	@orgID int, 
	@companyRecordTypeID int, 
	@PACERecordTypeID int,
	@VendorRecordTypeID int,
	@personRecordTypeID int,
	@relationshipTypeID int,
	@trashID int

set @orgcode = 'TAGD'
select @orgID = orgID from dbo.organizations where orgcode = @orgcode

-- Create Record Types
exec dbo.ams_createRecordType @orgID=@orgID, @recordTypeCode='Company', @recordTypeName='Company', @recordTypeDesc='Company', @isPerson=0, @isOrganization=1, @recordTypeID=@companyRecordTypeID OUTPUT
exec dbo.ams_createRecordType @orgID=@orgID, @recordTypeCode='PACEProvider', @recordTypeName='PACE Provider', @recordTypeDesc='PACE Provider', @isPerson=0, @isOrganization=1, @recordTypeID=@PACERecordTypeID OUTPUT
exec dbo.ams_createRecordType @orgID=@orgID, @recordTypeCode='Vendor', @recordTypeName='Vendor', @recordTypeDesc='Vendor', @isPerson=0, @isOrganization=1, @recordTypeID=@VendorRecordTypeID OUTPUT
exec dbo.ams_createRecordType @orgID=@orgID, @recordTypeCode='Individual', @recordTypeName='Individual', @recordTypeDesc='Individual', @isPerson=1, @isOrganization=0, @recordTypeID=@personRecordTypeID OUTPUT

-- Create Record Relationship Types
exec dbo.ams_createRecordRelationShipType @orgID=@orgID, @relationshipTypeCode='Dentist', @relationshipTypeName='Dentist', @relationshipTypeID=@relationshipTypeID OUTPUT
exec dbo.ams_createRecordTypesRelationshipTypes @relationshipTypeID=@relationshipTypeID, @masterRecordTypeID=@companyRecordTypeID, @childRecordTypeID=@personRecordTypeID, @isActive=1, @recordTypeRelationshipTypeID=@trashID OUTPUT

exec dbo.ams_createRecordRelationShipType @orgID=@orgID, @relationshipTypeCode='DentalStaff', @relationshipTypeName='Dental Staff', @relationshipTypeID=@relationshipTypeID OUTPUT
exec dbo.ams_createRecordTypesRelationshipTypes @relationshipTypeID=@relationshipTypeID, @masterRecordTypeID=@companyRecordTypeID, @childRecordTypeID=@personRecordTypeID, @isActive=1, @recordTypeRelationshipTypeID=@trashID OUTPUT

exec dbo.ams_createRecordRelationShipType @orgID=@orgID, @relationshipTypeCode='MainContact', @relationshipTypeName='Main Contact', @relationshipTypeID=@relationshipTypeID OUTPUT
exec dbo.ams_createRecordTypesRelationshipTypes @relationshipTypeID=@relationshipTypeID, @masterRecordTypeID=@PACERecordTypeID, @childRecordTypeID=@personRecordTypeID, @isActive=1, @recordTypeRelationshipTypeID=@trashID OUTPUT
exec dbo.ams_createRecordTypesRelationshipTypes @relationshipTypeID=@relationshipTypeID, @masterRecordTypeID=@VendorRecordTypeID, @childRecordTypeID=@personRecordTypeID, @isActive=1, @recordTypeRelationshipTypeID=@trashID OUTPUT

exec dbo.ams_createRecordRelationShipType @orgID=@orgID, @relationshipTypeCode='Representative', @relationshipTypeName='Representative', @relationshipTypeID=@relationshipTypeID OUTPUT
exec dbo.ams_createRecordTypesRelationshipTypes @relationshipTypeID=@relationshipTypeID, @masterRecordTypeID=@PACERecordTypeID, @childRecordTypeID=@personRecordTypeID, @isActive=1, @recordTypeRelationshipTypeID=@trashID OUTPUT
exec dbo.ams_createRecordTypesRelationshipTypes @relationshipTypeID=@relationshipTypeID, @masterRecordTypeID=@VendorRecordTypeID, @childRecordTypeID=@personRecordTypeID, @isActive=1, @recordTypeRelationshipTypeID=@trashID OUTPUT
GO