use membercentral
GO
DROP VIEW vw_memberData_OJI
GO

declare @siteID int
select @siteID = siteID from dbo.sites where sitecode = 'OJI'
update dbo.cms_pages set redirectID = null where redirectID in (select redirectID FROM dbo.siteRedirects where siteID = @siteID)
update dbo.cms_documents set redirectID = null where redirectID in (select redirectID FROM dbo.siteRedirects where siteID = @siteID)
DELETE FROM dbo.siteRedirects where siteID = @siteID
GO

declare @siteID int
select @siteID = siteID from dbo.sites where sitecode = 'OJI'
DELETE FROM dbo.fs_fileShare where applicationInstanceID in (select applicationInstanceID from dbo.cms_applicationInstances where siteID = @siteID)
GO

declare @siteID int
select @siteID = siteID from dbo.sites where sitecode = 'OJI'
DELETE FROM dbo.ams_memberDirectorySorts where memberDirectoryID in (select memberdirectoryID FROM dbo.ams_memberDirectories where applicationInstanceID in (select applicationInstanceID from dbo.cms_applicationInstances where siteID = @siteID))
DELETE FROM dbo.ams_memberDirectories where applicationInstance<PERSON> in (select applicationInstanceID from dbo.cms_applicationInstances where siteID = @siteID)
GO

declare @siteID int
select @siteID = siteID from dbo.sites where sitecode = 'OJI'
DELETE FROM dbo.ev_categories where calendarID in (select calendarID FROM dbo.ev_calendars where applicationInstanceID in (select applicationInstanceID from dbo.cms_applicationInstances where siteID = @siteID))
DELETE FROM dbo.ev_calendars where applicationInstanceID in (select applicationInstanceID from dbo.cms_applicationInstances where siteID = @siteID)
GO

declare @siteID int
select @siteID = siteID from dbo.sites where sitecode = 'OJI'
UPDATE dbo.pg_albums set featuredPhotoID = null where galleryID in (select galleryID FROM dbo.pg_gallery where applicationInstanceID in (select applicationInstanceID from dbo.cms_applicationInstances where siteID = @siteID))
UPDATE dbo.pg_gallery set rootAlbumID = null where applicationInstanceID in (select applicationInstanceID from dbo.cms_applicationInstances where siteID = @siteID)
DELETE FROM dbo.pg_albums where galleryID in (select galleryID FROM dbo.pg_gallery where applicationInstanceID in (select applicationInstanceID from dbo.cms_applicationInstances where siteID = @siteID))
DELETE FROM dbo.pg_gallery where applicationInstanceID in (select applicationInstanceID from dbo.cms_applicationInstances where siteID = @siteID)
GO

declare @siteID int
select @siteID = siteID from dbo.sites where sitecode = 'OJI'
UPDATE dbo.vg_albums set featuredVideoID = null where galleryID in (select galleryID FROM dbo.vg_gallery where applicationInstanceID in (select applicationInstanceID from dbo.cms_applicationInstances where siteID = @siteID))
UPDATE dbo.vg_gallery set rootAlbumID = null where applicationInstanceID in (select applicationInstanceID from dbo.cms_applicationInstances where siteID = @siteID)
DELETE FROM dbo.vg_albums where galleryID in (select galleryID FROM dbo.vg_gallery where applicationInstanceID in (select applicationInstanceID from dbo.cms_applicationInstances where siteID = @siteID))
DELETE FROM dbo.vg_gallery where applicationInstanceID in (select applicationInstanceID from dbo.cms_applicationInstances where siteID = @siteID)
GO

declare @siteID int
select @siteID = siteID from dbo.sites where sitecode = 'OJI'
DELETE FROM dbo.lists_listViewer where applicationInstanceID in (select applicationInstanceID from dbo.cms_applicationInstances where siteID = @siteID)
GO

declare @siteID int
select @siteID = siteID from dbo.sites where sitecode = 'OJI'
DELETE FROM dbo.cms_customPages where appInstanceID in (select applicationInstanceID from dbo.cms_applicationInstances where siteID = @siteID)
GO

declare @siteID int
select @siteID = siteID from dbo.sites where sitecode = 'OJI'
DELETE FROM dbo.cms_applicationInstances where siteID = @siteID
GO

declare @siteID int
select @siteID = siteID from dbo.sites where sitecode = 'OJI'
DELETE FROM dbo.cms_pageZonesResources where pageID in (select pageID FROM dbo.cms_pages where siteID = @siteID)
DELETE FROM dbo.cms_pageLanguages where pageID in (select pageID FROM dbo.cms_pages where siteID = @siteID)
DELETE FROM dbo.cms_pages where siteID = @siteID
GO

declare @siteID int
select @siteID = siteID from dbo.sites where sitecode = 'OJI'
DELETE FROM dbo.cache_cms_derivedPageSectionSettings where siteID = @siteID
DELETE FROM dbo.cms_pageTemplatesModesZones where templateID in (select templateID FROM dbo.cms_pageTemplates where siteID = @siteID)
UPDATE dbo.cms_pageSections set ovTemplateID = null where siteID = @siteID
DELETE FROM dbo.cms_pageTemplates where siteID = @siteID
GO

declare @siteID int
select @siteID = siteID from dbo.sites where sitecode = 'OJI'
DELETE FROM dbo.cms_documentVersions where documentLanguageID in (select documentLanguageID FROM dbo.cms_documentLanguages where documentID in (select documentID FROM dbo.cms_documents where siteID = @siteID))
DELETE FROM dbo.cms_documentLanguages where documentID in (select documentID FROM dbo.cms_documents where siteID = @siteID)
DELETE FROM dbo.cms_documents where siteID = @siteID
GO

declare @siteID int
select @siteID = siteID from dbo.sites where sitecode = 'OJI'
UPDATE cms_pageSections set parentSectionID = null where siteID = @siteID
DELETE FROM dbo.cms_pageSectionsZonesResources where sectionID in (select sectionID FROM dbo.cms_pageSections where siteID = @siteID)
DELETE FROM dbo.cache_cms_recursivePageSections where siteID = @siteID
DELETE FROM dbo.cms_pageSections where siteID = @siteID
GO

declare @siteID int
select @siteID = siteID from dbo.sites where sitecode = 'OJI'
delete from dbo.cache_perms_groupPrintsRightPrints where rightPrintID in (select rightPrintID from dbo.cache_perms_rightPrints where siteID = @siteID)
delete from dbo.cache_perms_siteResourceFunctionRightPrints where rightPrintID in (select rightPrintID from dbo.cache_perms_rightPrints where siteID = @siteID)
delete from dbo.cache_perms_rightPrintsAndGroups where rightPrintID in (select rightPrintID from dbo.cache_perms_rightPrints where siteID = @siteID)
delete from dbo.cache_perms_rightPrints where siteID = @siteID
GO

declare @siteID int
select @siteID = siteID from dbo.sites where sitecode = 'OJI'
DELETE FROM dbo.ams_memberNetworkProfiles where siteID = @siteID
GO

declare @siteID int
select @siteID = siteID from dbo.sites where sitecode = 'OJI'
DELETE FROM dbo.ams_memberFieldUsage where fieldSetID in (SELECT fieldSetID FROM dbo.ams_memberFieldSets where siteID = @siteID)
DELETE FROM dbo.ams_memberFields where fieldSetID in (SELECT fieldSetID FROM dbo.ams_memberFieldSets where siteID = @siteID)
DELETE FROM dbo.ams_memberFieldSets where siteID = @siteID
GO

declare @siteID int
select @siteID = siteID from dbo.sites where sitecode = 'OJI'
DELETE FROM dbo.cms_contentVersions where contentLanguageID in (select contentLanguageID FROM dbo.cms_contentLanguages where contentID in (SELECT contentID FROM dbo.cms_content where siteID = @siteID))
DELETE FROM dbo.cms_contentLanguages where contentID in (SELECT contentID FROM dbo.cms_content where siteID = @siteID)
UPDATE dbo.sites set noRightsContentID = null, inactiveUserContentID=null, siteAgreementContentID=null, welcomeMessageContentID=null, noRightsNotLoggedInContentID=null, firstTimeLoginContentID=null where siteID = @siteID
DELETE FROM dbo.cms_content where siteID = @siteID
GO

declare @siteID int
select @siteID = siteID from dbo.sites where sitecode = 'OJI'
DELETE FROM dbo.cms_categorySiteResources where categoryID in (select categoryID FROM dbo.cms_categories where categoryTreeID in (select categoryTreeID FROM dbo.cms_categoryTrees where siteID = @siteID))
DELETE FROM dbo.cms_categories where categoryTreeID in (select categoryTreeID FROM dbo.cms_categoryTrees where siteID = @siteID)
DELETE FROM dbo.cms_categoryTrees where siteID = @siteID
GO

declare @siteID int
select @siteID = siteID from dbo.sites where sitecode = 'OJI'
DELETE FROM dbo.lists_lists where siteResourceID in (select siteResourceID FROM dbo.cms_siteResources where siteID = @siteID)
GO

declare @siteID int
select @siteID = siteID from dbo.sites where sitecode = 'OJI'
UPDATE dbo.cms_siteResources set parentSiteResourceID = null where siteID = @siteID
DELETE FROM dbo.cms_siteResourceRightsCache where resourceID in (select siteResourceID FROM dbo.cms_siteResources where siteID = @siteID)
DELETE FROM dbo.cms_siteResourceRights where resourceID in (select siteResourceID FROM dbo.cms_siteResources where siteID = @siteID)
DELETE FROM dbo.cms_siteResources where siteID = @siteID
GO

declare @siteID int
select @siteID = siteID from dbo.sites where sitecode = 'OJI'
DELETE FROM dbo.siteHostnames where siteID = @siteID
GO

declare @siteID int
select @siteID = siteID from dbo.sites where sitecode = 'OJI'
DELETE FROM dbo.siteLanguages where siteID = @siteID
GO

declare @siteID int
select @siteID = siteID from dbo.sites where sitecode = 'OJI'
DELETE FROM dbo.networkSites where siteID = @siteID
GO

declare @siteID int
select @siteID = siteID from dbo.sites where sitecode = 'OJI'
DELETE FROM dbo.admin_siteTools where siteID = @siteID
GO

declare @siteID int
select @siteID = siteID from dbo.sites where sitecode = 'OJI'
DELETE FROM dbo.ams_memberSiteDefaults where siteID = @siteID
GO

DELETE FROm dbo.sites where siteCode = 'OJI'
GO

declare @orgID int
select @orgID = orgID from dbo.organizations where orgcode = 'OJI'
DELETE FROM dbo.cache_ams_recursiveGroups where orgID = @orgID
DELETE FROM dbo.cache_perms_groupPrintsAndGroups where groupID in (select groupID FROM dbo.ams_groups where orgID = @orgID)
DELETE FROM dbo.cache_members_groups where orgID = @orgID
DELETE FROM dbo.ams_memberGroups where groupID in (select groupID FROM dbo.ams_groups where orgID = @orgID)
DELETE FROM dbo.ams_groups where orgID = @orgID
GO

declare @orgID int
select @orgID = orgID from dbo.organizations where orgcode = 'OJI'
delete from dbo.ams_memberPhones where addressID in (select addressID from dbo.ams_memberAddresses where addressTypeID in (select addressTypeID from dbo.ams_memberAddressTypes where orgID = @orgID))
delete from dbo.ams_memberAddresses where addressTypeID in (select addressTypeID from dbo.ams_memberAddressTypes where orgID = @orgID)
update dbo.ams_members set billingAddressTypeID = (select top 1 addressTypeID from dbo.ams_memberAddressTypes) where orgID = @orgID
delete from dbo.ams_memberAddressTypes where orgID = @orgID
delete from dbo.ams_memberPhoneTypes where orgID = @orgID
GO

declare @orgID int
select @orgID = orgID from dbo.organizations where orgcode = 'OJI'
delete from dbo.ams_memberEmails where emailTypeID in (select emailTypeID from dbo.ams_memberEmailTypes where orgID = @orgID)
delete from dbo.ams_memberEmailTypes where orgID = @orgID
GO

declare @orgID int
select @orgID = orgID from dbo.organizations where orgcode = 'OJI'
delete from dbo.ams_memberWebsites where websiteTypeID in (select websiteTypeID from dbo.ams_memberWebsiteTypes where orgID = @orgID)
delete from dbo.ams_memberWebsiteTypes where orgID = @orgID
GO

declare @orgID int
select @orgID = orgID from dbo.organizations where orgcode = 'OJI'
DELETE FROM dbo.cache_members_conditions where memberID in (select memberID FROM dbo.ams_members where orgID = @orgID)
GO

declare @orgID int
select @orgID = orgID from dbo.organizations where orgcode = 'OJI'
DELETE FROM dbo.ams_members where orgID = @orgID 
GO

declare @orgID int
select @orgID = orgID from dbo.organizations where orgcode = 'OJI'
DELETE FROM dbo.cache_perms_groupPrints where orgID = @orgID 
GO

declare @orgID int
select @orgID = orgID from dbo.organizations where orgcode = 'OJI'
DELETE FROM dbo.tr_batches where orgID = @orgID 
GO

declare @orgID int
select @orgID = orgID from dbo.organizations where orgcode = 'OJI'
DELETE FROM dbo.tr_glaccounts where orgID = @orgID 
GO

declare @orgID int
select @orgID = orgID from dbo.organizations where orgcode = 'OJI'
DELETE FROM dbo.crd_sponsors where orgID = @orgID 
GO

declare @orgID int
select @orgID = orgID from dbo.organizations where orgcode = 'OJI'
DELETE FROM dbo.tr_invoiceProfiles where orgID = @orgID 
GO

DELETE FROM dbo.organizations where orgCode = 'OJI'
GO

use platformStats
GO
delete from dbo.ams_memberLogins
where memberID not in (select memberID from membercentral.dbo.ams_members)
GO
