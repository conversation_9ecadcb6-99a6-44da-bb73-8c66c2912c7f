ALTER PROC dbo.ts_problemSitesReport

AS

	DECLARE @messageContent VARCHAR(MAX), @errorSubject VARCHAR(200);	
	DECLARE @tlas TABLE (orgcode VARCHAR(10));
	DECLARE @sitesMissing TABLE (siteCode VARCHAR(10), hasUpgradeTrialSmith BIT, publicUpgradeTrialSmith BIT,
		hasJoinTrialSmith BIT, publicJoinTrialSmith BIT, hasUploadDocuments BIT, publicUploadDocuments BIT);

	INSERT INTO @tlas (orgcode)
	SELECT DISTINCT association_
	FROM trialslyris1.dbo.members_
	WHERE list_ = 'trialsmith' 
	AND membertype_ = 'normal';

	INSERT INTO @sitesMissing (siteCode, hasUpgradeTrialSmith, publicUpgradeTrialSmith, has<PERSON>oin<PERSON>rial<PERSON>mith, publicJoinTrialSmith, hasUploadDocuments, publicUploadDocuments)
	SELECT s.siteCode, 0, 0, 0, 0, 0, 0
	FROM @tlas tla
	INNER JOIN membercentral.membercentral.dbo.sites s ON s.sitecode = tla.orgcode COLLATE Latin1_General_CI_AI;

	UPDATE sm
	SET sm.hasUpgradeTrialSmith = CASE WHEN p.pageID IS NOT NULL THEN 1 ELSE 0 END,
		sm.publicUpgradeTrialSmith = CASE WHEN srr.resourceID IS NOT NULL THEN 1 ELSE 0 END
	FROM @sitesMissing AS sm
	INNER JOIN membercentral.membercentral.dbo.sites s ON s.sitecode = sm.siteCode COLLATE Latin1_General_CI_AI
	INNER JOIN membercentral.membercentral.dbo.ams_groups g ON g.orgID = s.orgID AND g.groupCode = 'public'
	LEFT OUTER JOIN membercentral.membercentral.dbo.cms_pages p
		INNER JOIN membercentral.membercentral.dbo.cms_siteResources sr ON sr.siteResourceID = p.siteResourceID AND sr.siteResourceStatusID = 1
		INNER JOIN membercentral.membercentral.dbo.cms_siteResources appsr ON appsr.parentSiteResourceID = sr.siteResourceID AND appsr.siteResourceStatusID = 1
		ON p.siteID = s.siteID AND p.pagename='upgradeTrialsmith'
	LEFT OUTER JOIN membercentral.membercentral.dbo.cms_siteResourceRights srr ON srr.resourceID = appsr.siteresourceID AND srr.functionID = 4 AND srr.groupID = g.groupID;

	UPDATE sm
	SET sm.hasJoinTrialSmith = CASE WHEN p.pageID IS NOT NULL THEN 1 ELSE 0 END,
		sm.publicJoinTrialSmith = CASE WHEN srr.resourceID IS NOT NULL THEN 1 ELSE 0 END
	FROM @sitesMissing AS sm
	INNER JOIN membercentral.membercentral.dbo.sites s ON s.sitecode = sm.siteCode COLLATE Latin1_General_CI_AI
	INNER JOIN membercentral.membercentral.dbo.ams_groups g ON g.orgID = s.orgID AND g.groupCode = 'public'
	LEFT OUTER JOIN membercentral.membercentral.dbo.cms_pages p
		INNER JOIN membercentral.membercentral.dbo.cms_siteResources sr ON sr.siteResourceID = p.siteResourceID AND sr.siteResourceStatusID = 1
		INNER JOIN membercentral.membercentral.dbo.cms_siteResources appsr ON appsr.parentSiteResourceID = sr.siteResourceID AND appsr.siteResourceStatusID = 1
		ON p.siteID = s.siteID AND p.pagename='joinTrialsmith'
	LEFT OUTER JOIN membercentral.membercentral.dbo.cms_siteResourceRights srr ON srr.resourceID = appsr.siteresourceID AND srr.functionID = 4 AND srr.groupID = g.groupID;

	UPDATE sm
	SET sm.hasUploadDocuments = CASE WHEN p.pageID IS NOT NULL THEN 1 ELSE 0 END,
		sm.publicUploadDocuments = CASE WHEN srr.resourceID IS NOT NULL THEN 1 ELSE 0 END
	FROM @sitesMissing AS sm
	INNER JOIN membercentral.membercentral.dbo.sites s ON s.sitecode = sm.siteCode COLLATE Latin1_General_CI_AI
	INNER JOIN membercentral.membercentral.dbo.ams_groups g ON g.orgID = s.orgID AND g.groupCode = 'public'
	LEFT OUTER JOIN membercentral.membercentral.dbo.cms_pages p
		INNER JOIN membercentral.membercentral.dbo.cms_siteResources sr ON sr.siteResourceID = p.siteResourceID AND sr.siteResourceStatusID = 1
		INNER JOIN membercentral.membercentral.dbo.cms_siteResources appsr ON appsr.parentSiteResourceID = sr.siteResourceID AND appsr.siteResourceStatusID = 1
		ON p.siteID = s.siteID AND p.pagename='uploadDocuments'
	LEFT OUTER JOIN membercentral.membercentral.dbo.cms_siteResourceRights srr ON srr.resourceID = appsr.siteresourceID AND srr.functionID = 4 AND srr.groupID = g.groupID;

	-- remove sites where things are setup correctly
	DELETE FROM @sitesMissing
	WHERE hasUpgradeTrialSmith = 1
	AND publicUpgradeTrialSmith = 1
	AND hasJoinTrialSmith = 1
	AND publicJoinTrialSmith = 1
	AND hasUploadDocuments = 1
	AND publicUploadDocuments = 1;

	SET @messageContent = '<table>' + 
		replace(replace(replace((
			SELECT 
			'|r' +'SiteCode: '+ sitecode + 
			'|d' + 'hasUpgradeTrialSmith: ' +  CASE WHEN hasUpgradeTrialSmith = 1 THEN 'True' ELSE 'False' END + 
			'|d' + 'publicUpgradeTrialSmith: ' +  CASE WHEN publicUpgradeTrialSmith = 1 THEN 'True' ELSE 'False' END + 
			'|d' + 'hasJoinTrialSmith: ' +  CASE WHEN hasJoinTrialSmith = 1 THEN 'True' ELSE 'False' END + 
			'|d' + 'publicJoinTrialSmith: ' +  CASE WHEN publicJoinTrialSmith = 1 THEN 'True' ELSE 'False' END + 
			'|d' + 'hasUploadDocuments: ' +  CASE WHEN hasUploadDocuments = 1 THEN 'True' ELSE 'False' END + 
			'|d' + 'publicUploadDocuments: ' +  CASE WHEN publicUploadDocuments = 1 THEN 'True' ELSE 'False' END + '|x' AS [text()]
			from @sitesMissing
			order by sitecode
			FOR XML PATH ('')
		),'|r','<tr><td>'),'|d','</td><td>'),'|x','</td></tr>') + '</table>';
	
	IF LEN(@messageContent) > 1 BEGIN
		SET @errorSubject = 'Missing applications in TrialSmith';
		EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@messageContent, @forDev=0;
	END

RETURN 0
GO
