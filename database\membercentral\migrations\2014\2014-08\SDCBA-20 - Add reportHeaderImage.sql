use membercentral
GO

-- Add settings node to existing admin instances
update cms_applicationInstances
set settingsXML = replace(cast(settingsXML as varchar(max)),'</settings>','<setting name="reportHeaderImage" value="gif" /></settings>')
where applicationInstanceName = 'admin'
GO

ALTER PROCEDURE [dbo].[cms_createApplicationInstanceAdmin]
	@siteid int,
	@languageID int,
	@sectionID int,
	@isVisible bit,
	@pageName varchar(50),
	@pageTitle varchar(200),
	@pagedesc varchar(400),
	@zoneID int,
	@pageTemplateID int,
	@pageModeID int,
	@pgResourceTypeID int,
	@pgParentResourceID int = null,
	@allowReturnAfterLogin bit,
	@applicationInstanceName varchar(100),
	@applicationInstanceDesc varchar(200),
	@applicationInstanceID int OUTPUT,
	@siteResourceID int OUTPUT,
	@pageID int OUTPUT
AS

-- null OUTPUT vars
SELECT @applicationInstanceID = null, @siteResourceID = null, @pageID = null

DECLARE @appCreatedSectionResourceTypeID int, @applicationTypeID int, @rootSectionID int, @rc int
DECLARE @documentSectionName varchar(50)

select @appCreatedSectionResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedSection')
select @applicationTypeID = applicationTypeID from cms_applicationTypes where applicationTypeName = 'Admin'
	
BEGIN TRAN

exec @rc = dbo.cms_createApplicationInstance
		@siteid = @siteid,
		@languageID = @languageID,
		@sectionID = @sectionID,
		@applicationTypeID = @applicationTypeID,
		@isVisible = @isVisible,
		@pageName = @pageName,
		@pageTitle = @pageTitle,
		@pagedesc = @pagedesc,
		@zoneID = @zoneID,
		@pageTemplateID = @pageTemplateID,
		@pageModeID = @pageModeID,
		@pgResourceTypeID = @pgResourceTypeID,
		@pgParentResourceID = @pgParentResourceID,
		@allowReturnAfterLogin = @allowReturnAfterLogin,
		@applicationInstanceName = @applicationInstanceName,
		@applicationInstanceDesc = @applicationInstanceDesc,
		@applicationInstanceID = @applicationInstanceID OUTPUT,
		@siteResourceID = @siteResourceID OUTPUT,
		@pageID = @pageID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

select @documentSectionName = 'MemberDocument'

exec @rc = dbo.cms_createPageSection
		@siteID = @siteID, 
		@sectionResourceTypeID = @appCreatedSectionResourceTypeID, 
		@ovTemplateID = NULL,
		@ovTemplateIDMobile=NULL,
		@ovModeID = NULL, 
		@parentSectionID = @sectionID, 
		@sectionName = @documentSectionName, 
		@sectionCode = @documentSectionName,
		@inheritPlacements = 1,
		@sectionID = @rootSectionID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- update parentSiteResourceID of section
UPDATE sr
SET sr.parentSiteResourceID = @siteResourceID
FROM cms_pageSections s
INNER JOIN cms_siteResources sr on s.siteResourceID = sr.siteResourceID
	AND s.sectionID = @rootSectionID
	IF @@ERROR <> 0 GOTO on_error

-- update settings xml
UPDATE dbo.cms_applicationInstances
SET settingsXML = '<settings><setting name="memberDocumentSectionID" value="' + cast(@rootSectionID as varchar(8)) + + '" /><setting name="showMemberDocuments" value="false" /><setting name="showNotes" value="false" /><setting name="showRelationships" value="false" /><setting name="showMemberHistory" value="false" /><setting name="showSubscriptions" value="false" /><setting name="showReferrals" value="false" /><setting name="showApptTracker" value="false" /><setting name="showMemberPhotosInSearchResults" value="true" /><setting name="numPerPageInSearchResults" value="25" /><setting name="reportHeaderImage" value="gif" /></settings>'
WHERE applicationInstanceID = @applicationInstanceID
	IF @@ERROR <> 0 GOTO on_error

-- create the suite of tools
EXEC @rc = dbo.createAdminSuite @siteID
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- create admin member lookup fieldset
declare @memberAdminSRID int, @fieldSetID int, @fieldID int, @useID int
select @memberAdminSRID = dbo.fn_getSiteResourceIDForResourceType('MemberAdmin',@siteID)
EXEC @rc = dbo.ams_createMemberFieldSet @siteID=@siteID, @fieldsetName='Member Admin Search Form', @nameformat='LSXPFM', @showHelp=0, @fieldsetID=@fieldSetID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldSetID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_firstname', @fieldLabel='First Name', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_lastname', @fieldLabel='Last Name', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_company', @fieldLabel='Company', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_membernumber', @fieldLabel='Member Number', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = ams_createMemberFieldUsage @siteResourceID=@memberAdminSRID, @fieldsetID=@fieldSetID, @area='search', @createSiteResourceID=0, @useID=@useID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @useID = 0 GOTO on_error

EXEC @rc = dbo.ams_createMemberFieldSet @siteID=@siteID, @fieldsetName='Member Admin Search Results', @nameformat='LSXPFM', @showHelp=0, @fieldsetID=@fieldSetID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldSetID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_prefix', @fieldLabel='Prefix', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_middlename', @fieldLabel='Middle Name', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = dbo.ams_createMemberField @fieldsetID=@fieldSetID, @fieldCode='m_suffix', @fieldLabel='Suffix', @fieldDescription='', @isRequired=0, @fieldID=@fieldID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @fieldID = 0 GOTO on_error
EXEC @rc = ams_createMemberFieldUsage @siteResourceID=@memberAdminSRID, @fieldsetID=@fieldSetID, @area='results', @createSiteResourceID=0, @useID=@useID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @useID = 0 GOTO on_error

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO


