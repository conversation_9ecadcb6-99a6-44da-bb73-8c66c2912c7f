ALTER PROC dbo.mc_updateListMembershipFromQueue
@itemID int

AS

BEGIN TRY

	DECLARE @siteID int, @orgID int, @orgCode varchar(10), @siteCode varchar(10), @listName varchar(100),
		@queueTypeID int, @readyToProcessStatusID int, @processingStatusID int,	@progressLog VARCHAR(max),
		@errorLog VARCHAR(max), @emailSubject VARCHAR(500), @emailTitle VARCHAR(300), @escalateError BIT,
		@errmsg nvarchar(2048), @severity tinyint, @state tinyint, @errno INT, @proc sysname, @lineno INT, 
		@defaultMembertype VARCHAR(100), @defaultSubType VARCHAR(100), @defaultMCOption_keepActive BIT, 
		@defaultMCOption_lockAddress BIT, @defaultMCOption_lockName BIT, @isAutoManageActive BIT, @message VARCHAR(500),
		@lastrowcount INT, @thisListOneWayList BIT, @expireDateCutoff DATETIME, @runByMemberID INT;

	SET @escalateError = 0;
	SET @errorLog = '';
	SET @defaultMembertype = 'normal';
	SET @defaultSubType = 'mail';
	SET @defaultMCOption_keepActive = 0;
	SET @defaultMCOption_lockAddress = 0;
	SET @defaultMCOption_lockName = 0;
	SET @expireDateCutoff = DATEADD(year,-1,GETDATE());

	SELECT @queueTypeID = queueTypeID
	FROM membercentral.platformQueue.dbo.tblQueueTypes
	WHERE queueType = 'lyrisListSync';

	SELECT @readyToProcessStatusID = queueStatusID
	FROM membercentral.platformQueue.dbo.tblQueueStatuses
	WHERE queueTypeID = @queueTypeID
	AND queueStatus = 'readyToProcess';

	SELECT @processingStatusID = queueStatusID
	FROM membercentral.platformQueue.dbo.tblQueueStatuses
	WHERE queueTypeID = @queueTypeID
	AND queueStatus = 'processingItem';

	SELECT @siteCode = siteCode, @listName = listName
	FROM membercentral.platformQueue.dbo.queue_lyrisListSync
	WHERE itemID = @itemID
	AND statusID = @readyToProcessStatusID;

	IF @listName IS NULL
		GOTO on_end;

	UPDATE membercentral.platformQueue.dbo.queue_lyrisListSync
	SET statusID = @processingStatusID,
		dateUpdated = GETDATE()
	WHERE itemID = @itemID;

	EXEC membercentral.membercentral.dbo.lists_getListMembersForLyris @siteCode=@siteCode, @listName=@listName;

	SELECT @siteID = siteID, @orgID = orgID, @orgCode = orgCode, @isAutoManageActive = isAutoManageActive
	FROM membercentral.datatransfer.dbo.ListsForLyris
	WHERE siteCode = @siteCode
	AND list_ = @listName;

	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;
	CREATE TABLE #tmpLogMessages (autoid INT IDENTITY(1,1), siteID INT, memberID INT, listName VARCHAR(100), msg VARCHAR(500));

	DELETE FROM dbo.MC_ListMembersForLyris WHERE sitecode = @sitecode and list_ = @listName;
	
	-- process only if list still exists in Lyris
	IF EXISTS (SELECT 1 FROM dbo.lists_ WHERE name_ = @listName) BEGIN
		
		INSERT INTO dbo.MC_ListMembersForLyris (siteID, siteCode, orgID, orgCode, list_, externalMemberID, MCMemberID, emailaddr_, fullname_, 
			functionName, isAutoManageActive, domain_, usernameLC_)
		SELECT siteID, siteCode, orgID, orgCode, list_, externalMemberID, MCMemberID, emailaddr_, fullname_, 
			functionName, @isAutoManageActive, RIGHT(emailaddr_,LEN(emailaddr_)-CHARINDEX('@',emailaddr_)), 
			LEFT(emailaddr_,CHARINDEX('@',emailaddr_)-1)
		FROM membercentral.datatransfer.dbo.ListMembersForLyris
		WHERE siteCode = @siteCode
		AND list_ = @listName;

		-- null blank emails
		UPDATE dbo.MC_ListMembersForLyris
		SET emailaddr_ = NULL
		WHERE ltrim(rtrim(ISNULL(emailaddr_,''))) = ''
		AND sitecode = @sitecode
		AND list_ = @listName;

		IF EXISTS (SELECT adminSend_ FROM dbo.lists_ WHERE name_ = @listName AND adminSend_ = 'T')
			SET @thisListOneWayList = 1;
		ELSE
			SET @thisListOneWayList = 0;

		SET @message = convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Start Processing List changes - AutoManage: ' + cast(@isAutoManageActive AS VARCHAR(5));
		SET @progressLog = @progressLog + '<br/>' + @message;

		/* ************ */
		/* UPDATE NAMES */
		/* ************ */
		-- mark rows with names to update, except when lockName is 1
		BEGIN TRY
			SET @message = convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Finding names to update';
			SET @progressLog = @progressLog + '<br/>' + @message;

			UPDATE lm
			SET updateStatus = 'UpdateName'
			FROM (
				SELECT min(autoID) AS autoID 
				FROM dbo.MC_ListMembersForLyris 
				WHERE list_ = @listName 
				AND functionName in ('managePopulation','manageStatus') 
				GROUP BY emailaddr_
			) AS deduped
			INNER JOIN dbo.MC_ListMembersForLyris lm ON deduped.autoID = lm.autoID
			INNER JOIN dbo.members_ m ON lm.list_ = m.list_
				AND lm.list_ = @listName
				AND lm.emailaddr_ IS NOT NULL
				AND lm.externalMemberID = m.externalMemberID
				AND lm.fullname_ <> m.fullname_
				AND (m.MCOption_lockName IS NULL or m.MCOption_lockName=0);

			SET @lastrowcount = @@rowcount;

			IF @lastrowcount > 0 AND @isAutoManageActive = 1 BEGIN
				INSERT INTO #tmpLogMessages (siteID, memberID, listName, msg)
				SELECT lm.siteID, lm.MCMemberID, lm.list_, 'Name changed FROM ['+ m.fullname_ +'] to ['+ lm.fullname_ +'] for [' + m.emailaddr_ + '].'
				FROM dbo.MC_ListMembersForLyris lm
				INNER JOIN dbo.members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
					AND lm.list_ = @listName
					AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
					AND lm.updateStatus = 'UpdateName'
					AND (m.MCOption_lockName IS NULL or m.MCOption_lockName=0);

				-- update the full names that have been marked
				UPDATE m 
				SET m.fullname_ = lm.fullname_
				FROM dbo.MC_ListMembersForLyris lm
				INNER JOIN dbo.members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
					AND lm.list_ = @listName
					AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
					AND lm.updateStatus = 'UpdateName'
					AND (m.MCOption_lockName IS NULL or m.MCOption_lockName=0);

				SET @lastrowcount = @@rowcount;
				SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Updated names - Records: ' + cast(@lastrowcount AS VARCHAR(10));
				SET @progressLog = @progressLog + '<br/>' + @message;
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		/* ********************** */
		/* UPDATE EMAILS - PASS 1 */
		/* ********************** */
		-- mark rows with email addresses to update, regardless of status when lockAddress is 0
		BEGIN TRY
			SET @message = convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Finding records to update (Pass 1)';
			SET @progressLog = @progressLog + '<br/>' + @message;

			UPDATE lm
			SET updateStatus = CASE WHEN updatestatus = 'UpdateName' THEN 'UpdateNameAndEmail' ELSE 'UpdateEmail' end
			FROM (
				SELECT min(autoID) AS autoID 
				FROM dbo.MC_ListMembersForLyris 
				WHERE list_ = @listName 
				AND functionName in ('managePopulation','manageStatus') 
				GROUP BY emailaddr_
			) AS deduped
			INNER JOIN dbo.MC_ListMembersForLyris lm ON deduped.autoID = lm.autoID
			INNER JOIN dbo.members_ m ON lm.list_ = m.list_
				AND lm.list_ = @listName
				AND lm.emailaddr_ IS NOT NULL
				AND lm.externalMemberID = m.externalMemberID
				AND lm.emailaddr_ <> m.emailaddr_
				AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0)
			LEFT OUTER JOIN dbo.members_ existingAddresses ON lm.list_ = existingAddresses.list_
				AND (
					(lm.emailaddr_ = existingAddresses.emailaddr_)
					or (existingAddresses.domain_ = lm.domain_ AND existingAddresses.usernamelc_ = lm.usernamelc_)
				)
			WHERE existingAddresses.memberID_ IS NULL;

			SET @lastrowcount = @@rowcount;

			IF @lastrowcount > 0 BEGIN
				UPDATE lm2 
				SET lm2.updateStatus = 'updateSkipped-targetsMultipleListMemberships'
				FROM dbo.MC_ListMembersForLyris lm2
				INNER JOIN (
					SELECT lm.emailaddr_, lm.list_
					FROM dbo.MC_ListMembersForLyris lm
					INNER JOIN dbo.members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
						AND lm.list_ = @listName
						AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
						AND lm.updateStatus in ('UpdateEmail','UpdateNameAndEmail')
						AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0)
					GROUP BY lm.emailaddr_, lm.list_
					HAVING count(*) > 1
				) AS temp ON lm2.emailaddr_ = temp.emailaddr_
					AND lm2.list_ = temp.list_
					AND lm2.updateStatus in ('UpdateEmail','UpdateNameAndEmail');

				IF @isAutoManageActive = 1 BEGIN
					INSERT INTO #tmpLogMessages (siteID, memberID, listName, msg)
					SELECT lm.siteID, lm.MCMemberID, lm.list_, 'Email changed FROM ['+ m.emailaddr_ +'] to ['+ lm.emailaddr_ +'].'
					FROM dbo.MC_ListMembersForLyris lm
					INNER JOIN dbo.members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
						AND lm.list_ = @listName
						AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
						AND lm.updateStatus in ('UpdateEmail','UpdateNameAndEmail')
						AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0);

					-- update the email addresses that have been marked
					UPDATE m 
					SET m.domain_ = CASE WHEN lm.domain_ IS NULL THEN m.domain_ ELSE lm.domain_ END,
						m.emailaddr_ = CASE WHEN lm.emailaddr_ IS NULL THEN m.emailaddr_ ELSE lm.emailaddr_ END,
						m.usernameLc_ = CASE WHEN lm.usernamelc_ IS NULL THEN m.usernameLc_ ELSE lm.usernamelc_ END,
						MCEmailKey = convert(varchar(75),HASHBYTES('SHA2_256',@listName + '|' + isnull(lm.usernamelc_,m.usernameLc_) collate SQL_Latin1_General_CP1_CI_AS + '@' + isnull(lm.domain_,m.domain_) collate SQL_Latin1_General_CP1_CI_AS),2),
						MCEmailKey_usernameLC = CASE WHEN lm.usernamelc_ IS NULL THEN m.usernameLc_ ELSE lm.usernamelc_ END,
						MCEmailKey_domain = CASE WHEN lm.domain_ IS NULL THEN m.domain_ ELSE lm.domain_ END
					FROM dbo.MC_ListMembersForLyris lm
					INNER JOIN dbo.members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
						AND lm.list_ = @listName
						AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
						AND lm.updateStatus in ('UpdateEmail','UpdateNameAndEmail')
						AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0);

					SET @lastrowcount = @@rowcount;
					SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Updated email addresses (Pass 1) - Records: ' + cast(@lastrowcount AS VARCHAR(10));
					SET @progressLog = @progressLog + '<br/>' + @message;
				END
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		/* ********************** */
		/* UPDATE EMAILS - PASS 2 */
		/* ********************** */
		-- repeat to allow updating addresses that were already in use before the last step 
		-- mark rows with email addresses to update, regardless of status when lockAddress is 0
		BEGIN TRY
			SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Finding records to update (Pass 2)';
			SET @progressLog = @progressLog + '<br/>' + @message;

			UPDATE lm
			SET lm.updateStatus = CASE WHEN updatestatus = 'UpdateName' THEN 'UpdateNameAndEmail1' ELSE 'UpdateEmail1' end
			FROM (
				SELECT min(autoID) AS autoID 
				FROM MC_ListMembersForLyris 
				WHERE list_ = @listName 
				AND updateStatus IS NULL 
				AND functionName in ('managePopulation','manageStatus') 
				GROUP BY emailaddr_
			) AS deduped
			INNER JOIN MC_ListMembersForLyris lm ON deduped.autoID = lm.autoID
			INNER JOIN dbo.members_ m ON lm.list_ = m.list_
				AND lm.list_ = @listName
				AND lm.externalMemberID = m.externalMemberID
				AND lm.emailaddr_ IS NOT NULL
				AND lm.emailaddr_ <> m.emailaddr_
				AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0);

			SET @lastrowcount = @@rowcount;

			IF @lastrowcount > 0 BEGIN
				UPDATE lm2 
				SET lm2.updateStatus = 'updateSkipped-emailAddressAlreadyOnList'
				FROM dbo.MC_ListMembersForLyris lm2
				INNER JOIN dbo.members_ existingAddresses ON lm2.list_ = existingAddresses.list_
					AND (
						(lm2.emailaddr_ = existingAddresses.emailaddr_)
						or (existingAddresses.domain_ = lm2.domain_ AND existingAddresses.usernamelc_ = lm2.usernamelc_)
					)
				WHERE lm2.list_ = @listName
				AND lm2.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1');

				UPDATE lm2 
				SET lm2.updateStatus = 'updateSkipped-targetsMultipleListMemberships'
				FROM MC_ListMembersForLyris lm2
				INNER JOIN (
					SELECT lm.emailaddr_, lm.list_
					FROM MC_ListMembersForLyris lm
					INNER JOIN dbo.members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
						AND lm.list_ = @listName
						AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
						AND lm.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1')
						AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0)
					GROUP BY lm.emailaddr_, lm.list_
					HAVING count(*) > 1
				) AS temp ON lm2.emailaddr_ = temp.emailaddr_
					AND lm2.list_ = temp.list_
					AND lm2.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1');

				IF @isAutoManageActive = 1
				BEGIN
					INSERT INTO #tmpLogMessages (siteID, memberID, listName, msg)
					SELECT lm.siteID, lm.MCMemberID, lm.list_, 'Email changed FROM ['+ m.emailaddr_ +'] to ['+ lm.emailaddr_ +'].'
					FROM dbo.MC_ListMembersForLyris lm
					INNER JOIN dbo.members_ m ON lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
						AND m.list_ = @listName
						AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
						AND lm.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1')
						AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0);

					-- update the email addresses that have been marked
					UPDATE m 
					SET m.domain_ = CASE WHEN lm.domain_ IS NULL THEN m.domain_ ELSE lm.domain_ END,
						m.emailaddr_ = CASE WHEN lm.emailaddr_ IS NULL THEN m.emailaddr_ ELSE lm.emailaddr_ END,
						m.usernameLc_ = CASE WHEN lm.usernamelc_ IS NULL THEN m.usernameLc_ ELSE lm.usernamelc_ END,
						MCEmailKey = convert(varchar(75),HASHBYTES('SHA2_256',@listName + '|' + isnull(lm.usernamelc_,m.usernameLc_) collate SQL_Latin1_General_CP1_CI_AS + '@' + isnull(lm.domain_,m.domain_) collate SQL_Latin1_General_CP1_CI_AS),2),
						MCEmailKey_usernameLC = CASE WHEN lm.usernamelc_ IS NULL THEN m.usernameLc_ ELSE lm.usernamelc_ END,
						MCEmailKey_domain = CASE WHEN lm.domain_ IS NULL THEN m.domain_ ELSE lm.domain_ END
					FROM dbo.MC_ListMembersForLyris lm
					INNER JOIN dbo.members_ m 
						on lm.list_ = m.list_ COLLATE Latin1_General_CI_AI
						AND m.list_ = @listName
						AND lm.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
						AND lm.updateStatus in ('UpdateEmail1','UpdateNameAndEmail1')
						AND (m.MCOption_lockAddress IS NULL or m.MCOption_lockAddress=0);
					
					SET @lastrowcount = @@rowcount;
					SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Updated email addresses (Pass 2) - Records: ' + cast(@lastrowcount AS VARCHAR(10));
					SET @progressLog = @progressLog + '<br/>' + @message;
				END
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- expire members that are no longer active (unless keepActive is true)
		BEGIN TRY
			SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Finding memberships to expire';
			SET @progressLog = @progressLog + '<br/>' + @message;

			INSERT INTO dbo.MC_ListMembersForLyris (siteID, siteCode, orgID, orgCode, list_, externalMemberID, emailaddr_, 
				fullname_, functionName,updateStatus,isAutoManageActive)
			SELECT @siteID, @siteCode, @orgID, @orgcode, m.list_, m.externalMemberID, m.emailaddr_, m.fullname_, 
				NULL AS functionName, 'expired' AS updateStatus, @isAutoManageActive
			FROM dbo.members_ m
			LEFT OUTER JOIN MC_ListMembersForLyris lm ON m.externalMemberID = lm.externalMemberID COLLATE Latin1_General_CI_AI
				AND m.list_ = lm.list_
			WHERE m.list_ = @listName
			AND m.membertype_ in ('confirm','held','normal')
			AND ltrim(rtrim(ISNULL(m.externalMemberID,''))) <> ''
			AND lm.autoID IS NULL AND (m.MCOption_keepActive IS NULL or m.MCOption_keepActive=0);

			SET @lastrowcount = @@rowcount;

			IF @lastrowcount > 0 AND @isAutoManageActive = 1 BEGIN
				UPDATE m 
				SET m.membertype_ = 'expired',
					m.ExpireDate_ = GETDATE()
				FROM MC_ListMembersForLyris lm
				INNER JOIN dbo.members_ m ON lm.list_ = m.list_
					AND m.list_ = @listName
					AND lm.externalMemberID = m.externalMemberID
					AND lm.updateStatus = 'expired';

				SET @lastrowcount = @@rowcount;
				SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Expired members - Records: ' + cast(@lastrowcount AS VARCHAR(10));
				SET @progressLog = @progressLog + '<br/>' + @message;
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- reactivate expired members that are now active
		BEGIN TRY
			SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Finding reactivations';
			SET @progressLog = @progressLog + '<br/>' + @message;

			UPDATE lm 
			SET lm.updateStatus = 'reactivate'
			FROM dbo.members_ m
			INNER JOIN MC_ListMembersForLyris lm ON m.externalMemberID = lm.externalMemberID
				AND ltrim(rtrim(ISNULL(m.externalMemberID,''))) <> ''
				AND m.list_ = lm.list_
				AND m.list_ = @listName
				AND m.membertype_ = 'expired'
				AND lm.functionName in ('managePopulation','manageStatus');

			SET @lastrowcount = @@rowcount;

			IF @lastrowcount > 0 AND @isAutoManageActive = 1 BEGIN
				INSERT INTO #tmpLogMessages (siteID, memberID, listName, msg)
				SELECT lm.siteID, lm.MCMemberID, lm.list_, 'Reactivated membership for [' + m.emailaddr_ + '].'
				FROM MC_ListMembersForLyris lm
				INNER JOIN dbo.members_ m ON lm.list_ = m.list_
					AND m.list_ = @listName
					AND lm.externalMemberID = m.externalMemberID
					AND lm.updateStatus = 'reactivate';

				UPDATE m 
				SET m.membertype_ = 'normal',
					m.ExpireDate_ = NULL
				FROM MC_ListMembersForLyris lm
				INNER JOIN dbo.members_ m ON lm.list_ = m.list_
					AND m.list_ = @listName
					AND lm.externalMemberID = m.externalMemberID
					AND lm.updateStatus = 'reactivate';

				SET @lastrowcount = @@rowcount;
				SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Reactivated memberships - Records: ' + cast(@lastrowcount AS VARCHAR(10));
				SET @progressLog = @progressLog + '<br/>' + @message;
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- add new memberships
		BEGIN TRY
			SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Finding new memberships';
			SET @progressLog = @progressLog + '<br/>' + @message;

			UPDATE lm
			SET lm.updateStatus = 'added'
			FROM 
				(
					SELECT min(autoID) AS autoID FROM MC_ListMembersForLyris WHERE list_ = @listName AND updateStatus IS NULL AND functionName in ('managePopulation') GROUP BY emailaddr_
				) deduped
				INNER JOIN MC_ListMembersForLyris lm
					on deduped.autoID = lm.autoID
					AND NULLif(lm.emailaddr_,'') IS NOT NULL
				LEFT OUTER JOIN dbo.members_ m
					on lm.list_ = m.list_
					AND (
						(lm.externalMemberID = m.externalMemberID)
						or (lm.emailaddr_ = m.emailaddr_)
						or (m.domain_ = RIGHT(lm.emailaddr_,LEN(lm.emailaddr_)-CHARINDEX('@',lm.emailaddr_)) AND m.usernamelc_ = LEFT(lm.emailaddr_,CHARINDEX('@',lm.emailaddr_)-1))
					)
			WHERE m.memberID_ IS NULL;

			SET @lastrowcount = @@rowcount;

			IF @lastrowcount > 0 AND @isAutoManageActive = 1 BEGIN
				INSERT INTO #tmpLogMessages (siteID, memberID, listName, msg)
				SELECT siteID, MCMemberID, list_, 'Email ['+ emailaddr_ +'] has been added to the list.'
				FROM MC_ListMembersForLyris
				WHERE list_ = @listName
				AND updateStatus = 'added';

				INSERT INTO dbo.members_ (DateJoined_,domain_,emailaddr_,fullname_ ,list_ ,usernameLc_ ,ExternalMemberID, membertype_, subtype_, mcemailkey,MCEmailKey_usernameLC,MCEmailKey_domain)
				SELECT GETDATE() AS DateJoined_, domain = lm.domain_, lm.emailaddr_, lm.fullname_, lm.list_, usernameLc_ = lm.usernamelc_,
					lm.ExternalMemberID, @defaultMembertype AS membertype_, @defaultSubType AS subype_,
					mcemailkey = convert(varchar(75),HASHBYTES('SHA2_256',@listName + '|' + lm.usernamelc_ collate SQL_Latin1_General_CP1_CI_AS + '@' + lm.domain_ collate SQL_Latin1_General_CP1_CI_AS),2),
					lm.usernamelc_, lm.domain_
				FROM MC_ListMembersForLyris lm
				WHERE lm.list_ = @listName
				AND lm.updateStatus = 'added';

				SET @lastrowcount = @@rowcount;
				SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Added new memberships - Records: ' + cast(@lastrowcount AS VARCHAR(10));
				SET @progressLog = @progressLog + '<br/>' + @message;
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH

		-- delete 1-way list expire members where ExpireDate_ more than one year old
		BEGIN TRY
			IF @thisListOneWayList = 1
			BEGIN
				SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Finding old expired memberships to delete';
				SET @progressLog = @progressLog + '<br/>' + @message;

				INSERT INTO dbo.MC_ListMembersForLyris (siteID, siteCode, orgID, orgCode, list_, externalMemberID, emailaddr_, 
					fullname_, functionName,updateStatus,isAutoManageActive)
				SELECT @siteID, @siteCode, @orgID, @orgcode, m.list_, m.externalMemberID, m.emailaddr_, m.fullname_, 
					NULL AS functionName, 'deletedFromMarketingList-expiredMoreThanOneYearAgo' AS updateStatus, @isAutoManageActive
				FROM dbo.members_ m  
				WHERE m.list_ = @listName
				AND m.membertype_ = 'expired'
				AND m.ExpireDate_ < @expireDateCutoff
				AND ltrim(rtrim(ISNULL(m.externalMemberID,''))) <> ''

				SET @lastrowcount = @@rowcount;

				IF @lastrowcount > 0 AND @isAutoManageActive = 1 BEGIN
					DELETE m
					FROM MC_ListMembersForLyris lm
					INNER JOIN dbo.members_ m ON lm.list_ = m.list_
						AND m.list_ = @listName
						AND lm.externalMemberID = m.externalMemberID
						AND lm.updateStatus = 'deletedFromMarketingList-expiredMoreThanOneYearAgo';

					SET @lastrowcount = @@rowcount;
					SET @message =  convert(varchar(19), GETDATE(), 121) + ' - ' + @listName + ': Expired members deleted - Records: ' + cast(@lastrowcount AS VARCHAR(10));
					SET @progressLog = @progressLog + '<br/>' + @message;
				END
			END
		END TRY
		BEGIN CATCH
			EXEC mc_updateListMemberships_error_handler @messageToAppend=@message, @message=@errorLog OUTPUT;
		END CATCH
	END

	-- remove queue item after processing
	DELETE
	FROM membercentral.platformQueue.dbo.queue_lyrisListSync
	WHERE itemID = @itemID;

	IF EXISTS(SELECT 1 FROM #tmpLogMessages) BEGIN
		SELECT @runByMemberID = memberID
		FROM memberCentral.memberCentral.dbo.ams_members
		WHERE orgID = 1 
		AND memberNumber = 'SYSTEM'
		AND [status] = 'A';

		UPDATE #tmpLogMessages
		SET msg = lyrisarchive.dbo.fn_cleanInvalidXMLChars(REPLACE(msg,'"','\"')),
			listName = lyrisarchive.dbo.fn_cleanInvalidXMLChars(REPLACE(listName,'"','\"'));

		INSERT INTO memberCentral.platformQueue.dbo.queue_mongo (msgjson)
		SELECT '{ "c":"historyEntries_SYS_ADMIN_LISTUPDATE", "d": { "HISTORYCODE":"SYS_ADMIN_LISTUPDATE", "SITEID":' + cast(tmp.siteID AS VARCHAR(10)) + 
			', "ACTORMEMBERID":' + cast(@runByMemberID AS VARCHAR(20)) + 
			', "RECEIVERMEMBERID":' + cast(tmp.memberID AS VARCHAR(10)) + 
			', "MAINMESSAGE":"List Membership Updated", "LISTNAME":"'+ tmp.listName +'", "MESSAGES":[ ' +
			STUFF((SELECT ', "' + msg + '"'
				FROM #tmpLogMessages
				WHERE siteID = tmp.siteID
				AND memberID = tmp.memberID
				AND listName = tmp.listName
				ORDER BY msg
				FOR XML PATH(''), TYPE).value('.','varchar(max)')
			,1,1,'') + ' ]'+
			', "UPDATEDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '"'+
			' } }'
		FROM (
			SELECT DISTINCT siteID, memberID, listName
			FROM #tmpLogMessages
		) AS tmp;
	END
	
END TRY
BEGIN CATCH
	-- remove queue item
	DELETE
	FROM membercentral.platformQueue.dbo.queue_lyrisListSync
	WHERE itemID = @itemID;

	SELECT @errmsg = error_message(), @severity = error_severity(),   -- 10
		   @state  = error_state(), @errno = error_number(),
		   @proc   = error_procedure(), @lineno = error_line();
	       
	IF @errmsg NOT LIKE '***%'                                        -- 11  
	BEGIN 
	   SELECT @errmsg = '*** ' + coalesce(quotename(@proc), '<dynamic SQL>') + 
						', ' + ltrim(str(@lineno)) + '. Errno ' + 
						ltrim(str(@errno)) + ': ' + @errmsg;
	END
	SET @escalateError = 1;
END CATCH

IF LEN(rtrim(ltrim(@errorLog))) > 0 BEGIN
	SET @errorLog = @errorLog + '<br/><br/>' + ISNULL(@progressLog,'');
	SET @emailSubject =  convert(varchar(19), GETDATE(), 121) + ' - MC ListSync Process: Errors Generated';
	SET @emailTitle =  'MC ListSync Process: Errors Generated';
	EXEC membercentral.platformMail.dbo.email_sendMessageSupport @errorSubject=@emailSubject, @errorTitle=@emailTitle, @messageContent=@errorLog, @forDev=1;
END

IF @escalateError = 1
	RAISERROR (@errmsg, @severity, @state, @errno);

IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
	DROP TABLE #tmpLogMessages;

on_end:
RETURN 0;
GO
