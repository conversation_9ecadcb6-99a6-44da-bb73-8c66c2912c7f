declare @authorityID int, @type1 int, @ASID int

insert into dbo.crd_authorities (authorityName, contact, address, city, state, ZIP, phone, fax, email, website, creditIDText)
values ('Kansas Continuing Legal Education Commission', '<PERSON>', '400S. Kansas Ave., Ste. 202', 'Topeka', 'KS', '66603', '************', '', '<EMAIL>', 'http://www.kscle.org', 'ID Number')
	select @authorityID = SCOPE_IDENTITY()

insert into dbo.crd_authorityTypes (authorityID, typeName)
values (@authorityID, 'General')
	select @type1 = SCOPE_IDENTITY()

insert into dbo.crd_authoritySponsors (authorityID, sponsorID, certificateMessage, creditMessage, affirmationFileName)
values (@authorityID, 25, '', '', null)
	select @ASID = SCOPE_IDENTITY()

insert into dbo.crd_authoritySponsorTypes (ASID, typeID, ovTypeName, LiveApprovedCertificateID, LiveDeniedCertificateID)
values (@ASID, @type1, null, null, null)
GO

declare @authorityID int, @type1 int, @ASID int

insert into dbo.crd_authorities (authorityName, contact, address, city, state, ZIP, phone, fax, email, website, creditIDText)
values ('Washington State Bar Association', '', '1325 Fourth Ave., Ste. 600', 'Seattle', 'WA', '98101-2539', '************', '************', '', 'http://www.wsba.org', 'Bar Number')
	select @authorityID = SCOPE_IDENTITY()

insert into dbo.crd_authorityTypes (authorityID, typeName)
values (@authorityID, 'General')
	select @type1 = SCOPE_IDENTITY()

insert into dbo.crd_authoritySponsors (authorityID, sponsorID, certificateMessage, creditMessage, affirmationFileName)
values (@authorityID, 25, '', '', null)
	select @ASID = SCOPE_IDENTITY()

insert into dbo.crd_authoritySponsorTypes (ASID, typeID, ovTypeName, LiveApprovedCertificateID, LiveDeniedCertificateID)
values (@ASID, @type1, null, null, null)
GO


