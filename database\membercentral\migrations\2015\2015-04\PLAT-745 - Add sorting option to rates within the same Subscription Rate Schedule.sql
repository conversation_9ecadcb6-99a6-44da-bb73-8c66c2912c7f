use membercentral
GO

if not exists(select * from sys.columns 
            where Name = N'rateOrder' and Object_ID = Object_ID(N'sub_rates')) begin
	alter table dbo.sub_rates
		add rateOrder int
end
GO

-- ****************************************************************************************************

declare @testing bit

-- set to 0 to run update
-- set to 1 to see select

set @testing = 0


IF OBJECT_ID('tempdb..#newRateOrder') IS NOT NULL
   DROP TABLE #newRateOrder
CREATE TABLE #newRateOrder (rateID int,rateOrder int)


DECLARE @FID int, @scheduleID int, @currentDate datetime
set @currentDate = getdate()

select @FID = dbo.fn_getResourceFunctionID('qualify',dbo.fn_getResourceTypeID('SubscriptionRate'))

insert into #newRateOrder (rateID, rateOrder)
select tmp.rateID, row_number() over(partition by tmp.scheduleID order by tmp.isRenewalRate, tmp.qualifiedMembers, tmp.allowFrontEnd desc) as rateOrder
from (
	select r.rateID, r.ratename, r.isRenewalRate, r.scheduleID, count(*) as qualifiedMembers, 
	   allowFrontEnd = case 
		  when exists(select rateID from sub_rateFrequencies where allowFrontEnd=1 and rateID = r.rateID) then 1 
		  else 0 end
	from dbo.sub_rates as r 
	INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints srfrp 
		ON srfrp.siteResourceID = r.siteResourceID
		AND srfrp.functionID = @FID
		and r.status = 'A'
	INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp 
		on srfrp.rightPrintID = gprp.rightPrintID
	inner join ams_members m
		on m.groupPrintID = gprp.groupPrintID
		and m.memberID = m.activeMemberID
	group by r.rateID, r.ratename, r.isRenewalRate, r.scheduleID
) as tmp

if @testing = 0
BEGIN
    update r set
	   r.rateOrder = nr.rateOrder
    from #newRateOrder nr
    inner join sub_rates r
	   on r.rateID = nr.rateID
END
if @testing = 1
BEGIN
    select *
    from #newRateOrder nr
    inner join sub_rates r
	   on r.rateID = nr.rateID
END

IF OBJECT_ID('tempdb..#newRateOrder') IS NOT NULL
   DROP TABLE #newRateOrder
GO

-- ****************************************************************************************************

ALTER PROCEDURE [dbo].[sub_getRatesForAdminByScheduleID] 
	@scheduleID int
AS

declare @rtid int, @rfid int
select @rtid = dbo.fn_getResourceTypeID('SubscriptionRate')
select @rfid = dbo.fn_getResourceFunctionID('Qualify',@rtid) 

select case when LEN(r.rateName) = 0 then '(name not set)' else r.rateName end as rateName, 
	r.rateID, r.status, r.rateOrder,
	r.rateStartDate, r.rateEndDate, r.rateAFStartDate, r.rateAFEndDate,
	r.termStartDate, r.termEndDate, r.termAFStartDate, r.termAFEndDate,
	r.recogStartDate, r.recogEndDate, r.recogAFStartDate, r.recogAFEndDate,
	r.siteResourceID, r.isRenewalRate, r.forceUpfront, g.groupID, g.groupName, srrc.include
from dbo.sub_rates r
inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc in ('Active', 'Deleted')
left outer join dbo.cms_siteResourceRightsCache as srrc
	INNER JOIN dbo.ams_groups as g on g.groupID = srrc.groupID 
	on srrc.resourceid = r.siteresourceID AND srrc.functionID = @rfid and g.status = 'A'
where r.scheduleID = @scheduleID
ORDER BY r.status, r.rateOrder
GO

-- ****************************************************************************************************

ALTER FUNCTION [dbo].[fn_sub_getBestRenewalRateInfo]
(
	-- Add the parameters for the function here
	@memberID int, @subscriptionID int, @currentSubscriberID int
)
RETURNS 
@BestRenewalRateInfo TABLE 
(
rfid int,
rateAmt money,
numInstallments int,
frequencyName varchar(50), 
frequency int, 
frequencyID int, 
rateName varchar(200),
keepChangePrice bit,
modifiedRate money,
selectionMethod varchar(100),
generateWarning bit
)
AS
BEGIN

	DECLARE @FID int, @scheduleID int
	select @FID = dbo.fn_getResourceFunctionID('qualify',dbo.fn_getResourceTypeID('SubscriptionRate'))

	select @scheduleID = scheduleID
	from sub_subscriptions
	where subscriptionID = @subscriptionID

	insert into @BestRenewalRateInfo (rfid,rateAmt,numInstallments,frequencyName, frequency, frequencyID, rateName,keepChangePrice,modifiedRate,selectionMethod,generateWarning)

	select top 1 rf.rfid, rf.rateAmt, rf.numInstallments, isnull(f.frequencyName,'') as frequencyName, isnull(f.frequency,0) as frequency, isnull(f.frequencyID,0) as frequencyID, isnull(newRate.rateName,'') as rateName,
		keepChangePrice = case when existingRate.keepChangedPriceOnRenewal = 1 and (newRate.linkedNonRenewalRateID = existingRate.rateID or newRate.rateID = existingRate.rateID) then cast (1 as bit) else cast (0 as bit) end,
		modifiedRate = case when existingRate.keepChangedPriceOnRenewal = 1 and (newRate.linkedNonRenewalRateID = existingRate.rateID or newRate.rateID = existingRate.rateID) then ss.modifiedRate else null end,

		selectionMethod = case 
			when newRate.rateID = existingRate.rateID then 'sameRateMatch'
			--linkedRateMatch (user had a Primary Join Rate that was directly linked to a renewal rate)
			when newRate.linkedNonRenewalRateID = existingRate.rateID then 'linkedRateMatch'
			--linkedJoinRateMatch (user had a Join rate linked to the same Primary Join Rate as this Renewal Rate)
			when existingRate.isRenewalRate = 0 and newRate.linkedNonRenewalRateID = existingRate.linkedNonRenewalRateID then 'linkedJoinRate_linkedRenewalRateMatch'
			-- fallbackRateMatch (direct fallback of existing rate -- works for both join and renewal rates )
			when newRate.rateID = existingRate.fallbackRenewalRateID then 'fallbackRateMatch'
			-- linkedJoinRateFallbackRateMatch of linked join rate (covers both when user had a renewal rate or a secondary join rates)
			when newRate.rateID = linkedJoinRateOfExistingRate.fallbackRenewalRateID then 'linkedJoinRate_FallbackRateMatch'
			-- favors the rates with the least number of qualified records
			else 'rateOrder'
		end,
		0 as generateWarning
	from sub_subscriptions subs
	inner join dbo.sub_rateSchedules as rs
		on rs.scheduleID = subs.scheduleID
		and rs.status = 'A'
		and subs.subscriptionID = @subscriptionID
	inner join dbo.sub_rates as newRate on newRate.scheduleID = rs.scheduleID and newRate.status = 'A' and newRate.isRenewalRate = 1
		and getdate() between newRate.rateAFStartDate and dateadd(day, datediff(day, 0, newRate.rateAFEndDate)+1, 0)
	inner join dbo.sub_rateFrequencies rf on rf.rateID = newRate.rateID
	inner join dbo.sub_frequencies f on rf.frequencyID = f.frequencyID
	INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints srfrp ON srfrp.siteResourceID = newRate.siteResourceID
		AND srfrp.functionID = @FID
	INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp on srfrp.rightPrintID = gprp.rightPrintID
	inner join ams_members m
		on m.groupPrintID = gprp.groupPrintID
		and m.memberID = @memberID
	left outer join sub_subscribers ss
		inner join dbo.sub_rateFrequencies rf2 on rf2.rfid = ss.rfid
		inner join dbo.sub_rates existingRate on existingRate.rateID = rf2.rateID
		left outer join dbo.sub_rates linkedJoinRateOfExistingRate on existingRate.linkedNonRenewalRateID = linkedJoinRateOfExistingRate.rateID
		--left outer join dbo.sub_rates linkedRenewRatesOflinkedJoinRateOfExistingRate on linkedRenewRatesOflinkedJoinRateOfExistingRate.linkedNonRenewalRateID = linkedJoinRateOfExistingRate.rateID
	on ss.subscriberID = @currentSubscriberID
	order by 
		--	sameRateMatch (user already had a renewal rate)
		case when newRate.rateID = existingRate.rateID then 1 else 0 end desc,
		--linkedRenewalRateMatch (user had a Primary Join Rate that was directly linked to a renewal rate)
		case when newRate.linkedNonRenewalRateID = existingRate.rateID then 1 else 0 end desc,
		--linkedJoinRate_linkedRenewalRateMatch (user had a Join rate linked to the same Primary Join Rate as this Renewal Rate)
		case when existingRate.isRenewalRate = 0 and newRate.linkedNonRenewalRateID = existingRate.linkedNonRenewalRateID then 1 else 0 end desc,
		-- fallbackRateMatch (direct fallback of existing rate -- works for both join and renewal rates )
		case when newRate.rateID = existingRate.fallbackRenewalRateID then 1 else 0 end desc,
		-- linkedJoinRate_FallbackRateMatch of linked join rate (covers both when user had a renewal rate or a secondary join rates)
		case when newRate.rateID = linkedJoinRateOfExistingRate.fallbackRenewalRateID then 1 else 0 end desc,
		-- favors the rates based on ordering within the rate schedule
		newRate.rateOrder,
		-- favors rates with frequencies available on the front end
		case when exists(select rateID from sub_rateFrequencies where allowFrontEnd=1 and rateID = newRate.rateID) then 1 else 0 end desc,
		--sameFrequencyMatch
		case when rf.frequencyID = rf2.frequencyID then 1 else 0 end desc,
		--fullRateMatch
		case when f.frequencyName = 'Full' then 1 else 0 end desc

	update @BestRenewalRateInfo set
		generateWarning = 1
	where selectionMethod in ('rateOrder')	

	RETURN 
END
GO

-- ****************************************************************************************************

ALTER FUNCTION [dbo].[fn_sub_getMostExclusiveRateInfo]
(
	-- Add the parameters for the function here
	@memberID int, @subscriptionID int, @isRenewalRate int
)
RETURNS 

@MostExclusiveRateInfo TABLE 
(
rateID int,
uid uniqueIdentifier,
rateName varchar(200)
)
AS
BEGIN
    DECLARE @FID int, @scheduleID int, @currentDate datetime
    set @currentDate = getdate()

    select @FID = dbo.fn_getResourceFunctionID('qualify',dbo.fn_getResourceTypeID('SubscriptionRate'))

	select @scheduleID = scheduleID
	from sub_subscriptions
	where subscriptionID = @subscriptionID

	insert into @MostExclusiveRateInfo (rateID, uid,rateName)

	select top 1 newRate.rateID, newRate.uid,  newRate.rateName
	from sub_subscriptions subs
	inner join dbo.sub_rateSchedules as rs
		on rs.scheduleID = subs.scheduleID
		and rs.status = 'A'
		and subs.subscriptionID = @subscriptionID
	inner join dbo.sub_rates as newRate 
		on newRate.scheduleID = rs.scheduleID 
		and newRate.status = 'A' 
		and newRate.isRenewalRate = @isRenewalRate
		and @currentDate between newRate.rateAFStartDate and dateadd(day, datediff(day, 0, newRate.rateAFEndDate)+1, 0)
	INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints srfrp 
		ON srfrp.siteResourceID = newRate.siteResourceID
		AND srfrp.functionID = @FID
	INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp on srfrp.rightPrintID = gprp.rightPrintID
	inner join ams_members m
		on m.groupPrintID = gprp.groupPrintID
		and m.memberID = @memberID
	order by 
		newRate.rateORder
	RETURN 
END
GO

-- ****************************************************************************************************

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].sub_reorderRates') AND type in (N'P', N'PC'))
	DROP PROCEDURE [dbo].sub_reorderRates
GO

CREATE PROCEDURE dbo.sub_reorderRates
	@rateID int
AS

DECLARE @tmp TABLE (neworder int IDENTITY(1,1) NOT NULL, 
					rateID int NOT NULL, 
					rateOrder int NOT NULL)

DECLARE	@rateOrder int, @scheduleID int, @error_section int

SELECT @scheduleID = scheduleID
FROM dbo.sub_rates
WHERE rateID = @rateID

BEGIN TRAN

	INSERT INTO @tmp (rateID, rateOrder)
	SELECT r.rateID, r.rateOrder
	FROM dbo.sub_rates as r
	WHERE  r.scheduleID = @scheduleID
		and r.status = 'A'
	ORDER BY r.rateOrder
		IF @@ERROR <> 0 BEGIN
			select @error_section = 1
			GOTO on_error
		END

	UPDATE rates
	SET rates.rateOrder = t.neworder
	FROM dbo.sub_rates as rates 
	INNER JOIN @tmp as t on rates.rateID = t.rateID
	WHERE rates.scheduleID = @scheduleID
		and rates.status = 'A'
		IF @@ERROR <> 0 BEGIN
			select @error_section = 2
			GOTO on_error
		END

	-- normal exit
	IF @@TRANCOUNT > 0 COMMIT TRAN

	RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

-- ****************************************************************************************************

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].sub_moveRateUp') AND type in (N'P', N'PC'))
	DROP PROCEDURE [dbo].sub_moveRateUp
GO

CREATE PROCEDURE dbo.sub_moveRateUp
	@rateID int
AS

declare 
	@rateOrder int, 
	@scheduleID int
	
SELECT 
	@rateOrder = rateOrder, 
	@scheduleID = scheduleID
FROM 
	dbo.sub_rates
WHERE 
	rateID = @rateID

UPDATE 
	r
SET 
	r.rateOrder = r.rateOrder + 1
FROM 
	dbo.sub_rates as r
WHERE  
	r.scheduleID = @scheduleID
	AND r.status = 'A'
	AND r.rateOrder >= @rateOrder - 1

UPDATE 
	dbo.sub_rates
SET 
	rateOrder = rateOrder  - 2
WHERE 
	rateID = @rateID
	
EXEC dbo.sub_reorderRates @rateID = @rateID

RETURN
GO

-- ****************************************************************************************************

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].sub_moveRateDown') AND type in (N'P', N'PC'))
	DROP PROCEDURE [dbo].sub_moveRateDown
GO

CREATE PROCEDURE dbo.sub_moveRateDown
	@rateID int
AS

DECLARE 
	@rateOrder int, 
	@scheduleID int
	
SELECT 
	@rateOrder = rateOrder, 
	@scheduleID = scheduleID
FROM 
	dbo.sub_rates
WHERE 
	rateID = @rateID

UPDATE 
	r
SET 
	r.rateOrder = r.rateOrder - 1
FROM 
	dbo.sub_rates as r
WHERE  
	r.scheduleID = @scheduleID
	AND r.status = 'A'
	AND r.rateOrder <= @rateOrder + 1
	
UPDATE 
	dbo.sub_rates
SET 
	rateOrder = rateOrder  + 2
WHERE 
	rateID = @rateID

EXEC dbo.sub_reorderRates @rateID = @rateID

RETURN
GO

-- ****************************************************************************************************

ALTER PROCEDURE [dbo].[sub_createRate] 
@scheduleID int,
@rateName varchar(100),
@reportCode varchar(15),
@status char(1),
@rateStartDate datetime,
@rateEndDate datetime,
@rateStartAFID INT,
@rateEndAFID INT,
@termStartDate datetime,
@termEndDate datetime,
@termStartAFID INT,
@termEndAFID INT,
@graceEndDate datetime,
@graceEndAFID INT,
@recogStartDate datetime,
@recogEndDate datetime,
@recogStartAFID INT,
@recogEndAFID INT,
@rateAdvanceOnTermEnd INT,
@isRenewalRate INT,
@forceUpfront INT,
@accountID INT,
@frontEndAllowChangePrice INT,
@linkedNonRenewalRateID INT,
@fallbackRenewalRateID INT,
@keepChangedPriceOnRenewal INT,
@frontEndChangePriceMin money,
@frontEndChangePriceMax money,
@rateID int OUTPUT,
@siteResourceID int OUTPUT

AS

SET NOCOUNT ON

-- null output variables
select @rateID = null, @siteResourceID = null

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	declare @siteID int, @siteResourceTypeID int, @siteResourceStatusID int, @rateOrder int

	select @siteID = siteid from dbo.sub_rateSchedules where scheduleID = @scheduleID
	select @siteResourceTypeID = dbo.fn_getResourceTypeID('SubscriptionRate')
	select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

	if (@frontEndChangePriceMin is not null) and (@frontEndChangePriceMax is not null) and (@frontEndChangePriceMin > @frontEndChangePriceMax) BEGIN
		set @frontEndChangePriceMin = null;
		set @frontEndChangePriceMax = null;
	END

	-- get new rate's sort order
	select 
		@rateOrder = isNull(max(r.rateOrder),0)+1 
	from 
		dbo.sub_rates as r
		inner join dbo.cms_siteResources as sr on 
			sr.siteResourceID = r.siteResourceID
		inner join dbo.cms_siteResourceStatuses as srs on 
			srs.siteResourceStatusID = sr.siteResourceStatusID
			and srs.siteResourceStatusDesc = 'Active'
	where 
		r.scheduleID = @scheduleID

	-- create a resourceID for the rate
	exec dbo.cms_createSiteResource @resourceTypeID=@siteResourceTypeID, @siteResourceStatusID=@siteResourceStatusID,
		@siteID=@siteid, @isVisible=1, @parentSiteResourceID=null, @siteResourceID=@siteResourceID OUTPUT

	-- add rate
	INSERT INTO dbo.sub_rates (scheduleID, siteResourceID, [status], 
		rateStartDate, rateEndDate, rateStartDateAFID, rateEndDateAFID, rateAFStartDate, rateAFEndDate, 
		termStartDate, termEndDate, termStartDateAFID, termEndDateAFID, termAFStartDate, termAFEndDate, 
		graceEndDate, graceAFID, 
		recogStartDate, recogEndDate, recogStartDateAFID, recogEndDateAFID, recogAFStartDate, recogAFEndDate, 
		rateName, rateAdvanceOnTermEnd, isRenewalRate, forceUpfront, reportCode, GLAccountID, 
		frontEndAllowChangePrice, linkedNonRenewalRateID, fallbackRenewalRateID, keepChangedPriceOnRenewal, 
		frontEndChangePriceMin, frontEndChangePriceMax, rateOrder)
	VALUES (@scheduleID, @siteResourceID, @status, 
		@rateStartDate, @rateEndDate, @rateStartAFID, @rateEndAFID, @rateStartDate, @rateEndDate, 
		@termStartDate, @termEndDate, @termStartAFID, @termEndAFID, @termStartDate, @termEndDate, 
		@graceEndDate, @graceEndAFID, 
		@recogStartDate, @recogEndDate, @recogStartAFID, @recogEndAFID, @recogStartDate, @recogEndDate, 
		@rateName, @rateAdvanceOnTermEnd, @isRenewalRate, @forceUpfront, @reportCode, @accountID,
		@frontEndAllowChangePrice, @linkedNonRenewalRateID, @fallbackRenewalRateID, @keepChangedPriceOnRenewal,
		@frontEndChangePriceMin, @frontEndChangePriceMax, @rateOrder)

	select @rateID = SCOPE_IDENTITY()


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	SELECT @rateID = 0
	SELECT @siteResourceID = 0

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

-- ****************************************************************************************************

CREATE PROCEDURE dbo.sub_moveRateToPosition
	@rateID int,
	@pos int
AS

declare 
	@rateOrder int, 
	@scheduleID int,
	@error_section int

declare @tmp table (neworder int IDENTITY(1,1) NOT NULL, 
					rateID int NOT NULL, 
					rateOrder int NOT NULL)

select 
	@scheduleID = scheduleID,
	@rateOrder = rateOrder
from 
	dbo.sub_rates
where 
	rateID = @rateID	

BEGIN TRAN

	if @rateOrder > @pos begin
		insert into @tmp (rateID, rateOrder)
		select 
			rateID, rateOrder + 1
		from 
			dbo.sub_rates as r
			inner join dbo.cms_siteResources as sr on 
				sr.siteResourceID = r.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on 
				srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
		where 
			r.scheduleID = @scheduleID
			and (r.rateOrder >= @pos
				and r.rateOrder <= @rateOrder)
			
		order by
			rateOrder

		update 
			rates
		set 
			rates.rateOrder = t.rateOrder
		from 
			dbo.sub_rates as rates 
			inner join @tmp as t on t.rateID = rates.rateID 
		where 
			rates.scheduleID = @scheduleID
			and rates.status = 'A'
		if @@ERROR <> 0 begin
			select @error_section = 2
			goto on_error
		end

	end 
	else begin
		insert into @tmp (rateID, rateOrder)
		select 
			rateID, rateOrder
		from 
			dbo.sub_rates as r
			inner join dbo.cms_siteResources as sr on 
				sr.siteResourceID = r.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on 
				srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
		where 
			r.scheduleID = @scheduleID
			and rateOrder <= @pos
			and r.rateID <> @rateID
		order by
			rateOrder

		update 
			rates
		set 
			rates.rateOrder = t.neworder
		from 
			dbo.sub_rates as rates 
			inner join @tmp as t on t.rateID = rates.rateID 
		where 
			rates.scheduleID = @scheduleID
			and rates.status = 'A'
		if @@ERROR <> 0 begin
			select @error_section = 2
			goto on_error
		end
	end

	update 
		dbo.sub_rates
	set 
		rateOrder = @pos
	where 
		rateID = @rateID
	if @@ERROR <> 0 begin
		select @error_section = 3
		goto on_error
	end

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN

RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO
