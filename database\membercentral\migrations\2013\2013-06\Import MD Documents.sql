USE [dataTransfer]
GO

declare @minID varchar(200), @orgID int, @siteID int, @resourceTypeID int, @sectionID int, @contributorMemberID int, @docTitle varchar(200), 
	@documentID int, @documentVersionID int, @documentSiteResourceID int, @memberID int, @counter int, @loadData bit, @trashID int,
	@param1 varchar(300), @param2 varchar(300), @pathImport varchar(200), @pathFinal varchar(200), @doLoop bit, @moveDocs bit, @fileName varchar(200)
select @orgID = orgID, @siteID = siteID from membercentral.dbo.sites where sitecode = 'MD'
select @resourceTypeID = membercentral.dbo.fn_getResourceTypeId('UserCreatedDocument')
SELECT @sectionID = membercentral.dbo.fn_getRootSectionID(@siteID)
select @contributorMemberID = memberID from membercentral.dbo.ams_members where memberNumber = 'SYSTEM' and orgID = 1
set @loadData = 0
set @doLoop = 0
set @moveDocs = 1

IF @@servername = 'TSSQL1\MEMBERCENTRAL' BEGIN
	set @pathImport = 'e:\temp\'
	set @pathFinal = '\\tsfile1\f$\platform\siteDocuments\MD\MD\'
END
IF @@servername = 'DEV03\PLATFORM2008' BEGIN
	set @pathImport = 'c:\temp\'
	set @pathFinal = '\\dev03\wwwroot\siteDocuments\MD\MD\'
END
IF @@servername = 'DEV04\PLATFORM2008' BEGIN
	set @pathImport = 'e:\temp\'
	set @pathFinal = '\\dev01\wwwroot\siteDocuments\MD\MD\'
END

IF @loadData = 1 BEGIN
	IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[MDDocs]') AND type in (N'U'))
		DROP TABLE datatransfer.dbo.MDDocs;

	CREATE TABLE datatransfer.dbo.MDDocs (
		[sortName] [varchar](200) NULL,
		[MemberNumber] [varchar](200) NULL,
		[FileName] [varchar](200) NULL,
		[key] [varchar](200) NULL
	)

	declare @qry varchar(8000)
	SELECT @qry = 'BULK INSERT datatransfer.dbo.MDDocs FROM ''' + @pathImport + 'MAJ_File_Extraction.txt'' WITH (FIELDTERMINATOR = ''' + char(9) + ''', FIRSTROW = 2);'
	EXEC(@qry)

	ALTER TABLE datatransfer.dbo.MDDocs ADD isFile bit, memberID int, documentVersionID int;

	update datatransfer.dbo.MDDocs set isFile = 0
	update datatransfer.dbo.MDDocs set isFile = 1 where membercentral.dbo.fn_fileExists(@pathImport + 'mdfiles\' + [key] + '.pdf') = 1

	--select * from datatransfer.dbo.MDDocs where isFile = 0
	delete from datatransfer.dbo.MDDocs where isFile = 0

	update tmp
	set tmp.memberID = m.memberid
	from datatransfer.dbo.MDDocs as tmp
	inner join membercentral.dbo.ams_members as m on m.memberNumber = tmp.memberNumber
		and m.orgID = @orgID
		and m.memberID = m.activeMemberID

	--select * from datatransfer.dbo.MDDocs where memberid is null
	delete from datatransfer.dbo.MDDocs where memberid is null
END

IF @doLoop = 1 BEGIN
	set @counter = 0
	SELECT @minID = min([key]) from datatransfer.dbo.MDDocs where documentVersionID is null
	WHILE @minID is not null and @counter < 400 BEGIN
		set @counter = @counter + 1
		select @documentID = null, @documentVersionID = null, @documentSiteResourceID = null
		select @docTitle = [fileName], @fileName=[fileName] + '.pdf', @memberID = memberID from datatransfer.dbo.MDDocs where [key] = @minID

		EXEC membercentral.dbo.cms_createDocument @siteID, @resourceTypeID, 1, 1, @sectionID, @contributorMemberID, 1, 1, @docTitle, @docTitle, null, null, @fileName, 'pdf', @documentID OUTPUT, @documentVersionID OUTPUT, @documentSiteResourceID OUTPUT
	
		IF @documentVersionID is not null BEGIN
			update datatransfer.dbo.MDDocs
			set documentVersionID = @documentVersionID
			where [key] = @minID

			INSERT INTO membercentral.dbo.ams_memberDocuments (memberID, documentID)
			VALUES (@memberID, @documentID)
		END

		SELECT @minID = min([key]) from datatransfer.dbo.MDDocs where documentVersionID is null and [key] > @minID
	END
END

IF @moveDocs = 1 BEGIN
	SELECT @minID = min([key]) from datatransfer.dbo.MDDocs where documentVersionID is not null
	WHILE @minID is not null BEGIN
		select @documentVersionID = documentVersionID from datatransfer.dbo.MDDocs where [key] = @minID

		set @param1 = @pathImport + 'mdfiles\' + @minID + '.pdf'
		set @param2 = @pathFinal + cast(@documentVersionID as varchar(10)) + '.pdf'
		select @trashID = membercentral.dbo.fn_copyFile(@param1, @param2, 1)

		SELECT @minID = min([key]) from datatransfer.dbo.MDDocs where documentVersionID is not null and [key] > @minID
	END
END
