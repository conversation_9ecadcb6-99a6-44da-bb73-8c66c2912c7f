declare @orgcode varchar(5), @siteCode varchar(5), @siteID int, @orgID int
declare @subscriberIDs TABLE (subscriberID int PRIMARY KEY, statusHistoryID int,oldStatusID int)

set @orgcode = 'NM'
set @siteCode  ='NM'
set @orgID = dbo.fn_getOrgIDfromOrgCode(@siteCode)
set @siteID  = dbo.fn_getSiteIDfromSiteCode(@siteCode)


-- declare variable for this specific case
-- insert into temp table all subscriberIDs that need to have there most recent status change reversed
insert into @subscriberIDs (subscriberID, statusHistoryID, oldStatusID)
select s.subscriberID, sh.statusHistoryID, sh.oldStatusID
from sub_statusHistory as sh
inner join sub_subscribers as s on s.subscriberID = sh.subscriberID
inner join sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
inner join sub_types as t on t.typeID = sub.typeID
where t.siteID = @siteID
and sh.updateDate between '11/1/2014' and '11/2/2014'
and sh.statusID = 6
and not exists (
	select subscriberID
	from sub_subscribers as s2
	where s2.memberID = s.memberID 
	and s2.subscriptionID = s.subscriptionID
	and s2.statusID = 1
)

update ss 
set	ss.statusID = temp.oldStatusID
from @subscriberIDs temp
inner join sub_subscribers ss on ss.subscriberID = temp.subscriberID

delete sh
from @subscriberIDs temp
inner join sub_statusHistory sh on sh.statusHistoryID = temp.statusHistoryID

exec dbo.sub_fixGroups @siteID
GO