ALTER TABLE dbo.mh_memberHistory ADD [isRelationship] bit NOT NULL DEFAULT(0)

GO


declare @tblConvert table (id int identity(1,1), siteID int, mainMemberID int, subMemberID int, categoryName varchar(300), categoryID int, [description] varchar(max), relationshipDate datetime, dateCreated datetime, creatorMemberID int)

-- 1461 rows in dev took 3 seconds 
insert into @tblConvert(siteID, mainMemberID, subMemberID, categoryID, categoryName, [description], relationshipDate, dateCreated, creatorMemberID)
select sr.siteID,
mMain.activeMemberID as mainMemberID, mSub.activeMemberID as subMemberID,
cType.categoryID, cType.categoryName,
case when LEN(isnull(mr.subMemberDescription,'')) > 0 then mr.subMemberDescription + char(13)+char(10) + relContent.rawContent
	else relContent.rawContent 
end as [description], 
mr.relationshipDate, mr.dateCreated, mr.creatorMember<PERSON>
from ams_memberRelationships mr
inner join cms_siteResources sr on sr.siteResourceID = mr.siteResourceID --and sr.siteID = @siteID
INNER JOIN dbo.cms_siteResourceStatuses srs ON sr.siteResourceStatusID = srs.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
inner join dbo.ams_members mMain on mMain.memberID = mr.mainMemberID
left outer join dbo.ams_members mSub on mSub.memberID = mr.subMemberID
inner join dbo.cms_categories cType on cType.categoryID = mr.categoryID
cross apply dbo.fn_getContent(mr.relationshipContentID,1) as relContent
where sr.siteID <> 6
order by sr.siteID, cType.categoryName, mr.relationshipDate, mr.mainMemberID

-- just outputting for verification purposes 
select *
from @tblConvert


declare @minMHID int, @currMHSiteID int, @currMainMemberID int, @currSubMemberID int, @currCategoryID int, 
	@currRelDate datetime, @currDateCreated datetime, @currDescription varchar(max), @currCreatorMemberID int, 
	@trashID int, @errSection varchar(100)

print 'STARTING Relationships'
select @errSection = 'Member History'

select @minMHID = min(id)
from @tblConvert


BEGIN TRAN

while @minMHID is not null
begin
	select @currMHSiteID=NULL, @currMainMemberID=NULL, @currSubMemberID=NULL, @currCategoryID=NULL, 
		@currRelDate=NULL, @currDateCreated=NULL, @currDescription=NULL, @currCreatorMemberID=NULL, @trashID=NULL

	select @currMHSiteID=siteID, @currMainMemberID=mainMemberID, @currSubMemberID=subMemberID,
		@currCategoryID=categoryID, @currRelDate=relationshipDate, @currDateCreated=dateCreated,
		@currDescription=[description],@currCreatorMemberID=creatorMemberID
	from @tblConvert
	where id = @minMHID

	exec dbo.mh_addMemberHistory	@siteid=@currMHSiteID, 
									@memberID=@currMainMemberID, 
									@categoryID=@currCategoryID, 
									@subCategoryID=NULL, 
									@userDate=@currRelDate, 
									@qty=0, 
									@dollarAmt=NULL, 
									@description=@currDescription, 
									@linkMemberID=@currSubMemberID,
									@enteredByMemberID=@currCreatorMemberID, 
									@memberHistoryID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @trashID = 0 GOTO on_error

	update dbo.mh_memberHistory
	set dateEntered = @currDateCreated,
		isRelationship = 1
	where memberHistoryID = @trashID

	select @minMHID = min(id)
	from @tblConvert
	where id > @minMHID
end
print 'END Relationships'


IF @@TRANCOUNT > 0 COMMIT TRAN	

GOTO on_success

on_error:
	ROLLBACK TRAN
	IF (@errSection = 'Member History')
	BEGIN
		print 'Error (' + @errSection + '): @currMHSiteID=' + isNull(cast(@currMHSiteID as varchar),'[NULL]') +
			', @currMainMemberID=' + isNull(cast(@currMainMemberID as varchar),'[NULL]') +
			', @currSubMemberID=' + isNull(cast(@currSubMemberID as varchar),'[NULL]') +
			', @currCategoryID=' +  isNull(cast(@currCategoryID as varchar(max)),'[NULL]') +
			', @currRelDate=' +  isNull(cast(@currRelDate as varchar),'[NULL]') +
			', @currDateCreated=' +  isNull(cast(@currDateCreated as varchar),'[NULL]') +
			', @currDescription=' +  isNull(cast(@currDescription as varchar(max)),'[NULL]') + 
			', @currCreatorMemberID = ' + isnull(CAST (@currCreatorMemberID as varchar), 'NULL') 
	END

on_success:


GO



DROP TABLE ams_memberRelationshipsStatuses
GO
DROP TABLE ams_memberRelationships
GO
