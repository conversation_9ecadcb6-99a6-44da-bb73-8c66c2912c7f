ALTER PROCEDURE MoveRecipient
  		@MemberID int,
  		@ResultCode int,
 	@RecipientID numeric(19),
 	@Now smalldatetime, 
  		@TransactionLog varchar(8000) = NULL
 	 AS
 	 begin
 		DECLARE @ptrval binary(16)
 
 		set @ptrval = NULL
  		set nocount on
 		set xact_abort on
 
  		if @TransactionLog is not NULL
 		begin
  			SELECT @ptrval = TEXTPTR(TransactionLog) 
 			from lyrActiveRecips with ( index ( PK_lyrActiveRecips ) ) 
  			where RecipientID = @RecipientID
 
 			if @@error > 0
 			begin
 				raiserror ( 'Unable to retrieve from lyrActiveRecips.  Error: %d', 11, 1, @@error )
 				return
 			end
 		end
 
  		begin transaction MoveRecipient
 
  		if @TransactionLog is not NULL and @ptrval is not null
 		begin
 			UpdateText lyrActiveRecips.TransactionLog @ptrval NULL 0 @TransactionLog
 			if @@error != 0
 			begin
 				rollback transaction
 				raiserror ( 'Call to UpdateText failed.  Error: %d', 11, 2, @@error )
 				return
 			end
 		end
 
  		if @MemberID = 0 and @TransactionLog is not NULL and @ptrval is null
 			insert into lyrCompletedRecips ( RecipientID, MailingID, FinalAttempt, SendTry,
  				UserName, Domain, CompletionStatusID, MemberID, TransactionLog, FirstAttempt )
  				select RecipientID, MailingID, @Now, SendTry + 1, UserName, Domain,
 				@ResultCode, NULL, @TransactionLog, IsNull ( FirstAttempt, @Now )
  				from lyrActiveRecips with ( index ( PK_lyrActiveRecips ) ) 
  				where RecipientID = @RecipientID
 		else if @MemberID = 0 and @TransactionLog is not NULL and @ptrval is not null
 			insert into lyrCompletedRecips ( RecipientID, MailingID, FinalAttempt, SendTry,
 				UserName, Domain, CompletionStatusID, MemberID, TransactionLog, FirstAttempt )
  				select RecipientID, MailingID, @Now, SendTry + 1, UserName, Domain,
 				@ResultCode, NULL, TransactionLog, IsNull ( FirstAttempt, @Now )
 				from lyrActiveRecips with ( index ( PK_lyrActiveRecips ) ) 
  				where RecipientID = @RecipientID else if @MemberID = 0 and @TransactionLog is null and @ptrval is null
 			insert into lyrCompletedRecips ( RecipientID, MailingID, FinalAttempt, SendTry,
 				UserName, Domain, CompletionStatusID, MemberID, FirstAttempt )
  				select RecipientID, MailingID, @Now, SendTry + 1, UserName, Domain,
 				@ResultCode, NULL, IsNull ( FirstAttempt, @Now )
 				from lyrActiveRecips with ( index ( PK_lyrActiveRecips ) ) 
  				where RecipientID = @RecipientID
  		else if @MemberID = 0 and @TransactionLog is null and @ptrval is not null
 			insert into lyrCompletedRecips ( RecipientID, MailingID, FinalAttempt, SendTry,
 				UserName, Domain, CompletionStatusID, MemberID, TransactionLog, FirstAttempt )
  				select RecipientID, MailingID, @Now, SendTry + 1, UserName, Domain,
 				@ResultCode, NULL, TransactionLog, IsNull ( FirstAttempt, @Now )
 				from lyrActiveRecips with ( index ( PK_lyrActiveRecips ) ) 
  				where RecipientID = @RecipientID
  		else if @MemberID > 0 and @TransactionLog is not null and @ptrval is null
  			insert into lyrCompletedRecips ( RecipientID, MailingID, FinalAttempt, SendTry,
  				UserName, Domain, CompletionStatusID, MemberID, TransactionLog, FirstAttempt )
  				select RecipientID, MailingID, @Now, SendTry + 1, NULL, NULL,
  				@ResultCode, @MemberID,	@TransactionLog, IsNull ( FirstAttempt, @Now )
  				from lyrActiveRecips with ( index ( PK_lyrActiveRecips ) ) 
  				where RecipientID = @RecipientID
  		else if @MemberID > 0 and @TransactionLog is not null and @ptrval is not null
  			insert into lyrCompletedRecips ( RecipientID, MailingID, FinalAttempt, SendTry,
  				UserName, Domain, CompletionStatusID, MemberID, TransactionLog, FirstAttempt )
  				select RecipientID, MailingID, @Now, SendTry + 1, NULL, NULL,
  				@ResultCode, @MemberID,	TransactionLog, IsNull ( FirstAttempt, @Now )
  				from lyrActiveRecips with ( index ( PK_lyrActiveRecips ) ) 
  				where RecipientID = @RecipientID 
else if @MemberID > 0 and @TransactionLog is null and @ptrval is null
  			insert into lyrCompletedRecips ( RecipientID, MailingID, FinalAttempt, SendTry,
  				UserName, Domain, CompletionStatusID, MemberID, FirstAttempt )
  				select RecipientID, MailingID, @Now, SendTry + 1, NULL, NULL,
  				@ResultCode, @MemberID, IsNull ( FirstAttempt, @Now )
  				from lyrActiveRecips with ( index ( PK_lyrActiveRecips ) ) 
  				where RecipientID = @RecipientID
  		else if @MemberID > 0 and @TransactionLog is null and @ptrval is not null
  			insert into lyrCompletedRecips ( RecipientID, MailingID, FinalAttempt, SendTry,
  				UserName, Domain, CompletionStatusID, MemberID, TransactionLog, FirstAttempt )
  				select RecipientID, MailingID, @Now, SendTry + 1, NULL, NULL,
  				@ResultCode, @MemberID, TransactionLog, IsNull ( FirstAttempt, @Now )
  				from lyrActiveRecips with ( index ( PK_lyrActiveRecips ) ) 
  				where RecipientID = @RecipientID
  
  		if @@error != 0
  		begin
  			rollback transaction
 			raiserror ( 'Insert into lyrCompletedRecips failed.  Error: %d', 11, 3, @@error )
 			set nocount off
  			return
  		end
  
  		delete from lyrActiveRecips 
  		where RecipientID = @RecipientID
  
  		if @@error != 0
  		begin
  			rollback transaction
 			raiserror ( 'Delete from lyrActiveRecips failed.  Error: %d', 11, 4, @@error )
 			set nocount off
  			return
  		end
  
  		commit tran
 	 end
GO
