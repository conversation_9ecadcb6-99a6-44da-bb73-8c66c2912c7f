USE memberCentral
GO

/*
The following actions are performed in this script:
- ALTER sites TBL 
- ALTER cms_ContentVersions TBL 
- CREATE "Send Page Change Update Notification Emails" Scheduled Task
- ALTER cms_createContent SP 
- ALTER cms_createContentObject SP 
- ALTER cms_createContentVersion SP 
- ALTER cms_updateContent SP 
- ALTER an_addNotice SP 
- ALTER bl_createEntry SP 
- ALTER [cms_createApplicationInstanceCommunity] SP 
- ALTER [cms_createApplicationInstanceMemberDirectory] SP 
- ALTER [cms_createApplicationInstanceReferral] SP 
- ALTER [cms_createApplicationInstanceSocialNetwork] SP 
- ALTER cms_createApplicationInstanceStore SP 
- ALTER cms_createContentField SP 
- ALTER cms_createDefaultPages SP 
- ALTER co_createComment SP 
- ALTER createSite SP 
- ALTER email_createEmailBlast SP 
- ALTER et_createEmailTemplate SP 
- ALTER ev_createEvent SP 
- ALTER ev_createRegistration SP 
- ALTER ev_createSponsor SP 
- ALTER menu_createMenu SP 
- ALTER migrate_store SP 
- ALTER mp_insertProfile SP 
- ALTER nt_addNote SP 
- ALTER store_createProduct SP 
- ALTER sub_createSubscription SP 
- ALTER sub_createSubscriptionType SP 
- ALTER ams_importMemberData_holdingToPerm SP 
- ALTER email_copyEmailBlast SP 
- ALTER ev_copyEvent SP 
*/

print '%%%%%%%%%% ALTER sites TBL %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO
alter table sites
	add pageChangeUpdateNotificationEmails varchar(400) NULL
GO

print '%%%%%%%%%% ALTER cms_ContentVersions TBL %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO
alter table cms_ContentVersions
	add contributorMemberID int null
GO

declare @contributorMemberID int

select 
	@contributorMemberID  = memberID
 from 
	ams_members 
where 
	orgID =  1
	and memberNumber = 'SYSTEM'
	and status = 'A'

update
	cms_ContentVersions
set
	contributorMemberID = @contributorMemberID
GO

alter table cms_ContentVersions 
	alter column contributorMemberID int not null
GO

print '%%%%%%%%%% CREATE "Send Page Change Update Notification Emails" Scheduled Task  %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

declare @taskID int

insert into dbo.scheduledTasks (
	[name],
	nextRunDate,
	interval,
	intervalTypeID,
	taskCFC,
	timeoutMinutes,
	disabled,
	siteid
)
values (
	'Send Page Change Update Notification Emails',
	getDate(),
	4,
	4,
	'model.scheduledTasks.tasks.sendPageChangeUpdateNotificationEmails',
	10,
	1,
	1
)

select @taskID = SCOPE_IDENTITY()	

insert into platformstats.dbo.scheduledTaskHistory(
	taskID,
	statusTypeID,
	dateStarted,
	dateEnded,
	serverid,
	dateLastUpdated
)
select 
	top 1 
	@taskID,
	statusTypeID,
	dateStarted,
	dateEnded,
	serverid,
	dateLastUpdated
from 
	platformstats.dbo.scheduledTaskHistory 
order by 
	taskid desc
GO

print '%%%%%%%%%% ALTER cms_createContent SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROC [dbo].[cms_createContent]
@siteID int,
@pageID int,
@zoneID int,
@resourceTypeID int,
@siteResourceStatusID int,
@isSSL bit,
@isHTML bit,
@languageID int,
@isActive bit,
@contentTitle varchar(200),
@contentDesc varchar(400),
@rawContent varchar(max),
@memberID int = NULL,
@contentID int OUTPUT,
@contentSiteResourceID int OUTPUT

AS

BEGIN TRAN

declare @rc int, @siteResourceID int, @pzrID int

-- ensure contentID is null (can be passed in)
SELECT @contentID = null, @contentSiteResourceID = null
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@resourceTypeID, @siteResourceStatusID=@siteResourceStatusID, @isSSL=@isSSL, @isHTML=@isHTML, @languageID=@languageID, @isActive=@isActive, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawContent=@rawContent, @memberID=@memberID, @contentID=@contentID OUTPUT, @siteResourceID=@contentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- add content resource to zone
EXEC @rc = dbo.cms_createPageZoneResource @pageID=@pageID, @zoneID=@zoneID, @siteResourceID=@contentSiteResourceID, @pzrID=@pzrID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

IF @@TRANCOUNT > 0 COMMIT TRAN

-- normal exit
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @contentID = 0
	SELECT @contentSiteResourceID = 0
	RETURN -1

GO

print '%%%%%%%%%% ALTER cms_createContentObject SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROC [dbo].[cms_createContentObject]
@siteID int,
@resourceTypeID int,
@parentSiteResourceID int = NULL,
@siteResourceStatusID int,
@isSSL bit,
@isHTML bit,
@languageID int,
@isActive bit,
@contentTitle varchar(200),
@contentDesc varchar(400),
@rawContent varchar(max),
@memberID int = NULL,
@contentID int OUTPUT,
@siteResourceID int OUTPUT

AS

BEGIN TRAN

declare @rc int, @contentLanguageID int, @contentVersionID int

-- ensure contentID and siteResourceID are null (can be passed in)
SELECT @contentID = null
SELECT @siteResourceID = null

-- first create a resourceID for the content
exec dbo.cms_createSiteResource
	@resourceTypeID = @resourceTypeID,
	@siteResourceStatusID = @siteResourceStatusID,
	@siteID = @siteid,
	@isVisible = 1,
	@parentSiteResourceID = @parentSiteResourceID,
	@siteResourceID   = @siteResourceID OUTPUT

-- add the content
INSERT INTO dbo.cms_content (siteID, siteResourceID, isSSL, isHTML, dateCreated)
VALUES (@siteid, @siteResourceID, @isSSL, @isHTML, getdate())
	IF @@ERROR <> 0 GOTO on_error
	SELECT @contentID = SCOPE_IDENTITY()

-- add content language
INSERT INTO dbo.cms_contentLanguages (contentID, languageID, contentTitle, contentDesc, dateCreated, dateModified)
VALUES (@contentID, @languageID, isnull(@contentTitle,''), isnull(@contentDesc,''), getdate(), getdate())
	IF @@ERROR <> 0 GOTO on_error
	SELECT @contentLanguageID = SCOPE_IDENTITY()

-- add content version
EXEC @rc = dbo.cms_createContentVersion @contentLanguageID=@contentLanguageID, @rawContent=@rawContent, @isActive=@isActive, @memberID=@memberID, @contentVersionID=@contentVersionID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

IF @@TRANCOUNT > 0 COMMIT TRAN

-- normal exit
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @contentID = 0
	RETURN -1
GO

print '%%%%%%%%%% ALTER cms_createContentVersion SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROC [dbo].[cms_createContentVersion]
@contentLanguageID int,
@rawContent varchar(max),
@isActive bit,
@memberID int = NULL,
@contentVersionID int OUTPUT

AS

-- ensure @contentVersionID is null (can be passed in)
SELECT @contentVersionID = null


BEGIN TRAN

-- if isactive = 1, then deactivate all other versions of this contentLanguageID
IF @isactive = 1 BEGIN
	UPDATE dbo.cms_contentVersions
	SET isActive = 0
	WHERE contentLanguageID = @contentLanguageID
		IF @@ERROR <> 0 GOTO on_error
END

declare @contributorMemberID int

set @contributorMemberID = @memberID

IF @contributorMemberID is NULL BEGIN
	select 
		@contributorMemberID  = memberID
	 from 
		ams_members 
	where 
		orgID = 1
		and MemberNumber = 'SYSTEM'
		and status = 'A'
END

-- add content version
INSERT INTO dbo.cms_contentVersions (contentLanguageID, rawContent, isActive, dateCreated, contributorMemberID)
VALUES (@contentLanguageID, @rawContent, @isactive, getdate(), @contributorMemberID)
	IF @@ERROR <> 0 GOTO on_error
	SELECT @contentVersionID = SCOPE_IDENTITY()


-- update contentLanguages dateModified
update dbo.cms_contentLanguages set dateModified = getdate()
where contentLanguageID = @contentLanguageID


IF @@TRANCOUNT > 0 COMMIT TRAN

-- normal exit
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @contentVersionID = 0
	RETURN -1
GO

print '%%%%%%%%%% ALTER cms_updateContent SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROC [dbo].[cms_updateContent]
@contentID int,
@languageID int,
@isSSL bit,
@isHTML bit,
@contentTitle varchar(200),
@contentDesc varchar(400),
@rawcontent varchar(max),
@memberID int = NULL

AS

BEGIN TRAN

declare @updateDate datetime

set @updateDate = getdate()

-- update content
update dbo.cms_content
set isSSL=@isSSL, isHTML=@isHTML
where contentID = @contentID
	IF @@ERROR <> 0 GOTO on_error

-- if language is there, update it if anything changed -- otherwise add it if new
declare @contentLanguageID int
SELECT @contentLanguageID = contentLanguageID from dbo.cms_contentLanguages where contentID = @contentID and languageID = @languageID
IF @contentLanguageID is null 
	BEGIN
		INSERT INTO dbo.cms_contentLanguages (contentID, languageID, contentTitle, contentDesc, dateCreated, dateModified)
		VALUES (@contentID, @languageID, @contentTitle, @contentDesc, @updateDate, @updateDate)
			IF @@ERROR <> 0 GOTO on_error
			SELECT @contentLanguageID = SCOPE_IDENTITY()
	END

ELSE
	BEGIN
		declare @curr_contentTitle varchar(200), @curr_contentDesc varchar(400)
		select @curr_contentTitle = isnull(contentTitle,''), @curr_contentDesc = isnull(contentDesc,'')
			from dbo.cms_contentLanguages
			where contentLanguageID = @contentLanguageID
		IF @curr_contentTitle collate SQL_Latin1_General_CP1_CS_AS <> @contentTitle collate SQL_Latin1_General_CP1_CS_AS
		OR @curr_contentDesc collate SQL_Latin1_General_CP1_CS_AS <> @contentDesc collate SQL_Latin1_General_CP1_CS_AS
			BEGIN
				update dbo.cms_contentLanguages
				set contentTitle = @contentTitle, 
					contentDesc = @contentDesc,
					dateModified = @updateDate
				where contentLanguageID = @contentLanguageID
					IF @@ERROR <> 0 GOTO on_error
			END
	END

-- Is this a page content? If so, update dateModified
-- If it exists, we will update the pageLanguage row that matches the content language, if not we update site default
declare @pageLanguageID int
select 
	@pageLanguageID = isnull(pl.pageLanguageID,pldefault.pageLanguageID)
from
	cms_content c WITH(NOLOCK)
	inner join cms_siteResources sr WITH(NOLOCK) on 
		sr.siteResourceID = c.siteResourceID
		and c.contentID = @contentID
	inner join cms_siteResourceTypes srt WITH(NOLOCK)  on 
		srt.resourceTypeID = sr.resourceTypeID
		and srt.resourceType = 'UserCreatedContent'
	inner join dbo.cms_pageZonesResources pzr WITH(NOLOCK) on 
		pzr.siteResourceID = sr.siteResourceID
	inner join dbo.cms_pages p WITH(NOLOCK) on 
		p.pageID = pzr.pageID
	inner join sites s on
		s.siteID = p.siteID
	left outer join dbo.cms_pageLanguages pl WITH(NOLOCK) on
		pl.pageID = p.pageID
		and pl.languageID = @languageID
	left outer join dbo.cms_pageLanguages pldefault WITH(NOLOCK) on
		pldefault.pageID = p.pageID
		and pldefault.languageID = s.defaultLanguageID

IF @pageLanguageID is not null
BEGIN
	update
		cms_pageLanguages
	set
		dateModified = @updateDate
	where
		pageLanguageID = @pageLanguageID
END	

-- has anything changed in the content? If so, create a new version of it
declare @curr_rawContent varchar(max), @rc int, @contentVersionID int
select TOP 1 @curr_rawContent = rawContent
	from dbo.cms_contentVersions
	where contentLanguageID = @contentLanguageID
	and isActive = 1
IF (@curr_rawContent IS NULL) OR (@curr_rawContent collate SQL_Latin1_General_CP1_CS_AS <> @rawcontent collate SQL_Latin1_General_CP1_CS_AS)
	BEGIN
		EXEC @rc = dbo.cms_createContentVersion @contentLanguageID=@contentLanguageID, @rawContent=@rawContent, @isActive=1, @memberID=@memberID, @contentVersionID=@contentVersionID OUTPUT
			IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	END

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO
print '%%%%%%%%%% ALTER an_addNotice SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROCEDURE [dbo].[an_addNotice] 
	@siteID int,
	@centerID int,
	@languageID int,
	@creatorMemberID int,
	@noticeName varchar(100),
	@noticeTitle varchar(200),
	@noticeDetails varchar(max),
	@isNoticeDetailsHTML bit,
	@startDate datetime,
	@endDate datetime,
	@emailNotice bit,
	@emailDateScheduled datetime,
	@noticeID int OUTPUT
AS
BEGIN TRAN

DECLARE @AnnouncementsResourceTypeID int,
		@AnnouncementNoticeResourceTypeID int,
		@centerSiteResourceID int,
		@appCreatedContentResourceTypeID int,
		@siteResourceStatusID int,
		@thisNoticeResourceID int,
		@thisNoticeContentID int,
		@thisNoticeContentResourceID int,
		@noticeViewFunctionID int,
		@noticeEditFunctionID int,
		@noticeDeleteFunctionID int,
		@noticeReceiveEmailFunctionID int,
		@defaultViewFunctionID int,
		@defaultEditFunctionID int,
		@defaultDeleteFunctionID int,
		@defaultReceiveEmailFunctionID int,
		@trashID int,
		@applicationInstanceID int

select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')
select @AnnouncementNoticeResourceTypeID  = dbo.fn_getResourceTypeId('Announcement')
select @AnnouncementsResourceTypeID  = dbo.fn_getResourceTypeId('Announcements')
select @siteResourceStatusID = dbo.fn_getResourceStatusId('Active')

select @noticeViewFunctionID = dbo.fn_getResourceFunctionID('View',@AnnouncementNoticeResourceTypeID)
select @noticeEditFunctionID = dbo.fn_getResourceFunctionID('Edit',@AnnouncementNoticeResourceTypeID)
select @noticeDeleteFunctionID = dbo.fn_getResourceFunctionID('Delete',@AnnouncementNoticeResourceTypeID)
select @noticeReceiveEmailFunctionID = dbo.fn_getResourceFunctionID('ReceiveAnnouncementEmail',@AnnouncementNoticeResourceTypeID)

select @defaultViewFunctionID = dbo.fn_getResourceFunctionID('ViewAnnouncementByDefault',@AnnouncementsResourceTypeID)
select @defaultEditFunctionID = dbo.fn_getResourceFunctionID('EditAnnouncementByDefault',@AnnouncementsResourceTypeID)
select @defaultDeleteFunctionID = dbo.fn_getResourceFunctionID('DeleteAnnouncementByDefault',@AnnouncementsResourceTypeID)
select @defaultReceiveEmailFunctionID = dbo.fn_getResourceFunctionID('ReceiveAnnouncementEmailByDefault',@AnnouncementsResourceTypeID)


select @centerSiteResourceID = ai.siteResourceID, @applicationInstanceID = c.applicationInstanceID
	from an_centers c
	inner join cms_applicationinstances ai on c.applicationInstanceID = ai.applicationInstanceID
	where c.centerID = @centerID

-- first create a resourceID for the notice
	exec dbo.cms_createSiteResource
		@resourceTypeID = @AnnouncementNoticeResourceTypeID,
		@siteResourceStatusID = @siteResourceStatusID,
		@siteID = @siteid,
		@isVisible = 1,
		@parentSiteResourceID = @centerSiteResourceID,
		@siteResourceID   = @thisNoticeResourceID OUTPUT

-- create event content object
exec dbo.cms_createContentObject @siteID, @appCreatedContentResourceTypeID, null, @siteResourceStatusID, 0, @isNoticeDetailsHTML, @languageID, 1, @noticeTitle, '', @noticeDetails, @creatorMemberID, @thisNoticeContentID OUTPUT, @thisNoticeContentResourceID OUTPUT
	IF @@ERROR <> 0 GOTO on_error

-- insert notice
insert into dbo.an_notices (noticeName, noticeContentID,  creatorMemberID, startDate, endDate, emailNotice, emailDateScheduled, siteResourceID)
values (@noticeName, @thisNoticeContentID, @creatorMemberID, @startDate, @endDate, @emailNotice, @emailDateScheduled, @thisNoticeResourceID)
	IF @@ERROR <> 0 GOTO on_error
	select @noticeID = SCOPE_IDENTITY()

-- update the parentSiteResourceID on the content object.
update dbo.cms_siteResources 
set parentSiteResourceID = @thisNoticeResourceID
where siteResourceID = @thisNoticeContentResourceID
	IF @@ERROR <> 0 GOTO on_error

-- add notice to center
insert into an_centerNotices(centerID, sourceCenterID, sourceNoticeID)
values (@centerID, @centerID, @noticeID)
	IF @@ERROR <> 0 GOTO on_error


-- get applicationTypeID
DECLARE @applicationTypeID int
select @applicationTypeID = dbo.fn_getApplicationTypeIDFromName('Announcements')

-- create activity log entry
EXEC platformstats.dbo.act_recordLog @memberID=@creatorMemberID, @activityType='post', 
	@applicationTypeID=@applicationTypeID, @applicationInstanceID=@applicationInstanceID,
	@supportSiteResourceID=@thisNoticeResourceID, @supportMemberID=null, @supportMessage=null
	IF @@ERROR <> 0 GOTO on_error


-- resource rights
exec dbo.cms_createSiteResourceRight
	@siteID=@siteid, 
	@siteResourceID=@thisNoticeResourceID,
	@include=1,
	@functionID=@noticeViewFunctionID,
	@roleID=null,
	@groupID=null,
	@memberID=null,
	@inheritedRightsResourceID=@centerSiteResourceID,
	@inheritedRightsFunctionID=@defaultViewFunctionID,
	@resourceRightID=@trashID OUTPUT
	IF @@ERROR <> 0 GOTO on_error

exec dbo.cms_createSiteResourceRight
	@siteID=@siteid, 
	@siteResourceID=@thisNoticeResourceID,
	@include=1,
	@functionID=@noticeEditFunctionID,
	@roleID=null,
	@groupID=null,
	@memberID=null,
	@inheritedRightsResourceID=@centerSiteResourceID,
	@inheritedRightsFunctionID=@defaultEditFunctionID,
	@resourceRightID=@trashID OUTPUT
	IF @@ERROR <> 0 GOTO on_error

exec dbo.cms_createSiteResourceRight
	@siteID=@siteid, 
	@siteResourceID=@thisNoticeResourceID,
	@include=1,
	@functionID=@noticeDeleteFunctionID,
	@roleID=null,
	@groupID=null,
	@memberID=null,
	@inheritedRightsResourceID=@centerSiteResourceID,
	@inheritedRightsFunctionID=@defaultDeleteFunctionID,
	@resourceRightID=@trashID OUTPUT
	IF @@ERROR <> 0 GOTO on_error

exec dbo.cms_createSiteResourceRight
	@siteID=@siteid, 
	@siteResourceID=@thisNoticeResourceID,
	@include=1,
	@functionID=@noticeReceiveEmailFunctionID,
	@roleID=null,
	@groupID=null,
	@memberID=null,
	@inheritedRightsResourceID=@centerSiteResourceID,
	@inheritedRightsFunctionID=@defaultReceiveEmailFunctionID,
	@resourceRightID=@trashID OUTPUT
	IF @@ERROR <> 0 GOTO on_error

IF @@TRANCOUNT > 0 COMMIT TRAN

-- normal exit
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @noticeID = 0
	RETURN -1

GO

print '%%%%%%%%%% ALTER bl_createEntry SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROC [dbo].[bl_createEntry]
@siteid int,
@languageID int,
@blogID int,
@siteResourceStatusID int,
@blogTitle varchar(500),
@blogBody varchar(max),
@isVisible bit,
@contributorMemberID int,
@blogURL varchar(500),
@memberWall int = null,
@blogEntryID int OUTPUT

AS

-- ensure @blogEntryID is null (can be passed in)
SELECT @blogEntryID = null


declare @rc int, 
	@siteResourceID int,
	@appCreatedContentResourceTypeID int, 
	@activeSiteResourceStatusID int,
	@maincontentID int, 
	@maincontentSiteResourceID int,
	@blogEntryResourceTypeID int

select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')
select @activeSiteResourceStatusID = dbo.fn_getResourceStatusId('Active')
select @blogEntryResourceTypeID = dbo.fn_getResourceTypeId('BlogEntry')

-- get applicationTypeID
DECLARE @applicationTypeID int
select @applicationTypeID = dbo.fn_getApplicationTypeIDFromName('Blog')

-- get the blogs applicationInstanceID
DECLARE @applicationInstanceID int
SELECT	@applicationInstanceID = bl.applicationInstanceID
	from dbo.bl_blog as bl
	INNER JOIN cms_applicationInstances ai on ai.applicationInstanceID = bl.applicationInstanceID
	where bl.blogID = @blogID
		AND ai.siteID = @siteID

IF @applicationInstanceID IS NOT NULL 
BEGIN TRAN

--check to see if user has a notebook. If not, create one
DECLARE @notebookID int, @memberName varchar(100), @wallNotebookName varchar(200), @wallNotebookDesc varchar(200), @addInheritance bit, @notebookSiteResourceID int

SELECT	@notebookID = notebookID, @notebookSiteResourceID = sr.siteResourceID
FROM	bl_notebooks n
inner join cms_siteResources sr on n.siteResourceID = sr.siteResourceID
inner join cms_siteResourceStatuses srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
WHERE	creatorMemberID = @contributorMemberID
	AND blogID = @blogID
	IF @@ERROR <> 0 GOTO on_error

IF @notebookID IS NULL
BEGIN

SELECT @membername = isnull(m.firstName,'') + ' ' + isnull(m.lastName,'')
FROM	ams_members m 
WHERE	m.memberID = @contributorMemberID
	AND m.status = 'A'
	IF @@ERROR <> 0 GOTO on_error

	SELECT @addInheritance = 1
	SELECT @wallNotebookName = @membername + '''s Wall Notebook'	
	SELECT @wallNotebookDesc = @membername + '''s Wall Notebook'

	EXEC @rc = dbo.bl_createNotebook @siteid=@siteid, @notebookName=@wallNotebookName, @notebookDesc=@wallNotebookDesc			, @applicationInstanceID=@applicationInstanceID, @creatorMemberID=@contributorMemberID, @addInheritance=@addInheritance, @notebookID=@notebookID OUTPUT 
		IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

	SELECT	@notebookSiteResourceID = siteResourceID
	FROM	bl_notebooks n
	WHERE	notebookID = @notebookID
		IF @@ERROR <> 0 GOTO on_error

END

-- first create a resourceID for the blog entry
	exec @rc = dbo.cms_createSiteResource
		@resourceTypeID = @blogEntryResourceTypeID,
		@siteResourceStatusID = @siteResourceStatusID,
		@siteID = @siteid,
		@isVisible = @isVisible,
		@parentSiteResourceID = @notebookSiteResourceID,
		@siteResourceID   = @siteResourceID OUTPUT
		IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
-- inherit entry view right from notebook view right

DECLARE @viewFunctionID int, @trashID int
SELECT @viewFunctionID = dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('BlogEntry'))

EXEC @rc = dbo.cms_createSiteResourceRight
	@siteID = @siteID,
	@siteResourceID = @siteResourceID,
	@include = 1,
	@functionID = @viewFunctionID,
	@roleID=null,
	@groupID=null,
	@memberID=null,
	@inheritedRightsResourceID=@notebookSiteResourceID,
	@inheritedRightsFunctionID=@viewFunctionID,
	@resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- inherit entry CanComment right from notebook CanCommentByDefault
DECLARE @CanCommentByDefaultFunctionID int, @CanCommentFunctionID int

SELECT @CanCommentFunctionID = dbo.fn_getResourceFunctionID('CanComment',dbo.fn_getResourceTypeID('BlogEntry'))

EXEC @rc = dbo.cms_createSiteResourceRight
	@siteID = @siteID,
	@siteResourceID = @siteResourceID,
	@include = 1,
	@functionID = @CanCommentFunctionID,
	@roleID=null,
	@groupID=null,
	@memberID=null,
	@inheritedRightsResourceID=@notebookSiteResourceID,
	@inheritedRightsFunctionID=@CanCommentFunctionID,
	@resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- create mainContent
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID,
	@parentSiteResourceID = @siteResourceID,
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@languageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent=@blogBody, @memberID=@contributorMemberID,
	@contentID=@maincontentID OUTPUT, @siteResourceID=@maincontentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

		
-- add the blog entry
INSERT INTO dbo.bl_entry (blogID, siteResourceID, blogTitle, dateCreated, contributorMemberID, blogContentID, blogURL, notebookID)
VALUES (@blogID, @siteResourceID, @blogTitle, getdate(),@contributorMemberID, @maincontentID, @blogURL,@notebookID)
	IF @@ERROR <> 0 GOTO on_error
	SELECT @blogEntryID = SCOPE_IDENTITY()

-- create activity log entry
EXEC @rc = platformstats.dbo.act_recordLog @memberID=@contributorMemberID, @activityType='post', 
	@applicationTypeID=@applicationTypeID, @applicationInstanceID=@applicationInstanceID,
	@supportSiteResourceID=@siteResourceID, @supportMemberID=@memberWall, @supportMessage=null
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

IF @@TRANCOUNT > 0 COMMIT TRAN

-- normal exit
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @blogEntryID = 0
	RETURN -1

GO

print '%%%%%%%%%% ALTER cms_createApplicationInstanceCommunity SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROCEDURE [dbo].[cms_createApplicationInstanceCommunity] 
@siteid int,
@languageID int,
@sectionID int,
@isVisible bit,
@pageName varchar(50),
@pageTitle varchar(200),
@pagedesc varchar(400),
@zoneID int,
@pageTemplateID int,
@subPageTemplateID int,
@pageModeID int,
@pgResourceTypeID int,
@pgParentResourceID int=null,
@allowReturnAfterLogin bit,
@applicationInstanceName varchar(100),
@applicationInstanceDesc varchar(200),
@applicationInstanceID int OUTPUT,
@siteResourceID int OUTPUT,
@pageID int OUTPUT

AS

BEGIN TRAN

declare @rc int, @commApplicationTypeID int, @communityID int, @appCreatedSectionResourceTypeID int
declare @rootSectionID int
DECLARE @templateid int, @zoneIDA int, @modeID int,
	@applicationWidgetTypeID int,
	@applicationWidgetInstanceName varchar(50),
	@applicationWidgetInstanceDesc varchar(100),
	@applicationWidgetInstanceID int,
	@widgetSiteResourceID int,
	@pageSiteResourceID int,
	@trashID int,
	@ovTemplateIDMobile int


declare @viewFunctionID int



select @applicationInstanceID = null
select @siteResourceID = null
select @pageID = null
select @ovTemplateIDMobile = null
select @commApplicationTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'Community'
select @appCreatedSectionResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedSection')
select @zoneIDA = dbo.fn_getZoneID('A')
select @modeID = dbo.fn_getModeID('Normal')

select @viewFunctionID = dbo.fn_getResourceFunctionID('View',@commApplicationTypeID)
	IF @@ERROR <> 0 GOTO on_error


-- create instance
EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, 
		@applicationTypeID=@commApplicationTypeID, @isVisible=@isVisible, @pageName=@pageName, 
		@pageTitle=@pageTitle, @pageDesc=@pagedesc, @zoneID=@zoneID, @pagetemplateid=@pageTemplateID,
		@pageModeID=@pageModeID, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID = @pgParentResourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
		@applicationInstanceName=@applicationInstanceName, @applicationInstanceDesc=@applicationInstanceDesc, 
		@applicationInstanceID=@applicationInstanceID OUTPUT, 
		@siteresourceID=@siteResourceID OUTPUT, 
		@pageID=@pageID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


select @pageSiteResourceID = siteResourceID from cms_pages where pageID = @pageID

-- create section for community
EXEC @rc = dbo.cms_createPageSection 
		@siteID=@siteID, 
		@sectionResourceTypeID=@appCreatedSectionResourceTypeID,
		@ovTemplateID=@subPageTemplateID,
		@ovTemplateIDMobile=@ovTemplateIDMobile, 
		@ovModeID=@modeID, 
		@parentSectionID=@sectionID, 
		@sectionName=@pageName,
		@sectionCode=@pageName, 
		@inheritPlacements=0,
		@sectionID=@rootSectionID OUTPUT
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- update parentSiteResourceID of section
update sr
set sr.parentSiteResourceID = @siteResourceID
from cms_pageSections s
inner join cms_siteResources sr on s.siteResourceID = sr.siteResourceID	and s.sectionID = @rootSectionID
IF @@ERROR <> 0 GOTO on_error
-- add community
INSERT INTO dbo.comm_communities (applicationInstanceID, communityName, communityDescription, rootSectionID)
VALUES (@applicationInstanceID, left(@pageTitle,100), @pagedesc, @rootSectionID)
	IF @@ERROR <> 0 GOTO on_error
	select @communityID = SCOPE_IDENTITY()

-- create homepage for community
DECLARE @AppSubPageTypeID int, @siteResourceStatusID int, @commpgTemplateID int
DECLARE @commpgName varchar(60), @commpgTitle varchar(210), @commpgPageID int, @communityPageID int
DECLARE @userCreatedContentResourceTypeID int, @contentID int, @contentSiteResourceID int
SELECT @AppSubPageTypeID = dbo.fn_getResourceTypeId('ApplicationSubPage')
select @siteResourceStatusID = dbo.fn_getResourceStatusId('Active')
select @commpgName = @pageName + 'Home'
select @userCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('UserCreatedContent')
EXEC @rc = dbo.cms_createPage @siteid=@siteid, @languageID=@languageID, @resourceTypeID=@AppSubPageTypeID, 
	@siteResourceStatusID=@siteResourceStatusID, @pgParentResourceID = @siteresourceID, @isVisible=@isVisible, @sectionid=@rootSectionID, 
	@ovTemplateID=NULL, @ovTemplateIDMobile=@ovTemplateIDMobile, @ovModeID=NULL, @pageName=@commpgName, @pageTitle='Home',
	@pageDesc=null, @keywords=null, @allowReturnAfterLogin=0, @inheritPlacements=1, @checkReservedNames=1, @pageID=@commpgPageID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 OR @commpgPageID = 0 GOTO on_error

UPDATE dbo.comm_communities 
SET defaultCommunityPageName = @commpgName
WHERE communityID = @communityID
	IF @@ERROR <> 0 GOTO on_error
EXEC @rc = dbo.cms_createContent @siteID=@siteID, @pageID=@commpgPageID, @zoneID=@zoneID, 
	@resourceTypeID=@userCreatedContentResourceTypeID, @siteResourceStatusID=@siteResourceStatusID,
	@isSSL=0, @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle='Welcome', 
	@contentDesc='Default welcome message for community', 
	@rawContent='Welcome to this community. Click around and see what''s here.',
	@memberID=NULL,
	@contentID=@contentID OUTPUT, @contentSiteResourceID=@contentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error



-- create navigation widget

select @applicationWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes
where applicationTypeID = @commApplicationTypeID and applicationWidgetTypeName = 'communityNavigation'



exec @rc = dbo.cms_createApplicationWidgetInstance
@siteid = @siteID,
@applicationInstanceID = @applicationInstanceID,
@applicationWidgetTypeID = @applicationWidgetTypeID,
@applicationWidgetInstanceName = 'Navigation',
@applicationWidgetInstanceDesc = 'Community Navigation',
@applicationWidgetInstanceID = @applicationWidgetInstanceID OUTPUT,
@siteResourceID = @widgetSiteResourceID OUTPUT
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

update cms_applicationWidgetInstances set
settingsXML = '<settings horizontal="True" communityHDR="True" />'
where applicationWidgetInstanceID = @applicationWidgetInstanceID 
IF @@ERROR <> 0 GOTO on_error

exec @rc = dbo.cms_createPageZoneResource
	@pageID = @pageID,
	@zoneID = @zoneID,
	@siteResourceID = @widgetSiteResourceID,
	@pzrID = @trashID OUTPUT
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

exec @rc = dbo.cms_moveZoneResourceUp
	@itemSiteResourceID=@widgetSiteResourceID, @containerSiteResourceID=@pageSiteResourceID, @zoneID=@zoneID
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

EXEC @rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@widgetSiteResourceID, @include=1, 
		@functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, 
		@inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@viewFunctionID, 
		@resourceRightID=@trashID OUTPUT
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


/* link view rights of all community page resources to community view right */


declare @SRID TABLE (autoid int IDENTITY(1,1), resourceID int, inheritedRightsResourceID int)
insert into @SRID (resourceID, inheritedRightsResourceID)
select pzr.siteResourceID as resourceID, ai.siteResourceID as inheritedRightsResourceID
from dbo.comm_communities comm 
inner join dbo.cms_applicationInstances ai on comm.applicationInstanceID = ai.applicationInstanceID
	and comm.communityID = @communityID
inner join dbo.cms_siteResources sr on parentSiteResourceID = ai.siteResourceID
inner join dbo.cms_pages p on sr.siteResourceID = p.siteResourceID
inner join dbo.cms_pageZonesResources pzr on p.pageID = pzr.pageID
	IF @@ERROR <> 0 GOTO on_error

DECLARE @minID int, @srr_resourceID int, @srr_inheritedRightsResourceID int, @resourceRightID int
SELECT @minID = min(autoid) from @SRID
WHILE @minID IS NOT NULL BEGIN
	SELECT @srr_resourceID=resourceID, @srr_inheritedRightsResourceID=inheritedRightsResourceID
	FROM @SRID
	WHERE autoid = @minID

	EXEC @rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@srr_resourceID, @include=1, 
		@functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, 
		@inheritedRightsResourceID=@srr_inheritedRightsResourceID, @inheritedRightsFunctionID=@viewFunctionID, 
		@resourceRightID=@resourceRightID OUTPUT
	IF @@ERROR <> 0 GOTO on_error

	SELECT @minID = min(autoid) from @SRID where autoid > @minID
END

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1

GO

print '%%%%%%%%%% ALTER cms_createApplicationInstanceMemberDirectory SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROCEDURE [dbo].[cms_createApplicationInstanceMemberDirectory] 
@siteid int,
@languageID int,
@sectionID int,
@isVisible bit,
@pageName varchar(50),
@pageTitle varchar(200),
@pagedesc varchar(400),
@zoneID int,
@pageTemplateID int,
@pageModeID int,
@pgResourceTypeID int,
@pgParentResourceID int = NULL,
@allowReturnAfterLogin bit,
@applicationInstanceName varchar(100),
@applicationInstanceDesc varchar(200),
@applicationInstanceID int OUTPUT,
@siteResourceID int OUTPUT,
@pageID int OUTPUT

AS

BEGIN TRAN

declare @rc int, @applicationTypeID int, @centerID int,
	@appCreatedContentResourceTypeID int, @activeSiteResourceStatusID int, @defaultLanguageID int,
	@searchContentID int, @searchContentSiteResourceID int

declare @resourceTypeID int, @appearInDirectoryFunctionID int, @trashID int
declare 
	@resultsShowDetailsLinkFunctionID int,
	@detailsShowMapFunctionID int,
	@resultsShowMapFunctionID int,
	@detailsShowPhotoFunctionID int,
	@resultsShowPhotoFunctionID int,
	@detailsShowVcardFunctionID int,
	@resultsShowVcardFunctionID int



select @applicationInstanceID = null
select @siteResourceID = null
select @pageID = null

select @applicationTypeID = applicationTypeID, @resourceTypeID=resourceTypeID
from dbo.cms_applicationTypes where applicationTypeName = 'Members'

select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedContent')
select @activeSiteResourceStatusID = dbo.fn_getResourceStatusID('Active')
select @defaultLanguageID = defaultLanguageID from sites where siteID = @siteID

select @appearInDirectoryFunctionID = dbo.fn_getResourceFunctionID('AppearInDirectory',@resourceTypeID)
select @resultsShowDetailsLinkFunctionID = dbo.fn_getResourceFunctionID('resultsShowDetailsLink',@resourceTypeID)
select @detailsShowMapFunctionID = dbo.fn_getResourceFunctionID('detailsShowMap',@resourceTypeID)
select @resultsShowMapFunctionID = dbo.fn_getResourceFunctionID('resultsShowMap',@resourceTypeID)
select @detailsShowPhotoFunctionID  = dbo.fn_getResourceFunctionID('detailsShowPhoto',@resourceTypeID)
select @resultsShowPhotoFunctionID  = dbo.fn_getResourceFunctionID('resultsShowPhoto',@resourceTypeID)
select @detailsShowVcardFunctionID  = dbo.fn_getResourceFunctionID('detailsShowVcard',@resourceTypeID)
select @resultsShowVcardFunctionID  = dbo.fn_getResourceFunctionID('resultsShowVcard',@resourceTypeID)


-- create instance
EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, 
		@applicationTypeID=@applicationTypeID, @isVisible=@isVisible, @pageName=@pageName, 
		@pageTitle=@pageTitle, @pageDesc=@pagedesc, @zoneID=@zoneID, @pagetemplateid=@pageTemplateID,
		@pageModeID=@pageModeID, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID = @pgParentResourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
		@applicationInstanceName=@applicationInstanceName, @applicationInstanceDesc=@applicationInstanceDesc, 
		@applicationInstanceID=@applicationInstanceID OUTPUT, 
		@siteresourceID=@siteResourceID OUTPUT, 
		@pageID=@pageID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- create searchcontentid
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@defaultLanguageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', @memberID=NULL, @contentID=@searchContentID OUTPUT, 
	@siteResourceID=@searchContentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- insert into memberDirectory table
INSERT INTO dbo.ams_memberDirectories (applicationInstanceID, maxsearchresults, recordsperpage, inactivemembersVisible, searchContentID)
VALUES (@applicationInstanceID, 50, 15, 0, @searchContentID)
	IF @@ERROR <> 0 GOTO on_error
	select @centerID = SCOPE_IDENTITY()


exec @rc = dbo.cms_createSiteResourceRight
	@siteID=@siteid, @siteResourceID=@siteResourceID, @include=1,
	@functionID=@resultsShowDetailsLinkFunctionID, @roleID=null, @groupID=null, @memberID=null,
	@inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@appearInDirectoryFunctionID, @resourceRightID=@trashID OUTPUT
IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
exec @rc = dbo.cms_createSiteResourceRight
	@siteID=@siteid, @siteResourceID=@siteResourceID, @include=1,
	@functionID=@detailsShowVcardFunctionID, @roleID=null, @groupID=null, @memberID=null,
	@inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@appearInDirectoryFunctionID, @resourceRightID=@trashID OUTPUT
IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
exec @rc = dbo.cms_createSiteResourceRight
	@siteID=@siteid, @siteResourceID=@siteResourceID, @include=1,
	@functionID=@resultsShowVcardFunctionID, @roleID=null, @groupID=null, @memberID=null,
	@inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@appearInDirectoryFunctionID, @resourceRightID=@trashID OUTPUT
IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
exec @rc = dbo.cms_createSiteResourceRight
	@siteID=@siteid, @siteResourceID=@siteResourceID, @include=1,
	@functionID=@detailsShowMapFunctionID, @roleID=null, @groupID=null, @memberID=null,
	@inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@appearInDirectoryFunctionID, @resourceRightID=@trashID OUTPUT
IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
exec @rc = dbo.cms_createSiteResourceRight
	@siteID=@siteid, @siteResourceID=@siteResourceID, @include=1,
	@functionID=@resultsShowMapFunctionID, @roleID=null, @groupID=null, @memberID=null,
	@inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@appearInDirectoryFunctionID, @resourceRightID=@trashID OUTPUT
IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
exec @rc = dbo.cms_createSiteResourceRight
	@siteID=@siteid, @siteResourceID=@siteResourceID, @include=1,
	@functionID=@detailsShowPhotoFunctionID, @roleID=null, @groupID=null, @memberID=null,
	@inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@appearInDirectoryFunctionID, @resourceRightID=@trashID OUTPUT
IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
exec @rc = dbo.cms_createSiteResourceRight
	@siteID=@siteid, @siteResourceID=@siteResourceID, @include=1,
	@functionID=@resultsShowPhotoFunctionID, @roleID=null, @groupID=null, @memberID=null,
	@inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@appearInDirectoryFunctionID, @resourceRightID=@trashID OUTPUT



-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

print '%%%%%%%%%% ALTER cms_createApplicationInstanceReferral SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROCEDURE [dbo].[cms_createApplicationInstanceReferral] 
	@siteid int,
	@languageID int,
	@sectionID int,
	@isVisible bit,
	@pageName varchar(50),
	@pageTitle varchar(200),
	@pagedesc varchar(400),
	@zoneID int,
	@pageTemplateID int,
	@pageModeID int,
	@pgResourceTypeID int,
	@defaultGLAccountID int,
	@allowReturnAfterLogin bit,
	@applicationInstanceName varchar(100),
	@applicationInstanceDesc varchar(200),
	@emailRecipient varchar(200) = NULL ,
	@applicationInstanceID int OUTPUT,
	@siteResourceID int OUTPUT,
	@pageID int OUTPUT
AS

BEGIN TRAN

declare 
	@rc int, 
	@applicationTypeID int, 
	@appCreatedContentResourceTypeID int, 
	@activeSiteResourceStatusID int,
	@maincontentID int, 
	@maincontentSiteResourceID int,
	@documentSectionName varchar(50), 
	@appCreatedSectionResourceTypeID int, 
	@rootSectionID int

select @applicationInstanceID = null
select @siteResourceID = null
select @pageID = null
select @appCreatedSectionResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedSection')
select @applicationTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'Referrals'
select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')
select @activeSiteResourceStatusID = dbo.fn_getResourceStatusId('Active')

-- create instance
EXEC @rc = dbo.cms_createApplicationInstance 
	@siteid=@siteid, 
	@languageID=@languageID, 
	@sectionID=@sectionID, 
	@applicationTypeID=@applicationTypeID, 
	@isVisible=@isVisible, 
	@pageName=@pageName, 
	@pageTitle=@pageTitle, 
	@pageDesc=@pagedesc, 
	@zoneID=@zoneID, 
	@pagetemplateid=@pageTemplateID,
	@pageModeID=@pageModeID, 
	@pgResourceTypeID=@pgResourceTypeID, 
	@pgParentResourceID=null, 
	@allowReturnAfterLogin=@allowReturnAfterLogin, 
	@applicationInstanceName=@applicationInstanceName, 
	@applicationInstanceDesc=@applicationInstanceDesc, 
	@applicationInstanceID=@applicationInstanceID OUTPUT, 
	@siteresourceID=@siteResourceID OUTPUT, 
	@pageID=@pageID OUTPUT
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- create mainContent
EXEC @rc = dbo.cms_createContentObject 
	@siteID=@siteID, 
	@resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, 
	@isSSL=0, 
	@isHTML=1, 
	@languageID=@languageID, 
	@isActive=1, 
	@contentTitle=null, 
	@contentDesc=null, 
	@rawContent='Welcome to the Referral Information Service.', 
	@memberID=NULL,
	@contentID=@maincontentID OUTPUT, 
	@siteResourceID=@maincontentSiteResourceID OUTPUT
IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- add referral
INSERT INTO dbo.ref_referrals (
	applicationInstanceID, 
	mainContentID, 
	emailRecipient, 
	GLAccountID,
	dateCreated
)
VALUES (
	@applicationInstanceID, 
	@maincontentID, 
	@emailRecipient, 
	@defaultGLAccountID,
	getDate()	
)
IF @@ERROR <> 0 GOTO on_error

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO

print '%%%%%%%%%% ALTER cms_createApplicationInstanceSocialNetwork SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROCEDURE [dbo].[cms_createApplicationInstanceSocialNetwork] 
@siteid int,
@languageID int,
@sectionID int,
@isVisible bit,
@pageName varchar(50),
@pageTitle varchar(200),
@pagedesc varchar(400),
@zoneID int,
@pageTemplateID int,
@subPageTemplateID int,
@masterSiteID int,
@addressTypeID int,
@emailTypeID int,
@phoneTypeID int,
@pageModeID int,
@pgResourceTypeID int,
@pgParentResourceID int=null,
@allowReturnAfterLogin bit,
@applicationInstanceName varchar(100),
@applicationInstanceDesc varchar(200),
@creatorMemberID int,
@applicationInstanceID int OUTPUT,
@siteResourceID int OUTPUT,
@pageID int OUTPUT

AS

BEGIN TRAN

declare @rc int, @snApplicationTypeID int, @socialNetworkID int, @appCreatedSectionResourceTypeID int
declare @masterOrgID int, @masterSocialNetworkGroupID int, @masterSocialNetworkID int, @masterSocialNetworkApplicationInstanceID int
declare @rootSectionID int
declare @appWidgetTypeID int, @applicationWidgetInstanceID int, @applicationWidgetSiteResourceID int, @zoneIDA int, @zoneIDB int, @trashID int;
declare @participateFunctioniD int, @viewFunctionID int, @SNResourceTypeID int, @AddBlogFunctionID int, @editOwnFunctionID int, @deleteOwnFunctionID int, @canCommentFunctionID int


declare 
	@poolTypeID int,
	@SNMasterInstancePoolRoleTypeID int,
	@SNChildInstancePoolRoleTypeID int,
	@SNPoolID int



select @applicationInstanceID = null
select @siteResourceID = null
select @masterOrgID = null
select @pageID = null
select @snApplicationTypeID = applicationTypeID, @SNResourceTypeID = resourceTypeID from dbo.cms_applicationTypes where applicationTypeName = 'SocialNetwork'
select @appWidgetTypeID = applicationWidgetTypeID from dbo.cms_applicationWidgetTypes where applicationWidgetTypeName='socialNetworkNavigation' and applicationTypeID = @snApplicationTypeID
select @zoneIDA = dbo.fn_getZoneID('A')
select @zoneIDB = dbo.fn_getZoneID('B')

select 
	@participateFunctioniD = dbo.fn_getResourceFunctionID('Participate', @SNResourceTypeID),
	@viewFunctionID = dbo.fn_getResourceFunctionID('View', @SNResourceTypeID),
	@AddBlogFunctionID = dbo.fn_getResourceFunctionID('View', dbo.fn_getResourceTypeID('Blog')),
	@editOwnFunctionID = dbo.fn_getResourceFunctionID('editOwn', dbo.fn_getResourceTypeID('Blog')),
	@deleteOwnFunctionID = dbo.fn_getResourceFunctionID('deleteOwn', dbo.fn_getResourceTypeID('Blog')),
	@canCommentFunctionID = dbo.fn_getResourceFunctionID('canComment', dbo.fn_getResourceTypeID('Blog')),
	@appCreatedSectionResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedSection'),
	@masterOrgID =dbo.fn_getOrgIDFromSiteID(@masterSiteID),
	@poolTypeID = dbo.fn_getResourcePoolTypeID ('SNConfig'),
	@SNMasterInstancePoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'MasterInstance'),
	@SNChildInstancePoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'ChildInstance')

if @masterSiteID <> @siteID
	select 
		@masterSocialNetworkID=socialNetworkID,
		@masterSocialNetworkGroupID = masterSocialNetworkGroupID,
		@masterSocialNetworkApplicationInstanceID = ai.applicationInstanceID,
		@SNPoolID = srpm.poolID
	from sn_socialNetworks sn
	inner join cms_applicationInstances ai
		on sn.applicationinstanceID = ai.applicationInstanceID
		and ai.siteID = @masterSiteID
	inner join cms_siteResources sr on ai.siteResourceID = sr.siteResourceID
	inner join cms_siteResourceStatuses srs
		on sr.siteResourceStatusID = srs.siteResourceStatusID
		and srs.siteResourceStatusDesc = 'Active'
	inner join cms_siteResourcePoolMembers srpm
		on srpm.siteResourceID = ai.siteResourceID
		and srpm.poolRoleTypeID = @SNMasterInstancePoolRoleTypeID
	inner join cms_siteResourcePools srp
		on srpm.poolID = srp.poolID
else
	select @masterSocialNetworkID=null,
		@masterSocialNetworkGroupID = null,
		@masterSocialNetworkApplicationInstanceID = null,
		@SNPoolID = null


-- create instance
EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, 
		@applicationTypeID=@snApplicationTypeID, @isVisible=@isVisible, @pageName=@pageName, 
		@pageTitle=@pageTitle, @pageDesc=@pagedesc, @zoneID=@zoneID, @pagetemplateid=@pageTemplateID,
		@pageModeID=@pageModeID, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID = @pgParentResourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
		@applicationInstanceName=@applicationInstanceName, @applicationInstanceDesc=@applicationInstanceDesc, 
		@applicationInstanceID=@applicationInstanceID OUTPUT, 
		@siteresourceID=@siteResourceID OUTPUT, 
		@pageID=@pageID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
print 'Created SN instance'
-- create section for Social Network
EXEC @rc = dbo.cms_createPageSection 
		@siteID=@siteID, 
		@sectionResourceTypeID=@appCreatedSectionResourceTypeID,
		@ovTemplateID=@subPageTemplateID, 
		@ovTemplateIDMobile=NULL,
		@ovModeID=NULL, 
		@parentSectionID=@sectionID, 
		@sectionName=@pageName,
		@sectionCode=@pageName, 
		@inheritPlacements=0,
		@sectionID=@rootSectionID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


-- update parentSiteResourceID of section
update sr 
set sr.parentSiteResourceID = @siteResourceID
from cms_pageSections s
inner join cms_siteResources sr on s.siteResourceID = sr.siteResourceID	and s.sectionID = @rootSectionID
IF @@ERROR <> 0 GOTO on_error

-- create Navigation Widget and assign to zone A of section


EXEC @rc = dbo.cms_createApplicationWidgetInstance
	@siteid = @siteid,
	@applicationInstanceID = @applicationInstanceID,
	@applicationWidgetTypeID = @appWidgetTypeID,
	@applicationWidgetInstanceName = 'Social Network Navigation',
	@applicationWidgetInstanceDesc = 'Navigation',
	@applicationWidgetInstanceID = @applicationWidgetInstanceID OUTPUT,
	@siteResourceID = @applicationWidgetSiteResourceID  OUTPUT
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

exec @rc = dbo.cms_createPageSectionZoneResource
	@sectionID=@rootSectionID,
	@zoneID=@zoneIDA,
	@siteResourceID = @applicationWidgetSiteResourceID,
	@pszrID = @trashID OUTPUT
IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- create a group for social network users if the masterSiteID and siteID are the same, this implies
-- a master site is being created.
IF @masterSiteID = @siteid BEGIN
	EXEC @rc = dbo.ams_createGroup 
			@orgID=@masterOrgID, 
			@groupCode=null, 
			@groupName='Social Network Users', 
			@groupDesc='Social Network Users', 
			@isSystemGroup=0, 
			@allowManualAssignment=0, 
			@parentGroupID=null, 
			@hideOnGroupLists=1,
			@groupID=@masterSocialNetworkGroupID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	update dbo.ams_groups
	set hideOnGroupLists = 1
	where groupID = @masterSocialNetworkGroupID
	IF @@ERROR <> 0 GOTO on_error


	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@siteResourceID, @include=1, @functionID=@participateFunctionID, @roleID=null, @groupID=@masterSocialNetworkGroupID, @memberID=null, @inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error



END

-- add Social Network
INSERT INTO dbo.sn_socialNetworks (applicationInstanceID, masterSiteID, rootSectionID, masterOrgID, masterSocialNetworkGroupID, masterSocialNetworkID,defaultPageAlias, addressTypeID, emailTypeID,phoneTypeID)
VALUES (@applicationInstanceID, @masterSiteID, @rootSectionID, @masterOrgID, @masterSocialNetworkGroupID,@masterSocialNetworkID,'home', @addressTypeID, @emailTypeID,@phoneTypeID)
	IF @@ERROR <> 0 GOTO on_error
	select @socialNetworkID = SCOPE_IDENTITY()

IF @masterSiteID <> @siteID BEGIN
	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@siteResourceID, @poolRoleTypeID=@SNChildInstancePoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
END
ELSE BEGIN

	print 'Doing Master Instance stuff'

	update sn_socialNetworks
	set masterSocialNetworkID = @socialNetworkID
	where socialNetworkID = @socialNetworkID

	set @masterSocialNetworkID = @socialNetworkID;


	declare
		@MemberManagerPoolRoleTypeID int,
		@SharedAppWallPoolRoleTypeID int,
		@SharedWidgetWallAddEntryPoolRoleTypeID int,
		@HomePoolRoleTypeID int,
		@ProfileEditorPoolRoleTypeID int,
		@ProfileViewerPoolRoleTypeID int,
		@OrgProfileViewerPoolRoleTypeID int,
		@SharedAppBlogPoolRoleTypeID int,
		@SharedAppPhotosPoolRoleTypeID int,
		@SharedAppVideosPoolRoleTypeID int,
		@SharedAppCommunityManagerPoolRoleTypeID int,
		@SharedAppWelcomePoolRoleTypeID int;


	select @MemberManagerPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'MemberManager')
	select @SharedAppWallPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'SharedAppWall')
	select @SharedWidgetWallAddEntryPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'SharedWidgetWallAddEntry')
	select @HomePoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'Home')
	select @ProfileEditorPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'ProfileEditor')
	select @ProfileViewerPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'ProfileViewer')
	select @OrgProfileViewerPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'OrgProfileViewer')
	select @SharedAppBlogPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'SharedAppBlog')
	select @SharedAppPhotosPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'SharedAppPhotos')
	select @SharedAppVideosPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'SharedAppVideos')
	select @SharedAppCommunityManagerPoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'SharedAppCommunityManager')
	select @SharedAppWelcomePoolRoleTypeID = dbo.fn_getResourcePoolRoleTypeID(@poolTypeID,'SharedAppWelcome')

	-- create SNConfig Resource Pool
	exec dbo.cms_createSiteResourcePool
		@poolTypeID=@poolTypeID, @poolName='Social Network Config', @poolID = @SNPoolID OUTPUT

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@siteResourceID, @poolRoleTypeID=@SNMasterInstancePoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	
	declare
		@appSubPageResourceTypeID int,
		@subAppPageName varchar(100),
		@subAppPageID int,
		@snNetworkHomePageID int,
		@snNetworkHomeApplicationInstanceID int,
		@snViewProfilePageID int,
		@subAppApplicationInstanceID int,
		@subAppResourceID int,
		@snHomeAppTypeID int,
		@snmemberManagerAppTypeID int,
		@sneditProfileAppTypeID int,
		@snviewProfileAppTypeID int,
		@snviewOrgProfileAppTypeID int,
		@snblogAppTypeID int,
		@snstatusAppTypeID int,
		@snphotosAppTypeID int,
		@snPhotosApplicationInstanceID int,
		@snVideosAppTypeID int,
		@communityManagerAppTypeID int,
		@SNWelcomeApplicationTypeID int,
		@MasterInstanceUserGroupID int;

	declare
		@upcomingEventsWidgetTypeID int,
		@recentUploadsWidgetTypeID int,
		@recentPhotosWidgetTypeID int,
		@profilePictureWidgetTypeID int,
		@myStatusWidgetTypeID int,
		@myFriendsWidgetTypeID int,
		@activityFeedWidgetTypeID int,
		@applicationWidgetInstanceSiteResourceID int,
		@myFriendsWidgetInstanceSiteResourceID int,
		@profilePicWidgetInstanceSiteResourceID int,
		@myStatusWidgetInstanceSiteResourceID int,
		@activityFeedWidgetInstanceSiteResourceID int,
		@recentVideosWidgetTypeID int,
		@recentBlogsWidgetTypeID int,
		@recentCenterListMessagesWidgetTypeID int,
		@recentActiveMembersWidgetTypeID int;

	select @appSubPageResourceTypeID = dbo.fn_getResourceTypeID('ApplicationSubPage')
	select @snHomeAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'socialNetworkHome'
	select @snmemberManagerAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'MemberManager'
	select @sneditProfileAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'ProfileManager'
	select @snviewProfileAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'profileViewer'
	select @snviewOrgProfileAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'orgProfileViewer'

	select @snblogAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'Blog'
	select @snstatusAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'Blog'
	select @snphotosAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'PhotoGallery'
	select @snVideosAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'VideoGallery'
	select @communityManagerAppTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'CommunityCenter'
	select @SNWelcomeApplicationTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'SocialNetworkWelcome'


	select @recentPhotosWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationTypeID = @snphotosAppTypeID and applicationWidgetTypeName = 'recentPhotos'
	select @profilePictureWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationTypeID = @snApplicationTypeID and applicationWidgetTypeName = 'profilePic'
	select @myStatusWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationTypeID = @snstatusAppTypeID and applicationWidgetTypeName = 'addEntry'
	select @myFriendsWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationTypeID = @snApplicationTypeID and applicationWidgetTypeName = 'myFriends'
	select @activityFeedWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationTypeID = @snApplicationTypeID and applicationWidgetTypeName = 'socialNetworkActivity'
	select @recentVideosWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationTypeID = @snVideosAppTypeID and applicationWidgetTypeName = 'recentVideos'
	select @recentBlogsWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationTypeID = @snblogAppTypeID and applicationWidgetTypeName = 'recentBlogs'
	select @recentCenterListMessagesWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationTypeID = @communityManagerAppTypeID and applicationWidgetTypeName = 'recentCenterListMessages'
	select @recentActiveMembersWidgetTypeID = applicationWidgetTypeID from cms_applicationWidgetTypes where applicationTypeID = @snmemberManagerAppTypeID and applicationWidgetTypeName = 'recentActiveMembers'


	select @MasterInstanceUserGroupID = groupID
	from ams_groups
	where parentGroupID is null
	and groupName = 'public'
	and orgID = @masterOrgID

	-- create shared Widgets for Master Network

	EXEC @rc = dbo.cms_createApplicationWidgetInstance
		@siteid=@siteID,
		@applicationInstanceID=@applicationInstanceID,
		@applicationWidgetTypeID=@profilePictureWidgetTypeID,
		@applicationWidgetInstanceName='Social Network Profile Picture',
		@applicationWidgetInstanceDesc='Profile Picture',
		@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
		@siteResourceID=@profilePicWidgetInstanceSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

/*
	EXEC @rc = dbo.cms_createApplicationWidgetInstance
		@siteid=@siteID,
		@applicationInstanceID=@applicationInstanceID,
		@applicationWidgetTypeID=@myFriendsWidgetTypeID,
		@applicationWidgetInstanceName='Social Network Friends',
		@applicationWidgetInstanceDesc='Friends',
		@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
		@siteResourceID=@myFriendsWidgetInstanceSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
*/
	EXEC @rc = dbo.cms_createApplicationWidgetInstance
		@siteid=@siteID,
		@applicationInstanceID=@applicationInstanceID,
		@applicationWidgetTypeID=@activityFeedWidgetTypeID ,
		@applicationWidgetInstanceName='Activity Feed',
		@applicationWidgetInstanceDesc='Social Networking Activity Feed',
		@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
		@siteResourceID=@activityFeedWidgetInstanceSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	-- create shared subApplications for Master network
	set @subAppPageName = @pageName + 'Wall';
	EXEC @rc = dbo.cms_createApplicationInstanceBlog @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@isVisible=@isVisible, @pageName=@subAppPageName, 
			@pageTitle='Social Network Wall', @pageDesc='Wall for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,@subPageTemplateID = null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@applicationInstanceName='Social Network Wall', @applicationInstanceDesc='Wall for Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@subAppPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@AddBlogFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@editOwnFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@deleteOwnFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@canCommentFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error




	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@subAppPageID,@socialNetworkID,'wall',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@SharedAppWallPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = dbo.cms_createApplicationWidgetInstance
		@siteid=@siteID,
		@applicationInstanceID=@subAppApplicationInstanceID,
		@applicationWidgetTypeID=@myStatusWidgetTypeID ,
		@applicationWidgetInstanceName='My Status',
		@applicationWidgetInstanceDesc='Social Networking Status Tool',
		@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
		@siteResourceID=@myStatusWidgetInstanceSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	
	update dbo.sn_socialNetworks
	set statusApplicationWidgetInstanceID = @applicationWidgetInstanceID
	where socialNetworkID = @socialNetworkID

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@myStatusWidgetInstanceSiteResourceID, @poolRoleTypeID=@SharedWidgetWallAddEntryPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error



	set @subAppPageName = @pageName + 'Welcome';
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@applicationTypeID=@SNWelcomeApplicationTypeID,
			@isVisible=@isVisible, @pageName=@subAppPageName, 
			@pageTitle='Social Network Welcome', @pageDesc='Welcome for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@applicationInstanceName='Social Network Welcome', @applicationInstanceDesc='Welcome for Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@subAppPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=@MasterInstanceUserGroupID, @memberID=null, @inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@subAppPageID,@socialNetworkID,'welcomePage',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@SharedAppWelcomePoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	set @subAppPageName = @pageName + 'Home';
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@applicationTypeID=@snHomeAppTypeID,
			@isVisible=@isVisible, @pageName=@subAppPageName, 
			@pageTitle='Social Network Home', @pageDesc='Homepage for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@applicationInstanceName='Social Network Home', @applicationInstanceDesc='Homepage for Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@snNetworkHomePageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@snNetworkHomePageID,@socialNetworkID,'home',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@HomePoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


/*	
	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snNetworkHomePageID, @zoneID=@zoneIDB, @siteResourceID=@myFriendsWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
*/


	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snNetworkHomePageID, @zoneID=@zoneIDB, @siteResourceID=@profilePicWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snNetworkHomePageID, @zoneID=@zoneID, @siteResourceID=@myStatusWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snNetworkHomePageID, @zoneID=@zoneID, @siteResourceID=@activityFeedWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	set @subAppPageName = @pageName + 'CommunityManager';
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@applicationTypeID=@communityManagerAppTypeID,
			@isVisible=@isVisible, @pageName=@subAppPageName, 
			@pageTitle='Communities', @pageDesc='Communities for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@applicationInstanceName='Communities', @applicationInstanceDesc='Communities for the Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@subAppPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@subAppPageID,@socialNetworkID,'communitymanager',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@SharedAppCommunityManagerPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = dbo.cms_createApplicationWidgetInstance
		@siteid=@siteID,
		@applicationInstanceID=@subAppApplicationInstanceID,
		@applicationWidgetTypeID=@recentCenterListMessagesWidgetTypeID ,
		@applicationWidgetInstanceName='Recent List Messages',
		@applicationWidgetInstanceDesc='Recent List Messages',
		@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
		@siteResourceID=@applicationWidgetInstanceSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	


	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snNetworkHomePageID, @zoneID=@zoneID, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	set @subAppPageName = @pageName + 'MemberManager';
	EXEC @rc = dbo.cms_createApplicationInstanceMemberManager @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@isVisible=@isVisible, @pageName=@subAppPageName, 
			@pageTitle='Social Network Member Manager', @pageDesc=' Member Manager for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,@subPageTemplateID = null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@applicationInstanceName='Social Network Member Manager', @applicationInstanceDesc=' Member Manager for Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@subAppPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@subAppPageID,@socialNetworkID,'membermanager',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@MemberManagerPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error




	EXEC @rc = dbo.cms_createApplicationWidgetInstance
		@siteid=@siteID,
		@applicationInstanceID=@subAppApplicationInstanceID,
		@applicationWidgetTypeID=@recentActiveMembersWidgetTypeID ,
		@applicationWidgetInstanceName='Recently Active Users',
		@applicationWidgetInstanceDesc='Recently Active Users',
		@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
		@siteResourceID=@applicationWidgetInstanceSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	


	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snNetworkHomePageID, @zoneID=@zoneID, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error



	set @subAppPageName = @pageName + 'ProfileEditor';
	EXEC @rc = dbo.cms_createApplicationInstanceProfileManager @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@isVisible=@isVisible, @pageName=@subAppPageName, 
			@pageTitle='Social Network Profile Manager', @pageDesc=' Profile Manager for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,@subPageTemplateID = null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@applicationInstanceName='Social Network Profile Manager', @applicationInstanceDesc=' Profile Manager for Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@subAppPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@subAppPageID,@socialNetworkID,'editProfile',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error


	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@ProfileEditorPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error



	set @subAppPageName = @pageName + 'ProfileViewer';
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@applicationTypeID=@snviewProfileAppTypeID,
			@isVisible=@isVisible, @pageName=@subAppPageName, 
			@pageTitle='Social Network Profile Viewer', @pageDesc='Profile Viewer for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@applicationInstanceName='Social Network Profile Viewer', @applicationInstanceDesc='Profile Viewer for Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@snViewProfilePageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@snViewProfilePageID,@socialNetworkID,'viewProfile',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snViewProfilePageID, @zoneID=@zoneIDB, @siteResourceID=@profilePicWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

/*
	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snViewProfilePageID, @zoneID=@zoneIDB, @siteResourceID=@myFriendsWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
*/

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@ProfileViewerPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error



	set @subAppPageName = @pageName + 'OrgProfileViewer';
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@applicationTypeID=@snviewOrgProfileAppTypeID,
			@isVisible=@isVisible, @pageName=@subAppPageName, 
			@pageTitle='Social Network Organization Profile Viewer', @pageDesc='Organization Profile Viewer for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@applicationInstanceName='Social Network Organization Profile Viewer', @applicationInstanceDesc='Organization Profile Viewer for Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@subAppPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@subAppPageID,@socialNetworkID,'viewOrgProfile',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@OrgProfileViewerPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error



	set @subAppPageName = @pageName + 'Photos';
	EXEC @rc = dbo.cms_createApplicationInstancePhotoGallery @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@isVisible=@isVisible,@pageName=@subAppPageName, 
			@pageTitle='Social Network Photos', @pageDesc='Photos for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@allowSubGalleries = 1,
			@applicationInstanceName='Social Network Photos', @applicationInstanceDesc='Photos for Social Network', @creatorMemberID=@creatorMemberID,
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@subAppPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@subAppPageID,@socialNetworkID,'photos',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@SharedAppPhotosPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error


	EXEC @rc = dbo.cms_createApplicationWidgetInstance
		@siteid=@siteID,
		@applicationInstanceID=@subAppApplicationInstanceID,
		@applicationWidgetTypeID=@recentPhotosWidgetTypeID,
		@applicationWidgetInstanceName='Social Network Recent Photos',
		@applicationWidgetInstanceDesc='Recent Photos',
		@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
		@siteResourceID=@applicationWidgetInstanceSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	
	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snNetworkHomePageID, @zoneID=@zoneIDB, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snViewProfilePageID, @zoneID=@zoneIDB, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	set @subAppPageName = @pageName + 'Videos';
	EXEC @rc = dbo.cms_createApplicationInstanceVideoGallery @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@isVisible=@isVisible,@pageName=@subAppPageName, 
			@pageTitle='Social Network Videos', @pageDesc='Videos for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@allowSubGalleries = 1, @creatorMemberID=@creatorMemberID,
			@applicationInstanceName='Social Network Videos', @applicationInstanceDesc='Videos for Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@subAppPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@subAppPageID,@socialNetworkID,'videos',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@SharedAppVideosPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = dbo.cms_createApplicationWidgetInstance
		@siteid=@siteID,
		@applicationInstanceID=@subAppApplicationInstanceID,
		@applicationWidgetTypeID=@recentVideosWidgetTypeID,
		@applicationWidgetInstanceName='Social Network Recent Videos',
		@applicationWidgetInstanceDesc='Recent Videos',
		@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
		@siteResourceID=@applicationWidgetInstanceSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	
	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snNetworkHomePageID, @zoneID=@zoneIDB, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snViewProfilePageID, @zoneID=@zoneIDB, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error



	set @subAppPageName = @pageName + 'Blog';
	EXEC @rc = dbo.cms_createApplicationInstanceBlog @siteid=@siteid, @languageID=@languageID, @sectionID=@rootSectionID, 
			@isVisible=@isVisible, @pageName=@subAppPageName, 
			@pageTitle='Social Network Blog', @pageDesc='Blog for the Social Network', @zoneID=@zoneID, @pagetemplateid=null,@subPageTemplateID = null,
			@pageModeID=null, @pgResourceTypeID=@appSubPageResourceTypeID, @pgParentResourceID = @siteresourceID, @allowReturnAfterLogin=@allowReturnAfterLogin, 
			@applicationInstanceName='Social Network Blog', @applicationInstanceDesc='Blog for Social Network', 
			@applicationInstanceID=@subAppApplicationInstanceID OUTPUT, 
			@siteresourceID=@subAppResourceID OUTPUT, 
			@pageID=@subAppPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@AddBlogFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@editOwnFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@deleteOwnFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteid, @siteResourceID=@subAppResourceID, @include=1, @functionID=@canCommentFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID, @inheritedRightsFunctionID=@participateFunctionID, @resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error




	insert into sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	values (@subAppPageID,@socialNetworkID,'blog',1,@subAppApplicationInstanceID)
	IF @@ERROR <> 0 GOTO on_error


	exec @rc = dbo.cms_createSiteResourcePoolMember
		@poolID=@SNPoolID, @siteResourceID=@subAppResourceID, @poolRoleTypeID=@SharedAppBlogPoolRoleTypeID, @poolMemberID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = dbo.cms_createApplicationWidgetInstance
		@siteid=@siteID,
		@applicationInstanceID=@subAppApplicationInstanceID,
		@applicationWidgetTypeID=@recentBlogsWidgetTypeID,
		@applicationWidgetInstanceName='Social Network Recent Blogs',
		@applicationWidgetInstanceDesc='Recent Blogs',
		@applicationWidgetInstanceID=@applicationWidgetInstanceID OUTPUT,
		@siteResourceID=@applicationWidgetInstanceSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	
	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snNetworkHomePageID, @zoneID=@zoneIDB, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	exec @rc = dbo.cms_createPageZoneResource
		@pageID=@snViewProfilePageID, @zoneID=@zoneIDB, @siteResourceID=@applicationWidgetInstanceSiteResourceID, @pzrID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- CREATE USER GUIDE FOR THE SOCIAL NETWORK
	DECLARE @userGuidePageID int, @siteResourceStatusID int
	DECLARE @contentID int, @userGuideContentSiteResourceID int, @resourceTypeID int

	SELECT	@resourceTypeID = dbo.fn_getResourceTypeID('ApplicationSubPage') 
	SELECT	@siteResourceStatusID = dbo.fn_getResourceStatusID('Active') 

	EXEC  @rc = dbo.cms_createPage @siteid=@siteID, @languageID=@languageID, @resourceTypeID=@resourceTypeID
		, @siteResourceStatusID=@siteResourceStatusID
		, @pgParentResourceID=@siteResourceID
		, @isVisible=1, @sectionID=@rootSectionID, @ovTemplateID=null, @ovTemplateIDMobile=NULL, @ovModeID=null, @pageName='UserGuide'
		, @pageTitle='UserGuide'
		, @pageDesc='User Guide', @keywords='', @inheritPlacements=1, @allowReturnAfterLogin=1
		, @checkReservedNames=1, @pageID=@userGuidePageID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	INSERT INTO sn_pages (pageID,socialNetworkID, alias, isSharedApp, applicationInstanceID)
	VALUES (@userGuidePageID,@socialNetworkID,'userguide',1,null)
	IF @@ERROR <> 0 GOTO on_error

	SELECT	@resourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedContent')

	EXEC  @rc = cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @resourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = 1,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'UserGuide',
		@contentDesc = 'User Guide',
		@rawContent = '',
		@memberID=NULL,
		@contentID = @contentID OUTPUT,
		@siteResourceID = @userGuideContentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	SELECT @zoneID = dbo.fn_getZoneID('Main')

	EXEC  @rc = cms_createPageZoneResource
		@pageID = @userGuidePageID,
		@zoneID = @zoneID,
		@siteResourceID = @userGuideContentSiteResourceID,
		@pzrID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	SELECT	@viewFunctionID = dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('ApplicationCreatedContent'))

	EXEC	@rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@userGuideContentSiteResourceID, @include=1
	, @functionID=@viewFunctionID, @roleID=null, @groupID=null, @memberID=null, @inheritedRightsResourceID=@siteResourceID
	, @inheritedRightsFunctionID=@viewFunctionID, @resourceRightID=@trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

END

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1


GO

print '%%%%%%%%%% ALTER cms_createApplicationInstanceStore SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROCEDURE [dbo].[cms_createApplicationInstanceStore] 
@siteid int,
@languageID int,
@sectionID int,
@isVisible bit,
@pageName varchar(50),
@pageTitle varchar(200),
@pagedesc varchar(400),
@zoneID int,
@pageTemplateID int,
@pageModeID int,
@pgResourceTypeID int,
@defaultGLAccountID int,
@allowReturnAfterLogin bit,
@applicationInstanceName varchar(100),
@applicationInstanceDesc varchar(200),
@applicationInstanceID int OUTPUT,
@siteResourceID int OUTPUT,
@pageID int OUTPUT

AS

BEGIN TRAN

declare @rc int, @applicationTypeID int, @appCreatedContentResourceTypeID int, @activeSiteResourceStatusID int,
	@maincontentID int, @maincontentSiteResourceID int
DECLARE @documentSectionName varchar(50), @appCreatedSectionResourceTypeID int, @rootSectionID int

select @applicationInstanceID = null
select @siteResourceID = null
select @pageID = null
select @appCreatedSectionResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedSection')
select @applicationTypeID = applicationTypeID from dbo.cms_applicationTypes where applicationTypeName = 'Store'
select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')
select @activeSiteResourceStatusID = dbo.fn_getResourceStatusId('Active')

-- create instance
EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, 
		@applicationTypeID=@applicationTypeID, @isVisible=@isVisible, @pageName=@pageName, 
		@pageTitle=@pageTitle, @pageDesc=@pagedesc, @zoneID=@zoneID, @pagetemplateid=@pageTemplateID,
		@pageModeID=@pageModeID, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID=null, 
		@allowReturnAfterLogin=@allowReturnAfterLogin, @applicationInstanceName=@applicationInstanceName, 
		@applicationInstanceDesc=@applicationInstanceDesc, @applicationInstanceID=@applicationInstanceID OUTPUT, 
		@siteresourceID=@siteResourceID OUTPUT, 
		@pageID=@pageID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- create mainContent
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@languageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='Welcome to the store.<br/><br/>Click on the links to the left to browse the catalog.<br/><br/>Please note that applicable taxes and shipping charges will be added to purchases.', 
	@memberID=NULL,
	@contentID=@maincontentID OUTPUT, @siteResourceID=@maincontentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- setup a section to hold downloadable content
select @documentSectionName = 'StoreDocs ' + cast(@applicationInstanceID as varchar(8))

exec @rc = dbo.cms_createPageSection
		@siteID = @siteID, 
		@sectionResourceTypeID = @appCreatedSectionResourceTypeID, 
		@ovTemplateID = NULL,
		@ovTemplateIDMobile=NULL,
		@ovModeID = NULL, 
		@parentSectionID = @sectionID, 
		@sectionName = @documentSectionName, 
		@sectionCode = @documentSectionName,
		@inheritPlacements = 1,
		@sectionID = @rootSectionID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

-- update parentSiteResourceID of section
update sr
set sr.parentSiteResourceID = @siteResourceID
from cms_pageSections s
	inner join cms_siteResources sr on s.siteResourceID = sr.siteResourceID
		and s.sectionID = @rootSectionID

-- add store
INSERT INTO dbo.store (siteID, applicationInstanceID, mainContentID, showProductID, MaxRecords, showCartThumbnails, rootSectionID, GLAccountID, shippingGLAccountID)
VALUES (@siteid, @applicationInstanceID, @maincontentID, 0, 25, 0, @rootSectionID, @defaultGLAccountID, @defaultGLAccountID)
	IF @@ERROR <> 0 GOTO on_error

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1

GO

print '%%%%%%%%%% ALTER cms_createContentField SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROC [dbo].[cms_createContentField]
@siteID int,
@isSSL bit,
@isHTML bit,
@isActive bit,
@languageID int,
@contentTitle varchar(200),
@contentDesc varchar(400),
@rawContent varchar(max),
@contentID int OUTPUT,
@contentSiteResourceID int OUTPUT

AS

declare @rc int
declare @contentResourceTypeID int, @appCreatedContentResourceTypeID int, @activeSiteResourceStatusID int
declare @parentSiteResourceID int

select @contentSiteResourceID = null
select @contentID = null

select @contentResourceTypeID = dbo.fn_getResourceTypeId('Members')
select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')
select @activeSiteResourceStatusID = dbo.fn_getResourceStatusId('Active')

select top 1 @parentSiteResourceID = sr.siteResourceID
	from dbo.cms_siteResources as sr
	inner join dbo.cms_siteResourceTypes as srt on srt.resourceTypeID = sr.resourceTypeID
	where sr.siteID = @siteID
	and sr.siteResourceStatusID = @activeSiteResourceStatusID
	and srt.resourceType = 'MemberAdmin'

BEGIN TRAN

EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@parentSiteResourceID=@parentSiteResourceID, @siteResourceStatusID=@activeSiteResourceStatusID, 
	@isSSL=@isSSL, @isHTML=@isHTML, @languageID=@languageID, @isActive=@isActive, 
	@contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawContent=@rawContent, 
	@memberID=NULL,
	@contentID=@contentID OUTPUT, @siteResourceID=@contentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

IF @@TRANCOUNT > 0 COMMIT TRAN

-- normal exit
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @contentID = 0
	RETURN -1

RETURN 0

GO

print '%%%%%%%%%% ALTER cms_createDefaultPages SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROC [dbo].[cms_createDefaultPages]
@siteid int,
@sectionid int,
@languageID int

AS

declare @appPageID int

-- get resourceType for system created pages and HTML content
declare @pgResourceTypeID int, @HTMLResourceTypeID int, @appPgResourceTypeID int, @siteResourceTypeID int
select @pgResourceTypeID = dbo.fn_getResourceTypeID('SystemCreatedPage')
select @HTMLResourceTypeID = dbo.fn_getResourceTypeID('UserCreatedContent')
select @appPgResourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedPage')
select @siteResourceTypeID = dbo.fn_getResourceTypeID('site')

-- get siteresourceid
declare @websiteResourceID int
select @websiteResourceID = siteResourceID from sites where siteID = @siteID

-- get active resource status
declare @siteResourceStatusID int
select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

-- get Main Zone
declare @mainZoneID int
select @mainZoneID = dbo.fn_getZoneID('Main')

-- get page modes
declare @fullModeID int, @directModeID int, @streamModeID int
select @fullModeID = dbo.fn_getModeID('Full')
select @directModeID = dbo.fn_getModeID('Direct')
select @streamModeID = dbo.fn_getModeID('Stream')

-- get resource functions
declare @viewFID int, @loginFID int
SELECT @viewFID = dbo.fn_getResourceFunctionID('view',@HTMLResourceTypeID)
SELECT @loginFID = dbo.fn_getResourceFunctionID('login',@siteResourceTypeID)


-- get public, siteadmin group
declare @publicGID int, @siteadminGID int, @usersGID int, @guestGID int
select @publicGID = g.groupID from dbo.ams_groups as g inner join dbo.sites as s on s.orgid = g.orgid where g.isSystemGroup = 1 and g.groupName = 'Public' AND g.status <> 'D' and s.siteid = @siteid
select @usersGID = g.groupID from dbo.ams_groups as g inner join dbo.sites as s on s.orgid = g.orgid where g.isSystemGroup = 1 and g.groupName = 'Users' AND g.status <> 'D' and s.siteid = @siteid
select @guestGID = g.groupID from dbo.ams_groups as g inner join dbo.sites as s on s.orgid = g.orgid where g.isSystemGroup = 1 and g.groupName = 'Guests' AND g.status <> 'D' and s.siteid = @siteid
select @siteadminGID = g.groupID from dbo.ams_groups as g inner join dbo.sites as s on s.orgid = g.orgid where g.isSystemGroup = 1 and g.groupName = 'Site Administrators' AND g.status <> 'D' and s.siteid = @siteid

-- create default pages and applications
DECLARE @rc int, @pageID int, @contentID int, @contentSiteResourceID int, @applicationTypeID int, @suggestedPageName varchar(100), @applicationInstanceID int, @siteresourceID int, @trashID int
BEGIN TRAN

	-- main page and content
	EXEC @rc = dbo.cms_createPage 
		@siteid=@siteid, 
		@languageID=@languageID, 
		@resourceTypeID=@pgResourceTypeID, 
		@siteResourceStatusID=@siteResourceStatusID, 
		@pgParentResourceID=null, 
		@isVisible=1, 
		@sectionID=@sectionID, 
		@ovTemplateID=null, 
		@ovTemplateIDMobile=null,
		@ovModeID=null, 
		@pageName='Main', 
		@pageTitle='Welcome', 
		@pageDesc=null, 
		@keywords=null,
		@inheritPlacements=1,
		@allowReturnAfterLogin=1, 
		@checkReservedNames=0, 
		@pageID=@pageID OUTPUT

		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	EXEC @rc = dbo.cms_createContent @siteID=@siteid, @pageID=@pageID, @zoneID=@mainZoneID, @resourceTypeID=@HTMLResourceTypeID, @siteResourceStatusID=@siteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle='Welcome', @contentDesc=null, @rawContent='Put your homepage text here. You can include tables, images, lists, and more! Our built-in content editor can do all of this and more.', @memberID=NULL, @contentID=@contentID, @contentSiteResourceID=@contentSiteResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- login app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'Login'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=1, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@fullModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- org doc download app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'OrgDocDownload'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- store doc download app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'StoreDocDownload'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- ts doc download app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'TSDocDownload'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- account locator
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'accountLocator'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@directModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1,
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null,
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null,
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- userinfo app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'UserInfo'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@directModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- updatemember app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'UpdateMember'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=1, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=null, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- Ticket 8352718 - default permissions when setting up a site
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@usersGID, @memberID=null, 
		@inheritedRightsResourceID=@websiteResourceID, @inheritedRightsFunctionID=@loginFID, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@guestGID, @memberID=null, 
		@inheritedRightsResourceID=@websiteResourceID, @inheritedRightsFunctionID=@loginFID, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- ContentEditor app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'ContentEditor'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@fullModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- ajax app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'Ajax'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	
	-- Flash Express Install app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'flashExpressInstall'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	
	-- AppProxy app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'appProxy'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@streamModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	
	-- invoices app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'invoices'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@fullModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- BuyNow app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'buyNow'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@fullModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	
	-- ViewCart app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'viewCart'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=@fullModeID, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error
	
	-- Support app
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'Support'
	EXEC @rc = dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle=@suggestedPageName, @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=null, @pageModeID=null, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=0, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@publicGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	-- admin app
	-- set template used by the admin app to the admin template
	select @applicationTypeID = null, @suggestedPageName = null
	select @applicationTypeID = applicationTypeID, @suggestedPageName = suggestedPageName from dbo.cms_applicationTypes where applicationTypeName = 'Admin'
	EXEC @rc = dbo.cms_createApplicationInstanceAdmin @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @isVisible=0, @pageName=@suggestedPageName, @pageTitle='Website Control Panel', @pageDesc=null, @zoneID=@mainZoneID, @pagetemplateid=2, @pageModeID=null, @pgResourceTypeID=@appPgResourceTypeID, @pgParentResourceID=null, @allowReturnAfterLogin=1, @applicationInstanceName=@suggestedPageName, @applicationInstanceDesc=null, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteresourceID=@siteresourceID OUTPUT, @pageID = @appPageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, 
		@functionID=@viewFID, @roleID=null, @groupID=@siteadminGID, @memberID=null, 
		@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null, 
		@resourceRightID=@trashID OUTPUT
		IF @@ERROR <> 0 GOTO on_error

	--set admin page inheritPlacements = 0
	update cms_pages set inheritPlacements = 0 where pageID = @appPageID
	IF @@ERROR <> 0 GOTO on_error

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1

GO

print '%%%%%%%%%% ALTER co_createComment SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROC [dbo].[co_createComment]
@siteid int,
@languageID int,
@contributorMemberID int,
@siteResourceID int,
@comment varchar(max),
@applicationInstanceID int,
@applicationTypeID int,
@supportMemberID int,
@commentID int OUTPUT

AS

-- ensure @blogEntryID is null (can be passed in)
SELECT @commentID = null


declare @rc int, 
	@appCreatedContentResourceTypeID int, 
	@activeSiteResourceStatusID int,
	@maincontentID int, 
	@maincontentSiteResourceID int

select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')
select @activeSiteResourceStatusID = dbo.fn_getResourceStatusId('Active')


BEGIN TRAN

-- create mainContent
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=0, @languageID=@languageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent=@comment, 
	@memberID=@contributorMemberID,
	@contentID=@maincontentID OUTPUT, @siteResourceID=@maincontentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

		
-- add the comment to the comments table
INSERT INTO dbo.comments(siteResourceID, contributorMemberID, commentContentID)
VALUES (@siteResourceID, @contributorMemberID, @maincontentID)
	IF @@ERROR <> 0 GOTO on_error
	SELECT @commentID = SCOPE_IDENTITY()

-- create activity log entry
EXEC platformstats.dbo.act_recordLog @memberID=@contributorMemberID, @activityType='comment', 
	@applicationTypeID=@applicationTypeID, @applicationInstanceID=@applicationInstanceID,
	@supportSiteResourceID=@siteResourceID, @supportMemberID=@supportMemberID, @supportMessage=null


IF @@TRANCOUNT > 0 COMMIT TRAN

-- normal exit
RETURN 1

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @commentID = 0
	RETURN -1
GO

print '%%%%%%%%%% ALTER createSite SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROC [dbo].[createSite]
	@orgID int,
	@sitecode varchar(10),
	@siteName varchar(60),
	@mainNetworkID int,
	@isLoginNetwork bit,
	@isMasterSite bit,
	@defaultLanguageID int,
	@defaultTimeZoneID int,
	@defaultCurrencyTypeID int,
	@showCurrencyType bit,
	@hasSSL bit,
	@allowGuestAccounts bit,
	@forceLoginPage bit,
	@useRemoteLogin bit,
	@affiliationRequired bit,
	@providesFreeFastCase bit,
	@enforceSiteAgreement bit,
	@allowMemberUpdates bit,
	@immediateMemberUpdates bit,
	@emailMemberUpdates varchar(200),
	@defaultPostalState varchar(10),
	@joinURL varchar(100),
	@pdfPassword varchar(30),
	@alternateGuestAccountCreationLink varchar(400),
	@alternateGuestAccountPopup bit,
	@alternateForgotPasswordLink varchar(400),
	@mainhostname varchar(80),
	@norightsContent varchar(max),
	@norightsNotLoggedInContent varchar(max),
	@inactiveUserContent varchar(max),
	@siteagreementContent varchar(max),
	@welcomeMessageContent varchar(max),
	@firstTimeLoginContent varchar(max),
	@siteID int OUTPUT

AS

DECLARE @rc int, @templateID int, @modeID int, @sectionID int, 
	@sectionResourceTypeID int, @siteAdminRoleID int, @superAdminRoleID int, 
	@siteAdminGroupID int, @superAdminGroupID int, @trashID int

BEGIN TRAN
	-- check for existing sitecode
	SELECT @siteID = null
	SELECT @siteID = siteID FROM dbo.sites where sitecode = @sitecode

	-- if not there, add it
	IF @siteID is not null
		GOTO on_error

	-- insert sites
	INSERT INTO dbo.sites (orgID, sitecode, siteName, defaultLanguageID, defaultTimeZoneId, defaultCurrencyTypeID,
		hasSSL, allowGuestAccounts, forceLoginPage, useRemoteLogin, affiliationRequired, 
		providesFreeFastCase, allowMemberUpdates, enforceSiteAgreement, immediateMemberUpdates, emailMemberUpdates, 
		defaultPostalState, joinURL, pdfPassword, alternateGuestAccountCreationLink, alternateGuestAccountPopup, 
		alternateForgotPasswordLink, showCurrencyType, enableMobile, enableDeviceDetection)
	VALUES (@orgID, @sitecode, @siteName, @defaultLanguageID, @defaultTimeZoneId, @defaultCurrencyTypeID, 
		@hasSSL, @allowGuestAccounts, @forceLoginPage, @useRemoteLogin, @affiliationRequired, 
		@providesFreeFastCase, @allowMemberUpdates, @enforceSiteAgreement, @immediateMemberUpdates, @emailMemberUpdates, 
		@defaultPostalState, @joinURL, @pdfPassword, @alternateGuestAccountCreationLink, @alternateGuestAccountPopup, 
		@alternateForgotPasswordLink, @showCurrencyType, 0, 0)
		IF @@ERROR <> 0 GOTO on_error
		SELECT @siteID = SCOPE_IDENTITY()

	-- createSiteLanguage		
	EXEC @rc = dbo.createSiteLanguage @siteID=@siteID, @languageID=@defaultLanguageID		
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- get resourceType for site
	declare @siteResourceTypeID int
	select @siteResourceTypeID = dbo.fn_getResourceTypeID('Site')

	-- get active resource status
	declare @siteResourceStatusID int
	select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')

	-- create a resourceID for the site
	DECLARE @siteResourceID int	
	exec dbo.cms_createSiteResource
		@resourceTypeID = @siteResourceTypeID,
		@siteResourceStatusID = @siteResourceStatusID,
		@siteID = @siteid,
		@isVisible = 1,
		@parentSiteResourceID = null,
		@siteResourceID = @siteResourceID OUTPUT
		IF @@ERROR <> 0 OR @siteResourceID = 0 GOTO on_error
	
	-- update site with new resource
	UPDATE dbo.sites
	SET siteResourceID = @siteResourceID
	WHERE siteID = @siteID
		IF @@ERROR <> 0 GOTO on_error
		
	-- roles
	select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')
	select @siteAdminRoleID = dbo.fn_getResourceRoleID('Site Administrator')
	select @siteAdminGroupID = groupID 
		from dbo.ams_groups 
		where groupCode = 'SiteAdmins' 
		and orgID = @orgID
	select @superAdminGroupID = groupID 
		from dbo.ams_groups 
		where groupCode = 'SuperAdmins' 
		and orgID = 1

	-- give siteAdmin Role to siteAdmin Group
	EXEC @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteID,
		@siteResourceID=@siteResourceID,
		@include=1,
		@functionID=null,
		@roleID=@siteAdminRoleID,
		@groupID=@siteAdminGroupID,
		@memberID=null,
		@inheritedRightsResourceID=null,
		@inheritedRightsFunctionID=null,
		@resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- give superAdmin Role to superAdmin Group
	EXEC @rc = dbo.cms_createSiteResourceRight
		@siteID=@siteID,
		@siteResourceID=@siteResourceID,
		@include=1,
		@functionID=null,
		@roleID=@superAdminRoleID,
		@groupID=@superAdminGroupID,
		@memberID=null,
		@inheritedRightsResourceID=null,
		@inheritedRightsFunctionID=null,
		@resourceRightID = @trashID OUTPUT
	IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- add default hostname
	DECLARE @hostnameID int	
	EXEC @rc = dbo.createSiteHostName @siteID=@siteID, @hostname=@mainhostname, @useRedirect=null, @hostnameID=@hostnameID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	-- add default template
	DECLARE @templateTypeID int
	SELECT @templateTypeID = dbo.fn_getTemplateTypeID('Page')
	EXEC @rc = dbo.cms_CreatePageTemplate @siteid=@siteID, @templateTypeID=@templateTypeID, @templateName='Default', @templateDesc='Default site template', @templateFileName='Default', @templateID=@templateID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @templateID = 0 GOTO on_error2
	
	-- add default page sections
	SELECT @modeID = dbo.fn_getModeID('Normal')
	SELECT @sectionResourceTypeID = dbo.fn_getResourceTypeID('SystemCreatedSection')
	EXEC @rc = dbo.cms_createPageSection @siteID=@siteID, @sectionResourceTypeID=@sectionResourceTypeID, @ovTemplateID=@templateID, @ovTemplateIDMobile=null, @ovModeID=@modeID, @parentSectionID=null, @sectionName='Root', @sectionCode='Root', @inheritPlacements=1, @sectionID=@sectionID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createPageSection @siteID=@siteID, @sectionResourceTypeID=@sectionResourceTypeID, @ovTemplateID=null, @ovTemplateIDMobile=null, @ovModeID=null, @parentSectionID=@sectionID, @sectionName='MCAMSMemberDocuments', @sectionCode='MCAMSMemberDocuments', @inheritPlacements=0, @sectionID=@trashID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- default fieldsets
	EXEC @rc = dbo.cms_createDefaultFieldsets @siteid=@siteID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add default pages
	EXEC @rc = dbo.cms_createDefaultPages @siteid=@siteID, @sectionid=@sectionID, @languageID=@defaultLanguageID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add default History (notes) Categories
	EXEC @rc = dbo.cms_createDefaultHistoryCategories @siteid=@siteID, @contributingMemberID=461530
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add to network
	EXEC @rc = dbo.createNetworkSite @networkID=@mainNetworkID, @siteID=@siteID, @isLoginNetwork=@isLoginNetwork, @isMasterSite=@isMasterSite
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

	-- add default FULL Frequency
	insert into dbo.sub_frequencies(frequencyName, frequency, frequencyShortName, uid, 
		rateRequired, hasInstallments, monthlyInterval, isSystemRate, siteID, status)
	values('Full', 1, 'F', newid(), 1, 1, 1, 1, @siteID, 'A')	
		IF @@ERROR <> 0 GOTO on_error2

	-- add content objects
	DECLARE @sysCreatedContentResourceTypeID int, @activesiteResourceStatusID int, @newContentid int, @newresourceid int
	select @sysCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('SystemCreatedContent')
	select @activesiteResourceStatusID = dbo.fn_getResourceStatusId('Active')
	EXEC @rc = dbo.cms_createContentObject 
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'NoRights',
		@contentDesc = null,
		@rawContent = @norightsContent,
		@memberID=NULL,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET noRightsContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject 
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'NoRightsNotLoggedIn',
		@contentDesc = null,
		@rawContent = @norightsNotLoggedInContent,
		@memberID=NULL,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET noRightsNotLoggedInContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'InactiveUser',
		@contentDesc = null,
		@rawContent = @inactiveUserContent,
		@memberID=NULL,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET InactiveUserContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'SiteAgreement',
		@contentDesc = null,
		@rawContent = @siteagreementContent,
		@memberID=NULL,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET SiteAgreementContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'Welcome Message',
		@contentDesc = null,
		@rawContent = @welcomeMessageContent,
		@memberID=NULL,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET welcomeMessageContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2
	EXEC @rc = dbo.cms_createContentObject
		@siteID = @siteID,
		@resourceTypeID = @sysCreatedContentResourceTypeID,
		@parentSiteResourceID = null,
		@siteResourceStatusID = @activesiteResourceStatusID,
		@isSSL = 0,
		@isHTML = 1,
		@languageID = 1,
		@isActive = 1,
		@contentTitle = 'First Time Login Message',
		@contentDesc = null,
		@rawContent = @firstTimeLoginContent,
		@memberID=NULL,
		@contentID = @newContentID OUTPUT,
		@siteResourceID = @newResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2
		UPDATE dbo.sites SET firstTimeLoginContentID = @newContentid where siteID = @siteID
			IF @@ERROR <> 0 GOTO on_error2

	-- link up superusers to all new sites
	INSERT INTO dbo.ams_memberNetworkProfiles (memberID, profileID, [status], dateCreated, siteID)
	SELECT distinct mnp.memberID, mnp.profileID, 'A', getdate(), @siteID
	FROM dbo.ams_memberNetworkProfiles AS mnp 
	INNER JOIN dbo.ams_networkProfiles AS np ON mnp.profileID = np.profileID
	WHERE mnp.status = 'A'
	AND np.networkID = dbo.fn_getNetworkID('MemberCentral Super Administrators')
	AND np.status = 'A'
	AND mnp.siteID <> @siteID
		IF @@ERROR <> 0 GOTO on_error2

	EXEC @rc = dbo.cms_populateSiteResourceRightsCache @siteID=@siteID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error2

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @siteID = 0
	RETURN -1

on_error2:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1

GO

print '%%%%%%%%%% ALTER email_createEmailBlast SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROC [dbo].[email_createEmailBlast]
@siteID int, 
@memberid int, 
@blastName varchar(200),
@categoryID int, 
@subCategoryID int, 
@blastID int OUTPUT

AS

declare @rc int, @ruleID int, @orgID int, @ruleXML xml, @siteName varchar(60), @subject varchar(80), 
	@fromName varchar(150), @fromEmail varchar(200), 
	@appCreatedContentResourceTypeID int, @siteResourceStatusID int, @languageID int,
	@thisContentID int, @thisContentResourceID int

select @blastID = 0
select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')
select @siteResourceStatusID = dbo.fn_getResourceStatusId('Active')
select @languageID = 1
select @orgID = orgID, @siteName = siteName from dbo.sites where siteID = @siteID
select @ruleXML = '<rule><conditionset op="AND" act="include" id="' + cast(NewID() as varchar(36)) + '" /></rule>'
select @fromName = firstname + ' ' + lastName from dbo.ams_members where memberid = @memberID
select TOP 1 @fromEmail = me.email
	FROM dbo.ams_memberEmails AS me
	INNER JOIN dbo.ams_memberEmailTypes as met on met.emailtypeID = me.emailTypeID and met.emailTypeOrder = 1
	WHERE me.memberID = @memberID

IF EXISTS (select blastID from dbo.email_emailBlasts where siteID = @siteID and categoryID = @categoryID and isnull(subCategoryID,0) = isnull(@subCategoryID,0) and blastName = @blastName)
	select @blastName = @blastName + ' ' + cast(cast(rand()*100000000 as int) as varchar(10))

BEGIN TRAN
	EXEC @rc = dbo.ams_createVirtualGroupRule @orgID=@orgID, @ruleTypeID=3, @ruleName='Saved Email Blast', @ruleXML=@ruleXML, @ruleSQL='', @ruleID=@ruleID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 or @ruleID = 0 GOTO on_error
	
	select @subject = 'Message from ' + @siteName
	exec @rc = dbo.cms_createContentObject @siteID, @appCreatedContentResourceTypeID, null, @siteResourceStatusID, 0, 1, @languageID, 1, @subject, '', '', @memberid, @thisContentID OUTPUT, @thisContentResourceID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 or @thisContentID = 0 GOTO on_error

	INSERT INTO dbo.email_emailBlasts (siteid, categoryID, subCategoryID, memberid, blastName, dateCreated, ruleID, fromName, fromEmail, replyTo, contentID)
	VALUES (@siteID, @categoryID, @subCategoryID, @memberID, left(@blastName,200), getdate(), @ruleID, @fromName, isnull(@fromEmail,''), isnull(@fromEmail,''), @thisContentID)
		IF @@ERROR <> 0 GOTO on_error
		SELECT @blastid = SCOPE_IDENTITY()

	UPDATE dbo.ams_virtualGroupRules
	SET ruleName = 'Saved Email Blast ' + cast(@blastid as varchar(10))
	WHERE ruleID = @ruleID
		IF @@ERROR <> 0 GOTO on_error

IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @blastid = 0
	RETURN -1

GO

print '%%%%%%%%%% ALTER et_createEmailTemplate SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROCEDURE [dbo].[et_createEmailTemplate]
 @templateName varchar(200),
 @templateDescription varchar(max),
 @categoryID int,
 @rawContent varchar(max),
 @subjectLine varchar(200),
 @emailFrom varchar(200),
 @createdByMemberID int,
 @siteID int,
 @templateID int OUTPUT
AS
BEGIN
 -- SET NOCOUNT ON added to prevent extra result sets from
 -- interfering with SELECT statements.
 SET NOCOUNT ON;
 declare @appCreatedContentResourceTypeID int, @activeStatusID int, @parentSiteResourceID int, @contentID int, @contentSiteResourceID int
 
 select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')
 select @activeStatusID = dbo.fn_getResourceStatusID('Active')
 
 select @parentSiteResourceID = dbo.fn_getSiteResourceIDForResourceType('EmailTemplateAdmin', @siteID)
 
 BEGIN TRAN
 
 exec dbo.cms_createContentObject
  @siteID=@siteID,
  @resourceTypeID=@appCreatedContentResourceTypeID,
  @parentSiteResourceID=@parentSiteResourceID,
  @siteResourceStatusID=@activeStatusID,
  @isSSL=0,
  @isHTML=1,
  @languageID=1,
  @isActive=1,
  @contentTitle=@templateName,
  @contentDesc='',
  @rawContent=@rawContent,
  @memberID=@createdByMemberID,
  @contentID= @contentID OUTPUT,
  @siteResourceID = @contentSiteResourceID OUTPUT
 
 IF @@ERROR <> 0 GOTO on_error
 
 select @contentSiteResourceID as contentSiteResourceID, @contentID as contentID
 
 INSERT INTO dbo.et_emailTemplates(templateName, categoryID, templateDescription, contentID, subjectLine, emailFrom, status, createdByMemberID)
 VALUES(@templateName, @categoryID, @templateDescription, @contentID, @subjectLine, @emailFrom, 'A', @createdByMemberID)
 SELECT @templateID = SCOPE_IDENTITY()
 
 IF @@TRANCOUNT > 0 COMMIT TRAN
 
 -- normal exit
 RETURN 0
 
 -- error exit
 on_error:
  IF @@TRANCOUNT > 1 COMMIT TRAN
  ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
  SELECT @templateID = 0
  RETURN -1
END

GO

print '%%%%%%%%%% ALTER ev_createEvent SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROCEDURE [dbo].[ev_createEvent] 
@siteID int,
@calendarID int,
@eventTypeID int,
@enteredByMemberID int,
@lockTimeZoneID int,
@isAllDayEvent bit,
@altRegistrationURL varchar(300),
@status char(1),
@reportCode varchar(15),
@emailContactContent bit,
@emailLocationContent bit,
@emailCancelContent	bit,
@emailTravelContent	bit,
@eventID int OUTPUT

AS

declare @rc int
select @eventID = null

BEGIN TRAN

declare @appCreatedContentResourceTypeID int, @activeSiteResourceStatusID int, @defaultLanguageID int
declare @eventSiteResourceID int, @eventResourceTypeID int, @eventcontentID int, @eventcontentSiteResourceID int
declare @locationContentID int, @travelcontentid int, @contactcontentid int, @cancellationPolicyContentID int, @informationContentID int
declare @defaultGLAccountID int
select @eventResourceTypeID = dbo.fn_getResourceTypeId('Event')
select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')
select @activeSiteResourceStatusID = dbo.fn_getResourceStatusId('Active')
select @defaultLanguageID = defaultLanguageID from dbo.sites where siteID = @siteID

-- create a resourceID for the event
exec dbo.cms_createSiteResource
	@resourceTypeID = @eventResourceTypeID,
	@siteResourceStatusID = @activeSiteResourceStatusID,
	@siteID = @siteid,
	@isVisible = 1,
	@parentSiteResourceID = null,
	@siteResourceID   = @eventSiteResourceID OUTPUT

EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@defaultLanguageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='',
	@memberID=@enteredByMemberID,
	@contentID=@eventcontentID OUTPUT, 
	@siteResourceID=@eventcontentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- create eventcontentid
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@defaultLanguageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', 
	@memberID=@enteredByMemberID,
	@contentID=@eventcontentID OUTPUT, 
	@siteResourceID=@eventcontentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- create locationcontentid
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@defaultLanguageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', 
	@memberID=@enteredByMemberID,
	@contentID=@locationContentID OUTPUT, 
	@siteResourceID=@eventcontentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- create travelcontentid
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@defaultLanguageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', 
	@memberID=@enteredByMemberID,
	@contentID=@travelcontentid OUTPUT, 
	@siteResourceID=@eventcontentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- create contactcontentid
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@defaultLanguageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', 
	@memberID=@enteredByMemberID,
	@contentID=@contactcontentid OUTPUT, 
	@siteResourceID=@eventcontentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- create cancelcontentid
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@defaultLanguageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', 
	@memberID=@enteredByMemberID,
	@contentID=@cancellationPolicyContentID OUTPUT, 
	@siteResourceID=@eventcontentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- create informationcontentid
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@defaultLanguageID, 
	@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', 
	@memberID=@enteredByMemberID,
	@contentID=@informationContentID OUTPUT, 
	@siteResourceID=@eventcontentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- get GLAccountID from home calendar
select @defaultGLAccountID = defaultGLAccountID
from dbo.ev_calendars
where calendarID = @calendarID
	IF @@ERROR <> 0 OR @defaultGLAccountID is null GOTO on_error

-- add event
insert into dbo.ev_events (eventTypeID, siteID, siteResourceID, eventContentID, locationContentID, travelContentId, contactContentID, cancellationPolicyContentID, enteredByMemberID, lockTimeZoneID, isAllDayEvent, altRegistrationURL, GLAccountID, [status], reportCode, informationContentID, emailContactContent, emailLocationContent, emailCancelContent, emailTravelContent)
values (@eventTypeID, @siteID, @eventSiteResourceID, @eventContentID, @locationContentID, @travelContentId, @contactContentID, @cancellationPolicyContentID, @enteredByMemberID, @lockTimeZoneID, @isAllDayEvent, @altRegistrationURL, @defaultGLAccountID, @status, nullif(@reportCode,''), @informationContentID, @emailContactContent, @emailLocationContent, @emailCancelContent, @emailTravelContent)
	IF @@ERROR <> 0 GOTO on_error
	select @eventID = SCOPE_IDENTITY()

-- add permissions for event management
declare @resourceRightID int, @calApplicationSiteResourceID int
declare @eventfunctionid int, @inheritedRightsFunctionID int, @calApplicationInstanceID int
select @calApplicationSiteResourceID = ai.siteResourceID, @calApplicationInstanceID = c.applicationInstanceID
	FROM dbo.ev_calendars AS c 
	INNER JOIN dbo.cms_applicationInstances AS ai ON c.applicationInstanceID = ai.applicationInstanceID
	WHERE c.calendarID = @calendarID
SELECT @eventfunctionid = dbo.fn_getResourceFunctionID('EditEvent',dbo.fn_getResourceTypeID('Event'))
SELECT @inheritedRightsFunctionID = dbo.fn_getResourceFunctionID('EditEventByDefault',dbo.fn_getResourceTypeID('Events'))
EXEC @rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@eventSiteResourceID, @functionID=@eventfunctionid, @inheritedRightsResourceID=@calApplicationSiteResourceID, @inheritedRightsFunctionID=@inheritedRightsFunctionID, @resourceRightID=@resourceRightID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
SELECT @eventfunctionid = dbo.fn_getResourceFunctionID('DeleteEvent',dbo.fn_getResourceTypeID('Event'))
SELECT @inheritedRightsFunctionID = dbo.fn_getResourceFunctionID('DeleteEventByDefault',dbo.fn_getResourceTypeID('Events'))
EXEC @rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@eventSiteResourceID, @functionID=@eventfunctionid, @inheritedRightsResourceID=@calApplicationSiteResourceID, @inheritedRightsFunctionID=@inheritedRightsFunctionID, @resourceRightID=@resourceRightID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
SELECT @eventfunctionid = dbo.fn_getResourceFunctionID('ViewRegistrants',dbo.fn_getResourceTypeID('Event'))
SELECT @inheritedRightsFunctionID = dbo.fn_getResourceFunctionID('ViewRegistrantsByDefault',dbo.fn_getResourceTypeID('Events'))
EXEC @rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@eventSiteResourceID, @functionID=@eventfunctionid, @inheritedRightsResourceID=@calApplicationSiteResourceID, @inheritedRightsFunctionID=@inheritedRightsFunctionID, @resourceRightID=@resourceRightID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
SELECT @eventfunctionid = dbo.fn_getResourceFunctionID('EditRegistrants',dbo.fn_getResourceTypeID('Event'))
SELECT @inheritedRightsFunctionID = dbo.fn_getResourceFunctionID('EditRegistrantsByDefault',dbo.fn_getResourceTypeID('Events'))
EXEC @rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@eventSiteResourceID, @functionID=@eventfunctionid, @inheritedRightsResourceID=@calApplicationSiteResourceID, @inheritedRightsFunctionID=@inheritedRightsFunctionID, @resourceRightID=@resourceRightID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
SELECT @eventfunctionid = dbo.fn_getResourceFunctionID('ManageFreeRates',dbo.fn_getResourceTypeID('Event'))
SELECT @inheritedRightsFunctionID = dbo.fn_getResourceFunctionID('ManageFreeRatesByDefault',dbo.fn_getResourceTypeID('Events'))
EXEC @rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@eventSiteResourceID, @functionID=@eventfunctionid, @inheritedRightsResourceID=@calApplicationSiteResourceID, @inheritedRightsFunctionID=@inheritedRightsFunctionID, @resourceRightID=@resourceRightID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
SELECT @eventfunctionid = dbo.fn_getResourceFunctionID('ManagePaidRates',dbo.fn_getResourceTypeID('Event'))
SELECT @inheritedRightsFunctionID = dbo.fn_getResourceFunctionID('ManagePaidRatesByDefault',dbo.fn_getResourceTypeID('Events'))
EXEC @rc = dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@eventSiteResourceID, @functionID=@eventfunctionid, @inheritedRightsResourceID=@calApplicationSiteResourceID, @inheritedRightsFunctionID=@inheritedRightsFunctionID, @resourceRightID=@resourceRightID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- add to its main calendar
EXEC @rc = dbo.ev_createCalendarEvent @calendarID=@calendarID, @sourceCalendarID=@calendarID, @sourceCategoryID=null, @sourceEventID=@eventID, @ovCategoryID=null
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error


-- get applicationTypeID
DECLARE @applicationTypeID int
select @applicationTypeID = dbo.fn_getApplicationTypeIDFromName('Events')

-- create activity log entry
EXEC platformstats.dbo.act_recordLog @memberID=@enteredByMemberID, @activityType='post', 
	@applicationTypeID=@applicationTypeID, @applicationInstanceID=@calApplicationInstanceID,
	@supportSiteResourceID=@eventSiteResourceID, @supportMemberID=null, @supportMessage=null
	IF @@ERROR <> 0 GOTO on_error


-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @eventID = 0
	RETURN -1

GO

print '%%%%%%%%%% ALTER ev_createRegistration SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROCEDURE [dbo].[ev_createRegistration] 
@eventID int,
@registrationTypeID int,
@startDate datetime,
@endDate datetime,
@registrantCap int,
@replyToEmail varchar(200),
@notifyEmail varchar(200),
@isPriceBasedOnActual bit,
@bulkCountByRate bit,
@registrationID int OUTPUT

AS

declare @rc int
select @registrationID = null

BEGIN TRAN

-- create content object
declare @appCreatedContentResourceTypeID int, @activeSiteResourceStatusID int, @defaultLanguageID int
declare @contentSiteResourceID int, @siteid int
declare @expirationcontentid int, @paymentContentId int, @registrantCapContentID int
select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')
select @activeSiteResourceStatusID = dbo.fn_getResourceStatusId('Active')
select @siteID=s.siteID, @defaultLanguageID=s.defaultLanguageID 
	from dbo.sites as s
	inner join dbo.ev_events as e on e.siteID = s.siteID
	where e.eventid = @eventID

-- create expirationcontentid
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@defaultLanguageID, 
	@isActive=1, @contentTitle='Expiration Message', @contentDesc=null, @rawContent='', @memberID=NULL, 
	@contentID=@expirationcontentid OUTPUT, 
	@siteResourceID=@contentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- create registrationcapcontentid
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@defaultLanguageID, 
	@isActive=1, @contentTitle='Registration Cap Message', @contentDesc=null, @rawContent='', @memberID=NULL,
	@contentID=@registrantCapContentID OUTPUT, 
	@siteResourceID=@contentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- add registration
insert into dbo.ev_registration (eventID, registrationTypeID, startDate, endDate, expirationContentID, registrantCap, registrantCapContentID, replyToEmail, notifyEmail, [status], isPriceBasedOnActual, bulkCountByRate)
values (@eventID, @registrationTypeID, @startDate, @endDate, @expirationContentID, @registrantCap, @registrantCapContentID, @replyToEmail, @notifyEmail, 'A', @isPriceBasedOnActual, @bulkCountByRate)
	IF @@ERROR <> 0 GOTO on_error
	select @registrationID = SCOPE_IDENTITY()

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @registrationID = 0
	RETURN -1

GO

print '%%%%%%%%%% ALTER ev_createSponsor SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROCEDURE [dbo].[ev_createSponsor] 
@eventID int,
@sponsorcontentID int OUTPUT

AS

select @sponsorcontentID = null
declare @rc int

BEGIN TRAN

-- create content object
declare @appCreatedContentResourceTypeID int, @activeSiteResourceStatusID int, @defaultLanguageID int
declare @sponsorcontentSiteResourceID int, @siteID int
select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')
select @activeSiteResourceStatusID = dbo.fn_getResourceStatusId('Active')
select @siteID=s.siteID, @defaultLanguageID=s.defaultLanguageID 
	from dbo.sites as s
	inner join dbo.ev_events as e on e.siteID = s.siteID
	where e.eventid = @eventID

-- get siteResourceID of the home calendar for the event
DECLARE @aiSiteResourceID int
SELECT @aiSiteResourceID = ai.siteResourceID
	FROM dbo.ev_events AS e 
	INNER JOIN dbo.ev_calendarEvents AS ce ON e.eventID = ce.sourceEventID 
	INNER JOIN dbo.ev_calendars AS c ON ce.calendarID = c.calendarID 
	INNER JOIN dbo.cms_applicationInstances AS ai ON c.applicationInstanceID = ai.applicationInstanceID
	WHERE e.eventID = @eventID 
	AND ce.sourceCalendarID = ce.calendarID
	IF @@ERROR <> 0 GOTO on_error

-- create sponsorContentID
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@parentSiteResourceID=@aiSiteResourceID, @siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, 
	@isHTML=1, @languageID=@defaultLanguageID, @isActive=1, @contentTitle=null, @contentDesc=null, 
	@rawContent='', @memberID=NULL,
	@contentID=@sponsorcontentID OUTPUT, @siteResourceID=@sponsorcontentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

declare @sponsorOrder int
select @sponsorOrder = isnull(max(sponsorOrder),0)+1 from dbo.ev_sponsors where eventID=@eventID
	IF @@ERROR <> 0 GOTO on_error

-- add sponsor
INSERT INTO dbo.ev_sponsors (eventID, sponsorContentID, sponsorOrder)
VALUES (@eventID, @sponsorcontentID, @sponsorOrder)
	IF @@ERROR <> 0 GOTO on_error

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @sponsorcontentID = 0
	RETURN -1
GO

print '%%%%%%%%%% ALTER menu_createMenu SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROCEDURE [dbo].[menu_createMenu]
 @orgID int,
 @siteid INT,
 @menuName varchar(200),
 @menuCode varchar(50),
 @menuRawContent varchar(max),
 @menuID INT OUTPUT
AS
BEGIN
 -- SET NOCOUNT ON added to prevent extra result sets from
 -- interfering with SELECT statements.
 SET NOCOUNT ON;
 DECLARE @rc INT, @trashID INT
 DECLARE @menuAdminResourceID INT, @siteResourceStatusID INT, @appCreatedContentResourceTypeID INT, @languageID INT, 
			@contentID INT, @contentSRID INT


 select @languageID = defaultLanguageID
 from dbo.sites
 where siteID = @siteID

 select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')
 select @siteResourceStatusID = dbo.fn_getResourceStatusId('Active')
 select @menuAdminResourceID = dbo.fn_getSiteResourceIDForResourceType('MenuAdmin',@siteid)

 BEGIN TRAN

 -- Add content object
 exec dbo.cms_createContentObject 
		@siteid, @appCreatedContentResourceTypeID, 
		@menuAdminResourceID, @siteResourceStatusID, 
		0, 1, @languageID, 1, '', '', @menuRawContent, NULL,
		@contentID OUTPUT, @contentSRID OUTPUT
 IF @@ERROR <> 0 GOTO on_error

 insert into dbo.cms_menus(menuName, menuCode, contentID, siteID)
 values(@menuName, @menuCode, @contentID, @siteID)   
  IF @@ERROR <> 0 GOTO on_error
 
 SELECT @menuID = SCOPE_IDENTITY()


 IF @@TRANCOUNT > 0 COMMIT TRAN
 RETURN 0
 on_error:
  select @menuID = 0
  RETURN -1
END

GO

print '%%%%%%%%%% ALTER migrate_store SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROC [dbo].[migrate_store]
@orgcode varchar(5)

AS

-- this can be called if orgcode on old, sitecode on new, and orgcode on new are all the same
-- requires store to have been created

set nocount on

DECLARE @errCode int, @siteID int, @storeID int, @storeContentID int, @rc int, @minCatID int,
	@newCatID int, @minPCID int, @newPCID int, @minItemID int, @appCreatedContentResourceTypeID int,
	@appInstanceResourceID int, @activeSiteResourceStatusID int, @contentID int, 
	@contentSiteResourceID int, @newItemID int, @minFormatID int, @newFormatID int
DECLARE @oldRoot varchar(26), @ContentPath varchar(60), @ptitle varchar(200),
	@pid varchar(50), @pImage varchar(400), @tImage varchar(400)
DECLARE @rawContent varchar(max), @pDesc varchar(max)
DECLARE @showQuantity bit
DECLARE @store_Categories TABLE (oldcategoryID int, newcategoryID int)
DECLARE @store_PriceCodes TABLE (oldPriceCodeid int, newPriceCodeID int)
DECLARE @store_products TABLE (olditemID int, newItemID int)
DECLARE @store_productFormats TABLE (oldformatid int, newformatid int)
DECLARE @tblProducts TABLE (ItemID int, ProductID varchar(50), ProductName varchar(200), 
	ProductDesc varchar(max), productImage varchar(400), thumbnailImage varchar(400), 
	ShowQuantity bit)

SET @errCode = 0
SET @siteID = dbo.fn_getSiteIDFromSiteCode(@orgcode)
SET @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')
SET @activeSiteResourceStatusID = dbo.fn_getResourceStatusId('Active')

IF @@servername = 'TSSQL1\MEMBERCENTRAL'
	set @oldRoot = '\\tsfile1\f$\tlasites\www\'
ELSE
	set @oldRoot = '\\devserver\tlasites\www\'

-- store needed
SELECT @storeID = storeID from dbo.store where siteID = @siteID
IF @storeID is null BEGIN
	SET @errCode = 900
	GOTO on_done
END

-- merchant profile needed
IF NOT EXISTS (select storeID from dbo.store_merchantProfiles where storeID = @storeID) BEGIN
	SET @errCode = 901
	GOTO on_done
END

SELECT @appInstanceResourceID = siteResourceID
	FROM dbo.cms_applicationInstances as ai
	INNER JOIN dbo.store as s on s.applicationInstanceID = ai.applicationInstanceID
	WHERE s.storeID = @storeID
SELECT @storeContentID = mainContentID from dbo.store where siteID = @siteID
SELECT @ContentPath = @oldRoot + @orgcode + '\views\StoreMainContent.cfm'
SELECT @rawContent = dbo.fn_readFile(@ContentPath,0,0)
	IF @@ERROR <> 0 BEGIN
		SET @errCode = 902
		GOTO on_done
	END

BEGIN TRAN

	-- store main content
	IF @storeContentID is not null BEGIN
		exec @rc = dbo.cms_updateContent @contentID=@storeContentID, @languageID=1, @isSSL=0,
			@isHTML=1, @contentTitle='StoreMain', @contentDesc='', @rawcontent=@rawContent
			IF @@ERROR <> 0 OR @rc <> 0 BEGIN
				SET @errCode = 903
				GOTO on_done
			END
	END

	-- store categories
	SELECT @minCatID = min(categoryID)
		FROM tlasites.trialsmith.dbo.storeCategories
		WHERE orgCode = @orgcode
	WHILE @minCatID is not null BEGIN
		INSERT INTO dbo.store_Categories (storeID, CategoryName)
		SELECT @storeID, categoryName
		FROM tlasites.trialsmith.dbo.storeCategories
		WHERE orgCode = @orgcode
		AND categoryID = @minCatID
			IF @@ERROR <> 0 BEGIN
				SET @errCode = 904
				GOTO on_done
			END
			select @newCatID = SCOPE_IDENTITY()

		INSERT INTO @store_Categories (oldcategoryID, newCategoryID)
		VALUES (@minCatID, @newCatID)
			IF @@ERROR <> 0 BEGIN
				SET @errCode = 905
				GOTO on_done
			END

		SELECT @minCatID = min(categoryID)
			FROM tlasites.trialsmith.dbo.storeCategories
			WHERE orgCode = @orgcode
			AND categoryID > @minCatID
	END

	-- store price codes
	SELECT @minPCID = min(PriceCodeid)
		FROM tlasites.trialsmith.dbo.storePriceCodes
		WHERE orgCode = @orgcode
	WHILE @minPCID is not null BEGIN

		declare @siteResourceID int, @siteResourceTypeID int, @siteResourceStatusID int
		
		select @siteResourceTypeID = dbo.fn_getResourceTypeID('storePriceCode')
		select @siteResourceStatusID = dbo.fn_getResourceStatusID('Active')
	
		exec dbo.cms_createSiteResource
					@resourceTypeID = @siteResourceTypeID,
					@siteResourceStatusID = @siteResourceStatusID,
					@siteID = @siteID,
					@isVisible = 1,
					@parentSiteResourceID = null,
					@siteResourceID   = @siteResourceID OUTPUT					

		INSERT INTO dbo.store_PriceCodes (storeID, [Name], Visible, siteResourceID)
		SELECT @storeID, [Name], Visible,  @siteResourceID
		FROM tlasites.trialsmith.dbo.storePriceCodes
		WHERE orgCode = @orgcode
		AND PriceCodeid = @minPCID
			IF @@ERROR <> 0 BEGIN
				SET @errCode = 906
				GOTO on_done
			END
			select @newPCID = SCOPE_IDENTITY()

		INSERT INTO @store_PriceCodes (oldPriceCodeid, newPriceCodeid)
		VALUES (@minPCID, @newPCID)
			IF @@ERROR <> 0 BEGIN
				SET @errCode = 907
				GOTO on_done
			END

		SELECT @minPCID = min(PriceCodeid)
			FROM tlasites.trialsmith.dbo.storePriceCodes
			WHERE orgCode = @orgcode
			AND PriceCodeid > @minPCID
	END

	-- get products
	INSERT INTO @tblProducts (ItemID, ProductID, ProductName, ProductDesc, productImage, thumbnailImage, ShowQuantity)
	SELECT ItemID, ProductID, ProductName, Details, ImageURL, Thumbnail, ShowQuantity = case ShowQuantity WHEN 'Yes' THEN 1 ELSE 0 END
	FROM tlasites.trialsmith.dbo.storeProducts
	WHERE orgCode = @orgcode
		IF @@ERROR <> 0 BEGIN
			SET @errCode = 908
			GOTO on_done
		END

	select @minItemID = min(itemID) from @tblProducts
	while @minItemID is not null BEGIN
		select @ptitle = ProductName, @pDesc = ProductDesc, @pID = ProductID,
			@pImage = productImage, @tImage = thumbnailImage, @showQuantity = ShowQuantity
			from @tblProducts
			where ItemID = @minItemID

		-- create contentID
		EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, 
			@resourceTypeID=@appCreatedContentResourceTypeID, 
			@parentSiteResourceID=@appInstanceResourceID,
			@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=1, 
			@isActive=1, @contentTitle=@ptitle, @contentDesc=null, @rawContent=@pDesc, @memberID=NULL,
			@contentID=@contentID OUTPUT, @siteResourceID=@contentSiteResourceID OUTPUT
			IF @@ERROR <> 0 OR @RC <> 0 OR @contentID = 0 BEGIN
				SET @errCode = 909
				GOTO on_done
			END

		-- add product
		INSERT INTO dbo.store_products (storeID, productID, productContentID, productImage, thumbnailImage, showQuantity, status)
		VALUES (@storeid, @pID, @contentID, @pImage, @tImage, @showQuantity, 'A')
			IF @@ERROR <> 0 BEGIN
				SET @errCode = 910
				GOTO on_done
			END
			SELECT @newItemID = SCOPE_IDENTITY()

		INSERT INTO @store_products (oldItemID, newItemID)
		VALUES (@minItemID, @newItemID)
			IF @@ERROR <> 0 BEGIN
				SET @errCode = 911
				GOTO on_done
			END

		select @minItemID = min(itemID) from @tblProducts where itemID > @minItemID
	END

	-- products and categories
	INSERT INTO dbo.store_ProductCategoryLinks(ItemID, CategoryID)
	SELECT newp.newItemID, newc.newCategoryID
	FROM tlasites.trialsmith.dbo.storeProductCategoryLinks as oldpl
	INNER JOIN tlasites.trialsmith.dbo.storeProducts as oldp ON oldp.itemID = oldpl.itemID AND oldp.orgCode = @orgcode
	inner join @store_products as newp on newp.oldItemID = oldp.itemid
	inner join @store_Categories as newc on newc.oldcategoryID = oldpl.categoryID
		IF @@ERROR <> 0 BEGIN
			SET @errCode = 912
			GOTO on_done
		END

	-- product formats
	select @minFormatID = min(pf.formatid)
		FROM tlasites.trialsmith.dbo.storeProductFormats as pf
		INNER JOIN tlasites.trialsmith.dbo.storeProducts as p ON p.itemID = pf.itemID 
			AND p.orgCode = @orgcode
	while @minFormatID is not null BEGIN
		INSERT INTO dbo.store_ProductFormats(Itemid, Name)
		select newp.newItemID, pf.name
		FROM tlasites.trialsmith.dbo.storeProductFormats as pf
		inner join @store_products as newp on newp.oldItemID = pf.Itemid
		where pf.formatID = @minFormatID
			IF @@ERROR <> 0 BEGIN
				SET @errCode = 913
				GOTO on_done
			END
			SELECT @newformatID = SCOPE_IDENTITY()

		INSERT INTO @store_ProductFormats (oldformatid, newformatid)
		VALUES (@minFormatID, @newformatID)
			IF @@ERROR <> 0 BEGIN
				SET @errCode = 914
				GOTO on_done
			END

		select @minFormatID = min(pf.formatid)
			FROM tlasites.trialsmith.dbo.storeProductFormats as pf
			INNER JOIN tlasites.trialsmith.dbo.storeProducts as p ON p.itemID = pf.itemID 
				AND p.orgCode = @orgcode
			WHERE pf.formatid > @minFormatID
	END

	INSERT INTO dbo.store_ProductPrices(FormatID, Price, ShippingCosts)
	SELECT newpf.newformatid, pp.price, pp.ShippingCosts
	FROM tlasites.trialsmith.dbo.storeProductPrices pp
	INNER JOIN tlasites.trialsmith.dbo.storeProductFormats pf ON pp.formatID = pf.formatID
	INNER JOIN tlasites.trialsmith.dbo.storeProducts p ON p.itemID = pf.itemID AND p.orgCode = @orgcode
	INNER JOIN @store_ProductFormats as newpf on newpf.oldformatid = pp.formatid
	INNER JOIN @store_PriceCodes as newpc on newpc.oldPriceCodeid = pp.pricecodeid
		IF @@ERROR <> 0 BEGIN
			SET @errCode = 915
			GOTO on_done
		END

COMMIT TRAN


on_done:
	IF @errCode = 900
		print 'Migration stopped. Store instance is not installed.' 
	IF @errCode = 901
		print 'Migration stopped. Store has no merchant profiles.' 
	IF @errCode = 902
		print 'Migration stopped. Unable to read StoreMainContent.' 
	IF @errCode = 903
		print 'Migration stopped. Unable to update store content.' 
	IF @errCode = 904
		print 'Migration stopped. Unable to create new category.' 
	IF @errCode = 905
		print 'Migration stopped. Unable to insert into @store_Categories.' 
	IF @errCode = 906
		print 'Migration stopped. Unable to create new price code.' 
	IF @errCode = 907
		print 'Migration stopped. Unable to insert into @store_PriceCodes.' 
	IF @errCode = 908
		print 'Migration stopped. Unable to get products.' 
	IF @errCode = 909
		print 'Migration stopped. Unable to create new content object for ' + @ptitle + '.' 
	IF @errCode = 910
		print 'Migration stopped. Unable to insert into store_products.' 
	IF @errCode = 911
		print 'Migration stopped. Unable to insert into @store_products.' 
	IF @errCode = 912
		print 'Migration stopped. Unable to insert into store_ProductCategoryLinks.' 
	IF @errCode = 913
		print 'Migration stopped. Unable to insert into store_ProductFormats.' 
	IF @errCode = 914
		print 'Migration stopped. Unable to insert into @store_ProductFormats.' 
	IF @errCode = 915
		print 'Migration stopped. Unable to insert into store_ProductPrices.' 

	IF @errCode >= 903
		ROLLBACK TRAN

	print 'Migration migrate_store finished.'

GO

print '%%%%%%%%%% ALTER mp_insertProfile SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROCEDURE [dbo].[mp_insertProfile]
@siteID int,
@gatewayID int,
@profileName varchar(100),
@profileCode varchar(20),
@gatewayUsername varchar(50),
@gatewayPassword varchar(50),
@allowPayments bit,
@allowRefunds bit,
@allowRefundsFromAnyProfile bit,
@GLAccountID int,
@gatewayMerchantId varchar(50),
@allowPayInvoicesOnline bit,
@bankAccountName varchar(200),
@profileID int OUTPUT

AS

SELECT @profileID = null

BEGIN TRAN

INSERT INTO dbo.mp_profiles (siteID, gatewayID, profileName, profileCode, gatewayUsername, 
	gatewayPassword, gatewayMerchantId, allowPayments, allowRefunds, allowRefundsFromAnyProfile, 
	GLAccountID, [status], allowPayInvoicesOnline, bankAccountName)
VALUES (@siteID, @gatewayID, @profileName, @profileCode, @gatewayUsername, @gatewayPassword, 
	@gatewayMerchantId, @allowPayments, @allowRefunds, @allowRefundsFromAnyProfile, 
	nullif(@GLAccountID,0), 'A', @allowPayInvoicesOnline, @bankAccountName)
	IF @@ERROR <> 0 GOTO on_error
	SELECT @profileID = SCOPE_IDENTITY()

IF @gatewayID = 11 BEGIN
	declare @rc int, @sysCreatedContentResourceTypeID int, @activeSiteResourceStatusID int,
		@PayInstrContentID int, @PayInstrSiteResourceID int
	select @sysCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('SystemCreatedContent')
		IF @@ERROR <> 0 GOTO on_error
	select @activeSiteResourceStatusID = dbo.fn_getResourceStatusId('Active')
		IF @@ERROR <> 0 GOTO on_error

	EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@sysCreatedContentResourceTypeID, 
		@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=1, 
		@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', @memberID=NULL,
		@contentID=@PayInstrContentID OUTPUT, 
		@siteResourceID=@PayInstrSiteResourceID OUTPUT
		IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

	UPDATE dbo.mp_profiles
	SET paymentInstructionsContentID = @PayInstrContentID
	WHERE profileID = @profileID
		IF @@ERROR <> 0 GOTO on_error
END

IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN	
	SELECT @profileID = 0
	RETURN -1
GO

print '%%%%%%%%%% ALTER nt_addNote SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROCEDURE [dbo].[nt_addNote] 
	@siteID int,
	@languageID int,
	@categoryID int, 
	@subCategoryID int,
	@creatorMemberID int,
	@assignedMemberID int, 
	@associatedMemberID int,
	@noteStatusID int,
	@noteDetails varchar(max),
	@isNoteDetailsHTML bit,
	@dateDue datetime = NULL,
	@noteID int OUTPUT
AS

BEGIN TRAN

DECLARE @NoteResourceTypeID int,
		@siteResourceStatusID int,
		@appCreatedContentResourceTypeID int,
		@thisNoteResourceID int,
		@thisNoteContentResourceID int,
		@thisNoteContentID int

select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')
select @NoteResourceTypeID  = dbo.fn_getResourceTypeId('Note')
select @siteResourceStatusID = dbo.fn_getResourceStatusId('Active')


-- first create a resourceID for the note
exec dbo.cms_createSiteResource
		@resourceTypeID = @NoteResourceTypeID,
		@siteResourceStatusID = @siteResourceStatusID,
		@siteID = @siteid,
		@isVisible = 1,
		@parentSiteResourceID = NULL,
		@siteResourceID   = @thisNoteResourceID OUTPUT

-- create note content object
exec dbo.cms_createContentObject @siteID, @appCreatedContentResourceTypeID, null, @siteResourceStatusID, 
								 0, @isNoteDetailsHTML, @languageID, 1, '', '', @noteDetails, @creatorMemberID,
								 @thisNoteContentID OUTPUT, @thisNoteContentResourceID OUTPUT
	IF @@ERROR <> 0 GOTO on_error


-- insert notice
insert into dbo.nt_notes(categoryID, subCategoryID, creatorMemberID, assignedMemberID, associatedMemberID, noteStatusID, noteContentID, siteResourceID, dateEntered, dateDue)
values(@categoryID, @subCategoryID, @creatorMemberID, @assignedMemberID, @associatedMemberID, @noteStatusID, @thisNoteContentID, @thisNoteResourceID, getdate(), @dateDue)
	IF @@ERROR <> 0 GOTO on_error
	select @noteID = SCOPE_IDENTITY()


-- update the parentSiteResourceID on the content object.
update dbo.cms_siteResources 
set parentSiteResourceID = @thisNoteResourceID
where siteResourceID = @thisNoteContentResourceID
	IF @@ERROR <> 0 GOTO on_error

exec dbo.insertCategorySiteResource @categoryID, @thisNoteResourceID
	IF @@ERROR <> 0 GOTO on_error

exec dbo.insertCategorySiteResource @subCategoryID, @thisNoteResourceID
	IF @@ERROR <> 0 GOTO on_error



IF @@TRANCOUNT > 0 COMMIT TRAN

-- normal exit
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @noteID = 0
	RETURN -1

GO
print '%%%%%%%%%% ALTER store_createProduct SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROCEDURE [dbo].[store_createProduct] 
@siteid int,
@storeid int,
@productID varchar(50),
@languageID int,
@productTitle varchar(200),
@productDesc varchar(max),
@productImage varchar(400),
@thumbnailImage varchar(400),
@showQuantity bit,
@showAvailable bit,
@GLAccountID int,
@ShippingGLAccountID int,
@productSummary varchar(max),
@productDate datetime,
@itemID int OUTPUT,
@errorMessage varchar(100) = '' OUTPUT
AS

BEGIN TRAN

declare @rc int, @appCreatedContentResourceTypeID int, @activeSiteResourceStatusID int
declare @contentID int, @contentSiteResourceID int, @summaryContentID int
select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')
select @activeSiteResourceStatusID = dbo.fn_getResourceStatusId('Active')

select @itemID = null
select @errorMessage = null

-- check to see if the productID already exists as an active product in this store
IF EXISTS (select itemID from dbo.store_products where productID = @productID and storeID = @storeID and status = 'A') BEGIN
	set @errorMessage = 'ProductIDExists'
	GOTO on_error
END

-- create contentID for productContentID
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@languageID, 
	@isActive=1, @contentTitle=@productTitle, @contentDesc=null, @rawContent=@productDesc, @memberID=NULL,
	@contentID=@contentID OUTPUT, @siteResourceID=@contentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- create contentID for summaryContentID
EXEC @rc = dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
	@siteResourceStatusID=@activeSiteResourceStatusID, @isSSL=0, @isHTML=1, @languageID=@languageID, 
	@isActive=1, @contentTitle='ProductSummary', @contentDesc=null, @rawContent=@productSummary, @memberID=NULL,
	@contentID=@summaryContentID OUTPUT, @siteResourceID=@contentSiteResourceID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- add product
INSERT INTO dbo.store_products (storeID, productID, productContentID, productImage, thumbnailImage, showQuantity, showAvailable, status, GLAccountID, ShippingGLAccountID, summaryContentID, productDate)
VALUES (@storeid, @productID, @contentID, @productImage, @thumbnailImage, @showQuantity, @showAvailable, 'A', @GLAccountID, @ShippingGLAccountID, @summaryContentID, @productDate)
	IF @@ERROR <> 0 GOTO on_error
	SELECT @itemID = SCOPE_IDENTITY()

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @itemID = 0
	RETURN -1

GO

print '%%%%%%%%%% ALTER sub_createSubscription SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROCEDURE [dbo].[sub_createSubscription]
 @orgID int,
 @siteid INT,
 @typeID INT,
 @subName varchar(300),
 @reportCode varchar(15),
 @scheduleID INT,
 @rateTermDateFlag varchar(1),
 @autoExpire BIT,
 @status varchar(1),
 @comments varchar(max),
 @isFeatured BIT,
 @soldSeparately BIT,
 @paymentOrder INT,
 @GLAccountID INT,
 @allowRateGLOverride BIT,
 @activationOptionCode varchar(1),
 @alternateActivationOptionCode varchar(1),
 @feRawContent varchar(max),
 @feCompletedRawContent varchar(max),
 @subID INT OUTPUT
AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	DECLARE @typeGroupCode varchar(30), @typeGroupID INT, @activationOptionID int, @alternateActivationOptionID int, @rc INT
	DECLARE @groupCode varchar(30), @groupName varchar(115), @groupDesc varchar(200), @trashID INT
	DECLARE @subAdminResourceID INT, @siteResourceStatusID INT, @appCreatedContentResourceTypeID INT, @languageID INT, @contentID INT, @contentSRID INT, @completedContentID INT, @completedContentSRID INT

	SELECT typeID
	from dbo.sub_Types
	where typeID = @typeID
	and siteID = @siteid
		IF @@ROWCOUNT < 1 RAISERROR('More than 1 type found',16,1)

	SELECT @activationOptionID = subActivationID
	from dbo.sub_activationOptions
	where subActivationCode = @activationOptionCode

	SELECT @alternateActivationOptionID = subActivationID
	from dbo.sub_activationOptions
	where subActivationCode = @alternateActivationOptionCode

	select @languageID = defaultLanguageID
	from dbo.sites
	where siteID = @siteID

	select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')
	select @siteResourceStatusID = dbo.fn_getResourceStatusId('Active')
	select @subAdminResourceID = dbo.fn_getSiteResourceIDForResourceType('SubscriptionAdmin',@siteid)

	insert into dbo.sub_subscriptions(typeID, subscriptionName, reportCode, scheduleID, autoExpire, [status], comments, isFeatured, soldSeparately, paymentOrder, GLAccountID, rateTermDateFlag, subActivationID, subAlternateActivationID, allowRateGLAccountOverride)
	values(@typeID, @subName, @reportCode, @scheduleID, @autoExpire, @status, @comments, @isFeatured, @soldSeparately, @paymentOrder, @GLAccountID, @rateTermDateFlag, @activationOptionID, @alternateActivationOptionID, @allowRateGLOverride)   
		SELECT @subID = SCOPE_IDENTITY()

	exec dbo.cms_createContentObject @siteid, @appCreatedContentResourceTypeID, @subAdminResourceID, @siteResourceStatusID, 
		0, 1, @languageID, 1, '', '', @feRawContent, NULL,
		@contentID OUTPUT, @contentSRID OUTPUT

	exec dbo.cms_createContentObject @siteid, @appCreatedContentResourceTypeID, @subAdminResourceID, @siteResourceStatusID, 
		0, 1, @languageID, 1, '', '', @feCompletedRawContent, NULL,
		@completedContentID OUTPUT, @completedContentSRID OUTPUT

	update dbo.sub_subscriptions 
	set frontEndContentID = @contentID, 
		frontEndCompletedContentID = @completedContentID
	where subscriptionID = @subID

	-- Add groups
	SELECT @typeGroupCode = 'Active_' + cast(@typeID AS varchar(10)) + '_Tracking'
	SELECT @typeGroupID=groupID
	FROM dbo.ams_groups
	WHERE groupCode = @typeGroupCode AND [status] <> 'D'
	IF @typeGroupID IS NOT NULL BEGIN
		SELECT @groupCode = 'SubActive_' + cast(@subID AS varchar(9)) + '_Tracking'
		SELECT @groupName = @subName + '(Active)'
		SELECT @groupDesc = 'Subscription ' + cast(@subID as varchar(9)) + ' Active Tracking Group'
		EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode=@groupCode, @groupName=@groupName,
			@groupDesc=@groupDesc, @isSystemGroup=0, @allowManualAssignment=1,
			@parentGroupID=@typeGroupID, @hideOnGroupLists=1, @groupID=@trashID OUTPUT
		IF @trashID = 0 RAISERROR('Unable to create group %s with groupcode %s',16,1,@groupName,@groupCode)
	END

	SELECT @typeGroupCode = 'WaitingPay_' + cast(@typeID AS varchar(10)) + '_Tracking'
	SELECT @typeGroupID=groupID
	FROM dbo.ams_groups
	WHERE groupCode = @typeGroupCode AND [status] <> 'D'
	IF @typeGroupID IS NOT NULL BEGIN
		SELECT @groupCode = 'SubWaitingPay_' + cast(@subID AS varchar(9)) + '_Tracking'
		SELECT @groupName = @subName + '(Not Activated)'
		SELECT @groupDesc = 'Subscription ' + cast(@subID as varchar(9)) + ' Not Activated Tracking Group'
		EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode=@groupCode, @groupName=@groupName,
			@groupDesc=@groupDesc, @isSystemGroup=0, @allowManualAssignment=1,
			@parentGroupID=@typeGroupID, @hideOnGroupLists=1, @groupID=@trashID OUTPUT
		IF @trashID = 0 RAISERROR('Unable to create group %s with groupcode %s',16,1,@groupName,@groupCode)
	END

	SELECT @typeGroupCode = 'Inactive_' + cast(@typeID as varchar(10)) + '_Tracking'
	SELECT @typeGroupID=groupID
	FROM dbo.ams_groups
	WHERE groupCode = @typeGroupCode AND [status] <> 'D'
	IF @typeGroupID IS NOT NULL BEGIN
		SELECT @groupCode = 'SubInactive_' + cast(@subID AS varchar(9)) + '_Tracking'
		SELECT @groupName = @subName + '(Inactive)'
		SELECT @groupDesc = 'Subscription ' + cast(@subID as varchar(9)) + ' Inactive Tracking Group'
		EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode=@groupCode, @groupName=@groupName,
			@groupDesc=@groupDesc, @isSystemGroup=0, @allowManualAssignment=1,
			@parentGroupID=@typeGroupID, @hideOnGroupLists=1, @groupID=@trashID OUTPUT
		IF @trashID = 0 RAISERROR('Unable to create group %s with groupcode %s',16,1,@groupName,@groupCode)
	END

	SELECT @typeGroupCode = 'Renewable_' + cast(@typeID as varchar(10)) + '_Tracking'
	SELECT @typeGroupID=groupID
	FROM dbo.ams_groups
	WHERE groupCode = @typeGroupCode AND [status] <> 'D'
	IF @typeGroupID IS NOT NULL BEGIN
		SELECT @groupCode = 'SubRenew_' + cast(@subID AS varchar(9)) + '_Tracking'
		SELECT @groupName = @subName + '(Renewable)'
		SELECT @groupDesc = 'Subscription ' + cast(@subID as varchar(9)) + ' Renewable Tracking Group'
		EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode=@groupCode, @groupName=@groupName,
			@groupDesc=@groupDesc, @isSystemGroup=0, @allowManualAssignment=1,
			@parentGroupID=@typeGroupID, @hideOnGroupLists=1, @groupID=@trashID OUTPUT
		IF @trashID = 0 RAISERROR('Unable to create group %s with groupcode %s',16,1,@groupName,@groupCode)
	END

	SELECT @typeGroupCode = 'Pending_' + cast(@typeID as varchar(10)) + '_Tracking'
	SELECT @typeGroupID=groupID
	FROM dbo.ams_groups
	WHERE groupCode = @typeGroupCode AND [status] <> 'D'
	IF @typeGroupID IS NOT NULL BEGIN
		SELECT @groupCode = 'SubPending_' + cast(@subID AS varchar(9)) + '_Tracking'
		SELECT @groupName = @subName + '(Accepted)'
		SELECT @groupDesc = 'Subscription ' + cast(@subID as varchar(9)) + ' Pending Tracking Group'
		EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode=@groupCode, @groupName=@groupName,
			@groupDesc=@groupDesc, @isSystemGroup=0, @allowManualAssignment=1,
			@parentGroupID=@typeGroupID, @hideOnGroupLists=1, @groupID=@trashID OUTPUT
		IF @trashID = 0 RAISERROR('Unable to create group %s with groupcode %s',16,1,@groupName,@groupCode)
	END

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	select @subID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

print '%%%%%%%%%% ALTER sub_createSubscriptionType SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROC [dbo].[sub_createSubscriptionType] 
	@orgID int, 
	@siteid int,
	@typeName varchar(100),
	@feRawContent varchar(max),
	@feCompletedRawContent varchar(max),
	@feEmailNotification varchar(max),
	@subTypeID INT OUTPUT
AS 

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	DECLARE @rc INT
	DECLARE @subAdminResourceTypeID INT, @siteResourceStatusID INT, @appCreatedContentResourceTypeID INT, @languageID INT, @contentID INT, @contentSRID INT,  @completedContentID INT, @completedContentSRID INT

	select @languageID = defaultLanguageID
		from dbo.sites
		where siteID = @siteID

	select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent')
	select @siteResourceStatusID = dbo.fn_getResourceStatusId('Active')
	select @subAdminResourceTypeID = dbo.fn_getSiteResourceIDForResourceType('SubscriptionAdmin',@siteid)

	-- insert into types
	INSERT INTO dbo.sub_Types (typeName, siteID, feEmailNotification)
	VALUES(@typeName, @siteid, @feEmailNotification)
		SELECT @subTypeID = SCOPE_IDENTITY()

	-- Add content object
	exec dbo.cms_createContentObject @siteid, @appCreatedContentResourceTypeID, @subAdminResourceTypeID, @siteResourceStatusID, 
									 0, 1, @languageID, 1, '', '', @feRawContent, NULL,
									 @contentID OUTPUT, @contentSRID OUTPUT

	exec dbo.cms_createContentObject @siteid, @appCreatedContentResourceTypeID, @subAdminResourceTypeID, @siteResourceStatusID, 
									 0, 1, @languageID, 1, '', '', @feCompletedRawContent, NULL,
									 @completedContentID OUTPUT, @completedContentSRID OUTPUT

	update dbo.sub_Types 
	set frontEndContentID = @contentID, 
		frontEndCompletedContentID = @completedContentID
	where typeID = @subTypeID

	-- create tracking groups
	DECLARE @groupCode varchar(30), @groupName varchar(115), @groupDesc varchar(200), @subTypeGroupID int, @trashID int
	SELECT @groupCode = 'SubType_' + cast(@subTypeID as varchar(10)) + '_Tracking'
	SELECT @groupName = @typeName + ' (All)'
	SELECT @groupDesc = 'Subscription Type ' + cast(@subTypeID as varchar(10)) + ' Tracking Group'
	EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode=@groupCode, @groupName=@groupName,
		@groupDesc=@groupDesc, @isSystemGroup=0, @allowManualAssignment=1, 
		@parentGroupID=null, @hideOnGroupLists=1, @groupID=@subTypeGroupID OUTPUT
		IF @subTypeGroupID = 0 RAISERROR('Unable to create group %s with groupcode %s',16,1,@groupName,@groupCode)

	SELECT @groupCode = 'Active_' + cast(@subTypeID as varchar(10)) + '_Tracking'
	SELECT @groupName = @typeName + ' (Active)'
	SELECT @groupDesc = 'Subscription Type ' + cast(@subTypeID as varchar(10)) + ' Active Tracking Group'
	EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode=@groupCode, @groupName=@groupName,
		@groupDesc=@groupDesc, @isSystemGroup=0, @allowManualAssignment=1, 
		@parentGroupID=@subTypeGroupID, @hideOnGroupLists=1, @groupID=@trashID OUTPUT
		IF @trashID = 0 RAISERROR('Unable to create group %s with groupcode %s',16,1,@groupName,@groupCode)

	SELECT @groupCode = 'WaitingPay_' + cast(@subTypeID as varchar(10)) + '_Tracking'
	SELECT @groupName = @typeName + ' (Not Activated)'
	SELECT @groupDesc = 'Subscription Type ' + cast(@subTypeID as varchar(10)) + ' Not Activated Tracking Group'
	EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode=@groupCode, @groupName=@groupName,
		@groupDesc=@groupDesc, @isSystemGroup=0, @allowManualAssignment=1, 
		@parentGroupID=@subTypeGroupID, @hideOnGroupLists=1, @groupID=@trashID OUTPUT
		IF @trashID = 0 RAISERROR('Unable to create group %s with groupcode %s',16,1,@groupName,@groupCode)

	SELECT @groupCode = 'Inactive_' + cast(@subTypeID as varchar(10)) + '_Tracking'
	SELECT @groupName = @typeName + ' (Inactive)'
	SELECT @groupDesc = 'Subscription Type ' + cast(@subTypeID as varchar(10)) + ' Inactive Tracking Group'
	EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode=@groupCode, @groupName=@groupName,
		@groupDesc=@groupDesc, @isSystemGroup=0, @allowManualAssignment=1, 
		@parentGroupID=@subTypeGroupID, @hideOnGroupLists=1, @groupID=@trashID OUTPUT
		IF @trashID = 0 RAISERROR('Unable to create group %s with groupcode %s',16,1,@groupName,@groupCode)

	SELECT @groupCode = 'Renewable_' + cast(@subTypeID as varchar(10)) + '_Tracking'
	SELECT @groupName = @typeName + ' (Renewable)'
	SELECT @groupDesc = 'Subscription Type ' + cast(@subTypeID as varchar(10)) + ' Renewable Tracking Group'
	EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode=@groupCode, @groupName=@groupName,
		@groupDesc=@groupDesc, @isSystemGroup=0, @allowManualAssignment=1, 
		@parentGroupID=@subTypeGroupID, @hideOnGroupLists=1, @groupID=@trashID OUTPUT
		IF @trashID = 0 RAISERROR('Unable to create group %s with groupcode %s',16,1,@groupName,@groupCode)

	SELECT @groupCode = 'Pending_' + cast(@subTypeID as varchar(10)) + '_Tracking'
	SELECT @groupName = @typeName + ' (Accepted)'
	SELECT @groupDesc = 'Subscription Type ' + cast(@subTypeID as varchar(10)) + ' Pending Tracking Group'
	EXEC dbo.ams_createGroup @orgID=@orgID, @groupCode=@groupCode, @groupName=@groupName,
		@groupDesc=@groupDesc, @isSystemGroup=0, @allowManualAssignment=1, 
		@parentGroupID=@subTypeGroupID, @hideOnGroupLists=1, @groupID=@trashID OUTPUT
		IF @trashID = 0 RAISERROR('Unable to create group %s with groupcode %s',16,1,@groupName,@groupCode)

	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	SELECT @subTypeID = 0
	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH

GO

print '%%%%%%%%%% ALTER ams_importMemberData_holdingToPerm SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROC [dbo].[ams_importMemberData_holdingToPerm]
@orgID int,
@flatfile varchar(160)

AS

SET NOCOUNT ON

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Starting importMemberData_toPerm'

declare @stepNum tinyint
declare @returnVal int
declare @cmd varchar(400), @sql varchar(max), @cmd2 varchar(8000), @createSQL varchar(max)
declare @logTable TABLE (stepNum tinyint, stepMsg varchar(100), stepDate datetime)
DECLARE @orgCode varchar(10), @hasPrefix bit, @hasMiddleName bit, @hasSuffix bit, @hasProfessionalSuffix bit, @hasCompany bit
select @orgcode=orgcode, @hasPrefix=hasPrefix, @hasMiddleName=hasMiddleName,
	@hasSuffix=hasSuffix, @hasProfessionalSuffix=hasProfessionalSuffix, 
	@hasCompany=hasCompany
	from dbo.organizations 
	where orgID = @orgid

EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Preparing to Import Flattened Data'

-- see if files exist
if dbo.fn_fileExists(@flatfile + '.sql') = 0
	or dbo.fn_fileExists(@flatfile + '.txt') = 0
	or dbo.fn_fileExists(@flatfile + '.bcp') = 0
	BEGIN
		EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Flattened Data files do not exist'
		RETURN -1
	END

-- **************
-- read in sql create script and create global temp table
-- **************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Creating table for flattened data'
select @createSQL = replace(dbo.fn_ReadFile(@flatfile + '.sql',0,1),'##xxx','##importMemberData')
IF OBJECT_ID('tempdb..##importMemberData') IS NOT NULL
	EXEC('DROP TABLE ##importMemberData')
EXEC(@createSQL)

-- *******************
-- bcp in data
-- *******************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Importing flattened data'
select @cmd = 'bcp ##importMemberData in ' + @flatfile + '.bcp -f ' + @flatfile + '.txt -n -T -S' + CAST(serverproperty('servername') as varchar(40))
exec master..xp_cmdshell @cmd, NO_OUTPUT

-- *******************
-- immediately put into local temp table and drop global temp (memberid is a holder to be updated later)
-- *******************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Importing into local temp table'
select 0 as memberID, * into #importMemberData from ##importMemberData
IF OBJECT_ID('tempdb..##importMemberData') IS NOT NULL
	EXEC('DROP TABLE ##importMemberData')

-- *******************
-- drop all columns that should be skipped upon import
-- *******************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Dropping Columns to be Skipped'
select @sql = null
select @sql = COALESCE(@sql + char(10), '') + 'IF EXISTS (select * from tempdb.sys.columns where name = ''' + columnName + ''' and object_id = object_id(''tempdb..#importMemberData'')) ALTER TABLE #importMemberData DROP COLUMN [' + columnName + ']'
	from dbo.ams_memberDataColumns
	where orgID = @orgID
	and skipImport = 1
IF LEN(@sql) > 0 
	EXEC(@sql)

-- *******************
-- add back the columns we dont use from members (sigh) so the queries dont break
-- *******************
IF @hasPrefix = 0 AND NOT EXISTS (select [name] from tempdb.sys.columns where name = 'prefix' and object_id = object_id('tempdb..#importMemberData')) 
	ALTER TABLE #importMemberData ADD prefix varchar(4) NOT NULL DEFAULT('');
IF @hasMiddleName = 0 AND NOT EXISTS (select [name] from tempdb.sys.columns where name = 'middlename' and object_id = object_id('tempdb..#importMemberData')) 
	ALTER TABLE #importMemberData ADD middlename varchar(4) NOT NULL DEFAULT('');
IF @hasSuffix = 0 AND NOT EXISTS (select [name] from tempdb.sys.columns where name = 'suffix' and object_id = object_id('tempdb..#importMemberData')) 
	ALTER TABLE #importMemberData ADD suffix varchar(4) NOT NULL DEFAULT('');
IF @hasProfessionalSuffix = 0 AND NOT EXISTS (select [name] from tempdb.sys.columns where name = 'ProfessionalSuffix' and object_id = object_id('tempdb..#importMemberData')) 
	ALTER TABLE #importMemberData ADD ProfessionalSuffix varchar(4) NOT NULL DEFAULT('');
IF @hasCompany = 0 AND NOT EXISTS (select [name] from tempdb.sys.columns where name = 'Company' and object_id = object_id('tempdb..#importMemberData')) 
	ALTER TABLE #importMemberData ADD Company varchar(4) NOT NULL DEFAULT('');

-- ensure no null values in data. saves us from having to blank if null inline.
update #importMemberData set prefix = '' where prefix is null
update #importMemberData set middlename = '' where middlename is null
update #importMemberData set suffix = '' where suffix is null
update #importMemberData set professionalsuffix = '' where professionalsuffix is null
update #importMemberData set company = '' where company is null

-- Add index for queries below
EXEC('CREATE NONCLUSTERED INDEX [idx_importMemberData_' + @orgcode + '] ON #importMemberData ([membernumber] ASC) INCLUDE ( [prefix],[firstname],[middlename],[lastname],[suffix],[company],[ProfessionalSuffix]); ')
EXEC('CREATE STATISTICS [stat_importMemberData_' + @orgcode + '] ON #importMemberData([prefix]); ')

-- *******************
-- bcp out copy of current data 
-- have to do it this way since bcp cannot queryout dynamic sql queries
-- *******************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Exporting backup of existing data'
EXEC dbo.ams_getFlattenedMemberDataSQL @orgID=@orgID, @limitTop=0, @limitActive=0, @limitMID='', @showMCFields=1, @showSkippedFields=1, @sql=@sql OUTPUT
select @cmd2 = 'bcp "SET QUOTED_IDENTIFIER ON; ' + dbo.fn_ConsolidateWhiteSpace(@sql) + '" queryout ' + replace(@flatfile,'_flat_','_export_') + '.bcp -n -T -S' + CAST(serverproperty('servername') as varchar(40))
exec master..xp_cmdshell @cmd2, NO_OUTPUT

-- *******************
-- process data
-- *******************
EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Processing flattened data'
SELECT @stepNum = max(stepNum)
	from datatransfer.dbo.ams_memberDataImportStatus
	where orgID = @orgID

BEGIN TRAN

	-- Inactivate all non-guest members (the import contains only active people)
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Inactivate non-guest accounts',getdate())

	UPDATE dbo.ams_members
	SET [status] = 'I'
	WHERE orgID = @orgID
	AND membertypeID = 2
	AND memberID = activeMemberID
	AND [status] = 'A'
		IF @@ERROR <> 0 GOTO on_error

	-- update non-guest members already in table based on membernumber
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Update existing non-guest accounts',getdate())

	UPDATE m
	set m.prefix = flat.prefix,
		m.firstname = flat.firstname,
		m.middlename = flat.middlename,
		m.lastname = flat.lastname,
		m.suffix = flat.suffix,
		m.professionalsuffix = flat.professionalsuffix,
		m.company = flat.company,
		m.memberNumber = flat.membernumber,
		m.status = 'A'
	FROM dbo.ams_members as m 
	INNER JOIN #importMemberData as flat on flat.memberNumber = m.memberNumber
		AND m.orgID = @orgID
		AND m.membertypeID = 2
		AND m.memberID = m.activeMemberID
		AND m.status <> 'D'
		IF @@ERROR <> 0 GOTO on_error

	-- add new non-guest members
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Inserting new non-guest accounts',getdate())

	-- get first address type for the billing ATID
	declare @billingaddressTypeID int
	select @billingaddressTypeID = addressTypeID from dbo.ams_memberAddressTypes where orgID = @orgID and addressTypeOrder = 1

	INSERT INTO dbo.ams_members (orgID, prefix, firstname, middlename, lastname, suffix, professionalsuffix, company, memberNumber, [status], dateCreated, membertypeID, dateLastUpdated, billingAddressTypeID)
	SELECT @orgID, flat.prefix, flat.Firstname, flat.middlename, flat.LastName, flat.suffix, flat.professionalsuffix, flat.company, flat.membernumber, 'A', getdate(), 2, getdate(), @billingaddressTypeID
	from #importMemberData as flat
	WHERE NOT EXISTS(
		select memberNumber 
		from dbo.ams_members as m 
		where m.memberNumber = flat.memberNumber 
		and m.orgID = @orgID
		and m.memberid = m.activeMemberID 
		and m.membertypeID = 2
		AND m.status <> 'D'
	)
	ORDER BY flat.membernumber
		IF @@ERROR <> 0 GOTO on_error
	UPDATE dbo.ams_members 
	SET activeMemberID = memberID 
	WHERE orgid=@orgid 
	and activeMemberID is null 
		IF @@ERROR <> 0 GOTO on_error

	-- ************
	-- update #importMemberData with memberid for easier reference later
	-- ************
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating flat file with memberid',getdate())

	update flat
	set flat.memberID = m.memberID
	from #importMemberData as flat
	inner join dbo.ams_members as m on m.membernumber = flat.membernumber 
		and m.orgid = @orgid
		and m.memberid = m.activememberid
		and m.memberTypeID = 2
		AND m.status <> 'D'
	IF @@ERROR <> 0 GOTO on_error

	-- *******
	-- websites
	-- loop over website types
	-- update website if it exists for member
	-- add website if it doesnt already exist for member
	-- *******
	declare @minWTID int, @minWT varchar(20), @wtsql varchar(max)
	select @minWTID = min(websiteTypeID) from dbo.ams_memberWebsiteTypes where orgID = @orgID
	while @minWTID is not null BEGIN
		select @minWT = websiteType from dbo.ams_memberWebsiteTypes where websiteTypeID = @minWTID

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Updating existing member websites',getdate())

		select @wtsql = ''
		select @wtsql = @wtsql + 'UPDATE mw '
		select @wtsql = @wtsql + 'SET mw.website = isnull(flat.[' + @minWT + '],'''') '
		select @wtsql = @wtsql + 'FROM dbo.ams_memberWebsites as mw '
		select @wtsql = @wtsql + 'inner join dbo.ams_members as m on m.memberID = mw.memberID '
		select @wtsql = @wtsql + '	AND m.orgID = ' + cast(@orgid as varchar(10)) + ' '
		select @wtsql = @wtsql + '	AND m.memberID = m.activeMemberID '
		select @wtsql = @wtsql + '	AND m.membertypeID = 2 '
		select @wtsql = @wtsql + '	AND m.status <> ''D'' '
		select @wtsql = @wtsql + '  AND mw.websiteTypeID = ' + cast(@minWTID as varchar(10)) + ' '
		select @wtsql = @wtsql + 'INNER JOIN #importMemberData as flat on flat.memberNumber = m.memberNumber '
		EXEC(@wtsql)
		IF @@ERROR <> 0 GOTO on_error

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Inserting new member websites',getdate())

		select @wtsql = ''
		select @wtsql = @wtsql + 'INSERT INTO dbo.ams_memberWebsites (memberID, websiteTypeID, website) '
		select @wtsql = @wtsql + 'select m.memberid, ' + cast(@minWTID as varchar(10)) + ', isnull(flat.[' + @minWT + '],'''') '
		select @wtsql = @wtsql + 'from #importMemberData as flat '
		select @wtsql = @wtsql + 'inner join dbo.ams_members as m on m.membernumber = flat.membernumber '
		select @wtsql = @wtsql + '	and m.orgid = ' + cast(@orgid as varchar(10)) + ' '
		select @wtsql = @wtsql + '	and m.memberID = m.activeMemberID '
		select @wtsql = @wtsql + '	and m.membertypeID = 2 '
		select @wtsql = @wtsql + '	AND m.status <> ''D'' '
		select @wtsql = @wtsql + 'WHERE NOT EXISTS( '
		select @wtsql = @wtsql + '	select websiteID '
		select @wtsql = @wtsql + '	from dbo.ams_memberWebsites as mw '
		select @wtsql = @wtsql + '	where mw.websiteTypeID = ' + cast(@minWTID as varchar(10)) + ' '
		select @wtsql = @wtsql + '	and mw.memberID = m.memberID '
		select @wtsql = @wtsql + ') '
		EXEC(@wtsql)
		IF @@ERROR <> 0 GOTO on_error

		select @minWTID = min(websiteTypeID) from dbo.ams_memberWebsiteTypes where orgID = @orgID and websiteTypeID > @minWTID
	END

	-- *******
	-- emails
	-- loop over email types
	-- update email if it exists for member
	-- add email if it doesnt already exist for member
	-- *******
	declare @minETID int, @minET varchar(20), @etsql varchar(max)
	select @minETID = min(emailTypeID) from dbo.ams_memberEmailTypes where orgID = @orgID
	while @minETID is not null BEGIN
		select @minET = emailType from dbo.ams_memberEmailTypes where emailTypeID = @minETID

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Updating existing member emails',getdate())

		select @etsql = ''
		select @etsql = @etsql + 'UPDATE me '
		select @etsql = @etsql + 'SET me.email = isnull(flat.[' + @minET + '],'''') '
		select @etsql = @etsql + 'FROM dbo.ams_memberEmails as me '
		select @etsql = @etsql + 'inner join dbo.ams_members as m on m.memberID = me.memberID '
		select @etsql = @etsql + '	AND m.orgID = ' + cast(@orgid as varchar(10)) + ' '
		select @etsql = @etsql + '	AND m.memberID = m.activeMemberID '
		select @etsql = @etsql + '	AND m.membertypeID = 2 '
		select @etsql = @etsql + '	AND m.status <> ''D'' '
		select @etsql = @etsql + '  AND me.emailTypeID = ' + cast(@minETID as varchar(10)) + ' '
		select @etsql = @etsql + 'INNER JOIN #importMemberData as flat on flat.memberNumber = m.memberNumber '
		EXEC(@etsql)
		IF @@ERROR <> 0 GOTO on_error

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Inserting new member emails',getdate())

		select @etsql = ''
		select @etsql = @etsql + 'INSERT INTO dbo.ams_memberEmails (memberID, emailTypeID, email) '
		select @etsql = @etsql + 'select m.memberid, ' + cast(@minETID as varchar(10)) + ', isnull(flat.[' + @minET + '],'''') '
		select @etsql = @etsql + 'from #importMemberData as flat '
		select @etsql = @etsql + 'inner join dbo.ams_members as m on m.membernumber = flat.membernumber '
		select @etsql = @etsql + '	and m.orgid = ' + cast(@orgid as varchar(10)) + ' '
		select @etsql = @etsql + '	and m.memberID = m.activeMemberID '
		select @etsql = @etsql + '	and m.membertypeID = 2 '
		select @etsql = @etsql + '	AND m.status <> ''D'' '
		select @etsql = @etsql + 'WHERE NOT EXISTS( '
		select @etsql = @etsql + '	select emailID '
		select @etsql = @etsql + '	from dbo.ams_memberEmails as me '
		select @etsql = @etsql + '	where me.emailTypeID = ' + cast(@minETID as varchar(10)) + ' '
		select @etsql = @etsql + '	and me.memberID = m.memberID '
		select @etsql = @etsql + ') '
		EXEC(@etsql)
		IF @@ERROR <> 0 GOTO on_error

		select @minETID = min(emailTypeID) from dbo.ams_memberEmailTypes where orgID = @orgID and emailTypeID > @minETID
	END

	-- *******
	-- addresses and phones
	-- loop over address types (and then phone types)
	-- update address/phone if it exists for member
	-- add address/phone if it doesnt already exist for member
	-- *******
	declare @minATID int, @minAT varchar(20), @atsql varchar(max), @minPTID int, @minPT varchar(20)
	declare @hasAttn bit, @hasAddress2 bit, @hasAddress3 bit, @hasCounty bit
	declare @updateAddressDate varchar(25)
	select @minATID = min(addressTypeID) from dbo.ams_memberAddressTypes where orgID = @orgID
	while @minATID is not null BEGIN
		select @minAT=addressType, @hasAttn=hasAttn, @hasAddress2=hasAddress2, @hasAddress3=hasAddress3, @hasCounty=hasCounty
			from dbo.ams_memberAddressTypes 
			where addressTypeID = @minATID

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Updating existing member addresses',getdate())

		select @updateAddressDate = convert(varchar(30),getdate(),21)

		select @atsql = ''
		select @atsql = @atsql + 'UPDATE ma '
		select @atsql = @atsql + 'SET '
		IF @hasAttn = 1
			select @atsql = @atsql + '  ma.attn = isnull(flat.[' + @minAT + '_attn],''''), '
		select @atsql = @atsql + '	ma.address1 = isnull(flat.[' + @minAT + '_address1],''''), '
		IF @hasAddress2 = 1		
			select @atsql = @atsql + '	ma.address2 = isnull(flat.[' + @minAT + '_address2],''''), '
		IF @hasAddress3 = 1		
			select @atsql = @atsql + '	ma.address3 = isnull(flat.[' + @minAT + '_address3],''''), '
		select @atsql = @atsql + '	ma.city = isnull(flat.[' + @minAT + '_city],''''), '
		select @atsql = @atsql + '	ma.stateID = s.stateID, '
		select @atsql = @atsql + '	ma.postalCode = isnull(flat.[' + @minAT + '_postalcode],''''), '
		IF @hasCounty = 1		
			select @atsql = @atsql + '	ma.county = isnull(flat.[' + @minAT + '_county],''''), '
		select @atsql = @atsql + '	ma.countryID = c.countryID, '
		select @atsql = @atsql + '	ma.dateLastUpdated = ''' + @updateAddressDate + ''' '
		select @atsql = @atsql + 'FROM dbo.ams_memberAddresses as ma '
		select @atsql = @atsql + 'inner join dbo.ams_members as m on m.memberID = ma.memberID '
		select @atsql = @atsql + '	AND m.orgID = ' + cast(@orgid as varchar(10)) + ' '
		select @atsql = @atsql + '	AND m.memberID = m.activeMemberID '
		select @atsql = @atsql + '	AND m.membertypeID = 2 '
		select @atsql = @atsql + '	AND m.status <> ''D'' '
		select @atsql = @atsql + '  AND ma.addressTypeID = ' + cast(@minATID as varchar(10)) + ' '
		select @atsql = @atsql + 'INNER JOIN #importMemberData as flat on flat.memberNumber = m.memberNumber '
		select @atsql = @atsql + 'left outer join dbo.ams_countries as c on c.country = isnull(flat.[' + @minAT + '_country],'''') '
		select @atsql = @atsql + 'left outer join dbo.ams_states as s on s.code = isnull(flat.[' + @minAT + '_stateprov],'''') and s.countryID = c.countryID '
		select @atsql = @atsql + 'WHERE NOT EXISTS ( '
		select @atsql = @atsql + '		SELECT addressID '
		select @atsql = @atsql + '		FROM dbo.ams_memberAddresses '
		select @atsql = @atsql + '		where addressID = ma.addressID '
		IF @hasAttn = 1
			select @atsql = @atsql + '		AND IsNull(attn,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_attn],'''') '
		select @atsql = @atsql + '		AND IsNull(address1,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_address1],'''') '
		IF @hasAddress2 = 1		
			select @atsql = @atsql + '		AND IsNull(address2,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_address2],'''') '
		IF @hasAddress3 = 1		
			select @atsql = @atsql + '		AND IsNull(address3,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_address3],'''') '
		select @atsql = @atsql + '		AND IsNull(city,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_city],'''') '
		select @atsql = @atsql + '		AND IsNull(stateID,-1) = IsNull(s.stateID,-1) '
		select @atsql = @atsql + '		AND IsNull(postalCode,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_postalcode],'''') '
		IF @hasCounty = 1		
			select @atsql = @atsql + '		AND IsNull(county,'''') COLLATE Latin1_General_CS_AS = IsNull(flat.[' + @minAT + '_county],'''') '
		select @atsql = @atsql + '		AND IsNull(countryID,-1) = IsNull(c.countryID,-1) '
		select @atsql = @atsql + ') '
		EXEC(@atsql)
		IF @@ERROR <> 0 GOTO on_error
		
		-- clear address data for addresses we just updated
		delete from dbo.ams_memberAddressData
		where addressID in (
			select ma.addressID
			from dbo.ams_memberAddresses as ma
			inner join dbo.ams_members as m on m.memberID = ma.memberID
			where m.orgID = @orgID
			and ma.dateLastUpdated = @updateAddressDate
		)
		IF @@ERROR <> 0 GOTO on_error

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Inserting new member addresses',getdate())

		select @atsql = ''
		select @atsql = @atsql + 'INSERT INTO dbo.ams_memberAddresses (memberID, addressTypeID, attn, address1, address2, address3, city, stateID, postalCode, county, countryID) '
		select @atsql = @atsql + 'select m.memberid, ' + cast(@minATID as varchar(10)) + ', ' + case when @hasAttn = 1 then 'isnull(flat.[' + @minAT + '_attn],''''), ' else ''''', ' end + 'isnull(flat.[' + @minAT + '_address1],''''), ' + case when @hasAddress2 = 1 then 'isnull(flat.[' + @minAT + '_address2],''''), ' else ''''', ' end + case when @hasAddress3 = 1 then 'isnull(flat.[' + @minAT + '_address3],''''), ' else ''''', ' end + 'isnull(flat.[' + @minAT + '_city],''''), s.stateID, isnull(flat.[' + @minAT + '_postalcode],''''), ' + case when @hasCounty = 1 then 'isnull(flat.[' + @minAT + '_county],''''), ' else ''''', ' end + 'c.countryID '
		select @atsql = @atsql + 'from #importMemberData as flat '
		select @atsql = @atsql + 'inner join dbo.ams_members as m on m.membernumber = flat.membernumber '
		select @atsql = @atsql + '	and m.orgid = ' + cast(@orgid as varchar(10)) + ' '
		select @atsql = @atsql + '	and m.memberID = m.activeMemberID '
		select @atsql = @atsql + '	and m.membertypeID = 2 '
		select @atsql = @atsql + '	AND m.status <> ''D'' '
		select @atsql = @atsql + 'left outer join dbo.ams_countries as c on c.country = isnull(flat.[' + @minAT + '_country],'''') '
		select @atsql = @atsql + 'left outer join dbo.ams_states as s on s.code = isnull(flat.[' + @minAT + '_stateprov],'''') and s.countryID = c.countryID '
		select @atsql = @atsql + 'WHERE NOT EXISTS( '
		select @atsql = @atsql + '	select addressID '
		select @atsql = @atsql + '	from dbo.ams_memberAddresses as ma '
		select @atsql = @atsql + '	where ma.addressTypeID = ' + cast(@minATID as varchar(10)) + ' '
		select @atsql = @atsql + '	and ma.memberID = m.memberID '
		select @atsql = @atsql + ') '
		EXEC(@atsql)
		IF @@ERROR <> 0 GOTO on_error

		select @minPTID = min(phoneTypeID) from dbo.ams_memberPhoneTypes where orgID = @orgID
		while @minPTID is not null BEGIN
			select @minPT = phoneType from dbo.ams_memberPhoneTypes where phoneTypeID = @minPTID

			SELECT @stepNum = @stepNum + 1
			INSERT INTO @logTable (stepNum,stepMsg,stepDate)
			VALUES (@stepNum,'Updating existing member phones',getdate())

			select @atsql = ''
			select @atsql = @atsql + 'UPDATE mp '
			select @atsql = @atsql + 'SET phone = isnull(flat.[' + @minAT + '_' + @minPT + '],'''') '
			select @atsql = @atsql + 'FROM dbo.ams_memberPhones as mp '
			select @atsql = @atsql + 'inner join dbo.ams_memberAddresses as ma on ma.addressID = mp.addressid '
			select @atsql = @atsql + '	and ma.addressTypeID = ' + cast(@minATID as varchar(10)) + ' '
			select @atsql = @atsql + '  and mp.phoneTypeID = ' + cast(@minPTID as varchar(10)) + ' '
			select @atsql = @atsql + 'inner join dbo.ams_members as m on m.memberID = ma.memberID '
			select @atsql = @atsql + '	AND m.orgID = ' + cast(@orgid as varchar(10)) + ' '
			select @atsql = @atsql + '	AND m.memberID = m.activeMemberID '
			select @atsql = @atsql + '	AND m.membertypeID = 2 '
			select @atsql = @atsql + '	AND m.status <> ''D'' '
			select @atsql = @atsql + 'INNER JOIN #importMemberData as flat on flat.memberNumber = m.memberNumber '
			EXEC(@atsql)
			IF @@ERROR <> 0 GOTO on_error

			SELECT @stepNum = @stepNum + 1
			INSERT INTO @logTable (stepNum,stepMsg,stepDate)
			VALUES (@stepNum,'Inserting new member phones',getdate())

			select @atsql = ''
			select @atsql = @atsql + 'INSERT INTO dbo.ams_memberPhones (phoneTypeID, addressid, phone) '
			select @atsql = @atsql + 'select ' + cast(@minPTID as varchar(10)) + ', ma.addressid, isnull(flat.[' + @minAT + '_' + @minPT + '],'''') '
			select @atsql = @atsql + 'from #importMemberData as flat '
			select @atsql = @atsql + 'inner join dbo.ams_members as m on m.membernumber = flat.membernumber '
			select @atsql = @atsql + '	and m.orgid = ' + cast(@orgid as varchar(10)) + ' '
			select @atsql = @atsql + '	and m.memberID = m.activeMemberID '
			select @atsql = @atsql + '	and m.membertypeID = 2 '
			select @atsql = @atsql + '	AND m.status <> ''D'' '
			select @atsql = @atsql + 'inner join dbo.ams_memberAddresses as ma on ma.memberid = m.memberid '
			select @atsql = @atsql + '	and ma.addressTypeID = ' + cast(@minATID as varchar(10)) + ' '
			select @atsql = @atsql + 'WHERE NOT EXISTS( '
			select @atsql = @atsql + '	select phoneID '
			select @atsql = @atsql + '	from dbo.ams_memberPhones as mp '
			select @atsql = @atsql + '	where mp.phoneTypeID = ' + cast(@minPTID as varchar(10)) + ' '
			select @atsql = @atsql + '	and mp.addressID = ma.addressID '
			select @atsql = @atsql + ') '
			EXEC(@atsql)
			IF @@ERROR <> 0 GOTO on_error

			select @minPTID = min(phoneTypeID) from dbo.ams_memberPhoneTypes where orgID = @orgID and phoneTypeID > @minPTID
		END

		select @minATID = min(addressTypeID) from dbo.ams_memberAddressTypes where orgID = @orgID and addressTypeID > @minATID
	END

	-- *******
	-- professional licenses
	-- loop over prof license types
	-- update prof license if it exists for member
	-- add prof license if it doesnt already exist for member
	-- *******
	declare @minPLTID int, @minPLT varchar(200), @pltsql varchar(max)
	select @minPLTID = min(PLTypeID) from dbo.ams_memberProfessionalLicenseTypes where orgID = @orgID
	while @minPLTID is not null BEGIN
		select @minPLT=PLName
			from dbo.ams_memberProfessionalLicenseTypes 
			where PLTypeID = @minPLTID

		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Updating existing member professional licenses',getdate())

		select @pltsql = ''
		select @pltsql = @pltsql + 'UPDATE mpl '
		select @pltsql = @pltsql + 'SET '
		select @pltsql = @pltsql + '	mpl.licenseNumber = isnull(flat.[' + @minPLT + '_licenseNumber],''''), '
		select @pltsql = @pltsql + '	mpl.activeDate = nullif(flat.[' + @minPLT + '_activeDate],''''), '
		select @pltsql = @pltsql + '	mpl.PLstatusID = pls.PLStatusID '
		select @pltsql = @pltsql + 'FROM dbo.ams_memberProfessionalLicenses as mpl '
		select @pltsql = @pltsql + 'inner join dbo.ams_members as m on m.memberID = mpl.memberID '
		select @pltsql = @pltsql + '	AND m.orgID = ' + cast(@orgid as varchar(10)) + ' '
		select @pltsql = @pltsql + '	AND m.memberID = m.activeMemberID '
		select @pltsql = @pltsql + '	AND m.membertypeID = 2 '
		select @pltsql = @pltsql + '	AND m.status <> ''D'' '
		select @pltsql = @pltsql + '  AND mpl.PLTypeID = ' + cast(@minPLTID as varchar(10)) + ' '
		select @pltsql = @pltsql + 'INNER JOIN #importMemberData as flat on flat.memberNumber = m.memberNumber '
		select @pltsql = @pltsql + 'LEFT OUTER JOIN dbo.ams_memberProfessionalLicenseStatuses as pls on pls.statusName = isnull(flat.[' + @minPLT + '_status],'''') and pls.orgID = ' + cast(@orgid as varchar(10))
		EXEC(@pltsql)
		IF @@ERROR <> 0 GOTO on_error
		
		SELECT @stepNum = @stepNum + 1
		INSERT INTO @logTable (stepNum,stepMsg,stepDate)
		VALUES (@stepNum,'Inserting new member professional licenses',getdate())

		select @pltsql = ''
		select @pltsql = @pltsql + 'INSERT INTO dbo.ams_memberProfessionalLicenses (memberID, PLTypeID, LicenseNumber, ActiveDate, PLstatusID) '
		select @pltsql = @pltsql + 'select m.memberid, ' + cast(@minPLTID as varchar(10)) + ', isnull(flat.[' + @minPLT + '_licenseNumber],''''), nullif(flat.[' + @minPLT + '_activeDate],''''), pls.PLStatusID '
		select @pltsql = @pltsql + 'from #importMemberData as flat '
		select @pltsql = @pltsql + 'LEFT OUTER JOIN dbo.ams_memberProfessionalLicenseStatuses as pls on pls.statusName = isnull(flat.[' + @minPLT + '_status],'''') and pls.orgID = ' + cast(@orgid as varchar(10))
		select @pltsql = @pltsql + 'inner join dbo.ams_members as m on m.membernumber = flat.membernumber '
		select @pltsql = @pltsql + '	and m.orgid = ' + cast(@orgid as varchar(10)) + ' '
		select @pltsql = @pltsql + '	and m.memberID = m.activeMemberID '
		select @pltsql = @pltsql + '	and m.membertypeID = 2 '
		select @pltsql = @pltsql + '	AND m.status <> ''D'' '
		select @pltsql = @pltsql + 'WHERE NOT EXISTS( '
		select @pltsql = @pltsql + '	select PLID '
		select @pltsql = @pltsql + '	from dbo.ams_memberProfessionalLicenses as mpl '
		select @pltsql = @pltsql + '	where mpl.PLTypeID = ' + cast(@minPLTID as varchar(10)) + ' '
		select @pltsql = @pltsql + '	and mpl.memberID = m.memberID '
		select @pltsql = @pltsql + ')  '
		select @pltsql = @pltsql + 'AND ( '
		select @pltsql = @pltsql + '	nullIf(flat.[' + @minPLT + '_licenseNumber],'''') is not null OR '
		select @pltsql = @pltsql + '	nullif(flat.[' + @minPLT + '_activeDate],'''') is not null OR '
		select @pltsql = @pltsql + '	nullIf(flat.[' + @minPLT + '_status],'''') is not null '
		select @pltsql = @pltsql + ')  '

		EXEC(@pltsql)
		IF @@ERROR <> 0 GOTO on_error

		select @minPLTID = min(PLTypeID) from dbo.ams_memberProfessionalLicenseTypes where orgID = @orgID and PLTypeID > @minPLTID
	END

	-- delete licenses where everything is now null (could have been updated to null values above)
	delete
	from dbo.ams_memberProfessionalLicenses
	where nullif(licenseNumber,'') is null
	and activedate is null
	and PlStatusID is null
		IF @@ERROR <> 0 GOTO on_error
	
	-- ***********
	-- memberdata
	-- figure out what should be in memberdata
	-- delete what shouldnt be but is
	-- add what should be but isnt
	-- ***********
	
	-- clear memberdataholding for org
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Cleaning up holding table',getdate())

	DELETE FROM datatransfer.dbo.ams_memberDataHolding where orgID = @orgID
		IF @@ERROR <> 0 GOTO on_error

	-- traverse each column and put into holding table
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Inserting custom data into holding table',getdate())

	DECLARE @minCol varchar(255), @minColID int, @coldataTypeCode varchar(20), @colDisplayTypeCode varchar(20), @allowMultiple bit, @colqry varchar(max)
	SELECT @minColID = MIN(mdc.columnID) 
		FROM dbo.ams_memberDataColumns as mdc
		where mdc.orgID = @orgid
		and mdc.skipImport = 0
	WHILE @minColID is not null BEGIN
		select @minCol=mdc.columnName, @coldataTypeCode=dt.dataTypeCode, @allowMultiple=mdc.allowMultiple
			from dbo.ams_memberDataColumns as mdc
			inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			where mdc.columnID = @minColID
			IF @@ERROR <> 0 GOTO on_error

		IF EXISTS (select * from tempdb.sys.columns where name = @minCol and object_id = object_id('tempdb..#importMemberData')) BEGIN
			SELECT @colqry = 'insert into datatransfer.dbo.ams_memberDataHolding (orgID, rowID, columnID, ' + 
				case @coldataTypeCode 
				when 'STRING' then 'columnValueString'
				when 'DECIMAL2' then 'columnValueDecimal2'
				when 'INTEGER' then 'columnValueInteger'
				when 'DATE' then 'columnValueDate'
				when 'BIT' then 'columnValueBit'
				when 'XML' then 'columnValueXML'
				when 'CONTENTOBJ' then 'columnValueContent'
				when 'DOCUMENTOBJ' then 'columnValueSiteResourceID'
				else 'columnValueString'
				end + ') '
			SELECT @colqry = @colqry + 'select ' + cast(@orgID as varchar(6)) + ', rowID, ' + cast(@minColID as varchar(12)) + ', ' + 
				case when @allowMultiple = 1 then 'tbl.listitem'
				else 
					case @coldataTypeCode 
					when 'STRING' then quotename(@minCol)
					when 'DECIMAL2' then 'cast(' + quotename(@minCol) + ' as decimal(9,2))'
					when 'INTEGER' then 'cast(' + quotename(@minCol) + ' as int)'
					when 'DATE' then 'cast(' + quotename(@minCol) + ' as datetime)'
					when 'BIT' then 'cast(' + quotename(@minCol) + ' as bit)'
					when 'XML' then 'cast(' + quotename(@minCol) + ' as xml)'
					when 'CONTENTOBJ' then quotename(@minCol)
					when 'DOCUMENTOBJ' then 'cast(' + quotename(@minCol) + ' as int)'
					else quotename(@minCol)
					end
				end + ' '
			SELECT @colqry = @colqry + 'from #importMemberData '
			IF @allowMultiple = 1 BEGIN
				SELECT @colqry = @colqry + 'cross apply ' + 
					case @coldataTypeCode 
					when 'STRING' then 'dbo.fn_varcharListToTable(' + quotename(@minCol) + ',''|'') as tbl '
					when 'DECIMAL2' then 'dbo.fn_decimal2ListToTable(' + quotename(@minCol) + ',''|'') as tbl '
					when 'INTEGER' then 'dbo.fn_intListToTable(' + quotename(@minCol) + ',''|'') as tbl '
					end + ' '
			END
			SELECT @colqry = @colqry + 'where ' + 
				case @coldataTypeCode 
				when 'STRING' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(255)),'''') is not null'
				when 'DECIMAL2' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(20)),'''') is not null'
				when 'INTEGER' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(20)),'''') is not null'
				when 'DATE' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(10)),'''') is not null'
				when 'BIT' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(1)),'''') is not null'
				when 'XML' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(max)),'''') is not null'
				when 'CONTENTOBJ' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(max)),'''') is not null'
				when 'DOCUMENTOBJ' then 'nullif(cast(' + quotename(@minCol) + ' as varchar(20)),'''') is not null'
				else 'nullif(cast(' + quotename(@minCol) + ' as varchar(255)),'''') is not null'
				end
			EXEC(@colqry)
			IF @@ERROR <> 0 GOTO on_error
		END
				
		SELECT @minColID = MIN(mdc.columnID) 
			FROM dbo.ams_memberDataColumns as mdc
			where mdc.orgID = @orgid
			and mdc.skipImport = 0
			and columnID > @minColID
	END

	-- handle the content object fields in the holding table
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Handling content object custom data in holding table',getdate())

	-- get first site created for the org. We will put content objects on this site.
	declare @siteID int, @isHTML bit, @dataID bigint, @rc int, @contentID int, @contentSiteResourceID int, @rawContent varchar(max)
	select TOP 1 @siteID=siteID from dbo.sites where orgID = @orgID order by siteID
		IF @@ERROR <> 0 GOTO on_error

	SELECT @minColID = null
	SELECT @minColID = MIN(mdc.columnID) 
		FROM dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgid
		and mdc.skipImport = 0
		and dt.dataTypeCode = 'CONTENTOBJ'
		and EXISTS (select * from tempdb.sys.columns where name = mdc.columnName and object_id = object_id('tempdb..#importMemberData'))
	WHILE @minColID is not null BEGIN
		select @colDisplayTypeCode=dt.displayTypeCode
			from dbo.ams_memberDataColumns as mdc
			inner join dbo.ams_memberDataColumnDisplayTypes as dt on dt.displayTypeID = mdc.displayTypeID
			where mdc.columnID = @minColID
			IF @@ERROR <> 0 GOTO on_error

		-- lookup any existing SiteResourceID
		update mdh
		set mdh.columnValueSiteResourceID = mdcv.columnValueSiteResourceID
		from #importMemberData as imp
		inner join datatransfer.dbo.ams_memberDataHolding as mdh on mdh.rowID = imp.rowID
			and mdh.orgID = @orgID
		inner join membercentral.dbo.ams_memberData as md WITH(NOLOCK) on md.memberid = imp.memberid
		inner join membercentral.dbo.ams_memberDataColumnValues as mdcv WITH(NOLOCK) on mdcv.valueID = md.valueID
			and mdcv.columnID = mdh.columnID
		where mdh.columnValueContent is not null
		and mdcv.columnValueSiteResourceID is not null
		and mdcv.columnID = @minColID
			IF @@ERROR <> 0 GOTO on_error

		IF @colDisplayTypeCode = 'HTMLCONTENT'
			SET @isHTML = 1
		ELSE
			SET @isHTML = 0

		-- update content objects for those that already have one
		select @dataID = null
		select @dataID = min(dataid)
			from datatransfer.dbo.ams_memberDataHolding
			where orgID = @orgID
			and columnValueSiteResourceID is not null
		while @dataID is not null BEGIN
			select @contentSiteResourceID = null, @rawContent = null, @contentID = null
			select @contentSiteResourceID = columnValueSiteResourceID, @rawContent = columnValueContent
				from datatransfer.dbo.ams_memberDataHolding
				where dataID = @dataID
				IF @@ERROR <> 0 GOTO on_error
			select @contentID = contentID
				from dbo.cms_content
				where siteResourceID = @contentSiteResourceID
				and siteID = @siteID

			if @contentID is not null BEGIN
				EXEC @rc = dbo.cms_updateContent @contentID=@contentID, @languageID=1, @isSSL=0, 
					@isHTML=@isHTML, @contentTitle='', @contentDesc='', @rawContent=@rawContent, @memberID=NULL
					IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
			end

			select @dataID = min(dataid)
				from datatransfer.dbo.ams_memberDataHolding
				where orgID = @orgID
				and columnValueSiteResourceID is not null
				and dataid > @dataID
		end

		-- create content objects for those that need it
		select @dataID = null
		select @dataID = min(dataid)
			from datatransfer.dbo.ams_memberDataHolding
			where orgID = @orgID
			and columnValueContent is not null
			and columnValueSiteResourceID is null
		while @dataID is not null BEGIN
			select @rawContent = null, @contentID = null, @contentSiteResourceID = null
			select @rawContent = columnValueContent
				from datatransfer.dbo.ams_memberDataHolding
				where dataID = @dataID
				IF @@ERROR <> 0 GOTO on_error

			EXEC @rc = dbo.cms_createContentField @siteID=@siteID, @isSSL=0, @isHTML=@isHTML, @isActive=1, 
				@languageID=1, @contentTitle='', @contentDesc='', @rawContent=@rawContent, 
				@contentID=@contentID OUTPUT, @contentSiteResourceID=@contentSiteResourceID OUTPUT
				IF @@ERROR <> 0 OR @RC <> 0 OR @contentID = 0 GOTO on_error

			UPDATE datatransfer.dbo.ams_memberDataHolding
			SET columnValueSiteResourceID = @contentSiteResourceID
			WHERE dataID = @dataID
				IF @@ERROR <> 0 GOTO on_error

			select @dataID = min(dataid)
				from datatransfer.dbo.ams_memberDataHolding
				where orgID = @orgID
				and columnValueContent is not null
				and columnValueSiteResourceID is null
				and dataID > @dataID
		end

		SELECT @minColID = MIN(mdc.columnID) 
			FROM dbo.ams_memberDataColumns as mdc
			inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
			where mdc.orgID = @orgid
			and mdc.skipImport = 0
			and dt.dataTypeCode = 'CONTENTOBJ'
			and EXISTS (select * from tempdb.sys.columns where name = mdc.columnName and object_id = object_id('tempdb..#importMemberData'))
			and mdc.columnID > @minColID
	END

	-- remove empty values in holding table
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Removing empty values in holding table',getdate())

	DELETE FROM datatransfer.dbo.ams_memberDataHolding
	WHERE orgID = @orgID
	AND (columnValueString = '' or columnValueString is null)
	AND columnValueDecimal2 is null
	AND columnValueInteger is null
	AND columnValueDate is null
	AND columnValueBit is null
	AND columnValueXML is null
	AND columnValueSiteResourceID is null
	AND (columnValueContent = '' or columnValueContent is null)
		IF @@ERROR <> 0 GOTO on_error

	-- add new columnValues	
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Inserting new member data column values',getdate())

	INSERT INTO dbo.ams_memberDataColumnValues (columnID, columnValueString, columnValueDecimal2, columnValueInteger, columnValueDate, columnValueBit, columnValueXML, columnValueSiteResourceID)
	select distinct columnID, columnValueString, columnValueDecimal2, columnValueInteger, columnValueDate, columnValueBit, cast(columnValueXML as varchar(max)), columnValueSiteResourceID
	from datatransfer.dbo.ams_memberDataHolding
	where orgID = @orgid
		except
	select mdcv.columnID, mdcv.columnValueString, mdcv.columnValueDecimal2, mdcv.columnValueInteger, mdcv.columnValueDate, mdcv.columnValueBit, cast(mdcv.columnValueXML as varchar(max)), mdcv.columnValueSiteResourceID
	from dbo.ams_memberDataColumnValues as mdcv
	inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
		and mdc.orgID = @orgID
		IF @@ERROR <> 0 GOTO on_error

	-- update holding with valueIDs
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for string',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode = 'STRING'
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and mdcv.columnValueString = mdh.columnValueString
			IF @@ERROR <> 0 GOTO on_error

	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for decimal',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode = 'DECIMAL2'
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and mdcv.columnValueDecimal2 = mdh.columnValueDecimal2
			IF @@ERROR <> 0 GOTO on_error

	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for integer',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode = 'INTEGER'
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and mdcv.columnValueInteger = mdh.columnValueInteger
			IF @@ERROR <> 0 GOTO on_error

	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for date',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode = 'DATE'
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and mdcv.columnValueDate = mdh.columnValueDate
			IF @@ERROR <> 0 GOTO on_error

	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for bit',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode = 'BIT'
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and mdcv.columnValueBit = mdh.columnValueBit
			IF @@ERROR <> 0 GOTO on_error

	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for xml',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode = 'XML'
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and cast(mdcv.columnValueXML as varchar(max)) = cast(mdh.columnValueXML as varchar(max))
			IF @@ERROR <> 0 GOTO on_error

	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Updating holding table with valueIDs for SRID',getdate())

		UPDATE mdh
		set mdh.valueID = mdcv.valueID
		from datatransfer.dbo.ams_memberDataHolding as mdh
		inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdh.columnID
		inner join dbo.ams_memberDataColumnDataTypes as mdcdt on mdcdt.dataTypeID = mdc.dataTypeID
			and mdcdt.dataTypeCode IN ('CONTENTOBJ','DOCUMENTOBJ')
		inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID 
		where mdh.orgID = @orgid
		and mdcv.columnValueSiteResourceID = mdh.columnValueSiteResourceID
			IF @@ERROR <> 0 GOTO on_error

	-- figure out what should be in memberdata
	-- include imported data and current data for columns that were skipped
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Figure out what should be in memberdata',getdate())

	CREATE TABLE #shouldBeMemberData (dataid int IDENTITY(1,1), memberid int, valueid int)
	INSERT INTO #shouldBeMemberData (memberid, valueID)
	SELECT distinct m.memberid, mdcv.valueid
	from #importMemberData as M
	inner join datatransfer.dbo.ams_memberDataHolding as mdh on mdh.rowid = M.rowid 
		and mdh.orgID = @orgID
	inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdh.columnID 
		and mdcv.valueID = mdh.valueID
		union
	select distinct md.memberid, md.valueid
	from dbo.ams_memberData as md
	inner join dbo.ams_members as m on m.memberid = md.memberID
		and m.orgID = @orgID
		and m.memberid = m.activeMemberID
	inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
	inner join dbo.ams_memberDataColumns as mdc on mdc.columnID = mdcv.columnID
		and mdc.skipImport = 1
		IF @@ERROR <> 0 GOTO on_error

	-- delete what shouldnt be but is
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Removing old custom data for members',getdate())

	DELETE FROM dbo.ams_memberData
	where dataid in (
		select md.dataid
		from dbo.ams_memberData as md
		inner join #importMemberData as flat on md.memberid = flat.memberid
		WHERE NOT EXISTS(
			select dataid 
			from #shouldBeMemberData as sb
			where sb.memberid = md.memberid
			and sb.valueid = md.valueid
		)
	)
		IF @@ERROR <> 0 GOTO on_error
	
	-- add what should be but isnt
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Inserting new custom data for members',getdate())

	INSERT INTO dbo.ams_memberData (memberID, valueID)
	select sb.memberid, sb.valueid
	from #shouldBeMemberData as sb
	WHERE NOT EXISTS(
		select dataid 
		from dbo.ams_memberData as md
		where md.memberid = sb.memberid
		and md.valueid = sb.valueid
	)
		IF @@ERROR <> 0 GOTO on_error

	-- clear #shouldBeMemberData
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Cleaning up #shouldBeMemberData',getdate())

	IF OBJECT_ID('tempdb..#shouldBeMemberData') IS NOT NULL BEGIN
		DROP TABLE #shouldBeMemberData
		IF @@ERROR <> 0 GOTO on_error
	END

	-- clear memberdataholding
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Cleaning up holding table',getdate())

	DELETE FROM datatransfer.dbo.ams_memberDataHolding where orgID = @orgID
		IF @@ERROR <> 0 GOTO on_error

	SELECT @returnVal = 0

IF @@TRANCOUNT > 0 COMMIT TRAN
GOTO on_cleanup

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @returnVal = -2
	GOTO on_cleanup

on_cleanup:
	SELECT @stepNum = @stepNum + 1
	INSERT INTO @logTable (stepNum,stepMsg,stepDate)
	VALUES (@stepNum,'Inserting entries into log table',getdate())

	INSERT INTO dataTransfer.dbo.ams_memberDataImportStatus (orgID,stepNum,stepMsg,stepDate)
	select @orgID, stepNum, stepMsg, stepDate
	from @logTable
	order by stepNum

	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Cleaning up #importMemberData'
	IF OBJECT_ID('tempdb..#importMemberData') IS NOT NULL
		DROP TABLE #importMemberData

	-- populate member group cache (@runSchedule=1 indicates immediate processing)
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Repopulating Member Group Cache'
	declare @itemGroupUID uniqueidentifier
	EXEC platformQueue.dbo.queue_processMemberGroups_insert @orgID=@orgID, @memberIDList='', @conditionIDList='', @runSchedule=1, @itemGroupUID=@itemGroupUID OUTPUT
	EXEC dbo.ams_importMemberDataStatusLog @orgID=@orgID, @stepMsg='Import complete'

	RETURN @returnVal
GO

print '%%%%%%%%%% ALTER email_copyEmailBlast SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROC [dbo].[email_copyEmailBlast]
@blastID int,
@memberID int,
@newBlastID int OUTPUT

AS

declare @rc int, @siteID int, @categoryID int, @subCategoryID int, @newRuleID int,
	@newContentID int, @blastName varchar(200), @oldRuleXML xml, @oldRuleSQL varchar(max), 
	@oldFromName varchar(200), @oldFromEmail varchar(200), @oldReplyTo varchar(200), 
	@oldContentTitle varchar(200), @oldRawContent varchar(max)

select @siteID=sr.siteID, @categoryID=sr.categoryID, @subCategoryID=sr.subCategoryID, 
	@blastName='Copy of ' + left(sr.blastName,192), @oldFromName=sr.fromName, 
	@oldFromEmail=sr.fromEmail, @oldReplyTo=sr.replyTo, @oldContentTitle=cl.contentTitle, 
	@oldRuleXML=vgr.ruleXML, @oldRuleSQL=vgr.ruleSQL, @oldRawContent=cv.rawcontent
from dbo.email_emailBlasts as sr
inner join dbo.ams_virtualGroupRules as vgr on vgr.ruleID = sr.ruleID
inner join dbo.cms_content as c on c.contentID = sr.contentID
inner join dbo.cms_contentLanguages as cl on cl.contentID = c.contentID
inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID
where sr.blastID = @blastID
and cv.isActive = 1

BEGIN TRAN
	EXEC @rc = dbo.email_createEmailBlast @siteID=@siteID, @memberid=@memberID, @blastName=@blastName, @categoryID=@categoryID, @subCategoryID=@subCategoryID, @blastID=@NewBlastID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 or @newBlastID = 0 GOTO on_error

	UPDATE dbo.email_emailBlasts
	SET fromName = @oldFromName,
		fromEmail = @oldFromEmail,
		replyTo = @oldReplyTo
	WHERE blastID = @newBlastID
		IF @@ERROR <> 0 GOTO on_error

	SELECT @newRuleID=ruleID, @newContentID=contentID
	FROM dbo.email_emailBlasts
	WHERE blastID = @newBlastID
		IF @@ERROR <> 0 GOTO on_error

	UPDATE dbo.ams_virtualGroupRules
	set ruleXML = @oldRuleXML,
		ruleSQL = @oldRuleSQL
	where ruleID = @newRuleID
		IF @@ERROR <> 0 GOTO on_error

	EXEC @rc = dbo.cms_updateContent @contentID=@newContentID, @languageID=1, @isSSL=0, @isHTML=1, @contentTitle=@oldContentTitle, @contentDesc='', @rawcontent=@oldRawContent, @memberID=@memberID
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @newBlastID = 0
	RETURN -1
GO

print '%%%%%%%%%% ALTER ev_copyEvent SP %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%'
GO

ALTER PROC [dbo].[ev_copyEvent]
@eventid int,
@copiedByMemberID int,
@newEventID int OUTPUT

AS

DECLARE @rc int
DECLARE @siteID int, @eventTypeID int, @lockTimeZoneID int, @isAllDayEvent bit,
	@altRegistrationURL varchar(300), @GLAccountID int, @calendarID int, @status char(1), @reportCode varchar(15),
	@internalNotes varchar(max)
DECLARE @eventContentID int, @contactContentID int, @locationContentID int, @cancelContentID int, @travelContentID int, @informationContentID int
DECLARE @neweventContentID int, @newcontactContentID int, @newlocationContentID int, @newcancelContentID int, @newtravelContentID int, @newinformationContentID int
DECLARE @languageID int, @isSSL bit, @isHTML bit, @contentTitle varchar(200), @contentDesc varchar(400), @rawcontent varchar(max)
DECLARE @emailContactContent bit, @emailLocationContent bit, @emailCancelContent bit, @emailTravelContent	bit
DECLARE @registrationID int, @registrationTypeID int, @startDate datetime, @endDate datetime, @registrantCap int,
	@ReplyToEmail varchar(200), @notifyEmail varchar(200), @isPriceBasedOnActual bit, @bulkCountByRate bit, @newregistrationID int
DECLARE @expirationContentID int, @registrantCapContentID int
DECLARE @newexpirationContentID int, @newregistrantCapContentID int
DECLARE @minofferingID int, @newofferingID int
DECLARE @minRateId int, @rateGroupingID int, @rateGLAccountID int, @rateName varchar(100),
	@rate money, @ratestartDate datetime, @rateendDate datetime, @newRateID int, @newRatesiteResourceID int,
	@ratereportCode varchar(15), @rateQty int
DECLARE @minBulkRateID int, @bulkrate money, @bulksiteResourceID int, @bulkrateqty int, @newBulkRateID int,
	@newBulkRatesiteResourceID int, @bulkresourceRightID int
DECLARE @siteResourceID int, @newrateGroupingID int
DECLARE @srr_rightsID int, @srr_roleid int, @srr_functionID int, @srr_groupid int, @srr_memberid int, @srr_include bit, @srr_inheritedRightsResourceID int, @srr_inheritedRightsFunctionID int, @resourceRightID int
DECLARE @minCustomID int, @newCustomID int
SELECT @newEventID = null

BEGIN TRAN

-- get the event we are copying
SELECT @siteID=siteID, @eventTypeID=eventTypeID, 
	@lockTimeZoneID=lockTimeZoneID, @isAllDayEvent=isAllDayEvent, @altRegistrationURL=altRegistrationURL,
	@GLAccountID=GLAccountID, @status=status, @reportCode=reportcode, @internalNotes=internalNotes,
	@emailContactContent = emailContactContent, @emailLocationContent = emailLocationContent,
	@emailCancelContent = emailCancelContent, @emailTravelContent = emailTravelContent
	FROM dbo.ev_events
	WHERE eventID = @eventID
	IF @@ERROR <> 0 GOTO on_error
SELECT TOP 1 @calendarID = calendarID
	FROM dbo.ev_calendarEvents
	WHERE calendarID = sourceCalendarID
	AND sourceEventID = @eventID
	IF @@ERROR <> 0 GOTO on_error

-- create event
EXEC @rc = dbo.ev_createEvent @siteID=@siteID, @calendarID=@calendarID, @eventTypeID=@eventTypeID, 
	@enteredByMemberID=@copiedByMemberID, @lockTimeZoneID=@lockTimeZoneID, @isAllDayEvent=@isAllDayEvent, 
	@altRegistrationURL=@altRegistrationURL, @status=@status, @reportCode=@reportCode, 
	@emailContactContent=@emailContactContent, @emailLocationContent=@emailLocationContent,
	@emailCancelContent=@emailCancelContent, @emailTravelContent=@emailTravelContent,
	@eventID=@newEventID OUTPUT
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

update dbo.ev_events
set GLAccountID = @GLAccountID,
	internalNotes = @internalNotes
where eventID = @newEventID
	IF @@ERROR <> 0 GOTO on_error

select @neweventContentID=eventContentID, @newcontactContentID=contactContentID, @newlocationContentID=locationContentID,
	@newcancelContentID=cancellationPolicyContentID, @newtravelContentID=travelContentID, @newinformationContentID=informationContentID
FROM dbo.ev_events
where eventID = @newEventID
	IF @@ERROR <> 0 GOTO on_error

select @eventContentID=eventContentID, @contactContentID=contactContentID, @locationContentID=locationContentID,
	@cancelContentID=cancellationPolicyContentID, @travelContentID=travelContentID, @informationContentID=informationContentID
FROM dbo.ev_events
where eventID = @EventID
	IF @@ERROR <> 0 GOTO on_error

-- make event inactive
UPDATE dbo.ev_events
SET [status] = 'I'
WHERE eventID = @newEventID
	IF @@ERROR <> 0 GOTO on_error

-- copy event category
INSERT INTO dbo.ev_eventcategories (eventID, categoryID)
select @newEventID, categoryID
from dbo.ev_eventcategories
where eventID = @eventID
	IF @@ERROR <> 0 GOTO on_error

-- copy event times
INSERT INTO dbo.ev_Times (eventid, timeZoneID, startTime, endTime)
select @newEventID, timeZoneID, startTime, endTime
from dbo.ev_times
where eventid = @eventID
	IF @@ERROR <> 0 GOTO on_error

-- copy content objects
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle='Copy of ' + contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@eventContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@neweventContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@contactContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newcontactContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@locationContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newlocationContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@cancelContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newcancelContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@travelContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newtravelContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@informationContentID,1)
EXEC @rc = dbo.cms_updateContent @contentID=@newinformationContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
	IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

-- copy sponsors
insert into dbo.ev_sponsors (eventID, sponsorContentID, sponsorOrder)
select @newEventID, sponsorContentID, sponsorOrder
from dbo.ev_sponsors
where eventID = @eventID
	IF @@ERROR <> 0 GOTO on_error

-- does orig event have registration?
SELECT @registrationID=registrationid, @registrationTypeID=registrationTypeID, @startDate=startdate, 
	@endDate=endDate, @registrantCap=registrantCap, @ReplyToEmail=ReplyToEmail, @notifyEmail=notifyEmail, 
	@isPriceBasedOnActual=isPriceBasedOnActual, @bulkCountByRate=bulkCountByRate, @expirationContentID=expirationContentID, 
	@registrantCapContentID=registrantCapContentID
	from dbo.ev_registration
	where eventID = @eventID
	and [status] <> 'D'
IF @registrationID is not null BEGIN

	-- insert registration
	EXEC @rc = dbo.ev_createRegistration @eventID=@newEventID, @registrationTypeID=@registrationTypeID, 
			@startDate=@startDate, @endDate=@endDate, @registrantCap=@registrantCap, @ReplyToEmail=@ReplyToEmail,
			@notifyEmail=@notifyEmail, @isPriceBasedOnActual=@isPriceBasedOnActual, @bulkCountByRate=@bulkCountByRate,
			@registrationID=@newregistrationID OUTPUT
		IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

	select @newexpirationContentID=expirationContentID, @newregistrantCapContentID=registrantCapContentID
	FROM dbo.ev_registration
	where registrationID = @newregistrationID
		IF @@ERROR <> 0 GOTO on_error

	-- copy content objects
	select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@expirationContentID,1)
	EXEC @rc = dbo.cms_updateContent @contentID=@newexpirationContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
		IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error
	select @languageID=languageID, @isSSL=isSSL, @isHTML=isHTML, @contentTitle=contentTitle, @contentDesc=contentDesc, @rawcontent=rawContent from dbo.fn_getContent(@registrantCapContentID,1)
	EXEC @rc = dbo.cms_updateContent @contentID=@newregistrantCapContentID, @languageID=@languageID, @isSSL=@isSSL, @isHTML=@isHTML, @contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@copiedByMemberID
		IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

	-- if reg type
	IF @registrationTypeID = 1 BEGIN

		-- merchant profiles
		insert into dbo.ev_registrationMerchantProfiles (registrationID, profileID)
		select @newregistrationID, rmp.profileID
		from dbo.ev_registrationMerchantProfiles as rmp
		inner join dbo.mp_profiles as mp on mp.profileID = rmp.profileID
		inner join dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
		where rmp.registrationID = @registrationID
		and mp.status = 'A'
		and mp.allowPayments = 1
		and g.isActive = 1
			IF @@ERROR <> 0 GOTO on_error

		-- credit offered
		select @minofferingID = min(offeringID) from dbo.crd_offerings where eventID = @eventID
		while @minofferingID is not null BEGIN
			INSERT INTO dbo.crd_offerings (ASID, statusID, ApprovalNum, offeredStartDate, offeredEndDate, completeByDate, isCreditRequired, isIDRequired, isCreditDefaulted, showCredit, eventID)
			SELECT ASID, statusID, ApprovalNum, offeredStartDate, offeredEndDate, completeByDate, isCreditRequired, isIDRequired, isCreditDefaulted, showCredit, @newEventID
			FROM dbo.crd_offerings
			WHERE offeringID = @minofferingID
				IF @@ERROR <> 0 GOTO on_error
				SELECT @newofferingID = SCOPE_IDENTITY()

			INSERT INTO dbo.crd_offeringTypes (offeringID, ASTID, creditValue)
			SELECT @newofferingID, ASTID, creditValue
			FROM dbo.crd_offeringTypes
			WHERE offeringID = @minofferingID
				IF @@ERROR <> 0 GOTO on_error

			select @minofferingID = min(offeringID) from dbo.crd_offerings where eventID = @eventID and offeringID > @minofferingID
		END

		-- rate groupings
		insert into dbo.ev_rateGrouping (rateGrouping, registrationID, rateGroupingOrder)
		select rateGrouping, @newregistrationID, rateGroupingOrder
		from dbo.ev_rateGrouping
		where registrationID = @registrationID
			IF @@ERROR <> 0 GOTO on_error

		-- active rates and permissions
		select @minRateId = min(r.rateID) 
			from dbo.ev_rates as r
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			where r.registrationID = @registrationID
			and r.parentRateID is null
		while @minRateID is not null BEGIN
			select @rateGroupingID = null, @rateGLAccountID = null, @rateName = null, 
				@ratereportCode = null, @rate = null, @ratestartDate = null, @rateendDate = null, 
				@siteResourceID = null, @rateqty = null

			select @rateGroupingID=rateGroupingID, @rateGLAccountID=GLAccountID, @rateName=rateName, 
				@ratereportCode=reportCode, @rate=rate, @ratestartDate=startDate, @rateendDate=endDate, 
				@siteResourceID=siteResourceID, @rateqty=bulkQty
			from dbo.ev_rates 
			where rateID = @minRateID

			select @newrateGroupingID = rg1.rateGroupingID
			from dbo.ev_rateGrouping as rg1
			inner join dbo.ev_rateGrouping as rg2 
				on rg2.rateGrouping = rg1.rateGrouping
				and rg1.registrationID = @newregistrationID
				and rg2.registrationID = @registrationID
				and isnull(rg2.rateGroupingID,0) = isnull(@rateGroupingID,0)
			
			IF @rateName is not null and @rate is not null BEGIN
				EXEC @rc = dbo.ev_createRate @registrationID=@newregistrationID,  
					@rateGroupingID=@newrateGroupingID, @GLAccountID=@rateGLAccountID, @rateName=@rateName, 
					@reportCode=@ratereportCode, @rate=@rate, @startDate=@ratestartDate, @endDate=@rateendDate,
					@parentRateID=null, @qty=@rateqty, 
					@rateID=@newRateID OUTPUT, @siteResourceID=@newRatesiteResourceID OUTPUT
					IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

				select @minBulkRateID = min(rateID)
				from dbo.ev_rates
				where parentRateID = @minRateID

				while @minBulkRateID is not null begin
					select @bulkrate=rate, @bulksiteResourceID=siteResourceID, @bulkrateqty=bulkQty
					from dbo.ev_rates 
					where rateID = @minBulkRateID

					EXEC @rc = dbo.ev_createRate @registrationID=@newregistrationID,  
						@rateGroupingID=@newrateGroupingID, @GLAccountID=@rateGLAccountID, @rateName=@rateName, 
						@reportCode=@ratereportCode, @rate=@rate, @startDate=@ratestartDate, @endDate=@rateendDate,
						@parentRateID=@newRateID, @qty=@bulkrateqty, 
						@rateID=@newBulkRateID OUTPUT, @siteResourceID=@newBulkRatesiteResourceID OUTPUT
						IF @@ERROR <> 0 OR @RC <> 0 GOTO on_error

					select @minBulkRateID = min(rateID)
					from dbo.ev_rates
					where parentRateID = @minRateID
					and rateID > @minBulkRateID
				end

				-- copy resource rights for this resource		
				SELECT @srr_rightsID = null	
				SELECT @srr_rightsID = min(resourceRightsID) from dbo.cms_siteResourceRights where resourceID = @siteResourceID
				WHILE @srr_rightsID IS NOT NULL BEGIN
					SELECT @srr_roleid=roleID, @srr_functionID=functionID, @srr_groupid=groupID, 
						@srr_memberid=memberID, @srr_include=[include], @srr_inheritedRightsResourceID=inheritedRightsResourceID, 
						@srr_inheritedRightsFunctionID=inheritedRightsFunctionID
					FROM dbo.cms_siteResourceRights
					WHERE resourceRightsID = @srr_rightsID

					EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@newRatesiteResourceID, @include=@srr_include, 
						@functionID=@srr_functionID, @roleID=@srr_roleid, @groupID=@srr_groupid, @memberID=@srr_memberid, 
						@inheritedRightsResourceID=@srr_inheritedRightsResourceID, @inheritedRightsFunctionID=@srr_inheritedRightsFunctionID, 
						@resourceRightID=@resourceRightID OUTPUT
					IF @@ERROR <> 0 GOTO on_error

					select @minBulkRateID = min(rateID)
					from dbo.ev_rates
					where parentRateID = @newRateID

					while @minBulkRateID is not null begin
						select @bulksiteResourceID=siteResourceID
						from dbo.ev_rates 
						where rateID = @minBulkRateID

						EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@bulksiteResourceID, @include=@srr_include, 
							@functionID=@srr_functionID, @roleID=@srr_roleid, @groupID=@srr_groupid, @memberID=@srr_memberid, 
							@inheritedRightsResourceID=@srr_inheritedRightsResourceID, @inheritedRightsFunctionID=@srr_inheritedRightsFunctionID, 
							@resourceRightID=@bulkresourceRightID OUTPUT
						IF @@ERROR <> 0 GOTO on_error

						select @minBulkRateID = min(rateID)
						from dbo.ev_rates
						where parentRateID = @minRateID
						and rateID > @newRateID
					end
		
					SELECT @srr_rightsID = min(resourceRightsID) from dbo.cms_siteResourceRights where resourceID = @siteResourceID and resourceRightsID > @srr_rightsID
				END

			END
			
			select @minRateId = min(rateID) 
				from dbo.ev_rates as r
				inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
				inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
					and srs.siteResourceStatusDesc = 'Active'
				where r.registrationID = @registrationID
				and r.parentRateID is null
				and rateID > @minRateID
		END

		-- custom questions
		SELECT @minCustomID = min(CustomID) FROM dbo.ev_registrationCustom WHERE registrationID = @registrationID
		WHILE @minCustomID is not null BEGIN
			INSERT INTO dbo.ev_registrationCustom (registrationID, areaID, FieldDesc, titleOnInvoice, customTypeID, IsRequired, RequiredMsg, fieldOrder, Status, amount, offerQTY, GLAccountID)
			SELECT	@newregistrationID, areaID, FieldDesc, titleOnInvoice, customTypeID, IsRequired, RequiredMsg, fieldOrder, Status, amount, offerQTY, GLAccountID
			FROM	dbo.ev_registrationCustom	
			WHERE	customID = @minCustomID
				IF @@ERROR <> 0 GOTO on_error
				SELECT @newCustomID = SCOPE_IDENTITY()
				
			INSERT INTO dbo.ev_customOptions(customID, optionDesc, optionOrder, status, amount)
			SELECT	@newCustomID, optionDesc, optionOrder, status, amount
			FROM	dbo.ev_customOptions
			WHERE	customID = @minCustomID
				IF @@ERROR <> 0 GOTO on_error			
	
			SELECT @minCustomID = min(CustomID) FROM dbo.ev_registrationCustom WHERE registrationID = @registrationID and CustomID > @minCustomID	
		END

	END

END

-- normal exit
IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

-- error exit
on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	SELECT @newEventID = 0
	RETURN -1

GO