use membercentral
GO

update dbo.admin_navigation
set navName = 'Accrual Schedule', navDesc = 'Accrual Schedule for Currently Deferred Revenue'
where navName = 'Deferred Income'
GO

update dbo.admin_tooltypes
set toolType = 'AccrualScheduleReport', toolCFC = 'Accounting.AccrualScheduleReport', toolDesc = 'Accrual Schedule Report'
where toolType = 'DeferredIncomeAnalysisReport'
GO

update dbo.cms_siteresourceTypes
set resourceType = 'AccrualScheduleReport' 
where resourceType = 'DeferredIncomeAnalysisReport'
GO

ALTER PROC dbo.tr_report_deferredIncome
@siteID int,
@groupBy varchar(12),
@endDate datetime,
@filename varchar(400)

AS

set nocount on

declare @orgID int, @orgcode varchar(10), @dtlist varchar(max), @fullSQL varchar(max)
select @orgID = o.orgID, @orgcode = o.orgcode 
	from dbo.sites as s
	inner join dbo.organizations as o on o.orgID = s.orgID
	where s.siteID = @siteID

declare @allGLs TABLE (GLAccountID int, thePathExpanded varchar(max), accountCode varchar(200), accountType varchar(30), GLCode varchar(30), thePath varchar(max))
insert into @allGLS
select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode, rgl.accountType, rgl.GLCode, rgl.thePath
from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl

-- set date to 11:59:59 of asOfDate
select @endDate = dateadd(ms,-3,dateadd(day,1,DATEADD(dd,DATEDIFF(dd,0,@endDate),0)))


-- get DITs to consider
IF OBJECT_ID('tempdb..#tmpRecog_a') IS NOT NULL 
	DROP TABLE #tmpRecog_a
CREATE TABLE #tmpRecog_a (transactionID int, recognitionDate datetime, recogMonth varchar(7), amount money, GLAccountID int)

insert into #tmpRecog_a
select t.transactionID, tDIT.recognitionDate, convert(varchar(7),tDit.recognitionDate,20) as recogMonth, t.amount, t.debitGlAccountID
from dbo.tr_transactionDIT as tDIT
inner join dbo.tr_transactions as t on t.transactionID = tDIT.transactionID
where t.recordedOnSiteID = @siteID
and tDIT.recognitionDate > @endDate
and tDIT.isActive = 1

insert into #tmpRecog_a
select tmp.transactionID, tmp.recognitionDate, tmp.recogMonth, t.amount * -1, tmp.GLAccountID
from dbo.tr_transactions as t
inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITOffsetTrans'
inner join #tmpRecog_a as tmp on tmp.transactionID = r.appliedToTransactionID
where t.transactionDate <= @endDate

insert into #tmpRecog_a
select tmp.transactionID, tmp.recognitionDate, tmp.recogMonth, tmp.amount * -1, tmp.GLAccountID
from dbo.tr_transactions as t
inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'OffsetTrans'
inner join #tmpRecog_a as tmp on tmp.transactionID = r.appliedToTransactionID
where t.transactionDate <= @endDate


-- sum by transactionID
IF OBJECT_ID('tempdb..#tmpRecog') IS NOT NULL
	DROP TABLE #tmpRecog
CREATE TABLE #tmpRecog (transactionID int, recognitionDate datetime, recogMonth varchar(7), amount money, GlAccountID int);

insert into #tmpRecog
select transactionID, recognitionDate, recogMonth, sum(amount) as amount, GLAccountID
from #tmpRecog_a
group by transactionID, recognitionDate, recogMonth, GLAccountID
having sum(amount) > 0	


IF @groupBy = 'raw' BEGIN
	IF OBJECT_ID('tempdb..#tmpRecog2') IS NOT NULL
		DROP TABLE #tmpRecog2
	IF OBJECT_ID('tempdb..##tmpMembersDIARpt' + @orgcode) IS NOT NULL 
		EXEC('DROP TABLE ##tmpMembersDIARpt' + @orgcode)

	select convert(varchar(10),tmp.recognitionDate,101) as [Recognition Date], tmp.recogMonth as [Recognition Period], 
		tmp.amount as [Recognition Amount],
		glDeb.thePathExpanded as [Debit Account], glDeb.accountCode as [Debit Account Code], 
		glCred.thePathExpanded as [Credit Account], glCred.accountCode as [Credit Account Code],
		tSaleTaxAdj.detail as [Revenue Detail], @orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as [Invoice Number],
		convert(varchar(10),i.dateBilled,101) as [Invoice Billed Date], 
		convert(varchar(10),i.dateDue,101) as [Invoice Due Date],
		m2.lastname + ', ' + m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') as [Member Name], 
		m2.membernumber as [Member Number], m2.memberID as assignedToMemberID
	into #tmpRecog2
	from #tmpRecog as tmp
	inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
	inner join @allGLs as glDeb on glDeb.glAccountID = t.debitGlAccountID
	inner join @allGLs as glCred on glCred.glAccountID = t.creditGlAccountID
	inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
	inner join dbo.tr_transactions as tSaleTaxAdj on tSaleTaxAdj.transactionID = r.appliedToTransactionID
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSaleTaxAdj.transactionID
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	inner join dbo.ams_members as m on m.memberID = tSaleTaxAdj.assignedToMemberID
	inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID

	declare @memList varchar(max)
	select @memList = COALESCE(@memList + ',', '') + cast(assignedToMemberID as varchar(10)) FROM #tmpRecog2
	declare @MDsql varchar(max)
	EXEC dbo.ams_getFlattenedMemberDataSQL @orgID=@orgID, @importDataMode=0, @memberIDList=@memList, @sql=@MDsql OUTPUT
	select @MDsql = stuff(@MDsql, charIndex('from membercentral',@MDsql), len('from membercentral'), 'into ##tmpMembersDIARpt' + @orgcode + ' from membercentral')
	EXEC(@MDsql)

	set @fullSQL = 'select tmp.[Recognition Date], tmp.[Recognition Period], tmp.[Recognition Amount], 
			tmp.[Debit Account], tmp.[Debit Account Code], tmp.[Credit Account], tmp.[Credit Account Code],
			tmp.[Revenue Detail], tmp.[Invoice Number], tmp.[Invoice Billed Date], tmp.[Invoice Due Date],
			tmp.[Member Name], tmp.[Member Number], m.*
		from #tmpRecog2 as tmp
		LEFT OUTER JOIN ##tmpMembersDIARpt' + @orgcode + ' as m ON m.MemberCentralID = tmp.assignedToMemberID'
	EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql

	IF OBJECT_ID('tempdb..#tmpRecog2') IS NOT NULL
		DROP TABLE #tmpRecog2
	IF OBJECT_ID('tempdb..##tmpMembersDIARpt' + @orgcode) IS NOT NULL 
		EXEC('DROP TABLE ##tmpMembersDIARpt' + @orgcode)
END 
ELSE BEGIN

	SELECT @dtlist = COALESCE(@dtlist + ',','') + quoteName(recogMonth)
		FROM #tmpRecog
		group by recogMonth
		ORDER BY recogMonth

	IF @dtlist is not null begin	
		IF OBJECT_ID('tempdb..##tmpRecog') IS NOT NULL
			DROP TABLE ##tmpRecog
		set @fullSQL = 'select *
			into ##tmpRecog
			from (select recogMonth, amount, GLAccountID from #tmpRecog) as tmp
			PIVOT (sum(amount) FOR recogMonth in (' + @dtlist + ')) as pvt'
		EXEC(@fullSQL)

		IF OBJECT_ID('tempdb..#tmpRecogTotal') IS NOT NULL
			DROP TABLE #tmpRecogTotal
		select GlAccountID, sum(amount) as total
		into #tmpRecogTotal
		from #tmpRecog
		group by GlAccountID

		IF OBJECT_ID('tempdb..#tmpRecogFinal') IS NOT NULL
			DROP TABLE #tmpRecogFinal
		select ROW_NUMBER() OVER(ORDER BY gl.thePath) as row, gl.thePathExpanded as [GL Account], gl.accountCode as [GL Account Code], 
			tmp.*, tmpt.total as Total
		into #tmpRecogFinal
		from ##tmpRecog as tmp
		inner join @allGLs as gl on gl.glAccountID = tmp.GlAccountID
		inner join #tmpRecogTotal as tmpt on tmpt.GlAccountID = tmp.GlAccountID

		IF OBJECT_ID('tempdb..##tmpRecog') IS NOT NULL
			DROP TABLE ##tmpRecog
		IF OBJECT_ID('tempdb..#tmpRecogTotal') IS NOT NULL
			DROP TABLE #tmpRecogTotal

		EXEC('ALTER TABLE #tmpRecogFinal DROP COLUMN GlAccountID;')

		set @fullSQL = 'select * from #tmpRecogFinal order by row'
		EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql

		IF OBJECT_ID('tempdb..#tmpRecogFinal') IS NOT NULL
			DROP TABLE #tmpRecogFinal
	END ELSE BEGIN
		set @fullSQL = 'select null as row, null as [GL Account], null as [GL Account Code], null as [Recognition Date], null as [Recognition Period] from #tmpRecog where 1=0'
		EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql
	END
END


IF OBJECT_ID('tempdb..#tmpRecog_a') IS NOT NULL 
	DROP TABLE #tmpRecog_a
IF OBJECT_ID('tempdb..#tmpRecog') IS NOT NULL
	DROP TABLE #tmpRecog

set nocount off

RETURN 0
GO


ALTER PROC [dbo].[enableSiteFeature]
	@siteID int,
	@toolTypeList varchar(1000)
AS


declare @tblTools TABLE (toolType varchar(100))
insert into @tblTools (toolType)
select listItem from dbo.fn_varCharListToTable(@toolTypeList,',')


declare @applicationInstanceID int
select @applicationInstanceID = applicationInstanceID
	from dbo.cms_applicationInstances
	where siteID = @siteID
	and applicationInstanceName = 'admin'


declare @toolType varchar(100)
SELECT @toolType = min(toolType) from @tblTools
WHILE @toolType is not null BEGIN

	-- Accrual Accounting
	if @toolType = 'AccrualAccounting' begin
		declare @orgID int, @GLAccountID int
		select @orgID = orgID from dbo.sites where siteID = @siteID

		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''useAccrualAccounting'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=5, @accountName='Deferred Revenue Accounts', @accountCode='', @GLCode='DEFERREDREVENUE', @parentGLAccountID=null, @invoiceProfileID=null, @isSystemAccount=1, @isTaxable=0, @invoiceContentID=null, @deferredGLAccountID=null, @GLAccountID=@GLAccountID output
		EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=5, @accountName='Deferred Sales Tax Accounts', @accountCode='', @GLCode='DEFERREDTAX', @parentGLAccountID=null, @invoiceProfileID=null, @isSystemAccount=1, @isTaxable=0, @invoiceContentID=null, @deferredGLAccountID=null, @GLAccountID=@GLAccountID output

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType in ('AccrualScheduleReport','DeferredIncomeAnalysisReport')
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- appt tracker
	if @toolType = 'AppointmentTrackerAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showApptTracker'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- email blast
	if @toolType = 'EmailBlast' begin
		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- member documents
	if @toolType = 'MemberDocs' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showMemberDocuments'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		declare @resourceTypeID int, @sectionID int, @parentSectionID int
		select @resourceTypeID = resourceTypeID from dbo.cms_siteResourceTypes where resourceType = 'ApplicationCreatedSection'
		select @sectionID = sectionID from dbo.cms_pageSections where siteID = @siteID and sectionName = 'MCAMSMemberDocuments'
		IF @sectionID is null begin
			select @parentSectionID = sectionID from dbo.cms_pageSections where sectionName = 'root' and parentSectionID is null and siteID = @siteID
			exec dbo.cms_createPageSection @siteID, @resourceTypeID, null, null, null, @parentSectionID, 'MCAMSMemberDocuments', 'MCAMSMemberDocuments', 0, @sectionID output
		end
	end	

	-- member history
	if @toolType = 'MemberHistoryAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showMemberHistory'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID
	end

	-- referrals
	if @toolType = 'ReferralsAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showReferrals'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- relationships
	if @toolType = 'RelationshipAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showRelationships'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- reports
	if @toolType = 'Reports' begin
		declare @siteCode varchar(10)
		select @sitecode = sitecode from dbo.sites where siteID = @siteID

		insert into dbo.admin_siteToolRestrictions (toolTypeID, siteID)
		select tooltypeid, @siteID 
		from dbo.admin_toolTypes 
		where toolCFC like 'Reports.custom.' + @sitecode + '.%'
		or (toolCFC like 'Reports.%' and left(toolCFC,15) <> 'Reports.custom.')
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- subscriptions
	if @toolType = 'SubscriptionAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showSubscriptions'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = 'SubRenewalAdmin'
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	-- tasks
	if @toolType = 'NotesAdmin' begin
		update dbo.cms_applicationInstances	
		set settingsXML.modify('replace value of (/settings/setting[@name=''showNotes'']/@value)[1] with ''true''')
		where applicationInstanceID = @applicationInstanceID

		insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
		select tooltypeID, @siteID
		from dbo.admin_toolTypes
		where toolType = @toolType
			except
		select tooltypeID, siteID 
		from dbo.admin_siteToolRestrictions
		where siteID = @siteID
	end

	SELECT @toolType = min(toolType) from @tblTools where toolType > @toolType
END


-- refresh and assign resources
exec dbo.createadminsuite @siteid


-- these need to be added after resources are there (createadminsuite)
IF EXISTS (select toolType from @tblTools where toolType = 'AppointmentTrackerAdmin')
	exec dbo.cms_createDefaultAppointmentCategories @siteID=@siteid, @contributingMemberID=461530

IF EXISTS (select toolType from @tblTools where toolType = 'EmailBlast')
	exec dbo.cms_createDefaultEmailBlastCategories @siteID=@siteID

IF EXISTS (select toolType from @tblTools where toolType = 'MemberHistoryAdmin')
	exec dbo.cms_createDefaultHistoryAdminCategories @siteID=@siteID, @contributingMemberID=461530

IF EXISTS (select toolType from @tblTools where toolType = 'RelationshipAdmin')
	exec dbo.cms_createDefaultRelationshipCategories @siteID=@siteID, @contributingMemberID=461530

IF EXISTS (select toolType from @tblTools where toolType = 'NotesAdmin')
	exec dbo.cms_createDefaultNotesCategories @siteID=@siteid, @contributingMemberID=461530

RETURN 0
GO

DECLARE	@toolTypeID int, @toolResourceTypeID int, @rc int, @level3NavigationID int, @resourceTypeFunctionID int
select @toolTypeID = null, @toolResourceTypeID = null, @level3NavigationID = null

BEGIN TRAN
	EXEC @rc = dbo.createAdminToolType @toolType='DeferredIncomeAnalysisReport', @toolCFC='Accounting.DeferredIncomeAnalysisReport', @toolDesc='Deferred Income Analysis Report',
		@toolTypeID=@toolTypeID OUTPUT, @resourceTypeID=@toolResourceTypeID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error
	EXEC @rc = dbo.createAdminNavigation @navName='Deferred Income', @navDesc='Deferred Income Analysis Report', @parentNavigationID=24, @navAreaID=3, @cfcMethod='showReport',
		@isHeader=0, @showInNav=1, @navigationID=@level3NavigationID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('Admin'),dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('Admin')))
	EXEC @rc = dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID=@resourceTypeFunctionID, @toolTypeID=@toolTypeID, @navigationID=@level3NavigationID 
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

	insert into dbo.admin_siteToolRestrictions (toolTypeID, siteID)	
	select @toolTypeID, siteID
	from dbo.sites
	where siteCode = 'ZZ'

IF @@TRANCOUNT > 0 COMMIT TRAN

declare @siteID int
select @siteID=min(siteID) from dbo.sites
while @siteID is not null begin
	exec dbo.createAdminSuite @siteID=@siteID
	select @siteID = min(siteID) from dbo.sites where siteID > @siteID
end

GOTO on_done

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN

on_done:

GO

CREATE PROC dbo.tr_report_deferredIncomeByCash
@siteID int,
@groupBy varchar(12),
@endDate datetime,
@filename varchar(400)

AS

set nocount on

declare @orgID int, @orgcode varchar(10), @dtlist varchar(max), @fullSQL varchar(max)
select @orgID = o.orgID, @orgcode = o.orgcode 
	from dbo.sites as s
	inner join dbo.organizations as o on o.orgID = s.orgID
	where s.siteID = @siteID

declare @allGLs TABLE (GLAccountID int, thePathExpanded varchar(max), accountCode varchar(200), accountType varchar(30), GLCode varchar(30), thePath varchar(max))
insert into @allGLS
select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode, rgl.accountType, rgl.GLCode, rgl.thePath
from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl

-- set date to 11:59:59 of asOfDate
select @endDate = dateadd(ms,-3,dateadd(day,1,DATEADD(dd,DATEDIFF(dd,0,@endDate),0)))


-- get DITs to consider
IF OBJECT_ID('tempdb..#tmpRecog') IS NOT NULL 
	DROP TABLE #tmpRecog
CREATE TABLE #tmpRecog (transactionID int, recognitionDate datetime, recogMonth varchar(7), amount money, GLAccountID int)

insert into #tmpRecog
select transactionID, recognitionDate, recogMonth, sum(amount) as amount, debitGlAccountID
from (
	select tDit.transactionID, trDit.recognitionDate, convert(varchar(7),trDit.recognitionDate,20) as recogMonth, 
		case when glAlloc.GLCode = 'ACCOUNTSRECEIVABLE' then tAlloc.amount else tAlloc.amount*-1 end as amount,
		tDit.debitGlAccountID
	from dbo.tr_batches as b
	inner join dbo.tr_batchTransactions as bt on bt.batchID = b.batchID
	inner join dbo.tr_transactions as tAlloc on tAlloc.transactionID = bt.transactionID
	inner join dbo.tr_GLAccounts as glAlloc on glAlloc.GLAccountID = tAlloc.creditGLAccountID
	inner join dbo.tr_relationships as r on r.transactionID = tAlloc.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as tDit on tDit.transactionID = r.appliedToTransactionID
	inner join dbo.tr_transactionDIT as trDit on trDit.transactionID = tDit.transactionID
	where b.orgID = @orgID
	and b.depositDate <= @endDate
	and trDit.isActive = 1
	and trDit.recognitionDate > @endDate
) as tmp
group by transactionID, recognitionDate, recogMonth, debitGlAccountID
having sum(amount) > 0


IF @groupBy = 'raw' BEGIN
	IF OBJECT_ID('tempdb..#tmpRecog2') IS NOT NULL
		DROP TABLE #tmpRecog2
	IF OBJECT_ID('tempdb..##tmpMembersDIARpt' + @orgcode) IS NOT NULL 
		EXEC('DROP TABLE ##tmpMembersDIARpt' + @orgcode)

	select convert(varchar(10),tmp.recognitionDate,101) as [Recognition Date], tmp.recogMonth as [Recognition Period], 
		tmp.amount as [Recognition Amount],
		glDeb.thePathExpanded as [Debit Account], glDeb.accountCode as [Debit Account Code], 
		glCred.thePathExpanded as [Credit Account], glCred.accountCode as [Credit Account Code],
		tSaleTaxAdj.detail as [Revenue Detail], @orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as [Invoice Number],
		convert(varchar(10),i.dateBilled,101) as [Invoice Billed Date], 
		convert(varchar(10),i.dateDue,101) as [Invoice Due Date],
		m2.lastname + ', ' + m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') as [Member Name], 
		m2.membernumber as [Member Number], m2.memberID as assignedToMemberID
	into #tmpRecog2
	from #tmpRecog as tmp
	inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
	inner join @allGLs as glDeb on glDeb.glAccountID = t.debitGlAccountID
	inner join @allGLs as glCred on glCred.glAccountID = t.creditGlAccountID
	inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
	inner join dbo.tr_transactions as tSaleTaxAdj on tSaleTaxAdj.transactionID = r.appliedToTransactionID
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSaleTaxAdj.transactionID
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	inner join dbo.ams_members as m on m.memberID = tSaleTaxAdj.assignedToMemberID
	inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID

	declare @memList varchar(max)
	select @memList = COALESCE(@memList + ',', '') + cast(assignedToMemberID as varchar(10)) FROM #tmpRecog2
	declare @MDsql varchar(max)
	EXEC dbo.ams_getFlattenedMemberDataSQL @orgID=@orgID, @importDataMode=0, @memberIDList=@memList, @sql=@MDsql OUTPUT
	select @MDsql = stuff(@MDsql, charIndex('from membercentral',@MDsql), len('from membercentral'), 'into ##tmpMembersDIARpt' + @orgcode + ' from membercentral')
	EXEC(@MDsql)

	set @fullSQL = 'select tmp.[Recognition Date], tmp.[Recognition Period], tmp.[Recognition Amount], 
			tmp.[Debit Account], tmp.[Debit Account Code], tmp.[Credit Account], tmp.[Credit Account Code],
			tmp.[Revenue Detail], tmp.[Invoice Number], tmp.[Invoice Billed Date], tmp.[Invoice Due Date],
			tmp.[Member Name], tmp.[Member Number], m.*
		from #tmpRecog2 as tmp
		LEFT OUTER JOIN ##tmpMembersDIARpt' + @orgcode + ' as m ON m.MemberCentralID = tmp.assignedToMemberID'
	EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql

	IF OBJECT_ID('tempdb..#tmpRecog2') IS NOT NULL
		DROP TABLE #tmpRecog2
	IF OBJECT_ID('tempdb..##tmpMembersDIARpt' + @orgcode) IS NOT NULL 
		EXEC('DROP TABLE ##tmpMembersDIARpt' + @orgcode)
END 
ELSE BEGIN

	SELECT @dtlist = COALESCE(@dtlist + ',','') + quoteName(recogMonth)
		FROM #tmpRecog
		group by recogMonth
		ORDER BY recogMonth

	IF @dtlist is not null begin
		IF OBJECT_ID('tempdb..##tmpRecog') IS NOT NULL
			DROP TABLE ##tmpRecog
		set @fullSQL = 'select *
			into ##tmpRecog
			from (select recogMonth, amount, GLAccountID from #tmpRecog) as tmp
			PIVOT (sum(amount) FOR recogMonth in (' + @dtlist + ')) as pvt'
		EXEC(@fullSQL)

		IF OBJECT_ID('tempdb..#tmpRecogTotal') IS NOT NULL
			DROP TABLE #tmpRecogTotal
		select GlAccountID, sum(amount) as total
		into #tmpRecogTotal
		from #tmpRecog
		group by GlAccountID

		IF OBJECT_ID('tempdb..#tmpRecogFinal') IS NOT NULL
			DROP TABLE #tmpRecogFinal
		select ROW_NUMBER() OVER(ORDER BY gl.thePath) as row, gl.thePathExpanded as [GL Account], gl.accountCode as [GL Account Code], 
			tmp.*, tmpt.total as Total
		into #tmpRecogFinal
		from ##tmpRecog as tmp
		inner join @allGLs as gl on gl.glAccountID = tmp.GlAccountID
		inner join #tmpRecogTotal as tmpt on tmpt.GlAccountID = tmp.GlAccountID

		IF OBJECT_ID('tempdb..##tmpRecog') IS NOT NULL
			DROP TABLE ##tmpRecog
		IF OBJECT_ID('tempdb..#tmpRecogTotal') IS NOT NULL
			DROP TABLE #tmpRecogTotal

		EXEC('ALTER TABLE #tmpRecogFinal DROP COLUMN GlAccountID;')

		set @fullSQL = 'select * from #tmpRecogFinal order by row'
		EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql

		IF OBJECT_ID('tempdb..#tmpRecogFinal') IS NOT NULL
			DROP TABLE #tmpRecogFinal
	END ELSE BEGIN
		set @fullSQL = 'select null as row, null as [GL Account], null as [GL Account Code], null as [Recognition Date], null as [Recognition Period] from #tmpRecog where 1=0'
		EXEC dbo.up_exportCSV @csvfilename=@filename, @sql=@fullsql
	END

END


IF OBJECT_ID('tempdb..#tmpRecog') IS NOT NULL
	DROP TABLE #tmpRecog

set nocount off

RETURN 0
GO

