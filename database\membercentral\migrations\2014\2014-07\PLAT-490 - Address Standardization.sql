use membercentral
GO

ALTER TABLE dbo.ams_memberAddressTypes ADD
	standardizeAddress bit NOT NULL CONSTRAINT DF_ams_memberAddressTypes_standardizeAddress DEFAULT 0,
	standardizeCity bit NOT NULL CONSTRAINT DF_ams_memberAddressTypes_standardizeCity DEFAULT 0,
	standardizeState bit NOT NULL CONSTRAINT DF_ams_memberAddressTypes_standardizeState DEFAULT 0,
	standardizePostalCode bit NOT NULL CONSTRAINT DF_ams_memberAddressTypes_standardizePostalCode DEFAULT 0,
	standardizeCounty bit NOT NULL CONSTRAINT DF_ams_memberAddressTypes_standardizeCounty DEFAULT 0
GO

ALTER TABLE dbo.ams_memberAddresses ADD
	dateLastUpdatedValidate datetime NULL
GO

ALTER PROC [dbo].[ams_getOrgAddressTypes]
@orgid int

AS

SELECT addressTypeID, addressType, addressTypeDesc, hasAttn, hasAddress2, hasAddress3, hasCounty, 
	districtMatching, standardizeAddress, standardizeCity, standardizeState, standardizePostalCode, 
	standardizeCounty
FROM dbo.ams_memberAddressTypes
WHERE orgID = @orgID
ORDER BY addressTypeOrder

RETURN 0
GO

ALTER PROC [dbo].[ams_createMemberAddressType]
@orgID int,
@addressType varchar(20),
@addressTypeDesc varchar(max),
@hasAttn bit,
@hasAddress2 bit,
@hasAddress3 bit,
@hasCounty bit,
@districtMatching bit,
@standardizeAddress bit,
@standardizeCity bit,
@standardizeState bit,
@standardizePostalCode bit,
@standardizeCounty bit

AS

IF NOT EXISTS (select addressTypeID from dbo.ams_memberAddressTypes where orgID=@orgID and addressType=@addressType) BEGIN
	DECLARE @newOrder int
	SELECT @newOrder = isnull(max(addressTypeOrder),0)+1
		from dbo.ams_memberAddressTypes
		where orgID = @orgID
		IF @@ERROR <> 0 GOTO on_error

	INSERT into dbo.ams_memberAddressTypes (orgid, addressType, addressTypeDesc, addressTypeOrder, hasAttn, 
		hasAddress2, hasAddress3, hasCounty, districtMatching, standardizeAddress, standardizeCity,
		standardizeState, standardizePostalCode, standardizeCounty) 
	VALUES (@orgID, @addressType, @addressTypeDesc, @newOrder, @hasAttn, @hasAddress2, @hasAddress3, 
		@hasCounty, @districtMatching, @standardizeAddress, @standardizeCity, @standardizeState, 
		@standardizePostalCode, @standardizeCounty)
		IF @@ERROR <> 0 GOTO on_error
END

RETURN 0

on_error:
	RETURN -1
GO

ALTER PROC [dbo].[ams_OrgSettingsUpdateAddress]
@orgID int,
@addressTypeID int,
@addressType varchar(20), 
@addressTypeDesc varchar(max), 
@hasAttn bit, 
@hasAddress2 bit, 
@hasAddress3 bit, 
@hasCounty bit,
@districtMatching bit,
@standardizeAddress bit,
@standardizeCity bit,
@standardizeState bit,
@standardizePostalCode bit,
@standardizeCounty bit

AS

DECLARE @oldaddressType varchar(20), @oldaddressTypeDesc varchar(max), @oldhasAttn bit, @oldhasAddress2 bit,
	@oldhasAddress3 bit, @oldhasCounty bit, @olddistrictMatching bit, @oldstandardizeAddress bit, 
	@oldstandardizeCity bit, @oldstandardizeState bit, @oldstandardizePostalCode bit,
	@oldstandardizeCounty bit
declare @rerunview bit, @minRID int, @ruleSQL varchar(max)
DECLARE @tblCond TABLE (conditionID int)

select @oldaddressType=addressType, @oldaddressTypeDesc=addressTypeDesc, @oldhasAttn=hasAttn,
	@oldhasAddress2=hasAddress2, @oldhasAddress3=hasAddress3, @oldhasCounty=hasCounty,
	@olddistrictMatching=districtMatching, @oldstandardizeAddress=standardizeAddress,
	@oldstandardizeCity=standardizeCity, @oldstandardizeState=standardizeState,
	@oldstandardizePostalCode=standardizePostalCode, @oldstandardizeCounty=standardizeCounty
	from dbo.ams_memberAddressTypes
	WHERE orgID = @orgID
	AND addressTypeID = @addressTypeID

BEGIN TRAN

IF @oldaddressType <> @addressType COLLATE SQL_Latin1_General_Cp1_CS_AS BEGIN
	-- new address type cannot already exist
	IF EXISTS (select addressTypeID from dbo.ams_memberAddressTypes where addressType = @addressType and orgID = @orgID and addressTypeID <> @addressTypeID) 
		GOTO on_error

	-- update address type
	update dbo.ams_memberAddressTypes set addressType = @addressType where orgID = @orgID and addressTypeID = @addressTypeID
		IF @@ERROR <> 0 GOTO on_error

	-- update memberfields
	update mf
	set mf.dbField = replace(mf.dbField,@oldaddressType+'_',@addressType+'_')
	from dbo.ams_memberFields as mf
	inner join dbo.ams_memberFieldSets as mfs on mfs.fieldsetID = mf.fieldsetID
	inner join dbo.sites as s on s.siteid = mfs.siteid and s.orgID = @orgID
	where (mf.fieldCode like 'ma\_'+cast(@addressTypeID as varchar(10))+'\_%' escape '\'
		or mf.fieldCode like 'mp\_'+cast(@addressTypeID as varchar(10))+'\_%' escape '\')
		IF @@ERROR <> 0 GOTO on_error

	-- update conditions. dont need to rerun conditions if we are just changing the name.
	UPDATE dbo.ams_virtualGroupConditions
	SET [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
	WHERE orgID = @orgID
	AND (fieldCode like 'ma\_'+cast(@addressTypeID as varchar(10))+'\_%' escape '\'
		or fieldCode like 'mp\_'+cast(@addressTypeID as varchar(10))+'\_%' escape '\')
		IF @@ERROR <> 0 GOTO on_error

	-- update rules
	SELECT @minRID = null
	SELECT @minRID = min(r.ruleID)
		from dbo.ams_virtualGroupRules as r
		CROSS APPLY ruleXML.nodes('//condition') as C(condition)
		inner join dbo.ams_virtualGroupConditions as vgc on vgc.uid = C.condition.value('@id','uniqueidentifier')
			and vgc.orgID = r.orgID
		where r.orgID = @orgID
		AND (vgc.fieldCode like 'ma\_'+cast(@addressTypeID as varchar(10))+'\_%' escape '\'
			or vgc.fieldCode like 'mp\_'+cast(@addressTypeID as varchar(10))+'\_%' escape '\')
	WHILE @minRID is not null BEGIN
		EXEC dbo.ams_updateVirtualGroupRuleSQL @ruleID=@minRID
			IF @@ERROR <> 0 GOTO on_error

		SELECT @minRID = min(r.ruleID)
			from dbo.ams_virtualGroupRules as r
			CROSS APPLY ruleXML.nodes('//condition') as C(condition)
			inner join dbo.ams_virtualGroupConditions as vgc on vgc.uid = C.condition.value('@id','uniqueidentifier')
				and vgc.orgID = r.orgID
			where r.orgID = @orgID
			AND (vgc.fieldCode like 'ma\_'+cast(@addressTypeID as varchar(10))+'\_%' escape '\'
				or vgc.fieldCode like 'mp\_'+cast(@addressTypeID as varchar(10))+'\_%' escape '\')
			and r.ruleID > @minRID
	END
	
	set @rerunview = 1
END

IF @oldaddressTypeDesc <> @addressTypeDesc COLLATE SQL_Latin1_General_Cp1_CS_AS BEGIN
	update dbo.ams_memberAddressTypes 
	set addressTypeDesc = @addressTypeDesc 
	where orgID = @orgID 
	and addressTypeID = @addressTypeID
		IF @@ERROR <> 0 GOTO on_error
END

IF @oldhasAttn <> @hasAttn BEGIN
	update dbo.ams_memberAddressTypes set hasAttn = @hasAttn where orgID = @orgID and addressTypeID = @addressTypeID
		IF @@ERROR <> 0 GOTO on_error
	IF @hasAttn = 0 BEGIN
		update dbo.ams_memberAddresses set attn = null where addressTypeID = @addressTypeID
			IF @@ERROR <> 0 GOTO on_error
		delete from dbo.ams_memberFields where fieldCode = 'ma_'+cast(@addressTypeID as varchar(10))+'_attn' and fieldsetID in (select fieldSetID from dbo.ams_memberFieldSets as mfs inner join dbo.sites as s on s.siteid = mfs.siteid and s.orgid = @orgid)
			IF @@ERROR <> 0 GOTO on_error
		INSERT INTO @tblCond(conditionID) SELECT conditionID FROM dbo.ams_virtualGroupConditions WHERE orgID = @orgID AND fieldCode = 'ma_'+cast(@addressTypeID as varchar(10))+'_attn'
			IF @@ERROR <> 0 GOTO on_error
	END
	set @rerunview = 1
END

IF @oldhasAddress2 <> @hasAddress2 BEGIN
	update dbo.ams_memberAddressTypes set hasAddress2 = @hasAddress2 where orgID = @orgID and addressTypeID = @addressTypeID
		IF @@ERROR <> 0 GOTO on_error
	IF @hasAddress2 = 0 BEGIN
		update dbo.ams_memberAddresses set address2 = null where addressTypeID = @addressTypeID
			IF @@ERROR <> 0 GOTO on_error
		delete from dbo.ams_memberFields where fieldCode = 'ma_'+cast(@addressTypeID as varchar(10))+'_address2' and fieldsetID in (select fieldSetID from dbo.ams_memberFieldSets as mfs inner join dbo.sites as s on s.siteid = mfs.siteid and s.orgid = @orgid)
			IF @@ERROR <> 0 GOTO on_error
		INSERT INTO @tblCond(conditionID) SELECT conditionID FROM dbo.ams_virtualGroupConditions WHERE orgID = @orgID AND fieldCode = 'ma_'+cast(@addressTypeID as varchar(10))+'_address2'
			IF @@ERROR <> 0 GOTO on_error
	END
	set @rerunview = 1
END

IF @oldhasAddress3 <> @hasAddress3 BEGIN
	update dbo.ams_memberAddressTypes set hasAddress3 = @hasAddress3 where orgID = @orgID and addressTypeID = @addressTypeID
		IF @@ERROR <> 0 GOTO on_error
	IF @hasAddress3 = 0 BEGIN
		update dbo.ams_memberAddresses set address3 = null where addressTypeID = @addressTypeID
			IF @@ERROR <> 0 GOTO on_error
		delete from dbo.ams_memberFields where fieldCode = 'ma_'+cast(@addressTypeID as varchar(10))+'_address3' and fieldsetID in (select fieldSetID from dbo.ams_memberFieldSets as mfs inner join dbo.sites as s on s.siteid = mfs.siteid and s.orgid = @orgid)
			IF @@ERROR <> 0 GOTO on_error
		INSERT INTO @tblCond(conditionID) SELECT conditionID FROM dbo.ams_virtualGroupConditions WHERE orgID = @orgID AND fieldCode = 'ma_'+cast(@addressTypeID as varchar(10))+'_address3'
			IF @@ERROR <> 0 GOTO on_error
	END
	set @rerunview = 1
END

IF @oldhasCounty <> @hasCounty BEGIN
	update dbo.ams_memberAddressTypes set hasCounty = @hasCounty where orgID = @orgID and addressTypeID = @addressTypeID
		IF @@ERROR <> 0 GOTO on_error
	IF @hasCounty = 0 BEGIN
		update dbo.ams_memberAddresses set county = null where addressTypeID = @addressTypeID
			IF @@ERROR <> 0 GOTO on_error
		delete from dbo.ams_memberFields where fieldCode = 'ma_'+cast(@addressTypeID as varchar(10))+'_county' and fieldsetID in (select fieldSetID from dbo.ams_memberFieldSets as mfs inner join dbo.sites as s on s.siteid = mfs.siteid and s.orgid = @orgid)
			IF @@ERROR <> 0 GOTO on_error
		INSERT INTO @tblCond(conditionID) SELECT conditionID FROM dbo.ams_virtualGroupConditions WHERE orgID = @orgID AND fieldCode = 'ma_'+cast(@addressTypeID as varchar(10))+'_county'
			IF @@ERROR <> 0 GOTO on_error
	END
	set @rerunview = 1
END

IF @olddistrictMatching <> @districtMatching BEGIN
	update dbo.ams_memberAddressTypes set districtMatching = @districtMatching where orgID = @orgID and addressTypeID = @addressTypeID
		IF @@ERROR <> 0 GOTO on_error
	IF @districtMatching = 0 BEGIN
		delete from dbo.ams_memberAddressData where addressID in (select addressID FROM dbo.ams_memberAddresses WHERE addressTypeID = @addressTypeID)
			IF @@ERROR <> 0 goto on_error
		delete from dbo.ams_memberFields where fieldCode like 'mad\_'+cast(@addressTypeID as varchar(10))+'\_%' ESCAPE('\') and fieldsetID in (select fieldSetID from dbo.ams_memberFieldSets as mfs inner join dbo.sites as s on s.siteid = mfs.siteid and s.orgid = @orgid)
			IF @@ERROR <> 0 GOTO on_error
		INSERT INTO @tblCond(conditionID) SELECT conditionID FROM dbo.ams_virtualGroupConditions WHERE orgID = @orgID AND fieldCode like 'mad\_'+cast(@addressTypeID as varchar(10))+'\_%' escape '\'
			IF @@ERROR <> 0 GOTO on_error
	END
	set @rerunview = 1
END

-- the standardize flags dont change the view so lets update them all 
update dbo.ams_memberAddressTypes 
set standardizeAddress = @standardizeAddress,
	standardizeCity = @standardizeCity,
	standardizeState = @standardizeState,
	standardizePostalCode = @standardizePostalCode,
	standardizeCounty = @standardizeCounty
where orgID = @orgID 
and addressTypeID = @addressTypeID
	IF @@ERROR <> 0 GOTO on_error

COMMIT TRAN

IF @rerunview = 1 BEGIN
	EXEC dbo.ams_createVWMemberData @orgID=@orgID

	declare @CIDList varchar(max)
	select @CIDList = COALESCE(@CIDList + ',', '') + cast(conditionID as varchar(10)) from @tblCond group by conditionID
	IF @CIDList is not null 
		EXEC dbo.ams_deleteVirtualGroupCondition @orgID=@orgID, @conditionIDList=@CIDList
END

RETURN 0

on_error:
	ROLLBACK TRAN
	RETURN -1
GO

ALTER PROC [dbo].[ams_createDefaultMemberAddressTypes]
@orgID int

AS

DECLARE @rc int

BEGIN TRAN

	EXEC @rc = dbo.ams_createMemberAddressType @orgID=@orgid, @addressType='Address', @addressTypeDesc='', 
		@hasAttn=1, @hasAddress2=1, @hasAddress3=1, @hasCounty=1, @districtMatching=0,
		@standardizeAddress=0, @standardizeCity=0, @standardizeState=0, @standardizePostalCode=0, 
		@standardizeCounty=0
		IF @@ERROR <> 0 OR @rc <> 0 GOTO on_error

IF @@TRANCOUNT > 0 COMMIT TRAN
RETURN 0

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN
	RETURN -1
GO


-- add function to permissions
declare @resourceTypeID int, @functionID int
select @resourceTypeID = dbo.fn_getResourceTypeID('Site')
EXEC dbo.cms_createSiteResourceFunction @resourceTypeID=@resourceTypeID, @functionName='validateMemberAddress',
	@displayName='Validate Member Address', @functionID=@functionID OUTPUT
GO

-- add logging table
USE [platformstats]
GO
CREATE TABLE [dbo].[apilog_smartystreets](
	[apilogID] [int] IDENTITY(1,1) NOT NULL,
	[orgID] [int] NOT NULL,
	[memberID] [int] NOT NULL,
	[addressTypeID] [int] NOT NULL,
	[dateEntered] [datetime] NOT NULL CONSTRAINT [DF_apilog_smartystreets_dateEntered]  DEFAULT (getdate()),
	[jsonResult] [varchar](max) NOT NULL,
 CONSTRAINT [PK_apilog_smartystreets] PRIMARY KEY CLUSTERED 
(
	[apilogID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO


-- add queue table
USE [customApps]
GO
CREATE TABLE [dbo].[schedTask_addressValidation](
	[autoID] [int] IDENTITY(1,1) NOT NULL,
	[addressID] [int] NOT NULL,
	[useStoredJSONIfCurrent] [bit] NOT NULL CONSTRAINT [DF_schedTask_addressValidation_useStoredJSONIfCurrent]  DEFAULT ((0)),
 CONSTRAINT [PK_schedTask_addressValidation] PRIMARY KEY CLUSTERED 
(
	[autoID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO


-- change API report scheduled Task
use membercentral
GO
update dbo.scheduledTasks
set name = 'MemberCentral API Usage Report',
	taskCFC = 'model.scheduledTasks.tasks.monthlyAPIReport'
where taskCFC = 'model.scheduledTasks.tasks.monthlyCiceroAPIReport'
GO


-- add scheduled Task   to call addressValidation.cfc - needs to run before districting
declare @taskID int, @nextRunDate datetime
select @nextRunDate = nextRunDate from membercentral.dbo.scheduledTasks where taskID = 56

insert into membercentral.dbo.scheduledTasks (name, nextRunDate, interval, intervalTypeID, taskCFC, timeoutMinutes, disabled, siteid)
values ('MemberCentral Address Validation', dateadd(m,-2,@nextRunDate), 10, 5, 'model.scheduledTasks.tasks.addressValidation', 10, 1, 1)
	select @taskID = SCOPE_IDENTITY()
insert into platformStats.dbo.scheduledTaskHistory (taskID, statusTypeID, dateStarted, dateEnded, serverid, dateLastUpdated)
values (@taskID, 2, '1/1/2000', '1/1/2000', 1, getdate())
GO

