USE [platformQueue]
GO
CREATE TABLE [dbo].[queue_S3Upload](
	[fileID] [int] IDENTITY(1,1) NOT NULL,
	[s3bucketName] [varchar](100) NOT NULL,
	[objectKey] [varchar](200) NOT NULL,
	[filePath] [varchar](200) NOT NULL,
     [deleteOnSuccess] [bit] NOT NULL,
	[dateAdded] [datetime] NOT NULL CONSTRAINT [DF_queue_S3Upload_dateAdded]  DEFAULT (getdate()),
	[nextAttemptDate] [datetime] NOT NULL CONSTRAINT [DF_queue_S3Upload_nextAttemptDate]  DEFAULT (getdate()),
	[hasFailed] [bit] NOT NULL CONSTRAINT [DF_queue_S3Upload_hasFailed]  DEFAULT ((0)),
 CONSTRAINT [PK_queue_S3Upload] PRIMARY KEY CLUSTERED 
(
	[fileID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
