USE [memberCentral]
GO
IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sub_report_SubscriptionBilling]') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].[sub_report_SubscriptionBilling]
GO
CREATE PROC dbo.sub_report_SubscriptionBilling
@tblName varchar(100),
@asOfDate datetime, 
@invoiceDueLower datetime

AS

declare @dynSQL varchar(4000)

-- "POOL OF TRANSACTIONS TO CONSIDER"
-- get all subscriber related transactions. this should include all transactions (ignoring status)
IF OBJECT_ID('tempdb..#tmpSubTrans') IS NOT NULL 
	DROP TABLE #tmpSubTrans
CREATE TABLE #tmpSubTrans (subscriberID int, transactionID int, typeID int)
	-- sales
	SET @dynSQL = '
		INSERT INTO #tmpSubTrans
		select s.subscriberID, t.transactionID, t.typeID
		from ' + @tblName + ' as s
		inner join dbo.tr_applications as a on a.applicationTypeID = 17 and a.itemType = ''Dues'' and a.ItemID = s.subscriberID
		inner join dbo.tr_transactions as t on t.transactionID = a.transactionID and t.typeID = 1'
	EXEC(@dynSQL)

	-- sales tax
	INSERT INTO #tmpSubTrans
	select rt.subscriberID, t.transactionID, t.typeid
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'SalesTaxTrans'
	inner join #tmpSubTrans as rt on rt.transactionID = tr.appliedToTransactionID and rt.typeID = 1
	where t.typeID = 7

	-- adjustments
	INSERT INTO #tmpSubTrans
	select rt.subscriberID, t.transactionID, t.typeid
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'AdjustTrans'
	inner join #tmpSubTrans as rt on rt.transactionID = tr.appliedToTransactionID  and rt.typeID in (1,7)
	where t.typeID = 3

	-- allocations (allocSaleTrans)
	INSERT INTO #tmpSubTrans
	select rt.subscriberID, t.transactionID, t.typeID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'AllocSaleTrans'
	inner join #tmpSubTrans as rt on rt.transactionID = tr.appliedToTransactionID and rt.typeID in (1,7,3)
	where t.typeID = 5

	-- writeoffs (writeOffSaleTrans)
	INSERT INTO #tmpSubTrans
	select rt.subscriberID, t.transactionID, t.typeID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'WriteOffSaleTrans'
	inner join #tmpSubTrans as rt on rt.transactionID = tr.appliedToTransactionID and rt.typeID in (1,7,3)
	where t.typeID = 6

	-- payments (tied to allocations - AllocPayTrans)
	INSERT INTO #tmpSubTrans
	select rt.subscriberID, t.transactionID, t.typeID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.appliedToTransactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'AllocPayTrans'
	inner join #tmpSubTrans as rt on rt.transactionID = tr.transactionID and rt.typeID = 5
	where t.typeID = 2

	-- writeoffs (writeOffPayTrans)
	INSERT INTO #tmpSubTrans
	select rt.subscriberID, t.transactionID, t.typeID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'WriteOffPayTrans'
	inner join #tmpSubTrans as rt on rt.transactionID = tr.appliedToTransactionID and rt.typeID = 2
	where t.typeID = 6

	-- refunds tied to included payments
	INSERT INTO #tmpSubTrans
	select rt.subscriberID, t.transactionID, t.typeID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'RefundTrans'
	inner join #tmpSubTrans as rt on rt.transactionID = tr.appliedToTransactionID and rt.typeID = 2
	where t.typeID = 4

	-- nsf tied to included payments
	INSERT INTO #tmpSubTrans
	select rt.subscriberID, t.transactionID, t.typeID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'NSFTrans'
	inner join #tmpSubTrans as rt on rt.transactionID = tr.appliedToTransactionID and rt.typeID = 2
	where t.typeID = 9

	-- void offsets
	INSERT INTO #tmpSubTrans
	select rt.subscriberID, t.transactionID, t.typeID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as tr on tr.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as trt on trt.typeID = tr.typeID and trt.type = 'OffsetTrans'
	inner join #tmpSubTrans as rt on rt.transactionID = tr.appliedToTransactionID
	where t.typeID = 8

-- consider amounts
IF OBJECT_ID('tempdb..#allRevTrans') IS NOT NULL 
	DROP TABLE #allRevTrans
CREATE TABLE #allRevTrans (subscriberID int, invoiceID int, subInvAmount money)
	-- all sales/tax 
	insert into #allRevTrans
	select tmp.subscriberID, i.invoiceID, t.amount
	from #tmpSubTrans as tmp
	inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	where t.typeID in (1,7)
	and i.statusID <> 1

	-- all postive adj 
	insert into #allRevTrans
	select tmp.subscriberID, i.invoiceID, t.amount
	from #tmpSubTrans as tmp
	inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
	inner join dbo.tr_glaccounts as gl on gl.glaccountID = t.debitGLAccountID
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	where t.typeID = 3
	and gl.GLCode = 'ACCOUNTSRECEIVABLE'
	and i.statusID <> 1

	-- all negative adj offsets revenue
	insert into #allRevTrans
	select tmp.subscriberID, itt.invoiceID, r.amount*-1
	from #tmpSubTrans as tmp
	inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
	inner join dbo.tr_glaccounts as gl on gl.glaccountID = t.creditGLAccountID
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustInvTrans'
	inner join dbo.tr_transactions as tSalesTax on tSalesTax.transactionID = r.appliedToTransactionID and tSalesTax.typeID in (1,3,7)
	inner join dbo.tr_invoiceTransactions as itt on itt.transactionID = tSalesTax.transactionID
	where t.typeID = 3
	and gl.GLCode = 'ACCOUNTSRECEIVABLE'
	and i.statusID <> 1

	-- voids of sales/tax offsets revenue
	insert into #allRevTrans
	select tmp.subscriberID, itt.invoiceID, t.amount*-1
	from #tmpSubTrans as tmp
	inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'OffsetTrans'
	inner join dbo.tr_transactions as tVoidee on tVoidee.transactionID = r.appliedToTransactionID and t.typeID in (1,7)
	inner join dbo.tr_invoiceTransactions as itt on itt.transactionID = tVoidee.transactionID
	where t.typeID = 8
	and i.statusID <> 1

	-- voids of pos adj offsets revenue
	insert into #allRevTrans
	select tmp.subscriberID, itt.invoiceID, t.amount*-1
	from #tmpSubTrans as tmp
	inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'OffsetTrans'
	inner join dbo.tr_transactions as tVoidee on tVoidee.transactionID = r.appliedToTransactionID and t.typeID = 3
	inner join dbo.tr_glaccounts as gl on gl.glaccountID = tVoidee.debitGLAccountID
	inner join dbo.tr_invoiceTransactions as itt on itt.transactionID = tVoidee.transactionID
	where t.typeID = 8
	and gl.GLCode = 'ACCOUNTSRECEIVABLE'
	and i.statusID <> 1

	-- voids of neg adj 
	insert into #allRevTrans
	select tmp.subscriberID, itt.invoiceID, t.amount
	from #tmpSubTrans as tmp
	inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
	inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
	inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'OffsetTrans'
	inner join dbo.tr_transactions as tVoidee on tVoidee.transactionID = r.appliedToTransactionID and t.typeID = 3
	inner join dbo.tr_glaccounts as gl on gl.glaccountID = tVoidee.creditGLAccountID
	inner join dbo.tr_relationships as rAdj on rAdj.transactionID = tVoidee.transactionID
	inner join dbo.tr_relationshipTypes as rtAdj on rtAdj.typeID = rAdj.typeID and rtAdj.type = 'AdjustTrans'
	inner join dbo.tr_transactions as tSalesTax on tSalesTax.transactionID = rAdj.appliedToTransactionID and tSalesTax.typeID in (1,7)
	inner join dbo.tr_invoiceTransactions as itt on itt.transactionID = tSalesTax.transactionID
	where t.typeID = 8
	and gl.GLCode = 'ACCOUNTSRECEIVABLE'
	and i.statusID <> 1

	-- allocation
	insert into #allRevTrans
	select tmp.subscriberID, it.invoiceID, t.amount*-1
	from #tmpSubTrans as tmp
	inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = t.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID
	inner join dbo.tr_glaccounts as gl on gl.glaccountID = t.creditGLAccountID
	inner join dbo.tr_relationships as rAlloc on rAlloc.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rtAlloc on rtAlloc.typeID = rAlloc.typeID and rtAlloc.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as tSalesTaxAdj on tSalesTaxAdj.transactionID = rAlloc.appliedToTransactionID
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSalesTaxAdj.transactionID
	where t.typeID = 5
	and b.depositDate <= @asOfDate
	and gl.glCode = 'ACCOUNTSRECEIVABLE'

	-- deallocation offsets alloc
	insert into #allRevTrans
	select tmp.subscriberID, it.invoiceID, t.amount
	from #tmpSubTrans as tmp
	inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = t.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID
	inner join dbo.tr_glaccounts as gl on gl.glaccountID = t.debitGLAccountID
	inner join dbo.tr_relationships as rAlloc on rAlloc.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rtAlloc on rtAlloc.typeID = rAlloc.typeID and rtAlloc.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as tSalesTaxAdj on tSalesTaxAdj.transactionID = rAlloc.appliedToTransactionID
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSalesTaxAdj.transactionID
	where t.typeID = 5
	and b.depositDate <= @asOfDate
	and gl.glCode = 'ACCOUNTSRECEIVABLE'

	-- write off
	insert into #allRevTrans
	select tmp.subscriberID, it.invoiceID, t.amount*-1
	from #tmpSubTrans as tmp
	inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
	inner join dbo.tr_glaccounts as gl on gl.glaccountID = t.creditGLAccountID
	inner join dbo.tr_relationships as rWO on rWO.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rtWO on rtWO.typeID = rWO.typeID and rtWO.type = 'WriteOffSaleTrans'
	inner join dbo.tr_transactions as tSalesTaxAdj on tSalesTaxAdj.transactionID = rWO.appliedToTransactionID
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSalesTaxAdj.transactionID
	where t.typeID = 6
	and t.transactionDate <= @asOfDate
	and gl.glCode = 'ACCOUNTSRECEIVABLE'

	-- void of allocation offsets alloc
	insert into #allRevTrans
	select tmp.subscriberID, it.invoiceID, t.amount
	from #tmpSubTrans as tmp
	inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = t.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID
	inner join dbo.tr_relationships as rV on rV.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rtV on rtV.typeID = rV.typeID and rtV.type = 'OffsetTrans'
	inner join dbo.tr_transactions as tAlloc on tAlloc.transactionID = rV.appliedToTransactionID and tAlloc.typeID = 5
	inner join dbo.tr_glaccounts as gl on gl.glaccountID = tAlloc.creditGLAccountID
	inner join dbo.tr_relationships as rAlloc on rAlloc.transactionID = tAlloc.transactionID
	inner join dbo.tr_relationshipTypes as rtAlloc on rtAlloc.typeID = rAlloc.typeID and rtAlloc.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as tSalesTaxAdj on tSalesTaxAdj.transactionID = rAlloc.appliedToTransactionID
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSalesTaxAdj.transactionID
	where t.typeID = 8
	and b.depositDate <= @asOfDate
	and gl.GLCode = 'ACCOUNTSRECEIVABLE'	

	-- void of deallocation 
	insert into #allRevTrans
	select tmp.subscriberID, it.invoiceID, t.amount*-1
	from #tmpSubTrans as tmp
	inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
	inner join dbo.tr_batchTransactions as bt on bt.transactionID = t.transactionID
	inner join dbo.tr_batches as b on b.batchID = bt.batchID
	inner join dbo.tr_relationships as rV on rV.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rtV on rtV.typeID = rV.typeID and rtV.type = 'OffsetTrans'
	inner join dbo.tr_transactions as tAlloc on tAlloc.transactionID = rV.appliedToTransactionID and tAlloc.typeID = 5
	inner join dbo.tr_glaccounts as gl on gl.glaccountID = tAlloc.debitGLAccountID
	inner join dbo.tr_relationships as rAlloc on rAlloc.transactionID = tAlloc.transactionID
	inner join dbo.tr_relationshipTypes as rtAlloc on rtAlloc.typeID = rAlloc.typeID and rtAlloc.type = 'AllocSaleTrans'
	inner join dbo.tr_transactions as tSalesTaxAdj on tSalesTaxAdj.transactionID = rAlloc.appliedToTransactionID
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSalesTaxAdj.transactionID
	where t.typeID = 8
	and b.depositDate <= @asOfDate
	and gl.GLCode = 'ACCOUNTSRECEIVABLE'	

	-- void of write off offets alloc
	insert into #allRevTrans
	select tmp.subscriberID, it.invoiceID, t.amount
	from #tmpSubTrans as tmp
	inner join dbo.tr_transactions as t on t.transactionID = tmp.transactionID
	inner join dbo.tr_relationships as rV on rV.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rtV on rtV.typeID = rV.typeID and rtV.type = 'OffsetTrans'
	inner join dbo.tr_transactions as tWO on tWO.transactionID = rV.appliedToTransactionID and tWO.typeID = 6
	inner join dbo.tr_glaccounts as gl on gl.glaccountID = tWO.creditGLAccountID
	inner join dbo.tr_relationships as rWO on rWO.transactionID = tWO.transactionID
	inner join dbo.tr_relationshipTypes as rtWO on rtWO.typeID = rWO.typeID and rtWO.type = 'WriteOffSaleTrans'
	inner join dbo.tr_transactions as tSalesTaxAdj on tSalesTaxAdj.transactionID = rWO.appliedToTransactionID
	inner join dbo.tr_invoiceTransactions as it on it.transactionID = tSalesTaxAdj.transactionID
	where t.typeID = 8
	and t.transactionDate <= @asOfDate
	and gl.GLCode = 'ACCOUNTSRECEIVABLE'

select subscriberID, invoiceID, dateDue, subInvAmount, 
	case when dateDue < @invoiceDueLower then DATEDIFF(dd,dateDue,@invoiceDueLower) else 0 end as daysPastDue
from (
	select tmp.subscriberID, tmp.invoiceID, i.dateDue, sum(tmp.subInvAmount) as subInvAmount
	from #allRevTrans as tmp
	inner join dbo.tr_invoices as i on i.invoiceID = tmp.invoiceID
	group by tmp.subscriberID, tmp.invoiceID, i.dateDue
	having sum(tmp.subInvAmount) > 0
) as tmp2


IF OBJECT_ID('tempdb..#tmpSubTrans') IS NOT NULL 
	DROP TABLE #tmpSubTrans
IF OBJECT_ID('tempdb..#allRevTrans') IS NOT NULL 
	DROP TABLE #allRevTrans

GO