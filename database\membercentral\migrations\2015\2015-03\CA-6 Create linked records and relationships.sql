-- CA-6 Create linked records and relationships
use membercentral
GO

BEGIN TRAN

declare @tmp table (memberID int, recordTypeID int, memberNumber varchar(max), [Contact Type] varchar(max), [mbrType] varchar(max), [firmID] varchar(max))
declare @tmpFirmMap table (masterMemberID int, childMemberID int)

declare 
	@LawFirmRTID int, 
	@IndivRTID int, 
	@VendorRTID int, 
	@CourtRTID int, 
	@orgID int,
	@firmID varchar(max),
	@recordTypeRelationshipTypeID int,
	@AttorneyRelTypeID int,
	@DefenseAttorneyRelTypeID int,
	@LawOfficeSupportRelTypeID int,
	@LawStudentRelTypeID int,
	@RepresentativeRelTypeID int,
	@GovernmentIndividualRelTypeID int,
	@IndividualRelTypeID int,
	@LawFirmChildRelTypeID int,
	@VendorFirmChildRelTypeID int,
	@SubCourtRelTypeID int,
	@AttorneyRTRTID int,
	@DefenseAttorneyRTRTID int,
	@LawOfficeSupportRTRTID int,
	@LawStudentRTRTID int,
	@VendorRTRTID int,
	@GovernmentIndividualRTRTID int,
	@GovernmentIndividualCourtRTRTID int,
	@SubCourtRTRTID int,
	@FirmOfficeRTRTID int,
	@VenderOfficeRTRTID int

select @orgID = orgID from sites where sitecode = 'CA'

--Record Types
select @LawFirmRTID = recordTypeID from dbo.ams_recordTypes where orgID = @orgID and recordTypeCode = 'LawFirm'
select @IndivRTID = recordTypeID from dbo.ams_recordTypes where orgID = @orgID and recordTypeCode = 'Individual'
select @VendorRTID = recordTypeID from dbo.ams_recordTypes where orgID = @orgID and recordTypeCode = 'VendorFirm'
select @CourtRTID = recordTypeID from dbo.ams_recordTypes where orgID = @orgID and recordTypeCode = 'Court'

--Relationship Types
select @AttorneyRelTypeID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgID and relationshipTypeCode = 'Attorney'
select @DefenseAttorneyRelTypeID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgID and relationshipTypeCode = 'DefenseAttorney'
select @LawOfficeSupportRelTypeID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgID and relationshipTypeCode = 'LawOfficeSupport'
select @LawStudentRelTypeID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgID and relationshipTypeCode = 'LawStudent'
select @RepresentativeRelTypeID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgID and relationshipTypeCode = 'Representative'
select @GovernmentIndividualRelTypeID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgID and relationshipTypeCode = 'GovernmentIndividual'
select @IndividualRelTypeID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgID and relationshipTypeCode = 'Individual'
select @LawFirmChildRelTypeID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgID and relationshipTypeCode = 'LawFirmChild'
select @VendorFirmChildRelTypeID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgID and relationshipTypeCode = 'VendorFirmChild'
select @SubCourtRelTypeID = relationshipTypeID from dbo.ams_recordRelationshipTypes where orgID = @orgID and relationshipTypeCode = 'SubCourt'


--RecordType Relationship Types
select @AttorneyRTRTID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes 
where masterRecordTypeID = @LawFirmRTID 
	and childRecordTypeID = @IndivRTID 
	and relationshipTypeID = @AttorneyRelTypeID

select @DefenseAttorneyRTRTID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes 
where masterRecordTypeID = @LawFirmRTID 
	and childRecordTypeID = @IndivRTID 
	and relationshipTypeID = @DefenseAttorneyRelTypeID

select @LawOfficeSupportRTRTID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes 
where masterRecordTypeID = @LawFirmRTID 
	and childRecordTypeID = @IndivRTID 
	and relationshipTypeID = @LawOfficeSupportRelTypeID

select @LawStudentRTRTID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes 
where masterRecordTypeID = @LawFirmRTID 
	and childRecordTypeID = @IndivRTID 
	and relationshipTypeID = @LawStudentRelTypeID

select @VendorRTRTID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes 
where masterRecordTypeID = @VendorRTID 
	and childRecordTypeID = @IndivRTID 
	and relationshipTypeID = @RepresentativeRelTypeID

select @GovernmentIndividualRTRTID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes 
where masterRecordTypeID =  @LawFirmRTID
	and childRecordTypeID = @IndivRTID 
	and relationshipTypeID = @GovernmentIndividualRelTypeID

select @GovernmentIndividualCourtRTRTID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes 
where masterRecordTypeID =  @CourtRTID
	and childRecordTypeID = @IndivRTID 
	and relationshipTypeID = @GovernmentIndividualRelTypeID

select @SubCourtRTRTID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes 
where masterRecordTypeID =  @CourtRTID
	and childRecordTypeID = @CourtRTID 
	and relationshipTypeID = @SubCourtRelTypeID

select @FirmOfficeRTRTID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes 
where masterRecordTypeID =  @LawFirmRTID
	and childRecordTypeID = @LawFirmRTID 
	and relationshipTypeID = @LawFirmChildRelTypeID

select @VenderOfficeRTRTID = recordTypeRelationshipTypeID from dbo.ams_recordTypesRelationshipTypes 
where masterRecordTypeID =  @VendorRTID
	and childRecordTypeID = @VendorRTID 
	and relationshipTypeID = @VendorFirmChildRelTypeID

insert into @tmp
select *
from (
	select m.memberID, m.recordTypeID, m.memberNumber, vw.[Contact Type], vw.[mbrtype], vw.[firmID]
	from dbo.vw_memberData_CA vw
	inner join dbo.ams_members m
	on m.memberID = vw.memberID
) as tmp


-- clear recordTypeID 
update ams_members set recordTypeid = null where orgID = @orgID
-- clear releationships 
delete from ams_recordRelationships 
where recordTypeRelationshipTypeID in (
	@AttorneyRTRTID, @DefenseAttorneyRTRTID, @LawOfficeSupportRTRTID,  @LawStudentRTRTID, @VendorRTRTID,
	@GovernmentIndividualRTRTID,@GovernmentIndividualCourtRTRTID,@SubCourtRTRTID,@FirmOfficeRTRTID,@VenderOfficeRTRTID)

-- Get master / child firm relationships

insert into @tmpFirmMap
select 	m.memberID as MasterMemberID, 
	tmp.memberID as ChildMemberID
from @tmp tmp
	inner join ams_members m
		on m.memberNumber = tmp.[firmID]
		and m.status <> 'D'
		and m.orgID = @orgID
where tmp.[contact type] in ('Firm Record%','Firm Record|Court','Firm Record|Previous Firm','Firm Record|Previous Firm|Court','Vendor Firm')
and tmp.firmID is not null



update dbo.ams_members
set recordTypeID = @CourtRTID
where memberID IN (
	select memberID from @tmp
	where [contact type] like '%Court%'
)
and orgID = @orgID
and recordTypeID is NULL
and status <> 'D'
IF @@ERROR <> 0 goto on_error

update dbo.ams_members
set recordTypeID = @LawFirmRTID
where memberID IN (
	select memberID from @tmp
	where [contact type] like 'Firm Record%'
)
and orgID = @orgID
and recordTypeID is NULL
and status <> 'D'
IF @@ERROR <> 0 goto on_error

update dbo.ams_members
set recordTypeID = @VendorRTID
where memberID IN (
	select memberID from @tmp
	where [contact type] like 'Vendor Firm'
)
and orgID = @orgID
and status <> 'D'
IF @@ERROR <> 0 goto on_error


update dbo.ams_members
set recordTypeID = @IndivRTID
where orgID = @orgID
and status <> 'D'
and recordTypeID is null
IF @@ERROR <> 0 goto on_error




------------------END RECORD TYPE SETTING--------------------------


------------------BEGIN RELATIONSHIP TYPE SETTING------------------
/* SubCourts and Law Offices */
insert into dbo.ams_recordRelationships (
	recordTypeRelationshipTypeID, 
	masterMemberID, 
	childMemberID, 
	isActive)
select 
	(
		select case when [contact type] = 'Firm Record|Court' then @SubCourtRTRTID 
					when [contact type] = 'Firm Record|Previous Firm|Court' then @SubCourtRTRTID 
					else @FirmOfficeRTRTID end from @tmp where memberid = m.memberID
	) as recordTypeRelationshipTypeID,
	m.memberID as MasterMemberID, 
	tmp.memberID as ChildMemberID,
	1 as isActive
from @tmp tmp
	inner join ams_members m
		on m.memberNumber = tmp.[firmID]
		and m.status <> 'D'
		and m.orgID = @orgID
where tmp.[contact type] like 'Firm%'
	AND tmp.[firmID] IS NOT NULL
IF @@ERROR <> 0 goto on_error

/* Vendor Offices */
insert into dbo.ams_recordRelationships (
	recordTypeRelationshipTypeID, 
	masterMemberID, 
	childMemberID, 
	isActive)
select 
	@VenderOfficeRTRTID as recordTypeRelationshipTypeID,
	m.memberID as MasterMemberID, 
	tmp.memberID as ChildMemberID,
	1 as isActive
from @tmp tmp
	inner join ams_members m
		on m.memberNumber = tmp.[firmID]
		and m.status <> 'D'
		and m.orgID = @orgID
where tmp.[contact type] in ('Vendor Firm')
	AND tmp.[firmID] IS NOT NULL
IF @@ERROR <> 0 goto on_error



/* Law Firms */
insert into dbo.ams_recordRelationships (
	recordTypeRelationshipTypeID, 
	masterMemberID, 
	childMemberID, 
	isActive)
select 
	@AttorneyRTRTID as recordTypeRelationshipTypeID,
	m.memberID as MasterMemberID, 
	tmp.memberID as ChildMemberID,
	1 as isActive
from @tmp tmp
	inner join ams_members m
		on m.memberNumber = tmp.[firmID]
		and m.status <> 'D'
		and m.orgID = @orgID
where tmp.[contact type] IN ('Attorney', 'Attorney|CAOC Staff', 'Attorney|Former CAOC Member','Attorney|Individual','Attorney|Out of State', 'Out of State') 
	AND tmp.[firmID] IS NOT NULL
IF @@ERROR <> 0 goto on_error

/* link to master record if exists */
insert into dbo.ams_recordRelationships (
	recordTypeRelationshipTypeID, 
	masterMemberID, 
	childMemberID, 
	isActive)
select 
	@AttorneyRTRTID as recordTypeRelationshipTypeID,
	fm.MasterMemberID as MasterMemberID, 
	tmp.memberID as ChildMemberID,
	1 as isActive
from @tmp tmp
	inner join ams_members m
		on m.memberNumber = tmp.[firmID]
		and m.status <> 'D'
		and m.orgID = @orgID
inner join @tmpFirmMap fm on fm.childmemberid = m.memberid
where tmp.[contact type] IN ('Attorney', 'Attorney|CAOC Staff', 'Attorney|Former CAOC Member','Attorney|Individual','Attorney|Out of State', 'Out of State') 
	AND tmp.[firmID] IS NOT NULL
IF @@ERROR <> 0 goto on_error

/* Government Individual */
insert into dbo.ams_recordRelationships (
	recordTypeRelationshipTypeID, 
	masterMemberID, 
	childMemberID, 
	isActive)
select 
	(
		select case when [contact type] = 'Firm Record|Court' then @GovernmentIndividualCourtRTRTID 
					when [contact type] = 'Firm Record|Previous Firm|Court' then @GovernmentIndividualCourtRTRTID 
					else @GovernmentIndividualRTRTID end from @tmp where memberid = m.memberID
	) as recordTypeRelationshipTypeID,
	m.memberID as MasterMemberID, 
	tmp.memberID as ChildMemberID,
	1 as isActive
from @tmp tmp
	inner join ams_members m
		on m.memberNumber = tmp.[firmID]
		and m.status <> 'D'
		and m.orgID = @orgID
where tmp.[contact type] IN ('Attorney|Government Individual', 'Government Individual') 
	AND tmp.[firmID] IS NOT NULL
IF @@ERROR <> 0 goto on_error

/* link to master record if exists */
insert into dbo.ams_recordRelationships (
	recordTypeRelationshipTypeID, 
	masterMemberID, 
	childMemberID, 
	isActive)
select 
	(
		select case when [contact type] = 'Firm Record|Court' then @GovernmentIndividualCourtRTRTID 
					when [contact type] = 'Firm Record|Previous Firm|Court' then @GovernmentIndividualCourtRTRTID 
					else @GovernmentIndividualRTRTID end from @tmp where memberid = m.memberID
	) as recordTypeRelationshipTypeID,
	fm.MasterMemberID as MasterMemberID, 
	tmp.memberID as ChildMemberID,
	1 as isActive
from @tmp tmp
	inner join ams_members m
		on m.memberNumber = tmp.[firmID]
		and m.status <> 'D'
		and m.orgID = @orgID
inner join @tmpFirmMap fm on fm.childmemberid = m.memberid
where tmp.[contact type] IN ('Attorney|Government Individual', 'Government Individual') 
	AND tmp.[firmID] IS NOT NULL
IF @@ERROR <> 0 goto on_error


insert into dbo.ams_recordRelationships (
	recordTypeRelationshipTypeID, 
	masterMemberID, 
	childMemberID, 
	isActive)
select 
	@DefenseAttorneyRTRTID as recordTypeRelationshipTypeID,
	m.memberID as MasterMemberID, 
	tmp.memberID as ChildMemberID,
	1 as isActive
from @tmp tmp
	inner join ams_members m
		on m.memberNumber = tmp.[firmID]
		and m.status <> 'D'
		and m.orgID = @orgID
where tmp.[contact type] IN ('Defense Attorney') 
	AND tmp.[firmID] IS NOT NULL
IF @@ERROR <> 0 goto on_error

/* link to master record if exists */
insert into dbo.ams_recordRelationships (
	recordTypeRelationshipTypeID, 
	masterMemberID, 
	childMemberID, 
	isActive)
select 
	@DefenseAttorneyRTRTID as recordTypeRelationshipTypeID,
	fm.MasterMemberID as MasterMemberID, 
	tmp.memberID as ChildMemberID,
	1 as isActive
from @tmp tmp
	inner join ams_members m
		on m.memberNumber = tmp.[firmID]
		and m.status <> 'D'
		and m.orgID = @orgID
inner join @tmpFirmMap fm on fm.childmemberid = m.memberid
where tmp.[contact type] IN ('Defense Attorney') 
	AND tmp.[firmID] IS NOT NULL
IF @@ERROR <> 0 goto on_error

insert into dbo.ams_recordRelationships (
	recordTypeRelationshipTypeID, 
	masterMemberID, 
	childMemberID, 
	isActive)
select 
	@LawOfficeSupportRTRTID as recordTypeRelationshipTypeID,
	m.memberID as MasterMemberID, 
	tmp.memberID as ChildMemberID,
	1 as isActive
from @tmp tmp
	inner join ams_members m
		on m.memberNumber = tmp.[firmID]
		and m.status <> 'D'
		and m.orgID = @orgID
where tmp.[contact type] IN ('Law Office Support') 
	AND tmp.[firmID] IS NOT NULL
IF @@ERROR <> 0 goto on_error

/* link to master record if exists */
insert into dbo.ams_recordRelationships (
	recordTypeRelationshipTypeID, 
	masterMemberID, 
	childMemberID, 
	isActive)
select 
	@LawOfficeSupportRTRTID as recordTypeRelationshipTypeID,
	fm.MasterMemberID as MasterMemberID, 
	tmp.memberID as ChildMemberID,
	1 as isActive
from @tmp tmp
	inner join ams_members m
		on m.memberNumber = tmp.[firmID]
		and m.status <> 'D'
		and m.orgID = @orgID
inner join @tmpFirmMap fm on fm.childmemberid = m.memberid
where tmp.[contact type] IN ('Law Office Support') 
	AND tmp.[firmID] IS NOT NULL
IF @@ERROR <> 0 goto on_error


insert into dbo.ams_recordRelationships (
	recordTypeRelationshipTypeID, 
	masterMemberID, 
	childMemberID, 
	isActive)
select 
	@LawStudentRTRTID as recordTypeRelationshipTypeID,
	m.memberID as MasterMemberID, 
	tmp.memberID as ChildMemberID,
	1 as isActive
from @tmp tmp
	inner join ams_members m
		on m.memberNumber = tmp.[firmID]
		and m.status <> 'D'
		and m.orgID = @orgID
where tmp.[contact type] IN ('Law Student') 
	AND tmp.[firmID] IS NOT NULL
IF @@ERROR <> 0 goto on_error

/* link to master record if exists */
insert into dbo.ams_recordRelationships (
	recordTypeRelationshipTypeID, 
	masterMemberID, 
	childMemberID, 
	isActive)
select 
	@LawStudentRTRTID as recordTypeRelationshipTypeID,
	fm.MasterMemberID as MasterMemberID,  
	tmp.memberID as ChildMemberID,
	1 as isActive
from @tmp tmp
	inner join ams_members m
		on m.memberNumber = tmp.[firmID]
		and m.status <> 'D'
		and m.orgID = @orgID
inner join @tmpFirmMap fm on fm.childmemberid = m.memberid
where tmp.[contact type] IN ('Law Student') 
	AND tmp.[firmID] IS NOT NULL
IF @@ERROR <> 0 goto on_error


/* Vendors */
insert into dbo.ams_recordRelationships (
	recordTypeRelationshipTypeID, 
	masterMemberID, 
	childMemberID, 
	isActive)
select 
	@VendorRTRTID as recordTypeRelationshipTypeID, 
	m.memberID as MasterMemberID, 
	tmp.memberID as ChildMemberID, 
	1 as isActive
from @tmp tmp
	inner join ams_members m
		on m.memberNumber = tmp.[firmID]
		and m.status <> 'D'
		and m.orgID = @orgID
where tmp.[contact type] in ('Vendor', 'Vendor Former Member')
	AND tmp.[firmID] IS NOT NULL
IF @@ERROR <> 0 goto on_error

/* link to master record if exists */
insert into dbo.ams_recordRelationships (
	recordTypeRelationshipTypeID, 
	masterMemberID, 
	childMemberID, 
	isActive)
select 
	@VendorRTRTID as recordTypeRelationshipTypeID, 
	fm.MasterMemberID as MasterMemberID,  
	tmp.memberID as ChildMemberID, 
	1 as isActive
from @tmp tmp
	inner join ams_members m
		on m.memberNumber = tmp.[firmID]
		and m.status <> 'D'
		and m.orgID = @orgID
inner join @tmpFirmMap fm on fm.childmemberid = m.memberid
where tmp.[contact type] in ('Vendor', 'Vendor Former Member')
	AND tmp.[firmID] IS NOT NULL
IF @@ERROR <> 0 goto on_error

COMMIT TRAN
goto on_success

on_error:
	ROLLBACK TRAN

on_success:
	-- queue member groups (@runSchedule=1 indicates immediate processing) 
	declare @itemGroupUID uniqueidentifier, @memberIDList varchar(max)
	select 
		@memberIDList = COALESCE(@memberIDList + ',', '') + cast(memberID as varchar(10)) 
	from 
		@tmp
	group by 
		memberID

	exec platformQueue.dbo.queue_processMemberGroups_insert 
			@orgID=@orgID, 
			@memberIDList=@memberIDList, 
			@conditionIDList='', 
			@runSchedule=1, 
			@itemGroupUID=@itemGroupUID OUTPUT
