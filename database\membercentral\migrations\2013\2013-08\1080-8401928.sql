-- OLD PLATFORM

declare @bucketTypeID int, @bucketGroupID int, @siteID int, @maxOrder int

select @siteid = 116
select @bucketTypeID = bucketTypeID from search.dbo.tblSearchBucketTypes  where bucketType = 'FileShare2'
select @bucketGroupID = 1

select 
	@maxOrder = isnull(max(bucketOrder) + 1,1)
from 
	search.dbo.tblSearchBuckets
where 
	siteid = @siteid
	and isActive = 1

print @siteid
print @bucketTypeID
print @bucketGroupID
print @maxOrder

declare @fileShareID varchar(200)
/*
select fileShareID = '<settings fileShareID="'+cast(fs.fileShareID as varchar(10))+'" />'
	from membercentral.dbo.fs_fileshare as fs
	inner join membercentral.dbo.cms_applicationInstances as ai on ai.applicationInstanceID = fs.applicationInstanceID
	where ai.siteID = 116
	and ai.applicationInstanceName = 'NYSTLA Plainitffs Only Transcript Database'
*/
set @fileShareID = '<settings fileShareID="707" />'
IF @fileShareID is not null 	
	insert into search.dbo.tblSearchBuckets (
		orgcode, 
		siteid, 
		bucketName, 
		bucketGroupID, 
		bucketTypeID, 
		bucketOrder, 
		bucketSettings, 
		hideUntilSearched, 
		isActive, 
		restrictToGroupID, 
		hideIfRestricted
	)
	values (
		NULL, 
		@siteid, 
		'Plaintiffs Only Database', 
		@bucketGroupID, 
		@bucketTypeID, 
		@maxOrder, 
		@fileShareID, 
		0, 
		1, 
		null, 
		0
	)

set @maxOrder = @maxOrder + 1
select @fileShareID = null
--select @fileShareID = '<settings fileShareID="'+cast(fs.fileShareID as varchar(10))+'" />'
--	from membercentral.dbo.fs_fileshare as fs
--	inner join membercentral.dbo.cms_applicationInstances as ai on ai.applicationInstanceID = fs.applicationInstanceID
--	where ai.siteID = 116
--	and ai.applicationInstanceName = 'NYSTLA Notice of Claims Database'
set @fileShareID = '<settings fileShareID="710" />'
IF @fileShareID is not null 	
	insert into search.dbo.tblSearchBuckets (
		orgcode, 
		siteid, 
		bucketName, 
		bucketGroupID, 
		bucketTypeID, 
		bucketOrder, 
		bucketSettings, 
		hideUntilSearched, 
		isActive, 
		restrictToGroupID, 
		hideIfRestricted
	)
	values (
		NULL, 
		@siteid, 
		'Notice of Claims', 
		@bucketGroupID, 
		@bucketTypeID, 
		@maxOrder, 
		@fileShareID, 
		0, 
		1, 
		null, 
		0
	)

GO



EXEC cms_deleteCategoryTree 579, 116
GO

