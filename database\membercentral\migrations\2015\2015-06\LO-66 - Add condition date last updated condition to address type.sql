/*
 In this SQL file the following tasks are performed:

(1) ALTER FUNCTION [fn_ams_getConditionFields_addrfields]
(2) ALTER FUNCTION [cache_members_populateMemberConditionCache]
(3) DROP PROCEDURE cache_members_populateMemberConditionCache_MA_EQ -- Replaced with PROCEDURE cache_members_populateMemberConditionCache_MA_EQ_STRING
(4) CREATE PROC cache_members_populateMemberConditionCache_MA_EQ_STRING
(5) DROP PROCEDURE cache_members_populateMemberConditionCache_MA_GT -- Replaced with PROCEDURE cache_members_populateMemberConditionCache_MA_GT_STRING
(6) CREATE PROC cache_members_populateMemberConditionCache_MA_GT_STRING
(7) DROP PROCEDURE cache_members_populateMemberConditionCache_MA_GTE -- Replaced with PROCEDURE cache_members_populateMemberConditionCache_MA_GTE_STRING
(8) CREATE PROC cache_members_populateMemberConditionCache_MA_GTE_STRING
(9) DROP PROCEDURE cache_members_populateMemberConditionCache_MA_LT -- Replaced with PROCEDURE cache_members_populateMemberConditionCache_MA_LT_STRING
(10) CREATE PROC cache_members_populateMemberConditionCache_MA_LT_STRING
(11) DROP PROCEDURE cache_members_populateMemberConditionCache_MA_LTE -- Replaced with PROCEDURE cache_members_populateMemberConditionCache_MA_LTE_STRING
(12) CREATE PROC cache_members_populateMemberConditionCache_MA_LTE_STRING
(13) DROP PROCEDURE cache_members_populateMemberConditionCache_MA_NEQ -- Replaced with PROCEDURE cache_members_populateMemberConditionCache_MA_NEQ_STRING
(14) CREATE PROC cache_members_populateMemberConditionCache_MA_NEQ_STRING
(15) CREATE PROC cache_members_populateMemberConditionCache_MA_NEQ_DATE
(16) CREATE PROC cache_members_populateMemberConditionCache_MA_LTE_DATE
(17) CREATE PROC cache_members_populateMemberConditionCache_MA_LT_DATE
(18) CREATE PROC cache_members_populateMemberConditionCache_MA_GTE_DATE
(19) CREATE PROC cache_members_populateMemberConditionCache_MA_GT_DATE
(20) CREATE PROC cache_members_populateMemberConditionCache_MA_EQ_DATE
(21) CREATE PROC cache_members_populateMemberConditionCache_MA_EXISTS_DATE
(22) CREATE PROC cache_members_populateMemberConditionCache_MA_NOTEXISTS_DATE

*/


USE membercentral
GO

/* (1) --------------------------------------------------------------------------------------------------------------------- */

ALTER FUNCTION [dbo].[fn_ams_getConditionFields_addrfields] (@orgID int, @addressTypeID int)
RETURNS TABLE 
AS
RETURN 
(
	select 1 as sorting, 'ma_' + cast(mat.addressTypeID as varchar(10)) + '_attn' as fieldCode, 'Attn' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from dbo.ams_memberAddressTypes as mat
		where mat.orgID = @orgID
		and mat.addressTypeID = @addressTypeID
		and mat.hasAttn = 1
		union all
	select 2 as sorting, 'ma_' + cast(mat.addressTypeID as varchar(10)) + '_address1' as fieldCode, 'Address' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from dbo.ams_memberAddressTypes as mat
		where mat.orgID = @orgID
		and mat.addressTypeID = @addressTypeID
		union all
	select 3 as sorting, 'ma_' + cast(mat.addressTypeID as varchar(10)) + '_address2' as fieldCode, 'Address 2' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from dbo.ams_memberAddressTypes as mat
		where mat.orgID = @orgID
		and mat.addressTypeID = @addressTypeID
		and mat.hasAddress2 = 1
		union all
	select 4 as sorting, 'ma_' + cast(mat.addressTypeID as varchar(10)) + '_address3' as fieldCode, 'Address 3' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from dbo.ams_memberAddressTypes as mat
		where mat.orgID = @orgID
		and mat.addressTypeID = @addressTypeID
		and mat.hasAddress3 = 1
		union all
	select 5 as sorting, 'ma_' + cast(mat.addressTypeID as varchar(10)) + '_city' as fieldCode, 'City' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from dbo.ams_memberAddressTypes as mat
		where mat.orgID = @orgID
		and mat.addressTypeID = @addressTypeID
		union all
	select 6 as sorting, 'ma_' + cast(mat.addressTypeID as varchar(10)) + '_stateprov' as fieldCode, 'State' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		from dbo.ams_memberAddressTypes as mat
		where mat.orgID = @orgID
		and mat.addressTypeID = @addressTypeID
		union all
	select 7 as sorting, 'ma_' + cast(mat.addressTypeID as varchar(10)) + '_postalcode' as fieldCode, 'Postal Code' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from dbo.ams_memberAddressTypes as mat
		where mat.orgID = @orgID
		and mat.addressTypeID = @addressTypeID
		union all
	select 8 as sorting, 'ma_' + cast(mat.addressTypeID as varchar(10)) + '_county' as fieldCode, 'County' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from dbo.ams_memberAddressTypes as mat
		where mat.orgID = @orgID
		and mat.addressTypeID = @addressTypeID
		and mat.hasCounty = 1
		union all
	select 9 as sorting, 'ma_' + cast(mat.addressTypeID as varchar(10)) + '_country' as fieldCode, 'Country' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		from dbo.ams_memberAddressTypes as mat
		where mat.orgID = @orgID
		and mat.addressTypeID = @addressTypeID
		union all
	select 10 as sorting, 'ma_' + cast(mat.addressTypeID as varchar(10)) + '_datelastupdated' as fieldCode, 'Last Updated Date' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'DATE' as dataTypeCode
		from dbo.ams_memberAddressTypes as mat
		where mat.orgID = @orgID
		and mat.addressTypeID = @addressTypeID
		union all
	
	select 10+row as sorting, 'mp_' + cast(addressTypeID as varchar(10)) + '_' + cast(phoneTypeID as varchar(10)) as fieldCode, phoneType as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
	from (
		select mat.addressTypeID, mpt.phoneTypeID, mat.addressType, mpt.phoneType, mat.addressTypeOrder, 
			ROW_NUMBER() OVER (ORDER BY mat.addressTypeOrder, mpt.phoneTypeOrder) as row
		from dbo.ams_memberPhoneTypes as mpt
		cross join dbo.ams_memberAddressTypes as mat
		where mpt.orgID = @orgID
		and mat.orgID = @orgID
		and mat.addressTypeID = @addressTypeID
	) as tmpPhones
	union all

	select 30+row as sorting, 'mad_' + cast(addressTypeID as varchar(10)) + '_' + cast(districtTypeID as varchar(10)) as fieldCode, districtType as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
	from (
		select mat.addressTypeID, mdt.districtTypeID, mat.addressType, mdt.districtType, mat.addressTypeOrder, 
			ROW_NUMBER() OVER (ORDER BY mat.addressTypeOrder, mdt.districtTypeOrder) as row
		from dbo.ams_memberDistrictTypes as mdt
		cross join dbo.ams_memberAddressTypes as mat
		where mdt.orgID = @orgID
		and mat.orgID = @orgID
		and mat.addressTypeID = @addressTypeID
		and mat.districtMatching = 1
	) as tmpDistricts

)
GO

/* (2) --------------------------------------------------------------------------------------------------------------------- */

ALTER PROC [dbo].[cache_members_populateMemberConditionCache]
@orgID int,
@conditionIDList varchar(max) = null,
@memberIDList varchar(max) = null,
@processImmediateOnly bit = 1,
@itemGroupUID uniqueidentifier,
@logTreeID uniqueidentifier

as

set nocount on

IF @itemGroupUID is null
	RETURN -1

declare @starttime datetime, @starttime2 datetime, @sql varchar(max), @totalMS int, @totalID int
select @starttime = getdate()
set @conditionIDList = isNull(@conditionIDList,'')
set @memberIDList = isNull(@memberIDList,'')


-- Log
INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage)
VALUES (@logTreeID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'Start Process of orgID=' + cast(@orgID as varchar(10)) + ' conditionID=' + left(@conditionIDList,100) + ' memberID=' + left(@memberIDList,100))


-- split members to calculate
IF OBJECT_ID('tempdb..#tblMembers') IS NOT NULL
	DROP TABLE #tblMembers
CREATE TABLE #tblMembers (memberID int PRIMARY KEY);

IF len(@memberIDList) > 0
	INSERT INTO #tblMembers
	select distinct m.memberID
	from dbo.fn_intListToTable(@memberIDList,',') as tmp
	inner join dbo.ams_members as m on m.memberID = tmp.listitem
	where m.orgID = @orgID
	and m.status <> 'D'
ELSE
	INSERT INTO #tblMembers
	select m.memberID
	from dbo.ams_members as m
	where m.orgID = @orgID
	and m.status <> 'D'

select @totalID = count(*) from #tblMembers 
INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage)
VALUES (@logTreeID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'Found ' + cast(@totalID as varchar(10)) + ' members to process');


-- split conditions to calculate. HONOR THE @processImmediateOnly parameter
IF OBJECT_ID('tempdb..#tblCond') IS NOT NULL
	DROP TABLE #tblCond
CREATE TABLE #tblCond (conditionID int PRIMARY KEY);

IF len(@conditionIDList) > 0
	INSERT INTO #tblCond
	select distinct c.conditionID
	from dbo.fn_intListToTable(@conditionIDList,',') as tmp
	inner join dbo.ams_virtualGroupConditions as c on c.conditionID = tmp.listitem
	inner join dbo.ams_virtualGroupConditionTypes as ct on ct.conditionTypeID = c.conditionTypeID
	where c.orgID = @orgID
	and c.isDefined = 1
	and 1 = case 
		when @processImmediateOnly = 1 and ct.processImmediately = 0 then 0
		else 1 end
ELSE
	INSERT INTO #tblCond
	select c.conditionID
	from dbo.ams_virtualGroupConditions as c
	inner join dbo.ams_virtualGroupConditionTypes as ct on ct.conditionTypeID = c.conditionTypeID
	where c.orgID = @orgID
	and c.isDefined = 1	
	and 1 = case 
		when @processImmediateOnly = 1 and ct.processImmediately = 0 then 0
		else 1 end


-- get all conditions to calculate
IF OBJECT_ID('tempdb..#tblCondALL') IS NOT NULL
	DROP TABLE #tblCondALL
CREATE TABLE #tblCondALL (conditionID int PRIMARY KEY, orgID int, expression varchar(20), fieldCode varchar(40), fieldCodeArea varchar(25), 
	displayTypeCode varchar(20), dataTypeCode varchar(20), fieldCodeAreaID int, fieldCodeAreaPartA varchar(20), subProc varchar(30));

INSERT INTO #tblCondALL
select c.conditionID, c.orgID, e.expression, c.fieldCode, fieldCodeArea = case
	when left(c.fieldCode,2) = 'm_' then 'Member Data'	
	when left(c.fieldCode,3) = 'md_' then 'Custom Fields'	
	when left(c.fieldCode,3) = 'ma_' then 'Addresses'	
	when left(c.fieldCode,3) = 'mp_' then 'Phones'	
	when left(c.fieldCode,4) = 'mad_' then 'Districting'	
	when left(c.fieldCode,3) = 'me_' then 'Emails'	
	when left(c.fieldCode,3) = 'mw_' then 'Websites'	
	when left(c.fieldCode,2) = 'e_' then 'Events'	
	when left(c.fieldcode,4) = 'mpl_' then 'Professional Licenses'	
	when left(c.fieldcode,4) = 'grp_' then 'Groups'	-- excluded in where clause
	when left(c.fieldcode,4) = 'sub_' then 'Subscriptions'	
	when left(c.fieldcode,3) = 'rt_' then 'Record Types'	
	when left(c.fieldcode,5) = 'acct_' then 'Accounting'	
	when left(c.fieldcode,3) = 'mh_' then 'Member History'	
	when left(c.fieldcode,2) = 'l_' then 'Listserver Memberships'
	end, dit.displayTypeCode, dat.dataTypeCode,
	fieldCodeAreaID = case 
	when left(c.fieldCode,3) = 'md_' then cast(replace(c.fieldcode,'md_','') as int)
	when left(c.fieldcode,4) = 'mpl_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,3) = 'ma_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,2) = 'e_' then cast(replace(c.fieldcode,'e_','') as int)
	when left(c.fieldCode,3) = 'me_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,3) = 'mw_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as int)
	when left(c.fieldCode,3) = 'mp_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as int)
	when left(c.fieldCode,4) = 'mad_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as int)
	else null end,
	fieldCodeAreaPartA = case
	when left(c.fieldcode,4) = 'mpl_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as varchar(20))
	when left(c.fieldCode,3) = 'ma_' then cast(parsename(replace(c.fieldcode,'_','.'),1) as varchar(20))
	when left(c.fieldCode,3) = 'mp_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as varchar(10))
	when left(c.fieldCode,4) = 'mad_' then cast(parsename(replace(c.fieldcode,'_','.'),2) as varchar(10))
	else null end,
	subProc = ''
from dbo.ams_virtualGroupConditions as c
inner join dbo.ams_virtualGroupExpressions as e on e.expressionID = c.expressionID
inner join dbo.ams_memberDataColumnDataTypes as dat on dat.dataTypeID = c.dataTypeID
inner join dbo.ams_memberDataColumnDisplayTypes as dit on dit.displayTypeID = c.displayTypeID
inner join #tblCond as tblC on tblc.conditionID = c.conditionID
where left(c.fieldCode,4) <> 'grp_'

-- add indexes for speed
CREATE INDEX IX_tblCondALL_areaid ON #tblCondALL (fieldCodeAreaID asc);
CREATE INDEX IX_tblCondALL_areaparta ON #tblCondALL (fieldCodeAreaPartA asc);
CREATE NONCLUSTERED INDEX IX_tblCondALL_DTA2 ON #tblCondALL (fieldCodeArea ASC, expression ASC, dataTypeCode ASC);

-- update subProcs to run
update #tblCondALL
set subProc = case 
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='between' then 'ACCT_BETWEEN_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='eq' then 'ACCT_EQ_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='exists' then 'ACCT_EXISTS_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='gt' then 'ACCT_GT_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='gte' then 'ACCT_GTE_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='lt' then 'ACCT_LT_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='lte' then 'ACCT_LTE_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='neq' then 'ACCT_NEQ_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsum' and expression='not_exists' then 'ACCT_NOTEXISTS_ALLOCSUM'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsumrecog' and expression='between' then 'ACCT_BETWEEN_ALLOCSUMRECOG'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsumrecog' and expression='eq' then 'ACCT_EQ_ALLOCSUMRECOG'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsumrecog' and expression='exists' then 'ACCT_EXISTS_ALLOCSUMRECOG'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsumrecog' and expression='gt' then 'ACCT_GT_ALLOCSUMRECOG'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsumrecog' and expression='gte' then 'ACCT_GTE_ALLOCSUMRECOG'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsumrecog' and expression='lt' then 'ACCT_LT_ALLOCSUMRECOG'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsumrecog' and expression='lte' then 'ACCT_LTE_ALLOCSUMRECOG'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsumrecog' and expression='neq' then 'ACCT_NEQ_ALLOCSUMRECOG'
	when fieldCodeArea='Accounting' and fieldCode='acct_allocsumrecog' and expression='not_exists' then 'ACCT_NOTEXISTS_ALLOCSUMRECOG'
	when fieldCodeArea='Accounting' and fieldCode='acct_inv' then 'ACCT_EXISTS_INV'
	when fieldCodeArea='Addresses' and expression='contains' then 'MA_CONTAINS'
	when fieldCodeArea='Addresses' and expression='contains_regex' then 'MA_CONTAINSREGEX'
	when fieldCodeArea='Addresses' and expression='eq' and dataTypeCode='STRING' then 'MA_EQ_STRING'
	when fieldCodeArea='Addresses' and expression='eq' and dataTypeCode='DATE' then 'MA_EQ_DATE'
	when fieldCodeArea='Addresses' and expression='exists' and dataTypeCode='DATE' then 'MA_EXISTS_DATE'
	when fieldCodeArea='Addresses' and expression='exists' then 'MA_EXISTS'
	when fieldCodeArea='Addresses' and expression='gt' and dataTypeCode='STRING' then 'MA_GT_STRING'
	when fieldCodeArea='Addresses' and expression='gt' and dataTypeCode='DATE' then 'MA_GT_DATE'
	when fieldCodeArea='Addresses' and expression='gte' and dataTypeCode='STRING' then 'MA_GTE_STRING'
	when fieldCodeArea='Addresses' and expression='gte' and dataTypeCode='DATE' then 'MA_GTE_DATE'
	when fieldCodeArea='Addresses' and expression='lt' and dataTypeCode='STRING' then 'MA_LT_STRING'
	when fieldCodeArea='Addresses' and expression='lt' and dataTypeCode='DATE' then 'MA_LT_DATE'
	when fieldCodeArea='Addresses' and expression='lte' and dataTypeCode='STRING' then 'MA_LTE_STRING'
	when fieldCodeArea='Addresses' and expression='lte' and dataTypeCode='DATE' then 'MA_LTE_DATE'
	when fieldCodeArea='Addresses' and expression='neq' and dataTypeCode='STRING' then 'MA_NEQ_STRING'
	when fieldCodeArea='Addresses' and expression='neq' and dataTypeCode='DATE' then 'MA_NEQ_DATE'
	when fieldCodeArea='Addresses' and expression='not_exists' and dataTypeCode='DATE' then 'MA_NOTEXISTS_DATE'
	when fieldCodeArea='Addresses' and expression='not_exists' then 'MA_NOTEXISTS'
	when fieldCodeArea='Custom Fields' and expression='datediff' then 'MD_DATEDIFF'
	when fieldCodeArea='Custom Fields' and expression='datepart' then 'MD_DATEPART'
	when fieldCodeArea='Custom Fields' and expression='contains' and dataTypeCode='STRING' then 'MD_CONTAINS_STRING'
	when fieldCodeArea='Custom Fields' and expression='contains_regex' and dataTypeCode='STRING' then 'MD_CONTAINSREGEX_STRING'
	when fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='STRING' then 'MD_EQ_STRING'
	when fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='BIT' then 'MD_EQ_BIT'
	when fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='INTEGER' then 'MD_EQ_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='DECIMAL2' then 'MD_EQ_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='eq' and dataTypeCode='DATE' then 'MD_EQ_DATE'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='STRING' then 'MD_EXISTS_STRING'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='BIT' then 'MD_EXISTS_BIT'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='INTEGER' then 'MD_EXISTS_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='DECIMAL2' then 'MD_EXISTS_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='DATE' then 'MD_EXISTS_DATE'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='CONTENTOBJ' then 'MD_EXISTS_CONTENTOBJ'
	when fieldCodeArea='Custom Fields' and expression='exists' and dataTypeCode='DOCUMENTOBJ' then 'MD_EXISTS_DOCUMENTOBJ'
	when fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='STRING' then 'MD_GT_STRING'
	when fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='INTEGER' then 'MD_GT_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='DECIMAL2' then 'MD_GT_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='gt' and dataTypeCode='DATE' then 'MD_GT_DATE'
	when fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='STRING' then 'MD_GTE_STRING'
	when fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='INTEGER' then 'MD_GTE_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='DECIMAL2' then 'MD_GTE_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='gte' and dataTypeCode='DATE' then 'MD_GTE_DATE'
	when fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='STRING' then 'MD_LT_STRING'
	when fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='INTEGER' then 'MD_LT_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='DECIMAL2' then 'MD_LT_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='lt' and dataTypeCode='DATE' then 'MD_LT_DATE'
	when fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='STRING' then 'MD_LTE_STRING'
	when fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='INTEGER' then 'MD_LTE_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='DECIMAL2' then 'MD_LTE_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='lte' and dataTypeCode='DATE' then 'MD_LTE_DATE'
	when fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='STRING' then 'MD_NEQ_STRING'
	when fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='BIT' then 'MD_NEQ_BIT'
	when fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='INTEGER' then 'MD_NEQ_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='DECIMAL2' then 'MD_NEQ_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='neq' and dataTypeCode='DATE' then 'MD_NEQ_DATE'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='STRING' then 'MD_NOTEXISTS_STRING'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='INTEGER' then 'MD_NOTEXISTS_INTEGER'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='DECIMAL2' then 'MD_NOTEXISTS_DECIMAL2'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='DATE' then 'MD_NOTEXISTS_DATE'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='BIT' then 'MD_NOTEXISTS_BIT'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='CONTENTOBJ' then 'MD_NOTEXISTS_CONTENTOBJ'
	when fieldCodeArea='Custom Fields' and expression='not_exists' and dataTypeCode='DOCUMENTOBJ' then 'MD_NOTEXISTS_DOCUMENTOBJ'
	when fieldCodeArea='Districting' and expression='eq' then 'MAD_EQ'
	when fieldCodeArea='Districting' and expression='exists' then 'MAD_EXISTS'
	when fieldCodeArea='Districting' and expression='neq' then 'MAD_NEQ'
	when fieldCodeArea='Districting' and expression='not_exists' then 'MAD_NOTEXISTS'
	when fieldCodeArea='Emails' and expression='contains' then 'ME_CONTAINS'
	when fieldCodeArea='Emails' and expression='contains_regex' then 'ME_CONTAINSREGEX'
	when fieldCodeArea='Emails' and expression='eq' then 'ME_EQ'
	when fieldCodeArea='Emails' and expression='exists' then 'ME_EXISTS'
	when fieldCodeArea='Emails' and expression='gt' then 'ME_GT'
	when fieldCodeArea='Emails' and expression='gte' then 'ME_GTE'
	when fieldCodeArea='Emails' and expression='lt' then 'ME_LT'
	when fieldCodeArea='Emails' and expression='lte' then 'ME_LTE'
	when fieldCodeArea='Emails' and expression='neq' then 'ME_NEQ'
	when fieldCodeArea='Emails' and expression='not_exists' then 'ME_NOTEXISTS'
	when fieldCodeArea='Events' and expression='registered' then 'E_REGISTERED'
	when fieldCodeArea='Events' and expression='attended' then 'E_ATTENDED'
	when fieldCodeArea='Events' and expression='awarded' then 'E_AWARDED'
	when fieldCodeArea='Listserver Memberships' and fieldCode='l_entry' and expression='onlist' then 'L_ONLIST'
	when fieldCodeArea='Member Data' and expression='contains' then 'M_CONTAINS'
	when fieldCodeArea='Member Data' and expression='contains_regex' then 'M_CONTAINSREGEX'
	when fieldCodeArea='Member Data' and expression='eq' and dataTypeCode='DATE' then 'M_EQ_DATE'
	when fieldCodeArea='Member Data' and expression='eq' and dataTypeCode='STRING' then 'M_EQ_STRING'
	when fieldCodeArea='Member Data' and expression='eq' and dataTypeCode='INTEGER' then 'M_EQ_INTEGER'
	when fieldCodeArea='Member Data' and expression='exists' and dataTypeCode='DATE' then 'M_EXISTS_DATE'
	when fieldCodeArea='Member Data' and expression='exists' then 'M_EXISTS'
	when fieldCodeArea='Member Data' and expression='gt' and dataTypeCode='DATE' then 'M_GT_DATE'
	when fieldCodeArea='Member Data' and expression='gt' and dataTypeCode='STRING' then 'M_GT_STRING'
	when fieldCodeArea='Member Data' and expression='gte' and dataTypeCode='DATE' then 'M_GTE_DATE'
	when fieldCodeArea='Member Data' and expression='gte' and dataTypeCode='STRING' then 'M_GTE_STRING'
	when fieldCodeArea='Member Data' and expression='lt' and dataTypeCode='DATE' then 'M_LT_DATE'
	when fieldCodeArea='Member Data' and expression='lt' and dataTypeCode='STRING' then 'M_LT_STRING'
	when fieldCodeArea='Member Data' and expression='lte' and dataTypeCode='DATE' then 'M_LTE_DATE'
	when fieldCodeArea='Member Data' and expression='lte' and dataTypeCode='STRING' then 'M_LTE_STRING'
	when fieldCodeArea='Member Data' and expression='neq' and dataTypeCode='DATE' then 'M_NEQ_DATE'
	when fieldCodeArea='Member Data' and expression='neq' and dataTypeCode='STRING' then 'M_NEQ_STRING'
	when fieldCodeArea='Member Data' and expression='neq' and dataTypeCode='INTEGER' then 'M_NEQ_INTEGER'
	when fieldCodeArea='Member Data' and expression='not_exists' and dataTypeCode='DATE' then 'M_NOTEXISTS_DATE'
	when fieldCodeArea='Member Data' and expression='not_exists' then 'M_NOTEXISTS'
	when fieldCodeArea='Member History' and fieldCode='mh_entry' and expression='exists' then 'MH_EXISTS'
	when fieldCodeArea='Phones' and expression='contains' then 'MP_CONTAINS'
	when fieldCodeArea='Phones' and expression='contains_regex' then 'MP_CONTAINSREGEX'
	when fieldCodeArea='Phones' and expression='eq' then 'MP_EQ'
	when fieldCodeArea='Phones' and expression='exists' then 'MP_EXISTS'
	when fieldCodeArea='Phones' and expression='gt' then 'MP_GT'
	when fieldCodeArea='Phones' and expression='gte' then 'MP_GTE'
	when fieldCodeArea='Phones' and expression='lt' then 'MP_LT'
	when fieldCodeArea='Phones' and expression='lte' then 'MP_LTE'
	when fieldCodeArea='Phones' and expression='neq' then 'MP_NEQ'
	when fieldCodeArea='Phones' and expression='not_exists' then 'MP_NOTEXISTS'
	when fieldCodeArea='Professional Licenses' and expression='contains' then 'MPL_CONTAINS'
	when fieldCodeArea='Professional Licenses' and expression='contains_regex' then 'MPL_CONTAINSREGEX'
	when fieldCodeArea='Professional Licenses' and expression='datepart' then 'MPL_DATEPART'
	when fieldCodeArea='Professional Licenses' and expression='datediff' then 'MPL_DATEDIFF'
	when fieldCodeArea='Professional Licenses' and expression='eq' and dataTypeCode='DATE' then 'MPL_EQ_DATE'
	when fieldCodeArea='Professional Licenses' and expression='eq' and dataTypeCode='STRING' then 'MPL_EQ_STRING'
	when fieldCodeArea='Professional Licenses' and expression='exists' and dataTypeCode='DATE' then 'MPL_EXISTS_DATE'
	when fieldCodeArea='Professional Licenses' and expression='exists' and dataTypeCode='STRING' then 'MPL_EXISTS_STRING'
	when fieldCodeArea='Professional Licenses' and expression='gt' and dataTypeCode='DATE' then 'MPL_GT_DATE'
	when fieldCodeArea='Professional Licenses' and expression='gt' and dataTypeCode='STRING' then 'MPL_GT_STRING'
	when fieldCodeArea='Professional Licenses' and expression='gte' and dataTypeCode='DATE' then 'MPL_GTE_DATE'
	when fieldCodeArea='Professional Licenses' and expression='gte' and dataTypeCode='STRING' then 'MPL_GTE_STRING'
	when fieldCodeArea='Professional Licenses' and expression='lt' and dataTypeCode='DATE' then 'MPL_LT_DATE'
	when fieldCodeArea='Professional Licenses' and expression='lt' and dataTypeCode='STRING' then 'MPL_LT_STRING'
	when fieldCodeArea='Professional Licenses' and expression='lte' and dataTypeCode='DATE' then 'MPL_LTE_DATE'
	when fieldCodeArea='Professional Licenses' and expression='lte' and dataTypeCode='STRING' then 'MPL_LTE_STRING'
	when fieldCodeArea='Professional Licenses' and expression='neq' and dataTypeCode='DATE' then 'MPL_NEQ_DATE'
	when fieldCodeArea='Professional Licenses' and expression='neq' and dataTypeCode='STRING' then 'MPL_NEQ_STRING'
	when fieldCodeArea='Professional Licenses' and expression='not_exists' and dataTypeCode='DATE' then 'MPL_NOTEXISTS_DATE'
	when fieldCodeArea='Professional Licenses' and expression='not_exists' and dataTypeCode='STRING' then 'MPL_NOTEXISTS_STRING'
	when fieldCodeArea='Record Types' and fieldCode='rt_role' and expression='linked' then 'RT_LINKED'
	when fieldCodeArea='Subscriptions' and expression='subscribed' then 'SUB_SUBSCRIBED'
	when fieldCodeArea='Websites' and expression='contains' then 'MW_CONTAINS'
	when fieldCodeArea='Websites' and expression='contains_regex' then 'MW_CONTAINSREGEX'
	when fieldCodeArea='Websites' and expression='eq' then 'MW_EQ'
	when fieldCodeArea='Websites' and expression='exists' then 'MW_EXISTS'
	when fieldCodeArea='Websites' and expression='gt' then 'MW_GT'
	when fieldCodeArea='Websites' and expression='gte' then 'MW_GTE'
	when fieldCodeArea='Websites' and expression='lt' then 'MW_LT'
	when fieldCodeArea='Websites' and expression='lte' then 'MW_LTE'
	when fieldCodeArea='Websites' and expression='neq' then 'MW_NEQ'
	when fieldCodeArea='Websites' and expression='not_exists' then 'MW_NOTEXISTS'
	else ''
	end

-- for the final processing at the end
IF OBJECT_ID('tempdb..#tblCondALLFinal') IS NOT NULL
	DROP TABLE #tblCondALLFinal
CREATE TABLE #tblCondALLFinal (conditionID int PRIMARY KEY);

insert into #tblCondALLFinal
select conditionID from #tblCondALL


-- put condition values into temp table by datatype to remove all casting in individual processing queries
IF OBJECT_ID('tempdb..#tblCondValues') IS NOT NULL
	DROP TABLE #tblCondValues
CREATE TABLE #tblCondValues (conditionID int, conditionKeyID int, conditionValueString varchar(max), conditionValueInteger int, conditionValueBit bit, conditionValueDecimal2 decimal(9,2), conditionValueDate datetime);

insert into #tblCondValues
select cv.conditionID, cv.conditionKeyID, null, cv.conditionValue, null, null, null
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where 
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'STRING' and tblc.displayTypeCode in ('RADIO','SELECT')) or
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'DECIMAL2' and tblc.displayTypeCode in ('RADIO','SELECT')) or 
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'DATE' and tblc.displayTypeCode in ('RADIO','SELECT')) or 
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq','lt','lte','gt','gte') and tblc.dataTypeCode = 'INTEGER') or
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('datepart','datediff')) or
	(tblc.fieldCodeArea = 'Addresses' and tblc.expression in ('datepart','datediff')) or
	(tblc.fieldCodeArea = 'Addresses' and tblc.expression in ('eq','neq') and tblc.displayTypeCode in ('RADIO','SELECT')) or
	(tblc.fieldCodeArea = 'Professional Licenses' and tblc.expression in ('datepart','datediff')) or
	(tblc.fieldCodeArea = 'Member Data' and tblc.expression in ('datepart','datediff')) or
	(tblc.fieldCodeArea = 'Member Data' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'INTEGER') or
	(tblc.fieldCodeArea = 'Districting' and tblc.expression in ('eq','neq'))
	union all
select cv.conditionID, cv.conditionKeyID, cv.conditionValue, null, null, null, null
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'STRING' and tblc.displayTypeCode not in ('RADIO','SELECT')) or
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('lt','lte','gt','gte','contains','contains_regex') and tblc.dataTypeCode = 'STRING') or
	(tblc.fieldCodeArea = 'Addresses' and tblc.expression in ('eq','neq') and tblc.displayTypeCode not in ('RADIO','SELECT')) or
	(tblc.fieldCodeArea = 'Addresses' and tblc.expression in ('lt','lte','gt','gte','contains','contains_regex') and tblc.dataTypeCode = 'STRING') or
	(tblc.fieldCodeArea = 'Professional Licenses' and tblc.expression in ('eq','neq','lt','lte','gt','gte','contains','contains_regex') and tblc.dataTypeCode = 'STRING') or
	(tblc.fieldCodeArea = 'Member Data' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'STRING') or
	(tblc.fieldCodeArea = 'Member Data' and tblc.expression in ('lt','lte','gt','gte','contains','contains_regex') and tblc.dataTypeCode = 'STRING') or
	(tblc.fieldCodeArea = 'Websites') or
	(tblc.fieldCodeArea = 'Emails') or
	(tblc.fieldCodeArea = 'Phones')
	union all
select cv.conditionID, cv.conditionKeyID, null, null, cv.conditionValue, null, null
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where (tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'BIT')
	union all
select cv.conditionID, cv.conditionKeyID, null, null, null, cv.conditionValue, null
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'DECIMAL2' and tblc.displayTypeCode not in ('RADIO','SELECT')) or
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('lt','lte','gt','gte') and tblc.dataTypeCode = 'DECIMAL2')
	union all
select cv.conditionID, cv.conditionKeyID, null, null, null, null, cv.conditionValue
from dbo.ams_virtualGroupConditionValues as cv
inner join #tblCondALL as tblC on tblC.conditionID = cv.conditionID
where
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('eq','neq') and tblc.dataTypeCode = 'DATE' and tblc.displayTypeCode not in ('RADIO','SELECT')) or
	(tblc.fieldCodeArea = 'Custom Fields' and tblc.expression in ('lt','lte','gt','gte') and tblc.dataTypeCode = 'DATE') or
	(tblc.fieldCodeArea = 'Addresses' and tblc.expression in ('eq','neq','lt','lte','gt','gte') and tblc.dataTypeCode = 'DATE') or
	(tblc.fieldCodeArea = 'Member Data' and tblc.expression in ('eq','neq','lt','lte','gt','gte') and tblc.dataTypeCode = 'DATE') or
	(tblc.fieldCodeArea = 'Professional Licenses' and tblc.expression in ('eq','neq','lt','lte','gt','gte') and tblc.dataTypeCode = 'DATE')

CREATE INDEX IX_tblCondValues_conditionID ON #tblCondValues (conditionID asc);


-- split ACCT data if necessary. This is here because several subProcs can use this data.
declare @acctCount int
select @acctCount = count(*) from #tblCondALL where fieldCodeArea = 'Accounting'
IF @acctCount > 0 BEGIN
	IF OBJECT_ID('tempdb..#tblAccSplit') IS NOT NULL
		DROP TABLE #tblAccSplit
	CREATE TABLE #tblAccSplit (conditionID int, revenueGLs varchar(max), batchDateLower datetime, batchDateUpper datetime, 
		revOrCash varchar(7), conditionValue money, conditionValueLower money, conditionValueUpper money);

	IF OBJECT_ID('tempdb..#tblAccSplitGL') IS NOT NULL
		DROP TABLE #tblAccSplitGL
	CREATE TABLE #tblAccSplitGL (conditionID int, revenueGL int);

	EXEC dbo.cache_members_populateMemberConditionCache_splitAcct
END


-- loop over subProcs to run
IF OBJECT_ID('tempdb..#cache_members_conditions_shouldbe') IS NOT NULL
	DROP TABLE #cache_members_conditions_shouldbe
CREATE TABLE #cache_members_conditions_shouldbe (memberid int, conditionID int);

declare @subProc varchar(30), @dynsql nvarchar(100)
select @subProc = min(subProc) from #tblCondALL where subProc <> ''
while @subProc is not null BEGIN
	SET @dynsql = 'EXEC dbo.cache_members_populateMemberConditionCache_' + @subProc
	EXEC sp_executesql @dynsql

	delete from #tblCondALL where subProc = @subProc

	select @subProc = min(subProc) from #tblCondALL where subProc <> '' and subProc > @subProc
END


-- Log
set @starttime2 = getdate()
INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage)
VALUES (@logTreeID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'Start changes to real tables');

-- delete member/conditions that should not be there
delete cmc
from dbo.cache_members_conditions as cmc
inner join #tblCondALLFinal as caf on caf.conditionID = cmc.conditionID
inner join #tblMembers as m on m.memberid = cmc.memberid
and not exists (
	select *
	from #cache_members_conditions_shouldbe
	where conditionID = cmc.conditionID
	and memberid = cmc.memberid
)	
		
-- insert member/conditions that should be but arent already there
insert into dbo.cache_members_conditions (memberID, conditionID)
select distinct cache.memberID, cache.conditionID
from #cache_members_conditions_shouldbe as cache
where not exists (
	select memberid, conditionid
	from dbo.cache_members_conditions
	where memberid = cache.memberid
	and conditionID = cache.conditionID
)

-- log
INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'End changes to real tables', Datediff(ms,@starttime2,getdate()));

-- return query of members affected
select distinct memberID
from #tblMembers
	
-- cleanup temp tables
IF OBJECT_ID('tempdb..#cache_members_conditions_shouldbe') IS NOT NULL
	DROP TABLE #cache_members_conditions_shouldbe
IF OBJECT_ID('tempdb..#tblCond') IS NOT NULL
	DROP TABLE #tblCond
IF OBJECT_ID('tempdb..#tblCondALL') IS NOT NULL
	DROP TABLE #tblCondALL
IF OBJECT_ID('tempdb..#tblCondALLFinal') IS NOT NULL
	DROP TABLE #tblCondALLFinal
IF OBJECT_ID('tempdb..#tblAccSplit') IS NOT NULL
	DROP TABLE #tblAccSplit
IF OBJECT_ID('tempdb..#tblAccSplitGL') IS NOT NULL
	DROP TABLE #tblAccSplitGL
IF OBJECT_ID('tempdb..#tblCondValues') IS NOT NULL
	DROP TABLE #tblCondValues
IF OBJECT_ID('tempdb..#tblMembers') IS NOT NULL
	DROP TABLE #tblMembers

-- Log
set @totalMS = Datediff(ms,@starttime,getdate())
INSERT INTO platformQueue.dbo.sb_ServiceBrokerLogs (LogTreeID, itemGroupUID, RunningProc, ErrorMessage, totalMS)
VALUES (@logTreeID, @itemGroupUID, OBJECT_NAME(@@PROCID), 'End Process of orgID=' + cast(@orgID as varchar(10)) + ' conditionID=' + left(@conditionIDList,100) + ' memberID=' + left(@memberIDList,100), @totalMS);

set nocount off

GO

/* (3) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_MA_EQ') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_MA_EQ
GO

/* (4) --------------------------------------------------------------------------------------------------------------------- */


IF OBJECT_ID('cache_members_populateMemberConditionCache_MA_EQ_STRING') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_MA_EQ_STRING
GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_MA_EQ_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MA_EQ_STRING'
and tblc.displayTypeCode not in ('RADIO','SELECT')
and case tblc.fieldCodeAreaPartA
	when 'address1' then isnull(ma.address1,'') 
	when 'address2' then isnull(ma.address2,'') 
	when 'address3' then isnull(ma.address3,'') 
	when 'city' then isnull(ma.city,'') 
	when 'postalcode' then isnull(ma.postalcode,'') 
	when 'county' then isnull(ma.county,'') 
	else isnull(ma.attn,'') end = cv.conditionValueString

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MA_EQ_STRING'
and tblc.displayTypeCode in ('RADIO','SELECT')
and case tblc.fieldCodeAreaPartA
	when 'stateprov' then isnull(ma.stateid,0) 
	else isnull(ma.countryid,0) end = cv.conditionValueInteger

GO

/* (5) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_MA_GT') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_MA_GT
GO

/* (6) --------------------------------------------------------------------------------------------------------------------- */


IF OBJECT_ID('cache_members_populateMemberConditionCache_MA_GT_STRING') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_MA_GT_STRING
GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_MA_GT_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MA_GT_STRING'
and case tblc.fieldCodeAreaPartA
	when 'address1' then isnull(ma.address1,'') 
	when 'address2' then isnull(ma.address2,'') 
	when 'address3' then isnull(ma.address3,'') 
	when 'city' then isnull(ma.city,'') 
	when 'postalcode' then isnull(ma.postalcode,'') 
	when 'county' then isnull(ma.county,'') 
	else isnull(ma.attn,'') end > cv.conditionValueString

GO

/* (7) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_MA_GTE') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_MA_GTE
GO

/* (8) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_MA_GTE_STRING') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_MA_GTE_STRING
GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_MA_GTE_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MA_GTE_STRING'
and case tblc.fieldCodeAreaPartA
	when 'address1' then isnull(ma.address1,'') 
	when 'address2' then isnull(ma.address2,'') 
	when 'address3' then isnull(ma.address3,'') 
	when 'city' then isnull(ma.city,'') 
	when 'postalcode' then isnull(ma.postalcode,'') 
	when 'county' then isnull(ma.county,'') 
	else isnull(ma.attn,'') end >= cv.conditionValueString

GO


/* (9) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_MA_LT') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_MA_LT
GO

/* (10) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_MA_LT_STRING') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_MA_LT_STRING
GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_MA_LT_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MA_LT_STRING'
and case tblc.fieldCodeAreaPartA
	when 'address1' then isnull(ma.address1,'') 
	when 'address2' then isnull(ma.address2,'') 
	when 'address3' then isnull(ma.address3,'') 
	when 'city' then isnull(ma.city,'') 
	when 'postalcode' then isnull(ma.postalcode,'') 
	when 'county' then isnull(ma.county,'') 
	else isnull(ma.attn,'') end < cv.conditionValueString

GO

/* (11) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_MA_LTE') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_MA_LTE
GO

/* (12) --------------------------------------------------------------------------------------------------------------------- */


IF OBJECT_ID('cache_members_populateMemberConditionCache_MA_LTE_STRING') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_MA_LTE_STRING
GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_MA_LTE_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MA_LTE_STRING'
and case tblc.fieldCodeAreaPartA
	when 'address1' then isnull(ma.address1,'') 
	when 'address2' then isnull(ma.address2,'') 
	when 'address3' then isnull(ma.address3,'') 
	when 'city' then isnull(ma.city,'') 
	when 'postalcode' then isnull(ma.postalcode,'') 
	when 'county' then isnull(ma.county,'') 
	else isnull(ma.attn,'') end <= cv.conditionValueString

GO

/* (13) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_MA_NEQ') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_MA_NEQ
GO

/* (14) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_MA_NEQ_STRING') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_MA_NEQ_STRING
GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_MA_NEQ_STRING
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberID = m.memberID
where tblc.subProc = 'MA_NEQ_STRING'
and tblc.displayTypeCode not in ('RADIO','SELECT')
and not exists (
	select ma.memberid
	from dbo.ams_memberAddresses as ma
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	where ma.memberid = m.memberid
	and ma.addressTypeID = tblc.fieldCodeAreaID
	and tblc.displayTypeCode not in ('RADIO','SELECT')
	and case tblc.fieldCodeAreaPartA
		when 'address1' then isnull(ma.address1,'') 
		when 'address2' then isnull(ma.address2,'') 
		when 'address3' then isnull(ma.address3,'') 
		when 'city' then isnull(ma.city,'') 
		when 'postalcode' then isnull(ma.postalcode,'') 
		when 'county' then isnull(ma.county,'') 
		else isnull(ma.attn,'') end = cv.conditionValueString
)

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberID = m.memberID
where tblc.subProc = 'MA_NEQ_STRING'
and tblc.displayTypeCode in ('RADIO','SELECT')
and not exists (
	select ma.memberid
	from dbo.ams_memberAddresses as ma
	inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
	where ma.memberid = m.memberid
	and ma.addressTypeID = tblc.fieldCodeAreaID
	and tblc.displayTypeCode in ('RADIO','SELECT')
	and case tblc.fieldCodeAreaPartA
		when 'stateprov' then isnull(ma.stateid,0) 
		else isnull(ma.countryid,0) end = cv.conditionValueInteger
)

GO


/* (15) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_MA_NEQ_DATE') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_MA_NEQ_DATE
GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_MA_NEQ_DATE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblMembers as m on m.memberID = m.memberID
where tblc.subProc = 'MA_NEQ_DATE'
and ma.datelastupdated <> cv.conditionValueDate
GO

/* (16) --------------------------------------------------------------------------------------------------------------------- */


IF OBJECT_ID('cache_members_populateMemberConditionCache_MA_LTE_DATE') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_MA_LTE_DATE
GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_MA_LTE_DATE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MA_LTE_DATE'
and (ma.datelastupdated < cv.conditionValueDate
	OR dateDiff(dd, ma.datelastupdated, cv.conditionValueDate) = 0)
GO

/* (17) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_MA_LT_DATE') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_MA_LT_DATE
GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_MA_LT_DATE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MA_LT_DATE'
and ma.datelastupdated < cv.conditionValueDate
GO

/* (18) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_MA_GTE_DATE') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_MA_GTE_DATE
GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_MA_GTE_DATE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MA_GTE_DATE'
and ma.datelastupdated >= cv.conditionValueDate
GO

/* (19) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_MA_GT_DATE') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_MA_GT_DATE
GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_MA_GT_DATE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MA_GT_DATE'
and ma.datelastupdated > cv.conditionValueDate
GO


/* (20) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_MA_EQ_DATE') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_MA_EQ_DATE
GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_MA_EQ_DATE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join #tblCondValues as cv on cv.conditionID = tblc.conditionID
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MA_EQ_DATE'
and dateDiff(dd, ma.datelastupdated, cv.conditionValueDate) = 0
GO

/* (21) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_MA_EXISTS_DATE') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_MA_EXISTS_DATE
GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_MA_EXISTS_DATE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberid = ma.memberid
where tblc.subProc = 'MA_EXISTS_DATE'
and ma.datelastupdated  is not null
GO

/* (22) --------------------------------------------------------------------------------------------------------------------- */

IF OBJECT_ID('cache_members_populateMemberConditionCache_MA_NOTEXISTS_DATE') IS NOT NULL
	DROP PROCEDURE cache_members_populateMemberConditionCache_MA_NOTEXISTS_DATE
GO

CREATE PROC dbo.cache_members_populateMemberConditionCache_MA_NOTEXISTS_DATE
AS

insert into #cache_members_conditions_shouldbe
select distinct m.memberid, tblc.conditionID
from #tblCondALL as tblc
inner join dbo.ams_memberAddresses as ma on ma.addressTypeID = tblc.fieldCodeAreaID
inner join #tblMembers as m on m.memberID = m.memberID
where tblc.subProc = 'MA_NOTEXISTS_DATE'
and ma.datelastupdated  is null
GO

