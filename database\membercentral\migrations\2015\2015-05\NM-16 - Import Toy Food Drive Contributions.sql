use membercentral
GO

/* ****************** */
/* SET VARIABLES HERE */
/* ****************** */
DECLARE @sitecode varchar(10), @payProfileCode varchar(20), @importFile varchar(400), @pathToExport varchar(400)

set @sitecode = 'NM'
set @payProfileCode = 'ImportedPayments'
set @importFile = 'c:\temp\ToyFoodDrive.txt'
set @pathToExport = 'c:\temp\'
/* ****************** */


/* ********************************************* */
/* the code below should not need to be modified */
/* ********************************************* */
SET NOCOUNT ON

declare @orgID int, @siteID int, @importResult xml, @flatfile varchar(400), @recordedByMemberID int, @qry varchar(400)
select @orgID = orgID, @siteID = siteID from dbo.sites where sitecode = @siteCode
select @recordedByMemberID = memberID from dbo.ams_members where memberNumber = 'SYSTEM' and orgID = 1

IF OBJECT_ID('tempdb..##tmpAccImport') IS NOT NULL 
	DROP TABLE ##tmpAccImport
IF OBJECT_ID('tempdb..##tmpAccImport2') IS NOT NULL 
	DROP TABLE ##tmpAccImport2

CREATE TABLE ##tmpAccImport (
	[saleDescription] [varchar](200) NULL, [saleDate] [datetime] NULL, [saleMemberID] [varchar](200) NULL,
	[saleAmount] [varchar](200) NULL, [saleRevenueGL] [varchar](200) NULL, [saleID] int NULL
)

BEGIN TRY
	SELECT @qry = 'BULK INSERT ##tmpAccImport FROM ''' + @importFile + ''' WITH (FIELDTERMINATOR = ''' + char(9) + ''', FIRSTROW = 2);'
	EXEC(@qry)

	select *, ROW_NUMBER() OVER (ORDER BY saleID) as rowID
	into ##tmpAccImport2 
	from ##tmpAccImport

	IF OBJECT_ID('tempdb..##tmpAccImport') IS NOT NULL 
		DROP TABLE ##tmpAccImport

	set @importResult = null
	EXEC dbo.tr_importTransactions_toTemp @orgid=@orgID, @payProfileCode=@payProfileCode, @tmptbl='##tmpAccImport2', @pathToExport=@pathToExport, @importResult=@importResult OUTPUT

	-- if no errors, run accounting import
	IF @importResult.value('count(/import/errors/error)','int') = 0
	BEGIN
		SELECT @flatfile = @importResult.value('(/import/@flatfile)[1]','varchar(160)')
		set @importResult = null
		EXEC dbo.tr_importTransactions_toPerm @orgID=@orgID, @siteID=@siteID, @recordedByMemberID=@recordedByMemberID, @statsSessionID=null, @flatfile=@flatfile, @importResult=@importResult OUTPUT
	END

	select @importResult	
	declare @errList varchar(max)
	select @errList = COALESCE(@errList + char(13)+char(10),'') + err.value('(@msg)[1]', 'varchar(max)')
		FROM @importResult.nodes('/import/errors/error') AS ref(err)
	IF len(@errList) > 0
		print 'Encountered errors running the import.' + char(13)+char(10) + @errList

END TRY
BEGIN CATCH
	IF OBJECT_ID('tempdb..##tmpAccImport') IS NOT NULL 
		DROP TABLE ##tmpAccImport
	IF OBJECT_ID('tempdb..##tmpAccImport2') IS NOT NULL 
		DROP TABLE ##tmpAccImport2
	EXEC dbo.up_errorHandler
END CATCH

IF OBJECT_ID('tempdb..##tmpAccImport') IS NOT NULL 
	DROP TABLE ##tmpAccImport
IF OBJECT_ID('tempdb..##tmpAccImport2') IS NOT NULL 
	DROP TABLE ##tmpAccImport2
print 'end of import.'

SET NOCOUNT OFF
