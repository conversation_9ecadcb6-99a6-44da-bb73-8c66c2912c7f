CREATE TRIGGER lyrRecipientTrackingUpdateTrig 
ON lyrRecipientTracking 
AFTER UPDATE AS 
BEGIN 
	SET NOCOUNT ON 

	INSERT INTO lyrUnsummarizedRecips (MailingID, MemberID, UniqueOpen, UniqueClick, UniqueStream, UniqueTrans, ForwardCount, OpenCount, ClickCount, StreamCount, TransCount, UnitCount, SubAmount ) 
		SELECT i.MailingID,  ISNULL(i.MemberID, d.MemberID), 
				CASE WHEN i.OpenCount > 0 AND d.OpenCount = 0 THEN 1 ELSE 0 END, 
				CASE WHEN i.ClickCount > 0 AND d.ClickCount = 0 THEN 1 ELSE 0 END, 
				CASE WHEN i.StreamCount > 0 AND d.StreamCount = 0 THEN 1 ELSE 0 END, 
				CASE WHEN i.TransCount > 0 AND d.TransCount = 0 THEN 1 ELSE 0 END, 
				CASE WHEN (i.ForwardCount - d.ForwardCount) > 254 THEN 254 ELSE (i.ForwardCount - d.ForwardCount) END, 				i.OpenCount - d.OpenCount, 
				i.ClickCount - d.ClickCount, 
				i.StreamCount - d.StreamCount, 
				i.TransCount - d.TransCount, 
				i.UnitCount - d.UnitCount, 
				i.SubAmount - d.SubAmount 
			FROM inserted i 
				JOIN deleted d ON i.MemberID = d.MemberID AND i.MailingID = d.MailingID 
	SET NOCOUNT OFF 
END
ALTER TABLE [dbo].[lyrRecipientTracking] ENABLE TRIGGER [lyrRecipientTrackingUpdateTrig]
GO
