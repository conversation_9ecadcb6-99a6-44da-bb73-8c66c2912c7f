use seminarweb
GO

ALTER TABLE dbo.tblSeminarsSWLive ADD codeSpeaker varchar(20) NULL;
GO
ALTER PROC [dbo].[swl_getSeminar]
@seminarID int

AS

select top 1 'SWL' as SWType, s.seminarName, s.seminarDesc, s.isPublished, s.offerCertificate, 
	s.allowRegistrants, s.isPriceBasedOnActual, s.productCode, s.seminarDescText, 
	swl.liveID, swl.seminarID, swl.swlTypeID, swl.gotoMeetingID, swl.agenda, swl.dateStart, swl.dateEnd, 
	swl.wddxTimeZones, swl.programPassword, swl.lineProvider, swl.phoneAdmin, swl.phoneAttendee, swl.codeAdmin, 
	swl.codeAttendee, swl.codeSpeaker, swl.surveyLink, swl.offerCredit, swl.offerDVD, swl.sendCountToPremiere, 
	swl.premiereUsePIN, swl.premiereConfID, swl.priceSyndication, swl.isOpen, swl.isNATLE, swl.materialsLink
from dbo.tblSeminars s
inner join dbo.tblSeminarsSWLive swl on swl.seminarID = s.seminarID
where s.seminarID = @seminarID
AND s.isDeleted = 0
GO

