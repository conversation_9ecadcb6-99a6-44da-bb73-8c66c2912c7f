ALTER PROC dbo.lists_listEngagementReportExport
@orgcode varchar(10),
@startDate datetime,
@endDate datetime,
@filename varchar(800)

AS

IF OBJECT_ID('tempdb..#tmpListEngagementExport') IS NOT NULL 
	DROP TABLE #tmpListEngagementExport;

select lf.orgcode, lf.subjecttag, l.name_, l.creatStamp_, l.descShort_, 0 as numTotalMembers, 0 as numNonSenders, 
	0 as [Original Message senders], 0 as [Reply Message Senders], 0 as [Original Message Count], 
	0 as [Reply Message Count]
into #tmpListEngagementExport
from trialslyris1.dbo.lists_ l
inner join trialslyris1.dbo.lists_format lf on l.name_ = lf.name COLLATE Latin1_General_CI_AI
	and l.adminSend_ = 'F'
	and lf.orgcode = @orgcode;

UPDATE tmp
set tmp.numTotalMembers = (select count(*) from trialslyris1.dbo.members_ where list_ = tmp.name_ COLLATE Latin1_General_CI_AI and membertype_ in ('normal','held'))
FROM #tmpListEngagementExport as tmp;

UPDATE tmp
set tmp.numNonSenders = 
	(
		select count(*)
		from trialslyris1.dbo.members_
		where list_ = tmp.name_ COLLATE Latin1_General_CI_AI
		and membertype_ in ('normal','held')
		and emailaddr_ not in (
			select hdrfromspc_ COLLATE Latin1_General_CI_AI
			from dbo.messages_ m
			inner join dbo.messageLists ml on m.listID = ml.listID and m.isVisible=1
				and ml.list = tmp.name_ COLLATE Latin1_General_CI_AI
				and m.creatStamp_ between @startdate and @enddate
		) 
	)
FROM #tmpListEngagementExport as tmp;

UPDATE tmp
set tmp.[Original Message senders] = 
	(
		select count(distinct hdrfromspc_)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID and m.isVisible=1
			and ml.list = tmp.name_ COLLATE Latin1_General_CI_AI
			and m.creatStamp_ between @startdate and @enddate
			and parentID_ = messageID_
	)
FROM #tmpListEngagementExport as tmp;

UPDATE tmp
set tmp.[Reply Message Senders] = 
	(
		select count(distinct hdrfromspc_)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID and m.isVisible=1
			and ml.list = tmp.name_ COLLATE Latin1_General_CI_AI
			and m.creatStamp_ between @startdate and @enddate
			and parentID_ <> messageID_
	)
FROM #tmpListEngagementExport as tmp;

UPDATE tmp
set tmp.[Original Message Count] = 
	(
		select count(*)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID and m.isVisible=1
			and ml.list = tmp.name_ COLLATE Latin1_General_CI_AI
			and m.creatStamp_ between @startdate and @enddate
			and parentID_ = messageID_
	)
FROM #tmpListEngagementExport as tmp;

UPDATE tmp
set tmp.[Reply Message Count] = 
	(
		select count(*)
		from dbo.messages_ m
		inner join dbo.messageLists ml on m.listID = ml.listID and m.isVisible=1
			and ml.list = tmp.name_ COLLATE Latin1_General_CI_AI
			and m.creatStamp_ between @startdate and @enddate
			and parentID_ <> messageID_
	)
FROM #tmpListEngagementExport as tmp;

EXEC dbo.up_exportCSV @csvfilename=@fileName, @sql='select * from #tmpListEngagementExport order by orgcode, name_';

IF OBJECT_ID('tempdb..#tmpListEngagementExport') IS NOT NULL 
	DROP TABLE #tmpListEngagementExport;
GO
