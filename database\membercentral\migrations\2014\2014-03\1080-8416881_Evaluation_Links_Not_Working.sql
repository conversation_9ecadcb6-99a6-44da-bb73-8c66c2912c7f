declare @SWLCode char(10)

-- http://ce.iacprx.org/seminarweb/?k=TCXGXMEXWY

-- http://ce.iacprx.org/seminarweb/?k=GWDTKXUTPH

-- http://ce.iacprx.org/seminarweb/?k=KWUDXYNHTM

set @SWLCode = 'KWUDXYNHTM'

/*
SELECT TOP 1 e.enrollmentID, p.catalogURL, sswl.gotoMeetingID, eswl.goToMeetingUID, eswl.SWLCode
FROM dbo.tblEnrollments AS e 
INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
INNER JOIN dbo.tblSeminars AS s ON e.seminarID = s.seminarID 
INNER JOIN dbo.tblSeminarsSWLive AS sswl ON s.seminarID = sswl.seminarID 
WHERE eswl.SWLCode = @SWLCode
AND LEN(eswl.SWLCode) > 0
--AND <PERSON>EN(eswl.goToMeetingUID) > 0
AND e.isActive = 1

sp_help tblEnrollmentsSWLive
*/

update tblEnrollmentsSWLive 
set goToMeetingUID = '1'
where  SWLCode = @SWLCode


select * from tblEnrollmentsSWLive where  SWLCode = @SWLCode