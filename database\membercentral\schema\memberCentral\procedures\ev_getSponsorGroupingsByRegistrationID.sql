ALTER PROC dbo.ev_getSponsorGroupingsByRegistrationID
@registrationID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SELECT sponsorGroupingID, sponsorGrouping, sponsorGroupingOrder
	FROM dbo.ev_sponsorGrouping
	WHERE registrationID = @registrationID
	ORDER BY sponsorGroupingOrder;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
