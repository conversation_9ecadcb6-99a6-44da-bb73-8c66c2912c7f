ALTER PROC dbo.sb_freeDialogFromPool
@dialogHandle UNIQUEIDENTIFIER,
@sendCount BIGINT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- Return the dialog to the pool.
	DECLARE @rowcount INT, @string VARCHAR(50);

	BEGIN TRAN;
		-- Release dialog by setting OwnerSPID to -1.
		UPDATE dbo.sb_dialogPool 
		SET OwnerSPID = -1, 
			SendCount = @sendCount 
		WHERE Handle = @dialogHandle;

		SELECT @rowcount = @@ROWCOUNT;

		IF @rowcount = 0 BEGIN
			SET @string = (SELECT CAST(@dialogHandle AS VARCHAR(50)));
			RAISERROR('sb_freeDialogFromPool: dialog %s not found in dialog pool', 16, 1, @string) WITH LOG;
		END
		IF @rowcount > 1 BEGIN
			SET @string = (SELECT CAST(@dialogHandle AS VARCHAR(50)));
			RAISERROR('sb_freeDialogFromPool: duplicate dialog %s found in dialog pool', 16, 1, @string) WITH LOG;
		END
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO
