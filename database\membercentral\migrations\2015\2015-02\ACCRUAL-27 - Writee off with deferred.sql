use membercentral
GO
ALTER FUNCTION [dbo].[fn_tr_getAllocatedPaymentsofSale] (
	@saleTransactionID int
) 
RETURNS @tblPayments TABLE (
	paymentTransactionID int,
	allocatedAmount money
)
AS
BEGIN

	DECLARE @tblHold TABLE (
		saleTransactionID int,	
		transactionID int
	)

	-- sale	
	INSERT INTO @tblHold
	select transactionID, transactionID
	from dbo.tr_transactions
	WHERE transactionID = @saleTransactionID
	AND typeID in (1,7,10)
	AND statusID = 1

	-- adj
	INSERT INTO @tblHold
	select distinct tbl.saleTransactionID, adj.transactionID
	from dbo.tr_transactions as adj
	inner join dbo.tr_relationships as r on r.transactionID = adj.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
	inner join @tblHold as tbl on tbl.saleTransactionID = r.appliedToTransactionID
	where adj.statusID = 1

	-- dit
	INSERT INTO @tblHold
	select distinct tbl.saleTransactionID, dit.transactionID
	from dbo.tr_transactions as dit
	inner join dbo.tr_relationships as r on r.transactionID = dit.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
	inner join @tblHold as tbl on tbl.transactionID = r.appliedToTransactionID
	where dit.statusID = 1


	insert into @tblPayments
	select paymentTransactionID, sum(allocAmt)
	from (
		select tPay.transactionID as paymentTransactionID, CASE WHEN glAllocDeb.GLCode = 'ACCOUNTSRECEIVABLE' then tAlloc.amount*-1 ELSE tAlloc.amount END as allocAmt
		from @tblHold as tbl
		inner join dbo.tr_relationships as rS on rS.appliedToTransactionID = tbl.transactionID
		inner join dbo.tr_relationshipTypes as rtS on rtS.typeID = rS.typeID and rtS.type = 'AllocSaleTrans'
		inner join dbo.tr_transactions as tAlloc on tAlloc.transactionID = rS.transactionID and tAlloc.statusID = 1
		inner join dbo.tr_GLAccounts as glAllocDeb on glAllocDeb.GLAccountID = tAlloc.debitGLAccountID
		inner join dbo.tr_relationships as rP on rP.transactionID = tAlloc.transactionID
		inner join dbo.tr_relationshipTypes as rtP on rtP.typeID = rP.typeID and rtP.type = 'AllocPayTrans'
		inner join dbo.tr_transactions as tPay on tPay.transactionID = rP.appliedToTransactionID and tPay.statusID in (1,3)
	) as tmp
	group by paymentTransactionID
	having sum(allocAmt) > 0

	RETURN

END
GO

ALTER FUNCTION [dbo].[fn_tr_salesWithAmountDue] (
	@memberid int,
	@orgid int
)
RETURNS @tblSales TABLE (
	transactionID int,
	cache_amountAfterAdjustment money,
	activePaymentAllocatedAmount money,
	pendingPaymentAllocatedAmount money,
	paymentDueAmount money,
	cache_amountAfterAdjustmentRecog money,
	activePaymentAllocatedAmountRecog money,
	pendingPaymentAllocatedAmountRecog money,
	paymentDueAmountRecog money
)
AS
BEGIN

	declare @tblHold TABLE (saleTransactionID int, transactionID int)

	-- sales
	INSERT INTO @tblHold
	select t.transactionID, t.transactionID
	from dbo.tr_transactions as t
	inner join dbo.ams_members as m on m.memberid = t.assignedToMemberID
	WHERE t.ownedByOrgID = @orgid
	and t.typeID = 1
	and t.statusID = 1
	and m.activeMemberID = @memberid

	-- tax
	INSERT INTO @tblHold
	select distinct tbl.saletransactionID, tax.transactionID
	from dbo.tr_transactions as tax
	inner join dbo.tr_relationships as r on r.transactionID = tax.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'SalesTaxTrans'
	inner join @tblHold as tbl on tbl.saleTransactionID = r.appliedToTransactionID
	where tax.statusID = 1

	-- adj of sales
	INSERT INTO @tblHold
	select distinct tbl.saletransactionID, adj.transactionID
	from dbo.tr_transactions as adj
	inner join dbo.tr_relationships as r on r.transactionID = adj.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
	inner join @tblHold as tbl on tbl.saleTransactionID = r.appliedToTransactionID
	where adj.statusID = 1

	-- adj of tax
	INSERT INTO @tblHold
	select distinct tbl.saletransactionID, adj.transactionID
	from dbo.tr_transactions as adj
	inner join dbo.tr_relationships as r on r.transactionID = adj.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
	inner join dbo.tr_relationships as r2 on r2.transactionID = r.appliedToTransactionID
	inner join dbo.tr_relationshipTypes as rt2 on rt2.typeID = r2.typeID and rt2.type = 'SalesTaxTrans'
	inner join @tblHold as tbl on tbl.saletransactionID = r2.appliedToTransactionID
	where adj.statusID = 1

	-- dit of sale
	INSERT INTO @tblHold
	select distinct tbl.saletransactionID, dit.transactionID
	from dbo.tr_transactions as dit
	inner join dbo.tr_relationships as r on r.transactionID = dit.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
	inner join @tblHold as tbl on tbl.SaletransactionID = r.appliedToTransactionID
	where dit.statusID = 1

	-- dit of adj of sale
	INSERT INTO @tblHold
	select distinct tbl.saletransactionID, dit.transactionID
	from dbo.tr_transactions as dit
	inner join dbo.tr_relationships as r on r.transactionID = dit.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
	inner join dbo.tr_relationships as r2 on r2.transactionID = r.appliedToTransactionID
	inner join dbo.tr_relationshipTypes as rt2 on rt2.typeID = r2.typeID and rt2.type = 'AdjustTrans'
	inner join @tblHold as tbl on tbl.SaletransactionID = r2.appliedToTransactionID
	where dit.statusID = 1

	-- dit of adj of tax
	INSERT INTO @tblHold
	select distinct tbl.saletransactionID, dit.transactionID
	from dbo.tr_transactions as dit
	inner join dbo.tr_relationships as r on r.transactionID = dit.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
	inner join dbo.tr_relationships as r2 on r2.transactionID = r.appliedToTransactionID
	inner join dbo.tr_relationshipTypes as rt2 on rt2.typeID = r2.typeID and rt2.type = 'AdjustTrans'
	inner join dbo.tr_relationships as r3 on r3.transactionID = r2.appliedToTransactionID
	inner join dbo.tr_relationshipTypes as rt3 on rt3.typeID = r3.typeID and rt3.type = 'SalesTaxTrans'
	inner join @tblHold as tbl on tbl.SaletransactionID = r3.appliedToTransactionID
	where dit.statusID = 1

	-- dit of tax
	INSERT INTO @tblHold
	select distinct tbl.saletransactionID, dit.transactionID
	from dbo.tr_transactions as dit
	inner join dbo.tr_relationships as r on r.transactionID = dit.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
	inner join dbo.tr_relationships as r2 on r2.transactionID = r.appliedToTransactionID
	inner join dbo.tr_relationshipTypes as rt2 on rt2.typeID = r2.typeID and rt2.type = 'SalesTaxTrans'
	inner join @tblHold as tbl on tbl.transactionID = r2.appliedToTransactionID
	where dit.statusID = 1

	insert into @tblSales	
	select tbl.saleTransactionID as transactionID, 
		sum(ts.cache_amountAfterAdjustment) as cache_amountAfterAdjustment,
		sum(ts.cache_activePaymentAllocatedAmount) as cache_activePaymentAllocatedAmount,
		sum(ts.cache_pendingPaymentAllocatedAmount) as cache_pendingPaymentAllocatedAmount,
		sum(ts.cache_amountAfterAdjustment) - sum(ts.cache_activePaymentAllocatedAmount) as paymentDueAmount,
		sum(case when t.typeID = 10 then 0 else ts.cache_amountAfterAdjustment end) as cache_amountAfterAdjustmentRecog,
		sum(case when t.typeID = 10 then 0 else ts.cache_activePaymentAllocatedAmount end) as cache_activePaymentAllocatedAmountRecog,
		sum(case when t.typeID = 10 then 0 else ts.cache_pendingPaymentAllocatedAmount end) as cache_pendingPaymentAllocatedAmountRecog,
		sum(case when t.typeID = 10 then 0 else ts.cache_amountAfterAdjustment end) - sum(case when t.typeID = 10 then 0 else ts.cache_activePaymentAllocatedAmount end) as paymentDueAmountRecog
	from @tblHold as tbl
	inner join dbo.tr_transactions as t on t.transactionID = tbl.transactionID
	inner join dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID	
	group by tbl.saleTransactionID
	having sum(ts.cache_amountAfterAdjustment) - sum(ts.cache_activePaymentAllocatedAmount) > 0

	RETURN
END
GO

ALTER PROC [dbo].[tr_writeOffSale]
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@amount money,
@transactionDate datetime,
@saleTransactionID int

AS

SET NOCOUNT ON

-- setup transaction
DECLARE @TranCounter int, @ProcName varchar(max);
SET @TranCounter = @@TRANCOUNT;
SET @ProcName = OBJECT_NAME(@@PROCID);
IF @TranCounter > 0
	SAVE TRAN @ProcName;
ELSE
	BEGIN TRAN;

BEGIN TRY

	-- amt needs to be positive.
	IF @amount <= 0
		RAISERROR('amount is not greater than 0', 16, 1);

	-- dont assume memberid is the active one. get the active one.
	select @recordedByMemberID = activeMemberID
		from dbo.ams_members WITH(NOLOCK)
		where memberID = @recordedByMemberID 

	-- ensure amount is 2 decimals
	select @amount = cast(@amount as decimal(10,2))

	-- use numeric(10,2) instead of money to prevent 4 digit money values
	-- get sale, tax, and adjustments but only consider recognized money
	declare @tblOpenTran TABLE (autoid int IDENTITY(1,1), transactionid int, maxAmtToWriteOff numeric(10,2), PITSaleTID int, PITWOAmt numeric(10,2), newWOTID int)

	;with innerTbl as (
		select t.transactionID
		from dbo.tr_transactions as t
		inner join dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID
		where t.typeID = 1
		and t.statusID = 1
		and t.transactionID = @saleTransactionID
			union all
		select t.transactionID
		from dbo.tr_transactions as t
		inner join dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID
		inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'SalesTaxTrans'
		inner join innerTbl on innerTbl.transactionID = r.appliedToTransactionID
		where t.typeID = 7
		and t.statusID = 1
			union all
		select t.transactionID
		from dbo.tr_transactions as t
		inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
		inner join innerTbl on innerTbl.transactionID = r.appliedToTransactionID
		where t.typeID = 3
		and t.statusID = 1
	), outerTbl AS (
		select it.transactionID, it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount as maxAmtToWriteOff
		from dbo.tr_invoiceTransactions as it
		inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
		inner join innerTbl on innerTbl.transactionID = it.transactionid
	)
	insert into @tblOpenTran (transactionID, maxAmtToWriteOff)
	select transactionID, sum(maxAmtToWriteOff)
	from (
		select transactionID, maxAmtToWriteOff
		from outerTbl
			union all
		select r.appliedToTransactionID, (ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount-ts.cache_pendingPaymentAllocatedAmount)*-1
		from dbo.tr_transactions as t
		inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
		inner join outerTbl on r.appliedToTransactionID = outerTbl.transactionID
		inner join dbo.tr_transactionDIT as dit on dit.transactionID = t.transactionID and dit.isActive = 1
		inner join dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID
		where t.statusID = 1
	) as tmp
	group by transactionID
	having sum(maxAmtToWriteOff) > 0
	order by transactionID

	-- group by PITTaxTrans. sales and adj to sales just put the transactionid
	update tbl
	set tbl.PITSaleTID = tbl.transactionID
	from @tblOpenTran as tbl
	inner join dbo.tr_transactions as t on t.transactionID = tbl.transactionID and t.typeID = 1

	update tbl
	set tbl.PITSaleTID = tbl.transactionID
	from @tblOpenTran as tbl
	inner join dbo.tr_transactions as t on t.transactionID = tbl.transactionID and t.typeID = 3
	inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
	inner join dbo.tr_transactions as tSale on tSale.transactionID = r.appliedToTransactionID and tSale.typeID = 1
	where tbl.PITSaleTID is null

	update tbl
	set tbl.PITSaleTID = tbl2.transactionID
	from @tblOpenTran as tbl
	inner join dbo.tr_relationships as r on r.transactionID = tbl.transactionID
	inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'PITTaxTrans'
	inner join @tblOpenTran as tbl2 on tbl2.transactionID = r.appliedToTransactionID
	where tbl.PITSaleTID is null

	-- did we miss any PITTaxTrans records?
	IF EXISTS (select autoid from @tblOpenTran where PITSaleTID is null)
		RAISERROR('Records are missing PITSaleTID', 16, 1);

	-- loop over each group to get write off amount and % split
	declare @totalAmountLeftToWriteOff money, @grpTID int, @grpMaxAmtToWriteOff money, @w_amount money
	select @totalAmountLeftToWriteOff = @amount
	select @grpTID = min(PITSaleTID) from @tblOpenTran
	while @grpTID is not null BEGIN

		-- how much can we writeoff to this group of transactions?
		select @grpMaxAmtToWriteOff = sum(maxAmtToWriteOff) from @tblOpenTran where PITSaleTID = @grpTID
		IF @totalAmountLeftToWriteOff > @grpMaxAmtToWriteOff
			select @w_amount = @grpMaxAmtToWriteOff
		ELSE
			select @w_amount = @totalAmountLeftToWriteOff

		-- calc amount to write off for each transaction
		update @tblOpenTran
		set PITWOAmt = case 
						when @w_amount = 0 then 0 
						else ((maxAmtToWriteOff/ @grpMaxAmtToWriteOff) * @w_amount) 
						end
		WHERE PITSaleTID = @grpTID

		-- handle remainders from percentage calculations
		-- put any diff in the 1st item that can accept it
		declare @sumWriteOffAmount numeric(10,2), @amountDiff numeric(10,2)
		select @sumWriteOffAmount = sum(PITWOAmt) from @tblOpenTran WHERE PITSaleTID = @grpTID
		IF @sumWriteOffAmount <> abs(@w_amount) BEGIN
			select @amountDiff = abs(@w_amount) - @sumWriteOffAmount
	
			update top (1) @tblOpenTran
			set PITWOAmt = PITWOAmt + @amountDiff
			where PITWOAmt + @amountDiff <= maxAmtToWriteOff
		END

		select @totalAmountLeftToWriteOff = @totalAmountLeftToWriteOff - @w_amount
		if @totalAmountLeftToWriteOff <= 0
			break

		select @grpTID = min(PITSaleTID) from @tblOpenTran where PITSaleTID > @grpTID
	END

	-- loop over and make write offs
	declare @minAutoID int, @w_TID int, @newWriteOffTID int
	select @minAutoID = min(autoID) from @tblOpenTran where PITWOAmt > 0
	while @minAutoID is not null BEGIN
		select @w_amount=PITWOAmt, @w_TID=transactionID
			from @tblOpenTran
			where autoID = @minAutoID

		EXEC dbo.tr_createTransaction_writeoff_sale @recordedOnSiteID=@recordedOnSiteID, 
			@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, 
			@status='Active', @amount=@w_amount, @transactionDate=@transactionDate, 
			@saleTransactionID=@w_TID, @transactionID=@newWriteOffTID OUTPUT

		update @tblOpenTran
		set newWOTID = @newWriteOffTID
		where autoID = @minAutoID

		select @minAutoID = min(autoID) from @tblOpenTran where PITWOAmt > 0 and autoID > @minAutoID
	END	

	-- insert WriteOffTaxTrans relationships
	IF EXISTS (select autoid from @tblOpenTran where PITSaleTID <> transactionID) BEGIN
		INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID)
		select dbo.fn_tr_getRelationshipTypeID('WriteOffTaxTrans'), tbl.newWOTID, tbl2.newWOTID
		from @tblOpenTran as tbl
		inner join @tblOpenTran as tbl2 on tbl2.transactionID = tbl.PITSaleTID
		where tbl.PITSaleTID <> tbl.transactionID
		and tbl.newWOTID is not null
		and tbl2.newWOTID is not null
	END	


	IF @TranCounter = 0
		COMMIT TRAN;
	RETURN 0
END TRY
BEGIN CATCH
	IF @TranCounter = 0 or XACT_STATE() = -1
		ROLLBACK TRAN;
	IF @TranCounter > 0 and XACT_STATE() <> -1
		ROLLBACK TRAN @ProcName;

	EXEC dbo.up_errorhandler
	RETURN -1
END CATCH
GO

ALTER PROC [dbo].[tr_viewTransaction_sale]
@transactionID int

AS

set nocount on

declare @orgID int
select @orgID = ownedByOrgID from dbo.tr_transactions where transactionID = @transactionID

declare @allGLs TABLE (GLAccountID int, thePathExpanded varchar(max), accountCode varchar(200), accountType varchar(30), GLCode varchar(30), thePath varchar(max))
insert into @allGLS
select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode, rgl.accountType, rgl.GLCode, rgl.thePath
from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl

-- transaction info
select TOP 1 t.transactionid, t.ownedByOrgID, t.recordedOnSiteID, tt.type, t.detail, t.amount, 
	t.transactionDate, t.dateRecorded, ts.status, mAss2.memberID as assignedTomemberID, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullif(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as recordedByMember,
	m2.company as recordedByMemberCompany
from dbo.tr_transactions as t
inner join dbo.tr_types as tt on tt.typeID = t.typeID
inner join dbo.tr_statuses as ts on ts.statusID = t.statusID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join dbo.ams_members as m on m.memberid = t.recordedByMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
where t.transactionID = @transactionID

-- sale info
select top 1 ts.saleID, 
	tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount-tsFull.cache_pendingPaymentAllocatedAmount as unAllocatedAmount,
	tsFull.cache_activePaymentAllocatedAmount+tsFull.cache_pendingPaymentAllocatedAmount as allocatedAmount,
	paymentDueAmount =
		case 
		when t.statusID = 1 then (isnull(tax.cache_amountAfterAdjustment,0)+tsFull.cache_amountAfterAdjustment) - (isnull(tax.cache_activePaymentAllocatedAmount,0)+tsFull.cache_activePaymentAllocatedAmount) - (isnull(tax.cache_pendingPaymentAllocatedAmount,0)+tsFull.cache_pendingPaymentAllocatedAmount)
		else 0
		end,
	btn_canApplyPayment = 
		case 
		when t.statusID = 1 and (isnull(tax.cache_amountAfterAdjustment,0)+tsFull.cache_amountAfterAdjustment) - (isnull(tax.cache_activePaymentAllocatedAmount,0)+tsFull.cache_activePaymentAllocatedAmount) > 0 then 1 
		else 0
		end,
	btn_canWriteOff =
		case 
		when t.statusID = 1 and tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount > 0 then 1 
		else 0 
		end,
	btn_canAdjust = case when t.statusID = 1 then 1 else 0 end,
	btn_canVoid = case when t.statusID = 1 then 1 else 0 end,
	hasAdjustments = case when exists (
		select adj.transactionID
		from dbo.tr_transactions as adj
		inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = t.transactionID
		inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
		where adj.statusID = 1
		) then 1 else 0 end
from dbo.tr_transactionSales as ts
inner join dbo.tr_transactions as t on t.transactionID = ts.transactionID
cross apply dbo.fn_tr_transactionSalesWithDIT(t.transactionID) as tsFull
outer apply (
	select sum(tsTaxFull.cache_amountAfterAdjustment) as cache_amountAfterAdjustment, 
		   sum(tsTaxFull.cache_activePaymentAllocatedAmount) as cache_activePaymentAllocatedAmount, 
		   sum(tsTaxFull.cache_pendingPaymentAllocatedAmount) as cache_pendingPaymentAllocatedAmount
	from dbo.tr_relationships as tr WITH(NOLOCK)
	inner join dbo.tr_relationshiptypes as trt WITH(NOLOCK) on trt.typeID = tr.typeID and trt.type = 'SalesTaxTrans'
	inner join dbo.tr_transactions as tTax WITH(NOLOCK) on tTax.transactionID = tr.transactionID and tTax.statusID = 1
	cross apply dbo.fn_tr_transactionSalesWithDIT(tTax.transactionID) as tsTaxFull
	where tr.appliedToTransactionID = t.transactionID
	) as tax
where t.transactionID = @transactionID

-- invoices
select distinct i.invoiceID, ins.status, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, i.dateDue, i.invoiceProfileID, ip.profileName, i.invoiceCode
from dbo.tr_transactions as t
inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
inner join dbo.organizations as o on o.orgID = @orgID
where t.transactionID in (
	select @transactionID as transactionID
	union
	select adj.transactionID
	from dbo.tr_transactions as adj
	inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
	inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
)
order by i.dateDue desc, 3 desc


-- current allocations (also include writeoffs)
select t.transactionID, t.typeID, apos.allocatedAmount as allocAmount, t.amount, t.detail, t.transactionDate, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL
from dbo.fn_tr_getAllocatedPaymentsofSale(@transactionID) as apos
inner join dbo.tr_transactions as t on t.transactionID = apos.paymentTransactionID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glDeb on glDeb.GLAccountID = t.debitGLAccountID
	union all
select tWO.transactionID, tWO.typeID, tWO.amount, tWO.amount, 'WriteOff of ' + tWO.detail, tWO.transactionDate, null, null, 
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL
from dbo.tr_transactions as tWO
inner join dbo.tr_relationships as rInner on rInner.transactionID = tWO.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'WriteOffSaleTrans'
inner join dbo.ams_members as mAss on mAss.memberid = tWO.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glDeb on glDeb.GLAccountID = tWO.debitGLAccountID
where rInner.appliedToTransactionID = @transactionID
and tWO.statusID = 1
	union all
select tWO.transactionID, tWO.typeID, tWO.amount, tWO.amount, 'WriteOff of ' + tWO.detail, tWO.transactionDate, null, null, 
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL
from dbo.tr_transactions as tWO
inner join dbo.tr_relationships as rWO on rWO.transactionID = tWO.transactionID
inner join dbo.tr_relationshipTypes as rtWO on rtWO.typeID = rWO.typeID and rtWO.type = 'WriteOffSaleTrans'
inner join dbo.tr_transactions as tAdj on tAdj.transactionID = rWO.appliedToTransactionID and tAdj.typeID = 3
inner join dbo.tr_relationships as rAdj on rAdj.transactionID = tAdj.transactionID
inner join dbo.tr_relationshipTypes as rtAdj on rtAdj.typeID = rAdj.typeID and rtAdj.type = 'AdjustTrans'
inner join dbo.ams_members as mAss on mAss.memberid = tWO.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glDeb on glDeb.GLAccountID = tWO.debitGLAccountID
where rAdj.appliedToTransactionID = @transactionID
and tWO.statusID = 1
order by 6 desc


-- current gl spread
DECLARE @tblHold TABLE (transactionID int, debitglAccountID int, creditglAccountID int, amount money)
declare @tblTrans TABLE (transactionID int, glAccountID int, debitAmount money, creditAmount money)

-- sale	
INSERT INTO @tblHold
select transactionID, debitglAccountID, creditglAccountID, amount
from dbo.tr_transactions
WHERE transactionID = @transactionID
and statusID = 1

-- adj
INSERT INTO @tblHold
select distinct adj.transactionID, adj.debitglAccountID, adj.creditglAccountID, adj.amount
from dbo.tr_transactions as adj
inner join dbo.tr_relationships as r on r.transactionID = adj.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
inner join @tblHold as tbl on tbl.transactionID = r.appliedToTransactionID
where adj.statusID = 1

-- wo
INSERT INTO @tblHold
select distinct wo.transactionID, wo.debitglAccountID, wo.creditglAccountID, wo.amount
from dbo.tr_transactions as wo
inner join dbo.tr_relationships as r on r.transactionID = wo.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'WriteOffSaleTrans'
inner join @tblHold as tbl on tbl.transactionID = r.appliedToTransactionID
where wo.statusID = 1

-- dit
INSERT INTO @tblHold
select distinct dit.transactionID, dit.debitglAccountID, dit.creditglAccountID, dit.amount
from dbo.tr_transactions as dit
inner join dbo.tr_relationships as r on r.transactionID = dit.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
inner join @tblHold as tbl on tbl.transactionID = r.appliedToTransactionID
where dit.statusID = 1

insert into @tblTrans
select transactionID, debitglAccountID, amount, 0
from @tblHold
	union all
select transactionID, creditglAccountID, 0, amount
from @tblHold

select gl.thePathExpanded + isnull(' (' + nullIf(gl.accountCode,'') + ')','') as glexpanded,
	case 
	when gl.accountType = 'Cash' and sd.debitAmount - sd.creditAmount >= 0 then sd.debitAmount - sd.creditAmount
	when gl.accountType = 'Asset' and gl.GLCode = 'ACCOUNTSRECEIVABLE' and sd.debitAmount - sd.creditAmount > 0 then sd.debitAmount - sd.creditAmount
	when gl.accountType = 'Liability' and gl.GLCode = 'DEPOSITS' and sd.creditAmount - sd.debitAmount <= 0 then abs(sd.creditAmount - sd.debitAmount)
	when gl.accountType = 'Revenue' and sd.creditAmount - sd.debitAmount < 0 then abs(sd.creditAmount - sd.debitAmount)
	when gl.accountType = 'Expense' and sd.debitAmount - sd.creditAmount >= 0 then sd.debitAmount - sd.creditAmount
	when gl.accountType = 'Liability' and sd.creditAmount - sd.debitAmount <= 0 then abs(sd.creditAmount - sd.debitAmount)
	else null
	end as debits,
	case 
	when gl.accountType = 'Cash' and sd.debitAmount - sd.creditAmount < 0 then abs(sd.debitAmount - sd.creditAmount)
	when gl.accountType = 'Asset' and gl.GLCode = 'ACCOUNTSRECEIVABLE' and sd.debitAmount - sd.creditAmount <= 0 then abs(sd.debitAmount - sd.creditAmount)
	when gl.accountType = 'Liability' and gl.GLCode = 'DEPOSITS' and sd.creditAmount - sd.debitAmount > 0 then sd.creditAmount - sd.debitAmount
	when gl.accountType = 'Revenue' and sd.creditAmount - sd.debitAmount >= 0 then sd.creditAmount - sd.debitAmount
	when gl.accountType = 'Expense' and sd.debitAmount - sd.creditAmount < 0 then abs(sd.debitAmount - sd.creditAmount)
	when gl.accountType = 'Liability' and sd.creditAmount - sd.debitAmount > 0 then sd.creditAmount - sd.debitAmount
	else null
	end as credits
from (
	select glAccountID, sum(debitAmount) as debitAmount, sum(creditAmount) as creditAmount
	from @tblTrans
	group by glAccountID
	having (sum(debitAmount) > 0 OR sum(creditAmount) > 0) and sum(debitAmount) <> sum(creditAmount)
) as sd
inner join @allGLS as gl on gl.GLAccountID = sd.GLAccountID
order by gl.thePath


-- deferred schedule
select dit.recognitionDate, sum(t.amount) as recogAmt
from dbo.tr_transactions as t
inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
inner join (
	select @transactionID as transactionID
		union
	select adj.transactionID
	from dbo.tr_transactions as adj
	inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
	inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
	where adj.statusID = 1
) as tbl on tbl.transactionID = r.appliedToTransactionID
inner join dbo.tr_transactionDIT as dit on dit.transactionID = t.transactionID
where t.statusID = 1
and dit.isActive = 1
group by dit.recognitionDate
order by dit.recognitionDate

-- related transactions
EXEC dbo.tr_viewTransaction_related_recursive @transactionID

RETURN 0
GO

ALTER PROC [dbo].[tr_viewTransaction_salestax]
@transactionID int

AS

set nocount on

declare @orgID int
select @orgID = ownedByOrgID from dbo.tr_transactions where transactionID = @transactionID

declare @allGLs TABLE (GLAccountID int, thePathExpanded varchar(max), accountCode varchar(200), accountType varchar(30), GLCode varchar(30), thePath varchar(max))
insert into @allGLS
select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode, rgl.accountType, rgl.GLCode, rgl.thePath
from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl


-- transaction info
select TOP 1 t.transactionid, t.ownedByOrgID, t.recordedOnSiteID, tt.type, t.detail, t.amount, 
	t.transactionDate, t.dateRecorded, ts.status, mAss2.memberID as assignedTomemberID, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullif(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as recordedByMember,
	m2.company as recordedByMemberCompany
from dbo.tr_transactions as t
inner join dbo.tr_types as tt on tt.typeID = t.typeID
inner join dbo.tr_statuses as ts on ts.statusID = t.statusID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join dbo.ams_members as m on m.memberid = t.recordedByMemberID
inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
where t.transactionID = @transactionID


-- salestax info
select top 1 ts.saleID, 
	tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount-tsFull.cache_pendingPaymentAllocatedAmount as unAllocatedAmount,
	tsFull.cache_activePaymentAllocatedAmount+tsFull.cache_pendingPaymentAllocatedAmount as allocatedAmount,
	hasAdjustments = case when exists (
		select adj.transactionID
		from dbo.tr_transactions as adj
		inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
		inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
		where adj.statusID = 1
		) then 1 else 0 end
from dbo.tr_transactionSales as ts
cross apply dbo.fn_tr_transactionSalesWithDIT(ts.transactionID) as tsFull
where ts.transactionID = @transactionID


-- invoices
select distinct i.invoiceID, ins.status, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, i.dateDue, i.invoiceProfileID, ip.profileName, i.invoiceCode
from dbo.tr_transactions as t
inner join dbo.tr_invoiceTransactions as it on it.transactionID = t.transactionID
inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
inner join dbo.organizations as o on o.orgID = @orgID
where t.transactionID in (
	select @transactionID as transactionID
	union
	select adj.transactionID
	from dbo.tr_transactions as adj
	inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
	inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
)
order by i.dateDue desc, 3 desc


-- current allocations (also include writeoffs)
select t.transactionID, t.typeID, apos.allocatedAmount as allocAmount, t.amount, t.detail, t.transactionDate, 
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL
from dbo.fn_tr_getAllocatedPaymentsofSale(@transactionID) as apos
inner join dbo.tr_transactions as t on t.transactionID = apos.paymentTransactionID
inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glDeb on glDeb.GLAccountID = t.debitGLAccountID
	union all
select tWO.transactionID, tWO.typeID, tWO.amount, tWO.amount, 'WriteOff of ' + tWO.detail, tWO.transactionDate, null, null, 
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL
from dbo.tr_transactions as tWO
inner join dbo.tr_relationships as rInner on rInner.transactionID = tWO.transactionID
inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'WriteOffSaleTrans'
inner join dbo.ams_members as mAss on mAss.memberid = tWO.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glDeb on glDeb.GLAccountID = tWO.debitGLAccountID
where rInner.appliedToTransactionID = @transactionID
and tWO.statusID = 1
	union all
select tWO.transactionID, tWO.typeID, tWO.amount, tWO.amount, 'WriteOff of ' + tWO.detail, tWO.transactionDate, null, null, 
	glDeb.thePathExpanded + isnull(' (' + nullIf(glDeb.accountCode,'') + ')','') as debitGL
from dbo.tr_transactions as tWO
inner join dbo.tr_relationships as rWO on rWO.transactionID = tWO.transactionID
inner join dbo.tr_relationshipTypes as rtWO on rtWO.typeID = rWO.typeID and rtWO.type = 'WriteOffSaleTrans'
inner join dbo.tr_transactions as tAdj on tAdj.transactionID = rWO.appliedToTransactionID and tAdj.typeID = 3
inner join dbo.tr_relationships as rAdj on rAdj.transactionID = tAdj.transactionID
inner join dbo.tr_relationshipTypes as rtAdj on rtAdj.typeID = rAdj.typeID and rtAdj.type = 'AdjustTrans'
inner join dbo.ams_members as mAss on mAss.memberid = tWO.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glDeb on glDeb.GLAccountID = tWO.debitGLAccountID
where rAdj.appliedToTransactionID = @transactionID
and tWO.statusID = 1
order by 6 desc


-- current gl spread
DECLARE @tblHold TABLE (transactionID int, debitglAccountID int, creditglAccountID int, amount money)
declare @tblTrans TABLE (transactionID int, glAccountID int, debitAmount money, creditAmount money)

-- tax	
INSERT INTO @tblHold
select transactionID, debitglAccountID, creditglAccountID, amount
from dbo.tr_transactions
WHERE transactionID = @transactionID
and statusID = 1

-- adj
INSERT INTO @tblHold
select distinct adj.transactionID, adj.debitglAccountID, adj.creditglAccountID, adj.amount
from dbo.tr_transactions as adj
inner join dbo.tr_relationships as r on r.transactionID = adj.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'AdjustTrans'
inner join @tblHold as tbl on tbl.transactionID = r.appliedToTransactionID
where adj.statusID = 1

-- wo
INSERT INTO @tblHold
select distinct wo.transactionID, wo.debitglAccountID, wo.creditglAccountID, wo.amount
from dbo.tr_transactions as wo
inner join dbo.tr_relationships as r on r.transactionID = wo.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'WriteOffSaleTrans'
inner join @tblHold as tbl on tbl.transactionID = r.appliedToTransactionID
where wo.statusID = 1

-- dit
INSERT INTO @tblHold
select distinct dit.transactionID, dit.debitglAccountID, dit.creditglAccountID, dit.amount
from dbo.tr_transactions as dit
inner join dbo.tr_relationships as r on r.transactionID = dit.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
inner join @tblHold as tbl on tbl.transactionID = r.appliedToTransactionID
where dit.statusID = 1

insert into @tblTrans
select transactionID, debitglAccountID, amount, 0
from @tblHold
	union all
select transactionID, creditglAccountID, 0, amount
from @tblHold

select gl.thePathExpanded + isnull(' (' + nullIf(gl.accountCode,'') + ')','') as glexpanded,
	case 
	when gl.accountType = 'Cash' and sd.debitAmount - sd.creditAmount >= 0 then sd.debitAmount - sd.creditAmount
	when gl.accountType = 'Asset' and gl.GLCode = 'ACCOUNTSRECEIVABLE' and sd.debitAmount - sd.creditAmount > 0 then sd.debitAmount - sd.creditAmount
	when gl.accountType = 'Liability' and gl.GLCode = 'DEPOSITS' and sd.creditAmount - sd.debitAmount <= 0 then abs(sd.creditAmount - sd.debitAmount)
	when gl.accountType = 'Revenue' and sd.creditAmount - sd.debitAmount < 0 then abs(sd.creditAmount - sd.debitAmount)
	when gl.accountType = 'Expense' and sd.debitAmount - sd.creditAmount >= 0 then sd.debitAmount - sd.creditAmount
	when gl.accountType = 'Liability' and sd.creditAmount - sd.debitAmount <= 0 then abs(sd.creditAmount - sd.debitAmount)
	else null
	end as debits,
	case 
	when gl.accountType = 'Cash' and sd.debitAmount - sd.creditAmount < 0 then abs(sd.debitAmount - sd.creditAmount)
	when gl.accountType = 'Asset' and gl.GLCode = 'ACCOUNTSRECEIVABLE' and sd.debitAmount - sd.creditAmount <= 0 then abs(sd.debitAmount - sd.creditAmount)
	when gl.accountType = 'Liability' and gl.GLCode = 'DEPOSITS' and sd.creditAmount - sd.debitAmount > 0 then sd.creditAmount - sd.debitAmount
	when gl.accountType = 'Revenue' and sd.creditAmount - sd.debitAmount >= 0 then sd.creditAmount - sd.debitAmount
	when gl.accountType = 'Expense' and sd.debitAmount - sd.creditAmount < 0 then abs(sd.debitAmount - sd.creditAmount)
	when gl.accountType = 'Liability' and sd.creditAmount - sd.debitAmount > 0 then sd.creditAmount - sd.debitAmount
	else null
	end as credits
from (
	select glAccountID, sum(debitAmount) as debitAmount, sum(creditAmount) as creditAmount
	from @tblTrans
	group by glAccountID
	having (sum(debitAmount) > 0 OR sum(creditAmount) > 0) and sum(debitAmount) <> sum(creditAmount)
) as sd
inner join @allGLS as gl on gl.GLAccountID = sd.GLAccountID
order by gl.thePath


-- deferred schedule
select dit.recognitionDate, sum(t.amount) as recogAmt
from dbo.tr_transactions as t
inner join dbo.tr_relationships as r on r.transactionID = t.transactionID
inner join dbo.tr_relationshipTypes as rt on rt.typeID = r.typeID and rt.type = 'DITSaleTrans'
inner join (
	select @transactionID as transactionID
		union
	select adj.transactionID
	from dbo.tr_transactions as adj
	inner join dbo.tr_relationships as rInner on rInner.transactionID = adj.transactionID and rInner.appliedToTransactionID = @transactionID
	inner join dbo.tr_relationshipTypes as rtInner on rtInner.typeID = rInner.typeID and rtInner.type = 'AdjustTrans'
	where adj.statusID = 1
) as tbl on tbl.transactionID = r.appliedToTransactionID
inner join dbo.tr_transactionDIT as dit on dit.transactionID = t.transactionID
where t.statusID = 1
and dit.isActive = 1
group by dit.recognitionDate
order by dit.recognitionDate


-- sale transaction
select top 1 tSale.transactionID, tt.type, tSale.amount, tSale.detail,
	mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
	mAss2.company as assignedToMemberCompany,
	glCred.thePathExpanded + isnull(' (' + nullIf(glCred.accountCode,'') + ')','') as creditGL
from dbo.tr_transactions as tSale
inner join dbo.tr_types as tt on tt.typeID = tSale.typeID
inner join dbo.tr_relationships as rTax on rTax.appliedToTransactionID = tSale.transactionID
inner join dbo.tr_relationshipTypes as rtTax on rtTax.typeID = rTax.typeID and rtTax.type = 'SalesTaxTrans'
inner join dbo.ams_members as mAss on mAss.memberid = tSale.assignedToMemberID
inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
inner join @allGLS as glCred on glCred.GLAccountID = tSale.creditGLAccountID
where rTax.transactionID = @transactionID

-- related transactions
EXEC dbo.tr_viewTransaction_related_recursive @transactionID

RETURN 0
GO

