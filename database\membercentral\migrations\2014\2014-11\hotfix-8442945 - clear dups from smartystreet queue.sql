use membercentral;
GO

delete av
from (
	select memberID, addresstypeID
	from platformStats.dbo.apilog_smartystreets
	group by orgID, memberID, addressTypeID
	having count(*) > 20
) temp
inner join membercentral.dbo.ams_memberAddresses ma
	on ma.memberID = temp.memberID
	and ma.addressTypeID = temp.addressTypeID
inner join customapps.dbo.schedTask_addressValidation av
	on av.addressID = ma.addressID

update membercentral.dbo.scheduledTasks set
disabled = 0
where taskID = 79