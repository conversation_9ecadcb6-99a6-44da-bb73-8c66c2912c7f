<!--- Consolidated form for both create and edit operations --->
<cfparam name="url.sponsorGroupingID" type="numeric" default="0">
<cfparam name="url.eventID" type="numeric" default="0">
<cfparam name="url.selectorID" type="string">

<cfset local.sponsorGroupingID = url.sponsorGroupingID>
<cfset local.eventID = url.eventID>
<cfset local.selectorID = url.selectorID>
<cfset local.isEdit = (local.sponsorGroupingID GT 0)>

<!--- Get current sponsor group data for edit mode --->
<cfset local.qryGrouping = queryNew("")>
<cfset local.currentGroupName = "">

<cfif local.isEdit>
	<cfset local.objSponsors = createObject("component", "sponsors")>
	<cfset local.qryGrouping = local.objSponsors.getSponsorGroupingByID(sponsorGroupingID=local.sponsorGroupingID)>

	<cfif local.qryGrouping.recordCount EQ 0>
		<cfthrow message="Sponsor group not found" type="Application">
	<cfelse>
		<cfset local.currentGroupName = local.qryGrouping.sponsorGrouping>
	</cfif>
</cfif>

<cfsavecontent variable="local.customJS">
	<cfoutput>
	<script type="text/javascript">
		function saveSponsorGroup() {
			mca_hideAlert('err_frmeditsponsorgroup');
			var isEdit = #local.isEdit ? 'true' : 'false'#;
			var saveSponsorGroupResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					if (isEdit) {
						top.onSponsorGroupUpdated_#local.selectorID#();
					} else {
						top.onSponsorGroupCreated_#local.selectorID#();
					}
				} else {
					let errMsg = r.errmsg ? r.errmsg : 'We were unable to ' + (isEdit ? 'update' : 'create') + ' this sponsor group. Please try again.';
					mca_showAlert('err_frmeditsponsorgroup', errMsg);
					top.$('##btnMCModalSave').prop('disabled', false).html(isEdit ? 'Save Changes' : 'Create Group');
				}
			};
			if($('##sponsorGroupName').val().trim() != ''){
				top.$('##btnMCModalSave').prop('disabled', true).html(isEdit ? 'Saving...' : 'Creating...');
				var objParams = {
					sponsorGrouping: $('##sponsorGroupName').val().trim()
				};

				if (isEdit) {
					objParams.sponsorGroupingID = #local.sponsorGroupingID#;
					TS_AJX('SPONSORS','updateSponsorGrouping',objParams,saveSponsorGroupResult,saveSponsorGroupResult,10000,saveSponsorGroupResult);
				} else {
					objParams.eventID = #local.eventID#;
					TS_AJX('SPONSORS','createSponsorGrouping',objParams,saveSponsorGroupResult,saveSponsorGroupResult,10000,saveSponsorGroupResult);
				}
			} else {
				mca_showAlert('err_frmeditsponsorgroup', 'Please enter a name for the sponsor group.');
				return false;
			}
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.customJS)#">

<cfoutput>
<form name="frmEditSponsorGroup" id="frmEditSponsorGroup" onsubmit="saveSponsorGroup(); return false;">

	<div id="err_frmeditsponsorgroup" class="alert alert-danger mb-3 d-none"></div>

	<div class="form-label-group mb-3">
		<input type="text" name="sponsorGroupName" id="sponsorGroupName" class="form-control"
			   value="#encodeForHTMLAttribute(local.currentGroupName)#" maxlength="100" required>
		<label for="sponsorGroupName">Sponsor Group Name</label>
	</div>

	<div class="form-text text-muted mb-3">
		<small><cfif local.isEdit>Update the name for this sponsor group.<cfelse>Enter a descriptive name for this sponsor group (e.g., "Gold Level Sponsors", "Premium Partners", etc.)</cfif></small>
	</div>

	<!--- Hidden submit button triggered from parent modal --->
	<button type="submit" class="d-none"></button>
</form>
</cfoutput>
