<cfparam name="url.sponsorGroupingID" type="numeric">
<cfparam name="url.selectorID" type="string">

<cfset local.sponsorGroupingID = url.sponsorGroupingID>
<cfset local.selectorID = url.selectorID>

<!--- Get current sponsor group data --->
<cfset local.objSponsors = createObject("component", "sponsors")>
<cfset local.qryGrouping = local.objSponsors.getSponsorGroupingByID(sponsorGroupingID=local.sponsorGroupingID)>

<cfif local.qryGrouping.recordCount EQ 0>
	<cfthrow message="Sponsor group not found" type="Application">
</cfif>

<cfsavecontent variable="local.customJS">
	<cfoutput>
	<script type="text/javascript">
		function updateSponsorGroup() {
			mca_hideAlert('err_frmeditsponsorgroup');
			var updateSponsorGroupResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					top.onSponsorGroupUpdated_#local.selectorID#();
				} else {
					let errMsg = r.errmsg ? r.errmsg : 'We were unable to update this sponsor group. Please try again.';
					mca_showAlert('err_frmeditsponsorgroup', errMsg);
					top.$('##btnMCModalSave').prop('disabled', false).html('Save Changes');
				}
			};
			if($('##sponsorGroupName').val().trim() != ''){
				top.$('##btnMCModalSave').prop('disabled', true).html('Saving...');
				var objParams = { 
					sponsorGroupingID: #local.sponsorGroupingID#, 
					sponsorGrouping: $('##sponsorGroupName').val().trim()
				};
				TS_AJX('SPONSORS','updateSponsorGrouping',objParams,updateSponsorGroupResult,updateSponsorGroupResult,10000,updateSponsorGroupResult);
			} else {
				mca_showAlert('err_frmeditsponsorgroup', 'Please enter a name for the sponsor group.');
				return false;
			}
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.customJS)#">

<cfoutput>
<form name="frmEditSponsorGroup" id="frmEditSponsorGroup" onsubmit="updateSponsorGroup(); return false;">
	
	<div id="err_frmeditsponsorgroup" class="alert alert-danger mb-3 d-none"></div>
	
	<div class="form-label-group mb-3">
		<input type="text" name="sponsorGroupName" id="sponsorGroupName" class="form-control" 
			   value="#encodeForHTMLAttribute(local.qryGrouping.sponsorGrouping)#" maxlength="100" required>
		<label for="sponsorGroupName">Sponsor Group Name</label>
	</div>
	
	<div class="form-text text-muted mb-3">
		<small>Update the name for this sponsor group.</small>
	</div>
	
	<!--- Hidden submit button triggered from parent modal --->
	<button type="submit" class="d-none"></button>
</form>
</cfoutput>
