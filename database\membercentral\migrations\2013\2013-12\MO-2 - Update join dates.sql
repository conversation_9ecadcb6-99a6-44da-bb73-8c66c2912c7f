use customApps
GO
declare @udid int

insert into dbo.schedTask_memberJoinDates (siteCode, joinDateFieldName, rejoinDateFieldName, droppedDateFieldName, lastSuccessDate, lastErrorCode, isActive, paidThruDateFieldName)
values ('MO', 'Join Date', 'Rejoin Date', 'Drop Date', '1/1/1972', 0, 1, 'Paid Thru Date')
	select @udid = SCOPE_IDENTITY()

insert into dbo.schedTask_memberJoinDateSubTypes (memberJoinDateUDID, subscriptionTypeUID)
values (@udid, '00DF1A8A-5061-47A6-9C0B-E598EB8E8411')

declare @error_code int
EXEC dbo.job_memberJoinDates @udid=@udid, @error_code=@error_code OUTPUT
select @error_code
GO

