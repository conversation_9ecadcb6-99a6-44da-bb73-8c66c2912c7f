use seminarweb
GO
CREATE PROC dbo.swod_cleanupLogTables
@pathToLogs varchar(400)

AS

SET NOCOUNT ON


/* *********************************************************************************** */
/* Write out activityLog, debugLog older than 1 day as text files and clear from table */
/* *********************************************************************************** */
IF OBJECT_ID('tempdb..#tmpLogAccess') IS NOT NULL 
	DROP TABLE #tmpLogAccess
CREATE TABLE #tmpLogAccess (logAccessID int PRIMARY KEY, folder char(4), debug bit DEFAULT(0), activity bit DEFAULT(0));

-- limit to this range so the query runs faster
declare @oneDayAgo datetime, @twoDaysAgo datetime
set @oneDayAgo = dateadd(d,-1,getdate()) 
set @twoDaysAgo = dateadd(d,-2,getdate()) 

insert into #tmpLogAccess (logAccessID, folder)
select logaccessid, right('0000' + cast(logAccessID%1000 as varchar(4)),4)
from dbo.tbllogaccessswod
where datelastmodified between @twoDaysAgo and @oneDayAgo

update tmp
set tmp.debug = trialsmith.dbo.fn_writefile(@pathToLogs + tmp.folder + '\' + cast(tmp.logAccessID as varchar(10)) + '-debug.txt',tbl.debuglog,1)
from #tmpLogAccess as tmp
inner join dbo.tbllogaccessswod as tbl on tbl.logAccessID = tmp.logAccessID
where tbl.debuglog is not null 

update tmp
set tmp.activity = trialsmith.dbo.fn_writefile(@pathToLogs + tmp.folder + '\' + cast(tmp.logAccessID as varchar(10)) + '-activity.txt',tbl.activityLog,1)
from #tmpLogAccess as tmp
inner join dbo.tbllogaccessswod as tbl on tbl.logAccessID = tmp.logAccessID
where tbl.activityLog is not null 

update tbl
set tbl.debuglog = null
from #tmpLogAccess as tmp
inner join dbo.tbllogaccessswod as tbl on tbl.logAccessID = tmp.logAccessID
where tmp.debug = 1

update tbl
set tbl.activityLog = null
from #tmpLogAccess as tmp
inner join dbo.tbllogaccessswod as tbl on tbl.logAccessID = tmp.logAccessID
where tmp.activity = 1

IF OBJECT_ID('tempdb..#tmpLogAccess') IS NOT NULL 
	DROP TABLE #tmpLogAccess


SET NOCOUNT OFF

GO


/* create folders. 
This has already run on dev and prod.

select trialsmith.dbo.fn_createDirectory('\\tsfile1\seminarwebfiles\SWODAccessLogs\' + right('0000' + cast(Number as varchar(3)),4))
from trialsmith.dbo.F_TABLE_NUMBER_RANGE(0,999)
*/

