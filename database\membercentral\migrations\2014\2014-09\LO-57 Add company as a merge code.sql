USE [memberCentral]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROC [dbo].[an_emailNotices]
@messageWrapper varchar(max)

AS

declare @functionID int, @recordedByMemberID int, @maxEmailsAllowedPerNotice int, @messageID int, 
	@messageTypeID int, @mesageStatusID int, @rawcontent varchar(max), @messageToParse varchar(max),
	@fieldID int, @fieldName varchar(60), @noticeID int
declare @metadataFields TABLE (fieldName varchar(60), fieldID int NULL)
select @functionID = dbo.fn_getResourceFunctionID('ReceiveAnnouncementEmail',dbo.fn_getResourceTypeId('Announcement'))
select @recordedByMemberID = memberID from dbo.ams_members where memberNumber = 'SYSTEM' and orgID = 1
select @messageTypeID = messageTypeID from platformMail.dbo.email_messageTypes where messageTypeCode = 'ANNOUNCE'
select @mesageStatusID = statusID from platformMail.dbo.email_statuses where statusCode = 'Q'
select @maxEmailsAllowedPerNotice = 2000

IF OBJECT_ID('tempdb..#tmpNotices') IS NOT NULL 
	DROP TABLE #tmpNotices
IF OBJECT_ID('tempdb..#tmpNoticeRecipients') IS NOT NULL 
	DROP TABLE #tmpNoticeRecipients

SELECT n.noticeID, s.siteID, n.siteResourceID, 
	coalesce(nullif(n.emailFromName,''),nullif(c.emailFromName,''),s.sitename) as emailFromName,
	coalesce(nullif(n.emailFromAddress,''),nullif(c.emailFromAddress,''),net.supportProviderEmail) as emailFromEmail,
	coalesce(nullif(n.emailFromAddress,''),nullif(c.emailFromAddress,''),net.supportProviderEmail) as emailReplyToEmail,
	net.supportProviderEmail as emailSenderEmail,
	cl.contentTitle as emailSubject, n.noticeContentID, cv.contentVersionID, n.notifyEmail, NEWID() as messageUID, 
	replace(replace(@messageWrapper,'@@centername@@',Cai.applicationInstanceName + isnull(' (' + CcommunityInstances.applicationInstanceName + ')','')),'@@centerlink@@','<a href="http://' + sh.HostName + '">' + s.sitename + '</a>') as messageWrapper, 
	0 as numRecipients, @maxEmailsAllowedPerNotice as maxEmailsAllowedPerNotice
INTO #tmpNotices
FROM dbo.an_notices as n 
INNER JOIN dbo.cms_siteResources as sr ON n.siteResourceID = sr.siteResourceID
INNER JOIN dbo.sites as s on s.siteID = sr.siteID
INNER JOIN dbo.siteHostNames as sh on sh.siteID = s.siteID and sh.hostnameID = sh.mainHostnameID
inner join dbo.networkSites as ns on ns.siteID = s.siteID and ns.isLoginNetwork = 1
INNER JOIN dbo.networks as net on net.networkID = ns.networkID
INNER JOIN dbo.an_centerNotices as cn ON cn.sourceNoticeID = n.noticeID
INNER JOIN dbo.an_centers as c on c.centerID = cn.centerID
INNER JOIN dbo.cms_applicationInstances as Cai on Cai.applicationInstanceID = c.applicationInstanceID
INNER JOIN dbo.cms_siteResources as Csr on Cai.siteResourceID = Csr.siteResourceID and Csr.siteResourceStatusID = 1
INNER JOIN dbo.cms_siteResources as CparentResource on CparentResource.siteResourceID = Csr.parentSiteResourceID
LEFT OUTER JOIN dbo.cms_siteResources as CgrandparentResource
	INNER JOIN dbo.cms_applicationInstances as CCommunityInstances on CcommunityInstances.siteResourceID = CgrandParentResource.siteResourceID
	INNER JOIN dbo.cms_siteResourceTypes as Csrt on Csrt.resourceTypeID = CgrandparentResource.resourceTypeID and Csrt.resourceType = 'Community'
	on CgrandparentResource.siteResourceID = CparentResource.parentSiteResourceID
INNER JOIN dbo.cms_contentLanguages as cl ON cl.contentID = n.noticeContentID AND cl.languageID = 1
INNER JOIN dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID AND cv.isActive = 1
cross apply dbo.fn_getContent(n.noticeContentID,1) as noticeContent
WHERE n.emailNotice = 1
AND n.emailDateScheduled < getDate()
AND n.endDate > getDate()
AND n.emailDateSent IS NULL
and noticeContent.rawContent LIKE '_%'

select uniqueEmails.noticeID, uniqueEmails.memberID, chosenMember.prefix, chosenMember.firstName, 
	chosenMember.middlename, chosenMember.lastName, chosenMember.company, chosenMember.suffix, uniqueEmails.email
into #tmpNoticeRecipients
from (
	SELECT n.noticeID, min(m.memberID) as memberID, me.email
	FROM dbo.an_notices as n
	INNER JOIN #tmpNotices as tmp on tmp.noticeID = n.noticeID
	inner join dbo.cache_perms_siteResourceFunctionRightPrints as srfrp on srfrp.siteResourceID = n.siteResourceID
		and srfrp.functionID = @functionID
	inner join dbo.cache_perms_groupPrintsRightPrints as gprp on gprp.rightPrintID = srfrp.rightPrintID
	inner join dbo.cache_perms_groupPrints as gp on gp.groupPrintID = gprp.groupPrintID
	INNER JOIN dbo.ams_members as m ON m.groupPrintID = gp.groupPrintID and m.status = 'A'
	INNER JOIN dbo.ams_memberEmails as me ON me.memberID = m.memberID 
	inner join dbo.ams_memberEmailTypes as met on met.emailTypeID = me.emailTypeID and met.emailTypeOrder = 1
	WHERE me.email <> ''
	group by n.noticeID, me.email
) as uniqueEmails
inner join dbo.ams_members as chosenMember on chosenMember.memberID = uniqueEmails.memberID

-- put recipient count in notices table
update #tmpNotices set numRecipients = (select count(*) from #tmpNoticeRecipients where noticeID = #tmpNotices.noticeID)

-- cancel notices that are over the threshold of recipients or have no recipients
update n
set n.emailNotice = 0
from dbo.an_notices as n
inner join #tmpNotices as tmp on tmp.noticeID = n.noticeID
where tmp.numRecipients >= @maxEmailsAllowedPerNotice
or tmp.numRecipients = 0

-- delete notices where there are no recipients
delete from #tmpNotices where numRecipients = 0

-- each notice becomes its own email_message (as queued status)
insert into platformMail.dbo.email_messages (messageTypeID, siteID, sendingSiteResourceID, dateEntered, 
	recordedByMemberID, fromName, fromEmail, replyToEmail, senderEmail, [subject], contentVersionID, 
	messageWrapper, [uid])
select @messageTypeID, siteID, siteResourceID, getdate(), @recordedByMemberID, emailFromName,
	emailFromEmail, emailReplyToEmail, emailSenderEmail, emailSubject, contentVersionID, 
	messageWrapper, messageUID
from #tmpNotices
where numRecipients < @maxEmailsAllowedPerNotice

-- add recipients
insert into platformMail.dbo.email_messageRecipientHistory (messageID, memberID, 
	dateLastUpdated, toName, toEmail, emailStatusID, batchID, batchStartDate)
select em.messageID, tmpR.memberID, getdate(), tmpR.firstName + ' ' + tmpR.lastName, 
	tmpR.email, @mesageStatusID, null, null
from platformMail.dbo.email_messages as em
inner join #tmpNotices as tmpN on tmpN.messageUID = em.uid
inner join #tmpNoticeRecipients as tmpR on tmpR.noticeID = tmpN.noticeID

-- loop over each notice to add any necessary metadata fields
select @messageID = min(em.messageID)
	from platformMail.dbo.email_messages as em
	inner join #tmpNotices as tmpN on tmpN.messageUID = em.uid
while @messageID is not null BEGIN
	select @messageWrapper = null, @rawcontent = null
	delete from @metadataFields

	select @noticeID = tmpN.noticeID, @messageWrapper = tmpN.messageWrapper, 
		@rawcontent = msgcontent.rawcontent
		from platformMail.dbo.email_messages as em
		inner join #tmpNotices as tmpN on tmpN.messageUID = em.uid
		cross apply dbo.fn_getContent(tmpN.noticeContentID,1) as msgcontent
		where em.messageID = @messageID

	select @messageToParse = replace(@messageWrapper,'@@rawcontent@@',@rawcontent)

	insert into @metadataFields (fieldName)
	select distinct [Text]
	from dbo.fn_RegexMatches(@messageToParse,'(?<=\[\[)([^,\]]+)(?=,?([^\]]+)?\]\])')

	insert into platformMail.dbo.email_metadataFields (fieldName, isMergeField)
	select fieldName, 1
	from @metadataFields
		except
	select fieldName, isMergeField
	from platformMail.dbo.email_metadataFields

	update tmp
	set tmp.fieldID = MF.fieldID
	from @metadataFields as tmp
	inner join platformMail.dbo.email_metadataFields as MF on MF.fieldName = tmp.fieldName
	where MF.isMergeField = 1

	select @fieldID = min(fieldID) from @metadataFields
	while @fieldID is not null BEGIN
		select @fieldName = fieldName from @metadataFields where fieldID = @fieldID

		insert into platformMail.dbo.email_messageMetadataFields (messageID, fieldID, memberID, fieldValue)
		select @messageID, @fieldID, memberID, 
			fieldValue = case @fieldName
				when 'firstname' then firstname
				when 'lastname' then lastname
				when 'company' then company
				when 'prefix' then prefix 
				when 'fullName' then firstname + ' ' + lastname
				when 'extendedname' then firstname + isnull(' ' + nullif(middlename,''),'') + ' ' + lastname + isnull(' ' + nullif(suffix,''),'')
				end
		from #tmpNoticeRecipients
		where noticeID = @noticeID

		select @fieldID = min(fieldID) from @metadataFields where fieldID > @fieldID
	END

	select @messageID = min(em.messageID)
		from platformMail.dbo.email_messages as em
		inner join #tmpNotices as tmpN on tmpN.messageUID = em.uid
		where em.messageID > @messageID
END

-- update notices with datesent
update n
set n.emailDateSent = getDate()
from dbo.an_notices as n
inner join #tmpNotices as tmp on tmp.noticeID = n.noticeID
where tmp.numRecipients < @maxEmailsAllowedPerNotice

-- return notices
select *
from #tmpNotices

IF OBJECT_ID('tempdb..#tmpNotices') IS NOT NULL 
	DROP TABLE #tmpNotices
IF OBJECT_ID('tempdb..#tmpNoticeRecipients') IS NOT NULL 
	DROP TABLE #tmpNoticeRecipients

RETURN 0





USE [memberCentral]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

ALTER PROC [dbo].[email_previewBlast]
@blastID int,
@memberID int,
@messageWrapper varchar(max)

AS

declare @messageToParse varchar(max)
select @messageToParse = replace(@messageWrapper,'@@rawcontent@@',messageContent.rawContent)
from dbo.email_EmailBlasts as eb
cross apply dbo.fn_getContent(eb.contentID,1) as messageContent
where eb.blastID = @blastID

-- all merge codes in message
select distinct [Text] as fieldName
from dbo.fn_RegexMatches(@messageToParse,'(?<=\[\[)([^,\]]+)(?=,?([^\]]+)?\]\])')

-- return recipient with all possible merge code data
select TOP 1 @messageToParse as messageBody, m.firstName + ' ' + m.lastName as toName, me.email as toEmail,
	m.firstname as firstname, m.lastname as lastname, m.company as company, m.prefix as prefix, 
	m.firstName + ' ' + m.lastName as fullName, 
	m.firstname + isnull(' ' + nullif(m.middlename,''),'') + ' ' + m.lastname + isnull(' ' + nullif(m.suffix,''),'') as extendedName
FROM dbo.ams_members as m WITH(NOLOCK)
inner join dbo.ams_memberEmails as me WITH(NOLOCK) on me.memberid = m.memberid
INNER JOIN dbo.ams_memberEmailTypes as met WITH(NOLOCK) on met.emailTypeID = me.emailTypeID
where m.memberID = @memberID
and met.emailTypeOrder = 1

RETURN 0






USE [memberCentral]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROC [dbo].[email_sendBlast]
@recordedByMemberID int,
@siteID int,
@blastID int,
@messageWrapper varchar(max)

AS

declare @numRecipients int, @rc int, @messageTypeID int, 
	@messageStatusIDInserting int, @messageStatusIDQueued int, @sendingSiteResourceID int, 
	@messageID int, @rawcontent varchar(max), @footercontent varchar(max), @messageToParse varchar(MAX),
	@fieldID int, @fieldName varchar(60), @contentID int, @footerContentID [int], @languageID int
declare @metadataFields TABLE (fieldName varchar(60), fieldID int NULL)
declare @ruleID int, @xmlMembers xml
declare @fromName varchar(200), @fromEmail varchar(200), @replyToEmail varchar(200), @senderEmail varchar(200), @subject varchar(400), @contentVersionID int

IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
	DROP TABLE #tmpRecipients

select @messageTypeID = messageTypeID from platformMail.dbo.email_messageTypes where messageTypeCode = 'EMAILBLAST'
select @messageStatusIDInserting = statusID from platformMail.dbo.email_statuses where statusCode = 'I'
select @messageStatusIDQueued = statusID from platformMail.dbo.email_statuses where statusCode = 'Q'
select @languageID = dbo.fn_getLanguageID('en')
select @senderEmail = '<EMAIL>'

select @sendingSiteResourceID = st.siteResourceID
from dbo.sites as s
inner join dbo.admin_siteTools as st on st.siteID = s.siteID and st.siteID = @siteID
inner join dbo.admin_toolTypes as tt on tt.toolTypeID = st.toolTypeID and tt.toolType = 'EmailBlast'
	IF @@ERROR <> 0 RETURN -1

select @ruleID = ruleID, @fromName = fromName, @fromEmail = fromEmail, @replyToEmail = replyTo, @contentID = contentID, @footerContentID = footerContentID
from dbo.email_EmailBlasts
where blastID = @blastID
	IF @@ERROR <> 0 RETURN -1

select @subject=contentTitle, @contentVersionID=contentVersionID, @rawContent = rawContent
from dbo.fn_getContent(@contentID,@languageID) as messageContent
	IF @@ERROR <> 0 RETURN -1
	
SELECT @footercontent = rawcontent
FROM [dbo].[fn_getContent](@footerContentID,@languageID) AS footerContent
	IF @@ERROR <> 0 RETURN -1

EXEC dbo.ams_RunVirtualGroupRule @ruleID=@ruleID, @xmlVirtual=@xmlMembers OUTPUT
	IF @@ERROR <> 0 RETURN -1

select m.memberID, m.prefix, m.firstName, m.middlename, m.lastName, m.company, m.suffix, me.email
into #tmpRecipients
FROM @xmlMembers.nodes('//m') AS XM(item)
INNER JOIN dbo.ams_members as m WITH(NOLOCK) on m.memberid = XM.item.value('@id', 'int')
	and m.status = 'A'
inner join dbo.ams_memberEmails as me WITH(NOLOCK) on me.memberid = m.memberid
	and len(me.email) > 0
INNER JOIN dbo.ams_memberEmailTypes as met WITH(NOLOCK) on met.emailTypeID = me.emailTypeID
	and met.emailTypeOrder = 1
	IF @@ERROR <> 0 RETURN -1
	select @numRecipients = @@rowcount	

-- add any necessary metadata fields
select @messageToParse = replace(@messageWrapper,'@@rawcontent@@',@rawcontent)
	IF @@ERROR <> 0 RETURN -1
select @messageToParse = replace(@messageToParse,'@@footerContent@@',@footercontent)
	IF @@ERROR <> 0 RETURN -1
	
insert into @metadataFields (fieldName)
select distinct [Text]
from dbo.fn_RegexMatches(@messageToParse,'(?<=\[\[)([^,\]]+)(?=,?([^\]]+)?\]\])')
	IF @@ERROR <> 0 RETURN -1

BEGIN TRAN
	-- add email_message
	EXEC @rc = platformMail.dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID, 
		@sendingSiteResourceID=@sendingSiteResourceID, @recordedByMemberID=@recordedByMemberID, 
		@fromName=@fromName, @fromEmail=@fromEmail, 
		@replyToEmail=@replyToEmail, @senderEmail=@senderEmail, 
		@subject=@subject, @contentVersionID=@contentVersionID, 
		@messageWrapper=@messageWrapper, @messageID=@messageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @messageID = 0 goto on_error

	-- add recipients as I (not ready to be queued yet)
	insert into platformMail.dbo.email_messageRecipientHistory (messageID, memberID, 
		dateLastUpdated, toName, toEmail, emailStatusID, batchID, batchStartDate)
	select @messageID, memberID, getdate(), firstName + ' ' + lastName, email, @messageStatusIDInserting, null, null
	from #tmpRecipients
		IF @@ERROR <> 0 goto on_error

	-- add message metadata fields
	insert into platformMail.dbo.email_metadataFields (fieldName, isMergeField)
	select fieldName, 1
	from @metadataFields
		except
	select fieldName, isMergeField
	from platformMail.dbo.email_metadataFields
		IF @@ERROR <> 0 goto on_error
	update tmp
	set tmp.fieldID = MF.fieldID
	from @metadataFields as tmp
	inner join platformMail.dbo.email_metadataFields as MF on MF.fieldName = tmp.fieldName
	where MF.isMergeField = 1
		IF @@ERROR <> 0 goto on_error

	-- add recipient metadata

	select @fieldID = min(fieldID) from @metadataFields where fieldName in ('firstname','lastname','company','prefix','fullName','extendedname')
	while @fieldID is not null BEGIN
		select @fieldName = fieldName from @metadataFields where fieldID = @fieldID
			IF @@ERROR <> 0 goto on_error
		insert into platformMail.dbo.email_messageMetadataFields (messageID, fieldID, memberID, fieldValue)
		select @messageID, @fieldID, memberID, 
			fieldValue = case @fieldName
				when 'firstname' then firstname
				when 'lastname' then lastname
				when 'company' then company
				when 'prefix' then prefix 
				when 'fullName' then firstname + ' ' + lastname
				when 'extendedname' then firstname + isnull(' ' + nullif(middlename,''),'') + ' ' + lastname + isnull(' ' + nullif(suffix,''),'')
				end
		from #tmpRecipients
			IF @@ERROR <> 0 goto on_error
		select @fieldID = min(fieldID) from @metadataFields where fieldName in ('firstname','lastname','company','prefix','fullName','extendedname') and fieldID > @fieldID
	END

	-- mark recipients as queued
	update mrh set
		emailStatusID = @messageStatusIDQueued
	from platformMail.dbo.email_messages m
	inner join platformMail.dbo.email_messageRecipientHistory mrh
		on m.messageID = mrh.messageID
		and m.messageID = @messageID
	IF @@ERROR <> 0 goto on_error

	-- mark emailSent if emailDateScheduled is not null so scheduled task will not attempt to send again.
	update dbo.email_EmailBlasts set 
		emailDateScheduled = null,
		emailDateSent = getDate()
	where blastID =  @blastID
	AND emailDateScheduled is not null
	IF @@ERROR <> 0 goto on_error

IF @@TRANCOUNT > 0 COMMIT TRAN

goto on_done

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN

	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients
	RETURN -1


on_done:
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients
	RETURN 0






USE [memberCentral]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROC [dbo].[email_sendTestBlast]
@recordedByMemberID int,
@siteID int,
@blastID int,
@memberID int,
@email varchar(200),
@messageWrapper varchar(max),
@messageID int OUTPUT

AS

declare @numRecipients int, @rc int, @messageTypeID int, 
	@messageStatusIDInserting int, @messageStatusIDQueued int, @sendingSiteResourceID int, 
	@rawcontent varchar(max), @messageToParse varchar(max), @footercontent varchar(max),
	@fieldID int, @fieldName varchar(60), @contentID int, @footerContentID int, @languageID int
declare @metadataFields TABLE (fieldName varchar(60), fieldID int NULL)
declare @fromName varchar(200), @fromEmail varchar(200), @replyToEmail varchar(200), @senderEmail varchar(200), @subject varchar(400), @contentVersionID int

IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
	DROP TABLE #tmpRecipients

select @messageID = null
select @messageTypeID = messageTypeID from platformMail.dbo.email_messageTypes where messageTypeCode = 'EMAILBLAST'
select @messageStatusIDInserting = statusID from platformMail.dbo.email_statuses where statusCode = 'I'
select @messageStatusIDQueued = statusID from platformMail.dbo.email_statuses where statusCode = 'Q'
select @languageID = dbo.fn_getLanguageID('en')
select @senderEmail = '<EMAIL>'

select @sendingSiteResourceID = st.siteResourceID
from dbo.sites as s WITH(NOLOCK)
inner join dbo.admin_siteTools as st WITH(NOLOCK) on st.siteID = s.siteID and st.siteID = @siteID
inner join dbo.admin_toolTypes as tt WITH(NOLOCK) on tt.toolTypeID = st.toolTypeID and tt.toolType = 'EmailBlast'
	IF @@ERROR <> 0 RETURN -1

select @fromName = fromName, @fromEmail = fromEmail, @replyToEmail = replyTo, @contentID = contentID, @footerContentID = footerContentID
from dbo.email_EmailBlasts WITH(NOLOCK)
where blastID = @blastID
	IF @@ERROR <> 0 RETURN -1

select @subject='**TEST** ' + contentTitle, @contentVersionID=contentVersionID, @rawContent = rawContent
from dbo.fn_getContent(@contentID,@languageID) as messageContent
	IF @@ERROR <> 0 RETURN -1
SELECT @footercontent = rawcontent
FROM dbo.fn_getContent(@footerContentID,@languageID) AS footerContent
	IF @@ERROR <> 0 RETURN -1

select m.memberID, m.prefix, m.firstName, m.middlename, m.lastName, m.company, m.suffix, @email as email
into #tmpRecipients
FROM dbo.ams_members as m WITH(NOLOCK)
WHERE m.memberID = @memberID
	IF @@ERROR <> 0 RETURN -1
	select @numRecipients = @@rowcount	

-- add any necessary metadata fields
select @messageToParse = replace(@messageWrapper,'@@rawcontent@@',@rawcontent)
	IF @@ERROR <> 0 RETURN -1
select @messageToParse = replace(@messageToParse,'@@footerContent@@',@footercontent)
	IF @@ERROR <> 0 RETURN -1
insert into @metadataFields (fieldName)
select distinct [Text]
from dbo.fn_RegexMatches(@messageToParse,'(?<=\[\[)([^,\]]+)(?=,?([^\]]+)?\]\])')
	IF @@ERROR <> 0 RETURN -1

BEGIN TRAN
	-- add email_message
	EXEC @rc = platformMail.dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID, 
		@sendingSiteResourceID=@sendingSiteResourceID, @recordedByMemberID=@recordedByMemberID, 
		@fromName=@fromName, @fromEmail=@fromEmail, 
		@replyToEmail=@replyToEmail, @senderEmail=@senderEmail, 
		@subject=@subject, @contentVersionID=@contentVersionID, 
		@messageWrapper=@messageWrapper, @messageID=@messageID OUTPUT
		IF @@ERROR <> 0 OR @rc <> 0 OR @messageID = 0 goto on_error

	-- add recipients as I (not ready to be queued yet)
	insert into platformMail.dbo.email_messageRecipientHistory (messageID, memberID, 
		dateLastUpdated, toName, toEmail, emailStatusID, batchID, batchStartDate)
	select @messageID, memberID, getdate(), firstName + ' ' + lastName, email, @messageStatusIDInserting, null, null
	from #tmpRecipients
		IF @@ERROR <> 0 goto on_error

	-- add message metadata fields
	insert into platformMail.dbo.email_metadataFields (fieldName, isMergeField)
	select fieldName, 1
	from @metadataFields
		except
	select fieldName, isMergeField
	from platformMail.dbo.email_metadataFields
		IF @@ERROR <> 0 goto on_error
	update tmp
	set tmp.fieldID = MF.fieldID
	from @metadataFields as tmp
	inner join platformMail.dbo.email_metadataFields as MF on MF.fieldName = tmp.fieldName
	where MF.isMergeField = 1
		IF @@ERROR <> 0 goto on_error

	-- add recipient metadata
	select @fieldID = min(fieldID) from @metadataFields where fieldName in ('firstname','lastname','company','prefix','fullName','extendedname')
	while @fieldID is not null BEGIN
		select @fieldName = fieldName from @metadataFields where fieldID = @fieldID
			IF @@ERROR <> 0 goto on_error
		insert into platformMail.dbo.email_messageMetadataFields (messageID, fieldID, memberID, fieldValue)
		select @messageID, @fieldID, memberID, 
			fieldValue = case @fieldName
				when 'firstname' then firstname
				when 'lastname' then lastname
				when 'company' then company
				when 'prefix' then prefix 
				when 'fullName' then firstname + ' ' + lastname
				when 'extendedname' then firstname + isnull(' ' + nullif(middlename,''),'') + ' ' + lastname + isnull(' ' + nullif(suffix,''),'')
				end
		from #tmpRecipients
			IF @@ERROR <> 0 goto on_error
		select @fieldID = min(fieldID) from @metadataFields where fieldName in ('firstname','lastname','company','prefix','fullName','extendedname') and fieldID > @fieldID
	END

	-- mark recipients as queued
	update mrh set
		emailStatusID = @messageStatusIDQueued
	from platformMail.dbo.email_messages m
	inner join platformMail.dbo.email_messageRecipientHistory mrh
		on m.messageID = mrh.messageID
		and m.messageID = @messageID
	IF @@ERROR <> 0 goto on_error


IF @@TRANCOUNT > 0 COMMIT TRAN

goto on_done

on_error:
	IF @@TRANCOUNT > 1 COMMIT TRAN
	ELSE IF @@TRANCOUNT = 1 ROLLBACK TRAN

	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients
	select @messageID = 0
	RETURN -1


on_done:
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients
	RETURN 0

