IF  EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME ='FK_ams_classifications_cms_siteResources')
BEGIN
	print 'Relationship ams_classifications_cms_siteResources Exists! Deleting...'
	ALTER TABLE [ams_classifications] DROP CONSTRAINT [FK_ams_classifications_cms_siteResources]
END
GO


print '********************************************'
print 'CREATING TABLE ams_classifications'

IF OBJECT_ID('ams_classifications') IS NOT NULL 
	EXEC('DROP TABLE  ams_classifications')
GO

create table ams_classifications(
	classificationid int IDENTITY(1,1) NOT NULL PRIMARY KEY CLUSTERED, 
	name varchar(100) NULL,
	allowSearch bit NULL,
	siteResourceID int NOT NULL,
	showInSearchResults bit NULL,
	groupSetID int NOT NULL
)
GO

print 'TABLE ams_classifications created'
print '********************************************'



ALTER TABLE [ams_classifications]  WITH NOCHECK ADD  CONSTRAINT [FK_ams_classifications_cms_siteResources] FOREIGN KEY([siteResourceID])
REFERENCES [dbo].[cms_siteResources] ([siteResourceID])
GO
