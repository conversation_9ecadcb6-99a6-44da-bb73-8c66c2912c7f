USE [memberCentral]
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ad_populateAdZones]') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].[ad_populateAdZ<PERSON>]
GO

CREATE PROC [dbo].[ad_populateAdZones]
AS

SET NOCOUNT ON

IF OBJECT_ID('datatransfer.dbo.ads_websiteZones') IS NOT NULL BEGIN
	DROP TABLE datatransfer.dbo.ads_websiteZones
END
IF OBJECT_ID('datatransfer.dbo.ads_eclipsZones') IS NOT NULL BEGIN
	DROP TABLE datatransfer.dbo.ads_eclipsZones
END
IF OBJECT_ID('datatransfer.dbo.ads_lyrisZones') IS NOT NULL BEGIN
	DROP TABLE datatransfer.dbo.ads_lyrisZones
END

declare
	@eclipsZoneTypeID int,
	@listserverZoneTypeID int,
	@searchZoneTypeID int,
	@listviewerZoneTypeID int,
	@loginPageZoneTypeID int
select @eclipsZoneTypeID = zoneTypeID from ad_zoneTypes where zoneTypeName = 'EClips'
select @listserverZoneTypeID = zoneTypeID from ad_zoneTypes where zoneTypeName = 'Listserver'
select @searchZoneTypeID = zoneTypeID from ad_zoneTypes where zoneTypeName = 'Search'
select @listviewerZoneTypeID = zoneTypeID from ad_zoneTypes where zoneTypeName = 'Listviewer'
select @loginPageZoneTypeID = zoneTypeID from ad_zoneTypes where zoneTypeName = 'Login Page'

-- Update Lyris zones
select name_ + ' (' + DescShort_ + ')' as ZoneName, @listserverZoneTypeID as zoneTypeID, lf.orgcode as sitecode, name_ as uniquecode
into datatransfer.dbo.ads_lyrisZones
from lyris.trialslyris1.dbo.lists_ l
inner join lyris.trialslyris1.dbo.lists_format lf 
	on lf.name = l.name_ 
	and l.adminSend_ = 'F'

-- remove exact matches
delete lz
from datatransfer.dbo.ads_lyrisZones lz
inner join ad_zones az on az.sitecode = lz.sitecode collate DATABASE_DEFAULT
	and az.zoneTypeID = lz.zoneTypeID
	and az.uniquecode = lz.uniquecode  collate DATABASE_DEFAULT
	and az.ZoneName = lz.ZoneName  collate DATABASE_DEFAULT

-- zone name updated
update az
set zoneName = lz.zoneName
from datatransfer.dbo.ads_lyrisZones lz
inner join ad_zones az on az.sitecode = lz.sitecode collate DATABASE_DEFAULT
	and az.zoneTypeID = lz.zoneTypeID
	and az.uniquecode = lz.uniquecode  collate DATABASE_DEFAULT

-- remove exact matches again
delete lz
from datatransfer.dbo.ads_lyrisZones lz
inner join ad_zones az on az.sitecode = lz.sitecode collate DATABASE_DEFAULT
	and az.zoneTypeID = lz.zoneTypeID
	and az.uniquecode = lz.uniquecode  collate DATABASE_DEFAULT
	and az.ZoneName = lz.ZoneName  collate DATABASE_DEFAULT

insert into ad_zones (zoneName, zoneTypeID, siteCode, uniqueCode)
select ZoneName, zoneTypeID, sitecode, uniquecode
from datatransfer.dbo.ads_lyrisZones


-- Update Eclips Zones
select 
	case
		when isSpecial = 0 and len(lyrislistname) > 0  then 'EClips: ' + name + ' (' + lyrislistname + ')'
		when isSpecial = 0 and len(lyrislistname) = 0 then 'EClips: ' + name + ' (no list defined)'
		when isSpecial = 1 and len(lyrislistname) > 0 then 'Specialty: ' + name + ' (' + lyrislistname + ')'
		when isSpecial = 1 and len(lyrislistname) = 0 then 'Specialty: ' + name + ' (no list defined)'
	end as ZoneName,
	@eclipsZoneTypeID as zoneTypeID,
	state as sitecode, 
	convert(varchar(20), publicationID) as uniquecode
into datatransfer.dbo.ads_eclipsZones
from tlasites.trialsmith.dbo.eclipsPublications

-- remove exact matches
delete ez
from datatransfer.dbo.ads_eclipsZones ez
inner join ad_zones az on az.sitecode = ez.sitecode
	and az.zoneTypeID = ez.zoneTypeID
	and az.uniquecode = ez.uniquecode
	and az.ZoneName = ez.ZoneName

-- zone name updated
update az
set zoneName = ez.zoneName
from datatransfer.dbo.ads_eclipsZones ez
inner join ad_zones az on az.sitecode = ez.sitecode
	and az.zoneTypeID = ez.zoneTypeID
	and az.uniquecode = ez.uniquecode

-- remove exact matches again
delete ez
from datatransfer.dbo.ads_eclipsZones ez
inner join ad_zones az on az.sitecode = ez.sitecode
	and az.zoneTypeID = ez.zoneTypeID
	and az.uniquecode = ez.uniquecode
	and az.ZoneName = ez.ZoneName

insert into ad_zones (zoneName,	zoneTypeID,	siteCode, uniqueCode)
select ZoneName, zoneTypeID, sitecode, uniquecode
from datatransfer.dbo.ads_eclipsZones


-- Add missing website zones
select 
	at.applicationTypeName as ZoneName, 
	case
		when at.applicationTypeName = 'Search' then @searchZoneTypeID
		when at.applicationTypeName = 'Login' then @loginPageZoneTypeID
		when at.applicationTypeName = 'Listviewer' then @listviewerZoneTypeID
	end as zoneTypeID,
	s.sitecode, 
	at.applicationTypeName as uniquecode
into datatransfer.dbo.ads_websiteZones
from dbo.sites s
inner join cms_applicationInstances ai on ai.siteID = s.siteID
inner join cms_applicationTypes at on at.applicationTypeID = ai.applicationTypeID
	and at.applicationTypeName in ('Search','Login','Listviewer')
inner join cms_siteResources sr on sr.siteResourceID = ai.siteResourceID
inner join cms_siteResourceStatuses srs on srs.siteResourceStatusID = sr.siteResourceStatusID
	and srs.siteResourceStatusDesc = 'Active'
group by s.sitecode, at.applicationTypeName

-- remove exact matches
delete wz
from datatransfer.dbo.ads_websiteZones wz
inner join ad_zones az on az.sitecode = wz.sitecode
	and az.zoneTypeID = wz.zoneTypeID
	and az.ZoneName = wz.ZoneName
	and az.uniquecode = wz.uniquecode
	
insert into ad_zones (zoneName, zoneTypeID,	siteCode, uniqueCode)
select ZoneName, zoneTypeID, sitecode, uniquecode
from datatransfer.dbo.ads_websiteZones


IF OBJECT_ID('datatransfer.dbo.ads_websiteZones') IS NOT NULL BEGIN
	DROP TABLE datatransfer.dbo.ads_websiteZones
END
IF OBJECT_ID('datatransfer.dbo.ads_eclipsZones') IS NOT NULL BEGIN
	DROP TABLE datatransfer.dbo.ads_eclipsZones
END
IF OBJECT_ID('datatransfer.dbo.ads_lyrisZones') IS NOT NULL BEGIN
	DROP TABLE datatransfer.dbo.ads_lyrisZones
END

SET NOCOUNT OFF

RETURN 0
GO


ALTER PROC [dbo].[job_runDailyMaintenanceJobs]
AS

DECLARE @tier varchar(20), @errorSubjectRoot varchar(100), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '***********'
IF @@SERVERNAME = 'DEV04\PLATFORM2008' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'DEV03\PLATFORM2008' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'STAGING01\PLATFORM2008' BEGIN
	SET @tier = 'STAGING'
	SET @smtpserver = 'mail.trialsmith.com'
END
SET @errorSubjectRoot = @tier + ' - Developer Needed - '


/* Recalc Date Based Virtual Group Rules */
BEGIN TRY
	EXEC dbo.ams_recalcVirtualGroupsBasedOnDateConditions
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Recalculating Date Based Conditions'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup resetPasswordRequests */
BEGIN TRY
	delete from dbo.ams_resetPasswordRequest
	where dateentered < dateadd(day,-30,getdate())
	and expire = 1

	delete from dbo.ams_resetPasswordRequest
	where dateentered < dateadd(day,-30,getdate())
	and expire = 0 
	and hasBeenUsed = 1
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of resetPasswordRequests'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Viewed Member Times */
BEGIN TRY
	delete tmp
	from dbo.ams_viewedMemberTimes as tmp
	inner join (
		select adminMemberViewedID, memRow
		from (
			select adminMemberViewedID, ROW_NUMBER() OVER (PARTITION BY adminID, viewedOrgID order by lastViewedDateTime desc) as memRow
			from dbo.ams_viewedMemberTimes
		) as innerTMP
		where memRow > 20
	) as t2 on t2.adminMemberViewedID = tmp.adminMemberViewedID
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of viewedMemberTimes'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Orphaned Member Data Content */
BEGIN TRY
	EXEC dbo.ams_cleanupOrphanedMemberDataContent
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of member data content'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Orphaned Network Profiles */
BEGIN TRY
	EXEC dbo.ams_deleteOrphanedNetworkProfiles
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of orphaned network profiles'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Orphaned Virtual Group Conditions */
BEGIN TRY
	EXEC dbo.ams_cleanupOrphanedVirtualGroupConditions
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of orphaned virtual group conditions'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup Search Site Resource Cache entries */
BEGIN TRY
	delete from search.dbo.tblSearchSiteResourceCache
	where dateCreated < dateadd(day,-1,getdate())
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of Search Site Resource Cache'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Cleanup BuyNow log entries */
BEGIN TRY
	delete from dbo.buyNow_Log
	where insertDate < dateadd(day,-7,getdate())
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of BuyNow log entries'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Auto Post System Batches */
BEGIN TRY
	EXEC dbo.tr_autoPostSystemBatches
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Auto Posting System Batches'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Mark paid closed invoices as paid */
BEGIN TRY
	EXEC dbo.tr_autoMarkClosedInvoicesAsPaid
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Marking paid closed invoices as paid'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Close Pending Invoices */
BEGIN TRY
	EXEC dbo.sub_closePendingInvoices
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Closing Pending Invoices'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Close empty open invoices */
BEGIN TRY
	EXEC dbo.tr_autoCloseEmptyOpenInvoices
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Closing Empty Open Invoices'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Advance Subscription Rates */
BEGIN TRY
	EXEC dbo.sub_advanceRates
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Advancing Subscription Rates'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Expire Subscription Offers */
BEGIN TRY
	EXEC dbo.sub_expireOffers
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Expiring Subscription Offers'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Expire Subscriptions */
BEGIN TRY
	EXEC dbo.sub_expireSubscriptions
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Expiring Subscriptions'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Add autoPay Invoices to Invoice Payment Queue */
BEGIN TRY
	EXEC dbo.tr_autoPayInvoices
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Adding autoPay Invoices to Invoice Payment Queue'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* Add items to LRIS Client Surveys Queue */
IF @tier = 'PRODUCTION' BEGIN
	BEGIN TRY
		declare @referralID int
		set @referralID = 1
		EXEC dbo.ref_addClientSurveyQueue @referralID = @referralID
	END TRY
	BEGIN CATCH
		SET @errorSubject = @errorSubjectRoot + 'Error Adding items to Client Surveys Queue'

		SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
		SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

		EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
			@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
			@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
	END CATCH
END

/* Cleanup tblQueueItems_processMemberGroups */
BEGIN TRY
	delete from platformQueue.dbo.tblQueueItems_processMemberGroups
	where dateadded < dateadd(day,-1,getdate())
	and itemUID not in (select itemUID from platformQueue.dbo.tblQueueItems)
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Cleanup of tblQueueItems_processMemberGroups'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* Add missing ad zones or update ad zone names */
BEGIN TRY
	EXEC dbo.ad_populateAdZones
END TRY
BEGIN CATCH
	SET @errorSubject = @errorSubjectRoot + 'Error Populating Ad Zones'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

RETURN 0
GO
