ALTER PROCEDURE [dbo].[up_UpdateListMembership] 
	@listname varchar(200),
	@emailaddress varchar(500),
	@newemailaddress varchar(500),
	@fullname varchar(200),
	@membertype varchar(200),
	@subtype varchar(200),
	@externalMemberID varchar(200)
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @usernameLC varchar(200)
	DECLARE @domain varchar(200)
	DECLARE @successFlag bit
	DECLARE @failureReason varchar(500)
	DECLARE @MCEmailKey varchar(75)

	DECLARE @ATposition smallint

	select list_, emailaddr_, fullname_, membertype_, subtype_ ,externalMemberID
	from members_ 
	where list_ = @listname and emailaddr_ = @emailaddress

	if (len(@newemailaddress) > 0 and lower(@newemailaddress) <> lower(@emailaddress))
		begin
			select @ATposition = charindex('@',@newemailaddress,0)
			select @usernameLC = left(@newemailaddress,@ATposition-1)
			select @domain = right(@newemailaddress,len(@newemailaddress)-@ATposition)

			select @MCEmailKey = convert(varchar(75),HASHBYTES('SHA2_256',@listname + '|' + @usernameLC + '@' + @domain),2)


			if not exists (select memberid_ from members_ where list_ = @listname and emailaddr_ = @newemailaddress)
				begin
					update members_ 
					set
						domain_ = @domain,
						emailaddr_ = @newemailaddress,
						fullname_= @fullname,
						membertype_= @membertype,
						subtype_= @subtype,
						usernameLc_ = @usernameLC,
						externalMemberID = @externalMemberID,
						MCEmailKey = @MCEmailKey,
						MCEmailKey_usernameLC = @usernameLC,
						MCEmailKey_domain = @domain
					where
						list_= @listname
						and emailaddr_ = @emailaddress

					set @successFlag = 1
				end
			else
				begin
					set @successFlag = 0
					set @failureReason = 'New email address is already a member of list'
				end


			select list_, emailaddr_, fullname_, membertype_, subtype_,externalMemberID
			from members_ 
			where list_ = @listname and emailaddr_ = @newemailaddress


		end
	else
		begin
			update members_ 
			set
				fullname_= @fullname,
				membertype_= @membertype,
				subtype_= @subtype,
				externalMemberID = @externalMemberID
			where
				list_= @listname
				and emailaddr_ = @emailaddress

			set @successFlag = 1

			select list_, emailaddr_, fullname_, membertype_, subtype_ ,externalMemberID
			from members_ 
			where list_ = @listname and emailaddr_ = @emailaddress
		end

	select @successFlag as successFlag, @failureReason as message



END
GO
