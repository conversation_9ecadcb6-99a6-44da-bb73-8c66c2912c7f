USE membercentral
GO
ALTER FUNCTION dbo.fn_getServerSettings()
RETURNS TABLE
AS
RETURN (
	select 
		smtpserver = 
			case 
			when @@SERVERNAME IN ('MCDEV01\PLATFORM2008','MCBETA01\PLATFORM2008') then 'mail.trialsmith.com'
			else '10.36.18.90'
			end,
		tier = 
			case
			when @@SERVERNAME IN ('MCDEV01\PLATFORM2008') then 'DEVELOPMENT'
			when @@SERVERNAME IN ('MCBETA01\PLATFORM2008') then 'BETA'
			else 'PRODUCTION'
			end,
		pathToRaidWebTemp =
			case
			when @@SERVERNAME IN ('MCDEV01\PLATFORM2008') then '\\mcdev01\wwwRoot\membercentral\temp\'
			when @@SERVERNAME IN ('MCBETA01\PLATFORM2008') then '\\mcbeta01\wwwRoot\membercentral\temp\'
			else '\\tsfile1\platform\membercentral\temp\'
			end,
		urlToWebTemp =
			case
			when @@SERVERNAME IN ('MCDEV01\PLATFORM2008') then 'http://mc.dev.membercentral.com/temp/'
			when @@SERVERNAME IN ('MCBETA01\PLATFORM2008') then 'http://mc.beta.membercentral.com/temp/'
			else 'http://mc.prod.membercentral.com/temp/'
			end,
		pathToTlasitesRoot =
			case
			when @@SERVERNAME IN ('MCDEV01\PLATFORM2008') then '\\mcdev01\tlasites\www\'
			when @@SERVERNAME IN ('MCBETA01\PLATFORM2008') then '\\mcbeta01\tlasites\www\'
			else '\\tsfile1\f$\tlasites\www\'
			end
)
GO
