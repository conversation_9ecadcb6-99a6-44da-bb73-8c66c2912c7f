ALTER PROC dbo.ev_reorderSponsorGroupings
@registrationID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @tmp TABLE (neworder int NOT NULL, sponsorGroupingID int NOT NULL, sponsorGroupingOrder int NOT NULL);
	
	INSERT INTO @tmp (sponsorGroupingID, sponsorGroupingOrder, newOrder)
	SELECT sponsorGroupingID, sponsorGroupingOrder, ROW_NUMBER() OVER(ORDER BY sponsorGroupingOrder) as newOrder
	FROM dbo.ev_sponsorGrouping
	WHERE registrationID = @registrationID;
	
	UPDATE sg
	SET sg.sponsorGroupingOrder = t.neworder
	FROM dbo.ev_sponsorGrouping as sg 
	INNER JOIN @tmp as t on sg.sponsorGroupingID = t.sponsorGroupingID;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCE<PERSON>r<PERSON><PERSON><PERSON> @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
