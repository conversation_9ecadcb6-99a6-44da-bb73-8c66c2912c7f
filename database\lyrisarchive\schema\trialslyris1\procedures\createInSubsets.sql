ALTER PROC createInSubsets
 	@segmentName varchar(60),
 	@listName varchar(60)
 AS
 	DECLARE @subsetWhereClause varchar(100)
 	DECLARE @description varchar(90)
 	DECLARE @normalType varchar(20)
 	DECLARE @subsetIDlocal int
 	SET @description = @segmentName + ' imported from ClickTracks' 
 	SELECT @subsetIDlocal = SubsetID_ FROM subsets_ WHERE Name_ = @segmentName AND List_ = @listName
 	IF @@ROWCOUNT = 0 
 	BEGIN 
 		INSERT INTO subsets_ (Name_, List_, Type_, Desc_ ) VALUES(@segmentName,@listName, 'normal',@description) 
 		SET @subsetWhereClause = 'members_.MemberID_ IN (SELECT MemberID FROM lyrMemberSubsets WHERE SubsetID = ' + CONVERT(varchar(10),SCOPE_IDENTITY()) + ')'
 		UPDATE subsets_ SET ClauseWhere_ = @subsetWhereClause WHERE SubsetID_=SCOPE_IDENTITY() 
 		RETURN SCOPE_IDENTITY() 
	END 
	ELSE 
		RETURN @subsetIDlocal
GO
