

declare @resourceTypeID int, @resourceTypeName varchar(50), @functionID int, @functionName varchar(50), @functionDisplayName varchar(50), @addToSuperUserRole bit, @addToSiteAdminRole bit

DECLARE @rc int, @siteAdminRoleID int, @superAdminRoleID int, @ResourceTypeFunctionID int

set @addToSuperUserRole = 1
set @addToSiteAdminRole = 1
set @resourceTypeName = 'accountLocator'
set @functionName = 'View'

--- Do not change below this line

select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)
select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')
select @siteAdminRoleID = dbo.fn_getResourceRoleID('Site Administrator')

SELECT @functionID = dbo.fn_getResourceFunctionID(@functionName,@resourceTypeID)
select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);

IF (@addToSuperUserRole = 1)
	exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@superAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;
IF (@addToSiteAdminRole = 1)
	exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@siteAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;


GO
	
declare @resourceTypeID int, @resourceTypeName varchar(50), @functionID int, @functionName varchar(50), @functionDisplayName varchar(50), @addToSuperUserRole bit, @addToSiteAdminRole bit

DECLARE @rc int, @siteAdminRoleID int, @superAdminRoleID int, @ResourceTypeFunctionID int

set @addToSuperUserRole = 1
set @addToSiteAdminRole = 1
set @resourceTypeName = 'invoices'
set @functionName = 'View'

--- Do not change below this line

select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)
select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')
select @siteAdminRoleID = dbo.fn_getResourceRoleID('Site Administrator')

SELECT @functionID = dbo.fn_getResourceFunctionID(@functionName,@resourceTypeID)
select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);

IF (@addToSuperUserRole = 1)
	exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@superAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;
IF (@addToSiteAdminRole = 1)
	exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@siteAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;
GO
	
declare @resourceTypeID int, @resourceTypeName varchar(50), @functionID int, @functionName varchar(50), @functionDisplayName varchar(50), @addToSuperUserRole bit, @addToSiteAdminRole bit

DECLARE @rc int, @siteAdminRoleID int, @superAdminRoleID int, @ResourceTypeFunctionID int

set @addToSuperUserRole = 1
set @addToSiteAdminRole = 1
set @resourceTypeName = 'jobbank'
set @functionName = 'View'

--- Do not change below this line

select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)
select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')
select @siteAdminRoleID = dbo.fn_getResourceRoleID('Site Administrator')

SELECT @functionID = dbo.fn_getResourceFunctionID(@functionName,@resourceTypeID)
select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);

IF (@addToSuperUserRole = 1)
	exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@superAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;
IF (@addToSiteAdminRole = 1)
	exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@siteAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;
	
GO

declare @resourceTypeID int, @resourceTypeName varchar(50), @functionID int, @functionName varchar(50), @functionDisplayName varchar(50), @addToSuperUserRole bit, @addToSiteAdminRole bit

DECLARE @rc int, @siteAdminRoleID int, @superAdminRoleID int, @ResourceTypeFunctionID int

set @addToSuperUserRole = 1
set @addToSiteAdminRole = 1
set @resourceTypeName = 'subscriptions'
set @functionName = 'View'

--- Do not change below this line

select @resourceTypeID = dbo.fn_getResourceTypeID(@resourceTypeName)
select @superAdminRoleID = dbo.fn_getResourceRoleID('Super Administrator')
select @siteAdminRoleID = dbo.fn_getResourceRoleID('Site Administrator')

SELECT @functionID = dbo.fn_getResourceFunctionID(@functionName,@resourceTypeID)
select @ResourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,@functionID);

IF (@addToSuperUserRole = 1)
	exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@superAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;
IF (@addToSiteAdminRole = 1)
	exec @rc = dbo.cms_createSiteResourceRoleFunction @roleID=@siteAdminRoleID, @resourceTypeFunctionID=@ResourceTypeFunctionID;
	
GO
