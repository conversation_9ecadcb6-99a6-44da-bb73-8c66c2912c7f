ALTER PROC dbo.emailTracking_createDeliveryMessageTemplate
@statusMessageTemplate VARCHAR(1000),
@messageTemplateSHA1 varchar(40)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

    SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

	BEGIN TRAN;
		INSERT INTO dbo.deliveryMessageTemplates (messageTemplateSHA1, dateCreated, statusMessageTemplate)
		SELECT @messageTemplateSHA1, getdate(), @statusMessageTemplate
		WHERE NOT EXISTS (
			SELECT 1
			FROM dbo.deliveryMessageTemplates
			WHERE messageTemplateSHA1 = @MessageTemplateSHA1
		);
	COMMIT TRAN;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_ErrorHandler;
	RETURN -1;
END CATCH
GO
