CREATE TRIGGER lyrCompletedRecipsInsertTrig
ON lyrCompletedRecips
AFTER INSERT AS
BEGIN
	SET NOCOUNT ON 
INSERT INTO lyrUnsummarizedRecips (RecipientID, MemberID, CompletionStatusID, MailingID, FirstAttempt, FinalAttempt, RecordCount)
	SELECT RecipientID, MemberID, CompletionStatusID, MailingID, FirstAttempt, FinalAttempt, 1 FROM inserted;
	SET NOCOUNT OFF 
END;
ALTER TABLE [dbo].[lyrCompletedRecips] ENABLE TRIGGER [lyrCompletedRecipsInsertTrig]
GO
