use customApps
GO

CREATE PROC dbo.ky_syncListMemberData
AS

IF OBJECT_ID('tempdb..#tmpKYLists') IS NOT NULL 
	DROP TABLE #tmpKYLists
IF OBJECT_ID('tempdb..#tmpKYCurrentMembers') IS NOT NULL 
	DROP TABLE #tmpKYCurrentMembers
IF OBJECT_ID('tempdb..#tmpKYAOP') IS NOT NULL 
	DROP TABLE #tmpKYAOP
IF OBJECT_ID('tempdb..#tmpKYBilled') IS NOT NULL 
	DROP TABLE #tmpKYBilled
IF OBJECT_ID('tempdb..#tmpKYBOG') IS NOT NULL 
	DROP TABLE #tmpKYBOG
IF OBJECT_ID('tempdb..#tmpKYRel') IS NOT NULL 
	DROP TABLE #tmpKYRel
IF OBJECT_ID('tempdb..#tmpKYMembersToLyris') IS NOT NULL 
	DROP TABLE #tmpKYMembersTo<PERSON>yris

declare @orgid int, @siteID int, @mainhostname varchar(80)
select @orgid=s.orgID, @siteID=s.siteID, @mainhostname=sh.hostname
	from membercentral.dbo.sites as s
	inner join membercentral.dbo.siteHostnames as sh on sh.siteID = s.siteID
	where s.sitecode = 'KY'
	and sh.mainHostNameID = sh.hostnameID

-- get all KY lists
select distinct lists.listname
into #tmpKYLists
from membercentral.dbo.lists_lists as lists
inner join membercentral.dbo.cms_siteResources as sr on sr.siteResourceID = lists.siteResourceID
where sr.siteID = @siteID
and lists.listname <> 'eclips_ky'

-- get membernumbers on those lists from lyris
select m.externalMemberID, tmp.listname
into #tmpKYCurrentMembers
from lyris.trialslyris1.dbo.members_ as m 
inner join #tmpKYLists as tmp on tmp.listname = m.list_ COLLATE Latin1_General_CI_AI
where m.externalMemberID is not null

-- get up to 3 active AOP subscriptions per member
select memberid, subscriptionName, row
into #tmpKYAOP
from (
	select distinct m.activeMemberID as memberid, sub.subscriptionName, ROW_NUMBER() OVER (partition by m.activeMemberID order by m.activeMemberID, sub.subscriptionName) as row
	from membercentral.dbo.ams_members as m
	inner join membercentral.dbo.sub_subscribers as s on s.memberID = m.memberID
	inner join membercentral.dbo.sub_statuses as ss on ss.statusID = s.statusID
	inner join membercentral.dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
	inner join membercentral.dbo.sub_types as t on t.typeID = sub.typeID
	where m.orgID = @orgid
	and t.siteID = @siteid
	and ss.statuscode = 'A'
	and t.typeName = 'Areas of Practice'
) as tmp
where row <= 3

-- get directLink for Billed Membership Dues subs
select memberid, directLink
into #tmpKYBilled
from (
	select distinct m.activeMemberID as memberid, s.directLink, ROW_NUMBER() OVER (partition by m.activeMemberID order by m.activeMemberID) as row
	from membercentral.dbo.ams_members as m
	inner join membercentral.dbo.sub_subscribers as s on s.memberID = m.memberID
	inner join membercentral.dbo.sub_statuses as ss on ss.statusID = s.statusID
	inner join membercentral.dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
	inner join membercentral.dbo.sub_types as t on t.typeID = sub.typeID
	where m.orgID = @orgid
	and t.siteID = @siteid
	and ss.statuscode = 'O'
	and t.typeName = 'Membership Dues'
	and s.parentSubscriberID is null
) as tmp
where row = 1

-- get one active BOG Title subscriptions per member
select memberid, subscriptionName
into #tmpKYBOG
from (
	select distinct m.activeMemberID as memberid, sub.subscriptionName, ROW_NUMBER() OVER (partition by m.activeMemberID order by m.activeMemberID, sub.subscriptionName) as row
	from membercentral.dbo.ams_members as m
	inner join membercentral.dbo.sub_subscribers as s on s.memberID = m.memberID
	inner join membercentral.dbo.sub_statuses as ss on ss.statusID = s.statusID
	inner join membercentral.dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
	inner join membercentral.dbo.sub_types as t on t.typeID = sub.typeID
	where m.orgID = @orgid
	and t.siteID = @siteid
	and ss.statuscode = 'A'
	and t.typeName = 'Board of Governors'
	and s.parentSubscriberID is not null
) as tmp
where row = 1

-- get one relationship per member
select memberid, relationshipTypeName
into #tmpKYRel
from (
	select m.memberid, position_rrt.relationshipTypeName, ROW_NUMBER() OVER (partition by m.memberid order by m.memberid, position_rrt.relationshipTypeName) as row
	from membercentral.dbo.ams_members as m
	inner join membercentral.dbo.ams_recordRelationships position_rr on m.memberID = position_rr.childMemberID
	inner join membercentral.dbo.ams_members as m2 on m2.memberid = position_rr.masterMemberID
	inner join membercentral.dbo.ams_recordTypesRelationshipTypes position_rtrt
		on position_rr.recordTypeRelationshipTypeID = position_rtrt.recordTypeRelationshipTypeID
	inner join membercentral.dbo.ams_recordRelationshipTypes position_rrt
		on position_rtrt.relationshipTypeID = position_rrt.relationshipTypeID
	inner join membercentral.dbo.ams_recordTypes position_masterrt
		on position_masterrt.recordTypeID = position_rtrt.masterRecordTypeID	
		and position_masterrt.isOrganization = 1
	inner join membercentral.dbo.ams_recordTypes position_childrt
		on position_childrt.recordTypeID = position_rtrt.childRecordTypeID	
		and position_childrt.isPerson = 1
	where m.orgID = @orgID
	and m.memberid = m.activeMemberID
	and m2.memberid = m2.activeMemberID
) as tmp
where row = 1

CREATE NONCLUSTERED INDEX [idx_tmpKYAOP_memberid_row] ON #tmpKYAOP([memberid] ASC,[row] ASC);
CREATE NONCLUSTERED INDEX [idx_tmpKYBilled_memberid] ON #tmpKYBilled([memberid] ASC);
CREATE NONCLUSTERED INDEX [idx_tmpKYBOG_memberid] ON #tmpKYBOG([memberid] ASC);
CREATE NONCLUSTERED INDEX [idx_tmpKYRel_memberid] ON #tmpKYRel([memberid] ASC);

-- get the new member data from membercentral
-- ticket refers to only wanting year for the dates not full date? not sure.
select m.memberNumber as externalMemberID, tmp.listname as list_, 
	vw.[Primary Address_address1] as Address1_,
	vw.[Primary Address_address2] as Address2_,
	vw.[Primary Address_address3] as Address3_,
	left(AOP1.subscriptionName,250) as areaofpractice1_,
	left(AOP2.subscriptionName,250) as areaofpractice2_,
	left(AOP3.subscriptionName,250) as areaofpractice3_,
	convert(varchar(10),vw.[Bar Date],101) as BarDate_,
	vw.[Primary Address_city] as City_,
	m.company as Company_,
	cast(vw.[Congressional District] as varchar(20)) as CongressionalDistrict_,
	rel.relationshipTypeName as ContactPosition_,
	vw.[Primary Address_county] as County_,
	left(vw.[Board District],20) as District_,
	left(vw.[Primary Address_Fax],20) as Fax_,
	left(m.firstname,50) as Firstname_,
	left(vw.[Gender],10) as Gender_,
	cast(vw.[House District] as varchar(5)) as HD,
	convert(varchar(10),vw.[Date Joined],101) as JoinDate_,
	left(m.lastname,50) as LastName_,
	left(vw.[Legislator],50) as Legislative_,
	left(vw.[League of Justice],250) as MemberLevel_,
	case 
	when vw.[Date Joined] is null then 'Never Been a Member'
	when vw.[Expiration Date] > getdate() then 'Active Member'
	when vw.[Expiration Date] <= getdate() then 'Expired Member'
	end as MemberStatus_,
	m.middlename as MiddleName_,
	left(vw.[Salutation],250) as nickname_,
	datePart(year,vw.[Bar Date]) as numeric1_,
	case when vw.[L-kjamember] = 'Yes' then 1 else 0 end as numeric2_,
	vw.[Prospect] as numeric3_,
	left(vw.[Primary Address_postalcode],10) as PostalCode_,
	left(m.prefix,10) as prefix_,
	left(m.professionalSuffix,20) as ProfSuffix_,
	left('http://' + @mainhostname + '/renewsub/' + nullif(Billed.directLink,''),250) as renewLink_,
	cast(vw.[Senate District] as varchar(5)) as SD,
	vw.[Primary Address_stateprov] as StateProvince_,
	left(m.Suffix,20) as Suffix_,
	left(bog.subscriptionName,250) as Text1_,
	left(vw.[Sponsor],250) as Text3_,
	left(vw.[Contact Type],250) as Text4_,
	left(vw.[Law School],250) as Text5_,
	left(vw.[Website Address],250) as Website_,
	left(vw.[Primary Address_Phone],20) as WorkPhone_
into #tmpKYMembersToLyris
from #tmpKYCurrentMembers as tmp
inner join membercentral.dbo.ams_members as m on tmp.externalMemberID = m.membernumber COLLATE Latin1_General_CI_AI
	and m.orgID = @orgid
	and m.status='A' 
	and m.memberID = m.activeMemberID
inner join membercentral.dbo.vw_memberdata_KY as vw on vw.memberid = m.memberid
left outer join #tmpKYAOP as AOP1 on AOP1.memberid = m.memberid and AOP1.row = 1
left outer join #tmpKYAOP as AOP2 on AOP2.memberid = m.memberid and AOP2.row = 2
left outer join #tmpKYAOP as AOP3 on AOP3.memberid = m.memberid and AOP3.row = 3
left outer join #tmpKYBilled as Billed on Billed.memberid = m.memberid
left outer join #tmpKYBOG as bog on bog.memberid = m.memberid
left outer join #tmpKYRel as rel on rel.memberid = m.memberid

-- update lyris member data
update m
set m.Address1_ = tmp.Address1_,
	m.Address2_ = tmp.Address2_,
	m.Address3_ = tmp.Address3_,
	m.areaofpractice1_ = tmp.areaofpractice1_,
	m.areaofpractice2_ = tmp.areaofpractice2_,
	m.areaofpractice3_ = tmp.areaofpractice3_,
	m.BarDate_ = tmp.BarDate_,
	m.City_ = tmp.City_,
	m.Company_ = tmp.Company_,
	m.CongressionalDistrict_ = tmp.CongressionalDistrict_,
	m.Contactposition_ = tmp.Contactposition_,
	m.County_ = tmp.County_,
	m.District_ = tmp.district_,
	m.Fax_ = tmp.Fax_,
	m.Firstname_ = tmp.Firstname_,
	m.Gender_ = tmp.Gender_,
	m.HD = tmp.HD,
	m.JoinDate_ = tmp.JoinDate_,
	m.LastName_ = tmp.LastName_,
	m.Legislative_ = tmp.Legislative_,
	m.MemberLevel_ = tmp.MemberLevel_,
	m.MemberStatus_ = tmp.MemberStatus_,
	m.MiddleName_ = tmp.MiddleName_,
	m.nickname_ = tmp.nickname_,
	m.numeric1_ = tmp.numeric1_,
	m.numeric2_ = tmp.numeric2_,
	m.numeric3_ = tmp.numeric3_,
	m.PostalCode_ = tmp.PostalCode_,
	m.prefix_ = tmp.prefix_,
	m.ProfSuffix_ = tmp.ProfSuffix_,
	m.RenewLink_ = tmp.RenewLink_,
	m.SD = tmp.SD,
	m.StateProvince_ = tmp.StateProvince_,
	m.Suffix_ = tmp.Suffix_,
	m.Text1_ = tmp.Text1_,
	m.Text3_ = tmp.Text3_,
	m.Text4_ = tmp.Text4_,
	m.Text5_ = tmp.Text5_,
	m.Website_ = tmp.Website_,
	m.WorkPhone_ = tmp.WorkPhone_
from lyris.trialslyris1.dbo.members_ as m
inner join #tmpKYMembersToLyris as tmp on tmp.externalMemberID = m.externalMemberID COLLATE Latin1_General_CI_AI
	and tmp.list_ = m.list_ COLLATE Latin1_General_CI_AI

IF OBJECT_ID('tempdb..#tmpKYLists') IS NOT NULL 
	DROP TABLE #tmpKYLists
IF OBJECT_ID('tempdb..#tmpKYCurrentMembers') IS NOT NULL 
	DROP TABLE #tmpKYCurrentMembers
IF OBJECT_ID('tempdb..#tmpKYAOP') IS NOT NULL 
	DROP TABLE #tmpKYAOP
IF OBJECT_ID('tempdb..#tmpKYBilled') IS NOT NULL 
	DROP TABLE #tmpKYBilled
IF OBJECT_ID('tempdb..#tmpKYBOG') IS NOT NULL 
	DROP TABLE #tmpKYBOG
IF OBJECT_ID('tempdb..#tmpKYRel') IS NOT NULL 
	DROP TABLE #tmpKYRel
IF OBJECT_ID('tempdb..#tmpKYMembersToLyris') IS NOT NULL 
	DROP TABLE #tmpKYMembersToLyris

RETURN 0
GO

ALTER PROC [dbo].[job_runDailyCustomJobs]
AS

DECLARE @tier varchar(20), @errorSubject varchar(100), @smtpserver varchar(20)
DECLARE @errmsg nvarchar(2048), @proc sysname, @lineno int, @crlf varchar(10)

/* variables */
SET @crlf = char(13) + char(10)
SET @tier = 'PRODUCTION'
SET @smtpserver = '10.36.18.90'
IF @@SERVERNAME = 'DEV04\PLATFORM2008' BEGIN
	SET @tier = 'DEVELOPMENT'
	SET @smtpserver = 'mail.trialsmith.com'
END
IF @@SERVERNAME = 'DEV03\PLATFORM2008' BEGIN
	SET @tier = 'BETA'
	SET @smtpserver = 'mail.trialsmith.com'
END


/* ********************** */
/* tn_updateOldestBarDate */
/* ********************** */
BEGIN TRY
	EXEC customApps.dbo.tn_updateOldestBarDate
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Oldest Bar Date - TN'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* ********************** */
/* ky_updateOldestBarDate */
/* ********************** */
BEGIN TRY
	EXEC customApps.dbo.ky_updateOldestBarDate
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Oldest Bar Date - KY'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* ***************** */
/* MEMBER JOIN DATES */
/* ***************** */
BEGIN TRY
	declare @mjd_udid int, @mjd_error_code int, @mjd_emsg varchar(70)
	select @mjd_udid = min(udid) from customApps.dbo.schedTask_memberJoinDates where isActive = 1
	while @mjd_udid is not null BEGIN
		EXEC customApps.dbo.job_memberJoinDates @udid=@mjd_udid, @error_code=@mjd_error_code OUTPUT
		IF @mjd_error_code <> 0 BEGIN
			select @mjd_emsg = 'UDID ' + cast(@mjd_udid as varchar(10)) + ' was not successful. Error ' + cast(@mjd_error_code as varchar(10)) + '.'
			RAISERROR(@mjd_emsg,16,1)
		END
		select @mjd_udid = min(udid) from customApps.dbo.schedTask_memberJoinDates where isActive = 1 and udid > @mjd_udid
	END
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Member Join Dates'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* ********************** */
/* caaa_updateHomeChapterRO */
/* ********************** */
BEGIN TRY
	EXEC customApps.dbo.caaa_updateHomeChapterRO
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Home Chapter - CAAA'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


/* ********************** */
/* caaa_checkInvoice90Days */
/* ********************** */
BEGIN TRY
	EXEC customApps.dbo.caaa_checkInvoice90Days
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Running 90 Day Invoice Check - CAAA'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ************************************ */
/* ny_updateCLECreditsSinceLastBirthday */
/* ************************************ */
BEGIN TRY
	EXEC customApps.dbo.ny_updateCLECreditsSinceLastBirthday
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating CLE Credit Since Last Birthday - NY'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* *************************** */
/* sdcba_updateGeographicAreas */
/* *************************** */
BEGIN TRY
	exec customApps.dbo.sdcba_updateGeographicAreas
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Geographic Areas - SDCBA'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH

/* ********************* */
/* ky_syncListMemberData */
/* ********************* */
BEGIN TRY
	EXEC customApps.dbo.ky_syncListMemberData
END TRY
BEGIN CATCH
	SET @errorSubject = @tier + ': Error Updating Lyris Member Data - KY'

	SELECT @errmsg = error_message(), @proc = error_procedure(), @lineno = error_line()
	SELECT @errmsg = @errorSubject + @crlf + @crlf + coalesce(@proc,'<dynamic SQL>') + ' line ' + ltrim(str(@lineno)) + @crlf + @crlf + @errmsg

	EXEC membercentral.dbo.sys_sendEmail @from='<EMAIL>', @to='<EMAIL>',
		@cc=null, @bcc=null, @subject=@errorSubject, @message=@errmsg, @priority='high', 
		@smtpserver=@smtpserver, @authUsername=null, @authPassword=null
END CATCH


RETURN 0
GO

